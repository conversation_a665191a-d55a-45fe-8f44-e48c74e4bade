<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📤 صندوق الصادر - نظام البريد الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            direction: rtl;
            color: #ffffff;
            overflow-x: hidden;
        }
        
        /* شريط التنقل */
        .navbar-custom {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .navbar-brand {
            color: #ffffff !important;
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .navbar-nav .nav-link {
            color: #a0a0a0 !important;
            font-weight: 500;
            padding: 0.8rem 1.2rem !important;
            border-radius: 50px;
            transition: all 0.3s ease;
            margin: 0 0.2rem;
        }
        
        .navbar-nav .nav-link:hover {
            color: #ffffff !important;
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }
        
        .navbar-nav .nav-link.active {
            color: #ffffff !important;
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .navbar-toggler {
            border: none;
            color: #ffffff;
        }
        
        .navbar-toggler:focus {
            box-shadow: none;
        }
        
        .breadcrumb-custom {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
        }
        
        .breadcrumb-custom .breadcrumb {
            margin: 0;
            background: none;
            padding: 0;
        }
        
        .breadcrumb-custom .breadcrumb-item {
            color: #a0a0a0;
        }
        
        .breadcrumb-custom .breadcrumb-item.active {
            color: #ffffff;
            font-weight: 600;
        }
        
        .breadcrumb-custom .breadcrumb-item a {
            color: #667eea;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .breadcrumb-custom .breadcrumb-item a:hover {
            color: #ffffff;
        }
        
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .page-title {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            text-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
        }
        
        .page-subtitle {
            font-size: 1.2rem;
            color: #a0a0a0;
            margin-bottom: 2rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #28a745, #20c997);
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(40, 167, 69, 0.3);
        }
        
        .stat-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #a0a0a0;
            font-size: 0.9rem;
        }
        
        .emails-container {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .email-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .email-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
            border-color: rgba(102, 126, 234, 0.5);
        }
        
        .email-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .email-recipient {
            font-weight: 600;
            color: #ffffff;
            font-size: 1rem;
        }
        
        .email-date {
            color: #a0a0a0;
            font-size: 0.9rem;
        }
        
        .email-subject {
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        
        .email-preview {
            color: #c0c0c0;
            font-size: 0.9rem;
            line-height: 1.4;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
        
        .no-emails {
            text-align: center;
            padding: 4rem 2rem;
        }
        
        .no-emails-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 1rem;
        }
        
        .compose-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .compose-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        
        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .navbar-nav {
                text-align: center;
            }
            
            .navbar-nav .nav-link {
                margin: 0.2rem 0;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('email.inbox') }}">
                <i class="fas fa-envelope me-2"></i>
                نظام البريد الإلكتروني
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <i class="fas fa-bars"></i>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.inbox') }}">
                            <i class="fas fa-inbox me-1"></i>
                            صندوق الوارد
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.compose') }}">
                            <i class="fas fa-edit me-1"></i>
                            إنشاء رسالة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('email.sent_box') }}">
                            <i class="fas fa-paper-plane me-1"></i>
                            صندوق الصادر
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.drafts_box') }}">
                            <i class="fas fa-file-text me-1"></i>
                            المسودات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.trash_box') }}">
                            <i class="fas fa-trash me-1"></i>
                            سلة المحذوفات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.contacts') }}">
                            <i class="fas fa-address-book me-1"></i>
                            دفتر العناوين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.templates') }}">
                            <i class="fas fa-file-alt me-1"></i>
                            القوالب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.analytics') }}">
                            <i class="fas fa-chart-line me-1"></i>
                            التحليلات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.settings') }}">
                            <i class="fas fa-cog me-1"></i>
                            الإعدادات
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user me-1"></i>
                            {{ current_user.username if current_user.is_authenticated else 'المستخدم' }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url_for('main.dashboard') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>لوحة المعلومات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <div class="main-container">
        <!-- Breadcrumb -->
        <div class="breadcrumb-custom">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('main.dashboard') }}">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('email.inbox') }}">
                            <i class="fas fa-envelope me-1"></i>
                            البريد الإلكتروني
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <i class="fas fa-paper-plane me-1"></i>
                        صندوق الصادر
                    </li>
                </ol>
            </nav>
        </div>
        
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-paper-plane me-2"></i>
                صندوق الصادر
            </h1>
            <p class="page-subtitle">
                <i class="fas fa-check-circle me-1"></i>
                الرسائل التي تم إرسالها بنجاح
            </p>
        </div>
        
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-paper-plane"></i>
                </div>
                <div class="stat-number">{{ stats.total_sent or 0 }}</div>
                <div class="stat-label">إجمالي المرسلة</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stat-number">{{ stats.sent_today or 0 }}</div>
                <div class="stat-label">مرسلة اليوم</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-calendar-week"></i>
                </div>
                <div class="stat-number">{{ stats.sent_this_week or 0 }}</div>
                <div class="stat-label">مرسلة هذا الأسبوع</div>
            </div>
        </div>
        
        <!-- Emails Container -->
        <div class="emails-container">
            {% if emails and emails|length > 0 %}
                <!-- Emails List -->
                {% for email in emails %}
                <div class="email-item">
                    <div class="email-header">
                        <div class="email-recipient">
                            <i class="fas fa-user me-1"></i>
                            إلى: {{ email.recipient_email }}
                        </div>
                        <div class="email-date">
                            <i class="fas fa-clock me-1"></i>
                            {{ email.sent_date.strftime('%Y-%m-%d %H:%M') if email.sent_date else 'غير محدد' }}
                        </div>
                    </div>
                    <div class="email-subject">
                        {{ email.subject }}
                        {% if email.has_attachments %}
                            <i class="fas fa-paperclip ms-2 text-warning"></i>
                        {% endif %}
                    </div>
                    <div class="email-preview">
                        {{ email.body_text[:200] }}{% if email.body_text|length > 200 %}...{% endif %}
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <!-- No Emails Message -->
                <div class="no-emails">
                    <div class="no-emails-icon">
                        <i class="fas fa-paper-plane"></i>
                    </div>
                    <h3 style="color: #ffffff; margin-bottom: 1rem;">لا توجد رسائل مرسلة</h3>
                    <p style="color: #a0a0a0; margin-bottom: 2rem;">
                        لم يتم إرسال أي رسائل بعد.<br>
                        ابدأ بإنشاء وإرسال رسالتك الأولى.
                    </p>
                    <a href="{{ url_for('email.compose') }}" class="compose-btn">
                        <i class="fas fa-edit"></i>
                        إنشاء رسالة جديدة
                    </a>
                </div>
            {% endif %}
        </div>
        
        <!-- Back Button -->
        <div class="text-center">
            <a href="{{ url_for('email.inbox') }}" class="compose-btn">
                <i class="fas fa-arrow-right"></i>
                العودة إلى صندوق الوارد
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
