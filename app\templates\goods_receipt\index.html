{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-truck me-2"></i>
                    {{ title }}
                </h2>
                <a href="{{ url_for('goods_receipt.new') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    استلام جديد
                </a>
            </div>

            <!-- Search and Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">البحث</label>
                            <input type="text" name="search" class="form-control" 
                                   value="{{ request.args.get('search', '') }}" 
                                   placeholder="رقم الاستلام أو أمر الشراء...">
                        </div>
                        
                        <div class="col-md-2">
                            <label class="form-label">الحالة</label>
                            <select name="status" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="pending" {{ 'selected' if request.args.get('status') == 'pending' }}>في الانتظار</option>
                                <option value="partial" {{ 'selected' if request.args.get('status') == 'partial' }}>استلام جزئي</option>
                                <option value="completed" {{ 'selected' if request.args.get('status') == 'completed' }}>مكتمل</option>
                                <option value="rejected" {{ 'selected' if request.args.get('status') == 'rejected' }}>مرفوض</option>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" name="date_from" class="form-control" 
                                   value="{{ request.args.get('date_from', '') }}">
                        </div>
                        
                        <div class="col-md-2">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" name="date_to" class="form-control" 
                                   value="{{ request.args.get('date_to', '') }}">
                        </div>
                        
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                                <a href="{{ url_for('goods_receipt.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>
                                    مسح
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Results -->
            <div class="card">
                <div class="card-body">
                    {% if receipts.items %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الاستلام</th>
                                        <th>أمر الشراء</th>
                                        <th>المورد</th>
                                        <th>تاريخ الاستلام</th>
                                        <th>الحالة</th>
                                        <th>إجمالي الكمية</th>
                                        <th>المستلم</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for receipt in receipts.items %}
                                        <tr>
                                            <td>
                                                <strong>{{ receipt.receipt_number }}</strong>
                                            </td>
                                            <td>
                                                {% if receipt.purchase_order %}
                                                    <a href="{{ url_for('purchase_orders.view', id=receipt.purchase_order.id) }}" 
                                                       class="text-decoration-none">
                                                        {{ receipt.purchase_order.order_number }}
                                                    </a>
                                                {% else %}
                                                    <span class="text-muted">غير محدد</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if receipt.supplier %}
                                                    {{ receipt.supplier.name_ar }}
                                                {% else %}
                                                    <span class="text-muted">غير محدد</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ receipt.receipt_date.strftime('%Y-%m-%d') if receipt.receipt_date else '-' }}</td>
                                            <td>
                                                {% if receipt.status == 'pending' %}
                                                    <span class="badge bg-warning">في الانتظار</span>
                                                {% elif receipt.status == 'partial' %}
                                                    <span class="badge bg-info">استلام جزئي</span>
                                                {% elif receipt.status == 'completed' %}
                                                    <span class="badge bg-success">مكتمل</span>
                                                {% elif receipt.status == 'rejected' %}
                                                    <span class="badge bg-danger">مرفوض</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">{{ receipt.status }}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% set total_qty = receipt.items|sum(attribute='received_quantity') %}
                                                {{ "%.2f"|format(total_qty) if total_qty else '0' }}
                                            </td>
                                            <td>
                                                {% if receipt.received_by_user %}
                                                    {{ receipt.received_by_user.username }}
                                                {% else %}
                                                    <span class="text-muted">غير محدد</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ url_for('goods_receipt.view', id=receipt.id) }}" 
                                                       class="btn btn-outline-primary" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    {% if receipt.status in ['pending', 'partial'] %}
                                                        <a href="{{ url_for('goods_receipt.edit', id=receipt.id) }}" 
                                                           class="btn btn-outline-secondary" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    {% endif %}
                                                    <a href="{{ url_for('goods_receipt.print', id=receipt.id) }}" 
                                                       class="btn btn-outline-info" title="طباعة" target="_blank">
                                                        <i class="fas fa-print"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if receipts.pages > 1 %}
                            <nav aria-label="تصفح النتائج" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    {% if receipts.has_prev %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('goods_receipt.index', page=receipts.prev_num, **request.args) }}">
                                                السابق
                                            </a>
                                        </li>
                                    {% endif %}
                                    
                                    {% for page_num in receipts.iter_pages() %}
                                        {% if page_num %}
                                            {% if page_num != receipts.page %}
                                                <li class="page-item">
                                                    <a class="page-link" href="{{ url_for('goods_receipt.index', page=page_num, **request.args) }}">
                                                        {{ page_num }}
                                                    </a>
                                                </li>
                                            {% else %}
                                                <li class="page-item active">
                                                    <span class="page-link">{{ page_num }}</span>
                                                </li>
                                            {% endif %}
                                        {% else %}
                                            <li class="page-item disabled">
                                                <span class="page-link">...</span>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if receipts.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('goods_receipt.index', page=receipts.next_num, **request.args) }}">
                                                التالي
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}

                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد عمليات استلام</h5>
                            <p class="text-muted">لم يتم العثور على عمليات استلام تطابق معايير البحث</p>
                            <a href="{{ url_for('goods_receipt.new') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                إنشاء استلام جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">إجمالي الاستلامات</h6>
                        <h3 class="mb-0">{{ receipts.total if receipts else 0 }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-truck fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">في الانتظار</h6>
                        <h3 class="mb-0">
                            {{ receipts.items|selectattr('status', 'equalto', 'pending')|list|length if receipts else 0 }}
                        </h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">مكتملة</h6>
                        <h3 class="mb-0">
                            {{ receipts.items|selectattr('status', 'equalto', 'completed')|list|length if receipts else 0 }}
                        </h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">استلام جزئي</h6>
                        <h3 class="mb-0">
                            {{ receipts.items|selectattr('status', 'equalto', 'partial')|list|length if receipts else 0 }}
                        </h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
