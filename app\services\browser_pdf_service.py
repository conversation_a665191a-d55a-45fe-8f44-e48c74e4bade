# -*- coding: utf-8 -*-
"""
خدمة PDF تستخدم المتصفح لتحويل صفحة المعاينة إلى PDF
Browser PDF Service using the existing viewer page
"""

import os
import sys
import time
import tempfile
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class BrowserPDFService:
    """خدمة PDF تستخدم المتصفح"""
    
    def __init__(self):
        """تهيئة الخدمة"""
        self.output_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'pdf')
        os.makedirs(self.output_dir, exist_ok=True)
        self.base_url = 'http://127.0.0.1:5000'
    
    def generate_pdf_from_viewer_page(self, delivery_order_id):
        """إنشاء PDF من صفحة المعاينة باستخدام المتصفح"""
        try:
            # إنشاء اسم الملف
            filename = f"delivery_order_viewer_{delivery_order_id}.pdf"
            filepath = os.path.join(self.output_dir, filename)
            
            # URL صفحة المعاينة
            viewer_url = f"{self.base_url}/shipments/delivery-order-viewer/{delivery_order_id}"
            
            print(f"🌐 فتح صفحة المعاينة: {viewer_url}")
            
            # استخدام Selenium لمحاكاة المتصفح
            if self._generate_with_selenium(viewer_url, filepath):
                return filepath, "تم إنشاء PDF من صفحة المعاينة بنجاح"
            
            # استخدام Chrome headless مباشرة
            if self._generate_with_chrome_direct(viewer_url, filepath):
                return filepath, "تم إنشاء PDF باستخدام Chrome بنجاح"
            
            return None, "فشل في إنشاء PDF - المتصفح غير متاح"
            
        except Exception as e:
            return None, f"خطأ في إنشاء PDF: {str(e)}"
    
    def _generate_with_selenium(self, viewer_url, output_path):
        """إنشاء PDF باستخدام Selenium (يحاكي الزر الموجود)"""
        try:
            # إعداد Chrome options
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            # إعداد مجلد التحميل
            download_dir = os.path.dirname(output_path)
            prefs = {
                "download.default_directory": download_dir,
                "download.prompt_for_download": False,
                "download.directory_upgrade": True,
                "safebrowsing.enabled": True
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            # إنشاء driver
            driver = webdriver.Chrome(options=chrome_options)
            
            try:
                # فتح الصفحة
                print(f"🌐 فتح الصفحة: {viewer_url}")
                driver.get(viewer_url)
                
                # انتظار تحميل الصفحة
                wait = WebDriverWait(driver, 15)
                wait.until(EC.presence_of_element_located((By.ID, "delivery-order-content")))
                
                # انتظار تحميل المكتبات (html2canvas, jsPDF)
                wait.until(lambda driver: driver.execute_script("return typeof html2canvas !== 'undefined'"))
                wait.until(lambda driver: driver.execute_script("return typeof window.jspdf !== 'undefined'"))
                
                print("✅ تم تحميل الصفحة والمكتبات")
                
                # انتظار إضافي لتحميل الخطوط والأنماط
                time.sleep(3)
                
                # تنفيذ دالة downloadPDF() مباشرة
                print("📄 تنفيذ دالة downloadPDF()...")
                
                # تعديل دالة downloadPDF لحفظ الملف بدلاً من تحميله
                pdf_script = f"""
                return new Promise((resolve, reject) => {{
                    try {{
                        // إخفاء أزرار التحكم
                        const controls = document.querySelector('.controls');
                        if (controls) controls.style.display = 'none';
                        
                        // استخدام html2canvas لتحويل HTML إلى صورة ثم PDF
                        html2canvas(document.getElementById('delivery-order-content'), {{
                            scale: 1.5,
                            useCORS: true,
                            allowTaint: true,
                            backgroundColor: '#ffffff'
                        }}).then(function(canvas) {{
                            const {{ jsPDF }} = window.jspdf;
                            const pdf = new jsPDF('p', 'mm', 'a4');
                            
                            const imgData = canvas.toDataURL('image/png');
                            const imgWidth = 210; // A4 width in mm
                            const imgHeight = (canvas.height * imgWidth) / canvas.width;
                            
                            // إضافة الصورة
                            pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
                            
                            // الحصول على PDF كـ blob
                            const pdfBlob = pdf.output('blob');
                            
                            // إنشاء رابط تحميل
                            const url = URL.createObjectURL(pdfBlob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = 'delivery_order_{delivery_order_id}.pdf';
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                            URL.revokeObjectURL(url);
                            
                            // إظهار أزرار التحكم مرة أخرى
                            if (controls) controls.style.display = 'block';
                            
                            resolve('PDF created successfully');
                        }}).catch(function(error) {{
                            console.error('خطأ في إنشاء PDF:', error);
                            reject(error);
                        }});
                    }} catch (error) {{
                        reject(error);
                    }}
                }});
                """
                
                # تنفيذ الكود وانتظار النتيجة
                result = driver.execute_async_script(pdf_script)
                
                print(f"✅ تم تنفيذ دالة PDF: {result}")
                
                # انتظار تحميل الملف
                time.sleep(5)
                
                # البحث عن الملف المُحمل
                downloaded_files = [f for f in os.listdir(download_dir) if f.startswith('delivery_order_') and f.endswith('.pdf')]
                
                if downloaded_files:
                    # نقل الملف إلى المسار المطلوب
                    downloaded_file = os.path.join(download_dir, downloaded_files[0])
                    if os.path.exists(downloaded_file):
                        os.rename(downloaded_file, output_path)
                        print(f"✅ تم نقل الملف إلى: {output_path}")
                        return True
                
                print("❌ لم يتم العثور على الملف المُحمل")
                return False
                
            finally:
                driver.quit()
                
        except Exception as e:
            print(f"❌ خطأ في Selenium: {e}")
            return False
    
    def _generate_with_chrome_direct(self, viewer_url, output_path):
        """إنشاء PDF باستخدام Chrome مباشرة"""
        try:
            import subprocess
            
            # البحث عن Chrome
            chrome_paths = [
                r'C:\Program Files\Google\Chrome\Application\chrome.exe',
                r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe',
                'chrome.exe',
                'google-chrome'
            ]
            
            chrome_path = None
            for path in chrome_paths:
                try:
                    result = subprocess.run([path, '--version'], capture_output=True, timeout=5)
                    if result.returncode == 0:
                        chrome_path = path
                        print(f"✅ تم العثور على Chrome: {path}")
                        break
                except:
                    continue
            
            if not chrome_path:
                print("❌ Chrome غير متاح")
                return False
            
            cmd = [
                chrome_path,
                '--headless',
                '--disable-gpu',
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-extensions',
                '--disable-plugins',
                '--run-all-compositor-stages-before-draw',
                '--virtual-time-budget=10000',  # انتظار 10 ثوان للتحميل
                '--print-to-pdf=' + output_path,
                '--print-to-pdf-no-header',
                viewer_url
            ]
            
            print("🖨️ تشغيل Chrome لإنشاء PDF...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            # فحص النتيجة
            if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:  # على الأقل 1KB
                file_size = os.path.getsize(output_path)
                print(f"✅ تم إنشاء PDF بنجاح! حجم الملف: {file_size} بايت")
                return True
            else:
                print(f"❌ فشل Chrome في إنشاء PDF")
                if result.stderr:
                    print(f"خطأ Chrome: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في Chrome: {e}")
            return False
    
    def check_browser_availability(self):
        """فحص توفر المتصفحات"""
        browsers = {
            'selenium': False,
            'chrome': False
        }
        
        # فحص Selenium
        try:
            from selenium import webdriver
            browsers['selenium'] = True
        except ImportError:
            pass
        
        # فحص Chrome
        import subprocess
        chrome_paths = [
            r'C:\Program Files\Google\Chrome\Application\chrome.exe',
            r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe',
            'chrome.exe',
            'google-chrome'
        ]
        
        for path in chrome_paths:
            try:
                result = subprocess.run([path, '--version'], capture_output=True, timeout=5)
                if result.returncode == 0:
                    browsers['chrome'] = True
                    break
            except:
                continue
        
        return browsers


# إنشاء instance عام للخدمة
browser_pdf_service = BrowserPDFService()


def generate_pdf_from_viewer_page(delivery_order_id):
    """دالة مساعدة لإنشاء PDF من صفحة المعاينة"""
    return browser_pdf_service.generate_pdf_from_viewer_page(delivery_order_id)


def check_browser_pdf_tools():
    """فحص أدوات المتصفح المتاحة"""
    return browser_pdf_service.check_browser_availability()
