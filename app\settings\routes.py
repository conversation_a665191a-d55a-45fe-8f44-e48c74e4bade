"""
مسارات الإعدادات والتهيئة
Settings and Configuration Routes
"""

from flask import render_template, request, jsonify, flash, redirect, url_for, current_app
from flask_login import login_required, current_user
from app.settings import bp
from app.models import User, UserSettings, Branch, Currency, db
from app import cache
from database_manager import db_manager
import json
import os
from datetime import datetime
import logging
from werkzeug.utils import secure_filename
import uuid

logger = logging.getLogger(__name__)

@bp.route('/')
@login_required
def index():
    """الصفحة الرئيسية للإعدادات"""
    return render_template('settings/index.html')

@bp.route('/general')
@login_required
def general_settings():
    """الإعدادات العامة"""
    # جلب العملات المتاحة
    currencies = Currency.query.filter_by(is_active=1).order_by(Currency.code).all()
    return render_template('settings/general.html', currencies=currencies)

@bp.route('/database')
@login_required
def database_settings():
    """إعدادات قاعدة البيانات"""

    # تهيئة مدير قاعدة البيانات
    db_manager.initialize()

    # اختبار الاتصال والحصول على معلومات قاعدة البيانات
    connection_test = db_manager.test_connection()

    database_info = {
        'connected': connection_test['status'] == 'success',
        'database_type': connection_test.get('database_type', 'Unknown'),
        'database_name': connection_test.get('database_type', 'غير متاح'),
        'tables': [],
        'error': connection_test.get('error') if connection_test['status'] == 'error' else None
    }

    if database_info['connected']:
        # الحصول على معلومات جميع الجداول
        tables_info = db_manager.get_all_tables_info()

        # تحويل إلى التنسيق المطلوب للعرض
        formatted_tables = []
        table_names_ar = {
            'users': 'المستخدمين',
            'user_settings': 'إعدادات المستخدمين',
            'branches': 'الفروع',
            'suppliers': 'الموردين',
            'item_categories': 'فئات الأصناف',
            'items': 'الأصناف',
            'purchase_requests': 'طلبات الشراء',
            'purchase_request_items': 'عناصر طلبات الشراء',
            'purchase_orders': 'أوامر الشراء',
            'purchase_order_items': 'عناصر أوامر الشراء',
            'goods_receipts': 'إيصالات البضائع',
            'goods_receipt_items': 'عناصر إيصالات البضائع',
            'stock_movements': 'حركات المخزون',
            'contracts': 'العقود',
            'invoices': 'الفواتير',
            'invoice_items': 'عناصر الفواتير',
            'payments': 'المدفوعات',
            'approvals': 'الموافقات',
            'workflow_templates': 'قوالب سير العمل',
            'workflow_steps': 'خطوات سير العمل',
            'budgets': 'الميزانيات',
            'expenses': 'المصروفات'
        }

        for table_info in tables_info:
            if table_info['status'] == 'active':
                formatted_tables.append({
                    'name': table_names_ar.get(table_info['name'], table_info['name']),
                    'table': table_info['name'],
                    'count': table_info['row_count']
                })

        database_info['tables'] = formatted_tables

    return render_template('settings/database.html', database_info=database_info)

@bp.route('/security')
@login_required
def security_settings():
    """إعدادات الأمان"""
    return render_template('settings/security.html')

@bp.route('/backup')
@login_required
def backup_settings():
    """إعدادات النسخ الاحتياطي"""
    return render_template('settings/backup.html')

@bp.route('/system-info')
@login_required
def system_info():
    """معلومات النظام"""
    return render_template('settings/system_info.html')

@bp.route('/test')
@login_required
def test_settings():
    """صفحة اختبار الإعدادات"""
    return render_template('settings/test.html')

@bp.route('/api/system-status')
@login_required
def system_status():
    """حالة النظام"""
    import platform
    from datetime import datetime

    try:
        import psutil

        # معلومات النظام
        system_info = {
            'platform': platform.system(),
            'platform_version': platform.version(),
            'architecture': platform.architecture()[0],
            'processor': platform.processor(),
            'python_version': platform.python_version(),
            'hostname': platform.node()
        }

        # معلومات الذاكرة
        memory = psutil.virtual_memory()
        memory_info = {
            'total': round(memory.total / (1024**3), 2),  # GB
            'available': round(memory.available / (1024**3), 2),
            'used': round(memory.used / (1024**3), 2),
            'percentage': memory.percent
        }

        # معلومات المعالج
        cpu_info = {
            'count': psutil.cpu_count(),
            'usage': psutil.cpu_percent(interval=1),
            'frequency': psutil.cpu_freq().current if psutil.cpu_freq() else 'غير متاح'
        }

        # معلومات القرص
        try:
            disk = psutil.disk_usage('C:' if platform.system() == 'Windows' else '/')
            disk_info = {
                'total': round(disk.total / (1024**3), 2),
                'used': round(disk.used / (1024**3), 2),
                'free': round(disk.free / (1024**3), 2),
                'percentage': round((disk.used / disk.total) * 100, 2)
            }
        except:
            disk_info = {
                'total': 'غير متاح',
                'used': 'غير متاح',
                'free': 'غير متاح',
                'percentage': 'غير متاح'
            }

        # معلومات التطبيق
        app_info = {
            'flask_version': current_app.config.get('FLASK_VERSION', 'غير محدد'),
            'debug_mode': current_app.debug,
            'environment': current_app.config.get('FLASK_ENV', 'غير محدد'),
            'secret_key_set': bool(current_app.config.get('SECRET_KEY')),
            'database_url': current_app.config.get('SQLALCHEMY_DATABASE_URI', '').split('/')[-1] if current_app.config.get('SQLALCHEMY_DATABASE_URI') else 'غير محدد'
        }

        return jsonify({
            'success': True,
            'timestamp': datetime.now().isoformat(),
            'system': system_info,
            'memory': memory_info,
            'cpu': cpu_info,
            'disk': disk_info,
            'application': app_info
        })

    except ImportError:
        return jsonify({
            'success': False,
            'message': 'مكتبة psutil غير مثبتة',
            'basic_info': {
                'platform': platform.system(),
                'python_version': platform.python_version(),
                'debug_mode': current_app.debug,
                'timestamp': datetime.now().isoformat()
            }
        })
    except Exception as e:
        logger.error(f"خطأ في الحصول على معلومات النظام: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}',
            'basic_info': {
                'platform': platform.system(),
                'python_version': platform.python_version(),
                'debug_mode': current_app.debug,
                'timestamp': datetime.now().isoformat()
            }
        })

@bp.route('/api/database-status')
@login_required
def database_status():
    """حالة قاعدة البيانات"""
    try:
        # تهيئة مدير قاعدة البيانات
        db_manager.initialize()

        # اختبار الاتصال
        connection_test = db_manager.test_connection()

        if connection_test['status'] == 'success':
            # الحصول على معلومات جميع الجداول
            tables_info = db_manager.get_all_tables_info()

            # تحويل إلى التنسيق المطلوب للـ API
            formatted_tables = []
            table_names_ar = {
                'users': 'المستخدمين',
                'user_settings': 'إعدادات المستخدمين',
                'branches': 'الفروع',
                'suppliers': 'الموردين',
                'item_categories': 'فئات الأصناف',
                'items': 'الأصناف',
                'purchase_requests': 'طلبات الشراء',
                'purchase_request_items': 'عناصر طلبات الشراء',
                'purchase_orders': 'أوامر الشراء',
                'purchase_order_items': 'عناصر أوامر الشراء',
                'goods_receipts': 'إيصالات البضائع',
                'goods_receipt_items': 'عناصر إيصالات البضائع',
                'stock_movements': 'حركات المخزون',
                'contracts': 'العقود',
                'invoices': 'الفواتير',
                'invoice_items': 'عناصر الفواتير',
                'payments': 'المدفوعات',
                'approvals': 'الموافقات',
                'workflow_templates': 'قوالب سير العمل',
                'workflow_steps': 'خطوات سير العمل',
                'budgets': 'الميزانيات',
                'expenses': 'المصروفات'
            }

            for table_info in tables_info:
                if table_info['status'] == 'active':
                    formatted_tables.append({
                        'name': table_names_ar.get(table_info['name'], table_info['name']),
                        'table': table_info['name'],
                        'count': table_info['row_count']
                    })

            return jsonify({
                'success': True,
                'connected': True,
                'database_type': connection_test.get('database_type', 'Unknown'),
                'database_url': connection_test.get('database_type', 'غير متاح'),
                'tables': formatted_tables,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'connected': False,
                'error': connection_test.get('error', 'Unknown error')
            })

    except Exception as e:
        logger.error(f"خطأ في فحص قاعدة البيانات: {str(e)}")
        return jsonify({
            'success': False,
            'connected': False,
            'error': str(e)
        })



@bp.route('/api/update-general-settings', methods=['POST'])
@login_required
def update_general_settings():
    """تحديث الإعدادات العامة"""
    try:
        data = request.get_json()
        logger.info(f"تحديث إعدادات المستخدم {current_user.id}: {data}")

        if not data:
            return jsonify({
                'success': False,
                'message': 'لم يتم استلام بيانات'
            })

        # البحث عن إعدادات المستخدم الحالية أو إنشاء جديدة
        user_settings = UserSettings.query.filter_by(user_id=current_user.id).first()

        if not user_settings:
            user_settings = UserSettings(user_id=current_user.id)
            db.session.add(user_settings)

        # تحديث الإعدادات
        user_settings.language = data.get('language', 'ar')

        # التحقق من صحة العملة المختارة
        selected_currency = data.get('currency', 'SAR')
        currency_exists = Currency.query.filter_by(code=selected_currency, is_active=1).first()
        if currency_exists:
            user_settings.currency = selected_currency
        else:
            # استخدام العملة الأساسية إذا كانت العملة المختارة غير صحيحة
            base_currency = Currency.get_base_currency()
            user_settings.currency = base_currency.code if base_currency else 'SAR'

        user_settings.timezone = data.get('timezone', 'Asia/Riyadh')
        user_settings.date_format = data.get('date_format', 'DD/MM/YYYY')
        user_settings.theme = data.get('theme', 'light')
        user_settings.items_per_page = int(data.get('items_per_page', 25))
        user_settings.updated_at = datetime.now()

        # حفظ في قاعدة البيانات
        db.session.commit()

        # مسح cache لضمان قراءة البيانات الجديدة
        cache.delete(f'user_settings_{current_user.id}')
        cache.clear()  # مسح جميع cache

        return jsonify({
            'success': True,
            'message': 'تم حفظ الإعدادات بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في تحديث الإعدادات: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

@bp.route('/api/get-general-settings')
@login_required
def get_general_settings():
    """الحصول على الإعدادات العامة"""
    try:
        # محاولة الحصول على الإعدادات من cache أولاً
        settings = cache.get(f'user_settings_{current_user.id}')

        if not settings:
            # البحث في قاعدة البيانات
            user_settings = UserSettings.query.filter_by(user_id=current_user.id).first()

            if user_settings:
                settings = {
                    'language': user_settings.language,
                    'currency': user_settings.currency,
                    'timezone': user_settings.timezone,
                    'date_format': user_settings.date_format,
                    'theme': user_settings.theme,
                    'items_per_page': user_settings.items_per_page
                }
                # حفظ في cache للمرات القادمة
                cache.set(f'user_settings_{current_user.id}', settings, timeout=86400)
            else:
                # إعدادات افتراضية
                base_currency = Currency.get_base_currency()
                default_currency = base_currency.code if base_currency else 'SAR'

                settings = {
                    'language': 'ar',
                    'currency': default_currency,
                    'timezone': 'Asia/Riyadh',
                    'date_format': 'DD/MM/YYYY',
                    'theme': 'light',
                    'items_per_page': 25
                }

        return jsonify({
            'success': True,
            'settings': settings
        })

    except Exception as e:
        logger.error(f"خطأ في الحصول على الإعدادات: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

@bp.route('/api/backup-database', methods=['POST'])
@login_required
def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        import shutil
        from datetime import datetime
        
        # مسار قاعدة البيانات الحالية
        db_path = current_app.config.get('SQLALCHEMY_DATABASE_URI')
        if db_path and db_path.startswith('sqlite:///'):
            db_file = db_path.replace('sqlite:///', '')
            
            # إنشاء مجلد النسخ الاحتياطية
            backup_dir = 'backups'
            os.makedirs(backup_dir, exist_ok=True)
            
            # اسم ملف النسخة الاحتياطية
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = f"{backup_dir}/backup_{timestamp}.db"
            
            # نسخ قاعدة البيانات
            shutil.copy2(db_file, backup_file)
            
            return jsonify({
                'success': True,
                'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
                'backup_file': backup_file,
                'timestamp': timestamp
            })
        else:
            return jsonify({
                'success': False,
                'message': 'نوع قاعدة البيانات غير مدعوم للنسخ الاحتياطي التلقائي'
            })
            
    except Exception as e:
        logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

@bp.route('/api/clear-cache', methods=['POST'])
@login_required
def clear_cache():
    """مسح التخزين المؤقت"""
    try:
        cache.clear()
        
        return jsonify({
            'success': True,
            'message': 'تم مسح التخزين المؤقت بنجاح'
        })
        
    except Exception as e:
        logger.error(f"خطأ في مسح التخزين المؤقت: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })


# ==================== إدارة الفروع ====================

@bp.route('/branches')
@login_required
def branches():
    """صفحة إدارة الفروع"""
    return render_template('settings/branches.html')


@bp.route('/api/branches', methods=['GET'])
@login_required
def get_branches():
    """الحصول على قائمة الفروع"""
    try:
        # استخدام Oracle Manager للحصول على الفروع
        from oracle_manager import get_oracle_manager
        oracle_mgr = get_oracle_manager()

        # استعلام للحصول على جميع الفروع بالحقول الجديدة
        query = """
        SELECT BRN_NO, BRN_LNAME, BRN_FNAME, BRN_LADD, BRN_FADD,
               BRN_LTELE, BRN_FTELE, BRN_LFAX, BRN_FFAX, BRN_LBOX,
               BRN_FBOX, BRN_IMG, BRN_CODE, DBLINK_NAME, IS_ACTIVE,
               CREATED_AT, UPDATED_AT
        FROM branches
        ORDER BY BRN_NO
        """
        branches_data = oracle_mgr.execute_query(query)

        # تحويل البيانات إلى قائمة
        branches = []
        for branch_data in branches_data:
            try:
                branch_dict = {
                    'brn_no': int(branch_data[0]) if branch_data[0] else 0,
                    'brn_lname': str(branch_data[1]) if branch_data[1] else '',
                    'brn_fname': str(branch_data[2]) if branch_data[2] else '',
                    'brn_ladd': str(branch_data[3]) if branch_data[3] else '',
                    'brn_fadd': str(branch_data[4]) if branch_data[4] else '',
                    'brn_ltele': str(branch_data[5]) if branch_data[5] else '',
                    'brn_ftele': str(branch_data[6]) if branch_data[6] else '',
                    'brn_lfax': str(branch_data[7]) if branch_data[7] else '',
                    'brn_ffax': str(branch_data[8]) if branch_data[8] else '',
                    'brn_lbox': str(branch_data[9]) if branch_data[9] else '',
                    'brn_fbox': str(branch_data[10]) if branch_data[10] else '',
                    'brn_img': str(branch_data[11]) if branch_data[11] else '',
                    'brn_code': str(branch_data[12]) if branch_data[12] else '',
                    'dblink_name': str(branch_data[13]) if branch_data[13] else '',
                    'is_active': bool(int(str(branch_data[14]))) if branch_data[14] else True,
                    'created_at': str(branch_data[15]) if branch_data[15] else None,
                    'updated_at': str(branch_data[16]) if branch_data[16] else None
                }
                branches.append(branch_dict)
            except Exception as e:
                logger.error(f"خطأ في تحويل بيانات الفرع: {e}")
                continue

        return jsonify({
            'success': True,
            'branches': branches
        })

    except Exception as e:
        logger.error(f"خطأ في جلب الفروع: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })


@bp.route('/api/branches/<int:branch_id>', methods=['GET'])
@login_required
def get_branch(branch_id):
    """الحصول على فرع محدد"""
    try:
        # استخدام Oracle Manager للحصول على فرع محدد
        from oracle_manager import get_oracle_manager
        oracle_mgr = get_oracle_manager()

        query = """
        SELECT BRN_NO, BRN_LNAME, BRN_FNAME, BRN_LADD, BRN_FADD,
               BRN_LTELE, BRN_FTELE, BRN_LFAX, BRN_FFAX, BRN_LBOX,
               BRN_FBOX, BRN_IMG, BRN_CODE, DBLINK_NAME, IS_ACTIVE,
               CREATED_AT, UPDATED_AT
        FROM branches
        WHERE BRN_NO = :1
        """
        branch_data = oracle_mgr.execute_query(query, [branch_id])

        if not branch_data:
            return jsonify({
                'success': False,
                'message': 'الفرع غير موجود'
            }), 404

        branch_info = branch_data[0]
        branch_dict = {
            'brn_no': branch_info[0],
            'brn_lname': branch_info[1],
            'brn_fname': branch_info[2],
            'brn_ladd': branch_info[3],
            'brn_fadd': branch_info[4],
            'brn_ltele': branch_info[5],
            'brn_ftele': branch_info[6],
            'brn_lfax': branch_info[7],
            'brn_ffax': branch_info[8],
            'brn_lbox': branch_info[9],
            'brn_fbox': branch_info[10],
            'brn_img': branch_info[11],
            'brn_code': branch_info[12],
            'dblink_name': branch_info[13],
            'is_active': bool(int(branch_info[14])) if branch_info[14] else True,
            'created_at': branch_info[15].strftime('%Y-%m-%d %H:%M:%S') if branch_info[15] else None,
            'updated_at': branch_info[16].strftime('%Y-%m-%d %H:%M:%S') if branch_info[16] else None
        }

        return jsonify({
            'success': True,
            'branch': branch_dict
        })

    except Exception as e:
        logger.error(f"خطأ في جلب الفرع: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })


@bp.route('/api/branches', methods=['POST'])
@login_required
def create_branch():
    """إنشاء فرع جديد مع الربط الصحيح للأعمدة"""
    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        if not data.get('brn_lname'):
            return jsonify({
                'success': False,
                'message': 'اسم الفرع مطلوب'
            })

        # استخدام Oracle Manager لإنشاء فرع جديد
        from oracle_manager import get_oracle_manager
        oracle_mgr = get_oracle_manager()

        # إدراج فرع جديد مع جميع الحقول
        insert_sql = """
        INSERT INTO branches (
            BRN_NO, BRN_LNAME, BRN_FNAME, BRN_LADD, BRN_FADD,
            BRN_LTELE, BRN_FTELE, BRN_LFAX, BRN_FFAX, BRN_LBOX,
            BRN_FBOX, BRN_IMG, BRN_CODE, DBLINK_NAME, IS_ACTIVE,
            CREATED_AT, UPDATED_AT
        ) VALUES (
            branches_seq.NEXTVAL, :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13, 1, SYSDATE, SYSDATE
        )
        """

        # تحضير البيانات
        params = [
            data.get('brn_lname', ''),      # اسم الفرع
            data.get('brn_fname', ''),      # اسم الفرع بالإنجليزي
            data.get('brn_ladd', ''),       # عنوان الفرع
            data.get('brn_fadd', ''),       # عنوان الفرع بالإنجليزي
            data.get('brn_ltele', ''),      # رقم الهاتف
            data.get('brn_ftele', ''),      # رقم الهاتف بالإنجليزي
            data.get('brn_lfax', ''),       # رقم الفاكس
            data.get('brn_ffax', ''),       # رقم الفاكس بالإنجليزي
            data.get('brn_lbox', ''),       # رقم صندوق البريد
            data.get('brn_fbox', ''),       # رقم صندوق البريد بالإنجليزي
            data.get('brn_img', ''),        # شعار الفرع
            data.get('brn_code', ''),       # اسم المستخدم في قاعدة البيانات
            data.get('dblink_name', '')     # اسم DBLINKS
        ]

        # تنفيذ الإدراج
        insert_result = oracle_mgr.execute_update(insert_sql, params)
        logger.info(f"نتيجة الإدراج: {insert_result}")

        # الحصول على الفرع المُدرج حديثاً
        get_new_branch_sql = """
        SELECT BRN_NO, BRN_LNAME, BRN_FNAME, BRN_LADD, BRN_FADD,
               BRN_LTELE, BRN_FTELE, BRN_LFAX, BRN_FFAX, BRN_LBOX,
               BRN_FBOX, BRN_IMG, BRN_CODE, DBLINK_NAME, IS_ACTIVE,
               CREATED_AT, UPDATED_AT
        FROM branches
        WHERE BRN_NO = (SELECT MAX(BRN_NO) FROM branches)
        """
        logger.info(f"استعلام الحصول على الفرع: {get_new_branch_sql}")
        new_branch_data = oracle_mgr.execute_query(get_new_branch_sql)
        logger.info(f"بيانات الفرع الجديد: {new_branch_data}")

        if new_branch_data:
            branch_info = new_branch_data[0]
            branch_dict = {
                'brn_no': branch_info[0],
                'brn_lname': branch_info[1],
                'brn_fname': branch_info[2],
                'brn_ladd': branch_info[3],
                'brn_fadd': branch_info[4],
                'brn_ltele': branch_info[5],
                'brn_ftele': branch_info[6],
                'brn_lfax': branch_info[7],
                'brn_ffax': branch_info[8],
                'brn_lbox': branch_info[9],
                'brn_fbox': branch_info[10],
                'brn_img': branch_info[11],
                'brn_code': branch_info[12],
                'dblink_name': branch_info[13],
                'is_active': bool(int(branch_info[14])) if branch_info[14] else True,
                'created_at': branch_info[15].strftime('%Y-%m-%d %H:%M:%S') if branch_info[15] else None,
                'updated_at': branch_info[16].strftime('%Y-%m-%d %H:%M:%S') if branch_info[16] else None
            }
        else:
            branch_dict = {}

        return jsonify({
            'success': True,
            'message': 'تم إنشاء الفرع بنجاح',
            'branch': branch_dict
        })

    except Exception as e:
        logger.error(f"خطأ في إنشاء الفرع: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })


@bp.route('/api/branches/<int:branch_id>', methods=['PUT'])
@login_required
def update_branch(branch_id):
    """تحديث فرع موجود مع الربط الصحيح للأعمدة"""
    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        if not data.get('brn_lname'):
            return jsonify({
                'success': False,
                'message': 'اسم الفرع مطلوب'
            })

        # استخدام Oracle Manager لتحديث الفرع
        from oracle_manager import get_oracle_manager
        oracle_mgr = get_oracle_manager()

        # تحديث الفرع
        update_sql = """
        UPDATE branches SET
            BRN_LNAME = :1, BRN_FNAME = :2, BRN_LADD = :3, BRN_FADD = :4,
            BRN_LTELE = :5, BRN_FTELE = :6, BRN_LFAX = :7, BRN_FFAX = :8,
            BRN_LBOX = :9, BRN_FBOX = :10, BRN_IMG = :11, BRN_CODE = :12,
            DBLINK_NAME = :13, UPDATED_AT = SYSDATE
        WHERE BRN_NO = :14
        """

        # تحضير البيانات
        params = [
            data.get('brn_lname', ''),      # اسم الفرع
            data.get('brn_fname', ''),      # اسم الفرع بالإنجليزي
            data.get('brn_ladd', ''),       # عنوان الفرع
            data.get('brn_fadd', ''),       # عنوان الفرع بالإنجليزي
            data.get('brn_ltele', ''),      # رقم الهاتف
            data.get('brn_ftele', ''),      # رقم الهاتف بالإنجليزي
            data.get('brn_lfax', ''),       # رقم الفاكس
            data.get('brn_ffax', ''),       # رقم الفاكس بالإنجليزي
            data.get('brn_lbox', ''),       # رقم صندوق البريد
            data.get('brn_fbox', ''),       # رقم صندوق البريد بالإنجليزي
            data.get('brn_img', ''),        # شعار الفرع
            data.get('brn_code', ''),       # اسم المستخدم في قاعدة البيانات
            data.get('dblink_name', ''),    # اسم DBLINKS
            branch_id                       # رقم الفرع
        ]

        # تنفيذ التحديث
        oracle_mgr.execute_update(update_sql, params)

        # الحصول على الفرع المُحدث
        get_updated_branch_sql = """
        SELECT BRN_NO, BRN_LNAME, BRN_FNAME, BRN_LADD, BRN_FADD,
               BRN_LTELE, BRN_FTELE, BRN_LFAX, BRN_FFAX, BRN_LBOX,
               BRN_FBOX, BRN_IMG, BRN_CODE, DBLINK_NAME, IS_ACTIVE,
               CREATED_AT, UPDATED_AT
        FROM branches
        WHERE BRN_NO = :1
        """
        updated_branch_data = oracle_mgr.execute_query(get_updated_branch_sql, [branch_id])

        if updated_branch_data:
            branch_info = updated_branch_data[0]
            branch_dict = {
                'brn_no': branch_info[0],
                'brn_lname': branch_info[1],
                'brn_fname': branch_info[2],
                'brn_ladd': branch_info[3],
                'brn_fadd': branch_info[4],
                'brn_ltele': branch_info[5],
                'brn_ftele': branch_info[6],
                'brn_lfax': branch_info[7],
                'brn_ffax': branch_info[8],
                'brn_lbox': branch_info[9],
                'brn_fbox': branch_info[10],
                'brn_img': branch_info[11],
                'brn_code': branch_info[12],
                'dblink_name': branch_info[13],
                'is_active': bool(int(branch_info[14])) if branch_info[14] else True,
                'created_at': branch_info[15].strftime('%Y-%m-%d %H:%M:%S') if branch_info[15] else None,
                'updated_at': branch_info[16].strftime('%Y-%m-%d %H:%M:%S') if branch_info[16] else None
            }
        else:
            branch_dict = {}

        return jsonify({
            'success': True,
            'message': 'تم تحديث الفرع بنجاح',
            'branch': branch_dict
        })

    except Exception as e:
        logger.error(f"خطأ في تحديث الفرع: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })


@bp.route('/api/branches/<int:branch_id>', methods=['DELETE'])
@login_required
def delete_branch(branch_id):
    """حذف فرع"""
    try:
        # استخدام Oracle Manager لحذف الفرع
        from oracle_manager import get_oracle_manager
        oracle_mgr = get_oracle_manager()

        # الحصول على اسم الفرع أولاً
        get_branch_sql = "SELECT BRN_LNAME FROM branches WHERE BRN_NO = :1"
        branch_data = oracle_mgr.execute_query(get_branch_sql, [branch_id])

        if not branch_data:
            return jsonify({
                'success': False,
                'message': 'الفرع غير موجود'
            }), 404

        branch_name = branch_data[0][0]

        # حذف الفرع
        delete_sql = "DELETE FROM branches WHERE BRN_NO = :1"
        oracle_mgr.execute_update(delete_sql, [branch_id])

        return jsonify({
            'success': True,
            'message': f'تم حذف الفرع "{branch_name}" بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في حذف الفرع: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

@bp.route('/api/upload-logo', methods=['POST'])
@login_required
def upload_logo():
    """رفع شعار الفرع"""
    try:
        if 'logo' not in request.files:
            return jsonify({
                'success': False,
                'message': 'لم يتم اختيار ملف'
            })

        file = request.files['logo']
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': 'لم يتم اختيار ملف'
            })

        # التحقق من نوع الملف
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
        if not ('.' in file.filename and
                file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({
                'success': False,
                'message': 'نوع الملف غير مدعوم. الأنواع المدعومة: PNG, JPG, JPEG, GIF, BMP, WEBP'
            })

        # إنشاء مجلد الشعارات إذا لم يكن موجوداً
        upload_folder = os.path.join(current_app.root_path, 'static', 'uploads', 'logos')
        os.makedirs(upload_folder, exist_ok=True)

        # إنشاء اسم ملف فريد
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        unique_filename = f"{uuid.uuid4().hex}.{file_extension}"
        file_path = os.path.join(upload_folder, unique_filename)

        # حفظ الملف
        file.save(file_path)

        # إنشاء المسار النسبي للحفظ في قاعدة البيانات
        relative_path = f"/static/uploads/logos/{unique_filename}"

        logger.info(f"تم رفع شعار جديد: {relative_path}")

        return jsonify({
            'success': True,
            'message': 'تم رفع الشعار بنجاح',
            'file_path': relative_path
        })

    except Exception as e:
        logger.error(f"خطأ في رفع الشعار: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في رفع الشعار: {str(e)}'
        })
