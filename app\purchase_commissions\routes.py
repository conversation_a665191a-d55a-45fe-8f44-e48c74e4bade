# -*- coding: utf-8 -*-
"""
مسارات نظام عمولات مندوبي المشتريات
Routes for Purchase Representatives Commission System
"""

from flask import render_template, request, jsonify, flash, redirect, url_for, send_file, make_response
from flask_login import login_required, current_user
from app.purchase_commissions import purchase_commissions_bp
from database_manager import DatabaseManager
from app.purchase_commissions.models import *
import logging
import io
import oracledb


def safe_clob_value(value):
    """
    معالجة آمنة لقيم CLOB - تجنب Oracle ORA-22275 error
    """
    if value is None:
        return None
    if isinstance(value, str):
        stripped = value.strip()
        return stripped if stripped else None
    return value

# استيراد اختياري للمكتبات الخارجية
try:
    import openpyxl
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
    from openpyxl.utils import get_column_letter
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

try:
    from reportlab.lib import colors
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
from reportlab.lib.units import inch
from datetime import datetime, timedelta

# إعداد نظام السجلات
logger = logging.getLogger(__name__)


@purchase_commissions_bp.route('/')
@login_required
def index():
    """الصفحة الرئيسية لنظام العمولات"""
    try:
        db_manager = DatabaseManager()

        # جلب إحصائيات سريعة
        stats = get_simple_stats(db_manager)

        return render_template('purchase_commissions/index.html', stats=stats)

    except Exception as e:
        logger.error(f"خطأ في تحميل الصفحة الرئيسية: {e}")
        flash(f'خطأ في تحميل البيانات: {str(e)}', 'error')
        # إرسال إحصائيات افتراضية في حالة الخطأ
        default_stats = {
            'representatives': {'total': 0, 'active': 0},
            'commission_types': {'total': 0},
            'commission_rules': {'total': 0},
            'calculations': {'total': 0, 'pending': 0, 'approved': 0, 'paid': 0},
            'commissions': {'total_amount': 0, 'this_month': 0, 'paid_amount': 0},
            'orders': {'linked': 0, 'total_value': 0}
        }
        return render_template('purchase_commissions/index.html', stats=default_stats)


def get_simple_stats(db_manager):
    """جلب إحصائيات مبسطة للصفحة الرئيسية"""
    try:
        stats = {
            'representatives': {'total': 0, 'active': 0},
            'commission_types': {'total': 0},
            'commission_rules': {'total': 0},
            'calculations': {'total': 0, 'pending': 0, 'approved': 0, 'paid': 0},
            'commissions': {'total_amount': 0, 'this_month': 0, 'paid_amount': 0},
            'orders': {'linked': 0, 'total_value': 0}
        }

        # إحصائيات المندوبين
        reps_query = """
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active
        FROM purchase_representatives
        """
        reps_data = db_manager.execute_query(reps_query)
        if reps_data:
            row = reps_data[0]
            stats['representatives']['total'] = row[0] or 0
            stats['representatives']['active'] = row[1] or 0

        # إحصائيات أنواع العمولات
        types_query = "SELECT COUNT(*) FROM commission_types WHERE is_active = 1"
        types_data = db_manager.execute_query(types_query)
        if types_data:
            stats['commission_types']['total'] = types_data[0][0] or 0

        # إحصائيات قواعد العمولات
        rules_query = "SELECT COUNT(*) FROM commission_rules WHERE is_active = 1"
        rules_data = db_manager.execute_query(rules_query)
        if rules_data:
            stats['commission_rules']['total'] = rules_data[0][0] or 0

        # إحصائيات الحسابات والعمولات
        calc_query = """
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN status = 'calculated' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
            SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paid,
            SUM(commission_amount) as total_amount,
            SUM(CASE WHEN calculation_date >= TRUNC(SYSDATE, 'MM') THEN commission_amount ELSE 0 END) as this_month,
            SUM(CASE WHEN status = 'paid' THEN commission_amount ELSE 0 END) as paid_amount
        FROM commission_calculations
        """
        calc_data = db_manager.execute_query(calc_query)
        if calc_data:
            row = calc_data[0]
            stats['calculations']['total'] = row[0] or 0
            stats['calculations']['pending'] = row[1] or 0
            stats['calculations']['approved'] = row[2] or 0
            stats['calculations']['paid'] = row[3] or 0
            stats['commissions']['total_amount'] = row[4] or 0
            stats['commissions']['this_month'] = row[5] or 0
            stats['commissions']['paid_amount'] = row[6] or 0

        # إحصائيات أوامر الشراء المربوطة
        try:
            orders_query = """
            SELECT
                COUNT(DISTINCT purchase_order_id) as linked,
                SUM(order_total_value) as total_value
            FROM commission_tracking
            """
            orders_data = db_manager.execute_query(orders_query)
            if orders_data:
                row = orders_data[0]
                stats['orders']['linked'] = row[0] or 0
                stats['orders']['total_value'] = row[1] or 0
        except Exception as e:
            # في حالة عدم وجود جدول commission_tracking بعد
            logger.warning(f"جدول commission_tracking غير متاح: {e}")
            stats['orders']['linked'] = 0
            stats['orders']['total_value'] = 0

        return stats

    except Exception as e:
        logger.error(f"خطأ في جلب الإحصائيات المبسطة: {e}")
        return {
            'representatives': {'total': 0, 'active': 0},
            'commission_types': {'total': 0},
            'commission_rules': {'total': 0},
            'calculations': {'total': 0, 'pending': 0, 'approved': 0, 'paid': 0},
            'commissions': {'total_amount': 0, 'this_month': 0, 'paid_amount': 0},
            'orders': {'linked': 0, 'total_value': 0}
        }


@purchase_commissions_bp.route('/representatives')
@login_required
def representatives():
    """صفحة إدارة المندوبين"""
    try:
        db_manager = DatabaseManager()

        # جلب قائمة المندوبين
        reps = get_all_representatives(db_manager)

        return render_template('purchase_commissions/representatives.html', representatives=reps)

    except Exception as e:
        logger.error(f"خطأ في تحميل المندوبين: {e}")
        flash(f'خطأ في تحميل البيانات: {str(e)}', 'error')
        return render_template('purchase_commissions/representatives.html', representatives=[])


@purchase_commissions_bp.route('/representatives/add', methods=['POST'])
@login_required
def add_representative():
    """إضافة مندوب جديد"""
    try:
        db_manager = DatabaseManager()

        # جلب البيانات من النموذج
        rep_code = request.form.get('rep_code')
        rep_name = request.form.get('rep_name')
        rep_name_en = request.form.get('rep_name_en')
        specialization = request.form.get('specialization')
        phone = request.form.get('phone')
        email = request.form.get('email')
        target_monthly_orders = request.form.get('target_monthly_orders', 0)
        target_monthly_quantity = request.form.get('target_monthly_quantity', 0)
        target_monthly_value = request.form.get('target_monthly_value', 0)
        is_active = 1 if request.form.get('is_active') else 0
        commission_eligible = 1 if request.form.get('commission_eligible') else 0
        notes = request.form.get('notes')

        # التحقق من البيانات المطلوبة
        if not rep_code or not rep_name:
            flash('كود المندوب والاسم مطلوبان', 'error')
            return redirect(url_for('purchase_commissions.representatives'))

        # التحقق من عدم تكرار الكود
        check_query = "SELECT COUNT(*) FROM purchase_representatives WHERE rep_code = :1"
        existing = db_manager.execute_query(check_query, (rep_code,))
        if existing and existing[0][0] > 0:
            flash(f'كود المندوب {rep_code} موجود مسبقاً', 'error')
            return redirect(url_for('purchase_commissions.representatives'))

        # معالجة آمنة لحقل NOTES (CLOB)
        notes_value = safe_clob_value(notes)

        # إدراج المندوب الجديد
        insert_query = """
        INSERT INTO purchase_representatives (
            id, rep_code, rep_name, rep_name_en, specialization, phone, email,
            target_monthly_orders, target_monthly_quantity, target_monthly_value,
            is_active, commission_eligible, notes, created_by
        ) VALUES (
            purchase_representatives_seq.NEXTVAL, :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13
        )
        """

        db_manager.execute_update(insert_query, (
            rep_code, rep_name, rep_name_en, specialization, phone, email,
            int(target_monthly_orders) if target_monthly_orders else 0,
            int(target_monthly_quantity) if target_monthly_quantity else 0,
            float(target_monthly_value) if target_monthly_value else 0,
            is_active, commission_eligible, notes_value, current_user.id
        ))

        flash(f'تم إضافة المندوب {rep_name} بنجاح', 'success')
        logger.info(f"تم إضافة مندوب جديد: {rep_code} - {rep_name}")

    except Exception as e:
        logger.error(f"خطأ في إضافة المندوب: {e}")
        flash('حدث خطأ أثناء إضافة المندوب', 'error')

    return redirect(url_for('purchase_commissions.representatives'))


@purchase_commissions_bp.route('/representatives/edit/<int:rep_id>', methods=['GET', 'POST'])
@login_required
def edit_representative(rep_id):
    """تعديل مندوب"""
    try:
        db_manager = DatabaseManager()

        if request.method == 'GET':
            # جلب بيانات المندوب
            rep_query = """
            SELECT id, rep_code, rep_name, rep_name_en, specialization, phone, email,
                   target_monthly_orders, target_monthly_quantity, target_monthly_value,
                   is_active, commission_eligible, notes
            FROM purchase_representatives WHERE id = :1
            """
            rep_data = db_manager.execute_query(rep_query, (rep_id,))

            if not rep_data:
                flash('المندوب غير موجود', 'error')
                return redirect(url_for('purchase_commissions.representatives'))

            rep = {
                'id': rep_data[0][0],
                'rep_code': rep_data[0][1],
                'rep_name': rep_data[0][2],
                'rep_name_en': rep_data[0][3],
                'specialization': rep_data[0][4],
                'phone': rep_data[0][5],
                'email': rep_data[0][6],
                'target_monthly_orders': rep_data[0][7],
                'target_monthly_quantity': rep_data[0][8],
                'target_monthly_value': rep_data[0][9],
                'is_active': rep_data[0][10],
                'commission_eligible': rep_data[0][11],
                'notes': rep_data[0][12]
            }

            return render_template('purchase_commissions/edit_representative.html', representative=rep)

        else:  # POST
            # تحديث بيانات المندوب
            rep_code = request.form.get('rep_code')
            rep_name = request.form.get('rep_name')
            rep_name_en = request.form.get('rep_name_en')
            specialization = request.form.get('specialization')
            phone = request.form.get('phone')
            email = request.form.get('email')
            target_monthly_orders = request.form.get('target_monthly_orders', 0)
            target_monthly_quantity = request.form.get('target_monthly_quantity', 0)
            target_monthly_value = request.form.get('target_monthly_value', 0)
            is_active = 1 if request.form.get('is_active') else 0
            commission_eligible = 1 if request.form.get('commission_eligible') else 0
            notes = request.form.get('notes')

            if not rep_code or not rep_name:
                flash('كود المندوب والاسم مطلوبان', 'error')
                return redirect(url_for('purchase_commissions.edit_representative', rep_id=rep_id))

            # معالجة آمنة لحقل NOTES (CLOB)
            notes_value = safe_clob_value(notes)

            update_query = """
            UPDATE purchase_representatives SET
                rep_code = :1, rep_name = :2, rep_name_en = :3, specialization = :4,
                phone = :5, email = :6, target_monthly_orders = :7,
                target_monthly_quantity = :8, target_monthly_value = :9,
                is_active = :10, commission_eligible = :11, notes = :12,
                updated_at = SYSDATE, updated_by = :13
            WHERE id = :14
            """

            db_manager.execute_update(update_query, (
                rep_code, rep_name, rep_name_en, specialization, phone, email,
                int(target_monthly_orders) if target_monthly_orders else 0,
                float(target_monthly_quantity) if target_monthly_quantity else 0,
                float(target_monthly_value) if target_monthly_value else 0,
                is_active, commission_eligible, notes_value, current_user.id, rep_id
            ))

            flash(f'تم تحديث بيانات المندوب {rep_name} بنجاح', 'success')
            logger.info(f"تم تحديث مندوب: {rep_name}")

            return redirect(url_for('purchase_commissions.representatives'))

    except Exception as e:
        logger.error(f"خطأ في تعديل المندوب: {e}")
        flash('حدث خطأ أثناء تعديل المندوب', 'error')
        return redirect(url_for('purchase_commissions.representatives'))


@purchase_commissions_bp.route('/representatives/delete/<int:rep_id>', methods=['POST', 'GET'])
def delete_representative(rep_id):
    """حذف مندوب"""
    print(f"🔥 DELETE ROUTE CALLED FOR REP_ID: {rep_id}")
    try:
        db_manager = DatabaseManager()

        # حذف جميع البيانات المرتبطة بالمندوب

        # 1. حذف حسابات العمولات
        db_manager.execute_update(
            "DELETE FROM commission_calculations WHERE rule_id IN (SELECT id FROM commission_rules WHERE rep_id = :1)", (rep_id,)
        )

        # 2. حذف قواعد العمولات
        db_manager.execute_update(
            "DELETE FROM commission_rules WHERE rep_id = :1", (rep_id,)
        )

        # 3. حذف ربط أوامر الشراء
        db_manager.execute_update(
            "DELETE FROM purchase_order_representatives WHERE rep_id = :1", (rep_id,)
        )

        # 4. حذف تتبع العمولات
        db_manager.execute_update(
            "DELETE FROM commission_tracking WHERE rep_id = :1", (rep_id,)
        )



        # 5. حذف المندوب
        result = db_manager.execute_update(
            "DELETE FROM purchase_representatives WHERE id = :1", (rep_id,)
        )

        print(f"🔥 DELETE RESULT: {result}")

        flash('تم حذف المندوب بنجاح', 'success')
        logger.info(f"تم حذف مندوب: {rep_id}")

    except Exception as e:
        logger.error(f"خطأ في حذف المندوب: {e}")
        flash('حدث خطأ أثناء حذف المندوب', 'error')

    return redirect(url_for('purchase_commissions.representatives'))


@purchase_commissions_bp.route('/commission-types')
@login_required
def commission_types():
    """صفحة إدارة أنواع العمولات"""
    try:
        db_manager = DatabaseManager()

        # جلب أنواع العمولات
        types = get_all_commission_types(db_manager)

        return render_template('purchase_commissions/commission_types.html', commission_types=types)

    except Exception as e:
        logger.error(f"خطأ في تحميل أنواع العمولات: {e}")
        flash(f'خطأ في تحميل البيانات: {str(e)}', 'error')
        return render_template('purchase_commissions/commission_types.html', commission_types=[])


@purchase_commissions_bp.route('/commission-types/add', methods=['POST'])
@login_required
def add_commission_type():
    """إضافة نوع عمولة جديد"""
    try:
        db_manager = DatabaseManager()

        # جلب البيانات من النموذج
        type_code = request.form.get('type_code')
        type_name = request.form.get('type_name')
        type_name_en = request.form.get('type_name_en')
        calculation_method = request.form.get('calculation_method')
        description = request.form.get('description')
        display_order = request.form.get('display_order', 1)
        is_active = 1 if request.form.get('is_active') else 0
        supports_combination = 1 if request.form.get('supports_combination') else 0

        # التحقق من البيانات المطلوبة
        if not type_code or not type_name or not calculation_method:
            flash('كود النوع والاسم وطريقة الحساب مطلوبة', 'error')
            return redirect(url_for('purchase_commissions.commission_types'))

        # التحقق من عدم تكرار الكود
        check_query = "SELECT COUNT(*) FROM commission_types WHERE type_code = :1"
        existing = db_manager.execute_query(check_query, (type_code,))
        if existing and existing[0][0] > 0:
            flash(f'كود النوع {type_code} موجود مسبقاً', 'error')
            return redirect(url_for('purchase_commissions.commission_types'))

        # إدراج النوع الجديد
        insert_query = """
        INSERT INTO commission_types (
            id, type_code, type_name, type_name_en, calculation_method,
            description, display_order, is_active, supports_combination, created_by
        ) VALUES (
            commission_types_seq.NEXTVAL, :1, :2, :3, :4, :5, :6, :7, :8, :9
        )
        """

        db_manager.execute_update(insert_query, (
            type_code, type_name, type_name_en, calculation_method, description,
            int(display_order) if display_order else 1,
            is_active, supports_combination, current_user.id
        ))

        flash(f'تم إضافة نوع العمولة {type_name} بنجاح', 'success')
        logger.info(f"تم إضافة نوع عمولة جديد: {type_code} - {type_name}")

    except Exception as e:
        logger.error(f"خطأ في إضافة نوع العمولة: {e}")
        flash('حدث خطأ أثناء إضافة نوع العمولة', 'error')

    return redirect(url_for('purchase_commissions.commission_types'))


@purchase_commissions_bp.route('/commission-rules')
@login_required
def commission_rules():
    """صفحة إدارة قواعد العمولات"""
    try:
        db_manager = DatabaseManager()
        
        # جلب قواعد العمولات
        rules = get_all_commission_rules(db_manager)
        
        # جلب المندوبين وأنواع العمولات والعملات للفلاتر
        representatives = get_all_representatives(db_manager)
        commission_types = get_all_commission_types(db_manager)
        currencies = get_all_currencies(db_manager)
        
        return render_template('purchase_commissions/commission_rules.html', 
                             commission_rules=rules,
                             representatives=representatives,
                             commission_types=commission_types,
                             currencies=currencies)
        
    except Exception as e:
        logger.error(f"خطأ في تحميل قواعد العمولات: {e}")
        flash(f'خطأ في تحميل البيانات: {str(e)}', 'error')
        return render_template('purchase_commissions/commission_rules.html',
                             commission_rules=[],
                             representatives=[],
                             commission_types=[],
                             currencies=[])


@purchase_commissions_bp.route('/add-commission-rule', methods=['POST'])
def add_commission_rule():
    """إضافة قاعدة عمولة جديدة"""
    try:
        db_manager = DatabaseManager()

        # جلب البيانات من الطلب (JSON أو Form)
        if request.is_json:
            data = request.get_json()
            rule_name = data.get('rule_name')
            rule_description = data.get('rule_description')
            commission_type_id = data.get('commission_type_id')
            rep_id = data.get('rep_id')
            currency_id = data.get('currency_id')
            item_category = data.get('item_category')
            fixed_amount = data.get('fixed_amount', 0)
            percentage_rate = data.get('percentage_rate', 0)
            quantity_rate = data.get('quantity_rate', 0)
            quantity_unit = data.get('quantity_unit')
            min_order_value = data.get('min_order_value', 0)
            max_commission = data.get('max_commission')
            effective_from = data.get('effective_from')
            effective_to = data.get('effective_to')
            priority_order = data.get('priority_order', 1)
            is_active = 1 if data.get('is_active') else 0
        else:
            # جلب البيانات من النموذج (للتوافق مع الإصدار الحالي)
            rule_name = request.form.get('rule_name')
            rule_description = request.form.get('rule_description')
            commission_type_id = request.form.get('commission_type_id')
            rep_id = request.form.get('rep_id')
            currency_id = request.form.get('currency_id')
            item_category = request.form.get('item_category')
            fixed_amount = request.form.get('fixed_amount', 0)
            percentage_rate = request.form.get('percentage_rate', 0)
            quantity_rate = request.form.get('quantity_rate', 0)
            quantity_unit = request.form.get('quantity_unit')
            min_order_value = request.form.get('min_order_value', 0)
            max_commission = request.form.get('max_commission')
            effective_from = request.form.get('effective_from')
            effective_to = request.form.get('effective_to')
            priority_order = request.form.get('priority_order', 1)
            is_active = 1 if request.form.get('is_active') else 0

        # التحقق من البيانات المطلوبة
        if not rule_name or not commission_type_id or not rep_id:
            flash('اسم القاعدة ونوع العمولة والمندوب مطلوبة', 'error')
            return redirect(url_for('purchase_commissions.commission_rules'))

        # إدراج القاعدة الجديدة
        insert_query = """
        INSERT INTO commission_rules (
            id, rule_name, rule_description, commission_type_id, rep_id, item_category,
            fixed_amount, percentage_rate, quantity_rate, quantity_unit, min_order_value,
            max_commission, effective_from, effective_to, priority_order, is_active, created_by
        ) VALUES (
            commission_rules_seq.NEXTVAL, :1, :2, :3, :4, :5, :6, :7, :8, :9, :10,
            :11, :12, :13, :14, :15, :16
        )
        """

        # تحضير التواريخ
        eff_from = effective_from if effective_from else None
        eff_to = effective_to if effective_to else None

        # تحضير المعلمات
        params = [
            rule_name,  # :1
            None,  # :2 - سيتم ملؤه لاحقًا بالقيمة المُرجعة
            int(commission_type_id),  # :3
            int(rep_id),  # :4
            int(currency_id) if currency_id else None,  # :5
            item_category,  # :6
            float(fixed_amount) if fixed_amount else 0,  # :7
            float(percentage_rate) if percentage_rate else 0,  # :8
            float(quantity_rate) if quantity_rate else 0,  # :9
            quantity_unit,  # :10
            float(min_order_value) if min_order_value else 0,  # :11
            float(max_commission) if max_commission else None,  # :12
            eff_from,  # :13
            eff_to,  # :14
            int(priority_order) if priority_order else 1,  # :15
            is_active,  # :16
            '1'  # :17 - user ID مؤقت
        ]
        
        # تنفيذ الاستعلام
        db_manager.execute_update(insert_query, params)

        print(f"✅ تم إضافة قاعدة العمولة: {rule_name}")

        flash(f'تم إضافة قاعدة العمولة {rule_name} بنجاح', 'success')
        logger.info(f"تم إضافة قاعدة عمولة جديدة: {rule_name}")
        
        # إذا كان الطلب JSON، أرجع استجابة JSON
        if request.is_json:
            return jsonify({
                'success': True,
                'message': f'تم إضافة قاعدة العمولة {rule_name} بنجاح'
            })

    except Exception as e:
        logger.error(f"خطأ في إضافة قاعدة العمولة: {e}")
        
        # إذا كان الطلب JSON، أرجع استجابة JSON
        if request.is_json:
            return jsonify({
                'success': False,
                'message': f'حدث خطأ أثناء إضافة قاعدة العمولة: {str(e)}'
            }), 500
        else:
            flash('حدث خطأ أثناء إضافة قاعدة العمولة', 'error')

    # إذا لم يكن الطلب JSON، أعد التوجيه للصفحة
    return redirect(url_for('purchase_commissions.commission_rules'))


@purchase_commissions_bp.route('/commission-rules/edit/<int:rule_id>', methods=['GET', 'POST'])
@login_required
def edit_commission_rule(rule_id):
    """تعديل قاعدة عمولة"""
    try:
        db_manager = DatabaseManager()

        if request.method == 'GET':
            # جلب بيانات القاعدة
            rule_query = """
            SELECT cr.id, cr.rule_name, cr.rule_description, cr.commission_type_id,
                   cr.rep_id, cr.item_category, cr.fixed_amount, cr.percentage_rate,
                   cr.quantity_rate, cr.quantity_unit, cr.min_order_value,
                   cr.max_commission, cr.effective_from, cr.effective_to,
                   cr.priority_order, cr.is_active, ct.type_name, pr.rep_name
            FROM commission_rules cr
            JOIN commission_types ct ON cr.commission_type_id = ct.id
            LEFT JOIN purchase_representatives pr ON cr.rep_id = pr.id
            WHERE cr.id = :1
            """
            rule_data = db_manager.execute_query(rule_query, (rule_id,))

            if not rule_data:
                flash('قاعدة العمولة غير موجودة', 'error')
                return redirect(url_for('purchase_commissions.commission_rules'))

            row = rule_data[0]
            rule = {
                'id': row[0],
                'rule_name': row[1],
                'rule_description': row[2].read() if hasattr(row[2], 'read') else row[2],
                'commission_type_id': row[3],
                'rep_id': row[4],
                'item_category': row[5],
                'fixed_amount': row[6] or 0,
                'percentage_rate': row[7] or 0,
                'quantity_rate': row[8] or 0,
                'quantity_unit': row[9],
                'min_order_value': row[10] or 0,
                'max_commission': row[11],
                'effective_from': row[12],
                'effective_to': row[13],
                'priority_order': row[14] or 1,
                'is_active': row[15] or 0,
                'type_name': row[16],
                'rep_name': row[17]
            }

            # جلب أنواع العمولات والمندوبين
            commission_types = get_commission_types()
            representatives = get_representatives()

            return render_template('purchase_commissions/edit_commission_rule.html',
                                 rule=rule, commission_types=commission_types,
                                 representatives=representatives)

        else:  # POST
            # تحديث بيانات القاعدة
            rule_name = request.form.get('rule_name')
            rule_description = request.form.get('rule_description')
            commission_type_id = request.form.get('commission_type_id')
            rep_id = request.form.get('rep_id')
            item_category = request.form.get('item_category')
            fixed_amount = request.form.get('fixed_amount')
            percentage_rate = request.form.get('percentage_rate')
            quantity_rate = request.form.get('quantity_rate')
            quantity_unit = request.form.get('quantity_unit')
            min_order_value = request.form.get('min_order_value')
            max_commission = request.form.get('max_commission')
            effective_from = request.form.get('effective_from')
            effective_to = request.form.get('effective_to')
            priority_order = request.form.get('priority_order', 1)
            is_active = 1 if request.form.get('is_active') else 0

            if not rule_name or not commission_type_id:
                flash('اسم القاعدة ونوع العمولة مطلوبان', 'error')
                return redirect(url_for('purchase_commissions.edit_commission_rule', rule_id=rule_id))

            # معالجة التواريخ
            eff_from = effective_from if effective_from else None
            eff_to = effective_to if effective_to else None

            update_query = """
            UPDATE commission_rules SET
                rule_name = :1, rule_description = :2, commission_type_id = :3,
                rep_id = :4, item_category = :5, fixed_amount = :6,
                percentage_rate = :7, quantity_rate = :8, quantity_unit = :9,
                min_order_value = :10, max_commission = :11,
                effective_from = TO_DATE(:12, 'YYYY-MM-DD'),
                effective_to = TO_DATE(:13, 'YYYY-MM-DD'),
                priority_order = :14, is_active = :15,
                updated_at = SYSDATE, updated_by = :16
            WHERE id = :17
            """

            db_manager.execute_update(update_query, (
                rule_name, rule_description, int(commission_type_id),
                int(rep_id) if rep_id else None, item_category,
                float(fixed_amount) if fixed_amount else None,
                float(percentage_rate) if percentage_rate else None,
                float(quantity_rate) if quantity_rate else None,
                quantity_unit,
                float(min_order_value) if min_order_value else None,
                float(max_commission) if max_commission else None,
                eff_from, eff_to,
                int(priority_order), is_active, current_user.id, rule_id
            ))

            flash(f'تم تحديث قاعدة العمولة {rule_name} بنجاح', 'success')
            logger.info(f"تم تحديث قاعدة عمولة: {rule_name}")

            return redirect(url_for('purchase_commissions.commission_rules'))

    except Exception as e:
        logger.error(f"خطأ في تعديل قاعدة العمولة: {e}")
        flash('حدث خطأ أثناء تعديل قاعدة العمولة', 'error')
        return redirect(url_for('purchase_commissions.commission_rules'))


@purchase_commissions_bp.route('/commission-rules/delete/<int:rule_id>', methods=['POST'])
@login_required
def delete_commission_rule(rule_id):
    """حذف قاعدة عمولة"""
    try:
        db_manager = DatabaseManager()

        # التحقق من وجود حسابات عمولات مرتبطة
        calculations_check = db_manager.execute_query(
            "SELECT COUNT(*) FROM commission_calculations WHERE rule_id = :1", (rule_id,)
        )

        if calculations_check[0][0] > 0:
            flash('لا يمكن حذف قاعدة العمولة لوجود حسابات مرتبطة بها', 'error')
            return redirect(url_for('purchase_commissions.commission_rules'))

        # حذف القاعدة
        db_manager.execute_update(
            "DELETE FROM commission_rules WHERE id = :1", (rule_id,)
        )

        flash('تم حذف قاعدة العمولة بنجاح', 'success')
        logger.info(f"تم حذف قاعدة عمولة: {rule_id}")

    except Exception as e:
        logger.error(f"خطأ في حذف قاعدة العمولة: {e}")
        flash('حدث خطأ أثناء حذف قاعدة العمولة', 'error')

    return redirect(url_for('purchase_commissions.commission_rules'))


@purchase_commissions_bp.route('/calculations/calculate', methods=['POST'])
@login_required
def calculate_commission():
    """حساب عمولة جديدة"""
    try:
        db_manager = DatabaseManager()

        # جلب البيانات من النموذج
        rep_id = request.form.get('rep_id')
        rule_id = request.form.get('rule_id')
        order_value = request.form.get('order_value')
        total_quantity = request.form.get('total_quantity', 0)
        quantity_unit = request.form.get('quantity_unit')
        purchase_order_id = request.form.get('purchase_order_id')
        notes = request.form.get('notes')

        # التحقق من البيانات المطلوبة
        if not rep_id or not rule_id or not order_value:
            flash('المندوب وقاعدة العمولة وقيمة الطلب مطلوبة', 'error')
            return redirect(url_for('purchase_commissions.calculations'))

        # جلب تفاصيل القاعدة
        rule_query = """
        SELECT fixed_amount, percentage_rate, quantity_rate, min_order_value, max_commission
        FROM commission_rules WHERE id = :1
        """
        rule_data = db_manager.execute_query(rule_query, (int(rule_id),))

        if not rule_data:
            flash('قاعدة العمولة غير موجودة', 'error')
            return redirect(url_for('purchase_commissions.calculations'))

        rule = rule_data[0]
        fixed_amount = rule[0] or 0
        percentage_rate = rule[1] or 0
        quantity_rate = rule[2] or 0
        min_order_value = rule[3] or 0
        max_commission = rule[4]

        order_val = float(order_value)
        total_qty = float(total_quantity) if total_quantity else 0

        # التحقق من الحد الأدنى
        if order_val < min_order_value:
            flash(f'قيمة الطلب أقل من الحد الأدنى المطلوب: {min_order_value:,.2f} ريال', 'error')
            return redirect(url_for('purchase_commissions.calculations'))

        # حساب العمولة
        commission_amount = 0
        commission_rate_used = 0
        calculation_method = ''

        if fixed_amount > 0:
            commission_amount = fixed_amount
            calculation_method = 'FIXED'
        elif percentage_rate > 0:
            commission_amount = order_val * (percentage_rate / 100)
            commission_rate_used = percentage_rate
            calculation_method = 'PERCENTAGE'
        elif quantity_rate > 0 and total_qty > 0:
            commission_amount = total_qty * quantity_rate
            calculation_method = 'QUANTITY'

        # تطبيق الحد الأقصى
        if max_commission and commission_amount > max_commission:
            commission_amount = max_commission

        # إدراج الحساب
        insert_query = """
        INSERT INTO commission_calculations (
            id, calculation_date, rep_id, rule_id, purchase_order_id, order_value,
            commission_rate, commission_amount, calculation_method, total_quantity,
            quantity_unit, notes, status, created_by
        ) VALUES (
            commission_calculations_seq.NEXTVAL, SYSDATE, :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, 'CALCULATED', :11
        )
        """

        db_manager.execute_update(insert_query, (
            int(rep_id), int(rule_id),
            int(purchase_order_id) if purchase_order_id else None,
            order_val, commission_rate_used, commission_amount, calculation_method,
            total_qty, quantity_unit, notes, current_user.id
        ))

        flash(f'تم حساب العمولة بنجاح: {commission_amount:,.2f} ريال', 'success')
        logger.info(f"تم حساب عمولة جديدة: {commission_amount:,.2f} ريال للمندوب {rep_id}")

    except Exception as e:
        logger.error(f"خطأ في حساب العمولة: {e}")
        flash('حدث خطأ أثناء حساب العمولة', 'error')

    return redirect(url_for('purchase_commissions.calculations'))


@purchase_commissions_bp.route('/calculations')
@login_required
def calculations():
    """صفحة حساب ومراجعة العمولات"""
    try:
        db_manager = DatabaseManager()

        # جلب العمولات المحسوبة
        calculations = get_commission_calculations(db_manager)

        # جلب المندوبين وقواعد العمولات للنماذج
        representatives = get_all_representatives(db_manager)
        commission_rules = get_all_commission_rules(db_manager)

        return render_template('purchase_commissions/calculations.html',
                             calculations=calculations,
                             representatives=representatives,
                             commission_rules=commission_rules)

    except Exception as e:
        logger.error(f"خطأ في تحميل حسابات العمولات: {e}")
        flash(f'خطأ في تحميل البيانات: {str(e)}', 'error')
        return render_template('purchase_commissions/calculations.html',
                             calculations=[],
                             representatives=[],
                             commission_rules=[])


@purchase_commissions_bp.route('/payments')
@login_required
def payments():
    """صفحة مدفوعات العمولات"""
    try:
        db_manager = DatabaseManager()
        
        # جلب مدفوعات العمولات
        payments = get_commission_payments(db_manager)
        
        return render_template('purchase_commissions/payments.html', payments=payments)
        
    except Exception as e:
        logger.error(f"خطأ في تحميل مدفوعات العمولات: {e}")
        flash(f'خطأ في تحميل البيانات: {str(e)}', 'error')
        return render_template('purchase_commissions/payments.html', payments=[])


@purchase_commissions_bp.route('/reports')
@login_required
def reports():
    """صفحة التقارير والتحليلات"""
    try:
        db_manager = DatabaseManager()

        # جلب بيانات التقارير
        report_data = get_commission_reports(db_manager)

        # جلب المندوبين لفلترة التقارير
        representatives = get_all_representatives(db_manager)

        # جلب أنواع العمولات
        commission_types = get_commission_types()

        return render_template('purchase_commissions/reports.html',
                             report_data=report_data,
                             representatives=representatives,
                             commission_types=commission_types)

    except Exception as e:
        logger.error(f"خطأ في تحميل التقارير: {e}")
        flash(f'خطأ في تحميل البيانات: {str(e)}', 'error')
        return render_template('purchase_commissions/reports.html',
                             report_data={}, representatives=[], commission_types=[])


@purchase_commissions_bp.route('/reports/generate', methods=['POST'])
@login_required
def generate_report():
    """إنشاء تقرير مخصص"""
    try:
        db_manager = DatabaseManager()

        # جلب معايير التقرير
        report_type = request.form.get('report_type')
        date_from = request.form.get('date_from')
        date_to = request.form.get('date_to')
        rep_id = request.form.get('rep_id')
        commission_type_id = request.form.get('commission_type_id')
        status = request.form.get('status')

        # إنشاء التقرير حسب النوع
        if report_type == 'summary':
            report_data = generate_summary_report(db_manager, date_from, date_to, rep_id, status)
        elif report_type == 'detailed':
            report_data = generate_detailed_report(db_manager, date_from, date_to, rep_id, commission_type_id, status)
        elif report_type == 'performance':
            report_data = generate_performance_report(db_manager, date_from, date_to, rep_id)
        elif report_type == 'comparison':
            report_data = generate_comparison_report(db_manager, date_from, date_to)
        else:
            flash('نوع التقرير غير صحيح', 'error')
            return redirect(url_for('purchase_commissions.reports'))

        # إرجاع التقرير
        return render_template('purchase_commissions/report_result.html',
                             report_data=report_data,
                             report_type=report_type,
                             filters={
                                 'date_from': date_from,
                                 'date_to': date_to,
                                 'rep_id': rep_id,
                                 'commission_type_id': commission_type_id,
                                 'status': status
                             })

    except Exception as e:
        logger.error(f"خطأ في إنشاء التقرير: {e}")
        flash('حدث خطأ أثناء إنشاء التقرير', 'error')
        return redirect(url_for('purchase_commissions.reports'))


@purchase_commissions_bp.route('/reports/export/<report_type>')
@login_required
def export_report(report_type):
    """تصدير التقرير إلى Excel"""
    try:
        db_manager = DatabaseManager()

        # جلب معايير التقرير من الجلسة أو المعاملات
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        rep_id = request.args.get('rep_id')
        commission_type_id = request.args.get('commission_type_id')
        status = request.args.get('status')

        # إنشاء التقرير
        if report_type == 'summary':
            report_data = generate_summary_report(db_manager, date_from, date_to, rep_id, status)
        elif report_type == 'detailed':
            report_data = generate_detailed_report(db_manager, date_from, date_to, rep_id, commission_type_id, status)
        elif report_type == 'performance':
            report_data = generate_performance_report(db_manager, date_from, date_to, rep_id)
        else:
            flash('نوع التقرير غير صحيح', 'error')
            return redirect(url_for('purchase_commissions.reports'))

        # تصدير إلى Excel (سيتم تطويره لاحقاً)
        flash('سيتم تطوير وظيفة التصدير قريباً', 'info')
        return redirect(url_for('purchase_commissions.reports'))

    except Exception as e:
        logger.error(f"خطأ في تصدير التقرير: {e}")
        flash('حدث خطأ أثناء تصدير التقرير', 'error')
        return redirect(url_for('purchase_commissions.reports'))


@purchase_commissions_bp.route('/calculations/approve/<int:calc_id>', methods=['POST'])
@login_required
def approve_calculation(calc_id):
    """اعتماد حساب عمولة"""
    try:
        db_manager = DatabaseManager()

        # التحقق من وجود الحساب وحالته
        calc_query = """
        SELECT id, status, commission_amount, rep_id, rule_id
        FROM commission_calculations WHERE id = :1
        """
        calc_data = db_manager.execute_query(calc_query, (calc_id,))

        if not calc_data:
            flash('حساب العمولة غير موجود', 'error')
            return redirect(url_for('purchase_commissions.calculations'))

        current_status = calc_data[0][1]
        if current_status != 'calculated':
            flash('يمكن اعتماد الحسابات في حالة "محسوبة" فقط', 'error')
            return redirect(url_for('purchase_commissions.calculations'))

        # تحديث الحالة إلى معتمدة
        update_query = """
        UPDATE commission_calculations SET
            status = 'approved',
            approved_by = :1,
            approved_at = SYSDATE,
            updated_at = SYSDATE,
            updated_by = :2
        WHERE id = :3
        """

        db_manager.execute_update(update_query, (current_user.id, current_user.id, calc_id))

        flash('تم اعتماد حساب العمولة بنجاح', 'success')
        logger.info(f"تم اعتماد حساب عمولة: {calc_id} بواسطة {current_user.id}")

    except Exception as e:
        logger.error(f"خطأ في اعتماد حساب العمولة: {e}")
        flash('حدث خطأ أثناء اعتماد حساب العمولة', 'error')

    return redirect(url_for('purchase_commissions.calculations'))


@purchase_commissions_bp.route('/calculations/reject/<int:calc_id>', methods=['POST'])
@login_required
def reject_calculation(calc_id):
    """رفض حساب عمولة"""
    try:
        db_manager = DatabaseManager()

        rejection_reason = request.form.get('rejection_reason', 'لم يتم تحديد سبب الرفض')

        # التحقق من وجود الحساب وحالته
        calc_query = """
        SELECT id, status FROM commission_calculations WHERE id = :1
        """
        calc_data = db_manager.execute_query(calc_query, (calc_id,))

        if not calc_data:
            flash('حساب العمولة غير موجود', 'error')
            return redirect(url_for('purchase_commissions.calculations'))

        current_status = calc_data[0][1]
        if current_status not in ['calculated', 'approved']:
            flash('يمكن رفض الحسابات في حالة "محسوبة" أو "معتمدة" فقط', 'error')
            return redirect(url_for('purchase_commissions.calculations'))

        # تحديث الحالة إلى مرفوضة
        update_query = """
        UPDATE commission_calculations SET
            status = 'rejected',
            rejection_reason = :1,
            rejected_by = :2,
            rejected_at = SYSDATE,
            updated_at = SYSDATE,
            updated_by = :3
        WHERE id = :4
        """

        db_manager.execute_update(update_query, (rejection_reason, current_user.id, current_user.id, calc_id))

        flash('تم رفض حساب العمولة', 'warning')
        logger.info(f"تم رفض حساب عمولة: {calc_id} بواسطة {current_user.id}")

    except Exception as e:
        logger.error(f"خطأ في رفض حساب العمولة: {e}")
        flash('حدث خطأ أثناء رفض حساب العمولة', 'error')

    return redirect(url_for('purchase_commissions.calculations'))


@purchase_commissions_bp.route('/calculations/pay/<int:calc_id>', methods=['POST'])
@login_required
def mark_as_paid(calc_id):
    """تسجيل دفع العمولة"""
    try:
        db_manager = DatabaseManager()

        payment_notes = request.form.get('payment_notes', '')

        # التحقق من وجود الحساب وحالته
        calc_query = """
        SELECT id, status, commission_amount FROM commission_calculations WHERE id = :1
        """
        calc_data = db_manager.execute_query(calc_query, (calc_id,))

        if not calc_data:
            flash('حساب العمولة غير موجود', 'error')
            return redirect(url_for('purchase_commissions.calculations'))

        current_status = calc_data[0][1]
        if current_status != 'approved':
            flash('يمكن دفع العمولات المعتمدة فقط', 'error')
            return redirect(url_for('purchase_commissions.calculations'))

        # تحديث الحالة إلى مدفوعة
        update_query = """
        UPDATE commission_calculations SET
            status = 'paid',
            payment_notes = :1,
            paid_by = :2,
            paid_at = SYSDATE,
            updated_at = SYSDATE,
            updated_by = :3
        WHERE id = :4
        """

        db_manager.execute_update(update_query, (payment_notes, current_user.id, current_user.id, calc_id))

        flash('تم تسجيل دفع العمولة بنجاح', 'success')
        logger.info(f"تم تسجيل دفع عمولة: {calc_id} بواسطة {current_user.id}")

    except Exception as e:
        logger.error(f"خطأ في تسجيل دفع العمولة: {e}")
        flash('حدث خطأ أثناء تسجيل دفع العمولة', 'error')

    return redirect(url_for('purchase_commissions.calculations'))


# =====================================================
# دوال مساعدة لجلب البيانات
# Helper Functions for Data Retrieval
# =====================================================

def get_dashboard_stats(db_manager):
    """جلب إحصائيات لوحة المعلومات"""
    try:
        stats = {}

        # التحقق من وجود الجداول أولاً
        try:
            # إحصائيات المندوبين
            rep_query = "SELECT COUNT(*) as total, SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active FROM purchase_representatives"
            rep_result = db_manager.fetch_one(rep_query)
            stats['representatives'] = {
                'total': rep_result[0] if rep_result else 0,
                'active': rep_result[1] if rep_result else 0
            }
        except:
            # الجداول غير موجودة - استخدام قيم افتراضية
            stats['representatives'] = {
                'total': 0,
                'active': 0
            }

        try:
            # إحصائيات العمولات هذا الشهر
            comm_query = """
                SELECT
                    COUNT(*) as total_calculations,
                    SUM(commission_amount) as total_amount,
                    SUM(CASE WHEN status = 'CALCULATED' THEN 1 ELSE 0 END) as pending_approval,
                    SUM(CASE WHEN status = 'APPROVED' THEN 1 ELSE 0 END) as approved,
                    SUM(CASE WHEN status = 'PAID' THEN 1 ELSE 0 END) as paid
                FROM commission_calculations
                WHERE EXTRACT(MONTH FROM calculation_date) = EXTRACT(MONTH FROM SYSDATE)
                AND EXTRACT(YEAR FROM calculation_date) = EXTRACT(YEAR FROM SYSDATE)
            """
            comm_result = db_manager.fetch_one(comm_query)
            if comm_result:
                stats['commissions'] = {
                    'total_calculations': comm_result[0] or 0,
                    'total_amount': comm_result[1] or 0,
                    'pending_approval': comm_result[2] or 0,
                    'approved': comm_result[3] or 0,
                    'paid': comm_result[4] or 0
                }
            else:
                stats['commissions'] = {
                    'total_calculations': 0,
                    'total_amount': 0,
                    'pending_approval': 0,
                    'approved': 0,
                    'paid': 0
                }
        except:
            # الجداول غير موجودة - استخدام قيم افتراضية
            stats['commissions'] = {
                'total_calculations': 0,
                'total_amount': 0,
                'pending_approval': 0,
                'approved': 0,
                'paid': 0
            }

        return stats

    except Exception as e:
        logger.error(f"خطأ في جلب إحصائيات لوحة المعلومات: {e}")
        return {
            'representatives': {'total': 0, 'active': 0},
            'commissions': {
                'total_calculations': 0,
                'total_amount': 0,
                'pending_approval': 0,
                'approved': 0,
                'paid': 0
            }
        }


def get_all_representatives(db_manager):
    """جلب جميع المندوبين"""
    try:
        query = """
            SELECT id, rep_code, rep_name, rep_name_en, specialization,
                   phone, mobile, email, target_monthly_orders,
                   target_monthly_quantity, target_monthly_value,
                   is_active, commission_eligible, notes
            FROM purchase_representatives
            ORDER BY rep_code
        """
        reps_data = db_manager.execute_query(query)

        # تحويل البيانات إلى قاموس
        representatives = []
        if reps_data:
            for row in reps_data:
                rep = {
                    'id': row[0],
                    'rep_code': row[1],
                    'rep_name': row[2],
                    'rep_name_en': row[3],
                    'specialization': row[4],
                    'phone': row[5],
                    'mobile': row[6],
                    'email': row[7],
                    'target_monthly_orders': row[8],
                    'target_monthly_quantity': row[9],
                    'target_monthly_value': row[10],
                    'is_active': row[11],
                    'commission_eligible': row[12],
                    'notes': row[13]
                }
                representatives.append(rep)

        return representatives

    except Exception as e:
        logger.error(f"خطأ في جلب المندوبين: {e}")
        return []


def get_all_commission_types(db_manager):
    """جلب جميع أنواع العمولات"""
    try:
        query = """
            SELECT id, type_code, type_name, type_name_en, calculation_method,
                   description, is_active, display_order, supports_combination
            FROM commission_types
            ORDER BY display_order, type_name
        """
        types_data = db_manager.execute_query(query)

        # تحويل البيانات إلى قاموس
        commission_types = []
        if types_data:
            for row in types_data:
                type_obj = {
                    'id': row[0],
                    'type_code': row[1],
                    'type_name': row[2],
                    'type_name_en': row[3],
                    'calculation_method': row[4],
                    'description': row[5],
                    'is_active': row[6],
                    'display_order': row[7],
                    'supports_combination': row[8]
                }
                commission_types.append(type_obj)

        return commission_types

    except Exception as e:
        logger.error(f"خطأ في جلب أنواع العمولات: {e}")
        return []


def get_all_currencies(db_manager):
    """جلب جميع العملات"""
    try:
        query = """
            SELECT id, code, name_ar, symbol, is_base_currency, is_active
            FROM currencies
            WHERE is_active = 1
            ORDER BY is_base_currency DESC, name_ar
        """
        currencies_data = db_manager.execute_query(query)
        
        # تحويل البيانات إلى قاموس
        currencies = []
        if currencies_data:
            for row in currencies_data:
                currency = {
                    'id': row[0],
                    'code': row[1],
                    'name_ar': row[2],
                    'symbol': row[3],
                    'is_base_currency': row[4],
                    'is_active': row[5]
                }
                currencies.append(currency)
        
        return currencies
        
    except Exception as e:
        logger.error(f"خطأ في جلب العملات: {e}")
        return []


def get_all_commission_rules(db_manager):
    """جلب جميع قواعد العمولات"""
    try:
        query = """
            SELECT cr.id, cr.rule_name, cr.rule_description, ct.type_name,
                   pr.rep_name, pr.rep_code, cr.item_category,
                   cr.fixed_amount, cr.percentage_rate, cr.quantity_rate, cr.quantity_unit,
                   cr.min_order_value, cr.max_commission, cr.effective_from, cr.effective_to,
                   cr.priority_order, cr.is_active
            FROM commission_rules cr
            LEFT JOIN commission_types ct ON cr.commission_type_id = ct.id
            LEFT JOIN purchase_representatives pr ON cr.rep_id = pr.id
            ORDER BY cr.priority_order, cr.rule_name
        """
        rules_data = db_manager.execute_query(query)

        # تحويل البيانات إلى قاموس
        commission_rules = []
        if rules_data:
            for row in rules_data:
                # التعامل مع LOB objects
                rule_description = row[2]
                if hasattr(rule_description, 'read'):
                    rule_description = rule_description.read()

                rule = {
                    'id': row[0],
                    'rule_name': row[1],
                    'rule_description': rule_description,
                    'type_name': row[3],
                    'rep_name': row[4],
                    'rep_code': row[5],
                    'item_category': row[6],
                    'fixed_amount': row[7] or 0,
                    'percentage_rate': row[8] or 0,
                    'quantity_rate': row[9] or 0,
                    'quantity_unit': row[10],
                    'min_order_value': row[11] or 0,
                    'max_commission': row[12],
                    'effective_from': row[13],
                    'effective_to': row[14],
                    'priority_order': row[15] or 1,
                    'is_active': row[16] or 0
                }
                commission_rules.append(rule)

        return commission_rules

    except Exception as e:
        logger.error(f"خطأ في جلب قواعد العمولات: {e}")
        return []


def get_commission_calculations(db_manager):
    """جلب حسابات العمولات"""
    try:
        query = """
            SELECT cc.id, cc.calculation_date, pr.rep_name, pr.rep_code,
                   cr.rule_name, cc.purchase_order_id, cc.order_value,
                   cc.commission_rate, cc.commission_amount, cc.total_quantity,
                   cc.quantity_unit, cc.status, cc.approved_at, cc.paid_at
            FROM commission_calculations cc
            JOIN purchase_representatives pr ON cc.rep_id = pr.id
            JOIN commission_rules cr ON cc.rule_id = cr.id
            ORDER BY cc.calculation_date DESC
        """
        calculations_data = db_manager.execute_query(query)

        # تحويل البيانات إلى قاموس
        calculations = []
        if calculations_data:
            for row in calculations_data:
                calc = {
                    'id': row[0],
                    'calculation_date': row[1],
                    'rep_name': row[2],
                    'rep_code': row[3],
                    'rule_name': row[4],
                    'purchase_order_id': row[5],
                    'order_value': row[6] or 0,
                    'commission_rate': row[7] or 0,
                    'commission_amount': row[8] or 0,
                    'total_quantity': row[9] or 0,
                    'quantity_unit': row[10],
                    'status': row[11],
                    'approved_at': row[12],
                    'paid_at': row[13]
                }
                calculations.append(calc)

        return calculations

    except Exception as e:
        logger.error(f"خطأ في جلب حسابات العمولات: {e}")
        return []


def get_commission_payments(db_manager):
    """جلب مدفوعات العمولات"""
    try:
        query = """
            SELECT cp.id, cp.payment_date, pr.rep_name, cp.total_amount,
                   cp.currency, cp.payment_method, cp.status,
                   cp.transfer_request_id
            FROM commission_payments cp
            JOIN purchase_representatives pr ON cp.rep_id = pr.id
            ORDER BY cp.payment_date DESC
        """
        return db_manager.fetch_all(query)
        
    except Exception as e:
        logger.error(f"خطأ في جلب مدفوعات العمولات: {e}")
        return []


def get_commission_reports(db_manager):
    """جلب بيانات التقارير الأساسية"""
    try:
        reports = {}

        # إحصائيات عامة
        stats_query = """
        SELECT
            COUNT(*) as total_calculations,
            SUM(CASE WHEN status = 'calculated' THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_count,
            SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paid_count,
            SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_count,
            SUM(commission_amount) as total_amount,
            SUM(CASE WHEN status = 'paid' THEN commission_amount ELSE 0 END) as paid_amount
        FROM commission_calculations
        WHERE calculation_date >= TRUNC(SYSDATE, 'MM')
        """

        stats_data = db_manager.execute_query(stats_query)
        if stats_data:
            row = stats_data[0]
            reports['monthly_stats'] = {
                'total_calculations': row[0] or 0,
                'pending_count': row[1] or 0,
                'approved_count': row[2] or 0,
                'paid_count': row[3] or 0,
                'rejected_count': row[4] or 0,
                'total_amount': row[5] or 0,
                'paid_amount': row[6] or 0
            }

        # أفضل المندوبين هذا الشهر
        top_reps_query = """
        SELECT pr.rep_name, pr.rep_code,
               COUNT(cc.id) as calculations_count,
               SUM(cc.commission_amount) as total_commission
        FROM commission_calculations cc
        JOIN purchase_representatives pr ON cc.rep_id = pr.id
        WHERE cc.calculation_date >= TRUNC(SYSDATE, 'MM')
        AND cc.status IN ('approved', 'paid')
        GROUP BY pr.rep_name, pr.rep_code
        ORDER BY SUM(cc.commission_amount) DESC
        FETCH FIRST 5 ROWS ONLY
        """

        top_reps_data = db_manager.execute_query(top_reps_query)
        reports['top_representatives'] = []
        if top_reps_data:
            for row in top_reps_data:
                reports['top_representatives'].append({
                    'rep_name': row[0],
                    'rep_code': row[1],
                    'calculations_count': row[2] or 0,
                    'total_commission': row[3] or 0
                })

        return reports

    except Exception as e:
        logger.error(f"خطأ في جلب بيانات التقارير: {e}")
        return {}


def generate_summary_report(db_manager, date_from=None, date_to=None, rep_id=None, status=None):
    """إنشاء تقرير ملخص العمولات"""
    try:
        # بناء الاستعلام الأساسي
        query = """
        SELECT pr.rep_name, pr.rep_code,
               COUNT(cc.id) as calculations_count,
               SUM(cc.commission_amount) as total_commission,
               SUM(CASE WHEN cc.status = 'calculated' THEN cc.commission_amount ELSE 0 END) as pending_amount,
               SUM(CASE WHEN cc.status = 'approved' THEN cc.commission_amount ELSE 0 END) as approved_amount,
               SUM(CASE WHEN cc.status = 'paid' THEN cc.commission_amount ELSE 0 END) as paid_amount
        FROM commission_calculations cc
        JOIN purchase_representatives pr ON cc.rep_id = pr.id
        WHERE 1=1
        """

        params = []

        # إضافة فلاتر التاريخ
        if date_from:
            query += " AND cc.calculation_date >= TO_DATE(:1, 'YYYY-MM-DD')"
            params.append(date_from)
        if date_to:
            query += f" AND cc.calculation_date <= TO_DATE(:{len(params)+1}, 'YYYY-MM-DD')"
            params.append(date_to)

        # إضافة فلتر المندوب
        if rep_id:
            query += f" AND cc.rep_id = :{len(params)+1}"
            params.append(int(rep_id))

        # إضافة فلتر الحالة
        if status:
            query += f" AND cc.status = :{len(params)+1}"
            params.append(status)

        query += """
        GROUP BY pr.rep_name, pr.rep_code
        ORDER BY SUM(cc.commission_amount) DESC
        """

        data = db_manager.execute_query(query, params)

        summary_data = []
        total_calculations = 0
        total_commission = 0
        total_pending = 0
        total_approved = 0
        total_paid = 0

        if data:
            for row in data:
                item = {
                    'rep_name': row[0],
                    'rep_code': row[1],
                    'calculations_count': row[2] or 0,
                    'total_commission': row[3] or 0,
                    'pending_amount': row[4] or 0,
                    'approved_amount': row[5] or 0,
                    'paid_amount': row[6] or 0
                }
                summary_data.append(item)

                total_calculations += item['calculations_count']
                total_commission += item['total_commission']
                total_pending += item['pending_amount']
                total_approved += item['approved_amount']
                total_paid += item['paid_amount']

        return {
            'data': summary_data,
            'totals': {
                'total_calculations': total_calculations,
                'total_commission': total_commission,
                'total_pending': total_pending,
                'total_approved': total_approved,
                'total_paid': total_paid
            }
        }

    except Exception as e:
        logger.error(f"خطأ في إنشاء تقرير الملخص: {e}")
        return {'data': [], 'totals': {}}


def generate_detailed_report(db_manager, date_from=None, date_to=None, rep_id=None, commission_type_id=None, status=None):
    """إنشاء تقرير تفصيلي للعمولات"""
    try:
        query = """
        SELECT cc.id, cc.calculation_date, pr.rep_name, pr.rep_code,
               cr.rule_name, ct.type_name, cc.order_value,
               cc.commission_rate, cc.commission_amount, cc.total_quantity,
               cc.quantity_unit, cc.status, cc.purchase_order_id,
               cc.approved_at, cc.paid_at
        FROM commission_calculations cc
        JOIN purchase_representatives pr ON cc.rep_id = pr.id
        JOIN commission_rules cr ON cc.rule_id = cr.id
        JOIN commission_types ct ON cr.commission_type_id = ct.id
        WHERE 1=1
        """

        params = []

        # إضافة الفلاتر
        if date_from:
            query += " AND cc.calculation_date >= TO_DATE(:1, 'YYYY-MM-DD')"
            params.append(date_from)
        if date_to:
            query += f" AND cc.calculation_date <= TO_DATE(:{len(params)+1}, 'YYYY-MM-DD')"
            params.append(date_to)
        if rep_id:
            query += f" AND cc.rep_id = :{len(params)+1}"
            params.append(int(rep_id))
        if commission_type_id:
            query += f" AND cr.commission_type_id = :{len(params)+1}"
            params.append(int(commission_type_id))
        if status:
            query += f" AND cc.status = :{len(params)+1}"
            params.append(status)

        query += " ORDER BY cc.calculation_date DESC, pr.rep_name"

        data = db_manager.execute_query(query, params)

        detailed_data = []
        if data:
            for row in data:
                detailed_data.append({
                    'id': row[0],
                    'calculation_date': row[1],
                    'rep_name': row[2],
                    'rep_code': row[3],
                    'rule_name': row[4],
                    'type_name': row[5],
                    'order_value': row[6] or 0,
                    'commission_rate': row[7] or 0,
                    'commission_amount': row[8] or 0,
                    'total_quantity': row[9] or 0,
                    'quantity_unit': row[10],
                    'status': row[11],
                    'purchase_order_id': row[12],
                    'approved_at': row[13],
                    'paid_at': row[14]
                })

        return {'data': detailed_data}

    except Exception as e:
        logger.error(f"خطأ في إنشاء التقرير التفصيلي: {e}")
        return {'data': []}


def generate_performance_report(db_manager, date_from=None, date_to=None, rep_id=None):
    """إنشاء تقرير أداء المندوبين"""
    try:
        query = """
        SELECT pr.rep_name, pr.rep_code, pr.specialization,
               COUNT(cc.id) as total_calculations,
               SUM(cc.commission_amount) as total_commission,
               AVG(cc.commission_amount) as avg_commission,
               SUM(cc.order_value) as total_orders_value,
               AVG(cc.order_value) as avg_order_value,
               SUM(CASE WHEN cc.status = 'paid' THEN 1 ELSE 0 END) as paid_count,
               SUM(CASE WHEN cc.status = 'rejected' THEN 1 ELSE 0 END) as rejected_count,
               ROUND(SUM(CASE WHEN cc.status = 'paid' THEN 1 ELSE 0 END) * 100.0 / COUNT(cc.id), 2) as success_rate
        FROM commission_calculations cc
        JOIN purchase_representatives pr ON cc.rep_id = pr.id
        WHERE 1=1
        """

        params = []

        if date_from:
            query += " AND cc.calculation_date >= TO_DATE(:1, 'YYYY-MM-DD')"
            params.append(date_from)
        if date_to:
            query += f" AND cc.calculation_date <= TO_DATE(:{len(params)+1}, 'YYYY-MM-DD')"
            params.append(date_to)
        if rep_id:
            query += f" AND cc.rep_id = :{len(params)+1}"
            params.append(int(rep_id))

        query += """
        GROUP BY pr.rep_name, pr.rep_code, pr.specialization
        ORDER BY SUM(cc.commission_amount) DESC
        """

        data = db_manager.execute_query(query, params)

        performance_data = []
        if data:
            for row in data:
                performance_data.append({
                    'rep_name': row[0],
                    'rep_code': row[1],
                    'specialization': row[2],
                    'total_calculations': row[3] or 0,
                    'total_commission': row[4] or 0,
                    'avg_commission': row[5] or 0,
                    'total_orders_value': row[6] or 0,
                    'avg_order_value': row[7] or 0,
                    'paid_count': row[8] or 0,
                    'rejected_count': row[9] or 0,
                    'success_rate': row[10] or 0
                })

        return {'data': performance_data}

    except Exception as e:
        logger.error(f"خطأ في إنشاء تقرير الأداء: {e}")
        return {'data': []}


def generate_comparison_report(db_manager, date_from=None, date_to=None):
    """إنشاء تقرير مقارنة الفترات"""
    try:
        # تقرير مقارنة شهري
        query = """
        SELECT TO_CHAR(cc.calculation_date, 'YYYY-MM') as month_year,
               COUNT(cc.id) as calculations_count,
               SUM(cc.commission_amount) as total_commission,
               AVG(cc.commission_amount) as avg_commission,
               SUM(CASE WHEN cc.status = 'paid' THEN cc.commission_amount ELSE 0 END) as paid_amount
        FROM commission_calculations cc
        WHERE 1=1
        """

        params = []

        if date_from:
            query += " AND cc.calculation_date >= TO_DATE(:1, 'YYYY-MM-DD')"
            params.append(date_from)
        if date_to:
            query += f" AND cc.calculation_date <= TO_DATE(:{len(params)+1}, 'YYYY-MM-DD')"
            params.append(date_to)

        query += """
        GROUP BY TO_CHAR(cc.calculation_date, 'YYYY-MM')
        ORDER BY TO_CHAR(cc.calculation_date, 'YYYY-MM') DESC
        """

        data = db_manager.execute_query(query, params)

        comparison_data = []
        if data:
            for row in data:
                comparison_data.append({
                    'month_year': row[0],
                    'calculations_count': row[1] or 0,
                    'total_commission': row[2] or 0,
                    'avg_commission': row[3] or 0,
                    'paid_amount': row[4] or 0
                })

        return {'data': comparison_data}

    except Exception as e:
        logger.error(f"خطأ في إنشاء تقرير المقارنة: {e}")
        return {'data': []}


@purchase_commissions_bp.route('/purchase-orders')
def purchase_orders():
    """صفحة إدارة ربط أوامر الشراء"""
    logger.info("🔗 تم طلب صفحة ربط أوامر الشراء")
    try:
        db_manager = DatabaseManager()
        logger.info("✅ تم إنشاء مدير قاعدة البيانات")

        # جلب أوامر الشراء المربوطة مع المندوبين
        orders_query = """
        SELECT por.purchase_order_id, pr.rep_name, pr.rep_code,
               por.commission_percentage, por.assignment_date,
               ct.order_total_value, ct.commission_amount, ct.status,
               por.notes, ct.calculation_date
        FROM purchase_order_representatives por
        JOIN purchase_representatives pr ON por.rep_id = pr.id
        LEFT JOIN commission_tracking ct ON por.purchase_order_id = ct.purchase_order_id
                                         AND por.rep_id = ct.rep_id
        ORDER BY por.assignment_date DESC
        """

        orders_data = db_manager.execute_query(orders_query)
        logger.info(f"✅ تم جلب {len(orders_data) if orders_data else 0} أمر شراء مربوط")

        # إحصائيات سريعة
        stats_query = """
        SELECT
            COUNT(DISTINCT por.purchase_order_id) as total_orders,
            COUNT(por.id) as total_assignments,
            SUM(CASE WHEN ct.status = 'calculated' THEN 1 ELSE 0 END) as calculated_count,
            SUM(CASE WHEN ct.status = 'approved' THEN 1 ELSE 0 END) as approved_count,
            SUM(CASE WHEN ct.status = 'paid' THEN 1 ELSE 0 END) as paid_count,
            SUM(ct.commission_amount) as total_commission_amount
        FROM purchase_order_representatives por
        LEFT JOIN commission_tracking ct ON por.purchase_order_id = ct.purchase_order_id
                                         AND por.rep_id = ct.rep_id
        """

        stats_data = db_manager.execute_query(stats_query)
        stats = {}
        if stats_data:
            row = stats_data[0]
            stats = {
                'total_orders': row[0] or 0,
                'total_assignments': row[1] or 0,
                'calculated_count': row[2] or 0,
                'approved_count': row[3] or 0,
                'paid_count': row[4] or 0,
                'total_commission_amount': row[5] or 0
            }

        # جلب المندوبين للنموذج
        representatives_query = "SELECT id, rep_name, rep_code FROM purchase_representatives WHERE is_active = 1"
        representatives = db_manager.execute_query(representatives_query)
        logger.info(f"✅ تم جلب {len(representatives) if representatives else 0} مندوب")

        logger.info("🎯 سيتم عرض template: purchase_commissions/purchase_orders.html")
        return render_template('purchase_commissions/purchase_orders.html',
                             orders=orders_data or [],
                             stats=stats,
                             representatives=representatives)

    except Exception as e:
        logger.error(f"خطأ في تحميل أوامر الشراء: {e}")
        flash(f'خطأ في تحميل أوامر الشراء: {str(e)}', 'error')
        return redirect(url_for('purchase_commissions.index'))


@purchase_commissions_bp.route('/assign-order', methods=['POST'])
def assign_order():
    """ربط أمر شراء مع مندوب"""
    try:
        db_manager = DatabaseManager()

        purchase_order_id = request.form.get('purchase_order_id')
        rep_id = request.form.get('rep_id')
        commission_percentage = request.form.get('commission_percentage', 100)
        notes = request.form.get('notes', '')

        if not purchase_order_id or not rep_id:
            flash('يرجى إدخال رقم أمر الشراء والمندوب', 'error')
            return redirect(url_for('purchase_commissions.purchase_orders'))

        # التحقق من عدم وجود ربط مسبق
        check_query = """
        SELECT COUNT(*) FROM purchase_order_representatives
        WHERE purchase_order_id = :1 AND rep_id = :2
        """
        existing = db_manager.execute_query(check_query, (purchase_order_id, rep_id))

        if existing and existing[0][0] > 0:
            flash('هذا المندوب مربوط مسبقاً بهذا الأمر', 'warning')
            return redirect(url_for('purchase_commissions.purchase_orders'))

        # إدراج الربط الجديد
        insert_query = """
        INSERT INTO purchase_order_representatives
        (purchase_order_id, rep_id, commission_percentage, notes, created_by)
        VALUES (:1, :2, :3, :4, 1)
        """

        db_manager.execute_update(insert_query, (
            purchase_order_id, rep_id, commission_percentage, notes
        ))

        flash('تم ربط أمر الشراء مع المندوب بنجاح', 'success')
        return redirect(url_for('purchase_commissions.purchase_orders'))

    except Exception as e:
        logger.error(f"خطأ في ربط أمر الشراء: {e}")
        flash(f'خطأ في ربط أمر الشراء: {str(e)}', 'error')
        return redirect(url_for('purchase_commissions.purchase_orders'))


@purchase_commissions_bp.route('/auto-calculate-commission/<int:order_id>/<int:rep_id>')
def auto_calculate_commission(order_id, rep_id):
    """حساب العمولة تلقائياً لأمر شراء"""
    try:
        db_manager = DatabaseManager()

        # جلب بيانات الأمر والمندوب
        order_query = """
        SELECT por.commission_percentage, ct.order_total_value
        FROM purchase_order_representatives por
        LEFT JOIN commission_tracking ct ON por.purchase_order_id = ct.purchase_order_id
                                         AND por.rep_id = ct.rep_id
        WHERE por.purchase_order_id = :1 AND por.rep_id = :2
        """

        order_data = db_manager.execute_query(order_query, (order_id, rep_id))

        if not order_data:
            flash('لم يتم العثور على بيانات الأمر', 'error')
            return redirect(url_for('purchase_commissions.purchase_orders'))

        commission_pct = order_data[0][0] or 100
        order_value = order_data[0][1] or 0

        if order_value == 0:
            # إنشاء قيمة تجريبية للأمر
            order_value = 10000 + (order_id * 1000)

        # حساب العمولة (نسبة بسيطة)
        commission_amount = (order_value * commission_pct / 100) * 0.02  # 2% عمولة افتراضية

        # تحديث أو إدراج في جدول التتبع
        update_query = """
        MERGE INTO commission_tracking ct
        USING (SELECT :1 as purchase_order_id, :2 as rep_id FROM dual) src
        ON (ct.purchase_order_id = src.purchase_order_id AND ct.rep_id = src.rep_id)
        WHEN MATCHED THEN
            UPDATE SET
                order_total_value = :3,
                commission_eligible_amount = :4,
                commission_calculated = 1,
                commission_amount = :5,
                calculation_date = SYSDATE,
                auto_calculated = 1,
                status = 'calculated',
                updated_at = SYSDATE
        WHEN NOT MATCHED THEN
            INSERT (purchase_order_id, rep_id, order_total_value, commission_eligible_amount,
                   commission_calculated, commission_amount, calculation_date, auto_calculated,
                   status, created_by)
            VALUES (:6, :7, :8, :9, 1, :10, SYSDATE, 1, 'calculated', 1)
        """

        db_manager.execute_update(update_query, (
            order_id, rep_id, order_value, order_value, commission_amount,
            order_id, rep_id, order_value, order_value, commission_amount
        ))

        flash(f'تم حساب العمولة تلقائياً: {commission_amount:.2f} ريال', 'success')
        return redirect(url_for('purchase_commissions.purchase_orders'))

    except Exception as e:
        logger.error(f"خطأ في الحساب التلقائي للعمولة: {e}")
        flash(f'خطأ في الحساب التلقائي: {str(e)}', 'error')
        return redirect(url_for('purchase_commissions.purchase_orders'))


@purchase_commissions_bp.route('/export-excel/<report_type>')
def export_excel(report_type):
    """تصدير التقارير إلى Excel"""
    try:
        # فحص توفر مكتبة openpyxl
        if not OPENPYXL_AVAILABLE:
            flash('مكتبة Excel غير متاحة. يرجى تثبيت openpyxl', 'error')
            return redirect(url_for('purchase_commissions.reports'))
        db_manager = DatabaseManager()

        # جلب معاملات الفلترة من URL
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        rep_id = request.args.get('rep_id')
        commission_type_id = request.args.get('commission_type_id')
        status = request.args.get('status')

        # إنشاء التقرير حسب النوع
        if report_type == 'summary':
            report_data = generate_summary_report(db_manager, date_from, date_to, rep_id, status)
            filename = f'تقرير_ملخص_العمولات_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            return create_summary_excel(report_data, filename, date_from, date_to)
        elif report_type == 'detailed':
            report_data = generate_detailed_report(db_manager, date_from, date_to, rep_id, commission_type_id, status)
            filename = f'تقرير_تفصيلي_العمولات_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            return create_detailed_excel(report_data, filename, date_from, date_to)
        elif report_type == 'performance':
            report_data = generate_performance_report(db_manager, date_from, date_to, rep_id)
            filename = f'تقرير_أداء_المندوبين_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            return create_performance_excel(report_data, filename, date_from, date_to)
        elif report_type == 'comparison':
            report_data = generate_comparison_report(db_manager, date_from, date_to)
            filename = f'تقرير_مقارنة_الفترات_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            return create_comparison_excel(report_data, filename, date_from, date_to)
        else:
            flash('نوع التقرير غير صحيح', 'error')
            return redirect(url_for('purchase_commissions.reports'))

    except Exception as e:
        logger.error(f"خطأ في تصدير Excel: {e}")
        flash(f'خطأ في تصدير التقرير: {str(e)}', 'error')
        return redirect(url_for('purchase_commissions.reports'))


def create_summary_excel(report_data, filename, date_from=None, date_to=None):
    """إنشاء ملف Excel لتقرير الملخص"""
    try:
        # إنشاء workbook جديد
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "ملخص العمولات"

        # تنسيق الخلايا
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        border = Border(left=Side(style='thin'), right=Side(style='thin'),
                       top=Side(style='thin'), bottom=Side(style='thin'))

        # العنوان الرئيسي
        ws.merge_cells('A1:G1')
        ws['A1'] = "تقرير ملخص العمولات"
        ws['A1'].font = Font(bold=True, size=16)
        ws['A1'].alignment = Alignment(horizontal='center')

        # معلومات التقرير
        row = 3
        if date_from:
            ws[f'A{row}'] = f"من تاريخ: {date_from}"
            row += 1
        if date_to:
            ws[f'A{row}'] = f"إلى تاريخ: {date_to}"
            row += 1

        ws[f'A{row}'] = f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        row += 2

        # رؤوس الأعمدة
        headers = ['المندوب', 'كود المندوب', 'عدد الحسابات', 'إجمالي العمولة',
                  'في الانتظار', 'معتمدة', 'مدفوعة']

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='center')

        # البيانات
        if report_data.get('data'):
            for item in report_data['data']:
                row += 1
                ws.cell(row=row, column=1, value=item['rep_name']).border = border
                ws.cell(row=row, column=2, value=item['rep_code']).border = border
                ws.cell(row=row, column=3, value=item['calculations_count']).border = border
                ws.cell(row=row, column=4, value=item['total_commission']).border = border
                ws.cell(row=row, column=5, value=item['pending_amount']).border = border
                ws.cell(row=row, column=6, value=item['approved_amount']).border = border
                ws.cell(row=row, column=7, value=item['paid_amount']).border = border

        # الإجماليات
        if report_data.get('totals'):
            row += 2
            ws.cell(row=row, column=1, value="الإجماليات").font = Font(bold=True)
            ws.cell(row=row, column=3, value=report_data['totals']['total_calculations']).font = Font(bold=True)
            ws.cell(row=row, column=4, value=report_data['totals']['total_commission']).font = Font(bold=True)
            ws.cell(row=row, column=5, value=report_data['totals']['total_pending']).font = Font(bold=True)
            ws.cell(row=row, column=6, value=report_data['totals']['total_approved']).font = Font(bold=True)
            ws.cell(row=row, column=7, value=report_data['totals']['total_paid']).font = Font(bold=True)

        # تعديل عرض الأعمدة
        for col in range(1, 8):
            ws.column_dimensions[get_column_letter(col)].width = 15

        # حفظ الملف في الذاكرة
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        logger.error(f"خطأ في إنشاء ملف Excel للملخص: {e}")
        raise e


def create_detailed_excel(report_data, filename, date_from=None, date_to=None):
    """إنشاء ملف Excel للتقرير التفصيلي"""
    try:
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "التقرير التفصيلي"

        # تنسيق الخلايا
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        border = Border(left=Side(style='thin'), right=Side(style='thin'),
                       top=Side(style='thin'), bottom=Side(style='thin'))

        # العنوان الرئيسي
        ws.merge_cells('A1:I1')
        ws['A1'] = "التقرير التفصيلي للعمولات"
        ws['A1'].font = Font(bold=True, size=16)
        ws['A1'].alignment = Alignment(horizontal='center')

        # معلومات التقرير
        row = 3
        if date_from:
            ws[f'A{row}'] = f"من تاريخ: {date_from}"
            row += 1
        if date_to:
            ws[f'A{row}'] = f"إلى تاريخ: {date_to}"
            row += 1

        ws[f'A{row}'] = f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        row += 2

        # رؤوس الأعمدة
        headers = ['التاريخ', 'المندوب', 'كود المندوب', 'قاعدة العمولة', 'نوع العمولة',
                  'قيمة الطلب', 'معدل العمولة', 'مبلغ العمولة', 'الحالة']

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='center')

        # البيانات
        if report_data.get('data'):
            for item in report_data['data']:
                row += 1
                ws.cell(row=row, column=1, value=item['calculation_date'].strftime('%Y-%m-%d') if item['calculation_date'] else '').border = border
                ws.cell(row=row, column=2, value=item['rep_name']).border = border
                ws.cell(row=row, column=3, value=item['rep_code']).border = border
                ws.cell(row=row, column=4, value=item['rule_name']).border = border
                ws.cell(row=row, column=5, value=item['type_name']).border = border
                ws.cell(row=row, column=6, value=item['order_value']).border = border
                ws.cell(row=row, column=7, value=f"{item['commission_rate']}%").border = border
                ws.cell(row=row, column=8, value=item['commission_amount']).border = border
                ws.cell(row=row, column=9, value=item['status']).border = border

        # تعديل عرض الأعمدة
        for col in range(1, 10):
            ws.column_dimensions[get_column_letter(col)].width = 15

        # حفظ الملف في الذاكرة
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        logger.error(f"خطأ في إنشاء ملف Excel التفصيلي: {e}")
        raise e


def create_performance_excel(report_data, filename, date_from=None, date_to=None):
    """إنشاء ملف Excel لتقرير الأداء"""
    try:
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "تقرير الأداء"

        # تنسيق الخلايا
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        border = Border(left=Side(style='thin'), right=Side(style='thin'),
                       top=Side(style='thin'), bottom=Side(style='thin'))

        # العنوان الرئيسي
        ws.merge_cells('A1:H1')
        ws['A1'] = "تقرير أداء المندوبين"
        ws['A1'].font = Font(bold=True, size=16)
        ws['A1'].alignment = Alignment(horizontal='center')

        # معلومات التقرير
        row = 3
        if date_from:
            ws[f'A{row}'] = f"من تاريخ: {date_from}"
            row += 1
        if date_to:
            ws[f'A{row}'] = f"إلى تاريخ: {date_to}"
            row += 1

        ws[f'A{row}'] = f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        row += 2

        # رؤوس الأعمدة
        headers = ['المندوب', 'كود المندوب', 'التخصص', 'عدد الحسابات',
                  'إجمالي العمولة', 'متوسط العمولة', 'إجمالي قيمة الطلبات', 'معدل النجاح %']

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='center')

        # البيانات
        if report_data.get('data'):
            for item in report_data['data']:
                row += 1
                ws.cell(row=row, column=1, value=item['rep_name']).border = border
                ws.cell(row=row, column=2, value=item['rep_code']).border = border
                ws.cell(row=row, column=3, value=item['specialization'] or '').border = border
                ws.cell(row=row, column=4, value=item['total_calculations']).border = border
                ws.cell(row=row, column=5, value=item['total_commission']).border = border
                ws.cell(row=row, column=6, value=item['avg_commission']).border = border
                ws.cell(row=row, column=7, value=item['total_orders_value']).border = border
                ws.cell(row=row, column=8, value=f"{item['success_rate']:.1f}%").border = border

        # تعديل عرض الأعمدة
        for col in range(1, 9):
            ws.column_dimensions[get_column_letter(col)].width = 18

        # حفظ الملف في الذاكرة
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        logger.error(f"خطأ في إنشاء ملف Excel للأداء: {e}")
        raise e


def create_comparison_excel(report_data, filename, date_from=None, date_to=None):
    """إنشاء ملف Excel لتقرير المقارنة"""
    try:
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "تقرير المقارنة"

        # تنسيق الخلايا
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        border = Border(left=Side(style='thin'), right=Side(style='thin'),
                       top=Side(style='thin'), bottom=Side(style='thin'))

        # العنوان الرئيسي
        ws.merge_cells('A1:E1')
        ws['A1'] = "تقرير مقارنة الفترات"
        ws['A1'].font = Font(bold=True, size=16)
        ws['A1'].alignment = Alignment(horizontal='center')

        # معلومات التقرير
        row = 3
        if date_from:
            ws[f'A{row}'] = f"من تاريخ: {date_from}"
            row += 1
        if date_to:
            ws[f'A{row}'] = f"إلى تاريخ: {date_to}"
            row += 1

        ws[f'A{row}'] = f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        row += 2

        # رؤوس الأعمدة
        headers = ['الشهر', 'عدد الحسابات', 'إجمالي العمولة', 'متوسط العمولة', 'المبلغ المدفوع']

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='center')

        # البيانات
        if report_data.get('data'):
            for item in report_data['data']:
                row += 1
                ws.cell(row=row, column=1, value=item['month_year']).border = border
                ws.cell(row=row, column=2, value=item['calculations_count']).border = border
                ws.cell(row=row, column=3, value=item['total_commission']).border = border
                ws.cell(row=row, column=4, value=item['avg_commission']).border = border
                ws.cell(row=row, column=5, value=item['paid_amount']).border = border

        # تعديل عرض الأعمدة
        for col in range(1, 6):
            ws.column_dimensions[get_column_letter(col)].width = 20

        # حفظ الملف في الذاكرة
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        logger.error(f"خطأ في إنشاء ملف Excel للمقارنة: {e}")
        raise e


@purchase_commissions_bp.route('/export-pdf/<report_type>')
def export_pdf(report_type):
    """تصدير التقارير إلى PDF"""
    try:
        # فحص توفر مكتبة reportlab
        if not REPORTLAB_AVAILABLE:
            flash('مكتبة PDF غير متاحة. يرجى تثبيت reportlab', 'error')
            return redirect(url_for('purchase_commissions.reports'))

        flash('تصدير PDF سيتم تطويره قريباً', 'info')
        return redirect(url_for('purchase_commissions.reports'))

    except Exception as e:
        logger.error(f"خطأ في تصدير PDF: {e}")
        flash(f'خطأ في تصدير التقرير: {str(e)}', 'error')
        return redirect(url_for('purchase_commissions.reports'))


@purchase_commissions_bp.route('/dashboard')
def dashboard():
    """لوحة تحكم تفاعلية مع رسوم بيانية"""
    try:
        db_manager = DatabaseManager()

        # إحصائيات عامة
        stats = get_dashboard_stats(db_manager)

        # بيانات الرسوم البيانية
        charts_data = get_charts_data(db_manager)

        # أحدث العمليات
        recent_activities = get_recent_activities(db_manager)

        # تنبيهات النظام
        alerts = get_system_alerts(db_manager)

        return render_template('purchase_commissions/dashboard.html',
                             stats=stats,
                             charts_data=charts_data,
                             recent_activities=recent_activities,
                             alerts=alerts)

    except Exception as e:
        logger.error(f"خطأ في تحميل لوحة التحكم: {e}")
        flash(f'خطأ في تحميل لوحة التحكم: {str(e)}', 'error')
        return redirect(url_for('purchase_commissions.index'))


def get_dashboard_stats(db_manager):
    """جلب إحصائيات لوحة التحكم"""
    try:
        stats = {}

        # إحصائيات المندوبين
        reps_query = """
        SELECT
            COUNT(*) as total_reps,
            SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_reps,
            COUNT(DISTINCT specialization) as specializations
        FROM purchase_representatives
        """
        reps_data = db_manager.execute_query(reps_query)
        if reps_data:
            row = reps_data[0]
            stats['total_reps'] = row[0] or 0
            stats['active_reps'] = row[1] or 0
            stats['specializations'] = row[2] or 0

        # إحصائيات العمولات
        commissions_query = """
        SELECT
            COUNT(*) as total_calculations,
            SUM(commission_amount) as total_amount,
            SUM(CASE WHEN status = 'calculated' THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_count,
            SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paid_count,
            SUM(CASE WHEN status = 'paid' THEN commission_amount ELSE 0 END) as paid_amount
        FROM commission_calculations
        WHERE calculation_date >= TRUNC(SYSDATE, 'MM')
        """
        comm_data = db_manager.execute_query(commissions_query)
        if comm_data:
            row = comm_data[0]
            stats['total_calculations'] = row[0] or 0
            stats['total_amount'] = row[1] or 0
            stats['pending_count'] = row[2] or 0
            stats['approved_count'] = row[3] or 0
            stats['paid_count'] = row[4] or 0
            stats['paid_amount'] = row[5] or 0

        # إحصائيات أوامر الشراء
        orders_query = """
        SELECT
            COUNT(DISTINCT purchase_order_id) as linked_orders,
            SUM(order_total_value) as total_orders_value,
            COUNT(*) as total_assignments
        FROM commission_tracking
        """
        orders_data = db_manager.execute_query(orders_query)
        if orders_data:
            row = orders_data[0]
            stats['linked_orders'] = row[0] or 0
            stats['total_orders_value'] = row[1] or 0
            stats['total_assignments'] = row[2] or 0

        return stats

    except Exception as e:
        logger.error(f"خطأ في جلب إحصائيات لوحة التحكم: {e}")
        return {}


def get_charts_data(db_manager):
    """جلب بيانات الرسوم البيانية"""
    try:
        charts = {}

        # رسم بياني للعمولات الشهرية
        monthly_query = """
        SELECT TO_CHAR(calculation_date, 'YYYY-MM') as month,
               SUM(commission_amount) as total_amount,
               COUNT(*) as calculations_count
        FROM commission_calculations
        WHERE calculation_date >= ADD_MONTHS(SYSDATE, -6)
        GROUP BY TO_CHAR(calculation_date, 'YYYY-MM')
        ORDER BY TO_CHAR(calculation_date, 'YYYY-MM')
        """
        monthly_data = db_manager.execute_query(monthly_query)

        charts['monthly_commissions'] = {
            'labels': [],
            'amounts': [],
            'counts': []
        }

        if monthly_data:
            for row in monthly_data:
                charts['monthly_commissions']['labels'].append(row[0])
                charts['monthly_commissions']['amounts'].append(float(row[1] or 0))
                charts['monthly_commissions']['counts'].append(row[2] or 0)

        # رسم بياني لأداء المندوبين
        performance_query = """
        SELECT pr.rep_name,
               SUM(cc.commission_amount) as total_commission,
               COUNT(cc.id) as calculations_count
        FROM commission_calculations cc
        JOIN purchase_representatives pr ON cc.rep_id = pr.id
        WHERE cc.calculation_date >= TRUNC(SYSDATE, 'MM')
        GROUP BY pr.rep_name
        ORDER BY SUM(cc.commission_amount) DESC
        FETCH FIRST 5 ROWS ONLY
        """
        perf_data = db_manager.execute_query(performance_query)

        charts['top_performers'] = {
            'labels': [],
            'amounts': [],
            'counts': []
        }

        if perf_data:
            for row in perf_data:
                charts['top_performers']['labels'].append(row[0])
                charts['top_performers']['amounts'].append(float(row[1] or 0))
                charts['top_performers']['counts'].append(row[2] or 0)

        # رسم بياني لحالات العمولات
        status_query = """
        SELECT status, COUNT(*) as count, SUM(commission_amount) as amount
        FROM commission_calculations
        WHERE calculation_date >= TRUNC(SYSDATE, 'MM')
        GROUP BY status
        """
        status_data = db_manager.execute_query(status_query)

        charts['commission_status'] = {
            'labels': [],
            'counts': [],
            'amounts': []
        }

        if status_data:
            for row in status_data:
                status_name = {
                    'calculated': 'محسوبة',
                    'approved': 'معتمدة',
                    'paid': 'مدفوعة',
                    'rejected': 'مرفوضة'
                }.get(row[0], row[0])

                charts['commission_status']['labels'].append(status_name)
                charts['commission_status']['counts'].append(row[1] or 0)
                charts['commission_status']['amounts'].append(float(row[2] or 0))

        return charts

    except Exception as e:
        logger.error(f"خطأ في جلب بيانات الرسوم البيانية: {e}")
        return {}


def get_recent_activities(db_manager):
    """جلب أحدث العمليات"""
    try:
        query = """
        SELECT cc.calculation_date, pr.rep_name, cc.commission_amount, cc.status,
               cr.rule_name, cc.purchase_order_id
        FROM commission_calculations cc
        JOIN purchase_representatives pr ON cc.rep_id = pr.id
        JOIN commission_rules cr ON cc.rule_id = cr.id
        ORDER BY cc.calculation_date DESC
        FETCH FIRST 10 ROWS ONLY
        """

        activities_data = db_manager.execute_query(query)
        activities = []

        if activities_data:
            for row in activities_data:
                activities.append({
                    'date': row[0],
                    'rep_name': row[1],
                    'amount': row[2] or 0,
                    'status': row[3],
                    'rule_name': row[4],
                    'order_id': row[5]
                })

        return activities

    except Exception as e:
        logger.error(f"خطأ في جلب أحدث العمليات: {e}")
        return []


def get_system_alerts(db_manager):
    """جلب تنبيهات النظام"""
    try:
        alerts = []

        # تنبيه العمولات المعلقة
        pending_query = """
        SELECT COUNT(*) FROM commission_calculations
        WHERE status = 'calculated' AND calculation_date < SYSDATE - 7
        """
        pending_data = db_manager.execute_query(pending_query)
        if pending_data and pending_data[0][0] > 0:
            alerts.append({
                'type': 'warning',
                'title': 'عمولات معلقة',
                'message': f'يوجد {pending_data[0][0]} عمولة معلقة لأكثر من أسبوع',
                'action': 'مراجعة العمولات المعلقة'
            })

        # تنبيه المندوبين غير النشطين
        inactive_query = """
        SELECT COUNT(*) FROM purchase_representatives
        WHERE is_active = 0
        """
        inactive_data = db_manager.execute_query(inactive_query)
        if inactive_data and inactive_data[0][0] > 0:
            alerts.append({
                'type': 'info',
                'title': 'مندوبين غير نشطين',
                'message': f'يوجد {inactive_data[0][0]} مندوب غير نشط',
                'action': 'مراجعة حالة المندوبين'
            })

        # تنبيه أوامر الشراء غير المربوطة
        unlinked_query = """
        SELECT COUNT(*) FROM commission_tracking
        WHERE status = 'pending' AND calculation_date < SYSDATE - 3
        """
        unlinked_data = db_manager.execute_query(unlinked_query)
        if unlinked_data and unlinked_data[0][0] > 0:
            alerts.append({
                'type': 'danger',
                'title': 'أوامر غير مربوطة',
                'message': f'يوجد {unlinked_data[0][0]} أمر شراء غير مربوط لأكثر من 3 أيام',
                'action': 'ربط أوامر الشراء'
            })

        return alerts

    except Exception as e:
        logger.error(f"خطأ في جلب تنبيهات النظام: {e}")
        return []


@purchase_commissions_bp.route('/notifications')
def notifications():
    """صفحة الإشعارات"""
    try:
        db_manager = DatabaseManager()

        # جلب الإشعارات
        user_id = 1  # يجب جلبه من session المستخدم الحالي
        notifications_data = get_user_notifications(db_manager, user_id)

        # إحصائيات الإشعارات
        stats = get_notifications_stats(db_manager, user_id)

        return render_template('purchase_commissions/notifications.html',
                             notifications=notifications_data,
                             stats=stats)

    except Exception as e:
        logger.error(f"خطأ في تحميل الإشعارات: {e}")
        flash(f'خطأ في تحميل الإشعارات: {str(e)}', 'error')
        return redirect(url_for('purchase_commissions.index'))


@purchase_commissions_bp.route('/notifications/mark-read/<int:notification_id>')
def mark_notification_read(notification_id):
    """تحديد إشعار كمقروء"""
    try:
        db_manager = DatabaseManager()

        update_query = """
        UPDATE commission_notifications
        SET is_read = 1, read_at = SYSDATE
        WHERE id = :1
        """

        db_manager.execute_update(update_query, (notification_id,))

        return jsonify({'success': True, 'message': 'تم تحديد الإشعار كمقروء'})

    except Exception as e:
        logger.error(f"خطأ في تحديد الإشعار كمقروء: {e}")
        return jsonify({'success': False, 'message': str(e)})


@purchase_commissions_bp.route('/notifications/mark-all-read')
def mark_all_notifications_read():
    """تحديد جميع الإشعارات كمقروءة"""
    try:
        db_manager = DatabaseManager()
        user_id = 1  # يجب جلبه من session المستخدم الحالي

        update_query = """
        UPDATE commission_notifications
        SET is_read = 1, read_at = SYSDATE
        WHERE (recipient_type = 'user' AND recipient_id = :1)
           OR recipient_type = 'all'
           AND is_read = 0
        """

        db_manager.execute_update(update_query, (user_id,))

        return jsonify({'success': True, 'message': 'تم تحديد جميع الإشعارات كمقروءة'})

    except Exception as e:
        logger.error(f"خطأ في تحديد جميع الإشعارات كمقروءة: {e}")
        return jsonify({'success': False, 'message': str(e)})


def get_user_notifications(db_manager, user_id, limit=50):
    """جلب إشعارات المستخدم"""
    try:
        query = """
        SELECT id, notification_type, title, message, priority_level,
               is_read, created_at, action_url, action_label,
               related_entity_type, related_entity_id
        FROM commission_notifications
        WHERE (recipient_type = 'user' AND recipient_id = :1)
           OR recipient_type = 'all'
        ORDER BY
            CASE WHEN is_read = 0 THEN 0 ELSE 1 END,
            CASE priority_level
                WHEN 'urgent' THEN 1
                WHEN 'high' THEN 2
                WHEN 'normal' THEN 3
                WHEN 'low' THEN 4
            END,
            created_at DESC
        FETCH FIRST :2 ROWS ONLY
        """

        notifications_data = db_manager.execute_query(query, (user_id, limit))
        notifications = []

        if notifications_data:
            for row in notifications_data:
                notifications.append({
                    'id': row[0],
                    'type': row[1],
                    'title': row[2],
                    'message': row[3],
                    'priority': row[4],
                    'is_read': bool(row[5]),
                    'created_at': row[6],
                    'action_url': row[7],
                    'action_label': row[8],
                    'entity_type': row[9],
                    'entity_id': row[10]
                })

        return notifications

    except Exception as e:
        logger.error(f"خطأ في جلب إشعارات المستخدم: {e}")
        return []


def get_notifications_stats(db_manager, user_id):
    """جلب إحصائيات الإشعارات"""
    try:
        query = """
        SELECT
            COUNT(*) as total_count,
            SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread_count,
            SUM(CASE WHEN priority_level = 'urgent' AND is_read = 0 THEN 1 ELSE 0 END) as urgent_count,
            SUM(CASE WHEN priority_level = 'high' AND is_read = 0 THEN 1 ELSE 0 END) as high_count
        FROM commission_notifications
        WHERE (recipient_type = 'user' AND recipient_id = :1)
           OR recipient_type = 'all'
        """

        stats_data = db_manager.execute_query(query, (user_id,))

        if stats_data:
            row = stats_data[0]
            return {
                'total_count': row[0] or 0,
                'unread_count': row[1] or 0,
                'urgent_count': row[2] or 0,
                'high_count': row[3] or 0
            }

        return {'total_count': 0, 'unread_count': 0, 'urgent_count': 0, 'high_count': 0}

    except Exception as e:
        logger.error(f"خطأ في جلب إحصائيات الإشعارات: {e}")
        return {'total_count': 0, 'unread_count': 0, 'urgent_count': 0, 'high_count': 0}


def create_notification(db_manager, notification_type, title, message, recipient_type='user',
                       recipient_id=None, entity_type=None, entity_id=None, priority='normal',
                       action_url=None, action_label=None):
    """إنشاء إشعار جديد"""
    try:
        insert_query = """
        INSERT INTO commission_notifications
        (notification_type, title, message, recipient_type, recipient_id,
         related_entity_type, related_entity_id, priority_level,
         action_url, action_label, created_by)
        VALUES (:1, :2, :3, :4, :5, :6, :7, :8, :9, :10, 1)
        """

        db_manager.execute_update(insert_query, (
            notification_type, title, message, recipient_type, recipient_id,
            entity_type, entity_id, priority, action_url, action_label
        ))

        logger.info(f"تم إنشاء إشعار جديد: {title}")
        return True

    except Exception as e:
        logger.error(f"خطأ في إنشاء الإشعار: {e}")
        return False
