<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المخلص - نظام إدارة الشحنات المتقدم</title>

    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0891b2;
            --light-bg: #f8fafc;
            --dark-bg: #0f172a;
            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --card-shadow-hover: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #1e293b;
            overflow-x: hidden;
        }

        /* Header Styles */
        .agent-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .agent-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--info-color), var(--success-color));
        }

        .agent-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .agent-subtitle {
            color: var(--secondary-color);
            font-size: 1.1rem;
            font-weight: 400;
        }

        /* Navigation Styles */
        .top-nav {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 1rem 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .nav-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 0.5rem 1rem;
            text-decoration: none;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            transform: translateY(-2px);
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-align: center;
            transition: var(--transition);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
            transition: var(--transition);
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--card-shadow-hover);
        }

        .stat-card.success::before { background: var(--success-color); }
        .stat-card.warning::before { background: var(--warning-color); }
        .stat-card.danger::before { background: var(--danger-color); }
        .stat-card.info::before { background: var(--info-color); }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .stat-card.success .stat-icon { color: var(--success-color); }
        .stat-card.warning .stat-icon { color: var(--warning-color); }
        .stat-card.danger .stat-icon { color: var(--danger-color); }
        .stat-card.info .stat-icon { color: var(--info-color); }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }

        .stat-card.success .stat-number { color: var(--success-color); }
        .stat-card.warning .stat-number { color: var(--warning-color); }
        .stat-card.danger .stat-number { color: var(--danger-color); }
        .stat-card.info .stat-number { color: var(--info-color); }

        .stat-label {
            font-size: 0.9rem;
            color: var(--secondary-color);
            font-weight: 500;
        }

        /* Dashboard Cards */
        .dashboard-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: var(--transition);
        }

        .dashboard-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--card-shadow-hover);
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: between;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f1f5f9;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .card-badge {
            background: var(--primary-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
    </style>
</head>
<body>

    <!-- Navigation -->
    <div class="container-fluid">
        <nav class="top-nav">
            <div class="d-flex justify-content-between align-items-center">
                <a href="#" class="nav-brand">
                    <i class="fas fa-shipping-fast"></i>
                    نظام إدارة الشحنات
                </a>
                <div class="nav-actions">
                    <a href="{{ url_for('agent_portal.orders') }}" class="nav-btn">
                        <i class="fas fa-list"></i>
                        أوامر التسليم
                    </a>
                    <a href="{{ url_for('agent_portal.performance') }}" class="nav-btn">
                        <i class="fas fa-chart-line"></i>
                        الأداء
                    </a>
                    <a href="{{ url_for('auth.logout') }}" class="nav-btn">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل الخروج
                    </a>
                </div>
            </div>
        </nav>

        <!-- Header -->
        <div class="agent-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="agent-title">
                        <i class="fas fa-user-tie"></i>
                        مرحباً بك في بوابة المخلص
                    </h1>
                    <p class="agent-subtitle">إدارة أوامر التسليم والوثائق بكفاءة عالية</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex gap-2 flex-wrap justify-content-end">
                        <span class="badge bg-success fs-6 px-3 py-2">
                            <i class="fas fa-circle me-1"></i>
                            متصل
                        </span>
                        <span class="badge bg-info fs-6 px-3 py-2">
                            <i class="fas fa-clock me-1"></i>
                            {{ moment().format('HH:mm') }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div class="stat-number">{{ dashboard_data.orders_stats.total or 0 }}</div>
                <div class="stat-label">إجمالي الأوامر</div>
            </div>

            <div class="stat-card success">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number">{{ dashboard_data.orders_stats.completed or 0 }}</div>
                <div class="stat-label">أوامر مكتملة</div>
            </div>

            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-hourglass-half"></i>
                </div>
                <div class="stat-number">{{ dashboard_data.orders_stats.in_progress or 0 }}</div>
                <div class="stat-label">قيد التنفيذ</div>
            </div>

            <div class="stat-card danger">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number">{{ dashboard_data.orders_stats.overdue or 0 }}</div>
                <div class="stat-label">أوامر متأخرة</div>
            </div>

            <div class="stat-card info">
                <div class="stat-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="stat-number">{{ dashboard_data.documents_stats.available or 0 }}</div>
                <div class="stat-label">وثائق متوفرة</div>
            </div>

            <div class="stat-card danger">
                <div class="stat-icon">
                    <i class="fas fa-file-times"></i>
                </div>
                <div class="stat-number">{{ dashboard_data.documents_stats.missing_required or 0 }}</div>
                <div class="stat-label">وثائق مطلوبة</div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row">
            <!-- Recent Orders -->
            <div class="col-lg-8">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-clock"></i>
                            أوامر التسليم الحديثة
                        </h2>
                        <span class="card-badge">{{ dashboard_data.recent_orders|length }}</span>
                    </div>

                    {% if dashboard_data.recent_orders %}
                        <div class="orders-list">
                            {% for order in dashboard_data.recent_orders %}
                            <div class="order-item">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <div class="order-info">
                                            <h6 class="order-title">{{ order[1] }}</h6>
                                            <small class="order-subtitle">{{ order[6] }}</small>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <span class="priority-badge priority-{{ order[3] }}">
                                            {{ order[3] }}
                                        </span>
                                    </div>
                                    <div class="col-md-2">
                                        {% set status_class = {
                                            'draft': 'secondary',
                                            'sent': 'info',
                                            'in_progress': 'warning',
                                            'completed': 'success',
                                            'cancelled': 'danger'
                                        } %}
                                        <span class="status-badge bg-{{ status_class.get(order[2], 'secondary') }}">
                                            {{ order[2] }}
                                        </span>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="order-dates">
                                            <small>إنشاء: {{ order[4].strftime('%Y-%m-%d') if order[4] else 'غير محدد' }}</small>
                                            <br>
                                            <small>موعد: {{ order[5].strftime('%Y-%m-%d') if order[5] else 'غير محدد' }}</small>
                                        </div>
                                    </div>
                                    <div class="col-md-2 text-end">
                                        <a href="{{ url_for('agent_portal.order_details', order_id=order[0]) }}"
                                           class="action-btn">
                                            <i class="fas fa-eye"></i>
                                            عرض
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <div class="text-center mt-4">
                            <a href="{{ url_for('agent_portal.orders') }}" class="primary-btn">
                                <i class="fas fa-list me-2"></i>
                                عرض جميع الأوامر
                            </a>
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-inbox"></i>
                            <h4>لا توجد أوامر تسليم حديثة</h4>
                            <p>ستظهر أوامر التسليم الجديدة هنا</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Overdue Orders -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title text-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            أوامر متأخرة
                        </h3>
                        <span class="card-badge bg-danger">{{ dashboard_data.overdue_orders|length }}</span>
                    </div>

                    {% if dashboard_data.overdue_orders %}
                        <div class="notifications-list">
                            {% for order in dashboard_data.overdue_orders[:5] %}
                            <div class="notification-item overdue">
                                <div class="notification-content">
                                    <h6 class="notification-title">{{ order[1] }}</h6>
                                    <p class="notification-text">{{ order[5] }}</p>
                                    <small class="notification-time text-danger">
                                        <i class="fas fa-clock me-1"></i>
                                        متأخر {{ order[4] }} يوم
                                    </small>
                                </div>
                                <div class="notification-badge">
                                    <span class="priority-badge priority-{{ order[2] }}">
                                        {{ order[2] }}
                                    </span>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state small">
                            <i class="fas fa-check-circle text-success"></i>
                            <h5>ممتاز!</h5>
                            <p>لا توجد أوامر متأخرة</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Recent Notifications -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title text-info">
                            <i class="fas fa-bell"></i>
                            إشعارات حديثة
                        </h3>
                        <span class="card-badge bg-info">{{ dashboard_data.recent_notifications|length }}</span>
                    </div>

                    {% if dashboard_data.recent_notifications %}
                        <div class="notifications-list">
                            {% for notification in dashboard_data.recent_notifications %}
                            <div class="notification-item">
                                <div class="notification-content">
                                    <h6 class="notification-title">{{ notification[0] or 'إشعار' }}</h6>
                                    <p class="notification-text">{{ notification[1][:50] }}...</p>
                                    <small class="notification-time">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ notification[2].strftime('%Y-%m-%d %H:%M') if notification[2] else '' }}
                                    </small>
                                </div>
                                <div class="notification-icon">
                                    {% if notification[3] == 'SUCCESS' %}
                                        <i class="fas fa-check-circle text-success"></i>
                                    {% else %}
                                        <i class="fas fa-exclamation-circle text-warning"></i>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <div class="text-center mt-3">
                            <a href="{{ url_for('agent_portal.notifications') }}" class="secondary-btn">
                                عرض جميع الإشعارات
                            </a>
                        </div>
                    {% else %}
                        <div class="empty-state small">
                            <i class="fas fa-bell-slash"></i>
                            <h5>لا توجد إشعارات</h5>
                            <p>ستظهر الإشعارات الجديدة هنا</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Order Items */
        .orders-list {
            max-height: 600px;
            overflow-y: auto;
        }

        .order-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: var(--transition);
        }

        .order-item:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.1);
            transform: translateY(-2px);
        }

        .order-info h6 {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .order-subtitle {
            color: var(--secondary-color);
            font-size: 0.85rem;
        }

        .order-dates small {
            color: var(--secondary-color);
            font-size: 0.8rem;
        }

        /* Priority Badges */
        .priority-badge {
            font-size: 0.75rem;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .priority-urgent {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            color: white;
        }

        .priority-high {
            background: linear-gradient(135deg, #d97706, #b45309);
            color: white;
        }

        .priority-normal {
            background: linear-gradient(135deg, #0891b2, #0e7490);
            color: white;
        }

        .priority-low {
            background: linear-gradient(135deg, #64748b, #475569);
            color: white;
        }

        /* Status Badges */
        .status-badge {
            font-size: 0.75rem;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-weight: 600;
            color: white;
        }

        /* Buttons */
        .primary-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .primary-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
            color: white;
        }

        .secondary-btn {
            background: rgba(100, 116, 139, 0.1);
            color: var(--secondary-color);
            border: 1px solid rgba(100, 116, 139, 0.2);
            border-radius: 8px;
            padding: 0.5rem 1rem;
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .secondary-btn:hover {
            background: rgba(100, 116, 139, 0.2);
            color: var(--secondary-color);
            transform: translateY(-1px);
        }

        .action-btn {
            background: rgba(37, 99, 235, 0.1);
            color: var(--primary-color);
            border: 1px solid rgba(37, 99, 235, 0.2);
            border-radius: 6px;
            padding: 0.5rem 1rem;
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
        }

        .action-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-1px);
        }

        /* Notifications */
        .notifications-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .notification-item {
            background: #f8fafc;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            border-left: 4px solid var(--primary-color);
            transition: var(--transition);
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .notification-item.overdue {
            border-left-color: var(--danger-color);
            background: #fef2f2;
        }

        .notification-item:hover {
            transform: translateX(-4px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.25rem;
        }

        .notification-text {
            font-size: 0.8rem;
            color: var(--secondary-color);
            margin-bottom: 0.25rem;
            line-height: 1.4;
        }

        .notification-time {
            font-size: 0.75rem;
            color: var(--secondary-color);
        }

        .notification-icon {
            font-size: 1.2rem;
        }

        /* Empty States */
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: var(--secondary-color);
        }

        .empty-state.small {
            padding: 2rem 1rem;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .empty-state.small i {
            font-size: 2rem;
        }

        .empty-state h4, .empty-state h5 {
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
        }

        .empty-state p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container-fluid {
                padding: 1rem;
            }

            .agent-header {
                padding: 1.5rem;
            }

            .agent-title {
                font-size: 1.5rem;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 1rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .dashboard-card {
                padding: 1.5rem;
            }

            .nav-actions {
                flex-direction: column;
                gap: 0.5rem;
            }

            .nav-btn {
                padding: 0.75rem 1rem;
                justify-content: center;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dashboard-card, .stat-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .stat-card:nth-child(1) { animation-delay: 0.1s; }
        .stat-card:nth-child(2) { animation-delay: 0.2s; }
        .stat-card:nth-child(3) { animation-delay: 0.3s; }
        .stat-card:nth-child(4) { animation-delay: 0.4s; }
        .stat-card:nth-child(5) { animation-delay: 0.5s; }
        .stat-card:nth-child(6) { animation-delay: 0.6s; }

        /* Scrollbar Styling */
        .orders-list::-webkit-scrollbar,
        .notifications-list::-webkit-scrollbar {
            width: 6px;
        }

        .orders-list::-webkit-scrollbar-track,
        .notifications-list::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .orders-list::-webkit-scrollbar-thumb,
        .notifications-list::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        .orders-list::-webkit-scrollbar-thumb:hover,
        .notifications-list::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }
    </style>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Auto refresh every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);

        // Enhanced interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading states
            const actionButtons = document.querySelectorAll('.action-btn, .primary-btn, .secondary-btn');
            actionButtons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    if (!this.classList.contains('loading')) {
                        this.classList.add('loading');
                        const originalText = this.innerHTML;
                        this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...';

                        setTimeout(() => {
                            this.innerHTML = originalText;
                            this.classList.remove('loading');
                        }, 2000);
                    }
                });
            });

            // Real-time clock
            function updateClock() {
                const now = new Date();
                const timeString = now.toLocaleTimeString('ar-SA', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
                const clockElement = document.querySelector('.badge:has(.fa-clock)');
                if (clockElement) {
                    clockElement.innerHTML = `<i class="fas fa-clock me-1"></i>${timeString}`;
                }
            }

            // Update clock every second
            setInterval(updateClock, 1000);
            updateClock();

            // Add smooth scrolling
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add notification sound (optional)
            function playNotificationSound() {
                const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                audio.play().catch(() => {}); // Ignore errors if audio fails
            }

            // Simulate real-time updates (for demo)
            setInterval(() => {
                const badges = document.querySelectorAll('.card-badge');
                badges.forEach(badge => {
                    const currentValue = parseInt(badge.textContent);
                    if (Math.random() > 0.95) { // 5% chance of update
                        badge.textContent = currentValue + (Math.random() > 0.5 ? 1 : -1);
                        badge.style.animation = 'pulse 0.5s ease-in-out';
                        setTimeout(() => {
                            badge.style.animation = '';
                        }, 500);
                    }
                });
            }, 10000); // Check every 10 seconds
        });

        // Add CSS for loading state
        const style = document.createElement('style');
        style.textContent = `
            .loading {
                pointer-events: none;
                opacity: 0.7;
            }

            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.1); }
                100% { transform: scale(1); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
