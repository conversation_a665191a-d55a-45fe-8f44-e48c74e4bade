-- حذف الجداول الموجودة
DROP TABLE cargo_document_shipments CASCADE CONSTRAINTS;
DROP TABLE cargo_shipment_items CASCADE CONSTRAINTS;
DROP TABLE cargo_containers CASCADE CONSTRAINTS;
DROP TABLE cargo_shipments CASCADE CONSTRAINTS;

-- حذف الـ sequences
DROP SEQUENCE cargo_shipments_seq;
DROP SEQUENCE cargo_containers_seq;
DROP SEQUENCE cargo_shipment_items_seq;
DROP SEQUENCE cargo_document_shipments_seq;

-- إنشاء الـ sequences
CREATE SEQUENCE cargo_shipments_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE cargo_containers_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE cargo_shipment_items_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE cargo_document_shipments_seq START WITH 1 INCREMENT BY 1;

-- جدول الشحنات الرئيسي
CREATE TABLE cargo_shipments (
    id NUMBER DEFAULT cargo_shipments_seq.NEXTVAL PRIMARY KEY,
    shipment_number VARCHAR2(50) UNIQUE NOT NULL,
    
    -- البيانات الأساسية
    booking_number VARCHAR2(100) NOT NULL,
    shipping_type VARCHAR2(20) DEFAULT 'FCL',
    shipping_line_id NUMBER,
    priority VARCHAR2(20),
    bill_of_lading_number VARCHAR2(100),
    tracking_number VARCHAR2(100),
    
    -- الموانئ والتواريخ
    origin_port_id NUMBER,
    destination_port_id NUMBER,
    etd TIMESTAMP,
    eta TIMESTAMP,
    estimated_transit_time NUMBER,
    
    -- بيانات السفينة والشحن
    vessel_name VARCHAR2(200),
    voyage_number VARCHAR2(100),
    container_seal_number VARCHAR2(100),
    freight_forwarder VARCHAR2(200),
    incoterms VARCHAR2(50),
    port_of_loading VARCHAR2(200),
    port_of_discharge VARCHAR2(200),
    
    -- البيانات الجمركية
    customs_declaration_number VARCHAR2(100),
    insurance_policy_number VARCHAR2(100),
    shipping_instructions CLOB,
    
    -- الموردين
    shipper_id NUMBER,
    consignee_id NUMBER,
    notify_party_id NUMBER,
    purchase_order_id NUMBER,
    
    -- البضاعة
    cargo_description CLOB,
    cargo_type VARCHAR2(100),
    total_weight NUMBER(15,3),
    total_volume NUMBER(15,3),
    total_packages NUMBER,
    package_type VARCHAR2(100),
    is_dangerous NUMBER(1) DEFAULT 0,
    temperature_controlled NUMBER(1) DEFAULT 0,
    special_instructions CLOB,
    
    -- التكاليف
    currency VARCHAR2(10) DEFAULT 'USD',
    freight_cost NUMBER(15,2),
    other_charges NUMBER(15,2),
    total_cost NUMBER(15,2),
    
    -- حالة الشحنة
    status VARCHAR2(50) DEFAULT 'محجوز',
    
    -- بيانات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR2(100),
    updated_at TIMESTAMP,
    updated_by VARCHAR2(100)
);

-- جدول الحاويات
CREATE TABLE cargo_containers (
    id NUMBER DEFAULT cargo_containers_seq.NEXTVAL PRIMARY KEY,
    cargo_shipment_id NUMBER NOT NULL,
    container_number VARCHAR2(50) NOT NULL,
    container_type VARCHAR2(20),
    container_size VARCHAR2(10),
    seal_number VARCHAR2(50),
    weight_empty NUMBER(10,2),
    weight_loaded NUMBER(10,2),
    temperature_controlled NUMBER(1) DEFAULT 0,
    notes CLOB,
    
    -- بيانات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_container_shipment FOREIGN KEY (cargo_shipment_id) 
        REFERENCES cargo_shipments(id) ON DELETE CASCADE
);

-- جدول أصناف الشحنة
CREATE TABLE cargo_shipment_items (
    id NUMBER DEFAULT cargo_shipment_items_seq.NEXTVAL PRIMARY KEY,
    cargo_shipment_id NUMBER NOT NULL,
    po_item_id NUMBER,
    item_code VARCHAR2(50),
    item_name VARCHAR2(500) NOT NULL,
    unit VARCHAR2(50),
    quantity NUMBER(15,3) NOT NULL,
    unit_price NUMBER(15,2),
    total_price NUMBER(15,2),
    container_id NUMBER,
    recipient_id NUMBER,
    production_date DATE,
    expiry_date DATE,
    notes CLOB,
    
    -- بيانات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_item_shipment FOREIGN KEY (cargo_shipment_id) 
        REFERENCES cargo_shipments(id) ON DELETE CASCADE,
    CONSTRAINT fk_item_container FOREIGN KEY (container_id) 
        REFERENCES cargo_containers(id) ON DELETE SET NULL
);

-- جدول إرسال المستندات
CREATE TABLE cargo_document_shipments (
    id NUMBER DEFAULT cargo_document_shipments_seq.NEXTVAL PRIMARY KEY,
    cargo_shipment_id NUMBER NOT NULL,
    courier_company VARCHAR2(200),
    tracking_number VARCHAR2(100),
    recipient_name VARCHAR2(200),
    recipient_address CLOB,
    sent_date DATE,
    delivery_status VARCHAR2(50) DEFAULT 'لم يتم الإرسال',
    notes CLOB,
    
    -- بيانات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_docs_shipment FOREIGN KEY (cargo_shipment_id) 
        REFERENCES cargo_shipments(id) ON DELETE CASCADE
);

-- إنشاء الفهارس
CREATE INDEX idx_shipment_number ON cargo_shipments(shipment_number);
CREATE INDEX idx_booking_number ON cargo_shipments(booking_number);
CREATE INDEX idx_bl_number ON cargo_shipments(bill_of_lading_number);
CREATE INDEX idx_tracking_number ON cargo_shipments(tracking_number);
CREATE INDEX idx_container_shipment ON cargo_containers(cargo_shipment_id);
CREATE INDEX idx_item_shipment ON cargo_shipment_items(cargo_shipment_id);
CREATE INDEX idx_item_container ON cargo_shipment_items(container_id);
CREATE INDEX idx_docs_shipment ON cargo_document_shipments(cargo_shipment_id);

COMMIT;
