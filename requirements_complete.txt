# النظام المحاسبي المتقدم - متطلبات شاملة
# Advanced Accounting System - Complete Requirements

# إطار العمل الأساسي - Core Framework
Flask==2.3.3
Werkzeug==2.3.7
Jinja2==3.1.2
MarkupSafe==2.1.3
itsdangerous==2.1.2
click==8.1.7

# قاعدة البيانات - Database
Flask-SQLAlchemy==3.0.5
Flask-Migrate==4.0.5
SQLAlchemy==2.0.21

# المصادقة والأمان - Authentication & Security
Flask-Login==0.6.3
Flask-WTF==1.1.1
WTForms==3.0.1
bcrypt==4.0.1
cryptography==41.0.4

# النماذج والتحقق - Forms & Validation
email-validator==2.0.0
phonenumbers==8.13.19

# التاريخ والوقت - Date & Time
python-dateutil==2.8.2
pytz==2023.3

# البريد الإلكتروني - <PERSON><PERSON>lask-Mail==0.9.1

# التقارير والتصدير - Reports & Export
openpyxl==3.1.2
reportlab==4.0.4
pandas==2.1.1
numpy==1.25.2
matplotlib==3.7.2

# التخزين المؤقت - Caching
Flask-Caching==2.1.0

# الاختبارات - Testing
pytest==7.4.2
pytest-flask==1.2.0
coverage==7.3.1

# التطوير - Development
python-dotenv==1.0.0

# الأمان المتقدم - Advanced Security
PyJWT==2.8.0

# إدارة الملفات - File Management
Pillow==10.0.0

# أدوات التطوير - Development Tools
black==23.7.0
flake8==6.0.0

# أدوات إضافية - Additional Tools
requests==2.31.0
