<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أرصدة الموردين - نافذة محسنة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa; }
        .page-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px 0; margin-bottom: 0; }
        .breadcrumb { background: #e9ecef; padding: 10px 0; margin-bottom: 20px; }
        .breadcrumb-item + .breadcrumb-item::before { content: "←"; }
        .filter-card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); }
        .results-card { background: white; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .balance-positive { color: #28a745; font-weight: bold; }
        .balance-negative { color: #dc3545; font-weight: bold; }
        .balance-zero { color: #6c757d; }
        .loading { text-align: center; padding: 40px; }
        .no-results { text-align: center; padding: 40px; color: #6c757d; }
        .filter-info { background: #e3f2fd; padding: 10px; border-radius: 5px; margin-bottom: 15px; }
        #voiceSearchBtn { border-right: none; }
        #voiceSearchBtn:hover { background-color: #0d6efd; color: white; }
        .voice-listening { animation: pulse 1.5s infinite; }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        .currency-usd { color: #28a745; font-weight: bold; }
        .currency-eur { color: #007bff; font-weight: bold; }
        .currency-sar { color: #17a2b8; font-weight: bold; }
        .currency-aed { color: #fd7e14; font-weight: bold; }
        .currency-cny { color: #dc3545; font-weight: bold; }
        .currency-default { color: #6c757d; font-weight: bold; }
    </style>
</head>
<body>
    <!-- هيدر الصفحة -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1 class="mb-0"><i class="fas fa-balance-scale"></i> أرصدة الموردين - النافذة المحسنة</h1>
                    <p class="mb-0 mt-2 opacity-75">
                        عرض الأرصدة الموجودة فقط • بحث صوتي ذكي • تصنيف محاسبي صحيح
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- مسار التنقل -->
    <div class="breadcrumb">
        <div class="container-fluid">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('suppliers.index') }}"><i class="fas fa-truck"></i> الموردين</a></li>
                    <li class="breadcrumb-item active" aria-current="page"><i class="fas fa-balance-scale"></i> أرصدة الموردين المحسنة</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="container-fluid">

        <!-- قسم الفلاتر -->
        <div class="filter-card">
            <h5><i class="fas fa-filter"></i> الفلاتر</h5>
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">البحث السريع</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchFilter" placeholder="اسم المورد أو الكود...">
                        <button class="btn btn-outline-primary" type="button" id="voiceSearchBtn" title="البحث الصوتي">
                            <i class="fas fa-microphone" id="micIcon"></i>
                        </button>
                    </div>
                    <small class="text-muted" id="voiceStatus"></small>
                </div>
                <div class="col-md-3">
                    <label class="form-label">الفترة الزمنية</label>
                    <select class="form-select" id="periodFilter">
                        <option value="">جميع الفترات</option>
                        <option value="today">اليوم</option>
                        <option value="this_week">هذا الأسبوع</option>
                        <option value="this_month" selected>هذا الشهر</option>
                        <option value="last_month">الشهر الماضي</option>
                        <option value="this_quarter">هذا الربع</option>
                        <option value="this_year">هذا العام</option>
                        <option value="custom">فترة مخصصة</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">العملة</label>
                    <select class="form-select" id="currencyFilter">
                        <option value="">جميع العملات</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">حالة الرصيد</label>
                    <select class="form-select" id="balanceStatusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="creditor">دائن (سالب)</option>
                        <option value="debtor">مدين (موجب)</option>
                        <option value="zero">رصيد صفر</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">نوع المورد</label>
                    <select class="form-select" id="supplierTypeFilter">
                        <option value="">جميع الأنواع</option>
                    </select>
                </div>
            </div>

            <!-- فترة مخصصة -->
            <div class="row mt-3" id="customPeriodRow" style="display: none;">
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="fromDate">
                </div>
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="toDate">
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button class="btn btn-primary" id="applyFiltersBtn">
                        <i class="fas fa-search"></i> تطبيق الفلاتر
                    </button>
                    <button class="btn btn-secondary" id="clearFiltersBtn">
                        <i class="fas fa-times"></i> مسح الفلاتر
                    </button>
                    <span class="ms-3" id="filterStatus"></span>
                </div>
            </div>
        </div>

        <!-- معلومات الفلاتر المطبقة -->
        <div class="filter-info" id="appliedFilters" style="display: none;">
            <strong>الفلاتر المطبقة:</strong> <span id="appliedFiltersText"></span>
        </div>

        <!-- النتائج -->
        <div class="results-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5><i class="fas fa-table"></i> النتائج</h5>
                <div>
                    <span class="badge bg-primary" id="totalCount">0 مورد</span>
                    <span class="badge bg-success" id="creditorCount">0 دائن</span>
                    <span class="badge bg-danger" id="debtorCount">0 مدين</span>
                </div>
            </div>

            <!-- جدول النتائج -->
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>كود المورد</th>
                            <th>اسم المورد</th>
                            <th>نوع المورد</th>
                            <th>الرصيد</th>
                            <th>العملة</th>
                            <th>نوع الرصيد</th>
                            <th>آخر معاملة</th>
                        </tr>
                    </thead>
                    <tbody id="resultsTableBody">
                        <tr>
                            <td colspan="7" class="loading">
                                <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            console.log('🚀 تم تحميل النافذة المحسنة');

            // تحميل بيانات الفلاتر
            loadFilterData();

            // تحميل البيانات الأولية
            loadData();

            // ربط الأحداث
            setupEventListeners();

            // إعداد البحث الصوتي
            setupVoiceSearch();
        });

        function loadFilterData() {
            console.log('📋 تحميل بيانات الفلاتر...');
            
            // تحميل العملات
            $.get('/currencies/api/currencies')
                .done(function(response) {
                    if (response.success && response.currencies) {
                        const currencySelect = $('#currencyFilter');
                        response.currencies.forEach(function(currency) {
                            currencySelect.append(`<option value="${currency.code}">${currency.name_ar} (${currency.code})</option>`);
                        });
                        console.log(`✅ تم تحميل ${response.currencies.length} عملة`);
                    }
                })
                .fail(function() {
                    console.log('⚠️ فشل تحميل العملات، سيتم استخدام القيم الافتراضية');
                    $('#currencyFilter').append('<option value="USD">دولار أمريكي (USD)</option>');
                    $('#currencyFilter').append('<option value="SAR">ريال سعودي (SAR)</option>');
                });

            // تحميل أنواع الموردين
            $.get('/suppliers/api/supplier-types')
                .done(function(response) {
                    if (response.success && response.types) {
                        const typeSelect = $('#supplierTypeFilter');
                        response.types.forEach(function(type) {
                            typeSelect.append(`<option value="${type}">${type}</option>`);
                        });
                        console.log(`✅ تم تحميل ${response.types.length} نوع مورد`);
                    }
                })
                .fail(function() {
                    console.log('⚠️ فشل تحميل أنواع الموردين، سيتم استخدام القيم الافتراضية');
                    $('#supplierTypeFilter').append('<option value="مورد تجاري">مورد تجاري</option>');
                    $('#supplierTypeFilter').append('<option value="مورد خدمات">مورد خدمات</option>');
                });
        }

        function setupEventListeners() {
            console.log('🔗 ربط الأحداث...');
            
            // زر تطبيق الفلاتر
            $('#applyFiltersBtn').click(function() {
                console.log('🔍 تطبيق الفلاتر...');
                loadData();
            });

            // زر مسح الفلاتر
            $('#clearFiltersBtn').click(function() {
                console.log('🧹 مسح الفلاتر...');
                $('#currencyFilter').val('');
                $('#balanceStatusFilter').val('');
                $('#supplierTypeFilter').val('');
                $('#searchFilter').val('');
                $('#appliedFilters').hide();
                loadData();
            });

            // البحث السريع
            $('#searchFilter').on('input', function() {
                clearTimeout(window.searchTimeout);
                window.searchTimeout = setTimeout(function() {
                    console.log('🔍 بحث سريع...');
                    loadData();
                }, 500);
            });

            // تطبيق الفلاتر عند التغيير
            $('#currencyFilter, #balanceStatusFilter, #supplierTypeFilter, #periodFilter').change(function() {
                console.log('🔄 تغيير فلتر...');
                loadData();
            });

            // إظهار/إخفاء الفترة المخصصة
            $('#periodFilter').change(function() {
                if ($(this).val() === 'custom') {
                    $('#customPeriodRow').show();
                } else {
                    $('#customPeriodRow').hide();
                }
            });

            // تطبيق الفلاتر عند تغيير التواريخ المخصصة
            $('#fromDate, #toDate').change(function() {
                if ($('#periodFilter').val() === 'custom') {
                    loadData();
                }
            });
        }

        function loadData() {
            console.log('📊 تحميل البيانات...');
            
            // إظهار حالة التحميل
            $('#resultsTableBody').html('<tr><td colspan="7" class="loading"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</td></tr>');
            $('#filterStatus').html('<i class="fas fa-spinner fa-spin"></i> جاري التحميل...');

            // جمع معاملات الفلاتر
            const filters = {
                currency: $('#currencyFilter').val(),
                balance_status: $('#balanceStatusFilter').val(),
                supplier_type: $('#supplierTypeFilter').val(),
                search: $('#searchFilter').val().trim(),
                period: $('#periodFilter').val(),
                from_date: $('#fromDate').val(),
                to_date: $('#toDate').val()
            };

            console.log('🔍 الفلاتر المطبقة:', filters);

            // استدعاء API
            $.get('/suppliers/api/balances/simple', filters)
                .done(function(response) {
                    console.log('✅ تم استلام البيانات:', response);
                    
                    if (response.success) {
                        displayResults(response.data);
                        updateFilterInfo(filters, response.data.total);
                    } else {
                        showError('خطأ في جلب البيانات: ' + response.message);
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('❌ خطأ في API:', error, xhr.responseText);

                    let errorMessage = 'خطأ في الاتصال بالخادم';

                    if (xhr.status === 500) {
                        errorMessage = 'خطأ في الخادم - يرجى المحاولة لاحقاً';
                    } else if (xhr.status === 404) {
                        errorMessage = 'API غير موجود';
                    } else if (xhr.status === 0) {
                        errorMessage = 'لا يمكن الاتصال بالخادم - تأكد من تشغيل النظام';
                    }

                    showError(errorMessage);
                });
        }

        function displayResults(data) {
            console.log('📋 عرض النتائج...', data);

            // تحديث الإحصائيات
            $('#totalCount').text(data.total + ' مورد');
            $('#creditorCount').text(data.creditor_count + ' دائن');
            $('#debtorCount').text(data.debtor_count + ' مدين');

            // عرض النتائج في الجدول
            const tbody = $('#resultsTableBody');
            tbody.empty();

            // التحقق من وجود رسالة خطأ
            if (data.error_message) {
                tbody.html(`<tr><td colspan="7" class="text-center text-warning"><i class="fas fa-exclamation-triangle"></i> ${data.error_message}</td></tr>`);
                $('#filterStatus').html(`<i class="fas fa-exclamation-triangle text-warning"></i> ${data.error_message}`);
                return;
            }

            if (data.suppliers && data.suppliers.length > 0) {
                data.suppliers.forEach(function(supplier) {
                    const balanceAmount = parseFloat(supplier.balance_amount) || 0;
                    const balanceClass = balanceAmount > 0 ? 'balance-positive' :
                                       balanceAmount < 0 ? 'balance-negative' : 'balance-zero';

                    // تصحيح المحاسبة: الرصيد الموجب = مدين، الرصيد السالب = دائن
                    const balanceType = supplier.balance_type || (balanceAmount > 0 ? 'مدين' : balanceAmount < 0 ? 'دائن' : 'متوازن');

                    // ألوان مناسبة لنوع الرصيد
                    const badgeClass = balanceType === 'مدين' ? 'bg-warning' :
                                     balanceType === 'دائن' ? 'bg-info' : 'bg-secondary';

                    // الحصول على رمز العملة والفئة اللونية
                    const currencyInfo = getCurrencySymbolAndClass(supplier.currency || 'USD');

                    const row = `
                        <tr>
                            <td>${supplier.supplier_code || 'غير محدد'}</td>
                            <td>${supplier.name || 'غير محدد'}</td>
                            <td>${supplier.supplier_type || 'غير محدد'}</td>
                            <td class="${balanceClass}">${balanceAmount.toFixed(2)}</td>
                            <td><span class="${currencyInfo.class}">${currencyInfo.symbol}</span></td>
                            <td><span class="badge ${badgeClass}">${balanceType}</span></td>
                            <td>${supplier.last_transaction_date || 'غير متوفر'}</td>
                        </tr>
                    `;
                    tbody.append(row);
                });
            } else {
                tbody.html('<tr><td colspan="7" class="no-results"><i class="fas fa-search"></i><br>لا توجد أرصدة مطابقة للفلاتر المحددة</td></tr>');
            }

            $('#filterStatus').html(`<i class="fas fa-check text-success"></i> تم العثور على ${data.total} نتيجة`);
        }

        function updateFilterInfo(filters, total) {
            const appliedFilters = [];

            if (filters.search) appliedFilters.push(`البحث: "${filters.search}"`);
            if (filters.period) {
                const periodText = $('#periodFilter option:selected').text();
                appliedFilters.push(`الفترة: ${periodText}`);
            }
            if (filters.currency) appliedFilters.push(`العملة: ${filters.currency}`);
            if (filters.balance_status) appliedFilters.push(`الحالة: ${$('#balanceStatusFilter option:selected').text()}`);
            if (filters.supplier_type) appliedFilters.push(`النوع: ${filters.supplier_type}`);

            if (appliedFilters.length > 0) {
                $('#appliedFiltersText').text(appliedFilters.join(' | ') + ` (${total} نتيجة)`);
                $('#appliedFilters').show();
            } else {
                $('#appliedFilters').hide();
            }
        }

        function showError(message) {
            $('#resultsTableBody').html(`<tr><td colspan="7" class="text-center text-danger"><i class="fas fa-exclamation-triangle"></i> ${message}</td></tr>`);
            $('#filterStatus').html(`<i class="fas fa-exclamation-triangle text-danger"></i> ${message}`);
        }

        function getCurrencySymbolAndClass(currencyCode) {
            const currencies = {
                'USD': { symbol: '$', class: 'currency-usd' },
                'EUR': { symbol: '€', class: 'currency-eur' },
                'SAR': { symbol: 'ر.س', class: 'currency-sar' },
                'AED': { symbol: 'د.إ', class: 'currency-aed' },
                'CNY': { symbol: '¥', class: 'currency-cny' },
                'GBP': { symbol: '£', class: 'currency-default' },
                'JPY': { symbol: '¥', class: 'currency-default' },
                'KWD': { symbol: 'د.ك', class: 'currency-default' },
                'QAR': { symbol: 'ر.ق', class: 'currency-default' },
                'OMR': { symbol: 'ر.ع', class: 'currency-default' },
                'BHD': { symbol: 'د.ب', class: 'currency-default' },
                'JOD': { symbol: 'د.أ', class: 'currency-default' },
                'EGP': { symbol: 'ج.م', class: 'currency-default' }
            };

            return currencies[currencyCode] || { symbol: currencyCode, class: 'currency-default' };
        }

        function setupVoiceSearch() {
            console.log('🎤 إعداد البحث الصوتي...');

            // التحقق من دعم المتصفح للبحث الصوتي
            if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                $('#voiceSearchBtn').prop('disabled', true);
                $('#voiceStatus').text('البحث الصوتي غير مدعوم في هذا المتصفح');
                return;
            }

            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            const recognition = new SpeechRecognition();

            recognition.lang = 'ar-SA'; // العربية السعودية
            recognition.continuous = false;
            recognition.interimResults = false;

            $('#voiceSearchBtn').click(function() {
                console.log('🎤 بدء البحث الصوتي...');

                $('#micIcon').removeClass('fa-microphone').addClass('fa-microphone-slash text-danger');
                $('#voiceStatus').text('🎤 يتم الاستماع... تحدث الآن');

                recognition.start();
            });

            recognition.onresult = function(event) {
                const transcript = event.results[0][0].transcript;
                console.log('🎤 تم التعرف على الصوت:', transcript);

                $('#searchFilter').val(transcript);
                $('#voiceStatus').text(`تم التعرف على: "${transcript}"`);

                // تطبيق البحث تلقائياً
                loadData();
            };

            recognition.onerror = function(event) {
                console.error('❌ خطأ في البحث الصوتي:', event.error);
                $('#voiceStatus').text('خطأ في البحث الصوتي: ' + event.error);
            };

            recognition.onend = function() {
                $('#micIcon').removeClass('fa-microphone-slash text-danger').addClass('fa-microphone');

                setTimeout(function() {
                    $('#voiceStatus').text('');
                }, 3000);
            };
        }
    </script>
</body>
</html>
