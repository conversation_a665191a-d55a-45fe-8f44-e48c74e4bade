{% extends "base.html" %}

{% block title %}نظام الموافقات{% endblock %}

{% block extra_css %}
<style>
.priority-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.priority-urgent {
    background-color: #dc3545;
    color: white;
}

.priority-high {
    background-color: #fd7e14;
    color: white;
}

.priority-normal {
    background-color: #28a745;
    color: white;
}

.risk-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.risk-high {
    background-color: #dc3545;
    color: white;
}

.risk-medium {
    background-color: #ffc107;
    color: #212529;
}

.risk-low {
    background-color: #28a745;
    color: white;
}

.days-pending {
    font-weight: bold;
}

.days-pending.urgent {
    color: #dc3545;
}

.days-pending.warning {
    color: #fd7e14;
}

.approval-actions {
    white-space: nowrap;
}

.table-responsive {
    border-radius: 0.5rem;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.stats-card {
    border-radius: 1rem;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.bulk-actions {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #dee2e6;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        نظام الموافقات
                    </h1>
                    <p class="text-muted mb-0">إدارة الموافقة على طلبات الحوالات المعلقة</p>
                </div>
                <div>
                    <button class="btn btn-outline-primary" onclick="refreshData()">
                        <i class="fas fa-sync-alt me-2"></i>تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <div class="text-warning mb-2">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                    <h3 class="mb-1" id="pendingCount">-</h3>
                    <p class="text-muted mb-0">طلبات معلقة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <div class="text-danger mb-2">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                    <h3 class="mb-1" id="urgentCount">-</h3>
                    <p class="text-muted mb-0">طلبات عاجلة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <div class="text-info mb-2">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                    <h3 class="mb-1" id="totalAmount">-</h3>
                    <p class="text-muted mb-0">إجمالي المبالغ</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <div class="text-secondary mb-2">
                        <i class="fas fa-calendar-day fa-2x"></i>
                    </div>
                    <h3 class="mb-1" id="avgDays">-</h3>
                    <p class="text-muted mb-0">متوسط أيام الانتظار</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="row mb-4" id="bulkActionsRow" style="display: none;">
        <div class="col-12">
            <div class="bulk-actions">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong id="selectedCount">0</strong> طلب محدد
                    </div>
                    <div>
                        <button class="btn btn-success btn-sm me-2" onclick="bulkApprove()">
                            <i class="fas fa-check me-1"></i>موافقة جماعية
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                            <i class="fas fa-times me-1"></i>إلغاء التحديد
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Requests Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>الطلبات المعلقة للموافقة
                        </h5>
                        <div class="d-flex gap-2">
                            <select class="form-select form-select-sm" id="priorityFilter" onchange="filterRequests()">
                                <option value="">جميع الأولويات</option>
                                <option value="urgent">عاجل</option>
                                <option value="high">مرتفع</option>
                                <option value="normal">عادي</option>
                            </select>
                            <select class="form-select form-select-sm" id="riskFilter" onchange="filterRequests()">
                                <option value="">جميع المخاطر</option>
                                <option value="high">مخاطر عالية</option>
                                <option value="medium">مخاطر متوسطة</option>
                                <option value="low">مخاطر منخفضة</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>رقم الطلب</th>
                                    <th>المستفيد</th>
                                    <th>المبلغ</th>
                                    <th>الغرض</th>
                                    <th>الفرع</th>
                                    <th>الأولوية</th>
                                    <th>المخاطر</th>
                                    <th>أيام الانتظار</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="requestsTableBody">
                                <tr>
                                    <td colspan="10" class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <p class="mt-2 mb-0">جاري تحميل الطلبات...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    موافقة على الطلب
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>رقم الطلب:</strong> <span id="approvalRequestNumber"></span>
                </div>
                <div class="mb-3">
                    <strong>المستفيد:</strong> <span id="approvalBeneficiary"></span>
                </div>
                <div class="mb-3">
                    <strong>المبلغ:</strong> <span id="approvalAmount"></span>
                </div>
                <div class="mb-3">
                    <label for="approvalComment" class="form-label">تعليق الموافقة (اختياري)</label>
                    <textarea class="form-control" id="approvalComment" rows="3"
                              placeholder="أضف تعليق على الموافقة..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="confirmApproval()">
                    <i class="fas fa-check me-2"></i>تأكيد الموافقة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div class="modal fade" id="rejectionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-times-circle text-danger me-2"></i>
                    رفض الطلب
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>رقم الطلب:</strong> <span id="rejectionRequestNumber"></span>
                </div>
                <div class="mb-3">
                    <strong>المستفيد:</strong> <span id="rejectionBeneficiary"></span>
                </div>
                <div class="mb-3">
                    <strong>المبلغ:</strong> <span id="rejectionAmount"></span>
                </div>
                <div class="mb-3">
                    <label for="rejectionReason" class="form-label required">سبب الرفض *</label>
                    <textarea class="form-control" id="rejectionReason" rows="3"
                              placeholder="يرجى توضيح سبب رفض الطلب..." required></textarea>
                    <div class="form-text">سبب الرفض مطلوب ولا يمكن تركه فارغاً</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" onclick="confirmRejection()">
                    <i class="fas fa-times me-2"></i>تأكيد الرفض
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Approval Modal -->
<div class="modal fade" id="bulkApprovalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-check-double text-success me-2"></i>
                    موافقة جماعية
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>عدد الطلبات المحددة:</strong> <span id="bulkSelectedCount"></span>
                </div>
                <div class="mb-3">
                    <label for="bulkApprovalComment" class="form-label">تعليق الموافقة الجماعية (اختياري)</label>
                    <textarea class="form-control" id="bulkApprovalComment" rows="3"
                              placeholder="أضف تعليق على الموافقة الجماعية..."></textarea>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    سيتم اعتماد جميع الطلبات المحددة بنفس التعليق
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="confirmBulkApproval()">
                    <i class="fas fa-check-double me-2"></i>تأكيد الموافقة الجماعية
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
let pendingRequests = [];
let selectedRequests = [];
let currentRequestId = null;

$(document).ready(function() {
    loadPendingRequests();
});

// تحميل الطلبات المعلقة
function loadPendingRequests() {
    console.log('🔄 تحميل الطلبات المعلقة...');

    $.get('/transfers/api/pending-requests')
        .done(function(response) {
            if (response.success) {
                pendingRequests = response.data;
                displayRequests(pendingRequests);
                updateStatistics();
                console.log(`✅ تم تحميل ${pendingRequests.length} طلب معلق`);
            } else {
                showError('فشل في تحميل الطلبات المعلقة');
            }
        })
        .fail(function() {
            showError('حدث خطأ أثناء تحميل الطلبات');
        });
}

// عرض الطلبات في الجدول
function displayRequests(requests) {
    const tbody = $('#requestsTableBody');
    tbody.empty();

    if (requests.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="10" class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5>لا توجد طلبات معلقة</h5>
                    <p class="text-muted">جميع الطلبات تم معالجتها</p>
                </td>
            </tr>
        `);
        return;
    }

    requests.forEach(function(request) {
        const row = createRequestRow(request);
        tbody.append(row);
    });
}

// إنشاء صف طلب
function createRequestRow(request) {
    const priorityClass = {
        'urgent': 'priority-urgent',
        'high': 'priority-high',
        'normal': 'priority-normal'
    }[request.priority_level] || 'priority-normal';

    const riskClass = {
        'high': 'risk-high',
        'medium': 'risk-medium',
        'low': 'risk-low'
    }[request.risk_level] || 'risk-low';

    const daysClass = request.days_pending > 7 ? 'urgent' :
                     request.days_pending > 3 ? 'warning' : '';

    const priorityText = {
        'urgent': 'عاجل',
        'high': 'مرتفع',
        'normal': 'عادي'
    }[request.priority_level] || 'عادي';

    const riskText = {
        'high': 'عالية',
        'medium': 'متوسطة',
        'low': 'منخفضة'
    }[request.risk_level] || 'منخفضة';

    return `
        <tr data-request-id="${request.id}">
            <td>
                <input type="checkbox" class="request-checkbox" value="${request.id}"
                       onchange="updateSelection()">
            </td>
            <td>
                <strong>${request.request_number}</strong>
                <br><small class="text-muted">${formatDate(request.created_at)}</small>
            </td>
            <td>
                <div>${request.beneficiary_name}</div>
                <small class="text-muted">${request.bank_name}</small>
            </td>
            <td>
                <strong>${formatAmount(request.amount)} ${request.currency}</strong>
            </td>
            <td>
                <span class="text-truncate" style="max-width: 150px; display: inline-block;"
                      title="${request.purpose}">${request.purpose}</span>
            </td>
            <td>${request.branch_name}</td>
            <td>
                <span class="badge ${priorityClass}">${priorityText}</span>
            </td>
            <td>
                <span class="badge ${riskClass}">${riskText}</span>
            </td>
            <td>
                <span class="days-pending ${daysClass}">${request.days_pending} يوم</span>
            </td>
            <td class="approval-actions">
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-outline-primary"
                            onclick="viewRequestDetails(${request.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-success"
                            onclick="showApprovalModal(${request.id})" title="موافقة">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn btn-sm btn-danger"
                            onclick="showRejectionModal(${request.id})" title="رفض">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </td>
        </tr>
    `;
}

// تحديث الإحصائيات
function updateStatistics() {
    const totalPending = pendingRequests.length;
    const urgentCount = pendingRequests.filter(r => r.priority_level === 'urgent').length;
    const totalAmount = pendingRequests.reduce((sum, r) => sum + r.amount, 0);
    const avgDays = totalPending > 0 ?
        Math.round(pendingRequests.reduce((sum, r) => sum + r.days_pending, 0) / totalPending) : 0;

    $('#pendingCount').text(totalPending);
    $('#urgentCount').text(urgentCount);
    $('#totalAmount').text(formatAmount(totalAmount));
    $('#avgDays').text(avgDays);
}

// عرض modal الموافقة
function showApprovalModal(requestId) {
    const request = pendingRequests.find(r => r.id === requestId);
    if (!request) return;

    currentRequestId = requestId;
    $('#approvalRequestNumber').text(request.request_number);
    $('#approvalBeneficiary').text(request.beneficiary_name);
    $('#approvalAmount').text(`${formatAmount(request.amount)} ${request.currency}`);
    $('#approvalComment').val('');

    $('#approvalModal').modal('show');
}

// عرض modal الرفض
function showRejectionModal(requestId) {
    const request = pendingRequests.find(r => r.id === requestId);
    if (!request) return;

    currentRequestId = requestId;
    $('#rejectionRequestNumber').text(request.request_number);
    $('#rejectionBeneficiary').text(request.beneficiary_name);
    $('#rejectionAmount').text(`${formatAmount(request.amount)} ${request.currency}`);
    $('#rejectionReason').val('');

    $('#rejectionModal').modal('show');
}

// تأكيد الموافقة
function confirmApproval() {
    if (!currentRequestId) return;

    const comment = $('#approvalComment').val().trim();

    $.post(`/transfers/api/approve-request/${currentRequestId}`, {
        comment: comment
    })
    .done(function(response) {
        if (response.success) {
            showSuccess(response.message);
            $('#approvalModal').modal('hide');
            loadPendingRequests(); // إعادة تحميل القائمة
        } else {
            showError(response.message);
        }
    })
    .fail(function(xhr, status, error) {
        console.error('خطأ في الموافقة:', xhr.responseText);
        let errorMessage = 'حدث خطأ أثناء الموافقة على الطلب';

        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage = xhr.responseJSON.message;
        } else if (xhr.responseText) {
            try {
                const response = JSON.parse(xhr.responseText);
                if (response.message) {
                    errorMessage = response.message;
                }
            } catch (e) {
                // استخدام الرسالة الافتراضية
            }
        }

        showError(errorMessage);
    });
}

// تأكيد الرفض
function confirmRejection() {
    if (!currentRequestId) return;

    const reason = $('#rejectionReason').val().trim();

    if (!reason) {
        showError('سبب الرفض مطلوب');
        return;
    }

    $.post(`/transfers/api/reject-request/${currentRequestId}`, {
        reason: reason
    })
    .done(function(response) {
        if (response.success) {
            showSuccess(response.message);
            $('#rejectionModal').modal('hide');
            loadPendingRequests(); // إعادة تحميل القائمة
        } else {
            showError(response.message);
        }
    })
    .fail(function(xhr, status, error) {
        console.error('خطأ في الرفض:', xhr.responseText);
        let errorMessage = 'حدث خطأ أثناء رفض الطلب';

        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage = xhr.responseJSON.message;
        } else if (xhr.responseText) {
            try {
                const response = JSON.parse(xhr.responseText);
                if (response.message) {
                    errorMessage = response.message;
                }
            } catch (e) {
                // استخدام الرسالة الافتراضية
            }
        }

        showError(errorMessage);
    });
}

// إدارة التحديد
function updateSelection() {
    selectedRequests = [];
    $('.request-checkbox:checked').each(function() {
        selectedRequests.push(parseInt($(this).val()));
    });

    $('#selectedCount').text(selectedRequests.length);

    if (selectedRequests.length > 0) {
        $('#bulkActionsRow').show();
    } else {
        $('#bulkActionsRow').hide();
    }

    // تحديث حالة "تحديد الكل"
    const totalCheckboxes = $('.request-checkbox').length;
    const checkedCheckboxes = $('.request-checkbox:checked').length;

    $('#selectAll').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
    $('#selectAll').prop('checked', checkedCheckboxes === totalCheckboxes && totalCheckboxes > 0);
}

// تحديد/إلغاء تحديد الكل
function toggleSelectAll() {
    const isChecked = $('#selectAll').prop('checked');
    $('.request-checkbox').prop('checked', isChecked);
    updateSelection();
}

// إلغاء التحديد
function clearSelection() {
    $('.request-checkbox').prop('checked', false);
    $('#selectAll').prop('checked', false);
    updateSelection();
}

// موافقة جماعية
function bulkApprove() {
    if (selectedRequests.length === 0) {
        showError('لم يتم تحديد أي طلبات');
        return;
    }

    $('#bulkSelectedCount').text(selectedRequests.length);
    $('#bulkApprovalComment').val('');
    $('#bulkApprovalModal').modal('show');
}

// تأكيد الموافقة الجماعية
function confirmBulkApproval() {
    if (selectedRequests.length === 0) return;

    const comment = $('#bulkApprovalComment').val().trim();

    $.post('/transfers/api/bulk-approve', {
        request_ids: selectedRequests,
        comment: comment
    })
    .done(function(response) {
        if (response.success) {
            showSuccess(`${response.message}. تم اعتماد ${response.approved_count} طلب`);
            $('#bulkApprovalModal').modal('hide');
            clearSelection();
            loadPendingRequests(); // إعادة تحميل القائمة

            if (response.failed_requests && response.failed_requests.length > 0) {
                console.warn('طلبات فشلت:', response.failed_requests);
            }
        } else {
            showError(response.message);
        }
    })
    .fail(function() {
        showError('حدث خطأ أثناء الموافقة الجماعية');
    });
}

// فلترة الطلبات
function filterRequests() {
    const priorityFilter = $('#priorityFilter').val();
    const riskFilter = $('#riskFilter').val();

    let filteredRequests = pendingRequests;

    if (priorityFilter) {
        filteredRequests = filteredRequests.filter(r => r.priority_level === priorityFilter);
    }

    if (riskFilter) {
        filteredRequests = filteredRequests.filter(r => r.risk_level === riskFilter);
    }

    displayRequests(filteredRequests);
}

// تحديث البيانات
function refreshData() {
    clearSelection();
    loadPendingRequests();
}

// عرض تفاصيل الطلب
function viewRequestDetails(requestId) {
    // يمكن ربطها بنافذة تفاصيل الطلب الموجودة
    window.open(`/transfers/edit-request/${requestId}`, '_blank');
}

// دوال مساعدة
function formatAmount(amount) {
    return new Intl.NumberFormat('ar-SA').format(amount);
}

function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function showSuccess(message) {
    // يمكن استخدام نظام الإشعارات الموجود
    alert(message); // مؤقت
}

function showError(message) {
    // يمكن استخدام نظام الإشعارات الموجود
    alert(message); // مؤقت
}
</script>
{% endblock %}
