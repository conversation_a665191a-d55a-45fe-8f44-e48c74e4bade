-- =====================================================
-- جدول تفاصيل توزيع الحوالات على الموردين
-- Transfer Supplier Distributions Table
-- =====================================================

-- 1. إنشاء جدول تفاصيل توزيع الحوالات على الموردين
CREATE TABLE transfer_supplier_distributions (
    id NUMBER PRIMARY KEY,
    transfer_id NUMBER NOT NULL,
    supplier_id NUMBER NOT NULL,
    amount NUMBER(15,2) NOT NULL,
    currency_code VARCHAR2(10) NOT NULL DEFAULT 'SAR',
    exchange_rate NUMBER(15,6) DEFAULT 1.000000,
    base_currency_amount NUMBER(15,2) GENERATED ALWAYS AS (amount * exchange_rate),
    
    -- معلومات إضافية
    percentage_of_total NUMBER(5,2), -- نسبة من إجمالي الحوالة
    notes VARCHAR2(500),
    
    -- معلومات التتبع
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_at TIMESTAMP,
    updated_by NUMBER,
    
    -- القيود
    CONSTRAINT fk_tsd_transfer FOREIGN KEY (transfer_id) REFERENCES transfers(id),
    CONSTRAINT fk_tsd_supplier FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    CONSTRAINT chk_tsd_amount CHECK (amount > 0),
    CONSTRAINT chk_tsd_percentage CHECK (percentage_of_total >= 0 AND percentage_of_total <= 100),
    CONSTRAINT uk_tsd_unique UNIQUE (transfer_id, supplier_id)
);

-- 2. إنشاء sequence للـ ID
CREATE SEQUENCE transfer_supplier_distributions_seq 
START WITH 1 
INCREMENT BY 1 
NOCACHE;

-- 3. إنشاء trigger للـ ID التلقائي
CREATE OR REPLACE TRIGGER tsd_id_trigger
    BEFORE INSERT OR UPDATE ON transfer_supplier_distributions
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        IF :NEW.id IS NULL THEN
            :NEW.id := transfer_supplier_distributions_seq.NEXTVAL;
        END IF;
        :NEW.created_at := CURRENT_TIMESTAMP;
    END IF;
    
    -- تحديث وقت التعديل
    :NEW.updated_at := CURRENT_TIMESTAMP;
    
    -- حساب النسبة المئوية إذا لم تكن محددة
    IF :NEW.percentage_of_total IS NULL THEN
        DECLARE
            v_total_amount NUMBER(15,2);
        BEGIN
            SELECT amount INTO v_total_amount
            FROM transfers
            WHERE id = :NEW.transfer_id;
            
            IF v_total_amount > 0 THEN
                :NEW.percentage_of_total := ROUND((:NEW.amount / v_total_amount) * 100, 2);
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                :NEW.percentage_of_total := 0;
        END;
    END IF;
END;
/

-- 4. إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_tsd_transfer_id ON transfer_supplier_distributions(transfer_id);
CREATE INDEX idx_tsd_supplier_id ON transfer_supplier_distributions(supplier_id);
CREATE INDEX idx_tsd_currency ON transfer_supplier_distributions(currency_code);
CREATE INDEX idx_tsd_amount ON transfer_supplier_distributions(amount);
CREATE INDEX idx_tsd_created_at ON transfer_supplier_distributions(created_at);

-- 5. إنشاء view لعرض التفاصيل مع أسماء الموردين
CREATE OR REPLACE VIEW transfer_supplier_distributions_view AS
SELECT 
    tsd.*,
    s.name as supplier_name,
    s.code as supplier_code,
    s.contact_person as supplier_contact,
    t.request_number as transfer_number,
    t.status as transfer_status,
    t.executed_at as transfer_executed_at,
    t.beneficiary_name as transfer_beneficiary
FROM transfer_supplier_distributions tsd
JOIN suppliers s ON tsd.supplier_id = s.id
JOIN transfers t ON tsd.transfer_id = t.id;

-- 6. إنشاء trigger للتحقق من تطابق المبالغ
CREATE OR REPLACE TRIGGER tsd_amount_validation_trigger
    AFTER INSERT OR UPDATE OR DELETE ON transfer_supplier_distributions
    FOR EACH ROW
DECLARE
    v_transfer_id NUMBER;
    v_total_distributed NUMBER(15,2) := 0;
    v_transfer_amount NUMBER(15,2) := 0;
    v_difference NUMBER(15,2);
BEGIN
    -- تحديد معرف الحوالة
    IF INSERTING OR UPDATING THEN
        v_transfer_id := :NEW.transfer_id;
    ELSIF DELETING THEN
        v_transfer_id := :OLD.transfer_id;
    END IF;
    
    -- حساب إجمالي المبالغ الموزعة
    SELECT NVL(SUM(amount), 0) INTO v_total_distributed
    FROM transfer_supplier_distributions
    WHERE transfer_id = v_transfer_id;
    
    -- الحصول على مبلغ الحوالة الأصلي
    SELECT amount INTO v_transfer_amount
    FROM transfers
    WHERE id = v_transfer_id;
    
    -- حساب الفرق
    v_difference := ABS(v_total_distributed - v_transfer_amount);
    
    -- تحديث حالة التوزيع في جدول الحوالات
    UPDATE transfers SET
        total_distributed_amount = v_total_distributed,
        distribution_variance = v_difference,
        is_distribution_complete = CASE 
            WHEN v_difference <= 0.01 THEN 1 
            ELSE 0 
        END,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = v_transfer_id;
    
EXCEPTION
    WHEN OTHERS THEN
        -- تسجيل الخطأ دون إيقاف العملية
        NULL;
END;
/

-- 7. إضافة أعمدة جديدة لجدول transfers إذا لم تكن موجودة
BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE transfers ADD (
        total_distributed_amount NUMBER(15,2) DEFAULT 0,
        distribution_variance NUMBER(15,2) DEFAULT 0,
        is_distribution_complete NUMBER(1) DEFAULT 0,
        supplier_distributions_count NUMBER DEFAULT 0
    )';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN -- Column already exists
            RAISE;
        END IF;
END;
/

-- 8. إنشاء trigger لتحديث عدد التوزيعات
CREATE OR REPLACE TRIGGER tsd_count_trigger
    AFTER INSERT OR DELETE ON transfer_supplier_distributions
    FOR EACH ROW
DECLARE
    v_transfer_id NUMBER;
    v_count NUMBER;
BEGIN
    -- تحديد معرف الحوالة
    IF INSERTING THEN
        v_transfer_id := :NEW.transfer_id;
    ELSIF DELETING THEN
        v_transfer_id := :OLD.transfer_id;
    END IF;
    
    -- حساب عدد التوزيعات
    SELECT COUNT(*) INTO v_count
    FROM transfer_supplier_distributions
    WHERE transfer_id = v_transfer_id;
    
    -- تحديث العدد في جدول الحوالات
    UPDATE transfers SET
        supplier_distributions_count = v_count,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = v_transfer_id;
    
EXCEPTION
    WHEN OTHERS THEN
        NULL;
END;
/

-- 9. إنشاء view لملخص التوزيعات حسب الحوالة
CREATE OR REPLACE VIEW transfer_distributions_summary AS
SELECT 
    t.id as transfer_id,
    t.request_number,
    t.amount as transfer_amount,
    t.currency,
    t.status,
    COUNT(tsd.id) as distributions_count,
    SUM(tsd.amount) as total_distributed,
    t.amount - NVL(SUM(tsd.amount), 0) as remaining_amount,
    CASE 
        WHEN ABS(t.amount - NVL(SUM(tsd.amount), 0)) <= 0.01 THEN 'مكتمل'
        WHEN NVL(SUM(tsd.amount), 0) = 0 THEN 'غير موزع'
        ELSE 'جزئي'
    END as distribution_status,
    ROUND((NVL(SUM(tsd.amount), 0) / t.amount) * 100, 2) as completion_percentage
FROM transfers t
LEFT JOIN transfer_supplier_distributions tsd ON t.id = tsd.transfer_id
GROUP BY t.id, t.request_number, t.amount, t.currency, t.status;

-- 10. إنشاء view لملخص التوزيعات حسب المورد
CREATE OR REPLACE VIEW supplier_distributions_summary AS
SELECT 
    s.id as supplier_id,
    s.name as supplier_name,
    s.code as supplier_code,
    tsd.currency_code,
    COUNT(tsd.id) as transfers_count,
    SUM(tsd.amount) as total_amount,
    AVG(tsd.amount) as average_amount,
    MIN(tsd.amount) as min_amount,
    MAX(tsd.amount) as max_amount,
    MIN(tsd.created_at) as first_distribution_date,
    MAX(tsd.created_at) as last_distribution_date
FROM suppliers s
JOIN transfer_supplier_distributions tsd ON s.id = tsd.supplier_id
JOIN transfers t ON tsd.transfer_id = t.id
WHERE t.status = 'executed'
GROUP BY s.id, s.name, s.code, tsd.currency_code;

-- 11. إنشاء إجراء لحذف التوزيعات (مع التحقق)
CREATE OR REPLACE PROCEDURE DELETE_TRANSFER_DISTRIBUTIONS(
    p_transfer_id IN NUMBER,
    p_user_id IN NUMBER DEFAULT 1
) AS
    v_transfer_status VARCHAR2(50);
BEGIN
    -- التحقق من حالة الحوالة
    SELECT status INTO v_transfer_status
    FROM transfers
    WHERE id = p_transfer_id;
    
    IF v_transfer_status = 'executed' THEN
        RAISE_APPLICATION_ERROR(-20001, 'لا يمكن حذف توزيعات حوالة منفذة');
    END IF;
    
    -- حذف التوزيعات
    DELETE FROM transfer_supplier_distributions
    WHERE transfer_id = p_transfer_id;
    
    COMMIT;
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RAISE_APPLICATION_ERROR(-20002, 'الحوالة غير موجودة');
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END;
/

-- 12. إنشاء comments للتوثيق
COMMENT ON TABLE transfer_supplier_distributions IS 'جدول تفاصيل توزيع الحوالات على الموردين';
COMMENT ON COLUMN transfer_supplier_distributions.id IS 'المعرف الفريد للتوزيع';
COMMENT ON COLUMN transfer_supplier_distributions.transfer_id IS 'معرف الحوالة';
COMMENT ON COLUMN transfer_supplier_distributions.supplier_id IS 'معرف المورد';
COMMENT ON COLUMN transfer_supplier_distributions.amount IS 'المبلغ المخصص للمورد';
COMMENT ON COLUMN transfer_supplier_distributions.currency_code IS 'رمز العملة';
COMMENT ON COLUMN transfer_supplier_distributions.exchange_rate IS 'سعر الصرف';
COMMENT ON COLUMN transfer_supplier_distributions.base_currency_amount IS 'المبلغ بالعملة الأساسية (محسوب تلقائياً)';
COMMENT ON COLUMN transfer_supplier_distributions.percentage_of_total IS 'النسبة المئوية من إجمالي الحوالة';
COMMENT ON COLUMN transfer_supplier_distributions.notes IS 'ملاحظات إضافية';

-- إنهاء السكريبت
COMMIT;

-- عرض رسالة نجاح
SELECT 'تم إنشاء جدول transfer_supplier_distributions بنجاح' as result FROM DUAL;
