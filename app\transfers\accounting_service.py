"""
خدمة الترحيل المحاسبي للحوالات
Transfer Accounting Service
"""

import json
import logging
from typing import List, Dict, Any, Optional
from decimal import Decimal
from datetime import datetime
import oracledb
from flask import request, session

from app.utils.exceptions import ValidationError, DatabaseError

logger = logging.getLogger(__name__)

def get_oracle_connection():
    """الحصول على اتصال Oracle باستخدام oracledb"""
    try:
        # إعدادات الاتصال
        dsn = "localhost:1521/orcl"
        username = "accounting_user"
        password = "accounting_password"

        # إنشاء الاتصال
        connection = oracledb.connect(
            user=username,
            password=password,
            dsn=dsn
        )

        return connection

    except Exception as e:
        logger.error(f"خطأ في الاتصال بـ Oracle: {str(e)}")
        raise DatabaseError(f"فشل في الاتصال بقاعدة البيانات: {str(e)}")

class TransferAccountingService:
    """خدمة الترحيل المحاسبي للحوالات"""

    def __init__(self):
        self.db = get_oracle_connection()

    def _get_user_context(self) -> Dict[str, Any]:
        """الحصول على معلومات المستخدم والجلسة"""
        return {
            'user_id': session.get('user_id', 1),
            'ip_address': request.environ.get('REMOTE_ADDR'),
            'user_agent': request.environ.get('HTTP_USER_AGENT'),
            'session_id': session.get('session_id')
        }
    
    def execute_transfer(self, transfer_id: int, money_changer_id: int,
                        total_amount: Decimal, currency_code: str,
                        supplier_distributions: List[Dict[str, Any]],
                        user_id: int = None) -> Dict[str, Any]:
        """
        تنفيذ الحوالة وترحيل الأرصدة

        Args:
            transfer_id: معرف الحوالة
            money_changer_id: معرف الصراف/البنك
            total_amount: المبلغ الإجمالي
            currency_code: رمز العملة
            supplier_distributions: قائمة توزيعات الموردين
            user_id: معرف المستخدم (اختياري)

        Returns:
            Dict مع نتيجة العملية
        """
        try:
            # الحصول على معلومات المستخدم والجلسة
            user_context = self._get_user_context()
            if user_id is None:
                user_id = user_context['user_id']

            # التحقق من صحة البيانات
            self._validate_transfer_data(transfer_id, money_changer_id,
                                       total_amount, supplier_distributions)

            # تحويل توزيعات الموردين إلى JSON
            distributions_json = json.dumps([{
                'supplier_id': dist['supplier_id'],
                'amount': float(dist['amount'])
            } for dist in supplier_distributions])

            # إعداد المعاملات للإجراء المخزن
            cursor = self.db.cursor()

            try:
                # استدعاء الإجراء المخزن
                cursor.callproc('EXECUTE_TRANSFER_ACCOUNTING', [
                    transfer_id,
                    money_changer_id,
                    float(total_amount),
                    currency_code,
                    distributions_json,
                    user_id
                ])

                self.db.commit()

                logger.info(f"تم تنفيذ الحوالة {transfer_id} بنجاح بواسطة المستخدم {user_id}")

                return {
                    'success': True,
                    'message': 'تم تنفيذ الحوالة وترحيل الأرصدة بنجاح',
                    'transfer_id': transfer_id,
                    'total_amount': float(total_amount),
                    'suppliers_count': len(supplier_distributions),
                    'money_changer_id': money_changer_id,
                    'currency': currency_code
                }

            finally:
                cursor.close()

        except oracledb.DatabaseError as e:
            error_message = str(e)
            logger.error(f"خطأ في قاعدة البيانات عند تنفيذ الحوالة {transfer_id}: {error_message}")

            # استخراج رسالة الخطأ المخصصة من Oracle
            if 'ORA-20001' in error_message:
                raise ValidationError("بيانات الحوالة غير صحيحة")
            elif 'ORA-20002' in error_message:
                raise ValidationError("الحوالة غير موجودة أو غير معتمدة")
            elif 'ORA-20004' in error_message:
                raise ValidationError("مجموع التوزيعات لا يطابق مبلغ الحوالة")
            elif 'ORA-20005' in error_message:
                raise ValidationError("رصيد الصراف غير كافي")
            else:
                raise DatabaseError(f"فشل في تنفيذ الحوالة: {error_message}")

        except Exception as e:
            logger.error(f"خطأ غير متوقع في تنفيذ الحوالة {transfer_id}: {str(e)}")
            raise DatabaseError(f"فشل في تنفيذ الحوالة: {str(e)}")
    
    def cancel_transfer(self, transfer_id: int, user_id: int = None,
                       cancellation_reason: str = None) -> Dict[str, Any]:
        """
        إلغاء الحوالة وعكس الترحيلات

        Args:
            transfer_id: معرف الحوالة
            user_id: معرف المستخدم (اختياري)
            cancellation_reason: سبب الإلغاء (اختياري)

        Returns:
            Dict مع نتيجة العملية
        """
        try:
            # الحصول على معلومات المستخدم والجلسة
            user_context = self._get_user_context()
            if user_id is None:
                user_id = user_context['user_id']

            # التحقق من إمكانية الإلغاء
            cancellation_check = self.check_cancellation_eligibility(transfer_id)
            if not cancellation_check['can_cancel']:
                raise ValidationError(cancellation_check['message'])

            # التحقق من وجود الحوالة وحالتها
            transfer_info = self._get_transfer_info(transfer_id)
            if not transfer_info:
                raise ValidationError("الحوالة غير موجودة")

            if transfer_info['status'] != 'executed':
                raise ValidationError("لا يمكن إلغاء حوالة غير منفذة")

            # إعداد المعاملات للإجراء المخزن
            cursor = self.db.cursor()

            try:
                # استدعاء الإجراء المخزن للإلغاء
                cursor.callproc('CANCEL_TRANSFER_ACCOUNTING', [
                    transfer_id,
                    user_id,
                    user_context['ip_address'],
                    user_context['session_id'],
                    cancellation_reason
                ])

                self.db.commit()

                logger.info(f"تم إلغاء الحوالة {transfer_id} بنجاح بواسطة المستخدم {user_id}")

                return {
                    'success': True,
                    'message': 'تم إلغاء الحوالة وعكس جميع الترحيلات بنجاح',
                    'transfer_id': transfer_id,
                    'cancelled_amount': float(transfer_info['amount']),
                    'currency': transfer_info['currency'],
                    'cancellation_reason': cancellation_reason,
                    'warnings': cancellation_check.get('warnings', [])
                }

            finally:
                cursor.close()

        except oracledb.DatabaseError as e:
            error_message = str(e)
            logger.error(f"خطأ في قاعدة البيانات عند إلغاء الحوالة {transfer_id}: {error_message}")

            # استخراج رسالة الخطأ المخصصة من Oracle
            if 'ORA-20001' in error_message:
                raise ValidationError("الحوالة غير موجودة")
            elif 'ORA-20002' in error_message:
                raise ValidationError("لا يمكن إلغاء حوالة غير منفذة")
            elif 'ORA-20003' in error_message:
                raise ValidationError("رصيد غير كافي لإتمام الإلغاء")
            else:
                raise DatabaseError(f"فشل في إلغاء الحوالة: {error_message}")

        except Exception as e:
            logger.error(f"خطأ غير متوقع في إلغاء الحوالة {transfer_id}: {str(e)}")
            raise DatabaseError(f"فشل في إلغاء الحوالة: {str(e)}")

    def check_cancellation_eligibility(self, transfer_id: int) -> Dict[str, Any]:
        """
        التحقق من إمكانية إلغاء الحوالة

        Args:
            transfer_id: معرف الحوالة

        Returns:
            Dict مع نتيجة التحقق
        """
        try:
            cursor = self.db.cursor()

            # استدعاء الدالة باستخدام oracledb
            cursor.execute("SELECT CAN_CANCEL_TRANSFER(:1) FROM DUAL", [transfer_id])
            result = cursor.fetchone()[0]

            cursor.close()

            if result.startswith('OK:'):
                return {
                    'can_cancel': True,
                    'message': result[3:].strip(),
                    'warnings': []
                }
            elif result.startswith('WARNING:'):
                return {
                    'can_cancel': True,
                    'message': result[8:].strip(),
                    'warnings': [result[8:].strip()]
                }
            else:  # ERROR:
                # تنظيف النص وضمان الترميز الصحيح
                error_message = result[6:].strip() if result.startswith('ERROR:') else result

                # إذا كانت الرسالة تحتوي على نص مُرمز بشكل خاطئ، استبدلها برسالة واضحة
                if 'ط§ظ„ط' in error_message or 'ظ…ظˆط¬ظˆط¯' in error_message:
                    error_message = "الحوالة غير موجودة"

                return {
                    'can_cancel': False,
                    'message': error_message,
                    'warnings': []
                }

        except Exception as e:
            logger.error(f"خطأ في التحقق من إمكانية إلغاء الحوالة {transfer_id}: {str(e)}")
            return {
                'can_cancel': False,
                'message': f"خطأ في التحقق: {str(e)}",
                'warnings': []
            }

    def get_transfer_accounting_details(self, transfer_id: int) -> Dict[str, Any]:
        """
        الحصول على تفاصيل الترحيل المحاسبي للحوالة
        
        Args:
            transfer_id: معرف الحوالة
            
        Returns:
            Dict مع تفاصيل الترحيل
        """
        try:
            # معلومات الحوالة الأساسية
            transfer_info = self._get_transfer_info(transfer_id)
            if not transfer_info:
                raise ValidationError("الحوالة غير موجودة")
            
            # توزيعات الموردين
            supplier_distributions = self._get_supplier_distributions(transfer_id)
            
            # سجل الأنشطة
            activity_log = self._get_activity_log(transfer_id)
            
            # تأثير على الأرصدة
            balance_changes = self._get_balance_changes(transfer_id)
            
            return {
                'transfer_info': transfer_info,
                'supplier_distributions': supplier_distributions,
                'activity_log': activity_log,
                'balance_changes': balance_changes
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على تفاصيل الحوالة {transfer_id}: {str(e)}")
            raise DatabaseError(f"فشل في الحصول على التفاصيل: {str(e)}")
    
    def get_entity_balance(self, entity_type: str, entity_id: int, 
                          currency_code: str = 'SAR') -> Dict[str, Any]:
        """
        الحصول على رصيد كيان معين
        
        Args:
            entity_type: نوع الكيان (SUPPLIER, MONEY_CHANGER, BANK)
            entity_id: معرف الكيان
            currency_code: رمز العملة
            
        Returns:
            Dict مع معلومات الرصيد
        """
        try:
            query = """
                SELECT * FROM current_balances_detailed
                WHERE entity_type_code = :entity_type
                AND entity_id = :entity_id
                AND currency_code = :currency_code
            """
            
            cursor = self.db.cursor()
            cursor.execute(query, {
                'entity_type': entity_type,
                'entity_id': entity_id,
                'currency_code': currency_code
            })
            result = cursor.fetchone()
            cursor.close()

            if result:
                columns = [desc[0] for desc in cursor.description]
                result = dict(zip(columns, result))
            
            if result:
                return {
                    'entity_type': result['entity_type_code'],
                    'entity_id': result['entity_id'],
                    'entity_name': result['entity_name'],
                    'currency_code': result['currency_code'],
                    'current_balance': result['current_balance'],
                    'debit_amount': result['debit_amount'],
                    'credit_amount': result['credit_amount'],
                    'total_transactions': result['total_transactions_count'],
                    'last_transaction_date': result['last_transaction_date']
                }
            else:
                return {
                    'entity_type': entity_type,
                    'entity_id': entity_id,
                    'currency_code': currency_code,
                    'current_balance': 0,
                    'debit_amount': 0,
                    'credit_amount': 0,
                    'total_transactions': 0,
                    'last_transaction_date': None
                }
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على رصيد {entity_type}-{entity_id}: {str(e)}")
            raise DatabaseError(f"فشل في الحصول على الرصيد: {str(e)}")
    
    def _validate_transfer_data(self, transfer_id: int, money_changer_id: int,
                               total_amount: Decimal, supplier_distributions: List[Dict]) -> None:
        """التحقق من صحة بيانات الحوالة"""
        if not transfer_id or transfer_id <= 0:
            raise ValidationError("معرف الحوالة غير صحيح")
        
        if not money_changer_id or money_changer_id <= 0:
            raise ValidationError("معرف الصراف غير صحيح")
        
        if not total_amount or total_amount <= 0:
            raise ValidationError("مبلغ الحوالة يجب أن يكون أكبر من صفر")
        
        if not supplier_distributions or len(supplier_distributions) == 0:
            raise ValidationError("يجب تحديد توزيعات الموردين")
        
        # التحقق من تطابق المبالغ
        total_distributed = sum(Decimal(str(dist['amount'])) for dist in supplier_distributions)
        total_amount_decimal = Decimal(str(total_amount))
        if abs(total_distributed - total_amount_decimal) > Decimal('0.01'):
            raise ValidationError(f"مجموع التوزيعات ({total_distributed}) لا يطابق مبلغ الحوالة ({total_amount_decimal})")
    
    def _get_transfer_info(self, transfer_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على معلومات الحوالة"""
        query = """
            SELECT id, total_amount as amount, currency, status, money_changer_bank_id as money_changer_id,
                   processed_at as executed_at, processed_by as executed_by, updated_at, updated_by
            FROM TRANSFER_REQUESTS
            WHERE id = :transfer_id
        """

        cursor = self.db.cursor()
        try:
            cursor.execute(query, {'transfer_id': transfer_id})
            result = cursor.fetchone()

            if result:
                return {
                    'id': result[0],
                    'amount': result[1],
                    'currency': result[2],
                    'status': result[3],
                    'money_changer_id': result[4],
                    'executed_at': result[5],
                    'executed_by': result[6],
                    'updated_at': result[7],
                    'updated_by': result[8]
                }
        finally:
            cursor.close()

        return None
    
    def _get_supplier_distributions(self, transfer_id: int) -> List[Dict[str, Any]]:
        """الحصول على توزيعات الموردين"""
        query = """
            SELECT tsd.*, s.name as supplier_name
            FROM transfer_supplier_distributions tsd
            JOIN suppliers s ON tsd.supplier_id = s.id
            WHERE tsd.transfer_id = :transfer_id
            ORDER BY tsd.amount DESC
        """
        
        return self.db.fetch_all(query, {'transfer_id': transfer_id})
    
    def _get_activity_log(self, transfer_id: int) -> List[Dict[str, Any]]:
        """الحصول على سجل أنشطة الحوالة"""
        query = """
            SELECT * FROM transfer_activity_log
            WHERE transfer_id = :transfer_id
            ORDER BY created_at DESC
        """
        
        return self.db.fetch_all(query, {'transfer_id': transfer_id})
    
    def _get_balance_changes(self, transfer_id: int) -> List[Dict[str, Any]]:
        """الحصول على تغييرات الأرصدة"""
        query = """
            SELECT * FROM balance_audit_trail
            WHERE document_number LIKE :doc_pattern
            ORDER BY created_at
        """
        
        return self.db.fetch_all(query, {'doc_pattern': f'%TRF-{transfer_id}%'})

    def validate_supplier_distributions(self, transfer_id: int,
                                      supplier_distributions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        التحقق من صحة توزيعات الموردين

        Args:
            transfer_id: معرف الحوالة
            supplier_distributions: قائمة توزيعات الموردين

        Returns:
            Dict مع نتيجة التحقق
        """
        try:
            # الحصول على معلومات الحوالة
            transfer_info = self._get_transfer_info(transfer_id)
            if not transfer_info:
                return {
                    'valid': False,
                    'errors': ['الحوالة غير موجودة'],
                    'warnings': []
                }

            errors = []
            warnings = []
            total_distributed = Decimal('0')

            # التحقق من وجود توزيعات
            if not supplier_distributions or len(supplier_distributions) == 0:
                errors.append('يجب تحديد توزيعات الموردين')
                return {'valid': False, 'errors': errors, 'warnings': warnings}

            # التحقق من كل توزيع
            for i, dist in enumerate(supplier_distributions):
                dist_num = i + 1

                # التحقق من وجود معرف المورد
                if 'supplier_id' not in dist or not dist['supplier_id']:
                    errors.append(f'معرف المورد مطلوب في التوزيع رقم {dist_num}')
                    continue

                # التحقق من وجود المبلغ
                if 'amount' not in dist or not dist['amount']:
                    errors.append(f'المبلغ مطلوب في التوزيع رقم {dist_num}')
                    continue

                try:
                    amount = Decimal(str(dist['amount']))
                    if amount <= 0:
                        errors.append(f'المبلغ يجب أن يكون أكبر من صفر في التوزيع رقم {dist_num}')
                        continue

                    total_distributed += amount

                except (ValueError, TypeError):
                    errors.append(f'المبلغ غير صحيح في التوزيع رقم {dist_num}')
                    continue

                # التحقق من وجود المورد
                cursor = self.db.cursor()
                cursor.execute(
                    "SELECT COUNT(*) as count FROM suppliers WHERE id = :supplier_id",
                    {'supplier_id': dist['supplier_id']}
                )
                supplier_result = cursor.fetchone()
                cursor.close()

                supplier_count = supplier_result[0] if supplier_result else 0

                if supplier_count == 0:
                    errors.append(f'المورد رقم {dist["supplier_id"]} غير موجود')

            # التحقق من تطابق المبالغ
            transfer_amount = Decimal(str(transfer_info['amount']))
            difference = abs(total_distributed - transfer_amount)

            if difference > Decimal('0.01'):
                errors.append(f'مجموع التوزيعات ({total_distributed}) لا يطابق مبلغ الحوالة ({transfer_amount})')
            elif difference > Decimal('0.001'):
                warnings.append(f'فرق بسيط في المبالغ: {difference}')

            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings,
                'total_distributed': float(total_distributed),
                'transfer_amount': float(transfer_amount),
                'difference': float(difference)
            }

        except Exception as e:
            logger.error(f"خطأ في التحقق من توزيعات الموردين: {str(e)}")
            return {
                'valid': False,
                'errors': [f'خطأ في التحقق: {str(e)}'],
                'warnings': []
            }

    def get_money_changer_balance_check(self, money_changer_id: int,
                                       amount: Decimal, currency_code: str = 'SAR') -> Dict[str, Any]:
        """
        التحقق من رصيد الصراف

        Args:
            money_changer_id: معرف الصراف
            amount: المبلغ المطلوب
            currency_code: رمز العملة

        Returns:
            Dict مع معلومات الرصيد
        """
        try:
            balance_info = self.get_entity_balance('MONEY_CHANGER', money_changer_id, currency_code)
            current_balance = Decimal(str(balance_info.get('current_balance', 0)))

            return {
                'current_balance': float(current_balance),
                'required_amount': float(amount),
                'sufficient': current_balance >= amount,
                'shortage': float(max(amount - current_balance, 0)),
                'currency': currency_code,
                'money_changer_id': money_changer_id
            }

        except Exception as e:
            logger.error(f"خطأ في التحقق من رصيد الصراف {money_changer_id}: {str(e)}")
            return {
                'current_balance': 0,
                'required_amount': float(amount),
                'sufficient': False,
                'shortage': float(amount),
                'currency': currency_code,
                'money_changer_id': money_changer_id,
                'error': str(e)
            }

    def comprehensive_validation(self, transfer_id: int, money_changer_id: int,
                                total_amount: Decimal, currency_code: str,
                                supplier_distributions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        التحقق الشامل قبل تنفيذ الحوالة

        Args:
            transfer_id: معرف الحوالة
            money_changer_id: معرف الصراف
            total_amount: المبلغ الإجمالي
            currency_code: رمز العملة
            supplier_distributions: قائمة توزيعات الموردين

        Returns:
            Dict مع نتيجة التحقق الشامل
        """
        try:
            # تحويل توزيعات الموردين إلى JSON
            distributions_json = json.dumps([{
                'supplier_id': dist['supplier_id'],
                'amount': float(dist['amount'])
            } for dist in supplier_distributions])

            # استدعاء دالة التحقق الشامل
            cursor = self.db.cursor()

            cursor.execute("SELECT VALIDATE_TRANSFER_EXECUTION(:1, :2, :3, :4, :5) FROM DUAL", [
                transfer_id,
                money_changer_id,
                float(total_amount),
                currency_code,
                distributions_json
            ])

            result = cursor.fetchone()[0]
            cursor.close()

            # تحليل النتيجة
            if result.startswith('OK:'):
                return {
                    'valid': True,
                    'message': result[3:].strip(),
                    'errors': [],
                    'warnings': []
                }
            elif result.startswith('WARNING:'):
                warnings = result[8:].strip().split(';')
                return {
                    'valid': True,
                    'message': 'تحذيرات موجودة',
                    'errors': [],
                    'warnings': [w for w in warnings if w.strip()]
                }
            else:  # ERROR:
                errors = result[6:].strip().split(';') if result.startswith('ERROR:') else [result]
                return {
                    'valid': False,
                    'message': 'فشل في التحقق',
                    'errors': [e for e in errors if e.strip()],
                    'warnings': []
                }

        except Exception as e:
            logger.error(f"خطأ في التحقق الشامل: {str(e)}")
            return {
                'valid': False,
                'message': f'خطأ في التحقق: {str(e)}',
                'errors': [str(e)],
                'warnings': []
            }

    def check_minimum_balance_limits(self, money_changer_id: int,
                                   currency_code: str = 'SAR') -> Dict[str, Any]:
        """
        التحقق من حدود الرصيد الأدنى

        Args:
            money_changer_id: معرف الصراف
            currency_code: رمز العملة

        Returns:
            Dict مع معلومات حدود الرصيد
        """
        try:
            cursor = self.db.cursor()

            # استدعاء الدالة باستخدام oracledb
            cursor.execute("SELECT CHECK_MIN_BALANCE_LIMITS(:1, :2) FROM DUAL", [
                money_changer_id,
                currency_code
            ])

            result = cursor.fetchone()[0]
            cursor.close()

            # تحليل النتيجة
            parts = result.split('|')
            status_message = parts[0]

            details = {}
            for part in parts[1:]:
                if ':' in part:
                    key, value = part.split(':', 1)
                    try:
                        details[key.lower()] = float(value)
                    except ValueError:
                        details[key.lower()] = value

            if status_message.startswith('CRITICAL:'):
                level = 'critical'
                message = status_message[9:].strip()
            elif status_message.startswith('WARNING:'):
                level = 'warning'
                message = status_message[8:].strip()
            elif status_message.startswith('OK:'):
                level = 'ok'
                message = status_message[3:].strip()
            else:
                level = 'error'
                message = status_message

            return {
                'level': level,
                'message': message,
                'current_balance': details.get('current', 0),
                'minimum_limit': details.get('minimum', 0),
                'warning_limit': details.get('warning', 0),
                'money_changer_id': money_changer_id,
                'currency': currency_code
            }

        except Exception as e:
            logger.error(f"خطأ في التحقق من حدود الرصيد: {str(e)}")
            return {
                'level': 'error',
                'message': f'خطأ في التحقق: {str(e)}',
                'current_balance': 0,
                'minimum_limit': 0,
                'warning_limit': 0,
                'money_changer_id': money_changer_id,
                'currency': currency_code
            }

    def get_balances_summary(self, entity_type: str = None,
                           currency_code: str = None) -> List[Dict[str, Any]]:
        """
        الحصول على ملخص شامل للأرصدة

        Args:
            entity_type: نوع الكيان (اختياري)
            currency_code: رمز العملة (اختياري)

        Returns:
            List مع ملخص الأرصدة
        """
        try:
            cursor = self.db.cursor()

            # بناء الاستعلام
            query = """
                SELECT
                    entity_type_code,
                    entity_id,
                    currency_code,
                    opening_balance,
                    debit_amount,
                    credit_amount,
                    current_balance,
                    total_transactions_count,
                    last_transaction_date,
                    created_at,
                    updated_at,
                    description
                FROM CURRENT_BALANCES
                WHERE 1=1
            """

            params = []

            if entity_type:
                query += " AND entity_type_code = :1"
                params.append(entity_type)

            if currency_code:
                if params:
                    query += " AND currency_code = :2"
                else:
                    query += " AND currency_code = :1"
                params.append(currency_code)

            query += " ORDER BY entity_type_code, entity_id, currency_code"

            cursor.execute(query, params)

            # قراءة النتائج
            columns = [desc[0] for desc in cursor.description]
            results = []

            for row in cursor:
                result = dict(zip(columns, row))
                # تحويل التواريخ والأرقام
                for key, value in result.items():
                    if isinstance(value, (int, float)) and value is not None:
                        result[key] = float(value)
                    elif hasattr(value, 'strftime'):  # التواريخ
                        result[key] = value.strftime('%Y-%m-%d %H:%M:%S')

                results.append(result)

            cursor.close()

            return results

        except Exception as e:
            logger.error(f"خطأ في الحصول على ملخص الأرصدة: {str(e)}")
            return []


# إنشاء instance عام للاستخدام
transfer_accounting = TransferAccountingService()
