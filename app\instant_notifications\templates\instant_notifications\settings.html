{% extends "base.html" %}

{% block title %}إعدادات الإشعارات الفورية{% endblock %}

{% block extra_css %}
<style>
/* إصلاح شامل لمشاكل Bootstrap Modal */
.modal {
    z-index: 9999 !important;
}

.modal-backdrop {
    z-index: 9998 !important;
    opacity: 0.5 !important;
}

.modal-dialog {
    z-index: 10000 !important;
    position: relative !important;
}

.modal-content {
    z-index: 10001 !important;
    position: relative !important;
    background: white !important;
    border: none !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* إصلاح مشكلة التظليل */
body.modal-open {
    overflow: hidden !important;
}

.modal.show {
    display: block !important;
    opacity: 1 !important;
}

.modal.show .modal-dialog {
    transform: none !important;
}

/* تحسين مظهر textarea */
#editMessageTemplate {
    font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.5;
    border: 2px solid #e3e6f0;
    border-radius: 8px;
    transition: border-color 0.3s ease;
}

#editMessageTemplate:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* تحسين مظهر المعاينة */
#messagePreview {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 8px;
    padding: 15px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* تحسين الجدول */
.message-template {
    max-width: 300px;
    word-wrap: break-word;
    font-size: 0.9em;
    line-height: 1.4;
}

/* تحسين الأزرار */
.btn-sm {
    margin: 2px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-cog text-primary"></i>
            إعدادات الإشعارات الفورية
        </h1>
        <div>
            <a href="{{ url_for('instant_notifications.dashboard') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>

    <!-- إعدادات الإشعارات -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-bell"></i>
                إعدادات أنواع الإشعارات
            </h6>
        </div>
        <div class="card-body">
            {% if settings %}
                <div class="table-responsive">
                    <table class="table table-bordered" id="settingsTable">
                        <thead>
                            <tr>
                                <th>نوع الإشعار</th>
                                <th>الحالة</th>
                                <th>قالب الرسالة</th>
                                <th>آخر تحديث</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for setting in settings %}
                            <tr>
                                <td>
                                    <strong>{{ setting.event_name_ar }}</strong>
                                    <br>
                                    <small class="text-muted">{{ setting.event_type }}</small>
                                </td>
                                <td>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" 
                                               id="enabled_{{ setting.id }}" 
                                               {{ 'checked' if setting.is_enabled else '' }}
                                               onchange="toggleSetting({{ setting.id }}, this.checked)">
                                        <label class="form-check-label" for="enabled_{{ setting.id }}">
                                            <span class="badge {{ 'bg-success' if setting.is_enabled else 'bg-secondary' }}">
                                                {{ 'مفعل' if setting.is_enabled else 'معطل' }}
                                            </span>
                                        </label>
                                    </div>
                                </td>
                                <td>
                                    <div class="message-template" id="template_{{ setting.id }}">
                                        {{ setting.message_template }}
                                    </div>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ setting.updated_at.strftime('%Y-%m-%d %H:%M') if setting.updated_at else 'غير محدد' }}
                                    </small>
                                </td>
                                <td>
                                    <button class="btn btn-primary btn-sm"
                                            onclick="editMessageCustom({{ setting.id }}, '{{ setting.message_template|replace("'", "\\'") }}')">
                                        <i class="fas fa-edit"></i> تعديل
                                    </button>
                                    <button class="btn btn-success btn-sm"
                                            onclick="testMessage('{{ setting.event_type }}', '{{ setting.message_template|replace("'", "\\'") }}')">
                                        <i class="fas fa-paper-plane"></i> اختبار
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-circle fa-3x text-warning mb-3"></i>
                    <h5>لا توجد إعدادات</h5>
                    <p class="text-muted">لم يتم العثور على إعدادات الإشعارات</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- معلومات المتغيرات المتاحة -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">
                <i class="fas fa-info-circle"></i>
                المتغيرات المتاحة في قوالب الرسائل
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">متغيرات الشحنة:</h6>
                    <ul class="list-unstyled">
                        <li><code>{tracking_number}</code> - رقم التتبع</li>
                        <li><code>{shipment_id}</code> - معرف الشحنة</li>
                        <li><code>{old_status}</code> - الحالة السابقة</li>
                        <li><code>{new_status}</code> - الحالة الجديدة</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="text-success">متغيرات إضافية:</h6>
                    <ul class="list-unstyled">
                        <li><code>{port_of_discharge}</code> - ميناء التفريغ</li>
                        <li><code>{created_at}</code> - وقت الحدث</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لتعديل الرسالة -->
<div class="modal fade" id="editMessageModal" tabindex="-1" aria-labelledby="editMessageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editMessageModalLabel">تعديل قالب الرسالة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    يمكنك استخدام المتغيرات التالية: <code>{tracking_number}</code>, <code>{port_of_discharge}</code>, <code>{old_status}</code>, <code>{new_status}</code>
                </div>
                <form id="editMessageForm">
                    <input type="hidden" id="editSettingId">
                    <div class="mb-3">
                        <label for="editMessageTemplate" class="form-label">
                            <strong>قالب الرسالة:</strong>
                        </label>
                        <textarea class="form-control" id="editMessageTemplate" rows="5"
                                  placeholder="أدخل قالب الرسالة..."
                                  style="resize: vertical; min-height: 120px;"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">معاينة الرسالة:</label>
                        <div class="alert alert-light border" id="messagePreview" style="min-height: 60px;">
                            سيتم عرض معاينة الرسالة هنا...
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> إلغاء
                </button>
                <button type="button" class="btn btn-success" onclick="previewMessage()">
                    <i class="fas fa-eye"></i> معاينة
                </button>
                <button type="button" class="btn btn-primary" onclick="saveMessage()">
                    <i class="fas fa-save"></i> حفظ التغييرات
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function toggleSetting(settingId, enabled) {
    fetch('/instant-notifications/api/update_setting', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            setting_id: settingId,
            field: 'is_enabled',
            value: enabled
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث شارة الحالة
            const badge = document.querySelector(`#enabled_${settingId}`).nextElementSibling.querySelector('.badge');
            badge.textContent = enabled ? 'مفعل' : 'معطل';
            badge.className = `badge ${enabled ? 'bg-success' : 'bg-secondary'}`;
            
            showAlert('تم تحديث الإعداد بنجاح', 'success');
        } else {
            showAlert('خطأ في التحديث: ' + data.message, 'error');
            // إعادة تعيين الحالة
            document.querySelector(`#enabled_${settingId}`).checked = !enabled;
        }
    })
    .catch(error => {
        showAlert('خطأ في الاتصال: ' + error, 'error');
        document.querySelector(`#enabled_${settingId}`).checked = !enabled;
    });
}

function editMessage(settingId, currentMessage) {
    console.log('فتح نافذة التعديل للإعداد:', settingId);

    // تنظيف أي نوافذ سابقة
    const existingModals = document.querySelectorAll('.modal.show');
    existingModals.forEach(modal => {
        const bsModal = bootstrap.Modal.getInstance(modal);
        if (bsModal) {
            bsModal.hide();
        }
    });

    // إزالة أي backdrop قديم
    const oldBackdrops = document.querySelectorAll('.modal-backdrop');
    oldBackdrops.forEach(backdrop => backdrop.remove());

    // إعادة تعيين body
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';

    // تعبئة النموذج
    document.getElementById('editSettingId').value = settingId;
    document.getElementById('editMessageTemplate').value = currentMessage;
    document.getElementById('messagePreview').innerHTML = 'سيتم عرض معاينة الرسالة هنا...';

    // انتظار قصير ثم إظهار النافذة
    setTimeout(() => {
        const modalElement = document.getElementById('editMessageModal');

        // إنشاء modal جديد
        const modal = new bootstrap.Modal(modalElement, {
            backdrop: true,
            keyboard: true,
            focus: true
        });

        // إظهار النافذة
        modal.show();

        console.log('تم إظهار النافذة');

        // التركيز على النص بعد إظهار النافذة
        modalElement.addEventListener('shown.bs.modal', function() {
            const textarea = document.getElementById('editMessageTemplate');
            if (textarea) {
                textarea.focus();
                textarea.setSelectionRange(textarea.value.length, textarea.value.length);
            }
        }, { once: true });

    }, 100);
}

function previewMessage() {
    const messageTemplate = document.getElementById('editMessageTemplate').value;

    if (!messageTemplate.trim()) {
        document.getElementById('messagePreview').innerHTML = '<span class="text-muted">أدخل قالب الرسالة أولاً</span>';
        return;
    }

    // استبدال المتغيرات بقيم تجريبية
    let previewText = messageTemplate
        .replace(/{tracking_number}/g, '<strong>COSU12345678</strong>')
        .replace(/{shipment_id}/g, '<strong>999</strong>')
        .replace(/{old_status}/g, '<span class="badge bg-warning">في الطريق</span>')
        .replace(/{new_status}/g, '<span class="badge bg-success">قيد التخليص الجمركي</span>')
        .replace(/{port_of_discharge}/g, '<strong>ميناء جدة</strong>')
        .replace(/{created_at}/g, '<em>' + new Date().toLocaleString('ar') + '</em>');

    document.getElementById('messagePreview').innerHTML = `
        <div class="border-start border-primary border-4 ps-3">
            <i class="fab fa-whatsapp text-success"></i>
            <strong>معاينة رسالة WhatsApp:</strong><br>
            ${previewText}
        </div>
    `;
}

function saveMessage() {
    const settingId = document.getElementById('editSettingId').value;
    const newMessage = document.getElementById('editMessageTemplate').value;

    if (!newMessage.trim()) {
        showAlert('يرجى إدخال قالب الرسالة', 'error');
        return;
    }
    
    fetch('/instant-notifications/api/update_setting', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            setting_id: settingId,
            field: 'message_template',
            value: newMessage
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث النص في الجدول
            document.getElementById(`template_${settingId}`).textContent = newMessage;
            
            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('editMessageModal'));
            modal.hide();
            
            showAlert('تم حفظ قالب الرسالة بنجاح', 'success');
        } else {
            showAlert('خطأ في الحفظ: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showAlert('خطأ في الاتصال: ' + error, 'error');
    });
}

function testMessage(eventType, messageTemplate) {
    // استبدال المتغيرات بقيم تجريبية
    let testMessage = messageTemplate
        .replace('{tracking_number}', 'TEST-12345')
        .replace('{shipment_id}', '999')
        .replace('{old_status}', 'in_transit')
        .replace('{new_status}', 'customs_clearance')
        .replace('{port_of_discharge}', 'جدة')
        .replace('{created_at}', new Date().toLocaleString('ar'));
    
    if (confirm('هل تريد إرسال رسالة اختبار؟\n\nالرسالة:\n' + testMessage)) {
        fetch('/instant-notifications/api/test_message', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                phone_number: '967774893877',
                message: testMessage
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم إرسال رسالة الاختبار بنجاح!', 'success');
            } else {
                showAlert('فشل في إرسال رسالة الاختبار: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('خطأ في الاتصال: ' + error, 'error');
        });
    }
}

function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // إضافة التنبيه في أعلى الصفحة
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // إزالة التنبيه بعد 5 ثوانٍ
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

// دالة بديلة للتعديل باستخدام prompt
function editMessageCustom(settingId, currentMessage) {
    console.log('استخدام النافذة المخصصة للتعديل');

    // إنشاء نافذة تعديل مخصصة
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 99999;
        display: flex;
        align-items: center;
        justify-content: center;
    `;

    const dialog = document.createElement('div');
    dialog.style.cssText = `
        background: white;
        border-radius: 10px;
        padding: 20px;
        max-width: 600px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    `;

    dialog.innerHTML = `
        <div style="margin-bottom: 20px;">
            <h5 style="margin: 0; color: #333;">
                <i class="fas fa-edit" style="color: #007bff;"></i>
                تعديل قالب الرسالة
            </h5>
        </div>

        <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">قالب الرسالة:</label>
            <textarea id="customMessageTemplate" style="
                width: 100%;
                height: 120px;
                padding: 10px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-family: Arial, sans-serif;
                resize: vertical;
            " placeholder="أدخل قالب الرسالة...">${currentMessage}</textarea>
        </div>

        <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">معاينة:</label>
            <div id="customPreview" style="
                padding: 10px;
                background: #f8f9fa;
                border: 1px solid #ddd;
                border-radius: 5px;
                min-height: 50px;
            ">سيتم عرض المعاينة هنا...</div>
        </div>

        <div style="text-align: center;">
            <button onclick="saveCustomMessage(${settingId})" style="
                background: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                margin-right: 10px;
                cursor: pointer;
            ">
                <i class="fas fa-save"></i> حفظ
            </button>
            <button onclick="closeCustomModal()" style="
                background: #6c757d;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
            ">
                <i class="fas fa-times"></i> إلغاء
            </button>
        </div>
    `;

    overlay.appendChild(dialog);
    document.body.appendChild(overlay);

    // إضافة معالج للمعاينة
    const textarea = document.getElementById('customMessageTemplate');
    const preview = document.getElementById('customPreview');

    function updatePreview() {
        const text = textarea.value;
        if (text.trim()) {
            let previewText = text
                .replace(/{tracking_number}/g, '<strong>COSU12345678</strong>')
                .replace(/{port_of_discharge}/g, '<strong>ميناء جدة</strong>')
                .replace(/{old_status}/g, '<span style="background: #ffc107; padding: 2px 6px; border-radius: 3px;">في الطريق</span>')
                .replace(/{new_status}/g, '<span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 3px;">قيد التخليص الجمركي</span>');

            preview.innerHTML = previewText;
        } else {
            preview.innerHTML = 'سيتم عرض المعاينة هنا...';
        }
    }

    textarea.addEventListener('input', updatePreview);
    updatePreview();

    // التركيز على النص
    setTimeout(() => textarea.focus(), 100);

    // حفظ مرجع للنافذة
    window.customModalOverlay = overlay;
}

function saveCustomMessage(settingId) {
    const newMessage = document.getElementById('customMessageTemplate').value;

    if (!newMessage.trim()) {
        alert('يرجى إدخال قالب الرسالة');
        return;
    }

    fetch('/instant-notifications/api/update_setting', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            setting_id: settingId,
            field: 'message_template',
            value: newMessage
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث النص في الجدول
            const templateCell = document.querySelector(`#template_${settingId}`);
            if (templateCell) {
                templateCell.textContent = newMessage;
            }

            closeCustomModal();
            showAlert('تم حفظ قالب الرسالة بنجاح', 'success');
        } else {
            alert('خطأ في الحفظ: ' + data.message);
        }
    })
    .catch(error => {
        alert('خطأ في الاتصال: ' + error);
    });
}

function closeCustomModal() {
    if (window.customModalOverlay) {
        document.body.removeChild(window.customModalOverlay);
        window.customModalOverlay = null;
    }
}

// تحديث المعاينة تلقائياً عند الكتابة
document.addEventListener('DOMContentLoaded', function() {
    const textarea = document.getElementById('editMessageTemplate');
    if (textarea) {
        textarea.addEventListener('input', function() {
            // تأخير قصير لتجنب التحديث المستمر
            clearTimeout(this.previewTimeout);
            this.previewTimeout = setTimeout(previewMessage, 500);
        });
    }
});

// إصلاح مشاكل النوافذ المنبثقة عند إغلاقها
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('editMessageModal');
    if (modal) {
        modal.addEventListener('hidden.bs.modal', function() {
            // إزالة أي backdrop متبقي
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());

            // إعادة تعيين body
            document.body.classList.remove('modal-open');
            document.body.style.paddingRight = '';
        });
    }
});
</script>
{% endblock %}
