-- إضا<PERSON>ة عمودي الكمية المنفذة والكمية المتبقية لتفاصيل العقد
-- تاريخ الإنشاء: 2025-08-03
-- الوصف: إض<PERSON><PERSON>ة عمودي EXECUTED_QUANTITY و REMAINING_QUANTITY لتتبع تنفيذ العقد

-- إضا<PERSON>ة العمودين الجديدين
ALTER TABLE CONTRACT_DETAILS ADD (
    EXECUTED_QUANTITY NUMBER(15,3) DEFAULT 0 NOT NULL,
    REMAINING_QUANTITY NUMBER(15,3) DEFAULT 0 NOT NULL
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> قيود التحقق
ALTER TABLE CONTRACT_DETAILS ADD CONSTRAINT CHK_EXECUTED_QUANTITY 
CHECK (EXECUTED_QUANTITY >= 0);

ALTER TABLE CONTRACT_DETAILS ADD CONSTRAINT CHK_REMAINING_QUANTITY 
CHECK (REMAINING_QUANTITY >= 0);

-- إضافة فهارس للأداء
CREATE INDEX IDX_CONTRACT_DETAILS_EXECUTED ON CONTRACT_DETAILS(EXECUTED_QUANTITY);
CREATE INDEX IDX_CONTRACT_DETAILS_REMAINING ON CONTRACT_DETAILS(REMAINING_QUANTITY);

-- إضافة تعليقات للأعمدة
COMMENT ON COLUMN CONTRACT_DETAILS.EXECUTED_QUANTITY IS 'الكمية المنفذة من الصنف في أوامر الشراء';
COMMENT ON COLUMN CONTRACT_DETAILS.REMAINING_QUANTITY IS 'الكمية المتبقية = الكمية الأصلية - الكمية المنفذة';

-- تحديث البيانات الموجودة
-- حساب الكمية المتبقية = الكمية الأصلية (لأن لا توجد كميات منفذة بعد)
UPDATE CONTRACT_DETAILS 
SET REMAINING_QUANTITY = QUANTITY
WHERE REMAINING_QUANTITY = 0;

-- عرض النتائج
SELECT 
    cd.CONTRACT_ID,
    cd.ITEM_ID,
    cd.ITEM_NAME,
    cd.QUANTITY as ORIGINAL_QUANTITY,
    cd.EXECUTED_QUANTITY,
    cd.REMAINING_QUANTITY,
    CASE 
        WHEN cd.EXECUTED_QUANTITY = 0 THEN 'لم ينفذ'
        WHEN cd.EXECUTED_QUANTITY < cd.QUANTITY THEN 'منفذ جزئياً'
        WHEN cd.EXECUTED_QUANTITY >= cd.QUANTITY THEN 'منفذ كلياً'
    END AS EXECUTION_STATUS
FROM CONTRACT_DETAILS cd
ORDER BY cd.CONTRACT_ID, cd.ITEM_ID;

-- إنشاء دالة لحساب حالة العقد
CREATE OR REPLACE FUNCTION GET_CONTRACT_EXECUTION_STATUS(p_contract_id NUMBER)
RETURN VARCHAR2
IS
    v_total_items NUMBER := 0;
    v_fully_executed_items NUMBER := 0;
    v_partially_executed_items NUMBER := 0;
    v_status VARCHAR2(20);
BEGIN
    -- حساب إجمالي الأصناف
    SELECT COUNT(*)
    INTO v_total_items
    FROM CONTRACT_DETAILS
    WHERE CONTRACT_ID = p_contract_id;
    
    -- حساب الأصناف المنفذة كلياً
    SELECT COUNT(*)
    INTO v_fully_executed_items
    FROM CONTRACT_DETAILS
    WHERE CONTRACT_ID = p_contract_id
    AND EXECUTED_QUANTITY >= QUANTITY;
    
    -- حساب الأصناف المنفذة جزئياً
    SELECT COUNT(*)
    INTO v_partially_executed_items
    FROM CONTRACT_DETAILS
    WHERE CONTRACT_ID = p_contract_id
    AND EXECUTED_QUANTITY > 0
    AND EXECUTED_QUANTITY < QUANTITY;
    
    -- تحديد الحالة
    IF v_fully_executed_items = v_total_items THEN
        v_status := 'FULLY_EXECUTED';
    ELSIF v_partially_executed_items > 0 OR v_fully_executed_items > 0 THEN
        v_status := 'PARTIALLY_EXECUTED';
    ELSE
        v_status := 'APPROVED'; -- لم ينفذ أي شيء بعد
    END IF;
    
    RETURN v_status;
END;
/

-- اختبار الدالة
SELECT 
    CONTRACT_ID,
    GET_CONTRACT_EXECUTION_STATUS(CONTRACT_ID) as CALCULATED_STATUS
FROM CONTRACTS
ORDER BY CONTRACT_ID;

COMMIT;
