<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام ERP المتطور{% endblock %}</title>
    
    <!-- Bootstrap CSS - إصدار مستقر -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">

    <!-- إصلاح المودالات - يجب أن يكون بعد Bootstrap -->
    <link href="{{ url_for('static', filename='css/modal-fix.css') }}" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">


    
    <!-- NetSuite REAL CSS -->
    <link href="{{ url_for('static', filename='css/netsuite-real.css') }}" rel="stylesheet">

    <style>
        /* CSS بسيط للتحكم في الطي والفتح */
        .ns-nav-subsection.collapsed,
        .ns-nav-category-items.collapsed {
            display: none;
        }

        .ns-nav-subsection.expanded,
        .ns-nav-category-items.expanded {
            display: block;
        }

        .toggle-icon {
            transition: transform 0.3s ease;
        }

        .toggle-icon.rotated {
            transform: rotate(180deg);
        }

        /* تعديل عرض الشريط الجانبي إلى 13% - تجاوز CSS الخارجي */
        .ns-sidebar,
        .ns-sidebar-real {
            width: 13vw !important;
            min-width: 250px !important;
            max-width: 300px !important;
            transition: transform 0.3s ease !important;
        }

        /* تعديل المحتوى الرئيسي ليتناسب مع العرض الجديد */
        .ns-main-content,
        .main-content {
            margin-left: 13vw !important;
            margin-right: 0 !important;
            transition: margin-left 0.3s ease !important;
        }

        /* تجاوز القواعد القوية في CSS الخارجي */
        body .ns-sidebar-real {
            width: 13vw !important;
            min-width: 250px !important;
            max-width: 300px !important;
        }

        /* حالة إخفاء الشريط الجانبي */
        .ns-sidebar-real.hidden,
        .ns-sidebar.hidden {
            transform: translateX(100%) !important;
        }

        /* تعديل المحتوى عند إخفاء الشريط - قواعد قوية */
        body.sidebar-hidden .ns-main-content,
        body.sidebar-hidden .main-content,
        body.sidebar-hidden .ns-main-real,
        body.sidebar-hidden main,
        body.sidebar-hidden .content,
        body.sidebar-hidden .page-content,
        body.sidebar-hidden .main-wrapper {
            margin-left: 0 !important;
            margin-right: 0 !important;
            width: 100vw !important;
            max-width: 100vw !important;
            left: 0 !important;
            right: 0 !important;
            position: relative !important;
        }

        /* إزالة أي padding أو margin من body */
        body.sidebar-hidden {
            margin: 0 !important;
            padding: 0 !important;
        }

        /* توسيع النوافذ والحاويات عند إخفاء الشريط */
        body.sidebar-hidden .container,
        body.sidebar-hidden .container-fluid,
        body.sidebar-hidden .container-lg,
        body.sidebar-hidden .container-md,
        body.sidebar-hidden .container-sm,
        body.sidebar-hidden .container-xl,
        body.sidebar-hidden .container-xxl {
            max-width: 100vw !important;
            width: 100vw !important;
            margin-left: 0 !important;
            margin-right: 0 !important;
            padding-left: 15px !important;
            padding-right: 15px !important;
        }

        /* تجاوز أي قواعد خارجية قوية */
        html body.sidebar-hidden .ns-main-content,
        html body.sidebar-hidden .main-content,
        html body.sidebar-hidden .ns-main-real {
            margin-left: 0 !important;
            width: 100vw !important;
            max-width: 100vw !important;
        }

        /* توسيع الجداول والكروت */
        body.sidebar-hidden .card,
        body.sidebar-hidden .table-responsive,
        body.sidebar-hidden .ns-table-real {
            width: 100% !important;
        }

        /* توسيع النماذج */
        body.sidebar-hidden .form-container,
        body.sidebar-hidden .ns-form-real {
            max-width: 100% !important;
        }

        /* توسيع الرسوم البيانية والتشارت */
        body.sidebar-hidden .chart-container,
        body.sidebar-hidden canvas,
        body.sidebar-hidden .dashboard-widget {
            width: 100% !important;
            max-width: 100% !important;
        }

        /* للشاشات الصغيرة */
        @media (max-width: 1280px) {
            .ns-sidebar,
            .ns-sidebar-real,
            body .ns-sidebar-real {
                width: 250px !important;
            }
            .ns-main-content,
            .main-content {
                margin-left: 250px !important;
            }
        }

        /* للشاشات الكبيرة جداً */
        @media (min-width: 2000px) {
            .ns-sidebar,
            .ns-sidebar-real,
            body .ns-sidebar-real {
                max-width: 300px !important;
            }
        }

        /* تحسين قائمة المفضلة في الهيدر */
        #favoritesDropdown .dropdown-menu {
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border: 1px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }

        #favoritesDropdown .dropdown-header {
            background: linear-gradient(135deg, #ffc107, #ffb300);
            color: #212529;
            font-weight: 600;
            border-radius: 8px 8px 0 0;
            margin: -8px -16px 8px -16px;
            padding: 12px 16px;
        }

        #favoritesDropdown .favorite-dropdown-item .dropdown-item {
            border-radius: 8px;
            margin: 2px 8px;
            transition: all 0.3s ease;
            padding: 8px 12px;
        }

        #favoritesDropdown .favorite-dropdown-item .dropdown-item:hover {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            transform: translateX(-3px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        #favoritesCount {
            font-size: 10px;
            min-width: 18px;
            height: 18px;
            line-height: 18px;
            border-radius: 9px;
        }

        /* تحسينات إضافية عند إخفاء الشريط */
        body.sidebar-hidden {
            /* إزالة أي هوامش إضافية */
            overflow-x: auto !important;
        }

        body.sidebar-hidden .row {
            margin-left: 0 !important;
            margin-right: 0 !important;
        }

        body.sidebar-hidden .col,
        body.sidebar-hidden [class*="col-"] {
            max-width: 100% !important;
        }

        /* تحسين عرض لوحة المعلومات */
        body.sidebar-hidden .dashboard-container,
        body.sidebar-hidden .main-dashboard {
            width: 100vw !important;
            max-width: 100vw !important;
            padding: 15px !important;
        }

        /* تحسين النوافذ المنبثقة والمودال */
        body.sidebar-hidden .modal-dialog {
            max-width: 90% !important;
        }

        /* تحسين الجداول الكبيرة */
        body.sidebar-hidden .table-container {
            width: 100% !important;
            overflow-x: auto !important;
        }

        /* تحسين عرض النصوص في الشريط الجانبي */
        .ns-nav-item {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 8px 12px !important;
            font-size: 13px !important;
            line-height: 1.4 !important;
        }

        .ns-nav-header {
            padding: 10px 12px !important;
            font-size: 13px !important;
            line-height: 1.3 !important;
        }

        /* تحسين عرض الشارات */
        .badge {
            font-size: 10px !important;
            padding: 2px 5px !important;
            margin-left: 5px !important;
        }

        /* تحسين الأيقونات */
        .ns-nav-item i {
            font-size: 13px !important;
            width: 16px !important;
            margin-left: 8px !important;
        }

        /* ثيمات الشريط الجانبي المحسنة */

        /* الثيم الافتراضي */
        .ns-sidebar-real.theme-default {
            background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 3px solid #007bff;
        }

        .ns-sidebar-real.theme-default .sidebar-controls,
        .ns-sidebar-real.theme-default .sidebar-search,
        .ns-sidebar-real.theme-default .sidebar-theme-selector,
        .ns-sidebar-real.theme-default .dashboard-link {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid #dee2e6;
            color: #495057;
        }

        /* الثيم الداكن */
        .ns-sidebar-real.theme-dark {
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            border-left: 3px solid #3498db;
            color: #ecf0f1;
        }

        .ns-sidebar-real.theme-dark .sidebar-controls,
        .ns-sidebar-real.theme-dark .sidebar-search,
        .ns-sidebar-real.theme-dark .sidebar-theme-selector,
        .ns-sidebar-real.theme-dark .dashboard-link {
            background: rgba(52, 73, 94, 0.8);
            border: 1px solid #34495e;
            color: #ecf0f1;
        }

        .ns-sidebar-real.theme-dark .ns-nav-header {
            color: #3498db;
            border-bottom: 1px solid #34495e;
        }

        .ns-sidebar-real.theme-dark .ns-nav-item {
            color: #bdc3c7;
        }

        .ns-sidebar-real.theme-dark .ns-nav-item:hover {
            background: rgba(52, 152, 219, 0.2);
            color: #ecf0f1;
        }

        /* الثيم الأزرق */
        .ns-sidebar-real.theme-blue {
            background: linear-gradient(180deg, #1e3c72 0%, #2a5298 100%);
            border-left: 3px solid #74b9ff;
            color: #ddd;
        }

        .ns-sidebar-real.theme-blue .sidebar-controls,
        .ns-sidebar-real.theme-blue .sidebar-search,
        .ns-sidebar-real.theme-blue .sidebar-theme-selector,
        .ns-sidebar-real.theme-blue .dashboard-link {
            background: rgba(30, 60, 114, 0.8);
            border: 1px solid #2a5298;
            color: #ddd;
        }

        .ns-sidebar-real.theme-blue .ns-nav-header {
            color: #74b9ff;
            border-bottom: 1px solid #2a5298;
        }

        .ns-sidebar-real.theme-blue .ns-nav-item {
            color: #b2bec3;
        }

        .ns-sidebar-real.theme-blue .ns-nav-item:hover {
            background: rgba(116, 185, 255, 0.2);
            color: #ddd;
        }

        /* الثيم الأخضر */
        .ns-sidebar-real.theme-green {
            background: linear-gradient(180deg, #27ae60 0%, #2ecc71 100%);
            border-left: 3px solid #00b894;
            color: #2d3436;
        }

        .ns-sidebar-real.theme-green .sidebar-controls,
        .ns-sidebar-real.theme-green .sidebar-search,
        .ns-sidebar-real.theme-green .sidebar-theme-selector,
        .ns-sidebar-real.theme-green .dashboard-link {
            background: rgba(39, 174, 96, 0.8);
            border: 1px solid #2ecc71;
            color: #2d3436;
        }

        .ns-sidebar-real.theme-green .ns-nav-header {
            color: #00b894;
            border-bottom: 1px solid #2ecc71;
        }

        .ns-sidebar-real.theme-green .ns-nav-item {
            color: #2d3436;
        }

        .ns-sidebar-real.theme-green .ns-nav-item:hover {
            background: rgba(0, 184, 148, 0.2);
            color: #2d3436;
        }

        /* الثيم البنفسجي */
        .ns-sidebar-real.theme-purple {
            background: linear-gradient(180deg, #8e44ad 0%, #9b59b6 100%);
            border-left: 3px solid #a29bfe;
            color: #ecf0f1;
        }

        .ns-sidebar-real.theme-purple .sidebar-controls,
        .ns-sidebar-real.theme-purple .sidebar-search,
        .ns-sidebar-real.theme-purple .sidebar-theme-selector,
        .ns-sidebar-real.theme-purple .dashboard-link {
            background: rgba(142, 68, 173, 0.8);
            border: 1px solid #9b59b6;
            color: #ecf0f1;
        }

        .ns-sidebar-real.theme-purple .ns-nav-header {
            color: #a29bfe;
            border-bottom: 1px solid #9b59b6;
        }

        .ns-sidebar-real.theme-purple .ns-nav-item {
            color: #bdc3c7;
        }

        .ns-sidebar-real.theme-purple .ns-nav-item:hover {
            background: rgba(162, 155, 254, 0.2);
            color: #ecf0f1;
        }

        /* ثيمات جديدة مميزة */

        /* ثيم الذهبي الفاخر */
        .ns-sidebar-real.theme-gold {
            background: linear-gradient(180deg, #f39c12 0%, #e67e22 100%);
            border-left: 3px solid #f1c40f;
            color: #2c3e50;
        }

        .ns-sidebar-real.theme-gold .sidebar-controls,
        .ns-sidebar-real.theme-gold .sidebar-search,
        .ns-sidebar-real.theme-gold .sidebar-theme-selector,
        .ns-sidebar-real.theme-gold .dashboard-link {
            background: rgba(243, 156, 18, 0.8);
            border: 1px solid #e67e22;
            color: #2c3e50;
        }

        .ns-sidebar-real.theme-gold .ns-nav-header {
            color: #f1c40f;
            border-bottom: 1px solid #e67e22;
            font-weight: bold;
        }

        .ns-sidebar-real.theme-gold .ns-nav-item {
            color: #2c3e50;
        }

        .ns-sidebar-real.theme-gold .ns-nav-item:hover {
            background: rgba(241, 196, 15, 0.3);
            color: #2c3e50;
        }

        /* ثيم الأحمر الناري */
        .ns-sidebar-real.theme-fire {
            background: linear-gradient(180deg, #e74c3c 0%, #c0392b 100%);
            border-left: 3px solid #ff6b6b;
            color: #ecf0f1;
        }

        .ns-sidebar-real.theme-fire .sidebar-controls,
        .ns-sidebar-real.theme-fire .sidebar-search,
        .ns-sidebar-real.theme-fire .sidebar-theme-selector,
        .ns-sidebar-real.theme-fire .dashboard-link {
            background: rgba(231, 76, 60, 0.8);
            border: 1px solid #c0392b;
            color: #ecf0f1;
        }

        .ns-sidebar-real.theme-fire .ns-nav-header {
            color: #ff6b6b;
            border-bottom: 1px solid #c0392b;
        }

        .ns-sidebar-real.theme-fire .ns-nav-item {
            color: #ecf0f1;
        }

        .ns-sidebar-real.theme-fire .ns-nav-item:hover {
            background: rgba(255, 107, 107, 0.2);
            color: #ecf0f1;
        }

        /* ثيم المحيط الأزرق */
        .ns-sidebar-real.theme-ocean {
            background: linear-gradient(180deg, #0984e3 0%, #74b9ff 100%);
            border-left: 3px solid #00cec9;
            color: #2d3436;
        }

        .ns-sidebar-real.theme-ocean .sidebar-controls,
        .ns-sidebar-real.theme-ocean .sidebar-search,
        .ns-sidebar-real.theme-ocean .sidebar-theme-selector,
        .ns-sidebar-real.theme-ocean .dashboard-link {
            background: rgba(9, 132, 227, 0.8);
            border: 1px solid #74b9ff;
            color: #2d3436;
        }

        .ns-sidebar-real.theme-ocean .ns-nav-header {
            color: #00cec9;
            border-bottom: 1px solid #74b9ff;
        }

        .ns-sidebar-real.theme-ocean .ns-nav-item {
            color: #2d3436;
        }

        .ns-sidebar-real.theme-ocean .ns-nav-item:hover {
            background: rgba(0, 206, 201, 0.2);
            color: #2d3436;
        }

        /* ثيم الغروب الوردي */
        .ns-sidebar-real.theme-sunset {
            background: linear-gradient(180deg, #fd79a8 0%, #e84393 100%);
            border-left: 3px solid #fdcb6e;
            color: #2d3436;
        }

        .ns-sidebar-real.theme-sunset .sidebar-controls,
        .ns-sidebar-real.theme-sunset .sidebar-search,
        .ns-sidebar-real.theme-sunset .sidebar-theme-selector,
        .ns-sidebar-real.theme-sunset .dashboard-link {
            background: rgba(253, 121, 168, 0.8);
            border: 1px solid #e84393;
            color: #2d3436;
        }

        .ns-sidebar-real.theme-sunset .ns-nav-header {
            color: #fdcb6e;
            border-bottom: 1px solid #e84393;
        }

        .ns-sidebar-real.theme-sunset .ns-nav-item {
            color: #2d3436;
        }

        .ns-sidebar-real.theme-sunset .ns-nav-item:hover {
            background: rgba(253, 203, 110, 0.3);
            color: #2d3436;
        }

        /* ثيم الليل النجمي */
        .ns-sidebar-real.theme-starry {
            background: linear-gradient(180deg, #2d3436 0%, #636e72 100%);
            border-left: 3px solid #00b894;
            color: #ddd;
            position: relative;
        }

        .ns-sidebar-real.theme-starry::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: radial-gradient(2px 2px at 20px 30px, #fff, transparent),
                              radial-gradient(2px 2px at 40px 70px, #fff, transparent),
                              radial-gradient(1px 1px at 90px 40px, #fff, transparent),
                              radial-gradient(1px 1px at 130px 80px, #fff, transparent),
                              radial-gradient(2px 2px at 160px 30px, #fff, transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            opacity: 0.3;
            pointer-events: none;
        }

        .ns-sidebar-real.theme-starry .sidebar-controls,
        .ns-sidebar-real.theme-starry .sidebar-search,
        .ns-sidebar-real.theme-starry .sidebar-theme-selector,
        .ns-sidebar-real.theme-starry .dashboard-link {
            background: rgba(45, 52, 54, 0.9);
            border: 1px solid #636e72;
            color: #ddd;
            position: relative;
            z-index: 1;
        }

        .ns-sidebar-real.theme-starry .ns-nav-header {
            color: #00b894;
            border-bottom: 1px solid #636e72;
            position: relative;
            z-index: 1;
        }

        .ns-sidebar-real.theme-starry .ns-nav-item {
            color: #ddd;
            position: relative;
            z-index: 1;
        }

        .ns-sidebar-real.theme-starry .ns-nav-item:hover {
            background: rgba(0, 184, 148, 0.2);
            color: #ddd;
        }

        /* تحسينات عامة للثيمات - شاملة لجميع العناصر */
        .ns-sidebar-real .sidebar-controls,
        .ns-sidebar-real .sidebar-controls button,
        .ns-sidebar-real .sidebar-search,
        .ns-sidebar-real .sidebar-search input,
        .ns-sidebar-real .sidebar-theme-selector,
        .ns-sidebar-real .sidebar-theme-selector select,
        .ns-sidebar-real .dashboard-link {
            transition: all 0.3s ease;
            border-radius: 6px;
        }

        .ns-sidebar-real .sidebar-controls button:hover,
        .ns-sidebar-real .sidebar-search input:focus,
        .ns-sidebar-real .sidebar-theme-selector select:focus {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* تأكيد تطبيق الثيمات على جميع العناصر */
        .ns-sidebar-real .sidebar-search input,
        .ns-sidebar-real .sidebar-theme-selector select {
            background: inherit;
            color: inherit;
            border-color: inherit;
        }

        .ns-sidebar-real .sidebar-search input::placeholder {
            color: inherit;
            opacity: 0.7;
        }
    </style>

</head>

<body>
    <!-- NetSuite REAL Header -->
    <header class="ns-header-real">
        <div class="ns-logo">
            <i class="fas fa-building"></i>
            NetSuite Oracle - نظام ERP المتطور
        </div>

        <!-- زر إخفاء/إظهار الشريط الجانبي وقائمة المفضلة -->
        <div class="ms-auto d-flex align-items-center">
            <!-- زر إخفاء/إظهار الشريط الجانبي -->
            <button class="btn btn-sm btn-outline-light me-2" onclick="toggleMainSidebar()" id="mainSidebarToggle" title="إخفاء/إظهار الشريط الجانبي">
                <i class="fas fa-bars" id="mainSidebarIcon"></i>
                <span class="d-none d-md-inline ms-1" id="mainSidebarText">إخفاء الشريط</span>
            </button>

            <!-- قائمة المفضلة -->
            <div class="position-relative">
                <button class="btn btn-sm btn-outline-light" type="button" id="favoritesToggle" title="المفضلة">
                    <i class="fas fa-star text-warning"></i>
                    <span class="d-none d-md-inline ms-1">المفضلة</span>
                    <span class="badge bg-warning text-dark ms-1">2</span>
                </button>
                <div class="dropdown-menu position-absolute" id="favoritesMenu" style="min-width: 250px; top: 100%; left: 0; display: none; z-index: 1050;">
                    <h6 class="dropdown-header"><i class="fas fa-star text-warning me-2"></i>الصفحات المفضلة</h6>
                    <div class="dropdown-divider"></div>

                    <a class="dropdown-item d-flex align-items-center" href="/dashboard">
                        <i class="fas fa-tachometer-alt me-2 text-primary" style="width: 16px;"></i>
                        <span class="flex-grow-1">لوحة المعلومات</span>
                        <i class="fas fa-external-link-alt text-muted ms-2" style="font-size: 10px;"></i>
                    </a>

                    <a class="dropdown-item d-flex align-items-center" href="/shipments/automation-dashboard">
                        <i class="fas fa-robot me-2 text-primary" style="width: 16px;"></i>
                        <span class="flex-grow-1">لوحة الأتمتة</span>
                        <i class="fas fa-external-link-alt text-muted ms-2" style="font-size: 10px;"></i>
                    </a>

                    <div class="dropdown-divider"></div>
                    <div class="dropdown-item-text text-muted text-center">
                        <small>اضغط النجوم في الشريط الجانبي لإضافة المزيد</small>
                    </div>
                </div>
            </div>
        </div>

        {% if current_user.is_authenticated %}
        <div class="ns-user-menu">
            <div class="dropdown">
                <button class="dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user-circle"></i>
                    {{ current_user.full_name }}
                    <i class="fas fa-chevron-down"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                        <i class="fas fa-user me-2"></i>الملف الشخصي
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                        <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                    </a></li>
                </ul>
            </div>
        </div>
        {% endif %}
    </header>

    {% if current_user.is_authenticated %}
    <!-- NetSuite REAL Sidebar -->
    <nav class="ns-sidebar-real">
        <!-- أزرار التحكم في الشريط الجانبي -->
        <div class="sidebar-controls">
            <button class="btn btn-sm btn-outline-secondary me-1" onclick="window.expandAllSections()" title="فتح جميع الأقسام">
                <i class="fas fa-expand-alt"></i>
            </button>
            <button class="btn btn-sm btn-outline-secondary" onclick="window.collapseAllSections()" title="إغلاق جميع الأقسام">
                <i class="fas fa-compress-alt"></i>
            </button>
        </div>




        <!-- 1. لوحة المعلومات الرئيسية -->
        <div class="ns-nav-section">
            <a href="{{ url_for('main.dashboard') }}" class="ns-nav-item active dashboard-link">
                <i class="fas fa-tachometer-alt"></i>
                لوحة المعلومات الرئيسية
            </a>
        </div>

        <!-- الإعدادات -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">
                <i class="fas fa-cog me-2"></i>
                الإعدادات
            </div>
            <a class="ns-nav-item" href="{{ url_for('settings.index') }}">
                <i class="fas fa-cog"></i>
                الإعدادات العامة
            </a>
            <a class="ns-nav-item" href="{{ url_for('settings.general_settings') }}">
                <i class="fas fa-sliders-h"></i>
                إعدادات النظام
            </a>
            <a class="ns-nav-item" href="{{ url_for('settings.database_settings') }}">
                <i class="fas fa-database"></i>
                إعدادات قاعدة البيانات
            </a>
            <a class="ns-nav-item" href="{{ url_for('settings.security_settings') }}">
                <i class="fas fa-shield-alt"></i>
                إعدادات الأمان
            </a>
            <a class="ns-nav-item" href="{{ url_for('settings.backup_settings') }}">
                <i class="fas fa-save"></i>
                النسخ الاحتياطي
            </a>
            <a class="ns-nav-item" href="{{ url_for('currencies.index') }}">
                <i class="fas fa-coins"></i>
                إدارة العملات
            </a>
            <a class="ns-nav-item" href="{{ url_for('settings.branches') }}">
                <i class="fas fa-building"></i>
                إدارة الفروع
            </a>
            <a class="ns-nav-item" href="{{ url_for('settings.system_info') }}">
                <i class="fas fa-info-circle"></i>
                معلومات النظام
            </a>
            <a class="ns-nav-item" href="/admin/server-management">
                <i class="fas fa-server"></i>
                إدارة الخادم والنشر
                <span class="badge bg-warning ms-auto">PRO</span>
            </a>
        </div>

        <!-- المشتريات -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">
                <i class="fas fa-shopping-cart me-2"></i>
                المشتريات
            </div>
            <a class="ns-nav-item" href="{{ url_for('purchase_orders.index') }}">
                <i class="fas fa-shopping-cart"></i>
                أوامر الشراء
            </a>
            <a class="ns-nav-item" href="{{ url_for('purchase_requests.index') }}">
                <i class="fas fa-file-alt"></i>
                طلبات الشراء
            </a>
            <a class="ns-nav-item" href="{{ url_for('contracts.index') }}">
                <i class="fas fa-file-contract"></i>
                بيانات العقود
            </a>
            <a class="ns-nav-item" href="{{ url_for('purchase_contracts.index') }}">
                <i class="fas fa-handshake"></i>
                عقود الشراء
            </a>
            <a class="ns-nav-item" href="{{ url_for('purchase_orders.items_analysis') }}">
                <i class="fas fa-chart-line"></i>
                أصناف المشتريات
            </a>
            <a class="ns-nav-item" href="{{ url_for('purchase_orders.items_analysis_advanced') }}">
                <i class="fas fa-chart-pie"></i>
                تحليل أصناف متقدم
            </a>
        </div>

        <!-- إعدادات المشتريات -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">
                <i class="fas fa-cogs me-2"></i>
                إعدادات المشتريات
            </div>
            <a class="ns-nav-item" href="{{ url_for('purchase_settings.supplier_variables') }}">
                <i class="fas fa-cogs"></i>
                متغيرات الموردين
            </a>
            <a class="ns-nav-item" href="{{ url_for('purchase_settings.supplier_groups') }}">
                <i class="fas fa-layer-group"></i>
                مجموعات الموردين
            </a>
            <a class="ns-nav-item" href="{{ url_for('purchase_commissions.representatives') }}">
                <i class="fas fa-percentage"></i>
                إدارة عمولات المندوبين
            </a>
        </div>

        <!-- الموردين -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">
                <i class="fas fa-truck me-2"></i>
                الموردين
            </div>
            <a class="ns-nav-item" href="{{ url_for('suppliers.index') }}">
                <i class="fas fa-building"></i>
                إدارة الموردين
            </a>
            <a class="ns-nav-item" href="{{ url_for('opening_balances.index') }}">
                <i class="fas fa-calculator"></i>
                الأرصدة الافتتاحية
            </a>
            <a class="ns-nav-item" href="{{ url_for('suppliers.accounts_management') }}">
                <i class="fas fa-user-tie"></i>
                إدارة حسابات الموردين
            </a>
            <a class="ns-nav-item" href="{{ url_for('suppliers.balances_management') }}">
                <i class="fas fa-balance-scale"></i>
                إدارة أرصدة الموردين
            </a>
            <a class="ns-nav-item" href="{{ url_for('suppliers.balances_simple') }}">
                <i class="fas fa-balance-scale text-success"></i>
                أرصدة الموردين - محسنة ✨
            </a>
            <a class="ns-nav-item" href="{{ url_for('suppliers.statements_management') }}">
                <i class="fas fa-file-invoice"></i>
                كشوفات الحساب
            </a>
            <a class="ns-nav-item" href="{{ url_for('suppliers.reconciliation_management') }}">
                <i class="fas fa-balance-scale-right"></i>
                نظام المطابقة
            </a>
            <a class="ns-nav-item" href="{{ url_for('suppliers.payments_management') }}">
                <i class="fas fa-credit-card"></i>
                إدارة المدفوعات
            </a>
            <a class="ns-nav-item" href="{{ url_for('suppliers.purchase_orders_integration') }}">
                <i class="fas fa-link"></i>
                تكامل أوامر الشراء والمدفوعات
            </a>
        </div>

        <!-- المخزون -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">
                <i class="fas fa-boxes me-2"></i>
                المخزون
            </div>
            <a class="ns-nav-item" href="{{ url_for('inventory.index') }}">
                <i class="fas fa-boxes"></i>
                بيانات الأصناف
            </a>
            <a class="ns-nav-item" href="{{ url_for('inventory.import_items') }}">
                <i class="fas fa-download"></i>
                استيراد الأصناف
            </a>
            <a class="ns-nav-item" href="#" onclick="alert('قريباً')">
                <i class="fas fa-tags"></i>
                فئات المخزون
            </a>
            <a class="ns-nav-item" href="#" onclick="alert('قريباً')">
                <i class="fas fa-chart-line"></i>
                تقارير المخزون
            </a>
        </div>

        <!-- البريد الإلكتروني -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">
                <i class="fas fa-envelope me-2"></i>
                البريد الإلكتروني
                <span class="badge bg-primary ms-auto" id="emailNotificationBadge">3</span>
            </div>
            <a class="ns-nav-item" href="{{ url_for('email.inbox') }}">
                <i class="fas fa-inbox"></i>
                صندوق الوارد
                <span class="badge bg-info ms-auto">15</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('email.compose_new') }}">
                <i class="fas fa-edit"></i>
                إنشاء رسالة
                <span class="badge bg-success ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('email.sent_box') }}">
                <i class="fas fa-paper-plane"></i>
                المرسل
            </a>
            <a class="ns-nav-item" href="{{ url_for('email.drafts_box') }}">
                <i class="fas fa-file-alt"></i>
                المسودات
                <span class="badge bg-warning ms-auto">3</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('email.trash_box') }}">
                <i class="fas fa-trash"></i>
                سلة المحذوفات
            </a>
            <a class="ns-nav-item" href="{{ url_for('email.contacts') }}">
                <i class="fas fa-address-book"></i>
                دفتر العناوين
            </a>
            <a class="ns-nav-item" href="{{ url_for('email.templates') }}">
                <i class="fas fa-file-code"></i>
                القوالب
                <span class="badge bg-secondary ms-auto">AI</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('email.analytics') }}">
                <i class="fas fa-chart-line"></i>
                تحليلات البريد
                <span class="badge bg-info ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('email.settings') }}">
                <i class="fas fa-cog"></i>
                إعدادات البريد
            </a>
        </div>



        <!-- الشحنات الأساسية -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">
                <i class="fas fa-ship me-2"></i>
                الشحنات الأساسية
            </div>
            <a class="ns-nav-item" href="{{ url_for('shipments.dashboard_fullscreen') }}">
                <i class="fas fa-tachometer-alt"></i>
                لوحة الشحنات
                <span class="badge bg-success ms-auto">محدث</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('shipments.new_cargo_shipment') }}">
                <i class="fas fa-plus-circle"></i>
                حجز شحنة جديدة
            </a>
            <a class="ns-nav-item" href="{{ url_for('shipments.status_management') }}">
                <i class="fas fa-tasks"></i>
                إدارة حالات الشحنة
                <span class="badge bg-success ms-auto">AI</span>
            </a>
        </div>

        <!-- التتبع والخرائط -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">
                <i class="fas fa-map-marked-alt me-2"></i>
                التتبع والخرائط
            </div>
            <a class="ns-nav-item" href="{{ url_for('shipments.live_map') }}">
                <i class="fas fa-map"></i>
                الخريطة المباشرة
            </a>
            <a class="ns-nav-item" href="{{ url_for('shipments.tracking_dashboard') }}">
                <i class="fas fa-map-marked-alt"></i>
                لوحة تتبع الشحنات
                <span class="badge bg-info ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('shipments.items_tracking_dashboard') }}">
                <i class="fas fa-chart-line"></i>
                لوحة تتبع الأصناف
                <span class="badge bg-success ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="#" onclick="$('#trackingModal').modal('show')">
                <i class="fas fa-search"></i>
                تتبع شحنة سريع
            </a>
        </div>

        <!-- إدارة المخلصين وأوامر التسليم -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">
                <i class="fas fa-file-contract me-2"></i>
                إدارة المخلصين وأوامر التسليم
            </div>
            <a class="ns-nav-item" href="{{ url_for('shipments.delivery_orders_dashboard') }}">
                <i class="fas fa-clipboard-list"></i>
                لوحة أوامر التسليم
                <span class="badge bg-primary ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('shipments.create_delivery_order') }}">
                <i class="fas fa-plus-square"></i>
                إنشاء أمر تسليم
                <span class="badge bg-success ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('shipments.customs_agents_management') }}">
                <i class="fas fa-users-cog"></i>
                إدارة المخلصين
                <span class="badge bg-info ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('shipments.customs_ports') }}">
                <i class="fas fa-anchor"></i>
                المنافذ الجمركية
                <span class="badge bg-primary ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="/agent-portal/dashboard">
                <i class="fas fa-user-tie"></i>
                بوابة المخلص الإلكترونية
                <span class="badge bg-primary ms-auto">جديد</span>
            </a>
        </div>

        <!-- إدارة الإفراجات -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">
                <i class="fas fa-unlock-alt me-2"></i>
                إدارة الإفراجات
            </div>
            <a class="ns-nav-item" href="{{ url_for('shipments.release_dashboard') }}">
                <i class="fas fa-tachometer-alt"></i>
                لوحة الإفراج
                <span class="badge bg-warning ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('shipments.release_management') }}">
                <i class="fas fa-clipboard-check"></i>
                إدارة الإفراج
                <span class="badge bg-primary ms-auto">مهم</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('shipments.release_approval') }}">
                <i class="fas fa-stamp"></i>
                اعتماد الإفراج
                <span class="badge bg-danger ms-auto">حساس</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('shipments.release_reports') }}">
                <i class="fas fa-chart-bar"></i>
                تقارير الإفراج
            </a>
            <a class="ns-nav-item" href="#" onclick="showTimelineSearch()">
                <i class="fas fa-history"></i>
                البحث في التاريخ الزمني
            </a>
        </div>

        <!-- إدارة الإشعارات -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">
                <i class="fas fa-bell me-2"></i>
                إدارة الإشعارات
            </div>
            <a class="ns-nav-item" href="{{ url_for('instant_notifications.dashboard') }}">
                <i class="fas fa-bolt"></i>
                الإشعارات الفورية
                <span class="badge bg-warning ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="/shipments/notifications-center">
                <i class="fas fa-bell-slash"></i>
                مركز الإشعارات المتقدم
                <span class="badge bg-danger ms-auto">فوري</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('shipments.notifications') }}">
                <i class="fas fa-cog"></i>
                إدارة الإشعارات
            </a>
            <a class="ns-nav-item" href="/notifications/contacts/">
                <i class="fas fa-address-book"></i>
                جهات الاتصال
                <span class="badge bg-primary ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('shipments.whatsapp_settings') }}">
                <i class="fab fa-whatsapp"></i>
                إعدادات WhatsApp
                <span class="badge bg-success ms-auto">جديد</span>
            </a>
        </div>

        <!-- نظام الحوالات -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">
                <i class="fas fa-money-bill-transfer me-2"></i>
                نظام الحوالات
            </div>
            <a class="ns-nav-item" href="{{ url_for('transfers.dashboard') }}">
                <i class="fas fa-tachometer-alt"></i>
                لوحة تحكم الحوالات
                <span class="badge bg-primary ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('transfers.money_changers') }}">
                <i class="fas fa-university"></i>
                إدارة الصرافين والبنوك
                <span class="badge bg-info ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('transfers.money_changers_opening_balances') }}">
                <i class="fas fa-balance-scale"></i>
                الأرصدة الافتتاحية للصرافين/البنوك
                <span class="badge bg-warning ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('transfers.money_changers_balances') }}">
                <i class="fas fa-chart-line"></i>
                أرصدة الصرافين/البنوك
                <span class="badge bg-primary ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('transfers.beneficiaries') }}">
                <i class="fas fa-users"></i>
                إدارة المستفيدين
                <span class="badge bg-success ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="/transfers/new_request">
                <i class="fas fa-plus-circle"></i>
                طلب حوالة جديدة
                <span class="badge bg-warning ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('transfers.list_requests') }}">
                <i class="fas fa-list-alt"></i>
                قائمة الطلبات
                <span class="badge bg-info ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('transfers.list_requests') }}?status=pending">
                <i class="fas fa-clock"></i>
                الطلبات المعلقة
                <span class="badge bg-danger ms-auto">عاجل</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('transfers.execution') }}">
                <i class="fas fa-play-circle"></i>
                تنفيذ الحوالات
                <span class="badge bg-success ms-auto">تنفيذ</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('transfers.list_requests') }}">
                <i class="fas fa-search"></i>
                تتبع الحوالات
                <span class="badge bg-info ms-auto">تتبع</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('transfers.list_requests') }}">
                <i class="fas fa-chart-line"></i>
                إدارة أسعار الصرف
                <span class="badge bg-warning ms-auto">قريباً</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('transfers.list_requests') }}">
                <i class="fas fa-check-circle"></i>
                الموافقات والصلاحيات
                <span class="badge bg-primary ms-auto">قريباً</span>
            </a>
            <a class="ns-nav-item" href="/transfers/requests">
                <i class="fas fa-chart-bar"></i>
                التقارير والتحليلات
                <span class="badge bg-secondary ms-auto">قريباً</span>
            </a>
        </div>

        <!-- الأنظمة الذكية للشحن -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">
                <i class="fas fa-robot me-2"></i>
                الأنظمة الذكية للشحن
            </div>
            <a class="ns-nav-item" href="{{ url_for('shipments.smart_ports_manager') }}">
                <i class="fas fa-anchor"></i>
                نظام الموانئ الذكي
                <span class="badge bg-success ms-auto">AI</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('shipments.smart_shipping_companies_enhanced') }}">
                <i class="fas fa-robot"></i>
                النظام الذكي لشركات الشحن
                <span class="badge bg-warning ms-auto">AI</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('shipments.automation_dashboard') }}">
                <i class="fas fa-cogs"></i>
                لوحة الأتمتة التلقائية
                <span class="badge bg-info ms-auto">AI</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('shipments.professional_automation_dashboard') }}">
                <i class="fas fa-robot"></i>
                الأتمتة الاحترافية
                <span class="badge bg-danger ms-auto">PRO</span>
            </a>
        </div>

        <!-- النقل والشركات -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">
                <i class="fas fa-truck me-2"></i>
                النقل والشركات
            </div>
            <a class="ns-nav-item" href="{{ url_for('shipments.carriers') }}">
                <i class="fas fa-building"></i>
                شركات الشحن
            </a>
            <a class="ns-nav-item" href="{{ url_for('shipments.drivers') }}">
                <i class="fas fa-users"></i>
                إدارة السائقين
            </a>
        </div>

        <!-- التحليلات -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">
                <i class="fas fa-chart-pie me-2"></i>
                التحليلات
            </div>
            <a class="ns-nav-item" href="{{ url_for('accounting_analytics.opening_balances') }}">
                <i class="fas fa-balance-scale"></i>
                الأرصدة الافتتاحية الموحدة
                <span class="badge bg-primary ms-auto">جديد</span>
            </a>
            <a class="ns-nav-item" href="{{ url_for('shipments.analytics') }}">
                <i class="fas fa-chart-line"></i>
                التحليلات والتقارير
            </a>
            <a class="ns-nav-item" href="/analytics/executive-dashboard">
                <i class="fas fa-chart-pie"></i>
                لوحة المعلومات التنفيذية
                <span class="badge bg-success ms-auto">متقدم</span>
            </a>
        </div>





        <!-- سير العمل -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">سير العمل</div>
            <a class="ns-nav-item" href="{{ url_for('workflow.index') }}">
                <i class="fas fa-project-diagram"></i>
                لوحة سير العمل
            </a>
        </div>

        <!-- الذكاء الاصطناعي -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">الذكاء الاصطناعي</div>
            <a class="ns-nav-item" href="{{ url_for('ai_voice.voice_purchase_contracts') }}">
                <i class="fas fa-microphone"></i>
                التحكم الصوتي
            </a>
        </div>


    </nav>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>
    {% endif %}

    <!-- NetSuite REAL Main Content -->
    <main class="ns-main-real">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="ns-alert-real ns-alert-{{ 'danger' if category == 'error' else category }}-real">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Page Content -->
        {% block content %}{% endblock %}
    </main>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- DataTables CSS & JS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>

    <!-- Bootstrap JS - إصدار مستقر -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p" crossorigin="anonymous"></script>

    <!-- إصلاح المودالات - يجب أن يكون بعد Bootstrap -->
    <script src="{{ url_for('static', filename='js/modal-fix.js') }}?v={{ moment().timestamp }}"></script>

    <!-- Collapsible Sidebar JS -->
    <script src="{{ url_for('static', filename='js/collapsible-sidebar.js') }}?v={{ moment().timestamp }}"></script>

    <!-- Bootstrap 5.1.3 Test Script -->
    <script>
    // التأكد من تحميل Bootstrap 5.1.3
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof bootstrap === 'undefined') {
            console.error('Bootstrap 5.1.3 is not loaded!');
            alert('خطأ: Bootstrap غير محمل بشكل صحيح');
        } else {
            console.log('Bootstrap 5.1.3 loaded successfully - Modal issues should be resolved');
        }
    });
    </script>

    <!-- NetSuite REAL JS -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set active navigation item
        const currentPath = window.location.pathname;
        const navItems = document.querySelectorAll('.ns-nav-item');

        navItems.forEach(item => {
            item.classList.remove('active');
            if (item.getAttribute('href') === currentPath) {
                item.classList.add('active');
            }
        });

        // Mobile sidebar toggle
        const sidebarToggle = document.querySelector('.ns-sidebar-toggle');
        const sidebar = document.querySelector('.ns-sidebar-real');

        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('show');
            });
        }

        console.log('NetSuite REAL Design loaded');

        // تطبيق نظام الشريط الجانبي المتقدم
        initAdvancedSidebar();
    });

    // مسح الحالات المحفوظة للأقسام فقط (مع الحفاظ على المفضلة والثيمات)
    function clearSavedSectionStates() {
        console.log('🗑️ مسح حالات الأقسام فقط (مع الحفاظ على المفضلة والثيمات)...');

        // مسح الحالات المتعلقة بطي/فتح الأقسام فقط
        Object.keys(localStorage).forEach(key => {
            // مسح حالات الأقسام فقط وليس المفضلة أو الثيمات
            if (key.startsWith('nav-items-section-') ||
                key.startsWith('nav-section-') ||
                key.startsWith('section-collapsed-') ||
                key.startsWith('section-expanded-')) {
                localStorage.removeItem(key);
                console.log(`🗑️ تم مسح حالة القسم: ${key}`);
            }
        });

        console.log('✅ تم مسح حالات الأقسام مع الحفاظ على المفضلة والثيمات');
    }

    // فرض الحالة الافتراضية (مطوي) لجميع الأقسام
    function forceDefaultCollapsedState() {
        console.log('🔒 فرض الحالة الافتراضية (مطوي) لجميع الأقسام...');

        let collapsedCount = 0;

        document.querySelectorAll('.ns-nav-items').forEach((container, index) => {
            // فرض الحالة المطوية
            container.classList.add('collapsed');
            container.classList.remove('expanded');
            container.style.maxHeight = '0';
            container.style.opacity = '0';
            container.style.display = 'block'; // تأكد من أنه مرئي لكن مطوي

            // تحديث الأيقونة
            const header = container.parentElement.querySelector('.ns-nav-header');
            const icon = header ? header.querySelector('.toggle-icon') : null;
            if (icon) {
                icon.classList.remove('fa-chevron-down', 'rotated');
                icon.classList.add('fa-chevron-left');
                icon.style.transform = 'rotate(-90deg)';
            }

            collapsedCount++;
        });

        console.log(`🔒 تم فرض الحالة المطوية على ${collapsedCount} قسم`);
        console.log('✅ جميع الأقسام الآن في الحالة الافتراضية (مطوي)');
    }

    // نظام الشريط الجانبي المتقدم
    function initAdvancedSidebar() {
        console.log('🚀 تهيئة الشريط الجانبي المتقدم...');

        // إعداد البحث
        setupSearch();

        // إعداد البحث والثيمات بقوة
        console.log('🔧 فرض إنشاء عناصر الشريط الجانبي...');

        // إعداد البحث
        setupSearch();

        // إعداد الثيمات
        setupThemes();

        // إعداد المفضلات
        setupFavorites();

        // فرض تطبيق الثيم مرة أخرى
        setTimeout(() => {
            const savedTheme = localStorage.getItem('sidebar-theme') || 'default';
            applyTheme(savedTheme);
            console.log('🎨 تم فرض تطبيق الثيم:', savedTheme);
        }, 100);

        // مسح أي حالات محفوظة قد تفتح الأقسام
        clearSavedSectionStates();

        // إعداد الطي والتوسيع
        setupCollapsible();

        // فرض الحالة الافتراضية مرة أخيرة بعد الإعداد
        setTimeout(() => {
            forceDefaultCollapsedState();
        }, 200);

        console.log('✅ تم تحميل الشريط الجانبي المتقدم');
    }

    // إعداد البحث مع إنشاء صندوق البحث
    function setupSearch() {
        const sidebar = document.querySelector('.ns-sidebar-real');

        // إنشاء صندوق البحث إذا لم يكن موجوداً
        let searchInput = document.getElementById('sidebarSearch');

        if (!searchInput) {
            console.log('🔍 إنشاء صندوق البحث...');

            // إنشاء حاوية البحث
            const searchContainer = document.createElement('div');
            searchContainer.className = 'sidebar-search p-2 mb-2';
            searchContainer.innerHTML = `
                <div class="position-relative">
                    <input type="text" id="sidebarSearch" class="form-control form-control-sm"
                           placeholder="🔍 البحث في القائمة..." style="padding-left: 30px;">
                    <button type="button" id="clearSearch" class="btn btn-sm position-absolute"
                            style="right: 5px; top: 50%; transform: translateY(-50%); display: none; border: none; background: none; color: #6c757d;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            // إدراج صندوق البحث بعد أزرار التحكم
            const controlsDiv = sidebar.querySelector('.sidebar-controls');
            if (controlsDiv) {
                controlsDiv.parentNode.insertBefore(searchContainer, controlsDiv.nextSibling);
                console.log('✅ تم إنشاء صندوق البحث بنجاح');
            }

            searchInput = document.getElementById('sidebarSearch');
        }

        const clearButton = document.getElementById('clearSearch');

        if (searchInput) {
            searchInput.addEventListener('input', function(e) {
                const query = e.target.value.toLowerCase().trim();
                filterMenuItems(query);
                if (clearButton) {
                    clearButton.style.display = query ? 'block' : 'none';
                }
            });
        }

        if (clearButton) {
            clearButton.addEventListener('click', function() {
                searchInput.value = '';
                filterMenuItems('');
                clearButton.style.display = 'none';
                searchInput.focus();
            });
        }
    }

    // تصفية عناصر القائمة
    function filterMenuItems(query) {
        const allItems = document.querySelectorAll('.ns-nav-item');
        const allSections = document.querySelectorAll('.ns-nav-section');

        if (!query) {
            allItems.forEach(item => {
                item.style.display = '';
                item.classList.remove('search-highlight');
            });
            allSections.forEach(section => section.style.display = '');
            return;
        }

        let hasVisibleItems = false;

        allSections.forEach(section => {
            const items = section.querySelectorAll('.ns-nav-item');
            let sectionHasVisibleItems = false;

            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(query)) {
                    item.style.display = '';
                    item.classList.add('search-highlight');
                    sectionHasVisibleItems = true;
                    hasVisibleItems = true;
                } else {
                    item.style.display = 'none';
                    item.classList.remove('search-highlight');
                }
            });

            section.style.display = sectionHasVisibleItems ? '' : 'none';
        });
    }

    // إعداد الثيمات مع إنشاء قائمة الثيمات الجديدة
    function setupThemes() {
        const sidebar = document.querySelector('.ns-sidebar-real');

        // إنشاء قائمة الثيمات إذا لم تكن موجودة
        let themeSelector = document.getElementById('themeSelector');

        if (!themeSelector) {
            console.log('🎨 إنشاء قائمة الثيمات...');

            // إنشاء حاوية الثيمات مع جميع الثيمات الجديدة
            const themeContainer = document.createElement('div');
            themeContainer.className = 'sidebar-theme-selector p-2 mb-2';
            themeContainer.innerHTML = `
                <label for="themeSelector" class="form-label mb-1" style="font-size: 11px;">
                    <i class="fas fa-palette me-1"></i>اختيار الثيم
                </label>
                <select id="themeSelector" class="form-select form-select-sm">
                    <option value="default">🔵 الافتراضي</option>
                    <option value="dark">🌙 الداكن</option>
                    <option value="blue">💙 الأزرق</option>
                    <option value="green">💚 الأخضر</option>
                    <option value="purple">💜 البنفسجي</option>
                    <option value="gold">✨ الذهبي الفاخر</option>
                    <option value="fire">🔥 الأحمر الناري</option>
                    <option value="ocean">🌊 المحيط الأزرق</option>
                    <option value="sunset">🌅 الغروب الوردي</option>
                    <option value="starry">⭐ الليل النجمي</option>
                </select>
            `;

            // إدراج قائمة الثيمات بعد أزرار التحكم
            const controlsDiv = sidebar.querySelector('.sidebar-controls');
            if (controlsDiv) {
                controlsDiv.parentNode.insertBefore(themeContainer, controlsDiv.nextSibling);
                console.log('✅ تم إنشاء قائمة الثيمات بنجاح');
            }

            themeSelector = document.getElementById('themeSelector');
        }

        if (!themeSelector) {
            console.error('❌ فشل في إنشاء قائمة الثيمات');
            return;
        }

        // تحميل الثيم المحفوظ مع تتبع
        const savedTheme = localStorage.getItem('sidebar-theme') || 'default';
        console.log(`🎨 تحميل الثيم المحفوظ: ${savedTheme}`);
        themeSelector.value = savedTheme;
        applyTheme(savedTheme);

        themeSelector.addEventListener('change', function(e) {
            const theme = e.target.value;
            console.log(`🎨 تغيير الثيم إلى: ${theme}`);
            applyTheme(theme);
            localStorage.setItem('sidebar-theme', theme);
            console.log(`💾 تم حفظ الثيم: ${theme}`);
        });
    }

    // تطبيق الثيم المحسن
    function applyTheme(theme) {
        const sidebar = document.querySelector('.ns-sidebar-real');

        // إزالة جميع الثيمات الموجودة
        const themeClasses = [
            'theme-default', 'theme-dark', 'theme-blue', 'theme-green', 'theme-purple',
            'theme-gold', 'theme-fire', 'theme-ocean', 'theme-sunset', 'theme-starry'
        ];

        themeClasses.forEach(themeClass => {
            sidebar.classList.remove(themeClass);
        });

        // إضافة الثيم الجديد
        sidebar.classList.add('theme-' + theme);

        console.log(`✨ تم تطبيق الثيم: ${theme}`);
    }

    // إعداد المفضلات
    function setupFavorites() {
        const favorites = JSON.parse(localStorage.getItem('sidebar-favorites') || '[]');

        // إضافة أيقونات المفضلات
        document.querySelectorAll('.ns-nav-item').forEach(item => {
            const href = item.getAttribute('href');
            if (href && href !== '#' && !item.querySelector('.favorite-icon')) {
                const favoriteIcon = document.createElement('i');
                favoriteIcon.className = favorites.includes(href) ?
                    'fas fa-star favorite-icon active' :
                    'far fa-star favorite-icon';
                favoriteIcon.title = 'إضافة للمفضلة';
                favoriteIcon.style.marginLeft = 'auto';
                favoriteIcon.style.fontSize = '12px';
                favoriteIcon.style.color = '#ffc107';
                favoriteIcon.style.cursor = 'pointer';
                favoriteIcon.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleFavorite(href, favoriteIcon);
                });

                item.appendChild(favoriteIcon);
            }
        });

        // إزالة أي قسم مفضلة موجود في الشريط الجانبي
        const existingFavoritesSection = document.querySelector('.favorites-section');
        if (existingFavoritesSection) {
            existingFavoritesSection.remove();
            console.log('🗑️ تم حذف قسم المفضلة من الشريط الجانبي');
        }

        // تحديث قائمة المفضلة في الهيدر فقط
        updateFavoritesDropdown(favorites);
    }

    // دالة تحديث قائمة المفضلة في الهيدر
    function updateFavoritesDropdown(favorites) {
        const favoritesMenu = document.getElementById('favoritesMenu');
        const favoritesToggle = document.getElementById('favoritesToggle');

        if (!favoritesMenu || !favoritesToggle) {
            console.error('❌ عناصر المفضلة غير موجودة');
            return;
        }

        // تحديث العداد
        const badge = favoritesToggle.querySelector('.badge');
        if (badge) {
            badge.textContent = favorites.length;
            badge.style.display = favorites.length > 0 ? 'inline' : 'none';
        }

        // مسح المحتوى القديم
        favoritesMenu.innerHTML = '';

        // إضافة الهيدر
        const header = document.createElement('h6');
        header.className = 'dropdown-header';
        header.innerHTML = '<i class="fas fa-star text-warning me-2"></i>الصفحات المفضلة';
        favoritesMenu.appendChild(header);

        const divider1 = document.createElement('div');
        divider1.className = 'dropdown-divider';
        favoritesMenu.appendChild(divider1);

        if (favorites.length === 0) {
            // رسالة فارغة
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'dropdown-item-text text-muted text-center';
            emptyMessage.innerHTML = '<small><i class="fas fa-star-o me-2"></i>لا توجد صفحات مفضلة</small>';
            favoritesMenu.appendChild(emptyMessage);
        } else {
            // إضافة عناصر المفضلة
            favorites.forEach(href => {
                const originalItem = document.querySelector('[href="' + href + '"]');
                if (originalItem) {
                    const iconElement = originalItem.querySelector('i');
                    const icon = iconElement ? iconElement.className : 'fas fa-link';
                    const text = originalItem.textContent.trim().replace(/\s+/g, ' ');

                    const favoriteItem = document.createElement('a');
                    favoriteItem.className = 'dropdown-item d-flex align-items-center';
                    favoriteItem.href = href;
                    favoriteItem.innerHTML = `
                        <i class="${icon} me-2 text-primary" style="width: 16px;"></i>
                        <span class="flex-grow-1">${text}</span>
                        <i class="fas fa-external-link-alt text-muted ms-2" style="font-size: 10px;"></i>
                    `;
                    favoritesMenu.appendChild(favoriteItem);
                }
            });
        }

        // إضافة فاصل ورسالة إرشادية
        const divider2 = document.createElement('div');
        divider2.className = 'dropdown-divider';
        favoritesMenu.appendChild(divider2);

        const helpMessage = document.createElement('div');
        helpMessage.className = 'dropdown-item-text text-muted text-center';
        helpMessage.innerHTML = '<small>اضغط النجوم في الشريط الجانبي لإضافة المزيد</small>';
        favoritesMenu.appendChild(helpMessage);

        console.log('✅ تم تحديث قائمة المفضلة:', favorites.length, 'عنصر');
    }

    // تبديل المفضلة
    function toggleFavorite(href, iconElement) {
        let favorites = JSON.parse(localStorage.getItem('sidebar-favorites') || '[]');
        const index = favorites.indexOf(href);

        if (index > -1) {
            favorites.splice(index, 1);
            iconElement.className = 'far fa-star favorite-icon';
            console.log('🗑️ تم حذف من المفضلة:', href);
        } else {
            favorites.push(href);
            iconElement.className = 'fas fa-star favorite-icon active';
            console.log('⭐ تم إضافة للمفضلة:', href);
        }

        localStorage.setItem('sidebar-favorites', JSON.stringify(favorites));
        console.log(`💾 تم حفظ المفضلة: ${favorites.length} عنصر`);

        // تحديث قائمة المفضلة في الهيدر
        updateFavoritesDropdown(favorites);
    }

    // تحديث قائمة المفضلة في الهيدر - دالة عامة
    window.updateHeaderFavorites = function(favorites) {
        console.log('🔄 بدء تحديث المفضلة في الهيدر:', favorites);

        const favoritesMenu = document.getElementById('favoritesMenu');
        const favoritesCount = document.getElementById('favoritesCount');
        const noFavorites = document.getElementById('noFavorites');

        console.log('📋 عناصر الهيدر:', {
            favoritesMenu: !!favoritesMenu,
            favoritesCount: !!favoritesCount,
            noFavorites: !!noFavorites
        });

        if (!favoritesMenu) {
            console.error('❌ عنصر favoritesMenu غير موجود');
            return;
        }

        // تحديث عداد المفضلة
        if (favorites.length > 0) {
            favoritesCount.textContent = favorites.length;
            favoritesCount.style.display = 'inline';
            noFavorites.style.display = 'none';
        } else {
            favoritesCount.style.display = 'none';
            noFavorites.style.display = 'block';
        }

        // إزالة العناصر القديمة (ما عدا الهيدر والفاصل والرسالة الفارغة)
        const existingItems = favoritesMenu.querySelectorAll('.favorite-dropdown-item');
        existingItems.forEach(item => item.remove());

        // إضافة العناصر الجديدة
        console.log('📝 إضافة', favorites.length, 'عنصر للمفضلة');
        favorites.forEach((href, index) => {
            console.log(`📌 معالجة المفضلة ${index + 1}:`, href);

            const originalItem = document.querySelector('[href="' + href + '"]');
            if (originalItem) {
                const iconElement = originalItem.querySelector('i');
                const icon = iconElement ? iconElement.className : 'fas fa-link';
                const text = originalItem.textContent.trim().replace(/\s+/g, ' ');

                console.log(`✅ تم العثور على العنصر:`, { href, icon, text });

                const listItem = document.createElement('li');
                listItem.innerHTML = `
                    <a class="dropdown-item favorite-dropdown-item d-flex align-items-center" href="${href}">
                        <i class="${icon} me-2 text-primary" style="width: 16px;"></i>
                        <span class="flex-grow-1">${text}</span>
                        <i class="fas fa-external-link-alt text-muted ms-2" style="font-size: 10px;"></i>
                    </a>
                `;

                // إدراج قبل العنصر الأخير (noFavorites)
                favoritesMenu.insertBefore(listItem, noFavorites.parentElement);
                console.log(`✅ تم إضافة العنصر للقائمة:`, text);
            } else {
                console.warn(`⚠️ لم يتم العثور على العنصر:`, href);
            }
        });

        // إضافة فاصل إذا كان هناك مفضلات
        if (favorites.length > 0) {
            const divider = document.createElement('li');
            divider.innerHTML = '<hr class="dropdown-divider">';
            divider.className = 'favorite-dropdown-item';
            favoritesMenu.insertBefore(divider, noFavorites.parentElement);
        }
    }

    // إعداد الطي والتوسيع مع ضمان الحالة الافتراضية (مطوي)
    function setupCollapsible() {
        console.log('🔧 بدء إعداد الأقسام القابلة للطي...');

        // أولاً: فرض طي جميع الأقسام الموجودة
        console.log('📁 فرض طي جميع الأقسام للحالة الافتراضية...');
        document.querySelectorAll('.ns-nav-items').forEach((container, index) => {
            container.classList.add('collapsed');
            container.style.maxHeight = '0';
            container.style.opacity = '0';

            // تحديث الأيقونة
            const header = container.parentElement.querySelector('.ns-nav-header');
            const icon = header ? header.querySelector('.toggle-icon') : null;
            if (icon) {
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-left');
                icon.style.transform = 'rotate(-90deg)';
            }
            console.log(`📁 تم طي القسم ${index} للحالة الافتراضية`);
        });

        // تحويل العناوين إلى قابلة للطي
        const headers = document.querySelectorAll('.ns-nav-header');
        console.log(`📋 تم العثور على ${headers.length} عنوان قسم`);

        headers.forEach((header, index) => {
            if (!header.querySelector('.toggle-icon')) {
                console.log(`⚙️ إعداد القسم ${index}: ${header.textContent.trim()}`);

                header.style.cursor = 'pointer';
                header.style.display = 'flex';
                header.style.alignItems = 'center';
                header.style.justifyContent = 'space-between';
                header.style.userSelect = 'none';

                const icon = document.createElement('i');
                icon.className = 'fas fa-chevron-left toggle-icon ms-auto';
                icon.style.fontSize = '10px';
                icon.style.color = '#6c757d';
                icon.style.transition = 'transform 0.3s ease';
                icon.style.transform = 'rotate(-90deg)';
                header.appendChild(icon);

                header.setAttribute('data-section', 'section-' + index);

                // إنشاء حاوية للعناصر
                const section = header.parentElement;
                const items = Array.from(section.querySelectorAll('.ns-nav-item'));
                console.log(`   📦 القسم ${index} يحتوي على ${items.length} عنصر`);

                if (items.length > 0) {
                    const itemsContainer = document.createElement('div');
                    itemsContainer.className = 'ns-nav-items collapsed';
                    itemsContainer.id = 'items-section-' + index;
                    itemsContainer.style.overflow = 'hidden';
                    itemsContainer.style.transition = 'all 0.3s ease';
                    itemsContainer.style.maxHeight = '0';
                    itemsContainer.style.opacity = '0';

                    items.forEach(item => {
                        item.style.paddingRight = '30px';
                        item.style.borderRight = '3px solid transparent';
                        item.style.transition = 'all 0.2s ease';
                        itemsContainer.appendChild(item);
                    });

                    section.appendChild(itemsContainer);

                    // إضافة مستمع النقر مع تتبع
                    header.addEventListener('click', function(e) {
                        console.log(`🖱️ تم النقر على القسم ${index}: ${header.textContent.trim()}`);
                        e.preventDefault();
                        e.stopPropagation();
                        toggleSection(header, itemsContainer, icon);
                    });

                    console.log(`✅ تم إعداد القسم ${index} بنجاح`);
                }
            } else {
                console.log(`⏭️ القسم ${index} مُعد مسبقاً - إضافة مستمع الأحداث`);

                // حتى لو كان مُعد مسبقاً، نحتاج لإضافة مستمع الأحداث
                const section = header.parentElement;
                const itemsContainer = section.querySelector('.ns-nav-items');
                const icon = header.querySelector('.toggle-icon');

                if (itemsContainer && icon) {
                    // إزالة مستمع الأحداث القديم (إن وجد)
                    header.removeEventListener('click', header._clickHandler);

                    // إضافة مستمع أحداث جديد
                    header._clickHandler = function(e) {
                        console.log(`🖱️ تم النقر على القسم ${index}: ${header.textContent.trim()}`);
                        e.preventDefault();
                        e.stopPropagation();
                        toggleSection(header, itemsContainer, icon);
                    };

                    header.addEventListener('click', header._clickHandler);
                    console.log(`✅ تم إضافة مستمع الأحداث للقسم ${index}`);
                }
            }
        });

        console.log('🎉 تم الانتهاء من إعداد جميع الأقسام');
    }

    // تبديل القسم مع تتبع
    function toggleSection(header, itemsContainer, icon) {
        const isCollapsed = itemsContainer.classList.contains('collapsed');
        const sectionName = header.textContent.trim();

        console.log(`🔄 تبديل القسم "${sectionName}" - الحالة الحالية: ${isCollapsed ? 'مطوي' : 'مفتوح'}`);

        if (isCollapsed) {
            // فتح القسم
            console.log(`📂 فتح القسم "${sectionName}"`);
            itemsContainer.classList.remove('collapsed');
            itemsContainer.style.maxHeight = '1000px';
            itemsContainer.style.opacity = '1';
            icon.classList.remove('fa-chevron-left');
            icon.classList.add('fa-chevron-down');
            icon.style.transform = 'rotate(0deg)';
        } else {
            // إغلاق القسم
            console.log(`📁 إغلاق القسم "${sectionName}"`);
            itemsContainer.classList.add('collapsed');
            itemsContainer.style.maxHeight = '0';
            itemsContainer.style.opacity = '0';
            icon.classList.remove('fa-chevron-down');
            icon.classList.add('fa-chevron-left');
            icon.style.transform = 'rotate(-90deg)';
        }

        console.log(`✅ تم تبديل القسم "${sectionName}" بنجاح`);
    }

    // وظائف عامة للتحكم
    window.expandAllSections = function() {
        document.querySelectorAll('.ns-nav-items.collapsed').forEach(container => {
            const header = container.parentElement.querySelector('.ns-nav-header');
            const icon = header.querySelector('.toggle-icon');
            if (header && icon) {
                toggleSection(header, container, icon);
            }
        });
    };

    // وظائف الهيكلة الجديدة للشريط الجانبي
    window.toggleNavSection = function(sectionId) {
        const section = document.getElementById(sectionId);
        const icon = document.getElementById(sectionId + '-icon');

        if (!section || !icon) return;

        const isCollapsed = section.classList.contains('collapsed');

        if (isCollapsed) {
            // فتح القسم
            section.classList.remove('collapsed');
            section.classList.add('expanded');
            icon.classList.add('rotated');

            // حفظ الحالة
            localStorage.setItem('nav-' + sectionId, 'expanded');
        } else {
            // إغلاق القسم
            section.classList.remove('expanded');
            section.classList.add('collapsed');
            icon.classList.remove('rotated');

            // حفظ الحالة
            localStorage.setItem('nav-' + sectionId, 'collapsed');
        }
    };



    // استعادة حالة الأقسام عند تحميل الصفحة - نسخة مبسطة
    window.restoreNavSections = function() {
        // لا نفعل شيئاً هنا - نترك الأقسام تعمل بشكل طبيعي
        // الحالة الافتراضية محددة في setupCollapsible
    };

    // وظيفة بسيطة لطي جميع الأقسام
    window.collapseAllSections = function() {
        document.querySelectorAll('.ns-nav-items:not(.collapsed)').forEach(container => {
            const header = container.parentElement.querySelector('.ns-nav-header');
            const icon = header ? header.querySelector('.toggle-icon') : null;
            if (header && icon) {
                toggleSection(header, container, icon);
            }
        });
    };

    // وظيفة لفرض الحالة الافتراضية (يمكن استدعاؤها من Console)
    window.forceDefaultState = function() {
        console.log('🔧 فرض الحالة الافتراضية من Console...');
        clearSavedSectionStates();
        forceDefaultCollapsedState();
        console.log('✅ تم فرض الحالة الافتراضية بنجاح');
    };

    // وظيفة لإعادة تعيين الشريط الجانبي بالكامل
    window.resetSidebar = function() {
        console.log('🔄 إعادة تعيين الشريط الجانبي بالكامل...');
        clearSavedSectionStates();
        location.reload();
    };

    // وظيفة لاختبار المفضلة والثيمات (للتشخيص)
    window.checkSavedData = function() {
        console.log('🔍 فحص البيانات المحفوظة:');

        // فحص المفضلة
        const favorites = localStorage.getItem('sidebar-favorites');
        console.log('⭐ المفضلة:', favorites ? JSON.parse(favorites) : 'لا توجد مفضلة');

        // فحص الثيم
        const theme = localStorage.getItem('sidebar-theme');
        console.log('🎨 الثيم:', theme || 'الثيم الافتراضي');

        // فحص حالات الأقسام
        const sectionStates = Object.keys(localStorage).filter(key =>
            key.startsWith('nav-') || key.startsWith('section-')
        );
        console.log('📁 حالات الأقسام:', sectionStates);

        // فحص جميع البيانات المحفوظة
        console.log('💾 جميع البيانات المحفوظة:', Object.keys(localStorage));
    };

    // وظيفة لإعادة تعيين المفضلة والثيمات فقط
    window.resetFavoritesAndThemes = function() {
        console.log('🗑️ مسح المفضلة والثيمات...');
        localStorage.removeItem('sidebar-favorites');
        localStorage.removeItem('sidebar-theme');
        location.reload();
    };

    // وظيفة لاختبار الثيمات الجديدة
    window.testNewThemes = function() {
        console.log('🎨 اختبار الثيمات الجديدة...');

        const themes = ['default', 'dark', 'blue', 'green', 'purple', 'gold', 'fire', 'ocean', 'sunset', 'starry'];
        let currentIndex = 0;

        const interval = setInterval(() => {
            const theme = themes[currentIndex];
            console.log(`🎨 تطبيق الثيم: ${theme}`);
            applyTheme(theme);

            // تحديث قائمة الثيمات
            const themeSelector = document.getElementById('themeSelector');
            if (themeSelector) {
                themeSelector.value = theme;
            }

            currentIndex++;
            if (currentIndex >= themes.length) {
                clearInterval(interval);
                console.log('✅ انتهى اختبار جميع الثيمات');
            }
        }, 2000);
    };

    // وظيفة لفرض إعادة إنشاء عناصر الشريط الجانبي
    window.recreateSidebarElements = function() {
        console.log('🔄 إعادة إنشاء عناصر الشريط الجانبي...');

        // حذف العناصر الموجودة
        const existingSearch = document.querySelector('.sidebar-search');
        const existingThemes = document.querySelector('.sidebar-theme-selector');

        if (existingSearch) existingSearch.remove();
        if (existingThemes) existingThemes.remove();

        // إعادة إنشاء العناصر
        setupSearch();
        setupThemes();

        console.log('✅ تم إعادة إنشاء العناصر بنجاح');
    };

    // لا نحتاج لاستدعاء setupCollapsible هنا لأن النظام المتقدم يتولى الأمر
    // document.addEventListener('DOMContentLoaded', function() {
    //     setupCollapsible();
    // });



    // عرض الخريطة المباشرة
    function showLiveMap() {
        alert('الخريطة المباشرة - قريباً\nستعرض مواقع جميع الشحنات والسائقين في الوقت الفعلي');
    }

    // عرض إدارة الإشعارات
    function showNotifications() {
        alert('إدارة الإشعارات - قريباً\nستتيح إدارة وإرسال الإشعارات للعملاء والسائقين');
    }

    // عرض البحث عن الجدولة الزمنية
    function showTimelineSearch() {
        const shipmentId = prompt('أدخل رقم الشحنة أو رقم التتبع:');
        if (shipmentId && shipmentId.trim()) {
            // إذا كان رقم، اذهب مباشرة
            if (!isNaN(shipmentId)) {
                window.location.href = `/shipments/status-timeline/${shipmentId}`;
            } else {
                // إذا كان رقم تتبع، ابحث عنه أولاً
                alert('البحث برقم التتبع - قريباً\nحالياً استخدم رقم الشحنة (ID)');
            }
        }
    }
    </script>

    {% block scripts %}{% endblock %}
    {% block extra_js %}{% endblock %}

    <script>
    // 📱 وظائف إخفاء/إظهار الشريط الجانبي الرئيسي
    let mainSidebarVisible = true;

    function toggleMainSidebar() {
        const sidebar = document.querySelector('.ns-sidebar-real');
        const mainContent = document.querySelector('.ns-main-real');
        const toggleBtn = document.getElementById('mainSidebarToggle');
        const toggleText = document.getElementById('mainSidebarText');
        const toggleIcon = document.getElementById('mainSidebarIcon');

        if (mainSidebarVisible) {
            // إخفاء الشريط الجانبي
            sidebar.classList.add('hidden');
            document.body.classList.add('sidebar-hidden');
            if (mainContent) mainContent.classList.add('expanded');
            toggleBtn.classList.add('collapsed');
            if (toggleText) toggleText.textContent = 'إظهار الشريط';
            toggleIcon.className = 'fas fa-chevron-left';

            // حفظ الحالة
            localStorage.setItem('mainSidebarVisible', 'false');
            mainSidebarVisible = false;

            // إجبار إعادة حساب العرض
            setTimeout(() => {
                window.dispatchEvent(new Event('resize'));
                // إجبار إعادة رسم العناصر
                document.querySelectorAll('.container, .container-fluid, .ns-main-content, .main-content').forEach(el => {
                    el.style.width = '100vw';
                    el.style.maxWidth = '100vw';
                    el.style.marginLeft = '0';
                });
            }, 100);

            console.log('✅ تم إخفاء الشريط الجانبي الرئيسي');
        } else {
            // إظهار الشريط الجانبي
            sidebar.classList.remove('hidden');
            document.body.classList.remove('sidebar-hidden');
            if (mainContent) mainContent.classList.remove('expanded');
            toggleBtn.classList.remove('collapsed');
            if (toggleText) toggleText.textContent = 'إخفاء الشريط';
            toggleIcon.className = 'fas fa-bars';

            // حفظ الحالة
            localStorage.setItem('mainSidebarVisible', 'true');
            mainSidebarVisible = true;

            // إجبار إعادة حساب العرض
            setTimeout(() => {
                window.dispatchEvent(new Event('resize'));
                // إعادة تعيين العرض للوضع العادي مع الهامش الصحيح
                document.querySelectorAll('.ns-main-content, .main-content').forEach(el => {
                    el.style.width = '';
                    el.style.maxWidth = '';
                    el.style.marginLeft = '13vw';
                });
                document.querySelectorAll('.container, .container-fluid').forEach(el => {
                    el.style.width = '';
                    el.style.maxWidth = '';
                });
            }, 100);

            console.log('✅ تم إظهار الشريط الجانبي الرئيسي');
        }
    }

    // استعادة حالة الشريط الجانبي عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        const savedState = localStorage.getItem('mainSidebarVisible');

        if (savedState === 'false') {
            // إخفاء الشريط الجانبي إذا كان مخفي سابقاً
            setTimeout(() => {
                toggleMainSidebar();
            }, 100);
        }

        // تحديث قائمة المفضلة في الهيدر عند تحميل الصفحة
        setTimeout(() => {
            // إزالة أي قسم مفضلة من الشريط الجانبي
            const existingFavoritesSection = document.querySelector('.favorites-section');
            if (existingFavoritesSection) {
                existingFavoritesSection.remove();
                console.log('🗑️ تم حذف قسم المفضلة من الشريط الجانبي عند التحميل');
            }

            let favorites = JSON.parse(localStorage.getItem('sidebar-favorites') || '[]');
            console.log('📋 المفضلات المحفوظة:', favorites);

            // تحديث قائمة المفضلة في الهيدر
            updateFavoritesDropdown(favorites);
        }, 200);

        console.log('📱 تم تحميل النظام مع إمكانية إخفاء/إظهار الشريط الجانبي الرئيسي');

        // إعداد قائمة المفضلة البسيطة
        setTimeout(() => {
            const favoritesToggle = document.getElementById('favoritesToggle');
            const favoritesMenu = document.getElementById('favoritesMenu');

            if (favoritesToggle && favoritesMenu) {
                console.log('✅ تم العثور على عناصر المفضلة');

                // إضافة event listener لإظهار/إخفاء القائمة
                favoritesToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    if (favoritesMenu.style.display === 'none' || favoritesMenu.style.display === '') {
                        favoritesMenu.style.display = 'block';
                        console.log('📂 تم فتح قائمة المفضلة');
                    } else {
                        favoritesMenu.style.display = 'none';
                        console.log('📁 تم إغلاق قائمة المفضلة');
                    }
                });

                // إغلاق القائمة عند الضغط خارجها
                document.addEventListener('click', function(e) {
                    if (!favoritesToggle.contains(e.target) && !favoritesMenu.contains(e.target)) {
                        favoritesMenu.style.display = 'none';
                    }
                });

            } else {
                console.error('❌ عناصر المفضلة غير موجودة');
            }
        }, 500);

        // مراقب لحذف أي قسم مفضلة يتم إنشاؤه في الشريط الجانبي
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1 && (node.classList.contains('favorites-section') || node.querySelector('.favorites-section'))) {
                        const favSection = node.classList.contains('favorites-section') ? node : node.querySelector('.favorites-section');
                        if (favSection) {
                            favSection.remove();
                            console.log('🚫 تم منع إنشاء قسم مفضلة في الشريط الجانبي');
                        }
                    }
                });
            });
        });

        // بدء مراقبة الشريط الجانبي
        const sidebar = document.querySelector('.ns-sidebar-real');
        if (sidebar) {
            observer.observe(sidebar, { childList: true, subtree: true });
        }

        // إخفاء زر الاختبار بعد فترة
        setTimeout(() => {
            const testButtons = document.querySelectorAll('button');
            testButtons.forEach(btn => {
                if (btn.textContent.includes('تحديث المفضلة') || btn.textContent.includes('اختبار المفضلة')) {
                    btn.style.display = 'none';
                    console.log('🙈 تم إخفاء زر الاختبار');
                }
            });
        }, 2000);
    });

    // اختصار لوحة المفاتيح (Ctrl + B)
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'b') {
            e.preventDefault();
            toggleMainSidebar();
        }
    });
    </script>
</body>
</html>
