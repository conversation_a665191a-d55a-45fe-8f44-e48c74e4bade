#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية لحسابات العمولات
"""

from database_manager import DatabaseManager
import logging
from datetime import datetime, timedelta

# إعداد logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    try:
        db_manager = DatabaseManager()
        
        # حذف الحسابات الموجودة
        try:
            db_manager.execute_update("DELETE FROM commission_calculations")
            logger.info('✅ تم حذف الحسابات القديمة')
        except:
            pass
        
        # إضافة حسابات تجريبية
        logger.info('إضافة حسابات عمولات تجريبية...')
        
        # حساب 1: عمولة ثابتة للكمبيوترات - أحمد
        calc1_sql = '''
        INSERT INTO commission_calculations (
            id, calculation_date, rep_id, rule_id, order_value, 
            commission_rate, commission_amount, total_quantity, 
            quantity_unit, status, created_by
        ) VALUES (1, :1, 1, 1, 15000, 0, 500, 5, 'جهاز', 'CALCULATED', 1)
        '''
        db_manager.execute_update(calc1_sql, (datetime.now() - timedelta(days=5),))
        logger.info('✅ تم إدراج حساب: عمولة كمبيوترات - أحمد')
        
        # حساب 2: عمولة نسبية للمواد الغذائية - فاطمة
        calc2_sql = '''
        INSERT INTO commission_calculations (
            id, calculation_date, rep_id, rule_id, order_value, 
            commission_rate, commission_amount, total_quantity, 
            quantity_unit, status, approved_at, created_by
        ) VALUES (2, :1, 2, 2, 25000, 2, 500, 1000, 'كيلو', 'APPROVED', :2, 1)
        '''
        db_manager.execute_update(calc2_sql, (
            datetime.now() - timedelta(days=3),
            datetime.now() - timedelta(days=1)
        ))
        logger.info('✅ تم إدراج حساب: عمولة مواد غذائية - فاطمة')
        
        # حساب 3: عمولة حسب الكمية للأجهزة الطبية - محمد
        calc3_sql = '''
        INSERT INTO commission_calculations (
            id, calculation_date, rep_id, rule_id, order_value, 
            commission_rate, commission_amount, total_quantity, 
            quantity_unit, status, approved_at, paid_at, created_by
        ) VALUES (3, :1, 3, 3, 50000, 0, 300, 3, 'جهاز', 'PAID', :2, :3, 1)
        '''
        db_manager.execute_update(calc3_sql, (
            datetime.now() - timedelta(days=7),
            datetime.now() - timedelta(days=3),
            datetime.now() - timedelta(days=1)
        ))
        logger.info('✅ تم إدراج حساب: عمولة أجهزة طبية - محمد')
        
        # حساب 4: عمولة متدرجة للمواد الخام - سارة
        calc4_sql = '''
        INSERT INTO commission_calculations (
            id, calculation_date, rep_id, rule_id, order_value, 
            commission_rate, commission_amount, total_quantity, 
            quantity_unit, status, created_by
        ) VALUES (4, :1, 4, 4, 35000, 2, 700, 2000, 'كيلو', 'CALCULATED', 1)
        '''
        db_manager.execute_update(calc4_sql, (datetime.now() - timedelta(days=2),))
        logger.info('✅ تم إدراج حساب: عمولة مواد خام - سارة')
        
        # حساب 5: عمولة إضافية للكمبيوترات - أحمد
        calc5_sql = '''
        INSERT INTO commission_calculations (
            id, calculation_date, rep_id, rule_id, order_value, 
            commission_rate, commission_amount, total_quantity, 
            quantity_unit, status, approved_at, created_by
        ) VALUES (5, :1, 1, 1, 8000, 0, 500, 2, 'جهاز', 'APPROVED', :2, 1)
        '''
        db_manager.execute_update(calc5_sql, (
            datetime.now() - timedelta(days=1),
            datetime.now()
        ))
        logger.info('✅ تم إدراج حساب: عمولة كمبيوترات إضافية - أحمد')
        
        # التحقق من النتائج
        count = db_manager.execute_query('SELECT COUNT(*) FROM commission_calculations')[0][0]
        logger.info(f'🎉 تم إنشاء {count} حسابات عمولات')
        
        # عرض الحسابات
        calculations = db_manager.execute_query('''
            SELECT cc.id, pr.rep_name, cr.rule_name, cc.order_value, 
                   cc.commission_amount, cc.status
            FROM commission_calculations cc
            JOIN purchase_representatives pr ON cc.rep_id = pr.id
            JOIN commission_rules cr ON cc.rule_id = cr.id
            ORDER BY cc.calculation_date DESC
        ''')
        
        logger.info('الحسابات المُدرجة:')
        for calc in calculations:
            logger.info(f'  - {calc[1]} - {calc[2]} - {calc[3]:,.0f} ريال - عمولة: {calc[4]:,.0f} ريال - {calc[5]}')
        
        logger.info('✅ تم إنجاز إضافة حسابات العمولات التجريبية بنجاح!')
        
    except Exception as e:
        logger.error(f'خطأ عام: {e}')

if __name__ == '__main__':
    main()
