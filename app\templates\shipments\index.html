{% extends "base.html" %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/live-map.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/shipments.css') }}">

<!-- 🎨 لمسات فنية فورية -->
<style>
/* إجبار التطبيق الفوري للألوان */
html, body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    min-height: 100vh !important;
}

.container-fluid {
    background: rgba(255, 255, 255, 0.95) !important;
    border-radius: 20px !important;
    margin: 15px !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
    backdrop-filter: blur(15px) !important;
}

/* البطاقات - إجبار الألوان */
.card {
    background: linear-gradient(145deg, #ffffff 0%, #f1f3f4 100%) !important;
    border: none !important;
    border-radius: 20px !important;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2) !important;
    margin-bottom: 25px !important;
    overflow: hidden !important;
}

/* رؤوس البطاقات - إجبار الألوان */
.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-radius: 20px 20px 0 0 !important;
    border: none !important;
    padding: 1.5rem 2rem !important;
}

.card-header h1, .card-header h2, .card-header h3,
.card-header h4, .card-header h5, .card-header h6 {
    color: white !important;
    margin: 0 !important;
    font-weight: 600 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.card-header i {
    color: rgba(255, 255, 255, 0.9) !important;
    margin-left: 10px !important;
}

/* محتوى البطاقات */
.card-body {
    background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%) !important;
    padding: 2rem !important;
}

/* الجداول */
.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border: none !important;
    font-weight: 600 !important;
    padding: 1.2rem 1rem !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%) !important;
}

/* الأزرار */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    border-radius: 25px !important;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    border: none !important;
    border-radius: 25px !important;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
    border: none !important;
    border-radius: 25px !important;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
    border: none !important;
    border-radius: 25px !important;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
    border: none !important;
    border-radius: 25px !important;
}

/* الشارات */
.badge {
    border-radius: 20px !important;
    padding: 0.6rem 1.2rem !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
}

.badge.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* النوافذ المنبثقة */
.modal-content {
    border-radius: 25px !important;
    border: none !important;
    box-shadow: 0 30px 80px rgba(0, 0, 0, 0.4) !important;
}

.modal-header.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 25px 25px 0 0 !important;
}

.modal-header.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
    border-radius: 25px 25px 0 0 !important;
}
</style>
{% endblock %}

{% block content %}

<!-- 🎨 إجبار الألوان على البطاقات فوراً -->
<style>
/* إجبار تطبيق الألوان على البطاقات بأولوية قصوى */
div.card {
    background: linear-gradient(145deg, #ffffff 0%, #f1f3f4 100%) !important;
    border: none !important;
    border-radius: 20px !important;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2) !important;
    margin-bottom: 25px !important;
    overflow: hidden !important;
}

div.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-radius: 20px 20px 0 0 !important;
    border: none !important;
    padding: 1.5rem 2rem !important;
}

div.card-header h1, div.card-header h2, div.card-header h3,
div.card-header h4, div.card-header h5, div.card-header h6 {
    color: white !important;
    margin: 0 !important;
    font-weight: 600 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

div.card-header i {
    color: rgba(255, 255, 255, 0.9) !important;
    margin-left: 10px !important;
}

div.card-body {
    background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%) !important;
    padding: 2rem !important;
}

/* إجبار ألوان الجداول */
table.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border: none !important;
    font-weight: 600 !important;
    padding: 1.2rem 1rem !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

table.table tbody tr:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%) !important;
}

/* إجبار ألوان الأزرار */
button.btn-primary, a.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    border-radius: 25px !important;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
}

button.btn-success, a.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    border: none !important;
    border-radius: 25px !important;
}

button.btn-warning, a.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
    border: none !important;
    border-radius: 25px !important;
}

button.btn-danger, a.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
    border: none !important;
    border-radius: 25px !important;
}

button.btn-info, a.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
    border: none !important;
    border-radius: 25px !important;
}

/* إجبار ألوان الشارات */
span.badge.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    border-radius: 20px !important;
    padding: 0.6rem 1.2rem !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

span.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
    border-radius: 20px !important;
    padding: 0.6rem 1.2rem !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

span.badge.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
    border-radius: 20px !important;
    padding: 0.6rem 1.2rem !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

span.badge.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
    border-radius: 20px !important;
    padding: 0.6rem 1.2rem !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

span.badge.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 20px !important;
    padding: 0.6rem 1.2rem !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}
</style>

<style>
/* إعادة تعيين التنسيق الأساسي */
body {
    background-color: #f8f9fa !important;
    font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    color: #212529 !important;
}

.container-fluid {
    background-color: #ffffff !important;
    min-height: 100vh !important;
    padding: 20px !important;
}

.card {
    background-color: #ffffff !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    margin-bottom: 20px !important;
}

.card-header {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6 !important;
    padding: 15px 20px !important;
    font-weight: 600 !important;
    color: #495057 !important;
}

.card-body {
    padding: 20px !important;
    background-color: #ffffff !important;
}

.btn {
    border-radius: 6px !important;
    font-weight: 500 !important;
    padding: 8px 16px !important;
}

.btn-primary {
    background-color: #007bff !important;
    border-color: #007bff !important;
    color: #ffffff !important;
}

.btn-success {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: #ffffff !important;
}

.btn-warning {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #212529 !important;
}

.btn-danger {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: #ffffff !important;
}

.table {
    background-color: #ffffff !important;
    border-collapse: collapse !important;
}

.table th {
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #dee2e6 !important;
    color: #495057 !important;
    font-weight: 600 !important;
    padding: 12px 8px !important;
}

.table td {
    border-bottom: 1px solid #dee2e6 !important;
    padding: 12px 8px !important;
    vertical-align: middle !important;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: #f8f9fa !important;
}

.badge {
    font-size: 0.75rem !important;
    padding: 0.375rem 0.75rem !important;
    border-radius: 0.375rem !important;
    font-weight: 500 !important;
}

.form-control {
    border: 1px solid #ced4da !important;
    border-radius: 6px !important;
    padding: 8px 12px !important;
    background-color: #ffffff !important;
    color: #495057 !important;
}

.form-select {
    border: 1px solid #ced4da !important;
    border-radius: 6px !important;
    padding: 8px 12px !important;
    background-color: #ffffff !important;
    color: #495057 !important;
}

/* تنسيق خاص بالشحنات */
.shipment-card {
    transition: all 0.3s ease !important;
    border: 1px solid #dee2e6 !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
    background-color: #ffffff !important;
}

.shipment-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 5px 20px rgba(0,0,0,0.15) !important;
}

/* تنسيق الحالات */
.shipment-status-badge {
    font-size: 0.75rem !important;
    padding: 0.375rem 0.75rem !important;
    border-radius: 0.375rem !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.shipment-status-badge:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important;
}

.release-status-badge {
    font-size: 0.75rem !important;
    padding: 0.375rem 0.75rem !important;
    border-radius: 0.375rem !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.release-status-badge:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important;
}

.clickable-status {
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.clickable-status:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important;
}

/* ألوان الحالات */
.bg-draft { background-color: #6c757d !important; color: white !important; }
.bg-confirmed { background-color: #007bff !important; color: white !important; }
.bg-in_transit { background-color: #fd7e14 !important; color: white !important; }
.bg-arrived_port { background-color: #17a2b8 !important; color: white !important; }
.bg-customs_clearance { background-color: #ffc107 !important; color: #212529 !important; }
.bg-ready_pickup { background-color: #20c997 !important; color: white !important; }
.bg-delivered { background-color: #28a745 !important; color: white !important; }
.bg-cancelled { background-color: #dc3545 !important; color: white !important; }
.bg-delayed { background-color: #e83e8c !important; color: white !important; }
.bg-returned { background-color: #6f42c1 !important; color: white !important; }

/* ألوان حالات الإفراج */
.bg-pending { background-color: #6c757d !important; color: white !important; }
.bg-documents_review { background-color: #17a2b8 !important; color: white !important; }
.bg-payment_verification { background-color: #ffc107 !important; color: #212529 !important; }
.bg-quality_check { background-color: #007bff !important; color: white !important; }
.bg-approved { background-color: #28a745 !important; color: white !important; }
.bg-released { background-color: #28a745 !important; color: white !important; }
.bg-on_hold { background-color: #dc3545 !important; color: white !important; }
.bg-rejected { background-color: #dc3545 !important; color: white !important; }

/* تنسيق إضافي للصفحة */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 20px !important;
    border-radius: 8px !important;
    margin-bottom: 20px !important;
}

.stats-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 8px !important;
}

.filter-section {
    background-color: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 8px !important;
    padding: 15px !important;
    margin-bottom: 20px !important;
}

.table-responsive {
    border-radius: 8px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    background-color: #ffffff !important;
}

.modal-content {
    border-radius: 8px !important;
    border: none !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
}

.modal-header {
    border-bottom: 1px solid #dee2e6 !important;
    border-radius: 8px 8px 0 0 !important;
}

.modal-footer {
    border-top: 1px solid #dee2e6 !important;
    border-radius: 0 0 8px 8px !important;
}

.status-badge {
    font-size: 0.8rem !important;
    padding: 0.4rem 0.8rem !important;
    border-radius: 20px !important;
    font-weight: 500 !important;
}

/* تنسيق البحث الصوتي */
#voiceSearchBtn {
    border-left: none !important;
    transition: all 0.3s ease !important;
}

#voiceSearchBtn:hover {
    background-color: #007bff !important;
    color: white !important;
    transform: scale(1.05) !important;
}

#voiceSearchBtn.listening {
    background-color: #dc3545 !important;
    color: white !important;
    border-color: #dc3545 !important;
    animation: pulse-voice 1.5s infinite !important;
}

@keyframes pulse-voice {
    0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

.pulse {
    animation: pulse-dot 1s infinite !important;
}

@keyframes pulse-dot {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

#voiceSearchStatus {
    font-size: 0.75rem !important;
    color: #dc3545 !important;
    font-weight: 500 !important;
}

.tracking-number {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #007bff;
}

.progress-bar-custom {
    height: 8px;
    border-radius: 10px;
}

.shipment-map {
    height: 400px;
    border-radius: 10px;
}

.realtime-indicator {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* تحسين أزرار الإجراءات */
.btn-group-sm .btn {
    margin: 0 1px;
    transition: all 0.2s ease;
    padding: 0.25rem 0.4rem;
    font-size: 0.75rem;
    white-space: nowrap;
}

/* ضمان عرض الأزرار في صف واحد */
.btn-group {
    display: flex !important;
    flex-wrap: nowrap !important;
    gap: 2px;
    align-items: center;
}

.btn-group .btn {
    flex-shrink: 0;
    min-width: auto;
    height: 28px;
    width: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-group .btn i {
    font-size: 0.75rem;
}

/* تحسين ألوان الأزرار */
.btn-group .btn:hover {
    transform: scale(1.05);
    z-index: 1;
}

/* ترتيب الأزرار حسب الأهمية */
.btn-outline-primary { order: 1; } /* تتبع */
.btn-outline-warning { order: 2; }  /* تعديل */
.btn-outline-info { order: 3; }     /* وثائق/خريطة */
.btn-outline-secondary { order: 4; } /* أصناف */
.btn-outline-success { order: 5; }   /* إشعار */
.btn-outline-danger { order: 6; }    /* حذف */

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    transform: scale(1.05);
}

.btn-outline-danger:hover i {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

/* تحسينات للأعمدة الجديدة */
.table td:first-child {
    font-size: 0.85rem;
    color: #6c757d;
    min-width: 90px;
    white-space: nowrap;
}

/* تحسينات عمود الإجراءات */
.table td:last-child {
    white-space: nowrap;
    min-width: 280px;
    padding: 0.5rem 0.75rem;
}

/* تحسين عرض الجدول */
.table-responsive {
    overflow-x: auto;
    min-height: 400px;
}

.table {
    table-layout: auto;
    width: 100%;
    min-width: 1200px; /* ضمان عرض كافي للأزرار */
}

/* تحسين responsive للأزرار */
@media (max-width: 768px) {
    .btn-group .btn {
        width: 28px;
        height: 24px;
        padding: 0.125rem 0.25rem;
    }

    .btn-group .btn i {
        font-size: 0.7rem;
    }

    .table td:last-child {
        min-width: 240px;
    }
}

.table td:nth-child(4) {
    max-width: 150px;
    font-size: 0.85rem;
    color: #6c757d;
}

.table td:nth-child(4) small {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* تحسين عرض تفاصيل البضاعة */
.cargo-details {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 0.85rem;
    color: #6c757d;
}

/* تحسين عرض التاريخ */
.shipment-date {
    font-size: 0.85rem;
    color: #6c757d;
    white-space: nowrap;
}

/* تحسين عرض رقم التتبع */
.tracking-number {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #007bff;
    font-size: 0.9rem;
    background: rgba(0, 123, 255, 0.1);
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    border: 1px solid rgba(0, 123, 255, 0.2);
    display: inline-block;
}

.tracking-number:hover {
    background: rgba(0, 123, 255, 0.15);
    border-color: rgba(0, 123, 255, 0.3);
}

.tracking-number i {
    font-size: 0.8rem;
}

.tracking-number .copy-icon {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.tracking-number:hover .copy-icon {
    opacity: 1;
}

/* تحسينات للتوست */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* تحسين الجدول العام */
.table th {
    font-weight: 600;
    color: #495057;
    border-top: none;
    white-space: nowrap;
    font-size: 0.85rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.85rem;
}

/* تحسينات للأعمدة الجديدة */
.container-numbers {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 0.8rem;
    color: #6c757d;
}

.table .badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
}

.table .text-center {
    text-align: center !important;
}

.table .text-muted {
    color: #6c757d !important;
    font-size: 0.8rem;
}

/* تحسينات لحالات الشحنة والإفراج */
.shipment-status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
}

/* تنسيق خلية الحالة القابلة للنقر */
.shipment-status-badge.clickable {
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.shipment-status-badge.clickable:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    opacity: 0.9;
}

.shipment-status-badge.clickable:active {
    transform: scale(0.98);
}

.release-status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
}

/* ألوان مخصصة لحالات الشحنة */
.shipment-status-badge.bg-draft {
    background-color: #6c757d !important;
    color: white;
}

.shipment-status-badge.bg-confirmed {
    background-color: #0d6efd !important;
    color: white;
}

.shipment-status-badge.bg-in_transit {
    background-color: #fd7e14 !important;
    color: white;
}

.shipment-status-badge.bg-ready_pickup {
    background-color: #ffc107 !important;
    color: #212529;
}

.shipment-status-badge.bg-delivered {
    background-color: #198754 !important;
    color: white;
}

.shipment-status-badge.bg-cancelled {
    background-color: #dc3545 !important;
    color: white;
}

/* ألوان مخصصة لحالات الإفراج */
.release-status-badge.bg-pending {
    background-color: #6c757d !important;
    color: white;
}

.release-status-badge.bg-documents_review {
    background-color: #0dcaf0 !important;
    color: #212529;
}

.release-status-badge.bg-payment_verification {
    background-color: #fd7e14 !important;
    color: white;
}

.release-status-badge.bg-quality_check {
    background-color: #6f42c1 !important;
    color: white;
}

.release-status-badge.bg-approved {
    background-color: #20c997 !important;
    color: white;
}

.release-status-badge.bg-released {
    background-color: #198754 !important;
    color: white;
}

.release-status-badge.bg-on_hold {
    background-color: #dc3545 !important;
    color: white;
}

.release-status-badge.bg-rejected {
    background-color: #721c24 !important;
    color: white;
}

/* تحسينات شريط التصفية المتقدم */
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

#filterCard .card-header {
    border-radius: 0.375rem 0.375rem 0 0;
}

#filterCard .form-label {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

#filterCard .form-control, #filterCard .form-select {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

#filterCard .form-control:focus, #filterCard .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#filterCard .btn-group .btn {
    border-radius: 0.25rem;
    font-size: 0.8rem;
    padding: 0.375rem 0.75rem;
}

#filterCard .btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.filter-transition {
    transition: all 0.3s ease;
}

#filterBody.collapsed {
    display: none;
}

/* تحسين عداد النتائج */
#filterResults {
    border-left: 4px solid #007bff;
    background: linear-gradient(90deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 123, 255, 0.05) 100%);
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    #filterCard .btn-group {
        display: flex;
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    #filterCard .btn-group .btn {
        flex: 1;
        min-width: auto;
    }

    #filterCard .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }
}

/* تحسين عرض الجدول للشاشات الصغيرة */
@media (max-width: 1200px) {
    .table th, .table td {
        font-size: 0.75rem;
        padding: 0.3rem;
    }

    .container-numbers {
        max-width: 80px;
    }

    .cargo-details {
        max-width: 100px;
    }
}

/* تحسينات للخريطة الجديدة */
.shipment-map-full {
    height: 400px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0 0 0.375rem 0.375rem;
    position: relative;
    overflow: hidden;
}

.realtime-indicator {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* تحسين مظهر الخريطة */
.shipment-map-full .text-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
}

.shipment-map-full i {
    color: #6c757d;
    opacity: 0.7;
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    .shipment-map-full {
        height: 300px;
    }
}
</style>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-2 text-white">
                                <i class="fas fa-ship me-2"></i>
                                نظام إدارة شحن الحاويات والبضائع
                            </h1>
                            <p class="mb-0 text-white-50">نظام متطور لإدارة الشحن البحري والجوي للحاويات والبضائع</p>
                        </div>
                        <div>
                            <a href="{{ url_for('shipments.new_cargo_shipment') }}" class="btn btn-light btn-lg">
                                <i class="fas fa-plus me-2"></i>
                                حجز شحنة جديدة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient rounded-circle p-3">
                                <i class="fas fa-boxes text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">إجمالي الشحنات</h6>
                            <h3 class="mb-0">{{ stats.total }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient rounded-circle p-3">
                                <i class="fas fa-clock text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">معلقة</h6>
                            <h3 class="mb-0">{{ stats.pending }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient rounded-circle p-3">
                                <i class="fas fa-truck text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">في الطريق</h6>
                            <h3 class="mb-0">{{ stats.in_transit }}</h3>
                            <span class="realtime-indicator text-success">
                                <i class="fas fa-circle"></i> مباشر
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient rounded-circle p-3">
                                <i class="fas fa-check-circle text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">مسلمة</h6>
                            <h3 class="mb-0">{{ stats.delivered }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <!-- Advanced Filter Bar -->
        <div class="col-12 mb-3">
            <div class="card border-0 shadow-sm" id="filterCard">
                <div class="card-header bg-gradient-primary text-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0 text-white">
                            <i class="fas fa-filter me-2"></i>
                            مرشحات البحث المتقدم
                        </h6>
                        <button class="btn btn-sm btn-outline-light" onclick="toggleFilterCard()" id="filterToggleBtn">
                            <i class="fas fa-chevron-up" id="filterToggleIcon"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body" id="filterBody">
                    <!-- Search and Date Filters -->
                    <div class="row mb-3">
                        <div class="col-md-4 mb-2">
                            <label class="form-label small text-muted">
                                <i class="fas fa-search me-1"></i>
                                البحث السريع (جميع الحقول)
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="quickSearch"
                                       placeholder="ابحث في رقم التتبع، المرسل، رقم البوليصة، الحاويات..."
                                       onkeyup="performQuickSearch()">
                                <button class="btn btn-outline-primary" type="button" id="voiceSearchBtn"
                                        onclick="toggleVoiceSearch()" title="البحث الصوتي">
                                    <i class="fas fa-microphone" id="voiceSearchIcon"></i>
                                </button>
                            </div>
                            <div id="voiceSearchStatus" class="small text-muted mt-1" style="display: none;">
                                <i class="fas fa-circle text-danger me-1 pulse"></i>
                                جاري الاستماع... تحدث الآن
                            </div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <label class="form-label small text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                من تاريخ
                            </label>
                            <input type="date" class="form-control" id="dateFrom" onchange="applyFilters()">
                        </div>
                        <div class="col-md-4 mb-2">
                            <label class="form-label small text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                إلى تاريخ
                            </label>
                            <input type="date" class="form-control" id="dateTo" onchange="applyFilters()">
                        </div>
                    </div>

                    <!-- Status and Dropdown Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3 mb-2">
                            <label class="form-label small text-muted">
                                <i class="fas fa-shipping-fast me-1"></i>
                                حالة الشحنة
                            </label>
                            <select class="form-select" id="shipmentStatusFilter" onchange="applyFilters()">
                                <option value="">جميع الحالات</option>
                                <option value="draft">مسودة</option>
                                <option value="confirmed">مؤكد</option>
                                <option value="in_transit">في الطريق</option>
                                <option value="ready_pickup">جاهز للاستلام</option>
                                <option value="delivered">تم التسليم</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-2">
                            <label class="form-label small text-muted">
                                <i class="fas fa-unlock-alt me-1"></i>
                                حالة الإفراج
                            </label>
                            <select class="form-select" id="releaseStatusFilter" onchange="applyFilters()">
                                <option value="">جميع الحالات</option>
                                <option value="pending">في انتظار الإفراج</option>
                                <option value="documents_review">مراجعة المستندات</option>
                                <option value="payment_verification">التحقق من المدفوعات</option>
                                <option value="quality_check">فحص الجودة</option>
                                <option value="approved">معتمد للإفراج</option>
                                <option value="released">تم الإفراج</option>
                                <option value="on_hold">محجوز مؤقت</option>
                                <option value="rejected">مرفوض الإفراج</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-2">
                            <label class="form-label small text-muted">
                                <i class="fas fa-ship me-1"></i>
                                خط الشحن
                            </label>
                            <select class="form-select" id="shippingLineFilter" onchange="applyFilters()">
                                <option value="">جميع خطوط الشحن</option>
                                <option value="MSC">Mediterranean Shipping Company</option>
                                <option value="MAERSK">Maersk Line</option>
                                <option value="COSCO">COSCO Shipping</option>
                                <option value="CMA CGM">CMA CGM</option>
                                <option value="HAPAG">Hapag-Lloyd</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-2">
                            <label class="form-label small text-muted">
                                <i class="fas fa-anchor me-1"></i>
                                ميناء الوصول
                            </label>
                            <select class="form-select" id="destinationPortFilter" onchange="applyFilters()">
                                <option value="">جميع الموانئ</option>
                                <option value="عدن">عدن - اليمن</option>
                                <option value="جدة">جدة - السعودية</option>
                                <option value="دبي">دبي - الإمارات</option>
                                <option value="الدمام">الدمام - السعودية</option>
                                <option value="الكويت">الكويت</option>
                            </select>
                        </div>
                    </div>

                    <!-- 🎨 زر تطبيق الألوان الواضح - محدث 2025 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="alert alert-warning text-center" style="background: linear-gradient(135deg, #ff6b6b, #feca57) !important; color: white !important; border: none !important; border-radius: 20px !important; box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4) !important; padding: 2rem !important; margin: 20px 0 !important;">
                                <h3 style="margin: 0 0 15px 0 !important; color: white !important; font-weight: bold !important; text-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;">
                                    <i class="fas fa-palette me-3" style="font-size: 2rem !important;"></i>
                                    تطبيق الألوان الجديدة
                                </h3>
                                <p style="color: white !important; margin-bottom: 20px !important; font-size: 1.1rem !important;">
                                    انقر على الزر أدناه لتطبيق الألوان الجميلة على البطاقات والجداول
                                </p>
                                <button type="button" class="btn btn-light btn-lg" onclick="forceApplyColors()" style="border-radius: 30px !important; font-weight: 700 !important; padding: 15px 40px !important; box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3) !important; font-size: 1.2rem !important;">
                                    <i class="fas fa-magic me-2" style="font-size: 1.3rem !important;"></i>
                                    🎨 تطبيق الألوان الآن 🎨
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Date Buttons and Actions -->
                    <div class="row">
                        <div class="col-md-8 mb-2">
                            <label class="form-label small text-muted">
                                <i class="fas fa-clock me-1"></i>
                                تصفية سريعة بالتاريخ
                            </label>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickDate('today')">اليوم</button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickDate('week')">هذا الأسبوع</button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickDate('month')">هذا الشهر</button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickDate('year')">هذا العام</button>
                            </div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <label class="form-label small text-muted">
                                <i class="fas fa-tools me-1"></i>
                                إجراءات
                            </label>
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="resetAllFilters()">
                                    <i class="fas fa-undo me-1"></i>
                                    إعادة تعيين
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="exportFilteredData()">
                                    <i class="fas fa-download me-1"></i>
                                    تصدير
                                </button>
                                <button type="button" class="btn btn-outline-info btn-sm" onclick="debugFilterSystem()">
                                    <i class="fas fa-bug me-1"></i>
                                    تشخيص
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Results Counter -->
                    <div class="row mt-2">
                        <div class="col-12">
                            <div class="alert alert-info py-2 mb-0" id="filterResults">
                                <i class="fas fa-info-circle me-2"></i>
                                <span id="resultsCount">عرض جميع الشحنات</span>
                                <span id="totalCount" class="ms-2 badge bg-primary">{{ recent_shipments|length }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Shipments -->
        <div class="col-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        شحنات الحاويات الحديثة
                    </h5>
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('shipments.dashboard_fullscreen') }}" class="btn btn-outline-success btn-sm" title="وضع ملء الشاشة">
                            <i class="fas fa-expand"></i>
                            <span class="d-none d-md-inline">ملء الشاشة</span>
                        </a>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshShipments()" title="تحديث البيانات">
                            <i class="fas fa-sync-alt me-1"></i>
                            <span class="d-none d-lg-inline">تحديث</span>
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="exportShipments()" title="تصدير البيانات">
                            <i class="fas fa-download me-1"></i>
                            <span class="d-none d-lg-inline">تصدير</span>
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="toggleFilterCard()" title="إظهار/إخفاء المرشحات">
                            <i class="fas fa-filter me-1"></i>
                            <span class="d-none d-lg-inline">المرشحات</span>
                        </button>
                        <a href="{{ url_for('shipments.new_cargo_shipment') }}" class="btn btn-success btn-sm" title="إنشاء شحنة جديدة">
                            <i class="fas fa-plus me-1"></i>
                            <span class="d-none d-lg-inline">شحنة جديدة</span>
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if recent_shipments %}
                        <div class="table-responsive">
                            <table id="shipmentsTable" class="table table-hover mb-0">
                                <thead class="bg-light">
                                    <tr>
                                        <th>تاريخ الشحنة</th>
                                        <th>رقم التتبع</th>
                                        <th>المرسل</th>
                                        <th>تفاصيل البضاعة</th>
                                        <th>عدد الطرود</th>
                                        <!-- تم إخفاء عمودي الوزن الإجمالي والوزن الصافي -->
                                        <!-- <th>الوزن الإجمالي</th> -->
                                        <!-- <th>الوزن الصافي</th> -->
                                        <th>ميناء الوصول</th>
                                        <th>رقم البوليصة</th>
                                        <th>أرقام الحاويات</th>
                                        <th>عدد الحاويات</th>
                                        <th>خط الشحن</th>
                                        <th>حالة الشحنة</th>
                                        <th>حالة الإفراج</th>
                                        <th>التقدم</th>
                                        <th style="min-width: 280px;">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for shipment in recent_shipments %}
                                    <!-- شحنة {{ loop.index }}: ID={{ shipment.id }}, رقم التتبع={{ shipment.tracking_number }} -->
                                    <tr>
                                        <td class="shipment-date">
                                            {{ shipment.shipment_date.strftime('%Y-%m-%d') if shipment.shipment_date else 'غير محدد' }}
                                        </td>
                                        <td>
                                            <span class="tracking-number"
                                                  title="انقر لنسخ رقم التتبع: {{ shipment.tracking_number }}"
                                                  onclick="copyTrackingNumber('{{ shipment.tracking_number }}')"
                                                  style="cursor: pointer;">
                                                <i class="fas fa-barcode me-1 text-muted"></i>
                                                {{ shipment.tracking_number }}
                                                <i class="fas fa-copy ms-1 text-muted copy-icon" style="font-size: 0.7rem;"></i>
                                            </span>
                                        </td>
                                        <td>{{ shipment.sender_name }}</td>
                                        <td class="cargo-details" title="{{ shipment.cargo_description or 'غير محدد' }}">
                                            {{ shipment.cargo_description[:30] + '...' if shipment.cargo_description and shipment.cargo_description|length > 30 else (shipment.cargo_description or 'غير محدد') }}
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-info">{{ shipment.total_packages or 0 }}</span>
                                        </td>
                                        <!-- <td class="text-center">
                                            {% if shipment.total_weight %}
                                                <span class="badge bg-primary">
                                                    <i class="fas fa-weight-hanging me-1"></i>
                                                    {{ "%.1f"|format(shipment.total_weight) }} كجم
                                                </span>
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td> -->
                                        <!-- <td class="text-center">
                                            {% if shipment.net_weight %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-balance-scale me-1"></i>
                                                    {{ "%.1f"|format(shipment.net_weight) }} كجم
                                                </span>
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td> -->
                                        <td class="text-muted">
                                            {{ shipment.destination_port or 'غير محدد' }}
                                        </td>
                                        <td class="text-muted">
                                            <small>{{ shipment.bill_of_lading_number or 'غير محدد' }}</small>
                                        </td>
                                        <td class="container-numbers" title="{{ shipment.container_numbers or 'غير محدد' }}">
                                            {{ shipment.container_numbers[:20] + '...' if shipment.container_numbers and shipment.container_numbers|length > 20 else (shipment.container_numbers or 'غير محدد') }}
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-secondary">{{ shipment.container_count or 0 }}</span>
                                        </td>
                                        <td class="text-muted">
                                            {{ shipment.shipping_line or 'غير محدد' }}
                                        </td>
                                        <td>
                                            <span class="badge shipment-status-badge bg-{{ shipment.shipment_status or 'secondary' }} status-badge clickable"
                                                  data-shipment-id="{{ shipment.id }}"
                                                  data-tracking-number="{{ shipment.tracking_number }}"
                                                  data-current-status="{{ shipment.shipment_status }}"
                                                  data-current-status-display="{{ shipment.shipment_status_display }}"
                                                  onclick="openStatusModal(this)"
                                                  title="انقر لتعديل حالة الشحنة: {{ shipment.shipment_status_display or 'غير محدد' }}">
                                                <i class="fas fa-edit me-1"></i>
                                                {{ shipment.shipment_status_display or 'غير محدد' }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge release-status-badge bg-{{ shipment.release_status or 'pending' }} clickable-status"
                                                  title="انقر لتعديل حالة الإفراج"
                                                  onclick="openReleaseStatusModal(this)"
                                                  data-shipment-id="{{ shipment.id }}"
                                                  data-tracking-number="{{ shipment.tracking_number }}"
                                                  data-current-release-status="{{ shipment.release_status or 'pending' }}"
                                                  data-current-release-status-display="{{ shipment.release_status_display or 'في انتظار الإفراج' }}"
                                                  style="cursor: pointer;">
                                                <i class="fas fa-unlock-alt me-1"></i>
                                                {{ shipment.release_status_display or 'في انتظار الإفراج' }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="progress progress-bar-custom">
                                                <div class="progress-bar bg-primary" style="width: {{ shipment.progress_percentage }}%"></div>
                                            </div>
                                            <small class="text-muted">{{ shipment.progress_percentage }}%</small>
                                        </td>
                                        <td style="white-space: nowrap; min-width: 280px;">
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{{ url_for('shipments.track_shipment', tracking_number=shipment.tracking_number) }}"
                                                   class="btn btn-outline-primary" title="تتبع">
                                                    <i class="fas fa-search"></i>
                                                </a>
                                                <a href="{{ url_for('shipments.new_cargo_shipment', shipment_id=shipment.id) }}"
                                                   class="btn btn-outline-warning" title="تعديل الشحنة"
                                                   onclick="
                                                       console.log('🔗 رابط التعديل:', this.href);
                                                       console.log('📋 معرف الشحنة:', {{ shipment.id }});
                                                       console.log('🎯 الرابط المتوقع:', '/shipments/cargo/edit/{{ shipment.id }}');
                                                       console.log('✅ سيتم فتح الرابط الآن...');
                                                       return true;
                                                   ">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                {% if shipment.cargo_id %}
                                                <a href="{{ url_for('shipments.cargo_documents', shipment_id=shipment.cargo_id) }}"
                                                   class="btn btn-outline-info" title="إدارة الوثائق">
                                                    <i class="fas fa-file-alt"></i>
                                                </a>
                                                <a href="{{ url_for('shipments.items_tracking', shipment_id=shipment.id) }}"
                                                   class="btn btn-outline-secondary" title="تتبع الأصناف">
                                                    <i class="fas fa-boxes"></i>
                                                </a>
                                                {% endif %}
                                                <button class="btn btn-outline-info" onclick="showShipmentMap('{{ shipment.tracking_number }}')" title="الخريطة">
                                                    <i class="fas fa-map-marker-alt"></i>
                                                </button>
                                                <button class="btn btn-outline-success" onclick="sendNotification('{{ shipment.id }}')" title="إشعار">
                                                    <i class="fas fa-bell"></i>
                                                </button>

                                                <button class="btn btn-outline-danger" onclick="deleteShipment('{{ shipment.id }}', '{{ shipment.tracking_number }}')" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-inbox text-muted mb-3" style="font-size: 3rem;"></i>
                            <h5 class="text-muted">لا توجد شحنات</h5>
                            <p class="text-muted">لم يتم إنشاء أي شحنات بعد</p>
                            <a href="{{ url_for('shipments.new_shipment') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                إنشاء شحنة جديدة
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>


    </div>
</div>

<!-- Live Tracking Map Section -->
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-map text-primary me-2"></i>
                        خريطة التتبع المباشر
                        <span class="realtime-indicator text-success ms-2">
                            <i class="fas fa-circle"></i> مباشر
                        </span>
                    </h5>
                    <small class="text-muted">تتبع جميع الشحنات النشطة في الوقت الفعلي</small>
                </div>
                <div class="card-body p-0">
                    <div id="liveTrackingMap" style="height: 400px; position: relative; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <!-- خريطة التتبع المباشر -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tracking Modal -->
<div class="modal fade" id="trackingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تتبع الشحنة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">رقم التتبع</label>
                    <input type="text" class="form-control" id="trackingNumberInput" placeholder="أدخل رقم التتبع">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="trackShipment()">تتبع</button>
            </div>
        </div>
    </div>
</div>

<script>
// متغيرات عامة للتصفية
let allShipments = [];
let filteredShipments = [];

// تطبيق جميع المرشحات - تعريف مبكر لحل مشكلة ReferenceError
function applyFilters() {
    console.log('🔍 تطبيق جميع المرشحات...');

    if (!allShipments || allShipments.length === 0) {
        console.log('⚠️ لا توجد بيانات مجمعة، جمع البيانات أولاً...');
        collectAllShipments();
    }

    filteredShipments = [...allShipments];

    // تطبيق البحث السريع أولاً
    const searchTerm = document.getElementById('quickSearch')?.value?.toLowerCase()?.trim() || '';
    if (searchTerm !== '') {
        console.log(`🔍 تطبيق البحث السريع: "${searchTerm}"`);
        filteredShipments = filteredShipments.filter(shipment => {
            return shipment.searchText.includes(searchTerm);
        });
        console.log(`📊 بعد البحث السريع: ${filteredShipments.length} شحنة`);
    }

    // تطبيق المرشحات الأخرى
    applyOtherFilters();
    updateDisplay();
}

// تطبيق المرشحات الأخرى (غير البحث السريع)
function applyOtherFilters() {
    // مرشح حالة الشحنة
    const shipmentStatus = document.getElementById('statusFilter')?.value;
    if (shipmentStatus) {
        console.log(`🔍 تطبيق مرشح الحالة: ${shipmentStatus}`);

        const beforeCount = filteredShipments.length;
        filteredShipments = filteredShipments.filter(shipment => {
            const found = shipment.shipmentStatus.toLowerCase().includes(shipmentStatus.toLowerCase()) ||
                         shipment.searchText.includes(shipmentStatus.toLowerCase());
            return found;
        });

        console.log(`📊 مرشح الحالة: ${beforeCount} → ${filteredShipments.length}`);
    }

    // يمكن إضافة مرشحات أخرى هنا
}

// تحديث العرض
function updateDisplay() {
    if (!allShipments || !filteredShipments) {
        console.log('⚠️ البيانات غير جاهزة للعرض');
        return;
    }

    console.log(`🔄 تحديث العرض: إخفاء ${allShipments.length} وإظهار ${filteredShipments.length}`);

    // إخفاء جميع الصفوف والبطاقات
    allShipments.forEach((shipment, index) => {
        if (shipment.element) {
            shipment.element.style.display = 'none';
        }
        // إخفاء البطاقات أيضاً إذا وجدت
        if (shipment.cardElement) {
            shipment.cardElement.style.display = 'none';
        }
    });

    // إظهار الصفوف والبطاقات المفلترة
    filteredShipments.forEach((shipment, index) => {
        if (shipment.element) {
            shipment.element.style.display = '';
        }
        // إظهار البطاقات أيضاً إذا وجدت
        if (shipment.cardElement) {
            shipment.cardElement.style.display = '';
        }
    });

    // تحديث عداد النتائج
    updateResultsCounter();

    console.log(`📊 تم عرض ${filteredShipments.length} من ${allShipments.length} شحنة`);

    // إضافة تأثير بصري للتحديث
    const tableBody = document.querySelector('#shipmentsTable tbody');
    if (tableBody) {
        tableBody.style.opacity = '0.7';
        setTimeout(() => {
            tableBody.style.opacity = '1';
        }, 200);
    }

    // تأثير بصري للبطاقات أيضاً
    const cardsContainer = document.querySelector('.cards-container') ||
                          document.querySelector('.shipment-cards') ||
                          document.querySelector('[class*="card"]').parentElement;
    if (cardsContainer) {
        cardsContainer.style.opacity = '0.7';
        setTimeout(() => {
            cardsContainer.style.opacity = '1';
        }, 200);
    }
}

// جمع جميع الشحنات من الجدول والبطاقات
function collectAllShipments() {
    allShipments = [];

    // جمع البيانات من الجدول
    const rows = document.querySelectorAll('#shipmentsTable tbody tr');
    console.log(`🔍 جمع البيانات من ${rows.length} صف...`);

    // جمع البيانات من البطاقات أيضاً
    const cards = document.querySelectorAll('.shipment-card, .card[data-shipment], [class*="shipment"][class*="card"]');
    console.log(`🔍 جمع البيانات من ${cards.length} بطاقة...`);

    rows.forEach((row, index) => {
        const cells = row.querySelectorAll('td');
        if (cells.length > 0) {
            // استخراج النص من العناصر المعقدة
            const trackingNumberElement = cells[1]?.querySelector('.tracking-number');
            const trackingNumber = trackingNumberElement ?
                trackingNumberElement.textContent.replace(/\s+/g, ' ').trim() :
                cells[1]?.textContent?.trim() || '';

            const shipmentStatusElement = cells[10]?.querySelector('.shipment-status-badge');
            const shipmentStatus = shipmentStatusElement ?
                shipmentStatusElement.textContent.replace(/\s+/g, ' ').trim() :
                cells[10]?.textContent?.trim() || '';

            const shipment = {
                element: row,
                index: index,
                trackingNumber: trackingNumber,
                shipmentStatus: shipmentStatus,
                searchText: [
                    cells[0]?.textContent?.trim() || '',
                    trackingNumber,
                    cells[2]?.textContent?.trim() || '',
                    cells[3]?.textContent?.trim() || '',
                    shipmentStatus
                ].join(' ').toLowerCase()
            };

            allShipments.push(shipment);
        }
    });

    // معالجة البطاقات أيضاً
    cards.forEach((card, index) => {
        // البحث عن رقم التتبع في البطاقة
        const trackingElement = card.querySelector('[class*="tracking"], [class*="number"], .card-title, h5, h6') ||
                               card.querySelector('strong, b, .fw-bold');
        const trackingNumber = trackingElement ? trackingElement.textContent.trim() : '';

        // جمع كل النص من البطاقة للبحث
        const cardText = card.textContent.toLowerCase().trim();

        // التحقق من عدم وجود هذه البطاقة في القائمة (تجنب التكرار)
        const existingShipment = allShipments.find(s => s.trackingNumber === trackingNumber && trackingNumber !== '');

        if (!existingShipment && cardText) {
            const shipment = {
                element: null, // لا يوجد صف في الجدول
                cardElement: card, // عنصر البطاقة
                index: allShipments.length + index,
                trackingNumber: trackingNumber,
                searchText: cardText
            };

            allShipments.push(shipment);
        } else if (existingShipment) {
            // ربط البطاقة بالشحنة الموجودة
            existingShipment.cardElement = card;
        }
    });

    filteredShipments = [...allShipments];
    console.log(`📊 تم جمع ${allShipments.length} شحنة بنجاح (${rows.length} صف + ${cards.length} بطاقة)`);
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 تهيئة نظام التصفية...');
    console.log('📍 الصفحة محدثة - يجب أن ترى زر الألوان الكبير الآن!');

    // جمع البيانات من الجدول
    setTimeout(() => {
        collectAllShipments();
        console.log('✅ تم تهيئة نظام التصفية بنجاح');
    }, 500);

    // إجبار تطبيق الألوان على البطاقات
    setTimeout(() => {
        forceApplyColors();
        console.log('🎨 تم تطبيق الألوان تلقائياً');
    }, 100);

    // التأكد من وجود الزر
    setTimeout(() => {
        const colorButton = document.querySelector('button[onclick="forceApplyColors()"]');
        if (colorButton) {
            console.log('✅ زر تطبيق الألوان موجود ومرئي!');
        } else {
            console.log('❌ زر تطبيق الألوان غير موجود!');
        }
    }, 1000);
});

// إجبار تطبيق الألوان
function forceApplyColors() {
    console.log('🎨 بدء تطبيق الألوان يدوياً...');
    // البطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.style.background = 'linear-gradient(145deg, #ffffff 0%, #f1f3f4 100%)';
        card.style.borderRadius = '20px';
        card.style.boxShadow = '0 15px 35px rgba(0, 0, 0, 0.2)';
        card.style.border = 'none';
        card.style.marginBottom = '25px';
        card.style.overflow = 'hidden';
    });

    // رؤوس البطاقات
    const cardHeaders = document.querySelectorAll('.card-header');
    cardHeaders.forEach(header => {
        header.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        header.style.color = 'white';
        header.style.borderRadius = '20px 20px 0 0';
        header.style.border = 'none';
        header.style.padding = '1.5rem 2rem';

        // العناوين داخل الرأس
        const titles = header.querySelectorAll('h1, h2, h3, h4, h5, h6');
        titles.forEach(title => {
            title.style.color = 'white';
            title.style.margin = '0';
            title.style.fontWeight = '600';
            title.style.textShadow = '0 2px 4px rgba(0, 0, 0, 0.3)';
        });

        // الأيقونات
        const icons = header.querySelectorAll('i');
        icons.forEach(icon => {
            icon.style.color = 'rgba(255, 255, 255, 0.9)';
            icon.style.marginLeft = '10px';
        });
    });

    // محتوى البطاقات
    const cardBodies = document.querySelectorAll('.card-body');
    cardBodies.forEach(body => {
        body.style.background = 'linear-gradient(145deg, #ffffff 0%, #fafbfc 100%)';
        body.style.padding = '2rem';
    });

    // رؤوس الجداول
    const tableHeaders = document.querySelectorAll('.table thead th');
    tableHeaders.forEach(th => {
        th.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        th.style.color = 'white';
        th.style.border = 'none';
        th.style.fontWeight = '600';
        th.style.padding = '1.2rem 1rem';
        th.style.textShadow = '0 1px 2px rgba(0, 0, 0, 0.3)';
    });

    // الأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(btn => {
        btn.style.borderRadius = '25px';
        btn.style.border = 'none';

        if (btn.classList.contains('btn-primary')) {
            btn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            btn.style.boxShadow = '0 6px 20px rgba(102, 126, 234, 0.4)';
        } else if (btn.classList.contains('btn-success')) {
            btn.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
        } else if (btn.classList.contains('btn-warning')) {
            btn.style.background = 'linear-gradient(135deg, #ffc107 0%, #fd7e14 100%)';
        } else if (btn.classList.contains('btn-danger')) {
            btn.style.background = 'linear-gradient(135deg, #dc3545 0%, #e83e8c 100%)';
        } else if (btn.classList.contains('btn-info')) {
            btn.style.background = 'linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%)';
        }
    });

    // الشارات
    const badges = document.querySelectorAll('.badge');
    badges.forEach(badge => {
        badge.style.borderRadius = '20px';
        badge.style.padding = '0.6rem 1.2rem';
        badge.style.fontWeight = '600';
        badge.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';

        if (badge.classList.contains('bg-success')) {
            badge.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
        } else if (badge.classList.contains('bg-warning')) {
            badge.style.background = 'linear-gradient(135deg, #ffc107 0%, #fd7e14 100%)';
        } else if (badge.classList.contains('bg-danger')) {
            badge.style.background = 'linear-gradient(135deg, #dc3545 0%, #e83e8c 100%)';
        } else if (badge.classList.contains('bg-info')) {
            badge.style.background = 'linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%)';
        } else if (badge.classList.contains('bg-primary')) {
            badge.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        }
    });

    console.log('✅ تم تطبيق الألوان بنجاح!');

    // إظهار رسالة نجاح
    const alertDiv = document.querySelector('.alert-info');
    if (alertDiv) {
        alertDiv.className = 'alert alert-success d-flex align-items-center';
        alertDiv.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            <span>تم تطبيق الألوان بنجاح! يجب أن ترى الآن البطاقات ملونة.</span>
        `;

        // إعادة الرسالة الأصلية بعد 3 ثوان
        setTimeout(() => {
            alertDiv.className = 'alert alert-info d-flex align-items-center';
            alertDiv.innerHTML = \`
                <i class="fas fa-palette me-2"></i>
                <span class="me-3">لتطبيق الألوان الجديدة على البطاقات:</span>
                <button type="button" class="btn btn-primary" onclick="forceApplyColors()">
                    <i class="fas fa-magic me-1"></i>
                    تطبيق الألوان الآن
                </button>
            \`;
        }, 3000);
    }
}

// تحديث عداد النتائج
function updateResultsCounter() {
    const resultsCount = document.getElementById('resultsCount');
    const totalCount = document.getElementById('totalCount');

    if (filteredShipments.length === allShipments.length) {
        resultsCount.textContent = 'عرض جميع الشحنات';
    } else {
        resultsCount.textContent = `عرض ${filteredShipments.length} من ${allShipments.length} شحنة`;
    }

    totalCount.textContent = filteredShipments.length;

    // تحديث badge في header الجدول
    const shipmentCountBadge = document.querySelector('.shipment-count');
    if (shipmentCountBadge) {
        shipmentCountBadge.textContent = filteredShipments.length;
    }
}

// تعيين تاريخ سريع
function setQuickDate(period) {
    const today = new Date();
    const dateFrom = document.getElementById('dateFrom');
    const dateTo = document.getElementById('dateTo');

    dateTo.value = today.toISOString().split('T')[0];

    switch(period) {
        case 'today':
            dateFrom.value = today.toISOString().split('T')[0];
            break;
        case 'week':
            const weekAgo = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
            dateFrom.value = weekAgo.toISOString().split('T')[0];
            break;
        case 'month':
            const monthAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
            dateFrom.value = monthAgo.toISOString().split('T')[0];
            break;
        case 'year':
            const yearAgo = new Date(today.getTime() - (365 * 24 * 60 * 60 * 1000));
            dateFrom.value = yearAgo.toISOString().split('T')[0];
            break;
    }

    applyFilters();
}

// إعادة تعيين جميع المرشحات
function resetAllFilters() {
    document.getElementById('quickSearch').value = '';
    document.getElementById('dateFrom').value = '';
    document.getElementById('dateTo').value = '';
    document.getElementById('shipmentStatusFilter').value = '';
    document.getElementById('releaseStatusFilter').value = '';
    document.getElementById('shippingLineFilter').value = '';
    document.getElementById('destinationPortFilter').value = '';

    filteredShipments = [...allShipments];
    updateDisplay();
    clearFilterSettings();

    showToast('تم إعادة تعيين جميع المرشحات', 'success');
}

// تصدير البيانات المفلترة
function exportFilteredData() {
    if (filteredShipments.length === 0) {
        showToast('لا توجد بيانات للتصدير', 'warning');
        return;
    }

    // إنشاء CSV
    const headers = [
        'تاريخ الشحنة', 'رقم التتبع', 'المرسل', 'تفاصيل البضاعة',
        'عدد الطرود', 'ميناء الوصول', 'رقم البوليصة', 'أرقام الحاويات',
        'عدد الحاويات', 'خط الشحن', 'حالة الشحنة', 'حالة الإفراج'
    ];

    let csvContent = headers.join(',') + '\n';

    filteredShipments.forEach(shipment => {
        const row = [
            shipment.shipmentDate,
            shipment.trackingNumber,
            shipment.sender,
            shipment.cargoDetails,
            shipment.packageCount,
            shipment.destinationPort,
            shipment.billOfLading,
            shipment.containerNumbers,
            shipment.containerCount,
            shipment.shippingLine,
            shipment.shipmentStatus,
            shipment.releaseStatus
        ].map(field => `"${field.replace(/"/g, '""')}"`);

        csvContent += row.join(',') + '\n';
    });

    // تحميل الملف
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `shipments_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showToast(`تم تصدير ${filteredShipments.length} شحنة بنجاح`, 'success');
}

// طي/فتح شريط التصفية
function toggleFilterCard() {
    const filterBody = document.getElementById('filterBody');
    const toggleIcon = document.getElementById('filterToggleIcon');

    if (filterBody.style.display === 'none') {
        filterBody.style.display = 'block';
        toggleIcon.className = 'fas fa-chevron-up';
    } else {
        filterBody.style.display = 'none';
        toggleIcon.className = 'fas fa-chevron-down';
    }
}

// حفظ إعدادات المرشحات
function saveFilterSettings() {
    const settings = {
        quickSearch: document.getElementById('quickSearch').value,
        dateFrom: document.getElementById('dateFrom').value,
        dateTo: document.getElementById('dateTo').value,
        shipmentStatus: document.getElementById('shipmentStatusFilter').value,
        releaseStatus: document.getElementById('releaseStatusFilter').value,
        shippingLine: document.getElementById('shippingLineFilter').value,
        destinationPort: document.getElementById('destinationPortFilter').value
    };

    localStorage.setItem('shipmentFilters', JSON.stringify(settings));
}

// تحميل إعدادات المرشحات
function loadFilterSettings() {
    const saved = localStorage.getItem('shipmentFilters');
    if (saved) {
        const settings = JSON.parse(saved);

        document.getElementById('quickSearch').value = settings.quickSearch || '';
        document.getElementById('dateFrom').value = settings.dateFrom || '';
        document.getElementById('dateTo').value = settings.dateTo || '';
        document.getElementById('shipmentStatusFilter').value = settings.shipmentStatus || '';
        document.getElementById('releaseStatusFilter').value = settings.releaseStatus || '';
        document.getElementById('shippingLineFilter').value = settings.shippingLine || '';
        document.getElementById('destinationPortFilter').value = settings.destinationPort || '';

        // لا نطبق المرشحات تلقائياً - المستخدم يطبقها حسب الحاجة
        console.log('📋 تم استرداد إعدادات المرشحات المحفوظة بدون تطبيقها');
    }
}

// مسح إعدادات المرشحات
function clearFilterSettings() {
    localStorage.removeItem('shipmentFilters');
}

// عرض رسالة toast
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = 'toast-notification';

    const iconMap = {
        'success': 'fas fa-check-circle text-success',
        'warning': 'fas fa-exclamation-triangle text-warning',
        'error': 'fas fa-times-circle text-danger',
        'info': 'fas fa-info-circle text-info'
    };

    const colorMap = {
        'success': { bg: '#d4edda', border: '#c3e6cb', text: '#155724' },
        'warning': { bg: '#fff3cd', border: '#ffeaa7', text: '#856404' },
        'error': { bg: '#f8d7da', border: '#f5c6cb', text: '#721c24' },
        'info': { bg: '#d1ecf1', border: '#bee5eb', text: '#0c5460' }
    };

    const colors = colorMap[type];

    toast.innerHTML = `
        <i class="${iconMap[type]} me-2"></i>
        ${message}
    `;

    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${colors.bg};
        border: 1px solid ${colors.border};
        color: ${colors.text};
        border-radius: 0.375rem;
        padding: 0.75rem 1rem;
        box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
        z-index: 1060;
        font-size: 0.875rem;
        animation: slideInRight 0.3s ease-out;
        max-width: 300px;
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// عد الصفوف في الجدول
function countTableRows() {
    const tbody = document.querySelector('table tbody');
    const rows = tbody ? tbody.querySelectorAll('tr') : [];
    console.log(`📊 عدد صفوف الجدول في HTML: ${rows.length}`);

    rows.forEach((row, index) => {
        const trackingCell = row.querySelector('.tracking-number');
        const trackingNumber = trackingCell ? trackingCell.textContent.trim() : 'غير محدد';
        console.log(`   الصف ${index + 1}: ${trackingNumber}`);
    });

    return rows.length;
}

// تشخيص نظام التصفية
function debugFilterSystem() {
    console.log('🔧 تشخيص نظام التصفية...');

    // عد الصفوف أولاً
    const tableRowCount = countTableRows();

    const debugInfo = {
        totalShipments: allShipments.length,
        filteredShipments: filteredShipments.length,
        searchTerm: document.getElementById('quickSearch').value,
        filters: {
            dateFrom: document.getElementById('dateFrom').value,
            dateTo: document.getElementById('dateTo').value,
            shipmentStatus: document.getElementById('shipmentStatusFilter').value,
            releaseStatus: document.getElementById('releaseStatusFilter').value,
            shippingLine: document.getElementById('shippingLineFilter').value,
            destinationPort: document.getElementById('destinationPortFilter').value
        },
        sampleShipment: allShipments[0] || null
    };

    console.table(debugInfo);

    // اختبار البحث السريع
    if (allShipments.length > 0) {
        const testSearch = allShipments[0].trackingNumber.substring(0, 3);
        console.log(`🧪 اختبار البحث بـ "${testSearch}"`);

        const testResults = allShipments.filter(shipment =>
            shipment.searchText.includes(testSearch.toLowerCase())
        );

        console.log(`📊 نتائج الاختبار: ${testResults.length} شحنة`);

        // اختبار خاص لرقم البوليصة
        const firstShipment = allShipments[0];
        if (firstShipment.billOfLading) {
            const bolTest = firstShipment.billOfLading.substring(0, 4);
            console.log(`🧪 اختبار البحث برقم البوليصة: "${bolTest}"`);

            const bolResults = allShipments.filter(shipment =>
                shipment.searchText.includes(bolTest.toLowerCase())
            );

            console.log(`📊 نتائج اختبار البوليصة: ${bolResults.length} شحنة`);

            if (bolResults.length > 0) {
                console.log('✅ البحث في رقم البوليصة يعمل');
                showToast(`البحث يعمل! رقم البوليصة "${bolTest}" تم العثور عليه`, 'success');
            } else {
                console.log('❌ البحث في رقم البوليصة لا يعمل');
                showToast('مشكلة في البحث برقم البوليصة', 'error');
            }
        }

        if (testResults.length > 0) {
            console.log('✅ البحث العام يعمل بشكل صحيح');
        } else {
            console.log('❌ البحث العام لا يعمل');
            showToast('مشكلة في البحث - لم يتم العثور على نتائج', 'error');
        }
    }

    // عرض معلومات التشخيص
    const debugMessage = `
        📊 إجمالي الشحنات: ${debugInfo.totalShipments}
        🔍 الشحنات المفلترة: ${debugInfo.filteredShipments}
        🔎 مصطلح البحث: "${debugInfo.searchTerm}"
    `;

    alert(debugMessage);
}

// تهيئة خريطة التتبع المباشر
function initLiveMap() {
    const mapContainer = document.getElementById('liveTrackingMap');
    if (!mapContainer) return;

    // إنشاء خريطة تفاعلية بسيطة
    createSimpleMap(mapContainer);
}

// إنشاء خريطة بسيطة مع بيانات الشحنات
function createSimpleMap(container) {
    // تحميل بيانات الشحنات
    fetch('/shipments/api/live-tracking-data')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderInteractiveMap(container, data.shipments, data.stats);
            } else {
                renderMapWithDemo(container);
            }
        })
        .catch(error => {
            console.error('خطأ في جلب البيانات:', error);
            renderMapWithDemo(container);
        });
}

// رسم الخريطة التفاعلية
function renderInteractiveMap(container, shipments, stats) {
    container.innerHTML = `
        <div style="height: 100%; position: relative; background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); overflow: hidden;">
            <!-- خلفية الخريطة -->
            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1000 600\"><defs><pattern id=\"grid\" width=\"50\" height=\"50\" patternUnits=\"userSpaceOnUse\"><path d=\"M 50 0 L 0 0 0 50\" fill=\"none\" stroke=\"%23ffffff\" stroke-width=\"0.5\" opacity=\"0.1\"/></pattern></defs><rect width=\"100%\" height=\"100%\" fill=\"url(%23grid)\"/></svg>'); opacity: 0.3;"></div>

            <!-- مؤشر التحديث المباشر -->
            <div style="position: absolute; top: 15px; left: 15px; background: rgba(40, 167, 69, 0.9); color: white; padding: 8px 12px; border-radius: 20px; font-size: 0.85rem; display: flex; align-items: center; gap: 8px; z-index: 100;">
                <div style="width: 8px; height: 8px; background: #fff; border-radius: 50%; animation: pulse 1.5s infinite;"></div>
                <span>مباشر</span>
            </div>

            <!-- أدوات التحكم -->
            <div style="position: absolute; top: 15px; right: 15px; display: flex; flex-direction: column; gap: 8px; z-index: 100;">
                <button onclick="refreshMapData()" style="background: white; border: 1px solid #ddd; border-radius: 6px; padding: 8px; cursor: pointer; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" title="تحديث البيانات">
                    <i class="fas fa-sync-alt"></i>
                </button>
                <button onclick="toggleMapView()" style="background: white; border: 1px solid #ddd; border-radius: 6px; padding: 8px; cursor: pointer; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" title="تغيير العرض">
                    <i class="fas fa-expand-arrows-alt"></i>
                </button>
            </div>

            <!-- إحصائيات الخريطة -->
            <div style="position: absolute; bottom: 15px; left: 15px; background: rgba(255, 255, 255, 0.95); padding: 12px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); font-size: 0.85rem; z-index: 100;">
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 6px;">
                    <div style="width: 12px; height: 12px; background: #28a745; border-radius: 50%; border: 2px solid white; box-shadow: 0 1px 2px rgba(0,0,0,0.2);"></div>
                    <span>في الطريق: ${stats?.in_transit || 0}</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 6px;">
                    <div style="width: 12px; height: 12px; background: #ffc107; border-radius: 50%; border: 2px solid white; box-shadow: 0 1px 2px rgba(0,0,0,0.2);"></div>
                    <span>متأخر: ${stats?.delayed || 0}</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 6px;">
                    <div style="width: 12px; height: 12px; background: #6c757d; border-radius: 50%; border: 2px solid white; box-shadow: 0 1px 2px rgba(0,0,0,0.2);"></div>
                    <span>وصل: ${stats?.arrived || 0}</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <div style="width: 12px; height: 12px; background: #17a2b8; border-radius: 50%; border: 2px solid white; box-shadow: 0 1px 2px rgba(0,0,0,0.2);"></div>
                    <span>قيد التحميل: ${stats?.loading || 0}</span>
                </div>
            </div>

            <!-- منطقة الشحنات -->
            <div id="shipmentsArea" style="position: absolute; top: 60px; left: 60px; right: 60px; bottom: 120px; z-index: 50;">
                ${renderShipmentMarkers(shipments)}
            </div>

            <!-- معلومات الشحنة المحددة -->
            <div id="shipmentInfo" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.15); display: none; z-index: 200; min-width: 300px;">
                <!-- سيتم ملؤها عند اختيار شحنة -->
            </div>
        </div>
    `;

    // إضافة الأنيميشن
    addMapAnimations();

    // بدء التحديث التلقائي
    startAutoRefresh();
}

// رسم علامات الشحنات
function renderShipmentMarkers(shipments) {
    if (!shipments || shipments.length === 0) {
        return '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: rgba(255,255,255,0.7); font-size: 1.1rem;"><div class="text-center"><i class="fas fa-ship mb-2" style="font-size: 2rem;"></i><br>لا توجد شحنات نشطة</div></div>';
    }

    let markersHtml = '';

    shipments.forEach((shipment, index) => {
        const color = getStatusColor(shipment.status);
        const x = 10 + (index % 6) * 15; // توزيع أفقي
        const y = 10 + Math.floor(index / 6) * 20; // توزيع عمودي

        markersHtml += `
            <div onclick="showShipmentInfo('${shipment.id}', '${shipment.shipment_number}', '${shipment.status}', '${shipment.current_location.name}', '${shipment.destination.name}', '${shipment.progress}', '${shipment.eta}', '${shipment.container_count}')"
                 style="position: absolute; left: ${x}%; top: ${y}%; cursor: pointer; z-index: 60;">
                <div style="background: ${color}; border: 3px solid white; border-radius: 50%; width: 24px; height: 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.3); display: flex; align-items: center; justify-content: center; animation: ${shipment.status === 'في الطريق' ? 'pulse 2s infinite' : 'none'};">
                    <i class="fas fa-ship" style="color: white; font-size: 10px;"></i>
                </div>
                <div style="background: rgba(0,0,0,0.8); color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.7rem; margin-top: 5px; white-space: nowrap; transform: translateX(-50%); margin-left: 50%;">
                    ${shipment.shipment_number}
                </div>
            </div>
        `;
    });

    return markersHtml;
}

// الحصول على لون الحالة
function getStatusColor(status) {
    const colors = {
        'في الطريق': '#28a745',
        'متأخر': '#ffc107',
        'وصل': '#6c757d',
        'مشكلة': '#dc3545',
        'قيد التحميل': '#17a2b8',
        'قيد التفريغ': '#fd7e14'
    };
    return colors[status] || '#007bff';
}

// عرض معلومات الشحنة
function showShipmentInfo(id, number, status, currentLocation, destination, progress, eta, containerCount) {
    const infoDiv = document.getElementById('shipmentInfo');
    if (!infoDiv) return;

    const statusColor = getStatusColor(status);

    infoDiv.innerHTML = `
        <div style="border-bottom: 1px solid #eee; padding-bottom: 10px; margin-bottom: 15px;">
            <h6 style="margin: 0; color: #333; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-ship" style="color: ${statusColor};"></i>
                ${number}
            </h6>
        </div>
        <div style="margin-bottom: 12px;">
            <span style="display: inline-block; padding: 4px 12px; background: ${statusColor}; color: white; border-radius: 15px; font-size: 0.8rem; font-weight: bold;">
                ${status}
            </span>
        </div>
        <div style="margin-bottom: 10px;">
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 6px;">
                <i class="fas fa-map-marker-alt" style="color: #007bff; width: 16px;"></i>
                <span style="font-size: 0.9rem;"><strong>الموقع:</strong> ${currentLocation}</span>
            </div>
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 6px;">
                <i class="fas fa-flag-checkered" style="color: #28a745; width: 16px;"></i>
                <span style="font-size: 0.9rem;"><strong>الوجهة:</strong> ${destination}</span>
            </div>
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 6px;">
                <i class="fas fa-percentage" style="color: #17a2b8; width: 16px;"></i>
                <span style="font-size: 0.9rem;"><strong>التقدم:</strong> ${progress}%</span>
            </div>
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 6px;">
                <i class="fas fa-clock" style="color: #ffc107; width: 16px;"></i>
                <span style="font-size: 0.9rem;"><strong>الوصول المتوقع:</strong> ${eta}</span>
            </div>
            <div style="display: flex; align-items: center; gap: 8px;">
                <i class="fas fa-cube" style="color: #6c757d; width: 16px;"></i>
                <span style="font-size: 0.9rem;"><strong>الحاويات:</strong> ${containerCount}</span>
            </div>
        </div>
        <div style="text-align: center; margin-top: 15px;">
            <button onclick="hideShipmentInfo()" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 0.85rem;">
                إغلاق
            </button>
            <button onclick="viewShipmentDetails('${id}')" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 0.85rem; margin-left: 8px;">
                عرض التفاصيل
            </button>
        </div>
    `;

    infoDiv.style.display = 'block';
}

// إخفاء معلومات الشحنة
function hideShipmentInfo() {
    const infoDiv = document.getElementById('shipmentInfo');
    if (infoDiv) {
        infoDiv.style.display = 'none';
    }
}

// عرض تفاصيل الشحنة
function viewShipmentDetails(shipmentId) {
    window.open(`/shipments/cargo/edit/${shipmentId}`, '_blank');
}

// رسم خريطة تجريبية
function renderMapWithDemo(container) {
    const demoShipments = [
        {
            id: 1,
            shipment_number: 'CRG20250812001',
            status: 'في الطريق',
            current_location: { name: 'مانيلا، الفلبين' },
            destination: { name: 'عدن، اليمن' },
            progress: 65,
            eta: '2025-08-20',
            container_count: 2
        },
        {
            id: 2,
            shipment_number: 'CRG20250812002',
            status: 'قيد التحميل',
            current_location: { name: 'دبي، الإمارات' },
            destination: { name: 'صنعاء، اليمن' },
            progress: 10,
            eta: '2025-08-25',
            container_count: 1
        },
        {
            id: 3,
            shipment_number: 'CRG20250812003',
            status: 'وصل',
            current_location: { name: 'عدن، اليمن' },
            destination: { name: 'عدن، اليمن' },
            progress: 100,
            eta: 'وصل',
            container_count: 3
        }
    ];

    const demoStats = {
        in_transit: 1,
        delayed: 0,
        arrived: 1,
        loading: 1
    };

    renderInteractiveMap(container, demoShipments, demoStats);
}

// إضافة الأنيميشن
function addMapAnimations() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        #shipmentInfo {
            animation: fadeIn 0.3s ease-out;
        }
    `;
    document.head.appendChild(style);
}

// تحديث بيانات الخريطة
function refreshMapData() {
    const btn = event.target;
    btn.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i>';

    setTimeout(() => {
        initLiveMap();
        btn.innerHTML = '<i class="fas fa-sync-alt"></i>';
        showToast('تم تحديث بيانات الخريطة', 'success');
    }, 1000);
}

// تغيير عرض الخريطة
function toggleMapView() {
    const container = document.getElementById('liveTrackingMap');
    if (container.style.height === '600px') {
        container.style.height = '400px';
    } else {
        container.style.height = '600px';
    }
}

// بدء التحديث التلقائي
let autoRefreshInterval;
function startAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }

    autoRefreshInterval = setInterval(() => {
        // تحديث صامت للبيانات
        fetch('/shipments/api/live-tracking-data')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث الإحصائيات فقط لتجنب إعادة رسم كامل
                    updateMapStats(data.stats);
                }
            })
            .catch(error => {
                console.log('تحديث صامت فشل:', error);
            });
    }, 30000); // كل 30 ثانية
}

// تحديث إحصائيات الخريطة
function updateMapStats(stats) {
    // تحديث الأرقام في الإحصائيات
    const statsContainer = document.querySelector('#liveTrackingMap [style*="bottom: 15px"]');
    if (statsContainer && stats) {
        const spans = statsContainer.querySelectorAll('span');
        if (spans.length >= 4) {
            spans[0].textContent = `في الطريق: ${stats.in_transit || 0}`;
            spans[1].textContent = `متأخر: ${stats.delayed || 0}`;
            spans[2].textContent = `وصل: ${stats.arrived || 0}`;
            spans[3].textContent = `قيد التحميل: ${stats.loading || 0}`;
        }
    }
}

// عرض خطأ في حالة فشل تحميل الخريطة
function showMapError() {
    const mapElement = document.getElementById('liveTrackingMap');
    if (mapElement) {
        mapElement.innerHTML = `
            <div class="d-flex align-items-center justify-content-center h-100 text-danger">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle mb-3" style="font-size: 3rem;"></i>
                    <h5 class="mb-2">خطأ في تحميل الخريطة</h5>
                    <p class="mb-3">فشل في تحميل نظام الخريطة التفاعلية</p>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-redo me-1"></i>
                        إعادة المحاولة
                    </button>
                </div>
            </div>
        `;
    }
}

// حساب عدد الشحنات النشطة
function getActiveShipmentsCount() {
    const rows = document.querySelectorAll('#shipmentsTable tbody tr');
    return rows.length || 0;
}

// نسخ رقم التتبع
function copyTrackingNumber(trackingNumber) {
    if (navigator.clipboard && window.isSecureContext) {
        // استخدام Clipboard API الحديث
        navigator.clipboard.writeText(trackingNumber).then(() => {
            showCopySuccess(trackingNumber);
        }).catch(err => {
            console.error('فشل في نسخ رقم التتبع:', err);
            fallbackCopyTextToClipboard(trackingNumber);
        });
    } else {
        // استخدام الطريقة التقليدية
        fallbackCopyTextToClipboard(trackingNumber);
    }
}

// طريقة بديلة لنسخ النص
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showCopySuccess(text);
        } else {
            console.error('فشل في نسخ النص');
        }
    } catch (err) {
        console.error('خطأ في نسخ النص:', err);
    }

    document.body.removeChild(textArea);
}

// عرض رسالة نجاح النسخ
function showCopySuccess(trackingNumber) {
    // إنشاء toast notification
    const toast = document.createElement('div');
    toast.className = 'toast-notification';
    toast.innerHTML = `
        <i class="fas fa-check-circle text-success me-2"></i>
        تم نسخ رقم التتبع: <strong>${trackingNumber}</strong>
    `;

    // إضافة CSS للـ toast
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        border: 1px solid #28a745;
        border-radius: 0.375rem;
        padding: 0.75rem 1rem;
        box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
        z-index: 1060;
        font-size: 0.875rem;
        color: #155724;
        background-color: #d4edda;
        border-color: #c3e6cb;
        animation: slideInRight 0.3s ease-out;
    `;

    document.body.appendChild(toast);

    // إزالة التوست بعد 3 ثوان
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// تم نقل جميع JavaScript functions إلى ملف shipments.js
    console.log('🔧 تهيئة نظام التصفية المتقدم...');

    // جمع جميع الشحنات من الجدول
    collectAllShipments();

    // تعيين التاريخ الافتراضي (آخر 30 يوم) بدون تطبيق المرشحات
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

    document.getElementById('dateTo').value = today.toISOString().split('T')[0];
    document.getElementById('dateFrom').value = thirtyDaysAgo.toISOString().split('T')[0];

    console.log('✅ تم تهيئة نظام التصفية بنجاح');
    console.log('📋 عرض جميع الشحنات بدون تطبيق مرشحات افتراضية');

    // تهيئة عرض جميع الشحنات
    filteredShipments = [...allShipments];
    updateDisplay();

    // عد الصفوف في الجدول للتشخيص
    setTimeout(() => {
        countTableRows();
    }, 500);
}

// جمع جميع الشحنات من الجدول
function collectAllShipments() {
    allShipments = [];
    const rows = document.querySelectorAll('#shipmentsTable tbody tr');

    console.log(`🔍 جمع البيانات من ${rows.length} صف...`);

    rows.forEach((row, index) => {
        const cells = row.querySelectorAll('td');
        if (cells.length > 0) {
            // استخراج النص من العناصر المعقدة
            const trackingNumberElement = cells[1]?.querySelector('.tracking-number');
            const trackingNumber = trackingNumberElement ?
                trackingNumberElement.textContent.replace(/\s+/g, ' ').trim() :
                cells[1]?.textContent?.trim() || '';

            const shipmentStatusElement = cells[10]?.querySelector('.shipment-status-badge');
            const shipmentStatus = shipmentStatusElement ?
                shipmentStatusElement.textContent.replace(/\s+/g, ' ').trim() :
                cells[10]?.textContent?.trim() || '';

            const releaseStatusElement = cells[11]?.querySelector('.release-status-badge');
            const releaseStatus = releaseStatusElement ?
                releaseStatusElement.textContent.replace(/\s+/g, ' ').trim() :
                cells[11]?.textContent?.trim() || '';

            const shipment = {
                element: row,
                index: index,
                shipmentDate: cells[0]?.textContent?.trim() || '',
                trackingNumber: trackingNumber,
                sender: cells[2]?.textContent?.trim() || '',
                cargoDetails: cells[3]?.textContent?.trim() || '',
                packageCount: cells[4]?.textContent?.trim() || '',
                destinationPort: cells[5]?.textContent?.trim() || '',
                billOfLading: cells[6]?.textContent?.trim() || '',
                containerNumbers: cells[7]?.textContent?.trim() || '',
                containerCount: cells[8]?.textContent?.trim() || '',
                shippingLine: cells[9]?.textContent?.trim() || '',
                shipmentStatus: shipmentStatus,
                releaseStatus: releaseStatus,
                // إضافة نص مجمع للبحث السريع مع رقم البوليصة
                searchText: [
                    cells[0]?.textContent?.trim() || '',           // تاريخ الشحنة
                    trackingNumber,                                // رقم التتبع
                    cells[2]?.textContent?.trim() || '',           // المرسل
                    cells[3]?.textContent?.trim() || '',           // تفاصيل البضاعة
                    cells[4]?.textContent?.trim() || '',           // عدد الطرود
                    cells[5]?.textContent?.trim() || '',           // ميناء الوصول
                    cells[6]?.textContent?.trim() || '',           // رقم البوليصة ✅
                    cells[7]?.textContent?.trim() || '',           // أرقام الحاويات
                    cells[8]?.textContent?.trim() || '',           // عدد الحاويات
                    cells[9]?.textContent?.trim() || '',           // خط الشحن
                    shipmentStatus,                                // حالة الشحنة
                    releaseStatus                                  // حالة الإفراج
                ].join(' ').toLowerCase()
            };

            allShipments.push(shipment);

            // تسجيل تفصيلي للشحنة الأولى للتشخيص
            if (index === 0) {
                console.log('📋 عينة من البيانات المجمعة:', {
                    trackingNumber: shipment.trackingNumber,
                    sender: shipment.sender,
                    billOfLading: shipment.billOfLading,
                    shipmentStatus: shipment.shipmentStatus,
                    releaseStatus: shipment.releaseStatus,
                    searchText: shipment.searchText.substring(0, 150) + '...'
                });

                // فحص خاص لحالة الشحنة
                console.log('🔍 تشخيص حالة الشحنة:', {
                    cellContent: cells[10]?.textContent?.trim(),
                    badgeContent: cells[10]?.querySelector('.shipment-status-badge')?.textContent?.trim(),
                    finalStatus: shipment.shipmentStatus
                });

                // فحص خاص لرقم البوليصة
                console.log('🔍 فحص رقم البوليصة:', {
                    billOfLadingRaw: cells[6]?.textContent,
                    billOfLadingTrimmed: cells[6]?.textContent?.trim(),
                    includesInSearch: shipment.searchText.includes(shipment.billOfLading.toLowerCase())
                });
            }
        }
    });

    filteredShipments = [...allShipments];
    console.log(`📊 تم جمع ${allShipments.length} شحنة بنجاح`);
}

// البحث السريع الفوري
function performQuickSearch() {
    const searchInput = document.getElementById('quickSearch');
    if (!searchInput) {
        console.warn('⚠️ حقل البحث السريع غير موجود');
        return;
    }

    const searchTerm = searchInput.value.toLowerCase().trim();
    console.log(`🔍 البحث عن: "${searchTerm}"`);

    // التأكد من وجود البيانات
    if (!allShipments || allShipments.length === 0) {
        console.log('📊 لا توجد بيانات شحنات، محاولة جمع البيانات...');
        collectAllShipments();

        // إذا لم تنجح عملية الجمع، استخدم البحث المباشر
        if (!allShipments || allShipments.length === 0) {
            console.log('🔄 استخدام البحث المباشر...');
            performDirectSearch(searchTerm);
            return;
        }
    }

    if (searchTerm === '') {
        // إذا كان البحث فارغ، أظهر جميع الشحنات
        console.log('🔄 البحث فارغ، عرض جميع الشحنات...');
        filteredShipments = [...allShipments];
        updateDisplay();
        return;
    }

    // البحث في النص المجمع
    filteredShipments = allShipments.filter(shipment => {
        const found = shipment.searchText.includes(searchTerm);

        // تسجيل تفصيلي للنتيجة الأولى
        if (found && filteredShipments.length === 0) {
            console.log('✅ تم العثور على تطابق:', {
                trackingNumber: shipment.trackingNumber,
                searchText: shipment.searchText.substring(0, 100) + '...'
            });
        }

        return found;
    });

    console.log(`📊 نتائج البحث: ${filteredShipments.length} من ${allShipments.length}`);

    // طبق المرشحات الأخرى على نتائج البحث
    applyOtherFilters();
    updateDisplay();
}

// البحث المباشر في الجدول والبطاقات (احتياطي)
function performDirectSearch(searchTerm) {
    console.log('🔍 تنفيذ البحث المباشر في الجدول والبطاقات...');

    let visibleCount = 0;

    // البحث في الجدول
    const table = document.querySelector('#shipmentsTable tbody');
    if (table) {
        const rows = table.querySelectorAll('tr');
        rows.forEach(row => {
            if (searchTerm === '') {
                row.style.display = '';
                visibleCount++;
            } else {
                const rowText = row.textContent.toLowerCase();
                if (rowText.includes(searchTerm)) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            }
        });
    }

    // البحث في البطاقات
    const cards = document.querySelectorAll('.shipment-card, .card[data-shipment], [class*="shipment"][class*="card"]');
    cards.forEach(card => {
        if (searchTerm === '') {
            card.style.display = '';
            if (!table) visibleCount++; // عد البطاقات فقط إذا لم يكن هناك جدول
        } else {
            const cardText = card.textContent.toLowerCase();
            if (cardText.includes(searchTerm)) {
                card.style.display = '';
                if (!table) visibleCount++; // عد البطاقات فقط إذا لم يكن هناك جدول
            } else {
                card.style.display = 'none';
            }
        }
    });

    console.log(`📊 البحث المباشر: عرض ${visibleCount} نتيجة`);

    // إظهار رسالة إذا لم توجد نتائج
    if (visibleCount === 0 && searchTerm !== '') {
        showNotification(`لم يتم العثور على نتائج للبحث: "${searchTerm}"`, 'warning');
    } else if (searchTerm !== '') {
        showNotification(`تم العثور على ${visibleCount} نتيجة`, 'success');
    }
}

// تطبيق جميع المرشحات
function applyFilters() {
    console.log('🔍 تطبيق جميع المرشحات...');

    filteredShipments = [...allShipments];

    // تطبيق البحث السريع أولاً
    const searchTerm = document.getElementById('quickSearch').value.toLowerCase().trim();
    if (searchTerm !== '') {
        console.log(`🔍 تطبيق البحث السريع: "${searchTerm}"`);
        filteredShipments = filteredShipments.filter(shipment => {
            return shipment.searchText.includes(searchTerm);
        });
        console.log(`📊 بعد البحث السريع: ${filteredShipments.length} شحنة`);
    }

    // تطبيق المرشحات الأخرى
    applyOtherFilters();
    updateDisplay();
    saveFilterSettings();
}

// تطبيق المرشحات الأخرى (غير البحث السريع)
function applyOtherFilters() {
    // مرشح التاريخ
    const dateFrom = document.getElementById('dateFrom').value;
    const dateTo = document.getElementById('dateTo').value;

    if (dateFrom || dateTo) {
        filteredShipments = filteredShipments.filter(shipment => {
            const shipmentDate = shipment.shipmentDate;
            if (!shipmentDate || shipmentDate === 'غير محدد') return true;

            const date = new Date(shipmentDate);
            if (isNaN(date.getTime())) return true;

            if (dateFrom && date < new Date(dateFrom)) return false;
            if (dateTo && date > new Date(dateTo)) return false;

            return true;
        });
    }

    // مرشح حالة الشحنة
    const shipmentStatus = document.getElementById('statusFilter').value;
    if (shipmentStatus) {
        console.log(`🔍 تطبيق مرشح الحالة: ${shipmentStatus}`);

        const beforeCount = filteredShipments.length;

        // طباعة عينة من البيانات للتشخيص
        if (filteredShipments.length > 0) {
            console.log('📋 عينة من حالات الشحنة في البيانات:');
            filteredShipments.slice(0, 3).forEach((shipment, i) => {
                console.log(`   ${i+1}. ${shipment.trackingNumber}: "${shipment.shipmentStatus}"`);
            });
        }

        // البحث المباشر بالكود الإنجليزي
        filteredShipments = filteredShipments.filter(shipment => {
            // البحث المباشر في حالة الشحنة
            const found = shipment.shipmentStatus.toLowerCase().includes(shipmentStatus.toLowerCase()) ||
                         shipment.searchText.includes(shipmentStatus.toLowerCase());

            if (found) {
                console.log(`✅ تطابق: ${shipment.trackingNumber} - حالة: "${shipment.shipmentStatus}"`);
            }

            return found;
        });

        console.log(`📊 مرشح الحالة: ${beforeCount} → ${filteredShipments.length}`);

        if (filteredShipments.length === 0) {
            console.log('⚠️ لا توجد نتائج! تحقق من:');
            console.log('   • هل الحالة موجودة في البيانات؟');
            console.log('   • هل النص يتطابق؟');
        }
    }

    // مرشح حالة الإفراج
    const releaseStatus = document.getElementById('releaseStatusFilter').value;
    if (releaseStatus) {
        const statusMap = {
            'pending': 'في انتظار الإفراج',
            'documents_review': 'مراجعة المستندات',
            'payment_verification': 'التحقق من المدفوعات',
            'quality_check': 'فحص الجودة',
            'approved': 'معتمد للإفراج',
            'released': 'تم الإفراج',
            'on_hold': 'محجوز مؤقت',
            'rejected': 'مرفوض الإفراج'
        };

        const statusText = statusMap[releaseStatus];
        filteredShipments = filteredShipments.filter(shipment =>
            shipment.releaseStatus.includes(statusText)
        );
    }

    // مرشح خط الشحن
    const shippingLine = document.getElementById('shippingLineFilter').value;
    if (shippingLine) {
        console.log(`🚢 تطبيق مرشح خط الشحن: ${shippingLine}`);

        // خريطة أكواد خطوط الشحن
        const shippingLineMap = {
            'MAEU': ['Maersk Line', 'MAEU', 'مايرسك'],
            'MSCU': ['MSC Mediterranean', 'MSCU', 'MSC'],
            'CMAU': ['CMA CGM', 'CMAU'],
            'COSU': ['COSCO Shipping', 'COSU', 'COSCO'],
            'HLCU': ['Hapag-Lloyd', 'HLCU', 'HAPAG'],
            'EGLV': ['Evergreen Line', 'EGLV'],
            'OOLU': ['OOCL', 'OOLU'],
            'YMLU': ['Yang Ming Marine', 'YMLU'],
            'HDMU': ['Hyundai Merchant Marine', 'HDMU'],
            'ONEU': ['Ocean Network Express', 'ONEU'],
            'UASU': ['United Arab Shipping', 'UASU'],
            'ARKU': ['Arkas Line', 'ARKU'],
            'MATS': ['Matson Navigation', 'MATS'],
            'NSCSA': ['الخطوط السعودية للنقل البحري', 'NSCSA']
        };

        const beforeCount = filteredShipments.length;
        filteredShipments = filteredShipments.filter(shipment => {
            // البحث بالكود أو الاسم
            const searchTerms = shippingLineMap[shippingLine] || [shippingLine];
            const found = searchTerms.some(term =>
                shipment.searchText.includes(term.toLowerCase())
            );
            return found;
        });

        console.log(`📊 مرشح خط الشحن: ${beforeCount} → ${filteredShipments.length}`);
    }

    // مرشح ميناء الوصول
    const destinationPort = document.getElementById('destinationPortFilter').value;
    if (destinationPort) {
        filteredShipments = filteredShipments.filter(shipment =>
            shipment.destinationPort.includes(destinationPort)
        );
    }
}

// تتبع شحنة من النافذة المنبثقة
function trackShipment() {
    const trackingNumber = document.getElementById('trackingNumberInput').value;
    if (trackingNumber) {
        window.location.href = `/shipments/track/${trackingNumber}`;
    }
}

// تحديث الشحنات
function refreshShipments() {
    location.reload();
}

// تصدير الشحنات
function exportShipments() {
    // استخدام نفس function التصدير الموجودة
    exportFilteredData();
}

// عرض خريطة الشحنة
function showShipmentMap(trackingNumber) {
    alert(`عرض خريطة الشحنة ${trackingNumber} - قريباً`);
}

// إرسال إشعار
function sendNotification(shipmentId) {
    alert(`إرسال إشعار للشحنة ${shipmentId} - قريباً`);
}

// تحديث جماعي
function bulkUpdate() {
    alert('التحديث الجماعي - قريباً');
}

// إنشاء تقرير
function generateReport() {
    alert('إنشاء تقرير - قريباً');
}

// إشعارات جماعية
function sendBulkNotifications() {
    alert('الإشعارات الجماعية - قريباً');
}

// تشخيص التصفية - حل سريع للمشكلة
function debugFiltering() {
    console.log('🔧 تشخيص شامل للتصفية...');

    // 1. فحص البيانات المجمعة
    console.log(`📊 إجمالي الشحنات المجمعة: ${allShipments.length}`);

    if (allShipments.length > 0) {
        console.log('📋 عينة من حالات الشحنة:');
        allShipments.slice(0, 5).forEach((shipment, i) => {
            console.log(`   ${i+1}. ${shipment.trackingNumber}: "${shipment.shipmentStatus}"`);
        });

        // 2. فحص الحالات الفريدة
        const uniqueStatuses = [...new Set(allShipments.map(s => s.shipmentStatus))];
        console.log(`🎯 الحالات الفريدة الموجودة: ${uniqueStatuses.join(', ')}`);

        // 3. اختبار التصفية المباشرة
        const testStatus = 'in_transit';
        const testResults = allShipments.filter(shipment =>
            shipment.shipmentStatus.toLowerCase().includes(testStatus) ||
            shipment.searchText.includes(testStatus)
        );
        console.log(`🧪 اختبار تصفية "${testStatus}": ${testResults.length} نتيجة`);

        // 4. عرض قيم المرشح الحالي
        const currentFilter = document.getElementById('statusFilter').value;
        console.log(`🔍 المرشح المحدد حالياً: "${currentFilter}"`);

        // 5. تطبيق التصفية يدوياً
        if (currentFilter) {
            const manualResults = allShipments.filter(shipment =>
                shipment.shipmentStatus.toLowerCase().includes(currentFilter.toLowerCase()) ||
                shipment.searchText.includes(currentFilter.toLowerCase())
            );
            console.log(`✅ نتائج التصفية اليدوية: ${manualResults.length}`);

            if (manualResults.length > 0) {
                console.log('🎯 الشحنات المطابقة:');
                manualResults.forEach(shipment => {
                    console.log(`   • ${shipment.trackingNumber}: "${shipment.shipmentStatus}"`);
                });
            }
        }
    } else {
        console.log('❌ لا توجد بيانات مجمعة! تحقق من:');
        console.log('   • هل الجدول موجود؟');
        console.log('   • هل تم استدعاء collectAllShipments()؟');
    }

    alert('تم عرض تشخيص التصفية في Console (F12)');
}



// حذف شحنة
function deleteShipment(shipmentId, trackingNumber) {
    // تسجيل القيم المرسلة
    console.log(`🔍 DEBUG: shipmentId = '${shipmentId}' (نوع: ${typeof shipmentId})`);
    console.log(`🔍 DEBUG: trackingNumber = '${trackingNumber}'`);

    // التحقق من صحة shipmentId
    if (!shipmentId || shipmentId === '' || shipmentId === 'undefined') {
        alert('❌ خطأ: ID الشحنة غير صحيح');
        return;
    }

    // تأكيد الحذف
    const confirmMessage = `هل أنت متأكد من حذف الشحنة؟\n\nرقم التتبع: ${trackingNumber}\nID: ${shipmentId}\n\n⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!`;

    if (confirm(confirmMessage)) {
        // إظهار مؤشر التحميل
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;

        // إرسال طلب الحذف - route سريع جديد
        const deleteUrl = `/shipments/quick-delete/${shipmentId}`;
        console.log(`🗑️ إرسال طلب حذف إلى: ${deleteUrl}`);

        fetch(deleteUrl, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        })
        .then(response => {
            console.log(`📊 رد الخادم: ${response.status} ${response.statusText}`);
            console.log(`🔍 URL المطلوب: ${response.url}`);

            if (response.ok) {
                return response.json();
            } else if (response.status === 404) {
                throw new Error(`المسار غير موجود: ${deleteUrl}`);
            } else {
                throw new Error(`خطأ في الخادم: ${response.status} - ${response.statusText}`);
            }
        })
        .then(data => {
            if (data.success) {
                // إظهار رسالة نجاح
                alert(`✅ تم حذف الشحنة ${trackingNumber} بنجاح`);

                // إزالة الصف من الجدول
                const row = button.closest('tr');
                row.style.transition = 'opacity 0.3s';
                row.style.opacity = '0';
                setTimeout(() => {
                    row.remove();

                    // تحديث عداد الشحنات إذا وجد
                    const countElement = document.querySelector('.shipment-count');
                    if (countElement) {
                        const currentCount = parseInt(countElement.textContent) || 0;
                        countElement.textContent = Math.max(0, currentCount - 1);
                    }
                }, 300);
            } else {
                throw new Error(data.message || 'فشل في حذف الشحنة');
            }
        })
        .catch(error => {
            console.error('خطأ في حذف الشحنة:', error);
            alert(`❌ فشل في حذف الشحنة: ${error.message}`);

            // استعادة الزر
            button.innerHTML = originalContent;
            button.disabled = false;
        });
    }
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initLiveMap();
});

// تم نقل جميع JavaScript functions إلى ملف shipments.js
</script>

<!-- نافذة تعديل حالة الشحنة -->
<div class="modal fade" id="statusEditModal" tabindex="-1" aria-labelledby="statusEditModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="statusEditModalLabel">
                    <i class="fas fa-edit me-2"></i>
                    تعديل حالة الشحنة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="statusEditForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">رقم التتبع:</label>
                            <input type="text" class="form-control" id="modalTrackingNumber" readonly>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">الحالة الحالية:</label>
                            <span id="modalCurrentStatus" class="badge bg-secondary fs-6"></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="newStatus" class="form-label fw-bold">الحالة الجديدة:</label>
                        <select class="form-select" id="newStatus" required>
                            <option value="">اختر الحالة الجديدة...</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="statusNotes" class="form-label fw-bold">ملاحظات التحديث:</label>
                        <textarea class="form-control" id="statusNotes" rows="3" placeholder="أدخل ملاحظات حول تغيير الحالة (اختياري)"></textarea>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> سيتم تحديث حالة أمر الشراء المرتبط تلقائياً حسب الحالة الجديدة.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-primary" onclick="updateShipmentStatus()">
                    <i class="fas fa-save me-1"></i>
                    حفظ التحديث
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تعديل حالة الإفراج -->
<div class="modal fade" id="releaseStatusEditModal" tabindex="-1" aria-labelledby="releaseStatusEditModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="releaseStatusEditModalLabel">
                    <i class="fas fa-unlock-alt me-2"></i>
                    تعديل حالة الإفراج
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="releaseStatusEditForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">رقم التتبع:</label>
                            <input type="text" class="form-control" id="modalReleaseTrackingNumber" readonly>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">حالة الإفراج الحالية:</label>
                            <span id="modalCurrentReleaseStatus" class="badge bg-secondary fs-6"></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="newReleaseStatus" class="form-label fw-bold">حالة الإفراج الجديدة:</label>
                        <select class="form-select" id="newReleaseStatus" required>
                            <option value="">اختر حالة الإفراج الجديدة...</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="releaseStatusNotes" class="form-label fw-bold">ملاحظات الإفراج:</label>
                        <textarea class="form-control" id="releaseStatusNotes" rows="3" placeholder="أدخل ملاحظات حول تغيير حالة الإفراج (اختياري)"></textarea>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تنبيه:</strong> تغيير حالة الإفراج قد يؤثر على إجراءات التخليص الجمركي والتسليم.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-warning" onclick="updateReleaseStatus()">
                    <i class="fas fa-save me-1"></i>
                    حفظ التحديث
                </button>
            </div>
        </div>
    </div>
</div>

<!-- إصلاح شامل لمشكلة تداخل السهم مع المحتوى في القوائم المنسدلة + لمسات فنية -->
<style>
/* 🎨 لمسات فنية فورية - أولوية عالية */

/* خلفية الصفحة */
html, body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    min-height: 100vh !important;
}

/* الحاوي الرئيسي */
.container-fluid {
    background: rgba(255, 255, 255, 0.98) !important;
    border-radius: 20px !important;
    margin: 15px !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2) !important;
    backdrop-filter: blur(15px) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* البطاقات */
.card {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%) !important;
    border: none !important;
    border-radius: 20px !important;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15) !important;
    transition: all 0.4s ease !important;
    margin-bottom: 25px !important;
    overflow: hidden !important;
}

.card:hover {
    transform: translateY(-8px) !important;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25) !important;
}

/* رؤوس البطاقات */
.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-radius: 20px 20px 0 0 !important;
    border: none !important;
    padding: 1.5rem 2rem !important;
    position: relative !important;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.card-header h1, .card-header h2, .card-header h3,
.card-header h4, .card-header h5, .card-header h6 {
    color: white !important;
    margin: 0 !important;
    font-weight: 600 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
    position: relative !important;
    z-index: 1 !important;
}

.card-header i {
    color: rgba(255, 255, 255, 0.9) !important;
    margin-left: 10px !important;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3)) !important;
}

/* محتوى البطاقات */
.card-body {
    background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%) !important;
    padding: 2rem !important;
    position: relative !important;
}

.card-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
    background-size: 200% 100%;
    animation: gradient-move 3s ease infinite;
}

@keyframes gradient-move {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* الجداول */
.table {
    background: white !important;
    border-radius: 15px !important;
    overflow: hidden !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
    border: none !important;
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border: none !important;
    font-weight: 600 !important;
    padding: 1.2rem 1rem !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    position: relative !important;
}

.table thead th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
}

.table tbody tr {
    transition: all 0.3s ease !important;
    border: none !important;
}

.table tbody tr:nth-child(even) {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%) !important;
    transform: scale(1.02) !important;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2) !important;
}

.table tbody td {
    border: none !important;
    padding: 1rem !important;
    vertical-align: middle !important;
}

/* الأزرار */
.btn {
    border-radius: 25px !important;
    padding: 0.6rem 1.8rem !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    border: none !important;
    position: relative !important;
    overflow: hidden !important;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a42a0 100%) !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.5) !important;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4) !important;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4) !important;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4) !important;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
    box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4) !important;
}

/* الشارات */
.badge {
    border-radius: 20px !important;
    padding: 0.6rem 1.2rem !important;
    font-weight: 600 !important;
    font-size: 0.85rem !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
    position: relative !important;
    overflow: hidden !important;
}

.badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: badge-shine 2s infinite;
}

@keyframes badge-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.badge.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
}

.badge.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%) !important;
}

/* التنبيهات */
.alert {
    border-radius: 20px !important;
    border: none !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    position: relative !important;
    overflow: hidden !important;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, transparent, currentColor, transparent);
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.15) 0%, rgba(32, 201, 151, 0.15) 100%) !important;
    color: #155724 !important;
    border-left: 4px solid #28a745 !important;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.15) 0%, rgba(253, 126, 20, 0.15) 100%) !important;
    color: #856404 !important;
    border-left: 4px solid #ffc107 !important;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.15) 0%, rgba(232, 62, 140, 0.15) 100%) !important;
    color: #721c24 !important;
    border-left: 4px solid #dc3545 !important;
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.15) 0%, rgba(111, 66, 193, 0.15) 100%) !important;
    color: #0c5460 !important;
    border-left: 4px solid #17a2b8 !important;
}

/* النوافذ المنبثقة */
.modal-content {
    border-radius: 25px !important;
    border: none !important;
    box-shadow: 0 30px 80px rgba(0, 0, 0, 0.4) !important;
    backdrop-filter: blur(20px) !important;
    overflow: hidden !important;
}

.modal-header {
    border-radius: 25px 25px 0 0 !important;
    border-bottom: none !important;
    padding: 2rem !important;
    position: relative !important;
}

.modal-header.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.modal-header.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
}

.modal-body {
    padding: 2rem !important;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%) !important;
}

.modal-footer {
    border-top: none !important;
    border-radius: 0 0 25px 25px !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    padding: 1.5rem 2rem !important;
}
/* إصلاح عام لجميع القوائم المنسدلة في النظام */
.form-select {
    /* إصلاح مشكلة تداخل السهم مع المحتوى */
    padding-right: 2.5rem !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right 0.75rem center !important;
    background-size: 16px 12px !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
}

/* تحسين مظهر القوائم المنسدلة عند التركيز */
.form-select:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
}

/* تحسين مظهر القوائم المنسدلة في الوضع المظلم */
@media (prefers-color-scheme: dark) {
    .form-select {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
    }
}

/* إصلاح خاص للقوائم المنسدلة الصغيرة */
.form-select.form-select-sm {
    padding-right: 2rem !important;
    background-position: right 0.5rem center !important;
    background-size: 14px 10px !important;
}

/* إصلاح خاص للقوائم المنسدلة الكبيرة */
.form-select.form-select-lg {
    padding-right: 3rem !important;
    background-position: right 1rem center !important;
    background-size: 18px 14px !important;
}

/* تحسين للنصوص الطويلة في القوائم المنسدلة */
.form-select option {
    padding: 8px 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* تحسين للقوائم المنسدلة في الجداول */
.table .form-select {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 0.875rem;
}

/* تحسين للقوائم المنسدلة في النماذج المضغوطة */
.compact-form .form-select {
    padding: 0.375rem 2rem 0.375rem 0.75rem !important;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* 🎨 لمسات فنية للنافذة - بدون تغيير التخطيط */

/* خلفية متدرجة جميلة للصفحة */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    min-height: 100vh;
}

/* لمسة فنية للحاوي الرئيسي */
.container-fluid {
    background: rgba(255, 255, 255, 0.95) !important;
    border-radius: 15px !important;
    margin: 10px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
    backdrop-filter: blur(10px) !important;
}

/* تحسين البطاقات */
.card {
    background: linear-gradient(145deg, #ffffff, #f8f9fa) !important;
    border: none !important;
    border-radius: 15px !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
}

.card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15) !important;
}

/* تحسين رؤوس البطاقات */
.card-header {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    border-radius: 15px 15px 0 0 !important;
    border: none !important;
    padding: 1.25rem !important;
}

.card-header h5, .card-header h6 {
    color: white !important;
    margin: 0 !important;
    font-weight: 600 !important;
}

/* تحسين الجداول */
.table {
    background: white !important;
    border-radius: 10px !important;
    overflow: hidden !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08) !important;
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
    color: #495057 !important;
    border: none !important;
    font-weight: 600 !important;
    padding: 1rem !important;
}

.table tbody tr {
    transition: all 0.3s ease !important;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, #f8f9ff, #fff5f5) !important;
    transform: scale(1.01) !important;
}

/* تحسين الأزرار */
.btn {
    border-radius: 25px !important;
    padding: 0.5rem 1.5rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    border: none !important;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a42a0) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3) !important;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3) !important;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c) !important;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3) !important;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1) !important;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3) !important;
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #adb5bd) !important;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3) !important;
}

/* تحسين الشارات (Badges) */
.badge {
    border-radius: 20px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 500 !important;
    font-size: 0.85rem !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1) !important;
}

.badge.bg-primary {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
}

/* تحسين النماذج */
.form-control, .form-select {
    border-radius: 10px !important;
    border: 2px solid #e9ecef !important;
    transition: all 0.3s ease !important;
    background: rgba(255, 255, 255, 0.9) !important;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15) !important;
    background: white !important;
}

/* تحسين التنبيهات */
.alert {
    border-radius: 15px !important;
    border: none !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.1)) !important;
    border-left: 5px solid #28a745 !important;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(253, 126, 20, 0.1)) !important;
    border-left: 5px solid #ffc107 !important;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(232, 62, 140, 0.1)) !important;
    border-left: 5px solid #dc3545 !important;
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(111, 66, 193, 0.1)) !important;
    border-left: 5px solid #17a2b8 !important;
}

/* تحسين الأيقونات */
.fas, .far, .fab {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1)) !important;
}

/* تحسين النوافذ المنبثقة */
.modal-content {
    border-radius: 20px !important;
    border: none !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
    backdrop-filter: blur(10px) !important;
}

.modal-header {
    border-radius: 20px 20px 0 0 !important;
    border-bottom: none !important;
}

.modal-header.bg-primary {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
}

.modal-header.bg-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
}

.modal-footer {
    border-top: none !important;
    border-radius: 0 0 20px 20px !important;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
}

/* تأثيرات حركية جميلة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card, .table, .alert {
    animation: fadeInUp 0.6s ease-out !important;
}

/* تحسين شريط التقدم */
.progress {
    border-radius: 20px !important;
    height: 8px !important;
    background: rgba(0, 0, 0, 0.1) !important;
}

.progress-bar {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    border-radius: 20px !important;
}

/* تحسين القوائم */
.list-group-item {
    border: none !important;
    border-radius: 10px !important;
    margin-bottom: 5px !important;
    background: rgba(255, 255, 255, 0.8) !important;
    transition: all 0.3s ease !important;
}

.list-group-item:hover {
    background: rgba(102, 126, 234, 0.1) !important;
    transform: translateX(5px) !important;
}

/* إصلاح خاص للقوائم المنسدلة في النوافذ المنبثقة */
.modal .form-select {
    padding-right: 2.5rem !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right 0.75rem center !important;
    background-size: 16px 12px !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
}

/* تحسين خاص لنوافذ تعديل الحالة */
#statusEditModal .form-select,
#releaseStatusEditModal .form-select {
    border: 2px solid #dee2e6 !important;
    border-radius: 8px !important;
    padding: 0.75rem 2.5rem 0.75rem 0.75rem !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    background-color: #f8f9fa !important;
}

#statusEditModal .form-select:focus,
#releaseStatusEditModal .form-select:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25) !important;
    background-color: #ffffff !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
}

/* تحسين مظهر الخيارات في النوافذ المنبثقة */
#statusEditModal .form-select option,
#releaseStatusEditModal .form-select option {
    padding: 10px 15px !important;
    font-weight: 500 !important;
}

/* تحسين للنوافذ المنبثقة في الشاشات الصغيرة */
@media (max-width: 768px) {
    .modal .form-select {
        padding-right: 2.25rem !important;
        background-position: right 0.5rem center !important;
        background-size: 14px 10px !important;
    }
}
</style>

<!-- تحميل ملف JavaScript للشحنات -->
<script src="{{ url_for('static', filename='js/shipments.js') }}"></script>

<script>
// دالة عرض الإشعارات
function showNotification(message, type = 'info') {
    const typeClasses = {
        'success': 'alert-success',
        'warning': 'alert-warning',
        'error': 'alert-danger',
        'info': 'alert-info'
    };

    const typeIcons = {
        'success': 'fas fa-check-circle',
        'warning': 'fas fa-exclamation-triangle',
        'error': 'fas fa-times-circle',
        'info': 'fas fa-info-circle'
    };

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${typeClasses[type] || 'alert-info'} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="${typeIcons[type] || 'fas fa-info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة الرسالة تلقائياً بعد 4 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 4000);
}
</script>

{% endblock %}
