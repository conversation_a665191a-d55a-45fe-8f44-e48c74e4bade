# -*- coding: utf-8 -*-
"""
مولد PDF من صفحة HTML الموجودة
HTML to PDF Generator using existing delivery order viewer
"""

import os
import sys
import requests
from datetime import datetime
import subprocess
import tempfile

# إضافة مسار قاعدة البيانات
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class HTMLToPDFGenerator:
    """مولد PDF من صفحة HTML الموجودة"""
    
    def __init__(self):
        """تهيئة مولد PDF"""
        self.output_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'pdf')
        os.makedirs(self.output_dir, exist_ok=True)
        self.base_url = 'http://127.0.0.1:5000'
    
    def generate_delivery_order_pdf_from_html(self, delivery_order_id):
        """إنشاء PDF من صفحة HTML الموجودة"""
        try:
            # إنشاء اسم الملف
            filename = f"delivery_order_html_{delivery_order_id}.pdf"
            filepath = os.path.join(self.output_dir, filename)
            
            # URL صفحة أمر التسليم
            html_url = f"{self.base_url}/shipments/delivery-order-viewer?id={delivery_order_id}"
            
            # الطريقة 1: استخدام wkhtmltopdf (إذا كان متاح)
            if self._try_wkhtmltopdf(html_url, filepath):
                return filepath, "تم إنشاء PDF من HTML بنجاح"
            
            # الطريقة 2: استخدام Chrome/Chromium headless
            if self._try_chrome_headless(html_url, filepath):
                return filepath, "تم إنشاء PDF من HTML بنجاح"
            
            # الطريقة 3: استخدام Playwright (إذا كان متاح)
            if self._try_playwright(html_url, filepath):
                return filepath, "تم إنشاء PDF من HTML بنجاح"
            
            # الطريقة 4: تحميل HTML وتحويله باستخدام WeasyPrint
            if self._try_weasyprint(html_url, filepath):
                return filepath, "تم إنشاء PDF من HTML بنجاح"
            
            return None, "فشل في تحويل HTML إلى PDF - لا توجد أدوات متاحة"
            
        except Exception as e:
            return None, f"خطأ في إنشاء PDF من HTML: {str(e)}"
    
    def _try_wkhtmltopdf(self, html_url, output_path):
        """محاولة استخدام wkhtmltopdf"""
        try:
            cmd = [
                'wkhtmltopdf',
                '--page-size', 'A4',
                '--orientation', 'Portrait',
                '--margin-top', '0.75in',
                '--margin-right', '0.75in',
                '--margin-bottom', '0.75in',
                '--margin-left', '0.75in',
                '--encoding', 'UTF-8',
                '--no-stop-slow-scripts',
                '--javascript-delay', '2000',
                html_url,
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0 and os.path.exists(output_path):
                print("✅ تم إنشاء PDF باستخدام wkhtmltopdf")
                return True
            else:
                print(f"❌ فشل wkhtmltopdf: {result.stderr}")
                return False
                
        except (subprocess.TimeoutExpired, FileNotFoundError, Exception) as e:
            print(f"❌ wkhtmltopdf غير متاح: {e}")
            return False
    
    def _try_chrome_headless(self, html_url, output_path):
        """محاولة استخدام Chrome headless"""
        try:
            # البحث عن Chrome/Chromium
            chrome_paths = [
                'google-chrome',
                'chromium-browser',
                'chromium',
                'chrome',
                r'C:\Program Files\Google\Chrome\Application\chrome.exe',
                r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe'
            ]
            
            chrome_path = None
            for path in chrome_paths:
                try:
                    result = subprocess.run([path, '--version'], capture_output=True, timeout=5)
                    if result.returncode == 0:
                        chrome_path = path
                        break
                except:
                    continue
            
            if not chrome_path:
                return False
            
            cmd = [
                chrome_path,
                '--headless',
                '--disable-gpu',
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--print-to-pdf=' + output_path,
                '--print-to-pdf-no-header',
                '--run-all-compositor-stages-before-draw',
                '--virtual-time-budget=2000',
                html_url
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                print("✅ تم إنشاء PDF باستخدام Chrome headless")
                return True
            else:
                print(f"❌ فشل Chrome headless: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Chrome headless غير متاح: {e}")
            return False
    
    def _try_playwright(self, html_url, output_path):
        """محاولة استخدام Playwright"""
        try:
            from playwright.sync_api import sync_playwright
            
            with sync_playwright() as p:
                browser = p.chromium.launch()
                page = browser.new_page()
                page.goto(html_url)
                page.wait_for_timeout(2000)  # انتظار تحميل الصفحة
                
                page.pdf(
                    path=output_path,
                    format='A4',
                    margin={
                        'top': '0.75in',
                        'right': '0.75in',
                        'bottom': '0.75in',
                        'left': '0.75in'
                    }
                )
                
                browser.close()
                
                if os.path.exists(output_path):
                    print("✅ تم إنشاء PDF باستخدام Playwright")
                    return True
                    
        except ImportError:
            print("❌ Playwright غير مثبت")
            return False
        except Exception as e:
            print(f"❌ فشل Playwright: {e}")
            return False
    
    def _try_weasyprint(self, html_url, output_path):
        """محاولة استخدام WeasyPrint"""
        try:
            import weasyprint
            
            # تحميل HTML
            response = requests.get(html_url, timeout=10)
            if response.status_code != 200:
                return False
            
            # تحويل إلى PDF
            html_doc = weasyprint.HTML(string=response.text, base_url=self.base_url)
            html_doc.write_pdf(output_path)
            
            if os.path.exists(output_path):
                print("✅ تم إنشاء PDF باستخدام WeasyPrint")
                return True
                
        except ImportError:
            print("❌ WeasyPrint غير مثبت")
            return False
        except Exception as e:
            print(f"❌ فشل WeasyPrint: {e}")
            return False
    
    def check_available_tools(self):
        """فحص الأدوات المتاحة لتحويل HTML إلى PDF"""
        tools = {
            'wkhtmltopdf': False,
            'chrome': False,
            'playwright': False,
            'weasyprint': False
        }
        
        # فحص wkhtmltopdf
        try:
            result = subprocess.run(['wkhtmltopdf', '--version'], capture_output=True, timeout=5)
            tools['wkhtmltopdf'] = result.returncode == 0
        except:
            pass
        
        # فحص Chrome
        chrome_paths = ['google-chrome', 'chromium-browser', 'chrome']
        for path in chrome_paths:
            try:
                result = subprocess.run([path, '--version'], capture_output=True, timeout=5)
                if result.returncode == 0:
                    tools['chrome'] = True
                    break
            except:
                continue
        
        # فحص Playwright
        try:
            import playwright
            tools['playwright'] = True
        except ImportError:
            pass
        
        # فحص WeasyPrint
        try:
            import weasyprint
            tools['weasyprint'] = True
        except ImportError:
            pass
        
        return tools


# إنشاء instance عام للمولد
html_to_pdf_generator = HTMLToPDFGenerator()


def generate_delivery_order_pdf_from_html(delivery_order_id):
    """دالة مساعدة لإنشاء PDF من HTML"""
    return html_to_pdf_generator.generate_delivery_order_pdf_from_html(delivery_order_id)


def check_pdf_tools():
    """فحص الأدوات المتاحة"""
    return html_to_pdf_generator.check_available_tools()
