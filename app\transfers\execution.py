#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نظام تنفيذ الحوالات
Transfer Execution Management System
"""

from flask import render_template, request as flask_request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from . import transfers_bp
from database_manager import DatabaseManager
import logging
from datetime import datetime
import os
from werkzeug.utils import secure_filename

logger = logging.getLogger(__name__)

@transfers_bp.route('/execution')
@login_required
def execution():
    """صفحة تنفيذ الحوالات - عرض الطلبات المعتمدة للتنفيذ"""
    return render_template('transfers/execution.html')

@transfers_bp.route('/execute-transfers')
@login_required
def execute_transfers():
    """صفحة تنفيذ الحوالات (للتوافق مع النظام القديم)"""
    return redirect(url_for('transfers.execution'))

@transfers_bp.route('/execution-dashboard')
@login_required
def execution_dashboard():
    """لوحة تحكم تنفيذ الحوالات"""
    return render_template('transfers/execution_dashboard.html')

@transfers_bp.route('/execute-transfer')
@login_required
def execute_transfer():
    """صفحة تنفيذ حوالة منفصلة - صفحة كاملة"""
    return render_template('transfers/execute_transfer.html')

@transfers_bp.route('/api/requests/<int:request_id>')
@login_required
def api_get_request(request_id):
    """API لجلب بيانات طلب واحد"""
    try:
        db = DatabaseManager()

        query = """
        SELECT
            tr.id,
            tr.request_number,
            tr.amount,
            tr.currency,
            tr.status,
            tr.created_at,
            tr.approved_at,
            tr.branch_id,
            tr.requested_by,
            tr.purpose,
            tr.notes,
            b.beneficiary_name,
            b.bank_name,
            b.account_number,
            b.phone_number,
            b.country,
            b.bank_branch,
            COALESCE(br.branch_name, 'الفرع الرئيسي') as branch_name,
            COALESCE(br.branch_code, 'MAIN') as branch_code,
            COALESCE(u.username, 'مستخدم غير معروف') as cashier_name,
            COALESCE(u.full_name, u.username, 'مستخدم غير معروف') as cashier_full_name
        FROM TRANSFER_REQUESTS tr
        LEFT JOIN BENEFICIARIES b ON tr.beneficiary_id = b.id
        LEFT JOIN BRANCHES br ON tr.branch_id = br.id
        LEFT JOIN USERS u ON tr.requested_by = u.id
        WHERE tr.id = :1 AND tr.status = 'approved'
        """

        result = db.execute_query(query, [request_id])

        if result:
            request_data = {
                'id': result[0][0],
                'request_number': result[0][1],
                'amount': float(result[0][2]),
                'currency': result[0][3],
                'status': result[0][4],
                'created_at': result[0][5].isoformat() if result[0][5] else None,
                'approved_at': result[0][6].isoformat() if result[0][6] else None,
                'branch_id': result[0][7],
                'requested_by': result[0][8],
                'purpose': result[0][9],
                'notes': result[0][10],
                'beneficiary_name': result[0][11],
                'bank_name': result[0][12],
                'account_number': result[0][13],
                'phone_number': result[0][14],
                'country': result[0][15],
                'bank_branch': result[0][16],
                'branch_name': result[0][17] or 'الفرع الرئيسي',
                'branch_code': result[0][18] or 'MAIN',
                'cashier_name': result[0][19] or f'مستخدم رقم {result[0][8]}',
                'cashier_full_name': result[0][20] or result[0][19] or f'مستخدم رقم {result[0][8]}'
            }

            return jsonify({
                'success': True,
                'data': request_data
            })
        else:
            return jsonify({
                'success': False,
                'message': 'الطلب غير موجود أو غير معتمد'
            }), 404

    except Exception as e:
        logger.error(f"خطأ في جلب بيانات الطلب {request_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب البيانات: {str(e)}'
        }), 500

# ===== APIs نظام التنفيذ =====

@transfers_bp.route('/api/approved-requests', methods=['GET'])
@login_required
def api_get_approved_requests():
    """API للحصول على الطلبات المعتمدة للتنفيذ"""
    try:
        db = DatabaseManager()

        # جلب جميع الطلبات المعتمدة مع حالة التنفيذ
        query = """
        SELECT
            tr.id, tr.request_number, tr.amount, tr.currency, tr.purpose,
            tr.status, tr.created_at, tr.approved_at,
            b.beneficiary_name, b.bank_account, b.bank_name, b.bank_country,
            COALESCE(br.BRN_LNAME, 'فرع غير محدد') as branch_name,
            COALESCE(mcb.NAME, 'غير محدد') as money_changer_bank_name,
            COALESCE(mcb.TYPE, 'غير محدد') as transfer_type,
            'مستخدم النظام' as approved_by_name,
            tr.priority_level,
            tr.risk_level,
            CASE WHEN t.id IS NOT NULL THEN 'executed' ELSE tr.status END as execution_status
        FROM TRANSFER_REQUESTS tr
        LEFT JOIN BENEFICIARIES b ON tr.beneficiary_id = b.id
        LEFT JOIN BRANCHES br ON tr.branch_id = br.BRN_NO
        LEFT JOIN MONEY_CHANGERS_BANKS mcb ON tr.money_changer_bank_id = mcb.ID
        LEFT JOIN TRANSFERS t ON tr.id = t.request_id
        WHERE tr.status IN ('approved', 'executed')
        ORDER BY
            CASE
                WHEN tr.priority_level = 'urgent' THEN 1
                WHEN tr.priority_level = 'high' THEN 2
                WHEN tr.priority_level = 'normal' THEN 3
                ELSE 4
            END,
            tr.approved_at ASC
        """

        result = db.execute_query(query)

        requests = []
        if result:
            for row in result:
                requests.append({
                    'id': int(row[0]) if row[0] else 0,
                    'request_number': str(row[1]) if row[1] else '',
                    'amount': float(row[2]) if row[2] else 0,
                    'currency': str(row[3]) if row[3] else '',
                    'purpose': str(row[4]) if row[4] else '',
                    'status': str(row[5]) if row[5] else '',
                    'created_at': row[6].strftime('%Y-%m-%d %H:%M:%S') if row[6] else None,
                    'approved_at': row[7].strftime('%Y-%m-%d %H:%M:%S') if row[7] else None,
                    'beneficiary_name': str(row[8]) if row[8] else '',
                    'bank_account': str(row[9]) if row[9] else '',
                    'bank_name': str(row[10]) if row[10] else '',
                    'bank_country': str(row[11]) if row[11] else '',
                    'branch_name': str(row[12]) if row[12] else '',
                    'money_changer_bank_name': str(row[13]) if row[13] else '',
                    'transfer_type': str(row[14]) if row[14] else '',
                    'approved_by_name': str(row[15]) if row[15] else '',
                    'priority_level': str(row[16]) if row[16] else 'normal',
                    'risk_level': str(row[17]) if row[17] else 'low',
                    'execution_status': str(row[18]) if row[18] else 'approved',
                    'days_since_approval': (datetime.now() - row[7]).days if row[7] else 0
                })

        return jsonify({
            'success': True,
            'data': requests,
            'total_count': len(requests)
        })

    except Exception as e:
        logger.error(f"خطأ في جلب الطلبات المعتمدة: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب الطلبات المعتمدة: {str(e)}'
        }), 500

@transfers_bp.route('/api/execute-request/<int:request_id>', methods=['POST'])
@login_required
def api_execute_request(request_id):
    """API محسن لتنفيذ طلب حوالة مع موردين متعددين"""
    logger.info(f"🚀 بدء تنفيذ طلب الحوالة رقم: {request_id}")
    try:
        data = flask_request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': 'لا توجد بيانات'
            }), 400

        # التحقق من الحقول المطلوبة
        required_fields = ['reference', 'suppliers']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'الحقل {field} مطلوب'
                }), 400

        suppliers = data.get('suppliers', [])
        if not suppliers:
            return jsonify({
                'success': False,
                'message': 'يجب إضافة مورد واحد على الأقل'
            }), 400

        db = DatabaseManager()

        # التحقق من وجود الطلب وحالته
        check_query = """
        SELECT tr.id, tr.amount, tr.currency, tr.status, tr.beneficiary_id, tr.branch_id
        FROM TRANSFER_REQUESTS tr
        WHERE tr.id = :1 AND tr.status = 'approved'
        """

        result = db.execute_query(check_query, [request_id])

        if not result:
            return jsonify({
                'success': False,
                'message': 'الطلب غير موجود أو غير معتمد'
            }), 404

        request_data = result[0]
        original_amount = float(request_data[1])

        # التحقق من مطابقة المبالغ
        total_distribution = sum(float(s.get('amount', 0)) for s in suppliers)
        if abs(original_amount - total_distribution) > 0.01:
            return jsonify({
                'success': False,
                'message': f'إجمالي التوزيع ({total_distribution}) لا يطابق المبلغ الأصلي ({original_amount})'
            }), 400

        # بدء المعاملة
        try:
            # إنشاء رقم التحويل
            transfer_number = f"TF{datetime.now().strftime('%Y%m%d%H%M%S')}"

            # إدراج سجل التحويل - جميع الأعمدة المطلوبة مع الفرع
            insert_transfer_query = """
            INSERT INTO TRANSFERS (
                transfer_number, request_id, branch_id, money_changer_bank_id, execution_date,
                executed_by, status, execution_reference, execution_method, execution_notes
            ) VALUES (
                :1, :2, :3, :4, CURRENT_TIMESTAMP, :5, 'completed', :6, :7, :8
            )
            """

            # الحصول على معرف الصراف الصحيح من طلب الحوالة
            # نحتاج للحصول على money_changer_bank_id من transfer_requests
            money_changer_query = """
            SELECT money_changer_bank_id
            FROM transfer_requests
            WHERE id = :1
            """
            mc_result = db.execute_query(money_changer_query, [request_id])
            money_changer_bank_id = mc_result[0][0] if mc_result and mc_result[0][0] else 1

            logger.info(f"🏦 معرف الصراف الصحيح: {money_changer_bank_id}")

            result = db.execute_update(insert_transfer_query, [
                transfer_number,                    # :1
                request_id,                         # :2
                request_data[5],                    # :3 branch_id (الآن في الفهرس 5)
                money_changer_bank_id,              # :4 money_changer_bank_id (الصحيح!)
                int(current_user.id),              # :5 executed_by
                data.get('reference'),              # :6 execution_reference
                data.get('execution_method', 'mixed'),  # :7 execution_method
                data.get('notes', '')               # :8 execution_notes
            ])

            print(f"نتيجة الإدراج: {result}")

            # الحصول على ID التحويل
            transfer_id_query = "SELECT id FROM TRANSFERS WHERE transfer_number = :1"
            transfer_result = db.execute_query(transfer_id_query, [transfer_number])

            if not transfer_result:
                raise Exception('فشل في إنشاء سجل التحويل')

            transfer_id = transfer_result[0][0]
            print(f"ID التحويل: {transfer_id}")

            # إدراج سجلات الموردين
            for i, supplier in enumerate(suppliers, 1):
                # جلب معلومات المورد من جدول SUPPLIERS
                supplier_query = """
                SELECT name_ar, name_en, supplier_type, supplier_code
                FROM SUPPLIERS
                WHERE id = :1 AND is_active = 1
                """
                supplier_result = db.execute_query(supplier_query, [supplier.get('supplier_id')])

                if not supplier_result:
                    # إذا لم نجد في SUPPLIERS، نجرب money_changers_banks
                    supplier_query2 = "SELECT name, type FROM money_changers_banks WHERE id = :1"
                    supplier_result = db.execute_query(supplier_query2, [supplier.get('supplier_id')])

                    if not supplier_result:
                        raise Exception(f'المورد رقم {supplier.get("supplier_id")} غير موجود')

                    supplier_name = supplier_result[0][0]
                    supplier_type = supplier_result[0][1]
                else:
                    supplier_data = supplier_result[0]
                    supplier_name = supplier_data[0] or supplier_data[1]  # name_ar أو name_en
                    supplier_type = supplier_data[2] or 'تجاري'

                # إدراج سجل المورد
                insert_supplier_query = """
                INSERT INTO transfer_execution_suppliers (
                    transfer_id, supplier_id, supplier_name, supplier_type,
                    amount, currency, exchange_rate, commission,
                    supplier_reference, supplier_notes, execution_order,
                    status, created_by
                ) VALUES (
                    :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, 'completed', :12
                )
                """

                supplier_result = db.execute_update(insert_supplier_query, [
                    transfer_id,
                    supplier.get('supplier_id'),
                    supplier_name,     # من جدول SUPPLIERS
                    supplier_type,     # من جدول SUPPLIERS
                    supplier.get('amount'),
                    request_data[2],   # currency
                    supplier.get('exchange_rate', 1.0),
                    supplier.get('commission', 0),
                    supplier.get('reference', ''),
                    supplier.get('notes', ''),
                    i,  # execution_order
                    current_user.id
                ])

                print(f"✅ تم إدراج المورد {i}: {supplier_result} سجل")

            # تحديث حالة الطلب إلى منفذ
            update_query = """
            UPDATE TRANSFER_REQUESTS
            SET status = 'executed', updated_at = CURRENT_TIMESTAMP
            WHERE id = :1
            """

            update_result = db.execute_update(update_query, [request_id])
            print(f"✅ تم تحديث حالة الطلب: {update_result} سجل")

            # ===== إضافة الترحيل المحاسبي =====
            # إنشاء ملف سجل منفصل للترحيل المحاسبي
            import os
            import datetime as dt

            log_dir = "logs"
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)

            accounting_log_file = os.path.join(log_dir, "accounting_migration.log")

            def log_accounting(message):
                timestamp = dt.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                with open(accounting_log_file, "a", encoding="utf-8") as f:
                    f.write(f"[{timestamp}] {message}\n")
                logger.info(message)

            log_accounting("🔄 بدء الترحيل المحاسبي...")
            log_accounting(f"📋 معرف الحوالة: {transfer_id}")
            log_accounting(f"📋 معرف الطلب: {request_id}")
            log_accounting(f"📋 عدد الموردين: {len(suppliers)}")

            try:
                log_accounting("📦 استيراد خدمة الترحيل المحاسبي...")

                # استيراد خدمة الترحيل المحاسبي
                from app.transfers.accounting_service import TransferAccountingService
                log_accounting("✅ تم استيراد خدمة الترحيل المحاسبي بنجاح")

                # إنشاء مثيل من خدمة الترحيل
                log_accounting("🔧 إنشاء مثيل من خدمة الترحيل المحاسبي...")
                accounting_service = TransferAccountingService()
                log_accounting("✅ تم إنشاء مثيل خدمة الترحيل المحاسبي")

                # تحضير بيانات توزيع الموردين للترحيل المحاسبي
                log_accounting("📋 تحضير بيانات توزيع الموردين...")
                supplier_distributions = []
                for i, supplier in enumerate(suppliers):
                    supplier_data = {
                        'supplier_id': supplier.get('supplier_id'),
                        'amount': float(supplier.get('amount')),
                        'currency_code': request_data[2],  # العملة من الطلب
                        'exchange_rate': float(supplier.get('exchange_rate', 1.0)),
                        'notes': supplier.get('notes', '')
                    }
                    supplier_distributions.append(supplier_data)
                    log_accounting(f"   📦 مورد {i+1}: {supplier_data}")

                log_accounting(f"✅ تم تحضير بيانات {len(supplier_distributions)} مورد")

                # تنفيذ الترحيل المحاسبي
                log_accounting("🚀 بدء تنفيذ الترحيل المحاسبي...")
                log_accounting(f"   📋 معرف الحوالة: {transfer_id}")
                log_accounting(f"   📋 معرف الصراف: {money_changer_bank_id}")
                log_accounting(f"   📋 المبلغ الإجمالي: {total_distribution}")
                log_accounting(f"   📋 العملة: {request_data[2]}")
                log_accounting(f"   📋 معرف المستخدم: {current_user.id}")

                accounting_result = accounting_service.execute_transfer(
                    transfer_id=transfer_id,
                    money_changer_id=money_changer_bank_id,  # استخدام معرف الصراف الصحيح
                    total_amount=total_distribution,
                    currency_code=request_data[2],
                    supplier_distributions=supplier_distributions,
                    user_id=current_user.id
                )

                log_accounting(f"📋 نتيجة الترحيل المحاسبي: {accounting_result}")

                if accounting_result.get('success'):
                    log_accounting("✅ تم الترحيل المحاسبي بنجاح")
                else:
                    log_accounting(f"⚠️ تحذير: فشل الترحيل المحاسبي - {accounting_result.get('message', 'خطأ غير معروف')}")

            except Exception as accounting_error:
                log_accounting(f"⚠️ تحذير: خطأ في الترحيل المحاسبي - {str(accounting_error)}")
                # طباعة تفاصيل الخطأ
                import traceback
                log_accounting(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
                # لا نوقف العملية، فقط نسجل التحذير
                log_accounting(f"فشل الترحيل المحاسبي للحوالة {transfer_id}: {str(accounting_error)}")

            return jsonify({
                'success': True,
                'message': 'تم تنفيذ الحوالة بنجاح',
                'transfer_number': transfer_number,
                'transfer_id': transfer_id,
                'suppliers_count': len(suppliers),
                'total_amount': total_distribution
            })

        except Exception as e:
            # في حالة الخطأ، نحاول التراجع
            logger.error(f"خطأ في تنفيذ الحوالة: {str(e)}")
            raise e

    except Exception as e:
        logger.error(f"خطأ في تنفيذ الطلب {request_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في تنفيذ الطلب: {str(e)}'
        }), 500

@transfers_bp.route('/api/suppliers')
@login_required
def api_get_suppliers():
    """API للحصول على قائمة الموردين من جدول SUPPLIERS"""
    try:
        db = DatabaseManager()

        query = """
        SELECT id, supplier_code, name_ar, name_en, supplier_type,
               contact_person, phone, email, is_active
        FROM SUPPLIERS
        WHERE is_active = 1
        ORDER BY name_ar
        """

        result = db.execute_query(query)

        suppliers = []
        if result:
            for row in result:
                suppliers.append({
                    'id': int(row[0]),
                    'supplier_code': str(row[1]) if row[1] else '',
                    'name': str(row[2]) if row[2] else str(row[3]),  # name_ar أو name_en
                    'name_ar': str(row[2]) if row[2] else '',
                    'name_en': str(row[3]) if row[3] else '',
                    'type': str(row[4]) if row[4] else 'تجاري',
                    'contact_person': str(row[5]) if row[5] else '',
                    'phone': str(row[6]) if row[6] else '',
                    'email': str(row[7]) if row[7] else '',
                    'is_active': bool(row[8])
                })

        return jsonify({
            'success': True,
            'data': suppliers
        })

    except Exception as e:
        logger.error(f"خطأ في جلب الموردين: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب الموردين: {str(e)}'
        }), 500

@transfers_bp.route('/api/execution-history/<int:request_id>', methods=['GET'])
@login_required
def api_get_execution_history(request_id):
    """API للحصول على تاريخ تنفيذ طلب محدد"""
    try:
        db = DatabaseManager()

        query = """
        SELECT
            te.execution_reference, te.execution_date, te.executed_by,
            te.execution_amount, te.execution_currency, te.exchange_rate,
            te.commission_amount, te.net_amount, te.execution_notes,
            te.receipt_number, te.status,
            'مستخدم النظام' as executed_by_name
        FROM TRANSFER_EXECUTIONS te
        WHERE te.request_id = :1
        ORDER BY te.execution_date DESC
        """

        result = db.execute_query(query, [request_id])

        executions = []
        if result:
            for row in result:
                executions.append({
                    'execution_reference': str(row[0]) if row[0] else '',
                    'execution_date': row[1].strftime('%Y-%m-%d %H:%M:%S') if row[1] else None,
                    'executed_by': int(row[2]) if row[2] else 0,
                    'execution_amount': float(row[3]) if row[3] else 0,
                    'execution_currency': str(row[4]) if row[4] else '',
                    'exchange_rate': float(row[5]) if row[5] else 1.0,
                    'commission_amount': float(row[6]) if row[6] else 0,
                    'net_amount': float(row[7]) if row[7] else 0,
                    'execution_notes': str(row[8]) if row[8] else '',
                    'receipt_number': str(row[9]) if row[9] else '',
                    'status': str(row[10]) if row[10] else '',
                    'executed_by_name': str(row[11]) if row[11] else ''
                })

        return jsonify({
            'success': True,
            'data': executions
        })

    except Exception as e:
        logger.error(f"خطأ في جلب تاريخ التنفيذ: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب تاريخ التنفيذ: {str(e)}'
        }), 500

@transfers_bp.route('/api/execution-statistics', methods=['GET'])
@login_required
def api_execution_statistics():
    """API للحصول على إحصائيات التنفيذ"""
    try:
        db = DatabaseManager()

        # إحصائيات التنفيذ
        stats_query = """
        SELECT
            COUNT(DISTINCT tr.id) as total_approved,
            COUNT(DISTINCT te.id) as total_executed,
            SUM(CASE WHEN te.id IS NOT NULL THEN te.execution_amount ELSE 0 END) as total_executed_amount,
            AVG(CASE WHEN te.id IS NOT NULL THEN te.commission_amount ELSE 0 END) as avg_commission,
            COUNT(CASE WHEN tr.priority_level = 'urgent' AND te.id IS NULL THEN 1 END) as urgent_pending
        FROM TRANSFER_REQUESTS tr
        LEFT JOIN TRANSFER_EXECUTIONS te ON tr.id = te.request_id
        WHERE tr.status IN ('approved', 'executed')
        AND tr.created_at >= SYSDATE - 30
        """

        result = db.execute_query(stats_query)

        if result:
            row = result[0]
            statistics = {
                'total_approved': int(row[0]) if row[0] else 0,
                'total_executed': int(row[1]) if row[1] else 0,
                'total_executed_amount': float(row[2]) if row[2] else 0,
                'avg_commission': float(row[3]) if row[3] else 0,
                'urgent_pending': int(row[4]) if row[4] else 0,
                'execution_rate': round((int(row[1]) / int(row[0]) * 100) if row[0] and int(row[0]) > 0 else 0, 2)
            }
        else:
            statistics = {
                'total_approved': 0,
                'total_executed': 0,
                'total_executed_amount': 0,
                'avg_commission': 0,
                'urgent_pending': 0,
                'execution_rate': 0
            }

        return jsonify({
            'success': True,
            'data': statistics
        })

    except Exception as e:
        logger.error(f"خطأ في جلب إحصائيات التنفيذ: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب الإحصائيات: {str(e)}'
        }), 500

@transfers_bp.route('/api/execution-details/<int:transfer_id>')
@login_required
def api_get_execution_details(transfer_id):
    """API للحصول على تفاصيل التنفيذ الكاملة مع الموردين"""
    try:
        db = DatabaseManager()

        # جلب معلومات التحويل الأساسية
        transfer_query = """
        SELECT t.id, t.transfer_number, t.execution_reference, t.execution_date,
               t.execution_method, t.execution_notes, t.total_suppliers, t.execution_status,
               tr.request_number, tr.amount as original_amount, tr.currency,
               b.beneficiary_name, b.bank_name, b.bank_account, b.bank_branch
        FROM TRANSFERS t
        JOIN TRANSFER_REQUESTS tr ON t.request_id = tr.id
        JOIN BENEFICIARIES b ON tr.beneficiary_id = b.id
        WHERE t.id = :1
        """

        transfer_result = db.execute_query(transfer_query, [transfer_id])

        if not transfer_result:
            return jsonify({
                'success': False,
                'message': 'التحويل غير موجود'
            }), 404

        transfer_data = transfer_result[0]

        # جلب تفاصيل الموردين
        suppliers_query = """
        SELECT tes.id, tes.supplier_id, tes.supplier_name, tes.supplier_type,
               tes.amount, tes.currency, tes.exchange_rate, tes.commission,
               tes.supplier_reference, tes.supplier_notes, tes.execution_order,
               tes.status, tes.executed_at,
               s.supplier_code, s.name_ar, s.phone, s.email
        FROM transfer_execution_suppliers tes
        LEFT JOIN SUPPLIERS s ON tes.supplier_id = s.id
        WHERE tes.transfer_id = :1
        ORDER BY tes.execution_order
        """

        suppliers_result = db.execute_query(suppliers_query, [transfer_id])

        suppliers = []
        if suppliers_result:
            for row in suppliers_result:
                suppliers.append({
                    'id': int(row[0]),
                    'supplier_id': int(row[1]),
                    'supplier_name': str(row[2]),
                    'supplier_type': str(row[3]),
                    'amount': float(row[4]),
                    'currency': str(row[5]),
                    'exchange_rate': float(row[6]),
                    'commission': float(row[7]),
                    'supplier_reference': str(row[8]) if row[8] else '',
                    'supplier_notes': str(row[9]) if row[9] else '',
                    'execution_order': int(row[10]),
                    'status': str(row[11]),
                    'executed_at': row[12].isoformat() if row[12] else None,
                    'supplier_code': str(row[13]) if row[13] else '',
                    'supplier_name_ar': str(row[14]) if row[14] else '',
                    'phone': str(row[15]) if row[15] else '',
                    'email': str(row[16]) if row[16] else ''
                })

        execution_details = {
            'transfer_info': {
                'id': int(transfer_data[0]),
                'transfer_number': str(transfer_data[1]),
                'execution_reference': str(transfer_data[2]),
                'execution_date': transfer_data[3].isoformat() if transfer_data[3] else None,
                'execution_method': str(transfer_data[4]),
                'execution_notes': str(transfer_data[5]) if transfer_data[5] else '',
                'total_suppliers': int(transfer_data[6]),
                'execution_status': str(transfer_data[7])
            },
            'request_info': {
                'request_number': str(transfer_data[8]),
                'original_amount': float(transfer_data[9]),
                'currency': str(transfer_data[10])
            },
            'beneficiary_info': {
                'beneficiary_name': str(transfer_data[11]),
                'bank_name': str(transfer_data[12]),
                'bank_account': str(transfer_data[13]),
                'bank_branch': str(transfer_data[14]) if transfer_data[14] else ''
            },
            'suppliers': suppliers
        }

        return jsonify({
            'success': True,
            'data': execution_details
        })

    except Exception as e:
        logger.error(f"خطأ في جلب تفاصيل التنفيذ {transfer_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب تفاصيل التنفيذ: {str(e)}'
        }), 500
