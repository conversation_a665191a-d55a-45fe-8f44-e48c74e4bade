#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from oracle_manager import OracleManager

def verify_saved_data():
    """التحقق من البيانات المحفوظة"""
    oracle = OracleManager()
    
    try:
        oracle.connect()
        
        # فحص أوامر الشراء المحفوظة
        po_query = """
        SELECT ID, PO_NUMBER, SUPPLIER_NAME, TITLE, STATUS, 
               GROSS_AMOUNT, DISCOUNT_AMOUNT, NET_AMOUNT, CREATED_AT
        FROM PURCHASE_ORDERS 
        ORDER BY ID DESC
        FETCH FIRST 5 ROWS ONLY
        """
        purchase_orders = oracle.execute_query(po_query)
        
        print("📋 أوامر الشراء المحفوظة:")
        print("-" * 80)
        for po in purchase_orders:
            print(f"- ID: {po[0]}, رقم: {po[1]}, مورد: {po[2]}")
            print(f"  عنوان: {po[3]}, حالة: {po[4]}")
            print(f"  إجمالي: {po[5]}, خصم: {po[6]}, صافي: {po[7]}")
            print(f"  تاريخ الإنشاء: {po[8]}")
            print()
            
        # فحص أصناف أوامر الشراء
        items_query = """
        SELECT PO_ID, ITEM_CODE, ITEM_NAME, QUANTITY, UNIT_PRICE, 
               TOTAL_PRICE, PRODUCTION_DATE, EXPIRY_DATE
        FROM PO_ITEMS 
        ORDER BY ID DESC
        FETCH FIRST 10 ROWS ONLY
        """
        items = oracle.execute_query(items_query)
        
        print("📦 أصناف أوامر الشراء المحفوظة:")
        print("-" * 80)
        for item in items:
            print(f"- أمر رقم: {item[0]}, كود: {item[1]}, اسم: {item[2]}")
            print(f"  كمية: {item[3]}, سعر: {item[4]}, إجمالي: {item[5]}")
            print(f"  إنتاج: {item[6]}, انتهاء: {item[7]}")
            print()
            
        # إحصائيات
        count_query = "SELECT COUNT(*) FROM PURCHASE_ORDERS"
        po_count = oracle.execute_query(count_query)
        
        items_count_query = "SELECT COUNT(*) FROM PO_ITEMS"
        items_count = oracle.execute_query(items_count_query)
        
        print("📊 الإحصائيات:")
        print("-" * 40)
        print(f"- إجمالي أوامر الشراء: {po_count[0][0] if po_count else 0}")
        print(f"- إجمالي الأصناف: {items_count[0][0] if items_count else 0}")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
    finally:
        oracle.disconnect()

if __name__ == "__main__":
    verify_saved_data()
