# -*- coding: utf-8 -*-
"""
نماذج البيانات لنظام البريد الإلكتروني
Email System Data Models
"""

from datetime import datetime
from database_manager import DatabaseManager

class EmailAccount:
    """حسابات البريد الإلكتروني"""
    
    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.user_id = kwargs.get('user_id')
        self.email_address = kwargs.get('email_address')
        self.display_name = kwargs.get('display_name')
        self.smtp_server = kwargs.get('smtp_server')
        self.smtp_port = kwargs.get('smtp_port', 587)
        self.smtp_use_tls = kwargs.get('smtp_use_tls', True)
        self.smtp_use_ssl = kwargs.get('smtp_use_ssl', False)
        self.imap_server = kwargs.get('imap_server')
        self.imap_port = kwargs.get('imap_port', 993)
        self.imap_use_ssl = kwargs.get('imap_use_ssl', True)
        self.password_encrypted = kwargs.get('password_encrypted')
        self.signature = kwargs.get('signature')
        self.auto_reply_enabled = kwargs.get('auto_reply_enabled', False)
        self.auto_reply_message = kwargs.get('auto_reply_message')
        self.is_active = kwargs.get('is_active', True)
        self.is_default = kwargs.get('is_default', False)
        self.last_sync = kwargs.get('last_sync')
        self.created_at = kwargs.get('created_at')
        self.updated_at = kwargs.get('updated_at')
    
    @classmethod
    def query(cls):
        """إرجاع كائن استعلام مخصص"""
        return EmailAccountQuery(cls)

class EmailAccountQuery:
    """كلاس مساعد لاستعلامات EmailAccount"""

    def __init__(self, model_class):
        self.model_class = model_class
        self.filters = {}

    def filter_by(self, **kwargs):
        self.filters = kwargs
        return self
    
    def first(self):
        """جلب أول نتيجة"""
        db = DatabaseManager()
        try:
            conditions = []
            params = []
            
            for key, value in self.filters.items():
                conditions.append(f"{key} = :{len(params) + 1}")
                params.append(value)
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            query = f"SELECT * FROM email_accounts WHERE {where_clause}"
            
            results = db.execute_query(query, params)
            if results:
                row = results[0]
                return EmailAccount(
                    id=row[0], user_id=row[1], email_address=row[2],
                    display_name=row[3], smtp_server=row[4], smtp_port=row[5],
                    smtp_use_tls=bool(row[6]), smtp_use_ssl=bool(row[7]),
                    imap_server=row[8], imap_port=row[9], imap_use_ssl=bool(row[10]),
                    password_encrypted=row[11], signature=row[12],
                    auto_reply_enabled=bool(row[13]), auto_reply_message=row[14],
                    is_active=bool(row[15]), is_default=bool(row[16]),
                    last_sync=row[17], created_at=row[18], updated_at=row[19]
                )
            return None
        finally:
            db.close()
    
    def all(self):
        """جلب جميع النتائج"""
        db = DatabaseManager()
        try:
            conditions = []
            params = []
            
            for key, value in self.filters.items():
                conditions.append(f"{key} = :{len(params) + 1}")
                params.append(value)
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            query = f"SELECT * FROM email_accounts WHERE {where_clause} ORDER BY is_default DESC, created_at DESC"
            
            results = db.execute_query(query, params)
            accounts = []
            for row in results:
                account = EmailAccount(
                    id=row[0], user_id=row[1], email_address=row[2],
                    display_name=row[3], smtp_server=row[4], smtp_port=row[5],
                    smtp_use_tls=bool(row[6]), smtp_use_ssl=bool(row[7]),
                    imap_server=row[8], imap_port=row[9], imap_use_ssl=bool(row[10]),
                    password_encrypted=row[11], signature=row[12],
                    auto_reply_enabled=bool(row[13]), auto_reply_message=row[14],
                    is_active=bool(row[15]), is_default=bool(row[16]),
                    last_sync=row[17], created_at=row[18], updated_at=row[19]
                )
                accounts.append(account)
            return accounts
        finally:
            db.close()

class EmailFolder:
    """مجلدات البريد الإلكتروني"""
    
    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.account_id = kwargs.get('account_id')
        self.name = kwargs.get('name')
        self.name_arabic = kwargs.get('name_arabic')
        self.folder_type = kwargs.get('folder_type')
        self.parent_id = kwargs.get('parent_id')
        self.unread_count = kwargs.get('unread_count', 0)
        self.total_count = kwargs.get('total_count', 0)
        self.auto_delete_days = kwargs.get('auto_delete_days')
        self.is_system = kwargs.get('is_system', False)
        self.is_active = kwargs.get('is_active', True)
        self.sort_order = kwargs.get('sort_order', 0)
        self.created_at = kwargs.get('created_at')
    
    @classmethod
    def query(cls):
        return EmailFolderQuery(cls)

class EmailFolderQuery:
    """كلاس مساعد لاستعلامات EmailFolder"""

    def __init__(self, model_class):
        self.model_class = model_class
        self.filters = {}

    def filter_by(self, **kwargs):
        self.filters = kwargs
        return self
    
    def first(self):
        db = DatabaseManager()
        try:
            conditions = []
            params = []
            
            for key, value in self.filters.items():
                conditions.append(f"{key} = :{len(params) + 1}")
                params.append(value)
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            query = f"SELECT * FROM email_folders WHERE {where_clause}"
            
            results = db.execute_query(query, params)
            if results:
                row = results[0]
                return EmailFolder(
                    id=row[0], account_id=row[1], name=row[2], name_arabic=row[3],
                    folder_type=row[4], parent_id=row[5], unread_count=row[6],
                    total_count=row[7], auto_delete_days=row[8], is_system=bool(row[9]),
                    is_active=bool(row[10]), sort_order=row[11], created_at=row[12]
                )
            return None
        finally:
            db.close()

class EmailMessage:
    """رسائل البريد الإلكتروني"""
    
    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.account_id = kwargs.get('account_id')
        self.folder_id = kwargs.get('folder_id')
        self.message_id = kwargs.get('message_id')
        self.thread_id = kwargs.get('thread_id')
        self.in_reply_to = kwargs.get('in_reply_to')
        self.subject = kwargs.get('subject')
        self.sender_email = kwargs.get('sender_email')
        self.sender_name = kwargs.get('sender_name')
        self.to_emails = kwargs.get('to_emails')
        self.cc_emails = kwargs.get('cc_emails')
        self.bcc_emails = kwargs.get('bcc_emails')
        self.body_text = kwargs.get('body_text')
        self.body_html = kwargs.get('body_html')
        self.priority = kwargs.get('priority', 'normal')
        self.size_bytes = kwargs.get('size_bytes', 0)
        self.has_attachments = kwargs.get('has_attachments', False)
        self.is_read = kwargs.get('is_read', False)
        self.is_starred = kwargs.get('is_starred', False)
        self.is_important = kwargs.get('is_important', False)
        self.is_spam = kwargs.get('is_spam', False)
        self.is_deleted = kwargs.get('is_deleted', False)
        self.sent_at = kwargs.get('sent_at')
        self.received_at = kwargs.get('received_at')
        self.read_at = kwargs.get('read_at')
        self.created_at = kwargs.get('created_at')
        self.updated_at = kwargs.get('updated_at')
    
    @classmethod
    def query(cls):
        return EmailMessageQuery(cls)

class EmailMessageQuery:
    """كلاس مساعد لاستعلامات EmailMessage"""

    def __init__(self, model_class):
        self.model_class = model_class
        self.filters = {}

    def filter_by(self, **kwargs):
        self.filters = kwargs
        return self
    
    def order_by(self, field):
        self.order_field = field
        return self
    
    def limit(self, count):
        self.limit_count = count
        return self
    
    def all(self):
        db = DatabaseManager()
        try:
            conditions = []
            params = []
            
            for key, value in self.filters.items():
                conditions.append(f"{key} = :{len(params) + 1}")
                params.append(value)
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            order_clause = ""
            if hasattr(self, 'order_field'):
                if 'desc' in str(self.order_field).lower():
                    order_clause = " ORDER BY received_at DESC"
                else:
                    order_clause = " ORDER BY received_at ASC"
            
            limit_clause = ""
            if hasattr(self, 'limit_count'):
                limit_clause = f" AND ROWNUM <= {self.limit_count}"
            
            query = f"SELECT * FROM email_messages WHERE {where_clause}{limit_clause}{order_clause}"
            
            results = db.execute_query(query, params)
            messages = []
            for row in results:
                message = EmailMessage(
                    id=row[0], account_id=row[1], folder_id=row[2], message_id=row[3],
                    thread_id=row[4], in_reply_to=row[5], subject=row[6],
                    sender_email=row[7], sender_name=row[8], to_emails=row[9],
                    cc_emails=row[10], bcc_emails=row[11], body_text=row[12],
                    body_html=row[13], priority=row[14], size_bytes=row[15],
                    has_attachments=bool(row[16]), is_read=bool(row[17]),
                    is_starred=bool(row[18]), is_important=bool(row[19]),
                    is_spam=bool(row[20]), is_deleted=bool(row[21]),
                    sent_at=row[22], received_at=row[23], read_at=row[24],
                    created_at=row[25], updated_at=row[26]
                )
                messages.append(message)
            return messages
        finally:
            db.close()

# نماذج بسيطة للباقي
class EmailContact:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    @classmethod
    def query(cls):
        return EmailContactQuery(cls)

class EmailContactQuery:
    def __init__(self, model_class):
        self.model_class = model_class
        self.filters = {}

    def filter_by(self, **kwargs):
        self.filters = kwargs
        return self
    
    def order_by(self, field):
        return self
    
    def all(self):
        return []  # مؤقتاً

class EmailTemplate:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    @classmethod
    def query(cls):
        return EmailTemplateQuery(cls)

class EmailTemplateQuery:
    def __init__(self, model_class):
        self.model_class = model_class
        self.filters = {}

    def filter(self, *args):
        return self
    
    def order_by(self, field):
        return self
    
    def all(self):
        return []  # مؤقتاً

class EmailLabel:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

class EmailRule:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
