<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح JavaScript</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .upload-area { 
            border: 2px dashed #ccc; 
            padding: 20px; 
            text-align: center; 
            margin: 20px 0;
        }
        .btn { 
            background: #007bff; 
            color: white; 
            padding: 10px 20px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer;
        }
        .form-control, .form-select { 
            width: 100%; 
            padding: 8px; 
            margin: 5px 0; 
            border: 1px solid #ccc; 
            border-radius: 4px;
        }
        .text-success { color: green; }
        .mt-2 { margin-top: 10px; }
        .mb-3 { margin-bottom: 15px; }
    </style>
</head>
<body>
    <h1>🧪 اختبار إصلاح JavaScript لرفع الوثائق</h1>
    
    <div class="mb-3">
        <label>نوع الوثيقة:</label>
        <select class="form-select" name="document_type" required>
            <option value="">اختر نوع الوثيقة</option>
            <option value="bill_of_lading">بوليصة الشحن</option>
            <option value="commercial_invoice">الفاتورة التجارية</option>
        </select>
    </div>
    
    <div class="mb-3">
        <label>اسم الوثيقة:</label>
        <input type="text" class="form-control" name="document_name" placeholder="اسم الوثيقة (اختياري)">
    </div>
    
    <form id="uploadForm" enctype="multipart/form-data">
        <div class="upload-area" id="uploadArea">
            <h5>اسحب الملف هنا أو انقر للاختيار</h5>
            <p>الأنواع المدعومة: PDF, DOC, XLS, JPG, PNG (حد أقصى 10 MB)</p>
            <input type="file" id="fileInput" name="document_file" 
                   accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.tiff,.txt,.csv"
                   style="display: none;" required>
            <button type="button" class="btn" onclick="document.getElementById('fileInput').click()">
                اختيار ملف
            </button>
            <div id="selectedFileName" class="mt-2 text-success" style="display: none;">
                <span id="fileName"></span>
            </div>
        </div>
        
        <button type="submit" class="btn">
            رفع الوثيقة (اختبار)
        </button>
    </form>
    
    <div id="console" style="background: #f8f9fa; padding: 15px; margin-top: 20px; border-radius: 5px;">
        <h4>🔍 Console Output:</h4>
        <div id="consoleOutput"></div>
    </div>

    <script>
        // إعادة توجيه console.log للعرض في الصفحة
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('consoleOutput');
        
        function addToConsole(message, type = 'log') {
            const div = document.createElement('div');
            div.style.color = type === 'error' ? 'red' : 'black';
            div.style.marginBottom = '5px';
            div.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        // انتظار تحميل DOM بالكامل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOM loaded, initializing document upload...');
            
            // متغيرات عامة
            const shipmentId = 73; // للاختبار
            
            // إعداد منطقة السحب والإفلات
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            
            // التحقق من وجود العناصر المطلوبة
            if (!uploadArea) {
                console.error('❌ uploadArea element not found');
            }
            if (!fileInput) {
                console.error('❌ fileInput element not found');
            }
            
            console.log('✅ Elements found:', {
                uploadArea: !!uploadArea,
                fileInput: !!fileInput
            });

            // إضافة عرض اسم الملف المختار
            function updateFileDisplay() {
                // التحقق من وجود fileInput
                if (!fileInput) {
                    console.error('❌ fileInput غير متاح في updateFileDisplay');
                    return;
                }
                
                const file = fileInput.files && fileInput.files[0];
                const selectedFileName = document.getElementById('selectedFileName');
                const fileName = document.getElementById('fileName');
                
                if (file && selectedFileName && fileName) {
                    fileName.textContent = file.name + ' (' + (file.size / 1024 / 1024).toFixed(2) + ' MB)';
                    selectedFileName.style.display = 'block';
                    console.log('✅ تم اختيار الملف:', file.name, 'الحجم:', file.size);
                } else if (selectedFileName) {
                    selectedFileName.style.display = 'none';
                    console.log('❌ لم يتم اختيار ملف');
                }
            }

            // إضافة event listeners فقط إذا كانت العناصر موجودة
            if (uploadArea && fileInput) {
                fileInput.addEventListener('change', updateFileDisplay);
                console.log('✅ Event listeners added successfully');
            } else {
                console.error('❌ لا يمكن إضافة event listeners - العناصر مفقودة');
            }

            // رفع الوثيقة (اختبار)
            document.getElementById('uploadForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                // التحقق من الملف قبل الإرسال
                const fileInput = document.getElementById('fileInput');
                const documentType = document.querySelector('select[name="document_type"]').value;
                
                console.log('🔍 فحص البيانات قبل الإرسال:');
                console.log('  - fileInput:', fileInput);
                
                // التحقق من وجود عنصر الملف
                if (!fileInput) {
                    alert('خطأ: لم يتم العثور على عنصر اختيار الملف');
                    console.error('❌ fileInput element not found');
                    return;
                }
                
                console.log('  - fileInput.files:', fileInput.files);
                console.log('  - fileInput.files.length:', fileInput.files ? fileInput.files.length : 'null');
                console.log('  - documentType:', documentType);
                
                if (!documentType) {
                    alert('يرجى اختيار نوع الوثيقة');
                    return;
                }
                
                // التحقق من وجود ملفات
                if (!fileInput.files || fileInput.files.length === 0) {
                    alert('يرجى اختيار ملف للرفع');
                    console.error('❌ لا توجد ملفات محددة');
                    return;
                }
                
                const file = fileInput.files[0];
                
                // التحقق من صحة الملف
                if (!file) {
                    alert('خطأ في الملف المحدد');
                    console.error('❌ الملف غير صالح');
                    return;
                }
                
                console.log('  - file:', file);
                console.log('  - file.name:', file.name);
                console.log('  - file.size:', file.size);
                
                // التحقق من حجم الملف (10 MB)
                const maxSize = 10 * 1024 * 1024;
                if (file.size > maxSize) {
                    alert('حجم الملف كبير جداً. الحد الأقصى 10 MB');
                    console.error('❌ حجم الملف كبير:', file.size);
                    return;
                }
                
                console.log('✅ جميع الفحوصات نجحت! الملف جاهز للرفع');
                alert('✅ اختبار ناجح! الملف جاهز للرفع: ' + file.name);
            });
            
        }); // نهاية DOMContentLoaded
    </script>
</body>
</html>
