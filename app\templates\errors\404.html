{% extends "base.html" %}

{% block content %}
<div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
    <div class="text-center">
        <div class="error-code mb-4">
            <h1 class="display-1 fw-bold text-primary">404</h1>
        </div>
        
        <div class="error-message mb-4">
            <h2 class="h3 mb-3">الصفحة غير موجودة</h2>
            <p class="lead text-muted mb-4">
                عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.
            </p>
        </div>
        
        <div class="error-illustration mb-4">
            <i class="fas fa-search fa-5x text-muted opacity-50"></i>
        </div>
        
        <div class="error-actions">
            <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                <a href="{{ url_for('main.index') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-home me-2"></i>
                    العودة للرئيسية
                </a>
                
                {% if current_user.is_authenticated %}
                    <a href="{{ url_for('main.dashboard') }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة المعلومات
                    </a>
                {% endif %}
                
                <button onclick="history.back()" class="btn btn-outline-secondary btn-lg">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للخلف
                </button>
            </div>
        </div>
        
        <div class="error-help mt-5">
            <div class="card border-0 bg-light">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-lightbulb me-2"></i>
                        اقتراحات مفيدة
                    </h6>
                    <ul class="list-unstyled mb-0 text-start">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            تأكد من صحة الرابط المكتوب
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            استخدم شريط البحث للعثور على ما تريد
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            تصفح الأقسام الرئيسية من القائمة
                        </li>
                        <li>
                            <i class="fas fa-check text-success me-2"></i>
                            تواصل مع الدعم الفني إذا استمرت المشكلة
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add animation to error code
    const errorCode = document.querySelector('.error-code h1');
    errorCode.style.opacity = '0';
    errorCode.style.transform = 'scale(0.5)';
    errorCode.style.transition = 'all 0.5s ease';
    
    setTimeout(() => {
        errorCode.style.opacity = '1';
        errorCode.style.transform = 'scale(1)';
    }, 100);
    
    // Add bounce animation to illustration
    const illustration = document.querySelector('.error-illustration i');
    illustration.style.animation = 'bounce 2s infinite';
    
    // Add CSS for bounce animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
    `;
    document.head.appendChild(style);
});
</script>

<style>
.error-code h1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.error-message h2 {
    color: #495057;
}

.error-actions .btn {
    min-width: 180px;
}

@media (max-width: 576px) {
    .error-code h1 {
        font-size: 6rem;
    }
    
    .error-actions .btn {
        min-width: auto;
        width: 100%;
    }
}
</style>
{% endblock %}
