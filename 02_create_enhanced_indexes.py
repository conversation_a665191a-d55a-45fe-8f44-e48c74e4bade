#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إنشاء الفهارس المحسنة لجدول BALANCE_TRANSACTIONS
Create Enhanced Indexes for BALANCE_TRANSACTIONS Table
"""

import sys
import os
sys.path.insert(0, os.getcwd())

from oracle_manager import OracleManager

def check_existing_indexes():
    """فحص الفهارس الموجودة"""
    
    oracle = OracleManager()
    
    print("🔍 فحص الفهارس الموجودة على BALANCE_TRANSACTIONS...")
    print("=" * 70)
    
    # فحص الفهارس الحالية
    query = """
    SELECT 
        index_name,
        uniqueness,
        status,
        tablespace_name
    FROM user_indexes 
    WHERE table_name = 'BALANCE_TRANSACTIONS'
    ORDER BY index_name
    """
    
    result = oracle.execute_query(query)
    if result:
        print("الفهارس الحالية:")
        for row in result:
            print(f"   {row[0]}: {row[1]}, حالة: {row[2]}, مساحة: {row[3]}")
    else:
        print("لا توجد فهارس حالية")
    
    # فحص أعمدة الفهارس
    columns_query = """
    SELECT 
        i.index_name,
        ic.column_name,
        ic.column_position
    FROM user_indexes i
    JOIN user_ind_columns ic ON i.index_name = ic.index_name
    WHERE i.table_name = 'BALANCE_TRANSACTIONS'
    ORDER BY i.index_name, ic.column_position
    """
    
    columns_result = oracle.execute_query(columns_query)
    if columns_result:
        print("\nأعمدة الفهارس:")
        current_index = None
        for row in columns_result:
            if row[0] != current_index:
                print(f"   {row[0]}:")
                current_index = row[0]
            print(f"     {row[2]}. {row[1]}")
    
    return result

def create_enhanced_indexes():
    """إنشاء الفهارس المحسنة"""
    
    oracle = OracleManager()
    
    print("\n🔧 إنشاء الفهارس المحسنة...")
    print("=" * 70)
    
    # قائمة الفهارس المطلوب إنشاؤها
    indexes = [
        {
            "name": "IDX_BT_ENT_BAL",
            "columns": "ENTITY_TYPE_CODE, ENTITY_ID, CURRENCY_CODE",
            "description": "فهرس الكيان والعملة للاستعلامات السريعة"
        },
        {
            "name": "IDX_BT_PERIOD",
            "columns": "YEAR_NO, MONTH_NO",
            "description": "فهرس الفترة الزمنية للتقارير الشهرية والسنوية"
        },
        {
            "name": "IDX_BT_BRANCH",
            "columns": "BRANCH_ID, ENTITY_TYPE_CODE",
            "description": "فهرس الفرع ونوع الكيان"
        },
        {
            "name": "IDX_BT_DOC",
            "columns": "DOCUMENT_TYPE_CODE, DOCUMENT_NUMBER",
            "description": "فهرس نوع ورقم المستند"
        },
        {
            "name": "IDX_BT_DATE",
            "columns": "DOCUMENT_DATE",
            "description": "فهرس تاريخ المستند"
        },
        {
            "name": "IDX_BT_STATUS",
            "columns": "STATUS",
            "description": "فهرس حالة المعاملة"
        },
        {
            "name": "IDX_BT_COMPOSITE",
            "columns": "ENTITY_TYPE_CODE, ENTITY_ID, CURRENCY_CODE, BRANCH_ID, YEAR_NO",
            "description": "فهرس مركب للاستعلامات المعقدة"
        }
    ]
    
    created_indexes = []
    failed_indexes = []
    
    for index_info in indexes:
        try:
            # فحص إذا كان الفهرس موجود
            check_query = """
            SELECT COUNT(*) FROM user_indexes 
            WHERE index_name = :index_name
            """
            
            exists = oracle.execute_query(check_query, {"index_name": index_info["name"]})
            
            if exists and exists[0][0] > 0:
                print(f"   ⚠️ الفهرس {index_info['name']} موجود مسبقاً")
                continue
            
            # إنشاء الفهرس
            create_query = f"""
            CREATE INDEX {index_info['name']} 
            ON BALANCE_TRANSACTIONS ({index_info['columns']})
            """
            
            oracle.execute_update(create_query)
            print(f"   ✅ تم إنشاء الفهرس {index_info['name']}")
            print(f"      الأعمدة: {index_info['columns']}")
            print(f"      الوصف: {index_info['description']}")
            
            created_indexes.append(index_info["name"])
            
        except Exception as e:
            print(f"   ❌ فشل إنشاء الفهرس {index_info['name']}: {str(e)}")
            failed_indexes.append(index_info["name"])
    
    print(f"\nملخص إنشاء الفهارس:")
    print(f"   تم إنشاؤها: {len(created_indexes)}")
    print(f"   فشلت: {len(failed_indexes)}")
    
    return created_indexes, failed_indexes

def analyze_index_usage():
    """تحليل استخدام الفهارس"""
    
    oracle = OracleManager()
    
    print("\n📊 تحليل الفهارس المنشأة...")
    print("=" * 70)
    
    # إحصائيات الفهارس
    stats_query = """
    SELECT 
        index_name,
        num_rows,
        distinct_keys,
        leaf_blocks,
        clustering_factor,
        status
    FROM user_indexes 
    WHERE table_name = 'BALANCE_TRANSACTIONS'
    ORDER BY index_name
    """
    
    try:
        stats = oracle.execute_query(stats_query)
        if stats:
            print("إحصائيات الفهارس:")
            print("   الاسم | السجلات | المفاتيح | الكتل | التجميع | الحالة")
            print("   " + "-" * 65)
            for row in stats:
                print(f"   {row[0][:15]} | {row[1] or 'N/A'} | {row[2] or 'N/A'} | {row[3] or 'N/A'} | {row[4] or 'N/A'} | {row[5]}")
    except Exception as e:
        print(f"   ⚠️ لا يمكن الحصول على إحصائيات الفهارس: {str(e)}")

def test_index_performance():
    """اختبار أداء الفهارس"""
    
    oracle = OracleManager()
    
    print("\n⚡ اختبار أداء الفهارس...")
    print("=" * 70)
    
    # اختبارات أداء مختلفة
    performance_tests = [
        {
            "name": "استعلام الرصيد الحالي",
            "query": """
            SELECT SUM(BAL) 
            FROM BALANCE_TRANSACTIONS 
            WHERE ENTITY_TYPE_CODE = 'SUPPLIER' 
            AND ENTITY_ID = 1 
            AND CURRENCY_CODE = 'USD'
            """,
            "expected_index": "IDX_BT_ENT_BAL"
        },
        {
            "name": "تقرير شهري",
            "query": """
            SELECT ENTITY_TYPE_CODE, COUNT(*), SUM(BAL)
            FROM BALANCE_TRANSACTIONS 
            WHERE YEAR_NO = 2025 AND MONTH_NO = 9
            GROUP BY ENTITY_TYPE_CODE
            """,
            "expected_index": "IDX_BT_PERIOD"
        },
        {
            "name": "تقرير الفرع",
            "query": """
            SELECT ENTITY_TYPE_CODE, COUNT(*)
            FROM BALANCE_TRANSACTIONS 
            WHERE BRANCH_ID = 1
            GROUP BY ENTITY_TYPE_CODE
            """,
            "expected_index": "IDX_BT_BRANCH"
        },
        {
            "name": "البحث بالمستند",
            "query": """
            SELECT * 
            FROM BALANCE_TRANSACTIONS 
            WHERE DOCUMENT_TYPE_CODE = 'TRANSFER' 
            AND DOCUMENT_NUMBER = 'TRF-4444'
            """,
            "expected_index": "IDX_BT_DOC"
        }
    ]
    
    for test in performance_tests:
        try:
            print(f"\n   🧪 {test['name']}:")
            
            # تنفيذ الاستعلام وقياس الوقت
            import time
            start_time = time.time()
            
            result = oracle.execute_query(test["query"])
            
            end_time = time.time()
            execution_time = (end_time - start_time) * 1000  # بالميلي ثانية
            
            print(f"      وقت التنفيذ: {execution_time:.2f} ms")
            print(f"      عدد النتائج: {len(result) if result else 0}")
            print(f"      الفهرس المتوقع: {test['expected_index']}")
            
            if execution_time < 100:
                print(f"      ✅ أداء ممتاز")
            elif execution_time < 500:
                print(f"      🟡 أداء جيد")
            else:
                print(f"      🔴 أداء يحتاج تحسين")
                
        except Exception as e:
            print(f"      ❌ خطأ في الاختبار: {str(e)}")

def generate_index_recommendations():
    """إنشاء توصيات للفهارس"""
    
    print("\n💡 توصيات الفهارس:")
    print("=" * 70)
    
    recommendations = [
        "🎯 IDX_BT_ENT_BAL: الأهم للاستعلامات اليومية للأرصدة",
        "📅 IDX_BT_PERIOD: ضروري للتقارير الشهرية والسنوية",
        "🏢 IDX_BT_BRANCH: مهم للشركات متعددة الفروع",
        "📄 IDX_BT_DOC: مفيد للبحث عن معاملات محددة",
        "📊 IDX_BT_COMPOSITE: للاستعلامات المعقدة والتقارير المتقدمة",
        "⚡ مراقبة الأداء: راقب استخدام الفهارس وحدثها حسب الحاجة",
        "🔧 الصيانة: قم بإعادة بناء الفهارس دورياً للحفاظ على الأداء"
    ]
    
    for rec in recommendations:
        print(f"   {rec}")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء إنشاء الفهارس المحسنة لـ BALANCE_TRANSACTIONS")
    print("=" * 80)
    
    try:
        # 1. فحص الفهارس الموجودة
        existing_indexes = check_existing_indexes()
        
        # 2. إنشاء الفهارس المحسنة
        created, failed = create_enhanced_indexes()
        
        if created:
            # 3. تحليل الفهارس
            analyze_index_usage()
            
            # 4. اختبار الأداء
            test_index_performance()
            
            # 5. التوصيات
            generate_index_recommendations()
            
            print("\n🎉 تم إكمال إنشاء الفهارس المحسنة بنجاح!")
            print("✅ المهمة Ej8Uy8Uy8Uy8Uy8Uy8Uy8 مكتملة!")
            
            return True
        else:
            print("\n❌ لم يتم إنشاء أي فهارس جديدة")
            return False
            
    except Exception as e:
        print(f"\n❌ خطأ عام: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n" + "=" * 80)
        print("🏁 انتهى إنشاء الفهارس بنجاح - جاهز للمهمة التالية!")
    else:
        print("\n" + "=" * 80)
        print("🚫 فشل في إنشاء الفهارس - يرجى مراجعة الأخطاء")
