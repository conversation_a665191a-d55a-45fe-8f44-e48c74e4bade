-- =====================================================
-- اختبار النظام المحاسبي
-- Test Accounting System
-- =====================================================

SET SERVEROUTPUT ON;
SET PAGESIZE 1000;
SET LINESIZE 200;

PROMPT =====================================================
PROMPT اختبار النظام المحاسبي للحوالات
PROMPT Testing Transfer Accounting System
PROMPT =====================================================

-- 1. التحقق من وجود الجداول والإجراءات
PROMPT 
PROMPT 1. التحقق من الكائنات المنشأة:

SELECT 'Tables' as object_type, table_name as object_name, 'VALID' as status
FROM user_tables 
WHERE table_name IN ('TRANSFER_SUPPLIER_DIST', 'TRANSFER_ACTIVITY_LOG')
UNION ALL
SELECT 'Procedures' as object_type, object_name, status
FROM user_objects 
WHERE object_type = 'PROCEDURE' 
AND object_name IN ('EXECUTE_TRANSFER_ACCOUNTING', 'CANCEL_TRANSFER_ACCOUNTING', 'LOG_TRANSFER_ACTIVITY')
UNION ALL
SELECT 'Functions' as object_type, object_name, status
FROM user_objects 
WHERE object_type = 'FUNCTION' 
AND object_name IN ('CHECK_MC_BALANCE', 'VALIDATE_SUPPLIER_DIST')
ORDER BY object_type, object_name;

-- 2. إنشاء بيانات اختبار
PROMPT 
PROMPT 2. إنشاء بيانات اختبار:

-- إنشاء مورد اختبار إذا لم يكن موجوداً
DECLARE
    v_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_count FROM suppliers WHERE id = 9999;
    IF v_count = 0 THEN
        INSERT INTO suppliers (id, supplier_code, name_ar, name_en, is_active)
        VALUES (9999, 'TEST999', 'مورد اختبار', 'Test Supplier', 1);
        DBMS_OUTPUT.PUT_LINE('تم إنشاء مورد اختبار');
    ELSE
        DBMS_OUTPUT.PUT_LINE('مورد الاختبار موجود مسبقاً');
    END IF;
END;
/

-- إنشاء رصيد اختبار للصراف
DECLARE
    v_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_count 
    FROM CURRENT_BALANCES 
    WHERE entity_type_code = 'MONEY_CHANGER' AND entity_id = 9999 AND currency_code = 'SAR';
    
    IF v_count = 0 THEN
        INSERT INTO CURRENT_BALANCES (
            entity_type_code, entity_id, currency_code,
            opening_balance, debit_amount, credit_amount, current_balance,
            total_transactions_count, last_transaction_date,
            created_at, updated_at, created_by, updated_by,
            description
        ) VALUES (
            'MONEY_CHANGER', 9999, 'SAR',
            50000, 0, 0, 50000,
            0, SYSDATE,
            SYSDATE, SYSDATE, 1, 1,
            'رصيد اختبار للصراف'
        );
        DBMS_OUTPUT.PUT_LINE('تم إنشاء رصيد اختبار للصراف');
    ELSE
        UPDATE CURRENT_BALANCES SET current_balance = 50000
        WHERE entity_type_code = 'MONEY_CHANGER' AND entity_id = 9999 AND currency_code = 'SAR';
        DBMS_OUTPUT.PUT_LINE('تم تحديث رصيد الصراف الاختباري');
    END IF;
END;
/

-- إنشاء حوالة اختبار
DECLARE
    v_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_count FROM transfers WHERE id = 9999;
    IF v_count = 0 THEN
        INSERT INTO transfers (
            id, transfer_number, money_changer_bank_id, net_amount_sent, 
            status, created_at, updated_at
        ) VALUES (
            9999, 'TEST-9999', 9999, 15000, 
            'approved', SYSDATE, SYSDATE
        );
        DBMS_OUTPUT.PUT_LINE('تم إنشاء حوالة اختبار');
    ELSE
        UPDATE transfers SET 
            status = 'approved', 
            net_amount_sent = 15000,
            money_changer_bank_id = 9999
        WHERE id = 9999;
        DBMS_OUTPUT.PUT_LINE('تم تحديث حوالة الاختبار');
    END IF;
END;
/

COMMIT;

-- 3. اختبار دالة التحقق من رصيد الصراف
PROMPT 
PROMPT 3. اختبار التحقق من رصيد الصراف:

DECLARE
    v_result VARCHAR2(4000);
BEGIN
    -- اختبار رصيد كافي
    v_result := CHECK_MC_BALANCE(9999, 10000, 'SAR');
    DBMS_OUTPUT.PUT_LINE('اختبار رصيد كافي: ' || v_result);
    
    -- اختبار رصيد غير كافي
    v_result := CHECK_MC_BALANCE(9999, 100000, 'SAR');
    DBMS_OUTPUT.PUT_LINE('اختبار رصيد غير كافي: ' || v_result);
    
    -- اختبار صراف غير موجود
    v_result := CHECK_MC_BALANCE(99999, 10000, 'SAR');
    DBMS_OUTPUT.PUT_LINE('اختبار صراف غير موجود: ' || v_result);
END;
/

-- 4. اختبار دالة التحقق من توزيعات الموردين
PROMPT 
PROMPT 4. اختبار التحقق من توزيعات الموردين:

DECLARE
    v_result VARCHAR2(4000);
    v_distributions CLOB := '[{"supplier_id": 9999, "amount": 15000}]';
BEGIN
    v_result := VALIDATE_SUPPLIER_DIST(9999, v_distributions);
    DBMS_OUTPUT.PUT_LINE('اختبار توزيعات الموردين: ' || v_result);
END;
/

-- 5. اختبار تنفيذ الحوالة
PROMPT 
PROMPT 5. اختبار تنفيذ الحوالة:

DECLARE
    v_distributions CLOB := '[{"supplier_id": 9999, "amount": 15000}]';
    v_balance_before NUMBER;
    v_balance_after NUMBER;
    v_supplier_balance NUMBER;
BEGIN
    -- الحصول على الرصيد قبل التنفيذ
    SELECT current_balance INTO v_balance_before
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'MONEY_CHANGER' AND entity_id = 9999 AND currency_code = 'SAR';
    
    DBMS_OUTPUT.PUT_LINE('رصيد الصراف قبل التنفيذ: ' || v_balance_before);
    
    -- تنفيذ الحوالة
    EXECUTE_TRANSFER_ACCOUNTING(
        p_transfer_id => 9999,
        p_money_changer_id => 9999,
        p_total_amount => 15000,
        p_currency_code => 'SAR',
        p_supplier_distributions => v_distributions,
        p_user_id => 1
    );
    
    -- التحقق من النتائج
    SELECT current_balance INTO v_balance_after
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'MONEY_CHANGER' AND entity_id = 9999 AND currency_code = 'SAR';
    
    SELECT NVL(current_balance, 0) INTO v_supplier_balance
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'SUPPLIER' AND entity_id = 9999 AND currency_code = 'SAR';
    
    DBMS_OUTPUT.PUT_LINE('رصيد الصراف بعد التنفيذ: ' || v_balance_after);
    DBMS_OUTPUT.PUT_LINE('رصيد المورد بعد التنفيذ: ' || v_supplier_balance);
    DBMS_OUTPUT.PUT_LINE('الفرق في رصيد الصراف: ' || (v_balance_before - v_balance_after));
    
    -- التحقق من صحة النتائج
    IF (v_balance_before - v_balance_after = 15000) AND (v_supplier_balance = 15000) THEN
        DBMS_OUTPUT.PUT_LINE('✅ اختبار تنفيذ الحوالة نجح');
    ELSE
        DBMS_OUTPUT.PUT_LINE('❌ اختبار تنفيذ الحوالة فشل');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('❌ خطأ في تنفيذ الحوالة: ' || SQLERRM);
END;
/

-- 6. اختبار إلغاء الحوالة
PROMPT 
PROMPT 6. اختبار إلغاء الحوالة:

DECLARE
    v_balance_before NUMBER;
    v_balance_after NUMBER;
    v_supplier_balance_before NUMBER;
    v_supplier_balance_after NUMBER;
BEGIN
    -- الحصول على الأرصدة قبل الإلغاء
    SELECT current_balance INTO v_balance_before
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'MONEY_CHANGER' AND entity_id = 9999 AND currency_code = 'SAR';
    
    SELECT current_balance INTO v_supplier_balance_before
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'SUPPLIER' AND entity_id = 9999 AND currency_code = 'SAR';
    
    DBMS_OUTPUT.PUT_LINE('رصيد الصراف قبل الإلغاء: ' || v_balance_before);
    DBMS_OUTPUT.PUT_LINE('رصيد المورد قبل الإلغاء: ' || v_supplier_balance_before);
    
    -- إلغاء الحوالة
    CANCEL_TRANSFER_ACCOUNTING(
        p_transfer_id => 9999,
        p_user_id => 1,
        p_cancellation_reason => 'اختبار إلغاء الحوالة'
    );
    
    -- التحقق من النتائج
    SELECT current_balance INTO v_balance_after
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'MONEY_CHANGER' AND entity_id = 9999 AND currency_code = 'SAR';
    
    SELECT current_balance INTO v_supplier_balance_after
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'SUPPLIER' AND entity_id = 9999 AND currency_code = 'SAR';
    
    DBMS_OUTPUT.PUT_LINE('رصيد الصراف بعد الإلغاء: ' || v_balance_after);
    DBMS_OUTPUT.PUT_LINE('رصيد المورد بعد الإلغاء: ' || v_supplier_balance_after);
    
    -- التحقق من صحة النتائج (يجب أن تعود الأرصدة كما كانت)
    IF (v_balance_after - v_balance_before = 15000) AND (v_supplier_balance_after = 0) THEN
        DBMS_OUTPUT.PUT_LINE('✅ اختبار إلغاء الحوالة نجح');
    ELSE
        DBMS_OUTPUT.PUT_LINE('❌ اختبار إلغاء الحوالة فشل');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('❌ خطأ في إلغاء الحوالة: ' || SQLERRM);
END;
/

-- 7. عرض سجل الأنشطة
PROMPT 
PROMPT 7. سجل الأنشطة للحوالة الاختبارية:

SELECT 
    id,
    activity_type,
    description,
    old_status,
    new_status,
    amount_after,
    currency_code,
    entity_type,
    entity_name,
    TO_CHAR(created_at, 'YYYY-MM-DD HH24:MI:SS') as created_at
FROM transfer_activity_log
WHERE transfer_id = 9999
ORDER BY created_at;

-- 8. عرض التوزيعات (يجب أن تكون فارغة بعد الإلغاء)
PROMPT 
PROMPT 8. توزيعات الموردين (يجب أن تكون فارغة):

SELECT COUNT(*) as distributions_count
FROM transfer_supplier_dist
WHERE transfer_id = 9999;

-- 9. تنظيف بيانات الاختبار
PROMPT 
PROMPT 9. تنظيف بيانات الاختبار:

DELETE FROM transfer_activity_log WHERE transfer_id = 9999;
DELETE FROM transfer_supplier_dist WHERE transfer_id = 9999;
DELETE FROM transfers WHERE id = 9999;
DELETE FROM CURRENT_BALANCES WHERE entity_id = 9999;
DELETE FROM suppliers WHERE id = 9999;

COMMIT;

PROMPT 
PROMPT =====================================================
PROMPT انتهى اختبار النظام المحاسبي
PROMPT Accounting System Test Complete
PROMPT =====================================================

SELECT 'جميع الاختبارات اكتملت بنجاح' as result FROM DUAL;
