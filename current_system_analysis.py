#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
تحليل شامل للنظام المحاسبي الحالي
Comprehensive Analysis of Current Accounting System
"""

import sys
import os
sys.path.insert(0, os.getcwd())

from oracle_manager import OracleManager

def analyze_current_balances():
    """تحليل جدول CURRENT_BALANCES"""
    
    oracle = OracleManager()
    
    print("📊 تحليل جدول CURRENT_BALANCES")
    print("=" * 60)
    
    # بنية الجدول
    print("1️⃣ بنية الجدول:")
    structure_query = """
    SELECT column_name, data_type, nullable, data_default
    FROM user_tab_columns 
    WHERE table_name = 'CURRENT_BALANCES'
    ORDER BY column_id
    """
    
    structure = oracle.execute_query(structure_query)
    if structure:
        for row in structure:
            nullable = 'NULL' if row[2] == 'Y' else 'NOT NULL'
            default = f', افتراضي: {row[3]}' if row[3] else ''
            print(f"   {row[0]}: {row[1]} ({nullable}{default})")
    
    # إحصائيات البيانات
    print("\n2️⃣ إحصائيات البيانات:")
    stats_query = """
    SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT entity_type_code) as entity_types,
        COUNT(DISTINCT currency_code) as currencies,
        SUM(CASE WHEN current_balance > 0 THEN 1 ELSE 0 END) as positive_balances,
        SUM(CASE WHEN current_balance < 0 THEN 1 ELSE 0 END) as negative_balances,
        SUM(CASE WHEN current_balance IS NULL THEN 1 ELSE 0 END) as null_balances
    FROM CURRENT_BALANCES
    """
    
    stats = oracle.execute_query(stats_query)
    if stats:
        total, entity_types, currencies, positive, negative, null_bal = stats[0]
        print(f"   إجمالي السجلات: {total}")
        print(f"   أنواع الكيانات: {entity_types}")
        print(f"   العملات: {currencies}")
        print(f"   أرصدة موجبة: {positive}")
        print(f"   أرصدة سالبة: {negative}")
        print(f"   أرصدة NULL: {null_bal}")
    
    # أنواع الكيانات
    print("\n3️⃣ أنواع الكيانات:")
    entities_query = """
    SELECT entity_type_code, COUNT(*) as count
    FROM CURRENT_BALANCES
    GROUP BY entity_type_code
    ORDER BY count DESC
    """
    
    entities = oracle.execute_query(entities_query)
    if entities:
        for row in entities:
            print(f"   {row[0]}: {row[1]} سجل")

def analyze_opening_balances():
    """تحليل جدول OPENING_BALANCES"""
    
    oracle = OracleManager()
    
    print("\n📊 تحليل جدول OPENING_BALANCES")
    print("=" * 60)
    
    # فحص وجود الجدول
    table_exists_query = """
    SELECT COUNT(*) FROM user_tables WHERE table_name = 'OPENING_BALANCES'
    """
    
    table_exists = oracle.execute_query(table_exists_query)
    if table_exists and table_exists[0][0] > 0:
        print("✅ جدول OPENING_BALANCES موجود")
        
        # بنية الجدول
        structure_query = """
        SELECT column_name, data_type, nullable
        FROM user_tab_columns 
        WHERE table_name = 'OPENING_BALANCES'
        ORDER BY column_id
        """
        
        structure = oracle.execute_query(structure_query)
        if structure:
            print("1️⃣ بنية الجدول:")
            for row in structure:
                nullable = 'NULL' if row[2] == 'Y' else 'NOT NULL'
                print(f"   {row[0]}: {row[1]} ({nullable})")
        
        # إحصائيات البيانات
        stats_query = """
        SELECT COUNT(*) as total_records
        FROM OPENING_BALANCES
        """
        
        stats = oracle.execute_query(stats_query)
        if stats:
            print(f"\n2️⃣ إجمالي السجلات: {stats[0][0]}")
    else:
        print("❌ جدول OPENING_BALANCES غير موجود")

def analyze_balance_transactions():
    """تحليل جدول BALANCE_TRANSACTIONS"""
    
    oracle = OracleManager()
    
    print("\n📊 تحليل جدول BALANCE_TRANSACTIONS")
    print("=" * 60)
    
    # بنية الجدول
    print("1️⃣ بنية الجدول:")
    structure_query = """
    SELECT column_name, data_type, nullable
    FROM user_tab_columns 
    WHERE table_name = 'BALANCE_TRANSACTIONS'
    ORDER BY column_id
    """
    
    structure = oracle.execute_query(structure_query)
    if structure:
        for row in structure:
            nullable = 'NULL' if row[2] == 'Y' else 'NOT NULL'
            print(f"   {row[0]}: {row[1]} ({nullable})")
    
    # إحصائيات البيانات
    print("\n2️⃣ إحصائيات البيانات:")
    stats_query = """
    SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT entity_type_code) as entity_types,
        COUNT(DISTINCT document_type_code) as document_types,
        COUNT(DISTINCT currency_code) as currencies,
        MIN(document_date) as earliest_date,
        MAX(document_date) as latest_date
    FROM BALANCE_TRANSACTIONS
    """
    
    stats = oracle.execute_query(stats_query)
    if stats:
        total, entity_types, doc_types, currencies, earliest, latest = stats[0]
        print(f"   إجمالي السجلات: {total}")
        print(f"   أنواع الكيانات: {entity_types}")
        print(f"   أنواع المستندات: {doc_types}")
        print(f"   العملات: {currencies}")
        print(f"   أقدم معاملة: {earliest}")
        print(f"   أحدث معاملة: {latest}")
    
    # أنواع المستندات
    print("\n3️⃣ أنواع المستندات:")
    doc_types_query = """
    SELECT document_type_code, COUNT(*) as count
    FROM BALANCE_TRANSACTIONS
    GROUP BY document_type_code
    ORDER BY count DESC
    """
    
    doc_types = oracle.execute_query(doc_types_query)
    if doc_types:
        for row in doc_types:
            print(f"   {row[0]}: {row[1]} معاملة")

def analyze_entity_types():
    """تحليل جدول ENTITY_TYPES"""
    
    oracle = OracleManager()
    
    print("\n📊 تحليل جدول ENTITY_TYPES")
    print("=" * 60)
    
    # أنواع الكيانات الموجودة
    query = """
    SELECT code, name_ar, name_en, is_active
    FROM ENTITY_TYPES
    ORDER BY code
    """
    
    result = oracle.execute_query(query)
    if result:
        print("أنواع الكيانات الموجودة:")
        for row in result:
            status = "نشط" if row[3] == 1 else "غير نشط"
            print(f"   {row[0]}: {row[1]} ({row[2]}) - {status}")
    
    # الكيانات المطلوب إضافتها
    print("\n🔍 الكيانات المطلوب إضافتها:")
    required_entities = [
        ('PURCHASE_AGENT', 'مندوب مشتريات', 'Purchase Agent'),
        ('SALES_AGENT', 'مندوب مبيعات', 'Sales Agent'),
        ('SHIPPING_COMPANY', 'شركة شحن', 'Shipping Company')
    ]
    
    for code, name_ar, name_en in required_entities:
        check_query = "SELECT COUNT(*) FROM ENTITY_TYPES WHERE code = :code"
        exists = oracle.execute_query(check_query, {'code': code})
        
        if exists and exists[0][0] > 0:
            print(f"   ✅ {code}: موجود مسبقاً")
        else:
            print(f"   ❌ {code}: مطلوب إضافته")

def identify_issues():
    """تحديد المشاكل في النظام الحالي"""
    
    oracle = OracleManager()
    
    print("\n🚨 المشاكل المحددة في النظام الحالي:")
    print("=" * 60)
    
    issues = []
    
    # مشكلة 1: تضارب البيانات
    print("1️⃣ فحص تضارب البيانات بين CURRENT_BALANCES و BALANCE_TRANSACTIONS:")
    
    # مشكلة 2: عدم وجود تتبع تاريخي
    print("2️⃣ عدم وجود تتبع تاريخي كامل:")
    print("   ❌ CURRENT_BALANCES يحفظ الرصيد الحالي فقط")
    print("   ❌ لا يوجد سجل للتغييرات التاريخية")
    
    # مشكلة 3: عدم دعم الفروع
    print("3️⃣ عدم دعم الفروع المتعددة:")
    print("   ❌ لا يوجد عمود BRANCH_ID")
    
    # مشكلة 4: عدم دعم التقارير الشهرية
    print("4️⃣ صعوبة التقارير الشهرية:")
    print("   ❌ لا يوجد أعمدة MONTH_NO و YEAR_NO")
    
    # مشكلة 5: تعقيد العملات المتعددة
    print("5️⃣ تعقيد العملات المتعددة:")
    print("   ❌ لا يوجد دعم محسن لأسعار الصرف")
    
    return issues

def generate_recommendations():
    """إنشاء التوصيات للنظام الجديد"""
    
    print("\n💡 التوصيات للنظام الجديد:")
    print("=" * 60)
    
    recommendations = [
        "🎯 توحيد النظام: استخدام BALANCE_TRANSACTIONS كمصدر وحيد للحقيقة",
        "📊 إضافة أعمدة: BAL, BAL_F, MONTH_NO, YEAR_NO, BRANCH_ID",
        "🏢 دعم الفروع: إضافة BRANCH_ID لجميع المعاملات",
        "📅 التقارير الشهرية: أعمدة MONTH_NO و YEAR_NO للتقارير السريعة",
        "💱 العملات المتعددة: BAL_F لدعم أسعار الصرف",
        "📦 Packages موحدة: OB_PKG و BT_PKG بتسميات مختصرة",
        "🔍 Views محسنة: V_CURR_BAL و V_MONTH_BAL للاستعلامات السريعة",
        "⚡ فهارس محسنة: فهارس على الأعمدة الجديدة للأداء",
        "🛡️ أمان البيانات: إجراءات محمية مع معالجة الأخطاء",
        "📋 تتبع كامل: سجل تاريخي شامل لجميع المعاملات"
    ]
    
    for i, rec in enumerate(recommendations, 1):
        print(f"   {rec}")

if __name__ == "__main__":
    print("🔍 بدء التحليل الشامل للنظام المحاسبي الحالي")
    print("=" * 80)
    
    try:
        analyze_current_balances()
        analyze_opening_balances()
        analyze_balance_transactions()
        analyze_entity_types()
        identify_issues()
        generate_recommendations()
        
        print("\n✅ تم إكمال التحليل الشامل للنظام الحالي")
        print("📋 النتائج جاهزة لوضع خطة النظام الجديد")
        
    except Exception as e:
        print(f"❌ خطأ في التحليل: {str(e)}")
    
    print("\n" + "=" * 80)
    print("🏁 انتهى التحليل")
