<!-- واجهة اختيار جهات الاتصال للأتمتة -->
<div class="modal fade" id="contactSelectorModal" tabindex="-1" aria-labelledby="contactSelectorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="contactSelectorModalLabel">
                    <i class="fas fa-address-book me-2"></i>
                    اختيار جهات الاتصال للإشعارات
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <!-- فلاتر البحث -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <label class="form-label">نوع جهة الاتصال</label>
                        <select class="form-select" id="contactTypeFilter">
                            <option value="">جميع الأنواع</option>
                            <option value="CUSTOMER">العملاء</option>
                            <option value="DRIVER">السائقين</option>
                            <option value="AGENT">المخلصين الجمركيين</option>
                            <option value="MANAGER">المديرين</option>
                            <option value="EXTERNAL">خارجي</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">الأولوية</label>
                        <select class="form-select" id="priorityFilter">
                            <option value="">جميع المستويات</option>
                            <option value="8">أولوية عالية (8+)</option>
                            <option value="5">أولوية متوسطة (5+)</option>
                            <option value="1">جميع الأولويات</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">البحث</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="contactSearchInput" placeholder="البحث في الأسماء...">
                            <button class="btn btn-outline-primary" onclick="filterContacts()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- مجموعات سريعة -->
                <div class="mb-4">
                    <h6 class="mb-3">
                        <i class="fas fa-layer-group me-2"></i>
                        مجموعات سريعة
                    </h6>
                    <div class="row" id="quickGroups">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>

                <!-- قائمة جهات الاتصال -->
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">
                            <i class="fas fa-users me-2"></i>
                            جهات الاتصال المتاحة
                            <span class="badge bg-secondary ms-2" id="contactsCount">0</span>
                        </h6>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" onclick="selectAllContacts()">
                                <i class="fas fa-check-square me-1"></i>
                                تحديد الكل
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="clearAllContacts()">
                                <i class="fas fa-square me-1"></i>
                                إلغاء التحديد
                            </button>
                        </div>
                    </div>
                    
                    <div class="contact-list" id="contactsList" style="max-height: 400px; overflow-y: auto;">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>

                <!-- جهات الاتصال المختارة -->
                <div class="selected-contacts-section">
                    <h6 class="mb-3">
                        <i class="fas fa-check-circle me-2 text-success"></i>
                        جهات الاتصال المختارة
                        <span class="badge bg-success ms-2" id="selectedCount">0</span>
                    </h6>
                    <div class="selected-contacts" id="selectedContacts" style="min-height: 60px; max-height: 200px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem; padding: 1rem; background-color: #f8f9fa;">
                        <p class="text-muted mb-0" id="noSelectionMessage">لم يتم اختيار أي جهة اتصال بعد</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer bg-light border-top">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <div>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="forceCloseModal()">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm ms-2" onclick="forceCloseModal()" title="إغلاق قسري في حالة التعطل">
                            <i class="fas fa-power-off"></i>
                        </button>
                    </div>
                    <div>
                        <button type="button" class="btn btn-success btn-lg shadow" onclick="confirmContactSelection()" id="confirmButton" disabled
                                title="اختر جهة اتصال واحدة على الأقل لتفعيل زر الحفظ">
                            <i class="fas fa-save me-2"></i>
                            <span id="saveButtonText">حفظ الربط (<span id="confirmCount">0</span> جهة اتصال)</span>
                        </button>
                    </div>
                </div>

                <!-- رسالة توضيحية -->
                <div class="w-100 mt-2">
                    <small class="text-muted d-block text-center" id="saveHelpText">
                        <i class="fas fa-info-circle me-1"></i>
                        اختر جهات الاتصال من القائمة أعلاه ثم اضغط "حفظ الربط"
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.contact-item {
    border: 1px solid #e3e6f0;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.contact-item:hover {
    border-color: #4e73df;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.contact-item.selected {
    border-color: #28a745;
    background-color: #d4edda;
}

.contact-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #4e73df, #224abe);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1rem;
}

.contact-type-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.priority-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-left: 0.5rem;
}

.priority-high { background-color: #e74a3b; }
.priority-medium { background-color: #f39c12; }
.priority-low { background-color: #28a745; }

.vip-badge {
    background: linear-gradient(45deg, #f6c23e, #dda20a);
    color: white;
    border: none;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* أنيميشن لزر الحفظ */
.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* أنيميشن لزر الحفظ */
.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

.quick-group-card {
    border: 1px solid #e3e6f0;
    border-radius: 0.375rem;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.quick-group-card:hover {
    border-color: #4e73df;
    background-color: #f8f9fc;
}

.quick-group-card.selected {
    border-color: #28a745;
    background-color: #d4edda;
}

.selected-contact-tag {
    display: inline-block;
    background: #4e73df;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    margin: 0.25rem;
    font-size: 0.875rem;
}

.selected-contact-tag .remove-btn {
    margin-left: 0.5rem;
    cursor: pointer;
    opacity: 0.8;
}

.selected-contact-tag .remove-btn:hover {
    opacity: 1;
}
</style>

<script>
// متغيرات عامة
let allContacts = [];
let filteredContacts = [];
let selectedContacts = [];
let quickGroups = [];

// تهيئة واجهة اختيار جهات الاتصال
function initContactSelector() {
    console.log('تهيئة واجهة اختيار جهات الاتصال');

    try {
        // إعادة تعيين المتغيرات
        allContacts = [];
        filteredContacts = [];
        selectedContacts = [];
        quickGroups = [];

        // التحقق من وجود العناصر المطلوبة
        const requiredElements = [
            'contactsList',
            'selectedContacts',
            'contactsCount',
            'selectedCount',
            'confirmButton',
            'confirmCount'
        ];

        const missingElements = requiredElements.filter(id => !document.getElementById(id));
        if (missingElements.length > 0) {
            console.warn('عناصر مفقودة:', missingElements);
        }

        // جلب المجموعات السريعة
        loadQuickGroups();

        // جلب جهات الاتصال
        loadContacts();

        // إعداد event listeners بشكل آمن
        setupEventListeners();

        // تحديث العدادات
        updateSelectionCount();

        console.log('تم تهيئة واجهة اختيار جهات الاتصال بنجاح');

    } catch (error) {
        console.error('خطأ في تهيئة واجهة اختيار جهات الاتصال:', error);
    }
}

// إعداد event listeners بشكل آمن
function setupEventListeners() {
    const eventListeners = [
        { id: 'contactTypeFilter', event: 'change', handler: filterContacts },
        { id: 'priorityFilter', event: 'change', handler: filterContacts },
        { id: 'contactSearchInput', event: 'input', handler: filterContacts }
    ];

    eventListeners.forEach(({ id, event, handler }) => {
        const element = document.getElementById(id);
        if (element) {
            // إزالة event listeners السابقة لتجنب التكرار
            element.removeEventListener(event, handler);
            element.addEventListener(event, handler);
            console.log(`تم إعداد event listener لـ ${id}`);
        } else {
            console.warn(`لم يتم العثور على العنصر: ${id}`);
        }
    });
}

// جلب المجموعات السريعة
function loadQuickGroups() {
    fetch('/notifications/contacts/api/automation/contact-groups')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            quickGroups = data.groups;
            renderQuickGroups();
        }
    })
    .catch(error => {
        console.error('خطأ في جلب المجموعات:', error);
    });
}

// عرض المجموعات السريعة
function renderQuickGroups() {
    const container = document.getElementById('quickGroups');
    container.innerHTML = '';
    
    quickGroups.forEach(group => {
        const groupCard = document.createElement('div');
        groupCard.className = 'col-md-4 col-lg-3 mb-2';
        groupCard.innerHTML = `
            <div class="quick-group-card" onclick="selectGroup('${group.id}')">
                <i class="${group.icon} fa-2x text-primary mb-2"></i>
                <div class="fw-bold">${group.name}</div>
                <small class="text-muted">${group.description}</small>
            </div>
        `;
        container.appendChild(groupCard);
    });
}

// جلب جهات الاتصال
function loadContacts() {
    console.log('جلب جهات الاتصال من API...');

    fetch('/notifications/contacts/api/automation/contacts')
    .then(response => {
        console.log('استجابة API:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('بيانات جهات الاتصال:', data);
        if (data.success) {
            allContacts = data.contacts || [];
            filteredContacts = [...allContacts];
            console.log(`تم تحميل ${allContacts.length} جهة اتصال`);
            renderContacts();
            updateContactsCount();
        } else {
            console.error('فشل في جلب جهات الاتصال:', data.message);
            showContactsError('فشل في تحميل جهات الاتصال: ' + (data.message || 'خطأ غير معروف'));
        }
    })
    .catch(error => {
        console.error('خطأ في جلب جهات الاتصال:', error);
        showContactsError('خطأ في الاتصال بالخادم. يرجى المحاولة مرة أخرى.');
    });
}

// عرض رسالة خطأ في قائمة جهات الاتصال
function showContactsError(message) {
    const container = document.getElementById('contactsList');
    if (container) {
        container.innerHTML = `
            <div class="alert alert-danger text-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
                <br>
                <button class="btn btn-sm btn-outline-danger mt-2" onclick="loadContacts()">
                    <i class="fas fa-redo me-1"></i>
                    إعادة المحاولة
                </button>
            </div>
        `;
    }
}

// تصفية جهات الاتصال
function filterContacts() {
    const typeFilter = document.getElementById('contactTypeFilter').value;
    const priorityFilter = document.getElementById('priorityFilter').value;
    const searchFilter = document.getElementById('contactSearchInput').value.toLowerCase();
    
    filteredContacts = allContacts.filter(contact => {
        // فلتر النوع
        if (typeFilter && contact.type !== typeFilter) return false;
        
        // فلتر الأولوية
        if (priorityFilter && contact.priority < parseInt(priorityFilter)) return false;
        
        // فلتر البحث
        if (searchFilter && !contact.name.toLowerCase().includes(searchFilter)) return false;
        
        return true;
    });
    
    renderContacts();
    updateContactsCount();
}

// عرض جهات الاتصال
function renderContacts() {
    const container = document.getElementById('contactsList');

    if (!container) {
        console.warn('لم يتم العثور على container قائمة جهات الاتصال');
        return;
    }

    container.innerHTML = '';

    if (filteredContacts.length === 0) {
        container.innerHTML = '<p class="text-muted text-center py-3">لا توجد جهات اتصال مطابقة للفلاتر المحددة</p>';
        return;
    }
    
    filteredContacts.forEach(contact => {
        const isSelected = selectedContacts.some(sc => sc.id === contact.id);
        const contactItem = document.createElement('div');
        contactItem.className = `contact-item ${isSelected ? 'selected' : ''}`;
        contactItem.onclick = () => toggleContactSelection(contact);
        
        const priorityClass = contact.priority >= 8 ? 'priority-high' : 
                             contact.priority >= 5 ? 'priority-medium' : 'priority-low';
        
        const typeNames = {
            'CUSTOMER': 'عميل',
            'DRIVER': 'سائق',
            'AGENT': 'مخلص',
            'MANAGER': 'مدير',
            'EXTERNAL': 'خارجي'
        };
        
        contactItem.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="contact-avatar me-3">
                    ${contact.name.charAt(0).toUpperCase()}
                </div>
                <div class="flex-grow-1">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">
                                ${contact.name}
                                ${contact.is_vip ? '<span class="badge vip-badge ms-1">VIP</span>' : ''}
                                <span class="priority-indicator ${priorityClass}"></span>
                            </h6>
                            <span class="badge contact-type-badge bg-primary">${typeNames[contact.type] || contact.type}</span>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input contact-checkbox"
                                   type="checkbox"
                                   ${isSelected ? 'checked' : ''}
                                   data-contact-id="${contact.id}"
                                   onchange="handleContactCheckboxChange(this, ${JSON.stringify(contact).replace(/"/g, '&quot;')})">
                        </div>
                    </div>
                    <div class="mt-2">
                        ${contact.phone ? `<small class="text-muted me-3"><i class="fas fa-phone me-1"></i>${contact.phone}</small>` : ''}
                        ${contact.email ? `<small class="text-muted me-3"><i class="fas fa-envelope me-1"></i>${contact.email}</small>` : ''}
                        ${contact.company ? `<small class="text-muted"><i class="fas fa-building me-1"></i>${contact.company}</small>` : ''}
                    </div>
                </div>
            </div>
        `;
        
        container.appendChild(contactItem);
    });
}

// معالجة تغيير checkbox جهة الاتصال
function handleContactCheckboxChange(checkbox, contact) {
    console.log('تغيير checkbox:', contact.name, 'محدد:', checkbox.checked);

    const index = selectedContacts.findIndex(sc => sc.id === contact.id);

    if (checkbox.checked && index === -1) {
        selectedContacts.push(contact);
        console.log('تم إضافة:', contact.name);
    } else if (!checkbox.checked && index > -1) {
        selectedContacts.splice(index, 1);
        console.log('تم إزالة:', contact.name);
    }

    console.log('إجمالي المختارة:', selectedContacts.length);

    // تحديث العرض
    updateContactItemSelection(contact.id, checkbox.checked);
    renderSelectedContacts();
    updateSelectionCount();
}

// تبديل اختيار جهة اتصال (للاستخدام مع النقر على العنصر)
function toggleContactSelection(contact) {
    console.log('تبديل اختيار جهة الاتصال:', contact.name);

    const index = selectedContacts.findIndex(sc => sc.id === contact.id);
    const isSelected = index > -1;

    if (isSelected) {
        selectedContacts.splice(index, 1);
        console.log('تم إلغاء اختيار:', contact.name);
    } else {
        selectedContacts.push(contact);
        console.log('تم اختيار:', contact.name);
    }

    console.log('جهات الاتصال المختارة:', selectedContacts.length);

    // تحديث checkbox المقابل
    const checkbox = document.querySelector(`input[data-contact-id="${contact.id}"]`);
    if (checkbox) {
        checkbox.checked = !isSelected;
    }

    // تحديث العرض
    updateContactItemSelection(contact.id, !isSelected);
    renderSelectedContacts();
    updateSelectionCount();
}

// تحديث مظهر عنصر جهة الاتصال
function updateContactItemSelection(contactId, isSelected) {
    const contactItems = document.querySelectorAll('.contact-item');
    contactItems.forEach(item => {
        const checkbox = item.querySelector(`input[data-contact-id="${contactId}"]`);
        if (checkbox) {
            if (isSelected) {
                item.classList.add('selected');
            } else {
                item.classList.remove('selected');
            }
        }
    });
}

// عرض جهات الاتصال المختارة
function renderSelectedContacts() {
    const container = document.getElementById('selectedContacts');

    if (!container) {
        console.warn('لم يتم العثور على container جهات الاتصال المختارة');
        return;
    }

    if (selectedContacts.length === 0) {
        container.innerHTML = '<p class="text-muted mb-0" id="noSelectionMessage">لم يتم اختيار أي جهة اتصال بعد</p>';
        return;
    }

    container.innerHTML = '';

    selectedContacts.forEach(contact => {
        const tag = document.createElement('span');
        tag.className = 'selected-contact-tag';
        tag.innerHTML = `
            ${contact.name}
            ${contact.is_vip ? '<i class="fas fa-star ms-1"></i>' : ''}
            <span class="remove-btn" onclick="removeSelectedContact(${contact.id})">
                <i class="fas fa-times"></i>
            </span>
        `;
        container.appendChild(tag);
    });
}

// إزالة جهة اتصال من المختارة
function removeSelectedContact(contactId) {
    console.log('إزالة جهة اتصال:', contactId);

    selectedContacts = selectedContacts.filter(sc => sc.id !== contactId);

    // تحديث checkbox المقابل
    const checkbox = document.querySelector(`input[data-contact-id="${contactId}"]`);
    if (checkbox) {
        checkbox.checked = false;
    }

    // تحديث مظهر العنصر
    updateContactItemSelection(contactId, false);

    renderSelectedContacts();
    updateSelectionCount();
}

// تحديد جميع جهات الاتصال
function selectAllContacts() {
    console.log('تحديد جميع جهات الاتصال');
    selectedContacts = [...filteredContacts];

    // تحديث جميع checkboxes
    const checkboxes = document.querySelectorAll('.contact-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });

    // تحديث مظهر جميع العناصر
    const contactItems = document.querySelectorAll('.contact-item');
    contactItems.forEach(item => {
        item.classList.add('selected');
    });

    renderSelectedContacts();
    updateSelectionCount();
}

// إلغاء تحديد جميع جهات الاتصال
function clearAllContacts() {
    console.log('إلغاء تحديد جميع جهات الاتصال');
    selectedContacts = [];

    // إلغاء تحديد جميع checkboxes
    const checkboxes = document.querySelectorAll('.contact-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });

    // إزالة تحديد مظهر جميع العناصر
    const contactItems = document.querySelectorAll('.contact-item');
    contactItems.forEach(item => {
        item.classList.remove('selected');
    });

    renderSelectedContacts();
    updateSelectionCount();
}

// اختيار مجموعة سريعة
function selectGroup(groupId) {
    const group = quickGroups.find(g => g.id === groupId);
    if (!group) return;
    
    let groupContacts = [];
    
    switch(groupId) {
        case 'all_managers':
            groupContacts = allContacts.filter(c => c.type === 'MANAGER');
            break;
        case 'all_customers':
            groupContacts = allContacts.filter(c => c.type === 'CUSTOMER');
            break;
        case 'all_drivers':
            groupContacts = allContacts.filter(c => c.type === 'DRIVER');
            break;
        case 'all_agents':
            groupContacts = allContacts.filter(c => c.type === 'AGENT');
            break;
        case 'vip_contacts':
            groupContacts = allContacts.filter(c => c.is_vip);
            break;
        case 'high_priority':
            groupContacts = allContacts.filter(c => c.priority >= 8);
            break;
    }
    
    selectedContacts = groupContacts;
    renderContacts();
    renderSelectedContacts();
    updateSelectionCount();
}

// تحديث عدد جهات الاتصال
function updateContactsCount() {
    const countElement = document.getElementById('contactsCount');
    if (countElement) {
        countElement.textContent = filteredContacts.length;
    }
}

// تحديث عدد المختارة
function updateSelectionCount() {
    const count = selectedContacts.length;
    console.log('تحديث العدد:', count);

    const selectedCountElement = document.getElementById('selectedCount');
    const confirmCountElement = document.getElementById('confirmCount');
    const confirmButtonElement = document.getElementById('confirmButton');

    if (selectedCountElement) {
        selectedCountElement.textContent = count;
    }

    if (confirmCountElement) {
        confirmCountElement.textContent = count;
    }

    if (confirmButtonElement) {
        confirmButtonElement.disabled = count === 0;

        // تحديث نص الزر والمساعدة
        const saveButtonText = document.getElementById('saveButtonText');
        const saveHelpText = document.getElementById('saveHelpText');

        if (count === 0) {
            confirmButtonElement.className = 'btn btn-success btn-lg shadow';
            confirmButtonElement.title = 'اختر جهة اتصال واحدة على الأقل لتفعيل زر الحفظ';
            if (saveButtonText) {
                saveButtonText.innerHTML = '<i class="fas fa-save me-2"></i>حفظ الربط (0 جهة اتصال)';
            }
            if (saveHelpText) {
                saveHelpText.innerHTML = '<i class="fas fa-info-circle me-1"></i>اختر جهات الاتصال من القائمة أعلاه ثم اضغط "حفظ الربط"';
                saveHelpText.className = 'text-muted d-block text-center';
            }
        } else {
            confirmButtonElement.className = 'btn btn-success btn-lg shadow pulse-animation';
            confirmButtonElement.title = `انقر لحفظ الربط مع ${count} جهة اتصال`;
            if (saveButtonText) {
                saveButtonText.innerHTML = `<i class="fas fa-save me-2"></i>حفظ الربط (${count} جهة اتصال)`;
            }
            if (saveHelpText) {
                saveHelpText.innerHTML = `<i class="fas fa-check-circle me-1 text-success"></i>جاهز للحفظ! تم اختيار ${count} جهة اتصال`;
                saveHelpText.className = 'text-success d-block text-center fw-bold';
            }
        }

        console.log('زر الحفظ معطل:', count === 0);
    }
}

// تأكيد الاختيار
function confirmContactSelection() {
    console.log('تأكيد الاختيار، العدد:', selectedContacts.length);

    if (selectedContacts.length === 0) {
        alert('⚠️ يرجى اختيار جهة اتصال واحدة على الأقل لحفظ الربط');
        return;
    }

    console.log('جهات الاتصال المختارة:', selectedContacts.map(c => c.name));

    try {
        // إرجاع جهات الاتصال المختارة للدالة المستدعية
        if (window.onContactsSelected) {
            console.log('استدعاء دالة onContactsSelected');
            window.onContactsSelected([...selectedContacts]); // نسخة من المصفوفة

            // رسالة نجاح
            setTimeout(() => {
                alert(`✅ تم حفظ الربط بنجاح!\nتم ربط ${selectedContacts.length} جهة اتصال بالإشعار`);
            }, 100);
        } else {
            console.warn('دالة onContactsSelected غير موجودة');
            alert('❌ خطأ: لم يتم العثور على دالة الحفظ');
        }

        // إغلاق Modal بطرق متعددة للتأكد
        closeContactSelectorModal();

    } catch (error) {
        console.error('خطأ في تأكيد الاختيار:', error);
        // محاولة إغلاق Modal حتى لو حدث خطأ
        closeContactSelectorModal();
    }
}

// دالة إغلاق Modal بطرق متعددة
function closeContactSelectorModal() {
    console.log('محاولة إغلاق modal...');

    const modalElement = document.getElementById('contactSelectorModal');
    if (!modalElement) {
        console.warn('لم يتم العثور على modal element');
        return;
    }

    try {
        // الطريقة الأولى: استخدام Bootstrap Modal API
        const modalInstance = bootstrap.Modal.getInstance(modalElement);
        if (modalInstance) {
            console.log('إغلاق modal باستخدام instance موجود');
            modalInstance.hide();
        } else {
            console.log('إنشاء instance جديد وإغلاق modal');
            const newModal = new bootstrap.Modal(modalElement);
            newModal.hide();
        }

        // الطريقة الثانية: إزالة classes يدوياً (backup)
        setTimeout(() => {
            if (modalElement.classList.contains('show')) {
                console.log('إزالة classes يدوياً...');
                modalElement.classList.remove('show');
                modalElement.style.display = 'none';
                modalElement.setAttribute('aria-hidden', 'true');
                modalElement.removeAttribute('aria-modal');

                // إزالة backdrop
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }

                // إزالة modal-open من body
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';

                console.log('تم إغلاق modal يدوياً');
            }
        }, 500);

    } catch (error) {
        console.error('خطأ في إغلاق modal:', error);

        // الطريقة الثالثة: إغلاق قسري (emergency)
        forceCloseModal();
    }
}

// إغلاق قسري للModal
function forceCloseModal() {
    console.log('إغلاق قسري للmodal...');

    try {
        const modalElement = document.getElementById('contactSelectorModal');
        if (modalElement) {
            modalElement.classList.remove('show');
            modalElement.style.display = 'none';
            modalElement.setAttribute('aria-hidden', 'true');
            modalElement.removeAttribute('aria-modal');
        }

        // إزالة جميع backdrops
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());

        // تنظيف body
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';

        console.log('تم الإغلاق القسري');

    } catch (error) {
        console.error('فشل في الإغلاق القسري:', error);
    }
}

// فتح واجهة اختيار جهات الاتصال
function openContactSelector(callback) {
    console.log('فتح واجهة اختيار جهات الاتصال');

    try {
        // تنظيف أي modal مفتوح سابقاً
        cleanupPreviousModals();

        // تعيين callback
        window.onContactsSelected = callback;

        const modalElement = document.getElementById('contactSelectorModal');
        if (!modalElement) {
            console.error('لم يتم العثور على modal جهات الاتصال');
            return;
        }

        // إنشاء modal instance جديد
        const modal = new bootstrap.Modal(modalElement, {
            backdrop: 'static', // منع الإغلاق بالنقر خارج Modal
            keyboard: true      // السماح بالإغلاق بـ ESC
        });

        // إضافة event listeners للModal
        modalElement.addEventListener('shown.bs.modal', function() {
            console.log('تم فتح modal بنجاح');
            // تهيئة البيانات بعد فتح Modal
            setTimeout(() => {
                initContactSelector();
            }, 100);
        }, { once: true });

        modalElement.addEventListener('hidden.bs.modal', function() {
            console.log('تم إغلاق modal');
            // تنظيف البيانات
            cleanupModalData();
        }, { once: true });

        // فتح Modal
        modal.show();

    } catch (error) {
        console.error('خطأ في فتح modal:', error);
    }
}

// تنظيف أي modals مفتوحة سابقاً
function cleanupPreviousModals() {
    console.log('تنظيف modals سابقة...');

    // إزالة جميع backdrops
    const backdrops = document.querySelectorAll('.modal-backdrop');
    backdrops.forEach(backdrop => backdrop.remove());

    // تنظيف body
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';

    // إغلاق أي modal instances موجودة
    const modalElement = document.getElementById('contactSelectorModal');
    if (modalElement) {
        const existingInstance = bootstrap.Modal.getInstance(modalElement);
        if (existingInstance) {
            existingInstance.dispose();
        }
    }
}

// تنظيف بيانات Modal
function cleanupModalData() {
    console.log('تنظيف بيانات modal...');

    // إعادة تعيين المتغيرات
    selectedContacts = [];
    allContacts = [];
    filteredContacts = [];

    // تنظيف callback
    window.onContactsSelected = null;
}

// تنظيف شامل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تنظيف شامل للmodals عند تحميل الصفحة');
    cleanupPreviousModals();
});

// تنظيف عند إغلاق الصفحة
window.addEventListener('beforeunload', function() {
    cleanupPreviousModals();
});

// إضافة دالة عامة للوصول من خارج الملف
window.forceCloseContactModal = forceCloseModal;
window.cleanupContactModals = cleanupPreviousModals;
</script>
