#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الاختبار النهائي للأتمتة مع PDF
Final Automation Test with PDF
"""

import requests
import time

def final_test():
    """الاختبار النهائي"""
    print("🎯 الاختبار النهائي للأتمتة مع PDF")
    print("=" * 50)
    
    try:
        # تشغيل الأتمتة
        print("🚀 تشغيل الأتمتة...")
        response = requests.post('http://localhost:5000/api/automation/test')
        
        if response.status_code == 200:
            print("✅ تم تشغيل الأتمتة بنجاح")
            
            # انتظار للمعالجة
            print("⏳ انتظار المعالجة...")
            time.sleep(5)
            
            # فحص النتائج
            status_response = requests.get('http://localhost:5000/api/automation/status')
            if status_response.status_code == 200:
                data = status_response.json()
                stats = data.get('status', {}).get('statistics', {})
                
                processed = stats.get('total_processed', 0)
                success = stats.get('total_success', 0)
                errors = stats.get('total_errors', 0)
                
                print(f"\n📊 النتائج النهائية:")
                print(f"  📈 معالج: {processed}")
                print(f"  ✅ نجح: {success}")
                print(f"  ❌ أخطاء: {errors}")
                
                if success > 0:
                    print("\n🎉 ممتاز! تم إنشاء أوامر التسليم وإرسال الواتساب مع PDF!")
                    print("📱 تحقق من الواتساب - يجب أن تكون وصلت رسالة مع ملف PDF!")
                    return True
                elif processed > 0:
                    print("\n⚠️ تم إنشاء أوامر لكن فشل في الإرسال")
                    print("🔍 فحص السجلات للتفاصيل...")
                    return False
                else:
                    print("\n❌ لم يتم معالجة أي شحنات")
                    print("🔍 تأكد من وجود شحنات بحالة arrived_port")
                    return False
            else:
                print(f"❌ فشل في جلب الحالة: {status_response.status_code}")
                return False
        else:
            print(f"❌ فشل في تشغيل الأتمتة: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    success = final_test()
    
    if success:
        print("\n🎊 تهانينا! الأتمتة تعمل بشكل مثالي!")
        print("📱 الواتساب يُرسل مع ملفات PDF!")
        print("🤖 النظام جاهز للعمل التلقائي!")
    else:
        print("\n😔 لا تزال هناك مشكلة...")
        print("🔍 تحقق من السجلات لمعرفة السبب")
    
    print("\n🏁 انتهى الاختبار النهائي!")
