{% extends "base.html" %}

{% block title %}عرض العقد{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-file-contract text-primary"></i>
                        عرض العقد رقم {{ contract[0] }}
                    </h4>
                    <div>
                        <a href="{{ url_for('contracts.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> العودة للقائمة
                        </a>
                        {% if contract[13] == 1 %}
                            <button class="btn btn-warning" disabled title="لا يمكن تعديل العقد المستخدم">
                                <i class="fas fa-lock me-2"></i> تعديل (محظور)
                            </button>
                        {% else %}
                            <a href="{{ url_for('contracts.edit_contract', contract_id=contract[0]) }}" class="btn btn-warning">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                        {% endif %}
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- تحذير للعقود المستخدمة -->
                    {% if contract[13] == 1 %}
                    <div class="alert alert-warning mb-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle fa-2x me-3 text-warning"></i>
                            <div>
                                <h6 class="alert-heading mb-1">
                                    <i class="fas fa-lock me-2"></i>
                                    عقد مُستخدم - محمي من التعديل
                                </h6>
                                <p class="mb-0">
                                    هذا العقد مُستخدم حالياً في أوامر الشراء ولا يمكن تعديله.
                                    لتعديل العقد، يجب أولاً إلغاء ربطه بجميع أوامر الشراء المرتبطة به.
                                </p>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- التبويبات -->
                    <ul class="nav nav-tabs" id="contractTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="main-data-tab" data-bs-toggle="tab" data-bs-target="#main-data" type="button" role="tab" aria-controls="main-data" aria-selected="true">
                                <i class="fas fa-info-circle"></i> البيانات الرئيسية
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="documents-tab" data-bs-toggle="tab" data-bs-target="#documents" type="button" role="tab" aria-controls="documents" aria-selected="false">
                                <i class="fas fa-file-alt"></i> المستندات
                            </button>
                        </li>
                    </ul>

                    <!-- محتوى التبويبات -->
                    <div class="tab-content" id="contractTabsContent">
                        <!-- تبويب البيانات الرئيسية -->
                        <div class="tab-pane fade show active" id="main-data" role="tabpanel" aria-labelledby="main-data-tab">
                            <div class="mt-4">
                                <!-- معلومات العقد الأساسية -->
                                <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="info-label">رقم العقد:</label>
                                <span class="info-value">{{ contract[0] }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="info-label">رقم الفرع:</label>
                                <span class="info-value">{{ contract[1] }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="info-label">تاريخ العقد:</label>
                                <span class="info-value">{{ contract[2] }}</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="info-label">تاريخ البداية:</label>
                                <span class="info-value">{{ contract[3] }}</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="info-label">تاريخ النهاية:</label>
                                <span class="info-value">{{ contract[4] }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="info-label">كود المورد:</label>
                                <span class="info-value">{{ contract[5] }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="info-label">اسم المورد:</label>
                                <span class="info-value">{{ contract[6] }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-group">
                                <label class="info-label">العملة:</label>
                                <span class="info-value">{{ contract[7] }}</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-group">
                                <label class="info-label">سعر الصرف:</label>
                                <span class="info-value">{{ contract[8] }}</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-group">
                                <label class="info-label">مبلغ العقد:</label>
                                <span class="info-value text-success font-weight-bold">{{ "{:,.2f}".format(contract[10]) }} {{ contract[7] }}</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-group">
                                <label class="info-label">حالة الاستخدام:</label>
                                <span class="info-value">
                                    {% if contract[13] == 1 %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>
                                            مُستخدم
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-times-circle me-1"></i>
                                            غير مُستخدم
                                        </span>
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-group">
                                <label class="info-label">حالة العقد:</label>
                                <span class="info-value">
                                    {% set contract_status = contract[14] if contract|length > 14 else 'DRAFT' %}
                                    {% if contract_status == 'DRAFT' %}
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-edit me-1"></i>
                                            مسودة
                                        </span>
                                    {% elif contract_status == 'APPROVED' %}
                                        <span class="badge bg-primary">
                                            <i class="fas fa-check me-1"></i>
                                            معتمد
                                        </span>
                                    {% elif contract_status == 'PARTIALLY_EXECUTED' %}
                                        <span class="badge bg-info">
                                            <i class="fas fa-clock me-1"></i>
                                            منفذ جزئياً
                                        </span>
                                    {% elif contract_status == 'FULLY_EXECUTED' %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-double me-1"></i>
                                            منفذ كلياً
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-question me-1"></i>
                                            غير محدد
                                        </span>
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    {% if contract[9] %}
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="info-group">
                                <label class="info-label">الرقم المرجعي:</label>
                                <span class="info-value">{{ contract[9] }}</span>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if contract[11] %}
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="info-group">
                                <label class="info-label">الوصف:</label>
                                <span class="info-value">{{ contract[11] }}</span>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- تفاصيل العقد -->
                    <div class="row">
                        <div class="col-12">
                            <h5 class="mb-3">
                                <i class="fas fa-list text-info"></i>
                                تفاصيل الأصناف
                            </h5>
                            
                            {% if details %}
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>كود الصنف</th>
                                            <th>اسم الصنف</th>
                                            <th>الكمية الأصلية</th>
                                            <th>الكمية المنفذة</th>
                                            <th>الكمية المتبقية</th>
                                            <th>الكمية المجانية</th>
                                            <th>الوحدة</th>
                                            <th>سعر الوحدة</th>
                                            <th>نسبة الخصم</th>
                                            <th>ضريبة</th>
                                            <th>الإجمالي</th>
                                            <th>تاريخ الإنتاج</th>
                                            <th>تاريخ الانتهاء</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for detail in details %}
                                        <tr>
                                            <td>{{ detail[0] or '-' }}</td>
                                            <td>{{ detail[1] }}</td>
                                            <td class="text-primary font-weight-bold">{{ detail[2] }}</td>
                                            <td class="text-info">
                                                {{ detail[3] or 0 }}
                                                {% if detail[3] and detail[3] > 0 %}
                                                    <small class="text-muted">({{ "%.1f"|format((detail[3]/detail[2]*100) if detail[2] > 0 else 0) }}%)</small>
                                                {% endif %}
                                            </td>
                                            <td class="text-warning">
                                                {{ detail[4] or detail[2] }}
                                                {% if detail[4] != detail[2] %}
                                                    <small class="text-muted">({{ "%.1f"|format((detail[4]/detail[2]*100) if detail[2] > 0 else 100) }}%)</small>
                                                {% endif %}
                                            </td>
                                            <td>{{ detail[5] }}</td>
                                            <td>{{ detail[6] }}</td>
                                            <td>{{ "{:,.2f}".format(detail[7]) }}</td>
                                            <td>{{ detail[8] }}%</td>
                                            <td>{{ "{:,.2f}".format(detail[9]) }}</td>
                                            <td class="text-success font-weight-bold">{{ "{:,.2f}".format(detail[10]) }}</td>
                                            <td>{{ detail[11] or '-' }}</td>
                                            <td>{{ detail[12] or '-' }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                لا توجد تفاصيل أصناف لهذا العقد
                            </div>
                            {% endif %}
                        </div>
                    </div>
                            </div>
                        </div>

                        <!-- تبويب المستندات -->
                        <div class="tab-pane fade" id="documents" role="tabpanel" aria-labelledby="documents-tab">
                            <div class="mt-4">
                                <!-- القسم الأول: العقد المعتمد -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <div class="card border-primary">
                                            <div class="card-header bg-primary text-white">
                                                <h5 class="mb-0">
                                                    <i class="fas fa-file-contract"></i>
                                                    العقد المعتمد
                                                </h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6 mb-3">
                                                        <button type="button" class="btn btn-success btn-lg w-100" onclick="openAttachmentModal()">
                                                            <i class="fas fa-paperclip"></i>
                                                            إضافة مرفق
                                                        </button>
                                                    </div>
                                                    <div class="col-md-6 mb-3">
                                                        <button type="button" class="btn btn-info btn-lg w-100" onclick="openLinkModal()">
                                                            <i class="fas fa-link"></i>
                                                            إضافة رابط
                                                        </button>
                                                    </div>
                                                </div>

                                                <!-- قائمة المرفقات والروابط -->
                                                <div class="mt-4">
                                                    <h6 class="text-muted mb-3">
                                                        <i class="fas fa-list"></i>
                                                        المرفقات والروابط المضافة
                                                    </h6>

                                                    <div id="documentsContainer">
                                                        <!-- سيتم تحميل المستندات هنا -->
                                                        <div class="alert alert-light text-center">
                                                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                                                            <p class="text-muted">لا توجد مستندات مضافة بعد</p>
                                                            <small class="text-muted">استخدم الأزرار أعلاه لإضافة مرفقات أو روابط</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة مرفق -->
<div class="modal fade" id="attachmentModal" tabindex="-1" aria-labelledby="attachmentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="attachmentModalLabel">
                    <i class="fas fa-paperclip"></i>
                    إضافة مرفق
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="attachmentForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="attachmentTitle" class="form-label">عنوان المرفق</label>
                        <input type="text" class="form-control" id="attachmentTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="attachmentFile" class="form-label">اختر الملف</label>
                        <input type="file" class="form-control" id="attachmentFile" required>
                        <div class="form-text">الحد الأقصى: 10 ميجابايت. الأنواع المدعومة: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG</div>
                    </div>
                    <div class="mb-3">
                        <label for="attachmentDescription" class="form-label">وصف المرفق (اختياري)</label>
                        <textarea class="form-control" id="attachmentDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="uploadAttachment()">
                    <i class="fas fa-upload"></i>
                    رفع المرفق
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة رابط -->
<div class="modal fade" id="linkModal" tabindex="-1" aria-labelledby="linkModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="linkModalLabel">
                    <i class="fas fa-link"></i>
                    إضافة رابط
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="linkForm">
                    <div class="mb-3">
                        <label for="linkTitle" class="form-label">عنوان الرابط</label>
                        <input type="text" class="form-control" id="linkTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="linkUrl" class="form-label">الرابط (URL)</label>
                        <input type="url" class="form-control" id="linkUrl" required placeholder="https://example.com">
                    </div>
                    <div class="mb-3">
                        <label for="linkDescription" class="form-label">وصف الرابط (اختياري)</label>
                        <textarea class="form-control" id="linkDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-info" onclick="addLink()">
                    <i class="fas fa-plus"></i>
                    إضافة الرابط
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.info-group {
    margin-bottom: 15px;
}

.info-label {
    font-weight: bold;
    color: #495057;
    display: inline-block;
    min-width: 120px;
}

.info-value {
    color: #212529;
    margin-right: 10px;
}

.table th {
    background-color: #343a40;
    color: white;
    border: none;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.card-header h4 {
    color: white;
}

/* تنسيق التبويبات */
.nav-tabs .nav-link {
    color: #495057;
    border: 1px solid transparent;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    isolation: isolate;
}

.nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.tab-content {
    border: 1px solid #dee2e6;
    border-top: none;
    padding: 0;
}

/* تنسيق قسم المستندات */
.document-item {
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.document-item:hover {
    border-color: #007bff;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 123, 255, 0.075);
}

.document-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.document-actions {
    margin-top: 1rem;
}
</style>

<script>
// متغيرات عامة
const contractId = {{ contract[0] }};

// فتح نافذة إضافة مرفق
function openAttachmentModal() {
    $('#attachmentModal').modal('show');
    // إعادة تعيين النموذج
    document.getElementById('attachmentForm').reset();
}

// فتح نافذة إضافة رابط
function openLinkModal() {
    $('#linkModal').modal('show');
    // إعادة تعيين النموذج
    document.getElementById('linkForm').reset();
}

// رفع مرفق
function uploadAttachment() {
    const title = document.getElementById('attachmentTitle').value;
    const file = document.getElementById('attachmentFile').files[0];
    const description = document.getElementById('attachmentDescription').value;

    if (!title || !file) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // التحقق من حجم الملف (10 ميجابايت)
    if (file.size > 10 * 1024 * 1024) {
        alert('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت');
        return;
    }

    // التحقق من نوع الملف
    const allowedTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png'];
    const fileExtension = file.name.split('.').pop().toLowerCase();

    if (!allowedTypes.includes(fileExtension)) {
        alert('نوع الملف غير مدعوم. الأنواع المدعومة: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG');
        return;
    }

    // إنشاء FormData
    const formData = new FormData();
    formData.append('contract_id', contractId);
    formData.append('title', title);
    formData.append('file', file);
    formData.append('description', description);
    formData.append('type', 'attachment');

    // عرض مؤشر التحميل
    const uploadBtn = event.target;
    const originalText = uploadBtn.innerHTML;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الرفع...';
    uploadBtn.disabled = true;

    // إرسال الطلب
    fetch('/contracts/api/documents/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم رفع المرفق بنجاح');
            $('#attachmentModal').modal('hide');
            loadDocuments(); // إعادة تحميل قائمة المستندات
        } else {
            alert('خطأ في رفع المرفق: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('خطأ في رفع المرفق');
    })
    .finally(() => {
        // إعادة تعيين الزر
        uploadBtn.innerHTML = originalText;
        uploadBtn.disabled = false;
    });
}

// إضافة رابط
function addLink() {
    const title = document.getElementById('linkTitle').value;
    const url = document.getElementById('linkUrl').value;
    const description = document.getElementById('linkDescription').value;

    if (!title || !url) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // التحقق من صحة الرابط
    try {
        new URL(url);
    } catch {
        alert('الرابط غير صحيح');
        return;
    }

    // عرض مؤشر التحميل
    const addBtn = event.target;
    const originalText = addBtn.innerHTML;
    addBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإضافة...';
    addBtn.disabled = true;

    // إرسال الطلب
    fetch('/contracts/api/documents/add-link', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            contract_id: contractId,
            title: title,
            url: url,
            description: description,
            type: 'link'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إضافة الرابط بنجاح');
            $('#linkModal').modal('hide');
            loadDocuments(); // إعادة تحميل قائمة المستندات
        } else {
            alert('خطأ في إضافة الرابط: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('خطأ في إضافة الرابط');
    })
    .finally(() => {
        // إعادة تعيين الزر
        addBtn.innerHTML = originalText;
        addBtn.disabled = false;
    });
}

// تحميل قائمة المستندات
function loadDocuments() {
    fetch(`/contracts/api/documents/${contractId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayDocuments(data.documents);
        } else {
            console.error('خطأ في تحميل المستندات:', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// عرض قائمة المستندات
function displayDocuments(documents) {
    const container = document.getElementById('documentsContainer');

    if (documents.length === 0) {
        container.innerHTML = `
            <div class="alert alert-light text-center">
                <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                <p class="text-muted">لا توجد مستندات مضافة بعد</p>
                <small class="text-muted">استخدم الأزرار أعلاه لإضافة مرفقات أو روابط</small>
            </div>
        `;
        return;
    }

    let html = '';
    documents.forEach(doc => {
        const icon = doc.type === 'attachment' ? 'fas fa-paperclip' : 'fas fa-link';
        const color = doc.type === 'attachment' ? 'text-success' : 'text-info';

        html += `
            <div class="document-item">
                <div class="row align-items-center">
                    <div class="col-md-1 text-center">
                        <i class="${icon} document-icon ${color}"></i>
                    </div>
                    <div class="col-md-8">
                        <h6 class="mb-1">${doc.title}</h6>
                        ${doc.description ? `<p class="text-muted mb-1">${doc.description}</p>` : ''}
                        <small class="text-muted">
                            <i class="fas fa-calendar"></i> ${doc.created_at}
                        </small>
                    </div>
                    <div class="col-md-3 text-end">
                        <div class="document-actions">
                            ${doc.type === 'attachment' ?
                                `<a href="/contracts/download/${doc.file_path}" class="btn btn-sm btn-outline-primary" target="_blank">
                                    <i class="fas fa-eye"></i> عرض
                                </a>` :
                                `<a href="${doc.url}" class="btn btn-sm btn-outline-info" target="_blank">
                                    <i class="fas fa-external-link-alt"></i> فتح
                                </a>`
                            }
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteDocument(${doc.id})">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

// حذف مستند
function deleteDocument(documentId) {
    if (!confirm('هل أنت متأكد من حذف هذا المستند؟')) {
        return;
    }

    fetch(`/contracts/api/documents/delete/${documentId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم حذف المستند بنجاح');
            loadDocuments(); // إعادة تحميل قائمة المستندات
        } else {
            alert('خطأ في حذف المستند: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('خطأ في حذف المستند');
    });
}

// تحميل المستندات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadDocuments();
});
</script>
{% endblock %}
