# -*- coding: utf-8 -*-
"""
مسارات المصادقة والتفويض
Authentication and Authorization Routes
"""

from flask import render_template, request, redirect, url_for, flash, session
from flask_login import login_user, logout_user, current_user, login_required
try:
    from werkzeug.urls import url_parse
except ImportError:
    from urllib.parse import urlparse as url_parse
from app.auth import bp
from app import db
from app.models import User
from app.auth.forms import LoginForm, RegistrationForm, ChangePasswordForm
from datetime import datetime
from database_manager import DatabaseManager

@bp.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))

    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '').strip()

        # دخول مباشر للمدير
        if username == 'admin' and password == 'admin':
            # إنشاء مستخدم admin مؤقت للاختبار
            from flask_login import UserMixin

            class SimpleUser(UserMixin):
                def __init__(self, user_id, username, email, full_name, is_admin):
                    self.id = str(user_id)
                    self.username = username
                    self.email = email
                    self.full_name = full_name
                    self.is_admin = is_admin

                def get_id(self):
                    return self.id

            # إنشاء مستخدم admin مؤقت
            admin_user = SimpleUser(1, 'admin', '<EMAIL>', 'مدير النظام', True)
            login_user(admin_user)
            flash('مرحباً مدير النظام', 'success')
            return redirect(url_for('main.dashboard'))

        # محاولة دخول عادية لأي مستخدم
        db_manager = DatabaseManager()
        try:
            # أولاً: البحث في جدول المستخدمين العاديين
            query = f"SELECT id, username, email, full_name, is_admin FROM users WHERE username = '{username}'"
            result = db_manager.execute_query(query)

            if result:
                # إنشاء user object
                from flask_login import UserMixin

                class SimpleUser(UserMixin):
                    def __init__(self, user_data):
                        self.id = str(user_data[0])
                        self.username = user_data[1]
                        self.email = user_data[2]
                        self.full_name = user_data[3]
                        self.is_admin = bool(int(user_data[4]))
                        self.user_type = 'user'

                    def get_id(self):
                        return self.id

                user = SimpleUser(result[0])
                login_user(user)
                flash(f'مرحباً {user.full_name}', 'success')
                return redirect(url_for('main.dashboard'))

            # ثانياً: البحث في جدول المخلصين
            agent_query = f"SELECT id, agent_code, agent_name, email, phone FROM customs_agents WHERE agent_code = '{username}' AND is_active = 1"
            agent_result = db_manager.execute_query(agent_query)

            if agent_result:
                # إنشاء agent user object
                from flask_login import UserMixin

                class AgentUser(UserMixin):
                    def __init__(self, agent_data):
                        self.id = f"agent_{agent_data[0]}"  # إضافة بادئة للتمييز
                        self.agent_id = agent_data[0]
                        self.username = agent_data[1]  # agent_code
                        self.agent_code = agent_data[1]
                        self.full_name = agent_data[2]  # agent_name
                        self.email = agent_data[3] if agent_data[3] else ''
                        self.phone = agent_data[4] if agent_data[4] else ''
                        self.is_admin = False
                        self.user_type = 'agent'

                    def get_id(self):
                        return self.id

                agent_user = AgentUser(agent_result[0])
                login_user(agent_user)
                flash(f'مرحباً {agent_user.full_name} - المخلص', 'success')
                return redirect(url_for('agent_portal.dashboard'))

            # إذا لم يتم العثور على المستخدم في أي من الجدولين
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
        except Exception as e:
            flash(f'خطأ في الاتصال بقاعدة البيانات: {e}', 'error')

        flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template('auth/login.html', title='تسجيل الدخول')

@bp.route('/agent-login', methods=['GET', 'POST'])
def agent_login():
    """تسجيل دخول المخلصين"""
    if current_user.is_authenticated:
        if hasattr(current_user, 'user_type') and current_user.user_type == 'agent':
            return redirect(url_for('agent_portal.dashboard'))
        else:
            return redirect(url_for('main.dashboard'))

    if request.method == 'POST':
        agent_code = request.form.get('agent_code', '').strip()
        password = request.form.get('password', '').strip()

        # للتجربة: أي مخلص يمكنه الدخول بكلمة مرور "agent"
        if password == 'agent':
            db_manager = DatabaseManager()
            try:
                agent_query = f"SELECT id, agent_code, agent_name, email, phone FROM customs_agents WHERE agent_code = '{agent_code}' AND is_active = 1"
                agent_result = db_manager.execute_query(agent_query)

                if agent_result:
                    from flask_login import UserMixin

                    class AgentUser(UserMixin):
                        def __init__(self, agent_data):
                            self.id = f"agent_{agent_data[0]}"
                            self.agent_id = agent_data[0]
                            self.username = agent_data[1]
                            self.agent_code = agent_data[1]
                            self.full_name = agent_data[2]
                            self.email = agent_data[3] if agent_data[3] else ''
                            self.phone = agent_data[4] if agent_data[4] else ''
                            self.is_admin = False
                            self.user_type = 'agent'

                        def get_id(self):
                            return self.id

                    agent_user = AgentUser(agent_result[0])
                    login_user(agent_user)

                    # إضافة agent_id إلى الجلسة
                    session['agent_id'] = agent_user.agent_id
                    session['user_type'] = 'agent'

                    flash(f'مرحباً {agent_user.full_name} - المخلص', 'success')
                    return redirect(url_for('agent_portal.dashboard'))
                else:
                    flash('رمز المخلص غير صحيح أو غير نشط', 'error')
            except Exception as e:
                flash(f'خطأ في الاتصال بقاعدة البيانات: {e}', 'error')
        else:
            flash('كلمة المرور غير صحيحة', 'error')

    return render_template('auth/agent_login.html', title='تسجيل دخول المخلصين')

@bp.route('/logout')
def logout():
    """تسجيل الخروج"""
    logout_user()

    # تنظيف الجلسة
    session.pop('agent_id', None)
    session.pop('user_type', None)

    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('main.index'))

@bp.route('/register', methods=['GET', 'POST'])
def register():
    """تسجيل مستخدم جديد"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            full_name=form.full_name.data,
            department=form.department.data,
            position=form.position.data,
            phone=form.phone.data
        )
        user.set_password(form.password.data)
        
        db.session.add(user)
        db.session.commit()
        
        flash('تم إنشاء الحساب بنجاح. يمكنك الآن تسجيل الدخول', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/register.html', title='إنشاء حساب جديد', form=form)

@bp.route('/change_password', methods=['GET', 'POST'])
def change_password():
    """تغيير كلمة المرور"""
    form = ChangePasswordForm()
    if form.validate_on_submit():
        if not current_user.check_password(form.old_password.data):
            flash('كلمة المرور الحالية غير صحيحة', 'error')
            return redirect(url_for('auth.change_password'))
        
        current_user.set_password(form.new_password.data)
        db.session.commit()
        
        flash('تم تغيير كلمة المرور بنجاح', 'success')
        return redirect(url_for('main.dashboard'))
    
    return render_template('auth/change_password.html', title='تغيير كلمة المرور', form=form)

@bp.route('/profile')
def profile():
    """الملف الشخصي"""
    return render_template('auth/profile.html', title='الملف الشخصي', user=current_user)

@bp.route('/users')
def users():
    """قائمة المستخدمين - للمديرين فقط"""
    if not current_user.is_admin:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('main.dashboard'))
    
    # استخدام Oracle JDBC لجلب المستخدمين
    db_manager = DatabaseManager()
    try:
        query = "SELECT id, username, email, full_name, is_admin, created_at FROM users ORDER BY created_at DESC"
        users_data = db_manager.execute_query(query)

        # تحويل البيانات إلى قائمة
        users = []
        for user_data in users_data:
            user_dict = {
                'id': user_data[0],
                'username': user_data[1],
                'email': user_data[2],
                'full_name': user_data[3],
                'is_admin': bool(int(user_data[4])),
                'created_at': user_data[5]
            }
            users.append(user_dict)

        return render_template('auth/users.html', title='إدارة المستخدمين', users=users)
    except Exception as e:
        flash(f'خطأ في جلب بيانات المستخدمين: {e}', 'error')
        return redirect(url_for('main.dashboard'))

@bp.route('/users/<int:user_id>/toggle_status')
def toggle_user_status(user_id):
    """تفعيل/إلغاء تفعيل المستخدم"""
    if not current_user.is_admin:
        flash('ليس لديك صلاحية لتنفيذ هذا الإجراء', 'error')
        return redirect(url_for('main.dashboard'))
    
    # استخدام Oracle JDBC
    db_manager = DatabaseManager()
    try:
        # جلب بيانات المستخدم
        query = f"SELECT id, username, full_name, is_active FROM users WHERE id = {user_id}"
        result = db_manager.execute_query(query)

        if not result:
            flash('المستخدم غير موجود', 'error')
            return redirect(url_for('auth.users'))

        user_data = result[0]
        if user_data[0] == int(current_user.id):
            flash('لا يمكنك تعديل حالة حسابك الخاص', 'error')
            return redirect(url_for('auth.users'))

        # تبديل حالة التفعيل
        new_status = 0 if user_data[3] else 1
        update_query = f"UPDATE users SET is_active = {new_status} WHERE id = {user_id}"
        db_manager.execute_update(update_query)

        status = 'تم تفعيل' if new_status else 'تم إلغاء تفعيل'
        flash(f'{status} المستخدم {user_data[2]}', 'success')
        return redirect(url_for('auth.users'))
    except Exception as e:
        flash(f'خطأ في تحديث المستخدم: {e}', 'error')
        return redirect(url_for('auth.users'))

@bp.route('/users/<int:user_id>/toggle_admin')
def toggle_admin_status(user_id):
    """منح/إلغاء صلاحيات المدير"""
    if not current_user.is_admin:
        flash('ليس لديك صلاحية لتنفيذ هذا الإجراء', 'error')
        return redirect(url_for('main.dashboard'))
    
    # استخدام Oracle JDBC
    db_manager = DatabaseManager()
    try:
        # جلب بيانات المستخدم
        query = f"SELECT id, username, full_name, is_admin FROM users WHERE id = {user_id}"
        result = db_manager.execute_query(query)

        if not result:
            flash('المستخدم غير موجود', 'error')
            return redirect(url_for('auth.users'))

        user_data = result[0]
        if user_data[0] == int(current_user.id):
            flash('لا يمكنك تعديل صلاحياتك الخاصة', 'error')
            return redirect(url_for('auth.users'))

        # تبديل صلاحيات المدير
        new_admin_status = 0 if user_data[3] else 1
        update_query = f"UPDATE users SET is_admin = {new_admin_status} WHERE id = {user_id}"
        db_manager.execute_update(update_query)

        status = 'تم منح' if new_admin_status else 'تم إلغاء'
        flash(f'{status} صلاحيات المدير للمستخدم {user_data[2]}', 'success')
        return redirect(url_for('auth.users'))
    except Exception as e:
        flash(f'خطأ في تحديث صلاحيات المستخدم: {e}', 'error')
        return redirect(url_for('auth.users'))
