-- =====================================================
-- اختبار دالة إلغاء الحوالة
-- Test Cancel Transfer Function
-- =====================================================

SET SERVEROUTPUT ON;

-- 1. إنشاء بيانات اختبار
INSERT INTO transfers (
    id, transfer_number, money_changer_bank_id, net_amount_sent, 
    status, execution_date, created_at, updated_at
) VALUES (
    99999, 'TEST-CANCEL-99999', 1, 10000, 
    'executed', SYSDATE - 1, SYSDATE, SYSDATE
);

-- إنشاء توزيع اختبار
INSERT INTO transfer_supplier_dist (
    id, transfer_id, supplier_id, amount, currency_code, created_by
) VALUES (
    99999, 99999, 1, 10000, 'SAR', 1
);

COMMIT;

-- 2. اختبار دالة التحقق من إمكانية الإلغاء
DECLARE
    v_result VARCHAR2(4000);
BEGIN
    DBMS_OUTPUT.PUT_LINE('=== اختبار دالة CAN_CANCEL_TRANSFER ===');
    
    -- اختبار حوالة موجودة ومنفذة
    v_result := CAN_CANCEL_TRANSFER(99999);
    DBMS_OUTPUT.PUT_LINE('نتيجة الاختبار للحوالة 99999: ' || v_result);
    
    -- اختبار حوالة غير موجودة
    v_result := CAN_CANCEL_TRANSFER(88888);
    DBMS_OUTPUT.PUT_LINE('نتيجة الاختبار للحوالة غير موجودة: ' || v_result);
    
END;
/

-- 3. اختبار الدوال الأخرى
DECLARE
    v_result VARCHAR2(4000);
BEGIN
    DBMS_OUTPUT.PUT_LINE('=== اختبار الدوال الأخرى ===');
    
    -- اختبار GET_TRANSFER_INFO
    v_result := GET_TRANSFER_INFO(99999);
    DBMS_OUTPUT.PUT_LINE('معلومات الحوالة: ' || v_result);
    
    -- اختبار CHECK_MC_BALANCE
    v_result := CHECK_MC_BALANCE(1, 5000, 'SAR');
    DBMS_OUTPUT.PUT_LINE('رصيد الصراف: ' || v_result);
    
    -- اختبار GET_BALANCE_SUMMARY
    v_result := GET_BALANCE_SUMMARY('MONEY_CHANGER', 'SAR');
    DBMS_OUTPUT.PUT_LINE('ملخص الأرصدة: ' || v_result);
    
END;
/

-- 4. تنظيف بيانات الاختبار
DELETE FROM transfer_supplier_dist WHERE id = 99999;
DELETE FROM transfers WHERE id = 99999;
COMMIT;

DBMS_OUTPUT.PUT_LINE('=== انتهى الاختبار ===');

SELECT 'Test completed successfully' as result FROM DUAL;
