# -*- coding: utf-8 -*-
"""
نماذج النظام المالي
Financial System Forms
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, DecimalField, DateField, SubmitField, BooleanField
from wtforms.validators import DataRequired, Length, NumberRange, Optional
from wtforms.widgets import TextArea
from app.models import Supplier, PurchaseOrder
from datetime import datetime

class InvoiceForm(FlaskForm):
    """نموذج إنشاء فاتورة"""
    
    # معلومات أساسية
    invoice_number = StringField(
        'رقم الفاتورة',
        validators=[DataRequired(), Length(min=1, max=50)],
        render_kw={'class': 'form-control', 'placeholder': 'رقم الفاتورة'}
    )
    
    supplier_id = SelectField(
        'المورد',
        coerce=int,
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    purchase_order_id = SelectField(
        'أمر الشراء',
        coerce=int,
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    invoice_date = DateField(
        'تاريخ الفاتورة',
        validators=[DataRequired()],
        default=datetime.utcnow,
        render_kw={'class': 'form-control'}
    )
    
    due_date = DateField(
        'تاريخ الاستحقاق',
        validators=[DataRequired()],
        render_kw={'class': 'form-control'}
    )
    
    # المبالغ
    subtotal = DecimalField(
        'المبلغ الفرعي',
        validators=[DataRequired(), NumberRange(min=0)],
        places=2,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0'}
    )
    
    tax_amount = DecimalField(
        'مبلغ الضريبة',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        default=0,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0'}
    )
    
    discount_amount = DecimalField(
        'مبلغ الخصم',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        default=0,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0'}
    )
    
    total_amount = DecimalField(
        'المبلغ الإجمالي',
        validators=[DataRequired(), NumberRange(min=0)],
        places=2,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0', 'readonly': True}
    )
    
    # معلومات إضافية
    currency = SelectField(
        'العملة',
        choices=[
            ('SAR', 'ريال سعودي'),
            ('USD', 'دولار أمريكي'),
            ('EUR', 'يورو'),
            ('GBP', 'جنيه إسترليني')
        ],
        default='SAR',
        render_kw={'class': 'form-select'}
    )
    
    status = SelectField(
        'حالة الفاتورة',
        choices=[
            ('draft', 'مسودة'),
            ('sent', 'مرسلة'),
            ('approved', 'معتمدة'),
            ('paid', 'مدفوعة'),
            ('cancelled', 'ملغية')
        ],
        default='draft',
        render_kw={'class': 'form-select'}
    )
    
    notes = TextAreaField(
        'ملاحظات',
        validators=[Optional(), Length(max=1000)],
        widget=TextArea(),
        render_kw={'class': 'form-control', 'rows': 4, 'placeholder': 'ملاحظات إضافية...'}
    )
    
    submit = SubmitField('حفظ الفاتورة', render_kw={'class': 'btn btn-primary'})
    
    def __init__(self, *args, **kwargs):
        super(InvoiceForm, self).__init__(*args, **kwargs)
        
        # تحديث خيارات الموردين
        try:
            self.supplier_id.choices = [
                (s.id, s.name_ar)
                for s in Supplier.query.filter_by(is_active=True).all()
            ]
        except:
            self.supplier_id.choices = []
        
        # تحديث خيارات أوامر الشراء
        self.purchase_order_id.choices = [('', 'بدون أمر شراء')] + [
            (po.id, f"{po.order_number} - {po.supplier.name_ar if po.supplier else 'غير محدد'}")
            for po in PurchaseOrder.query.filter(
                PurchaseOrder.status.in_(['confirmed', 'in_progress', 'delivered'])
            ).all()
        ]

class PaymentForm(FlaskForm):
    """نموذج تسجيل دفعة"""
    
    # معلومات أساسية
    payment_number = StringField(
        'رقم الدفعة',
        validators=[DataRequired(), Length(min=1, max=50)],
        render_kw={'class': 'form-control', 'placeholder': 'رقم الدفعة'}
    )
    
    invoice_id = SelectField(
        'الفاتورة',
        coerce=int,
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    payment_date = DateField(
        'تاريخ الدفع',
        validators=[DataRequired()],
        default=datetime.utcnow,
        render_kw={'class': 'form-control'}
    )
    
    amount = DecimalField(
        'المبلغ المدفوع',
        validators=[DataRequired(), NumberRange(min=0.01)],
        places=2,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0.01'}
    )
    
    payment_method = SelectField(
        'طريقة الدفع',
        choices=[
            ('cash', 'نقداً'),
            ('bank_transfer', 'تحويل بنكي'),
            ('check', 'شيك'),
            ('credit_card', 'بطاقة ائتمان'),
            ('online', 'دفع إلكتروني')
        ],
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    reference_number = StringField(
        'رقم المرجع',
        validators=[Optional(), Length(max=100)],
        render_kw={'class': 'form-control', 'placeholder': 'رقم الشيك أو التحويل'}
    )
    
    bank_name = StringField(
        'اسم البنك',
        validators=[Optional(), Length(max=100)],
        render_kw={'class': 'form-control', 'placeholder': 'اسم البنك'}
    )
    
    notes = TextAreaField(
        'ملاحظات',
        validators=[Optional(), Length(max=500)],
        render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات حول الدفعة...'}
    )
    
    submit = SubmitField('تسجيل الدفعة', render_kw={'class': 'btn btn-primary'})
    
    def __init__(self, *args, **kwargs):
        super(PaymentForm, self).__init__(*args, **kwargs)
        
        # تحديث خيارات الفواتير غير المدفوعة بالكامل
        from app.models import Invoice
        self.invoice_id.choices = [
            (inv.id, f"{inv.invoice_number} - {inv.supplier.name_ar if inv.supplier else 'غير محدد'} ({inv.remaining_amount:.2f} ر.س)")
            for inv in Invoice.query.filter(
                Invoice.status.in_(['sent', 'approved']),
                Invoice.remaining_amount > 0
            ).all()
        ]

class BudgetForm(FlaskForm):
    """نموذج الميزانية"""
    
    name = StringField(
        'اسم الميزانية',
        validators=[DataRequired(), Length(min=1, max=200)],
        render_kw={'class': 'form-control', 'placeholder': 'اسم الميزانية'}
    )
    
    description = TextAreaField(
        'الوصف',
        validators=[Optional(), Length(max=500)],
        render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف الميزانية...'}
    )
    
    budget_type = SelectField(
        'نوع الميزانية',
        choices=[
            ('annual', 'سنوية'),
            ('quarterly', 'ربع سنوية'),
            ('monthly', 'شهرية'),
            ('project', 'مشروع')
        ],
        default='annual',
        render_kw={'class': 'form-select'}
    )
    
    start_date = DateField(
        'تاريخ البداية',
        validators=[DataRequired()],
        render_kw={'class': 'form-control'}
    )
    
    end_date = DateField(
        'تاريخ النهاية',
        validators=[DataRequired()],
        render_kw={'class': 'form-control'}
    )
    
    total_budget = DecimalField(
        'إجمالي الميزانية',
        validators=[DataRequired(), NumberRange(min=0)],
        places=2,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0'}
    )
    
    currency = SelectField(
        'العملة',
        choices=[
            ('SAR', 'ريال سعودي'),
            ('USD', 'دولار أمريكي'),
            ('EUR', 'يورو'),
            ('GBP', 'جنيه إسترليني')
        ],
        default='SAR',
        render_kw={'class': 'form-select'}
    )
    
    status = SelectField(
        'الحالة',
        choices=[
            ('draft', 'مسودة'),
            ('active', 'نشطة'),
            ('completed', 'مكتملة'),
            ('cancelled', 'ملغية')
        ],
        default='draft',
        render_kw={'class': 'form-select'}
    )
    
    notes = TextAreaField(
        'ملاحظات',
        validators=[Optional(), Length(max=1000)],
        render_kw={'class': 'form-control', 'rows': 4, 'placeholder': 'ملاحظات إضافية...'}
    )
    
    submit = SubmitField('حفظ الميزانية', render_kw={'class': 'btn btn-primary'})

class ExpenseForm(FlaskForm):
    """نموذج المصروفات"""
    
    expense_number = StringField(
        'رقم المصروف',
        validators=[DataRequired(), Length(min=1, max=50)],
        render_kw={'class': 'form-control', 'placeholder': 'رقم المصروف'}
    )
    
    description = StringField(
        'الوصف',
        validators=[DataRequired(), Length(min=1, max=200)],
        render_kw={'class': 'form-control', 'placeholder': 'وصف المصروف'}
    )
    
    category = SelectField(
        'الفئة',
        choices=[
            ('office_supplies', 'مستلزمات مكتبية'),
            ('equipment', 'معدات'),
            ('maintenance', 'صيانة'),
            ('utilities', 'مرافق'),
            ('travel', 'سفر'),
            ('training', 'تدريب'),
            ('consulting', 'استشارات'),
            ('other', 'أخرى')
        ],
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    amount = DecimalField(
        'المبلغ',
        validators=[DataRequired(), NumberRange(min=0.01)],
        places=2,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0.01'}
    )
    
    expense_date = DateField(
        'تاريخ المصروف',
        validators=[DataRequired()],
        default=datetime.utcnow,
        render_kw={'class': 'form-control'}
    )
    
    supplier_id = SelectField(
        'المورد',
        coerce=int,
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    payment_method = SelectField(
        'طريقة الدفع',
        choices=[
            ('cash', 'نقداً'),
            ('bank_transfer', 'تحويل بنكي'),
            ('check', 'شيك'),
            ('credit_card', 'بطاقة ائتمان'),
            ('petty_cash', 'نثريات')
        ],
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    receipt_number = StringField(
        'رقم الإيصال',
        validators=[Optional(), Length(max=50)],
        render_kw={'class': 'form-control', 'placeholder': 'رقم الإيصال أو الفاتورة'}
    )
    
    notes = TextAreaField(
        'ملاحظات',
        validators=[Optional(), Length(max=500)],
        render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات إضافية...'}
    )
    
    submit = SubmitField('تسجيل المصروف', render_kw={'class': 'btn btn-primary'})
    
    def __init__(self, *args, **kwargs):
        super(ExpenseForm, self).__init__(*args, **kwargs)
        
        # تحديث خيارات الموردين
        try:
            self.supplier_id.choices = [('', 'بدون مورد')] + [
                (s.id, s.name_ar)
                for s in Supplier.query.filter_by(is_active=True).all()
            ]
        except:
            self.supplier_id.choices = [('', 'بدون مورد')]

class FinancialSearchForm(FlaskForm):
    """نموذج البحث المالي"""
    
    search_term = StringField(
        'البحث',
        validators=[Optional(), Length(max=100)],
        render_kw={'class': 'form-control', 'placeholder': 'رقم الفاتورة، المورد، أو الوصف...'}
    )
    
    transaction_type = SelectField(
        'نوع المعاملة',
        choices=[
            ('', 'جميع المعاملات'),
            ('invoice', 'فاتورة'),
            ('payment', 'دفعة'),
            ('expense', 'مصروف')
        ],
        default='',
        render_kw={'class': 'form-select'}
    )
    
    status = SelectField(
        'الحالة',
        choices=[
            ('', 'جميع الحالات'),
            ('draft', 'مسودة'),
            ('pending', 'في الانتظار'),
            ('approved', 'معتمد'),
            ('paid', 'مدفوع'),
            ('cancelled', 'ملغي')
        ],
        default='',
        render_kw={'class': 'form-select'}
    )
    
    date_from = DateField(
        'من تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    date_to = DateField(
        'إلى تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    submit = SubmitField('بحث', render_kw={'class': 'btn btn-outline-primary'})
