#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة وثائق شحنات الحاويات
Cargo Shipment Document Management System
"""

import os
import uuid
from datetime import datetime
from werkzeug.utils import secure_filename
from flask import current_app
from database_manager import DatabaseManager
import logging

logger = logging.getLogger(__name__)

class CargoDocumentManager:
    """مدير وثائق شحنات الحاويات"""
    
    # أنواع الوثائق المدعومة
    DOCUMENT_TYPES = {
        'bill_of_lading': 'بوليصة الشحن',
        'commercial_invoice': 'الفاتورة التجارية',
        'packing_list': 'قائمة التعبئة',
        'certificate_of_origin': 'شهادة المنشأ',
        'insurance_certificate': 'شهادة التأمين',
        'customs_declaration': 'إقرار جمركي',
        'customs_documents': 'مستندات الجمارك',
        'preliminary_documents': 'المستندات الأولية',
        'dn_invoice': 'الفاتورة (DN)',
        'delivery_order': 'أمر التسليم',
        'freight_invoice': 'فاتورة الشحن',
        'container_seal': 'ختم الحاوية',
        'inspection_certificate': 'شهادة الفحص',
        'other': 'أخرى'
    }
    
    # أنواع الملفات المسموحة
    ALLOWED_EXTENSIONS = {
        'pdf', 'doc', 'docx', 'xls', 'xlsx',
        'jpg', 'jpeg', 'png', 'gif', 'tiff',
        'txt', 'csv', 'zip', 'rar'
    }
    
    # الحد الأقصى لحجم الملف (10 MB)
    MAX_FILE_SIZE = 10 * 1024 * 1024
    
    def __init__(self):
        """تهيئة مدير الوثائق"""
        # استخدام مسار ثابت بدلاً من current_app
        base_path = os.path.dirname(os.path.dirname(__file__))
        self.upload_folder = os.path.join(base_path, 'static', 'uploads', 'cargo_documents')
        self._ensure_upload_folder()
    
    def _ensure_upload_folder(self):
        """التأكد من وجود مجلد الرفع"""
        if not os.path.exists(self.upload_folder):
            os.makedirs(self.upload_folder, exist_ok=True)
            logger.info(f"تم إنشاء مجلد الوثائق: {self.upload_folder}")
    
    def is_allowed_file(self, filename):
        """التحقق من نوع الملف المسموح"""
        return ('.' in filename and 
                filename.rsplit('.', 1)[1].lower() in self.ALLOWED_EXTENSIONS)
    
    def upload_document(self, cargo_shipment_id, document_type, file,
                       document_name=None, notes=None, uploaded_by=None):
        """رفع وثيقة جديدة"""
        try:
            # التحقق من الملف
            if not file or file.filename == '':
                raise ValueError("لم يتم اختيار ملف")
            
            if not self.is_allowed_file(file.filename):
                raise ValueError("نوع الملف غير مدعوم")
            
            # التحقق من حجم الملف
            file.seek(0, os.SEEK_END)
            file_size = file.tell()
            file.seek(0)
            
            if file_size > self.MAX_FILE_SIZE:
                raise ValueError("حجم الملف كبير جداً (الحد الأقصى 10 MB)")
            
            # إنشاء اسم ملف فريد
            original_filename = secure_filename(file.filename)
            file_extension = original_filename.rsplit('.', 1)[1].lower()
            unique_filename = f"{uuid.uuid4().hex}.{file_extension}"
            
            # مسار الحفظ
            file_path = os.path.join(self.upload_folder, unique_filename)
            
            # حفظ الملف
            file.save(file_path)
            logger.info(f"تم حفظ الملف: {file_path}")
            
            # حفظ معلومات الوثيقة في قاعدة البيانات
            db_manager = DatabaseManager()
            
            insert_sql = """
                INSERT INTO cargo_shipment_documents (
                    cargo_shipment_id, document_type, document_name,
                    file_name, file_path, file_size, mime_type,
                    uploaded_by, notes
                ) VALUES (
                    :cargo_shipment_id, :document_type, :document_name,
                    :file_name, :file_path, :file_size, :mime_type,
                    :uploaded_by, :notes
                )
            """
            
            params = {
                'cargo_shipment_id': cargo_shipment_id,
                'document_type': document_type,
                'document_name': document_name or original_filename,
                'file_name': original_filename,
                'file_path': file_path,
                'file_size': file_size,
                'mime_type': file.content_type,
                'uploaded_by': uploaded_by,
                'notes': notes
            }
            
            # تنفيذ الإدراج والحصول على معرف الوثيقة
            result = db_manager.execute_update(insert_sql, params)

            # الحصول على معرف الوثيقة المُدرجة
            document_id = None
            try:
                # استعلام للحصول على آخر وثيقة مُدرجة
                get_id_query = """
                    SELECT id FROM cargo_shipment_documents
                    WHERE cargo_shipment_id = :cargo_shipment_id
                    AND file_name = :file_name
                    ORDER BY id DESC
                    FETCH FIRST 1 ROWS ONLY
                """
                id_result = db_manager.execute_query(get_id_query, {
                    'cargo_shipment_id': cargo_shipment_id,
                    'file_name': unique_filename
                })

                if id_result:
                    document_id = id_result[0][0]

            except Exception as e:
                logger.warning(f"لم يتم الحصول على معرف الوثيقة: {e}")

            db_manager.close()

            logger.info(f"تم رفع وثيقة جديدة للشحنة {cargo_shipment_id}")

            return {
                'success': True,
                'message': 'تم رفع الوثيقة بنجاح',
                'document_id': document_id,
                'file_path': file_path,
                'filename': unique_filename,
                'original_name': original_filename,
                'size': file_size
            }
            
        except Exception as e:
            logger.error(f"خطأ في رفع الوثيقة: {e}")
            # حذف الملف في حالة الخطأ
            if 'file_path' in locals() and os.path.exists(file_path):
                os.remove(file_path)
            
            return {
                'success': False,
                'message': str(e)
            }
    
    def get_shipment_documents(self, cargo_shipment_id):
        """جلب جميع وثائق الشحنة"""
        try:
            db_manager = DatabaseManager()
            
            query = """
                SELECT id, document_type, document_name, file_name,
                       file_size, mime_type, uploaded_by, uploaded_at,
                       is_generated, notes, share_link, share_service,
                       share_created_at, nextcloud_share_link, nextcloud_service_info,
                       nextcloud_created_at, onedrive_share_link, onedrive_service_info,
                       onedrive_created_at
                FROM cargo_shipment_documents
                WHERE cargo_shipment_id = :cargo_shipment_id
                ORDER BY uploaded_at DESC
            """
            
            results = db_manager.execute_query(query, {'cargo_shipment_id': cargo_shipment_id})
            db_manager.close()
            
            documents = []
            for row in results or []:
                # معالجة آمنة للملاحظات (Oracle LOB)
                try:
                    notes = str(row[9]) if row[9] else None
                except:
                    notes = "ملاحظات متوفرة"

                # معالجة تواريخ الإنشاء
                share_created_at = row[12] if len(row) > 12 else None
                if share_created_at:
                    try:
                        if hasattr(share_created_at, 'strftime'):
                            share_created_at = share_created_at.strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            share_created_at = str(share_created_at)
                    except:
                        share_created_at = str(share_created_at)

                nextcloud_created_at = row[15] if len(row) > 15 else None
                if nextcloud_created_at:
                    try:
                        if hasattr(nextcloud_created_at, 'strftime'):
                            nextcloud_created_at = nextcloud_created_at.strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            nextcloud_created_at = str(nextcloud_created_at)
                    except:
                        nextcloud_created_at = str(nextcloud_created_at)

                onedrive_created_at = row[18] if len(row) > 18 else None
                if onedrive_created_at:
                    try:
                        if hasattr(onedrive_created_at, 'strftime'):
                            onedrive_created_at = onedrive_created_at.strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            onedrive_created_at = str(onedrive_created_at)
                    except:
                        onedrive_created_at = str(onedrive_created_at)

                doc = {
                    'id': row[0],
                    'document_type': row[1],
                    'document_type_name': self.DOCUMENT_TYPES.get(row[1], row[1]),
                    'document_name': row[2],
                    'file_name': row[3],
                    'file_size': row[4],
                    'file_size_mb': round(row[4] / (1024 * 1024), 2) if row[4] else 0,
                    'mime_type': row[5],
                    'uploaded_by': row[6],
                    'uploaded_at': row[7],
                    'is_generated': bool(row[8]),
                    'notes': notes,
                    # الروابط القديمة (للتوافق مع النسخة السابقة)
                    'share_link': row[10] if len(row) > 10 else None,
                    'share_service': row[11] if len(row) > 11 else None,
                    'share_created_at': share_created_at,
                    # روابط Nextcloud
                    'nextcloud_share_link': row[13] if len(row) > 13 else None,
                    'nextcloud_service_info': row[14] if len(row) > 14 else None,
                    'nextcloud_created_at': nextcloud_created_at,
                    # روابط OneDrive
                    'onedrive_share_link': row[16] if len(row) > 16 else None,
                    'onedrive_service_info': row[17] if len(row) > 17 else None,
                    'onedrive_created_at': onedrive_created_at,
                    # مؤشرات وجود الروابط
                    'has_nextcloud_link': bool(row[13] if len(row) > 13 else None),
                    'has_onedrive_link': bool(row[16] if len(row) > 16 else None),
                    'has_any_link': bool((row[13] if len(row) > 13 else None) or (row[16] if len(row) > 16 else None))
                }
                documents.append(doc)
            
            return documents
            
        except Exception as e:
            logger.error(f"خطأ في جلب وثائق الشحنة: {e}")
            return []
    
    def delete_document(self, document_id, user=None):
        """حذف وثيقة"""
        try:
            db_manager = DatabaseManager()
            
            # جلب معلومات الوثيقة
            query = "SELECT file_path FROM cargo_shipment_documents WHERE id = :id"
            result = db_manager.execute_query(query, {'id': document_id})
            
            if not result:
                return {'success': False, 'message': 'الوثيقة غير موجودة'}
            
            file_path = result[0][0]
            
            # حذف من قاعدة البيانات
            delete_sql = "DELETE FROM cargo_shipment_documents WHERE id = :id"
            db_manager.execute_update(delete_sql, {'id': document_id})
            
            # حذف الملف الفعلي
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"تم حذف الملف: {file_path}")
            
            db_manager.close()
            
            return {'success': True, 'message': 'تم حذف الوثيقة بنجاح'}
            
        except Exception as e:
            logger.error(f"خطأ في حذف الوثيقة: {e}")
            return {'success': False, 'message': str(e)}
    
    def get_document_stats(self, cargo_shipment_id):
        """إحصائيات وثائق الشحنة"""
        try:
            db_manager = DatabaseManager()
            
            query = """
                SELECT 
                    COUNT(*) as total_docs,
                    SUM(file_size) as total_size,
                    COUNT(CASE WHEN is_generated = 1 THEN 1 END) as generated_docs,
                    COUNT(CASE WHEN is_generated = 0 THEN 1 END) as uploaded_docs
                FROM cargo_shipment_documents
                WHERE cargo_shipment_id = :cargo_shipment_id
            """
            
            result = db_manager.execute_query(query, {'cargo_shipment_id': cargo_shipment_id})
            db_manager.close()
            
            if result:
                row = result[0]
                return {
                    'total_documents': row[0] or 0,
                    'total_size_mb': round((row[1] or 0) / (1024 * 1024), 2),
                    'generated_documents': row[2] or 0,
                    'uploaded_documents': row[3] or 0
                }
            
            return {
                'total_documents': 0,
                'total_size_mb': 0,
                'generated_documents': 0,
                'uploaded_documents': 0
            }
            
        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات الوثائق: {e}")
            return {
                'total_documents': 0,
                'total_size_mb': 0,
                'generated_documents': 0,
                'uploaded_documents': 0
            }

# مثيل عام للاستخدام
document_manager = CargoDocumentManager()
