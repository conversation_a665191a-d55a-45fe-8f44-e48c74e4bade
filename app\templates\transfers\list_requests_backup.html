{% extends "base.html" %}

{% block title %}قائمة طلبات الحوالات{% endblock %}

{% block extra_css %}
<style>
.status-badge {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-weight: 500;
}

.status-pending,
.badge.status-pending,
span.status-pending {
    background-color: #ffc107 !important;
    color: #000 !important;
    padding: 6px 10px !important;
    border-radius: 4px !important;
    font-size: 0.85em !important;
    font-weight: 500 !important;
    border: 1px solid #ffc107 !important;
    display: inline-block !important;
    min-width: 60px !important;
    text-align: center !important;
}
.status-approved,
.badge.status-approved,
span.status-approved {
    background-color: #28a745 !important;
    color: #fff !important;
    padding: 6px 10px !important;
    border-radius: 4px !important;
    font-size: 0.85em !important;
    font-weight: 500 !important;
    border: 1px solid #28a745 !important;
    display: inline-block !important;
    min-width: 60px !important;
    text-align: center !important;
}
.status-rejected,
.badge.status-rejected,
span.status-rejected {
    background-color: #dc3545 !important;
    color: #fff !important;
    padding: 6px 10px !important;
    border-radius: 4px !important;
    font-size: 0.85em !important;
    font-weight: 500 !important;
    border: 1px solid #dc3545 !important;
    display: inline-block !important;
    min-width: 60px !important;
    text-align: center !important;
}
.status-processing,
.badge.status-processing,
span.status-processing {
    background-color: #17a2b8 !important;
    color: #fff !important;
    padding: 6px 10px !important;
    border-radius: 4px !important;
    font-size: 0.85em !important;
    font-weight: 500 !important;
    border: 1px solid #17a2b8 !important;
    display: inline-block !important;
    min-width: 60px !important;
    text-align: center !important;
}
.status-completed,
.badge.status-completed,
span.status-completed {
    background-color: #6f42c1 !important;
    color: #fff !important;
    padding: 6px 10px !important;
    border-radius: 4px !important;
    font-size: 0.85em !important;
    font-weight: 500 !important;
    border: 1px solid #6f42c1 !important;
    display: inline-block !important;
    min-width: 60px !important;
    text-align: center !important;
}

.request-card {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.request-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.filter-section {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.amount-display {
    font-weight: 600;
    font-size: 1.1rem;
}

.request-meta {
    font-size: 0.875rem;
    color: #6c757d;
}

.action-buttons .btn {
    margin: 0 0.25rem;
}

/* تحسينات الجدول */
#requestsTable {
    font-size: 0.9rem;
}

#requestsTable th {
    background-color: #343a40 !important;
    color: white !important;
    font-weight: 600;
    border: none;
    padding: 1rem 0.75rem;
}

#requestsTable td {
    padding: 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #dee2e6;
}

#requestsTable tbody tr:hover {
    background-color: #f8f9fa;
}

.amount-display {
    font-family: 'Courier New', monospace;
    direction: ltr !important;
    text-align: left !important;
    display: inline-block !important;
}

.btn-group .btn {
    margin: 0 1px;
}

.table-responsive {
    border-radius: 0.375rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">
                <i class="fas fa-list-alt me-2 text-primary"></i>
                طلبات الحوالات
            </h2>
            <p class="text-muted mb-0">إدارة ومتابعة طلبات التحويلات المالية</p>
        </div>
        <div>
            <a href="{{ url_for('transfers.new_request') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>طلب حوالة جديد
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-section">
        <div class="row">
            <div class="col-md-3 mb-3">
                <label class="form-label">حالة الطلب</label>
                <select class="form-select" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="pending">معلق</option>
                    <option value="approved">معتمد</option>
                    <option value="rejected">مرفوض</option>
                    <option value="processing">قيد المعالجة</option>
                    <option value="completed">مكتمل</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">الفرع</label>
                <select class="form-select" id="branchFilter">
                    <option value="">جميع الفروع</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="dateFromFilter">
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="dateToFilter">
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 mb-3">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" id="searchFilter" placeholder="رقم الطلب، اسم المستفيد، أو رقم الحساب...">
            </div>
            <div class="col-md-6 mb-3 d-flex align-items-end">
                <button type="button" class="btn btn-primary me-2" onclick="applyFilters()">
                    <i class="fas fa-search me-2"></i>بحث
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                    <i class="fas fa-times me-2"></i>مسح
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4" id="statisticsRow">
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="pendingCount">0</h4>
                            <p class="mb-0">طلبات معلقة</p>
                        </div>
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="approvedCount">0</h4>
                            <p class="mb-0">طلبات معتمدة</p>
                        </div>
                        <i class="fas fa-check fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="completedCount">0</h4>
                            <p class="mb-0">طلبات مكتملة</p>
                        </div>
                        <i class="fas fa-check-double fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="totalAmount">0</h4>
                            <p class="mb-0">إجمالي المبالغ</p>
                        </div>
                        <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Requests Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-table me-2"></i>
                قائمة طلبات الحوالات
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="requestsTable">
                    <thead class="table-dark">
                        <tr>
                            <th width="10%">رقم الطلب</th>
                            <th width="18%">المستفيد</th>
                            <th width="12%">المبلغ</th>
                            <th width="8%">الحالة</th>
                            <th width="12%">الفرع</th>
                            <th width="15%">الصراف/البنك</th>
                            <th width="12%">تاريخ الإنشاء</th>
                            <th width="13%">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="requestsTableBody">
                        <!-- سيتم تحميل الطلبات هنا -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Loading -->
    <div class="text-center py-5" id="loadingIndicator">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-2 text-muted">جاري تحميل الطلبات...</p>
    </div>

    <!-- No Results -->
    <div class="text-center py-5" id="noResults" style="display: none;">
        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">لا توجد طلبات</h5>
        <p class="text-muted">لم يتم العثور على طلبات تطابق معايير البحث</p>
    </div>
</div>

<!-- Request Details Modal -->
<div class="modal fade" id="requestDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="requestDetailsContent">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="printCurrentRequest()">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentRequests = [];
let currentPage = 1;
const itemsPerPage = 12;

$(document).ready(function() {
    console.log('🚀 تهيئة صفحة طلبات الحوالات...');
    
    // تحميل البيانات الأولية
    loadBranches();
    loadRequests();
    
    // ربط أحداث البحث
    $('#searchFilter').on('input', debounce(applyFilters, 500));
});

// تحميل الفروع
function loadBranches() {
    $.get('/transfers/api/branches')
        .done(function(response) {
            if (response.success) {
                const branchSelect = $('#branchFilter');
                response.data.forEach(function(branch) {
                    branchSelect.append(`<option value="${branch.id}">${branch.name}</option>`);
                });
            }
        })
        .fail(function() {
            console.error('فشل في تحميل الفروع');
        });
}

// تحميل طلبات الحوالات
function loadRequests() {
    console.log('📋 تحميل طلبات الحوالات...');
    
    $('#loadingIndicator').show();
    $('#requestsList').empty();
    $('#noResults').hide();
    
    const filters = {
        status: $('#statusFilter').val(),
        branch_id: $('#branchFilter').val(),
        date_from: $('#dateFromFilter').val(),
        date_to: $('#dateToFilter').val(),
        search: $('#searchFilter').val()
    };
    
    $.get('/transfers/api/requests', filters)
        .done(function(response) {
            $('#loadingIndicator').hide();
            
            console.log('📥 استجابة API:', response);

            if (response.success) {
                currentRequests = response.data;
                console.log('📊 البيانات المستلمة:', currentRequests);
                displayRequests(currentRequests);
                updateStatistics(response.statistics || {});
                console.log(`✅ تم تحميل ${currentRequests.length} طلب`);
            } else {
                console.error('❌ فشل API:', response.message);
                showError('فشل في تحميل الطلبات: ' + response.message);
            }
        })
        .fail(function(xhr) {
            $('#loadingIndicator').hide();
            console.error('خطأ في تحميل الطلبات:', xhr);
            console.error('Response status:', xhr.status);
            console.error('Response text:', xhr.responseText);
            showError('حدث خطأ أثناء تحميل الطلبات: ' + (xhr.responseJSON?.message || xhr.statusText));
        });
}

// عرض الطلبات في الجدول
function displayRequests(requests) {
    console.log('📊 عرض الطلبات:', requests.length, 'طلب');
    console.log('📋 بيانات الطلبات:', requests);

    const tableBody = $('#requestsTableBody');
    tableBody.empty();

    if (requests.length === 0) {
        console.log('❌ لا توجد طلبات للعرض');
        $('#noResults').show();
        $('#requestsTable').closest('.card').hide();
        return;
    }

    console.log('✅ عرض', requests.length, 'طلب في الجدول');
    $('#noResults').hide();
    $('#requestsTable').closest('.card').show();

    requests.forEach(function(request, index) {
        console.log(`📝 إنشاء صف للطلب ${index + 1}:`, request.request_number);
        const requestRow = createRequestRow(request);
        tableBody.append(requestRow);
    });

    console.log('🎉 تم عرض جميع الطلبات بنجاح');
}

// إنشاء صف جدول للطلب
function createRequestRow(request) {
    console.log('🔍 إنشاء صف للطلب:', request.request_number, 'الحالة:', request.status);
    const statusClass = `status-${request.status}`;
    const statusText = getStatusText(request.status);
    console.log('📊 statusClass:', statusClass, 'statusText:', statusText);

    return $(`
        <tr>
            <td>
                <strong class="text-primary">${request.request_number}</strong>
            </td>
            <td>
                <div class="d-flex flex-column">
                    <span class="fw-bold">${request.beneficiary_name || 'غير محدد'}</span>
                    <small class="text-muted">${request.bank_name || ''}</small>
                </div>
            </td>
            <td>
                <span class="amount-display text-success fw-bold">
                    ${formatAmount(request.amount)} ${request.currency}
                </span>
            </td>
            <td>
                <span class="badge ${statusClass}" style="
                    ${request.status === 'pending' ? 'background-color: #ffc107 !important; color: #000 !important;' : ''}
                    ${request.status === 'approved' ? 'background-color: #28a745 !important; color: #fff !important;' : ''}
                    ${request.status === 'rejected' ? 'background-color: #dc3545 !important; color: #fff !important;' : ''}
                    ${request.status === 'processing' ? 'background-color: #17a2b8 !important; color: #fff !important;' : ''}
                    ${request.status === 'completed' ? 'background-color: #6f42c1 !important; color: #fff !important;' : ''}
                    padding: 6px 10px !important;
                    border-radius: 4px !important;
                    font-size: 0.85em !important;
                    font-weight: 500 !important;
                    display: inline-block !important;
                    min-width: 60px !important;
                    text-align: center !important;
                    border: none !important;
                ">${statusText || request.status || 'غير محدد'}</span>
            </td>
            <td>
                <small class="text-muted">${request.branch_name || 'غير محدد'}</small>
            </td>
            <td>
                <div class="d-flex flex-column">
                    <span class="fw-bold text-info">${request.money_changer_bank_name || 'غير محدد'}</span>
                    <small class="text-muted">
                        <i class="fas fa-${request.money_changer_bank_type === 'bank' ? 'university' : 'exchange-alt'} me-1"></i>
                        ${request.money_changer_bank_type === 'bank' ? 'بنك' : 'صراف'}
                    </small>
                </div>
            </td>
            <td>
                <div class="d-flex flex-column">
                    <span>${formatDate(request.created_at)}</span>
                    <small class="text-muted">${formatTime(request.created_at)}</small>
                </div>
            </td>
            <td>
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewRequestDetails(${request.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="manageDocuments(${request.id})" title="إدارة الوثائق">
                        <i class="fas fa-file-alt"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="editRequest(${request.id})" title="تعديل الطلب">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="printRequest(${request.id})" title="طباعة النموذج">
                        <i class="fas fa-print"></i>
                    </button>
                    ${getActionButtons(request)}
                </div>
            </td>
        </tr>
    `);
}

// الحصول على نص الحالة
function getStatusText(status) {
    console.log('🔍 getStatusText called with:', status);
    const statusMap = {
        'pending': 'معلق',
        'approved': 'معتمد',
        'rejected': 'مرفوض',
        'processing': 'قيد المعالجة',
        'completed': 'مكتمل'
    };
    const result = statusMap[status] || status || 'غير محدد';
    console.log('📊 getStatusText result:', result);
    return result;
}

// الحصول على أزرار الإجراءات
function getActionButtons(request) {
    let buttons = '';

    if (request.status === 'pending') {
        buttons += `
            <button class="btn btn-sm btn-success" onclick="approveRequest(${request.id})" title="اعتماد الطلب">
                <i class="fas fa-check"></i>
            </button>
            <button class="btn btn-sm btn-danger" onclick="rejectRequest(${request.id})" title="رفض الطلب">
                <i class="fas fa-times"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger" onclick="deleteRequest(${request.id})" title="حذف الطلب">
                <i class="fas fa-trash"></i>
            </button>
        `;
    } else if (request.status === 'approved') {
        buttons += `
            <button class="btn btn-sm btn-info" onclick="processRequest(${request.id})" title="معالجة الطلب">
                <i class="fas fa-cog"></i>
            </button>
        `;
    } else if (request.status === 'processing') {
        buttons += `
            <button class="btn btn-sm btn-warning" onclick="completeRequest(${request.id})" title="إكمال الطلب">
                <i class="fas fa-check-double"></i>
            </button>
        `;
    } else if (request.status === 'rejected') {
        // إضافة زر الحذف للطلبات المرفوضة
        buttons += `
            <button class="btn btn-sm btn-outline-danger" onclick="deleteRequest(${request.id})" title="حذف الطلب">
                <i class="fas fa-trash"></i>
            </button>
        `;
    }

    return buttons;
}

// حذف طلب
function deleteRequest(requestId) {
    if (confirm('⚠️ تحذير: هل أنت متأكد من حذف هذا الطلب نهائياً؟\n\nهذا الإجراء لا يمكن التراجع عنه وسيؤدي إلى حذف جميع البيانات المرتبطة بالطلب.')) {
        if (confirm('تأكيد نهائي: اضغط موافق لحذف الطلب نهائياً')) {
            console.log('🗑️ حذف الطلب:', requestId);

            $.ajax({
                url: `/transfers/api/requests/${requestId}`,
                method: 'DELETE',
                success: function(response) {
                    console.log('✅ استجابة حذف الطلب:', response);
                    if (response.success) {
                        showAlert('تم حذف الطلب بنجاح', 'success');
                        loadRequests(); // إعادة تحميل القائمة
                    } else {
                        showAlert('فشل في حذف الطلب: ' + response.message, 'danger');
                    }
                },
                error: function(xhr) {
                    console.error('❌ خطأ في حذف الطلب:', xhr);
                    const response = xhr.responseJSON;
                    showAlert(response?.message || 'حدث خطأ أثناء حذف الطلب', 'danger');
                }
            });
        }
    }
}

// طباعة نموذج الطلب
function printRequest(requestId) {
    console.log('🖨️ طباعة نموذج الطلب:', requestId);

    // فتح صفحة الطباعة في نافذة جديدة مع إعدادات أفضل
    const printUrl = `/transfers/print-request/${requestId}`;

    // محاولة فتح نافذة جديدة
    const printWindow = window.open(
        printUrl,
        'printWindow',
        'width=1000,height=800,scrollbars=yes,resizable=yes,menubar=no,toolbar=no,location=no,status=no'
    );

    if (printWindow) {
        printWindow.focus();
        console.log('✅ تم فتح نافذة الطباعة بنجاح');
    } else {
        // إذا تم حظر النوافذ المنبثقة، اعرض تنبيه وانتقل مباشرة
        alert('يرجى السماح للنوافذ المنبثقة لهذا الموقع لفتح نموذج الطباعة');
        window.open(printUrl, '_blank');
    }
}

// تطبيق الفلاتر
function applyFilters() {
    loadRequests();
}

// مسح الفلاتر
function clearFilters() {
    $('#statusFilter').val('');
    $('#branchFilter').val('');
    $('#dateFromFilter').val('');
    $('#dateToFilter').val('');
    $('#searchFilter').val('');
    loadRequests();
}

// تحديث الإحصائيات
function updateStatistics(stats) {
    $('#pendingCount').text(stats.pending || 0);
    $('#approvedCount').text(stats.approved || 0);
    $('#completedCount').text(stats.completed || 0);
    $('#totalAmount').text(formatAmount(stats.total_amount || 0));
}

// عرض تفاصيل الطلب
function viewRequestDetails(requestId) {
    console.log('👁️ عرض تفاصيل الطلب:', requestId);

    $.get(`/transfers/api/requests/${requestId}`)
        .done(function(response) {
            if (response.success) {
                displayRequestDetails(response.data);
                $('#requestDetailsModal').data('request-id', requestId);
                $('#requestDetailsModal').modal('show');
            } else {
                showError('فشل في تحميل تفاصيل الطلب');
            }
        })
        .fail(function() {
            showError('حدث خطأ أثناء تحميل التفاصيل');
        });
}

// إدارة وثائق الطلب
function manageDocuments(requestId) {
    console.log('📁 إدارة وثائق الطلب:', requestId);

    try {
        // فتح صفحة إدارة الوثائق في نافذة جديدة
        const documentsUrl = `/transfers/requests/${requestId}/documents`;
        const newWindow = window.open(documentsUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

        // التحقق من نجاح فتح النافذة
        if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
            // إذا فشل فتح النافذة (حاجب النوافذ المنبثقة)
            showNotification('⚠️ تم حجب النافذة المنبثقة. يرجى السماح للنوافذ المنبثقة وإعادة المحاولة.', 'warning');

            // محاولة بديلة - فتح في نفس التبويب
            if (confirm('هل تريد فتح صفحة إدارة الوثائق في نفس التبويب؟')) {
                window.location.href = documentsUrl;
            }
        } else {
            showNotification('✅ تم فتح صفحة إدارة الوثائق في نافذة جديدة', 'success');
        }
    } catch (error) {
        console.error('❌ خطأ في فتح نافذة إدارة الوثائق:', error);
        showNotification('❌ حدث خطأ في فتح نافذة إدارة الوثائق', 'error');

        // محاولة بديلة
        const documentsUrl = `/transfers/requests/${requestId}/documents`;
        window.location.href = documentsUrl;
    }
}

// دالة عرض الإشعارات
function showNotification(message, type = 'info') {
    const typeClasses = {
        'success': 'alert-success',
        'warning': 'alert-warning',
        'error': 'alert-danger',
        'info': 'alert-info'
    };

    const typeIcons = {
        'success': 'fas fa-check-circle',
        'warning': 'fas fa-exclamation-triangle',
        'error': 'fas fa-times-circle',
        'info': 'fas fa-info-circle'
    };

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${typeClasses[type] || 'alert-info'} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="${typeIcons[type] || 'fas fa-info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة الرسالة تلقائياً بعد 4 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 4000);
}

// عرض تفاصيل الطلب في النافذة المنبثقة
function displayRequestDetails(request) {
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>معلومات الطلب</h6>
                <table class="table table-sm">
                    <tr><td>رقم الطلب:</td><td>${request.request_number}</td></tr>
                    <tr><td>الحالة:</td><td><span class="badge status-${request.status}">${getStatusText(request.status)}</span></td></tr>
                    <tr><td>المبلغ:</td><td>${formatAmount(request.amount)} ${request.currency}</td></tr>
                    <tr><td>الغرض:</td><td>${request.purpose}</td></tr>
                    <tr><td>تاريخ الإنشاء:</td><td>${formatDateTime(request.created_at)}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>معلومات المستفيد</h6>
                <table class="table table-sm">
                    <tr><td>الاسم:</td><td>${request.beneficiary_name}</td></tr>
                    <tr><td>رقم الحساب:</td><td>${request.bank_account}</td></tr>
                    <tr><td>البنك:</td><td>${request.bank_name}</td></tr>
                    <tr><td>الفرع:</td><td>${request.bank_branch || 'غير محدد'}</td></tr>
                    <tr><td>IBAN:</td><td>${request.iban || 'غير محدد'}</td></tr>
                </table>
            </div>
        </div>
        ${request.notes ? `<div class="mt-3"><h6>ملاحظات</h6><p>${request.notes}</p></div>` : ''}
    `;
    
    $('#requestDetailsContent').html(content);
}

// دوال مساعدة
function formatAmount(amount) {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

function formatDate(dateString) {
    if (!dateString) return 'غير محدد';
    return new Date(dateString).toLocaleDateString('en-GB', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

function formatTime(dateString) {
    if (!dateString) return '';
    return new Date(dateString).toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    });
}

function formatDateTime(dateString) {
    if (!dateString) return 'غير محدد';
    return formatDate(dateString) + ' ' + formatTime(dateString);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showError(message) {
    showAlert(message, 'danger');
}

function showAlert(message, type = 'info') {
    // إنشاء تنبيه Bootstrap
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // إضافة التنبيه في أعلى الصفحة
    $('.container-fluid').prepend(alertHtml);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

// دوال الإجراءات
function editRequest(requestId) {
    console.log('✏️ تعديل الطلب:', requestId);

    // التوجه إلى صفحة تعديل الطلب
    window.location.href = `/transfers/edit-request/${requestId}`;
}

function approveRequest(requestId) {
    console.log('✅ اعتماد الطلب:', requestId);

    if (confirm('هل أنت متأكد من اعتماد هذا الطلب؟')) {
        // TODO: تطوير API اعتماد الطلب
        $.post(`/transfers/api/approve-request/${requestId}`)
            .done(function(response) {
                if (response.success) {
                    showAlert('تم اعتماد الطلب بنجاح', 'success');
                    loadRequests(); // إعادة تحميل القائمة
                } else {
                    showAlert('فشل في اعتماد الطلب: ' + response.message, 'danger');
                }
            })
            .fail(function() {
                showAlert('حدث خطأ أثناء اعتماد الطلب', 'danger');
            });
    }
}

function rejectRequest(requestId) {
    console.log('❌ رفض الطلب:', requestId);

    const reason = prompt('أدخل سبب رفض الطلب:');
    if (reason) {
        // TODO: تطوير API رفض الطلب
        $.post(`/transfers/api/reject-request/${requestId}`, {
            reason: reason
        })
            .done(function(response) {
                if (response.success) {
                    showAlert('تم رفض الطلب بنجاح', 'success');
                    loadRequests(); // إعادة تحميل القائمة
                } else {
                    showAlert('فشل في رفض الطلب: ' + response.message, 'danger');
                }
            })
            .fail(function() {
                showAlert('حدث خطأ أثناء رفض الطلب', 'danger');
            });
    }
}

function processRequest(requestId) {
    console.log('⚙️ معالجة الطلب:', requestId);

    if (confirm('هل تريد بدء معالجة هذا الطلب؟')) {
        // TODO: تطوير API معالجة الطلب
        $.post(`/transfers/api/requests/${requestId}/process`)
            .done(function(response) {
                if (response.success) {
                    showAlert('تم بدء معالجة الطلب بنجاح', 'success');
                    loadRequests(); // إعادة تحميل القائمة
                } else {
                    showAlert('فشل في معالجة الطلب: ' + response.message, 'danger');
                }
            })
            .fail(function() {
                showAlert('حدث خطأ أثناء معالجة الطلب', 'danger');
            });
    }
}

function completeRequest(requestId) {
    console.log('✅ إكمال الطلب:', requestId);

    if (confirm('هل تريد إكمال هذا الطلب؟')) {
        // TODO: تطوير API إكمال الطلب
        $.post(`/transfers/api/requests/${requestId}/complete`)
            .done(function(response) {
                if (response.success) {
                    showAlert('تم إكمال الطلب بنجاح', 'success');
                    loadRequests(); // إعادة تحميل القائمة
                } else {
                    showAlert('فشل في إكمال الطلب: ' + response.message, 'danger');
                }
            })
            .fail(function() {
                showAlert('حدث خطأ أثناء إكمال الطلب', 'danger');
            });
    }
}

// طباعة الطلب الحالي من modal
function printCurrentRequest() {
    const currentRequestId = $('#requestDetailsModal').data('request-id');
    if (currentRequestId) {
        printRequest(currentRequestId);
        $('#requestDetailsModal').modal('hide');
    } else {
        alert('لم يتم العثور على معرف الطلب');
    }
}


</script>
{% endblock %}
