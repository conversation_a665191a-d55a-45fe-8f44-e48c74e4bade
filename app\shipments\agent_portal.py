#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بوابة المخلص الإلكترونية
Customs Agent Electronic Portal

هذا النظام يوفر:
- واجهة مخصصة للمخلصين
- إدارة أوامر التسليم
- رفع وإدارة الوثائق
- تتبع مباشر للأوامر
- تحديث الحالات
- التواصل مع العملاء
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, flash
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
import os
import json
from database_manager import DatabaseManager
from app.shipments.notifications import AdvancedNotificationManager

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# إنشاء Blueprint للبوابة
agent_portal_bp = Blueprint('agent_portal', __name__, url_prefix='/agent-portal')

class AgentPortalManager:
    """مدير بوابة المخلص الإلكترونية"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.notification_manager = AdvancedNotificationManager()
        
        # إعدادات البوابة
        self.portal_config = {
            'upload_folder': 'uploads/agent_documents',
            'allowed_extensions': {'pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'},
            'max_file_size': 10 * 1024 * 1024,  # 10MB
            'documents_per_page': 20,
            'orders_per_page': 15
        }
    
    def get_agent_dashboard_data(self, agent_id: int) -> Dict:
        """جلب بيانات لوحة تحكم المخلص"""
        try:
            dashboard_data = {}
            
            # إحصائيات الأوامر
            orders_stats_query = """
                SELECT 
                    COUNT(*) as total_orders,
                    COUNT(CASE WHEN order_status = 'draft' THEN 1 END) as draft_orders,
                    COUNT(CASE WHEN order_status = 'sent' THEN 1 END) as sent_orders,
                    COUNT(CASE WHEN order_status = 'in_progress' THEN 1 END) as in_progress_orders,
                    COUNT(CASE WHEN order_status = 'completed' THEN 1 END) as completed_orders,
                    COUNT(CASE WHEN expected_completion_date < SYSDATE AND order_status NOT IN ('completed', 'cancelled') THEN 1 END) as overdue_orders
                FROM delivery_orders
                WHERE customs_agent_id = :agent_id
                AND created_date >= SYSDATE - 30
            """
            
            orders_stats = self.db_manager.execute_query(orders_stats_query, {'agent_id': agent_id})
            if orders_stats:
                row = orders_stats[0]
                dashboard_data['orders_stats'] = {
                    'total': row[0] or 0,
                    'draft': row[1] or 0,
                    'sent': row[2] or 0,
                    'in_progress': row[3] or 0,
                    'completed': row[4] or 0,
                    'overdue': row[5] or 0
                }
            
            # الأوامر الحديثة
            recent_orders_query = """
                SELECT
                    do.id, do.order_number, do.order_status, do.priority,
                    do.created_date, do.expected_completion_date,
                    cs.tracking_number, cs.shipment_number, cs.port_of_discharge
                FROM delivery_orders do
                JOIN cargo_shipments cs ON do.shipment_id = cs.id
                WHERE do.customs_agent_id = :agent_id
                ORDER BY do.created_date DESC
                FETCH FIRST 10 ROWS ONLY
            """
            
            recent_orders = self.db_manager.execute_query(recent_orders_query, {'agent_id': agent_id})
            dashboard_data['recent_orders'] = recent_orders or []
            
            # الأوامر المتأخرة
            overdue_orders_query = """
                SELECT
                    do.id, do.order_number, do.priority,
                    do.expected_completion_date,
                    TRUNC(SYSDATE - do.expected_completion_date) as days_overdue,
                    cs.tracking_number
                FROM delivery_orders do
                JOIN cargo_shipments cs ON do.shipment_id = cs.id
                WHERE do.customs_agent_id = :agent_id
                AND do.expected_completion_date < SYSDATE
                AND do.order_status NOT IN ('completed', 'cancelled')
                ORDER BY do.expected_completion_date ASC
            """
            
            overdue_orders = self.db_manager.execute_query(overdue_orders_query, {'agent_id': agent_id})
            dashboard_data['overdue_orders'] = overdue_orders or []
            
            # إحصائيات الوثائق
            documents_stats_query = """
                SELECT 
                    COUNT(*) as total_documents,
                    COUNT(CASE WHEN is_available = 1 THEN 1 END) as available_documents,
                    COUNT(CASE WHEN is_required = 1 AND is_available = 0 THEN 1 END) as missing_required_documents
                FROM delivery_order_documents dod
                JOIN delivery_orders do ON dod.delivery_order_id = do.id
                WHERE do.customs_agent_id = :agent_id
                AND do.order_status NOT IN ('completed', 'cancelled')
            """
            
            documents_stats = self.db_manager.execute_query(documents_stats_query, {'agent_id': agent_id})
            if documents_stats:
                row = documents_stats[0]
                dashboard_data['documents_stats'] = {
                    'total': row[0] or 0,
                    'available': row[1] or 0,
                    'missing_required': row[2] or 0
                }
            
            # الإشعارات الحديثة
            notifications_query = """
                SELECT 
                    notification_title, notification_content, sent_at, status
                FROM notifications_log
                WHERE recipient_id = :agent_id
                AND recipient_type = 'agent'
                ORDER BY sent_at DESC
                FETCH FIRST 5 ROWS ONLY
            """
            
            notifications = self.db_manager.execute_query(notifications_query, {'agent_id': agent_id})
            dashboard_data['recent_notifications'] = notifications or []
            
            return dashboard_data
            
        except Exception as e:
            logger.error(f"Error getting agent dashboard data: {e}")
            return {}
    
    def get_agent_orders(self, agent_id: int, status_filter: str = None, 
                        page: int = 1, per_page: int = 15) -> Dict:
        """جلب أوامر التسليم للمخلص مع التصفية والترقيم"""
        try:
            offset = (page - 1) * per_page
            
            # بناء الاستعلام مع التصفية
            where_clause = "WHERE do.customs_agent_id = :agent_id"
            params = {'agent_id': agent_id}
            
            if status_filter and status_filter != 'all':
                where_clause += " AND do.order_status = :status_filter"
                params['status_filter'] = status_filter
            
            # استعلام الأوامر
            orders_query = f"""
                SELECT
                    do.id, do.order_number, do.order_status, do.priority,
                    do.created_date, do.expected_completion_date, do.actual_completion_date,
                    do.special_instructions,
                    cs.tracking_number, cs.shipment_number, cs.port_of_discharge,
                    cs.cargo_description, cs.total_cost,
                    0 as required_docs,
                    0 as available_docs
                FROM delivery_orders do
                JOIN cargo_shipments cs ON do.shipment_id = cs.id
                {where_clause}
                ORDER BY
                    CASE WHEN do.order_status = 'overdue' THEN 1
                         WHEN do.priority = 'urgent' THEN 2
                         WHEN do.priority = 'high' THEN 3
                         ELSE 4 END,
                    do.expected_completion_date ASC,
                    do.created_date DESC
                OFFSET {offset} ROWS FETCH NEXT {per_page} ROWS ONLY
            """
            
            orders = self.db_manager.execute_query(orders_query, params)
            
            # استعلام العدد الإجمالي
            count_query = f"""
                SELECT COUNT(*)
                FROM delivery_orders do
                {where_clause}
            """
            
            total_count = self.db_manager.execute_query(count_query, params)
            total = total_count[0][0] if total_count else 0
            
            return {
                'orders': orders or [],
                'total': total,
                'page': page,
                'per_page': per_page,
                'total_pages': (total + per_page - 1) // per_page
            }
            
        except Exception as e:
            logger.error(f"Error getting agent orders: {e}")
            return {'orders': [], 'total': 0, 'page': 1, 'per_page': per_page, 'total_pages': 0}
    
    def get_order_details(self, order_id: int, agent_id: int) -> Optional[Dict]:
        """جلب تفاصيل أمر التسليم للمخلص"""
        try:
            # التحقق من ملكية الأمر للمخلص
            ownership_query = """
                SELECT 1 FROM delivery_orders
                WHERE id = :order_id AND customs_agent_id = :agent_id
            """
            
            ownership = self.db_manager.execute_query(ownership_query, {
                'order_id': order_id,
                'agent_id': agent_id
            })
            
            if not ownership:
                return None
            
            # جلب تفاصيل الأمر
            order_query = """
                SELECT 
                    do.id, do.order_number, do.order_status, do.priority,
                    do.created_date, do.expected_completion_date, do.actual_completion_date,
                    do.special_instructions,
                    0 as estimated_cost, 0 as actual_cost, 'SAR' as currency,
                    '' as contact_person, '' as contact_phone, '' as contact_email,
                    'pending' as documents_status, '' as documents_notes,
                    cs.id as shipment_id, cs.tracking_number, cs.shipment_number,
                    cs.port_of_loading, cs.port_of_discharge, cs.status as status,
                    cs.cargo_description, cs.total_cost, cs.total_weight, cs.total_volume,
                    '' as po_number, '' as supplier_name
                FROM delivery_orders do
                JOIN cargo_shipments cs ON do.shipment_id = cs.id
                WHERE do.id = :order_id
            """
            
            order_result = self.db_manager.execute_query(order_query, {'order_id': order_id})
            if not order_result:
                return None
            
            order_data = order_result[0]
            
            # جلب الوثائق المطلوبة
            documents_query = """
                SELECT 
                    id, document_type, document_name, is_required, is_available,
                    file_path, upload_date, notes
                FROM delivery_order_documents
                WHERE delivery_order_id = :order_id
                ORDER BY is_required DESC, document_type
            """
            
            documents = self.db_manager.execute_query(documents_query, {'order_id': order_id})
            
            # جلب تاريخ الحالات
            status_history_query = """
                SELECT 
                    old_status, new_status, status_date, notes, location
                FROM delivery_order_status_history
                WHERE delivery_order_id = :order_id
                ORDER BY status_date DESC
            """
            
            status_history = self.db_manager.execute_query(status_history_query, {'order_id': order_id})
            
            return {
                'order': order_data,
                'documents': documents or [],
                'status_history': status_history or []
            }
            
        except Exception as e:
            logger.error(f"Error getting order details: {e}")
            return None

    def update_order_status(self, order_id: int, agent_id: int, new_status: str,
                           notes: str = None, location: str = None) -> Dict:
        """تحديث حالة أمر التسليم"""
        try:
            # التحقق من ملكية الأمر
            ownership_query = """
                SELECT order_status FROM delivery_orders
                WHERE id = :order_id AND customs_agent_id = :agent_id
            """

            ownership = self.db_manager.execute_query(ownership_query, {
                'order_id': order_id,
                'agent_id': agent_id
            })

            if not ownership:
                return {'success': False, 'message': 'أمر التسليم غير موجود أو غير مخول لك'}

            old_status = ownership[0][0]

            # تحديث حالة الأمر
            update_query = """
                UPDATE delivery_orders
                SET order_status = :new_status,
                    updated_at = SYSDATE,
                    updated_by = :agent_id
            """

            params = {
                'new_status': new_status,
                'agent_id': agent_id,
                'order_id': order_id
            }

            # إضافة تاريخ الإنجاز إذا كانت الحالة مكتملة
            if new_status == 'completed':
                update_query += ", actual_completion_date = SYSDATE"

            update_query += " WHERE id = :order_id"

            self.db_manager.execute_update(update_query, params)

            # إضافة سجل في تاريخ الحالات
            history_query = """
                INSERT INTO delivery_order_status_history (
                    id, delivery_order_id, old_status, new_status,
                    status_date, updated_by, notes, location
                ) VALUES (
                    delivery_order_status_seq.NEXTVAL, :order_id, :old_status, :new_status,
                    SYSDATE, :agent_id, :notes, :location
                )
            """

            self.db_manager.execute_update(history_query, {
                'order_id': order_id,
                'old_status': old_status,
                'new_status': new_status,
                'agent_id': agent_id,
                'notes': notes,
                'location': location
            })

            # إرسال إشعار بتحديث الحالة
            self._send_status_update_notification(order_id, old_status, new_status)

            return {
                'success': True,
                'message': 'تم تحديث حالة الأمر بنجاح',
                'old_status': old_status,
                'new_status': new_status
            }

        except Exception as e:
            logger.error(f"Error updating order status: {e}")
            return {'success': False, 'message': f'خطأ في تحديث الحالة: {str(e)}'}

    def upload_document(self, order_id: int, agent_id: int, document_type: str,
                       file, notes: str = None) -> Dict:
        """رفع وثيقة لأمر التسليم"""
        try:
            # التحقق من ملكية الأمر
            ownership_query = """
                SELECT 1 FROM delivery_orders
                WHERE id = :order_id AND customs_agent_id = :agent_id
            """

            ownership = self.db_manager.execute_query(ownership_query, {
                'order_id': order_id,
                'agent_id': agent_id
            })

            if not ownership:
                return {'success': False, 'message': 'أمر التسليم غير موجود أو غير مخول لك'}

            # التحقق من نوع الملف
            if not self._allowed_file(file.filename):
                return {'success': False, 'message': 'نوع الملف غير مسموح'}

            # حفظ الملف
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{order_id}_{document_type}_{timestamp}_{filename}"

            upload_path = os.path.join(self.portal_config['upload_folder'], filename)
            os.makedirs(os.path.dirname(upload_path), exist_ok=True)
            file.save(upload_path)

            # تحديث سجل الوثيقة في قاعدة البيانات
            update_doc_query = """
                UPDATE delivery_order_documents
                SET is_available = 1,
                    file_path = :file_path,
                    upload_date = SYSDATE,
                    uploaded_by = :agent_id,
                    notes = :notes
                WHERE delivery_order_id = :order_id
                AND document_type = :document_type
            """

            self.db_manager.execute_update(update_doc_query, {
                'file_path': upload_path,
                'agent_id': agent_id,
                'notes': notes,
                'order_id': order_id,
                'document_type': document_type
            })

            # التحقق من اكتمال جميع الوثائق المطلوبة
            self._check_documents_completion(order_id)

            return {
                'success': True,
                'message': 'تم رفع الوثيقة بنجاح',
                'filename': filename,
                'file_path': upload_path
            }

        except Exception as e:
            logger.error(f"Error uploading document: {e}")
            return {'success': False, 'message': f'خطأ في رفع الوثيقة: {str(e)}'}

    def get_agent_performance(self, agent_id: int, period_days: int = 30) -> Dict:
        """جلب إحصائيات أداء المخلص"""
        try:
            performance_query = """
                SELECT
                    COUNT(*) as total_orders,
                    COUNT(CASE WHEN order_status = 'completed' THEN 1 END) as completed_orders,
                    COUNT(CASE WHEN order_status = 'cancelled' THEN 1 END) as cancelled_orders,
                    COUNT(CASE WHEN expected_completion_date < SYSDATE AND order_status NOT IN ('completed', 'cancelled') THEN 1 END) as overdue_orders,
                    AVG(CASE
                        WHEN actual_completion_date IS NOT NULL AND expected_completion_date IS NOT NULL
                        THEN actual_completion_date - expected_completion_date
                        ELSE NULL
                    END) as avg_delay_days,
                    AVG(CASE
                        WHEN order_status = 'completed'
                        THEN actual_completion_date - created_date
                        ELSE NULL
                    END) as avg_completion_days
                FROM delivery_orders
                WHERE customs_agent_id = :agent_id
                AND created_date >= SYSDATE - :period_days
            """

            performance_result = self.db_manager.execute_query(performance_query, {
                'agent_id': agent_id,
                'period_days': period_days
            })

            if performance_result:
                row = performance_result[0]
                total_orders = row[0] or 0
                completed_orders = row[1] or 0

                performance = {
                    'total_orders': total_orders,
                    'completed_orders': completed_orders,
                    'cancelled_orders': row[2] or 0,
                    'overdue_orders': row[3] or 0,
                    'completion_rate': (completed_orders / total_orders * 100) if total_orders > 0 else 0,
                    'avg_delay_days': float(row[4] or 0),
                    'avg_completion_days': float(row[5] or 0)
                }

                # حساب التقييم
                performance['rating'] = self._calculate_performance_rating(performance)

                return performance

            return {}

        except Exception as e:
            logger.error(f"Error getting agent performance: {e}")
            return {}

    def _allowed_file(self, filename: str) -> bool:
        """التحقق من نوع الملف المسموح"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in self.portal_config['allowed_extensions']

    def _check_documents_completion(self, order_id: int):
        """فحص اكتمال الوثائق المطلوبة"""
        try:
            completion_query = """
                SELECT
                    COUNT(CASE WHEN is_required = 1 THEN 1 END) as required_count,
                    COUNT(CASE WHEN is_required = 1 AND is_available = 1 THEN 1 END) as available_count
                FROM delivery_order_documents
                WHERE delivery_order_id = :order_id
            """

            result = self.db_manager.execute_query(completion_query, {'order_id': order_id})
            if result:
                required_count, available_count = result[0]

                if required_count > 0 and available_count == required_count:
                    # جميع الوثائق المطلوبة متوفرة
                    update_query = """
                        UPDATE delivery_orders
                        SET documents_status = 'complete'
                        WHERE id = :order_id
                    """
                    self.db_manager.execute_update(update_query, {'order_id': order_id})

                    # إرسال إشعار باكتمال الوثائق
                    self._send_documents_complete_notification(order_id)

        except Exception as e:
            logger.error(f"Error checking documents completion: {e}")

    def _send_status_update_notification(self, order_id: int, old_status: str, new_status: str):
        """إرسال إشعار تحديث الحالة"""
        try:
            # يمكن تطوير هذه الوظيفة مع نظام الإشعارات المتقدم
            pass
        except Exception as e:
            logger.error(f"Error sending status update notification: {e}")

    def _send_documents_complete_notification(self, order_id: int):
        """إرسال إشعار اكتمال الوثائق"""
        try:
            # يمكن تطوير هذه الوظيفة مع نظام الإشعارات المتقدم
            pass
        except Exception as e:
            logger.error(f"Error sending documents complete notification: {e}")

    def _calculate_performance_rating(self, performance: Dict) -> float:
        """حساب تقييم الأداء"""
        try:
            completion_rate = performance.get('completion_rate', 0)
            avg_delay = performance.get('avg_delay_days', 0)
            overdue_orders = performance.get('overdue_orders', 0)
            total_orders = performance.get('total_orders', 0)

            # نقاط معدل الإنجاز (0-2)
            completion_score = min(2.0, completion_rate / 50)

            # نقاط التأخير (0-2)
            if avg_delay <= 0:
                delay_score = 2.0
            elif avg_delay <= 1:
                delay_score = 1.5
            elif avg_delay <= 3:
                delay_score = 1.0
            else:
                delay_score = 0.5

            # نقاط الأوامر المتأخرة (0-1)
            overdue_rate = (overdue_orders / total_orders * 100) if total_orders > 0 else 0
            if overdue_rate == 0:
                overdue_score = 1.0
            elif overdue_rate <= 10:
                overdue_score = 0.8
            elif overdue_rate <= 20:
                overdue_score = 0.5
            else:
                overdue_score = 0.2

            total_rating = completion_score + delay_score + overdue_score
            return min(5.0, max(1.0, total_rating))

        except Exception as e:
            logger.error(f"Error calculating performance rating: {e}")
            return 3.0

# مثيل عام للاستخدام
agent_portal_manager = AgentPortalManager()

# ===== Routes للبوابة =====

@agent_portal_bp.route('/dashboard')
@login_required
def dashboard():
    """لوحة تحكم المخلص"""
    try:
        # التحقق من أن المستخدم مخلص
        agent_id = session.get('agent_id')
        if not agent_id:
            flash('يجب تسجيل الدخول كمخلص للوصول لهذه الصفحة', 'error')
            return redirect(url_for('auth.login'))

        # جلب بيانات لوحة التحكم
        dashboard_data = agent_portal_manager.get_agent_dashboard_data(agent_id)

        return render_template('agent_portal/dashboard.html',
                             dashboard_data=dashboard_data,
                             agent_id=agent_id)

    except Exception as e:
        logger.error(f"Error in agent dashboard: {e}")
        flash('حدث خطأ في تحميل لوحة التحكم', 'error')
        return redirect(url_for('main.index'))

@agent_portal_bp.route('/orders')
@login_required
def orders():
    """قائمة أوامر التسليم للمخلص"""
    try:
        agent_id = session.get('agent_id')
        if not agent_id:
            return redirect(url_for('auth.login'))

        # معاملات التصفية والترقيم
        status_filter = request.args.get('status', 'all')
        page = int(request.args.get('page', 1))
        per_page = 15

        # جلب الأوامر
        orders_data = agent_portal_manager.get_agent_orders(
            agent_id, status_filter, page, per_page
        )

        return render_template('agent_portal/orders.html',
                             orders_data=orders_data,
                             status_filter=status_filter,
                             agent_id=agent_id)

    except Exception as e:
        logger.error(f"Error in agent orders: {e}")
        flash('حدث خطأ في تحميل الأوامر', 'error')
        return redirect(url_for('agent_portal.dashboard'))

@agent_portal_bp.route('/order/<int:order_id>')
@login_required
def order_details(order_id):
    """تفاصيل أمر التسليم"""
    try:
        agent_id = session.get('agent_id')
        if not agent_id:
            return redirect(url_for('auth.login'))

        # جلب تفاصيل الأمر
        order_data = agent_portal_manager.get_order_details(order_id, agent_id)
        if not order_data:
            flash('أمر التسليم غير موجود أو غير مخول لك', 'error')
            return redirect(url_for('agent_portal.orders'))

        return render_template('agent_portal/order_details.html',
                             order_data=order_data,
                             agent_id=agent_id)

    except Exception as e:
        logger.error(f"Error in order details: {e}")
        flash('حدث خطأ في تحميل تفاصيل الأمر', 'error')
        return redirect(url_for('agent_portal.orders'))

@agent_portal_bp.route('/api/update-status', methods=['POST'])
@login_required
def update_order_status():
    """تحديث حالة أمر التسليم"""
    try:
        agent_id = session.get('agent_id')
        if not agent_id:
            return jsonify({'success': False, 'message': 'غير مخول'})

        data = request.get_json()
        order_id = data.get('order_id')
        new_status = data.get('new_status')
        notes = data.get('notes', '')
        location = data.get('location', '')

        if not order_id or not new_status:
            return jsonify({'success': False, 'message': 'بيانات ناقصة'})

        result = agent_portal_manager.update_order_status(
            order_id, agent_id, new_status, notes, location
        )

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error updating order status: {e}")
        return jsonify({'success': False, 'message': f'خطأ في التحديث: {str(e)}'})

@agent_portal_bp.route('/api/upload-document', methods=['POST'])
@login_required
def upload_document():
    """رفع وثيقة"""
    try:
        agent_id = session.get('agent_id')
        if not agent_id:
            return jsonify({'success': False, 'message': 'غير مخول'})

        order_id = request.form.get('order_id')
        document_type = request.form.get('document_type')
        notes = request.form.get('notes', '')

        if 'file' not in request.files:
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})

        if not order_id or not document_type:
            return jsonify({'success': False, 'message': 'بيانات ناقصة'})

        result = agent_portal_manager.upload_document(
            int(order_id), agent_id, document_type, file, notes
        )

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        return jsonify({'success': False, 'message': f'خطأ في رفع الملف: {str(e)}'})

@agent_portal_bp.route('/performance')
@login_required
def performance():
    """صفحة إحصائيات الأداء"""
    try:
        agent_id = session.get('agent_id')
        if not agent_id:
            return redirect(url_for('auth.login'))

        # فترات مختلفة للإحصائيات
        periods = [7, 30, 90, 365]
        performance_data = {}

        for period in periods:
            performance_data[f'period_{period}'] = agent_portal_manager.get_agent_performance(
                agent_id, period
            )

        return render_template('agent_portal/performance.html',
                             performance_data=performance_data,
                             agent_id=agent_id)

    except Exception as e:
        logger.error(f"Error in performance page: {e}")
        flash('حدث خطأ في تحميل إحصائيات الأداء', 'error')
        return redirect(url_for('agent_portal.dashboard'))

@agent_portal_bp.route('/api/performance-chart')
@login_required
def performance_chart_data():
    """بيانات الرسم البياني للأداء"""
    try:
        agent_id = session.get('agent_id')
        if not agent_id:
            return jsonify({'error': 'غير مخول'})

        period = int(request.args.get('period', 30))

        # جلب بيانات الأداء اليومية
        daily_performance_query = """
            SELECT
                TRUNC(created_date) as order_date,
                COUNT(*) as total_orders,
                COUNT(CASE WHEN order_status = 'completed' THEN 1 END) as completed_orders
            FROM delivery_orders
            WHERE customs_agent_id = :agent_id
            AND created_date >= SYSDATE - :period
            GROUP BY TRUNC(created_date)
            ORDER BY order_date
        """

        daily_data = agent_portal_manager.db_manager.execute_query(
            daily_performance_query,
            {'agent_id': agent_id, 'period': period}
        )

        chart_data = {
            'dates': [],
            'total_orders': [],
            'completed_orders': [],
            'completion_rates': []
        }

        for row in daily_data or []:
            date, total, completed = row
            chart_data['dates'].append(date.strftime('%Y-%m-%d'))
            chart_data['total_orders'].append(total)
            chart_data['completed_orders'].append(completed)
            chart_data['completion_rates'].append(
                (completed / total * 100) if total > 0 else 0
            )

        return jsonify(chart_data)

    except Exception as e:
        logger.error(f"Error getting performance chart data: {e}")
        return jsonify({'error': f'خطأ في جلب البيانات: {str(e)}'})

@agent_portal_bp.route('/notifications')
@login_required
def notifications():
    """صفحة الإشعارات"""
    try:
        agent_id = session.get('agent_id')
        if not agent_id:
            return redirect(url_for('auth.login'))

        # جلب الإشعارات
        notifications_query = """
            SELECT
                id, notification_title, notification_content,
                sent_at, status, template_key
            FROM notifications_log
            WHERE recipient_id = :agent_id
            AND recipient_type = 'agent'
            ORDER BY sent_at DESC
            FETCH FIRST 50 ROWS ONLY
        """

        notifications_data = agent_portal_manager.db_manager.execute_query(
            notifications_query, {'agent_id': agent_id}
        )

        return render_template('agent_portal/notifications.html',
                             notifications=notifications_data or [],
                             agent_id=agent_id)

    except Exception as e:
        logger.error(f"Error in notifications page: {e}")
        flash('حدث خطأ في تحميل الإشعارات', 'error')
        return redirect(url_for('agent_portal.dashboard'))
