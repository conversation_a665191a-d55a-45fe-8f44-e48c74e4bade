<!-- مكون البحث والتصفية -->
<div class="search-filters-component">
    <!-- البحث السريع -->
    <div class="row mb-3">
        <div class="col-md-6 mb-2">
            <label class="form-label">
                <i class="fas fa-search me-1"></i>
                البحث السريع
            </label>
            <div class="input-group">
                <input type="text" class="form-control" id="quickSearch" placeholder="ابحث برقم التتبع، رقم الشحنة، أو اسم العميل..." onkeyup="performQuickSearch()">
                <button class="btn btn-outline-primary" type="button" id="voiceSearchBtn"
                        onclick="toggleVoiceSearch()" title="البحث الصوتي">
                    <i class="fas fa-microphone" id="voiceSearchIcon"></i>
                    <span class="icon-fallback">🎤</span>
                </button>
                <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()" title="مسح البحث">
                    <i class="fas fa-times"></i>
                    <span class="icon-fallback">✖️</span>
                </button>
            </div>
            <div id="voiceSearchStatus" class="small text-muted mt-1" style="display: none;">
                <i class="fas fa-circle text-danger me-1 pulse"></i>
                جاري الاستماع... تحدث الآن
            </div>
        </div>
        <div class="col-md-3 mb-2">
            <label class="form-label">
                <i class="fas fa-filter me-1"></i>
                حالة الشحنة
            </label>
            <select class="form-select" id="statusFilter" onchange="handleStatusFilter(this)">
                <option value="">جميع الحالات</option>
                {% if shipment_statuses %}
                    {% for status in shipment_statuses %}
                        <option value="{{ status[0] }}">{{ status[1] }}</option>
                    {% endfor %}
                {% else %}
                    <!-- قيم افتراضية في حالة عدم وجود بيانات -->
                    <option value="معلق">معلق</option>
                    <option value="محجوز">محجوز</option>
                    <option value="مؤكد">مؤكد</option>
                    <option value="تم الاستلام">تم الاستلام</option>
                    <option value="في الطريق">في الطريق</option>
                    <option value="خارج للتسليم">خارج للتسليم</option>
                    <option value="تم التسليم">تم التسليم</option>
                    <option value="مرتجع">مرتجع</option>
                {% endif %}
            </select>
        </div>
        <div class="col-md-3 mb-2">
            <label class="form-label">
                <i class="fas fa-truck me-1"></i>
                نوع الشحن
            </label>
            <select class="form-select" id="shippingTypeFilter" onchange="applyFilters()">
                <option value="">جميع الأنواع</option>
                <option value="بحري">بحري</option>
                <option value="جوي">جوي</option>
                <option value="بري">بري</option>
            </select>
        </div>
    </div>

    <!-- مرشحات التاريخ -->
    <div class="row mb-3">
        <div class="col-md-3 mb-2">
            <label class="form-label">
                <i class="fas fa-calendar-alt me-1"></i>
                من تاريخ
            </label>
            <input type="date" class="form-control" id="dateFrom" onchange="applyFilters()">
        </div>
        <div class="col-md-3 mb-2">
            <label class="form-label">
                <i class="fas fa-calendar-alt me-1"></i>
                إلى تاريخ
            </label>
            <input type="date" class="form-control" id="dateTo" onchange="applyFilters()">
        </div>
        <div class="col-md-3 mb-2">
            <label class="form-label">
                <i class="fas fa-ship me-1"></i>
                خط الشحن
            </label>
            <select class="form-select" id="shippingLineFilter" onchange="applyFilters()">
                <option value="">جميع خطوط الشحن</option>
                {% if shipping_lines %}
                    {% for line in shipping_lines %}
                        <option value="{{ line[0] }}">{{ line[2] }}</option>
                    {% endfor %}
                {% else %}
                    <!-- قيم افتراضية في حالة عدم وجود بيانات -->
                    <option value="1">Mediterranean Shipping Company</option>
                    <option value="2">Maersk Line</option>
                    <option value="3">COSCO Shipping</option>
                    <option value="4">CMA CGM</option>
                    <option value="5">Hapag-Lloyd</option>
                {% endif %}
            </select>
        </div>
        <div class="col-md-3 mb-2">
            <label class="form-label">
                <i class="fas fa-anchor me-1"></i>
                ميناء الوصول
            </label>
            <select class="form-select" id="destinationPortFilter" onchange="applyFilters()">
                <option value="">جميع الموانئ</option>
                <option value="عدن">عدن - اليمن</option>
                <option value="جدة">جدة - السعودية</option>
                <option value="دبي">دبي - الإمارات</option>
                <option value="الدمام">الدمام - السعودية</option>
                <option value="الكويت">الكويت</option>
            </select>
        </div>
    </div>

    <!-- أزرار التصفية السريعة والإجراءات -->
    <div class="row">
        <div class="col-md-8 mb-2">
            <label class="form-label small text-muted">
                <i class="fas fa-clock me-1"></i>
                تصفية سريعة بالتاريخ
            </label>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickDate('today')">اليوم</button>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickDate('week')">هذا الأسبوع</button>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickDate('month')">هذا الشهر</button>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickDate('year')">هذا العام</button>
            </div>
        </div>
        <div class="col-md-4 mb-2">
            <label class="form-label small text-muted">
                <i class="fas fa-tools me-1"></i>
                إجراءات
            </label>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="resetAllFilters()">
                    <i class="fas fa-undo me-1"></i>
                    إعادة تعيين
                </button>
                <button type="button" class="btn btn-outline-info btn-sm" onclick="saveFilters()">
                    <i class="fas fa-save me-1"></i>
                    حفظ المرشحات
                </button>
                <button type="button" class="btn btn-outline-success btn-sm" onclick="exportFilteredData()">
                    <i class="fas fa-download me-1"></i>
                    تصدير النتائج
                </button>
            </div>
        </div>
    </div>

    <!-- شريط النتائج -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="alert alert-info d-none" id="filterResults">
                <i class="fas fa-info-circle me-2"></i>
                <span id="filterResultsText">جاري تطبيق المرشحات...</span>
            </div>
        </div>
    </div>
</div>

<script>
// حل سريع ومضمون لمشكلة التصفية
function handleStatusFilter(selectElement) {
    const selectedStatus = selectElement.value;
    console.log('🔍 تصفية حالة الشحنة:', selectedStatus);

    // جلب جميع صفوف الجدول
    const tableRows = document.querySelectorAll('#shipmentsTable tbody tr');
    console.log(`📊 عدد الصفوف في الجدول: ${tableRows.length}`);

    let visibleCount = 0;

    tableRows.forEach(row => {
        if (!selectedStatus) {
            // إذا لم يتم اختيار حالة، أظهر جميع الصفوف
            row.style.display = '';
            visibleCount++;
        } else {
            // البحث الذكي في جميع أعمدة الصف
            const rowText = row.textContent.toLowerCase();

            // خريطة ترجمة الحالات
            const statusMap = {
                'draft': ['مسودة', 'draft'],
                'confirmed': ['مؤكدة', 'confirmed'],
                'in_transit': ['قيد الشحن', 'في الطريق', 'in_transit', 'in transit'],
                'arrived_port': ['وصلت للميناء', 'arrived_port', 'arrived'],
                'customs_clearance': ['قيد التخليص', 'customs_clearance', 'customs'],
                'ready_pickup': ['جاهزة للاستلام', 'ready_pickup', 'ready'],
                'delivered': ['تم التسليم', 'delivered', 'مسلم'],
                'cancelled': ['ملغية', 'cancelled', 'ملغي'],
                'delayed': ['متأخرة', 'delayed', 'متأخر'],
                'returned': ['معادة', 'returned', 'مرتجع']
            };

            // البحث بالكود أو الترجمات
            let isMatch = false;
            const searchTerms = statusMap[selectedStatus] || [selectedStatus];

            for (const term of searchTerms) {
                if (rowText.includes(term.toLowerCase())) {
                    isMatch = true;
                    break;
                }
            }

            // تشخيص مفصل للصفوف الأولى
            if (row === tableRows[0] || row === tableRows[1]) {
                const statusCell = row.querySelector('td:nth-child(10)');
                const statusCellText = statusCell ? statusCell.textContent.trim() : 'غير موجود';

                console.log(`🔍 تشخيص الصف ${Array.from(tableRows).indexOf(row) + 1}:`, {
                    selectedStatus: selectedStatus,
                    searchTerms: searchTerms,
                    statusCellText: statusCellText,
                    rowText: rowText.substring(0, 150) + '...',
                    isMatch: isMatch
                });
            }

            if (isMatch) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        }
    });

    console.log(`📊 عرض ${visibleCount} من ${tableRows.length} شحنة`);

    // إظهار رسالة إذا لم توجد نتائج
    if (visibleCount === 0 && selectedStatus) {
        console.log('⚠️ لا توجد شحنات بالحالة المحددة');
    }
}

// نسخة احتياطية من applyFilters
function applyFilters() {
    console.log('🔄 استدعاء applyFilters احتياطي...');
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        handleStatusFilter(statusFilter);
    }
}
</script>

<style>
/* 🎨 تحسينات فنية فورية للمكون */
.search-filters-component {
    background: linear-gradient(145deg, #ffffff, #f8f9fa) !important;
    border-radius: 20px !important;
    padding: 25px !important;
    margin-bottom: 25px !important;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15) !important;
    border: 2px solid rgba(102, 126, 234, 0.2) !important;
    position: relative !important;
    overflow: hidden !important;
}

/* إضافة تأثير متوهج للمرشحات */
.search-filters-component::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
    background-size: 200% 100%;
    animation: gradient-flow 3s ease infinite;
}

@keyframes gradient-flow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* تأثير خلفية متحركة */
.search-filters-component::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(102, 126, 234, 0.03), transparent);
    animation: shimmer 3s infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.search-filters-component .form-label {
    font-weight: 600 !important;
    color: #495057 !important;
    margin-bottom: 8px !important;
    position: relative !important;
    z-index: 1 !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
}

.search-filters-component .form-label i {
    color: #667eea !important;
    margin-left: 5px !important;
    filter: drop-shadow(0 1px 2px rgba(102, 126, 234, 0.3)) !important;
}

.search-filters-component .form-control,
.search-filters-component .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
    /* إصلاح مشكلة تداخل السهم مع المحتوى */
    padding-right: 2.5rem !important;
    background-position: right 0.75rem center !important;
    background-size: 16px 12px !important;
}

.search-filters-component .form-control:focus,
.search-filters-component .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* تحسين مظهر القوائم المنسدلة */
.search-filters-component .form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right 0.75rem center !important;
    background-size: 16px 12px !important;
    padding-right: 2.5rem !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
}

.search-filters-component .btn-group .btn {
    border-radius: 20px !important;
    margin-right: 8px !important;
    margin-bottom: 5px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    border: none !important;
    position: relative !important;
    z-index: 1 !important;
}

.search-filters-component .btn-sm {
    padding: 0.5rem 1.2rem !important;
    font-size: 0.875rem !important;
}

/* تحسين أزرار المرشحات */
.search-filters-component .btn-outline-secondary {
    background: linear-gradient(135deg, #ffffff, #f8f9fa) !important;
    border: 2px solid #e9ecef !important;
    color: #495057 !important;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1) !important;
}

.search-filters-component .btn-outline-secondary:hover {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3) !important;
}

.search-filters-component .btn-outline-info {
    background: linear-gradient(135deg, #ffffff, #f0f8ff) !important;
    border: 2px solid #17a2b8 !important;
    color: #17a2b8 !important;
    box-shadow: 0 3px 10px rgba(23, 162, 184, 0.2) !important;
}

.search-filters-component .btn-outline-info:hover {
    background: linear-gradient(135deg, #17a2b8, #6f42c1) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4) !important;
}

.search-filters-component .btn-outline-success {
    background: linear-gradient(135deg, #ffffff, #f0fff4) !important;
    border: 2px solid #28a745 !important;
    color: #28a745 !important;
    box-shadow: 0 3px 10px rgba(40, 167, 69, 0.2) !important;
}

.search-filters-component .btn-outline-success:hover {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4) !important;
}

.search-filters-component .btn-outline-warning {
    background: linear-gradient(135deg, #ffffff, #fffbf0) !important;
    border: 2px solid #ffc107 !important;
    color: #856404 !important;
    box-shadow: 0 3px 10px rgba(255, 193, 7, 0.2) !important;
}

.search-filters-component .btn-outline-warning:hover {
    background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(255, 193, 7, 0.4) !important;
}

.search-filters-component .input-group .btn {
    border-radius: 0;
}

.search-filters-component .input-group .btn:last-child {
    border-radius: 0 8px 8px 0;
}

/* تنسيق البحث الصوتي */
#voiceSearchBtn {
    transition: all 0.3s ease !important;
}

#voiceSearchBtn:hover {
    background-color: #007bff !important;
    color: white !important;
    transform: scale(1.05) !important;
}

#voiceSearchBtn.listening {
    background-color: #dc3545 !important;
    color: white !important;
    border-color: #dc3545 !important;
    animation: pulse-voice 1.5s infinite !important;
}

@keyframes pulse-voice {
    0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

.pulse {
    animation: pulse-dot 1s infinite !important;
}

@keyframes pulse-dot {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

#voiceSearchStatus {
    font-size: 0.75rem !important;
    color: #dc3545 !important;
    font-weight: 500 !important;
}

.search-filters-component .alert {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .search-filters-component {
        padding: 15px;
    }
    
    .search-filters-component .btn-group {
        flex-wrap: wrap;
    }
    
    .search-filters-component .btn-group .btn {
        margin-bottom: 5px;
    }
}
</style>
