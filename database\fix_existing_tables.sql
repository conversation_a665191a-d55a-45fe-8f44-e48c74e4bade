-- =====================================================
-- إصلاح الجداول الموجودة وإضافة الحقول المفقودة
-- Fix Existing Tables and Add Missing Fields
-- =====================================================

-- إضافة حقول مفقودة لجدول PURCHASE_ORDERS (إذا لم تكن موجودة)
BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD supplier_id NUMBER';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN -- العمود موجود بالفعل
            NULL; -- تجاهل الخطأ
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD payment_terms_days NUMBER DEFAULT 30';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD subtotal_amount NUMBER(15,2) DEFAULT 0';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD tax_amount NUMBER(15,2) DEFAULT 0';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD shipping_amount NUMBER(15,2) DEFAULT 0';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD other_charges NUMBER(15,2) DEFAULT 0';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD total_amount_due NUMBER(15,2) DEFAULT 0';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD advance_payment_amount NUMBER(15,2) DEFAULT 0';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD advance_payment_date DATE';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD final_payment_amount NUMBER(15,2) DEFAULT 0';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD final_payment_date DATE';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD exchange_rate NUMBER(15,6) DEFAULT 1';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD base_currency_amount NUMBER(15,2)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD delivery_status VARCHAR2(30) DEFAULT ''PENDING''';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD goods_received_date DATE';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD goods_received_by NUMBER';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD is_recurring CHAR(1) DEFAULT ''N''';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD parent_po_id NUMBER';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD approval_workflow_id NUMBER';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD approved_date DATE';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD approved_by NUMBER';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD cancelled_date DATE';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD cancelled_by NUMBER';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD cancellation_reason VARCHAR2(500)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            NULL;
        END IF;
END;
/

-- تحديث القيم الافتراضية للحقول الموجودة
UPDATE PURCHASE_ORDERS SET 
    payment_status = 'PENDING' 
WHERE payment_status IS NULL;

UPDATE PURCHASE_ORDERS SET 
    outstanding_amount = NVL(total_amount, 0) - NVL(paid_amount, 0)
WHERE outstanding_amount IS NULL;

UPDATE PURCHASE_ORDERS SET 
    delivery_status = 'PENDING' 
WHERE delivery_status IS NULL;

-- إنشاء مفاتيح خارجية آمنة
BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD CONSTRAINT fk_po_supplier FOREIGN KEY (supplier_id) REFERENCES SUPPLIERS(id)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -2275 THEN -- المفتاح موجود بالفعل
            NULL;
        END IF;
END;
/

-- إنشاء فهارس إضافية للأداء
BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX idx_po_supplier_new ON PURCHASE_ORDERS(supplier_id)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 THEN -- الفهرس موجود بالفعل
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX idx_po_payment_status_new ON PURCHASE_ORDERS(payment_status)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX idx_po_delivery_status_new ON PURCHASE_ORDERS(delivery_status)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 THEN
            NULL;
        END IF;
END;
/

-- إنشاء trigger لتحديث الحقول تلقائياً
CREATE OR REPLACE TRIGGER purchase_orders_update_trigger
    BEFORE UPDATE ON PURCHASE_ORDERS
    FOR EACH ROW
BEGIN
    -- تحديث المبلغ المتبقي
    :NEW.outstanding_amount := NVL(:NEW.total_amount, 0) - NVL(:NEW.paid_amount, 0);
    
    -- تحديث المبلغ بالعملة الأساسية
    IF :NEW.exchange_rate IS NOT NULL AND :NEW.total_amount IS NOT NULL THEN
        :NEW.base_currency_amount := :NEW.total_amount * :NEW.exchange_rate;
    END IF;
    
    -- تحديث تاريخ التعديل
    :NEW.updated_at := SYSDATE;
END;
/

-- رسالة نجاح
SELECT 'تم إصلاح الجداول الموجودة بنجاح!' as status FROM dual;

COMMIT;
