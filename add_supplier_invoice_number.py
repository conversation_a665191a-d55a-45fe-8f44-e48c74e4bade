#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة حقل رقم فاتورة المورد إلى جدول أوامر الشراء
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oracle_manager import OracleManager
import logging

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def add_supplier_invoice_number_column():
    """إضافة عمود رقم فاتورة المورد إلى جدول PURCHASE_ORDERS"""
    
    oracle_manager = None
    try:
        # الاتصال بقاعدة البيانات
        oracle_manager = OracleManager()
        oracle_manager.connect()
        
        logger.info("🔗 تم الاتصال بقاعدة البيانات بنجاح")
        
        # فحص وجود العمود أولاً
        check_column_query = """
        SELECT COUNT(*) 
        FROM USER_TAB_COLUMNS 
        WHERE TABLE_NAME = 'PURCHASE_ORDERS' 
        AND COLUMN_NAME = 'SUPPLIER_INVOICE_NUMBER'
        """
        
        result = oracle_manager.execute_query(check_column_query)
        column_exists = result[0][0] > 0 if result else False
        
        if column_exists:
            logger.info("✅ عمود SUPPLIER_INVOICE_NUMBER موجود بالفعل في جدول PURCHASE_ORDERS")
            return True
        
        # إضافة العمود الجديد
        logger.info("📝 إضافة عمود SUPPLIER_INVOICE_NUMBER إلى جدول PURCHASE_ORDERS...")
        
        add_column_query = """
        ALTER TABLE PURCHASE_ORDERS 
        ADD SUPPLIER_INVOICE_NUMBER VARCHAR2(100)
        """
        
        oracle_manager.execute_update(add_column_query)
        
        logger.info("✅ تم إضافة عمود SUPPLIER_INVOICE_NUMBER بنجاح")
        
        # إضافة تعليق على العمود
        comment_query = """
        COMMENT ON COLUMN PURCHASE_ORDERS.SUPPLIER_INVOICE_NUMBER 
        IS 'رقم فاتورة المورد - اختياري'
        """
        
        oracle_manager.execute_update(comment_query)
        
        logger.info("📝 تم إضافة تعليق على العمود")
        
        # التحقق من إضافة العمود
        verify_query = """
        SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE
        FROM USER_TAB_COLUMNS 
        WHERE TABLE_NAME = 'PURCHASE_ORDERS' 
        AND COLUMN_NAME = 'SUPPLIER_INVOICE_NUMBER'
        """
        
        verify_result = oracle_manager.execute_query(verify_query)
        
        if verify_result:
            column_info = verify_result[0]
            logger.info(f"✅ تم التحقق من العمود الجديد:")
            logger.info(f"   اسم العمود: {column_info[0]}")
            logger.info(f"   نوع البيانات: {column_info[1]}")
            logger.info(f"   الطول: {column_info[2]}")
            logger.info(f"   يقبل NULL: {column_info[3]}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في إضافة عمود رقم فاتورة المورد: {e}")
        return False
        
    finally:
        if oracle_manager:
            oracle_manager.disconnect()
            logger.info("🔌 تم قطع الاتصال بقاعدة البيانات")

def test_supplier_invoice_number_functionality():
    """اختبار وظائف رقم فاتورة المورد"""
    
    oracle_manager = None
    try:
        oracle_manager = OracleManager()
        oracle_manager.connect()
        
        logger.info("🧪 اختبار وظائف رقم فاتورة المورد...")
        
        # اختبار إدراج قيمة تجريبية
        test_po_number = "TEST-SUPPLIER-INV-001"
        
        # حذف أي بيانات تجريبية سابقة
        delete_test_query = "DELETE FROM PURCHASE_ORDERS WHERE PO_NUMBER = :1"
        oracle_manager.execute_update(delete_test_query, [test_po_number])
        
        # إدراج أمر شراء تجريبي مع رقم فاتورة المورد
        insert_test_query = """
        INSERT INTO PURCHASE_ORDERS (
            PO_NUMBER, SUPPLIER_NAME, TITLE, PO_DATE, 
            SUPPLIER_INVOICE_NUMBER, STATUS, CREATED_BY
        ) VALUES (
            :1, :2, :3, SYSDATE, :4, :5, :6
        )
        """
        
        test_params = [
            test_po_number,
            "مورد تجريبي",
            "أمر شراء تجريبي لاختبار رقم فاتورة المورد",
            "INV-SUPPLIER-2025-001",
            "مسودة",
            "SYSTEM"
        ]
        
        oracle_manager.execute_update(insert_test_query, test_params)
        
        logger.info("✅ تم إدراج أمر شراء تجريبي مع رقم فاتورة المورد")
        
        # استعلام للتحقق من البيانات
        select_test_query = """
        SELECT PO_NUMBER, SUPPLIER_NAME, SUPPLIER_INVOICE_NUMBER
        FROM PURCHASE_ORDERS 
        WHERE PO_NUMBER = :1
        """
        
        result = oracle_manager.execute_query(select_test_query, [test_po_number])
        
        if result:
            po_data = result[0]
            logger.info(f"📋 بيانات أمر الشراء التجريبي:")
            logger.info(f"   رقم أمر الشراء: {po_data[0]}")
            logger.info(f"   اسم المورد: {po_data[1]}")
            logger.info(f"   رقم فاتورة المورد: {po_data[2]}")
        
        # تحديث رقم فاتورة المورد
        update_test_query = """
        UPDATE PURCHASE_ORDERS 
        SET SUPPLIER_INVOICE_NUMBER = :1 
        WHERE PO_NUMBER = :2
        """
        
        new_invoice_number = "INV-SUPPLIER-2025-001-UPDATED"
        oracle_manager.execute_update(update_test_query, [new_invoice_number, test_po_number])
        
        logger.info("✅ تم تحديث رقم فاتورة المورد")
        
        # التحقق من التحديث
        updated_result = oracle_manager.execute_query(select_test_query, [test_po_number])
        
        if updated_result:
            updated_data = updated_result[0]
            logger.info(f"📋 بيانات أمر الشراء بعد التحديث:")
            logger.info(f"   رقم فاتورة المورد الجديد: {updated_data[2]}")
        
        # حذف البيانات التجريبية
        oracle_manager.execute_update(delete_test_query, [test_po_number])
        
        logger.info("🗑️ تم حذف البيانات التجريبية")
        logger.info("✅ اختبار وظائف رقم فاتورة المورد مكتمل بنجاح")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار وظائف رقم فاتورة المورد: {e}")
        return False
        
    finally:
        if oracle_manager:
            oracle_manager.disconnect()

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    print("\n" + "="*60)
    print("📋 تعليمات استخدام حقل رقم فاتورة المورد")
    print("="*60)
    
    print("\n🎯 الغرض من الحقل:")
    print("• تسجيل رقم الفاتورة الصادرة من المورد")
    print("• ربط أمر الشراء بفاتورة المورد الفعلية")
    print("• تسهيل عمليات المتابعة والمراجعة")
    print("• تحسين إدارة المستندات")
    
    print("\n📝 كيفية الاستخدام:")
    print("1. في نافذة إنشاء أمر شراء جديد:")
    print("   • ابحث عن حقل 'رقم فاتورة المورد'")
    print("   • أدخل رقم الفاتورة (اختياري)")
    print("   • احفظ أمر الشراء")
    
    print("\n2. في نافذة تحديث أمر الشراء:")
    print("   • يمكن تحديث رقم فاتورة المورد في أي وقت")
    print("   • الحقل اختياري ولا يؤثر على حفظ أمر الشراء")
    
    print("\n3. في صفحة عرض تفاصيل أمر الشراء:")
    print("   • سيظهر رقم فاتورة المورد في قسم 'معلومات المورد'")
    print("   • إذا لم يكن محدد سيظهر 'غير محدد'")
    
    print("\n✅ المزايا:")
    print("• تتبع أفضل للفواتير")
    print("• سهولة في المراجعة والتدقيق")
    print("• ربط واضح بين أوامر الشراء والفواتير")
    print("• تحسين إدارة المستندات المالية")

if __name__ == "__main__":
    print("🚀 بدء إضافة حقل رقم فاتورة المورد إلى نظام أوامر الشراء")
    print("="*70)
    
    # إضافة العمود
    success = add_supplier_invoice_number_column()
    
    if success:
        print("\n✅ تم إضافة العمود بنجاح!")
        
        # اختبار الوظائف
        test_success = test_supplier_invoice_number_functionality()
        
        if test_success:
            print("\n🎉 تم إنجاز جميع العمليات بنجاح!")
            
            # عرض تعليمات الاستخدام
            show_usage_instructions()
            
            print("\n🎯 الخطوات التالية:")
            print("1. أعد تشغيل الخادم")
            print("2. اذهب إلى صفحة إنشاء أمر شراء جديد")
            print("3. ستجد حقل 'رقم فاتورة المورد' الجديد")
            print("4. جرب إنشاء أمر شراء مع رقم فاتورة المورد")
            
        else:
            print("\n❌ فشل في اختبار الوظائف")
    else:
        print("\n❌ فشل في إضافة العمود")
        print("💡 تحقق من اتصال قاعدة البيانات والأذونات")
