{% extends "base.html" %}

{% block title %}الإعدادات والتهيئة{% endblock %}

{% block extra_css %}
<style>
    .settings-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        padding: 30px;
        color: white;
        margin-bottom: 30px;
    }
    
    .settings-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .settings-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }
    
    .settings-icon {
        font-size: 3rem;
        color: #667eea;
        margin-bottom: 20px;
    }
    
    .settings-title {
        font-size: 1.5rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 10px;
    }
    
    .settings-description {
        color: #7f8c8d;
        margin-bottom: 15px;
    }
    
    .settings-badge {
        background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        color: white;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-left: 8px;
    }
    
    .status-online {
        background-color: #2ecc71;
        animation: pulse-green 2s infinite;
    }
    
    .status-offline {
        background-color: #e74c3c;
    }
    
    @keyframes pulse-green {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="settings-container">
                <div class="text-center">
                    <h1><i class="fas fa-cogs"></i> الإعدادات والتهيئة</h1>
                    <p class="lead">إدارة شاملة لجميع إعدادات النظام</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- الإعدادات العامة -->
        <div class="col-md-4 col-sm-6">
            <div class="settings-card" onclick="location.href='{{ url_for('settings.general_settings') }}'">
                <div class="text-center">
                    <i class="fas fa-sliders-h settings-icon"></i>
                    <h4 class="settings-title">الإعدادات العامة</h4>
                    <p class="settings-description">
                        اللغة، العملة، المنطقة الزمنية، وإعدادات العرض
                    </p>
                    <span class="settings-badge">أساسي</span>
                </div>
            </div>
        </div>
        
        <!-- إعدادات قاعدة البيانات -->
        <div class="col-md-4 col-sm-6">
            <div class="settings-card" onclick="location.href='{{ url_for('settings.database_settings') }}'">
                <div class="text-center">
                    <i class="fas fa-database settings-icon"></i>
                    <h4 class="settings-title">قاعدة البيانات</h4>
                    <p class="settings-description">
                        إعدادات الاتصال، النسخ الاحتياطي، والصيانة
                    </p>
                    <span class="settings-badge">
                        متصل <span class="status-indicator status-online"></span>
                    </span>
                </div>
            </div>
        </div>
        
        <!-- إعدادات الأمان -->
        <div class="col-md-4 col-sm-6">
            <div class="settings-card" onclick="location.href='{{ url_for('settings.security_settings') }}'">
                <div class="text-center">
                    <i class="fas fa-shield-alt settings-icon"></i>
                    <h4 class="settings-title">الأمان والحماية</h4>
                    <p class="settings-description">
                        كلمات المرور، الجلسات، والصلاحيات
                    </p>
                    <span class="settings-badge">حماية</span>
                </div>
            </div>
        </div>
        
        <!-- النسخ الاحتياطي -->
        <div class="col-md-4 col-sm-6">
            <div class="settings-card" onclick="location.href='{{ url_for('settings.backup_settings') }}'">
                <div class="text-center">
                    <i class="fas fa-cloud-upload-alt settings-icon"></i>
                    <h4 class="settings-title">النسخ الاحتياطي</h4>
                    <p class="settings-description">
                        إنشاء واستعادة النسخ الاحتياطية
                    </p>
                    <span class="settings-badge">تلقائي</span>
                </div>
            </div>
        </div>
        
        <!-- معلومات النظام -->
        <div class="col-md-4 col-sm-6">
            <div class="settings-card" onclick="location.href='{{ url_for('settings.system_info') }}'">
                <div class="text-center">
                    <i class="fas fa-info-circle settings-icon"></i>
                    <h4 class="settings-title">معلومات النظام</h4>
                    <p class="settings-description">
                        حالة الخادم، الذاكرة، والأداء
                    </p>
                    <span class="settings-badge">مراقبة</span>
                </div>
            </div>
        </div>
        
        <!-- أدوات الصيانة -->
        <div class="col-md-4 col-sm-6">
            <div class="settings-card" onclick="showMaintenanceTools()">
                <div class="text-center">
                    <i class="fas fa-tools settings-icon"></i>
                    <h4 class="settings-title">أدوات الصيانة</h4>
                    <p class="settings-description">
                        مسح التخزين المؤقت، تحسين الأداء
                    </p>
                    <span class="settings-badge">أدوات</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات سريعة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> إحصائيات سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="quickStats">
                        <div class="col-md-3 text-center">
                            <h4 class="text-primary" id="systemUptime">--</h4>
                            <small class="text-muted">وقت التشغيل</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-success" id="memoryUsage">--</h4>
                            <small class="text-muted">استخدام الذاكرة</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-info" id="diskUsage">--</h4>
                            <small class="text-muted">استخدام القرص</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-warning" id="dbStatus">--</h4>
                            <small class="text-muted">حالة قاعدة البيانات</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal أدوات الصيانة -->
<div class="modal fade" id="maintenanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-tools"></i> أدوات الصيانة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-warning" onclick="clearCache()">
                        <i class="fas fa-broom"></i> مسح التخزين المؤقت
                    </button>
                    <button class="btn btn-info" onclick="optimizeDatabase()">
                        <i class="fas fa-database"></i> تحسين قاعدة البيانات
                    </button>
                    <button class="btn btn-success" onclick="createBackup()">
                        <i class="fas fa-save"></i> إنشاء نسخة احتياطية
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحميل الإحصائيات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadQuickStats();
    
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(loadQuickStats, 30000);
});

// تحميل الإحصائيات السريعة
async function loadQuickStats() {
    try {
        const response = await fetch('/settings/api/system-status');
        const data = await response.json();
        
        if (data.success) {
            document.getElementById('memoryUsage').textContent = `${data.memory.percentage}%`;
            document.getElementById('diskUsage').textContent = `${data.disk.percentage}%`;
            document.getElementById('systemUptime').textContent = 'متصل';
            document.getElementById('dbStatus').textContent = 'نشط';
        }
    } catch (error) {
        console.error('خطأ في تحميل الإحصائيات:', error);
    }
}

// عرض أدوات الصيانة
function showMaintenanceTools() {
    const modal = new bootstrap.Modal(document.getElementById('maintenanceModal'));
    modal.show();
}

// مسح التخزين المؤقت
async function clearCache() {
    try {
        const response = await fetch('/settings/api/clear-cache', {
            method: 'POST'
        });
        const data = await response.json();
        
        if (data.success) {
            alert('تم مسح التخزين المؤقت بنجاح');
        } else {
            alert('خطأ: ' + data.message);
        }
    } catch (error) {
        alert('خطأ في مسح التخزين المؤقت');
    }
}

// تحسين قاعدة البيانات
function optimizeDatabase() {
    alert('ميزة تحسين قاعدة البيانات قيد التطوير');
}

// إنشاء نسخة احتياطية
async function createBackup() {
    try {
        const response = await fetch('/settings/api/backup-database', {
            method: 'POST'
        });
        const data = await response.json();
        
        if (data.success) {
            alert('تم إنشاء النسخة الاحتياطية بنجاح: ' + data.backup_file);
        } else {
            alert('خطأ: ' + data.message);
        }
    } catch (error) {
        alert('خطأ في إنشاء النسخة الاحتياطية');
    }
}
</script>
{% endblock %}
