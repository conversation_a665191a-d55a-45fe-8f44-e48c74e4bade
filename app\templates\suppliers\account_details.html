{% extends "base.html" %}

{% block title %}تفاصيل حساب المورد{% endblock %}

{% block extra_css %}
<style>
    .account-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
    }
    
    .account-info-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    }
    
    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
    }
    
    .info-item:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 500;
        color: #666;
    }
    
    .info-value {
        font-weight: 600;
        color: #333;
    }
    
    .balance-positive { color: #28a745; }
    .balance-negative { color: #dc3545; }
    .balance-zero { color: #6c757d; }
    
    .status-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }
    
    .status-active { background: #d4edda; color: #155724; }
    .status-suspended { background: #f8d7da; color: #721c24; }
    .status-closed { background: #d1ecf1; color: #0c5460; }
    
    .risk-badge {
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .risk-low { background: #d4edda; color: #155724; }
    .risk-medium { background: #fff3cd; color: #856404; }
    .risk-high { background: #f8d7da; color: #721c24; }
    .risk-critical { background: #f5c6cb; color: #491217; }
    
    .transactions-table {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    }
    
    .transaction-row {
        padding: 15px;
        border-bottom: 1px solid #eee;
        transition: background-color 0.3s ease;
    }
    
    .transaction-row:hover {
        background-color: #f8f9fa;
    }
    
    .transaction-row:last-child {
        border-bottom: none;
    }
    
    .transaction-type {
        font-weight: 600;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
    }
    
    .type-invoice { background: #fff3cd; color: #856404; }
    .type-payment { background: #d4edda; color: #155724; }
    .type-credit { background: #d1ecf1; color: #0c5460; }
    .type-debit { background: #f8d7da; color: #721c24; }
    
    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        height: 400px;
    }
    
    .chart-wrapper {
        position: relative;
        height: 300px;
        width: 100%;
    }
    
    .action-buttons {
        margin-top: 20px;
    }
    
    .btn-custom {
        border-radius: 25px;
        padding: 10px 25px;
        font-weight: 500;
        margin-right: 10px;
        margin-bottom: 10px;
    }
    
    .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Account Header -->
    <div class="account-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 id="accountName">جاري التحميل...</h2>
                <p id="accountCode" class="mb-2">كود المورد: ---</p>
                <p id="accountNumber" class="mb-0">رقم الحساب: ---</p>
            </div>
            <div class="col-md-4 text-end">
                <div id="accountStatus" class="mb-2">
                    <span class="status-badge status-active">نشط</span>
                </div>
                <div id="riskRating">
                    <span class="risk-badge risk-low">منخفض المخاطر</span>
                </div>
            </div>
        </div>
        
        <div class="action-buttons">
            <button class="btn btn-light btn-custom" onclick="editAccount()">
                <i class="fas fa-edit"></i> تعديل الحساب
            </button>
            <button class="btn btn-light btn-custom" onclick="generateStatement()">
                <i class="fas fa-file-alt"></i> كشف حساب
            </button>
            <button class="btn btn-light btn-custom" onclick="createPayment()">
                <i class="fas fa-money-bill"></i> إنشاء دفعة
            </button>
            <button class="btn btn-light btn-custom" onclick="viewReconciliation()">
                <i class="fas fa-balance-scale"></i> المطابقة
            </button>
        </div>
    </div>

    <!-- Account Information -->
    <div class="row">
        <div class="col-md-6">
            <div class="account-info-card">
                <h5><i class="fas fa-info-circle"></i> معلومات الحساب</h5>
                <div id="accountInfo">
                    <div class="loading-container">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="account-info-card">
                <h5><i class="fas fa-chart-bar"></i> إحصائيات الحساب</h5>
                <div id="accountStats">
                    <div class="loading-container">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Balance Trends Chart -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5><i class="fas fa-chart-line"></i> اتجاهات الرصيد (آخر 6 أشهر)</h5>
                <div class="chart-wrapper">
                    <canvas id="balanceTrendsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="row">
        <div class="col-12">
            <div class="transactions-table">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-list"></i> المعاملات الأخيرة</h5>
                    <div>
                        <button class="btn btn-outline-primary btn-sm" onclick="loadAllTransactions()">
                            عرض جميع المعاملات
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="addTransaction()">
                            <i class="fas fa-plus"></i> إضافة معاملة
                        </button>
                    </div>
                </div>
                <div id="transactionsList">
                    <div class="loading-container">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Transaction Modal -->
<div class="modal fade" id="addTransactionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة معاملة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addTransactionForm">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">نوع المعاملة *</label>
                            <select class="form-select" id="transactionType" required>
                                <option value="">اختر نوع المعاملة</option>
                                <option value="INVOICE">فاتورة</option>
                                <option value="PAYMENT">دفعة</option>
                                <option value="CREDIT_NOTE">إشعار دائن</option>
                                <option value="DEBIT_NOTE">إشعار مدين</option>
                                <option value="ADJUSTMENT">تسوية</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">المبلغ *</label>
                            <input type="number" class="form-control" id="transactionAmount" step="0.01" required>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">تاريخ المعاملة *</label>
                            <input type="date" class="form-control" id="transactionDate" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تاريخ الاستحقاق</label>
                            <input type="date" class="form-control" id="dueDate">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">العملة</label>
                            <select class="form-select" id="transactionCurrency">
                                <option value="SAR">ريال سعودي</option>
                                <option value="USD">دولار أمريكي</option>
                                <option value="EUR">يورو</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم المرجع</label>
                            <input type="text" class="form-control" id="referenceNumber">
                        </div>
                    </div>
                    <div class="mt-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" id="transactionDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveTransaction()">حفظ المعاملة</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script src="{{ url_for('static', filename='js/account_details.js') }}"></script>
<script>
$(document).ready(function() {
    // الحصول على معرف الحساب من URL
    const accountId = {{ account_id }};
    
    // تحميل تفاصيل الحساب
    loadAccountDetails(accountId);
    
    // تعيين التاريخ الافتراضي
    $('#transactionDate').val(new Date().toISOString().split('T')[0]);
});
</script>
{% endblock %}
