-- إنشاء نظام إدارة جهات الاتصال للإشعارات
-- Notification Contacts Management System

-- 1. جدول جهات الاتصال للإشعارات
CREATE TABLE notification_contacts (
    id NUMBER PRIMARY KEY,
    contact_name VARCHAR2(200) NOT NULL,
    contact_type VARCHAR2(50) NOT NULL, -- CUSTOMER, DRIVER, AGENT, MANAGER, EXTERNAL
    
    -- معلومات الاتصال
    phone_number VARCHAR2(20),
    email_address VARCHAR2(200),
    whatsapp_number VARCHAR2(20),
    
    -- معلومات إضافية
    company_name VARCHAR2(200),
    department VARCHAR2(100),
    position VARCHAR2(100),
    notes CLOB,
    
    -- إعد<PERSON><PERSON><PERSON> الإشعارات
    preferred_channels VARCHAR2(200), -- SMS,EMAIL,WHATSAPP,PUSH
    notification_preferences CLOB, -- JSON: أنواع الإشعارات المفضلة
    timezone VARCHAR2(50) DEFAULT 'Asia/Riyadh',
    language_preference VARCHAR2(10) DEFAULT 'ar',
    
    -- حالة جهة الاتصال
    is_active NUMBER(1) DEFAULT 1,
    is_vip NUMBER(1) DEFAULT 0,
    priority_level NUMBER DEFAULT 5, -- 1-10 (10 = أولوية عالية)
    
    -- معلومات النظام
    created_at DATE DEFAULT SYSDATE,
    created_by NUMBER,
    updated_at DATE DEFAULT SYSDATE,
    updated_by NUMBER,
    
    -- فهرسة
    CONSTRAINT notification_contacts_pk PRIMARY KEY (id),
    CONSTRAINT notification_contacts_phone_uk UNIQUE (phone_number),
    CONSTRAINT notification_contacts_email_uk UNIQUE (email_address)
);

-- 2. إنشاء sequence
CREATE SEQUENCE notification_contacts_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 3. جدول مجموعات جهات الاتصال
CREATE TABLE notification_contact_groups (
    id NUMBER PRIMARY KEY,
    group_name VARCHAR2(200) NOT NULL,
    group_description CLOB,
    group_type VARCHAR2(50), -- DEPARTMENT, PROJECT, EMERGENCY, CUSTOM
    
    -- إعدادات المجموعة
    default_channels VARCHAR2(200),
    notification_schedule CLOB, -- JSON: أوقات الإرسال المفضلة
    
    -- حالة المجموعة
    is_active NUMBER(1) DEFAULT 1,
    created_at DATE DEFAULT SYSDATE,
    created_by NUMBER
);

-- 4. إنشاء sequence للمجموعات
CREATE SEQUENCE notification_contact_groups_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 5. جدول ربط جهات الاتصال بالمجموعات
CREATE TABLE notification_contact_group_members (
    id NUMBER PRIMARY KEY,
    contact_id NUMBER NOT NULL,
    group_id NUMBER NOT NULL,
    role_in_group VARCHAR2(50), -- MEMBER, ADMIN, MODERATOR
    added_at DATE DEFAULT SYSDATE,
    added_by NUMBER,
    
    CONSTRAINT ncgm_contact_fk FOREIGN KEY (contact_id) REFERENCES notification_contacts(id),
    CONSTRAINT ncgm_group_fk FOREIGN KEY (group_id) REFERENCES notification_contact_groups(id),
    CONSTRAINT ncgm_unique UNIQUE (contact_id, group_id)
);

-- 6. إنشاء sequence لأعضاء المجموعات
CREATE SEQUENCE notification_contact_group_members_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 7. جدول قوالب الإشعارات المخصصة
CREATE TABLE notification_custom_templates (
    id NUMBER PRIMARY KEY,
    template_name VARCHAR2(200) NOT NULL,
    template_category VARCHAR2(50), -- DELIVERY, SHIPMENT, SYSTEM, REMINDER, CUSTOM
    
    -- محتوى القوالب
    sms_template CLOB,
    email_subject VARCHAR2(500),
    email_body CLOB,
    whatsapp_template CLOB,
    
    -- إعدادات القالب
    default_channels VARCHAR2(200),
    priority_level NUMBER DEFAULT 5,
    is_active NUMBER(1) DEFAULT 1,
    
    -- متغيرات القالب
    template_variables CLOB, -- JSON: قائمة المتغيرات المتاحة
    
    created_at DATE DEFAULT SYSDATE,
    created_by NUMBER
);

-- 8. إنشاء sequence للقوالب المخصصة
CREATE SEQUENCE notification_custom_templates_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 9. إدراج بيانات تجريبية
INSERT INTO notification_contacts (id, contact_name, contact_type, phone_number, email_address, whatsapp_number, preferred_channels, is_vip, priority_level) VALUES 
(notification_contacts_seq.NEXTVAL, 'أحمد محمد - مدير العمليات', 'MANAGER', '+966501234567', '<EMAIL>', '+966501234567', 'SMS,EMAIL,WHATSAPP', 1, 10);

INSERT INTO notification_contacts (id, contact_name, contact_type, phone_number, email_address, preferred_channels, priority_level) VALUES 
(notification_contacts_seq.NEXTVAL, 'فاطمة أحمد - خدمة العملاء', 'MANAGER', '+966507654321', '<EMAIL>', 'EMAIL,SMS', 8);

INSERT INTO notification_contacts (id, contact_name, contact_type, phone_number, whatsapp_number, preferred_channels, priority_level) VALUES 
(notification_contacts_seq.NEXTVAL, 'محمد علي - سائق', 'DRIVER', '+966512345678', '+966512345678', 'SMS,WHATSAPP', 6);

INSERT INTO notification_contacts (id, contact_name, contact_type, phone_number, email_address, preferred_channels, priority_level) VALUES 
(notification_contacts_seq.NEXTVAL, 'سارة خالد - عميل VIP', 'CUSTOMER', '+966598765432', '<EMAIL>', 'EMAIL,SMS', 9);

INSERT INTO notification_contacts (id, contact_name, contact_type, phone_number, preferred_channels, priority_level) VALUES 
(notification_contacts_seq.NEXTVAL, 'عبدالله أحمد - مخلص جمركي', 'AGENT', '+966556789012', '<EMAIL>', 'SMS,EMAIL', 7);

-- 10. إنشاء مجموعات تجريبية
INSERT INTO notification_contact_groups (id, group_name, group_description, group_type, default_channels) VALUES 
(notification_contact_groups_seq.NEXTVAL, 'الإدارة العليا', 'مجموعة المديرين وصناع القرار', 'DEPARTMENT', 'EMAIL,SMS,WHATSAPP');

INSERT INTO notification_contact_groups (id, group_name, group_description, group_type, default_channels) VALUES 
(notification_contact_groups_seq.NEXTVAL, 'السائقين النشطين', 'مجموعة السائقين المتاحين للعمل', 'DEPARTMENT', 'SMS,WHATSAPP');

INSERT INTO notification_contact_groups (id, group_name, group_description, group_type, default_channels) VALUES 
(notification_contact_groups_seq.NEXTVAL, 'العملاء المميزين', 'مجموعة العملاء ذوي الأولوية العالية', 'CUSTOM', 'EMAIL,SMS');

INSERT INTO notification_contact_groups (id, group_name, group_description, group_type, default_channels) VALUES 
(notification_contact_groups_seq.NEXTVAL, 'الطوارئ', 'مجموعة الاتصال في حالات الطوارئ', 'EMERGENCY', 'SMS,WHATSAPP,EMAIL');

-- 11. ربط جهات الاتصال بالمجموعات
INSERT INTO notification_contact_group_members (id, contact_id, group_id, role_in_group) VALUES 
(notification_contact_group_members_seq.NEXTVAL, 1, 1, 'ADMIN'); -- أحمد في الإدارة العليا

INSERT INTO notification_contact_group_members (id, contact_id, group_id, role_in_group) VALUES 
(notification_contact_group_members_seq.NEXTVAL, 2, 1, 'MEMBER'); -- فاطمة في الإدارة العليا

INSERT INTO notification_contact_group_members (id, contact_id, group_id, role_in_group) VALUES 
(notification_contact_group_members_seq.NEXTVAL, 3, 2, 'MEMBER'); -- محمد في السائقين

INSERT INTO notification_contact_group_members (id, contact_id, group_id, role_in_group) VALUES 
(notification_contact_group_members_seq.NEXTVAL, 4, 3, 'MEMBER'); -- سارة في العملاء المميزين

INSERT INTO notification_contact_group_members (id, contact_id, group_id, role_in_group) VALUES 
(notification_contact_group_members_seq.NEXTVAL, 1, 4, 'ADMIN'); -- أحمد في الطوارئ

-- 12. إنشاء فهارس للأداء
CREATE INDEX idx_notification_contacts_type ON notification_contacts(contact_type);
CREATE INDEX idx_notification_contacts_active ON notification_contacts(is_active);
CREATE INDEX idx_notification_contacts_vip ON notification_contacts(is_vip);
CREATE INDEX idx_notification_contacts_priority ON notification_contacts(priority_level);

-- 13. إنشاء view للاستعلامات السريعة
CREATE OR REPLACE VIEW v_notification_contacts_summary AS
SELECT 
    nc.id,
    nc.contact_name,
    nc.contact_type,
    nc.phone_number,
    nc.email_address,
    nc.whatsapp_number,
    nc.preferred_channels,
    nc.is_vip,
    nc.priority_level,
    nc.is_active,
    COUNT(ncgm.group_id) as groups_count
FROM notification_contacts nc
LEFT JOIN notification_contact_group_members ncgm ON nc.id = ncgm.contact_id
WHERE nc.is_active = 1
GROUP BY nc.id, nc.contact_name, nc.contact_type, nc.phone_number, 
         nc.email_address, nc.whatsapp_number, nc.preferred_channels, 
         nc.is_vip, nc.priority_level, nc.is_active
ORDER BY nc.priority_level DESC, nc.contact_name;

COMMIT;
