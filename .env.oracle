# إعدادات Oracle Database للنظام المحاسبي المتقدم
# Oracle Database Configuration for Advanced Accounting System

# بيئة التشغيل (development, testing, production)
FLASK_ENV=development
FLASK_CONFIG=development

# المفتاح السري للتطبيق
SECRET_KEY=مفتاح-سري-للنظام-المحاسبي-Oracle-2024-غير-هذا-في-الإنتاج

# ===== إعدادات Oracle Database =====

# إعدادات التطوير - Development Oracle Settings
ORACLE_HOST=localhost
ORACLE_PORT=1521
ORACLE_SID=ORCL
ORACLE_USERNAME=accounting_user
ORACLE_PASSWORD=accounting_password

# إعدادات التطوير البديلة
DEV_ORACLE_HOST=localhost
DEV_ORACLE_SID=ORCL
DEV_ORACLE_USERNAME=accounting_dev
DEV_ORACLE_PASSWORD=dev_password

# إعدادات الاختبار - Testing Oracle Settings
TEST_ORACLE_SID=ORCL
TEST_ORACLE_USERNAME=accounting_test
TEST_ORACLE_PASSWORD=test_password

# إعدادات الإنتاج - Production Oracle Settings (يجب تعيينها في الإنتاج)
# PROD_ORACLE_HOST=production-oracle-server.company.com
# PROD_ORACLE_PORT=1521
# PROD_ORACLE_SID=ORCL
# PROD_ORACLE_USERNAME=accounting_prod
# PROD_ORACLE_PASSWORD=secure_production_password

# ===== إعدادات البريد الإلكتروني =====
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# بريد المدير للإشعارات
ADMIN_EMAIL=<EMAIL>

# ===== إعدادات الأمان =====
WTF_CSRF_ENABLED=true
WTF_CSRF_TIME_LIMIT=3600

# إعدادات الجلسة
PERMANENT_SESSION_LIFETIME=28800

# ===== إعدادات الملفات =====
MAX_CONTENT_LENGTH=********
UPLOAD_FOLDER=uploads
REPORTS_FOLDER=reports
BACKUP_FOLDER=backups

# ===== إعدادات اللغة والمنطقة =====
DEFAULT_LANGUAGE=ar
BABEL_DEFAULT_LOCALE=ar
BABEL_DEFAULT_TIMEZONE=Asia/Riyadh

# ===== إعدادات العملة =====
DEFAULT_CURRENCY=SAR
CURRENCY_SYMBOL=ر.س

# ===== إعدادات التطوير =====
DEBUG=true
TESTING=false

# ===== إعدادات التسجيل =====
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# ===== إعدادات Oracle Client =====
# مسار Oracle Instant Client (إذا لزم الأمر)
# ORACLE_HOME=/opt/oracle/instantclient_21_1
# LD_LIBRARY_PATH=/opt/oracle/instantclient_21_1

# ===== إعدادات الاتصال المتقدمة =====
# ORACLE_POOL_SIZE=10
# ORACLE_MAX_OVERFLOW=20
# ORACLE_POOL_TIMEOUT=30
# ORACLE_POOL_RECYCLE=3600

# ===== ملاحظات مهمة =====
# 1. تأكد من تثبيت Oracle Instant Client
# 2. تأكد من إنشاء المستخدم والصلاحيات في Oracle
# 3. تأكد من فتح المنافذ المطلوبة
# 4. في الإنتاج، استخدم كلمات مرور قوية ومشفرة
# 5. تأكد من إعداد النسخ الاحتياطية المنتظمة

# ===== أوامر إنشاء المستخدم في Oracle =====
# -- تشغيل هذه الأوامر كـ SYSDBA
# CREATE USER accounting_user IDENTIFIED BY accounting_password;
# GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SEQUENCE TO accounting_user;
# GRANT UNLIMITED TABLESPACE TO accounting_user;
# 
# -- للتطوير
# CREATE USER accounting_dev IDENTIFIED BY dev_password;
# GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SEQUENCE TO accounting_dev;
# GRANT UNLIMITED TABLESPACE TO accounting_dev;
# 
# -- للاختبار
# CREATE USER accounting_test IDENTIFIED BY test_password;
# GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SEQUENCE TO accounting_test;
# GRANT UNLIMITED TABLESPACE TO accounting_test;
