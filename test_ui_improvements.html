
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحسينات واجهة المستخدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <style>
        /* تنسيق Header المتقدم */
        .header-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: #6c757d;
            font-weight: bold;
        }

        .breadcrumb-item a {
            color: #495057;
            transition: color 0.3s ease;
        }

        .breadcrumb-item a:hover {
            color: #007bff;
        }

        .breadcrumb-item.active {
            color: #6c757d;
            font-weight: 500;
        }
        
        /* تنسيق الفلاتر القابلة للطي */
        #filtersContent {
            transition: all 0.3s ease-in-out;
        }
        
        #filtersContent.collapse:not(.show) {
            display: none;
        }
        
        #filtersContent.collapse.show {
            display: block;
        }
        
        /* تحسين زر التبديل */
        #toggleFiltersBtn {
            transition: all 0.3s ease;
            border-radius: 20px;
        }
        
        #toggleFiltersBtn:hover {
            background-color: #f8f9fa;
            border-color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header المتقدم -->
        <div class="row mb-4">
            <div class="col-12">
                <!-- Header الرئيسي -->
                <div class="card border-0 shadow-sm mb-4 header-gradient">
                    <div class="card-body text-white">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="me-3">
                                        <i class="fas fa-chart-line fa-3x opacity-75"></i>
                                    </div>
                                    <div>
                                        <h1 class="h2 mb-1 fw-bold">تحليل أصناف أوامر الشراء</h1>
                                        <p class="mb-0 opacity-90">تحليل شامل ومتقدم لجميع الأصناف الفعلية في أوامر الشراء مع إحصائيات تفصيلية ورسوم بيانية</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="d-flex flex-column align-items-end">
                                    <div class="mb-2">
                                        <span class="badge bg-light text-dark px-3 py-2">
                                            <i class="fas fa-database me-1"></i>
                                            بيانات فعلية
                                        </span>
                                    </div>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-light btn-sm" onclick="exportToExcel()">
                                            <i class="fas fa-file-excel me-1"></i>
                                            Excel
                                        </button>
                                        <button class="btn btn-outline-light btn-sm" onclick="exportToPDF()">
                                            <i class="fas fa-file-pdf me-1"></i>
                                            PDF
                                        </button>
                                        <button class="btn btn-outline-light btn-sm" onclick="refreshData()">
                                            <i class="fas fa-sync-alt me-1"></i>
                                            تحديث
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- شريط التنقل السريع -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body py-2">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item">
                                    <a href="#" class="text-decoration-none">
                                        <i class="fas fa-home me-1"></i>
                                        الرئيسية
                                    </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <a href="#" class="text-decoration-none">
                                        <i class="fas fa-shopping-cart me-1"></i>
                                        أوامر الشراء
                                    </a>
                                </li>
                                <li class="breadcrumb-item active" aria-current="page">
                                    <i class="fas fa-chart-line me-1"></i>
                                    تحليل الأصناف
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإحصائيات السريعة -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="rounded-circle bg-primary bg-opacity-10 p-3 me-3">
                                <i class="fas fa-shopping-cart text-primary fs-4"></i>
                            </div>
                            <div>
                                <h3 class="mb-0 text-primary">8</h3>
                                <small class="text-muted">إجمالي أوامر الشراء</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="rounded-circle bg-success bg-opacity-10 p-3 me-3">
                                <i class="fas fa-boxes text-success fs-4"></i>
                            </div>
                            <div>
                                <h3 class="mb-0 text-success">13</h3>
                                <small class="text-muted">إجمالي الأصناف</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="rounded-circle bg-info bg-opacity-10 p-3 me-3">
                                <i class="fas fa-coins text-info fs-4"></i>
                            </div>
                            <div>
                                <h3 class="mb-0 text-info">¥2,845,422</h3>
                                <small class="text-muted">إجمالي القيمة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="rounded-circle bg-warning bg-opacity-10 p-3 me-3">
                                <i class="fas fa-chart-bar text-warning fs-4"></i>
                            </div>
                            <div>
                                <h3 class="mb-0 text-warning">¥355,678</h3>
                                <small class="text-muted">متوسط قيمة الأمر</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الفلاتر مع زر الإخفاء/الإظهار -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر البحث والتصفية
                    </h5>
                    <button class="btn btn-outline-secondary btn-sm" type="button" id="toggleFiltersBtn" onclick="toggleFilters()">
                        <i class="fas fa-eye me-1" id="toggleFiltersIcon"></i>
                        <span id="toggleFiltersText">إظهار الفلاتر</span>
                    </button>
                </div>
            </div>
            <div class="card-body collapse" id="filtersContent">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>اختبار الفلاتر:</strong> الفلاتر مخفية افتراضياً. اضغط على "إظهار الفلاتر" لرؤيتها.
                </div>
                <form>
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">المورد</label>
                            <select class="form-select">
                                <option value="">جميع الموردين</option>
                                <option value="شركة رايسن">شركة رايسن</option>
                                <option value="شركة يونجي-الصين">شركة يونجي-الصين</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الحالة</label>
                            <select class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="تم التسليم">تم التسليم</option>
                                <option value="مسودة">مسودة</option>
                            </select>
                        </div>
                        <div class="col-md-8">
                            <label class="form-label">البحث في الأصناف</label>
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="ابحث في أسماء الأصناف أو الأكواد...">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="fas fa-microphone"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="button" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>
                                بحث
                            </button>
                            <button type="button" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>
                                مسح
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- رسالة اختبار -->
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <h4>🎨 اختبار تحسينات واجهة المستخدم</h4>
                <p class="text-muted">تم تطبيق التحسينات التالية:</p>
                <div class="row">
                    <div class="col-md-6">
                        <div class="alert alert-success">
                            <h6>✅ Header متقدم</h6>
                            <ul class="list-unstyled mb-0">
                                <li>• تدرج لوني جميل</li>
                                <li>• أزرار تصدير وتحديث</li>
                                <li>• شريط تنقل (Breadcrumb)</li>
                                <li>• تصميم احترافي</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-info">
                            <h6>👁️ فلاتر قابلة للإخفاء</h6>
                            <ul class="list-unstyled mb-0">
                                <li>• مخفية افتراضياً</li>
                                <li>• زر إظهار/إخفاء</li>
                                <li>• انتقال سلس</li>
                                <li>• توفير مساحة</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button class="btn btn-primary" onclick="testRealSystem()">اختبار النظام الحقيقي</button>
                <div id="testResult" class="mt-3"></div>
            </div>
        </div>
    </div>

    <script>
    // متغير لحالة الفلاتر
    let filtersVisible = false; // الافتراضي مخفي

    // دالة إخفاء/إظهار الفلاتر
    function toggleFilters() {
        const filtersContent = document.getElementById('filtersContent');
        const toggleIcon = document.getElementById('toggleFiltersIcon');
        const toggleText = document.getElementById('toggleFiltersText');
        
        if (filtersVisible) {
            // إخفاء الفلاتر
            filtersContent.classList.remove('show');
            toggleIcon.className = 'fas fa-eye me-1';
            toggleText.textContent = 'إظهار الفلاتر';
            filtersVisible = false;
            console.log('تم إخفاء الفلاتر');
        } else {
            // إظهار الفلاتر
            filtersContent.classList.add('show');
            toggleIcon.className = 'fas fa-eye-slash me-1';
            toggleText.textContent = 'إخفاء الفلاتر';
            filtersVisible = true;
            console.log('تم إظهار الفلاتر');
        }
    }

    // دالة تحديث البيانات
    function refreshData() {
        console.log('تحديث البيانات...');
        alert('تم تحديث البيانات!');
    }

    // دالة تصدير Excel
    function exportToExcel() {
        console.log('تصدير Excel...');
        alert('ميزة تصدير Excel قيد التطوير');
    }

    // دالة تصدير PDF
    function exportToPDF() {
        console.log('تصدير PDF...');
        alert('ميزة تصدير PDF قيد التطوير');
    }
    
    function testRealSystem() {
        $('#testResult').html('<div class="text-center"><div class="spinner-border"></div><br>جاري اختبار النظام...</div>');
        
        fetch('/purchase-orders/items-analysis')
            .then(response => {
                if (response.ok) {
                    $('#testResult').html(`
                        <div class="alert alert-success">
                            <h5>✅ النظام يعمل بالتحسينات الجديدة!</h5>
                            <p>تم تطبيق Header المتقدم والفلاتر القابلة للإخفاء</p>
                            <a href="/purchase-orders/items-analysis" class="btn btn-primary" target="_blank">
                                افتح النظام الحقيقي
                            </a>
                        </div>
                    `);
                } else {
                    $('#testResult').html('<div class="alert alert-danger">❌ فشل في الوصول للنظام</div>');
                }
            })
            .catch(error => {
                $('#testResult').html(`<div class="alert alert-danger">❌ خطأ: ${error}</div>`);
            });
    }
    </script>
</body>
</html>
        