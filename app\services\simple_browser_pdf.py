# -*- coding: utf-8 -*-
"""
مولد PDF بسيط يستخدم صفحة المعاينة الموجودة
Simple PDF Generator using existing viewer page
"""

import os
import sys
import requests
import subprocess
import time
from datetime import datetime

# إضافة مسار قاعدة البيانات
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class SimpleBrowserPDF:
    """مولد PDF بسيط باستخدام صفحة المعاينة"""
    
    def __init__(self):
        """تهيئة مولد PDF"""
        self.output_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'pdf')
        os.makedirs(self.output_dir, exist_ok=True)
        self.base_url = 'http://127.0.0.1:5000'
    
    def generate_pdf_from_viewer(self, delivery_order_id):
        """إنشاء PDF من صفحة المعاينة"""
        try:
            # إنشاء اسم الملف
            filename = f"delivery_order_viewer_{delivery_order_id}.pdf"
            filepath = os.path.join(self.output_dir, filename)
            
            # URL صفحة المعاينة
            viewer_url = f"{self.base_url}/shipments/delivery-order-viewer/{delivery_order_id}"
            
            print(f"🌐 استخدام صفحة المعاينة: {viewer_url}")
            
            # الطريقة 1: Chrome headless
            if self._try_chrome_print(viewer_url, filepath):
                return filepath, "تم إنشاء PDF من صفحة المعاينة بنجاح"
            
            # الطريقة 2: wkhtmltopdf
            if self._try_wkhtmltopdf(viewer_url, filepath):
                return filepath, "تم إنشاء PDF من صفحة المعاينة بنجاح"
            
            # الطريقة 3: محاولة تحميل HTML وحفظه كـ PDF بسيط
            if self._try_simple_html_save(viewer_url, filepath, delivery_order_id):
                return filepath, "تم حفظ صفحة المعاينة كـ PDF"
            
            return None, "فشل في إنشاء PDF من صفحة المعاينة"
            
        except Exception as e:
            return None, f"خطأ في إنشاء PDF: {str(e)}"
    
    def _try_chrome_print(self, url, output_path):
        """محاولة استخدام Chrome للطباعة"""
        try:
            # البحث عن Chrome
            chrome_paths = [
                r'C:\Program Files\Google\Chrome\Application\chrome.exe',
                r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe',
                'chrome.exe',
                'google-chrome'
            ]
            
            chrome_path = None
            for path in chrome_paths:
                try:
                    result = subprocess.run([path, '--version'], capture_output=True, timeout=5)
                    if result.returncode == 0:
                        chrome_path = path
                        print(f"✅ تم العثور على Chrome: {path}")
                        break
                except:
                    continue
            
            if not chrome_path:
                print("❌ Chrome غير متاح")
                return False
            
            # أمر Chrome لإنشاء PDF
            cmd = [
                chrome_path,
                '--headless',
                '--disable-gpu',
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-extensions',
                '--disable-plugins',
                '--run-all-compositor-stages-before-draw',
                '--virtual-time-budget=10000',  # انتظار 10 ثوان للتحميل
                '--print-to-pdf=' + output_path,
                '--print-to-pdf-no-header',
                url
            ]
            
            print("🖨️ تشغيل Chrome لإنشاء PDF...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            # فحص النتيجة
            if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:  # على الأقل 1KB
                file_size = os.path.getsize(output_path)
                print(f"✅ تم إنشاء PDF بنجاح! حجم الملف: {file_size} بايت")
                return True
            else:
                print(f"❌ فشل Chrome في إنشاء PDF")
                if result.stderr:
                    print(f"خطأ Chrome: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ انتهت مهلة Chrome")
            return False
        except Exception as e:
            print(f"❌ خطأ في Chrome: {e}")
            return False
    
    def _try_wkhtmltopdf(self, url, output_path):
        """محاولة استخدام wkhtmltopdf"""
        try:
            cmd = [
                'wkhtmltopdf',
                '--page-size', 'A4',
                '--orientation', 'Portrait',
                '--margin-top', '0.75in',
                '--margin-right', '0.75in',
                '--margin-bottom', '0.75in',
                '--margin-left', '0.75in',
                '--encoding', 'UTF-8',
                '--javascript-delay', '5000',
                '--no-stop-slow-scripts',
                url,
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0 and os.path.exists(output_path):
                print("✅ تم إنشاء PDF باستخدام wkhtmltopdf")
                return True
            else:
                print(f"❌ فشل wkhtmltopdf: {result.stderr}")
                return False
                
        except (subprocess.TimeoutExpired, FileNotFoundError, Exception) as e:
            print(f"❌ wkhtmltopdf غير متاح: {e}")
            return False
    
    def _try_simple_html_save(self, url, output_path, delivery_order_id):
        """حفظ HTML كملف PDF بسيط (كحل أخير)"""
        try:
            # تحميل HTML
            response = requests.get(url, timeout=10)
            if response.status_code != 200:
                return False
            
            # حفظ HTML كملف مؤقت
            html_filename = f"delivery_order_{delivery_order_id}.html"
            html_path = os.path.join(self.output_dir, html_filename)
            
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            print(f"✅ تم حفظ HTML: {html_path}")
            
            # إنشاء ملف نصي يوضح كيفية تحويله لـ PDF
            instructions_path = output_path.replace('.pdf', '_instructions.txt')
            with open(instructions_path, 'w', encoding='utf-8') as f:
                f.write(f"""تعليمات تحويل أمر التسليم إلى PDF:

1. افتح الرابط التالي في المتصفح:
   {url}

2. اضغط على زر "تحميل PDF" في الصفحة

3. أو استخدم Ctrl+P للطباعة واختر "حفظ كـ PDF"

4. أو افتح الملف المحفوظ:
   {html_path}

تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
""")
            
            # نسخ الملف كـ PDF (مؤقتاً)
            import shutil
            shutil.copy2(html_path, output_path)
            
            print(f"✅ تم إنشاء ملف مرجعي: {instructions_path}")
            return True
            
        except Exception as e:
            print(f"❌ فشل في حفظ HTML: {e}")
            return False
    
    def check_tools(self):
        """فحص الأدوات المتاحة"""
        tools = {
            'chrome': False,
            'wkhtmltopdf': False,
            'requests': True  # متاح دائماً
        }
        
        # فحص Chrome
        chrome_paths = [
            r'C:\Program Files\Google\Chrome\Application\chrome.exe',
            r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe',
            'chrome.exe'
        ]
        
        for path in chrome_paths:
            try:
                result = subprocess.run([path, '--version'], capture_output=True, timeout=5)
                if result.returncode == 0:
                    tools['chrome'] = True
                    break
            except:
                continue
        
        # فحص wkhtmltopdf
        try:
            result = subprocess.run(['wkhtmltopdf', '--version'], capture_output=True, timeout=5)
            tools['wkhtmltopdf'] = result.returncode == 0
        except:
            pass
        
        return tools


# إنشاء instance عام للمولد
simple_browser_pdf = SimpleBrowserPDF()


def generate_pdf_from_viewer(delivery_order_id):
    """دالة مساعدة لإنشاء PDF من صفحة المعاينة"""
    return simple_browser_pdf.generate_pdf_from_viewer(delivery_order_id)


def check_pdf_generation_tools():
    """فحص أدوات إنشاء PDF المتاحة"""
    return simple_browser_pdf.check_tools()
