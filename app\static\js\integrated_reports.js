/**
 * التقارير المتكاملة للموردين والحوالات
 * Integrated Supplier-Transfer Reports
 */

/**
 * تحميل التقرير المالي
 */
function loadFinancialReport() {
    const params = new URLSearchParams(currentFilters);
    
    $.ajax({
        url: `/api/suppliers/transfers/reports/financial-summary?${params.toString()}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                displayFinancialReport(response.data);
            } else {
                $('#reportContent').html('<div class="alert alert-danger">خطأ في تحميل التقرير المالي</div>');
            }
        },
        error: function() {
            $('#reportContent').html('<div class="alert alert-danger">خطأ في الاتصال بالخادم</div>');
        }
    });
}

/**
 * عرض التقرير المالي
 */
function displayFinancialReport(data) {
    const content = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-chart-line text-success"></i> التقرير المالي</h5>
                <button class="btn btn-outline-primary btn-sm" onclick="exportReport('financial')">
                    <i class="fas fa-download"></i> تصدير
                </button>
            </div>
            <div class="card-body">
                <!-- إحصائيات سريعة -->
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${formatCurrency(data.summary.total_balances)}</div>
                        <div class="stat-label">إجمالي الأرصدة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${formatCurrency(data.summary.total_payments)}</div>
                        <div class="stat-label">إجمالي المدفوعات</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${data.summary.active_suppliers}</div>
                        <div class="stat-label">الموردين النشطين</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${data.summary.pending_payments}</div>
                        <div class="stat-label">المدفوعات المعلقة</div>
                    </div>
                </div>
                
                <!-- الرسوم البيانية -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="chart-container">
                            <canvas id="balancesByCurrencyChart"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container">
                            <canvas id="paymentsTrendChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- جدول أعلى الموردين -->
                <div class="mt-4">
                    <h6>أعلى الموردين من حيث الأرصدة</h6>
                    <div class="table-responsive">
                        <table class="table table-striped" id="topSuppliersTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>المورد</th>
                                    <th>الرصيد الحالي</th>
                                    <th>إجمالي المدفوعات</th>
                                    <th>آخر دفعة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${generateTopSuppliersRows(data.top_suppliers)}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    $('#reportContent').html(content);
    
    // رسم الرسوم البيانية
    drawBalancesByCurrencyChart(data.balances_by_currency);
    drawPaymentsTrendChart(data.payments_trend);
}

/**
 * تحميل تقرير الأداء
 */
function loadPerformanceReport() {
    const params = new URLSearchParams(currentFilters);
    
    $.ajax({
        url: `/api/suppliers/transfers/reports/performance-analysis?${params.toString()}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                displayPerformanceReport(response.data);
            } else {
                $('#reportContent').html('<div class="alert alert-danger">خطأ في تحميل تقرير الأداء</div>');
            }
        },
        error: function() {
            $('#reportContent').html('<div class="alert alert-danger">خطأ في الاتصال بالخادم</div>');
        }
    });
}

/**
 * عرض تقرير الأداء
 */
function displayPerformanceReport(data) {
    const content = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-chart-bar text-info"></i> تقرير الأداء</h5>
                <button class="btn btn-outline-primary btn-sm" onclick="exportReport('performance')">
                    <i class="fas fa-download"></i> تصدير
                </button>
            </div>
            <div class="card-body">
                <!-- مؤشرات الأداء -->
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${data.kpis.avg_payment_days.toFixed(1)}</div>
                        <div class="stat-label">متوسط أيام الدفع</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${data.kpis.completion_rate.toFixed(1)}%</div>
                        <div class="stat-label">معدل إكمال المدفوعات</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${data.kpis.on_time_rate.toFixed(1)}%</div>
                        <div class="stat-label">معدل الدفع في الوقت</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${formatCurrency(data.kpis.avg_payment_amount)}</div>
                        <div class="stat-label">متوسط قيمة الدفعة</div>
                    </div>
                </div>
                
                <!-- الرسوم البيانية -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="chart-container">
                            <canvas id="paymentStatusChart"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container">
                            <canvas id="supplierPerformanceChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- جدول أداء الموردين -->
                <div class="mt-4">
                    <h6>أداء الموردين</h6>
                    <div class="table-responsive">
                        <table class="table table-striped" id="supplierPerformanceTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>المورد</th>
                                    <th>عدد المدفوعات</th>
                                    <th>متوسط أيام الدفع</th>
                                    <th>معدل الإكمال</th>
                                    <th>التقييم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${generateSupplierPerformanceRows(data.supplier_performance)}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    $('#reportContent').html(content);
    
    // رسم الرسوم البيانية
    drawPaymentStatusChart(data.payment_status_distribution);
    drawSupplierPerformanceChart(data.supplier_performance);
}

/**
 * تحميل تقرير التحليلات المتقدمة
 */
function loadAnalysisReport() {
    const params = new URLSearchParams(currentFilters);
    
    $.ajax({
        url: `/api/suppliers/transfers/reports/advanced-analysis?${params.toString()}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                displayAnalysisReport(response.data);
            } else {
                $('#reportContent').html('<div class="alert alert-danger">خطأ في تحميل التحليلات المتقدمة</div>');
            }
        },
        error: function() {
            $('#reportContent').html('<div class="alert alert-danger">خطأ في الاتصال بالخادم</div>');
        }
    });
}

/**
 * عرض تقرير التحليلات المتقدمة
 */
function displayAnalysisReport(data) {
    const content = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-chart-pie text-warning"></i> التحليلات المتقدمة</h5>
                <button class="btn btn-outline-primary btn-sm" onclick="exportReport('analysis')">
                    <i class="fas fa-download"></i> تصدير
                </button>
            </div>
            <div class="card-body">
                <!-- تحليل الاتجاهات -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="chart-container">
                            <canvas id="trendsAnalysisChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- تحليل العملات -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="chart-container">
                            <canvas id="currencyDistributionChart"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container">
                            <canvas id="seasonalAnalysisChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- تحليل المخاطر -->
                <div class="mt-4">
                    <h6>تحليل المخاطر</h6>
                    <div class="table-responsive">
                        <table class="table table-striped" id="riskAnalysisTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>المورد</th>
                                    <th>مستوى المخاطر</th>
                                    <th>المدفوعات المتأخرة</th>
                                    <th>متوسط التأخير</th>
                                    <th>التوصيات</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${generateRiskAnalysisRows(data.risk_analysis)}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    $('#reportContent').html(content);
    
    // رسم الرسوم البيانية
    drawTrendsAnalysisChart(data.trends_analysis);
    drawCurrencyDistributionChart(data.currency_distribution);
    drawSeasonalAnalysisChart(data.seasonal_analysis);
}

/**
 * تحميل تقرير التتبع
 */
function loadTrackingReport() {
    const params = new URLSearchParams(currentFilters);
    
    $.ajax({
        url: `/api/suppliers/transfers/reports/tracking-summary?${params.toString()}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                displayTrackingReport(response.data);
            } else {
                $('#reportContent').html('<div class="alert alert-danger">خطأ في تحميل تقرير التتبع</div>');
            }
        },
        error: function() {
            $('#reportContent').html('<div class="alert alert-danger">خطأ في الاتصال بالخادم</div>');
        }
    });
}

/**
 * عرض تقرير التتبع
 */
function displayTrackingReport(data) {
    const content = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-route text-danger"></i> تقرير التتبع</h5>
                <button class="btn btn-outline-primary btn-sm" onclick="exportReport('tracking')">
                    <i class="fas fa-download"></i> تصدير
                </button>
            </div>
            <div class="card-body">
                <!-- إحصائيات التتبع -->
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${data.tracking_stats.total_transfers}</div>
                        <div class="stat-label">إجمالي الحوالات</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${data.tracking_stats.completed_transfers}</div>
                        <div class="stat-label">الحوالات المكتملة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${data.tracking_stats.pending_transfers}</div>
                        <div class="stat-label">الحوالات المعلقة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${data.tracking_stats.avg_processing_time.toFixed(1)}</div>
                        <div class="stat-label">متوسط وقت المعالجة (ساعة)</div>
                    </div>
                </div>
                
                <!-- جدول الحوالات الحديثة -->
                <div class="mt-4">
                    <h6>الحوالات الحديثة</h6>
                    <div class="table-responsive">
                        <table class="table table-striped" id="recentTransfersTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الحوالة</th>
                                    <th>المورد</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الطلب</th>
                                    <th>وقت المعالجة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${generateRecentTransfersRows(data.recent_transfers)}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    $('#reportContent').html(content);
}

// دوال مساعدة لتوليد صفوف الجداول
function generateTopSuppliersRows(suppliers) {
    return suppliers.map(supplier => `
        <tr>
            <td>${supplier.name}</td>
            <td class="amount-display">${formatCurrency(supplier.current_balance)}</td>
            <td class="amount-display">${formatCurrency(supplier.total_payments)}</td>
            <td>${formatDate(supplier.last_payment_date)}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewSupplierDetails(${supplier.id})">
                    <i class="fas fa-eye"></i> عرض
                </button>
            </td>
        </tr>
    `).join('');
}

function generateSupplierPerformanceRows(suppliers) {
    return suppliers.map(supplier => `
        <tr>
            <td>${supplier.name}</td>
            <td>${supplier.payment_count}</td>
            <td>${supplier.avg_payment_days.toFixed(1)}</td>
            <td>${supplier.completion_rate.toFixed(1)}%</td>
            <td>${getPerformanceRating(supplier.performance_score)}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewSupplierDetails(${supplier.id})">
                    <i class="fas fa-eye"></i> تفاصيل
                </button>
            </td>
        </tr>
    `).join('');
}

function generateRiskAnalysisRows(risks) {
    return risks.map(risk => `
        <tr>
            <td>${risk.supplier_name}</td>
            <td>${getRiskBadge(risk.risk_level)}</td>
            <td>${risk.overdue_payments}</td>
            <td>${risk.avg_delay_days.toFixed(1)} يوم</td>
            <td>${risk.recommendations}</td>
        </tr>
    `).join('');
}

function generateRecentTransfersRows(transfers) {
    return transfers.map(transfer => `
        <tr>
            <td>${transfer.transfer_number}</td>
            <td>${transfer.supplier_name}</td>
            <td class="amount-display">${formatCurrency(transfer.amount)}</td>
            <td>${getStatusBadge(transfer.status)}</td>
            <td>${formatDateTime(transfer.request_date)}</td>
            <td>${transfer.processing_time} ساعة</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="trackTransfer('${transfer.transfer_number}')">
                    <i class="fas fa-route"></i> تتبع
                </button>
            </td>
        </tr>
    `).join('');
}

// دوال مساعدة للتنسيق
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 2
    }).format(amount);
}

function formatDate(dateString) {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('ar-SA');
}

function formatDateTime(dateString) {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString('ar-SA');
}

function getPerformanceRating(score) {
    if (score >= 90) return '<span class="badge bg-success">ممتاز</span>';
    if (score >= 80) return '<span class="badge bg-info">جيد جداً</span>';
    if (score >= 70) return '<span class="badge bg-warning">جيد</span>';
    if (score >= 60) return '<span class="badge bg-orange">مقبول</span>';
    return '<span class="badge bg-danger">ضعيف</span>';
}

function getRiskBadge(level) {
    const badges = {
        'low': '<span class="badge bg-success">منخفض</span>',
        'medium': '<span class="badge bg-warning">متوسط</span>',
        'high': '<span class="badge bg-danger">عالي</span>'
    };
    return badges[level] || '<span class="badge bg-secondary">غير محدد</span>';
}

function getStatusBadge(status) {
    const badges = {
        'PENDING': '<span class="badge bg-warning">معلقة</span>',
        'APPROVED': '<span class="badge bg-info">معتمدة</span>',
        'EXECUTED': '<span class="badge bg-primary">منفذة</span>',
        'COMPLETED': '<span class="badge bg-success">مكتملة</span>',
        'CANCELLED': '<span class="badge bg-danger">ملغية</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">' + status + '</span>';
}
