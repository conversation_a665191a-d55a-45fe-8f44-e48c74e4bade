# -*- coding: utf-8 -*-
"""
معالج الأحداث الفورية من قاعدة البيانات
Instant Event Processor from Database
"""

import logging
import os
import time
import threading
import requests
from datetime import datetime
from database_manager import DatabaseManager

# إعداد logger أولاً
logger = logging.getLogger(__name__)

# استيراد خدمة WhatsApp
try:
    from app.services.green_whatsapp_service import green_whatsapp_service
    logger.info("✅ تم تحميل خدمة WhatsApp بنجاح")
except ImportError as e:
    logger.error(f"❌ فشل في تحميل خدمة WhatsApp: {e}")
    green_whatsapp_service = None

class InstantEventProcessor:
    """معالج الأحداث الفورية"""
    
    def __init__(self):
        """تهيئة المعالج"""
        self.running = False
        self.thread = None
        self.db = None
        
        # إعداد Green API
        self._setup_green_api()
        
        logger.info("🎯 تم تهيئة معالج الأحداث الفورية")
    
    def _setup_green_api(self):
        """إعداد Green API"""
        os.environ['GREEN_API_INSTANCE_ID'] = '7105306929'
        os.environ['GREEN_API_TOKEN'] = '0260de395caa4adfb2aa02d53511f9ffa19b29b41f094fd68c'
        os.environ['GREEN_API_URL'] = 'https://7105.api.greenapi.com'
        os.environ['GREEN_API_TEST_MODE'] = 'false'

        # إعادة تهيئة خدمة WhatsApp بعد تعيين المتغيرات
        global green_whatsapp_service
        if green_whatsapp_service:
            green_whatsapp_service.__init__()  # إعادة تهيئة

        logger.info("🔧 تم إعداد Green API وإعادة تهيئة الخدمة")

    def _send_whatsapp_direct(self, phone, message):
        """إرسال رسالة WhatsApp مباشرة"""
        try:
            if green_whatsapp_service is None:
                logger.error("❌ خدمة WhatsApp غير متاحة")
                return False

            result = green_whatsapp_service.send_text_message(phone, message)

            # التعامل مع تنسيق النتيجة المختلف
            if isinstance(result, tuple) and len(result) >= 3:
                success, msg_info, msg_id = result
                if success:
                    logger.info(f"✅ تم إرسال الرسالة بنجاح إلى {phone}")
                    return True
                else:
                    logger.error(f"❌ فشل في إرسال الرسالة إلى {phone}: {msg_info}")
                    return False
            elif isinstance(result, dict):
                if result.get('success', False):
                    logger.info(f"✅ تم إرسال الرسالة بنجاح إلى {phone}")
                    return True
                else:
                    logger.error(f"❌ فشل في إرسال الرسالة إلى {phone}: {result.get('message', 'خطأ غير معروف')}")
                    return False
            else:
                logger.warning(f"⚠️ تنسيق نتيجة غير متوقع: {result}")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال رسالة WhatsApp: {e}")
            return False
    
    def start(self):
        """بدء معالجة الأحداث"""
        if self.running:
            logger.warning("⚠️ المعالج يعمل بالفعل")
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._process_events_loop, daemon=True)
        self.thread.start()
        logger.info("🚀 تم بدء معالج الأحداث الفورية")
    
    def stop(self):
        """إيقاف معالجة الأحداث"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        logger.info("🛑 تم إيقاف معالج الأحداث الفورية")
    
    def _process_events_loop(self):
        """حلقة معالجة الأحداث"""
        while self.running:
            try:
                self._process_pending_events()
                time.sleep(2)  # فحص كل ثانيتين
            except Exception as e:
                logger.error(f"❌ خطأ في حلقة معالجة الأحداث: {e}")
                time.sleep(5)  # انتظار أطول عند الخطأ
    
    def _process_pending_events(self):
        """معالجة الأحداث المعلقة"""
        try:
            if not self.db:
                self.db = DatabaseManager()
            
            # أولاً: نسخ الأحداث من automation_queue
            self._sync_automation_queue_to_events()

            # جلب الأحداث غير المعالجة
            query = """
                SELECT id, event_type, shipment_id, tracking_number, event_data
                FROM instant_notification_events
                WHERE status = 'pending'
                ORDER BY created_at ASC
                FETCH FIRST 10 ROWS ONLY
            """
            
            events = self.db.execute_query(query)
            
            if not events:
                return  # لا توجد أحداث للمعالجة
            
            logger.info(f"🔄 معالجة {len(events)} حدث فوري")
            
            for event in events:
                event_id, event_type, shipment_id, tracking_number, event_data = event
                
                try:
                    logger.info(f"🔥 معالجة حدث {event_type} للشحنة {tracking_number}")
                    
                    # استخراج البيانات من event_data
                    import json
                    try:
                        if event_data:
                            # قراءة CLOB إذا كان كذلك
                            if hasattr(event_data, 'read'):
                                event_data_str = event_data.read()
                            else:
                                event_data_str = str(event_data)

                            data = json.loads(event_data_str)
                            logger.info(f"📊 تم قراءة البيانات من event_data: {data}")
                        else:
                            data = {}
                            logger.warning(f"⚠️ event_data فارغ للحدث {event_id}")
                    except Exception as json_error:
                        logger.error(f"❌ خطأ في قراءة JSON: {json_error}")
                        data = {}

                    # إضافة البيانات الأساسية
                    data.update({
                        'shipment_id': shipment_id,
                        'tracking_number': tracking_number
                    })

                    logger.info(f"📋 البيانات النهائية للمعالجة: {data}")

                    success = self._handle_event(event_type, data)
                    
                    # تحديث حالة المعالجة
                    if success:
                        self._mark_event_processed(event_id, True)
                        logger.info(f"✅ تم معالجة الحدث {event_id} بنجاح - النوع: {event_type}")
                    else:
                        error_msg = f"فشل في معالجة {event_type}"
                        self._mark_event_processed(event_id, False, error_msg)
                        logger.error(f"❌ فشل في معالجة الحدث {event_id} - النوع: {event_type}")

                except Exception as e:
                    error_msg = f"خطأ في معالجة {event_type}: {str(e)}"
                    logger.error(f"❌ خطأ في معالجة الحدث {event_id}: {e}")
                    logger.error(f"📋 تفاصيل الحدث: النوع={event_type}, البيانات={event_data}")
                    self._mark_event_processed(event_id, False, error_msg)
        
        except Exception as e:
            logger.error(f"❌ خطأ في جلب الأحداث المعلقة: {e}")
    
    def _handle_event(self, event_type, data):
        """معالجة حدث محدد باستخدام الإعدادات من قاعدة البيانات"""
        try:
            # فحص إذا كان نوع الحدث مفعل في الإعدادات
            if not self._is_event_enabled(event_type):
                logger.info(f"⚠️ نوع الحدث {event_type} معطل في الإعدادات")
                return True  # نعتبره نجح لكن لم يرسل

            # الحصول على قالب الرسالة من الإعدادات
            message_template = self._get_message_template(event_type)
            if not message_template:
                logger.warning(f"⚠️ لا يوجد قالب رسالة لنوع الحدث: {event_type}")
                return False

            # تنسيق الرسالة
            message = self._format_message(message_template, data)

            # الحصول على جهات الاتصال
            contacts = self._get_event_contacts(event_type)
            if not contacts:
                logger.warning(f"⚠️ لا توجد جهات اتصال لنوع الحدث: {event_type}")
                return False

            # إرسال الرسالة لجميع جهات الاتصال
            success_count = 0
            for contact in contacts:
                phone = contact.get('phone_number')
                if phone:
                    result = self._send_whatsapp_direct(phone, message)
                    if result:
                        success_count += 1
                        logger.info(f"✅ تم إرسال {event_type} إلى: {phone}")
                    else:
                        logger.error(f"❌ فشل إرسال {event_type} إلى: {phone}")

            return success_count > 0

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة نوع الحدث {event_type}: {e}")
            return False
    
    def _send_customs_clearance_notification(self, data):
        """إرسال إشعار التخليص الجمركي"""
        try:
            booking_number = data.get('booking_number', data.get('tracking_number', f"BOOK-{data.get('shipment_id')}"))

            message = f"🏛️ الشحنة رقم {booking_number} في التخليص الجمركي\n"
            message += f"📋 يتم حالياً إنجاز إجراءات التخليص الجمركي\n"
            message += f"⏳ المدة المتوقعة: 1-3 أيام عمل\n"
            message += f"📄 تأكد من توفر جميع الوثائق المطلوبة\n"
            message += f"⏰ آخر تحديث: {self._get_current_time()}\n"
            message += f"📞 للاستفسار اتصل بقسم التخليص"

            phone = "967774893877"

            success = self._send_whatsapp_direct(phone, message)

            if success:
                logger.info(f"✅ تم إرسال إشعار التخليص للشحنة {booking_number}")
                return True
            else:
                logger.error(f"❌ فشل في إرسال إشعار التخليص للشحنة {booking_number}")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال إشعار التخليص: {e}")
            return False
    
    def _send_delivery_notification(self, data):
        """إرسال إشعار التسليم"""
        try:
            booking_number = data.get('booking_number', data.get('tracking_number', f"BOOK-{data.get('shipment_id')}"))
            message = f"🎉 تم تسليم الشحنة رقم {booking_number} بنجاح\n"
            message += f"✅ وصلت الشحنة إلى وجهتها النهائية\n"
            message += f"📅 تاريخ التسليم: {self._get_current_time()}\n"
            message += f"🙏 شكراً لثقتكم بخدماتنا"
            phone = "967774893877"

            success = self._send_whatsapp_direct(phone, message)

            if success:
                logger.info(f"✅ تم إرسال إشعار التسليم للشحنة {booking_number}")
                return True
            else:
                logger.error(f"❌ فشل في إرسال إشعار التسليم للشحنة {booking_number}")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال إشعار التسليم: {e}")
            return False
    
    def _send_port_arrival_notification(self, data):
        """إرسال إشعار وصول الميناء"""
        try:
            booking_number = data.get('booking_number', data.get('tracking_number', f"BOOK-{data.get('shipment_id')}"))
            port = data.get('port_of_discharge', 'الميناء')
            message = f"🚢 وصلت الشحنة رقم {booking_number} إلى {port}\n"
            message += f"📍 الموقع: {port}\n"
            message += f"⏰ وقت الوصول: {self._get_current_time()}\n"
            message += f"📋 الخطوة التالية: التخليص الجمركي\n"
            message += f"📞 للاستفسار اتصل بنا"
            phone = "967774893877"

            success = self._send_whatsapp_direct(phone, message)

            if success:
                logger.info(f"✅ تم إرسال إشعار وصول الميناء للشحنة {booking_number}")
                return True
            else:
                logger.error(f"❌ فشل في إرسال إشعار وصول الميناء للشحنة {booking_number}")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال إشعار وصول الميناء: {e}")
            return False

    def _send_transit_notification(self, data):
        """إرسال إشعار النقل"""
        try:
            booking_number = data.get('booking_number', data.get('tracking_number', f"BOOK-{data.get('shipment_id')}"))
            old_status = data.get('old_status', 'غير محدد')
            new_status = data.get('new_status', 'في النقل')

            message = f"🚛 الشحنة رقم {booking_number} في الطريق\n"
            message += f"📦 حالة الشحنة: {new_status}\n"
            message += f"🛣️ الشحنة في طريقها إلى الوجهة\n"
            message += f"⏰ آخر تحديث: {self._get_current_time()}\n"
            message += f"📱 يمكنك متابعة الشحنة عبر النظام"

            phone = "967774893877"

            success = self._send_whatsapp_direct(phone, message)

            if success:
                logger.info(f"✅ تم إرسال إشعار النقل للشحنة {booking_number}")
                return True
            else:
                logger.error(f"❌ فشل في إرسال إشعار النقل للشحنة {booking_number}")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال إشعار النقل: {e}")
            return False

    def _send_confirmation_notification(self, data):
        """إرسال إشعار التأكيد"""
        try:
            booking_number = data.get('booking_number', data.get('tracking_number', f"BOOK-{data.get('shipment_id')}"))
            message = f"✅ تم تأكيد استلام الشحنة رقم {booking_number}\n"
            message += f"📋 تم تسجيل الشحنة في النظام بنجاح\n"
            message += f"🚀 سيتم البدء في معالجة الشحنة قريباً\n"
            message += f"⏰ وقت التأكيد: {self._get_current_time()}\n"
            message += f"📞 للاستفسار اتصل بخدمة العملاء"

            phone = "967774893877"

            success = self._send_whatsapp_direct(phone, message)

            if success:
                logger.info(f"✅ تم إرسال إشعار التأكيد للشحنة {booking_number}")
                return True
            else:
                logger.error(f"❌ فشل في إرسال إشعار التأكيد للشحنة {booking_number}")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال إشعار التأكيد: {e}")
            return False

    def _send_status_change_notification(self, data):
        """إرسال إشعار تغيير الحالة العام"""
        try:
            booking_number = data.get('booking_number', data.get('tracking_number', f"BOOK-{data.get('shipment_id')}"))
            old_status = data.get('old_status', 'غير محدد')
            new_status = data.get('new_status', 'غير محدد')

            message = f"📦 تحديث حالة الشحنة رقم {booking_number}\n"
            message += f"🔄 الحالة السابقة: {old_status}\n"
            message += f"✨ الحالة الجديدة: {new_status}\n"
            message += f"⏰ وقت التحديث: {self._get_current_time()}\n"
            message += f"📱 تابع شحنتك عبر النظام للمزيد من التفاصيل"

            phone = "967774893877"

            success = self._send_whatsapp_direct(phone, message)

            if success:
                logger.info(f"✅ تم إرسال إشعار تغيير الحالة للشحنة {booking_number}")
                return True
            else:
                logger.error(f"❌ فشل في إرسال إشعار تغيير الحالة للشحنة {booking_number}")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال إشعار تغيير الحالة: {e}")
            return False

    def _send_ready_for_delivery_notification(self, data):
        """إرسال إشعار الاستعداد للتسليم"""
        try:
            booking_number = data.get('booking_number', data.get('tracking_number', f"BOOK-{data.get('shipment_id')}"))

            message = f"🎯 الشحنة رقم {booking_number} جاهزة للتسليم\n"
            message += f"✅ تم الانتهاء من جميع الإجراءات\n"
            message += f"📦 الشحنة جاهزة للاستلام أو التوصيل\n"
            message += f"📞 اتصل بنا لتحديد موعد التسليم\n"
            message += f"⏰ وقت الجاهزية: {self._get_current_time()}\n"
            message += f"🚚 خدمة التوصيل متاحة"

            phone = "967774893877"

            success = self._send_whatsapp_direct(phone, message)

            if success:
                logger.info(f"✅ تم إرسال إشعار الجاهزية للتسليم للشحنة {booking_number}")
                return True
            else:
                logger.error(f"❌ فشل في إرسال إشعار الجاهزية للتسليم للشحنة {booking_number}")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال إشعار الجاهزية للتسليم: {e}")
            return False

    def _send_shipment_created_notification(self, data):
        """إرسال إشعار إنشاء شحنة جديدة"""
        try:
            booking_number = data.get('booking_number', data.get('tracking_number', f"BOOK-{data.get('shipment_id')}"))

            message = f"📦 تم إنشاء شحنة جديدة رقم {booking_number}\n"
            message += f"✅ تم تسجيل الشحنة في النظام بنجاح\n"
            message += f"📋 يمكنك متابعة حالة الشحنة عبر النظام\n"
            message += f"🚀 سيتم البدء في معالجة الشحنة قريباً\n"
            message += f"⏰ تاريخ الإنشاء: {self._get_current_time()}\n"
            message += f"📞 للاستفسار اتصل بخدمة العملاء"

            phone = "967774893877"

            success = self._send_whatsapp_direct(phone, message)

            if success:
                logger.info(f"✅ تم إرسال إشعار إنشاء الشحنة {booking_number}")
                return True
            else:
                logger.error(f"❌ فشل في إرسال إشعار إنشاء الشحنة {booking_number}")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال إشعار إنشاء الشحنة: {e}")
            return False

    def _send_delayed_shipment_notification(self, data):
        """إرسال إشعار تأخير الشحنة"""
        try:
            booking_number = data.get('booking_number', data.get('tracking_number', f"BOOK-{data.get('shipment_id')}"))

            message = f"⚠️ تأخير في الشحنة رقم {booking_number}\n"
            message += f"📅 الشحنة متأخرة عن الموعد المحدد\n"
            message += f"🔍 يتم حالياً متابعة الوضع مع الناقل\n"
            message += f"📞 سيتم إبلاغكم بأي تحديثات جديدة\n"
            message += f"⏰ آخر تحديث: {self._get_current_time()}\n"
            message += f"🙏 نعتذر عن أي إزعاج"

            phone = "967774893877"

            success = self._send_whatsapp_direct(phone, message)

            if success:
                logger.info(f"✅ تم إرسال إشعار التأخير للشحنة {booking_number}")
                return True
            else:
                logger.error(f"❌ فشل في إرسال إشعار التأخير للشحنة {booking_number}")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال إشعار التأخير: {e}")
            return False
    
    def _mark_event_processed(self, event_id, success, error_message=None):
        """تحديد الحدث كمعالج"""
        try:
            if success:
                update_query = """
                    UPDATE instant_notification_events
                    SET status = 'processed', processed_at = CURRENT_TIMESTAMP
                    WHERE id = :1
                """
                self.db.execute_update(update_query, [event_id])
                logger.info(f"✅ تم تحديث الحدث {event_id} إلى معالج")
            else:
                update_query = """
                    UPDATE instant_notification_events
                    SET status = 'failed', processed_at = CURRENT_TIMESTAMP, error_message = :1
                    WHERE id = :2
                """
                self.db.execute_update(update_query, [error_message, event_id])
                logger.info(f"❌ تم تحديث الحدث {event_id} إلى فاشل")

            self.db.commit()

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث حالة الحدث {event_id}: {e}")

    def _send_whatsapp_direct(self, phone, message):
        """إرسال WhatsApp مباشر عبر Green API"""
        try:
            # إعدادات Green API
            instance_id = "7105306929"
            token = "0260de395caa4adfb2aa02d53511f9ffa19b29b41f094fd68c"
            api_url = f"https://7105.api.greenapi.com/waInstance{instance_id}/sendMessage/{token}"

            # بيانات الرسالة
            payload = {
                "chatId": f"{phone}@c.us",
                "message": message
            }

            # إرسال الطلب
            response = requests.post(api_url, json=payload, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if result.get('idMessage'):
                    logger.info(f"✅ تم إرسال WhatsApp بنجاح - ID: {result.get('idMessage')}")
                    return True
                else:
                    logger.error(f"❌ فشل إرسال WhatsApp: {result}")
                    return False
            else:
                logger.error(f"❌ خطأ HTTP في إرسال WhatsApp: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال WhatsApp المباشر: {e}")
            return False

    def _get_message_template(self, event_type):
        """جلب قالب الرسالة من الإعدادات"""
        try:
            db = DatabaseManager()

            query = """
            SELECT message_template
            FROM instant_notification_settings
            WHERE event_type = :1 AND is_enabled = 1
            """

            result = db.execute_query(query, [event_type])

            if result:
                template = result[0][0]
                # معالجة CLOB
                if hasattr(template, 'read'):
                    template = template.read()
                return str(template) if template else f"تم تغيير حالة الشحنة {{tracking_number}}"
            else:
                return f"تم تغيير حالة الشحنة {{tracking_number}}"

        except Exception as e:
            logger.error(f"❌ خطأ في جلب قالب الرسالة: {e}")
            return f"تم تغيير حالة الشحنة {{tracking_number}}"

    def _get_event_contacts(self, event_type):
        """جلب جهات الاتصال المرتبطة بنوع الحدث"""
        try:
            db = DatabaseManager()

            query = """
            SELECT c.phone_number, c.contact_name, c.contact_type
            FROM instant_notification_mapping m
            JOIN instant_notification_contacts c ON m.contact_id = c.id
            WHERE m.event_type = :1 AND m.is_active = 1 AND c.is_active = 1
            """

            result = db.execute_query(query, [event_type])

            contacts = []
            if result:
                for row in result:
                    contacts.append({
                        'phone_number': row[0],
                        'contact_name': row[1],
                        'contact_type': row[2]
                    })

            # إذا لم توجد جهات اتصال، استخدم الافتراضية
            if not contacts:
                contacts = [{'phone_number': '967774893877', 'contact_name': 'الإدارة الرئيسية', 'contact_type': 'admin'}]
                logger.warning(f"⚠️ لا توجد جهات اتصال مرتبطة بـ {event_type}، استخدام الافتراضية")

            return contacts

        except Exception as e:
            logger.error(f"❌ خطأ في جلب جهات الاتصال: {e}")
            return [{'phone_number': '967774893877', 'contact_name': 'الإدارة الرئيسية', 'contact_type': 'admin'}]

    def _format_message(self, template, data):
        """تنسيق الرسالة بالبيانات الفعلية مع دعم جميع المتغيرات"""
        try:
            message = template

            logger.info(f"🔧 تنسيق الرسالة: {template}")
            logger.info(f"📊 البيانات المتاحة: {data}")

            # استبدال المتغيرات الأساسية
            # رقم التتبع - نستخدم booking_number كأولوية
            tracking_number = str(data.get('booking_number', data.get('tracking_number', 'غير محدد')))
            message = message.replace('{tracking_number}', tracking_number)
            message = message.replace('{booking_number}', tracking_number)

            # معرف الشحنة
            shipment_id = str(data.get('shipment_id', 'غير محدد'))
            message = message.replace('{shipment_id}', shipment_id)

            # الحالة السابقة والجديدة
            old_status = str(data.get('old_status', 'غير محدد'))
            new_status = str(data.get('new_status', 'غير محدد'))
            message = message.replace('{old_status}', old_status)
            message = message.replace('{new_status}', new_status)

            # متغيرات إضافية
            port_of_discharge = str(data.get('port_of_discharge', 'غير محدد'))
            message = message.replace('{port_of_discharge}', port_of_discharge)

            created_at = str(data.get('created_at', 'الآن'))
            message = message.replace('{created_at}', created_at)

            current_time = self._get_current_time()
            message = message.replace('{current_time}', current_time)

            # متغيرات الوقت
            message = message.replace('{date}', current_time.split(' ')[0] if ' ' in current_time else current_time)
            message = message.replace('{time}', current_time.split(' ')[1] if ' ' in current_time else current_time)

            logger.info(f"✅ الرسالة بعد التنسيق: {message}")
            return message

        except Exception as e:
            logger.error(f"❌ خطأ في تنسيق الرسالة: {e}")
            return f"تم تغيير حالة الشحنة رقم {data.get('booking_number', data.get('tracking_number', 'غير محدد'))}"

    def _is_event_enabled(self, event_type):
        """فحص إذا كان نوع الحدث مفعل في الإعدادات"""
        try:
            # التأكد من وجود اتصال قاعدة البيانات
            if not self.db:
                from database_manager import DatabaseManager
                self.db = DatabaseManager()

            query = """
            SELECT is_enabled
            FROM instant_notification_settings
            WHERE event_type = :1
            """

            result = self.db.execute_query(query, [event_type])

            if result:
                return result[0][0] == 1
            else:
                # إذا لم يوجد في الإعدادات، نعتبره مفعل افتراضياً
                logger.warning(f"⚠️ نوع الحدث {event_type} غير موجود في الإعدادات")
                return True

        except Exception as e:
            logger.error(f"❌ خطأ في فحص تفعيل الحدث {event_type}: {e}")
            return True  # افتراضياً مفعل في حالة الخطأ

    def _get_message_template(self, event_type):
        """الحصول على قالب الرسالة من الإعدادات"""
        try:
            # التأكد من وجود اتصال قاعدة البيانات
            if not self.db:
                from database_manager import DatabaseManager
                self.db = DatabaseManager()

            query = """
            SELECT message_template
            FROM instant_notification_settings
            WHERE event_type = :1 AND is_enabled = 1
            """

            result = self.db.execute_query(query, [event_type])

            if result:
                template = result[0][0]
                # قراءة محتوى CLOB إذا كان كذلك
                if hasattr(template, 'read'):
                    return template.read()
                else:
                    return str(template)
            else:
                logger.warning(f"⚠️ لا يوجد قالب رسالة لنوع الحدث: {event_type}")
                return None

        except Exception as e:
            logger.error(f"❌ خطأ في جلب قالب الرسالة لـ {event_type}: {e}")
            return None

    def _get_event_contacts(self, event_type):
        """الحصول على جهات الاتصال المرتبطة بنوع الحدث"""
        try:
            # التأكد من وجود اتصال قاعدة البيانات
            if not self.db:
                from database_manager import DatabaseManager
                self.db = DatabaseManager()

            # البحث في جدول جهات الاتصال
            query = """
            SELECT DISTINCT inc.phone_number, inc.contact_name
            FROM instant_notification_contacts inc
            JOIN instant_notification_mapping inm ON inc.id = inm.contact_id
            WHERE inm.event_type = :1 AND inc.is_active = 1
            """

            result = self.db.execute_query(query, [event_type])

            if result:
                contacts = []
                for row in result:
                    contacts.append({
                        'phone_number': row[0],
                        'contact_name': row[1]
                    })
                return contacts
            else:
                # إذا لم توجد جهات اتصال محددة، استخدم الافتراضي
                logger.warning(f"⚠️ لا توجد جهات اتصال محددة لـ {event_type}, استخدام الافتراضي")
                return [{'phone_number': '967774893877', 'contact_name': 'افتراضي'}]

        except Exception as e:
            logger.error(f"❌ خطأ في جلب جهات الاتصال لـ {event_type}: {e}")
            # في حالة الخطأ، استخدم الرقم الافتراضي
            return [{'phone_number': '967774893877', 'contact_name': 'افتراضي'}]

    def _sync_automation_queue_to_events(self):
        """نسخ الأحداث من automation_queue إلى instant_notification_events مع تجنب التكرار"""
        try:
            # جلب الأحداث غير المعالجة من automation_queue مع تجنب التكرار
            queue_query = """
            SELECT aq.id, aq.shipment_id, aq.old_status, aq.new_status,
                   cs.booking_number, aq.created_at
            FROM automation_queue aq
            LEFT JOIN cargo_shipments cs ON aq.shipment_id = cs.id
            WHERE aq.processed = 0
            AND NOT EXISTS (
                SELECT 1 FROM instant_notification_events ine
                WHERE ine.shipment_id = aq.shipment_id
                AND ine.event_type = CASE
                    WHEN aq.new_status = 'delivered' THEN 'delivery_notification'
                    WHEN aq.new_status = 'port_arrival' THEN 'port_arrival_notification'
                    WHEN aq.new_status = 'customs_clearance' THEN 'customs_clearance_notification'
                    WHEN aq.new_status = 'in_transit' THEN 'transit_notification'
                    WHEN aq.new_status = 'confirmed' THEN 'confirmation_notification'
                    ELSE 'status_change_notification'
                END
                AND ine.status IN ('pending', 'processed')
                AND ine.created_at >= aq.created_at - INTERVAL '1' MINUTE
            )
            ORDER BY aq.created_at ASC
            FETCH FIRST 3 ROWS ONLY
            """

            queue_items = self.db.execute_query(queue_query)

            if queue_items:
                logger.info(f"🔄 نسخ {len(queue_items)} حدث جديد من automation_queue")

                processed_combinations = set()  # تتبع الشحنات والأنواع المعالجة

                for item in queue_items:
                    queue_id, shipment_id, old_status, new_status, booking_number, created_at = item

                    # تحديد نوع الحدث
                    event_type = self._determine_event_type(new_status)

                    # إنشاء مفتاح فريد للتحقق من التكرار
                    combination_key = f"{shipment_id}_{event_type}_{new_status}"

                    if combination_key in processed_combinations:
                        logger.warning(f"⚠️ تجاهل حدث مكرر: شحنة {shipment_id} - {event_type}")
                        # تحديث automation_queue كمعالج بدون إنشاء حدث
                        update_queue = "UPDATE automation_queue SET processed = 1 WHERE id = :1"
                        self.db.execute_update(update_queue, [queue_id])
                        continue

                    # إضافة للمعالجة
                    processed_combinations.add(combination_key)

                    # إنشاء بيانات الحدث
                    event_data = {
                        'automation_queue_id': queue_id,
                        'shipment_id': shipment_id,
                        'old_status': old_status,
                        'new_status': new_status,
                        'booking_number': booking_number or f'BOOK-{shipment_id}'
                    }

                    # إدراج في instant_notification_events
                    insert_query = """
                    INSERT INTO instant_notification_events
                    (event_type, shipment_id, tracking_number, event_data, status)
                    VALUES (:1, :2, :3, :4, 'pending')
                    """

                    import json
                    self.db.execute_update(insert_query, [
                        event_type,
                        shipment_id,
                        booking_number or f'BOOK-{shipment_id}',
                        json.dumps(event_data, ensure_ascii=False)
                    ])

                    logger.info(f"✅ تم إنشاء حدث: {event_type} للشحنة {shipment_id}")

                    # تحديث automation_queue كمعالج
                    update_queue = "UPDATE automation_queue SET processed = 1 WHERE id = :1"
                    self.db.execute_update(update_queue, [queue_id])

                self.db.commit()
                logger.info(f"✅ تم نسخ {len(queue_items)} حدث بنجاح مع تجنب التكرار")

        except Exception as e:
            logger.error(f"❌ خطأ في نسخ الأحداث: {e}")

    def _determine_event_type(self, status):
        """تحديد نوع الحدث بناءً على الحالة (متطابق مع الإعدادات)"""
        status_mapping = {
            'delivered': 'delivery_notification',
            'port_arrival': 'port_arrival_notification',
            'customs_clearance': 'customs_clearance_notification',
            'in_transit': 'in_transit_notification',  # تطابق مع الإعدادات
            'confirmed': 'status_change_notification',
            'pending': 'pending_notification',
            'shipped': 'shipped_notification',
            'delayed': 'delayed_notification',
            'ready_for_delivery': 'status_change_notification',
            'created': 'status_change_notification'
        }
        return status_mapping.get(status, 'status_change_notification')

    def _get_current_time(self):
        """الحصول على الوقت الحالي بتنسيق عربي"""
        try:
            from datetime import datetime
            now = datetime.now()
            return now.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return "الآن"

# إنشاء مثيل عام من المعالج
try:
    instant_processor = InstantEventProcessor()
    logger.info("✅ تم إنشاء مثيل معالج الأحداث الفورية")
except Exception as e:
    logger.error(f"❌ فشل في إنشاء مثيل معالج الأحداث: {e}")
    instant_processor = None

def start_instant_processor():
    """بدء معالج الأحداث الفورية"""
    if instant_processor is not None:
        instant_processor.start()
    else:
        logger.error("❌ لا يمكن بدء معالج الأحداث - المثيل غير موجود")

def stop_instant_processor():
    """إيقاف معالج الأحداث الفورية"""
    if instant_processor is not None:
        instant_processor.stop()
    else:
        logger.warning("⚠️ لا يمكن إيقاف معالج الأحداث - المثيل غير موجود")
