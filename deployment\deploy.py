#!/usr/bin/env python3
"""
نص النشر التلقائي للتطبيق
Automated Deployment Script
"""

import os
import sys
import json
import subprocess
import argparse
from pathlib import Path

class DeploymentManager:
    def __init__(self, config_file='deployment_config.json'):
        self.config_file = config_file
        self.load_config()
    
    def load_config(self):
        """تحميل إعدادات النشر"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except FileNotFoundError:
            self.config = self.get_default_config()
            self.save_config()
    
    def get_default_config(self):
        """الإعدادات الافتراضية للنشر"""
        return {
            "app_name": "saserp",
            "domain": "",
            "server_ip": "",
            "ssh_user": "root",
            "ssh_key": "",
            "app_path": "/var/www/saserp",
            "python_version": "3.9",
            "database": {
                "type": "oracle",
                "host": "localhost",
                "port": 1521,
                "service_name": "XE"
            },
            "ssl": {
                "enabled": True,
                "auto_ssl": True,
                "email": ""
            },
            "services": {
                "nginx": True,
                "pm2": True,
                "redis": False,
                "celery": False
            },
            "backup": {
                "enabled": True,
                "schedule": "0 2 * * *"
            }
        }
    
    def save_config(self):
        """حفظ إعدادات النشر"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)
    
    def run_command(self, command, check=True):
        """تنفيذ أمر shell"""
        print(f"🔄 تنفيذ: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if check and result.returncode != 0:
            print(f"❌ فشل الأمر: {command}")
            print(f"خطأ: {result.stderr}")
            sys.exit(1)
        
        return result
    
    def setup_server(self):
        """إعداد الخادم الأساسي"""
        print("🚀 بدء إعداد الخادم...")
        
        commands = [
            "apt update && apt upgrade -y",
            "apt install -y python3 python3-pip python3-venv nginx git",
            "apt install -y oracle-instantclient-basic oracle-instantclient-devel",
            "curl -fsSL https://deb.nodesource.com/setup_18.x | bash -",
            "apt install -y nodejs",
            "npm install -g pm2",
            "ufw allow 22",
            "ufw allow 80",
            "ufw allow 443",
            "ufw --force enable"
        ]
        
        for cmd in commands:
            self.run_command(cmd)
        
        print("✅ تم إعداد الخادم الأساسي")
    
    def deploy_application(self):
        """نشر التطبيق"""
        print("📦 نشر التطبيق...")
        
        app_path = self.config['app_path']
        
        # إنشاء مجلد التطبيق
        self.run_command(f"mkdir -p {app_path}")
        
        # نسخ ملفات التطبيق (يجب تخصيصها حسب طريقة النشر)
        print("📁 نسخ ملفات التطبيق...")
        # هنا يمكن استخدام git clone أو rsync أو scp
        
        # إنشاء البيئة الافتراضية
        self.run_command(f"cd {app_path} && python3 -m venv venv")
        
        # تثبيت المتطلبات
        self.run_command(f"cd {app_path} && source venv/bin/activate && pip install -r requirements.txt")
        
        print("✅ تم نشر التطبيق")
    
    def setup_nginx(self):
        """إعداد Nginx"""
        print("🌐 إعداد Nginx...")
        
        domain = self.config['domain']
        app_path = self.config['app_path']
        
        nginx_config = f"""
server {{
    listen 80;
    server_name {domain} www.{domain};
    return 301 https://$server_name$request_uri;
}}

server {{
    listen 443 ssl http2;
    server_name {domain} www.{domain};

    ssl_certificate /etc/letsencrypt/live/{domain}/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/{domain}/privkey.pem;
    
    # SSL Security
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    
    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    location / {{
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_redirect off;
    }}
    
    location /static {{
        alias {app_path}/app/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }}
}}
"""
        
        # كتابة إعدادات Nginx
        config_path = f"/etc/nginx/sites-available/{self.config['app_name']}"
        with open(config_path, 'w') as f:
            f.write(nginx_config)
        
        # تفعيل الموقع
        self.run_command(f"ln -sf {config_path} /etc/nginx/sites-enabled/")
        self.run_command("rm -f /etc/nginx/sites-enabled/default")
        self.run_command("nginx -t")
        self.run_command("systemctl reload nginx")
        
        print("✅ تم إعداد Nginx")
    
    def setup_ssl(self):
        """إعداد SSL"""
        if not self.config['ssl']['enabled']:
            return
        
        print("🔒 إعداد SSL...")
        
        if self.config['ssl']['auto_ssl']:
            # تثبيت Certbot
            self.run_command("apt install -y certbot python3-certbot-nginx")
            
            # الحصول على شهادة SSL
            domain = self.config['domain']
            email = self.config['ssl']['email']
            
            cmd = f"certbot --nginx -d {domain} -d www.{domain} --non-interactive --agree-tos --email {email}"
            self.run_command(cmd)
            
            # إعداد التجديد التلقائي
            self.run_command("crontab -l | { cat; echo '0 12 * * * /usr/bin/certbot renew --quiet'; } | crontab -")
        
        print("✅ تم إعداد SSL")
    
    def setup_pm2(self):
        """إعداد PM2"""
        print("⚙️ إعداد PM2...")
        
        app_path = self.config['app_path']
        
        pm2_config = {
            "apps": [{
                "name": self.config['app_name'],
                "script": "app.py",
                "cwd": app_path,
                "instances": "max",
                "exec_mode": "cluster",
                "interpreter": f"{app_path}/venv/bin/python",
                "env": {
                    "FLASK_ENV": "production",
                    "PORT": 5000
                },
                "error_file": f"/var/log/pm2/{self.config['app_name']}-error.log",
                "out_file": f"/var/log/pm2/{self.config['app_name']}-out.log",
                "log_file": f"/var/log/pm2/{self.config['app_name']}.log",
                "time": True,
                "autorestart": True,
                "max_restarts": 10,
                "min_uptime": "10s"
            }]
        }
        
        # كتابة إعدادات PM2
        config_path = f"{app_path}/ecosystem.config.json"
        with open(config_path, 'w') as f:
            json.dump(pm2_config, f, indent=2)
        
        # بدء التطبيق مع PM2
        self.run_command(f"cd {app_path} && pm2 start ecosystem.config.json")
        self.run_command("pm2 save")
        self.run_command("pm2 startup")
        
        print("✅ تم إعداد PM2")
    
    def setup_backup(self):
        """إعداد النسخ الاحتياطي"""
        if not self.config['backup']['enabled']:
            return
        
        print("💾 إعداد النسخ الاحتياطي...")
        
        backup_script = f"""#!/bin/bash
# نسخ احتياطي للتطبيق
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/{self.config['app_name']}"
APP_DIR="{self.config['app_path']}"

mkdir -p $BACKUP_DIR

# نسخ ملفات التطبيق
tar -czf $BACKUP_DIR/app_$DATE.tar.gz -C $APP_DIR .

# نسخ قاعدة البيانات (Oracle)
# يمكن إضافة أوامر النسخ الاحتياطي لـ Oracle هنا

# حذف النسخ القديمة (أكثر من 30 يوم)
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "تم إنشاء نسخة احتياطية: $BACKUP_DIR/app_$DATE.tar.gz"
"""
        
        # كتابة نص النسخ الاحتياطي
        with open('/usr/local/bin/backup_saserp.sh', 'w') as f:
            f.write(backup_script)
        
        self.run_command("chmod +x /usr/local/bin/backup_saserp.sh")
        
        # إضافة مهمة cron
        schedule = self.config['backup']['schedule']
        cron_job = f"{schedule} /usr/local/bin/backup_saserp.sh >> /var/log/backup.log 2>&1"
        self.run_command(f'(crontab -l 2>/dev/null; echo "{cron_job}") | crontab -')
        
        print("✅ تم إعداد النسخ الاحتياطي")
    
    def full_deployment(self):
        """النشر الكامل"""
        print("🚀 بدء النشر الكامل...")
        
        self.setup_server()
        self.deploy_application()
        
        if self.config['services']['nginx']:
            self.setup_nginx()
        
        if self.config['ssl']['enabled']:
            self.setup_ssl()
        
        if self.config['services']['pm2']:
            self.setup_pm2()
        
        self.setup_backup()
        
        print("🎉 تم النشر بنجاح!")
        print(f"🌐 الموقع متاح على: https://{self.config['domain']}")

def main():
    parser = argparse.ArgumentParser(description='نشر تطبيق SASERP')
    parser.add_argument('--config', default='deployment_config.json', help='ملف الإعدادات')
    parser.add_argument('--full', action='store_true', help='النشر الكامل')
    parser.add_argument('--app-only', action='store_true', help='نشر التطبيق فقط')
    parser.add_argument('--nginx-only', action='store_true', help='إعداد Nginx فقط')
    parser.add_argument('--ssl-only', action='store_true', help='إعداد SSL فقط')
    
    args = parser.parse_args()
    
    deployer = DeploymentManager(args.config)
    
    if args.full:
        deployer.full_deployment()
    elif args.app_only:
        deployer.deploy_application()
    elif args.nginx_only:
        deployer.setup_nginx()
    elif args.ssl_only:
        deployer.setup_ssl()
    else:
        print("يرجى تحديد نوع النشر المطلوب")
        parser.print_help()

if __name__ == '__main__':
    main()
