# -*- coding: utf-8 -*-
"""
مولد PDF مطابق للتصميم الأصلي مع دعم RTL
Exact Design PDF Generator with RTL Support
"""

import os
import sys
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm, mm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image, PageBreak
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import arabic_reshaper
from bidi.algorithm import get_display

# إضافة مسار قاعدة البيانات
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from database_manager import DatabaseManager

class ExactDesignPDFGenerator:
    """مولد PDF مطابق للتصميم الأصلي مع دعم RTL"""
    
    def __init__(self):
        """تهيئة مولد PDF"""
        self.setup_arabic_fonts()
        self.output_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'pdf')
        os.makedirs(self.output_dir, exist_ok=True)
        
        # ألوان التصميم الأصلي
        self.colors = {
            'primary': colors.Color(0.12, 0.31, 0.47),      # #1f4e79
            'secondary': colors.Color(0.17, 0.35, 0.63),    # #2c5aa0
            'success': colors.Color(0.16, 0.65, 0.27),      # #28a745
            'info': colors.Color(0.09, 0.64, 0.72),         # #17a2b8
            'warning': colors.Color(1.0, 0.76, 0.03),       # #ffc107
            'light_bg': colors.Color(0.97, 0.98, 0.98),     # #f8f9fa
            'border': colors.Color(0.87, 0.89, 0.90),       # #dee2e6
            'text_muted': colors.Color(0.42, 0.46, 0.49)    # #6c757d
        }
    
    def setup_arabic_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # مسار الخط المُحمل
            font_path = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'fonts', 'NotoSansArabic-Regular.ttf')
            
            if os.path.exists(font_path):
                try:
                    pdfmetrics.registerFont(TTFont('Arabic', font_path))
                    pdfmetrics.registerFont(TTFont('Arabic-Bold', font_path))
                    print(f"✅ تم تسجيل الخط العربي: {font_path}")
                    self.arabic_font_available = True
                    return
                except Exception as e:
                    print(f"❌ فشل تسجيل الخط المُحمل: {e}")
            
            # محاولة استخدام خطوط النظام
            system_fonts = [
                'C:/Windows/Fonts/arial.ttf',
                'C:/Windows/Fonts/tahoma.ttf',
                'C:/Windows/Fonts/calibri.ttf'
            ]
            
            for font_path in system_fonts:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('Arabic', font_path))
                        pdfmetrics.registerFont(TTFont('Arabic-Bold', font_path))
                        print(f"✅ تم تسجيل خط النظام: {font_path}")
                        self.arabic_font_available = True
                        return
                    except Exception as e:
                        continue
            
            print("⚠️ لم يتم العثور على خط عربي مناسب")
            self.arabic_font_available = False
                
        except Exception as e:
            print(f"خطأ في إعداد الخطوط: {e}")
            self.arabic_font_available = False
    
    def reshape_arabic_text(self, text):
        """إعادة تشكيل النص العربي للعرض الصحيح مع دعم RTL"""
        try:
            if text and any('\u0600' <= char <= '\u06FF' for char in str(text)):
                reshaped_text = arabic_reshaper.reshape(str(text))
                return get_display(reshaped_text)
            return str(text) if text else ""
        except Exception as e:
            print(f"خطأ في معالجة النص العربي: {e}")
            return str(text) if text else ""
    
    def generate_exact_design_pdf(self, delivery_order_id):
        """إنشاء PDF مطابق للتصميم الأصلي"""
        try:
            # جلب بيانات أمر التسليم الكاملة
            order_data = self._get_complete_delivery_order_data(delivery_order_id)
            if not order_data:
                return None, "أمر التسليم غير موجود"
            
            # إنشاء اسم الملف
            filename = f"delivery_order_exact_{order_data['order_number']}.pdf"
            filepath = os.path.join(self.output_dir, filename)
            
            # إنشاء المستند مع هوامش مطابقة للأصل
            doc = SimpleDocTemplate(
                filepath,
                pagesize=A4,
                rightMargin=12*mm,
                leftMargin=12*mm,
                topMargin=12*mm,
                bottomMargin=12*mm
            )
            
            # إنشاء المحتوى
            story = []
            
            # إضافة الهيدر مع الشعار
            self._add_exact_header(story, order_data)
            
            # إضافة معلومات الأمر الأساسية (الشبكة الثلاثية)
            self._add_order_info_grid(story, order_data)
            
            # إضافة المعلومات الأساسية للشحنة
            self._add_basic_shipment_info(story, order_data)
            
            # إضافة بيانات الشحنة التفصيلية
            self._add_detailed_shipment_info(story, order_data)
            
            # إضافة التعليمات الخاصة (إذا وجدت)
            self._add_special_instructions(story, order_data)
            
            # إضافة الفوتر
            self._add_exact_footer(story, order_data)
            
            # بناء المستند
            doc.build(story)
            
            return filepath, "تم إنشاء PDF مطابق للتصميم الأصلي بنجاح"
            
        except Exception as e:
            return None, f"خطأ في إنشاء PDF: {str(e)}"
    
    def _get_complete_delivery_order_data(self, delivery_order_id):
        """جلب بيانات أمر التسليم الكاملة مع جميع الحقول"""
        try:
            db_manager = DatabaseManager()
            
            # استعلام مبسط للحقول الموجودة فقط
            query = """
                SELECT
                    do.id,
                    do.order_number,
                    do.shipment_id,
                    do.customs_agent_id,
                    ca.agent_name,
                    ca.phone,
                    ca.mobile,
                    ca.email,
                    do.branch_id,
                    b.brn_lname as branch_name,
                    b.brn_ladd as branch_address,
                    do.created_date,
                    do.order_status,
                    cs.shipment_number,
                    cs.port_of_loading,
                    cs.port_of_discharge,
                    cs.shipment_status,
                    cc.container_number,
                    cc.container_type,
                    cc.seal_number,
                    cc.total_weight,
                    cc.net_weight
                FROM delivery_orders do
                LEFT JOIN customs_agents ca ON do.customs_agent_id = ca.id
                LEFT JOIN branches b ON do.branch_id = b.brn_no
                LEFT JOIN cargo_shipments cs ON do.shipment_id = cs.id
                LEFT JOIN cargo_containers cc ON cs.id = cc.cargo_shipment_id
                WHERE do.id = :delivery_order_id
            """
            
            result = db_manager.execute_query(query, {'delivery_order_id': delivery_order_id})
            
            if result:
                row = result[0]
                return {
                    'id': row[0],
                    'order_number': row[1],
                    'shipment_id': row[2],
                    'customs_agent_id': row[3],
                    'agent_name': row[4],
                    'agent_phone': row[5],
                    'agent_mobile': row[6],
                    'agent_email': row[7],
                    'branch_id': row[8],
                    'branch_name': row[9],
                    'branch_address': row[10],
                    'created_date': row[11],
                    'order_status': row[12],
                    'shipment_reference': row[13],
                    'loading_port': row[14],
                    'discharge_port': row[15],
                    'shipment_status': row[16],
                    'container_number': row[17],
                    'container_type': row[18],
                    'seal_number': row[19],
                    'total_weight': row[20],
                    'net_weight': row[21],
                    # حقول افتراضية للحقول غير الموجودة
                    'tracking_number': f"TRK-{row[2]}" if row[2] else 'غير محدد',
                    'booking_number': f"BK-{row[2]}" if row[2] else 'غير محدد',
                    'delivery_location': 'عدن، اليمن',
                    'expected_completion_date': row[11],  # نفس تاريخ الإنشاء
                    'special_instructions': None
                }
            
            db_manager.close()
            return None
            
        except Exception as e:
            print(f"خطأ في جلب بيانات أمر التسليم: {e}")
            return None

    def _add_exact_header(self, story, order_data):
        """إضافة هيدر مطابق للتصميم الأصلي مع الشعار"""
        font_name = 'Arabic' if self.arabic_font_available else 'Helvetica'

        # إضافة الشعار (إذا كان متاح)
        # يمكن إضافة الشعار هنا إذا كان متاح في النظام

        # اسم الشركة
        company_style = ParagraphStyle(
            'CompanyName',
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=5,
            textColor=self.colors['primary'],
            fontName=font_name
        )

        company_name = self.reshape_arabic_text(order_data.get('branch_name', 'شركة النقل والشحن المتطورة'))
        story.append(Paragraph(company_name, company_style))

        # عنوان المستند
        title_style = ParagraphStyle(
            'DocumentTitle',
            fontSize=16,
            alignment=TA_CENTER,
            spaceAfter=8,
            textColor=self.colors['secondary'],
            fontName=font_name
        )

        title = self.reshape_arabic_text("أمر تسليم للمخلص الجمركي")
        story.append(Paragraph(title, title_style))

        # معلومات الفرع
        if order_data.get('branch_address') or order_data.get('agent_phone'):
            branch_info_parts = []
            if order_data.get('branch_address'):
                branch_info_parts.append(order_data['branch_address'])
            if order_data.get('agent_phone'):
                branch_info_parts.append(f"هاتف: {order_data['agent_phone']}")

            if branch_info_parts:
                branch_style = ParagraphStyle(
                    'BranchInfo',
                    fontSize=12,
                    alignment=TA_CENTER,
                    spaceAfter=15,
                    textColor=self.colors['text_muted'],
                    fontName=font_name
                )

                branch_text = self.reshape_arabic_text(" | ".join(branch_info_parts))
                story.append(Paragraph(branch_text, branch_style))

        story.append(Spacer(1, 10))

    def _add_order_info_grid(self, story, order_data):
        """إضافة شبكة معلومات الأمر الأساسية (3 أعمدة)"""
        font_name = 'Arabic' if self.arabic_font_available else 'Helvetica'

        # ترجمة حالة الأمر
        status_map = {
            'draft': 'مسودة',
            'sent': 'مرسل',
            'in_progress': 'قيد التنفيذ',
            'completed': 'مكتمل',
            'cancelled': 'ملغي'
        }

        status_text = status_map.get(order_data.get('order_status', 'draft'), order_data.get('order_status', 'مسودة'))

        # بيانات الشبكة
        grid_data = [
            [
                self.reshape_arabic_text("رقم الأمر"),
                self.reshape_arabic_text("تاريخ الإصدار"),
                self.reshape_arabic_text("حالة الأمر")
            ],
            [
                order_data.get('order_number', 'غير محدد'),
                str(order_data.get('created_date', 'غير محدد')),
                self.reshape_arabic_text(status_text)
            ]
        ]

        grid_table = Table(grid_data, colWidths=[6*cm, 6*cm, 6*cm])
        grid_table.setStyle(TableStyle([
            # الصف الأول (العناوين)
            ('BACKGROUND', (0, 0), (-1, 0), self.colors['light_bg']),
            ('TEXTCOLOR', (0, 0), (-1, 0), self.colors['text_muted']),
            ('FONTNAME', (0, 0), (-1, 0), font_name),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('VALIGN', (0, 0), (-1, 0), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (-1, 0), 8),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),

            # الصف الثاني (القيم)
            ('BACKGROUND', (0, 1), (-1, 1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, 1), colors.black),
            ('FONTNAME', (0, 1), (-1, 1), font_name),
            ('FONTSIZE', (0, 1), (-1, 1), 13),
            ('ALIGN', (0, 1), (-1, 1), 'CENTER'),
            ('VALIGN', (0, 1), (-1, 1), 'MIDDLE'),
            ('TOPPADDING', (0, 1), (-1, 1), 10),
            ('BOTTOMPADDING', (0, 1), (-1, 1), 10),

            # الحدود
            ('GRID', (0, 0), (-1, -1), 1, self.colors['border']),
        ]))

        story.append(grid_table)
        story.append(Spacer(1, 15))

    def _add_basic_shipment_info(self, story, order_data):
        """إضافة المعلومات الأساسية للشحنة"""
        font_name = 'Arabic' if self.arabic_font_available else 'Helvetica'

        # عنوان القسم
        section_style = ParagraphStyle(
            'SectionTitle',
            fontSize=14,
            alignment=TA_RIGHT,
            spaceAfter=10,
            textColor=self.colors['primary'],
            fontName=font_name
        )

        section_title = self.reshape_arabic_text("📦 المعلومات الأساسية للشحنة")
        story.append(Paragraph(section_title, section_style))

        # بيانات الجدول
        basic_data = [
            # هيدر الجدول
            [self.reshape_arabic_text("البيان"), self.reshape_arabic_text("القيمة")],
            # البيانات
            [self.reshape_arabic_text("رقم التتبع"), order_data.get('tracking_number', 'غير محدد')],
            [self.reshape_arabic_text("رقم الحجز"), order_data.get('booking_number', 'غير محدد')],
            [self.reshape_arabic_text("نوع الشحنة"), self.reshape_arabic_text("بحري")],
            [self.reshape_arabic_text("الوزن الإجمالي"), f"{order_data.get('total_weight', 'غير محدد')} كيلو" if order_data.get('total_weight') else 'غير محدد'],
            [self.reshape_arabic_text("الوزن الصافي"), f"{order_data.get('net_weight', 'غير محدد')} كيلو" if order_data.get('net_weight') else 'غير محدد'],
            [self.reshape_arabic_text("موقع التسليم"), self.reshape_arabic_text(order_data.get('delivery_location', 'غير محدد'))],
            [self.reshape_arabic_text("التاريخ المطلوب للتخليص"), str(order_data.get('expected_completion_date', 'غير محدد'))],
        ]

        basic_table = Table(basic_data, colWidths=[7*cm, 11*cm])
        basic_table.setStyle(TableStyle([
            # هيدر الجدول
            ('BACKGROUND', (0, 0), (-1, 0), self.colors['success']),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), font_name),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('VALIGN', (0, 0), (-1, 0), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (-1, 0), 8),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),

            # باقي الصفوف
            ('FONTNAME', (0, 1), (-1, -1), font_name),
            ('FONTSIZE', (0, 1), (-1, -1), 11),
            ('ALIGN', (0, 1), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 1), (-1, -1), 'MIDDLE'),
            ('TOPPADDING', (0, 1), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 1), (-1, -1), 6),

            # الألوان المتناوبة
            ('BACKGROUND', (0, 1), (-1, 1), colors.white),
            ('BACKGROUND', (0, 2), (-1, 2), self.colors['light_bg']),
            ('BACKGROUND', (0, 3), (-1, 3), colors.white),
            ('BACKGROUND', (0, 4), (-1, 4), self.colors['light_bg']),
            ('BACKGROUND', (0, 5), (-1, 5), colors.white),
            ('BACKGROUND', (0, 6), (-1, 6), self.colors['light_bg']),
            ('BACKGROUND', (0, 7), (-1, 7), colors.white),

            # الحدود
            ('GRID', (0, 0), (-1, -1), 1, self.colors['border']),
        ]))

        story.append(basic_table)
        story.append(Spacer(1, 15))

    def _add_detailed_shipment_info(self, story, order_data):
        """إضافة بيانات الشحنة التفصيلية"""
        font_name = 'Arabic' if self.arabic_font_available else 'Helvetica'

        # عنوان القسم
        section_style = ParagraphStyle(
            'SectionTitle',
            fontSize=14,
            alignment=TA_RIGHT,
            spaceAfter=10,
            textColor=self.colors['primary'],
            fontName=font_name
        )

        section_title = self.reshape_arabic_text("🚢 بيانات الشحنة التفصيلية")
        story.append(Paragraph(section_title, section_style))

        # بيانات الجدول
        detailed_data = [
            # هيدر الجدول
            [self.reshape_arabic_text("البيان"), self.reshape_arabic_text("القيمة")],
            # البيانات
            [self.reshape_arabic_text("رقم الشحنة"), order_data.get('shipment_reference', 'غير محدد')],
            [self.reshape_arabic_text("ميناء الشحن"), self.reshape_arabic_text(order_data.get('loading_port', 'غير محدد'))],
            [self.reshape_arabic_text("ميناء التفريغ"), self.reshape_arabic_text(order_data.get('discharge_port', 'غير محدد'))],
            [self.reshape_arabic_text("حالة الشحنة"), self.reshape_arabic_text(order_data.get('shipment_status', 'غير محدد'))],
            [self.reshape_arabic_text("رقم الحاوية"), order_data.get('container_number', 'غير محدد')],
            [self.reshape_arabic_text("نوع الحاوية"), order_data.get('container_type', 'غير محدد')],
            [self.reshape_arabic_text("رقم الختم"), order_data.get('seal_number', 'غير محدد')],
        ]

        detailed_table = Table(detailed_data, colWidths=[7*cm, 11*cm])
        detailed_table.setStyle(TableStyle([
            # هيدر الجدول
            ('BACKGROUND', (0, 0), (-1, 0), self.colors['info']),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), font_name),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('VALIGN', (0, 0), (-1, 0), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (-1, 0), 8),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),

            # باقي الصفوف
            ('FONTNAME', (0, 1), (-1, -1), font_name),
            ('FONTSIZE', (0, 1), (-1, -1), 11),
            ('ALIGN', (0, 1), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 1), (-1, -1), 'MIDDLE'),
            ('TOPPADDING', (0, 1), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 1), (-1, -1), 6),

            # الألوان المتناوبة
            ('BACKGROUND', (0, 1), (-1, 1), colors.white),
            ('BACKGROUND', (0, 2), (-1, 2), self.colors['light_bg']),
            ('BACKGROUND', (0, 3), (-1, 3), colors.white),
            ('BACKGROUND', (0, 4), (-1, 4), self.colors['light_bg']),
            ('BACKGROUND', (0, 5), (-1, 5), colors.white),
            ('BACKGROUND', (0, 6), (-1, 6), self.colors['light_bg']),
            ('BACKGROUND', (0, 7), (-1, 7), colors.white),

            # الحدود
            ('GRID', (0, 0), (-1, -1), 1, self.colors['border']),
        ]))

        story.append(detailed_table)
        story.append(Spacer(1, 15))

        # إضافة معلومات المخلص الجمركي
        self._add_agent_info_table(story, order_data)

    def _add_agent_info_table(self, story, order_data):
        """إضافة جدول معلومات المخلص الجمركي"""
        font_name = 'Arabic' if self.arabic_font_available else 'Helvetica'

        # عنوان القسم
        section_style = ParagraphStyle(
            'SectionTitle',
            fontSize=14,
            alignment=TA_RIGHT,
            spaceAfter=10,
            textColor=self.colors['primary'],
            fontName=font_name
        )

        section_title = self.reshape_arabic_text("👤 معلومات المخلص الجمركي")
        story.append(Paragraph(section_title, section_style))

        # بيانات الجدول
        agent_data = [
            # هيدر الجدول
            [self.reshape_arabic_text("البيان"), self.reshape_arabic_text("القيمة")],
            # البيانات
            [self.reshape_arabic_text("اسم المخلص"), self.reshape_arabic_text(order_data.get('agent_name', 'غير محدد'))],
            [self.reshape_arabic_text("رقم الهاتف"), order_data.get('agent_phone', 'غير محدد')],
            [self.reshape_arabic_text("رقم الجوال"), order_data.get('agent_mobile', 'غير محدد')],
            [self.reshape_arabic_text("البريد الإلكتروني"), order_data.get('agent_email', 'غير محدد')],
        ]

        agent_table = Table(agent_data, colWidths=[7*cm, 11*cm])
        agent_table.setStyle(TableStyle([
            # هيدر الجدول
            ('BACKGROUND', (0, 0), (-1, 0), self.colors['warning']),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('FONTNAME', (0, 0), (-1, 0), font_name),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('VALIGN', (0, 0), (-1, 0), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (-1, 0), 8),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),

            # باقي الصفوف
            ('FONTNAME', (0, 1), (-1, -1), font_name),
            ('FONTSIZE', (0, 1), (-1, -1), 11),
            ('ALIGN', (0, 1), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 1), (-1, -1), 'MIDDLE'),
            ('TOPPADDING', (0, 1), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 1), (-1, -1), 6),

            # الألوان المتناوبة
            ('BACKGROUND', (0, 1), (-1, 1), colors.white),
            ('BACKGROUND', (0, 2), (-1, 2), self.colors['light_bg']),
            ('BACKGROUND', (0, 3), (-1, 3), colors.white),
            ('BACKGROUND', (0, 4), (-1, 4), self.colors['light_bg']),

            # الحدود
            ('GRID', (0, 0), (-1, -1), 1, self.colors['border']),
        ]))

        story.append(agent_table)
        story.append(Spacer(1, 15))

    def _add_special_instructions(self, story, order_data):
        """إضافة التعليمات الخاصة إذا وجدت"""
        if order_data.get('special_instructions'):
            font_name = 'Arabic' if self.arabic_font_available else 'Helvetica'

            # عنوان التعليمات
            instructions_style = ParagraphStyle(
                'InstructionsTitle',
                fontSize=12,
                alignment=TA_RIGHT,
                spaceAfter=8,
                textColor=colors.Color(0.52, 0.39, 0.02),  # #856404
                fontName=font_name
            )

            instructions_title = self.reshape_arabic_text("📝 تعليمات خاصة")
            story.append(Paragraph(instructions_title, instructions_style))

            # محتوى التعليمات
            instructions_content_style = ParagraphStyle(
                'InstructionsContent',
                fontSize=11,
                alignment=TA_RIGHT,
                spaceAfter=15,
                textColor=colors.black,
                fontName=font_name,
                backColor=colors.Color(1.0, 0.95, 0.80),  # #fff3cd
                borderColor=colors.Color(1.0, 0.92, 0.65),  # #ffeaa7
                borderWidth=1,
                borderPadding=10
            )

            instructions_text = self.reshape_arabic_text(order_data['special_instructions'])
            story.append(Paragraph(instructions_text, instructions_content_style))

    def _add_exact_footer(self, story, order_data):
        """إضافة فوتر مطابق للتصميم الأصلي"""
        font_name = 'Arabic' if self.arabic_font_available else 'Helvetica'

        # خط فاصل
        line_table = Table([['']], colWidths=[18*cm])
        line_table.setStyle(TableStyle([
            ('LINEABOVE', (0, 0), (-1, -1), 1, self.colors['primary']),
            ('TOPPADDING', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 0),
        ]))
        story.append(line_table)

        # معلومات الشركة
        footer_style = ParagraphStyle(
            'Footer',
            fontSize=10,
            alignment=TA_CENTER,
            textColor=self.colors['text_muted'],
            fontName=font_name,
            leading=12
        )

        # بناء معلومات الفوتر
        footer_parts = []

        # اسم الشركة
        company_name = order_data.get('branch_name', 'شركة النقل والشحن المتطورة')
        footer_parts.append(f"<b>{self.reshape_arabic_text(company_name)}</b>")

        # معلومات الاتصال
        contact_parts = []
        if order_data.get('agent_phone'):
            contact_parts.append(f"الهاتف: {order_data['agent_phone']}")
        if order_data.get('agent_mobile'):
            contact_parts.append(f"جوال: {order_data['agent_mobile']}")

        if contact_parts:
            footer_parts.append(" | ".join(contact_parts))

        # العنوان
        if order_data.get('branch_address'):
            footer_parts.append(f"العنوان: {self.reshape_arabic_text(order_data['branch_address'])}")

        # تاريخ الطباعة
        print_date = datetime.now().strftime('%Y-%m-%d %H:%M')
        footer_parts.append(f"تاريخ الطباعة: {print_date}")

        footer_text = "<br/>".join(footer_parts)
        story.append(Paragraph(footer_text, footer_style))


# إنشاء instance عام للمولد
exact_design_pdf_generator = ExactDesignPDFGenerator()


def generate_exact_design_pdf(delivery_order_id):
    """دالة مساعدة لإنشاء PDF مطابق للتصميم الأصلي"""
    return exact_design_pdf_generator.generate_exact_design_pdf(delivery_order_id)
