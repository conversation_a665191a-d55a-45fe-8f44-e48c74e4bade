// Service Worker للنظام المحاسبي المتقدم
const CACHE_NAME = 'accounting-system-v1.0.1';
const urlsToCache = [
    '/',
    '/dashboard',
    '/inventory/',
    '/suppliers/',
    '/purchase-requests/',
    '/purchase-orders/',
    '/purchase-contracts/',
    '/goods-receipt/',
    '/financial/',
    '/workflow/',
    '/static/css/bootstrap.min.css',
    '/static/js/bootstrap.bundle.min.js',
    '/static/css/mobile.css',
    '/static/js/mobile.js',
    '/static/js/app.js',
    '/static/icons/icon-192x192.png',
    '/static/icons/icon-512x512.png',
    '/manifest.json'
];

// تثبيت Service Worker
self.addEventListener('install', function(event) {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                console.log('تم فتح التخزين المؤقت');
                return cache.addAll(urlsToCache);
            })
    );
});

// تفعيل Service Worker
self.addEventListener('activate', function(event) {
    event.waitUntil(
        caches.keys().then(function(cacheNames) {
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    if (cacheName !== CACHE_NAME) {
                        console.log('حذف التخزين المؤقت القديم:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
});

// اعتراض الطلبات
self.addEventListener('fetch', function(event) {
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                if (response) {
                    return response;
                }
                return fetch(event.request);
            }
        )
    );
});

// معالجة الرسائل
self.addEventListener('message', function(event) {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});

// إشعارات Push
self.addEventListener('push', function(event) {
    const options = {
        body: 'رسالة جديدة من النظام المحاسبي',
        icon: '/static/icons/icon-192x192.png',
        badge: '/static/icons/icon-192x192.png'
    };

    event.waitUntil(
        self.registration.showNotification('النظام المحاسبي', options)
    );
});

// معالجة النقر على الإشعارات
self.addEventListener('notificationclick', function(event) {
    event.notification.close();
    event.waitUntil(clients.openWindow('/'));
});
