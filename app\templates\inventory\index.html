{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-boxes text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">إدارة وعرض بيانات الأصناف</p>
                </div>
                <div>
                    <a href="{{ url_for('inventory.import_items') }}" class="btn btn-success me-2">
                        <i class="fas fa-download me-1"></i>
                        استيراد الأصناف
                    </a>
                    <button class="btn btn-primary" onclick="refreshData()">
                        <i class="fas fa-sync-alt me-1"></i>
                        تحديث البيانات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-boxes text-primary fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">إجمالي الأصناف</h6>
                            <h4 class="mb-0">{{ stats.total_items or 0 }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-check-circle text-success fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">الأصناف النشطة</h6>
                            <h4 class="mb-0">{{ stats.active_items or 0 }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-exclamation-triangle text-warning fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">مخزون منخفض</h6>
                            <h4 class="mb-0">{{ stats.low_stock_items or 0 }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-danger bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-times-circle text-danger fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">نفد المخزون</h6>
                            <h4 class="mb-0">{{ stats.out_of_stock_items or 0 }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">البحث</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="{{ search_term or '' }}" 
                                       placeholder="البحث بالاسم أو الكود...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>
                                    مسح الفلاتر
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Items Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة الأصناف
                        </h5>
                        {% if items and items.total > 0 %}
                        <span class="badge bg-primary">
                            {{ items.total }} صنف
                        </span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if items and items.items %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>كود الصنف</th>
                                    <th>اسم الصنف</th>
                                    <th>الوصف</th>
                                    <th>وحدة القياس</th>
                                    <th>المخزون الحالي</th>
                                    <th>الحد الأدنى</th>
                                    <th>سعر الوحدة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in items.items %}
                                <tr>
                                    <td>
                                        <span class="fw-bold text-primary">{{ item.item_code }}</span>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-semibold">{{ item.item_name_ar or 'غير محدد' }}</div>
                                            {% if item.item_name_en %}
                                            <small class="text-muted">{{ item.item_name_en }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ item.description or '-' }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ item.unit_of_measure or '-' }}</span>
                                    </td>
                                    <td>
                                        {% if item.current_stock <= 0 %}
                                        <span class="badge bg-danger">{{ item.current_stock }}</span>
                                        {% elif item.current_stock <= item.min_stock_level %}
                                        <span class="badge bg-warning">{{ item.current_stock }}</span>
                                        {% else %}
                                        <span class="badge bg-success">{{ item.current_stock }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ item.min_stock_level or 0 }}</span>
                                    </td>
                                    <td>
                                        <span class="fw-semibold">{{ "%.2f"|format(item.unit_price or 0) }}</span>
                                    </td>
                                    <td>
                                        {% if item.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                        {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('inventory.view_item', item_id=item.id) }}" 
                                               class="btn btn-outline-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد أصناف</h5>
                        <p class="text-muted">لم يتم العثور على أي أصناف في قاعدة البيانات</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshData() {
    window.location.reload();
}
</script>
{% endblock %}
