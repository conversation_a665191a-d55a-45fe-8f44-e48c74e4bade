# -*- coding: utf-8 -*-
"""
خدمة الأتمتة المدمجة في Flask
Flask Integrated Automation Service
"""

import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json
import os

class FlaskAutomationService:
    """خدمة الأتمتة المدمجة في Flask"""
    
    def __init__(self):
        """تهيئة الخدمة"""
        self.running = False
        self.automation_thread = None
        self.logger = self.setup_logging()
        
        # إعدادات افتراضية
        self.settings = {
            'auto_create_orders': True,
            'auto_send_notifications': True,
            'check_interval_minutes': 1,  # فحص كل دقيقة للاستجابة السريعة
            'working_hours_start': '08:00',
            'working_hours_end': '18:00',
            'weekend_enabled': False,
            'max_orders_per_batch': 10,
            'retry_failed_orders': True,
            'retry_attempts': 3,
            'always_enabled_for_testing': True  # للاختبار: تعمل دائماً
        }
        
        # إحصائيات
        self.statistics = {
            'total_processed': 0,
            'total_success': 0,
            'total_errors': 0,
            'last_run': None,
            'last_error': None
        }
        
        # تحميل الإعدادات المحفوظة
        self.load_settings_from_file()

        self.logger.info("🚀 تم تهيئة خدمة الأتمتة المدمجة")
    
    def setup_logging(self):
        """إعداد نظام السجلات"""
        logger = logging.getLogger('FlaskAutomationService')
        logger.setLevel(logging.INFO)
        
        # إنشاء handler إذا لم يكن موجوداً
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def is_working_hours(self) -> bool:
        """فحص إذا كان الوقت الحالي ضمن ساعات العمل"""
        try:
            # للاختبار: نعمل دائماً إذا كانت الخدمة مفعلة
            if self.settings.get('always_enabled_for_testing', True):
                return True

            now = datetime.now()

            # فحص نهاية الأسبوع
            if not self.settings.get('weekend_enabled', False):
                if now.weekday() >= 5:  # السبت والأحد
                    return False

            # فحص ساعات العمل
            start_time = datetime.strptime(self.settings.get('working_hours_start', '08:00'), '%H:%M').time()
            end_time = datetime.strptime(self.settings.get('working_hours_end', '18:00'), '%H:%M').time()

            current_time = now.time()

            return start_time <= current_time <= end_time

        except Exception as e:
            self.logger.error(f"❌ خطأ في فحص ساعات العمل: {e}")
            return True  # افتراضياً نعمل
    
    def get_pending_shipments(self) -> List[Dict]:
        """جلب الشحنات المعلقة من قاعدة البيانات الحقيقية"""
        try:
            # محاولة جلب البيانات الحقيقية من قاعدة البيانات
            try:
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
                from database_manager import DatabaseManager

                db_manager = DatabaseManager()

                # استعلام الشحنات المعلقة (بدون customs_agent_id لأنه غير موجود في cargo_shipments)
                query = """
                    SELECT * FROM (
                        SELECT DISTINCT
                            cs.id,
                            cs.shipment_number,
                            1 as customs_agent_id,
                            'مخلص افتراضي' as agent_name,
                            '+966501234567' as agent_phone,
                            '+966501234567' as agent_mobile,
                            cs.port_of_discharge,
                            cs.shipment_status,
                            cs.created_at as arrival_date
                        FROM cargo_shipments cs
                        WHERE cs.shipment_status = 'arrived_port'
                        AND (cs.port_of_discharge LIKE '%عدن%'
                             OR cs.port_of_discharge LIKE '%Aden%'
                             OR cs.port_of_discharge = 'عدن')
                        AND cs.id NOT IN (
                            SELECT DISTINCT shipment_id
                            FROM delivery_orders
                            WHERE shipment_id IS NOT NULL
                        )
                        ORDER BY cs.created_at ASC
                    ) WHERE ROWNUM <= 5
                """

                result = db_manager.execute_query(query)

                shipments = []
                if result:
                    for row in result:
                        shipments.append({
                            'shipment_id': row[0],
                            'shipment_number': row[1],
                            'customs_agent_id': row[2],
                            'agent_name': row[3] or 'مخلص غير محدد',
                            'agent_phone': row[4],
                            'agent_mobile': row[5],
                            'port_of_discharge': row[6] or 'جدة',
                            'shipment_status': row[7],
                            'arrival_date': row[8]
                        })

                db_manager.close()
                self.logger.info(f"📦 تم العثور على {len(shipments)} شحنة معلقة من قاعدة البيانات")
                return shipments

            except Exception as db_error:
                self.logger.warning(f"⚠️ فشل في جلب البيانات من قاعدة البيانات: {db_error}")

                # العودة للمحاكاة في حالة فشل قاعدة البيانات
                pending_shipments = [
                    {
                        'shipment_id': 999,
                        'shipment_number': 'SH-TEST-001',
                        'customs_agent_id': 1,
                        'agent_name': 'مخلص تجريبي',
                        'agent_phone': '+966501234567',
                        'agent_mobile': '+966501234567',
                        'port_of_discharge': 'جدة',
                        'shipment_status': 'arrived',
                        'arrival_date': datetime.now() - timedelta(days=1)
                    }
                ]

                self.logger.info(f"📦 استخدام بيانات تجريبية: {len(pending_shipments)} شحنة")
                return pending_shipments

        except Exception as e:
            self.logger.error(f"❌ خطأ في جلب الشحنات المعلقة: {e}")
            return []
    
    def create_delivery_order(self, shipment_data: Dict) -> Optional[int]:
        """إنشاء أمر تسليم حقيقي في قاعدة البيانات"""
        try:
            # محاولة إنشاء أمر تسليم حقيقي
            try:
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
                from database_manager import DatabaseManager

                db_manager = DatabaseManager()

                # الحصول على أكبر ID موجود
                max_id_query = "SELECT NVL(MAX(id), 0) + 1 FROM delivery_orders"
                max_id_result = db_manager.execute_query(max_id_query)
                new_order_id = max_id_result[0][0] if max_id_result else 1

                # إنشاء رقم أمر تسليم جديد
                order_number = f"DO-{datetime.now().strftime('%Y%m%d')}-{shipment_data['shipment_id']:06d}"

                # إدراج أمر التسليم مع ID
                insert_query = """
                    INSERT INTO delivery_orders (
                        id,
                        order_number,
                        shipment_id,
                        customs_agent_id,
                        branch_id,
                        order_status,
                        priority,
                        created_date,
                        expected_completion_date
                    ) VALUES (
                        :order_id,
                        :order_number,
                        :shipment_id,
                        :customs_agent_id,
                        1,
                        'draft',
                        'normal',
                        SYSDATE,
                        SYSDATE + 3
                    )
                """

                params = {
                    'order_id': new_order_id,
                    'order_number': order_number,
                    'shipment_id': shipment_data['shipment_id'],
                    'customs_agent_id': shipment_data['customs_agent_id']
                }

                db_manager.execute_query(insert_query, params)
                db_manager.close()

                self.logger.info(f"✅ تم إنشاء أمر تسليم حقيقي: {order_number} (ID: {new_order_id})")
                return new_order_id

            except Exception as db_error:
                self.logger.warning(f"⚠️ فشل في إنشاء أمر التسليم في قاعدة البيانات: {db_error}")

                # محاكاة في حالة فشل قاعدة البيانات
                order_id = 99999
                order_number = f"DO-TEST-{datetime.now().strftime('%Y%m%d')}-{shipment_data['shipment_id']:06d}"

                self.logger.info(f"✅ تم إنشاء أمر تسليم تجريبي: {order_number} (ID: {order_id})")
                return order_id

        except Exception as e:
            self.logger.error(f"❌ خطأ في إنشاء أمر التسليم: {e}")
            return None
    
    def send_delivery_order_notification(self, order_id: int, shipment_data: Dict) -> bool:
        """إرسال إشعار أمر التسليم عبر الواتساب (نفس آلية الاختبار الناجح)"""
        try:
            # تحديد رقم الهاتف (أولوية للموبايل، ثم الهاتف)
            phone = shipment_data.get('agent_mobile') or shipment_data.get('agent_phone')

            if not phone:
                self.logger.warning(f"⚠️ لا يوجد رقم هاتف للمخلص: {shipment_data['agent_name']}")
                return False

            try:
                # تعيين إعدادات Green API (نفس الاختبار الناجح)
                import os
                os.environ['GREEN_API_INSTANCE_ID'] = '7105306929'
                os.environ['GREEN_API_TOKEN'] = '0260de395caa4adfb2aa02d53511f9ffa19b29b41f094fd68c'
                os.environ['GREEN_API_URL'] = 'https://7105.api.greenapi.com'
                os.environ['GREEN_API_TEST_MODE'] = 'false'

                # استيراد خدمة الواتساب
                from app.services.green_whatsapp_service import green_whatsapp_service

                # إعداد بيانات أمر التسليم (نفس بنية الاختبار الناجح)
                order_data = {
                    'id': order_id,
                    'order_number': f"DO-{datetime.now().strftime('%Y%m%d')}-{shipment_data['shipment_id']:06d}",
                    'booking_number': shipment_data['shipment_number'],
                    'delivery_location': shipment_data['port_of_discharge'],
                    'expected_completion_date': '2025-08-25',
                    'contact_person': shipment_data['agent_name'],
                    'contact_phone': phone
                }

                self.logger.info(f"🚀 إرسال إلى المخلص: {shipment_data['agent_name']}")
                self.logger.info(f"📱 الرقم: {phone}")

                # إرسال الإشعار مع PDF (نفس طريقة الاختبار الناجح)
                success, message, msg_id = green_whatsapp_service.send_delivery_order(
                    order_data,
                    phone,
                    include_pdf=True
                )

                if success:
                    self.logger.info(f"✅ نجح الإرسال!")
                    self.logger.info(f"📝 الرسالة: {message}")
                    self.logger.info(f"🆔 معرف الرسالة: {msg_id}")
                    self.logger.info(f"📱 تحقق من الواتساب على الرقم: {phone}")
                    return True
                else:
                    self.logger.error(f"❌ فشل الإرسال!")
                    self.logger.error(f"📝 السبب: {message}")
                    return False

            except Exception as whatsapp_error:
                self.logger.error(f"❌ خطأ في خدمة الواتساب: {whatsapp_error}")
                return False

        except Exception as e:
            self.logger.error(f"❌ خطأ عام في إرسال إشعار أمر التسليم: {e}")
            return False

    def send_whatsapp_like_test(self, order_id, order_number, shipment_number, port_of_discharge, agent_name, phone):
        """إرسال واتساب مطابق للاختبار الناجح تماماً"""
        try:
            # تعيين إعدادات Green API (نفس الاختبار)
            import os
            os.environ['GREEN_API_INSTANCE_ID'] = '7105306929'
            os.environ['GREEN_API_TOKEN'] = '0260de395caa4adfb2aa02d53511f9ffa19b29b41f094fd68c'
            os.environ['GREEN_API_URL'] = 'https://7105.api.greenapi.com'
            os.environ['GREEN_API_TEST_MODE'] = 'false'

            # استيراد خدمة الواتساب
            from app.services.green_whatsapp_service import green_whatsapp_service

            # إعداد بيانات الأمر (نفس بنية الاختبار الناجح)
            order_data = {
                'id': order_id,
                'order_number': order_number,
                'booking_number': shipment_number,
                'delivery_location': port_of_discharge or 'جدة',
                'expected_completion_date': '2025-08-25',
                'contact_person': agent_name or 'مخلص',
                'contact_phone': phone
            }

            self.logger.info(f"🚀 إرسال إلى المخلص: {agent_name}")
            self.logger.info(f"📱 الرقم: {phone}")

            # إرسال مع PDF (نفس طريقة الاختبار الناجح)
            success, message, msg_id = green_whatsapp_service.send_delivery_order(
                order_data,
                phone,
                include_pdf=True
            )

            if success:
                self.logger.info(f"✅ نجح الإرسال!")
                self.logger.info(f"📝 الرسالة: {message}")
                self.logger.info(f"🆔 معرف الرسالة: {msg_id}")
                self.logger.info(f"📱 تحقق من الواتساب على الرقم: {phone}")
                return True
            else:
                self.logger.error(f"❌ فشل الإرسال!")
                self.logger.error(f"📝 السبب: {message}")
                return False

        except Exception as e:
            self.logger.error(f"❌ خطأ في إرسال الواتساب: {e}")
            return False

    def _send_notification_using_automation_engine(self, db_manager, shipment_id, target_status, rule_id):
        """إرسال إشعار باستخدام محرك الأتمتة الجديد"""
        try:
            self.logger.info(f"🔥 FLASK: استخدام محرك الأتمتة الجديد للشحنة {shipment_id}")

            # استيراد محرك الأتمتة
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shipments'))
            from automation_engine import AutomationEngine

            # إنشاء محرك الأتمتة
            engine = AutomationEngine()

            # جلب بيانات الشحنة
            shipment_query = """
                SELECT id, tracking_number, shipment_status
                FROM cargo_shipments
                WHERE id = :shipment_id
            """

            shipment_data_result = db_manager.execute_query(shipment_query, {'shipment_id': shipment_id})

            if not shipment_data_result:
                self.logger.error(f"❌ FLASK: لم يتم العثور على الشحنة {shipment_id}")
                return False

            ship_id, tracking_number, current_status = shipment_data_result[0]

            # جلب القاعدة
            rule_query = """
                SELECT id, rule_name, trigger_condition, condition_value, action_type, rule_config
                FROM automation_rules
                WHERE id = :rule_id
            """

            rule_result = db_manager.execute_query(rule_query, {'rule_id': rule_id})

            if not rule_result:
                self.logger.error(f"❌ FLASK: لم يتم العثور على القاعدة {rule_id}")
                return False

            rule_data = {
                'id': rule_result[0][0],
                'rule_name': rule_result[0][1],
                'trigger_condition': rule_result[0][2],
                'condition_value': rule_result[0][3],
                'action_type': rule_result[0][4],
                'rule_config': rule_result[0][5]
            }

            # بيانات الشحنة للأتمتة
            shipment_automation_data = {
                'shipment_id': shipment_id,
                'old_status': 'arrived_port',  # افتراضي
                'new_status': target_status,
                'current_status': current_status,
                'tracking_number': tracking_number
            }

            # تنفيذ الإجراء
            self.logger.info(f"🚀 FLASK: تنفيذ قاعدة {rule_data['rule_name']} للشحنة {tracking_number}")
            result = engine.execute_action(rule_data, shipment_automation_data)

            if result and result.get('success'):
                self.logger.info(f"✅ FLASK: نجح إرسال الإشعار - {result.get('notifications_sent', 0)} إشعار")
                return True
            else:
                self.logger.error(f"❌ FLASK: فشل إرسال الإشعار - {result.get('message', 'خطأ غير معروف')}")
                return False

        except Exception as e:
            self.logger.error(f"❌ FLASK: خطأ في إرسال الإشعار باستخدام محرك الأتمتة: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def process_automation_cycle(self):
        """دورة معالجة الأتمتة الرئيسية - إنشاء أوامر للشحنات الواصلة + إرسال الأوامر المعلقة"""
        try:
            if not self.is_working_hours():
                self.logger.info("⏰ خارج ساعات العمل - تخطي دورة الأتمتة")
                return

            self.logger.info("🔄 بدء دورة الأتمتة الجديدة")
            self.statistics['last_run'] = datetime.now()

            # حفظ حالة الخدمة
            self._save_service_status()

            try:
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
                from database_manager import DatabaseManager

                db_manager = DatabaseManager()

                # الخطوة 1: معالجة طابور الأتمتة أولاً
                self.logger.info("📋 معالجة طابور الأتمتة...")

                # معالجة العناصر المعلقة في طابور الأتمتة
                queue_processed = self._process_automation_queue(db_manager)

                # الخطوة 2: إنشاء أوامر التسليم للشحنات الواصلة (بدون إرسال)
                self.logger.info("📋 فحص الشحنات الواصلة لإنشاء أوامر التسليم...")

                # جلب الشحنات الواصلة التي لا تملك أوامر تسليم
                arrived_shipments_query = """
                    SELECT
                        cs.id,
                        cs.shipment_number,
                        cs.port_of_discharge,
                        cs.shipment_status
                    FROM cargo_shipments cs
                    WHERE cs.shipment_status = 'arrived_port'
                    AND (cs.port_of_discharge LIKE '%عدن%'
                         OR cs.port_of_discharge LIKE '%Aden%'
                         OR cs.port_of_discharge = 'عدن')
                    AND cs.id NOT IN (
                        SELECT DISTINCT shipment_id
                        FROM delivery_orders
                        WHERE shipment_id IS NOT NULL
                    )
                    ORDER BY cs.created_at ASC
                    FETCH FIRST 5 ROWS ONLY
                """

                arrived_shipments = db_manager.execute_query(arrived_shipments_query)

                if arrived_shipments:
                    self.logger.info(f"📦 وجد {len(arrived_shipments)} شحنة واصلة تحتاج أوامر تسليم")

                    for shipment in arrived_shipments:
                        try:
                            shipment_id, shipment_number, port_of_discharge, status = shipment

                            # إنشاء أمر تسليم للشحنة
                            self.logger.info(f"📋 إنشاء أمر تسليم للشحنة {shipment_number}")

                            # استخدام المخلص الافتراضي (ID = 4)
                            success = self._create_delivery_order_for_shipment(db_manager, shipment_id, 4)

                            if success:
                                self.logger.info(f"✅ تم إنشاء أمر تسليم للشحنة {shipment_number}")
                            else:
                                self.logger.warning(f"⚠️ فشل في إنشاء أمر تسليم للشحنة {shipment_number}")

                        except Exception as e:
                            self.logger.error(f"❌ خطأ في معالجة الشحنة {shipment[1]}: {e}")
                            continue
                else:
                    self.logger.info("📦 لا توجد شحنات واصلة تحتاج أوامر تسليم")

                rules = []  # تعطيل باقي النظام القديم لتجنب التكرار

                if rules:
                    self.logger.info(f"📋 وجد {len(rules)} قاعدة أتمتة نشطة")

                    # جلب الشحنات الواصلة
                    new_shipments_query = """
                        SELECT
                            cs.id,
                            cs.shipment_number,
                            cs.port_of_discharge,
                            cs.shipment_status
                        FROM cargo_shipments cs
                        WHERE cs.shipment_status = 'arrived_port'
                        AND (cs.port_of_discharge LIKE '%عدن%'
                             OR cs.port_of_discharge LIKE '%Aden%'
                             OR cs.port_of_discharge = 'عدن')
                        AND cs.id NOT IN (
                            SELECT DISTINCT shipment_id
                            FROM delivery_orders
                            WHERE shipment_id IS NOT NULL
                        )
                        ORDER BY cs.created_at ASC
                        FETCH FIRST 5 ROWS ONLY
                    """

                    new_shipments = db_manager.execute_query(new_shipments_query)

                    if new_shipments:
                        self.logger.info(f"📦 وجد {len(new_shipments)} شحنة واصلة جديدة")

                        for shipment_row in new_shipments:
                            try:
                                shipment_id = shipment_row[0]
                                shipment_number = shipment_row[1]
                                port_of_discharge = shipment_row[2]

                                # تطبيق القواعد على هذه الشحنة
                                for rule_row in rules:
                                    rule_id = rule_row[0]
                                    rule_name = rule_row[1]
                                    trigger_condition = rule_row[2]
                                    action_type = rule_row[3]
                                    selected_agent_id = rule_row[4]

                                    # فحص شرط التفعيل (مبسط)
                                    if self.check_rule_condition(trigger_condition, shipment_row):
                                        self.logger.info(f"✅ تطبيق قاعدة '{rule_name}' على شحنة {shipment_number}")

                                        if action_type.upper() in ['CREATE_DELIVERY_ORDER', 'CREATE_DELIVERY_ORDER_WITH_AGENT']:
                                            # إنشاء أمر تسليم باستخدام المخلص من القاعدة
                                            order_id = self.create_delivery_order_with_rule(
                                                shipment_id, shipment_number, port_of_discharge,
                                                selected_agent_id, db_manager
                                            )

                                            if order_id:
                                                self.logger.info(f"✅ تم إنشاء أمر تسليم للشحنة {shipment_number} باستخدام قاعدة {rule_name}")
                                                break  # تطبيق قاعدة واحدة فقط لكل شحنة
                                            else:
                                                self.logger.error(f"❌ فشل في إنشاء أمر تسليم للشحنة {shipment_number}")

                            except Exception as e:
                                self.logger.error(f"❌ خطأ في معالجة شحنة {shipment_row[1]}: {e}")
                                continue
                    else:
                        self.logger.info("📦 لا توجد شحنات واصلة جديدة")
                else:
                    self.logger.warning("⚠️ لا توجد قواعد أتمتة نشطة!")

                # الخطوة 2: إرسال أوامر التسليم المعلقة
                self.logger.info("📋 البحث عن أوامر تسليم معلقة للإرسال...")

                pending_orders_query = """
                    SELECT
                        do.id,
                        do.order_number,
                        do.shipment_id,
                        do.order_status,
                        do.created_date,
                        ca.agent_name,
                        ca.phone,
                        ca.mobile,
                        cs.shipment_number,
                        cs.port_of_discharge
                    FROM delivery_orders do
                    LEFT JOIN customs_agents ca ON do.customs_agent_id = ca.id
                    LEFT JOIN cargo_shipments cs ON do.shipment_id = cs.id
                    WHERE do.order_status = 'pending'
                    AND do.sent_date IS NULL
                    ORDER BY do.created_date DESC
                    FETCH FIRST 5 ROWS ONLY
                """

                pending_orders = db_manager.execute_query(pending_orders_query)

                if pending_orders:
                    self.logger.info(f"📦 وجد {len(pending_orders)} أمر تسليم للإرسال")

                    processed_count = 0
                    success_count = 0

                    for row in pending_orders:
                        try:
                            order_id = row[0]
                            order_number = row[1]
                            agent_name = row[5]
                            agent_phone = row[6]
                            agent_mobile = row[7]
                            shipment_number = row[8]
                            port_of_discharge = row[9]

                            # فحص إضافي لمنع الإرسال المكرر
                            double_check_query = """
                                SELECT sent_date FROM delivery_orders
                                WHERE id = :order_id AND sent_date IS NOT NULL
                            """

                            already_sent = db_manager.execute_query(double_check_query, {'order_id': order_id})
                            if already_sent:
                                self.logger.info(f"⚠️ تم تخطي أمر {order_number} - تم إرساله بالفعل")
                                continue

                            # تحديد الرقم المناسب للإرسال
                            phone_to_use = agent_mobile or agent_phone

                            if phone_to_use:
                                self.logger.info(f"📱 إرسال أمر {order_number} إلى {agent_name}: {phone_to_use}")

                                # إرسال الواتساب
                                if self.send_whatsapp_like_test(order_id, order_number, shipment_number, port_of_discharge, agent_name, phone_to_use):
                                    success_count += 1
                                    # تحديث حالة الأمر
                                    try:
                                        update_query = "UPDATE delivery_orders SET sent_date = SYSDATE WHERE id = :order_id"
                                        db_manager.execute_update(update_query, {'order_id': order_id})
                                        self.logger.info(f"✅ تم تحديث حالة الأمر {order_id}")
                                    except Exception as update_error:
                                        self.logger.warning(f"⚠️ فشل في تحديث حالة الأمر {order_id}: {update_error}")
                                    self.logger.info(f"✅ تم إرسال أمر {order_number} بنجاح")
                                else:
                                    self.logger.error(f"❌ فشل في إرسال أمر {order_number}")

                                processed_count += 1
                            else:
                                self.logger.warning(f"⚠️ لا يوجد رقم هاتف للمخلص في أمر {order_number}")

                            # انتظار قصير بين الإرسالات
                            time.sleep(2)

                        except Exception as e:
                            self.logger.error(f"❌ خطأ في معالجة أمر {row[1]}: {e}")
                            self.statistics['total_errors'] += 1
                            continue

                    # تحديث الإحصائيات
                    self.statistics['total_processed'] += processed_count
                    self.statistics['total_success'] += success_count

                    self.logger.info(f"🎉 انتهت دورة الأتمتة - معالج: {processed_count}, نجح: {success_count}")
                else:
                    self.logger.info("📦 لا توجد أوامر تسليم للإرسال")

                db_manager.close()

            except Exception as db_error:
                self.logger.error(f"❌ خطأ في قاعدة البيانات: {db_error}")
                self.statistics['last_error'] = str(db_error)

        except Exception as e:
            self.logger.error(f"❌ خطأ في دورة الأتمتة: {e}")
            self.statistics['last_error'] = str(e)
    
    def start_service(self) -> bool:
        """بدء خدمة الأتمتة"""
        try:
            if self.running:
                self.logger.warning("⚠️ خدمة الأتمتة تعمل بالفعل")
                return False

            self.running = True

            # بدء thread منفصل للأتمتة
            self.automation_thread = threading.Thread(target=self._run_automation_loop, daemon=True)
            self.automation_thread.start()

            # حفظ حالة الخدمة في ملف
            self._save_service_status()

            interval = self.settings.get('check_interval_minutes', 5)
            self.logger.info(f"🚀 تم بدء خدمة الأتمتة - فحص كل {interval} دقائق")
            return True

        except Exception as e:
            self.logger.error(f"❌ خطأ في بدء خدمة الأتمتة: {e}")
            self.running = False
            return False
    
    def stop_service(self) -> bool:
        """إيقاف خدمة الأتمتة"""
        try:
            if not self.running:
                self.logger.warning("⚠️ خدمة الأتمتة متوقفة بالفعل")
                return False

            self.running = False

            if self.automation_thread and self.automation_thread.is_alive():
                self.automation_thread.join(timeout=5)

            # حفظ حالة الخدمة في ملف
            self._save_service_status()

            self.logger.info("🛑 تم إيقاف خدمة الأتمتة")
            return True

        except Exception as e:
            self.logger.error(f"❌ خطأ في إيقاف خدمة الأتمتة: {e}")
            return False
    
    def _run_automation_loop(self):
        """حلقة تشغيل الأتمتة"""
        self.logger.info("🔄 بدء حلقة الأتمتة")
        
        while self.running:
            try:
                # تشغيل دورة الأتمتة
                self.process_automation_cycle()
                
                # انتظار حسب الفترة المحددة
                interval_seconds = self.settings.get('check_interval_minutes', 5) * 60
                
                # انتظار مع فحص دوري للإيقاف
                for _ in range(interval_seconds):
                    if not self.running:
                        break
                    time.sleep(1)
                    
            except Exception as e:
                self.logger.error(f"❌ خطأ في حلقة الأتمتة: {e}")
                time.sleep(60)  # انتظار دقيقة عند الخطأ
        
        self.logger.info("🛑 انتهت حلقة الأتمتة")
    
    def get_service_status(self) -> Dict:
        """الحصول على حالة خدمة الأتمتة"""
        return {
            'running': self.running,
            'thread_alive': self.automation_thread.is_alive() if self.automation_thread else False,
            'settings': self.settings,
            'working_hours': self.is_working_hours(),
            'statistics': self.statistics,
            'next_run': 'كل ' + str(self.settings.get('check_interval_minutes', 5)) + ' دقائق',
            'instance_id': id(self),  # إضافة معرف الـ instance
            'last_activity': datetime.now().isoformat()  # آخر نشاط
        }
    
    def update_settings(self, new_settings: Dict) -> bool:
        """تحديث إعدادات الأتمتة وحفظها"""
        try:
            # دمج الإعدادات الجديدة مع الموجودة
            self.settings.update(new_settings)

            # حفظ الإعدادات في ملف
            self.save_settings_to_file()

            self.logger.info("✅ تم تحديث وحفظ إعدادات الأتمتة")
            return True

        except Exception as e:
            self.logger.error(f"❌ خطأ في تحديث إعدادات الأتمتة: {e}")
            return False

    def save_settings_to_file(self):
        """حفظ الإعدادات في ملف JSON"""
        try:
            import os
            import json

            # إنشاء مجلد config إذا لم يكن موجوداً
            config_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'config')
            os.makedirs(config_dir, exist_ok=True)

            # مسار ملف الإعدادات
            settings_file = os.path.join(config_dir, 'flask_automation_settings.json')

            # حفظ الإعدادات
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)

            self.logger.info(f"✅ تم حفظ الإعدادات في: {settings_file}")

        except Exception as e:
            self.logger.error(f"❌ خطأ في حفظ الإعدادات: {e}")

    def load_settings_from_file(self):
        """تحميل الإعدادات من ملف JSON"""
        try:
            import os
            import json

            settings_file = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'flask_automation_settings.json')

            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    self.settings.update(saved_settings)
                    self.logger.info("✅ تم تحميل الإعدادات المحفوظة")
            else:
                self.logger.info("📝 استخدام الإعدادات الافتراضية")

        except Exception as e:
            self.logger.error(f"❌ خطأ في تحميل الإعدادات: {e}")

    def _process_automation_queue(self, db_manager):
        """معالجة طابور الأتمتة"""
        try:
            # جلب العناصر المعلقة من طابور الأتمتة
            queue_query = """
                SELECT id, shipment_id, old_status, new_status, created_at, processing_attempts
                FROM automation_queue
                WHERE processed = 0
                AND processing_attempts < 5
                ORDER BY created_at ASC
                FETCH FIRST 10 ROWS ONLY
            """

            queue_items = db_manager.execute_query(queue_query)

            if not queue_items:
                self.logger.info("📦 لا توجد عناصر معلقة في طابور الأتمتة")
                return 0

            self.logger.info(f"📦 وجد {len(queue_items)} عنصر معلق في طابور الأتمتة")

            processed_count = 0

            for item in queue_items:
                queue_id, shipment_id, old_status, new_status, created_at, attempts = item

                try:
                    self.logger.info(f"🔄 معالجة شحنة {shipment_id}: {old_status} → {new_status}")

                    # زيادة عدد المحاولات
                    update_attempts_query = """
                        UPDATE automation_queue
                        SET processing_attempts = processing_attempts + 1
                        WHERE id = :queue_id
                    """
                    db_manager.execute_update(update_attempts_query, {'queue_id': queue_id})

                    # جلب بيانات الشحنة
                    shipment_query = """
                        SELECT id, shipment_number, port_of_discharge, shipment_status
                        FROM cargo_shipments
                        WHERE id = :shipment_id
                    """

                    shipment_data = db_manager.execute_query(shipment_query, {'shipment_id': shipment_id})

                    if not shipment_data:
                        self.logger.warning(f"⚠️ لا توجد شحنة برقم {shipment_id}")
                        continue

                    shipment = shipment_data[0]

                    # تطبيق قواعد الأتمتة على هذه الشحنة
                    success = self._apply_automation_rules_to_shipment(db_manager, shipment, new_status)

                    if success:
                        # تحديد العنصر كمعالج
                        mark_processed_query = """
                            UPDATE automation_queue
                            SET processed = 1, processed_at = CURRENT_TIMESTAMP
                            WHERE id = :queue_id
                        """
                        db_manager.execute_update(mark_processed_query, {'queue_id': queue_id})

                        processed_count += 1
                        self.logger.info(f"✅ تم معالجة شحنة {shipment_id} بنجاح")
                    else:
                        self.logger.warning(f"⚠️ فشل في معالجة شحنة {shipment_id}")

                except Exception as e:
                    self.logger.error(f"❌ خطأ في معالجة عنصر الطابور {queue_id}: {e}")

                    # تسجيل الخطأ في قاعدة البيانات
                    error_query = """
                        UPDATE automation_queue
                        SET last_error = :error_msg
                        WHERE id = :queue_id
                    """
                    try:
                        db_manager.execute_update(error_query, {
                            'queue_id': queue_id,
                            'error_msg': str(e)
                        })
                    except:
                        pass

            self.logger.info(f"📊 تم معالجة {processed_count} من أصل {len(queue_items)} عنصر")
            return processed_count

        except Exception as e:
            self.logger.error(f"❌ خطأ في معالجة طابور الأتمتة: {e}")
            return 0

    def _apply_automation_rules_to_shipment(self, db_manager, shipment, target_status):
        """تطبيق قواعد الأتمتة على شحنة واحدة"""
        try:
            shipment_id, shipment_number, port_of_discharge, current_status = shipment

            # جلب قواعد الأتمتة المناسبة لهذه الحالة (محدث لدعم قواعد الإشعارات)
            rules_query = """
                SELECT id, rule_name, trigger_condition, action_type, SELECTED_AGENT_ID, condition_value
                FROM automation_rules
                WHERE is_active = 1
                AND (
                    (trigger_condition LIKE '%' || :target_status || '%')
                    OR (trigger_condition = 'any_status_change')
                    OR (trigger_condition = 'STATUS_CHANGE_ACTION' AND condition_value = :target_status)
                    OR (trigger_condition = 'STATUS_CHANGE' AND condition_value = :target_status)
                )
                ORDER BY PRIORITY_LEVEL ASC
            """

            rules = db_manager.execute_query(rules_query, {'target_status': target_status})

            if not rules:
                self.logger.info(f"📋 لا توجد قواعد أتمتة مناسبة للحالة {target_status}")
                return True  # نعتبرها نجحت حتى لو لم توجد قواعد

            success_count = 0

            for rule in rules:
                rule_id, rule_name, trigger_condition, action_type, agent_id, condition_value = rule

                try:
                    self.logger.info(f"🔥 FLASK: تطبيق قاعدة '{rule_name}' على شحنة {shipment_number} للحالة {target_status}")

                    if action_type in ['CREATE_DELIVERY_ORDER', 'CREATE_DELIVERY_ORDER_WITH_AGENT']:
                        # إنشاء أمر تسليم
                        self._create_delivery_order_for_shipment(db_manager, shipment_id, agent_id)

                    elif action_type == 'SEND_NOTIFICATION':
                        # إرسال إشعار - استخدام محرك الأتمتة الجديد
                        self.logger.info(f"📧 FLASK: إرسال إشعار للشحنة {shipment_id} - قاعدة {rule_name}")
                        self._send_notification_using_automation_engine(db_manager, shipment_id, target_status, rule_id)

                    elif action_type in ['SEND_WHATSAPP', 'SEND_WHATSAPP_NOTIFICATION']:
                        # إرسال واتساب
                        self._send_whatsapp_for_shipment(db_manager, shipment_id)

                    success_count += 1

                except Exception as e:
                    self.logger.error(f"❌ خطأ في تطبيق قاعدة {rule_name}: {e}")

            return success_count > 0

        except Exception as e:
            self.logger.error(f"❌ خطأ في تطبيق قواعد الأتمتة: {e}")
            return False

    def _save_service_status(self):
        """حفظ حالة الخدمة في ملف"""
        try:
            import os
            import json

            # إنشاء مجلد config إذا لم يكن موجوداً
            config_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'config')
            os.makedirs(config_dir, exist_ok=True)

            # مسار ملف حالة الخدمة
            status_file = os.path.join(config_dir, 'automation_service_status.json')

            # بيانات الحالة
            # تحويل datetime objects إلى strings
            statistics_copy = self.statistics.copy()
            if 'last_run' in statistics_copy and statistics_copy['last_run']:
                if hasattr(statistics_copy['last_run'], 'isoformat'):
                    statistics_copy['last_run'] = statistics_copy['last_run'].isoformat()
                else:
                    statistics_copy['last_run'] = str(statistics_copy['last_run'])

            status_data = {
                'running': self.running,
                'thread_alive': self.automation_thread.is_alive() if self.automation_thread else False,
                'instance_id': id(self),
                'last_update': datetime.now().isoformat(),
                'settings': self.settings,
                'statistics': statistics_copy,
                'process_id': os.getpid()
            }

            # حفظ الحالة
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"❌ خطأ في حفظ حالة الخدمة: {e}")

    def _create_delivery_order_for_shipment(self, db_manager, shipment_id, agent_id):
        """إنشاء أمر تسليم لشحنة"""
        try:
            # فحص إذا كان يوجد أمر تسليم بالفعل
            check_query = """
                SELECT COUNT(*) FROM delivery_orders
                WHERE shipment_id = :shipment_id
            """

            existing = db_manager.execute_query(check_query, {'shipment_id': shipment_id})
            if existing and existing[0][0] > 0:
                self.logger.info(f"📦 يوجد أمر تسليم بالفعل للشحنة {shipment_id}")
                return True

            # فحص إضافي لمنع التكرار - التأكد من عدم وجود أمر بنفس الرقم
            from datetime import datetime
            potential_order_number = f"DO-{datetime.now().strftime('%Y%m%d')}-{shipment_id:06d}"

            duplicate_check_query = """
                SELECT COUNT(*) FROM delivery_orders
                WHERE order_number = :order_number
            """

            duplicate_check = db_manager.execute_query(duplicate_check_query, {'order_number': potential_order_number})
            if duplicate_check and duplicate_check[0][0] > 0:
                self.logger.info(f"📦 يوجد أمر تسليم بنفس الرقم {potential_order_number} بالفعل")
                return True

            # إنشاء أمر تسليم جديد (استخدام الرقم المحسوب مسبقاً)
            order_number = potential_order_number

            insert_query = """
                INSERT INTO delivery_orders (
                    id, order_number, shipment_id, customs_agent_id,
                    created_date, order_status, created_by
                ) VALUES (
                    delivery_orders_seq.NEXTVAL, :order_number, :shipment_id, :agent_id,
                    SYSDATE, 'pending', 1
                )
            """

            db_manager.execute_update(insert_query, {
                'order_number': order_number,
                'shipment_id': shipment_id,
                'agent_id': agent_id or 1  # استخدام مخلص افتراضي إذا لم يحدد
            })

            self.logger.info(f"✅ تم إنشاء أمر تسليم {order_number} للشحنة {shipment_id}")
            return True

        except Exception as e:
            self.logger.error(f"❌ خطأ في إنشاء أمر التسليم: {e}")
            return False

    def _send_notification_for_shipment(self, db_manager, shipment_id, status):
        """إرسال إشعار للشحنة"""
        try:
            self.logger.info(f"📧 إرسال إشعار للشحنة {shipment_id} - الحالة: {status}")
            # هنا يمكن إضافة منطق إرسال الإشعارات
            return True
        except Exception as e:
            self.logger.error(f"❌ خطأ في إرسال الإشعار: {e}")
            return False

    def _send_whatsapp_for_shipment(self, db_manager, shipment_id):
        """إرسال واتساب للشحنة"""
        try:
            self.logger.info(f"📱 إرسال واتساب للشحنة {shipment_id}")
            # هنا يمكن إضافة منطق إرسال الواتساب
            return True
        except Exception as e:
            self.logger.error(f"❌ خطأ في إرسال الواتساب: {e}")
            return False

    @staticmethod
    def get_saved_service_status():
        """قراءة حالة الخدمة المحفوظة"""
        try:
            import os
            import json

            status_file = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'automation_service_status.json')

            if os.path.exists(status_file):
                with open(status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return None

        except Exception as e:
            return None

    def create_real_delivery_order(self, shipment_id, shipment_number, port_of_discharge, db_manager):
        """إنشاء أمر تسليم حقيقي للشحنة الواصلة"""
        try:
            # الحصول على أكبر ID موجود
            max_id_query = "SELECT NVL(MAX(id), 0) + 1 FROM delivery_orders"
            max_id_result = db_manager.execute_query(max_id_query)
            new_order_id = max_id_result[0][0] if max_id_result else 1

            # إنشاء رقم أمر تسليم جديد
            order_number = f"DO-{datetime.now().strftime('%Y%m%d')}-{shipment_id:06d}"

            # البحث عن مخلص موجود
            agent_query = "SELECT id FROM customs_agents WHERE ROWNUM = 1"
            agent_result = db_manager.execute_query(agent_query)
            agent_id = agent_result[0][0] if agent_result else None

            if not agent_id:
                self.logger.error("❌ لا يوجد مخلصين في النظام!")
                return None

            # إدراج أمر التسليم مع ID والمخلص
            insert_query = """
                INSERT INTO delivery_orders (
                    id,
                    order_number,
                    shipment_id,
                    customs_agent_id,
                    order_status,
                    priority,
                    created_date,
                    expected_completion_date,
                    delivery_location
                ) VALUES (
                    :order_id,
                    :order_number,
                    :shipment_id,
                    :agent_id,
                    'pending',
                    'normal',
                    SYSDATE,
                    SYSDATE + 3,
                    :delivery_location
                )
            """

            params = {
                'order_id': new_order_id,
                'order_number': order_number,
                'shipment_id': shipment_id,
                'agent_id': agent_id,
                'delivery_location': port_of_discharge or 'جدة'
            }

            db_manager.execute_update(insert_query, params)

            self.logger.info(f"✅ تم إنشاء أمر تسليم حقيقي: {order_number} (ID: {new_order_id})")
            return new_order_id

        except Exception as e:
            self.logger.error(f"❌ خطأ في إنشاء أمر التسليم: {e}")
            return None

    def check_rule_condition(self, trigger_condition, shipment_data):
        """فحص شرط تفعيل القاعدة (مبسط)"""
        try:
            # شرط بسيط: إذا كان الشرط يحتوي على "arrived_port"
            if 'arrived_port' in trigger_condition.lower():
                return True
            # يمكن إضافة شروط أكثر تعقيداً هنا
            return True  # افتراضياً تطبق على جميع الشحنات
        except:
            return True

    def create_delivery_order_with_rule(self, shipment_id, shipment_number, port_of_discharge, customs_agent_id, db_manager):
        """إنشاء أمر تسليم باستخدام قاعدة الأتمتة"""
        try:
            # الحصول على أكبر ID موجود
            max_id_query = "SELECT NVL(MAX(id), 0) + 1 FROM delivery_orders"
            max_id_result = db_manager.execute_query(max_id_query)
            new_order_id = max_id_result[0][0] if max_id_result else 1

            # إنشاء رقم أمر تسليم جديد
            order_number = f"DO-{datetime.now().strftime('%Y%m%d')}-{shipment_id:06d}"

            # إدراج أمر التسليم مع المخلص من القاعدة
            insert_query = """
                INSERT INTO delivery_orders (
                    id,
                    order_number,
                    shipment_id,
                    customs_agent_id,
                    order_status,
                    priority,
                    created_date,
                    expected_completion_date,
                    delivery_location
                ) VALUES (
                    :order_id,
                    :order_number,
                    :shipment_id,
                    :agent_id,
                    'pending',
                    'normal',
                    SYSDATE,
                    SYSDATE + 3,
                    :delivery_location
                )
            """

            params = {
                'order_id': new_order_id,
                'order_number': order_number,
                'shipment_id': shipment_id,
                'agent_id': customs_agent_id,
                'delivery_location': port_of_discharge or 'جدة'
            }

            db_manager.execute_update(insert_query, params)

            self.logger.info(f"✅ تم إنشاء أمر تسليم بقاعدة: {order_number} (ID: {new_order_id}) للمخلص: {customs_agent_id}")
            return new_order_id

        except Exception as e:
            self.logger.error(f"❌ خطأ في إنشاء أمر التسليم بقاعدة: {e}")
            return None


# إنشاء instance عام للخدمة
flask_automation_service = FlaskAutomationService()
