<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النظام الذكي لشركات الشحن العالمية</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .container-fluid {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .content-section {
            padding: 30px;
        }

        .stats-item {
            padding: 20px;
        }

        .stats-number {
            font-size: 2.5em;
            font-weight: 700;
            color: white;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 5px;
        }

        .stats-label {
            color: rgba(255,255,255,0.9);
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .company-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-bottom: 25px;
            overflow: hidden;
            border: 1px solid rgba(255,255,255,0.2);
            position: relative;
        }

        .company-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .company-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12, #e74c3c);
        }

        .company-header {
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid #dee2e6;
            position: relative;
        }

        .company-body {
            padding: 20px;
        }
        
        .company-body {
            padding: 20px;
        }
        
        .tracking-section {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .ai-badge {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75em;
            font-weight: 600;
            position: absolute;
            top: 15px;
            right: 15px;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
        }

        .company-title {
            color: #2c3e50;
            font-weight: 700;
            font-size: 1.1em;
            margin-bottom: 5px;
        }

        .company-subtitle {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-bottom: 8px;
        }

        .company-code {
            background: #3498db;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75em;
            font-weight: 600;
            display: inline-block;
        }

        .info-section {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }

        .info-label {
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.85em;
            margin-bottom: 8px;
            display: block;
        }

        .badge-container {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .region-badge {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 0.7em;
            font-weight: 500;
            white-space: nowrap;
            box-shadow: 0 2px 5px rgba(46, 204, 113, 0.3);
        }

        .container-type-badge {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7em;
            font-weight: 500;
            white-space: nowrap;
        }

        .supported-formats {
            background: #e8f4f8;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 8px 12px;
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            color: #0c5460;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .stats-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #3498db;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 20px;
        }

        .btn-website {
            background: linear-gradient(135deg, #3498db, #2980b9);
            border: none;
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            font-size: 0.85em;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .btn-website:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.4);
            color: white;
            text-decoration: none;
        }

        .btn-track {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            border: none;
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            font-size: 0.85em;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .btn-track:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(46, 204, 113, 0.4);
            color: white;
        }
        
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 40px;
        }
        
        .no-results {
            text-align: center;
            padding: 60px;
            color: #6c757d;
        }
        
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        /* تحسين المسافات */
        .companies-grid {
            margin: 0 -10px;
        }

        .companies-grid .col-12,
        .companies-grid .col-sm-6,
        .companies-grid .col-md-4,
        .companies-grid .col-lg-3,
        .companies-grid .col-xl-3 {
            padding: 0 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header Section -->
            <div class="header-section">
                <h1 class="mb-3">
                    <i class="fas fa-ship me-3"></i>
                    النظام الذكي لشركات الشحن العالمية
                </h1>
                <p class="lead mb-3">
                    اكتشف وتتبع شحناتك مع أكثر من 50 شركة شحن عالمية باستخدام الذكاء الاصطناعي
                </p>
                <div class="row text-center mt-4">
                    <div class="col-md-4">
                        <div class="stats-item">
                            <div class="stats-number" id="companiesCount">0</div>
                            <div class="stats-label">شركة شحن</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-item">
                            <div class="stats-number">100+</div>
                            <div class="stats-label">دولة مخدومة</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-item">
                            <div class="stats-number">24/7</div>
                            <div class="stats-label">تتبع مستمر</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Content Section -->
            <div class="content-section">
                <!-- Search and Tracking Section -->
                <div class="tracking-section mb-4">
                    <h4 class="mb-3">
                        <i class="fas fa-search me-2"></i>
                        تتبع الحاوية بالذكاء الاصطناعي
                    </h4>
                    <div class="row">
                        <div class="col-md-8">
                            <input type="text"
                                   class="form-control form-control-lg"
                                   id="containerInput"
                                   placeholder="أدخل رقم الحاوية (مثل: MAEU1234567)"
                                   style="border-radius: 25px;">
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-track btn-lg w-100" onclick="trackContainer()">
                                <i class="fas fa-search me-2"></i>
                                تتبع الآن
                            </button>
                        </div>
                    </div>
                    <div id="trackingResult" class="mt-3"></div>
                </div>
            
                <!-- Filter Section -->
                <div class="filter-section mb-4">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">البحث في الشركات:</label>
                            <input type="text"
                                   class="form-control"
                                   id="searchInput"
                                   placeholder="ابحث بالاسم أو الكود..."
                                   onkeyup="searchCompanies()">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">المنطقة:</label>
                            <select class="form-select" id="regionFilter" onchange="filterCompanies()">
                                <option value="">جميع المناطق</option>
                                <option value="Global">عالمية</option>
                                <option value="Asia">آسيا</option>
                                <option value="Middle East">الشرق الأوسط</option>
                                <option value="Mediterranean">البحر المتوسط</option>
                                <option value="Pacific">المحيط الهادئ</option>
                                <option value="US">الولايات المتحدة</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">نوع الحاوية:</label>
                            <select class="form-select" id="containerTypeFilter" onchange="filterCompanies()">
                                <option value="">جميع الأنواع</option>
                                <option value="20GP">20 قدم عادية</option>
                                <option value="40GP">40 قدم عادية</option>
                                <option value="40HC">40 قدم عالية</option>
                                <option value="45HC">45 قدم عالية</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Loading Spinner -->
                <div class="loading-spinner" id="loadingSpinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-3">جاري تحميل شركات الشحن...</p>
                </div>
                
                <!-- Companies Grid -->
                <div id="companiesGrid" class="row companies-grid">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
                
                <!-- No Results -->
                <div class="no-results" id="noResults" style="display: none;">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4>لا توجد نتائج</h4>
                    <p>لم يتم العثور على شركات شحن تطابق معايير البحث</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // متغيرات عامة
        let allCompanies = [];
        let filteredCompanies = [];
        let isLoading = false;
        let searchTimeout = null;

        // تحميل الشركات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء تحميل الصفحة...');
            loadShippingCompanies();
        });
        
        // تحميل شركات الشحن
        function loadShippingCompanies() {
            console.log('📦 بدء تحميل شركات الشحن...');

            if (isLoading) {
                console.log('⏳ تحميل جاري بالفعل...');
                return;
            }

            isLoading = true;
            showLoading(true);

            fetch('/shipments/api/shipping-companies')
                .then(response => {
                    console.log('📡 استجابة الخادم:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('📊 البيانات المستلمة:', data);
                    if (data.success && data.companies) {
                        allCompanies = data.companies;
                        filteredCompanies = allCompanies;
                        console.log(`✅ تم تحميل ${allCompanies.length} شركة`);
                        displayCompanies(filteredCompanies);
                        updateStats();
                    } else {
                        console.error('❌ فشل في تحميل الشركات:', data.message);
                        showError('فشل في تحميل شركات الشحن: ' + (data.message || 'خطأ غير معروف'));
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ في التحميل:', error);

                    // بيانات تجريبية للاختبار
                    console.log('🔄 استخدام البيانات التجريبية...');
                    const testCompanies = [
                        {
                            id: "MAEU", name: "Maersk Line", name_ar: "خط مايرسك",
                            code: "MAEU", website: "https://www.maersk.com",
                            tracking_url: "https://www.maersk.com/tracking/{container}",
                            supported_formats: ["MAEU", "MSKU"],
                            regions: ["Global"], container_types: ["20GP", "40GP", "40HC"]
                        },
                        {
                            id: "MSCU", name: "MSC Mediterranean", name_ar: "شركة الشحن المتوسطية",
                            code: "MSCU", website: "https://www.msc.com",
                            tracking_url: "https://www.msc.com/track/{container}",
                            supported_formats: ["MSCU", "MEDU"],
                            regions: ["Global"], container_types: ["20GP", "40GP", "40HC"]
                        },
                        {
                            id: "CMAU", name: "CMA CGM", name_ar: "سي إم إيه سي جي إم",
                            code: "CMAU", website: "https://www.cma-cgm.com",
                            tracking_url: "https://www.cma-cgm.com/tracking/{container}",
                            supported_formats: ["CMAU", "CGMU"],
                            regions: ["Global"], container_types: ["20GP", "40GP", "40HC"]
                        }
                    ];

                    allCompanies = testCompanies;
                    filteredCompanies = allCompanies;
                    displayCompanies(filteredCompanies);
                    updateStats();
                })
                .finally(() => {
                    isLoading = false;
                    showLoading(false);
                });
        }
        
        // عرض الشركات
        function displayCompanies(companies) {
            console.log('🎨 بدء عرض الشركات:', companies.length);

            const grid = document.getElementById('companiesGrid');
            const noResults = document.getElementById('noResults');

            if (!grid) {
                console.error('❌ لم يتم العثور على عنصر companiesGrid');
                return;
            }

            if (companies.length === 0) {
                console.log('⚠️ لا توجد شركات للعرض');
                grid.innerHTML = '';
                if (noResults) noResults.style.display = 'block';
                return;
            }

            console.log('✅ عرض', companies.length, 'شركة');
            if (noResults) noResults.style.display = 'none';
            
            grid.innerHTML = companies.map(company => `
                <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-4">
                    <div class="company-card">
                        <div class="company-header">
                            <div class="ai-badge">
                                <i class="fas fa-robot me-1"></i>AI
                            </div>
                            <div class="company-title">${company.name}</div>
                            <div class="company-subtitle">${company.name_ar}</div>
                            <span class="company-code">${company.code}</span>
                        </div>
                        <div class="company-body">
                            <div class="info-section">
                                <span class="info-label">المناطق المخدومة:</span>
                                <div class="badge-container">
                                    ${company.regions.map(region => `<span class="region-badge">${region}</span>`).join('')}
                                </div>
                            </div>

                            <div class="info-section">
                                <span class="info-label">أنواع الحاويات:</span>
                                <div class="badge-container">
                                    ${company.container_types.map(type => `<span class="container-type-badge">${type}</span>`).join('')}
                                </div>
                            </div>

                            <div class="info-section">
                                <span class="info-label">الأكواد المدعومة:</span>
                                <div class="supported-formats">${company.supported_formats.join(' • ')}</div>
                            </div>

                            <div class="action-buttons">
                                <a href="${company.website}" target="_blank" class="btn-website">
                                    <i class="fas fa-globe"></i>
                                    الموقع الرسمي
                                </a>
                                <button class="btn-track" onclick="openTrackingPage('${company.code}')">
                                    <i class="fas fa-search"></i>
                                    تتبع الشحنة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // البحث في الشركات مع debouncing
        function searchCompanies() {
            // إلغاء البحث السابق
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            // تأخير البحث لتجنب الطلبات الكثيرة
            searchTimeout = setTimeout(() => {
                filterCompanies();
            }, 300);
        }
        
        // تصفية الشركات
        function filterCompanies() {
            const searchQuery = document.getElementById('searchInput').value.toLowerCase();
            const regionFilter = document.getElementById('regionFilter').value;
            const containerTypeFilter = document.getElementById('containerTypeFilter').value;
            
            filteredCompanies = allCompanies.filter(company => {
                const matchesSearch = !searchQuery || 
                    company.name.toLowerCase().includes(searchQuery) ||
                    company.name_ar.includes(searchQuery) ||
                    company.code.toLowerCase().includes(searchQuery);
                
                const matchesRegion = !regionFilter || 
                    company.regions.includes(regionFilter);
                
                const matchesContainerType = !containerTypeFilter || 
                    company.container_types.includes(containerTypeFilter);
                
                return matchesSearch && matchesRegion && matchesContainerType;
            });
            
            displayCompanies(filteredCompanies);
            updateStats();
        }
        
        // تتبع الحاوية
        function trackContainer() {
            const containerNumber = document.getElementById('containerInput').value.trim();
            const resultDiv = document.getElementById('trackingResult');

            if (!containerNumber) {
                resultDiv.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        يرجى إدخال رقم الحاوية
                    </div>
                `;
                return;
            }

            // منع الطلبات المتكررة
            if (isLoading) {
                resultDiv.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        يرجى الانتظار حتى انتهاء العملية السابقة
                    </div>
                `;
                return;
            }

            isLoading = true;
            resultDiv.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status"></div>
                    <p class="mt-2">جاري تحليل رقم الحاوية بالذكاء الاصطناعي...</p>
                </div>
            `;

            // تأخير قصير لتجنب الطلبات السريعة
            setTimeout(() => {
                fetch('/shipments/api/identify-shipping-company', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache'
                    },
                    body: JSON.stringify({
                        container_number: containerNumber
                    })
                })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.company) {
                    const company = data.company;
                    const confidence = Math.round(data.confidence * 100);
                    
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>تم التعرف على الشركة بنجاح!</h6>
                            <div class="row mt-3">
                                <div class="col-md-8">
                                    <strong>الشركة:</strong> ${company.name} (${company.name_ar})<br>
                                    <strong>الكود:</strong> ${company.code}<br>
                                    <strong>دقة الذكاء الاصطناعي:</strong> ${confidence}%
                                </div>
                                <div class="col-md-4">
                                    <a href="${company.tracking_url.replace('{container}', containerNumber)}" 
                                       target="_blank" 
                                       class="btn btn-success btn-sm w-100">
                                        <i class="fas fa-external-link-alt me-2"></i>
                                        تتبع الآن
                                    </a>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            لم يتم التعرف على شركة الشحن. يرجى التحقق من رقم الحاوية.
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                if (error.message && error.message.includes('429')) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            الكثير من الطلبات - يرجى الانتظار قليلاً ثم إعادة المحاولة
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i>
                            خطأ في الاتصال بالخادم
                        </div>
                    `;
                }
            })
            .finally(() => {
                isLoading = false;
            });
            }, 500); // تأخير 500ms
        }
        }
        
        // فتح صفحة التتبع
        function openTrackingPage(companyCode) {
            const containerNumber = document.getElementById('containerInput').value.trim();
            if (containerNumber) {
                const company = allCompanies.find(c => c.code === companyCode);
                if (company) {
                    const trackingUrl = company.tracking_url.replace('{container}', containerNumber);
                    window.open(trackingUrl, '_blank');
                }
            } else {
                alert('يرجى إدخال رقم الحاوية أولاً');
            }
        }
        
        // تحديث الإحصائيات
        function updateStats() {
            document.getElementById('companiesCount').textContent = filteredCompanies.length;
        }
        
        // إظهار/إخفاء التحميل
        function showLoading(show) {
            document.getElementById('loadingSpinner').style.display = show ? 'block' : 'none';
            document.getElementById('companiesGrid').style.display = show ? 'none' : 'block';
        }
        
        // إظهار خطأ
        function showError(message) {
            document.getElementById('companiesGrid').innerHTML = `
                <div class="col-12">
                    <div class="alert alert-danger text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                        <h5>حدث خطأ</h5>
                        <p>${message}</p>
                        <button class="btn btn-primary" onclick="loadShippingCompanies()">
                            <i class="fas fa-redo me-2"></i>إعادة المحاولة
                        </button>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
