# 📊 دليل النموذج الجدولي الاحترافي - Excel-like Table Component

## 🎯 نظرة عامة

هذا النموذج الجدولي الاحترافي يوفر تجربة مشابهة تماماً لبرنامج Microsoft Excel داخل تطبيق الويب، مما يسمح بإدخال وتحرير البيانات بسهولة وسرعة فائقة.

## 🛠️ المكتبات والتقنيات المستخدمة

### المكتبات الأساسية:
1. **Handsontable** (v12.4.0)
   - مكتبة JavaScript متقدمة لإنشاء جداول تفاعلية
   - توفر تجربة مشابهة لـ Excel
   - دعم للتحرير المباشر والتحديد المتعدد

2. **jQuery** (v3.6.0)
   - للتعامل مع DOM والأحداث
   - تبسيط العمليات والاستدعاءات

3. **Bootstrap 5**
   - للتصميم والتخطيط
   - الأزرار والنماذج

4. **Font Awesome**
   - الأيقونات والرموز

### مكتبات CSS:
- `handsontable.full.min.css` - تنسيقات الجدول
- تنسيقات مخصصة لدعم العربية و RTL

## 🏗️ هيكل المكون

### ملفات المكون:
```
app/templates/inventory/excel_item_entry.html
├── CSS Styles (مدمج في الملف)
├── HTML Structure
└── JavaScript Functions
```

### الأقسام الرئيسية:
1. **شريط الأدوات** - أزرار التحكم
3. **الجدول التفاعلي** - منطقة إدخال البيانات
4. **شريط المعلومات** - الإحصائيات المباشرة

## 📊 تكوين الجدول

### أعمدة الجدول (بالترتيب):
```javascript
colHeaders: [
    'رقم الصنف',      // العمود 0 - Autocomplete
    'اسم الصنف',      // العمود 1 - ReadOnly
    'الوحدة',         // العمود 2 - ReadOnly
    'الكمية',         // العمود 3 - Numeric
    'السعر',          // العمود 4 - Numeric
    'الإجمالي',       // العمود 5 - Calculated
    'تاريخ الإنتاج',   // العمود 6 - Date
    'تاريخ الانتهاء',  // العمود 7 - Date
    'ملاحظات'         // العمود 8 - Text
]
```

### أنواع البيانات:
- **Autocomplete**: بحث تلقائي في الأصناف
- **Numeric**: أرقام مع تنسيق عربي
- **Date**: تواريخ بتنسيق YYYY-MM-DD
- **Text**: نصوص عادية
- **ReadOnly**: للقراءة فقط

## 🔧 الدوال الأساسية

### دوال التهيئة:
```javascript
// تهيئة الجدول
$(document).ready(function() {
    const today = new Date().toISOString().split('T')[0];
    $('#entryDate').val(today);
    
    const container = document.getElementById('excelTable');
    hot = new Handsontable(container, tableConfig);
    
    loadItemsCache();
    updateStatistics();
    setInterval(autoSave, 30000);
});

// إنشاء بيانات فارغة
function generateEmptyData(rows) {
    const data = [];
    for (let i = 0; i < rows; i++) {
        data.push(['', '', '', 0, 0, 0, '', '', '']);
    }
    return data;
}
```

### دوال البحث والتحقق:
```javascript
// تحميل كاش الأصناف
function loadItemsCache() {
    $.ajax({
        url: '/inventory/api/items/search',
        method: 'GET',
        data: { q: '' },
        success: function(items) {
            itemsCache = {};
            items.forEach(item => {
                itemsCache[item.code] = item;
            });
        }
    });
}

// البحث في الأصناف
function searchItems(query, process) {
    if (!query || query.length < 1) {
        process([]);
        return;
    }
    
    // البحث في الكاش أولاً
    const localResults = Object.keys(itemsCache).filter(code => 
        code.toLowerCase().includes(query.toLowerCase()) ||
        itemsCache[code].name.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 10);
    
    if (localResults.length > 0) {
        process(localResults);
        return;
    }
    
    // البحث في الخادم
    $.ajax({
        url: '/inventory/api/items/search',
        method: 'GET',
        data: { q: query },
        success: function(items) {
            const codes = items.map(item => {
                itemsCache[item.code] = item;
                return item.code;
            });
            process(codes);
        }
    });
}

// التحقق من رقم الصنف
function validateItemCode(value, callback) {
    if (itemsCache[value]) {
        callback(true);
    } else {
        $.ajax({
            url: '/inventory/api/items/search',
            method: 'GET',
            data: { q: value },
            success: function(items) {
                const exactMatch = items.find(item => item.code === value);
                if (exactMatch) {
                    itemsCache[value] = exactMatch;
                    callback(true);
                } else {
                    callback(false);
                }
            }
        });
    }
}
```

### دوال معالجة البيانات:
```javascript
// معالجة تغييرات الخلايا
function handleCellChanges(changes) {
    changes.forEach(([row, prop, oldValue, newValue]) => {
        if (prop === 0 && newValue && itemsCache[newValue]) {
            // تم تحديد صنف - ملء البيانات التلقائية
            const item = itemsCache[newValue];
            hot.setDataAtCell([
                [row, 1, item.name],    // اسم الصنف
                [row, 2, item.unit],    // الوحدة
                [row, 4, item.price]    // السعر
            ]);
            calculateRowTotal(row);
        } else if (prop === 3 || prop === 4) {
            // تم تغيير الكمية أو السعر
            calculateRowTotal(row);
        }
    });
    updateStatistics();
}

// حساب إجمالي الصف
function calculateRowTotal(row) {
    const quantity = hot.getDataAtCell(row, 3) || 0; // الكمية
    const price = hot.getDataAtCell(row, 4) || 0;    // السعر
    const total = quantity * price;
    
    hot.setDataAtCell(row, 5, total); // الإجمالي
}

// تحديث الإحصائيات
function updateStatistics() {
    const data = hot.getData();
    let totalRows = 0;
    let completedRows = 0;
    let totalQuantity = 0;
    let totalAmount = 0;
    
    data.forEach(row => {
        if (row[0]) { // إذا كان هناك رقم صنف
            totalRows++;
            
            if (row[0] && row[1] && row[3] > 0) { // صف مكتمل
                completedRows++;
            }
            
            totalQuantity += parseFloat(row[3]) || 0; // الكمية
            totalAmount += parseFloat(row[5]) || 0;   // الإجمالي
        }
    });
    
    $('#totalRows').text(totalRows);
    $('#completedRows').text(completedRows);
    $('#totalQuantity').text(totalQuantity.toFixed(3));
    $('#totalAmount').text(totalAmount.toFixed(3) + ' ر.س');
}
```

### دوال التحكم:
```javascript
// إضافة صفوف
function addRows(count) {
    const currentData = hot.getData();
    const newRows = generateEmptyData(count);
    hot.loadData([...currentData, ...newRows]);
    showAlert(`تم إضافة ${count} صف جديد`, 'success');
}

// مسح المحدد
function clearSelected() {
    const selected = hot.getSelected();
    if (selected && selected.length > 0) {
        const [row1, col1, row2, col2] = selected[0];
        
        for (let row = row1; row <= row2; row++) {
            for (let col = col1; col <= col2; col++) {
                hot.setDataAtCell(row, col, '');
            }
        }
        updateStatistics();
        showAlert('تم مسح البيانات المحددة', 'info');
    }
}

// حذف الصفوف
function deleteRows() {
    const selected = hot.getSelected();
    if (selected && selected.length > 0) {
        const [row1, , row2] = selected[0];
        const rowsToDelete = Math.abs(row2 - row1) + 1;
        
        if (confirm(`هل أنت متأكد من حذف ${rowsToDelete} صف؟`)) {
            hot.alter('remove_row', Math.min(row1, row2), rowsToDelete);
            updateStatistics();
            showAlert(`تم حذف ${rowsToDelete} صف`, 'success');
        }
    }
}
```

## 💾 دوال الحفظ والاستيراد

### حفظ البيانات:
```javascript
function saveData() {
    const data = hot.getData();
    const validRows = data.filter(row => row[0] && row[1] && row[3] > 0);
    
    if (validRows.length === 0) {
        showAlert('لا توجد بيانات صحيحة للحفظ', 'warning');
        return;
    }
    
    const entryData = {
        entry_date: $('#entryDate').val(),
        branch_id: $('#branchId').val(),
        status: $('#entryStatus').val(),
        notes: 'تم الإدخال باستخدام النموذج الاحترافي',
        items: validRows.map(row => ({
            item_code: row[0],
            item_name: row[1],
            unit: row[2],
            quantity: parseFloat(row[3]) || 0,
            unit_price: parseFloat(row[4]) || 0,
            production_date: row[6] || null,
            expiry_date: row[7] || null,
            notes: row[8] || ''
        }))
    };
    
    $.ajax({
        url: '/inventory/api/item-entry/save',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(entryData),
        success: function(response) {
            if (response.success) {
                showAlert(response.message, 'success');
                $('#entryNumber').val(response.entry_number);
            }
        }
    });
}
```

### حفظ تلقائي:
```javascript
function autoSave() {
    const data = hot.getData();
    const validRows = data.filter(row => row[0] && row[1]);
    
    if (validRows.length > 0) {
        localStorage.setItem('excel_entry_draft', JSON.stringify({
            data: data,
            timestamp: new Date().toISOString(),
            entry_info: {
                date: $('#entryDate').val(),
                branch: $('#branchId').val(),
                status: $('#entryStatus').val()
            }
        }));
    }
}
```

## ⌨️ اختصارات لوحة المفاتيح

```javascript
function handleKeyboardShortcuts(event) {
    // Ctrl + S للحفظ
    if (event.ctrlKey && event.keyCode === 83) {
        event.preventDefault();
        saveData();
    }
    
    // Ctrl + Shift + A لإضافة 10 صفوف
    if (event.ctrlKey && event.shiftKey && event.keyCode === 65) {
        event.preventDefault();
        addRows(10);
    }
}
```

### الاختصارات المدعومة:
- `Tab` - الانتقال للخلية التالية
- `Shift + Tab` - الانتقال للخلية السابقة
- `Enter` - الانتقال للصف التالي
- `Ctrl + C` - نسخ
- `Ctrl + V` - لصق
- `Ctrl + Z` - تراجع
- `Delete` - حذف المحتوى
- `F2` - تحرير الخلية
- `Ctrl + S` - حفظ البيانات
- `Ctrl + Shift + A` - إضافة 10 صفوف

## 🎨 التنسيقات والألوان

### CSS المخصص:
```css
/* خلايا للقراءة فقط */
.readonly-cell {
    background-color: #f8f9fa !important;
    color: #6c757d;
}

/* خلايا محسوبة */
.calculated-cell {
    background-color: #e3f2fd !important;
    font-weight: 600;
}

/* خلايا غير صحيحة */
.handsontable .htInvalid {
    background-color: #ffebee !important;
    color: #c62828;
}
```

## 🔧 إعدادات الجدول

### التكوين الأساسي:
```javascript
const tableConfig = {
    data: generateEmptyData(50),
    colHeaders: [...],
    columns: [...],
    rowHeaders: true,
    colWidths: [120, 200, 80, 100, 100, 120, 120, 120, 150],
    height: 500,
    licenseKey: 'non-commercial-and-evaluation',
    
    // إعدادات التحديد والتحرير
    selectionMode: 'multiple',
    fillHandle: {
        direction: 'vertical',
        autoInsertRow: true
    },
    
    // قائمة السياق
    contextMenu: {
        items: {
            'row_above': { name: 'إدراج صف أعلى' },
            'row_below': { name: 'إدراج صف أسفل' },
            'remove_row': { name: 'حذف الصف' },
            'copy': { name: 'نسخ' },
            'paste': { name: 'لصق' }
        }
    },
    
    // الأحداث
    afterChange: handleCellChanges,
    afterSelection: updateSelectionInfo,
    beforeKeyDown: handleKeyboardShortcuts
};
```

## 🚀 كيفية الاستخدام في مشاريع أخرى

### 1. نسخ الملفات المطلوبة:
```
app/templates/inventory/excel_item_entry.html
app/static/js/handsontable.full.min.js
app/static/css/handsontable.full.min.css
```

### 2. إعداد Backend API:
```python
# في routes.py
@bp.route('/api/items/search')
def search_items():
    query = request.args.get('q', '')
    # منطق البحث في قاعدة البيانات
    return jsonify(results)

@bp.route('/api/item-entry/save', methods=['POST'])
def save_item_entry():
    data = request.get_json()
    # منطق حفظ البيانات
    return jsonify({'success': True, 'message': 'تم الحفظ'})
```

### 3. تخصيص الأعمدة:
```javascript
// تعديل colHeaders حسب احتياجاتك
colHeaders: [
    'العمود الأول',
    'العمود الثاني',
    // ... باقي الأعمدة
],

// تعديل columns حسب نوع البيانات
columns: [
    { type: 'text' },           // نص عادي
    { type: 'numeric' },        // رقم
    { type: 'date' },           // تاريخ
    { type: 'autocomplete' },   // بحث تلقائي
    { readOnly: true },         // للقراءة فقط
]
```

## 🔄 دورة حياة البيانات

### 1. تحميل البيانات:
```
المستخدم يفتح الصفحة
    ↓
تهيئة الجدول بـ 50 صف فارغ
    ↓
تحميل كاش الأصناف من الخادم
    ↓
الجدول جاهز للاستخدام
```

### 2. إدخال البيانات:
```
المستخدم يكتب رقم صنف
    ↓
البحث في الكاش المحلي
    ↓
إذا لم يوجد، البحث في الخادم
    ↓
ملء بيانات الصنف تلقائياً
    ↓
حساب الإجمالي عند تغيير الكمية/السعر
    ↓
تحديث الإحصائيات المباشرة
```

### 3. حفظ البيانات:
```
المستخدم يضغط حفظ
    ↓
التحقق من صحة البيانات
    ↓
تجميع البيانات الصحيحة
    ↓
إرسال للخادم عبر AJAX
    ↓
عرض رسالة النجاح/الخطأ
```

## 🛡️ التحقق من البيانات

### التحقق من جانب العميل:
```javascript
// التحقق من وجود الصنف
validator: function(value, callback) {
    if (value && value.length > 0) {
        validateItemCode(value, callback);
    } else {
        callback(true);
    }
}

// التحقق من الأرقام
validator: function(value, callback) {
    callback(value >= 0);
}
```

### التحقق من جانب الخادم:
```python
def validate_item_entry(data):
    errors = []

    if not data.get('entry_date'):
        errors.append('تاريخ الإدخال مطلوب')

    if not data.get('items') or len(data['items']) == 0:
        errors.append('يجب إدخال صنف واحد على الأقل')

    for item in data.get('items', []):
        if not item.get('item_code'):
            errors.append('رقم الصنف مطلوب')
        if not item.get('quantity') or item['quantity'] <= 0:
            errors.append('الكمية يجب أن تكون أكبر من صفر')

    return errors
```

## 📱 الاستجابة للشاشات المختلفة

### CSS للشاشات الصغيرة:
```css
@media (max-width: 768px) {
    .excel-toolbar {
        flex-direction: column;
        align-items: stretch;
    }

    .toolbar-group {
        justify-content: center;
    }

    .table-container {
        height: 400px;
    }

    .handsontable {
        font-size: 12px;
    }
}
```

## 🔧 التخصيص والتوسيع

### إضافة أعمدة جديدة:
1. تحديث `colHeaders`
2. إضافة تعريف العمود في `columns`
3. تحديث `colWidths`
4. تعديل دوال معالجة البيانات

### إضافة وظائف جديدة:
```javascript
// مثال: إضافة وظيفة تصدير Excel
function exportToExcel() {
    const data = hot.getData();
    const validRows = data.filter(row => row[0]);

    // استخدام مكتبة مثل SheetJS
    const ws = XLSX.utils.aoa_to_sheet([
        ['رقم الصنف', 'اسم الصنف', 'الوحدة', 'الكمية', 'السعر'],
        ...validRows.map(row => [row[0], row[1], row[2], row[3], row[4]])
    ]);

    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'الأصناف');
    XLSX.writeFile(wb, 'items_export.xlsx');
}
```

## 🐛 استكشاف الأخطاء وإصلاحها

### مشاكل شائعة وحلولها:

1. **الجدول لا يظهر:**
   - تأكد من تحميل مكتبة Handsontable
   - تحقق من وجود عنصر `#excelTable`

2. **البحث التلقائي لا يعمل:**
   - تحقق من API endpoint
   - تأكد من تحميل كاش الأصناف

3. **الحفظ لا يعمل:**
   - تحقق من صحة البيانات
   - تأكد من API endpoint للحفظ

4. **مشاكل RTL:**
   - تأكد من إضافة `dir="rtl"` للحاوي
   - تحقق من CSS المخصص

## 📊 الأداء والتحسين

### نصائح للأداء:
1. **تحميل البيانات بالتدريج** - لا تحمل آلاف الصفوف مرة واحدة
2. **استخدام الكاش** - احفظ الأصناف المستخدمة محلياً
3. **الحفظ التلقائي** - احفظ المسودات في localStorage
4. **التحقق المحلي** - قلل من استدعاءات الخادم

### مراقبة الأداء:
```javascript
// قياس وقت التحميل
console.time('table-init');
hot = new Handsontable(container, tableConfig);
console.timeEnd('table-init');

// مراقبة استخدام الذاكرة
console.log('Memory usage:', performance.memory);
```

## 🔐 الأمان

### اعتبارات الأمان:
1. **التحقق من البيانات** - في العميل والخادم
2. **تشفير الاتصالات** - استخدم HTTPS
3. **التحقق من الصلاحيات** - تأكد من صلاحية المستخدم
4. **تنظيف البيانات** - امنع SQL Injection و XSS

## 📚 مراجع ومصادر

### الوثائق الرسمية:
- [Handsontable Documentation](https://handsontable.com/docs/)
- [jQuery Documentation](https://api.jquery.com/)
- [Bootstrap Documentation](https://getbootstrap.com/docs/)

### أمثلة إضافية:
- [Handsontable Examples](https://handsontable.com/examples)
- [Advanced Features](https://handsontable.com/docs/javascript-data-grid/advanced-features/)

---

## 📝 ملاحظات التطوير

تم تطوير هذا المكون بواسطة فريق التطوير في النظام المحاسبي السعودي.
آخر تحديث: أغسطس 2025

للدعم الفني أو الاستفسارات، يرجى التواصل مع فريق التطوير.
```
