# -*- coding: utf-8 -*-
"""
Oracle Database Manager for NetSuite-style ERP
مدير قاعدة بيانات Oracle لنظام ERP بنمط NetSuite
"""

from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.exc import SQLAlchemyError
from contextlib import contextmanager
import logging
from app.oracle_config import OracleConfig
from app.models.oracle_models import Base

# إعداد السجلات
logger = logging.getLogger(__name__)

class OracleManager:
    """مدير قاعدة بيانات Oracle"""
    
    def __init__(self):
        self.engine = None
        self.session_factory = None
        self.Session = None
        
    def initialize(self):
        """تهيئة الاتصال بـ Oracle"""
        try:
            # إنشاء المحرك
            self.engine = OracleConfig.create_oracle_engine()
            
            # إنشاء مصنع الجلسات
            self.session_factory = sessionmaker(bind=self.engine)
            self.Session = scoped_session(self.session_factory)
            
            logger.info("تم تهيئة مدير Oracle بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة مدير Oracle: {e}")
            return False
    
    def create_tables(self):
        """إنشاء الجداول"""
        try:
            Base.metadata.create_all(self.engine)
            logger.info("تم إنشاء جداول Oracle بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الجداول: {e}")
            return False
    
    def drop_tables(self):
        """حذف الجداول"""
        try:
            Base.metadata.drop_all(self.engine)
            logger.info("تم حذف جداول Oracle بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حذف الجداول: {e}")
            return False
    
    @contextmanager
    def get_session(self):
        """الحصول على جلسة قاعدة البيانات"""
        session = self.Session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"خطأ في جلسة قاعدة البيانات: {e}")
            raise
        finally:
            session.close()
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام"""
        try:
            with self.get_session() as session:
                result = session.execute(query, params or {})
                return result.fetchall()
                
        except Exception as e:
            logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            raise
    
    def execute_procedure(self, procedure_name, params=None):
        """تنفيذ إجراء مخزن"""
        try:
            with self.get_session() as session:
                result = session.execute(f"CALL {procedure_name}({','.join([':' + k for k in (params or {}).keys()])})", params or {})
                return result.fetchall()
                
        except Exception as e:
            logger.error(f"خطأ في تنفيذ الإجراء {procedure_name}: {e}")
            raise
    
    def get_dashboard_stats(self):
        """الحصول على إحصائيات لوحة المعلومات"""
        try:
            stats_query = """
            SELECT 
                'purchase_requests' as metric,
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved
            FROM SAS_PURCHASE_REQUESTS
            WHERE created_at >= TRUNC(SYSDATE) - 30
            
            UNION ALL
            
            SELECT 
                'purchase_orders' as metric,
                COUNT(*) as total,
                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
                0 as approved
            FROM SAS_PURCHASE_ORDERS
            WHERE created_at >= TRUNC(SYSDATE) - 30
            
            UNION ALL
            
            SELECT 
                'suppliers' as metric,
                COUNT(*) as total,
                SUM(CASE WHEN is_approved = 1 THEN 1 ELSE 0 END) as approved,
                0 as active
            FROM SAS_SUPPLIERS
            WHERE is_active = 1
            """
            
            return self.execute_query(stats_query)
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات لوحة المعلومات: {e}")
            return []
    
    def get_recent_activities(self, limit=10):
        """الحصول على الأنشطة الحديثة"""
        try:
            activities_query = """
            SELECT 
                'purchase_request' as activity_type,
                pr.request_number as reference,
                pr.title as description,
                pr.created_at as activity_date,
                u.full_name as user_name
            FROM SAS_PURCHASE_REQUESTS pr
            JOIN SAS_USERS u ON pr.requested_by = u.id
            WHERE pr.created_at >= TRUNC(SYSDATE) - 7
            
            UNION ALL
            
            SELECT 
                'purchase_order' as activity_type,
                po.order_number as reference,
                s.name_ar as description,
                po.created_at as activity_date,
                u.full_name as user_name
            FROM SAS_PURCHASE_ORDERS po
            JOIN SAS_SUPPLIERS s ON po.supplier_id = s.id
            JOIN SAS_USERS u ON po.created_by = u.id
            WHERE po.created_at >= TRUNC(SYSDATE) - 7
            
            ORDER BY activity_date DESC
            FETCH FIRST :limit ROWS ONLY
            """
            
            return self.execute_query(activities_query, {'limit': limit})
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على الأنشطة الحديثة: {e}")
            return []
    
    def get_low_stock_items(self, limit=10):
        """الحصول على الأصناف منخفضة المخزون"""
        try:
            low_stock_query = """
            SELECT 
                item_code,
                item_name,
                current_stock,
                min_stock_level,
                unit_of_measure
            FROM SAS_INVENTORY_ITEMS
            WHERE current_stock <= min_stock_level
            AND is_active = 1
            ORDER BY (current_stock / NULLIF(min_stock_level, 0)) ASC
            FETCH FIRST :limit ROWS ONLY
            """
            
            return self.execute_query(low_stock_query, {'limit': limit})
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على الأصناف منخفضة المخزون: {e}")
            return []
    
    def backup_database(self, backup_path):
        """نسخ احتياطي لقاعدة البيانات"""
        try:
            # هذا مثال بسيط - في الواقع ستحتاج لاستخدام Oracle Data Pump
            backup_query = """
            CREATE TABLE SAS_BACKUP_LOG AS
            SELECT 
                'BACKUP_' || TO_CHAR(SYSDATE, 'YYYYMMDD_HH24MISS') as backup_id,
                SYSDATE as backup_date,
                USER as backup_user,
                'SUCCESS' as status
            FROM DUAL
            """
            
            self.execute_query(backup_query)
            logger.info(f"تم إنشاء نسخة احتياطية في {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        try:
            # تحديث الإحصائيات
            optimize_queries = [
                "BEGIN DBMS_STATS.GATHER_SCHEMA_STATS(USER); END;",
                "ALTER SYSTEM FLUSH SHARED_POOL",
                "ALTER SYSTEM FLUSH BUFFER_CACHE"
            ]
            
            for query in optimize_queries:
                try:
                    self.execute_query(query)
                except Exception as e:
                    logger.warning(f"تحذير في تحسين قاعدة البيانات: {e}")
            
            logger.info("تم تحسين قاعدة البيانات بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحسين قاعدة البيانات: {e}")
            return False
    
    def health_check(self):
        """فحص صحة قاعدة البيانات"""
        try:
            health_checks = {
                'connection': False,
                'tables': False,
                'sequences': False,
                'performance': False
            }
            
            # فحص الاتصال
            try:
                result = self.execute_query("SELECT 1 FROM DUAL")
                health_checks['connection'] = len(result) > 0
            except:
                pass
            
            # فحص الجداول
            try:
                tables_query = """
                SELECT COUNT(*) as table_count
                FROM USER_TABLES
                WHERE TABLE_NAME LIKE 'SAS_%'
                """
                result = self.execute_query(tables_query)
                health_checks['tables'] = result[0][0] > 0 if result else False
            except:
                pass
            
            # فحص التسلسلات
            try:
                sequences_query = """
                SELECT COUNT(*) as seq_count
                FROM USER_SEQUENCES
                WHERE SEQUENCE_NAME LIKE 'SAS_%'
                """
                result = self.execute_query(sequences_query)
                health_checks['sequences'] = result[0][0] > 0 if result else False
            except:
                pass
            
            # فحص الأداء
            try:
                performance_query = """
                SELECT 
                    ROUND(AVG(elapsed_time)/1000000, 2) as avg_response_time
                FROM V$SQL
                WHERE LAST_ACTIVE_TIME >= SYSDATE - 1/24
                AND ROWNUM <= 100
                """
                result = self.execute_query(performance_query)
                health_checks['performance'] = result[0][0] < 5.0 if result and result[0][0] else True
            except:
                health_checks['performance'] = True  # افتراض أن الأداء جيد إذا لم نتمكن من القياس
            
            return health_checks
            
        except Exception as e:
            logger.error(f"خطأ في فحص صحة قاعدة البيانات: {e}")
            return {
                'connection': False,
                'tables': False,
                'sequences': False,
                'performance': False
            }
    
    def close(self):
        """إغلاق الاتصالات"""
        try:
            if self.Session:
                self.Session.remove()
            if self.engine:
                self.engine.dispose()
            logger.info("تم إغلاق اتصالات Oracle بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في إغلاق اتصالات Oracle: {e}")

# إنشاء مثيل عام
oracle_manager = OracleManager()

def init_oracle_app(app):
    """تهيئة Oracle مع التطبيق"""
    try:
        # تهيئة المدير
        if oracle_manager.initialize():
            app.logger.info("تم تهيئة Oracle بنجاح")
            
            # إنشاء الجداول إذا لم تكن موجودة
            oracle_manager.create_tables()
            
            # إضافة المدير للتطبيق
            app.oracle_manager = oracle_manager
            
            return True
        else:
            app.logger.error("فشل في تهيئة Oracle")
            return False
            
    except Exception as e:
        app.logger.error(f"خطأ في تهيئة Oracle مع التطبيق: {e}")
        return False

def get_oracle_session():
    """الحصول على جلسة Oracle"""
    return oracle_manager.get_session()

if __name__ == "__main__":
    # اختبار المدير
    print("🔍 اختبار مدير Oracle...")
    
    if oracle_manager.initialize():
        print("✅ تم تهيئة المدير بنجاح")
        
        # فحص الصحة
        health = oracle_manager.health_check()
        print(f"🏥 فحص الصحة: {health}")
        
        # إغلاق الاتصالات
        oracle_manager.close()
        print("✅ تم إغلاق الاتصالات")
    else:
        print("❌ فشل في تهيئة المدير")
