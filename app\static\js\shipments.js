/**
 * JavaScript للشحنات
 * Shipments JavaScript Functions
 */

// متغيرات عامة
let currentShipmentData = {};
let currentReleaseShipmentData = {};

/**
 * تهيئة الصفحة
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل صفحة الشحنات');

    // تهيئة الجداول
    initializeTables();

    // تهيئة المرشحات
    initializeFilters();

    // تهيئة الأحداث
    initializeEvents();

    // تهيئة البحث الصوتي
    setTimeout(() => {
        initializeVoiceSearch();
    }, 1000);
});

/**
 * تهيئة الجداول
 */
function initializeTables() {
    // إضافة تأثيرات hover للصفوف
    const tableRows = document.querySelectorAll('.table tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });

        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });

    // جمع بيانات الشحنات للبحث
    setTimeout(() => {
        collectAllShipments();
        console.log('✅ تم تهيئة البحث السريع');
    }, 500);
}

/**
 * تهيئة المرشحات
 */
function initializeFilters() {
    // حفظ إعدادات المرشحات في localStorage
    const filters = ['dateFrom', 'dateTo', 'shipmentStatusFilter', 'releaseStatusFilter', 'shippingLineFilter'];
    
    filters.forEach(filterId => {
        const element = document.getElementById(filterId);
        if (element) {
            // تحميل القيمة المحفوظة
            const savedValue = localStorage.getItem(`shipments_${filterId}`);
            if (savedValue) {
                element.value = savedValue;
            }
            
            // حفظ القيمة عند التغيير
            element.addEventListener('change', function() {
                localStorage.setItem(`shipments_${filterId}`, this.value);
            });
        }
    });
}

/**
 * تهيئة الأحداث
 */
function initializeEvents() {
    // إضافة أحداث النقر للحالات القابلة للتعديل
    const clickableStatuses = document.querySelectorAll('.clickable-status');
    clickableStatuses.forEach(status => {
        status.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (this.classList.contains('shipment-status-badge')) {
                openStatusModal(this);
            } else if (this.classList.contains('release-status-badge')) {
                openReleaseStatusModal(this);
            }
        });
    });

    // إضافة مستمع للبحث السريع
    const quickSearchInput = document.getElementById('quickSearch');
    if (quickSearchInput) {
        quickSearchInput.addEventListener('input', function() {
            console.log(`⌨️ تم كتابة: "${this.value}"`);
            performQuickSearch();
        });

        quickSearchInput.addEventListener('keyup', function() {
            performQuickSearch();
        });

        console.log('✅ تم إعداد البحث السريع');
    } else {
        console.warn('⚠️ لم يتم العثور على حقل البحث السريع');
    }
}

/**
 * فتح نافذة تعديل حالة الشحنة
 */
function openStatusModal(element) {
    console.log('🔧 فتح نافذة تعديل حالة الشحنة...');
    
    // الحصول على بيانات الشحنة من العنصر
    currentShipmentData = {
        id: element.getAttribute('data-shipment-id'),
        trackingNumber: element.getAttribute('data-tracking-number'),
        currentStatus: element.getAttribute('data-current-status'),
        currentStatusDisplay: element.getAttribute('data-current-status-display')
    };
    
    console.log('📦 بيانات الشحنة:', currentShipmentData);
    
    // تعبئة النافذة المنبثقة
    document.getElementById('modalTrackingNumber').value = currentShipmentData.trackingNumber;
    document.getElementById('modalCurrentStatus').textContent = currentShipmentData.currentStatusDisplay;
    document.getElementById('modalCurrentStatus').className = `badge bg-${getStatusColor(currentShipmentData.currentStatus)} fs-6`;
    
    // مسح حقل الملاحظات
    document.getElementById('statusNotes').value = '';
    
    // تحميل قائمة الحالات المتاحة
    loadAvailableStatuses();
    
    // إظهار النافذة المنبثقة
    const modal = new bootstrap.Modal(document.getElementById('statusEditModal'));
    modal.show();
}

/**
 * فتح نافذة تعديل حالة الإفراج
 */
function openReleaseStatusModal(element) {
    console.log('🔧 فتح نافذة تعديل حالة الإفراج...');
    
    // الحصول على بيانات الشحنة من العنصر
    currentReleaseShipmentData = {
        id: element.getAttribute('data-shipment-id'),
        trackingNumber: element.getAttribute('data-tracking-number'),
        currentReleaseStatus: element.getAttribute('data-current-release-status'),
        currentReleaseStatusDisplay: element.getAttribute('data-current-release-status-display')
    };
    
    console.log('📦 بيانات الشحنة للإفراج:', currentReleaseShipmentData);
    
    // تعبئة النافذة المنبثقة
    document.getElementById('modalReleaseTrackingNumber').value = currentReleaseShipmentData.trackingNumber;
    document.getElementById('modalCurrentReleaseStatus').textContent = currentReleaseShipmentData.currentReleaseStatusDisplay;
    document.getElementById('modalCurrentReleaseStatus').className = `badge bg-${getReleaseStatusColor(currentReleaseShipmentData.currentReleaseStatus)} fs-6`;
    
    // مسح حقل الملاحظات
    document.getElementById('releaseStatusNotes').value = '';
    
    // تحميل قائمة حالات الإفراج المتاحة
    loadAvailableReleaseStatuses();
    
    // إظهار النافذة المنبثقة
    const modal = new bootstrap.Modal(document.getElementById('releaseStatusEditModal'));
    modal.show();
}

/**
 * تحميل الحالات المتاحة
 */
function loadAvailableStatuses() {
    console.log('🔄 تحميل الحالات المتاحة من قاعدة البيانات...');
    
    const select = document.getElementById('newStatus');
    select.innerHTML = '<option value="">جاري تحميل الحالات...</option>';
    
    // إضافة timestamp لتجنب cache
    const timestamp = new Date().getTime();
    fetch(`/shipments/get_available_statuses?v=${timestamp}`)
        .then(response => {
            console.log('📡 استجابة الخادم:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📋 البيانات المستلمة:', data);
            select.innerHTML = '<option value="">اختر الحالة الجديدة...</option>';
            
            if (data.success && data.statuses) {
                console.log(`✅ تم جلب ${data.statuses.length} حالة من قاعدة البيانات`);
                
                data.statuses.forEach((status, index) => {
                    console.log(`${index + 1}. معالجة الحالة: ${status.code} → ${status.name}`);
                    
                    const option = document.createElement('option');
                    option.value = status.code;
                    option.textContent = status.name;
                    option.setAttribute('data-color', status.color);
                    
                    // تمييز الحالة الحالية
                    if (status.code === currentShipmentData.currentStatus) {
                        option.textContent = status.name + ' (الحالة الحالية)';
                        option.style.fontWeight = 'bold';
                        option.style.backgroundColor = '#fff3cd';
                        console.log(`   🔵 تم إضافة الحالة الحالية: ${status.code} → ${status.name}`);
                    } else {
                        console.log(`   ✅ تم إضافة: ${status.code} → ${status.name}`);
                    }
                    
                    select.appendChild(option);
                });
            } else {
                console.warn('⚠️ فشل في جلب الحالات، استخدام القائمة الاحتياطية');
                
                // في حالة الخطأ، استخدم قائمة احتياطية
                const fallbackStatuses = [
                    { code: 'draft', name: 'مسودة' },
                    { code: 'confirmed', name: 'مؤكدة' },
                    { code: 'in_transit', name: 'قيد الشحن' },
                    { code: 'arrived_port', name: 'وصلت للميناء' },
                    { code: 'customs_clearance', name: 'قيد التخليص' },
                    { code: 'ready_pickup', name: 'جاهزة للاستلام' },
                    { code: 'delivered', name: 'تم التسليم' },
                    { code: 'cancelled', name: 'ملغية' },
                    { code: 'delayed', name: 'متأخرة' },
                    { code: 'returned', name: 'معادة' }
                ];
                
                fallbackStatuses.forEach(status => {
                    const option = document.createElement('option');
                    option.value = status.code;
                    option.textContent = status.name;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل الحالات:', error);
            select.innerHTML = '<option value="">خطأ في تحميل الحالات</option>';
        });
}

/**
 * تحميل حالات الإفراج المتاحة
 */
function loadAvailableReleaseStatuses() {
    console.log('🔄 تحميل حالات الإفراج المتاحة من قاعدة البيانات...');
    
    const select = document.getElementById('newReleaseStatus');
    select.innerHTML = '<option value="">جاري تحميل حالات الإفراج...</option>';
    
    // إضافة timestamp لتجنب cache
    const timestamp = new Date().getTime();
    fetch(`/shipments/get_available_release_statuses?v=${timestamp}`)
        .then(response => {
            console.log('📡 استجابة الخادم:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📋 البيانات المستلمة:', data);
            select.innerHTML = '<option value="">اختر حالة الإفراج الجديدة...</option>';
            
            if (data.success && data.statuses) {
                console.log(`✅ تم جلب ${data.statuses.length} حالة إفراج من قاعدة البيانات`);
                
                data.statuses.forEach((status, index) => {
                    console.log(`${index + 1}. معالجة حالة الإفراج: ${status.code} → ${status.name}`);
                    
                    const option = document.createElement('option');
                    option.value = status.code;
                    option.textContent = status.name;
                    option.setAttribute('data-color', status.color);
                    
                    // تمييز الحالة الحالية
                    if (status.code === currentReleaseShipmentData.currentReleaseStatus) {
                        option.textContent = status.name + ' (الحالة الحالية)';
                        option.style.fontWeight = 'bold';
                        option.style.backgroundColor = '#fff3cd';
                        console.log(`   🔵 تم إضافة الحالة الحالية: ${status.code} → ${status.name}`);
                    } else {
                        console.log(`   ✅ تم إضافة: ${status.code} → ${status.name}`);
                    }
                    
                    select.appendChild(option);
                });
            } else {
                console.warn('⚠️ فشل في جلب حالات الإفراج، استخدام القائمة الاحتياطية');
                
                // في حالة الخطأ، استخدم قائمة احتياطية
                const fallbackStatuses = [
                    { code: 'pending', name: 'في انتظار الإفراج' },
                    { code: 'documents_review', name: 'مراجعة المستندات' },
                    { code: 'payment_verification', name: 'التحقق من المدفوعات' },
                    { code: 'quality_check', name: 'فحص الجودة' },
                    { code: 'approved', name: 'معتمد للإفراج' },
                    { code: 'released', name: 'تم الإفراج' },
                    { code: 'on_hold', name: 'محجوز مؤقت' },
                    { code: 'rejected', name: 'مرفوض الإفراج' }
                ];
                
                fallbackStatuses.forEach(status => {
                    const option = document.createElement('option');
                    option.value = status.code;
                    option.textContent = status.name;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل حالات الإفراج:', error);
            select.innerHTML = '<option value="">خطأ في تحميل حالات الإفراج</option>';
        });
}

/**
 * الحصول على لون الحالة
 */
function getStatusColor(status) {
    const statusColors = {
        'draft': 'secondary',
        'confirmed': 'primary',
        'in_transit': 'warning',
        'arrived_port': 'info',
        'customs_clearance': 'warning',
        'ready_pickup': 'success',
        'delivered': 'success',
        'cancelled': 'danger',
        'delayed': 'danger',
        'returned': 'secondary'
    };
    return statusColors[status] || 'secondary';
}

/**
 * الحصول على لون حالة الإفراج
 */
function getReleaseStatusColor(status) {
    const statusColors = {
        'pending': 'secondary',
        'documents_review': 'info',
        'payment_verification': 'warning',
        'quality_check': 'primary',
        'approved': 'success',
        'released': 'success',
        'on_hold': 'danger',
        'rejected': 'danger'
    };
    return statusColors[status] || 'secondary';
}

/**
 * تحديث حالة الشحنة
 */
function updateShipmentStatus() {
    const newStatus = document.getElementById('newStatus').value;
    const notes = document.getElementById('statusNotes').value;

    if (!newStatus) {
        alert('يرجى اختيار الحالة الجديدة');
        return;
    }

    const saveBtn = event.target;
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري التحديث...';
    saveBtn.disabled = true;

    const data = {
        shipment_id: currentShipmentData.id,
        new_status: newStatus,
        notes: notes
    };

    fetch('/shipments/update_status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage('تم تحديث حالة الشحنة بنجاح!');

            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('statusEditModal'));
            modal.hide();

            // تحديث فوري للحالة في الجدول بدلاً من إعادة التحميل
            updateShipmentStatusInTable(data.shipment_id || currentShipmentData.id, data.new_status);

            // إعادة تحميل الصفحة كـ backup بعد تأخير أطول
            setTimeout(() => {
                location.reload();
            }, 3000);
        } else {
            alert('خطأ في تحديث حالة الشحنة: ' + (data.message || 'خطأ غير معروف'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال بالخادم');
    })
    .finally(() => {
        // إعادة تعيين زر الحفظ
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    });
}

/**
 * تحديث حالة الشحنة في الجدول فورياً
 */
function updateShipmentStatusInTable(shipmentId, newStatus) {
    try {
        // البحث عن صف الشحنة في الجدول
        const table = document.querySelector('#shipmentsTable tbody');
        if (!table) return;

        const rows = table.querySelectorAll('tr');

        for (let row of rows) {
            // البحث عن الصف الذي يحتوي على معرف الشحنة
            const idCell = row.querySelector('td:first-child');
            if (idCell && idCell.textContent.trim() == shipmentId) {

                // العثور على خلية الحالة (عادة العمود الرابع أو الخامس)
                const statusCell = row.querySelector('td:nth-child(5)') || row.querySelector('td:nth-child(4)');

                if (statusCell) {
                    // تحديث نص الحالة
                    const statusMap = {
                        'customs_clearance': 'قيد التخليص الجمركي',
                        'delivered': 'تم التسليم',
                        'in_transit': 'في الطريق',
                        'arrived_port': 'وصل الميناء',
                        'pending': 'في الانتظار'
                    };

                    const statusText = statusMap[newStatus] || newStatus;

                    // تحديث النص مع تأثير بصري
                    statusCell.innerHTML = `<span class="badge bg-warning">${statusText}</span>`;

                    // إضافة تأثير بصري للتحديث
                    statusCell.style.backgroundColor = '#fff3cd';
                    statusCell.style.transition = 'background-color 2s ease';

                    setTimeout(() => {
                        statusCell.style.backgroundColor = '';
                    }, 2000);

                    console.log(`✅ تم تحديث حالة الشحنة ${shipmentId} إلى ${statusText} في الجدول`);
                    break;
                }
            }
        }

        // تحديث البيانات المحفوظة إذا كانت الشحنة الحالية
        if (currentShipmentData && currentShipmentData.id == shipmentId) {
            currentShipmentData.shipment_status = newStatus;
        }

    } catch (error) {
        console.error('خطأ في تحديث الجدول:', error);
    }
}

/**
 * تحديث حالة الإفراج
 */
function updateReleaseStatus() {
    const newReleaseStatus = document.getElementById('newReleaseStatus').value;
    const notes = document.getElementById('releaseStatusNotes').value;

    if (!newReleaseStatus) {
        alert('يرجى اختيار حالة الإفراج الجديدة');
        return;
    }

    const saveBtn = event.target;
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري التحديث...';
    saveBtn.disabled = true;

    const data = {
        shipment_id: currentReleaseShipmentData.id,
        new_release_status: newReleaseStatus,
        notes: notes
    };

    fetch('/shipments/api/update_release_status_quick', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage('تم تحديث حالة الإفراج بنجاح!');

            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('releaseStatusEditModal'));
            modal.hide();

            // إعادة تحميل الصفحة لإظهار التحديثات
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            alert('خطأ في تحديث حالة الإفراج: ' + (data.message || 'خطأ غير معروف'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال بالخادم');
    })
    .finally(() => {
        // إعادة تعيين زر الحفظ
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    });
}

/**
 * تحديث حالة الشحنة
 */
function updateShipmentStatus() {
    const newStatus = document.getElementById('newStatus').value;
    const notes = document.getElementById('statusNotes').value;

    if (!newStatus) {
        alert('يرجى اختيار الحالة الجديدة');
        return;
    }

    const saveBtn = event.target;
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري التحديث...';
    saveBtn.disabled = true;

    const data = {
        shipment_id: currentShipmentData.id,
        new_status: newStatus,
        notes: notes
    };

    fetch('/shipments/update_status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage('تم تحديث حالة الشحنة بنجاح!');

            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('statusEditModal'));
            modal.hide();

            // إعادة تحميل الصفحة لإظهار التحديثات
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            alert('خطأ في تحديث حالة الشحنة: ' + (data.message || 'خطأ غير معروف'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال بالخادم');
    })
    .finally(() => {
        // إعادة تعيين زر الحفظ
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    });
}

/**
 * تحديث حالة الإفراج
 */
function updateReleaseStatus() {
    const newReleaseStatus = document.getElementById('newReleaseStatus').value;
    const notes = document.getElementById('releaseStatusNotes').value;

    if (!newReleaseStatus) {
        alert('يرجى اختيار حالة الإفراج الجديدة');
        return;
    }

    const saveBtn = event.target;
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري التحديث...';
    saveBtn.disabled = true;

    const data = {
        shipment_id: currentReleaseShipmentData.id,
        new_release_status: newReleaseStatus,
        notes: notes
    };

    fetch('/shipments/api/update_release_status_quick', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage('تم تحديث حالة الإفراج بنجاح!');

            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('releaseStatusEditModal'));
            modal.hide();

            // إعادة تحميل الصفحة لإظهار التحديثات
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            alert('خطأ في تحديث حالة الإفراج: ' + (data.message || 'خطأ غير معروف'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال بالخادم');
    })
    .finally(() => {
        // إعادة تعيين زر الحفظ
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    });
}

// متغيرات البحث والتصفية
let allShipments = [];
let filteredShipments = [];

// متغيرات البحث الصوتي
let recognition = null;
let isListening = false;
let recognitionBusy = false; // لتجنب التضارب

/**
 * البحث السريع الفوري
 */
function performQuickSearch() {
    const searchInput = document.getElementById('quickSearch');
    if (!searchInput) {
        console.warn('⚠️ حقل البحث السريع غير موجود');
        return;
    }

    const searchTerm = searchInput.value.toLowerCase().trim();
    console.log(`🔍 البحث عن: "${searchTerm}"`);

    // التأكد من وجود البيانات
    if (!allShipments || allShipments.length === 0) {
        console.log('📊 لا توجد بيانات شحنات، محاولة جمع البيانات...');
        collectAllShipments();

        // إذا لم تنجح عملية الجمع، استخدم البحث المباشر
        if (!allShipments || allShipments.length === 0) {
            console.log('🔄 استخدام البحث المباشر في الجدول...');
            performDirectTableSearch(searchTerm);
            return;
        }
    }

    if (searchTerm === '') {
        // إذا كان البحث فارغ، أظهر جميع الشحنات
        console.log('🔄 البحث فارغ، عرض جميع الشحنات...');
        filteredShipments = [...allShipments];
        updateDisplay();
        return;
    }

    // البحث في النص المجمع
    filteredShipments = allShipments.filter(shipment => {
        const found = shipment.searchText.includes(searchTerm);

        // تسجيل تفصيلي للنتيجة الأولى
        if (found && filteredShipments.length === 0) {
            console.log('✅ تم العثور على تطابق:', {
                trackingNumber: shipment.trackingNumber,
                searchText: shipment.searchText.substring(0, 100) + '...'
            });
        }

        return found;
    });

    console.log(`📊 نتائج البحث: ${filteredShipments.length} من ${allShipments.length}`);

    // طبق المرشحات الأخرى على نتائج البحث
    applyOtherFilters();
    updateDisplay();
}

/**
 * البحث المباشر في الجدول (احتياطي)
 */
function performDirectTableSearch(searchTerm) {
    console.log('🔍 تنفيذ البحث المباشر في الجدول...');

    // البحث في جدول الشحنات - محاولة عدة selectors
    const table = document.querySelector('#shipmentsTable tbody') ||
                  document.querySelector('.shipments-table tbody') ||
                  document.querySelector('#shipments-table tbody') ||
                  document.querySelector('table.table tbody') ||
                  document.querySelector('.table tbody') ||
                  document.querySelector('table tbody');

    if (!table) {
        console.warn('⚠️ جدول الشحنات غير موجود');
        return;
    }

    const rows = table.querySelectorAll('tr');
    let visibleCount = 0;

    rows.forEach(row => {
        if (searchTerm === '') {
            row.style.display = '';
            visibleCount++;
        } else {
            const rowText = row.textContent.toLowerCase();

            if (rowText.includes(searchTerm)) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        }
    });

    console.log(`📊 البحث المباشر: عرض ${visibleCount} من ${rows.length} شحنة`);

    // إظهار رسالة إذا لم توجد نتائج
    if (visibleCount === 0 && searchTerm !== '') {
        showNotification(`لم يتم العثور على نتائج للبحث: "${searchTerm}"`, 'warning');
    } else if (searchTerm !== '') {
        showNotification(`تم العثور على ${visibleCount} نتيجة`, 'success');
    }
}

/**
 * جمع جميع الشحنات من الجدول
 */
function collectAllShipments() {
    allShipments = [];

    // محاولة العثور على الجدول بطرق مختلفة
    let rows = document.querySelectorAll('#shipmentsTable tbody tr');

    if (rows.length === 0) {
        // محاولة selectors أخرى
        rows = document.querySelectorAll('.shipments-table tbody tr') ||
               document.querySelectorAll('#shipments-table tbody tr') ||
               document.querySelectorAll('table.table tbody tr') ||
               document.querySelectorAll('.table tbody tr') ||
               document.querySelectorAll('table tbody tr');
    }

    console.log(`🔍 جمع البيانات من ${rows.length} صف...`);

    rows.forEach((row, index) => {
        const cells = row.querySelectorAll('td');
        if (cells.length > 0) {
            // استخراج النص من العناصر المعقدة
            const trackingNumberElement = cells[1]?.querySelector('.tracking-number');
            const trackingNumber = trackingNumberElement ?
                trackingNumberElement.textContent.replace(/\s+/g, ' ').trim() :
                cells[1]?.textContent?.trim() || '';

            const shipmentStatusElement = cells[10]?.querySelector('.shipment-status-badge');
            const shipmentStatus = shipmentStatusElement ?
                shipmentStatusElement.textContent.replace(/\s+/g, ' ').trim() :
                cells[10]?.textContent?.trim() || '';

            const releaseStatusElement = cells[11]?.querySelector('.release-status-badge');
            const releaseStatus = releaseStatusElement ?
                releaseStatusElement.textContent.replace(/\s+/g, ' ').trim() :
                cells[11]?.textContent?.trim() || '';

            const shipment = {
                element: row,
                index: index,
                shipmentDate: cells[0]?.textContent?.trim() || '',
                trackingNumber: trackingNumber,
                sender: cells[2]?.textContent?.trim() || '',
                cargoDetails: cells[3]?.textContent?.trim() || '',
                packageCount: cells[4]?.textContent?.trim() || '',
                destinationPort: cells[5]?.textContent?.trim() || '',
                billOfLading: cells[6]?.textContent?.trim() || '',
                containerNumbers: cells[7]?.textContent?.trim() || '',
                containerCount: cells[8]?.textContent?.trim() || '',
                shippingLine: cells[9]?.textContent?.trim() || '',
                shipmentStatus: shipmentStatus,
                releaseStatus: releaseStatus,
                // إضافة نص مجمع للبحث السريع
                searchText: [
                    cells[0]?.textContent?.trim() || '',
                    trackingNumber,
                    cells[2]?.textContent?.trim() || '',
                    cells[3]?.textContent?.trim() || '',
                    cells[4]?.textContent?.trim() || '',
                    cells[5]?.textContent?.trim() || '',
                    cells[6]?.textContent?.trim() || '',
                    cells[7]?.textContent?.trim() || '',
                    cells[8]?.textContent?.trim() || '',
                    cells[9]?.textContent?.trim() || '',
                    shipmentStatus,
                    releaseStatus
                ].join(' ').toLowerCase()
            };

            allShipments.push(shipment);
        }
    });

    filteredShipments = [...allShipments];
    console.log(`📊 تم جمع ${allShipments.length} شحنة بنجاح`);
}

/**
 * تطبيق المرشحات الأخرى (غير البحث السريع)
 */
function applyOtherFilters() {
    // يمكن إضافة مرشحات أخرى هنا لاحقاً
    console.log('🔍 تطبيق المرشحات الأخرى...');
}

/**
 * تحديث العرض
 */
function updateDisplay() {
    console.log(`📊 تحديث العرض: عرض ${filteredShipments.length} من ${allShipments.length} شحنة`);

    // إخفاء جميع الصفوف
    allShipments.forEach(shipment => {
        shipment.element.style.display = 'none';
    });

    // إظهار الصفوف المفلترة
    filteredShipments.forEach(shipment => {
        shipment.element.style.display = '';
    });

    // تحديث عداد النتائج إذا وجد
    const resultsCounter = document.querySelector('.results-counter');
    if (resultsCounter) {
        resultsCounter.textContent = `عرض ${filteredShipments.length} من ${allShipments.length} شحنة`;
    }
}

/**
 * التحقق من أذونات الميكروفون
 */
async function checkMicrophonePermission() {
    try {
        if (navigator.permissions) {
            const permission = await navigator.permissions.query({ name: 'microphone' });
            console.log('🎤 حالة إذن الميكروفون:', permission.state);
            return permission.state;
        }
        return 'unknown';
    } catch (error) {
        console.warn('⚠️ لا يمكن التحقق من أذونات الميكروفون:', error);
        return 'unknown';
    }
}

/**
 * طلب أذونات الميكروفون
 */
async function requestMicrophonePermission() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        // إيقاف الـ stream فوراً لأننا نريد فقط الحصول على الإذن
        stream.getTracks().forEach(track => track.stop());
        console.log('✅ تم الحصول على إذن الميكروفون');
        return true;
    } catch (error) {
        console.error('❌ فشل في الحصول على إذن الميكروفون:', error);
        return false;
    }
}

/**
 * تشخيص متقدم لمشاكل الميكروفون
 */
async function diagnoseMicrophoneIssue() {
    const diagnosis = {
        issue: 'unknown',
        details: '',
        solution: '',
        technical: ''
    };

    try {
        console.log('🔍 بدء التشخيص المتقدم...');

        // فحص توفر الميكروفون
        const devices = await navigator.mediaDevices.enumerateDevices();
        const audioInputs = devices.filter(device => device.kind === 'audioinput');

        console.log('🎤 عدد أجهزة الميكروفون المكتشفة:', audioInputs.length);

        if (audioInputs.length === 0) {
            diagnosis.issue = 'no_microphone';
            diagnosis.details = 'لم يتم العثور على أي ميكروفون متصل بالجهاز';
            diagnosis.solution = 'تأكد من توصيل ميكروفون وإعادة تحميل الصفحة';
            diagnosis.technical = 'No audio input devices detected';
            return diagnosis;
        }

        // محاولة الوصول للميكروفون مباشرة
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: false,
                    noiseSuppression: false,
                    autoGainControl: false
                }
            });

            // إذا نجحت، فالمشكلة في Speech Recognition
            stream.getTracks().forEach(track => track.stop());

            diagnosis.issue = 'speech_recognition_blocked';
            diagnosis.details = 'الميكروفون يعمل لكن خدمة التعرف على الصوت محجوبة';
            diagnosis.solution = 'تحقق من إعدادات الخصوصية في المتصفح أو جرب متصفح آخر';
            diagnosis.technical = 'MediaDevices access granted but SpeechRecognition blocked';

        } catch (mediaError) {
            console.error('❌ خطأ في الوصول للميكروفون:', mediaError);

            if (mediaError.name === 'NotAllowedError') {
                diagnosis.issue = 'permission_denied';
                diagnosis.details = 'تم رفض الوصول للميكروفون على مستوى النظام أو المتصفح';
                diagnosis.solution = 'امنح الأذونات في إعدادات المتصفح وإعدادات النظام';
                diagnosis.technical = `MediaDevices NotAllowedError: ${mediaError.message}`;
            } else if (mediaError.name === 'NotFoundError') {
                diagnosis.issue = 'device_not_found';
                diagnosis.details = 'الميكروفون غير متاح أو مستخدم من تطبيق آخر';
                diagnosis.solution = 'أغلق التطبيقات الأخرى التي تستخدم الميكروفون';
                diagnosis.technical = `MediaDevices NotFoundError: ${mediaError.message}`;
            } else {
                diagnosis.issue = 'hardware_error';
                diagnosis.details = 'مشكلة في الأجهزة أو تعريفات الميكروفون';
                diagnosis.solution = 'تحقق من تعريفات الميكروفون وإعدادات النظام';
                diagnosis.technical = `MediaDevices Error: ${mediaError.name} - ${mediaError.message}`;
            }
        }

    } catch (error) {
        console.error('❌ خطأ في التشخيص:', error);
        diagnosis.issue = 'diagnosis_failed';
        diagnosis.details = 'فشل في تشخيص المشكلة';
        diagnosis.solution = 'أعد تحميل الصفحة أو جرب متصفح آخر';
        diagnosis.technical = `Diagnosis error: ${error.message}`;
    }

    console.log('📋 نتيجة التشخيص:', diagnosis);
    return diagnosis;
}

/**
 * تشخيص متقدم لمشاكل الميكروفون
 */
async function diagnoseMicrophoneIssue() {
    const diagnosis = {
        issue: 'unknown',
        details: '',
        solution: '',
        technical: ''
    };

    try {
        console.log('🔍 بدء التشخيص المتقدم...');

        // فحص توفر الميكروفون
        const devices = await navigator.mediaDevices.enumerateDevices();
        const audioInputs = devices.filter(device => device.kind === 'audioinput');

        console.log('🎤 عدد أجهزة الميكروفون المكتشفة:', audioInputs.length);

        if (audioInputs.length === 0) {
            diagnosis.issue = 'no_microphone';
            diagnosis.details = 'لم يتم العثور على أي ميكروفون متصل بالجهاز';
            diagnosis.solution = 'تأكد من توصيل ميكروفون وإعادة تحميل الصفحة';
            diagnosis.technical = 'No audio input devices detected';
            return diagnosis;
        }

        // محاولة الوصول للميكروفون مباشرة
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: false,
                    noiseSuppression: false,
                    autoGainControl: false
                }
            });

            // إذا نجحت، فالمشكلة في Speech Recognition
            stream.getTracks().forEach(track => track.stop());

            diagnosis.issue = 'speech_recognition_blocked';
            diagnosis.details = 'الميكروفون يعمل لكن خدمة التعرف على الصوت محجوبة';
            diagnosis.solution = 'تحقق من إعدادات الخصوصية في المتصفح أو جرب متصفح آخر';
            diagnosis.technical = 'MediaDevices access granted but SpeechRecognition blocked';

        } catch (mediaError) {
            console.error('❌ خطأ في الوصول للميكروفون:', mediaError);

            if (mediaError.name === 'NotAllowedError') {
                diagnosis.issue = 'permission_denied';
                diagnosis.details = 'تم رفض الوصول للميكروفون على مستوى النظام أو المتصفح';
                diagnosis.solution = 'امنح الأذونات في إعدادات المتصفح وإعدادات النظام';
                diagnosis.technical = `MediaDevices NotAllowedError: ${mediaError.message}`;
            } else if (mediaError.name === 'NotFoundError') {
                diagnosis.issue = 'device_not_found';
                diagnosis.details = 'الميكروفون غير متاح أو مستخدم من تطبيق آخر';
                diagnosis.solution = 'أغلق التطبيقات الأخرى التي تستخدم الميكروفون';
                diagnosis.technical = `MediaDevices NotFoundError: ${mediaError.message}`;
            } else {
                diagnosis.issue = 'hardware_error';
                diagnosis.details = 'مشكلة في الأجهزة أو تعريفات الميكروفون';
                diagnosis.solution = 'تحقق من تعريفات الميكروفون وإعدادات النظام';
                diagnosis.technical = `MediaDevices Error: ${mediaError.name} - ${mediaError.message}`;
            }
        }

    } catch (error) {
        console.error('❌ خطأ في التشخيص:', error);
        diagnosis.issue = 'diagnosis_failed';
        diagnosis.details = 'فشل في تشخيص المشكلة';
        diagnosis.solution = 'أعد تحميل الصفحة أو جرب متصفح آخر';
        diagnosis.technical = `Diagnosis error: ${error.message}`;
    }

    console.log('📋 نتيجة التشخيص:', diagnosis);
    return diagnosis;
}

/**
 * دالة إصلاح الأرقام في النص الصوتي
 */
function fixNumbers(text) {
    if (!text) return '';

    // إصلاح الأرقام المتباعدة (مثل: "1 2 3 4" → "1234")
    text = text.replace(/(\d)\s+(\d)/g, '$1$2');

    // إصلاح الأرقام المتباعدة بشكل متكرر (للحالات الطويلة)
    for (let i = 0; i < 5; i++) {
        text = text.replace(/(\d)\s+(\d)/g, '$1$2');
    }

    // تحويل الأرقام المنطوقة إلى أرقام
    const spokenNumbers = {
        'صفر': '0', 'واحد': '1', 'اثنان': '2', 'اثنين': '2', 'ثلاثة': '3',
        'أربعة': '4', 'خمسة': '5', 'ستة': '6', 'سبعة': '7', 'ثمانية': '8',
        'تسعة': '9', 'عشرة': '10',
        'zero': '0', 'one': '1', 'two': '2', 'three': '3', 'four': '4',
        'five': '5', 'six': '6', 'seven': '7', 'eight': '8', 'nine': '9', 'ten': '10'
    };

    Object.keys(spokenNumbers).forEach(word => {
        const regex = new RegExp('\\b' + word + '\\b', 'gi');
        text = text.replace(regex, spokenNumbers[word]);
    });

    // إصلاح الأرقام بعد التحويل
    text = text.replace(/(\d)\s+(\d)/g, '$1$2');

    // تنظيف المسافات
    text = text.replace(/\s+/g, ' ').trim();

    return text;
}

/**
 * تهيئة البحث الصوتي
 */
async function initializeVoiceSearch() {
    console.log('🎤 تهيئة البحث الصوتي...');
        // البحث الصوتي متاح على جميع البروتوكولات
        console.log('🌐 البروتوكول المستخدم:', window.location.protocol);
        console.log('🌐 المضيف:', window.location.hostname);
        console.log('✅ البحث الصوتي متاح على HTTP و HTTPS');

    // التحقق من دعم المتصفح للبحث الصوتي
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        console.warn('⚠️ المتصفح لا يدعم البحث الصوتي');
        const voiceBtn = document.getElementById('voiceSearchBtn');
        if (voiceBtn) {
            voiceBtn.style.display = 'none';
            // إضافة tooltip يوضح عدم الدعم
            voiceBtn.parentElement.insertAdjacentHTML('afterend',
                '<small class="text-muted">البحث الصوتي غير مدعوم في هذا المتصفح</small>'
            );
        }
        return;
    }

    // التحقق من أذونات الميكروفون
    const permissionState = await checkMicrophonePermission();
    console.log('🔍 حالة أذونات الميكروفون:', permissionState);

    // تحديث واجهة الزر بناءً على حالة الأذونات
    const voiceBtn = document.getElementById('voiceSearchBtn');
    if (voiceBtn) {
        if (permissionState === 'denied') {
            voiceBtn.classList.add('btn-outline-danger');
            voiceBtn.title = 'تم رفض إذن الميكروفون - انقر للمساعدة';
        } else if (permissionState === 'granted') {
            voiceBtn.classList.add('btn-outline-success');
            voiceBtn.title = 'البحث الصوتي جاهز';
        }
    }

    // إنشاء كائن التعرف على الصوت
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    recognition = new SpeechRecognition();

    // إعدادات التعرف على الصوت
    recognition.lang = 'ar-SA'; // العربية السعودية
    recognition.continuous = false;
    recognition.interimResults = false;
    recognition.maxAlternatives = 1;

    // عند بدء الاستماع
    recognition.onstart = function() {
        console.log('🎤 بدء الاستماع...');
        isListening = true;
        recognitionBusy = true;
        updateVoiceSearchUI();
    };

    // عند انتهاء الاستماع
    recognition.onend = function() {
        console.log('🎤 انتهاء الاستماع');
        isListening = false;
        recognitionBusy = false;
        updateVoiceSearchUI();
    };

    // عند التعرف على النص
    recognition.onresult = function(event) {
        let transcript = event.results[0][0].transcript;
        console.log('🎤 النص المسموع:', transcript);

        // إزالة النقطة من النهاية فقط
        transcript = transcript.replace(/\.$/, '').trim();

        // إصلاح الأرقام المتباعدة
        transcript = fixNumbers(transcript);
        console.log('🎤 النص بعد إصلاح الأرقام:', transcript);

        // وضع النص في حقل البحث
        const searchInput = document.getElementById('quickSearch');
        if (searchInput) {
            searchInput.value = transcript;
            performQuickSearch();

            // إظهار رسالة نجاح
            showSuccessMessage(`تم البحث عن: "${transcript}"`);
        }
    };

    // عند حدوث خطأ
    recognition.onerror = function(event) {
        console.error('❌ خطأ في البحث الصوتي:', event.error);
        isListening = false;
        recognitionBusy = false;
        updateVoiceSearchUI();

        let errorMessage = 'حدث خطأ في البحث الصوتي';
        let showInstructions = false;
        let retryable = false;

        switch(event.error) {
            case 'no-speech':
                errorMessage = 'لم يتم سماع أي صوت. حاول مرة أخرى.';
                retryable = true;
                break;
            case 'audio-capture':
                errorMessage = 'لا يمكن الوصول للميكروفون. تأكد من توصيل الميكروفون.';
                showInstructions = true;
                break;
            case 'not-allowed':
                // تشخيص متقدم للمشكلة
                console.log('🔍 تشخيص مشكلة not-allowed...');

                // فحص الأذونات أولاً
                checkMicrophonePermission().then(permissionState => {
                    console.log('🔍 حالة الأذونات:', permissionState);

                    if (permissionState === 'granted') {
                        // الأذونات ممنوحة - المشكلة في مكان آخر
                        console.log('⚠️ الأذونات ممنوحة لكن هناك مشكلة أخرى');

                        // محاولة تشخيص أعمق
                        diagnoseMicrophoneIssue().then(diagnosis => {
                            showAdvancedErrorDialog(diagnosis);
                        });
                    } else if (permissionState === 'denied') {
                        // الأذونات مرفوضة صراحة
                        console.log('❌ الأذونات مرفوضة صراحة');
                        errorMessage = 'تم رفض الوصول للميكروفون صراحة. يرجى السماح بالوصول في إعدادات المتصفح.';
                        showMicrophonePermissionDialog(errorMessage);
                    } else {
                        // حالة غير واضحة
                        console.log('❓ حالة أذونات غير واضحة');
                        errorMessage = 'مشكلة في أذونات الميكروفون. يرجى التحقق من إعدادات المتصفح.';
                        showMicrophonePermissionDialog(errorMessage);
                    }
                }).catch(error => {
                    console.error('❌ خطأ في فحص الأذونات:', error);
                    showMicrophonePermissionDialog('لا يمكن التحقق من أذونات الميكروفون. يرجى التحقق من إعدادات المتصفح.');
                });
                return; // خروج مبكر لتجنب عرض رسالة مكررة
            case 'network':
                errorMessage = 'خطأ في الشبكة. تحقق من الاتصال بالإنترنت.';
                retryable = true;
                break;
            case 'service-not-allowed':
                errorMessage = 'خدمة التعرف على الصوت غير مسموحة. تحقق من إعدادات المتصفح.';
                showInstructions = true;
                break;
            case 'aborted':
                errorMessage = 'تم إلغاء البحث الصوتي.';
                retryable = true;
                break;
        }

        if (showInstructions) {
            showMicrophonePermissionDialog(errorMessage);
        } else if (retryable) {
            showRetryDialog(errorMessage);
        } else {
            showErrorMessage(errorMessage);
        }
    };

    console.log('✅ تم تهيئة البحث الصوتي بنجاح');
}

/**
 * تبديل البحث الصوتي
 */
async function toggleVoiceSearch() {
    if (!recognition) {
        showErrorMessage('البحث الصوتي غير مدعوم في هذا المتصفح');
        return;
    }

    // تجنب التضارب
    if (recognitionBusy) {
        console.log('⚠️ البحث الصوتي مشغول، انتظار...');
        showRetryDialog('البحث الصوتي مشغول حالياً. انتظر قليلاً وحاول مرة أخرى.');
        return;
    }

    if (isListening) {
        // إيقاف الاستماع
        recognitionBusy = true;
        recognition.stop();
        console.log('🛑 تم إيقاف البحث الصوتي');
        return;
    }

    // التحقق من الأذونات قبل البدء
    const permissionState = await checkMicrophonePermission();

    if (permissionState === 'denied') {
        showMicrophonePermissionDialog('تم رفض الوصول للميكروفون مسبقاً. يرجى السماح بالوصول في إعدادات المتصفح.');
        return;
    }

    // محاولة طلب الأذونات إذا لم تكن ممنوحة
    if (permissionState !== 'granted') {
        console.log('🔄 طلب أذونات الميكروفون...');
        const hasPermission = await requestMicrophonePermission();

        if (!hasPermission) {
            showMicrophonePermissionDialog('فشل في الحصول على إذن الميكروفون. يرجى السماح بالوصول والمحاولة مرة أخرى.');
            return;
        }
    }

    // بدء الاستماع مع معالجة التضارب
    try {
        // التأكد من أن recognition ليس قيد التشغيل
        if (recognition.state === 'listening') {
            console.log('⚠️ البحث الصوتي قيد التشغيل بالفعل، إيقافه أولاً...');
            recognition.stop();

            // انتظار قصير ثم إعادة المحاولة
            setTimeout(() => {
                try {
                    recognition.start();
                    console.log('🎤 تم بدء البحث الصوتي بعد إعادة التعيين');
                } catch (retryError) {
                    console.error('❌ فشل في إعادة بدء البحث الصوتي:', retryError);
                    showRetryDialog('فشل في بدء البحث الصوتي. جرب مرة أخرى.');
                }
            }, 100);
            return;
        }

        recognition.start();
        console.log('🎤 تم بدء البحث الصوتي');
    } catch (error) {
        console.error('❌ خطأ في بدء البحث الصوتي:', error);

        if (error.name === 'InvalidStateError') {
            showRetryDialog('البحث الصوتي قيد التشغيل بالفعل. جاري إعادة التعيين...');

            // محاولة إعادة تعيين recognition
            setTimeout(() => {
                try {
                    recognition.stop();
                    setTimeout(() => {
                        recognition.start();
                    }, 200);
                } catch (resetError) {
                    console.error('❌ فشل في إعادة تعيين البحث الصوتي:', resetError);
                    showMicrophonePermissionDialog('مشكلة في إعادة تعيين البحث الصوتي. أعد تحميل الصفحة.');
                }
            }, 100);
        } else {
            showMicrophonePermissionDialog('لا يمكن بدء البحث الصوتي. تحقق من إعدادات الميكروفون.');
        }
    }
}

/**
 * تحديث واجهة البحث الصوتي
 */
function updateVoiceSearchUI() {
    const voiceBtn = document.getElementById('voiceSearchBtn');
    const voiceIcon = document.getElementById('voiceSearchIcon');
    const voiceStatus = document.getElementById('voiceSearchStatus');

    if (!voiceBtn || !voiceIcon || !voiceStatus) return;

    if (isListening) {
        // حالة الاستماع
        voiceBtn.classList.add('listening');
        voiceIcon.className = 'fas fa-stop';
        voiceBtn.title = 'إيقاف البحث الصوتي';
        voiceStatus.style.display = 'block';
    } else {
        // حالة عدم الاستماع
        voiceBtn.classList.remove('listening');
        voiceIcon.className = 'fas fa-microphone';
        voiceBtn.title = 'البحث الصوتي';
        voiceStatus.style.display = 'none';
    }
}

/**
 * إظهار نافذة إعادة المحاولة
 */
function showRetryDialog(errorMessage) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-warning alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 350px; max-width: 500px;';
    alertDiv.innerHTML = `
        <div class="d-flex align-items-start">
            <i class="fas fa-exclamation-triangle me-3 mt-1"></i>
            <div class="flex-grow-1">
                <strong>مشكلة مؤقتة في البحث الصوتي</strong>
                <div class="mt-1">${errorMessage}</div>
                <div class="mt-3">
                    <button type="button" class="btn btn-warning btn-sm me-2" onclick="retryVoiceSearch()">
                        <i class="fas fa-redo me-1"></i>
                        إعادة المحاولة
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" data-bs-dismiss="alert">
                        <i class="fas fa-times me-1"></i>
                        إغلاق
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(alertDiv);

    // إزالة الرسالة تلقائياً بعد 10 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 10000);
}

/**
 * إعادة محاولة البحث الصوتي
 */
function retryVoiceSearch() {
    // إغلاق أي رسائل تحذير مفتوحة
    const alerts = document.querySelectorAll('.alert-warning');
    alerts.forEach(alert => alert.remove());

    // انتظار قصير ثم إعادة المحاولة
    setTimeout(() => {
        console.log('🔄 إعادة محاولة البحث الصوتي...');
        toggleVoiceSearch();
    }, 500);
}

/**
 * إظهار نافذة خطأ متقدمة مع تشخيص
 */
function showAdvancedErrorDialog(diagnosis) {
    // إنشاء نافذة منبثقة متقدمة
    const modalHtml = `
        <div class="modal fade" id="advancedErrorModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            تشخيص مشكلة البحث الصوتي
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="alert alert-danger">
                                    <h6 class="fw-bold mb-2">
                                        <i class="fas fa-bug me-2"></i>
                                        المشكلة المكتشفة:
                                    </h6>
                                    <p class="mb-0">${diagnosis.details}</p>
                                </div>

                                <div class="alert alert-info">
                                    <h6 class="fw-bold mb-2">
                                        <i class="fas fa-lightbulb me-2"></i>
                                        الحل المقترح:
                                    </h6>
                                    <p class="mb-0">${diagnosis.solution}</p>
                                </div>

                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0">
                                            <i class="fas fa-tools me-2"></i>
                                            خطوات الحل التفصيلية:
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        ${getDetailedSolution(diagnosis.issue)}
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-question-circle me-2"></i>
                                            هل تحتاج مساعدة؟
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-grid gap-2">
                                            <button type="button" class="btn btn-primary btn-sm" onclick="runMicrophoneTest()">
                                                <i class="fas fa-microphone-alt me-1"></i>
                                                اختبار الميكروفون
                                            </button>
                                            <button type="button" class="btn btn-warning btn-sm" onclick="retryVoiceSearch(); $('#advancedErrorModal').modal('hide');">
                                                <i class="fas fa-redo me-1"></i>
                                                إعادة المحاولة
                                            </button>
                                            <button type="button" class="btn btn-info btn-sm" onclick="showBrowserSettings()">
                                                <i class="fas fa-cog me-1"></i>
                                                إعدادات المتصفح
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="card border-secondary mt-3">
                                    <div class="card-header bg-secondary text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-code me-2"></i>
                                            معلومات تقنية
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <small class="text-muted">${diagnosis.technical}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>
                            إغلاق
                        </button>
                        <button type="button" class="btn btn-primary" onclick="location.reload()">
                            <i class="fas fa-refresh me-1"></i>
                            إعادة تحميل الصفحة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة النافذة للصفحة
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById('advancedErrorModal'));
    modal.show();

    // إزالة النافذة عند الإغلاق
    document.getElementById('advancedErrorModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

/**
 * الحصول على حل تفصيلي حسب نوع المشكلة
 */
function getDetailedSolution(issueType) {
    switch(issueType) {
        case 'no_microphone':
            return `
                <ol>
                    <li>تأكد من توصيل ميكروفون بالجهاز</li>
                    <li>تحقق من أن الميكروفون يعمل في تطبيقات أخرى</li>
                    <li>أعد تشغيل المتصفح</li>
                    <li>أعد تحميل الصفحة</li>
                </ol>
            `;
        case 'permission_denied':
            return `
                <ol>
                    <li>انقر على أيقونة القفل 🔒 في شريط العنوان</li>
                    <li>اختر "الميكروفون" → "السماح"</li>
                    <li>أعد تحميل الصفحة</li>
                    <li>تحقق من إعدادات الخصوصية في Windows</li>
                </ol>
            `;
        case 'device_not_found':
            return `
                <ol>
                    <li>أغلق Zoom, Teams, Skype وأي تطبيقات مكالمات</li>
                    <li>أغلق تطبيقات التسجيل الصوتي</li>
                    <li>انتظر 10 ثوان</li>
                    <li>أعد المحاولة</li>
                </ol>
            `;
        case 'speech_recognition_blocked':
            return `
                <ol>
                    <li>تحقق من إعدادات الخصوصية في المتصفح</li>
                    <li>جرب متصفح Chrome أو Edge</li>
                    <li>تأكد من الاتصال بالإنترنت</li>
                    <li>امسح cache المتصفح</li>
                </ol>
            `;
        default:
            return `
                <ol>
                    <li>أعد تحميل الصفحة</li>
                    <li>جرب متصفح آخر</li>
                    <li>أعد تشغيل المتصفح</li>
                    <li>تحقق من تحديثات المتصفح</li>
                </ol>
            `;
    }
}

/**
 * إظهار نافذة تعليمات أذونات الميكروفون
 */
function showMicrophonePermissionDialog(errorMessage) {
    // إنشاء نافذة منبثقة مخصصة
    const modalHtml = `
        <div class="modal fade" id="micPermissionModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title">
                            <i class="fas fa-microphone-slash me-2"></i>
                            مطلوب إذن الوصول للميكروفون
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ${errorMessage}
                        </div>

                        <h6 class="fw-bold mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            كيفية السماح بالوصول للميكروفون:
                        </h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-primary mb-3">
                                    <div class="card-header bg-primary text-white">
                                        <i class="fab fa-chrome me-2"></i>
                                        Google Chrome
                                    </div>
                                    <div class="card-body">
                                        <ol class="small">
                                            <li>انقر على أيقونة القفل 🔒 في شريط العنوان</li>
                                            <li>اختر "الميكروفون" → "السماح"</li>
                                            <li>أعد تحميل الصفحة</li>
                                            <li>جرب البحث الصوتي مرة أخرى</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-info mb-3">
                                    <div class="card-header bg-info text-white">
                                        <i class="fab fa-edge me-2"></i>
                                        Microsoft Edge
                                    </div>
                                    <div class="card-body">
                                        <ol class="small">
                                            <li>انقر على أيقونة القفل 🔒 في شريط العنوان</li>
                                            <li>اختر "أذونات الموقع"</li>
                                            <li>قم بتشغيل "الميكروفون"</li>
                                            <li>أعد تحميل الصفحة</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>نصيحة:</strong> تأكد من أن الميكروفون متصل ويعمل بشكل صحيح قبل المحاولة مرة أخرى.
                        </div>

                        <div class="alert alert-success">
                            <i class="fas fa-shield-alt me-2"></i>
                            <strong>الخصوصية:</strong> نحن نحترم خصوصيتك. البحث الصوتي يتم معالجته محلياً ولا يتم حفظ أي تسجيلات صوتية.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>
                            إغلاق
                        </button>
                        <button type="button" class="btn btn-primary" onclick="location.reload()">
                            <i class="fas fa-refresh me-1"></i>
                            إعادة تحميل الصفحة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة النافذة للصفحة
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById('micPermissionModal'));
    modal.show();

    // إزالة النافذة عند الإغلاق
    document.getElementById('micPermissionModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

/**
 * عرض رسالة خطأ
 */
function showErrorMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

/**
 * اختبار الميكروفون
 */
function runMicrophoneTest() {
    console.log('🧪 بدء اختبار الميكروفون...');

    const testButton = document.querySelector('[onclick="runMicrophoneTest()"]');
    if (testButton) {
        testButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الاختبار...';
        testButton.disabled = true;
    }

    navigator.mediaDevices.getUserMedia({ audio: true })
        .then(stream => {
            console.log('✅ اختبار الميكروفون نجح');
            stream.getTracks().forEach(track => track.stop());

            if (testButton) {
                testButton.innerHTML = '<i class="fas fa-check me-1"></i>الميكروفون يعمل!';
                testButton.className = 'btn btn-success btn-sm';
            }

            showSuccessMessage('الميكروفون يعمل بشكل صحيح! جرب البحث الصوتي مرة أخرى.');

            setTimeout(() => {
                if (testButton) {
                    testButton.innerHTML = '<i class="fas fa-microphone-alt me-1"></i>اختبار الميكروفون';
                    testButton.className = 'btn btn-primary btn-sm';
                    testButton.disabled = false;
                }
            }, 3000);
        })
        .catch(error => {
            console.error('❌ فشل اختبار الميكروفون:', error);

            if (testButton) {
                testButton.innerHTML = '<i class="fas fa-times me-1"></i>فشل الاختبار';
                testButton.className = 'btn btn-danger btn-sm';
            }

            showErrorMessage(`فشل اختبار الميكروفون: ${error.message}`);

            setTimeout(() => {
                if (testButton) {
                    testButton.innerHTML = '<i class="fas fa-microphone-alt me-1"></i>اختبار الميكروفون';
                    testButton.className = 'btn btn-primary btn-sm';
                    testButton.disabled = false;
                }
            }, 3000);
        });
}

/**
 * إظهار إعدادات المتصفح
 */
function showBrowserSettings() {
    const userAgent = navigator.userAgent;
    let instructions = '';

    if (userAgent.includes('Chrome')) {
        instructions = `
            <h6>إعدادات Chrome:</h6>
            <ol>
                <li>اذهب إلى: chrome://settings/content/microphone</li>
                <li>تأكد من أن "السماح للمواقع بالوصول للميكروفون" مفعل</li>
                <li>أضف هذا الموقع للمواقع المسموحة</li>
            </ol>
        `;
    } else if (userAgent.includes('Edge')) {
        instructions = `
            <h6>إعدادات Edge:</h6>
            <ol>
                <li>اذهب إلى: edge://settings/content/microphone</li>
                <li>تأكد من أن "السماح للمواقع بالوصول للميكروفون" مفعل</li>
                <li>أضف هذا الموقع للمواقع المسموحة</li>
            </ol>
        `;
    } else {
        instructions = `
            <h6>إعدادات عامة:</h6>
            <ol>
                <li>اذهب إلى إعدادات المتصفح</li>
                <li>ابحث عن "الخصوصية والأمان"</li>
                <li>اختر "إعدادات الموقع"</li>
                <li>اختر "الميكروفون"</li>
                <li>اسمح لهذا الموقع بالوصول</li>
            </ol>
        `;
    }

    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-info alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 400px; max-width: 500px;';
    alertDiv.innerHTML = `
        <h6><i class="fas fa-cog me-2"></i>إعدادات المتصفح</h6>
        ${instructions}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 10000);
}

/**
 * عرض رسالة نجاح
 */
function showSuccessMessage(message) {
    // إنشاء رسالة نجاح
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة الرسالة تلقائياً بعد 3 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

/**
 * عرض إشعار عام
 */
function showNotification(message, type = 'info') {
    const typeClasses = {
        'success': 'alert-success',
        'warning': 'alert-warning',
        'error': 'alert-danger',
        'info': 'alert-info'
    };

    const typeIcons = {
        'success': 'fas fa-check-circle',
        'warning': 'fas fa-exclamation-triangle',
        'error': 'fas fa-times-circle',
        'info': 'fas fa-info-circle'
    };

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${typeClasses[type] || 'alert-info'} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="${typeIcons[type] || 'fas fa-info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة الرسالة تلقائياً بعد 4 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 4000);
}
