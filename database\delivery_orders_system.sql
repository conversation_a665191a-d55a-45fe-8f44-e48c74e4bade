-- نظام إدارة أوامر التسليم للمخلص
-- Delivery Orders Management System for Customs Agents

-- 1. إنشاء جدول المخلصين الجمركيين
CREATE TABLE customs_agents (
    id NUMBER PRIMARY KEY,
    agent_code VARCHAR2(20) UNIQUE NOT NULL,
    agent_name VARCHAR2(200) NOT NULL,
    company_name VARCHAR2(300),
    license_number VARCHAR2(50) UNIQUE NOT NULL,
    license_expiry_date DATE,
    phone VARCHAR2(20),
    mobile VARCHAR2(20),
    email VARCHAR2(100),
    address CLOB,
    specialization VARCHAR2(100), -- نوع التخصص (عام، سيارات، أدوية، إلخ)
    rating NUMBER(3,2) DEFAULT 0, -- تقييم من 0 إلى 5
    total_orders NUMBER DEFAULT 0,
    completed_orders NUMBER DEFAULT 0,
    average_completion_days NUMBER(5,2) DEFAULT 0,
    is_active NUMBER(1) DEFAULT 1,
    notes CLOB,
    created_at DATE DEFAULT SYSDATE,
    created_by NUMBER,
    updated_at DATE,
    updated_by NUMBER
);

-- 2. إنشاء جدول أوامر التسليم
CREATE TABLE delivery_orders (
    id NUMBER PRIMARY KEY,
    order_number VARCHAR2(50) UNIQUE NOT NULL,
    shipment_id NUMBER NOT NULL,
    customs_agent_id NUMBER,
    order_status VARCHAR2(50) DEFAULT 'draft', -- draft, sent, in_progress, completed, cancelled
    priority VARCHAR2(20) DEFAULT 'normal', -- urgent, high, normal, low
    
    -- تواريخ مهمة
    created_date DATE DEFAULT SYSDATE,
    sent_date DATE,
    expected_completion_date DATE,
    actual_completion_date DATE,
    
    -- تفاصيل الأمر
    delivery_location VARCHAR2(500),
    special_instructions CLOB,
    estimated_cost NUMBER(15,2),
    actual_cost NUMBER(15,2),
    currency VARCHAR2(10) DEFAULT 'SAR',
    
    -- معلومات الاتصال
    contact_person VARCHAR2(200),
    contact_phone VARCHAR2(20),
    contact_email VARCHAR2(100),
    
    -- حالة الوثائق
    documents_status VARCHAR2(50) DEFAULT 'pending', -- pending, complete, incomplete
    documents_notes CLOB,
    
    -- معلومات النظام
    created_by NUMBER,
    updated_at DATE,
    updated_by NUMBER,
    
    CONSTRAINT fk_delivery_shipment FOREIGN KEY (shipment_id) REFERENCES cargo_shipments(id),
    CONSTRAINT fk_delivery_agent FOREIGN KEY (customs_agent_id) REFERENCES customs_agents(id)
);

-- 3. إنشاء جدول الوثائق المطلوبة
CREATE TABLE delivery_order_documents (
    id NUMBER PRIMARY KEY,
    delivery_order_id NUMBER NOT NULL,
    document_type VARCHAR2(100) NOT NULL, -- bill_of_lading, commercial_invoice, packing_list, etc.
    document_name VARCHAR2(200),
    is_required NUMBER(1) DEFAULT 1,
    is_available NUMBER(1) DEFAULT 0,
    file_path VARCHAR2(500),
    upload_date DATE,
    uploaded_by NUMBER,
    notes VARCHAR2(1000),
    
    CONSTRAINT fk_doc_delivery_order FOREIGN KEY (delivery_order_id) REFERENCES delivery_orders(id) ON DELETE CASCADE
);

-- 4. إنشاء جدول تتبع حالة أوامر التسليم
CREATE TABLE delivery_order_status_history (
    id NUMBER PRIMARY KEY,
    delivery_order_id NUMBER NOT NULL,
    old_status VARCHAR2(50),
    new_status VARCHAR2(50) NOT NULL,
    status_date DATE DEFAULT SYSDATE,
    updated_by NUMBER,
    notes VARCHAR2(1000),
    location VARCHAR2(200),
    
    CONSTRAINT fk_status_delivery_order FOREIGN KEY (delivery_order_id) REFERENCES delivery_orders(id) ON DELETE CASCADE
);

-- 5. إنشاء جدول رسوم التخليص
CREATE TABLE delivery_order_fees (
    id NUMBER PRIMARY KEY,
    delivery_order_id NUMBER NOT NULL,
    fee_type VARCHAR2(100) NOT NULL, -- customs_duty, service_fee, storage_fee, etc.
    fee_description VARCHAR2(300),
    amount NUMBER(15,2) NOT NULL,
    currency VARCHAR2(10) DEFAULT 'SAR',
    is_paid NUMBER(1) DEFAULT 0,
    payment_date DATE,
    payment_reference VARCHAR2(100),
    notes VARCHAR2(500),
    
    CONSTRAINT fk_fees_delivery_order FOREIGN KEY (delivery_order_id) REFERENCES delivery_orders(id) ON DELETE CASCADE
);

-- 6. إنشاء الـ sequences
CREATE SEQUENCE customs_agents_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE delivery_orders_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE delivery_order_documents_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE delivery_order_status_history_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE delivery_order_fees_seq START WITH 1 INCREMENT BY 1;

-- 7. إنشاء triggers للـ IDs التلقائية
CREATE OR REPLACE TRIGGER trg_customs_agents_id
    BEFORE INSERT ON customs_agents
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := customs_agents_seq.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_delivery_orders_id
    BEFORE INSERT ON delivery_orders
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := delivery_orders_seq.NEXTVAL;
    END IF;
    
    -- إنشاء رقم أمر تلقائي
    IF :NEW.order_number IS NULL THEN
        :NEW.order_number := 'DO' || TO_CHAR(SYSDATE, 'YYYYMMDD') || LPAD(delivery_orders_seq.CURRVAL, 4, '0');
    END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_delivery_order_documents_id
    BEFORE INSERT ON delivery_order_documents
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := delivery_order_documents_seq.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_delivery_order_status_history_id
    BEFORE INSERT ON delivery_order_status_history
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := delivery_order_status_history_seq.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_delivery_order_fees_id
    BEFORE INSERT ON delivery_order_fees
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := delivery_order_fees_seq.NEXTVAL;
    END IF;
END;
/

-- 8. إنشاء trigger لتسجيل تغييرات الحالة تلقائياً
CREATE OR REPLACE TRIGGER trg_delivery_order_status_change
    AFTER UPDATE OF order_status ON delivery_orders
    FOR EACH ROW
BEGIN
    IF :OLD.order_status != :NEW.order_status THEN
        INSERT INTO delivery_order_status_history (
            delivery_order_id, old_status, new_status, 
            status_date, updated_by, notes
        ) VALUES (
            :NEW.id,
            :OLD.order_status,
            :NEW.order_status,
            SYSDATE,
            :NEW.updated_by,
            'تم تحديث الحالة تلقائياً'
        );
    END IF;
END;
/

-- 9. إنشاء الفهارس للأداء
CREATE INDEX idx_delivery_orders_shipment ON delivery_orders(shipment_id);
CREATE INDEX idx_delivery_orders_agent ON delivery_orders(customs_agent_id);
CREATE INDEX idx_delivery_orders_status ON delivery_orders(order_status);
CREATE INDEX idx_delivery_orders_date ON delivery_orders(created_date);
CREATE INDEX idx_customs_agents_license ON customs_agents(license_number);
CREATE INDEX idx_customs_agents_active ON customs_agents(is_active);

-- 10. إدراج بيانات تجريبية للمخلصين
INSERT INTO customs_agents (
    agent_code, agent_name, company_name, license_number, 
    phone, email, specialization, is_active
) VALUES 
('CA001', 'أحمد محمد الشهري', 'مؤسسة الشهري للتخليص الجمركي', '*********', '**********', '<EMAIL>', 'عام', 1);

INSERT INTO customs_agents (
    agent_code, agent_name, company_name, license_number, 
    phone, email, specialization, is_active
) VALUES 
('CA002', 'فاطمة علي القحطاني', 'شركة القحطاني للخدمات الجمركية', '*********', '**********', '<EMAIL>', 'سيارات', 1);

INSERT INTO customs_agents (
    agent_code, agent_name, company_name, license_number, 
    phone, email, specialization, is_active
) VALUES 
('CA003', 'خالد عبدالله المطيري', 'مكتب المطيري للتخليص', '*********', '**********', '<EMAIL>', 'أدوية', 1);

COMMIT;

-- 11. إنشاء view شامل لأوامر التسليم
CREATE OR REPLACE VIEW v_delivery_orders_full AS
SELECT 
    do.*,
    ca.agent_name,
    ca.company_name as agent_company,
    ca.phone as agent_phone,
    ca.email as agent_email,
    ca.specialization as agent_specialization,
    cs.tracking_number,
    cs.booking_number,
    cs.port_of_loading,
    cs.port_of_discharge,
    cs.shipment_status,
    (SELECT COUNT(*) FROM delivery_order_documents dod WHERE dod.delivery_order_id = do.id AND dod.is_required = 1) as required_docs_count,
    (SELECT COUNT(*) FROM delivery_order_documents dod WHERE dod.delivery_order_id = do.id AND dod.is_required = 1 AND dod.is_available = 1) as available_docs_count,
    (SELECT SUM(amount) FROM delivery_order_fees dof WHERE dof.delivery_order_id = do.id) as total_fees,
    (SELECT SUM(amount) FROM delivery_order_fees dof WHERE dof.delivery_order_id = do.id AND dof.is_paid = 1) as paid_fees
FROM delivery_orders do
LEFT JOIN customs_agents ca ON do.customs_agent_id = ca.id
LEFT JOIN cargo_shipments cs ON do.shipment_id = cs.id;

COMMIT;
