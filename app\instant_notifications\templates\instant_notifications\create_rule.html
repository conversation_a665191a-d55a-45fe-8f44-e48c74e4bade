{% extends "base.html" %}

{% block title %}إنشاء قاعدة مرنة جديدة{% endblock %}

{% block extra_css %}
<style>
.step-indicator {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
}

.step {
    flex: 1;
    text-align: center;
    padding: 10px;
    border-radius: 5px;
    margin: 0 5px;
    background: #f8f9fc;
    border: 2px solid #e3e6f0;
    transition: all 0.3s ease;
}

.step.active {
    background: #4e73df;
    color: white;
    border-color: #4e73df;
}

.step.completed {
    background: #1cc88a;
    color: white;
    border-color: #1cc88a;
}

.form-section {
    display: none;
    animation: fadeIn 0.5s ease-in;
}

.form-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.condition-builder {
    background: #f8f9fc;
    border: 2px dashed #e3e6f0;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}

.message-preview {
    background: #e7f3ff;
    border-left: 4px solid #007bff;
    padding: 15px;
    border-radius: 0 5px 5px 0;
    margin: 15px 0;
}

.contact-selector {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e3e6f0;
    border-radius: 5px;
    padding: 10px;
}

.contact-item {
    padding: 8px;
    border-radius: 5px;
    margin: 5px 0;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.contact-item:hover {
    background-color: #f8f9fc;
}

.contact-item.selected {
    background-color: #e7f3ff;
    border: 1px solid #007bff;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-plus text-primary"></i>
            إنشاء قاعدة مرنة جديدة
        </h1>
        <div>
            <a href="{{ url_for('instant_notifications.flexible_rules') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-right"></i> العودة للقواعد
            </a>
        </div>
    </div>

    <!-- مؤشر الخطوات -->
    <div class="step-indicator">
        <div class="step active" id="step1">
            <i class="fas fa-info-circle"></i><br>
            <small>معلومات أساسية</small>
        </div>
        <div class="step" id="step2">
            <i class="fas fa-cogs"></i><br>
            <small>تحديد الشروط</small>
        </div>
        <div class="step" id="step3">
            <i class="fas fa-envelope"></i><br>
            <small>تخصيص الرسالة</small>
        </div>
        <div class="step" id="step4">
            <i class="fas fa-users"></i><br>
            <small>جهات الاتصال</small>
        </div>
        <div class="step" id="step5">
            <i class="fas fa-check"></i><br>
            <small>مراجعة وحفظ</small>
        </div>
    </div>

    <!-- نموذج إنشاء القاعدة -->
    <div class="card shadow">
        <div class="card-body">
            <form id="createRuleForm">
                
                <!-- الخطوة 1: معلومات أساسية -->
                <div class="form-section active" id="section1">
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-info-circle"></i> معلومات أساسية
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ruleName" class="form-label">اسم القاعدة *</label>
                                <input type="text" class="form-control" id="ruleName" 
                                       placeholder="أدخل اسم وصفي للقاعدة" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="priorityLevel" class="form-label">مستوى الأولوية</label>
                                <select class="form-select" id="priorityLevel">
                                    <option value="1">1 - عالية جداً (طوارئ)</option>
                                    <option value="2">2 - عالية</option>
                                    <option value="3">3 - متوسطة عالية</option>
                                    <option value="4">4 - متوسطة</option>
                                    <option value="5" selected>5 - عادية</option>
                                    <option value="6">6 - منخفضة</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="eventType" class="form-label">نوع الحدث *</label>
                        <select class="form-select" id="eventType" required onchange="updateEventDescription()">
                            <option value="">اختر نوع الحدث</option>
                            {% for event_type in event_types %}
                            <option value="{{ event_type.id }}" 
                                    data-category="{{ event_type.event_category }}"
                                    data-description="{{ event_type.description_ar }}">
                                {{ event_type.event_name_ar }}
                            </option>
                            {% endfor %}
                        </select>
                        <div id="eventDescription" class="form-text"></div>
                    </div>
                </div>

                <!-- الخطوة 2: تحديد الشروط -->
                <div class="form-section" id="section2">
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-cogs"></i> تحديد الشروط
                    </h5>
                    
                    <div class="condition-builder">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="conditionField" class="form-label">الحقل</label>
                                <select class="form-select" id="conditionField">
                                    <option value="">اختر الحقل</option>
                                    <option value="shipment_status">حالة الشحنة</option>
                                    <option value="total_value">القيمة الإجمالية</option>
                                    <option value="estimated_arrival">تاريخ الوصول المتوقع</option>
                                    <option value="document_expiry_date">تاريخ انتهاء الوثائق</option>
                                    <option value="payment_due_date">تاريخ استحقاق الدفع</option>
                                    <option value="port_of_discharge">ميناء التفريغ</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="conditionOperator" class="form-label">المشغل</label>
                                <select class="form-select" id="conditionOperator">
                                    <option value="=">=</option>
                                    <option value="!=">≠</option>
                                    <option value=">">></option>
                                    <option value="<"><</option>
                                    <option value=">=">≥</option>
                                    <option value="<=">≤</option>
                                    <option value="LIKE">يحتوي على</option>
                                </select>
                            </div>
                            <div class="col-md-5">
                                <label for="conditionValue" class="form-label">القيمة</label>
                                <input type="text" class="form-control" id="conditionValue" 
                                       placeholder="أدخل القيمة للمقارنة">
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                مثال: إذا كانت "حالة الشحنة" = "customs_clearance" فسيتم تشغيل هذه القاعدة
                            </small>
                        </div>
                    </div>
                </div>

                <!-- الخطوة 3: تخصيص الرسالة -->
                <div class="form-section" id="section3">
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-envelope"></i> تخصيص الرسالة
                    </h5>
                    
                    <div class="mb-3">
                        <label for="messageTemplate" class="form-label">قالب الرسالة *</label>
                        <textarea class="form-control" id="messageTemplate" rows="4" 
                                  placeholder="أدخل نص الرسالة..." required
                                  oninput="updateMessagePreview()"></textarea>
                        <div class="form-text">
                            يمكنك استخدام المتغيرات: {tracking_number}, {shipment_status}, {total_value}, {port_of_discharge}
                        </div>
                    </div>

                    <div class="message-preview" id="messagePreview">
                        <strong>معاينة الرسالة:</strong><br>
                        <span id="previewText">أدخل نص الرسالة لرؤية المعاينة</span>
                    </div>
                </div>

                <!-- الخطوة 4: جهات الاتصال -->
                <div class="form-section" id="section4">
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-users"></i> اختيار جهات الاتصال
                    </h5>
                    
                    <div class="mb-3">
                        <label class="form-label">جهات الاتصال المتاحة:</label>
                        <div class="contact-selector" id="contactSelector">
                            {% for contact in contacts %}
                            <div class="contact-item" data-contact-id="{{ contact.id }}" 
                                 onclick="toggleContact(this)">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>{{ contact.contact_name }}</strong><br>
                                        <small class="text-muted">{{ contact.phone_number }}</small>
                                    </div>
                                    <div>
                                        <span class="badge {{ 'bg-danger' if contact.contact_type == 'admin' else 'bg-info' }}">
                                            {{ 'إدارة' if contact.contact_type == 'admin' else 'عام' }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <small class="text-muted">اضغط على جهة الاتصال لتحديدها</small>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>تلميح:</strong> يمكنك اختيار عدة جهات اتصال. ستصل الرسالة لجميع الجهات المحددة عند تشغيل القاعدة.
                    </div>
                </div>

                <!-- الخطوة 5: مراجعة وحفظ -->
                <div class="form-section" id="section5">
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-check"></i> مراجعة وحفظ
                    </h5>
                    
                    <div class="card">
                        <div class="card-header">
                            <h6 class="m-0">ملخص القاعدة</h6>
                        </div>
                        <div class="card-body" id="ruleSummary">
                            <!-- سيتم ملء هذا القسم بـ JavaScript -->
                        </div>
                    </div>

                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تنبيه:</strong> تأكد من صحة جميع البيانات قبل الحفظ. ستبدأ القاعدة في العمل فور الحفظ.
                    </div>
                </div>

            </form>
        </div>

        <!-- أزرار التنقل -->
        <div class="card-footer">
            <div class="d-flex justify-content-between">
                <button type="button" class="btn btn-secondary" id="prevBtn" onclick="previousStep()" style="display: none;">
                    <i class="fas fa-arrow-right"></i> السابق
                </button>
                <div></div>
                <div>
                    <button type="button" class="btn btn-primary" id="nextBtn" onclick="nextStep()">
                        التالي <i class="fas fa-arrow-left"></i>
                    </button>
                    <button type="button" class="btn btn-success" id="saveBtn" onclick="saveRule()" style="display: none;">
                        <i class="fas fa-save"></i> حفظ القاعدة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentStep = 1;
const totalSteps = 5;
let selectedContacts = [];

function nextStep() {
    if (validateCurrentStep()) {
        if (currentStep < totalSteps) {
            currentStep++;
            updateStepDisplay();
            if (currentStep === 5) {
                generateRuleSummary();
            }
        }
    }
}

function previousStep() {
    if (currentStep > 1) {
        currentStep--;
        updateStepDisplay();
    }
}

function updateStepDisplay() {
    // تحديث مؤشر الخطوات
    for (let i = 1; i <= totalSteps; i++) {
        const step = document.getElementById(`step${i}`);
        const section = document.getElementById(`section${i}`);
        
        if (i < currentStep) {
            step.className = 'step completed';
        } else if (i === currentStep) {
            step.className = 'step active';
        } else {
            step.className = 'step';
        }
        
        section.classList.toggle('active', i === currentStep);
    }
    
    // تحديث الأزرار
    document.getElementById('prevBtn').style.display = currentStep > 1 ? 'block' : 'none';
    document.getElementById('nextBtn').style.display = currentStep < totalSteps ? 'block' : 'none';
    document.getElementById('saveBtn').style.display = currentStep === totalSteps ? 'block' : 'none';
}

function validateCurrentStep() {
    switch (currentStep) {
        case 1:
            const ruleName = document.getElementById('ruleName').value.trim();
            const eventType = document.getElementById('eventType').value;
            
            if (!ruleName) {
                alert('يرجى إدخال اسم القاعدة');
                return false;
            }
            if (!eventType) {
                alert('يرجى اختيار نوع الحدث');
                return false;
            }
            return true;
            
        case 2:
            // التحقق من الشروط اختياري
            return true;
            
        case 3:
            const messageTemplate = document.getElementById('messageTemplate').value.trim();
            if (!messageTemplate) {
                alert('يرجى إدخال قالب الرسالة');
                return false;
            }
            return true;
            
        case 4:
            if (selectedContacts.length === 0) {
                alert('يرجى اختيار جهة اتصال واحدة على الأقل');
                return false;
            }
            return true;
            
        default:
            return true;
    }
}

function updateEventDescription() {
    const select = document.getElementById('eventType');
    const description = select.options[select.selectedIndex]?.dataset.description || '';
    document.getElementById('eventDescription').textContent = description;
}

function updateMessagePreview() {
    const template = document.getElementById('messageTemplate').value;
    let preview = template
        .replace(/{tracking_number}/g, '<strong>COSU12345678</strong>')
        .replace(/{shipment_status}/g, '<span class="badge bg-success">قيد التخليص الجمركي</span>')
        .replace(/{total_value}/g, '<strong>150,000</strong>')
        .replace(/{port_of_discharge}/g, '<strong>ميناء جدة</strong>');
    
    document.getElementById('previewText').innerHTML = preview || 'أدخل نص الرسالة لرؤية المعاينة';
}

function toggleContact(element) {
    const contactId = parseInt(element.dataset.contactId);
    
    if (element.classList.contains('selected')) {
        element.classList.remove('selected');
        selectedContacts = selectedContacts.filter(id => id !== contactId);
    } else {
        element.classList.add('selected');
        selectedContacts.push(contactId);
    }
}

function generateRuleSummary() {
    const ruleName = document.getElementById('ruleName').value;
    const eventType = document.getElementById('eventType');
    const eventTypeName = eventType.options[eventType.selectedIndex]?.text || '';
    const priorityLevel = document.getElementById('priorityLevel').value;
    const conditionField = document.getElementById('conditionField').value;
    const conditionOperator = document.getElementById('conditionOperator').value;
    const conditionValue = document.getElementById('conditionValue').value;
    const messageTemplate = document.getElementById('messageTemplate').value;
    
    const summary = `
        <div class="row">
            <div class="col-md-6">
                <h6>معلومات القاعدة:</h6>
                <ul class="list-unstyled">
                    <li><strong>الاسم:</strong> ${ruleName}</li>
                    <li><strong>نوع الحدث:</strong> ${eventTypeName}</li>
                    <li><strong>الأولوية:</strong> ${priorityLevel}</li>
                </ul>
                
                <h6>الشرط:</h6>
                <div class="alert alert-light">
                    ${conditionField ? `${conditionField} ${conditionOperator} ${conditionValue}` : 'شرط عام'}
                </div>
            </div>
            <div class="col-md-6">
                <h6>الرسالة:</h6>
                <div class="alert alert-info">
                    ${messageTemplate.substring(0, 100)}${messageTemplate.length > 100 ? '...' : ''}
                </div>
                
                <h6>جهات الاتصال:</h6>
                <div class="alert alert-light">
                    ${selectedContacts.length} جهة اتصال محددة
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('ruleSummary').innerHTML = summary;
}

function saveRule() {
    const ruleData = {
        rule_name: document.getElementById('ruleName').value,
        event_type_id: document.getElementById('eventType').value,
        condition_type: 'field_value',
        condition_field: document.getElementById('conditionField').value,
        condition_operator: document.getElementById('conditionOperator').value,
        condition_value: document.getElementById('conditionValue').value,
        message_template: document.getElementById('messageTemplate').value,
        priority_level: document.getElementById('priorityLevel').value,
        selected_contacts: selectedContacts
    };
    
    fetch('/instant-notifications/api/create_flexible_rule', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(ruleData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إنشاء القاعدة بنجاح!');
            window.location.href = '/instant-notifications/flexible-rules';
        } else {
            alert('فشل في إنشاء القاعدة: ' + data.message);
        }
    })
    .catch(error => {
        alert('خطأ في الاتصال: ' + error);
    });
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateStepDisplay();
});
</script>
{% endblock %}
