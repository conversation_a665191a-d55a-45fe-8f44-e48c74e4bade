{% extends "base.html" %}

{% block title %}الجدولة الزمنية للشحنة{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-history me-2 text-info"></i>
                        الجدولة الزمنية للشحنة
                    </h2>
                    {% if shipment %}
                    <p class="text-muted mb-0">رقم التتبع: {{ shipment[1] or 'غير محدد' }}</p>
                    {% endif %}
                </div>
                <div>
                    <a href="{{ url_for('shipments.tracking_dashboard') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة للوحة التتبع
                    </a>
                    {% if shipment %}
                    <button class="btn btn-primary" onclick="updateStatus({{ shipment[0] }})">
                        <i class="fas fa-edit me-1"></i>
                        تحديث الحالة
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    {% if shipment %}
    <!-- معلومات الشحنة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الشحنة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>رقم التتبع:</strong><br>
                            <span class="text-muted">{{ shipment[1] or 'غير محدد' }}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>المرسل:</strong><br>
                            <span class="text-muted">{{ shipment[2] or 'غير محدد' }}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>المستلم:</strong><br>
                            <span class="text-muted">{{ shipment[3] or 'غير محدد' }}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>الحالة الحالية:</strong><br>
                            <span class="badge" style="background-color: {{ shipment[42] or '#6c757d' }}; color: white;">
                                <i class="{{ shipment[44] or 'fas fa-circle' }} me-1"></i>
                                {{ shipment[41] or 'غير محدد' }}
                            </span>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-md-3">
                            <strong>ميناء الشحن:</strong><br>
                            <span class="text-muted">{{ shipment[6] or 'غير محدد' }}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>ميناء الوصول:</strong><br>
                            <span class="text-muted">{{ shipment[7] or 'غير محدد' }}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>تاريخ التسليم المتوقع:</strong><br>
                            <span class="text-muted">
                                {{ shipment[39].strftime('%Y-%m-%d') if shipment[39] else 'غير محدد' }}
                            </span>
                        </div>
                        <div class="col-md-3">
                            <strong>تاريخ التسليم الفعلي:</strong><br>
                            <span class="text-muted">
                                {{ shipment[40].strftime('%Y-%m-%d') if shipment[40] else 'لم يتم التسليم بعد' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الجدولة الزمنية -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-timeline me-2"></i>
                        تاريخ تطور الحالات
                    </h5>
                </div>
                <div class="card-body">
                    {% if history %}
                    <div class="timeline">
                        {% for event in history %}
                        <div class="timeline-item">
                            <div class="timeline-marker" style="background-color: {{ event[8] or '#6c757d' }};">
                                <i class="{{ event[9] or 'fas fa-circle' }} text-white"></i>
                            </div>
                            <div class="timeline-content">
                                <div class="timeline-header">
                                    <h6 class="mb-1" style="color: {{ event[8] or '#6c757d' }};">
                                        {{ event[7] or 'تحديث الحالة' }}
                                    </h6>
                                    <small class="text-muted">
                                        {{ event[2].strftime('%Y-%m-%d %H:%M:%S') if event[2] else 'غير محدد' }}
                                    </small>
                                </div>
                                {% if event[0] %}
                                <div class="timeline-body">
                                    <p class="mb-1">
                                        <strong>من:</strong> 
                                        <span class="badge" style="background-color: {{ event[6] or '#6c757d' }}; color: white;">
                                            {{ event[5] or 'غير محدد' }}
                                        </span>
                                        <strong>إلى:</strong>
                                        <span class="badge" style="background-color: {{ event[8] or '#6c757d' }}; color: white;">
                                            {{ event[7] or 'غير محدد' }}
                                        </span>
                                    </p>
                                </div>
                                {% endif %}
                                {% if event[3] %}
                                <div class="timeline-notes">
                                    <p class="mb-1"><strong>ملاحظات:</strong> {{ event[3] }}</p>
                                </div>
                                {% endif %}
                                {% if event[4] %}
                                <div class="timeline-location">
                                    <p class="mb-0">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <strong>الموقع:</strong> {{ event[4] }}
                                    </p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد تاريخ للحالات</h5>
                        <p class="text-muted">لم يتم تسجيل أي تغييرات في حالة هذه الشحنة بعد</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- الشحنة غير موجودة -->
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h4 class="text-muted">الشحنة غير موجودة</h4>
                <p class="text-muted">لم يتم العثور على الشحنة المطلوبة</p>
                <a href="{{ url_for('shipments.tracking_dashboard') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للوحة التتبع
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Modal تحديث الحالة -->
<div class="modal fade" id="updateStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث حالة الشحنة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="updateStatusForm">
                    {% if shipment %}
                    <input type="hidden" id="shipmentId" name="shipment_id" value="{{ shipment[0] }}">
                    {% endif %}
                    
                    <div class="mb-3">
                        <label class="form-label">الحالة الجديدة</label>
                        <select class="form-select" name="new_status" required>
                            <option value="">اختر الحالة</option>
                            <option value="draft">📝 مسودة</option>
                            <option value="confirmed">✅ مؤكدة</option>
                            <option value="in_transit">🚢 قيد الشحن</option>
                            <option value="arrived_port">🏭 وصلت للميناء</option>
                            <option value="customs_clearance">🚚 قيد التخليص</option>
                            <option value="ready_pickup">📦 جاهزة للاستلام</option>
                            <option value="delivered">✅ تم التسليم</option>
                            <option value="cancelled">❌ ملغية</option>
                            <option value="delayed">⚠️ متأخرة</option>
                            <option value="returned">🔄 معادة</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الموقع (اختياري)</label>
                        <input type="text" class="form-control" name="location" placeholder="الموقع الحالي">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" name="notes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="submitStatusUpdate()">
                    <i class="fas fa-save me-1"></i>
                    تحديث الحالة
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.timeline-content {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.timeline-header {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 10px;
}

.timeline-body, .timeline-notes, .timeline-location {
    margin-bottom: 10px;
}

.timeline-body:last-child, .timeline-notes:last-child, .timeline-location:last-child {
    margin-bottom: 0;
}
</style>

<script>
// تحديث حالة الشحنة
function updateStatus(shipmentId) {
    new bootstrap.Modal(document.getElementById('updateStatusModal')).show();
}

// إرسال تحديث الحالة
function submitStatusUpdate() {
    const form = document.getElementById('updateStatusForm');
    const formData = new FormData(form);
    
    fetch('/shipments/api/update-status', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم تحديث حالة الشحنة بنجاح');
            location.reload();
        } else {
            alert('خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في تحديث الحالة');
    });
}
</script>
{% endblock %}
