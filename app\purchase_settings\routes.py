#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Purchase Settings Routes
مسارات إعدادات المشتريات
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from database_manager import DatabaseManager
from oracle_manager import get_oracle_manager
import logging

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

# إنشاء Blueprint
purchase_settings = Blueprint('purchase_settings', __name__, url_prefix='/purchase_settings')

@purchase_settings.route('/supplier_variables')
def supplier_variables():
    """نافذة متغيرات نظام الموردين"""
    try:
        db_manager = DatabaseManager()
        
        # جلب البيانات الحالية
        sql = "SELECT * FROM supplier_variables WHERE is_active = 1"
        result = db_manager.execute_query(sql)
        
        variables = {}
        if result and len(result) > 0:
            # تحويل النتيجة إلى dictionary
            columns_sql = """
            SELECT column_name FROM user_tab_columns 
            WHERE table_name = 'SUPPLIER_VARIABLES'
            ORDER BY column_id
            """
            columns_result = db_manager.execute_query(columns_sql)
            if columns_result:
                column_names = [str(row[0]).lower() for row in columns_result]
                row_data = result[0]

                # تحويل البيانات إلى أنواع Python
                variables = {}
                for i, col_name in enumerate(column_names):
                    value = row_data[i]
                    if value is not None:
                        # تحويل Java String إلى Python string
                        if hasattr(value, 'toString'):
                            variables[col_name] = str(value)
                        else:
                            variables[col_name] = value
                    else:
                        variables[col_name] = None
        
        return render_template('purchase_settings/supplier_variables.html', variables=variables)
        
    except Exception as e:
        logger.error(f"خطأ في عرض متغيرات الموردين: {e}")
        flash('حدث خطأ في تحميل البيانات', 'error')
        return render_template('purchase_settings/supplier_variables.html', variables={})

@purchase_settings.route('/api/supplier_variables', methods=['GET'])
def get_supplier_variables():
    """API لجلب متغيرات الموردين"""
    try:
        db_manager = DatabaseManager()
        
        sql = "SELECT * FROM supplier_variables WHERE is_active = 1"
        result = db_manager.execute_query(sql)
        
        if result and len(result) > 0:
            # جلب أسماء الأعمدة
            columns_sql = """
            SELECT column_name FROM user_tab_columns 
            WHERE table_name = 'SUPPLIER_VARIABLES'
            ORDER BY column_id
            """
            columns_result = db_manager.execute_query(columns_sql)
            if columns_result:
                column_names = [str(row[0]).lower() for row in columns_result]
                row_data = result[0]

                # تحويل البيانات إلى أنواع Python قابلة للتحويل إلى JSON
                variables = {}
                for i, col_name in enumerate(column_names):
                    value = row_data[i]
                    if value is not None:
                        # تحويل جميع القيم إلى أنواع Python أساسية
                        try:
                            if isinstance(value, (int, float, bool)):
                                variables[col_name] = value
                            else:
                                variables[col_name] = str(value)
                        except:
                            variables[col_name] = str(value)
                    else:
                        variables[col_name] = None

                return jsonify({
                    'success': True,
                    'variables': variables
                })
        
        return jsonify({
            'success': False,
            'message': 'لا توجد بيانات'
        })
        
    except Exception as e:
        logger.error(f"خطأ في جلب متغيرات الموردين: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب البيانات: {str(e)}'
        })

@purchase_settings.route('/api/supplier_variables', methods=['POST'])
def save_supplier_variables():
    """API لحفظ متغيرات الموردين"""
    try:
        db_manager = DatabaseManager()
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'لا توجد بيانات للحفظ'
            })
        
        # تحديث البيانات
        update_sql = """
        UPDATE supplier_variables SET
            supp_id_len = ?,
            decimal_places = ?,
            supp_id_type = ?,
            supp_gl_link = ?,
            doc_sequence = ?,
            supp_seq_type = ?,
            date_display = ?,
            pr_sequence = ?,
            po_sequence = ?,
            pi_sequence = ?,
            return_sequence = ?,
            delivery_seq = ?,
            contract_link = ?,
            use_return_req = ?,
            supp_inv_mand = ?,
            add_fields_cnt = ?,
            cost_var_method = ?,
            cc_type = ?,
            item_dup = ?,
            vat_calc_method = ?,
            use_free_qty = ?,
            show_free_pct = ?,
            return_free_pct = ?,
            free_qty_effect = ?,
            disc_type = ?,
            disc_count = ?,
            disc_code_type = ?,
            use_item_disc = ?,
            disc_no_effect = ?,
            use_period_disc = ?,
            inv_disc_calc = ?,
            use_inspection = ?,
            use_insp_appr = ?,
            show_est_cost = ?,
            mand_item_supp = ?,
            link_multi_supp = ?,
            check_item_supp = ?,
            sale_price_pur = ?,
            sale_price_po = ?,
            prev_year_inv = ?,
            allow_supp_edit = ?,
            link_po_pr = ?,
            link_inv_po = ?,
            alert_exist_pr = ?,
            alert_exist_po = ?,
            allow_serial_rep = ?,
            use_pack_data = ?,
            check_branch_wh = ?,
            inc_ref_comp = ?,
            allow_del_docs = ?,
            entry_cert = ?,
            allow_part_del = ?,
            exp_dist_method = ?,
            auto_upd_prices = ?,
            show_supp_item = ?,
            upd_supp_pr_po = ?,
            use_sale_as_pur = ?,
            sale_pr_level = ?,
            use_supp_lists = ?,
            updated_at = SYSDATE,
            updated_by = ?
        WHERE is_active = 1
        """
        
        # تحضير المعاملات
        params = [
            data.get('supp_id_len', 9),
            data.get('decimal_places', 2),
            data.get('supp_id_type', 'numeric'),
            data.get('supp_gl_link', 'multiple'),
            data.get('doc_sequence', 'auto_editable'),
            data.get('supp_seq_type', 'general'),
            data.get('date_display', 'auto_editable'),
            data.get('pr_sequence', 'cumulative'),
            data.get('po_sequence', 'cumulative'),
            data.get('pi_sequence', 'cumulative'),
            data.get('return_sequence', 'cumulative'),
            data.get('delivery_seq', 'cumulative'),
            data.get('contract_link', 'not_used'),
            data.get('use_return_req', 'not_used'),
            data.get('supp_inv_mand', 'not_used'),
            data.get('add_fields_cnt', 0),
            data.get('cost_var_method', 'by_branch'),
            data.get('cc_type', 'not_used'),
            data.get('item_dup', 'no'),
            data.get('vat_calc_method', 'price'),
            data.get('use_free_qty', 0),
            data.get('show_free_pct', 0),
            data.get('return_free_pct', 0),
            data.get('free_qty_effect', 'not_used'),
            data.get('disc_type', 'percentage'),
            data.get('disc_count', 0),
            data.get('disc_code_type', 'not_used'),
            data.get('use_item_disc', 0),
            data.get('disc_no_effect', 0),
            data.get('use_period_disc', 0),
            data.get('inv_disc_calc', 0),
            data.get('use_inspection', 0),
            data.get('use_insp_appr', 0),
            data.get('show_est_cost', 0),
            data.get('mand_item_supp', 0),
            data.get('link_multi_supp', 0),
            data.get('check_item_supp', 0),
            data.get('sale_price_pur', 0),
            data.get('sale_price_po', 0),
            data.get('prev_year_inv', 0),
            data.get('allow_supp_edit', 0),
            data.get('link_po_pr', 0),
            data.get('link_inv_po', 0),
            data.get('alert_exist_pr', 0),
            data.get('alert_exist_po', 0),
            data.get('allow_serial_rep', 0),
            data.get('use_pack_data', 0),
            data.get('check_branch_wh', 0),
            data.get('inc_ref_comp', 0),
            data.get('allow_del_docs', 0),
            data.get('entry_cert', 0),
            data.get('allow_part_del', 0),
            data.get('exp_dist_method', 'qty_only'),
            data.get('auto_upd_prices', 0),
            data.get('show_supp_item', 0),
            data.get('upd_supp_pr_po', 0),
            data.get('use_sale_as_pur', 0),
            data.get('sale_pr_level', 1),
            data.get('use_supp_lists', 0),
            'system'  # updated_by
        ]
        
        result = db_manager.execute_update(update_sql, params)
        
        if result:
            return jsonify({
                'success': True,
                'message': 'تم حفظ متغيرات الموردين بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في حفظ البيانات'
            })
        
    except Exception as e:
        logger.error(f"خطأ في حفظ متغيرات الموردين: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في حفظ البيانات: {str(e)}'
        })


# ==================== مجموعات الموردين ====================

@purchase_settings.route('/test_jquery')
def test_jquery():
    """صفحة اختبار jQuery"""
    return render_template('test_jquery.html')

@purchase_settings.route('/supplier_groups_simple')
@login_required
def supplier_groups_simple():
    """نافذة مجموعات الموردين المبسطة"""
    return render_template('purchase_settings/supplier_groups_simple.html')

@purchase_settings.route('/supplier_groups')
@login_required
def supplier_groups():
    """نافذة مجموعات الموردين"""
    try:
        return render_template('purchase_settings/supplier_groups.html')
    except Exception as e:
        logger.error(f"خطأ في عرض مجموعات الموردين: {e}")
        flash(f'خطأ في عرض الصفحة: {str(e)}', 'error')
        return redirect(url_for('main.dashboard'))


@purchase_settings.route('/api/supplier_groups', methods=['GET'])
@login_required
def get_supplier_groups():
    """الحصول على قائمة مجموعات الموردين"""
    try:
        logger.info("🔍 بدء جلب مجموعات الموردين...")
        oracle_mgr = get_oracle_manager()

        # استعلام للحصول على جميع مجموعات الموردين
        query = """
        SELECT group_id, group_name_ar, group_name_en, account_number, account_name,
               is_active, created_at, updated_at, notes
        FROM supplier_groups
        ORDER BY group_id
        """

        logger.info("📊 تنفيذ استعلام مجموعات الموردين...")
        groups_data = oracle_mgr.execute_query(query)
        logger.info(f"📋 تم العثور على {len(groups_data) if groups_data else 0} مجموعة")

        # تحويل البيانات إلى قائمة
        groups = []
        for group_data in groups_data:
            try:
                group_dict = {
                    'group_id': int(group_data[0]) if group_data[0] else 0,
                    'group_name_ar': str(group_data[1]) if group_data[1] else '',
                    'group_name_en': str(group_data[2]) if group_data[2] else '',
                    'account_number': str(group_data[3]) if group_data[3] else '',
                    'account_name': str(group_data[4]) if group_data[4] else '',
                    'is_active': bool(int(group_data[5])) if group_data[5] is not None else True,
                    'created_at': str(group_data[6]) if group_data[6] else '',
                    'updated_at': str(group_data[7]) if group_data[7] else '',
                    'notes': str(group_data[8]) if group_data[8] else ''
                }
                groups.append(group_dict)
            except Exception as e:
                logger.error(f"خطأ في تحويل بيانات المجموعة: {e}")
                continue

        logger.info(f"✅ تم تحويل {len(groups)} مجموعة بنجاح")

        response = {
            'success': True,
            'groups': groups,
            'total': len(groups)
        }

        logger.info("📤 إرسال الاستجابة...")
        return jsonify(response)

    except Exception as e:
        logger.error(f"❌ خطأ في جلب مجموعات الموردين: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })


# route اختبار بدون مصادقة
@purchase_settings.route('/api/test_supplier_groups', methods=['GET'])
def test_supplier_groups():
    """اختبار API مجموعات الموردين بدون مصادقة"""
    try:
        logger.info("🧪 اختبار API مجموعات الموردين...")
        oracle_mgr = get_oracle_manager()

        query = """
        SELECT group_id, group_name_ar, group_name_en, account_number, account_name,
               is_active, created_at, updated_at, notes
        FROM supplier_groups
        ORDER BY group_id
        """

        groups_data = oracle_mgr.execute_query(query)

        groups = []
        for group_data in groups_data:
            group_dict = {
                'group_id': int(group_data[0]) if group_data[0] else 0,
                'group_name_ar': str(group_data[1]) if group_data[1] else '',
                'group_name_en': str(group_data[2]) if group_data[2] else '',
                'account_number': str(group_data[3]) if group_data[3] else '',
                'account_name': str(group_data[4]) if group_data[4] else '',
                'is_active': bool(int(group_data[5])) if group_data[5] is not None else True,
                'created_at': str(group_data[6]) if group_data[6] else '',
                'updated_at': str(group_data[7]) if group_data[7] else '',
                'notes': str(group_data[8]) if group_data[8] else ''
            }
            groups.append(group_dict)

        return jsonify({
            'success': True,
            'groups': groups,
            'total': len(groups),
            'test': True
        })

    except Exception as e:
        logger.error(f"❌ خطأ في اختبار API: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}',
            'test': True
        })


@purchase_settings.route('/api/supplier_groups/<int:group_id>', methods=['GET'])
@login_required
def get_supplier_group(group_id):
    """الحصول على مجموعة موردين محددة"""
    try:
        oracle_mgr = get_oracle_manager()

        query = """
        SELECT group_id, group_name_ar, group_name_en, account_number, account_name,
               is_active, created_at, updated_at, notes
        FROM supplier_groups
        WHERE group_id = :1
        """

        group_data = oracle_mgr.execute_query(query, [group_id])

        if not group_data:
            return jsonify({
                'success': False,
                'message': 'المجموعة غير موجودة'
            }), 404

        group_info = group_data[0]
        group_dict = {
            'group_id': group_info[0],
            'group_name_ar': group_info[1],
            'group_name_en': group_info[2],
            'account_number': group_info[3],
            'account_name': group_info[4],
            'is_active': bool(int(group_info[5])) if group_info[5] is not None else True,
            'created_at': str(group_info[6]),
            'updated_at': str(group_info[7]),
            'notes': group_info[8]
        }

        return jsonify({
            'success': True,
            'group': group_dict
        })

    except Exception as e:
        logger.error(f"خطأ في جلب المجموعة: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })


@purchase_settings.route('/api/supplier_groups', methods=['POST'])
@login_required
def create_supplier_group():
    """إنشاء مجموعة موردين جديدة"""
    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        if not data.get('group_name_ar'):
            return jsonify({
                'success': False,
                'message': 'اسم المجموعة باللغة العربية مطلوب'
            })

        oracle_mgr = get_oracle_manager()

        # التحقق من عدم تكرار الاسم
        check_sql = "SELECT COUNT(*) FROM supplier_groups WHERE group_name_ar = :1"
        check_result = oracle_mgr.execute_query(check_sql, [data.get('group_name_ar')])

        if check_result and check_result[0][0] > 0:
            return jsonify({
                'success': False,
                'message': 'اسم المجموعة موجود بالفعل'
            })

        # إدراج مجموعة جديدة
        insert_sql = """
        INSERT INTO supplier_groups (group_name_ar, group_name_en, account_number,
                                   account_name, notes, created_by)
        VALUES (:1, :2, :3, :4, :5, :6)
        """

        params = [
            data.get('group_name_ar', ''),
            data.get('group_name_en', ''),
            data.get('account_number', ''),
            data.get('account_name', ''),
            data.get('notes', ''),
            current_user.id
        ]

        rows_affected = oracle_mgr.execute_update(insert_sql, params)

        if rows_affected > 0:
            # الحصول على المجموعة المُدرجة حديثاً
            get_new_group_sql = """
            SELECT group_id, group_name_ar, group_name_en, account_number, account_name,
                   is_active, created_at, updated_at, notes
            FROM supplier_groups
            WHERE group_id = (SELECT MAX(group_id) FROM supplier_groups)
            """

            new_group_data = oracle_mgr.execute_query(get_new_group_sql)

            if new_group_data:
                group_info = new_group_data[0]
                group_dict = {
                    'group_id': group_info[0],
                    'group_name_ar': group_info[1],
                    'group_name_en': group_info[2],
                    'account_number': group_info[3],
                    'account_name': group_info[4],
                    'is_active': bool(int(group_info[5])) if group_info[5] is not None else True,
                    'created_at': str(group_info[6]),
                    'updated_at': str(group_info[7]),
                    'notes': group_info[8]
                }

                return jsonify({
                    'success': True,
                    'message': 'تم إنشاء المجموعة بنجاح',
                    'group': group_dict
                })

        return jsonify({
            'success': False,
            'message': 'فشل في إنشاء المجموعة'
        })

    except Exception as e:
        logger.error(f"خطأ في إنشاء مجموعة الموردين: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })


@purchase_settings.route('/api/supplier_groups/<int:group_id>', methods=['PUT'])
@login_required
def update_supplier_group(group_id):
    """تحديث مجموعة موردين موجودة"""
    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        if not data.get('group_name_ar'):
            return jsonify({
                'success': False,
                'message': 'اسم المجموعة باللغة العربية مطلوب'
            })

        oracle_mgr = get_oracle_manager()

        # التحقق من عدم تكرار الاسم (باستثناء المجموعة الحالية)
        check_sql = "SELECT COUNT(*) FROM supplier_groups WHERE group_name_ar = :1 AND group_id != :2"
        check_result = oracle_mgr.execute_query(check_sql, [data.get('group_name_ar'), group_id])

        if check_result and check_result[0][0] > 0:
            return jsonify({
                'success': False,
                'message': 'اسم المجموعة موجود بالفعل'
            })

        # تحديث المجموعة
        update_sql = """
        UPDATE supplier_groups SET
            group_name_ar = :1, group_name_en = :2, account_number = :3,
            account_name = :4, notes = :5, updated_by = :6, updated_at = SYSDATE
        WHERE group_id = :7
        """

        params = [
            data.get('group_name_ar', ''),
            data.get('group_name_en', ''),
            data.get('account_number', ''),
            data.get('account_name', ''),
            data.get('notes', ''),
            current_user.id,
            group_id
        ]

        rows_affected = oracle_mgr.execute_update(update_sql, params)

        if rows_affected > 0:
            # الحصول على المجموعة المُحدثة
            get_updated_group_sql = """
            SELECT group_id, group_name_ar, group_name_en, account_number, account_name,
                   is_active, created_at, updated_at, notes
            FROM supplier_groups
            WHERE group_id = :1
            """

            updated_group_data = oracle_mgr.execute_query(get_updated_group_sql, [group_id])

            if updated_group_data:
                group_info = updated_group_data[0]
                group_dict = {
                    'group_id': group_info[0],
                    'group_name_ar': group_info[1],
                    'group_name_en': group_info[2],
                    'account_number': group_info[3],
                    'account_name': group_info[4],
                    'is_active': bool(int(group_info[5])) if group_info[5] is not None else True,
                    'created_at': str(group_info[6]),
                    'updated_at': str(group_info[7]),
                    'notes': group_info[8]
                }

                return jsonify({
                    'success': True,
                    'message': 'تم تحديث المجموعة بنجاح',
                    'group': group_dict
                })

        return jsonify({
            'success': False,
            'message': 'فشل في تحديث المجموعة'
        })

    except Exception as e:
        logger.error(f"خطأ في تحديث مجموعة الموردين: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })


@purchase_settings.route('/api/supplier_groups/<int:group_id>', methods=['DELETE'])
@login_required
def delete_supplier_group(group_id):
    """حذف مجموعة موردين"""
    try:
        oracle_mgr = get_oracle_manager()

        # الحصول على اسم المجموعة أولاً
        get_group_sql = "SELECT group_name_ar FROM supplier_groups WHERE group_id = :1"
        group_data = oracle_mgr.execute_query(get_group_sql, [group_id])

        if not group_data:
            return jsonify({
                'success': False,
                'message': 'المجموعة غير موجودة'
            }), 404

        group_name = group_data[0][0]

        # التحقق من عدم وجود موردين مرتبطين بهذه المجموعة
        check_suppliers_sql = "SELECT COUNT(*) FROM suppliers WHERE supplier_group_id = :1"
        suppliers_count = oracle_mgr.execute_query(check_suppliers_sql, [group_id])

        if suppliers_count and suppliers_count[0][0] > 0:
            return jsonify({
                'success': False,
                'message': f'لا يمكن حذف المجموعة "{group_name}" لأنها مرتبطة بـ {suppliers_count[0][0]} مورد'
            })

        # حذف المجموعة
        delete_sql = "DELETE FROM supplier_groups WHERE group_id = :1"
        rows_affected = oracle_mgr.execute_update(delete_sql, [group_id])

        if rows_affected > 0:
            return jsonify({
                'success': True,
                'message': f'تم حذف المجموعة "{group_name}" بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في حذف المجموعة'
            })

    except Exception as e:
        logger.error(f"خطأ في حذف مجموعة الموردين: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })
