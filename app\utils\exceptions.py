"""
استثناءات مخصصة للنظام
Custom Exceptions for the System
"""


class ValidationError(Exception):
    """استثناء للأخطاء في التحقق من صحة البيانات"""
    
    def __init__(self, message: str, details: dict = None):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)
    
    def __str__(self):
        return self.message


class DatabaseError(Exception):
    """استثناء لأخطاء قاعدة البيانات"""
    
    def __init__(self, message: str, original_error: Exception = None):
        self.message = message
        self.original_error = original_error
        super().__init__(self.message)
    
    def __str__(self):
        return self.message


class BusinessLogicError(Exception):
    """استثناء لأخطاء منطق الأعمال"""
    
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)
    
    def __str__(self):
        return self.message


class AuthenticationError(Exception):
    """استثناء لأخطاء المصادقة"""
    
    def __init__(self, message: str = "غير مصرح بالوصول"):
        self.message = message
        super().__init__(self.message)
    
    def __str__(self):
        return self.message


class AuthorizationError(Exception):
    """استثناء لأخطاء التخويل"""
    
    def __init__(self, message: str = "ليس لديك صلاحية للقيام بهذا الإجراء"):
        self.message = message
        super().__init__(self.message)
    
    def __str__(self):
        return self.message


class ConfigurationError(Exception):
    """استثناء لأخطاء الإعدادات"""
    
    def __init__(self, message: str, config_key: str = None):
        self.message = message
        self.config_key = config_key
        super().__init__(self.message)
    
    def __str__(self):
        return self.message


class ExternalServiceError(Exception):
    """استثناء لأخطاء الخدمات الخارجية"""
    
    def __init__(self, message: str, service_name: str = None, status_code: int = None):
        self.message = message
        self.service_name = service_name
        self.status_code = status_code
        super().__init__(self.message)
    
    def __str__(self):
        return self.message


class FileProcessingError(Exception):
    """استثناء لأخطاء معالجة الملفات"""
    
    def __init__(self, message: str, file_path: str = None):
        self.message = message
        self.file_path = file_path
        super().__init__(self.message)
    
    def __str__(self):
        return self.message


class DataIntegrityError(Exception):
    """استثناء لأخطاء سلامة البيانات"""
    
    def __init__(self, message: str, table_name: str = None, constraint: str = None):
        self.message = message
        self.table_name = table_name
        self.constraint = constraint
        super().__init__(self.message)
    
    def __str__(self):
        return self.message


# دوال مساعدة لمعالجة الاستثناءات
def handle_database_error(error: Exception) -> DatabaseError:
    """تحويل أخطاء قاعدة البيانات إلى DatabaseError"""
    
    error_message = str(error)
    
    # أخطاء Oracle الشائعة
    if 'ORA-00001' in error_message:
        return DataIntegrityError("انتهاك قيد الفريدة - البيانات موجودة مسبقاً")
    elif 'ORA-02291' in error_message:
        return DataIntegrityError("انتهاك قيد المرجع الخارجي - البيانات المرجعية غير موجودة")
    elif 'ORA-12541' in error_message:
        return DatabaseError("لا يمكن الاتصال بقاعدة البيانات - تحقق من إعدادات الشبكة")
    elif 'ORA-01017' in error_message:
        return AuthenticationError("اسم المستخدم أو كلمة المرور غير صحيحة")
    elif 'ORA-00942' in error_message:
        return DatabaseError("الجدول أو العرض غير موجود")
    elif 'ORA-00904' in error_message:
        return DatabaseError("العمود غير موجود")
    else:
        return DatabaseError(f"خطأ في قاعدة البيانات: {error_message}", error)


def handle_validation_error(field_name: str, value: any, expected_type: str = None) -> ValidationError:
    """إنشاء ValidationError مع تفاصيل الحقل"""
    
    details = {
        'field': field_name,
        'value': value,
        'expected_type': expected_type
    }
    
    if expected_type:
        message = f"قيمة غير صحيحة للحقل '{field_name}'. متوقع: {expected_type}"
    else:
        message = f"قيمة غير صحيحة للحقل '{field_name}'"
    
    return ValidationError(message, details)


def handle_business_logic_error(operation: str, reason: str, error_code: str = None) -> BusinessLogicError:
    """إنشاء BusinessLogicError مع تفاصيل العملية"""
    
    message = f"لا يمكن تنفيذ العملية '{operation}': {reason}"
    
    return BusinessLogicError(message, error_code)


# معالج عام للاستثناءات
def format_error_response(error: Exception) -> dict:
    """تنسيق استجابة الخطأ للـ API"""
    
    if isinstance(error, ValidationError):
        return {
            'success': False,
            'error_type': 'validation',
            'message': error.message,
            'details': error.details
        }
    elif isinstance(error, DatabaseError):
        return {
            'success': False,
            'error_type': 'database',
            'message': error.message
        }
    elif isinstance(error, BusinessLogicError):
        return {
            'success': False,
            'error_type': 'business_logic',
            'message': error.message,
            'error_code': error.error_code
        }
    elif isinstance(error, AuthenticationError):
        return {
            'success': False,
            'error_type': 'authentication',
            'message': error.message
        }
    elif isinstance(error, AuthorizationError):
        return {
            'success': False,
            'error_type': 'authorization',
            'message': error.message
        }
    else:
        return {
            'success': False,
            'error_type': 'unknown',
            'message': 'حدث خطأ غير متوقع'
        }
