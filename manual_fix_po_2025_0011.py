#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح يدوي لأمر الشراء PO-2025-0011
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def manual_fix_po_2025_0011():
    """إصلاح يدوي لأمر الشراء PO-2025-0011"""
    print("🔧 إصلاح يدوي لأمر الشراء PO-2025-0011...")
    
    try:
        from oracle_manager import get_oracle_manager
        db_manager = get_oracle_manager()
        
        if not db_manager.connect():
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        # فحص الوضع الحالي
        print("🔍 فحص الوضع الحالي...")
        
        # فحص أمر الشراء
        po_query = """
            SELECT ID, PO_NUMBER, IS_USED, USED_IN_SHIPMENT_ID
            FROM PURCHASE_ORDERS
            WHERE PO_NUMBER = 'PO-2025-0011'
        """
        
        po_result = db_manager.execute_query(po_query)
        if not po_result:
            print("❌ أمر الشراء PO-2025-0011 غير موجود")
            return False
        
        po_id, po_number, is_used, used_in_shipment_id = po_result[0]
        print(f"📋 أمر الشراء: {po_number} (ID: {po_id})")
        print(f"   مستخدم: {'نعم' if is_used == 1 else 'لا'}")
        print(f"   معرف الشحنة: {used_in_shipment_id}")
        
        # فحص الشحنة
        if used_in_shipment_id:
            shipment_query = """
                SELECT id, shipment_number, purchase_order_id
                FROM cargo_shipments
                WHERE id = :1
            """
            
            shipment_result = db_manager.execute_query(shipment_query, [used_in_shipment_id])
            if shipment_result:
                shipment_id, shipment_number, shipment_po_id = shipment_result[0]
                print(f"📦 الشحنة: {shipment_number} (ID: {shipment_id})")
                print(f"   أمر الشراء في الشحنة: {shipment_po_id}")
                
                # تحديد المشكلة
                if shipment_po_id != po_id:
                    print(f"❌ مشكلة: أمر الشراء يشير للشحنة ولكن الشحنة لا تشير لأمر الشراء")
                    
                    # الحل: ربط الشحنة بأمر الشراء
                    print(f"🔧 ربط الشحنة {shipment_id} بأمر الشراء {po_id}")
                    
                    update_shipment_query = """
                        UPDATE cargo_shipments 
                        SET purchase_order_id = :1,
                            updated_at = SYSDATE
                        WHERE id = :2
                    """
                    
                    db_manager.execute_update(update_shipment_query, [po_id, shipment_id])
                    print("✅ تم ربط الشحنة بأمر الشراء")
                    
                    db_manager.commit()
                    print("💾 تم حفظ التغييرات")
                    
                else:
                    print("✅ البيانات متطابقة")
            else:
                print(f"❌ الشحنة {used_in_shipment_id} غير موجودة")
                
                # الحل: إزالة الربط من أمر الشراء
                print(f"🔧 إزالة الربط من أمر الشراء {po_id}")
                
                reset_po_query = """
                    UPDATE PURCHASE_ORDERS 
                    SET IS_USED = 0,
                        USED_AT = NULL,
                        USED_IN_SHIPMENT_ID = NULL,
                        UPDATED_AT = SYSDATE
                    WHERE ID = :1
                """
                
                db_manager.execute_update(reset_po_query, [po_id])
                print("✅ تم إزالة الربط من أمر الشراء")
                
                db_manager.commit()
                print("💾 تم حفظ التغييرات")
        
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح: {e}")
        try:
            if 'db_manager' in locals():
                db_manager.rollback()
                db_manager.close()
        except:
            pass
        return False

if __name__ == "__main__":
    manual_fix_po_2025_0011()
