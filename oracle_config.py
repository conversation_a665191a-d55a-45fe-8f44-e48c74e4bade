# -*- coding: utf-8 -*-
"""
إعدادات Oracle Database للنظام المحاسبي المتقدم
Oracle Database Configuration for Advanced Accounting System
"""

import os
from datetime import timedelta

class OracleConfig:
    """إعدادات Oracle Database"""
    
    # إعدادات قاعدة البيانات - Oracle Database Settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'advanced-accounting-system-secret-key-2024'
    
    # إعدادات الاتصال بـ Oracle
    ORACLE_HOST = os.environ.get('ORACLE_HOST') or 'localhost'
    ORACLE_PORT = os.environ.get('ORACLE_PORT') or '1521'
    ORACLE_SID = os.environ.get('ORACLE_SID') or 'ORCL'
    ORACLE_USERNAME = os.environ.get('ORACLE_USERNAME') or 'accounting_user'
    ORACLE_PASSWORD = os.environ.get('ORACLE_PASSWORD') or 'accounting_password'

    # خيار استخدام TNS Names
    USE_TNS_NAMES = os.environ.get('USE_TNS_NAMES', 'false').lower() == 'true'

    if USE_TNS_NAMES:
        # استخدام TNS Names للاتصال
        SQLALCHEMY_DATABASE_URI = f"oracle+oracledb://{ORACLE_USERNAME}:{ORACLE_PASSWORD}@SASERP_DEV"
    else:
        # بناء connection string لـ Oracle باستخدام SID (الافتراضي)
        SQLALCHEMY_DATABASE_URI = (
            f"oracle+oracledb://{ORACLE_USERNAME}:{ORACLE_PASSWORD}@"
            f"{ORACLE_HOST}:{ORACLE_PORT}/{ORACLE_SID}"
        )
    
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'connect_args': {
            'encoding': 'UTF-8',
            'nencoding': 'UTF-8'
        }
    }
    
    # إعدادات اللغة والمنطقة - Language and Locale Settings
    LANGUAGES = {
        'ar': 'العربية',
        'en': 'English'
    }
    DEFAULT_LANGUAGE = 'ar'
    BABEL_DEFAULT_LOCALE = 'ar'
    BABEL_DEFAULT_TIMEZONE = 'Asia/Riyadh'

    # إعدادات العملة
    DEFAULT_CURRENCY = 'SAR'
    CURRENCY_SYMBOL = 'ر.س'
    
    # إعدادات الجلسة - Session Settings
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)
    SESSION_COOKIE_SECURE = False
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    SESSION_COOKIE_DOMAIN = None
    
    # إعدادات الملفات - File Settings
    UPLOAD_FOLDER = 'uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'xlsx', 'xls', 'csv'}
    
    # إعدادات التقارير - Reports Settings
    REPORTS_FOLDER = 'reports'
    BACKUP_FOLDER = 'backups'
    
    # إعدادات النظام المالي - Financial System Settings
    DECIMAL_PLACES = 2
    
    # إعدادات الإشعارات - Notifications Settings
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # إعدادات الأمان - Security Settings
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600
    
    # إعدادات التخزين المؤقت - Caching Settings
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 300

    # إعدادات PWA - Progressive Web App
    PWA_MANIFEST = {
        'name': 'النظام المحاسبي المتقدم',
        'short_name': 'المحاسبة',
        'description': 'نظام محاسبي متقدم للأجهزة المحمولة',
        'start_url': '/',
        'display': 'standalone',
        'background_color': '#667eea',
        'theme_color': '#667eea',
        'orientation': 'portrait'
    }
    
    @staticmethod
    def init_app(app):
        """تهيئة التطبيق"""
        pass

class OracleDevelopmentConfig(OracleConfig):
    """إعدادات بيئة التطوير مع Oracle"""
    DEBUG = True

    # يمكن استخدام Oracle محلي للتطوير
    ORACLE_HOST = os.environ.get('DEV_ORACLE_HOST') or 'localhost'
    ORACLE_PORT = os.environ.get('DEV_ORACLE_PORT') or '1521'
    ORACLE_SID = os.environ.get('DEV_ORACLE_SID') or 'ORCL'
    ORACLE_USERNAME = os.environ.get('DEV_ORACLE_USERNAME') or 'accounting_user'
    ORACLE_PASSWORD = os.environ.get('DEV_ORACLE_PASSWORD') or 'accounting_password'

    # خيار استخدام TNS Names للتطوير
    USE_TNS_NAMES = os.environ.get('USE_TNS_NAMES', 'false').lower() == 'true'

    if USE_TNS_NAMES:
        # استخدام TNS Names للاتصال
        SQLALCHEMY_DATABASE_URI = f"oracle+oracledb://{ORACLE_USERNAME}:{ORACLE_PASSWORD}@SASERP_DEV"
    else:
        # بناء connection string لـ Oracle باستخدام SID (الافتراضي)
        SQLALCHEMY_DATABASE_URI = (
            f"oracle+oracledb://{ORACLE_USERNAME}:{ORACLE_PASSWORD}@"
            f"{ORACLE_HOST}:{ORACLE_PORT}/{ORACLE_SID}"
        )

class OracleTestingConfig(OracleConfig):
    """إعدادات بيئة الاختبار مع Oracle"""
    TESTING = True
    WTF_CSRF_ENABLED = False

    # قاعدة بيانات اختبار منفصلة
    ORACLE_HOST = os.environ.get('TEST_ORACLE_HOST') or 'localhost'
    ORACLE_PORT = os.environ.get('TEST_ORACLE_PORT') or '1521'
    ORACLE_SID = os.environ.get('TEST_ORACLE_SID') or 'ORCL'
    ORACLE_USERNAME = os.environ.get('TEST_ORACLE_USERNAME') or 'accounting_test'
    ORACLE_PASSWORD = os.environ.get('TEST_ORACLE_PASSWORD') or 'test_password'

    SQLALCHEMY_DATABASE_URI = (
        f"oracle+oracledb://{ORACLE_USERNAME}:{ORACLE_PASSWORD}@"
        f"{ORACLE_HOST}:{ORACLE_PORT}/{ORACLE_SID}"
    )

class OracleProductionConfig(OracleConfig):
    """إعدادات بيئة الإنتاج مع Oracle"""
    
    # إعدادات الإنتاج الآمنة
    ORACLE_HOST = os.environ.get('PROD_ORACLE_HOST') or 'production-oracle-server'
    ORACLE_PORT = os.environ.get('PROD_ORACLE_PORT') or '1521'
    ORACLE_SID = os.environ.get('PROD_ORACLE_SID') or 'ORCL'
    ORACLE_USERNAME = os.environ.get('PROD_ORACLE_USERNAME') or 'accounting_user'
    ORACLE_PASSWORD = os.environ.get('PROD_ORACLE_PASSWORD') or 'accounting_password'

    # تحذير إذا لم يتم تعيين متغيرات الإنتاج
    if not os.environ.get('PROD_ORACLE_USERNAME') or not os.environ.get('PROD_ORACLE_PASSWORD'):
        import warnings
        warnings.warn("يُنصح بتعيين PROD_ORACLE_USERNAME و PROD_ORACLE_PASSWORD في بيئة الإنتاج")

    SQLALCHEMY_DATABASE_URI = (
        f"oracle+oracledb://{ORACLE_USERNAME}:{ORACLE_PASSWORD}@"
        f"{ORACLE_HOST}:{ORACLE_PORT}/{ORACLE_SID}"
    )
    
    # إعدادات الأمان المتقدمة للإنتاج
    SESSION_COOKIE_SECURE = True
    PREFERRED_URL_SCHEME = 'https'
    
    @classmethod
    def init_app(cls, app):
        OracleConfig.init_app(app)
        
        # تسجيل الأخطاء عبر البريد الإلكتروني
        import logging
        from logging.handlers import SMTPHandler
        if app.config['MAIL_SERVER']:
            auth = None
            if app.config['MAIL_USERNAME'] or app.config['MAIL_PASSWORD']:
                auth = (app.config['MAIL_USERNAME'], app.config['MAIL_PASSWORD'])
            secure = None
            if app.config['MAIL_USE_TLS']:
                secure = ()
            mail_handler = SMTPHandler(
                mailhost=(app.config['MAIL_SERVER'], app.config['MAIL_PORT']),
                fromaddr='no-reply@' + app.config['MAIL_SERVER'],
                toaddrs=[app.config.get('ADMIN_EMAIL', '<EMAIL>')],
                subject='خطأ في النظام المحاسبي',
                credentials=auth, secure=secure)
            mail_handler.setLevel(logging.ERROR)
            app.logger.addHandler(mail_handler)

# إعدادات Oracle
oracle_config = {
    'development': OracleDevelopmentConfig,
    'testing': OracleTestingConfig,
    'production': OracleProductionConfig,
    'default': OracleDevelopmentConfig
}
