"""
نظام التتبع المتقدم للشحنات
Advanced Shipment Tracking System
"""

import json
import requests
from datetime import datetime, timedelta
from flask import current_app

# استيرادات اختيارية
try:
    from geopy.distance import geodesic
except ImportError:
    geodesic = None

try:
    import googlemaps
except ImportError:
    googlemaps = None

# استيرادات محلية (مؤقتاً معطلة لأننا نستخدم Oracle)
# from .models import Shipment, TrackingEvent, Driver, Vehicle, ShipmentRoute
# from app import db

from .utils import send_notification, calculate_distance

class ShipmentTracker:
    """فئة التتبع المتقدم للشحنات"""
    
    def __init__(self):
        self.gmaps = None
        if hasattr(current_app.config, 'GOOGLE_MAPS_API_KEY'):
            self.gmaps = googlemaps.Client(key=current_app.config['GOOGLE_MAPS_API_KEY'])
    
    def update_shipment_location(self, shipment_id, latitude, longitude, location_name=None):
        """تحديث موقع الشحنة"""
        try:
            shipment = Shipment.query.get(shipment_id)
            if not shipment:
                return False
            
            # تحديث الموقع الحالي
            old_lat = shipment.current_latitude
            old_lng = shipment.current_longitude
            
            shipment.current_latitude = latitude
            shipment.current_longitude = longitude
            shipment.current_location = location_name or self.get_location_name(latitude, longitude)
            
            # حساب المسافة المقطوعة
            distance_moved = 0
            if old_lat and old_lng:
                distance_moved = calculate_distance((old_lat, old_lng), (latitude, longitude))
            
            # إنشاء حدث تتبع
            event = TrackingEvent(
                shipment_id=shipment_id,
                event_type="تحديث الموقع",
                event_description=f"تم تحديث موقع الشحنة. المسافة المقطوعة: {distance_moved:.2f} كم",
                location=shipment.current_location,
                latitude=latitude,
                longitude=longitude,
                created_by="نظام التتبع"
            )
            
            db.session.add(event)
            
            # تحديث الوقت المقدر للوصول
            self.update_estimated_arrival(shipment)
            
            # إرسال إشعارات إذا كانت الشحنة قريبة من الوجهة
            self.check_proximity_notifications(shipment)
            
            db.session.commit()
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error updating shipment location: {e}")
            db.session.rollback()
            return False
    
    def update_shipment_status(self, shipment_id, new_status, notes=None, location=None):
        """تحديث حالة الشحنة"""
        try:
            shipment = Shipment.query.get(shipment_id)
            if not shipment:
                return False
            
            old_status = shipment.status
            shipment.status = new_status
            
            # تحديث التواريخ حسب الحالة
            if new_status == "تم الاستلام":
                shipment.pickup_date = datetime.utcnow()
            elif new_status == "تم التسليم":
                shipment.actual_delivery_date = datetime.utcnow()
            
            # إنشاء حدث تتبع
            event_description = f"تم تغيير حالة الشحنة من '{old_status}' إلى '{new_status}'"
            if notes:
                event_description += f". ملاحظات: {notes}"
            
            event = TrackingEvent(
                shipment_id=shipment_id,
                event_type=f"تغيير الحالة إلى {new_status}",
                event_description=event_description,
                location=location or shipment.current_location,
                latitude=shipment.current_latitude,
                longitude=shipment.current_longitude,
                notes=notes,
                created_by="نظام التتبع"
            )
            
            db.session.add(event)
            
            # إرسال إشعارات للعملاء
            self.send_status_notifications(shipment, new_status)
            
            db.session.commit()
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error updating shipment status: {e}")
            db.session.rollback()
            return False
    
    def get_location_name(self, latitude, longitude):
        """الحصول على اسم الموقع من الإحداثيات"""
        try:
            if self.gmaps:
                result = self.gmaps.reverse_geocode((latitude, longitude))
                if result:
                    return result[0]['formatted_address']
            
            # استخدام Nominatim كبديل
            from geopy.geocoders import Nominatim
            geolocator = Nominatim(user_agent="saserp_tracking")
            location = geolocator.reverse(f"{latitude}, {longitude}")
            
            if location:
                return location.address
            
            return f"موقع غير معروف ({latitude:.4f}, {longitude:.4f})"
            
        except Exception as e:
            current_app.logger.error(f"Error getting location name: {e}")
            return f"موقع غير معروف ({latitude:.4f}, {longitude:.4f})"
    
    def update_estimated_arrival(self, shipment):
        """تحديث الوقت المقدر للوصول"""
        try:
            if not (shipment.current_latitude and shipment.current_longitude and 
                   shipment.recipient_latitude and shipment.recipient_longitude):
                return
            
            # حساب المسافة المتبقية
            remaining_distance = calculate_distance(
                (shipment.current_latitude, shipment.current_longitude),
                (shipment.recipient_latitude, shipment.recipient_longitude)
            )
            
            # تقدير الوقت (متوسط سرعة 50 كم/ساعة)
            estimated_hours = remaining_distance / 50
            
            # إضافة وقت إضافي للتوقفات والازدحام
            estimated_hours *= 1.3
            
            shipment.estimated_arrival = datetime.utcnow() + timedelta(hours=estimated_hours)
            
        except Exception as e:
            current_app.logger.error(f"Error updating estimated arrival: {e}")
    
    def check_proximity_notifications(self, shipment):
        """فحص القرب من الوجهة وإرسال إشعارات"""
        try:
            if not (shipment.current_latitude and shipment.current_longitude and 
                   shipment.recipient_latitude and shipment.recipient_longitude):
                return
            
            distance_to_destination = calculate_distance(
                (shipment.current_latitude, shipment.current_longitude),
                (shipment.recipient_latitude, shipment.recipient_longitude)
            )
            
            # إشعار عند الاقتراب (5 كم)
            if distance_to_destination <= 5 and shipment.status != "خارج للتسليم":
                self.update_shipment_status(
                    shipment.id, 
                    "خارج للتسليم",
                    f"الشحنة على بعد {distance_to_destination:.1f} كم من الوجهة"
                )
                
                # إشعار للمستقبل
                if shipment.recipient_phone:
                    send_notification(
                        shipment.id,
                        'SMS',
                        shipment.recipient_phone,
                        f"شحنتك رقم {shipment.tracking_number} ستصل خلال 30 دقيقة تقريباً"
                    )
            
            # إشعار عند الوصول (500 متر)
            elif distance_to_destination <= 0.5:
                if shipment.recipient_phone:
                    send_notification(
                        shipment.id,
                        'SMS',
                        shipment.recipient_phone,
                        f"شحنتك رقم {shipment.tracking_number} وصلت! السائق في الطريق إليك الآن"
                    )
            
        except Exception as e:
            current_app.logger.error(f"Error checking proximity notifications: {e}")
    
    def send_status_notifications(self, shipment, new_status):
        """إرسال إشعارات تغيير الحالة"""
        try:
            # رسائل الإشعارات
            status_messages = {
                "مؤكد": f"تم تأكيد شحنتك رقم {shipment.tracking_number}",
                "تم الاستلام": f"تم استلام شحنتك رقم {shipment.tracking_number} من المرسل",
                "في الطريق": f"شحنتك رقم {shipment.tracking_number} في الطريق إليك",
                "خارج للتسليم": f"شحنتك رقم {shipment.tracking_number} خارج للتسليم",
                "تم التسليم": f"تم تسليم شحنتك رقم {shipment.tracking_number} بنجاح",
                "مرتجع": f"تم إرجاع شحنتك رقم {shipment.tracking_number}",
                "ملغي": f"تم إلغاء شحنتك رقم {shipment.tracking_number}",
                "متأخر": f"شحنتك رقم {shipment.tracking_number} متأخرة عن الموعد المحدد"
            }
            
            message = status_messages.get(new_status)
            if not message:
                return
            
            # إشعار للمستقبل
            if shipment.recipient_phone:
                send_notification(
                    shipment.id,
                    'SMS',
                    shipment.recipient_phone,
                    message
                )
            
            # إشعار للمرسل في حالات معينة
            if new_status in ["تم التسليم", "مرتجع", "ملغي"] and shipment.sender_phone:
                send_notification(
                    shipment.id,
                    'SMS',
                    shipment.sender_phone,
                    message
                )
            
        except Exception as e:
            current_app.logger.error(f"Error sending status notifications: {e}")
    
    def calculate_optimal_route(self, shipment_id):
        """حساب المسار الأمثل"""
        try:
            shipment = Shipment.query.get(shipment_id)
            if not shipment or not self.gmaps:
                return None
            
            if not (shipment.sender_latitude and shipment.sender_longitude and 
                   shipment.recipient_latitude and shipment.recipient_longitude):
                return None
            
            # حساب المسار باستخدام Google Maps
            directions = self.gmaps.directions(
                origin=(shipment.sender_latitude, shipment.sender_longitude),
                destination=(shipment.recipient_latitude, shipment.recipient_longitude),
                mode="driving",
                optimize_waypoints=True,
                traffic_model="best_guess",
                departure_time=datetime.now()
            )
            
            if not directions:
                return None
            
            route = directions[0]
            leg = route['legs'][0]
            
            # استخراج نقاط المسار
            waypoints = []
            for step in leg['steps']:
                waypoints.append({
                    'lat': step['start_location']['lat'],
                    'lng': step['start_location']['lng'],
                    'instruction': step['html_instructions']
                })
            
            # إضافة نقطة النهاية
            waypoints.append({
                'lat': leg['end_location']['lat'],
                'lng': leg['end_location']['lng'],
                'instruction': 'الوصول إلى الوجهة'
            })
            
            # حفظ المسار في قاعدة البيانات
            route_data = ShipmentRoute(
                shipment_id=shipment_id,
                waypoints=waypoints,
                total_distance=leg['distance']['value'] / 1000,  # تحويل إلى كيلومتر
                estimated_duration=leg['duration']['value'] / 60,  # تحويل إلى دقائق
                route_type="أسرع"
            )
            
            db.session.add(route_data)
            db.session.commit()
            
            return {
                'waypoints': waypoints,
                'total_distance': route_data.total_distance,
                'estimated_duration': route_data.estimated_duration,
                'polyline': route['overview_polyline']['points']
            }
            
        except Exception as e:
            current_app.logger.error(f"Error calculating optimal route: {e}")
            return None
    
    def track_driver_location(self, driver_id, latitude, longitude):
        """تتبع موقع السائق"""
        try:
            driver = Driver.query.get(driver_id)
            if not driver:
                return False
            
            driver.current_latitude = latitude
            driver.current_longitude = longitude
            driver.last_location_update = datetime.utcnow()
            
            # تحديث مواقع الشحنات المرتبطة بالسائق
            active_shipments = Shipment.query.filter_by(
                driver_id=driver_id,
                status="في الطريق"
            ).all()
            
            for shipment in active_shipments:
                self.update_shipment_location(
                    shipment.id,
                    latitude,
                    longitude,
                    self.get_location_name(latitude, longitude)
                )
            
            db.session.commit()
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error tracking driver location: {e}")
            db.session.rollback()
            return False
