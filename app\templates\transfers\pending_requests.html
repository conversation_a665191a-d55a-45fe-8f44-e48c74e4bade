{% extends "base.html" %}

{% block title %}الطلبات المعلقة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="fas fa-clock text-warning me-2"></i>
                الطلبات المعلقة
            </h1>
            <p class="text-muted mb-0">مراجعة واعتماد طلبات الحوالات المعلقة</p>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">قائمة الطلبات المعلقة</h5>
                </div>
                <div class="card-body">
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center py-3">
                                    <h3 id="totalPendingCount" class="mb-1">-</h3>
                                    <small>إجمالي الطلبات المعلقة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center py-3">
                                    <h3 id="totalPendingAmount" class="mb-1">-</h3>
                                    <small>إجمالي المبلغ</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center py-3">
                                    <h3 id="todayPendingCount" class="mb-1">-</h3>
                                    <small>طلبات اليوم</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center py-3">
                                    <h3 id="avgPendingAmount" class="mb-1">-</h3>
                                    <small>متوسط المبلغ</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center py-3">
                                    <button class="btn btn-light btn-sm w-100" onclick="loadPendingRequests()">
                                        <i class="fas fa-sync-alt text-success"></i><br>
                                        <small class="text-success">تحديث</small>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- فلاتر البحث -->
                    <div class="card bg-light mb-3">
                        <div class="card-body py-3">
                            <div class="row">
                                <div class="col-md-5">
                                    <label class="form-label small text-muted mb-1">البحث</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" class="form-control" id="searchInput" placeholder="البحث برقم الطلب أو اسم المستفيد...">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label small text-muted mb-1">الفرع</label>
                                    <select class="form-select" id="branchFilter">
                                        <option value="">جميع الفروع</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label small text-muted mb-1">العملة</label>
                                    <select class="form-select" id="currencyFilter">
                                        <option value="">جميع العملات</option>
                                        <option value="TRY">ليرة تركية</option>
                                        <option value="USD">دولار أمريكي</option>
                                        <option value="EUR">يورو</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label small text-muted mb-1">&nbsp;</label>
                                    <button class="btn btn-outline-primary w-100 d-block" onclick="clearFilters()">
                                        <i class="fas fa-eraser"></i> مسح الفلاتر
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول الطلبات المعلقة -->
                    <div class="table-responsive">
                        <table class="table table-hover" id="pendingRequestsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>التاريخ</th>
                                    <th>المستفيد</th>
                                    <th>المبلغ</th>
                                    <th>العملة</th>
                                    <th>نوع التحويل</th>
                                    <th>الفرع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="pendingRequestsBody">
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <div class="mt-2">جاري تحميل الطلبات المعلقة...</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>


                </div>
            </div>
        </div>
    </div>
</div>

<script>
console.log('🚀 بدء تحميل صفحة الطلبات المعلقة');

// تحميل الطلبات المعلقة
function loadPendingRequests() {
    console.log('🔄 تحميل الطلبات المعلقة...');

    const searchInput = document.getElementById('searchInput');
    const branchFilter = document.getElementById('branchFilter');
    const currencyFilter = document.getElementById('currencyFilter');

    const searchTerm = searchInput ? searchInput.value : '';
    const branchId = branchFilter ? branchFilter.value : '';
    const currency = currencyFilter ? currencyFilter.value : '';

    // إظهار مؤشر التحميل
    const tbody = document.getElementById('pendingRequestsBody');
    if (tbody) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <div class="mt-2">جاري تحميل الطلبات المعلقة...</div>
                </td>
            </tr>
        `;
    }

    // بناء URL مع المعاملات
    const params = new URLSearchParams({
        status: 'pending',
        search: searchTerm,
        branch_id: branchId,
        currency: currency,
        limit: '100'
    });

    fetch(`/transfers/api/requests?${params}`)
        .then(response => {
            console.log('📥 استجابة HTTP:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📋 بيانات API:', data);

            if (data.success) {
                console.log(`✅ تم تحميل ${data.data.length} طلب معلق`);
                displayPendingRequests(data.data);
                updateStatistics(data.data);
            } else {
                console.error('❌ فشل API:', data.message);
                showError('فشل في تحميل الطلبات المعلقة: ' + data.message);
            }
        })
        .catch(error => {
            console.error('❌ خطأ في الطلب:', error);

            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center py-4 text-danger">
                            <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                            <h5>خطأ في تحميل البيانات</h5>
                            <p>خطأ في الاتصال بالخادم: ${error.message}</p>
                            <button class="btn btn-primary btn-sm" onclick="loadPendingRequests()">
                                <i class="fas fa-redo"></i> إعادة المحاولة
                            </button>
                        </td>
                    </tr>
                `;
            }
        });
}



// عرض الطلبات المعلقة
function displayPendingRequests(requests) {
    console.log(`📋 عرض ${requests.length} طلب معلق`);

    const tbody = document.getElementById('pendingRequestsBody');
    if (!tbody) {
        console.error('❌ لم يتم العثور على جدول البيانات');
        return;
    }

    tbody.innerHTML = '';

    if (!requests || requests.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4">
                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                    <h5>لا توجد طلبات معلقة</h5>
                    <p class="text-muted">جميع الطلبات تم معالجتها</p>
                </td>
            </tr>
        `;
        return;
    }

    requests.forEach(function(request, index) {
        console.log(`📄 معالجة الطلب ${index + 1}:`, request);

        // التأكد من وجود البيانات الأساسية
        const requestNumber = request.request_number || 'غير محدد';
        const beneficiaryName = request.beneficiary_name || 'غير محدد';
        const amount = request.amount || 0;
        const currency = request.currency || 'TRY';
        const bankName = request.bank_name || '';
        const branchName = request.branch_name || 'غير محدد';
        const createdAt = request.created_at || '';

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <strong>${requestNumber}</strong>
                <br><small class="text-muted">ID: ${request.id || 'غير محدد'}</small>
            </td>
            <td>
                ${formatDate(createdAt)}
                <br><small class="text-muted">${formatTime(createdAt)}</small>
            </td>
            <td>
                <div class="fw-bold">${beneficiaryName}</div>
                <small class="text-muted">${bankName}</small>
            </td>
            <td class="fw-bold text-primary">
                ${formatCurrency(amount)}
            </td>
            <td>
                <span class="badge bg-secondary">${currency}</span>
            </td>
            <td>
                <span class="badge ${request.money_changer_bank_type === 'bank' ? 'bg-info' : 'bg-warning'}">
                    ${request.money_changer_bank_type === 'bank' ? 'بنك' : 'صراف'}
                </span>
            </td>
            <td>${branchName}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewRequest(${request.id})" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="manageDocuments(${request.id})" title="إدارة الوثائق">
                        <i class="fas fa-file-alt"></i>
                    </button>
                    <button class="btn btn-outline-success" onclick="approveRequest(${request.id})" title="اعتماد">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn btn-outline-warning" onclick="editRequest(${request.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="rejectRequest(${request.id})" title="رفض">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });

    console.log(`✅ تم عرض ${requests.length} طلب في الجدول`);
}

// تحديث الإحصائيات
function updateStatistics(requests) {
    console.log('📊 تحديث الإحصائيات...');

    const totalCount = requests ? requests.length : 0;
    const totalAmount = requests ? requests.reduce((sum, req) => sum + (parseFloat(req.amount) || 0), 0) : 0;
    const avgAmount = totalCount > 0 ? totalAmount / totalCount : 0;

    // طلبات اليوم
    const today = new Date().toISOString().split('T')[0];
    const todayRequests = requests ? requests.filter(req =>
        req.created_at && req.created_at.startsWith(today)
    ) : [];

    console.log(`📈 الإحصائيات: العدد=${totalCount}, المبلغ=${totalAmount}, اليوم=${todayRequests.length}`);

    // تحديث العناصر
    const totalCountElement = document.getElementById('totalPendingCount');
    const totalAmountElement = document.getElementById('totalPendingAmount');
    const todayCountElement = document.getElementById('todayPendingCount');
    const avgAmountElement = document.getElementById('avgPendingAmount');

    if (totalCountElement) totalCountElement.textContent = totalCount;
    if (totalAmountElement) totalAmountElement.textContent = formatCurrency(totalAmount);
    if (todayCountElement) todayCountElement.textContent = todayRequests.length;
    if (avgAmountElement) avgAmountElement.textContent = formatCurrency(avgAmount);
}

// تحميل الفروع
function loadBranches() {
    console.log('🏢 تحميل قائمة الفروع...');

    fetch('/transfers/api/branches')
        .then(response => response.json())
        .then(data => {
            console.log('📥 استجابة الفروع:', data);

            const branchSelect = document.getElementById('branchFilter');
            if (branchSelect && data && Array.isArray(data)) {
                data.forEach(function(branch) {
                    const option = document.createElement('option');
                    option.value = branch.id;
                    option.textContent = branch.name;
                    branchSelect.appendChild(option);
                });
                console.log(`✅ تم تحميل ${data.length} فرع`);
            } else {
                console.warn('⚠️ استجابة غير متوقعة للفروع:', data);
            }
        })
        .catch(error => {
            console.error('❌ فشل في تحميل قائمة الفروع:', error);
        });
}

// عرض طلب
function viewRequest(requestId) {
    console.log('👁️ عرض تفاصيل الطلب:', requestId);

    // استخدام API لجلب التفاصيل وعرضها في modal
    fetch(`/transfers/api/requests/${requestId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showRequestModal(data.data);
            } else {
                alert('فشل في تحميل تفاصيل الطلب: ' + data.message);
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('خطأ في الاتصال بالخادم');
        });
}

// تعديل طلب
function editRequest(requestId) {
    window.location.href = `/transfers/edit-request/${requestId}`;
}

// اعتماد طلب
function approveRequest(requestId) {
    if (confirm('هل أنت متأكد من اعتماد هذا الطلب؟')) {
        $.post(`/transfers/api/approve-request/${requestId}`)
            .done(function(response) {
                if (response.success) {
                    showSuccess('تم اعتماد الطلب بنجاح');
                    loadPendingRequests(); // إعادة تحميل القائمة
                } else {
                    showError('فشل في اعتماد الطلب: ' + response.message);
                }
            })
            .fail(function() {
                showError('حدث خطأ أثناء اعتماد الطلب');
            });
    }
}

// رفض طلب
function rejectRequest(requestId) {
    const reason = prompt('يرجى إدخال سبب الرفض:');
    if (reason) {
        updateRequestStatus(requestId, 'rejected', reason);
    }
}

// تحديث حالة الطلب
function updateRequestStatus(requestId, status, reason = null) {
    $.ajax({
        url: `/transfers/api/requests/${requestId}/status`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify({
            status: status,
            reason: reason
        }),
        success: function(response) {
            if (response.success) {
                showSuccess(`تم ${status === 'approved' ? 'اعتماد' : 'رفض'} الطلب بنجاح`);
                loadPendingRequests(); // إعادة تحميل القائمة
            } else {
                showError('فشل في تحديث حالة الطلب: ' + response.message);
            }
        },
        error: function() {
            showError('خطأ في الاتصال بالخادم');
        }
    });
}

// دوال مساعدة
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function formatTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
}

function formatCurrency(amount) {
    if (!amount) return '0.00';
    return parseFloat(amount).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

function showSuccess(message) {
    // يمكن استخدام نظام التنبيهات الموجود
    alert(message);
}

function showError(message) {
    console.error('❌ خطأ:', message);

    const tbody = document.getElementById('pendingRequestsBody');
    if (tbody) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4 text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <h5>خطأ</h5>
                    <p>${message}</p>
                    <button class="btn btn-primary btn-sm" onclick="loadPendingRequests()">
                        <i class="fas fa-redo"></i> إعادة المحاولة
                    </button>
                </td>
            </tr>
        `;
    }
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 تم تحميل DOM');

    // تحميل البيانات
    loadPendingRequests();
    loadBranches();

    // ربط أحداث البحث والفلاتر
    const searchInput = document.getElementById('searchInput');
    const branchFilter = document.getElementById('branchFilter');
    const currencyFilter = document.getElementById('currencyFilter');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(window.searchTimeout);
            window.searchTimeout = setTimeout(loadPendingRequests, 500);
        });
    }

    if (branchFilter) {
        branchFilter.addEventListener('change', loadPendingRequests);
    }

    if (currencyFilter) {
        currencyFilter.addEventListener('change', loadPendingRequests);
    }

    // تحديث تلقائي كل 30 ثانية
    setInterval(loadPendingRequests, 30000);

    console.log('✅ تم تهيئة صفحة الطلبات المعلقة');
});

// مسح الفلاتر
function clearFilters() {
    console.log('🧹 مسح الفلاتر...');

    const searchInput = document.getElementById('searchInput');
    const branchFilter = document.getElementById('branchFilter');
    const currencyFilter = document.getElementById('currencyFilter');

    if (searchInput) searchInput.value = '';
    if (branchFilter) branchFilter.value = '';
    if (currencyFilter) currencyFilter.value = '';

    // إعادة تحميل البيانات
    loadPendingRequests();
}

// دالة عرض الإشعارات
function showNotification(message, type = 'info') {
    const typeClasses = {
        'success': 'alert-success',
        'warning': 'alert-warning',
        'error': 'alert-danger',
        'info': 'alert-info'
    };

    const typeIcons = {
        'success': 'fas fa-check-circle',
        'warning': 'fas fa-exclamation-triangle',
        'error': 'fas fa-times-circle',
        'info': 'fas fa-info-circle'
    };

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${typeClasses[type] || 'alert-info'} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="${typeIcons[type] || 'fas fa-info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة الرسالة تلقائياً بعد 4 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 4000);
}

// إدارة وثائق الطلب
function manageDocuments(requestId) {
    console.log('📁 إدارة وثائق الطلب:', requestId);

    try {
        // فتح صفحة إدارة الوثائق في نافذة جديدة
        const documentsUrl = `/transfers/requests/${requestId}/documents`;
        const newWindow = window.open(documentsUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

        // التحقق من نجاح فتح النافذة
        if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
            // إذا فشل فتح النافذة (حاجب النوافذ المنبثقة)
            showNotification('⚠️ تم حجب النافذة المنبثقة. يرجى السماح للنوافذ المنبثقة وإعادة المحاولة.', 'warning');

            // محاولة بديلة - فتح في نفس التبويب
            if (confirm('هل تريد فتح صفحة إدارة الوثائق في نفس التبويب؟')) {
                window.location.href = documentsUrl;
            }
        } else {
            showNotification('✅ تم فتح صفحة إدارة الوثائق في نافذة جديدة', 'success');
        }
    } catch (error) {
        console.error('❌ خطأ في فتح نافذة إدارة الوثائق:', error);
        showNotification('❌ حدث خطأ في فتح نافذة إدارة الوثائق', 'error');

        // محاولة بديلة
        const documentsUrl = `/transfers/requests/${requestId}/documents`;
        window.location.href = documentsUrl;
    }
}

// عرض modal تفاصيل الطلب
function showRequestModal(request) {
    const modalContent = `
        <div class="modal fade" id="requestModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تفاصيل طلب الحوالة - ${request.request_number}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>معلومات الطلب</h6>
                                <table class="table table-sm">
                                    <tr><td>رقم الطلب:</td><td>${request.request_number}</td></tr>
                                    <tr><td>المبلغ:</td><td>${request.amount} ${request.currency}</td></tr>
                                    <tr><td>الحالة:</td><td><span class="badge bg-warning">${request.status}</span></td></tr>
                                    <tr><td>الغرض:</td><td>${request.purpose || 'غير محدد'}</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>معلومات المستفيد</h6>
                                <table class="table table-sm">
                                    <tr><td>الاسم:</td><td>${request.beneficiary_name || 'غير محدد'}</td></tr>
                                    <tr><td>البنك:</td><td>${request.bank_name || 'غير محدد'}</td></tr>
                                    <tr><td>رقم الحساب:</td><td>${request.bank_account || 'غير محدد'}</td></tr>
                                    <tr><td>IBAN:</td><td>${request.iban || 'غير محدد'}</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-primary" onclick="editRequest(${request.id})">تعديل</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة modal سابق إن وجد
    const existingModal = document.getElementById('requestModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إضافة modal جديد
    document.body.insertAdjacentHTML('beforeend', modalContent);

    // عرض modal
    const modal = new bootstrap.Modal(document.getElementById('requestModal'));
    modal.show();
}
</script>

{% endblock %}
