"""
مسارات النظام المركزي للأرصدة والمعاملات
Central Balances and Transactions Routes
"""

from flask import request, jsonify, current_app
from . import central_balances_bp
from oracle_manager import OracleManager
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

@central_balances_bp.route('/api/setup/create_entity_types_table', methods=['POST'])
def create_entity_types_table():
    """إنشاء جدول أنواع الكيانات (ENTITY_TYPES)"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()
        
        # إنشاء جدول أنواع الكيانات
        create_table_sql = """
        CREATE TABLE ENTITY_TYPES (
            id NUMBER PRIMARY KEY,
            entity_type_code VARCHAR2(50) UNIQUE NOT NULL,
            entity_name_ar VARCHAR2(100) NOT NULL,
            entity_name_en VARCHAR2(100) NOT NULL,
            module_name VARCHAR2(50) NOT NULL,
            table_name VARCHAR2(50) NOT NULL,
            id_column VARCHAR2(50) NOT NULL,
            name_column VARCHAR2(50) NOT NULL,
            description_column VARCHAR2(50),
            has_balances NUMBER(1) DEFAULT 1,
            has_transactions NUMBER(1) DEFAULT 1,
            default_currency VARCHAR2(10) DEFAULT 'SAR',
            account_prefix VARCHAR2(10),
            sort_order NUMBER DEFAULT 0,
            is_active NUMBER(1) DEFAULT 1,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by NUMBER,
            updated_date TIMESTAMP,
            updated_by NUMBER
        )
        """
        
        oracle_mgr.execute_update(create_table_sql)
        
        # إنشاء sequence للـ ID
        sequence_sql = """
        CREATE SEQUENCE ENTITY_TYPES_SEQ
        START WITH 1
        INCREMENT BY 1
        NOCACHE
        """
        
        oracle_mgr.execute_update(sequence_sql)
        
        # إنشاء trigger للـ ID التلقائي
        trigger_sql = """
        CREATE OR REPLACE TRIGGER ENTITY_TYPES_TRG
        BEFORE INSERT ON ENTITY_TYPES
        FOR EACH ROW
        BEGIN
            IF :NEW.id IS NULL THEN
                :NEW.id := ENTITY_TYPES_SEQ.NEXTVAL;
            END IF;
            :NEW.created_date := CURRENT_TIMESTAMP;
        END;
        """
        
        oracle_mgr.execute_update(trigger_sql)
        oracle_mgr.commit()
        oracle_mgr.disconnect()
        
        logger.info("✅ تم إنشاء جدول ENTITY_TYPES بنجاح")
        return jsonify({
            'success': True,
            'message': 'تم إنشاء جدول أنواع الكيانات بنجاح'
        })
        
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء جدول ENTITY_TYPES: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/setup/insert_entity_types_data', methods=['POST'])
def insert_entity_types_data():
    """إدراج البيانات الأساسية في جدول أنواع الكيانات"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()
        
        # البيانات الأساسية لأنواع الكيانات
        entity_types_data = [
            (1, 'SUPPLIER', 'الموردين', 'Suppliers', 'SUPPLIERS', 'SUPPLIERS', 'id', 'name', 'description', 1, 1, 'SAR', '2', 10, 1, 1),
            (2, 'CUSTOMER', 'العملاء', 'Customers', 'CUSTOMERS', 'CUSTOMERS', 'id', 'name', 'description', 1, 1, 'SAR', '1', 20, 1, 1),
            (3, 'EMPLOYEE', 'الموظفين', 'Employees', 'HR', 'EMPLOYEES', 'id', 'name', 'department', 1, 1, 'SAR', '3', 30, 1, 1),
            (4, 'CLEARING_AGENT', 'المخلصين', 'Clearing Agents', 'CLEARING', 'CLEARING_AGENTS', 'id', 'name', 'license_number', 1, 1, 'SAR', '4', 40, 1, 1),
            (5, 'BANK_ACCOUNT', 'الحسابات البنكية', 'Bank Accounts', 'FINANCE', 'BANK_ACCOUNTS', 'id', 'account_name', 'bank_name', 1, 1, 'SAR', '5', 50, 1, 1),
            (6, 'CASH_ACCOUNT', 'حسابات النقدية', 'Cash Accounts', 'FINANCE', 'CASH_ACCOUNTS', 'id', 'account_name', 'location', 1, 1, 'SAR', '6', 60, 1, 1),
            (7, 'GL_ACCOUNT', 'حسابات الأستاذ العام', 'General Ledger Accounts', 'ACCOUNTING', 'CHART_OF_ACCOUNTS', 'account_code', 'account_name', 'account_type', 1, 1, 'SAR', '', 70, 1, 1),
            (8, 'COST_CENTER', 'مراكز التكلفة', 'Cost Centers', 'ACCOUNTING', 'COST_CENTERS', 'id', 'center_name', 'description', 1, 1, 'SAR', '7', 80, 1, 1),
            (9, 'PROJECT', 'المشاريع', 'Projects', 'PROJECTS', 'PROJECTS', 'id', 'project_name', 'description', 1, 1, 'SAR', '8', 90, 1, 1),
            (10, 'REMITTANCE_COMPANY', 'شركات الحوالات', 'Remittance Companies', 'REMITTANCES', 'REMITTANCE_COMPANIES', 'id', 'company_name', 'license_number', 1, 1, 'SAR', '9', 100, 1, 1)
        ]
        
        insert_sql = """
        INSERT INTO ENTITY_TYPES (
            id, entity_type_code, entity_name_ar, entity_name_en, module_name,
            table_name, id_column, name_column, description_column, has_balances,
            has_transactions, default_currency, account_prefix, sort_order, is_active, created_by
        ) VALUES (
            :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13, :14, :15, :16
        )
        """
        
        for data in entity_types_data:
            oracle_mgr.execute_update(insert_sql, data)
        
        oracle_mgr.commit()
        oracle_mgr.disconnect()
        
        logger.info(f"✅ تم إدراج {len(entity_types_data)} نوع كيان بنجاح")
        return jsonify({
            'success': True,
            'message': f'تم إدراج {len(entity_types_data)} نوع كيان بنجاح'
        })
        
    except Exception as e:
        logger.error(f"❌ خطأ في إدراج بيانات أنواع الكيانات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/entity_types')
def get_entity_types():
    """جلب جميع أنواع الكيانات"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()
        
        query = """
        SELECT 
            id, entity_type_code, entity_name_ar, entity_name_en, module_name,
            table_name, has_balances, has_transactions, default_currency,
            account_prefix, sort_order, is_active
        FROM ENTITY_TYPES
        WHERE is_active = 1
        ORDER BY sort_order, entity_name_ar
        """
        
        results = oracle_mgr.execute_query(query)
        oracle_mgr.disconnect()
        
        entity_types = []
        for row in results:
            entity_types.append({
                'id': row[0],
                'entity_type_code': row[1],
                'entity_name_ar': row[2],
                'entity_name_en': row[3],
                'module_name': row[4],
                'table_name': row[5],
                'has_balances': bool(row[6]),
                'has_transactions': bool(row[7]),
                'default_currency': row[8],
                'account_prefix': row[9],
                'sort_order': row[10],
                'is_active': bool(row[11])
            })
        
        return jsonify({
            'success': True,
            'data': entity_types,
            'count': len(entity_types)
        })
        
    except Exception as e:
        logger.error(f"❌ خطأ في جلب أنواع الكيانات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/setup/create_document_types_table', methods=['POST'])
def create_document_types_table():
    """إنشاء جدول أنواع الوثائق (DOCUMENT_TYPES)"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # إنشاء جدول أنواع الوثائق
        create_table_sql = """
        CREATE TABLE DOCUMENT_TYPES (
            id NUMBER PRIMARY KEY,
            document_type_code VARCHAR2(50) UNIQUE NOT NULL,
            document_name_ar VARCHAR2(100) NOT NULL,
            document_name_en VARCHAR2(100) NOT NULL,
            module_name VARCHAR2(50) NOT NULL,
            document_category VARCHAR2(50) NOT NULL,
            affects_balance NUMBER(1) DEFAULT 1,
            balance_effect VARCHAR2(20),
            requires_approval NUMBER(1) DEFAULT 0,
            auto_post NUMBER(1) DEFAULT 0,
            document_prefix VARCHAR2(10),
            next_number NUMBER DEFAULT 1,
            number_format VARCHAR2(50),
            is_reversible NUMBER(1) DEFAULT 1,
            sort_order NUMBER DEFAULT 0,
            is_active NUMBER(1) DEFAULT 1,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by NUMBER,
            updated_date TIMESTAMP,
            updated_by NUMBER
        )
        """

        oracle_mgr.execute_update(create_table_sql)

        # إنشاء sequence للـ ID
        sequence_sql = """
        CREATE SEQUENCE DOCUMENT_TYPES_SEQ
        START WITH 1
        INCREMENT BY 1
        NOCACHE
        """

        oracle_mgr.execute_update(sequence_sql)

        # إنشاء trigger للـ ID التلقائي
        trigger_sql = """
        CREATE OR REPLACE TRIGGER DOCUMENT_TYPES_TRG
        BEFORE INSERT ON DOCUMENT_TYPES
        FOR EACH ROW
        BEGIN
            IF :NEW.id IS NULL THEN
                :NEW.id := DOCUMENT_TYPES_SEQ.NEXTVAL;
            END IF;
            :NEW.created_date := CURRENT_TIMESTAMP;
        END;
        """

        oracle_mgr.execute_update(trigger_sql)
        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info("✅ تم إنشاء جدول DOCUMENT_TYPES بنجاح")
        return jsonify({
            'success': True,
            'message': 'تم إنشاء جدول أنواع الوثائق بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء جدول DOCUMENT_TYPES: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/setup/insert_document_types_data', methods=['POST'])
def insert_document_types_data():
    """إدراج البيانات الأساسية في جدول أنواع الوثائق"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # البيانات الأساسية لأنواع الوثائق
        document_types_data = [
            (1, 'PURCHASE_ORDER', 'أمر شراء', 'Purchase Order', 'PURCHASES', 'PROCUREMENT', 0, None, 1, 0, 'PO', 1, 'PO-{YYYY}-{####}', 1, 10, 1, 1),
            (2, 'PURCHASE_INVOICE', 'فاتورة شراء', 'Purchase Invoice', 'PURCHASES', 'PROCUREMENT', 1, 'CREDIT', 1, 0, 'PI', 1, 'PI-{YYYY}-{####}', 1, 20, 1, 1),
            (3, 'SALES_INVOICE', 'فاتورة مبيعات', 'Sales Invoice', 'SALES', 'SALES', 1, 'DEBIT', 1, 1, 'SI', 1, 'SI-{YYYY}-{####}', 1, 30, 1, 1),
            (4, 'PAYMENT_VOUCHER', 'سند صرف', 'Payment Voucher', 'FINANCE', 'PAYMENTS', 1, 'CREDIT', 1, 0, 'PV', 1, 'PV-{YYYY}-{####}', 1, 40, 1, 1),
            (5, 'RECEIPT_VOUCHER', 'سند قبض', 'Receipt Voucher', 'FINANCE', 'RECEIPTS', 1, 'DEBIT', 1, 0, 'RV', 1, 'RV-{YYYY}-{####}', 1, 50, 1, 1),
            (6, 'JOURNAL_ENTRY', 'قيد يومية', 'Journal Entry', 'ACCOUNTING', 'JOURNAL', 1, 'BOTH', 1, 0, 'JE', 1, 'JE-{YYYY}-{####}', 1, 60, 1, 1),
            (7, 'REMITTANCE', 'حوالة', 'Remittance', 'REMITTANCES', 'TRANSFERS', 1, 'BOTH', 1, 0, 'RM', 1, 'RM-{YYYY}-{####}', 1, 70, 1, 1),
            (8, 'PURCHASE_RETURN', 'مردود مشتريات', 'Purchase Return', 'PURCHASES', 'RETURNS', 1, 'DEBIT', 1, 0, 'PR', 1, 'PR-{YYYY}-{####}', 1, 80, 1, 1),
            (9, 'SALES_RETURN', 'مردود مبيعات', 'Sales Return', 'SALES', 'RETURNS', 1, 'CREDIT', 1, 0, 'SR', 1, 'SR-{YYYY}-{####}', 1, 90, 1, 1),
            (10, 'PURCHASE_DISCOUNT', 'خصم مشتريات', 'Purchase Discount', 'PURCHASES', 'DISCOUNTS', 1, 'DEBIT', 0, 1, 'PD', 1, 'PD-{YYYY}-{####}', 1, 100, 1, 1),
            (11, 'SALES_DISCOUNT', 'خصم مبيعات', 'Sales Discount', 'SALES', 'DISCOUNTS', 1, 'CREDIT', 0, 1, 'SD', 1, 'SD-{YYYY}-{####}', 1, 110, 1, 1),
            (12, 'BANK_TRANSFER', 'تحويل بنكي', 'Bank Transfer', 'FINANCE', 'TRANSFERS', 1, 'BOTH', 0, 1, 'BT', 1, 'BT-{YYYY}-{####}', 1, 120, 1, 1),
            (13, 'CASH_DEPOSIT', 'إيداع نقدي', 'Cash Deposit', 'FINANCE', 'DEPOSITS', 1, 'DEBIT', 0, 1, 'CD', 1, 'CD-{YYYY}-{####}', 1, 130, 1, 1),
            (14, 'CASH_WITHDRAWAL', 'سحب نقدي', 'Cash Withdrawal', 'FINANCE', 'WITHDRAWALS', 1, 'CREDIT', 0, 1, 'CW', 1, 'CW-{YYYY}-{####}', 1, 140, 1, 1),
            (15, 'OPENING_BALANCE', 'رصيد افتتاحي', 'Opening Balance', 'ACCOUNTING', 'OPENING', 1, 'BOTH', 1, 0, 'OB', 1, 'OB-{YYYY}-{####}', 0, 150, 1, 1)
        ]

        insert_sql = """
        INSERT INTO DOCUMENT_TYPES (
            id, document_type_code, document_name_ar, document_name_en, module_name,
            document_category, affects_balance, balance_effect, requires_approval, auto_post,
            document_prefix, next_number, number_format, is_reversible, sort_order, is_active, created_by
        ) VALUES (
            :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13, :14, :15, :16, :17
        )
        """

        for data in document_types_data:
            oracle_mgr.execute_update(insert_sql, data)

        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info(f"✅ تم إدراج {len(document_types_data)} نوع وثيقة بنجاح")
        return jsonify({
            'success': True,
            'message': f'تم إدراج {len(document_types_data)} نوع وثيقة بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إدراج بيانات أنواع الوثائق: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/document_types')
def get_document_types():
    """جلب جميع أنواع الوثائق"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        query = """
        SELECT
            id, document_type_code, document_name_ar, document_name_en, module_name,
            document_category, affects_balance, balance_effect, requires_approval, auto_post,
            document_prefix, next_number, number_format, is_reversible, sort_order, is_active
        FROM DOCUMENT_TYPES
        WHERE is_active = 1
        ORDER BY sort_order, document_name_ar
        """

        results = oracle_mgr.execute_query(query)
        oracle_mgr.disconnect()

        document_types = []
        for row in results:
            document_types.append({
                'id': row[0],
                'document_type_code': row[1],
                'document_name_ar': row[2],
                'document_name_en': row[3],
                'module_name': row[4],
                'document_category': row[5],
                'affects_balance': bool(row[6]),
                'balance_effect': row[7],
                'requires_approval': bool(row[8]),
                'auto_post': bool(row[9]),
                'document_prefix': row[10],
                'next_number': row[11],
                'number_format': row[12],
                'is_reversible': bool(row[13]),
                'sort_order': row[14],
                'is_active': bool(row[15])
            })

        return jsonify({
            'success': True,
            'data': document_types,
            'count': len(document_types)
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب أنواع الوثائق: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/setup/create_opening_balances_table', methods=['POST'])
def create_opening_balances_table():
    """إنشاء جدول الأرصدة الافتتاحية المركزي (OPENING_BALANCES)"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # إنشاء جدول الأرصدة الافتتاحية المركزي
        create_table_sql = """
        CREATE TABLE OPENING_BALANCES (
            id NUMBER PRIMARY KEY,
            entity_type_code VARCHAR2(50) NOT NULL,
            entity_id NUMBER NOT NULL,
            account_code VARCHAR2(50),
            fiscal_year NUMBER NOT NULL,
            fiscal_period_start_date DATE NOT NULL,
            currency_code VARCHAR2(10) NOT NULL,
            opening_balance_amount NUMBER(15,2) NOT NULL,
            balance_type VARCHAR2(10) CHECK (balance_type IN ('DEBIT', 'CREDIT')),
            exchange_rate NUMBER(10,4) DEFAULT 1,
            base_currency_amount NUMBER(15,2),
            document_type_code VARCHAR2(50),
            document_number VARCHAR2(50),
            status VARCHAR2(20) DEFAULT 'DRAFT',
            notes VARCHAR2(500),
            reference_document VARCHAR2(100),
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by NUMBER,
            updated_date TIMESTAMP,
            updated_by NUMBER,
            approved_date TIMESTAMP,
            approved_by NUMBER,
            posted_date TIMESTAMP,
            posted_by NUMBER,
            is_active NUMBER(1) DEFAULT 1,

            -- Foreign Keys
            CONSTRAINT fk_ob_entity_type FOREIGN KEY (entity_type_code) REFERENCES ENTITY_TYPES(entity_type_code),
            CONSTRAINT fk_ob_document_type FOREIGN KEY (document_type_code) REFERENCES DOCUMENT_TYPES(document_type_code)
        )
        """

        oracle_mgr.execute_update(create_table_sql)

        # إنشاء sequence للـ ID
        sequence_sql = """
        CREATE SEQUENCE OPENING_BALANCES_SEQ
        START WITH 1
        INCREMENT BY 1
        NOCACHE
        """

        oracle_mgr.execute_update(sequence_sql)

        # إنشاء trigger للـ ID التلقائي
        trigger_sql = """
        CREATE OR REPLACE TRIGGER OPENING_BALANCES_TRG
        BEFORE INSERT ON OPENING_BALANCES
        FOR EACH ROW
        BEGIN
            IF :NEW.id IS NULL THEN
                :NEW.id := OPENING_BALANCES_SEQ.NEXTVAL;
            END IF;
            :NEW.created_date := CURRENT_TIMESTAMP;

            -- حساب المبلغ بالعملة الأساسية
            IF :NEW.base_currency_amount IS NULL THEN
                :NEW.base_currency_amount := :NEW.opening_balance_amount * NVL(:NEW.exchange_rate, 1);
            END IF;
        END;
        """

        oracle_mgr.execute_update(trigger_sql)

        # إنشاء فهارس لتحسين الأداء
        indexes_sql = [
            "CREATE INDEX IDX_OB_ENTITY_TYPE ON OPENING_BALANCES(entity_type_code)",
            "CREATE INDEX IDX_OB_ENTITY_ID ON OPENING_BALANCES(entity_id)",
            "CREATE INDEX IDX_OB_FISCAL_YEAR ON OPENING_BALANCES(fiscal_year)",
            "CREATE INDEX IDX_OB_STATUS ON OPENING_BALANCES(status)",
            "CREATE INDEX IDX_OB_CURRENCY ON OPENING_BALANCES(currency_code)",
            "CREATE UNIQUE INDEX IDX_OB_UNIQUE ON OPENING_BALANCES(entity_type_code, entity_id, fiscal_year, currency_code) WHERE is_active = 1"
        ]

        for index_sql in indexes_sql:
            oracle_mgr.execute_update(index_sql)

        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info("✅ تم إنشاء جدول OPENING_BALANCES بنجاح")
        return jsonify({
            'success': True,
            'message': 'تم إنشاء جدول الأرصدة الافتتاحية المركزي بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء جدول OPENING_BALANCES: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/setup/create_current_balances_table', methods=['POST'])
def create_current_balances_table():
    """إنشاء جدول الأرصدة الجارية المركزي (CURRENT_BALANCES)"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # إنشاء جدول الأرصدة الجارية المركزي
        create_table_sql = """
        CREATE TABLE CURRENT_BALANCES (
            id NUMBER PRIMARY KEY,
            entity_type_code VARCHAR2(50) NOT NULL,
            entity_id NUMBER NOT NULL,
            account_code VARCHAR2(50),
            currency_code VARCHAR2(10) NOT NULL,
            opening_balance NUMBER(15,2) DEFAULT 0,
            debit_amount NUMBER(15,2) DEFAULT 0,
            credit_amount NUMBER(15,2) DEFAULT 0,
            current_balance NUMBER(15,2) DEFAULT 0,
            total_transactions_count NUMBER DEFAULT 0,
            last_transaction_date DATE,
            last_document_type VARCHAR2(50),
            last_document_number VARCHAR2(50),
            average_days NUMBER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP,
            created_by NUMBER,
            updated_by NUMBER,

            -- Foreign Key
            CONSTRAINT fk_cb_entity_type FOREIGN KEY (entity_type_code) REFERENCES ENTITY_TYPES(entity_type_code),

            -- Unique constraint
            CONSTRAINT uk_current_balances UNIQUE (entity_type_code, entity_id, currency_code)
        )
        """

        oracle_mgr.execute_update(create_table_sql)

        # إنشاء sequence للـ ID
        sequence_sql = """
        CREATE SEQUENCE CURRENT_BALANCES_SEQ
        START WITH 1
        INCREMENT BY 1
        NOCACHE
        """

        oracle_mgr.execute_update(sequence_sql)

        # إنشاء trigger للـ ID التلقائي وحساب الرصيد الجاري
        trigger_sql = """
        CREATE OR REPLACE TRIGGER CURRENT_BALANCES_TRG
        BEFORE INSERT OR UPDATE ON CURRENT_BALANCES
        FOR EACH ROW
        BEGIN
            IF INSERTING THEN
                IF :NEW.id IS NULL THEN
                    :NEW.id := CURRENT_BALANCES_SEQ.NEXTVAL;
                END IF;
                :NEW.created_at := CURRENT_TIMESTAMP;
            END IF;

            -- تحديث وقت التعديل
            :NEW.updated_at := CURRENT_TIMESTAMP;

            -- حساب الرصيد الجاري
            :NEW.current_balance := :NEW.opening_balance + :NEW.debit_amount - :NEW.credit_amount;
        END;
        """

        oracle_mgr.execute_update(trigger_sql)

        # إنشاء فهارس لتحسين الأداء
        indexes_sql = [
            "CREATE INDEX IDX_CB_ENTITY_TYPE ON CURRENT_BALANCES(entity_type_code)",
            "CREATE INDEX IDX_CB_ENTITY_ID ON CURRENT_BALANCES(entity_id)",
            "CREATE INDEX IDX_CB_CURRENCY ON CURRENT_BALANCES(currency_code)",
            "CREATE INDEX IDX_CB_BALANCE ON CURRENT_BALANCES(current_balance)",
            "CREATE INDEX IDX_CB_LAST_TRANSACTION ON CURRENT_BALANCES(last_transaction_date)"
        ]

        for index_sql in indexes_sql:
            oracle_mgr.execute_update(index_sql)

        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info("✅ تم إنشاء جدول CURRENT_BALANCES بنجاح")
        return jsonify({
            'success': True,
            'message': 'تم إنشاء جدول الأرصدة الجارية المركزي بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء جدول CURRENT_BALANCES: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/setup/create_balance_transactions_table', methods=['POST'])
def create_balance_transactions_table():
    """إنشاء جدول المعاملات المركزي (BALANCE_TRANSACTIONS)"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # إنشاء جدول المعاملات المركزي
        create_table_sql = """
        CREATE TABLE BALANCE_TRANSACTIONS (
            id NUMBER PRIMARY KEY,
            entity_type_code VARCHAR2(50) NOT NULL,
            entity_id NUMBER NOT NULL,
            document_type_code VARCHAR2(50) NOT NULL,
            document_number VARCHAR2(50) NOT NULL,
            document_date DATE NOT NULL,
            currency_code VARCHAR2(10) NOT NULL,
            debit_amount NUMBER(15,2) DEFAULT 0,
            credit_amount NUMBER(15,2) DEFAULT 0,
            exchange_rate NUMBER(10,4) DEFAULT 1,
            base_currency_debit NUMBER(15,2) DEFAULT 0,
            base_currency_credit NUMBER(15,2) DEFAULT 0,
            description VARCHAR2(500),
            reference_number VARCHAR2(100),
            status VARCHAR2(20) DEFAULT 'POSTED',
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by NUMBER,
            updated_date TIMESTAMP,
            updated_by NUMBER,

            -- Foreign Keys
            CONSTRAINT fk_bt_entity_type FOREIGN KEY (entity_type_code) REFERENCES ENTITY_TYPES(entity_type_code),
            CONSTRAINT fk_bt_document_type FOREIGN KEY (document_type_code) REFERENCES DOCUMENT_TYPES(document_type_code)
        )
        """

        oracle_mgr.execute_update(create_table_sql)

        # إنشاء sequence للـ ID
        sequence_sql = """
        CREATE SEQUENCE BALANCE_TRANSACTIONS_SEQ
        START WITH 1
        INCREMENT BY 1
        NOCACHE
        """

        oracle_mgr.execute_update(sequence_sql)

        # إنشاء trigger للـ ID التلقائي وحساب المبالغ بالعملة الأساسية
        trigger_sql = """
        CREATE OR REPLACE TRIGGER BALANCE_TRANSACTIONS_TRG
        BEFORE INSERT OR UPDATE ON BALANCE_TRANSACTIONS
        FOR EACH ROW
        BEGIN
            IF INSERTING THEN
                IF :NEW.id IS NULL THEN
                    :NEW.id := BALANCE_TRANSACTIONS_SEQ.NEXTVAL;
                END IF;
                :NEW.created_date := CURRENT_TIMESTAMP;
            END IF;

            -- تحديث وقت التعديل
            :NEW.updated_date := CURRENT_TIMESTAMP;

            -- حساب المبالغ بالعملة الأساسية
            :NEW.base_currency_debit := :NEW.debit_amount * NVL(:NEW.exchange_rate, 1);
            :NEW.base_currency_credit := :NEW.credit_amount * NVL(:NEW.exchange_rate, 1);
        END;
        """

        oracle_mgr.execute_update(trigger_sql)

        # إنشاء فهارس لتحسين الأداء
        indexes_sql = [
            "CREATE INDEX IDX_BT_ENTITY_TYPE ON BALANCE_TRANSACTIONS(entity_type_code)",
            "CREATE INDEX IDX_BT_ENTITY_ID ON BALANCE_TRANSACTIONS(entity_id)",
            "CREATE INDEX IDX_BT_DOCUMENT_TYPE ON BALANCE_TRANSACTIONS(document_type_code)",
            "CREATE INDEX IDX_BT_DOCUMENT_NUMBER ON BALANCE_TRANSACTIONS(document_number)",
            "CREATE INDEX IDX_BT_DOCUMENT_DATE ON BALANCE_TRANSACTIONS(document_date)",
            "CREATE INDEX IDX_BT_CURRENCY ON BALANCE_TRANSACTIONS(currency_code)",
            "CREATE INDEX IDX_BT_STATUS ON BALANCE_TRANSACTIONS(status)",
            "CREATE INDEX IDX_BT_CREATED_DATE ON BALANCE_TRANSACTIONS(created_date)"
        ]

        for index_sql in indexes_sql:
            oracle_mgr.execute_update(index_sql)

        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info("✅ تم إنشاء جدول BALANCE_TRANSACTIONS بنجاح")
        return jsonify({
            'success': True,
            'message': 'تم إنشاء جدول المعاملات المركزي بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء جدول BALANCE_TRANSACTIONS: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/setup/create_post_opening_balances_procedure', methods=['POST'])
def create_post_opening_balances_procedure():
    """إنشاء إجراء ترحيل الأرصدة الافتتاحية (POST_OPENING_BALANCES)"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # إنشاء إجراء ترحيل الأرصدة الافتتاحية
        procedure_sql = """
        CREATE OR REPLACE PROCEDURE POST_OPENING_BALANCES(
            p_entity_type_code IN VARCHAR2 DEFAULT NULL,
            p_fiscal_date IN DATE,
            p_currency_code IN VARCHAR2 DEFAULT NULL,
            p_posted_by IN NUMBER,
            p_posting_reference IN VARCHAR2 DEFAULT NULL,
            p_result OUT VARCHAR2
        ) AS
            v_count NUMBER := 0;
            v_error_msg VARCHAR2(4000);
        BEGIN
            -- التحقق من صحة المعاملات
            IF p_fiscal_date IS NULL THEN
                p_result := 'ERROR: تاريخ الفترة المحاسبية مطلوب';
                RETURN;
            END IF;

            IF p_posted_by IS NULL THEN
                p_result := 'ERROR: معرف المستخدم مطلوب';
                RETURN;
            END IF;

            -- ترحيل الأرصدة الافتتاحية
            MERGE INTO CURRENT_BALANCES cb
            USING (
                SELECT
                    ob.entity_type_code,
                    ob.entity_id,
                    ob.account_code,
                    ob.currency_code,
                    ob.opening_balance_amount,
                    ob.balance_type,
                    ob.document_type_code,
                    ob.document_number
                FROM OPENING_BALANCES ob
                WHERE ob.fiscal_period_start_date = p_fiscal_date
                AND ob.status = 'POSTED'
                AND ob.is_active = 1
                AND (p_entity_type_code IS NULL OR ob.entity_type_code = p_entity_type_code)
                AND (p_currency_code IS NULL OR ob.currency_code = p_currency_code)
            ) src ON (
                cb.entity_type_code = src.entity_type_code
                AND cb.entity_id = src.entity_id
                AND cb.currency_code = src.currency_code
            )
            WHEN MATCHED THEN
                UPDATE SET
                    opening_balance = src.opening_balance_amount,
                    current_balance = src.opening_balance_amount + cb.debit_amount - cb.credit_amount,
                    updated_at = CURRENT_TIMESTAMP,
                    updated_by = p_posted_by
            WHEN NOT MATCHED THEN
                INSERT (
                    entity_type_code, entity_id, account_code, currency_code,
                    opening_balance, current_balance,
                    created_at, updated_at, created_by, updated_by
                )
                VALUES (
                    src.entity_type_code, src.entity_id, src.account_code, src.currency_code,
                    src.opening_balance_amount, src.opening_balance_amount,
                    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, p_posted_by, p_posted_by
                );

            v_count := SQL%ROWCOUNT;

            -- تسجيل المعاملة في جدول المعاملات
            INSERT INTO BALANCE_TRANSACTIONS (
                entity_type_code, entity_id, document_type_code, document_number,
                document_date, currency_code, debit_amount, credit_amount,
                description, created_by
            )
            SELECT
                ob.entity_type_code, ob.entity_id,
                NVL(ob.document_type_code, 'OPENING_BALANCE'),
                NVL(ob.document_number, 'OB-' || TO_CHAR(p_fiscal_date, 'YYYY') || '-' || ob.id),
                p_fiscal_date, ob.currency_code,
                CASE WHEN ob.balance_type = 'DEBIT' THEN ob.opening_balance_amount ELSE 0 END,
                CASE WHEN ob.balance_type = 'CREDIT' THEN ob.opening_balance_amount ELSE 0 END,
                'ترحيل رصيد افتتاحي - ' || NVL(p_posting_reference, 'تلقائي'),
                p_posted_by
            FROM OPENING_BALANCES ob
            WHERE ob.fiscal_period_start_date = p_fiscal_date
            AND ob.status = 'POSTED'
            AND ob.is_active = 1
            AND (p_entity_type_code IS NULL OR ob.entity_type_code = p_entity_type_code)
            AND (p_currency_code IS NULL OR ob.currency_code = p_currency_code);

            COMMIT;
            p_result := 'SUCCESS: تم ترحيل ' || v_count || ' رصيد افتتاحي بنجاح';

        EXCEPTION
            WHEN OTHERS THEN
                ROLLBACK;
                v_error_msg := SQLERRM;
                p_result := 'ERROR: ' || v_error_msg;
        END POST_OPENING_BALANCES;
        """

        oracle_mgr.execute_update(procedure_sql)
        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info("✅ تم إنشاء إجراء POST_OPENING_BALANCES بنجاح")
        return jsonify({
            'success': True,
            'message': 'تم إنشاء إجراء ترحيل الأرصدة الافتتاحية بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء إجراء POST_OPENING_BALANCES: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/post_opening_balances', methods=['POST'])
def post_opening_balances():
    """ترحيل الأرصدة الافتتاحية"""
    try:
        data = request.get_json()

        # المعاملات المطلوبة
        fiscal_date = data.get('fiscal_date')
        posted_by = data.get('posted_by', 1)  # افتراضي المستخدم 1

        # المعاملات الاختيارية
        entity_type_code = data.get('entity_type_code')
        currency_code = data.get('currency_code')
        posting_reference = data.get('posting_reference')

        if not fiscal_date:
            return jsonify({'success': False, 'message': 'تاريخ الفترة المحاسبية مطلوب'}), 400

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # استدعاء الإجراء المخزن
        call_sql = """
        DECLARE
            v_result VARCHAR2(4000);
        BEGIN
            POST_OPENING_BALANCES(
                p_entity_type_code => :1,
                p_fiscal_date => TO_DATE(:2, 'YYYY-MM-DD'),
                p_currency_code => :3,
                p_posted_by => :4,
                p_posting_reference => :5,
                p_result => v_result
            );
            :6 := v_result;
        END;
        """

        # تحضير المعاملات
        params = [
            entity_type_code,
            fiscal_date,
            currency_code,
            posted_by,
            posting_reference,
            None  # للنتيجة
        ]

        # تنفيذ الإجراء
        cursor = oracle_mgr.connection.cursor()
        result_var = cursor.var(str)
        params[5] = result_var

        cursor.execute(call_sql, params)
        result = result_var.getvalue()

        cursor.close()
        oracle_mgr.disconnect()

        # تحليل النتيجة
        if result.startswith('SUCCESS:'):
            logger.info(f"✅ {result}")
            return jsonify({
                'success': True,
                'message': result.replace('SUCCESS: ', ''),
                'result': result
            })
        else:
            logger.error(f"❌ {result}")
            return jsonify({
                'success': False,
                'message': result.replace('ERROR: ', ''),
                'result': result
            }), 400

    except Exception as e:
        logger.error(f"❌ خطأ في ترحيل الأرصدة الافتتاحية: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/setup/create_update_balance_procedure', methods=['POST'])
def create_update_balance_procedure():
    """إنشاء إجراء تحديث الأرصدة (UPDATE_BALANCE)"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # إنشاء إجراء تحديث الأرصدة
        procedure_sql = """
        CREATE OR REPLACE PROCEDURE UPDATE_BALANCE(
            p_entity_type_code IN VARCHAR2,
            p_entity_id IN NUMBER,
            p_currency_code IN VARCHAR2,
            p_debit_amount IN NUMBER DEFAULT 0,
            p_credit_amount IN NUMBER DEFAULT 0,
            p_document_type_code IN VARCHAR2,
            p_document_number IN VARCHAR2,
            p_updated_by IN NUMBER,
            p_result OUT VARCHAR2
        ) AS
            v_error_msg VARCHAR2(4000);
            v_count NUMBER := 0;
        BEGIN
            -- التحقق من صحة المعاملات
            IF p_entity_type_code IS NULL THEN
                p_result := 'ERROR: نوع الكيان مطلوب';
                RETURN;
            END IF;

            IF p_entity_id IS NULL THEN
                p_result := 'ERROR: معرف الكيان مطلوب';
                RETURN;
            END IF;

            IF p_currency_code IS NULL THEN
                p_result := 'ERROR: رمز العملة مطلوب';
                RETURN;
            END IF;

            IF p_debit_amount = 0 AND p_credit_amount = 0 THEN
                p_result := 'ERROR: يجب أن يكون هناك مبلغ مدين أو دائن';
                RETURN;
            END IF;

            -- التحقق من وجود نوع الكيان
            SELECT COUNT(*) INTO v_count
            FROM ENTITY_TYPES
            WHERE entity_type_code = p_entity_type_code
            AND is_active = 1;

            IF v_count = 0 THEN
                p_result := 'ERROR: نوع الكيان غير موجود أو غير نشط';
                RETURN;
            END IF;

            -- التحقق من وجود نوع الوثيقة
            IF p_document_type_code IS NOT NULL THEN
                SELECT COUNT(*) INTO v_count
                FROM DOCUMENT_TYPES
                WHERE document_type_code = p_document_type_code
                AND is_active = 1;

                IF v_count = 0 THEN
                    p_result := 'ERROR: نوع الوثيقة غير موجود أو غير نشط';
                    RETURN;
                END IF;
            END IF;

            -- تحديث الرصيد الجاري
            MERGE INTO CURRENT_BALANCES cb
            USING (
                SELECT
                    p_entity_type_code as entity_type_code,
                    p_entity_id as entity_id,
                    p_currency_code as currency_code,
                    p_debit_amount as debit_amount,
                    p_credit_amount as credit_amount,
                    p_document_type_code as document_type_code,
                    p_document_number as document_number,
                    p_updated_by as updated_by
                FROM DUAL
            ) src ON (
                cb.entity_type_code = src.entity_type_code
                AND cb.entity_id = src.entity_id
                AND cb.currency_code = src.currency_code
            )
            WHEN MATCHED THEN
                UPDATE SET
                    debit_amount = cb.debit_amount + src.debit_amount,
                    credit_amount = cb.credit_amount + src.credit_amount,
                    current_balance = cb.opening_balance + (cb.debit_amount + src.debit_amount) - (cb.credit_amount + src.credit_amount),
                    total_transactions_count = cb.total_transactions_count + 1,
                    last_transaction_date = SYSDATE,
                    last_document_type = src.document_type_code,
                    last_document_number = src.document_number,
                    updated_at = CURRENT_TIMESTAMP,
                    updated_by = src.updated_by
            WHEN NOT MATCHED THEN
                INSERT (
                    entity_type_code, entity_id, currency_code,
                    opening_balance, debit_amount, credit_amount, current_balance,
                    total_transactions_count, last_transaction_date,
                    last_document_type, last_document_number,
                    created_at, updated_at, created_by, updated_by
                )
                VALUES (
                    src.entity_type_code, src.entity_id, src.currency_code,
                    0, src.debit_amount, src.credit_amount, src.debit_amount - src.credit_amount,
                    1, SYSDATE,
                    src.document_type_code, src.document_number,
                    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, src.updated_by, src.updated_by
                );

            COMMIT;
            p_result := 'SUCCESS: تم تحديث الرصيد بنجاح';

        EXCEPTION
            WHEN OTHERS THEN
                ROLLBACK;
                v_error_msg := SQLERRM;
                p_result := 'ERROR: ' || v_error_msg;
        END UPDATE_BALANCE;
        """

        oracle_mgr.execute_update(procedure_sql)
        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info("✅ تم إنشاء إجراء UPDATE_BALANCE بنجاح")
        return jsonify({
            'success': True,
            'message': 'تم إنشاء إجراء تحديث الأرصدة بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء إجراء UPDATE_BALANCE: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/setup/create_add_transaction_procedure', methods=['POST'])
def create_add_transaction_procedure():
    """إنشاء إجراء إضافة معاملة شامل (ADD_TRANSACTION)"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # إنشاء إجراء إضافة معاملة شامل
        procedure_sql = """
        CREATE OR REPLACE PROCEDURE ADD_TRANSACTION(
            p_entity_type_code IN VARCHAR2,
            p_entity_id IN NUMBER,
            p_document_type_code IN VARCHAR2,
            p_document_number IN VARCHAR2,
            p_document_date IN DATE,
            p_currency_code IN VARCHAR2,
            p_debit_amount IN NUMBER DEFAULT 0,
            p_credit_amount IN NUMBER DEFAULT 0,
            p_exchange_rate IN NUMBER DEFAULT 1,
            p_description IN VARCHAR2 DEFAULT NULL,
            p_reference_number IN VARCHAR2 DEFAULT NULL,
            p_created_by IN NUMBER,
            p_result OUT VARCHAR2,
            p_transaction_id OUT NUMBER
        ) AS
            v_error_msg VARCHAR2(4000);
            v_count NUMBER := 0;
            v_update_result VARCHAR2(4000);
            v_base_currency_debit NUMBER;
            v_base_currency_credit NUMBER;
        BEGIN
            -- التحقق من صحة المعاملات
            IF p_entity_type_code IS NULL THEN
                p_result := 'ERROR: نوع الكيان مطلوب';
                RETURN;
            END IF;

            IF p_entity_id IS NULL THEN
                p_result := 'ERROR: معرف الكيان مطلوب';
                RETURN;
            END IF;

            IF p_document_type_code IS NULL THEN
                p_result := 'ERROR: نوع الوثيقة مطلوب';
                RETURN;
            END IF;

            IF p_document_number IS NULL THEN
                p_result := 'ERROR: رقم الوثيقة مطلوب';
                RETURN;
            END IF;

            IF p_document_date IS NULL THEN
                p_result := 'ERROR: تاريخ الوثيقة مطلوب';
                RETURN;
            END IF;

            IF p_currency_code IS NULL THEN
                p_result := 'ERROR: رمز العملة مطلوب';
                RETURN;
            END IF;

            IF p_debit_amount = 0 AND p_credit_amount = 0 THEN
                p_result := 'ERROR: يجب أن يكون هناك مبلغ مدين أو دائن';
                RETURN;
            END IF;

            IF p_created_by IS NULL THEN
                p_result := 'ERROR: معرف المستخدم مطلوب';
                RETURN;
            END IF;

            -- التحقق من وجود نوع الكيان
            SELECT COUNT(*) INTO v_count
            FROM ENTITY_TYPES
            WHERE entity_type_code = p_entity_type_code
            AND is_active = 1;

            IF v_count = 0 THEN
                p_result := 'ERROR: نوع الكيان غير موجود أو غير نشط';
                RETURN;
            END IF;

            -- التحقق من وجود نوع الوثيقة
            SELECT COUNT(*) INTO v_count
            FROM DOCUMENT_TYPES
            WHERE document_type_code = p_document_type_code
            AND is_active = 1;

            IF v_count = 0 THEN
                p_result := 'ERROR: نوع الوثيقة غير موجود أو غير نشط';
                RETURN;
            END IF;

            -- التحقق من عدم تكرار رقم الوثيقة
            SELECT COUNT(*) INTO v_count
            FROM BALANCE_TRANSACTIONS
            WHERE document_type_code = p_document_type_code
            AND document_number = p_document_number
            AND status = 'POSTED';

            IF v_count > 0 THEN
                p_result := 'ERROR: رقم الوثيقة مكرر';
                RETURN;
            END IF;

            -- حساب المبالغ بالعملة الأساسية
            v_base_currency_debit := p_debit_amount * NVL(p_exchange_rate, 1);
            v_base_currency_credit := p_credit_amount * NVL(p_exchange_rate, 1);

            -- إدراج المعاملة في جدول المعاملات
            INSERT INTO BALANCE_TRANSACTIONS (
                entity_type_code, entity_id, document_type_code, document_number,
                document_date, currency_code, debit_amount, credit_amount,
                exchange_rate, base_currency_debit, base_currency_credit,
                description, reference_number, status, created_by
            ) VALUES (
                p_entity_type_code, p_entity_id, p_document_type_code, p_document_number,
                p_document_date, p_currency_code, p_debit_amount, p_credit_amount,
                p_exchange_rate, v_base_currency_debit, v_base_currency_credit,
                p_description, p_reference_number, 'POSTED', p_created_by
            ) RETURNING id INTO p_transaction_id;

            -- تحديث الرصيد الجاري
            UPDATE_BALANCE(
                p_entity_type_code => p_entity_type_code,
                p_entity_id => p_entity_id,
                p_currency_code => p_currency_code,
                p_debit_amount => p_debit_amount,
                p_credit_amount => p_credit_amount,
                p_document_type_code => p_document_type_code,
                p_document_number => p_document_number,
                p_updated_by => p_created_by,
                p_result => v_update_result
            );

            -- التحقق من نجاح تحديث الرصيد
            IF v_update_result NOT LIKE 'SUCCESS:%' THEN
                ROLLBACK;
                p_result := v_update_result;
                RETURN;
            END IF;

            COMMIT;
            p_result := 'SUCCESS: تم إضافة المعاملة وتحديث الرصيد بنجاح - معرف المعاملة: ' || p_transaction_id;

        EXCEPTION
            WHEN OTHERS THEN
                ROLLBACK;
                v_error_msg := SQLERRM;
                p_result := 'ERROR: ' || v_error_msg;
                p_transaction_id := NULL;
        END ADD_TRANSACTION;
        """

        oracle_mgr.execute_update(procedure_sql)
        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info("✅ تم إنشاء إجراء ADD_TRANSACTION بنجاح")
        return jsonify({
            'success': True,
            'message': 'تم إنشاء إجراء إضافة المعاملة الشامل بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء إجراء ADD_TRANSACTION: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/add_transaction', methods=['POST'])
def add_transaction():
    """إضافة معاملة جديدة وتحديث الرصيد"""
    try:
        data = request.get_json()

        # المعاملات المطلوبة
        entity_type_code = data.get('entity_type_code')
        entity_id = data.get('entity_id')
        document_type_code = data.get('document_type_code')
        document_number = data.get('document_number')
        document_date = data.get('document_date')
        currency_code = data.get('currency_code')
        created_by = data.get('created_by', 1)

        # المعاملات الاختيارية
        debit_amount = data.get('debit_amount', 0)
        credit_amount = data.get('credit_amount', 0)
        exchange_rate = data.get('exchange_rate', 1)
        description = data.get('description')
        reference_number = data.get('reference_number')

        # التحقق من المعاملات المطلوبة
        required_fields = {
            'entity_type_code': entity_type_code,
            'entity_id': entity_id,
            'document_type_code': document_type_code,
            'document_number': document_number,
            'document_date': document_date,
            'currency_code': currency_code
        }

        missing_fields = [field for field, value in required_fields.items() if not value]
        if missing_fields:
            return jsonify({
                'success': False,
                'message': f'الحقول التالية مطلوبة: {", ".join(missing_fields)}'
            }), 400

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # استدعاء الإجراء المخزن
        call_sql = """
        DECLARE
            v_result VARCHAR2(4000);
            v_transaction_id NUMBER;
        BEGIN
            ADD_TRANSACTION(
                p_entity_type_code => :1,
                p_entity_id => :2,
                p_document_type_code => :3,
                p_document_number => :4,
                p_document_date => TO_DATE(:5, 'YYYY-MM-DD'),
                p_currency_code => :6,
                p_debit_amount => :7,
                p_credit_amount => :8,
                p_exchange_rate => :9,
                p_description => :10,
                p_reference_number => :11,
                p_created_by => :12,
                p_result => v_result,
                p_transaction_id => v_transaction_id
            );
            :13 := v_result;
            :14 := v_transaction_id;
        END;
        """

        # تحضير المعاملات
        params = [
            entity_type_code, entity_id, document_type_code, document_number,
            document_date, currency_code, debit_amount, credit_amount,
            exchange_rate, description, reference_number, created_by,
            None, None  # للنتيجة ومعرف المعاملة
        ]

        # تنفيذ الإجراء
        cursor = oracle_mgr.connection.cursor()
        result_var = cursor.var(str)
        transaction_id_var = cursor.var(int)
        params[12] = result_var
        params[13] = transaction_id_var

        cursor.execute(call_sql, params)
        result = result_var.getvalue()
        transaction_id = transaction_id_var.getvalue()

        cursor.close()
        oracle_mgr.disconnect()

        # تحليل النتيجة
        if result.startswith('SUCCESS:'):
            logger.info(f"✅ {result}")
            return jsonify({
                'success': True,
                'message': result.replace('SUCCESS: ', ''),
                'transaction_id': transaction_id,
                'result': result
            })
        else:
            logger.error(f"❌ {result}")
            return jsonify({
                'success': False,
                'message': result.replace('ERROR: ', ''),
                'result': result
            }), 400

    except Exception as e:
        logger.error(f"❌ خطأ في إضافة المعاملة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/setup/create_reverse_transaction_procedure', methods=['POST'])
def create_reverse_transaction_procedure():
    """إنشاء إجراء عكس المعاملة (REVERSE_TRANSACTION)"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # إنشاء إجراء عكس المعاملة
        procedure_sql = """
        CREATE OR REPLACE PROCEDURE REVERSE_TRANSACTION(
            p_transaction_id IN NUMBER,
            p_reversal_reason IN VARCHAR2,
            p_reversed_by IN NUMBER,
            p_result OUT VARCHAR2
        ) AS
            v_error_msg VARCHAR2(4000);
            v_count NUMBER := 0;
            v_update_result VARCHAR2(4000);

            -- متغيرات المعاملة الأصلية
            v_entity_type_code VARCHAR2(50);
            v_entity_id NUMBER;
            v_document_type_code VARCHAR2(50);
            v_document_number VARCHAR2(50);
            v_document_date DATE;
            v_currency_code VARCHAR2(10);
            v_debit_amount NUMBER;
            v_credit_amount NUMBER;
            v_exchange_rate NUMBER;
            v_description VARCHAR2(500);
            v_reference_number VARCHAR2(100);
            v_original_created_by NUMBER;

            v_reversal_transaction_id NUMBER;
        BEGIN
            -- التحقق من صحة المعاملات
            IF p_transaction_id IS NULL THEN
                p_result := 'ERROR: معرف المعاملة مطلوب';
                RETURN;
            END IF;

            IF p_reversed_by IS NULL THEN
                p_result := 'ERROR: معرف المستخدم مطلوب';
                RETURN;
            END IF;

            -- التحقق من وجود المعاملة
            SELECT COUNT(*) INTO v_count
            FROM BALANCE_TRANSACTIONS
            WHERE id = p_transaction_id
            AND status = 'POSTED';

            IF v_count = 0 THEN
                p_result := 'ERROR: المعاملة غير موجودة أو تم عكسها مسبقاً';
                RETURN;
            END IF;

            -- جلب بيانات المعاملة الأصلية
            SELECT
                entity_type_code, entity_id, document_type_code, document_number,
                document_date, currency_code, debit_amount, credit_amount,
                exchange_rate, description, reference_number, created_by
            INTO
                v_entity_type_code, v_entity_id, v_document_type_code, v_document_number,
                v_document_date, v_currency_code, v_debit_amount, v_credit_amount,
                v_exchange_rate, v_description, v_reference_number, v_original_created_by
            FROM BALANCE_TRANSACTIONS
            WHERE id = p_transaction_id;

            -- التحقق من إمكانية عكس نوع الوثيقة
            SELECT COUNT(*) INTO v_count
            FROM DOCUMENT_TYPES
            WHERE document_type_code = v_document_type_code
            AND is_reversible = 1
            AND is_active = 1;

            IF v_count = 0 THEN
                p_result := 'ERROR: نوع الوثيقة غير قابل للعكس';
                RETURN;
            END IF;

            -- إنشاء معاملة العكس (عكس المبالغ)
            INSERT INTO BALANCE_TRANSACTIONS (
                entity_type_code, entity_id, document_type_code,
                document_number, document_date, currency_code,
                debit_amount, credit_amount, exchange_rate,
                base_currency_debit, base_currency_credit,
                description, reference_number, status, created_by
            ) VALUES (
                v_entity_type_code, v_entity_id, v_document_type_code,
                'REV-' || v_document_number, SYSDATE, v_currency_code,
                v_credit_amount, v_debit_amount, v_exchange_rate,  -- عكس المبالغ
                v_credit_amount * v_exchange_rate, v_debit_amount * v_exchange_rate,  -- عكس المبالغ بالعملة الأساسية
                'عكس معاملة: ' || NVL(v_description, '') || ' - السبب: ' || NVL(p_reversal_reason, 'غير محدد'),
                'REVERSAL-' || NVL(v_reference_number, TO_CHAR(p_transaction_id)),
                'POSTED', p_reversed_by
            ) RETURNING id INTO v_reversal_transaction_id;

            -- تحديث الرصيد الجاري (عكس التأثير)
            UPDATE_BALANCE(
                p_entity_type_code => v_entity_type_code,
                p_entity_id => v_entity_id,
                p_currency_code => v_currency_code,
                p_debit_amount => v_credit_amount,  -- عكس المبالغ
                p_credit_amount => v_debit_amount,  -- عكس المبالغ
                p_document_type_code => v_document_type_code,
                p_document_number => 'REV-' || v_document_number,
                p_updated_by => p_reversed_by,
                p_result => v_update_result
            );

            -- التحقق من نجاح تحديث الرصيد
            IF v_update_result NOT LIKE 'SUCCESS:%' THEN
                ROLLBACK;
                p_result := v_update_result;
                RETURN;
            END IF;

            -- تحديث حالة المعاملة الأصلية
            UPDATE BALANCE_TRANSACTIONS
            SET
                status = 'REVERSED',
                updated_date = CURRENT_TIMESTAMP,
                updated_by = p_reversed_by
            WHERE id = p_transaction_id;

            COMMIT;
            p_result := 'SUCCESS: تم عكس المعاملة بنجاح - معرف معاملة العكس: ' || v_reversal_transaction_id;

        EXCEPTION
            WHEN OTHERS THEN
                ROLLBACK;
                v_error_msg := SQLERRM;
                p_result := 'ERROR: ' || v_error_msg;
        END REVERSE_TRANSACTION;
        """

        oracle_mgr.execute_update(procedure_sql)
        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info("✅ تم إنشاء إجراء REVERSE_TRANSACTION بنجاح")
        return jsonify({
            'success': True,
            'message': 'تم إنشاء إجراء عكس المعاملة بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء إجراء REVERSE_TRANSACTION: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/setup/create_calculate_balances_procedure', methods=['POST'])
def create_calculate_balances_procedure():
    """إنشاء إجراء حساب الأرصدة (CALCULATE_BALANCES)"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # إنشاء إجراء حساب الأرصدة
        procedure_sql = """
        CREATE OR REPLACE PROCEDURE CALCULATE_BALANCES(
            p_entity_type_code IN VARCHAR2 DEFAULT NULL,
            p_entity_id IN NUMBER DEFAULT NULL,
            p_currency_code IN VARCHAR2 DEFAULT NULL,
            p_calculated_by IN NUMBER,
            p_result OUT VARCHAR2
        ) AS
            v_error_msg VARCHAR2(4000);
            v_count NUMBER := 0;
            v_processed_count NUMBER := 0;

            -- Cursor لجميع الكيانات المطلوب إعادة حساب أرصدتها
            CURSOR balance_cursor IS
                SELECT DISTINCT
                    bt.entity_type_code,
                    bt.entity_id,
                    bt.currency_code
                FROM BALANCE_TRANSACTIONS bt
                WHERE bt.status = 'POSTED'
                AND (p_entity_type_code IS NULL OR bt.entity_type_code = p_entity_type_code)
                AND (p_entity_id IS NULL OR bt.entity_id = p_entity_id)
                AND (p_currency_code IS NULL OR bt.currency_code = p_currency_code)
                ORDER BY bt.entity_type_code, bt.entity_id, bt.currency_code;

            v_opening_balance NUMBER;
            v_total_debit NUMBER;
            v_total_credit NUMBER;
            v_current_balance NUMBER;
            v_transaction_count NUMBER;
            v_last_transaction_date DATE;
            v_last_document_type VARCHAR2(50);
            v_last_document_number VARCHAR2(50);

        BEGIN
            -- التحقق من صحة المعاملات
            IF p_calculated_by IS NULL THEN
                p_result := 'ERROR: معرف المستخدم مطلوب';
                RETURN;
            END IF;

            -- معالجة كل كيان
            FOR balance_rec IN balance_cursor LOOP

                -- الحصول على الرصيد الافتتاحي
                BEGIN
                    SELECT NVL(opening_balance, 0)
                    INTO v_opening_balance
                    FROM CURRENT_BALANCES
                    WHERE entity_type_code = balance_rec.entity_type_code
                    AND entity_id = balance_rec.entity_id
                    AND currency_code = balance_rec.currency_code;
                EXCEPTION
                    WHEN NO_DATA_FOUND THEN
                        v_opening_balance := 0;
                END;

                -- حساب إجمالي المبالغ المدينة والدائنة
                SELECT
                    NVL(SUM(debit_amount), 0),
                    NVL(SUM(credit_amount), 0),
                    COUNT(*),
                    MAX(document_date),
                    MAX(document_type_code) KEEP (DENSE_RANK LAST ORDER BY document_date, id),
                    MAX(document_number) KEEP (DENSE_RANK LAST ORDER BY document_date, id)
                INTO
                    v_total_debit,
                    v_total_credit,
                    v_transaction_count,
                    v_last_transaction_date,
                    v_last_document_type,
                    v_last_document_number
                FROM BALANCE_TRANSACTIONS
                WHERE entity_type_code = balance_rec.entity_type_code
                AND entity_id = balance_rec.entity_id
                AND currency_code = balance_rec.currency_code
                AND status = 'POSTED';

                -- حساب الرصيد الجاري
                v_current_balance := v_opening_balance + v_total_debit - v_total_credit;

                -- تحديث أو إدراج الرصيد الجاري
                MERGE INTO CURRENT_BALANCES cb
                USING (
                    SELECT
                        balance_rec.entity_type_code as entity_type_code,
                        balance_rec.entity_id as entity_id,
                        balance_rec.currency_code as currency_code,
                        v_opening_balance as opening_balance,
                        v_total_debit as debit_amount,
                        v_total_credit as credit_amount,
                        v_current_balance as current_balance,
                        v_transaction_count as total_transactions_count,
                        v_last_transaction_date as last_transaction_date,
                        v_last_document_type as last_document_type,
                        v_last_document_number as last_document_number,
                        p_calculated_by as updated_by
                    FROM DUAL
                ) src ON (
                    cb.entity_type_code = src.entity_type_code
                    AND cb.entity_id = src.entity_id
                    AND cb.currency_code = src.currency_code
                )
                WHEN MATCHED THEN
                    UPDATE SET
                        debit_amount = src.debit_amount,
                        credit_amount = src.credit_amount,
                        current_balance = src.current_balance,
                        total_transactions_count = src.total_transactions_count,
                        last_transaction_date = src.last_transaction_date,
                        last_document_type = src.last_document_type,
                        last_document_number = src.last_document_number,
                        updated_at = CURRENT_TIMESTAMP,
                        updated_by = src.updated_by
                WHEN NOT MATCHED THEN
                    INSERT (
                        entity_type_code, entity_id, currency_code,
                        opening_balance, debit_amount, credit_amount, current_balance,
                        total_transactions_count, last_transaction_date,
                        last_document_type, last_document_number,
                        created_at, updated_at, created_by, updated_by
                    )
                    VALUES (
                        src.entity_type_code, src.entity_id, src.currency_code,
                        src.opening_balance, src.debit_amount, src.credit_amount, src.current_balance,
                        src.total_transactions_count, src.last_transaction_date,
                        src.last_document_type, src.last_document_number,
                        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, src.updated_by, src.updated_by
                    );

                v_processed_count := v_processed_count + 1;

                -- Commit كل 100 سجل لتجنب استهلاك الذاكرة
                IF MOD(v_processed_count, 100) = 0 THEN
                    COMMIT;
                END IF;

            END LOOP;

            COMMIT;
            p_result := 'SUCCESS: تم إعادة حساب ' || v_processed_count || ' رصيد بنجاح';

        EXCEPTION
            WHEN OTHERS THEN
                ROLLBACK;
                v_error_msg := SQLERRM;
                p_result := 'ERROR: ' || v_error_msg;
        END CALCULATE_BALANCES;
        """

        oracle_mgr.execute_update(procedure_sql)
        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info("✅ تم إنشاء إجراء CALCULATE_BALANCES بنجاح")
        return jsonify({
            'success': True,
            'message': 'تم إنشاء إجراء حساب الأرصدة بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء إجراء CALCULATE_BALANCES: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/test/procedures', methods=['POST'])
def test_procedures():
    """اختبار شامل لجميع الإجراءات المخزنة"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        test_results = []

        # اختبار 1: إضافة رصيد افتتاحي تجريبي
        logger.info("🧪 اختبار 1: إضافة رصيد افتتاحي تجريبي")
        try:
            # إدراج رصيد افتتاحي تجريبي
            insert_opening_sql = """
            INSERT INTO OPENING_BALANCES (
                entity_type_code, entity_id, fiscal_year, fiscal_period_start_date,
                currency_code, opening_balance_amount, balance_type, status,
                document_type_code, document_number, created_by
            ) VALUES (
                'SUPPLIER', 999, 2024, DATE '2024-01-01',
                'SAR', 10000, 'CREDIT', 'POSTED',
                'OPENING_BALANCE', 'TEST-OB-001', 1
            )
            """
            oracle_mgr.execute_update(insert_opening_sql)
            test_results.append("✅ تم إدراج رصيد افتتاحي تجريبي")
        except Exception as e:
            test_results.append(f"❌ خطأ في إدراج الرصيد الافتتاحي: {e}")

        # اختبار 2: ترحيل الأرصدة الافتتاحية
        logger.info("🧪 اختبار 2: ترحيل الأرصدة الافتتاحية")
        try:
            call_sql = """
            DECLARE
                v_result VARCHAR2(4000);
            BEGIN
                POST_OPENING_BALANCES(
                    p_entity_type_code => 'SUPPLIER',
                    p_fiscal_date => DATE '2024-01-01',
                    p_currency_code => 'SAR',
                    p_posted_by => 1,
                    p_posting_reference => 'TEST',
                    p_result => v_result
                );
                :1 := v_result;
            END;
            """

            cursor = oracle_mgr.connection.cursor()
            result_var = cursor.var(str)
            cursor.execute(call_sql, [result_var])
            result = result_var.getvalue()
            cursor.close()

            if result.startswith('SUCCESS:'):
                test_results.append(f"✅ ترحيل الأرصدة الافتتاحية: {result}")
            else:
                test_results.append(f"❌ ترحيل الأرصدة الافتتاحية: {result}")

        except Exception as e:
            test_results.append(f"❌ خطأ في ترحيل الأرصدة الافتتاحية: {e}")

        # اختبار 3: إضافة معاملة تجريبية
        logger.info("🧪 اختبار 3: إضافة معاملة تجريبية")
        try:
            call_sql = """
            DECLARE
                v_result VARCHAR2(4000);
                v_transaction_id NUMBER;
            BEGIN
                ADD_TRANSACTION(
                    p_entity_type_code => 'SUPPLIER',
                    p_entity_id => 999,
                    p_document_type_code => 'PURCHASE_INVOICE',
                    p_document_number => 'TEST-PI-001',
                    p_document_date => SYSDATE,
                    p_currency_code => 'SAR',
                    p_debit_amount => 0,
                    p_credit_amount => 5000,
                    p_exchange_rate => 1,
                    p_description => 'فاتورة شراء تجريبية',
                    p_reference_number => 'TEST-REF-001',
                    p_created_by => 1,
                    p_result => v_result,
                    p_transaction_id => v_transaction_id
                );
                :1 := v_result;
                :2 := v_transaction_id;
            END;
            """

            cursor = oracle_mgr.connection.cursor()
            result_var = cursor.var(str)
            transaction_id_var = cursor.var(int)
            cursor.execute(call_sql, [result_var, transaction_id_var])
            result = result_var.getvalue()
            transaction_id = transaction_id_var.getvalue()
            cursor.close()

            if result.startswith('SUCCESS:'):
                test_results.append(f"✅ إضافة معاملة: {result}")
                test_transaction_id = transaction_id
            else:
                test_results.append(f"❌ إضافة معاملة: {result}")
                test_transaction_id = None

        except Exception as e:
            test_results.append(f"❌ خطأ في إضافة المعاملة: {e}")
            test_transaction_id = None

        # اختبار 4: التحقق من الرصيد الجاري
        logger.info("🧪 اختبار 4: التحقق من الرصيد الجاري")
        try:
            check_balance_sql = """
            SELECT current_balance, debit_amount, credit_amount, total_transactions_count
            FROM CURRENT_BALANCES
            WHERE entity_type_code = 'SUPPLIER'
            AND entity_id = 999
            AND currency_code = 'SAR'
            """

            balance_result = oracle_mgr.execute_query(check_balance_sql)
            if balance_result:
                current_balance, debit_amount, credit_amount, transaction_count = balance_result[0]
                test_results.append(f"✅ الرصيد الجاري: {current_balance} SAR (مدين: {debit_amount}, دائن: {credit_amount}, المعاملات: {transaction_count})")
            else:
                test_results.append("❌ لم يتم العثور على رصيد جاري")

        except Exception as e:
            test_results.append(f"❌ خطأ في التحقق من الرصيد الجاري: {e}")

        # اختبار 5: عكس المعاملة (إذا تم إنشاؤها)
        if test_transaction_id:
            logger.info("🧪 اختبار 5: عكس المعاملة")
            try:
                call_sql = """
                DECLARE
                    v_result VARCHAR2(4000);
                BEGIN
                    REVERSE_TRANSACTION(
                        p_transaction_id => :1,
                        p_reversal_reason => 'اختبار عكس المعاملة',
                        p_reversed_by => 1,
                        p_result => v_result
                    );
                    :2 := v_result;
                END;
                """

                cursor = oracle_mgr.connection.cursor()
                result_var = cursor.var(str)
                cursor.execute(call_sql, [test_transaction_id, result_var])
                result = result_var.getvalue()
                cursor.close()

                if result.startswith('SUCCESS:'):
                    test_results.append(f"✅ عكس المعاملة: {result}")
                else:
                    test_results.append(f"❌ عكس المعاملة: {result}")

            except Exception as e:
                test_results.append(f"❌ خطأ في عكس المعاملة: {e}")

        # اختبار 6: إعادة حساب الأرصدة
        logger.info("🧪 اختبار 6: إعادة حساب الأرصدة")
        try:
            call_sql = """
            DECLARE
                v_result VARCHAR2(4000);
            BEGIN
                CALCULATE_BALANCES(
                    p_entity_type_code => 'SUPPLIER',
                    p_entity_id => 999,
                    p_currency_code => 'SAR',
                    p_calculated_by => 1,
                    p_result => v_result
                );
                :1 := v_result;
            END;
            """

            cursor = oracle_mgr.connection.cursor()
            result_var = cursor.var(str)
            cursor.execute(call_sql, [result_var])
            result = result_var.getvalue()
            cursor.close()

            if result.startswith('SUCCESS:'):
                test_results.append(f"✅ إعادة حساب الأرصدة: {result}")
            else:
                test_results.append(f"❌ إعادة حساب الأرصدة: {result}")

        except Exception as e:
            test_results.append(f"❌ خطأ في إعادة حساب الأرصدة: {e}")

        # تنظيف البيانات التجريبية
        logger.info("🧹 تنظيف البيانات التجريبية")
        try:
            cleanup_sql = [
                "DELETE FROM BALANCE_TRANSACTIONS WHERE entity_type_code = 'SUPPLIER' AND entity_id = 999",
                "DELETE FROM CURRENT_BALANCES WHERE entity_type_code = 'SUPPLIER' AND entity_id = 999",
                "DELETE FROM OPENING_BALANCES WHERE entity_type_code = 'SUPPLIER' AND entity_id = 999"
            ]

            for sql in cleanup_sql:
                oracle_mgr.execute_update(sql)

            test_results.append("✅ تم تنظيف البيانات التجريبية")

        except Exception as e:
            test_results.append(f"❌ خطأ في تنظيف البيانات التجريبية: {e}")

        oracle_mgr.commit()
        oracle_mgr.disconnect()

        # تحليل النتائج
        success_count = len([r for r in test_results if r.startswith('✅')])
        total_tests = len(test_results)

        logger.info(f"✅ اكتمل اختبار الإجراءات المخزنة: {success_count}/{total_tests} نجح")

        return jsonify({
            'success': True,
            'message': f'اكتمل اختبار الإجراءات المخزنة: {success_count}/{total_tests} نجح',
            'test_results': test_results,
            'success_count': success_count,
            'total_tests': total_tests
        })

    except Exception as e:
        logger.error(f"❌ خطأ في اختبار الإجراءات المخزنة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/setup/create_balance_views', methods=['POST'])
def create_balance_views():
    """إنشاء Views للأرصدة الشاملة"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        views_created = []

        # View 1: الأرصدة الجارية الشاملة
        view1_sql = """
        CREATE OR REPLACE VIEW V_CURRENT_BALANCES_DETAILED AS
        SELECT
            cb.id,
            cb.entity_type_code,
            et.entity_name_ar,
            et.entity_name_en,
            et.module_name,
            cb.entity_id,
            -- اسم الكيان حسب النوع (سيتم تحديثه لاحقاً بـ CASE)
            CASE
                WHEN cb.entity_type_code = 'SUPPLIER' THEN
                    (SELECT name FROM SUPPLIERS WHERE id = cb.entity_id AND ROWNUM = 1)
                WHEN cb.entity_type_code = 'CUSTOMER' THEN
                    (SELECT name FROM CUSTOMERS WHERE id = cb.entity_id AND ROWNUM = 1)
                ELSE 'غير محدد'
            END as entity_name,
            cb.account_code,
            cb.currency_code,
            cb.opening_balance,
            cb.debit_amount,
            cb.credit_amount,
            cb.current_balance,
            -- تصنيف الرصيد
            CASE
                WHEN cb.current_balance > 0 THEN 'مدين'
                WHEN cb.current_balance < 0 THEN 'دائن'
                ELSE 'متوازن'
            END as balance_status,
            ABS(cb.current_balance) as balance_amount,
            cb.total_transactions_count,
            cb.last_transaction_date,
            cb.last_document_type,
            dt.document_name_ar as last_document_name_ar,
            cb.last_document_number,
            cb.average_days,
            cb.created_at,
            cb.updated_at,
            cb.created_by,
            cb.updated_by,
            -- معلومات إضافية
            et.has_balances,
            et.has_transactions,
            et.default_currency,
            et.account_prefix
        FROM CURRENT_BALANCES cb
        JOIN ENTITY_TYPES et ON cb.entity_type_code = et.entity_type_code
        LEFT JOIN DOCUMENT_TYPES dt ON cb.last_document_type = dt.document_type_code
        WHERE et.is_active = 1
        """

        oracle_mgr.execute_update(view1_sql)
        views_created.append("V_CURRENT_BALANCES_DETAILED")

        # View 2: الأرصدة الافتتاحية الشاملة
        view2_sql = """
        CREATE OR REPLACE VIEW V_OPENING_BALANCES_DETAILED AS
        SELECT
            ob.id,
            ob.entity_type_code,
            et.entity_name_ar,
            et.entity_name_en,
            et.module_name,
            ob.entity_id,
            -- اسم الكيان حسب النوع
            CASE
                WHEN ob.entity_type_code = 'SUPPLIER' THEN
                    (SELECT name FROM SUPPLIERS WHERE id = ob.entity_id AND ROWNUM = 1)
                WHEN ob.entity_type_code = 'CUSTOMER' THEN
                    (SELECT name FROM CUSTOMERS WHERE id = ob.entity_id AND ROWNUM = 1)
                ELSE 'غير محدد'
            END as entity_name,
            ob.account_code,
            ob.fiscal_year,
            ob.fiscal_period_start_date,
            ob.currency_code,
            ob.opening_balance_amount,
            ob.balance_type,
            ob.exchange_rate,
            ob.base_currency_amount,
            ob.document_type_code,
            dt.document_name_ar as document_name_ar,
            ob.document_number,
            ob.status,
            -- ترجمة الحالة
            CASE ob.status
                WHEN 'DRAFT' THEN 'مسودة'
                WHEN 'POSTED' THEN 'مرحل'
                WHEN 'CANCELLED' THEN 'ملغي'
                ELSE ob.status
            END as status_ar,
            ob.notes,
            ob.reference_document,
            ob.created_date,
            ob.created_by,
            ob.updated_date,
            ob.updated_by,
            ob.approved_date,
            ob.approved_by,
            ob.posted_date,
            ob.posted_by,
            ob.is_active
        FROM OPENING_BALANCES ob
        JOIN ENTITY_TYPES et ON ob.entity_type_code = et.entity_type_code
        LEFT JOIN DOCUMENT_TYPES dt ON ob.document_type_code = dt.document_type_code
        WHERE et.is_active = 1
        """

        oracle_mgr.execute_update(view2_sql)
        views_created.append("V_OPENING_BALANCES_DETAILED")

        # View 3: ملخص الأرصدة حسب نوع الكيان
        view3_sql = """
        CREATE OR REPLACE VIEW V_BALANCES_SUMMARY_BY_TYPE AS
        SELECT
            cb.entity_type_code,
            et.entity_name_ar,
            et.entity_name_en,
            et.module_name,
            cb.currency_code,
            COUNT(*) as entities_count,
            SUM(cb.opening_balance) as total_opening_balance,
            SUM(cb.debit_amount) as total_debit_amount,
            SUM(cb.credit_amount) as total_credit_amount,
            SUM(cb.current_balance) as total_current_balance,
            SUM(CASE WHEN cb.current_balance > 0 THEN cb.current_balance ELSE 0 END) as total_debit_balance,
            SUM(CASE WHEN cb.current_balance < 0 THEN ABS(cb.current_balance) ELSE 0 END) as total_credit_balance,
            COUNT(CASE WHEN cb.current_balance > 0 THEN 1 END) as debit_entities_count,
            COUNT(CASE WHEN cb.current_balance < 0 THEN 1 END) as credit_entities_count,
            COUNT(CASE WHEN cb.current_balance = 0 THEN 1 END) as zero_balance_entities_count,
            SUM(cb.total_transactions_count) as total_transactions,
            MAX(cb.last_transaction_date) as latest_transaction_date,
            AVG(cb.average_days) as avg_days
        FROM CURRENT_BALANCES cb
        JOIN ENTITY_TYPES et ON cb.entity_type_code = et.entity_type_code
        WHERE et.is_active = 1
        GROUP BY cb.entity_type_code, et.entity_name_ar, et.entity_name_en, et.module_name, cb.currency_code
        """

        oracle_mgr.execute_update(view3_sql)
        views_created.append("V_BALANCES_SUMMARY_BY_TYPE")

        # View 4: الأرصدة النشطة فقط (غير الصفرية)
        view4_sql = """
        CREATE OR REPLACE VIEW V_ACTIVE_BALANCES AS
        SELECT
            cb.*,
            et.entity_name_ar,
            et.entity_name_en,
            et.module_name,
            -- اسم الكيان
            CASE
                WHEN cb.entity_type_code = 'SUPPLIER' THEN
                    (SELECT name FROM SUPPLIERS WHERE id = cb.entity_id AND ROWNUM = 1)
                WHEN cb.entity_type_code = 'CUSTOMER' THEN
                    (SELECT name FROM CUSTOMERS WHERE id = cb.entity_id AND ROWNUM = 1)
                ELSE 'غير محدد'
            END as entity_name,
            -- تصنيف الرصيد
            CASE
                WHEN cb.current_balance > 0 THEN 'مدين'
                WHEN cb.current_balance < 0 THEN 'دائن'
            END as balance_status,
            ABS(cb.current_balance) as balance_amount
        FROM CURRENT_BALANCES cb
        JOIN ENTITY_TYPES et ON cb.entity_type_code = et.entity_type_code
        WHERE et.is_active = 1
        AND cb.current_balance != 0
        """

        oracle_mgr.execute_update(view4_sql)
        views_created.append("V_ACTIVE_BALANCES")

        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info(f"✅ تم إنشاء {len(views_created)} Views للأرصدة بنجاح")
        return jsonify({
            'success': True,
            'message': f'تم إنشاء {len(views_created)} Views للأرصدة بنجاح',
            'views_created': views_created
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء Views للأرصدة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/setup/create_transaction_views', methods=['POST'])
def create_transaction_views():
    """إنشاء Views للمعاملات التفصيلية"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        views_created = []

        # View 1: المعاملات التفصيلية الشاملة
        view1_sql = """
        CREATE OR REPLACE VIEW V_BALANCE_TRANSACTIONS_DETAILED AS
        SELECT
            bt.id,
            bt.entity_type_code,
            et.entity_name_ar as entity_type_name_ar,
            et.entity_name_en as entity_type_name_en,
            et.module_name,
            bt.entity_id,
            -- اسم الكيان حسب النوع
            CASE
                WHEN bt.entity_type_code = 'SUPPLIER' THEN
                    (SELECT name FROM SUPPLIERS WHERE id = bt.entity_id AND ROWNUM = 1)
                WHEN bt.entity_type_code = 'CUSTOMER' THEN
                    (SELECT name FROM CUSTOMERS WHERE id = bt.entity_id AND ROWNUM = 1)
                ELSE 'غير محدد'
            END as entity_name,
            bt.document_type_code,
            dt.document_name_ar,
            dt.document_name_en,
            dt.document_category,
            dt.affects_balance,
            dt.balance_effect,
            bt.document_number,
            bt.document_date,
            bt.currency_code,
            bt.debit_amount,
            bt.credit_amount,
            -- صافي المبلغ
            (bt.debit_amount - bt.credit_amount) as net_amount,
            bt.exchange_rate,
            bt.base_currency_debit,
            bt.base_currency_credit,
            -- صافي المبلغ بالعملة الأساسية
            (bt.base_currency_debit - bt.base_currency_credit) as base_net_amount,
            bt.description,
            bt.reference_number,
            bt.status,
            -- ترجمة الحالة
            CASE bt.status
                WHEN 'POSTED' THEN 'مرحل'
                WHEN 'REVERSED' THEN 'معكوس'
                WHEN 'CANCELLED' THEN 'ملغي'
                WHEN 'DRAFT' THEN 'مسودة'
                ELSE bt.status
            END as status_ar,
            bt.created_date,
            bt.created_by,
            bt.updated_date,
            bt.updated_by,
            -- معلومات إضافية
            CASE
                WHEN bt.debit_amount > 0 THEN 'مدين'
                WHEN bt.credit_amount > 0 THEN 'دائن'
                ELSE 'متوازن'
            END as transaction_type,
            -- عمر المعاملة بالأيام
            TRUNC(SYSDATE - bt.document_date) as transaction_age_days,
            -- الشهر والسنة
            TO_CHAR(bt.document_date, 'YYYY-MM') as year_month,
            TO_CHAR(bt.document_date, 'YYYY') as year,
            TO_CHAR(bt.document_date, 'MM') as month,
            TO_CHAR(bt.document_date, 'Q') as quarter
        FROM BALANCE_TRANSACTIONS bt
        JOIN ENTITY_TYPES et ON bt.entity_type_code = et.entity_type_code
        JOIN DOCUMENT_TYPES dt ON bt.document_type_code = dt.document_type_code
        WHERE et.is_active = 1 AND dt.is_active = 1
        """

        oracle_mgr.execute_update(view1_sql)
        views_created.append("V_BALANCE_TRANSACTIONS_DETAILED")

        # View 2: ملخص المعاملات حسب نوع الوثيقة
        view2_sql = """
        CREATE OR REPLACE VIEW V_TRANSACTIONS_SUMMARY_BY_DOCTYPE AS
        SELECT
            bt.document_type_code,
            dt.document_name_ar,
            dt.document_name_en,
            dt.document_category,
            dt.module_name,
            bt.currency_code,
            COUNT(*) as transactions_count,
            SUM(bt.debit_amount) as total_debit_amount,
            SUM(bt.credit_amount) as total_credit_amount,
            SUM(bt.debit_amount - bt.credit_amount) as net_amount,
            SUM(bt.base_currency_debit) as total_base_debit,
            SUM(bt.base_currency_credit) as total_base_credit,
            SUM(bt.base_currency_debit - bt.base_currency_credit) as base_net_amount,
            MIN(bt.document_date) as earliest_transaction,
            MAX(bt.document_date) as latest_transaction,
            COUNT(DISTINCT bt.entity_id) as unique_entities_count,
            AVG(bt.debit_amount + bt.credit_amount) as avg_transaction_amount
        FROM BALANCE_TRANSACTIONS bt
        JOIN DOCUMENT_TYPES dt ON bt.document_type_code = dt.document_type_code
        WHERE bt.status = 'POSTED' AND dt.is_active = 1
        GROUP BY bt.document_type_code, dt.document_name_ar, dt.document_name_en,
                 dt.document_category, dt.module_name, bt.currency_code
        """

        oracle_mgr.execute_update(view2_sql)
        views_created.append("V_TRANSACTIONS_SUMMARY_BY_DOCTYPE")

        # View 3: المعاملات الشهرية
        view3_sql = """
        CREATE OR REPLACE VIEW V_MONTHLY_TRANSACTIONS_SUMMARY AS
        SELECT
            TO_CHAR(bt.document_date, 'YYYY-MM') as year_month,
            TO_CHAR(bt.document_date, 'YYYY') as year,
            TO_CHAR(bt.document_date, 'MM') as month,
            TO_CHAR(bt.document_date, 'Month YYYY', 'NLS_DATE_LANGUAGE=ARABIC') as month_name_ar,
            bt.entity_type_code,
            et.entity_name_ar as entity_type_name_ar,
            bt.currency_code,
            COUNT(*) as transactions_count,
            SUM(bt.debit_amount) as total_debit_amount,
            SUM(bt.credit_amount) as total_credit_amount,
            SUM(bt.debit_amount - bt.credit_amount) as net_amount,
            COUNT(DISTINCT bt.entity_id) as unique_entities_count,
            COUNT(DISTINCT bt.document_type_code) as unique_document_types_count,
            AVG(bt.debit_amount + bt.credit_amount) as avg_transaction_amount,
            MIN(bt.document_date) as first_transaction_date,
            MAX(bt.document_date) as last_transaction_date
        FROM BALANCE_TRANSACTIONS bt
        JOIN ENTITY_TYPES et ON bt.entity_type_code = et.entity_type_code
        WHERE bt.status = 'POSTED' AND et.is_active = 1
        GROUP BY TO_CHAR(bt.document_date, 'YYYY-MM'), TO_CHAR(bt.document_date, 'YYYY'),
                 TO_CHAR(bt.document_date, 'MM'), TO_CHAR(bt.document_date, 'Month YYYY', 'NLS_DATE_LANGUAGE=ARABIC'),
                 bt.entity_type_code, et.entity_name_ar, bt.currency_code
        ORDER BY year_month DESC
        """

        oracle_mgr.execute_update(view3_sql)
        views_created.append("V_MONTHLY_TRANSACTIONS_SUMMARY")

        # View 4: المعاملات المعكوسة
        view4_sql = """
        CREATE OR REPLACE VIEW V_REVERSED_TRANSACTIONS AS
        SELECT
            original.id as original_transaction_id,
            original.document_number as original_document_number,
            original.document_date as original_document_date,
            original.debit_amount as original_debit_amount,
            original.credit_amount as original_credit_amount,
            original.description as original_description,
            reversal.id as reversal_transaction_id,
            reversal.document_number as reversal_document_number,
            reversal.document_date as reversal_document_date,
            reversal.debit_amount as reversal_debit_amount,
            reversal.credit_amount as reversal_credit_amount,
            reversal.description as reversal_description,
            original.entity_type_code,
            et.entity_name_ar,
            original.entity_id,
            -- اسم الكيان
            CASE
                WHEN original.entity_type_code = 'SUPPLIER' THEN
                    (SELECT name FROM SUPPLIERS WHERE id = original.entity_id AND ROWNUM = 1)
                WHEN original.entity_type_code = 'CUSTOMER' THEN
                    (SELECT name FROM CUSTOMERS WHERE id = original.entity_id AND ROWNUM = 1)
                ELSE 'غير محدد'
            END as entity_name,
            original.document_type_code,
            dt.document_name_ar,
            original.currency_code,
            original.created_by as original_created_by,
            reversal.created_by as reversal_created_by,
            TRUNC(reversal.document_date - original.document_date) as reversal_delay_days
        FROM BALANCE_TRANSACTIONS original
        JOIN BALANCE_TRANSACTIONS reversal ON reversal.document_number = 'REV-' || original.document_number
                                           AND reversal.entity_type_code = original.entity_type_code
                                           AND reversal.entity_id = original.entity_id
                                           AND reversal.document_type_code = original.document_type_code
        JOIN ENTITY_TYPES et ON original.entity_type_code = et.entity_type_code
        JOIN DOCUMENT_TYPES dt ON original.document_type_code = dt.document_type_code
        WHERE original.status = 'REVERSED'
        AND reversal.status = 'POSTED'
        AND et.is_active = 1
        AND dt.is_active = 1
        """

        oracle_mgr.execute_update(view4_sql)
        views_created.append("V_REVERSED_TRANSACTIONS")

        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info(f"✅ تم إنشاء {len(views_created)} Views للمعاملات بنجاح")
        return jsonify({
            'success': True,
            'message': f'تم إنشاء {len(views_created)} Views للمعاملات بنجاح',
            'views_created': views_created
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء Views للمعاملات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/setup/create_report_views', methods=['POST'])
def create_report_views():
    """إنشاء Views للتقارير والتحليلات"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        views_created = []

        # View 1: ميزان المراجعة الشامل
        view1_sql = """
        CREATE OR REPLACE VIEW V_TRIAL_BALANCE AS
        SELECT
            cb.entity_type_code,
            et.entity_name_ar as entity_type_name_ar,
            et.entity_name_en as entity_type_name_en,
            et.module_name,
            cb.entity_id,
            -- اسم الكيان
            CASE
                WHEN cb.entity_type_code = 'SUPPLIER' THEN
                    (SELECT name FROM SUPPLIERS WHERE id = cb.entity_id AND ROWNUM = 1)
                WHEN cb.entity_type_code = 'CUSTOMER' THEN
                    (SELECT name FROM CUSTOMERS WHERE id = cb.entity_id AND ROWNUM = 1)
                ELSE 'غير محدد'
            END as entity_name,
            cb.currency_code,
            cb.opening_balance,
            cb.debit_amount,
            cb.credit_amount,
            cb.current_balance,
            -- تصنيف الرصيد للميزان
            CASE
                WHEN cb.current_balance >= 0 THEN cb.current_balance
                ELSE 0
            END as debit_balance,
            CASE
                WHEN cb.current_balance < 0 THEN ABS(cb.current_balance)
                ELSE 0
            END as credit_balance,
            cb.total_transactions_count,
            cb.last_transaction_date,
            -- حالة النشاط
            CASE
                WHEN cb.current_balance = 0 AND cb.total_transactions_count = 0 THEN 'غير نشط'
                WHEN cb.current_balance = 0 AND cb.total_transactions_count > 0 THEN 'متوازن'
                WHEN ABS(cb.current_balance) < 100 THEN 'رصيد منخفض'
                ELSE 'نشط'
            END as activity_status
        FROM CURRENT_BALANCES cb
        JOIN ENTITY_TYPES et ON cb.entity_type_code = et.entity_type_code
        WHERE et.is_active = 1
        ORDER BY cb.entity_type_code, cb.entity_id, cb.currency_code
        """

        oracle_mgr.execute_update(view1_sql)
        views_created.append("V_TRIAL_BALANCE")

        # View 2: تقرير أعمار الديون
        view2_sql = """
        CREATE OR REPLACE VIEW V_AGING_REPORT AS
        SELECT
            cb.entity_type_code,
            et.entity_name_ar as entity_type_name_ar,
            cb.entity_id,
            -- اسم الكيان
            CASE
                WHEN cb.entity_type_code = 'SUPPLIER' THEN
                    (SELECT name FROM SUPPLIERS WHERE id = cb.entity_id AND ROWNUM = 1)
                WHEN cb.entity_type_code = 'CUSTOMER' THEN
                    (SELECT name FROM CUSTOMERS WHERE id = cb.entity_id AND ROWNUM = 1)
                ELSE 'غير محدد'
            END as entity_name,
            cb.currency_code,
            cb.current_balance,
            cb.last_transaction_date,
            -- عمر آخر معاملة
            TRUNC(SYSDATE - NVL(cb.last_transaction_date, SYSDATE)) as days_since_last_transaction,
            -- تصنيف العمر
            CASE
                WHEN cb.last_transaction_date IS NULL THEN 'لا توجد معاملات'
                WHEN TRUNC(SYSDATE - cb.last_transaction_date) <= 30 THEN 'حديث (0-30 يوم)'
                WHEN TRUNC(SYSDATE - cb.last_transaction_date) <= 60 THEN 'متوسط (31-60 يوم)'
                WHEN TRUNC(SYSDATE - cb.last_transaction_date) <= 90 THEN 'قديم (61-90 يوم)'
                WHEN TRUNC(SYSDATE - cb.last_transaction_date) <= 180 THEN 'قديم جداً (91-180 يوم)'
                ELSE 'متقادم (+180 يوم)'
            END as aging_category,
            -- تصنيف المخاطر
            CASE
                WHEN cb.current_balance = 0 THEN 'لا يوجد رصيد'
                WHEN ABS(cb.current_balance) < 1000 AND TRUNC(SYSDATE - NVL(cb.last_transaction_date, SYSDATE)) <= 30 THEN 'منخفض'
                WHEN ABS(cb.current_balance) < 5000 AND TRUNC(SYSDATE - NVL(cb.last_transaction_date, SYSDATE)) <= 60 THEN 'متوسط'
                WHEN ABS(cb.current_balance) >= 5000 OR TRUNC(SYSDATE - NVL(cb.last_transaction_date, SYSDATE)) > 90 THEN 'عالي'
                ELSE 'متوسط'
            END as risk_level,
            cb.total_transactions_count,
            cb.average_days
        FROM CURRENT_BALANCES cb
        JOIN ENTITY_TYPES et ON cb.entity_type_code = et.entity_type_code
        WHERE et.is_active = 1
        AND cb.current_balance != 0
        ORDER BY days_since_last_transaction DESC, ABS(cb.current_balance) DESC
        """

        oracle_mgr.execute_update(view2_sql)
        views_created.append("V_AGING_REPORT")

        # View 3: تحليل التدفقات النقدية
        view3_sql = """
        CREATE OR REPLACE VIEW V_CASH_FLOW_ANALYSIS AS
        SELECT
            TO_CHAR(bt.document_date, 'YYYY-MM') as year_month,
            TO_CHAR(bt.document_date, 'YYYY') as year,
            TO_CHAR(bt.document_date, 'MM') as month,
            TO_CHAR(bt.document_date, 'Q') as quarter,
            bt.currency_code,
            dt.document_category,
            -- تصنيف التدفق النقدي
            CASE dt.document_category
                WHEN 'RECEIPTS' THEN 'تدفق داخل'
                WHEN 'PAYMENTS' THEN 'تدفق خارج'
                WHEN 'SALES' THEN 'تدفق داخل'
                WHEN 'PROCUREMENT' THEN 'تدفق خارج'
                WHEN 'TRANSFERS' THEN 'تحويل'
                ELSE 'أخرى'
            END as cash_flow_type,
            COUNT(*) as transactions_count,
            SUM(bt.debit_amount) as total_debit,
            SUM(bt.credit_amount) as total_credit,
            SUM(bt.debit_amount - bt.credit_amount) as net_flow,
            -- التدفق الداخل والخارج
            SUM(CASE WHEN dt.document_category IN ('RECEIPTS', 'SALES') THEN bt.debit_amount + bt.credit_amount ELSE 0 END) as cash_inflow,
            SUM(CASE WHEN dt.document_category IN ('PAYMENTS', 'PROCUREMENT') THEN bt.debit_amount + bt.credit_amount ELSE 0 END) as cash_outflow,
            COUNT(DISTINCT bt.entity_id) as unique_entities,
            AVG(bt.debit_amount + bt.credit_amount) as avg_transaction_amount
        FROM BALANCE_TRANSACTIONS bt
        JOIN DOCUMENT_TYPES dt ON bt.document_type_code = dt.document_type_code
        WHERE bt.status = 'POSTED'
        AND dt.is_active = 1
        AND bt.document_date >= ADD_MONTHS(SYSDATE, -24) -- آخر سنتين
        GROUP BY TO_CHAR(bt.document_date, 'YYYY-MM'), TO_CHAR(bt.document_date, 'YYYY'),
                 TO_CHAR(bt.document_date, 'MM'), TO_CHAR(bt.document_date, 'Q'),
                 bt.currency_code, dt.document_category
        ORDER BY year_month DESC, cash_flow_type
        """

        oracle_mgr.execute_update(view3_sql)
        views_created.append("V_CASH_FLOW_ANALYSIS")

        # View 4: تقرير أداء الكيانات
        view4_sql = """
        CREATE OR REPLACE VIEW V_ENTITY_PERFORMANCE_REPORT AS
        SELECT
            cb.entity_type_code,
            et.entity_name_ar as entity_type_name_ar,
            cb.entity_id,
            -- اسم الكيان
            CASE
                WHEN cb.entity_type_code = 'SUPPLIER' THEN
                    (SELECT name FROM SUPPLIERS WHERE id = cb.entity_id AND ROWNUM = 1)
                WHEN cb.entity_type_code = 'CUSTOMER' THEN
                    (SELECT name FROM CUSTOMERS WHERE id = cb.entity_id AND ROWNUM = 1)
                ELSE 'غير محدد'
            END as entity_name,
            cb.currency_code,
            cb.current_balance,
            cb.total_transactions_count,
            cb.debit_amount as total_debit,
            cb.credit_amount as total_credit,
            -- حجم النشاط
            (cb.debit_amount + cb.credit_amount) as total_activity_volume,
            -- متوسط المعاملة
            CASE WHEN cb.total_transactions_count > 0
                 THEN (cb.debit_amount + cb.credit_amount) / cb.total_transactions_count
                 ELSE 0 END as avg_transaction_amount,
            cb.last_transaction_date,
            TRUNC(SYSDATE - NVL(cb.last_transaction_date, SYSDATE)) as days_since_last_activity,
            -- تصنيف الأداء
            CASE
                WHEN cb.total_transactions_count = 0 THEN 'غير نشط'
                WHEN cb.total_transactions_count < 5 THEN 'نشاط منخفض'
                WHEN cb.total_transactions_count < 20 THEN 'نشاط متوسط'
                ELSE 'نشاط عالي'
            END as activity_level,
            -- تصنيف حجم المعاملات
            CASE
                WHEN (cb.debit_amount + cb.credit_amount) = 0 THEN 'لا يوجد'
                WHEN (cb.debit_amount + cb.credit_amount) < 10000 THEN 'صغير'
                WHEN (cb.debit_amount + cb.credit_amount) < 100000 THEN 'متوسط'
                ELSE 'كبير'
            END as volume_category,
            -- نسبة المدين إلى الدائن
            CASE WHEN cb.credit_amount > 0
                 THEN ROUND((cb.debit_amount / cb.credit_amount) * 100, 2)
                 ELSE NULL END as debit_credit_ratio_percent
        FROM CURRENT_BALANCES cb
        JOIN ENTITY_TYPES et ON cb.entity_type_code = et.entity_type_code
        WHERE et.is_active = 1
        ORDER BY total_activity_volume DESC, cb.total_transactions_count DESC
        """

        oracle_mgr.execute_update(view4_sql)
        views_created.append("V_ENTITY_PERFORMANCE_REPORT")

        # View 5: ملخص إحصائي شامل
        view5_sql = """
        CREATE OR REPLACE VIEW V_STATISTICAL_SUMMARY AS
        SELECT
            'CURRENT_BALANCES' as report_type,
            'الأرصدة الجارية' as report_name_ar,
            cb.entity_type_code,
            et.entity_name_ar as entity_type_name_ar,
            cb.currency_code,
            COUNT(*) as records_count,
            SUM(cb.current_balance) as total_balance,
            AVG(cb.current_balance) as avg_balance,
            MIN(cb.current_balance) as min_balance,
            MAX(cb.current_balance) as max_balance,
            STDDEV(cb.current_balance) as balance_std_deviation,
            COUNT(CASE WHEN cb.current_balance > 0 THEN 1 END) as positive_balances_count,
            COUNT(CASE WHEN cb.current_balance < 0 THEN 1 END) as negative_balances_count,
            COUNT(CASE WHEN cb.current_balance = 0 THEN 1 END) as zero_balances_count,
            SUM(cb.total_transactions_count) as total_transactions,
            AVG(cb.total_transactions_count) as avg_transactions_per_entity,
            MAX(cb.last_transaction_date) as latest_activity_date,
            MIN(cb.last_transaction_date) as earliest_activity_date
        FROM CURRENT_BALANCES cb
        JOIN ENTITY_TYPES et ON cb.entity_type_code = et.entity_type_code
        WHERE et.is_active = 1
        GROUP BY cb.entity_type_code, et.entity_name_ar, cb.currency_code

        UNION ALL

        SELECT
            'BALANCE_TRANSACTIONS' as report_type,
            'المعاملات المالية' as report_name_ar,
            bt.entity_type_code,
            et.entity_name_ar as entity_type_name_ar,
            bt.currency_code,
            COUNT(*) as records_count,
            SUM(bt.debit_amount - bt.credit_amount) as total_balance,
            AVG(bt.debit_amount - bt.credit_amount) as avg_balance,
            MIN(bt.debit_amount - bt.credit_amount) as min_balance,
            MAX(bt.debit_amount - bt.credit_amount) as max_balance,
            STDDEV(bt.debit_amount - bt.credit_amount) as balance_std_deviation,
            COUNT(CASE WHEN bt.debit_amount > bt.credit_amount THEN 1 END) as positive_balances_count,
            COUNT(CASE WHEN bt.credit_amount > bt.debit_amount THEN 1 END) as negative_balances_count,
            COUNT(CASE WHEN bt.debit_amount = bt.credit_amount THEN 1 END) as zero_balances_count,
            COUNT(*) as total_transactions,
            COUNT(*) / COUNT(DISTINCT bt.entity_id) as avg_transactions_per_entity,
            MAX(bt.document_date) as latest_activity_date,
            MIN(bt.document_date) as earliest_activity_date
        FROM BALANCE_TRANSACTIONS bt
        JOIN ENTITY_TYPES et ON bt.entity_type_code = et.entity_type_code
        WHERE bt.status = 'POSTED' AND et.is_active = 1
        GROUP BY bt.entity_type_code, et.entity_name_ar, bt.currency_code

        ORDER BY report_type, entity_type_code, currency_code
        """

        oracle_mgr.execute_update(view5_sql)
        views_created.append("V_STATISTICAL_SUMMARY")

        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info(f"✅ تم إنشاء {len(views_created)} Views للتقارير والتحليلات بنجاح")
        return jsonify({
            'success': True,
            'message': f'تم إنشاء {len(views_created)} Views للتقارير والتحليلات بنجاح',
            'views_created': views_created
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء Views للتقارير والتحليلات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/setup/create_basic_indexes', methods=['POST'])
def create_basic_indexes():
    """إنشاء الفهارس الأساسية"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        indexes_created = []

        # فهارس جدول ENTITY_TYPES
        entity_types_indexes = [
            "CREATE INDEX IDX_ENTITY_TYPES_CODE ON ENTITY_TYPES(entity_type_code)",
            "CREATE INDEX IDX_ENTITY_TYPES_MODULE ON ENTITY_TYPES(module_name)",
            "CREATE INDEX IDX_ENTITY_TYPES_ACTIVE ON ENTITY_TYPES(is_active)",
            "CREATE INDEX IDX_ENTITY_TYPES_SORT ON ENTITY_TYPES(sort_order)"
        ]

        for index_sql in entity_types_indexes:
            try:
                oracle_mgr.execute_update(index_sql)
                index_name = index_sql.split()[2]  # استخراج اسم الفهرس
                indexes_created.append(index_name)
            except Exception as e:
                if "already exists" not in str(e).lower():
                    logger.warning(f"تحذير في إنشاء فهرس: {e}")

        # فهارس جدول DOCUMENT_TYPES
        document_types_indexes = [
            "CREATE INDEX IDX_DOCUMENT_TYPES_CODE ON DOCUMENT_TYPES(document_type_code)",
            "CREATE INDEX IDX_DOCUMENT_TYPES_CATEGORY ON DOCUMENT_TYPES(document_category)",
            "CREATE INDEX IDX_DOCUMENT_TYPES_MODULE ON DOCUMENT_TYPES(module_name)",
            "CREATE INDEX IDX_DOCUMENT_TYPES_ACTIVE ON DOCUMENT_TYPES(is_active)",
            "CREATE INDEX IDX_DOCUMENT_TYPES_AFFECTS ON DOCUMENT_TYPES(affects_balance)",
            "CREATE INDEX IDX_DOCUMENT_TYPES_REVERSIBLE ON DOCUMENT_TYPES(is_reversible)"
        ]

        for index_sql in document_types_indexes:
            try:
                oracle_mgr.execute_update(index_sql)
                index_name = index_sql.split()[2]
                indexes_created.append(index_name)
            except Exception as e:
                if "already exists" not in str(e).lower():
                    logger.warning(f"تحذير في إنشاء فهرس: {e}")

        # فهارس جدول OPENING_BALANCES
        opening_balances_indexes = [
            "CREATE INDEX IDX_OB_ENTITY_TYPE_ID ON OPENING_BALANCES(entity_type_code, entity_id)",
            "CREATE INDEX IDX_OB_FISCAL_YEAR ON OPENING_BALANCES(fiscal_year)",
            "CREATE INDEX IDX_OB_FISCAL_DATE ON OPENING_BALANCES(fiscal_period_start_date)",
            "CREATE INDEX IDX_OB_CURRENCY ON OPENING_BALANCES(currency_code)",
            "CREATE INDEX IDX_OB_STATUS ON OPENING_BALANCES(status)",
            "CREATE INDEX IDX_OB_ACTIVE ON OPENING_BALANCES(is_active)",
            "CREATE INDEX IDX_OB_BALANCE_TYPE ON OPENING_BALANCES(balance_type)",
            "CREATE INDEX IDX_OB_AMOUNT ON OPENING_BALANCES(opening_balance_amount)",
            "CREATE INDEX IDX_OB_CREATED_DATE ON OPENING_BALANCES(created_date)",
            "CREATE INDEX IDX_OB_POSTED_DATE ON OPENING_BALANCES(posted_date)"
        ]

        for index_sql in opening_balances_indexes:
            try:
                oracle_mgr.execute_update(index_sql)
                index_name = index_sql.split()[2]
                indexes_created.append(index_name)
            except Exception as e:
                if "already exists" not in str(e).lower():
                    logger.warning(f"تحذير في إنشاء فهرس: {e}")

        # فهارس جدول CURRENT_BALANCES
        current_balances_indexes = [
            "CREATE INDEX IDX_CB_ENTITY_TYPE_ID ON CURRENT_BALANCES(entity_type_code, entity_id)",
            "CREATE INDEX IDX_CB_CURRENCY ON CURRENT_BALANCES(currency_code)",
            "CREATE INDEX IDX_CB_CURRENT_BALANCE ON CURRENT_BALANCES(current_balance)",
            "CREATE INDEX IDX_CB_OPENING_BALANCE ON CURRENT_BALANCES(opening_balance)",
            "CREATE INDEX IDX_CB_LAST_TRANSACTION ON CURRENT_BALANCES(last_transaction_date)",
            "CREATE INDEX IDX_CB_TRANSACTION_COUNT ON CURRENT_BALANCES(total_transactions_count)",
            "CREATE INDEX IDX_CB_CREATED_AT ON CURRENT_BALANCES(created_at)",
            "CREATE INDEX IDX_CB_UPDATED_AT ON CURRENT_BALANCES(updated_at)",
            "CREATE INDEX IDX_CB_DEBIT_AMOUNT ON CURRENT_BALANCES(debit_amount)",
            "CREATE INDEX IDX_CB_CREDIT_AMOUNT ON CURRENT_BALANCES(credit_amount)"
        ]

        for index_sql in current_balances_indexes:
            try:
                oracle_mgr.execute_update(index_sql)
                index_name = index_sql.split()[2]
                indexes_created.append(index_name)
            except Exception as e:
                if "already exists" not in str(e).lower():
                    logger.warning(f"تحذير في إنشاء فهرس: {e}")

        # فهارس جدول BALANCE_TRANSACTIONS
        balance_transactions_indexes = [
            "CREATE INDEX IDX_BT_ENTITY_TYPE_ID ON BALANCE_TRANSACTIONS(entity_type_code, entity_id)",
            "CREATE INDEX IDX_BT_DOCUMENT_TYPE ON BALANCE_TRANSACTIONS(document_type_code)",
            "CREATE INDEX IDX_BT_DOCUMENT_NUMBER ON BALANCE_TRANSACTIONS(document_number)",
            "CREATE INDEX IDX_BT_DOCUMENT_DATE ON BALANCE_TRANSACTIONS(document_date)",
            "CREATE INDEX IDX_BT_CURRENCY ON BALANCE_TRANSACTIONS(currency_code)",
            "CREATE INDEX IDX_BT_STATUS ON BALANCE_TRANSACTIONS(status)",
            "CREATE INDEX IDX_BT_DEBIT_AMOUNT ON BALANCE_TRANSACTIONS(debit_amount)",
            "CREATE INDEX IDX_BT_CREDIT_AMOUNT ON BALANCE_TRANSACTIONS(credit_amount)",
            "CREATE INDEX IDX_BT_CREATED_DATE ON BALANCE_TRANSACTIONS(created_date)",
            "CREATE INDEX IDX_BT_REFERENCE ON BALANCE_TRANSACTIONS(reference_number)"
        ]

        for index_sql in balance_transactions_indexes:
            try:
                oracle_mgr.execute_update(index_sql)
                index_name = index_sql.split()[2]
                indexes_created.append(index_name)
            except Exception as e:
                if "already exists" not in str(e).lower():
                    logger.warning(f"تحذير في إنشاء فهرس: {e}")

        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info(f"✅ تم إنشاء {len(indexes_created)} فهرس أساسي بنجاح")
        return jsonify({
            'success': True,
            'message': f'تم إنشاء {len(indexes_created)} فهرس أساسي بنجاح',
            'indexes_created': indexes_created
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء الفهارس الأساسية: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/setup/create_advanced_indexes', methods=['POST'])
def create_advanced_indexes():
    """إنشاء فهارس متقدمة للتقارير"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        indexes_created = []

        # فهارس مركبة للأرصدة الجارية (للتقارير السريعة)
        current_balances_advanced_indexes = [
            # فهرس مركب للبحث السريع حسب نوع الكيان والعملة
            "CREATE INDEX IDX_CB_TYPE_CURRENCY_BALANCE ON CURRENT_BALANCES(entity_type_code, currency_code, current_balance)",

            # فهرس مركب للتقارير الشهرية
            "CREATE INDEX IDX_CB_TYPE_ENTITY_CURRENCY ON CURRENT_BALANCES(entity_type_code, entity_id, currency_code)",

            # فهرس للأرصدة النشطة فقط (غير الصفرية)
            "CREATE INDEX IDX_CB_ACTIVE_BALANCES ON CURRENT_BALANCES(entity_type_code, currency_code) WHERE current_balance != 0",

            # فهرس للأرصدة المدينة
            "CREATE INDEX IDX_CB_DEBIT_BALANCES ON CURRENT_BALANCES(entity_type_code, currency_code, current_balance) WHERE current_balance > 0",

            # فهرس للأرصدة الدائنة
            "CREATE INDEX IDX_CB_CREDIT_BALANCES ON CURRENT_BALANCES(entity_type_code, currency_code, current_balance) WHERE current_balance < 0",

            # فهرس لتقرير أعمار الديون
            "CREATE INDEX IDX_CB_AGING_REPORT ON CURRENT_BALANCES(entity_type_code, last_transaction_date, current_balance) WHERE current_balance != 0"
        ]

        for index_sql in current_balances_advanced_indexes:
            try:
                oracle_mgr.execute_update(index_sql)
                index_name = index_sql.split()[2]
                indexes_created.append(index_name)
            except Exception as e:
                if "already exists" not in str(e).lower():
                    logger.warning(f"تحذير في إنشاء فهرس متقدم: {e}")

        # فهارس مركبة للمعاملات (للتحليلات والتقارير)
        balance_transactions_advanced_indexes = [
            # فهرس مركب للتقارير الشهرية
            "CREATE INDEX IDX_BT_MONTHLY_REPORT ON BALANCE_TRANSACTIONS(TO_CHAR(document_date, 'YYYY-MM'), entity_type_code, currency_code, status)",

            # فهرس مركب للتحليل حسب نوع الوثيقة
            "CREATE INDEX IDX_BT_DOCTYPE_ANALYSIS ON BALANCE_TRANSACTIONS(document_type_code, currency_code, status, document_date)",

            # فهرس للمعاملات المرحلة فقط
            "CREATE INDEX IDX_BT_POSTED_TRANSACTIONS ON BALANCE_TRANSACTIONS(entity_type_code, entity_id, document_date, currency_code) WHERE status = 'POSTED'",

            # فهرس للمعاملات المعكوسة
            "CREATE INDEX IDX_BT_REVERSED_TRANSACTIONS ON BALANCE_TRANSACTIONS(document_type_code, document_number, status) WHERE status IN ('REVERSED', 'POSTED')",

            # فهرس للتدفقات النقدية
            "CREATE INDEX IDX_BT_CASH_FLOW ON BALANCE_TRANSACTIONS(document_date, document_type_code, currency_code, debit_amount, credit_amount) WHERE status = 'POSTED'",

            # فهرس للبحث بالمرجع
            "CREATE INDEX IDX_BT_REFERENCE_SEARCH ON BALANCE_TRANSACTIONS(reference_number, document_number, entity_type_code)",

            # فهرس للمعاملات الحديثة (آخر 6 أشهر)
            "CREATE INDEX IDX_BT_RECENT_TRANSACTIONS ON BALANCE_TRANSACTIONS(document_date, entity_type_code, entity_id) WHERE document_date >= ADD_MONTHS(SYSDATE, -6)"
        ]

        for index_sql in balance_transactions_advanced_indexes:
            try:
                oracle_mgr.execute_update(index_sql)
                index_name = index_sql.split()[2]
                indexes_created.append(index_name)
            except Exception as e:
                if "already exists" not in str(e).lower():
                    logger.warning(f"تحذير في إنشاء فهرس متقدم: {e}")

        # فهارس مركبة للأرصدة الافتتاحية
        opening_balances_advanced_indexes = [
            # فهرس مركب للبحث حسب السنة المالية
            "CREATE INDEX IDX_OB_FISCAL_SEARCH ON OPENING_BALANCES(fiscal_year, entity_type_code, currency_code, status)",

            # فهرس للأرصدة المرحلة فقط
            "CREATE INDEX IDX_OB_POSTED_BALANCES ON OPENING_BALANCES(entity_type_code, entity_id, currency_code, opening_balance_amount) WHERE status = 'POSTED'",

            # فهرس للأرصدة الافتتاحية النشطة
            "CREATE INDEX IDX_OB_ACTIVE_BALANCES ON OPENING_BALANCES(fiscal_year, entity_type_code, currency_code) WHERE is_active = 1 AND status = 'POSTED'"
        ]

        for index_sql in opening_balances_advanced_indexes:
            try:
                oracle_mgr.execute_update(index_sql)
                index_name = index_sql.split()[2]
                indexes_created.append(index_name)
            except Exception as e:
                if "already exists" not in str(e).lower():
                    logger.warning(f"تحذير في إنشاء فهرس متقدم: {e}")

        # فهارس وظيفية (Function-Based Indexes)
        function_based_indexes = [
            # فهرس للبحث بالقيمة المطلقة للرصيد
            "CREATE INDEX IDX_CB_ABS_BALANCE ON CURRENT_BALANCES(ABS(current_balance), entity_type_code)",

            # فهرس للبحث بالسنة والشهر
            "CREATE INDEX IDX_BT_YEAR_MONTH ON BALANCE_TRANSACTIONS(TO_CHAR(document_date, 'YYYY-MM'), entity_type_code)",

            # فهرس للبحث بالربع السنوي
            "CREATE INDEX IDX_BT_QUARTER ON BALANCE_TRANSACTIONS(TO_CHAR(document_date, 'YYYY-Q'), entity_type_code)",

            # فهرس لصافي المبلغ
            "CREATE INDEX IDX_BT_NET_AMOUNT ON BALANCE_TRANSACTIONS(debit_amount - credit_amount, entity_type_code) WHERE status = 'POSTED'",

            # فهرس لعمر المعاملة بالأيام
            "CREATE INDEX IDX_BT_AGE_DAYS ON BALANCE_TRANSACTIONS(TRUNC(SYSDATE - document_date), entity_type_code) WHERE status = 'POSTED'"
        ]

        for index_sql in function_based_indexes:
            try:
                oracle_mgr.execute_update(index_sql)
                index_name = index_sql.split()[2]
                indexes_created.append(index_name)
            except Exception as e:
                if "already exists" not in str(e).lower():
                    logger.warning(f"تحذير في إنشاء فهرس وظيفي: {e}")

        # فهارس للأداء العالي (Bitmap Indexes للبيانات ذات القيم المحدودة)
        bitmap_indexes = [
            # فهرس bitmap لحالة المعاملات
            "CREATE BITMAP INDEX IDX_BT_STATUS_BITMAP ON BALANCE_TRANSACTIONS(status)",

            # فهرس bitmap لنوع الرصيد في الأرصدة الافتتاحية
            "CREATE BITMAP INDEX IDX_OB_BALANCE_TYPE_BITMAP ON OPENING_BALANCES(balance_type)",

            # فهرس bitmap للعملات
            "CREATE BITMAP INDEX IDX_CB_CURRENCY_BITMAP ON CURRENT_BALANCES(currency_code)",

            # فهرس bitmap لفئات الوثائق
            "CREATE BITMAP INDEX IDX_BT_DOCTYPE_BITMAP ON BALANCE_TRANSACTIONS(document_type_code)"
        ]

        for index_sql in bitmap_indexes:
            try:
                oracle_mgr.execute_update(index_sql)
                index_name = index_sql.split()[2]
                indexes_created.append(index_name)
            except Exception as e:
                if "already exists" not in str(e).lower():
                    logger.warning(f"تحذير في إنشاء فهرس bitmap: {e}")

        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info(f"✅ تم إنشاء {len(indexes_created)} فهرس متقدم بنجاح")
        return jsonify({
            'success': True,
            'message': f'تم إنشاء {len(indexes_created)} فهرس متقدم بنجاح',
            'indexes_created': indexes_created,
            'index_categories': {
                'current_balances_advanced': 6,
                'balance_transactions_advanced': 7,
                'opening_balances_advanced': 3,
                'function_based': 5,
                'bitmap': 4
            }
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء الفهارس المتقدمة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/test/performance', methods=['POST'])
def test_performance():
    """اختبار الأداء والتحقق من Views والفهارس"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        test_results = []

        # اختبار 1: التحقق من وجود جميع Views
        logger.info("🧪 اختبار 1: التحقق من وجود Views")
        try:
            views_check_sql = """
            SELECT view_name, status
            FROM user_views
            WHERE view_name LIKE 'V_%BALANCE%' OR view_name LIKE 'V_%TRANSACTION%'
            ORDER BY view_name
            """

            views_result = oracle_mgr.execute_query(views_check_sql)
            views_count = len(views_result) if views_result else 0

            expected_views = [
                'V_CURRENT_BALANCES_DETAILED', 'V_OPENING_BALANCES_DETAILED',
                'V_BALANCES_SUMMARY_BY_TYPE', 'V_ACTIVE_BALANCES',
                'V_BALANCE_TRANSACTIONS_DETAILED', 'V_TRANSACTIONS_SUMMARY_BY_DOCTYPE',
                'V_MONTHLY_TRANSACTIONS_SUMMARY', 'V_REVERSED_TRANSACTIONS',
                'V_TRIAL_BALANCE', 'V_AGING_REPORT', 'V_CASH_FLOW_ANALYSIS',
                'V_ENTITY_PERFORMANCE_REPORT', 'V_STATISTICAL_SUMMARY'
            ]

            test_results.append(f"✅ تم العثور على {views_count} Views من أصل {len(expected_views)} متوقع")

        except Exception as e:
            test_results.append(f"❌ خطأ في فحص Views: {e}")

        # اختبار 2: التحقق من وجود الفهارس
        logger.info("🧪 اختبار 2: التحقق من وجود الفهارس")
        try:
            indexes_check_sql = """
            SELECT index_name, table_name, status, uniqueness
            FROM user_indexes
            WHERE index_name LIKE 'IDX_%'
            ORDER BY table_name, index_name
            """

            indexes_result = oracle_mgr.execute_query(indexes_check_sql)
            indexes_count = len(indexes_result) if indexes_result else 0

            test_results.append(f"✅ تم العثور على {indexes_count} فهرس مخصص")

        except Exception as e:
            test_results.append(f"❌ خطأ في فحص الفهارس: {e}")

        # اختبار 3: اختبار أداء Views الأساسية
        logger.info("🧪 اختبار 3: اختبار أداء Views الأساسية")
        try:
            import time

            # اختبار View الأرصدة الجارية التفصيلية
            start_time = time.time()
            current_balances_sql = "SELECT COUNT(*) FROM V_CURRENT_BALANCES_DETAILED"
            result = oracle_mgr.execute_query(current_balances_sql)
            end_time = time.time()

            execution_time = round((end_time - start_time) * 1000, 2)  # بالميلي ثانية
            count = result[0][0] if result else 0

            test_results.append(f"✅ V_CURRENT_BALANCES_DETAILED: {count} سجل في {execution_time}ms")

            # اختبار View المعاملات التفصيلية
            start_time = time.time()
            transactions_sql = "SELECT COUNT(*) FROM V_BALANCE_TRANSACTIONS_DETAILED WHERE ROWNUM <= 1000"
            result = oracle_mgr.execute_query(transactions_sql)
            end_time = time.time()

            execution_time = round((end_time - start_time) * 1000, 2)
            count = result[0][0] if result else 0

            test_results.append(f"✅ V_BALANCE_TRANSACTIONS_DETAILED: {count} سجل في {execution_time}ms")

        except Exception as e:
            test_results.append(f"❌ خطأ في اختبار أداء Views: {e}")

        # اختبار 4: اختبار استعلامات التقارير
        logger.info("🧪 اختبار 4: اختبار استعلامات التقارير")
        try:
            # اختبار ميزان المراجعة
            start_time = time.time()
            trial_balance_sql = """
            SELECT entity_type_code, currency_code,
                   SUM(debit_balance) as total_debit,
                   SUM(credit_balance) as total_credit
            FROM V_TRIAL_BALANCE
            GROUP BY entity_type_code, currency_code
            """
            result = oracle_mgr.execute_query(trial_balance_sql)
            end_time = time.time()

            execution_time = round((end_time - start_time) * 1000, 2)
            count = len(result) if result else 0

            test_results.append(f"✅ ميزان المراجعة: {count} مجموعة في {execution_time}ms")

            # اختبار تقرير أعمار الديون
            start_time = time.time()
            aging_sql = """
            SELECT aging_category, COUNT(*), SUM(ABS(current_balance))
            FROM V_AGING_REPORT
            GROUP BY aging_category
            """
            result = oracle_mgr.execute_query(aging_sql)
            end_time = time.time()

            execution_time = round((end_time - start_time) * 1000, 2)
            count = len(result) if result else 0

            test_results.append(f"✅ تقرير أعمار الديون: {count} فئة في {execution_time}ms")

        except Exception as e:
            test_results.append(f"❌ خطأ في اختبار استعلامات التقارير: {e}")

        # اختبار 5: اختبار فعالية الفهارس
        logger.info("🧪 اختبار 5: اختبار فعالية الفهارس")
        try:
            # اختبار البحث بالفهرس المركب
            start_time = time.time()
            indexed_search_sql = """
            SELECT COUNT(*)
            FROM CURRENT_BALANCES
            WHERE entity_type_code = 'SUPPLIER'
            AND currency_code = 'SAR'
            """
            result = oracle_mgr.execute_query(indexed_search_sql)
            end_time = time.time()

            execution_time = round((end_time - start_time) * 1000, 2)
            count = result[0][0] if result else 0

            test_results.append(f"✅ البحث المفهرس (نوع+عملة): {count} سجل في {execution_time}ms")

            # اختبار البحث في المعاملات بالتاريخ
            start_time = time.time()
            date_search_sql = """
            SELECT COUNT(*)
            FROM BALANCE_TRANSACTIONS
            WHERE document_date >= SYSDATE - 30
            AND status = 'POSTED'
            """
            result = oracle_mgr.execute_query(date_search_sql)
            end_time = time.time()

            execution_time = round((end_time - start_time) * 1000, 2)
            count = result[0][0] if result else 0

            test_results.append(f"✅ البحث بالتاريخ والحالة: {count} سجل في {execution_time}ms")

        except Exception as e:
            test_results.append(f"❌ خطأ في اختبار فعالية الفهارس: {e}")

        # اختبار 6: اختبار الإحصائيات
        logger.info("🧪 اختبار 6: اختبار الإحصائيات")
        try:
            # إحصائيات الجداول
            stats_sql = """
            SELECT table_name, num_rows, blocks, avg_row_len
            FROM user_tables
            WHERE table_name IN ('ENTITY_TYPES', 'DOCUMENT_TYPES', 'CURRENT_BALANCES',
                                 'OPENING_BALANCES', 'BALANCE_TRANSACTIONS')
            ORDER BY table_name
            """

            stats_result = oracle_mgr.execute_query(stats_sql)
            if stats_result:
                for row in stats_result:
                    table_name, num_rows, blocks, avg_row_len = row
                    test_results.append(f"📊 {table_name}: {num_rows or 0} سجل، {blocks or 0} كتلة")

        except Exception as e:
            test_results.append(f"❌ خطأ في جلب الإحصائيات: {e}")

        # اختبار 7: التحقق من سلامة البيانات
        logger.info("🧪 اختبار 7: التحقق من سلامة البيانات")
        try:
            # التحقق من تطابق الأرصدة
            integrity_sql = """
            SELECT
                COUNT(*) as total_balances,
                COUNT(CASE WHEN current_balance = opening_balance + debit_amount - credit_amount THEN 1 END) as correct_balances
            FROM CURRENT_BALANCES
            """

            result = oracle_mgr.execute_query(integrity_sql)
            if result:
                total, correct = result[0]
                if total == correct:
                    test_results.append(f"✅ سلامة البيانات: {correct}/{total} رصيد صحيح")
                else:
                    test_results.append(f"⚠️ سلامة البيانات: {correct}/{total} رصيد صحيح")

        except Exception as e:
            test_results.append(f"❌ خطأ في فحص سلامة البيانات: {e}")

        oracle_mgr.disconnect()

        # تحليل النتائج
        success_count = len([r for r in test_results if r.startswith('✅')])
        warning_count = len([r for r in test_results if r.startswith('⚠️')])
        error_count = len([r for r in test_results if r.startswith('❌')])
        total_tests = len(test_results)

        logger.info(f"✅ اكتمل اختبار الأداء: {success_count} نجح، {warning_count} تحذير، {error_count} خطأ")

        return jsonify({
            'success': True,
            'message': f'اكتمل اختبار الأداء: {success_count} نجح، {warning_count} تحذير، {error_count} خطأ',
            'test_results': test_results,
            'summary': {
                'success_count': success_count,
                'warning_count': warning_count,
                'error_count': error_count,
                'total_tests': total_tests
            }
        })

    except Exception as e:
        logger.error(f"❌ خطأ في اختبار الأداء: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

# ==================== APIs إدارة الأرصدة الافتتاحية ====================

@central_balances_bp.route('/api/opening_balances', methods=['GET'])
def get_opening_balances():
    """جلب الأرصدة الافتتاحية مع فلترة متقدمة"""
    try:
        # معاملات الفلترة
        entity_type_code = request.args.get('entity_type_code')
        entity_id = request.args.get('entity_id')
        fiscal_year = request.args.get('fiscal_year')
        currency_code = request.args.get('currency_code')
        status = request.args.get('status')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 50))

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # بناء الاستعلام مع الفلترة
        where_conditions = ["ob.is_active = 1"]
        params = []

        if entity_type_code:
            where_conditions.append("ob.entity_type_code = :entity_type")
            params.append(entity_type_code)

        if entity_id:
            where_conditions.append("ob.entity_id = :entity_id")
            params.append(int(entity_id))

        if fiscal_year:
            where_conditions.append("ob.fiscal_year = :fiscal_year")
            params.append(int(fiscal_year))

        if currency_code:
            where_conditions.append("ob.currency_code = :currency_code")
            params.append(currency_code)

        if status:
            where_conditions.append("ob.status = :status")
            params.append(status)

        where_clause = " AND ".join(where_conditions)

        # استعلام العد الإجمالي
        count_sql = f"""
        SELECT COUNT(*)
        FROM OPENING_BALANCES ob
        WHERE {where_clause}
        """

        count_result = oracle_mgr.execute_query(count_sql, params)
        total_count = count_result[0][0] if count_result else 0

        # استعلام البيانات مع التصفح
        offset = (page - 1) * per_page

        data_sql = f"""
        SELECT * FROM (
            SELECT
                ob.id, ob.entity_type_code, et.entity_name_ar, ob.entity_id,
                CASE
                    WHEN ob.entity_type_code = 'SUPPLIER' THEN
                        (SELECT name FROM SUPPLIERS WHERE id = ob.entity_id AND ROWNUM = 1)
                    WHEN ob.entity_type_code = 'CUSTOMER' THEN
                        (SELECT name FROM CUSTOMERS WHERE id = ob.entity_id AND ROWNUM = 1)
                    ELSE 'غير محدد'
                END as entity_name,
                ob.account_code, ob.fiscal_year, ob.fiscal_period_start_date,
                ob.currency_code, ob.opening_balance_amount, ob.balance_type,
                ob.exchange_rate, ob.base_currency_amount, ob.document_type_code,
                dt.document_name_ar, ob.document_number, ob.status,
                CASE ob.status
                    WHEN 'DRAFT' THEN 'مسودة'
                    WHEN 'POSTED' THEN 'مرحل'
                    WHEN 'CANCELLED' THEN 'ملغي'
                    ELSE ob.status
                END as status_ar,
                ob.notes, ob.reference_document, ob.created_date, ob.created_by,
                ob.updated_date, ob.updated_by, ob.approved_date, ob.approved_by,
                ob.posted_date, ob.posted_by,
                ROW_NUMBER() OVER (ORDER BY ob.created_date DESC) as rn
            FROM OPENING_BALANCES ob
            JOIN ENTITY_TYPES et ON ob.entity_type_code = et.entity_type_code
            LEFT JOIN DOCUMENT_TYPES dt ON ob.document_type_code = dt.document_type_code
            WHERE {where_clause}
        ) WHERE rn BETWEEN :offset + 1 AND :offset + :per_page
        """

        params.extend([offset, offset, per_page])
        results = oracle_mgr.execute_query(data_sql, params)
        oracle_mgr.disconnect()

        # تحويل النتائج
        opening_balances = []
        if results:
            for row in results:
                opening_balances.append({
                    'id': row[0],
                    'entity_type_code': row[1],
                    'entity_type_name_ar': row[2],
                    'entity_id': row[3],
                    'entity_name': row[4],
                    'account_code': row[5],
                    'fiscal_year': row[6],
                    'fiscal_period_start_date': row[7].strftime('%Y-%m-%d') if row[7] else None,
                    'currency_code': row[8],
                    'opening_balance_amount': float(row[9]) if row[9] else 0,
                    'balance_type': row[10],
                    'exchange_rate': float(row[11]) if row[11] else 1,
                    'base_currency_amount': float(row[12]) if row[12] else 0,
                    'document_type_code': row[13],
                    'document_name_ar': row[14],
                    'document_number': row[15],
                    'status': row[16],
                    'status_ar': row[17],
                    'notes': row[18],
                    'reference_document': row[19],
                    'created_date': row[20].strftime('%Y-%m-%d %H:%M:%S') if row[20] else None,
                    'created_by': row[21],
                    'updated_date': row[22].strftime('%Y-%m-%d %H:%M:%S') if row[22] else None,
                    'updated_by': row[23],
                    'approved_date': row[24].strftime('%Y-%m-%d %H:%M:%S') if row[24] else None,
                    'approved_by': row[25],
                    'posted_date': row[26].strftime('%Y-%m-%d %H:%M:%S') if row[26] else None,
                    'posted_by': row[27]
                })

        # حساب معلومات التصفح
        total_pages = (total_count + per_page - 1) // per_page

        return jsonify({
            'success': True,
            'data': opening_balances,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total_count': total_count,
                'total_pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            }
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب الأرصدة الافتتاحية: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/opening_balances/<int:balance_id>', methods=['GET'])
def get_opening_balance_by_id(balance_id):
    """جلب رصيد افتتاحي محدد"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        query = """
        SELECT
            ob.id, ob.entity_type_code, et.entity_name_ar, ob.entity_id,
            CASE
                WHEN ob.entity_type_code = 'SUPPLIER' THEN
                    (SELECT name FROM SUPPLIERS WHERE id = ob.entity_id AND ROWNUM = 1)
                WHEN ob.entity_type_code = 'CUSTOMER' THEN
                    (SELECT name FROM CUSTOMERS WHERE id = ob.entity_id AND ROWNUM = 1)
                ELSE 'غير محدد'
            END as entity_name,
            ob.account_code, ob.fiscal_year, ob.fiscal_period_start_date,
            ob.currency_code, ob.opening_balance_amount, ob.balance_type,
            ob.exchange_rate, ob.base_currency_amount, ob.document_type_code,
            dt.document_name_ar, ob.document_number, ob.status, ob.notes,
            ob.reference_document, ob.created_date, ob.created_by,
            ob.updated_date, ob.updated_by, ob.approved_date, ob.approved_by,
            ob.posted_date, ob.posted_by, ob.is_active
        FROM OPENING_BALANCES ob
        JOIN ENTITY_TYPES et ON ob.entity_type_code = et.entity_type_code
        LEFT JOIN DOCUMENT_TYPES dt ON ob.document_type_code = dt.document_type_code
        WHERE ob.id = :balance_id
        """

        result = oracle_mgr.execute_query(query, [balance_id])
        oracle_mgr.disconnect()

        if not result:
            return jsonify({'success': False, 'message': 'الرصيد الافتتاحي غير موجود'}), 404

        row = result[0]
        opening_balance = {
            'id': row[0],
            'entity_type_code': row[1],
            'entity_type_name_ar': row[2],
            'entity_id': row[3],
            'entity_name': row[4],
            'account_code': row[5],
            'fiscal_year': row[6],
            'fiscal_period_start_date': row[7].strftime('%Y-%m-%d') if row[7] else None,
            'currency_code': row[8],
            'opening_balance_amount': float(row[9]) if row[9] else 0,
            'balance_type': row[10],
            'exchange_rate': float(row[11]) if row[11] else 1,
            'base_currency_amount': float(row[12]) if row[12] else 0,
            'document_type_code': row[13],
            'document_name_ar': row[14],
            'document_number': row[15],
            'status': row[16],
            'notes': row[17],
            'reference_document': row[18],
            'created_date': row[19].strftime('%Y-%m-%d %H:%M:%S') if row[19] else None,
            'created_by': row[20],
            'updated_date': row[21].strftime('%Y-%m-%d %H:%M:%S') if row[21] else None,
            'updated_by': row[22],
            'approved_date': row[23].strftime('%Y-%m-%d %H:%M:%S') if row[23] else None,
            'approved_by': row[24],
            'posted_date': row[25].strftime('%Y-%m-%d %H:%M:%S') if row[25] else None,
            'posted_by': row[26],
            'is_active': bool(row[27])
        }

        return jsonify({
            'success': True,
            'data': opening_balance
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب الرصيد الافتتاحي: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/opening_balances', methods=['POST'])
def create_opening_balance():
    """إنشاء رصيد افتتاحي جديد"""
    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        required_fields = ['entity_type_code', 'entity_id', 'fiscal_year',
                          'fiscal_period_start_date', 'currency_code',
                          'opening_balance_amount', 'balance_type']

        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            return jsonify({
                'success': False,
                'message': f'الحقول التالية مطلوبة: {", ".join(missing_fields)}'
            }), 400

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # التحقق من عدم وجود رصيد مكرر
        duplicate_check_sql = """
        SELECT COUNT(*) FROM OPENING_BALANCES
        WHERE entity_type_code = :1 AND entity_id = :2
        AND fiscal_year = :3 AND currency_code = :4
        AND is_active = 1
        """

        duplicate_result = oracle_mgr.execute_query(duplicate_check_sql, [
            data['entity_type_code'], data['entity_id'],
            data['fiscal_year'], data['currency_code']
        ])

        if duplicate_result and duplicate_result[0][0] > 0:
            oracle_mgr.disconnect()
            return jsonify({
                'success': False,
                'message': 'يوجد رصيد افتتاحي مسبق لهذا الكيان في نفس السنة المالية والعملة'
            }), 400

        # إدراج الرصيد الافتتاحي الجديد
        insert_sql = """
        INSERT INTO OPENING_BALANCES (
            entity_type_code, entity_id, account_code, fiscal_year,
            fiscal_period_start_date, currency_code, opening_balance_amount,
            balance_type, exchange_rate, base_currency_amount,
            document_type_code, document_number, status, notes,
            reference_document, created_by
        ) VALUES (
            :1, :2, :3, :4, TO_DATE(:5, 'YYYY-MM-DD'), :6, :7, :8, :9, :10,
            :11, :12, :13, :14, :15, :16
        ) RETURNING id INTO :17
        """

        # تحضير البيانات
        exchange_rate = data.get('exchange_rate', 1)
        base_currency_amount = data['opening_balance_amount'] * exchange_rate

        params = [
            data['entity_type_code'],
            data['entity_id'],
            data.get('account_code'),
            data['fiscal_year'],
            data['fiscal_period_start_date'],
            data['currency_code'],
            data['opening_balance_amount'],
            data['balance_type'],
            exchange_rate,
            base_currency_amount,
            data.get('document_type_code', 'OPENING_BALANCE'),
            data.get('document_number'),
            data.get('status', 'DRAFT'),
            data.get('notes'),
            data.get('reference_document'),
            data.get('created_by', 1)
        ]

        # تنفيذ الإدراج
        cursor = oracle_mgr.connection.cursor()
        new_id_var = cursor.var(int)
        params.append(new_id_var)

        cursor.execute(insert_sql, params)
        new_id = new_id_var.getvalue()

        cursor.close()
        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info(f"✅ تم إنشاء رصيد افتتاحي جديد برقم: {new_id}")
        return jsonify({
            'success': True,
            'message': 'تم إنشاء الرصيد الافتتاحي بنجاح',
            'data': {'id': new_id}
        }), 201

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء الرصيد الافتتاحي: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/opening_balances/<int:balance_id>', methods=['PUT'])
def update_opening_balance(balance_id):
    """تعديل رصيد افتتاحي موجود"""
    try:
        data = request.get_json()

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # التحقق من وجود الرصيد وحالته
        check_sql = """
        SELECT status FROM OPENING_BALANCES
        WHERE id = :1 AND is_active = 1
        """

        check_result = oracle_mgr.execute_query(check_sql, [balance_id])
        if not check_result:
            oracle_mgr.disconnect()
            return jsonify({'success': False, 'message': 'الرصيد الافتتاحي غير موجود'}), 404

        current_status = check_result[0][0]
        if current_status == 'POSTED':
            oracle_mgr.disconnect()
            return jsonify({'success': False, 'message': 'لا يمكن تعديل رصيد مرحل'}), 400

        # بناء استعلام التحديث
        update_fields = []
        params = []

        updatable_fields = {
            'entity_type_code': 'entity_type_code',
            'entity_id': 'entity_id',
            'account_code': 'account_code',
            'fiscal_year': 'fiscal_year',
            'fiscal_period_start_date': 'fiscal_period_start_date',
            'currency_code': 'currency_code',
            'opening_balance_amount': 'opening_balance_amount',
            'balance_type': 'balance_type',
            'exchange_rate': 'exchange_rate',
            'document_type_code': 'document_type_code',
            'document_number': 'document_number',
            'status': 'status',
            'notes': 'notes',
            'reference_document': 'reference_document'
        }

        for field, column in updatable_fields.items():
            if field in data:
                if field == 'fiscal_period_start_date':
                    update_fields.append(f"{column} = TO_DATE(:{len(params)+1}, 'YYYY-MM-DD')")
                else:
                    update_fields.append(f"{column} = :{len(params)+1}")
                params.append(data[field])

        if not update_fields:
            oracle_mgr.disconnect()
            return jsonify({'success': False, 'message': 'لا توجد حقول للتحديث'}), 400

        # إضافة حقول التحديث التلقائية
        update_fields.extend([
            f"updated_date = CURRENT_TIMESTAMP",
            f"updated_by = :{len(params)+1}"
        ])
        params.append(data.get('updated_by', 1))

        # حساب المبلغ بالعملة الأساسية إذا تم تحديث المبلغ أو سعر الصرف
        if 'opening_balance_amount' in data or 'exchange_rate' in data:
            update_fields.append(f"base_currency_amount = opening_balance_amount * NVL(exchange_rate, 1)")

        # تنفيذ التحديث
        update_sql = f"""
        UPDATE OPENING_BALANCES
        SET {', '.join(update_fields)}
        WHERE id = :{len(params)+1} AND is_active = 1
        """
        params.append(balance_id)

        oracle_mgr.execute_update(update_sql, params)
        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info(f"✅ تم تحديث الرصيد الافتتاحي رقم: {balance_id}")
        return jsonify({
            'success': True,
            'message': 'تم تحديث الرصيد الافتتاحي بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في تحديث الرصيد الافتتاحي: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/opening_balances/<int:balance_id>', methods=['DELETE'])
def delete_opening_balance(balance_id):
    """حذف رصيد افتتاحي (حذف منطقي)"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # التحقق من وجود الرصيد وحالته
        check_sql = """
        SELECT status FROM OPENING_BALANCES
        WHERE id = :1 AND is_active = 1
        """

        check_result = oracle_mgr.execute_query(check_sql, [balance_id])
        if not check_result:
            oracle_mgr.disconnect()
            return jsonify({'success': False, 'message': 'الرصيد الافتتاحي غير موجود'}), 404

        current_status = check_result[0][0]
        if current_status == 'POSTED':
            oracle_mgr.disconnect()
            return jsonify({'success': False, 'message': 'لا يمكن حذف رصيد مرحل'}), 400

        # الحذف المنطقي
        delete_sql = """
        UPDATE OPENING_BALANCES
        SET is_active = 0, updated_date = CURRENT_TIMESTAMP, updated_by = :1
        WHERE id = :2
        """

        oracle_mgr.execute_update(delete_sql, [1, balance_id])  # 1 = user_id افتراضي
        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info(f"✅ تم حذف الرصيد الافتتاحي رقم: {balance_id}")
        return jsonify({
            'success': True,
            'message': 'تم حذف الرصيد الافتتاحي بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في حذف الرصيد الافتتاحي: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/opening_balances/<int:balance_id>/approve', methods=['POST'])
def approve_opening_balance(balance_id):
    """اعتماد رصيد افتتاحي"""
    try:
        data = request.get_json()
        approved_by = data.get('approved_by', 1)

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # التحقق من وجود الرصيد وحالته
        check_sql = """
        SELECT status FROM OPENING_BALANCES
        WHERE id = :1 AND is_active = 1
        """

        check_result = oracle_mgr.execute_query(check_sql, [balance_id])
        if not check_result:
            oracle_mgr.disconnect()
            return jsonify({'success': False, 'message': 'الرصيد الافتتاحي غير موجود'}), 404

        current_status = check_result[0][0]
        if current_status != 'DRAFT':
            oracle_mgr.disconnect()
            return jsonify({'success': False, 'message': f'لا يمكن اعتماد رصيد في حالة {current_status}'}), 400

        # تحديث حالة الاعتماد
        approve_sql = """
        UPDATE OPENING_BALANCES
        SET status = 'APPROVED',
            approved_date = CURRENT_TIMESTAMP,
            approved_by = :1,
            updated_date = CURRENT_TIMESTAMP,
            updated_by = :2
        WHERE id = :3
        """

        oracle_mgr.execute_update(approve_sql, [approved_by, approved_by, balance_id])
        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info(f"✅ تم اعتماد الرصيد الافتتاحي رقم: {balance_id}")
        return jsonify({
            'success': True,
            'message': 'تم اعتماد الرصيد الافتتاحي بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في اعتماد الرصيد الافتتاحي: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/opening_balances/<int:balance_id>/post', methods=['POST'])
def post_opening_balance(balance_id):
    """ترحيل رصيد افتتاحي"""
    try:
        data = request.get_json()
        posted_by = data.get('posted_by', 1)

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # التحقق من وجود الرصيد وحالته
        check_sql = """
        SELECT status, entity_type_code, entity_id, fiscal_period_start_date, currency_code
        FROM OPENING_BALANCES
        WHERE id = :1 AND is_active = 1
        """

        check_result = oracle_mgr.execute_query(check_sql, [balance_id])
        if not check_result:
            oracle_mgr.disconnect()
            return jsonify({'success': False, 'message': 'الرصيد الافتتاحي غير موجود'}), 404

        current_status, entity_type_code, entity_id, fiscal_date, currency_code = check_result[0]

        if current_status not in ['APPROVED', 'DRAFT']:
            oracle_mgr.disconnect()
            return jsonify({'success': False, 'message': f'لا يمكن ترحيل رصيد في حالة {current_status}'}), 400

        # تحديث حالة الترحيل
        post_sql = """
        UPDATE OPENING_BALANCES
        SET status = 'POSTED',
            posted_date = CURRENT_TIMESTAMP,
            posted_by = :1,
            updated_date = CURRENT_TIMESTAMP,
            updated_by = :2
        WHERE id = :3
        """

        oracle_mgr.execute_update(post_sql, [posted_by, posted_by, balance_id])

        # استدعاء إجراء ترحيل الأرصدة للكيان المحدد
        call_sql = """
        DECLARE
            v_result VARCHAR2(4000);
        BEGIN
            POST_OPENING_BALANCES(
                p_entity_type_code => :1,
                p_fiscal_date => :2,
                p_currency_code => :3,
                p_posted_by => :4,
                p_posting_reference => 'API_SINGLE_BALANCE_' || :5,
                p_result => v_result
            );
            :6 := v_result;
        END;
        """

        cursor = oracle_mgr.connection.cursor()
        result_var = cursor.var(str)
        cursor.execute(call_sql, [entity_type_code, fiscal_date, currency_code, posted_by, balance_id, result_var])
        result = result_var.getvalue()
        cursor.close()

        if not result.startswith('SUCCESS:'):
            oracle_mgr.rollback()
            oracle_mgr.disconnect()
            return jsonify({'success': False, 'message': f'خطأ في الترحيل: {result}'}), 400

        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info(f"✅ تم ترحيل الرصيد الافتتاحي رقم: {balance_id}")
        return jsonify({
            'success': True,
            'message': 'تم ترحيل الرصيد الافتتاحي بنجاح',
            'posting_result': result
        })

    except Exception as e:
        logger.error(f"❌ خطأ في ترحيل الرصيد الافتتاحي: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/opening_balances/bulk_approve', methods=['POST'])
def bulk_approve_opening_balances():
    """اعتماد مجموعة من الأرصدة الافتتاحية"""
    try:
        data = request.get_json()
        balance_ids = data.get('balance_ids', [])
        approved_by = data.get('approved_by', 1)

        if not balance_ids:
            return jsonify({'success': False, 'message': 'لا توجد أرصدة للاعتماد'}), 400

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # اعتماد جميع الأرصدة المحددة
        approve_sql = """
        UPDATE OPENING_BALANCES
        SET status = 'APPROVED',
            approved_date = CURRENT_TIMESTAMP,
            approved_by = :1,
            updated_date = CURRENT_TIMESTAMP,
            updated_by = :2
        WHERE id = :3 AND status = 'DRAFT' AND is_active = 1
        """

        approved_count = 0
        for balance_id in balance_ids:
            result = oracle_mgr.execute_update(approve_sql, [approved_by, approved_by, balance_id])
            if result > 0:
                approved_count += 1

        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info(f"✅ تم اعتماد {approved_count} رصيد افتتاحي من أصل {len(balance_ids)}")
        return jsonify({
            'success': True,
            'message': f'تم اعتماد {approved_count} رصيد افتتاحي من أصل {len(balance_ids)}',
            'approved_count': approved_count,
            'total_requested': len(balance_ids)
        })

    except Exception as e:
        logger.error(f"❌ خطأ في الاعتماد المجمع: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/opening_balances/bulk_post', methods=['POST'])
def bulk_post_opening_balances():
    """ترحيل مجموعة من الأرصدة الافتتاحية"""
    try:
        data = request.get_json()
        entity_type_code = data.get('entity_type_code')
        fiscal_date = data.get('fiscal_date')
        currency_code = data.get('currency_code')
        posted_by = data.get('posted_by', 1)

        if not fiscal_date:
            return jsonify({'success': False, 'message': 'تاريخ الفترة المحاسبية مطلوب'}), 400

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # استدعاء إجراء الترحيل المجمع
        call_sql = """
        DECLARE
            v_result VARCHAR2(4000);
        BEGIN
            POST_OPENING_BALANCES(
                p_entity_type_code => :1,
                p_fiscal_date => TO_DATE(:2, 'YYYY-MM-DD'),
                p_currency_code => :3,
                p_posted_by => :4,
                p_posting_reference => 'API_BULK_POST',
                p_result => v_result
            );
            :5 := v_result;
        END;
        """

        cursor = oracle_mgr.connection.cursor()
        result_var = cursor.var(str)
        cursor.execute(call_sql, [entity_type_code, fiscal_date, currency_code, posted_by, result_var])
        result = result_var.getvalue()
        cursor.close()

        if result.startswith('SUCCESS:'):
            oracle_mgr.commit()
            oracle_mgr.disconnect()

            logger.info(f"✅ ترحيل مجمع للأرصدة الافتتاحية: {result}")
            return jsonify({
                'success': True,
                'message': result.replace('SUCCESS: ', ''),
                'posting_result': result
            })
        else:
            oracle_mgr.rollback()
            oracle_mgr.disconnect()

            logger.error(f"❌ خطأ في الترحيل المجمع: {result}")
            return jsonify({
                'success': False,
                'message': result.replace('ERROR: ', ''),
                'posting_result': result
            }), 400

    except Exception as e:
        logger.error(f"❌ خطأ في الترحيل المجمع: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

# ==================== APIs إدارة المعاملات ====================

@central_balances_bp.route('/api/transactions', methods=['GET'])
def get_transactions():
    """جلب المعاملات مع فلترة متقدمة"""
    try:
        # معاملات الفلترة
        entity_type_code = request.args.get('entity_type_code')
        entity_id = request.args.get('entity_id')
        document_type_code = request.args.get('document_type_code')
        document_number = request.args.get('document_number')
        currency_code = request.args.get('currency_code')
        status = request.args.get('status', 'POSTED')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 50))

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # بناء الاستعلام مع الفلترة
        where_conditions = []
        params = []

        if entity_type_code:
            where_conditions.append("bt.entity_type_code = :entity_type")
            params.append(entity_type_code)

        if entity_id:
            where_conditions.append("bt.entity_id = :entity_id")
            params.append(int(entity_id))

        if document_type_code:
            where_conditions.append("bt.document_type_code = :document_type")
            params.append(document_type_code)

        if document_number:
            where_conditions.append("bt.document_number LIKE :document_number")
            params.append(f"%{document_number}%")

        if currency_code:
            where_conditions.append("bt.currency_code = :currency_code")
            params.append(currency_code)

        if status:
            where_conditions.append("bt.status = :status")
            params.append(status)

        if date_from:
            where_conditions.append("bt.document_date >= TO_DATE(:date_from, 'YYYY-MM-DD')")
            params.append(date_from)

        if date_to:
            where_conditions.append("bt.document_date <= TO_DATE(:date_to, 'YYYY-MM-DD')")
            params.append(date_to)

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # استعلام العد الإجمالي
        count_sql = f"""
        SELECT COUNT(*)
        FROM BALANCE_TRANSACTIONS bt
        WHERE {where_clause}
        """

        count_result = oracle_mgr.execute_query(count_sql, params)
        total_count = count_result[0][0] if count_result else 0

        # استعلام البيانات مع التصفح
        offset = (page - 1) * per_page

        data_sql = f"""
        SELECT * FROM (
            SELECT
                bt.id, bt.entity_type_code, et.entity_name_ar, bt.entity_id,
                CASE
                    WHEN bt.entity_type_code = 'SUPPLIER' THEN
                        (SELECT name FROM SUPPLIERS WHERE id = bt.entity_id AND ROWNUM = 1)
                    WHEN bt.entity_type_code = 'CUSTOMER' THEN
                        (SELECT name FROM CUSTOMERS WHERE id = bt.entity_id AND ROWNUM = 1)
                    ELSE 'غير محدد'
                END as entity_name,
                bt.document_type_code, dt.document_name_ar, bt.document_number,
                bt.document_date, bt.currency_code, bt.debit_amount, bt.credit_amount,
                (bt.debit_amount - bt.credit_amount) as net_amount,
                bt.exchange_rate, bt.base_currency_debit, bt.base_currency_credit,
                bt.description, bt.reference_number, bt.status,
                CASE bt.status
                    WHEN 'POSTED' THEN 'مرحل'
                    WHEN 'REVERSED' THEN 'معكوس'
                    WHEN 'CANCELLED' THEN 'ملغي'
                    WHEN 'DRAFT' THEN 'مسودة'
                    ELSE bt.status
                END as status_ar,
                bt.created_date, bt.created_by, bt.updated_date, bt.updated_by,
                ROW_NUMBER() OVER (ORDER BY bt.created_date DESC) as rn
            FROM BALANCE_TRANSACTIONS bt
            JOIN ENTITY_TYPES et ON bt.entity_type_code = et.entity_type_code
            JOIN DOCUMENT_TYPES dt ON bt.document_type_code = dt.document_type_code
            WHERE {where_clause}
        ) WHERE rn BETWEEN :offset + 1 AND :offset + :per_page
        """

        params.extend([offset, offset, per_page])
        results = oracle_mgr.execute_query(data_sql, params)
        oracle_mgr.disconnect()

        # تحويل النتائج
        transactions = []
        if results:
            for row in results:
                transactions.append({
                    'id': row[0],
                    'entity_type_code': row[1],
                    'entity_type_name_ar': row[2],
                    'entity_id': row[3],
                    'entity_name': row[4],
                    'document_type_code': row[5],
                    'document_name_ar': row[6],
                    'document_number': row[7],
                    'document_date': row[8].strftime('%Y-%m-%d') if row[8] else None,
                    'currency_code': row[9],
                    'debit_amount': float(row[10]) if row[10] else 0,
                    'credit_amount': float(row[11]) if row[11] else 0,
                    'net_amount': float(row[12]) if row[12] else 0,
                    'exchange_rate': float(row[13]) if row[13] else 1,
                    'base_currency_debit': float(row[14]) if row[14] else 0,
                    'base_currency_credit': float(row[15]) if row[15] else 0,
                    'description': row[16],
                    'reference_number': row[17],
                    'status': row[18],
                    'status_ar': row[19],
                    'created_date': row[20].strftime('%Y-%m-%d %H:%M:%S') if row[20] else None,
                    'created_by': row[21],
                    'updated_date': row[22].strftime('%Y-%m-%d %H:%M:%S') if row[22] else None,
                    'updated_by': row[23]
                })

        # حساب معلومات التصفح
        total_pages = (total_count + per_page - 1) // per_page

        return jsonify({
            'success': True,
            'data': transactions,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total_count': total_count,
                'total_pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            }
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب المعاملات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/transactions/<int:transaction_id>', methods=['GET'])
def get_transaction_by_id(transaction_id):
    """جلب معاملة محددة"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        query = """
        SELECT
            bt.id, bt.entity_type_code, et.entity_name_ar, bt.entity_id,
            CASE
                WHEN bt.entity_type_code = 'SUPPLIER' THEN
                    (SELECT name FROM SUPPLIERS WHERE id = bt.entity_id AND ROWNUM = 1)
                WHEN bt.entity_type_code = 'CUSTOMER' THEN
                    (SELECT name FROM CUSTOMERS WHERE id = bt.entity_id AND ROWNUM = 1)
                ELSE 'غير محدد'
            END as entity_name,
            bt.document_type_code, dt.document_name_ar, bt.document_number,
            bt.document_date, bt.currency_code, bt.debit_amount, bt.credit_amount,
            bt.exchange_rate, bt.base_currency_debit, bt.base_currency_credit,
            bt.description, bt.reference_number, bt.status,
            bt.created_date, bt.created_by, bt.updated_date, bt.updated_by
        FROM BALANCE_TRANSACTIONS bt
        JOIN ENTITY_TYPES et ON bt.entity_type_code = et.entity_type_code
        JOIN DOCUMENT_TYPES dt ON bt.document_type_code = dt.document_type_code
        WHERE bt.id = :transaction_id
        """

        result = oracle_mgr.execute_query(query, [transaction_id])
        oracle_mgr.disconnect()

        if not result:
            return jsonify({'success': False, 'message': 'المعاملة غير موجودة'}), 404

        row = result[0]
        transaction = {
            'id': row[0],
            'entity_type_code': row[1],
            'entity_type_name_ar': row[2],
            'entity_id': row[3],
            'entity_name': row[4],
            'document_type_code': row[5],
            'document_name_ar': row[6],
            'document_number': row[7],
            'document_date': row[8].strftime('%Y-%m-%d') if row[8] else None,
            'currency_code': row[9],
            'debit_amount': float(row[10]) if row[10] else 0,
            'credit_amount': float(row[11]) if row[11] else 0,
            'net_amount': float(row[10] - row[11]) if row[10] and row[11] else 0,
            'exchange_rate': float(row[12]) if row[12] else 1,
            'base_currency_debit': float(row[13]) if row[13] else 0,
            'base_currency_credit': float(row[14]) if row[14] else 0,
            'description': row[15],
            'reference_number': row[16],
            'status': row[17],
            'created_date': row[18].strftime('%Y-%m-%d %H:%M:%S') if row[18] else None,
            'created_by': row[19],
            'updated_date': row[20].strftime('%Y-%m-%d %H:%M:%S') if row[20] else None,
            'updated_by': row[21]
        }

        return jsonify({
            'success': True,
            'data': transaction
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب المعاملة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/transactions', methods=['POST'])
def create_transaction():
    """إضافة معاملة جديدة (استخدام الإجراء المخزن ADD_TRANSACTION)"""
    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        required_fields = ['entity_type_code', 'entity_id', 'document_type_code',
                          'document_number', 'document_date', 'currency_code']

        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            return jsonify({
                'success': False,
                'message': f'الحقول التالية مطلوبة: {", ".join(missing_fields)}'
            }), 400

        # التحقق من وجود مبلغ مدين أو دائن
        debit_amount = data.get('debit_amount', 0)
        credit_amount = data.get('credit_amount', 0)

        if debit_amount == 0 and credit_amount == 0:
            return jsonify({
                'success': False,
                'message': 'يجب أن يكون هناك مبلغ مدين أو دائن'
            }), 400

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # استدعاء الإجراء المخزن ADD_TRANSACTION
        call_sql = """
        DECLARE
            v_result VARCHAR2(4000);
            v_transaction_id NUMBER;
        BEGIN
            ADD_TRANSACTION(
                p_entity_type_code => :1,
                p_entity_id => :2,
                p_document_type_code => :3,
                p_document_number => :4,
                p_document_date => TO_DATE(:5, 'YYYY-MM-DD'),
                p_currency_code => :6,
                p_debit_amount => :7,
                p_credit_amount => :8,
                p_exchange_rate => :9,
                p_description => :10,
                p_reference_number => :11,
                p_created_by => :12,
                p_result => v_result,
                p_transaction_id => v_transaction_id
            );
            :13 := v_result;
            :14 := v_transaction_id;
        END;
        """

        # تحضير المعاملات
        params = [
            data['entity_type_code'],
            data['entity_id'],
            data['document_type_code'],
            data['document_number'],
            data['document_date'],
            data['currency_code'],
            debit_amount,
            credit_amount,
            data.get('exchange_rate', 1),
            data.get('description'),
            data.get('reference_number'),
            data.get('created_by', 1),
            None, None  # للنتيجة ومعرف المعاملة
        ]

        # تنفيذ الإجراء
        cursor = oracle_mgr.connection.cursor()
        result_var = cursor.var(str)
        transaction_id_var = cursor.var(int)
        params[12] = result_var
        params[13] = transaction_id_var

        cursor.execute(call_sql, params)
        result = result_var.getvalue()
        transaction_id = transaction_id_var.getvalue()

        cursor.close()
        oracle_mgr.disconnect()

        # تحليل النتيجة
        if result.startswith('SUCCESS:'):
            logger.info(f"✅ تم إنشاء معاملة جديدة برقم: {transaction_id}")
            return jsonify({
                'success': True,
                'message': result.replace('SUCCESS: ', ''),
                'data': {'id': transaction_id},
                'result': result
            }), 201
        else:
            logger.error(f"❌ خطأ في إنشاء المعاملة: {result}")
            return jsonify({
                'success': False,
                'message': result.replace('ERROR: ', ''),
                'result': result
            }), 400

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء المعاملة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/transactions/<int:transaction_id>/reverse', methods=['POST'])
def reverse_transaction(transaction_id):
    """عكس معاملة (استخدام الإجراء المخزن REVERSE_TRANSACTION)"""
    try:
        data = request.get_json()
        reversal_reason = data.get('reversal_reason', 'عكس معاملة عبر API')
        reversed_by = data.get('reversed_by', 1)

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # استدعاء الإجراء المخزن REVERSE_TRANSACTION
        call_sql = """
        DECLARE
            v_result VARCHAR2(4000);
        BEGIN
            REVERSE_TRANSACTION(
                p_transaction_id => :1,
                p_reversal_reason => :2,
                p_reversed_by => :3,
                p_result => v_result
            );
            :4 := v_result;
        END;
        """

        # تنفيذ الإجراء
        cursor = oracle_mgr.connection.cursor()
        result_var = cursor.var(str)
        cursor.execute(call_sql, [transaction_id, reversal_reason, reversed_by, result_var])
        result = result_var.getvalue()
        cursor.close()

        oracle_mgr.disconnect()

        # تحليل النتيجة
        if result.startswith('SUCCESS:'):
            logger.info(f"✅ تم عكس المعاملة رقم: {transaction_id}")
            return jsonify({
                'success': True,
                'message': result.replace('SUCCESS: ', ''),
                'result': result
            })
        else:
            logger.error(f"❌ خطأ في عكس المعاملة: {result}")
            return jsonify({
                'success': False,
                'message': result.replace('ERROR: ', ''),
                'result': result
            }), 400

    except Exception as e:
        logger.error(f"❌ خطأ في عكس المعاملة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/transactions/<int:transaction_id>/update', methods=['PUT'])
def update_transaction(transaction_id):
    """تعديل معاملة (للمعاملات غير المرحلة فقط)"""
    try:
        data = request.get_json()

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # التحقق من وجود المعاملة وحالتها
        check_sql = """
        SELECT status FROM BALANCE_TRANSACTIONS
        WHERE id = :1
        """

        check_result = oracle_mgr.execute_query(check_sql, [transaction_id])
        if not check_result:
            oracle_mgr.disconnect()
            return jsonify({'success': False, 'message': 'المعاملة غير موجودة'}), 404

        current_status = check_result[0][0]
        if current_status != 'DRAFT':
            oracle_mgr.disconnect()
            return jsonify({'success': False, 'message': f'لا يمكن تعديل معاملة في حالة {current_status}'}), 400

        # بناء استعلام التحديث
        update_fields = []
        params = []

        updatable_fields = {
            'document_number': 'document_number',
            'document_date': 'document_date',
            'debit_amount': 'debit_amount',
            'credit_amount': 'credit_amount',
            'exchange_rate': 'exchange_rate',
            'description': 'description',
            'reference_number': 'reference_number'
        }

        for field, column in updatable_fields.items():
            if field in data:
                if field == 'document_date':
                    update_fields.append(f"{column} = TO_DATE(:{len(params)+1}, 'YYYY-MM-DD')")
                else:
                    update_fields.append(f"{column} = :{len(params)+1}")
                params.append(data[field])

        if not update_fields:
            oracle_mgr.disconnect()
            return jsonify({'success': False, 'message': 'لا توجد حقول للتحديث'}), 400

        # إضافة حقول التحديث التلقائية
        update_fields.extend([
            f"updated_date = CURRENT_TIMESTAMP",
            f"updated_by = :{len(params)+1}"
        ])
        params.append(data.get('updated_by', 1))

        # حساب المبالغ بالعملة الأساسية إذا تم تحديث المبلغ أو سعر الصرف
        if any(field in data for field in ['debit_amount', 'credit_amount', 'exchange_rate']):
            update_fields.extend([
                f"base_currency_debit = debit_amount * NVL(exchange_rate, 1)",
                f"base_currency_credit = credit_amount * NVL(exchange_rate, 1)"
            ])

        # تنفيذ التحديث
        update_sql = f"""
        UPDATE BALANCE_TRANSACTIONS
        SET {', '.join(update_fields)}
        WHERE id = :{len(params)+1}
        """
        params.append(transaction_id)

        oracle_mgr.execute_update(update_sql, params)
        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info(f"✅ تم تحديث المعاملة رقم: {transaction_id}")
        return jsonify({
            'success': True,
            'message': 'تم تحديث المعاملة بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في تحديث المعاملة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/transactions/entity/<entity_type_code>/<int:entity_id>', methods=['GET'])
def get_entity_transactions(entity_type_code, entity_id):
    """جلب معاملات كيان محدد"""
    try:
        # معاملات الفلترة
        currency_code = request.args.get('currency_code')
        status = request.args.get('status')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 50))

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # بناء الاستعلام مع الفلترة
        where_conditions = [
            "bt.entity_type_code = :entity_type",
            "bt.entity_id = :entity_id"
        ]
        params = [entity_type_code, entity_id]

        if currency_code:
            where_conditions.append("bt.currency_code = :currency_code")
            params.append(currency_code)

        if status:
            where_conditions.append("bt.status = :status")
            params.append(status)

        if date_from:
            where_conditions.append("bt.document_date >= TO_DATE(:date_from, 'YYYY-MM-DD')")
            params.append(date_from)

        if date_to:
            where_conditions.append("bt.document_date <= TO_DATE(:date_to, 'YYYY-MM-DD')")
            params.append(date_to)

        where_clause = " AND ".join(where_conditions)

        # استعلام العد الإجمالي
        count_sql = f"""
        SELECT COUNT(*)
        FROM BALANCE_TRANSACTIONS bt
        WHERE {where_clause}
        """

        count_result = oracle_mgr.execute_query(count_sql, params)
        total_count = count_result[0][0] if count_result else 0

        # استعلام البيانات مع التصفح
        offset = (page - 1) * per_page

        data_sql = f"""
        SELECT * FROM (
            SELECT
                bt.id, bt.document_type_code, dt.document_name_ar, bt.document_number,
                bt.document_date, bt.currency_code, bt.debit_amount, bt.credit_amount,
                (bt.debit_amount - bt.credit_amount) as net_amount,
                bt.description, bt.reference_number, bt.status,
                bt.created_date, bt.created_by,
                ROW_NUMBER() OVER (ORDER BY bt.document_date DESC, bt.created_date DESC) as rn
            FROM BALANCE_TRANSACTIONS bt
            JOIN DOCUMENT_TYPES dt ON bt.document_type_code = dt.document_type_code
            WHERE {where_clause}
        ) WHERE rn BETWEEN :offset + 1 AND :offset + :per_page
        """

        params.extend([offset, offset, per_page])
        results = oracle_mgr.execute_query(data_sql, params)
        oracle_mgr.disconnect()

        # تحويل النتائج
        transactions = []
        if results:
            for row in results:
                transactions.append({
                    'id': row[0],
                    'document_type_code': row[1],
                    'document_name_ar': row[2],
                    'document_number': row[3],
                    'document_date': row[4].strftime('%Y-%m-%d') if row[4] else None,
                    'currency_code': row[5],
                    'debit_amount': float(row[6]) if row[6] else 0,
                    'credit_amount': float(row[7]) if row[7] else 0,
                    'net_amount': float(row[8]) if row[8] else 0,
                    'description': row[9],
                    'reference_number': row[10],
                    'status': row[11],
                    'created_date': row[12].strftime('%Y-%m-%d %H:%M:%S') if row[12] else None,
                    'created_by': row[13]
                })

        # حساب معلومات التصفح
        total_pages = (total_count + per_page - 1) // per_page

        return jsonify({
            'success': True,
            'data': transactions,
            'entity_info': {
                'entity_type_code': entity_type_code,
                'entity_id': entity_id
            },
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total_count': total_count,
                'total_pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            }
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب معاملات الكيان: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

# ==================== APIs التقارير والاستعلامات ====================

@central_balances_bp.route('/api/reports/trial_balance', methods=['GET'])
def get_trial_balance():
    """ميزان المراجعة الشامل"""
    try:
        # معاملات الفلترة
        entity_type_code = request.args.get('entity_type_code')
        currency_code = request.args.get('currency_code')
        activity_status = request.args.get('activity_status')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 100))

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # بناء الاستعلام مع الفلترة
        where_conditions = []
        params = []

        if entity_type_code:
            where_conditions.append("entity_type_code = :entity_type")
            params.append(entity_type_code)

        if currency_code:
            where_conditions.append("currency_code = :currency_code")
            params.append(currency_code)

        if activity_status:
            where_conditions.append("activity_status = :activity_status")
            params.append(activity_status)

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # استعلام العد الإجمالي
        count_sql = f"""
        SELECT COUNT(*) FROM V_TRIAL_BALANCE WHERE {where_clause}
        """

        count_result = oracle_mgr.execute_query(count_sql, params)
        total_count = count_result[0][0] if count_result else 0

        # استعلام البيانات مع التصفح
        offset = (page - 1) * per_page

        data_sql = f"""
        SELECT * FROM (
            SELECT
                entity_type_code, entity_type_name_ar, entity_id, entity_name,
                currency_code, opening_balance, debit_amount, credit_amount,
                current_balance, debit_balance, credit_balance,
                total_transactions_count, last_transaction_date, activity_status,
                ROW_NUMBER() OVER (ORDER BY entity_type_code, entity_id) as rn
            FROM V_TRIAL_BALANCE
            WHERE {where_clause}
        ) WHERE rn BETWEEN :offset + 1 AND :offset + :per_page
        """

        params.extend([offset, offset, per_page])
        results = oracle_mgr.execute_query(data_sql, params)

        # حساب الإجماليات
        totals_sql = f"""
        SELECT
            currency_code,
            SUM(opening_balance) as total_opening,
            SUM(debit_amount) as total_debit,
            SUM(credit_amount) as total_credit,
            SUM(current_balance) as total_current,
            SUM(debit_balance) as total_debit_balance,
            SUM(credit_balance) as total_credit_balance,
            COUNT(*) as entities_count
        FROM V_TRIAL_BALANCE
        WHERE {where_clause}
        GROUP BY currency_code
        ORDER BY currency_code
        """

        totals_result = oracle_mgr.execute_query(totals_sql, params[:len(params)-3])
        oracle_mgr.disconnect()

        # تحويل النتائج
        trial_balance = []
        if results:
            for row in results:
                trial_balance.append({
                    'entity_type_code': row[0],
                    'entity_type_name_ar': row[1],
                    'entity_id': row[2],
                    'entity_name': row[3],
                    'currency_code': row[4],
                    'opening_balance': float(row[5]) if row[5] else 0,
                    'debit_amount': float(row[6]) if row[6] else 0,
                    'credit_amount': float(row[7]) if row[7] else 0,
                    'current_balance': float(row[8]) if row[8] else 0,
                    'debit_balance': float(row[9]) if row[9] else 0,
                    'credit_balance': float(row[10]) if row[10] else 0,
                    'total_transactions_count': row[11],
                    'last_transaction_date': row[12].strftime('%Y-%m-%d') if row[12] else None,
                    'activity_status': row[13]
                })

        # تحويل الإجماليات
        totals = []
        if totals_result:
            for row in totals_result:
                totals.append({
                    'currency_code': row[0],
                    'total_opening': float(row[1]) if row[1] else 0,
                    'total_debit': float(row[2]) if row[2] else 0,
                    'total_credit': float(row[3]) if row[3] else 0,
                    'total_current': float(row[4]) if row[4] else 0,
                    'total_debit_balance': float(row[5]) if row[5] else 0,
                    'total_credit_balance': float(row[6]) if row[6] else 0,
                    'entities_count': row[7]
                })

        # حساب معلومات التصفح
        total_pages = (total_count + per_page - 1) // per_page

        return jsonify({
            'success': True,
            'data': trial_balance,
            'totals': totals,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total_count': total_count,
                'total_pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            }
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب ميزان المراجعة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/reports/aging_report', methods=['GET'])
def get_aging_report():
    """تقرير أعمار الديون"""
    try:
        # معاملات الفلترة
        entity_type_code = request.args.get('entity_type_code')
        currency_code = request.args.get('currency_code')
        aging_category = request.args.get('aging_category')
        risk_level = request.args.get('risk_level')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 100))

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # بناء الاستعلام مع الفلترة
        where_conditions = []
        params = []

        if entity_type_code:
            where_conditions.append("entity_type_code = :entity_type")
            params.append(entity_type_code)

        if currency_code:
            where_conditions.append("currency_code = :currency_code")
            params.append(currency_code)

        if aging_category:
            where_conditions.append("aging_category = :aging_category")
            params.append(aging_category)

        if risk_level:
            where_conditions.append("risk_level = :risk_level")
            params.append(risk_level)

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # استعلام العد الإجمالي
        count_sql = f"""
        SELECT COUNT(*) FROM V_AGING_REPORT WHERE {where_clause}
        """

        count_result = oracle_mgr.execute_query(count_sql, params)
        total_count = count_result[0][0] if count_result else 0

        # استعلام البيانات مع التصفح
        offset = (page - 1) * per_page

        data_sql = f"""
        SELECT * FROM (
            SELECT
                entity_type_code, entity_type_name_ar, entity_id, entity_name,
                currency_code, current_balance, last_transaction_date,
                days_since_last_transaction, aging_category, risk_level,
                total_transactions_count, average_days,
                ROW_NUMBER() OVER (ORDER BY days_since_last_transaction DESC, ABS(current_balance) DESC) as rn
            FROM V_AGING_REPORT
            WHERE {where_clause}
        ) WHERE rn BETWEEN :offset + 1 AND :offset + :per_page
        """

        params.extend([offset, offset, per_page])
        results = oracle_mgr.execute_query(data_sql, params)

        # حساب إحصائيات الأعمار
        aging_stats_sql = f"""
        SELECT
            aging_category,
            currency_code,
            COUNT(*) as entities_count,
            SUM(ABS(current_balance)) as total_amount,
            AVG(days_since_last_transaction) as avg_days
        FROM V_AGING_REPORT
        WHERE {where_clause}
        GROUP BY aging_category, currency_code
        ORDER BY aging_category, currency_code
        """

        aging_stats_result = oracle_mgr.execute_query(aging_stats_sql, params[:len(params)-3])
        oracle_mgr.disconnect()

        # تحويل النتائج
        aging_report = []
        if results:
            for row in results:
                aging_report.append({
                    'entity_type_code': row[0],
                    'entity_type_name_ar': row[1],
                    'entity_id': row[2],
                    'entity_name': row[3],
                    'currency_code': row[4],
                    'current_balance': float(row[5]) if row[5] else 0,
                    'last_transaction_date': row[6].strftime('%Y-%m-%d') if row[6] else None,
                    'days_since_last_transaction': row[7],
                    'aging_category': row[8],
                    'risk_level': row[9],
                    'total_transactions_count': row[10],
                    'average_days': float(row[11]) if row[11] else 0
                })

        # تحويل إحصائيات الأعمار
        aging_stats = []
        if aging_stats_result:
            for row in aging_stats_result:
                aging_stats.append({
                    'aging_category': row[0],
                    'currency_code': row[1],
                    'entities_count': row[2],
                    'total_amount': float(row[3]) if row[3] else 0,
                    'avg_days': float(row[4]) if row[4] else 0
                })

        # حساب معلومات التصفح
        total_pages = (total_count + per_page - 1) // per_page

        return jsonify({
            'success': True,
            'data': aging_report,
            'aging_stats': aging_stats,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total_count': total_count,
                'total_pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            }
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب تقرير أعمار الديون: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/reports/cash_flow', methods=['GET'])
def get_cash_flow_analysis():
    """تحليل التدفقات النقدية"""
    try:
        # معاملات الفلترة
        currency_code = request.args.get('currency_code')
        document_category = request.args.get('document_category')
        year_month = request.args.get('year_month')
        year = request.args.get('year')
        quarter = request.args.get('quarter')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 50))

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # بناء الاستعلام مع الفلترة
        where_conditions = []
        params = []

        if currency_code:
            where_conditions.append("currency_code = :currency_code")
            params.append(currency_code)

        if document_category:
            where_conditions.append("document_category = :document_category")
            params.append(document_category)

        if year_month:
            where_conditions.append("year_month = :year_month")
            params.append(year_month)

        if year:
            where_conditions.append("year = :year")
            params.append(year)

        if quarter:
            where_conditions.append("quarter = :quarter")
            params.append(quarter)

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # استعلام البيانات
        data_sql = f"""
        SELECT * FROM (
            SELECT
                year_month, year, month, quarter, currency_code, document_category,
                cash_flow_type, transactions_count, total_debit, total_credit,
                net_flow, cash_inflow, cash_outflow, unique_entities, avg_transaction_amount,
                ROW_NUMBER() OVER (ORDER BY year_month DESC, document_category) as rn
            FROM V_CASH_FLOW_ANALYSIS
            WHERE {where_clause}
        ) WHERE rn BETWEEN :offset + 1 AND :offset + :per_page
        """

        offset = (page - 1) * per_page
        params.extend([offset, offset, per_page])
        results = oracle_mgr.execute_query(data_sql, params)

        # حساب الإجماليات
        totals_sql = f"""
        SELECT
            currency_code,
            SUM(total_debit) as total_debit,
            SUM(total_credit) as total_credit,
            SUM(net_flow) as net_flow,
            SUM(cash_inflow) as total_inflow,
            SUM(cash_outflow) as total_outflow,
            SUM(transactions_count) as total_transactions
        FROM V_CASH_FLOW_ANALYSIS
        WHERE {where_clause}
        GROUP BY currency_code
        ORDER BY currency_code
        """

        totals_result = oracle_mgr.execute_query(totals_sql, params[:len(params)-3])
        oracle_mgr.disconnect()

        # تحويل النتائج
        cash_flow = []
        if results:
            for row in results:
                cash_flow.append({
                    'year_month': row[0],
                    'year': row[1],
                    'month': row[2],
                    'quarter': row[3],
                    'currency_code': row[4],
                    'document_category': row[5],
                    'cash_flow_type': row[6],
                    'transactions_count': row[7],
                    'total_debit': float(row[8]) if row[8] else 0,
                    'total_credit': float(row[9]) if row[9] else 0,
                    'net_flow': float(row[10]) if row[10] else 0,
                    'cash_inflow': float(row[11]) if row[11] else 0,
                    'cash_outflow': float(row[12]) if row[12] else 0,
                    'unique_entities': row[13],
                    'avg_transaction_amount': float(row[14]) if row[14] else 0
                })

        # تحويل الإجماليات
        totals = []
        if totals_result:
            for row in totals_result:
                totals.append({
                    'currency_code': row[0],
                    'total_debit': float(row[1]) if row[1] else 0,
                    'total_credit': float(row[2]) if row[2] else 0,
                    'net_flow': float(row[3]) if row[3] else 0,
                    'total_inflow': float(row[4]) if row[4] else 0,
                    'total_outflow': float(row[5]) if row[5] else 0,
                    'total_transactions': row[6]
                })

        return jsonify({
            'success': True,
            'data': cash_flow,
            'totals': totals
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب تحليل التدفقات النقدية: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/reports/entity_performance', methods=['GET'])
def get_entity_performance():
    """تقرير أداء الكيانات"""
    try:
        # معاملات الفلترة
        entity_type_code = request.args.get('entity_type_code')
        currency_code = request.args.get('currency_code')
        activity_level = request.args.get('activity_level')
        volume_category = request.args.get('volume_category')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 50))

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # بناء الاستعلام مع الفلترة
        where_conditions = []
        params = []

        if entity_type_code:
            where_conditions.append("entity_type_code = :entity_type")
            params.append(entity_type_code)

        if currency_code:
            where_conditions.append("currency_code = :currency_code")
            params.append(currency_code)

        if activity_level:
            where_conditions.append("activity_level = :activity_level")
            params.append(activity_level)

        if volume_category:
            where_conditions.append("volume_category = :volume_category")
            params.append(volume_category)

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # استعلام البيانات مع التصفح
        offset = (page - 1) * per_page

        data_sql = f"""
        SELECT * FROM (
            SELECT
                entity_type_code, entity_type_name_ar, entity_id, entity_name,
                currency_code, current_balance, total_transactions_count,
                total_debit, total_credit, total_activity_volume,
                avg_transaction_amount, last_transaction_date, days_since_last_activity,
                activity_level, volume_category, debit_credit_ratio_percent,
                ROW_NUMBER() OVER (ORDER BY total_activity_volume DESC) as rn
            FROM V_ENTITY_PERFORMANCE_REPORT
            WHERE {where_clause}
        ) WHERE rn BETWEEN :offset + 1 AND :offset + :per_page
        """

        params.extend([offset, offset, per_page])
        results = oracle_mgr.execute_query(data_sql, params)
        oracle_mgr.disconnect()

        # تحويل النتائج
        performance = []
        if results:
            for row in results:
                performance.append({
                    'entity_type_code': row[0],
                    'entity_type_name_ar': row[1],
                    'entity_id': row[2],
                    'entity_name': row[3],
                    'currency_code': row[4],
                    'current_balance': float(row[5]) if row[5] else 0,
                    'total_transactions_count': row[6],
                    'total_debit': float(row[7]) if row[7] else 0,
                    'total_credit': float(row[8]) if row[8] else 0,
                    'total_activity_volume': float(row[9]) if row[9] else 0,
                    'avg_transaction_amount': float(row[10]) if row[10] else 0,
                    'last_transaction_date': row[11].strftime('%Y-%m-%d') if row[11] else None,
                    'days_since_last_activity': row[12],
                    'activity_level': row[13],
                    'volume_category': row[14],
                    'debit_credit_ratio_percent': float(row[15]) if row[15] else None
                })

        return jsonify({
            'success': True,
            'data': performance
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب تقرير أداء الكيانات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/reports/monthly_summary', methods=['GET'])
def get_monthly_summary():
    """الملخص الشهري للمعاملات"""
    try:
        # معاملات الفلترة
        entity_type_code = request.args.get('entity_type_code')
        currency_code = request.args.get('currency_code')
        year = request.args.get('year')
        month = request.args.get('month')

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # بناء الاستعلام مع الفلترة
        where_conditions = []
        params = []

        if entity_type_code:
            where_conditions.append("entity_type_code = :entity_type")
            params.append(entity_type_code)

        if currency_code:
            where_conditions.append("currency_code = :currency_code")
            params.append(currency_code)

        if year:
            where_conditions.append("year = :year")
            params.append(year)

        if month:
            where_conditions.append("month = :month")
            params.append(month)

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # استعلام البيانات
        data_sql = f"""
        SELECT
            year_month, year, month, month_name_ar, entity_type_code,
            entity_type_name_ar, currency_code, transactions_count,
            total_debit_amount, total_credit_amount, net_amount,
            unique_entities_count, unique_document_types_count,
            avg_transaction_amount, first_transaction_date, last_transaction_date
        FROM V_MONTHLY_TRANSACTIONS_SUMMARY
        WHERE {where_clause}
        ORDER BY year_month DESC, entity_type_code
        """

        results = oracle_mgr.execute_query(data_sql, params)
        oracle_mgr.disconnect()

        # تحويل النتائج
        monthly_summary = []
        if results:
            for row in results:
                monthly_summary.append({
                    'year_month': row[0],
                    'year': row[1],
                    'month': row[2],
                    'month_name_ar': row[3],
                    'entity_type_code': row[4],
                    'entity_type_name_ar': row[5],
                    'currency_code': row[6],
                    'transactions_count': row[7],
                    'total_debit_amount': float(row[8]) if row[8] else 0,
                    'total_credit_amount': float(row[9]) if row[9] else 0,
                    'net_amount': float(row[10]) if row[10] else 0,
                    'unique_entities_count': row[11],
                    'unique_document_types_count': row[12],
                    'avg_transaction_amount': float(row[13]) if row[13] else 0,
                    'first_transaction_date': row[14].strftime('%Y-%m-%d') if row[14] else None,
                    'last_transaction_date': row[15].strftime('%Y-%m-%d') if row[15] else None
                })

        return jsonify({
            'success': True,
            'data': monthly_summary
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب الملخص الشهري: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/reports/balances_summary', methods=['GET'])
def get_balances_summary():
    """ملخص الأرصدة حسب نوع الكيان"""
    try:
        # معاملات الفلترة
        entity_type_code = request.args.get('entity_type_code')
        currency_code = request.args.get('currency_code')

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # بناء الاستعلام مع الفلترة
        where_conditions = []
        params = []

        if entity_type_code:
            where_conditions.append("entity_type_code = :entity_type")
            params.append(entity_type_code)

        if currency_code:
            where_conditions.append("currency_code = :currency_code")
            params.append(currency_code)

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # استعلام البيانات
        data_sql = f"""
        SELECT
            entity_type_code, entity_name_ar, entity_name_en, module_name,
            currency_code, entities_count, total_opening_balance,
            total_debit_amount, total_credit_amount, total_current_balance,
            total_debit_balance, total_credit_balance, debit_entities_count,
            credit_entities_count, zero_balance_entities_count,
            total_transactions, latest_transaction_date, avg_days
        FROM V_BALANCES_SUMMARY_BY_TYPE
        WHERE {where_clause}
        ORDER BY entity_type_code, currency_code
        """

        results = oracle_mgr.execute_query(data_sql, params)
        oracle_mgr.disconnect()

        # تحويل النتائج
        balances_summary = []
        if results:
            for row in results:
                balances_summary.append({
                    'entity_type_code': row[0],
                    'entity_name_ar': row[1],
                    'entity_name_en': row[2],
                    'module_name': row[3],
                    'currency_code': row[4],
                    'entities_count': row[5],
                    'total_opening_balance': float(row[6]) if row[6] else 0,
                    'total_debit_amount': float(row[7]) if row[7] else 0,
                    'total_credit_amount': float(row[8]) if row[8] else 0,
                    'total_current_balance': float(row[9]) if row[9] else 0,
                    'total_debit_balance': float(row[10]) if row[10] else 0,
                    'total_credit_balance': float(row[11]) if row[11] else 0,
                    'debit_entities_count': row[12],
                    'credit_entities_count': row[13],
                    'zero_balance_entities_count': row[14],
                    'total_transactions': row[15],
                    'latest_transaction_date': row[16].strftime('%Y-%m-%d') if row[16] else None,
                    'avg_days': float(row[17]) if row[17] else 0
                })

        return jsonify({
            'success': True,
            'data': balances_summary
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب ملخص الأرصدة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

# ==================== APIs الإحصائيات والتحليلات ====================

@central_balances_bp.route('/api/analytics/dashboard_stats', methods=['GET'])
def get_dashboard_stats():
    """إحصائيات لوحة المعلومات الرئيسية"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # إحصائيات الأرصدة الجارية
        balances_stats_sql = """
        SELECT
            COUNT(*) as total_entities,
            COUNT(CASE WHEN current_balance > 0 THEN 1 END) as debit_entities,
            COUNT(CASE WHEN current_balance < 0 THEN 1 END) as credit_entities,
            COUNT(CASE WHEN current_balance = 0 THEN 1 END) as zero_entities,
            SUM(CASE WHEN current_balance > 0 THEN current_balance ELSE 0 END) as total_debit_balance,
            SUM(CASE WHEN current_balance < 0 THEN ABS(current_balance) ELSE 0 END) as total_credit_balance,
            COUNT(DISTINCT entity_type_code) as entity_types_count,
            COUNT(DISTINCT currency_code) as currencies_count
        FROM CURRENT_BALANCES
        """

        balances_result = oracle_mgr.execute_query(balances_stats_sql)

        # إحصائيات المعاملات
        transactions_stats_sql = """
        SELECT
            COUNT(*) as total_transactions,
            COUNT(CASE WHEN status = 'POSTED' THEN 1 END) as posted_transactions,
            COUNT(CASE WHEN status = 'DRAFT' THEN 1 END) as draft_transactions,
            COUNT(CASE WHEN status = 'REVERSED' THEN 1 END) as reversed_transactions,
            SUM(debit_amount) as total_debit_amount,
            SUM(credit_amount) as total_credit_amount,
            COUNT(DISTINCT entity_type_code) as active_entity_types,
            COUNT(DISTINCT document_type_code) as document_types_used,
            MAX(document_date) as latest_transaction_date,
            MIN(document_date) as earliest_transaction_date
        FROM BALANCE_TRANSACTIONS
        WHERE document_date >= SYSDATE - 365
        """

        transactions_result = oracle_mgr.execute_query(transactions_stats_sql)

        # إحصائيات اليوم
        today_stats_sql = """
        SELECT
            COUNT(*) as today_transactions,
            SUM(debit_amount) as today_debit,
            SUM(credit_amount) as today_credit,
            COUNT(DISTINCT entity_id) as today_active_entities
        FROM BALANCE_TRANSACTIONS
        WHERE TRUNC(document_date) = TRUNC(SYSDATE)
        AND status = 'POSTED'
        """

        today_result = oracle_mgr.execute_query(today_stats_sql)

        # إحصائيات الشهر الحالي
        month_stats_sql = """
        SELECT
            COUNT(*) as month_transactions,
            SUM(debit_amount) as month_debit,
            SUM(credit_amount) as month_credit,
            COUNT(DISTINCT entity_id) as month_active_entities
        FROM BALANCE_TRANSACTIONS
        WHERE TO_CHAR(document_date, 'YYYY-MM') = TO_CHAR(SYSDATE, 'YYYY-MM')
        AND status = 'POSTED'
        """

        month_result = oracle_mgr.execute_query(month_stats_sql)

        # أكبر الأرصدة المدينة والدائنة
        top_balances_sql = """
        SELECT
            entity_type_code, entity_id, currency_code, current_balance,
            CASE
                WHEN entity_type_code = 'SUPPLIER' THEN
                    (SELECT name FROM SUPPLIERS WHERE id = entity_id AND ROWNUM = 1)
                WHEN entity_type_code = 'CUSTOMER' THEN
                    (SELECT name FROM CUSTOMERS WHERE id = entity_id AND ROWNUM = 1)
                ELSE 'غير محدد'
            END as entity_name
        FROM (
            SELECT entity_type_code, entity_id, currency_code, current_balance,
                   ROW_NUMBER() OVER (ORDER BY ABS(current_balance) DESC) as rn
            FROM CURRENT_BALANCES
            WHERE current_balance != 0
        ) WHERE rn <= 5
        """

        top_balances_result = oracle_mgr.execute_query(top_balances_sql)
        oracle_mgr.disconnect()

        # تحويل النتائج
        dashboard_stats = {}

        # إحصائيات الأرصدة
        if balances_result:
            row = balances_result[0]
            dashboard_stats['balances'] = {
                'total_entities': row[0] or 0,
                'debit_entities': row[1] or 0,
                'credit_entities': row[2] or 0,
                'zero_entities': row[3] or 0,
                'total_debit_balance': float(row[4]) if row[4] else 0,
                'total_credit_balance': float(row[5]) if row[5] else 0,
                'entity_types_count': row[6] or 0,
                'currencies_count': row[7] or 0
            }

        # إحصائيات المعاملات
        if transactions_result:
            row = transactions_result[0]
            dashboard_stats['transactions'] = {
                'total_transactions': row[0] or 0,
                'posted_transactions': row[1] or 0,
                'draft_transactions': row[2] or 0,
                'reversed_transactions': row[3] or 0,
                'total_debit_amount': float(row[4]) if row[4] else 0,
                'total_credit_amount': float(row[5]) if row[5] else 0,
                'active_entity_types': row[6] or 0,
                'document_types_used': row[7] or 0,
                'latest_transaction_date': row[8].strftime('%Y-%m-%d') if row[8] else None,
                'earliest_transaction_date': row[9].strftime('%Y-%m-%d') if row[9] else None
            }

        # إحصائيات اليوم
        if today_result:
            row = today_result[0]
            dashboard_stats['today'] = {
                'transactions': row[0] or 0,
                'debit_amount': float(row[1]) if row[1] else 0,
                'credit_amount': float(row[2]) if row[2] else 0,
                'active_entities': row[3] or 0
            }

        # إحصائيات الشهر
        if month_result:
            row = month_result[0]
            dashboard_stats['month'] = {
                'transactions': row[0] or 0,
                'debit_amount': float(row[1]) if row[1] else 0,
                'credit_amount': float(row[2]) if row[2] else 0,
                'active_entities': row[3] or 0
            }

        # أكبر الأرصدة
        top_balances = []
        if top_balances_result:
            for row in top_balances_result:
                top_balances.append({
                    'entity_type_code': row[0],
                    'entity_id': row[1],
                    'currency_code': row[2],
                    'current_balance': float(row[3]) if row[3] else 0,
                    'entity_name': row[4]
                })

        dashboard_stats['top_balances'] = top_balances

        return jsonify({
            'success': True,
            'data': dashboard_stats
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب إحصائيات لوحة المعلومات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/analytics/trends', methods=['GET'])
def get_trends_analysis():
    """تحليل الاتجاهات والنمو"""
    try:
        # معاملات الفلترة
        period = request.args.get('period', 'monthly')  # daily, weekly, monthly, quarterly
        months_back = int(request.args.get('months_back', 12))
        entity_type_code = request.args.get('entity_type_code')
        currency_code = request.args.get('currency_code')

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # تحديد تنسيق التاريخ حسب الفترة
        date_format = {
            'daily': 'YYYY-MM-DD',
            'weekly': 'YYYY-IW',
            'monthly': 'YYYY-MM',
            'quarterly': 'YYYY-Q'
        }.get(period, 'YYYY-MM')

        # بناء الاستعلام مع الفلترة
        where_conditions = [
            f"document_date >= ADD_MONTHS(SYSDATE, -{months_back})",
            "status = 'POSTED'"
        ]
        params = []

        if entity_type_code:
            where_conditions.append("entity_type_code = :entity_type")
            params.append(entity_type_code)

        if currency_code:
            where_conditions.append("currency_code = :currency_code")
            params.append(currency_code)

        where_clause = " AND ".join(where_conditions)

        # استعلام الاتجاهات
        trends_sql = f"""
        SELECT
            TO_CHAR(document_date, '{date_format}') as period,
            COUNT(*) as transactions_count,
            SUM(debit_amount) as total_debit,
            SUM(credit_amount) as total_credit,
            SUM(debit_amount - credit_amount) as net_amount,
            COUNT(DISTINCT entity_id) as unique_entities,
            AVG(debit_amount + credit_amount) as avg_transaction_amount
        FROM BALANCE_TRANSACTIONS
        WHERE {where_clause}
        GROUP BY TO_CHAR(document_date, '{date_format}')
        ORDER BY period
        """

        trends_result = oracle_mgr.execute_query(trends_sql, params)

        # حساب معدلات النمو
        growth_rates = []
        if trends_result and len(trends_result) > 1:
            for i in range(1, len(trends_result)):
                current = trends_result[i]
                previous = trends_result[i-1]

                # حساب معدل النمو للمعاملات
                transactions_growth = 0
                if previous[1] > 0:
                    transactions_growth = ((current[1] - previous[1]) / previous[1]) * 100

                # حساب معدل النمو للمبالغ
                amount_growth = 0
                prev_total = (previous[2] or 0) + (previous[3] or 0)
                curr_total = (current[2] or 0) + (current[3] or 0)
                if prev_total > 0:
                    amount_growth = ((curr_total - prev_total) / prev_total) * 100

                growth_rates.append({
                    'period': current[0],
                    'transactions_growth': round(transactions_growth, 2),
                    'amount_growth': round(amount_growth, 2)
                })

        oracle_mgr.disconnect()

        # تحويل النتائج
        trends = []
        if trends_result:
            for row in trends_result:
                trends.append({
                    'period': row[0],
                    'transactions_count': row[1] or 0,
                    'total_debit': float(row[2]) if row[2] else 0,
                    'total_credit': float(row[3]) if row[3] else 0,
                    'net_amount': float(row[4]) if row[4] else 0,
                    'unique_entities': row[5] or 0,
                    'avg_transaction_amount': float(row[6]) if row[6] else 0
                })

        return jsonify({
            'success': True,
            'data': {
                'trends': trends,
                'growth_rates': growth_rates,
                'period_type': period,
                'months_analyzed': months_back
            }
        })

    except Exception as e:
        logger.error(f"❌ خطأ في تحليل الاتجاهات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/analytics/statistical_summary', methods=['GET'])
def get_statistical_summary():
    """الملخص الإحصائي الشامل"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # استعلام الملخص الإحصائي من View
        stats_sql = """
        SELECT
            report_type, report_name_ar, entity_type_code, entity_type_name_ar,
            currency_code, records_count, total_balance, avg_balance,
            min_balance, max_balance, balance_std_deviation,
            positive_balances_count, negative_balances_count, zero_balances_count,
            total_transactions, avg_transactions_per_entity,
            latest_activity_date, earliest_activity_date
        FROM V_STATISTICAL_SUMMARY
        ORDER BY report_type, entity_type_code, currency_code
        """

        results = oracle_mgr.execute_query(stats_sql)
        oracle_mgr.disconnect()

        # تحويل النتائج وتجميعها
        statistical_summary = {
            'current_balances': [],
            'balance_transactions': []
        }

        if results:
            for row in results:
                summary_item = {
                    'entity_type_code': row[2],
                    'entity_type_name_ar': row[3],
                    'currency_code': row[4],
                    'records_count': row[5] or 0,
                    'total_balance': float(row[6]) if row[6] else 0,
                    'avg_balance': float(row[7]) if row[7] else 0,
                    'min_balance': float(row[8]) if row[8] else 0,
                    'max_balance': float(row[9]) if row[9] else 0,
                    'balance_std_deviation': float(row[10]) if row[10] else 0,
                    'positive_balances_count': row[11] or 0,
                    'negative_balances_count': row[12] or 0,
                    'zero_balances_count': row[13] or 0,
                    'total_transactions': row[14] or 0,
                    'avg_transactions_per_entity': float(row[15]) if row[15] else 0,
                    'latest_activity_date': row[16].strftime('%Y-%m-%d') if row[16] else None,
                    'earliest_activity_date': row[17].strftime('%Y-%m-%d') if row[17] else None
                }

                if row[0] == 'CURRENT_BALANCES':
                    statistical_summary['current_balances'].append(summary_item)
                elif row[0] == 'BALANCE_TRANSACTIONS':
                    statistical_summary['balance_transactions'].append(summary_item)

        return jsonify({
            'success': True,
            'data': statistical_summary
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب الملخص الإحصائي: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/analytics/entity_analysis/<entity_type_code>/<int:entity_id>', methods=['GET'])
def get_entity_analysis(entity_type_code, entity_id):
    """تحليل تفصيلي لكيان محدد"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # معلومات الكيان الأساسية
        entity_info_sql = """
        SELECT
            cb.entity_type_code, et.entity_name_ar, cb.entity_id,
            CASE
                WHEN cb.entity_type_code = 'SUPPLIER' THEN
                    (SELECT name FROM SUPPLIERS WHERE id = cb.entity_id AND ROWNUM = 1)
                WHEN cb.entity_type_code = 'CUSTOMER' THEN
                    (SELECT name FROM CUSTOMERS WHERE id = cb.entity_id AND ROWNUM = 1)
                ELSE 'غير محدد'
            END as entity_name,
            cb.currency_code, cb.opening_balance, cb.debit_amount, cb.credit_amount,
            cb.current_balance, cb.total_transactions_count, cb.last_transaction_date,
            cb.average_days
        FROM CURRENT_BALANCES cb
        JOIN ENTITY_TYPES et ON cb.entity_type_code = et.entity_type_code
        WHERE cb.entity_type_code = :1 AND cb.entity_id = :2
        """

        entity_info_result = oracle_mgr.execute_query(entity_info_sql, [entity_type_code, entity_id])

        # إحصائيات المعاملات حسب نوع الوثيقة
        doc_types_stats_sql = """
        SELECT
            bt.document_type_code, dt.document_name_ar, dt.document_category,
            COUNT(*) as transactions_count,
            SUM(bt.debit_amount) as total_debit,
            SUM(bt.credit_amount) as total_credit,
            AVG(bt.debit_amount + bt.credit_amount) as avg_amount,
            MAX(bt.document_date) as latest_transaction,
            MIN(bt.document_date) as earliest_transaction
        FROM BALANCE_TRANSACTIONS bt
        JOIN DOCUMENT_TYPES dt ON bt.document_type_code = dt.document_type_code
        WHERE bt.entity_type_code = :1 AND bt.entity_id = :2 AND bt.status = 'POSTED'
        GROUP BY bt.document_type_code, dt.document_name_ar, dt.document_category
        ORDER BY transactions_count DESC
        """

        doc_types_result = oracle_mgr.execute_query(doc_types_stats_sql, [entity_type_code, entity_id])

        # تحليل المعاملات الشهرية
        monthly_analysis_sql = """
        SELECT
            TO_CHAR(document_date, 'YYYY-MM') as year_month,
            COUNT(*) as transactions_count,
            SUM(debit_amount) as total_debit,
            SUM(credit_amount) as total_credit,
            SUM(debit_amount - credit_amount) as net_amount
        FROM BALANCE_TRANSACTIONS
        WHERE entity_type_code = :1 AND entity_id = :2 AND status = 'POSTED'
        AND document_date >= ADD_MONTHS(SYSDATE, -12)
        GROUP BY TO_CHAR(document_date, 'YYYY-MM')
        ORDER BY year_month
        """

        monthly_result = oracle_mgr.execute_query(monthly_analysis_sql, [entity_type_code, entity_id])

        # آخر المعاملات
        recent_transactions_sql = """
        SELECT
            bt.id, bt.document_type_code, dt.document_name_ar, bt.document_number,
            bt.document_date, bt.debit_amount, bt.credit_amount, bt.description,
            bt.status
        FROM BALANCE_TRANSACTIONS bt
        JOIN DOCUMENT_TYPES dt ON bt.document_type_code = dt.document_type_code
        WHERE bt.entity_type_code = :1 AND bt.entity_id = :2
        ORDER BY bt.document_date DESC, bt.created_date DESC
        FETCH FIRST 10 ROWS ONLY
        """

        recent_result = oracle_mgr.execute_query(recent_transactions_sql, [entity_type_code, entity_id])
        oracle_mgr.disconnect()

        # تحويل النتائج
        entity_analysis = {}

        # معلومات الكيان
        if entity_info_result:
            balances = []
            for row in entity_info_result:
                balances.append({
                    'currency_code': row[4],
                    'opening_balance': float(row[5]) if row[5] else 0,
                    'debit_amount': float(row[6]) if row[6] else 0,
                    'credit_amount': float(row[7]) if row[7] else 0,
                    'current_balance': float(row[8]) if row[8] else 0,
                    'total_transactions_count': row[9] or 0,
                    'last_transaction_date': row[10].strftime('%Y-%m-%d') if row[10] else None,
                    'average_days': float(row[11]) if row[11] else 0
                })

            entity_analysis['entity_info'] = {
                'entity_type_code': entity_info_result[0][0],
                'entity_type_name_ar': entity_info_result[0][1],
                'entity_id': entity_info_result[0][2],
                'entity_name': entity_info_result[0][3],
                'balances': balances
            }

        # إحصائيات أنواع الوثائق
        doc_types_stats = []
        if doc_types_result:
            for row in doc_types_result:
                doc_types_stats.append({
                    'document_type_code': row[0],
                    'document_name_ar': row[1],
                    'document_category': row[2],
                    'transactions_count': row[3] or 0,
                    'total_debit': float(row[4]) if row[4] else 0,
                    'total_credit': float(row[5]) if row[5] else 0,
                    'avg_amount': float(row[6]) if row[6] else 0,
                    'latest_transaction': row[7].strftime('%Y-%m-%d') if row[7] else None,
                    'earliest_transaction': row[8].strftime('%Y-%m-%d') if row[8] else None
                })

        entity_analysis['document_types_stats'] = doc_types_stats

        # التحليل الشهري
        monthly_analysis = []
        if monthly_result:
            for row in monthly_result:
                monthly_analysis.append({
                    'year_month': row[0],
                    'transactions_count': row[1] or 0,
                    'total_debit': float(row[2]) if row[2] else 0,
                    'total_credit': float(row[3]) if row[3] else 0,
                    'net_amount': float(row[4]) if row[4] else 0
                })

        entity_analysis['monthly_analysis'] = monthly_analysis

        # آخر المعاملات
        recent_transactions = []
        if recent_result:
            for row in recent_result:
                recent_transactions.append({
                    'id': row[0],
                    'document_type_code': row[1],
                    'document_name_ar': row[2],
                    'document_number': row[3],
                    'document_date': row[4].strftime('%Y-%m-%d') if row[4] else None,
                    'debit_amount': float(row[5]) if row[5] else 0,
                    'credit_amount': float(row[6]) if row[6] else 0,
                    'description': row[7],
                    'status': row[8]
                })

        entity_analysis['recent_transactions'] = recent_transactions

        return jsonify({
            'success': True,
            'data': entity_analysis
        })

    except Exception as e:
        logger.error(f"❌ خطأ في تحليل الكيان: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/analytics/currency_analysis', methods=['GET'])
def get_currency_analysis():
    """تحليل العملات والأرصدة"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # تحليل الأرصدة حسب العملة
        currency_balances_sql = """
        SELECT
            currency_code,
            COUNT(*) as entities_count,
            SUM(current_balance) as total_balance,
            SUM(CASE WHEN current_balance > 0 THEN current_balance ELSE 0 END) as total_debit_balance,
            SUM(CASE WHEN current_balance < 0 THEN ABS(current_balance) ELSE 0 END) as total_credit_balance,
            AVG(current_balance) as avg_balance,
            MIN(current_balance) as min_balance,
            MAX(current_balance) as max_balance,
            COUNT(CASE WHEN current_balance > 0 THEN 1 END) as debit_entities,
            COUNT(CASE WHEN current_balance < 0 THEN 1 END) as credit_entities,
            COUNT(CASE WHEN current_balance = 0 THEN 1 END) as zero_entities
        FROM CURRENT_BALANCES
        GROUP BY currency_code
        ORDER BY entities_count DESC
        """

        currency_balances_result = oracle_mgr.execute_query(currency_balances_sql)

        # تحليل المعاملات حسب العملة
        currency_transactions_sql = """
        SELECT
            currency_code,
            COUNT(*) as transactions_count,
            SUM(debit_amount) as total_debit,
            SUM(credit_amount) as total_credit,
            AVG(debit_amount + credit_amount) as avg_transaction_amount,
            COUNT(DISTINCT entity_id) as active_entities,
            MAX(document_date) as latest_transaction,
            MIN(document_date) as earliest_transaction
        FROM BALANCE_TRANSACTIONS
        WHERE status = 'POSTED'
        GROUP BY currency_code
        ORDER BY transactions_count DESC
        """

        currency_transactions_result = oracle_mgr.execute_query(currency_transactions_sql)
        oracle_mgr.disconnect()

        # تحويل النتائج
        currency_analysis = {
            'balances_by_currency': [],
            'transactions_by_currency': []
        }

        # تحليل الأرصدة
        if currency_balances_result:
            for row in currency_balances_result:
                currency_analysis['balances_by_currency'].append({
                    'currency_code': row[0],
                    'entities_count': row[1] or 0,
                    'total_balance': float(row[2]) if row[2] else 0,
                    'total_debit_balance': float(row[3]) if row[3] else 0,
                    'total_credit_balance': float(row[4]) if row[4] else 0,
                    'avg_balance': float(row[5]) if row[5] else 0,
                    'min_balance': float(row[6]) if row[6] else 0,
                    'max_balance': float(row[7]) if row[7] else 0,
                    'debit_entities': row[8] or 0,
                    'credit_entities': row[9] or 0,
                    'zero_entities': row[10] or 0
                })

        # تحليل المعاملات
        if currency_transactions_result:
            for row in currency_transactions_result:
                currency_analysis['transactions_by_currency'].append({
                    'currency_code': row[0],
                    'transactions_count': row[1] or 0,
                    'total_debit': float(row[2]) if row[2] else 0,
                    'total_credit': float(row[3]) if row[3] else 0,
                    'avg_transaction_amount': float(row[4]) if row[4] else 0,
                    'active_entities': row[5] or 0,
                    'latest_transaction': row[6].strftime('%Y-%m-%d') if row[6] else None,
                    'earliest_transaction': row[7].strftime('%Y-%m-%d') if row[7] else None
                })

        return jsonify({
            'success': True,
            'data': currency_analysis
        })

    except Exception as e:
        logger.error(f"❌ خطأ في تحليل العملات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

# ==================== APIs التكامل مع الأنظمة الفرعية ====================

@central_balances_bp.route('/api/integration/sync_entity_balance', methods=['POST'])
def sync_entity_balance():
    """مزامنة رصيد كيان من النظام الفرعي"""
    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        required_fields = ['entity_type_code', 'entity_id', 'currency_code']
        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            return jsonify({
                'success': False,
                'message': f'الحقول التالية مطلوبة: {", ".join(missing_fields)}'
            }), 400

        entity_type_code = data['entity_type_code']
        entity_id = data['entity_id']
        currency_code = data['currency_code']

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # استدعاء إجراء إعادة حساب الأرصدة للكيان المحدد
        call_sql = """
        DECLARE
            v_result VARCHAR2(4000);
        BEGIN
            CALCULATE_BALANCES(
                p_entity_type_code => :1,
                p_entity_id => :2,
                p_currency_code => :3,
                p_result => v_result
            );
            :4 := v_result;
        END;
        """

        cursor = oracle_mgr.connection.cursor()
        result_var = cursor.var(str)
        cursor.execute(call_sql, [entity_type_code, entity_id, currency_code, result_var])
        result = result_var.getvalue()
        cursor.close()

        if result.startswith('SUCCESS:'):
            oracle_mgr.commit()
            oracle_mgr.disconnect()

            logger.info(f"✅ تم مزامنة رصيد الكيان: {entity_type_code}/{entity_id}/{currency_code}")
            return jsonify({
                'success': True,
                'message': result.replace('SUCCESS: ', ''),
                'sync_result': result
            })
        else:
            oracle_mgr.rollback()
            oracle_mgr.disconnect()

            logger.error(f"❌ خطأ في مزامنة الرصيد: {result}")
            return jsonify({
                'success': False,
                'message': result.replace('ERROR: ', ''),
                'sync_result': result
            }), 400

    except Exception as e:
        logger.error(f"❌ خطأ في مزامنة رصيد الكيان: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/integration/bulk_sync_balances', methods=['POST'])
def bulk_sync_balances():
    """مزامنة مجمعة للأرصدة"""
    try:
        data = request.get_json()
        entity_type_code = data.get('entity_type_code')
        currency_code = data.get('currency_code')
        entity_ids = data.get('entity_ids', [])

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        sync_results = []
        success_count = 0
        error_count = 0

        # إذا لم يتم تحديد كيانات محددة، مزامنة جميع الكيانات
        if not entity_ids:
            if entity_type_code:
                # جلب جميع الكيانات من النوع المحدد
                entities_sql = """
                SELECT DISTINCT entity_id
                FROM CURRENT_BALANCES
                WHERE entity_type_code = :1
                """
                params = [entity_type_code]
                if currency_code:
                    entities_sql += " AND currency_code = :2"
                    params.append(currency_code)

                entities_result = oracle_mgr.execute_query(entities_sql, params)
                if entities_result:
                    entity_ids = [row[0] for row in entities_result]

        # مزامنة كل كيان
        for entity_id in entity_ids:
            try:
                call_sql = """
                DECLARE
                    v_result VARCHAR2(4000);
                BEGIN
                    CALCULATE_BALANCES(
                        p_entity_type_code => :1,
                        p_entity_id => :2,
                        p_currency_code => :3,
                        p_result => v_result
                    );
                    :4 := v_result;
                END;
                """

                cursor = oracle_mgr.connection.cursor()
                result_var = cursor.var(str)
                cursor.execute(call_sql, [entity_type_code, entity_id, currency_code, result_var])
                result = result_var.getvalue()
                cursor.close()

                if result.startswith('SUCCESS:'):
                    success_count += 1
                    sync_results.append({
                        'entity_id': entity_id,
                        'status': 'success',
                        'message': result.replace('SUCCESS: ', '')
                    })
                else:
                    error_count += 1
                    sync_results.append({
                        'entity_id': entity_id,
                        'status': 'error',
                        'message': result.replace('ERROR: ', '')
                    })

            except Exception as e:
                error_count += 1
                sync_results.append({
                    'entity_id': entity_id,
                    'status': 'error',
                    'message': str(e)
                })

        oracle_mgr.commit()
        oracle_mgr.disconnect()

        logger.info(f"✅ مزامنة مجمعة: {success_count} نجح، {error_count} فشل")
        return jsonify({
            'success': True,
            'message': f'تمت المزامنة المجمعة: {success_count} نجح، {error_count} فشل',
            'summary': {
                'total_entities': len(entity_ids),
                'success_count': success_count,
                'error_count': error_count
            },
            'results': sync_results
        })

    except Exception as e:
        logger.error(f"❌ خطأ في المزامنة المجمعة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/integration/entity_balance_status/<entity_type_code>/<int:entity_id>', methods=['GET'])
def get_entity_balance_status(entity_type_code, entity_id):
    """حالة رصيد كيان للأنظمة الفرعية"""
    try:
        currency_code = request.args.get('currency_code')

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # بناء الاستعلام
        where_conditions = ["entity_type_code = :1", "entity_id = :2"]
        params = [entity_type_code, entity_id]

        if currency_code:
            where_conditions.append("currency_code = :3")
            params.append(currency_code)

        where_clause = " AND ".join(where_conditions)

        # جلب حالة الرصيد
        balance_status_sql = f"""
        SELECT
            entity_type_code, entity_id, currency_code,
            opening_balance, debit_amount, credit_amount, current_balance,
            total_transactions_count, last_transaction_date,
            created_at, updated_at,
            CASE
                WHEN current_balance > 0 THEN 'DEBIT'
                WHEN current_balance < 0 THEN 'CREDIT'
                ELSE 'ZERO'
            END as balance_type,
            ABS(current_balance) as balance_amount
        FROM CURRENT_BALANCES
        WHERE {where_clause}
        ORDER BY currency_code
        """

        results = oracle_mgr.execute_query(balance_status_sql, params)

        # جلب آخر المعاملات
        recent_transactions_sql = f"""
        SELECT
            id, document_type_code, document_number, document_date,
            debit_amount, credit_amount, status, description
        FROM BALANCE_TRANSACTIONS
        WHERE entity_type_code = :1 AND entity_id = :2
        ORDER BY document_date DESC, created_date DESC
        FETCH FIRST 5 ROWS ONLY
        """

        transactions_result = oracle_mgr.execute_query(recent_transactions_sql, [entity_type_code, entity_id])
        oracle_mgr.disconnect()

        # تحويل النتائج
        balance_status = []
        if results:
            for row in results:
                balance_status.append({
                    'entity_type_code': row[0],
                    'entity_id': row[1],
                    'currency_code': row[2],
                    'opening_balance': float(row[3]) if row[3] else 0,
                    'debit_amount': float(row[4]) if row[4] else 0,
                    'credit_amount': float(row[5]) if row[5] else 0,
                    'current_balance': float(row[6]) if row[6] else 0,
                    'total_transactions_count': row[7] or 0,
                    'last_transaction_date': row[8].strftime('%Y-%m-%d') if row[8] else None,
                    'created_at': row[9].strftime('%Y-%m-%d %H:%M:%S') if row[9] else None,
                    'updated_at': row[10].strftime('%Y-%m-%d %H:%M:%S') if row[10] else None,
                    'balance_type': row[11],
                    'balance_amount': float(row[12]) if row[12] else 0
                })

        # آخر المعاملات
        recent_transactions = []
        if transactions_result:
            for row in transactions_result:
                recent_transactions.append({
                    'id': row[0],
                    'document_type_code': row[1],
                    'document_number': row[2],
                    'document_date': row[3].strftime('%Y-%m-%d') if row[3] else None,
                    'debit_amount': float(row[4]) if row[4] else 0,
                    'credit_amount': float(row[5]) if row[5] else 0,
                    'status': row[6],
                    'description': row[7]
                })

        return jsonify({
            'success': True,
            'data': {
                'balance_status': balance_status,
                'recent_transactions': recent_transactions,
                'entity_info': {
                    'entity_type_code': entity_type_code,
                    'entity_id': entity_id
                }
            }
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب حالة رصيد الكيان: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/integration/webhook/transaction_created', methods=['POST'])
def webhook_transaction_created():
    """Webhook لإشعار إنشاء معاملة من نظام فرعي"""
    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        required_fields = ['source_system', 'entity_type_code', 'entity_id',
                          'document_type_code', 'document_number', 'document_date',
                          'currency_code', 'amount', 'transaction_type']

        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            return jsonify({
                'success': False,
                'message': f'الحقول التالية مطلوبة: {", ".join(missing_fields)}'
            }), 400

        # تحديد المبلغ المدين والدائن حسب نوع المعاملة
        amount = data['amount']
        transaction_type = data['transaction_type']  # DEBIT أو CREDIT

        debit_amount = amount if transaction_type == 'DEBIT' else 0
        credit_amount = amount if transaction_type == 'CREDIT' else 0

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # إضافة المعاملة باستخدام الإجراء المخزن
        call_sql = """
        DECLARE
            v_result VARCHAR2(4000);
            v_transaction_id NUMBER;
        BEGIN
            ADD_TRANSACTION(
                p_entity_type_code => :1,
                p_entity_id => :2,
                p_document_type_code => :3,
                p_document_number => :4,
                p_document_date => TO_DATE(:5, 'YYYY-MM-DD'),
                p_currency_code => :6,
                p_debit_amount => :7,
                p_credit_amount => :8,
                p_exchange_rate => :9,
                p_description => :10,
                p_reference_number => :11,
                p_created_by => :12,
                p_result => v_result,
                p_transaction_id => v_transaction_id
            );
            :13 := v_result;
            :14 := v_transaction_id;
        END;
        """

        params = [
            data['entity_type_code'],
            data['entity_id'],
            data['document_type_code'],
            data['document_number'],
            data['document_date'],
            data['currency_code'],
            debit_amount,
            credit_amount,
            data.get('exchange_rate', 1),
            f"معاملة من {data['source_system']}: {data.get('description', '')}",
            data.get('reference_number'),
            data.get('created_by', 1),
            None, None
        ]

        cursor = oracle_mgr.connection.cursor()
        result_var = cursor.var(str)
        transaction_id_var = cursor.var(int)
        params[12] = result_var
        params[13] = transaction_id_var

        cursor.execute(call_sql, params)
        result = result_var.getvalue()
        transaction_id = transaction_id_var.getvalue()
        cursor.close()

        oracle_mgr.disconnect()

        if result.startswith('SUCCESS:'):
            logger.info(f"✅ تم إنشاء معاملة من {data['source_system']}: {transaction_id}")
            return jsonify({
                'success': True,
                'message': 'تم إنشاء المعاملة بنجاح',
                'data': {
                    'transaction_id': transaction_id,
                    'source_system': data['source_system']
                },
                'result': result
            }), 201
        else:
            logger.error(f"❌ خطأ في إنشاء معاملة من {data['source_system']}: {result}")
            return jsonify({
                'success': False,
                'message': result.replace('ERROR: ', ''),
                'result': result
            }), 400

    except Exception as e:
        logger.error(f"❌ خطأ في webhook إنشاء المعاملة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/integration/validate_entity', methods=['POST'])
def validate_entity():
    """التحقق من صحة كيان للأنظمة الفرعية"""
    try:
        data = request.get_json()
        entity_type_code = data.get('entity_type_code')
        entity_id = data.get('entity_id')

        if not entity_type_code or not entity_id:
            return jsonify({
                'success': False,
                'message': 'نوع الكيان ومعرف الكيان مطلوبان'
            }), 400

        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        # التحقق من وجود نوع الكيان
        entity_type_sql = """
        SELECT entity_type_code, entity_name_ar, entity_name_en, module_name, is_active
        FROM ENTITY_TYPES
        WHERE entity_type_code = :1
        """

        entity_type_result = oracle_mgr.execute_query(entity_type_sql, [entity_type_code])

        if not entity_type_result:
            oracle_mgr.disconnect()
            return jsonify({
                'success': False,
                'message': f'نوع الكيان {entity_type_code} غير موجود',
                'validation_result': 'INVALID_ENTITY_TYPE'
            }), 404

        entity_type_info = entity_type_result[0]
        if not entity_type_info[4]:  # is_active
            oracle_mgr.disconnect()
            return jsonify({
                'success': False,
                'message': f'نوع الكيان {entity_type_code} غير نشط',
                'validation_result': 'INACTIVE_ENTITY_TYPE'
            }), 400

        # التحقق من وجود الكيان في النظام المناسب
        entity_exists = False
        entity_name = None

        if entity_type_code == 'SUPPLIER':
            supplier_sql = "SELECT name FROM SUPPLIERS WHERE id = :1"
            supplier_result = oracle_mgr.execute_query(supplier_sql, [entity_id])
            if supplier_result:
                entity_exists = True
                entity_name = supplier_result[0][0]

        elif entity_type_code == 'CUSTOMER':
            customer_sql = "SELECT name FROM CUSTOMERS WHERE id = :1"
            customer_result = oracle_mgr.execute_query(customer_sql, [entity_id])
            if customer_result:
                entity_exists = True
                entity_name = customer_result[0][0]

        # التحقق من وجود رصيد للكيان
        balance_sql = """
        SELECT currency_code, current_balance, total_transactions_count
        FROM CURRENT_BALANCES
        WHERE entity_type_code = :1 AND entity_id = :2
        """

        balance_result = oracle_mgr.execute_query(balance_sql, [entity_type_code, entity_id])
        oracle_mgr.disconnect()

        # تحضير النتيجة
        validation_result = {
            'entity_type_code': entity_type_code,
            'entity_type_name_ar': entity_type_info[1],
            'entity_type_name_en': entity_type_info[2],
            'module_name': entity_type_info[3],
            'entity_id': entity_id,
            'entity_exists': entity_exists,
            'entity_name': entity_name,
            'has_balance_records': len(balance_result) > 0 if balance_result else False,
            'balance_currencies': []
        }

        if balance_result:
            for row in balance_result:
                validation_result['balance_currencies'].append({
                    'currency_code': row[0],
                    'current_balance': float(row[1]) if row[1] else 0,
                    'transactions_count': row[2] or 0
                })

        # تحديد حالة التحقق
        if entity_exists:
            validation_status = 'VALID'
            message = 'الكيان صحيح وموجود'
        else:
            validation_status = 'ENTITY_NOT_FOUND'
            message = f'الكيان {entity_id} غير موجود في نظام {entity_type_info[3]}'

        return jsonify({
            'success': True,
            'message': message,
            'validation_status': validation_status,
            'data': validation_result
        })

    except Exception as e:
        logger.error(f"❌ خطأ في التحقق من الكيان: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/integration/system_health', methods=['GET'])
def get_system_health():
    """فحص صحة النظام المركزي للأنظمة الفرعية"""
    try:
        oracle_mgr = OracleManager()
        oracle_mgr.connect()

        health_checks = []
        overall_status = 'HEALTHY'

        # فحص الاتصال بقاعدة البيانات
        try:
            oracle_mgr.execute_query("SELECT 1 FROM DUAL")
            health_checks.append({
                'component': 'database_connection',
                'status': 'HEALTHY',
                'message': 'الاتصال بقاعدة البيانات سليم'
            })
        except Exception as e:
            health_checks.append({
                'component': 'database_connection',
                'status': 'UNHEALTHY',
                'message': f'خطأ في الاتصال بقاعدة البيانات: {e}'
            })
            overall_status = 'UNHEALTHY'

        # فحص الجداول الأساسية
        tables_to_check = ['ENTITY_TYPES', 'DOCUMENT_TYPES', 'CURRENT_BALANCES', 'BALANCE_TRANSACTIONS']
        for table in tables_to_check:
            try:
                result = oracle_mgr.execute_query(f"SELECT COUNT(*) FROM {table}")
                count = result[0][0] if result else 0
                health_checks.append({
                    'component': f'table_{table.lower()}',
                    'status': 'HEALTHY',
                    'message': f'الجدول {table} يحتوي على {count} سجل'
                })
            except Exception as e:
                health_checks.append({
                    'component': f'table_{table.lower()}',
                    'status': 'UNHEALTHY',
                    'message': f'خطأ في الوصول للجدول {table}: {e}'
                })
                overall_status = 'UNHEALTHY'

        # فحص الإجراءات المخزنة
        procedures_to_check = ['ADD_TRANSACTION', 'UPDATE_BALANCE', 'CALCULATE_BALANCES']
        for procedure in procedures_to_check:
            try:
                check_sql = """
                SELECT COUNT(*) FROM USER_PROCEDURES
                WHERE OBJECT_NAME = :1 AND OBJECT_TYPE = 'PROCEDURE'
                """
                result = oracle_mgr.execute_query(check_sql, [procedure])
                exists = result[0][0] > 0 if result else False

                if exists:
                    health_checks.append({
                        'component': f'procedure_{procedure.lower()}',
                        'status': 'HEALTHY',
                        'message': f'الإجراء المخزن {procedure} موجود'
                    })
                else:
                    health_checks.append({
                        'component': f'procedure_{procedure.lower()}',
                        'status': 'UNHEALTHY',
                        'message': f'الإجراء المخزن {procedure} غير موجود'
                    })
                    overall_status = 'UNHEALTHY'
            except Exception as e:
                health_checks.append({
                    'component': f'procedure_{procedure.lower()}',
                    'status': 'UNHEALTHY',
                    'message': f'خطأ في فحص الإجراء {procedure}: {e}'
                })
                overall_status = 'UNHEALTHY'

        # إحصائيات النظام
        stats_sql = """
        SELECT
            (SELECT COUNT(*) FROM CURRENT_BALANCES) as total_balances,
            (SELECT COUNT(*) FROM BALANCE_TRANSACTIONS WHERE status = 'POSTED') as posted_transactions,
            (SELECT COUNT(DISTINCT entity_type_code) FROM CURRENT_BALANCES) as active_entity_types,
            (SELECT COUNT(DISTINCT currency_code) FROM CURRENT_BALANCES) as active_currencies
        FROM DUAL
        """

        stats_result = oracle_mgr.execute_query(stats_sql)
        system_stats = {}
        if stats_result:
            row = stats_result[0]
            system_stats = {
                'total_balances': row[0] or 0,
                'posted_transactions': row[1] or 0,
                'active_entity_types': row[2] or 0,
                'active_currencies': row[3] or 0
            }

        oracle_mgr.disconnect()

        return jsonify({
            'success': True,
            'overall_status': overall_status,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'health_checks': health_checks,
            'system_stats': system_stats
        })

    except Exception as e:
        logger.error(f"❌ خطأ في فحص صحة النظام: {e}")
        return jsonify({
            'success': False,
            'overall_status': 'UNHEALTHY',
            'message': str(e)
        }), 500

# ==================== اختبار وتوثيق APIs ====================

@central_balances_bp.route('/api/test/comprehensive', methods=['POST'])
def comprehensive_api_test():
    """اختبار شامل لجميع APIs"""
    try:
        test_results = []

        # اختبار 1: APIs الأرصدة الافتتاحية
        logger.info("🧪 اختبار APIs الأرصدة الافتتاحية")

        # اختبار جلب الأرصدة الافتتاحية
        try:
            oracle_mgr = OracleManager()
            oracle_mgr.connect()

            # اختبار جلب الأرصدة
            opening_balances_sql = "SELECT COUNT(*) FROM OPENING_BALANCES WHERE is_active = 1"
            result = oracle_mgr.execute_query(opening_balances_sql)
            count = result[0][0] if result else 0

            test_results.append({
                'category': 'opening_balances',
                'test': 'get_opening_balances',
                'status': 'PASS',
                'message': f'تم جلب {count} رصيد افتتاحي',
                'endpoint': 'GET /api/opening_balances'
            })

        except Exception as e:
            test_results.append({
                'category': 'opening_balances',
                'test': 'get_opening_balances',
                'status': 'FAIL',
                'message': str(e),
                'endpoint': 'GET /api/opening_balances'
            })

        # اختبار 2: APIs المعاملات
        logger.info("🧪 اختبار APIs المعاملات")

        try:
            # اختبار جلب المعاملات
            transactions_sql = "SELECT COUNT(*) FROM BALANCE_TRANSACTIONS WHERE status = 'POSTED'"
            result = oracle_mgr.execute_query(transactions_sql)
            count = result[0][0] if result else 0

            test_results.append({
                'category': 'transactions',
                'test': 'get_transactions',
                'status': 'PASS',
                'message': f'تم جلب {count} معاملة مرحلة',
                'endpoint': 'GET /api/transactions'
            })

        except Exception as e:
            test_results.append({
                'category': 'transactions',
                'test': 'get_transactions',
                'status': 'FAIL',
                'message': str(e),
                'endpoint': 'GET /api/transactions'
            })

        # اختبار 3: APIs التقارير
        logger.info("🧪 اختبار APIs التقارير")

        try:
            # اختبار ميزان المراجعة
            trial_balance_sql = "SELECT COUNT(*) FROM V_TRIAL_BALANCE"
            result = oracle_mgr.execute_query(trial_balance_sql)
            count = result[0][0] if result else 0

            test_results.append({
                'category': 'reports',
                'test': 'trial_balance',
                'status': 'PASS',
                'message': f'ميزان المراجعة يحتوي على {count} عنصر',
                'endpoint': 'GET /api/reports/trial_balance'
            })

        except Exception as e:
            test_results.append({
                'category': 'reports',
                'test': 'trial_balance',
                'status': 'FAIL',
                'message': str(e),
                'endpoint': 'GET /api/reports/trial_balance'
            })

        # اختبار 4: APIs الإحصائيات
        logger.info("🧪 اختبار APIs الإحصائيات")

        try:
            # اختبار إحصائيات لوحة المعلومات
            dashboard_stats_sql = """
            SELECT
                (SELECT COUNT(*) FROM CURRENT_BALANCES) as balances,
                (SELECT COUNT(*) FROM BALANCE_TRANSACTIONS WHERE status = 'POSTED') as transactions
            FROM DUAL
            """
            result = oracle_mgr.execute_query(dashboard_stats_sql)
            if result:
                balances_count, transactions_count = result[0]
                test_results.append({
                    'category': 'analytics',
                    'test': 'dashboard_stats',
                    'status': 'PASS',
                    'message': f'إحصائيات لوحة المعلومات: {balances_count} رصيد، {transactions_count} معاملة',
                    'endpoint': 'GET /api/analytics/dashboard_stats'
                })

        except Exception as e:
            test_results.append({
                'category': 'analytics',
                'test': 'dashboard_stats',
                'status': 'FAIL',
                'message': str(e),
                'endpoint': 'GET /api/analytics/dashboard_stats'
            })

        # اختبار 5: APIs التكامل
        logger.info("🧪 اختبار APIs التكامل")

        try:
            # اختبار فحص صحة النظام
            health_check_sql = "SELECT 1 FROM DUAL"
            oracle_mgr.execute_query(health_check_sql)

            test_results.append({
                'category': 'integration',
                'test': 'system_health',
                'status': 'PASS',
                'message': 'فحص صحة النظام يعمل بنجاح',
                'endpoint': 'GET /api/integration/system_health'
            })

        except Exception as e:
            test_results.append({
                'category': 'integration',
                'test': 'system_health',
                'status': 'FAIL',
                'message': str(e),
                'endpoint': 'GET /api/integration/system_health'
            })

        # اختبار 6: Views والفهارس
        logger.info("🧪 اختبار Views والفهارس")

        try:
            # فحص Views
            views_sql = """
            SELECT COUNT(*) FROM user_views
            WHERE view_name LIKE 'V_%BALANCE%' OR view_name LIKE 'V_%TRANSACTION%'
            """
            result = oracle_mgr.execute_query(views_sql)
            views_count = result[0][0] if result else 0

            # فحص الفهارس
            indexes_sql = "SELECT COUNT(*) FROM user_indexes WHERE index_name LIKE 'IDX_%'"
            result = oracle_mgr.execute_query(indexes_sql)
            indexes_count = result[0][0] if result else 0

            test_results.append({
                'category': 'database',
                'test': 'views_and_indexes',
                'status': 'PASS',
                'message': f'تم العثور على {views_count} Views و {indexes_count} فهرس',
                'endpoint': 'Database Objects'
            })

        except Exception as e:
            test_results.append({
                'category': 'database',
                'test': 'views_and_indexes',
                'status': 'FAIL',
                'message': str(e),
                'endpoint': 'Database Objects'
            })

        oracle_mgr.disconnect()

        # تحليل النتائج
        total_tests = len(test_results)
        passed_tests = len([t for t in test_results if t['status'] == 'PASS'])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        logger.info(f"✅ اكتمل الاختبار الشامل: {passed_tests}/{total_tests} نجح ({success_rate:.1f}%)")

        return jsonify({
            'success': True,
            'message': f'اكتمل الاختبار الشامل: {passed_tests}/{total_tests} نجح ({success_rate:.1f}%)',
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'success_rate': round(success_rate, 1)
            },
            'test_results': test_results
        })

    except Exception as e:
        logger.error(f"❌ خطأ في الاختبار الشامل: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@central_balances_bp.route('/api/documentation', methods=['GET'])
def get_api_documentation():
    """توثيق شامل لجميع APIs"""
    try:
        documentation = {
            'title': 'توثيق النظام المركزي للأرصدة والمعاملات',
            'version': '1.0.0',
            'description': 'نظام مركزي شامل لإدارة الأرصدة والمعاملات لجميع الأنظمة الفرعية',
            'base_url': '/central_balances/api',
            'categories': {
                'opening_balances': {
                    'name': 'إدارة الأرصدة الافتتاحية',
                    'description': 'APIs لإدارة الأرصدة الافتتاحية مع عمليات CRUD والاعتماد والترحيل',
                    'endpoints': [
                        {
                            'method': 'GET',
                            'path': '/opening_balances',
                            'description': 'جلب الأرصدة الافتتاحية مع فلترة متقدمة',
                            'parameters': [
                                {'name': 'entity_type_code', 'type': 'string', 'description': 'نوع الكيان'},
                                {'name': 'entity_id', 'type': 'integer', 'description': 'معرف الكيان'},
                                {'name': 'fiscal_year', 'type': 'integer', 'description': 'السنة المالية'},
                                {'name': 'currency_code', 'type': 'string', 'description': 'رمز العملة'},
                                {'name': 'status', 'type': 'string', 'description': 'الحالة'},
                                {'name': 'page', 'type': 'integer', 'description': 'رقم الصفحة'},
                                {'name': 'per_page', 'type': 'integer', 'description': 'عدد العناصر في الصفحة'}
                            ]
                        },
                        {
                            'method': 'GET',
                            'path': '/opening_balances/{id}',
                            'description': 'جلب رصيد افتتاحي محدد',
                            'parameters': [
                                {'name': 'id', 'type': 'integer', 'description': 'معرف الرصيد الافتتاحي'}
                            ]
                        },
                        {
                            'method': 'POST',
                            'path': '/opening_balances',
                            'description': 'إنشاء رصيد افتتاحي جديد',
                            'body_example': {
                                'entity_type_code': 'SUPPLIER',
                                'entity_id': 1,
                                'fiscal_year': 2024,
                                'fiscal_period_start_date': '2024-01-01',
                                'currency_code': 'SAR',
                                'opening_balance_amount': 15000,
                                'balance_type': 'CREDIT',
                                'notes': 'رصيد افتتاحي'
                            }
                        },
                        {
                            'method': 'PUT',
                            'path': '/opening_balances/{id}',
                            'description': 'تعديل رصيد افتتاحي موجود'
                        },
                        {
                            'method': 'DELETE',
                            'path': '/opening_balances/{id}',
                            'description': 'حذف رصيد افتتاحي (حذف منطقي)'
                        },
                        {
                            'method': 'POST',
                            'path': '/opening_balances/{id}/approve',
                            'description': 'اعتماد رصيد افتتاحي'
                        },
                        {
                            'method': 'POST',
                            'path': '/opening_balances/{id}/post',
                            'description': 'ترحيل رصيد افتتاحي'
                        },
                        {
                            'method': 'POST',
                            'path': '/opening_balances/bulk_approve',
                            'description': 'اعتماد مجموعة من الأرصدة الافتتاحية'
                        },
                        {
                            'method': 'POST',
                            'path': '/opening_balances/bulk_post',
                            'description': 'ترحيل مجموعة من الأرصدة الافتتاحية'
                        }
                    ]
                },
                'transactions': {
                    'name': 'إدارة المعاملات',
                    'description': 'APIs لإدارة المعاملات مع إضافة وعكس وتعديل',
                    'endpoints': [
                        {
                            'method': 'GET',
                            'path': '/transactions',
                            'description': 'جلب المعاملات مع فلترة متقدمة'
                        },
                        {
                            'method': 'GET',
                            'path': '/transactions/{id}',
                            'description': 'جلب معاملة محددة'
                        },
                        {
                            'method': 'POST',
                            'path': '/transactions',
                            'description': 'إضافة معاملة جديدة',
                            'body_example': {
                                'entity_type_code': 'SUPPLIER',
                                'entity_id': 1,
                                'document_type_code': 'PURCHASE_INVOICE',
                                'document_number': 'INV-001',
                                'document_date': '2024-12-05',
                                'currency_code': 'SAR',
                                'debit_amount': 0,
                                'credit_amount': 8000,
                                'description': 'فاتورة شراء'
                            }
                        },
                        {
                            'method': 'POST',
                            'path': '/transactions/{id}/reverse',
                            'description': 'عكس معاملة'
                        },
                        {
                            'method': 'PUT',
                            'path': '/transactions/{id}/update',
                            'description': 'تعديل معاملة (للمسودات فقط)'
                        },
                        {
                            'method': 'GET',
                            'path': '/transactions/entity/{type}/{id}',
                            'description': 'جلب معاملات كيان محدد'
                        }
                    ]
                }
            }
        }

        return jsonify({
            'success': True,
            'data': documentation
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب التوثيق: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500
