
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API أصناف المشتريات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h1>اختبار API أصناف المشتريات</h1>
        
        <div class="row mt-4">
            <div class="col-md-4">
                <button class="btn btn-primary w-100" onclick="testStatsAPI()">اختبار API الإحصائيات</button>
            </div>
            <div class="col-md-4">
                <button class="btn btn-success w-100" onclick="testItemsAPI()">اختبار API البيانات</button>
            </div>
            <div class="col-md-4">
                <button class="btn btn-info w-100" onclick="testSuppliersAPI()">اختبار API الموردين</button>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>النتائج:</h3>
            <div id="results" class="border p-3" style="min-height: 200px; background-color: #f8f9fa;">
                اضغط على أحد الأزرار لاختبار API
            </div>
        </div>
    </div>

    <script>
    function testStatsAPI() {
        $('#results').html('<div class="text-center"><div class="spinner-border" role="status"></div><br>جاري تحميل الإحصائيات...</div>');
        
        // محاكاة البيانات المتوقعة
        const mockStats = {
        "total_items": 4,
        "total_orders": 4,
        "total_value": 9750.0,
        "avg_price": 24.5625
};
        
        setTimeout(() => {
            $('#results').html(`
                <h4>الإحصائيات السريعة:</h4>
                <div class="row">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5>${mockStats.total_items}</h5>
                                <small>إجمالي الأصناف</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5>${mockStats.total_orders}</h5>
                                <small>إجمالي الطلبات</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5>${mockStats.total_value.toLocaleString()}</h5>
                                <small>إجمالي القيمة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5>${mockStats.avg_price.toFixed(2)}</h5>
                                <small>متوسط السعر</small>
                            </div>
                        </div>
                    </div>
                </div>
            `);
        }, 1000);
    }
    
    function testItemsAPI() {
        $('#results').html('<div class="text-center"><div class="spinner-border" role="status"></div><br>جاري تحميل البيانات...</div>');
        
        fetch('/purchase-orders/api/items/data')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let html = '<h4>بيانات الأصناف:</h4><div class="table-responsive"><table class="table table-striped"><thead><tr><th>الكود</th><th>الاسم</th><th>المورد</th><th>الكمية</th><th>السعر</th><th>القيمة</th></tr></thead><tbody>';
                    
                    data.data.forEach(item => {
                        html += `<tr>
                            <td>${item.item_code}</td>
                            <td>${item.item_name}</td>
                            <td>${item.supplier_name}</td>
                            <td>${item.total_quantity}</td>
                            <td>${item.avg_price.toFixed(2)}</td>
                            <td>${item.total_value.toFixed(2)}</td>
                        </tr>`;
                    });
                    
                    html += '</tbody></table></div>';
                    $('#results').html(html);
                } else {
                    $('#results').html('<div class="alert alert-danger">خطأ: ' + data.error + '</div>');
                }
            })
            .catch(error => {
                $('#results').html('<div class="alert alert-danger">خطأ في الشبكة: ' + error + '</div>');
            });
    }
    
    function testSuppliersAPI() {
        $('#results').html('<div class="text-center"><div class="spinner-border" role="status"></div><br>جاري تحميل الموردين...</div>');
        
        fetch('/purchase-orders/api/items/suppliers')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let html = '<h4>قائمة الموردين:</h4><ul class="list-group">';
                    
                    data.data.forEach(supplier => {
                        html += `<li class="list-group-item">${supplier.label}</li>`;
                    });
                    
                    html += '</ul>';
                    $('#results').html(html);
                } else {
                    $('#results').html('<div class="alert alert-danger">خطأ: ' + data.error + '</div>');
                }
            })
            .catch(error => {
                $('#results').html('<div class="alert alert-danger">خطأ في الشبكة: ' + error + '</div>');
            });
    }
    </script>
</body>
</html>
        