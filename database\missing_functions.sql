-- =====================================================
-- الدوال المفقودة للنظام المحاسبي
-- Missing Functions for Accounting System
-- =====================================================

-- 1. إنشاء دالة التحقق من إمكانية إلغاء الحوالة
CREATE OR REPLACE FUNCTION CAN_CANCEL_TRANSFER(
    p_transfer_id IN NUMBER
) RETURN VARCHAR2 AS
    v_transfer_status VARCHAR2(50);
    v_execution_date DATE;
    v_days_since_execution NUMBER;
    v_distributions_count NUMBER;
    v_result VARCHAR2(4000);
BEGIN
    -- التحقق من وجود الحوالة وحالتها
    BEGIN
        SELECT status, execution_date
        INTO v_transfer_status, v_execution_date
        FROM transfers
        WHERE id = p_transfer_id;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RETURN 'ERROR: الحوالة غير موجودة';
    END;
    
    -- التحقق من حالة الحوالة
    IF v_transfer_status != 'executed' THEN
        RETURN 'ERROR: يمكن إلغاء الحوالات المنفذة فقط. الحالة الحالية: ' || v_transfer_status;
    END IF;
    
    -- التحقق من وجود توزيعات
    SELECT COUNT(*) INTO v_distributions_count
    FROM transfer_supplier_dist
    WHERE transfer_id = p_transfer_id;
    
    IF v_distributions_count = 0 THEN
        RETURN 'ERROR: لا توجد توزيعات للحوالة';
    END IF;
    
    -- حساب عدد الأيام منذ التنفيذ
    IF v_execution_date IS NOT NULL THEN
        v_days_since_execution := TRUNC(SYSDATE - v_execution_date);
        
        -- تحذير إذا مرت فترة طويلة
        IF v_days_since_execution > 30 THEN
            RETURN 'WARNING: مرت فترة طويلة على تنفيذ الحوالة (' || v_days_since_execution || ' يوم). تأكد من صحة العملية';
        ELSIF v_days_since_execution > 7 THEN
            RETURN 'WARNING: مرت أكثر من أسبوع على تنفيذ الحوالة (' || v_days_since_execution || ' يوم)';
        END IF;
    END IF;
    
    -- إذا وصلنا هنا، فيمكن الإلغاء
    v_result := 'OK: يمكن إلغاء الحوالة';
    
    IF v_execution_date IS NOT NULL THEN
        v_result := v_result || '. تم التنفيذ منذ ' || v_days_since_execution || ' يوم';
    END IF;
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'ERROR: خطأ في التحقق من إمكانية الإلغاء: ' || SQLERRM;
END;
/

-- 2. إنشاء دالة للتحقق الشامل قبل تنفيذ الحوالة
CREATE OR REPLACE FUNCTION VALIDATE_TRANSFER_EXECUTION(
    p_transfer_id IN NUMBER,
    p_money_changer_id IN NUMBER,
    p_total_amount IN NUMBER,
    p_currency_code IN VARCHAR2,
    p_supplier_distributions IN CLOB
) RETURN VARCHAR2 AS
    v_balance_check VARCHAR2(4000);
    v_dist_check VARCHAR2(4000);
    v_transfer_status VARCHAR2(50);
    v_result VARCHAR2(4000);
BEGIN
    -- التحقق من حالة الحوالة
    BEGIN
        SELECT status INTO v_transfer_status
        FROM transfers
        WHERE id = p_transfer_id;
        
        IF v_transfer_status = 'executed' THEN
            RETURN 'ERROR: الحوالة منفذة مسبقاً';
        ELSIF v_transfer_status = 'cancelled' THEN
            RETURN 'ERROR: الحوالة ملغاة';
        ELSIF v_transfer_status != 'approved' THEN
            RETURN 'ERROR: الحوالة غير معتمدة. الحالة الحالية: ' || v_transfer_status;
        END IF;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RETURN 'ERROR: الحوالة غير موجودة';
    END;
    
    -- التحقق من رصيد الصراف
    v_balance_check := CHECK_MC_BALANCE(p_money_changer_id, p_total_amount, p_currency_code);
    IF v_balance_check LIKE 'ERROR:%' THEN
        RETURN v_balance_check;
    END IF;
    
    -- التحقق من توزيعات الموردين
    v_dist_check := VALIDATE_SUPPLIER_DIST(p_transfer_id, p_supplier_distributions);
    IF v_dist_check LIKE 'ERROR:%' THEN
        RETURN v_dist_check;
    END IF;
    
    -- إذا وصلنا هنا، فجميع التحققات نجحت
    v_result := 'OK: جميع التحققات نجحت. ';
    v_result := v_result || 'رصيد الصراف: ' || SUBSTR(v_balance_check, 4) || '. ';
    v_result := v_result || 'التوزيعات: ' || SUBSTR(v_dist_check, 4);
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'ERROR: خطأ في التحقق الشامل: ' || SQLERRM;
END;
/

-- 3. إنشاء دالة للحصول على معلومات الحوالة
CREATE OR REPLACE FUNCTION GET_TRANSFER_INFO(
    p_transfer_id IN NUMBER
) RETURN VARCHAR2 AS
    v_transfer_number VARCHAR2(100);
    v_amount NUMBER;
    v_currency VARCHAR2(10);
    v_status VARCHAR2(50);
    v_money_changer_id NUMBER;
    v_result VARCHAR2(4000);
BEGIN
    SELECT 
        transfer_number,
        NVL(net_amount_sent, net_amount_received),
        'SAR', -- افتراضي
        status,
        money_changer_bank_id
    INTO 
        v_transfer_number,
        v_amount,
        v_currency,
        v_status,
        v_money_changer_id
    FROM transfers
    WHERE id = p_transfer_id;
    
    v_result := 'OK: رقم الحوالة: ' || v_transfer_number || 
                ', المبلغ: ' || v_amount || ' ' || v_currency ||
                ', الحالة: ' || v_status ||
                ', الصراف: ' || v_money_changer_id;
    
    RETURN v_result;
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN 'ERROR: الحوالة غير موجودة';
    WHEN OTHERS THEN
        RETURN 'ERROR: خطأ في الحصول على معلومات الحوالة: ' || SQLERRM;
END;
/

-- 4. إنشاء دالة للحصول على ملخص الأرصدة
CREATE OR REPLACE FUNCTION GET_BALANCE_SUMMARY(
    p_entity_type IN VARCHAR2 DEFAULT NULL,
    p_currency IN VARCHAR2 DEFAULT NULL
) RETURN VARCHAR2 AS
    v_total_entities NUMBER := 0;
    v_total_balance NUMBER := 0;
    v_positive_count NUMBER := 0;
    v_negative_count NUMBER := 0;
    v_zero_count NUMBER := 0;
    v_result VARCHAR2(4000);
BEGIN
    SELECT 
        COUNT(*),
        SUM(current_balance),
        SUM(CASE WHEN current_balance > 0 THEN 1 ELSE 0 END),
        SUM(CASE WHEN current_balance < 0 THEN 1 ELSE 0 END),
        SUM(CASE WHEN current_balance = 0 THEN 1 ELSE 0 END)
    INTO 
        v_total_entities,
        v_total_balance,
        v_positive_count,
        v_negative_count,
        v_zero_count
    FROM CURRENT_BALANCES
    WHERE (p_entity_type IS NULL OR entity_type_code = p_entity_type)
    AND (p_currency IS NULL OR currency_code = p_currency);
    
    v_result := 'OK: إجمالي الكيانات: ' || v_total_entities ||
                ', إجمالي الرصيد: ' || v_total_balance ||
                ', أرصدة موجبة: ' || v_positive_count ||
                ', أرصدة سالبة: ' || v_negative_count ||
                ', أرصدة صفرية: ' || v_zero_count;
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'ERROR: خطأ في الحصول على ملخص الأرصدة: ' || SQLERRM;
END;
/

-- 5. إنشاء دالة للتحقق من حدود الرصيد الأدنى
CREATE OR REPLACE FUNCTION CHECK_MIN_BALANCE_LIMITS(
    p_money_changer_id IN NUMBER,
    p_currency_code IN VARCHAR2
) RETURN VARCHAR2 AS
    v_current_balance NUMBER;
    v_min_balance_limit NUMBER := 1000; -- حد أدنى افتراضي
    v_warning_limit NUMBER := 5000; -- حد التحذير
    v_result VARCHAR2(4000);
BEGIN
    -- الحصول على الرصيد الحالي
    SELECT current_balance INTO v_current_balance
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'MONEY_CHANGER' 
    AND entity_id = p_money_changer_id 
    AND currency_code = p_currency_code;
    
    -- التحقق من الحدود
    IF v_current_balance < v_min_balance_limit THEN
        v_result := 'CRITICAL: الرصيد أقل من الحد الأدنى المسموح (' || v_min_balance_limit || '). الرصيد الحالي: ' || v_current_balance;
    ELSIF v_current_balance < v_warning_limit THEN
        v_result := 'WARNING: الرصيد منخفض (' || v_current_balance || '). يُنصح بالتزويد قريباً';
    ELSE
        v_result := 'OK: الرصيد ضمن الحدود المقبولة (' || v_current_balance || ')';
    END IF;
    
    RETURN v_result;
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN 'ERROR: رصيد الصراف غير موجود';
    WHEN OTHERS THEN
        RETURN 'ERROR: خطأ في التحقق من حدود الرصيد: ' || SQLERRM;
END;
/

-- 6. إنشاء إجراء لتحديث إحصائيات الحوالات
CREATE OR REPLACE PROCEDURE UPDATE_TRANSFER_STATISTICS AS
    v_total_transfers NUMBER;
    v_executed_transfers NUMBER;
    v_cancelled_transfers NUMBER;
    v_pending_transfers NUMBER;
BEGIN
    -- حساب الإحصائيات
    SELECT COUNT(*) INTO v_total_transfers FROM transfers;
    
    SELECT COUNT(*) INTO v_executed_transfers 
    FROM transfers WHERE status = 'executed';
    
    SELECT COUNT(*) INTO v_cancelled_transfers 
    FROM transfers WHERE status = 'cancelled';
    
    SELECT COUNT(*) INTO v_pending_transfers 
    FROM transfers WHERE status = 'approved';
    
    -- تسجيل الإحصائيات في سجل الأنشطة
    INSERT INTO transfer_activity_log (
        transfer_id, activity_type, description, operation_details, created_by
    ) VALUES (
        0, 'INFO', 'تحديث إحصائيات النظام',
        'إجمالي: ' || v_total_transfers || 
        ', منفذة: ' || v_executed_transfers || 
        ', ملغاة: ' || v_cancelled_transfers || 
        ', معلقة: ' || v_pending_transfers,
        1
    );
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END;
/

-- 7. إضافة comments للتوثيق
COMMENT ON FUNCTION CAN_CANCEL_TRANSFER IS 'التحقق من إمكانية إلغاء الحوالة';
COMMENT ON FUNCTION VALIDATE_TRANSFER_EXECUTION IS 'التحقق الشامل قبل تنفيذ الحوالة';
COMMENT ON FUNCTION GET_TRANSFER_INFO IS 'الحصول على معلومات الحوالة';
COMMENT ON FUNCTION GET_BALANCE_SUMMARY IS 'الحصول على ملخص الأرصدة';
COMMENT ON FUNCTION CHECK_MIN_BALANCE_LIMITS IS 'التحقق من حدود الرصيد الأدنى';
COMMENT ON PROCEDURE UPDATE_TRANSFER_STATISTICS IS 'تحديث إحصائيات الحوالات';

COMMIT;

SELECT 'Missing functions created successfully' as result FROM DUAL;
