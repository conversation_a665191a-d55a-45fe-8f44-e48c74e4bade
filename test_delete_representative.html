<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر الحذف</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h2>اختبار زر حذف المندوب</h2>
        
        <div class="card">
            <div class="card-body">
                <h5>مندوب تجريبي</h5>
                <p>أحمد محمد الأحمد</p>
                
                <button class="btn btn-danger btn-sm" onclick="deleteRepresentative(1, 'أحمد محمد الأحمد')">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </div>
    </div>

    <script>
        function deleteRepresentative(id, name) {
            if (confirm(`هل أنت متأكد من حذف المندوب "${name}"؟\n\nتحذير: سيتم حذف جميع البيانات المرتبطة بهذا المندوب نهائياً.`)) {
                // إظهار loading indicator
                const deleteBtn = document.querySelector(`button[onclick="deleteRepresentative(${id}, '${name}')"]`);
                if (deleteBtn) {
                    deleteBtn.disabled = true;
                    deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                }
                
                // إنشاء form للحذف
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/purchase-commissions/representatives/delete/${id}`;
                form.style.display = 'none';
                
                // إضافة CSRF token إذا كان متوفراً
                const csrfToken = document.querySelector('meta[name="csrf-token"]');
                if (csrfToken) {
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrf_token';
                    csrfInput.value = csrfToken.getAttribute('content');
                    form.appendChild(csrfInput);
                }
                
                document.body.appendChild(form);
                
                // للاختبار فقط - عرض رسالة بدلاً من الإرسال الفعلي
                alert(`سيتم إرسال طلب حذف إلى: ${form.action}`);
                
                // في البيئة الحقيقية، استخدم:
                // form.submit();
            }
        }
    </script>
</body>
</html>
