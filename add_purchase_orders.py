#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة أوامر شراء تجريبية لنظام ERP
"""

from database_manager import DatabaseManager
from datetime import datetime, timedelta
import random

def add_sample_purchase_orders():
    """إضافة أوامر شراء تجريبية للنظام"""
    db_manager = DatabaseManager()
    
    try:
        print("🚀 بدء إضافة أوامر الشراء التجريبية...")
        
        # بيانات أوامر الشراء التجريبية
        suppliers = [
            "شركة التقنية المتطورة",
            "مؤسسة الأجهزة الذكية", 
            "شركة المكاتب العصرية",
            "مجموعة الحلول التقنية",
            "شركة الأثاث المكتبي"
        ]
        
        order_types = [
            "أجهزة كمبيوتر",
            "أثاث مكتبي", 
            "مستلزمات مكتبية",
            "أجهزة طباعة",
            "ملحقات تقنية"
        ]
        
        statuses = ["نشط", "مكتمل", "قيد التنفيذ", "ملغي"]
        
        # إنشاء 20 أمر شراء تجريبي
        for i in range(20):
            order_no = f"PO-2024-{str(i+1).zfill(4)}"
            supplier = random.choice(suppliers)
            order_type = random.choice(order_types)
            status = random.choice(statuses)
            total_amount = round(random.uniform(1000, 50000), 2)
            
            # تواريخ عشوائية
            created_date = datetime.now() - timedelta(days=random.randint(1, 60))
            delivery_date = created_date + timedelta(days=random.randint(7, 30))
            
            description = f"أمر شراء {order_type} من {supplier}"
            
            try:
                db_manager.execute_query("""
                    INSERT INTO purchase_orders 
                    (order_no, supplier_name, order_type, order_status, total_amount, 
                     created_at, delivery_date, description, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
                """, (order_no, supplier, order_type, status, total_amount, 
                      created_date, delivery_date, description))
                
                print(f"✅ تم إضافة أمر الشراء: {order_no} - {supplier}")
                
            except Exception as e:
                print(f"❌ خطأ في إضافة أمر الشراء {order_no}: {e}")
        
        # إضافة بعض الفواتير المعلقة
        print("\n📋 إضافة فواتير معلقة...")
        
        for i in range(8):
            invoice_no = f"INV-2024-{str(i+1).zfill(4)}"
            supplier = random.choice(suppliers)
            amount = round(random.uniform(500, 15000), 2)
            due_date = datetime.now() + timedelta(days=random.randint(1, 45))
            
            try:
                db_manager.execute_query("""
                    INSERT INTO invoices 
                    (invoice_no, supplier_name, amount, due_date, status, created_at)
                    VALUES (?, ?, ?, ?, 'معلق', ?)
                """, (invoice_no, supplier, amount, due_date, datetime.now()))
                
                print(f"✅ تم إضافة الفاتورة: {invoice_no} - {amount} ر.س")
                
            except Exception as e:
                print(f"❌ خطأ في إضافة الفاتورة {invoice_no}: {e}")
        
        print("\n🎉 تم إضافة جميع أوامر الشراء والفواتير التجريبية بنجاح!")
        print("📊 يمكنك الآن رؤية أوامر الشراء الحقيقية في لوحة المعلومات")
        
    except Exception as e:
        print(f"❌ خطأ عام في إضافة البيانات: {e}")
    
    finally:
        db_manager.close()

if __name__ == "__main__":
    add_sample_purchase_orders()
