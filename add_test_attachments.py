#!/usr/bin/env python3
"""
إضافة مرفقات تجريبية للاختبار
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_manager import DatabaseManager
import logging

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_test_attachments():
    """إضافة مرفقات تجريبية"""
    try:
        db = DatabaseManager()
        
        # الحصول على رسائل تحتوي على مرفقات (حسب العلامة)
        messages_query = """
        SELECT id, subject FROM email_messages 
        WHERE has_attachments = 1 
        ORDER BY id DESC
        """
        
        result = db.execute_query(messages_query)
        if not result:
            logger.info("❌ لا توجد رسائل محددة كمحتوية على مرفقات")
            return False
        
        # مرفقات تجريبية
        test_attachments = [
            {
                'filename': 'document.pdf',
                'content_type': 'application/pdf',
                'size_bytes': 1024000  # 1 MB
            },
            {
                'filename': 'spreadsheet.xlsx',
                'content_type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'size_bytes': 512000  # 512 KB
            },
            {
                'filename': 'image.jpg',
                'content_type': 'image/jpeg',
                'size_bytes': 256000  # 256 KB
            },
            {
                'filename': 'archive.zip',
                'content_type': 'application/zip',
                'size_bytes': 2048000  # 2 MB
            }
        ]
        
        attachment_count = 0
        
        for i, row in enumerate(result[:5]):  # أول 5 رسائل فقط
            message_id = row[0]
            subject = row[1]
            
            # التحقق من وجود مرفقات بالفعل
            check_query = "SELECT COUNT(*) FROM email_attachments WHERE message_id = :1"
            existing = db.execute_query(check_query, [message_id])
            
            if existing and existing[0][0] > 0:
                logger.info(f"ℹ️ الرسالة {message_id} تحتوي بالفعل على {existing[0][0]} مرفق")
                continue
            
            # إضافة مرفق أو مرفقين لكل رسالة
            num_attachments = min(2, len(test_attachments))
            
            for j in range(num_attachments):
                attachment = test_attachments[j % len(test_attachments)]
                
                # تخصيص اسم الملف
                filename = f"{attachment['filename'].split('.')[0]}_{message_id}.{attachment['filename'].split('.')[1]}"
                
                try:
                    insert_query = """
                    INSERT INTO email_attachments (
                        id, message_id, filename, content_type, size_bytes, created_at
                    ) VALUES (
                        email_attachments_seq.NEXTVAL, :1, :2, :3, :4, SYSDATE
                    )
                    """
                    
                    db.execute_update(insert_query, [
                        message_id, filename, attachment['content_type'], attachment['size_bytes']
                    ])
                    db.commit()
                    
                    attachment_count += 1
                    logger.info(f"✅ تم إضافة مرفق: {filename} للرسالة {message_id}")
                    
                except Exception as e:
                    logger.error(f"❌ خطأ في إضافة مرفق للرسالة {message_id}: {e}")
            
            logger.info(f"📧 تم إضافة {num_attachments} مرفق للرسالة: {subject[:50]}...")
        
        logger.info(f"🎉 تم إضافة {attachment_count} مرفق تجريبي بنجاح")
        
        # عرض الإحصائيات النهائية
        stats_query = """
        SELECT 
            (SELECT COUNT(*) FROM email_messages WHERE has_attachments = 1) as messages_with_flag,
            (SELECT COUNT(DISTINCT message_id) FROM email_attachments) as messages_with_actual,
            (SELECT COUNT(*) FROM email_attachments) as total_attachments
        FROM dual
        """
        
        stats = db.execute_query(stats_query)
        if stats:
            row = stats[0]
            logger.info(f"📊 الإحصائيات:")
            logger.info(f"   - رسائل محددة كمحتوية على مرفقات: {row[0]}")
            logger.info(f"   - رسائل تحتوي فعلياً على مرفقات: {row[1]}")
            logger.info(f"   - إجمالي المرفقات: {row[2]}")
        
    except Exception as e:
        logger.error(f"❌ خطأ عام: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("📎 إضافة مرفقات تجريبية...")
    success = add_test_attachments()
    
    if success:
        print("✅ تم بنجاح!")
    else:
        print("❌ فشل في العملية!")
        sys.exit(1)
