#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إعادة تحميل نظام الإشعارات داخل التطبيق
Reload Notifications System within Flask App
"""

from flask import Blueprint, jsonify, request
import importlib
import sys

# إنشاء Blueprint للإدارة
admin_reload_bp = Blueprint('admin_reload', __name__, url_prefix='/admin')

@admin_reload_bp.route('/reload-notifications', methods=['POST'])
def reload_notifications():
    """إعادة تحميل نظام الإشعارات الفورية"""
    try:
        # إيقاف النظام الحالي
        try:
            from app.services.instant_event_processor import stop_instant_processor
            stop_instant_processor()
        except Exception as e:
            pass
        
        # إعادة تحميل الوحدات
        modules_to_reload = [
            'app.services.instant_event_processor',
            'app.services.green_whatsapp_service',
        ]
        
        reloaded_modules = []
        for module_name in modules_to_reload:
            try:
                if module_name in sys.modules:
                    importlib.reload(sys.modules[module_name])
                    reloaded_modules.append(module_name)
            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': f'خطأ في إعادة تحميل {module_name}: {str(e)}'
                })
        
        # بدء النظام الجديد
        from app.services.instant_event_processor import start_instant_processor
        start_instant_processor()
        
        return jsonify({
            'success': True,
            'message': 'تم إعادة تحميل نظام الإشعارات بنجاح',
            'reloaded_modules': reloaded_modules
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في إعادة التحميل: {str(e)}'
        })

@admin_reload_bp.route('/test-notifications', methods=['POST'])
def test_notifications():
    """اختبار نظام الإشعارات"""
    try:
        from app.services.instant_event_processor import instant_processor
        
        if not instant_processor:
            return jsonify({
                'success': False,
                'message': 'معالج الأحداث غير متاح'
            })
        
        # بيانات اختبار
        test_data = {
            'booking_number': '6419244690',
            'shipment_id': 174,
            'old_status': 'in_transit',
            'new_status': 'delivered'
        }
        
        # اختبار إرسال رسالة
        result = instant_processor._send_delivery_notification(test_data)
        
        return jsonify({
            'success': result,
            'message': 'تم إرسال رسالة اختبار بنجاح' if result else 'فشل في إرسال رسالة الاختبار',
            'booking_number_used': test_data['booking_number']
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في الاختبار: {str(e)}'
        })

# دالة لتسجيل Blueprint في التطبيق
def register_reload_routes(app):
    """تسجيل routes إعادة التحميل في التطبيق"""
    app.register_blueprint(admin_reload_bp)
    print("✅ تم تسجيل routes إعادة تحميل الإشعارات")
