-- جدول مسودات طلبات الحوالات
-- Transfer Request Drafts Table

-- إنشاء sequence للمسودات
CREATE SEQUENCE TRANSFER_DRAFTS_SEQ
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- إن<PERSON><PERSON><PERSON> جدول المسودات
CREATE TABLE TRANSFER_DRAFTS (
    id NUMBER PRIMARY KEY,
    draft_name VARCHAR2(200) NOT NULL,
    user_id NUMBER NOT NULL,
    
    -- تفاصيل الحوالة
    branch_id NUMBER,
    transfer_type VARCHAR2(50),
    money_changer_bank_id NUMBER,
    amount NUMBER(15,2),
    currency_id NUMBER,
    purpose VARCHAR2(500),
    notes CLOB,
    
    -- بيانات المستفيد (JSON)
    beneficiary_data CLOB,
    
    -- معلومات التتبع
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- فه<PERSON>رس
    CONSTRAINT fk_transfer_drafts_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_transfer_drafts_branch FOREIGN KEY (branch_id) REFERENCES BRANCHES(BRN_NO),
    CONSTRAINT fk_transfer_drafts_mcb FOREIGN KEY (money_changer_bank_id) REFERENCES MONEY_CHANGERS_BANKS(id),
    CONSTRAINT fk_transfer_drafts_currency FOREIGN KEY (currency_id) REFERENCES CURRENCIES(id)
);

-- إنشاء trigger للـ auto-increment
CREATE OR REPLACE TRIGGER TRANSFER_DRAFTS_TRG
BEFORE INSERT ON TRANSFER_DRAFTS
FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        SELECT TRANSFER_DRAFTS_SEQ.NEXTVAL INTO :NEW.id FROM DUAL;
    END IF;
END;
/

-- إنشاء trigger لتحديث updated_at
CREATE OR REPLACE TRIGGER TRANSFER_DRAFTS_UPDATE_TRG
BEFORE UPDATE ON TRANSFER_DRAFTS
FOR EACH ROW
BEGIN
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

-- إنشاء فهارس للأداء
CREATE INDEX idx_transfer_drafts_user ON TRANSFER_DRAFTS(user_id);
CREATE INDEX idx_transfer_drafts_created ON TRANSFER_DRAFTS(created_at);
CREATE INDEX idx_transfer_drafts_name ON TRANSFER_DRAFTS(draft_name);

-- إضافة تعليقات
COMMENT ON TABLE TRANSFER_DRAFTS IS 'جدول مسودات طلبات الحوالات المالية';
COMMENT ON COLUMN TRANSFER_DRAFTS.id IS 'معرف المسودة';
COMMENT ON COLUMN TRANSFER_DRAFTS.draft_name IS 'اسم المسودة';
COMMENT ON COLUMN TRANSFER_DRAFTS.user_id IS 'معرف المستخدم';
COMMENT ON COLUMN TRANSFER_DRAFTS.branch_id IS 'معرف الفرع';
COMMENT ON COLUMN TRANSFER_DRAFTS.transfer_type IS 'نوع التحويل (bank/money_changer)';
COMMENT ON COLUMN TRANSFER_DRAFTS.money_changer_bank_id IS 'معرف الصراف أو البنك';
COMMENT ON COLUMN TRANSFER_DRAFTS.amount IS 'مبلغ الحوالة';
COMMENT ON COLUMN TRANSFER_DRAFTS.currency_id IS 'معرف العملة';
COMMENT ON COLUMN TRANSFER_DRAFTS.purpose IS 'الغرض من التحويل';
COMMENT ON COLUMN TRANSFER_DRAFTS.notes IS 'ملاحظات إضافية';
COMMENT ON COLUMN TRANSFER_DRAFTS.beneficiary_data IS 'بيانات المستفيد بصيغة JSON';
COMMENT ON COLUMN TRANSFER_DRAFTS.created_at IS 'تاريخ الإنشاء';
COMMENT ON COLUMN TRANSFER_DRAFTS.updated_at IS 'تاريخ آخر تحديث';

-- إدراج بعض البيانات التجريبية (اختياري)
-- INSERT INTO TRANSFER_DRAFTS (draft_name, user_id, branch_id, transfer_type, amount, currency_id, purpose, beneficiary_data)
-- VALUES ('مسودة حوالة تجريبية', 1, 1, 'bank', 1000.00, 1, 'دفع مستحقات مورد', 
--         '{"beneficiary_name": "شركة تجريبية", "bank_name": "البنك التجريبي", "bank_account": "*********"}');

COMMIT;
