"""
خدمة إنشاء نموذج نصي عربي منسق
حل بديل بسيط وفعال للـ PDF
"""

import os
from datetime import datetime
from typing import Dict


class ArabicTextService:
    """خدمة إنشاء نموذج نصي عربي منسق"""
    
    def __init__(self):
        """تهيئة الخدمة"""
        pass
    
    def create_delivery_order_text(self, order_data: Dict) -> str:
        """إنشاء نموذج نصي لأمر التسليم"""
        
        current_date = datetime.now().strftime('%Y-%m-%d')
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M')
        
        text_content = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                          شركة النقل والشحن المتطورة                          ║
║                         أمر تسليم للمخلص الجمركي                           ║
╚══════════════════════════════════════════════════════════════════════════════╝

┌─────────────────────────────────────────────────────────────────────────────┐
│                              📋 معلومات الأمر                               │
├─────────────────────────────────────────────────────────────────────────────┤
│ رقم الأمر:           {order_data.get('order_number', 'غير محدد'):<30} │
│ تاريخ الإصدار:       {current_date:<30} │
│ حالة الأمر:          {self._get_status_arabic(order_data.get('order_status', 'draft')):<30} │
│ الأولوية:           عادية                                                    │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                        📦 المعلومات الأساسية للشحنة                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ رقم التتبع:          {order_data.get('tracking_number', 'غير محدد'):<30} │
│ رقم الحجز:           {order_data.get('booking_number', 'غير محدد'):<30} │
│ نوع الشحنة:          {order_data.get('shipment_type', 'غير محدد'):<30} │
│ الوزن الإجمالي:       {order_data.get('total_weight', 'غير محدد'):<15} كيلو          │
│ عدد الطرود:          {order_data.get('packages_count', 'غير محدد'):<30} │
│ وصف البضاعة:         {order_data.get('cargo_description', 'غير محدد'):<30} │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                           👤 بيانات المخلص الجمركي                          │
├─────────────────────────────────────────────────────────────────────────────┤
│ اسم المخلص:          {order_data.get('agent_name', 'غير محدد'):<30} │
│ اسم الشركة:          {order_data.get('company_name', 'غير محدد'):<30} │
│ رقم الترخيص:         {order_data.get('license_number', 'غير محدد'):<30} │
│ رقم الهاتف:          {order_data.get('agent_phone', 'غير محدد'):<30} │
│ البريد الإلكتروني:    {order_data.get('agent_email', 'غير محدد'):<30} │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                              🚚 تفاصيل التسليم                              │
├─────────────────────────────────────────────────────────────────────────────┤
│ موقع التسليم:        {order_data.get('delivery_location', 'غير محدد'):<30} │
│ التاريخ المطلوب:      {order_data.get('expected_completion_date', 'غير محدد'):<30} │
└─────────────────────────────────────────────────────────────────────────────┘

╔══════════════════════════════════════════════════════════════════════════════╗
║                          شركة النقل والشحن المتطورة                          ║
║                   العنوان: المملكة العربية السعودية - الرياض                 ║
║           الهاتف: +966 11 123 4567 | البريد: <EMAIL>            ║
║                      الموقع الإلكتروني: www.shipping.com                    ║
║                                                                              ║
║                        تاريخ الطباعة: {current_time:<20}                ║
╚══════════════════════════════════════════════════════════════════════════════╝

═══════════════════════════════════════════════════════════════════════════════

                              📋 تعليمات مهمة:

1. يرجى مراجعة جميع البيانات المذكورة أعلاه والتأكد من صحتها
2. في حالة وجود أي خطأ، يرجى التواصل فوراً على الرقم المذكور
3. يجب إحضار هذا المستند عند استلام البضاعة
4. التوقيع أدناه يعني الموافقة على جميع البيانات والشروط

═══════════════════════════════════════════════════════════════════════════════

التوقيع: ___________________    التاريخ: ___________________

الاسم:   ___________________    الختم:   ___________________

═══════════════════════════════════════════════════════════════════════════════
        """
        
        return text_content
    
    def _get_status_arabic(self, status):
        """ترجمة حالة الأمر للعربية"""
        status_map = {
            'draft': 'مسودة',
            'sent': 'مرسل',
            'in_progress': 'قيد التنفيذ',
            'completed': 'مكتمل',
            'cancelled': 'ملغي'
        }
        return status_map.get(status, status)
    
    def create_text_file(self, order_data: Dict) -> str:
        """إنشاء ملف نصي وحفظه"""
        
        # إنشاء المحتوى النصي
        text_content = self.create_delivery_order_text(order_data)
        
        # حفظ الملف في سطح المكتب
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        text_filename = f"delivery_order_{order_data.get('order_number', 'unknown')}.txt"
        text_path = os.path.join(desktop_path, text_filename)
        
        with open(text_path, 'w', encoding='utf-8') as f:
            f.write(text_content)
        
        return text_path


# إنشاء instance عام
arabic_text_service = ArabicTextService()


def generate_arabic_text_file(order_data: Dict) -> str:
    """دالة مساعدة لإنشاء ملف نصي عربي"""
    return arabic_text_service.create_text_file(order_data)
