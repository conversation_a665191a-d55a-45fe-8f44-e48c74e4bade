"""
وحدة الاتصال بقاعدة البيانات Oracle للتحليل الذكي
Database Connector for AI Analysis
"""

import cx_Oracle
import pandas as pd
import numpy as np
from sqlalchemy import create_engine
import logging
from typing import Dict, List, Optional, Tuple
import os

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OracleConnector:
    """فئة الاتصال بقاعدة البيانات Oracle"""
    
    def __init__(self, sid: str, username: str, password: str, host: str = "localhost", port: int = 1521):
        """
        تهيئة الاتصال بقاعدة البيانات
        
        Args:
            sid: معرف قاعدة البيانات
            username: اسم المستخدم
            password: كلمة المرور
            host: عنوان الخادم
            port: رقم المنفذ
        """
        self.sid = sid
        self.username = username
        self.password = password
        self.host = host
        self.port = port
        self.connection = None
        self.engine = None
        
    def connect(self) -> bool:
        """إنشاء اتصال بقاعدة البيانات"""
        try:
            # إنشاء DSN
            dsn = cx_Oracle.makedsn(self.host, self.port, service_name=self.sid)
            
            # إنشاء الاتصال
            self.connection = cx_Oracle.connect(
                user=self.username,
                password=self.password,
                dsn=dsn,
                encoding="UTF-8"
            )
            
            # إنشاء SQLAlchemy engine
            connection_string = f"oracle+cx_oracle://{self.username}:{self.password}@{self.host}:{self.port}/?service_name={self.sid}"
            self.engine = create_engine(connection_string)
            
            logger.info("تم الاتصال بقاعدة البيانات بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في الاتصال بقاعدة البيانات: {str(e)}")
            return False
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        try:
            if self.connection:
                self.connection.close()
            if self.engine:
                self.engine.dispose()
            logger.info("تم قطع الاتصال بقاعدة البيانات")
        except Exception as e:
            logger.error(f"خطأ في قطع الاتصال: {str(e)}")
    
    def execute_query(self, query: str) -> pd.DataFrame:
        """
        تنفيذ استعلام وإرجاع النتائج كـ DataFrame
        
        Args:
            query: الاستعلام المراد تنفيذه
            
        Returns:
            DataFrame: نتائج الاستعلام
        """
        try:
            df = pd.read_sql(query, self.engine)
            logger.info(f"تم تنفيذ الاستعلام بنجاح. عدد الصفوف: {len(df)}")
            return df
        except Exception as e:
            logger.error(f"خطأ في تنفيذ الاستعلام: {str(e)}")
            return pd.DataFrame()
    
    def get_table_info(self, table_name: str) -> Dict:
        """
        الحصول على معلومات الجدول
        
        Args:
            table_name: اسم الجدول
            
        Returns:
            Dict: معلومات الجدول
        """
        try:
            # الحصول على أعمدة الجدول
            columns_query = f"""
            SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE
            FROM USER_TAB_COLUMNS 
            WHERE TABLE_NAME = '{table_name.upper()}'
            ORDER BY COLUMN_ID
            """
            columns_df = self.execute_query(columns_query)
            
            # الحصول على عدد الصفوف
            count_query = f"SELECT COUNT(*) as ROW_COUNT FROM {table_name}"
            count_df = self.execute_query(count_query)
            row_count = count_df['ROW_COUNT'].iloc[0] if not count_df.empty else 0
            
            # الحصول على حجم الجدول
            size_query = f"""
            SELECT 
                ROUND(SUM(BYTES)/1024/1024, 2) as SIZE_MB
            FROM USER_SEGMENTS 
            WHERE SEGMENT_NAME = '{table_name.upper()}'
            """
            size_df = self.execute_query(size_query)
            size_mb = size_df['SIZE_MB'].iloc[0] if not size_df.empty else 0
            
            return {
                'table_name': table_name,
                'columns': columns_df.to_dict('records'),
                'row_count': row_count,
                'size_mb': size_mb,
                'column_count': len(columns_df)
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات الجدول: {str(e)}")
            return {}
    
    def get_sample_data(self, table_name: str, limit: int = 1000) -> pd.DataFrame:
        """
        الحصول على عينة من البيانات
        
        Args:
            table_name: اسم الجدول
            limit: عدد الصفوف المطلوبة
            
        Returns:
            DataFrame: عينة من البيانات
        """
        query = f"SELECT * FROM {table_name} WHERE ROWNUM <= {limit}"
        return self.execute_query(query)
    
    def get_data_statistics(self, table_name: str) -> Dict:
        """
        الحصول على إحصائيات البيانات
        
        Args:
            table_name: اسم الجدول
            
        Returns:
            Dict: إحصائيات البيانات
        """
        try:
            # الحصول على عينة من البيانات
            sample_df = self.get_sample_data(table_name, 10000)
            
            if sample_df.empty:
                return {}
            
            stats = {
                'basic_info': {
                    'total_rows': len(sample_df),
                    'total_columns': len(sample_df.columns),
                    'memory_usage': sample_df.memory_usage(deep=True).sum()
                },
                'column_stats': {},
                'data_quality': {
                    'missing_values': sample_df.isnull().sum().to_dict(),
                    'duplicate_rows': sample_df.duplicated().sum(),
                    'data_types': sample_df.dtypes.astype(str).to_dict()
                }
            }
            
            # إحصائيات لكل عمود
            for column in sample_df.columns:
                col_stats = {
                    'data_type': str(sample_df[column].dtype),
                    'non_null_count': sample_df[column].count(),
                    'null_count': sample_df[column].isnull().sum(),
                    'unique_count': sample_df[column].nunique()
                }
                
                # إحصائيات للأعمدة الرقمية
                if sample_df[column].dtype in ['int64', 'float64']:
                    col_stats.update({
                        'mean': sample_df[column].mean(),
                        'std': sample_df[column].std(),
                        'min': sample_df[column].min(),
                        'max': sample_df[column].max(),
                        'median': sample_df[column].median()
                    })
                
                # إحصائيات للأعمدة النصية
                elif sample_df[column].dtype == 'object':
                    col_stats.update({
                        'most_frequent': sample_df[column].mode().iloc[0] if not sample_df[column].mode().empty else None,
                        'avg_length': sample_df[column].astype(str).str.len().mean()
                    })
                
                stats['column_stats'][column] = col_stats
            
            return stats
            
        except Exception as e:
            logger.error(f"خطأ في حساب الإحصائيات: {str(e)}")
            return {}

# إنشاء مثيل عام للاتصال
db_connector = OracleConnector(
    sid="ORCL",
    username="IAS20251", 
    password="ys123"
)
