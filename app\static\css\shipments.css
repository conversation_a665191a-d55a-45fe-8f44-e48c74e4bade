/* 
تنسيق صفحة الشحنات
Shipments Page Styling
*/

/* 🎨 تنسيق جميل وملون للصفحة */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    font-family: 'Noto Sans Arabic', 'Se<PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif !important;
    color: #212529 !important;
    min-height: 100vh !important;
}

.container-fluid {
    background: rgba(255, 255, 255, 0.95) !important;
    min-height: 100vh !important;
    padding: 20px !important;
    border-radius: 20px !important;
    margin: 15px !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
    backdrop-filter: blur(15px) !important;
}

/* الوضع الداكن للحاوي الرئيسي */
body.dark-mode .container-fluid,
html.dark-mode .container-fluid,
.dark-mode .container-fluid {
    background: rgba(30, 30, 30, 0.95) !important;
    color: #ffffff !important;
}

/* 🎨 تنسيق البطاقات الجميل والمميز */
.card {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%) !important;
    border: none !important;
    border-radius: 25px !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
    margin-bottom: 30px !important;
    overflow: hidden !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    position: relative !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* الوضع الداكن للبطاقات */
body.dark-mode .card,
html.dark-mode .card,
.dark-mode .card {
    background: linear-gradient(145deg, #2d3748 0%, #374151 100%) !important;
    color: #ffffff !important;
    border: 1px solid rgba(74, 85, 104, 0.3) !important;
}

/* خط علوي ثابت للبطاقات */
.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    z-index: 1;
}

.card:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 25px 45px rgba(0, 0, 0, 0.2) !important;
}

/* رأس البطاقة المميز */
.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    padding: 1.5rem 2rem !important;
    font-weight: 600 !important;
    color: white !important;
    border-radius: 25px 25px 0 0 !important;
    position: relative !important;
}

.card-header h1, .card-header h2, .card-header h3,
.card-header h4, .card-header h5, .card-header h6 {
    color: white !important;
    margin: 0 !important;
    font-weight: 600 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
    font-size: 1.1rem !important;
}

.card-header i {
    color: rgba(255, 255, 255, 0.9) !important;
    margin-left: 10px !important;
    font-size: 1rem !important;
}

/* محتوى البطاقة المحسن */
.card-body {
    padding: 2rem !important;
    background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%) !important;
}

/* تحسين النصوص داخل البطاقات */
.card-body h1, .card-body h2, .card-body h3,
.card-body h4, .card-body h5, .card-body h6 {
    color: #2c3e50 !important;
    font-weight: 600 !important;
    margin-bottom: 1rem !important;
}

.card-body p {
    color: #5a6c7d !important;
    line-height: 1.6 !important;
    margin-bottom: 1rem !important;
}

/* تحسين قائمة البطاقات */
.card-text {
    color: #6c757d !important;
    font-size: 0.95rem !important;
    line-height: 1.5 !important;
}

/* تأثير خاص للبطاقات المهمة */
.card.border-primary {
    border: 2px solid #667eea !important;
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2) !important;
}

.card.border-success {
    border: 2px solid #28a745 !important;
    box-shadow: 0 20px 40px rgba(40, 167, 69, 0.2) !important;
}

.card.border-warning {
    border: 2px solid #ffc107 !important;
    box-shadow: 0 20px 40px rgba(255, 193, 7, 0.2) !important;
}

/* 🎨 تحسينات خاصة لبطاقات الإحصائيات */
.card .row .col-md-3,
.card .row .col-md-4,
.card .row .col-md-6 {
    padding: 1rem !important;
}

.card .text-center {
    padding: 1.5rem 1rem !important;
    border-radius: 15px !important;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.8), rgba(248, 249, 250, 0.8)) !important;
    margin: 0.5rem !important;
    transition: all 0.3s ease !important;
    border: 1px solid rgba(102, 126, 234, 0.1) !important;
}

.card .text-center:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08) !important;
    background: linear-gradient(145deg, rgba(255, 255, 255, 1), rgba(240, 248, 255, 1)) !important;
}

.card .text-center h1,
.card .text-center h2,
.card .text-center h3 {
    font-weight: 700 !important;
    margin-bottom: 0.5rem !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.card .text-primary {
    color: #667eea !important;
    font-weight: 700 !important;
}

.card .text-success {
    color: #28a745 !important;
    font-weight: 700 !important;
}

.card .text-warning {
    color: #ffc107 !important;
    font-weight: 700 !important;
}

.card .text-info {
    color: #17a2b8 !important;
    font-weight: 700 !important;
}

/* 🎨 تحسين البطاقات الفرعية */
.card .card {
    border-radius: 15px !important;
    margin: 1rem 0 !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08) !important;
}

/* تحسين البطاقات في الشاشات الصغيرة */
@media (max-width: 768px) {
    .card {
        border-radius: 20px !important;
        margin-bottom: 20px !important;
    }

    .card-header {
        padding: 1.5rem 1.5rem !important;
    }

    .card-body {
        padding: 1.5rem !important;
    }

    .card .text-center {
        margin: 0.25rem !important;
        padding: 1rem 0.5rem !important;
    }
}

/* تحسين البطاقات عند التركيز */
.card:focus-within {
    transform: translateY(-2px) !important;
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.15) !important;
}

/* 🎨 تنسيق الأزرار الجميل */
.btn {
    border-radius: 25px !important;
    font-weight: 600 !important;
    padding: 0.6rem 1.8rem !important;
    border: none !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a42a0 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4) !important;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4) !important;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4) !important;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
    box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4) !important;
}

/* 🔧 تحسين الأزرار الصغيرة وأزرار شريط الأدوات */
.btn-sm {
    border-radius: 20px !important;
    font-weight: 600 !important;
    padding: 0.5rem 1.2rem !important;
    font-size: 0.875rem !important;
    border: none !important;
    transition: all 0.3s ease !important;
    min-width: 120px !important;
    text-align: center !important;
}

/* أزرار outline محسنة */
.btn-outline-primary {
    background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%) !important;
    border: 2px solid #667eea !important;
    color: #667eea !important;
    box-shadow: 0 3px 10px rgba(102, 126, 234, 0.2) !important;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3) !important;
}

.btn-outline-secondary {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    border: 2px solid #6c757d !important;
    color: #6c757d !important;
    box-shadow: 0 3px 10px rgba(108, 117, 125, 0.2) !important;
}

.btn-outline-secondary:hover {
    background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4) !important;
}

.btn-outline-success {
    background: linear-gradient(135deg, #ffffff 0%, #f0fff4 100%) !important;
    border: 2px solid #28a745 !important;
    color: #28a745 !important;
    box-shadow: 0 3px 10px rgba(40, 167, 69, 0.2) !important;
}

.btn-outline-success:hover {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4) !important;
}

.btn-outline-warning {
    background: linear-gradient(135deg, #ffffff 0%, #fffbf0 100%) !important;
    border: 2px solid #ffc107 !important;
    color: #856404 !important;
    box-shadow: 0 3px 10px rgba(255, 193, 7, 0.2) !important;
}

.btn-outline-warning:hover {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(255, 193, 7, 0.4) !important;
}

.btn-outline-info {
    background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%) !important;
    border: 2px solid #17a2b8 !important;
    color: #17a2b8 !important;
    box-shadow: 0 3px 10px rgba(23, 162, 184, 0.2) !important;
}

.btn-outline-info:hover {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4) !important;
}
    color: #ffffff !important;
}

.btn-success {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: #ffffff !important;
}

.btn-warning {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #212529 !important;
}

.btn-danger {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: #ffffff !important;
}

/* 🎨 تنسيق الجداول الجميل */
.table {
    background: white !important;
    border-radius: 15px !important;
    overflow: hidden !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
    border: none !important;
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border: none !important;
    font-weight: 600 !important;
    padding: 1.2rem 1rem !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    position: relative !important;
}

.table tbody tr {
    transition: background-color 0.2s ease !important;
    border: none !important;
}

.table tbody tr:nth-child(even) {
    background: #f8f9fa !important;
}

.table tbody tr:hover {
    background: #e3f2fd !important;
    transform: none !important;
    box-shadow: none !important;
}

.table tbody td {
    border: none !important;
    padding: 1rem !important;
    vertical-align: middle !important;
}

/* 🎨 تنسيق الشارات الجميل */
.badge {
    border-radius: 20px !important;
    padding: 0.6rem 1.2rem !important;
    font-weight: 600 !important;
    font-size: 0.85rem !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
    position: relative !important;
    overflow: hidden !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
}

.badge.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%) !important;
}

/* 🛠️ تحسين شريط الأدوات والأيقونات */
.card-header .d-flex {
    gap: 10px !important;
}

.card-header .btn {
    min-width: auto !important;
    padding: 0.5rem 1rem !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    border-radius: 20px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    white-space: nowrap !important;
}

.card-header .btn i {
    margin-left: 5px !important;
    font-size: 1rem !important;
}

/* تحسين أزرار شريط الأدوات العلوي */
.btn-group .btn {
    margin-left: 5px !important;
    margin-bottom: 5px !important;
}

/* تحسين الأيقونات */
.fas, .far, .fab {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)) !important;
}

/* تحسين النصوص في الأزرار */
.btn {
    text-decoration: none !important;
    line-height: 1.5 !important;
}

.btn:focus, .btn:active {
    outline: none !important;
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25) !important;
}

/* تحسين الأزرار في الحاويات المختلفة */
.container-fluid .btn,
.card .btn,
.modal .btn {
    font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* تحسين أزرار الإجراءات */
.btn-group-sm > .btn, .btn-sm {
    padding: 0.4rem 1rem !important;
    font-size: 0.85rem !important;
    border-radius: 18px !important;
}

/* تحسين الأزرار في الجداول */
.table .btn {
    padding: 0.3rem 0.8rem !important;
    font-size: 0.8rem !important;
    border-radius: 15px !important;
    margin: 2px !important;
}

/* 📝 تحسين النصوص والتباعد */
.btn {
    letter-spacing: 0.5px !important;
    text-transform: none !important;
}

/* 🔧 إصلاح قوي وواضح لأزرار شريط الأدوات */
.card-header .btn,
.card-header .btn-outline-primary,
.card-header .btn-outline-secondary,
.card-header .btn-outline-success,
.card-header .btn-outline-warning,
.card-header .btn-outline-info,
.card-header .btn-success {
    min-width: 140px !important;
    height: 45px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: 700 !important;
    font-size: 1rem !important;
    margin-left: 10px !important;
    margin-bottom: 8px !important;
    padding: 0.75rem 1.25rem !important;
    border-width: 2px !important;
    border-radius: 30px !important;
    text-decoration: none !important;
    white-space: nowrap !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15) !important;
    text-transform: none !important;
    letter-spacing: 0.5px !important;
}

/* تحسين قوي لألوان الأزرار */
.card-header .btn-outline-primary {
    background: linear-gradient(135deg, #ffffff 0%, #e3f2fd 100%) !important;
    border-color: #2196f3 !important;
    color: #1976d2 !important;
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.3) !important;
    font-weight: 700 !important;
}

.card-header .btn-outline-primary:hover {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%) !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3) !important;
    border-color: #1976d2 !important;
}

.card-header .btn-outline-secondary {
    background: linear-gradient(135deg, #ffffff 0%, #f5f5f5 100%) !important;
    border-color: #757575 !important;
    color: #424242 !important;
    box-shadow: 0 6px 20px rgba(117, 117, 117, 0.3) !important;
    font-weight: 700 !important;
}

.card-header .btn-outline-secondary:hover {
    background: linear-gradient(135deg, #757575 0%, #616161 100%) !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(117, 117, 117, 0.3) !important;
    border-color: #616161 !important;
}

.card-header .btn-outline-info {
    background: linear-gradient(135deg, #ffffff 0%, #e0f7fa 100%) !important;
    border-color: #00bcd4 !important;
    color: #00838f !important;
    box-shadow: 0 6px 20px rgba(0, 188, 212, 0.3) !important;
    font-weight: 700 !important;
}

.card-header .btn-outline-info:hover {
    background: linear-gradient(135deg, #00bcd4 0%, #00838f 100%) !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3) !important;
    border-color: #00838f !important;
}

.card-header .btn-success {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%) !important;
    border: 2px solid #4caf50 !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4) !important;
    font-weight: 700 !important;
}

.card-header .btn-success:hover {
    background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4) !important;
    border-color: #2e7d32 !important;
}

/* تحسين خاص لزر التصدير الأخضر */
.card-header .btn-outline-success {
    background: linear-gradient(135deg, #ffffff 0%, #e8f5e8 100%) !important;
    border-color: #4caf50 !important;
    color: #2e7d32 !important;
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3) !important;
    font-weight: 700 !important;
}

.card-header .btn-outline-success:hover {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%) !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3) !important;
    border-color: #2e7d32 !important;
}

/* تحسين قوي للأيقونات والنصوص */
.card-header .btn i {
    font-size: 1.1rem !important;
    margin-left: 8px !important;
    filter: none !important;
    font-weight: 900 !important;
}

.card-header .btn span {
    font-weight: 700 !important;
    letter-spacing: 0.5px !important;
    font-size: 0.95rem !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* إجبار ظهور النصوص */
.card-header .btn .d-none.d-lg-inline {
    display: inline !important;
}

/* تحسين الأزرار عند التركيز */
.card-header .btn:focus,
.card-header .btn:active {
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25) !important;
}

/* إجبار عرض الأزرار بشكل صحيح */
.card-header .btn {
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
}

/* تحسين للشاشات الصغيرة والمتوسطة */
@media (max-width: 1200px) {
    .card-header .btn span {
        display: inline !important;
    }

    .card-header .btn {
        min-width: 120px !important;
        font-size: 0.9rem !important;
    }
}

@media (max-width: 768px) {
    .card-header .btn span {
        display: none !important;
    }

    .card-header .btn {
        min-width: 50px !important;
        padding: 0.6rem !important;
        margin-left: 5px !important;
    }

    .card-header .btn i {
        margin-left: 0 !important;
        font-size: 1.2rem !important;
    }
}

/* تحسين btn-group */
.card-header .btn-group {
    gap: 8px !important;
    flex-wrap: wrap !important;
}

.card-header .btn-group .btn {
    margin: 0 !important;
}

/* تحسين الأزرار مع النصوص الطويلة */
.btn {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* تحسين الأزرار في الشاشات الصغيرة */
@media (max-width: 768px) {
    .btn {
        padding: 0.4rem 0.8rem !important;
        font-size: 0.8rem !important;
        min-width: 80px !important;
    }

    .card-header .btn {
        margin-bottom: 5px !important;
        width: 100% !important;
        max-width: 150px !important;
    }
}

/* تحسين الأزرار عند التحميل */
.btn {
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
}

/* تأكيد ظهور النصوص */
.btn, .btn * {
    color: inherit !important;
    text-decoration: none !important;
}

/* تنسيق الشارات */
.badge {
    font-size: 0.75rem !important;
    padding: 0.375rem 0.75rem !important;
    border-radius: 0.375rem !important;
    font-weight: 500 !important;
}

/* تنسيق النماذج */
.form-control {
    border: 1px solid #ced4da !important;
    border-radius: 6px !important;
    padding: 8px 12px !important;
    background-color: #ffffff !important;
    color: #495057 !important;
}

.form-select {
    border: 1px solid #ced4da !important;
    border-radius: 6px !important;
    padding: 8px 12px !important;
    background-color: #ffffff !important;
    color: #495057 !important;
}

/* تنسيق خاص بالشحنات */
.shipment-card {
    transition: all 0.3s ease !important;
    border: 1px solid #dee2e6 !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
    background-color: #ffffff !important;
}

.shipment-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 5px 20px rgba(0,0,0,0.15) !important;
}

/* تنسيق الحالات */
.shipment-status-badge {
    font-size: 0.75rem !important;
    padding: 0.375rem 0.75rem !important;
    border-radius: 0.375rem !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.shipment-status-badge:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important;
}

.release-status-badge {
    font-size: 0.75rem !important;
    padding: 0.375rem 0.75rem !important;
    border-radius: 0.375rem !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.release-status-badge:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important;
}

.clickable-status {
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.clickable-status:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important;
}

/* ألوان حالات الشحنة */
.bg-draft { background-color: #6c757d !important; color: white !important; }
.bg-confirmed { background-color: #007bff !important; color: white !important; }
.bg-in_transit { background-color: #fd7e14 !important; color: white !important; }
.bg-arrived_port { background-color: #17a2b8 !important; color: white !important; }
.bg-customs_clearance { background-color: #ffc107 !important; color: #212529 !important; }
.bg-ready_pickup { background-color: #20c997 !important; color: white !important; }
.bg-delivered { background-color: #28a745 !important; color: white !important; }
.bg-cancelled { background-color: #dc3545 !important; color: white !important; }
.bg-delayed { background-color: #e83e8c !important; color: white !important; }
.bg-returned { background-color: #6f42c1 !important; color: white !important; }

/* ألوان حالات الإفراج */
.bg-pending { background-color: #6c757d !important; color: white !important; }
.bg-documents_review { background-color: #17a2b8 !important; color: white !important; }
.bg-payment_verification { background-color: #ffc107 !important; color: #212529 !important; }
.bg-quality_check { background-color: #007bff !important; color: white !important; }
.bg-approved { background-color: #28a745 !important; color: white !important; }
.bg-released { background-color: #28a745 !important; color: white !important; }
.bg-on_hold { background-color: #dc3545 !important; color: white !important; }
.bg-rejected { background-color: #dc3545 !important; color: white !important; }

/* تنسيق إضافي للصفحة */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 20px !important;
    border-radius: 8px !important;
    margin-bottom: 20px !important;
}

.stats-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 8px !important;
}

.filter-section {
    background-color: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 8px !important;
    padding: 15px !important;
    margin-bottom: 20px !important;
}

.table-responsive {
    border-radius: 8px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    background-color: #ffffff !important;
}

.modal-content {
    border-radius: 8px !important;
    border: none !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
}

.modal-header {
    border-bottom: 1px solid #dee2e6 !important;
    border-radius: 8px 8px 0 0 !important;
}

.modal-footer {
    border-top: 1px solid #dee2e6 !important;
    border-radius: 0 0 8px 8px !important;
}

.status-badge {
    font-size: 0.8rem !important;
    padding: 0.4rem 0.8rem !important;
    border-radius: 20px !important;
    font-weight: 500 !important;
}

.tracking-number {
    font-family: 'Courier New', monospace !important;
    font-weight: bold !important;
    color: #007bff !important;
}

.progress-bar-custom {
    height: 8px !important;
    border-radius: 10px !important;
}

.shipment-map {
    height: 400px !important;
    border-radius: 10px !important;
}

.realtime-indicator {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* تحسين مظهر الخريطة */
.shipment-map-full .text-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
}

.shipment-map-full i {
    color: #6c757d;
    opacity: 0.7;
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    .shipment-map-full {
        height: 300px;
    }

    .container-fluid {
        padding: 10px !important;
    }

    .card-body {
        padding: 15px !important;
    }
}

/* === إصلاح نهائي وقوي للوضع الداكن === */
/* هذا CSS له أولوية قصوى ويلغي جميع القواعد السابقة */

/* فرض قوي على الحاوي الرئيسي */
body.dark-mode .container-fluid,
html.dark-mode .container-fluid,
body.dark-mode.professional-dashboard .container-fluid,
html.dark-mode.professional-dashboard .container-fluid {
    background: rgba(30, 30, 30, 0.95) !important;
    color: #ffffff !important;
}

/* فرض قوي على البطاقات */
body.dark-mode .card,
html.dark-mode .card,
body.dark-mode .content-card,
html.dark-mode .content-card,
body.dark-mode #shipmentsTableCard,
html.dark-mode #shipmentsTableCard {
    background: #2d3748 !important;
    color: #ffffff !important;
    border-color: #4a5568 !important;
}

/* فرض قوي على رؤوس البطاقات */
body.dark-mode .card-header,
html.dark-mode .card-header,
body.dark-mode #shipmentsTableHeader,
html.dark-mode #shipmentsTableHeader {
    background: #1a202c !important;
    color: #ffffff !important;
    border-bottom: 1px solid #4a5568 !important;
}

/* فرض قوي على أجسام البطاقات */
body.dark-mode .card-body,
html.dark-mode .card-body,
body.dark-mode #shipmentsTableBody,
html.dark-mode #shipmentsTableBody {
    background: #2d3748 !important;
    color: #ffffff !important;
}

/* فرض قوي على الأزرار المحددة */
body.dark-mode #refreshBtn,
html.dark-mode #refreshBtn,
body.dark-mode #exportBtn,
html.dark-mode #exportBtn {
    background: transparent !important;
    border: 1px solid #718096 !important;
    color: #a0aec0 !important;
}

body.dark-mode #newShipmentBtn,
html.dark-mode #newShipmentBtn {
    background: #3182ce !important;
    border: 1px solid #3182ce !important;
    color: #ffffff !important;
}

/* فرض قوي على جميع عناصر الجدول */
body.dark-mode .shipments-table-component,
html.dark-mode .shipments-table-component,
body.dark-mode .shipments-table-component *,
html.dark-mode .shipments-table-component *,
body.dark-mode .table-responsive,
html.dark-mode .table-responsive,
body.dark-mode .table,
html.dark-mode .table,
body.dark-mode table,
html.dark-mode table {
    background: #2d3748 !important;
    color: #ffffff !important;
    border-color: #4a5568 !important;
}

/* فرض قوي على رؤوس الجداول */
body.dark-mode thead,
html.dark-mode thead,
body.dark-mode thead th,
html.dark-mode thead th,
body.dark-mode .bg-light,
html.dark-mode .bg-light {
    background: #1a202c !important;
    color: #ffffff !important;
}

/* فرض قوي على تذييل الجدول */
body.dark-mode .table-footer,
html.dark-mode .table-footer {
    background: #2d3748 !important;
    color: #ffffff !important;
}

/* فرض قوي على النصوص الباهتة */
body.dark-mode .text-muted,
html.dark-mode .text-muted {
    color: #a0aec0 !important;
}

/* === زر تبديل عرض الجوال === */
#mobileViewToggle {
    position: relative !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: 2px solid #667eea !important;
    color: #ffffff !important;
    border-radius: 10px !important;
    padding: 10px 15px !important;
    transition: all 0.3s ease !important;
    font-weight: 600 !important;
    min-width: 100px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
}

#mobileViewToggle:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%) !important;
    transform: scale(1.05) !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
}

#mobileViewToggle .view-text {
    font-size: 0.8rem !important;
    margin-left: 5px !important;
    font-weight: 600 !important;
}

#mobileViewToggle i {
    font-size: 1rem !important;
}

/* حالة نشطة للزر */
#mobileViewToggle.active {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%) !important;
    border-color: #48bb78 !important;
}

#mobileViewToggle.active:hover {
    background: linear-gradient(135deg, #38a169 0%, #48bb78 100%) !important;
}

/* الوضع الداكن للزر */
body.dark-mode #mobileViewToggle,
html.dark-mode #mobileViewToggle {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-color: #667eea !important;
    color: #ffffff !important;
}

body.dark-mode #mobileViewToggle:hover,
html.dark-mode #mobileViewToggle:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%) !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.6) !important;
}

body.dark-mode #mobileViewToggle.active,
html.dark-mode #mobileViewToggle.active {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%) !important;
    border-color: #48bb78 !important;
}

/* === عرض البطاقات للجوال - إصلاح جذري === */
.cards-view {
    width: 100vw !important;
    max-width: 100vw !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
    position: relative !important;
    left: 0 !important;
    right: 0 !important;
}

.mobile-cards-container {
    width: 95vw !important;
    max-width: 95vw !important;
    padding: 2px !important;
    margin: 0 auto !important;
    box-sizing: border-box !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 3px !important;
    overflow-x: hidden !important;
}

.shipment-card {
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 4px !important;
    padding: 4px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    margin: 0 !important;
    overflow: hidden !important;
    position: relative !important;
}

.shipment-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.shipment-card-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 2px;
    padding-bottom: 1px;
    border-bottom: 1px solid #e2e8f0;
    gap: 8px;
}

.shipment-number {
    font-size: 0.8rem;
    font-weight: bold;
    color: #2d3748;
    flex-shrink: 0;
    max-width: 60%;
}

.status-badges {
    flex-shrink: 0;
    margin-left: auto;
}

.shipment-card-body {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2px 0;
}

.info-label {
    font-weight: 600;
    color: #4a5568;
    font-size: 0.7rem;
    min-width: 50px;
}

.info-value {
    color: #2d3748;
    font-size: 0.7rem;
    text-align: right;
    flex: 1;
}

.shipment-card-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    padding-top: 8px;
    border-top: 1px solid #e2e8f0;
}

.card-action-btn {
    flex: 1;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 600;
    text-align: center;
    transition: all 0.2s ease;
    border: 1px solid;
    text-decoration: none;
    display: inline-block;
}

.card-action-btn.primary {
    background: #3182ce;
    border-color: #3182ce;
    color: #ffffff;
}

.card-action-btn.primary:hover {
    background: #2c5aa0;
    border-color: #2c5aa0;
}

.card-action-btn.secondary {
    background: transparent;
    border-color: #cbd5e0;
    color: #4a5568;
}

.card-action-btn.secondary:hover {
    background: #f7fafc;
    border-color: #a0aec0;
}

/* الوضع الداكن للبطاقات */
body.dark-mode .shipment-card,
html.dark-mode .shipment-card {
    background: #2d3748 !important;
    border-color: #4a5568 !important;
    color: #ffffff !important;
}

body.dark-mode .shipment-card-header,
html.dark-mode .shipment-card-header {
    border-bottom-color: #4a5568 !important;
}

body.dark-mode .shipment-number,
html.dark-mode .shipment-number {
    color: #ffffff !important;
}

body.dark-mode .info-label,
html.dark-mode .info-label {
    color: #a0aec0 !important;
}

body.dark-mode .info-value,
html.dark-mode .info-value {
    color: #ffffff !important;
}

body.dark-mode .shipment-card-actions,
html.dark-mode .shipment-card-actions {
    border-top-color: #4a5568 !important;
}

body.dark-mode .card-action-btn.secondary,
html.dark-mode .card-action-btn.secondary {
    border-color: #4a5568 !important;
    color: #a0aec0 !important;
}

body.dark-mode .card-action-btn.secondary:hover,
html.dark-mode .card-action-btn.secondary:hover {
    background: #374151 !important;
    border-color: #718096 !important;
    color: #ffffff !important;
}

/* رسالة عدم وجود بيانات */
.no-data {
    text-align: center;
    padding: 40px 20px;
    color: #718096;
    font-size: 1.1rem;
    font-weight: 500;
}

body.dark-mode .no-data,
html.dark-mode .no-data {
    color: #a0aec0 !important;
}

/* تحسين عرض الشارات في البطاقات */
.shipment-card .badge {
    font-size: 0.8rem !important;
    padding: 4px 8px !important;
    border-radius: 6px !important;
}

.status-badges {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

/* === إصلاح العرض الكامل للجوال === */
@media (max-width: 768px) {
    /* إعادة تعيين كامل للجوال */
    body, html {
        width: 100vw !important;
        max-width: 100vw !important;
        overflow-x: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        box-sizing: border-box !important;
    }

    /* الحاوي الرئيسي */
    .container-fluid {
        width: 100vw !important;
        max-width: 100vw !important;
        padding: 0 !important;
        margin: 0 !important;
        border-radius: 0 !important;
        box-sizing: border-box !important;
    }

    /* البطاقات الرئيسية */
    .card, .content-card {
        width: 100vw !important;
        max-width: 100vw !important;
        margin: 0 !important;
        border-radius: 0 !important;
        border-left: none !important;
        border-right: none !important;
        box-sizing: border-box !important;
    }

    /* جسم البطاقة */
    .card-body {
        padding: 0 !important;
        width: 100vw !important;
        max-width: 100vw !important;
        box-sizing: border-box !important;
    }

    /* عرض البطاقات للجوال */
    .cards-view {
        width: 100vw !important;
        max-width: 100vw !important;
        padding: 0 !important;
        margin: 0 !important;
        box-sizing: border-box !important;
    }

    /* حاوي البطاقات - العرض الكامل */
    .mobile-cards-container {
        width: 100vw !important;
        max-width: 100vw !important;
        padding: 8px !important;
        margin: 0 !important;
        box-sizing: border-box !important;
        display: flex !important;
        flex-direction: column !important;
        gap: 12px !important;
        overflow-x: hidden !important;
    }

    /* البطاقات الفردية - العرض الكامل */
    .shipment-card {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        padding: 4px !important;
        box-sizing: border-box !important;
        border-radius: 4px !important;
        overflow: hidden !important;
        background: #ffffff !important;
        border: 1px solid #e2e8f0 !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
        position: relative !important;
        transition: all 0.3s ease !important;
    }

    /* محتوى البطاقة - تحسين التخطيط */
    .shipment-card-header {
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin-bottom: 12px !important;
        padding-bottom: 8px !important;
        border-bottom: 1px solid #e2e8f0 !important;
    }

    .shipment-card-body {
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
        display: flex !important;
        flex-direction: column !important;
        gap: 8px !important;
    }

    .shipment-card-actions {
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
        display: flex !important;
        gap: 8px !important;
        margin-top: 12px !important;
        padding-top: 8px !important;
        border-top: 1px solid #e2e8f0 !important;
    }

    /* الصفوف داخل البطاقة - تحسين التخطيط */
    .info-row {
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 6px 0 !important;
        flex-wrap: wrap !important;
    }

    /* النصوص - أحجام أكبر ووضوح أفضل */
    .info-label {
        font-weight: 600 !important;
        color: #4a5568 !important;
        font-size: 0.9rem !important;
        min-width: 100px !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
    }

    .info-value {
        color: #2d3748 !important;
        font-size: 0.9rem !important;
        text-align: right !important;
        flex: 1 !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
        max-width: calc(100% - 110px) !important;
    }

    /* رقم الشحنة */
    .shipment-number {
        font-size: 1.1rem !important;
        font-weight: bold !important;
        color: #2d3748 !important;
    }

    /* مجموعة الشارات */
    .status-badges {
        display: flex !important;
        gap: 5px !important;
        flex-wrap: wrap !important;
        align-items: center !important;
    }

    /* الأزرار - أحجام مناسبة للمس */
    .card-action-btn {
        flex: 1 !important;
        min-height: 44px !important;
        font-size: 0.9rem !important;
        font-weight: 600 !important;
        padding: 12px 16px !important;
        border-radius: 6px !important;
        text-align: center !important;
        transition: all 0.2s ease !important;
        border: 1px solid !important;
        text-decoration: none !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        box-sizing: border-box !important;
    }

    .card-action-btn.primary {
        background: #3182ce !important;
        border-color: #3182ce !important;
        color: #ffffff !important;
    }

    .card-action-btn.primary:hover {
        background: #2c5aa0 !important;
        border-color: #2c5aa0 !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 8px rgba(49, 130, 206, 0.3) !important;
    }

    .card-action-btn.secondary {
        background: transparent !important;
        border-color: #cbd5e0 !important;
        color: #4a5568 !important;
    }

    .card-action-btn.secondary:hover {
        background: #f7fafc !important;
        border-color: #a0aec0 !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    }

    /* === الوضع الداكن للبطاقات === */

    body.dark-mode .shipment-card,
    html.dark-mode .shipment-card {
        background: #2d3748 !important;
        border-color: #4a5568 !important;
        color: #ffffff !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
    }

    body.dark-mode .shipment-card-header,
    html.dark-mode .shipment-card-header {
        border-bottom-color: #4a5568 !important;
    }

    body.dark-mode .shipment-card-actions,
    html.dark-mode .shipment-card-actions {
        border-top-color: #4a5568 !important;
    }

    body.dark-mode .shipment-number,
    html.dark-mode .shipment-number {
        color: #ffffff !important;
    }

    body.dark-mode .info-label,
    html.dark-mode .info-label {
        color: #a0aec0 !important;
    }

    body.dark-mode .info-value,
    html.dark-mode .info-value {
        color: #ffffff !important;
    }

    body.dark-mode .card-action-btn.secondary,
    html.dark-mode .card-action-btn.secondary {
        border-color: #4a5568 !important;
        color: #a0aec0 !important;
    }

    body.dark-mode .card-action-btn.secondary:hover,
    html.dark-mode .card-action-btn.secondary:hover {
        background: #374151 !important;
        border-color: #718096 !important;
        color: #ffffff !important;
    }

    /* تأثيرات hover للبطاقات */
    .shipment-card:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
    }

    body.dark-mode .shipment-card:hover,
    html.dark-mode .shipment-card:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4) !important;
    }

    /* تحسين الشارات في البطاقات */
    .shipment-card .badge {
        font-size: 0.75rem !important;
        padding: 4px 8px !important;
        border-radius: 6px !important;
        font-weight: 600 !important;
        white-space: nowrap !important;
    }

    /* شريط التنقل */
    .shipments-navbar {
        width: 100vw !important;
        max-width: 100vw !important;
        padding: 8px 5px !important;
        margin: 0 !important;
        box-sizing: border-box !important;
    }

    /* أزرار شريط التنقل */
    .nav-tools {
        gap: 5px !important;
    }

    .btn-tool {
        padding: 6px 8px !important;
        font-size: 0.8rem !important;
        min-width: auto !important;
    }

    #mobileViewToggle {
        min-width: 80px !important;
        padding: 6px 10px !important;
    }

    #mobileViewToggle .view-text {
        font-size: 0.7rem !important;
        margin-left: 3px !important;
    }

    /* === إصلاح جذري لجدول الشحنات في الجوال === */

    /* فرض العرض الكامل على جميع حاويات الجدول */
    .shipments-table-component {
        width: 100vw !important;
        max-width: 100vw !important;
        margin: 0 !important;
        padding: 0 !important;
        border-radius: 0 !important;
        overflow: hidden !important;
        box-sizing: border-box !important;
        position: relative !important;
        left: 0 !important;
        right: 0 !important;
    }

    /* حاوية الجدول المتجاوبة - العرض الكامل */
    .shipments-table-component .table-responsive {
        width: 100vw !important;
        max-width: 100vw !important;
        margin: 0 !important;
        padding: 0 !important;
        border-radius: 0 !important;
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
        box-sizing: border-box !important;
        border: none !important;
        position: relative !important;
        left: 0 !important;
        right: 0 !important;
    }

    /* الجدول نفسه - عرض كامل مع تمرير أفقي */
    .shipments-table-component .table,
    .shipments-table-component #shipmentsTable {
        width: 100% !important;
        min-width: 1000px !important; /* عرض أدنى للتمرير الأفقي */
        margin: 0 !important;
        border-collapse: collapse !important;
        font-size: 0.8rem !important;
        box-sizing: border-box !important;
        background-color: transparent !important;
        table-layout: fixed !important; /* تخطيط ثابت للأعمدة */
    }

    /* خلايا الجدول - أحجام مناسبة للجوال */
    .shipments-table-component .table th,
    .shipments-table-component .table td {
        padding: 8px 6px !important;
        font-size: 0.75rem !important;
        white-space: nowrap !important;
        border: 1px solid #dee2e6 !important;
        min-width: 90px !important;
        max-width: 150px !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        box-sizing: border-box !important;
        vertical-align: middle !important;
        word-wrap: break-word !important;
    }

    /* رؤوس الجدول - ثابتة ومقروءة */
    .shipments-table-component .table thead th {
        background-color: #f8f9fa !important;
        font-weight: bold !important;
        font-size: 0.7rem !important;
        padding: 10px 6px !important;
        position: sticky !important;
        top: 0 !important;
        z-index: 10 !important;
        border-bottom: 2px solid #dee2e6 !important;
        text-align: center !important;
        line-height: 1.2 !important;
    }

    /* إخفاء أعمدة أقل أهمية في الجوال */
    .shipments-table-component .table th:nth-child(n+9),
    .shipments-table-component .table td:nth-child(n+9) {
        display: none !important;
    }

    /* تذييل الجدول */
    .shipments-table-component .table-footer {
        width: 100vw !important;
        max-width: 100vw !important;
        margin: 0 !important;
        padding: 8px !important;
        border-radius: 0 !important;
        box-sizing: border-box !important;
        background-color: #f8f9fa !important;
    }

    /* الشارات في الجوال - أكبر وأوضح */
    .shipments-table-component .table .badge {
        font-size: 0.65rem !important;
        padding: 3px 6px !important;
        border-radius: 4px !important;
        white-space: nowrap !important;
        font-weight: 600 !important;
        min-width: 50px !important;
        text-align: center !important;
    }

    /* الأزرار في الجوال - أكبر للمس */
    .shipments-table-component .table .btn {
        padding: 4px 8px !important;
        font-size: 0.65rem !important;
        border-radius: 4px !important;
        min-width: 60px !important;
        white-space: nowrap !important;
        margin: 1px !important;
    }

    /* الروابط في الجوال */
    .shipments-table-component .table a {
        font-size: 0.7rem !important;
        text-decoration: none !important;
    }

    /* إصلاح البطاقة الحاوية للجدول */
    #shipmentsTableCard {
        width: 90vw !important;
        max-width: 90vw !important;
        margin: 0 auto !important;
        border-radius: 8px !important;
        border: 1px solid #e2e8f0 !important;
        box-sizing: border-box !important;
    }

    #shipmentsTableCard .card-body {
        width: 100% !important;
        max-width: 100% !important;
        padding: 8px !important;
        margin: 0 !important;
        box-sizing: border-box !important;
    }

    /* إصلاح عرض الجدول داخل البطاقة */
    #tableView {
        width: 100vw !important;
        max-width: 100vw !important;
        margin: 0 !important;
        padding: 0 !important;
        box-sizing: border-box !important;
        overflow: hidden !important;
    }

    /* === الوضع الداكن للجدول في الجوال === */

    body.dark-mode .shipments-table-component,
    html.dark-mode .shipments-table-component {
        background-color: #2d3748 !important;
        color: #ffffff !important;
    }

    body.dark-mode .shipments-table-component .table-responsive,
    html.dark-mode .shipments-table-component .table-responsive {
        background-color: #2d3748 !important;
        color: #ffffff !important;
    }

    body.dark-mode .shipments-table-component .table,
    body.dark-mode .shipments-table-component #shipmentsTable,
    html.dark-mode .shipments-table-component .table,
    html.dark-mode .shipments-table-component #shipmentsTable {
        background-color: #2d3748 !important;
        color: #ffffff !important;
        border-color: #4a5568 !important;
    }

    body.dark-mode .shipments-table-component .table th,
    body.dark-mode .shipments-table-component .table td,
    html.dark-mode .shipments-table-component .table th,
    html.dark-mode .shipments-table-component .table td {
        background-color: #2d3748 !important;
        color: #ffffff !important;
        border-color: #4a5568 !important;
    }

    body.dark-mode .shipments-table-component .table thead th,
    html.dark-mode .shipments-table-component .table thead th {
        background-color: #1a202c !important;
        color: #ffffff !important;
        border-color: #4a5568 !important;
    }

    body.dark-mode .shipments-table-component .table tbody tr:nth-child(even),
    body.dark-mode .shipments-table-component .table tbody tr:nth-child(even) td,
    html.dark-mode .shipments-table-component .table tbody tr:nth-child(even),
    html.dark-mode .shipments-table-component .table tbody tr:nth-child(even) td {
        background-color: #374151 !important;
    }

    body.dark-mode .shipments-table-component .table-footer,
    html.dark-mode .shipments-table-component .table-footer {
        background-color: #2d3748 !important;
        color: #ffffff !important;
        border-color: #4a5568 !important;
    }

    body.dark-mode .shipments-table-component .bg-light,
    html.dark-mode .shipments-table-component .bg-light {
        background-color: #1a202c !important;
        color: #ffffff !important;
    }

    body.dark-mode .shipments-table-component .text-muted,
    html.dark-mode .shipments-table-component .text-muted {
        color: #a0aec0 !important;
    }

    body.dark-mode .shipments-table-component .table a,
    html.dark-mode .shipments-table-component .table a {
        color: #63b3ed !important;
    }
}

/* === ألوان مميزة للشارات في الوضع الداكن === */
/* شارات ملونة ومشرقة مع تدرجات جميلة */

body.dark-mode .badge,
html.dark-mode .badge {
    font-weight: 600 !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
    transition: all 0.2s ease !important;
}

/* شارة النجاح - أخضر مشرق */
body.dark-mode .badge.bg-success,
html.dark-mode .badge.bg-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%) !important;
    border-color: #68d391 !important;
    color: #ffffff !important;
}

/* شارة التحذير - برتقالي مشرق */
body.dark-mode .badge.bg-warning,
html.dark-mode .badge.bg-warning {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%) !important;
    border-color: #f6ad55 !important;
    color: #ffffff !important;
}

/* شارة المعلومات - أزرق مشرق */
body.dark-mode .badge.bg-info,
html.dark-mode .badge.bg-info {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
    border-color: #63b3ed !important;
    color: #ffffff !important;
}

/* شارة الخطر - أحمر مشرق */
body.dark-mode .badge.bg-danger,
html.dark-mode .badge.bg-danger {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%) !important;
    border-color: #fc8181 !important;
    color: #ffffff !important;
}

/* شارة ثانوية - رمادي مشرق */
body.dark-mode .badge.bg-secondary,
html.dark-mode .badge.bg-secondary {
    background: linear-gradient(135deg, #718096 0%, #4a5568 100%) !important;
    border-color: #a0aec0 !important;
    color: #ffffff !important;
}

/* شارة أساسية - أزرق بنفسجي */
body.dark-mode .badge.bg-primary,
html.dark-mode .badge.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-color: #90cdf4 !important;
    color: #ffffff !important;
}

/* تأثيرات hover للشارات */
body.dark-mode .badge:hover,
html.dark-mode .badge:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4) !important;
}

/* تأثيرات hover محددة لكل لون */
body.dark-mode .badge.bg-success:hover,
html.dark-mode .badge.bg-success:hover {
    background: linear-gradient(135deg, #68d391 0%, #48bb78 100%) !important;
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.4) !important;
}

body.dark-mode .badge.bg-warning:hover,
html.dark-mode .badge.bg-warning:hover {
    background: linear-gradient(135deg, #f6ad55 0%, #ed8936 100%) !important;
    box-shadow: 0 4px 12px rgba(237, 137, 54, 0.4) !important;
}

body.dark-mode .badge.bg-info:hover,
html.dark-mode .badge.bg-info:hover {
    background: linear-gradient(135deg, #63b3ed 0%, #4299e1 100%) !important;
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4) !important;
}

body.dark-mode .badge.bg-danger:hover,
html.dark-mode .badge.bg-danger:hover {
    background: linear-gradient(135deg, #fc8181 0%, #f56565 100%) !important;
    box-shadow: 0 4px 12px rgba(245, 101, 101, 0.4) !important;
}

body.dark-mode .badge.bg-secondary:hover,
html.dark-mode .badge.bg-secondary:hover {
    background: linear-gradient(135deg, #a0aec0 0%, #718096 100%) !important;
    box-shadow: 0 4px 12px rgba(113, 128, 150, 0.4) !important;
}

body.dark-mode .badge.bg-primary:hover,
html.dark-mode .badge.bg-primary:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%) !important;
    box-shadow: 0 4px 12px rgba(118, 75, 162, 0.4) !important;
}

/* === إصلاح خاص لعرض البطاقات في قسم الشحنات للجوال === */
@media (max-width: 768px) {

    /* فرض العرض المحدود على البطاقات في قسم الشحنات */
    #shipmentsTableCard .cards-view {
        width: 100% !important;
        max-width: 100% !important;
        padding: 4px !important;
        margin: 0 !important;
        overflow-x: hidden !important;
        box-sizing: border-box !important;
        position: relative !important;
    }

    #shipmentsTableCard .mobile-cards-container {
        width: 90vw !important;
        max-width: 90vw !important;
        padding: 4px !important;
        margin: 0 auto !important;
        box-sizing: border-box !important;
        display: flex !important;
        flex-direction: column !important;
        gap: 8px !important;
    }

    #shipmentsTableCard .shipment-card {
        width: calc(100vw - 16px) !important;
        max-width: calc(100vw - 16px) !important;
        margin: 0 !important;
        padding: 10px !important;
        box-sizing: border-box !important;
        background: #ffffff !important;
        border: 1px solid #e2e8f0 !important;
        border-radius: 6px !important;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1) !important;
        overflow: hidden !important;
        position: relative !important;
        min-height: auto !important;
        font-size: 0.85rem !important;
    }

    /* إصلاح محتوى البطاقة - أحجام أصغر */
    #shipmentsTableCard .shipment-card-header {
        display: flex !important;
        justify-content: flex-start !important;
        align-items: center !important;
        margin-bottom: 4px !important;
        padding-bottom: 2px !important;
        border-bottom: 1px solid #e2e8f0 !important;
        width: 100% !important;
        box-sizing: border-box !important;
        gap: 6px !important;
    }

    #shipmentsTableCard .shipment-card-body {
        display: flex !important;
        flex-direction: column !important;
        gap: 4px !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }

    #shipmentsTableCard .shipment-card-actions {
        display: flex !important;
        gap: 6px !important;
        margin-top: 8px !important;
        padding-top: 6px !important;
        border-top: 1px solid #e2e8f0 !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }

    #shipmentsTableCard .info-row {
        display: flex !important;
        justify-content: space-between !important;
        align-items: flex-start !important;
        padding: 0 !important;
        width: 100% !important;
        box-sizing: border-box !important;
        flex-wrap: nowrap !important;
        min-height: 12px !important;
        gap: 4px !important;
    }

    #shipmentsTableCard .info-label {
        font-weight: 600 !important;
        color: #4a5568 !important;
        font-size: 0.65rem !important;
        min-width: 32px !important;
        max-width: 32px !important;
        word-wrap: break-word !important;
        line-height: 1.1 !important;
        flex-shrink: 0 !important;
        text-align: left !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }

    #shipmentsTableCard .info-value {
        color: #2d3748 !important;
        font-size: 0.65rem !important;
        text-align: right !important;
        line-height: 1.1 !important;
        flex: 1 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        max-width: calc(100% - 38px) !important;
    }

    #shipmentsTableCard .shipment-number {
        font-size: 0.7rem !important;
        font-weight: bold !important;
        color: #2d3748 !important;
        line-height: 1.2 !important;
        flex-shrink: 0 !important;
        max-width: 60% !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
    }

    #shipmentsTableCard .status-badges {
        display: flex !important;
        gap: 2px !important;
        flex-wrap: nowrap !important;
        align-items: center !important;
        flex-shrink: 0 !important;
        margin-left: auto !important;
    }

    #shipmentsTableCard .badge {
        font-size: 0.65rem !important;
        padding: 2px 6px !important;
        border-radius: 4px !important;
        font-weight: 600 !important;
        white-space: nowrap !important;
        line-height: 1.2 !important;
        min-height: 18px !important;
        display: inline-flex !important;
        align-items: center !important;
    }

    #shipmentsTableCard .card-action-btn {
        flex: 1 !important;
        min-height: 34px !important;
        font-size: 0.7rem !important;
        font-weight: 600 !important;
        padding: 6px 8px !important;
        border-radius: 4px !important;
        text-align: center !important;
        border: 1px solid !important;
        text-decoration: none !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        box-sizing: border-box !important;
        transition: all 0.2s ease !important;
        line-height: 1.2 !important;
    }

    #shipmentsTableCard .card-action-btn.primary {
        background: #3182ce !important;
        border-color: #3182ce !important;
        color: #ffffff !important;
    }

    #shipmentsTableCard .card-action-btn.primary:hover {
        background: #2c5aa0 !important;
        border-color: #2c5aa0 !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 8px rgba(49, 130, 206, 0.3) !important;
    }

    #shipmentsTableCard .card-action-btn.secondary {
        background: transparent !important;
        border-color: #cbd5e0 !important;
        color: #4a5568 !important;
    }

    #shipmentsTableCard .card-action-btn.secondary:hover {
        background: #f7fafc !important;
        border-color: #a0aec0 !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    }

    /* === الوضع الداكن للبطاقات في قسم الشحنات === */

    body.dark-mode #shipmentsTableCard .shipment-card,
    html.dark-mode #shipmentsTableCard .shipment-card {
        background: #2d3748 !important;
        border-color: #4a5568 !important;
        color: #ffffff !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
    }

    body.dark-mode #shipmentsTableCard .shipment-card-header,
    html.dark-mode #shipmentsTableCard .shipment-card-header {
        border-bottom-color: #4a5568 !important;
    }

    body.dark-mode #shipmentsTableCard .shipment-card-actions,
    html.dark-mode #shipmentsTableCard .shipment-card-actions {
        border-top-color: #4a5568 !important;
    }

    body.dark-mode #shipmentsTableCard .shipment-number,
    html.dark-mode #shipmentsTableCard .shipment-number {
        color: #ffffff !important;
    }

    body.dark-mode #shipmentsTableCard .info-label,
    html.dark-mode #shipmentsTableCard .info-label {
        color: #a0aec0 !important;
    }

    body.dark-mode #shipmentsTableCard .info-value,
    html.dark-mode #shipmentsTableCard .info-value {
        color: #ffffff !important;
    }

    body.dark-mode #shipmentsTableCard .card-action-btn.secondary,
    html.dark-mode #shipmentsTableCard .card-action-btn.secondary {
        border-color: #4a5568 !important;
        color: #a0aec0 !important;
    }

    body.dark-mode #shipmentsTableCard .card-action-btn.secondary:hover,
    html.dark-mode #shipmentsTableCard .card-action-btn.secondary:hover {
        background: #374151 !important;
        border-color: #718096 !important;
        color: #ffffff !important;
    }

    /* تأثيرات hover للبطاقات */
    #shipmentsTableCard .shipment-card:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
    }

    body.dark-mode #shipmentsTableCard .shipment-card:hover,
    html.dark-mode #shipmentsTableCard .shipment-card:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4) !important;
    }

    /* === تحسينات إضافية للبطاقات المدمجة === */

    /* جعل البطاقات أكثر إحكاماً */
    #shipmentsTableCard .shipment-card * {
        margin: 0 !important;
    }

    /* تحسين عرض النصوص الطويلة - إزالة التكرار */

    /* إضافة tooltip للنصوص المقطوعة */
    #shipmentsTableCard .info-value[title] {
        cursor: help !important;
    }

    /* تحسين المسافة بين البطاقات */
    #shipmentsTableCard .card {
        margin-bottom: 4px !important;
        padding: 6px !important;
    }

    /* تحسين عرض التاريخ */
    #shipmentsTableCard .card-header h6 {
        font-size: 0.7rem !important;
        font-weight: 700 !important;
        margin: 0 !important;
        color: #2d3748 !important;
        padding: 4px 8px !important;
    }

    /* تقليص حجم body البطاقة */
    #shipmentsTableCard .card-body {
        padding: 4px 6px !important;
    }

    /* تحسين إضافي للعرض الضيق */
    @media (max-width: 480px) {
        .mobile-cards-container {
            width: 98vw !important;
            max-width: 98vw !important;
            padding: 1px !important;
        }

        .shipment-card {
            border-radius: 3px !important;
            padding: 3px !important;
        }

        #shipmentsTableCard .info-label {
            min-width: 25px !important;
            max-width: 25px !important;
            font-size: 0.5rem !important;
        }

        #shipmentsTableCard .info-value {
            font-size: 0.5rem !important;
            max-width: calc(100% - 30px) !important;
        }
    }

    /* تحسين عرض الشارات */
    #shipmentsTableCard .status-badges .badge {
        max-width: 80px !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }

    /* تحسين الأزرار للجوال */
    #shipmentsTableCard .card-action-btn {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        max-width: 120px !important;
    }

    /* تقليل الارتفاع الإجمالي للبطاقة */
    #shipmentsTableCard .shipment-card {
        max-height: 180px !important;
        overflow: hidden !important;
    }

    /* تحسين التخطيط للشاشات الصغيرة جداً */
    @media (max-width: 480px) {
        #shipmentsTableCard .shipment-card {
            padding: 8px !important;
            max-height: 160px !important;
        }

        #shipmentsTableCard .info-label,
        #shipmentsTableCard .info-value {
            font-size: 0.7rem !important;
        }

        #shipmentsTableCard .shipment-number {
            font-size: 0.8rem !important;
        }

        #shipmentsTableCard .badge {
            font-size: 0.6rem !important;
            padding: 1px 4px !important;
        }

        #shipmentsTableCard .card-action-btn {
            font-size: 0.7rem !important;
            padding: 4px 8px !important;
            min-height: 28px !important;
        }

    }
}

/* === إصلاح نهائي وقوي لعرض البطاقات === */
/* هذه القواعد لها أولوية قصوى */

@media (max-width: 768px) {
    /* فرض قوي على حاوي البطاقات */
    .mobile-cards-container,
    #shipmentsTableCard .mobile-cards-container,
    .cards-view .mobile-cards-container {
        width: 85vw !important;
        max-width: 85vw !important;
        margin: 0 auto !important;
        padding: 3px !important;
        box-sizing: border-box !important;
        display: flex !important;
        flex-direction: column !important;
        gap: 2px !important;
        overflow-x: hidden !important;
    }

    /* فرض قوي على البطاقات الفردية */
    .shipment-card,
    #shipmentsTableCard .shipment-card,
    .cards-view .shipment-card {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        padding: 3px !important;
        border-radius: 3px !important;
        box-sizing: border-box !important;
        overflow: hidden !important;
        background: #ffffff !important;
        border: 1px solid #e2e8f0 !important;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    }

    /* تقليص المحتوى الداخلي */
    .shipment-card-header,
    #shipmentsTableCard .shipment-card-header {
        margin-bottom: 1px !important;
        padding-bottom: 1px !important;
        font-size: 0.65rem !important;
        justify-content: flex-start !important;
        gap: 4px !important;
    }

    /* تحسين موضع الشارات */
    .status-badges,
    #shipmentsTableCard .status-badges {
        margin-left: auto !important;
        flex-shrink: 0 !important;
    }

    /* تحسين رقم الشحنة */
    .shipment-number,
    #shipmentsTableCard .shipment-number {
        flex-shrink: 0 !important;
        max-width: 55% !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
    }

    .shipment-card-body,
    #shipmentsTableCard .shipment-card-body {
        gap: 1px !important;
    }

    .info-row,
    #shipmentsTableCard .info-row {
        min-height: 10px !important;
        gap: 3px !important;
        padding: 0 !important;
    }

    .info-label,
    #shipmentsTableCard .info-label {
        min-width: 28px !important;
        max-width: 28px !important;
        font-size: 0.6rem !important;
        line-height: 1.1 !important;
    }

    .info-value,
    #shipmentsTableCard .info-value {
        font-size: 0.6rem !important;
        line-height: 1.1 !important;
        max-width: calc(100% - 32px) !important;
    }

    .shipment-number {
        font-size: 0.75rem !important;
    }
}

/* === إجبار تحديث فوري === */
/* إضافة timestamp لإجبار تحديث cache */
/* Updated: 2024-01-16-FINAL-COMPACT */

/* فرض إضافي للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .mobile-cards-container,
    #shipmentsTableCard .mobile-cards-container {
        width: 90vw !important;
        max-width: 90vw !important;
        margin: 0 auto !important;
        padding: 2px !important;
    }

    .shipment-card,
    #shipmentsTableCard .shipment-card {
        padding: 2px !important;
        border-radius: 2px !important;
    }

    .info-label,
    #shipmentsTableCard .info-label {
        min-width: 20px !important;
        max-width: 20px !important;
        font-size: 0.45rem !important;
    }

    .info-value,
    #shipmentsTableCard .info-value {
        font-size: 0.45rem !important;
        max-width: calc(100% - 25px) !important;
    }
}

/* === إصلاح نهائي للحاوي الرئيسي === */
/* أولوية قصوى لتقليص عرض الحاوي */

@media (max-width: 768px) {
    /* فرض قوي على الحاوي الرئيسي */
    #shipmentsTableCard,
    .content-card#shipmentsTableCard,
    div#shipmentsTableCard {
        width: 85vw !important;
        max-width: 85vw !important;
        margin: 0 auto !important;
        border-radius: 8px !important;
        border: 1px solid #e2e8f0 !important;
        box-sizing: border-box !important;
        overflow: hidden !important;
    }

    /* فرض على card-body */
    #shipmentsTableCard .card-body,
    .content-card#shipmentsTableCard .card-body {
        width: 100% !important;
        max-width: 100% !important;
        padding: 4px !important;
        margin: 0 !important;
        box-sizing: border-box !important;
        overflow: hidden !important;
    }

    /* فرض على cards-view */
    #shipmentsTableCard .cards-view,
    .content-card#shipmentsTableCard .cards-view {
        width: 100% !important;
        max-width: 100% !important;
        padding: 2px !important;
        margin: 0 !important;
        box-sizing: border-box !important;
        overflow: hidden !important;
    }
}

/* للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    #shipmentsTableCard,
    .content-card#shipmentsTableCard {
        width: 95vw !important;
        max-width: 95vw !important;
    }
}
