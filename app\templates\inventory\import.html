{% extends "base.html" %}

{% block title %}استيراد بيانات الأصناف{% endblock %}

{% block content %}
<div class="ns-page-header">
    <div class="ns-page-header-content">
        <h1 class="ns-page-title">
            <i class="fas fa-download"></i>
            استيراد بيانات الأصناف
        </h1>
        <p class="ns-page-subtitle">استيراد الأصناف من قاعدة البيانات الخارجية</p>
    </div>
</div>

<div class="ns-content">
    <div class="ns-section">
        <div class="ns-section-header">
            <h2 class="ns-section-title">معلومات الاستيراد</h2>
        </div>
        
        <div class="ns-section-content">
            <div class="ns-info-box">
                <div class="ns-info-item">
                    <strong>مصدر البيانات:</strong>
                    <span>قاعدة البيانات الخارجية (IAS20251.YEMENSOFT.COM)</span>
                </div>
                <div class="ns-info-item">
                    <strong>الجداول المصدر:</strong>
                    <span>IAS_ITM_MST و IAS_ITM_DTL</span>
                </div>
                <div class="ns-info-item">
                    <strong>حالة الاتصال:</strong>
                    <span id="connection-status" class="text-warning">جاري التحقق...</span>
                </div>
                <div class="ns-info-item">
                    <strong>الحقول المستوردة:</strong>
                    <ul>
                        <li>من IAS_ITM_MST: I_CODE, I_NAME, I_E_NAME, G_CODE</li>
                        <li>من IAS_ITM_DTL: ITM_UNT, P_SIZE (حيث SALE_UNIT=1)</li>
                    </ul>
                </div>
                <div class="ns-info-item">
                    <strong>الشروط:</strong>
                    <ul>
                        <li>I_CODE لا ينتهي بالنجمة "*"</li>
                        <li>SALE_UNIT = 1 في جدول IAS_ITM_DTL</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="ns-section">
        <div class="ns-section-header">
            <h2 class="ns-section-title">بدء الاستيراد</h2>
        </div>
        
        <div class="ns-section-content">
            <form method="POST" id="importForm">
                <div class="ns-form-group">
                    <div class="ns-warning-box">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تنبيه:</strong> عملية الاستيراد قد تستغرق بعض الوقت حسب كمية البيانات.
                        سيتم تحديث الأصناف الموجودة وإضافة الأصناف الجديدة.
                    </div>
                </div>
                
                <div class="ns-form-actions">
                    <button type="submit" class="ns-button ns-button-primary" id="importBtn">
                        <i class="fas fa-download"></i>
                        بدء استيراد الأصناف
                    </button>
                    <a href="{{ url_for('inventory.index') }}" class="ns-button ns-button-secondary">
                        <i class="fas fa-arrow-left"></i>
                        العودة لقائمة الأصناف
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.getElementById('importForm').addEventListener('submit', function(e) {
    const btn = document.getElementById('importBtn');
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاستيراد...';
    
    // إظهار رسالة تحميل
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'ns-loading-overlay';
    loadingDiv.innerHTML = `
        <div class="ns-loading-content">
            <i class="fas fa-spinner fa-spin fa-3x"></i>
            <p>جاري استيراد بيانات الأصناف...</p>
            <p>الرجاء الانتظار...</p>
        </div>
    `;
    document.body.appendChild(loadingDiv);
});
</script>

<style>
.ns-info-box {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.ns-info-item {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.ns-info-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.ns-info-item strong {
    color: #495057;
    display: inline-block;
    min-width: 120px;
}

.ns-info-item ul {
    margin: 5px 0 0 120px;
    padding: 0;
    list-style-type: disc;
}

.ns-info-item li {
    margin-bottom: 5px;
}

.ns-warning-box {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 15px;
    color: #856404;
}

.ns-warning-box i {
    margin-right: 8px;
    color: #f39c12;
}

.ns-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.ns-loading-content {
    background: white;
    padding: 40px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.ns-loading-content i {
    color: #007bff;
    margin-bottom: 20px;
}

.ns-loading-content p {
    margin: 10px 0;
    font-size: 16px;
    color: #333;
}
</style>
{% endblock %}
