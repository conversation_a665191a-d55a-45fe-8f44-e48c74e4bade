#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نماذج البيانات لنظام الأتمتة التلقائية
Automation System Data Models
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
import logging
from database_manager import DatabaseManager

logger = logging.getLogger(__name__)

class AutomationLog:
    """نموذج سجل الأتمتة"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def log_action(self, action: str, description: str, **kwargs) -> bool:
        """تسجيل إجراء أتمتة"""
        try:
            query = """
                INSERT INTO automation_log (
                    id, action, description, shipment_id, delivery_order_id, 
                    agent_id, status, error_message, execution_time
                ) VALUES (
                    automation_log_seq.NEXTVAL, :action, :description, 
                    :shipment_id, :delivery_order_id, :agent_id, 
                    :status, :error_message, :execution_time
                )
            """
            
            params = {
                'action': action,
                'description': description,
                'shipment_id': kwargs.get('shipment_id'),
                'delivery_order_id': kwargs.get('delivery_order_id'),
                'agent_id': kwargs.get('agent_id'),
                'status': kwargs.get('status', 'SUCCESS'),
                'error_message': kwargs.get('error_message'),
                'execution_time': kwargs.get('execution_time', 0)
            }
            
            self.db_manager.execute_update(query, params)
            return True
            
        except Exception as e:
            logger.error(f"Error logging automation action: {e}")
            return False
    
    def get_recent_activities(self, limit: int = 10) -> List[Dict]:
        """جلب الأنشطة الحديثة"""
        try:
            query = """
                SELECT 
                    action, description, action_date, status,
                    shipment_id, delivery_order_id, agent_id
                FROM automation_log 
                ORDER BY action_date DESC 
                FETCH FIRST :limit ROWS ONLY
            """
            
            results = self.db_manager.execute_query(query, {'limit': limit})
            
            activities = []
            for row in results:
                activities.append({
                    'action': row[0],
                    'description': row[1],
                    'action_date': row[2],
                    'status': row[3],
                    'shipment_id': row[4],
                    'delivery_order_id': row[5],
                    'agent_id': row[6]
                })
            
            return activities
            
        except Exception as e:
            logger.error(f"Error getting recent activities: {e}")
            return []

class AutomationSettings:
    """نموذج إعدادات الأتمتة"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def get_setting(self, key: str) -> Optional[str]:
        """جلب إعداد معين"""
        try:
            query = """
                SELECT setting_value 
                FROM automation_settings 
                WHERE setting_key = :key AND is_active = 1
            """
            
            result = self.db_manager.execute_query(query, {'key': key})
            return result[0][0] if result else None
            
        except Exception as e:
            logger.error(f"Error getting setting {key}: {e}")
            return None
    
    def update_setting(self, key: str, value: str) -> bool:
        """تحديث إعداد"""
        try:
            query = """
                UPDATE automation_settings 
                SET setting_value = :value, updated_at = SYSDATE 
                WHERE setting_key = :key
            """
            
            self.db_manager.execute_update(query, {'key': key, 'value': value})
            return True
            
        except Exception as e:
            logger.error(f"Error updating setting {key}: {e}")
            return False
    
    def get_all_settings(self) -> Dict[str, str]:
        """جلب جميع الإعدادات"""
        try:
            query = """
                SELECT setting_key, setting_value 
                FROM automation_settings 
                WHERE is_active = 1
            """
            
            results = self.db_manager.execute_query(query)
            return {row[0]: row[1] for row in results}
            
        except Exception as e:
            logger.error(f"Error getting all settings: {e}")
            return {}

class AutomationRules:
    """نموذج قواعد الأتمتة"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def get_active_rules(self) -> List[Dict]:
        """جلب القواعد النشطة"""
        try:
            query = """
                SELECT 
                    id, rule_name, rule_type, trigger_condition, 
                    action_type, rule_config, priority_level,
                    success_count, failure_count, last_executed
                FROM automation_rules 
                WHERE is_active = 1 
                ORDER BY priority_level DESC
            """
            
            results = self.db_manager.execute_query(query)
            
            rules = []
            for row in results:
                rules.append({
                    'id': row[0],
                    'rule_name': row[1],
                    'rule_type': row[2],
                    'trigger_condition': row[3],
                    'action_type': row[4],
                    'rule_config': json.loads(row[5]) if row[5] else {},
                    'priority_level': row[6],
                    'success_count': row[7] or 0,
                    'failure_count': row[8] or 0,
                    'last_executed': row[9]
                })
            
            return rules
            
        except Exception as e:
            logger.error(f"Error getting active rules: {e}")
            return []
    
    def update_rule_stats(self, rule_id: int, success: bool) -> bool:
        """تحديث إحصائيات القاعدة"""
        try:
            if success:
                query = """
                    UPDATE automation_rules 
                    SET success_count = success_count + 1, 
                        last_executed = SYSDATE 
                    WHERE id = :rule_id
                """
            else:
                query = """
                    UPDATE automation_rules 
                    SET failure_count = failure_count + 1, 
                        last_executed = SYSDATE 
                    WHERE id = :rule_id
                """
            
            self.db_manager.execute_update(query, {'rule_id': rule_id})
            return True
            
        except Exception as e:
            logger.error(f"Error updating rule stats: {e}")
            return False

class AutomationStatistics:
    """نموذج إحصائيات الأتمتة"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def get_daily_stats(self, date: datetime = None) -> Dict:
        """جلب إحصائيات يوم معين"""
        if not date:
            date = datetime.now()
        
        try:
            # إحصائيات الأوامر المؤتمتة
            orders_query = """
                SELECT 
                    COUNT(*) as total_orders,
                    COUNT(CASE WHEN order_status = 'completed' THEN 1 END) as completed_orders,
                    COUNT(CASE WHEN created_by = 1 THEN 1 END) as automated_orders
                FROM delivery_orders 
                WHERE TRUNC(created_date) = TRUNC(:date)
            """
            
            orders_result = self.db_manager.execute_query(orders_query, {'date': date})
            
            # إحصائيات الأتمتة
            automation_query = """
                SELECT 
                    COUNT(*) as total_actions,
                    COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) as successful_actions
                FROM automation_log 
                WHERE TRUNC(action_date) = TRUNC(:date)
            """
            
            automation_result = self.db_manager.execute_query(automation_query, {'date': date})
            
            # عدد القواعد النشطة
            rules_query = """
                SELECT COUNT(*) as active_rules 
                FROM automation_rules 
                WHERE is_active = 1
            """
            
            rules_result = self.db_manager.execute_query(rules_query)
            
            # تجميع النتائج
            orders_data = orders_result[0] if orders_result else (0, 0, 0)
            automation_data = automation_result[0] if automation_result else (0, 0)
            rules_data = rules_result[0] if rules_result else (0,)
            
            total_orders = orders_data[0] or 0
            completed_orders = orders_data[1] or 0
            automated_orders = orders_data[2] or 0
            
            total_actions = automation_data[0] or 0
            successful_actions = automation_data[1] or 0
            
            active_rules = rules_data[0] or 0
            
            # حساب معدل النجاح
            success_rate = (successful_actions / total_actions * 100) if total_actions > 0 else 0
            
            return {
                'total_automated_orders': automated_orders,
                'success_rate': round(success_rate, 1),
                'active_rules': active_rules,
                'processed_today': total_actions,
                'completed_orders': completed_orders,
                'total_orders': total_orders
            }
            
        except Exception as e:
            logger.error(f"Error getting daily stats: {e}")
            return {
                'total_automated_orders': 0,
                'success_rate': 0.0,
                'active_rules': 0,
                'processed_today': 0,
                'completed_orders': 0,
                'total_orders': 0
            }
    
    def get_overall_stats(self) -> Dict:
        """جلب الإحصائيات الإجمالية"""
        try:
            # إجمالي الأوامر المؤتمتة
            total_query = """
                SELECT 
                    COUNT(*) as total_automated,
                    COUNT(CASE WHEN order_status = 'completed' THEN 1 END) as completed_automated
                FROM delivery_orders 
                WHERE created_by = 1
            """
            
            total_result = self.db_manager.execute_query(total_query)
            
            # إجمالي إجراءات الأتمتة
            actions_query = """
                SELECT 
                    COUNT(*) as total_actions,
                    COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) as successful_actions
                FROM automation_log
            """
            
            actions_result = self.db_manager.execute_query(actions_query)
            
            total_data = total_result[0] if total_result else (0, 0)
            actions_data = actions_result[0] if actions_result else (0, 0)
            
            total_automated = total_data[0] or 0
            completed_automated = total_data[1] or 0
            total_actions = actions_data[0] or 0
            successful_actions = actions_data[1] or 0
            
            # حساب معدلات النجاح
            completion_rate = (completed_automated / total_automated * 100) if total_automated > 0 else 0
            action_success_rate = (successful_actions / total_actions * 100) if total_actions > 0 else 0
            
            return {
                'total_automated_orders': total_automated,
                'completed_automated_orders': completed_automated,
                'completion_rate': round(completion_rate, 1),
                'total_automation_actions': total_actions,
                'successful_actions': successful_actions,
                'action_success_rate': round(action_success_rate, 1)
            }
            
        except Exception as e:
            logger.error(f"Error getting overall stats: {e}")
            return {}
