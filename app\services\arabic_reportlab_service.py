"""
خدمة PDF عربية باستخدام ReportLab
تحاكي النموذج العربي المصحح بدون الحاجة لـ wkhtmltopdf
"""

from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import io
from datetime import datetime
from typing import Dict, Optional


class ArabicReportLabService:
    """خدمة PDF عربية باستخدام ReportLab"""
    
    def __init__(self):
        """تهيئة الخدمة"""
        self.setup_styles()
    
    def setup_styles(self):
        """إعداد الأنماط"""
        self.styles = getSampleStyleSheet()
        
        # نمط العنوان الرئيسي
        self.title_style = ParagraphStyle(
            'ArabicTitle',
            parent=self.styles['Heading1'],
            fontSize=20,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#1f4e79'),
            fontName='Helvetica-Bold'
        )
        
        # نمط العنوان الفرعي
        self.subtitle_style = ParagraphStyle(
            'ArabicSubtitle',
            parent=self.styles['Heading2'],
            fontSize=16,
            spaceAfter=15,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#2c5aa0'),
            fontName='Helvetica-Bold'
        )
        
        # نمط النص العادي
        self.normal_style = ParagraphStyle(
            'ArabicNormal',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=6,
            alignment=TA_RIGHT,
            fontName='Helvetica'
        )
        
        # نمط التسميات
        self.label_style = ParagraphStyle(
            'ArabicLabel',
            parent=self.styles['Normal'],
            fontSize=10,
            spaceAfter=3,
            alignment=TA_RIGHT,
            textColor=colors.HexColor('#495057'),
            fontName='Helvetica-Bold'
        )
    
    def create_delivery_order_pdf(self, order_data: Dict) -> bytes:
        """إنشاء PDF لأمر التسليم بالنموذج العربي"""
        
        # إنشاء buffer للـ PDF
        buffer = io.BytesIO()
        
        # إنشاء المستند
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=2*cm,
            bottomMargin=2*cm
        )
        
        # قائمة العناصر
        story = []
        
        # رأس المستند
        self._add_header(story, order_data)
        
        # معلومات الأمر الأساسية
        self._add_order_info(story, order_data)
        
        # معلومات الشحنة
        self._add_shipment_info(story, order_data)
        
        # بيانات المخلص
        self._add_agent_info(story, order_data)
        
        # تفاصيل التسليم
        self._add_delivery_info(story, order_data)
        
        # التذييل
        self._add_footer(story)
        
        # بناء المستند
        doc.build(story)
        
        # الحصول على البيانات
        pdf_data = buffer.getvalue()
        buffer.close()
        
        return pdf_data
    
    def _add_header(self, story, order_data):
        """إضافة رأس المستند"""
        # اسم الشركة
        company_name = Paragraph("شركة النقل والشحن المتطورة", self.title_style)
        story.append(company_name)
        
        # عنوان المستند
        doc_title = Paragraph("أمر تسليم للمخلص الجمركي", self.subtitle_style)
        story.append(doc_title)
        
        story.append(Spacer(1, 20))
        
        # خط فاصل
        line_data = [['', '', '', '']]
        line_table = Table(line_data, colWidths=[4*cm, 4*cm, 4*cm, 4*cm])
        line_table.setStyle(TableStyle([
            ('LINEBELOW', (0, 0), (-1, -1), 3, colors.HexColor('#1f4e79')),
        ]))
        story.append(line_table)
        story.append(Spacer(1, 15))
    
    def _add_order_info(self, story, order_data):
        """إضافة معلومات الأمر الأساسية"""
        # عنوان القسم
        section_title = Paragraph("📋 معلومات الأمر", self.subtitle_style)
        story.append(section_title)
        
        # جدول معلومات الأمر
        order_info = [
            ['رقم الأمر:', order_data.get('order_number', 'غير محدد'), 'تاريخ الإصدار:', datetime.now().strftime('%Y-%m-%d')],
            ['حالة الأمر:', self._get_status_arabic(order_data.get('order_status', 'draft')), 'الأولوية:', self._get_priority_arabic(order_data.get('priority', 'normal'))]
        ]
        
        order_table = Table(order_info, colWidths=[3*cm, 5*cm, 3*cm, 5*cm])
        order_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#f8f9fa')),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#dee2e6')),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (2, 0), (2, -1), 'Helvetica-Bold'),
        ]))
        
        story.append(order_table)
        story.append(Spacer(1, 15))
    
    def _add_shipment_info(self, story, order_data):
        """إضافة معلومات الشحنة"""
        section_title = Paragraph("📦 المعلومات الأساسية للشحنة", self.subtitle_style)
        story.append(section_title)
        
        shipment_info = [
            ['رقم التتبع:', order_data.get('tracking_number', 'غير محدد')],
            ['رقم الحجز:', order_data.get('booking_number', 'غير محدد')],
            ['نوع الشحنة:', order_data.get('shipment_type', 'غير محدد')],
            ['الوزن الإجمالي:', f"{order_data.get('total_weight', 'غير محدد')} كيلو" if order_data.get('total_weight') else 'غير محدد'],
            ['عدد الطرود:', str(order_data.get('packages_count', 'غير محدد'))],
            ['وصف البضاعة:', order_data.get('cargo_description', 'غير محدد')],
        ]
        
        shipment_table = Table(shipment_info, colWidths=[4*cm, 12*cm])
        shipment_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#e3f2fd')),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#90caf9')),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#1976d2')),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.white),
        ]))
        
        story.append(shipment_table)
        story.append(Spacer(1, 15))
    
    def _add_agent_info(self, story, order_data):
        """إضافة بيانات المخلص"""
        section_title = Paragraph("👤 بيانات المخلص الجمركي", self.subtitle_style)
        story.append(section_title)
        
        agent_info = [
            ['اسم المخلص:', order_data.get('agent_name', 'غير محدد')],
            ['اسم الشركة:', order_data.get('company_name', 'غير محدد')],
            ['رقم الترخيص:', order_data.get('license_number', 'غير محدد')],
            ['رقم الهاتف:', order_data.get('agent_phone', 'غير محدد')],
            ['البريد الإلكتروني:', order_data.get('agent_email', 'غير محدد')],
        ]
        
        agent_table = Table(agent_info, colWidths=[4*cm, 12*cm])
        agent_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#fff3e0')),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#ffb74d')),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f57c00')),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.white),
        ]))
        
        story.append(agent_table)
        story.append(Spacer(1, 15))
    
    def _add_delivery_info(self, story, order_data):
        """إضافة تفاصيل التسليم"""
        section_title = Paragraph("🚚 تفاصيل التسليم", self.subtitle_style)
        story.append(section_title)
        
        delivery_info = [
            ['موقع التسليم:', order_data.get('delivery_location', 'غير محدد')],
            ['التاريخ المطلوب للتخليص:', order_data.get('expected_completion_date', 'غير محدد')],
        ]
        
        delivery_table = Table(delivery_info, colWidths=[4*cm, 12*cm])
        delivery_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#e8f5e8')),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#81c784')),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#388e3c')),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.white),
        ]))
        
        story.append(delivery_table)
        story.append(Spacer(1, 20))
    
    def _add_footer(self, story):
        """إضافة التذييل"""
        # خط فاصل
        line_data = [['', '', '', '']]
        line_table = Table(line_data, colWidths=[4*cm, 4*cm, 4*cm, 4*cm])
        line_table.setStyle(TableStyle([
            ('LINEABOVE', (0, 0), (-1, -1), 2, colors.HexColor('#1f4e79')),
        ]))
        story.append(line_table)
        story.append(Spacer(1, 10))
        
        # معلومات الشركة
        footer_text = f"""
        <b>شركة النقل والشحن المتطورة</b><br/>
        العنوان: المملكة العربية السعودية - الرياض<br/>
        الهاتف: +966 11 123 4567 | البريد الإلكتروني: <EMAIL><br/>
        الموقع الإلكتروني: www.shipping.com<br/><br/>
        تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M')}
        """
        
        footer = Paragraph(footer_text, self.normal_style)
        story.append(footer)
    
    def _get_status_arabic(self, status):
        """ترجمة حالة الأمر للعربية"""
        status_map = {
            'draft': 'مسودة',
            'sent': 'مرسل',
            'in_progress': 'قيد التنفيذ',
            'completed': 'مكتمل',
            'cancelled': 'ملغي'
        }
        return status_map.get(status, status)
    
    def _get_priority_arabic(self, priority):
        """ترجمة الأولوية للعربية"""
        priority_map = {
            'low': 'منخفضة',
            'normal': 'عادية',
            'high': 'عالية',
            'urgent': 'عاجلة'
        }
        return priority_map.get(priority, priority)


# إنشاء instance عام
arabic_reportlab_service = ArabicReportLabService()


def generate_arabic_reportlab_pdf(order_data: Dict) -> bytes:
    """دالة مساعدة لإنشاء PDF عربي"""
    return arabic_reportlab_service.create_delivery_order_pdf(order_data)
