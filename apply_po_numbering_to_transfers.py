#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق آلية ترقيم أوامر الشراء على طلبات الحوالات
Apply PO Numbering Logic to Transfer Requests
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oracle_manager import OracleManager
import logging

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_smart_tr_numbering_function():
    """إنشاء دالة ترقيم ذكية لطلبات الحوالات مطابقة لأوامر الشراء"""
    
    oracle = OracleManager()
    
    try:
        oracle.connect()
        print("🔗 تم الاتصال بقاعدة البيانات بنجاح")
        
        print("\n1️⃣ إنشاء دالة GENERATE_SMART_TR_NUMBER...")
        
        # دالة ترقيم ذكية مطابقة لأوامر الشراء
        smart_tr_function = """
        CREATE OR REPLACE FUNCTION GENERATE_SMART_TR_NUMBER(p_year NUMBER DEFAULT NULL) RETURN VARCHAR2 IS
            v_year NUMBER;
            v_next_number NUMBER;
            v_year_prefix VARCHAR2(20);
            v_tr_number VARCHAR2(50);
        BEGIN
            -- استخدام السنة المحددة أو السنة الحالية
            v_year := NVL(p_year, EXTRACT(YEAR FROM SYSDATE));
            v_year_prefix := 'TR-' || v_year || '-';
            
            -- البحث عن أصغر رقم متاح في السنة المحددة
            SELECT MIN(missing_number) INTO v_next_number
            FROM (
                -- إنشاء سلسلة من 1 إلى أكبر رقم + 1
                SELECT LEVEL AS missing_number
                FROM DUAL
                CONNECT BY LEVEL <= (
                    SELECT NVL(MAX(TO_NUMBER(SUBSTR(REQUEST_NUMBER, -4))), 0) + 1
                    FROM TRANSFER_REQUESTS
                    WHERE REQUEST_NUMBER LIKE v_year_prefix || '%'
                )
                MINUS
                -- طرح الأرقام الموجودة فعلاً
                SELECT TO_NUMBER(SUBSTR(REQUEST_NUMBER, -4))
                FROM TRANSFER_REQUESTS
                WHERE REQUEST_NUMBER LIKE v_year_prefix || '%'
                AND REGEXP_LIKE(SUBSTR(REQUEST_NUMBER, -4), '^[0-9]+$')
            );
            
            -- إذا لم توجد فجوات، ابدأ من 1
            IF v_next_number IS NULL THEN
                v_next_number := 1;
            END IF;
            
            -- تكوين رقم REQUEST_NUMBER النهائي
            v_tr_number := v_year_prefix || LPAD(v_next_number, 4, '0');
            
            RETURN v_tr_number;
            
        EXCEPTION
            WHEN OTHERS THEN
                -- في حالة حدوث خطأ، استخدم الطريقة التقليدية
                SELECT NVL(MAX(TO_NUMBER(SUBSTR(REQUEST_NUMBER, -4))), 0) + 1 INTO v_next_number
                FROM TRANSFER_REQUESTS
                WHERE REQUEST_NUMBER LIKE v_year_prefix || '%';
                
                RETURN v_year_prefix || LPAD(v_next_number, 4, '0');
        END;
        """
        
        oracle.execute_update(smart_tr_function)
        oracle.commit()
        print("✅ تم إنشاء دالة GENERATE_SMART_TR_NUMBER")
        
        # 2. إنشاء دالة للبحث عن الفجوات (مطابقة لأوامر الشراء)
        print("\n2️⃣ إنشاء دالة GET_NEXT_AVAILABLE_TR_ID...")
        
        gap_function = """
        CREATE OR REPLACE FUNCTION GET_NEXT_AVAILABLE_TR_ID RETURN NUMBER IS
            v_next_id NUMBER;
            v_max_id NUMBER;
        BEGIN
            -- الحصول على أكبر رقم موجود
            SELECT NVL(MAX(id), 0) INTO v_max_id FROM TRANSFER_REQUESTS;
            
            -- إذا كان الجدول فارغ، ابدأ من 1
            IF v_max_id = 0 THEN
                RETURN 1;
            END IF;
            
            -- البحث عن أصغر فجوة في الترقيم
            SELECT MIN(missing_id) INTO v_next_id
            FROM (
                -- إنشاء سلسلة من 1 إلى أكبر رقم + 1
                SELECT LEVEL AS missing_id
                FROM DUAL
                CONNECT BY LEVEL <= v_max_id + 1
                MINUS
                -- طرح الأرقام الموجودة فعلاً
                SELECT id FROM TRANSFER_REQUESTS
            );
            
            -- إذا لم توجد فجوات، استخدم الرقم التالي
            IF v_next_id IS NULL THEN
                v_next_id := v_max_id + 1;
            END IF;
            
            RETURN v_next_id;
            
        EXCEPTION
            WHEN OTHERS THEN
                -- في حالة حدوث خطأ، استخدم الطريقة التقليدية
                SELECT NVL(MAX(id), 0) + 1 INTO v_next_id FROM TRANSFER_REQUESTS;
                RETURN v_next_id;
        END;
        """
        
        oracle.execute_update(gap_function)
        oracle.commit()
        print("✅ تم إنشاء دالة GET_NEXT_AVAILABLE_TR_ID")
        
        # 3. إنشاء دالة معاينة الرقم التالي
        print("\n3️⃣ إنشاء دالة PREVIEW_NEXT_TR_NUMBER...")
        
        preview_function = """
        CREATE OR REPLACE FUNCTION PREVIEW_NEXT_TR_NUMBER(p_year NUMBER DEFAULT NULL) RETURN VARCHAR2 IS
            v_year NUMBER;
            v_next_number NUMBER;
            v_year_prefix VARCHAR2(20);
            v_tr_number VARCHAR2(50);
        BEGIN
            v_year := NVL(p_year, EXTRACT(YEAR FROM SYSDATE));
            v_year_prefix := 'TR-' || v_year || '-';
            
            -- البحث عن أصغر رقم متاح (بدون تحديث)
            SELECT MIN(missing_number) INTO v_next_number
            FROM (
                SELECT LEVEL AS missing_number
                FROM DUAL
                CONNECT BY LEVEL <= (
                    SELECT NVL(MAX(TO_NUMBER(SUBSTR(REQUEST_NUMBER, -4))), 0) + 1
                    FROM TRANSFER_REQUESTS
                    WHERE REQUEST_NUMBER LIKE v_year_prefix || '%'
                )
                MINUS
                SELECT TO_NUMBER(SUBSTR(REQUEST_NUMBER, -4))
                FROM TRANSFER_REQUESTS
                WHERE REQUEST_NUMBER LIKE v_year_prefix || '%'
                AND REGEXP_LIKE(SUBSTR(REQUEST_NUMBER, -4), '^[0-9]+$')
            );
            
            IF v_next_number IS NULL THEN
                v_next_number := 1;
            END IF;
            
            v_tr_number := v_year_prefix || LPAD(v_next_number, 4, '0');
            
            RETURN v_tr_number;
            
        EXCEPTION
            WHEN OTHERS THEN
                RETURN v_year_prefix || '0001';
        END;
        """
        
        oracle.execute_update(preview_function)
        oracle.commit()
        print("✅ تم إنشاء دالة PREVIEW_NEXT_TR_NUMBER")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الدوال: {e}")
        return False
        
    finally:
        oracle.disconnect()

def test_smart_tr_numbering():
    """اختبار نظام الترقيم الذكي لطلبات الحوالات"""
    
    oracle = OracleManager()
    
    try:
        oracle.connect()
        print("\n4️⃣ اختبار نظام الترقيم الذكي...")
        
        # اختبار دالة المعاينة
        print("👁️ معاينة الرقم التالي:")
        preview_result = oracle.execute_query("SELECT PREVIEW_NEXT_TR_NUMBER() FROM DUAL")
        if preview_result:
            print(f"   الرقم التالي: {preview_result[0][0]}")
        
        # اختبار توليد أرقام جديدة
        print("\n🆕 توليد أرقام جديدة:")
        for i in range(5):
            result = oracle.execute_query("SELECT GENERATE_SMART_TR_NUMBER() FROM DUAL")
            if result:
                print(f"   رقم {i+1}: {result[0][0]}")
        
        # اختبار دالة الفجوات
        print("\n🔍 اختبار دالة البحث عن الفجوات:")
        gap_result = oracle.execute_query("SELECT GET_NEXT_AVAILABLE_TR_ID() FROM DUAL")
        if gap_result:
            print(f"   أصغر ID متاح: {gap_result[0][0]}")
        
        # عرض الأرقام الحالية
        print("\n📋 الأرقام الحالية في الجدول:")
        current_numbers = oracle.execute_query("""
            SELECT ID, REQUEST_NUMBER, 
                   TO_CHAR(CREATED_AT, 'YYYY-MM-DD HH24:MI:SS') as created
            FROM TRANSFER_REQUESTS 
            ORDER BY ID
        """)
        
        if current_numbers:
            for row in current_numbers:
                tr_id, request_number, created = row
                print(f"   ID: {tr_id}, رقم: {request_number}, تاريخ: {created}")
        else:
            print("   لا توجد طلبات حوالات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        return False
        
    finally:
        oracle.disconnect()

def update_transfer_requests_code():
    """تحديث كود طلبات الحوالات لاستخدام النظام الجديد"""
    
    print("\n5️⃣ تحديث كود طلبات الحوالات...")
    
    # تحديث ملف routes.py
    routes_file = "app/transfers/requests.py"
    
    try:
        # قراءة الملف الحالي
        with open(routes_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن الكود القديم واستبداله
        old_code = '''# إنشاء رقم طلب فريد
        from datetime import datetime
        current_time = datetime.now()
        user_id = int(current_user.id) if current_user.id else 1
        request_number = f"TR{current_time.strftime('%Y%m%d%H%M%S')}{user_id:03d}"'''
        
        new_code = '''# إنشاء رقم طلب فريد باستخدام النظام الذكي
        try:
            # استخدام الدالة الذكية الجديدة
            number_result = db.execute_query("SELECT GENERATE_SMART_TR_NUMBER() FROM DUAL")
            request_number = number_result[0][0] if number_result and number_result[0][0] else None
            
            if not request_number:
                # في حالة فشل النظام الذكي، استخدم الطريقة التقليدية
                from datetime import datetime
                current_year = datetime.now().year
                request_number = f"TR-{current_year}-0001"
        except Exception as e:
            logger.warning(f"فشل في استخدام النظام الذكي: {e}")
            # استخدام الطريقة التقليدية
            from datetime import datetime
            current_year = datetime.now().year
            request_number = f"TR-{current_year}-0001"'''
        
        if old_code in content:
            content = content.replace(old_code, new_code)
            
            # حفظ الملف المحدث
            with open(routes_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ تم تحديث كود طلبات الحوالات")
            return True
        else:
            print("⚠️ لم يتم العثور على الكود القديم للتحديث")
            print("💡 يرجى تحديث الكود يدوياً لاستخدام GENERATE_SMART_TR_NUMBER()")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تحديث الكود: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء تطبيق آلية ترقيم أوامر الشراء على طلبات الحوالات")
    print("=" * 80)
    
    # إنشاء الدوال الذكية
    functions_success = create_smart_tr_numbering_function()
    
    if functions_success:
        # اختبار النظام
        test_success = test_smart_tr_numbering()
        
        if test_success:
            # تحديث الكود
            code_success = update_transfer_requests_code()
            
            print("\n" + "=" * 80)
            print("🎉 تم تطبيق آلية ترقيم أوامر الشراء على طلبات الحوالات بنجاح!")
            print("\n✅ المزايا الجديدة:")
            print("   - نمط موحد: TR-YYYY-NNNN")
            print("   - إعادة استخدام الأرقام المحذوفة تلقائياً")
            print("   - عدم وجود فجوات في التسلسل")
            print("   - نفس آلية أوامر الشراء تماماً")
            
            print("\n📋 الدوال المتاحة:")
            print("   - GENERATE_SMART_TR_NUMBER(): توليد رقم جديد")
            print("   - PREVIEW_NEXT_TR_NUMBER(): معاينة الرقم التالي")
            print("   - GET_NEXT_AVAILABLE_TR_ID(): البحث عن فجوات في ID")
            
            if code_success:
                print("\n🔄 تم تحديث الكود لاستخدام النظام الجديد")
            else:
                print("\n⚠️ يرجى تحديث الكود يدوياً")
                
        else:
            print("\n" + "=" * 80)
            print("⚠️ تم إنشاء الدوال مع بعض التحذيرات في الاختبار")
    else:
        print("\n" + "=" * 80)
        print("❌ فشل في إنشاء الدوال الذكية")

if __name__ == "__main__":
    main()
