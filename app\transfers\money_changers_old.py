#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إدارة الصرافين والبنوك
Money Changers and Banks Management
"""

from flask import render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from . import transfers_bp
from database_manager import DatabaseManager
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

def format_date_safe(date_obj):
    """تنسيق آمن للتاريخ"""
    if not date_obj:
        return ''

    try:
        if hasattr(date_obj, 'strftime'):
            return date_obj.strftime('%Y-%m-%d %H:%M:%S')
        else:
            return str(date_obj)
    except Exception as e:
        logger.warning(f"خطأ في تنسيق التاريخ: {e}")
        return str(date_obj) if date_obj else ''

@transfers_bp.route('/money-changers')
@login_required
def money_changers():
    """صفحة إدارة الصرافين والبنوك"""
    try:
        db = DatabaseManager()
        
        # جلب جميع الصرافين والبنوك (استعلام بسيط)
        money_changers_query = """
        SELECT
            id,
            name,
            type,
            branch_id,
            contact_person,
            phone,
            email,
            commission_rate,
            is_active,
            created_at
        FROM money_changers_banks
        ORDER BY name
        """

        result = db.execute_query(money_changers_query)
        money_changers_list = []

        logger.info(f"تم جلب {len(result) if result else 0} سجل من قاعدة البيانات")

        if result:
            for i, row in enumerate(result):
                try:


                    money_changers_list.append({
                        'id': row[0],
                        'name': row[1] or '',
                        'type': row[2] or '',
                        'branch_id': row[3],
                        'contact_person': row[4] or '',
                        'phone': row[5] or '',
                        'email': row[6] or '',
                        'commission_rate': float(row[7]) if row[7] else 0.0,
                        'is_active': bool(row[8]) if row[8] is not None else True,
                        'created_at': format_date_safe(row[9]),
                        'transfer_count': 0,  # قيمة افتراضية
                        'total_amount': 0.0,  # قيمة افتراضية
                        'branch_name': 'الفرع الرئيسي'  # قيمة افتراضية
                    })
                except Exception as row_error:
                    logger.warning(f"خطأ في معالجة السجل {i}: {row_error}")
                    continue
        
        # جلب الفروع للاختيار (قائمة افتراضية)
        branches = [
            {'id': 1, 'name': 'الفرع الرئيسي'},
            {'id': 2, 'name': 'فرع صنعاء'},
            {'id': 3, 'name': 'فرع عدن'},
            {'id': 4, 'name': 'فرع تعز'}
        ]
        
        logger.info(f"عرض صفحة الصرافين والبنوك: {len(money_changers_list)} صراف/بنك، {len(branches)} فرع")

        return render_template('transfers/money_changers.html',
                             money_changers=money_changers_list,
                             branches=branches)
        
    except Exception as e:
        logger.error(f"خطأ في صفحة الصرافين والبنوك: {e}")
        flash('حدث خطأ في تحميل البيانات', 'error')

        # إرجاع قائمة فروع افتراضية حتى في حالة الخطأ
        default_branches = [
            {'id': 1, 'name': 'الفرع الرئيسي'},
            {'id': 2, 'name': 'فرع صنعاء'}
        ]

        return render_template('transfers/money_changers.html',
                             money_changers=[],
                             branches=default_branches)

@transfers_bp.route('/money-changers/add', methods=['POST'])
@login_required
def add_money_changer():
    """إضافة صراف/بنك جديد"""
    try:
        db = DatabaseManager()
        
        # استلام البيانات من النموذج
        data = request.get_json() if request.is_json else request.form
        
        name = data.get('name', '').strip()
        type_value = data.get('type', '').strip()
        branch_id = data.get('branch_id')
        contact_person = data.get('contact_person', '').strip()
        phone = data.get('phone', '').strip()
        email = data.get('email', '').strip()
        address = data.get('address', '').strip()
        account_details = data.get('account_details', '').strip()
        commission_rate = data.get('commission_rate', 0)
        
        # التحقق من البيانات المطلوبة
        if not name:
            return jsonify({'success': False, 'message': 'اسم الصراف/البنك مطلوب'})
        
        if not type_value or type_value not in ['bank', 'money_changer']:
            return jsonify({'success': False, 'message': 'نوع الصراف/البنك غير صحيح'})
        
        # التحقق من عدم تكرار الاسم
        existing_query = "SELECT id FROM money_changers_banks WHERE name = :1"
        existing = db.execute_query(existing_query, [name])
        
        if existing:
            return jsonify({'success': False, 'message': 'اسم الصراف/البنك موجود مسبقاً'})
        
        # إدراج البيانات
        insert_query = """
        INSERT INTO money_changers_banks 
        (name, type, branch_id, contact_person, phone, email, address, 
         account_details, commission_rate, is_active, created_at, updated_at)
        VALUES (:1, :2, :3, :4, :5, :6, :7, :8, :9, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        """
        
        db.execute_update(insert_query, [
            name, type_value, branch_id, contact_person, phone, 
            email, address, account_details, commission_rate
        ])
        
        db.commit()
        
        logger.info(f"تم إضافة صراف/بنك جديد: {name} بواسطة {current_user.username}")
        
        return jsonify({
            'success': True, 
            'message': f'تم إضافة {name} بنجاح'
        })
        
    except Exception as e:
        logger.error(f"خطأ في إضافة صراف/بنك: {e}")
        return jsonify({
            'success': False, 
            'message': 'حدث خطأ في إضافة الصراف/البنك'
        }), 500

@transfers_bp.route('/money-changers/<int:money_changer_id>/edit', methods=['POST'])
@login_required
def edit_money_changer(money_changer_id):
    """تعديل صراف/بنك"""
    try:
        db = DatabaseManager()
        
        # التحقق من وجود الصراف/البنك
        existing_query = "SELECT id FROM money_changers_banks WHERE id = :1"
        existing = db.execute_query(existing_query, [money_changer_id])
        
        if not existing:
            return jsonify({'success': False, 'message': 'الصراف/البنك غير موجود'})
        
        # استلام البيانات من النموذج
        data = request.get_json() if request.is_json else request.form
        
        name = data.get('name', '').strip()
        type_value = data.get('type', '').strip()
        branch_id = data.get('branch_id')
        contact_person = data.get('contact_person', '').strip()
        phone = data.get('phone', '').strip()
        email = data.get('email', '').strip()
        address = data.get('address', '').strip()
        account_details = data.get('account_details', '').strip()
        commission_rate = data.get('commission_rate', 0)
        is_active = data.get('is_active', True)
        
        # التحقق من البيانات المطلوبة
        if not name:
            return jsonify({'success': False, 'message': 'اسم الصراف/البنك مطلوب'})
        
        if not type_value or type_value not in ['bank', 'money_changer']:
            return jsonify({'success': False, 'message': 'نوع الصراف/البنك غير صحيح'})
        
        # التحقق من عدم تكرار الاسم (باستثناء السجل الحالي)
        duplicate_query = "SELECT id FROM money_changers_banks WHERE name = :1 AND id != :2"
        duplicate = db.execute_query(duplicate_query, [name, money_changer_id])
        
        if duplicate:
            return jsonify({'success': False, 'message': 'اسم الصراف/البنك موجود مسبقاً'})
        
        # تحديث البيانات
        update_query = """
        UPDATE money_changers_banks 
        SET name = :1, type = :2, branch_id = :3, contact_person = :4, 
            phone = :5, email = :6, address = :7, account_details = :8, 
            commission_rate = :9, is_active = :10, updated_at = CURRENT_TIMESTAMP
        WHERE id = :11
        """
        
        db.execute_update(update_query, [
            name, type_value, branch_id, contact_person, phone, 
            email, address, account_details, commission_rate, 
            1 if is_active else 0, money_changer_id
        ])
        
        db.commit()
        
        logger.info(f"تم تعديل صراف/بنك: {name} بواسطة {current_user.username}")
        
        return jsonify({
            'success': True, 
            'message': f'تم تعديل {name} بنجاح'
        })
        
    except Exception as e:
        logger.error(f"خطأ في تعديل صراف/بنك: {e}")
        return jsonify({
            'success': False, 
            'message': 'حدث خطأ في تعديل الصراف/البنك'
        }), 500

@transfers_bp.route('/money-changers/<int:money_changer_id>/toggle-status', methods=['POST'])
@login_required
def toggle_money_changer_status(money_changer_id):
    """تفعيل/إلغاء تفعيل صراف/بنك"""
    try:
        db = DatabaseManager()
        
        # جلب الحالة الحالية
        current_query = "SELECT name, is_active FROM money_changers_banks WHERE id = :1"
        current = db.execute_query(current_query, [money_changer_id])
        
        if not current:
            return jsonify({'success': False, 'message': 'الصراف/البنك غير موجود'})
        
        name, is_active = current[0]
        new_status = 0 if is_active else 1
        
        # تحديث الحالة
        update_query = """
        UPDATE money_changers_banks 
        SET is_active = :1, updated_at = CURRENT_TIMESTAMP 
        WHERE id = :2
        """
        
        db.execute_update(update_query, [new_status, money_changer_id])
        db.commit()
        
        status_text = 'تم تفعيل' if new_status else 'تم إلغاء تفعيل'
        logger.info(f"{status_text} صراف/بنك: {name} بواسطة {current_user.username}")
        
        return jsonify({
            'success': True, 
            'message': f'{status_text} {name} بنجاح',
            'new_status': bool(new_status)
        })
        
    except Exception as e:
        logger.error(f"خطأ في تغيير حالة صراف/بنك: {e}")
        return jsonify({
            'success': False, 
            'message': 'حدث خطأ في تغيير الحالة'
        }), 500

@transfers_bp.route('/money-changers/<int:money_changer_id>/details')
@login_required
def money_changer_details(money_changer_id):
    """تفاصيل صراف/بنك"""
    try:
        db = DatabaseManager()
        
        # جلب تفاصيل الصراف/البنك
        details_query = """
        SELECT 
            mcb.*,
            b.name as branch_name,
            COUNT(t.id) as total_transfers,
            COALESCE(SUM(t.amount), 0) as total_amount,
            COALESCE(SUM(t.commission), 0) as total_commission
        FROM money_changers_banks mcb
        LEFT JOIN branches b ON mcb.branch_id = b.id
        LEFT JOIN transfers t ON mcb.id = t.money_changer_bank_id
            AND t.status = 'completed'
        WHERE mcb.id = :1
        GROUP BY mcb.id, mcb.name, mcb.type, mcb.branch_id, 
                 mcb.contact_person, mcb.phone, mcb.email, 
                 mcb.address, mcb.account_details, mcb.commission_rate, 
                 mcb.is_active, mcb.created_at, mcb.updated_at, b.name
        """
        
        result = db.execute_query(details_query, [money_changer_id])
        
        if not result:
            return jsonify({'success': False, 'message': 'الصراف/البنك غير موجود'})
        
        row = result[0]
        details = {
            'id': row[0],
            'name': row[1],
            'type': row[2],
            'branch_id': row[3],
            'contact_person': row[4],
            'phone': row[5],
            'email': row[6],
            'address': row[7],
            'account_details': row[8],
            'commission_rate': float(row[9]) if row[9] else 0.0,
            'is_active': bool(row[10]),
            'created_at': format_date_safe(row[11]),
            'updated_at': format_date_safe(row[12]),
            'branch_name': row[13],
            'total_transfers': row[14],
            'total_amount': float(row[15]),
            'total_commission': float(row[16])
        }
        
        return jsonify({
            'success': True,
            'data': details
        })
        
    except Exception as e:
        logger.error(f"خطأ في جلب تفاصيل صراف/بنك: {e}")
        return jsonify({
            'success': False, 
            'message': 'حدث خطأ في جلب التفاصيل'
        }), 500
