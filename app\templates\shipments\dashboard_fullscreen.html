<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>{{ title }} - نظام إدارة الشحن المتطور</title>

    <!-- تطبيق فوري للوضع الداكن -->
    <script>
        // تطبيق الوضع الداكن فوراً قبل تحميل أي شيء
        (function() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.documentElement.classList.add('dark-mode');
                if (document.body) {
                    document.body.classList.add('dark-mode');
                }
            }
        })();
    </script>

    <!-- CSS فوري للوضع الداكن - أولوية قصوى -->
    <style>
        /* تطبيق فوري للوضع الداكن على الجدول */
        body.dark-mode,
        html.dark-mode body {
            background-color: #1a202c !important;
            color: #ffffff !important;
        }

        body.dark-mode .table,
        body.dark-mode table,
        html.dark-mode .table,
        html.dark-mode table {
            background-color: #2d3748 !important;
            color: #ffffff !important;
            border-color: #4a5568 !important;
        }

        body.dark-mode .table thead,
        body.dark-mode .table thead th,
        body.dark-mode table thead,
        body.dark-mode table thead th,
        html.dark-mode .table thead,
        html.dark-mode .table thead th,
        html.dark-mode table thead,
        html.dark-mode table thead th {
            background-color: #1a202c !important;
            color: #ffffff !important;
            border-color: #4a5568 !important;
        }

        body.dark-mode .table tbody,
        body.dark-mode .table tbody tr,
        body.dark-mode .table tbody td,
        body.dark-mode table tbody,
        body.dark-mode table tbody tr,
        body.dark-mode table tbody td,
        html.dark-mode .table tbody,
        html.dark-mode .table tbody tr,
        html.dark-mode .table tbody td,
        html.dark-mode table tbody,
        html.dark-mode table tbody tr,
        html.dark-mode table tbody td {
            background-color: #2d3748 !important;
            color: #ffffff !important;
            border-color: #4a5568 !important;
        }

        body.dark-mode .table tbody tr:nth-child(even),
        body.dark-mode table tbody tr:nth-child(even),
        html.dark-mode .table tbody tr:nth-child(even),
        html.dark-mode table tbody tr:nth-child(even) {
            background-color: #374151 !important;
        }

        body.dark-mode .table tbody tr:hover,
        body.dark-mode table tbody tr:hover,
        html.dark-mode .table tbody tr:hover,
        html.dark-mode table tbody tr:hover {
            background-color: #4a5568 !important;
        }

        body.dark-mode .card,
        body.dark-mode .content-card,
        html.dark-mode .card,
        html.dark-mode .content-card {
            background-color: #2d3748 !important;
            border-color: #4a5568 !important;
            color: #ffffff !important;
        }

        body.dark-mode .card-header,
        html.dark-mode .card-header {
            background-color: #1a202c !important;
            color: #ffffff !important;
            border-bottom: 1px solid #4a5568 !important;
        }

        body.dark-mode .table-responsive,
        html.dark-mode .table-responsive {
            background-color: #2d3748 !important;
        }

        /* أزرار رأس الجدول */
        body.dark-mode .card-header .btn:not(.btn-primary),
        html.dark-mode .card-header .btn:not(.btn-primary) {
            background-color: transparent !important;
            border-color: #718096 !important;
            color: #a0aec0 !important;
        }

        body.dark-mode .card-header .btn:not(.btn-primary):hover,
        html.dark-mode .card-header .btn:not(.btn-primary):hover {
            background-color: #4a5568 !important;
            color: #ffffff !important;
        }

        /* === تجاوب الجوال الجذري === */
        @media (max-width: 768px) {
            /* إعادة تعيين كامل للجوال */
            * {
                box-sizing: border-box !important;
            }

            html, body {
                width: 100vw !important;
                max-width: 100vw !important;
                overflow-x: hidden !important;
                margin: 0 !important;
                padding: 0 !important;
                font-size: 16px !important; /* خط أكبر للجوال */
            }

            /* حاوي رئيسي يأخذ العرض الكامل */
            .container-fluid {
                width: 100vw !important;
                max-width: 100vw !important;
                padding: 0 !important;
                margin: 0 !important;
            }

            /* البطاقات تأخذ العرض الكامل */
            .content-card,
            .card {
                width: 100vw !important;
                max-width: 100vw !important;
                margin: 0 !important;
                border-radius: 0 !important;
                border-left: none !important;
                border-right: none !important;
            }

            /* رأس البطاقة */
            .card-header {
                padding: 15px !important;
                font-size: 1.1rem !important;
                font-weight: bold !important;
            }

            /* جسم البطاقة */
            .card-body {
                padding: 0 !important;
                width: 100vw !important;
                max-width: 100vw !important;
            }

            /* تحسين الشريط العلوي */
            .navbar {
                padding: 0.25rem 0.5rem !important;
            }

            .navbar-brand {
                font-size: 1rem !important;
            }

            /* تحسين البطاقات */
            .content-card {
                margin-bottom: 10px !important;
                border-radius: 8px !important;
            }

            .card-header {
                padding: 8px 12px !important;
                font-size: 0.9rem !important;
            }

            .card-body {
                padding: 8px !important;
            }

            /* تحسين الأزرار */
            .btn {
                padding: 0.25rem 0.5rem !important;
                font-size: 0.8rem !important;
            }

            .btn-group .btn {
                padding: 0.2rem 0.4rem !important;
                font-size: 0.75rem !important;
            }

            /* الجدول يأخذ العرض الكامل */
            .table-responsive {
                width: 100vw !important;
                max-width: 100vw !important;
                overflow-x: auto !important;
                -webkit-overflow-scrolling: touch !important;
                margin: 0 !important;
                padding: 0 !important;
                border-radius: 0 !important;
            }

            .table {
                width: 100% !important;
                min-width: 100vw !important; /* عرض كامل للشاشة */
                margin: 0 !important;
                font-size: 1rem !important; /* خط أكبر للقراءة */
                border-collapse: collapse !important;
            }

            .table th,
            .table td {
                padding: 12px 8px !important; /* مساحة أكبر */
                font-size: 0.9rem !important;
                white-space: nowrap !important;
                border: 1px solid #dee2e6 !important;
                min-width: 100px !important; /* عرض أدنى للخلايا */
            }

            /* رؤوس الجدول أكبر وأوضح */
            .table th {
                font-size: 1rem !important;
                font-weight: bold !important;
                background-color: #f8f9fa !important;
                padding: 15px 10px !important;
            }

            /* إبقاء الأعمدة المهمة فقط */
            .table th:nth-child(n+8),
            .table td:nth-child(n+8) {
                display: none !important;
            }

            /* الإحصائيات تأخذ العرض الكامل */
            .row {
                width: 100vw !important;
                max-width: 100vw !important;
                margin: 0 !important;
                padding: 10px !important;
            }

            .col-xl-3,
            .col-md-6 {
                flex: 0 0 50% !important; /* عمودين في الصف */
                max-width: 50% !important;
                padding: 5px !important;
            }

            .stats-card {
                padding: 15px !important;
                margin: 0 !important;
                border-radius: 8px !important;
                width: 100% !important;
            }

            .stats-number {
                font-size: 1.8rem !important; /* أرقام أكبر */
                font-weight: bold !important;
            }

            .stats-label {
                font-size: 0.9rem !important; /* نص أكبر */
                margin-top: 5px !important;
            }

            .stats-icon {
                font-size: 2rem !important; /* أيقونات أكبر */
            }

            /* تحسين الفلاتر */
            .filters-section {
                padding: 8px !important;
            }

            .filter-group {
                margin-bottom: 8px !important;
            }

            .form-control {
                font-size: 0.8rem !important;
                padding: 0.25rem 0.5rem !important;
            }

            /* تحسين الشريط العائم */
            .floating-toolbar {
                bottom: 10px !important;
                right: 10px !important;
                padding: 8px !important;
            }

            .floating-toolbar .btn {
                padding: 0.3rem !important;
                margin: 0 2px !important;
            }

            /* تحسين النوافذ المنبثقة */
            .modal-dialog {
                margin: 10px !important;
                max-width: calc(100% - 20px) !important;
            }

            .modal-content {
                border-radius: 8px !important;
            }

            .modal-header {
                padding: 8px 12px !important;
            }

            .modal-body {
                padding: 12px !important;
            }

            /* إخفاء عناصر غير ضرورية */
            .d-none-mobile {
                display: none !important;
            }

            /* تحسين النصوص */
            h1, h2, h3, h4, h5, h6 {
                font-size: 1rem !important;
                margin-bottom: 8px !important;
            }

            /* تحسين التمرير الأفقي */
            .table-responsive {
                overflow-x: auto !important;
                -webkit-overflow-scrolling: touch !important;
            }

            /* تحسين الأيقونات */
            .fas, .far, .fab {
                font-size: 0.8rem !important;
            }
        }

        @media (max-width: 576px) {
            /* شاشات صغيرة جداً - عمود واحد */
            .col-xl-3,
            .col-md-6 {
                flex: 0 0 100% !important; /* عمود واحد */
                max-width: 100% !important;
                padding: 5px !important;
            }

            .stats-card {
                padding: 20px !important; /* مساحة أكبر */
                margin-bottom: 10px !important;
            }

            .stats-number {
                font-size: 2.2rem !important; /* أرقام أكبر */
            }

            .stats-label {
                font-size: 1rem !important; /* نص أكبر */
            }

            .stats-icon {
                font-size: 2.5rem !important; /* أيقونات أكبر */
            }

            /* الجدول يأخذ العرض الكامل */
            .table {
                min-width: 100vw !important;
                font-size: 1.1rem !important; /* خط أكبر */
            }

            .table th,
            .table td {
                padding: 15px 10px !important; /* مساحة أكبر */
                font-size: 1rem !important;
                min-width: 120px !important;
            }

            .table th {
                font-size: 1.1rem !important;
                padding: 18px 12px !important;
            }

            /* إخفاء أعمدة أقل أهمية */
            .table th:nth-child(n+6),
            .table td:nth-child(n+6) {
                display: none !important;
            }

            /* أزرار أكبر */
            .btn {
                padding: 0.5rem 1rem !important;
                font-size: 0.9rem !important;
                min-height: 44px !important; /* حجم مناسب للمس */
            }

            .card-header {
                padding: 20px !important;
                font-size: 1.2rem !important;
            }
        }
    </style>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome - عدة مصادر للتأكد -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- التصميم الاحترافي الجديد -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/professional-dashboard.css') }}">

    <!-- CSS مخصص للوحة ملء الشاشة -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard-fullscreen.css') }}">

    <!-- CSS الأصلي للشحنات -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/shipments.css') }}">
    
    <style>
        /* إصلاح عاجل لأزرار الإجراءات */
        .professional-dashboard .btn-group {
            display: flex !important;
            flex-wrap: nowrap !important;
            gap: 4px !important;
            align-items: center !important;
            justify-content: flex-start !important;
        }

        .professional-dashboard .btn-group .btn {
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            min-width: 34px !important;
            height: 34px !important;
            padding: 6px 10px !important;
            font-size: 0.85rem !important;
            border-radius: 8px !important;
            flex-shrink: 0 !important;
            white-space: nowrap !important;
            opacity: 1 !important;
            visibility: visible !important;
            position: relative !important;
            z-index: 1 !important;
        }

        .professional-dashboard .btn-group .btn i {
            font-size: 0.85rem !important;
            margin: 0 !important;
            font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome" !important;
            font-weight: 900 !important;
            display: inline-block !important;
        }

        /* إصلاح خاص للأيقونات */
        .professional-dashboard .btn-group .btn i:before {
            font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome" !important;
            font-weight: 900 !important;
        }

        /* نص احتياطي إذا لم تظهر الأيقونات */
        .professional-dashboard .btn-group .btn[title="عرض التفاصيل"] i:after {
            content: "👁" !important;
            font-family: inherit !important;
        }

        .professional-dashboard .btn-group .btn[title="تعديل الشحنة"] i:after {
            content: "✏" !important;
            font-family: inherit !important;
        }

        .professional-dashboard .btn-group .btn[title="إدارة الوثائق"] i:after {
            content: "📄" !important;
            font-family: inherit !important;
        }

        .professional-dashboard .btn-group .btn[title="تتبع الأصناف"] i:after {
            content: "📦" !important;
            font-family: inherit !important;
        }

        .professional-dashboard .btn-group .btn[title="عرض على الخريطة"] i:after {
            content: "🗺" !important;
            font-family: inherit !important;
        }

        .professional-dashboard .btn-group .btn[title="إرسال إشعار"] i:after {
            content: "🔔" !important;
            font-family: inherit !important;
        }

        .professional-dashboard .btn-group .btn[title="حذف الشحنة"] i:after {
            content: "🗑" !important;
            font-family: inherit !important;
        }

        /* إدارة الأيقونات الاحتياطية */
        .professional-dashboard .btn .icon-fallback {
            display: none;
            font-size: 0.9rem;
        }

        /* إظهار الأيقونة الاحتياطية إذا لم تعمل Font Awesome */
        .professional-dashboard .btn i:not([class*="fa-"]) + .icon-fallback,
        .professional-dashboard .btn i:empty + .icon-fallback {
            display: inline-block !important;
        }

        .professional-dashboard .btn i:not([class*="fa-"]),
        .professional-dashboard .btn i:empty {
            display: none !important;
        }

        /* حل بديل - إظهار الأيقونة الاحتياطية دائماً إذا لم تعمل الأيقونات */
        @supports not (font-family: "Font Awesome 6 Free") {
            .professional-dashboard .btn i {
                display: none !important;
            }
            .professional-dashboard .btn .icon-fallback {
                display: inline-block !important;
            }
        }

        .professional-dashboard table td:last-child {
            min-width: 320px !important;
            max-width: 320px !important;
            white-space: nowrap !important;
            padding: 15px !important;
            overflow: visible !important;
        }

        .professional-dashboard table th:last-child {
            min-width: 320px !important;
            max-width: 320px !important;
            white-space: nowrap !important;
            text-align: center !important;
        }

        /* نقل الأيقونات للجهة اليسرى مقابل الأرقام */
        .professional-dashboard .stats-card {
            position: relative !important;
            display: flex !important;
            align-items: center !important;
            padding: 20px !important;
        }

        .professional-dashboard .stats-content {
            flex: 1 !important;
            text-align: right !important;
        }

        .professional-dashboard .stats-icon {
            position: static !important;
            font-size: 3rem !important;
            color: #6c757d !important;
            opacity: 0.7 !important;
            margin-left: 20px !important;
            flex-shrink: 0 !important;
        }

        .professional-dashboard .stats-label {
            font-size: 0.85rem !important;
            font-weight: 600 !important;
            text-transform: uppercase !important;
            color: #6c757d !important;
            margin-bottom: 5px !important;
        }

        .professional-dashboard .stats-number {
            font-size: 2.2rem !important;
            font-weight: 700 !important;
            color: var(--primary-color) !important;
            margin: 5px 0 !important;
        }

        .professional-dashboard .stats-card .text-muted {
            font-size: 0.75rem !important;
            margin-top: 5px !important;
        }

        /* ألوان مميزة لكل بطاقة إحصائيات */
        .professional-dashboard .stats-card.border-left-primary {
            border-left: 4px solid #007bff !important;
            background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%) !important;
        }

        .professional-dashboard .stats-card.border-left-primary .stats-icon {
            color: #007bff !important;
        }

        .professional-dashboard .stats-card.border-left-warning {
            border-left: 4px solid #ffc107 !important;
            background: linear-gradient(135deg, #fffbf0 0%, #fff3cd 100%) !important;
        }

        .professional-dashboard .stats-card.border-left-warning .stats-icon {
            color: #ffc107 !important;
        }

        .professional-dashboard .stats-card.border-left-info {
            border-left: 4px solid #17a2b8 !important;
            background: linear-gradient(135deg, #f0fdff 0%, #d1ecf1 100%) !important;
        }

        .professional-dashboard .stats-card.border-left-info .stats-icon {
            color: #17a2b8 !important;
        }

        .professional-dashboard .stats-card.border-left-success {
            border-left: 4px solid #28a745 !important;
            background: linear-gradient(135deg, #f8fff9 0%, #d4edda 100%) !important;
        }

        .professional-dashboard .stats-card.border-left-success .stats-icon {
            color: #28a745 !important;
        }

        /* تحسين ألوان الأرقام حسب نوع البطاقة */
        .professional-dashboard .stats-card.border-left-primary .stats-number {
            color: #0056b3 !important;
        }

        .professional-dashboard .stats-card.border-left-warning .stats-number {
            color: #e0a800 !important;
        }

        .professional-dashboard .stats-card.border-left-info .stats-number {
            color: #138496 !important;
        }

        .professional-dashboard .stats-card.border-left-success .stats-number {
            color: #1e7e34 !important;
        }

        /* تأثيرات hover مميزة لكل بطاقة */
        .professional-dashboard .stats-card.border-left-primary:hover {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15) !important;
        }

        .professional-dashboard .stats-card.border-left-warning:hover {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
            box-shadow: 0 8px 25px rgba(255, 193, 7, 0.15) !important;
        }

        .professional-dashboard .stats-card.border-left-info:hover {
            background: linear-gradient(135deg, #d1ecf1 0%, #b3e5fc 100%) !important;
            box-shadow: 0 8px 25px rgba(23, 162, 184, 0.15) !important;
        }

        .professional-dashboard .stats-card.border-left-success:hover {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.15) !important;
        }

        /* تأثير نبضة للأرقام عند hover */
        .professional-dashboard .stats-card:hover .stats-number {
            animation: pulse 1.5s ease-in-out !important;
        }

        .professional-dashboard .stats-card:hover .stats-icon {
            transform: scale(1.1) !important;
            transition: transform 0.3s ease !important;
        }

        /* إصلاح أيقونات أزرار البحث */
        .professional-dashboard .input-group .btn {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            min-width: 45px !important;
            padding: 8px 12px !important;
        }

        .professional-dashboard .input-group .btn i {
            font-size: 0.9rem !important;
            margin: 0 !important;
        }

        .professional-dashboard .input-group .btn .icon-fallback {
            display: none !important;
            font-size: 0.9rem !important;
        }

        /* إظهار الأيقونة الاحتياطية إذا لم تعمل Font Awesome */
        .professional-dashboard .input-group .btn i:not([class*="fa-"]) + .icon-fallback,
        .professional-dashboard .input-group .btn i:empty + .icon-fallback {
            display: inline-block !important;
        }

        .professional-dashboard .input-group .btn i:not([class*="fa-"]),
        .professional-dashboard .input-group .btn i:empty {
            display: none !important;
        }

        /* === الوضع الداكن === */
        body.dark-mode {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%) !important;
            color: #ffffff !important;
        }

        .dark-mode .container-fluid {
            background: rgba(30, 30, 30, 0.95) !important;
            backdrop-filter: blur(10px) !important;
            color: #ffffff !important;
        }

        .dark-mode .page-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
        }

        .dark-mode .stats-card {
            background: #2d3748 !important;
            color: #ffffff !important;
            border: 1px solid #4a5568 !important;
        }

        .dark-mode .stats-card.border-left-primary {
            background: linear-gradient(135deg, #2a4a6b 0%, #1e3a5f 100%) !important;
        }

        .dark-mode .stats-card.border-left-warning {
            background: linear-gradient(135deg, #6b5b2a 0%, #5f4f1e 100%) !important;
        }

        .dark-mode .stats-card.border-left-info {
            background: linear-gradient(135deg, #2a5a6b 0%, #1e4f5f 100%) !important;
        }

        .dark-mode .stats-card.border-left-success {
            background: linear-gradient(135deg, #2a6b3a 0%, #1e5f2e 100%) !important;
        }

        .dark-mode .content-card,
        .dark-mode .filters-section {
            background: #2d3748 !important;
            color: #ffffff !important;
            border: 1px solid #4a5568 !important;
        }

        .dark-mode .content-card .card-header,
        .dark-mode .filters-header {
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%) !important;
            border-bottom: 2px solid #718096 !important;
            color: #ffffff !important;
        }

        /* أزرار رأس الجدول في الوضع الداكن */
        .dark-mode .content-card .card-header .btn-group .btn {
            background-color: transparent !important;
            border-color: #718096 !important;
            color: #ffffff !important;
        }

        .dark-mode .content-card .card-header .btn-outline-primary {
            border-color: #63b3ed !important;
            color: #63b3ed !important;
        }

        .dark-mode .content-card .card-header .btn-outline-primary:hover {
            background-color: #63b3ed !important;
            border-color: #63b3ed !important;
            color: #1a202c !important;
        }

        .dark-mode .content-card .card-header .btn-outline-success {
            border-color: #68d391 !important;
            color: #68d391 !important;
        }

        .dark-mode .content-card .card-header .btn-outline-success:hover {
            background-color: #68d391 !important;
            border-color: #68d391 !important;
            color: #1a202c !important;
        }

        .dark-mode .content-card .card-header .btn-primary {
            background-color: #3182ce !important;
            border-color: #3182ce !important;
            color: #ffffff !important;
        }

        .dark-mode .content-card .card-header .btn-primary:hover {
            background-color: #2c5aa0 !important;
            border-color: #2c5aa0 !important;
            color: #ffffff !important;
        }

        .dark-mode .table {
            color: #ffffff !important;
        }

        .dark-mode .table thead th {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
            color: #ffffff !important;
        }

        .dark-mode .table tbody tr:hover {
            background-color: rgba(74, 85, 104, 0.3) !important;
        }

        .dark-mode .table tbody td {
            background-color: #2d3748 !important;
            color: #ffffff !important;
            border-color: #4a5568 !important;
        }

        .dark-mode .table tbody tr {
            background-color: #2d3748 !important;
            color: #ffffff !important;
        }

        .dark-mode .table tbody tr:nth-child(even) {
            background-color: #374151 !important;
        }

        .dark-mode .table tbody tr:nth-child(odd) {
            background-color: #2d3748 !important;
        }

        .dark-mode .form-control,
        .dark-mode .form-select {
            background-color: #4a5568 !important;
            border-color: #718096 !important;
            color: #ffffff !important;
        }

        .dark-mode .form-control:focus,
        .dark-mode .form-select:focus {
            background-color: #4a5568 !important;
            border-color: #63b3ed !important;
            color: #ffffff !important;
            box-shadow: 0 0 0 0.2rem rgba(99, 179, 237, 0.25) !important;
        }

        .dark-mode .btn-outline-primary {
            border-color: #63b3ed !important;
            color: #63b3ed !important;
        }

        .dark-mode .btn-outline-primary:hover {
            background-color: #63b3ed !important;
            color: #1a202c !important;
        }

        /* أزرار الجدول في الوضع الداكن */
        .dark-mode .table .btn-outline-primary {
            border-color: #63b3ed !important;
            color: #63b3ed !important;
            background-color: transparent !important;
        }

        .dark-mode .table .btn-outline-warning {
            border-color: #fbb040 !important;
            color: #fbb040 !important;
            background-color: transparent !important;
        }

        .dark-mode .table .btn-outline-info {
            border-color: #4fd1c7 !important;
            color: #4fd1c7 !important;
            background-color: transparent !important;
        }

        .dark-mode .table .btn-outline-secondary {
            border-color: #a0aec0 !important;
            color: #a0aec0 !important;
            background-color: transparent !important;
        }

        .dark-mode .table .btn-outline-success {
            border-color: #68d391 !important;
            color: #68d391 !important;
            background-color: transparent !important;
        }

        .dark-mode .table .btn-outline-danger {
            border-color: #fc8181 !important;
            color: #fc8181 !important;
            background-color: transparent !important;
        }

        /* hover للأزرار في الجدول */
        .dark-mode .table .btn-outline-primary:hover {
            background-color: #63b3ed !important;
            color: #1a202c !important;
        }

        .dark-mode .table .btn-outline-warning:hover {
            background-color: #fbb040 !important;
            color: #1a202c !important;
        }

        .dark-mode .table .btn-outline-info:hover {
            background-color: #4fd1c7 !important;
            color: #1a202c !important;
        }

        .dark-mode .table .btn-outline-secondary:hover {
            background-color: #a0aec0 !important;
            color: #1a202c !important;
        }

        .dark-mode .table .btn-outline-success:hover {
            background-color: #68d391 !important;
            color: #1a202c !important;
        }

        .dark-mode .table .btn-outline-danger:hover {
            background-color: #fc8181 !important;
            color: #1a202c !important;
        }

        .dark-mode .floating-toolbar {
            background: #2d3748 !important;
            border: 1px solid #4a5568 !important;
            color: #ffffff !important;
        }

        /* شريط التنقل في الوضع الداكن */
        .dark-mode .shipments-navbar {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%) !important;
            border-bottom: 1px solid #4a5568 !important;
        }

        .dark-mode .shipments-navbar .nav-brand a {
            color: #ffffff !important;
        }

        .dark-mode .shipments-navbar .btn-nav {
            color: #a0aec0 !important;
        }

        .dark-mode .shipments-navbar .btn-nav:hover,
        .dark-mode .shipments-navbar .btn-nav.active {
            background-color: rgba(255, 255, 255, 0.1) !important;
            color: #ffffff !important;
        }

        .dark-mode .shipments-navbar .btn-tool {
            color: #a0aec0 !important;
        }

        .dark-mode .shipments-navbar .btn-tool:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
            color: #ffffff !important;
        }

        /* تحسينات إضافية لأزرار رأس الجدول */
        .dark-mode .card-header .btn {
            background-color: transparent !important;
            color: #ffffff !important;
        }

        .dark-mode .card-header .btn:not(.btn-primary):not(.btn-success):not(.btn-info):not(.btn-warning):not(.btn-danger) {
            border-color: #718096 !important;
            color: #a0aec0 !important;
        }

        .dark-mode .card-header .btn:not(.btn-primary):not(.btn-success):not(.btn-info):not(.btn-warning):not(.btn-danger):hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
            border-color: #a0aec0 !important;
            color: #ffffff !important;
        }

        /* أزرار محددة في رأس الجدول */
        .dark-mode .card-header .ms-auto .btn-group .btn,
        .dark-mode .card-header .d-flex .btn-group .btn {
            margin-left: 5px !important;
        }

        .dark-mode .card-header h6 {
            color: #ffffff !important;
        }

        .dark-mode .card-header h6 i {
            color: #a0aec0 !important;
        }

        /* زر تبديل الوضع في شريط التنقل */
        .shipments-navbar .btn-tool#themeToggle {
            position: relative !important;
            transition: all 0.3s ease !important;
        }

        .shipments-navbar .btn-tool#themeToggle:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
            transform: scale(1.1) !important;
        }

        .shipments-navbar .btn-tool#themeToggle i,
        .shipments-navbar .btn-tool#themeToggle .icon-fallback {
            font-size: 1.1rem !important;
            color: #ffffff !important;
        }

        .dark-mode .shipments-navbar .btn-tool#themeToggle:hover {
            background-color: rgba(255, 255, 255, 0.2) !important;
        }

        /* إخفاء/إظهار الأيقونات حسب الحاجة */
        .shipments-navbar .btn-tool#themeToggle .icon-fallback {
            display: none !important;
        }

        .shipments-navbar .btn-tool#themeToggle i:not([class*="fa-"]) + .icon-fallback,
        .shipments-navbar .btn-tool#themeToggle i:empty + .icon-fallback {
            display: inline-block !important;
        }

        .shipments-navbar .btn-tool#themeToggle i:not([class*="fa-"]),
        .shipments-navbar .btn-tool#themeToggle i:empty {
            display: none !important;
        }

        /* تحسينات إضافية للوضع الداكن */
        .dark-mode .badge {
            background-color: #4a5568 !important;
            color: #ffffff !important;
        }

        .dark-mode .badge.bg-success {
            background-color: #38a169 !important;
        }

        .dark-mode .badge.bg-warning {
            background-color: #d69e2e !important;
        }

        .dark-mode .badge.bg-info {
            background-color: #3182ce !important;
        }

        .dark-mode .badge.bg-danger {
            background-color: #e53e3e !important;
        }

        /* شارات الحالة في الجدول */
        .dark-mode .table .badge {
            color: #ffffff !important;
        }

        .dark-mode .table .badge.bg-success {
            background-color: #38a169 !important;
        }

        .dark-mode .table .badge.bg-warning {
            background-color: #d69e2e !important;
            color: #1a202c !important;
        }

        .dark-mode .table .badge.bg-info {
            background-color: #3182ce !important;
        }

        .dark-mode .table .badge.bg-danger {
            background-color: #e53e3e !important;
        }

        .dark-mode .table .badge.bg-secondary {
            background-color: #4a5568 !important;
        }

        .dark-mode .table .badge.bg-primary {
            background-color: #3182ce !important;
        }

        /* روابط في الجدول */
        .dark-mode .table a {
            color: #63b3ed !important;
        }

        .dark-mode .table a:hover {
            color: #90cdf4 !important;
        }

        /* نصوص صغيرة في الجدول */
        .dark-mode .table .small,
        .dark-mode .table small {
            color: #a0aec0 !important;
        }

        .dark-mode .text-muted {
            color: #a0aec0 !important;
        }

        .dark-mode .border {
            border-color: #4a5568 !important;
        }

        .dark-mode .dropdown-menu {
            background-color: #2d3748 !important;
            border-color: #4a5568 !important;
        }

        .dark-mode .dropdown-item {
            color: #ffffff !important;
        }

        .dark-mode .dropdown-item:hover {
            background-color: #4a5568 !important;
            color: #ffffff !important;
        }

        /* إصلاح شامل لجميع الأزرار في الوضع الداكن */
        .dark-mode .btn:not(.btn-primary):not(.btn-success):not(.btn-info):not(.btn-warning):not(.btn-danger) {
            background-color: transparent !important;
            border-color: #718096 !important;
            color: #a0aec0 !important;
        }

        .dark-mode .btn:not(.btn-primary):not(.btn-success):not(.btn-info):not(.btn-warning):not(.btn-danger):hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
            border-color: #a0aec0 !important;
            color: #ffffff !important;
        }

        /* حاوي الجدول في الوضع الداكن */
        .dark-mode .table-responsive {
            background-color: #2d3748 !important;
            border: 1px solid #4a5568 !important;
        }

        .dark-mode .content-card .card-body {
            background-color: #2d3748 !important;
            color: #ffffff !important;
        }

        /* تحسين pagination في الوضع الداكن */
        .dark-mode .pagination .page-link {
            background-color: #4a5568 !important;
            border-color: #718096 !important;
            color: #ffffff !important;
        }

        .dark-mode .pagination .page-link:hover {
            background-color: #718096 !important;
            border-color: #a0aec0 !important;
            color: #ffffff !important;
        }

        .dark-mode .pagination .page-item.active .page-link {
            background-color: #3182ce !important;
            border-color: #3182ce !important;
        }

        /* CSS مخصص لأزرار التحكم في رأس الجدول */
        .dark-mode .content-card .card-header .btn-group {
            gap: 5px !important;
        }

        .dark-mode .content-card .card-header .btn-sm {
            padding: 6px 12px !important;
            font-size: 0.875rem !important;
        }

        /* تحديد أزرار محددة */
        .dark-mode button[onclick="refreshDashboardData()"],
        .dark-mode button[onclick="exportShipmentsData()"] {
            background-color: transparent !important;
            border-color: #718096 !important;
            color: #a0aec0 !important;
        }

        .dark-mode button[onclick="refreshDashboardData()"]:hover,
        .dark-mode button[onclick="exportShipmentsData()"]:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
            border-color: #a0aec0 !important;
            color: #ffffff !important;
        }

        .dark-mode a[href*="new_cargo_shipment"] {
            background-color: #3182ce !important;
            border-color: #3182ce !important;
            color: #ffffff !important;
        }

        .dark-mode a[href*="new_cargo_shipment"]:hover {
            background-color: #2c5aa0 !important;
            border-color: #2c5aa0 !important;
            color: #ffffff !important;
        }

        /* إصلاح شامل وقوي للجدول في الوضع الداكن */
        body.dark-mode .table-responsive,
        body.dark-mode .table-responsive .table,
        body.dark-mode table,
        body.dark-mode .content-card .table-responsive,
        body.dark-mode .content-card table {
            background-color: #2d3748 !important;
            color: #ffffff !important;
        }

        body.dark-mode .table thead,
        body.dark-mode .table thead th,
        body.dark-mode table thead,
        body.dark-mode table thead th {
            background-color: #1a202c !important;
            color: #ffffff !important;
            border-color: #4a5568 !important;
        }

        body.dark-mode .table tbody,
        body.dark-mode .table tbody tr,
        body.dark-mode .table tbody td,
        body.dark-mode table tbody,
        body.dark-mode table tbody tr,
        body.dark-mode table tbody td {
            background-color: #2d3748 !important;
            color: #ffffff !important;
            border-color: #4a5568 !important;
        }

        body.dark-mode .table tbody tr:nth-child(even),
        body.dark-mode table tbody tr:nth-child(even) {
            background-color: #374151 !important;
        }

        body.dark-mode .table tbody tr:hover,
        body.dark-mode table tbody tr:hover {
            background-color: #4a5568 !important;
        }

        /* إصلاح شامل للأزرار في رأس الجدول */
        body.dark-mode .content-card .card-header,
        body.dark-mode .card-header {
            background: #1a202c !important;
            color: #ffffff !important;
            border-bottom: 1px solid #4a5568 !important;
        }

        body.dark-mode .content-card .card-header .btn,
        body.dark-mode .card-header .btn,
        body.dark-mode .content-card .card-header button,
        body.dark-mode .card-header button,
        body.dark-mode .content-card .card-header a.btn,
        body.dark-mode .card-header a.btn {
            background-color: transparent !important;
            border-color: #718096 !important;
            color: #a0aec0 !important;
        }

        body.dark-mode .content-card .card-header .btn:hover,
        body.dark-mode .card-header .btn:hover,
        body.dark-mode .content-card .card-header button:hover,
        body.dark-mode .card-header button:hover,
        body.dark-mode .content-card .card-header a.btn:hover,
        body.dark-mode .card-header a.btn:hover {
            background-color: #4a5568 !important;
            border-color: #a0aec0 !important;
            color: #ffffff !important;
        }

        body.dark-mode .content-card .card-header .btn-primary,
        body.dark-mode .card-header .btn-primary {
            background-color: #3182ce !important;
            border-color: #3182ce !important;
            color: #ffffff !important;
        }

        /* CSS محدد لرأس جدول الشحنات */
        body.dark-mode #shipmentsTableCard {
            background-color: #2d3748 !important;
            border-color: #4a5568 !important;
        }

        body.dark-mode #shipmentsTableHeader {
            background-color: #1a202c !important;
            color: #ffffff !important;
            border-bottom: 1px solid #4a5568 !important;
        }

        body.dark-mode #shipmentsTableBody {
            background-color: #2d3748 !important;
            color: #ffffff !important;
        }

        /* أزرار رأس جدول الشحنات */
        body.dark-mode #refreshBtn,
        body.dark-mode #exportBtn {
            background-color: transparent !important;
            border-color: #718096 !important;
            color: #a0aec0 !important;
        }

        body.dark-mode #refreshBtn:hover,
        body.dark-mode #exportBtn:hover {
            background-color: #4a5568 !important;
            border-color: #a0aec0 !important;
            color: #ffffff !important;
        }

        body.dark-mode #newShipmentBtn {
            background-color: #3182ce !important;
            border-color: #3182ce !important;
            color: #ffffff !important;
        }

        body.dark-mode #newShipmentBtn:hover {
            background-color: #2c5aa0 !important;
            border-color: #2c5aa0 !important;
        }

        /* تأثير انتقال سلس للوضع الداكن */
        body, .container-fluid, .card, .table, .form-control, .btn, .badge, .pagination {
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
        }

        /* تحسينات إضافية للوضع الجديد */
        .dashboard-fullscreen .container-fluid {
            padding: 20px;
            max-width: none;
        }
        
        .dashboard-fullscreen .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        
        .dashboard-fullscreen .table-responsive {
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        /* تنسيق خلية الحالة القابلة للنقر */
        .status-badge.clickable {
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .status-badge.clickable:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            opacity: 0.9;
        }

        .status-badge.clickable:active {
            transform: scale(0.98);
        }
        
        /* إخفاء عناصر التنقل الأصلية فقط */
        .dashboard-fullscreen .breadcrumb {
            display: none;
        }

        /* إظهار رأس الصفحة الجديد */
        .dashboard-fullscreen .page-header {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* تأكيد ظهور زر الوضع الداكن */
        .theme-toggle {
            display: block !important;
            visibility: visible !important;
        }

        .theme-toggle .btn {
            display: flex !important;
            align-items: center !important;
            visibility: visible !important;
            opacity: 1 !important;
            z-index: 1000 !important;
        }

        /* تحسينات قسم التصفية القابل للطي */
        #toggleFiltersBtn {
            transition: all 0.3s ease;
            border-radius: 8px;
        }

        #toggleFiltersBtn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        #toggleFiltersIcon {
            transition: transform 0.3s ease;
        }

        #activeFiltersCount {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* تحسين انتقال الطي */
        #filtersCollapse {
            transition: all 0.4s ease;
        }

        /* تحسين مظهر البطاقة عند الطي */
        .card.filters-collapsed {
            margin-bottom: 10px;
        }

        .card.filters-collapsed .card-header {
            border-bottom: none;
            border-radius: 15px;
        }

        /* CSS للبطاقات */
        .shipment-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .shipment-card-header {
            padding: 12px 15px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .shipment-number {
            font-weight: bold;
            font-size: 1.1em;
        }

        .shipment-card-body {
            padding: 15px;
        }

        .info-row {
            display: flex;
            margin-bottom: 8px;
        }

        .info-label {
            font-weight: 600;
            min-width: 80px;
            color: #6c757d;
        }

        .info-value {
            flex: 1;
            margin-right: 10px;
        }

        .shipment-card-actions {
            padding: 12px 15px;
            border-top: 1px solid #dee2e6;
            display: flex;
            gap: 10px;
        }

        .card-action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            font-size: 0.9em;
            cursor: pointer;
            transition: all 0.2s;
        }

        .card-action-btn.primary {
            background-color: #007bff;
            color: white;
        }

        .card-action-btn.primary:hover {
            background-color: #0056b3;
            color: white;
        }

        .card-action-btn.secondary {
            background-color: #6c757d;
            color: white;
        }

        .card-action-btn.secondary:hover {
            background-color: #5a6268;
            color: white;
        }

        .card-action-btn.disabled {
            background-color: #e9ecef !important;
            color: #6c757d !important;
            cursor: not-allowed !important;
            opacity: 0.6;
        }

        .card-action-btn.disabled:hover {
            background-color: #e9ecef !important;
            color: #6c757d !important;
        }

        /* الوضع الداكن للبطاقات */
        body.dark-mode .shipment-card {
            background: #2d3748;
            border-color: #4a5568;
            color: #ffffff;
        }

        body.dark-mode .shipment-card-header {
            border-bottom-color: #4a5568;
        }

        body.dark-mode .info-label {
            color: #a0aec0;
        }

        body.dark-mode .shipment-card-actions {
            border-top-color: #4a5568;
        }
    </style>
</head>

<body class="dashboard-fullscreen professional-dashboard">
    <!-- شريط التنقل مخفي -->
    <!-- {% include 'shipments/components/navbar.html' %} -->
    
    <!-- المحتوى الرئيسي -->
    <main class="dashboard-main">
        <div class="container-fluid fade-in">
            <!-- رأس الصفحة الاحترافي -->
            <div class="page-header animate-fade-in">
                <h1>
                    <i class="fas fa-ship"></i>
                    لوحة إدارة الشحنات
                </h1>
                <div class="subtitle">
                    نظام متطور لإدارة ومتابعة جميع عمليات الشحن والنقل
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card border-left-primary animate-fade-in">
                        <div class="stats-content">
                            <div class="stats-label">إجمالي الشحنات</div>
                            <div class="stats-number">{{ stats.total }}</div>
                            <div class="text-muted small">
                                <i class="fas fa-chart-line me-1"></i>
                                جميع الشحنات المسجلة
                            </div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card border-left-warning animate-fade-in">
                        <div class="stats-content">
                            <div class="stats-label">شحنات معلقة</div>
                            <div class="stats-number">{{ stats.pending }}</div>
                            <div class="text-muted small">
                                <i class="fas fa-hourglass-half me-1"></i>
                                في انتظار المعالجة
                            </div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card border-left-info animate-fade-in">
                        <div class="stats-content">
                            <div class="stats-label">في الطريق</div>
                            <div class="stats-number">{{ stats.in_transit }}</div>
                            <div class="text-muted small">
                                <i class="fas fa-route me-1"></i>
                                قيد النقل والتوصيل
                            </div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-shipping-fast"></i>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card border-left-success animate-fade-in">
                        <div class="stats-content">
                            <div class="stats-label">شحنات مسلمة</div>
                            <div class="stats-number">{{ stats.delivered }}</div>
                            <div class="text-muted small">
                                <i class="fas fa-thumbs-up me-1"></i>
                                تم التسليم بنجاح
                            </div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أدوات البحث والتصفية - قابل للطي -->
            <div class="filters-section animate-fade-in">
                <div class="filters-header" data-bs-toggle="collapse" data-bs-target="#filtersCollapse" aria-expanded="false">
                    <div class="d-flex align-items-center justify-content-between w-100">
                        <!-- زر الخيارات في الجهة اليسرى -->
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-cog me-1"></i>
                                خيارات
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="resetFilters()">
                                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="saveCurrentFilters()">
                                    <i class="fas fa-save me-2"></i>حفظ المرشحات
                                </a></li>
                            </ul>
                        </div>

                        <!-- العنوان في الوسط -->
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-search me-2"></i>البحث والتصفية المتقدمة
                            <span class="badge bg-secondary ms-2" id="activeFiltersCount" style="display: none;">0</span>
                        </h6>

                        <!-- مؤشر التوسيع في الجهة اليمنى -->
                        <div class="d-flex align-items-center">
                            <span class="text-muted small me-2">انقر للتوسيع</span>
                            <i class="fas fa-chevron-down" id="toggleFiltersIcon"></i>
                        </div>
                    </div>
                </div>
                <div class="collapse" id="filtersCollapse">
                    <div class="filters-body">
                        <!-- نفس محتوى البحث والتصفية من الصفحة الأصلية -->
                        {% include 'shipments/components/search_filters.html' %}
                    </div>
                </div>
            </div>

            <!-- جدول الشحنات -->
            <div class="content-card animate-fade-in" id="shipmentsTableCard">
                <div class="card-header" id="shipmentsTableHeader">
                    <h6 class="m-0">
                        <i class="fas fa-list me-2"></i>قائمة الشحنات المتقدمة
                    </h6>
                    <div class="ms-auto">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm" id="refreshBtn" onclick="refreshDashboardData()">
                                <i class="fas fa-sync-alt me-1"></i>تحديث
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" id="exportBtn" onclick="exportShipmentsData()">
                                <i class="fas fa-download me-1"></i>تصدير
                            </button>
                            <a href="{{ url_for('shipments.new_cargo_shipment') }}" class="btn btn-primary btn-sm" id="newShipmentBtn">
                                <i class="fas fa-plus me-1"></i>شحنة جديدة
                        </a>
                    </div>
                </div>
                <div class="card-body" id="shipmentsTableBody">
                    <!-- CSS فوري وقوي جداً للجدول والأزرار -->
                    <style>
                        /* فرض قوي جداً على جميع العناصر */
                        body.dark-mode *,
                        html.dark-mode *,
                        .dark-mode * {
                            box-sizing: border-box !important;
                        }

                        /* فرض على البطاقة الرئيسية */
                        body.dark-mode #shipmentsTableCard,
                        html.dark-mode #shipmentsTableCard,
                        body.dark-mode .content-card,
                        html.dark-mode .content-card {
                            background-color: #2d3748 !important;
                            color: #ffffff !important;
                            border-color: #4a5568 !important;
                        }

                        /* فرض على رأس البطاقة */
                        body.dark-mode #shipmentsTableHeader,
                        html.dark-mode #shipmentsTableHeader,
                        body.dark-mode .card-header,
                        html.dark-mode .card-header {
                            background-color: #1a202c !important;
                            color: #ffffff !important;
                            border-bottom: 1px solid #4a5568 !important;
                        }

                        /* فرض قوي جداً على الأزرار المحددة */
                        body.dark-mode #refreshBtn,
                        html.dark-mode #refreshBtn,
                        body.dark-mode button[onclick*="refreshDashboardData"],
                        html.dark-mode button[onclick*="refreshDashboardData"] {
                            background-color: transparent !important;
                            border: 1px solid #718096 !important;
                            color: #a0aec0 !important;
                        }

                        body.dark-mode #exportBtn,
                        html.dark-mode #exportBtn,
                        body.dark-mode button[onclick*="exportShipmentsData"],
                        html.dark-mode button[onclick*="exportShipmentsData"] {
                            background-color: transparent !important;
                            border: 1px solid #718096 !important;
                            color: #a0aec0 !important;
                        }

                        body.dark-mode #newShipmentBtn,
                        html.dark-mode #newShipmentBtn,
                        body.dark-mode a[href*="new_cargo_shipment"],
                        html.dark-mode a[href*="new_cargo_shipment"] {
                            background-color: #3182ce !important;
                            border: 1px solid #3182ce !important;
                            color: #ffffff !important;
                        }

                        /* فرض على جسم البطاقة */
                        body.dark-mode #shipmentsTableBody,
                        html.dark-mode #shipmentsTableBody,
                        body.dark-mode .card-body,
                        html.dark-mode .card-body {
                            background-color: #2d3748 !important;
                            color: #ffffff !important;
                        }

                        /* فرض على جميع عناصر الجدول */
                        body.dark-mode #shipmentsTableBody *,
                        html.dark-mode #shipmentsTableBody *,
                        body.dark-mode .shipments-table-component,
                        html.dark-mode .shipments-table-component,
                        body.dark-mode .shipments-table-component *,
                        html.dark-mode .shipments-table-component * {
                            background-color: #2d3748 !important;
                            color: #ffffff !important;
                            border-color: #4a5568 !important;
                        }

                        #shipmentsTableBody.dark-mode thead,
                        #shipmentsTableBody.dark-mode thead th,
                        body.dark-mode #shipmentsTableBody thead,
                        body.dark-mode #shipmentsTableBody thead th,
                        html.dark-mode #shipmentsTableBody thead,
                        html.dark-mode #shipmentsTableBody thead th {
                            background-color: #1a202c !important;
                            color: #ffffff !important;
                            border-color: #4a5568 !important;
                        }

                        #shipmentsTableBody.dark-mode tbody,
                        #shipmentsTableBody.dark-mode tbody tr,
                        #shipmentsTableBody.dark-mode tbody td,
                        body.dark-mode #shipmentsTableBody tbody,
                        body.dark-mode #shipmentsTableBody tbody tr,
                        body.dark-mode #shipmentsTableBody tbody td,
                        html.dark-mode #shipmentsTableBody tbody,
                        html.dark-mode #shipmentsTableBody tbody tr,
                        html.dark-mode #shipmentsTableBody tbody td {
                            background-color: #2d3748 !important;
                            color: #ffffff !important;
                            border-color: #4a5568 !important;
                        }

                        #shipmentsTableBody.dark-mode tbody tr:nth-child(even),
                        body.dark-mode #shipmentsTableBody tbody tr:nth-child(even),
                        html.dark-mode #shipmentsTableBody tbody tr:nth-child(even) {
                            background-color: #374151 !important;
                        }

                        #shipmentsTableBody.dark-mode tbody tr:nth-child(even) td,
                        body.dark-mode #shipmentsTableBody tbody tr:nth-child(even) td,
                        html.dark-mode #shipmentsTableBody tbody tr:nth-child(even) td {
                            background-color: #374151 !important;
                        }

                        #shipmentsTableBody.dark-mode tbody tr:hover,
                        #shipmentsTableBody.dark-mode tbody tr:hover td,
                        body.dark-mode #shipmentsTableBody tbody tr:hover,
                        body.dark-mode #shipmentsTableBody tbody tr:hover td,
                        html.dark-mode #shipmentsTableBody tbody tr:hover,
                        html.dark-mode #shipmentsTableBody tbody tr:hover td {
                            background-color: #4a5568 !important;
                        }

                        #shipmentsTableBody.dark-mode .table-responsive,
                        body.dark-mode #shipmentsTableBody .table-responsive,
                        html.dark-mode #shipmentsTableBody .table-responsive {
                            background-color: #2d3748 !important;
                        }
                    </style>

                    <!-- عرض الجدول العادي -->
                    <div id="tableView" class="table-view">
                        {% include 'shipments/components/shipments_table.html' %}
                    </div>

                    <!-- عرض البطاقات للجوال -->
                    <div id="cardsView" class="cards-view" style="display: none;">
                        <div class="mobile-cards-container">
                            <!-- سيتم ملء البطاقات بـ JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- شريط الأدوات العائم -->
    {% include 'shipments/components/floating-toolbar.html' %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- JavaScript للوحة ملء الشاشة -->
    <script src="{{ url_for('static', filename='js/dashboard-fullscreen.js') }}"></script>
    
    <!-- JavaScript الأصلي للشحنات -->
    <script src="{{ url_for('static', filename='js/shipments.js') }}"></script>

    <script>
        // وظائف الأزرار المفقودة
        function viewShipmentDetails(shipmentId) {
            console.log(`👁️ عرض تفاصيل الشحنة: ${shipmentId}`);
            // فتح صفحة تعديل الشحنة (التي تحتوي على جميع التفاصيل)
            window.open(`/shipments/cargo/edit/${shipmentId}`, '_blank');
        }

        function editShipment(shipmentId) {
            console.log(`✏️ تعديل الشحنة: ${shipmentId}`);
            // الانتقال لصفحة التعديل
            window.location.href = `/shipments/cargo/edit/${shipmentId}`;
        }

        function trackShipment(trackingNumber) {
            console.log(`📍 تتبع الشحنة: ${trackingNumber}`);
            // فتح نافذة التتبع
            window.open(`/shipments/track/${trackingNumber}`, '_blank');
        }

        function refreshShipments() {
            console.log('🔄 تحديث قائمة الشحنات...');
            window.location.reload();
        }

        /**
         * تحديث حالة الشحنة في الجدول فورياً
         */
        function updateShipmentStatusInTable(shipmentId, newStatus) {
            try {
                // البحث عن صف الشحنة في الجدول
                const table = document.querySelector('#shipmentsTable tbody');
                if (!table) return;

                const rows = table.querySelectorAll('tr');

                for (let row of rows) {
                    // البحث عن الصف الذي يحتوي على معرف الشحنة
                    const cells = row.querySelectorAll('td');
                    if (cells.length > 0) {
                        // البحث في جميع الخلايا عن معرف الشحنة
                        let shipmentFound = false;
                        for (let cell of cells) {
                            if (cell.textContent.includes(shipmentId)) {
                                shipmentFound = true;
                                break;
                            }
                        }

                        if (shipmentFound) {
                            // العثور على خلية الحالة (عادة العمود الخامس)
                            const statusCell = cells[4]; // العمود الخامس (0-indexed)

                            if (statusCell) {
                                // تحديث نص الحالة
                                const statusMap = {
                                    'customs_clearance': 'قيد التخليص الجمركي',
                                    'delivered': 'تم التسليم',
                                    'in_transit': 'في الطريق',
                                    'arrived_port': 'وصل الميناء',
                                    'pending': 'في الانتظار'
                                };

                                const statusText = statusMap[newStatus] || newStatus;

                                // تحديث النص مع تأثير بصري
                                statusCell.innerHTML = `<span class="badge bg-warning">${statusText}</span>`;

                                // إضافة تأثير بصري للتحديث
                                statusCell.style.backgroundColor = '#fff3cd';
                                statusCell.style.transition = 'background-color 3s ease';

                                setTimeout(() => {
                                    statusCell.style.backgroundColor = '';
                                }, 3000);

                                console.log(`✅ تم تحديث حالة الشحنة ${shipmentId} إلى ${statusText} في الجدول`);
                                break;
                            }
                        }
                    }
                }

                // تحديث البيانات المحفوظة إذا كانت الشحنة الحالية
                if (currentShipmentData && currentShipmentData.id == shipmentId) {
                    currentShipmentData.shipment_status = newStatus;
                }

            } catch (error) {
                console.error('خطأ في تحديث الجدول:', error);
            }
        }

        // وظائف التحكم في قسم التصفية القابل للطي
        document.addEventListener('DOMContentLoaded', function() {
            const filtersCollapse = document.getElementById('filtersCollapse');
            const toggleBtn = document.getElementById('toggleFiltersBtn');
            const toggleIcon = document.getElementById('toggleFiltersIcon');
            const toggleText = document.getElementById('toggleFiltersText');
            const activeFiltersCount = document.getElementById('activeFiltersCount');

            // مراقبة حالة الطي/الفتح
            filtersCollapse.addEventListener('shown.bs.collapse', function () {
                toggleIcon.className = 'fas fa-chevron-up me-1';
                toggleText.textContent = 'إخفاء المرشحات';
                localStorage.setItem('filtersCollapsed', 'false');
                // إزالة class الطي من البطاقة
                filtersCollapse.closest('.card').classList.remove('filters-collapsed');
            });

            filtersCollapse.addEventListener('hidden.bs.collapse', function () {
                toggleIcon.className = 'fas fa-chevron-down me-1';
                toggleText.textContent = 'إظهار المرشحات';
                localStorage.setItem('filtersCollapsed', 'true');
                // إضافة class الطي للبطاقة
                filtersCollapse.closest('.card').classList.add('filters-collapsed');
            });

            // استعادة حالة الطي من localStorage - الافتراضي مطوي
            const isCollapsed = localStorage.getItem('filtersCollapsed');
            if (isCollapsed === 'false') {
                // إذا كان المستخدم اختار الإظهار سابقاً
                filtersCollapse.classList.add('show');
                toggleIcon.className = 'fas fa-chevron-up me-1';
                toggleText.textContent = 'إخفاء المرشحات';
                filtersCollapse.closest('.card').classList.remove('filters-collapsed');
            } else {
                // الافتراضي: مطوي
                filtersCollapse.classList.remove('show');
                toggleIcon.className = 'fas fa-chevron-down me-1';
                toggleText.textContent = 'إظهار المرشحات';
                filtersCollapse.closest('.card').classList.add('filters-collapsed');
            }

            // تحديث عداد المرشحات النشطة
            updateActiveFiltersCount();
        });

        // تحديث عداد المرشحات النشطة
        function updateActiveFiltersCount() {
            const activeFiltersCount = document.getElementById('activeFiltersCount');
            let count = 0;

            // فحص جميع حقول التصفية
            const filters = [
                'quickSearch',
                'statusFilter',
                'shippingTypeFilter',
                'dateFrom',
                'dateTo',
                'shippingLineFilter',
                'destinationPortFilter'
            ];

            filters.forEach(filterId => {
                const element = document.getElementById(filterId);
                if (element && element.value && element.value.trim() !== '') {
                    count++;
                }
            });

            if (count > 0) {
                activeFiltersCount.textContent = count;
                activeFiltersCount.style.display = 'inline-block';
            } else {
                activeFiltersCount.style.display = 'none';
            }
        }

        // ربط تحديث العداد مع تغيير المرشحات
        function applyFilters() {
            updateActiveFiltersCount();
            // باقي كود التصفية...
        }

        function performQuickSearch() {
            updateActiveFiltersCount();
            // باقي كود البحث...
        }

        function resetAllFilters() {
            // إعادة تعيين جميع المرشحات
            const filters = [
                'quickSearch',
                'statusFilter',
                'shippingTypeFilter',
                'dateFrom',
                'dateTo',
                'shippingLineFilter',
                'destinationPortFilter'
            ];

            filters.forEach(filterId => {
                const element = document.getElementById(filterId);
                if (element) {
                    element.value = '';
                }
            });

            updateActiveFiltersCount();
            // تحديث الجدول
            window.location.reload();
        }

        function exportShipments() {
            console.log('📤 تصدير الشحنات...');
            showNotification('جاري تحضير ملف التصدير...', 'info');
            // استدعاء API التصدير
            window.open('/shipments/export', '_blank');
        }

        // وظائف البحث والمرشحات
        function toggleQuickSearch() {
            console.log('🔍 تبديل البحث السريع');
            const searchContainer = document.querySelector('.search-filters-container');
            if (searchContainer) {
                if (searchContainer.style.display === 'none' || !searchContainer.style.display) {
                    searchContainer.style.display = 'block';
                    const searchInput = document.getElementById('quickSearch');
                    if (searchInput) {
                        searchInput.focus();
                    }
                    showNotification('تم فتح البحث السريع', 'info');
                } else {
                    searchContainer.style.display = 'none';
                    showNotification('تم إغلاق البحث السريع', 'info');
                }
            } else {
                showNotification('مكون البحث غير متاح', 'warning');
            }
        }

        function toggleAdvancedFilters() {
            console.log('🔧 تبديل المرشحات المتقدمة');
            const filtersContainer = document.querySelector('.advanced-filters');
            if (filtersContainer) {
                if (filtersContainer.style.display === 'none' || !filtersContainer.style.display) {
                    filtersContainer.style.display = 'block';
                    showNotification('تم فتح المرشحات المتقدمة', 'info');
                } else {
                    filtersContainer.style.display = 'none';
                    showNotification('تم إغلاق المرشحات المتقدمة', 'info');
                }
            } else {
                showNotification('المرشحات المتقدمة غير متاحة', 'warning');
            }
        }

        // وظائف شريط التنقل
        function switchDashboardView(view) {
            console.log(`🔄 تبديل العرض إلى: ${view}`);

            // إزالة الفئة النشطة من جميع الأزرار
            document.querySelectorAll('.btn-nav').forEach(btn => {
                btn.classList.remove('active');
            });

            // إضافة الفئة النشطة للزر المحدد
            const activeBtn = document.querySelector(`[data-view="${view}"]`);
            if (activeBtn) {
                activeBtn.classList.add('active');
            }

            // تنفيذ منطق التبديل حسب النوع
            switch(view) {
                case 'overview':
                    showNotification('عرض النظرة العامة', 'info');
                    // إظهار جميع المكونات
                    showAllComponents();
                    break;
                case 'active':
                    showNotification('عرض الشحنات النشطة فقط', 'info');
                    // تطبيق مرشح للشحنات النشطة
                    filterActiveShipments();
                    break;
                case 'tracking':
                    showNotification('عرض التتبع المباشر', 'info');
                    // فتح واجهة التتبع
                    showTrackingInterface();
                    break;
                case 'analytics':
                    showNotification('عرض التحليلات', 'info');
                    // فتح لوحة التحليلات
                    showAnalytics();
                    break;
                default:
                    showNotification('عرض غير معروف', 'warning');
            }
        }

        function showAllComponents() {
            // إظهار جميع مكونات اللوحة
            const components = ['.stats-container', '.shipments-table-container'];
            components.forEach(selector => {
                const element = document.querySelector(selector);
                if (element) {
                    element.style.display = 'block';
                }
            });
        }

        function filterActiveShipments() {
            // تطبيق مرشح للشحنات النشطة
            const statusFilter = document.getElementById('statusFilter');
            if (statusFilter) {
                statusFilter.value = 'في الطريق';
                applyFilters();
            }
        }

        function showTrackingInterface() {
            // فتح واجهة التتبع المباشر
            showNotification('جاري تحميل واجهة التتبع...', 'info');
            // يمكن إضافة منطق لفتح خريطة أو واجهة تتبع
        }

        function showAnalytics() {
            // فتح لوحة التحليلات
            showNotification('جاري تحميل التحليلات...', 'info');
            // يمكن إضافة منطق لعرض الرسوم البيانية
        }

        // وظائف أدوات التحكم
        function toggleSidebar() {
            console.log('📋 تبديل الشريط الجانبي');
            showNotification('تبديل الشريط الجانبي', 'info');
            // منطق إظهار/إخفاء الشريط الجانبي
            // يمكن إضافة الكود هنا لإظهار الشريط الجانبي الأصلي
        }

        function toggleFullscreen() {
            console.log('🖥️ تبديل ملء الشاشة');
            if (document.fullscreenElement) {
                document.exitFullscreen();
                showNotification('تم الخروج من ملء الشاشة', 'info');
            } else {
                document.documentElement.requestFullscreen();
                showNotification('تم تفعيل ملء الشاشة', 'success');
            }
        }

        function showDashboardSettings() {
            console.log('⚙️ إعدادات اللوحة');
            showNotification('فتح إعدادات اللوحة', 'info');
            // يمكن إضافة modal للإعدادات
            showSettingsModal();
        }

        function showSettingsModal() {
            // إنشاء modal للإعدادات
            const modalHtml = `
                <div class="modal fade" id="settingsModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">إعدادات اللوحة</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="autoRefresh">
                                    <label class="form-check-label" for="autoRefresh">
                                        تحديث تلقائي كل 30 ثانية
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showNotifications">
                                    <label class="form-check-label" for="showNotifications">
                                        إظهار الإشعارات
                                    </label>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="saveSettings()">حفظ</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // إضافة modal للصفحة
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // إظهار modal
            const modal = new bootstrap.Modal(document.getElementById('settingsModal'));
            modal.show();

            // إزالة modal عند الإغلاق
            document.getElementById('settingsModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }

        function saveSettings() {
            const autoRefresh = document.getElementById('autoRefresh').checked;
            const showNotifications = document.getElementById('showNotifications').checked;

            localStorage.setItem('autoRefresh', autoRefresh);
            localStorage.setItem('showNotifications', showNotifications);

            showNotification('تم حفظ الإعدادات بنجاح', 'success');

            // إغلاق modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('settingsModal'));
            modal.hide();
        }

        // وظائف المرشحات
        function applyFilters() {
            console.log('🔍 تطبيق المرشحات...');
            // منطق تطبيق المرشحات
        }

        function resetAllFilters() {
            console.log('🔄 إعادة تعيين المرشحات...');
            // إعادة تعيين جميع المرشحات
            document.getElementById('quickSearch').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('shippingTypeFilter').value = '';
            document.getElementById('dateFrom').value = '';
            document.getElementById('dateTo').value = '';
            document.getElementById('shippingLineFilter').value = '';
            document.getElementById('destinationPortFilter').value = '';

            // إعادة تحميل الصفحة
            window.location.reload();
        }

        function performQuickSearch() {
            const searchInput = document.getElementById('quickSearch');
            if (!searchInput) {
                console.warn('⚠️ حقل البحث السريع غير موجود');
                return;
            }

            const searchTerm = searchInput.value.toLowerCase().trim();
            console.log(`🔎 البحث عن: "${searchTerm}"`);

            // تحديد العرض الحالي
            const tableView = document.getElementById('tableView');
            const cardsView = document.getElementById('cardsView');
            const isCardsView = cardsView && cardsView.style.display !== 'none';

            let visibleCount = 0;

            if (isCardsView) {
                // البحث في البطاقات
                console.log('🃏 البحث في وضع البطاقات');
                const cards = document.querySelectorAll('.shipment-card');

                cards.forEach(card => {
                    if (searchTerm === '') {
                        card.style.display = '';
                        visibleCount++;
                    } else {
                        // البحث في البيانات الكاملة المحفوظة أولاً
                        const searchData = card.getAttribute('data-search-content');
                        let found = false;

                        if (searchData && searchData.includes(searchTerm)) {
                            found = true;
                        } else {
                            // البحث الاحتياطي في النص المرئي
                            const cardText = card.textContent.toLowerCase();
                            if (cardText.includes(searchTerm)) {
                                found = true;
                            }
                        }

                        if (found) {
                            card.style.display = '';
                            visibleCount++;
                        } else {
                            card.style.display = 'none';
                        }
                    }
                });

                console.log(`📊 عرض ${visibleCount} من ${cards.length} بطاقة`);
            } else {
                // البحث في الجدول
                console.log('📊 البحث في وضع الجدول');
                const shipmentsTable = document.querySelector('#shipmentsTable tbody') ||
                                     document.querySelector('.shipments-table tbody') ||
                                     document.querySelector('table tbody');

                if (!shipmentsTable) {
                    console.warn('⚠️ جدول الشحنات غير موجود');
                    return;
                }

                const rows = shipmentsTable.querySelectorAll('tr');

                rows.forEach(row => {
                    if (searchTerm === '') {
                        row.style.display = '';
                        visibleCount++;
                    } else {
                        const rowText = row.textContent.toLowerCase();
                        if (rowText.includes(searchTerm)) {
                            row.style.display = '';
                            visibleCount++;
                        } else {
                            row.style.display = 'none';
                        }
                    }
                });

                console.log(`📊 عرض ${visibleCount} من ${rows.length} صف`);
            }

            // إظهار رسالة إذا لم توجد نتائج
            if (visibleCount === 0 && searchTerm !== '') {
                showNotification(`لم يتم العثور على نتائج للبحث: "${searchTerm}"`, 'warning');
            } else if (searchTerm !== '') {
                showNotification(`تم العثور على ${visibleCount} نتيجة`, 'success');
            }
        }

        function clearSearch() {
            document.getElementById('quickSearch').value = '';
            performQuickSearch();
        }

        function setQuickDate(period) {
            const today = new Date();
            const dateFrom = document.getElementById('dateFrom');
            const dateTo = document.getElementById('dateTo');

            dateTo.value = today.toISOString().split('T')[0];

            switch(period) {
                case 'today':
                    dateFrom.value = today.toISOString().split('T')[0];
                    break;
                case 'week':
                    const weekAgo = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
                    dateFrom.value = weekAgo.toISOString().split('T')[0];
                    break;
                case 'month':
                    const monthAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
                    dateFrom.value = monthAgo.toISOString().split('T')[0];
                    break;
                case 'year':
                    const yearAgo = new Date(today.getTime() - (365 * 24 * 60 * 60 * 1000));
                    dateFrom.value = yearAgo.toISOString().split('T')[0];
                    break;
            }

            applyFilters();
        }

        function saveFilters() {
            console.log('💾 حفظ المرشحات...');
            showNotification('تم حفظ المرشحات', 'success');
        }

        function exportFilteredData() {
            console.log('📊 تصدير البيانات المفلترة...');
            showNotification('جاري تحضير ملف التصدير...', 'info');
        }

        // وظائف الأزرار الإضافية
        function showShipmentMap(trackingNumber) {
            console.log(`🗺️ عرض الخريطة للشحنة: ${trackingNumber}`);
            showNotification(`عرض موقع الشحنة: ${trackingNumber}`, 'info');
            // فتح نافذة الخريطة
            window.open(`/shipments/map/${trackingNumber}`, '_blank', 'width=800,height=600');
        }

        function sendNotification(shipmentId) {
            console.log(`🔔 إرسال إشعار للشحنة: ${shipmentId}`);

            // تأكيد الإرسال
            if (confirm('هل تريد إرسال إشعار للعميل بحالة الشحنة؟')) {
                showNotification('جاري إرسال الإشعار...', 'info');

                // محاكاة إرسال الإشعار
                setTimeout(() => {
                    showNotification('تم إرسال الإشعار بنجاح', 'success');
                }, 2000);
            }
        }

        function deleteShipment(shipmentId, trackingNumber) {
            console.log(`🗑️ حذف الشحنة: ${shipmentId}`);

            if (!shipmentId || shipmentId === '' || shipmentId === 'undefined') {
                showNotification('❌ خطأ: ID الشحنة غير صحيح', 'danger');
                return;
            }

            // تأكيد الحذف
            const confirmMessage = `هل أنت متأكد من حذف الشحنة؟\n\nرقم التتبع: ${trackingNumber}\nID: ${shipmentId}\n\n⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!`;

            if (confirm(confirmMessage)) {
                showNotification('جاري حذف الشحنة...', 'warning');

                // إرسال طلب الحذف
                const deleteUrl = `/shipments/quick-delete/${shipmentId}`;
                console.log(`🗑️ إرسال طلب حذف إلى: ${deleteUrl}`);

                fetch(deleteUrl, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    }
                    throw new Error('فشل في حذف الشحنة');
                })
                .then(data => {
                    if (data.success) {
                        showNotification('✅ تم حذف الشحنة بنجاح', 'success');
                        // إعادة تحميل الصفحة بعد 2 ثانية
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    } else {
                        showNotification(`❌ خطأ: ${data.message}`, 'danger');
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ في حذف الشحنة:', error);
                    showNotification('❌ حدث خطأ أثناء حذف الشحنة', 'danger');
                });
            }
        }

        // وظيفة إخفاء/إظهار الشريط العائم
        function toggleFloatingToolbar() {
            const toolbar = document.getElementById('floatingToolbar');
            const toggle = document.getElementById('toolbarToggle');
            const icon = toggle.querySelector('i');

            if (toolbar.classList.contains('hidden')) {
                // إظهار الشريط
                toolbar.classList.remove('hidden');
                icon.className = 'fas fa-chevron-left';
                toggle.title = 'إخفاء الأدوات';
                localStorage.setItem('toolbarVisible', 'true');
            } else {
                // إخفاء الشريط
                toolbar.classList.add('hidden');
                icon.className = 'fas fa-chevron-right';
                toggle.title = 'إظهار الأدوات';
                localStorage.setItem('toolbarVisible', 'false');
            }
        }

        // استعادة حالة الشريط من localStorage
        function restoreToolbarState() {
            const isVisible = localStorage.getItem('toolbarVisible');
            if (isVisible === 'false') {
                toggleFloatingToolbar();
            }
        }

        // وظيفة إظهار الإشعارات
        function showNotification(message, type = 'info') {
            // إنشاء عنصر الإشعار
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;

            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            // إضافة الإشعار للصفحة
            document.body.appendChild(notification);

            // إزالة الإشعار تلقائياً بعد 5 ثوان
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // تهيئة إضافية للوضع الجديد
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تم تحميل لوحة الشحنات بوضع ملء الشاشة');

            // إضافة البيانات للمتغيرات العامة
            window.shipmentsData = {{ recent_shipments | tojson }};
            window.statsData = {{ stats | tojson }};

            // استعادة حالة الشريط العائم
            restoreToolbarState();

            // تهيئة المكونات
            if (typeof initializeShipmentsPage === 'function') {
                initializeShipmentsPage();
            }

            // تهيئة البحث الصوتي
            setTimeout(() => {
                if (typeof initializeVoiceSearch === 'function') {
                    initializeVoiceSearch();
                    console.log('✅ تم تهيئة البحث الصوتي في dashboard');
                } else {
                    console.warn('⚠️ function initializeVoiceSearch غير موجودة');
                }
            }, 1500);

            // رسالة تأكيد تحديث JavaScript
            console.log('✅ تم تحميل JavaScript المحدث - إصدار 2024-01-16-FINAL');
            console.log('🔧 إجبار تحديث cache...');

            // إجبار تحديث cache
            if ('caches' in window) {
                caches.keys().then(function(names) {
                    names.forEach(function(name) {
                        caches.delete(name);
                    });
                });
            }
        });

        // JavaScript لإدارة تعديل حالة الشحنة
        // تحقق من وجود المتغير قبل التعريف
        if (typeof currentShipmentData === 'undefined') {
            var currentShipmentData = {};
        }

        function openStatusModal(element) {
            console.log('🔧 فتح نافذة تعديل الحالة...');

            // الحصول على بيانات الشحنة من العنصر
            currentShipmentData = {
                id: element.getAttribute('data-shipment-id'),
                trackingNumber: element.getAttribute('data-tracking-number'),
                currentStatus: element.getAttribute('data-current-status'),
                currentStatusDisplay: element.getAttribute('data-current-status-display')
            };

            console.log('📦 بيانات الشحنة:', currentShipmentData);

            // تعبئة النافذة المنبثقة
            document.getElementById('modalTrackingNumber').value = currentShipmentData.trackingNumber;
            document.getElementById('modalCurrentStatus').textContent = currentShipmentData.currentStatusDisplay;
            document.getElementById('modalCurrentStatus').className = `badge bg-${getStatusColor(currentShipmentData.currentStatusDisplay)} fs-6`;

            // مسح حقل الملاحظات
            document.getElementById('statusNotes').value = '';

            // تحميل قائمة الحالات المتاحة
            loadAvailableStatuses();

            // إظهار النافذة المنبثقة
            const modal = new bootstrap.Modal(document.getElementById('statusEditModal'));
            modal.show();
        }

        function getStatusColor(status) {
            const statusColors = {
                'مسودة': 'secondary',
                'مؤكدة': 'primary',
                'قيد الشحن': 'warning',
                'وصلت للميناء': 'info',
                'قيد التخليص': 'warning',
                'جاهزة للاستلام': 'success',
                'تم التسليم': 'success',
                'ملغية': 'danger',
                'متأخرة': 'danger',
                'معادة': 'secondary'
            };
            return statusColors[status] || 'secondary';
        }

        function loadAvailableStatuses() {
            console.log('🔄 تحميل الحالات المتاحة من قاعدة البيانات...');

            const select = document.getElementById('newStatus');
            select.innerHTML = '<option value="">جاري تحميل الحالات...</option>';

            // إضافة timestamp لتجنب cache
            const timestamp = new Date().getTime();
            fetch(`/shipments/get_available_statuses?v=${timestamp}`)
                .then(response => {
                    console.log('📡 استجابة الخادم:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('📋 البيانات المستلمة:', data);
                    select.innerHTML = '<option value="">اختر الحالة الجديدة...</option>';

                    if (data.success && data.statuses) {
                        console.log(`✅ تم جلب ${data.statuses.length} حالة من قاعدة البيانات`);
                        console.log('📋 جميع الحالات المستلمة:', data.statuses);
                        console.log('🔍 الحالة الحالية للشحنة:', currentShipmentData.currentStatus);

                        let addedCount = 0;
                        data.statuses.forEach((status, index) => {
                            console.log(`${index + 1}. معالجة الحالة: ${status.code} → ${status.name}`);

                            const option = document.createElement('option');
                            option.value = status.code;
                            option.textContent = status.name;
                            option.setAttribute('data-color', status.color);

                            // تمييز الحالة الحالية
                            if (status.code === currentShipmentData.currentStatus) {
                                option.textContent = status.name + ' (الحالة الحالية)';
                                option.style.fontWeight = 'bold';
                                option.style.backgroundColor = '#e3f2fd';
                                console.log(`   🔵 تم إضافة الحالة الحالية: ${status.code} → ${status.name}`);
                            } else {
                                console.log(`   ✅ تم إضافة: ${status.code} → ${status.name}`);
                            }

                            select.appendChild(option);
                            addedCount++;
                        });

                        console.log(`📊 تم إضافة ${addedCount} حالة إلى القائمة المنسدلة`);

                        // التحقق من وجود arrived_port
                        const arrivedPortExists = data.statuses.some(s => s.code === 'arrived_port');
                        console.log(`🔍 هل توجد حالة arrived_port؟ ${arrivedPortExists ? 'نعم ✅' : 'لا ❌'}`);

                        if (arrivedPortExists) {
                            const arrivedPort = data.statuses.find(s => s.code === 'arrived_port');
                            console.log(`📦 تفاصيل حالة arrived_port:`, arrivedPort);
                        }
                    } else {
                        console.warn('⚠️ فشل في جلب الحالات، استخدام القائمة الاحتياطية');

                        // في حالة الخطأ، استخدم قائمة احتياطية محدثة
                        const fallbackStatuses = [
                            { code: 'draft', name: 'مسودة' },
                            { code: 'confirmed', name: 'مؤكدة' },
                            { code: 'in_transit', name: 'قيد الشحن' },
                            { code: 'arrived_port', name: 'وصلت للميناء' },
                            { code: 'customs_clearance', name: 'قيد التخليص' },
                            { code: 'ready_pickup', name: 'جاهزة للاستلام' },
                            { code: 'delivered', name: 'تم التسليم' },
                            { code: 'cancelled', name: 'ملغية' },
                            { code: 'delayed', name: 'متأخرة' },
                            { code: 'returned', name: 'معادة' }
                        ];

                        fallbackStatuses.forEach(status => {
                            if (status.code !== currentShipmentData.currentStatus) {
                                const option = document.createElement('option');
                                option.value = status.code;
                                option.textContent = status.name;
                                select.appendChild(option);
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ في تحميل الحالات:', error);
                    select.innerHTML = '<option value="">خطأ في تحميل الحالات</option>';
                })
                .finally(() => {
                    // التحقق النهائي من القائمة المنسدلة
                    setTimeout(() => {
                        const finalOptions = select.querySelectorAll('option');
                        console.log(`🔍 عدد العناصر النهائي في القائمة: ${finalOptions.length}`);
                        console.log('📋 جميع العناصر في القائمة:');
                        finalOptions.forEach((option, index) => {
                            if (option.value) { // تجاهل الخيار الافتراضي
                                console.log(`   ${index}. ${option.value} → ${option.textContent}`);
                            }
                        });

                        // البحث عن arrived_port في القائمة
                        const arrivedPortOption = Array.from(finalOptions).find(opt => opt.value === 'arrived_port');
                        if (arrivedPortOption) {
                            console.log('✅ تم العثور على حالة arrived_port في القائمة المنسدلة!');
                            console.log('📦 النص المعروض:', arrivedPortOption.textContent);
                        } else {
                            console.log('❌ لم يتم العثور على حالة arrived_port في القائمة المنسدلة!');
                        }
                    }, 100);
                });
        }

        function updateShipmentStatus() {
            const newStatus = document.getElementById('newStatus').value;
            const notes = document.getElementById('statusNotes').value;

            if (!newStatus) {
                alert('يرجى اختيار الحالة الجديدة');
                return;
            }

            // إظهار مؤشر التحميل
            const saveBtn = document.querySelector('#statusEditModal .btn-primary');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الحفظ...';
            saveBtn.disabled = true;

            // إرسال طلب التحديث
            fetch('/shipments/update_status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    shipment_id: currentShipmentData.id,
                    new_status: newStatus,
                    notes: notes
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إغلاق النافذة المنبثقة
                    bootstrap.Modal.getInstance(document.getElementById('statusEditModal')).hide();

                    // إظهار رسالة نجاح
                    showSuccessMessage('تم تحديث حالة الشحنة بنجاح');

                    // تحديث فوري للحالة في الجدول
                    updateShipmentStatusInTable(currentShipmentData.id, newStatus);

                    // إعادة تحميل الصفحة كـ backup بعد تأخير أطول
                    setTimeout(() => {
                        location.reload();
                    }, 5000);
                } else {
                    alert('خطأ في تحديث الحالة: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في تحديث الحالة');
            })
            .finally(() => {
                // إعادة تعيين زر الحفظ
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;
            });
        }

        function showSuccessMessage(message) {
            // إنشاء رسالة نجاح
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            // إزالة الرسالة تلقائياً بعد 3 ثوان
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        // JavaScript لإدارة تعديل حالة الإفراج
        // تحقق من وجود المتغير قبل التعريف
        if (typeof currentReleaseShipmentData === 'undefined') {
            var currentReleaseShipmentData = {};
        }

        function openReleaseStatusModal(element) {
            console.log('🔧 فتح نافذة تعديل حالة الإفراج...');

            // الحصول على بيانات الشحنة من العنصر
            currentReleaseShipmentData = {
                id: element.getAttribute('data-shipment-id'),
                trackingNumber: element.getAttribute('data-tracking-number'),
                currentReleaseStatus: element.getAttribute('data-current-release-status'),
                currentReleaseStatusDisplay: element.getAttribute('data-current-release-status-display')
            };

            console.log('📦 بيانات الشحنة للإفراج:', currentReleaseShipmentData);

            // تعبئة النافذة المنبثقة
            document.getElementById('modalReleaseTrackingNumber').value = currentReleaseShipmentData.trackingNumber;
            document.getElementById('modalCurrentReleaseStatus').textContent = currentReleaseShipmentData.currentReleaseStatusDisplay;
            document.getElementById('modalCurrentReleaseStatus').className = `badge bg-${getReleaseStatusColor(currentReleaseShipmentData.currentReleaseStatus)} fs-6`;

            // مسح حقل الملاحظات
            document.getElementById('releaseStatusNotes').value = '';

            // تحميل قائمة حالات الإفراج المتاحة
            loadAvailableReleaseStatuses();

            // إظهار النافذة المنبثقة
            const modal = new bootstrap.Modal(document.getElementById('releaseStatusEditModal'));
            modal.show();
        }

        function loadAvailableReleaseStatuses() {
            console.log('🔄 تحميل حالات الإفراج المتاحة من قاعدة البيانات...');

            const select = document.getElementById('newReleaseStatus');
            select.innerHTML = '<option value="">جاري تحميل حالات الإفراج...</option>';

            // إضافة timestamp لتجنب cache
            const timestamp = new Date().getTime();
            fetch(`/shipments/get_available_release_statuses?v=${timestamp}`)
                .then(response => {
                    console.log('📡 استجابة الخادم:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('📋 البيانات المستلمة:', data);
                    select.innerHTML = '<option value="">اختر حالة الإفراج الجديدة...</option>';

                    if (data.success && data.statuses) {
                        console.log(`✅ تم جلب ${data.statuses.length} حالة إفراج من قاعدة البيانات`);

                        data.statuses.forEach((status, index) => {
                            console.log(`${index + 1}. معالجة حالة الإفراج: ${status.code} → ${status.name}`);

                            const option = document.createElement('option');
                            option.value = status.code;
                            option.textContent = status.name;
                            option.setAttribute('data-color', status.color);

                            // تمييز الحالة الحالية
                            if (status.code === currentReleaseShipmentData.currentReleaseStatus) {
                                option.textContent = status.name + ' (الحالة الحالية)';
                                option.style.fontWeight = 'bold';
                                option.style.backgroundColor = '#fff3cd';
                                console.log(`   🔵 تم إضافة الحالة الحالية: ${status.code} → ${status.name}`);
                            } else {
                                console.log(`   ✅ تم إضافة: ${status.code} → ${status.name}`);
                            }

                            select.appendChild(option);
                        });
                    } else {
                        console.warn('⚠️ فشل في جلب حالات الإفراج، استخدام القائمة الاحتياطية');

                        // في حالة الخطأ، استخدم قائمة احتياطية
                        const fallbackStatuses = [
                            { code: 'pending', name: 'في انتظار الإفراج' },
                            { code: 'documents_review', name: 'مراجعة المستندات' },
                            { code: 'payment_verification', name: 'التحقق من المدفوعات' },
                            { code: 'quality_check', name: 'فحص الجودة' },
                            { code: 'approved', name: 'معتمد للإفراج' },
                            { code: 'released', name: 'تم الإفراج' },
                            { code: 'on_hold', name: 'محجوز مؤقت' },
                            { code: 'rejected', name: 'مرفوض الإفراج' }
                        ];

                        fallbackStatuses.forEach(status => {
                            const option = document.createElement('option');
                            option.value = status.code;
                            option.textContent = status.name;
                            select.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ في تحميل حالات الإفراج:', error);
                    select.innerHTML = '<option value="">خطأ في تحميل حالات الإفراج</option>';
                });
        }

        function getReleaseStatusColor(status) {
            const statusColors = {
                'pending': 'secondary',
                'documents_review': 'info',
                'payment_verification': 'warning',
                'quality_check': 'primary',
                'approved': 'success',
                'released': 'success',
                'on_hold': 'danger',
                'rejected': 'danger'
            };
            return statusColors[status] || 'secondary';
        }

        function updateReleaseStatus() {
            const newReleaseStatus = document.getElementById('newReleaseStatus').value;
            const notes = document.getElementById('releaseStatusNotes').value;

            if (!newReleaseStatus) {
                alert('يرجى اختيار حالة الإفراج الجديدة');
                return;
            }

            const saveBtn = event.target;
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري التحديث...';
            saveBtn.disabled = true;

            const data = {
                shipment_id: currentReleaseShipmentData.id,
                new_release_status: newReleaseStatus,
                notes: notes
            };

            fetch('/shipments/api/update_release_status_quick', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessMessage('تم تحديث حالة الإفراج بنجاح!');

                    // إغلاق النافذة المنبثقة
                    const modal = bootstrap.Modal.getInstance(document.getElementById('releaseStatusEditModal'));
                    modal.hide();

                    // إعادة تحميل الصفحة لإظهار التحديثات
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    alert('خطأ في تحديث حالة الإفراج: ' + (data.message || 'خطأ غير معروف'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال بالخادم');
            })
            .finally(() => {
                // إعادة تعيين زر الحفظ
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;
            });
        }
    </script>

    <!-- نافذة تعديل حالة الشحنة -->
    <div class="modal fade" id="statusEditModal" tabindex="-1" aria-labelledby="statusEditModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="statusEditModalLabel">
                        <i class="fas fa-edit me-2"></i>
                        تعديل حالة الشحنة
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <form id="statusEditForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">رقم التتبع:</label>
                                <input type="text" class="form-control" id="modalTrackingNumber" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">الحالة الحالية:</label>
                                <span id="modalCurrentStatus" class="badge bg-secondary fs-6"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="newStatus" class="form-label fw-bold">الحالة الجديدة:</label>
                            <select class="form-select" id="newStatus" required>
                                <option value="">اختر الحالة الجديدة...</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="statusNotes" class="form-label fw-bold">ملاحظات التحديث:</label>
                            <textarea class="form-control" id="statusNotes" rows="3" placeholder="أدخل ملاحظات حول تغيير الحالة (اختياري)"></textarea>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> سيتم تحديث حالة أمر الشراء المرتبط تلقائياً حسب الحالة الجديدة.
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </button>
                    <button type="button" class="btn btn-primary" onclick="updateShipmentStatus()">
                        <i class="fas fa-save me-1"></i>
                        حفظ التحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل حالة الإفراج -->
    <div class="modal fade" id="releaseStatusEditModal" tabindex="-1" aria-labelledby="releaseStatusEditModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="releaseStatusEditModalLabel">
                        <i class="fas fa-unlock-alt me-2"></i>
                        تعديل حالة الإفراج
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <form id="releaseStatusEditForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">رقم التتبع:</label>
                                <input type="text" class="form-control" id="modalReleaseTrackingNumber" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">حالة الإفراج الحالية:</label>
                                <span id="modalCurrentReleaseStatus" class="badge bg-secondary fs-6"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="newReleaseStatus" class="form-label fw-bold">حالة الإفراج الجديدة:</label>
                            <select class="form-select" id="newReleaseStatus" required>
                                <option value="">اختر حالة الإفراج الجديدة...</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="releaseStatusNotes" class="form-label fw-bold">ملاحظات الإفراج:</label>
                            <textarea class="form-control" id="releaseStatusNotes" rows="3" placeholder="أدخل ملاحظات حول تغيير حالة الإفراج (اختياري)"></textarea>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> تغيير حالة الإفراج قد يؤثر على إجراءات التخليص الجمركي والتسليم.
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </button>
                    <button type="button" class="btn btn-warning" onclick="updateReleaseStatus()">
                        <i class="fas fa-save me-1"></i>
                        حفظ التحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- شريط الأدوات العائم -->
    <div class="floating-toolbar">
        <div class="d-flex align-items-center gap-3">
            <button class="btn btn-primary btn-sm" onclick="refreshDashboardData()" title="تحديث البيانات">
                <i class="fas fa-sync-alt"></i>
            </button>
            <button class="btn btn-success btn-sm" onclick="exportShipmentsData()" title="تصدير البيانات">
                <i class="fas fa-download"></i>
            </button>
            <a href="{{ url_for('shipments.new_cargo_shipment') }}" class="btn btn-info btn-sm" title="شحنة جديدة">
                <i class="fas fa-plus"></i>
            </a>
            <div class="vr"></div>
            <small class="text-muted">
                <i class="fas fa-clock me-1"></i>
                آخر تحديث: <span id="lastUpdateTime">الآن</span>
            </small>
        </div>
    </div>

    <!-- تحديث وقت آخر تحديث -->
    <script>
        // تحديث وقت آخر تحديث
        function updateLastUpdateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('lastUpdateTime').textContent = timeString;
        }

        // تحديث الوقت كل دقيقة
        setInterval(updateLastUpdateTime, 60000);

        // تحديث الوقت عند تحديث البيانات
        const originalRefresh = window.refreshDashboardData;
        if (originalRefresh) {
            window.refreshDashboardData = function() {
                originalRefresh();
                updateLastUpdateTime();
            };
        }

        // فحص الأيقونات وإظهار البديل إذا لزم الأمر
        function checkIcons() {
            // فحص إذا كان Font Awesome محمل
            const testIcon = document.createElement('i');
            testIcon.className = 'fas fa-eye';
            testIcon.style.position = 'absolute';
            testIcon.style.left = '-9999px';
            document.body.appendChild(testIcon);

            const computedStyle = window.getComputedStyle(testIcon, ':before');
            const fontFamily = computedStyle.getPropertyValue('font-family');

            document.body.removeChild(testIcon);

            // إذا لم يعمل Font Awesome، أظهر الأيقونات الاحتياطية
            if (!fontFamily.includes('Font Awesome')) {
                const buttons = document.querySelectorAll('.btn-group .btn');
                buttons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    const fallback = btn.querySelector('.icon-fallback');
                    if (icon && fallback) {
                        icon.style.display = 'none';
                        fallback.style.display = 'inline-block';
                    }
                });
            }
        }

        // تشغيل الفحص عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkIcons, 1000); // انتظار ثانية لتحميل الخطوط
            initializeTheme(); // تهيئة الوضع المحفوظ

            // فرض الوضع الداكن على الجدول فوراً
            setTimeout(forceDarkModeStyles, 100);
            setTimeout(forceDarkModeStyles, 500);
            setTimeout(forceDarkModeStyles, 1000);
            setTimeout(forceDarkModeStyles, 2000);

            // فرض إضافي على حاوي الجدول
            setTimeout(function() {
                const tableBody = document.getElementById('shipmentsTableBody');
                if (tableBody && (document.body.classList.contains('dark-mode') || localStorage.getItem('theme') === 'dark')) {
                    tableBody.classList.add('dark-mode');
                    console.log('🌙 تم إضافة dark-mode لحاوي الجدول');
                }
            }, 100);

            // تحسين التجاوب للجوال
            optimizeMobileExperience();

            // محذوف لتحسين الأداء

            // تهيئة زر تبديل العرض
            setTimeout(function() {
                const toggleBtn = document.getElementById('mobileViewToggle');
                if (toggleBtn) {
                    console.log('✅ تم العثور على زر تبديل العرض');
                    // تأكد من أن الزر يعمل
                    toggleBtn.style.display = 'flex';
                } else {
                    console.error('❌ لم يتم العثور على زر تبديل العرض');
                }
            }, 1000);

            // مراقب للتغييرات في DOM لتطبيق الوضع الداكن على العناصر الجديدة
            const observer = new MutationObserver(function(mutations) {
                if (document.body.classList.contains('dark-mode')) {
                    setTimeout(forceDarkModeStyles, 50);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            // تطبيق الوضع الداكن كل ثانيتين للتأكد
            setInterval(function() {
                if (document.body.classList.contains('dark-mode')) {
                    forceDarkModeStyles();
                }
            }, 2000);
        });

        // === وظائف الوضع الداكن ===
        function toggleDarkMode() {
            const body = document.body;
            const html = document.documentElement;
            const themeIcon = document.getElementById('themeIcon');
            const themeIconFallback = document.getElementById('themeIconFallback');
            const themeToggle = document.getElementById('themeToggle');

            const tableBody = document.getElementById('shipmentsTableBody');

            if (body.classList.contains('dark-mode')) {
                // التبديل للوضع الفاتح
                body.classList.remove('dark-mode');
                html.classList.remove('dark-mode');
                if (tableBody) tableBody.classList.remove('dark-mode');
                themeIcon.className = 'fas fa-moon';
                themeIconFallback.textContent = '🌙';
                themeToggle.title = 'تبديل للوضع الداكن';
                localStorage.setItem('theme', 'light');
                showToast('تم التبديل للوضع الفاتح', 'success', 'fas fa-sun');
                console.log('☀️ تم التبديل للوضع الفاتح');
            } else {
                // التبديل للوضع الداكن
                body.classList.add('dark-mode');
                html.classList.add('dark-mode');
                if (tableBody) tableBody.classList.add('dark-mode');
                themeIcon.className = 'fas fa-sun';
                themeIconFallback.textContent = '☀️';
                themeToggle.title = 'تبديل للوضع الفاتح';
                localStorage.setItem('theme', 'dark');
                showToast('تم التبديل للوضع الداكن', 'success', 'fas fa-moon');
                console.log('🌙 تم التبديل للوضع الداكن');
            }

            // فرض تطبيق الوضع الداكن على العناصر المقاومة
            forceDarkModeStyles();
            // تطبيق إضافي متكرر للتأكد
            setTimeout(forceDarkModeStyles, 50);
            setTimeout(forceDarkModeStyles, 200);
            setTimeout(forceDarkModeStyles, 500);
        }

        // دالة لفرض تطبيق الوضع الداكن
        function forceDarkModeStyles() {
            if (document.body.classList.contains('dark-mode')) {
                // فرض تطبيق الوضع الداكن على بطاقة الجدول
                const tableCard = document.getElementById('shipmentsTableCard');
                if (tableCard) {
                    tableCard.style.backgroundColor = '#2d3748';
                    tableCard.style.borderColor = '#4a5568';
                }

                // فرض تطبيق الوضع الداكن على رأس الجدول
                const tableHeader = document.getElementById('shipmentsTableHeader');
                if (tableHeader) {
                    tableHeader.style.backgroundColor = '#1a202c';
                    tableHeader.style.color = '#ffffff';
                    tableHeader.style.borderBottom = '1px solid #4a5568';
                }

                // فرض تطبيق الوضع الداكن على جسم الجدول
                const tableBody = document.getElementById('shipmentsTableBody');
                if (tableBody) {
                    tableBody.style.backgroundColor = '#2d3748';
                    tableBody.style.color = '#ffffff';
                }

                // فرض تطبيق الوضع الداكن على أزرار رأس الجدول
                const refreshBtn = document.getElementById('refreshBtn');
                const exportBtn = document.getElementById('exportBtn');
                const newShipmentBtn = document.getElementById('newShipmentBtn');

                if (refreshBtn) {
                    refreshBtn.style.backgroundColor = 'transparent';
                    refreshBtn.style.borderColor = '#718096';
                    refreshBtn.style.color = '#a0aec0';
                }

                if (exportBtn) {
                    exportBtn.style.backgroundColor = 'transparent';
                    exportBtn.style.borderColor = '#718096';
                    exportBtn.style.color = '#a0aec0';
                }

                if (newShipmentBtn) {
                    newShipmentBtn.style.backgroundColor = '#3182ce';
                    newShipmentBtn.style.borderColor = '#3182ce';
                    newShipmentBtn.style.color = '#ffffff';
                }

                // فرض تطبيق الوضع الداكن على الجدول نفسه
                const tables = document.querySelectorAll('.shipments-table-component table, .shipments-table-component .table');
                tables.forEach(table => {
                    table.style.backgroundColor = '#2d3748';
                    table.style.color = '#ffffff';
                });

                // فرض تطبيق الوضع الداكن على رؤوس الجداول
                const tableHeaders = document.querySelectorAll('.shipments-table-component thead, .shipments-table-component thead th');
                tableHeaders.forEach(header => {
                    header.style.backgroundColor = '#1a202c';
                    header.style.color = '#ffffff';
                    header.style.borderColor = '#4a5568';
                });

                // فرض تطبيق الوضع الداكن على خلايا الجدول
                const tableCells = document.querySelectorAll('.shipments-table-component tbody td, .shipments-table-component tbody tr');
                tableCells.forEach(cell => {
                    cell.style.backgroundColor = '#2d3748';
                    cell.style.color = '#ffffff';
                    cell.style.borderColor = '#4a5568';
                });

                // فرض تطبيق الوضع الداكن على حاوي الجدول
                const tableContainers = document.querySelectorAll('.table-responsive, .content-card .card-body');
                tableContainers.forEach(container => {
                    container.style.backgroundColor = '#2d3748';
                    container.style.color = '#ffffff';
                });

                // فرض تطبيق الوضع الداكن على جميع الجداول بقوة
                const allTables = document.querySelectorAll('table, .table');
                allTables.forEach(table => {
                    table.style.setProperty('background-color', '#2d3748', 'important');
                    table.style.setProperty('color', '#ffffff', 'important');
                });

                // فرض تطبيق الوضع الداكن على جميع رؤوس الجداول
                const allHeaders = document.querySelectorAll('table thead, table thead th, .table thead, .table thead th');
                allHeaders.forEach(header => {
                    header.style.setProperty('background-color', '#1a202c', 'important');
                    header.style.setProperty('color', '#ffffff', 'important');
                    header.style.setProperty('border-color', '#4a5568', 'important');
                });

                // فرض تطبيق الوضع الداكن على جميع خلايا الجداول
                const allCells = document.querySelectorAll('table tbody td, table tbody tr, .table tbody td, .table tbody tr');
                allCells.forEach(cell => {
                    cell.style.setProperty('background-color', '#2d3748', 'important');
                    cell.style.setProperty('color', '#ffffff', 'important');
                    cell.style.setProperty('border-color', '#4a5568', 'important');
                });

                // فرض تطبيق الوضع الداكن على الصفوف الزوجية
                const evenRows = document.querySelectorAll('table tbody tr:nth-child(even), .table tbody tr:nth-child(even)');
                evenRows.forEach(row => {
                    row.style.setProperty('background-color', '#374151', 'important');
                });

                console.log('🌙 تم تطبيق الوضع الداكن بقوة على جميع عناصر الجدول');
            }
        }

        // تهيئة الوضع المحفوظ
        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme');
            const body = document.body;
            const html = document.documentElement;
            const themeIcon = document.getElementById('themeIcon');
            const themeIconFallback = document.getElementById('themeIconFallback');
            const themeToggle = document.getElementById('themeToggle');

            const tableBody = document.getElementById('shipmentsTableBody');

            if (savedTheme === 'dark') {
                body.classList.add('dark-mode');
                html.classList.add('dark-mode');
                if (tableBody) tableBody.classList.add('dark-mode');
                if (themeIcon) {
                    themeIcon.className = 'fas fa-sun';
                }
                if (themeIconFallback) {
                    themeIconFallback.textContent = '☀️';
                }
                if (themeToggle) {
                    themeToggle.title = 'تبديل للوضع الفاتح';
                }
                // تطبيق الوضع الداكن فوراً ومتكرر
                setTimeout(forceDarkModeStyles, 100);
                setTimeout(forceDarkModeStyles, 500);
                setTimeout(forceDarkModeStyles, 1000);
                console.log('🌙 تم تطبيق الوضع الداكن');
            } else {
                body.classList.remove('dark-mode');
                html.classList.remove('dark-mode');
                if (tableBody) tableBody.classList.remove('dark-mode');
                if (themeIcon) {
                    themeIcon.className = 'fas fa-moon';
                }
                if (themeIconFallback) {
                    themeIconFallback.textContent = '🌙';
                }
                if (themeToggle) {
                    themeToggle.title = 'تبديل للوضع الداكن';
                }
                console.log('☀️ تم تطبيق الوضع الفاتح');
            }
        }

        // دالة محذوفة لتحسين الأداء
        function forceTableDarkModeAggressively() {
            // محذوفة لتحسين الأداء
        }

        // === وظائف تبديل عرض الجوال ===
        let currentView = 'table'; // table أو cards

        function toggleMobileView() {
            console.log('🔄 محاولة تبديل العرض...');

            const tableView = document.getElementById('tableView');
            const cardsView = document.getElementById('cardsView');
            const toggleBtn = document.getElementById('mobileViewToggle');

            // تحقق من وجود العناصر
            if (!tableView) {
                console.error('❌ لم يتم العثور على tableView');
                return;
            }
            if (!cardsView) {
                console.error('❌ لم يتم العثور على cardsView');
                return;
            }
            if (!toggleBtn) {
                console.error('❌ لم يتم العثور على mobileViewToggle');
                return;
            }

            const viewText = toggleBtn.querySelector('.view-text');
            if (!viewText) {
                console.error('❌ لم يتم العثور على view-text');
                return;
            }

            console.log('✅ جميع العناصر موجودة، العرض الحالي:', currentView);

            if (currentView === 'table') {
                // التبديل إلى عرض البطاقات
                tableView.style.display = 'none';
                cardsView.style.display = 'block';
                currentView = 'cards';
                viewText.textContent = 'جدول';
                toggleBtn.classList.add('active');

                // إنشاء البطاقات من بيانات الجدول
                createMobileCards();

                // تطبيق البحث الحالي على البطاقات الجديدة
                const searchInput = document.getElementById('quickSearch');
                if (searchInput && searchInput.value.trim() !== '') {
                    setTimeout(() => {
                        performQuickSearch();
                    }, 100);
                }

                console.log('🃏 تم التبديل إلى عرض البطاقات');
            } else {
                // التبديل إلى عرض الجدول
                tableView.style.display = 'block';
                cardsView.style.display = 'none';
                currentView = 'table';
                viewText.textContent = 'بطاقات';
                toggleBtn.classList.remove('active');

                // تطبيق البحث الحالي على الجدول
                const searchInput = document.getElementById('quickSearch');
                if (searchInput && searchInput.value.trim() !== '') {
                    setTimeout(() => {
                        performQuickSearch();
                    }, 100);
                }

                console.log('📊 تم التبديل إلى عرض الجدول');
            }
        }

        function createMobileCards() {
            console.log('🃏 بدء إنشاء البطاقات...');

            const container = document.querySelector('.mobile-cards-container');
            if (!container) {
                console.error('❌ لم يتم العثور على mobile-cards-container');
                return;
            }

            // البحث عن الجدول في أماكن مختلفة
            let tableRows = document.querySelectorAll('.shipments-table-component tbody tr');
            if (tableRows.length === 0) {
                tableRows = document.querySelectorAll('#shipmentsTableBody tbody tr');
            }
            if (tableRows.length === 0) {
                tableRows = document.querySelectorAll('table tbody tr');
            }

            console.log(`📊 تم العثور على ${tableRows.length} صف في الجدول`);

            // مسح البطاقات الموجودة
            container.innerHTML = '';

            if (tableRows.length === 0) {
                container.innerHTML = '<div class="no-data">لا توجد شحنات لعرضها</div>';
                return;
            }

            tableRows.forEach((row, index) => {
                const cells = row.querySelectorAll('td');
                if (cells.length === 0) return;

                console.log(`📝 معالجة الصف ${index + 1}:`, cells.length, 'خلايا');

                // استخراج البيانات من الجدول بالترتيب الصحيح
                const shipmentDate = cells[0]?.textContent?.trim() || `${index + 1}`;
                const client = cells[1]?.textContent?.trim() || 'غير محدد';
                const cargoDetails = cells[2]?.textContent?.trim() || 'غير محدد';
                const packagesText = cells[3]?.textContent?.trim() || '0';
                const packagesHtml = cells[3]?.innerHTML || '<span class="badge bg-secondary">0</span>';
                const destination = cells[4]?.textContent?.trim() || 'غير محدد'; // ميناء الوصول
                const statusText = cells[9]?.textContent?.trim() || 'غير محدد';
                const statusHtml = cells[9]?.innerHTML || '<span class="badge bg-secondary">غير محدد</span>'; // حالة الشحنة
                const releaseStatusText = cells[10]?.textContent?.trim() || 'غير محدد';
                const releaseStatusHtml = cells[10]?.innerHTML || '<span class="badge bg-secondary">غير محدد</span>'; // حالة الإفراج

                // استخراج cargo_id من رابط إدارة الوثائق في الجدول
                let cargoId = null;
                const actionsCell = cells[11]; // خلية الإجراءات
                if (actionsCell) {
                    const documentsLink = actionsCell.querySelector('a[href*="/cargo/"][href*="/documents"]');
                    if (documentsLink) {
                        const href = documentsLink.getAttribute('href');
                        const match = href.match(/\/cargo\/(\d+)\/documents/);
                        if (match) {
                            cargoId = match[1];
                        }
                    }
                }

                console.log(`🔍 استخراج cargo_id للصف ${index + 1}: ${cargoId}`);

                // إذا لم نجد cargo_id، نحاول استخراجه من data attributes
                if (!cargoId) {
                    const statusBadge = cells[9]?.querySelector('[data-shipment-id]');
                    if (statusBadge) {
                        cargoId = statusBadge.getAttribute('data-shipment-id');
                        console.log(`🔍 استخراج cargo_id من data-shipment-id: ${cargoId}`);
                    }
                }

                // جمع جميع البيانات للبحث (من جميع خلايا الصف)
                let allRowData = '';
                cells.forEach((cell, cellIndex) => {
                    const cellText = cell.textContent.toLowerCase().trim();
                    allRowData += ' ' + cellText;

                    // تسجيل تفصيلي للصف الأول فقط للتشخيص
                    if (index === 0) {
                        console.log(`   خلية ${cellIndex}: "${cellText.substring(0, 50)}${cellText.length > 50 ? '...' : ''}"`);
                    }
                });

                if (index === 0) {
                    console.log(`🔍 بيانات البحث الكاملة للصف الأول: "${allRowData.substring(0, 200)}${allRowData.length > 200 ? '...' : ''}"`);
                }

                // إنشاء البطاقة
                const card = createShipmentCard({
                    number: shipmentDate,
                    cargoId: cargoId,
                    client: client,
                    cargoDetails: cargoDetails,
                    destination: destination,
                    packagesText: packagesText,
                    packagesHtml: packagesHtml,
                    statusText: statusText,
                    statusHtml: statusHtml,
                    releaseStatusText: releaseStatusText,
                    releaseStatusHtml: releaseStatusHtml,
                    rowData: row,
                    searchData: allRowData // جميع بيانات الصف للبحث
                });

                container.appendChild(card);
            });

            console.log(`✅ تم إنشاء ${container.children.length} بطاقة بنجاح`);
        }

        function createShipmentCard(data) {
            console.log('🃏 إنشاء بطاقة جديدة:', data.number);

            const card = document.createElement('div');
            card.className = 'shipment-card';

            // حفظ بيانات البحث في البطاقة
            if (data.searchData) {
                card.setAttribute('data-search-content', data.searchData);
            }

            card.innerHTML = `
                <div class="shipment-card-header">
                    <span class="shipment-number">${data.number}</span>
                    <div class="status-badges">
                        ${data.statusHtml}
                    </div>
                </div>
                <div class="shipment-card-body">
                    <div class="info-row">
                        <span class="info-label">المرسل:</span>
                        <span class="info-value" title="${data.client}">${data.client}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">البضاعة:</span>
                        <span class="info-value" title="${data.cargoDetails}">${data.cargoDetails}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">الطرود:</span>
                        <span class="info-value" title="${data.packagesText}">${data.packagesHtml}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">الوجهة:</span>
                        <span class="info-value" title="${data.destination}">${data.destination}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">حالة الإفراج:</span>
                        <span class="info-value" title="${data.releaseStatusText}">${data.releaseStatusHtml}</span>
                    </div>
                </div>
                <div class="shipment-card-actions">
                    <a href="#" class="card-action-btn primary" onclick="viewShipmentDetails('${data.cargoId || data.number}')">
                        تفاصيل
                    </a>
                    ${data.cargoId ? `
                        <a href="#" class="card-action-btn secondary" onclick="manageDocuments('${data.cargoId}')">
                            إدارة الوثائق
                        </a>
                    ` : `
                        <span class="card-action-btn secondary disabled" title="معرف الشحنة غير متوفر">
                            إدارة الوثائق
                        </span>
                    `}
                </div>
            `;

            return card;
        }

        // وظائف مساعدة للبطاقات
        function viewShipmentDetails(shipmentNumber) {
            console.log('عرض تفاصيل الشحنة:', shipmentNumber);
            // يمكن إضافة منطق عرض التفاصيل هنا
        }

        function manageDocuments(cargoId) {
            console.log('📁 إدارة وثائق الشحنة، cargo_id:', cargoId);

            if (!cargoId || cargoId === 'null' || cargoId === 'undefined') {
                showNotification('❌ معرف الشحنة غير متوفر', 'error');
                return;
            }

            try {
                // فتح صفحة إدارة الوثائق الصحيحة
                const documentsUrl = `/shipments/cargo/${cargoId}/documents`;
                const newWindow = window.open(documentsUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

                // التحقق من نجاح فتح النافذة
                if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
                    // إذا فشل فتح النافذة (حاجب النوافذ المنبثقة)
                    showNotification('⚠️ تم حجب النافذة المنبثقة. يرجى السماح للنوافذ المنبثقة وإعادة المحاولة.', 'warning');

                    // محاولة بديلة - فتح في نفس التبويب
                    if (confirm('هل تريد فتح صفحة إدارة الوثائق في نفس التبويب؟')) {
                        window.location.href = documentsUrl;
                    }
                } else {
                    showNotification('✅ تم فتح صفحة إدارة الوثائق', 'success');
                }
            } catch (error) {
                console.error('❌ خطأ في فتح نافذة إدارة الوثائق:', error);
                showNotification('❌ حدث خطأ في فتح نافذة إدارة الوثائق', 'error');

                // محاولة بديلة
                const documentsUrl = `/shipments/cargo/${cargoId}/documents`;
                window.location.href = documentsUrl;
            }
        }

        // دالة تحسين التجاوب للجوال
        function optimizeMobileExperience() {
            const isMobile = window.innerWidth <= 768;

            if (isMobile) {
                console.log('📱 تطبيق تحسينات الجوال...');

                // تحسين الجدول
                const tables = document.querySelectorAll('.table');
                tables.forEach(table => {
                    // إضافة تمرير أفقي
                    if (!table.closest('.table-responsive')) {
                        const wrapper = document.createElement('div');
                        wrapper.className = 'table-responsive';
                        table.parentNode.insertBefore(wrapper, table);
                        wrapper.appendChild(table);
                    }
                });

                // تحسين الأزرار
                const btnGroups = document.querySelectorAll('.btn-group');
                btnGroups.forEach(group => {
                    group.classList.add('btn-group-vertical');
                });

                // إخفاء عناصر غير ضرورية
                const hideElements = document.querySelectorAll('.d-none-mobile');
                hideElements.forEach(el => {
                    el.style.display = 'none';
                });

                // تحسين النوافذ المنبثقة
                const modals = document.querySelectorAll('.modal-dialog');
                modals.forEach(modal => {
                    modal.classList.add('modal-fullscreen-sm-down');
                });

                // تحسين الفلاتر
                const filterRows = document.querySelectorAll('.filters-section .row');
                filterRows.forEach(row => {
                    row.classList.add('g-2'); // تقليل المسافات
                });

                console.log('✅ تم تطبيق تحسينات الجوال');
            }
        }

        // مراقبة تغيير حجم الشاشة
        window.addEventListener('resize', function() {
            clearTimeout(window.resizeTimeout);
            window.resizeTimeout = setTimeout(function() {
                optimizeMobileExperience();
                forceTableDarkModeAggressively();
            }, 250);
        });

        // مراقب محذوف لتحسين الأداء

        // دالة إظهار الرسائل (إذا لم تكن موجودة)
        function showToast(message, type = 'info', icon = 'fas fa-info-circle') {
            // إنشاء عنصر التوست
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            toast.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;

            toast.innerHTML = `
                <i class="${icon} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(toast);

            // إزالة التوست بعد 3 ثوان
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
