// JavaScript للوحة الشحنات بوضع ملء الشاشة

class FullScreenDashboard {
    constructor() {
        this.currentView = 'overview';
        this.sidebarVisible = false;
        this.toolbarMinimized = false;
        this.originalSidebar = null;
        this.originalMainContent = null;
        
        this.initializeLayout();
        this.setupEventListeners();
        this.loadUserPreferences();
    }
    
    initializeLayout() {
        // حفظ العناصر الأصلية
        this.originalSidebar = document.querySelector('.sidebar');
        this.originalMainContent = document.querySelector('.main-content');
        
        // تطبيق التصميم الجديد
        document.body.classList.add('dashboard-fullscreen');
        
        console.log('✅ تم تهيئة تخطيط ملء الشاشة');
    }
    
    setupEventListeners() {
        // مستمع أحداث تغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.handleResize();
        });
        
        // مستمع أحداث اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
        
        console.log('✅ تم إعداد مستمعي الأحداث');
    }
    
    loadUserPreferences() {
        // تحميل تفضيلات المستخدم من localStorage
        const preferences = localStorage.getItem('dashboard-preferences');
        if (preferences) {
            const prefs = JSON.parse(preferences);
            this.currentView = prefs.currentView || 'overview';
            this.toolbarMinimized = prefs.toolbarMinimized || false;
            
            if (this.toolbarMinimized) {
                this.toggleFloatingToolbar();
            }
        }
    }
    
    saveUserPreferences() {
        const preferences = {
            currentView: this.currentView,
            toolbarMinimized: this.toolbarMinimized
        };
        localStorage.setItem('dashboard-preferences', JSON.stringify(preferences));
    }
    
    handleResize() {
        // التعامل مع تغيير حجم النافذة
        const width = window.innerWidth;
        
        if (width < 768) {
            // إخفاء أزرار التنقل في الشاشات الصغيرة
            document.querySelector('.nav-actions').style.display = 'none';
        } else {
            document.querySelector('.nav-actions').style.display = 'flex';
        }
    }
    
    handleKeyboardShortcuts(e) {
        // اختصارات لوحة المفاتيح
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case 'b':
                    e.preventDefault();
                    this.toggleSidebar();
                    break;
                case 'f':
                    e.preventDefault();
                    this.toggleFullscreen();
                    break;
                case 'r':
                    e.preventDefault();
                    this.refreshDashboard();
                    break;
            }
        }
    }
}

// متغير عام للوحة
let dashboard;

// تهيئة اللوحة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    dashboard = new FullScreenDashboard();
    console.log('🚀 تم تهيئة لوحة الشحنات بوضع ملء الشاشة');
});

// وظائف التحكم في العرض
function switchDashboardView(viewName) {
    console.log(`🔄 تبديل العرض إلى: ${viewName}`);
    
    // تحديث الأزرار
    document.querySelectorAll('.btn-nav').forEach(btn => {
        btn.classList.remove('active');
    });
    
    document.querySelector(`[data-view="${viewName}"]`).classList.add('active');
    
    // تحديث العرض الحالي
    dashboard.currentView = viewName;
    dashboard.saveUserPreferences();
    
    // هنا يمكن إضافة منطق تبديل المحتوى إذا لزم الأمر
    // لكن حالياً نحافظ على المحتوى الأصلي
    
    showNotification(`تم التبديل إلى: ${getViewDisplayName(viewName)}`, 'success');
}

function getViewDisplayName(viewName) {
    const viewNames = {
        'overview': 'نظرة عامة',
        'active': 'الشحنات النشطة',
        'tracking': 'التتبع المباشر',
        'analytics': 'التحليلات'
    };
    return viewNames[viewName] || viewName;
}

// وظائف التحكم في الشريط الجانبي
function toggleSidebar() {
    if (!dashboard.originalSidebar) return;
    
    dashboard.sidebarVisible = !dashboard.sidebarVisible;
    
    if (dashboard.sidebarVisible) {
        // إظهار الشريط الجانبي
        dashboard.originalSidebar.style.display = 'block';
        dashboard.originalMainContent.style.marginLeft = '250px';
        dashboard.originalMainContent.style.width = 'calc(100% - 250px)';
        
        console.log('👁️ تم إظهار الشريط الجانبي');
        showNotification('تم إظهار الشريط الجانبي', 'info');
    } else {
        // إخفاء الشريط الجانبي
        dashboard.originalSidebar.style.display = 'none';
        dashboard.originalMainContent.style.marginLeft = '0';
        dashboard.originalMainContent.style.width = '100%';
        
        console.log('🙈 تم إخفاء الشريط الجانبي');
        showNotification('تم إخفاء الشريط الجانبي', 'info');
    }
}

// وظائف التحكم في ملء الشاشة
function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().then(() => {
            console.log('🖥️ تم تفعيل وضع ملء الشاشة');
            showNotification('تم تفعيل وضع ملء الشاشة', 'success');
        });
    } else {
        document.exitFullscreen().then(() => {
            console.log('🪟 تم إلغاء وضع ملء الشاشة');
            showNotification('تم إلغاء وضع ملء الشاشة', 'info');
        });
    }
}

// وظائف شريط الأدوات العائم
function toggleFloatingToolbar() {
    const toolbar = document.getElementById('floatingToolbar');
    if (!toolbar) return;
    
    dashboard.toolbarMinimized = !dashboard.toolbarMinimized;
    
    if (dashboard.toolbarMinimized) {
        toolbar.classList.add('minimized');
        console.log('📦 تم تصغير شريط الأدوات');
    } else {
        toolbar.classList.remove('minimized');
        console.log('📋 تم توسيع شريط الأدوات');
    }
    
    dashboard.saveUserPreferences();
}

// وظائف الأدوات
function refreshDashboard() {
    console.log('🔄 تحديث لوحة الشحنات...');
    showNotification('جاري تحديث البيانات...', 'info');
    
    // إعادة تحميل الصفحة مع الحفاظ على الوضع الحالي
    window.location.reload();
}

function refreshDashboardData() {
    console.log('📊 تحديث بيانات اللوحة...');
    showNotification('جاري تحديث البيانات...', 'info');
    
    // هنا يمكن إضافة منطق تحديث البيانات عبر AJAX
    // مؤقتاً نعيد تحميل الصفحة
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

function exportShipmentsData() {
    console.log('📤 تصدير بيانات الشحنات...');
    showNotification('جاري تحضير ملف التصدير...', 'info');
    
    // استدعاء وظيفة التصدير الموجودة
    if (typeof exportShipments === 'function') {
        exportShipments();
    } else {
        showNotification('وظيفة التصدير غير متاحة حالياً', 'warning');
    }
}

function showAdvancedFilters() {
    console.log('🔍 إظهار المرشحات المتقدمة...');
    showNotification('المرشحات المتقدمة قيد التطوير', 'info');
}

function showQuickSearch() {
    console.log('🔎 إظهار البحث السريع...');
    
    // التركيز على حقل البحث الموجود
    const searchInput = document.getElementById('quickSearch');
    if (searchInput) {
        searchInput.focus();
        showNotification('تم التركيز على حقل البحث', 'success');
    } else {
        showNotification('حقل البحث غير متاح', 'warning');
    }
}

function showNotifications() {
    console.log('🔔 إظهار الإشعارات...');
    showNotification('نظام الإشعارات قيد التطوير', 'info');
}

function showDashboardSettings() {
    console.log('⚙️ إظهار إعدادات اللوحة...');
    showNotification('إعدادات اللوحة قيد التطوير', 'info');
}

// وظيفة إظهار الإشعارات
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification-toast`;
    notification.style.cssText = `
        position: fixed;
        top: 80px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideInRight 0.3s ease;
    `;
    notification.innerHTML = `
        <i class="fas fa-${getNotificationIcon(type)} me-2"></i>
        ${message}
    `;
    
    document.body.appendChild(notification);
    
    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function getNotificationIcon(type) {
    const icons = {
        'success': 'check-circle',
        'info': 'info-circle',
        'warning': 'exclamation-triangle',
        'danger': 'times-circle'
    };
    return icons[type] || 'info-circle';
}

// إضافة CSS للإشعارات
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(notificationStyles);
