#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Routes للعقود - Contract Routes
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, send_file, current_app
from flask_login import login_required, current_user
from oracle_manager import get_oracle_manager
import logging
from datetime import datetime
import os
from werkzeug.utils import secure_filename
import uuid
from typing import Optional

# استيراد مدير الروابط السحابية من الشحنات
try:
    from app.shipments.cloud_link_manager import cloud_link_manager
    CLOUD_SERVICES_AVAILABLE = True
except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.warning(f"⚠️ مدير الروابط السحابية غير متوفر: {e}")
    CLOUD_SERVICES_AVAILABLE = False
    cloud_link_manager = None

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

# استخدام Blueprint من __init__.py
from app.contracts import bp as contracts

# ==================== دوال مساعدة ====================

def safe_convert_lob(value, default='غير محدد'):
    """تحويل LOB إلى string بشكل آمن"""
    if value is None:
        return default
    if hasattr(value, 'read'):
        try:
            return value.read().decode('utf-8') if value else default
        except:
            return default
    return str(value) if value else default

# ==================== الصفحات الرئيسية ====================

@contracts.route('/test')
@login_required
def test_contract():
    """صفحة اختبار الجدول"""
    return render_template('contracts/test.html')

@contracts.route('/new_complete')
@login_required
def new_contract_complete():
    """صفحة إضافة عقد جديد - كاملة"""
    today = datetime.now().strftime('%Y-%m-%d')
    return render_template('contracts/new_complete.html', today=today)

@contracts.route('/new_excel')
@login_required
def new_contract_excel():
    """صفحة إضافة عقد جديد - مع مكون Excel"""
    today = datetime.now().strftime('%Y-%m-%d')
    return render_template('contracts/new_excel.html', today=today)



@contracts.route('/view/')
@contracts.route('/view/<int:contract_id>')
@login_required
def view_contract_details(contract_id=None):
    """عرض تفاصيل العقد"""
    try:
        oracle_manager = get_oracle_manager()

        # إذا لم يتم تحديد رقم العقد، اجلب أول عقد موجود
        if contract_id is None:
            first_contract_query = "SELECT CONTRACT_ID FROM CONTRACTS ORDER BY CONTRACT_ID FETCH FIRST 1 ROWS ONLY"
            first_result = oracle_manager.execute_query(first_contract_query, [])
            if not first_result:
                flash('لا توجد عقود في النظام', 'error')
                return redirect(url_for('contracts.index'))
            contract_id = first_result[0][0]

        # جلب بيانات العقد الحقيقية
        contract_query = """
        SELECT CONTRACT_ID, BRANCH_ID, CONTRACT_DATE, START_DATE, END_DATE,
               SUPPLIER_ID, SUPPLIER_NAME, CURRENCY_CODE, EXCHANGE_RATE,
               REFERENCE_NUMBER, CONTRACT_AMOUNT, DESCRIPTION, CREATED_AT, IS_USED, CONTRACT_STATUS
        FROM CONTRACTS
        WHERE CONTRACT_ID = :1
        """

        contract_result = oracle_manager.execute_query(contract_query, [contract_id])

        if not contract_result:
            flash('العقد غير موجود', 'error')
            return redirect(url_for('contracts.index'))

        # إصلاح: تبديل supplier_id و supplier_name لأن البيانات معكوسة في قاعدة البيانات
        original_contract = contract_result[0]
        contract = list(original_contract)
        contract[5], contract[6] = contract[6], contract[5]  # تبديل supplier_id مع supplier_name

        # جلب تفاصيل أصناف العقد الحقيقية
        details_query = """
        SELECT ITEM_ID, ITEM_NAME, QUANTITY, EXECUTED_QUANTITY, REMAINING_QUANTITY,
               FREE_QUANTITY, UNIT_NAME, UNIT_PRICE, DISCOUNT_PERCENTAGE, TAX_AMOUNT,
               LINE_TOTAL, PRODUCTION_DATE, EXPIRY_DATE
        FROM CONTRACT_DETAILS
        WHERE CONTRACT_ID = :1
        ORDER BY DETAIL_ID
        """

        details = oracle_manager.execute_query(details_query, [contract_id])

        return render_template('contracts/view.html',
                             contract=contract,
                             details=details)

    except Exception as e:
        logger.error(f"❌ خطأ في عرض العقد {contract_id}: {e}")
        flash(f'خطأ في عرض العقد: {str(e)}', 'error')
        return redirect(url_for('contracts.index'))

@contracts.route('/new')
@login_required
def new():
    """صفحة إضافة عقد جديد"""
    try:
        oracle_manager = get_oracle_manager()

        # جلب قائمة العملات النشطة من قاعدة البيانات
        currencies_query = """
        SELECT id, code, name_ar, name_en, symbol, exchange_rate, is_base_currency
        FROM currencies
        WHERE is_active = 1
        ORDER BY is_base_currency DESC, name_ar
        """
        currencies_data = oracle_manager.execute_query(currencies_query, [])

        # تحويل البيانات إلى قائمة مناسبة للنموذج
        currencies = []
        if currencies_data:
            for currency in currencies_data:
                currencies.append({
                    'id': currency[0],
                    'code': currency[1],
                    'name_ar': currency[2],
                    'name_en': currency[3],
                    'symbol': currency[4],
                    'exchange_rate': float(currency[5]) if currency[5] else 1.0,
                    'is_base_currency': bool(currency[6])
                })

        # إذا لم توجد عملات، استخدم القائمة الافتراضية
        if not currencies:
            currencies = [
                {'code': 'SAR', 'name_ar': 'ريال سعودي', 'symbol': 'ر.س', 'exchange_rate': 1.0, 'is_base_currency': True},
                {'code': 'USD', 'name_ar': 'دولار أمريكي', 'symbol': '$', 'exchange_rate': 3.75, 'is_base_currency': False},
                {'code': 'EUR', 'name_ar': 'يورو', 'symbol': '€', 'exchange_rate': 4.1, 'is_base_currency': False}
            ]

        logger.info(f"✅ تم جلب {len(currencies)} عملة للنموذج")

        return render_template('contracts/new.html', currencies=currencies)
    except Exception as e:
        logger.error(f"خطأ في عرض صفحة إضافة العقد: {e}")
        flash(f'خطأ في عرض الصفحة: {str(e)}', 'error')
        return redirect(url_for('contracts.index'))

@contracts.route('/api/contracts/create-with-table', methods=['POST'])
@login_required
def create_contract_with_table():
    """إنشاء عقد جديد مع بيانات جدول Handsontable"""
    try:
        # طباعة جميع البيانات المستلمة للتشخيص
        logger.info("📥 جميع البيانات المستلمة من النموذج:")
        for key, value in request.form.items():
            logger.info(f"  {key}: {value}")

        # الحصول على البيانات من النموذج
        contract_data = {
            'branch_id': request.form.get('branch_id'),
            'contract_date': request.form.get('contract_date'),
            'start_date': request.form.get('start_date'),
            'end_date': request.form.get('end_date'),
            'supplier_id': request.form.get('supplier_id'),
            'supplier_name': request.form.get('name_ar'),  # اسم المورد من حقل name_ar
            'currency_code': request.form.get('currency_code', 'SAR'),
            'exchange_rate': request.form.get('exchange_rate', 1),
            'reference_number': request.form.get('reference_number', ''),
            'description': request.form.get('description', ''),
            'contract_amount': request.form.get('contract_amount', 0),
            'discount_amount': request.form.get('discount_amount', 0),
            'net_amount': request.form.get('net_amount', 0),
            'contract_status': 'DRAFT'  # دائماً استخدم DRAFT للعقود الجديدة
        }

        # التحقق من البيانات المطلوبة
        if not contract_data['supplier_id']:
            logger.error("❌ supplier_id مفقود!")
            flash('يرجى اختيار المورد', 'error')
            return redirect(url_for('contracts.new'))

        if not contract_data['supplier_name']:
            logger.error("❌ supplier_name مفقود!")
            flash('يرجى اختيار المورد', 'error')
            return redirect(url_for('contracts.new'))

        # الحصول على بيانات الجدول
        table_data_json = request.form.get('table_data')
        table_data = []

        logger.info(f"📥 table_data_json من النموذج: {table_data_json}")

        if table_data_json:
            import json
            try:
                table_data = json.loads(table_data_json)
                logger.info(f"✅ تم تحليل JSON بنجاح: {len(table_data)} صنف")
                for i, item in enumerate(table_data):
                    logger.info(f"  الصنف {i+1}: {item}")
            except Exception as json_error:
                logger.error(f"❌ خطأ في تحليل JSON: {json_error}")
                table_data = []
        else:
            logger.warning("⚠️ table_data_json فارغ أو غير موجود")

            # محاولة جمع البيانات مباشرة من الحقول كـ backup
            logger.info("🔄 محاولة جمع البيانات مباشرة من الحقول...")
            table_data = []

            # البحث عن جميع حقول الأصناف
            item_code_keys = [key for key in request.form.keys() if key.startswith('item_code_')]
            logger.info(f"🔍 وجدت {len(item_code_keys)} حقل كود صنف: {item_code_keys}")

            for key in item_code_keys:
                row_number = key.replace('item_code_', '')
                item_code = request.form.get(f'item_code_{row_number}', '').strip()

                if item_code:  # إذا كان هناك كود صنف
                    item_data = {
                        'item_code': item_code,
                        'item_name': request.form.get(f'item_name_{row_number}', ''),
                        'unit_name': request.form.get(f'unit_{row_number}', 'قطعة'),
                        'quantity': float(request.form.get(f'quantity_{row_number}', 0) or 0),
                        'free_quantity': float(request.form.get(f'free_quantity_{row_number}', 0) or 0),
                        'unit_price': float(request.form.get(f'unit_price_{row_number}', 0) or 0),
                        'discount_percentage': float(request.form.get(f'discount_percentage_{row_number}', 0) or 0),
                        'tax_amount': float(request.form.get(f'tax_amount_{row_number}', 0) or 0),
                        'line_total': float(request.form.get(f'line_total_{row_number}', 0) or 0),
                        'production_date': request.form.get(f'production_date_{row_number}', ''),
                        'expiry_date': request.form.get(f'expiry_date_{row_number}', '')
                    }
                    table_data.append(item_data)
                    logger.info(f"✅ تم جمع بيانات الصنف {item_code}: {item_data}")

            logger.info(f"📊 تم جمع {len(table_data)} صنف من الحقول مباشرة")

        logger.info(f"🔄 إنشاء عقد جديد مع {len(table_data)} صنف")
        # تحويل حالة العقد إلى القيم المقبولة
        status_mapping = {
            'مسودة': 'DRAFT',
            'معتمد': 'APPROVED',
            'منفذ جزئياً': 'PARTIALLY_EXECUTED',
            'منفذ كلياً': 'FULLY_EXECUTED'
        }

        if contract_data['contract_status'] in status_mapping:
            contract_data['contract_status'] = status_mapping[contract_data['contract_status']]
        elif contract_data['contract_status'] not in ['DRAFT', 'APPROVED', 'PARTIALLY_EXECUTED', 'FULLY_EXECUTED']:
            contract_data['contract_status'] = 'DRAFT'  # القيمة الافتراضية

        logger.info(f"📋 بيانات العقد: {contract_data}")
        logger.info(f"📦 بيانات الجدول: {table_data}")
        logger.info(f"📊 حالة العقد بعد التحويل: {contract_data['contract_status']}")

        oracle_mgr = get_oracle_manager()

        # التحقق من وجود الجداول
        check_contracts_table = "SELECT table_name FROM user_tables WHERE table_name = 'CONTRACTS'"
        contracts_table_exists = oracle_mgr.execute_query(check_contracts_table)
        if not contracts_table_exists:
            logger.error("❌ جدول CONTRACTS غير موجود!")
            flash('خطأ: جدول العقود غير موجود في قاعدة البيانات', 'error')
            return redirect(url_for('contracts.new'))

        check_details_table = "SELECT table_name FROM user_tables WHERE table_name = 'CONTRACT_DETAILS'"
        details_table_exists = oracle_mgr.execute_query(check_details_table)
        if not details_table_exists:
            logger.error("❌ جدول CONTRACT_DETAILS غير موجود!")
            flash('خطأ: جدول تفاصيل العقود غير موجود في قاعدة البيانات', 'error')
            return redirect(url_for('contracts.new'))

        # فحص أعمدة جدول CONTRACT_DETAILS
        columns_sql = """
        SELECT column_name, data_type, nullable
        FROM user_tab_columns
        WHERE table_name = 'CONTRACT_DETAILS'
        ORDER BY column_id
        """
        columns_result = oracle_mgr.execute_query(columns_sql)
        logger.info("📋 أعمدة جدول CONTRACT_DETAILS:")
        for col in columns_result:
            logger.info(f"  - {col[0]} ({col[1]}) {'NULL' if col[2] == 'Y' else 'NOT NULL'}")

        logger.info("✅ جميع الجداول موجودة")

        # حساب إجمالي العقد
        total_amount = sum(float(item.get('line_total', 0)) for item in table_data)
        net_amount = total_amount - float(contract_data.get('discount_amount', 0))

        logger.info(f"💰 إجمالي العقد: {total_amount}")
        logger.info(f"💰 صافي العقد: {net_amount}")

        # التحقق من البيانات الأساسية
        if not contract_data.get('branch_id'):
            logger.error("❌ branch_id مفقود!")
            flash('يرجى اختيار الفرع', 'error')
            return redirect(url_for('contracts.new'))

        if not contract_data.get('contract_date'):
            logger.error("❌ contract_date مفقود!")
            flash('يرجى تحديد تاريخ العقد', 'error')
            return redirect(url_for('contracts.new'))

        # التحقق من وجود sequence وإعادة تعيينه
        check_seq_sql = "SELECT sequence_name FROM user_sequences WHERE sequence_name = 'CONTRACTS_SEQ'"
        seq_check = oracle_mgr.execute_query(check_seq_sql)

        if not seq_check:
            logger.error("❌ CONTRACTS_SEQ غير موجود! إنشاء sequence...")
            # الحصول على أكبر contract_id موجود
            max_id_sql = "SELECT NVL(MAX(contract_id), 0) + 1 FROM contracts"
            max_id_result = oracle_mgr.execute_query(max_id_sql)
            start_value = max_id_result[0][0] if max_id_result else 1

            create_seq_sql = f"CREATE SEQUENCE contracts_seq START WITH {start_value} INCREMENT BY 1"
            oracle_mgr.execute_update(create_seq_sql)
            logger.info(f"✅ تم إنشاء CONTRACTS_SEQ بدءاً من {start_value}")
        else:
            # التحقق من أن الـ sequence متزامن مع الجدول
            max_id_sql = "SELECT NVL(MAX(contract_id), 0) FROM contracts"
            max_id_result = oracle_mgr.execute_query(max_id_sql)
            max_existing_id = max_id_result[0][0] if max_id_result else 0

            logger.info(f"🔍 أكبر contract_id موجود: {max_existing_id}")

            # دائماً أعد إنشاء الـ sequence لضمان التزامن
            new_start_value = max_existing_id + 1
            logger.info(f"🔄 إعادة تعيين sequence إلى {new_start_value}")

            # حذف وإعادة إنشاء الـ sequence
            try:
                oracle_mgr.execute_update("DROP SEQUENCE contracts_seq")
            except:
                pass  # قد لا يكون موجوداً

            create_seq_sql = f"CREATE SEQUENCE contracts_seq START WITH {new_start_value} INCREMENT BY 1"
            oracle_mgr.execute_update(create_seq_sql)
            logger.info(f"✅ تم إعادة إنشاء CONTRACTS_SEQ بدءاً من {new_start_value}")

        # الحصول على معرف العقد الجديد أولاً
        contract_id_sql = "SELECT contracts_seq.NEXTVAL FROM DUAL"
        contract_id_result = oracle_mgr.execute_query(contract_id_sql)
        contract_id = contract_id_result[0][0]
        logger.info(f"🆔 معرف العقد الجديد: {contract_id}")

        # إنشاء العقد الرئيسي
        # أولاً: فحص الأعمدة الموجودة في جدول contracts
        columns_check_sql = """
        SELECT column_name
        FROM user_tab_columns
        WHERE table_name = 'CONTRACTS'
        AND column_name IN ('DISCOUNT_AMOUNT', 'CONTRACT_DISCOUNT', 'NET_AMOUNT')
        ORDER BY column_name
        """

        existing_columns = oracle_mgr.execute_query(columns_check_sql)
        logger.info(f"🔍 الأعمدة الموجودة للخصم والصافي: {existing_columns}")

        # تحديد أسماء الأعمدة الصحيحة
        discount_column = 'CONTRACT_DISCOUNT'  # الافتراضي
        net_column = 'NET_AMOUNT'  # الافتراضي

        if existing_columns:
            for col in existing_columns:
                col_name = col[0]
                if 'DISCOUNT' in col_name:
                    discount_column = col_name
                    logger.info(f"✅ استخدام عمود الخصم: {discount_column}")
                if 'NET' in col_name:
                    net_column = col_name
                    logger.info(f"✅ استخدام عمود الصافي: {net_column}")

        contract_sql = f"""
        INSERT INTO contracts (
            contract_id, branch_id, contract_date, start_date, end_date, supplier_id, supplier_name,
            currency_code, exchange_rate, reference_number, contract_amount,
            description, created_at, updated_at, contract_status, {discount_column}, {net_column}
        ) VALUES (
            :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13, :14, :15, :16, :17
        )
        """

        # حساب القيم من البيانات المرسلة
        contract_amount = float(contract_data.get('contract_amount', 0))
        discount_amount = float(contract_data.get('discount_amount', 0))
        net_amount = float(contract_data.get('net_amount', 0))

        logger.info(f"🔍 القيم المستلمة من النموذج:")
        logger.info(f"  - contract_amount: {contract_data.get('contract_amount')} -> {contract_amount}")
        logger.info(f"  - discount_amount: {contract_data.get('discount_amount')} -> {discount_amount}")
        logger.info(f"  - net_amount: {contract_data.get('net_amount')} -> {net_amount}")

        # إذا لم يتم حساب صافي المبلغ، احسبه
        if net_amount == 0 or net_amount == contract_amount:
            net_amount = contract_amount - discount_amount
            logger.info(f"🧮 تم حساب صافي المبلغ: {contract_amount} - {discount_amount} = {net_amount}")

        contract_params = [
            contract_id,
            contract_data['branch_id'],
            datetime.strptime(contract_data['contract_date'], '%Y-%m-%d').date(),
            datetime.strptime(contract_data['start_date'], '%Y-%m-%d').date(),
            datetime.strptime(contract_data['end_date'], '%Y-%m-%d').date(),
            contract_data['supplier_id'],    # حفظ كود المورد في supplier_id (الصحيح)
            contract_data['supplier_name'],  # حفظ اسم المورد في supplier_name (الصحيح)
            contract_data['currency_code'],
            float(contract_data['exchange_rate']),
            contract_data['reference_number'],
            contract_amount,                 # مبلغ العقد (قبل الخصم)
            contract_data['description'],
            datetime.now(),
            datetime.now(),
            contract_data['contract_status'],
            discount_amount,                 # مبلغ الخصم
            net_amount                       # صافي المبلغ (بعد الخصم)
        ]

        logger.info(f"💰 القيم المالية للعقد:")
        logger.info(f"  - مبلغ العقد: {contract_amount}")
        logger.info(f"  - مبلغ الخصم: {discount_amount}")
        logger.info(f"  - صافي المبلغ: {net_amount}")

        logger.info(f"📝 SQL: {contract_sql}")
        logger.info(f"📊 Parameters: {contract_params}")

        try:
            result = oracle_mgr.execute_update(contract_sql, contract_params)
            logger.info(f"📈 نتيجة إدراج العقد: {result}")

            if result == 0:
                logger.error("❌ لم يتم إدراج أي سجل في جدول العقود!")
                flash('خطأ: فشل في حفظ العقد', 'error')
                return redirect(url_for('contracts.new'))

        except Exception as contract_error:
            logger.error(f"❌ خطأ في إدراج العقد: {contract_error}")
            flash(f'خطأ في حفظ العقد: {str(contract_error)}', 'error')
            return redirect(url_for('contracts.new'))

        # التحقق من أن العقد تم حفظه فعلياً
        try:
            check_sql = "SELECT COUNT(*) FROM contracts WHERE contract_id = :1"
            check_result = oracle_mgr.execute_query(check_sql, [contract_id])
            if check_result and check_result[0][0] > 0:
                logger.info(f"✅ تم التحقق: العقد {contract_id} موجود في قاعدة البيانات")
            else:
                logger.error(f"❌ العقد {contract_id} غير موجود في قاعدة البيانات!")
                flash('خطأ: العقد لم يُحفظ في قاعدة البيانات', 'error')
                return redirect(url_for('contracts.new'))
        except Exception as check_error:
            logger.error(f"❌ خطأ في التحقق من العقد: {check_error}")

        # إدراج تفاصيل العقد
        logger.info(f"📦 بدء حفظ تفاصيل العقد...")
        logger.info(f"📊 عدد الأصناف في table_data: {len(table_data) if table_data else 0}")

        if table_data:
            detail_sql = """
            INSERT INTO contract_details (
                contract_id, item_id, item_name, quantity, executed_quantity,
                remaining_quantity, free_quantity, unit_name, unit_price,
                discount_percentage, tax_amount, line_total, production_date,
                expiry_date, created_at, updated_at
            ) VALUES (
                :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13, :14, :15, :16
            )
            """

            saved_items_count = 0
            for index, item in enumerate(table_data):
                logger.info(f"📦 معالجة الصنف {index + 1}:")
                logger.info(f"  - البيانات الكاملة: {item}")
                logger.info(f"  - تاريخ الإنتاج: {item.get('production_date')}")
                logger.info(f"  - تاريخ الانتهاء: {item.get('expiry_date')}")

                item_code = item.get('item_code', '').strip()
                item_name = item.get('item_name', '').strip()
                quantity = float(item.get('quantity', 0) or 0)

                if not item_code:
                    logger.warning(f"⚠️ الصنف {index + 1} بدون كود - تجاهل")
                    continue

                if quantity <= 0:
                    logger.warning(f"⚠️ الصنف {item_code} بكمية صفر أو سالبة - تجاهل")
                    continue

                logger.info(f"✅ الصنف {item_code} صالح للحفظ (الكمية: {quantity})")

                # تحضير البيانات للإدراج
                unit_price = float(item.get('unit_price', 0) or 0)
                line_total = float(item.get('line_total', 0) or 0)  # استخدام الإجمالي المحسوب

                # معالجة التواريخ
                production_date = item.get('production_date')
                expiry_date = item.get('expiry_date')

                # تحويل التواريخ إلى datetime إذا كانت موجودة
                production_date_obj = None
                expiry_date_obj = None

                if production_date and production_date.strip():
                    try:
                        production_date_obj = datetime.strptime(production_date, '%Y-%m-%d').date()
                        logger.info(f"📅 تاريخ الإنتاج للصنف {item_code}: {production_date_obj}")
                    except ValueError:
                        logger.warning(f"⚠️ تاريخ إنتاج غير صحيح للصنف {item_code}: {production_date}")

                if expiry_date and expiry_date.strip():
                    try:
                        expiry_date_obj = datetime.strptime(expiry_date, '%Y-%m-%d').date()
                        logger.info(f"📅 تاريخ الانتهاء للصنف {item_code}: {expiry_date_obj}")
                    except ValueError:
                        logger.warning(f"⚠️ تاريخ انتهاء غير صحيح للصنف {item_code}: {expiry_date}")

                detail_params = [
                        contract_id,                                    # :1
                        item_code,                                      # :2
                        item_name,                                      # :3
                        quantity,                                       # :4
                        0,                                              # :5 executed_quantity
                        quantity,                                       # :6 remaining_quantity
                        float(item.get('free_quantity', 0) or 0),      # :7
                        item.get('unit_name', 'قطعة'),                 # :8
                        unit_price,                                     # :9
                        float(item.get('discount_percentage', 0) or 0), # :10
                        0,                                              # :11 tax_amount (مؤقتاً)
                        line_total,                                     # :12
                        production_date_obj,                            # :13 production_date
                        expiry_date_obj,                                # :14 expiry_date
                        datetime.now(),                                 # :15 created_at
                        datetime.now()                                  # :16 updated_at
                ]

                logger.info(f"📋 معاملات الإدراج للصنف {item_code}:")
                logger.info(f"  contract_id: {contract_id}")
                logger.info(f"  item_code: {item_code}")
                logger.info(f"  item_name: {item_name}")
                logger.info(f"  quantity: {quantity}")
                logger.info(f"  unit_price: {unit_price}")
                logger.info(f"  line_total: {line_total}")

                try:
                    logger.info(f"📝 SQL التفاصيل: {detail_sql}")
                    logger.info(f"📊 معاملات التفاصيل: {detail_params}")

                    detail_result = oracle_mgr.execute_update(detail_sql, detail_params)
                    logger.info(f"📦 نتيجة إدراج تفصيل الصنف {item_code}: {detail_result}")

                    if detail_result > 0:
                        saved_items_count += 1
                        logger.info(f"✅ تم حفظ الصنف {item_code} بنجاح")

                        # التحقق الفوري من وجود السجل
                        immediate_check_sql = "SELECT COUNT(*) FROM contract_details WHERE contract_id = :1 AND item_id = :2"
                        immediate_check = oracle_mgr.execute_query(immediate_check_sql, [contract_id, item_code])
                        if immediate_check and immediate_check[0][0] > 0:
                            logger.info(f"✅ تأكيد: الصنف {item_code} موجود في قاعدة البيانات")
                        else:
                            logger.error(f"❌ الصنف {item_code} غير موجود في قاعدة البيانات رغم نجاح الإدراج!")
                    else:
                        logger.error(f"❌ فشل في حفظ الصنف {item_code}")

                except Exception as detail_error:
                    logger.error(f"❌ خطأ في حفظ تفصيل الصنف {item_code}: {detail_error}")
                    logger.error(f"❌ تفاصيل الخطأ: {str(detail_error)}")

            logger.info(f"📊 تم حفظ {saved_items_count} من {len(table_data)} صنف")
        else:
            logger.warning("⚠️ لا توجد بيانات جدول لحفظها")

        # التحقق النهائي من تفاصيل العقد
        logger.info("🔍 التحقق النهائي من تفاصيل العقد...")
        details_check_sql = "SELECT COUNT(*) FROM contract_details WHERE contract_id = :1"
        details_check_result = oracle_mgr.execute_query(details_check_sql, [contract_id])
        if details_check_result:
            details_count = details_check_result[0][0]
            logger.info(f"✅ تم التحقق: العقد {contract_id} يحتوي على {details_count} تفصيل")

            if details_count == 0 and saved_items_count > 0:
                logger.error(f"❌ مشكلة خطيرة: تم حفظ {saved_items_count} صنف لكن العدد في قاعدة البيانات = 0!")
                logger.error("❌ قد تكون مشكلة في commit أو rollback")

                # محاولة commit يدوي
                try:
                    oracle_mgr.connection.commit()
                    logger.info("🔄 تم تنفيذ commit يدوي")

                    # إعادة التحقق
                    recheck_result = oracle_mgr.execute_query(details_check_sql, [contract_id])
                    if recheck_result:
                        recheck_count = recheck_result[0][0]
                        logger.info(f"🔍 بعد commit اليدوي: العقد {contract_id} يحتوي على {recheck_count} تفصيل")
                except Exception as commit_error:
                    logger.error(f"❌ خطأ في commit اليدوي: {commit_error}")

        logger.info(f"✅ تم إنشاء العقد {contract_id} بنجاح")

        flash(f'تم إنشاء العقد رقم {contract_id} بنجاح', 'success')
        return redirect(url_for('contracts.index'))

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء العقد: {e}")
        flash(f'خطأ في إنشاء العقد: {str(e)}', 'error')
        return redirect(url_for('contracts.new'))



@contracts.route('/')
@login_required
def index():
    """صفحة قائمة العقود"""
    try:
        oracle_mgr = get_oracle_manager()

        # استعلام للعقود مع حالة الاستخدام والعملة
        query = """
        SELECT c.CONTRACT_ID, c.CONTRACT_NUMBER, c.CONTRACT_DATE, c.START_DATE, c.END_DATE,
               c.SUPPLIER_NAME, c.CONTRACT_AMOUNT, c.CONTRACT_STATUS, c.IS_USED,
               c.CURRENCY_CODE, c.EXCHANGE_RATE, curr.SYMBOL, curr.NAME_AR
        FROM contracts c
        LEFT JOIN currencies curr ON c.CURRENCY_CODE = curr.CODE
        ORDER BY c.CONTRACT_ID DESC
        """

        contracts_data = oracle_mgr.execute_query(query)
        contracts_list = []

        if contracts_data:
            for contract_data in contracts_data:
                contract_dict = {
                    'contract_id': contract_data[0],
                    'contract_number': contract_data[1] if contract_data[1] else f"عقد-{contract_data[0]}",
                    'contract_date': contract_data[2],
                    'start_date': contract_data[3],
                    'end_date': contract_data[4],
                    'supplier_name': contract_data[5] if contract_data[5] else 'غير محدد',
                    'contract_amount': contract_data[6] if contract_data[6] else 0,
                    'contract_status': contract_data[7] if contract_data[7] else 'DRAFT',
                    'is_used': contract_data[8] if len(contract_data) > 8 else 0,
                    'currency_code': contract_data[9] if len(contract_data) > 9 and contract_data[9] else 'SAR',
                    'exchange_rate': float(contract_data[10]) if len(contract_data) > 10 and contract_data[10] else 1.0,
                    'currency_symbol': contract_data[11] if len(contract_data) > 11 and contract_data[11] else 'ر.س',
                    'currency_name': contract_data[12] if len(contract_data) > 12 and contract_data[12] else 'ريال سعودي'
                }
                contracts_list.append(contract_dict)

        return render_template('contracts/index.html', contracts=contracts_list)

    except Exception as e:
        logger.error(f"خطأ في جلب العقود: {e}")
        flash(f'خطأ في جلب العقود: {str(e)}', 'error')
        return render_template('contracts/index.html', contracts=[])





@contracts.route('/<int:contract_id>/edit')
@login_required
def edit_contract(contract_id):
    """صفحة تعديل العقد"""
    try:
        oracle_mgr = get_oracle_manager()

        # جلب بيانات العقد
        contract_sql = """
        SELECT contract_id, branch_id, contract_date, start_date, end_date,
               supplier_id, supplier_name, currency_code, exchange_rate,
               reference_number, contract_amount, description, created_at, is_used, contract_status
        FROM contracts
        WHERE contract_id = :1
        """
        contract_result = oracle_mgr.execute_query(contract_sql, [contract_id])

        if not contract_result:
            flash('العقد غير موجود', 'error')
            return redirect(url_for('contracts.index'))

        contract = contract_result[0]

        # فحص إذا كان العقد مستخدماً في أوامر الشراء
        if contract[13] == 1:  # IS_USED = 1
            flash('⚠️ لا يمكن تعديل هذا العقد لأنه مُستخدم في أوامر الشراء. يرجى إلغاء ربطه بأوامر الشراء أولاً.', 'warning')
            return redirect(url_for('contracts.index'))

        # تحويل التواريخ إلى تنسيق مناسب للـ HTML
        # إصلاح: البيانات معكوسة في قاعدة البيانات - supplier_id يحتوي على الاسم و supplier_name يحتوي على الكود
        contract_data = {
            'contract_id': contract[0],
            'branch_id': contract[1],
            'contract_date': contract[2].strftime('%Y-%m-%d') if contract[2] and hasattr(contract[2], 'strftime') else (str(contract[2])[:10] if contract[2] else ''),
            'start_date': contract[3].strftime('%Y-%m-%d') if contract[3] and hasattr(contract[3], 'strftime') else (str(contract[3])[:10] if contract[3] else ''),
            'end_date': contract[4].strftime('%Y-%m-%d') if contract[4] and hasattr(contract[4], 'strftime') else (str(contract[4])[:10] if contract[4] else ''),
            'supplier_id': contract[6],      # تبديل: استخدام supplier_name كـ supplier_id (الكود)
            'supplier_name': contract[5],    # تبديل: استخدام supplier_id كـ supplier_name (الاسم)
            'currency_code': contract[7],
            'exchange_rate': contract[8],
            'reference_number': contract[9] or '',
            'contract_amount': contract[10],
            'description': contract[11] or '',
            'created_at': contract[12],
            'is_used': contract[13]
        }

        logger.info(f"📊 بيانات العقد المرسلة للتعديل:")
        logger.info(f"  - contract_id: {contract_data['contract_id']}")
        logger.info(f"  - branch_id: {contract_data['branch_id']}")
        logger.info(f"  - supplier_id: {contract_data['supplier_id']}")
        logger.info(f"  - supplier_name: {contract_data['supplier_name']}")
        logger.info(f"  - contract_amount: {contract_data['contract_amount']}")
        logger.info(f"  - contract_date: {contract_data['contract_date']}")

        # جلب تفاصيل العقد
        details_sql = """
        SELECT detail_id, item_id, item_name, quantity, executed_quantity, remaining_quantity,
               free_quantity, unit_name, unit_price, discount_percentage, tax_amount,
               line_total, production_date, expiry_date
        FROM contract_details
        WHERE contract_id = :1
        ORDER BY detail_id
        """
        details_result = oracle_mgr.execute_query(details_sql, [contract_id])

        # معالجة التواريخ في التفاصيل
        processed_details = []
        if details_result:
            for detail in details_result:
                detail_list = list(detail)
                # معالجة تاريخ الإنتاج (index 12)
                if detail_list[12] and hasattr(detail_list[12], 'strftime'):
                    detail_list[12] = detail_list[12].strftime('%Y-%m-%d')
                elif detail_list[12]:
                    detail_list[12] = str(detail_list[12])[:10]

                # معالجة تاريخ الانتهاء (index 13)
                if detail_list[13] and hasattr(detail_list[13], 'strftime'):
                    detail_list[13] = detail_list[13].strftime('%Y-%m-%d')
                elif detail_list[13]:
                    detail_list[13] = str(detail_list[13])[:10]

                processed_details.append(detail_list)

        # جلب قائمة الفروع للتعديل
        branches_sql = "SELECT branch_id, branch_name FROM branches WHERE is_active = 1 ORDER BY branch_name"
        branches = oracle_mgr.execute_query(branches_sql)

        # جلب قائمة العملات النشطة
        currencies_query = """
        SELECT id, code, name_ar, name_en, symbol, exchange_rate, is_base_currency
        FROM currencies
        WHERE is_active = 1
        ORDER BY is_base_currency DESC, name_ar
        """
        currencies_data = oracle_mgr.execute_query(currencies_query, [])

        # تحويل البيانات إلى قائمة مناسبة للنموذج
        currencies = []
        if currencies_data:
            for currency in currencies_data:
                currencies.append({
                    'id': currency[0],
                    'code': currency[1],
                    'name_ar': currency[2],
                    'name_en': currency[3],
                    'symbol': currency[4],
                    'exchange_rate': float(currency[5]) if currency[5] else 1.0,
                    'is_base_currency': bool(currency[6])
                })

        # إذا لم توجد عملات، استخدم القائمة الافتراضية
        if not currencies:
            currencies = [
                {'code': 'SAR', 'name_ar': 'ريال سعودي', 'symbol': 'ر.س', 'exchange_rate': 1.0, 'is_base_currency': True},
                {'code': 'USD', 'name_ar': 'دولار أمريكي', 'symbol': '$', 'exchange_rate': 3.75, 'is_base_currency': False},
                {'code': 'EUR', 'name_ar': 'يورو', 'symbol': '€', 'exchange_rate': 4.1, 'is_base_currency': False}
            ]

        return render_template('contracts/new.html',
                             contract=contract_data,
                             details=processed_details,
                             branches=branches,
                             currencies=currencies,
                             edit_mode=True)

    except Exception as e:
        logger.error(f"خطأ في تحميل صفحة تعديل العقد {contract_id}: {e}")
        flash(f'خطأ في تحميل صفحة التعديل: {str(e)}', 'error')
        return redirect(url_for('contracts.index'))

# ==================== API Routes ====================

@contracts.route('/api/currencies', methods=['GET'])
@login_required
def get_currencies():
    """API لجلب قائمة العملات النشطة"""
    try:
        oracle_manager = get_oracle_manager()

        # جلب العملات النشطة
        currencies_query = """
        SELECT id, code, name_ar, name_en, symbol, exchange_rate, is_base_currency
        FROM currencies
        WHERE is_active = 1
        ORDER BY is_base_currency DESC, name_ar
        """
        currencies_data = oracle_manager.execute_query(currencies_query, [])

        currencies = []
        if currencies_data:
            for currency in currencies_data:
                currencies.append({
                    'id': currency[0],
                    'code': currency[1],
                    'name_ar': currency[2],
                    'name_en': currency[3],
                    'symbol': currency[4],
                    'exchange_rate': float(currency[5]) if currency[5] else 1.0,
                    'is_base_currency': bool(currency[6])
                })

        return jsonify({
            'success': True,
            'currencies': currencies
        })

    except Exception as e:
        logger.error(f"خطأ في جلب العملات: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@contracts.route('/api/contract-statuses', methods=['GET'])
@login_required
def get_contract_statuses():
    """API لجلب حالات العقود"""
    try:
        logger.info("🔍 بدء جلب حالات العقود...")
        oracle_manager = get_oracle_manager()

        # جلب جميع الحالات النشطة
        query = """
        SELECT
            STATUS_ID,
            STATUS_CODE,
            STATUS_NAME_AR,
            STATUS_NAME_EN,
            STATUS_DESCRIPTION,
            STATUS_COLOR,
            STATUS_ICON,
            SORT_ORDER
        FROM CONTRACT_STATUSES
        WHERE IS_ACTIVE = 1
        ORDER BY SORT_ORDER
        """

        statuses_data = oracle_manager.execute_query(query)
        logger.info(f"📊 تم جلب {len(statuses_data) if statuses_data else 0} حالة")

        statuses_list = []
        if statuses_data:
            for status in statuses_data:
                try:
                    status_dict = {
                        'status_id': int(status[0]) if status[0] is not None else None,
                        'status_code': str(status[1]) if status[1] else '',
                        'status_name_ar': str(status[2]) if status[2] else '',
                        'status_name_en': str(status[3]) if status[3] else '',
                        'status_description': str(status[4]) if status[4] else '',
                        'status_color': str(status[5]) if status[5] else '#6c757d',
                        'status_icon': str(status[6]) if status[6] else 'fas fa-circle',
                        'sort_order': int(status[7]) if status[7] is not None else 0
                    }
                    statuses_list.append(status_dict)
                    logger.info(f"✅ تم تحويل الحالة: {status_dict['status_name_ar']}")
                except Exception as e:
                    logger.error(f"خطأ في تحويل بيانات الحالة: {e}")
                    continue

        logger.info(f"✅ تم تحويل {len(statuses_list)} حالة بنجاح")

        response = {
            'success': True,
            'statuses': statuses_list,
            'total': len(statuses_list)
        }

        logger.info("📤 إرسال استجابة حالات العقود...")
        return jsonify(response)

    except Exception as e:
        logger.error(f"❌ خطأ في جلب حالات العقود: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

@contracts.route('/api/contracts', methods=['GET'])
@login_required
def get_contracts():
    """الحصول على قائمة العقود"""
    try:
        logger.info("🔍 بدء جلب العقود...")
        oracle_mgr = get_oracle_manager()
        
        # استعلام للحصول على جميع العقود من قاعدة البيانات الحقيقية مع معلومات العملة
        query = """
        SELECT c.contract_id, c.contract_number, c.contract_date, c.start_date, c.end_date,
               c.supplier_id, c.supplier_name, c.contract_amount, c.is_used, c.is_active,
               c.created_at, c.contract_status, c.currency_code, c.exchange_rate,
               curr.symbol, curr.name_ar
        FROM contracts c
        LEFT JOIN currencies curr ON c.currency_code = curr.code
        ORDER BY c.contract_id DESC
        """

        logger.info("📊 تنفيذ استعلام العقود...")
        contracts_data = oracle_mgr.execute_query(query)
        logger.info(f"📋 تم العثور على {len(contracts_data) if contracts_data else 0} عقد")
        
        # تحويل البيانات إلى قائمة
        contracts_list = []
        for contract_data in contracts_data:
            try:
                contract_dict = {
                    'contract_id': int(contract_data[0]) if contract_data[0] else 0,
                    'contract_number': str(contract_data[1]) if contract_data[1] else '',
                    'contract_date': str(contract_data[2]) if contract_data[2] else '',
                    'start_date': str(contract_data[3]) if contract_data[3] else '',
                    'end_date': str(contract_data[4]) if contract_data[4] else '',
                    'supplier_id': int(contract_data[5]) if contract_data[5] else 0,
                    'supplier_name': str(contract_data[6]) if contract_data[6] else '',
                    'contract_amount': float(contract_data[7]) if contract_data[7] else 0.0,
                    'is_used': bool(int(contract_data[8])) if contract_data[8] is not None else False,
                    'is_active': bool(int(contract_data[9])) if contract_data[9] is not None else True,
                    'created_at': str(contract_data[10]) if contract_data[10] else '',
                    'contract_status': str(contract_data[11]) if contract_data[11] else 'DRAFT',
                    'currency_code': str(contract_data[12]) if len(contract_data) > 12 and contract_data[12] else 'SAR',
                    'exchange_rate': float(contract_data[13]) if len(contract_data) > 13 and contract_data[13] else 1.0,
                    'currency_symbol': str(contract_data[14]) if len(contract_data) > 14 and contract_data[14] else 'ر.س',
                    'currency_name': str(contract_data[15]) if len(contract_data) > 15 and contract_data[15] else 'ريال سعودي'
                }
                contracts_list.append(contract_dict)
            except Exception as e:
                logger.error(f"خطأ في تحويل بيانات العقد: {e}")
                continue
        
        logger.info(f"✅ تم تحويل {len(contracts_list)} عقد بنجاح")
        
        response = {
            'success': True,
            'contracts': contracts_list,
            'total': len(contracts_list)
        }
        
        logger.info("📤 إرسال الاستجابة...")
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"❌ خطأ في جلب العقود: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

@contracts.route('/api/branches', methods=['GET'])
@login_required
def get_branches():
    """الحصول على قائمة الفروع"""
    try:
        oracle_mgr = get_oracle_manager()
        
        query = """
        SELECT BRN_NO, BRN_LNAME, BRN_CODE, DBLINK_NAME
        FROM branches
        WHERE IS_ACTIVE = 1
        ORDER BY BRN_LNAME
        """
        
        branches_data = oracle_mgr.execute_query(query)
        
        branches = []
        for branch_data in branches_data:
            branch_dict = {
                'branch_id': int(branch_data[0]) if branch_data[0] else 0,  # تحويل إلى رقم
                'branch_name': str(branch_data[1]) if branch_data[1] else '',
                'branch_code': str(branch_data[2]) if branch_data[2] else '',
                'dblink_name': str(branch_data[3]) if branch_data[3] else ''
            }
            branches.append(branch_dict)
        
        return jsonify({
            'success': True,
            'branches': branches
        })
        
    except Exception as e:
        logger.error(f"خطأ في جلب الفروع: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

# ==================== التحدي الحقيقي: جلب بيانات الموردين من DB_LINKS ====================

@contracts.route('/api/suppliers/search', methods=['POST'])
@login_required
def search_suppliers():
    """البحث عن الموردين من جدول V_DETAILS باستخدام DB_LINKS - التحدي الحقيقي"""
    try:
        data = request.get_json()
        branch_id = data.get('branch_id')
        search_term = data.get('search_term', '')
        
        if not branch_id:
            return jsonify({
                'success': False,
                'message': 'رقم الفرع مطلوب'
            })
        
        logger.info(f"🔍 البحث عن الموردين في الفرع {branch_id} بالمصطلح: {search_term}")
        
        oracle_mgr = get_oracle_manager()
        
        # الحصول على بيانات الاتصال للفرع
        branch_query = """
        SELECT BRN_CODE, DBLINK_NAME, BRN_LNAME
        FROM branches
        WHERE BRN_NO = :1 AND IS_ACTIVE = 1
        """
        
        branch_data = oracle_mgr.execute_query(branch_query, [branch_id])
        
        if not branch_data:
            return jsonify({
                'success': False,
                'message': 'الفرع غير موجود أو غير نشط'
            })
        
        branch_code = branch_data[0][0]  # اسم المستخدم
        dblink_name = branch_data[0][1]  # اسم DB_LINK
        branch_name = branch_data[0][2]  # اسم الفرع
        
        logger.info(f"📡 استخدام DB_LINK: {dblink_name} للمستخدم: {branch_code}")
        
        # بناء استعلام البحث في جدول V_DETAILS عبر DB_LINK فقط
        # التأكد من أن اسم DB_LINK صحيح ومحاط بعلامات اقتباس إذا لزم الأمر
        safe_dblink = f'"{dblink_name}"' if '.' in dblink_name else dblink_name

        # افتراض وجود الأعمدة الأساسية (تجنب مشاكل timeout)
        available_columns = ['V_CODE', 'V_A_NAME', 'V_E_NAME', 'V_A_CODE', 'V_GROUP_CODE', 'V_CLASS', 'V_DEGREE', 'V_TAX_CODE', 'TAXPAYER', 'V_ADDRESS', 'CNTRY_NO', 'CITY_NO', 'CC_CODE', 'CC_NO', 'V_PHONE', 'V_FAX', 'V_BOX', 'V_PERSON', 'V_E_MAIL', 'V_WEB_SITE', 'V_MOBILE']
        logger.info(f"📋 استخدام الأعمدة الافتراضية لجدول V_DETAILS")

        # بناء الاستعلام للأعمدة المطلوبة: V_CODE و V_A_NAME فقط
        select_columns = []

        # العمود الأول: V_CODE (كود المورد)
        if 'V_CODE' in available_columns:
            select_columns.append('V_CODE as SUPPLIER_ID')      # كود المورد كمعرف
            select_columns.append('V_CODE as SUPPLIER_CODE')    # نفس الكود
        else:
            logger.error("❌ العمود V_CODE غير موجود في جدول V_DETAILS")
            select_columns.append("'' as SUPPLIER_ID")
            select_columns.append("'' as SUPPLIER_CODE")

        # العمود الثاني: V_A_NAME (اسم المورد بالعربية)
        if 'V_A_NAME' in available_columns:
            select_columns.append('V_A_NAME as SUPPLIER_NAME')
        else:
            logger.error("❌ العمود V_A_NAME غير موجود في جدول V_DETAILS")
            select_columns.append("'غير محدد' as SUPPLIER_NAME")

        # الأعمدة الإضافية (فارغة لأننا نريد V_CODE و V_A_NAME فقط)
        select_columns.append("'' as PHONE")
        select_columns.append("'' as EMAIL")
        select_columns.append("'' as CITY")

        columns_str = ', '.join(select_columns)
        logger.info(f"📝 الأعمدة المستخدمة: V_CODE و V_A_NAME من جدول IAS20251.V_DETAILS")
        logger.info(f"📝 الاستعلام النهائي: {columns_str}")

        if search_term:
            where_conditions = []
            search_params = []

            # البحث في V_A_NAME (اسم المورد بالعربية)
            if 'V_A_NAME' in available_columns:
                where_conditions.append("UPPER(V_A_NAME) LIKE UPPER(:1)")
                search_params.append(f"%{search_term}%")

            # البحث في V_CODE (كود المورد)
            if 'V_CODE' in available_columns:
                param_num = len(search_params) + 1
                where_conditions.append(f"UPPER(V_CODE) LIKE UPPER(:{param_num})")
                search_params.append(f"%{search_term}%")

            # إذا لم توجد الأعمدة المطلوبة، ابحث في جميع الصفوف
            if not where_conditions:
                where_clause = "1=1"
                search_params = []
                logger.warning("⚠️ لم يتم العثور على V_CODE أو V_A_NAME للبحث")
            else:
                where_clause = " OR ".join(where_conditions)
                logger.info(f"🔍 البحث في: {', '.join(['V_A_NAME' if 'V_A_NAME' in available_columns else '', 'V_CODE' if 'V_CODE' in available_columns else ''])}")

            suppliers_query = f"""
            SELECT {columns_str}
            FROM IAS20251.V_DETAILS@{safe_dblink}
            WHERE ({where_clause})
            AND ROWNUM <= 50
            ORDER BY V_A_NAME
            """
            params = search_params
        else:
            # إذا لم يكن هناك مصطلح بحث، جلب أول 50 مورد
            suppliers_query = f"""
            SELECT {columns_str}
            FROM IAS20251.V_DETAILS@{safe_dblink}
            WHERE ROWNUM <= 50
            ORDER BY V_A_NAME
            """
            params = []
        
        logger.info(f"🔗 تنفيذ استعلام عبر DB_LINK: {safe_dblink}")
        logger.info(f"📝 الاستعلام: {suppliers_query}")

        try:
            suppliers_data = oracle_mgr.execute_query(suppliers_query, params)
            logger.info(f"📋 تم العثور على {len(suppliers_data) if suppliers_data else 0} مورد من IAS20251.V_DETAILS")

            suppliers = []
            if suppliers_data:
                for supplier_data in suppliers_data:
                    try:
                        supplier_dict = {
                            'supplier_id': int(supplier_data[0]) if supplier_data[0] else 0,
                            'supplier_name': str(supplier_data[1]) if supplier_data[1] else '',
                            'supplier_code': str(supplier_data[2]) if supplier_data[2] else '',
                            'phone': str(supplier_data[3]) if supplier_data[3] else '',
                            'email': str(supplier_data[4]) if supplier_data[4] else '',
                            'city': str(supplier_data[5]) if supplier_data[5] else ''
                        }
                        suppliers.append(supplier_dict)
                    except Exception as parse_error:
                        logger.error(f"خطأ في تحويل بيانات المورد: {parse_error}")
                        continue
            
            return jsonify({
                'success': True,
                'suppliers': suppliers,
                'branch_info': {
                    'branch_name': branch_name,
                    'branch_code': branch_code,
                    'dblink_name': dblink_name
                },
                'total': len(suppliers),
                'source': 'remote_database',
                'message': f'تم جلب البيانات من IAS20251.V_DETAILS عبر {safe_dblink}'
            })
            
        except Exception as db_error:
            error_msg = str(db_error)
            logger.error(f"❌ خطأ في الاتصال بـ DB_LINK {safe_dblink}: {error_msg}")

            # تحليل نوع الخطأ
            error_details = ""
            if "ORA-00942" in error_msg:
                error_details = "جدول V_DETAILS غير موجود في المستخدم IAS20251"
                logger.error(f"❌ {error_details}")
            elif "ORA-02063" in error_msg:
                error_details = "مشكلة في DB_LINK أو الاتصال"
                logger.error(f"❌ {error_details}")
            elif "ORA-12154" in error_msg:
                error_details = "DB_LINK غير موجود أو غير صحيح"
                logger.error(f"❌ {error_details}")
            elif "ORA-00904" in error_msg:
                error_details = "عمود غير موجود في جدول V_DETAILS"
                logger.error(f"❌ {error_details}")
            else:
                error_details = f"خطأ غير معروف: {error_msg}"

            # إرجاع خطأ واضح بدلاً من التبديل للموردين المحليين
            return jsonify({
                'success': False,
                'message': f'فشل في الوصول إلى جدول IAS20251.V_DETAILS عبر DB_LINK {safe_dblink}',
                'error_details': error_details,
                'branch_info': {
                    'branch_name': branch_name,
                    'branch_code': branch_code,
                    'dblink_name': dblink_name
                },
                'suggestions': [
                    'تحقق من وجود DB_LINK في قاعدة البيانات',
                    'تحقق من صحة اسم المستخدم IAS20251',
                    'تحقق من وجود جدول V_DETAILS في المستخدم IAS20251',
                    'تحقق من صلاحيات الوصول عبر DB_LINK'
                ]
            })
        
    except Exception as e:
        logger.error(f"❌ خطأ في البحث عن الموردين: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في البحث: {str(e)}'
        })

@contracts.route('/api/contracts/<int:contract_id>', methods=['PUT'])
@login_required
def update_contract(contract_id):
    """تحديث العقد"""
    try:
        data = request.get_json()
        oracle_mgr = get_oracle_manager()

        logger.info(f"🔄 تحديث العقد رقم {contract_id}")
        logger.info(f"📥 البيانات المستلمة: {data}")

        # تحديث العقد الرئيسي (بدون DESCRIPTION لتجنب مشكلة NCLOB)
        # إصلاح: حفظ البيانات في الأعمدة الصحيحة (عكس ما هو موجود حالياً)
        update_contract_sql = """
        UPDATE contracts SET
            start_date = :1, end_date = :2, supplier_id = :3, supplier_name = :4,
            currency_code = :5, exchange_rate = :6, reference_number = :7,
            contract_amount = :8, updated_at = :9, contract_status = :10
        WHERE contract_id = :11
        """

        contract_params = [
            datetime.strptime(data.get('start_date'), '%Y-%m-%d').date(),
            datetime.strptime(data.get('end_date'), '%Y-%m-%d').date(),
            data.get('supplier_name'),      # حفظ اسم المورد في عمود supplier_id (لأن البيانات معكوسة)
            data.get('supplier_id'),        # حفظ كود المورد في عمود supplier_name (لأن البيانات معكوسة)
            data.get('currency_code', 'SAR'),
            float(data.get('exchange_rate', 1)),
            data.get('reference_number', ''),
            float(data.get('contract_amount', 0)),
            datetime.now(),
            data.get('contract_status', 'DRAFT'),  # حالة العقد
            contract_id
        ]

        # تحديث description منفصلاً لتجنب مشكلة NCLOB
        description = data.get('description', '')
        if description:
            update_desc_sql = "UPDATE contracts SET description = :1 WHERE contract_id = :2"
            oracle_mgr.execute_update(update_desc_sql, [description, contract_id])

        logger.info(f"📝 معاملات التحديث: {contract_params}")
        rows_affected = oracle_mgr.execute_update(update_contract_sql, contract_params)
        logger.info(f"📊 عدد الصفوف المتأثرة: {rows_affected}")

        if rows_affected > 0:
            # تحديث تفاصيل العقد إذا تم إرسالها
            contract_items = data.get('contract_items', [])
            if contract_items:
                # حذف التفاصيل القديمة
                delete_details_sql = "DELETE FROM contract_details WHERE contract_id = :1"
                oracle_mgr.execute_update(delete_details_sql, [contract_id])

                # إضافة التفاصيل الجديدة
                insert_item_sql = """
                INSERT INTO contract_details (contract_id, item_id, item_name, quantity, executed_quantity,
                                            remaining_quantity, free_quantity, unit_name, unit_price,
                                            discount_percentage, tax_amount, line_total, production_date,
                                            expiry_date, created_at, updated_at)
                VALUES (:1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13, :14, :15, :16)
                """

                for item in contract_items:
                    quantity = float(item.get('quantity', 0))
                    item_params = [
                        contract_id,                                    # contract_id
                        item.get('item_code', ''),                     # item_id (كود الصنف)
                        item.get('item_name', ''),                     # item_name
                        quantity,                                       # quantity
                        0,                                             # executed_quantity (افتراضياً 0)
                        quantity,                                       # remaining_quantity (افتراضياً = الكمية الأصلية)
                        float(item.get('free_quantity', 0)),           # free_quantity
                        item.get('unit_name', 'قطعة'),                 # unit_name
                        float(item.get('unit_price', 0)),              # unit_price
                        float(item.get('discount_percentage', 0)),     # discount_percentage
                        float(item.get('tax_amount', 0)),              # tax_amount
                        float(item.get('line_total', 0)),              # line_total
                        None,                                          # production_date
                        None,                                          # expiry_date
                        datetime.now(),                                # created_at
                        datetime.now()                                 # updated_at
                    ]

                    try:
                        oracle_mgr.execute_update(insert_item_sql, item_params)
                    except Exception as e:
                        logger.error(f"خطأ في حفظ تفاصيل الصنف {item.get('item_name')}: {e}")

            return jsonify({
                'success': True,
                'message': 'تم تحديث العقد والتفاصيل بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'العقد غير موجود'
            })

    except Exception as e:
        logger.error(f"خطأ في تحديث العقد {contract_id}: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في تحديث العقد: {str(e)}'
        })

@contracts.route('/api/contracts/<int:contract_id>/update-with-table', methods=['POST'])
@login_required
def update_contract_with_table(contract_id):
    """تحديث العقد مع بيانات جدول Handsontable"""
    try:
        oracle_mgr = get_oracle_manager()

        # الحصول على البيانات من النموذج
        contract_data = {
            'start_date': request.form.get('start_date'),
            'end_date': request.form.get('end_date'),
            'supplier_id': request.form.get('supplier_id'),
            'supplier_name': request.form.get('supplier_name'),
            'currency_code': request.form.get('currency_code', 'SAR'),
            'exchange_rate': request.form.get('exchange_rate', 1),
            'reference_number': request.form.get('reference_number', ''),
            'description': request.form.get('description', ''),
            'contract_status': request.form.get('contract_status', 'DRAFT')
        }

        # الحصول على بيانات الجدول
        table_data_json = request.form.get('table_data')
        table_data = []

        if table_data_json:
            import json
            table_data = json.loads(table_data_json)

        logger.info(f"🔄 تحديث العقد {contract_id} مع {len(table_data)} صنف")

        # تحديث العقد الرئيسي
        update_contract_sql = """
        UPDATE contracts SET
            start_date = :1, end_date = :2, supplier_id = :3, supplier_name = :4,
            currency_code = :5, exchange_rate = :6, reference_number = :7,
            contract_amount = :8, updated_at = :9, contract_status = :10
        WHERE contract_id = :11
        """

        # حساب إجمالي العقد
        total_amount = sum(float(item.get('line_total', 0)) for item in table_data)

        contract_params = [
            datetime.strptime(contract_data['start_date'], '%Y-%m-%d').date(),
            datetime.strptime(contract_data['end_date'], '%Y-%m-%d').date(),
            contract_data['supplier_name'],  # حفظ اسم المورد في عمود supplier_id
            contract_data['supplier_id'],    # حفظ كود المورد في عمود supplier_name
            contract_data['currency_code'],
            float(contract_data['exchange_rate']),
            contract_data['reference_number'],
            total_amount,
            datetime.now(),
            contract_data['contract_status'],
            contract_id
        ]

        oracle_mgr.execute_update(update_contract_sql, contract_params)

        # تحديث الوصف منفصلاً
        if contract_data['description']:
            update_description_sql = "UPDATE contracts SET description = :1 WHERE contract_id = :2"
            oracle_mgr.execute_update(update_description_sql, [contract_data['description'], contract_id])

        # حذف التفاصيل القديمة
        delete_details_sql = "DELETE FROM contract_details WHERE contract_id = :1"
        oracle_mgr.execute_update(delete_details_sql, [contract_id])

        # إدراج التفاصيل الجديدة
        if table_data:
            insert_detail_sql = """
            INSERT INTO contract_details (
                contract_id, item_id, item_name, quantity, executed_quantity,
                remaining_quantity, free_quantity, unit_name, unit_price,
                discount_percentage, tax_amount, line_total, created_at, updated_at
            ) VALUES (
                :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13, :14
            )
            """

            for item in table_data:
                if item.get('item_code') and item.get('item_name'):
                    detail_params = [
                        contract_id,
                        item.get('item_code', ''),
                        item.get('item_name', ''),
                        float(item.get('quantity', 0)),
                        float(item.get('executed_quantity', 0)),
                        float(item.get('remaining_quantity', 0)),
                        float(item.get('free_quantity', 0)),
                        item.get('unit_name', 'قطعة'),
                        float(item.get('unit_price', 0)),
                        float(item.get('discount_percentage', 0)),
                        float(item.get('tax_amount', 0)),
                        float(item.get('line_total', 0)),
                        datetime.now(),
                        datetime.now()
                    ]

                    oracle_mgr.execute_update(insert_detail_sql, detail_params)

        logger.info(f"✅ تم تحديث العقد {contract_id} بنجاح")

        # إعادة توجيه إلى صفحة عرض العقد
        return redirect(url_for('contracts.view_contract', contract_id=contract_id))

    except Exception as e:
        logger.error(f"❌ خطأ في تحديث العقد {contract_id}: {e}")
        flash(f'خطأ في تحديث العقد: {str(e)}', 'error')
        return redirect(url_for('contracts.edit_contract', contract_id=contract_id))

@contracts.route('/api/contracts/<int:contract_id>', methods=['DELETE'])
@login_required
def delete_contract(contract_id):
    """حذف العقد"""
    try:
        oracle_mgr = get_oracle_manager()

        # حذف تفاصيل العقد أولاً
        delete_details_sql = "DELETE FROM contract_details WHERE contract_id = :1"
        oracle_mgr.execute_update(delete_details_sql, [contract_id])

        # حذف العقد الرئيسي
        delete_contract_sql = "DELETE FROM contracts WHERE contract_id = :1"
        rows_affected = oracle_mgr.execute_update(delete_contract_sql, [contract_id])

        if rows_affected > 0:
            return jsonify({
                'success': True,
                'message': 'تم حذف العقد بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'العقد غير موجود'
            })

    except Exception as e:
        logger.error(f"خطأ في حذف العقد {contract_id}: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في حذف العقد: {str(e)}'
        })





@contracts.route('/api/items/search', methods=['POST'])
@login_required
def search_items():
    """البحث عن الأصناف من جدول IAS_ITM_MST"""
    try:
        data = request.get_json()
        branch_id = data.get('branch_id')
        search_term = data.get('search_term', '').strip()

        logger.info(f"🔍 البحث عن الأصناف في الفرع {branch_id} بالمصطلح: '{search_term}'")

        # الحصول على بيانات الفرع
        branch_query = "SELECT BRN_LNAME, BRN_CODE, DBLINK_NAME FROM branches WHERE BRN_NO = :1"
        oracle_mgr = get_oracle_manager()
        branch_result = oracle_mgr.execute_query(branch_query, [branch_id])

        if not branch_result:
            return jsonify({
                'success': False,
                'message': 'الفرع غير موجود'
            })

        branch_name, branch_code, dblink_name = branch_result[0]
        logger.info(f"🏢 بيانات الفرع: {branch_name} ({branch_code}) - DB_LINK: {dblink_name}")

        # بناء استعلام البحث في جدول IAS_ITM_MST
        safe_dblink = f'"{dblink_name}"' if '.' in dblink_name else dblink_name

        # افتراض وجود الأعمدة الأساسية في IAS_ITM_MST (تجنب timeout)
        available_columns = ['I_CODE', 'I_NAME', 'I_E_NAME', 'G_CODE', 'GRP_CLASS_CODE', 'MNG_CODE', 'SUBG_CODE', 'ITEM_SIZE', 'ITEM_TYPE', 'INIT_PRIMARY_COST', 'PRIMARY_COST', 'I_CWTAVG', 'I_DESC', 'ALTER_CODE', 'MANF_CODE', 'BLOCKED', 'INACTIVE', 'INACTIVE_RES', 'INACTIVE_DATE', 'INACTIVE_U_ID', 'SERVICE_ITM', 'CASH_SALE', 'NO_RETURN_SALE', 'RETURN_PERIOD', 'KIT_ITM', 'USE_EXP_DATE', 'USE_BATCH_NO', 'USE_SERIALNO', 'USE_ATTCH', 'VAT_TYPE', 'VAT_PER', 'ALLOW_DISC', 'DISC_PER', 'ALLOW_DISC_PI', 'REST_ITM', 'DISC_PER_PI', 'ALLOW_FREE_QTY', 'FREE_QTY_PER', 'USE_QTY_FRACTION', 'UNDER_SELLING', 'USE_ITM_IN_CSS_SYS_FLG', 'RMS_ITM_TYP', 'GROUP_NO', 'ILEV_NO', 'I_IMG', 'DAY_ITM_EXPIRE', 'MIN_LMT_COST_PER', 'MAX_LMT_COST_PER']
        logger.info(f"📋 استخدام الأعمدة الافتراضية لجدول IAS_ITM_MST")

        # بناء الاستعلام بناءً على الأعمدة المطلوبة I_CODE و I_NAME
        # مع جلب الوحدة من جدول IAS_ITM_DTL

        # فحص وجود جدول IAS_ITM_DTL
        check_dtl_table_query = f"""
        SELECT TABLE_NAME
        FROM ALL_TABLES@{safe_dblink}
        WHERE OWNER = 'IAS20251' AND TABLE_NAME = 'IAS_ITM_DTL'
        """

        dtl_table_exists = False
        try:
            logger.info(f"🔍 فحص وجود جدول IAS_ITM_DTL...")
            dtl_result = oracle_mgr.execute_query(check_dtl_table_query)
            dtl_table_exists = len(dtl_result) > 0 if dtl_result else False
            logger.info(f"📋 جدول IAS_ITM_DTL موجود: {dtl_table_exists}")
        except Exception as dtl_error:
            logger.error(f"❌ خطأ في فحص جدول IAS_ITM_DTL: {dtl_error}")
            dtl_table_exists = False

        # بناء الاستعلام مع أو بدون JOIN حسب وجود جدول IAS_ITM_DTL
        if dtl_table_exists:
            # استعلام مع JOIN لجلب الوحدة من IAS_ITM_DTL
            logger.info(f"📝 بناء استعلام مع JOIN لجلب الوحدة من IAS_ITM_DTL")

            if search_term:
                where_conditions = []
                search_params = []

                # البحث في رقم الصنف - I_CODE
                if 'I_CODE' in available_columns:
                    where_conditions.append("UPPER(mst.I_CODE) LIKE UPPER(:1)")
                    search_params.append(f"%{search_term}%")

                # البحث في اسم الصنف - I_NAME
                if 'I_NAME' in available_columns:
                    param_num = len(search_params) + 1
                    where_conditions.append(f"UPPER(mst.I_NAME) LIKE UPPER(:{param_num})")
                    search_params.append(f"%{search_term}%")

                if not where_conditions:
                    where_clause = "1=1"
                    search_params = []
                else:
                    where_clause = " OR ".join(where_conditions)

                items_query = f"""
                SELECT mst.I_CODE as ITEM_ID,
                       mst.I_NAME as ITEM_NAME,
                       NVL(dtl.ITM_UNT, 'قطعة') as UNIT,
                       1 as IS_TAXABLE
                FROM IAS20251.IAS_ITM_MST@{safe_dblink} mst
                LEFT JOIN IAS20251.IAS_ITM_DTL@{safe_dblink} dtl
                    ON mst.I_CODE = dtl.I_CODE AND dtl.PUR_UNIT = 1
                WHERE ({where_clause})
                AND ROWNUM <= 50
                ORDER BY mst.I_NAME
                """
                params = search_params
            else:
                # إذا لم يكن هناك مصطلح بحث، جلب أول 50 صنف
                items_query = f"""
                SELECT mst.I_CODE as ITEM_ID,
                       mst.I_NAME as ITEM_NAME,
                       NVL(dtl.ITM_UNT, 'قطعة') as UNIT,
                       1 as IS_TAXABLE
                FROM IAS20251.IAS_ITM_MST@{safe_dblink} mst
                LEFT JOIN IAS20251.IAS_ITM_DTL@{safe_dblink} dtl
                    ON mst.I_CODE = dtl.I_CODE AND dtl.PUR_UNIT = 1
                WHERE ROWNUM <= 50
                ORDER BY mst.I_NAME
                """
                params = []
        else:
            # استعلام بدون JOIN (الطريقة القديمة)
            logger.info(f"📝 بناء استعلام بدون JOIN - جدول IAS_ITM_DTL غير متاح")

            if search_term:
                where_conditions = []
                search_params = []

                # البحث في رقم الصنف - I_CODE
                if 'I_CODE' in available_columns:
                    where_conditions.append("UPPER(I_CODE) LIKE UPPER(:1)")
                    search_params.append(f"%{search_term}%")

                # البحث في اسم الصنف - I_NAME
                if 'I_NAME' in available_columns:
                    param_num = len(search_params) + 1
                    where_conditions.append(f"UPPER(I_NAME) LIKE UPPER(:{param_num})")
                    search_params.append(f"%{search_term}%")

                if not where_conditions:
                    where_clause = "1=1"
                    search_params = []
                else:
                    where_clause = " OR ".join(where_conditions)

                items_query = f"""
                SELECT I_CODE as ITEM_ID, I_NAME as ITEM_NAME, 'قطعة' as UNIT, 1 as IS_TAXABLE
                FROM IAS20251.IAS_ITM_MST@{safe_dblink}
                WHERE ({where_clause})
                AND ROWNUM <= 50
                ORDER BY I_NAME
                """
                params = search_params
            else:
                # إذا لم يكن هناك مصطلح بحث، جلب أول 50 صنف
                items_query = f"""
                SELECT I_CODE as ITEM_ID, I_NAME as ITEM_NAME, 'قطعة' as UNIT, 1 as IS_TAXABLE
                FROM IAS20251.IAS_ITM_MST@{safe_dblink}
                WHERE ROWNUM <= 50
                ORDER BY I_NAME
                """
                params = []

        logger.info(f"📝 استعلام الأصناف: {items_query}")

        # تنفيذ الاستعلام
        items_data = oracle_mgr.execute_query(items_query, params)

        items = []
        if items_data:
            for item_data in items_data:
                try:
                    item_dict = {
                        'item_id': str(item_data[0]) if item_data[0] else '',
                        'item_name': str(item_data[1]) if item_data[1] else '',
                        'unit': str(item_data[2]) if item_data[2] else 'قطعة',
                        'is_taxable': bool(item_data[3]) if len(item_data) > 3 else True
                    }
                    items.append(item_dict)
                except Exception as parse_error:
                    logger.error(f"خطأ في تحويل بيانات الصنف: {parse_error}")
                    continue

        unit_source = "IAS_ITM_DTL (ITM_UNT حيث PUR_UNIT=1)" if dtl_table_exists else "افتراضي"
        logger.info(f"📋 تم العثور على {len(items)} صنف من IAS_ITM_MST (I_CODE, I_NAME) مع الوحدة من {unit_source}")

        return jsonify({
            'success': True,
            'items': items,
            'branch_info': {
                'branch_name': branch_name,
                'branch_code': branch_code,
                'dblink_name': dblink_name
            },
            'total': len(items),
            'message': f'تم العثور على {len(items)} صنف من IAS_ITM_MST مع الوحدة من {unit_source}',
            'unit_source': unit_source
        })

    except Exception as db_error:
        error_msg = str(db_error)
        logger.error(f"❌ خطأ في البحث عن الأصناف: {error_msg}")

        return jsonify({
            'success': False,
            'message': f'فشل في الوصول إلى جدول IAS_ITM_MST: {error_msg}',
            'suggestions': [
                'تحقق من وجود DB_LINK في قاعدة البيانات',
                'تحقق من وجود جدول IAS_ITM_MST في المستخدم IAS20251',
                'تحقق من صلاحيات الوصول عبر DB_LINK'
            ]
        })

# ==================== API للمستندات (تم حذف الدوال المكررة) ====================

@contracts.route('/download/<filename>')
@login_required
def download_file(filename):
    """عرض/تحميل المرفق"""
    try:
        upload_dir = os.path.join(current_app.root_path, 'static', 'uploads', 'contracts')
        file_path = os.path.join(upload_dir, filename)

        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=False)
        else:
            flash('الملف غير موجود', 'error')
            return redirect(request.referrer or url_for('contracts.index'))

    except Exception as e:
        logger.error(f"❌ خطأ في عرض الملف: {e}")
        flash('خطأ في عرض الملف', 'error')
        return redirect(request.referrer or url_for('contracts.index'))

# ==================== Document Management Routes ====================

@contracts.route('/api/documents/<int:contract_id>')
@login_required
def get_contract_documents(contract_id):
    """جلب مستندات العقد"""
    try:
        oracle_mgr = get_oracle_manager()

        # جلب مستندات العقد
        query = """
        SELECT id, contract_id, title, document_type, file_path, file_size,
               description, created_at, created_by, url
        FROM contract_documents
        WHERE contract_id = :1
        ORDER BY created_at DESC
        """

        documents = oracle_mgr.execute_query(query, [contract_id])

        documents_list = []
        if documents:
            for doc in documents:
                doc_data = {
                    'id': doc[0],
                    'contract_id': doc[1],
                    'title': doc[2],
                    'type': doc[3],  # document_type
                    'filename': doc[4],  # file_path
                    'file_size': doc[5],
                    'description': doc[6],
                    'created_at': doc[7].isoformat() if doc[7] else None,
                    'created_by': doc[8],
                    'url': doc[9] if len(doc) > 9 else None  # url column
                }

                # إذا كان النوع رابط، إضافة معلومات الرابط
                if doc[3] == 'link':
                    # استخدام عمود URL إذا كان موجود، وإلا استخدم file_path
                    doc_data['url'] = doc[9] if doc[9] else doc[4]
                    doc_data['open_new_tab'] = True  # افتراضي

                documents_list.append(doc_data)

        return jsonify({
            'success': True,
            'documents': documents_list
        })

    except Exception as e:
        logger.error(f"خطأ في جلب مستندات العقد {contract_id}: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب المستندات: {str(e)}'
        })

@contracts.route('/api/documents/upload', methods=['POST'])
@login_required
def upload_contract_document():
    """رفع مستند جديد"""
    try:
        contract_id = request.form.get('contract_id')
        title = request.form.get('document_title')
        doc_type = request.form.get('document_type', 'other')
        description = request.form.get('document_description', '')

        if not contract_id or not title:
            return jsonify({
                'success': False,
                'message': 'معرف العقد وعنوان المستند مطلوبان'
            })

        # التحقق من وجود ملف
        if 'document_file' not in request.files:
            return jsonify({
                'success': False,
                'message': 'لم يتم اختيار ملف'
            })

        file = request.files['document_file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': 'لم يتم اختيار ملف'
            })

        # التحقق من نوع الملف
        allowed_extensions = {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.jpg', '.jpeg', '.png'}
        file_ext = os.path.splitext(file.filename)[1].lower()

        if file_ext not in allowed_extensions:
            return jsonify({
                'success': False,
                'message': 'نوع الملف غير مدعوم'
            })

        # إنشاء مجلد الرفع إذا لم يكن موجوداً
        upload_dir = os.path.join(current_app.root_path, 'static', 'uploads', 'contracts', 'documents')
        os.makedirs(upload_dir, exist_ok=True)

        # إنشاء اسم ملف فريد
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{contract_id}_{timestamp}_{secure_filename(file.filename)}"
        file_path = os.path.join(upload_dir, filename)

        # حفظ الملف
        file.save(file_path)
        file_size = os.path.getsize(file_path)

        # حفظ معلومات المستند في قاعدة البيانات
        oracle_mgr = get_oracle_manager()

        insert_query = """
        INSERT INTO contract_documents
        (contract_id, title, document_type, file_path, file_name, file_size, file_type, description, created_by, created_at)
        VALUES (:1, :2, :3, :4, :5, :6, :7, :8, :9, SYSDATE)
        """

        # تصحيح نوع المستند ليتوافق مع قيود قاعدة البيانات
        if doc_type not in ['attachment', 'link']:
            doc_type = 'attachment'  # القيمة الافتراضية

        oracle_mgr.execute_update(insert_query, [
            contract_id, title, doc_type, filename, file.filename,
            file_size, file_ext, description, current_user.username
        ])

        return jsonify({
            'success': True,
            'message': 'تم رفع المستند بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في رفع المستند: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في رفع المستند: {str(e)}'
        })

@contracts.route('/api/documents/<int:doc_id>/view')
@login_required
def view_document(doc_id):
    """عرض المستند"""
    try:
        oracle_mgr = get_oracle_manager()

        # جلب معلومات المستند
        query = "SELECT file_path FROM contract_documents WHERE id = :1"
        result = oracle_mgr.execute_query(query, [doc_id])

        if not result:
            return "المستند غير موجود", 404

        file_path_name = result[0][0]
        file_path = os.path.join(current_app.root_path, 'static', 'uploads', 'contracts', 'documents', file_path_name)

        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=False)
        else:
            return "الملف غير موجود", 404

    except Exception as e:
        logger.error(f"خطأ في عرض المستند {doc_id}: {e}")
        return "خطأ في عرض المستند", 500

@contracts.route('/api/documents/<int:doc_id>/download')
@login_required
def download_document(doc_id):
    """تحميل المستند"""
    try:
        oracle_mgr = get_oracle_manager()

        # جلب معلومات المستند
        query = "SELECT file_path, title FROM contract_documents WHERE id = :1"
        result = oracle_mgr.execute_query(query, [doc_id])

        if not result:
            return "المستند غير موجود", 404

        file_path_name, title = result[0]
        file_path = os.path.join(current_app.root_path, 'static', 'uploads', 'contracts', 'documents', file_path_name)

        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=True, download_name=f"{title}_{file_path_name}")
        else:
            return "الملف غير موجود", 404

    except Exception as e:
        logger.error(f"خطأ في تحميل المستند {doc_id}: {e}")
        return "خطأ في تحميل المستند", 500

@contracts.route('/api/documents/<int:doc_id>', methods=['DELETE'])
@login_required
def delete_document(doc_id):
    """حذف المستند"""
    try:
        oracle_mgr = get_oracle_manager()

        # جلب معلومات المستند
        query = "SELECT file_path, document_type FROM contract_documents WHERE id = :1"
        result = oracle_mgr.execute_query(query, [doc_id])

        if not result:
            return jsonify({
                'success': False,
                'message': 'المستند غير موجود'
            })

        file_path_name, doc_type = result[0]

        # حذف المستند من قاعدة البيانات
        delete_query = "DELETE FROM contract_documents WHERE id = :1"
        rows_affected = oracle_mgr.execute_update(delete_query, [doc_id])

        if rows_affected > 0:
            # حذف الملف من النظام (فقط إذا لم يكن رابط)
            if doc_type != 'link' and file_path_name:
                file_path = os.path.join(current_app.root_path, 'static', 'uploads', 'contracts', 'documents', file_path_name)
                if os.path.exists(file_path):
                    os.remove(file_path)

            return jsonify({
                'success': True,
                'message': 'تم حذف المستند بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في حذف المستند'
            })

    except Exception as e:
        logger.error(f"خطأ في حذف المستند {doc_id}: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في حذف المستند: {str(e)}'
        })

@contracts.route('/api/documents/add-link', methods=['POST'])
@login_required
def add_contract_link():
    """إضافة رابط للعقد"""
    try:
        logger.info("🔗 بدء معالجة طلب إضافة رابط")

        data = request.get_json()
        logger.info(f"📊 البيانات المستلمة: {data}")

        contract_id = data.get('contract_id')
        title = data.get('title')
        url = data.get('url')
        link_type = data.get('type', 'website')
        description = data.get('description', '')
        open_new_tab = data.get('open_new_tab', True)

        logger.info(f"📋 معرف العقد: {contract_id}")
        logger.info(f"📋 العنوان: {title}")
        logger.info(f"📋 الرابط: {url}")
        logger.info(f"📋 النوع: {link_type}")

        if not contract_id or not title or not url:
            logger.error("❌ بيانات ناقصة")
            return jsonify({
                'success': False,
                'message': 'معرف العقد وعنوان الرابط والرابط مطلوبان'
            })

        # التحقق من صحة الرابط
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            logger.info(f"🔧 تم تصحيح الرابط إلى: {url}")

        oracle_mgr = get_oracle_manager()

        # إدراج الرابط في قاعدة البيانات
        insert_query = """
        INSERT INTO contract_documents
        (contract_id, title, document_type, url, file_size, description, created_by, created_at)
        VALUES (:1, :2, :3, :4, 0, :5, :6, SYSDATE)
        """

        params = [
            contract_id, title, 'link', url,
            description, current_user.username
        ]

        logger.info(f"📝 الاستعلام: {insert_query}")
        logger.info(f"📝 المعاملات: {params}")

        # حفظ الرابط في عمود URL المخصص
        rows_affected = oracle_mgr.execute_update(insert_query, params)

        logger.info(f"✅ تم تأثير {rows_affected} صف")

        if rows_affected > 0:
            logger.info("✅ تم حفظ الرابط بنجاح")
            return jsonify({
                'success': True,
                'message': 'تم إضافة الرابط بنجاح'
            })
        else:
            logger.error("❌ لم يتم حفظ أي صف")
            return jsonify({
                'success': False,
                'message': 'فشل في حفظ الرابط'
            })

    except Exception as e:
        logger.error(f"❌ خطأ في إضافة الرابط: {e}")
        logger.error(f"❌ تفاصيل الخطأ: {str(e)}")
        import traceback
        logger.error(f"❌ Stack trace: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'message': f'خطأ في إضافة الرابط: {str(e)}'
        })

# تم حذف الدوال المكررة القديمة


# ==================== APIs للجدول التفاعلي ====================



@contracts.route('/api/items/save', methods=['POST'])
@login_required
def api_save_items():
    """API حفظ بيانات الأصناف"""
    try:
        data = request.get_json()

        if not data or 'items' not in data:
            return jsonify({
                'success': False,
                'message': 'بيانات غير صحيحة'
            }), 400

        items = data['items']

        # هنا يجب حفظ البيانات في قاعدة البيانات
        # مثال على المعالجة:
        saved_count = 0
        for item in items:
            if item.get('code') and item.get('quantity', 0) > 0:
                # حفظ الصنف في قاعدة البيانات
                saved_count += 1

        return jsonify({
            'success': True,
            'message': f'تم حفظ {saved_count} صنف بنجاح',
            'count': saved_count
        })

    except Exception as e:
        logger.error(f"خطأ في حفظ الأصناف: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@contracts.route('/api/suppliers/search', methods=['GET', 'POST'])
@login_required
def api_search_suppliers():
    """API جلب الموردين الفعليين من قاعدة البيانات"""
    try:
        # جلب معامل البحث
        if request.method == 'POST':
            data = request.get_json() or {}
            search_term = data.get('search_term', '').strip()
        else:
            search_term = request.args.get('search_term', '').strip()

        oracle_mgr = get_oracle_manager()

        # أولاً: اكتشاف بنية جدول الموردين
        try:
            structure_sql = """
            SELECT column_name
            FROM user_tab_columns
            WHERE table_name = 'SUPPLIERS'
            ORDER BY column_id
            """
            columns_result = oracle_mgr.execute_query(structure_sql, [])
            available_columns = [col[0] for col in columns_result] if columns_result else []
            logger.info(f"أعمدة جدول الموردين المتاحة: {available_columns}")
        except:
            available_columns = []

        # ثانياً: جلب الموردين بأسماء الأعمدة الصحيحة
        try:
            if search_term:
                suppliers_sql = """
                SELECT id, supplier_code, name_ar, name_en, phone, email, address, is_active
                FROM suppliers
                WHERE UPPER(name_ar) LIKE UPPER(:1)
                   OR UPPER(name_en) LIKE UPPER(:2)
                   OR UPPER(supplier_code) LIKE UPPER(:3)
                   OR id LIKE :4
                ORDER BY name_ar
                FETCH FIRST 50 ROWS ONLY
                """
                search_pattern = f'%{search_term}%'
                suppliers_result = oracle_mgr.execute_query(suppliers_sql, [search_pattern, search_pattern, search_pattern, search_pattern])
            else:
                suppliers_sql = """
                SELECT id, supplier_code, name_ar, name_en, phone, email, address, is_active
                FROM suppliers
                ORDER BY id
                FETCH FIRST 100 ROWS ONLY
                """
                suppliers_result = oracle_mgr.execute_query(suppliers_sql, [])

            # طباعة أول صف لفهم البنية
            if suppliers_result and len(suppliers_result) > 0:
                logger.info(f"أول صف من جدول الموردين: {suppliers_result[0]}")
                logger.info(f"عدد الأعمدة: {len(suppliers_result[0])}")

        except Exception as e:
            logger.error(f"خطأ في جلب الموردين: {e}")
            suppliers_result = []

        logger.info(f"تم جلب {len(suppliers_result) if suppliers_result else 0} مورد من قاعدة البيانات")

        suppliers = []
        if suppliers_result:
            for i, supplier in enumerate(suppliers_result):
                # تحويل LOB إلى string وتنظيف البيانات
                # البنية الصحيحة: id, supplier_code, name_ar, name_en, phone, email, address, is_active
                supplier_data = {
                    'supplier_id': int(supplier[0]) if supplier[0] else 0,
                    'supplier_code': safe_convert_lob(supplier[1], '-') if len(supplier) > 1 else str(supplier[0]),
                    'supplier_name': safe_convert_lob(supplier[2], '-') if len(supplier) > 2 and supplier[2] else (safe_convert_lob(supplier[3], '-') if len(supplier) > 3 else f'مورد {supplier[0]}'),
                    'name_ar': safe_convert_lob(supplier[2], '-') if len(supplier) > 2 else '-',
                    'name_en': safe_convert_lob(supplier[3], '-') if len(supplier) > 3 else '-',
                    'phone': safe_convert_lob(supplier[4], '-') if len(supplier) > 4 else '-',
                    'email': safe_convert_lob(supplier[5], '-') if len(supplier) > 5 else '-',
                    'address': safe_convert_lob(supplier[6], '-') if len(supplier) > 6 else '-',
                    'is_active': safe_convert_lob(supplier[7], '1') if len(supplier) > 7 else '1'
                }
                suppliers.append(supplier_data)

                # طباعة أول 3 موردين للتشخيص
                if i < 3:
                    logger.info(f"مورد {i+1}: ID={supplier[0]}, كود={safe_convert_lob(supplier[1])}, اسم عربي={safe_convert_lob(supplier[2])}, اسم إنجليزي={safe_convert_lob(supplier[3])}")

        return jsonify({
            'success': True,
            'suppliers': suppliers,
            'count': len(suppliers),
            'search_term': search_term,
            'available_columns': available_columns,
            'debug_info': {
                'total_suppliers_found': len(suppliers_result) if suppliers_result else 0,
                'first_row_sample': suppliers_result[0] if suppliers_result else None
            }
        })

    except Exception as e:
        logger.error(f"خطأ في البحث عن الموردين: {e}")
        import traceback
        logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")

        return jsonify({
            'success': False,
            'message': f'خطأ في البحث: {str(e)}',
            'error_details': str(e),
            'error_type': type(e).__name__
        })

@contracts.route('/api/branches')
@login_required
def api_get_branches():
    """API جلب الفروع الفعلية"""
    try:
        oracle_mgr = get_oracle_manager()

        # جلب الفروع الفعلية من قاعدة البيانات
        branches_sql = """
        SELECT branch_id, branch_name, branch_code, is_active
        FROM branches
        WHERE is_active = 'Y'
        ORDER BY branch_name
        """
        branches_result = oracle_mgr.execute_query(branches_sql, [])

        branches = []
        if branches_result:
            for branch in branches_result:
                branches.append({
                    'branch_id': branch[0],
                    'branch_name': branch[1],
                    'branch_code': branch[2] or branch[0],
                    'is_active': branch[3]
                })

        return jsonify({
            'success': True,
            'branches': branches,
            'count': len(branches)
        })

    except Exception as e:
        logger.error(f"خطأ في جلب الفروع: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب الفروع: {str(e)}'
        })


@contracts.route('/api/items/details')
@login_required
def api_get_item_details():
    """جلب تفاصيل صنف محدد من جدول الأصناف IAS_ITM_MST"""
    try:
        item_code = request.args.get('item_code', '').strip()
        branch_id = request.args.get('branch_id', '').strip()

        if not item_code:
            return jsonify({
                'success': False,
                'message': 'كود الصنف مطلوب'
            }), 400

        logger.info(f"🔍 البحث عن تفاصيل الصنف: {item_code}")

        oracle_mgr = get_oracle_manager()

        # إذا تم تمرير branch_id، استخدم جدول الأصناف الفعلي
        if branch_id:
            # الحصول على بيانات الفرع
            branch_query = "SELECT BRN_LNAME, BRN_CODE, DBLINK_NAME FROM branches WHERE BRN_NO = :1"
            branch_result = oracle_mgr.execute_query(branch_query, [branch_id])

            if branch_result:
                branch_name, branch_code, dblink_name = branch_result[0]
                safe_dblink = f'"{dblink_name}"' if '.' in dblink_name else dblink_name

                # فحص وجود جدول IAS_ITM_DTL للوحدات
                check_dtl_query = f"""
                SELECT TABLE_NAME
                FROM ALL_TABLES@{safe_dblink}
                WHERE OWNER = 'IAS20251' AND TABLE_NAME = 'IAS_ITM_DTL'
                """

                dtl_exists = False
                try:
                    dtl_result = oracle_mgr.execute_query(check_dtl_query)
                    dtl_exists = len(dtl_result) > 0 if dtl_result else False
                except:
                    dtl_exists = False

                # البحث في جدول الأصناف الفعلي IAS_ITM_MST مع أو بدون الوحدات
                if dtl_exists:
                    items_sql = f"""
                    SELECT mst.I_CODE as ITEM_CODE,
                           mst.I_NAME as ITEM_NAME,
                           mst.I_E_NAME as ITEM_NAME_EN,
                           NVL(dtl.ITM_UNT, 'قطعة') as UNIT_NAME
                    FROM IAS20251.IAS_ITM_MST@{safe_dblink} mst
                    LEFT JOIN IAS20251.IAS_ITM_DTL@{safe_dblink} dtl
                        ON mst.I_CODE = dtl.I_CODE AND dtl.PUR_UNIT = 1
                    WHERE UPPER(mst.I_CODE) = UPPER(:1)
                    AND ROWNUM <= 1
                    """
                else:
                    items_sql = f"""
                    SELECT mst.I_CODE as ITEM_CODE,
                           mst.I_NAME as ITEM_NAME,
                           mst.I_E_NAME as ITEM_NAME_EN,
                           'قطعة' as UNIT_NAME
                    FROM IAS20251.IAS_ITM_MST@{safe_dblink} mst
                    WHERE UPPER(mst.I_CODE) = UPPER(:1)
                    AND ROWNUM <= 1
                    """

                logger.info(f"📝 البحث في جدول الأصناف الفعلي: {items_sql}")
                items_result = oracle_mgr.execute_query(items_sql, [item_code])
            else:
                return jsonify({
                    'success': False,
                    'message': 'الفرع غير موجود'
                }), 404
        else:
            # البحث في جدول الموردين كبديل مؤقت (للتوافق مع النظام القديم)
            items_sql = """
            SELECT supplier_code, name_ar, name_en, 'قطعة' as unit
            FROM suppliers
            WHERE UPPER(supplier_code) = UPPER(:1)
            AND ROWNUM <= 1
            """

            logger.info(f"📝 البحث في جدول الموردين (مؤقت): {items_sql}")
            items_result = oracle_mgr.execute_query(items_sql, [item_code])

        if items_result and len(items_result) > 0:
            item = items_result[0]

            item_data = {
                'item_code': safe_convert_lob(item[0]) or item_code,
                'item_name': safe_convert_lob(item[1]) if item[1] else safe_convert_lob(item[2]),
                'name_ar': safe_convert_lob(item[1]),
                'name_en': safe_convert_lob(item[2]),
                'unit_name': safe_convert_lob(item[3]),
                'unit': safe_convert_lob(item[3])
            }

            logger.info(f"✅ تم العثور على الصنف: {item_data}")

            return jsonify({
                'success': True,
                'item': item_data,
                'message': f'تم العثور على الصنف {item_code}'
            })
        else:
            logger.info(f"❌ لم يتم العثور على الصنف: {item_code}")

            return jsonify({
                'success': False,
                'message': f'الصنف {item_code} غير موجود في قاعدة البيانات'
            }), 404

    except Exception as e:
        logger.error(f"خطأ في جلب تفاصيل الصنف: {e}")
        import traceback
        logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': 'حدث خطأ أثناء جلب تفاصيل الصنف'
        }), 500


@contracts.route('/api/items/search')
@login_required
def api_search_items():
    """البحث في جميع الأصناف المتاحة - استخدام جدول الموردين مؤقتاً"""
    try:
        search_term = request.args.get('search_term', '').strip()

        logger.info(f"🔍 البحث في الأصناف: {search_term}")

        oracle_mgr = get_oracle_manager()

        # استخدام جدول الموردين كأصناف مؤقتاً
        if search_term:
            items_sql = """
            SELECT supplier_code, name_ar, name_en, 'قطعة' as unit
            FROM suppliers
            WHERE UPPER(supplier_code) LIKE UPPER(:1)
               OR UPPER(name_ar) LIKE UPPER(:2)
               OR UPPER(name_en) LIKE UPPER(:3)
            ORDER BY supplier_code
            FETCH FIRST 50 ROWS ONLY
            """
            search_pattern = f'%{search_term}%'
            items_result = oracle_mgr.execute_query(items_sql, [search_pattern, search_pattern, search_pattern])
        else:
            items_sql = """
            SELECT supplier_code, name_ar, name_en, 'قطعة' as unit
            FROM suppliers
            ORDER BY supplier_code
            FETCH FIRST 100 ROWS ONLY
            """
            items_result = oracle_mgr.execute_query(items_sql, [])

        items = []
        if items_result:
            for item in items_result:
                items.append({
                    'item_code': safe_convert_lob(item[0]),
                    'item_name': safe_convert_lob(item[1]) if item[1] else safe_convert_lob(item[2]),
                    'name_ar': safe_convert_lob(item[1]),
                    'name_en': safe_convert_lob(item[2]),
                    'unit_name': safe_convert_lob(item[3]),
                    'unit': safe_convert_lob(item[3])
                })

        logger.info(f"✅ تم جلب {len(items)} صنف من قاعدة البيانات")

        return jsonify({
            'success': True,
            'items': items,
            'count': len(items),
            'search_term': search_term
        })

    except Exception as e:
        logger.error(f"خطأ في البحث عن الأصناف: {e}")
        import traceback
        logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': 'حدث خطأ أثناء البحث عن الأصناف'
        }), 500

# ==================== إدارة وثائق العقود ====================

@contracts.route('/<int:contract_id>/documents')
@login_required
def contract_documents(contract_id):
    """صفحة إدارة وثائق العقد"""
    logger.info(f"🔍 طلب إدارة وثائق العقد {contract_id}")
    try:
        oracle_manager = get_oracle_manager()

        # جلب بيانات العقد
        contract_query = """
            SELECT contract_id, contract_number, supplier_name, contract_date, contract_amount
            FROM contracts
            WHERE contract_id = :contract_id
        """

        contract_result = oracle_manager.execute_query(contract_query, {'contract_id': contract_id})

        if not contract_result:
            flash('العقد غير موجود', 'error')
            return redirect(url_for('contracts.index'))

        contract = {
            'contract_id': contract_result[0][0],
            'contract_number': safe_convert_lob(contract_result[0][1]),
            'supplier_name': safe_convert_lob(contract_result[0][2]),
            'contract_date': contract_result[0][3],
            'contract_amount': float(contract_result[0][4]) if contract_result[0][4] else 0
        }

        logger.info(f"✅ تم جلب بيانات العقد: {contract['contract_number']}")

        # جلب وثائق العقد (مع أعمدة الروابط)
        documents_query = """
            SELECT id, document_type, title, file_name, file_size,
                   created_by, created_at, description, nextcloud_share_link, onedrive_share_link
            FROM contract_documents
            WHERE contract_id = :contract_id
            ORDER BY created_at DESC
        """

        documents_result = oracle_manager.execute_query(documents_query, {'contract_id': contract_id})
        documents = []

        if documents_result:
            for doc in documents_result:
                doc_dict = {
                    'id': doc[0],
                    'document_type': safe_convert_lob(doc[1]),
                    'document_name': safe_convert_lob(doc[2]),
                    'file_name': safe_convert_lob(doc[3]),
                    'file_size': doc[4] if doc[4] else 0,
                    'file_size_mb': round(doc[4] / (1024 * 1024), 2) if doc[4] else 0,
                    'uploaded_by': safe_convert_lob(doc[5]),
                    'uploaded_at': doc[6],
                    'notes': safe_convert_lob(doc[7])
                }

                # إضافة روابط المشاركة من قاعدة البيانات
                doc_dict['nextcloud_share_link'] = safe_convert_lob(doc[8]) if len(doc) > 8 and doc[8] else None
                doc_dict['onedrive_share_link'] = safe_convert_lob(doc[9]) if len(doc) > 9 and doc[9] else None

                # تسجيل للتأكد من البيانات
                logger.info(f"📄 وثيقة {doc_dict['id']}: nextcloud={doc_dict['nextcloud_share_link']}, onedrive={doc_dict['onedrive_share_link']}")

                documents.append(doc_dict)

        # حساب الإحصائيات
        stats = {
            'total_documents': len(documents),
            'total_size_mb': round(sum(doc['file_size_mb'] for doc in documents), 2),
            'uploaded_documents': len([doc for doc in documents if not doc.get('is_generated', False)]),
            'generated_documents': len([doc for doc in documents if doc.get('is_generated', False)])
        }

        logger.info(f"✅ تم جلب {len(documents)} وثيقة للعقد {contract_id}")
        logger.info(f"📊 الإحصائيات: {stats}")

        return render_template('contracts/contract_documents.html',
                             contract=contract,
                             documents=documents,
                             stats=stats)

    except Exception as e:
        logger.error(f"خطأ في جلب وثائق العقد: {e}")
        import traceback
        logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")

        # إرجاع صفحة خطأ بسيطة بدلاً من redirect
        return f"""
        <html dir="rtl">
        <head><title>خطأ في تحميل الوثائق</title></head>
        <body style="font-family: Arial; text-align: center; padding: 50px;">
            <h1>❌ خطأ في تحميل وثائق العقد</h1>
            <p>معرف العقد: {contract_id}</p>
            <p>تفاصيل الخطأ: {str(e)}</p>
            <a href="/contracts/" style="color: blue;">العودة لصفحة العقود</a>
        </body>
        </html>
        """

@contracts.route('/<int:contract_id>/documents/test')
@login_required
def test_contract_documents(contract_id):
    """صفحة اختبار وثائق العقد"""
    return f"<h1>صفحة وثائق العقد {contract_id}</h1><p>الـ route يعمل بشكل صحيح!</p>"

@contracts.route('/<int:contract_id>/documents/upload', methods=['POST'])
@login_required
def upload_contract_documents(contract_id):
    """رفع وثيقة للعقد"""
    logger.info(f"🔄 بدء رفع وثائق للعقد {contract_id}")
    try:
        oracle_manager = get_oracle_manager()

        # التحقق من وجود العقد
        contract_check = oracle_manager.execute_query(
            "SELECT contract_id FROM contracts WHERE contract_id = :contract_id",
            {'contract_id': contract_id}
        )

        if not contract_check:
            return jsonify({'success': False, 'message': 'العقد غير موجود'}), 404

        # الحصول على بيانات النموذج
        document_type = request.form.get('document_type')
        document_name = request.form.get('document_name')
        notes = request.form.get('notes', '')

        logger.info(f"📝 بيانات النموذج: نوع={document_type}, اسم={document_name}")

        if not document_type:
            logger.warning("❌ نوع الوثيقة مطلوب")
            return jsonify({'success': False, 'message': 'نوع الوثيقة مطلوب'}), 400

        # التحقق من وجود ملفات
        if 'document_files' not in request.files:
            logger.warning("❌ لم يتم العثور على حقل document_files")
            return jsonify({'success': False, 'message': 'لم يتم اختيار أي ملفات'}), 400

        files = request.files.getlist('document_files')
        logger.info(f"📁 عدد الملفات المرسلة: {len(files)}")

        if not files or all(file.filename == '' for file in files):
            logger.warning("❌ لا توجد ملفات صالحة")
            return jsonify({'success': False, 'message': 'لم يتم اختيار أي ملفات'}), 400

        uploaded_files = []

        # إنشاء مجلد الوثائق إذا لم يكن موجوداً
        upload_folder = os.path.join(current_app.root_path, 'static', 'uploads', 'contracts', str(contract_id))
        os.makedirs(upload_folder, exist_ok=True)
        logger.info(f"📂 مجلد الرفع: {upload_folder}")

        for file in files:
            if file and file.filename:
                # تأمين اسم الملف
                filename = secure_filename(file.filename)
                unique_filename = f"{uuid.uuid4()}_{filename}"
                file_path = os.path.join(upload_folder, unique_filename)

                logger.info(f"💾 حفظ الملف: {filename} -> {file_path}")

                # حفظ الملف
                file.save(file_path)
                file_size = os.path.getsize(file_path)
                logger.info(f"✅ تم حفظ الملف بحجم: {file_size} بايت")

                # إدراج بيانات الوثيقة في قاعدة البيانات
                insert_query = """
                    INSERT INTO contract_documents
                    (contract_id, document_type, title, file_name, file_path,
                     file_size, created_by, description, created_at)
                    VALUES (:contract_id, :document_type, :document_name, :file_name,
                            :file_path, :file_size, :uploaded_by, :notes, SYSDATE)
                """

                params = {
                    'contract_id': contract_id,
                    'document_type': document_type,
                    'document_name': document_name or filename,
                    'file_name': filename,
                    'file_path': file_path,
                    'file_size': file_size,
                    'uploaded_by': current_user.username,
                    'notes': notes
                }

                logger.info(f"💾 إدراج في قاعدة البيانات: {params}")

                oracle_manager.execute_update(insert_query, params)
                logger.info(f"✅ تم إدراج الوثيقة في قاعدة البيانات")

                uploaded_files.append(filename)

        oracle_manager.commit()

        logger.info(f"✅ تم رفع {len(uploaded_files)} وثيقة للعقد {contract_id}")

        return jsonify({
            'success': True,
            'message': f'تم رفع {len(uploaded_files)} وثيقة بنجاح',
            'uploaded_files': uploaded_files
        })

    except Exception as e:
        logger.error(f"خطأ في رفع وثيقة العقد: {e}")
        import traceback
        logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في رفع الوثيقة: {str(e)}'
        }), 500

@contracts.route('/documents/<int:document_id>/delete', methods=['DELETE'])
@login_required
def delete_contract_document(document_id):
    """حذف وثيقة عقد"""
    try:
        oracle_manager = get_oracle_manager()

        # جلب بيانات الوثيقة
        doc_query = """
            SELECT file_path FROM contract_documents WHERE id = :document_id
        """
        doc_result = oracle_manager.execute_query(doc_query, {'document_id': document_id})

        if not doc_result:
            return jsonify({'success': False, 'message': 'الوثيقة غير موجودة'}), 404

        file_path = safe_convert_lob(doc_result[0][0])

        # حذف الملف من النظام
        if file_path and os.path.exists(file_path):
            os.remove(file_path)

        # حذف السجل من قاعدة البيانات
        delete_query = "DELETE FROM contract_documents WHERE id = :document_id"
        oracle_manager.execute_update(delete_query, {'document_id': document_id})
        oracle_manager.commit()

        logger.info(f"✅ تم حذف الوثيقة {document_id}")

        return jsonify({'success': True, 'message': 'تم حذف الوثيقة بنجاح'})

    except Exception as e:
        logger.error(f"خطأ في حذف وثيقة العقد: {e}")
        return jsonify({'success': False, 'message': 'حدث خطأ في حذف الوثيقة'}), 500

@contracts.route('/download_contract_document/<int:document_id>')
@login_required
def download_contract_document(document_id):
    """تحميل وثيقة عقد"""
    try:
        oracle_manager = get_oracle_manager()

        # جلب بيانات الوثيقة
        doc_query = """
            SELECT file_path, file_name FROM contract_documents WHERE id = :document_id
        """
        doc_result = oracle_manager.execute_query(doc_query, {'document_id': document_id})

        if not doc_result:
            flash('الوثيقة غير موجودة', 'error')
            return redirect(url_for('contracts.index'))

        file_path = safe_convert_lob(doc_result[0][0])
        file_name = safe_convert_lob(doc_result[0][1])

        if not file_path or not os.path.exists(file_path):
            flash('ملف الوثيقة غير موجود', 'error')
            return redirect(url_for('contracts.index'))

        return send_file(file_path, as_attachment=True, download_name=file_name)

    except Exception as e:
        logger.error(f"خطأ في تحميل وثيقة العقد: {e}")
        flash('حدث خطأ في تحميل الوثيقة', 'error')
        return redirect(url_for('contracts.index'))

@contracts.route('/documents/<int:document_id>/create-link', methods=['POST'])
@login_required
def create_contract_document_link(document_id):
    """إنشاء رابط مشاركة لوثيقة عقد"""
    logger.info(f"🔗 طلب إنشاء رابط للوثيقة {document_id}")
    try:
        oracle_manager = get_oracle_manager()

        # الحصول على نوع الخدمة
        data = request.get_json()
        service = data.get('service', 'nextcloud')  # nextcloud أو onedrive
        logger.info(f"📝 البيانات المستلمة: {data}")
        logger.info(f"🔧 نوع الخدمة: {service}")

        # التحقق من وجود الوثيقة
        doc_query = """
            SELECT id, file_path, file_name, nextcloud_share_link, onedrive_share_link
            FROM contract_documents
            WHERE id = :document_id
        """
        doc_result = oracle_manager.execute_query(doc_query, {'document_id': document_id})

        if not doc_result:
            return jsonify({'success': False, 'message': 'الوثيقة غير موجودة'}), 404

        doc = doc_result[0]
        file_path = safe_convert_lob(doc[1])
        file_name = safe_convert_lob(doc[2])
        existing_nextcloud_link = safe_convert_lob(doc[3]) if len(doc) > 3 and doc[3] else None
        existing_onedrive_link = safe_convert_lob(doc[4]) if len(doc) > 4 and doc[4] else None

        # تنظيف الروابط (إزالة المسافات والقيم الفارغة)
        if existing_nextcloud_link:
            existing_nextcloud_link = existing_nextcloud_link.strip()
            if not existing_nextcloud_link:
                existing_nextcloud_link = None

        if existing_onedrive_link:
            existing_onedrive_link = existing_onedrive_link.strip()
            if not existing_onedrive_link:
                existing_onedrive_link = None

        logger.info(f"🔍 فحص الروابط الموجودة: nextcloud='{existing_nextcloud_link}', onedrive='{existing_onedrive_link}'")

        # التحقق من وجود رابط مسبق
        if service == 'nextcloud' and existing_nextcloud_link:
            logger.info(f"✅ رابط Nextcloud موجود مسبقاً: {existing_nextcloud_link}")
            return jsonify({
                'success': True,
                'is_existing': True,
                'share_link': existing_nextcloud_link,
                'message': 'رابط Nextcloud موجود مسبقاً'
            })

        if service == 'onedrive' and existing_onedrive_link:
            logger.info(f"✅ رابط OneDrive موجود مسبقاً: {existing_onedrive_link}")
            return jsonify({
                'success': True,
                'is_existing': True,
                'share_link': existing_onedrive_link,
                'message': 'رابط OneDrive موجود مسبقاً'
            })

        logger.info(f"🔧 إنشاء رابط جديد للخدمة: {service}")

        # إنشاء رابط حقيقي باستخدام الخدمات السحابية
        share_link = None
        update_field = None

        if service == 'nextcloud':
            share_link = _create_cloud_link(file_path, file_name, 'nextcloud')
            update_field = 'nextcloud_share_link'
        elif service == 'onedrive':
            # إنشاء رابط تحميل مباشر بدلاً من OneDrive وهمي
            share_link = _create_direct_download_link(file_name, document_id)
            update_field = 'onedrive_share_link'

        if not share_link:
            return jsonify({
                'success': False,
                'message': f'فشل في إنشاء رابط {service}'
            }), 500

        # تحديث قاعدة البيانات (إضافة العمود إذا لم يكن موجوداً)
        try:
            update_query = f"""
                UPDATE contract_documents
                SET {update_field} = :share_link, updated_at = SYSDATE, updated_by = :updated_by
                WHERE id = :document_id
            """

            oracle_manager.execute_update(update_query, {
                'share_link': share_link,
                'updated_by': current_user.username,
                'document_id': document_id
            })
            oracle_manager.commit()
        except Exception as db_error:
            # إذا فشل التحديث، نرجع رابط وهمي فقط
            logger.warning(f"فشل في تحديث قاعدة البيانات: {db_error}")

        logger.info(f"✅ تم إنشاء رابط {service} للوثيقة {document_id}")

        return jsonify({
            'success': True,
            'is_existing': False,
            'share_link': share_link,
            'message': f'تم إنشاء رابط {service} بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في إنشاء رابط الوثيقة: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في إنشاء الرابط'
        }), 500



def _create_direct_download_link(file_name: str, document_id: int) -> Optional[str]:
    """إنشاء رابط تحميل مباشر من النظام"""
    try:
        import hashlib
        import time

        # إنشاء معرف فريد للملف
        unique_string = f"{document_id}_{file_name}_{int(time.time())}"
        file_hash = hashlib.sha256(unique_string.encode()).hexdigest()[:16]

        # إنشاء رابط تحميل مباشر
        base_url = "https://saserp.alfogehi.net:5000"  # أو عنوان الخادم الخاص بك
        share_link = f"{base_url}/contracts/shared/download/{file_hash}"

        logger.info(f"✅ تم إنشاء رابط تحميل مباشر: {share_link}")
        return share_link

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء رابط التحميل المباشر: {e}")
        return None

def _create_cloud_link(file_path: str, file_name: str, service: str) -> Optional[str]:
    """إنشاء رابط سحابي باستخدام cloud_link_manager"""
    try:
        if not CLOUD_SERVICES_AVAILABLE or not cloud_link_manager:
            logger.warning("⚠️ مدير الروابط السحابية غير متوفر")
            return None

        logger.info(f"🔗 إنشاء رابط {service} للملف: {file_name}")

        # تنظيف اسم الملف لتجنب مشاكل التشفير
        import re
        import unicodedata

        # إزالة الأحرف الخاصة والعربية
        clean_file_name = file_name

        # تحويل الأحرف العربية والخاصة
        clean_file_name = unicodedata.normalize('NFKD', clean_file_name)
        clean_file_name = clean_file_name.encode('ascii', 'ignore').decode('ascii')

        # الاحتفاظ فقط بالأحرف والأرقام والنقاط والشرطات
        clean_file_name = re.sub(r'[^a-zA-Z0-9\-_\.]', '_', clean_file_name)
        clean_file_name = re.sub(r'_+', '_', clean_file_name)  # دمج الشرطات المتعددة
        clean_file_name = clean_file_name.strip('_')

        # التأكد من وجود امتداد
        if '.' not in clean_file_name:
            # استخراج الامتداد من الملف الأصلي
            original_ext = os.path.splitext(file_name)[1]
            if original_ext:
                clean_file_name += original_ext
            else:
                clean_file_name += '.pdf'  # امتداد افتراضي

        # إضافة timestamp لضمان الفرادة
        import time
        timestamp = str(int(time.time()))
        name_parts = clean_file_name.rsplit('.', 1)
        if len(name_parts) == 2:
            clean_file_name = f"{name_parts[0]}_{timestamp}.{name_parts[1]}"
        else:
            clean_file_name = f"{clean_file_name}_{timestamp}"

        logger.info(f"📝 اسم الملف الأصلي: {file_name}")
        logger.info(f"📝 اسم الملف المنظف: {clean_file_name}")

        # استخدام cloud_link_manager من الشحنات
        logger.info(f"📡 استدعاء cloud_link_manager.create_share_link({file_path}, {clean_file_name}, {service})")
        result = cloud_link_manager.create_share_link(file_path, clean_file_name, service)

        logger.info(f"📨 نتيجة cloud_link_manager: {result}")

        if result and result.get('success'):
            share_link = result.get('share_link')
            if share_link:
                logger.info(f"✅ تم إنشاء رابط {service}: {share_link}")

                # فحص إضافي للروابط التي تحتوي على رموز تشفير خاطئة
                if '%EF%BF%BD' in share_link:
                    logger.warning(f"⚠️ الرابط يحتوي على رموز تشفير خاطئة: {share_link}")
                    # محاولة تنظيف الرابط
                    import urllib.parse
                    try:
                        clean_link = urllib.parse.unquote(share_link, errors='ignore')
                        logger.info(f"🔧 الرابط بعد التنظيف: {clean_link}")
                        return clean_link if clean_link != share_link else share_link
                    except:
                        logger.warning("❌ فشل في تنظيف الرابط")

                return share_link
            else:
                logger.error(f"❌ لم يتم الحصول على رابط {service}")
                return None
        else:
            error_msg = result.get('error', 'خطأ غير معروف') if result else 'لا توجد استجابة'
            logger.error(f"❌ فشل في إنشاء رابط {service}: {error_msg}")
            return None

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء رابط {service}: {e}")
        return None
