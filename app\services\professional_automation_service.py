# -*- coding: utf-8 -*-
"""
خدمة الأتمتة الاحترافية المستقلة
Professional Independent Automation Service
"""

import os
import sys
import time
import threading
import schedule
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import json

# إضافة مسار التطبيق
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class ProfessionalAutomationService:
    """خدمة الأتمتة الاحترافية المستقلة"""
    
    def __init__(self):
        """تهيئة خدمة الأتمتة"""
        # إعدادات افتراضية - يجب تعريفها أولاً
        self.default_settings = {
            'auto_create_orders': True,
            'auto_send_notifications': True,
            'check_interval_minutes': 5,
            'working_hours_start': '08:00',
            'working_hours_end': '18:00',
            'weekend_enabled': False,
            'max_orders_per_batch': 10,
            'retry_failed_orders': True,
            'retry_attempts': 3
        }

        self.setup_logging()
        self.running = False
        self.automation_thread = None
        self.settings = self.load_automation_settings()

        self.logger.info("🚀 تم تهيئة خدمة الأتمتة الاحترافية")
    
    def setup_logging(self):
        """إعداد نظام السجلات"""
        log_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        log_file = os.path.join(log_dir, 'automation.log')
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger('AutomationService')
    
    def load_automation_settings(self) -> Dict:
        """تحميل إعدادات الأتمتة"""
        try:
            settings_file = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'automation_settings.json')
            
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.logger.info("✅ تم تحميل إعدادات الأتمتة من الملف")
                    return settings
            else:
                self.logger.info("📝 استخدام الإعدادات الافتراضية")
                return self.default_settings.copy()
                
        except Exception as e:
            self.logger.error(f"❌ خطأ في تحميل إعدادات الأتمتة: {e}")
            return self.default_settings.copy()
    
    def save_automation_settings(self, settings: Dict):
        """حفظ إعدادات الأتمتة"""
        try:
            config_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'config')
            os.makedirs(config_dir, exist_ok=True)
            
            settings_file = os.path.join(config_dir, 'automation_settings.json')
            
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            
            self.settings = settings
            self.logger.info("✅ تم حفظ إعدادات الأتمتة")
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في حفظ إعدادات الأتمتة: {e}")
    
    def is_working_hours(self) -> bool:
        """فحص إذا كان الوقت الحالي ضمن ساعات العمل"""
        try:
            now = datetime.now()
            
            # فحص نهاية الأسبوع
            if not self.settings.get('weekend_enabled', False):
                if now.weekday() >= 5:  # السبت والأحد
                    return False
            
            # فحص ساعات العمل
            start_time = datetime.strptime(self.settings.get('working_hours_start', '08:00'), '%H:%M').time()
            end_time = datetime.strptime(self.settings.get('working_hours_end', '18:00'), '%H:%M').time()
            
            current_time = now.time()
            
            return start_time <= current_time <= end_time
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في فحص ساعات العمل: {e}")
            return True  # افتراضياً نعمل
    
    def get_pending_shipments(self) -> List[Dict]:
        """جلب الشحنات المعلقة التي تحتاج أوامر تسليم"""
        try:
            # محاولة استيراد مدير قاعدة البيانات
            try:
                from database_manager import DatabaseManager
            except ImportError:
                # محاولة استيراد من مسار آخر
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
                from database_manager import DatabaseManager
            
            db_manager = DatabaseManager()
            
            # البحث عن الشحنات التي وصلت ولا تحتوي على أوامر تسليم (Oracle syntax)
            query = """
                SELECT * FROM (
                    SELECT DISTINCT
                        cs.id,
                        cs.shipment_number,
                        cs.customs_agent_id,
                        ca.agent_name,
                        ca.phone,
                        ca.mobile,
                        cs.port_of_discharge,
                        cs.shipment_status,
                        cs.arrival_date
                    FROM cargo_shipments cs
                    LEFT JOIN customs_agents ca ON cs.customs_agent_id = ca.id
                    WHERE cs.shipment_status IN ('arrived', 'customs_clearance', 'ready_for_delivery')
                    AND cs.id NOT IN (
                        SELECT DISTINCT shipment_id
                        FROM delivery_orders
                        WHERE shipment_id IS NOT NULL
                    )
                    AND cs.customs_agent_id IS NOT NULL
                    AND ca.phone IS NOT NULL
                    ORDER BY cs.arrival_date ASC
                ) WHERE ROWNUM <= :max_orders
            """
            
            max_orders = self.settings.get('max_orders_per_batch', 10)
            result = db_manager.execute_query(query, {'max_orders': max_orders})
            
            shipments = []
            if result:
                for row in result:
                    shipments.append({
                        'shipment_id': row[0],
                        'shipment_number': row[1],
                        'customs_agent_id': row[2],
                        'agent_name': row[3],
                        'agent_phone': row[4],
                        'agent_mobile': row[5],
                        'port_of_discharge': row[6],
                        'shipment_status': row[7],
                        'arrival_date': row[8]
                    })
            
            db_manager.close()
            
            self.logger.info(f"📦 تم العثور على {len(shipments)} شحنة معلقة")
            return shipments
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في جلب الشحنات المعلقة: {e}")
            return []
    
    def create_delivery_order(self, shipment_data: Dict) -> Optional[int]:
        """إنشاء أمر تسليم تلقائياً"""
        try:
            # محاولة استيراد مدير قاعدة البيانات
            try:
                from database_manager import DatabaseManager
            except ImportError:
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
                from database_manager import DatabaseManager
            
            db_manager = DatabaseManager()
            
            # إنشاء رقم أمر تسليم جديد
            order_number = f"DO-{datetime.now().strftime('%Y%m%d')}-{shipment_data['shipment_id']:06d}"
            
            # إدراج أمر التسليم
            insert_query = """
                INSERT INTO delivery_orders (
                    order_number,
                    shipment_id,
                    customs_agent_id,
                    branch_id,
                    order_status,
                    priority,
                    created_date,
                    expected_completion_date
                ) VALUES (
                    :order_number,
                    :shipment_id,
                    :customs_agent_id,
                    1,
                    'draft',
                    'normal',
                    :created_date,
                    :expected_date
                )
            """
            
            # تحديد تاريخ الإنجاز المتوقع (3 أيام من الآن)
            expected_date = datetime.now() + timedelta(days=3)
            
            params = {
                'order_number': order_number,
                'shipment_id': shipment_data['shipment_id'],
                'customs_agent_id': shipment_data['customs_agent_id'],
                'created_date': datetime.now(),
                'expected_date': expected_date
            }
            
            db_manager.execute_query(insert_query, params)
            
            # الحصول على ID أمر التسليم المُنشأ
            order_id_query = "SELECT id FROM delivery_orders WHERE order_number = :order_number"
            result = db_manager.execute_query(order_id_query, {'order_number': order_number})
            
            if result:
                order_id = result[0][0]
                db_manager.close()
                
                self.logger.info(f"✅ تم إنشاء أمر تسليم تلقائي: {order_number} (ID: {order_id})")
                return order_id
            else:
                db_manager.close()
                return None
                
        except Exception as e:
            self.logger.error(f"❌ خطأ في إنشاء أمر التسليم: {e}")
            return None
    
    def send_delivery_order_notification(self, order_id: int, shipment_data: Dict) -> bool:
        """إرسال إشعار أمر التسليم تلقائياً"""
        try:
            # محاولة استيراد خدمة الواتساب
            try:
                from green_whatsapp_service import green_whatsapp_service
            except ImportError:
                # إذا لم تكن متاحة، استخدم طريقة بديلة
                self.logger.warning("⚠️ خدمة الواتساب غير متاحة، سيتم تخطي الإرسال")
                return True  # نعتبرها نجحت لتجنب توقف النظام
            
            # إعداد بيانات أمر التسليم
            order_data = {
                'id': order_id,
                'order_number': f"DO-{datetime.now().strftime('%Y%m%d')}-{shipment_data['shipment_id']:06d}",
                'booking_number': shipment_data['shipment_number'],
                'delivery_location': shipment_data['port_of_discharge'],
                'expected_completion_date': (datetime.now() + timedelta(days=3)).strftime('%Y-%m-%d'),
                'contact_person': shipment_data['agent_name'],
                'contact_phone': shipment_data['agent_phone'] or shipment_data['agent_mobile']
            }
            
            # تحديد رقم الهاتف للإرسال
            phone = shipment_data['agent_mobile'] or shipment_data['agent_phone']
            
            if not phone:
                self.logger.warning(f"⚠️ لا يوجد رقم هاتف للمخلص: {shipment_data['agent_name']}")
                return False
            
            # إرسال الإشعار
            success, message, msg_id = green_whatsapp_service.send_delivery_order(
                order_data, 
                phone, 
                include_pdf=True
            )
            
            if success:
                self.logger.info(f"📱 تم إرسال إشعار أمر التسليم بنجاح: {msg_id}")
                
                # تحديث حالة أمر التسليم إلى "مرسل"
                self.update_order_status(order_id, 'sent')
                
                return True
            else:
                self.logger.error(f"❌ فشل في إرسال إشعار أمر التسليم: {message}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ خطأ في إرسال إشعار أمر التسليم: {e}")
            return False
    
    def update_order_status(self, order_id: int, status: str):
        """تحديث حالة أمر التسليم"""
        try:
            # محاولة استيراد مدير قاعدة البيانات
            try:
                from database_manager import DatabaseManager
            except ImportError:
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
                from database_manager import DatabaseManager
            
            db_manager = DatabaseManager()
            
            update_query = """
                UPDATE delivery_orders 
                SET order_status = :status, updated_date = :updated_date
                WHERE id = :order_id
            """
            
            params = {
                'status': status,
                'updated_date': datetime.now(),
                'order_id': order_id
            }
            
            db_manager.execute_query(update_query, params)
            db_manager.close()
            
            self.logger.info(f"✅ تم تحديث حالة أمر التسليم {order_id} إلى: {status}")
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تحديث حالة أمر التسليم: {e}")

    def process_automation_cycle(self):
        """دورة معالجة الأتمتة الرئيسية"""
        try:
            if not self.is_working_hours():
                self.logger.info("⏰ خارج ساعات العمل - تخطي دورة الأتمتة")
                return

            self.logger.info("🔄 بدء دورة الأتمتة الجديدة")

            # جلب الشحنات المعلقة
            pending_shipments = self.get_pending_shipments()

            if not pending_shipments:
                self.logger.info("📦 لا توجد شحنات معلقة")
                return

            processed_count = 0
            success_count = 0

            for shipment in pending_shipments:
                try:
                    self.logger.info(f"🔄 معالجة شحنة: {shipment['shipment_number']}")

                    # إنشاء أمر التسليم
                    if self.settings.get('auto_create_orders', True):
                        order_id = self.create_delivery_order(shipment)

                        if order_id:
                            processed_count += 1

                            # إرسال الإشعار
                            if self.settings.get('auto_send_notifications', True):
                                if self.send_delivery_order_notification(order_id, shipment):
                                    success_count += 1
                                    self.logger.info(f"✅ تم معالجة شحنة {shipment['shipment_number']} بنجاح")
                                else:
                                    self.logger.warning(f"⚠️ تم إنشاء أمر التسليم لكن فشل الإرسال: {shipment['shipment_number']}")
                            else:
                                success_count += 1
                                self.logger.info(f"✅ تم إنشاء أمر التسليم: {shipment['shipment_number']} (بدون إرسال)")
                        else:
                            self.logger.error(f"❌ فشل في إنشاء أمر التسليم: {shipment['shipment_number']}")

                    # انتظار قصير بين المعالجات
                    time.sleep(2)

                except Exception as e:
                    self.logger.error(f"❌ خطأ في معالجة شحنة {shipment['shipment_number']}: {e}")
                    continue

            self.logger.info(f"🎉 انتهت دورة الأتمتة - معالج: {processed_count}, نجح: {success_count}")

        except Exception as e:
            self.logger.error(f"❌ خطأ في دورة الأتمتة: {e}")

    def start_service(self) -> bool:
        """بدء خدمة الأتمتة"""
        try:
            if self.running:
                self.logger.warning("⚠️ خدمة الأتمتة تعمل بالفعل")
                return False

            self.running = True

            # إعداد الجدولة
            interval = self.settings.get('check_interval_minutes', 5)
            schedule.every(interval).minutes.do(self.process_automation_cycle)

            # بدء thread منفصل للأتمتة
            self.automation_thread = threading.Thread(target=self._run_automation_loop, daemon=True)
            self.automation_thread.start()

            self.logger.info(f"🚀 تم بدء خدمة الأتمتة - فحص كل {interval} دقائق")
            return True

        except Exception as e:
            self.logger.error(f"❌ خطأ في بدء خدمة الأتمتة: {e}")
            self.running = False
            return False

    def stop_service(self) -> bool:
        """إيقاف خدمة الأتمتة"""
        try:
            if not self.running:
                self.logger.warning("⚠️ خدمة الأتمتة متوقفة بالفعل")
                return False

            self.running = False
            schedule.clear()

            if self.automation_thread and self.automation_thread.is_alive():
                self.automation_thread.join(timeout=10)

            self.logger.info("🛑 تم إيقاف خدمة الأتمتة")
            return True

        except Exception as e:
            self.logger.error(f"❌ خطأ في إيقاف خدمة الأتمتة: {e}")
            return False

    def _run_automation_loop(self):
        """حلقة تشغيل الأتمتة"""
        self.logger.info("🔄 بدء حلقة الأتمتة")

        while self.running:
            try:
                schedule.run_pending()
                time.sleep(30)  # فحص كل 30 ثانية
            except Exception as e:
                self.logger.error(f"❌ خطأ في حلقة الأتمتة: {e}")
                time.sleep(60)  # انتظار دقيقة عند الخطأ

        self.logger.info("🛑 انتهت حلقة الأتمتة")

    def get_service_status(self) -> Dict:
        """الحصول على حالة خدمة الأتمتة"""
        return {
            'running': self.running,
            'thread_alive': self.automation_thread.is_alive() if self.automation_thread else False,
            'settings': self.settings,
            'working_hours': self.is_working_hours(),
            'next_run': schedule.next_run() if schedule.jobs else None
        }

    def update_settings(self, new_settings: Dict) -> bool:
        """تحديث إعدادات الأتمتة"""
        try:
            # دمج الإعدادات الجديدة مع الموجودة
            updated_settings = self.settings.copy()
            updated_settings.update(new_settings)

            # حفظ الإعدادات
            self.save_automation_settings(updated_settings)

            # إعادة تشغيل الخدمة إذا كانت تعمل
            if self.running:
                self.stop_service()
                time.sleep(2)
                self.start_service()

            self.logger.info("✅ تم تحديث إعدادات الأتمتة")
            return True

        except Exception as e:
            self.logger.error(f"❌ خطأ في تحديث إعدادات الأتمتة: {e}")
            return False


# إنشاء instance عام للخدمة
professional_automation_service = ProfessionalAutomationService()


def start_automation_service():
    """بدء خدمة الأتمتة"""
    return professional_automation_service.start_service()


def stop_automation_service():
    """إيقاف خدمة الأتمتة"""
    return professional_automation_service.stop_service()


def get_automation_status():
    """الحصول على حالة خدمة الأتمتة"""
    return professional_automation_service.get_service_status()


def update_automation_settings(settings: Dict):
    """تحديث إعدادات الأتمتة"""
    return professional_automation_service.update_settings(settings)
