#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
اختبار نهائي شامل للنظام المحاسبي الكامل
Final Complete Test for Full Accounting System
"""

import sys
import os
sys.path.insert(0, os.getcwd())

from oracle_manager import OracleManager

def test_cancellation():
    """اختبار إلغاء الحوالة"""
    
    oracle = OracleManager()
    
    print('1️⃣ اختبار إلغاء الحوالة 5555...')

    # فحص عدد المعاملات قبل الإلغاء
    count_query = 'SELECT COUNT(*) FROM BALANCE_TRANSACTIONS'
    count_before = oracle.execute_query(count_query)[0][0]
    print(f'   عدد المعاملات قبل الإلغاء: {count_before}')

    try:
        cancel_query = """
        BEGIN
            CANCEL_TRANSFER_ACCOUNTING(5555, 1, NULL, NULL, 'Test complete cancellation after fix');
        END;
        """
        
        oracle.execute_update(cancel_query)
        print('✅ تم إلغاء الحوالة 5555 بنجاح!')
        
        # فحص عدد المعاملات بعد الإلغاء
        count_after = oracle.execute_query(count_query)[0][0]
        print(f'   عدد المعاملات بعد الإلغاء: {count_after}')
        print(f'   معاملات إلغاء جديدة: {count_after - count_before}')
        
        if count_after > count_before:
            print('✅ تم تسجيل معاملات الإلغاء في BALANCE_TRANSACTIONS!')
            
            # عرض معاملات الإلغاء
            cancel_transactions_query = """
            SELECT entity_type_code, entity_id, document_type_code,
                   debit_amount, credit_amount, document_number, description
            FROM BALANCE_TRANSACTIONS 
            WHERE document_number = 'CANCEL-TRF-5555'
            ORDER BY created_date
            """
            
            cancel_transactions = oracle.execute_query(cancel_transactions_query)
            if cancel_transactions:
                print('   معاملات الإلغاء المسجلة:')
                for row in cancel_transactions:
                    amount = row[3] if row[3] and row[3] > 0 else row[4]
                    trans_type = 'مدين' if row[3] and row[3] > 0 else 'دائن'
                    print(f'     {row[0]} {row[1]}: {trans_type} = {amount}')
                    print(f'       نوع: {row[2]}, مستند: {row[5]}')
                    print(f'       وصف: {row[6]}')
            
            return True
        else:
            print('❌ لم يتم تسجيل معاملات الإلغاء!')
            return False
        
    except Exception as e:
        print(f'❌ خطأ في إلغاء الحوالة: {str(e)}')
        return False

def test_new_execution():
    """اختبار تنفيذ حوالة جديدة"""
    
    oracle = OracleManager()
    
    print('\n2️⃣ اختبار تنفيذ حوالة جديدة...')

    # إنشاء حوالة اختبار جديدة
    try:
        new_transfer_query = """
        INSERT INTO TRANSFER_REQUESTS (
            id, request_number, beneficiary_id, amount, currency, total_amount,
            purpose, delivery_method, status, money_changer_bank_id,
            created_at, updated_at, created_by, updated_by
        ) VALUES (
            4444, 'TEST-4444', 1, 2500, 'USD', 2500,
            'Final complete test', 'BANK', 'approved', 2,
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1
        )
        """
        
        oracle.execute_update(new_transfer_query)
        print('✅ تم إنشاء حوالة اختبار 4444')
        
    except Exception as e:
        if 'unique constraint' in str(e).lower():
            print('⚠️ الحوالة 4444 موجودة مسبقاً')

    # تنفيذ الحوالة الجديدة
    count_query = 'SELECT COUNT(*) FROM BALANCE_TRANSACTIONS'
    count_before_exec = oracle.execute_query(count_query)[0][0]

    try:
        exec_query = """
        BEGIN
            EXECUTE_TRANSFER_ACCOUNTING(
                p_transfer_id => 4444,
                p_money_changer_id => 2,
                p_total_amount => 2500,
                p_currency_code => 'USD',
                p_supplier_distributions => '[{"supplier_id": 1, "amount": 2500}]',
                p_user_id => 1
            );
        END;
        """
        
        oracle.execute_update(exec_query)
        print('✅ تم تنفيذ الحوالة 4444 بنجاح!')
        
        count_after_exec = oracle.execute_query(count_query)[0][0]
        print(f'   معاملات تنفيذ جديدة: {count_after_exec - count_before_exec}')
        
        if count_after_exec > count_before_exec:
            return True
        else:
            return False
        
    except Exception as e:
        print(f'❌ خطأ في تنفيذ الحوالة: {str(e)}')
        return False

def show_final_summary():
    """عرض ملخص نهائي"""
    
    oracle = OracleManager()
    
    print('\n3️⃣ ملخص نهائي للنظام...')
    
    # إجمالي المعاملات
    count_query = 'SELECT COUNT(*) FROM BALANCE_TRANSACTIONS'
    total_count = oracle.execute_query(count_query)[0][0]
    print(f'📈 إجمالي المعاملات: {total_count}')

    # معاملات التنفيذ
    exec_count_query = "SELECT COUNT(*) FROM BALANCE_TRANSACTIONS WHERE document_type_code = 'TRANSFER'"
    exec_count = oracle.execute_query(exec_count_query)[0][0]
    print(f'🔄 معاملات التنفيذ: {exec_count}')

    # معاملات الإلغاء
    cancel_count_query = "SELECT COUNT(*) FROM BALANCE_TRANSACTIONS WHERE document_type_code = 'TRANSFER_CANCEL'"
    cancel_count = oracle.execute_query(cancel_count_query)[0][0]
    print(f'❌ معاملات الإلغاء: {cancel_count}')

    # آخر المعاملات
    print('\n📋 آخر 5 معاملات:')
    last_query = """
    SELECT entity_type_code, entity_id, document_type_code,
           debit_amount, credit_amount, document_number, created_date
    FROM BALANCE_TRANSACTIONS 
    ORDER BY created_date DESC
    FETCH FIRST 5 ROWS ONLY
    """
    
    last_result = oracle.execute_query(last_query)
    if last_result:
        for row in last_result:
            amount = row[3] if row[3] and row[3] > 0 else row[4]
            trans_type = 'مدين' if row[3] and row[3] > 0 else 'دائن'
            print(f'   {row[0]} {row[1]}: {trans_type} = {amount}, نوع: {row[2]}, مستند: {row[5]}')

if __name__ == "__main__":
    print("🎯 بدء الاختبار النهائي الشامل للنظام المحاسبي")
    print("=" * 80)
    
    cancellation_success = test_cancellation()
    execution_success = test_new_execution()
    
    show_final_summary()
    
    if cancellation_success and execution_success:
        print("\n🎉 النظام المحاسبي يعمل بكفاءة كاملة!")
        print("✅ تنفيذ الحوالات: يسجل في CURRENT_BALANCES و BALANCE_TRANSACTIONS")
        print("✅ إلغاء الحوالات: يسجل في CURRENT_BALANCES و BALANCE_TRANSACTIONS")
        print("✅ السجل التاريخي: محفوظ بالكامل")
        print("✅ المراجعة المحاسبية: ممكنة ودقيقة")
        print("✅ النظام عاد للعمل بكفاءة كما كان!")
        print("\n🏆 تم إصلاح جميع الكوارث المحاسبية بنجاح!")
    else:
        print("\n❌ ما زالت هناك مشاكل في النظام")
    
    print("\n" + "=" * 80)
    print("🏁 انتهى الاختبار النهائي الشامل")
