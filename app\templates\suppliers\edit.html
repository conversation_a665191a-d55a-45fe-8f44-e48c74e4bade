{% extends "base.html" %}

{% block title %}تعديل المورد - {{ supplier.name_ar }}{% endblock %}

{% block extra_css %}
<style>
    .supplier-edit-container {
        background: #f8f9fa;
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .page-header h1 {
        font-size: 2.5rem;
        font-weight: bold;
        margin: 0;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }
    
    .form-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: 1px solid #e9ecef;
    }
    
    .form-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        background: #f8f9fa;
    }
    
    .form-section h5 {
        color: #495057;
        font-weight: bold;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #667eea;
    }
    
    .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .form-label {
        font-weight: bold;
        color: #495057;
        margin-bottom: 8px;
    }
    
    .btn-save {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        color: white;
    }
    
    .btn-cancel {
        background: linear-gradient(45deg, #6c757d, #5a6268);
        border: none;
        color: white;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-cancel:hover {
        transform: translateY(-2px);
        color: white;
    }
    
    .required {
        color: #dc3545;
    }
    
    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }
    
    @media (max-width: 768px) {
        .page-header h1 {
            font-size: 2rem;
        }
        
        .form-container {
            padding: 20px;
        }
        
        .form-section {
            padding: 15px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="supplier-edit-container">
    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1><i class="fas fa-edit"></i> تعديل المورد</h1>
                        <p>تعديل بيانات المورد: {{ supplier.name_ar }}</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex align-items-center justify-content-end">
                            <i class="fas fa-user-edit fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نموذج التعديل -->
        <div class="container">
            <div class="form-container">
                <form id="supplierForm" method="POST">
                    
                    <!-- المعلومات الأساسية -->
                    <div class="form-section">
                        <h5><i class="fas fa-info-circle"></i> المعلومات الأساسية</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name_ar" class="form-label">
                                        <i class="fas fa-tag"></i> اسم المورد (عربي) <span class="required">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="name_ar" name="name_ar" 
                                           value="{{ supplier.name_ar }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name_en" class="form-label">
                                        <i class="fas fa-tag"></i> اسم المورد (إنجليزي)
                                    </label>
                                    <input type="text" class="form-control" id="name_en" name="name_en" 
                                           value="{{ supplier.name_en or '' }}">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="supplier_code" class="form-label">
                                        <i class="fas fa-barcode"></i> كود المورد
                                    </label>
                                    <input type="text" class="form-control" id="supplier_code" name="supplier_code" 
                                           value="{{ supplier.supplier_code or '' }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="supplier_type" class="form-label">
                                        <i class="fas fa-layer-group"></i> نوع المورد
                                    </label>
                                    <input type="text" class="form-control" id="supplier_type" name="supplier_type" 
                                           value="{{ supplier.supplier_type or '' }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="supplier_group_id" class="form-label">
                                        <i class="fas fa-users"></i> مجموعة المورد
                                    </label>
                                    <select class="form-control" id="supplier_group_id" name="supplier_group_id">
                                        <option value="">اختر مجموعة المورد</option>
                                        {% for group in supplier_groups %}
                                            <option value="{{ group.id }}" 
                                                    {% if supplier.supplier_group_id == group.id %}selected{% endif %}>
                                                {{ group.name }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات الاتصال -->
                    <div class="form-section">
                        <h5><i class="fas fa-address-book"></i> معلومات الاتصال</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_person" class="form-label">
                                        <i class="fas fa-user"></i> الشخص المسؤول
                                    </label>
                                    <input type="text" class="form-control" id="contact_person" name="contact_person" 
                                           value="{{ supplier.contact_person or '' }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">
                                        <i class="fas fa-phone"></i> الهاتف
                                    </label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="{{ supplier.phone or '' }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="mobile" class="form-label">
                                        <i class="fas fa-mobile-alt"></i> الجوال
                                    </label>
                                    <input type="tel" class="form-control" id="mobile" name="mobile" 
                                           value="{{ supplier.mobile or '' }}">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope"></i> البريد الإلكتروني
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="{{ supplier.email or '' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="website" class="form-label">
                                        <i class="fas fa-globe"></i> الموقع الإلكتروني
                                    </label>
                                    <input type="url" class="form-control" id="website" name="website" 
                                           value="{{ supplier.website or '' }}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- العنوان -->
                    <div class="form-section">
                        <h5><i class="fas fa-map-marker-alt"></i> العنوان</h5>
                        <div class="mb-3">
                            <label for="address" class="form-label">
                                <i class="fas fa-home"></i> العنوان التفصيلي
                            </label>
                            <textarea class="form-control" id="address" name="address" rows="3">{{ supplier.address or '' }}</textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="city" class="form-label">
                                        <i class="fas fa-city"></i> المدينة
                                    </label>
                                    <input type="text" class="form-control" id="city" name="city" 
                                           value="{{ supplier.city or '' }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="country" class="form-label">
                                        <i class="fas fa-flag"></i> الدولة
                                    </label>
                                    <input type="text" class="form-control" id="country" name="country" 
                                           value="{{ supplier.country or '' }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="postal_code" class="form-label">
                                        <i class="fas fa-mail-bulk"></i> الرمز البريدي
                                    </label>
                                    <input type="text" class="form-control" id="postal_code" name="postal_code" 
                                           value="{{ supplier.postal_code or '' }}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- المعلومات القانونية والمالية -->
                    <div class="form-section">
                        <h5><i class="fas fa-file-contract"></i> المعلومات القانونية والمالية</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="commercial_register" class="form-label">
                                        <i class="fas fa-certificate"></i> السجل التجاري
                                    </label>
                                    <input type="text" class="form-control" id="commercial_register" name="commercial_register" 
                                           value="{{ supplier.commercial_register or '' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tax_number" class="form-label">
                                        <i class="fas fa-receipt"></i> الرقم الضريبي
                                    </label>
                                    <input type="text" class="form-control" id="tax_number" name="tax_number" 
                                           value="{{ supplier.tax_number or '' }}">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="payment_terms" class="form-label">
                                        <i class="fas fa-calendar-alt"></i> شروط الدفع (أيام)
                                    </label>
                                    <input type="number" class="form-control" id="payment_terms" name="payment_terms" 
                                           value="{{ supplier.payment_terms or 30 }}" min="0">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="credit_limit" class="form-label">
                                        <i class="fas fa-dollar-sign"></i> حد الائتمان
                                    </label>
                                    <input type="number" class="form-control" id="credit_limit" name="credit_limit" 
                                           value="{{ supplier.credit_limit or 0 }}" min="0" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                               value="1" {% if supplier.is_active %}checked{% endif %}>
                                        <label class="form-check-label" for="is_active">
                                            <i class="fas fa-toggle-on"></i> مورد نشط
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- المعلومات المصرفية -->
                    <div class="form-section">
                        <h5><i class="fas fa-university"></i> المعلومات المصرفية</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="bank_name" class="form-label">
                                        <i class="fas fa-building"></i> اسم البنك
                                    </label>
                                    <input type="text" class="form-control" id="bank_name" name="bank_name" 
                                           value="{{ supplier.bank_name or '' }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="bank_account" class="form-label">
                                        <i class="fas fa-credit-card"></i> رقم الحساب
                                    </label>
                                    <input type="text" class="form-control" id="bank_account" name="bank_account" 
                                           value="{{ supplier.bank_account or '' }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="iban" class="form-label">
                                        <i class="fas fa-money-check"></i> رقم IBAN
                                    </label>
                                    <input type="text" class="form-control" id="iban" name="iban" 
                                           value="{{ supplier.iban or '' }}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات -->
                    <div class="form-section">
                        <h5><i class="fas fa-sticky-note"></i> ملاحظات</h5>
                        <div class="mb-3">
                            <textarea class="form-control" id="notes" name="notes" rows="4" 
                                      placeholder="أي ملاحظات إضافية عن المورد...">{{ supplier.notes or '' }}</textarea>
                        </div>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-save me-3">
                            <i class="fas fa-save"></i> حفظ التغييرات
                        </button>
                        <a href="{{ url_for('suppliers.view', id=supplier.id) }}" class="btn btn-cancel">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $('#supplierForm').on('submit', function(e) {
        e.preventDefault();
        
        // التحقق من البيانات المطلوبة
        const nameAr = $('#name_ar').val().trim();
        if (!nameAr) {
            alert('اسم المورد باللغة العربية مطلوب');
            $('#name_ar').focus();
            return;
        }
        
        // إرسال البيانات
        const formData = new FormData(this);
        
        $.ajax({
            url: window.location.href,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    alert('تم تحديث المورد بنجاح');
                    window.location.href = "{{ url_for('suppliers.view', id=supplier.id) }}";
                } else {
                    alert('خطأ: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                alert('خطأ في الاتصال بالخادم');
                console.error('Error:', error);
            }
        });
    });
});
</script>
{% endblock %}
