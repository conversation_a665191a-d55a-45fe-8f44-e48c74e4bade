#!/usr/bin/env python3
"""
إضافة المجلدات المفقودة لنظام البريد الإلكتروني
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_manager import DatabaseManager
import logging

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_missing_folders():
    """إضافة المجلدات المفقودة"""
    try:
        db = DatabaseManager()
        
        # الحصول على معرف الحساب الأول
        account_query = "SELECT id FROM email_accounts WHERE ROWNUM = 1"
        account_result = db.execute_query(account_query)

        if not account_result:
            logger.error("❌ لا يوجد حسابات بريد في النظام")
            return False

        account_id = account_result[0][0]
        logger.info(f"📧 معرف الحساب: {account_id}")
        
        # قائمة المجلدات المطلوبة
        folders_to_add = [
            {
                'name': 'Archive',
                'name_arabic': 'الأرشيف',
                'folder_type': 'archive',
                'sort_order': 5
            },
            {
                'name': 'Important',
                'name_arabic': 'المهمة',
                'folder_type': 'important',
                'sort_order': 6
            },
            {
                'name': 'Unread',
                'name_arabic': 'غير مقروءة',
                'folder_type': 'unread',
                'sort_order': 7
            }
        ]
        
        for folder in folders_to_add:
            try:
                # التحقق من وجود المجلد
                check_query = """
                SELECT id FROM email_folders
                WHERE account_id = :1 AND folder_type = :2
                """
                existing = db.execute_query(check_query, [account_id, folder['folder_type']])

                if existing:
                    logger.info(f"ℹ️ المجلد {folder['name_arabic']} موجود بالفعل")
                    continue

                # إضافة المجلد مع sequence
                insert_query = """
                INSERT INTO email_folders (
                    id, account_id, name, name_arabic, folder_type,
                    is_system, is_active, sort_order, created_at
                ) VALUES (
                    email_folders_seq.NEXTVAL, :1, :2, :3, :4, 1, 1, :5, SYSDATE
                )
                """

                db.execute_update(insert_query, [
                    account_id,
                    folder['name'],
                    folder['name_arabic'],
                    folder['folder_type'],
                    folder['sort_order']
                ])
                db.commit()
                
                logger.info(f"✅ تم إضافة المجلد: {folder['name_arabic']}")
                
            except Exception as e:
                logger.error(f"❌ خطأ في إضافة المجلد {folder['name_arabic']}: {e}")
        
        # عرض جميع المجلدات
        all_folders_query = """
        SELECT name_arabic, folder_type, sort_order
        FROM email_folders
        WHERE account_id = :1
        ORDER BY sort_order
        """

        all_folders = db.execute_query(all_folders_query, [account_id])
        if all_folders:
            logger.info("📁 المجلدات الموجودة:")
            for folder in all_folders:
                logger.info(f"   - {folder[0]} ({folder[1]}) - ترتيب: {folder[2]}")
        
        logger.info("🎉 تم الانتهاء من إضافة المجلدات المفقودة")
        
    except Exception as e:
        logger.error(f"❌ خطأ عام: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("📁 إضافة المجلدات المفقودة لنظام البريد الإلكتروني...")
    success = add_missing_folders()
    
    if success:
        print("✅ تم بنجاح!")
    else:
        print("❌ فشل في العملية!")
        sys.exit(1)
