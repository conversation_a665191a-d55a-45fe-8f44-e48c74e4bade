# -*- coding: utf-8 -*-
"""
نماذج عقود الشراء
Purchase Contracts Forms
"""

from flask_wtf import FlaskForm
from wtforms import (
    StringField, TextAreaField, SelectField, DecimalField, 
    DateField, IntegerField, FieldList, FormField, HiddenField
)
from wtforms.validators import DataRequired, Length, NumberRange, Optional
from datetime import datetime, date
from decimal import Decimal


class PurchaseContractDetailForm(FlaskForm):
    """نموذج تفاصيل عقد الشراء"""
    
    # معلومات الصنف
    item_code = StringField('رقم الصنف', validators=[DataRequired(), Length(max=50)])
    item_name = StringField('اسم الصنف', validators=[DataRequired(), Length(max=200)])
    item_id = SelectField('الصنف', coerce=int, validators=[Optional()])
    
    # معلومات الوحدة والعبوة
    unit_code = StringField('الوحدة', validators=[DataRequired(), Length(max=10)])
    pack_size = StringField('العبوة', validators=[Optional(), Length(max=50)])
    
    # التواريخ
    production_date = DateField('تاريخ الإنتاج', validators=[Optional()])
    expiry_date = DateField('تاريخ الانتهاء', validators=[Optional()])
    
    # الكميات والأسعار
    quantity = DecimalField('الكمية', validators=[DataRequired(), NumberRange(min=0.001)], places=3)
    unit_price = DecimalField('السعر', validators=[DataRequired(), NumberRange(min=0.0001)], places=4)
    free_quantity = DecimalField('الكمية المجانية', validators=[Optional(), NumberRange(min=0)], places=3, default=0)
    discount_percent = DecimalField('الخصم %', validators=[Optional(), NumberRange(min=0, max=100)], places=2, default=0)
    line_total = DecimalField('المجموع', validators=[Optional()], places=2)
    
    # وصف السطر
    line_description = TextAreaField('البيان', validators=[Optional()])
    
    # رقم السطر (مخفي)
    line_no = HiddenField()


class PurchaseContractForm(FlaskForm):
    """نموذج عقد الشراء الرئيسي"""
    
    # المعلومات الأساسية
    branch_no = StringField('رقم الفرع', validators=[DataRequired(), Length(max=10)], default='001')
    contract_no = StringField('رقم العقد', validators=[DataRequired(), Length(max=50)])
    contract_serial = IntegerField('تسلسل العقد', validators=[DataRequired()])
    
    # التواريخ
    contract_date = DateField('تاريخ العقد', validators=[DataRequired()], default=datetime.utcnow)
    contract_from_date = DateField('تاريخ البداية', validators=[DataRequired()])
    contract_to_date = DateField('تاريخ النهاية', validators=[DataRequired()])
    
    # معلومات المورد
    vendor_code = StringField('رقم المورد', validators=[DataRequired(), Length(max=20)])
    vendor_name = StringField('اسم المورد', validators=[DataRequired(), Length(max=200)])
    supplier_id = SelectField('المورد', coerce=int, validators=[Optional()])
    
    # المعلومات المالية
    currency_code = SelectField('العملة', validators=[DataRequired()], 
                               choices=[('SAR', 'ريال سعودي'), ('USD', 'دولار أمريكي'), 
                                       ('EUR', 'يورو'), ('GBP', 'جنيه إسترليني')],
                               default='SAR')
    contract_rate = DecimalField('سعر الصرف', validators=[DataRequired(), NumberRange(min=0.0001)], 
                                places=4, default=1.0000)
    contract_type = SelectField('نوع العقد', validators=[DataRequired()],
                               choices=[('إطاري', 'إطاري'), ('محدد', 'محدد'), 
                                       ('خدمات', 'خدمات'), ('توريد', 'توريد')])
    contract_amount = DecimalField('مبلغ العقد', validators=[DataRequired(), NumberRange(min=0)], places=2)
    discount_amount = DecimalField('مبلغ الخصم', validators=[Optional(), NumberRange(min=0)], 
                                  places=2, default=0)
    net_amount = DecimalField('صافي المبلغ', validators=[Optional()], places=2)
    
    # معلومات إضافية
    reference_no = StringField('رقم المرجع', validators=[Optional(), Length(max=100)])
    contract_desc = TextAreaField('وصف العقد', validators=[Optional()])
    status = SelectField('الحالة', validators=[DataRequired()],
                        choices=[('نشط', 'نشط'), ('منتهي', 'منتهي'), 
                                ('ملغي', 'ملغي'), ('معلق', 'معلق')],
                        default='نشط')
    contract_note = TextAreaField('ملاحظات', validators=[Optional()])
    
    # تفاصيل العقد
    details = FieldList(FormField(PurchaseContractDetailForm), min_entries=1)


class PurchaseContractSearchForm(FlaskForm):
    """نموذج البحث في عقود الشراء"""
    
    contract_no = StringField('رقم العقد', validators=[Optional(), Length(max=50)])
    vendor_name = StringField('اسم المورد', validators=[Optional(), Length(max=200)])
    contract_date_from = DateField('تاريخ العقد من', validators=[Optional()])
    contract_date_to = DateField('تاريخ العقد إلى', validators=[Optional()])
    status = SelectField('الحالة', validators=[Optional()],
                        choices=[('', 'جميع الحالات'), ('نشط', 'نشط'), 
                                ('منتهي', 'منتهي'), ('ملغي', 'ملغي'), ('معلق', 'معلق')])
    contract_type = SelectField('نوع العقد', validators=[Optional()],
                               choices=[('', 'جميع الأنواع'), ('إطاري', 'إطاري'), 
                                       ('محدد', 'محدد'), ('خدمات', 'خدمات'), ('توريد', 'توريد')])
    amount_from = DecimalField('المبلغ من', validators=[Optional(), NumberRange(min=0)], places=2)
    amount_to = DecimalField('المبلغ إلى', validators=[Optional(), NumberRange(min=0)], places=2)
