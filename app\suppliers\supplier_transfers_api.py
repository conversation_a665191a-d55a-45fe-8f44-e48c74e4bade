# -*- coding: utf-8 -*-
"""
API التكامل بين نظام الموردين ونظام الحوالات
Supplier-Transfer Integration API
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from app.database_manager import DatabaseManager
import logging
import json
from datetime import datetime, date
from decimal import Decimal

# إنشاء Blueprint
supplier_transfers_api_bp = Blueprint('supplier_transfers_api', __name__, url_prefix='/api/suppliers/transfers')

logger = logging.getLogger(__name__)

def decimal_default(obj):
    """تحويل Decimal إلى float للـ JSON"""
    if isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, (date, datetime)):
        return obj.isoformat()
    raise TypeError

@supplier_transfers_api_bp.route('/outstanding-invoices/<int:supplier_id>')
@login_required
def get_supplier_outstanding_invoices(supplier_id):
    """API للحصول على الفواتير المستحقة للمورد"""
    try:
        db = DatabaseManager()
        
        query = """
        SELECT 
            transaction_id,
            reference_number as invoice_number,
            transaction_date as invoice_date,
            due_date,
            currency_code,
            original_amount,
            outstanding_amount,
            due_status,
            days_overdue,
            days_until_due,
            currency_symbol,
            outstanding_base_currency
        FROM V_SUPPLIER_OUTSTANDING_INVOICES
        WHERE supplier_id = :1
        ORDER BY due_date ASC
        """
        
        results = db.execute_query(query, [supplier_id])
        
        outstanding_invoices = []
        for row in results:
            outstanding_invoices.append({
                'transaction_id': row[0],
                'invoice_number': row[1],
                'invoice_date': row[2].strftime('%Y-%m-%d') if row[2] else None,
                'due_date': row[3].strftime('%Y-%m-%d') if row[3] else None,
                'currency_code': row[4],
                'original_amount': float(row[5]) if row[5] else 0,
                'outstanding_amount': float(row[6]) if row[6] else 0,
                'due_status': row[7],
                'days_overdue': int(row[8]) if row[8] else 0,
                'days_until_due': int(row[9]) if row[9] else 0,
                'currency_symbol': row[10],
                'outstanding_base_currency': float(row[11]) if row[11] else 0
            })
        
        return jsonify({
            'success': True,
            'outstanding_invoices': outstanding_invoices,
            'total_count': len(outstanding_invoices),
            'total_outstanding': sum(inv['outstanding_base_currency'] for inv in outstanding_invoices)
        })
        
    except Exception as e:
        logger.error(f"خطأ في جلب الفواتير المستحقة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@supplier_transfers_api_bp.route('/balances/<int:supplier_id>')
@login_required
def get_supplier_balances(supplier_id):
    """API للحصول على أرصدة المورد بجميع العملات"""
    try:
        db = DatabaseManager()
        
        query = """
        SELECT 
            sb.currency_code,
            sb.current_balance,
            sb.credit_limit,
            sb.last_transaction_date,
            sb.last_payment_date,
            sb.total_invoices_count,
            sb.total_payments_count,
            sb.average_payment_days,
            c.symbol as currency_symbol,
            c.name_ar as currency_name,
            c.exchange_rate,
            sb.current_balance * c.exchange_rate as balance_base_currency
        FROM SUPPLIER_BALANCES sb
        JOIN CURRENCIES c ON sb.currency_code = c.code
        WHERE sb.supplier_id = :1
        ORDER BY c.is_base_currency DESC, sb.current_balance DESC
        """
        
        results = db.execute_query(query, [supplier_id])
        
        balances = []
        total_balance_base = 0
        
        for row in results:
            balance_data = {
                'currency_code': row[0],
                'current_balance': float(row[1]) if row[1] else 0,
                'credit_limit': float(row[2]) if row[2] else 0,
                'last_transaction_date': row[3].strftime('%Y-%m-%d') if row[3] else None,
                'last_payment_date': row[4].strftime('%Y-%m-%d') if row[4] else None,
                'total_invoices_count': int(row[5]) if row[5] else 0,
                'total_payments_count': int(row[6]) if row[6] else 0,
                'average_payment_days': float(row[7]) if row[7] else 0,
                'currency_symbol': row[8],
                'currency_name': row[9],
                'exchange_rate': float(row[10]) if row[10] else 1,
                'balance_base_currency': float(row[11]) if row[11] else 0
            }
            balances.append(balance_data)
            total_balance_base += balance_data['balance_base_currency']
        
        return jsonify({
            'success': True,
            'balances': balances,
            'total_balance_base_currency': total_balance_base,
            'currencies_count': len(balances)
        })
        
    except Exception as e:
        logger.error(f"خطأ في جلب أرصدة المورد: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@supplier_transfers_api_bp.route('/create-payment-request', methods=['POST'])
@login_required
def create_supplier_payment_request():
    """API لإنشاء طلب دفع مورد عبر نظام الحوالات"""
    try:
        db = DatabaseManager()
        data = request.get_json()
        
        # التحقق من البيانات المطلوبة
        required_fields = ['supplier_id', 'amount', 'currency_code', 'money_changer_id', 'payment_purpose']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'success': False, 'message': f'الحقل {field} مطلوب'}), 400
        
        supplier_id = data['supplier_id']
        amount = float(data['amount'])
        currency_code = data['currency_code']
        money_changer_id = data['money_changer_id']
        payment_purpose = data['payment_purpose']
        invoice_numbers = data.get('invoice_numbers', [])
        notes = data.get('notes', '')
        discount_amount = float(data.get('discount_amount', 0))
        tax_amount = float(data.get('tax_amount', 0))
        
        # التحقق من وجود المورد
        supplier_check = db.execute_query(
            "SELECT id, name_ar, phone, email, bank_name, bank_account FROM SUPPLIERS WHERE id = :1", 
            [supplier_id]
        )
        if not supplier_check:
            return jsonify({'success': False, 'message': 'المورد غير موجود'}), 404
        
        supplier_data = supplier_check[0]
        supplier_name = supplier_data[1]
        
        # إنشاء أو تحديث مستفيد للمورد
        beneficiary_query = """
        SELECT id FROM BENEFICIARIES 
        WHERE supplier_id = :1 AND type = 'supplier'
        """
        beneficiary_result = db.execute_query(beneficiary_query, [supplier_id])
        
        if beneficiary_result:
            beneficiary_id = beneficiary_result[0][0]
        else:
            # إنشاء مستفيد جديد
            beneficiary_insert = """
            INSERT INTO BENEFICIARIES (
                beneficiary_name, type, supplier_id, bank_account, bank_name,
                phone, email, is_active, created_at, created_by
            ) VALUES (
                :1, 'supplier', :2, :3, :4, :5, :6, 1, CURRENT_TIMESTAMP, :7
            )
            """
            
            beneficiary_id = db.get_next_sequence_value('BENEFICIARIES_SEQ')
            db.execute_update(beneficiary_insert, [
                supplier_name,
                supplier_id,
                supplier_data[5] or 'غير محدد',  # bank_account
                supplier_data[4] or 'غير محدد',  # bank_name
                supplier_data[2],  # phone
                supplier_data[3],  # email
                current_user.id
            ])
        
        # إنشاء رقم طلب فريد باستخدام النظام الذكي
        try:
            # استخدام الدالة الذكية الجديدة
            number_result = db.execute_query("SELECT GENERATE_SMART_TR_NUMBER() FROM DUAL")
            request_number = number_result[0][0] if number_result and number_result[0][0] else None

            if not request_number:
                # في حالة فشل النظام الذكي، استخدم نمط مخصص للموردين
                current_year = datetime.now().year
                request_number = f"SP-{current_year}-{supplier_id:04d}"
        except Exception as e:
            logger.warning(f"فشل في استخدام النظام الذكي: {e}")
            # استخدام نمط مخصص للموردين
            current_year = datetime.now().year
            request_number = f"SP-{current_year}-{supplier_id:04d}"
        
        # إنشاء طلب حوالة
        transfer_request_query = """
        INSERT INTO TRANSFER_REQUESTS (
            request_number, beneficiary_id, amount, currency, purpose, notes,
            branch_id, status, created_by, updated_by, total_amount, 
            delivery_method, transfer_type, money_changer_bank_id,
            supplier_id, payment_type, payment_due_date, discount_amount,
            tax_amount, net_payment_amount, created_at, updated_at
        ) VALUES (
            :1, :2, :3, :4, :5, :6, 1, 'pending', :7, :8, :9, 
            'bank_transfer', 'supplier_payment', :10, :11, 'SUPPLIER_PAYMENT',
            NULL, :12, :13, :14, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        )
        """
        
        net_amount = amount - discount_amount - tax_amount
        transfer_request_id = db.get_next_sequence_value('TRANSFER_REQUESTS_SEQ')
        
        db.execute_update(transfer_request_query, [
            request_number, beneficiary_id, amount, currency_code, payment_purpose, notes,
            current_user.id, current_user.id, amount, money_changer_id, supplier_id,
            discount_amount, tax_amount, net_amount
        ])
        
        # إنشاء سجل في جدول مدفوعات الموردين
        supplier_payment_query = """
        INSERT INTO SUPPLIER_PAYMENT_TRANSFERS (
            supplier_id, transfer_request_id, payment_amount, currency_code,
            payment_purpose, invoice_numbers, payment_status, payment_method,
            discount_applied, tax_withheld, net_amount_transferred,
            requested_date, created_at, created_by
        ) VALUES (
            :1, :2, :3, :4, :5, :6, 'PENDING', 'BANK_TRANSFER',
            :7, :8, :9, SYSDATE, CURRENT_TIMESTAMP, :10
        )
        """
        
        db.execute_update(supplier_payment_query, [
            supplier_id, transfer_request_id, amount, currency_code,
            payment_purpose, json.dumps(invoice_numbers), 
            discount_amount, tax_amount, net_amount, current_user.id
        ])
        
        # إنشاء معاملة في حساب المورد
        supplier_transaction_query = """
        INSERT INTO SUPPLIER_TRANSACTIONS (
            supplier_id, transaction_type, reference_type, reference_id, reference_number,
            transaction_date, currency_code, original_amount, credit_amount,
            description, status, created_date, created_by
        ) VALUES (
            :1, 'PAYMENT_REQUEST', 'TRANSFER_REQUEST', :2, :3, CURRENT_TIMESTAMP,
            :4, :5, :6, :7, 'PENDING', CURRENT_TIMESTAMP, :8
        )
        """
        
        db.execute_update(supplier_transaction_query, [
            supplier_id, transfer_request_id, request_number, currency_code,
            amount, amount, f'طلب دفع للمورد {supplier_name} - {payment_purpose}', current_user.id
        ])
        
        # تخصيص المدفوعات للفواتير المحددة
        if invoice_numbers:
            for invoice_data in invoice_numbers:
                if isinstance(invoice_data, dict) and 'transaction_id' in invoice_data:
                    allocation_query = """
                    INSERT INTO SUPPLIER_PAYMENT_ALLOCATIONS (
                        supplier_payment_transfer_id, supplier_transaction_id,
                        allocated_amount, currency_code, allocation_type,
                        allocation_date, created_at, created_by
                    ) VALUES (
                        (SELECT id FROM SUPPLIER_PAYMENT_TRANSFERS WHERE transfer_request_id = :1),
                        :2, :3, :4, 'INVOICE', SYSDATE, CURRENT_TIMESTAMP, :5
                    )
                    """
                    
                    db.execute_update(allocation_query, [
                        transfer_request_id,
                        invoice_data['transaction_id'],
                        invoice_data.get('allocated_amount', 0),
                        currency_code,
                        current_user.id
                    ])
        
        return jsonify({
            'success': True,
            'message': 'تم إنشاء طلب الدفع بنجاح',
            'transfer_request_id': transfer_request_id,
            'request_number': request_number,
            'net_amount': net_amount
        })
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء طلب الدفع: {e}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'}), 500

@supplier_transfers_api_bp.route('/payment-status/<int:transfer_request_id>')
@login_required
def get_payment_status(transfer_request_id):
    """API للحصول على حالة طلب الدفع"""
    try:
        db = DatabaseManager()
        
        query = """
        SELECT 
            payment_id,
            supplier_name,
            request_number,
            transfer_number,
            payment_amount,
            currency_code,
            currency_symbol,
            payment_status,
            request_status,
            transfer_status,
            requested_date,
            approved_date,
            executed_date,
            completed_date,
            money_changer_name,
            transfer_reference
        FROM V_SUPPLIER_PAYMENTS_DETAILED
        WHERE transfer_request_id = :1
        """
        
        result = db.execute_query(query, [transfer_request_id])
        
        if not result:
            return jsonify({'success': False, 'message': 'طلب الدفع غير موجود'}), 404
        
        row = result[0]
        payment_data = {
            'payment_id': row[0],
            'supplier_name': row[1],
            'request_number': row[2],
            'transfer_number': row[3],
            'payment_amount': float(row[4]) if row[4] else 0,
            'currency_code': row[5],
            'currency_symbol': row[6],
            'payment_status': row[7],
            'request_status': row[8],
            'transfer_status': row[9],
            'requested_date': row[10].strftime('%Y-%m-%d %H:%M:%S') if row[10] else None,
            'approved_date': row[11].strftime('%Y-%m-%d %H:%M:%S') if row[11] else None,
            'executed_date': row[12].strftime('%Y-%m-%d %H:%M:%S') if row[12] else None,
            'completed_date': row[13].strftime('%Y-%m-%d %H:%M:%S') if row[13] else None,
            'money_changer_name': row[14],
            'transfer_reference': row[15]
        }
        
        return jsonify({
            'success': True,
            'payment_data': payment_data
        })

    except Exception as e:
        logger.error(f"خطأ في جلب حالة الدفع: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@supplier_transfers_api_bp.route('/payments-history/<int:supplier_id>')
@login_required
def get_supplier_payments_history(supplier_id):
    """API للحصول على تاريخ مدفوعات المورد"""
    try:
        db = DatabaseManager()

        # معاملات البحث
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status_filter = request.args.get('status', '')
        currency_filter = request.args.get('currency', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')

        # بناء الاستعلام
        base_query = """
        SELECT
            payment_id,
            request_number,
            transfer_number,
            payment_amount,
            currency_code,
            currency_symbol,
            payment_status,
            payment_method,
            requested_date,
            completed_date,
            money_changer_name,
            payment_purpose
        FROM V_SUPPLIER_PAYMENTS_DETAILED
        WHERE supplier_id = :1
        """

        params = [supplier_id]
        param_counter = 2

        if status_filter:
            base_query += f" AND payment_status = :{param_counter}"
            params.append(status_filter)
            param_counter += 1

        if currency_filter:
            base_query += f" AND currency_code = :{param_counter}"
            params.append(currency_filter)
            param_counter += 1

        if date_from:
            base_query += f" AND requested_date >= TO_DATE(:{param_counter}, 'YYYY-MM-DD')"
            params.append(date_from)
            param_counter += 1

        if date_to:
            base_query += f" AND requested_date <= TO_DATE(:{param_counter}, 'YYYY-MM-DD') + 1"
            params.append(date_to)
            param_counter += 1

        base_query += " ORDER BY requested_date DESC"

        # تنفيذ الاستعلام
        results = db.execute_query(base_query, params)

        payments = []
        for row in results:
            payments.append({
                'payment_id': row[0],
                'request_number': row[1],
                'transfer_number': row[2],
                'payment_amount': float(row[3]) if row[3] else 0,
                'currency_code': row[4],
                'currency_symbol': row[5],
                'payment_status': row[6],
                'payment_method': row[7],
                'requested_date': row[8].strftime('%Y-%m-%d %H:%M:%S') if row[8] else None,
                'completed_date': row[9].strftime('%Y-%m-%d %H:%M:%S') if row[9] else None,
                'money_changer_name': row[10],
                'payment_purpose': row[11]
            })

        # حساب الإحصائيات
        total_payments = len(payments)
        total_amount = sum(p['payment_amount'] for p in payments)
        completed_payments = len([p for p in payments if p['payment_status'] == 'COMPLETED'])

        return jsonify({
            'success': True,
            'payments': payments,
            'pagination': {
                'total_count': total_payments,
                'page': page,
                'per_page': per_page
            },
            'statistics': {
                'total_payments': total_payments,
                'total_amount': total_amount,
                'completed_payments': completed_payments,
                'completion_rate': (completed_payments / total_payments * 100) if total_payments > 0 else 0
            }
        })

    except Exception as e:
        logger.error(f"خطأ في جلب تاريخ المدفوعات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@supplier_transfers_api_bp.route('/update-payment-status', methods=['POST'])
@login_required
def update_payment_status():
    """API لتحديث حالة المدفوعات (للاستخدام الداخلي من نظام الحوالات)"""
    try:
        db = DatabaseManager()
        data = request.get_json()

        transfer_id = data.get('transfer_id')
        new_status = data.get('status')
        notes = data.get('notes', '')

        if not transfer_id or not new_status:
            return jsonify({'success': False, 'message': 'معرف الحوالة والحالة مطلوبان'}), 400

        # تحديث حالة الدفعة
        update_query = """
        UPDATE SUPPLIER_PAYMENT_TRANSFERS SET
            payment_status = :1,
            updated_at = CURRENT_TIMESTAMP,
            updated_by = :2
        WHERE transfer_id = :3
        """

        # تحديث التواريخ حسب الحالة
        if new_status == 'APPROVED':
            update_query = update_query.replace(
                "updated_by = :2",
                "approved_date = SYSDATE, updated_by = :2"
            )
        elif new_status == 'EXECUTED':
            update_query = update_query.replace(
                "updated_by = :2",
                "executed_date = SYSDATE, updated_by = :2"
            )
        elif new_status == 'COMPLETED':
            update_query = update_query.replace(
                "updated_by = :2",
                "completed_date = SYSDATE, updated_by = :2"
            )

        rows_updated = db.execute_update(update_query, [new_status, current_user.id, transfer_id])

        if rows_updated == 0:
            return jsonify({'success': False, 'message': 'لم يتم العثور على الدفعة'}), 404

        # إذا كانت الحالة مكتملة، قم بمعالجة الدفعة
        if new_status == 'COMPLETED':
            try:
                # استدعاء الإجراء المخزن لمعالجة الدفعة المكتملة
                db.execute_procedure('PROCESS_COMPLETED_SUPPLIER_PAYMENT', [transfer_id])
            except Exception as proc_error:
                logger.error(f"خطأ في معالجة الدفعة المكتملة: {proc_error}")
                # لا نرجع خطأ هنا لأن تحديث الحالة تم بنجاح

        return jsonify({
            'success': True,
            'message': f'تم تحديث حالة الدفعة إلى {new_status}',
            'transfer_id': transfer_id,
            'new_status': new_status
        })

    except Exception as e:
        logger.error(f"خطأ في تحديث حالة الدفعة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@supplier_transfers_api_bp.route('/money-changers')
@login_required
def get_available_money_changers():
    """API للحصول على قائمة الصرافين والبنوك المتاحة"""
    try:
        db = DatabaseManager()

        query = """
        SELECT
            id, name, type, contact_person, phone, email,
            commission_rate, is_active
        FROM MONEY_CHANGERS_BANKS
        WHERE is_active = 1
        ORDER BY type, name
        """

        results = db.execute_query(query)

        money_changers = []
        for row in results:
            money_changers.append({
                'id': row[0],
                'name': row[1],
                'type': row[2],
                'contact_person': row[3],
                'phone': row[4],
                'email': row[5],
                'commission_rate': float(row[6]) if row[6] else 0,
                'is_active': bool(row[7])
            })

        return jsonify({
            'success': True,
            'money_changers': money_changers,
            'total_count': len(money_changers)
        })

    except Exception as e:
        logger.error(f"خطأ في جلب قائمة الصرافين: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@supplier_transfers_api_bp.route('/currencies')
@login_required
def get_available_currencies():
    """API للحصول على قائمة العملات المتاحة"""
    try:
        db = DatabaseManager()

        query = """
        SELECT
            code, name_ar, name_en, symbol, exchange_rate,
            is_base_currency, decimal_places
        FROM CURRENCIES
        WHERE is_active = 1
        ORDER BY is_base_currency DESC, name_ar
        """

        results = db.execute_query(query)

        currencies = []
        for row in results:
            currencies.append({
                'code': row[0],
                'name_ar': row[1],
                'name_en': row[2],
                'symbol': row[3],
                'exchange_rate': float(row[4]) if row[4] else 1,
                'is_base_currency': bool(row[5]),
                'decimal_places': int(row[6]) if row[6] else 2
            })

        return jsonify({
            'success': True,
            'currencies': currencies,
            'total_count': len(currencies)
        })

    except Exception as e:
        logger.error(f"خطأ في جلب قائمة العملات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@supplier_transfers_api_bp.route('/tracking-history/<int:payment_id>')
@login_required
def get_payment_tracking_history(payment_id):
    """API للحصول على تاريخ تتبع الدفعة"""
    try:
        from app.suppliers.payment_tracking_service import payment_tracking_service

        tracking_history = payment_tracking_service.get_payment_tracking_history(payment_id)

        return jsonify({
            'success': True,
            'tracking_history': tracking_history,
            'total_count': len(tracking_history)
        })

    except Exception as e:
        logger.error(f"خطأ في جلب تاريخ تتبع الدفعة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@supplier_transfers_api_bp.route('/overdue-payments')
@login_required
def get_overdue_payments():
    """API للحصول على المدفوعات المتأخرة"""
    try:
        from app.suppliers.payment_tracking_service import payment_tracking_service

        days_threshold = request.args.get('days_threshold', 7, type=int)
        overdue_payments = payment_tracking_service.get_overdue_payments_report(days_threshold)

        return jsonify({
            'success': True,
            'overdue_payments': overdue_payments,
            'total_count': len(overdue_payments),
            'days_threshold': days_threshold
        })

    except Exception as e:
        logger.error(f"خطأ في جلب المدفوعات المتأخرة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@supplier_transfers_api_bp.route('/payments-list')
@login_required
def get_payments_list():
    """API للحصول على قائمة المدفوعات مع الفلاتر"""
    try:
        db = DatabaseManager()

        # معاملات البحث
        supplier_id = request.args.get('supplier_id', '')
        status_filter = request.args.get('status', '')
        currency_filter = request.args.get('currency', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')

        # بناء الاستعلام
        base_query = """
        SELECT
            payment_id,
            supplier_name,
            request_number,
            transfer_number,
            payment_amount,
            currency_code,
            currency_symbol,
            payment_status,
            payment_purpose,
            requested_date,
            money_changer_name
        FROM V_SUPPLIER_PAYMENTS_DETAILED
        WHERE 1=1
        """

        params = []
        param_counter = 1

        if supplier_id:
            base_query += f" AND supplier_id = :{param_counter}"
            params.append(supplier_id)
            param_counter += 1

        if status_filter:
            base_query += f" AND payment_status = :{param_counter}"
            params.append(status_filter)
            param_counter += 1

        if currency_filter:
            base_query += f" AND currency_code = :{param_counter}"
            params.append(currency_filter)
            param_counter += 1

        if date_from:
            base_query += f" AND requested_date >= TO_DATE(:{param_counter}, 'YYYY-MM-DD')"
            params.append(date_from)
            param_counter += 1

        if date_to:
            base_query += f" AND requested_date <= TO_DATE(:{param_counter}, 'YYYY-MM-DD') + 1"
            params.append(date_to)
            param_counter += 1

        base_query += " ORDER BY requested_date DESC"

        # تنفيذ الاستعلام
        results = db.execute_query(base_query, params)

        payments = []
        for row in results:
            payments.append({
                'payment_id': row[0],
                'supplier_name': row[1],
                'request_number': row[2],
                'transfer_number': row[3],
                'payment_amount': float(row[4]) if row[4] else 0,
                'currency_code': row[5],
                'currency_symbol': row[6],
                'payment_status': row[7],
                'payment_purpose': row[8],
                'requested_date': row[9].strftime('%Y-%m-%d %H:%M:%S') if row[9] else None,
                'money_changer_name': row[10]
            })

        return jsonify({
            'success': True,
            'payments': payments,
            'total_count': len(payments)
        })

    except Exception as e:
        logger.error(f"خطأ في جلب قائمة المدفوعات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@supplier_transfers_api_bp.route('/payment-details/<int:payment_id>')
@login_required
def get_payment_details(payment_id):
    """API للحصول على تفاصيل الدفعة"""
    try:
        db = DatabaseManager()

        # تفاصيل الدفعة الأساسية
        payment_query = """
        SELECT
            payment_id,
            supplier_name,
            request_number,
            transfer_number,
            payment_amount,
            currency_code,
            currency_symbol,
            payment_status,
            payment_purpose,
            payment_method,
            discount_applied,
            tax_withheld,
            net_amount_transferred,
            requested_date,
            approved_date,
            executed_date,
            completed_date,
            money_changer_name,
            beneficiary_name,
            bank_name,
            bank_account
        FROM V_SUPPLIER_PAYMENTS_DETAILED
        WHERE payment_id = :1
        """

        payment_result = db.execute_query(payment_query, [payment_id])

        if not payment_result:
            return jsonify({'success': False, 'message': 'الدفعة غير موجودة'}), 404

        row = payment_result[0]
        payment_data = {
            'payment_id': row[0],
            'supplier_name': row[1],
            'request_number': row[2],
            'transfer_number': row[3],
            'payment_amount': float(row[4]) if row[4] else 0,
            'currency_code': row[5],
            'currency_symbol': row[6],
            'payment_status': row[7],
            'payment_purpose': row[8],
            'payment_method': row[9],
            'discount_applied': float(row[10]) if row[10] else 0,
            'tax_withheld': float(row[11]) if row[11] else 0,
            'net_amount_transferred': float(row[12]) if row[12] else 0,
            'requested_date': row[13].strftime('%Y-%m-%d %H:%M:%S') if row[13] else None,
            'approved_date': row[14].strftime('%Y-%m-%d %H:%M:%S') if row[14] else None,
            'executed_date': row[15].strftime('%Y-%m-%d %H:%M:%S') if row[15] else None,
            'completed_date': row[16].strftime('%Y-%m-%d %H:%M:%S') if row[16] else None,
            'money_changer_name': row[17],
            'beneficiary_name': row[18],
            'bank_name': row[19],
            'bank_account': row[20]
        }

        # تخصيصات الفواتير
        allocations_query = """
        SELECT
            st.reference_number as invoice_number,
            spa.allocated_amount,
            spa.currency_code,
            spa.allocation_date
        FROM SUPPLIER_PAYMENT_ALLOCATIONS spa
        JOIN SUPPLIER_TRANSACTIONS st ON spa.supplier_transaction_id = st.transaction_id
        WHERE spa.supplier_payment_transfer_id = (
            SELECT id FROM SUPPLIER_PAYMENT_TRANSFERS WHERE id = :1
        )
        """

        allocations_result = db.execute_query(allocations_query, [payment_id])

        allocations = []
        for alloc_row in allocations_result:
            allocations.append({
                'invoice_number': alloc_row[0],
                'allocated_amount': float(alloc_row[1]) if alloc_row[1] else 0,
                'currency_code': alloc_row[2],
                'allocation_date': alloc_row[3].strftime('%Y-%m-%d') if alloc_row[3] else None
            })

        payment_data['allocations'] = allocations

        return jsonify({
            'success': True,
            'payment_data': payment_data
        })

    except Exception as e:
        logger.error(f"خطأ في جلب تفاصيل الدفعة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500
