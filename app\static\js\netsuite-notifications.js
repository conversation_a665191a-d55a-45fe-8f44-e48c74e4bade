/**
 * NetSuite Notifications System
 * نظام الإشعارات والتنبيهات المتقدم بنمط NetSuite Oracle
 */

class NetSuiteNotifications {
    constructor() {
        this.container = null;
        this.notifications = new Map();
        this.defaultOptions = {
            duration: 5000,
            position: 'top-left',
            showIcon: true,
            closable: true,
            pauseOnHover: true
        };
        
        this.init();
    }
    
    init() {
        this.createContainer();
        this.bindEvents();
    }
    
    createContainer() {
        // إنشاء حاوي الإشعارات إذا لم يكن موجوداً
        this.container = document.querySelector('.ns-notifications');
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.className = 'ns-notifications';
            document.body.appendChild(this.container);
        }
    }
    
    bindEvents() {
        // إيقاف مؤقت الإخفاء عند التمرير
        this.container.addEventListener('mouseenter', (e) => {
            const notification = e.target.closest('.ns-notification');
            if (notification && notification.timer) {
                clearTimeout(notification.timer);
            }
        });
        
        // استئناف مؤقت الإخفاء عند مغادرة المؤشر
        this.container.addEventListener('mouseleave', (e) => {
            const notification = e.target.closest('.ns-notification');
            if (notification && notification.dataset.duration > 0) {
                this.setAutoHide(notification, parseInt(notification.dataset.duration));
            }
        });
    }
    
    // عرض إشعار
    show(message, type = 'info', options = {}) {
        const config = { ...this.defaultOptions, ...options };
        const id = this.generateId();
        
        const notification = this.createNotification(id, message, type, config);
        this.container.appendChild(notification);
        
        // إظهار الإشعار مع تأثير الحركة
        requestAnimationFrame(() => {
            notification.classList.add('show');
        });
        
        // إعداد الإخفاء التلقائي
        if (config.duration > 0) {
            this.setAutoHide(notification, config.duration);
        }
        
        // حفظ مرجع الإشعار
        this.notifications.set(id, notification);
        
        return id;
    }
    
    // إنشاء عنصر الإشعار
    createNotification(id, message, type, options) {
        const notification = document.createElement('div');
        notification.className = `ns-notification ns-notification-${type}`;
        notification.dataset.id = id;
        notification.dataset.duration = options.duration;
        
        const icon = this.getIcon(type);
        const iconHtml = options.showIcon ? `<i class="ns-notification-icon ${type} ${icon}"></i>` : '';
        const closeHtml = options.closable ? `
            <button class="ns-notification-close" onclick="nsNotifications.close('${id}')">
                <i class="fas fa-times"></i>
            </button>
        ` : '';
        
        notification.innerHTML = `
            <div class="ns-notification-content">
                ${iconHtml}
                <span class="ns-notification-message">${message}</span>
                ${closeHtml}
            </div>
        `;
        
        return notification;
    }
    
    // الحصول على أيقونة حسب النوع
    getIcon(type) {
        const icons = {
            info: 'fas fa-info-circle',
            success: 'fas fa-check-circle',
            warning: 'fas fa-exclamation-triangle',
            error: 'fas fa-times-circle'
        };
        return icons[type] || icons.info;
    }
    
    // إعداد الإخفاء التلقائي
    setAutoHide(notification, duration) {
        notification.timer = setTimeout(() => {
            this.close(notification.dataset.id);
        }, duration);
    }
    
    // إغلاق إشعار
    close(id) {
        const notification = this.notifications.get(id);
        if (!notification) return;
        
        // إلغاء المؤقت
        if (notification.timer) {
            clearTimeout(notification.timer);
        }
        
        // إخفاء الإشعار مع تأثير الحركة
        notification.classList.remove('show');
        
        // إزالة الإشعار من DOM
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            this.notifications.delete(id);
        }, 300);
    }
    
    // إغلاق جميع الإشعارات
    closeAll() {
        this.notifications.forEach((notification, id) => {
            this.close(id);
        });
    }
    
    // توليد معرف فريد
    generateId() {
        return 'ns-notification-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }
    
    // طرق مختصرة لأنواع الإشعارات المختلفة
    info(message, options = {}) {
        return this.show(message, 'info', options);
    }
    
    success(message, options = {}) {
        return this.show(message, 'success', options);
    }
    
    warning(message, options = {}) {
        return this.show(message, 'warning', options);
    }
    
    error(message, options = {}) {
        return this.show(message, 'error', options);
    }
    
    // إشعار مع شريط تقدم
    progress(message, options = {}) {
        const config = { 
            ...this.defaultOptions, 
            ...options,
            duration: 0, // لا إخفاء تلقائي
            closable: false 
        };
        
        const id = this.generateId();
        const notification = this.createProgressNotification(id, message, config);
        this.container.appendChild(notification);
        
        requestAnimationFrame(() => {
            notification.classList.add('show');
        });
        
        this.notifications.set(id, notification);
        
        return {
            id: id,
            updateProgress: (percent) => this.updateProgress(id, percent),
            complete: (successMessage) => this.completeProgress(id, successMessage),
            error: (errorMessage) => this.errorProgress(id, errorMessage)
        };
    }
    
    // إنشاء إشعار مع شريط تقدم
    createProgressNotification(id, message, options) {
        const notification = document.createElement('div');
        notification.className = 'ns-notification ns-notification-info';
        notification.dataset.id = id;
        
        notification.innerHTML = `
            <div class="ns-notification-content">
                <i class="ns-notification-icon info fas fa-spinner fa-spin"></i>
                <div style="flex: 1;">
                    <div class="ns-notification-message">${message}</div>
                    <div class="ns-progress" style="margin-top: 0.5rem;">
                        <div class="ns-progress-bar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;
        
        return notification;
    }
    
    // تحديث شريط التقدم
    updateProgress(id, percent) {
        const notification = this.notifications.get(id);
        if (!notification) return;
        
        const progressBar = notification.querySelector('.ns-progress-bar');
        if (progressBar) {
            progressBar.style.width = `${Math.min(100, Math.max(0, percent))}%`;
        }
    }
    
    // إكمال شريط التقدم بنجاح
    completeProgress(id, successMessage) {
        const notification = this.notifications.get(id);
        if (!notification) return;
        
        // تحديث الأيقونة والرسالة
        const icon = notification.querySelector('.ns-notification-icon');
        const message = notification.querySelector('.ns-notification-message');
        const progressBar = notification.querySelector('.ns-progress-bar');
        
        if (icon) {
            icon.className = 'ns-notification-icon success fas fa-check-circle';
        }
        
        if (message && successMessage) {
            message.textContent = successMessage;
        }
        
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.parentElement.classList.add('ns-progress-success');
        }
        
        notification.className = 'ns-notification ns-notification-success show';
        
        // إغلاق تلقائي بعد ثانيتين
        setTimeout(() => this.close(id), 2000);
    }
    
    // إكمال شريط التقدم بخطأ
    errorProgress(id, errorMessage) {
        const notification = this.notifications.get(id);
        if (!notification) return;
        
        // تحديث الأيقونة والرسالة
        const icon = notification.querySelector('.ns-notification-icon');
        const message = notification.querySelector('.ns-notification-message');
        const progressBar = notification.querySelector('.ns-progress-bar');
        
        if (icon) {
            icon.className = 'ns-notification-icon error fas fa-times-circle';
        }
        
        if (message && errorMessage) {
            message.textContent = errorMessage;
        }
        
        if (progressBar) {
            progressBar.parentElement.classList.add('ns-progress-error');
        }
        
        notification.className = 'ns-notification ns-notification-error show';
        
        // إضافة زر إغلاق
        const content = notification.querySelector('.ns-notification-content');
        const closeBtn = document.createElement('button');
        closeBtn.className = 'ns-notification-close';
        closeBtn.innerHTML = '<i class="fas fa-times"></i>';
        closeBtn.onclick = () => this.close(id);
        content.appendChild(closeBtn);
        
        // إغلاق تلقائي بعد 5 ثوان
        setTimeout(() => this.close(id), 5000);
    }
    
    // إشعار تأكيد
    confirm(message, options = {}) {
        return new Promise((resolve) => {
            const config = {
                ...this.defaultOptions,
                ...options,
                duration: 0,
                closable: false
            };
            
            const id = this.generateId();
            const notification = this.createConfirmNotification(id, message, config, resolve);
            this.container.appendChild(notification);
            
            requestAnimationFrame(() => {
                notification.classList.add('show');
            });
            
            this.notifications.set(id, notification);
        });
    }
    
    // إنشاء إشعار تأكيد
    createConfirmNotification(id, message, options, resolve) {
        const notification = document.createElement('div');
        notification.className = 'ns-notification ns-notification-warning';
        notification.dataset.id = id;
        
        notification.innerHTML = `
            <div class="ns-notification-content">
                <i class="ns-notification-icon warning fas fa-question-circle"></i>
                <div style="flex: 1;">
                    <div class="ns-notification-message">${message}</div>
                    <div style="margin-top: 0.75rem; display: flex; gap: 0.5rem;">
                        <button class="ns-btn ns-btn-primary ns-btn-sm" onclick="nsNotifications.resolveConfirm('${id}', true)">
                            نعم
                        </button>
                        <button class="ns-btn ns-btn-secondary ns-btn-sm" onclick="nsNotifications.resolveConfirm('${id}', false)">
                            لا
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        notification.resolve = resolve;
        return notification;
    }
    
    // حل إشعار التأكيد
    resolveConfirm(id, result) {
        const notification = this.notifications.get(id);
        if (notification && notification.resolve) {
            notification.resolve(result);
            this.close(id);
        }
    }
}

// إنشاء مثيل عام
const nsNotifications = new NetSuiteNotifications();

// تصدير للاستخدام العام
window.nsNotifications = nsNotifications;
window.NetSuiteNotifications = NetSuiteNotifications;
