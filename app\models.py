# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات للنظام المحاسبي المتقدم
Database Models for Advanced Accounting System
"""

from datetime import datetime, date
from decimal import Decimal
from flask_sqlalchemy import SQLAlchemy
# Oracle-specific imports
from sqlalchemy import Sequence
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.dialects.oracle import VARCHAR2, CLOB, NUMBER

# Oracle-specific imports
from sqlalchemy import Sequence
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.dialects.oracle import VARCHAR2, CLOB, NUMBER

# Oracle-specific imports
from sqlalchemy import Sequence
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.dialects.oracle import VARCHAR2, CLOB, NUMBER

# Oracle-specific imports
from sqlalchemy import Sequence
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.dialects.oracle import VARCHAR2, CLOB, NUMBER

# Oracle-specific imports
from sqlalchemy import Sequence
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.dialects.oracle import VARCHAR2, CLOB, NUMBER

# Oracle-specific imports
from sqlalchemy import Sequence
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.dialects.oracle import VARCHAR2, CLOB, NUMBER

# Oracle-specific imports
from sqlalchemy import Sequence
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.dialects.oracle import VARCHAR2, CLOB, NUMBER

# Oracle-specific imports
from sqlalchemy import Sequence
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.dialects.oracle import VARCHAR2, CLOB, NUMBER

# Oracle-specific imports
from sqlalchemy import Sequence
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.dialects.oracle import VARCHAR2, CLOB, NUMBER

# Oracle-specific imports
from sqlalchemy import Sequence
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.dialects.oracle import VARCHAR2, CLOB, NUMBER

# Oracle-specific imports
from sqlalchemy import Sequence
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.dialects.oracle import VARCHAR2, CLOB, NUMBER

# Oracle-specific imports
from sqlalchemy import Sequence
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.dialects.oracle import VARCHAR2, CLOB, NUMBER

from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app import db

# جدول المستخدمين - Users Table
class User(UserMixin, db.Model):
    """نموذج المستخدمين"""
    __tablename__ = 'users'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(200), nullable=False)
    department = db.Column(db.String(100))
    position = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    is_active = db.Column(db.Integer, default=1)  # Oracle Boolean as Integer
    is_admin = db.Column(db.Integer, default=0)  # Oracle Boolean as Integer
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    # العلاقات
    purchase_requests = db.relationship('PurchaseRequest', backref='requester', lazy='dynamic')
    approvals = db.relationship('Approval', backref='approver', lazy='dynamic')
    
    def set_password(self, password):
        """تعيين كلمة المرور"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """التحقق من كلمة المرور"""
        return check_password_hash(self.password_hash, password)

    def get_id(self):
        """إرجاع معرف المستخدم كنص إنجليزي"""
        return str(self.id)

    @property
    def role(self):
        """دور المستخدم"""
        return 'admin' if self.is_admin else 'user'

    def __repr__(self):
        return f'<User {self.username}>'

# جدول إعدادات المستخدم - User Settings Table
class UserSettings(db.Model):
    """نموذج إعدادات المستخدم"""
    __tablename__ = 'user_settings'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    language = db.Column(db.String(10), default='ar')
    currency = db.Column(db.String(10), default='SAR')
    timezone = db.Column(db.String(50), default='Asia/Riyadh')
    date_format = db.Column(db.String(20), default='DD/MM/YYYY')
    theme = db.Column(db.String(20), default='light')
    items_per_page = db.Column(db.Integer, default=25)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقة مع المستخدم
    user = db.relationship('User', backref=db.backref('settings', uselist=False))

    def __repr__(self):
        return f'<UserSettings for User {self.user_id}>'

# جدول الموردين - Suppliers Table
class Supplier(db.Model):
    """نموذج الموردين"""
    __tablename__ = 'suppliers'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), unique=True, nullable=False, index=True)
    name_ar = db.Column(db.String(200), nullable=False)
    name_en = db.Column(db.String(200))
    commercial_register = db.Column(db.String(50))
    tax_number = db.Column(db.String(50))
    contact_person = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    mobile = db.Column(db.String(20))
    email = db.Column(db.String(120))
    website = db.Column(db.String(200))
    address = db.Column(db.Text)
    city = db.Column(db.String(100))
    country = db.Column(db.String(100))
    postal_code = db.Column(db.String(20))
    bank_name = db.Column(db.String(100))
    bank_account = db.Column(db.String(50))
    iban = db.Column(db.String(50))
    payment_terms = db.Column(db.Integer, default=30)  # أيام الدفع
    credit_limit = db.Column(db.Numeric(15, 2), default=0)
    rating = db.Column(db.Integer, default=0)  # تقييم من 1-5
    category = db.Column(db.String(100))
    is_active = db.Column(db.Integer, default=1)  # Oracle Boolean as Integer
    is_approved = db.Column(db.Integer, default=0)  # Oracle Boolean as Integer
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    purchase_requests = db.relationship('PurchaseRequest', backref='supplier', lazy='dynamic')
    purchase_orders = db.relationship('PurchaseOrder', backref='supplier', lazy='dynamic')
    contracts = db.relationship('Contract', foreign_keys='Contract.supplier_id', lazy='dynamic')
    
    def __repr__(self):
        return f'<Supplier {self.name_ar}>'

# جدول الأصناف - Items Table
class Item(db.Model):
    """نموذج الأصناف"""
    __tablename__ = 'items'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(50), unique=True, nullable=False, index=True)
    name_ar = db.Column(db.String(200), nullable=False)
    name_en = db.Column(db.String(200))
    description_ar = db.Column(db.Text)
    description_en = db.Column(db.Text)
    category_id = db.Column(db.Integer, db.ForeignKey('item_categories.id'))
    unit_of_measure = db.Column(db.String(20), nullable=False)
    unit_price = db.Column(db.Numeric(15, 2), default=0)
    min_stock_level = db.Column(db.Numeric(10, 2), default=0)
    max_stock_level = db.Column(db.Numeric(10, 2), default=0)
    reorder_point = db.Column(db.Numeric(10, 2), default=0)
    current_stock = db.Column(db.Numeric(10, 2), default=0)
    reserved_stock = db.Column(db.Numeric(10, 2), default=0)
    available_stock = db.Column(db.Numeric(10, 2), default=0)
    location = db.Column(db.String(100))
    barcode = db.Column(db.String(100))
    is_active = db.Column(db.Integer, default=1)  # Oracle Boolean as Integer
    is_stockable = db.Column(db.Integer, default=1)  # Oracle Boolean as Integer
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    purchase_request_items = db.relationship('PurchaseRequestItem', backref='master_item', lazy='dynamic')
    purchase_order_items = db.relationship('PurchaseOrderItem', backref='master_item', lazy='dynamic')
    stock_movements = db.relationship('StockMovement', backref='master_item', lazy='dynamic')
    
    def __repr__(self):
        return f'<Item {self.name_ar}>'

# جدول فئات الأصناف - Item Categories Table
class ItemCategory(db.Model):
    """نموذج فئات الأصناف"""
    __tablename__ = 'item_categories'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), unique=True, nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100))
    description_ar = db.Column(db.Text)
    description_en = db.Column(db.Text)
    parent_id = db.Column(db.Integer, db.ForeignKey('item_categories.id'))
    is_active = db.Column(db.Integer, default=1)  # Oracle Boolean as Integer
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    items = db.relationship('Item', backref='category', lazy='dynamic')
    children = db.relationship('ItemCategory', backref=db.backref('parent', remote_side=[id]))
    
    def __repr__(self):
        return f'<ItemCategory {self.name_ar}>'

# جدول طلبات الشراء - Purchase Requests Table
class PurchaseRequest(db.Model):
    """نموذج طلب الشراء المحسن - Enhanced Purchase Request Model"""
    __tablename__ = 'purchase_requests'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # المعلومات الأساسية - Basic Information
    id = db.Column(db.Integer, primary_key=True)
    req_no = db.Column(db.String(50), unique=True, nullable=False, index=True)  # رقم الطلب
    req_serial = db.Column(db.Integer, nullable=False, index=True)  # الرقم التسلسلي
    requester_name = db.Column(db.String(100), nullable=False, index=True)  # اسم الطالب
    department_name = db.Column(db.String(100), nullable=False, index=True)  # اسم القسم
    req_type = db.Column(db.String(50), nullable=False, default='عادي')  # نوع الطلب
    req_priority = db.Column(db.String(20), nullable=False, default='عادي')  # أولوية الطلب: عادي/عاجل/طارئ
    req_date = db.Column(db.Date, nullable=False, default=datetime.utcnow)  # تاريخ الطلب
    needed_date = db.Column(db.Date, nullable=False)  # تاريخ الحاجة
    req_status = db.Column(db.String(20), nullable=False, default='مسودة')  # حالة الطلب: مسودة/مرسل/معتمد

    # المعلومات المالية - Financial Information
    currency = db.Column(db.String(10), nullable=False, default='ريال')  # العملة: ريال/دولار/يورو
    total_amount = db.Column(db.Numeric(15, 2), nullable=False, default=0)  # الإجمالي
    approval_status = db.Column(db.String(20), nullable=False, default='في انتظار')  # حالة الموافقة: في انتظار/معتمد/مرفوض

    # معلومات العقد والمورد - Contract and Supplier Information
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=True)  # المورد
    contract_no = db.Column(db.String(50), nullable=True, index=True)  # رقم العقد
    contract_serial = db.Column(db.Integer, nullable=True)  # رقم تسلسلي العقد
    contract_amount = db.Column(db.Numeric(15, 2), nullable=True)  # مبلغ العقد

    # معلومات إضافية - Additional Information
    description = db.Column(db.Text, nullable=True)  # الوصف
    notes = db.Column(db.Text, nullable=True)  # الملاحظات
    attachments = db.Column(db.String(500), nullable=True)  # المرفقات

    # معلومات النظام - System Information
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات - Relationships
    creator = db.relationship('User', foreign_keys=[created_by], overlaps="purchase_requests,requester")
    items = db.relationship('PurchaseRequestItem', backref='purchase_request', lazy='dynamic', cascade='all, delete-orphan')
    approvals = db.relationship('Approval', backref='purchase_request', lazy='dynamic')

    def __repr__(self):
        return f'<PurchaseRequest {self.req_no}>'

    @property
    def formatted_total(self):
        """إجمالي منسق مع العملة"""
        return f"{self.total_amount:,.2f} {self.currency}"

    @property
    def status_color(self):
        """لون الحالة للعرض"""
        colors = {
            'مسودة': '#6C757D',
            'مرسل': '#FFC107',
            'معتمد': '#28A745',
            'مرفوض': '#DC3545'
        }
        return colors.get(self.req_status, '#6C757D')

    @property
    def priority_color(self):
        """لون الأولوية للعرض"""
        colors = {
            'عادي': '#28A745',
            'عاجل': '#FFC107',
            'طارئ': '#DC3545'
        }
        return colors.get(self.req_priority, '#28A745')

# جدول عناصر طلبات الشراء - Purchase Request Items Table
class PurchaseRequestItem(db.Model):
    """نموذج تفاصيل طلب الشراء المحسن - Enhanced Purchase Request Item Model"""
    __tablename__ = 'purchase_request_items'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    purchase_request_id = db.Column(db.Integer, db.ForeignKey('purchase_requests.id'), nullable=False)

    # معلومات الصنف - Item Information (7 أعمدة حسب المواصفات)
    item_code = db.Column(db.String(50), nullable=False, index=True)  # كود الصنف
    item_name = db.Column(db.String(200), nullable=False, index=True)  # اسم الصنف
    item_description = db.Column(db.Text, nullable=True)  # وصف الصنف
    quantity = db.Column(db.Numeric(10, 3), nullable=False)  # الكمية
    unit_name = db.Column(db.String(50), nullable=False)  # الوحدة
    unit_price = db.Column(db.Numeric(15, 2), nullable=False, default=0)  # سعر الوحدة
    total_price = db.Column(db.Numeric(15, 2), nullable=False, default=0)  # الإجمالي

    # معلومات إضافية
    specifications = db.Column(db.Text, nullable=True)  # المواصفات
    notes = db.Column(db.Text, nullable=True)  # الملاحظات
    line_number = db.Column(db.Integer, nullable=False, default=1)  # رقم السطر

    # ربط مع جدول الأصناف (اختياري)
    item_id = db.Column(db.Integer, db.ForeignKey('items.id'), nullable=True)
    item = db.relationship('Item', foreign_keys=[item_id], overlaps="master_item,purchase_request_items")

    def __repr__(self):
        return f'<PurchaseRequestItem {self.item_name}>'

    @property
    def formatted_unit_price(self):
        """سعر الوحدة منسق"""
        return f"{self.unit_price:,.2f}"

    @property
    def formatted_total_price(self):
        """الإجمالي منسق"""
        return f"{self.total_price:,.2f}"

    def calculate_total(self):
        """حساب الإجمالي تلقائياً"""
        self.total_price = self.quantity * self.unit_price
        return self.total_price

# جدول أوامر الشراء - Purchase Orders Table
class PurchaseOrder(db.Model):
    """نموذج أوامر الشراء"""
    __tablename__ = 'purchase_orders'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(50), unique=True, nullable=False, index=True)
    purchase_request_id = db.Column(db.Integer, db.ForeignKey('purchase_requests.id'))
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    contract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'))
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    order_date = db.Column(db.Date, nullable=False)
    delivery_date = db.Column(db.Date)
    delivery_address = db.Column(db.Text)
    payment_terms = db.Column(db.String(100))
    supplier_invoice_number = db.Column(db.String(100))  # رقم فاتورة المورد
    shipping_cost = db.Column(db.Numeric(15, 2), default=0)  # أجور الشحن
    clearance_cost = db.Column(db.Numeric(15, 2), default=0)  # أجور التخليص
    status = db.Column(db.String(50), default='مسودة')  # مسودة، مرسل، مؤكد، جاري التنفيذ، مكتمل، ملغي
    subtotal = db.Column(db.Numeric(15, 2), default=0)
    tax_amount = db.Column(db.Numeric(15, 2), default=0)
    gross_amount = db.Column(db.Numeric(15, 2), default=0)  # المبلغ الإجمالي قبل الخصم
    discount_amount = db.Column(db.Numeric(15, 2), default=0)  # مبلغ الخصم
    total_amount = db.Column(db.Numeric(15, 2), default=0)  # صافي المبلغ (net_amount)
    currency = db.Column(db.String(3), default='SAR')
    notes = db.Column(db.Text)
    terms_conditions = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    items = db.relationship('PurchaseOrderItem', backref='purchase_order', lazy='dynamic', cascade='all, delete-orphan')
    receipts = db.relationship('GoodsReceipt', backref='purchase_order', lazy='dynamic')
    invoices = db.relationship('Invoice', backref='purchase_order', lazy='dynamic')

    def __repr__(self):
        return f'<PurchaseOrder {self.order_number}>'

# جدول عناصر أوامر الشراء - Purchase Order Items Table
class PurchaseOrderItem(db.Model):
    """نموذج عناصر أوامر الشراء"""
    __tablename__ = 'purchase_order_items'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    purchase_order_id = db.Column(db.Integer, db.ForeignKey('purchase_orders.id'), nullable=False)
    item_id = db.Column(db.Integer, db.ForeignKey('items.id'), nullable=False)
    description = db.Column(db.Text)
    quantity = db.Column(db.Numeric(10, 2), nullable=False)
    received_quantity = db.Column(db.Numeric(10, 2), default=0)
    unit_price = db.Column(db.Numeric(15, 2), nullable=False)
    total_price = db.Column(db.Numeric(15, 2), nullable=False)
    tax_rate = db.Column(db.Numeric(5, 2), default=0)
    discount_rate = db.Column(db.Numeric(5, 2), default=0)
    specifications = db.Column(db.Text)
    production_date = db.Column(db.Date)  # تاريخ الإنتاج
    expiry_date = db.Column(db.Date)      # تاريخ الانتهاء
    line_number = db.Column(db.Integer, default=1)

    def __repr__(self):
        return f'<PurchaseOrderItem {self.item.name_ar}>'

# جدول استلام البضائع - Goods Receipt Table
class GoodsReceipt(db.Model):
    """نموذج استلام البضائع"""
    __tablename__ = 'goods_receipts'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    receipt_number = db.Column(db.String(50), unique=True, nullable=False, index=True)
    purchase_order_id = db.Column(db.Integer, db.ForeignKey('purchase_orders.id'), nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    receipt_date = db.Column(db.Date, nullable=False)
    delivery_note_number = db.Column(db.String(50))
    vehicle_number = db.Column(db.String(20))
    driver_name = db.Column(db.String(100))
    received_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    status = db.Column(db.String(50), default='مسودة')  # مسودة، مؤكد، مرفوض
    total_quantity = db.Column(db.Numeric(10, 2), default=0)
    notes = db.Column(db.Text)
    quality_check_status = db.Column(db.String(50))  # مقبول، مرفوض، يحتاج فحص
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    items = db.relationship('GoodsReceiptItem', backref='goods_receipt', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<GoodsReceipt {self.receipt_number}>'

# جدول عناصر استلام البضائع - Goods Receipt Items Table
class GoodsReceiptItem(db.Model):
    """نموذج عناصر استلام البضائع"""
    __tablename__ = 'goods_receipt_items'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    goods_receipt_id = db.Column(db.Integer, db.ForeignKey('goods_receipts.id'), nullable=False)
    purchase_order_item_id = db.Column(db.Integer, db.ForeignKey('purchase_order_items.id'), nullable=False)
    item_id = db.Column(db.Integer, db.ForeignKey('items.id'), nullable=False)
    ordered_quantity = db.Column(db.Numeric(10, 2), nullable=False)
    received_quantity = db.Column(db.Numeric(10, 2), nullable=False)
    accepted_quantity = db.Column(db.Numeric(10, 2), default=0)
    rejected_quantity = db.Column(db.Numeric(10, 2), default=0)
    unit_price = db.Column(db.Numeric(15, 2))
    batch_number = db.Column(db.String(50))
    expiry_date = db.Column(db.Date)
    quality_status = db.Column(db.String(50))  # مقبول، مرفوض، يحتاج فحص
    rejection_reason = db.Column(db.Text)
    notes = db.Column(db.Text)
    line_number = db.Column(db.Integer, default=1)

    def __repr__(self):
        return f'<GoodsReceiptItem {self.item.name_ar}>'

# جدول حركات المخزون - Stock Movements Table
class StockMovement(db.Model):
    """نموذج حركات المخزون"""
    __tablename__ = 'stock_movements'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    item_id = db.Column(db.Integer, db.ForeignKey('items.id'), nullable=False)
    movement_type = db.Column(db.String(50), nullable=False)  # استلام، صرف، تحويل، تسوية، إرجاع
    reference_type = db.Column(db.String(50))  # أمر شراء، استلام بضائع، صرف، تحويل
    reference_id = db.Column(db.Integer)
    reference_number = db.Column(db.String(50))
    quantity = db.Column(db.Numeric(10, 2), nullable=False)
    unit_cost = db.Column(db.Numeric(15, 2))
    total_cost = db.Column(db.Numeric(15, 2))
    balance_before = db.Column(db.Numeric(10, 2))
    balance_after = db.Column(db.Numeric(10, 2))
    location_from = db.Column(db.String(100))
    location_to = db.Column(db.String(100))
    movement_date = db.Column(db.DateTime, nullable=False)
    notes = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<StockMovement {self.item.name_ar} - {self.movement_type}>'

# جدول العقود - Contracts Table
class Contract(db.Model):
    """نموذج العقود المحسن - Enhanced Contract Model (12 أعمدة)"""
    __tablename__ = 'contracts'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)

    # الأعمدة الـ 12 حسب المواصفات
    contract_no = db.Column(db.String(50), unique=True, nullable=False, index=True)  # 1. رقم العقد
    contract_title = db.Column(db.String(200), nullable=False, index=True)  # 2. عنوان العقد
    contract_type = db.Column(db.String(50), nullable=False, default='توريد')  # 3. نوع العقد: توريد، خدمات، صيانة، استشارات
    supplier_name = db.Column(db.String(100), nullable=False, index=True)  # 4. المورد
    contract_amount = db.Column(db.Numeric(15, 2), nullable=False, default=0)  # 5. مبلغ العقد
    used_amount = db.Column(db.Numeric(15, 2), nullable=False, default=0)  # 6. المبلغ المستخدم
    remaining_amount = db.Column(db.Numeric(15, 2), nullable=False, default=0)  # 7. المبلغ المتبقي
    currency = db.Column(db.String(10), nullable=False, default='ريال')  # 8. العملة
    contract_status = db.Column(db.String(20), nullable=False, default='نشط')  # 9. الحالة: نشط، منتهي، معلق
    start_date = db.Column(db.Date, nullable=False)  # 10. تاريخ البداية
    end_date = db.Column(db.Date, nullable=False)  # 11. تاريخ الانتهاء
    usage_percentage = db.Column(db.Numeric(5, 2), nullable=False, default=0)  # 12. نسبة الاستخدام

    # معلومات إضافية
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=True)
    payment_terms = db.Column(db.String(200), nullable=True)
    delivery_terms = db.Column(db.String(200), nullable=True)
    terms_conditions = db.Column(db.Text, nullable=True)
    attachment_path = db.Column(db.String(500), nullable=True)

    # معلومات النظام
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    main_supplier = db.relationship('Supplier', foreign_keys=[supplier_id], overlaps="contracts")
    creator = db.relationship('User', foreign_keys=[created_by])

    def __repr__(self):
        return f'<Contract {self.contract_no}>'

    @property
    def formatted_contract_amount(self):
        """مبلغ العقد منسق"""
        return f"{self.contract_amount:,.2f} {self.currency}"

    @property
    def formatted_used_amount(self):
        """المبلغ المستخدم منسق"""
        return f"{self.used_amount:,.2f} {self.currency}"

    @property
    def formatted_remaining_amount(self):
        """المبلغ المتبقي منسق"""
        return f"{self.remaining_amount:,.2f} {self.currency}"

    @property
    def status_color(self):
        """لون الحالة للعرض"""
        colors = {
            'نشط': '#28A745',
            'منتهي': '#DC3545',
            'معلق': '#FFC107'
        }
        return colors.get(self.contract_status, '#6C757D')

    def calculate_remaining_amount(self):
        """حساب المبلغ المتبقي ونسبة الاستخدام"""
        self.remaining_amount = self.contract_amount - self.used_amount
        if self.contract_amount > 0:
            self.usage_percentage = (self.used_amount / self.contract_amount) * 100
        else:
            self.usage_percentage = 0
        return self.remaining_amount

# جدول الفواتير - Invoices Table
class Invoice(db.Model):
    """نموذج الفواتير"""
    __tablename__ = 'invoices'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False, index=True)
    supplier_invoice_number = db.Column(db.String(50))
    purchase_order_id = db.Column(db.Integer, db.ForeignKey('purchase_orders.id'))
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    invoice_date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date)
    invoice_type = db.Column(db.String(50), default='عادية')  # عادية، ائتمانية، مدينة
    status = db.Column(db.String(50), default='مستلمة')  # مستلمة، معتمدة، مدفوعة، مرفوضة
    subtotal = db.Column(db.Numeric(15, 2), default=0)
    tax_amount = db.Column(db.Numeric(15, 2), default=0)
    discount_amount = db.Column(db.Numeric(15, 2), default=0)
    total_amount = db.Column(db.Numeric(15, 2), default=0)
    paid_amount = db.Column(db.Numeric(15, 2), default=0)
    remaining_amount = db.Column(db.Numeric(15, 2), default=0)
    currency = db.Column(db.String(3), default='SAR')
    notes = db.Column(db.Text)
    attachment_path = db.Column(db.String(500))
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    items = db.relationship('InvoiceItem', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')
    payments = db.relationship('Payment', backref='invoice', lazy='dynamic')

    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'

# جدول عناصر الفواتير - Invoice Items Table
class InvoiceItem(db.Model):
    """نموذج عناصر الفواتير"""
    __tablename__ = 'invoice_items'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)
    item_id = db.Column(db.Integer, db.ForeignKey('items.id'))
    description = db.Column(db.Text, nullable=False)
    quantity = db.Column(db.Numeric(10, 2), nullable=False)
    unit_price = db.Column(db.Numeric(15, 2), nullable=False)
    total_price = db.Column(db.Numeric(15, 2), nullable=False)
    tax_rate = db.Column(db.Numeric(5, 2), default=0)
    discount_rate = db.Column(db.Numeric(5, 2), default=0)
    line_number = db.Column(db.Integer, default=1)

    def __repr__(self):
        return f'<InvoiceItem {self.description}>'

# جدول المدفوعات - Payments Table
class Payment(db.Model):
    """نموذج المدفوعات"""
    __tablename__ = 'payments'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    payment_number = db.Column(db.String(50), unique=True, nullable=False, index=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    payment_date = db.Column(db.Date, nullable=False)
    payment_method = db.Column(db.String(50))  # نقدي، شيك، تحويل بنكي، بطاقة ائتمان
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    currency = db.Column(db.String(3), default='SAR')
    reference_number = db.Column(db.String(100))
    bank_name = db.Column(db.String(100))
    check_number = db.Column(db.String(50))
    check_date = db.Column(db.Date)
    status = db.Column(db.String(50), default='معلق')  # معلق، مؤكد، ملغي
    notes = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Payment {self.payment_number}>'

# جدول الموافقات - Approvals Table
class Approval(db.Model):
    """نموذج الموافقات"""
    __tablename__ = 'approvals'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    purchase_request_id = db.Column(db.Integer, db.ForeignKey('purchase_requests.id'), nullable=False)
    approver_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    approval_level = db.Column(db.Integer, nullable=False)
    status = db.Column(db.String(50), default='معلق')  # معلق، موافق، مرفوض
    approval_date = db.Column(db.DateTime)
    comments = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Approval {self.purchase_request.request_number} - Level {self.approval_level}>'

# جدول سير العمل - Workflow Table
class WorkflowTemplate(db.Model):
    """نموذج قوالب سير العمل"""
    __tablename__ = 'workflow_templates'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    document_type = db.Column(db.String(50), nullable=False)  # طلب شراء، أمر شراء، فاتورة
    is_active = db.Column(db.Integer, default=1)  # Oracle Boolean as Integer
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    steps = db.relationship('WorkflowStep', backref='template', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<WorkflowTemplate {self.name}>'

# جدول خطوات سير العمل - Workflow Steps Table
class WorkflowStep(db.Model):
    """نموذج خطوات سير العمل"""
    __tablename__ = 'workflow_steps'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    template_id = db.Column(db.Integer, db.ForeignKey('workflow_templates.id'), nullable=False)
    step_number = db.Column(db.Integer, nullable=False)
    step_name = db.Column(db.String(100), nullable=False)
    approver_role = db.Column(db.String(100))
    approver_user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    min_amount = db.Column(db.Numeric(15, 2), default=0)
    max_amount = db.Column(db.Numeric(15, 2))
    is_mandatory = db.Column(db.Integer, default=1)  # Oracle Boolean as Integer
    auto_approve = db.Column(db.Integer, default=0)  # Oracle Boolean as Integer
    timeout_hours = db.Column(db.Integer, default=24)

    def __repr__(self):
        return f'<WorkflowStep {self.step_name}>'

# جدول تقييم الموردين - Supplier Evaluations Table
class SupplierEvaluation(db.Model):
    """نموذج تقييم الموردين"""
    __tablename__ = 'supplier_evaluations'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    evaluation_date = db.Column(db.Date, nullable=False, default=datetime.utcnow)
    evaluation_period_start = db.Column(db.Date, nullable=False)
    evaluation_period_end = db.Column(db.Date, nullable=False)

    # معايير التقييم (من 1 إلى 5)
    quality_rating = db.Column(db.Integer, nullable=False)  # جودة المنتجات
    delivery_rating = db.Column(db.Integer, nullable=False)  # الالتزام بالتسليم
    price_rating = db.Column(db.Integer, nullable=False)  # تنافسية الأسعار
    service_rating = db.Column(db.Integer, nullable=False)  # جودة الخدمة
    communication_rating = db.Column(db.Integer, nullable=False)  # التواصل

    # التقييم الإجمالي
    overall_rating = db.Column(db.Float, nullable=False)

    # ملاحظات وتوصيات
    strengths = db.Column(db.Text)  # نقاط القوة
    weaknesses = db.Column(db.Text)  # نقاط الضعف
    recommendations = db.Column(db.Text)  # التوصيات
    notes = db.Column(db.Text)

    # معلومات التتبع
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # العلاقات
    evaluator = db.relationship('User', foreign_keys=[created_by])

    def __repr__(self):
        return f'<SupplierEvaluation {self.supplier.name_ar} - {self.evaluation_date}>'

    def calculate_overall_rating(self):
        """حساب التقييم الإجمالي"""
        ratings = [
            self.quality_rating,
            self.delivery_rating,
            self.price_rating,
            self.service_rating,
            self.communication_rating
        ]
        self.overall_rating = sum(ratings) / len(ratings)
        return self.overall_rating

# جدول الميزانيات - Budgets Table
class Budget(db.Model):
    """نموذج الميزانيات"""
    __tablename__ = 'budgets'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    budget_type = db.Column(db.String(50), nullable=False)  # annual, quarterly, monthly, project
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    total_budget = db.Column(db.Numeric(15, 2), nullable=False)
    spent_amount = db.Column(db.Numeric(15, 2), default=0)
    remaining_amount = db.Column(db.Numeric(15, 2), default=0)
    currency = db.Column(db.String(3), default='SAR')
    status = db.Column(db.String(20), default='draft')  # draft, active, completed, cancelled
    notes = db.Column(db.Text)

    # معلومات التتبع
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # العلاقات
    creator = db.relationship('User', foreign_keys=[created_by])

    def __repr__(self):
        return f'<Budget {self.name}>'

# جدول المصروفات - Expenses Table
class Expense(db.Model):
    """نموذج المصروفات"""
    __tablename__ = 'expenses'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    expense_number = db.Column(db.String(50), unique=True, nullable=False, index=True)
    description = db.Column(db.String(200), nullable=False)
    category = db.Column(db.String(100), nullable=False)
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    expense_date = db.Column(db.Date, nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'))
    payment_method = db.Column(db.String(50), nullable=False)
    receipt_number = db.Column(db.String(50))
    notes = db.Column(db.Text)

    # معلومات التتبع
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # العلاقات
    creator = db.relationship('User', foreign_keys=[created_by])

    def __repr__(self):
        return f'<Expense {self.expense_number}>'

# جدول مثيلات سير العمل - Workflow Instances Table
class WorkflowInstance(db.Model):
    """نموذج مثيلات سير العمل"""
    __tablename__ = 'workflow_instances'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    template_id = db.Column(db.Integer, db.ForeignKey('workflow_templates.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    entity_type = db.Column(db.String(50), nullable=False)  # purchase_request, purchase_order, etc.
    entity_id = db.Column(db.Integer, nullable=False)
    status = db.Column(db.String(20), default='active')  # active, completed, rejected, cancelled

    # معلومات التتبع
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # العلاقات
    template = db.relationship('WorkflowTemplate')
    steps = db.relationship('WorkflowStepInstance', backref='workflow_instance', lazy='dynamic')
    creator = db.relationship('User', foreign_keys=[created_by])

    def __repr__(self):
        return f'<WorkflowInstance {self.title}>'

# جدول خطوات مثيلات سير العمل - Workflow Step Instances Table
class WorkflowStepInstance(db.Model):
    """نموذج خطوات مثيلات سير العمل"""
    __tablename__ = 'workflow_step_instances'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    workflow_instance_id = db.Column(db.Integer, db.ForeignKey('workflow_instances.id'), nullable=False)
    step_name = db.Column(db.String(100), nullable=False)
    step_description = db.Column(db.Text)
    step_order = db.Column(db.Integer, nullable=False)
    assigned_to = db.Column(db.Integer, db.ForeignKey('users.id'))
    status = db.Column(db.String(20), default='waiting')  # waiting, pending, completed, rejected

    # معلومات التوقيت
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)
    completed_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # ملاحظات
    notes = db.Column(db.Text)

    # معلومات التتبع
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    assignee = db.relationship('User', foreign_keys=[assigned_to])
    completer = db.relationship('User', foreign_keys=[completed_by])

    def __repr__(self):
        return f'<WorkflowStepInstance {self.step_name}>'

# جدول عقود الشراء - Purchase Contracts Table
class PurchaseContract(db.Model):
    """نموذج عقود الشراء"""
    __tablename__ = 'purchase_contracts'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    branch_no = db.Column(db.String(10), nullable=False, default='001')
    contract_no = db.Column(db.String(50), nullable=False, unique=True)
    contract_serial = db.Column(db.Integer, nullable=False)

    # التواريخ
    contract_date = db.Column(db.Date, nullable=False, default=datetime.utcnow)
    contract_from_date = db.Column(db.Date, nullable=False)
    contract_to_date = db.Column(db.Date, nullable=False)

    # معلومات المورد
    vendor_code = db.Column(db.String(20), nullable=False)
    vendor_name = db.Column(db.String(200), nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=True)

    # المعلومات المالية
    currency_code = db.Column(db.String(3), nullable=False, default='SAR')
    contract_rate = db.Column(db.Numeric(10, 4), nullable=False, default=1.0000)
    contract_type = db.Column(db.String(50), nullable=False)
    contract_amount = db.Column(db.Numeric(15, 2), nullable=False)
    discount_amount = db.Column(db.Numeric(15, 2), nullable=False, default=0.00)
    net_amount = db.Column(db.Numeric(15, 2), nullable=False)

    # معلومات إضافية
    reference_no = db.Column(db.String(100))
    contract_desc = db.Column(db.Text)
    status = db.Column(db.String(20), nullable=False, default='نشط')
    contract_note = db.Column(db.Text)

    # معلومات النظام
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # العلاقات
    supplier = db.relationship('Supplier', backref='purchase_contracts')
    creator = db.relationship('User', backref='created_contracts')
    details = db.relationship('PurchaseContractDetail', backref='contract', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<PurchaseContract {self.contract_no}>'

# جدول تفاصيل عقود الشراء - Purchase Contract Details Table
class PurchaseContractDetail(db.Model):
    """نموذج تفاصيل عقود الشراء"""
    __tablename__ = 'purchase_contract_details'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    id = db.Column(db.Integer, primary_key=True)
    contract_id = db.Column(db.Integer, db.ForeignKey('purchase_contracts.id'), nullable=False)
    line_no = db.Column(db.Integer, nullable=False)

    # معلومات الصنف
    item_code = db.Column(db.String(50), nullable=False)
    item_name = db.Column(db.String(200), nullable=False)
    item_id = db.Column(db.Integer, db.ForeignKey('items.id'), nullable=True)

    # معلومات الوحدة والعبوة
    unit_code = db.Column(db.String(10), nullable=False)
    pack_size = db.Column(db.String(50))

    # التواريخ
    production_date = db.Column(db.Date)
    expiry_date = db.Column(db.Date)

    # الكميات والأسعار
    quantity = db.Column(db.Numeric(10, 3), nullable=False)
    unit_price = db.Column(db.Numeric(10, 4), nullable=False)
    free_quantity = db.Column(db.Numeric(10, 3), nullable=False, default=0.000)
    discount_percent = db.Column(db.Numeric(5, 2), nullable=False, default=0.00)
    line_total = db.Column(db.Numeric(15, 2), nullable=False)

    # وصف السطر
    line_description = db.Column(db.Text)

    # العلاقات
    item = db.relationship('Item', backref='contract_details')

    def __repr__(self):
        return f'<PurchaseContractDetail {self.item_name}: {self.quantity}>'

# جدول الفروع - Branches Table
class Branch(db.Model):
    """نموذج الفروع - مطابق لمواصفات Oracle المحددة"""
    __tablename__ = 'branches'
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}
    # Oracle optimizations
    __table_args__ = {'oracle_compress': True}

    # الحقول حسب المواصفات المطلوبة بدقة
    brn_no = db.Column(db.Integer, primary_key=True)  # NUMBER(6) - رقم الفرع
    brn_lname = db.Column(db.String(100), nullable=False)  # VARCHAR2(100 BYTE) NOT NULL - اسم الفرع
    brn_fname = db.Column(db.String(100))  # VARCHAR2(100 BYTE) - اسم الفرع بالإنجليزية
    brn_ladd = db.Column(db.String(100))  # VARCHAR2(100 BYTE) - عنوان الفرع
    brn_fadd = db.Column(db.String(100))  # VARCHAR2(100 BYTE) - عنوان الفرع بالإنجليزية
    brn_ltele = db.Column(db.String(100))  # VARCHAR2(100 BYTE) - رقم الهاتف
    brn_ftele = db.Column(db.String(100))  # VARCHAR2(100 BYTE) - رقم الهاتف بالإنجليزية
    brn_lfax = db.Column(db.String(100))  # VARCHAR2(100 BYTE) - رقم الفاكس
    brn_ffax = db.Column(db.String(100))  # VARCHAR2(100 BYTE) - رقم الفاكس بالإنجليزية
    brn_lbox = db.Column(db.String(100))  # VARCHAR2(100 BYTE) - رقم صندوق البريد
    brn_fbox = db.Column(db.String(100))  # VARCHAR2(100 BYTE) - رقم صندوق البريد بالإنجليزية
    dial_name = db.Column(db.String(100))  # VARCHAR2(100 BYTE) - اسم المنطقة
    rprt_hdr_typ = db.Column(db.Integer, default=1)  # NUMBER(2) DEFAULT 1 - نوع رأس التقرير
    brn_img = db.Column(db.String(100))  # VARCHAR2(100 BYTE) - شعار الفرع
    use_db_link = db.Column(db.Integer)  # NUMBER(1) - استخدام رابط قاعدة البيانات
    brn_code = db.Column(db.String(30))  # VARCHAR2(30 BYTE) - كود الفرع
    dblink_name = db.Column(db.String(60))  # VARCHAR2(60 BYTE) - اسم DBLINKS
    agncy_scl_scrty_code = db.Column(db.String(60))  # VARCHAR2(60 BYTE) - كود الأمان الاجتماعي للوكالة
    agncy_scl_scrty_nm = db.Column(db.String(100))  # VARCHAR2(100 BYTE) - اسم الأمان الاجتماعي للوكالة
    tax_web_site = db.Column(db.String(500))  # VARCHAR2(500 BYTE) - موقع الضرائب
    rc_code = db.Column(db.String(40))  # VARCHAR2(40 BYTE) - كود السجل التجاري
    nai_tax = db.Column(db.String(40))  # VARCHAR2(40 BYTE) - الرقم الضريبي الوطني
    nis_code = db.Column(db.String(40))  # VARCHAR2(40 BYTE) - كود التأمينات الاجتماعية
    brn_tel_no = db.Column(db.String(30))  # VARCHAR2(30 BYTE) - رقم هاتف الفرع
    inactive = db.Column(db.Integer)  # NUMBER(1) - غير نشط
    inactive_date = db.Column(db.Date)  # DATE - تاريخ إلغاء التفعيل
    inactive_u_id = db.Column(db.Integer)  # NUMBER(5) - معرف المستخدم الذي ألغى التفعيل
    inactive_res = db.Column(db.String(250))  # VARCHAR2(250 BYTE) - سبب إلغاء التفعيل
    capital = db.Column(db.Numeric)  # NUMBER - رأس المال
    ad_trmnl_nm = db.Column(db.String(50))  # VARCHAR2(50 BYTE) - اسم المحطة الإدارية
    grp_no = db.Column(db.Integer)  # NUMBER(5) - رقم المجموعة
    pl_cls = db.Column(db.Integer, default=0)  # NUMBER(1) DEFAULT 0 - إقفال الأرباح والخسائر
    pl_cls_cnt = db.Column(db.Numeric)  # NUMBER - عداد إقفال الأرباح والخسائر
    pl_cls_u_id = db.Column(db.Integer)  # NUMBER(5) - معرف مستخدم الإقفال
    pl_cls_date = db.Column(db.Date)  # DATE - تاريخ الإقفال
    pl_cls_a_code = db.Column(db.String(30))  # VARCHAR2(30 BYTE) - كود حساب الإقفال
    pl_cls_cur_code = db.Column(db.String(7))  # VARCHAR2(7 BYTE) - كود عملة الإقفال
    pl_cls_amt = db.Column(db.Numeric)  # NUMBER - مبلغ الإقفال
    pl_uncls_u_id = db.Column(db.Integer)  # NUMBER(5) - معرف مستخدم إلغاء الإقفال
    pl_uncls_date = db.Column(db.Date)  # DATE - تاريخ إلغاء الإقفال
    pan_code = db.Column(db.String(30))  # VARCHAR2(30 BYTE) - كود PAN
    tan_code = db.Column(db.String(30))  # VARCHAR2(30 BYTE) - كود TAN
    building_no = db.Column(db.String(200))  # VARCHAR2(200 BYTE) - رقم المبنى
    street = db.Column(db.String(200))  # VARCHAR2(200 BYTE) - الشارع
    tax_auth_code = db.Column(db.String(500))  # VARCHAR2(500 BYTE) - كود السلطة الضريبية
    use_e_invoice = db.Column(db.Integer, default=0)  # NUMBER(1) DEFAULT 0 - استخدام الفواتير الإلكترونية
    shw_qr_code_rprt_flg = db.Column(db.Integer, default=0)  # NUMBER(1) DEFAULT 0 - عرض رمز QR في التقارير

    def __repr__(self):
        return f'<Branch {self.brn_no}: {self.brn_lname}>'

    def to_dict(self):
        """تحويل البيانات إلى قاموس"""
        return {
            'brn_no': self.brn_no,
            'brn_lname': self.brn_lname,
            'brn_fname': self.brn_fname,
            'brn_ladd': self.brn_ladd,
            'brn_fadd': self.brn_fadd,
            'brn_ltele': self.brn_ltele,
            'brn_ftele': self.brn_ftele,
            'brn_lfax': self.brn_lfax,
            'brn_ffax': self.brn_ffax,
            'brn_lbox': self.brn_lbox,
            'brn_fbox': self.brn_fbox,
            'dial_name': self.dial_name,
            'rprt_hdr_typ': self.rprt_hdr_typ,
            'brn_img': self.brn_img,
            'use_db_link': self.use_db_link,
            'brn_code': self.brn_code,
            'dblink_name': self.dblink_name,
            'agncy_scl_scrty_code': self.agncy_scl_scrty_code,
            'agncy_scl_scrty_nm': self.agncy_scl_scrty_nm,
            'tax_web_site': self.tax_web_site,
            'rc_code': self.rc_code,
            'nai_tax': self.nai_tax,
            'nis_code': self.nis_code,
            'brn_tel_no': self.brn_tel_no,
            'inactive': self.inactive,
            'inactive_date': self.inactive_date.isoformat() if self.inactive_date else None,
            'inactive_u_id': self.inactive_u_id,
            'inactive_res': self.inactive_res,
            'capital': float(self.capital) if self.capital else None,
            'ad_trmnl_nm': self.ad_trmnl_nm,
            'grp_no': self.grp_no,
            'pl_cls': self.pl_cls,
            'pl_cls_cnt': float(self.pl_cls_cnt) if self.pl_cls_cnt else None,
            'pl_cls_u_id': self.pl_cls_u_id,
            'pl_cls_date': self.pl_cls_date.isoformat() if self.pl_cls_date else None,
            'pl_cls_a_code': self.pl_cls_a_code,
            'pl_cls_cur_code': self.pl_cls_cur_code,
            'pl_cls_amt': float(self.pl_cls_amt) if self.pl_cls_amt else None,
            'pl_uncls_u_id': self.pl_uncls_u_id,
            'pl_uncls_date': self.pl_uncls_date.isoformat() if self.pl_uncls_date else None,
            'pan_code': self.pan_code,
            'tan_code': self.tan_code,
            'building_no': self.building_no,
            'street': self.street,
            'tax_auth_code': self.tax_auth_code,
            'use_e_invoice': self.use_e_invoice,
            'shw_qr_code_rprt_flg': self.shw_qr_code_rprt_flg
        }


# ========== نماذج إدارة العملات ==========

class Currency(db.Model):
    """نموذج العملات"""
    __tablename__ = 'currencies'

    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(3), unique=True, nullable=False, index=True)
    name_ar = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100), nullable=False)
    symbol = db.Column(db.String(10), nullable=False)
    exchange_rate = db.Column(db.Numeric(15, 6), default=1.000000)
    is_base_currency = db.Column(db.Integer, default=0)  # Oracle Boolean as Integer
    is_active = db.Column(db.Integer, default=1)  # Oracle Boolean as Integer
    decimal_places = db.Column(db.Integer, default=2)
    position = db.Column(db.String(10), default='before')  # before/after symbol position
    thousands_separator = db.Column(db.String(5), default=',')
    decimal_separator = db.Column(db.String(5), default='.')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # العلاقات
    exchange_rates = db.relationship('CurrencyExchangeRate',
                                   foreign_keys='CurrencyExchangeRate.currency_id',
                                   backref='currency', lazy='dynamic')
    base_rates = db.relationship('CurrencyExchangeRate',
                               foreign_keys='CurrencyExchangeRate.base_currency_id',
                               backref='base_currency', lazy='dynamic')

    def __repr__(self):
        return f'<Currency {self.code}: {self.name_ar}>'

    @property
    def is_base(self):
        """هل هي العملة الأساسية"""
        return bool(self.is_base_currency)

    @property
    def is_enabled(self):
        """هل العملة مفعلة"""
        return bool(self.is_active)

    def format_amount(self, amount):
        """تنسيق المبلغ حسب إعدادات العملة"""
        if amount is None:
            return "0"

        # تنسيق المبلغ حسب عدد المنازل العشرية
        if self.decimal_places == 0:
            formatted = f"{amount:,.0f}"
        else:
            formatted = f"{amount:,.{self.decimal_places}f}"

        # استبدال الفواصل حسب إعدادات العملة
        if self.thousands_separator != ',':
            formatted = formatted.replace(',', '|TEMP|')
            formatted = formatted.replace('.', self.decimal_separator)
            formatted = formatted.replace('|TEMP|', self.thousands_separator)
        elif self.decimal_separator != '.':
            formatted = formatted.replace('.', self.decimal_separator)

        # إضافة رمز العملة
        if self.position == 'before':
            return f"{self.symbol} {formatted}"
        else:
            return f"{formatted} {self.symbol}"

    def convert_to(self, amount, target_currency_code):
        """تحويل مبلغ إلى عملة أخرى"""
        if self.code == target_currency_code:
            return amount

        target_currency = Currency.query.filter_by(code=target_currency_code, is_active=1).first()
        if not target_currency:
            return None

        # التحويل عبر العملة الأساسية
        if self.is_base_currency:
            # من العملة الأساسية إلى العملة الهدف
            return amount * target_currency.exchange_rate
        elif target_currency.is_base_currency:
            # من العملة الحالية إلى العملة الأساسية
            return amount / self.exchange_rate
        else:
            # من عملة إلى أخرى عبر العملة الأساسية
            base_amount = amount / self.exchange_rate
            return base_amount * target_currency.exchange_rate

    def get_latest_rate(self):
        """الحصول على آخر سعر صرف"""
        latest_rate = self.exchange_rates.order_by(CurrencyExchangeRate.rate_date.desc()).first()
        return latest_rate.exchange_rate if latest_rate else self.exchange_rate

    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'code': self.code,
            'name_ar': self.name_ar,
            'name_en': self.name_en,
            'symbol': self.symbol,
            'exchange_rate': float(self.exchange_rate),
            'is_base_currency': bool(self.is_base_currency),
            'is_active': bool(self.is_active),
            'decimal_places': self.decimal_places,
            'position': self.position,
            'thousands_separator': self.thousands_separator,
            'decimal_separator': self.decimal_separator,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    @staticmethod
    def get_base_currency():
        """الحصول على العملة الأساسية"""
        return Currency.query.filter_by(is_base_currency=1, is_active=1).first()

    @staticmethod
    def get_active_currencies():
        """الحصول على العملات المفعلة"""
        return Currency.query.filter_by(is_active=1).order_by(Currency.code).all()


class CurrencyExchangeRate(db.Model):
    """نموذج أسعار صرف العملات"""
    __tablename__ = 'currency_exchange_rates'

    id = db.Column(db.Integer, primary_key=True)
    currency_id = db.Column(db.Integer, db.ForeignKey('currencies.id'), nullable=False)
    base_currency_id = db.Column(db.Integer, db.ForeignKey('currencies.id'), nullable=False)
    exchange_rate = db.Column(db.Numeric(15, 6), nullable=False)
    rate_date = db.Column(db.Date, default=date.today)
    source = db.Column(db.String(100))  # manual, api, bank
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    def __repr__(self):
        return f'<ExchangeRate {self.currency.code}/{self.base_currency.code}: {self.exchange_rate}>'

    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'currency_id': self.currency_id,
            'currency_code': self.currency.code if self.currency else None,
            'base_currency_id': self.base_currency_id,
            'base_currency_code': self.base_currency.code if self.base_currency else None,
            'exchange_rate': float(self.exchange_rate),
            'rate_date': self.rate_date.isoformat() if self.rate_date else None,
            'source': self.source,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
