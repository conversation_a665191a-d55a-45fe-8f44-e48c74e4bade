{% extends "base.html" %}

{% block title %}إدارة الإشعارات الفورية{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-bell text-primary"></i>
            إدارة الإشعارات الفورية
        </h1>
        <div>
            <a href="{{ url_for('instant_notifications.flexible_rules') }}" class="btn btn-success btn-sm">
                <i class="fas fa-cogs"></i> القواعد المرنة
            </a>
            <a href="{{ url_for('instant_notifications.notification_mapping') }}" class="btn btn-warning btn-sm">
                <i class="fas fa-link"></i> ربط الإشعارات
            </a>
            <a href="{{ url_for('instant_notifications.settings') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
            <a href="{{ url_for('instant_notifications.contacts') }}" class="btn btn-info btn-sm">
                <i class="fas fa-address-book"></i> جهات الاتصال
            </a>
            <a href="{{ url_for('instant_notifications.logs') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-history"></i> السجل
            </a>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row">
        <!-- إجمالي الإعدادات -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الإعدادات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.total_settings or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-cogs fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإعدادات المفعلة -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                الإعدادات المفعلة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.enabled_settings or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جهات الاتصال النشطة -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                جهات الاتصال النشطة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.active_contacts or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الرسائل المرسلة اليوم -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                رسائل اليوم
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.today_success or 0 }} / {{ stats.today_total or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fab fa-whatsapp fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات النظام -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle"></i>
                        معلومات النظام
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">🚀 كيف يعمل النظام:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> تغيير حالة الشحنة من الواجهة</li>
                                <li><i class="fas fa-check text-success"></i> Database Trigger يضيف حدث فوري</li>
                                <li><i class="fas fa-check text-success"></i> معالج الأحداث يعالج تلقائياً</li>
                                <li><i class="fas fa-check text-success"></i> إرسال WhatsApp فوري</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-info">⚙️ الميزات المتاحة:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-cog text-info"></i> تخصيص رسائل الإشعارات</li>
                                <li><i class="fas fa-users text-info"></i> إدارة جهات الاتصال</li>
                                <li><i class="fas fa-toggle-on text-info"></i> تفعيل/تعطيل الإشعارات</li>
                                <li><i class="fas fa-chart-line text-info"></i> تتبع الإحصائيات</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-rocket"></i>
                        إجراءات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('instant_notifications.settings') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> تعديل الرسائل
                        </a>
                        <a href="{{ url_for('instant_notifications.contacts') }}" class="btn btn-info btn-sm">
                            <i class="fas fa-plus"></i> إضافة جهة اتصال
                        </a>
                        <button class="btn btn-success btn-sm" onclick="testSystem()">
                            <i class="fas fa-play"></i> اختبار النظام
                        </button>
                        <a href="{{ url_for('instant_notifications.logs') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-eye"></i> عرض السجل
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testSystem() {
    if (confirm('هل تريد إرسال رسالة اختبار؟')) {
        fetch('/instant-notifications/api/test_message', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                phone_number: '967774893877',
                message: 'اختبار النظام - ' + new Date().toLocaleString('ar')
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ تم إرسال رسالة الاختبار بنجاح!');
            } else {
                alert('❌ فشل في إرسال رسالة الاختبار: ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ خطأ في الاتصال: ' + error);
        });
    }
}
</script>
{% endblock %}
