/* نظام البريد الإلكتروني - الأنماط */

/* الألوان الأساسية */
:root {
    --email-primary: #667eea;
    --email-secondary: #764ba2;
    --email-success: #28a745;
    --email-warning: #ffc107;
    --email-danger: #dc3545;
    --email-info: #17a2b8;
    --email-light: #f8f9fa;
    --email-dark: #343a40;
    --email-border: #dee2e6;
    --email-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* تخطيط عام */
.email-layout {
    display: flex;
    height: calc(100vh - 80px);
    background: var(--email-light);
}

/* الشريط الجانبي */
.email-sidebar {
    width: 280px;
    background: white;
    border-right: 1px solid var(--email-border);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.email-sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--email-border);
    background: linear-gradient(135deg, var(--email-primary) 0%, var(--email-secondary) 100%);
    color: white;
}

.email-sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

/* أزرار الإجراءات */
.email-action-btn {
    width: 100%;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.email-action-btn.primary {
    background: linear-gradient(135deg, var(--email-primary) 0%, var(--email-secondary) 100%);
    color: white;
}

.email-action-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--email-shadow);
}

.email-action-btn.secondary {
    background: white;
    color: var(--email-primary);
    border: 2px solid var(--email-primary);
}

.email-action-btn.secondary:hover {
    background: var(--email-primary);
    color: white;
}

/* قائمة المجلدات */
.email-folders {
    list-style: none;
    padding: 0;
    margin: 0;
}

.email-folder {
    margin-bottom: 5px;
}

.email-folder-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 15px;
    color: var(--email-dark);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.email-folder-link:hover {
    background: var(--email-light);
    color: var(--email-primary);
}

.email-folder-link.active {
    background: var(--email-primary);
    color: white;
}

.email-folder-icon {
    margin-right: 10px;
    width: 16px;
    text-align: center;
}

.email-folder-count {
    background: var(--email-info);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.email-folder-link.active .email-folder-count {
    background: rgba(255, 255, 255, 0.3);
}

/* المحتوى الرئيسي */
.email-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* شريط الأدوات */
.email-toolbar {
    background: white;
    padding: 15px 20px;
    border-bottom: 1px solid var(--email-border);
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.email-toolbar-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.email-toolbar-btn {
    padding: 8px 12px;
    border: 1px solid var(--email-border);
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.email-toolbar-btn:hover {
    background: var(--email-light);
    border-color: var(--email-primary);
}

.email-toolbar-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.email-toolbar-btn.danger:hover {
    background: var(--email-danger);
    color: white;
    border-color: var(--email-danger);
}

/* قائمة الرسائل */
.email-list {
    flex: 1;
    overflow-y: auto;
    background: white;
}

.email-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 15px;
}

.email-item:hover {
    background: var(--email-light);
}

.email-item.unread {
    background: #fff8e1;
    border-left: 4px solid var(--email-warning);
}

.email-item.selected {
    background: #e3f2fd;
    border-left: 4px solid var(--email-primary);
}

.email-item.important {
    border-left: 4px solid var(--email-danger);
}

/* عناصر الرسالة */
.email-checkbox {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.email-star {
    color: #ddd;
    cursor: pointer;
    font-size: 16px;
    transition: color 0.2s ease;
}

.email-star:hover {
    color: var(--email-warning);
}

.email-star.starred {
    color: var(--email-warning);
}

.email-content {
    flex: 1;
    min-width: 0;
}

.email-sender {
    font-weight: 600;
    color: var(--email-dark);
    margin-bottom: 4px;
    font-size: 0.95rem;
}

.email-subject {
    color: #555;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.9rem;
}

.email-preview {
    color: #888;
    font-size: 0.85rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.email-date {
    color: #888;
    font-size: 0.8rem;
    white-space: nowrap;
    min-width: 80px;
    text-align: right;
}

.email-attachments {
    color: var(--email-info);
    font-size: 0.8rem;
}

/* بطاقات الإحصائيات */
.email-stats {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: var(--email-shadow);
}

.email-stats-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--email-dark);
    margin-bottom: 15px;
}

.email-stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.email-stat-item {
    text-align: center;
    padding: 10px;
    background: var(--email-light);
    border-radius: 6px;
    transition: transform 0.2s ease;
}

.email-stat-item:hover {
    transform: translateY(-2px);
}

.email-stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--email-primary);
    display: block;
}

.email-stat-label {
    font-size: 0.75rem;
    color: #666;
    margin-top: 4px;
}

/* نوافذ منبثقة */
.email-modal .modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.email-modal .modal-header {
    background: linear-gradient(135deg, var(--email-primary) 0%, var(--email-secondary) 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.email-modal .modal-body {
    padding: 30px;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .email-layout {
        flex-direction: column;
        height: auto;
    }
    
    .email-sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid var(--email-border);
    }
    
    .email-sidebar-content {
        padding: 15px;
    }
    
    .email-toolbar {
        padding: 10px 15px;
        flex-wrap: wrap;
    }
    
    .email-item {
        padding: 12px 15px;
        gap: 10px;
    }
    
    .email-date {
        min-width: 60px;
        font-size: 0.75rem;
    }
    
    .email-stats-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }
}

/* تأثيرات التحميل */
.email-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: var(--email-primary);
}

.email-loading .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--email-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* رسائل فارغة */
.email-empty {
    text-align: center;
    padding: 60px 20px;
    color: #888;
}

.email-empty-icon {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 20px;
}

.email-empty-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #666;
}

.email-empty-text {
    font-size: 1rem;
    color: #888;
}

/* تأثيرات الانتقال */
.email-fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.email-slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* شارات الإشعارات */
.email-badge {
    background: var(--email-danger);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
    position: absolute;
    top: -5px;
    right: -5px;
    min-width: 18px;
    text-align: center;
}

.email-badge.success {
    background: var(--email-success);
}

.email-badge.warning {
    background: var(--email-warning);
}

.email-badge.info {
    background: var(--email-info);
}
