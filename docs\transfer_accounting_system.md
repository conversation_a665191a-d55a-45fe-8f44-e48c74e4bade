# نظام الترحيل المحاسبي للحوالات
## Transfer Accounting System

## 📋 نظرة عامة

نظام الترحيل المحاسبي للحوالات يتكامل مع جدول `CURRENT_BALANCES` الموجود لتنفيذ الترحيلات المحاسبية التالية:

### الجانب المدين (Debit):
- ترحيل المبالغ المحولة للموردين حسب تفاصيل التوزيع
- زيادة أرصدة الموردين

### الجانب الدائن (Credit):
- ترحيل مبلغ الحوالة الكلي من رصيد الصراف/البنك
- تقليل رصيد الصراف/البنك

## 🏗️ هيكل النظام

### 1. الجداول الأساسية

#### `CURRENT_BALANCES` (موجود مسبقاً)
```sql
- entity_type_code: نوع الكيان (SUPPLIER, MONEY_CHANGER, BANK)
- entity_id: معرف الكيان
- currency_code: رمز العملة
- debit_amount: إجمالي المدين
- credit_amount: إجمالي الدائن
- current_balance: الرصيد الجاري
```

#### `transfer_supplier_distributions` (جديد)
```sql
- transfer_id: معرف الحوالة
- supplier_id: معرف المورد
- amount: المبلغ المخصص للمورد
- currency_code: رمز العملة
```

#### `transfer_activity_log` (جديد)
```sql
- transfer_id: معرف الحوالة
- activity_type: نوع النشاط (EXECUTED, CANCELLED, ERROR)
- description: وصف النشاط
- created_at: تاريخ النشاط
```

### 2. الإجراءات المخزنة

#### `EXECUTE_TRANSFER_ACCOUNTING`
```sql
EXECUTE_TRANSFER_ACCOUNTING(
    p_transfer_id,
    p_money_changer_id,
    p_total_amount,
    p_currency_code,
    p_supplier_distributions,
    p_user_id
)
```

**الوظيفة:**
1. التحقق من صحة البيانات
2. ترحيل المبالغ للموردين (مدين)
3. ترحيل المبلغ الكلي من الصراف (دائن)
4. تحديث حالة الحوالة إلى 'executed'
5. تسجيل العملية في سجل الأنشطة

#### `CANCEL_TRANSFER_ACCOUNTING`
```sql
CANCEL_TRANSFER_ACCOUNTING(
    p_transfer_id,
    p_user_id
)
```

**الوظيفة:**
1. التحقق من حالة الحوالة
2. عكس ترحيلات الموردين (دائن)
3. عكس ترحيل الصراف (مدين)
4. تحديث حالة الحوالة إلى 'cancelled'
5. تسجيل العملية في سجل الأنشطة

### 3. خدمة Python

#### `TransferAccountingService`
```python
- execute_transfer(): تنفيذ الحوالة وترحيل الأرصدة
- cancel_transfer(): إلغاء الحوالة وعكس الترحيلات
- get_transfer_accounting_details(): تفاصيل الترحيل
- get_entity_balance(): رصيد كيان معين
```

### 4. Flask Routes

#### `/transfers/accounting/execute` (POST)
```json
{
    "transfer_id": 123,
    "money_changer_id": 456,
    "total_amount": 10000.00,
    "currency_code": "SAR",
    "supplier_distributions": [
        {"supplier_id": 1, "amount": 5000.00},
        {"supplier_id": 2, "amount": 5000.00}
    ]
}
```

#### `/transfers/accounting/cancel` (POST)
```json
{
    "transfer_id": 123
}
```

## 🔄 سير العمل

### 1. تنفيذ الحوالة

```mermaid
graph TD
    A[طلب تنفيذ الحوالة] --> B[التحقق من البيانات]
    B --> C[بدء المعاملة]
    C --> D[ترحيل للموردين - مدين]
    D --> E[ترحيل من الصراف - دائن]
    E --> F[تحديث حالة الحوالة]
    F --> G[تسجيل النشاط]
    G --> H[إنهاء المعاملة]
```

### 2. إلغاء الحوالة

```mermaid
graph TD
    A[طلب إلغاء الحوالة] --> B[التحقق من الحالة]
    B --> C[بدء المعاملة]
    C --> D[عكس ترحيل الموردين - دائن]
    D --> E[عكس ترحيل الصراف - مدين]
    E --> F[تحديث حالة الحوالة]
    F --> G[تسجيل النشاط]
    G --> H[إنهاء المعاملة]
```

## 📊 أمثلة عملية

### مثال 1: تنفيذ حوالة بقيمة 10,000 ريال

**البيانات:**
- الحوالة: 10,000 ريال
- المورد الأول: 6,000 ريال
- المورد الثاني: 4,000 ريال
- الصراف: رقم 5

**الترحيلات:**
```sql
-- المورد الأول (مدين)
UPDATE CURRENT_BALANCES 
SET debit_amount = debit_amount + 6000,
    current_balance = current_balance + 6000
WHERE entity_type_code = 'SUPPLIER' AND entity_id = 1

-- المورد الثاني (مدين)
UPDATE CURRENT_BALANCES 
SET debit_amount = debit_amount + 4000,
    current_balance = current_balance + 4000
WHERE entity_type_code = 'SUPPLIER' AND entity_id = 2

-- الصراف (دائن)
UPDATE CURRENT_BALANCES 
SET credit_amount = credit_amount + 10000,
    current_balance = current_balance - 10000
WHERE entity_type_code = 'MONEY_CHANGER' AND entity_id = 5
```

### مثال 2: إلغاء الحوالة السابقة

**الترحيلات العكسية:**
```sql
-- عكس المورد الأول (دائن)
UPDATE CURRENT_BALANCES 
SET credit_amount = credit_amount + 6000,
    current_balance = current_balance - 6000
WHERE entity_type_code = 'SUPPLIER' AND entity_id = 1

-- عكس المورد الثاني (دائن)
UPDATE CURRENT_BALANCES 
SET credit_amount = credit_amount + 4000,
    current_balance = current_balance - 4000
WHERE entity_type_code = 'SUPPLIER' AND entity_id = 2

-- عكس الصراف (مدين)
UPDATE CURRENT_BALANCES 
SET debit_amount = debit_amount + 10000,
    current_balance = current_balance + 10000
WHERE entity_type_code = 'MONEY_CHANGER' AND entity_id = 5
```

## 🔒 الأمان والتحقق

### 1. التحقق من البيانات
- صحة معرفات الحوالة والموردين والصرافين
- تطابق مجموع التوزيعات مع مبلغ الحوالة
- صحة المبالغ (أكبر من صفر)

### 2. المعاملات (Transactions)
- استخدام SAVEPOINT لضمان التكامل
- ROLLBACK في حالة الخطأ
- COMMIT عند النجاح

### 3. تسجيل العمليات
- سجل مفصل لجميع الأنشطة
- تسجيل الأخطاء مع التفاصيل
- تتبع المستخدم والوقت

## 📈 المراقبة والتقارير

### 1. Views للاستعلامات
- `supplier_transfers_summary`: ملخص حوالات الموردين
- `money_changer_transfers_summary`: ملخص حوالات الصرافين
- `current_balances_detailed`: الأرصدة مع تفاصيل الكيان

### 2. التقارير المتاحة
- رصيد كيان معين
- تفاصيل ترحيل حوالة معينة
- سجل أنشطة الحوالة
- ملخص يومي للحوالات

## 🚀 التطبيق

### 1. تنفيذ قاعدة البيانات
```bash
# تنفيذ الجداول
sqlplus user/password @database/transfer_accounting_tables.sql

# تنفيذ الإجراءات المخزنة
sqlplus user/password @database/transfer_accounting_procedures.sql
```

### 2. تكوين Flask
```python
# في app/__init__.py
from app.transfers.accounting_routes import accounting_bp
app.register_blueprint(accounting_bp)
```

### 3. اختبار النظام
```python
# اختبار تنفيذ حوالة
response = client.post('/transfers/accounting/execute', json={
    "transfer_id": 1,
    "money_changer_id": 1,
    "total_amount": 10000.00,
    "supplier_distributions": [
        {"supplier_id": 1, "amount": 6000.00},
        {"supplier_id": 2, "amount": 4000.00}
    ]
})
```

## 🔧 الصيانة

### 1. النسخ الاحتياطي
- نسخ احتياطي يومي لجدول CURRENT_BALANCES
- نسخ احتياطي لجداول الحوالات

### 2. المراقبة
- مراقبة أداء الإجراءات المخزنة
- تتبع الأخطاء في سجل الأنشطة
- مراجعة دورية للأرصدة

### 3. التحسين
- فهرسة الجداول حسب الاستخدام
- تحسين الاستعلامات
- أرشفة البيانات القديمة
