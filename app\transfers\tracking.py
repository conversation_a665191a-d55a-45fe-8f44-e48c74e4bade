#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نظام تتبع الحوالات
Transfer Tracking Management System
"""

from flask import render_template, request as flask_request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from . import transfers_bp
from database_manager import DatabaseManager
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@transfers_bp.route('/tracking')
@login_required
def tracking():
    """صفحة تتبع الحوالات الرئيسية"""
    return render_template('transfers/tracking.html')

@transfers_bp.route('/tracking-dashboard')
@login_required
def tracking_dashboard():
    """لوحة تحكم تتبع الحوالات"""
    return render_template('transfers/tracking_dashboard.html')

@transfers_bp.route('/track-request/<request_number>')
@login_required
def track_single_request(request_number):
    """تتبع طلب محدد"""
    return render_template('transfers/track_single.html', request_number=request_number)

# ===== APIs نظام التتبع =====

@transfers_bp.route('/api/track-requests', methods=['GET'])
@login_required
def api_track_requests():
    """API للحصول على الحوالات المنفذة مع فلترة متقدمة"""
    try:
        db = DatabaseManager()

        # معاملات الفلترة المتقدمة
        branch_filter = flask_request.args.get('branch', '')
        money_changer_filter = flask_request.args.get('money_changer', '')
        supplier_filter = flask_request.args.get('supplier', '')
        date_from = flask_request.args.get('date_from', '')
        date_to = flask_request.args.get('date_to', '')
        search_term = flask_request.args.get('search', '')

        # استعلام مبسط للحوالات المنفذة مع الفرع من TRANSFERS
        base_query = """
        SELECT
            tr.id, tr.request_number, tr.amount, tr.currency, tr.purpose,
            tr.status, tr.created_at, tr.updated_at,
            b.beneficiary_name, b.bank_account, b.bank_name, b.bank_country,
            COALESCE(br.BRN_LNAME, 'فرع غير محدد') as branch_name,
            COALESCE(mcb.NAME, 'غير محدد') as money_changer_bank_name,
            COALESCE(mcb.TYPE, 'غير محدد') as transfer_type,
            tr.priority_level, tr.risk_level,
            -- بيانات التنفيذ
            t.transfer_number, t.execution_reference, t.execution_date,
            t.execution_method, t.execution_notes, t.total_suppliers
        FROM TRANSFER_REQUESTS tr
        INNER JOIN TRANSFERS t ON tr.id = t.request_id
        LEFT JOIN BENEFICIARIES b ON tr.beneficiary_id = b.id
        LEFT JOIN BRANCHES br ON t.branch_id = br.BRN_NO
        LEFT JOIN MONEY_CHANGERS_BANKS mcb ON tr.money_changer_bank_id = mcb.ID
        WHERE tr.status = 'executed'
        """

        params = []

        # فلاتر متقدمة للحوالات المنفذة
        if branch_filter:
            base_query += f" AND t.branch_id = :{len(params)+1}"
            params.append(branch_filter)

        if money_changer_filter:
            base_query += f" AND tr.money_changer_bank_id = :{len(params)+1}"
            params.append(money_changer_filter)

        if supplier_filter:
            base_query += f" AND tes.supplier_id = :{len(params)+1}"
            params.append(supplier_filter)

        if date_from:
            base_query += f" AND t.execution_date >= TO_DATE(:{len(params)+1}, 'YYYY-MM-DD')"
            params.append(date_from)

        if date_to:
            base_query += f" AND t.execution_date <= TO_DATE(:{len(params)+1}, 'YYYY-MM-DD') + 1"
            params.append(date_to)

        if search_term:
            base_query += f" AND (UPPER(tr.request_number) LIKE UPPER(:{len(params)+1}) OR UPPER(b.beneficiary_name) LIKE UPPER(:{len(params)+1}) OR UPPER(t.execution_reference) LIKE UPPER(:{len(params)+1}))"
            search_pattern = f"%{search_term}%"
            params.extend([search_pattern, search_pattern, search_pattern])

        # إضافة ORDER BY
        base_query += " ORDER BY t.execution_date DESC"

        result = db.execute_query(base_query, params)

        executed_transfers = []
        if result:
            for row in result:
                executed_transfers.append({
                    'id': int(row[0]) if row[0] else 0,
                    'request_number': str(row[1]) if row[1] else '',
                    'original_amount': float(row[2]) if row[2] else 0,
                    'currency': str(row[3]) if row[3] else '',
                    'purpose': str(row[4]) if row[4] else '',
                    'status': str(row[5]) if row[5] else '',
                    'created_at': row[6].strftime('%Y-%m-%d %H:%M:%S') if row[6] else None,
                    'updated_at': row[7].strftime('%Y-%m-%d %H:%M:%S') if row[7] else None,
                    'beneficiary_name': str(row[8]) if row[8] else '',
                    'bank_account': str(row[9]) if row[9] else '',
                    'bank_name': str(row[10]) if row[10] else '',
                    'bank_country': str(row[11]) if row[11] else '',
                    'branch_name': str(row[12]) if row[12] else '',
                    'money_changer_bank_name': str(row[13]) if row[13] else '',
                    'transfer_type': str(row[14]) if row[14] else '',
                    'priority_level': str(row[15]) if row[15] else 'normal',
                    'risk_level': str(row[16]) if row[16] else 'low',
                    # بيانات التنفيذ
                    'transfer_number': str(row[17]) if row[17] else '',
                    'execution_reference': str(row[18]) if row[18] else '',
                    'execution_date': row[19].strftime('%Y-%m-%d %H:%M:%S') if row[19] else None,
                    'execution_method': str(row[20]) if row[20] else '',
                    'execution_notes': str(row[21]) if row[21] else '',
                    'total_suppliers': int(row[22]) if row[22] else 0,
                    'actual_suppliers_count': 0,  # سنحسبها لاحقاً
                    'total_distributed_amount': 0,  # سنحسبها لاحقاً
                    'suppliers_list': '',  # سنجلبها لاحقاً
                    'days_since_execution': (datetime.now() - row[19]).days if row[19] else 0
                })

        # جلب بيانات الموردين لكل حوالة
        for transfer in executed_transfers:
            # جلب ID الحوالة من جدول TRANSFERS
            transfer_id_query = "SELECT id FROM TRANSFERS WHERE request_id = :1"
            transfer_id_result = db.execute_query(transfer_id_query, [transfer['id']])

            if transfer_id_result:
                transfer_id = transfer_id_result[0][0]

                suppliers_query = """
                SELECT tes.amount, tes.currency, s.name_ar, s.supplier_code, tes.supplier_name
                FROM transfer_execution_suppliers tes
                LEFT JOIN SUPPLIERS s ON tes.supplier_id = s.id
                WHERE tes.transfer_id = :1
                ORDER BY tes.execution_order
                """

                suppliers_result = db.execute_query(suppliers_query, [transfer_id])

                if suppliers_result:
                    transfer['actual_suppliers_count'] = len(suppliers_result)
                    transfer['total_distributed_amount'] = sum(float(row[0]) for row in suppliers_result if row[0])

                    suppliers_list = []
                    for row in suppliers_result:
                        # استخدام اسم المورد من الجدول أو من SUPPLIERS
                        supplier_name = row[2] if row[2] else row[4] if row[4] else row[3] if row[3] else 'غير محدد'
                        amount = float(row[0]) if row[0] else 0
                        currency = row[1] if row[1] else ''
                        suppliers_list.append(f"{supplier_name} ({amount} {currency})")

                    transfer['suppliers_list'] = ', '.join(suppliers_list)

                    print(f"✅ الطلب {transfer['request_number']}: {len(suppliers_result)} موردين، إجمالي: {transfer['total_distributed_amount']}")
                else:
                    print(f"⚠️ لا توجد موردين للحوالة {transfer_id} (الطلب {transfer['request_number']})")
            else:
                print(f"⚠️ لم يتم العثور على ID الحوالة للطلب {transfer['id']} ({transfer['request_number']})")

        return jsonify({
            'success': True,
            'data': executed_transfers,
            'total_count': len(executed_transfers)
        })

    except Exception as e:
        logger.error(f"خطأ في تتبع الطلبات: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في تتبع الطلبات: {str(e)}'
        }), 500

@transfers_bp.route('/api/tracking-filters', methods=['GET'])
@login_required
def api_get_tracking_filters():
    """API للحصول على خيارات الفلترة المتقدمة"""
    try:
        db = DatabaseManager()

        # جلب جميع الفروع النشطة (مبسط)
        branches_query = """
        SELECT BRN_NO, BRN_LNAME
        FROM BRANCHES
        WHERE BRN_STATUS = 'A'
        ORDER BY BRN_LNAME
        """
        branches_result = db.execute_query(branches_query)

        print(f"🔍 تم جلب {len(branches_result) if branches_result else 0} فرع")

        # إذا لم نجد فروع، نجرب استعلام أبسط
        if not branches_result:
            simple_query = "SELECT BRN_NO, BRN_LNAME FROM BRANCHES"
            simple_result = db.execute_query(simple_query)
            print(f"🔍 استعلام بسيط: {len(simple_result) if simple_result else 0} فرع")
            branches_result = simple_result

        branches = []
        if branches_result:
            for row in branches_result:
                branches.append({
                    'id': int(row[0]),
                    'name': str(row[1])
                })
                print(f"📋 فرع: {row[0]} - {row[1]}")

        # جلب الصرافين والبنوك
        money_changers_query = "SELECT ID, NAME, TYPE FROM MONEY_CHANGERS_BANKS WHERE IS_ACTIVE = 1 ORDER BY NAME"
        money_changers_result = db.execute_query(money_changers_query)

        money_changers = []
        if money_changers_result:
            for row in money_changers_result:
                money_changers.append({
                    'id': int(row[0]),
                    'name': str(row[1]),
                    'type': str(row[2])
                })

        # جلب الموردين المستخدمين في التنفيذ
        suppliers_query = """
        SELECT DISTINCT s.id, s.name_ar, s.supplier_code, s.supplier_type
        FROM SUPPLIERS s
        INNER JOIN transfer_execution_suppliers tes ON s.id = tes.supplier_id
        WHERE s.is_active = 1
        ORDER BY s.name_ar
        """
        suppliers_result = db.execute_query(suppliers_query)

        suppliers = []
        if suppliers_result:
            for row in suppliers_result:
                suppliers.append({
                    'id': int(row[0]),
                    'name': str(row[1]) if row[1] else str(row[2]),
                    'code': str(row[2]) if row[2] else '',
                    'type': str(row[3]) if row[3] else ''
                })

        return jsonify({
            'success': True,
            'data': {
                'branches': branches,
                'money_changers': money_changers,
                'suppliers': suppliers
            }
        })

    except Exception as e:
        logger.error(f"خطأ في جلب فلاتر التتبع: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب فلاتر التتبع: {str(e)}'
        }), 500

@transfers_bp.route('/api/track-request/<request_number>', methods=['GET'])
@login_required
def api_track_single_request(request_number):
    """API لتتبع طلب محدد بالتفصيل"""
    try:
        db = DatabaseManager()

        # جلب بيانات الطلب الأساسية
        request_query = """
        SELECT
            tr.id, tr.request_number, tr.amount, tr.currency, tr.purpose,
            tr.status, tr.created_at, tr.updated_at,
            b.beneficiary_name, b.bank_account, b.bank_name, b.bank_country,
            COALESCE(br.BRN_LNAME, 'فرع غير محدد') as branch_name,
            COALESCE(mcb.NAME, 'غير محدد') as money_changer_bank_name,
            sc.status_name
        FROM TRANSFER_REQUESTS tr
        LEFT JOIN BENEFICIARIES b ON tr.beneficiary_id = b.id
        LEFT JOIN BRANCHES br ON tr.branch_id = br.BRN_NO
        LEFT JOIN MONEY_CHANGERS_BANKS mcb ON tr.money_changer_bank_id = mcb.ID
        LEFT JOIN TRANSFER_STATUS_CODES sc ON tr.status = sc.status_code
        WHERE tr.request_number = :1
        """

        request_result = db.execute_query(request_query, [request_number])

        if not request_result:
            return jsonify({
                'success': False,
                'message': 'الطلب غير موجود'
            }), 404

        request_data = request_result[0]
        request_id = request_data[0]

        # جلب سجل التتبع
        tracking_query = """
        SELECT
            tt.status_code, tt.status_description, tt.tracking_date,
            tt.location_info, tt.notes, tt.is_milestone,
            sc.status_name
        FROM TRANSFER_TRACKING tt
        LEFT JOIN TRANSFER_STATUS_CODES sc ON tt.status_code = sc.status_code
        WHERE tt.request_id = :1
        ORDER BY tt.tracking_date DESC
        """

        tracking_result = db.execute_query(tracking_query, [request_id])

        tracking_history = []
        if tracking_result:
            for row in tracking_result:
                tracking_history.append({
                    'status_code': str(row[0]) if row[0] else '',
                    'status_description': str(row[1]) if row[1] else '',
                    'tracking_date': row[2].strftime('%Y-%m-%d %H:%M:%S') if row[2] else None,
                    'location_info': str(row[3]) if row[3] else '',
                    'notes': str(row[4]) if row[4] else '',
                    'is_milestone': bool(row[5]) if row[5] else False,
                    'status_name': str(row[6]) if row[6] else ''
                })

        # جلب بيانات التنفيذ إذا وجدت
        execution_query = """
        SELECT
            te.execution_reference, te.execution_date, te.execution_amount,
            te.execution_currency, te.exchange_rate, te.commission_amount,
            te.net_amount, te.receipt_number, te.execution_notes
        FROM TRANSFER_EXECUTIONS te
        WHERE te.request_id = :1
        """

        execution_result = db.execute_query(execution_query, [request_id])
        execution_data = None

        if execution_result:
            exec_row = execution_result[0]
            execution_data = {
                'execution_reference': str(exec_row[0]) if exec_row[0] else '',
                'execution_date': exec_row[1].strftime('%Y-%m-%d %H:%M:%S') if exec_row[1] else None,
                'execution_amount': float(exec_row[2]) if exec_row[2] else 0,
                'execution_currency': str(exec_row[3]) if exec_row[3] else '',
                'exchange_rate': float(exec_row[4]) if exec_row[4] else 1.0,
                'commission_amount': float(exec_row[5]) if exec_row[5] else 0,
                'net_amount': float(exec_row[6]) if exec_row[6] else 0,
                'receipt_number': str(exec_row[7]) if exec_row[7] else '',
                'execution_notes': str(exec_row[8]) if exec_row[8] else ''
            }

        # تجميع البيانات
        response_data = {
            'request_info': {
                'id': int(request_data[0]) if request_data[0] else 0,
                'request_number': str(request_data[1]) if request_data[1] else '',
                'amount': float(request_data[2]) if request_data[2] else 0,
                'currency': str(request_data[3]) if request_data[3] else '',
                'purpose': str(request_data[4]) if request_data[4] else '',
                'status': str(request_data[5]) if request_data[5] else '',
                'created_at': request_data[6].strftime('%Y-%m-%d %H:%M:%S') if request_data[6] else None,
                'updated_at': request_data[7].strftime('%Y-%m-%d %H:%M:%S') if request_data[7] else None,
                'beneficiary_name': str(request_data[8]) if request_data[8] else '',
                'bank_account': str(request_data[9]) if request_data[9] else '',
                'bank_name': str(request_data[10]) if request_data[10] else '',
                'bank_country': str(request_data[11]) if request_data[11] else '',
                'branch_name': str(request_data[12]) if request_data[12] else '',
                'money_changer_bank_name': str(request_data[13]) if request_data[13] else '',
                'status_name': str(request_data[14]) if request_data[14] else ''
            },
            'tracking_history': tracking_history,
            'execution_data': execution_data
        }

        return jsonify({
            'success': True,
            'data': response_data
        })

    except Exception as e:
        logger.error(f"خطأ في تتبع الطلب {request_number}: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في تتبع الطلب: {str(e)}'
        }), 500

@transfers_bp.route('/api/tracking-statistics', methods=['GET'])
@login_required
def api_tracking_statistics():
    """API للحصول على إحصائيات التتبع"""
    try:
        db = DatabaseManager()

        # إحصائيات عامة
        stats_query = """
        SELECT
            COUNT(*) as total_requests,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
            COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
            COUNT(CASE WHEN status = 'executed' THEN 1 END) as executed_count,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
            COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count,
            COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_count,
            SUM(amount) as total_amount,
            AVG(amount) as average_amount
        FROM TRANSFER_REQUESTS
        WHERE created_at >= SYSDATE - 30
        """

        stats_result = db.execute_query(stats_query)

        if stats_result:
            stats_row = stats_result[0]
            statistics = {
                'total_requests': int(stats_row[0]) if stats_row[0] else 0,
                'pending_count': int(stats_row[1]) if stats_row[1] else 0,
                'approved_count': int(stats_row[2]) if stats_row[2] else 0,
                'executed_count': int(stats_row[3]) if stats_row[3] else 0,
                'completed_count': int(stats_row[4]) if stats_row[4] else 0,
                'rejected_count': int(stats_row[5]) if stats_row[5] else 0,
                'cancelled_count': int(stats_row[6]) if stats_row[6] else 0,
                'total_amount': float(stats_row[7]) if stats_row[7] else 0,
                'average_amount': float(stats_row[8]) if stats_row[8] else 0
            }
        else:
            statistics = {
                'total_requests': 0,
                'pending_count': 0,
                'approved_count': 0,
                'executed_count': 0,
                'completed_count': 0,
                'rejected_count': 0,
                'cancelled_count': 0,
                'total_amount': 0,
                'average_amount': 0
            }

        return jsonify({
            'success': True,
            'data': statistics
        })

    except Exception as e:
        logger.error(f"خطأ في جلب إحصائيات التتبع: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب الإحصائيات: {str(e)}'
        }), 500

@transfers_bp.route('/api/add-tracking-update/<int:request_id>', methods=['POST'])
@login_required
def api_add_tracking_update(request_id):
    """API لإضافة تحديث تتبع جديد"""
    try:
        db = DatabaseManager()
        data = flask_request.get_json()

        # التحقق من وجود الطلب
        check_query = "SELECT request_number FROM TRANSFER_REQUESTS WHERE id = :1"
        check_result = db.execute_query(check_query, [request_id])

        if not check_result:
            return jsonify({
                'success': False,
                'message': 'الطلب غير موجود'
            }), 404

        user_id = int(current_user.id) if current_user.id else 1

        # بيانات التحديث
        status_code = data.get('status_code', '').strip()
        status_description = data.get('status_description', '').strip()
        location_info = data.get('location_info', '').strip()
        notes = data.get('notes', '').strip()
        is_milestone = bool(data.get('is_milestone', False))

        if not status_code or not status_description:
            return jsonify({
                'success': False,
                'message': 'رمز الحالة ووصف الحالة مطلوبان'
            }), 400

        # إدراج تحديث التتبع
        tracking_query = """
        INSERT INTO TRANSFER_TRACKING (
            id, request_id, status_code, status_description, tracking_date,
            location_info, updated_by, notes, is_milestone
        ) VALUES (
            TRACKING_SEQ.NEXTVAL, :1, :2, :3, SYSDATE, :4, :5, :6, :7
        )
        """

        db.execute_update(tracking_query, [
            request_id, status_code, status_description, location_info,
            user_id, notes, 1 if is_milestone else 0
        ])

        logger.info(f"تم إضافة تحديث تتبع للطلب {request_id} بواسطة المستخدم {user_id}")

        return jsonify({
            'success': True,
            'message': 'تم إضافة تحديث التتبع بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في إضافة تحديث التتبع: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في إضافة تحديث التتبع: {str(e)}'
        }), 500

@transfers_bp.route('/api/status-codes', methods=['GET'])
@login_required
def api_get_status_codes():
    """API للحصول على رموز الحالات المتاحة"""
    try:
        db = DatabaseManager()

        query = """
        SELECT status_code, status_name, description_text, is_milestone
        FROM TRANSFER_STATUS_CODES
        WHERE is_active = 1
        ORDER BY display_order, status_name
        """

        result = db.execute_query(query)

        status_codes = []
        if result:
            for row in result:
                status_codes.append({
                    'status_code': str(row[0]) if row[0] else '',
                    'status_name': str(row[1]) if row[1] else '',
                    'description_text': str(row[2]) if row[2] else '',
                    'is_milestone': bool(row[3]) if row[3] else False
                })

        return jsonify({
            'success': True,
            'data': status_codes
        })

    except Exception as e:
        logger.error(f"خطأ في جلب رموز الحالات: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب رموز الحالات: {str(e)}'
        }), 500

@transfers_bp.route('/api/dashboard-summary', methods=['GET'])
@login_required
def api_dashboard_summary():
    """API للحصول على ملخص شامل للوحة التحكم"""
    try:
        db = DatabaseManager()

        # ملخص شامل للحالات
        summary_query = """
        SELECT
            COUNT(*) as total_requests,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
            COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
            COUNT(CASE WHEN status = 'executed' THEN 1 END) as executed_count,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
            COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count,
            SUM(amount) as total_amount,
            COUNT(CASE WHEN priority_level = 'urgent' AND status IN ('pending', 'approved') THEN 1 END) as urgent_pending,
            COUNT(CASE WHEN created_at >= SYSDATE - 1 THEN 1 END) as today_requests,
            COUNT(CASE WHEN created_at >= SYSDATE - 7 THEN 1 END) as week_requests
        FROM TRANSFER_REQUESTS
        WHERE created_at >= SYSDATE - 30
        """

        result = db.execute_query(summary_query)

        if result:
            row = result[0]
            summary = {
                'total_requests': int(row[0]) if row[0] else 0,
                'pending_count': int(row[1]) if row[1] else 0,
                'approved_count': int(row[2]) if row[2] else 0,
                'executed_count': int(row[3]) if row[3] else 0,
                'completed_count': int(row[4]) if row[4] else 0,
                'rejected_count': int(row[5]) if row[5] else 0,
                'total_amount': float(row[6]) if row[6] else 0,
                'urgent_pending': int(row[7]) if row[7] else 0,
                'today_requests': int(row[8]) if row[8] else 0,
                'week_requests': int(row[9]) if row[9] else 0
            }
        else:
            summary = {
                'total_requests': 0,
                'pending_count': 0,
                'approved_count': 0,
                'executed_count': 0,
                'completed_count': 0,
                'rejected_count': 0,
                'total_amount': 0,
                'urgent_pending': 0,
                'today_requests': 0,
                'week_requests': 0
            }

        return jsonify({
            'success': True,
            'data': summary
        })

    except Exception as e:
        logger.error(f"خطأ في جلب ملخص لوحة التحكم: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب الملخص: {str(e)}'
        }), 500

@transfers_bp.route('/api/tracking-statistics', methods=['GET'])
@login_required
def api_get_tracking_statistics():
    """API للحصول على إحصائيات الحوالات المنفذة"""
    try:
        db = DatabaseManager()

        # استعلامات بسيطة ومباشرة
        try:
            # إجمالي الحوالات المنفذة
            total_executed_result = db.execute_query("SELECT COUNT(*) FROM TRANSFER_REQUESTS WHERE status = 'executed'")
            total_executed = int(total_executed_result[0][0]) if total_executed_result and total_executed_result[0][0] is not None else 0

            # إجمالي المبالغ المنفذة
            total_amount_result = db.execute_query("SELECT SUM(amount) FROM TRANSFER_REQUESTS WHERE status = 'executed'")
            total_amount = float(total_amount_result[0][0]) if total_amount_result and total_amount_result[0][0] is not None else 0

            # العملة الأكثر استخداماً
            currency_result = db.execute_query("SELECT currency FROM TRANSFER_REQUESTS WHERE status = 'executed' AND ROWNUM = 1")
            currency = str(currency_result[0][0]) if currency_result and currency_result[0][0] else 'ريال'

            # إجمالي الموردين المستخدمين
            suppliers_result = db.execute_query("SELECT COUNT(DISTINCT supplier_id) FROM transfer_execution_suppliers")
            total_suppliers = int(suppliers_result[0][0]) if suppliers_result and suppliers_result[0][0] is not None else 0

            # تنفيذات اليوم
            today_result = db.execute_query("""
                SELECT COUNT(*) FROM TRANSFER_REQUESTS tr
                INNER JOIN TRANSFERS t ON tr.id = t.request_id
                WHERE tr.status = 'executed' AND TRUNC(t.execution_date) = TRUNC(SYSDATE)
            """)
            today_executions = int(today_result[0][0]) if today_result and today_result[0][0] is not None else 0

        except Exception as query_error:
            print(f"🚫 خطأ في استعلامات الإحصائيات: {query_error}")
            total_executed = 0
            total_amount = 0
            currency = 'ريال'
            total_suppliers = 0
            today_executions = 0

        # طباعة النتائج للتشخيص
        print(f"🔍 إحصائيات نهائية:")
        print(f"  - منفذة: {total_executed}")
        print(f"  - مبلغ: {total_amount}")
        print(f"  - عملة: {currency}")
        print(f"  - موردين: {total_suppliers}")
        print(f"  - اليوم: {today_executions}")

        # بناء الإحصائيات النهائية
        stats = {
            'total_executed': total_executed,
            'total_amount': total_amount,
            'currencies': currency,
            'total_suppliers': total_suppliers,
            'today_executions': today_executions
        }

        print(f"📊 JSON الإحصائيات: {stats}")

        return jsonify({
            'success': True,
            'data': stats
        })

    except Exception as e:
        logger.error(f"خطأ في جلب إحصائيات التتبع: {e}")
        print(f"🚫 خطأ في الإحصائيات: {str(e)}")

        # إرجاع إحصائيات فارغة في حالة الخطأ
        return jsonify({
            'success': True,
            'data': {
                'total_executed': 0,
                'total_amount': 0,
                'currencies': 'ريال',
                'total_suppliers': 0,
                'today_executions': 0
            }
        })
