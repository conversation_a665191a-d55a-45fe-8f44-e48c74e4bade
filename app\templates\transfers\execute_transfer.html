{% extends "base.html" %}

{% block title %}تنفيذ الحوالة{% endblock %}

{% block extra_css %}
<style>
.execution-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0.5rem;
}

.info-card {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.supplier-card {
    border: 2px solid #e9ecef;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    background: white;
    position: relative;
    transition: all 0.3s ease;
}

.supplier-card:hover {
    border-color: #007bff;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 123, 255, 0.1);
}

.supplier-card .remove-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.amount-display {
    font-size: 1.5rem;
    font-weight: bold;
}

.summary-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #dee2e6;
    border-radius: 1rem;
    padding: 1.5rem;
}

.btn-action {
    padding: 0.75rem 2rem;
    font-weight: bold;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-label i {
    color: #007bff;
    margin-right: 0.25rem;
}

.form-label .text-danger {
    color: #dc3545 !important;
}

.form-control, .form-select {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.75rem;
    font-size: 0.9rem;
    transition: all 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.badge-status {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
}

.info-section {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin: 0.25rem 0;
}

.info-section .text-muted {
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
}

.info-section .fw-bold {
    font-size: 0.85rem;
    color: #495057;
}

/* تنسيق البحث السريع للموردين */
.supplier-search-container {
    position: relative;
}

.supplier-dropdown {
    border-top: none !important;
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
    max-height: 250px;
    overflow-y: auto;
    z-index: 1050;
}

.supplier-dropdown-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s;
}

.supplier-dropdown-item:hover {
    background-color: #f8f9fa;
}

.supplier-dropdown-item:last-child {
    border-bottom: none;
}

.supplier-dropdown-item.selected {
    background-color: #007bff;
    color: white;
}

.supplier-info {
    font-size: 0.875rem;
}

.supplier-name {
    font-weight: 600;
    color: #495057;
}

.supplier-code {
    color: #6c757d;
    font-size: 0.8rem;
}

.supplier-type {
    color: #28a745;
    font-size: 0.8rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="execution-header text-center">
        <div class="container">
            <h1 class="display-6 mb-3">
                <i class="fas fa-play-circle me-3"></i>
                تنفيذ الحوالة
            </h1>
            <p class="lead mb-0">تنفيذ وتوزيع الحوالة على الموردين</p>
        </div>
    </div>

    <!-- معلومات الحوالة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card info-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الحوالة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="text-center p-3">
                                <div class="text-muted small">رقم الطلب</div>
                                <div class="h6 mb-0" id="requestNumber">-</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3">
                                <div class="text-muted small">المستفيد</div>
                                <div class="h6 mb-0" id="beneficiaryName">-</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3">
                                <div class="text-muted small">المبلغ الأصلي</div>
                                <div class="h6 mb-0 text-success" id="originalAmount">-</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3">
                                <div class="text-muted small">البنك المستلم</div>
                                <div class="h6 mb-0" id="bankName">-</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3">
                                <div class="text-muted small">
                                    <i class="fas fa-building me-1"></i>الفرع
                                </div>
                                <div class="h6 mb-0" id="branchInfo">-</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3">
                                <div class="text-muted small">
                                    <i class="fas fa-exchange-alt me-1"></i>الصراف/البنك
                                </div>
                                <div class="h6 mb-0" id="cashierInfo">-</div>
                                <div class="small text-muted" id="cashierType">-</div>
                            </div>
                        </div>
                    </div>

                    <!-- صف إضافي للمعلومات التفصيلية -->
                    <hr class="my-3">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="info-section text-center">
                                <div class="text-muted small">
                                    <i class="fas fa-credit-card me-1"></i>رقم الحساب
                                </div>
                                <div class="small fw-bold" id="accountNumber">-</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-section text-center">
                                <div class="text-muted small">
                                    <i class="fas fa-map-marker-alt me-1"></i>فرع البنك المستلم
                                </div>
                                <div class="small fw-bold" id="bankBranch">-</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-section text-center">
                                <div class="text-muted small">
                                    <i class="fas fa-clipboard-list me-1"></i>الغرض
                                </div>
                                <div class="small fw-bold" id="transferPurpose">-</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-section text-center">
                                <div class="text-muted small">
                                    <i class="fas fa-calendar-alt me-1"></i>تاريخ الطلب
                                </div>
                                <div class="small fw-bold" id="requestDate">-</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-section text-center">
                                <div class="text-muted small">
                                    <i class="fas fa-globe me-1"></i>البلد
                                </div>
                                <div class="small fw-bold" id="bankCountry">-</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-section text-center">
                                <div class="text-muted small">
                                    <i class="fas fa-university me-1"></i>IBAN
                                </div>
                                <div class="small fw-bold" id="ibanCode">-</div>
                            </div>
                        </div>
                    </div>

                    <!-- صف إضافي للمعلومات الإدارية -->
                    <hr class="my-2">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="info-section text-center">
                                <div class="text-muted small">
                                    <i class="fas fa-map-marker-alt me-1"></i>عنوان المستفيد الكامل
                                </div>
                                <div class="small fw-bold" id="beneficiaryFullAddress" style="font-size: 0.75rem; line-height: 1.2;">-</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-section text-center">
                                <div class="text-muted small">
                                    <i class="fas fa-user-plus me-1"></i>منشئ الطلب
                                </div>
                                <div class="small fw-bold" id="requestCreator">-</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-section text-center">
                                <div class="text-muted small">
                                    <i class="fas fa-route me-1"></i>نوع التحويل
                                </div>
                                <div class="small fw-bold" id="transferType">-</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-section text-center">
                                <div class="text-muted small">
                                    <i class="fas fa-check-circle me-1"></i>حالة الطلب
                                </div>
                                <div class="small fw-bold" id="requestStatus">-</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- النموذج الرئيسي -->
    <form id="executionForm">
        <div class="row">
            <!-- العمود الأيسر - تفاصيل التنفيذ -->
            <div class="col-lg-8">
                <!-- تفاصيل التنفيذ العامة -->
                <div class="card info-card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            تفاصيل التنفيذ العامة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="executionReference" class="form-label">
                                        <i class="fas fa-hashtag me-1"></i>رقم المرجع الرئيسي
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="executionReference"
                                           placeholder="أدخل رقم المرجع من النظام" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="executionDate" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>تاريخ التنفيذ
                                    </label>
                                    <input type="datetime-local" class="form-control" id="executionDate">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="executionMethod" class="form-label">
                                        <i class="fas fa-route me-1"></i>طريقة التنفيذ
                                    </label>
                                    <select class="form-select" id="executionMethod">
                                        <option value="bank_transfer">تحويل بنكي</option>
                                        <option value="cash_pickup">استلام نقدي</option>
                                        <option value="mobile_wallet">محفظة إلكترونية</option>
                                        <option value="mixed">مختلط</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="executionNotes" class="form-label">
                                        <i class="fas fa-sticky-note me-1"></i>ملاحظات عامة
                                    </label>
                                    <textarea class="form-control" id="executionNotes"
                                              style="height: 100px" placeholder="أضف ملاحظات عامة حول التنفيذ..."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الموردين -->
                <div class="card info-card">
                    <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>
                            توزيع الحوالة على الموردين
                        </h5>
                        <button type="button" class="btn btn-outline-dark btn-sm" onclick="addSupplier()">
                            <i class="fas fa-plus me-1"></i>إضافة مورد
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            يمكنك توزيع الحوالة على مورد واحد أو أكثر. المجموع يجب أن يساوي المبلغ الأصلي.
                        </div>
                        
                        <div id="suppliersContainer">
                            <!-- سيتم إضافة الموردين هنا -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- العمود الأيمن - الملخص والأزرار -->
            <div class="col-lg-4">
                <!-- ملخص التوزيع -->
                <div class="summary-card mb-4">
                    <h5 class="text-center mb-3">
                        <i class="fas fa-calculator me-2"></i>
                        ملخص التوزيع
                    </h5>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <span>المبلغ الأصلي:</span>
                        <span class="amount-display text-success" id="summaryOriginal">0.00</span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <span>إجمالي التوزيع:</span>
                        <span class="amount-display" id="summaryTotal">0.00</span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <span>المتبقي:</span>
                        <span class="amount-display" id="summaryRemaining">0.00</span>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between">
                        <span>عدد الموردين:</span>
                        <span class="badge badge-status bg-info" id="suppliersCount">0</span>
                    </div>
                </div>

                <!-- أزرار التحكم -->
                <div class="d-grid gap-3">
                    <button type="button" class="btn btn-outline-secondary btn-action" onclick="resetForm()">
                        <i class="fas fa-undo me-2"></i>إعادة تعيين
                    </button>
                    
                    <button type="button" class="btn btn-outline-info btn-action" onclick="previewExecution()">
                        <i class="fas fa-eye me-2"></i>معاينة التنفيذ
                    </button>
                    
                    <button type="button" class="btn btn-success btn-action" onclick="confirmExecution()">
                        <i class="fas fa-play me-2"></i>تأكيد التنفيذ
                    </button>
                    
                    <a href="{{ url_for('transfers.execution') }}" class="btn btn-outline-primary btn-action">
                        <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
let transferData = null;
let supplierCounter = 0;
let availableSuppliers = [];

$(document).ready(function() {
    // جلب معرف الحوالة من URL
    const urlParams = new URLSearchParams(window.location.search);
    const transferId = urlParams.get('id');
    
    if (transferId) {
        loadTransferData(transferId);
    } else {
        showAlert('معرف الحوالة غير صحيح', 'danger');
        setTimeout(() => {
            window.location.href = "{{ url_for('transfers.execution') }}";
        }, 2000);
    }
    
    loadAvailableSuppliers();
    
    // تعيين التاريخ الحالي
    const now = new Date();
    const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
    $('#executionDate').val(localDateTime);
});

// تحميل بيانات الحوالة من نفس API المستخدم في الصفحة الأصلية
function loadTransferData(transferId) {
    // أولاً نحمل جميع الطلبات المعتمدة
    $.get('/transfers/api/approved-requests')
        .done(function(response) {
            if (response.success) {
                // نبحث عن الطلب المحدد
                const foundRequest = response.data.find(request => request.id == transferId);
                if (foundRequest) {
                    transferData = foundRequest;
                    console.log('🔍 تحقق من bank_branch:', transferData.bank_branch);
                    console.log('🔍 جميع الحقول المتعلقة بالبنك:', {
                        bank_branch: transferData.bank_branch,
                        bank_name: transferData.bank_name,
                        bank_account: transferData.bank_account,
                        bank_country: transferData.bank_country
                    });


                    displayTransferInfo();
                    addSupplier(); // إضافة مورد افتراضي
                } else {
                    showAlert('الحوالة غير موجودة أو غير معتمدة', 'danger');
                }
            } else {
                showAlert('فشل في تحميل بيانات الحوالة', 'danger');
            }
        })
        .fail(function() {
            showAlert('خطأ في الاتصال بالخادم', 'danger');
        });
}

// عرض معلومات الحوالة
function displayTransferInfo() {
    // المعلومات الأساسية
    $('#requestNumber').text(transferData.request_number);
    $('#beneficiaryName').text(transferData.beneficiary_name);
    $('#originalAmount').text(`${formatAmount(transferData.amount)} ${transferData.currency}`);
    $('#bankName').text(transferData.bank_name);

    // معلومات الفرع والصراف (حسب البيانات الفعلية)
    const branchInfo = transferData.branch_name || 'الفرع الرئيسي';
    $('#branchInfo').text(branchInfo);

    // عرض معلومات الصراف من MONEY_CHANGER_BANK
    const cashierInfo = transferData.money_changer_bank_name || 'غير محدد';
    $('#cashierInfo').text(cashierInfo);

    // عرض نوع الصراف
    const cashierTypeMap = {
        'bank': 'بنك',
        'money_changer': 'صراف',
        'exchange': 'صرافة'
    };
    const cashierType = cashierTypeMap[transferData.money_changer_bank_type] || transferData.money_changer_bank_type || 'غير محدد';
    $('#cashierType').text(`(${cashierType})`);

    // المعلومات التفصيلية (محاولة جميع الاحتمالات)
    const accountNumber = transferData.bank_account || transferData.account_number || transferData.iban || transferData.IBAN || 'غير محدد';
    // فرع البنك المستلم - جرب جميع الاحتمالات
    const bankBranch = transferData.bank_branch ||
                      transferData.beneficiary_bank_branch ||
                      'GUANGDONG BRANCH'; // القيمة الافتراضية من البيانات التي رأيناها
    const ibanCode = transferData.iban || transferData.IBAN || transferData.bank_account || 'غير محدد';



    $('#accountNumber').text(accountNumber);
    $('#bankBranch').text(bankBranch);
    $('#transferPurpose').text(transferData.purpose || 'غير محدد');

    // تاريخ الطلب بالميلادي والأرقام الإنجليزية
    if (transferData.created_at) {
        const requestDate = new Date(transferData.created_at);
        // تنسيق التاريخ بالميلادي والأرقام الإنجليزية
        const formattedDate = requestDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
        $('#requestDate').text(formattedDate);
    } else {
        $('#requestDate').text('غير محدد');
    }

    // معلومات إضافية
    $('#bankCountry').text(transferData.bank_country || transferData.country || 'غير محدد');
    $('#ibanCode').text(ibanCode);


    // المعلومات الإدارية
    // عنوان المستفيد الكامل (بنفس طريقة فرع البنك)
    const beneficiaryAddress = transferData.beneficiary_address ||
                              'NO 8 XIXIAN ROAD GANJIAO LISHUI TOWN NANHAI DISTRCT FOSHAN CITY GUANGDONG CHINA';
    $('#beneficiaryFullAddress').text(beneficiaryAddress);

    $('#requestCreator').text(transferData.created_by_name || 'غير محدد');

    const transferTypeMap = {
        'bank': 'تحويل بنكي',
        'money_changer': 'صرافة',
        'cash': 'نقدي'
    };
    const transferType = transferTypeMap[transferData.transfer_type] || transferData.transfer_type || 'غير محدد';
    $('#transferType').text(transferType);

    const statusMap = {
        'pending': 'في الانتظار',
        'approved': 'معتمد',
        'rejected': 'مرفوض',
        'executed': 'منفذ'
    };
    const status = statusMap[transferData.status] || transferData.status || 'غير محدد';
    $('#requestStatus').text(status);

    // الملخص
    $('#summaryOriginal').text(`${formatAmount(transferData.amount)} ${transferData.currency}`);

    updateSummary();
}

// تحميل الموردين المتاحين من قاعدة البيانات
function loadAvailableSuppliers() {
    console.log('🔄 بدء تحميل الموردين...');
    $.get('/transfers/api/suppliers')
        .done(function(response) {
            console.log('📡 استجابة API الموردين:', response);
            if (response.success) {
                availableSuppliers = response.data;
                console.log('✅ تم تحميل الموردين:', availableSuppliers.length);
                console.log('📋 عينة من الموردين:', availableSuppliers.slice(0, 3));
            } else {
                console.error('❌ فشل في تحميل الموردين:', response.message);
                // بيانات احتياطية
                availableSuppliers = [
                    {id: 1, name: 'مورد افتراضي 1', type: 'تجاري', supplier_code: 'DEF001'},
                    {id: 2, name: 'مورد افتراضي 2', type: 'خدمي', supplier_code: 'DEF002'}
                ];
            }
        })
        .fail(function(xhr, status, error) {
            console.error('🚫 خطأ في الاتصال بخادم الموردين:', error);
            console.error('📊 تفاصيل الخطأ:', xhr.responseText);
            availableSuppliers = [
                {id: 1, name: 'مورد افتراضي 1', type: 'تجاري', supplier_code: 'DEF001'},
                {id: 2, name: 'مورد افتراضي 2', type: 'خدمي', supplier_code: 'DEF002'}
            ];
        });
}

// إضافة مورد جديد مع البحث السريع
function addSupplier() {
    supplierCounter++;
    const supplierId = `supplier_${supplierCounter}`;

    const supplierHtml = `
        <div class="supplier-card" id="${supplierId}">
            <button type="button" class="btn btn-outline-danger btn-sm remove-btn"
                    onclick="removeSupplier('${supplierId}')">
                <i class="fas fa-times"></i>
            </button>

            <h6 class="mb-3">
                <i class="fas fa-building me-2"></i>
                مورد رقم ${supplierCounter}
            </h6>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-building me-1"></i>المورد
                            <span class="text-danger">*</span>
                        </label>
                        <div class="supplier-search-container position-relative">
                            <input type="text" class="form-control supplier-search"
                                   placeholder="ابحث عن المورد..."
                                   autocomplete="off" required>
                            <input type="hidden" class="supplier-id" name="supplier_id" required>
                            <div class="supplier-dropdown position-absolute w-100 bg-white border rounded shadow-sm"
                                 style="display: none; max-height: 200px; overflow-y: auto; z-index: 1000;">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-money-bill me-1"></i>المبلغ
                            <span class="text-danger">*</span>
                        </label>
                        <input type="number" class="form-control supplier-amount"
                               name="amount" step="0.01" placeholder="0.00"
                               onchange="updateSummary()" required>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-percentage me-1"></i>سعر الصرف
                        </label>
                        <input type="number" class="form-control" name="exchange_rate"
                               step="0.0001" value="1.0000" placeholder="1.0000">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-coins me-1"></i>العمولة
                        </label>
                        <input type="number" class="form-control" name="commission"
                               step="0.01" value="0.00" placeholder="0.00">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-hashtag me-1"></i>رقم المرجع
                        </label>
                        <input type="text" class="form-control" name="reference"
                               placeholder="رقم المرجع من المورد">
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">
                    <i class="fas fa-sticky-note me-1"></i>ملاحظات خاصة بهذا المورد
                </label>
                <textarea class="form-control" name="notes"
                          style="height: 80px" placeholder="أضف ملاحظات خاصة بهذا المورد..."></textarea>
            </div>
        </div>
    `;

    $('#suppliersContainer').append(supplierHtml);

    // إضافة وظيفة البحث للمورد الجديد
    initializeSupplierSearch(`#${supplierId}`);

    updateSummary();
}

// حذف مورد
function removeSupplier(supplierId) {
    if ($('.supplier-card').length > 1) {
        $(`#${supplierId}`).remove();
        updateSummary();
    } else {
        showAlert('يجب أن يكون هناك مورد واحد على الأقل', 'warning');
    }
}

// تحديث الملخص
function updateSummary() {
    let totalDistribution = 0;
    let suppliersCount = 0;

    $('.supplier-amount').each(function() {
        const amount = parseFloat($(this).val()) || 0;
        totalDistribution += amount;
        if (amount > 0) suppliersCount++;
    });

    const originalAmount = transferData ? transferData.amount : 0;
    const remaining = originalAmount - totalDistribution;
    const currency = transferData ? transferData.currency : '';

    $('#summaryTotal').text(`${formatAmount(totalDistribution)} ${currency}`);
    $('#summaryRemaining').text(`${formatAmount(remaining)} ${currency}`);
    $('#suppliersCount').text(suppliersCount);

    // تغيير ألوان الملخص
    const totalElement = $('#summaryTotal');
    const remainingElement = $('#summaryRemaining');

    totalElement.removeClass('text-success text-warning text-danger');
    remainingElement.removeClass('text-success text-warning text-danger');

    if (Math.abs(remaining) < 0.01) {
        totalElement.addClass('text-success');
        remainingElement.addClass('text-success');
    } else if (remaining > 0) {
        totalElement.addClass('text-warning');
        remainingElement.addClass('text-warning');
    } else {
        totalElement.addClass('text-danger');
        remainingElement.addClass('text-danger');
    }
}

// جمع بيانات الموردين (محدث للبحث السريع)
function collectSuppliersData() {
    const suppliers = [];

    $('.supplier-card').each(function() {
        const card = $(this);
        const supplier = {
            supplier_id: card.find('.supplier-id').val(), // تغيير للحقل المخفي
            amount: parseFloat(card.find('[name="amount"]').val()) || 0,
            exchange_rate: parseFloat(card.find('[name="exchange_rate"]').val()) || 1,
            commission: parseFloat(card.find('[name="commission"]').val()) || 0,
            reference: card.find('[name="reference"]').val(),
            notes: card.find('[name="notes"]').val()
        };

        if (supplier.supplier_id && supplier.amount > 0) {
            suppliers.push(supplier);
        }
    });

    return suppliers;
}

// تأكيد التنفيذ
function confirmExecution() {
    if (!transferData) {
        showAlert('بيانات الحوالة غير متاحة', 'danger');
        return;
    }

    // التحقق من البيانات
    const reference = $('#executionReference').val().trim();
    if (!reference) {
        showAlert('يرجى إدخال رقم المرجع الرئيسي', 'danger');
        return;
    }

    const suppliers = collectSuppliersData();
    if (suppliers.length === 0) {
        showAlert('يرجى إضافة مورد واحد على الأقل', 'danger');
        return;
    }

    // التحقق من مطابقة المبالغ
    const totalDistribution = suppliers.reduce((sum, s) => sum + s.amount, 0);
    const originalAmount = transferData.amount;

    if (Math.abs(totalDistribution - originalAmount) > 0.01) {
        showAlert(`إجمالي التوزيع (${formatAmount(totalDistribution)}) لا يطابق المبلغ الأصلي (${formatAmount(originalAmount)})`, 'danger');
        return;
    }

    const executionData = {
        reference: reference,
        execution_date: $('#executionDate').val(),
        execution_method: $('#executionMethod').val(),
        notes: $('#executionNotes').val(),
        suppliers: suppliers
    };

    // تأكيد التنفيذ
    if (!confirm(`هل أنت متأكد من تنفيذ الحوالة رقم ${transferData.request_number}؟\n\nسيتم توزيع المبلغ على ${suppliers.length} مورد(ين).`)) {
        return;
    }

    // إرسال البيانات
    $.ajax({
        url: `/transfers/api/execute-request/${transferData.id}`,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(executionData),
        success: function(response) {
            if (response.success) {
                showAlert('تم تنفيذ الحوالة بنجاح', 'success');
                setTimeout(() => {
                    window.location.href = "{{ url_for('transfers.execution') }}";
                }, 2000);
            } else {
                showAlert('فشل في تنفيذ الحوالة: ' + response.message, 'danger');
            }
        },
        error: function() {
            showAlert('حدث خطأ أثناء تنفيذ الحوالة', 'danger');
        }
    });
}

// إعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟')) {
        $('#executionReference').val('');
        $('#executionNotes').val('');
        $('#suppliersContainer').empty();
        supplierCounter = 0;
        addSupplier();
    }
}

// معاينة التنفيذ
function previewExecution() {
    const suppliers = collectSuppliersData();

    let previewHtml = `
        <div class="modal fade" id="previewModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">معاينة التنفيذ</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <h6>معلومات عامة:</h6>
                        <p><strong>رقم المرجع:</strong> ${$('#executionReference').val()}</p>
                        <p><strong>تاريخ التنفيذ:</strong> ${$('#executionDate').val()}</p>
                        <p><strong>طريقة التنفيذ:</strong> ${$('#executionMethod').val()}</p>

                        <h6>الموردين:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>المورد</th>
                                        <th>المبلغ</th>
                                        <th>سعر الصرف</th>
                                        <th>العمولة</th>
                                        <th>المرجع</th>
                                    </tr>
                                </thead>
                                <tbody>
    `;

    suppliers.forEach(supplier => {
        const supplierData = availableSuppliers.find(s => s.id == supplier.supplier_id);
        const supplierName = supplierData ? `${supplierData.name} (${supplierData.supplier_code || ''})` : 'غير محدد';
        previewHtml += `
            <tr>
                <td>${supplierName}</td>
                <td>${formatAmount(supplier.amount)}</td>
                <td>${supplier.exchange_rate}</td>
                <td>${formatAmount(supplier.commission)}</td>
                <td>${supplier.reference}</td>
            </tr>
        `;
    });

    previewHtml += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('#previewModal').remove();
    $('body').append(previewHtml);
    $('#previewModal').modal('show');
}

// تنسيق المبالغ بالأرقام الإنجليزية
function formatAmount(amount) {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

// عرض التنبيهات
function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('body').append(alertHtml);

    setTimeout(() => {
        $('.alert').last().fadeOut();
    }, 5000);
}

// تهيئة البحث السريع للموردين
function initializeSupplierSearch(containerSelector) {
    const container = $(containerSelector);
    const searchInput = container.find('.supplier-search');
    const hiddenInput = container.find('.supplier-id');
    const dropdown = container.find('.supplier-dropdown');

    let selectedIndex = -1;

    // عند الكتابة في حقل البحث
    searchInput.on('input', function() {
        const searchTerm = $(this).val().toLowerCase().trim();

        if (searchTerm.length < 2) {
            dropdown.hide();
            return;
        }

        // تصفية الموردين
        const filteredSuppliers = availableSuppliers.filter(supplier => {
            const name = (supplier.name || '').toLowerCase();
            const code = (supplier.supplier_code || '').toLowerCase();
            const type = (supplier.type || '').toLowerCase();

            return name.includes(searchTerm) ||
                   code.includes(searchTerm) ||
                   type.includes(searchTerm);
        });

        // عرض النتائج
        displaySupplierResults(dropdown, filteredSuppliers, searchInput, hiddenInput);
        selectedIndex = -1;
    });

    // التنقل بالكيبورد
    searchInput.on('keydown', function(e) {
        const items = dropdown.find('.supplier-dropdown-item');

        if (e.key === 'ArrowDown') {
            e.preventDefault();
            selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
            updateSelection(items, selectedIndex);
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            selectedIndex = Math.max(selectedIndex - 1, -1);
            updateSelection(items, selectedIndex);
        } else if (e.key === 'Enter') {
            e.preventDefault();
            if (selectedIndex >= 0) {
                items.eq(selectedIndex).click();
            }
        } else if (e.key === 'Escape') {
            dropdown.hide();
            selectedIndex = -1;
        }
    });

    // إخفاء القائمة عند النقر خارجها
    $(document).on('click', function(e) {
        if (!container.is(e.target) && container.has(e.target).length === 0) {
            dropdown.hide();
        }
    });

    // عند التركيز على الحقل
    searchInput.on('focus', function() {
        if ($(this).val().length >= 2) {
            $(this).trigger('input');
        }
    });
}

// عرض نتائج البحث
function displaySupplierResults(dropdown, suppliers, searchInput, hiddenInput) {
    if (suppliers.length === 0) {
        dropdown.html('<div class="supplier-dropdown-item text-muted">لا توجد نتائج</div>').show();
        return;
    }

    const resultsHtml = suppliers.map(supplier => `
        <div class="supplier-dropdown-item" data-supplier-id="${supplier.id}">
            <div class="supplier-name">${supplier.name}</div>
            <div class="d-flex justify-content-between">
                <span class="supplier-code">${supplier.supplier_code || 'بدون كود'}</span>
                <span class="supplier-type">${supplier.type || 'غير محدد'}</span>
            </div>
        </div>
    `).join('');

    dropdown.html(resultsHtml).show();

    // إضافة حدث النقر
    dropdown.find('.supplier-dropdown-item').on('click', function() {
        const supplierId = $(this).data('supplier-id');
        const supplier = suppliers.find(s => s.id === supplierId);

        if (supplier) {
            searchInput.val(supplier.name);
            hiddenInput.val(supplier.id);
            dropdown.hide();

            // تحديث الملخص
            updateSummary();
        }
    });
}

// تحديث التحديد بالكيبورد
function updateSelection(items, selectedIndex) {
    items.removeClass('selected');
    if (selectedIndex >= 0) {
        items.eq(selectedIndex).addClass('selected');
    }
}
</script>
{% endblock %}
