-- =====================================================
-- إنشاء جدول الصرافين والبنوك
-- Create Money Changers and Banks Table
-- =====================================================

-- التحقق من وجود الجدول وإنشاؤه إذا لم يكن موجوداً
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*)
    INTO table_count
    FROM user_tables
    WHERE table_name = 'MONEY_CHANGERS_BANKS';
    
    IF table_count = 0 THEN
        -- إنشاء الجدول
        EXECUTE IMMEDIATE '
        CREATE TABLE money_changers_banks (
            id NUMBER PRIMARY KEY,
            name VARCHAR2(200) NOT NULL,
            type VARCHAR2(50) NOT NULL CHECK (type IN (''bank'', ''money_changer'')),
            contact_person VARCHAR2(100),
            phone VARCHAR2(20),
            email VARCHAR2(100),
            address VARCHAR2(500),
            commission_rate NUMBER(5,2) DEFAULT 0,
            is_active NUMBER(1) DEFAULT 1 CHECK (is_active IN (0,1)),
            notes VARCHAR2(1000),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by NUMBER DEFAULT 1,
            updated_by NUMBER
        )';
        
        DBMS_OUTPUT.PUT_LINE('✅ تم إنشاء جدول money_changers_banks');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️ جدول money_changers_banks موجود بالفعل');
    END IF;
END;
/

-- إنشاء sequence للجدول
DECLARE
    seq_count NUMBER;
BEGIN
    SELECT COUNT(*)
    INTO seq_count
    FROM user_sequences
    WHERE sequence_name = 'MONEY_CHANGERS_BANKS_SEQ';
    
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE MONEY_CHANGERS_BANKS_SEQ START WITH 1 INCREMENT BY 1 NOCACHE';
        DBMS_OUTPUT.PUT_LINE('✅ تم إنشاء sequence للصرافين/البنوك');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️ sequence الصرافين/البنوك موجود بالفعل');
    END IF;
END;
/

-- إنشاء trigger للـ auto increment
DECLARE
    trigger_count NUMBER;
BEGIN
    SELECT COUNT(*)
    INTO trigger_count
    FROM user_triggers
    WHERE trigger_name = 'MONEY_CHANGERS_BANKS_TRG';
    
    IF trigger_count = 0 THEN
        EXECUTE IMMEDIATE '
        CREATE OR REPLACE TRIGGER MONEY_CHANGERS_BANKS_TRG
            BEFORE INSERT ON money_changers_banks
            FOR EACH ROW
        BEGIN
            IF :NEW.id IS NULL THEN
                :NEW.id := MONEY_CHANGERS_BANKS_SEQ.NEXTVAL;
            END IF;
            :NEW.updated_at := CURRENT_TIMESTAMP;
        END;';
        
        DBMS_OUTPUT.PUT_LINE('✅ تم إنشاء trigger للصرافين/البنوك');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️ trigger الصرافين/البنوك موجود بالفعل');
    END IF;
END;
/

-- إنشاء trigger للتحديث
DECLARE
    trigger_count NUMBER;
BEGIN
    SELECT COUNT(*)
    INTO trigger_count
    FROM user_triggers
    WHERE trigger_name = 'MONEY_CHANGERS_BANKS_UPD_TRG';
    
    IF trigger_count = 0 THEN
        EXECUTE IMMEDIATE '
        CREATE OR REPLACE TRIGGER MONEY_CHANGERS_BANKS_UPD_TRG
            BEFORE UPDATE ON money_changers_banks
            FOR EACH ROW
        BEGIN
            :NEW.updated_at := CURRENT_TIMESTAMP;
        END;';
        
        DBMS_OUTPUT.PUT_LINE('✅ تم إنشاء trigger التحديث للصرافين/البنوك');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️ trigger التحديث للصرافين/البنوك موجود بالفعل');
    END IF;
END;
/

-- إضافة فهارس للبحث السريع
DECLARE
    index_count NUMBER;
BEGIN
    -- فهرس على الاسم
    SELECT COUNT(*)
    INTO index_count
    FROM user_indexes
    WHERE index_name = 'IDX_MCB_NAME';
    
    IF index_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_MCB_NAME ON money_changers_banks(name)';
        DBMS_OUTPUT.PUT_LINE('✅ تم إنشاء فهرس الاسم');
    END IF;
    
    -- فهرس على النوع والحالة
    SELECT COUNT(*)
    INTO index_count
    FROM user_indexes
    WHERE index_name = 'IDX_MCB_TYPE_ACTIVE';
    
    IF index_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_MCB_TYPE_ACTIVE ON money_changers_banks(type, is_active)';
        DBMS_OUTPUT.PUT_LINE('✅ تم إنشاء فهرس النوع والحالة');
    END IF;
END;
/

-- إضافة بيانات تجريبية
DECLARE
    data_count NUMBER;
BEGIN
    SELECT COUNT(*)
    INTO data_count
    FROM money_changers_banks;
    
    IF data_count = 0 THEN
        -- إضافة بنوك
        INSERT INTO money_changers_banks (name, type, contact_person, phone, email, commission_rate, is_active, notes) VALUES
        ('البنك الأهلي السعودي - فرع الرياض', 'bank', 'أحمد محمد الأحمد', '************', '<EMAIL>', 1.5, 1, 'البنك الأهلي السعودي الفرع الرئيسي');
        
        INSERT INTO money_changers_banks (name, type, contact_person, phone, email, commission_rate, is_active, notes) VALUES
        ('بنك الرياض - فرع الدمام', 'bank', 'سارة أحمد العلي', '************', '<EMAIL>', 1.8, 1, 'بنك الرياض فرع الدمام');
        
        INSERT INTO money_changers_banks (name, type, contact_person, phone, email, commission_rate, is_active, notes) VALUES
        ('بنك ساب - فرع جدة', 'bank', 'محمد عبدالله', '************', '<EMAIL>', 1.6, 1, 'البنك السعودي البريطاني');
        
        -- إضافة صرافات
        INSERT INTO money_changers_banks (name, type, contact_person, phone, email, commission_rate, is_active, notes) VALUES
        ('صرافة الراجحي - فرع جدة', 'money_changer', 'محمد علي الراجحي', '************', '<EMAIL>', 2.0, 1, 'صرافة الراجحي للحوالات السريعة');
        
        INSERT INTO money_changers_banks (name, type, contact_person, phone, email, commission_rate, is_active, notes) VALUES
        ('صرافة الأهلي للصرافة', 'money_changer', 'فاطمة محمد', '************', '<EMAIL>', 2.2, 1, 'صرافة الأهلي للحوالات الدولية');
        
        INSERT INTO money_changers_banks (name, type, contact_person, phone, email, commission_rate, is_active, notes) VALUES
        ('صرافة الخليج الذهبية', 'money_changer', 'عبدالرحمن خالد', '<EMAIL>', '************', 1.9, 1, 'صرافة الخليج الذهبية للحوالات');
        
        INSERT INTO money_changers_banks (name, type, contact_person, phone, email, commission_rate, is_active, notes) VALUES
        ('بنك الإنماء - فرع الرياض', 'bank', 'نورا سعد', '************', '<EMAIL>', 1.7, 1, 'بنك الإنماء الفرع الرئيسي');
        
        INSERT INTO money_changers_banks (name, type, contact_person, phone, email, commission_rate, is_active, notes) VALUES
        ('صرافة الوطن السريعة', 'money_changer', 'خالد أحمد', '************', '<EMAIL>', 2.1, 1, 'صرافة الوطن للحوالات السريعة');
        
        COMMIT;
        DBMS_OUTPUT.PUT_LINE('✅ تم إضافة البيانات التجريبية للصرافين/البنوك');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️ البيانات التجريبية موجودة بالفعل');
    END IF;
END;
/

-- عرض ملخص البيانات المضافة
SELECT 
    'تم إعداد جدول الصرافين/البنوك بنجاح!' as status,
    COUNT(*) as total_records,
    SUM(CASE WHEN type = 'bank' THEN 1 ELSE 0 END) as banks_count,
    SUM(CASE WHEN type = 'money_changer' THEN 1 ELSE 0 END) as money_changers_count,
    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_count
FROM money_changers_banks;
