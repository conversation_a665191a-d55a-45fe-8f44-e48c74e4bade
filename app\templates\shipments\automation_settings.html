<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الأتمتة التلقائية - النظام المحاسبي المتقدم</title>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">

<style>
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.settings-container {
    min-height: 100vh;
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.settings-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    transition: all 0.4s ease;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.settings-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 25px 30px;
    border-bottom: none;
}

.card-header h4 {
    font-size: 1.4rem;
    font-weight: 700;
    margin: 0;
}

.card-body {
    padding: 0;
}

.setting-item {
    padding: 25px 30px;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: translateX(5px);
}

.setting-info {
    flex: 1;
}

.setting-title {
    font-weight: 700;
    color: #1e293b;
    font-size: 1.1rem;
    margin-bottom: 8px;
}

.setting-description {
    color: #64748b;
    font-size: 0.95rem;
    margin-bottom: 8px;
    line-height: 1.5;
}

.setting-meta {
    font-size: 0.8rem;
    color: #94a3b8;
    font-weight: 500;
}

.setting-control {
    margin-right: 20px;
}

/* مفتاح التبديل المخصص */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #e2e8f0;
    transition: all 0.4s ease;
    border-radius: 30px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    right: 4px;
    bottom: 4px;
    background: white;
    transition: all 0.4s ease;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
    background: linear-gradient(135deg, #667eea, #764ba2);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

input:checked + .slider:before {
    transform: translateX(-30px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.switch-label {
    margin-right: 15px;
    font-weight: 600;
    color: #475569;
    transition: color 0.3s ease;
}

.switch-label.active {
    color: #667eea;
}

.page-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-content h1 {
    font-size: 2.2rem;
    font-weight: 800;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 15px;
}

.btn {
    display: inline-flex;
    align-items: center;
    padding: 12px 24px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-outline-light {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    backdrop-filter: blur(10px);
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-info {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #64748b, #475569);
    color: white;
}

.advanced-settings {
    padding: 30px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    flex: 1;
}

.form-label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.form-control, .form-select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: white;
    color: #374151;
}

.form-control:focus, .form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-text {
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 5px;
}

.system-actions {
    padding: 30px;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.action-item {
    text-align: center;
}

.action-item .btn {
    width: 100%;
    margin-bottom: 10px;
    justify-content: center;
}

.action-description {
    font-size: 0.85rem;
    color: #6b7280;
    line-height: 1.4;
}

.empty-state {
    text-align: center;
    padding: 60px 30px;
    color: #6b7280;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #d1d5db;
}

.empty-state h5 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #374151;
}

/* تأثيرات الحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.settings-card {
    animation: fadeInUp 0.6s ease-out;
}

.settings-card:nth-child(1) { animation-delay: 0.1s; }
.settings-card:nth-child(2) { animation-delay: 0.2s; }
.settings-card:nth-child(3) { animation-delay: 0.3s; }

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .settings-container {
        padding: 15px;
    }

    .page-header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .header-actions {
        flex-direction: column;
        width: 100%;
    }

    .setting-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .form-row {
        flex-direction: column;
    }

    .actions-grid {
        grid-template-columns: 1fr;
    }
}
</style>

</head>

<body>
<div class="settings-container">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="header-content">
            <h1>
                <i class="fas fa-cog" style="margin-left: 15px;"></i>
                إعدادات الأتمتة التلقائية
            </h1>
            <p>تخصيص وإدارة إعدادات نظام الأتمتة الذكي</p>
        </div>
        <div class="header-actions">
            <a href="{{ url_for('shipments.automation_dashboard') }}" class="btn btn-outline-light">
                <i class="fas fa-arrow-right" style="margin-left: 8px;"></i>
                العودة للوحة الأتمتة
            </a>
        </div>
    </div>

    <!-- إعدادات الأتمتة -->
    <div class="settings-card">
        <div class="card-header">
            <h4>
                <i class="fas fa-sliders-h" style="margin-left: 10px;"></i>
                إعدادات الأتمتة الأساسية
            </h4>
        </div>
        <div class="card-body">
            {% if settings_data %}
                {% for setting in settings_data %}
                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-title">{{ setting.description }}</div>
                        <div class="setting-description">
                            {% if setting.key == 'auto_create_orders' %}
                                إنشاء أوامر التسليم تلقائياً عند وصول الشحنات للميناء
                            {% elif setting.key == 'auto_assign_agents' %}
                                تعيين أفضل مخلص متاح تلقائياً بناءً على التخصص والتقييم
                            {% elif setting.key == 'auto_send_notifications' %}
                                إرسال إشعارات فورية للعملاء والمخلصين عند تحديث الحالات
                            {% elif setting.key == 'auto_update_ratings' %}
                                تحديث تقييمات المخلصين تلقائياً بناءً على الأداء والسرعة
                            {% else %}
                                {{ setting.description }}
                            {% endif %}
                        </div>
                        <div class="setting-meta">
                            آخر تحديث: {{ setting.updated_at }}
                        </div>
                    </div>
                    <div class="setting-control">
                        <span class="switch-label {{ setting.value and 'active' or '' }}" id="label-{{ setting.key }}">
                            {{ setting.value and 'مفعل' or 'معطل' }}
                        </span>
                        <label class="switch">
                            <input type="checkbox"
                                   class="automation-setting"
                                   id="{{ setting.key }}"
                                   data-setting="{{ setting.key }}"
                                   {{ setting.value and 'checked' or '' }}>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h5>لا توجد إعدادات متاحة</h5>
                    <p>تأكد من إنشاء جداول الأتمتة في قاعدة البيانات</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- إعدادات متقدمة -->
    <div class="settings-card">
        <div class="card-header" style="background: linear-gradient(135deg, #64748b, #475569);">
            <h4>
                <i class="fas fa-tools" style="margin-left: 10px;"></i>
                إعدادات متقدمة
            </h4>
        </div>
        <div class="advanced-settings">
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">العمليات المتزامنة القصوى</label>
                    <input type="number" class="form-control" id="max_concurrent_operations" value="5" min="1" max="20">
                    <div class="form-text">الحد الأقصى للعمليات التي تعمل في نفس الوقت</div>
                </div>
                <div class="form-group">
                    <label class="form-label">حجم المعالجة المجمعة</label>
                    <input type="number" class="form-control" id="batch_processing_size" value="10" min="5" max="100">
                    <div class="form-text">عدد العناصر التي تتم معالجتها في دفعة واحدة</div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">مدة التخزين المؤقت (بالدقائق)</label>
                    <input type="number" class="form-control" id="cache_duration" value="60" min="5" max="1440">
                    <div class="form-text">مدة الاحتفاظ بالبيانات في الذاكرة المؤقتة</div>
                </div>
                <div class="form-group">
                    <label class="form-label">الحد الأقصى للمحاولات</label>
                    <input type="number" class="form-control" id="max_retry_attempts" value="3" min="1" max="10">
                    <div class="form-text">عدد المحاولات عند فشل إجراء الأتمتة</div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">مهلة الأمان (بالثواني)</label>
                    <input type="number" class="form-control" id="security_timeout" value="30" min="5" max="300">
                    <div class="form-text">الحد الأقصى لانتظار استجابة العمليات الحساسة</div>
                </div>
                <div class="form-group">
                    <label class="form-label">موافقة للقيم العالية</label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="require_approval_for_high_value">
                        <label class="form-check-label" for="require_approval_for_high_value">
                            يتطلب موافقة للشحنات عالية القيمة
                        </label>
                    </div>
                    <div class="form-text">تفعيل الموافقة اليدوية للشحنات التي تزيد قيمتها عن حد معين</div>
                </div>
            </div>

            <div style="text-align: left; margin-top: 20px;">
                <button type="button" class="btn btn-primary" id="saveAdvancedSettings" onclick="saveAdvancedSettings()">
                    <i class="fas fa-save" style="margin-left: 8px;"></i>
                    حفظ الإعدادات المتقدمة
                </button>
            </div>
        </div>
    </div>

    <!-- إجراءات النظام -->
    <div class="settings-card">
        <div class="card-header" style="background: linear-gradient(135deg, #f59e0b, #d97706); color: #1f2937;">
            <h4>
                <i class="fas fa-exclamation-triangle" style="margin-left: 10px;"></i>
                إجراءات النظام
            </h4>
        </div>
        <div class="system-actions">
            <div class="actions-grid">
                <div class="action-item">
                    <button type="button" class="btn btn-info">
                        <i class="fas fa-sync" style="margin-left: 8px;"></i>
                        تحديث تقييمات المخلصين
                    </button>
                    <div class="action-description">تحديث فوري لجميع تقييمات المخلصين</div>
                </div>
                <div class="action-item">
                    <button type="button" class="btn btn-warning">
                        <i class="fas fa-broom" style="margin-left: 8px;"></i>
                        تنظيف سجل الأتمتة
                    </button>
                    <div class="action-description">حذف السجلات القديمة (أكثر من 30 يوم)</div>
                </div>
                <div class="action-item">
                    <button type="button" class="btn btn-danger">
                        <i class="fas fa-power-off" style="margin-left: 8px;"></i>
                        إيقاف الأتمتة مؤقتاً
                    </button>
                    <div class="action-description">إيقاف جميع عمليات الأتمتة لصيانة النظام</div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديث إعدادات الأتمتة
document.querySelectorAll('.automation-setting').forEach(input => {
    input.addEventListener('change', function() {
        const settingKey = this.dataset.setting;
        const value = this.checked;
        const label = document.getElementById('label-' + settingKey);

        // تأثير تحميل
        this.disabled = true;
        label.style.opacity = '0.6';

        // إرسال طلب تحديث الإعداد
        fetch('/shipments/automation-api/settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                setting_key: settingKey,
                value: value
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث النص والحالة
                label.textContent = value ? 'مفعل' : 'معطل';
                label.className = 'switch-label ' + (value ? 'active' : '');

                // إظهار رسالة نجاح
                showNotification('تم تحديث الإعداد بنجاح', 'success');
            } else {
                // إظهار رسالة خطأ وإعادة الإعداد
                showNotification(data.message || 'فشل في تحديث الإعداد', 'error');
                this.checked = !value;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('حدث خطأ في الاتصال', 'error');
            this.checked = !value;
        })
        .finally(() => {
            this.disabled = false;
            label.style.opacity = '1';
        });
    });
});

// دالة إظهار الإشعارات
function showNotification(message, type) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        background: ${type === 'success' ? '#10b981' : '#ef4444'};
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        min-width: 300px;
        transform: translateX(400px);
        transition: all 0.4s ease;
        font-weight: 500;
    `;

    notification.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" style="
                background: none;
                border: none;
                color: white;
                font-size: 1.2rem;
                cursor: pointer;
                margin-right: 10px;
            ">×</button>
        </div>
    `;

    document.body.appendChild(notification);

    // تأثير الظهور
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // إزالة تلقائية
    setTimeout(() => {
        notification.style.transform = 'translateX(400px)';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 400);
    }, 3000);
}

// دالة حفظ الإعدادات المتقدمة
function saveAdvancedSettings() {
    const saveBtn = document.getElementById('saveAdvancedSettings');
    const originalContent = saveBtn.innerHTML;

    // تأثير التحميل
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-left: 8px;"></i>جاري الحفظ...';
    saveBtn.disabled = true;

    // جمع جميع الإعدادات المتقدمة باستخدام IDs
    const advancedSettings = {
        max_concurrent_operations: parseInt(document.getElementById('max_concurrent_operations').value) || 5,
        batch_processing_size: parseInt(document.getElementById('batch_processing_size').value) || 10,
        cache_duration: parseInt(document.getElementById('cache_duration').value) || 60,
        max_retry_attempts: parseInt(document.getElementById('max_retry_attempts').value) || 3,
        security_timeout: parseInt(document.getElementById('security_timeout').value) || 30,
        require_approval_for_high_value: document.getElementById('require_approval_for_high_value').checked
    };

    console.log('📋 الإعدادات المجمعة:', advancedSettings);

    // إرسال البيانات للخادم
    fetch('/shipments/automation-api/advanced-settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(advancedSettings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم حفظ الإعدادات المتقدمة بنجاح', 'success');
        } else {
            showNotification('فشل في حفظ الإعدادات: ' + (data.message || 'خطأ غير معروف'), 'error');
        }
    })
    .catch(error => {
        console.error('خطأ في حفظ الإعدادات:', error);
        showNotification('حدث خطأ في الاتصال بالخادم', 'error');
    })
    .finally(() => {
        // إعادة الزر لحالته الطبيعية
        saveBtn.innerHTML = originalContent;
        saveBtn.disabled = false;
    });
}

// تأثيرات إضافية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تأثير تحميل الكروت
    const cards = document.querySelectorAll('.settings-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });
});
</script>

</body>
</html>


