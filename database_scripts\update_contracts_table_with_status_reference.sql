-- تحديث جدول العقود لربطه بجدول الحالات
-- تاريخ التحديث: 2025-01-02
-- الوصف: إضافة مرجع لجدول حالات العقود وتحديث البيانات الموجودة

-- الخطوة 1: إضافة عمود STATUS_ID كمرجع لجدول الحالات
ALTER TABLE CONTRACTS ADD (
    STATUS_ID NUMBER
);

-- الخطوة 2: إضافة مفتاح خارجي
ALTER TABLE CONTRACTS ADD CONSTRAINT FK_CONTRACTS_STATUS_ID 
    FOREIGN KEY (STATUS_ID) REFERENCES CONTRACT_STATUSES(STATUS_ID);

-- الخطوة 3: إنشاء فهرس للأداء
CREATE INDEX IDX_CONTRACTS_STATUS_ID ON CONTRACTS(STATUS_ID);

-- الخطوة 4: تحديث البيانات الموجودة بناءً على CONTRACT_STATUS الحالي
UPDATE CONTRACTS SET STATUS_ID = (
    SELECT STATUS_ID 
    FROM CONTRACT_STATUSES 
    WHERE STATUS_CODE = CONTRACTS.CONTRACT_STATUS
) WHERE CONTRACT_STATUS IS NOT NULL;

-- الخطوة 5: تحديث العقود التي لا تحتوي على حالة محددة
UPDATE CONTRACTS SET STATUS_ID = (
    SELECT STATUS_ID 
    FROM CONTRACT_STATUSES 
    WHERE STATUS_CODE = 'DRAFT'
) WHERE STATUS_ID IS NULL;

-- الخطوة 6: جعل العمود إجباري بعد تحديث البيانات
ALTER TABLE CONTRACTS MODIFY STATUS_ID NOT NULL;

-- الخطوة 7: إضافة تعليق
COMMENT ON COLUMN CONTRACTS.STATUS_ID IS 'معرف حالة العقد (مرجع لجدول CONTRACT_STATUSES)';

-- الخطوة 8: إنشاء view محسن للعقود مع الحالات
CREATE OR REPLACE VIEW V_CONTRACTS_WITH_STATUS AS
SELECT 
    c.CONTRACT_ID,
    c.CONTRACT_NUMBER,
    c.CONTRACT_DATE,
    c.SUPPLIER_NAME,
    c.START_DATE,
    c.END_DATE,
    c.CONTRACT_AMOUNT,
    c.CURRENCY,
    c.IS_USED,
    c.IS_ACTIVE,
    c.CREATED_AT,
    -- معلومات الحالة
    cs.STATUS_ID,
    cs.STATUS_CODE,
    cs.STATUS_NAME_AR,
    cs.STATUS_NAME_EN,
    cs.STATUS_DESCRIPTION,
    cs.STATUS_COLOR,
    cs.STATUS_ICON,
    -- الحالة القديمة للمقارنة
    c.CONTRACT_STATUS as OLD_STATUS
FROM CONTRACTS c
LEFT JOIN CONTRACT_STATUSES cs ON c.STATUS_ID = cs.STATUS_ID
ORDER BY c.CONTRACT_ID;

-- الخطوة 9: إنشاء دالة لجلب لون الحالة
CREATE OR REPLACE FUNCTION GET_CONTRACT_STATUS_COLOR(p_contract_id NUMBER)
RETURN VARCHAR2
IS
    v_color VARCHAR2(20);
BEGIN
    SELECT cs.STATUS_COLOR
    INTO v_color
    FROM CONTRACTS c
    JOIN CONTRACT_STATUSES cs ON c.STATUS_ID = cs.STATUS_ID
    WHERE c.CONTRACT_ID = p_contract_id;
    
    RETURN v_color;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN '#6c757d'; -- اللون الافتراضي
END;
/

-- الخطوة 10: إنشاء دالة لجلب أيقونة الحالة
CREATE OR REPLACE FUNCTION GET_CONTRACT_STATUS_ICON(p_contract_id NUMBER)
RETURN VARCHAR2
IS
    v_icon VARCHAR2(50);
BEGIN
    SELECT cs.STATUS_ICON
    INTO v_icon
    FROM CONTRACTS c
    JOIN CONTRACT_STATUSES cs ON c.STATUS_ID = cs.STATUS_ID
    WHERE c.CONTRACT_ID = p_contract_id;
    
    RETURN v_icon;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN 'fas fa-circle'; -- الأيقونة الافتراضية
END;
/

-- الخطوة 11: عرض النتائج للتحقق
SELECT 
    CONTRACT_ID,
    CONTRACT_NUMBER,
    OLD_STATUS,
    STATUS_CODE,
    STATUS_NAME_AR,
    STATUS_COLOR,
    STATUS_ICON
FROM V_CONTRACTS_WITH_STATUS
ORDER BY CONTRACT_ID;

-- الخطوة 12: إحصائيات الحالات
SELECT 
    cs.STATUS_NAME_AR,
    COUNT(*) as CONTRACT_COUNT
FROM CONTRACTS c
JOIN CONTRACT_STATUSES cs ON c.STATUS_ID = cs.STATUS_ID
GROUP BY cs.STATUS_NAME_AR, cs.SORT_ORDER
ORDER BY cs.SORT_ORDER;

COMMIT;

-- الخطوة 13: إنشاء مشغل لتحديث الحالة تلقائياً عند التغيير
CREATE OR REPLACE TRIGGER TRG_CONTRACTS_STATUS_UPDATE
    BEFORE UPDATE OF STATUS_ID ON CONTRACTS
    FOR EACH ROW
BEGIN
    -- تسجيل التغيير في جدول التاريخ (إذا كان موجوداً)
    NULL; -- يمكن إضافة منطق إضافي هنا
END;
/

-- منح الصلاحيات
GRANT SELECT ON V_CONTRACTS_WITH_STATUS TO PUBLIC;
