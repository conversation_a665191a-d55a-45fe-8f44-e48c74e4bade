{% extends "base.html" %}

{% block extra_css %}
<!-- Handsontable CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/handsontable@12.4.0/dist/handsontable.full.min.css">

<style>
.contract-form {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.form-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.form-body {
    padding: 30px;
}

.section-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    margin: 20px -30px;
    font-weight: 600;
    font-size: 18px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.required {
    color: #dc3545;
}

.table-container {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.handsontable {
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.handsontable .htCore {
    direction: rtl;
}

.form-actions {
    background: #f8f9fa;
    padding: 30px;
    text-align: center;
    border-top: 1px solid #dee2e6;
}

.btn-action {
    padding: 12px 30px;
    border-radius: 8px;
    font-weight: 600;
    margin: 0 10px;
    min-width: 150px;
    transition: all 0.3s ease;
}

.btn-save {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
}

.btn-save:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    color: white;
}

.btn-cancel:hover {
    background: #5a6268;
    color: white;
}
</style>
{% endblock %}

{% block content %}
<div class="contract-form">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-11">
                <div class="form-container">
                    <!-- رأس النموذج -->
                    <div class="form-header">
                        <h1><i class="fas fa-file-contract me-3"></i>إضافة عقد جديد</h1>
                        <p>تسجيل بيانات العقود مع الموردين وربطها بأوامر الشراء</p>
                    </div>

                    <!-- نموذج البيانات -->
                    <form id="contractForm" method="POST" action="{{ url_for('contracts.create_contract_with_table') }}">
                        <div class="form-body">
                            <!-- القسم الأول: البيانات الرئيسية -->
                            <div class="section-title">
                                <i class="fas fa-info-circle"></i>
                                البيانات الرئيسية
                            </div>

                            <div class="row">
                                <!-- رقم الفرع -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">رقم الفرع <span class="required">*</span></label>
                                        <select class="form-select" id="branch_id" name="branch_id" required>
                                            <option value="">اختر الفرع</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- رقم العقد -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">رقم العقد</label>
                                        <input type="text" class="form-control" id="contract_number" name="contract_number" 
                                               placeholder="سيتم توليده تلقائياً">
                                    </div>
                                </div>

                                <!-- التاريخ -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">التاريخ <span class="required">*</span></label>
                                        <input type="date" class="form-control" id="contract_date" name="contract_date" 
                                               value="{{ today }}" required>
                                    </div>
                                </div>

                                <!-- تاريخ البداية -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">تاريخ البداية</label>
                                        <input type="date" class="form-control" id="start_date" name="start_date">
                                    </div>
                                </div>

                                <!-- تاريخ النهاية -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">تاريخ النهاية</label>
                                        <input type="date" class="form-control" id="end_date" name="end_date">
                                    </div>
                                </div>

                                <!-- المورد -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">المورد <span class="required">*</span></label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="supplier_name" name="supplier_name" 
                                                   placeholder="اسم المورد" readonly required>
                                            <button type="button" class="btn btn-outline-primary" onclick="openSupplierSearch()">
                                                <i class="fas fa-search"></i>
                                            </button>
                                        </div>
                                        <input type="hidden" id="supplier_id" name="supplier_id">
                                    </div>
                                </div>

                                <!-- العملة -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">العملة</label>
                                        <select class="form-select" id="currency_code" name="currency_code">
                                            <option value="SAR">ريال سعودي (SAR)</option>
                                            <option value="USD">دولار أمريكي (USD)</option>
                                            <option value="EUR">يورو (EUR)</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- سعر الصرف -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">سعر الصرف</label>
                                        <input type="number" class="form-control" id="exchange_rate" name="exchange_rate" 
                                               value="1" step="0.0001" min="0">
                                    </div>
                                </div>

                                <!-- الرقم المرجعي -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">الرقم المرجعي</label>
                                        <input type="text" class="form-control" id="reference_number" name="reference_number" 
                                               placeholder="رقم مرجعي اختياري">
                                    </div>
                                </div>

                                <!-- مبلغ العقد -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">مبلغ العقد</label>
                                        <input type="number" class="form-control" id="contract_amount" name="contract_amount" 
                                               step="0.001" min="0" placeholder="0.000">
                                    </div>
                                </div>

                                <!-- مبلغ الخصم -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">مبلغ الخصم</label>
                                        <input type="number" class="form-control" id="discount_amount" name="discount_amount" 
                                               step="0.001" min="0" value="0" onchange="calculateNetAmount()">
                                    </div>
                                </div>

                                <!-- صافي المبلغ -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">صافي المبلغ</label>
                                        <input type="number" class="form-control" id="net_amount" name="net_amount" 
                                               step="0.001" min="0" readonly>
                                    </div>
                                </div>

                                <!-- الوصف -->
                                <div class="col-12">
                                    <div class="form-group">
                                        <label class="form-label">الوصف</label>
                                        <textarea class="form-control" id="description" name="description" rows="3" 
                                                  placeholder="وصف العقد وتفاصيل إضافية"></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- القسم الثاني: البيانات التفصيلية -->
                            <div class="section-title mt-4">
                                <i class="fas fa-table"></i>
                                البيانات التفصيلية - أصناف العقد
                            </div>

                            <!-- حاوي الجدول -->
                            <div class="table-container">
                                <!-- الجدول التفاعلي Handsontable -->
                                <div id="contractDetailsTable"></div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="form-actions">
                            <button type="submit" class="btn btn-action btn-save">
                                <i class="fas fa-save me-2"></i>حفظ العقد
                            </button>
                            <button type="button" class="btn btn-action btn-cancel" onclick="window.history.back()">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Handsontable JS -->
<script src="https://cdn.jsdelivr.net/npm/handsontable@12.4.0/dist/handsontable.full.min.js"></script>

<script>
// متغيرات عامة
let contractTable;
let selectedBranch = null;

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء تحميل صفحة العقد الجديد...');
    
    // تحميل الفروع
    loadBranches();
    
    // تهيئة الجدول
    initializeContractTable();

    console.log('✅ تم تحميل الصفحة بنجاح');
});

// تحميل الفروع
function loadBranches() {
    $.ajax({
        url: '/contracts/api/branches',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const branchSelect = $('#branch_id');
                branchSelect.empty().append('<option value="">اختر الفرع</option>');

                response.branches.forEach(branch => {
                    branchSelect.append(`
                        <option value="${branch.branch_id}">${branch.branch_name}</option>
                    `);
                });
            }
        },
        error: function() {
            console.error('خطأ في تحميل الفروع');
        }
    });
}

// تهيئة جدول Handsontable
function initializeContractTable() {
    console.log('🔧 بدء تهيئة جدول Handsontable...');

    const container = document.getElementById('contractDetailsTable');
    if (!container) {
        console.error('❌ لم يتم العثور على عنصر الجدول');
        return;
    }

    // التحقق من تحميل Handsontable
    if (typeof Handsontable === 'undefined') {
        console.log('⏳ انتظار تحميل Handsontable...');
        setTimeout(initializeContractTable, 500);
        return;
    }

    try {
        // إنشاء بيانات أولية فارغة (20 صف × 12 عمود)
        const initialData = [];
        for (let i = 0; i < 20; i++) {
            initialData.push(['', '', '', '', '', '', '', '', '', '', '', '']);
        }

        // إنشاء جدول Handsontable
        contractTable = new Handsontable(container, {
            data: initialData,
            colHeaders: [
                'كود الصنف',
                'اسم الصنف',
                'الوحدة',
                'الكمية',
                'الكمية المجانية',
                'سعر الوحدة',
                'نسبة الخصم %',
                'مبلغ الضريبة',
                'الإجمالي',
                'تاريخ الإنتاج',
                'تاريخ الانتهاء',
                'ملاحظات'
            ],
            columns: [
                { type: 'text', width: 120 },                    // كود الصنف
                { type: 'text', width: 200, readOnly: true },    // اسم الصنف
                { type: 'text', width: 80, readOnly: true },     // الوحدة
                { type: 'numeric', width: 100 },                 // الكمية
                { type: 'numeric', width: 100 },                 // الكمية المجانية
                { type: 'numeric', width: 100 },                 // سعر الوحدة
                { type: 'numeric', width: 100 },                 // نسبة الخصم
                { type: 'numeric', width: 100 },                 // مبلغ الضريبة
                { type: 'numeric', width: 120, readOnly: true }, // الإجمالي
                { type: 'date', width: 120 },                    // تاريخ الإنتاج
                { type: 'date', width: 120 },                    // تاريخ الانتهاء
                { type: 'text', width: 150 }                     // ملاحظات
            ],
            rowHeaders: true,
            height: 400,
            width: '100%',
            licenseKey: 'non-commercial-and-evaluation',
            contextMenu: true,
            fillHandle: true,
            copyPaste: true,
            manualRowResize: true,
            manualColumnResize: true,
            stretchH: 'all',
            afterChange: function(changes, source) {
                if (source !== 'loadData' && changes) {
                    console.log('تم تغيير البيانات:', changes);
                    // هنا يمكن إضافة منطق حساب الإجماليات
                }
            }
        });

        console.log('✅ تم تهيئة جدول Handsontable بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تهيئة الجدول:', error);
        container.innerHTML = '<div class="alert alert-danger">خطأ في تحميل الجدول: ' + error.message + '</div>';
    }
}

// حساب صافي المبلغ
function calculateNetAmount() {
    const contractAmount = parseFloat($('#contract_amount').val()) || 0;
    const discountAmount = parseFloat($('#discount_amount').val()) || 0;
    const netAmount = contractAmount - discountAmount;
    $('#net_amount').val(netAmount.toFixed(3));
}

// فتح نافذة البحث عن الموردين
function openSupplierSearch() {
    alert('سيتم إضافة نافذة البحث عن الموردين لاحقاً');
}
</script>
{% endblock %}
