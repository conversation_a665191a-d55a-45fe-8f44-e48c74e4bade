"""
خدمة PDF عربية نهائية باستخدام ReportLab
تدعم النصوص العربية بشكل صحيح
"""

from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
import io
import os
from datetime import datetime
from typing import Dict, Optional

try:
    from arabic_reshaper import reshape
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False


class FinalArabicPDFService:
    """خدمة PDF عربية نهائية"""
    
    def __init__(self):
        """تهيئة الخدمة"""
        self.setup_styles()
    
    def setup_styles(self):
        """إعداد الأنماط"""
        self.styles = getSampleStyleSheet()
        
        # نمط العنوان الرئيسي
        self.title_style = ParagraphStyle(
            'ArabicTitle',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#1f4e79'),
            fontName='Helvetica-Bold'
        )
        
        # نمط العنوان الفرعي
        self.subtitle_style = ParagraphStyle(
            'ArabicSubtitle',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=15,
            alignment=TA_RIGHT,
            textColor=colors.HexColor('#2c5aa0'),
            fontName='Helvetica-Bold'
        )
        
        # نمط النص العادي
        self.normal_style = ParagraphStyle(
            'ArabicNormal',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=6,
            alignment=TA_RIGHT,
            fontName='Helvetica'
        )
    
    def process_arabic_text(self, text: str) -> str:
        """معالجة النص العربي للعرض الصحيح"""
        if not ARABIC_SUPPORT or not text:
            return text
        
        try:
            # إعادة تشكيل النص العربي
            reshaped_text = reshape(text)
            # تطبيق خوارزمية الاتجاه الثنائي
            display_text = get_display(reshaped_text)
            return display_text
        except:
            return text
    
    def create_delivery_order_pdf(self, order_data: Dict) -> bytes:
        """إنشاء PDF لأمر التسليم بالعربية"""
        
        # إنشاء buffer للـ PDF
        buffer = io.BytesIO()
        
        # إنشاء المستند
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=2*cm,
            bottomMargin=2*cm
        )
        
        # قائمة العناصر
        story = []
        
        # رأس المستند
        self._add_header(story)
        
        # معلومات الأمر الأساسية
        self._add_order_info(story, order_data)
        
        # معلومات الشحنة
        self._add_shipment_info(story, order_data)
        
        # بيانات المخلص
        self._add_agent_info(story, order_data)
        
        # تفاصيل التسليم
        self._add_delivery_info(story, order_data)
        
        # التذييل
        self._add_footer(story)
        
        # بناء المستند
        doc.build(story)
        
        # الحصول على البيانات
        pdf_data = buffer.getvalue()
        buffer.close()
        
        return pdf_data
    
    def _add_header(self, story):
        """إضافة رأس المستند"""
        # اسم الشركة
        company_name = Paragraph(
            self.process_arabic_text("شركة النقل والشحن المتطورة"), 
            self.title_style
        )
        story.append(company_name)
        
        # عنوان المستند
        doc_title = Paragraph(
            self.process_arabic_text("أمر تسليم للمخلص الجمركي"), 
            self.subtitle_style
        )
        story.append(doc_title)
        
        story.append(Spacer(1, 20))
        
        # خط فاصل
        line_data = [['', '', '', '']]
        line_table = Table(line_data, colWidths=[4*cm, 4*cm, 4*cm, 4*cm])
        line_table.setStyle(TableStyle([
            ('LINEBELOW', (0, 0), (-1, -1), 3, colors.HexColor('#1f4e79')),
        ]))
        story.append(line_table)
        story.append(Spacer(1, 15))
    
    def _add_order_info(self, story, order_data):
        """إضافة معلومات الأمر الأساسية"""
        # عنوان القسم
        section_title = Paragraph(
            self.process_arabic_text("معلومات الأمر"), 
            self.subtitle_style
        )
        story.append(section_title)
        
        # جدول معلومات الأمر
        order_info = [
            [
                self.process_arabic_text("رقم الأمر:"), 
                order_data.get('order_number', 'غير محدد'),
                self.process_arabic_text("تاريخ الإصدار:"), 
                datetime.now().strftime('%Y-%m-%d')
            ],
            [
                self.process_arabic_text("حالة الأمر:"), 
                self.process_arabic_text(self._get_status_arabic(order_data.get('order_status', 'draft'))),
                self.process_arabic_text("الأولوية:"), 
                self.process_arabic_text("عادية")
            ]
        ]
        
        order_table = Table(order_info, colWidths=[3*cm, 5*cm, 3*cm, 5*cm])
        order_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#f8f9fa')),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#dee2e6')),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (2, 0), (2, -1), 'Helvetica-Bold'),
        ]))
        
        story.append(order_table)
        story.append(Spacer(1, 15))
    
    def _add_shipment_info(self, story, order_data):
        """إضافة معلومات الشحنة"""
        section_title = Paragraph(
            self.process_arabic_text("المعلومات الأساسية للشحنة"), 
            self.subtitle_style
        )
        story.append(section_title)
        
        shipment_info = [
            [self.process_arabic_text("رقم التتبع:"), order_data.get('tracking_number', 'غير محدد')],
            [self.process_arabic_text("رقم الحجز:"), order_data.get('booking_number', 'غير محدد')],
            [self.process_arabic_text("نوع الشحنة:"), self.process_arabic_text(order_data.get('shipment_type', 'غير محدد'))],
            [self.process_arabic_text("الوزن الإجمالي:"), f"{order_data.get('total_weight', 'غير محدد')} {self.process_arabic_text('كيلو') if order_data.get('total_weight') else ''}"],
            [self.process_arabic_text("عدد الطرود:"), str(order_data.get('packages_count', 'غير محدد'))],
            [self.process_arabic_text("وصف البضاعة:"), self.process_arabic_text(order_data.get('cargo_description', 'غير محدد'))],
        ]
        
        shipment_table = Table(shipment_info, colWidths=[4*cm, 12*cm])
        shipment_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#e3f2fd')),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#90caf9')),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#1976d2')),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.white),
        ]))
        
        story.append(shipment_table)
        story.append(Spacer(1, 15))
    
    def _add_agent_info(self, story, order_data):
        """إضافة بيانات المخلص"""
        section_title = Paragraph(
            self.process_arabic_text("بيانات المخلص الجمركي"), 
            self.subtitle_style
        )
        story.append(section_title)
        
        agent_info = [
            [self.process_arabic_text("اسم المخلص:"), self.process_arabic_text(order_data.get('agent_name', 'غير محدد'))],
            [self.process_arabic_text("اسم الشركة:"), self.process_arabic_text(order_data.get('company_name', 'غير محدد'))],
            [self.process_arabic_text("رقم الترخيص:"), order_data.get('license_number', 'غير محدد')],
            [self.process_arabic_text("رقم الهاتف:"), order_data.get('agent_phone', 'غير محدد')],
            [self.process_arabic_text("البريد الإلكتروني:"), order_data.get('agent_email', 'غير محدد')],
        ]
        
        agent_table = Table(agent_info, colWidths=[4*cm, 12*cm])
        agent_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#fff3e0')),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#ffb74d')),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f57c00')),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.white),
        ]))
        
        story.append(agent_table)
        story.append(Spacer(1, 15))
    
    def _add_delivery_info(self, story, order_data):
        """إضافة تفاصيل التسليم"""
        section_title = Paragraph(
            self.process_arabic_text("تفاصيل التسليم"), 
            self.subtitle_style
        )
        story.append(section_title)
        
        delivery_info = [
            [self.process_arabic_text("موقع التسليم:"), self.process_arabic_text(order_data.get('delivery_location', 'غير محدد'))],
            [self.process_arabic_text("التاريخ المطلوب للتخليص:"), order_data.get('expected_completion_date', 'غير محدد')],
        ]
        
        delivery_table = Table(delivery_info, colWidths=[4*cm, 12*cm])
        delivery_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#e8f5e8')),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#81c784')),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#388e3c')),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.white),
        ]))
        
        story.append(delivery_table)
        story.append(Spacer(1, 20))
    
    def _add_footer(self, story):
        """إضافة التذييل"""
        # خط فاصل
        line_data = [['', '', '', '']]
        line_table = Table(line_data, colWidths=[4*cm, 4*cm, 4*cm, 4*cm])
        line_table.setStyle(TableStyle([
            ('LINEABOVE', (0, 0), (-1, -1), 2, colors.HexColor('#1f4e79')),
        ]))
        story.append(line_table)
        story.append(Spacer(1, 10))
        
        # معلومات الشركة
        footer_text = f"""
        <b>{self.process_arabic_text("شركة النقل والشحن المتطورة")}</b><br/>
        {self.process_arabic_text("العنوان: المملكة العربية السعودية - الرياض")}<br/>
        {self.process_arabic_text("الهاتف:")} +966 11 123 4567 | {self.process_arabic_text("البريد الإلكتروني:")} <EMAIL><br/>
        {self.process_arabic_text("الموقع الإلكتروني:")} www.shipping.com<br/><br/>
        {self.process_arabic_text("تاريخ الطباعة:")} {datetime.now().strftime('%Y-%m-%d %H:%M')}
        """
        
        footer = Paragraph(footer_text, self.normal_style)
        story.append(footer)
    
    def _get_status_arabic(self, status):
        """ترجمة حالة الأمر للعربية"""
        status_map = {
            'draft': 'مسودة',
            'sent': 'مرسل',
            'in_progress': 'قيد التنفيذ',
            'completed': 'مكتمل',
            'cancelled': 'ملغي'
        }
        return status_map.get(status, status)
    
    def create_pdf_file(self, order_data: Dict) -> str:
        """إنشاء ملف PDF وحفظه"""
        
        # إنشاء PDF
        pdf_data = self.create_delivery_order_pdf(order_data)
        
        # حفظ PDF في سطح المكتب
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        pdf_filename = f"delivery_order_{order_data.get('order_number', 'unknown')}.pdf"
        pdf_path = os.path.join(desktop_path, pdf_filename)
        
        with open(pdf_path, 'wb') as f:
            f.write(pdf_data)
        
        return pdf_path


# إنشاء instance عام
final_arabic_pdf_service = FinalArabicPDFService()


def generate_final_arabic_pdf(order_data: Dict) -> str:
    """دالة مساعدة لإنشاء PDF عربي نهائي"""
    return final_arabic_pdf_service.create_pdf_file(order_data)
