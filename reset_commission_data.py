#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعادة إدراج البيانات الأساسية لنظام العمولات
"""

from database_manager import DatabaseManager
import logging

# إعداد logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    try:
        db_manager = DatabaseManager()
        
        # التحقق من وجود الجداول
        tables = db_manager.execute_query(
            "SELECT table_name FROM user_tables WHERE table_name IN ('PURCHASE_REPRESENTATIVES', 'COMMISSION_TYPES')"
        )
        logger.info(f'الجداول الموجودة: {[t[0] for t in tables] if tables else "لا توجد"}')
        
        if not tables or len(tables) < 2:
            logger.error('الجداول غير موجودة!')
            return
        
        # حذف البيانات الموجودة
        logger.info('حذف البيانات الموجودة...')
        try:
            db_manager.execute_update("DELETE FROM purchase_representatives")
            db_manager.execute_update("DELETE FROM commission_types")
            logger.info('✅ تم حذف البيانات القديمة')
        except Exception as e:
            logger.warning(f'تحذير في حذف البيانات: {e}')
        
        # إدراج أنواع العمولات
        logger.info('إدراج أنواع العمولات...')
        types_data = [
            (1, 'FIXED', 'العمولة الثابتة', 'Fixed Commission', 'FIXED', 'مبلغ ثابت لكل أمر شراء معتمد', 1),
            (2, 'PERCENTAGE', 'العمولة النسبية', 'Percentage Commission', 'PERCENTAGE', 'نسبة مئوية من قيمة أمر الشراء', 2),
            (3, 'TIERED', 'العمولة المتدرجة', 'Tiered Commission', 'TIERED', 'نسب مختلفة حسب شرائح قيمة الطلب', 3),
            (4, 'QUANTITY_FIXED', 'العمولة الثابتة حسب الكمية', 'Fixed Quantity Commission', 'QUANTITY_FIXED', 'مبلغ ثابت لكل وحدة مطلوبة', 4),
            (5, 'QUANTITY_TIERED', 'العمولة المتدرجة حسب الكمية', 'Tiered Quantity Commission', 'QUANTITY_TIERED', 'نسب أو مبالغ مختلفة حسب شرائح الكمية', 5),
            (6, 'ITEM_BASED', 'العمولة حسب الصنف', 'Item-based Commission', 'ITEM_BASED', 'عمولات مختلفة لأصناف مختلفة', 6),
            (7, 'SUPPLIER_BASED', 'العمولة حسب المورد', 'Supplier-based Commission', 'SUPPLIER_BASED', 'عمولات مختلفة لموردين مختلفين', 7),
            (8, 'SEASONAL', 'العمولة الموسمية', 'Seasonal Commission', 'SEASONAL', 'عمولات خاصة لفترات محددة', 8)
        ]
        
        for data in types_data:
            insert_sql = """
            INSERT INTO commission_types (id, type_code, type_name, type_name_en, calculation_method, description, display_order)
            VALUES (:1, :2, :3, :4, :5, :6, :7)
            """
            try:
                db_manager.execute_update(insert_sql, data)
                logger.info(f'✅ تم إدراج نوع العمولة: {data[2]}')
            except Exception as e:
                logger.error(f'❌ خطأ في إدراج {data[2]}: {e}')
        
        # إدراج مندوبين تجريبيين
        logger.info('إدراج المندوبين التجريبيين...')
        reps_data = [
            (1, 'REP001', 'أحمد محمد الأحمد', 'Ahmed Mohammed Al-Ahmad', 'أجهزة كمبيوتر ومعدات تقنية', 50, 1000, 500000),
            (2, 'REP002', 'فاطمة علي السالم', 'Fatima Ali Al-Salem', 'مواد غذائية ومستهلكات', 30, 2000, 300000),
            (3, 'REP003', 'محمد عبدالله الخالد', 'Mohammed Abdullah Al-Khalid', 'أجهزة ومعدات طبية', 20, 500, 800000),
            (4, 'REP004', 'سارة أحمد المطيري', 'Sarah Ahmed Al-Mutairi', 'مواد خام وكيماويات', 40, 1500, 600000)
        ]
        
        for data in reps_data:
            insert_sql = """
            INSERT INTO purchase_representatives (id, rep_code, rep_name, rep_name_en, specialization, target_monthly_orders, target_monthly_quantity, target_monthly_value)
            VALUES (:1, :2, :3, :4, :5, :6, :7, :8)
            """
            try:
                db_manager.execute_update(insert_sql, data)
                logger.info(f'✅ تم إدراج المندوب: {data[2]}')
            except Exception as e:
                logger.error(f'❌ خطأ في إدراج {data[2]}: {e}')
        
        # التحقق من النتائج
        reps_count = db_manager.execute_query('SELECT COUNT(*) FROM purchase_representatives')[0][0]
        types_count = db_manager.execute_query('SELECT COUNT(*) FROM commission_types')[0][0]
        
        logger.info(f'🎉 النتائج النهائية: {reps_count} مندوبين، {types_count} أنواع عمولات')
        
        # عرض البيانات
        logger.info('المندوبين المُدرجين:')
        reps = db_manager.execute_query('SELECT rep_code, rep_name, specialization FROM purchase_representatives ORDER BY id')
        for rep in reps:
            logger.info(f'  - {rep[0]}: {rep[1]} ({rep[2]})')
        
        logger.info('أنواع العمولات المُدرجة:')
        types = db_manager.execute_query('SELECT type_code, type_name FROM commission_types ORDER BY display_order')
        for t in types:
            logger.info(f'  - {t[0]}: {t[1]}')
        
        logger.info('✅ تم إنجاز إعادة إدراج البيانات بنجاح!')
        
    except Exception as e:
        logger.error(f'خطأ عام: {e}')

if __name__ == '__main__':
    main()
