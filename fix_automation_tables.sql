-- إصلا<PERSON> جداول الأتمتة
-- Fix Automation Tables

-- أولاً: حذف الجداول الموجودة إذا كانت موجودة
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE automation_log CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE automation_settings CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE automation_rules CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE automation_statistics CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE agent_performance_log CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

-- حذ<PERSON> التسلسلات
BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE automation_log_seq';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE automation_settings_seq';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE automation_rules_seq';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE automation_statistics_seq';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE agent_performance_log_seq';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

-- إنشاء الجداول من جديد بشكل صحيح

-- 1. جدول سجل الأتمتة
CREATE TABLE automation_log (
    id NUMBER PRIMARY KEY,
    action VARCHAR2(100) NOT NULL,
    description VARCHAR2(500) NOT NULL,
    action_date DATE DEFAULT SYSDATE,
    system_user VARCHAR2(50) DEFAULT 'SYSTEM',
    shipment_id NUMBER,
    delivery_order_id NUMBER,
    agent_id NUMBER,
    status VARCHAR2(20) DEFAULT 'SUCCESS',
    error_message VARCHAR2(1000),
    execution_time NUMBER(10,3),
    created_at DATE DEFAULT SYSDATE
);

-- 2. جدول إعدادات الأتمتة
CREATE TABLE automation_settings (
    id NUMBER PRIMARY KEY,
    setting_key VARCHAR2(100) UNIQUE NOT NULL,
    setting_value VARCHAR2(500) NOT NULL,
    setting_type VARCHAR2(50) DEFAULT 'STRING',
    description VARCHAR2(500),
    is_active NUMBER(1) DEFAULT 1,
    created_at DATE DEFAULT SYSDATE,
    updated_at DATE DEFAULT SYSDATE
);

-- 3. جدول قواعد الأتمتة
CREATE TABLE automation_rules (
    id NUMBER PRIMARY KEY,
    rule_name VARCHAR2(200) NOT NULL,
    rule_type VARCHAR2(100) NOT NULL,
    trigger_condition VARCHAR2(500) NOT NULL,
    action_type VARCHAR2(100) NOT NULL,
    rule_config CLOB,
    is_active NUMBER(1) DEFAULT 1,
    priority_level NUMBER DEFAULT 5,
    success_count NUMBER DEFAULT 0,
    failure_count NUMBER DEFAULT 0,
    last_executed DATE,
    created_at DATE DEFAULT SYSDATE,
    updated_at DATE DEFAULT SYSDATE
);

-- 4. جدول إحصائيات الأتمتة
CREATE TABLE automation_statistics (
    id NUMBER PRIMARY KEY,
    stat_date DATE NOT NULL,
    total_automated_orders NUMBER DEFAULT 0,
    successful_orders NUMBER DEFAULT 0,
    failed_orders NUMBER DEFAULT 0,
    avg_processing_time NUMBER(10,3) DEFAULT 0,
    total_agents_assigned NUMBER DEFAULT 0,
    total_notifications_sent NUMBER DEFAULT 0,
    created_at DATE DEFAULT SYSDATE
);

-- 5. جدول تقييمات المخلصين التلقائية
CREATE TABLE agent_performance_log (
    id NUMBER PRIMARY KEY,
    agent_id NUMBER NOT NULL,
    evaluation_date DATE DEFAULT SYSDATE,
    total_orders NUMBER DEFAULT 0,
    completed_orders NUMBER DEFAULT 0,
    avg_completion_days NUMBER(5,2) DEFAULT 0,
    avg_delay_days NUMBER(5,2) DEFAULT 0,
    calculated_rating NUMBER(3,2) DEFAULT 0,
    performance_score NUMBER(5,2) DEFAULT 0,
    created_at DATE DEFAULT SYSDATE
);

-- إنشاء التسلسلات
CREATE SEQUENCE automation_log_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE automation_settings_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE automation_rules_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE automation_statistics_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE agent_performance_log_seq START WITH 1 INCREMENT BY 1;

-- إدراج الإعدادات الافتراضية
INSERT INTO automation_settings (id, setting_key, setting_value, setting_type, description) VALUES 
(automation_settings_seq.NEXTVAL, 'auto_create_orders', '1', 'BOOLEAN', 'إنشاء أوامر التسليم تلقائياً');

INSERT INTO automation_settings (id, setting_key, setting_value, setting_type, description) VALUES 
(automation_settings_seq.NEXTVAL, 'auto_assign_agents', '1', 'BOOLEAN', 'تعيين المخلصين تلقائياً');

INSERT INTO automation_settings (id, setting_key, setting_value, setting_type, description) VALUES 
(automation_settings_seq.NEXTVAL, 'auto_send_notifications', '1', 'BOOLEAN', 'إرسال الإشعارات تلقائياً');

INSERT INTO automation_settings (id, setting_key, setting_value, setting_type, description) VALUES 
(automation_settings_seq.NEXTVAL, 'auto_update_ratings', '1', 'BOOLEAN', 'تحديث تقييمات المخلصين تلقائياً');

-- إدراج قواعد الأتمتة الافتراضية
INSERT INTO automation_rules (id, rule_name, rule_type, trigger_condition, action_type, rule_config) VALUES 
(automation_rules_seq.NEXTVAL, 'إنشاء أمر تسليم تلقائي', 'SHIPMENT_STATUS', 'arrived_port,customs_clearance,ready_for_delivery', 'CREATE_DELIVERY_ORDER', '{"auto_assign": true}');

INSERT INTO automation_rules (id, rule_name, rule_type, trigger_condition, action_type, rule_config) VALUES 
(automation_rules_seq.NEXTVAL, 'اختيار المخلص الأمثل', 'DELIVERY_ORDER', 'order_created', 'ASSIGN_AGENT', '{"criteria": {"specialization_weight": 0.4}}');

INSERT INTO automation_rules (id, rule_name, rule_type, trigger_condition, action_type, rule_config) VALUES 
(automation_rules_seq.NEXTVAL, 'إرسال إشعارات تلقائية', 'STATUS_CHANGE', 'any_status_change', 'SEND_NOTIFICATION', '{"channels": ["SMS", "EMAIL"]}');

INSERT INTO automation_rules (id, rule_name, rule_type, trigger_condition, action_type, rule_config) VALUES 
(automation_rules_seq.NEXTVAL, 'تحديث تقييمات المخلصين', 'SCHEDULED', 'daily_midnight', 'UPDATE_RATINGS', '{"calculation_method": "performance_based"}');

-- إدراج بيانات تجريبية لسجل الأتمتة (بدون أخطاء)
INSERT INTO automation_log (id, action, description, action_date, shipment_id, delivery_order_id, agent_id, status) VALUES 
(automation_log_seq.NEXTVAL, 'AUTO_CREATE_ORDER', 'تم إنشاء أمر تسليم تلقائي DO-2025-000156', SYSDATE - INTERVAL '5' MINUTE, 1, 156, NULL, 'SUCCESS');

INSERT INTO automation_log (id, action, description, action_date, shipment_id, delivery_order_id, agent_id, status) VALUES 
(automation_log_seq.NEXTVAL, 'ASSIGN_AGENT', 'تم تعيين المخلص أحمد محمد تلقائياً', SYSDATE - INTERVAL '12' MINUTE, 1, 156, 1, 'SUCCESS');

INSERT INTO automation_log (id, action, description, action_date, shipment_id, delivery_order_id, agent_id, status) VALUES 
(automation_log_seq.NEXTVAL, 'SEND_NOTIFICATION', 'تم إرسال إشعار تذكير للمخلص', SYSDATE - INTERVAL '18' MINUTE, NULL, 156, 1, 'SUCCESS');

INSERT INTO automation_log (id, action, description, action_date, shipment_id, delivery_order_id, agent_id, status) VALUES 
(automation_log_seq.NEXTVAL, 'UPDATE_RATINGS', 'تم تحديث تقييم المخلص سعد العلي', SYSDATE - INTERVAL '25' MINUTE, NULL, NULL, 2, 'SUCCESS');

INSERT INTO automation_log (id, action, description, action_date, shipment_id, delivery_order_id, agent_id, status) VALUES 
(automation_log_seq.NEXTVAL, 'AUTO_CREATE_ORDER', 'تم إنشاء أمر تسليم تلقائي DO-2025-000157', SYSDATE - INTERVAL '35' MINUTE, 2, 157, NULL, 'SUCCESS');

-- إدراج إحصائيات اليوم الحالي
INSERT INTO automation_statistics (id, stat_date, total_automated_orders, successful_orders, failed_orders, avg_processing_time, total_agents_assigned, total_notifications_sent) VALUES 
(automation_statistics_seq.NEXTVAL, TRUNC(SYSDATE), 127, 124, 3, 2.5, 15, 89);

-- إدراج بيانات تجريبية لأداء المخلصين
INSERT INTO agent_performance_log (id, agent_id, total_orders, completed_orders, avg_completion_days, calculated_rating, performance_score) VALUES 
(agent_performance_log_seq.NEXTVAL, 1, 25, 23, 2.1, 4.6, 92.0);

INSERT INTO agent_performance_log (id, agent_id, total_orders, completed_orders, avg_completion_days, calculated_rating, performance_score) VALUES 
(agent_performance_log_seq.NEXTVAL, 2, 18, 17, 1.8, 4.8, 94.4);

-- حفظ التغييرات
COMMIT;

-- التحقق من إنشاء الجداول
SELECT 'automation_log' as table_name, COUNT(*) as record_count FROM automation_log
UNION ALL
SELECT 'automation_settings', COUNT(*) FROM automation_settings
UNION ALL
SELECT 'automation_rules', COUNT(*) FROM automation_rules
UNION ALL
SELECT 'automation_statistics', COUNT(*) FROM automation_statistics
UNION ALL
SELECT 'agent_performance_log', COUNT(*) FROM agent_performance_log;

-- عرض بنية جدول automation_log للتأكد
DESCRIBE automation_log;
