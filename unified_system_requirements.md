# متطلبات النظام المحاسبي الموحد
# Unified Accounting System Requirements

## 📋 **ملخص المشروع**

تطوير نظام محاسبي موحد يعتمد على جدول `BALANCE_TRANSACTIONS` كمصدر وحيد للحقيقة، مع إلغاء الاعتماد على `CURRENT_BALANCES` وتوحيد إدارة الأرصدة الافتتاحية.

---

## 🎯 **الأهداف الرئيسية**

### **1️⃣ التوحيد والتبسيط:**
- مصدر واحد للحقيقة: `BALANCE_TRANSACTIONS`
- إلغاء تضارب البيانات بين الجداول
- نظام موحد لجميع أنواع الكيانات

### **2️⃣ المرونة والتوسع:**
- دعم الفروع المتعددة
- دعم العملات المتعددة مع أسعار الصرف
- إمكانية إضافة كيانات جديدة بسهولة

### **3️⃣ التقارير المحسنة:**
- تقارير شهرية وسنوية سريعة
- تتبع تاريخي كامل للمعاملات
- إمكانيات تحليلية متقدمة

---

## 🏗️ **المتطلبات التقنية**

### **1️⃣ تعديلات قاعدة البيانات:**

#### **أ. تعديل جدول BALANCE_TRANSACTIONS:**
```sql
-- الأعمدة الجديدة المطلوبة
ALTER TABLE BALANCE_TRANSACTIONS ADD (
    BAL NUMBER(15,2) DEFAULT 0,              -- رصيد موحد
    BAL_F NUMBER(15,2) DEFAULT 0,            -- رصيد بالعملة الأساسية
    MONTH_NO NUMBER(2),                      -- رقم الشهر
    YEAR_NO NUMBER(4),                       -- رقم السنة
    BRANCH_ID NUMBER DEFAULT 1               -- رقم الفرع
);
```

#### **ب. إضافة أنواع كيانات جديدة:**
- `PURCHASE_AGENT` - مندوبي المشتريات
- `SALES_AGENT` - مندوبي المبيعات  
- `SHIPPING_COMPANY` - شركات الشحن

#### **ج. فهارس محسنة للأداء:**
```sql
CREATE INDEX IDX_BT_ENT_BAL ON BALANCE_TRANSACTIONS(ENTITY_TYPE_CODE, ENTITY_ID, CURRENCY_CODE);
CREATE INDEX IDX_BT_PERIOD ON BALANCE_TRANSACTIONS(YEAR_NO, MONTH_NO);
CREATE INDEX IDX_BT_BRANCH ON BALANCE_TRANSACTIONS(BRANCH_ID);
```

### **2️⃣ Packages المطلوبة:**

#### **أ. Package الأرصدة الافتتاحية (OB_PKG):**
- `INSERT_BAL` - إدراج رصيد افتتاحي
- `UPDATE_BAL` - تعديل رصيد افتتاحي
- `DELETE_BAL` - حذف رصيد افتتاحي
- `GET_BAL` - الحصول على رصيد افتتاحي

#### **ب. Package ترحيل الأرصدة (BT_PKG):**
- `POST_TXN` - ترحيل معاملة
- `REVERSE_TXN` - عكس معاملة
- `GET_BAL` - الحصول على الرصيد الحالي
- `GET_MONTH_BAL` - الحصول على رصيد شهري
- `GET_BAL_HIST` - الحصول على تاريخ الرصيد

### **3️⃣ Views للتقارير:**
- `V_CURR_BAL` - الأرصدة الحالية
- `V_MONTH_BAL` - الأرصدة الشهرية
- `V_ENT_SUM` - ملخص الكيانات

---

## 🔧 **المتطلبات الوظيفية**

### **1️⃣ إدارة الأرصدة الافتتاحية:**
- إدخال أرصدة افتتاحية لجميع أنواع الكيانات
- تعديل وحذف الأرصدة الافتتاحية
- دعم العملات المتعددة
- دعم الفروع المتعددة

### **2️⃣ ترحيل المعاملات:**
- ترحيل معاملات مدينة ودائنة
- دعم أسعار الصرف
- عكس المعاملات
- تتبع تاريخي كامل

### **3️⃣ التقارير:**
- رصيد حالي لأي كيان
- أرصدة شهرية وسنوية
- تقارير الفروع
- تقارير العملات المتعددة
- تاريخ المعاملات

### **4️⃣ الأمان والتحكم:**
- صلاحيات المستخدمين
- تتبع التغييرات
- معالجة الأخطاء
- النسخ الاحتياطي

---

## 📊 **متطلبات الأداء**

### **1️⃣ سرعة الاستعلامات:**
- استعلام الرصيد الحالي: أقل من 100ms
- التقارير الشهرية: أقل من 500ms
- تاريخ المعاملات: أقل من 1s

### **2️⃣ قابلية التوسع:**
- دعم مليون معاملة سنوياً
- دعم 100 فرع
- دعم 50 عملة

### **3️⃣ الموثوقية:**
- توفر 99.9%
- نسخ احتياطي يومي
- استرداد سريع

---

## 🎨 **متطلبات واجهة المستخدم**

### **1️⃣ شاشات الأرصدة الافتتاحية:**
- شاشة إدخال أرصدة افتتاحية
- شاشة تعديل الأرصدة
- شاشة عرض الأرصدة بالفروع

### **2️⃣ شاشات التقارير:**
- تقرير الأرصدة الحالية
- تقرير الأرصدة الشهرية
- تقرير تاريخ المعاملات
- تقرير ملخص الكيانات

### **3️⃣ شاشات الإدارة:**
- إدارة أنواع الكيانات
- إدارة الفروع
- إدارة العملات

---

## 🔄 **خطة الهجرة**

### **1️⃣ المرحلة التحضيرية:**
- تعديل بنية قاعدة البيانات
- إنشاء الـ Packages والـ Views
- اختبار الأداء

### **2️⃣ مرحلة الهجرة:**
- نقل البيانات من `CURRENT_BALANCES`
- نقل البيانات من `OPENING_BALANCES`
- التحقق من صحة البيانات

### **3️⃣ مرحلة التشغيل:**
- تشغيل متوازي مع النظام القديم
- اختبار شامل
- التحول الكامل للنظام الجديد

---

## ✅ **معايير القبول**

### **1️⃣ الوظائف الأساسية:**
- [ ] إدخال وتعديل الأرصدة الافتتاحية
- [ ] ترحيل المعاملات بدقة
- [ ] عكس المعاملات بنجاح
- [ ] تقارير دقيقة وسريعة

### **2️⃣ الأداء:**
- [ ] استعلامات سريعة (أقل من المحدد)
- [ ] دعم الحمولة المطلوبة
- [ ] استقرار النظام

### **3️⃣ الأمان:**
- [ ] حماية البيانات
- [ ] تتبع التغييرات
- [ ] صلاحيات المستخدمين

### **4️⃣ سهولة الاستخدام:**
- [ ] واجهات بديهية
- [ ] تدريب الفريق
- [ ] توثيق شامل

---

## 🎯 **المخاطر والتحديات**

### **1️⃣ المخاطر التقنية:**
- **الأداء**: استعلامات SUM قد تكون بطيئة
- **الحل**: فهارس محسنة ومعايرة دقيقة

### **2️⃣ مخاطر البيانات:**
- **فقدان البيانات**: أثناء الهجرة
- **الحل**: نسخ احتياطي ومراحل تدريجية

### **3️⃣ مخاطر المستخدمين:**
- **مقاومة التغيير**: تعود على النظام القديم
- **الحل**: تدريب شامل ودعم مستمر

---

## 📅 **الجدول الزمني المقترح**

| **المرحلة** | **المدة** | **الوصف** |
|-------------|-----------|-----------|
| **التحليل والتصميم** | أسبوعان | تحليل شامل وتصميم مفصل |
| **تطوير قاعدة البيانات** | أسبوع | تعديل الجداول والفهارس |
| **تطوير الـ Packages** | أسبوعان | إنشاء OB_PKG و BT_PKG |
| **تطوير الواجهات** | أسبوعان | شاشات الإدخال والتقارير |
| **الاختبار** | أسبوع | اختبار شامل وتحسين الأداء |
| **الهجرة** | أسبوع | نقل البيانات والتشغيل |
| **التدريب والنشر** | أسبوع | تدريب المستخدمين والنشر |

**إجمالي المدة: 10 أسابيع**

---

## 🏆 **الفوائد المتوقعة**

### **1️⃣ فوائد تقنية:**
- نظام موحد ومتسق
- أداء محسن
- سهولة الصيانة

### **2️⃣ فوائد تشغيلية:**
- تقارير أسرع ودقيقة
- مرونة في التوسع
- تقليل الأخطاء

### **3️⃣ فوائد استراتيجية:**
- قاعدة قوية للتطوير المستقبلي
- دعم اتخاذ القرارات
- ميزة تنافسية

---

**تم إعداد هذه الوثيقة بناءً على التحليل الشامل للنظام الحالي ومتطلبات العمل المستقبلية.**
