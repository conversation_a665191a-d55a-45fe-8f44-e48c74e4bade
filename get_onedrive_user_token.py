#!/usr/bin/env python3
"""
الحصول على OneDrive User token مباشرة
"""

import sys
import os
import requests
import json

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def get_user_token_with_code():
    """الحصول على User token باستخدام الكود"""
    
    # الكود المستخرج من الرابط
    auth_code = "*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    
    # قراءة الإعدادات
    config_path = "app/shipments/cloud_config.json"
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        onedrive_config = config.get('onedrive', {})
        client_id = onedrive_config.get('client_id')
        client_secret = onedrive_config.get('client_secret')
        tenant_id = onedrive_config.get('tenant_id')
        redirect_uri = onedrive_config.get('redirect_uri', 'https://sas.alfogehi.net:5000/auth/onedrive/callback')
        
        print(f"🔄 تبديل الكود بـ access token...")
        
        # تبديل الكود بـ access token
        token_url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token"
        
        token_data = {
            'grant_type': 'authorization_code',
            'client_id': client_id,
            'client_secret': client_secret,
            'code': auth_code,
            'redirect_uri': redirect_uri,
            'scope': 'Files.ReadWrite Files.ReadWrite.All offline_access'
        }
        
        response = requests.post(token_url, data=token_data, timeout=30)
        
        print(f"📊 استجابة تبديل الكود: {response.status_code}")
        
        if response.status_code == 200:
            token_response = response.json()
            access_token = token_response.get('access_token')
            refresh_token = token_response.get('refresh_token')
            
            if access_token:
                print(f"✅ تم الحصول على User access token")
                print(f"📄 Token: {access_token[:50]}...")
                
                # اختبار الـ token
                test_url = "https://graph.microsoft.com/v1.0/me/drive"
                headers = {
                    'Authorization': f'Bearer {access_token}',
                    'Content-Type': 'application/json'
                }
                
                test_response = requests.get(test_url, headers=headers, timeout=10)
                
                print(f"📊 اختبار الـ token: {test_response.status_code}")
                
                if test_response.status_code == 200:
                    drive_info = test_response.json()
                    print(f"✅ User token يعمل بشكل صحيح")
                    print(f"📁 Drive ID: {drive_info.get('id', 'غير متوفر')}")
                    print(f"👤 Owner: {drive_info.get('owner', {}).get('user', {}).get('displayName', 'غير متوفر')}")
                    
                    # حفظ الـ tokens الجديدة
                    config['onedrive']['access_token'] = access_token
                    config['onedrive']['refresh_token'] = refresh_token
                    config['onedrive']['token_type'] = 'user'  # تمييز نوع الـ token
                    
                    with open(config_path, 'w', encoding='utf-8') as f:
                        json.dump(config, f, indent=2, ensure_ascii=False)
                    
                    print(f"💾 تم حفظ User tokens")
                    return True
                else:
                    print(f"❌ فشل في اختبار User token: {test_response.status_code}")
                    print(f"📄 Response: {test_response.text}")
                    return False
            else:
                print(f"❌ لم يتم الحصول على access token")
                return False
        else:
            print(f"❌ فشل في تبديل الكود: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء الحصول على OneDrive User token...")
    success = get_user_token_with_code()
    
    if success:
        print("\n✅ تم الحصول على User token بنجاح")
        print("🎉 الآن يمكن إنشاء روابط OneDrive حقيقية")
    else:
        print("\n❌ فشل في الحصول على User token")
        sys.exit(1)
