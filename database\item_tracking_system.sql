-- نظام تتبع الأصناف مرتبط بحالات الشحنة
-- Item Tracking System Linked to Shipment Status

-- 1. إضافة أعمدة تتبع الأصناف لجدول الأصناف الموجود
ALTER TABLE cargo_shipment_items ADD (
    item_status VARCHAR2(50) DEFAULT 'draft',
    item_location VARCHAR2(200),
    status_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status_updated_by NUMBER,
    tracking_notes CLOB,
    is_damaged NUMBER(1) DEFAULT 0,
    damage_notes CLOB,
    current_latitude NUMBER,
    current_longitude NUMBER
);

-- 2. إ<PERSON><PERSON><PERSON><PERSON> جدول تاريخ حالات الأصناف
CREATE TABLE item_status_history (
    id NUMBER PRIMARY KEY,
    shipment_item_id NUMBER NOT NULL,
    old_status VARCHAR2(50),
    new_status VARCHAR2(50) NOT NULL,
    status_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by NUMBER,
    notes CLOB,
    location VARCHAR2(200),
    latitude NUMBER,
    longitude NUMBER,
    auto_updated NUMBER(1) DEFAULT 0,
    CONSTRAINT fk_item_status_history FOREIGN KEY (shipment_item_id) 
        REFERENCES cargo_shipment_items(id) ON DELETE CASCADE
);

-- 3. إنشاء sequence لتاريخ حالات الأصناف
CREATE SEQUENCE item_status_history_seq START WITH 1 INCREMENT BY 1;

-- 4. إنشاء جدول ربط حالات الشحنة بحالات الأصناف
CREATE TABLE shipment_item_status_mapping (
    id NUMBER PRIMARY KEY,
    shipment_status VARCHAR2(50) NOT NULL,
    item_status VARCHAR2(50) NOT NULL,
    auto_update NUMBER(1) DEFAULT 1,
    priority_order NUMBER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_mapping_shipment_status FOREIGN KEY (shipment_status) 
        REFERENCES shipment_status_config(status_code),
    CONSTRAINT uk_status_mapping UNIQUE (shipment_status, item_status)
);

-- 5. إدراج قواعد الربط الافتراضية
INSERT INTO shipment_item_status_mapping VALUES (shipment_item_status_mapping_seq.NEXTVAL, 'draft', 'draft', 1, 1, CURRENT_TIMESTAMP);
INSERT INTO shipment_item_status_mapping VALUES (shipment_item_status_mapping_seq.NEXTVAL, 'confirmed', 'confirmed', 1, 2, CURRENT_TIMESTAMP);
INSERT INTO shipment_item_status_mapping VALUES (shipment_item_status_mapping_seq.NEXTVAL, 'in_transit', 'in_transit', 1, 3, CURRENT_TIMESTAMP);
INSERT INTO shipment_item_status_mapping VALUES (shipment_item_status_mapping_seq.NEXTVAL, 'arrived_port', 'arrived_port', 1, 4, CURRENT_TIMESTAMP);
INSERT INTO shipment_item_status_mapping VALUES (shipment_item_status_mapping_seq.NEXTVAL, 'customs_clearance', 'customs_clearance', 1, 5, CURRENT_TIMESTAMP);
INSERT INTO shipment_item_status_mapping VALUES (shipment_item_status_mapping_seq.NEXTVAL, 'ready_pickup', 'ready_pickup', 1, 6, CURRENT_TIMESTAMP);
INSERT INTO shipment_item_status_mapping VALUES (shipment_item_status_mapping_seq.NEXTVAL, 'delivered', 'delivered', 1, 7, CURRENT_TIMESTAMP);
INSERT INTO shipment_item_status_mapping VALUES (shipment_item_status_mapping_seq.NEXTVAL, 'cancelled', 'cancelled', 1, 8, CURRENT_TIMESTAMP);
INSERT INTO shipment_item_status_mapping VALUES (shipment_item_status_mapping_seq.NEXTVAL, 'delayed', 'delayed', 1, 9, CURRENT_TIMESTAMP);
INSERT INTO shipment_item_status_mapping VALUES (shipment_item_status_mapping_seq.NEXTVAL, 'returned', 'returned', 1, 10, CURRENT_TIMESTAMP);

-- 6. إنشاء sequence للربط
CREATE SEQUENCE shipment_item_status_mapping_seq START WITH 1 INCREMENT BY 1;

-- 7. إنشاء trigger لتحديث حالة الأصناف تلقائياً عند تغيير حالة الشحنة
CREATE OR REPLACE TRIGGER trg_auto_update_item_status
    AFTER UPDATE OF shipment_status ON cargo_shipments
    FOR EACH ROW
DECLARE
    v_new_item_status VARCHAR2(50);
    v_count NUMBER;
BEGIN
    -- التحقق من وجود ربط للحالة الجديدة
    SELECT COUNT(*) INTO v_count
    FROM shipment_item_status_mapping
    WHERE shipment_status = :NEW.shipment_status
    AND auto_update = 1;
    
    IF v_count > 0 THEN
        -- الحصول على حالة الأصناف المقابلة
        SELECT item_status INTO v_new_item_status
        FROM shipment_item_status_mapping
        WHERE shipment_status = :NEW.shipment_status
        AND auto_update = 1
        AND ROWNUM = 1;
        
        -- تحديث جميع أصناف الشحنة
        UPDATE cargo_shipment_items
        SET item_status = v_new_item_status,
            status_updated_at = CURRENT_TIMESTAMP,
            status_updated_by = :NEW.status_updated_by
        WHERE cargo_shipment_id = :NEW.id
        AND item_status != v_new_item_status; -- تجنب التحديث غير الضروري
        
        -- إضافة سجل في تاريخ الأصناف للأصناف المحدثة
        INSERT INTO item_status_history (
            id, shipment_item_id, old_status, new_status,
            status_date, updated_by, notes, auto_updated
        )
        SELECT 
            item_status_history_seq.NEXTVAL,
            csi.id,
            csi.item_status,
            v_new_item_status,
            CURRENT_TIMESTAMP,
            :NEW.status_updated_by,
            'تم التحديث تلقائياً مع حالة الشحنة: ' || :NEW.shipment_status,
            1
        FROM cargo_shipment_items csi
        WHERE csi.cargo_shipment_id = :NEW.id
        AND csi.item_status != v_new_item_status;
    END IF;
END;
/

-- 8. إنشاء view شامل للأصناف مع حالاتها
CREATE OR REPLACE VIEW v_shipment_items_with_status AS
SELECT 
    csi.*,
    cs.shipment_number,
    cs.shipment_status as shipment_status_code,
    sc_shipment.status_name_ar as shipment_status_name,
    sc_shipment.status_color as shipment_status_color,
    sc_item.status_name_ar as item_status_name,
    sc_item.status_color as item_status_color,
    sc_item.status_icon as item_status_icon,
    (SELECT COUNT(*) FROM item_status_history WHERE shipment_item_id = csi.id) as status_changes_count,
    (SELECT MAX(status_date) FROM item_status_history WHERE shipment_item_id = csi.id) as last_status_change
FROM cargo_shipment_items csi
JOIN cargo_shipments cs ON csi.cargo_shipment_id = cs.id
LEFT JOIN shipment_status_config sc_shipment ON cs.shipment_status = sc_shipment.status_code
LEFT JOIN shipment_status_config sc_item ON csi.item_status = sc_item.status_code;

-- 9. إنشاء فهارس للأداء
CREATE INDEX idx_item_status ON cargo_shipment_items(item_status);
CREATE INDEX idx_item_status_updated ON cargo_shipment_items(status_updated_at);
CREATE INDEX idx_item_history_item ON item_status_history(shipment_item_id);
CREATE INDEX idx_item_history_date ON item_status_history(status_date);
CREATE INDEX idx_mapping_shipment_status ON shipment_item_status_mapping(shipment_status);

-- 10. إنشاء procedure لتحديث حالة صنف محدد يدوياً
CREATE OR REPLACE PROCEDURE update_item_status(
    p_item_id IN NUMBER,
    p_new_status IN VARCHAR2,
    p_location IN VARCHAR2 DEFAULT NULL,
    p_notes IN CLOB DEFAULT NULL,
    p_user_id IN NUMBER DEFAULT NULL,
    p_latitude IN NUMBER DEFAULT NULL,
    p_longitude IN NUMBER DEFAULT NULL
) AS
    v_old_status VARCHAR2(50);
    v_shipment_id NUMBER;
BEGIN
    -- الحصول على الحالة الحالية
    SELECT item_status, cargo_shipment_id 
    INTO v_old_status, v_shipment_id
    FROM cargo_shipment_items 
    WHERE id = p_item_id;
    
    -- تحديث الصنف
    UPDATE cargo_shipment_items
    SET item_status = p_new_status,
        item_location = NVL(p_location, item_location),
        status_updated_at = CURRENT_TIMESTAMP,
        status_updated_by = p_user_id,
        tracking_notes = NVL(p_notes, tracking_notes),
        current_latitude = NVL(p_latitude, current_latitude),
        current_longitude = NVL(p_longitude, current_longitude)
    WHERE id = p_item_id;
    
    -- إضافة سجل في التاريخ
    INSERT INTO item_status_history (
        id, shipment_item_id, old_status, new_status,
        status_date, updated_by, notes, location,
        latitude, longitude, auto_updated
    ) VALUES (
        item_status_history_seq.NEXTVAL,
        p_item_id,
        v_old_status,
        p_new_status,
        CURRENT_TIMESTAMP,
        p_user_id,
        p_notes,
        p_location,
        p_latitude,
        p_longitude,
        0
    );
    
    COMMIT;
END;
/

-- 11. إنشاء function للحصول على إحصائيات الأصناف
CREATE OR REPLACE FUNCTION get_shipment_items_stats(p_shipment_id IN NUMBER)
RETURN SYS_REFCURSOR AS
    v_cursor SYS_REFCURSOR;
BEGIN
    OPEN v_cursor FOR
        SELECT 
            sc.status_name_ar,
            sc.status_color,
            sc.status_icon,
            COUNT(csi.id) as item_count,
            ROUND(COUNT(csi.id) * 100.0 / SUM(COUNT(csi.id)) OVER(), 2) as percentage
        FROM cargo_shipment_items csi
        LEFT JOIN shipment_status_config sc ON csi.item_status = sc.status_code
        WHERE csi.cargo_shipment_id = p_shipment_id
        GROUP BY sc.status_name_ar, sc.status_color, sc.status_icon, sc.status_order
        ORDER BY sc.status_order;
    
    RETURN v_cursor;
END;
/

COMMIT;
