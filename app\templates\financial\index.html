{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    {{ title }}
                </h2>
                <div class="d-flex gap-2">
                    <div class="dropdown">
                        <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-plus me-2"></i>
                            إضافة جديد
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="alert('ميزة الفواتير قيد التطوير')">
                                <i class="fas fa-file-invoice me-2"></i>فاتورة جديدة
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="alert('ميزة المدفوعات قيد التطوير')">
                                <i class="fas fa-credit-card me-2"></i>دفعة جديدة
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="alert('ميزة المصروفات قيد التطوير')">
                                <i class="fas fa-receipt me-2"></i>مصروف جديد
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Financial Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إجمالي الإيرادات</h6>
                                    <h3 class="mb-0">{{ "%.2f"|format(stats.total_revenue or 0) }} ر.س</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-arrow-up fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إجمالي المصروفات</h6>
                                    <h3 class="mb-0">{{ "%.2f"|format(stats.total_expenses or 0) }} ر.س</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-arrow-down fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">المبالغ المستحقة</h6>
                                    <h3 class="mb-0">{{ "%.2f"|format(stats.pending_receivables or 0) }} ر.س</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">صافي الربح</h6>
                                    <h3 class="mb-0">{{ "%.2f"|format((stats.total_revenue or 0) - (stats.total_expenses or 0)) }} ر.س</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                إجراءات سريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-2">
                                    <a href="#" onclick="alert('ميزة الفواتير قيد التطوير')" class="btn btn-outline-primary w-100 mb-2">
                                        <i class="fas fa-file-invoice me-2"></i>
                                        الفواتير
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a href="{{ url_for('financial.payments') }}" class="btn btn-outline-success w-100 mb-2">
                                        <i class="fas fa-credit-card me-2"></i>
                                        المدفوعات
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a href="{{ url_for('financial.expenses') }}" class="btn btn-outline-danger w-100 mb-2">
                                        <i class="fas fa-receipt me-2"></i>
                                        المصروفات
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a href="#" onclick="alert('ميزة الحسابات قيد التطوير')" class="btn btn-outline-info w-100 mb-2">
                                        <i class="fas fa-university me-2"></i>
                                        الحسابات
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a href="#" onclick="alert('ميزة التقارير قيد التطوير')" class="btn btn-outline-warning w-100 mb-2">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        التقارير
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a href="#" onclick="alert('ميزة الميزانية قيد التطوير')" class="btn btn-outline-secondary w-100 mb-2">
                                        <i class="fas fa-calculator me-2"></i>
                                        الميزانية
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-file-invoice me-2"></i>
                                آخر الفواتير
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if recent_invoices %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>رقم الفاتورة</th>
                                                <th>المورد</th>
                                                <th>المبلغ</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for invoice in recent_invoices %}
                                                <tr>
                                                    <td>{{ invoice.invoice_number }}</td>
                                                    <td>{{ invoice.supplier.name_ar if invoice.supplier else 'غير محدد' }}</td>
                                                    <td>{{ "%.2f"|format(invoice.total_amount) }} ر.س</td>
                                                    <td>
                                                        {% if invoice.status == 'paid' %}
                                                            <span class="badge bg-success">مدفوعة</span>
                                                        {% elif invoice.status == 'pending' %}
                                                            <span class="badge bg-warning">في الانتظار</span>
                                                        {% elif invoice.status == 'overdue' %}
                                                            <span class="badge bg-danger">متأخرة</span>
                                                        {% else %}
                                                            <span class="badge bg-secondary">{{ invoice.status }}</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-center mt-3">
                                    <a href="#" onclick="alert('ميزة الفواتير قيد التطوير')" class="btn btn-sm btn-outline-primary">
                                        عرض جميع الفواتير
                                    </a>
                                </div>
                            {% else %}
                                <div class="text-center py-3">
                                    <i class="fas fa-file-invoice fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">لا توجد فواتير حديثة</p>
                                    <a href="#" onclick="alert('ميزة الفواتير قيد التطوير')" class="btn btn-sm btn-primary">
                                        إنشاء فاتورة جديدة
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-credit-card me-2"></i>
                                آخر المدفوعات
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if recent_payments %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>رقم الدفعة</th>
                                                <th>المبلغ</th>
                                                <th>الطريقة</th>
                                                <th>التاريخ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for payment in recent_payments %}
                                                <tr>
                                                    <td>{{ payment.payment_number }}</td>
                                                    <td>{{ "%.2f"|format(payment.amount) }} ر.س</td>
                                                    <td>
                                                        {% if payment.payment_method == 'cash' %}
                                                            نقداً
                                                        {% elif payment.payment_method == 'bank_transfer' %}
                                                            تحويل بنكي
                                                        {% elif payment.payment_method == 'check' %}
                                                            شيك
                                                        {% else %}
                                                            {{ payment.payment_method }}
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ payment.payment_date.strftime('%Y-%m-%d') if payment.payment_date else '-' }}</td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-center mt-3">
                                    <a href="{{ url_for('financial.payments') }}" class="btn btn-sm btn-outline-success">
                                        عرض جميع المدفوعات
                                    </a>
                                </div>
                            {% else %}
                                <div class="text-center py-3">
                                    <i class="fas fa-credit-card fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">لا توجد مدفوعات حديثة</p>
                                    <a href="#" onclick="alert('ميزة المدفوعات قيد التطوير')" class="btn btn-sm btn-success">
                                        تسجيل دفعة جديدة
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Monthly Chart -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-area me-2"></i>
                                الأداء المالي الشهري
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="text-center py-5">
                                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">الرسم البياني للأداء المالي</h5>
                                <p class="text-muted">سيتم عرض الرسم البياني للإيرادات والمصروفات هنا</p>
                                <small class="text-muted">قيد التطوير - سيتم إضافة الرسوم البيانية التفاعلية قريباً</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
