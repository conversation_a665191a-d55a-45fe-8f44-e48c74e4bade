"""
نظام تتبع الأصناف مرتبط بحالات الشحنة
Item Tracking System Linked to Shipment Status
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from database_manager import DatabaseManager
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class ItemTracker:
    """مدير تتبع الأصناف"""
    
    def __init__(self):
        self.db = DatabaseManager()
    
    def get_shipment_items_with_status(self, shipment_id):
        """الحصول على أصناف الشحنة مع حالاتها"""
        try:
            query = """
                SELECT 
                    csi.id,
                    csi.item_name,
                    csi.quantity,
                    csi.unit,
                    csi.item_status,
                    csi.item_location,
                    csi.status_updated_at,
                    csi.tracking_notes,
                    csi.is_damaged,
                    csi.damage_notes,
                    sc.status_name_ar as status_name,
                    sc.status_color,
                    sc.status_icon,
                    cs.shipment_status as shipment_status_code,
                    sc_shipment.status_name_ar as shipment_status_name
                FROM cargo_shipment_items csi
                JOIN cargo_shipments cs ON csi.cargo_shipment_id = cs.id
                LEFT JOIN shipment_status_config sc ON csi.item_status = sc.status_code
                LEFT JOIN shipment_status_config sc_shipment ON cs.shipment_status = sc_shipment.status_code
                WHERE csi.cargo_shipment_id = :1
                ORDER BY csi.id
            """
            
            results = self.db.execute_query(query, [shipment_id])
            
            items = []
            for row in results:
                items.append({
                    'id': row[0],
                    'item_name': row[1],
                    'quantity': row[2],
                    'unit': row[3],
                    'item_status': row[4],
                    'item_location': row[5],
                    'status_updated_at': row[6],
                    'tracking_notes': row[7],
                    'is_damaged': bool(row[8]),
                    'damage_notes': row[9],
                    'status_name': row[10],
                    'status_color': row[11],
                    'status_icon': row[12],
                    'shipment_status_code': row[13],
                    'shipment_status_name': row[14]
                })
            
            return items
            
        except Exception as e:
            logger.error(f"خطأ في جلب أصناف الشحنة: {e}")
            return []
    
    def get_item_status_history(self, item_id):
        """الحصول على تاريخ حالات الصنف"""
        try:
            query = """
                SELECT 
                    ish.old_status,
                    ish.new_status,
                    ish.status_date,
                    ish.notes,
                    ish.location,
                    ish.auto_updated,
                    sc_old.status_name_ar as old_status_name,
                    sc_old.status_color as old_status_color,
                    sc_new.status_name_ar as new_status_name,
                    sc_new.status_color as new_status_color,
                    sc_new.status_icon as new_status_icon
                FROM item_status_history ish
                LEFT JOIN shipment_status_config sc_old ON ish.old_status = sc_old.status_code
                LEFT JOIN shipment_status_config sc_new ON ish.new_status = sc_new.status_code
                WHERE ish.shipment_item_id = :1
                ORDER BY ish.status_date DESC
            """
            
            results = self.db.execute_query(query, [item_id])
            
            history = []
            for row in results:
                history.append({
                    'old_status': row[0],
                    'new_status': row[1],
                    'status_date': row[2],
                    'notes': row[3],
                    'location': row[4],
                    'auto_updated': bool(row[5]),
                    'old_status_name': row[6],
                    'old_status_color': row[7],
                    'new_status_name': row[8],
                    'new_status_color': row[9],
                    'new_status_icon': row[10]
                })
            
            return history
            
        except Exception as e:
            logger.error(f"خطأ في جلب تاريخ الصنف: {e}")
            return []
    
    def update_item_status_manual(self, item_id, new_status, location=None, notes=None, user_id=None):
        """تحديث حالة صنف يدوياً"""
        try:
            # استخدام الـ procedure المُنشأ
            query = """
                BEGIN
                    update_item_status(
                        p_item_id => :1,
                        p_new_status => :2,
                        p_location => :3,
                        p_notes => :4,
                        p_user_id => :5
                    );
                END;
            """
            
            self.db.execute_update(query, [item_id, new_status, location, notes, user_id])
            
            logger.info(f"تم تحديث حالة الصنف {item_id} إلى {new_status}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحديث حالة الصنف: {e}")
            return False
    
    def get_shipment_items_stats(self, shipment_id):
        """الحصول على إحصائيات أصناف الشحنة"""
        try:
            query = """
                SELECT 
                    sc.status_name_ar,
                    sc.status_color,
                    sc.status_icon,
                    COUNT(csi.id) as item_count,
                    ROUND(COUNT(csi.id) * 100.0 / SUM(COUNT(csi.id)) OVER(), 2) as percentage
                FROM cargo_shipment_items csi
                LEFT JOIN shipment_status_config sc ON csi.item_status = sc.status_code
                WHERE csi.cargo_shipment_id = :1
                GROUP BY sc.status_name_ar, sc.status_color, sc.status_icon, sc.status_order
                ORDER BY sc.status_order
            """
            
            results = self.db.execute_query(query, [shipment_id])
            
            stats = []
            for row in results:
                stats.append({
                    'status_name': row[0],
                    'status_color': row[1],
                    'status_icon': row[2],
                    'item_count': row[3],
                    'percentage': row[4]
                })
            
            return stats
            
        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات الأصناف: {e}")
            return []
    
    def mark_item_damaged(self, item_id, damage_notes, user_id=None):
        """تمييز صنف كتالف"""
        try:
            query = """
                UPDATE cargo_shipment_items
                SET is_damaged = 1,
                    damage_notes = :1,
                    item_status = 'damaged',
                    status_updated_at = CURRENT_TIMESTAMP,
                    status_updated_by = :2
                WHERE id = :3
            """
            
            self.db.execute_update(query, [damage_notes, user_id, item_id])
            
            # إضافة سجل في التاريخ
            self.update_item_status_manual(item_id, 'damaged', notes=f"تم تمييز الصنف كتالف: {damage_notes}", user_id=user_id)
            
            logger.info(f"تم تمييز الصنف {item_id} كتالف")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تمييز الصنف كتالف: {e}")
            return False
    
    def get_status_mapping(self):
        """الحصول على قواعد ربط الحالات"""
        try:
            query = """
                SELECT 
                    sm.shipment_status,
                    sm.item_status,
                    sm.auto_update,
                    sc_shipment.status_name_ar as shipment_status_name,
                    sc_item.status_name_ar as item_status_name
                FROM shipment_item_status_mapping sm
                LEFT JOIN shipment_status_config sc_shipment ON sm.shipment_status = sc_shipment.status_code
                LEFT JOIN shipment_status_config sc_item ON sm.item_status = sc_item.status_code
                ORDER BY sm.priority_order
            """
            
            results = self.db.execute_query(query)
            
            mappings = []
            for row in results:
                mappings.append({
                    'shipment_status': row[0],
                    'item_status': row[1],
                    'auto_update': bool(row[2]),
                    'shipment_status_name': row[3],
                    'item_status_name': row[4]
                })
            
            return mappings
            
        except Exception as e:
            logger.error(f"خطأ في جلب قواعد الربط: {e}")
            return []
    
    def update_status_mapping(self, shipment_status, item_status, auto_update=True):
        """تحديث قاعدة ربط الحالات"""
        try:
            query = """
                MERGE INTO shipment_item_status_mapping sm
                USING (SELECT :1 as shipment_status, :2 as item_status FROM dual) src
                ON (sm.shipment_status = src.shipment_status)
                WHEN MATCHED THEN
                    UPDATE SET item_status = src.item_status, auto_update = :3
                WHEN NOT MATCHED THEN
                    INSERT (id, shipment_status, item_status, auto_update)
                    VALUES (shipment_item_status_mapping_seq.NEXTVAL, src.shipment_status, src.item_status, :3)
            """
            
            self.db.execute_update(query, [shipment_status, item_status, 1 if auto_update else 0])
            
            logger.info(f"تم تحديث قاعدة الربط: {shipment_status} -> {item_status}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحديث قاعدة الربط: {e}")
            return False
    
    def get_items_by_status(self, status, limit=None):
        """الحصول على الأصناف حسب الحالة"""
        try:
            base_query = """
                SELECT
                    csi.id,
                    csi.item_name,
                    csi.quantity,
                    cs.shipment_number,
                    csi.item_location,
                    csi.status_updated_at
                FROM cargo_shipment_items csi
                JOIN cargo_shipments cs ON csi.cargo_shipment_id = cs.id
                WHERE csi.item_status = :1
                ORDER BY csi.status_updated_at DESC
            """

            if limit:
                query = f"{base_query} FETCH FIRST {limit} ROWS ONLY"
            else:
                query = base_query

            results = self.db.execute_query(query, [status])

            items = []
            for row in results:
                items.append({
                    'id': row[0],
                    'item_name': row[1],
                    'quantity': row[2],
                    'shipment_number': row[3],
                    'location': row[4],
                    'updated_at': row[5]
                })

            return items

        except Exception as e:
            logger.error(f"خطأ في جلب الأصناف حسب الحالة: {e}")
            return []

    def get_dashboard_stats(self):
        """الحصول على إحصائيات اللوحة الرئيسية"""
        try:
            # إحصائيات عامة
            general_stats_query = """
                SELECT
                    sc.status_name_ar,
                    sc.status_color,
                    sc.status_icon,
                    COUNT(csi.id) as item_count,
                    ROUND(COUNT(csi.id) * 100.0 / SUM(COUNT(csi.id)) OVER(), 2) as percentage
                FROM cargo_shipment_items csi
                LEFT JOIN shipment_status_config sc ON csi.item_status = sc.status_code
                WHERE csi.item_status IS NOT NULL
                GROUP BY sc.status_name_ar, sc.status_color, sc.status_icon, sc.status_order
                ORDER BY sc.status_order
            """

            general_stats = self.db.execute_query(general_stats_query)

            # إحصائيات الشحنات
            shipments_stats_query = """
                SELECT
                    COUNT(DISTINCT cs.id) as total_shipments,
                    COUNT(DISTINCT CASE WHEN csi.item_status = 'delivered' THEN cs.id END) as completed_shipments,
                    COUNT(DISTINCT CASE WHEN csi.item_status IN ('in_transit', 'arrived_port', 'customs_clearance') THEN cs.id END) as active_shipments,
                    COUNT(DISTINCT CASE WHEN csi.is_damaged = 1 THEN cs.id END) as shipments_with_damage,
                    COUNT(csi.id) as total_items,
                    COUNT(CASE WHEN csi.is_damaged = 1 THEN 1 END) as damaged_items
                FROM cargo_shipments cs
                LEFT JOIN cargo_shipment_items csi ON cs.id = csi.cargo_shipment_id
                WHERE cs.created_at >= SYSDATE - 90
            """

            shipments_stats = self.db.execute_query(shipments_stats_query)

            return {
                'general_stats': general_stats,
                'shipments_stats': shipments_stats[0] if shipments_stats else None
            }

        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات اللوحة: {e}")
            return {
                'general_stats': [],
                'shipments_stats': None
            }

    def get_recent_activities(self, limit=50):
        """الحصول على الأنشطة الحديثة"""
        try:
            query = """
                SELECT
                    ish.shipment_item_id,
                    csi.item_name,
                    ish.old_status,
                    ish.new_status,
                    ish.status_date,
                    ish.notes,
                    ish.auto_updated,
                    cs.shipment_number,
                    cs.id as shipment_id,
                    sc_old.status_name_ar as old_status_name,
                    sc_new.status_name_ar as new_status_name,
                    sc_new.status_color as new_status_color,
                    sc_new.status_icon as new_status_icon
                FROM item_status_history ish
                JOIN cargo_shipment_items csi ON ish.shipment_item_id = csi.id
                JOIN cargo_shipments cs ON csi.cargo_shipment_id = cs.id
                LEFT JOIN shipment_status_config sc_old ON ish.old_status = sc_old.status_code
                LEFT JOIN shipment_status_config sc_new ON ish.new_status = sc_new.status_code
                WHERE ish.status_date >= SYSDATE - 7
                ORDER BY ish.status_date DESC
                FETCH FIRST :1 ROWS ONLY
            """

            results = self.db.execute_query(query, [limit])

            activities = []
            for row in results:
                activities.append({
                    'item_id': row[0],
                    'item_name': row[1],
                    'old_status': row[2],
                    'new_status': row[3],
                    'status_date': row[4],
                    'notes': row[5],
                    'auto_updated': bool(row[6]),
                    'shipment_number': row[7],
                    'shipment_id': row[8],
                    'old_status_name': row[9],
                    'new_status_name': row[10],
                    'new_status_color': row[11],
                    'new_status_icon': row[12]
                })

            return activities

        except Exception as e:
            logger.error(f"خطأ في جلب الأنشطة الحديثة: {e}")
            return []
    
    def close(self):
        """إغلاق الاتصال"""
        if self.db:
            self.db.close()

# إنشاء instance عام
item_tracker = ItemTracker()
