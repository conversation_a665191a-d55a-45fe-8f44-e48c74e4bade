{% extends "base.html" %}

{% block title %}لوحة تتبع الشحنات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-map-marked-alt me-2 text-primary"></i>
                        لوحة تتبع الشحنات
                    </h2>
                    <p class="text-muted mb-0">تتبع ومراقبة جميع الشحنات في الوقت الفعلي</p>
                </div>
                <div>
                    <button class="btn btn-outline-info me-2" onclick="toggleSidebar()" id="sidebarToggle">
                        <i class="fas fa-sidebar me-1" id="sidebarIcon"></i>
                        <span id="sidebarText">إخفاء الشريط</span>
                    </button>
                    <button class="btn btn-primary me-2" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt me-1"></i>
                        تحديث
                    </button>
                    <button class="btn btn-success" onclick="exportReport()">
                        <i class="fas fa-download me-1"></i>
                        تصدير التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الحالات -->
    <div class="row mb-4">
        {% if status_stats %}
            {% for stat in status_stats %}
            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="mb-2">
                            <i class="{{ stat[3] or 'fas fa-circle' }} fa-2x" style="color: {{ stat[2] or '#6c757d' }}"></i>
                        </div>
                        <h4 class="mb-1" style="color: {{ stat[2] or '#6c757d' }}">{{ stat[4] or 0 }}</h4>
                        <p class="text-muted mb-0 small">{{ stat[1] or 'غير محدد' }}</p>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد إحصائيات متاحة حالياً
                </div>
            </div>
        {% endif %}
    </div>

    <!-- أدوات البحث والفلترة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-search me-2"></i>
                        البحث والفلترة
                        <span class="filter-count text-muted small"></span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- البحث العام -->
                        <div class="col-md-3 mb-3">
                            <label class="form-label">البحث العام</label>
                            <input type="text" class="form-control" id="searchInput"
                                   placeholder="رقم التتبع أو اسم المرسل..."
                                   oninput="applyShipmentFilters()" onkeyup="applyShipmentFilters()">
                        </div>

                        <!-- فلترة بالحالة -->
                        <div class="col-md-3 mb-3">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" id="statusFilter" onchange="applyShipmentFilters()">
                                <option value="">جميع الحالات</option>
                                {% if status_stats %}
                                    {% for stat in status_stats %}
                                    <option value="{{ stat[0] }}">{{ stat[1] or stat[0] }}</option>
                                    {% endfor %}
                                {% endif %}
                            </select>
                        </div>

                        <!-- فلترة بالتاريخ -->
                        <div class="col-md-2 mb-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="dateFromFilter"
                                   onchange="applyShipmentFilters()">
                        </div>

                        <div class="col-md-2 mb-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="dateToFilter"
                                   onchange="applyShipmentFilters()">
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="col-md-2 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="searchShipments()">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="resetShipmentFilters()">
                                    <i class="fas fa-undo me-1"></i>
                                    إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الشحنات الحديثة والمتأخرة -->
    <div class="row">
        <!-- الشحنات الحديثة -->
        <div class="col-lg-8 mb-4" id="mainContent">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        الشحنات الحديثة
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم التتبع</th>
                                    <th>رقم الحجز</th>
                                    <th>نوع البضاعة</th>
                                    <th>ميناء التحميل</th>
                                    <th>ميناء التفريغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if recent_shipments %}
                                    {% for shipment in recent_shipments %}
                                    <tr>
                                        <td>
                                            <strong>{{ shipment[1] or 'غير محدد' }}</strong>
                                            <br>
                                            <small class="text-muted">{{ shipment[10].strftime('%Y-%m-%d') if shipment[10] else 'غير محدد' }}</small>
                                        </td>
                                        <td>
                                            {{ shipment[2] or 'غير محدد' }}
                                            <br>
                                            <small class="text-muted">رقم الحجز</small>
                                        </td>
                                        <td>
                                            {{ shipment[3] or 'غير محدد' }}
                                            <br>
                                            <small class="text-muted">نوع البضاعة</small>
                                        </td>
                                        <td>{{ shipment[4] or 'غير محدد' }}</td>
                                        <td>{{ shipment[5] or 'غير محدد' }}</td>
                                        <td>
                                            <span class="badge" style="background-color: {{ shipment[8] or '#6c757d' }}; color: white;">
                                                <i class="{{ shipment[9] or 'fas fa-circle' }} me-1"></i>
                                                {{ shipment[7] or 'غير محدد' }}
                                            </span>
                                            {% if shipment[12] and shipment[12] > 0 %}
                                            <br>
                                            <span class="badge bg-danger ms-1">
                                                متأخر {{ shipment[12] }} يوم
                                            </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary btn-sm"
                                                        onclick="showTimeline({{ shipment[0] }})">
                                                    <i class="fas fa-history"></i>
                                                </button>
                                                <button class="btn btn-outline-success btn-sm"
                                                        onclick="updateStatus({{ shipment[0] }})">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <i class="fas fa-ship fa-2x text-muted mb-2"></i>
                                            <p class="text-muted mb-0">لا توجد شحنات حديثة</p>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- الشحنات المتأخرة -->
        <div class="col-lg-4 mb-4" id="sidebar">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        شحنات متأخرة
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if delayed_shipments %}
                    <div class="list-group list-group-flush">
                        {% for delayed in delayed_shipments %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ delayed[1] }}</h6>
                                    <p class="mb-1 small text-muted">{{ delayed[2] }} → {{ delayed[3] }}</p>
                                    <small class="text-muted">
                                        موعد التسليم: {{ delayed[4].strftime('%Y-%m-%d') if delayed[4] else 'غير محدد' }}
                                    </small>
                                </div>
                                <span class="badge bg-danger">
                                    {{ delayed[5] }} يوم
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <p class="text-muted">لا توجد شحنات متأخرة</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تحديث الحالة المخصصة -->
<div id="updateStatusOverlay" class="custom-modal-overlay" style="display: none;">
    <div class="custom-modal">
        <div class="custom-modal-header">
            <h5>
                <i class="fas fa-edit me-2"></i>
                تحديث حالة الشحنة
            </h5>
            <button type="button" class="custom-close-btn" onclick="closeUpdateModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="custom-modal-body">
            <div id="updateStatusAlert" class="alert alert-info" style="display: none;">
                <i class="fas fa-info-circle me-2"></i>
                <span id="updateStatusMessage"></span>
            </div>

            <form id="updateStatusForm">
                <input type="hidden" id="shipmentId" name="shipment_id">

                <div class="mb-3">
                    <label for="newStatus" class="form-label">
                        <i class="fas fa-tasks me-1"></i>
                        الحالة الجديدة
                    </label>
                    <select class="form-select" id="newStatus" name="new_status" required>
                        <option value="">اختر الحالة الجديدة</option>
                        <option value="draft">📝 مسودة</option>
                        <option value="confirmed">✅ مؤكدة</option>
                        <option value="in_transit">🚢 قيد الشحن</option>
                        <option value="arrived_port">🏭 وصلت للميناء</option>
                        <option value="customs_clearance">🚚 قيد التخليص</option>
                        <option value="ready_pickup">📦 جاهزة للاستلام</option>
                        <option value="delivered">✅ تم التسليم</option>
                        <option value="cancelled">❌ ملغية</option>
                        <option value="delayed">⚠️ متأخرة</option>
                        <option value="returned">🔄 معادة</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="statusLocation" class="form-label">
                        <i class="fas fa-map-marker-alt me-1"></i>
                        الموقع (اختياري)
                    </label>
                    <input type="text" class="form-control" id="statusLocation" name="location"
                           placeholder="أدخل الموقع الحالي للشحنة">
                </div>

                <div class="mb-3">
                    <label for="statusNotes" class="form-label">
                        <i class="fas fa-sticky-note me-1"></i>
                        ملاحظات
                    </label>
                    <textarea class="form-control" id="statusNotes" name="notes" rows="3"
                              placeholder="أي ملاحظات أو تفاصيل إضافية..."></textarea>
                </div>
            </form>
        </div>

        <div class="custom-modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeUpdateModal()">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </button>
            <button type="button" class="btn btn-primary" id="submitStatusBtn" onclick="submitStatusUpdate()">
                <i class="fas fa-save me-1"></i>
                تحديث الحالة
            </button>
        </div>
    </div>
</div>

<script>
// تحديث اللوحة
function refreshDashboard() {
    location.reload();
}

// تصدير التقرير
function exportReport() {
    alert('تصدير التقرير - قريباً');
}

// تحديث حالة الشحنة - حل بسيط وفعال
function updateStatus(shipmentId) {
    console.log('🔄 فتح نافذة تحديث الحالة للشحنة:', shipmentId);

    // تعيين ID الشحنة
    document.getElementById('shipmentId').value = shipmentId;

    // إعادة تعيين النموذج
    document.getElementById('updateStatusForm').reset();
    document.getElementById('shipmentId').value = shipmentId; // إعادة تعيين بعد reset

    // إخفاء رسائل التنبيه
    const alertDiv = document.getElementById('updateStatusAlert');
    alertDiv.style.display = 'none';

    // عرض النافذة المخصصة
    const overlay = document.getElementById('updateStatusOverlay');
    overlay.style.display = 'flex';

    // إضافة تأثير الظهور
    setTimeout(() => {
        overlay.classList.add('show');
    }, 10);

    // التركيز على حقل الحالة
    setTimeout(() => {
        document.getElementById('newStatus').focus();
    }, 300);

    console.log('✅ تم فتح النافذة بنجاح');
}

// إغلاق نافذة التحديث
function closeUpdateModal() {
    console.log('🔄 إغلاق نافذة التحديث');

    const overlay = document.getElementById('updateStatusOverlay');
    overlay.classList.remove('show');

    // إخفاء النافذة بعد انتهاء التأثير
    setTimeout(() => {
        overlay.style.display = 'none';
    }, 300);

    console.log('✅ تم إغلاق النافذة بنجاح');
}

// إرسال تحديث الحالة
function submitStatusUpdate() {
    console.log('📤 بدء إرسال تحديث الحالة...');

    const form = document.getElementById('updateStatusForm');
    const submitBtn = document.getElementById('submitStatusBtn');
    const alertDiv = document.getElementById('updateStatusAlert');
    const messageSpan = document.getElementById('updateStatusMessage');

    // التحقق من صحة البيانات
    const shipmentId = document.getElementById('shipmentId').value;
    const newStatus = document.getElementById('newStatus').value;

    if (!shipmentId || !newStatus) {
        showAlert('يرجى اختيار الحالة الجديدة', 'warning');
        return;
    }

    // تعطيل الزر أثناء الإرسال
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري التحديث...';

    const formData = new FormData(form);

    console.log('📋 بيانات النموذج:', {
        shipment_id: formData.get('shipment_id'),
        new_status: formData.get('new_status'),
        location: formData.get('location'),
        notes: formData.get('notes')
    });

    fetch('/shipments/api/update-status', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('📡 استجابة الخادم:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('📊 بيانات الاستجابة:', data);

        if (data.success) {
            showAlert('تم تحديث حالة الشحنة بنجاح!', 'success');
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('خطأ: ' + (data.message || 'فشل في تحديث الحالة'), 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الطلب:', error);
        showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
    })
    .finally(() => {
        // إعادة تفعيل الزر
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-save me-1"></i> تحديث الحالة';
    });
}

// عرض رسائل التنبيه
function showAlert(message, type) {
    const alertDiv = document.getElementById('updateStatusAlert');
    const messageSpan = document.getElementById('updateStatusMessage');

    alertDiv.className = `alert alert-${type}`;
    messageSpan.textContent = message;
    alertDiv.style.display = 'block';

    // إخفاء الرسالة تلقائياً بعد 5 ثوان للرسائل الناجحة
    if (type === 'success') {
        setTimeout(() => {
            alertDiv.style.display = 'none';
        }, 5000);
    }
}

// تحديث تلقائي كل 30 ثانية
setInterval(refreshDashboard, 30000);

// التأكد من تحميل الصفحة وإعداد الأحداث
document.addEventListener('DOMContentLoaded', function() {
    console.log('✅ تم تحميل الصفحة بالكامل');

    // إضافة حدث الإغلاق عند النقر خارج النافذة
    const overlay = document.getElementById('updateStatusOverlay');
    if (overlay) {
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                closeUpdateModal();
            }
        });
    }

    // إضافة حدث الإغلاق بمفتاح Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const overlay = document.getElementById('updateStatusOverlay');
            if (overlay && overlay.style.display !== 'none') {
                closeUpdateModal();
            }
        }
    });

    console.log('✅ تم إعداد أحداث النافذة المخصصة');
});
</script>

<style>
/* النافذة المخصصة لتحديث الحالة */
.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.custom-modal-overlay.show {
    opacity: 1;
}

.custom-modal {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.7);
    transition: transform 0.3s ease;
}

.custom-modal-overlay.show .custom-modal {
    transform: scale(1);
}

.custom-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #dee2e6;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.custom-modal-header h5 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.custom-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.custom-close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.custom-modal-body {
    padding: 1.5rem;
}

.custom-modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

/* تحسين مظهر النموذج */
.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-select, .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus, .form-control:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* تحسين الأزرار */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

/* تحسين التنبيهات */
.alert {
    border-radius: 6px;
    border: none;
}

/* 📱 تحسين زر إخفاء/إظهار الشريط الجانبي */
#sidebarToggle {
    border-radius: 25px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    background: linear-gradient(135deg, #ffffff 0%, #e0f7fa 100%) !important;
    border: 2px solid #00bcd4 !important;
    color: #00838f !important;
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.2) !important;
}

#sidebarToggle:hover {
    background: linear-gradient(135deg, #00bcd4 0%, #00838f 100%) !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 6px 18px rgba(0, 188, 212, 0.4) !important;
}

/* تأثيرات إخفاء/إظهار الشريط الجانبي */
#sidebar {
    transition: all 0.4s ease !important;
    transform-origin: right center !important;
}

#sidebar.hidden {
    transform: translateX(100%) !important;
    opacity: 0 !important;
    visibility: hidden !important;
    width: 0 !important;
    min-width: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
}

#mainContent {
    transition: all 0.4s ease !important;
}

#mainContent.expanded {
    flex: 0 0 100% !important;
    max-width: 100% !important;
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 992px) {
    #sidebar.hidden {
        display: none !important;
    }

    #mainContent.expanded {
        flex: 0 0 100% !important;
        max-width: 100% !important;
    }
}

/* تأثير الأيقونة */
#sidebarIcon {
    transition: transform 0.3s ease !important;
}

#sidebarToggle.collapsed #sidebarIcon {
    transform: rotate(180deg) !important;
}
</style>

<script>
// 📱 وظائف إخفاء/إظهار الشريط الجانبي
let sidebarVisible = true;

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    const toggleBtn = document.getElementById('sidebarToggle');
    const toggleText = document.getElementById('sidebarText');
    const toggleIcon = document.getElementById('sidebarIcon');

    if (sidebarVisible) {
        // إخفاء الشريط الجانبي
        sidebar.classList.add('hidden');
        mainContent.classList.add('expanded');
        toggleBtn.classList.add('collapsed');
        toggleText.textContent = 'إظهار الشريط';
        toggleIcon.className = 'fas fa-eye me-1';

        // حفظ الحالة
        localStorage.setItem('sidebarVisible', 'false');
        sidebarVisible = false;

        console.log('✅ تم إخفاء الشريط الجانبي');
    } else {
        // إظهار الشريط الجانبي
        sidebar.classList.remove('hidden');
        mainContent.classList.remove('expanded');
        toggleBtn.classList.remove('collapsed');
        toggleText.textContent = 'إخفاء الشريط';
        toggleIcon.className = 'fas fa-eye-slash me-1';

        // حفظ الحالة
        localStorage.setItem('sidebarVisible', 'true');
        sidebarVisible = true;

        console.log('✅ تم إظهار الشريط الجانبي');
    }
}

// استعادة حالة الشريط الجانبي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const savedState = localStorage.getItem('sidebarVisible');

    if (savedState === 'false') {
        // إخفاء الشريط الجانبي إذا كان مخفي سابقاً
        setTimeout(() => {
            toggleSidebar();
        }, 100);
    }

    console.log('📱 تم تحميل لوحة التتبع مع إمكانية إخفاء/إظهار الشريط الجانبي');

    // تفعيل فلاتر البحث
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const dateFromFilter = document.getElementById('dateFromFilter');
    const dateToFilter = document.getElementById('dateToFilter');

    // إضافة event listeners للفلاتر
    if (searchInput) {
        searchInput.addEventListener('input', applyShipmentFilters);
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchShipments();
            }
        });
        console.log('✅ تم تفعيل البحث النصي للشحنات');
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', applyShipmentFilters);
        console.log('✅ تم تفعيل فلترة الحالة للشحنات');
    }

    if (dateFromFilter) {
        dateFromFilter.addEventListener('change', applyShipmentFilters);
        console.log('✅ تم تفعيل فلترة التاريخ من');
    }

    if (dateToFilter) {
        dateToFilter.addEventListener('change', applyShipmentFilters);
        console.log('✅ تم تفعيل فلترة التاريخ إلى');
    }

    // إضافة مؤشرات بصرية للفلاتر النشطة
    function updateShipmentFilterIndicators() {
        const filters = [searchInput, statusFilter, dateFromFilter, dateToFilter];
        let activeFilters = 0;

        filters.forEach(filter => {
            if (filter && filter.value.trim()) {
                filter.style.borderColor = '#28a745';
                filter.style.boxShadow = '0 0 0 0.2rem rgba(40, 167, 69, 0.25)';
                activeFilters++;
            } else if (filter) {
                filter.style.borderColor = '';
                filter.style.boxShadow = '';
            }
        });

        // تحديث عداد الفلاتر النشطة
        const filterCount = document.querySelector('.filter-count');
        if (filterCount) {
            filterCount.textContent = activeFilters > 0 ? `(${activeFilters} فلتر نشط)` : '';
        }
    }

    // تطبيق مؤشرات الفلاتر عند التغيير
    [searchInput, statusFilter, dateFromFilter, dateToFilter].forEach(filter => {
        if (filter) {
            filter.addEventListener('input', updateShipmentFilterIndicators);
            filter.addEventListener('change', updateShipmentFilterIndicators);
        }
    });

    console.log('🎉 تم تهيئة جميع فلاتر الشحنات بنجاح');
});

// وظائف أخرى موجودة
function refreshDashboard() {
    console.log('🔄 تحديث لوحة التتبع...');
    location.reload();
}

function exportReport() {
    console.log('📊 تصدير التقرير...');
    alert('سيتم تطوير ميزة تصدير التقرير قريباً');
}

function showTimeline(shipmentId) {
    console.log('📅 عرض الجدول الزمني للشحنة:', shipmentId);
    alert('سيتم تطوير ميزة الجدول الزمني قريباً');
}

function updateStatus(shipmentId) {
    console.log('✏️ تحديث حالة الشحنة:', shipmentId);
    alert('سيتم تطوير ميزة تحديث الحالة قريباً');
}

// 🔍 وظائف البحث والفلترة للشحنات
function applyShipmentFilters() {
    // تأخير قصير لتحسين الأداء
    clearTimeout(window.shipmentFilterTimeout);
    window.shipmentFilterTimeout = setTimeout(() => {
        searchShipments();
    }, 300);
}

function searchShipments() {
    const searchTerm = document.getElementById('searchInput').value.trim();
    const statusFilter = document.getElementById('statusFilter').value;
    const dateFromFilter = document.getElementById('dateFromFilter').value;
    const dateToFilter = document.getElementById('dateToFilter').value;

    console.log('🔍 بحث الشحنات:', {
        searchTerm: searchTerm,
        statusFilter: statusFilter,
        dateFromFilter: dateFromFilter,
        dateToFilter: dateToFilter
    });

    // إظهار مؤشر التحميل
    const searchBtn = document.querySelector('button[onclick="searchShipments()"]');
    const originalText = searchBtn.innerHTML;
    searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري البحث...';
    searchBtn.disabled = true;

    const params = new URLSearchParams();
    if (searchTerm) params.append('q', searchTerm);
    if (statusFilter) params.append('status', statusFilter);
    if (dateFromFilter) params.append('date_from', dateFromFilter);
    if (dateToFilter) params.append('date_to', dateToFilter);

    fetch(`/shipments/api/tracking-search?${params.toString()}`)
    .then(response => response.json())
    .then(data => {
        console.log('📊 نتائج البحث:', data);

        if (data.success) {
            displayShipmentResults(data.shipments, data.count);

            // إظهار رسالة النجاح
            if (data.count === 0) {
                showShipmentMessage('لا توجد شحنات تطابق معايير البحث', 'warning');
            } else {
                showShipmentMessage(`تم العثور على ${data.count} شحنة`, 'success');
            }
        } else {
            console.error('❌ خطأ في البحث:', data.message);
            showShipmentMessage('خطأ في البحث: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الشبكة:', error);
        showShipmentMessage('حدث خطأ في الاتصال بالخادم', 'danger');
    })
    .finally(() => {
        // إعادة تعيين زر البحث
        searchBtn.innerHTML = originalText;
        searchBtn.disabled = false;
    });
}

function displayShipmentResults(shipments, count) {
    console.log('📋 عرض نتائج الشحنات:', count, 'شحنة');

    // تحديث العنوان
    const titleElement = document.querySelector('#mainContent .card-header h5');
    if (titleElement) {
        titleElement.innerHTML = `
            <i class="fas fa-search me-2"></i>
            نتائج البحث
            <span class="badge bg-primary ms-2">${count}</span>
        `;
    }

    // تحديث محتوى الجدول
    const tableBody = document.querySelector('#mainContent .table tbody');
    if (!tableBody) {
        console.error('❌ لم يتم العثور على جدول الشحنات');
        return;
    }

    tableBody.innerHTML = '';

    if (count === 0) {
        // عرض رسالة عدم وجود نتائج
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <h5>لا توجد شحنات تطابق معايير البحث</h5>
                        <p>جرب تغيير معايير البحث أو إعادة تعيين الفلاتر</p>
                        <button class="btn btn-outline-primary" onclick="resetShipmentFilters()">
                            <i class="fas fa-undo me-1"></i>
                            إعادة تعيين الفلاتر
                        </button>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // عرض النتائج مع ترتيب صحيح للأعمدة
    shipments.forEach(shipment => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <strong>${shipment.tracking_number}</strong>
                <br>
                <small class="text-muted">${shipment.created_at}</small>
            </td>
            <td>
                ${shipment.booking_number || 'غير محدد'}
                <br>
                <small class="text-muted">رقم الحجز</small>
            </td>
            <td>
                ${shipment.cargo_type || 'غير محدد'}
                <br>
                <small class="text-muted">نوع البضاعة</small>
            </td>
            <td>${shipment.port_of_loading || 'غير محدد'}</td>
            <td>${shipment.port_of_discharge || 'غير محدد'}</td>
            <td>
                <span class="badge" style="background-color: ${shipment.status_color};">
                    <i class="${shipment.status_icon} me-1"></i>
                    ${shipment.status_name}
                </span>
                <br>
                <small class="text-muted">آخر تحديث: ${shipment.last_update || 'غير محدد'}</small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="showTimeline('${shipment.tracking_number}')" title="الجدول الزمني">
                        <i class="fas fa-history"></i>
                    </button>
                    <button class="btn btn-outline-success" onclick="updateStatus('${shipment.tracking_number}')" title="تحديث الحالة">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

function resetShipmentFilters() {
    console.log('🔄 إعادة تعيين فلاتر الشحنات');

    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('dateFromFilter').value = '';
    document.getElementById('dateToFilter').value = '';

    // إعادة تحميل الصفحة لعرض البيانات الأصلية
    location.reload();
}

function showShipmentMessage(message, type = 'info') {
    // إزالة الرسائل السابقة
    const existingAlerts = document.querySelectorAll('.shipment-search-alert');
    existingAlerts.forEach(alert => alert.remove());

    // إنشاء رسالة جديدة
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show shipment-search-alert`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إضافة الرسالة قبل جدول الشحنات
    const mainContent = document.getElementById('mainContent');
    if (mainContent) {
        const cardBody = mainContent.querySelector('.card-body');
        if (cardBody) {
            cardBody.insertBefore(alertDiv, cardBody.firstChild);

            // إزالة الرسالة تلقائياً بعد 5 ثوان
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
    }
}
</script>

{% endblock %}
