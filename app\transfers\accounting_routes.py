"""
Flask Routes للترحيل المحاسبي للحوالات
Transfer Accounting Routes
"""

from flask import Blueprint, request, jsonify, session, current_app
from decimal import Decimal
import logging
import json

from app.transfers.accounting_service import transfer_accounting
from flask_login import login_required
from app.utils.exceptions import ValidationError, DatabaseError

logger = logging.getLogger(__name__)

def arabic_jsonify(data, status_code=200):
    """
    دالة مساعدة لإرجاع JSON مع دعم صحيح للنصوص العربية
    """
    from flask import Response
    import json

    # تحويل البيانات إلى JSON مع دعم العربية
    json_string = json.dumps(data, ensure_ascii=False, indent=2)

    # إنشاء استجابة مع ترميز UTF-8 صحيح
    response = Response(
        json_string,
        status=status_code,
        mimetype='application/json',
        headers={'Content-Type': 'application/json; charset=utf-8'}
    )

    return response

# إنشاء Blueprint
accounting_bp = Blueprint('transfer_accounting', __name__, url_prefix='/transfers/accounting')

@accounting_bp.route('/validate', methods=['POST'])
@login_required
def validate_transfer_data():
    """
    التحقق من صحة بيانات الحوالة قبل التنفيذ

    Expected JSON:
    {
        "transfer_id": 123,
        "money_changer_id": 456,
        "total_amount": 10000.00,
        "currency_code": "SAR",
        "supplier_distributions": [
            {"supplier_id": 1, "amount": 5000.00},
            {"supplier_id": 2, "amount": 5000.00}
        ]
    }
    """
    try:
        data = request.get_json()

        # التحقق من وجود البيانات المطلوبة
        required_fields = ['transfer_id', 'money_changer_id', 'total_amount', 'supplier_distributions']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'message': f'الحقل {field} مطلوب'
                }), 400

        # استخراج البيانات
        transfer_id = int(data['transfer_id'])
        money_changer_id = int(data['money_changer_id'])
        total_amount = Decimal(str(data['total_amount']))
        currency_code = data.get('currency_code', 'SAR')
        supplier_distributions = data['supplier_distributions']

        # التحقق من توزيعات الموردين
        validation_result = transfer_accounting.validate_supplier_distributions(
            transfer_id, supplier_distributions
        )

        # التحقق من رصيد الصراف
        balance_check = transfer_accounting.get_money_changer_balance_check(
            money_changer_id, total_amount, currency_code
        )

        return jsonify({
            'success': True,
            'validation': validation_result,
            'balance_check': balance_check,
            'can_execute': validation_result['valid'] and balance_check['sufficient']
        }), 200

    except Exception as e:
        logger.error(f"خطأ في التحقق من البيانات: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في التحقق: {str(e)}'
        }), 500

@accounting_bp.route('/execute', methods=['POST'])
@login_required
def execute_transfer():
    """
    تنفيذ الحوالة وترحيل الأرصدة

    Expected JSON:
    {
        "transfer_id": 123,
        "money_changer_id": 456,
        "total_amount": 10000.00,
        "currency_code": "SAR",
        "supplier_distributions": [
            {"supplier_id": 1, "amount": 5000.00},
            {"supplier_id": 2, "amount": 5000.00}
        ]
    }
    """
    try:
        data = request.get_json()

        # التحقق من وجود البيانات المطلوبة
        required_fields = ['transfer_id', 'money_changer_id', 'total_amount', 'supplier_distributions']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'message': f'الحقل {field} مطلوب'
                }), 400

        # استخراج البيانات
        transfer_id = int(data['transfer_id'])
        money_changer_id = int(data['money_changer_id'])
        total_amount = Decimal(str(data['total_amount']))
        currency_code = data.get('currency_code', 'SAR')
        supplier_distributions = data['supplier_distributions']

        # تنفيذ الحوالة
        result = transfer_accounting.execute_transfer(
            transfer_id=transfer_id,
            money_changer_id=money_changer_id,
            total_amount=total_amount,
            currency_code=currency_code,
            supplier_distributions=supplier_distributions
        )

        logger.info(f"تم تنفيذ الحوالة {transfer_id} بواسطة المستخدم {session.get('user_id')}")

        return jsonify(result), 200

    except ValidationError as e:
        logger.warning(f"خطأ في التحقق من البيانات: {str(e)}")
        return jsonify({
            'success': False,
            'message': str(e),
            'error_type': 'validation'
        }), 400

    except DatabaseError as e:
        logger.error(f"خطأ في قاعدة البيانات: {str(e)}")
        return jsonify({
            'success': False,
            'message': str(e),
            'error_type': 'database'
        }), 500

    except Exception as e:
        logger.error(f"خطأ غير متوقع في تنفيذ الحوالة: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ غير متوقع',
            'error_type': 'unexpected'
        }), 500

@accounting_bp.route('/check-cancellation/<int:transfer_id>', methods=['GET'])
@login_required
def check_cancellation_eligibility(transfer_id):
    """التحقق من إمكانية إلغاء الحوالة"""
    try:
        result = transfer_accounting.check_cancellation_eligibility(transfer_id)

        return arabic_jsonify({
            'success': True,
            'data': result
        }, 200)

    except Exception as e:
        logger.error(f"خطأ في التحقق من إمكانية إلغاء الحوالة {transfer_id}: {str(e)}")
        return arabic_jsonify({
            'success': False,
            'message': 'حدث خطأ في النظام'
        }, 500)

@accounting_bp.route('/cancel', methods=['POST'])
@login_required
def cancel_transfer():
    """
    إلغاء الحوالة وعكس الترحيلات

    Expected JSON:
    {
        "transfer_id": 123,
        "cancellation_reason": "سبب الإلغاء (اختياري)"
    }
    """
    try:
        data = request.get_json()

        if 'transfer_id' not in data:
            return jsonify({
                'success': False,
                'message': 'معرف الحوالة مطلوب'
            }), 400

        transfer_id = int(data['transfer_id'])
        cancellation_reason = data.get('cancellation_reason')

        # إلغاء الحوالة
        result = transfer_accounting.cancel_transfer(
            transfer_id=transfer_id,
            cancellation_reason=cancellation_reason
        )

        logger.info(f"تم إلغاء الحوالة {transfer_id} بواسطة المستخدم {session.get('user_id')}")

        return jsonify(result), 200

    except ValidationError as e:
        logger.warning(f"خطأ في التحقق من البيانات: {str(e)}")
        return arabic_jsonify({
            'success': False,
            'message': str(e),
            'error_type': 'validation'
        }, 400)

    except DatabaseError as e:
        logger.error(f"خطأ في قاعدة البيانات: {str(e)}")
        return arabic_jsonify({
            'success': False,
            'message': str(e),
            'error_type': 'database'
        }, 500)

    except Exception as e:
        logger.error(f"خطأ غير متوقع في إلغاء الحوالة: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ غير متوقع',
            'error_type': 'unexpected'
        }), 500

@accounting_bp.route('/details/<int:transfer_id>', methods=['GET'])
@login_required
def get_transfer_details(transfer_id):
    """الحصول على تفاصيل الترحيل المحاسبي للحوالة"""
    try:
        details = transfer_accounting.get_transfer_accounting_details(transfer_id)
        
        return jsonify({
            'success': True,
            'data': details
        }), 200
        
    except ValidationError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400
        
    except Exception as e:
        logger.error(f"خطأ في الحصول على تفاصيل الحوالة {transfer_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في النظام'
        }), 500

@accounting_bp.route('/balance/<entity_type>/<int:entity_id>', methods=['GET'])
@login_required
def get_entity_balance(entity_type, entity_id):
    """الحصول على رصيد كيان معين"""
    try:
        # التحقق من صحة نوع الكيان
        valid_types = ['SUPPLIER', 'MONEY_CHANGER', 'BANK']
        if entity_type.upper() not in valid_types:
            return jsonify({
                'success': False,
                'message': f'نوع الكيان غير صحيح. الأنواع المسموحة: {", ".join(valid_types)}'
            }), 400
        
        currency_code = request.args.get('currency', 'SAR')
        
        balance = transfer_accounting.get_entity_balance(
            entity_type=entity_type.upper(),
            entity_id=entity_id,
            currency_code=currency_code
        )
        
        return jsonify({
            'success': True,
            'data': balance
        }), 200
        
    except Exception as e:
        logger.error(f"خطأ في الحصول على رصيد {entity_type}-{entity_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في النظام'
        }), 500

@accounting_bp.route('/summary/supplier/<int:supplier_id>', methods=['GET'])
@login_required
def get_supplier_summary(supplier_id):
    """الحصول على ملخص حوالات مورد معين"""
    try:
        # يمكن إضافة المزيد من المعايير كـ query parameters
        currency = request.args.get('currency', 'SAR')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        # هنا يمكن إضافة استعلام مخصص للملخص
        # مؤقتاً سنستخدم الرصيد الحالي
        balance = transfer_accounting.get_entity_balance(
            entity_type='SUPPLIER',
            entity_id=supplier_id,
            currency_code=currency
        )
        
        return jsonify({
            'success': True,
            'data': {
                'supplier_id': supplier_id,
                'currency': currency,
                'current_balance': balance,
                'period': {
                    'from': date_from,
                    'to': date_to
                }
            }
        }), 200
        
    except Exception as e:
        logger.error(f"خطأ في الحصول على ملخص المورد {supplier_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في النظام'
        }), 500

@accounting_bp.route('/summary/money-changer/<int:money_changer_id>', methods=['GET'])
@login_required
def get_money_changer_summary(money_changer_id):
    """الحصول على ملخص حوالات صراف معين"""
    try:
        currency = request.args.get('currency', 'SAR')

        balance = transfer_accounting.get_entity_balance(
            entity_type='MONEY_CHANGER',
            entity_id=money_changer_id,
            currency_code=currency
        )

        return jsonify({
            'success': True,
            'data': {
                'money_changer_id': money_changer_id,
                'currency': currency,
                'current_balance': balance
            }
        }), 200

    except Exception as e:
        logger.error(f"خطأ في الحصول على ملخص الصراف {money_changer_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في النظام'
        }), 500

@accounting_bp.route('/balance-check', methods=['POST'])
@login_required
def check_money_changer_balance():
    """
    التحقق من رصيد الصراف

    Expected JSON:
    {
        "money_changer_id": 123,
        "amount": 10000.00,
        "currency_code": "SAR"
    }
    """
    try:
        data = request.get_json()

        required_fields = ['money_changer_id', 'amount']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'message': f'الحقل {field} مطلوب'
                }), 400

        money_changer_id = int(data['money_changer_id'])
        amount = Decimal(str(data['amount']))
        currency_code = data.get('currency_code', 'SAR')

        balance_check = transfer_accounting.get_money_changer_balance_check(
            money_changer_id, amount, currency_code
        )

        return jsonify({
            'success': True,
            'data': balance_check
        }), 200

    except Exception as e:
        logger.error(f"خطأ في التحقق من رصيد الصراف: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في النظام'
        }), 500

@accounting_bp.route('/activity-log/<int:transfer_id>', methods=['GET'])
@login_required
def get_transfer_activity_log(transfer_id):
    """الحصول على سجل أنشطة الحوالة"""
    try:
        # الحصول على سجل الأنشطة
        query = """
            SELECT * FROM transfer_activity_log_view
            WHERE transfer_id = :transfer_id
            ORDER BY created_at DESC
        """

        activities = transfer_accounting.db.fetch_all(query, {'transfer_id': transfer_id})

        return jsonify({
            'success': True,
            'data': {
                'transfer_id': transfer_id,
                'activities': activities,
                'total_count': len(activities)
            }
        }), 200

    except Exception as e:
        logger.error(f"خطأ في الحصول على سجل أنشطة الحوالة {transfer_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في النظام'
        }), 500

@accounting_bp.route('/distributions/<int:transfer_id>', methods=['GET'])
@login_required
def get_transfer_distributions(transfer_id):
    """الحصول على توزيعات الحوالة"""
    try:
        # الحصول على توزيعات الموردين
        query = """
            SELECT * FROM transfer_supplier_distributions_view
            WHERE transfer_id = :transfer_id
            ORDER BY amount DESC
        """

        distributions = transfer_accounting.db.fetch_all(query, {'transfer_id': transfer_id})

        # حساب الإحصائيات
        total_amount = sum(float(d.get('amount', 0)) for d in distributions)

        return jsonify({
            'success': True,
            'data': {
                'transfer_id': transfer_id,
                'distributions': distributions,
                'summary': {
                    'total_suppliers': len(distributions),
                    'total_amount': total_amount,
                    'currency': distributions[0].get('currency_code', 'SAR') if distributions else 'SAR'
                }
            }
        }), 200

    except Exception as e:
        logger.error(f"خطأ في الحصول على توزيعات الحوالة {transfer_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في النظام'
        }), 500

@accounting_bp.route('/comprehensive-validation', methods=['POST'])
@login_required
def comprehensive_validation():
    """
    التحقق الشامل قبل تنفيذ الحوالة

    Expected JSON:
    {
        "transfer_id": 123,
        "money_changer_id": 456,
        "total_amount": 10000.00,
        "currency_code": "SAR",
        "supplier_distributions": [
            {"supplier_id": 1, "amount": 5000.00},
            {"supplier_id": 2, "amount": 5000.00}
        ]
    }
    """
    try:
        data = request.get_json()

        # التحقق من وجود البيانات المطلوبة
        required_fields = ['transfer_id', 'money_changer_id', 'total_amount', 'supplier_distributions']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'message': f'الحقل {field} مطلوب'
                }), 400

        # استخراج البيانات
        transfer_id = int(data['transfer_id'])
        money_changer_id = int(data['money_changer_id'])
        total_amount = Decimal(str(data['total_amount']))
        currency_code = data.get('currency_code', 'SAR')
        supplier_distributions = data['supplier_distributions']

        # التحقق الشامل
        validation_result = transfer_accounting.comprehensive_validation(
            transfer_id, money_changer_id, total_amount, currency_code, supplier_distributions
        )

        # التحقق من حدود الرصيد
        balance_limits = transfer_accounting.check_minimum_balance_limits(
            money_changer_id, currency_code
        )

        return jsonify({
            'success': True,
            'validation': validation_result,
            'balance_limits': balance_limits,
            'can_execute': validation_result['valid'] and balance_limits['level'] != 'critical'
        }), 200

    except Exception as e:
        logger.error(f"خطأ في التحقق الشامل: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في التحقق: {str(e)}'
        }), 500

@accounting_bp.route('/balance-limits/<int:money_changer_id>', methods=['GET'])
@login_required
def check_balance_limits(money_changer_id):
    """التحقق من حدود الرصيد الأدنى للصراف"""
    try:
        currency = request.args.get('currency', 'SAR')

        balance_limits = transfer_accounting.check_minimum_balance_limits(
            money_changer_id, currency
        )

        return jsonify({
            'success': True,
            'data': balance_limits
        }), 200

    except Exception as e:
        logger.error(f"خطأ في التحقق من حدود الرصيد: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في النظام'
        }), 500

@accounting_bp.route('/balances-summary', methods=['GET'])
@login_required
def get_balances_summary():
    """الحصول على ملخص شامل للأرصدة"""
    try:
        entity_type = request.args.get('entity_type')
        currency = request.args.get('currency')

        # التحقق من صحة نوع الكيان
        if entity_type and entity_type.upper() not in ['SUPPLIER', 'MONEY_CHANGER', 'BANK']:
            return jsonify({
                'success': False,
                'message': 'نوع الكيان غير صحيح'
            }), 400

        balances = transfer_accounting.get_balances_summary(
            entity_type.upper() if entity_type else None,
            currency
        )

        # حساب الإحصائيات
        total_entities = len(balances)
        total_balance = sum(float(b.get('CURRENT_BALANCE', 0)) for b in balances)

        # تجميع حسب نوع الكيان
        by_entity_type = {}
        for balance in balances:
            entity_type = balance.get('ENTITY_TYPE_CODE', 'UNKNOWN')
            if entity_type not in by_entity_type:
                by_entity_type[entity_type] = {
                    'count': 0,
                    'total_balance': 0,
                    'entities': []
                }

            by_entity_type[entity_type]['count'] += 1
            by_entity_type[entity_type]['total_balance'] += float(balance.get('CURRENT_BALANCE', 0))
            by_entity_type[entity_type]['entities'].append(balance)

        return jsonify({
            'success': True,
            'data': {
                'balances': balances,
                'summary': {
                    'total_entities': total_entities,
                    'total_balance': total_balance,
                    'by_entity_type': by_entity_type
                }
            }
        }), 200

    except Exception as e:
        logger.error(f"خطأ في الحصول على ملخص الأرصدة: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في النظام'
        }), 500

# معالج الأخطاء العام
@accounting_bp.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'message': 'الصفحة المطلوبة غير موجودة'
    }), 404

@accounting_bp.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        'success': False,
        'message': 'الطريقة غير مسموحة'
    }), 405
