"""
خدمة إنشاء PDF عربي لأوامر التسليم
باستخدام HTML/CSS وتحويله لـ PDF
"""

import os
from datetime import datetime
from typing import Dict, Optional
from flask import render_template_string

# إزالة المكتبات التي تسبب مشاكل في Windows
# try:
#     import pdfkit
#     PDFKIT_AVAILABLE = True
# except ImportError:
#     PDFKIT_AVAILABLE = False

# try:
#     from weasyprint import HTML, CSS
#     WEASYPRINT_AVAILABLE = True
# except ImportError:
#     WEASYPRINT_AVAILABLE = False

# تعطيل المكتبات المسببة للمشاكل
PDFKIT_AVAILABLE = False
WEASYPRINT_AVAILABLE = False

class ArabicDeliveryOrderPDFService:
    """خدمة إنشاء PDF عربي لأوامر التسليم"""
    
    def __init__(self):
        """تهيئة خدمة PDF العربية"""
        # إعدادات wkhtmltopdf
        self.options = {
            'page-size': 'A4',
            'margin-top': '0.75in',
            'margin-right': '0.75in',
            'margin-bottom': '0.75in',
            'margin-left': '0.75in',
            'encoding': "UTF-8",
            'no-outline': None,
            'enable-local-file-access': None
        }
    
    def get_status_arabic(self, status: str) -> str:
        """ترجمة حالة الأمر للعربية"""
        status_map = {
            'draft': 'مسودة',
            'sent': 'مرسل',
            'in_progress': 'قيد التنفيذ',
            'completed': 'مكتمل',
            'cancelled': 'ملغي'
        }
        return status_map.get(status, status)
    
    def get_priority_arabic(self, priority: str) -> str:
        """ترجمة الأولوية للعربية"""
        priority_map = {
            'low': 'منخفض',
            'normal': 'عادي',
            'high': 'عالي',
            'urgent': 'عاجل'
        }
        return priority_map.get(priority, priority)
    
    def get_shipment_type_arabic(self, shipment_type: str) -> str:
        """ترجمة نوع الشحنة للعربية"""
        type_map = {
            'sea': 'بحري',
            'air': 'جوي',
            'land': 'بري',
            'express': 'سريع'
        }
        return type_map.get(shipment_type, shipment_type)
    
    def create_html_template(self, order_data: Dict) -> str:
        """إنشاء قالب HTML لأمر التسليم"""
        
        html_template = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أمر تسليم - {{ order_data.order_number }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
            text-align: right;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #1f4e79;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: 700;
            color: #1f4e79;
            margin-bottom: 10px;
        }
        
        .document-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c5aa0;
            margin-bottom: 15px;
        }
        
        .order-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .order-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }
        
        .info-item {
            text-align: center;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }
        
        .info-value {
            font-size: 16px;
            color: #212529;
            margin-top: 5px;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f4e79;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 8px;
            margin-bottom: 15px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .data-table th {
            background: #1f4e79;
            color: white;
            padding: 12px;
            text-align: center;
            font-weight: 600;
        }
        
        .data-table td {
            padding: 10px 12px;
            border: 1px solid #dee2e6;
            text-align: center;
        }
        
        .data-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .shipment-table th {
            background: #28a745;
        }
        
        .agent-table th {
            background: #17a2b8;
        }
        
        .delivery-table th {
            background: #ffc107;
            color: #212529;
        }
        
        .special-instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .special-instructions h4 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .footer {
            border-top: 2px solid #1f4e79;
            padding-top: 20px;
            margin-top: 30px;
            text-align: center;
            font-size: 14px;
            color: #6c757d;
        }
        
        .company-info {
            margin-bottom: 10px;
        }
        
        .print-date {
            font-size: 12px;
            color: #adb5bd;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-draft { background: #6c757d; color: white; }
        .status-sent { background: #007bff; color: white; }
        .status-in_progress { background: #ffc107; color: #212529; }
        .status-completed { background: #28a745; color: white; }
        .status-cancelled { background: #dc3545; color: white; }
        
        .priority-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .priority-low { background: #6c757d; color: white; }
        .priority-normal { background: #007bff; color: white; }
        .priority-high { background: #ffc107; color: #212529; }
        .priority-urgent { background: #dc3545; color: white; }
        
        @media print {
            body { font-size: 12px; }
            .container { padding: 10px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- رأس المستند -->
        <div class="header">
            <div class="company-name">شركة النقل والشحن المتطورة</div>
            <div class="document-title">أمر تسليم للمخلص الجمركي</div>
        </div>
        
        <!-- معلومات الأمر الأساسية -->
        <div class="order-info">
            <div class="order-info-grid">
                <div class="info-item">
                    <div class="info-label">رقم الأمر</div>
                    <div class="info-value">{{ order_data.order_number or 'غير محدد' }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">تاريخ الإصدار</div>
                    <div class="info-value">{{ order_data.created_date or 'غير محدد' }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">حالة الأمر</div>
                    <div class="info-value">
                        <span class="status-badge status-{{ order_data.order_status or 'draft' }}">
                            {{ status_text }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- المعلومات الأساسية للشحنة -->
        <div class="section">
            <h3 class="section-title">📦 المعلومات الأساسية للشحنة</h3>
            <table class="data-table shipment-table">
                <thead>
                    <tr>
                        <th>البيان</th>
                        <th>القيمة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>رقم التتبع</td>
                        <td><strong>{{ order_data.tracking_number or 'غير محدد' }}</strong></td>
                    </tr>
                    <tr>
                        <td>رقم الحجز</td>
                        <td><strong>{{ order_data.booking_number or 'غير محدد' }}</strong></td>
                    </tr>
                    <tr>
                        <td>نوع الشحنة</td>
                        <td>{{ shipment_type_text }}</td>
                    </tr>
                    <tr>
                        <td>الوزن الإجمالي (Gross Weight)</td>
                        <td>{{ order_data.total_weight or 'غير محدد' }}{% if order_data.total_weight %} كيلو{% endif %}</td>
                    </tr>
                    <tr>
                        <td>الوزن الصافي (Net Weight)</td>
                        <td>{{ order_data.net_weight or 'غير محدد' }}{% if order_data.net_weight %} كيلو{% endif %}</td>
                    </tr>
                    <tr>
                        <td>عدد الطرود</td>
                        <td>{{ order_data.packages_count or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td>وصف البضاعة</td>
                        <td>{{ order_data.cargo_description or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td>بلد المنشأ</td>
                        <td>{{ order_data.origin_country or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td>موقع التسليم</td>
                        <td><strong>{{ order_data.delivery_location or 'غير محدد' }}</strong></td>
                    </tr>
                    <tr>
                        <td>التاريخ المطلوب للتخليص</td>
                        <td><strong>{{ order_data.expected_completion_date or 'غير محدد' }}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- بيانات الشحنة التفصيلية -->
        <div class="section">
            <h3 class="section-title">🚢 بيانات الشحنة التفصيلية</h3>
            <table class="data-table agent-table">
                <thead>
                    <tr>
                        <th>البيان</th>
                        <th>القيمة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>رقم بوليصة الشحن (B/L)</td>
                        <td><strong>{{ order_data.bl_number or 'غير محدد' }}</strong></td>
                    </tr>
                    <tr>
                        <td>رقم الحاوية</td>
                        <td><strong>{{ order_data.container_number or 'غير محدد' }}</strong></td>
                    </tr>
                    <tr>
                        <td>نوع الحاوية</td>
                        <td>{{ order_data.container_type or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td>حجم الحاوية</td>
                        <td>{{ order_data.container_size or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td>رقم الختم (Seal Number)</td>
                        <td><strong>{{ order_data.seal_number or 'غير محدد' }}</strong></td>
                    </tr>
                    <tr>
                        <td>شركة الشحن</td>
                        <td>{{ order_data.shipping_company_name or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td>ميناء الشحن</td>
                        <td>{{ order_data.loading_port or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td>ميناء الوصول</td>
                        <td>{{ order_data.discharge_port or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td>اسم السفينة</td>
                        <td>{{ order_data.vessel_name or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td>رقم الرحلة</td>
                        <td>{{ order_data.voyage_number or 'غير محدد' }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        

        
        <!-- التعليمات الخاصة -->
        {% if order_data.special_instructions %}
        <div class="special-instructions">
            <h4>📝 تعليمات خاصة</h4>
            <p>{{ order_data.special_instructions }}</p>
        </div>
        {% endif %}
        
        <!-- تذييل المستند -->
        <div class="footer">
            <div class="company-info">
                <strong>شركة النقل والشحن المتطورة</strong><br>
                العنوان: المملكة العربية السعودية - الرياض<br>
                الهاتف: +966 11 123 4567 | البريد الإلكتروني: <EMAIL><br>
                الموقع الإلكتروني: www.shipping.com
            </div>
            <div class="print-date">
                تاريخ الطباعة: {{ print_date }}
            </div>
        </div>
    </div>
</body>
</html>
        """
        
        # تحضير البيانات للقالب
        template_data = {
            'order_data': order_data,
            'status_text': self.get_status_arabic(order_data.get('order_status', 'draft')),
            'priority_text': self.get_priority_arabic(order_data.get('priority', 'normal')),
            'shipment_type_text': self.get_shipment_type_arabic(order_data.get('shipment_type', 'بحري')),
            'print_date': datetime.now().strftime('%Y-%m-%d %H:%M')
        }
        
        return render_template_string(html_template, **template_data)
    
    def generate_delivery_order_pdf(self, order_data: Dict, output_path: Optional[str] = None) -> bytes:
        """إنشاء PDF لأمر التسليم من HTML"""

        # إنشاء HTML
        html_content = self.create_html_template(order_data)

        # تخطي WeasyPrint لتجنب مشاكل GTK على ويندوز
        # if WEASYPRINT_AVAILABLE:
        #     try:
        #         html_doc = HTML(string=html_content)
        #         pdf_data = html_doc.write_pdf()
        #         if output_path:
        #             with open(output_path, 'wb') as f:
        #                 f.write(pdf_data)
        #         return pdf_data
        #     except Exception as e:
        #         print(f"Error with WeasyPrint: {e}")

        # محاولة استخدام pdfkit
        if PDFKIT_AVAILABLE:
            try:
                pdf_data = pdfkit.from_string(html_content, False, options=self.options)

                # حفظ في ملف إذا تم تحديد المسار
                if output_path:
                    with open(output_path, 'wb') as f:
                        f.write(pdf_data)

                return pdf_data

            except Exception as e:
                print(f"Error with pdfkit: {e}")

        # استخدام الطريقة البديلة
        return self.generate_simple_pdf(order_data, output_path)
    
    def generate_simple_pdf(self, order_data: Dict, output_path: Optional[str] = None) -> bytes:
        """إنشاء PDF بسيط في حالة فشل wkhtmltopdf"""
        # استخدام الخدمة القديمة كبديل
        from app.services.pdf_service import generate_delivery_order_pdf
        return generate_delivery_order_pdf(order_data, output_path)


# إنشاء instance عام للخدمة
arabic_pdf_service = ArabicDeliveryOrderPDFService()


def generate_arabic_delivery_order_pdf(order_data: Dict, output_path: Optional[str] = None) -> bytes:
    """دالة مساعدة لإنشاء PDF عربي لأمر التسليم"""
    return arabic_pdf_service.generate_delivery_order_pdf(order_data, output_path)
