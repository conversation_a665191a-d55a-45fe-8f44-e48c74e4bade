# -*- coding: utf-8 -*-
"""
مولد PDF جميل يحاكي تصميم صفحة المعاينة
Beautiful PDF Generator matching the viewer page design
"""

import os
import sys
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm, mm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import arabic_reshaper
from bidi.algorithm import get_display

# إضافة مسار قاعدة البيانات
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from database_manager import DatabaseManager

class BeautifulPDFGenerator:
    """مولد PDF جميل يحاكي تصميم صفحة المعاينة"""
    
    def __init__(self):
        """تهيئة مولد PDF"""
        self.setup_arabic_fonts()
        self.output_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'pdf')
        os.makedirs(self.output_dir, exist_ok=True)
    
    def setup_arabic_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # مسار الخط المُحمل
            font_path = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'fonts', 'NotoSansArabic-Regular.ttf')
            
            if os.path.exists(font_path):
                try:
                    pdfmetrics.registerFont(TTFont('Arabic', font_path))
                    pdfmetrics.registerFont(TTFont('Arabic-Bold', font_path))  # نفس الخط للعريض
                    print(f"✅ تم تسجيل الخط العربي: {font_path}")
                    self.arabic_font_available = True
                    return
                except Exception as e:
                    print(f"❌ فشل تسجيل الخط المُحمل: {e}")
            
            # محاولة استخدام خطوط النظام
            system_fonts = [
                'C:/Windows/Fonts/arial.ttf',
                'C:/Windows/Fonts/tahoma.ttf',
                'C:/Windows/Fonts/calibri.ttf'
            ]
            
            for font_path in system_fonts:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('Arabic', font_path))
                        pdfmetrics.registerFont(TTFont('Arabic-Bold', font_path))
                        print(f"✅ تم تسجيل خط النظام: {font_path}")
                        self.arabic_font_available = True
                        return
                    except Exception as e:
                        continue
            
            print("⚠️ لم يتم العثور على خط عربي مناسب")
            self.arabic_font_available = False
                
        except Exception as e:
            print(f"خطأ في إعداد الخطوط: {e}")
            self.arabic_font_available = False
    
    def reshape_arabic_text(self, text):
        """إعادة تشكيل النص العربي للعرض الصحيح"""
        try:
            if text and any('\u0600' <= char <= '\u06FF' for char in str(text)):
                reshaped_text = arabic_reshaper.reshape(str(text))
                return get_display(reshaped_text)
            return str(text) if text else ""
        except Exception as e:
            print(f"خطأ في معالجة النص العربي: {e}")
            return str(text) if text else ""
    
    def generate_beautiful_delivery_order_pdf(self, delivery_order_id):
        """إنشاء PDF جميل يحاكي تصميم صفحة المعاينة"""
        try:
            # جلب بيانات أمر التسليم
            order_data = self._get_delivery_order_data(delivery_order_id)
            if not order_data:
                return None, "أمر التسليم غير موجود"
            
            # إنشاء اسم الملف
            filename = f"delivery_order_beautiful_{order_data['order_number']}.pdf"
            filepath = os.path.join(self.output_dir, filename)
            
            # إنشاء المستند
            doc = SimpleDocTemplate(
                filepath,
                pagesize=A4,
                rightMargin=15*mm,
                leftMargin=15*mm,
                topMargin=15*mm,
                bottomMargin=15*mm
            )
            
            # إنشاء المحتوى
            story = []
            
            # إضافة الهيدر الجميل
            self._add_beautiful_header(story, order_data)
            
            # إضافة معلومات الأمر في شبكة
            self._add_order_info_grid(story, order_data)
            
            # إضافة معلومات الشحنة
            self._add_shipment_section(story, order_data)
            
            # إضافة معلومات المخلص
            self._add_agent_section(story, order_data)
            
            # إضافة معلومات الاتصال
            self._add_contact_section(story, order_data)
            
            # إضافة التوقيع
            self._add_signature_section(story, order_data)
            
            # إضافة الفوتر
            self._add_beautiful_footer(story, order_data)
            
            # بناء المستند
            doc.build(story)
            
            return filepath, "تم إنشاء PDF جميل بنجاح"
            
        except Exception as e:
            return None, f"خطأ في إنشاء PDF جميل: {str(e)}"
    
    def _get_delivery_order_data(self, delivery_order_id):
        """جلب بيانات أمر التسليم من قاعدة البيانات"""
        try:
            db_manager = DatabaseManager()
            
            query = """
                SELECT 
                    do.id,
                    do.order_number,
                    do.shipment_id,
                    do.customs_agent_id,
                    ca.agent_name,
                    ca.phone,
                    ca.mobile,
                    ca.email,
                    do.branch_id,
                    b.brn_lname as branch_name,
                    b.brn_ladd as branch_address,
                    do.created_date,
                    do.order_status,
                    cs.shipment_number,
                    cs.port_of_loading,
                    cs.port_of_discharge,
                    cs.shipment_status,
                    cc.container_number,
                    cc.container_type,
                    cc.seal_number,
                    cc.total_weight,
                    cc.net_weight
                FROM delivery_orders do
                LEFT JOIN customs_agents ca ON do.customs_agent_id = ca.id
                LEFT JOIN branches b ON do.branch_id = b.brn_no
                LEFT JOIN cargo_shipments cs ON do.shipment_id = cs.id
                LEFT JOIN cargo_containers cc ON cs.id = cc.cargo_shipment_id
                WHERE do.id = :delivery_order_id
            """
            
            result = db_manager.execute_query(query, {'delivery_order_id': delivery_order_id})
            
            if result:
                row = result[0]
                return {
                    'id': row[0],
                    'order_number': row[1],
                    'shipment_id': row[2],
                    'customs_agent_id': row[3],
                    'agent_name': row[4],
                    'agent_phone': row[5],
                    'agent_mobile': row[6],
                    'agent_email': row[7],
                    'branch_id': row[8],
                    'branch_name': row[9],
                    'branch_address': row[10],
                    'created_date': row[11],
                    'order_status': row[12],
                    'shipment_reference': row[13],
                    'origin_port': row[14],
                    'destination_port': row[15],
                    'shipment_status': row[16],
                    'container_number': row[17],
                    'container_type': row[18],
                    'seal_number': row[19],
                    'total_weight': row[20],
                    'net_weight': row[21]
                }
            
            db_manager.close()
            return None
            
        except Exception as e:
            print(f"خطأ في جلب بيانات أمر التسليم: {e}")
            return None
    
    def _add_beautiful_header(self, story, order_data):
        """إضافة هيدر جميل يحاكي التصميم الأصلي"""
        # تحديد الخط المناسب
        font_name = 'Arabic' if self.arabic_font_available else 'Helvetica'
        font_bold = 'Arabic-Bold' if self.arabic_font_available else 'Helvetica-Bold'
        
        # اسم الشركة
        company_style = ParagraphStyle(
            'CompanyName',
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=5,
            textColor=colors.Color(0.12, 0.31, 0.47),  # #1f4e79
            fontName=font_bold
        )
        
        company_name = self.reshape_arabic_text(order_data.get('branch_name', 'شركة النقل والشحن المتطورة'))
        story.append(Paragraph(company_name, company_style))
        
        # عنوان المستند
        title_style = ParagraphStyle(
            'DocumentTitle',
            fontSize=16,
            alignment=TA_CENTER,
            spaceAfter=8,
            textColor=colors.Color(0.17, 0.35, 0.63),  # #2c5aa0
            fontName=font_name
        )
        
        title = self.reshape_arabic_text("أمر تسليم للمخلص الجمركي")
        story.append(Paragraph(title, title_style))
        
        # عنوان الفرع إذا كان متاح
        if order_data.get('branch_address'):
            address_style = ParagraphStyle(
                'BranchAddress',
                fontSize=12,
                alignment=TA_CENTER,
                spaceAfter=15,
                textColor=colors.grey,
                fontName=font_name
            )
            
            address = self.reshape_arabic_text(order_data['branch_address'])
            story.append(Paragraph(address, address_style))
        
        # خط فاصل
        story.append(Spacer(1, 10))
        
        # خط أزرق
        line_table = Table([['']], colWidths=[18*cm])
        line_table.setStyle(TableStyle([
            ('LINEBELOW', (0, 0), (-1, -1), 3, colors.Color(0, 0.48, 1)),  # #007bff
            ('TOPPADDING', (0, 0), (-1, -1), 0),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 0),
        ]))
        story.append(line_table)
        story.append(Spacer(1, 15))

    def _add_order_info_grid(self, story, order_data):
        """إضافة معلومات الأمر في شبكة جميلة"""
        font_name = 'Arabic' if self.arabic_font_available else 'Helvetica'

        # بيانات الشبكة (3 أعمدة)
        grid_data = [
            [
                self.reshape_arabic_text("رقم الأمر"),
                self.reshape_arabic_text("تاريخ الإصدار"),
                self.reshape_arabic_text("حالة الأمر")
            ],
            [
                order_data['order_number'],
                str(order_data['created_date']),
                self.reshape_arabic_text(order_data.get('order_status', 'جديد'))
            ]
        ]

        grid_table = Table(grid_data, colWidths=[6*cm, 6*cm, 6*cm])
        grid_table.setStyle(TableStyle([
            # الصف الأول (العناوين)
            ('BACKGROUND', (0, 0), (-1, 0), colors.Color(0.97, 0.98, 0.98)),  # #f8f9fa
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.Color(0.29, 0.31, 0.34)),  # #495057
            ('FONTNAME', (0, 0), (-1, 0), font_name),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('VALIGN', (0, 0), (-1, 0), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (-1, 0), 8),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),

            # الصف الثاني (القيم)
            ('BACKGROUND', (0, 1), (-1, 1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, 1), colors.Color(0.13, 0.15, 0.16)),  # #212529
            ('FONTNAME', (0, 1), (-1, 1), font_name),
            ('FONTSIZE', (0, 1), (-1, 1), 13),
            ('ALIGN', (0, 1), (-1, 1), 'CENTER'),
            ('VALIGN', (0, 1), (-1, 1), 'MIDDLE'),
            ('TOPPADDING', (0, 1), (-1, 1), 10),
            ('BOTTOMPADDING', (0, 1), (-1, 1), 10),

            # الحدود
            ('GRID', (0, 0), (-1, -1), 1, colors.Color(0.87, 0.92, 0.94)),  # #e9ecef
            ('ROUNDEDCORNERS', [4, 4, 4, 4]),
        ]))

        story.append(grid_table)
        story.append(Spacer(1, 15))

    def _add_shipment_section(self, story, order_data):
        """إضافة قسم معلومات الشحنة"""
        font_name = 'Arabic' if self.arabic_font_available else 'Helvetica'

        # عنوان القسم
        section_style = ParagraphStyle(
            'SectionTitle',
            fontSize=14,
            alignment=TA_RIGHT,
            spaceAfter=10,
            textColor=colors.Color(0.12, 0.31, 0.47),  # #1f4e79
            fontName=font_name
        )

        section_title = self.reshape_arabic_text("معلومات الشحنة")
        story.append(Paragraph(section_title, section_style))

        # بيانات الشحنة
        shipment_data = [
            [self.reshape_arabic_text("رقم الشحنة"), order_data.get('shipment_reference', 'غير محدد')],
            [self.reshape_arabic_text("ميناء المنشأ"), order_data.get('origin_port', 'غير محدد')],
            [self.reshape_arabic_text("ميناء الوصول"), order_data.get('destination_port', 'غير محدد')],
            [self.reshape_arabic_text("حالة الشحنة"), self.reshape_arabic_text(order_data.get('shipment_status', 'غير محدد'))],
            [self.reshape_arabic_text("رقم الحاوية"), order_data.get('container_number', 'غير محدد')],
            [self.reshape_arabic_text("نوع الحاوية"), order_data.get('container_type', 'غير محدد')],
        ]

        shipment_table = Table(shipment_data, colWidths=[4.5*cm, 13.5*cm])
        shipment_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.Color(0.97, 0.98, 0.98)),  # #f8f9fa
            ('TEXTCOLOR', (0, 0), (0, -1), colors.Color(0.29, 0.31, 0.34)),  # #495057
            ('BACKGROUND', (1, 0), (1, -1), colors.white),
            ('TEXTCOLOR', (1, 0), (1, -1), colors.Color(0.13, 0.15, 0.16)),  # #212529
            ('FONTNAME', (0, 0), (-1, -1), font_name),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, colors.Color(0.87, 0.92, 0.94)),  # #e9ecef
        ]))

        story.append(shipment_table)
        story.append(Spacer(1, 15))

    def _add_agent_section(self, story, order_data):
        """إضافة قسم معلومات المخلص"""
        font_name = 'Arabic' if self.arabic_font_available else 'Helvetica'

        # عنوان القسم
        section_style = ParagraphStyle(
            'SectionTitle',
            fontSize=14,
            alignment=TA_RIGHT,
            spaceAfter=10,
            textColor=colors.Color(0.12, 0.31, 0.47),  # #1f4e79
            fontName=font_name
        )

        section_title = self.reshape_arabic_text("معلومات المخلص الجمركي")
        story.append(Paragraph(section_title, section_style))

        # بيانات المخلص
        agent_data = [
            [self.reshape_arabic_text("اسم المخلص"), self.reshape_arabic_text(order_data.get('agent_name', 'غير محدد'))],
            [self.reshape_arabic_text("رقم الهاتف"), order_data.get('agent_phone', 'غير محدد')],
            [self.reshape_arabic_text("رقم الجوال"), order_data.get('agent_mobile', 'غير محدد')],
            [self.reshape_arabic_text("البريد الإلكتروني"), order_data.get('agent_email', 'غير محدد')],
        ]

        agent_table = Table(agent_data, colWidths=[4.5*cm, 13.5*cm])
        agent_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.Color(0.97, 0.98, 0.98)),  # #f8f9fa
            ('TEXTCOLOR', (0, 0), (0, -1), colors.Color(0.29, 0.31, 0.34)),  # #495057
            ('BACKGROUND', (1, 0), (1, -1), colors.white),
            ('TEXTCOLOR', (1, 0), (1, -1), colors.Color(0.13, 0.15, 0.16)),  # #212529
            ('FONTNAME', (0, 0), (-1, -1), font_name),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, colors.Color(0.87, 0.92, 0.94)),  # #e9ecef
        ]))

        story.append(agent_table)
        story.append(Spacer(1, 15))

    def _add_contact_section(self, story, order_data):
        """إضافة قسم معلومات الاتصال"""
        font_name = 'Arabic' if self.arabic_font_available else 'Helvetica'

        # عنوان القسم
        section_style = ParagraphStyle(
            'SectionTitle',
            fontSize=14,
            alignment=TA_RIGHT,
            spaceAfter=10,
            textColor=colors.Color(0.12, 0.31, 0.47),  # #1f4e79
            fontName=font_name
        )

        section_title = self.reshape_arabic_text("معلومات الاتصال والمتابعة")
        story.append(Paragraph(section_title, section_style))

        # معلومات الاتصال (نص بسيط بدون تعقيد)
        contact_lines = [
            "للاستفسارات والمتابعة:",
            "",
            f"هاتف الفرع: {order_data.get('agent_phone', 'غير محدد')}",
            f"جوال المخلص: {order_data.get('agent_mobile', 'غير محدد')}",
            f"البريد الإلكتروني: {order_data.get('agent_email', 'غير محدد')}",
            "",
            "يرجى الاحتفاظ بهذا المستند للمراجعة والمتابعة."
        ]

        contact_text = "<br/>".join([self.reshape_arabic_text(line) for line in contact_lines])

        contact_style = ParagraphStyle(
            'ContactInfo',
            fontSize=10,
            alignment=TA_RIGHT,
            spaceAfter=15,
            textColor=colors.Color(0.29, 0.31, 0.34),  # #495057
            fontName=font_name,
            leading=14
        )

        story.append(Paragraph(contact_text, contact_style))

    def _add_signature_section(self, story, order_data):
        """إضافة قسم التوقيع"""
        font_name = 'Arabic' if self.arabic_font_available else 'Helvetica'

        # عنوان قسم التوقيع
        signature_title_style = ParagraphStyle(
            'SignatureTitle',
            fontSize=14,
            alignment=TA_RIGHT,
            spaceAfter=10,
            textColor=colors.Color(0.12, 0.31, 0.47),  # #1f4e79
            fontName=font_name
        )

        signature_title = self.reshape_arabic_text("التوقيع والاعتماد")
        story.append(Paragraph(signature_title, signature_title_style))

        # جدول التوقيع
        signature_data = [
            [self.reshape_arabic_text("توقيع المخلص"), ""],
            [self.reshape_arabic_text("التاريخ"), ""],
            [self.reshape_arabic_text("الختم الرسمي"), ""],
        ]

        signature_table = Table(signature_data, colWidths=[4*cm, 14*cm], rowHeights=[1.5*cm, 1.5*cm, 1.5*cm])
        signature_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.Color(0.97, 0.98, 0.98)),  # #f8f9fa
            ('TEXTCOLOR', (0, 0), (0, -1), colors.Color(0.29, 0.31, 0.34)),  # #495057
            ('BACKGROUND', (1, 0), (1, -1), colors.white),
            ('FONTNAME', (0, 0), (-1, -1), font_name),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, colors.Color(0.87, 0.92, 0.94)),  # #e9ecef
        ]))

        story.append(signature_table)
        story.append(Spacer(1, 20))

    def _add_beautiful_footer(self, story, order_data):
        """إضافة فوتر جميل"""
        font_name = 'Arabic' if self.arabic_font_available else 'Helvetica'

        # ملاحظة مهمة
        note_style = ParagraphStyle(
            'ImportantNote',
            fontSize=10,
            alignment=TA_RIGHT,
            spaceAfter=15,
            textColor=colors.Color(0.86, 0.20, 0.27),  # #dc3545
            fontName=font_name
        )

        note_text = self.reshape_arabic_text("ملاحظة مهمة: يرجى مراجعة جميع البيانات والتأكد من صحتها قبل البدء في إجراءات التخليص الجمركي.")
        story.append(Paragraph(note_text, note_style))

        # معلومات النظام
        footer_style = ParagraphStyle(
            'Footer',
            fontSize=8,
            alignment=TA_CENTER,
            textColor=colors.grey,
            fontName=font_name
        )

        footer_lines = [
            "تم إنشاء هذا المستند تلقائياً بواسطة نظام إدارة الشحنات",
            datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        ]

        footer_text = "<br/>".join([self.reshape_arabic_text(line) for line in footer_lines])

        story.append(Paragraph(footer_text, footer_style))


# إنشاء instance عام للمولد
beautiful_pdf_generator = BeautifulPDFGenerator()


def generate_beautiful_delivery_order_pdf(delivery_order_id):
    """دالة مساعدة لإنشاء PDF جميل"""
    return beautiful_pdf_generator.generate_beautiful_delivery_order_pdf(delivery_order_id)
