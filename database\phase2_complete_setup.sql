-- إعد<PERSON> قاعدة البيانات الكامل للمرحلة الثانية المتقدمة
-- Phase 2 Complete Database Setup - Advanced Delivery Orders System

-- ===== جداول التكامل والمراقبة =====

-- 1. جدول أحداث التكامل
CREATE TABLE integration_events_log (
    id NUMBER PRIMARY KEY,
    shipment_id NUMBER,
    order_id NUMBER,
    event_type VARCHAR2(100) NOT NULL, -- status_change, arrival, document_uploaded, etc.
    event_source VARCHAR2(50) DEFAULT 'system', -- system, user, api, automation
    
    -- تفاصيل المعالجة
    processed_components VARCHAR2(500), -- automation,notifications,analytics
    processing_status VARCHAR2(20) DEFAULT 'success', -- success, partial, failed
    success NUMBER(1) DEFAULT 1,
    error_count NUMBER DEFAULT 0,
    error_details CLOB,
    
    -- معلومات التوقيت
    event_date DATE DEFAULT SYSDATE,
    processing_time NUMBER(10,3), -- بالثواني
    
    -- بيانات إضافية
    event_data CLOB, -- JSON data
    user_id NUMBER,
    ip_address VARCHAR2(45),
    
    created_at DATE DEFAULT SYSDATE
);

-- 2. إنشاء sequence لأحداث التكامل
CREATE SEQUENCE integration_events_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 3. جدول مراقبة صحة النظام
CREATE TABLE system_health_monitoring (
    id NUMBER PRIMARY KEY,
    check_date DATE DEFAULT SYSDATE,
    component_name VARCHAR2(100) NOT NULL, -- automation, notifications, agent_portal, analytics
    
    -- حالة المكون
    component_status VARCHAR2(20) DEFAULT 'healthy', -- healthy, warning, error, offline
    response_time NUMBER(10,3), -- بالثواني
    
    -- مؤشرات الأداء
    cpu_usage NUMBER(5,2),
    memory_usage NUMBER(5,2),
    disk_usage NUMBER(5,2),
    
    -- إحصائيات المكون
    active_processes NUMBER DEFAULT 0,
    pending_tasks NUMBER DEFAULT 0,
    completed_tasks NUMBER DEFAULT 0,
    failed_tasks NUMBER DEFAULT 0,
    
    -- تفاصيل إضافية
    health_details CLOB, -- JSON data
    error_message CLOB,
    
    created_at DATE DEFAULT SYSDATE
);

-- 4. إنشاء sequence لمراقبة الصحة
CREATE SEQUENCE system_health_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 5. جدول إعدادات النظام المتقدمة
CREATE TABLE advanced_system_settings (
    id NUMBER PRIMARY KEY,
    category VARCHAR2(100) NOT NULL, -- automation, notifications, analytics, portal
    setting_key VARCHAR2(200) NOT NULL,
    setting_value CLOB,
    setting_type VARCHAR2(50) DEFAULT 'string', -- string, number, boolean, json, encrypted
    
    -- وصف وتحقق
    description VARCHAR2(1000),
    validation_rules CLOB, -- JSON validation rules
    default_value CLOB,
    
    -- حالة الإعداد
    is_active NUMBER(1) DEFAULT 1,
    is_system_setting NUMBER(1) DEFAULT 0, -- لا يمكن تعديله من المستخدم
    requires_restart NUMBER(1) DEFAULT 0,
    
    -- معلومات التحديث
    created_at DATE DEFAULT SYSDATE,
    created_by NUMBER,
    updated_at DATE,
    updated_by NUMBER,
    last_applied_at DATE,
    
    CONSTRAINT uk_advanced_settings UNIQUE (category, setting_key)
);

-- 6. إنشاء sequence للإعدادات المتقدمة
CREATE SEQUENCE advanced_settings_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 7. جدول تتبع الأداء المتقدم
CREATE TABLE performance_metrics (
    id NUMBER PRIMARY KEY,
    metric_date DATE DEFAULT SYSDATE,
    metric_type VARCHAR2(100) NOT NULL, -- daily, weekly, monthly, real_time
    
    -- مؤشرات الأوامر
    total_orders NUMBER DEFAULT 0,
    completed_orders NUMBER DEFAULT 0,
    cancelled_orders NUMBER DEFAULT 0,
    overdue_orders NUMBER DEFAULT 0,
    
    -- مؤشرات الوقت
    avg_completion_time NUMBER(10,2), -- بالساعات
    avg_response_time NUMBER(10,2), -- بالساعات
    on_time_delivery_rate NUMBER(5,2), -- نسبة مئوية
    
    -- مؤشرات الجودة
    document_completion_rate NUMBER(5,2),
    customer_satisfaction_rate NUMBER(5,2),
    agent_efficiency_rate NUMBER(5,2),
    
    -- مؤشرات مالية
    total_revenue NUMBER(15,2),
    avg_order_value NUMBER(10,2),
    cost_per_order NUMBER(10,2),
    profit_margin NUMBER(5,2),
    
    -- مؤشرات النظام
    system_uptime NUMBER(5,2), -- نسبة مئوية
    automation_success_rate NUMBER(5,2),
    notification_delivery_rate NUMBER(5,2),
    
    created_at DATE DEFAULT SYSDATE
);

-- 8. إنشاء sequence لمؤشرات الأداء
CREATE SEQUENCE performance_metrics_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 9. جدول تحليل الاتجاهات المتقدم
CREATE TABLE trend_analysis (
    id NUMBER PRIMARY KEY,
    analysis_date DATE DEFAULT SYSDATE,
    analysis_type VARCHAR2(100) NOT NULL, -- growth, seasonal, predictive, comparative
    period_type VARCHAR2(50) NOT NULL, -- daily, weekly, monthly, quarterly, yearly
    
    -- البيانات المحللة
    data_points CLOB, -- JSON array of data points
    trend_direction VARCHAR2(20), -- increasing, decreasing, stable, volatile
    trend_strength NUMBER(5,2), -- قوة الاتجاه من 0 إلى 100
    
    -- التنبؤات
    predicted_values CLOB, -- JSON array of predictions
    confidence_level NUMBER(5,2), -- مستوى الثقة في التنبؤ
    prediction_accuracy NUMBER(5,2), -- دقة التنبؤات السابقة
    
    -- التوصيات
    recommendations CLOB, -- JSON array of recommendations
    risk_factors CLOB, -- JSON array of risk factors
    opportunities CLOB, -- JSON array of opportunities
    
    -- معلومات التحليل
    algorithm_used VARCHAR2(100),
    data_quality_score NUMBER(5,2),
    sample_size NUMBER,
    
    created_at DATE DEFAULT SYSDATE,
    created_by NUMBER
);

-- 10. إنشاء sequence لتحليل الاتجاهات
CREATE SEQUENCE trend_analysis_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- ===== الفهارس للأداء =====

-- فهارس أحداث التكامل
CREATE INDEX idx_integration_events_date ON integration_events_log(event_date);
CREATE INDEX idx_integration_events_type ON integration_events_log(event_type);
CREATE INDEX idx_integration_events_shipment ON integration_events_log(shipment_id);
CREATE INDEX idx_integration_events_status ON integration_events_log(processing_status);

-- فهارس مراقبة الصحة
CREATE INDEX idx_system_health_date ON system_health_monitoring(check_date);
CREATE INDEX idx_system_health_component ON system_health_monitoring(component_name);
CREATE INDEX idx_system_health_status ON system_health_monitoring(component_status);

-- فهارس مؤشرات الأداء
CREATE INDEX idx_performance_metrics_date ON performance_metrics(metric_date);
CREATE INDEX idx_performance_metrics_type ON performance_metrics(metric_type);

-- فهارس تحليل الاتجاهات
CREATE INDEX idx_trend_analysis_date ON trend_analysis(analysis_date);
CREATE INDEX idx_trend_analysis_type ON trend_analysis(analysis_type);

-- ===== إدراج الإعدادات الافتراضية =====

-- إعدادات الأتمتة
INSERT INTO advanced_system_settings (id, category, setting_key, setting_value, setting_type, description) VALUES
(advanced_settings_seq.NEXTVAL, 'automation', 'auto_create_orders', 'true', 'boolean', 'تفعيل إنشاء أوامر التسليم تلقائياً عند وصول الشحنات');

INSERT INTO advanced_system_settings (id, category, setting_key, setting_value, setting_type, description) VALUES
(advanced_settings_seq.NEXTVAL, 'automation', 'auto_assign_agents', 'true', 'boolean', 'تفعيل تعيين المخلصين تلقائياً بناءً على الخوارزمية الذكية');

INSERT INTO advanced_system_settings (id, category, setting_key, setting_value, setting_type, description) VALUES
(advanced_settings_seq.NEXTVAL, 'automation', 'agent_selection_algorithm', 'weighted_scoring', 'string', 'خوارزمية اختيار المخلص الأمثل');

-- إعدادات الإشعارات
INSERT INTO advanced_system_settings (id, category, setting_key, setting_value, setting_type, description) VALUES
(advanced_settings_seq.NEXTVAL, 'notifications', 'multi_channel_enabled', 'true', 'boolean', 'تفعيل الإشعارات متعددة القنوات');

INSERT INTO advanced_system_settings (id, category, setting_key, setting_value, setting_type, description) VALUES
(advanced_settings_seq.NEXTVAL, 'notifications', 'default_channels', '["SMS", "EMAIL", "WHATSAPP"]', 'json', 'القنوات الافتراضية للإشعارات');

INSERT INTO advanced_system_settings (id, category, setting_key, setting_value, setting_type, description) VALUES
(advanced_settings_seq.NEXTVAL, 'notifications', 'smart_scheduling', 'true', 'boolean', 'تفعيل الجدولة الذكية للإشعارات');

-- إعدادات التحليلات
INSERT INTO advanced_system_settings (id, category, setting_key, setting_value, setting_type, description) VALUES
(advanced_settings_seq.NEXTVAL, 'analytics', 'real_time_analytics', 'true', 'boolean', 'تفعيل التحليلات في الوقت الفعلي');

INSERT INTO advanced_system_settings (id, category, setting_key, setting_value, setting_type, description) VALUES
(advanced_settings_seq.NEXTVAL, 'analytics', 'predictive_analytics', 'true', 'boolean', 'تفعيل التحليلات التنبؤية');

INSERT INTO advanced_system_settings (id, category, setting_key, setting_value, setting_type, description) VALUES
(advanced_settings_seq.NEXTVAL, 'analytics', 'data_retention_days', '365', 'number', 'عدد أيام الاحتفاظ بالبيانات التحليلية');

-- إعدادات بوابة المخلص
INSERT INTO advanced_system_settings (id, category, setting_key, setting_value, setting_type, description) VALUES
(advanced_settings_seq.NEXTVAL, 'agent_portal', 'portal_enabled', 'true', 'boolean', 'تفعيل بوابة المخلص الإلكترونية');

INSERT INTO advanced_system_settings (id, category, setting_key, setting_value, setting_type, description) VALUES
(advanced_settings_seq.NEXTVAL, 'agent_portal', 'mobile_app_enabled', 'true', 'boolean', 'تفعيل تطبيق الموبايل للمخلصين');

INSERT INTO advanced_system_settings (id, category, setting_key, setting_value, setting_type, description) VALUES
(advanced_settings_seq.NEXTVAL, 'agent_portal', 'document_upload_limit', '10', 'number', 'الحد الأقصى لحجم الملف بالميجابايت');

-- ===== إنشاء Views شاملة =====

-- 1. View شامل لحالة النظام
CREATE OR REPLACE VIEW v_system_status_dashboard AS
SELECT 
    -- إحصائيات اليوم
    (SELECT COUNT(*) FROM integration_events_log WHERE TRUNC(event_date) = TRUNC(SYSDATE)) as today_events,
    (SELECT COUNT(*) FROM integration_events_log WHERE TRUNC(event_date) = TRUNC(SYSDATE) AND success = 1) as today_successful_events,
    (SELECT COUNT(*) FROM integration_events_log WHERE TRUNC(event_date) = TRUNC(SYSDATE) AND success = 0) as today_failed_events,
    
    -- حالة المكونات
    (SELECT COUNT(DISTINCT component_name) FROM system_health_monitoring WHERE check_date >= SYSDATE - 1 AND component_status = 'healthy') as healthy_components,
    (SELECT COUNT(DISTINCT component_name) FROM system_health_monitoring WHERE check_date >= SYSDATE - 1 AND component_status = 'warning') as warning_components,
    (SELECT COUNT(DISTINCT component_name) FROM system_health_monitoring WHERE check_date >= SYSDATE - 1 AND component_status = 'error') as error_components,
    
    -- مؤشرات الأداء
    (SELECT AVG(on_time_delivery_rate) FROM performance_metrics WHERE metric_date >= SYSDATE - 7) as avg_on_time_rate_7d,
    (SELECT AVG(automation_success_rate) FROM performance_metrics WHERE metric_date >= SYSDATE - 7) as avg_automation_rate_7d,
    (SELECT AVG(system_uptime) FROM performance_metrics WHERE metric_date >= SYSDATE - 7) as avg_uptime_7d,
    
    -- إحصائيات الأوامر
    (SELECT COUNT(*) FROM delivery_orders WHERE TRUNC(created_date) = TRUNC(SYSDATE)) as today_orders,
    (SELECT COUNT(*) FROM delivery_orders WHERE order_status = 'completed' AND TRUNC(actual_completion_date) = TRUNC(SYSDATE)) as today_completed_orders,
    (SELECT COUNT(*) FROM delivery_orders WHERE expected_completion_date < SYSDATE AND order_status NOT IN ('completed', 'cancelled')) as overdue_orders
FROM DUAL;

-- 2. View لتحليل الأداء المتقدم
CREATE OR REPLACE VIEW v_advanced_performance_analysis AS
SELECT 
    pm.metric_date,
    pm.total_orders,
    pm.completed_orders,
    pm.on_time_delivery_rate,
    pm.document_completion_rate,
    pm.automation_success_rate,
    pm.avg_completion_time,
    pm.total_revenue,
    pm.profit_margin,
    
    -- مقارنة مع الفترة السابقة
    LAG(pm.total_orders, 1) OVER (ORDER BY pm.metric_date) as prev_total_orders,
    LAG(pm.on_time_delivery_rate, 1) OVER (ORDER BY pm.metric_date) as prev_on_time_rate,
    
    -- حساب معدل النمو
    CASE 
        WHEN LAG(pm.total_orders, 1) OVER (ORDER BY pm.metric_date) > 0 
        THEN ROUND(((pm.total_orders - LAG(pm.total_orders, 1) OVER (ORDER BY pm.metric_date)) / LAG(pm.total_orders, 1) OVER (ORDER BY pm.metric_date)) * 100, 2)
        ELSE 0 
    END as growth_rate,
    
    -- تصنيف الأداء
    CASE 
        WHEN pm.on_time_delivery_rate >= 95 THEN 'ممتاز'
        WHEN pm.on_time_delivery_rate >= 85 THEN 'جيد'
        WHEN pm.on_time_delivery_rate >= 70 THEN 'متوسط'
        ELSE 'ضعيف'
    END as performance_rating
    
FROM performance_metrics pm
WHERE pm.metric_type = 'daily'
ORDER BY pm.metric_date DESC;

-- ===== Triggers للمراقبة التلقائية =====

-- Trigger لمراقبة أحداث التكامل
CREATE OR REPLACE TRIGGER trg_integration_monitoring
    AFTER INSERT ON integration_events_log
    FOR EACH ROW
BEGIN
    -- تسجيل في مراقبة الصحة إذا كان هناك فشل
    IF :NEW.success = 0 THEN
        INSERT INTO system_health_monitoring (
            id, component_name, component_status, error_message
        ) VALUES (
            system_health_seq.NEXTVAL, 
            'integration_system', 
            'warning',
            'Integration event failed: ' || :NEW.event_type
        );
    END IF;
END;
/

COMMIT;
