{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- شريط الأدوات العلوي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            تقارير طلبات الشراء
                        </h5>
                        <div class="btn-group">
                            <a href="{{ url_for('purchase_requests.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أنواع التقارير -->
    <div class="row">
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        تقارير عامة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('all_requests')">
                            <i class="fas fa-file-alt me-2"></i>
                            جميع طلبات الشراء
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('pending_requests')">
                            <i class="fas fa-clock me-2"></i>
                            الطلبات قيد المراجعة
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('approved_requests')">
                            <i class="fas fa-check-circle me-2"></i>
                            الطلبات المعتمدة
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('rejected_requests')">
                            <i class="fas fa-times-circle me-2"></i>
                            الطلبات المرفوضة
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        تقارير إحصائية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('monthly_summary')">
                            <i class="fas fa-calendar-alt me-2"></i>
                            ملخص شهري
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('department_summary')">
                            <i class="fas fa-building me-2"></i>
                            ملخص حسب القسم
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('priority_analysis')">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            تحليل الأولويات
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('value_analysis')">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            تحليل القيم المالية
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        تقارير مخصصة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action" onclick="showCustomReportForm()">
                            <i class="fas fa-filter me-2"></i>
                            تقرير مخصص
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('overdue_requests')">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            الطلبات المتأخرة
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('high_value_requests')">
                            <i class="fas fa-gem me-2"></i>
                            الطلبات عالية القيمة
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('frequent_items')">
                            <i class="fas fa-repeat me-2"></i>
                            الأصناف الأكثر طلباً
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج التقرير المخصص -->
    <div class="row mt-4" id="customReportForm" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        إعدادات التقرير المخصص
                    </h6>
                </div>
                <div class="card-body">
                    <form id="customReportFormData">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="report_date_from" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="report_date_from" name="date_from">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="report_date_to" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="report_date_to" name="date_to">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="report_status" class="form-label">الحالة</label>
                                    <select class="form-select" id="report_status" name="status">
                                        <option value="">جميع الحالات</option>
                                        <option value="مسودة">مسودة</option>
                                        <option value="قيد المراجعة">قيد المراجعة</option>
                                        <option value="معتمد">معتمد</option>
                                        <option value="مرفوض">مرفوض</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="report_priority" class="form-label">الأولوية</label>
                                    <select class="form-select" id="report_priority" name="priority">
                                        <option value="">جميع الأولويات</option>
                                        <option value="عادي">عادي</option>
                                        <option value="مهم">مهم</option>
                                        <option value="عاجل">عاجل</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="report_department" class="form-label">القسم</label>
                                    <input type="text" class="form-control" id="report_department" name="department" 
                                           placeholder="اسم القسم (اختياري)">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="report_format" class="form-label">تنسيق التقرير</label>
                                    <select class="form-select" id="report_format" name="format">
                                        <option value="html">عرض على الشاشة</option>
                                        <option value="pdf">PDF</option>
                                        <option value="excel">Excel</option>
                                        <option value="csv">CSV</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="button" class="btn btn-primary" onclick="generateCustomReport()">
                                            <i class="fas fa-play me-1"></i>إنشاء التقرير
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary ms-2" onclick="hideCustomReportForm()">
                                            <i class="fas fa-times me-1"></i>إلغاء
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- منطقة عرض التقرير -->
    <div class="row mt-4" id="reportResult" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0" id="reportTitle">نتائج التقرير</h6>
                        <div class="btn-group">
                            <button class="btn btn-outline-primary btn-sm" onclick="printReport()">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="exportReport()">
                                <i class="fas fa-download me-1"></i>تصدير
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body" id="reportContent">
                    <!-- سيتم عرض محتوى التقرير هنا -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function generateReport(reportType) {
    // إظهار رسالة تحميل
    showLoading();
    
    // محاكاة إنشاء التقرير
    setTimeout(() => {
        hideLoading();
        showReportResult(reportType);
    }, 1500);
}

function showCustomReportForm() {
    document.getElementById('customReportForm').style.display = 'block';
    document.getElementById('customReportForm').scrollIntoView({ behavior: 'smooth' });
}

function hideCustomReportForm() {
    document.getElementById('customReportForm').style.display = 'none';
}

function generateCustomReport() {
    const formData = new FormData(document.getElementById('customReportFormData'));
    
    // التحقق من صحة البيانات
    if (!formData.get('date_from') || !formData.get('date_to')) {
        alert('يرجى تحديد فترة زمنية للتقرير');
        return;
    }
    
    showLoading();
    
    // محاكاة إنشاء التقرير المخصص
    setTimeout(() => {
        hideLoading();
        showReportResult('custom');
        hideCustomReportForm();
    }, 2000);
}

function showReportResult(reportType) {
    const reportTitles = {
        'all_requests': 'تقرير جميع طلبات الشراء',
        'pending_requests': 'تقرير الطلبات قيد المراجعة',
        'approved_requests': 'تقرير الطلبات المعتمدة',
        'rejected_requests': 'تقرير الطلبات المرفوضة',
        'monthly_summary': 'الملخص الشهري',
        'department_summary': 'ملخص حسب القسم',
        'priority_analysis': 'تحليل الأولويات',
        'value_analysis': 'تحليل القيم المالية',
        'custom': 'التقرير المخصص'
    };
    
    document.getElementById('reportTitle').textContent = reportTitles[reportType] || 'نتائج التقرير';
    document.getElementById('reportContent').innerHTML = `
        <div class="text-center py-5">
            <i class="fas fa-chart-bar fa-3x text-primary mb-3"></i>
            <h5>تم إنشاء التقرير بنجاح</h5>
            <p class="text-muted">سيتم إضافة محتوى التقرير الفعلي هنا</p>
            <div class="alert alert-info mt-3">
                <i class="fas fa-info-circle me-2"></i>
                هذا مثال توضيحي - سيتم ربط التقارير بقاعدة البيانات الفعلية
            </div>
        </div>
    `;
    
    document.getElementById('reportResult').style.display = 'block';
    document.getElementById('reportResult').scrollIntoView({ behavior: 'smooth' });
}

function showLoading() {
    // يمكن إضافة مؤشر تحميل هنا
    console.log('جاري إنشاء التقرير...');
}

function hideLoading() {
    console.log('تم إنشاء التقرير');
}

function printReport() {
    window.print();
}

function exportReport() {
    alert('سيتم إضافة وظيفة التصدير قريباً');
}
</script>

<style>
.list-group-item-action:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
    transition: all 0.3s ease;
}

.card-header.bg-primary {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
}

.card-header.bg-success {
    background: linear-gradient(135deg, #28a745, #1e7e34) !important;
}

.card-header.bg-info {
    background: linear-gradient(135deg, #17a2b8, #117a8b) !important;
}

@media print {
    .btn-group, .card-header .btn-group {
        display: none !important;
    }
}
</style>
{% endblock %}
