-- =====================================================
-- إجراءات الترحيل المحاسبي (محسنة)
-- Accounting Procedures (Fixed)
-- =====================================================

-- 1. حذف الإجراءات الموجودة
BEGIN
    EXECUTE IMMEDIATE 'DROP PROCEDURE EXECUTE_TRANSFER_ACCOUNTING';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -4043 THEN
            NULL; -- تجاهل الخطأ إذا لم يكن الإجراء موجوداً
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP FUNCTION CHECK_MONEY_CHANGER_BALANCE';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -4043 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP FUNCTION VALIDATE_SUPPLIER_DIST';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -4043 THEN
            NULL;
        END IF;
END;
/

-- 2. إنشاء إجراء تنفيذ الحوالة المحاسبي (مبسط)
CREATE OR REPLACE PROCEDURE EXECUTE_TRANSFER_ACCOUNTING(
    p_transfer_id IN NUMBER,
    p_money_changer_id IN NUMBER,
    p_total_amount IN NUMBER,
    p_currency_code IN VARCHAR2 DEFAULT 'SAR',
    p_supplier_distributions IN CLOB,
    p_user_id IN NUMBER DEFAULT 1
) AS
    v_start_time TIMESTAMP := CURRENT_TIMESTAMP;
    v_supplier_id NUMBER;
    v_supplier_amount NUMBER;
    v_supplier_name VARCHAR2(200);
    v_money_changer_balance NUMBER;
    v_total_distributed NUMBER := 0;
    v_error_msg VARCHAR2(4000);
    v_dist_count NUMBER := 0;
    
    -- متغيرات لمعالجة JSON بدون JSON_ARRAY_T
    v_json_text CLOB := p_supplier_distributions;
    v_start_pos NUMBER := 1;
    v_end_pos NUMBER;
    v_supplier_json VARCHAR2(1000);
    
BEGIN
    -- بداية المعاملة
    SAVEPOINT start_transfer_execution;
    
    -- تسجيل بداية العملية
    INSERT INTO transfer_activity_log (
        transfer_id, activity_type, description, created_by
    ) VALUES (
        p_transfer_id, 'EXECUTION', 'بدء تنفيذ الحوالة المحاسبي', p_user_id
    );
    
    -- 1. التحقق من صحة البيانات
    IF p_transfer_id IS NULL OR p_transfer_id <= 0 THEN
        RAISE_APPLICATION_ERROR(-20001, 'معرف الحوالة غير صحيح');
    END IF;
    
    IF p_money_changer_id IS NULL OR p_money_changer_id <= 0 THEN
        RAISE_APPLICATION_ERROR(-20002, 'معرف الصراف غير صحيح');
    END IF;
    
    IF p_total_amount IS NULL OR p_total_amount <= 0 THEN
        RAISE_APPLICATION_ERROR(-20003, 'مبلغ الحوالة غير صحيح');
    END IF;
    
    -- 2. التحقق من رصيد الصراف
    BEGIN
        SELECT current_balance INTO v_money_changer_balance
        FROM CURRENT_BALANCES
        WHERE entity_type_code = 'MONEY_CHANGER' 
        AND entity_id = p_money_changer_id 
        AND currency_code = p_currency_code;
        
        IF v_money_changer_balance < p_total_amount THEN
            RAISE_APPLICATION_ERROR(-20005, 'رصيد الصراف غير كافي. الرصيد: ' || v_money_changer_balance || ', المطلوب: ' || p_total_amount);
        END IF;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RAISE_APPLICATION_ERROR(-20006, 'رصيد الصراف غير موجود');
    END;
    
    -- 3. معالجة توزيعات الموردين (معالجة JSON مبسطة)
    -- إزالة الأقواس والمسافات
    v_json_text := REPLACE(REPLACE(REPLACE(v_json_text, '[', ''), ']', ''), ' ', '');
    
    -- معالجة كل توزيع
    WHILE v_start_pos <= LENGTH(v_json_text) LOOP
        -- البحث عن نهاية الكائن الحالي
        v_end_pos := INSTR(v_json_text, '}', v_start_pos);
        IF v_end_pos = 0 THEN
            EXIT;
        END IF;
        
        -- استخراج الكائن الحالي
        v_supplier_json := SUBSTR(v_json_text, v_start_pos, v_end_pos - v_start_pos + 1);
        
        -- استخراج معرف المورد
        v_start_pos := INSTR(v_supplier_json, '"supplier_id":') + 14;
        v_end_pos := INSTR(v_supplier_json, ',', v_start_pos);
        IF v_end_pos = 0 THEN
            v_end_pos := INSTR(v_supplier_json, '}', v_start_pos);
        END IF;
        v_supplier_id := TO_NUMBER(SUBSTR(v_supplier_json, v_start_pos, v_end_pos - v_start_pos));
        
        -- استخراج المبلغ
        v_start_pos := INSTR(v_supplier_json, '"amount":') + 9;
        v_end_pos := INSTR(v_supplier_json, '}', v_start_pos);
        v_supplier_amount := TO_NUMBER(SUBSTR(v_supplier_json, v_start_pos, v_end_pos - v_start_pos));
        
        -- التحقق من وجود المورد
        BEGIN
            SELECT name_ar INTO v_supplier_name
            FROM suppliers
            WHERE id = v_supplier_id;
        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                RAISE_APPLICATION_ERROR(-20007, 'المورد غير موجود: ' || v_supplier_id);
        END;
        
        -- إضافة التوزيع
        INSERT INTO transfer_supplier_dist (
            transfer_id, supplier_id, amount, currency_code, created_by
        ) VALUES (
            p_transfer_id, v_supplier_id, v_supplier_amount, p_currency_code, p_user_id
        );
        
        -- تحديث رصيد المورد (مدين - زيادة)
        MERGE INTO CURRENT_BALANCES cb
        USING (SELECT 1 as dummy FROM dual) d
        ON (cb.entity_type_code = 'SUPPLIER' AND cb.entity_id = v_supplier_id AND cb.currency_code = p_currency_code)
        WHEN MATCHED THEN
            UPDATE SET
                debit_amount = debit_amount + v_supplier_amount,
                current_balance = current_balance + v_supplier_amount,
                total_transactions_count = total_transactions_count + 1,
                last_transaction_date = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP,
                updated_by = p_user_id
        WHEN NOT MATCHED THEN
            INSERT (
                entity_type_code, entity_id, currency_code,
                opening_balance, debit_amount, credit_amount, current_balance,
                total_transactions_count, last_transaction_date,
                created_at, updated_at, created_by, updated_by,
                description
            ) VALUES (
                'SUPPLIER', v_supplier_id, p_currency_code,
                0, v_supplier_amount, 0, v_supplier_amount,
                1, CURRENT_TIMESTAMP,
                CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, p_user_id, p_user_id,
                'رصيد المورد: ' || v_supplier_name
            );
        
        -- تسجيل النشاط
        INSERT INTO transfer_activity_log (
            transfer_id, activity_type, description, amount_after, 
            currency_code, entity_type, entity_id, entity_name, created_by
        ) VALUES (
            p_transfer_id, 'DISTRIBUTION', 'توزيع مبلغ على المورد: ' || v_supplier_name,
            v_supplier_amount, p_currency_code, 'SUPPLIER', v_supplier_id, v_supplier_name, p_user_id
        );
        
        v_total_distributed := v_total_distributed + v_supplier_amount;
        v_dist_count := v_dist_count + 1;
        
        -- الانتقال للكائن التالي
        v_start_pos := INSTR(v_json_text, '{', v_end_pos + 1);
        IF v_start_pos = 0 THEN
            EXIT;
        END IF;
    END LOOP;
    
    -- 4. التحقق من تطابق المبالغ
    IF ABS(v_total_distributed - p_total_amount) > 0.01 THEN
        RAISE_APPLICATION_ERROR(-20009, 'مجموع التوزيعات (' || v_total_distributed || ') لا يطابق مبلغ الحوالة (' || p_total_amount || ')');
    END IF;
    
    -- 5. تحديث رصيد الصراف (دائن - تقليل)
    UPDATE CURRENT_BALANCES SET
        credit_amount = credit_amount + p_total_amount,
        current_balance = current_balance - p_total_amount,
        total_transactions_count = total_transactions_count + 1,
        last_transaction_date = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP,
        updated_by = p_user_id
    WHERE entity_type_code = 'MONEY_CHANGER' 
    AND entity_id = p_money_changer_id 
    AND currency_code = p_currency_code;
    
    -- 6. تحديث حالة الحوالة
    UPDATE transfers SET
        status = 'executed',
        execution_date = CURRENT_TIMESTAMP,
        executed_by = p_user_id,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_transfer_id;
    
    -- تسجيل نجاح العملية
    INSERT INTO transfer_activity_log (
        transfer_id, activity_type, description, old_status, new_status,
        amount_after, currency_code, operation_details, created_by
    ) VALUES (
        p_transfer_id, 'EXECUTION', 'تم تنفيذ الحوالة بنجاح', 'approved', 'executed',
        p_total_amount, p_currency_code, 'Suppliers: ' || v_dist_count || ', Total: ' || v_total_distributed, p_user_id
    );
    
    -- تأكيد المعاملة
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        -- التراجع عن جميع التغييرات
        ROLLBACK TO start_transfer_execution;
        
        v_error_msg := SQLERRM;
        
        -- تسجيل الخطأ
        INSERT INTO transfer_activity_log (
            transfer_id, activity_type, description, error_message, created_by
        ) VALUES (
            p_transfer_id, 'ERROR', 'فشل في تنفيذ الحوالة', v_error_msg, p_user_id
        );
        
        COMMIT; -- تأكيد تسجيل الخطأ فقط
        
        -- إعادة رفع الخطأ
        RAISE;
END;
/

-- 3. إنشاء دالة للتحقق من رصيد الصراف
CREATE OR REPLACE FUNCTION CHECK_MC_BALANCE(
    p_money_changer_id IN NUMBER,
    p_amount IN NUMBER,
    p_currency_code IN VARCHAR2
) RETURN VARCHAR2 AS
    v_current_balance NUMBER;
    v_result VARCHAR2(4000);
BEGIN
    SELECT current_balance INTO v_current_balance
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'MONEY_CHANGER' 
    AND entity_id = p_money_changer_id 
    AND currency_code = p_currency_code;
    
    IF v_current_balance >= p_amount THEN
        v_result := 'OK: الرصيد كافي. الحالي: ' || v_current_balance || ', المطلوب: ' || p_amount;
    ELSE
        v_result := 'ERROR: الرصيد غير كافي. الحالي: ' || v_current_balance || ', المطلوب: ' || p_amount;
    END IF;
    
    RETURN v_result;
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN 'ERROR: رصيد الصراف غير موجود';
    WHEN OTHERS THEN
        RETURN 'ERROR: خطأ في التحقق: ' || SQLERRM;
END;
/

-- 4. إنشاء دالة للتحقق من توزيعات الموردين
CREATE OR REPLACE FUNCTION VALIDATE_SUPPLIER_DIST(
    p_transfer_id IN NUMBER,
    p_supplier_distributions IN CLOB
) RETURN VARCHAR2 AS
    v_total_distributed NUMBER := 0;
    v_transfer_amount NUMBER;
    v_result VARCHAR2(4000);
BEGIN
    SELECT NVL(net_amount_sent, NVL(net_amount_received, 0)) INTO v_transfer_amount
    FROM transfers
    WHERE id = p_transfer_id;
    
    -- معالجة مبسطة للتحقق من وجود البيانات
    IF p_supplier_distributions IS NULL OR LENGTH(p_supplier_distributions) < 10 THEN
        RETURN 'ERROR: توزيعات الموردين مطلوبة';
    END IF;
    
    -- التحقق الأساسي
    IF v_transfer_amount <= 0 THEN
        RETURN 'ERROR: مبلغ الحوالة غير صحيح';
    END IF;
    
    v_result := 'OK: التوزيعات جاهزة للمعالجة. مبلغ الحوالة: ' || v_transfer_amount;
    RETURN v_result;
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN 'ERROR: الحوالة غير موجودة';
    WHEN OTHERS THEN
        RETURN 'ERROR: خطأ في التحقق: ' || SQLERRM;
END;
/

-- 5. إنشاء إجراء إلغاء الحوالة
CREATE OR REPLACE PROCEDURE CANCEL_TRANSFER_ACCOUNTING(
    p_transfer_id IN NUMBER,
    p_user_id IN NUMBER DEFAULT 1,
    p_cancellation_reason IN VARCHAR2 DEFAULT NULL
) AS
    v_transfer_status VARCHAR2(50);
    v_currency VARCHAR2(10);
    v_money_changer_id NUMBER;
    v_total_amount NUMBER;
BEGIN
    SAVEPOINT start_transfer_cancellation;
    
    -- التحقق من حالة الحوالة
    SELECT status, NVL(net_amount_sent, net_amount_received), money_changer_bank_id
    INTO v_transfer_status, v_total_amount, v_money_changer_id
    FROM transfers
    WHERE id = p_transfer_id;
    
    IF v_transfer_status != 'executed' THEN
        RAISE_APPLICATION_ERROR(-20010, 'يمكن إلغاء الحوالات المنفذة فقط');
    END IF;
    
    -- عكس ترحيلات الموردين
    FOR rec IN (SELECT supplier_id, amount, currency_code 
                FROM transfer_supplier_dist 
                WHERE transfer_id = p_transfer_id) LOOP
        
        -- تقليل رصيد المورد
        UPDATE CURRENT_BALANCES SET
            credit_amount = credit_amount + rec.amount,
            current_balance = current_balance - rec.amount,
            total_transactions_count = total_transactions_count + 1,
            last_transaction_date = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP,
            updated_by = p_user_id
        WHERE entity_type_code = 'SUPPLIER' 
        AND entity_id = rec.supplier_id 
        AND currency_code = rec.currency_code;
        
        v_currency := rec.currency_code;
    END LOOP;
    
    -- عكس ترحيل الصراف (زيادة رصيده)
    UPDATE CURRENT_BALANCES SET
        debit_amount = debit_amount + v_total_amount,
        current_balance = current_balance + v_total_amount,
        total_transactions_count = total_transactions_count + 1,
        last_transaction_date = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP,
        updated_by = p_user_id
    WHERE entity_type_code = 'MONEY_CHANGER' 
    AND entity_id = v_money_changer_id 
    AND currency_code = v_currency;
    
    -- حذف التوزيعات
    DELETE FROM transfer_supplier_dist WHERE transfer_id = p_transfer_id;
    
    -- تحديث حالة الحوالة
    UPDATE transfers SET
        status = 'cancelled',
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_transfer_id;
    
    -- تسجيل الإلغاء
    INSERT INTO transfer_activity_log (
        transfer_id, activity_type, description, old_status, new_status,
        operation_details, created_by
    ) VALUES (
        p_transfer_id, 'CANCELLATION', 'تم إلغاء الحوالة', 'executed', 'cancelled',
        'السبب: ' || NVL(p_cancellation_reason, 'غير محدد'), p_user_id
    );
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK TO start_transfer_cancellation;
        
        INSERT INTO transfer_activity_log (
            transfer_id, activity_type, description, error_message, created_by
        ) VALUES (
            p_transfer_id, 'ERROR', 'فشل في إلغاء الحوالة', SQLERRM, p_user_id
        );
        
        COMMIT;
        RAISE;
END;
/

COMMIT;

SELECT 'Accounting procedures created successfully' as result FROM DUAL;
