{% extends "base.html" %}

{% block title %}لوحة أوامر التسليم للمخلص{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان الرئيسي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-file-contract text-primary me-2"></i>
                        لوحة أوامر التسليم للمخلص
                    </h2>
                    <p class="text-muted mb-0">إدارة ومتابعة أوامر التسليم للمخلصين الجمركيين</p>
                </div>
                <div>
                    <a href="{{ url_for('shipments.create_delivery_order') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إنشاء أمر تسليم جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات أوامر التسليم -->
    <div class="row mb-4">
        {% if order_stats %}
            {% for stat in order_stats %}
            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="mb-2">
                            {% if stat[0] == 'draft' %}
                                <i class="fas fa-edit fa-2x text-secondary"></i>
                            {% elif stat[0] == 'sent' %}
                                <i class="fas fa-paper-plane fa-2x text-info"></i>
                            {% elif stat[0] == 'in_progress' %}
                                <i class="fas fa-cog fa-spin fa-2x text-warning"></i>
                            {% elif stat[0] == 'completed' %}
                                <i class="fas fa-check-circle fa-2x text-success"></i>
                            {% elif stat[0] == 'cancelled' %}
                                <i class="fas fa-times-circle fa-2x text-danger"></i>
                            {% else %}
                                <i class="fas fa-question-circle fa-2x text-muted"></i>
                            {% endif %}
                        </div>
                        <h4 class="mb-1">{{ stat[1] or 0 }}</h4>
                        <p class="text-muted mb-0 small">
                            {% if stat[0] == 'draft' %}
                                مسودة
                            {% elif stat[0] == 'sent' %}
                                مرسل
                            {% elif stat[0] == 'in_progress' %}
                                قيد التنفيذ
                            {% elif stat[0] == 'completed' %}
                                مكتمل
                            {% elif stat[0] == 'cancelled' %}
                                ملغي
                            {% else %}
                                {{ stat[0] }}
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد إحصائيات متاحة حالياً
                </div>
            </div>
        {% endif %}
    </div>

    <!-- أوامر التسليم الحديثة والمتأخرة -->
    <div class="row">
        <!-- أوامر التسليم الحديثة -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        أوامر التسليم الحديثة
                        <span class="badge bg-primary ms-2">{{ recent_orders|length }}</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الأمر</th>
                                    <th>رقم التتبع</th>
                                    <th>المخلص</th>
                                    <th class="d-none d-md-table-cell">الحالة</th>
                                    <th class="d-none d-lg-table-cell">الأولوية</th>
                                    <th class="d-none d-lg-table-cell">تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if recent_orders %}
                                    {% for order in recent_orders %}
                                    <tr>
                                        <td>
                                            <strong>{{ order[1] }}</strong>
                                            <!-- معلومات إضافية للجوال -->
                                            <div class="d-md-none mobile-info mt-1">
                                                {% if order[2] == 'draft' %}
                                                    <span class="badge bg-secondary">مسودة</span>
                                                {% elif order[2] == 'sent' %}
                                                    <span class="badge bg-info">مرسل</span>
                                                {% elif order[2] == 'in_progress' %}
                                                    <span class="badge bg-warning">قيد التنفيذ</span>
                                                {% elif order[2] == 'completed' %}
                                                    <span class="badge bg-success">مكتمل</span>
                                                {% elif order[2] == 'cancelled' %}
                                                    <span class="badge bg-danger">ملغي</span>
                                                {% endif %}

                                                {% if order[3] == 'urgent' %}
                                                    <span class="badge bg-danger">عاجل</span>
                                                {% elif order[3] == 'high' %}
                                                    <span class="badge bg-warning">عالي</span>
                                                {% elif order[3] == 'normal' %}
                                                    <span class="badge bg-primary">عادي</span>
                                                {% elif order[3] == 'low' %}
                                                    <span class="badge bg-secondary">منخفض</span>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            {{ order[8] or 'غير محدد' }}
                                            <br>
                                            <small class="text-muted">{{ order[9] or '' }}</small>
                                        </td>
                                        <td>
                                            <span class="text-truncate-mobile">{{ order[6] or 'غير محدد' }}</span>
                                            <br>
                                            <small class="text-muted d-none d-md-inline">{{ order[7] or '' }}</small>
                                        </td>
                                        <td class="d-none d-md-table-cell">
                                            {% if order[2] == 'draft' %}
                                                <span class="badge bg-secondary">مسودة</span>
                                            {% elif order[2] == 'sent' %}
                                                <span class="badge bg-info">مرسل</span>
                                            {% elif order[2] == 'in_progress' %}
                                                <span class="badge bg-warning">قيد التنفيذ</span>
                                            {% elif order[2] == 'completed' %}
                                                <span class="badge bg-success">مكتمل</span>
                                            {% elif order[2] == 'cancelled' %}
                                                <span class="badge bg-danger">ملغي</span>
                                            {% else %}
                                                <span class="badge bg-light text-dark">{{ order[2] }}</span>
                                            {% endif %}
                                        </td>
                                        <td class="d-none d-lg-table-cell">
                                            {% if order[3] == 'urgent' %}
                                                <span class="badge bg-danger">عاجل</span>
                                            {% elif order[3] == 'high' %}
                                                <span class="badge bg-warning">عالي</span>
                                            {% elif order[3] == 'normal' %}
                                                <span class="badge bg-primary">عادي</span>
                                            {% elif order[3] == 'low' %}
                                                <span class="badge bg-secondary">منخفض</span>
                                            {% else %}
                                                <span class="badge bg-light text-dark">{{ order[3] }}</span>
                                            {% endif %}
                                        </td>
                                        <td class="d-none d-lg-table-cell">
                                            <small class="text-muted">
                                                {{ order[4].strftime('%Y-%m-%d') if order[4] else 'غير محدد' }}
                                            </small>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1 flex-wrap">
                                                <!-- الأزرار الرئيسية -->
                                                <button class="btn btn-success btn-sm"
                                                        onclick="sendWhatsApp({{ order[0] }})" title="إرسال رسالة واتساب">
                                                    <i class="fab fa-whatsapp d-md-none"></i>
                                                    <span class="d-none d-md-inline"><i class="fab fa-whatsapp me-1"></i>واتساب</span>
                                                </button>

                                                <button class="btn btn-info btn-sm"
                                                        onclick="generatePDF({{ order[0] }})" title="معاينة PDF">
                                                    <i class="fas fa-eye d-md-none"></i>
                                                    <span class="d-none d-md-inline"><i class="fas fa-eye me-1"></i>معاينة</span>
                                                </button>

                                                <button class="btn btn-warning btn-sm d-none d-lg-inline-block"
                                                        onclick="openDownloadsFolder()" title="فتح مجلد التحميلات">
                                                    <i class="fas fa-folder-open me-1"></i>مجلد
                                                </button>

                                                <!-- تم إخفاء زر اختبار الواتساب -->
                                                <!-- <button class="btn btn-secondary btn-sm"
                                                        onclick="testWhatsApp()" title="اختبار فتح واتساب">
                                                    <i class="fab fa-whatsapp me-1"></i>اختبار
                                                </button> -->

                                                <!-- الأزرار الثانوية -->
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary btn-sm"
                                                            onclick="viewOrder({{ order[0] }})" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </button>

                                                    {% if order[2] in ['draft', 'sent'] %}
                                                    <button class="btn btn-outline-secondary btn-sm"
                                                            onclick="editOrder({{ order[0] }})" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    {% endif %}

                                                    <button class="btn btn-outline-danger btn-sm"
                                                            onclick="deleteOrder({{ order[0] }}, '{{ order[1] }}')" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>


                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <i class="fas fa-file-contract fa-2x text-muted mb-2"></i>
                                            <br>
                                            <span class="text-muted">لا توجد أوامر تسليم حديثة</span>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- أوامر التسليم المتأخرة -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        أوامر متأخرة
                        <span class="badge bg-warning ms-2">{{ overdue_orders|length }}</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if overdue_orders %}
                        {% for order in overdue_orders %}
                        <div class="border-bottom p-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ order[1] }}</h6>
                                    <p class="text-muted mb-1 small">{{ order[5] or 'غير محدد' }}</p>
                                    <p class="text-muted mb-0 small">المخلص: {{ order[4] or 'غير محدد' }}</p>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-danger">{{ order[6] }} يوم</span>
                                    <br>
                                    <small class="text-muted">متأخر</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <br>
                            <span class="text-muted">لا توجد أوامر متأخرة</span>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewOrder(orderId) {
    console.log('👁️ عرض تفاصيل الأمر:', orderId);

    if (!orderId) {
        alert('خطأ: معرف الأمر غير صحيح');
        return;
    }

    // إظهار رسالة تحميل
    showMessage('جاري تحميل تفاصيل الأمر...', 'info');

    // جلب تفاصيل الأمر
    fetch(`/shipments/api/delivery-orders/${orderId}`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showOrderDetailsModal(result.order);
        } else {
            showMessage('خطأ في جلب تفاصيل الأمر: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الشبكة:', error);
        showMessage('حدث خطأ في الاتصال بالخادم', 'danger');
    });
}

function editOrder(orderId) {
    console.log('✏️ تعديل الأمر:', orderId);

    if (!orderId) {
        alert('خطأ: معرف الأمر غير صحيح');
        return;
    }

    // إظهار رسالة تحميل
    showMessage('جاري تحميل بيانات الأمر للتعديل...', 'info');

    // جلب تفاصيل الأمر
    fetch(`/shipments/api/delivery-orders/${orderId}`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showEditOrderModal(result.order);
        } else {
            showMessage('خطأ في جلب بيانات الأمر: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الشبكة:', error);
        showMessage('حدث خطأ في الاتصال بالخادم', 'danger');
    });
}

// وظيفة عرض تفاصيل الأمر
function showOrderDetailsModal(order) {
    const statusBadge = getStatusBadge(order.order_status);
    const priorityBadge = getPriorityBadge(order.priority);

    const modalContent = `
        <div class="modal fade" id="orderDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-file-contract me-2"></i>
                            تفاصيل أمر التسليم: ${order.order_number}
                        </h5>
                        <button type="button" class="btn-close btn-close-white" onclick="closeModal('orderDetailsModal')"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <!-- معلومات الأمر الأساسية -->
                            <div class="col-12 mb-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-info-circle me-1"></i>
                                    معلومات الأمر
                                </h6>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <strong>رقم الأمر:</strong>
                                        <span class="badge bg-primary">${order.order_number}</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>الحالة:</strong> ${statusBadge}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>الأولوية:</strong> ${priorityBadge}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>تاريخ الإنشاء:</strong> ${order.created_date || 'غير محدد'}
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات الشحنة -->
                            <div class="col-12 mb-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-ship me-1"></i>
                                    معلومات الشحنة
                                </h6>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <strong>رقم التتبع:</strong>
                                        <span class="badge bg-info">${order.tracking_number || 'غير محدد'}</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>رقم الحجز:</strong> ${order.booking_number || 'غير محدد'}
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات المخلص -->
                            <div class="col-12 mb-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-user-tie me-1"></i>
                                    معلومات المخلص
                                </h6>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <strong>اسم المخلص:</strong> ${order.agent_name || 'غير محدد'}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>الشركة:</strong> ${order.company_name || 'غير محدد'}
                                    </div>
                                </div>
                            </div>

                            <!-- تفاصيل التسليم -->
                            <div class="col-12 mb-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    تفاصيل التسليم
                                </h6>
                                <div class="row">
                                    <div class="col-12 mb-2">
                                        <strong>موقع التسليم:</strong><br>
                                        <div class="bg-light p-2 rounded">${order.delivery_location || 'غير محدد'}</div>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>التاريخ المتوقع:</strong> ${order.expected_completion_date || 'غير محدد'}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>التكلفة المقدرة:</strong>
                                        ${order.estimated_cost ? order.estimated_cost + ' ' + (order.currency || 'SAR') : 'غير محدد'}
                                    </div>
                                </div>
                                ${order.special_instructions ? `
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <strong>تعليمات خاصة:</strong><br>
                                        <div class="bg-light p-2 rounded">${order.special_instructions}</div>
                                    </div>
                                </div>
                                ` : ''}
                            </div>

                            <!-- معلومات الاتصال -->
                            ${order.contact_person || order.contact_phone || order.contact_email ? `
                            <div class="col-12 mb-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-phone me-1"></i>
                                    معلومات الاتصال
                                </h6>
                                <div class="row">
                                    ${order.contact_person ? `<div class="col-md-4 mb-2"><strong>الشخص المسؤول:</strong> ${order.contact_person}</div>` : ''}
                                    ${order.contact_phone ? `<div class="col-md-4 mb-2"><strong>الهاتف:</strong> ${order.contact_phone}</div>` : ''}
                                    ${order.contact_email ? `<div class="col-md-4 mb-2"><strong>البريد الإلكتروني:</strong> ${order.contact_email}</div>` : ''}
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('orderDetailsModal')">
                            <i class="fas fa-times me-1"></i>
                            إغلاق
                        </button>
                        ${order.order_status === 'draft' ? `
                        <button type="button" class="btn btn-primary" onclick="editOrder(${order.id})">
                            <i class="fas fa-edit me-1"></i>
                            تعديل
                        </button>
                        ` : ''}
                        <button type="button" class="btn btn-info" onclick="printOrder(${order.id})">
                            <i class="fas fa-print me-1"></i>
                            طباعة
                        </button>
                        <a href="/shipments/delivery-order-pdf/${order.id}" target="_blank" class="btn btn-danger">
                            <i class="fas fa-file-pdf me-1"></i>
                            تحميل PDF
                        </a>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة أي نافذة سابقة
    const existingModal = document.getElementById('orderDetailsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إضافة النافذة الجديدة
    document.body.insertAdjacentHTML('beforeend', modalContent);

    // فتح النافذة
    const modal = document.getElementById('orderDetailsModal');
    modal.style.display = 'block';
    modal.classList.add('show');
    document.body.classList.add('modal-open');

    // إضافة backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop fade show';
    backdrop.id = 'orderDetailsBackdrop';
    document.body.appendChild(backdrop);
}

// وظيفة عرض نافذة التعديل
function showEditOrderModal(order) {
    const modalContent = `
        <div class="modal fade" id="editOrderModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-edit me-2"></i>
                            تعديل أمر التسليم: ${order.order_number}
                        </h5>
                        <button type="button" class="btn-close btn-close-white" onclick="closeModal('editOrderModal')"></button>
                    </div>
                    <div class="modal-body">
                        <form id="editOrderForm">
                            <input type="hidden" id="editOrderId" value="${order.id}">

                            <div class="row">
                                <!-- معلومات الفرع والشحنة -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم الفرع</label>
                                    <select class="form-select" id="editBranchId" disabled>
                                        <option value="">جاري التحميل...</option>
                                    </select>
                                    <small class="text-muted">لا يمكن تغيير الفرع بعد الإنشاء</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الشحنة</label>
                                    <input type="text" class="form-control" id="editShipmentInfo" readonly>
                                    <small class="text-muted">لا يمكن تغيير الشحنة بعد الإنشاء</small>
                                </div>

                                <!-- المخلص الجمركي -->
                                <div class="col-12 mb-3">
                                    <label class="form-label">المخلص الجمركي</label>
                                    <select class="form-select" id="editCustomsAgentId">
                                        <option value="">جاري التحميل...</option>
                                    </select>
                                </div>

                                <!-- موقع التسليم -->
                                <div class="col-12 mb-3">
                                    <label class="form-label">موقع التسليم <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="editDeliveryLocation" rows="2" required>${order.delivery_location || ''}</textarea>
                                </div>

                                <!-- التاريخ المتوقع والأولوية -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">التاريخ المتوقع للإنجاز <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="editExpectedDate" value="${order.expected_completion_date || ''}" required>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الأولوية</label>
                                    <select class="form-select" id="editPriority">
                                        <option value="low" ${order.priority === 'low' ? 'selected' : ''}>منخفض</option>
                                        <option value="normal" ${order.priority === 'normal' ? 'selected' : ''}>عادي</option>
                                        <option value="high" ${order.priority === 'high' ? 'selected' : ''}>عالي</option>
                                        <option value="urgent" ${order.priority === 'urgent' ? 'selected' : ''}>عاجل</option>
                                    </select>
                                </div>

                                <!-- التكلفة والعملة -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">التكلفة المقدرة</label>
                                    <input type="number" class="form-control" id="editEstimatedCost" step="0.01" value="${order.estimated_cost || ''}">
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">العملة</label>
                                    <select class="form-select" id="editCurrency">
                                        <option value="SAR" ${order.currency === 'SAR' ? 'selected' : ''}>ريال سعودي</option>
                                        <option value="USD" ${order.currency === 'USD' ? 'selected' : ''}>دولار أمريكي</option>
                                        <option value="EUR" ${order.currency === 'EUR' ? 'selected' : ''}>يورو</option>
                                    </select>
                                </div>

                                <!-- معلومات الاتصال -->
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">الشخص المسؤول</label>
                                    <input type="text" class="form-control" id="editContactPerson" value="${order.contact_person || ''}">
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="editContactPhone" value="${order.contact_phone || ''}">
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="editContactEmail" value="${order.contact_email || ''}">
                                </div>

                                <!-- التعليمات الخاصة -->
                                <div class="col-12 mb-3">
                                    <label class="form-label">تعليمات خاصة</label>
                                    <textarea class="form-control" id="editSpecialInstructions" rows="3">${order.special_instructions || ''}</textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('editOrderModal')">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </button>
                        <button type="button" class="btn btn-success" onclick="saveOrderChanges()">
                            <i class="fas fa-save me-1"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة أي نافذة سابقة
    const existingModal = document.getElementById('editOrderModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إضافة النافذة الجديدة
    document.body.insertAdjacentHTML('beforeend', modalContent);

    // فتح النافذة
    const modal = document.getElementById('editOrderModal');
    modal.style.display = 'block';
    modal.classList.add('show');
    document.body.classList.add('modal-open');

    // إضافة backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop fade show';
    backdrop.id = 'editOrderBackdrop';
    document.body.appendChild(backdrop);

    // تحميل البيانات المطلوبة
    loadEditModalData(order);
}

// تحميل البيانات المطلوبة لنافذة التعديل
async function loadEditModalData(order) {
    try {
        console.log('🔄 بدء تحميل بيانات نافذة التعديل...');

        // تحميل الفروع
        try {
            const branchesResponse = await fetch('/settings/api/branches');

            if (!branchesResponse.ok) {
                throw new Error(`HTTP ${branchesResponse.status}: ${branchesResponse.statusText}`);
            }

            const branchesData = await branchesResponse.json();

            if (branchesData.success) {
                const branchSelect = document.getElementById('editBranchId');
                branchSelect.innerHTML = '<option value="">اختر الفرع...</option>';

                branchesData.branches.forEach(branch => {
                    const option = document.createElement('option');
                    option.value = branch.brn_no;
                    option.textContent = `${branch.brn_no} - ${branch.brn_lname}`;
                    if (branch.brn_no == order.branch_id) {
                        option.selected = true;
                    }
                    branchSelect.appendChild(option);
                });
                console.log('✅ تم تحميل الفروع');
            } else {
                console.error('❌ فشل في تحميل الفروع:', branchesData.message);
                document.getElementById('editBranchId').innerHTML = '<option value="">خطأ في تحميل الفروع</option>';
            }
        } catch (branchError) {
            console.error('❌ خطأ في تحميل الفروع:', branchError);
            document.getElementById('editBranchId').innerHTML = '<option value="">خطأ في تحميل الفروع</option>';
        }

        // تحميل المخلصين الجمركيين
        try {
            const agentsResponse = await fetch('/shipments/api/customs-agents');

            if (!agentsResponse.ok) {
                throw new Error(`HTTP ${agentsResponse.status}: ${agentsResponse.statusText}`);
            }

            const agentsData = await agentsResponse.json();

            if (agentsData.success) {
                const agentSelect = document.getElementById('editCustomsAgentId');
                agentSelect.innerHTML = '<option value="">اختر المخلص...</option>';

                agentsData.agents.forEach(agent => {
                    const option = document.createElement('option');
                    option.value = agent.id;
                    option.textContent = `${agent.name} - ${agent.license_number}`;
                    if (agent.id == order.customs_agent_id) {
                        option.selected = true;
                    }
                    agentSelect.appendChild(option);
                });
                console.log('✅ تم تحميل المخلصين الجمركيين');
            } else {
                console.error('❌ فشل في تحميل المخلصين:', agentsData.message);
                document.getElementById('editCustomsAgentId').innerHTML = '<option value="">خطأ في تحميل المخلصين</option>';
            }
        } catch (agentError) {
            console.error('❌ خطأ في تحميل المخلصين:', agentError);
            document.getElementById('editCustomsAgentId').innerHTML = '<option value="">خطأ في تحميل المخلصين</option>';
        }

        // عرض معلومات الشحنة
        const shipmentInfo = `${order.tracking_number || 'غير محدد'} - ${order.booking_number || 'غير محدد'}`;
        document.getElementById('editShipmentInfo').value = shipmentInfo;

        console.log('✅ تم تحميل بيانات نافذة التعديل بنجاح');

    } catch (error) {
        console.error('❌ خطأ عام في تحميل البيانات:', error);
        showMessage('خطأ في تحميل البيانات المطلوبة: ' + error.message, 'danger');
    }
}

// وظيفة حفظ التغييرات
function saveOrderChanges() {
    const orderId = document.getElementById('editOrderId').value;

    // جمع البيانات
    const data = {
        branch_id: document.getElementById('editBranchId').value,
        customs_agent_id: document.getElementById('editCustomsAgentId').value,
        delivery_location: document.getElementById('editDeliveryLocation').value.trim(),
        expected_completion_date: document.getElementById('editExpectedDate').value,
        priority: document.getElementById('editPriority').value,
        estimated_cost: document.getElementById('editEstimatedCost').value,
        currency: document.getElementById('editCurrency').value,
        contact_person: document.getElementById('editContactPerson').value.trim(),
        contact_phone: document.getElementById('editContactPhone').value.trim(),
        contact_email: document.getElementById('editContactEmail').value.trim(),
        special_instructions: document.getElementById('editSpecialInstructions').value.trim()
    };

    // التحقق من البيانات المطلوبة
    if (!data.customs_agent_id || !data.delivery_location || !data.expected_completion_date) {
        showMessage('يرجى ملء جميع الحقول المطلوبة (المخلص، موقع التسليم، التاريخ المتوقع)', 'warning');
        return;
    }

    // إظهار مؤشر التحميل
    const saveBtn = document.querySelector('button[onclick="saveOrderChanges()"]');
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الحفظ...';
    saveBtn.disabled = true;

    // إرسال البيانات
    fetch(`/shipments/api/delivery-orders/${orderId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showMessage('تم تحديث أمر التسليم بنجاح', 'success');
            closeModal('editOrderModal');

            // إعادة تحميل الصفحة بعد ثانيتين
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showMessage('خطأ في تحديث أمر التسليم: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الشبكة:', error);
        showMessage('حدث خطأ في الاتصال بالخادم', 'danger');
    })
    .finally(() => {
        // إعادة تعيين الزر
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    });
}

// وظائف مساعدة
function getStatusBadge(status) {
    const statusMap = {
        'draft': '<span class="badge bg-secondary">مسودة</span>',
        'sent': '<span class="badge bg-primary">مرسل</span>',
        'in_progress': '<span class="badge bg-warning">قيد التنفيذ</span>',
        'completed': '<span class="badge bg-success">مكتمل</span>',
        'cancelled': '<span class="badge bg-danger">ملغي</span>'
    };
    return statusMap[status] || `<span class="badge bg-light text-dark">${status}</span>`;
}

function getPriorityBadge(priority) {
    const priorityMap = {
        'low': '<span class="badge bg-secondary">منخفض</span>',
        'normal': '<span class="badge bg-primary">عادي</span>',
        'high': '<span class="badge bg-warning">عالي</span>',
        'urgent': '<span class="badge bg-danger">عاجل</span>'
    };
    return priorityMap[priority] || `<span class="badge bg-light text-dark">${priority}</span>`;
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('show');
        modal.remove();
    }

    // إزالة backdrop
    const backdrop = document.getElementById(modalId.replace('Modal', 'Backdrop'));
    if (backdrop) {
        backdrop.remove();
    }

    // إزالة classes من body
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
}

function deleteOrder(orderId) {
    console.log('🗑️ حذف أمر التسليم:', orderId);

    if (!orderId) {
        alert('خطأ: معرف الأمر غير صحيح');
        return;
    }

    // تأكيد الحذف
    if (!confirm('هل أنت متأكد من حذف أمر التسليم؟\n\nهذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }

    // إظهار رسالة تحميل
    showMessage('جاري حذف أمر التسليم...', 'info');

    // إرسال طلب الحذف
    fetch(`/shipments/api/delivery-orders/${orderId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showMessage(result.message, 'success');

            // إعادة تحميل الصفحة بعد ثانيتين
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showMessage('خطأ في حذف أمر التسليم: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الشبكة:', error);
        showMessage('حدث خطأ في الاتصال بالخادم', 'danger');
    });
}

function trackOrder(orderId) {
    console.log('📍 تتبع أمر التسليم:', orderId);
    showMessage('سيتم تطوير ميزة التتبع قريباً', 'info');
}

function cancelOrder(orderId) {
    console.log('❌ إلغاء أمر التسليم:', orderId);

    if (!orderId) {
        alert('خطأ: معرف الأمر غير صحيح');
        return;
    }

    // تأكيد الإلغاء
    const reason = prompt('يرجى إدخال سبب الإلغاء:');
    if (!reason || reason.trim() === '') {
        return;
    }

    // إظهار رسالة تحميل
    showMessage('جاري إلغاء أمر التسليم...', 'info');

    // إرسال طلب الإلغاء
    fetch(`/shipments/api/delivery-orders/${orderId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            order_status: 'cancelled',
            cancellation_reason: reason.trim()
        })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showMessage('تم إلغاء أمر التسليم بنجاح', 'success');
            setTimeout(() => location.reload(), 2000);
        } else {
            showMessage('خطأ في إلغاء أمر التسليم: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الشبكة:', error);
        showMessage('حدث خطأ في الاتصال بالخادم', 'danger');
    });
}

function completeOrder(orderId) {
    console.log('✅ إكمال أمر التسليم:', orderId);

    if (!orderId) {
        alert('خطأ: معرف الأمر غير صحيح');
        return;
    }

    // تأكيد الإكمال
    if (!confirm('هل أنت متأكد من إكمال أمر التسليم؟')) {
        return;
    }

    // إظهار رسالة تحميل
    showMessage('جاري إكمال أمر التسليم...', 'info');

    // إرسال طلب الإكمال
    fetch(`/shipments/api/delivery-orders/${orderId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            order_status: 'completed',
            actual_completion_date: new Date().toISOString().split('T')[0]
        })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showMessage('تم إكمال أمر التسليم بنجاح', 'success');
            setTimeout(() => location.reload(), 2000);
        } else {
            showMessage('خطأ في إكمال أمر التسليم: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الشبكة:', error);
        showMessage('حدث خطأ في الاتصال بالخادم', 'danger');
    });
}

function previewAndSend(orderId) {
    console.log('📋 معاينة وإرسال أمر التسليم:', orderId);

    if (!orderId) {
        alert('خطأ: معرف الأمر غير صحيح');
        return;
    }

    // الانتقال إلى صفحة المعاينة
    window.location.href = `/shipments/delivery-order-preview/${orderId}`;
}

function printOrder(orderId) {
    console.log('🖨️ طباعة أمر التسليم:', orderId);
    showMessage('سيتم تطوير ميزة الطباعة قريباً', 'info');
}

// دالة حذف أمر التسليم
function deleteOrder(orderId, orderNumber) {
    console.log('🗑️ طلب حذف أمر التسليم:', orderId, orderNumber);

    // التأكد من الحذف
    const confirmMessage = `هل أنت متأكد من حذف أمر التسليم رقم: ${orderNumber}؟\n\nهذا الإجراء لا يمكن التراجع عنه.`;

    if (confirm(confirmMessage)) {
        // إظهار مؤشر التحميل
        const deleteBtn = event.target.closest('button');
        const originalContent = deleteBtn.innerHTML;
        deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        deleteBtn.disabled = true;

        // إرسال طلب الحذف
        fetch(`/shipments/api/delivery-orders/${orderId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('📋 استجابة الحذف:', data);

            if (data.success) {
                // إظهار رسالة نجاح
                showMessage('تم حذف أمر التسليم بنجاح', 'success');

                // إزالة الصف من الجدول مع تأثير بصري
                const row = deleteBtn.closest('tr');
                row.style.transition = 'all 0.3s ease';
                row.style.opacity = '0';
                row.style.transform = 'translateX(100px)';

                setTimeout(() => {
                    row.remove();
                    updateOrdersCount();
                }, 300);

            } else {
                // إظهار رسالة خطأ
                showMessage(data.message || 'حدث خطأ أثناء حذف أمر التسليم', 'danger');
                // إعادة الزر لحالته الأصلية
                deleteBtn.innerHTML = originalContent;
                deleteBtn.disabled = false;
            }
        })
        .catch(error => {
            console.error('❌ خطأ في حذف الأمر:', error);
            showMessage('حدث خطأ في الاتصال بالخادم', 'danger');
            // إعادة الزر لحالته الأصلية
            deleteBtn.innerHTML = originalContent;
            deleteBtn.disabled = false;
        });
    }
}

// دالة تحديث عداد الأوامر
function updateOrdersCount() {
    const tableBody = document.querySelector('#ordersTable tbody');
    const rows = tableBody.querySelectorAll('tr:not(.no-data)');
    const count = rows.length;

    console.log('📊 تحديث عداد الأوامر:', count);

    // تحديث العداد في العنوان إذا وجد
    const countElements = document.querySelectorAll('.badge');
    countElements.forEach(badge => {
        if (badge.textContent.match(/^\d+$/)) {
            badge.textContent = count;
        }
    });

    // إظهار رسالة "لا توجد أوامر" إذا كان العدد صفر
    if (count === 0) {
        tableBody.innerHTML = `
            <tr class="no-data">
                <td colspan="7" class="text-center py-4">
                    <i class="fas fa-file-contract fa-2x text-muted mb-2"></i>
                    <br>
                    <span class="text-muted">لا توجد أوامر تسليم</span>
                </td>
            </tr>
        `;
    }
}

function showMessage(message, type = 'info') {
    // إنشاء عنصر التنبيه
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إضافة التنبيه للصفحة
    document.body.appendChild(alertDiv);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function trackOrder(orderId) {
    console.log('تتبع الأمر:', orderId);
    // سيتم تطوير هذه الوظيفة لاحقاً
    alert('سيتم تطوير التتبع قريباً');
}

// دالة إنشاء PDF
function generatePDF(orderId) {
    console.log('📄 إنشاء PDF للأمر:', orderId);

    if (!orderId) {
        alert('خطأ: معرف الأمر غير صحيح');
        return;
    }

    // إظهار رسالة تحميل
    showMessage('جاري إنشاء ملف PDF...', 'info');

    // فتح PDF في نافذة جديدة
    const pdfUrl = `/shipments/delivery-order-viewer/${orderId}`;
    window.open(pdfUrl, '_blank');

    showMessage('تم إنشاء ملف PDF بنجاح!', 'success');
}

// دالة إرسال واتساب
function sendWhatsApp(orderId) {
    if (!orderId) {
        alert('خطأ: معرف الأمر غير صحيح');
        return;
    }

    if (!confirm('هل تريد إرسال أمر التسليم عبر واتساب؟\n\nسيتم إنشاء ملف PDF وفتح واتساب ويب.')) {
        return;
    }

    showMessage('جاري إنشاء ملف PDF وفتح واتساب...', 'info');

    fetch(`/shipments/send-whatsapp-delivery-order/${orderId}`, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'}
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('تم إرسال رسالة واتساب بنجاح!', 'success');

            const instructions = `✅ تم إرسال رسالة واتساب بنجاح!

👤 المخلص: ${data.agent_name}
📱 الرقم: ${data.phone}

💡 لإرفاق أمر التسليم:
1️⃣ اضغط زر "معاينة" لفتح أمر التسليم
2️⃣ اضغط Ctrl+P واختر "حفظ كـ PDF"
3️⃣ ارجع لواتساب وارفق الملف المحفوظ

📋 خطوات الإرسال:
1. سيتم فتح واتساب ويب الآن مع الرسالة جاهزة
2. 📎 اضغط على أيقونة المرفقات في واتساب
3. 📄 اختر "مستند" أو "Document"
4. 🔍 اختر ملف PDF المحفوظ
5. ✅ اضغط إرسال

💡 جاري فتح واتساب ويب...`;

            alert(instructions);

            // تسجيل الرابط للتحقق
            console.log('WhatsApp URL:', data.whatsapp_url);

            // فتح واتساب ويب من المتصفح بطرق متعددة
            try {
                // الطريقة الأولى: window.open
                const whatsappWindow = window.open(data.whatsapp_url, '_blank', 'noopener,noreferrer');

                // التحقق من نجاح الفتح
                if (!whatsappWindow) {
                    // الطريقة الثانية: تغيير الموقع
                    window.location.href = data.whatsapp_url;
                } else {
                    console.log('WhatsApp opened successfully');
                }
            } catch (error) {
                console.error('Error opening WhatsApp:', error);
                // الطريقة الثالثة: إنشاء رابط وضغطه
                const link = document.createElement('a');
                link.href = data.whatsapp_url;
                link.target = '_blank';
                link.rel = 'noopener noreferrer';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }

            // تأخير إعادة التحميل
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showMessage('خطأ في الإنشاء: ' + data.message, 'error');
            alert('خطأ في الإنشاء: ' + data.message);
        }
    })
    .catch(error => {
        showMessage('حدث خطأ في العملية', 'error');
        alert('حدث خطأ في العملية');
    });
}



// دالة فتح مجلد التحميلات
function openDownloadsFolder() {
    const message = `📁 ملفات PDF محفوظة في مجلد التحميلات:

🖥️ على ويندوز:
C:\\Users\\<USER>\\Downloads

🍎 على ماك:
/Users/<USER>/Downloads

💡 نصائح:
• ابحث عن ملفات تبدأ بـ "delivery_order_"
• يمكنك فتح مجلد التحميلات من متصفح الملفات
• الملفات مرتبة حسب التاريخ

🔍 للبحث السريع:
اكتب "delivery_order" في مربع البحث`;

    alert(message);
}

// دالة اختبار فتح واتساب
function testWhatsApp() {
    const testUrl = 'https://web.whatsapp.com/';

    console.log('Testing WhatsApp URL:', testUrl);

    try {
        const whatsappWindow = window.open(testUrl, '_blank', 'noopener,noreferrer');

        if (!whatsappWindow) {
            alert('❌ فشل فتح واتساب ويب!\n\nالمتصفح يحجب النوافذ المنبثقة.\nيرجى السماح للنوافذ المنبثقة لهذا الموقع.');
        } else {
            alert('✅ تم فتح واتساب ويب بنجاح!\n\nإذا لم تظهر النافذة، تحقق من النوافذ المنبثقة.');
        }
    } catch (error) {
        console.error('Error testing WhatsApp:', error);
        alert('❌ خطأ في فتح واتساب ويب:\n' + error.message);
    }
}

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    console.log('📋 تم تحميل لوحة أوامر التسليم');
});
</script>

<!-- CSS متجاوب للجوال -->
<style>
/* تحسينات الجوال */
@media (max-width: 768px) {
    /* تقليل الهوامش والحشو */
    .container-fluid {
        padding: 10px !important;
    }

    /* تحسين العنوان الرئيسي */
    .row.mb-4 .col-12 .d-flex {
        flex-direction: column !important;
        gap: 15px;
    }

    .row.mb-4 h2 {
        font-size: 1.5rem !important;
        margin-bottom: 5px !important;
    }

    .row.mb-4 p {
        font-size: 0.9rem !important;
    }

    /* تحسين الأزرار */
    .btn {
        font-size: 0.9rem !important;
        padding: 8px 16px !important;
    }

    /* تحسين البطاقات الإحصائية */
    .row.mb-4:nth-child(2) .col-xl-2 {
        margin-bottom: 10px !important;
    }

    .card-body {
        padding: 15px !important;
    }

    .card-body h4 {
        font-size: 1.2rem !important;
    }

    .card-body small {
        font-size: 0.8rem !important;
    }

    /* تحسين الجداول */
    .table-responsive {
        font-size: 0.85rem !important;
    }

    .table th,
    .table td {
        padding: 8px 6px !important;
        white-space: nowrap;
    }

    /* إخفاء بعض الأعمدة في الجوال */
    .table th:nth-child(n+5),
    .table td:nth-child(n+5) {
        display: none !important;
    }

    /* تحسين أزرار الإجراءات */
    .btn-sm {
        font-size: 0.75rem !important;
        padding: 4px 8px !important;
    }

    /* تحسين النماذج */
    .form-control {
        font-size: 0.9rem !important;
        padding: 8px 12px !important;
    }

    .form-label {
        font-size: 0.9rem !important;
        margin-bottom: 5px !important;
    }

    /* تحسين الـ modals */
    .modal-dialog {
        margin: 10px !important;
        max-width: calc(100% - 20px) !important;
    }

    .modal-body {
        padding: 15px !important;
    }

    .modal-title {
        font-size: 1.1rem !important;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 576px) {
    .container-fluid {
        padding: 5px !important;
    }

    .row.mb-4 h2 {
        font-size: 1.3rem !important;
    }

    /* إخفاء المزيد من الأعمدة */
    .table th:nth-child(n+4),
    .table td:nth-child(n+4) {
        display: none !important;
    }

    /* تحسين البطاقات */
    .card {
        margin-bottom: 10px !important;
    }

    .card-header {
        padding: 10px 15px !important;
    }

    .card-header h5 {
        font-size: 1rem !important;
    }

    /* تحسين الأزرار */
    .btn-group .btn {
        font-size: 0.7rem !important;
        padding: 3px 6px !important;
    }

    /* تحسين الـ badges */
    .badge {
        font-size: 0.7rem !important;
    }
}

/* تحسين التمرير الأفقي للجداول */
.table-responsive {
    -webkit-overflow-scrolling: touch;
}

/* تحسين النصوص الطويلة */
.text-truncate-mobile {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@media (max-width: 768px) {
    .text-truncate-mobile {
        max-width: 100px;
    }

    /* تحسين الأيقونات في الأزرار */
    .btn-sm i {
        font-size: 1rem !important;
    }

    /* تحسين معلومات الجوال */
    .mobile-info {
        display: flex !important;
        flex-wrap: wrap;
        gap: 4px;
        margin-top: 4px;
    }

    .mobile-info .badge {
        font-size: 0.65rem !important;
        padding: 2px 6px !important;
    }

    /* تحسين الـ tooltips للجوال */
    .btn[title]:hover::after {
        content: attr(title);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        white-space: nowrap;
        z-index: 1000;
    }
}

/* تحسين الحالة والأولوية في الجوال */
@media (max-width: 767px) {
    /* إضافة الحالة والأولوية كـ badges صغيرة تحت رقم الأمر */
    .mobile-status-badges {
        display: flex;
        gap: 4px;
        margin-top: 4px;
    }

    .mobile-status-badges .badge {
        font-size: 0.6rem !important;
        padding: 2px 6px !important;
    }
}

/* تحسين عام للجوال */
.mobile-info {
    display: none;
}

@media (max-width: 767px) {
    .mobile-info {
        display: block !important;
    }

    /* تحسين التمرير السلس */
    html {
        scroll-behavior: smooth;
    }

    /* تحسين اللمس */
    .btn, .card, .table-responsive {
        touch-action: manipulation;
    }
}
</style>

{% endblock %}
