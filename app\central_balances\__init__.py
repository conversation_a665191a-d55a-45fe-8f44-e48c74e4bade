"""
النظام المركزي للأرصدة والمعاملات
Central Balances and Transactions System

هذا النظام يوفر إدارة مركزية شاملة لجميع الأرصدة والمعاملات
في جميع الأنظمة الفرعية للمشروع.

المكونات الرئيسية:
- إدارة أنواع الكيانات (الموردين، العملاء، الموظفين، إلخ)
- إدارة أنواع الوثائق (الفواتير، السندات، الحوالات، إلخ)
- إدارة الأرصدة الافتتاحية والجارية
- تتبع جميع المعاملات المالية
"""

from flask import Blueprint

# إنشاء Blueprint للنظام المركزي
central_balances_bp = Blueprint('central_balances', __name__, url_prefix='/central_balances')

# استيراد الـ routes
from . import routes

def init_central_balances(app):
    """تهيئة النظام المركزي للأرصدة"""
    app.register_blueprint(central_balances_bp)
    
    # تسجيل النظام في السجل
    app.logger.info("✅ تم تسجيل النظام المركزي للأرصدة والمعاملات")
