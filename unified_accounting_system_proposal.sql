-- ========================================================================
-- نظام محاسبي موحد ومتطور - اقتراح التطوير
-- Unified Advanced Accounting System - Development Proposal
-- ========================================================================

-- ========================================================================
-- المرحلة الأولى: تعديل بنية BALANCE_TRANSACTIONS
-- Phase 1: Modify BALANCE_TRANSACTIONS Structure
-- ========================================================================

-- 1. إضافة الأعمدة الجديدة
ALTER TABLE BALANCE_TRANSACTIONS ADD (
    BALANCE NUMBER(15,2) DEFAULT 0,           -- رصيد موحد (موجب للمدين، سالب للدائن)
    BALANCE_F NUMBER(15,2) DEFAULT 0,         -- رصيد بالعملة الأساسية مع سعر الصرف
    MONTH_NUMBER NUMBER(2),                   -- رقم الشهر (1-12)
    YEAR_NUMBER NUMBER(4),                    -- رقم السنة
    BRANCH_ID NUMBER DEFAULT 1                -- رقم الفرع
);

-- 2. إنشاء فهارس محسنة للأداء
CREATE INDEX IDX_BT_ENTITY_BALANCE ON BALANCE_TRANSACTIONS(ENTITY_TYPE_CODE, ENTITY_ID, CURRENCY_CODE);
CREATE INDEX IDX_BT_PERIOD ON BALANCE_TRANSACTIONS(YEAR_NUMBER, MONTH_NUMBER);
CREATE INDEX IDX_BT_BRANCH ON BALANCE_TRANSACTIONS(BRANCH_ID);
CREATE INDEX IDX_BT_DOCUMENT ON BALANCE_TRANSACTIONS(DOCUMENT_TYPE_CODE, DOCUMENT_NUMBER);

-- ========================================================================
-- المرحلة الثانية: إضافة أنواع الكيانات الجديدة
-- Phase 2: Add New Entity Types
-- ========================================================================

-- إضافة أنواع الكيانات الجديدة
INSERT INTO ENTITY_TYPES (code, name_ar, name_en, description, is_active, created_by, created_date) VALUES
('PURCHASE_AGENT', 'مندوب مشتريات', 'Purchase Agent', 'Purchase representative', 1, 1, CURRENT_TIMESTAMP);

INSERT INTO ENTITY_TYPES (code, name_ar, name_en, description, is_active, created_by, created_date) VALUES
('SALES_AGENT', 'مندوب مبيعات', 'Sales Agent', 'Sales representative', 1, 1, CURRENT_TIMESTAMP);

INSERT INTO ENTITY_TYPES (code, name_ar, name_en, description, is_active, created_by, created_date) VALUES
('SHIPPING_COMPANY', 'شركة شحن', 'Shipping Company', 'Shipping and logistics company', 1, 1, CURRENT_TIMESTAMP);

-- ========================================================================
-- المرحلة الثالثة: Package الأرصدة الافتتاحية
-- Phase 3: Opening Balances Package
-- ========================================================================

CREATE OR REPLACE PACKAGE OPENING_BALANCES_PKG AS
    
    -- إدراج رصيد افتتاحي
    PROCEDURE INSERT_OPENING_BALANCE(
        p_entity_type_code IN VARCHAR2,
        p_entity_id IN NUMBER,
        p_currency_code IN VARCHAR2,
        p_balance_amount IN NUMBER,
        p_branch_id IN NUMBER DEFAULT 1,
        p_year_number IN NUMBER DEFAULT EXTRACT(YEAR FROM SYSDATE),
        p_user_id IN NUMBER DEFAULT 1
    );
    
    -- تعديل رصيد افتتاحي
    PROCEDURE UPDATE_OPENING_BALANCE(
        p_entity_type_code IN VARCHAR2,
        p_entity_id IN NUMBER,
        p_currency_code IN VARCHAR2,
        p_new_balance_amount IN NUMBER,
        p_branch_id IN NUMBER DEFAULT 1,
        p_year_number IN NUMBER DEFAULT EXTRACT(YEAR FROM SYSDATE),
        p_user_id IN NUMBER DEFAULT 1
    );
    
    -- حذف رصيد افتتاحي
    PROCEDURE DELETE_OPENING_BALANCE(
        p_entity_type_code IN VARCHAR2,
        p_entity_id IN NUMBER,
        p_currency_code IN VARCHAR2,
        p_branch_id IN NUMBER DEFAULT 1,
        p_year_number IN NUMBER DEFAULT EXTRACT(YEAR FROM SYSDATE)
    );
    
    -- الحصول على رصيد افتتاحي
    FUNCTION GET_OPENING_BALANCE(
        p_entity_type_code IN VARCHAR2,
        p_entity_id IN NUMBER,
        p_currency_code IN VARCHAR2,
        p_branch_id IN NUMBER DEFAULT 1,
        p_year_number IN NUMBER DEFAULT EXTRACT(YEAR FROM SYSDATE)
    ) RETURN NUMBER;
    
END OPENING_BALANCES_PKG;
/

-- ========================================================================
-- المرحلة الرابعة: Package ترحيل الأرصدة
-- Phase 4: Balance Transactions Package
-- ========================================================================

CREATE OR REPLACE PACKAGE BALANCE_TRANSACTIONS_PKG AS
    
    -- ترحيل معاملة
    PROCEDURE POST_TRANSACTION(
        p_entity_type_code IN VARCHAR2,
        p_entity_id IN NUMBER,
        p_document_type_code IN VARCHAR2,
        p_document_number IN VARCHAR2,
        p_document_date IN DATE,
        p_currency_code IN VARCHAR2,
        p_debit_amount IN NUMBER DEFAULT 0,
        p_credit_amount IN NUMBER DEFAULT 0,
        p_exchange_rate IN NUMBER DEFAULT 1,
        p_description IN VARCHAR2 DEFAULT NULL,
        p_branch_id IN NUMBER DEFAULT 1,
        p_user_id IN NUMBER DEFAULT 1
    );
    
    -- عكس معاملة
    PROCEDURE REVERSE_TRANSACTION(
        p_original_document_number IN VARCHAR2,
        p_reversal_document_number IN VARCHAR2,
        p_reversal_reason IN VARCHAR2,
        p_user_id IN NUMBER DEFAULT 1
    );
    
    -- الحصول على الرصيد الحالي
    FUNCTION GET_CURRENT_BALANCE(
        p_entity_type_code IN VARCHAR2,
        p_entity_id IN NUMBER,
        p_currency_code IN VARCHAR2,
        p_branch_id IN NUMBER DEFAULT 1,
        p_as_of_date IN DATE DEFAULT SYSDATE
    ) RETURN NUMBER;
    
    -- الحصول على رصيد شهري
    FUNCTION GET_MONTHLY_BALANCE(
        p_entity_type_code IN VARCHAR2,
        p_entity_id IN NUMBER,
        p_currency_code IN VARCHAR2,
        p_year_number IN NUMBER,
        p_month_number IN NUMBER,
        p_branch_id IN NUMBER DEFAULT 1
    ) RETURN NUMBER;
    
    -- الحصول على تاريخ الرصيد
    PROCEDURE GET_BALANCE_HISTORY(
        p_entity_type_code IN VARCHAR2,
        p_entity_id IN NUMBER,
        p_currency_code IN VARCHAR2,
        p_from_date IN DATE,
        p_to_date IN DATE,
        p_branch_id IN NUMBER DEFAULT 1,
        p_cursor OUT SYS_REFCURSOR
    );
    
END BALANCE_TRANSACTIONS_PKG;
/

-- ========================================================================
-- المرحلة الخامسة: Views للتقارير المحسنة
-- Phase 5: Enhanced Reporting Views
-- ========================================================================

-- عرض الأرصدة الحالية
CREATE OR REPLACE VIEW V_CURRENT_BALANCES AS
SELECT 
    entity_type_code,
    entity_id,
    currency_code,
    branch_id,
    SUM(balance) as current_balance,
    SUM(balance_f) as current_balance_base_currency,
    COUNT(*) as transaction_count,
    MAX(document_date) as last_transaction_date
FROM BALANCE_TRANSACTIONS
GROUP BY entity_type_code, entity_id, currency_code, branch_id;

-- عرض الأرصدة الشهرية
CREATE OR REPLACE VIEW V_MONTHLY_BALANCES AS
SELECT 
    entity_type_code,
    entity_id,
    currency_code,
    branch_id,
    year_number,
    month_number,
    SUM(balance) as monthly_balance,
    SUM(balance_f) as monthly_balance_base_currency,
    COUNT(*) as monthly_transaction_count
FROM BALANCE_TRANSACTIONS
GROUP BY entity_type_code, entity_id, currency_code, branch_id, year_number, month_number;

-- عرض ملخص الكيانات
CREATE OR REPLACE VIEW V_ENTITY_SUMMARY AS
SELECT 
    bt.entity_type_code,
    et.name_ar as entity_type_name,
    bt.entity_id,
    bt.branch_id,
    COUNT(DISTINCT bt.currency_code) as currency_count,
    SUM(bt.balance_f) as total_balance_base_currency,
    COUNT(*) as total_transactions,
    MIN(bt.document_date) as first_transaction_date,
    MAX(bt.document_date) as last_transaction_date
FROM BALANCE_TRANSACTIONS bt
JOIN ENTITY_TYPES et ON bt.entity_type_code = et.code
GROUP BY bt.entity_type_code, et.name_ar, bt.entity_id, bt.branch_id;

-- ========================================================================
-- المرحلة السادسة: إجراءات الهجرة
-- Phase 6: Migration Procedures
-- ========================================================================

-- إجراء هجرة البيانات من CURRENT_BALANCES
CREATE OR REPLACE PROCEDURE MIGRATE_CURRENT_BALANCES AS
BEGIN
    -- هجرة البيانات الموجودة
    INSERT INTO BALANCE_TRANSACTIONS (
        entity_type_code, entity_id, document_type_code,
        document_number, document_date, currency_code,
        debit_amount, credit_amount, balance, balance_f,
        month_number, year_number, branch_id,
        description, status, created_by, created_date
    )
    SELECT 
        entity_type_code,
        entity_id,
        'MIGRATION',
        'MIG-' || entity_type_code || '-' || entity_id,
        COALESCE(last_transaction_date, SYSDATE),
        currency_code,
        CASE WHEN current_balance > 0 THEN current_balance ELSE 0 END,
        CASE WHEN current_balance < 0 THEN ABS(current_balance) ELSE 0 END,
        current_balance,
        current_balance, -- افتراض أن العملة الأساسية هي نفسها
        EXTRACT(MONTH FROM COALESCE(last_transaction_date, SYSDATE)),
        EXTRACT(YEAR FROM COALESCE(last_transaction_date, SYSDATE)),
        1, -- فرع افتراضي
        'Migrated from CURRENT_BALANCES',
        'POSTED',
        1,
        SYSDATE
    FROM CURRENT_BALANCES
    WHERE current_balance IS NOT NULL
    AND current_balance != 0;
    
    COMMIT;
    DBMS_OUTPUT.PUT_LINE('Migration completed successfully');
END;
/

-- ========================================================================
-- المرحلة السابعة: اختبارات الأداء
-- Phase 7: Performance Tests
-- ========================================================================

-- اختبار سرعة الاستعلام
CREATE OR REPLACE PROCEDURE TEST_BALANCE_PERFORMANCE AS
    v_start_time TIMESTAMP;
    v_end_time TIMESTAMP;
    v_balance NUMBER;
BEGIN
    -- اختبار الرصيد الحالي
    v_start_time := SYSTIMESTAMP;
    
    SELECT SUM(balance) INTO v_balance
    FROM BALANCE_TRANSACTIONS
    WHERE entity_type_code = 'SUPPLIER'
    AND entity_id = 1
    AND currency_code = 'USD';
    
    v_end_time := SYSTIMESTAMP;
    
    DBMS_OUTPUT.PUT_LINE('Balance query time: ' || 
        EXTRACT(SECOND FROM (v_end_time - v_start_time)) || ' seconds');
    DBMS_OUTPUT.PUT_LINE('Balance result: ' || v_balance);
END;
/
