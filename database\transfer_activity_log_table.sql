-- =====================================================
-- جدول سجل أنشطة الحوالات
-- Transfer Activity Log Table
-- =====================================================

-- 1. إنشاء جدول سجل أنشطة الحوالات
CREATE TABLE transfer_activity_log (
    id NUMBER PRIMARY KEY,
    transfer_id NUMBER NOT NULL,
    activity_type VARCHAR2(50) NOT NULL,
    description CLOB,
    
    -- بيانات الحالة
    old_status VARCHAR2(50),
    new_status VARCHAR2(50),
    
    -- بيانات المبالغ
    amount_before NUMBER(15,2),
    amount_after NUMBER(15,2),
    currency_code VARCHAR2(10),
    
    -- بيانات إضافية
    entity_type VARCHAR2(50), -- SUPPLIER, MONEY_CHANGER, BANK
    entity_id NUMBER,
    entity_name VARCHAR2(200),
    
    -- تفاصيل العملية
    operation_details CLOB, -- JSON format for complex data
    error_message CLOB,
    execution_time_ms NUMBER, -- وقت التنفيذ بالميلي ثانية
    
    -- معلومات التتبع
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    ip_address VARCHAR2(45),
    user_agent VARCHAR2(500),
    session_id VARCHAR2(100),
    
    -- معلومات النظام
    server_name VARCHAR2(100),
    application_version VARCHAR2(50),
    
    -- القيود
    CONSTRAINT fk_tal_transfer FOREIGN KEY (transfer_id) REFERENCES transfers(id),
    CONSTRAINT chk_tal_activity_type CHECK (activity_type IN (
        'CREATED', 'APPROVED', 'REJECTED', 'EXECUTED', 'CANCELLED', 
        'ERROR', 'MODIFIED', 'DISTRIBUTION_ADDED', 'DISTRIBUTION_REMOVED',
        'BALANCE_UPDATED', 'VALIDATION_FAILED', 'NOTIFICATION_SENT'
    )),
    CONSTRAINT chk_tal_entity_type CHECK (entity_type IS NULL OR entity_type IN (
        'SUPPLIER', 'MONEY_CHANGER', 'BANK', 'USER', 'SYSTEM'
    ))
);

-- 2. إنشاء sequence للـ ID
CREATE SEQUENCE transfer_activity_log_seq 
START WITH 1 
INCREMENT BY 1 
NOCACHE;

-- 3. إنشاء trigger للـ ID التلقائي
CREATE OR REPLACE TRIGGER tal_id_trigger
    BEFORE INSERT ON transfer_activity_log
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := transfer_activity_log_seq.NEXTVAL;
    END IF;
    
    -- تعيين الوقت الحالي إذا لم يكن محدداً
    IF :NEW.created_at IS NULL THEN
        :NEW.created_at := CURRENT_TIMESTAMP;
    END IF;
    
    -- تعيين معلومات النظام الافتراضية
    IF :NEW.server_name IS NULL THEN
        :NEW.server_name := SYS_CONTEXT('USERENV', 'SERVER_HOST');
    END IF;
    
    IF :NEW.application_version IS NULL THEN
        :NEW.application_version := 'SASERP-1.0';
    END IF;
END;
/

-- 4. إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_tal_transfer_id ON transfer_activity_log(transfer_id);
CREATE INDEX idx_tal_activity_type ON transfer_activity_log(activity_type);
CREATE INDEX idx_tal_created_at ON transfer_activity_log(created_at);
CREATE INDEX idx_tal_created_by ON transfer_activity_log(created_by);
CREATE INDEX idx_tal_status_change ON transfer_activity_log(old_status, new_status);
CREATE INDEX idx_tal_entity ON transfer_activity_log(entity_type, entity_id);

-- 5. إنشاء view لعرض السجل مع التفاصيل
CREATE OR REPLACE VIEW transfer_activity_log_view AS
SELECT 
    tal.*,
    t.request_number as transfer_number,
    t.beneficiary_name,
    t.amount as transfer_amount,
    t.currency as transfer_currency,
    u.username as user_name,
    u.full_name as user_full_name,
    CASE tal.activity_type
        WHEN 'CREATED' THEN 'تم إنشاء الحوالة'
        WHEN 'APPROVED' THEN 'تم اعتماد الحوالة'
        WHEN 'REJECTED' THEN 'تم رفض الحوالة'
        WHEN 'EXECUTED' THEN 'تم تنفيذ الحوالة'
        WHEN 'CANCELLED' THEN 'تم إلغاء الحوالة'
        WHEN 'ERROR' THEN 'حدث خطأ'
        WHEN 'MODIFIED' THEN 'تم تعديل الحوالة'
        WHEN 'DISTRIBUTION_ADDED' THEN 'تم إضافة توزيع'
        WHEN 'DISTRIBUTION_REMOVED' THEN 'تم حذف توزيع'
        WHEN 'BALANCE_UPDATED' THEN 'تم تحديث الرصيد'
        WHEN 'VALIDATION_FAILED' THEN 'فشل في التحقق'
        WHEN 'NOTIFICATION_SENT' THEN 'تم إرسال إشعار'
        ELSE tal.activity_type
    END as activity_type_ar
FROM transfer_activity_log tal
JOIN transfers t ON tal.transfer_id = t.id
LEFT JOIN users u ON tal.created_by = u.id;

-- 6. إنشاء إجراء لتسجيل النشاط
CREATE OR REPLACE PROCEDURE LOG_TRANSFER_ACTIVITY(
    p_transfer_id IN NUMBER,
    p_activity_type IN VARCHAR2,
    p_description IN VARCHAR2,
    p_old_status IN VARCHAR2 DEFAULT NULL,
    p_new_status IN VARCHAR2 DEFAULT NULL,
    p_amount_before IN NUMBER DEFAULT NULL,
    p_amount_after IN NUMBER DEFAULT NULL,
    p_currency_code IN VARCHAR2 DEFAULT NULL,
    p_entity_type IN VARCHAR2 DEFAULT NULL,
    p_entity_id IN NUMBER DEFAULT NULL,
    p_entity_name IN VARCHAR2 DEFAULT NULL,
    p_operation_details IN CLOB DEFAULT NULL,
    p_error_message IN CLOB DEFAULT NULL,
    p_execution_time_ms IN NUMBER DEFAULT NULL,
    p_user_id IN NUMBER DEFAULT 1,
    p_ip_address IN VARCHAR2 DEFAULT NULL,
    p_user_agent IN VARCHAR2 DEFAULT NULL,
    p_session_id IN VARCHAR2 DEFAULT NULL
) AS
BEGIN
    INSERT INTO transfer_activity_log (
        transfer_id, activity_type, description,
        old_status, new_status,
        amount_before, amount_after, currency_code,
        entity_type, entity_id, entity_name,
        operation_details, error_message, execution_time_ms,
        created_by, ip_address, user_agent, session_id
    ) VALUES (
        p_transfer_id, p_activity_type, p_description,
        p_old_status, p_new_status,
        p_amount_before, p_amount_after, p_currency_code,
        p_entity_type, p_entity_id, p_entity_name,
        p_operation_details, p_error_message, p_execution_time_ms,
        p_user_id, p_ip_address, p_user_agent, p_session_id
    );
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        -- تسجيل الخطأ دون إيقاف العملية الأساسية
        ROLLBACK;
        NULL;
END;
/

-- 7. إنشاء trigger لتسجيل تغييرات الحوالات تلقائياً
CREATE OR REPLACE TRIGGER transfers_audit_trigger
    AFTER UPDATE ON transfers
    FOR EACH ROW
DECLARE
    v_changes CLOB := '';
    v_change_count NUMBER := 0;
BEGIN
    -- تتبع تغييرات الحالة
    IF NVL(:OLD.status, 'NULL') != NVL(:NEW.status, 'NULL') THEN
        LOG_TRANSFER_ACTIVITY(
            p_transfer_id => :NEW.id,
            p_activity_type => CASE :NEW.status
                WHEN 'approved' THEN 'APPROVED'
                WHEN 'rejected' THEN 'REJECTED'
                WHEN 'executed' THEN 'EXECUTED'
                WHEN 'cancelled' THEN 'CANCELLED'
                ELSE 'MODIFIED'
            END,
            p_description => 'تغيير حالة الحوالة من ' || NVL(:OLD.status, 'غير محدد') || ' إلى ' || NVL(:NEW.status, 'غير محدد'),
            p_old_status => :OLD.status,
            p_new_status => :NEW.status,
            p_user_id => NVL(:NEW.updated_by, 1)
        );
        v_change_count := v_change_count + 1;
    END IF;
    
    -- تتبع تغييرات المبلغ
    IF NVL(:OLD.amount, 0) != NVL(:NEW.amount, 0) THEN
        LOG_TRANSFER_ACTIVITY(
            p_transfer_id => :NEW.id,
            p_activity_type => 'MODIFIED',
            p_description => 'تغيير مبلغ الحوالة من ' || NVL(:OLD.amount, 0) || ' إلى ' || NVL(:NEW.amount, 0),
            p_amount_before => :OLD.amount,
            p_amount_after => :NEW.amount,
            p_currency_code => :NEW.currency,
            p_user_id => NVL(:NEW.updated_by, 1)
        );
        v_change_count := v_change_count + 1;
    END IF;
    
    -- تتبع تغييرات المستفيد
    IF NVL(:OLD.beneficiary_name, 'NULL') != NVL(:NEW.beneficiary_name, 'NULL') THEN
        LOG_TRANSFER_ACTIVITY(
            p_transfer_id => :NEW.id,
            p_activity_type => 'MODIFIED',
            p_description => 'تغيير اسم المستفيد من ' || NVL(:OLD.beneficiary_name, 'غير محدد') || ' إلى ' || NVL(:NEW.beneficiary_name, 'غير محدد'),
            p_user_id => NVL(:NEW.updated_by, 1)
        );
        v_change_count := v_change_count + 1;
    END IF;
    
    -- تسجيل عام للتعديل إذا لم يتم تسجيل تغييرات محددة
    IF v_change_count = 0 THEN
        LOG_TRANSFER_ACTIVITY(
            p_transfer_id => :NEW.id,
            p_activity_type => 'MODIFIED',
            p_description => 'تم تعديل بيانات الحوالة',
            p_user_id => NVL(:NEW.updated_by, 1)
        );
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        -- تجاهل الأخطاء لعدم إيقاف العملية الأساسية
        NULL;
END;
/

-- 8. إنشاء view لملخص الأنشطة حسب النوع
CREATE OR REPLACE VIEW activity_summary_by_type AS
SELECT 
    activity_type,
    COUNT(*) as total_count,
    COUNT(DISTINCT transfer_id) as unique_transfers,
    COUNT(DISTINCT created_by) as unique_users,
    MIN(created_at) as first_activity,
    MAX(created_at) as last_activity,
    AVG(execution_time_ms) as avg_execution_time,
    COUNT(CASE WHEN error_message IS NOT NULL THEN 1 END) as error_count
FROM transfer_activity_log
GROUP BY activity_type
ORDER BY total_count DESC;

-- 9. إنشاء view لملخص الأنشطة حسب المستخدم
CREATE OR REPLACE VIEW activity_summary_by_user AS
SELECT 
    tal.created_by as user_id,
    u.username,
    u.full_name,
    COUNT(*) as total_activities,
    COUNT(DISTINCT tal.transfer_id) as unique_transfers,
    COUNT(DISTINCT tal.activity_type) as activity_types_used,
    MIN(tal.created_at) as first_activity,
    MAX(tal.created_at) as last_activity,
    COUNT(CASE WHEN tal.error_message IS NOT NULL THEN 1 END) as error_count
FROM transfer_activity_log tal
LEFT JOIN users u ON tal.created_by = u.id
GROUP BY tal.created_by, u.username, u.full_name
ORDER BY total_activities DESC;

-- 10. إنشاء إجراء لتنظيف السجلات القديمة
CREATE OR REPLACE PROCEDURE CLEANUP_OLD_ACTIVITY_LOGS(
    p_days_to_keep IN NUMBER DEFAULT 365,
    p_keep_error_logs IN NUMBER DEFAULT 1
) AS
    v_cutoff_date DATE;
    v_deleted_count NUMBER;
BEGIN
    v_cutoff_date := SYSDATE - p_days_to_keep;
    
    -- حذف السجلات القديمة (مع الاحتفاظ بسجلات الأخطاء حسب الخيار)
    DELETE FROM transfer_activity_log
    WHERE created_at < v_cutoff_date
    AND (p_keep_error_logs = 0 OR error_message IS NULL);
    
    v_deleted_count := SQL%ROWCOUNT;
    
    COMMIT;
    
    -- تسجيل عملية التنظيف
    DBMS_OUTPUT.PUT_LINE('تم حذف ' || v_deleted_count || ' سجل من سجلات الأنشطة القديمة');
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END;
/

-- 11. إنشاء comments للتوثيق
COMMENT ON TABLE transfer_activity_log IS 'جدول سجل أنشطة الحوالات لتتبع جميع العمليات والتغييرات';
COMMENT ON COLUMN transfer_activity_log.id IS 'المعرف الفريد للنشاط';
COMMENT ON COLUMN transfer_activity_log.transfer_id IS 'معرف الحوالة';
COMMENT ON COLUMN transfer_activity_log.activity_type IS 'نوع النشاط (CREATED, APPROVED, EXECUTED, etc.)';
COMMENT ON COLUMN transfer_activity_log.description IS 'وصف تفصيلي للنشاط';
COMMENT ON COLUMN transfer_activity_log.old_status IS 'الحالة السابقة للحوالة';
COMMENT ON COLUMN transfer_activity_log.new_status IS 'الحالة الجديدة للحوالة';
COMMENT ON COLUMN transfer_activity_log.operation_details IS 'تفاصيل العملية بصيغة JSON';
COMMENT ON COLUMN transfer_activity_log.execution_time_ms IS 'وقت تنفيذ العملية بالميلي ثانية';

-- إنهاء السكريبت
COMMIT;

-- عرض رسالة نجاح
SELECT 'تم إنشاء جدول transfer_activity_log بنجاح' as result FROM DUAL;
