{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- شريط الأدوات العلوي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-eye me-2"></i>
                            عرض طلب الشراء - {{ request.req_no }}
                        </h5>
                        <div class="btn-group">
                            <a href="{{ url_for('purchase_requests.edit', id=request.id) }}" class="btn btn-warning">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </a>
                            <a href="{{ url_for('purchase_requests.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                            </a>
                            <button onclick="window.print()" class="btn btn-info">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات الطلب -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">معلومات الطلب الأساسية</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">رقم الطلب:</label>
                                <p class="form-control-plaintext">{{ request.req_no or '-' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">تاريخ الطلب:</label>
                                <p class="form-control-plaintext">{{ request.req_date.strftime('%Y-%m-%d') if request.req_date else '-' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">اسم الطالب:</label>
                                <p class="form-control-plaintext">{{ request.requester_name or '-' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">القسم:</label>
                                <p class="form-control-plaintext">{{ request.department or '-' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الحالة:</label>
                                <p class="form-control-plaintext">
                                    {% if request.req_status == 'معتمد' %}
                                        <span class="badge bg-success">{{ request.req_status }}</span>
                                    {% elif request.req_status == 'مرفوض' %}
                                        <span class="badge bg-danger">{{ request.req_status }}</span>
                                    {% elif request.req_status == 'قيد المراجعة' %}
                                        <span class="badge bg-warning">{{ request.req_status }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ request.req_status or 'مسودة' }}</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الأولوية:</label>
                                <p class="form-control-plaintext">
                                    {% if request.priority == 'عاجل' %}
                                        <span class="badge bg-danger">{{ request.priority }}</span>
                                    {% elif request.priority == 'مهم' %}
                                        <span class="badge bg-warning">{{ request.priority }}</span>
                                    {% else %}
                                        <span class="badge bg-info">{{ request.priority or 'عادي' }}</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الغرض من الطلب:</label>
                                <p class="form-control-plaintext">{{ request.purpose or '-' }}</p>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label fw-bold">ملاحظات:</label>
                                <p class="form-control-plaintext">{{ request.notes or '-' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">معلومات مالية</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">إجمالي المبلغ:</label>
                        <p class="form-control-plaintext">
                            <span class="h4 text-primary">{{ "{:,.2f}".format(request.total_amount or 0) }} ريال</span>
                        </p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">العملة:</label>
                        <p class="form-control-plaintext">{{ request.currency or 'ريال سعودي' }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">تاريخ الحاجة:</label>
                        <p class="form-control-plaintext">{{ request.needed_date.strftime('%Y-%m-%d') if request.needed_date else '-' }}</p>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">معلومات النظام</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">تاريخ الإنشاء:</label>
                        <p class="form-control-plaintext">{{ request.created_at.strftime('%Y-%m-%d %H:%M') if request.created_at else '-' }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">آخر تحديث:</label>
                        <p class="form-control-plaintext">{{ request.updated_at.strftime('%Y-%m-%d %H:%M') if request.updated_at else '-' }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- عناصر الطلب -->
    {% if request.items %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">عناصر الطلب</h6>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th width="50">م</th>
                                    <th width="100">كود الصنف</th>
                                    <th>اسم الصنف</th>
                                    <th width="80">الكمية</th>
                                    <th width="80">الوحدة</th>
                                    <th width="100">سعر الوحدة</th>
                                    <th width="100">الإجمالي</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in request.items %}
                                <tr>
                                    <td>{{ item.line_number or loop.index }}</td>
                                    <td>{{ item.item_code or '-' }}</td>
                                    <td>
                                        <strong>{{ item.item_name or '-' }}</strong>
                                        {% if item.item_description %}
                                            <br><small class="text-muted">{{ item.item_description }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ "{:,.2f}".format(item.quantity or 0) }}</td>
                                    <td>{{ item.unit_name or '-' }}</td>
                                    <td>{{ "{:,.2f}".format(item.unit_price or 0) }}</td>
                                    <td><strong>{{ "{:,.2f}".format(item.total_price or 0) }}</strong></td>
                                    <td>{{ item.notes or '-' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="table-info">
                                    <th colspan="6" class="text-end">الإجمالي:</th>
                                    <th>{{ "{:,.2f}".format(request.total_amount or 0) }} ريال</th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn-group, .card-header .btn-group {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .card-header {
        background: none !important;
        border: none !important;
    }
}
</style>
{% endblock %}
