#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إضافة أنواع الكيانات الجديدة بالبيانات الكاملة
Add New Entity Types with Complete Data
"""

import sys
import os
sys.path.insert(0, os.getcwd())

from oracle_manager import OracleManager

def add_complete_entity_types():
    """إضافة أنواع الكيانات بالبيانات الكاملة المطلوبة"""
    
    oracle = OracleManager()
    
    print("🔧 إضافة أنواع الكيانات بالبيانات الكاملة...")
    print("=" * 70)
    
    # أنواع الكيانات الجديدة مع جميع البيانات المطلوبة
    new_entity_types = [
        {
            'code': 'PURCHASE_AGENT',
            'name_ar': 'مندوبي المشتريات',
            'name_en': 'Purchase Agents',
            'module': 'PROCUREMENT',
            'table': 'PURCHASE_AGENTS',
            'id_column': 'id',
            'name_column': 'name',
            'description_column': 'description',
            'prefix': 'PA',
            'sort_order': 100
        },
        {
            'code': 'SALES_AGENT',
            'name_ar': 'مندوبي المبيعات',
            'name_en': 'Sales Agents',
            'module': 'SALES',
            'table': 'SALES_AGENTS',
            'id_column': 'id',
            'name_column': 'name',
            'description_column': 'description',
            'prefix': 'SA',
            'sort_order': 110
        },
        {
            'code': 'SHIPPING_COMPANY',
            'name_ar': 'شركات الشحن',
            'name_en': 'Shipping Companies',
            'module': 'LOGISTICS',
            'table': 'SHIPPING_COMPANIES',
            'id_column': 'id',
            'name_column': 'company_name',
            'description_column': 'description',
            'prefix': 'SC',
            'sort_order': 120
        }
    ]
    
    successful_inserts = 0
    
    for entity_type in new_entity_types:
        try:
            print(f"\n   إضافة {entity_type['code']}...")
            
            # فحص إذا كان موجود
            check_query = "SELECT COUNT(*) FROM ENTITY_TYPES WHERE ENTITY_TYPE_CODE = :code"
            exists = oracle.execute_query(check_query, {"code": entity_type['code']})
            
            if exists and exists[0][0] > 0:
                print(f"      ⚠️ {entity_type['code']} موجود مسبقاً")
                successful_inserts += 1
                continue
            
            # الحصول على أكبر ID
            max_id_query = "SELECT NVL(MAX(id), 0) + 1 FROM ENTITY_TYPES"
            max_id_result = oracle.execute_query(max_id_query)
            new_id = max_id_result[0][0] if max_id_result else 1
            
            # إدراج النوع الجديد بجميع البيانات المطلوبة
            insert_query = """
            INSERT INTO ENTITY_TYPES (
                id, entity_type_code, entity_name_ar, entity_name_en,
                module_name, table_name, id_column, name_column, description_column,
                has_balances, has_transactions, default_currency, account_prefix,
                sort_order, is_active, created_date, created_by
            ) VALUES (
                :id, :code, :name_ar, :name_en,
                :module, :table_name, :id_column, :name_column, :description_column,
                1, 1, 'USD', :prefix,
                :sort_order, 1, CURRENT_TIMESTAMP, 1
            )
            """
            
            params = {
                'id': new_id,
                'code': entity_type['code'],
                'name_ar': entity_type['name_ar'],
                'name_en': entity_type['name_en'],
                'module': entity_type['module'],
                'table_name': entity_type['table'],
                'id_column': entity_type['id_column'],
                'name_column': entity_type['name_column'],
                'description_column': entity_type['description_column'],
                'prefix': entity_type['prefix'],
                'sort_order': entity_type['sort_order']
            }
            
            oracle.execute_update(insert_query, params)
            print(f"      ✅ تم إضافة {entity_type['code']} بـ ID: {new_id}")
            print(f"         الاسم العربي: {entity_type['name_ar']}")
            print(f"         الاسم الإنجليزي: {entity_type['name_en']}")
            print(f"         الوحدة: {entity_type['module']}")
            print(f"         الجدول: {entity_type['table']}")
            successful_inserts += 1
            
        except Exception as e:
            print(f"      ❌ خطأ في إضافة {entity_type['code']}: {str(e)}")
    
    print(f"\nملخص الإضافة:")
    print(f"   نجح: {successful_inserts}/{len(new_entity_types)}")
    
    return successful_inserts

def verify_complete_entity_types():
    """التحقق من أنواع الكيانات المضافة بالتفصيل"""
    
    oracle = OracleManager()
    
    print("\n✅ التحقق من أنواع الكيانات المضافة...")
    print("=" * 70)
    
    # فحص الأنواع الجديدة
    new_types = ['PURCHASE_AGENT', 'SALES_AGENT', 'SHIPPING_COMPANY']
    
    for entity_type in new_types:
        try:
            query = """
            SELECT entity_type_code, entity_name_ar, entity_name_en, 
                   module_name, table_name, account_prefix, sort_order, is_active
            FROM ENTITY_TYPES 
            WHERE entity_type_code = :code
            """
            result = oracle.execute_query(query, {"code": entity_type})
            
            if result and len(result) > 0:
                row = result[0]
                print(f"   ✅ {row[0]}:")
                print(f"      الاسم العربي: {row[1]}")
                print(f"      الاسم الإنجليزي: {row[2]}")
                print(f"      الوحدة: {row[3]}")
                print(f"      الجدول: {row[4]}")
                print(f"      البادئة: {row[5]}")
                print(f"      ترتيب العرض: {row[6]}")
                print(f"      نشط: {'نعم' if row[7] == 1 else 'لا'}")
                print()
            else:
                print(f"   ❌ {entity_type}: غير موجود")
                
        except Exception as e:
            print(f"   ❌ خطأ في فحص {entity_type}: {str(e)}")

def test_with_ob_pkg():
    """اختبار أنواع الكيانات الجديدة مع OB_PKG"""
    
    oracle = OracleManager()
    
    print("\n🧪 اختبار أنواع الكيانات الجديدة مع OB_PKG...")
    print("=" * 70)
    
    test_cases = [
        {
            "name": "رصيد افتتاحي لمندوب مشتريات",
            "entity_type": "PURCHASE_AGENT",
            "entity_id": 1,
            "currency": "USD",
            "amount": -1500
        },
        {
            "name": "رصيد افتتاحي لمندوب مبيعات",
            "entity_type": "SALES_AGENT",
            "entity_id": 1,
            "currency": "USD",
            "amount": 2500
        },
        {
            "name": "رصيد افتتاحي لشركة شحن",
            "entity_type": "SHIPPING_COMPANY",
            "entity_id": 1,
            "currency": "USD",
            "amount": -3000
        }
    ]
    
    successful_tests = 0
    
    for test in test_cases:
        try:
            print(f"\n   🧪 {test['name']}:")
            
            # محاولة إدراج رصيد افتتاحي
            call_query = """
            BEGIN
                OB_PKG.INSERT_BAL(
                    p_ent_type => :p_ent_type,
                    p_ent_id => :p_ent_id,
                    p_curr => :p_curr,
                    p_amount => :p_amount,
                    p_branch => 1,
                    p_user => 1
                );
            END;
            """
            
            params = {
                "p_ent_type": test['entity_type'],
                "p_ent_id": test['entity_id'],
                "p_curr": test['currency'],
                "p_amount": test['amount']
            }
            
            oracle.execute_update(call_query, params)
            print(f"      ✅ نجح إدراج الرصيد الافتتاحي")
            successful_tests += 1
            
            # التحقق من الرصيد
            balance_query = """
            SELECT OB_PKG.GET_BAL(:p_ent_type, :p_ent_id, :p_curr, 1) FROM DUAL
            """
            
            balance = oracle.execute_query(balance_query, {
                "p_ent_type": test['entity_type'],
                "p_ent_id": test['entity_id'],
                "p_curr": test['currency']
            })
            
            if balance:
                print(f"      الرصيد المحفوظ: {balance[0][0]} {test['currency']}")
            
        except Exception as e:
            if "already exists" in str(e).lower():
                print(f"      ⚠️ الرصيد موجود مسبقاً")
                successful_tests += 1
            else:
                print(f"      ❌ فشل الاختبار: {str(e)}")
    
    print(f"\nملخص اختبارات OB_PKG:")
    print(f"   نجح: {successful_tests}/{len(test_cases)}")
    
    return successful_tests

def show_all_entity_types():
    """عرض جميع أنواع الكيانات الموجودة"""
    
    oracle = OracleManager()
    
    print("\n📋 جميع أنواع الكيانات الموجودة:")
    print("=" * 70)
    
    query = """
    SELECT entity_type_code, entity_name_ar, entity_name_en, 
           module_name, account_prefix, sort_order, is_active
    FROM ENTITY_TYPES
    ORDER BY sort_order, entity_type_code
    """
    
    result = oracle.execute_query(query)
    if result:
        print("   الكود | الاسم العربي | الاسم الإنجليزي | الوحدة | البادئة | الترتيب | نشط")
        print("   " + "-" * 90)
        
        for row in result:
            status = "✅" if row[6] == 1 else "❌"
            print(f"   {row[0][:15]:<15} | {row[1][:15]:<15} | {row[2][:20]:<20} | {row[3][:10]:<10} | {row[4] or 'N/A':<6} | {row[5]:<6} | {status}")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء إضافة أنواع الكيانات الجديدة بالبيانات الكاملة")
    print("=" * 80)
    
    try:
        # 1. إضافة أنواع الكيانات بالبيانات الكاملة
        successful_count = add_complete_entity_types()
        
        if successful_count > 0:
            # 2. التحقق من النتائج
            verify_complete_entity_types()
            
            # 3. اختبار مع OB_PKG
            test_with_ob_pkg()
            
            # 4. عرض جميع أنواع الكيانات
            show_all_entity_types()
            
            print("\n🎉 تم إكمال إضافة أنواع الكيانات الجديدة بنجاح!")
            print("✅ المهمة dchjpJRykotw4cHB7YuFy4 مكتملة نهائياً!")
            
            return True
        else:
            print("\n❌ فشل في إضافة أنواع الكيانات")
            return False
            
    except Exception as e:
        print(f"\n❌ خطأ عام: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n" + "=" * 80)
        print("🏁 انتهى إضافة أنواع الكيانات بنجاح!")
    else:
        print("\n" + "=" * 80)
        print("🚫 فشل في إضافة أنواع الكيانات - يرجى مراجعة الأخطاء")
