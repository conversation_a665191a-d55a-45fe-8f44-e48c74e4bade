{% extends "base.html" %}

{% block extra_css %}
<!-- Handsontable CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/handsontable@12.4.0/dist/handsontable.full.min.css">

<style>
.contract-form {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.form-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.form-body {
    padding: 30px;
}

.section-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    margin: 20px -30px;
    font-weight: 600;
    font-size: 18px;
}

.table-container {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.handsontable {
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}
</style>
{% endblock %}

{% block content %}
<div class="contract-form">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-11">
                <div class="form-container">
                    <!-- رأس النموذج -->
                    <div class="form-header">
                        <h1><i class="fas fa-file-contract me-3"></i>إضافة عقد جديد</h1>
                        <p>تسجيل بيانات العقود مع الموردين وربطها بأوامر الشراء</p>
                    </div>

                    <!-- نموذج البيانات -->
                    <form id="contractForm" method="POST">
                        <div class="form-body">
                            <!-- القسم الأول: البيانات الرئيسية -->
                            <div class="section-title">
                                <i class="fas fa-info-circle"></i>
                                البيانات الرئيسية
                            </div>

                            <div class="row">
                                <!-- رقم الفرع -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">رقم الفرع <span class="required">*</span></label>
                                        <select class="form-select" id="branch_id" name="branch_id" required>
                                            <option value="">اختر الفرع</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- رقم العقد -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">رقم العقد</label>
                                        <input type="text" class="form-control" id="contract_number" name="contract_number" 
                                               placeholder="سيتم توليده تلقائياً">
                                    </div>
                                </div>

                                <!-- التاريخ -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">التاريخ <span class="required">*</span></label>
                                        <input type="date" class="form-control" id="contract_date" name="contract_date" required>
                                    </div>
                                </div>
                            </div>

                            <!-- القسم الثاني: البيانات التفصيلية -->
                            <div class="section-title mt-4">
                                <i class="fas fa-table"></i>
                                البيانات التفصيلية - أصناف العقد
                            </div>

                            <!-- حاوي الجدول -->
                            <div class="table-container">
                                <!-- الجدول التفاعلي Handsontable -->
                                <div id="contractDetailsTable" style="height: 400px; width: 100%; direction: rtl;"></div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>حفظ العقد
                            </button>
                            <button type="button" class="btn btn-secondary btn-lg ms-3" onclick="window.history.back()">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Handsontable JS -->
<script src="https://cdn.jsdelivr.net/npm/handsontable@12.4.0/dist/handsontable.full.min.js"></script>

<script>
// متغيرات عامة
let contractTable;

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء تحميل صفحة العقد الجديد...');
    
    // انتظار تحميل Handsontable ثم تهيئة الجدول
    setTimeout(() => {
        initializeContractTable();
    }, 1000);

    console.log('✅ تم تحميل الصفحة بنجاح');
});

// تهيئة جدول Handsontable
function initializeContractTable() {
    console.log('🔧 بدء تهيئة جدول Handsontable...');
    
    const container = document.getElementById('contractDetailsTable');
    if (!container) {
        console.error('❌ لم يتم العثور على عنصر الجدول');
        return;
    }

    // إنشاء بيانات أولية فارغة
    const initialData = [];
    for (let i = 0; i < 20; i++) {
        initialData.push(['', '', '', '', '', '', '', '', '', '', '', '']);
    }

    try {
        // إنشاء جدول Handsontable
        contractTable = new Handsontable(container, {
            data: initialData,
            colHeaders: [
                'كود الصنف',
                'اسم الصنف', 
                'الوحدة',
                'الكمية',
                'الكمية المجانية',
                'سعر الوحدة',
                'نسبة الخصم %',
                'مبلغ الضريبة',
                'الإجمالي',
                'تاريخ الإنتاج',
                'تاريخ الانتهاء',
                'ملاحظات'
            ],
            columns: [
                { type: 'text', width: 120 },
                { type: 'text', width: 200, readOnly: true },
                { type: 'text', width: 80, readOnly: true },
                { type: 'numeric', width: 100 },
                { type: 'numeric', width: 100 },
                { type: 'numeric', width: 100 },
                { type: 'numeric', width: 100 },
                { type: 'numeric', width: 100 },
                { type: 'numeric', width: 120, readOnly: true },
                { type: 'date', width: 120 },
                { type: 'date', width: 120 },
                { type: 'text', width: 150 }
            ],
            rowHeaders: true,
            height: 400,
            licenseKey: 'non-commercial-and-evaluation',
            contextMenu: true,
            fillHandle: true,
            copyPaste: true,
            manualRowResize: true,
            manualColumnResize: true,
            stretchH: 'all'
        });

        console.log('✅ تم تهيئة جدول Handsontable بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في تهيئة الجدول:', error);
        container.innerHTML = '<div class="alert alert-danger">خطأ في تحميل الجدول: ' + error.message + '</div>';
    }
}
</script>
{% endblock %}
