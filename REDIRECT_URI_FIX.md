# إصلاح مشكلة Redirect URI في Azure Portal
# Redirect URI Fix for Azure Portal

## 🔍 **المشكلة:**
```
❌ خطأ: Must start with "HTTPS" or "http://localhost" Must be a valid URL
```

## ✅ **الحل:**

### 1️⃣ **استخدم هذا الرابط بدلاً من localhost:**
```
http://127.0.0.1:5000/auth/onedrive/callback
```

### 2️⃣ **في Azure Portal:**

#### أ. **إذا كنت تنشئ تطبيق جديد:**
```
📝 Redirect URI:
   Type: Web
   URL: http://127.0.0.1:5000/auth/onedrive/callback
```

#### ب. **إذا كان التطبيق موجود بالفعل:**
1. اذهب إلى التطبيق في Azure Portal
2. **Authentication** من القائمة الجانبية
3. **Add a platform** → **Web**
4. **Redirect URIs**: `http://127.0.0.1:5000/auth/onedrive/callback`
5. **Configure**

### 3️⃣ **بدائل أخرى مقبولة:**

```
✅ http://127.0.0.1:5000/auth/onedrive/callback
✅ http://localhost:8080/auth/onedrive/callback
✅ https://yourdomain.com/auth/onedrive/callback
```

### 4️⃣ **تأكد من التطابق:**

**في Azure Portal:**
```
http://127.0.0.1:5000/auth/onedrive/callback
```

**في SASERP (cloud_config.json):**
```json
{
    "onedrive": {
        "redirect_uri": "http://127.0.0.1:5000/auth/onedrive/callback"
    }
}
```

## 🎯 **خطوات سريعة:**

1. **غير الرابط** في Azure Portal إلى: `http://127.0.0.1:5000/auth/onedrive/callback`
2. **احفظ** التغييرات
3. **انسخ** Client ID و Client Secret
4. **حدث** إعدادات SASERP
5. **اختبر** الاتصال

## ✅ **تم الإصلاح بالفعل في SASERP:**

جميع ملفات SASERP تم تحديثها لاستخدام `127.0.0.1` بدلاً من `localhost`.

## 🚀 **الآن يمكنك المتابعة:**

1. استخدم الرابط الجديد في Azure Portal
2. أكمل إعداد التطبيق
3. احصل على Client ID و Client Secret
4. حدث إعدادات SASERP
