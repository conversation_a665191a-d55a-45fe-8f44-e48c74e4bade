<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة وثائق الشحنة - {{ shipment.shipment_number }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        
        .container-fluid {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content-section {
            padding: 30px;
        }
        
        .shipment-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #3498db;
        }
        
        .stats-row {
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-top: 4px solid #3498db;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        
        .upload-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .documents-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .document-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #28a745;
            transition: all 0.3s ease;
        }
        
        .document-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .document-type-badge {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 3px 10px;
            border-radius: 12px;
            font-size: 0.75em;
            font-weight: 500;
            white-space: nowrap;
            box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
            transition: all 0.2s ease;
        }

        .document-type-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(52, 152, 219, 0.4);
        }

        /* تحسين المسافات في عرض اسم الملف */
        .d-flex.gap-2 {
            gap: 0.5rem !important;
        }

        .document-name-container h6 {
            color: #2c3e50;
            font-weight: 600;
            margin: 0;
        }

        /* ألوان مختلفة لأنواع الوثائق */
        .document-type-badge.invoice {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .document-type-badge.contract {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .document-type-badge.bill-of-lading {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .document-type-badge.certificate {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }

        .document-type-badge.customs {
            background: linear-gradient(135deg, #34495e, #2c3e50);
        }

        /* ألوان للوثائق الجديدة */
        .document-type-badge.مستندات-الجمارك,
        .document-type-badge.customs-documents {
            background: linear-gradient(135deg, #8b4513, #654321);
        }

        .document-type-badge.المستندات-الأولية,
        .document-type-badge.preliminary-documents {
            background: linear-gradient(135deg, #20b2aa, #008b8b);
        }

        .document-type-badge.الفاتورة-dn,
        .document-type-badge.dn-invoice {
            background: linear-gradient(135deg, #ff6347, #dc143c);
        }

        .document-type-badge.other {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
        }

        /* تحسين اسم الوثيقة */
        .document-name {
            color: #2c3e50;
            font-weight: 600;
            font-size: 1rem;
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .document-name:hover {
            color: #3498db;
        }

        /* تحسين التخطيط العام */
        .document-info-container {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex-wrap: wrap;
        }
        
        .file-size {
            color: #7f8c8d;
            font-size: 0.85em;
        }

        /* تنسيقات الملفات المختارة */
        .selected-files-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .file-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef !important;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .file-item:hover {
            background: #e9ecef;
            border-color: #dee2e6 !important;
        }

        .file-item .btn-outline-danger {
            border: none;
            background: transparent;
            color: #dc3545;
            padding: 2px 6px;
            font-size: 0.8em;
        }

        .file-item .btn-outline-danger:hover {
            background: #dc3545;
            color: white;
        }

        /* تحسين شريط التقدم */
        .progress-upload {
            display: none;
            margin-top: 1rem;
        }

        .progress-upload .progress {
            height: 8px;
            border-radius: 4px;
            background-color: #e9ecef;
        }

        .progress-upload .progress-bar {
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 4px;
            transition: width 0.3s ease;
            font-size: 0.7em;
            line-height: 8px;
        }

        /* أيقونات الملفات */
        .file-item i.fa-file-pdf { color: #dc3545; }
        .file-item i.fa-file-word { color: #2b579a; }
        .file-item i.fa-file-excel { color: #217346; }
        .file-item i.fa-file-image { color: #17a2b8; }
        .file-item i.fa-file-archive { color: #ffc107; }
        .file-item i.fa-file-alt { color: #6c757d; }
        
        .upload-area {
            border: 2px dashed #3498db;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            background: #e3f2fd;
            border-color: #2980b9;
        }
        
        .upload-area.dragover {
            background: #e3f2fd;
            border-color: #2980b9;
            transform: scale(1.02);
        }
        
        .btn-upload {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-upload:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
            color: white;
        }
        
        .btn-download {
            background: linear-gradient(135deg, #17a2b8, #138496);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85em;
            transition: all 0.3s ease;
        }
        
        .btn-download:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4);
            color: white;
        }
        
        .btn-delete {
            background: linear-gradient(135deg, #dc3545, #c82333);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85em;
            transition: all 0.3s ease;
        }
        
        .btn-delete:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
            color: white;
        }
        
        .no-documents {
            text-align: center;
            padding: 60px;
            color: #7f8c8d;
        }
        
        .progress-upload {
            display: none;
            margin-top: 15px;
        }

        /* تحسين أزرار الإجراءات الجديدة */
        .btn-group-sm .btn {
            margin: 0;
            transition: all 0.2s ease;
            border-radius: 0.25rem !important;
        }

        .btn-group-sm .btn:first-child {
            border-top-left-radius: 0.375rem !important;
            border-bottom-left-radius: 0.375rem !important;
        }

        .btn-group-sm .btn:last-child {
            border-top-right-radius: 0.375rem !important;
            border-bottom-right-radius: 0.375rem !important;
        }

        .btn-group-sm .btn:hover {
            transform: translateY(-1px);
            z-index: 2;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .btn-outline-info:hover {
            background-color: #17a2b8;
            border-color: #17a2b8;
            color: white;
        }

        .btn-outline-success:hover {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
        }

        .btn-outline-primary:hover {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
        }

        .btn-outline-secondary:hover {
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
        }

        .btn-outline-danger:hover {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }

        /* تحسين الأزرار المعطلة */
        .btn-group-sm .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn-group-sm .btn:disabled:hover {
            background-color: transparent;
            border-color: #dee2e6;
            color: #6c757d;
            transform: none !important;
            box-shadow: none !important;
        }

        /* تحسين الرسائل المنبثقة */
        .alert.position-fixed {
            animation: slideInRight 0.3s ease-out;
            transition: all 0.3s ease;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* تحسين شكل الوثائق */
        .document-row {
            transition: all 0.3s ease;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }

        .document-row:hover {
            background: #ffffff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        /* تحسين أيقونات الملفات */
        .file-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .file-icon.pdf { color: #dc3545; }
        .file-icon.doc { color: #007bff; }
        .file-icon.xls { color: #28a745; }
        .file-icon.img { color: #ffc107; }
        .file-icon.default { color: #6c757d; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header Section -->
        <div class="header-section">
            <h1 class="mb-3">
                <i class="fas fa-file-alt me-3"></i>
                إدارة وثائق الشحنة
            </h1>
            <p class="lead mb-0">
                رقم الشحنة: {{ shipment.shipment_number }}
            </p>
        </div>
        
        <!-- Content Section -->
        <div class="content-section">
            <!-- معلومات الشحنة -->
            <div class="shipment-info">
                <div class="row">
                    <div class="col-md-3">
                        <strong>رقم الشحنة:</strong><br>
                        <span class="text-primary">{{ shipment.shipment_number }}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>الحالة:</strong><br>
                        <span class="badge bg-info">{{ shipment.status }}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>الشاحن:</strong><br>
                        {{ shipment.shipper_name or 'غير محدد' }}
                    </div>
                    <div class="col-md-3">
                        <strong>المستلم:</strong><br>
                        {{ shipment.consignee_name or 'غير محدد' }}
                    </div>
                </div>
            </div>
            
            <!-- إحصائيات الوثائق -->
            <div class="row stats-row">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number">{{ stats.total_documents }}</div>
                        <div class="stat-label">إجمالي الوثائق</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number">{{ stats.total_size_mb }}</div>
                        <div class="stat-label">الحجم (MB)</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number">{{ stats.uploaded_documents }}</div>
                        <div class="stat-label">وثائق مرفوعة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number">{{ stats.generated_documents }}</div>
                        <div class="stat-label">وثائق مُنشأة</div>
                    </div>
                </div>
            </div>
            
            <!-- قسم رفع الوثائق -->
            <div class="upload-section">
                <h5 class="mb-4">
                    <i class="fas fa-cloud-upload-alt me-2"></i>
                    رفع وثيقة جديدة
                </h5>
                
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">نوع الوثيقة:</label>
                            <select class="form-select" name="document_type" required>
                                <option value="">اختر نوع الوثيقة</option>
                                {% for key, value in document_types.items() %}
                                <option value="{{ key }}">{{ value }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">اسم الوثيقة:</label>
                            <input type="text" class="form-control" name="document_name" 
                                   placeholder="اسم الوثيقة (اختياري)">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">ملاحظات:</label>
                            <input type="text" class="form-control" name="notes" 
                                   placeholder="ملاحظات (اختياري)">
                        </div>
                    </div>
                    
                    <div class="upload-area mt-3" id="uploadArea">
                        <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                        <h5>اسحب الملفات هنا أو انقر للاختيار</h5>
                        <p class="text-muted">الأنواع المدعومة: PDF, DOC, XLS, JPG, PNG, ZIP, RAR (حد أقصى 10 MB لكل ملف)</p>
                        <small class="text-info">💡 يمكنك اختيار عدة ملفات مرة واحدة للوثيقة نفسها</small>
                        <input type="file" id="fileInput" name="document_files"
                               accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.tiff,.txt,.csv,.zip,.rar"
                               style="display: none;" multiple required>
                        <button type="button" class="btn btn-upload" onclick="document.getElementById('fileInput').click()">
                            <i class="fas fa-folder-open me-2"></i>
                            اختيار ملفات
                        </button>
                        <div id="selectedFiles" class="mt-3" style="display: none;">
                            <h6 class="text-success mb-2">
                                <i class="fas fa-check-circle me-1"></i>
                                الملفات المختارة:
                            </h6>
                            <div id="filesList" class="selected-files-list"></div>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <span id="filesCount">0</span> ملف مختار |
                                    الحجم الإجمالي: <span id="totalSize">0 KB</span>
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="progress-upload">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted">جاري الرفع...</small>
                    </div>
                    
                    <div class="text-center mt-3">
                        <button type="submit" class="btn btn-upload btn-lg" id="uploadButton">
                            <i class="fas fa-upload me-2"></i>
                            رفع الوثائق
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- قسم الوثائق -->
            <div class="documents-section">
                <h5 class="mb-4">
                    <i class="fas fa-folder-open me-2"></i>
                    الوثائق المرفوعة
                </h5>
                
                <div id="documentsContainer">
                    {% if documents %}
                        {% for doc in documents %}
                        <div class="document-card document-row" data-doc-id="{{ doc.id }}">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file-alt fa-2x text-primary me-3"></i>
                                        <div>
                                            <div class="d-flex align-items-center gap-2 mb-1">
                                                <h6 class="mb-0 document-name">{{ doc.document_name }}</h6>
                                                <span class="document-type-badge {{ doc.document_type_name|lower|replace(' ', '-')|replace('_', '-') }}">
                                                    {{ doc.document_type_name }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <small class="file-size">
                                        <i class="fas fa-weight-hanging me-1"></i>
                                        {{ doc.file_size_mb }} MB
                                    </small><br>
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        {{ doc.uploaded_by }}
                                    </small><br>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ doc.uploaded_at.strftime('%Y-%m-%d %H:%M') if doc.uploaded_at else '' }}
                                    </small>
                                </div>
                                <div class="col-md-3 text-end">
                                    <div class="btn-group btn-group-sm" role="group" aria-label="إجراءات الوثيقة">
                                        <button class="btn btn-outline-secondary"
                                                onclick="viewDocumentDetails({{ doc.id }})"
                                                title="استعراض تفاصيل الوثيقة">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <a href="{{ url_for('shipments.download_cargo_document', document_id=doc.id) }}"
                                           class="btn btn-outline-info" title="تحميل الوثيقة">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <!-- أزرار إنشاء الروابط -->
                                        <button class="btn btn-success btn-sm"
                                                onclick="createShareLink({{ doc.id }}, 'nextcloud')"
                                                title="إنشاء رابط Nextcloud">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.232 7.005c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zm7.553 0c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zM12.017 18.55c-2.69 0-4.923-1.273-6.096-3.234l1.431-.716c.79 1.431 2.334 2.39 4.665 2.39s3.875-.959 4.665-2.39l1.431.716c-1.173 1.961-3.406 3.234-6.096 3.234z"/>
                                            </svg>
                                            {% if doc.nextcloud_share_link %}
                                                <span class="badge bg-success rounded-pill ms-1">●</span>
                                            {% endif %}
                                        </button>

                                        <button class="btn btn-primary btn-sm"
                                                onclick="createShareLink({{ doc.id }}, 'onedrive')"
                                                title="إنشاء رابط OneDrive">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M8.9 2.9c-1.6 0-2.9 1.3-2.9 2.9 0 .2 0 .4.1.6C3.6 7.2 1.5 9.7 1.5 12.8c0 3.5 2.8 6.3 6.3 6.3h9.4c2.9 0 5.3-2.4 5.3-5.3 0-2.5-1.7-4.6-4-5.2-.1-3.1-2.7-5.7-5.8-5.7-1.9 0-3.6.9-4.6 2.3-.4-.2-.8-.3-1.2-.3zm0 1.5c.3 0 .6.1.8.2l.4.2.2-.4c.7-1.3 2.1-2.1 3.6-2.1 2.3 0 4.2 1.9 4.2 4.2v.8h.8c2.1 0 3.8 1.7 3.8 3.8s-1.7 3.8-3.8 3.8H7.8c-2.7 0-4.8-2.2-4.8-4.8 0-2.4 1.7-4.4 4-4.7l.5-.1-.1-.5c0-.2-.1-.4-.1-.6 0-.8.6-1.4 1.4-1.4z"/>
                                            </svg>
                                            {% if doc.onedrive_share_link %}
                                                <span class="badge bg-primary rounded-pill ms-1">●</span>
                                            {% endif %}
                                        </button>

                                        <!-- قائمة النسخ -->
                                        <div class="btn-group">
                                            <button class="btn btn-warning btn-sm dropdown-toggle"
                                                    data-bs-toggle="dropdown"
                                                    title="نسخ الروابط"
                                                    {% if not (doc.nextcloud_share_link or doc.onedrive_share_link) %}disabled{% endif %}>
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                {% if doc.nextcloud_share_link %}
                                                <li><a class="dropdown-item" onclick="copyShareLink('{{ doc.nextcloud_share_link }}')">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="#198754" class="me-2">
                                                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.232 7.005c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zm7.553 0c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zM12.017 18.55c-2.69 0-4.923-1.273-6.096-3.234l1.431-.716c.79 1.431 2.334 2.39 4.665 2.39s3.875-.959 4.665-2.39l1.431.716c-1.173 1.961-3.406 3.234-6.096 3.234z"/>
                                                    </svg>
                                                    نسخ رابط Nextcloud
                                                </a></li>
                                                {% endif %}
                                                {% if doc.onedrive_share_link %}
                                                <li><a class="dropdown-item" onclick="copyShareLink('{{ doc.onedrive_share_link }}')">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="#0d6efd" class="me-2">
                                                        <path d="M8.9 2.9c-1.6 0-2.9 1.3-2.9 2.9 0 .2 0 .4.1.6C3.6 7.2 1.5 9.7 1.5 12.8c0 3.5 2.8 6.3 6.3 6.3h9.4c2.9 0 5.3-2.4 5.3-5.3 0-2.5-1.7-4.6-4-5.2-.1-3.1-2.7-5.7-5.8-5.7-1.9 0-3.6.9-4.6 2.3-.4-.2-.8-.3-1.2-.3zm0 1.5c.3 0 .6.1.8.2l.4.2.2-.4c.7-1.3 2.1-2.1 3.6-2.1 2.3 0 4.2 1.9 4.2 4.2v.8h.8c2.1 0 3.8 1.7 3.8 3.8s-1.7 3.8-3.8 3.8H7.8c-2.7 0-4.8-2.2-4.8-4.8 0-2.4 1.7-4.4 4-4.7l.5-.1-.1-.5c0-.2-.1-.4-.1-.6 0-.8.6-1.4 1.4-1.4z"/>
                                                    </svg>
                                                    نسخ رابط OneDrive
                                                </a></li>
                                                {% endif %}
                                                {% if not (doc.nextcloud_share_link or doc.onedrive_share_link) %}
                                                <li><span class="dropdown-item-text text-muted">لا توجد روابط متاحة</span></li>
                                                {% endif %}
                                            </ul>
                                        </div>

                                        <!-- قائمة الفتح -->
                                        <div class="btn-group">
                                            <button class="btn btn-secondary btn-sm dropdown-toggle"
                                                    data-bs-toggle="dropdown"
                                                    title="فتح الروابط"
                                                    {% if not (doc.nextcloud_share_link or doc.onedrive_share_link) %}disabled{% endif %}>
                                                <i class="fas fa-external-link-alt"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                {% if doc.nextcloud_share_link %}
                                                <li><a class="dropdown-item" onclick="openShareLink('{{ doc.nextcloud_share_link }}')">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="#198754" class="me-2">
                                                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.232 7.005c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zm7.553 0c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zM12.017 18.55c-2.69 0-4.923-1.273-6.096-3.234l1.431-.716c.79 1.431 2.334 2.39 4.665 2.39s3.875-.959 4.665-2.39l1.431.716c-1.173 1.961-3.406 3.234-6.096 3.234z"/>
                                                    </svg>
                                                    فتح رابط Nextcloud
                                                </a></li>
                                                {% endif %}
                                                {% if doc.onedrive_share_link %}
                                                <li><a class="dropdown-item" onclick="openShareLink('{{ doc.onedrive_share_link }}')">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="#0d6efd" class="me-2">
                                                        <path d="M8.9 2.9c-1.6 0-2.9 1.3-2.9 2.9 0 .2 0 .4.1.6C3.6 7.2 1.5 9.7 1.5 12.8c0 3.5 2.8 6.3 6.3 6.3h9.4c2.9 0 5.3-2.4 5.3-5.3 0-2.5-1.7-4.6-4-5.2-.1-3.1-2.7-5.7-5.8-5.7-1.9 0-3.6.9-4.6 2.3-.4-.2-.8-.3-1.2-.3zm0 1.5c.3 0 .6.1.8.2l.4.2.2-.4c.7-1.3 2.1-2.1 3.6-2.1 2.3 0 4.2 1.9 4.2 4.2v.8h.8c2.1 0 3.8 1.7 3.8 3.8s-1.7 3.8-3.8 3.8H7.8c-2.7 0-4.8-2.2-4.8-4.8 0-2.4 1.7-4.4 4-4.7l.5-.1-.1-.5c0-.2-.1-.4-.1-.6 0-.8.6-1.4 1.4-1.4z"/>
                                                    </svg>
                                                    فتح رابط OneDrive
                                                </a></li>
                                                {% endif %}
                                                {% if not (doc.nextcloud_share_link or doc.onedrive_share_link) %}
                                                <li><span class="dropdown-item-text text-muted">لا توجد روابط متاحة</span></li>
                                                {% endif %}
                                            </ul>
                                        </div>
                                        <button class="btn btn-outline-danger"
                                                onclick="deleteDocument({{ doc.id }})"
                                                title="حذف الوثيقة">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {% if doc.notes and doc.notes != 'None' %}
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-sticky-note me-1"></i>
                                    {{ doc.notes|truncate(200) }}
                                </small>
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="no-documents">
                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                            <h5>لا توجد وثائق مرفوعة</h5>
                            <p>ابدأ برفع الوثائق المطلوبة للشحنة</p>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- أزرار التنقل -->
            <div class="text-center mt-4">
                <a href="{{ url_for('shipments.dashboard_fullscreen') }}" class="btn btn-secondary btn-lg me-3">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للشحنات
                </a>
                <button class="btn btn-primary btn-lg" onclick="generateBillOfLading()">
                    <i class="fas fa-file-pdf me-2"></i>
                    إنشاء بوليصة الشحن
                </button>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // حل مبسط لمشكلة fileInput
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOM loaded, initializing document upload...');

            // تأخير للتأكد من تحميل جميع العناصر
            setTimeout(function() {
                console.log('⏰ Starting initialization after delay...');

                // متغيرات عامة
                const shipmentId = {{ shipment.id }};

                // البحث عن العناصر بطرق متعددة
                let uploadArea = document.getElementById('uploadArea');
                let fileInput = document.getElementById('fileInput') ||
                               document.querySelector('input[name="document_files"]') ||
                               document.querySelector('input[type="file"]');

                console.log('🔍 Elements check:');
                console.log('  - uploadArea:', uploadArea);
                console.log('  - fileInput:', fileInput);

                // إنشاء fileInput إذا لم يوجد
                if (!fileInput && uploadArea) {
                    console.log('🔧 إنشاء fileInput جديد...');
                    fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.id = 'fileInput';
                    fileInput.name = 'document_files';
                    fileInput.accept = '.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.tiff,.txt,.csv,.zip,.rar';
                    fileInput.style.display = 'none';
                    fileInput.multiple = true;
                    fileInput.required = true;

                    uploadArea.appendChild(fileInput);
                    console.log('✅ تم إنشاء fileInput جديد');
                }

                // تهيئة العناصر
                if (uploadArea && fileInput) {
                    console.log('✅ تهيئة العناصر...');

                    // وظيفة عرض الملفات المختارة
                    function updateFilesDisplay() {
                        const files = fileInput.files;
                        const selectedFiles = document.getElementById('selectedFiles');
                        const filesList = document.getElementById('filesList');
                        const filesCount = document.getElementById('filesCount');
                        const totalSize = document.getElementById('totalSize');
                        const uploadButton = document.getElementById('uploadButton');

                        if (files && files.length > 0 && selectedFiles && filesList) {
                            // حساب الحجم الإجمالي
                            let totalSizeBytes = 0;
                            for (let i = 0; i < files.length; i++) {
                                totalSizeBytes += files[i].size;
                            }

                            // تحديث العدادات
                            if (filesCount) filesCount.textContent = files.length;
                            if (totalSize) totalSize.textContent = (totalSizeBytes / 1024 / 1024).toFixed(2) + ' MB';

                            // إنشاء قائمة الملفات
                            filesList.innerHTML = '';
                            for (let i = 0; i < files.length; i++) {
                                const file = files[i];
                                const fileItem = document.createElement('div');
                                fileItem.className = 'file-item d-flex justify-content-between align-items-center mb-2 p-2 border rounded';

                                // تحديد أيقونة الملف حسب النوع
                                const extension = file.name.split('.').pop().toLowerCase();
                                let icon = 'fas fa-file';
                                if (['pdf'].includes(extension)) icon = 'fas fa-file-pdf text-danger';
                                else if (['doc', 'docx'].includes(extension)) icon = 'fas fa-file-word text-primary';
                                else if (['xls', 'xlsx'].includes(extension)) icon = 'fas fa-file-excel text-success';
                                else if (['jpg', 'jpeg', 'png', 'gif', 'tiff'].includes(extension)) icon = 'fas fa-file-image text-info';
                                else if (['zip', 'rar'].includes(extension)) icon = 'fas fa-file-archive text-warning';
                                else if (['txt', 'csv'].includes(extension)) icon = 'fas fa-file-alt text-secondary';

                                fileItem.innerHTML = `
                                    <div class="d-flex align-items-center">
                                        <i class="${icon} me-2"></i>
                                        <div>
                                            <div class="fw-bold">${file.name}</div>
                                            <small class="text-muted">${(file.size / 1024 / 1024).toFixed(2)} MB</small>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(${i})">
                                        <i class="fas fa-times"></i>
                                    </button>
                                `;
                                filesList.appendChild(fileItem);
                            }

                            selectedFiles.style.display = 'block';

                            // تحديث نص الزر
                            if (uploadButton) {
                                uploadButton.innerHTML = `
                                    <i class="fas fa-upload me-2"></i>
                                    رفع ${files.length} ${files.length === 1 ? 'ملف' : 'ملفات'}
                                `;
                            }

                            console.log(`✅ تم اختيار ${files.length} ملف`);
                        } else if (selectedFiles) {
                            selectedFiles.style.display = 'none';
                            if (uploadButton) {
                                uploadButton.innerHTML = `
                                    <i class="fas fa-upload me-2"></i>
                                    رفع الوثائق
                                `;
                            }
                        }
                    }

                    // دالة حذف ملف من القائمة
                    window.removeFile = function(index) {
                        const dt = new DataTransfer();
                        const files = fileInput.files;

                        for (let i = 0; i < files.length; i++) {
                            if (i !== index) {
                                dt.items.add(files[i]);
                            }
                        }

                        fileInput.files = dt.files;
                        updateFilesDisplay();
                    };

                    // إضافة event listeners
                    fileInput.addEventListener('change', updateFilesDisplay);

                    // تحديث زر اختيار الملف
                    const selectButton = uploadArea.querySelector('.btn-upload');
                    if (selectButton) {
                        selectButton.onclick = function() {
                            fileInput.click();
                        };
                    }

                    console.log('✅ تم تهيئة العناصر بنجاح');
                } else {
                    console.error('❌ فشل في تهيئة العناصر');
                }

                // تهيئة نموذج الرفع
                const uploadForm = document.getElementById('uploadForm');
                if (uploadForm) {
                    uploadForm.addEventListener('submit', function(e) {
                        e.preventDefault();

                        console.log('📤 محاولة رفع الوثائق...');

                        // البحث عن العناصر مرة أخرى للتأكد
                        const currentFileInput = document.getElementById('fileInput') ||
                                               document.querySelector('input[name="document_files"]') ||
                                               document.querySelector('input[type="file"]');

                        const documentType = document.querySelector('select[name="document_type"]');

                        if (!currentFileInput) {
                            alert('خطأ: لم يتم العثور على عنصر اختيار الملفات');
                            return;
                        }

                        if (!documentType || !documentType.value) {
                            alert('يرجى اختيار نوع الوثيقة');
                            return;
                        }

                        if (!currentFileInput.files || currentFileInput.files.length === 0) {
                            alert('يرجى اختيار ملفات للرفع');
                            return;
                        }

                        const files = currentFileInput.files;
                        console.log(`✅ جميع الفحوصات نجحت، بدء رفع ${files.length} ملف...`);

                        // رفع الملفات واحد تلو الآخر
                        uploadMultipleFiles(files, documentType.value);

                    });
                }

                // دالة رفع عدة ملفات
                async function uploadMultipleFiles(files, documentType) {
                    const progressBar = document.querySelector('.progress-upload');
                    const progressBarInner = progressBar?.querySelector('.progress-bar');
                    const uploadButton = document.getElementById('uploadButton');

                    // إظهار شريط التقدم
                    if (progressBar) {
                        progressBar.style.display = 'block';
                    }

                    // تعطيل زر الرفع
                    if (uploadButton) {
                        uploadButton.disabled = true;
                        uploadButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الرفع...';
                    }

                    let successCount = 0;
                    let errorCount = 0;
                    const errors = [];

                    for (let i = 0; i < files.length; i++) {
                        const file = files[i];
                        console.log(`📤 رفع الملف ${i + 1}/${files.length}: ${file.name}`);

                        // تحديث شريط التقدم
                        const progress = ((i + 1) / files.length) * 100;
                        if (progressBarInner) {
                            progressBarInner.style.width = progress + '%';
                            progressBarInner.textContent = `${i + 1}/${files.length}`;
                        }

                        try {
                            // إنشاء FormData لكل ملف
                            const formData = new FormData();
                            formData.append('document_type', documentType);
                            formData.append('document_name', document.querySelector('input[name="document_name"]')?.value || '');
                            formData.append('notes', document.querySelector('input[name="notes"]')?.value || '');
                            formData.append('document_file', file);

                            // رفع الملف
                            const response = await fetch(`/shipments/cargo/${shipmentId}/documents/upload`, {
                                method: 'POST',
                                body: formData
                            });

                            const data = await response.json();

                            if (data.success) {
                                successCount++;
                                console.log(`✅ تم رفع ${file.name} بنجاح`);
                            } else {
                                errorCount++;
                                errors.push(`${file.name}: ${data.message}`);
                                console.error(`❌ فشل رفع ${file.name}: ${data.message}`);
                            }
                        } catch (error) {
                            errorCount++;
                            errors.push(`${file.name}: خطأ في الشبكة`);
                            console.error(`❌ خطأ في رفع ${file.name}:`, error);
                        }
                    }

                    // إخفاء شريط التقدم
                    if (progressBar) {
                        progressBar.style.display = 'none';
                    }

                    // إعادة تفعيل زر الرفع
                    if (uploadButton) {
                        uploadButton.disabled = false;
                        uploadButton.innerHTML = '<i class="fas fa-upload me-2"></i>رفع الوثائق';
                    }

                    // إظهار النتائج
                    let message = '';
                    if (successCount > 0) {
                        message += `✅ تم رفع ${successCount} ملف بنجاح\n`;
                    }
                    if (errorCount > 0) {
                        message += `❌ فشل رفع ${errorCount} ملف:\n${errors.join('\n')}`;
                    }

                    if (typeof Swal !== 'undefined') {
                        Swal.fire({
                            icon: errorCount === 0 ? 'success' : 'warning',
                            title: 'نتيجة رفع الملفات',
                            text: message,
                            confirmButtonText: 'موافق'
                        }).then(() => {
                            if (successCount > 0) {
                                location.reload();
                            }
                        });
                    } else {
                        alert(message);
                        if (successCount > 0) {
                            location.reload();
                        }
                    }
                }

            }, 1000); // نهاية setTimeout
        }); // نهاية DOMContentLoaded

        // وظائف مساعدة للوثائق

        // حذف وثيقة
        function deleteDocument(documentId) {
            if (confirm('هل أنت متأكد من حذف هذه الوثيقة؟\n\nلا يمكن التراجع عن هذا الإجراء.')) {
                // إظهار مؤشر التحميل
                const deleteButton = document.querySelector(`[onclick="deleteDocument(${documentId})"]`);
                const originalContent = deleteButton.innerHTML;
                deleteButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                deleteButton.disabled = true;

                fetch(`/shipments/cargo/documents/${documentId}/delete`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('تم حذف الوثيقة بنجاح', 'success', 'fas fa-trash');

                        // إزالة صف الوثيقة بتأثير بصري
                        const documentRow = deleteButton.closest('.row');
                        if (documentRow) {
                            documentRow.style.transition = 'all 0.3s ease';
                            documentRow.style.opacity = '0';
                            documentRow.style.transform = 'translateX(-100%)';
                            setTimeout(() => {
                                documentRow.remove();
                            }, 300);
                        }
                    } else {
                        showToast('خطأ في حذف الوثيقة: ' + data.message, 'danger', 'fas fa-exclamation-circle');
                        // استعادة الزر
                        deleteButton.innerHTML = originalContent;
                        deleteButton.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('خطأ:', error);
                    showToast('حدث خطأ في حذف الوثيقة', 'danger', 'fas fa-exclamation-triangle');
                    // استعادة الزر
                    deleteButton.innerHTML = originalContent;
                    deleteButton.disabled = false;
                });
            }
        }
        
        // نسخ رابط المشاركة
        function copyShareLink(shareLink) {
            if (shareLink && shareLink.trim() !== '') {
                navigator.clipboard.writeText(shareLink).then(() => {
                    showToast('تم نسخ رابط المشاركة بنجاح!', 'success', 'fas fa-check-circle');
                }).catch(err => {
                    showToast('فشل في نسخ الرابط: ' + err, 'danger', 'fas fa-exclamation-circle');
                });
            } else {
                showToast('رابط المشاركة غير متوفر', 'warning', 'fas fa-exclamation-triangle');
            }
        }

        // فتح رابط المشاركة في نافذة جديدة
        function openShareLink(shareLink) {
            if (shareLink && shareLink.trim() !== '') {
                window.open(shareLink, '_blank', 'noopener,noreferrer');
                showToast('تم فتح رابط المشاركة في نافذة جديدة', 'info', 'fas fa-external-link-alt');
            } else {
                showToast('رابط المشاركة غير متوفر', 'warning', 'fas fa-exclamation-triangle');
            }
        }

        // استعراض تفاصيل وثيقة محددة
        function viewDocumentDetails(documentId) {
            // جلب تفاصيل الوثيقة
            fetch(`/shipments/cargo/documents/${documentId}/details`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showDocumentDetailsModal(data.document);
                    } else {
                        showToast('فشل في جلب تفاصيل الوثيقة: ' + data.message, 'danger', 'fas fa-exclamation-circle');
                    }
                })
                .catch(error => {
                    console.error('خطأ في جلب تفاصيل الوثيقة:', error);
                    showToast('حدث خطأ في جلب تفاصيل الوثيقة', 'danger', 'fas fa-exclamation-circle');
                });
        }

        // إظهار نافذة تفاصيل الوثيقة
        function showDocumentDetailsModal(doc) {
            // إنشاء نافذة منبثقة لتفاصيل الوثيقة
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'documentDetailsModal';
            modal.setAttribute('tabindex', '-1');

            // تحديد أيقونة الملف حسب النوع
            let fileIcon = 'fas fa-file';
            let fileIconColor = 'text-secondary';
            const extension = doc.file_name.split('.').pop().toLowerCase();

            switch(extension) {
                case 'pdf':
                    fileIcon = 'fas fa-file-pdf';
                    fileIconColor = 'text-danger';
                    break;
                case 'doc':
                case 'docx':
                    fileIcon = 'fas fa-file-word';
                    fileIconColor = 'text-primary';
                    break;
                case 'xls':
                case 'xlsx':
                    fileIcon = 'fas fa-file-excel';
                    fileIconColor = 'text-success';
                    break;
                case 'jpg':
                case 'jpeg':
                case 'png':
                case 'gif':
                    fileIcon = 'fas fa-file-image';
                    fileIconColor = 'text-warning';
                    break;
                case 'zip':
                case 'rar':
                    fileIcon = 'fas fa-file-archive';
                    fileIconColor = 'text-secondary';
                    break;
            }

            // تنسيق حجم الملف
            const formatFileSize = (bytes) => {
                if (!bytes) return 'غير محدد';
                const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
                const i = Math.floor(Math.log(bytes) / Math.log(1024));
                return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
            };

            // تنسيق التاريخ
            const formatDate = (dateString) => {
                if (!dateString) return 'غير محدد';
                const date = new Date(dateString);
                return date.toLocaleDateString('ar-SA', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            };

            modal.innerHTML = `
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-gradient" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <h5 class="modal-title text-white">
                                <i class="${fileIcon} ${fileIconColor} me-2"></i>
                                تفاصيل الوثيقة
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <!-- معلومات أساسية -->
                                <div class="col-md-6">
                                    <div class="card h-100 shadow-sm border-0">
                                        <div class="card-header bg-light border-0">
                                            <h6 class="mb-0 text-primary">
                                                <i class="fas fa-info-circle me-2"></i>
                                                المعلومات الأساسية
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="fw-bold text-muted">اسم الوثيقة:</label>
                                                <div>${doc.document_name}</div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="fw-bold text-muted">اسم الملف:</label>
                                                <div>${doc.file_name}</div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="fw-bold text-muted">حجم الملف:</label>
                                                <div>${formatFileSize(doc.file_size)}</div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="fw-bold text-muted">تاريخ الرفع:</label>
                                                <div>${formatDate(doc.uploaded_at)}</div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="fw-bold text-muted">رقم الشحنة:</label>
                                                <div>${doc.shipment_number}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- روابط المشاركة -->
                                <div class="col-md-6">
                                    <div class="card h-100 shadow-sm border-0">
                                        <div class="card-header bg-light border-0">
                                            <h6 class="mb-0 text-success">
                                                <i class="fas fa-share-alt me-2"></i>
                                                روابط المشاركة
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <!-- Nextcloud -->
                                            <div class="mb-4">
                                                <div class="d-flex align-items-center mb-2">
                                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" class="text-success me-2">
                                                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.232 7.005c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zm7.553 0c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zM12.017 18.55c-2.69 0-4.923-1.273-6.096-3.234l1.431-.716c.79 1.431 2.334 2.39 4.665 2.39s3.875-.959 4.665-2.39l1.431.716c-1.173 1.961-3.406 3.234-6.096 3.234z"/>
                                                    </svg>
                                                    <strong>Nextcloud</strong>
                                                </div>
                                                ${doc.nextcloud_share_link ? `
                                                    <div class="input-group mb-2">
                                                        <input type="text" class="form-control" value="${doc.nextcloud_share_link}" readonly>
                                                        <button class="btn btn-outline-success" onclick="copyShareLink('${doc.nextcloud_share_link}')" title="نسخ">
                                                            <i class="fas fa-copy"></i>
                                                        </button>
                                                        <button class="btn btn-outline-success" onclick="openShareLink('${doc.nextcloud_share_link}')" title="فتح">
                                                            <i class="fas fa-external-link-alt"></i>
                                                        </button>
                                                    </div>
                                                    <small class="text-muted">تم الإنشاء: ${formatDate(doc.nextcloud_created_at)}</small>
                                                ` : `
                                                    <div class="text-muted">
                                                        <i class="fas fa-times-circle me-1"></i>
                                                        لم يتم إنشاء رابط
                                                    </div>
                                                    <button class="btn btn-success btn-sm mt-2" onclick="createShareLink(${doc.id}, 'nextcloud')">
                                                        <i class="fas fa-plus me-1"></i>
                                                        إنشاء رابط
                                                    </button>
                                                `}
                                            </div>

                                            <!-- OneDrive -->
                                            <div class="mb-3">
                                                <div class="d-flex align-items-center mb-2">
                                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" class="text-primary me-2">
                                                        <path d="M8.9 2.9c-1.6 0-2.9 1.3-2.9 2.9 0 .2 0 .4.1.6C3.6 7.2 1.5 9.7 1.5 12.8c0 3.5 2.8 6.3 6.3 6.3h9.4c2.9 0 5.3-2.4 5.3-5.3 0-2.5-1.7-4.6-4-5.2-.1-3.1-2.7-5.7-5.8-5.7-1.9 0-3.6.9-4.6 2.3-.4-.2-.8-.3-1.2-.3zm0 1.5c.3 0 .6.1.8.2l.4.2.2-.4c.7-1.3 2.1-2.1 3.6-2.1 2.3 0 4.2 1.9 4.2 4.2v.8h.8c2.1 0 3.8 1.7 3.8 3.8s-1.7 3.8-3.8 3.8H7.8c-2.7 0-4.8-2.2-4.8-4.8 0-2.4 1.7-4.4 4-4.7l.5-.1-.1-.5c0-.2-.1-.4-.1-.6 0-.8.6-1.4 1.4-1.4z"/>
                                                    </svg>
                                                    <strong>OneDrive</strong>
                                                </div>
                                                ${doc.onedrive_share_link ? `
                                                    <div class="input-group mb-2">
                                                        <input type="text" class="form-control" value="${doc.onedrive_share_link}" readonly>
                                                        <button class="btn btn-outline-primary" onclick="copyShareLink('${doc.onedrive_share_link}')" title="نسخ">
                                                            <i class="fas fa-copy"></i>
                                                        </button>
                                                        <button class="btn btn-outline-primary" onclick="openShareLink('${doc.onedrive_share_link}')" title="فتح">
                                                            <i class="fas fa-external-link-alt"></i>
                                                        </button>
                                                    </div>
                                                    <small class="text-muted">تم الإنشاء: ${formatDate(doc.onedrive_created_at)}</small>
                                                ` : `
                                                    <div class="text-muted">
                                                        <i class="fas fa-times-circle me-1"></i>
                                                        لم يتم إنشاء رابط
                                                    </div>
                                                    <button class="btn btn-primary btn-sm mt-2" onclick="createShareLink(${doc.id}, 'onedrive')">
                                                        <i class="fas fa-plus me-1"></i>
                                                        إنشاء رابط
                                                    </button>
                                                `}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            ${doc.notes && doc.notes !== 'None' ? `
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="card shadow-sm border-0">
                                            <div class="card-header bg-light border-0">
                                                <h6 class="mb-0 text-warning">
                                                    <i class="fas fa-sticky-note me-2"></i>
                                                    ملاحظات
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                ${doc.notes}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-2"></i>
                                إغلاق
                            </button>
                            <a href="/shipments/cargo/documents/${doc.id}/download" class="btn btn-info">
                                <i class="fas fa-download me-2"></i>
                                تحميل الوثيقة
                            </a>
                        </div>
                    </div>
                </div>
            `;

            // إضافة النافذة للصفحة
            document.body.appendChild(modal);

            // إظهار النافذة
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();

            // إزالة النافذة عند الإغلاق
            modal.addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        }

        // استعراض الوثائق المرفوعة (deprecated - تم استبدالها بـ viewDocumentDetails)
        function viewUploadedDocuments() {
            // إنشاء نافذة منبثقة لاستعراض الوثائق
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'uploadedDocumentsModal';
            modal.setAttribute('tabindex', '-1');
            modal.innerHTML = `
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-info text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-eye me-2"></i>
                                استعراض الوثائق المرفوعة - الشحنة {{ shipment.shipment_number }}
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div id="documentsGrid" class="row">
                                <!-- سيتم تحميل الوثائق هنا -->
                                <div class="col-12 text-center">
                                    <div class="spinner-border text-info" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                    <p class="mt-2">جاري تحميل الوثائق...</p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-2"></i>
                                إغلاق
                            </button>
                            <button type="button" class="btn btn-info" onclick="refreshDocumentsView()">
                                <i class="fas fa-sync-alt me-2"></i>
                                تحديث
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // إضافة النافذة للصفحة
            document.body.appendChild(modal);

            // إظهار النافذة
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();

            // تحميل الوثائق
            loadUploadedDocuments();

            // إزالة النافذة عند الإغلاق
            modal.addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        }

        // تحميل الوثائق المرفوعة
        function loadUploadedDocuments() {
            fetch(`/shipments/cargo/{{ shipment.id }}/documents/list`)
                .then(response => response.json())
                .then(data => {
                    const documentsGrid = document.getElementById('documentsGrid');

                    if (data.success && data.documents && data.documents.length > 0) {
                        documentsGrid.innerHTML = '';

                        data.documents.forEach(doc => {
                            const docCard = createDocumentCard(doc);
                            documentsGrid.appendChild(docCard);
                        });
                    } else {
                        documentsGrid.innerHTML = `
                            <div class="col-12 text-center">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    لا توجد وثائق مرفوعة لهذه الشحنة
                                </div>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحميل الوثائق:', error);
                    const documentsGrid = document.getElementById('documentsGrid');
                    documentsGrid.innerHTML = `
                        <div class="col-12 text-center">
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                حدث خطأ في تحميل الوثائق
                            </div>
                        </div>
                    `;
                });
        }

        // إنشاء بطاقة وثيقة
        function createDocumentCard(doc) {
            const col = document.createElement('div');
            col.className = 'col-md-6 col-lg-4 mb-3';

            // تحديد أيقونة الملف حسب النوع
            let fileIcon = 'fas fa-file';
            const extension = doc.file_name.split('.').pop().toLowerCase();

            switch(extension) {
                case 'pdf':
                    fileIcon = 'fas fa-file-pdf text-danger';
                    break;
                case 'doc':
                case 'docx':
                    fileIcon = 'fas fa-file-word text-primary';
                    break;
                case 'xls':
                case 'xlsx':
                    fileIcon = 'fas fa-file-excel text-success';
                    break;
                case 'jpg':
                case 'jpeg':
                case 'png':
                case 'gif':
                    fileIcon = 'fas fa-file-image text-warning';
                    break;
                case 'zip':
                case 'rar':
                    fileIcon = 'fas fa-file-archive text-secondary';
                    break;
            }

            // تنسيق تاريخ الرفع
            const uploadDate = new Date(doc.uploaded_at).toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });

            col.innerHTML = `
                <div class="card h-100 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <i class="${fileIcon} fa-2x me-3"></i>
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-1">${doc.document_name}</h6>
                                <small class="text-muted">${doc.file_name}</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                ${uploadDate}
                            </small>
                        </div>

                        <!-- مؤشرات الروابط -->
                        <div class="mb-3">
                            ${doc.nextcloud_share_link ? '<span class="badge bg-success me-1"><i class="fas fa-cloud me-1"></i>Nextcloud</span>' : ''}
                            ${doc.onedrive_share_link ? '<span class="badge bg-primary me-1"><i class="fas fa-cloud me-1"></i>OneDrive</span>' : ''}
                            ${!doc.nextcloud_share_link && !doc.onedrive_share_link ? '<span class="badge bg-secondary">لا توجد روابط</span>' : ''}
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="btn-group w-100" role="group">
                            <a href="/shipments/cargo/documents/${doc.id}/download"
                               class="btn btn-outline-info btn-sm"
                               title="تحميل">
                                <i class="fas fa-download"></i>
                            </a>

                            ${doc.nextcloud_share_link ? `
                                <button class="btn btn-outline-success btn-sm"
                                        onclick="copyShareLink('${doc.nextcloud_share_link}')"
                                        title="نسخ رابط Nextcloud">
                                    <i class="fas fa-copy"></i>
                                </button>
                            ` : ''}

                            ${doc.onedrive_share_link ? `
                                <button class="btn btn-outline-primary btn-sm"
                                        onclick="copyShareLink('${doc.onedrive_share_link}')"
                                        title="نسخ رابط OneDrive">
                                    <i class="fas fa-copy"></i>
                                </button>
                            ` : ''}

                            <button class="btn btn-outline-danger btn-sm"
                                    onclick="deleteDocumentFromModal(${doc.id})"
                                    title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;

            return col;
        }

        // تحديث عرض الوثائق
        function refreshDocumentsView() {
            const documentsGrid = document.getElementById('documentsGrid');
            documentsGrid.innerHTML = `
                <div class="col-12 text-center">
                    <div class="spinner-border text-info" role="status">
                        <span class="visually-hidden">جاري التحديث...</span>
                    </div>
                    <p class="mt-2">جاري تحديث الوثائق...</p>
                </div>
            `;
            loadUploadedDocuments();
        }

        // حذف وثيقة من النافذة المنبثقة
        function deleteDocumentFromModal(documentId) {
            if (confirm('هل أنت متأكد من حذف هذه الوثيقة؟')) {
                fetch(`/shipments/cargo/documents/${documentId}/delete`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('تم حذف الوثيقة بنجاح', 'success', 'fas fa-check-circle');
                        refreshDocumentsView();
                        // تحديث الصفحة الرئيسية أيضاً
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        showToast('فشل في حذف الوثيقة: ' + data.message, 'danger', 'fas fa-exclamation-circle');
                    }
                })
                .catch(error => {
                    console.error('خطأ في حذف الوثيقة:', error);
                    showToast('حدث خطأ في حذف الوثيقة', 'danger', 'fas fa-exclamation-circle');
                });
            }
        }

        // إنشاء بوليصة الشحن
        function generateBillOfLading() {
            alert('سيتم تطوير هذه الميزة قريباً!');
        }

        // إنشاء رابط مشاركة (دعم خدمات متعددة)
        function createShareLink(documentId, service) {
            const serviceName = service === 'nextcloud' ? 'Nextcloud' : 'OneDrive';

            if (confirm(`هل تريد إنشاء رابط مشاركة ${serviceName} لهذه الوثيقة؟`)) {
                // إظهار مؤشر التحميل
                showToast(`جاري إنشاء رابط ${serviceName}...`, 'info', 'fas fa-spinner fa-spin');

                fetch(`/shipments/cargo/documents/${documentId}/create-link`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        service: service
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.is_existing) {
                            showToast(`رابط ${serviceName} موجود مسبقاً`, 'warning', 'fas fa-info-circle');
                        } else {
                            showToast(`تم إنشاء رابط ${serviceName} بنجاح!`, 'success', 'fas fa-check-circle');
                        }

                        // نسخ الرابط للحافظة
                        navigator.clipboard.writeText(data.share_link).then(() => {
                            showToast('تم نسخ الرابط تلقائياً للحافظة', 'info', 'fas fa-copy');
                        });

                        // إعادة تحميل الصفحة لتحديث الأزرار
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        showToast(`خطأ في إنشاء رابط ${serviceName}: ` + data.message, 'danger', 'fas fa-exclamation-circle');
                    }
                })
                .catch(error => {
                    console.error('خطأ:', error);
                    showToast(`حدث خطأ في إنشاء رابط ${serviceName}`, 'danger', 'fas fa-exclamation-triangle');
                });
            }
        }



        // نسخ رابط المشاركة
        function copyShareLink(shareLink) {
            navigator.clipboard.writeText(shareLink).then(() => {
                showToast('تم نسخ رابط المشاركة بنجاح!', 'success', 'fas fa-check-circle');
            }).catch(err => {
                showToast('فشل في نسخ الرابط: ' + err, 'danger', 'fas fa-exclamation-circle');
            });
        }

        // فتح رابط المشاركة في نافذة جديدة
        function openShareLink(shareLink) {
            if (shareLink) {
                window.open(shareLink, '_blank', 'noopener,noreferrer');
                showToast('تم فتح رابط المشاركة في نافذة جديدة', 'info', 'fas fa-external-link-alt');
            } else {
                showToast('رابط المشاركة غير متوفر', 'warning', 'fas fa-exclamation-triangle');
            }
        }



        // دالة عامة لإظهار الرسائل
        function showToast(message, type = 'success', icon = 'fas fa-info-circle') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';
            toast.innerHTML = `
                <i class="${icon} me-2"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(toast);

            // إزالة الرسالة بعد 4 ثوان
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => toast.remove(), 300);
                }
            }, 4000);
        }
    </script>
</body>
</html>
