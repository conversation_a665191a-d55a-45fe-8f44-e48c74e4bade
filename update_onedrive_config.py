#!/usr/bin/env python3
"""
تحديث إعدادات OneDrive يدوياً
Manual OneDrive Configuration Update
"""

import json
import os

def update_onedrive_config(client_id, client_secret):
    """تحديث إعدادات OneDrive"""
    
    config_path = os.path.join('app', 'shipments', 'cloud_config.json')
    
    try:
        # قراءة الإعدادات الحالية
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # تحديث إعدادات OneDrive
        config['onedrive'] = {
            "enabled": True,
            "client_id": client_id,
            "client_secret": client_secret,
            "tenant_id": "common",
            "redirect_uri": "http://localhost:5000/auth/onedrive/callback",
            "scopes": ["Files.ReadWrite", "Files.ReadWrite.All"],
            "demo_mode": False,
            "note": "إعدادات حقيقية - تم التحديث يدوياً"
        }
        
        # تحديث الخدمة الافتراضية
        config['settings']['default_service'] = 'onedrive'
        
        # حفظ الإعدادات
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        
        print("✅ تم تحديث إعدادات OneDrive بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث الإعدادات: {e}")
        return False

if __name__ == "__main__":
    print("🔧 تحديث إعدادات OneDrive يدوياً")
    print("=" * 50)
    
    # مثال على الاستخدام
    print("📝 مثال على الاستخدام:")
    print("client_id = 'your-client-id-here'")
    print("client_secret = 'your-client-secret-here'")
    print("update_onedrive_config(client_id, client_secret)")
    
    print("\n💡 بعد الحصول على البيانات من Azure Portal:")
    print("1. عدل هذا الملف")
    print("2. ضع Client ID و Client Secret الحقيقيين")
    print("3. شغل الملف")
    
    # يمكنك تعديل هذه القيم بعد الحصول عليها من Azure
    # client_id = "ضع-client-id-هنا"
    # client_secret = "ضع-client-secret-هنا"
    # update_onedrive_config(client_id, client_secret)
