# النظام المحاسبي المتقدم - ملف .gitignore
# Advanced Accounting System - .gitignore file

# النظام المحاسبي - ملف .gitignore
# Accounting System - .gitignore file

# ملفات Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# ملفات البيئة الافتراضية
venv/
env/
ENV/
env.bak/
venv.bak/

# ملفات قواعد البيانات
*.db
*.sqlite
*.sqlite3
instance/

# ملفات التكوين الحساسة
.env
.env.local
.env.production
config_local.py
*_local.py

# ملفات Oracle
*.jar
tnsnames.ora
sqlnet.ora
listener.ora

# ملفات النسخ الاحتياطية
*_backup_*.py
*_backup.py
backup_*/
*.bak

# ملفات الاختبار المؤقتة
test_*.py
check_*.py
debug_*.py
temp_*.py

# ملفات التشغيل المؤقتة
*.bat
*.sh
run_*.py
start_*.py
quick_*.py
fix_*.py
setup_*.py
install_*.py

# ملفات التوثيق المؤقتة
*_DOCUMENTATION.md
*_STATUS.md
*_GUIDE.md
*_README.md
CHANGELOG.md
CONTRIBUTING.md

# ملفات الوسائط المؤقتة
*.png
*.jpg
*.jpeg
*.gif
*.bmp
*.ico
*.svg

# ملفات Microsoft Office
*.docx
*.xlsx
*.pptx
*.doc
*.xls
*.ppt

# ملفات النظام
.DS_Store
Thumbs.db
desktop.ini

# ملفات IDE
.vscode/
.idea/
*.swp
*.swo
*~

# ملفات السجلات
*.log
logs/
log/

# ملفات SSL
ssl/
*.crt
*.key
*.pem

# ملفات مؤقتة
temp/
tmp/
*.tmp
*.temp

# ملفات التحليل
analysis/
reports/
*.csv

# ملفات خاصة بالمشروع
create_*.py
migrate_*.py
switch_*.py
clean_*.py
complete_*.py

# استثناءات - ملفات مهمة يجب الاحتفاظ بها
!app/
!requirements.txt
!README.md
!LICENSE
!config.py
!database_manager.py
!oracle_config.py
!oracle_jdbc.py
!start_oracle.py
!run.py

# قواعد البيانات - Databases
*.db
*.sqlite
*.sqlite3
accounting_system.db
data.sqlite
data-dev.sqlite

# ملفات التطوير - Development files
.vscode/
.idea/
*.swp
*.swo
*~

# ملفات النظام - System files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ملفات الرفع - Upload files
uploads/
app/static/uploads/

# ملفات التقارير - Report files
reports/
app/static/reports/

# ملفات النسخ الاحتياطي - Backup files
backups/
*.backup
*.bak

# ملفات السجلات - Log files
logs/
*.log
*.log.*

# ملفات مؤقتة - Temporary files
tmp/
temp/
*.tmp
*.temp

# ملفات الإعدادات المحلية - Local configuration files
config_local.py
local_settings.py

# ملفات الأسرار - Secret files
secrets.py
.secrets
*.key
*.pem
*.p12

# ملفات الهجرة المحلية - Local migration files
migrations/versions/*.py
!migrations/versions/__init__.py

# ملفات التخزين المؤقت - Cache files
.cache/
*.cache

# ملفات الجلسات - Session files
flask_session/

# ملفات الاختبار - Test files
.coverage
htmlcov/
.pytest_cache/
test_results/

# ملفات التوثيق المولدة - Generated documentation
docs/_build/
docs/build/

# ملفات Node.js (إذا كانت مستخدمة) - Node.js files (if used)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ملفات الإنتاج - Production files
*.pid
*.sock

# ملفات SSL - SSL files
*.crt
*.csr
*.key
ssl/

# ملفات Docker - Docker files
.dockerignore
docker-compose.override.yml

# ملفات Kubernetes - Kubernetes files
k8s/secrets/

# ملفات التكوين الخاصة بالبيئة - Environment-specific config files
.env.local
.env.development
.env.test
.env.production

# ملفات قواعد البيانات المحلية - Local database files
*.db-journal
*.db-wal
*.db-shm

# ملفات الإحصائيات - Statistics files
stats/
analytics/

# ملفات التصدير - Export files
exports/
*.xlsx
*.csv
*.pdf
!static/sample_files/

# ملفات الذكاء الاصطناعي - AI/ML files
models/
*.pkl
*.joblib
*.h5

# ملفات الشهادات - Certificate files
certificates/
*.p12
*.pfx
