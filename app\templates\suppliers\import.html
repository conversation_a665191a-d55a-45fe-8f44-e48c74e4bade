{% extends "base.html" %}

{% block title %}استيراد الموردين{% endblock %}

{% block content %}
<div class="ns-main-real">
    <!-- Page Header -->
    <div class="ns-page-header-real">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">
                    <i class="fas fa-download me-2" style="color: #fd7e14;"></i>
                    استيراد الموردين
                </h1>
                <p class="text-muted mb-0">استيراد وتحديث بيانات الموردين من المصدر الخارجي</p>
            </div>
            <div>
                <a href="{{ url_for('suppliers.index') }}" class="ns-btn-real ns-btn-secondary-real">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Import Form -->
    <div class="ns-card-real">
        <div class="ns-card-header">
            <i class="fas fa-database me-2"></i>
            استيراد من المصدر الخارجي
        </div>
        <div class="ns-card-body">
            <div class="row">
                <div class="col-md-8">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle me-2"></i>معلومات المصدر</h5>
                        <ul class="mb-0">
                            <li><strong>المستخدم:</strong> ias20251</li>
                            <li><strong>قاعدة البيانات:</strong> IAS20251.YEMENSOFT.COM</li>
                            <li><strong>الجدول:</strong> V_DETAILS</li>
                            <li><strong>الحقول المستوردة:</strong> V_CODE, V_A_NAME, V_E_NAME, V_ADDRESS, V_PHONE, V_E_MAIL</li>
                            <li><strong>العملية:</strong> استيراد الموردين الجدد وتحديث الموجودين</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه</h6>
                        <p class="mb-0">
                            سيتم استيراد جميع الموردين من المصدر الخارجي. الموردين الموجودين سيتم تحديث بياناتهم.
                            هذه العملية قد تستغرق بعض الوقت حسب كمية البيانات.
                        </p>
                    </div>

                    <form id="importForm">
                        <div class="d-grid gap-2">
                            <button type="submit" class="ns-btn-real ns-btn-primary-real" id="importBtn">
                                <i class="fas fa-download me-2"></i>
                                بدء عملية الاستيراد
                            </button>
                        </div>
                    </form>
                </div>

                <div class="col-md-4">
                    <div class="card border-primary">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>إحصائيات الاستيراد</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-success mb-1" id="importedCount">0</h4>
                                        <small class="text-muted">مستورد</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-warning mb-1" id="skippedCount">0</h4>
                                    <small class="text-muted">متجاوز</small>
                                </div>
                            </div>
                            <hr>
                            <div class="text-center">
                                <h5 class="text-danger mb-1" id="errorsCount">0</h5>
                                <small class="text-muted">أخطاء</small>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <div class="progress" style="display: none;" id="progressBar">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results -->
    <div class="ns-card-real" id="resultsCard" style="display: none;">
        <div class="ns-card-header">
            <i class="fas fa-list-check me-2"></i>
            نتائج الاستيراد
        </div>
        <div class="ns-card-body">
            <div id="resultsContent"></div>
        </div>
    </div>

    <!-- Errors -->
    <div class="ns-card-real" id="errorsCard" style="display: none;">
        <div class="ns-card-header">
            <i class="fas fa-exclamation-triangle me-2"></i>
            الأخطاء
        </div>
        <div class="ns-card-body">
            <div id="errorsContent"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const importForm = document.getElementById('importForm');
    const importBtn = document.getElementById('importBtn');
    const progressBar = document.getElementById('progressBar');
    const resultsCard = document.getElementById('resultsCard');
    const errorsCard = document.getElementById('errorsCard');

    importForm.addEventListener('submit', function(e) {
        e.preventDefault();
        startImport();
    });

    function startImport() {
        // تعطيل الزر وإظهار التحميل
        importBtn.disabled = true;
        importBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الاستيراد...';
        progressBar.style.display = 'block';
        progressBar.querySelector('.progress-bar').style.width = '50%';

        // إخفاء النتائج السابقة
        resultsCard.style.display = 'none';
        errorsCard.style.display = 'none';

        // إرسال طلب الاستيراد
        fetch('{{ url_for("suppliers.import_suppliers") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            progressBar.querySelector('.progress-bar').style.width = '100%';
            
            if (data.success) {
                // تحديث الإحصائيات
                document.getElementById('importedCount').textContent = data.imported || 0;
                document.getElementById('skippedCount').textContent = data.skipped || 0;
                document.getElementById('errorsCount').textContent = data.errors ? data.errors.length : 0;

                // إظهار النتائج
                showResults(data);
                
                // إظهار الأخطاء إن وجدت
                if (data.errors && data.errors.length > 0) {
                    showErrors(data.errors);
                }

                // رسالة نجاح
                showAlert(data.message, 'success');
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('خطأ في الاستيراد:', error);
            showAlert('حدث خطأ أثناء عملية الاستيراد', 'error');
        })
        .finally(() => {
            // إعادة تفعيل الزر
            importBtn.disabled = false;
            importBtn.innerHTML = '<i class="fas fa-download me-2"></i>بدء عملية الاستيراد';
            
            setTimeout(() => {
                progressBar.style.display = 'none';
                progressBar.querySelector('.progress-bar').style.width = '0%';
            }, 2000);
        });
    }

    function showResults(data) {
        const content = `
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>تمت العملية بنجاح</h5>
                <ul class="mb-0">
                    <li>تم استيراد <strong>${data.imported || 0}</strong> مورد جديد</li>
                    <li>تم تجاوز <strong>${data.skipped || 0}</strong> مورد موجود</li>
                    <li>إجمالي الموردين المعالجين: <strong>${data.total_processed || 0}</strong></li>
                </ul>
            </div>
        `;
        document.getElementById('resultsContent').innerHTML = content;
        resultsCard.style.display = 'block';
    }

    function showErrors(errors) {
        const errorsList = errors.map(error => `<li>${error}</li>`).join('');
        const content = `
            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>الأخطاء التي حدثت:</h6>
                <ul>${errorsList}</ul>
            </div>
        `;
        document.getElementById('errorsContent').innerHTML = content;
        errorsCard.style.display = 'block';
    }

    function showAlert(message, type) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const icon = type === 'success' ? 'check-circle' : 'exclamation-circle';
        
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert ${alertClass} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            <i class="fas fa-${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.querySelector('.ns-main-real').insertBefore(alertDiv, document.querySelector('.ns-page-header-real').nextSibling);
        
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
});
</script>
{% endblock %}
