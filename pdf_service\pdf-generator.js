const express = require('express');
const puppeteer = require('puppeteer');
const app = express();
const port = 3001;

app.use(express.json());

// تحويل HTML إلى PDF
app.post('/generate-pdf', async (req, res) => {
    console.log(`📥 طلب PDF جديد - ${new Date().toISOString()}`);

    try {
        const { html, filename } = req.body;

        if (!html) {
            console.log('❌ لا يوجد محتوى HTML');
            return res.status(400).json({ error: 'HTML content is required' });
        }

        console.log(`📄 HTML length: ${html.length} characters`);
        console.log(`📁 Filename: ${filename || 'document.pdf'}`);

        // إطلاق المتصفح
        const browser = await puppeteer.launch({
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        const page = await browser.newPage();
        
        // تعيين viewport لضمان الحجم الصحيح
        await page.setViewport({
            width: 794,  // A4 width in pixels at 96 DPI
            height: 1123, // A4 height in pixels at 96 DPI
            deviceScaleFactor: 1
        });

        // تعيين المحتوى مع انتظار تحميل الصور
        await page.setContent(html, {
            waitUntil: 'networkidle0',
            timeout: 30000
        });

        // انتظار تحميل جميع الصور
        await page.evaluate(() => {
            return Promise.all(
                Array.from(document.images, img => {
                    if (img.complete) return Promise.resolve();
                    return new Promise((resolve, reject) => {
                        img.addEventListener('load', resolve);
                        img.addEventListener('error', reject);
                    });
                })
            );
        });

        // إنشاء PDF بمقاس A4 دقيق
        const pdf = await page.pdf({
            format: 'A4',
            width: '210mm',
            height: '297mm',
            margin: {
                top: '15mm',
                right: '15mm',
                bottom: '15mm',
                left: '15mm'
            },
            printBackground: true,
            preferCSSPageSize: false,
            displayHeaderFooter: false,
            scale: 1.0
        });

        await browser.close();

        console.log(`✅ تم إنشاء PDF بحجم ${pdf.length} bytes`);

        // إرسال PDF
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="${filename || 'document.pdf'}"`);
        res.send(pdf);

    } catch (error) {
        console.error('❌ خطأ في إنشاء PDF:', error);
        res.status(500).json({ error: 'Failed to generate PDF', details: error.message });
    }
});

// صحة الخدمة
app.get('/health', (req, res) => {
    res.json({ status: 'OK', service: 'PDF Generator', timestamp: new Date().toISOString() });
});

app.listen(port, () => {
    console.log(`PDF Generator service running on port ${port}`);
});
