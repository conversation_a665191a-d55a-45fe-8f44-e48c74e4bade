#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة وثائق طلبات الحوالات
Transfer Request Document Management System
"""

import os
import uuid
from datetime import datetime
from werkzeug.utils import secure_filename
from flask import current_app
from database_manager import DatabaseManager
import logging

logger = logging.getLogger(__name__)

class TransferDocumentManager:
    """مدير وثائق طلبات الحوالات"""
    
    # أنواع الوثائق المدعومة
    DOCUMENT_TYPES = {
        'identity_document': 'وثيقة الهوية',
        'passport': 'جواز السفر',
        'bank_statement': 'كشف حساب بنكي',
        'salary_certificate': 'شهادة راتب',
        'employment_letter': 'خطاب عمل',
        'commercial_license': 'رخصة تجارية',
        'tax_certificate': 'شهادة ضريبية',
        'beneficiary_id': 'هوية المستفيد',
        'beneficiary_bank_info': 'معلومات بنك المستفيد',
        'transfer_receipt': 'إيصال التحويل',
        'exchange_rate_proof': 'إثبات سعر الصرف',
        'compliance_documents': 'وثائق الامتثال',
        'aml_documents': 'وثائق مكافحة غسل الأموال',
        'source_of_funds': 'مصدر الأموال',
        'purpose_proof': 'إثبات الغرض',
        'invoice': 'فاتورة',
        'contract': 'عقد',
        'other': 'أخرى'
    }
    
    # أنواع الملفات المسموحة
    ALLOWED_EXTENSIONS = {
        'pdf', 'doc', 'docx', 'xls', 'xlsx',
        'jpg', 'jpeg', 'png', 'gif', 'tiff',
        'txt', 'csv', 'zip', 'rar'
    }
    
    # الحد الأقصى لحجم الملف (10 MB)
    MAX_FILE_SIZE = 10 * 1024 * 1024
    
    def __init__(self):
        """تهيئة مدير الوثائق"""
        # استخدام مسار ثابت بدلاً من current_app
        base_path = os.path.dirname(os.path.dirname(__file__))
        self.upload_folder = os.path.join(base_path, 'static', 'uploads', 'transfer_documents')
        self._ensure_upload_folder()
    
    def _ensure_upload_folder(self):
        """التأكد من وجود مجلد الرفع"""
        if not os.path.exists(self.upload_folder):
            os.makedirs(self.upload_folder, exist_ok=True)
            logger.info(f"تم إنشاء مجلد الوثائق: {self.upload_folder}")
    
    def is_allowed_file(self, filename):
        """التحقق من نوع الملف المسموح"""
        return ('.' in filename and 
                filename.rsplit('.', 1)[1].lower() in self.ALLOWED_EXTENSIONS)
    
    def upload_document(self, transfer_request_id, document_type, file,
                       document_name=None, notes=None, uploaded_by=None):
        """رفع وثيقة جديدة"""
        try:
            # التحقق من الملف
            if not file or file.filename == '':
                raise ValueError("لم يتم اختيار ملف")
            
            if not self.is_allowed_file(file.filename):
                raise ValueError("نوع الملف غير مدعوم")
            
            # التحقق من حجم الملف
            file.seek(0, os.SEEK_END)
            file_size = file.tell()
            file.seek(0)
            
            if file_size > self.MAX_FILE_SIZE:
                raise ValueError("حجم الملف كبير جداً (الحد الأقصى 10 MB)")
            
            # إنشاء اسم ملف فريد
            original_filename = secure_filename(file.filename)
            file_extension = original_filename.rsplit('.', 1)[1].lower()
            unique_filename = f"{uuid.uuid4().hex}.{file_extension}"
            
            # مسار الحفظ
            file_path = os.path.join(self.upload_folder, unique_filename)
            
            # حفظ الملف
            file.save(file_path)
            logger.info(f"تم حفظ الملف: {file_path}")
            
            # حفظ معلومات الوثيقة في قاعدة البيانات
            db_manager = DatabaseManager()
            
            insert_sql = """
                INSERT INTO transfer_request_documents (
                    transfer_request_id, document_type, document_name,
                    file_name, file_path, file_size, mime_type,
                    uploaded_by, notes
                ) VALUES (
                    :1, :2, :3, :4, :5, :6, :7, :8, :9
                )
            """
            
            # تحديد نوع MIME
            mime_type = 'application/octet-stream'
            if file_extension == 'pdf':
                mime_type = 'application/pdf'
            elif file_extension in ['jpg', 'jpeg']:
                mime_type = 'image/jpeg'
            elif file_extension == 'png':
                mime_type = 'image/png'
            elif file_extension in ['doc', 'docx']:
                mime_type = 'application/msword'
            elif file_extension in ['xls', 'xlsx']:
                mime_type = 'application/vnd.ms-excel'
            
            # تحديد اسم الوثيقة
            if not document_name:
                document_name = self.DOCUMENT_TYPES.get(document_type, 'وثيقة')
            
            params = [
                transfer_request_id,
                document_type,
                document_name,
                original_filename,
                file_path,
                file_size,
                mime_type,
                uploaded_by or 'system',
                notes
            ]
            
            result = db_manager.execute_update(insert_sql, params)
            db_manager.close()

            if result > 0:
                logger.info(f"تم حفظ معلومات الوثيقة في قاعدة البيانات")
                return {
                    'success': True,
                    'message': 'تم رفع الوثيقة بنجاح',
                    'file_path': file_path,
                    'file_size': file_size
                }
            else:
                # حذف الملف إذا فشل حفظ البيانات
                if os.path.exists(file_path):
                    os.remove(file_path)
                raise Exception("فشل في حفظ معلومات الوثيقة")
                
        except Exception as e:
            logger.error(f"خطأ في رفع الوثيقة: {e}")
            return {
                'success': False,
                'message': str(e)
            }
    
    def get_request_documents(self, transfer_request_id):
        """جلب جميع وثائق طلب الحوالة"""
        try:
            db_manager = DatabaseManager()
            
            query = """
                SELECT id, document_type, document_name, file_name,
                       file_size, mime_type, uploaded_by, uploaded_at,
                       notes, share_link, share_service, share_created_at,
                       nextcloud_share_link, nextcloud_service_info, nextcloud_created_at,
                       onedrive_share_link, onedrive_service_info, onedrive_created_at
                FROM transfer_request_documents
                WHERE transfer_request_id = :1
                ORDER BY uploaded_at DESC
            """

            results = db_manager.execute_query(query, [transfer_request_id])
            db_manager.close()
            
            if results:
                documents = []
                for row in results:
                    # تحويل حجم الملف إلى MB
                    file_size_mb = round(row[4] / (1024 * 1024), 2) if row[4] else 0

                    # تحديد نوع الوثيقة
                    document_type_name = self.DOCUMENT_TYPES.get(row[1], row[1])

                    # تحويل التاريخ إلى نص
                    uploaded_at = row[7]
                    if uploaded_at:
                        try:
                            if hasattr(uploaded_at, 'strftime'):
                                uploaded_at = uploaded_at.strftime('%Y-%m-%d %H:%M:%S')
                            else:
                                uploaded_at = str(uploaded_at)
                        except:
                            uploaded_at = str(uploaded_at)
                    else:
                        uploaded_at = ''

                    # تحويل CLOB إلى نص
                    notes = row[8]
                    if notes is not None:
                        # إذا كان CLOB، قم بقراءته
                        if hasattr(notes, 'read'):
                            notes = notes.read()
                        notes = str(notes) if notes else ''
                    else:
                        notes = ''

                    # معالجة تاريخ إنشاء رابط المشاركة القديم
                    share_created_at = row[11]
                    if share_created_at:
                        try:
                            if hasattr(share_created_at, 'strftime'):
                                share_created_at = share_created_at.strftime('%Y-%m-%d %H:%M:%S')
                            else:
                                share_created_at = str(share_created_at)
                        except:
                            share_created_at = str(share_created_at)
                    else:
                        share_created_at = ''

                    # معالجة تاريخ إنشاء رابط Nextcloud
                    nextcloud_created_at = row[14]
                    if nextcloud_created_at:
                        try:
                            if hasattr(nextcloud_created_at, 'strftime'):
                                nextcloud_created_at = nextcloud_created_at.strftime('%Y-%m-%d %H:%M:%S')
                            else:
                                nextcloud_created_at = str(nextcloud_created_at)
                        except:
                            nextcloud_created_at = str(nextcloud_created_at)
                    else:
                        nextcloud_created_at = ''

                    # معالجة تاريخ إنشاء رابط OneDrive
                    onedrive_created_at = row[17]
                    if onedrive_created_at:
                        try:
                            if hasattr(onedrive_created_at, 'strftime'):
                                onedrive_created_at = onedrive_created_at.strftime('%Y-%m-%d %H:%M:%S')
                            else:
                                onedrive_created_at = str(onedrive_created_at)
                        except:
                            onedrive_created_at = str(onedrive_created_at)
                    else:
                        onedrive_created_at = ''

                    documents.append({
                        'id': row[0],
                        'document_type': row[1],
                        'document_type_name': document_type_name,
                        'document_name': row[2],
                        'file_name': row[3],
                        'file_size': row[4],
                        'file_size_mb': file_size_mb,
                        'mime_type': row[5],
                        'uploaded_by': row[6] or 'system',
                        'uploaded_at': uploaded_at,
                        'notes': notes,
                        # الروابط القديمة (للتوافق مع النسخة السابقة)
                        'share_link': row[9],
                        'share_service': row[10],
                        'share_created_at': share_created_at,
                        # روابط Nextcloud
                        'nextcloud_share_link': row[12],
                        'nextcloud_service_info': row[13],
                        'nextcloud_created_at': nextcloud_created_at,
                        # روابط OneDrive
                        'onedrive_share_link': row[15],
                        'onedrive_service_info': row[16],
                        'onedrive_created_at': onedrive_created_at,
                        # مؤشرات وجود الروابط
                        'has_nextcloud_link': bool(row[12]),
                        'has_onedrive_link': bool(row[15]),
                        'has_any_link': bool(row[12] or row[15])
                    })
                
                return documents
            
            return []
            
        except Exception as e:
            logger.error(f"خطأ في جلب وثائق الطلب: {e}")
            return []
    
    def delete_document(self, document_id, user=None):
        """حذف وثيقة"""
        try:
            db_manager = DatabaseManager()
            
            # جلب معلومات الوثيقة
            query = "SELECT file_path FROM transfer_request_documents WHERE id = :1"
            result = db_manager.execute_query(query, [document_id])

            if not result:
                return {'success': False, 'message': 'الوثيقة غير موجودة'}

            file_path = result[0][0]

            # حذف من قاعدة البيانات
            delete_sql = "DELETE FROM transfer_request_documents WHERE id = :1"
            db_manager.execute_update(delete_sql, [document_id])
            
            # حذف الملف الفعلي
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"تم حذف الملف: {file_path}")
            
            db_manager.close()
            
            return {'success': True, 'message': 'تم حذف الوثيقة بنجاح'}
            
        except Exception as e:
            logger.error(f"خطأ في حذف الوثيقة: {e}")
            return {'success': False, 'message': str(e)}
    
    def get_document_stats(self, transfer_request_id):
        """إحصائيات وثائق طلب الحوالة"""
        try:
            db_manager = DatabaseManager()
            
            query = """
                SELECT
                    COUNT(*) as total_docs,
                    SUM(file_size) as total_size
                FROM transfer_request_documents
                WHERE transfer_request_id = :1
            """

            result = db_manager.execute_query(query, [transfer_request_id])
            db_manager.close()
            
            if result:
                row = result[0]
                total_size_bytes = row[1] or 0
                total_size_mb = round(total_size_bytes / (1024 * 1024), 3) if total_size_bytes > 0 else 0

                return {
                    'total_documents': row[0] or 0,
                    'total_size_mb': total_size_mb
                }
            
            return {
                'total_documents': 0,
                'total_size_mb': 0
            }
            
        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات الوثائق: {e}")
            return {
                'total_documents': 0,
                'total_size_mb': 0
            }

# إنشاء مثيل مشترك
document_manager = TransferDocumentManager()
