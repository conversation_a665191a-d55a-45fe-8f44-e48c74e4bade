{% extends "base.html" %}

{% block title %}بيانات العقود{% endblock %}

{% block extra_css %}
<style>
    .contracts-container {
        background: #f8f9fa;
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .page-header h1 {
        font-size: 2.5rem;
        font-weight: bold;
        margin: 0;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }
    
    .control-panel {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: 1px solid #e9ecef;
    }
    
    .btn-add-contract {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }
    
    .btn-add-contract:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        color: white;
    }

    .loading-spinner {
        display: none;
        text-align: center;
        padding: 50px;
    }
    
    .spinner-border {
        width: 3rem;
        height: 3rem;
        color: #667eea;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #dee2e6;
    }
    
    .search-box {
        border-radius: 25px;
        border: 2px solid #e9ecef;
        padding: 10px 20px;
        transition: all 0.3s ease;
    }
    
    .search-box:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    /* تحسين المرشحات المتقدمة */
    .advanced-filters {
        margin-bottom: 20px;
        animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .stats-info .badge {
        font-size: 0.85em;
        padding: 6px 10px;
    }

    .view-options .btn-group .btn {
        border-radius: 4px;
    }

    .view-options .btn-group .btn.active {
        background-color: #667eea;
        border-color: #667eea;
        color: white;
    }

    /* تحسين الجدول */
    .table-responsive {
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        font-weight: 600;
        color: #495057;
        padding: 12px;
    }

    .table td {
        padding: 12px;
        vertical-align: middle;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
    }

    /* تحسين البطاقات */
    .contracts-cards {
        display: none;
    }

    .contract-card {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
        background: white;
    }

    .contract-card:hover {
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .contract-card-header {
        padding: 15px;
        border-bottom: 1px solid #dee2e6;
        background-color: #f8f9fa;
    }

    .contract-card-body {
        padding: 15px;
    }

    .contract-card-footer {
        padding: 10px 15px;
        border-top: 1px solid #dee2e6;
        background-color: #f8f9fa;
    }

    /* تحسين مظهر حالات العقود */
    .badge {
        font-size: 0.85em;
        padding: 0.5em 0.75em;
        border-radius: 0.375rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .badge i {
        font-size: 0.9em;
    }

    /* ألوان مخصصة للحالات */
    .badge.bg-success {
        background-color: #198754 !important;
        color: white;
    }

    .badge.bg-warning {
        background-color: #ffc107 !important;
        color: #212529;
    }

    .badge.bg-info {
        background-color: #0dcaf0 !important;
        color: #212529;
    }

    .badge.bg-primary {
        background-color: #0d6efd !important;
        color: white;
    }

    .badge.bg-danger {
        background-color: #dc3545 !important;
        color: white;
    }

    .badge.bg-dark {
        background-color: #212529 !important;
        color: white;
    }

    .badge.bg-secondary {
        background-color: #6c757d !important;
        color: white;
    }

    /* تأثير hover للحالات */
    .badge:hover {
        opacity: 0.9;
        transform: scale(1.05);
        transition: all 0.2s ease;
        cursor: default;
    }

    /* تحسين مظهر الجدول */
    .table td {
        vertical-align: middle;
    }

    /* ألوان إضافية للحالات الجديدة */
    .badge.bg-info.text-dark {
        background-color: #0dcaf0 !important;
        color: #212529 !important;
    }

    /* تحسين عرض النصوص الطويلة في الحالات */
    .badge {
        white-space: nowrap;
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* تحسين الحالات المختلطة (عربي/إنجليزي) */
    .badge[title] {
        cursor: help;
    }

    /* تنسيق مسار التنقل */
    .breadcrumb {
        background: none;
        padding: 0;
        margin: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: #6c757d;
        font-weight: bold;
    }

    .breadcrumb-item a {
        color: #495057;
        transition: color 0.3s ease;
    }

    .breadcrumb-item a:hover {
        color: #007bff;
    }

    .breadcrumb-item.active {
        color: #6c757d;
        font-weight: 500;
    }
</style>
{% endblock %}

{% block content %}
<div class="contracts-container">
    <div class="container-fluid">
        <!-- Header المتقدم -->
        <div class="row mb-4">
            <div class="col-12">
                <!-- Header الرئيسي -->
                <div class="card border-0 shadow-sm mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="card-body text-white">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="me-3">
                                        <i class="fas fa-file-contract fa-3x opacity-75"></i>
                                    </div>
                                    <div>
                                        <h1 class="h2 mb-1 fw-bold">إدارة العقود</h1>
                                        <p class="mb-0 opacity-90">إدارة شاملة لجميع العقود والاتفاقيات مع المورديين والشركاء</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="d-flex justify-content-end align-items-center">
                                    <!-- إحصائيات سريعة -->
                                    <div class="me-3">
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <div class="h5 mb-0 fw-bold">{{ contracts|length }}</div>
                                                <small class="opacity-75">إجمالي العقود</small>
                                            </div>
                                            <div class="col-6">
                                                <div class="h5 mb-0 fw-bold">
                                                    {% set active_count = contracts|selectattr('contract_status', 'equalto', 'ACTIVE')|list|length %}
                                                    {{ active_count }}
                                                </div>
                                                <small class="opacity-75">نشط</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="vr opacity-50 me-3"></div>
                                    <div>
                                        <a href="{{ url_for('contracts.new') }}" class="btn btn-light btn-sm">
                                            <i class="fas fa-plus me-1"></i>إضافة عقد جديد
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- شريط التنقل السريع -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body py-2">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item">
                                    <a href="{{ url_for('main.dashboard') }}" class="text-decoration-none">
                                        <i class="fas fa-home me-1"></i>
                                        الرئيسية
                                    </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <a href="{{ url_for('main.dashboard') }}" class="text-decoration-none">
                                        <i class="fas fa-cogs me-1"></i>
                                        إدارة النظام
                                    </a>
                                </li>
                                <li class="breadcrumb-item active" aria-current="page">
                                    <i class="fas fa-file-contract me-1"></i>
                                    إدارة العقود
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <!-- بطاقات الإحصائيات السريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="rounded-circle bg-success bg-opacity-10 p-3 me-2">
                                <i class="fas fa-check-circle text-success fa-lg"></i>
                            </div>
                            <div>
                                <h4 class="mb-0 fw-bold text-success">
                                    {% set active_count = contracts|selectattr('contract_status', 'equalto', 'ACTIVE')|list|length %}
                                    {{ active_count }}
                                </h4>
                                <small class="text-muted">عقود نشطة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="rounded-circle bg-warning bg-opacity-10 p-3 me-2">
                                <i class="fas fa-edit text-warning fa-lg"></i>
                            </div>
                            <div>
                                <h4 class="mb-0 fw-bold text-warning">
                                    {% set draft_count = contracts|selectattr('contract_status', 'equalto', 'DRAFT')|list|length %}
                                    {{ draft_count }}
                                </h4>
                                <small class="text-muted">مسودات</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="rounded-circle bg-primary bg-opacity-10 p-3 me-2">
                                <i class="fas fa-thumbs-up text-primary fa-lg"></i>
                            </div>
                            <div>
                                <h4 class="mb-0 fw-bold text-primary">
                                    {% set approved_count = contracts|selectattr('contract_status', 'equalto', 'APPROVED')|list|length %}
                                    {{ approved_count }}
                                </h4>
                                <small class="text-muted">معتمدة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="rounded-circle bg-info bg-opacity-10 p-3 me-2">
                                <i class="fas fa-calculator text-info fa-lg"></i>
                            </div>
                            <div>
                                <h4 class="mb-0 fw-bold text-info">
                                    {% set total_amount = contracts|sum(attribute='contract_amount') %}
                                    {{ "{:,.0f}".format(total_amount) }}
                                </h4>
                                <small class="text-muted">إجمالي القيمة (عملات مختلطة)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- لوحة التحكم والتصفية المحسنة -->
        <div class="control-panel">
            <!-- البحث السريع -->
            <div class="row mb-3">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" id="searchInput" class="search-box form-control border-0"
                               placeholder="البحث برقم العقد، اسم المورد، أو المبلغ..."
                               onkeyup="performQuickSearch()">
                        <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()" title="مسح البحث">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-outline-primary me-2" onclick="loadContracts()">
                        <i class="fas fa-sync-alt me-2"></i>تحديث
                    </button>
                    <button class="btn btn-outline-info" id="filterToggleBtn" onclick="
                        var filters = document.getElementById('advancedFilters');
                        var btn = this;
                        if (filters.style.display === 'block') {
                            filters.style.display = 'none';
                            btn.innerHTML = '<i class=&quot;fas fa-filter me-2&quot;></i>إظهار التصفية المتقدمة';
                            btn.className = 'btn btn-outline-info';
                        } else {
                            filters.style.display = 'block';
                            btn.innerHTML = '<i class=&quot;fas fa-times me-2&quot;></i>إخفاء التصفية المتقدمة';
                            btn.className = 'btn btn-warning';
                        }
                    ">
                        <i class="fas fa-filter me-2"></i>إظهار التصفية المتقدمة
                    </button>
                </div>
            </div>

            <!-- المرشحات المتقدمة (مخفية افتراضياً) -->
            <div id="advancedFilters" class="advanced-filters" style="display: none;">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-sliders-h me-2"></i>التصفية المتقدمة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- تصفية حسب الحالة -->
                            <div class="col-md-3 mb-3">
                                <label class="form-label">حالة العقد</label>
                                <select class="form-select" id="statusFilter" onchange="
                                    var selectedValue = this.value;
                                    var rows = document.querySelectorAll('.table tbody tr');
                                    var count = 0;
                                    rows.forEach(function(row) {
                                        var statusCell = row.querySelector('td:nth-child(7)');
                                        if (selectedValue === '') {
                                            row.style.display = '';
                                            count++;
                                        } else if (statusCell) {
                                            var statusText = statusCell.textContent.trim();
                                            if (statusText.includes(selectedValue)) {
                                                row.style.display = '';
                                                count++;
                                            } else {
                                                row.style.display = 'none';
                                            }
                                        }
                                    });
                                    console.log('عرض ' + count + ' من ' + rows.length + ' صف');
                                ">
                                    <option value="">جميع الحالات</option>
                                    <option value="مسودة">مسودة</option>
                                    <option value="معتمد">معتمد</option>
                                    <option value="منفذ جزئياً">منفذ جزئياً</option>
                                    <option value="منفذ كلياً">منفذ كلياً</option>
                                    <option value="نشط">نشط</option>
                                    <option value="معلق">معلق</option>
                                    <option value="منتهي الصلاحية">منتهي الصلاحية</option>
                                    <option value="ملغي">ملغي</option>
                                    <option value="مرفوض">مرفوض</option>
                                    <option value="قيد المراجعة">قيد المراجعة</option>
                                </select>
                            </div>

                            <!-- تصفية حسب حالة الاستخدام -->
                            <div class="col-md-3 mb-3">
                                <label class="form-label">حالة الاستخدام</label>
                                <select class="form-select" id="usageFilter" onchange="
                                    var selectedValue = this.value;
                                    var rows = document.querySelectorAll('.table tbody tr');
                                    var count = 0;
                                    rows.forEach(function(row) {
                                        var usageCell = row.querySelector('td:nth-child(8)'); // العمود الثامن هو حالة الاستخدام
                                        if (selectedValue === '') {
                                            row.style.display = '';
                                            count++;
                                        } else if (usageCell) {
                                            var usageText = usageCell.textContent.trim();
                                            var showRow = false;
                                            if (selectedValue === '1' && usageText.includes('مستخدم')) {
                                                showRow = true;
                                            } else if (selectedValue === '0' && usageText.includes('غير مستخدم')) {
                                                showRow = true;
                                            }

                                            if (showRow) {
                                                row.style.display = '';
                                                count++;
                                            } else {
                                                row.style.display = 'none';
                                            }
                                        }
                                    });
                                    console.log('تصفية الاستخدام: عرض ' + count + ' من ' + rows.length + ' صف');
                                ">
                                    <option value="">جميع الحالات</option>
                                    <option value="0">غير مستخدم</option>
                                    <option value="1">مستخدم</option>
                                </select>
                            </div>

                            <!-- تصفية حسب التاريخ -->
                            <div class="col-md-3 mb-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="dateFromFilter" onchange="applyFilters()">
                            </div>

                            <div class="col-md-3 mb-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="dateToFilter" onchange="applyFilters()">
                            </div>
                        </div>

                        <div class="row">
                            <!-- تصفية حسب المبلغ -->
                            <div class="col-md-3 mb-3">
                                <label class="form-label">المبلغ من</label>
                                <input type="number" class="form-control" id="amountFromFilter"
                                       placeholder="0.00" step="0.01" onchange="applyFilters()">
                            </div>

                            <div class="col-md-3 mb-3">
                                <label class="form-label">المبلغ إلى</label>
                                <input type="number" class="form-control" id="amountToFilter"
                                       placeholder="0.00" step="0.01" onchange="applyFilters()">
                            </div>

                            <!-- تصفية حسب العملة -->
                            <div class="col-md-3 mb-3">
                                <label class="form-label">العملة</label>
                                <select class="form-select" id="currencyFilter" onchange="applyFilters()">
                                    <option value="">جميع العملات</option>
                                    <option value="SAR">ريال سعودي</option>
                                    <option value="USD">دولار أمريكي</option>
                                    <option value="EUR">يورو</option>
                                </select>
                            </div>

                            <!-- أزرار التحكم -->
                            <div class="col-md-3 mb-3 d-flex align-items-end">
                                <button class="btn btn-outline-warning me-2" onclick="
                                    document.getElementById('statusFilter').value = '';
                                    document.getElementById('usageFilter').value = '';
                                    var rows = document.querySelectorAll('.table tbody tr');
                                    rows.forEach(function(row) { row.style.display = ''; });
                                    console.log('تم إعادة تعيين جميع المرشحات');
                                ">
                                    <i class="fas fa-undo me-1"></i>إعادة تعيين
                                </button>
                                <button class="btn btn-outline-success me-2" onclick="exportFilteredData()">
                                    <i class="fas fa-download me-1"></i>تصدير
                                </button>
                                <button class="btn btn-outline-info me-2" onclick="debugTableStructure()" title="فحص بنية الجدول">
                                    <i class="fas fa-bug"></i>
                                </button>
                                <button class="btn btn-outline-primary" onclick="testFiltering()" title="اختبار التصفية">
                                    <i class="fas fa-vial"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="stats-info">
                            <span class="badge bg-primary me-2">
                                <i class="fas fa-list me-1"></i>
                                إجمالي: <span id="totalCount">{{ contracts|length if contracts else 0 }}</span>
                            </span>
                            <span class="badge bg-success me-2">
                                <i class="fas fa-check me-1"></i>
                                نشط: <span id="activeCount">0</span>
                            </span>
                            <span class="badge bg-warning me-2">
                                <i class="fas fa-clock me-1"></i>
                                مسودة: <span id="draftCount">0</span>
                            </span>
                            <span class="badge bg-info">
                                <i class="fas fa-eye me-1"></i>
                                معروض: <span id="visibleCount">{{ contracts|length if contracts else 0 }}</span>
                            </span>
                        </div>
                        <div class="view-options">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeView('table')" id="tableViewBtn">
                                    <i class="fas fa-table"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeView('cards')" id="cardsViewBtn">
                                    <i class="fas fa-th-large"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- محتوى العقود -->
        <div class="control-panel">
            {% if contracts %}
                <!-- عرض الجدول -->
                <div class="table-responsive" id="tableView">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم العقد</th>
                                <th>تاريخ العقد</th>
                                <th>المورد</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ النهاية</th>
                                <th>مبلغ العقد</th>
                                <th>حالة العقد</th>
                                <th>حالة الاستخدام</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for contract in contracts %}
                            <tr>
                                <td><strong>{{ contract.contract_number }}</strong></td>
                                <td>{{ contract.contract_date }}</td>
                                <td>{{ contract.supplier_name }}</td>
                                <td>{{ contract.start_date }}</td>
                                <td>{{ contract.end_date }}</td>
                                <td>{{ "{:,.2f}".format(contract.contract_amount) }} {{ contract.currency_symbol }}</td>
                                <td>
                                    {% if contract.contract_status == 'ACTIVE' %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>نشط
                                        </span>
                                    {% elif contract.contract_status == 'DRAFT' %}
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-edit me-1"></i>مسودة
                                        </span>
                                    {% elif contract.contract_status == 'PENDING' %}
                                        <span class="badge bg-info">
                                            <i class="fas fa-clock me-1"></i>في الانتظار
                                        </span>
                                    {% elif contract.contract_status == 'APPROVED' %}
                                        <span class="badge bg-primary">
                                            <i class="fas fa-thumbs-up me-1"></i>معتمد
                                        </span>
                                    {% elif contract.contract_status == 'REJECTED' %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times-circle me-1"></i>مرفوض
                                        </span>
                                    {% elif contract.contract_status == 'EXPIRED' %}
                                        <span class="badge bg-dark">
                                            <i class="fas fa-calendar-times me-1"></i>منتهي الصلاحية
                                        </span>
                                    {% elif contract.contract_status == 'CANCELLED' %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-ban me-1"></i>ملغي
                                        </span>
                                    {% elif contract.contract_status == 'COMPLETED' %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-double me-1"></i>مكتمل
                                        </span>
                                    {% elif contract.contract_status == 'SUSPENDED' %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-pause-circle me-1"></i>معلق
                                        </span>
                                    {% elif contract.contract_status == 'TERMINATED' %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-stop-circle me-1"></i>منهي
                                        </span>
                                    {% elif contract.contract_status == 'RENEWED' %}
                                        <span class="badge bg-info">
                                            <i class="fas fa-redo me-1"></i>مجدد
                                        </span>
                                    {% elif contract.contract_status == 'UNDER_REVIEW' %}
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-search me-1"></i>قيد المراجعة
                                        </span>
                                    {% elif contract.contract_status == 'PARTIALLY_EXECUTED' or contract.contract_status == 'منفذ جزئيا' %}
                                        <span class="badge bg-info">
                                            <i class="fas fa-tasks me-1"></i>منفذ جزئياً
                                        </span>
                                    {% elif contract.contract_status == 'FULLY_EXECUTED' or contract.contract_status == 'منفذ كليا' %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-double me-1"></i>منفذ كلياً
                                        </span>
                                    {% elif contract.contract_status == 'IN_PROGRESS' %}
                                        <span class="badge bg-primary">
                                            <i class="fas fa-spinner me-1"></i>قيد التنفيذ
                                        </span>
                                    {% elif contract.contract_status == 'ON_HOLD' %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-hand-paper me-1"></i>معلق مؤقتاً
                                        </span>
                                    {% elif contract.contract_status == 'AWAITING_SIGNATURE' %}
                                        <span class="badge bg-info text-dark">
                                            <i class="fas fa-signature me-1"></i>في انتظار التوقيع
                                        </span>
                                    {% elif contract.contract_status == 'SIGNED' %}
                                        <span class="badge bg-primary">
                                            <i class="fas fa-file-signature me-1"></i>موقع
                                        </span>
                                    {% elif contract.contract_status == 'VOID' %}
                                        <span class="badge bg-dark">
                                            <i class="fas fa-times me-1"></i>باطل
                                        </span>
                                    {% elif contract.contract_status == 'AMENDMENT_PENDING' %}
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-edit me-1"></i>تعديل معلق
                                        </span>
                                    {% elif contract.contract_status == 'RENEGOTIATION' %}
                                        <span class="badge bg-info">
                                            <i class="fas fa-handshake me-1"></i>إعادة تفاوض
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-question-circle me-1"></i>
                                            {% if contract.contract_status %}
                                                {% if contract.contract_status == 'منفذ جزئيا' %}
                                                    منفذ جزئياً
                                                {% elif contract.contract_status == 'منفذ كليا' %}
                                                    منفذ كلياً
                                                {% else %}
                                                    {{ contract.contract_status }}
                                                {% endif %}
                                            {% else %}
                                                غير محدد
                                            {% endif %}
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if contract.is_used == 1 %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>مستخدم
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-circle me-1"></i>غير مستخدم
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('contracts.view_contract_details', contract_id=contract.contract_id) }}"
                                           class="btn btn-outline-primary btn-sm" title="عرض تفاصيل العقد">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('contracts.edit_contract', contract_id=contract.contract_id) }}"
                                           class="btn btn-outline-secondary btn-sm" title="تعديل العقد">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-outline-info btn-sm"
                                                title="إدارة وثائق العقد"
                                                onclick="window.location.href='/contracts/{{ contract.contract_id }}/documents'">
                                            <i class="fas fa-folder-open"></i>
                                        </button>
                                        <button type="button"
                                                class="btn btn-outline-danger btn-sm"
                                                title="حذف العقد"
                                                onclick="confirmDelete({{ contract.contract_id }}, '{{ contract.contract_number }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- عرض البطاقات -->
                <div class="contracts-cards" id="cardsView" style="display: none;">
                    <div class="row" id="cardsContainer">
                        {% for contract in contracts %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="contract-card">
                                <div class="contract-card-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">{{ contract.contract_number }}</h6>
                                        {% if contract.contract_status == 'ACTIVE' %}
                                            <span class="badge bg-success">نشط</span>
                                        {% elif contract.contract_status == 'DRAFT' %}
                                            <span class="badge bg-warning text-dark">مسودة</span>
                                        {% elif contract.contract_status == 'EXPIRED' %}
                                            <span class="badge bg-danger">منتهي</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ contract.contract_status }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="contract-card-body">
                                    <div class="mb-2">
                                        <strong>المورد:</strong> {{ contract.supplier_name }}
                                    </div>
                                    <div class="mb-2">
                                        <strong>تاريخ العقد:</strong> {{ contract.contract_date }}
                                    </div>
                                    <div class="mb-2">
                                        <strong>المبلغ:</strong> {{ "{:,.2f}".format(contract.contract_amount) }} {{ contract.currency_symbol }}
                                    </div>
                                    <div class="mb-2">
                                        <strong>الفترة:</strong> {{ contract.start_date }} - {{ contract.end_date }}
                                    </div>
                                    <div class="mb-2">
                                        <strong>الاستخدام:</strong>
                                        {% if contract.is_used == 1 %}
                                            <span class="badge bg-success">مستخدم</span>
                                        {% else %}
                                            <span class="badge bg-secondary">غير مستخدم</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="contract-card-footer">
                                    <div class="btn-group w-100" role="group">
                                        <a href="{{ url_for('contracts.view_contract_details', contract_id=contract.contract_id) }}"
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i> عرض
                                        </a>
                                        <a href="{{ url_for('contracts.edit_contract', contract_id=contract.contract_id) }}"
                                           class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                        <button class="btn btn-outline-info btn-sm"
                                                onclick="window.location.href='/contracts/{{ contract.contract_id }}/documents'">
                                            <i class="fas fa-folder-open"></i> وثائق
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-file-contract"></i>
                    <h4>لا توجد عقود</h4>
                    <p>لم يتم العثور على أي عقود في قاعدة البيانات.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تطبيق التصفية - حل نهائي
function applyFilters() {
    console.log('🔧 تطبيق المرشحات...');

    const statusFilter = document.getElementById('statusFilter')?.value;
    console.log(`📋 تصفية الحالة: "${statusFilter}"`);

    // الحصول على جميع صفوف الجدول
    const tableRows = document.querySelectorAll('.table tbody tr');
    let visibleCount = 0;

    tableRows.forEach((row, index) => {
        let showRow = true;

        // تصفية حسب الحالة
        if (statusFilter && statusFilter.trim() !== '') {
            const statusCell = row.querySelector('td:nth-child(7)'); // العمود السابع هو حالة العقد
            if (statusCell) {
                // استخراج النص من داخل span أو أي عنصر آخر
                const statusSpan = statusCell.querySelector('span');
                let statusText = '';

                if (statusSpan) {
                    // إزالة الأيقونات والحصول على النص فقط
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = statusSpan.innerHTML;
                    // إزالة عناصر i (الأيقونات)
                    const icons = tempDiv.querySelectorAll('i');
                    icons.forEach(icon => icon.remove());
                    statusText = tempDiv.textContent.trim();
                } else {
                    statusText = statusCell.textContent.trim();
                }

                const filterText = statusFilter.trim();

                // مقارنة دقيقة
                if (statusText !== filterText) {
                    showRow = false;
                }

                console.log(`الصف ${index + 1}: "${statusText}" vs "${filterText}" - ${showRow ? 'مرئي' : 'مخفي'}`);
            }
        }

        // إظهار أو إخفاء الصف
        if (showRow) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    console.log(`✅ تم عرض ${visibleCount} من ${tableRows.length} صف`);

    // إظهار رسالة للمستخدم
    if (statusFilter && visibleCount === 0) {
        console.warn('⚠️ لا توجد عقود تطابق المرشح المحدد');
    }
}

// إعادة تعيين المرشحات
function resetFilters() {
    console.log('🔄 إعادة تعيين المرشحات...');

    // مسح قيم المرشحات
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) statusFilter.value = '';

    // إظهار جميع الصفوف
    const tableRows = document.querySelectorAll('.table tbody tr');
    tableRows.forEach(row => {
        row.style.display = '';
    });

    console.log(`✅ تم إظهار جميع الصفوف (${tableRows.length})`);
}



document.addEventListener('DOMContentLoaded', function() {
    console.log('صفحة العقود جاهزة');

    // تهيئة DataTable إذا كان موجوداً
    if (typeof $.fn.DataTable !== 'undefined' && $('#contractsTable').length) {
        $('#contractsTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            }
        });
    }

    // تهيئة البحث والتصفية
    initializeFilters();
    updateStatistics();

    // الحالات موجودة مباشرة في HTML

    // تفعيل وضع الجدول افتراضياً
    changeView('table');

    // التأكد من أن المرشحات مخفية في البداية
    const filtersDiv = document.getElementById('advancedFilters');
    if (filtersDiv) {
        filtersDiv.style.display = 'none';
        console.log('✅ تم إخفاء المرشحات المتقدمة افتراضياً');
    }
});

// متغيرات عامة
let allContracts = [];
let filteredContracts = [];
let currentView = 'table';

// تهيئة المرشحات
function initializeFilters() {
    // جمع جميع العقود من الجدول
    collectAllContracts();

    // إضافة معالجات الأحداث
    document.getElementById('searchInput').addEventListener('input', performQuickSearch);

    console.log('✅ تم تهيئة نظام البحث والتصفية');
}

// جمع جميع العقود من الجدول
function collectAllContracts() {
    allContracts = [];
    const rows = document.querySelectorAll('.table tbody tr');

    rows.forEach((row, index) => {
        const cells = row.querySelectorAll('td');
        if (cells.length === 0) return;

        // استخراج البيانات من الجدول
        const contractNumber = cells[0]?.textContent?.trim() || '';
        const contractDate = cells[1]?.textContent?.trim() || '';
        const supplierName = cells[2]?.textContent?.trim() || '';
        const startDate = cells[3]?.textContent?.trim() || '';
        const endDate = cells[4]?.textContent?.trim() || '';
        const amount = cells[5]?.textContent?.trim() || '';

        // استخراج حالة العقد (النص والـ HTML)
        const statusCell = cells[6];
        const statusText = statusCell?.textContent?.trim() || '';
        const statusHTML = statusCell?.innerHTML?.trim() || '';

        const usage = cells[7]?.textContent?.trim() || '';

        // جمع جميع النصوص للبحث
        const searchText = [
            contractNumber, contractDate, supplierName,
            startDate, endDate, amount, statusText, usage
        ].join(' ').toLowerCase();

        const contract = {
            element: row,
            index: index,
            contractNumber: contractNumber,
            contractDate: contractDate,
            supplierName: supplierName,
            startDate: startDate,
            endDate: endDate,
            amount: amount,
            status: statusText,
            statusHTML: statusHTML,
            usage: usage,
            searchText: searchText
        };

        allContracts.push(contract);
    });

    filteredContracts = [...allContracts];
    console.log(`📊 تم جمع ${allContracts.length} عقد`);
}

// تحميل حالات العقود من قاعدة البيانات
function loadContractStatusesForFilter() {
    console.log('🔄 تحميل حالات العقود من قاعدة البيانات...');

    fetch('/contracts/api/contract-statuses')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.statuses) {
                console.log(`✅ تم جلب ${data.statuses.length} حالة من قاعدة البيانات`);
                populateStatusFilterFromAPI(data.statuses);
            } else {
                console.warn('⚠️ فشل في جلب حالات العقود من API');
                addDefaultStatusesToFilter();
            }
        })
        .catch(error => {
            console.error('❌ خطأ في جلب حالات العقود:', error);
            addDefaultStatusesToFilter();
        });
}

// ملء قائمة التصفية بالحالات من API
function populateStatusFilterFromAPI(statuses) {
    const statusFilter = document.getElementById('statusFilter');
    if (!statusFilter) {
        console.error('❌ لم يتم العثور على قائمة تصفية الحالات');
        return;
    }

    // مسح الخيارات الموجودة
    statusFilter.innerHTML = '<option value="">جميع الحالات</option>';

    // إضافة الحالات من قاعدة البيانات
    statuses.forEach(status => {
        const option = document.createElement('option');
        option.value = status.status_name_ar;
        option.textContent = status.status_name_ar;
        option.setAttribute('data-code', status.status_code);
        statusFilter.appendChild(option);
    });

    console.log(`✅ تم تحديث قائمة التصفية بـ ${statuses.length} حالة من قاعدة البيانات`);
}

// إضافة حالات افتراضية للتصفية
function addDefaultStatusesToFilter() {
    const statusFilter = document.getElementById('statusFilter');
    if (!statusFilter) return;

    const defaultStatuses = ['مسودة', 'معتمد', 'منفذ جزئياً', 'منفذ كلياً', 'نشط', 'منتهي الصلاحية', 'ملغي'];

    statusFilter.innerHTML = '<option value="">جميع الحالات</option>';

    defaultStatuses.forEach(status => {
        const option = document.createElement('option');
        option.value = status;
        option.textContent = status;
        statusFilter.appendChild(option);
    });

    console.log(`📋 تم إضافة ${defaultStatuses.length} حالة افتراضية للتصفية`);
}

// ملء قائمة حالات العقد من الجدول مباشرة
function populateStatusFilterFromTable() {
    console.log('🔄 ملء قائمة حالات العقد من الجدول...');

    const statusFilter = document.getElementById('statusFilter');
    if (!statusFilter) {
        console.error('❌ لم يتم العثور على قائمة تصفية الحالات');
        return;
    }

    // التحقق من وجود الجدول
    const table = document.querySelector('.table');
    if (!table) {
        console.error('❌ لم يتم العثور على الجدول');
        addDefaultStatuses(statusFilter);
        return;
    }

    // التحقق من وجود tbody
    const tbody = table.querySelector('tbody');
    if (!tbody) {
        console.error('❌ لم يتم العثور على tbody في الجدول');
        addDefaultStatuses(statusFilter);
        return;
    }

    // التحقق من وجود صفوف
    const rows = tbody.querySelectorAll('tr');
    console.log(`📊 عدد الصفوف في الجدول: ${rows.length}`);

    if (rows.length === 0) {
        console.warn('⚠️ لا توجد صفوف في الجدول');
        addDefaultStatuses(statusFilter);
        return;
    }

    // فحص الصف الأول لمعرفة عدد الأعمدة
    const firstRow = rows[0];
    const firstRowCells = firstRow.querySelectorAll('td');
    console.log(`📊 عدد الأعمدة في الصف الأول: ${firstRowCells.length}`);

    // جمع جميع الحالات من الجدول
    const statusCells = tbody.querySelectorAll('tr td:nth-child(7)'); // العمود السابع هو حالة العقد
    console.log(`📊 عدد خلايا الحالة الموجودة: ${statusCells.length}`);

    const uniqueStatuses = new Set();

    statusCells.forEach((cell, index) => {
        console.log(`🔍 فحص الخلية ${index + 1}:`, cell.innerHTML);

        // استخراج النص من HTML (إزالة العلامات)
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = cell.innerHTML;
        const statusText = (tempDiv.textContent || tempDiv.innerText || '').trim();

        console.log(`📝 النص المستخرج: "${statusText}"`);

        if (statusText && statusText !== '') {
            uniqueStatuses.add(statusText);
            console.log(`✅ تم إضافة الحالة: "${statusText}"`);
        }
    });

    console.log(`📋 إجمالي الحالات المختلفة: ${uniqueStatuses.size}`);
    console.log(`📋 الحالات:`, Array.from(uniqueStatuses));

    // مسح الخيارات الموجودة وإضافة الجديدة
    statusFilter.innerHTML = '<option value="">جميع الحالات</option>';

    // إضافة الحالات المستخرجة
    if (uniqueStatuses.size > 0) {
        Array.from(uniqueStatuses).sort().forEach(status => {
            const option = document.createElement('option');
            option.value = status;
            option.textContent = status;
            statusFilter.appendChild(option);
            console.log(`➕ تم إضافة خيار: "${status}"`);
        });
        console.log(`✅ تم تحديث قائمة حالات العقد بـ ${uniqueStatuses.size} حالة`);
    } else {
        console.warn('⚠️ لم توجد حالات، إضافة الحالات الافتراضية');
        addDefaultStatuses(statusFilter);
    }
}

// إضافة الحالات الافتراضية
function addDefaultStatuses(statusFilter) {
    const defaultStatuses = ['مسودة', 'معتمد', 'نشط', 'منتهي الصلاحية', 'ملغي'];
    defaultStatuses.forEach(status => {
        const option = document.createElement('option');
        option.value = status;
        option.textContent = status;
        statusFilter.appendChild(option);
    });
    console.log(`📋 تم إضافة ${defaultStatuses.length} حالة افتراضية`);
}

// دالة تشخيص بنية الجدول
function debugTableStructure() {
    console.log('🔍 === فحص بنية الجدول ===');

    const table = document.querySelector('.table');
    console.log('📊 الجدول:', table);

    if (!table) {
        alert('❌ لم يتم العثور على الجدول!');
        return;
    }

    const thead = table.querySelector('thead');
    const tbody = table.querySelector('tbody');

    console.log('📋 رأس الجدول:', thead);
    console.log('📋 جسم الجدول:', tbody);

    if (thead) {
        const headerCells = thead.querySelectorAll('th');
        console.log(`📊 عدد أعمدة الرأس: ${headerCells.length}`);
        headerCells.forEach((cell, index) => {
            console.log(`📋 العمود ${index + 1}: "${cell.textContent.trim()}"`);
        });
    }

    if (tbody) {
        const rows = tbody.querySelectorAll('tr');
        console.log(`📊 عدد الصفوف: ${rows.length}`);

        if (rows.length > 0) {
            const firstRow = rows[0];
            const cells = firstRow.querySelectorAll('td');
            console.log(`📊 عدد خلايا الصف الأول: ${cells.length}`);

            cells.forEach((cell, index) => {
                const text = cell.textContent.trim();
                console.log(`📋 الخلية ${index + 1}: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`);

                if (index === 6) { // العمود السابع (حالة العقد)
                    console.log(`🎯 خلية حالة العقد (العمود 7):`, cell.innerHTML);
                }
            });
        }
    }

    // محاولة تحديث قائمة الحالات
    console.log('🔄 محاولة تحديث قائمة الحالات...');
    populateStatusFilterFromTable();

    alert('تم فحص بنية الجدول. راجع console للتفاصيل.');
}

// اختبار التصفية
function testFiltering() {
    console.log('🧪 === اختبار التصفية ===');

    // فحص البيانات
    console.log(`📊 عدد العقود الكلي: ${allContracts ? allContracts.length : 'غير محدد'}`);
    console.log(`📊 عدد العقود المفلترة: ${filteredContracts ? filteredContracts.length : 'غير محدد'}`);

    // فحص قائمة التصفية
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        console.log(`📋 قيمة التصفية المختارة: "${statusFilter.value}"`);
        console.log(`📋 عدد خيارات التصفية: ${statusFilter.options.length}`);
    }

    // جمع البيانات إذا لم تكن موجودة
    if (!allContracts || allContracts.length === 0) {
        console.log('🔄 جمع بيانات العقود...');
        collectAllContracts();
        console.log(`📊 تم جمع ${allContracts ? allContracts.length : 0} عقد`);
    }

    // اختبار التصفية
    console.log('🔄 تطبيق التصفية...');
    applyFilters();

    alert('تم اختبار التصفية. راجع console للتفاصيل.');
}





// تحديث خيارات التصفية بناءً على البيانات من API
function populateFilterOptions() {
    // جلب حالات العقود من API
    loadContractStatuses();
}

    // جمع جميع العملات الموجودة (إذا كانت موجودة في البيانات)
    const uniqueCurrencies = [...new Set(allContracts.map(c => {
        const amount = c.amount;
        if (amount.includes('ريال')) return 'SAR';
        if (amount.includes('دولار')) return 'USD';
        if (amount.includes('يورو')) return 'EUR';
        return 'SAR'; // افتراضي
    }))];

    console.log(`💰 العملات الموجودة: ${uniqueCurrencies.join(', ')}`);
}

// جلب حالات العقود من API
function loadContractStatuses() {
    console.log('🔄 جلب حالات العقود من API...');

    fetch('/contracts/api/contract-statuses')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.statuses) {
                console.log(`✅ تم جلب ${data.statuses.length} حالة من API`);
                populateStatusFilter(data.statuses);
            } else {
                console.warn('⚠️ فشل في جلب حالات العقود من API، استخدام الحالات الافتراضية');
                populateStatusFilterFromData();
            }
        })
        .catch(error => {
            console.error('❌ خطأ في جلب حالات العقود:', error);
            populateStatusFilterFromData();
        });
}

// ملء قائمة التصفية بالحالات من API
function populateStatusFilter(statuses) {
    const statusFilter = document.getElementById('statusFilter');
    if (!statusFilter) return;

    // الاحتفاظ بالخيار الأول (جميع الحالات)
    const firstOption = statusFilter.options[0];
    statusFilter.innerHTML = '';
    statusFilter.appendChild(firstOption);

    // إضافة الحالات من API
    statuses.forEach(status => {
        const option = document.createElement('option');
        option.value = status.status_name_ar;
        option.textContent = status.status_name_ar;
        option.setAttribute('data-color', status.status_color);
        option.setAttribute('data-icon', status.status_icon);
        option.setAttribute('data-code', status.status_code);
        statusFilter.appendChild(option);
    });

    console.log(`📋 تم تحديث قائمة التصفية بـ ${statuses.length} حالة من API`);
}

// ملء قائمة التصفية من البيانات الموجودة (حل احتياطي)
function populateStatusFilterFromData() {
    console.log('🔄 استخدام الحالات من البيانات الموجودة...');

    // جمع جميع الحالات الموجودة (استخراج النص من HTML)
    const uniqueStatuses = [...new Set(allContracts.map(c => {
        if (c.statusHTML) {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = c.statusHTML;
            return (tempDiv.textContent || tempDiv.innerText || '').trim();
        }
        return c.status.trim();
    }))].filter(s => s !== '');

    // تحديث قائمة حالات العقد
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter && uniqueStatuses.length > 0) {
        // الاحتفاظ بالخيار الأول (جميع الحالات)
        const firstOption = statusFilter.options[0];
        statusFilter.innerHTML = '';
        statusFilter.appendChild(firstOption);

        // إضافة الحالات الموجودة فعلياً
        uniqueStatuses.forEach(status => {
            const option = document.createElement('option');
            option.value = status;
            option.textContent = status;
            statusFilter.appendChild(option);
        });

        console.log(`📋 تم تحديث قائمة الحالات من البيانات المحلية: ${uniqueStatuses.join(', ')}`);
    }
}

// دالة تأكيد الحذف
function confirmDelete(contractId, contractNumber) {
    if (confirm(`هل أنت متأكد من حذف العقد رقم ${contractNumber}؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه!`)) {
        deleteContract(contractId);
    }
}

// دالة تحديث قائمة العقود
function loadContracts() {
    // إعادة تحميل الصفحة
    location.reload();
}

// البحث السريع
function performQuickSearch() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
    console.log(`🔍 البحث عن: "${searchTerm}"`);

    if (searchTerm === '') {
        filteredContracts = [...allContracts];
        console.log(`📊 مسح البحث: عرض جميع العقود (${allContracts.length})`);
    } else {
        filteredContracts = allContracts.filter(contract => {
            return contract.searchText.includes(searchTerm);
        });
        console.log(`📊 نتائج البحث: ${filteredContracts.length} من ${allContracts.length} عقد`);
    }

    applyFilters();
}

// مسح البحث
function clearSearch() {
    document.getElementById('searchInput').value = '';
    performQuickSearch();
}





// إعادة تعيين المرشحات
function resetFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('usageFilter').value = '';
    document.getElementById('dateFromFilter').value = '';
    document.getElementById('dateToFilter').value = '';
    document.getElementById('amountFromFilter').value = '';
    document.getElementById('amountToFilter').value = '';

    filteredContracts = [...allContracts];
    updateDisplay(filteredContracts);
}

// تحديث العرض
function updateDisplay(contracts = filteredContracts) {
    if (currentView === 'table') {
        // إخفاء جميع الصفوف
        allContracts.forEach(contract => {
            contract.element.style.display = 'none';
        });

        // إظهار الصفوف المفلترة
        contracts.forEach(contract => {
            contract.element.style.display = '';
        });
    } else if (currentView === 'cards') {
        // تحديث البطاقات
        filteredContracts = contracts;
        createContractCards();
    }

    // تحديث الإحصائيات
    updateStatistics(contracts);

    console.log(`📊 عرض ${contracts.length} من ${allContracts.length} عقد في وضع ${currentView}`);
}

// تحديث الإحصائيات
function updateStatistics(contracts = allContracts) {
    const total = allContracts.length;
    const visible = contracts.length;

    // عد الحالات بناءً على النص العربي
    const active = allContracts.filter(c => {
        const status = c.status.toLowerCase();
        return status.includes('نشط') || status.includes('active');
    }).length;

    const draft = allContracts.filter(c => {
        const status = c.status.toLowerCase();
        return status.includes('مسودة') || status.includes('draft');
    }).length;

    // تحديث العرض
    document.getElementById('totalCount').textContent = total;
    document.getElementById('visibleCount').textContent = visible;
    document.getElementById('activeCount').textContent = active;
    document.getElementById('draftCount').textContent = draft;

    console.log(`📊 الإحصائيات: إجمالي=${total}, معروض=${visible}, نشط=${active}, مسودة=${draft}`);
}

// تغيير وضع العرض
function changeView(view) {
    currentView = view;

    const tableView = document.querySelector('.table-responsive');
    const cardsView = document.querySelector('.contracts-cards');
    const tableBtn = document.getElementById('tableViewBtn');
    const cardsBtn = document.getElementById('cardsViewBtn');

    if (view === 'table') {
        if (tableView) tableView.style.display = 'block';
        if (cardsView) cardsView.style.display = 'none';
        tableBtn?.classList.add('active');
        cardsBtn?.classList.remove('active');
    } else {
        if (tableView) tableView.style.display = 'none';
        if (cardsView) cardsView.style.display = 'block';
        tableBtn?.classList.remove('active');
        cardsBtn?.classList.add('active');

        // إنشاء البطاقات إذا لم تكن موجودة
        createContractCards();
    }
}

// إنشاء بطاقات العقود
function createContractCards() {
    console.log('🃏 إنشاء بطاقات العقود...');

    const cardsContainer = document.getElementById('cardsContainer');
    if (!cardsContainer) return;

    // مسح البطاقات الموجودة
    cardsContainer.innerHTML = '';

    // إنشاء بطاقة لكل عقد مفلتر
    filteredContracts.forEach(contract => {
        const cardCol = document.createElement('div');
        cardCol.className = 'col-md-6 col-lg-4 mb-3';

        // تحديد لون الحالة
        let statusBadge = '';
        const status = contract.status.toLowerCase();
        if (status.includes('نشط') || status.includes('active')) {
            statusBadge = '<span class="badge bg-success">نشط</span>';
        } else if (status.includes('مسودة') || status.includes('draft')) {
            statusBadge = '<span class="badge bg-warning text-dark">مسودة</span>';
        } else if (status.includes('منتهي') || status.includes('expired')) {
            statusBadge = '<span class="badge bg-danger">منتهي</span>';
        } else {
            statusBadge = `<span class="badge bg-secondary">${contract.status}</span>`;
        }

        // تحديد حالة الاستخدام
        const usageBadge = contract.usage.includes('مستخدم') || contract.usage.includes('used') ?
            '<span class="badge bg-success">مستخدم</span>' :
            '<span class="badge bg-secondary">غير مستخدم</span>';

        cardCol.innerHTML = `
            <div class="contract-card">
                <div class="contract-card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">${contract.contractNumber}</h6>
                        ${statusBadge}
                    </div>
                </div>
                <div class="contract-card-body">
                    <div class="mb-2">
                        <strong>المورد:</strong> ${contract.supplierName}
                    </div>
                    <div class="mb-2">
                        <strong>تاريخ العقد:</strong> ${contract.contractDate}
                    </div>
                    <div class="mb-2">
                        <strong>المبلغ:</strong> ${contract.amount}
                    </div>
                    <div class="mb-2">
                        <strong>الفترة:</strong> ${contract.startDate} - ${contract.endDate}
                    </div>
                    <div class="mb-2">
                        <strong>الاستخدام:</strong> ${usageBadge}
                    </div>
                </div>
                <div class="contract-card-footer">
                    <div class="btn-group w-100" role="group">
                        <button class="btn btn-outline-primary btn-sm" onclick="viewContractDetails('${contract.contractNumber}')">
                            <i class="fas fa-eye"></i> عرض
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="editContract('${contract.contractNumber}')">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="openDocumentsModal('${contract.contractNumber}')">
                            <i class="fas fa-file-alt"></i> وثائق
                        </button>
                    </div>
                </div>
            </div>
        `;

        cardsContainer.appendChild(cardCol);
    });

    console.log(`🃏 تم إنشاء ${filteredContracts.length} بطاقة`);
}

// دوال مساعدة للبطاقات
function viewContractDetails(contractNumber) {
    // البحث عن العقد في الجدول للحصول على contract_id
    const rows = document.querySelectorAll('.table tbody tr');
    for (let row of rows) {
        const firstCell = row.querySelector('td');
        if (firstCell && firstCell.textContent.trim() === contractNumber) {
            const viewLink = row.querySelector('a[href*="view_contract_details"]');
            if (viewLink) {
                window.location.href = viewLink.href;
                return;
            }
        }
    }
    console.warn('لم يتم العثور على رابط عرض العقد');
}

function editContract(contractNumber) {
    // البحث عن العقد في الجدول للحصول على contract_id
    const rows = document.querySelectorAll('.table tbody tr');
    for (let row of rows) {
        const firstCell = row.querySelector('td');
        if (firstCell && firstCell.textContent.trim() === contractNumber) {
            const editLink = row.querySelector('a[href*="edit_contract"]');
            if (editLink) {
                window.location.href = editLink.href;
                return;
            }
        }
    }
    console.warn('لم يتم العثور على رابط تعديل العقد');
}

function openDocumentsModal(contractNumber) {
    // البحث عن العقد في الجدول للحصول على contract_id
    const rows = document.querySelectorAll('.table tbody tr');
    for (let row of rows) {
        const firstCell = row.querySelector('td');
        if (firstCell && firstCell.textContent.trim() === contractNumber) {
            const docsButton = row.querySelector('button[onclick*="openDocumentsModal"]');
            if (docsButton) {
                docsButton.click();
                return;
            }
        }
    }
    console.warn('لم يتم العثور على زر الوثائق');
}

// تصدير البيانات المفلترة
function exportFilteredData() {
    console.log('📤 تصدير البيانات المفلترة...');
    // سيتم تنفيذ هذه الدالة لاحقاً
    alert('سيتم تنفيذ تصدير البيانات قريباً');
}

// دالة حذف العقد
function deleteContract(contractId) {
    // إظهار مؤشر التحميل
    const deleteBtn = event.target.closest('button');
    const originalContent = deleteBtn.innerHTML;
    deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    deleteBtn.disabled = true;

    fetch(`/contracts/api/contracts/${contractId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إظهار رسالة نجاح
            alert('تم حذف العقد بنجاح');
            // إعادة تحميل الصفحة
            location.reload();
        } else {
            // إظهار رسالة خطأ
            alert('خطأ في حذف العقد: ' + (data.message || 'خطأ غير معروف'));
            // إعادة تعيين الزر
            deleteBtn.innerHTML = originalContent;
            deleteBtn.disabled = false;
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ أثناء حذف العقد');
        // إعادة تعيين الزر
        deleteBtn.innerHTML = originalContent;
        deleteBtn.disabled = false;
    });
}

// فتح نافذة المستندات
function openDocumentsModal(contractId, contractNumber) {
    console.log('📁 فتح نافذة المستندات للعقد:', contractId);
    console.log('📄 رقم العقد:', contractNumber);

    // تعيين معرف العقد
    const uploadContractIdField = document.getElementById('upload_contract_id');
    console.log('🔍 حقل معرف العقد للرفع:', uploadContractIdField);

    if (uploadContractIdField) {
        uploadContractIdField.value = contractId;
        console.log('✅ تم تعيين معرف العقد للرفع:', contractId);
    } else {
        console.error('❌ لم يتم العثور على حقل معرف العقد للرفع!');
    }

    // تحديث عنوان النافذة
    document.getElementById('documentsModalLabel').innerHTML =
        `<i class="fas fa-file-alt me-2"></i>إدارة مستندات العقد رقم ${contractNumber}`;

    // تحميل المستندات
    loadContractDocuments(contractId);

    // فتح النافذة
    const modal = new bootstrap.Modal(document.getElementById('documentsModal'));
    modal.show();
}

// تحميل مستندات العقد
function loadContractDocuments(contractId) {
    const content = document.getElementById('documentsContent');

    // عرض مؤشر التحميل
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-info" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2">جاري تحميل المستندات...</p>
        </div>
    `;

    // استدعاء API لجلب المستندات
    fetch(`/contracts/api/documents/${contractId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayDocuments(data.documents);
            } else {
                content.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ${data.message || 'لا توجد مستندات لهذا العقد'}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل المستندات:', error);
            content.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    حدث خطأ أثناء تحميل المستندات
                </div>
            `;
        });
}

// عرض المستندات
function displayDocuments(documents) {
    const content = document.getElementById('documentsContent');

    if (!documents || documents.length === 0) {
        content.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                لا توجد مستندات مرفقة لهذا العقد
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>العنوان</th>
                        <th>النوع</th>
                        <th>تاريخ الرفع</th>
                        <th>الحجم</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
    `;

    documents.forEach(doc => {
        const fileSize = doc.file_size ? formatFileSize(doc.file_size) : '-';
        const uploadDate = new Date(doc.created_at).toLocaleDateString('ar-SA');
        const isLink = doc.type === 'link' || doc.url;

        // تحديد الأيقونة حسب النوع
        const icon = isLink ? 'fas fa-link text-primary' : 'fas fa-file-alt text-info';

        html += `
            <tr>
                <td>
                    <i class="${icon} me-2"></i>
                    ${doc.title}
                    ${isLink ? '<small class="text-muted d-block">رابط خارجي</small>' : ''}
                </td>
                <td>
                    <span class="badge bg-secondary">${getDocumentTypeLabel(doc.type)}</span>
                </td>
                <td>${uploadDate}</td>
                <td>${fileSize}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        ${isLink ?
                            `<button class="btn btn-outline-primary" onclick="openLink('${doc.url}', ${doc.open_new_tab || true})" title="فتح الرابط">
                                <i class="fas fa-external-link-alt"></i>
                            </button>` :
                            `<button class="btn btn-outline-primary" onclick="viewDocument(${doc.id})" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="downloadDocument(${doc.id})" title="تحميل">
                                <i class="fas fa-download"></i>
                            </button>`
                        }
                        <button class="btn btn-outline-danger" onclick="deleteDocument(${doc.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    content.innerHTML = html;
}

// فتح نافذة رفع المستندات
function uploadDocument() {
    const modal = new bootstrap.Modal(document.getElementById('uploadDocumentModal'));
    modal.show();
}

// فتح نافذة إضافة رابط
function addLinkDocument() {
    // تعيين معرف العقد
    const contractId = document.getElementById('upload_contract_id').value;
    document.getElementById('link_contract_id').value = contractId;

    // فتح النافذة
    const modal = new bootstrap.Modal(document.getElementById('addLinkModal'));
    modal.show();
}

// معالج رفع المستند
document.addEventListener('DOMContentLoaded', function() {
    const uploadDocumentForm = document.getElementById('uploadDocumentForm');
    if (uploadDocumentForm) {
    uploadDocumentForm.addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    // تعطيل الزر وإظهار مؤشر التحميل
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الرفع...';

    fetch('/contracts/api/documents/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إغلاق نافذة الرفع
            bootstrap.Modal.getInstance(document.getElementById('uploadDocumentModal')).hide();

            // إعادة تحميل المستندات
            const contractId = document.getElementById('upload_contract_id').value;
            loadContractDocuments(contractId);

            // إعادة تعيين النموذج
            this.reset();

            alert('تم رفع المستند بنجاح');
        } else {
            alert(data.message || 'حدث خطأ أثناء رفع المستند');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ أثناء رفع المستند');
    })
    .finally(() => {
        // إعادة تعيين الزر
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
    });
}
});

// معالج إرسال نموذج الرابط - نسخة مبسطة
$(document).ready(function() {
    $('#addLinkForm').on('submit', function(e) {
        e.preventDefault();

        // جمع البيانات
        const linkData = {
            contract_id: $('#link_contract_id').val(),
            title: $('#link_title').val(),
            url: $('#link_url').val(),
            type: $('#link_type').val(),
            description: $('#link_description').val()
        };

        // التحقق من البيانات
        if (!linkData.contract_id || !linkData.title || !linkData.url) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();

        // تعطيل الزر
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>جاري الإضافة...');


        // إرسال الطلب
        $.ajax({
            url: '/contracts/api/documents/add-link',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(linkData),
            success: function(response) {
                if (response.success) {
                    alert('تم إضافة الرابط بنجاح');
                    $('#addLinkModal').modal('hide');
                    $('#addLinkForm')[0].reset();

                    // إعادة تحميل المستندات
                    const contractId = $('#link_contract_id').val();
                    loadContractDocuments(contractId);
                } else {
                    alert(response.message || 'حدث خطأ أثناء إضافة الرابط');
                }
            },
            error: function(xhr, status, error) {
                console.error('خطأ:', error);
                alert('حدث خطأ أثناء إضافة الرابط');
            },
            complete: function() {
                // إعادة تعيين الزر
                submitBtn.prop('disabled', false).html(originalText);
            }
        });

    });
});

// دوال مساعدة
function formatFileSize(bytes) {
    if (bytes === 0) return '0 بايت';
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getDocumentTypeLabel(type) {
    const types = {
        'attachment': 'مرفق',
        'link': 'رابط'
    };
    return types[type] || 'غير محدد';
}

function openLink(url, openNewTab = true) {
    if (!url) {
        alert('الرابط غير صحيح');
        return;
    }

    // التأكد من أن الرابط يبدأ بـ http أو https
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://' + url;
    }

    if (openNewTab) {
        window.open(url, '_blank');
    } else {
        window.location.href = url;
    }
}

function viewDocument(docId) {
    window.open(`/contracts/api/documents/${docId}/view`, '_blank');
}

function downloadDocument(docId) {
    window.location.href = `/contracts/api/documents/${docId}/download`;
}

function deleteDocument(docId) {
    if (confirm('هل أنت متأكد من حذف هذا المستند؟')) {
        fetch(`/contracts/api/documents/${docId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إعادة تحميل المستندات
                const contractId = document.getElementById('upload_contract_id').value;
                loadContractDocuments(contractId);
                alert('تم حذف المستند بنجاح');
            } else {
                alert(data.message || 'حدث خطأ أثناء حذف المستند');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('حدث خطأ أثناء حذف المستند');
        });
    }
}

// إدارة وثائق العقد - فتح صفحة منفصلة
function manageContractDocuments(contractId, contractNumber) {
    console.log('📁 إدارة وثائق العقد، contract_id:', contractId);
    console.log('📄 رقم العقد:', contractNumber);

    if (!contractId || contractId === 'null' || contractId === 'undefined') {
        alert('❌ معرف العقد غير متوفر');
        return;
    }

    try {
        // فتح صفحة إدارة الوثائق مباشرة في نفس التبويب
        const documentsUrl = `/contracts/${contractId}/documents`;
        console.log('🔗 الرابط:', documentsUrl);

        // فتح في نفس التبويب
        window.location.href = documentsUrl;

    } catch (error) {
        console.error('❌ خطأ في فتح صفحة إدارة الوثائق:', error);
        alert('حدث خطأ في فتح صفحة إدارة الوثائق. يرجى المحاولة مرة أخرى.');
    }
}
</script>

<!-- نافذة إدارة المستندات -->
<div class="modal fade" id="documentsModal" tabindex="-1" aria-labelledby="documentsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="documentsModalLabel">
                    <i class="fas fa-file-alt me-2"></i>إدارة مستندات العقد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div id="documentsContent">
                    <!-- محتوى المستندات سيتم تحميله هنا -->
                    <div class="text-center">
                        <div class="spinner-border text-info" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2">جاري تحميل المستندات...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" onclick="uploadDocument()">
                    <i class="fas fa-upload me-2"></i>رفع مستند جديد
                </button>
                <button type="button" class="btn btn-info" onclick="addLinkDocument()">
                    <i class="fas fa-link me-2"></i>إضافة رابط
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>إغلاق
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة رفع المستندات -->
<div class="modal fade" id="uploadDocumentModal" tabindex="-1" aria-labelledby="uploadDocumentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="uploadDocumentModalLabel">
                    <i class="fas fa-upload me-2"></i>رفع مستند جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form id="uploadDocumentForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="upload_contract_id" name="contract_id">

                    <div class="mb-3">
                        <label for="document_title" class="form-label">عنوان المستند <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="document_title" name="document_title" required>
                    </div>

                    <div class="mb-3">
                        <label for="document_type" class="form-label">نوع المستند</label>
                        <select class="form-select" id="document_type" name="document_type">
                            <option value="attachment">مرفق</option>
                            <option value="link">رابط</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="document_file" class="form-label">الملف <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="document_file" name="document_file"
                               accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png" required>
                        <div class="form-text">الملفات المدعومة: PDF, Word, Excel, الصور (حد أقصى 10 ميجابايت)</div>
                    </div>

                    <div class="mb-3">
                        <label for="document_description" class="form-label">وصف المستند</label>
                        <textarea class="form-control" id="document_description" name="document_description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-upload me-2"></i>رفع المستند
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة إضافة رابط -->
<div class="modal fade" id="addLinkModal" tabindex="-1" aria-labelledby="addLinkModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="addLinkModalLabel">
                    <i class="fas fa-link me-2"></i>إضافة رابط جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form id="addLinkForm">
                <div class="modal-body">
                    <input type="hidden" id="link_contract_id" name="contract_id">

                    <div class="mb-3">
                        <label for="link_title" class="form-label">عنوان الرابط <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="link_title" name="link_title" required>
                    </div>

                    <div class="mb-3">
                        <label for="link_url" class="form-label">الرابط (URL) <span class="text-danger">*</span></label>
                        <input type="url" class="form-control" id="link_url" name="link_url"
                               placeholder="https://example.com" required>
                        <div class="form-text">يجب أن يبدأ الرابط بـ http:// أو https://</div>
                    </div>

                    <div class="mb-3">
                        <label for="link_type" class="form-label">نوع الرابط</label>
                        <select class="form-select" id="link_type" name="link_type">
                            <option value="link">رابط</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="link_description" class="form-label">وصف الرابط</label>
                        <textarea class="form-control" id="link_description" name="link_description" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="link_open_new_tab" name="link_open_new_tab" checked>
                            <label class="form-check-label" for="link_open_new_tab">
                                فتح في نافذة جديدة
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-link me-2"></i>إضافة الرابط
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}
