{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- شريط الأدوات العلوي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-search me-2"></i>
                            البحث المتقدم في طلبات الشراء
                        </h5>
                        <div class="btn-group">
                            <a href="{{ url_for('purchase_requests.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج البحث المتقدم -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">معايير البحث</h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ url_for('purchase_requests.index') }}">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="req_no" class="form-label">رقم الطلب</label>
                                    <input type="text" class="form-control" id="req_no" name="req_no" 
                                           placeholder="أدخل رقم الطلب">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="requester_name" class="form-label">اسم الطالب</label>
                                    <input type="text" class="form-control" id="requester_name" name="requester_name" 
                                           placeholder="أدخل اسم الطالب">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="department" class="form-label">القسم</label>
                                    <input type="text" class="form-control" id="department" name="department" 
                                           placeholder="أدخل اسم القسم">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="status" class="form-label">الحالة</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="">جميع الحالات</option>
                                        <option value="مسودة">مسودة</option>
                                        <option value="قيد المراجعة">قيد المراجعة</option>
                                        <option value="معتمد">معتمد</option>
                                        <option value="مرفوض">مرفوض</option>
                                        <option value="ملغي">ملغي</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="priority" class="form-label">الأولوية</label>
                                    <select class="form-select" id="priority" name="priority">
                                        <option value="">جميع الأولويات</option>
                                        <option value="عادي">عادي</option>
                                        <option value="مهم">مهم</option>
                                        <option value="عاجل">عاجل</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="date_from" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="date_to" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="amount_from" class="form-label">من مبلغ</label>
                                    <input type="number" class="form-control" id="amount_from" name="amount_from" 
                                           step="0.01" placeholder="0.00">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="amount_to" class="form-label">إلى مبلغ</label>
                                    <input type="number" class="form-control" id="amount_to" name="amount_to" 
                                           step="0.01" placeholder="0.00">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="currency" class="form-label">العملة</label>
                                    <select class="form-select" id="currency" name="currency">
                                        <option value="">جميع العملات</option>
                                        <option value="ريال سعودي">ريال سعودي</option>
                                        <option value="دولار أمريكي">دولار أمريكي</option>
                                        <option value="يورو">يورو</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="purpose" class="form-label">الغرض من الطلب</label>
                                    <input type="text" class="form-control" id="purpose" name="purpose" 
                                           placeholder="البحث في الغرض من الطلب">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">الملاحظات</label>
                                    <input type="text" class="form-control" id="notes" name="notes" 
                                           placeholder="البحث في الملاحظات">
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-1"></i>بحث
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary ms-2" onclick="clearForm()">
                                            <i class="fas fa-eraser me-1"></i>مسح
                                        </button>
                                    </div>
                                    <div>
                                        <button type="button" class="btn btn-outline-success" onclick="exportResults()">
                                            <i class="fas fa-file-excel me-1"></i>تصدير النتائج
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- نصائح البحث -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        نصائح للبحث
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>يمكنك استخدام جزء من النص للبحث</li>
                                <li><i class="fas fa-check text-success me-2"></i>البحث غير حساس لحالة الأحرف</li>
                                <li><i class="fas fa-check text-success me-2"></i>يمكنك الجمع بين عدة معايير</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>اترك الحقل فارغاً لتجاهل المعيار</li>
                                <li><i class="fas fa-check text-success me-2"></i>استخدم التواريخ لتحديد فترة زمنية</li>
                                <li><i class="fas fa-check text-success me-2"></i>يمكنك تصدير النتائج إلى Excel</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function clearForm() {
    document.querySelector('form').reset();
}

function exportResults() {
    // هنا يمكن إضافة وظيفة التصدير
    alert('سيتم إضافة وظيفة التصدير قريباً');
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات للحقول
    const inputs = document.querySelectorAll('input, select');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
});
</script>

<style>
.focused {
    transform: scale(1.02);
    transition: transform 0.2s ease;
}

.card-header.bg-info {
    background: linear-gradient(135deg, #17a2b8, #20c997) !important;
}

.list-unstyled li {
    padding: 0.25rem 0;
}
</style>
{% endblock %}
