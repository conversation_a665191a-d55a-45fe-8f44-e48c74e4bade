-- =====================================================
-- إجراء تنفيذ الحوالة والترحيل المحاسبي
-- Execute Transfer Accounting Procedure
-- =====================================================

-- إنشاء إجراء تنفيذ الحوالة وترحيل الأرصدة
CREATE OR REPLACE PROCEDURE EXECUTE_TRANSFER_ACCOUNTING(
    p_transfer_id IN NUMBER,
    p_money_changer_id IN NUMBER,
    p_total_amount IN NUMBER,
    p_currency_code IN VARCHAR2,
    p_supplier_distributions IN CLOB, -- JSON array of supplier distributions
    p_user_id IN NUMBER DEFAULT 1,
    p_ip_address IN VARCHAR2 DEFAULT NULL,
    p_session_id IN VARCHAR2 DEFAULT NULL
) AS
    v_supplier_id NUMBER;
    v_supplier_amount NUMBER;
    v_distribution_count NUMBER := 0;
    v_total_distributed NUMBER := 0;
    v_json_array JSON_ARRAY_T;
    v_json_obj JSON_OBJECT_T;
    v_error_msg VARCHAR2(4000);
    v_start_time TIMESTAMP;
    v_execution_time NUMBER;
    v_transfer_status VARCHAR2(50);
    v_existing_money_changer NUMBER;
    
    -- Exception للأخطاء المخصصة
    e_invalid_data EXCEPTION;
    e_transfer_not_found EXCEPTION;
    e_amount_mismatch EXCEPTION;
    e_insufficient_balance EXCEPTION;
    
BEGIN
    v_start_time := CURRENT_TIMESTAMP;
    
    -- بدء المعاملة
    SAVEPOINT transfer_start;
    
    -- تسجيل بداية العملية
    LOG_TRANSFER_ACTIVITY(
        p_transfer_id => p_transfer_id,
        p_activity_type => 'EXECUTION_STARTED',
        p_description => 'بدء عملية تنفيذ الحوالة والترحيل المحاسبي',
        p_amount_after => p_total_amount,
        p_currency_code => p_currency_code,
        p_entity_type => 'MONEY_CHANGER',
        p_entity_id => p_money_changer_id,
        p_user_id => p_user_id,
        p_ip_address => p_ip_address,
        p_session_id => p_session_id
    );
    
    -- 1. التحقق من صحة البيانات الأساسية
    IF p_transfer_id IS NULL OR p_transfer_id <= 0 THEN
        RAISE e_invalid_data;
    END IF;
    
    IF p_money_changer_id IS NULL OR p_money_changer_id <= 0 THEN
        RAISE e_invalid_data;
    END IF;
    
    IF p_total_amount IS NULL OR p_total_amount <= 0 THEN
        RAISE e_invalid_data;
    END IF;
    
    IF p_currency_code IS NULL THEN
        RAISE e_invalid_data;
    END IF;
    
    -- 2. التحقق من وجود الحوالة وحالتها
    BEGIN
        SELECT status, money_changer_id 
        INTO v_transfer_status, v_existing_money_changer
        FROM transfers 
        WHERE id = p_transfer_id;
        
        IF v_transfer_status != 'approved' THEN
            v_error_msg := 'الحوالة غير معتمدة. الحالة الحالية: ' || v_transfer_status;
            RAISE e_transfer_not_found;
        END IF;
        
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RAISE e_transfer_not_found;
    END;
    
    -- 3. التحقق من رصيد الصراف/البنك
    DECLARE
        v_current_balance NUMBER := 0;
    BEGIN
        SELECT NVL(current_balance, 0) INTO v_current_balance
        FROM CURRENT_BALANCES
        WHERE entity_type_code = 'MONEY_CHANGER'
        AND entity_id = p_money_changer_id
        AND currency_code = p_currency_code;
        
        IF v_current_balance < p_total_amount THEN
            v_error_msg := 'رصيد الصراف غير كافي. الرصيد الحالي: ' || v_current_balance || ' المطلوب: ' || p_total_amount;
            RAISE e_insufficient_balance;
        END IF;
        
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            v_error_msg := 'لا يوجد رصيد للصراف في هذه العملة';
            RAISE e_insufficient_balance;
    END;
    
    -- 4. معالجة توزيعات الموردين
    IF p_supplier_distributions IS NOT NULL THEN
        BEGIN
            v_json_array := JSON_ARRAY_T.parse(p_supplier_distributions);
            
            FOR i IN 0..v_json_array.get_size-1 LOOP
                v_json_obj := JSON_OBJECT_T(v_json_array.get(i));
                v_supplier_id := v_json_obj.get_Number('supplier_id');
                v_supplier_amount := v_json_obj.get_Number('amount');
                
                -- التحقق من صحة بيانات التوزيع
                IF v_supplier_id IS NULL OR v_supplier_id <= 0 THEN
                    v_error_msg := 'معرف المورد غير صحيح في التوزيع رقم ' || (i + 1);
                    RAISE e_invalid_data;
                END IF;
                
                IF v_supplier_amount IS NULL OR v_supplier_amount <= 0 THEN
                    v_error_msg := 'مبلغ المورد غير صحيح في التوزيع رقم ' || (i + 1);
                    RAISE e_invalid_data;
                END IF;
                
                -- التحقق من وجود المورد
                DECLARE
                    v_supplier_exists NUMBER;
                BEGIN
                    SELECT COUNT(*) INTO v_supplier_exists
                    FROM suppliers WHERE id = v_supplier_id;
                    
                    IF v_supplier_exists = 0 THEN
                        v_error_msg := 'المورد رقم ' || v_supplier_id || ' غير موجود';
                        RAISE e_invalid_data;
                    END IF;
                END;
                
                -- ترحيل المبلغ للمورد (زيادة رصيد المورد - الجانب المدين)
                UPDATE_CURRENT_BALANCE(
                    p_entity_type => 'SUPPLIER',
                    p_entity_id => v_supplier_id,
                    p_currency_code => p_currency_code,
                    p_debit_amount => v_supplier_amount,
                    p_credit_amount => 0,
                    p_document_type => 'TRANSFER',
                    p_document_number => 'TRF-' || p_transfer_id,
                    p_description => 'تنفيذ حوالة رقم ' || p_transfer_id,
                    p_user_id => p_user_id
                );
                
                -- حفظ تفاصيل التوزيع
                INSERT INTO transfer_supplier_distributions (
                    transfer_id, supplier_id, amount, currency_code,
                    created_by, notes
                ) VALUES (
                    p_transfer_id, v_supplier_id, v_supplier_amount, p_currency_code,
                    p_user_id, 'توزيع تلقائي عند تنفيذ الحوالة'
                );
                
                -- تسجيل النشاط للمورد
                LOG_TRANSFER_ACTIVITY(
                    p_transfer_id => p_transfer_id,
                    p_activity_type => 'BALANCE_UPDATED',
                    p_description => 'تم ترحيل مبلغ ' || v_supplier_amount || ' ' || p_currency_code || ' لرصيد المورد',
                    p_amount_after => v_supplier_amount,
                    p_currency_code => p_currency_code,
                    p_entity_type => 'SUPPLIER',
                    p_entity_id => v_supplier_id,
                    p_user_id => p_user_id,
                    p_ip_address => p_ip_address,
                    p_session_id => p_session_id
                );
                
                v_distribution_count := v_distribution_count + 1;
                v_total_distributed := v_total_distributed + v_supplier_amount;
            END LOOP;
            
        EXCEPTION
            WHEN OTHERS THEN
                v_error_msg := 'خطأ في معالجة توزيعات الموردين: ' || SQLERRM;
                RAISE e_invalid_data;
        END;
    END IF;
    
    -- 5. التحقق من تطابق المبالغ
    IF ABS(v_total_distributed - p_total_amount) > 0.01 THEN
        v_error_msg := 'مجموع التوزيعات (' || v_total_distributed || ') لا يطابق مبلغ الحوالة (' || p_total_amount || ')';
        RAISE e_amount_mismatch;
    END IF;
    
    -- 6. ترحيل المبلغ من الصراف/البنك (تقليل رصيد الصراف - الجانب الدائن)
    UPDATE_CURRENT_BALANCE(
        p_entity_type => 'MONEY_CHANGER',
        p_entity_id => p_money_changer_id,
        p_currency_code => p_currency_code,
        p_debit_amount => 0,
        p_credit_amount => p_total_amount,
        p_document_type => 'TRANSFER',
        p_document_number => 'TRF-' || p_transfer_id,
        p_description => 'خصم حوالة رقم ' || p_transfer_id,
        p_user_id => p_user_id
    );
    
    -- 7. تحديث حالة الحوالة
    UPDATE transfers SET
        status = 'executed',
        executed_at = CURRENT_TIMESTAMP,
        executed_by = p_user_id,
        money_changer_id = p_money_changer_id,
        supplier_distributions_count = v_distribution_count,
        total_distributed_amount = v_total_distributed,
        updated_at = CURRENT_TIMESTAMP,
        updated_by = p_user_id
    WHERE id = p_transfer_id;
    
    -- 8. حساب وقت التنفيذ
    v_execution_time := EXTRACT(SECOND FROM (CURRENT_TIMESTAMP - v_start_time)) * 1000;
    
    -- 9. تسجيل نجاح العملية
    LOG_TRANSFER_ACTIVITY(
        p_transfer_id => p_transfer_id,
        p_activity_type => 'EXECUTED',
        p_description => 'تم تنفيذ الحوالة وترحيل الأرصدة بنجاح - الموردين: ' || v_distribution_count || ' - المبلغ: ' || p_total_amount,
        p_old_status => 'approved',
        p_new_status => 'executed',
        p_amount_after => p_total_amount,
        p_currency_code => p_currency_code,
        p_entity_type => 'MONEY_CHANGER',
        p_entity_id => p_money_changer_id,
        p_execution_time_ms => v_execution_time,
        p_user_id => p_user_id,
        p_ip_address => p_ip_address,
        p_session_id => p_session_id
    );
    
    COMMIT;
    
    -- إرجاع رسالة نجاح
    DBMS_OUTPUT.PUT_LINE('SUCCESS: تم تنفيذ الحوالة ' || p_transfer_id || ' بنجاح');
    
EXCEPTION
    WHEN e_invalid_data THEN
        ROLLBACK TO transfer_start;
        v_error_msg := NVL(v_error_msg, 'بيانات الحوالة غير صحيحة');
        
        LOG_TRANSFER_ACTIVITY(
            p_transfer_id => p_transfer_id,
            p_activity_type => 'VALIDATION_FAILED',
            p_description => 'فشل في التحقق من البيانات',
            p_error_message => v_error_msg,
            p_user_id => p_user_id,
            p_ip_address => p_ip_address,
            p_session_id => p_session_id
        );
        
        RAISE_APPLICATION_ERROR(-20001, v_error_msg);
        
    WHEN e_transfer_not_found THEN
        ROLLBACK TO transfer_start;
        v_error_msg := NVL(v_error_msg, 'الحوالة غير موجودة أو غير معتمدة');
        
        LOG_TRANSFER_ACTIVITY(
            p_transfer_id => p_transfer_id,
            p_activity_type => 'ERROR',
            p_description => 'الحوالة غير موجودة أو غير معتمدة',
            p_error_message => v_error_msg,
            p_user_id => p_user_id,
            p_ip_address => p_ip_address,
            p_session_id => p_session_id
        );
        
        RAISE_APPLICATION_ERROR(-20002, v_error_msg);
        
    WHEN e_amount_mismatch THEN
        ROLLBACK TO transfer_start;
        
        LOG_TRANSFER_ACTIVITY(
            p_transfer_id => p_transfer_id,
            p_activity_type => 'VALIDATION_FAILED',
            p_description => 'عدم تطابق المبالغ',
            p_error_message => v_error_msg,
            p_user_id => p_user_id,
            p_ip_address => p_ip_address,
            p_session_id => p_session_id
        );
        
        RAISE_APPLICATION_ERROR(-20004, v_error_msg);
        
    WHEN e_insufficient_balance THEN
        ROLLBACK TO transfer_start;
        
        LOG_TRANSFER_ACTIVITY(
            p_transfer_id => p_transfer_id,
            p_activity_type => 'VALIDATION_FAILED',
            p_description => 'رصيد الصراف غير كافي',
            p_error_message => v_error_msg,
            p_entity_type => 'MONEY_CHANGER',
            p_entity_id => p_money_changer_id,
            p_user_id => p_user_id,
            p_ip_address => p_ip_address,
            p_session_id => p_session_id
        );
        
        RAISE_APPLICATION_ERROR(-20005, v_error_msg);
        
    WHEN OTHERS THEN
        ROLLBACK TO transfer_start;
        v_error_msg := 'خطأ غير متوقع في تنفيذ الحوالة: ' || SQLERRM;
        
        LOG_TRANSFER_ACTIVITY(
            p_transfer_id => p_transfer_id,
            p_activity_type => 'ERROR',
            p_description => 'خطأ غير متوقع في تنفيذ الحوالة',
            p_error_message => v_error_msg,
            p_user_id => p_user_id,
            p_ip_address => p_ip_address,
            p_session_id => p_session_id
        );
        
        RAISE_APPLICATION_ERROR(-20000, v_error_msg);
END;
/

-- إنشاء comments للتوثيق
COMMENT ON PROCEDURE EXECUTE_TRANSFER_ACCOUNTING IS 'إجراء تنفيذ الحوالة والترحيل المحاسبي مع التكامل مع جدول CURRENT_BALANCES';

-- عرض رسالة نجاح
SELECT 'تم إنشاء إجراء EXECUTE_TRANSFER_ACCOUNTING بنجاح' as result FROM DUAL;
