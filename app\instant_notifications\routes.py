# -*- coding: utf-8 -*-
"""
مسارات نظام إدارة الإشعارات الفورية
Instant Notifications Management Routes
"""

from flask import render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from . import instant_notifications_bp
from database_manager import DatabaseManager
import json

def handle_clob(clob_value):
    """معالجة قيم CLOB من Oracle"""
    if clob_value is None:
        return ''
    elif hasattr(clob_value, 'read'):
        return clob_value.read()
    else:
        return str(clob_value)

@instant_notifications_bp.route('/')
@login_required
def dashboard():
    """لوحة تحكم الإشعارات الفورية"""
    try:
        db = DatabaseManager()
        
        # جلب إحصائيات سريعة
        stats_query = """
        SELECT 
            COUNT(*) as total_settings,
            SUM(CASE WHEN is_enabled = 1 THEN 1 ELSE 0 END) as enabled_settings
        FROM instant_notification_settings
        """
        
        stats_result = db.execute_query(stats_query)
        stats = {
            'total_settings': stats_result[0][0] if stats_result else 0,
            'enabled_settings': stats_result[0][1] if stats_result else 0
        }
        
        # جلب عدد جهات الاتصال
        contacts_query = "SELECT COUNT(*) FROM instant_notification_contacts WHERE is_active = 1"
        contacts_result = db.execute_query(contacts_query)
        stats['active_contacts'] = contacts_result[0][0] if contacts_result else 0
        
        # جلب إحصائيات الإرسال اليوم
        today_stats_query = """
        SELECT 
            COUNT(*) as total_sent,
            SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful_sent
        FROM instant_notification_stats 
        WHERE DATE(sent_at) = DATE(CURRENT_TIMESTAMP)
        """
        
        today_result = db.execute_query(today_stats_query)
        if today_result:
            stats['today_total'] = today_result[0][0] or 0
            stats['today_success'] = today_result[0][1] or 0
        else:
            stats['today_total'] = 0
            stats['today_success'] = 0
        
        return render_template('instant_notifications/dashboard.html', stats=stats)
        
    except Exception as e:
        flash(f'خطأ في تحميل لوحة التحكم: {str(e)}', 'error')
        return render_template('instant_notifications/dashboard.html', stats={})

@instant_notifications_bp.route('/settings')
@login_required
def settings():
    """صفحة إعدادات الإشعارات"""
    try:
        db = DatabaseManager()
        
        # جلب جميع إعدادات الإشعارات
        settings_query = """
        SELECT id, event_type, event_name_ar, event_name_en, 
               message_template, is_enabled, updated_at
        FROM instant_notification_settings
        ORDER BY event_name_ar
        """
        
        settings_result = db.execute_query(settings_query)
        settings_list = []
        
        if settings_result:
            for row in settings_result:
                settings_list.append({
                    'id': row[0],
                    'event_type': row[1],
                    'event_name_ar': row[2],
                    'event_name_en': row[3],
                    'message_template': row[4],
                    'is_enabled': bool(row[5]),
                    'updated_at': row[6]
                })
        
        return render_template('instant_notifications/settings.html', settings=settings_list)
        
    except Exception as e:
        flash(f'خطأ في تحميل الإعدادات: {str(e)}', 'error')
        return render_template('instant_notifications/settings.html', settings=[])

@instant_notifications_bp.route('/contacts')
@login_required
def contacts():
    """صفحة إدارة جهات الاتصال"""
    try:
        db = DatabaseManager()
        
        # جلب جميع جهات الاتصال مع أنواعها
        contacts_query = """
        SELECT c.id, c.contact_name, c.phone_number, c.contact_type,
               c.is_active, c.notes, c.created_at,
               ct.type_name_ar, ct.icon_class, ct.color_class, ct.type_code
        FROM instant_notification_contacts c
        LEFT JOIN instant_contact_types ct ON c.contact_type_id = ct.id
        ORDER BY ct.display_order, c.contact_name
        """
        
        contacts_result = db.execute_query(contacts_query)
        contacts_list = []
        
        if contacts_result:
            for row in contacts_result:
                contacts_list.append({
                    'id': row[0],
                    'contact_name': row[1],
                    'phone_number': row[2],
                    'contact_type': row[3],
                    'is_active': bool(row[4]),
                    'notes': row[5],
                    'created_at': row[6],
                    'type_name_ar': row[7] if row[7] else 'غير محدد',
                    'icon_class': row[8] if row[8] else 'fas fa-user',
                    'color_class': row[9] if row[9] else 'text-secondary',
                    'type_code': row[10] if row[10] else 'general'
                })
        
        # جلب أنواع جهات الاتصال المتاحة
        contact_types_query = """
        SELECT id, type_code, type_name_ar, icon_class, color_class
        FROM instant_contact_types
        WHERE is_active = 1
        ORDER BY display_order
        """

        contact_types_result = db.execute_query(contact_types_query)
        contact_types = []

        if contact_types_result:
            for row in contact_types_result:
                contact_types.append({
                    'id': row[0],
                    'type_code': row[1],
                    'type_name_ar': row[2],
                    'icon_class': row[3],
                    'color_class': row[4]
                })

        return render_template('instant_notifications/contacts.html',
                             contacts=contacts_list, contact_types=contact_types)
        
    except Exception as e:
        flash(f'خطأ في تحميل جهات الاتصال: {str(e)}', 'error')
        return render_template('instant_notifications/contacts.html', contacts=[])

@instant_notifications_bp.route('/logs')
@login_required
def logs():
    """صفحة سجل الإشعارات"""
    try:
        db = DatabaseManager()
        
        # جلب آخر 100 إشعار
        logs_query = """
        SELECT id, event_type, phone_number, message_content, 
               status, whatsapp_message_id, sent_at, error_message,
               shipment_id, tracking_number
        FROM instant_notification_stats
        ORDER BY sent_at DESC
        FETCH FIRST 100 ROWS ONLY
        """
        
        logs_result = db.execute_query(logs_query)
        logs_list = []
        
        if logs_result:
            for row in logs_result:
                logs_list.append({
                    'id': row[0],
                    'event_type': row[1],
                    'phone_number': row[2],
                    'message_content': row[3],
                    'status': row[4],
                    'whatsapp_message_id': row[5],
                    'sent_at': row[6],
                    'error_message': row[7],
                    'shipment_id': row[8],
                    'tracking_number': row[9]
                })
        
        return render_template('instant_notifications/logs.html', logs=logs_list)
        
    except Exception as e:
        flash(f'خطأ في تحميل السجل: {str(e)}', 'error')
        return render_template('instant_notifications/logs.html', logs=[])

@instant_notifications_bp.route('/api/update_setting', methods=['POST'])
@login_required
def update_setting():
    """تحديث إعداد إشعار"""
    try:
        data = request.get_json()
        setting_id = data.get('setting_id')
        field = data.get('field')
        value = data.get('value')
        
        if not all([setting_id, field]):
            return jsonify({'success': False, 'message': 'بيانات ناقصة'})
        
        db = DatabaseManager()
        
        # تحديث الحقل المحدد
        if field == 'is_enabled':
            update_query = """
            UPDATE instant_notification_settings 
            SET is_enabled = :1, updated_at = CURRENT_TIMESTAMP, updated_by = :2
            WHERE id = :3
            """
            db.execute_update(update_query, [1 if value else 0, current_user.id, setting_id])
        
        elif field == 'message_template':
            update_query = """
            UPDATE instant_notification_settings 
            SET message_template = :1, updated_at = CURRENT_TIMESTAMP, updated_by = :2
            WHERE id = :3
            """
            db.execute_update(update_query, [value, current_user.id, setting_id])
        
        else:
            return jsonify({'success': False, 'message': 'حقل غير مدعوم'})
        
        db.commit()
        
        return jsonify({'success': True, 'message': 'تم التحديث بنجاح'})
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في التحديث: {str(e)}'})

@instant_notifications_bp.route('/api/add_contact', methods=['POST'])
@login_required
def add_contact():
    """إضافة جهة اتصال جديدة"""
    try:
        data = request.get_json()
        contact_name = data.get('contact_name')
        phone_number = data.get('phone_number')
        contact_type = data.get('contact_type', 'general')
        contact_type_id = data.get('contact_type_id')
        notes = data.get('notes', '')
        
        if not all([contact_name, phone_number]):
            return jsonify({'success': False, 'message': 'الاسم ورقم الهاتف مطلوبان'})
        
        db = DatabaseManager()
        
        # فحص إذا كان الرقم موجود
        check_query = "SELECT COUNT(*) FROM instant_notification_contacts WHERE phone_number = :1"
        result = db.execute_query(check_query, [phone_number])
        
        if result and result[0][0] > 0:
            return jsonify({'success': False, 'message': 'رقم الهاتف موجود بالفعل'})
        
        # إضافة جهة الاتصال
        insert_query = """
        INSERT INTO instant_notification_contacts
        (id, contact_name, phone_number, contact_type, contact_type_id, notes, created_by)
        VALUES (inst_notif_contacts_seq.NEXTVAL, :1, :2, :3, :4, :5, :6)
        """

        db.execute_update(insert_query, [contact_name, phone_number, contact_type, contact_type_id, notes, current_user.id])
        db.commit()
        
        return jsonify({'success': True, 'message': 'تم إضافة جهة الاتصال بنجاح'})
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في الإضافة: {str(e)}'})

@instant_notifications_bp.route('/api/test_message', methods=['POST'])
@login_required
def test_message():
    """اختبار إرسال رسالة"""
    try:
        data = request.get_json()
        phone_number = data.get('phone_number')
        message = data.get('message')

        if not all([phone_number, message]):
            return jsonify({'success': False, 'message': 'رقم الهاتف والرسالة مطلوبان'})

        # إرسال رسالة اختبار
        from app.services.instant_event_processor import InstantEventProcessor
        processor = InstantEventProcessor()

        success = processor._send_whatsapp_direct(phone_number, f"🧪 رسالة اختبار: {message}")

        if success:
            return jsonify({'success': True, 'message': 'تم إرسال رسالة الاختبار بنجاح'})
        else:
            return jsonify({'success': False, 'message': 'فشل في إرسال رسالة الاختبار'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في الاختبار: {str(e)}'})

# ==================== القواعد المرنة ====================

@instant_notifications_bp.route('/flexible-rules')
@login_required
def flexible_rules():
    """صفحة إدارة القواعد المرنة"""
    try:
        db = DatabaseManager()

        # جلب جميع القواعد مع تفاصيلها
        rules_query = """
        SELECT r.id, r.rule_name, r.condition_type, r.condition_field,
               r.condition_operator, r.condition_value, r.message_template,
               r.is_enabled, r.priority_level, r.created_at,
               et.event_name_ar, et.event_category
        FROM notification_flexible_rules r
        JOIN notification_event_types et ON r.event_type_id = et.id
        ORDER BY r.priority_level, r.created_at DESC
        """

        rules_result = db.execute_query(rules_query)
        rules_list = []

        if rules_result:
            for row in rules_result:
                rules_list.append({
                    'id': row[0],
                    'rule_name': row[1],
                    'condition_type': row[2],
                    'condition_field': row[3],
                    'condition_operator': row[4],
                    'condition_value': row[5],
                    'message_template': handle_clob(row[6]),
                    'is_enabled': bool(row[7]),
                    'priority_level': row[8],
                    'created_at': row[9],
                    'event_name_ar': row[10],
                    'event_category': row[11]
                })

        return render_template('instant_notifications/flexible_rules.html', rules=rules_list)

    except Exception as e:
        flash(f'خطأ في تحميل القواعد المرنة: {str(e)}', 'error')
        return render_template('instant_notifications/flexible_rules.html', rules=[])

@instant_notifications_bp.route('/flexible-rules/create')
@login_required
def create_flexible_rule():
    """صفحة إنشاء قاعدة مرنة جديدة"""
    try:
        db = DatabaseManager()

        # جلب أنواع الأحداث
        event_types_query = """
        SELECT id, event_code, event_name_ar, event_category, description_ar
        FROM notification_event_types
        WHERE is_active = 1
        ORDER BY event_category, event_name_ar
        """

        event_types_result = db.execute_query(event_types_query)
        event_types = []

        if event_types_result:
            for row in event_types_result:
                event_types.append({
                    'id': row[0],
                    'event_code': row[1],
                    'event_name_ar': row[2],
                    'event_category': row[3],
                    'description_ar': handle_clob(row[4])
                })

        # جلب جهات الاتصال
        contacts_query = """
        SELECT id, contact_name, phone_number, contact_type
        FROM instant_notification_contacts
        WHERE is_active = 1
        ORDER BY contact_name
        """

        contacts_result = db.execute_query(contacts_query)
        contacts = []

        if contacts_result:
            for row in contacts_result:
                contacts.append({
                    'id': row[0],
                    'contact_name': row[1],
                    'phone_number': row[2],
                    'contact_type': row[3]
                })

        return render_template('instant_notifications/create_rule.html',
                             event_types=event_types, contacts=contacts)

    except Exception as e:
        flash(f'خطأ في تحميل صفحة الإنشاء: {str(e)}', 'error')
        return redirect(url_for('instant_notifications.flexible_rules'))

@instant_notifications_bp.route('/api/create_flexible_rule', methods=['POST'])
@login_required
def api_create_flexible_rule():
    """إنشاء قاعدة مرنة جديدة"""
    try:
        data = request.get_json()

        rule_name = data.get('rule_name')
        event_type_id = data.get('event_type_id')
        condition_type = data.get('condition_type')
        condition_field = data.get('condition_field')
        condition_operator = data.get('condition_operator')
        condition_value = data.get('condition_value')
        message_template = data.get('message_template')
        priority_level = data.get('priority_level', 5)
        selected_contacts = data.get('selected_contacts', [])

        if not all([rule_name, event_type_id, message_template]):
            return jsonify({'success': False, 'message': 'البيانات الأساسية مطلوبة'})

        db = DatabaseManager()

        # إنشاء القاعدة
        create_rule_query = """
        INSERT INTO notification_flexible_rules
        (id, rule_name, event_type_id, condition_type, condition_field,
         condition_operator, condition_value, message_template, priority_level, created_by)
        VALUES (notif_flexible_rules_seq.NEXTVAL, :1, :2, :3, :4, :5, :6, :7, :8, :9)
        """

        db.execute_update(create_rule_query, [
            rule_name, event_type_id, condition_type, condition_field,
            condition_operator, condition_value, message_template,
            priority_level, current_user.id
        ])

        # الحصول على ID القاعدة المُنشأة
        get_id_query = "SELECT notif_flexible_rules_seq.CURRVAL FROM DUAL"
        id_result = db.execute_query(get_id_query)
        rule_id = id_result[0][0] if id_result else None

        # ربط جهات الاتصال
        if selected_contacts and rule_id:
            for contact_id in selected_contacts:
                contact_query = """
                INSERT INTO notification_rule_contacts
                (id, rule_id, contact_id)
                VALUES (notif_rule_contacts_seq.NEXTVAL, :1, :2)
                """
                db.execute_update(contact_query, [rule_id, contact_id])

        db.commit()

        return jsonify({'success': True, 'message': 'تم إنشاء القاعدة بنجاح', 'rule_id': rule_id})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في الإنشاء: {str(e)}'})

@instant_notifications_bp.route('/api/toggle_rule/<int:rule_id>', methods=['POST'])
@login_required
def toggle_rule(rule_id):
    """تفعيل/تعطيل قاعدة"""
    try:
        data = request.get_json()
        is_enabled = data.get('is_enabled', False)

        db = DatabaseManager()

        update_query = """
        UPDATE notification_flexible_rules
        SET is_enabled = :1, updated_at = CURRENT_TIMESTAMP
        WHERE id = :2
        """

        db.execute_update(update_query, [1 if is_enabled else 0, rule_id])
        db.commit()

        return jsonify({'success': True, 'message': 'تم تحديث حالة القاعدة'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في التحديث: {str(e)}'})

@instant_notifications_bp.route('/api/test_rule/<int:rule_id>', methods=['POST'])
@login_required
def test_rule(rule_id):
    """اختبار قاعدة مرنة"""
    try:
        db = DatabaseManager()

        # جلب تفاصيل القاعدة
        rule_query = """
        SELECT r.rule_name, r.message_template, r.condition_field, r.condition_value,
               et.event_name_ar
        FROM notification_flexible_rules r
        JOIN notification_event_types et ON r.event_type_id = et.id
        WHERE r.id = :1
        """

        rule_result = db.execute_query(rule_query, [rule_id])

        if not rule_result:
            return jsonify({'success': False, 'message': 'القاعدة غير موجودة'})

        rule_name, message_template, condition_field, condition_value, event_name = rule_result[0]

        # معالجة CLOB للرسالة
        message_template = handle_clob(message_template)

        # إنشاء رسالة اختبار
        test_message = message_template.replace('{tracking_number}', 'TEST-12345')
        test_message = test_message.replace('{' + condition_field + '}', str(condition_value))
        test_message = f"🧪 اختبار القاعدة: {rule_name}\n\n{test_message}"

        # إرسال لجهات الاتصال المرتبطة
        contacts_query = """
        SELECT c.phone_number, c.contact_name
        FROM notification_rule_contacts rc
        JOIN instant_notification_contacts c ON rc.contact_id = c.id
        WHERE rc.rule_id = :1 AND rc.is_active = 1
        """

        contacts_result = db.execute_query(contacts_query, [rule_id])

        if not contacts_result:
            return jsonify({'success': False, 'message': 'لا توجد جهات اتصال مرتبطة بهذه القاعدة'})

        # إرسال الرسائل
        from app.services.instant_event_processor import InstantEventProcessor
        processor = InstantEventProcessor()

        sent_count = 0
        for phone, name in contacts_result:
            success = processor._send_whatsapp_direct(phone, test_message)
            if success:
                sent_count += 1

        return jsonify({
            'success': True,
            'message': f'تم إرسال {sent_count} رسالة اختبار من أصل {len(contacts_result)}'
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في الاختبار: {str(e)}'})

@instant_notifications_bp.route('/mapping')
@login_required
def notification_mapping():
    """صفحة إدارة ربط الإشعارات بجهات الاتصال"""
    try:
        db = DatabaseManager()

        # جلب جميع أنواع الإشعارات مع جهات الاتصال المرتبطة
        mapping_query = """
        SELECT s.event_type, s.event_name_ar, s.is_enabled,
               m.id as mapping_id, m.contact_id, m.is_active as mapping_active,
               c.contact_name, c.phone_number, c.contact_type
        FROM instant_notification_settings s
        LEFT JOIN instant_notification_mapping m ON s.event_type = m.event_type
        LEFT JOIN instant_notification_contacts c ON m.contact_id = c.id
        ORDER BY s.event_name_ar, c.contact_name
        """

        mapping_result = db.execute_query(mapping_query)

        # تنظيم البيانات
        notifications_map = {}

        if mapping_result:
            for row in mapping_result:
                event_type = row[0]
                event_name_ar = row[1]
                is_enabled = bool(row[2])
                mapping_id = row[3]
                contact_id = row[4]
                mapping_active = bool(row[5]) if row[5] is not None else False
                contact_name = row[6]
                phone_number = row[7]
                contact_type = row[8]

                if event_type not in notifications_map:
                    notifications_map[event_type] = {
                        'event_name_ar': event_name_ar,
                        'is_enabled': is_enabled,
                        'contacts': []
                    }

                if contact_id:
                    notifications_map[event_type]['contacts'].append({
                        'mapping_id': mapping_id,
                        'contact_id': contact_id,
                        'contact_name': contact_name,
                        'phone_number': phone_number,
                        'contact_type': contact_type,
                        'mapping_active': mapping_active
                    })

        # جلب جميع جهات الاتصال المتاحة
        all_contacts_query = """
        SELECT id, contact_name, phone_number, contact_type
        FROM instant_notification_contacts
        WHERE is_active = 1
        ORDER BY contact_name
        """

        all_contacts_result = db.execute_query(all_contacts_query)
        all_contacts = []

        if all_contacts_result:
            for row in all_contacts_result:
                all_contacts.append({
                    'id': row[0],
                    'contact_name': row[1],
                    'phone_number': row[2],
                    'contact_type': row[3]
                })

        return render_template('instant_notifications/mapping.html',
                             notifications_map=notifications_map,
                             all_contacts=all_contacts)

    except Exception as e:
        flash(f'خطأ في تحميل صفحة الربط: {str(e)}', 'error')
        return render_template('instant_notifications/mapping.html',
                             notifications_map={}, all_contacts=[])

@instant_notifications_bp.route('/api/add_mapping', methods=['POST'])
@login_required
def add_mapping():
    """إضافة ربط جديد بين إشعار وجهة اتصال"""
    try:
        data = request.get_json()
        event_type = data.get('event_type')
        contact_id = data.get('contact_id')

        if not all([event_type, contact_id]):
            return jsonify({'success': False, 'message': 'البيانات مطلوبة'})

        db = DatabaseManager()

        # فحص إذا كان الربط موجود
        check_query = """
        SELECT COUNT(*) FROM instant_notification_mapping
        WHERE event_type = :1 AND contact_id = :2
        """

        result = db.execute_query(check_query, [event_type, contact_id])

        if result and result[0][0] > 0:
            return jsonify({'success': False, 'message': 'الربط موجود بالفعل'})

        # إضافة الربط الجديد
        insert_query = """
        INSERT INTO instant_notification_mapping
        (id, event_type, contact_id)
        VALUES (inst_notif_mapping_seq.NEXTVAL, :1, :2)
        """

        db.execute_update(insert_query, [event_type, contact_id])
        db.commit()

        return jsonify({'success': True, 'message': 'تم إضافة الربط بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في الإضافة: {str(e)}'})

@instant_notifications_bp.route('/api/remove_mapping/<int:mapping_id>', methods=['DELETE'])
@login_required
def remove_mapping(mapping_id):
    """حذف ربط بين إشعار وجهة اتصال"""
    try:
        db = DatabaseManager()

        delete_query = "DELETE FROM instant_notification_mapping WHERE id = :1"
        db.execute_update(delete_query, [mapping_id])
        db.commit()

        return jsonify({'success': True, 'message': 'تم حذف الربط بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في الحذف: {str(e)}'})

@instant_notifications_bp.route('/api/update_contact/<int:contact_id>', methods=['PUT'])
@login_required
def update_contact(contact_id):
    """تحديث جهة اتصال"""
    try:
        data = request.get_json()
        contact_name = data.get('contact_name')
        phone_number = data.get('phone_number')
        contact_type = data.get('contact_type', 'general')
        contact_type_id = data.get('contact_type_id')
        notes = data.get('notes', '')

        if not all([contact_name, phone_number]):
            return jsonify({'success': False, 'message': 'الاسم ورقم الهاتف مطلوبان'})

        db = DatabaseManager()

        # فحص إذا كان رقم الهاتف موجود لجهة اتصال أخرى
        check_query = """
        SELECT COUNT(*) FROM instant_notification_contacts
        WHERE phone_number = :1 AND id != :2
        """

        result = db.execute_query(check_query, [phone_number, contact_id])

        if result and result[0][0] > 0:
            return jsonify({'success': False, 'message': 'رقم الهاتف موجود لجهة اتصال أخرى'})

        # تحديث جهة الاتصال
        update_query = """
        UPDATE instant_notification_contacts
        SET contact_name = :1, phone_number = :2, contact_type = :3,
            contact_type_id = :4, notes = :5, updated_at = CURRENT_TIMESTAMP
        WHERE id = :6
        """

        db.execute_update(update_query, [contact_name, phone_number, contact_type,
                                       contact_type_id, notes, contact_id])
        db.commit()

        return jsonify({'success': True, 'message': 'تم تحديث جهة الاتصال بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في التحديث: {str(e)}'})
