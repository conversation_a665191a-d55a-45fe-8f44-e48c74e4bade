-- =====================================================
-- جدول تفاصيل توزيع الحوالات على الموردين (مبسط)
-- Transfer Supplier Distributions Table (Simple)
-- =====================================================

-- 1. حذف الكائنات الموجودة إذا كانت موجودة
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE transfer_supplier_dist CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -942 THEN
            RAISE;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE transfer_supplier_dist_seq';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -2289 THEN
            RAISE;
        END IF;
END;
/

-- 2. إنشاء الجدول
CREATE TABLE transfer_supplier_dist (
    id NUMBER PRIMARY KEY,
    transfer_id NUMBER NOT NULL,
    supplier_id NUMBER NOT NULL,
    amount NUMBER(15,2) NOT NULL,
    currency_code VARCHAR2(10) DEFAULT 'SAR',
    exchange_rate NUMBER(15,6) DEFAULT 1.000000,
    base_currency_amount NUMBER(15,2),
    percentage_of_total NUMBER(5,2),
    notes VARCHAR2(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_at TIMESTAMP,
    updated_by NUMBER
);

-- 3. إضافة القيود
ALTER TABLE transfer_supplier_dist ADD CONSTRAINT fk_tsd_transfer FOREIGN KEY (transfer_id) REFERENCES transfers(id);
ALTER TABLE transfer_supplier_dist ADD CONSTRAINT fk_tsd_supplier FOREIGN KEY (supplier_id) REFERENCES suppliers(id);
ALTER TABLE transfer_supplier_dist ADD CONSTRAINT chk_tsd_amount CHECK (amount > 0);
ALTER TABLE transfer_supplier_dist ADD CONSTRAINT chk_tsd_percentage CHECK (percentage_of_total >= 0 AND percentage_of_total <= 100);
ALTER TABLE transfer_supplier_dist ADD CONSTRAINT uk_tsd_unique UNIQUE (transfer_id, supplier_id);

-- 4. إنشاء sequence
CREATE SEQUENCE transfer_supplier_dist_seq START WITH 1 INCREMENT BY 1 NOCACHE;

-- 5. إنشاء trigger للـ ID التلقائي
CREATE OR REPLACE TRIGGER tsd_id_trigger
    BEFORE INSERT OR UPDATE ON transfer_supplier_dist
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        IF :NEW.id IS NULL THEN
            :NEW.id := transfer_supplier_dist_seq.NEXTVAL;
        END IF;
        :NEW.created_at := CURRENT_TIMESTAMP;
    END IF;
    
    :NEW.updated_at := CURRENT_TIMESTAMP;
    :NEW.base_currency_amount := :NEW.amount * :NEW.exchange_rate;
    
    IF :NEW.percentage_of_total IS NULL THEN
        DECLARE
            v_total_amount NUMBER(15,2);
        BEGIN
            SELECT NVL(net_amount_sent, net_amount_received) INTO v_total_amount
            FROM transfers
            WHERE id = :NEW.transfer_id;
            
            IF v_total_amount > 0 THEN
                :NEW.percentage_of_total := ROUND((:NEW.amount / v_total_amount) * 100, 2);
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                :NEW.percentage_of_total := 0;
        END;
    END IF;
END;
/

-- 6. إنشاء فهارس
CREATE INDEX idx_tsd_transfer ON transfer_supplier_dist(transfer_id);
CREATE INDEX idx_tsd_supplier ON transfer_supplier_dist(supplier_id);
CREATE INDEX idx_tsd_currency ON transfer_supplier_dist(currency_code);
CREATE INDEX idx_tsd_amount ON transfer_supplier_dist(amount);
CREATE INDEX idx_tsd_created ON transfer_supplier_dist(created_at);

-- 7. إضافة أعمدة جديدة لجدول transfers
DECLARE
    v_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_count FROM user_tab_columns WHERE table_name = 'TRANSFERS' AND column_name = 'TOTAL_DISTRIBUTED_AMOUNT';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE transfers ADD total_distributed_amount NUMBER(15,2) DEFAULT 0';
    END IF;
    
    SELECT COUNT(*) INTO v_count FROM user_tab_columns WHERE table_name = 'TRANSFERS' AND column_name = 'DISTRIBUTION_VARIANCE';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE transfers ADD distribution_variance NUMBER(15,2) DEFAULT 0';
    END IF;
    
    SELECT COUNT(*) INTO v_count FROM user_tab_columns WHERE table_name = 'TRANSFERS' AND column_name = 'IS_DISTRIBUTION_COMPLETE';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE transfers ADD is_distribution_complete NUMBER(1) DEFAULT 0';
    END IF;
    
    SELECT COUNT(*) INTO v_count FROM user_tab_columns WHERE table_name = 'TRANSFERS' AND column_name = 'SUPPLIER_DISTRIBUTIONS_COUNT';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE transfers ADD supplier_distributions_count NUMBER DEFAULT 0';
    END IF;
END;
/

-- 8. إنشاء trigger للتحقق من تطابق المبالغ
CREATE OR REPLACE TRIGGER tsd_amount_validation_trigger
    AFTER INSERT OR UPDATE OR DELETE ON transfer_supplier_dist
    FOR EACH ROW
DECLARE
    v_transfer_id NUMBER;
    v_total_distributed NUMBER(15,2) := 0;
    v_transfer_amount NUMBER(15,2) := 0;
    v_difference NUMBER(15,2);
BEGIN
    IF INSERTING OR UPDATING THEN
        v_transfer_id := :NEW.transfer_id;
    ELSIF DELETING THEN
        v_transfer_id := :OLD.transfer_id;
    END IF;
    
    SELECT NVL(SUM(amount), 0) INTO v_total_distributed
    FROM transfer_supplier_dist
    WHERE transfer_id = v_transfer_id;
    
    BEGIN
        SELECT NVL(net_amount_sent, NVL(net_amount_received, 0)) INTO v_transfer_amount
        FROM transfers
        WHERE id = v_transfer_id;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            v_transfer_amount := 0;
    END;
    
    v_difference := ABS(v_total_distributed - v_transfer_amount);
    
    UPDATE transfers SET
        total_distributed_amount = v_total_distributed,
        distribution_variance = v_difference,
        is_distribution_complete = CASE WHEN v_difference <= 0.01 THEN 1 ELSE 0 END,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = v_transfer_id;
    
EXCEPTION
    WHEN OTHERS THEN
        NULL;
END;
/

-- 9. إنشاء trigger لتحديث عدد التوزيعات
CREATE OR REPLACE TRIGGER tsd_count_trigger
    AFTER INSERT OR DELETE ON transfer_supplier_dist
    FOR EACH ROW
DECLARE
    v_transfer_id NUMBER;
    v_count NUMBER;
BEGIN
    IF INSERTING THEN
        v_transfer_id := :NEW.transfer_id;
    ELSIF DELETING THEN
        v_transfer_id := :OLD.transfer_id;
    END IF;
    
    SELECT COUNT(*) INTO v_count
    FROM transfer_supplier_dist
    WHERE transfer_id = v_transfer_id;
    
    UPDATE transfers SET
        supplier_distributions_count = v_count,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = v_transfer_id;
    
EXCEPTION
    WHEN OTHERS THEN
        NULL;
END;
/

-- 10. إنشاء view للتفاصيل
CREATE OR REPLACE VIEW v_transfer_supplier_dist AS
SELECT 
    tsd.*,
    s.name_ar as supplier_name,
    s.supplier_code as supplier_code,
    s.contact_person as supplier_contact,
    t.transfer_number as transfer_number,
    t.status as transfer_status,
    t.execution_date as transfer_executed_at
FROM transfer_supplier_dist tsd
JOIN suppliers s ON tsd.supplier_id = s.id
JOIN transfers t ON tsd.transfer_id = t.id;

-- 11. إنشاء إجراء لحذف التوزيعات
CREATE OR REPLACE PROCEDURE DELETE_TRANSFER_DISTRIBUTIONS(
    p_transfer_id IN NUMBER,
    p_user_id IN NUMBER DEFAULT 1
) AS
    v_transfer_status VARCHAR2(50);
BEGIN
    SELECT status INTO v_transfer_status
    FROM transfers
    WHERE id = p_transfer_id;
    
    IF v_transfer_status = 'executed' THEN
        RAISE_APPLICATION_ERROR(-20001, 'Cannot delete distributions for executed transfer');
    END IF;
    
    DELETE FROM transfer_supplier_dist
    WHERE transfer_id = p_transfer_id;
    
    COMMIT;
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RAISE_APPLICATION_ERROR(-20002, 'Transfer not found');
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END;
/

-- 12. إضافة comments
COMMENT ON TABLE transfer_supplier_dist IS 'Transfer supplier distributions table';
COMMENT ON COLUMN transfer_supplier_dist.id IS 'Unique distribution ID';
COMMENT ON COLUMN transfer_supplier_dist.transfer_id IS 'Transfer ID';
COMMENT ON COLUMN transfer_supplier_dist.supplier_id IS 'Supplier ID';
COMMENT ON COLUMN transfer_supplier_dist.amount IS 'Amount allocated to supplier';

COMMIT;

SELECT 'Transfer supplier distributions table created successfully' as result FROM DUAL;
