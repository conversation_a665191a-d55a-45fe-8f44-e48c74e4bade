"""
مسارات التحكم الصوتي
Voice Control Routes
"""

from flask import Blueprint, render_template, request, jsonify, session
from flask_login import login_required, current_user
import json
import logging
from datetime import datetime

# استيراد نظام التحكم الصوتي
VOICE_AVAILABLE = True  # تفعيل مؤقت للاختبار
try:
    from .voice_controller import ArabicVoiceController
except ImportError as e:
    logging.warning(f"Voice controller not available: {str(e)}")
    VOICE_AVAILABLE = False

bp = Blueprint('ai_voice', __name__, url_prefix='/ai-voice')

# إنشاء مثيل من نظام التحكم الصوتي عند الحاجة
voice_controller = None

def get_voice_controller():
    """الحصول على مثيل من نظام التحكم الصوتي"""
    global voice_controller
    if voice_controller is None and VOICE_AVAILABLE:
        try:
            voice_controller = ArabicVoiceController()
        except Exception as e:
            logging.error(f"Failed to initialize voice controller: {str(e)}")
            return None
    return voice_controller

@bp.route('/purchase-contracts')
@login_required
def voice_purchase_contracts():
    """صفحة التحكم الصوتي لعقود الشراء"""
    return render_template('ai_voice/purchase_contracts_voice.html',
                         voice_available=VOICE_AVAILABLE)

@bp.route('/api/start-voice-session', methods=['POST'])
@login_required
def start_voice_session():
    """بدء جلسة التحكم الصوتي"""
    controller = get_voice_controller()
    if not VOICE_AVAILABLE or not controller:
        return jsonify({
            'success': False,
            'message': 'نظام التحكم الصوتي غير متاح'
        })

    try:
        # بدء جلسة جديدة
        session_id = f"voice_session_{current_user.id}_{datetime.now().timestamp()}"

        # حفظ معرف الجلسة
        session['voice_session_id'] = session_id

        return jsonify({
            'success': True,
            'session_id': session_id,
            'message': 'تم بدء جلسة التحكم الصوتي',
            'suggestions': controller.get_field_suggestions()
        })
        
    except Exception as e:
        logging.error(f"Error starting voice session: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في بدء الجلسة: {str(e)}'
        })

@bp.route('/api/process-voice-command', methods=['POST'])
@login_required
def process_voice_command():
    """معالجة أمر صوتي"""
    controller = get_voice_controller()
    if not VOICE_AVAILABLE or not controller:
        return jsonify({
            'success': False,
            'message': 'نظام التحكم الصوتي غير متاح'
        })

    try:
        data = request.get_json()
        command_type = data.get('type', 'listen')

        if command_type == 'listen':
            # الاستماع للأمر الصوتي
            speech_text = controller.listen_for_speech()

            if speech_text:
                # تحديد الحقل
                field = controller.identify_field_from_speech(speech_text)

                if field:
                    # استخراج البيانات
                    extracted_data = controller.extract_data_from_speech(speech_text, field)

                    return jsonify({
                        'success': True,
                        'speech_text': speech_text,
                        'identified_field': field,
                        'extracted_data': extracted_data,
                        'field_name_ar': controller.get_arabic_field_name(field)
                    })
                else:
                    return jsonify({
                        'success': True,
                        'speech_text': speech_text,
                        'message': 'لم يتم تحديد حقل محدد. يرجى ذكر اسم الحقل.'
                    })
            else:
                return jsonify({
                    'success': False,
                    'message': 'لم يتم التعرف على أي كلام'
                })
        
        elif command_type == 'speak':
            # نطق نص معين
            text = data.get('text', '')
            if text:
                controller.speak_arabic(text)
                return jsonify({
                    'success': True,
                    'message': 'تم نطق النص'
                })
        
        return jsonify({
            'success': False,
            'message': 'نوع أمر غير مدعوم'
        })
        
    except Exception as e:
        logging.error(f"Error processing voice command: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في معالجة الأمر: {str(e)}'
        })

@bp.route('/api/voice-status')
@login_required
def voice_status():
    """حالة نظام التحكم الصوتي"""
    return jsonify({
        'available': VOICE_AVAILABLE,
        'session_active': 'voice_session_id' in session,
        'session_id': session.get('voice_session_id'),
        'microphone_available': True if VOICE_AVAILABLE else False
    })

@bp.route('/api/end-voice-session', methods=['POST'])
@login_required
def end_voice_session():
    """إنهاء جلسة التحكم الصوتي"""
    try:
        # إزالة معرف الجلسة
        if 'voice_session_id' in session:
            del session['voice_session_id']
        
        return jsonify({
            'success': True,
            'message': 'تم إنهاء جلسة التحكم الصوتي'
        })
        
    except Exception as e:
        logging.error(f"Error ending voice session: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في إنهاء الجلسة: {str(e)}'
        })

@bp.route('/api/test-voice')
@login_required
def test_voice():
    """اختبار نظام التحكم الصوتي"""
    if not VOICE_AVAILABLE:
        return jsonify({
            'success': False,
            'message': 'نظام التحكم الصوتي غير متاح',
            'details': 'يرجى تثبيت المكتبات المطلوبة'
        })

    try:
        controller = get_voice_controller()

        # اختبار بسيط للنظام
        test_results = {
            'speech_recognition': True,
            'text_to_speech': True,
            'microphone': True,
            'arabic_support': True
        }

        # اختبار النطق
        if controller:
            controller.speak_arabic("اختبار نظام التحكم الصوتي")

        return jsonify({
            'success': True,
            'message': 'نظام التحكم الصوتي يعمل بشكل صحيح',
            'test_results': test_results
        })
        
    except Exception as e:
        logging.error(f"Voice system test failed: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'فشل اختبار النظام: {str(e)}'
        })
