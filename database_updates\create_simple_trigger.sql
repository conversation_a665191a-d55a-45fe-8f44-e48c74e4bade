-- إنشاء trigger مبسط للأتمتة
CREATE OR REPLACE TRIGGER trg_shipment_automation
    AFTER UPDATE OF status ON shipments
    FOR EACH ROW
    WHEN (OLD.status != NEW.status)
BEGIN
    -- تسجيل تغيير الحالة
    INSERT INTO shipment_status_changes (
        id, shipment_id, old_status, new_status, 
        changed_at, automation_processed
    ) VALUES (
        shipment_status_changes_seq.NEXTVAL,
        :NEW.id,
        :OLD.status,
        :NEW.status,
        CURRENT_TIMESTAMP,
        0
    );
    
    -- إضافة للطابور إذا وجدت قواعد مطابقة
    FOR rule_rec IN (
        SELECT id 
        FROM automation_rules
        WHERE is_active = 1
        AND trigger_condition = 'STATUS_CHANGE_ACTION'
        AND condition_value = :NEW.status
    ) LOOP
        INSERT INTO automation_queue (
            id, shipment_id, old_status, new_status,
            created_at, processed
        ) VALUES (
            automation_queue_seq.NEXTVAL,
            :NEW.id,
            :OLD.status,
            :NEW.status,
            CURRENT_TIMESTAMP,
            0
        );
        EXIT; -- إدراج واحد فقط
    END LOOP;
    
EXCEPTION
    WHEN OTHERS THEN
        NULL; -- تجاهل الأخطاء
END;
/
