-- إصلاح أخطاء الـ triggers

-- 1. حذف الـ trigger القديم إذا كان موجوداً
BEGIN
    EXECUTE IMMEDIATE 'DROP TRIGGER transfer_exec_suppliers_trigger';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -4080 THEN RAISE; END IF; -- تجاهل خطأ "الكائن غير موجود"
END;
/

-- 2. إنشاء trigger جديد باسم أقصر
CREATE OR REPLACE TRIGGER trg_exec_suppliers
    BEFORE INSERT ON transfer_execution_suppliers
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := transfer_exec_supp_seq.NEXTVAL;
    END IF;
END;
/

-- 3. حذف الـ trigger الثاني إذا كان موجوداً
BEGIN
    EXECUTE IMMEDIATE 'DROP TRIGGER update_total_suppliers_trigger';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -4080 THEN RAISE; END IF;
END;
/

-- 4. إضافة العمود المفقود أولاً
BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE transfers ADD updated_execution_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN RAISE; END IF; -- تجاهل خطأ "العمود موجود"
END;
/

-- 5. إنشاء trigger جديد لتحديث إجمالي الموردين
CREATE OR REPLACE TRIGGER trg_update_suppliers_count
    AFTER INSERT OR DELETE ON transfer_execution_suppliers
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        UPDATE transfers 
        SET total_suppliers = (
            SELECT COUNT(*) 
            FROM transfer_execution_suppliers 
            WHERE transfer_id = :NEW.transfer_id
        ),
        updated_execution_at = CURRENT_TIMESTAMP
        WHERE id = :NEW.transfer_id;
    END IF;
    
    IF DELETING THEN
        UPDATE transfers 
        SET total_suppliers = (
            SELECT COUNT(*) 
            FROM transfer_execution_suppliers 
            WHERE transfer_id = :OLD.transfer_id
        ),
        updated_execution_at = CURRENT_TIMESTAMP
        WHERE id = :OLD.transfer_id;
    END IF;
END;
/

-- 6. إضافة بعض البيانات التجريبية للموردين إذا لم تكن موجودة
BEGIN
    INSERT INTO money_changers_banks (id, name, type, contact_person, phone, commission_rate, is_active) 
    VALUES (100, 'البنك الأهلي السعودي - فرع الرياض', 'bank', 'أحمد محمد', '************', 1.5, 1);
EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
        NULL; -- تجاهل خطأ التكرار
END;
/

BEGIN
    INSERT INTO money_changers_banks (id, name, type, contact_person, phone, commission_rate, is_active) 
    VALUES (101, 'صرافة الراجحي - فرع جدة', 'money_changer', 'محمد علي', '************', 2.0, 1);
EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
        NULL;
END;
/

BEGIN
    INSERT INTO money_changers_banks (id, name, type, contact_person, phone, commission_rate, is_active) 
    VALUES (102, 'بنك الرياض - فرع الدمام', 'bank', 'سارة أحمد', '************', 1.8, 1);
EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
        NULL;
END;
/

COMMIT;
