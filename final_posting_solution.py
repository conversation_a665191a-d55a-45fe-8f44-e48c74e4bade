#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الحل النهائي لترحيل أوامر الشراء إلى CURRENT_BALANCES
Final Solution for Purchase Order Posting to CURRENT_BALANCES
"""

import sys
sys.path.append('.')
from oracle_manager import oracle_manager

def manual_post_purchase_order(po_id):
    """ترحيل أمر شراء يدوياً إلى CURRENT_BALANCES"""
    
    try:
        print(f"🔧 ترحيل أمر الشراء ID: {po_id}")
        
        # الحصول على بيانات أمر الشراء
        po_data = oracle_manager.execute_query("""
            SELECT SUPPLIER_CODE, TOTAL_AMOUNT, NVL(CURRENCY, 'USD'), PO_NUMBER, SUPPLIER_NAME
            FROM PURCHASE_ORDERS WHERE ID = :1
        """, [po_id])
        
        if not po_data:
            print(f"❌ أمر الشراء {po_id} غير موجود")
            return False
        
        supplier_code, total_amount, currency, po_number, supplier_name = po_data[0]
        
        if not supplier_code:
            print(f"❌ أمر الشراء {po_id} لا يحتوي على كود مورد")
            return False
        
        print(f"📋 بيانات أمر الشراء:")
        print(f"   📄 رقم الأمر: {po_number}")
        print(f"   👤 المورد: {supplier_name} ({supplier_code})")
        print(f"   💰 المبلغ: {total_amount} {currency}")
        
        # البحث عن الرصيد الحالي
        balance_data = oracle_manager.execute_query("""
            SELECT ID, CURRENT_BALANCE, DEBIT_AMOUNT
            FROM CURRENT_BALANCES
            WHERE entity_type_code = 'SUPPLIER' 
            AND entity_id = :1 AND currency_code = :2
        """, [supplier_code, currency])
        
        if balance_data:
            # تحديث رصيد موجود
            balance_id, current_balance, debit_amount = balance_data[0]
            new_debit = (debit_amount or 0) + total_amount
            new_balance = (current_balance or 0) + total_amount
            
            print(f"📊 تحديث رصيد موجود:")
            print(f"   💰 الرصيد السابق: {current_balance}")
            print(f"   📈 المدين السابق: {debit_amount}")
            print(f"   💰 الرصيد الجديد: {new_balance}")
            print(f"   📈 المدين الجديد: {new_debit}")
            
            oracle_manager.execute_update("""
                UPDATE CURRENT_BALANCES SET
                    DEBIT_AMOUNT = :1,
                    CURRENT_BALANCE = :2,
                    TOTAL_TRANSACTIONS_COUNT = NVL(TOTAL_TRANSACTIONS_COUNT, 0) + 1,
                    LAST_TRANSACTION_DATE = SYSDATE,
                    LAST_DOCUMENT_TYPE = 'PURCHASE_ORDER',
                    LAST_DOCUMENT_NUMBER = :3,
                    UPDATED_AT = CURRENT_TIMESTAMP,
                    UPDATED_BY = 1
                WHERE ID = :4
            """, [new_debit, new_balance, po_number, balance_id])
            
            print(f"✅ تم تحديث الرصيد الموجود")
            
        else:
            # إنشاء رصيد جديد
            new_balance_id = oracle_manager.execute_query("SELECT CURRENT_BALANCES_SEQ.NEXTVAL FROM DUAL")[0][0]
            
            print(f"🆕 إنشاء رصيد جديد:")
            print(f"   🆔 ID الرصيد الجديد: {new_balance_id}")
            print(f"   💰 الرصيد الابتدائي: {total_amount}")
            
            oracle_manager.execute_update("""
                INSERT INTO CURRENT_BALANCES (
                    ID, ENTITY_TYPE_CODE, ENTITY_ID, CURRENCY_CODE,
                    OPENING_BALANCE, DEBIT_AMOUNT, CREDIT_AMOUNT, CURRENT_BALANCE,
                    TOTAL_TRANSACTIONS_COUNT, LAST_TRANSACTION_DATE,
                    LAST_DOCUMENT_TYPE, LAST_DOCUMENT_NUMBER,
                    CREATED_AT, UPDATED_AT, CREATED_BY, UPDATED_BY
                ) VALUES (
                    :1, 'SUPPLIER', :2, :3, 0, :4, 0, :4, 1, SYSDATE,
                    'PURCHASE_ORDER', :5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1
                )
            """, [new_balance_id, supplier_code, currency, total_amount, po_number])
            
            print(f"✅ تم إنشاء رصيد جديد")
        
        # تسجيل العملية
        oracle_manager.execute_update("""
            INSERT INTO SYSTEM_LOGS (
                id, log_type, table_name, operation, record_id, log_message, created_at
            ) VALUES (
                SYSTEM_LOGS_SEQ.NEXTVAL, 'MANUAL_PO_POSTING', 'CURRENT_BALANCES', 'INSERT', :1,
                :2, SYSDATE
            )
        """, [po_id, f'ترحيل يدوي لأمر الشراء {po_number} للمورد {supplier_code} بمبلغ {total_amount} {currency}'])
        
        oracle_manager.commit()
        print(f"✅ تم ترحيل أمر الشراء {po_number} بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الترحيل: {e}")
        oracle_manager.rollback()
        import traceback
        traceback.print_exc()
        return False

def verify_posting_result(po_id):
    """التحقق من نتيجة الترحيل"""
    
    print(f"\n🔍 التحقق من نتيجة ترحيل أمر الشراء ID: {po_id}")
    print("=" * 60)
    
    try:
        # الحصول على بيانات أمر الشراء
        po_data = oracle_manager.execute_query("""
            SELECT SUPPLIER_CODE, PO_NUMBER, TOTAL_AMOUNT, CURRENCY
            FROM PURCHASE_ORDERS WHERE ID = :1
        """, [po_id])
        
        if not po_data:
            print("❌ أمر الشراء غير موجود")
            return False
        
        supplier_code, po_number, total_amount, currency = po_data[0]
        
        # فحص الرصيد في CURRENT_BALANCES
        balance_data = oracle_manager.execute_query("""
            SELECT entity_id, currency_code, current_balance, debit_amount, 
                   last_document_number, last_transaction_date, total_transactions_count
            FROM CURRENT_BALANCES 
            WHERE entity_type_code = 'SUPPLIER' AND entity_id = :1
        """, [supplier_code])
        
        if balance_data:
            balance = balance_data[0]
            print(f"💰 نتيجة الترحيل:")
            print(f"   👤 مورد: {balance[0]}")
            print(f"   💱 عملة: {balance[1]}")
            print(f"   💰 الرصيد الحالي: {balance[2]}")
            print(f"   📈 إجمالي المدين: {balance[3]}")
            print(f"   📄 آخر مستند: {balance[4]}")
            print(f"   📅 آخر معاملة: {balance[5]}")
            print(f"   🔢 عدد المعاملات: {balance[6]}")
            
            # التحقق من صحة الترحيل
            if balance[4] == po_number:
                print("✅ الترحيل صحيح ومطابق لأمر الشراء")
                return True
            else:
                print("⚠️ آخر مستند لا يطابق أمر الشراء المُرحل")
                return False
        else:
            print("❌ لم يتم العثور على رصيد للمورد")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False

def post_all_existing_pos():
    """ترحيل جميع أوامر الشراء الموجودة"""
    
    print("\n🔄 ترحيل جميع أوامر الشراء الموجودة")
    print("=" * 60)
    
    try:
        # الحصول على جميع أوامر الشراء
        all_pos = oracle_manager.execute_query("""
            SELECT ID, PO_NUMBER, SUPPLIER_CODE, SUPPLIER_NAME, TOTAL_AMOUNT, CURRENCY
            FROM PURCHASE_ORDERS 
            WHERE SUPPLIER_CODE IS NOT NULL
            ORDER BY ID
        """)
        
        if not all_pos:
            print("❌ لا توجد أوامر شراء للترحيل")
            return False
        
        print(f"📋 تم العثور على {len(all_pos)} أمر شراء للترحيل")
        
        success_count = 0
        for po in all_pos:
            po_id, po_number, supplier_code, supplier_name, total_amount, currency = po
            
            print(f"\n📄 ترحيل {po_number} للمورد {supplier_name} ({supplier_code})")
            
            if manual_post_purchase_order(po_id):
                success_count += 1
            else:
                print(f"❌ فشل في ترحيل {po_number}")
        
        print(f"\n📊 النتيجة النهائية:")
        print(f"   ✅ تم ترحيل {success_count} أمر شراء بنجاح")
        print(f"   ❌ فشل في ترحيل {len(all_pos) - success_count} أمر شراء")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في ترحيل أوامر الشراء: {e}")
        return False

def show_final_summary():
    """عرض الملخص النهائي"""
    
    print("\n📊 الملخص النهائي لنظام الترحيل")
    print("=" * 60)
    
    try:
        # إحصائيات أوامر الشراء
        po_stats = oracle_manager.execute_query("""
            SELECT COUNT(*) as total_pos,
                   COUNT(CASE WHEN SUPPLIER_CODE IS NOT NULL THEN 1 END) as pos_with_supplier,
                   SUM(TOTAL_AMOUNT) as total_amount
            FROM PURCHASE_ORDERS
        """)
        
        # إحصائيات أرصدة الموردين
        balance_stats = oracle_manager.execute_query("""
            SELECT COUNT(*) as total_balances,
                   SUM(CURRENT_BALANCE) as total_balance,
                   COUNT(CASE WHEN LAST_DOCUMENT_TYPE = 'PURCHASE_ORDER' THEN 1 END) as po_related
            FROM CURRENT_BALANCES 
            WHERE entity_type_code = 'SUPPLIER'
        """)
        
        if po_stats and balance_stats:
            po_data = po_stats[0]
            balance_data = balance_stats[0]
            
            print(f"📋 إحصائيات أوامر الشراء:")
            print(f"   📊 إجمالي أوامر الشراء: {po_data[0]}")
            print(f"   👤 أوامر بكود مورد: {po_data[1]}")
            print(f"   💰 إجمالي المبالغ: {po_data[2] or 0}")
            
            print(f"\n💰 إحصائيات أرصدة الموردين:")
            print(f"   📊 إجمالي أرصدة الموردين: {balance_data[0]}")
            print(f"   💰 إجمالي الأرصدة: {balance_data[1] or 0}")
            print(f"   📄 مرتبطة بأوامر الشراء: {balance_data[2]}")
            
            # تحليل النتائج
            if balance_data[2] > 0:
                print(f"\n✅ نجح نظام الترحيل!")
                print(f"   🎯 تم ترحيل أوامر الشراء إلى CURRENT_BALANCES")
                print(f"   📊 نسبة النجاح: {(balance_data[2] / po_data[1] * 100):.1f}%")
            else:
                print(f"\n⚠️ لم يتم ترحيل أي أوامر شراء بعد")
        
        print(f"\n🎯 التوصيات:")
        print(f"   ✅ استخدم الدالة manual_post_purchase_order() لترحيل أوامر جديدة")
        print(f"   📊 راجع جدول CURRENT_BALANCES لمتابعة الأرصدة")
        print(f"   📝 راجع جدول SYSTEM_LOGS لمتابعة عمليات الترحيل")
        
    except Exception as e:
        print(f"❌ خطأ في عرض الملخص: {e}")

if __name__ == "__main__":
    print("🚀 الحل النهائي لترحيل أوامر الشراء إلى CURRENT_BALANCES")
    print("📅 التاريخ: 2025-09-06")
    print("🕒 الوقت: 00:10")
    
    # ترحيل أمر الشراء الأول كاختبار
    if manual_post_purchase_order(1):
        # التحقق من النتيجة
        if verify_posting_result(1):
            print("\n🎉 نجح الترحيل الأولي!")
            
            # ترحيل باقي أوامر الشراء
            print("\n" + "=" * 60)
            user_input = input("هل تريد ترحيل جميع أوامر الشراء الموجودة؟ (y/n): ")
            
            if user_input.lower() in ['y', 'yes', 'نعم']:
                post_all_existing_pos()
            
            # عرض الملخص النهائي
            show_final_summary()
            
            print("\n" + "=" * 60)
            print("🎉 تم إنشاء نظام ترحيل أوامر الشراء بنجاح!")
            print("💡 الآن يمكن ترحيل أوامر الشراء إلى CURRENT_BALANCES")
            print("🔧 استخدم manual_post_purchase_order(po_id) لترحيل أوامر جديدة")
        else:
            print("\n❌ فشل في التحقق من الترحيل")
    else:
        print("\n❌ فشل في الترحيل الأولي")
