#!/usr/bin/env python3
"""
مصادقة OneDrive باستخدام Device Code Flow
"""

import sys
import os
import requests
import json
import time

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def device_code_auth():
    """مصادقة باستخدام Device Code Flow"""
    
    # قراءة الإعدادات
    config_path = "app/shipments/cloud_config.json"
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        onedrive_config = config.get('onedrive', {})
        client_id = onedrive_config.get('client_id')
        client_secret = onedrive_config.get('client_secret')
        tenant_id = onedrive_config.get('tenant_id')
        
        print(f"🔧 بدء Device Code Flow...")
        print(f"📋 Client ID: {client_id}")
        print(f"📋 Tenant ID: {tenant_id}")
        
        # الخطوة 1: طلب Device Code
        device_code_url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/devicecode"
        
        device_data = {
            'client_id': client_id,
            'scope': 'Files.ReadWrite Files.ReadWrite.All offline_access'
        }
        
        response = requests.post(device_code_url, data=device_data, timeout=30)
        
        if response.status_code != 200:
            print(f"❌ فشل في الحصول على Device Code: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
        
        device_response = response.json()
        device_code = device_response.get('device_code')
        user_code = device_response.get('user_code')
        verification_uri = device_response.get('verification_uri')
        expires_in = device_response.get('expires_in', 900)
        interval = device_response.get('interval', 5)
        
        print(f"\n🔑 Device Code: {device_code}")
        print(f"👤 User Code: {user_code}")
        print(f"🌐 Verification URL: {verification_uri}")
        print(f"⏰ ينتهي خلال: {expires_in} ثانية")
        
        print(f"\n📋 الخطوات:")
        print(f"1. اذهب إلى: {verification_uri}")
        print(f"2. أدخل الكود: {user_code}")
        print(f"3. سجل دخول بحساب Microsoft")
        print(f"4. اقبل الصلاحيات")
        
        # فتح الرابط تلقائياً
        try:
            import webbrowser
            webbrowser.open(verification_uri)
            print("✅ تم فتح المتصفح تلقائياً")
        except:
            print("⚠️ لم يتم فتح المتصفح تلقائياً - افتح الرابط يدوياً")
        
        input(f"\n⏸️ اضغط Enter بعد إكمال المصادقة...")
        
        # الخطوة 2: استطلاع للحصول على Token
        token_url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token"
        
        max_attempts = expires_in // interval
        
        for attempt in range(max_attempts):
            print(f"🔄 محاولة {attempt + 1}/{max_attempts}...")
            
            token_data = {
                'grant_type': 'urn:ietf:params:oauth:grant-type:device_code',
                'client_id': client_id,
                'client_secret': client_secret,
                'device_code': device_code
            }
            
            token_response = requests.post(token_url, data=token_data, timeout=30)
            
            if token_response.status_code == 200:
                token_result = token_response.json()
                access_token = token_result.get('access_token')
                refresh_token = token_result.get('refresh_token')
                
                if access_token:
                    print(f"✅ تم الحصول على User access token!")
                    
                    # اختبار الـ token
                    if test_onedrive_access(access_token):
                        # حفظ الـ tokens
                        config['onedrive']['access_token'] = access_token
                        config['onedrive']['refresh_token'] = refresh_token
                        config['onedrive']['token_type'] = 'user'
                        
                        with open(config_path, 'w', encoding='utf-8') as f:
                            json.dump(config, f, indent=2, ensure_ascii=False)
                        
                        print(f"💾 تم حفظ User tokens")
                        return True
                    else:
                        print(f"❌ فشل في اختبار الـ token")
                        return False
                else:
                    print(f"❌ لم يتم الحصول على access token")
                    return False
            
            elif token_response.status_code == 400:
                error_data = token_response.json()
                error_code = error_data.get('error')
                
                if error_code == 'authorization_pending':
                    print(f"⏳ في انتظار المصادقة...")
                    time.sleep(interval)
                    continue
                elif error_code == 'slow_down':
                    print(f"🐌 إبطاء الطلبات...")
                    time.sleep(interval + 5)
                    continue
                elif error_code == 'expired_token':
                    print(f"❌ انتهت صلاحية Device Code")
                    return False
                elif error_code == 'access_denied':
                    print(f"❌ تم رفض الوصول")
                    return False
                else:
                    print(f"❌ خطأ غير معروف: {error_code}")
                    print(f"📄 Response: {token_response.text}")
                    return False
            else:
                print(f"❌ خطأ في الطلب: {token_response.status_code}")
                print(f"📄 Response: {token_response.text}")
                return False
        
        print(f"❌ انتهت المحاولات")
        return False
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def test_onedrive_access(access_token):
    """اختبار الوصول لـ OneDrive"""
    try:
        print(f"🧪 اختبار الوصول لـ OneDrive...")
        
        # اختبار /me/drive
        test_url = "https://graph.microsoft.com/v1.0/me/drive"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(test_url, headers=headers, timeout=10)
        
        print(f"📊 اختبار /me/drive: {response.status_code}")
        
        if response.status_code == 200:
            drive_info = response.json()
            print(f"✅ OneDrive متاح!")
            print(f"📁 Drive ID: {drive_info.get('id', 'غير متوفر')}")
            print(f"👤 Owner: {drive_info.get('owner', {}).get('user', {}).get('displayName', 'غير متوفر')}")
            
            # اختبار رفع ملف
            return test_file_upload(access_token)
        else:
            print(f"❌ فشل في الوصول لـ OneDrive: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار OneDrive: {e}")
        return False

def test_file_upload(access_token):
    """اختبار رفع ملف"""
    try:
        print(f"📤 اختبار رفع ملف...")
        
        # إنشاء ملف تجريبي
        test_content = "اختبار رفع OneDrive - " + str(int(time.time()))
        test_filename = "test_upload.txt"
        
        with open(test_filename, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # رفع الملف
        upload_url = f"https://graph.microsoft.com/v1.0/me/drive/root:/{test_filename}:/content"
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/octet-stream"
        }
        
        with open(test_filename, 'rb') as f:
            response = requests.put(upload_url, data=f, headers=headers, timeout=30)
        
        print(f"📊 نتيجة الرفع: {response.status_code}")
        
        # حذف الملف التجريبي
        if os.path.exists(test_filename):
            os.remove(test_filename)
        
        if response.status_code in [200, 201]:
            file_data = response.json()
            print(f"✅ تم رفع الملف بنجاح!")
            print(f"🆔 File ID: {file_data.get('id')}")
            return True
        else:
            print(f"❌ فشل في رفع الملف: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الرفع: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء مصادقة OneDrive باستخدام Device Code Flow...")
    success = device_code_auth()
    
    if success:
        print("\n✅ تم إعداد OneDrive بنجاح!")
        print("🎉 الآن يمكن الرفع الحقيقي لـ OneDrive!")
    else:
        print("\n❌ فشل في إعداد OneDrive")
        sys.exit(1)
