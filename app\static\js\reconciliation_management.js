/**
 * نظام إدارة المطابقة المتطور - JavaScript
 * Advanced Reconciliation Management System - JavaScript
 */

// متغيرات عامة
let reconciliationData = {
    cycles: [],
    filteredCycles: [],
    currentPage: 1,
    itemsPerPage: 10,
    totalPages: 0,
    filters: {
        search: '',
        status: 'all',
        type: 'all',
        scope: 'all',
        dateFrom: ''
    }
};

/**
 * تحميل بيانات المطابقة
 */
function loadReconciliationData() {
    showLoadingState();
    
    $.ajax({
        url: '/suppliers/api/reconciliation/cycles',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                reconciliationData.cycles = response.cycles || [];
                reconciliationData.filteredCycles = [...reconciliationData.cycles];
                updateStatistics(response.statistics || {});
                displayReconciliationCycles();
                updatePagination();
            } else {
                showError('خطأ في تحميل بيانات المطابقة: ' + response.message);
                loadSampleReconciliationData();
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في تحميل بيانات المطابقة:', error);
            showError('خطأ في الاتصال بالخادم');
            loadSampleReconciliationData();
        }
    });
}

/**
 * تحميل بيانات تجريبية للمطابقة
 */
function loadSampleReconciliationData() {
    const sampleCycles = [
        {
            cycle_id: 1,
            cycle_name: 'مطابقة سبتمبر 2024',
            cycle_type: 'MONTHLY',
            period_from: '2024-09-01',
            period_to: '2024-09-30',
            status: 'IN_PROGRESS',
            scope: 'ALL',
            total_accounts: 45,
            processed_accounts: 32,
            matched_accounts: 28,
            unmatched_accounts: 4,
            total_differences_amount: -2300.00,
            created_by: 'أحمد محمد',
            created_date: '2024-10-01T09:00:00',
            started_date: '2024-10-01T09:30:00',
            progress_percentage: 71
        },
        {
            cycle_id: 2,
            cycle_name: 'مطابقة أغسطس 2024',
            cycle_type: 'MONTHLY',
            period_from: '2024-08-01',
            period_to: '2024-08-31',
            status: 'COMPLETED',
            scope: 'ALL',
            total_accounts: 43,
            processed_accounts: 43,
            matched_accounts: 41,
            unmatched_accounts: 2,
            total_differences_amount: 850.00,
            created_by: 'فاطمة علي',
            created_date: '2024-09-01T08:00:00',
            completed_date: '2024-09-03T16:45:00',
            progress_percentage: 100
        },
        {
            cycle_id: 3,
            cycle_name: 'مطابقة الربع الثالث 2024',
            cycle_type: 'QUARTERLY',
            period_from: '2024-07-01',
            period_to: '2024-09-30',
            status: 'OPEN',
            scope: 'HIGH_VALUE',
            total_accounts: 15,
            processed_accounts: 0,
            matched_accounts: 0,
            unmatched_accounts: 0,
            total_differences_amount: 0.00,
            created_by: 'محمد سالم',
            created_date: '2024-10-01T14:20:00',
            progress_percentage: 0
        }
    ];
    
    const sampleStats = {
        total_cycles: 3,
        completed_cycles: 1,
        active_cycles: 2,
        total_differences: 1450.00
    };
    
    reconciliationData.cycles = sampleCycles;
    reconciliationData.filteredCycles = [...sampleCycles];
    updateStatistics(sampleStats);
    displayReconciliationCycles();
    updatePagination();
}

/**
 * تحديث الإحصائيات
 */
function updateStatistics(stats) {
    $('#totalCycles').text(stats.total_cycles || 0);
    $('#completedCycles').text(stats.completed_cycles || 0);
    $('#activeCycles').text(stats.active_cycles || 0);
    $('#totalDifferences').text(formatCurrency(stats.total_differences || 0));
}

/**
 * عرض دورات المطابقة
 */
function displayReconciliationCycles() {
    const container = $('#reconciliationList');
    
    if (!reconciliationData.filteredCycles || reconciliationData.filteredCycles.length === 0) {
        showEmptyState();
        return;
    }
    
    // حساب البيانات للصفحة الحالية
    const startIndex = (reconciliationData.currentPage - 1) * reconciliationData.itemsPerPage;
    const endIndex = startIndex + reconciliationData.itemsPerPage;
    const pageCycles = reconciliationData.filteredCycles.slice(startIndex, endIndex);
    
    let html = '';
    pageCycles.forEach(cycle => {
        html += createReconciliationCard(cycle);
    });
    
    container.html(html);
    updatePaginationInfo();
}

/**
 * إنشاء بطاقة دورة مطابقة
 */
function createReconciliationCard(cycle) {
    const statusClass = getStatusClass(cycle.status);
    const statusText = getStatusText(cycle.status);
    const typeText = getTypeText(cycle.cycle_type);
    const scopeText = getScopeText(cycle.scope);
    const differenceClass = cycle.total_differences_amount >= 0 ? 'text-success' : 'text-danger';
    
    return `
        <div class="reconciliation-card ${statusClass}">
            <div class="cycle-header">
                <div class="cycle-info">
                    <h6>${cycle.cycle_name}</h6>
                    <small class="text-muted">
                        ${typeText} | ${scopeText} | ${formatDate(cycle.period_from)} - ${formatDate(cycle.period_to)}
                    </small>
                </div>
                <div class="cycle-status">
                    <span class="status-badge status-${statusClass}">${statusText}</span>
                </div>
            </div>
            
            <div class="cycle-metrics">
                <div class="metric-item">
                    <div class="metric-value">${cycle.total_accounts}</div>
                    <div class="metric-label">إجمالي الحسابات</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value text-success">${cycle.matched_accounts}</div>
                    <div class="metric-label">متطابقة</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value text-warning">${cycle.unmatched_accounts}</div>
                    <div class="metric-label">غير متطابقة</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value ${differenceClass}">
                        ${formatCurrency(cycle.total_differences_amount)}
                    </div>
                    <div class="metric-label">صافي الفروقات</div>
                </div>
            </div>
            
            <div class="progress-section">
                <div class="progress-label">
                    <span>التقدم</span>
                    <span>${cycle.progress_percentage}%</span>
                </div>
                <div class="progress">
                    <div class="progress-bar" role="progressbar" style="width: ${cycle.progress_percentage}%"></div>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <small class="text-muted">
                        <i class="fas fa-user"></i> ${cycle.created_by}
                    </small>
                </div>
                <div class="col-md-6">
                    <small class="text-muted">
                        <i class="fas fa-calendar"></i> ${formatDateTime(cycle.created_date)}
                    </small>
                </div>
            </div>
            
            <div class="cycle-actions">
                <button class="btn btn-primary btn-sm-custom" onclick="viewCycleDetails(${cycle.cycle_id})">
                    <i class="fas fa-eye"></i> عرض التفاصيل
                </button>
                ${cycle.status === 'OPEN' ? 
                    `<button class="btn btn-success btn-sm-custom" onclick="startReconciliation(${cycle.cycle_id})">
                        <i class="fas fa-play"></i> بدء المطابقة
                    </button>` : ''
                }
                ${cycle.status === 'IN_PROGRESS' ? 
                    `<button class="btn btn-warning btn-sm-custom" onclick="pauseReconciliation(${cycle.cycle_id})">
                        <i class="fas fa-pause"></i> إيقاف مؤقت
                    </button>` : ''
                }
                <button class="btn btn-info btn-sm-custom" onclick="exportCycleReport(${cycle.cycle_id})">
                    <i class="fas fa-download"></i> تقرير
                </button>
                <button class="btn btn-outline-secondary btn-sm-custom" onclick="duplicateCycle(${cycle.cycle_id})">
                    <i class="fas fa-copy"></i> نسخ
                </button>
                ${cycle.status === 'OPEN' ? 
                    `<button class="btn btn-danger btn-sm-custom" onclick="deleteCycle(${cycle.cycle_id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>` : ''
                }
            </div>
        </div>
    `;
}

/**
 * فلترة دورات المطابقة
 */
function filterReconciliation() {
    // جمع معايير الفلترة
    reconciliationData.filters = {
        search: $('#searchInput').val().toLowerCase(),
        status: $('#statusFilter').val(),
        type: $('#typeFilter').val(),
        scope: $('#scopeFilter').val(),
        dateFrom: $('#dateFrom').val()
    };
    
    // تطبيق الفلاتر
    reconciliationData.filteredCycles = reconciliationData.cycles.filter(cycle => {
        // فلتر البحث
        if (reconciliationData.filters.search) {
            const searchText = reconciliationData.filters.search;
            const matchesSearch = 
                cycle.cycle_name.toLowerCase().includes(searchText) ||
                cycle.created_by.toLowerCase().includes(searchText);
            if (!matchesSearch) return false;
        }
        
        // فلتر الحالة
        if (reconciliationData.filters.status !== 'all' && cycle.status !== reconciliationData.filters.status) {
            return false;
        }
        
        // فلتر النوع
        if (reconciliationData.filters.type !== 'all' && cycle.cycle_type !== reconciliationData.filters.type) {
            return false;
        }
        
        // فلتر النطاق
        if (reconciliationData.filters.scope !== 'all' && cycle.scope !== reconciliationData.filters.scope) {
            return false;
        }
        
        // فلتر التاريخ
        if (reconciliationData.filters.dateFrom) {
            const cycleDate = new Date(cycle.created_date);
            const filterDate = new Date(reconciliationData.filters.dateFrom);
            if (cycleDate < filterDate) return false;
        }
        
        return true;
    });
    
    // إعادة تعيين الصفحة الحالية
    reconciliationData.currentPage = 1;
    
    // عرض النتائج
    displayReconciliationCycles();
    updatePagination();
}

/**
 * مسح الفلاتر
 */
function clearFilters() {
    $('#searchInput').val('');
    $('#statusFilter').val('all');
    $('#typeFilter').val('all');
    $('#scopeFilter').val('all');
    $('#dateFrom').val('');
    
    reconciliationData.filteredCycles = [...reconciliationData.cycles];
    reconciliationData.currentPage = 1;
    
    displayReconciliationCycles();
    updatePagination();
}

/**
 * عرض نافذة إنشاء دورة جديدة
 */
function showCreateCycleModal() {
    $('#createCycleModal').modal('show');
}

/**
 * إنشاء دورة مطابقة جديدة
 */
function createReconciliationCycle() {
    const formData = {
        cycle_name: $('#cycleName').val(),
        cycle_type: $('#cycleType').val(),
        period_from: $('#periodFrom').val(),
        period_to: $('#periodTo').val(),
        scope: $('#reconciliationScope').val(),
        tolerance_amount: $('#toleranceAmount').val(),
        notes: $('#cycleNotes').val(),
        auto_process: $('#autoProcess').is(':checked')
    };
    
    // التحقق من البيانات المطلوبة
    if (!formData.cycle_name || !formData.period_from || !formData.period_to) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }
    
    showNotification('جاري إنشاء دورة المطابقة...', 'info');
    
    $.ajax({
        url: '/suppliers/api/reconciliation/create-cycle',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                showNotification('تم إنشاء دورة المطابقة بنجاح', 'success');
                $('#createCycleModal').modal('hide');
                loadReconciliationData(); // إعادة تحميل البيانات
            } else {
                showNotification('خطأ في إنشاء دورة المطابقة: ' + response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في إنشاء دورة المطابقة:', error);
            showNotification('خطأ في الاتصال بالخادم', 'error');
        }
    });
}

// دوال الإجراءات
function viewCycleDetails(cycleId) {
    window.location.href = `/suppliers/reconciliation/cycle/${cycleId}`;
}

function startReconciliation(cycleId) {
    if (confirm('هل أنت متأكد من بدء عملية المطابقة؟')) {
        showNotification('جاري بدء عملية المطابقة...', 'info');
    }
}

function pauseReconciliation(cycleId) {
    if (confirm('هل أنت متأكد من إيقاف عملية المطابقة مؤقتاً؟')) {
        showNotification('تم إيقاف عملية المطابقة مؤقتاً', 'warning');
    }
}

function exportCycleReport(cycleId) {
    window.location.href = `/suppliers/reconciliation/cycle/${cycleId}/export`;
}

function duplicateCycle(cycleId) {
    showNotification('سيتم نسخ دورة المطابقة قريباً', 'info');
}

function deleteCycle(cycleId) {
    if (confirm('هل أنت متأكد من حذف دورة المطابقة؟ لا يمكن التراجع عن هذا الإجراء.')) {
        showNotification('تم حذف دورة المطابقة', 'warning');
    }
}

function refreshReconciliation() {
    showNotification('جاري تحديث البيانات...', 'info');
    loadReconciliationData();
}

function showReportsModal() {
    showNotification('سيتم فتح تقارير المطابقة قريباً', 'info');
}

// دوال التصفح والصفحات (مشابهة للملفات الأخرى)
function updatePaginationInfo() {
    const total = reconciliationData.filteredCycles.length;
    const startIndex = (reconciliationData.currentPage - 1) * reconciliationData.itemsPerPage + 1;
    const endIndex = Math.min(startIndex + reconciliationData.itemsPerPage - 1, total);
    
    $('#showingFrom').text(total > 0 ? startIndex : 0);
    $('#showingTo').text(total > 0 ? endIndex : 0);
    $('#totalRecords').text(total);
}

function updatePagination() {
    const total = reconciliationData.filteredCycles.length;
    reconciliationData.totalPages = Math.ceil(total / reconciliationData.itemsPerPage);
    
    const pagination = $('#pagination');
    let html = '';
    
    // زر السابق
    html += `
        <li class="page-item ${reconciliationData.currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${reconciliationData.currentPage - 1})">السابق</a>
        </li>
    `;
    
    // أرقام الصفحات
    for (let i = 1; i <= reconciliationData.totalPages; i++) {
        if (i === reconciliationData.currentPage || 
            i === 1 || 
            i === reconciliationData.totalPages || 
            (i >= reconciliationData.currentPage - 1 && i <= reconciliationData.currentPage + 1)) {
            html += `
                <li class="page-item ${i === reconciliationData.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `;
        } else if (i === reconciliationData.currentPage - 2 || i === reconciliationData.currentPage + 2) {
            html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    // زر التالي
    html += `
        <li class="page-item ${reconciliationData.currentPage === reconciliationData.totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${reconciliationData.currentPage + 1})">التالي</a>
        </li>
    `;
    
    pagination.html(html);
}

function changePage(page) {
    if (page < 1 || page > reconciliationData.totalPages) return;
    
    reconciliationData.currentPage = page;
    displayReconciliationCycles();
    updatePagination();
}

// دوال مساعدة
function showLoadingState() {
    $('#reconciliationList').html(`
        <div class="loading-container">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `);
}

function showEmptyState() {
    $('#reconciliationList').html(`
        <div class="empty-state">
            <i class="fas fa-balance-scale-right"></i>
            <h5>لا توجد دورات مطابقة</h5>
            <p class="text-muted">لم يتم العثور على دورات مطابقة مطابقة للمعايير المحددة</p>
            <button class="btn btn-primary" onclick="showCreateCycleModal()">إنشاء دورة جديدة</button>
        </div>
    `);
}

function showError(message) {
    showNotification(message, 'error');
}

function getStatusClass(status) {
    const statusMap = {
        'OPEN': 'open',
        'IN_PROGRESS': 'in-progress',
        'COMPLETED': 'completed',
        'CANCELLED': 'cancelled'
    };
    return statusMap[status] || 'open';
}

function getStatusText(status) {
    const statusMap = {
        'OPEN': 'مفتوحة',
        'IN_PROGRESS': 'قيد التنفيذ',
        'COMPLETED': 'مكتملة',
        'CANCELLED': 'ملغية'
    };
    return statusMap[status] || status;
}

function getTypeText(type) {
    const typeMap = {
        'MONTHLY': 'شهرية',
        'QUARTERLY': 'ربع سنوية',
        'ANNUAL': 'سنوية',
        'ADHOC': 'خاصة'
    };
    return typeMap[type] || type;
}

function getScopeText(scope) {
    const scopeMap = {
        'ALL': 'جميع الموردين',
        'HIGH_VALUE': 'عالي القيمة',
        'HIGH_RISK': 'عالي المخاطر',
        'SELECTED': 'موردين محددين'
    };
    return scopeMap[scope] || scope;
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount || 0) + ' ر.س';
}

function formatDate(dateString) {
    if (!dateString) return 'غير محدد';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function formatDateTime(dateString) {
    if (!dateString) return 'غير محدد';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'});
}

function showNotification(message, type = 'info') {
    if (typeof showAlert === 'function') {
        showAlert(message, type);
    } else {
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}
