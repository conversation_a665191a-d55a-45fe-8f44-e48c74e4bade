{% extends "base.html" %}

{% block title %}الإعدادات العامة{% endblock %}

{% block extra_css %}
<style>
/* تحسينات المظهر الداكن للإعدادات */
.theme-dark {
    background-color: #1a1a1a !important;
    color: #ffffff !important;
}

.theme-dark .card {
    background-color: #2d2d2d !important;
    border-color: #404040 !important;
    color: #ffffff !important;
}

.theme-dark .card-header {
    background-color: #343a40 !important;
    border-color: #404040 !important;
    color: #ffffff !important;
}

.theme-dark .form-control,
.theme-dark .form-select {
    background-color: #404040 !important;
    border-color: #555555 !important;
    color: #ffffff !important;
}

.theme-dark .form-control:focus,
.theme-dark .form-select:focus {
    background-color: #404040 !important;
    border-color: #4a90e2 !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25) !important;
}

.theme-dark .form-label {
    color: #ffffff !important;
}

.theme-dark .btn-primary {
    background-color: #4a90e2 !important;
    border-color: #4a90e2 !important;
}

.theme-dark .btn-secondary {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
}

.theme-dark .breadcrumb {
    background-color: #2d2d2d !important;
}

.theme-dark .breadcrumb-item a {
    color: #4a90e2 !important;
}

.theme-dark .breadcrumb-item.active {
    color: #ffffff !important;
}

/* تأثير انتقالي سلس */
body, .card, .form-control, .form-select {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('settings.index') }}">الإعدادات</a></li>
                    <li class="breadcrumb-item active">الإعدادات العامة</li>
                </ol>
            </nav>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-sliders-h"></i> الإعدادات العامة</h4>
                </div>
                <div class="card-body">
                    <form id="generalSettingsForm">
                        <div class="row">
                            <!-- اللغة -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="language" class="form-label">اللغة</label>
                                    <select class="form-select" id="language" name="language">
                                        <option value="ar">العربية</option>
                                        <option value="en">English</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- العملة -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="currency" class="form-label">العملة الافتراضية</label>
                                    <select class="form-select" id="currency" name="currency">
                                        {% if currencies %}
                                            {% for currency in currencies %}
                                                <option value="{{ currency.code }}">{{ currency.name_ar }} ({{ currency.code }})</option>
                                            {% endfor %}
                                        {% else %}
                                            <option value="SAR">ريال سعودي (SAR)</option>
                                            <option value="USD">دولار أمريكي (USD)</option>
                                            <option value="EUR">يورو (EUR)</option>
                                            <option value="AED">درهم إماراتي (AED)</option>
                                        {% endif %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- المنطقة الزمنية -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="timezone" class="form-label">المنطقة الزمنية</label>
                                    <select class="form-select" id="timezone" name="timezone">
                                        <option value="Asia/Riyadh">الرياض (GMT+3)</option>
                                        <option value="Asia/Dubai">دبي (GMT+4)</option>
                                        <option value="Asia/Kuwait">الكويت (GMT+3)</option>
                                        <option value="UTC">UTC (GMT+0)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- تنسيق التاريخ -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="dateFormat" class="form-label">تنسيق التاريخ</label>
                                    <select class="form-select" id="dateFormat" name="date_format">
                                        <option value="DD/MM/YYYY">يوم/شهر/سنة</option>
                                        <option value="MM/DD/YYYY">شهر/يوم/سنة</option>
                                        <option value="YYYY-MM-DD">سنة-شهر-يوم</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- المظهر -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="theme" class="form-label">المظهر</label>
                                    <select class="form-select" id="theme" name="theme">
                                        <option value="light" {% if not USER_SETTINGS or USER_SETTINGS.theme == 'light' %}selected{% endif %}>فاتح</option>
                                        <option value="dark" {% if USER_SETTINGS and USER_SETTINGS.theme == 'dark' %}selected{% endif %}>داكن</option>
                                        <option value="auto" {% if USER_SETTINGS and USER_SETTINGS.theme == 'auto' %}selected{% endif %}>تلقائي</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- عدد العناصر في الصفحة -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="itemsPerPage" class="form-label">عدد العناصر في الصفحة</label>
                                    <select class="form-select" id="itemsPerPage" name="items_per_page">
                                        <option value="10">10</option>
                                        <option value="25">25</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-secondary" onclick="loadSettings()">
                                        <i class="fas fa-undo"></i> إعادة تحميل
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> حفظ الإعدادات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحميل الإعدادات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تطبيق المظهر الحالي فور تحميل الصفحة
    const currentTheme = {% if USER_SETTINGS and USER_SETTINGS.theme %}'{{ USER_SETTINGS.theme }}'{% else %}'light'{% endif %};
    applyTheme(currentTheme);

    loadSettings();

    // معالج إرسال النموذج
    document.getElementById('generalSettingsForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveSettings();
    });

    // معالج تغيير المظهر فوراً
    document.getElementById('theme').addEventListener('change', function(e) {
        const selectedTheme = e.target.value;
        console.log('تغيير المظهر إلى:', selectedTheme);
        applyTheme(selectedTheme);
    });
});

// تطبيق المظهر بشكل مباشر وفعال
function applyTheme(theme) {
    console.log('تطبيق المظهر:', theme);

    // إزالة جميع المظاهر السابقة
    document.documentElement.removeAttribute('data-theme');
    document.body.classList.remove('theme-dark', 'theme-light');

    if (theme === 'dark') {
        document.documentElement.setAttribute('data-theme', 'dark');
        document.body.classList.add('theme-dark');

        // تطبيق المظهر الداكن على العناصر الحالية
        document.querySelectorAll('.card').forEach(card => {
            card.style.backgroundColor = '#2d2d2d';
            card.style.borderColor = '#404040';
            card.style.color = '#ffffff';
        });

        document.querySelectorAll('.form-control, .form-select').forEach(input => {
            input.style.backgroundColor = '#404040';
            input.style.borderColor = '#555555';
            input.style.color = '#ffffff';
        });

        document.body.style.backgroundColor = '#1a1a1a';
        document.body.style.color = '#ffffff';

    } else {
        document.body.classList.add('theme-light');

        // إعادة تعيين المظهر الفاتح
        document.querySelectorAll('.card').forEach(card => {
            card.style.backgroundColor = '';
            card.style.borderColor = '';
            card.style.color = '';
        });

        document.querySelectorAll('.form-control, .form-select').forEach(input => {
            input.style.backgroundColor = '';
            input.style.borderColor = '';
            input.style.color = '';
        });

        document.body.style.backgroundColor = '';
        document.body.style.color = '';
    }
}

// تحميل الإعدادات الحالية
async function loadSettings() {
    try {
        const response = await fetch('/settings/api/get-general-settings');
        const data = await response.json();

        if (data.success && data.settings) {
            const settings = data.settings;

            // تحديث جميع الحقول
            if (document.getElementById('language')) {
                document.getElementById('language').value = settings.language || 'ar';
            }
            if (document.getElementById('currency')) {
                document.getElementById('currency').value = settings.currency || 'SAR';
            }
            if (document.getElementById('timezone')) {
                document.getElementById('timezone').value = settings.timezone || 'Asia/Riyadh';
            }
            if (document.getElementById('dateFormat')) {
                document.getElementById('dateFormat').value = settings.date_format || 'DD/MM/YYYY';
            }
            if (document.getElementById('theme')) {
                document.getElementById('theme').value = settings.theme || 'light';
            }
            if (document.getElementById('itemsPerPage')) {
                document.getElementById('itemsPerPage').value = settings.items_per_page || '25';
            }

            console.log('تم تحميل الإعدادات بنجاح:', settings);
        } else {
            console.log('لا توجد إعدادات محفوظة، استخدام الافتراضية');
        }
    } catch (error) {
        console.error('خطأ في تحميل الإعدادات:', error);
    }
}

// حفظ الإعدادات
async function saveSettings() {
    try {
        const form = document.getElementById('generalSettingsForm');
        const formData = new FormData(form);
        const settings = Object.fromEntries(formData);

        console.log('إرسال الإعدادات:', settings);

        const response = await fetch('/settings/api/update-general-settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        });

        console.log('استجابة الخادم:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('بيانات الاستجابة:', data);

        if (data.success) {
            alert('تم حفظ الإعدادات بنجاح');

            // إعادة تحميل الصفحة فوراً لتطبيق التغييرات
            window.location.reload();
        } else {
            alert('خطأ: ' + data.message);
        }
    } catch (error) {
        console.error('خطأ في حفظ الإعدادات:', error);
        alert('خطأ في حفظ الإعدادات: ' + error.message);
    }
}
</script>
{% endblock %}
