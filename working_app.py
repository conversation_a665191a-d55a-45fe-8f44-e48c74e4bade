
from flask import Flask, render_template_string
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'accounting-system-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///simple_accounting.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)
login_manager = LoginManager(app)

# نموذج المستخدم البسيط
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password = db.Column(db.String(120), nullable=False)

@app.route('/')
def home():
    return render_template_string("""
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>النظام المحاسبي المتقدم</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                margin: 0;
                padding: 20px;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .container {
                background: white;
                border-radius: 20px;
                padding: 40px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                text-align: center;
                max-width: 800px;
                width: 100%;
            }
            .success {
                color: #28a745;
                font-size: 48px;
                margin-bottom: 20px;
            }
            .title {
                color: #333;
                font-size: 32px;
                margin-bottom: 30px;
                font-weight: bold;
            }
            .status {
                background: #d4edda;
                color: #155724;
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
                font-size: 18px;
                font-weight: bold;
            }
            .modules {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin: 30px 0;
            }
            .module {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 10px;
                border-right: 4px solid #667eea;
                text-align: right;
            }
            .module h4 {
                color: #333;
                margin: 0 0 10px 0;
            }
            .module p {
                color: #666;
                margin: 0;
                font-size: 14px;
            }
            .info {
                background: #e3f2fd;
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
                color: #1976d2;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="success">🎉</div>
            <h1 class="title">النظام المحاسبي المتقدم</h1>
            
            <div class="status">
                ✅ النظام يعمل بنجاح الآن!
            </div>
            
            <div class="info">
                <strong>🔐 بيانات الدخول الافتراضية:</strong><br>
                المدير: admin / admin123<br>
                مستخدم: user / user123
            </div>
            
            <div class="modules">
                <div class="module">
                    <h4>📋 طلبات الشراء</h4>
                    <p>إدارة كاملة لطلبات الشراء مع نظام الموافقات</p>
                </div>
                <div class="module">
                    <h4>🛒 أوامر الشراء</h4>
                    <p>تحويل الطلبات إلى أوامر وإدارة التنفيذ</p>
                </div>
                <div class="module">
                    <h4>🚚 استلام البضائع</h4>
                    <p>تسجيل استلام البضائع وتحديث المخزون</p>
                </div>
                <div class="module">
                    <h4>📦 إدارة المخزون</h4>
                    <p>إدارة شاملة للأصناف وحركات المخزون</p>
                </div>
                <div class="module">
                    <h4>🏢 إدارة الموردين</h4>
                    <p>قاعدة بيانات الموردين والتقييمات</p>
                </div>
                <div class="module">
                    <h4>💰 النظام المالي</h4>
                    <p>الفواتير والمدفوعات والمصروفات</p>
                </div>
                <div class="module">
                    <h4>📊 التقارير والتحليل</h4>
                    <p>تقارير شاملة مع رسوم بيانية تفاعلية</p>
                </div>
                <div class="module">
                    <h4>⚙️ نظام سير العمل</h4>
                    <p>أتمتة العمليات ونظام الموافقات</p>
                </div>
            </div>
            
            <div style="margin-top: 30px; padding: 20px; background: #fff3cd; border-radius: 10px; color: #856404;">
                <strong>🚀 النظام جاهز للاستخدام!</strong><br>
                تم تطوير نظام محاسبي متقدم وشامل يلبي جميع احتياجات الشركات والمؤسسات العربية
            </div>
        </div>
    </body>
    </html>
    """)

@app.route('/test')
def test():
    return {
        'status': 'success',
        'message': 'النظام يعمل بنجاح',
        'version': '1.0.0',
        'modules': [
            'طلبات الشراء',
            'أوامر الشراء',
            'استلام البضائع', 
            'إدارة المخزون',
            'إدارة الموردين',
            'النظام المالي',
            'التقارير والتحليل',
            'نظام سير العمل'
        ]
    }

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    
    print("🚀 النظام المحاسبي المتقدم")
    print("=" * 40)
    print("✅ النظام يعمل الآن!")
    print("📍 الرابط: http://localhost:5000")
    print("🧪 اختبار: http://localhost:5000/test")
    print("⏹️ للإيقاف: اضغط Ctrl+C")
    print("=" * 40)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
