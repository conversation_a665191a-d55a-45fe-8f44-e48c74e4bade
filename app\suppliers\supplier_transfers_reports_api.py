# -*- coding: utf-8 -*-
"""
API التقارير والإحصائيات لنظام التكامل
Integration Reports and Analytics API
"""

from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from app.database_manager import DatabaseManager
import logging
from datetime import datetime, date, timedelta
from decimal import Decimal

# إنشاء Blueprint
supplier_transfers_reports_api_bp = Blueprint('supplier_transfers_reports_api', __name__, 
                                            url_prefix='/api/suppliers/transfers/reports')

logger = logging.getLogger(__name__)

def decimal_default(obj):
    """تحويل Decimal إلى float للـ JSON"""
    if isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, (date, datetime)):
        return obj.isoformat()
    raise TypeError

@supplier_transfers_reports_api_bp.route('/dashboard-stats')
@login_required
def get_dashboard_stats():
    """API للحصول على إحصائيات لوحة المعلومات"""
    try:
        db = DatabaseManager()
        
        # إحصائيات المدفوعات
        payments_stats_query = """
        SELECT 
            COUNT(*) as total_payments,
            SUM(CASE WHEN payment_status = 'PENDING' THEN 1 ELSE 0 END) as pending_payments,
            SUM(CASE WHEN payment_status = 'APPROVED' THEN 1 ELSE 0 END) as approved_payments,
            SUM(CASE WHEN payment_status = 'EXECUTED' THEN 1 ELSE 0 END) as executed_payments,
            SUM(CASE WHEN payment_status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_payments,
            SUM(base_currency_amount) as total_amount_base,
            SUM(CASE WHEN payment_status = 'COMPLETED' THEN base_currency_amount ELSE 0 END) as completed_amount_base
        FROM SUPPLIER_PAYMENT_TRANSFERS
        WHERE requested_date >= TRUNC(SYSDATE) - 30
        """
        
        payments_stats = db.execute_query(payments_stats_query)
        
        # إحصائيات الموردين
        suppliers_stats_query = """
        SELECT 
            COUNT(DISTINCT s.id) as total_suppliers,
            COUNT(DISTINCT CASE WHEN sb.current_balance > 0 THEN s.id END) as suppliers_with_credit,
            COUNT(DISTINCT CASE WHEN sb.current_balance < 0 THEN s.id END) as suppliers_with_debit,
            SUM(sb.current_balance * c.exchange_rate) as total_balance_base
        FROM SUPPLIERS s
        LEFT JOIN SUPPLIER_BALANCES sb ON s.id = sb.supplier_id
        LEFT JOIN CURRENCIES c ON sb.currency_code = c.code
        WHERE s.is_active = 1
        """
        
        suppliers_stats = db.execute_query(suppliers_stats_query)
        
        # أعلى الموردين من حيث المدفوعات
        top_suppliers_query = """
        SELECT 
            s.name_ar,
            s.supplier_code,
            COUNT(spt.id) as payments_count,
            SUM(spt.base_currency_amount) as total_amount_base
        FROM SUPPLIERS s
        JOIN SUPPLIER_PAYMENT_TRANSFERS spt ON s.id = spt.supplier_id
        WHERE spt.requested_date >= TRUNC(SYSDATE) - 30
        GROUP BY s.id, s.name_ar, s.supplier_code
        ORDER BY SUM(spt.base_currency_amount) DESC
        FETCH FIRST 5 ROWS ONLY
        """
        
        top_suppliers = db.execute_query(top_suppliers_query)
        
        # إحصائيات العملات
        currencies_stats_query = """
        SELECT 
            spt.currency_code,
            c.symbol,
            COUNT(spt.id) as payments_count,
            SUM(spt.payment_amount) as total_amount,
            SUM(spt.base_currency_amount) as total_amount_base
        FROM SUPPLIER_PAYMENT_TRANSFERS spt
        JOIN CURRENCIES c ON spt.currency_code = c.code
        WHERE spt.requested_date >= TRUNC(SYSDATE) - 30
        GROUP BY spt.currency_code, c.symbol
        ORDER BY SUM(spt.base_currency_amount) DESC
        """
        
        currencies_stats = db.execute_query(currencies_stats_query)
        
        # تجميع النتائج
        stats = {
            'payments': {
                'total': int(payments_stats[0][0]) if payments_stats else 0,
                'pending': int(payments_stats[0][1]) if payments_stats else 0,
                'approved': int(payments_stats[0][2]) if payments_stats else 0,
                'executed': int(payments_stats[0][3]) if payments_stats else 0,
                'completed': int(payments_stats[0][4]) if payments_stats else 0,
                'total_amount_base': float(payments_stats[0][5]) if payments_stats and payments_stats[0][5] else 0,
                'completed_amount_base': float(payments_stats[0][6]) if payments_stats and payments_stats[0][6] else 0
            },
            'suppliers': {
                'total': int(suppliers_stats[0][0]) if suppliers_stats else 0,
                'with_credit': int(suppliers_stats[0][1]) if suppliers_stats else 0,
                'with_debit': int(suppliers_stats[0][2]) if suppliers_stats else 0,
                'total_balance_base': float(suppliers_stats[0][3]) if suppliers_stats and suppliers_stats[0][3] else 0
            },
            'top_suppliers': [
                {
                    'name': row[0],
                    'code': row[1],
                    'payments_count': int(row[2]),
                    'total_amount_base': float(row[3])
                } for row in top_suppliers
            ],
            'currencies': [
                {
                    'currency_code': row[0],
                    'symbol': row[1],
                    'payments_count': int(row[2]),
                    'total_amount': float(row[3]),
                    'total_amount_base': float(row[4])
                } for row in currencies_stats
            ]
        }
        
        return jsonify({
            'success': True,
            'stats': stats,
            'period': '30 days'
        })
        
    except Exception as e:
        logger.error(f"خطأ في جلب إحصائيات لوحة المعلومات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@supplier_transfers_reports_api_bp.route('/payments-trend')
@login_required
def get_payments_trend():
    """API للحصول على اتجاه المدفوعات خلال فترة زمنية"""
    try:
        db = DatabaseManager()
        
        # معاملات الاستعلام
        period = request.args.get('period', '30')  # days
        group_by = request.args.get('group_by', 'day')  # day, week, month
        
        # تحديد تنسيق التجميع
        if group_by == 'week':
            date_format = "YYYY-IW"
            date_trunc = "IW"
        elif group_by == 'month':
            date_format = "YYYY-MM"
            date_trunc = "MM"
        else:
            date_format = "YYYY-MM-DD"
            date_trunc = "DD"
        
        query = f"""
        SELECT 
            TO_CHAR(TRUNC(requested_date, '{date_trunc}'), '{date_format}') as period_label,
            TRUNC(requested_date, '{date_trunc}') as period_date,
            COUNT(*) as payments_count,
            SUM(base_currency_amount) as total_amount_base,
            SUM(CASE WHEN payment_status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_count,
            SUM(CASE WHEN payment_status = 'COMPLETED' THEN base_currency_amount ELSE 0 END) as completed_amount_base
        FROM SUPPLIER_PAYMENT_TRANSFERS
        WHERE requested_date >= TRUNC(SYSDATE) - :1
        GROUP BY TRUNC(requested_date, '{date_trunc}')
        ORDER BY TRUNC(requested_date, '{date_trunc}')
        """
        
        results = db.execute_query(query, [int(period)])
        
        trend_data = []
        for row in results:
            trend_data.append({
                'period_label': row[0],
                'period_date': row[1].strftime('%Y-%m-%d') if row[1] else None,
                'payments_count': int(row[2]),
                'total_amount_base': float(row[3]) if row[3] else 0,
                'completed_count': int(row[4]),
                'completed_amount_base': float(row[5]) if row[5] else 0,
                'completion_rate': (int(row[4]) / int(row[2]) * 100) if int(row[2]) > 0 else 0
            })
        
        return jsonify({
            'success': True,
            'trend_data': trend_data,
            'period_days': int(period),
            'group_by': group_by
        })
        
    except Exception as e:
        logger.error(f"خطأ في جلب اتجاه المدفوعات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@supplier_transfers_reports_api_bp.route('/supplier-performance/<int:supplier_id>')
@login_required
def get_supplier_performance(supplier_id):
    """API للحصول على تقرير أداء مورد محدد"""
    try:
        db = DatabaseManager()
        
        # معلومات المورد الأساسية
        supplier_info_query = """
        SELECT 
            s.name_ar, s.supplier_code, s.supplier_type, s.city,
            s.phone, s.email, s.created_at
        FROM SUPPLIERS s
        WHERE s.id = :1
        """
        
        supplier_info = db.execute_query(supplier_info_query, [supplier_id])
        
        if not supplier_info:
            return jsonify({'success': False, 'message': 'المورد غير موجود'}), 404
        
        # إحصائيات المدفوعات
        payments_performance_query = """
        SELECT 
            COUNT(*) as total_payments,
            SUM(base_currency_amount) as total_amount_base,
            AVG(base_currency_amount) as avg_payment_amount,
            MIN(requested_date) as first_payment_date,
            MAX(requested_date) as last_payment_date,
            SUM(CASE WHEN payment_status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_payments,
            AVG(CASE 
                WHEN payment_status = 'COMPLETED' AND completed_date IS NOT NULL 
                THEN TRUNC(completed_date - requested_date) 
                ELSE NULL 
            END) as avg_completion_days
        FROM SUPPLIER_PAYMENT_TRANSFERS
        WHERE supplier_id = :1
        """
        
        payments_performance = db.execute_query(payments_performance_query, [supplier_id])
        
        # توزيع المدفوعات حسب الحالة
        status_distribution_query = """
        SELECT 
            payment_status,
            COUNT(*) as count,
            SUM(base_currency_amount) as total_amount_base
        FROM SUPPLIER_PAYMENT_TRANSFERS
        WHERE supplier_id = :1
        GROUP BY payment_status
        ORDER BY COUNT(*) DESC
        """
        
        status_distribution = db.execute_query(status_distribution_query, [supplier_id])
        
        # توزيع المدفوعات حسب العملة
        currency_distribution_query = """
        SELECT 
            spt.currency_code,
            c.symbol,
            COUNT(*) as count,
            SUM(spt.payment_amount) as total_amount,
            SUM(spt.base_currency_amount) as total_amount_base
        FROM SUPPLIER_PAYMENT_TRANSFERS spt
        JOIN CURRENCIES c ON spt.currency_code = c.code
        WHERE spt.supplier_id = :1
        GROUP BY spt.currency_code, c.symbol
        ORDER BY SUM(spt.base_currency_amount) DESC
        """
        
        currency_distribution = db.execute_query(currency_distribution_query, [supplier_id])
        
        # الأرصدة الحالية
        current_balances_query = """
        SELECT 
            sb.currency_code,
            c.symbol,
            sb.current_balance,
            sb.current_balance * c.exchange_rate as balance_base_currency
        FROM SUPPLIER_BALANCES sb
        JOIN CURRENCIES c ON sb.currency_code = c.code
        WHERE sb.supplier_id = :1
        ORDER BY sb.current_balance * c.exchange_rate DESC
        """
        
        current_balances = db.execute_query(current_balances_query, [supplier_id])
        
        # تجميع النتائج
        supplier_data = supplier_info[0]
        performance_data = payments_performance[0] if payments_performance else None
        
        performance_report = {
            'supplier_info': {
                'name': supplier_data[0],
                'code': supplier_data[1],
                'type': supplier_data[2],
                'city': supplier_data[3],
                'phone': supplier_data[4],
                'email': supplier_data[5],
                'created_at': supplier_data[6].strftime('%Y-%m-%d') if supplier_data[6] else None
            },
            'payments_summary': {
                'total_payments': int(performance_data[0]) if performance_data else 0,
                'total_amount_base': float(performance_data[1]) if performance_data and performance_data[1] else 0,
                'avg_payment_amount': float(performance_data[2]) if performance_data and performance_data[2] else 0,
                'first_payment_date': performance_data[3].strftime('%Y-%m-%d') if performance_data and performance_data[3] else None,
                'last_payment_date': performance_data[4].strftime('%Y-%m-%d') if performance_data and performance_data[4] else None,
                'completed_payments': int(performance_data[5]) if performance_data else 0,
                'avg_completion_days': float(performance_data[6]) if performance_data and performance_data[6] else 0,
                'completion_rate': (int(performance_data[5]) / int(performance_data[0]) * 100) if performance_data and int(performance_data[0]) > 0 else 0
            },
            'status_distribution': [
                {
                    'status': row[0],
                    'count': int(row[1]),
                    'total_amount_base': float(row[2]) if row[2] else 0
                } for row in status_distribution
            ],
            'currency_distribution': [
                {
                    'currency_code': row[0],
                    'symbol': row[1],
                    'count': int(row[2]),
                    'total_amount': float(row[3]) if row[3] else 0,
                    'total_amount_base': float(row[4]) if row[4] else 0
                } for row in currency_distribution
            ],
            'current_balances': [
                {
                    'currency_code': row[0],
                    'symbol': row[1],
                    'current_balance': float(row[2]) if row[2] else 0,
                    'balance_base_currency': float(row[3]) if row[3] else 0
                } for row in current_balances
            ]
        }
        
        return jsonify({
            'success': True,
            'performance_report': performance_report
        })
        
    except Exception as e:
        logger.error(f"خطأ في جلب تقرير أداء المورد: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500
