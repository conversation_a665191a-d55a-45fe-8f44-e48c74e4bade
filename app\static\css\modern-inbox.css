/* تصميم حديث ومتطور للبريد الإلكتروني */
:root {
    --primary: #667eea;
    --primary-dark: #5a67d8;
    --secondary: #764ba2;
    --success: #48bb78;
    --warning: #ed8936;
    --danger: #f56565;
    --info: #4299e1;
    
    --bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --bg-glass: rgba(255, 255, 255, 0.15);
    --bg-card: rgba(255, 255, 255, 0.95);
    --bg-sidebar: rgba(255, 255, 255, 0.9);
    
    --shadow-sm: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif !important;
    background: var(--bg-primary) !important;
    min-height: 100vh;
    overflow-x: hidden;
}

/* الحاوي الرئيسي */
.container-fluid {
    background: var(--bg-primary) !important;
    min-height: 100vh;
    position: relative;
    padding: 20px !important;
}

.container-fluid::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    pointer-events: none;
}

/* البطاقة الرئيسية */
.email-container {
    position: relative !important;
    z-index: 1;
    max-width: 1400px;
    margin: 0 auto;
    background: var(--bg-glass) !important;
    backdrop-filter: blur(20px);
    border-radius: var(--radius-xl) !important;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-xl) !important;
    overflow: hidden;
    animation: slideIn 0.8s ease-out;
    display: flex !important;
    min-height: calc(100vh - 40px);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(40px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* الشريط الجانبي */
.email-sidebar {
    width: 320px !important;
    background: var(--bg-sidebar) !important;
    backdrop-filter: blur(15px);
    border-right: 1px solid rgba(255, 255, 255, 0.2) !important;
    padding: 30px !important;
    overflow-y: auto;
    position: relative;
}

.email-sidebar::after {
    content: '';
    position: absolute;
    top: 20px;
    bottom: 20px;
    right: 0;
    width: 1px;
    background: linear-gradient(to bottom, transparent, var(--primary), transparent);
    opacity: 0.3;
}

/* زر الإنشاء */
.compose-btn {
    width: 100% !important;
    background: var(--bg-primary) !important;
    color: white !important;
    border: none !important;
    padding: 16px 24px !important;
    border-radius: var(--radius-lg) !important;
    font-weight: 600 !important;
    font-size: 15px !important;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-md) !important;
    margin-bottom: 30px !important;
    position: relative;
    overflow: hidden;
}

.compose-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.compose-btn:hover::before {
    left: 100%;
}

.compose-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-lg) !important;
}

/* بطاقة الإحصائيات */
.stats-card {
    background: var(--bg-card) !important;
    border-radius: var(--radius-lg) !important;
    padding: 24px !important;
    margin-bottom: 30px !important;
    box-shadow: var(--shadow-sm) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.stats-card h6 {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #374151 !important;
    margin-bottom: 20px !important;
    display: flex;
    align-items: center;
    gap: 8px;
}

.stats-grid {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 16px !important;
}

.stat-item {
    text-align: center !important;
    padding: 16px !important;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    border-radius: var(--radius-md) !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    transition: var(--transition);
    cursor: pointer;
}

.stat-item:hover {
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-md) !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
}

.stat-number {
    font-size: 24px !important;
    font-weight: 700 !important;
    color: var(--primary) !important;
    margin-bottom: 4px !important;
    display: block !important;
}

.stat-label {
    font-size: 12px !important;
    color: #6b7280 !important;
    font-weight: 500 !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* المجلدات */
.folders-section h6 {
    font-size: 14px !important;
    font-weight: 600 !important;
    color: #374151 !important;
    margin-bottom: 16px !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.folder-item {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 12px 16px !important;
    border-radius: var(--radius-md) !important;
    cursor: pointer !important;
    transition: var(--transition) !important;
    margin-bottom: 4px !important;
    position: relative;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.5) !important;
}

.folder-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--primary);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.folder-item:hover::before {
    transform: scaleY(1);
}

.folder-item:hover {
    background: rgba(102, 126, 234, 0.1) !important;
    transform: translateX(4px) !important;
}

.folder-item.active {
    background: var(--bg-primary) !important;
    color: white !important;
    box-shadow: var(--shadow-md) !important;
}

.folder-item.active::before {
    transform: scaleY(1);
    background: white;
}

.folder-count {
    background: var(--danger) !important;
    color: white !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    padding: 4px 8px !important;
    border-radius: 12px !important;
    min-width: 20px !important;
    text-align: center !important;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* المحتوى الرئيسي */
.email-main {
    background: var(--bg-card) !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
    flex: 1 !important;
}

/* شريط الأدوات */
.email-toolbar {
    padding: 20px 30px !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
    display: flex !important;
    align-items: center !important;
    gap: 16px !important;
    background: #fafafa !important;
}

.email-toolbar .btn {
    padding: 8px 16px !important;
    border: 1px solid #d1d5db !important;
    background: white !important;
    border-radius: var(--radius-md) !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: var(--transition) !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.email-toolbar .btn:hover {
    background: #f3f4f6 !important;
    border-color: var(--primary) !important;
    color: var(--primary) !important;
}

.email-toolbar .btn-outline-primary {
    background: var(--bg-primary) !important;
    color: white !important;
    border-color: var(--primary) !important;
}

.email-toolbar .btn-outline-primary:hover {
    background: var(--primary-dark) !important;
}

/* قائمة الرسائل */
.email-list {
    flex: 1 !important;
    overflow-y: auto !important;
    padding: 20px 30px !important;
}

.email-item {
    background: white !important;
    border: 1px solid #e5e7eb !important;
    border-radius: var(--radius-lg) !important;
    padding: 24px !important;
    margin-bottom: 16px !important;
    cursor: pointer !important;
    transition: var(--transition) !important;
    position: relative !important;
    overflow: hidden !important;
    animation: slideInUp 0.5s ease-out !important;
    display: block !important;
}

.email-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--bg-primary);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.email-item:hover {
    transform: translateY(-4px) !important;
    box-shadow: var(--shadow-lg) !important;
    border-color: var(--primary) !important;
}

.email-item:hover::before {
    transform: scaleY(1);
}

.email-item.unread {
    background: linear-gradient(135deg, #fef7ff 0%, #faf5ff 100%) !important;
    border-color: var(--primary) !important;
    box-shadow: var(--shadow-sm) !important;
}

.email-item.unread::before {
    transform: scaleY(1);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* محتوى الرسالة */
.email-content {
    display: flex !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    gap: 16px !important;
}

.email-sender {
    font-weight: 600 !important;
    font-size: 16px !important;
    color: #1f2937 !important;
    margin-bottom: 4px !important;
}

.email-subject {
    font-size: 15px !important;
    font-weight: 500 !important;
    color: #374151 !important;
    margin-bottom: 8px !important;
    line-height: 1.4 !important;
}

.email-preview {
    color: #6b7280 !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
}

.email-date {
    font-size: 13px !important;
    color: #9ca3af !important;
    font-weight: 500 !important;
    text-align: right !important;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .email-container {
        flex-direction: column !important;
        margin: 10px !important;
        min-height: calc(100vh - 20px) !important;
    }
    
    .email-sidebar {
        width: 100% !important;
        border-right: none !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
        padding: 20px !important;
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr) !important;
        gap: 12px !important;
    }
    
    .stat-number {
        font-size: 20px !important;
    }
    
    .stat-label {
        font-size: 10px !important;
    }
    
    .email-toolbar {
        padding: 15px 20px !important;
        flex-wrap: wrap !important;
        gap: 12px !important;
    }
    
    .email-list {
        padding: 15px 20px !important;
    }
    
    .email-item {
        padding: 20px !important;
    }
}
