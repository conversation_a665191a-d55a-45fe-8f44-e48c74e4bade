-- تحديث جدول وثائق طلبات الحوالات لدعم روابط متعددة
-- Update transfer_documents table for multiple share links support

-- إضافة أعمدة Nextcloud
ALTER TABLE transfer_documents ADD (
    nextcloud_share_link VARCHAR2(500),
    nextcloud_created_at DATE,
    nextcloud_service_info VARCHAR2(100)
);

-- إضافة أعمدة OneDrive  
ALTER TABLE transfer_documents ADD (
    onedrive_share_link VARCHAR2(500),
    onedrive_created_at DATE,
    onedrive_service_info VARCHAR2(100)
);

-- إضافة فهارس للبحث السريع
CREATE INDEX idx_transfer_docs_nextcloud ON transfer_documents(nextcloud_share_link);
CREATE INDEX idx_transfer_docs_onedrive ON transfer_documents(onedrive_share_link);

-- إضافة تعليقات للأعمدة
COMMENT ON COLUMN transfer_documents.nextcloud_share_link IS 'رابط المشاركة في Nextcloud';
COMMENT ON COLUMN transfer_documents.nextcloud_created_at IS 'تاريخ إنشاء رابط Nextcloud';
COMMENT ON COLUMN transfer_documents.nextcloud_service_info IS 'معلومات خدمة Nextcloud';

COMMENT ON COLUMN transfer_documents.onedrive_share_link IS 'رابط المشاركة في OneDrive';
COMMENT ON COLUMN transfer_documents.onedrive_created_at IS 'تاريخ إنشاء رابط OneDrive';
COMMENT ON COLUMN transfer_documents.onedrive_service_info IS 'معلومات خدمة OneDrive';

-- نقل البيانات الموجودة (إذا كانت موجودة)
UPDATE transfer_documents 
SET nextcloud_share_link = share_link,
    nextcloud_created_at = share_created_at,
    nextcloud_service_info = share_service
WHERE share_service LIKE '%Nextcloud%' AND share_link IS NOT NULL;

UPDATE transfer_documents 
SET onedrive_share_link = share_link,
    onedrive_created_at = share_created_at,
    onedrive_service_info = share_service
WHERE share_service LIKE '%OneDrive%' AND share_link IS NOT NULL;

-- إضافة عرض (View) لسهولة الاستعلام
CREATE OR REPLACE VIEW v_transfer_documents_with_links AS
SELECT 
    td.*,
    CASE 
        WHEN nextcloud_share_link IS NOT NULL THEN 1 
        ELSE 0 
    END as has_nextcloud_link,
    CASE 
        WHEN onedrive_share_link IS NOT NULL THEN 1 
        ELSE 0 
    END as has_onedrive_link,
    CASE 
        WHEN nextcloud_share_link IS NOT NULL OR onedrive_share_link IS NOT NULL THEN 1 
        ELSE 0 
    END as has_any_link
FROM transfer_documents td;

COMMIT;
