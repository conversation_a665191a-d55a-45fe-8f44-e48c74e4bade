<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البحث عن صنف</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
        }
        
        .search-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }
        
        .search-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .btn-search {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 10px 30px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .btn-search:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }
        
        .table-hover tbody tr:hover {
            background-color: #f8f9fa;
            cursor: pointer;
        }
        
        .btn-select {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
        }
        
        .btn-select:hover {
            transform: scale(1.1);
        }

        /* تحسينات البحث الفوري */
        .form-control {
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #0d6efd !important;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25) !important;
            transform: scale(1.02);
        }

        /* مؤشرات البحث الفوري */
        .badge {
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* تحسين عداد النتائج */
        #resultsCounter {
            transition: all 0.3s ease;
            border-radius: 20px;
            padding: 8px 12px;
        }

        #resultsCounter.bg-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
        }

        #resultsCounter.bg-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
        }

        #resultsCounter.bg-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
        }
    </style>
</head>
<body>
    <div class="search-container">
        <!-- رأس النافذة -->
        <div class="search-header">
            <h3><i class="fas fa-search me-2"></i>البحث عن صنف</h3>
            <p class="mb-0">جميع الأصناف المتاحة معروضة أدناه. يمكنك البحث لتصفية النتائج.</p>
        </div>
        
        <!-- معايير البحث -->
        <div class="row mb-4">
            <div class="col-md-6">
                <label for="searchItemCode" class="form-label">
                    <i class="fas fa-barcode me-1"></i>كود الصنف
                    <span id="searchIndicatorCode" class="badge bg-success ms-2" style="display: none;">
                        <i class="fas fa-search fa-spin"></i> بحث فوري
                    </span>
                </label>
                <input type="text" class="form-control" id="searchItemCode"
                       placeholder="أدخل كود الصنف..." autofocus>
            </div>
            <div class="col-md-6">
                <label for="searchItemName" class="form-label">
                    <i class="fas fa-tag me-1"></i>اسم الصنف
                    <span id="searchIndicatorName" class="badge bg-success ms-2" style="display: none;">
                        <i class="fas fa-search fa-spin"></i> بحث فوري
                    </span>
                </label>
                <input type="text" class="form-control" id="searchItemName"
                       placeholder="أدخل اسم الصنف...">
            </div>
        </div>
        
        <!-- أزرار البحث وعداد النتائج -->
        <div class="row mb-4">
            <div class="col-8 text-center">
                <button type="button" class="btn btn-search me-3" onclick="searchItems()">
                    <i class="fas fa-search me-2"></i>بحث
                </button>
                <button type="button" class="btn btn-secondary" onclick="clearSearch()">
                    <i class="fas fa-eraser me-2"></i>مسح
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="window.close()">
                    <i class="fas fa-times me-2"></i>إغلاق
                </button>
            </div>
            <div class="col-4 text-end">
                <span id="resultsCounter" class="badge bg-info fs-6">
                    <i class="fas fa-list me-1"></i>
                    جاري التحميل...
                </span>
            </div>
        </div>
        
        <!-- نتائج البحث -->
        <div id="searchResults">
            <div class="text-center text-muted">
                <div class="spinner-border text-success" role="status">
                    <span class="visually-hidden">جاري تحميل الأصناف...</span>
                </div>
                <p class="mt-3">جاري تحميل الأصناف المتاحة...</p>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // البحث عند الضغط على Enter
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchItems();
            }
        });
        
        // البحث في الأصناف
        function searchItems() {
            const searchData = {
                code: document.getElementById('searchItemCode').value.trim(),
                name: document.getElementById('searchItemName').value.trim()
            };
            
            const resultsDiv = document.getElementById('searchResults');
            
            // عرض مؤشر التحميل وتحديث العداد
            resultsDiv.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">جاري البحث...</span>
                    </div>
                    <p class="mt-3">جاري البحث عن الأصناف...</p>
                </div>
            `;
            updateResultsCounter('جاري البحث...', 'warning');
            
            // إرسال طلب البحث
            const params = new URLSearchParams();
            if (searchData.code) params.append('code', searchData.code);
            if (searchData.name) params.append('name', searchData.name);
            
            fetch(`/purchase-orders/api/search-items?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const items = data.items || [];
                    displayResults(items);
                    updateResultsCounter(`${items.length} صنف`, items.length > 0 ? 'success' : 'secondary');
                } else {
                    resultsDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            خطأ في البحث: ${data.message}
                        </div>
                    `;
                    updateResultsCounter('خطأ في البحث', 'danger');
                }
            })
            .catch(error => {
                console.error('خطأ في البحث:', error);
                resultsDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.
                    </div>
                `;
                updateResultsCounter('خطأ في الاتصال', 'danger');
            });
        }

        // تحديث عداد النتائج
        function updateResultsCounter(text, type = 'info') {
            const counter = document.getElementById('resultsCounter');
            if (counter) {
                counter.className = `badge bg-${type} fs-6`;
                counter.innerHTML = `<i class="fas fa-list me-1"></i>${text}`;
            }
        }

        // ==================== البحث الفوري ====================

        let searchTimeout;
        const SEARCH_DELAY = 300; // تأخير 300ms قبل البحث

        // البحث الفوري مع debounce
        function instantSearch() {
            // إلغاء البحث السابق إذا كان موجوداً
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            // إظهار مؤشر البحث الفوري
            showSearchIndicators();

            // تأخير البحث لتجنب الطلبات المتكررة
            searchTimeout = setTimeout(() => {
                searchItems();
                hideSearchIndicators();
            }, SEARCH_DELAY);
        }

        // إظهار مؤشرات البحث الفوري
        function showSearchIndicators() {
            const codeIndicator = document.getElementById('searchIndicatorCode');
            const nameIndicator = document.getElementById('searchIndicatorName');

            if (codeIndicator) codeIndicator.style.display = 'inline-block';
            if (nameIndicator) nameIndicator.style.display = 'inline-block';
        }

        // إخفاء مؤشرات البحث الفوري
        function hideSearchIndicators() {
            const codeIndicator = document.getElementById('searchIndicatorCode');
            const nameIndicator = document.getElementById('searchIndicatorName');

            if (codeIndicator) codeIndicator.style.display = 'none';
            if (nameIndicator) nameIndicator.style.display = 'none';
        }

        // تفعيل البحث الفوري عند تحميل الصفحة
        function initInstantSearch() {
            const codeInput = document.getElementById('searchItemCode');
            const nameInput = document.getElementById('searchItemName');

            if (codeInput) {
                // إزالة oninput القديم وإضافة الجديد
                codeInput.removeAttribute('oninput');
                codeInput.addEventListener('input', function(e) {
                    // تحديث placeholder ديناميكياً
                    updatePlaceholder(e.target);
                    instantSearch();
                });

                // البحث عند الضغط على Enter
                codeInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        if (searchTimeout) clearTimeout(searchTimeout);
                        hideSearchIndicators();
                        searchItems();
                    }
                });

                // تأثيرات التركيز
                codeInput.addEventListener('focus', function() {
                    this.style.borderColor = '#0d6efd';
                    this.style.boxShadow = '0 0 0 0.2rem rgba(13, 110, 253, 0.25)';
                });

                codeInput.addEventListener('blur', function() {
                    this.style.borderColor = '';
                    this.style.boxShadow = '';
                });
            }

            if (nameInput) {
                // إزالة oninput القديم وإضافة الجديد
                nameInput.removeAttribute('oninput');
                nameInput.addEventListener('input', function(e) {
                    // تحديث placeholder ديناميكياً
                    updatePlaceholder(e.target);
                    instantSearch();
                });

                // البحث عند الضغط على Enter
                nameInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        if (searchTimeout) clearTimeout(searchTimeout);
                        hideSearchIndicators();
                        searchItems();
                    }
                });

                // تأثيرات التركيز
                nameInput.addEventListener('focus', function() {
                    this.style.borderColor = '#0d6efd';
                    this.style.boxShadow = '0 0 0 0.2rem rgba(13, 110, 253, 0.25)';
                });

                nameInput.addEventListener('blur', function() {
                    this.style.borderColor = '';
                    this.style.boxShadow = '';
                });
            }

            console.log('✅ تم تفعيل البحث الفوري مع تحسينات UX');
        }

        // تحديث placeholder ديناميكياً
        function updatePlaceholder(input) {
            const originalPlaceholder = input.getAttribute('data-original-placeholder') || input.placeholder;

            if (!input.getAttribute('data-original-placeholder')) {
                input.setAttribute('data-original-placeholder', input.placeholder);
            }

            if (input.value.length > 0) {
                input.placeholder = 'جاري البحث الفوري...';
            } else {
                input.placeholder = originalPlaceholder;
            }
        }
        
        // عرض النتائج
        function displayResults(items) {
            const resultsDiv = document.getElementById('searchResults');
            
            if (!items || items.length === 0) {
                resultsDiv.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        لم يتم العثور على أصناف تطابق معايير البحث
                    </div>
                `;
                return;
            }
            
            let html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-success">
                            <tr>
                                <th><i class="fas fa-barcode me-1"></i>كود الصنف</th>
                                <th><i class="fas fa-tag me-1"></i>اسم الصنف</th>
                                <th><i class="fas fa-cube me-1"></i>الوحدة</th>
                                <th><i class="fas fa-hand-pointer me-1"></i>الإجراء</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            items.forEach(item => {
                html += `
                    <tr onclick="selectItem('${item.code}', '${item.name}', '${item.unit}')">
                        <td><strong>${item.code}</strong></td>
                        <td>${item.name}</td>
                        <td><span class="badge bg-secondary">${item.unit}</span></td>
                        <td>
                            <button type="button" class="btn btn-select"
                                    onclick="selectItem('${item.code}', '${item.name}', '${item.unit}')">
                                <i class="fas fa-check me-1"></i>اختيار
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            html += `
                        </tbody>
                    </table>
                </div>
                <div class="mt-3 text-center">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        تم العثور على ${items.length} صنف. اضغط على الصف أو زر "اختيار" لتحديد الصنف.
                        <br>
                        <i class="fas fa-lightbulb me-1"></i>
                        يمكنك استخدام حقول البحث أعلاه لتصفية النتائج.
                    </small>
                </div>
            `;
            
            resultsDiv.innerHTML = html;
        }
        
        // اختيار صنف
        function selectItem(code, name, unit) {
            // إرسال البيانات للنافذة الأصلية
            if (window.opener) {
                window.opener.postMessage({
                    type: 'itemSelected',
                    code: code,
                    name: name,
                    unit: unit
                }, window.location.origin);
            }
            
            // إغلاق النافذة
            window.close();
        }
        
        // مسح البحث
        function clearSearch() {
            document.getElementById('searchItemCode').value = '';
            document.getElementById('searchItemName').value = '';

            // عرض النتائج مرة أخرى بعد المسح
            searchItems();

            document.getElementById('searchItemCode').focus();
        }
        
        // تركيز على حقل البحث وعرض النتائج عند تحميل الصفحة
        window.onload = function() {
            // تفعيل البحث الفوري
            initInstantSearch();

            // تركيز على حقل البحث
            document.getElementById('searchItemCode').focus();

            // عرض النتائج مباشرة عند فتح النافذة
            searchItems();
        };
    </script>
</body>
</html>
