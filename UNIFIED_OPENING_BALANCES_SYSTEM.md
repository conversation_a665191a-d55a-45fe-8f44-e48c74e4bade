# 🎯 نظام الأرصدة الافتتاحية الموحدة
# Unified Opening Balances System

## 📋 نظرة عامة

تم إنشاء نظام الأرصدة الافتتاحية الموحدة كجزء من قسم التحليلات، وهو يوفر واجهة موحدة لإدارة الأرصدة الافتتاحية لجميع أنواع الكيانات باستخدام النظام المحاسبي الموحد الجديد.

## ✨ المميزات الرئيسية

### 🔧 التقنيات المستخدمة
- **النظام المحاسبي الموحد**: يستخدم `OB_PKG` للعمليات المحاسبية
- **دعم الفروع المتعددة**: حقل `BRANCH_ID` لدعم الفروع
- **دعم أنواع الكيانات المتعددة**: يدعم جميع أنواع الكيانات من جدول `ENTITY_TYPES`
- **دعم العملات المتعددة**: يدعم جميع العملات المتاحة
- **واجهة حديثة**: تصميم متجاوب مع Bootstrap 5

### 🎨 واجهة المستخدم
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **فلترة متقدمة**: فلترة حسب نوع الكيان، العملة، الفرع
- **جدول تفاعلي**: DataTable مع إمكانيات البحث والتصدير
- **نوافذ منبثقة**: لإضافة وتعديل الأرصدة
- **إشعارات فورية**: تأكيدات العمليات والأخطاء

## 🏗️ البنية التقنية

### 📁 الملفات المنشأة

#### Backend (Python/Flask)
```
app/analytics/
├── __init__.py              # Blueprint التحليلات
├── routes.py                # Routes وAPI endpoints
```

#### Frontend (HTML/CSS/JS)
```
app/templates/analytics/
├── opening_balances.html    # القالب الرئيسي

app/static/js/
├── analytics_opening_balances.js  # JavaScript المخصص
```

#### اختبار النظام
```
test_opening_balances_system.py     # اختبار شامل للنظام
UNIFIED_OPENING_BALANCES_SYSTEM.md  # هذا الملف
```

### 🔗 API Endpoints

| Method | Endpoint | الوصف |
|--------|----------|-------|
| `GET` | `/analytics/opening-balances` | صفحة النظام الرئيسية |
| `GET` | `/analytics/api/opening-balances` | جلب الأرصدة الافتتاحية |
| `POST` | `/analytics/api/opening-balances` | إنشاء رصيد افتتاحي جديد |
| `PUT` | `/analytics/api/opening-balances/<id>` | تحديث رصيد افتتاحي |
| `DELETE` | `/analytics/api/opening-balances/<id>` | حذف رصيد افتتاحي |
| `GET` | `/analytics/api/entity-types` | جلب أنواع الكيانات |
| `GET` | `/analytics/api/currencies` | جلب العملات |
| `GET` | `/analytics/api/branches` | جلب الفروع |

## 🚀 كيفية الاستخدام

### 1️⃣ الوصول إلى النظام
```
http://localhost:5000/analytics/opening-balances
```

### 2️⃣ الفلترة والبحث
- **نوع الكيان**: اختر من القائمة المنسدلة
- **معرف الكيان**: أدخل رقم الكيان
- **العملة**: اختر العملة المطلوبة
- **الفرع**: اختر الفرع المطلوب

### 3️⃣ إضافة رصيد افتتاحي جديد
1. انقر على زر "إضافة رصيد افتتاحي"
2. املأ البيانات المطلوبة:
   - نوع الكيان (مطلوب)
   - معرف الكيان (مطلوب)
   - العملة (مطلوب)
   - الفرع (مطلوب)
   - مبلغ الرصيد (مطلوب)
3. انقر "حفظ"

### 4️⃣ تعديل رصيد موجود
1. انقر على زر "تعديل" بجانب الرصيد
2. عدّل البيانات المطلوبة
3. انقر "حفظ"

### 5️⃣ حذف رصيد
1. انقر على زر "حذف" بجانب الرصيد
2. أكد الحذف في النافذة المنبثقة

## 🔧 التكامل مع النظام المحاسبي

### استخدام OB_PKG
النظام يستخدم `OB_PKG` للعمليات المحاسبية:

```sql
-- إدراج رصيد افتتاحي
OB_PKG.INSERT_BAL(
    p_ent_type => 'SUPPLIER',
    p_ent_id => 123,
    p_curr => 'USD',
    p_amount => 10000,
    p_branch => 1,
    p_user => 1
);

-- قراءة رصيد
SELECT OB_PKG.GET_BAL('SUPPLIER', 123, 'USD', 1) FROM DUAL;
```

### دعم الفروع المتعددة
```sql
-- رصيد لفرع محدد
WHERE branch_id = 1

-- رصيد لجميع الفروع
WHERE branch_id IS NOT NULL
```

### دعم أنواع الكيانات المتعددة
- `SUPPLIER` - الموردين
- `CUSTOMER` - العملاء
- `PURCHASE_AGENT` - مندوبي المشتريات
- `SALES_AGENT` - مندوبي المبيعات
- `SHIPPING_COMPANY` - شركات الشحن
- `MONEY_CHANGER` - الصرافين

## 🧪 اختبار النظام

### تشغيل الاختبار الشامل
```bash
python test_opening_balances_system.py
```

### الاختبارات المشمولة
1. **الاتصال بقاعدة البيانات**
2. **وجود OB_PKG ووظائفه**
3. **أنواع الكيانات الجديدة**
4. **بنية جدول BALANCE_TRANSACTIONS**
5. **الـ Views الجديدة**
6. **البيانات التجريبية**
7. **وظائف OB_PKG العملية**

## 📊 التقارير والإحصائيات

### البيانات المعروضة
- **نوع الكيان**: مع شارة ملونة
- **معرف الكيان**: رقم الكيان
- **العملة**: مع شارة ملونة
- **الفرع**: مع شارة ملونة
- **الرصيد**: مع تلوين حسب النوع (مدين/دائن)
- **الرصيد بالعملة الأساسية**: للمقارنة
- **نوع الرصيد**: مدين/دائن/متوازن
- **عدد المعاملات**: إجمالي المعاملات
- **آخر معاملة**: تاريخ آخر معاملة

### إمكانيات التصدير
- **Excel**: تصدير البيانات إلى Excel
- **PDF**: تصدير التقرير إلى PDF
- **طباعة**: طباعة التقرير

## 🔒 الأمان والصلاحيات

### متطلبات الوصول
- **تسجيل الدخول**: مطلوب للوصول إلى النظام
- **صلاحيات المستخدم**: حسب نظام الصلاحيات الموجود

### حماية البيانات
- **التحقق من صحة البيانات**: في الواجهة والخادم
- **معالجة الأخطاء**: رسائل واضحة ومفيدة
- **تسجيل العمليات**: جميع العمليات مسجلة في قاعدة البيانات

## 🛠️ الصيانة والتطوير

### إضافة نوع كيان جديد
1. أضف النوع إلى جدول `ENTITY_TYPES`
2. النظام سيتعرف عليه تلقائياً

### إضافة عملة جديدة
1. أضف العملة إلى جدول `CURRENCIES` (إذا كان موجود)
2. أو عدّل الكود في `api_currencies()` في routes.py

### إضافة فرع جديد
1. أضف الفرع إلى جدول `BRANCHES` (إذا كان موجود)
2. أو عدّل الكود في `api_branches()` في routes.py

## 📈 الأداء والتحسينات

### الفهارس المستخدمة
- فهارس على `entity_type_code`, `entity_id`, `currency_code`, `branch_id`
- فهارس على `document_date` للتقارير الزمنية

### التحسينات المطبقة
- **تحميل البيانات بشكل تدريجي**
- **فلترة من جانب الخادم**
- **ذاكرة التخزين المؤقت للقوائم**
- **ضغط البيانات المرسلة**

## 🔮 التطوير المستقبلي

### المميزات المخططة
- **تقارير متقدمة**: رسوم بيانية وتحليلات
- **استيراد من Excel**: استيراد الأرصدة من ملفات Excel
- **تصدير متقدم**: تصدير مخصص حسب الحاجة
- **إشعارات**: تنبيهات للأرصدة غير المتوازنة
- **تدقيق**: سجل مفصل للتغييرات

### التحسينات التقنية
- **API متقدم**: RESTful API كامل
- **واجهة محمولة**: تطبيق للهواتف الذكية
- **تكامل أوسع**: مع أنظمة أخرى في الشركة

## 📞 الدعم والمساعدة

### في حالة وجود مشاكل
1. **تشغيل الاختبار**: `python test_opening_balances_system.py`
2. **فحص السجلات**: في ملفات log الخادم
3. **التحقق من قاعدة البيانات**: التأكد من وجود الجداول والـ Packages

### الاتصال
- **المطور**: فريق التطوير
- **التاريخ**: 2025-09-08
- **الإصدار**: 1.0.0

---

## 🎉 خلاصة

تم إنشاء نظام الأرصدة الافتتاحية الموحدة بنجاح كجزء من قسم التحليلات. النظام يوفر:

✅ **واجهة موحدة** لجميع أنواع الكيانات  
✅ **تكامل كامل** مع النظام المحاسبي الموحد  
✅ **دعم الفروع والعملات المتعددة**  
✅ **تصميم حديث ومتجاوب**  
✅ **أمان وموثوقية عالية**  

**النظام جاهز للاستخدام الإنتاجي!** 🚀
