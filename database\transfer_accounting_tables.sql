-- =====================================================
-- جداول نظام الترحيل المحاسبي للحوالات
-- Transfer Accounting Tables
-- =====================================================

-- 1. جدول تفاصيل توزيع الحوالات على الموردين
CREATE TABLE transfer_supplier_distributions (
    id NUMBER PRIMARY KEY,
    transfer_id NUMBER NOT NULL,
    supplier_id NUMBER NOT NULL,
    amount NUMBER(15,2) NOT NULL,
    currency_code VARCHAR2(10) NOT NULL DEFAULT 'SAR',
    exchange_rate NUMBER(15,6) DEFAULT 1.000000,
    base_currency_amount NUMBER(15,2) GENERATED ALWAYS AS (amount * exchange_rate),
    
    -- معلومات التتبع
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_at TIMESTAMP,
    updated_by NUMBER,
    
    -- القيود
    CONSTRAINT fk_tsd_transfer FOREIGN KEY (transfer_id) REFERENCES transfers(id),
    CONSTRAINT fk_tsd_supplier FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    CONSTRAINT chk_tsd_amount CHECK (amount > 0),
    CONSTRAINT uk_tsd_unique UNIQUE (transfer_id, supplier_id)
);

-- 2. جدول سجل أنشطة الحوالات
CREATE TABLE transfer_activity_log (
    id NUMBER PRIMARY KEY,
    transfer_id NUMBER NOT NULL,
    activity_type VARCHAR2(50) NOT NULL, -- CREATED, APPROVED, EXECUTED, CANCELLED, ERROR
    description CLOB,
    
    -- بيانات إضافية
    old_status VARCHAR2(50),
    new_status VARCHAR2(50),
    amount_before NUMBER(15,2),
    amount_after NUMBER(15,2),
    
    -- معلومات التتبع
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    ip_address VARCHAR2(45),
    user_agent VARCHAR2(500),
    
    -- القيود
    CONSTRAINT fk_tal_transfer FOREIGN KEY (transfer_id) REFERENCES transfers(id),
    CONSTRAINT chk_tal_activity_type CHECK (activity_type IN (
        'CREATED', 'APPROVED', 'REJECTED', 'EXECUTED', 'CANCELLED', 'ERROR', 'MODIFIED'
    ))
);

-- 3. جدول ملخص الحوالات اليومي
CREATE TABLE transfer_daily_summary (
    id NUMBER PRIMARY KEY,
    summary_date DATE NOT NULL,
    currency_code VARCHAR2(10) NOT NULL,
    
    -- إحصائيات الحوالات
    total_transfers_count NUMBER DEFAULT 0,
    approved_transfers_count NUMBER DEFAULT 0,
    executed_transfers_count NUMBER DEFAULT 0,
    cancelled_transfers_count NUMBER DEFAULT 0,
    
    -- إحصائيات المبالغ
    total_amount NUMBER(15,2) DEFAULT 0,
    executed_amount NUMBER(15,2) DEFAULT 0,
    cancelled_amount NUMBER(15,2) DEFAULT 0,
    
    -- إحصائيات الموردين
    unique_suppliers_count NUMBER DEFAULT 0,
    total_supplier_distributions NUMBER DEFAULT 0,
    
    -- إحصائيات الصرافين
    unique_money_changers_count NUMBER DEFAULT 0,
    
    -- معلومات التحديث
    last_calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    calculated_by NUMBER,
    
    -- القيود
    CONSTRAINT uk_tds_unique UNIQUE (summary_date, currency_code)
);

-- 4. جدول تدقيق الأرصدة
CREATE TABLE balance_audit_trail (
    id NUMBER PRIMARY KEY,
    entity_type VARCHAR2(50) NOT NULL,
    entity_id NUMBER NOT NULL,
    currency_code VARCHAR2(10) NOT NULL,
    
    -- الأرصدة قبل وبعد
    balance_before NUMBER(15,2),
    balance_after NUMBER(15,2),
    change_amount NUMBER(15,2),
    change_type VARCHAR2(20), -- DEBIT, CREDIT
    
    -- معلومات المستند
    document_type VARCHAR2(50),
    document_number VARCHAR2(50),
    document_date DATE,
    description VARCHAR2(500),
    
    -- معلومات التتبع
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    
    -- القيود
    CONSTRAINT chk_bat_entity_type CHECK (entity_type IN ('SUPPLIER', 'MONEY_CHANGER', 'BANK')),
    CONSTRAINT chk_bat_change_type CHECK (change_type IN ('DEBIT', 'CREDIT'))
);

-- إنشاء Sequences
CREATE SEQUENCE transfer_supplier_distributions_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE transfer_activity_log_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE transfer_daily_summary_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE balance_audit_trail_seq START WITH 1 INCREMENT BY 1;

-- إنشاء Triggers للـ IDs التلقائية
CREATE OR REPLACE TRIGGER tsd_id_trigger
    BEFORE INSERT ON transfer_supplier_distributions
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := transfer_supplier_distributions_seq.NEXTVAL;
    END IF;
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER tal_id_trigger
    BEFORE INSERT ON transfer_activity_log
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := transfer_activity_log_seq.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER tds_id_trigger
    BEFORE INSERT ON transfer_daily_summary
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := transfer_daily_summary_seq.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER bat_id_trigger
    BEFORE INSERT ON balance_audit_trail
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := balance_audit_trail_seq.NEXTVAL;
    END IF;
END;
/

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_tsd_transfer_id ON transfer_supplier_distributions(transfer_id);
CREATE INDEX idx_tsd_supplier_id ON transfer_supplier_distributions(supplier_id);
CREATE INDEX idx_tsd_currency ON transfer_supplier_distributions(currency_code);

CREATE INDEX idx_tal_transfer_id ON transfer_activity_log(transfer_id);
CREATE INDEX idx_tal_activity_type ON transfer_activity_log(activity_type);
CREATE INDEX idx_tal_created_at ON transfer_activity_log(created_at);

CREATE INDEX idx_tds_date ON transfer_daily_summary(summary_date);
CREATE INDEX idx_tds_currency ON transfer_daily_summary(currency_code);

CREATE INDEX idx_bat_entity ON balance_audit_trail(entity_type, entity_id);
CREATE INDEX idx_bat_currency ON balance_audit_trail(currency_code);
CREATE INDEX idx_bat_document ON balance_audit_trail(document_type, document_number);
CREATE INDEX idx_bat_created_at ON balance_audit_trail(created_at);

-- إنشاء Views للاستعلامات الشائعة

-- 1. View لملخص الحوالات حسب المورد
CREATE OR REPLACE VIEW supplier_transfers_summary AS
SELECT 
    s.id as supplier_id,
    s.name as supplier_name,
    tsd.currency_code,
    COUNT(DISTINCT tsd.transfer_id) as transfers_count,
    SUM(tsd.amount) as total_amount,
    AVG(tsd.amount) as average_amount,
    MIN(t.executed_at) as first_transfer_date,
    MAX(t.executed_at) as last_transfer_date
FROM transfer_supplier_distributions tsd
JOIN transfers t ON tsd.transfer_id = t.id
JOIN suppliers s ON tsd.supplier_id = s.id
WHERE t.status = 'executed'
GROUP BY s.id, s.name, tsd.currency_code;

-- 2. View لملخص الحوالات حسب الصراف
CREATE OR REPLACE VIEW money_changer_transfers_summary AS
SELECT 
    mc.id as money_changer_id,
    mc.name as money_changer_name,
    t.currency,
    COUNT(*) as transfers_count,
    SUM(t.amount) as total_amount,
    AVG(t.amount) as average_amount,
    MIN(t.executed_at) as first_transfer_date,
    MAX(t.executed_at) as last_transfer_date
FROM transfers t
JOIN money_changers mc ON t.money_changer_id = mc.id
WHERE t.status = 'executed'
GROUP BY mc.id, mc.name, t.currency;

-- 3. View للأرصدة الحالية مع تفاصيل الكيان
CREATE OR REPLACE VIEW current_balances_detailed AS
SELECT 
    cb.*,
    CASE cb.entity_type_code
        WHEN 'SUPPLIER' THEN s.name
        WHEN 'MONEY_CHANGER' THEN mc.name
        WHEN 'BANK' THEN b.name
    END as entity_name,
    CASE cb.entity_type_code
        WHEN 'SUPPLIER' THEN s.contact_person
        WHEN 'MONEY_CHANGER' THEN mc.contact_person
        WHEN 'BANK' THEN b.contact_person
    END as contact_person
FROM CURRENT_BALANCES cb
LEFT JOIN suppliers s ON cb.entity_type_code = 'SUPPLIER' AND cb.entity_id = s.id
LEFT JOIN money_changers mc ON cb.entity_type_code = 'MONEY_CHANGER' AND cb.entity_id = mc.id
LEFT JOIN banks b ON cb.entity_type_code = 'BANK' AND cb.entity_id = b.id;
