{% extends "base.html" %}

{% block title %}إدارة حالات الشحنة{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-tasks me-2 text-success"></i>
                        إدارة حالات الشحنة
                    </h2>
                    <p class="text-muted mb-0">إدارة وتحديث حالات الشحنات بذكاء اصطناعي</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" onclick="bulkUpdateStatus()">
                        <i class="fas fa-edit me-1"></i>
                        تحديث متعدد
                    </button>
                    <button class="btn btn-success" onclick="autoUpdateStatuses()">
                        <i class="fas fa-magic me-1"></i>
                        تحديث ذكي
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إعدادات الحالات -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات الحالات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for status in statuses %}
                        <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
                            <div class="card border h-100" style="border-color: {{ status[3] }}!important;">
                                <div class="card-body text-center p-3">
                                    <div class="mb-2">
                                        <i class="{{ status[4] }} fa-2x" style="color: {{ status[3] }}"></i>
                                    </div>
                                    <h6 class="mb-1">{{ status[1] }}</h6>
                                    <small class="text-muted">{{ status[2] }}</small>
                                    <div class="mt-2">
                                        {% if status[6] == 1 %}
                                        <span class="badge bg-success">نشط</span>
                                        {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                        {% endif %}
                                        
                                        {% if status[7] == 1 %}
                                        <span class="badge bg-info">إشعارات</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الشحنات القابلة للتحديث -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            الشحنات القابلة للتحديث
                        </h5>
                        <div>
                            <input type="text" class="form-control form-control-sm" 
                                   placeholder="البحث..." id="searchInput" style="width: 200px;">
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="shipmentsTable">
                            <thead class="table-light">
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>رقم التتبع</th>
                                    <th>المرسل</th>
                                    <th>المستلم</th>
                                    <th>الحالة الحالية</th>
                                    <th>آخر تحديث</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for shipment in shipments %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="shipment-checkbox" value="{{ shipment[0] }}">
                                    </td>
                                    <td>
                                        <strong>{{ shipment[1] }}</strong>
                                    </td>
                                    <td>{{ shipment[2] or 'غير محدد' }}</td>
                                    <td>{{ shipment[3] or 'غير محدد' }}</td>
                                    <td>
                                        <span class="badge" style="background-color: {{ shipment[6] }}; color: white;">
                                            {{ shipment[5] }}
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ shipment[7].strftime('%Y-%m-%d %H:%M') if shipment[7] else 'غير محدد' }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary btn-sm" 
                                                    onclick="quickUpdate({{ shipment[0] }}, '{{ shipment[4] }}')">
                                                <i class="fas fa-edit"></i>
                                                تحديث
                                            </button>
                                            <a href="{{ url_for('shipments.status_timeline', shipment_id=shipment[0]) }}" 
                                               class="btn btn-outline-info btn-sm">
                                                <i class="fas fa-history"></i>
                                                التاريخ
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal التحديث السريع -->
<div class="modal fade" id="quickUpdateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث سريع للحالة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="quickUpdateForm">
                    <input type="hidden" id="quickShipmentId" name="shipment_id">
                    
                    <div class="mb-3">
                        <label class="form-label">الحالة الحالية</label>
                        <input type="text" class="form-control" id="currentStatus" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الحالة الجديدة</label>
                        <select class="form-select" name="new_status" required>
                            <option value="">اختر الحالة الجديدة</option>
                            {% for status in statuses %}
                            {% if status[6] == 1 %}
                            <option value="{{ status[0] }}" style="color: {{ status[3] }}">
                                {{ status[1] }}
                            </option>
                            {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">ملاحظات سريعة</label>
                        <textarea class="form-control" name="notes" rows="2" 
                                  placeholder="ملاحظات اختيارية..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="submitQuickUpdate()">
                    <i class="fas fa-save me-1"></i>
                    تحديث
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal التحديث المتعدد -->
<div class="modal fade" id="bulkUpdateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث متعدد للحالات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    سيتم تحديث <span id="selectedCount">0</span> شحنة محددة
                </div>
                
                <form id="bulkUpdateForm">
                    <div class="mb-3">
                        <label class="form-label">الحالة الجديدة</label>
                        <select class="form-select" name="new_status" required>
                            <option value="">اختر الحالة</option>
                            {% for status in statuses %}
                            {% if status[6] == 1 %}
                            <option value="{{ status[0] }}">{{ status[1] }}</option>
                            {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" name="notes" rows="3" 
                                  placeholder="ملاحظات للتحديث المتعدد..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="submitBulkUpdate()">
                    <i class="fas fa-edit me-1"></i>
                    تحديث الكل
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// البحث في الجدول
document.getElementById('searchInput').addEventListener('keyup', function() {
    const filter = this.value.toLowerCase();
    const rows = document.querySelectorAll('#shipmentsTable tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(filter) ? '' : 'none';
    });
});

// تحديد الكل
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.shipment-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSelectedCount();
}

// تحديث عدد المحدد
function updateSelectedCount() {
    const selected = document.querySelectorAll('.shipment-checkbox:checked').length;
    document.getElementById('selectedCount').textContent = selected;
}

// مراقبة تغيير التحديد
document.querySelectorAll('.shipment-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateSelectedCount);
});

// تحديث سريع
function quickUpdate(shipmentId, currentStatus) {
    document.getElementById('quickShipmentId').value = shipmentId;
    document.getElementById('currentStatus').value = currentStatus;
    new bootstrap.Modal(document.getElementById('quickUpdateModal')).show();
}

// إرسال التحديث السريع
function submitQuickUpdate() {
    const form = document.getElementById('quickUpdateForm');
    const formData = new FormData(form);
    
    fetch('/shipments/api/update-status', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم تحديث الحالة بنجاح');
            location.reload();
        } else {
            alert('خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في التحديث');
    });
}

// تحديث متعدد
function bulkUpdateStatus() {
    const selected = document.querySelectorAll('.shipment-checkbox:checked');
    if (selected.length === 0) {
        alert('يرجى تحديد شحنة واحدة على الأقل');
        return;
    }
    
    updateSelectedCount();
    new bootstrap.Modal(document.getElementById('bulkUpdateModal')).show();
}

// إرسال التحديث المتعدد
function submitBulkUpdate() {
    const selected = document.querySelectorAll('.shipment-checkbox:checked');
    const newStatus = document.querySelector('#bulkUpdateForm select[name="new_status"]').value;
    const notes = document.querySelector('#bulkUpdateForm textarea[name="notes"]').value;
    
    if (!newStatus) {
        alert('يرجى اختيار الحالة الجديدة');
        return;
    }
    
    const promises = Array.from(selected).map(checkbox => {
        const formData = new FormData();
        formData.append('shipment_id', checkbox.value);
        formData.append('new_status', newStatus);
        formData.append('notes', notes);
        
        return fetch('/shipments/api/update-status', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        });
    });
    
    Promise.all(promises)
        .then(() => {
            alert('تم تحديث جميع الشحنات المحددة بنجاح');
            location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في التحديث المتعدد');
        });
}

// تحديث ذكي
function autoUpdateStatuses() {
    alert('التحديث الذكي - قريباً\nسيقوم النظام بتحديث الحالات تلقائياً بناءً على التواريخ والقواعد المحددة');
}
</script>
{% endblock %}
