<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأتمتة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin: 50px auto;
            max-width: 800px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .test-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .log-entry {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
        }
        
        .log-entry.success {
            border-left-color: #28a745;
        }
        
        .log-entry.failed {
            border-left-color: #dc3545;
        }
        
        .log-entry.running {
            border-left-color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <div class="text-center mb-4">
                <h1 class="display-4">
                    <i class="fas fa-robot me-3"></i>
                    اختبار نظام الأتمتة
                </h1>
                <p class="lead text-muted">اختبر قواعد الأتمتة على الشحنات</p>
            </div>
            
            <!-- قسم اختبار الأتمتة -->
            <div class="test-card">
                <h3><i class="fas fa-play-circle me-2"></i>اختبار الأتمتة التلقائية</h3>
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="shipment_id" class="form-label">معرف الشحنة</label>
                            <input type="number" class="form-control" id="shipment_id" placeholder="أدخل معرف الشحنة" value="10">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="new_status" class="form-label">الحالة الجديدة</label>
                            <select class="form-select" id="new_status">
                                <option value="">اختر الحالة</option>
                                <option value="arrived_port">وصلت للميناء</option>
                                <option value="ready_pickup">جاهزة للاستلام</option>
                                <option value="delivered">تم التسليم</option>
                                <option value="in_transit">قيد الشحن</option>
                                <option value="customs_clearance">قيد التخليص</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-success w-100" onclick="updateStatusWithAutomation()">
                                <i class="fas fa-sync-alt me-2"></i>تحديث الحالة + أتمتة
                            </button>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <button type="button" class="btn btn-primary w-100" onclick="triggerAutomation()">
                            <i class="fas fa-rocket me-2"></i>تشغيل الأتمتة يدوياً
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button type="button" class="btn btn-info w-100" onclick="checkQueueStatus()">
                            <i class="fas fa-chart-bar me-2"></i>حالة طابور الأتمتة
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- قسم النتائج -->
            <div class="test-card" id="results_section" style="display: none;">
                <h3><i class="fas fa-chart-line me-2"></i>نتائج التنفيذ</h3>
                <div id="results_content"></div>
            </div>
            
            <!-- قسم سجل التنفيذ -->
            <div class="test-card" id="logs_section" style="display: none;">
                <h3><i class="fas fa-history me-2"></i>سجل تنفيذ الأتمتة</h3>
                <div id="logs_content"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updateStatusWithAutomation() {
            const shipmentId = document.getElementById('shipment_id').value;
            const newStatus = document.getElementById('new_status').value;

            if (!shipmentId) {
                alert('يرجى إدخال معرف الشحنة');
                return;
            }

            if (!newStatus) {
                alert('يرجى اختيار الحالة الجديدة');
                return;
            }

            const button = event.target;
            const originalContent = button.innerHTML;

            // تأثير التحميل
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...';
            button.disabled = true;

            // إرسال طلب تحديث الحالة مع الأتمتة
            fetch('/shipments/api/update-shipment-status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    shipment_id: parseInt(shipmentId),
                    new_status: newStatus
                })
            })
            .then(response => response.json())
            .then(data => {
                // عرض النتائج
                showResults(data);

                // جلب سجل التنفيذ
                if (data.success) {
                    setTimeout(() => {
                        loadExecutionLog(shipmentId);
                        checkQueueStatus();
                    }, 1000);
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                showResults({
                    success: false,
                    message: 'خطأ في الاتصال بالخادم'
                });
            })
            .finally(() => {
                // إعادة الزر لحالته الطبيعية
                button.innerHTML = originalContent;
                button.disabled = false;
            });
        }

        function checkQueueStatus() {
            fetch('/shipments/automation-api/queue-status')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const status = data.status;
                    const statusHtml = `
                        <div class="alert alert-info">
                            <h6><i class="fas fa-chart-bar me-2"></i>حالة طابور الأتمتة:</h6>
                            <div class="row text-center">
                                <div class="col-3">
                                    <strong>${status.total}</strong><br>
                                    <small>المجموع</small>
                                </div>
                                <div class="col-3">
                                    <strong class="text-warning">${status.pending}</strong><br>
                                    <small>في الانتظار</small>
                                </div>
                                <div class="col-3">
                                    <strong class="text-success">${status.completed}</strong><br>
                                    <small>مكتملة</small>
                                </div>
                                <div class="col-3">
                                    <strong class="text-danger">${status.failed}</strong><br>
                                    <small>فاشلة</small>
                                </div>
                            </div>
                            <div class="mt-2">
                                <small>حالة المعالج: ${status.running ? '<span class="text-success">يعمل</span>' : '<span class="text-danger">متوقف</span>'}</small>
                            </div>
                        </div>
                    `;

                    const resultsSection = document.getElementById('results_section');
                    const resultsContent = document.getElementById('results_content');

                    resultsContent.innerHTML = statusHtml;
                    resultsSection.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('خطأ في جلب حالة الطابور:', error);
            });
        }

        function triggerAutomation() {
            const shipmentId = document.getElementById('shipment_id').value;
            
            if (!shipmentId) {
                alert('يرجى إدخال معرف الشحنة');
                return;
            }
            
            const button = event.target;
            const originalContent = button.innerHTML;
            
            // تأثير التحميل
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التشغيل...';
            button.disabled = true;
            
            // إرسال طلب تشغيل الأتمتة
            fetch('/shipments/automation-api/trigger-manual', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    shipment_id: parseInt(shipmentId)
                })
            })
            .then(response => response.json())
            .then(data => {
                // عرض النتائج
                showResults(data);
                
                // جلب سجل التنفيذ
                if (data.success) {
                    loadExecutionLog(shipmentId);
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                showResults({
                    success: false,
                    message: 'خطأ في الاتصال بالخادم'
                });
            })
            .finally(() => {
                // إعادة الزر لحالته الطبيعية
                button.innerHTML = originalContent;
                button.disabled = false;
            });
        }
        
        function showResults(data) {
            const resultsSection = document.getElementById('results_section');
            const resultsContent = document.getElementById('results_content');
            
            let html = '';
            
            if (data.success) {
                html = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>نجح التشغيل!</strong><br>
                        ${data.message}
                    </div>
                `;
                
                if (data.status_change) {
                    html += `
                        <div class="alert alert-info">
                            <i class="fas fa-exchange-alt me-2"></i>
                            <strong>تغيير الحالة:</strong> ${data.status_change}
                        </div>
                    `;
                }
            } else {
                html = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>فشل التشغيل!</strong><br>
                        ${data.message}
                    </div>
                `;
            }
            
            resultsContent.innerHTML = html;
            resultsSection.style.display = 'block';
        }
        
        function loadExecutionLog(shipmentId) {
            fetch(`/shipments/automation-api/execution-log/${shipmentId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showExecutionLog(data.logs);
                }
            })
            .catch(error => {
                console.error('خطأ في جلب السجل:', error);
            });
        }
        
        function showExecutionLog(logs) {
            const logsSection = document.getElementById('logs_section');
            const logsContent = document.getElementById('logs_content');
            
            if (!logs || logs.length === 0) {
                logsContent.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i>
                        لا توجد سجلات تنفيذ لهذه الشحنة
                    </div>
                `;
            } else {
                let html = '';
                
                logs.forEach(log => {
                    const statusClass = log.execution_status.toLowerCase();
                    const statusIcon = getStatusIcon(log.execution_status);
                    
                    html += `
                        <div class="log-entry ${statusClass}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">
                                        ${statusIcon} ${log.rule_name || 'قاعدة غير معروفة'}
                                    </h6>
                                    <p class="mb-1">
                                        <strong>الإجراء:</strong> ${getActionDescription(log.action_type)}<br>
                                        <strong>الشرط:</strong> ${log.trigger_condition} = ${log.condition_value}
                                    </p>
                                    ${log.execution_result ? `<p class="mb-1 text-success">${log.execution_result}</p>` : ''}
                                    ${log.error_message ? `<p class="mb-1 text-danger">${log.error_message}</p>` : ''}
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">${log.executed_at}</small>
                                    ${log.execution_duration ? `<br><small class="text-muted">${log.execution_duration}ms</small>` : ''}
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                logsContent.innerHTML = html;
            }
            
            logsSection.style.display = 'block';
        }
        
        function getStatusIcon(status) {
            switch(status) {
                case 'SUCCESS': return '<i class="fas fa-check-circle text-success"></i>';
                case 'FAILED': return '<i class="fas fa-times-circle text-danger"></i>';
                case 'RUNNING': return '<i class="fas fa-spinner fa-spin text-warning"></i>';
                case 'PENDING': return '<i class="fas fa-clock text-info"></i>';
                default: return '<i class="fas fa-question-circle text-secondary"></i>';
            }
        }
        
        function getActionDescription(actionType) {
            const descriptions = {
                'CREATE_DELIVERY_ORDER_WITH_AGENT': 'إنشاء أمر تسليم وتعيين مخلص',
                'ASSIGN_AGENT': 'تعيين مخلص',
                'SEND_NOTIFICATION': 'إرسال إشعار',
                'UPDATE_RATINGS': 'تحديث التقييمات'
            };
            
            return descriptions[actionType] || actionType;
        }
    </script>
</body>
</html>
