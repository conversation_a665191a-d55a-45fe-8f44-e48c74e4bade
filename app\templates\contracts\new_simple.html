{% extends "base.html" %}

{% block extra_css %}
<!-- Handsontable CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/handsontable@12.4.0/dist/handsontable.full.min.css">

<style>
.contract-form {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.form-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.form-body {
    padding: 30px;
}

.section-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    margin: 20px -30px;
    font-weight: 600;
    font-size: 18px;
}

.table-container {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.btn-toolbar {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    border: none;
    transition: all 0.3s ease;
}

.btn-primary-toolbar {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.btn-success-toolbar {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
}

.btn-danger-toolbar {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.btn-info-toolbar {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

.handsontable {
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.handsontable .htCore {
    direction: rtl;
}
</style>
{% endblock %}

{% block content %}
<div class="contract-form">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-11">
                <div class="form-container">
                    <!-- رأس النموذج -->
                    <div class="form-header">
                        <h1><i class="fas fa-file-contract me-3"></i>إضافة عقد جديد</h1>
                        <p>تسجيل بيانات العقود مع الموردين وربطها بأوامر الشراء</p>
                    </div>

                    <!-- نموذج البيانات -->
                    <form id="contractForm" method="POST" action="{{ url_for('contracts.create_contract_with_table') }}">
                        <div class="form-body">
                            <!-- القسم الأول: البيانات الرئيسية -->
                            <div class="section-title">
                                <i class="fas fa-info-circle"></i>
                                البيانات الرئيسية
                            </div>

                            <div class="row">
                                <!-- رقم الفرع -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">رقم الفرع <span class="required">*</span></label>
                                        <select class="form-select" id="branch_id" name="branch_id" required>
                                            <option value="">اختر الفرع</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- رقم العقد -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">رقم العقد</label>
                                        <input type="text" class="form-control" id="contract_number" name="contract_number" 
                                               placeholder="سيتم توليده تلقائياً">
                                    </div>
                                </div>

                                <!-- التاريخ -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">التاريخ <span class="required">*</span></label>
                                        <input type="date" class="form-control" id="contract_date" name="contract_date" 
                                               value="{{ today }}" required>
                                    </div>
                                </div>
                            </div>

                            <!-- القسم الثاني: البيانات التفصيلية -->
                            <div class="section-title mt-4">
                                <i class="fas fa-table"></i>
                                البيانات التفصيلية - أصناف العقد
                            </div>

                            <!-- حاوي الجدول -->
                            <div class="table-container">
                                <!-- شريط أدوات الجدول -->
                                <div class="table-toolbar">
                                    <div class="toolbar-group">
                                        <button type="button" class="btn btn-toolbar btn-primary-toolbar" onclick="addTableRows(5)">
                                            <i class="fas fa-plus me-1"></i>إضافة 5 صفوف
                                        </button>
                                        <button type="button" class="btn btn-toolbar btn-success-toolbar" onclick="addTableRows(10)">
                                            <i class="fas fa-plus me-1"></i>إضافة 10 صفوف
                                        </button>
                                        <button type="button" class="btn btn-toolbar btn-danger-toolbar" onclick="deleteSelectedRows()">
                                            <i class="fas fa-trash me-1"></i>حذف المحدد
                                        </button>
                                        <button type="button" class="btn btn-toolbar btn-info-toolbar" onclick="clearSelectedCells()">
                                            <i class="fas fa-eraser me-1"></i>مسح المحدد
                                        </button>
                                    </div>
                                </div>

                                <!-- الجدول التفاعلي Handsontable -->
                                <div id="contractDetailsTable" style="height: 400px; width: 100%; direction: rtl;"></div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="form-actions">
                            <button type="submit" class="btn btn-action btn-save">
                                <i class="fas fa-save me-2"></i>حفظ العقد
                            </button>
                            <button type="button" class="btn btn-action btn-cancel" onclick="window.history.back()">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Handsontable JS -->
<script src="https://cdn.jsdelivr.net/npm/handsontable@12.4.0/dist/handsontable.full.min.js"></script>

<script>
// متغيرات عامة
let contractTable;

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء تحميل صفحة العقد الجديد...');
    
    // تحميل الفروع
    loadBranches();
    
    // انتظار تحميل Handsontable ثم تهيئة الجدول
    setTimeout(() => {
        if (typeof Handsontable !== 'undefined') {
            initializeContractTable();
        } else {
            console.error('❌ Handsontable غير محمل');
        }
    }, 1000);

    console.log('✅ تم تحميل الصفحة بنجاح');
});

// تحميل الفروع
function loadBranches() {
    $.ajax({
        url: '/contracts/api/branches',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const branchSelect = $('#branch_id');
                branchSelect.empty().append('<option value="">اختر الفرع</option>');

                response.branches.forEach(branch => {
                    branchSelect.append(`
                        <option value="${branch.branch_id}">${branch.branch_name}</option>
                    `);
                });
            }
        },
        error: function() {
            console.error('خطأ في تحميل الفروع');
        }
    });
}

// تهيئة جدول Handsontable
function initializeContractTable() {
    console.log('🔧 بدء تهيئة جدول Handsontable...');
    
    const container = document.getElementById('contractDetailsTable');
    if (!container) {
        console.error('❌ لم يتم العثور على عنصر الجدول');
        return;
    }

    // إنشاء بيانات أولية فارغة
    const initialData = [];
    for (let i = 0; i < 20; i++) {
        initialData.push(['', '', '', '', '', '', '', '', '', '', '', '']);
    }

    // إنشاء جدول Handsontable
    contractTable = new Handsontable(container, {
        data: initialData,
        colHeaders: [
            'كود الصنف',
            'اسم الصنف', 
            'الوحدة',
            'الكمية',
            'الكمية المجانية',
            'سعر الوحدة',
            'نسبة الخصم %',
            'مبلغ الضريبة',
            'الإجمالي',
            'تاريخ الإنتاج',
            'تاريخ الانتهاء',
            'ملاحظات'
        ],
        columns: [
            { type: 'text', width: 120 },                    // كود الصنف
            { type: 'text', width: 200, readOnly: true },    // اسم الصنف
            { type: 'text', width: 80, readOnly: true },     // الوحدة
            { type: 'numeric', width: 100 },                 // الكمية
            { type: 'numeric', width: 100 },                 // الكمية المجانية
            { type: 'numeric', width: 100 },                 // سعر الوحدة
            { type: 'numeric', width: 100 },                 // نسبة الخصم
            { type: 'numeric', width: 100 },                 // مبلغ الضريبة
            { type: 'numeric', width: 120, readOnly: true }, // الإجمالي
            { type: 'date', width: 120 },                    // تاريخ الإنتاج
            { type: 'date', width: 120 },                    // تاريخ الانتهاء
            { type: 'text', width: 150 }                     // ملاحظات
        ],
        rowHeaders: true,
        height: 400,
        licenseKey: 'non-commercial-and-evaluation',
        contextMenu: true,
        fillHandle: true,
        copyPaste: true,
        manualRowResize: true,
        manualColumnResize: true,
        stretchH: 'all'
    });

    console.log('✅ تم تهيئة جدول Handsontable بنجاح');
}

// إضافة صفوف جديدة لجدول Handsontable
function addTableRows(count) {
    if (contractTable) {
        const currentData = contractTable.getData();
        for (let i = 0; i < count; i++) {
            currentData.push(['', '', '', '', '', '', '', '', '', '', '', '']);
        }
        contractTable.loadData(currentData);
        console.log(`✅ تم إضافة ${count} صف جديد`);
    }
}

// حذف الصفوف المحددة
function deleteSelectedRows() {
    if (contractTable) {
        const selected = contractTable.getSelected();
        if (selected && selected.length > 0) {
            const [startRow, , endRow] = selected[0];
            contractTable.alter('remove_row', startRow, endRow - startRow + 1);
            console.log('✅ تم حذف الصفوف المحددة');
        } else {
            alert('يرجى تحديد الصفوف المراد حذفها');
        }
    }
}

// مسح الخلايا المحددة
function clearSelectedCells() {
    if (contractTable) {
        const selected = contractTable.getSelected();
        if (selected && selected.length > 0) {
            contractTable.setDataAtCell(selected[0][0], selected[0][1], '');
            console.log('✅ تم مسح الخلايا المحددة');
        }
    }
}
</script>
{% endblock %}
