#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
عرض توضيحي للنظام المحاسبي الموحد - التسميات المختصرة
Unified Accounting System Demo - Short Names
"""

import sys
import os
sys.path.insert(0, os.getcwd())

from oracle_manager import OracleManager

def demo_short_names_system():
    """عرض توضيحي للنظام بالتسميات المختصرة"""
    
    oracle = OracleManager()
    
    print("🎯 عرض توضيحي للنظام المحاسبي الموحد - التسميات المختصرة")
    print("=" * 80)
    
    # 1. إنشاء الأرصدة الافتتاحية بالتسميات المختصرة
    print("\n1️⃣ إنشاء الأرصدة الافتتاحية...")
    
    # رصيد افتتاحي لمورد
    try:
        opening_balance_query = """
        BEGIN
            OB_PKG.INSERT_BAL(
                p_ent_type => 'SUPPLIER',
                p_ent_id => 1,
                p_curr => 'USD',
                p_amount => 15000,
                p_branch => 1,
                p_year => 2025,
                p_user => 1
            );
        END;
        """
        
        oracle.execute_update(opening_balance_query)
        print("✅ تم إنشاء رصيد افتتاحي للمورد 1: 15,000 USD")
        
    except Exception as e:
        print(f"⚠️ رصيد المورد موجود مسبقاً: {str(e)}")
    
    # رصيد افتتاحي لصراف
    try:
        opening_balance_mc_query = """
        BEGIN
            OB_PKG.INSERT_BAL(
                p_ent_type => 'MONEY_CHANGER',
                p_ent_id => 2,
                p_curr => 'USD',
                p_amount => 75000,
                p_branch => 1,
                p_year => 2025,
                p_user => 1
            );
        END;
        """
        
        oracle.execute_update(opening_balance_mc_query)
        print("✅ تم إنشاء رصيد افتتاحي للصراف 2: 75,000 USD")
        
    except Exception as e:
        print(f"⚠️ رصيد الصراف موجود مسبقاً: {str(e)}")
    
    # 2. ترحيل معاملات جديدة بالتسميات المختصرة
    print("\n2️⃣ ترحيل معاملات جديدة...")
    
    # معاملة حوالة للمورد
    try:
        transfer_query = """
        BEGIN
            BT_PKG.POST_TXN(
                p_ent_type => 'SUPPLIER',
                p_ent_id => 1,
                p_doc_type => 'TRANSFER',
                p_doc_no => 'TRF-SHORT-001',
                p_doc_date => SYSDATE,
                p_curr => 'USD',
                p_dr => 3000,
                p_cr => 0,
                p_rate => 1,
                p_desc => 'Demo transfer with short names',
                p_branch => 1,
                p_user => 1
            );
        END;
        """
        
        oracle.execute_update(transfer_query)
        print("✅ تم ترحيل حوالة للمورد 1: +3,000 USD")
        
    except Exception as e:
        print(f"❌ خطأ في ترحيل الحوالة: {str(e)}")
    
    # معاملة خصم من الصراف
    try:
        debit_mc_query = """
        BEGIN
            BT_PKG.POST_TXN(
                p_ent_type => 'MONEY_CHANGER',
                p_ent_id => 2,
                p_doc_type => 'TRANSFER',
                p_doc_no => 'TRF-SHORT-001',
                p_doc_date => SYSDATE,
                p_curr => 'USD',
                p_dr => 0,
                p_cr => 3000,
                p_rate => 1,
                p_desc => 'Demo transfer with short names',
                p_branch => 1,
                p_user => 1
            );
        END;
        """
        
        oracle.execute_update(debit_mc_query)
        print("✅ تم خصم من الصراف 2: -3,000 USD")
        
    except Exception as e:
        print(f"❌ خطأ في خصم الصراف: {str(e)}")
    
    # 3. عرض الأرصدة الحالية بالتسميات المختصرة
    print("\n3️⃣ عرض الأرصدة الحالية...")
    
    # رصيد المورد
    supplier_balance_query = """
    SELECT BT_PKG.GET_BAL(
        p_ent_type => 'SUPPLIER',
        p_ent_id => 1,
        p_curr => 'USD',
        p_branch => 1,
        p_date => SYSDATE
    ) FROM DUAL
    """
    
    supplier_balance = oracle.execute_query(supplier_balance_query)
    if supplier_balance:
        print(f"💰 رصيد المورد 1: {supplier_balance[0][0]:,.2f} USD")
    
    # رصيد الصراف
    mc_balance_query = """
    SELECT BT_PKG.GET_BAL(
        p_ent_type => 'MONEY_CHANGER',
        p_ent_id => 2,
        p_curr => 'USD',
        p_branch => 1,
        p_date => SYSDATE
    ) FROM DUAL
    """
    
    mc_balance = oracle.execute_query(mc_balance_query)
    if mc_balance:
        print(f"💰 رصيد الصراف 2: {mc_balance[0][0]:,.2f} USD")
    
    # 4. استخدام الـ Views المختصرة
    print("\n4️⃣ استخدام الـ Views المختصرة...")
    
    # عرض الأرصدة الحالية من الـ View
    view_query = """
    SELECT ent_type, ent_id, curr, curr_bal, txn_count
    FROM V_CURR_BAL
    WHERE curr = 'USD'
    ORDER BY ent_type, ent_id
    """
    
    view_result = oracle.execute_query(view_query)
    if view_result:
        print("📊 الأرصدة من V_CURR_BAL:")
        for row in view_result:
            ent_type, ent_id, curr, curr_bal, txn_count = row
            print(f"   {ent_type} {ent_id}: {curr_bal:,.2f} {curr} ({txn_count} معاملات)")
    
    # 5. اختبار الرصيد الشهري
    print("\n5️⃣ اختبار الرصيد الشهري...")
    
    current_month = oracle.execute_query("SELECT EXTRACT(MONTH FROM SYSDATE) FROM DUAL")[0][0]
    current_year = oracle.execute_query("SELECT EXTRACT(YEAR FROM SYSDATE) FROM DUAL")[0][0]
    
    monthly_balance_query = f"""
    SELECT BT_PKG.GET_MONTH_BAL(
        p_ent_type => 'SUPPLIER',
        p_ent_id => 1,
        p_curr => 'USD',
        p_year => {current_year},
        p_month => {current_month},
        p_branch => 1
    ) FROM DUAL
    """
    
    monthly_balance = oracle.execute_query(monthly_balance_query)
    if monthly_balance:
        print(f"📅 رصيد المورد 1 لشهر {current_month}/{current_year}: {monthly_balance[0][0]:,.2f} USD")
    
    # 6. اختبار عكس المعاملة
    print("\n6️⃣ اختبار عكس المعاملة...")
    
    try:
        reverse_query = """
        BEGIN
            BT_PKG.REVERSE_TXN(
                p_orig_doc => 'TRF-SHORT-001',
                p_rev_doc => 'REV-SHORT-001',
                p_reason => 'Demo reversal with short names',
                p_user => 1
            );
        END;
        """
        
        oracle.execute_update(reverse_query)
        print("✅ تم عكس المعاملة TRF-SHORT-001")
        
        # عرض الأرصدة بعد العكس
        supplier_balance_after = oracle.execute_query(supplier_balance_query)
        mc_balance_after = oracle.execute_query(mc_balance_query)
        
        if supplier_balance_after and mc_balance_after:
            print(f"💰 رصيد المورد 1 بعد العكس: {supplier_balance_after[0][0]:,.2f} USD")
            print(f"💰 رصيد الصراف 2 بعد العكس: {mc_balance_after[0][0]:,.2f} USD")
        
    except Exception as e:
        print(f"❌ خطأ في عكس المعاملة: {str(e)}")

def show_naming_advantages():
    """عرض مزايا التسميات المختصرة"""
    
    print("\n🌟 مزايا التسميات المختصرة:")
    print("=" * 80)
    
    advantages = [
        "✅ متوافق مع جميع إصدارات Oracle (30 حرف كحد أقصى)",
        "⚡ سرعة في الكتابة والتطوير",
        "🎯 أسماء واضحة ومنطقية",
        "🔧 سهولة الصيانة والتطوير",
        "📝 تقليل الأخطاء الإملائية",
        "🎨 نمط موحد ومتسق",
        "🚀 أداء محسن في الاستعلامات",
        "🔄 سهولة التذكر والاستخدام",
        "📊 معايير ثابتة للفريق",
        "🛡️ تجنب مشاكل حدود Oracle"
    ]
    
    for advantage in advantages:
        print(f"   {advantage}")
    
    print("\n📋 أمثلة على الاختصارات:")
    print("   • BAL = Balance (رصيد)")
    print("   • TXN = Transaction (معاملة)")
    print("   • ENT = Entity (كيان)")
    print("   • CURR = Currency (عملة)")
    print("   • DOC = Document (مستند)")
    print("   • DR = Debit (مدين)")
    print("   • CR = Credit (دائن)")

def compare_old_vs_new():
    """مقارنة بين النظام القديم والجديد"""
    
    print("\n📊 مقارنة: النظام القديم vs الجديد")
    print("=" * 80)
    
    print("🔴 النظام القديم:")
    print("   • BALANCE_TRANSACTIONS_PKG.GET_CURRENT_BALANCE(...)")
    print("   • أسماء طويلة قد تسبب مشاكل")
    print("   • صعوبة في الكتابة والتذكر")
    
    print("\n🟢 النظام الجديد:")
    print("   • BT_PKG.GET_BAL(...)")
    print("   • أسماء مختصرة ومتوافقة")
    print("   • سهولة في الاستخدام")
    
    print("\n🎯 النتيجة: نظام أكثر كفاءة وأماناً!")

if __name__ == "__main__":
    try:
        demo_short_names_system()
        show_naming_advantages()
        compare_old_vs_new()
        
        print("\n🎉 انتهى العرض التوضيحي للتسميات المختصرة!")
        print("✅ النظام جاهز للتطبيق بأسماء متوافقة مع Oracle")
        
    except Exception as e:
        print(f"❌ خطأ في العرض التوضيحي: {str(e)}")
    
    print("\n" + "=" * 80)
    print("🏁 انتهى العرض")
