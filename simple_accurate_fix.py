#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح بسيط ودقيق لمشكلة تحديث حالة أوامر الشراء
"""

import sys
sys.path.append('.')
from oracle_manager import OracleManager

def fix_specific_issue():
    """إصلاح المشكلة المحددة مباشرة"""
    print('🔧 إصلاح المشكلة المحددة مباشرة...')

    try:
        oracle = OracleManager()
        
        # إصلاح PO-2025-0011 مباشرة
        print('🎯 إصلاح PO-2025-0011...')
        
        # التحقق من حالة الشحنة المرتبطة
        check_query = """
            SELECT 
                cs.tracking_number,
                cs.shipment_status,
                cs.status_updated_at,
                m.po_status
            FROM cargo_shipments cs
            JOIN po_shipment_status_map m ON cs.shipment_status = m.shipment_status
            WHERE cs.purchase_order_id = (
                SELECT ID FROM PURCHASE_ORDERS WHERE PO_NUMBER = 'PO-2025-0011'
            )
            AND m.auto_update = 1 AND m.is_active = 1
        """
        
        shipment_info = oracle.execute_query(check_query)
        
        if shipment_info:
            tracking, ship_status, updated_at, expected_po_status = shipment_info[0]
            print(f'   📦 الشحنة: {tracking}')
            print(f'   📊 حالة الشحنة: {ship_status}')
            print(f'   🎯 الحالة المتوقعة لأمر الشراء: {expected_po_status}')
            
            # تحديث أمر الشراء مباشرة
            update_query = """
                UPDATE PURCHASE_ORDERS
                SET STATUS = :1,
                    UPDATED_AT = SYSDATE
                WHERE PO_NUMBER = 'PO-2025-0011'
            """
            
            oracle.execute_update(update_query, [expected_po_status])
            
            # إضافة سجل في التاريخ
            po_id_query = "SELECT ID FROM PURCHASE_ORDERS WHERE PO_NUMBER = 'PO-2025-0011'"
            po_id_result = oracle.execute_query(po_id_query)
            po_id = po_id_result[0][0] if po_id_result else None
            
            if po_id:
                history_insert = """
                    INSERT INTO po_status_history (
                        id, po_id, old_status, new_status, 
                        shipment_id, shipment_status, change_reason, auto_updated
                    ) VALUES (
                        po_hist_seq.NEXTVAL, :1, 'قيد التنفيذ', :2,
                        (SELECT id FROM cargo_shipments WHERE tracking_number = :3), :4,
                        'إصلاح يدوي للمزامنة', 1
                    )
                """
                
                oracle.execute_update(history_insert, [po_id, expected_po_status, tracking, ship_status])
            
            print(f'   ✅ تم تحديث PO-2025-0011 إلى: {expected_po_status}')
        
        oracle.close()
        return True
        
    except Exception as e:
        print(f'❌ خطأ في الإصلاح: {e}')
        return False

def create_simple_accurate_function():
    """إنشاء دالة بسيطة ودقيقة"""
    print('\n🔧 إنشاء دالة بسيطة ودقيقة...')

    try:
        oracle = OracleManager()
        
        # حذف الدالة المعطلة
        try:
            oracle.execute_update("DROP FUNCTION sync_po_status_accurate")
            print('🗑️ تم حذف الدالة المعطلة')
        except:
            pass
        
        # إنشاء دالة بسيطة وفعالة
        simple_function_sql = """
        CREATE OR REPLACE FUNCTION update_po_from_shipments(p_po_id NUMBER)
        RETURN VARCHAR2
        IS
            v_new_status VARCHAR2(50);
            v_old_status VARCHAR2(50);
            v_count NUMBER;
            v_latest_ship_status VARCHAR2(50);
        BEGIN
            -- الحصول على الحالة الحالية
            SELECT STATUS INTO v_old_status
            FROM PURCHASE_ORDERS
            WHERE ID = p_po_id;
            
            -- عد الشحنات
            SELECT COUNT(*) INTO v_count
            FROM cargo_shipments
            WHERE purchase_order_id = p_po_id;
            
            IF v_count = 0 THEN
                -- لا توجد شحنات → مسودة
                v_new_status := 'مسودة';
                
                UPDATE PURCHASE_ORDERS
                SET STATUS = v_new_status,
                    IS_USED = 0,
                    USED_AT = NULL,
                    USED_IN_SHIPMENT_ID = NULL,
                    UPDATED_AT = SYSDATE
                WHERE ID = p_po_id;
                
            ELSE
                -- توجد شحنات → تحديد الحالة من أحدث شحنة
                SELECT cs.shipment_status INTO v_latest_ship_status
                FROM cargo_shipments cs
                WHERE cs.purchase_order_id = p_po_id
                ORDER BY cs.status_updated_at DESC, cs.created_at DESC
                FETCH FIRST 1 ROWS ONLY;
                
                -- تحديد حالة أمر الشراء بناءً على حالة الشحنة
                SELECT m.po_status INTO v_new_status
                FROM po_shipment_status_map m
                WHERE m.shipment_status = v_latest_ship_status
                AND m.auto_update = 1 AND m.is_active = 1;
                
                UPDATE PURCHASE_ORDERS
                SET STATUS = v_new_status,
                    IS_USED = 1,
                    USED_AT = (SELECT MIN(created_at) FROM cargo_shipments WHERE purchase_order_id = p_po_id),
                    USED_IN_SHIPMENT_ID = (SELECT MIN(id) FROM cargo_shipments WHERE purchase_order_id = p_po_id),
                    UPDATED_AT = SYSDATE
                WHERE ID = p_po_id;
            END IF;
            
            -- تسجيل التغيير إذا حدث
            IF v_old_status != v_new_status THEN
                INSERT INTO po_status_history (
                    id, po_id, old_status, new_status, 
                    shipment_id, shipment_status, change_reason, auto_updated
                ) VALUES (
                    po_hist_seq.NEXTVAL, p_po_id, v_old_status, v_new_status,
                    (SELECT id FROM cargo_shipments WHERE purchase_order_id = p_po_id AND shipment_status = v_latest_ship_status AND ROWNUM = 1),
                    v_latest_ship_status, 'تحديث تلقائي بسيط', 1
                );
            END IF;
            
            RETURN v_new_status;
            
        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                RETURN v_old_status;
            WHEN OTHERS THEN
                RETURN v_old_status;
        END;
        """
        
        oracle.execute_update(simple_function_sql)
        print('✅ تم إنشاء الدالة البسيطة')
        
        oracle.close()
        return True
        
    except Exception as e:
        print(f'❌ خطأ في إنشاء الدالة: {e}')
        return False

def update_triggers_simple():
    """تحديث الـ Triggers لاستخدام الدالة البسيطة"""
    print('\n🔄 تحديث الـ Triggers...')

    try:
        oracle = OracleManager()
        
        # Trigger للإدراج
        insert_trigger = """
        CREATE OR REPLACE TRIGGER trg_po_status_on_insert
            AFTER INSERT ON cargo_shipments
            FOR EACH ROW
        WHEN (NEW.purchase_order_id IS NOT NULL)
        DECLARE
            v_result VARCHAR2(50);
        BEGIN
            v_result := update_po_from_shipments(:NEW.purchase_order_id);
        EXCEPTION
            WHEN OTHERS THEN NULL;
        END;
        """
        
        # Trigger للتعديل
        update_trigger = """
        CREATE OR REPLACE TRIGGER trg_po_status_on_update
            AFTER UPDATE ON cargo_shipments
            FOR EACH ROW
        WHEN (NEW.purchase_order_id IS NOT NULL OR OLD.purchase_order_id IS NOT NULL)
        DECLARE
            v_result VARCHAR2(50);
        BEGIN
            IF :NEW.purchase_order_id IS NOT NULL THEN
                v_result := update_po_from_shipments(:NEW.purchase_order_id);
            END IF;
            
            IF :OLD.purchase_order_id IS NOT NULL AND 
               (:NEW.purchase_order_id IS NULL OR :NEW.purchase_order_id != :OLD.purchase_order_id) THEN
                v_result := update_po_from_shipments(:OLD.purchase_order_id);
            END IF;
        EXCEPTION
            WHEN OTHERS THEN NULL;
        END;
        """
        
        # Trigger للحذف
        delete_trigger = """
        CREATE OR REPLACE TRIGGER trg_po_status_on_delete
            AFTER DELETE ON cargo_shipments
            FOR EACH ROW
        WHEN (OLD.purchase_order_id IS NOT NULL)
        DECLARE
            v_result VARCHAR2(50);
        BEGIN
            v_result := update_po_from_shipments(:OLD.purchase_order_id);
        EXCEPTION
            WHEN OTHERS THEN NULL;
        END;
        """
        
        # تنفيذ الـ Triggers
        oracle.execute_update(insert_trigger)
        oracle.execute_update(update_trigger)
        oracle.execute_update(delete_trigger)
        
        print('✅ تم تحديث جميع الـ Triggers')
        
        oracle.close()
        return True
        
    except Exception as e:
        print(f'❌ خطأ في تحديث الـ Triggers: {e}')
        return False

def apply_fix_to_all():
    """تطبيق الإصلاح على جميع أوامر الشراء"""
    print('\n🔄 تطبيق الإصلاح على جميع أوامر الشراء...')

    try:
        oracle = OracleManager()
        
        # تطبيق الدالة على جميع أوامر الشراء
        all_pos = oracle.execute_query("SELECT ID, PO_NUMBER FROM PURCHASE_ORDERS ORDER BY ID")
        
        for po in all_pos:
            po_id, po_number = po
            
            try:
                result = oracle.execute_query(f"SELECT update_po_from_shipments({po_id}) FROM dual")
                if result:
                    new_status = result[0][0]
                    print(f'   ✅ {po_number}: {new_status}')
            except Exception as e:
                print(f'   ❌ {po_number}: خطأ - {e}')
        
        oracle.close()
        return True
        
    except Exception as e:
        print(f'❌ خطأ في التطبيق: {e}')
        return False

def final_check():
    """فحص نهائي"""
    print('\n🔍 فحص نهائي...')

    try:
        oracle = OracleManager()
        
        check_query = """
            SELECT 
                po.PO_NUMBER,
                po.STATUS,
                po.IS_USED,
                COUNT(cs.id) as shipments,
                MAX(cs.shipment_status) as latest_status
            FROM PURCHASE_ORDERS po
            LEFT JOIN cargo_shipments cs ON po.ID = cs.purchase_order_id
            GROUP BY po.ID, po.PO_NUMBER, po.STATUS, po.IS_USED
            ORDER BY po.PO_NUMBER
        """
        
        results = oracle.execute_query(check_query)
        
        print('📋 النتائج النهائية:')
        for result in results:
            po_number, status, is_used, shipments, latest_status = result
            print(f'   📦 {po_number}: {status}, مستخدم={is_used}, شحنات={shipments}')
            if latest_status:
                print(f'      آخر شحنة: {latest_status}')
        
        oracle.close()
        
    except Exception as e:
        print(f'❌ خطأ في الفحص: {e}')

if __name__ == '__main__':
    # إصلاح المشكلة المحددة
    fix_specific_issue()
    
    print('\n' + '='*50)
    # إنشاء دالة بسيطة
    create_simple_accurate_function()
    
    print('\n' + '='*50)
    # تحديث الـ Triggers
    update_triggers_simple()
    
    print('\n' + '='*50)
    # تطبيق الإصلاح
    apply_fix_to_all()
    
    print('\n' + '='*50)
    # فحص نهائي
    final_check()
