-- إعداد آمن لجداول المنافذ الجمركية
-- يتحقق من وجود الجداول قبل الإنشاء

-- التحقق من وجود الجداول وحذفها إذا كانت موجودة (اختياري)
-- DROP TABLE IF EXISTS customs_port_statistics CASCADE CONSTRAINTS;
-- DROP TABLE IF EXISTS customs_port_services CASCADE CONSTRAINTS;
-- DROP TABLE IF EXISTS customs_port_working_hours CASCADE CONSTRAINTS;
-- DROP TABLE IF EXISTS customs_port_fees CASCADE CONSTRAINTS;
-- DROP TABLE IF EXISTS customs_ports CASCADE CONSTRAINTS;

-- حذف التسلسلات إذا كانت موجودة
BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE customs_ports_seq';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE customs_port_fees_seq';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE customs_port_hours_seq';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE customs_port_services_seq';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE customs_port_stats_seq';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

-- إنشاء التسلسلات
CREATE SEQUENCE customs_ports_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE customs_port_fees_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE customs_port_hours_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE customs_port_services_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE customs_port_stats_seq START WITH 1 INCREMENT BY 1;

-- إنشاء جدول المنافذ الجمركية الرئيسي
CREATE TABLE customs_ports (
    id NUMBER PRIMARY KEY,
    port_code VARCHAR2(20) UNIQUE NOT NULL,
    port_name_ar VARCHAR2(200) NOT NULL,
    port_name_en VARCHAR2(200),
    port_type VARCHAR2(50) NOT NULL,
    country VARCHAR2(100) NOT NULL,
    city VARCHAR2(100) NOT NULL,
    region VARCHAR2(100),
    latitude NUMBER(10,8),
    longitude NUMBER(11,8),
    customs_authority VARCHAR2(200),
    contact_phone VARCHAR2(50),
    contact_email VARCHAR2(100),
    working_hours VARCHAR2(200),
    timezone VARCHAR2(50),
    is_active NUMBER(1) DEFAULT 1,
    is_24_hours NUMBER(1) DEFAULT 0,
    has_customs_clearance NUMBER(1) DEFAULT 1,
    has_quarantine NUMBER(1) DEFAULT 0,
    has_warehouse NUMBER(1) DEFAULT 0,
    max_container_size VARCHAR2(20),
    handling_capacity NUMBER(10,2),
    storage_capacity NUMBER(10,2),
    notes CLOB,
    created_at DATE DEFAULT SYSDATE,
    updated_at DATE DEFAULT SYSDATE,
    created_by NUMBER,
    updated_by NUMBER
);

-- إنشاء الفهارس الآمنة
BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX idx_customs_ports_type ON customs_ports(port_type)';
EXCEPTION
    WHEN OTHERS THEN 
        IF SQLCODE != -955 THEN -- ORA-00955: name is already used by an existing object
            RAISE;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX idx_customs_ports_country ON customs_ports(country)';
EXCEPTION
    WHEN OTHERS THEN 
        IF SQLCODE != -955 THEN
            RAISE;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX idx_customs_ports_active ON customs_ports(is_active)';
EXCEPTION
    WHEN OTHERS THEN 
        IF SQLCODE != -955 THEN
            RAISE;
        END IF;
END;
/

-- إدراج البيانات التجريبية
INSERT INTO customs_ports (
    id, port_code, port_name_ar, port_name_en, port_type, 
    country, city, region, customs_authority, contact_phone, 
    is_active, is_24_hours, has_customs_clearance, has_quarantine, has_warehouse
) VALUES (
    customs_ports_seq.NEXTVAL, 'JEDDAH_SEA', 'ميناء جدة الإسلامي', 'Jeddah Islamic Port', 'SEA',
    'السعودية', 'جدة', 'مكة المكرمة', 'الهيئة العامة للجمارك', '+966-12-6361000',
    1, 1, 1, 1, 1
);

INSERT INTO customs_ports (
    id, port_code, port_name_ar, port_name_en, port_type, 
    country, city, region, customs_authority, contact_phone, 
    is_active, is_24_hours, has_customs_clearance, has_quarantine, has_warehouse
) VALUES (
    customs_ports_seq.NEXTVAL, 'DAMMAM_SEA', 'ميناء الملك عبدالعزيز', 'King Abdulaziz Port', 'SEA',
    'السعودية', 'الدمام', 'المنطقة الشرقية', 'الهيئة العامة للجمارك', '+966-13-8576000',
    1, 1, 1, 1, 1
);

INSERT INTO customs_ports (
    id, port_code, port_name_ar, port_name_en, port_type, 
    country, city, region, customs_authority, contact_phone, 
    is_active, is_24_hours, has_customs_clearance, has_quarantine, has_warehouse
) VALUES (
    customs_ports_seq.NEXTVAL, 'RIYADH_AIR', 'مطار الملك خالد الدولي', 'King Khalid International Airport', 'AIR',
    'السعودية', 'الرياض', 'الرياض', 'الهيئة العامة للجمارك', '+966-11-2211000',
    1, 1, 1, 1, 1
);

INSERT INTO customs_ports (
    id, port_code, port_name_ar, port_name_en, port_type, 
    country, city, region, customs_authority, contact_phone, 
    is_active, is_24_hours, has_customs_clearance, has_quarantine, has_warehouse
) VALUES (
    customs_ports_seq.NEXTVAL, 'JEDDAH_AIR', 'مطار الملك عبدالعزيز الدولي', 'King Abdulaziz International Airport', 'AIR',
    'السعودية', 'جدة', 'مكة المكرمة', 'الهيئة العامة للجمارك', '+966-12-6844000',
    1, 1, 1, 1, 1
);

INSERT INTO customs_ports (
    id, port_code, port_name_ar, port_name_en, port_type, 
    country, city, region, customs_authority, contact_phone, 
    is_active, is_24_hours, has_customs_clearance, has_quarantine, has_warehouse
) VALUES (
    customs_ports_seq.NEXTVAL, 'HADITHA_LAND', 'منفذ الحديثة البري', 'Haditha Land Port', 'LAND',
    'السعودية', 'الحديثة', 'الحدود الشمالية', 'الهيئة العامة للجمارك', '+966-14-6221000',
    1, 0, 1, 0, 1
);

COMMIT;

-- رسالة تأكيد
SELECT 'تم إنشاء جداول المنافذ الجمركية بنجاح!' as status FROM dual;
SELECT COUNT(*) as total_ports FROM customs_ports;
