{% extends "base.html" %}

{% block title %}معاينة أمر الشراء{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-eye me-2"></i>
                        معاينة أمر الشراء - {{ purchase_order[1] }}
                    </h3>
                    <div class="btn-group">
                        <a href="{{ url_for('purchase_orders.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            العودة للقائمة
                        </a>
                        <a href="{{ url_for('purchase_orders.edit', po_id=purchase_order[0]) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>
                            تعديل
                        </a>
                        <button type="button" class="btn btn-success" onclick="printPO()">
                            <i class="fas fa-print me-2"></i>
                            طباعة
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    <!-- معلومات أمر الشراء الأساسية -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات أساسية</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>رقم أمر الشراء:</strong></td>
                                            <td>{{ purchase_order[1] }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ الأمر:</strong></td>
                                            <td>{{ purchase_order[5] }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ التسليم:</strong></td>
                                            <td>{{ purchase_order[6] or 'غير محدد' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحالة:</strong></td>
                                            <td>
                                                <span class="badge bg-info">{{ purchase_order[8] or 'مسودة' }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>العملة:</strong></td>
                                            <td>
                                                {% if purchase_order[30] and purchase_order[31] %}
                                                    <span class="badge bg-secondary">
                                                        {{ purchase_order[30] }} - {{ purchase_order[31] }}
                                                    </span>
                                                {% else %}
                                                    <span class="badge bg-secondary">ريال - ريال سعودي</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>الفرع:</strong></td>
                                            <td>
                                                {% if purchase_order|length > 33 and purchase_order[33] %}
                                                    <span class="badge bg-primary">
                                                        <i class="fas fa-building me-1"></i>
                                                        {{ purchase_order[33] }}
                                                    </span>
                                                    <small class="text-muted d-block mt-1">
                                                        رقم الفرع: {{ purchase_order[32] if purchase_order|length > 32 else 'غير محدد' }}
                                                    </small>
                                                {% else %}
                                                    <span class="badge bg-secondary">الفرع الرئيسي</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>حالة الاستخدام:</strong></td>
                                            <td>
                                                {% if purchase_order|length > 27 and purchase_order[27] == 1 %}
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check-circle me-1"></i>
                                                        مُستخدم في شحنة
                                                    </span>
                                                    {% if purchase_order|length > 28 and purchase_order[28] %}
                                                        <small class="text-muted d-block mt-1">
                                                            تاريخ الاستخدام: {{ purchase_order[28].strftime('%Y-%m-%d %H:%M') }}
                                                        </small>
                                                    {% endif %}
                                                    {% if purchase_order|length > 29 and purchase_order[29] %}
                                                        <a href="/shipments/view/{{ purchase_order[29] }}"
                                                           class="btn btn-sm btn-outline-primary mt-1" target="_blank">
                                                            <i class="fas fa-ship me-1"></i>
                                                            عرض الشحنة رقم {{ purchase_order[29] }}
                                                        </a>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-clock me-1"></i>
                                                        غير مُستخدم
                                                    </span>
                                                    <small class="text-muted d-block mt-1">
                                                        لم يتم استخدام هذا الأمر في أي شحنة بعد
                                                    </small>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="fas fa-user me-2"></i>معلومات المورد</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>كود المورد:</strong></td>
                                            <td>{{ purchase_order[2] or 'غير محدد' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>اسم المورد:</strong></td>
                                            <td>{{ purchase_order[3] or 'غير محدد' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>العقد المرجعي:</strong></td>
                                            <td>{{ purchase_order[1] if purchase_order[1] else 'بدون عقد' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong><i class="fas fa-file-invoice me-1"></i>رقم فاتورة المورد:</strong></td>
                                            <td>
                                                {% if purchase_order[10] %}
                                                    <span class="badge bg-info">{{ purchase_order[10] }}</span>
                                                {% else %}
                                                    <span class="text-muted">غير محدد</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- التكاليف الإضافية -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>التكاليف الإضافية</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong><i class="fas fa-shipping-fast me-1"></i>أجور الشحن:</strong></td>
                                            <td>
                                                {% if purchase_order[11] and purchase_order[11] > 0 %}
                                                    <span class="badge bg-primary">{{ "%.2f"|format(purchase_order[11]|float) }} ريال</span>
                                                {% else %}
                                                    <span class="text-muted">0.00 ريال</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong><i class="fas fa-file-import me-1"></i>أجور التخليص:</strong></td>
                                            <td>
                                                {% if purchase_order[12] and purchase_order[12] > 0 %}
                                                    <span class="badge bg-secondary">{{ "%.2f"|format(purchase_order[12]|float) }} ريال</span>
                                                {% else %}
                                                    <span class="text-muted">0.00 ريال</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr class="table-info">
                                            <td><strong><i class="fas fa-plus me-1"></i>إجمالي التكاليف الإضافية:</strong></td>
                                            <td>
                                                <strong class="text-info">
                                                    {{ "%.2f"|format((purchase_order[11]|float if purchase_order[11] else 0) + (purchase_order[12]|float if purchase_order[12] else 0)) }} ريال
                                                </strong>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>ملخص التكاليف</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>قيمة الأصناف:</strong></td>
                                            <td>{{ "%.2f"|format(purchase_order[16]|float if purchase_order[16] else 0) }} ريال</td>
                                        </tr>
                                        <tr>
                                            <td><strong>أجور الشحن:</strong></td>
                                            <td>{{ "%.2f"|format(purchase_order[11]|float if purchase_order[11] else 0) }} ريال</td>
                                        </tr>
                                        <tr>
                                            <td><strong>أجور التخليص:</strong></td>
                                            <td>{{ "%.2f"|format(purchase_order[12]|float if purchase_order[12] else 0) }} ريال</td>
                                        </tr>
                                        <tr class="table-success">
                                            <td><strong><i class="fas fa-calculator me-1"></i>إجمالي أمر الشراء:</strong></td>
                                            <td>
                                                <strong class="text-success">
                                                    {{ "%.2f"|format((purchase_order[16]|float if purchase_order[16] else 0) + (purchase_order[11]|float if purchase_order[11] else 0) + (purchase_order[12]|float if purchase_order[12] else 0)) }} ريال
                                                </strong>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول الأصناف -->
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0"><i class="fas fa-list me-2"></i>أصناف أمر الشراء</h6>
                        </div>
                        <div class="card-body">
                            {% if items %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>كود الصنف</th>
                                            <th>اسم الصنف</th>
                                            <th>الوحدة</th>
                                            <th>تاريخ الإنتاج</th>
                                            <th>تاريخ الانتهاء</th>
                                            <th>الكمية</th>
                                            <th>سعر الوحدة</th>
                                            <th>الإجمالي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in items %}
                                        <tr>
                                            <td><strong>{{ item[1] }}</strong></td>
                                            <td>{{ item[2] }}</td>
                                            <td><span class="badge bg-secondary">{{ item[3] }}</span></td>
                                            <td>{{ item[4] or '-' }}</td>
                                            <td>{{ item[5] or '-' }}</td>
                                            <td><span class="badge bg-primary">{{ item[6] }}</span></td>
                                            <td>
                                                {{ "{:,.2f}".format(item[7]|float if item[7] is not none else 0) }}
                                                {% if purchase_order[30] %}{{ purchase_order[30] }}{% else %}ريال{% endif %}
                                            </td>
                                            <td><strong>
                                                {{ "{:,.2f}".format(item[8]|float if item[8] is not none else 0) }}
                                                {% if purchase_order[30] %}{{ purchase_order[30] }}{% else %}ريال{% endif %}
                                            </strong></td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot class="table-light">
                                        <tr>
                                            <td colspan="7" class="text-end"><strong>الإجمالي النهائي:</strong></td>
                                            <td><strong class="text-success">
                                                {{ "{:,.2f}".format(purchase_order[9]|float if purchase_order[9] is not none else 0) }}
                                                {% if purchase_order[30] %}
                                                    {{ purchase_order[30] }}
                                                {% else %}
                                                    ريال
                                                {% endif %}
                                            </strong></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-box-open fa-3x mb-3"></i>
                                <p>لا توجد أصناف في هذا الأمر</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// دالة الطباعة
function printPO() {
    window.print();
}
</script>

<style>
@media print {
    .btn-group, .card-header .btn-group {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .card-header {
        background: white !important;
        color: black !important;
        border: none !important;
    }
}
</style>
{% endblock %}
