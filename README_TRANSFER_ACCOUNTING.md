# نظام الترحيل المحاسبي للحوالات
## Transfer Accounting System

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-2.0+-green.svg)](https://flask.palletsprojects.com)
[![Oracle](https://img.shields.io/badge/Oracle-12c+-red.svg)](https://oracle.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

---

## 📋 **نظرة عامة**

نظام الترحيل المحاسبي للحوالات هو نظام متكامل يدير العمليات المحاسبية للحوالات المالية بين الموردين والصرافين، مع ضمان دقة الترحيلات وسلامة الأرصدة.

### **الميزات الرئيسية:**
- ✅ **ترحيل تلقائي للأرصدة** عند تنفيذ الحوالات
- ✅ **إمكانية إلغاء الحوالات** وعكس الترحيلات
- ✅ **التحقق من كفاية الأرصدة** قبل التنفيذ
- ✅ **تتبع شامل للعمليات** والأنشطة
- ✅ **تقارير مفصلة** للأرصدة والحوالات
- ✅ **دعم عملات متعددة**
- ✅ **واجهة مستخدم سهلة** ومتجاوبة
- ✅ **أمان عالي** وتسجيل شامل

---

## 🏗️ **معمارية النظام**

```
Frontend (HTML/JS) ←→ Backend (Flask/Python) ←→ Database (Oracle)
     ↓                        ↓                        ↓
- واجهة المستخدم          - منطق الأعمال              - تخزين البيانات
- التحقق من البيانات      - API Endpoints            - الإجراءات المخزنة
- استدعاءات AJAX         - الخدمات                   - المشغلات
```

---

## 🚀 **التثبيت والإعداد**

### **المتطلبات:**
- Python 3.8+
- Flask 2.0+
- Oracle Database 12c+
- cx_Oracle 8.0+
- Bootstrap 5.0+

### **خطوات التثبيت:**

#### **1. استنساخ المشروع:**
```bash
git clone https://github.com/your-repo/transfer-accounting-system.git
cd transfer-accounting-system
```

#### **2. إنشاء البيئة الافتراضية:**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

#### **3. تثبيت المتطلبات:**
```bash
pip install -r requirements.txt
```

#### **4. إعداد قاعدة البيانات:**
```bash
# إنشاء الجداول
sqlplus user/password @database/transfer_supplier_distributions_table.sql
sqlplus user/password @database/transfer_activity_log_table.sql

# إنشاء الإجراءات المخزنة
sqlplus user/password @database/execute_transfer_accounting_procedure.sql
sqlplus user/password @database/cancel_transfer_accounting_procedure.sql
sqlplus user/password @database/balance_validation_procedures.sql
```

#### **5. إعداد متغيرات البيئة:**
```bash
export ORACLE_HOST=localhost
export ORACLE_PORT=1521
export ORACLE_SERVICE=ORCL
export ORACLE_USER=saserp
export ORACLE_PASSWORD=password
export FLASK_ENV=development
```

#### **6. تشغيل النظام:**
```bash
python app.py
```

---

## 📖 **كيفية الاستخدام**

### **1. تنفيذ الحوالة:**
1. انتقل إلى صفحة "تنفيذ الحوالات"
2. اختر الحوالة المطلوبة
3. حدد الصراف/البنك
4. وزع المبلغ على الموردين
5. انقر على "تنفيذ الحوالة"

### **2. إلغاء الحوالة:**
1. انقر على زر "إلغاء" بجانب الحوالة المنفذة
2. أدخل سبب الإلغاء
3. أكد عملية الإلغاء

### **3. عرض التقارير:**
1. انتقل إلى قسم "التقارير"
2. اختر نوع التقرير المطلوب
3. حدد المعايير (التاريخ، العملة، إلخ)
4. انقر على "إنشاء التقرير"

---

## 🗄️ **هيكل المشروع**

```
transfer-accounting-system/
├── app/
│   ├── transfers/
│   │   ├── accounting_service.py      # خدمة الترحيل المحاسبي
│   │   ├── accounting_routes.py       # API endpoints
│   │   ├── reports_service.py         # خدمة التقارير
│   │   └── reports_routes.py          # تقارير API
│   ├── templates/
│   │   └── transfers/
│   │       └── execution.html         # واجهة تنفيذ الحوالات
│   └── utils/
│       ├── auth.py                    # المصادقة
│       └── exceptions.py              # الاستثناءات
├── database/
│   ├── transfer_supplier_distributions_table.sql
│   ├── transfer_activity_log_table.sql
│   ├── execute_transfer_accounting_procedure.sql
│   ├── cancel_transfer_accounting_procedure.sql
│   └── balance_validation_procedures.sql
├── tests/
│   ├── test_transfer_accounting.py    # اختبارات الخدمة
│   ├── test_accounting_api.py         # اختبارات API
│   ├── test_database_procedures.sql   # اختبارات قاعدة البيانات
│   └── run_all_tests.py              # تشغيل جميع الاختبارات
├── docs/
│   ├── user_manual.md                 # دليل المستخدم
│   ├── technical_documentation.md     # التوثيق التقني
│   └── transfer_accounting_system.md  # وثائق النظام
└── README_TRANSFER_ACCOUNTING.md      # هذا الملف
```

---

## 🧪 **الاختبارات**

### **تشغيل جميع الاختبارات:**
```bash
python tests/run_all_tests.py
```

### **تشغيل اختبارات محددة:**
```bash
# اختبارات Python
python -m unittest tests.test_transfer_accounting
python -m unittest tests.test_accounting_api

# اختبارات قاعدة البيانات
sqlplus user/password @tests/test_database_procedures.sql
```

### **أنواع الاختبارات:**
- ✅ **اختبارات الوحدة**: للخدمات والدوال
- ✅ **اختبارات API**: لواجهات برمجة التطبيقات
- ✅ **اختبارات التكامل**: للنظام ككل
- ✅ **اختبارات قاعدة البيانات**: للإجراءات المخزنة

---

## 📊 **API Documentation**

### **تنفيذ الحوالة:**
```http
POST /transfers/accounting/execute
Content-Type: application/json

{
    "transfer_id": 123,
    "money_changer_id": 456,
    "total_amount": 10000.00,
    "currency_code": "SAR",
    "supplier_distributions": [
        {"supplier_id": 1, "amount": 6000.00},
        {"supplier_id": 2, "amount": 4000.00}
    ]
}
```

### **إلغاء الحوالة:**
```http
POST /transfers/accounting/cancel
Content-Type: application/json

{
    "transfer_id": 123,
    "cancellation_reason": "سبب الإلغاء"
}
```

### **التحقق من الرصيد:**
```http
POST /transfers/accounting/balance-check
Content-Type: application/json

{
    "money_changer_id": 456,
    "amount": 10000.00,
    "currency_code": "SAR"
}
```

### **تقرير الأرصدة:**
```http
GET /transfers/reports/balances?entity_type=SUPPLIER&currency=SAR&format=json
```

---

## 🔒 **الأمان**

### **ميزات الأمان:**
- 🔐 **مصادقة المستخدمين** مطلوبة لجميع العمليات
- 📝 **تسجيل شامل** لجميع الأنشطة
- 🛡️ **التحقق من الصلاحيات** قبل كل عملية
- 🔍 **تدقيق العمليات** مع تفاصيل المستخدم والوقت
- 🚫 **حماية من SQL Injection** باستخدام Prepared Statements
- 🔒 **تشفير البيانات الحساسة**

### **سجل الأنشطة:**
- 👤 **معرف المستخدم** الذي قام بالعملية
- ⏰ **وقت وتاريخ** العملية
- 🌐 **عنوان IP** للمستخدم
- 🖥️ **معلومات المتصفح**
- 📝 **تفاصيل العملية** والنتيجة

---

## 📈 **المراقبة والصيانة**

### **مراقبة الأداء:**
- 📊 **مراقبة استعلامات قاعدة البيانات**
- 💾 **مراقبة استخدام الذاكرة**
- 🔗 **مراقبة اتصالات قاعدة البيانات**
- ⚡ **مراقبة أوقات الاستجابة**

### **الصيانة الدورية:**
- 💾 **نسخ احتياطي يومي** لقاعدة البيانات
- 🧹 **تنظيف السجلات القديمة**
- 📊 **تحديث إحصائيات قاعدة البيانات**
- 🔒 **مراجعة الأمان والصلاحيات**

---

## 🤝 **المساهمة**

### **كيفية المساهمة:**
1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى Branch (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

### **معايير المساهمة:**
- ✅ **كتابة اختبارات** للميزات الجديدة
- ✅ **توثيق الكود** والتغييرات
- ✅ **اتباع معايير الكود** المحددة
- ✅ **اختبار التغييرات** قبل الإرسال

---

## 📞 **الدعم والمساعدة**

### **للحصول على المساعدة:**
- 📖 **راجع دليل المستخدم**: `docs/user_manual.md`
- 🔧 **راجع التوثيق التقني**: `docs/technical_documentation.md`
- 🐛 **أبلغ عن الأخطاء**: عبر GitHub Issues
- 💬 **تواصل مع الفريق**: <EMAIL>

### **الموارد المفيدة:**
- [دليل المستخدم](docs/user_manual.md)
- [التوثيق التقني](docs/technical_documentation.md)
- [وثائق النظام](docs/transfer_accounting_system.md)
- [أمثلة الاستخدام](examples/)

---

## 📄 **الترخيص**

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

## 🙏 **شكر وتقدير**

- فريق تطوير نظام SASERP
- مجتمع Flask و Python
- مجتمع Oracle Database
- جميع المساهمين في المشروع

---

## 📊 **الإحصائيات**

- 📁 **عدد الملفات**: 25+
- 📝 **أسطر الكود**: 5000+
- 🧪 **عدد الاختبارات**: 50+
- 📚 **صفحات التوثيق**: 10+
- 🌟 **معدل التغطية**: 90%+

---

*تم تطوير هذا النظام بواسطة فريق SASERP*  
*آخر تحديث: 2025-09-07*
