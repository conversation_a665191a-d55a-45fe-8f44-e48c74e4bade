"""
خدمة PDF بسيطة وموثوقة
تستخدم ReportLab فقط بدون تعقيدات
"""

from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.enums import TA_CENTER, TA_RIGHT
import io
from datetime import datetime
from typing import Dict, Optional


class SimplePDFService:
    """خدمة PDF بسيطة وموثوقة"""
    
    def __init__(self):
        """تهيئة الخدمة"""
        self.styles = getSampleStyleSheet()
        
        # إنشاء أنماط مخصصة
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )
        
        self.header_style = ParagraphStyle(
            'CustomHeader',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            alignment=TA_RIGHT,
            textColor=colors.darkgreen
        )
        
        self.normal_style = ParagraphStyle(
            'CustomNormal',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceAfter=6,
            alignment=TA_RIGHT
        )
    
    def create_delivery_order_pdf(self, order_data: Dict) -> bytes:
        """إنشاء ملف HTML لأمر التسليم بالعربية (بدلاً من PDF)"""

        # استخدام HTML بدلاً من PDF لحل مشكلة الخطوط العربية
        try:
            from app.services.html_to_pdf_service import generate_html_delivery_order

            # إنشاء ملف HTML بالنموذج العربي
            html_path = generate_html_delivery_order(order_data)

            # قراءة الملف وإرجاعه كـ bytes
            with open(html_path, 'r', encoding='utf-8') as f:
                html_content = f.read()

            return html_content.encode('utf-8')

        except Exception as e:
            print(f"HTML service failed: {e}")

            # في حالة الفشل، استخدم النسخة البسيطة
            return self._create_simple_pdf(order_data)

    def _create_simple_pdf(self, order_data: Dict) -> bytes:
        """إنشاء PDF بسيط كبديل"""

        # إنشاء buffer للـ PDF
        buffer = io.BytesIO()

        # إنشاء المستند
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )

        # قائمة العناصر
        story = []

        # العنوان الرئيسي
        title = Paragraph("أمر تسليم للمخلص الجمركي", self.title_style)
        story.append(title)
        story.append(Spacer(1, 20))

        # معلومات الأمر
        order_info = [
            ['رقم الأمر:', order_data.get('order_number', 'غير محدد')],
            ['تاريخ الإصدار:', datetime.now().strftime('%Y-%m-%d')],
            ['رقم التتبع:', order_data.get('tracking_number', 'غير محدد')],
            ['رقم الحجز:', order_data.get('booking_number', 'غير محدد')]
        ]

        order_table = Table(order_info, colWidths=[2*inch, 4*inch])
        order_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.lightblue),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (0, 0), (0, -1), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(order_table)
        story.append(Spacer(1, 20))

        # تفاصيل التسليم
        delivery_header = Paragraph("تفاصيل التسليم", self.header_style)
        story.append(delivery_header)

        delivery_info = [
            ['موقع التسليم:', order_data.get('delivery_location', 'غير محدد')],
            ['التاريخ المتوقع:', order_data.get('expected_completion_date', 'غير محدد')],
            ['اسم المخلص:', order_data.get('agent_name', 'غير محدد')]
        ]

        delivery_table = Table(delivery_info, colWidths=[2*inch, 4*inch])
        delivery_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.beige),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (0, 0), (0, -1), colors.darkgreen),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(delivery_table)
        story.append(Spacer(1, 30))

        # تذييل
        footer = Paragraph(
            f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br/>شركة الشحن المتقدمة",
            self.normal_style
        )
        story.append(footer)

        # بناء المستند
        doc.build(story)

        # الحصول على البيانات
        pdf_data = buffer.getvalue()
        buffer.close()

        return pdf_data


# إنشاء instance عام
simple_pdf_service = SimplePDFService()


def generate_simple_delivery_order_pdf(order_data: Dict) -> bytes:
    """دالة مساعدة لإنشاء PDF بسيط"""
    return simple_pdf_service.create_delivery_order_pdf(order_data)
