{% extends "base.html" %}

{% block extra_css %}
<!-- Handsontable CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/handsontable@12.4.0/dist/handsontable.full.min.css">

<style>
.contract-form {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.form-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.form-body {
    padding: 30px;
}

.section-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    margin: 20px -30px;
    font-weight: 600;
    font-size: 18px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.required {
    color: #dc3545;
}

.table-container {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

/* خلايا للقراءة فقط */
.readonly-cell {
    background-color: #f8f9fa !important;
    color: #6c757d;
}

/* خلايا محسوبة */
.calculated-cell {
    background-color: #e3f2fd !important;
    font-weight: 600;
}

/* خلايا غير صحيحة */
.handsontable .htInvalid {
    background-color: #ffebee !important;
    color: #c62828;
}

.handsontable {
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.handsontable .htCore {
    direction: rtl;
}

.form-actions {
    background: #f8f9fa;
    padding: 30px;
    text-align: center;
    border-top: 1px solid #dee2e6;
}

.btn-action {
    padding: 12px 30px;
    border-radius: 8px;
    font-weight: 600;
    margin: 0 10px;
    min-width: 150px;
    transition: all 0.3s ease;
}

.btn-save {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
}

.btn-save:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    color: white;
}

.btn-cancel:hover {
    background: #5a6268;
    color: white;
}

.statistics-panel {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #dee2e6;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    font-weight: 600;
    color: #495057;
}

.stat-value {
    font-weight: 700;
    color: #007bff;
}
</style>
{% endblock %}

{% block content %}
<div class="contract-form">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-11">
                <div class="form-container">
                    <!-- رأس النموذج -->
                    <div class="form-header">
                        <h1><i class="fas fa-file-contract me-3"></i>إضافة عقد جديد</h1>
                        <p>تسجيل بيانات العقود مع الموردين وربطها بأوامر الشراء</p>
                    </div>

                    <!-- نموذج البيانات -->
                    <form id="contractForm" method="POST" action="{{ url_for('contracts.create_contract_with_table') }}">
                        <div class="form-body">
                            <!-- القسم الأول: البيانات الرئيسية -->
                            <div class="section-title">
                                <i class="fas fa-info-circle"></i>
                                البيانات الرئيسية
                            </div>

                            <div class="row">
                                <!-- رقم الفرع -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">رقم الفرع <span class="required">*</span></label>
                                        <select class="form-select" id="branch_id" name="branch_id" required>
                                            <option value="">اختر الفرع</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- رقم العقد -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">رقم العقد</label>
                                        <input type="text" class="form-control" id="contract_number" name="contract_number" 
                                               placeholder="سيتم توليده تلقائياً">
                                    </div>
                                </div>

                                <!-- التاريخ -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">التاريخ <span class="required">*</span></label>
                                        <input type="date" class="form-control" id="contract_date" name="contract_date" 
                                               value="{{ today }}" required>
                                    </div>
                                </div>

                                <!-- المورد -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">المورد <span class="required">*</span></label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="supplier_name" name="supplier_name" 
                                                   placeholder="اسم المورد" readonly required>
                                            <button type="button" class="btn btn-outline-primary" onclick="openSupplierSearch()">
                                                <i class="fas fa-search"></i>
                                            </button>
                                        </div>
                                        <input type="hidden" id="supplier_id" name="supplier_id">
                                    </div>
                                </div>

                                <!-- العملة -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">العملة</label>
                                        <select class="form-select" id="currency_code" name="currency_code">
                                            <option value="SAR">ريال سعودي (SAR)</option>
                                            <option value="USD">دولار أمريكي (USD)</option>
                                            <option value="EUR">يورو (EUR)</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- مبلغ العقد -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">مبلغ العقد</label>
                                        <input type="number" class="form-control" id="contract_amount" name="contract_amount" 
                                               step="0.001" min="0" placeholder="0.000" readonly>
                                    </div>
                                </div>
                            </div>

                            <!-- القسم الثاني: البيانات التفصيلية -->
                            <div class="section-title mt-4">
                                <i class="fas fa-table"></i>
                                البيانات التفصيلية - أصناف العقد
                            </div>

                            <!-- حاوي الجدول -->
                            <div class="table-container">
                                <!-- الجدول التفاعلي -->
                                <div id="contractDetailsTable"></div>
                            </div>

                            <!-- لوحة الإحصائيات -->
                            <div class="statistics-panel">
                                <h5><i class="fas fa-chart-bar me-2"></i>إحصائيات العقد</h5>
                                <div id="statistics">
                                    <div class="stat-item">
                                        <span class="stat-label">إجمالي الصفوف:</span>
                                        <span class="stat-value" id="totalRows">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">صفوف مكتملة:</span>
                                        <span class="stat-value" id="completedRows">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">إجمالي الكمية:</span>
                                        <span class="stat-value" id="totalQuantity">0.000</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">إجمالي المبلغ:</span>
                                        <span class="stat-value" id="totalAmount">0.000 ر.س</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="form-actions">
                            <button type="submit" class="btn btn-action btn-save">
                                <i class="fas fa-save me-2"></i>حفظ العقد
                            </button>
                            <button type="button" class="btn btn-action btn-cancel" onclick="window.history.back()">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Handsontable JS -->
<script src="https://cdn.jsdelivr.net/npm/handsontable@12.4.0/dist/handsontable.full.min.js"></script>

<!-- Excel Table Component -->
<script src="{{ url_for('static', filename='js/excel-table-component.js') }}"></script>

<script>
// متغيرات عامة
let contractTable;

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء تحميل صفحة العقد الجديد...');

    // التحقق من تحميل المكتبات المطلوبة
    checkRequiredLibraries();

    // تحميل الفروع
    loadBranches();

    // تهيئة الجدول بعد التأكد من تحميل المكتبات
    waitForLibrariesAndInitialize();

    console.log('✅ تم تحميل الصفحة بنجاح');
});

// التحقق من تحميل المكتبات المطلوبة
function checkRequiredLibraries() {
    console.log('🔍 التحقق من المكتبات المطلوبة...');

    // التحقق من jQuery
    if (typeof $ === 'undefined') {
        console.error('❌ jQuery غير محمل');
        showError('jQuery غير محمل');
        return false;
    } else {
        console.log('✅ jQuery محمل');
    }

    // التحقق من Handsontable
    if (typeof Handsontable === 'undefined') {
        console.error('❌ Handsontable غير محمل');
        showError('Handsontable غير محمل');
        return false;
    } else {
        console.log('✅ Handsontable محمل');
    }

    // التحقق من ExcelTableComponent
    if (typeof ExcelTableComponent === 'undefined') {
        console.error('❌ ExcelTableComponent غير محمل');
        showError('ExcelTableComponent غير محمل');
        return false;
    } else {
        console.log('✅ ExcelTableComponent محمل');
    }

    return true;
}

// انتظار تحميل المكتبات وتهيئة الجدول
function waitForLibrariesAndInitialize() {
    let attempts = 0;
    const maxAttempts = 50; // 5 ثوان

    function checkAndInit() {
        attempts++;
        console.log(`🔄 محاولة ${attempts}: التحقق من المكتبات...`);

        if (checkRequiredLibraries()) {
            console.log('✅ جميع المكتبات محملة - بدء تهيئة الجدول');
            initializeContractTable();
        } else if (attempts < maxAttempts) {
            console.log('⏳ انتظار تحميل المكتبات...');
            setTimeout(checkAndInit, 100);
        } else {
            console.error('❌ فشل في تحميل المكتبات بعد 5 ثوان');
            showError('فشل في تحميل المكتبات المطلوبة');
        }
    }

    checkAndInit();
}

// عرض رسالة خطأ
function showError(message) {
    const container = document.getElementById('contractDetailsTable');
    if (container) {
        container.innerHTML = `
            <div class="alert alert-danger text-center">
                <h4><i class="fas fa-exclamation-triangle"></i> خطأ في التحميل</h4>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="fas fa-redo"></i> إعادة تحميل الصفحة
                </button>
            </div>
        `;
    }
}

// تحميل الفروع
function loadBranches() {
    $.ajax({
        url: '/contracts/api/branches',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const branchSelect = $('#branch_id');
                branchSelect.empty().append('<option value="">اختر الفرع</option>');

                response.branches.forEach(branch => {
                    branchSelect.append(`
                        <option value="${branch.branch_id}">${branch.branch_name}</option>
                    `);
                });
            }
        },
        error: function() {
            console.error('خطأ في تحميل الفروع');
        }
    });
}

// تهيئة جدول Excel
function initializeContractTable() {
    console.log('🔧 بدء تهيئة جدول Excel...');

    // التحقق من وجود العنصر المحدد
    const container = document.getElementById('contractDetailsTable');
    if (!container) {
        console.error('❌ لم يتم العثور على عنصر الجدول #contractDetailsTable');
        showError('لم يتم العثور على عنصر الجدول');
        return;
    }

    console.log('✅ تم العثور على عنصر الجدول');

    // التحقق النهائي من المكتبات
    if (!checkRequiredLibraries()) {
        return;
    }

    try {
        // تكوين الأعمدة المخصصة للعقود
        const contractColumns = [
        { key: 'code', title: 'كود الصنف', type: 'autocomplete', width: 120 },
        { key: 'name', title: 'اسم الصنف', type: 'text', readOnly: true, width: 200 },
        { key: 'unit', title: 'الوحدة', type: 'text', readOnly: true, width: 80 },
        { key: 'quantity', title: 'الكمية', type: 'numeric', width: 100 },
        { key: 'free_quantity', title: 'ك. مجانية', type: 'numeric', width: 100 },
        { key: 'unit_price', title: 'سعر الوحدة', type: 'numeric', width: 100 },
        { key: 'discount_percent', title: 'خصم %', type: 'numeric', width: 100 },
        { key: 'tax_amount', title: 'مبلغ الضريبة', type: 'numeric', width: 100 },
        { key: 'total', title: 'الإجمالي', type: 'numeric', readOnly: true, width: 120 },
        { key: 'production_date', title: 'تاريخ الإنتاج', type: 'date', width: 120 },
        { key: 'expiry_date', title: 'تاريخ الانتهاء', type: 'date', width: 120 },
        { key: 'notes', title: 'ملاحظات', type: 'text', width: 150 }
    ];

    // إنشاء مكون الجدول
    contractTable = new ExcelTableComponent('#contractDetailsTable', {
        columns: contractColumns,
        initialRows: 20,
        height: 400,
        autoSave: false,
        rtl: true,
        language: 'ar'
    });

    // الاستماع للأحداث
    document.addEventListener('excelTable:statisticsUpdated', function(e) {
        const { totalRows, completedRows, totalQuantity, totalAmount } = e.detail;

        document.getElementById('totalRows').textContent = totalRows;
        document.getElementById('completedRows').textContent = completedRows;
        document.getElementById('totalQuantity').textContent = totalQuantity.toFixed(3);
        document.getElementById('totalAmount').textContent = totalAmount.toFixed(3) + ' ر.س';

        // تحديث مبلغ العقد تلقائياً
        document.getElementById('contract_amount').value = totalAmount.toFixed(3);
    });

    document.addEventListener('excelTable:dataChanged', function(e) {
        console.log('تم تغيير البيانات:', e.detail.changes);
    });

    document.addEventListener('excelTable:saveSuccess', function(e) {
        alert('تم الحفظ بنجاح!');
    });

    document.addEventListener('excelTable:saveError', function(e) {
        alert('خطأ في الحفظ: ' + e.detail.message);
    });

        console.log('✅ تم تهيئة جدول Excel بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تهيئة جدول Excel:', error);
        showError('خطأ في تهيئة الجدول: ' + error.message);
    }
}

// فتح نافذة البحث عن الموردين
function openSupplierSearch() {
    alert('سيتم إضافة نافذة البحث عن الموردين لاحقاً');
}

// حفظ العقد
document.getElementById('contractForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // الحصول على البيانات الصحيحة من الجدول
    const tableData = contractTable.getValidData();

    if (tableData.length === 0) {
        alert('يرجى إضافة أصناف للعقد');
        return;
    }

    // إضافة بيانات الجدول للنموذج
    const hiddenInput = document.createElement('input');
    hiddenInput.type = 'hidden';
    hiddenInput.name = 'table_data';
    hiddenInput.value = JSON.stringify(tableData);
    this.appendChild(hiddenInput);

    // إرسال النموذج
    this.submit();
});
</script>
{% endblock %}
