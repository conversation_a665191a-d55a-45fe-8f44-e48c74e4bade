{% extends "base.html" %}

{% block title %}إدارة المخلصين الجمركيين{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/customs-agents.js') }}"></script>
{% endblock %}

{% block extra_css %}
<style>
/* تحسينات تقرير الأداء */
.modal-xl {
    max-width: 1200px;
}

.rating-bar {
    width: 100px;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.rating-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
    transition: width 0.3s ease;
}

.performance-rating .d-flex {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* تحسين مظهر الرسم البياني */
#ordersChart {
    max-width: 100%;
    height: auto;
}

/* تحسين مظهر النجوم */
.rating-stars i {
    font-size: 1.1em;
    margin-right: 2px;
}

/* تحسين مظهر شريط التقدم */
.progress {
    background-color: #e9ecef;
    border-radius: 0.5rem;
}

.progress-bar {
    border-radius: 0.5rem;
    transition: width 0.6s ease;
}

/* تحسين مظهر البطاقات */
.border-primary {
    border-color: #0d6efd !important;
}

.border-success {
    border-color: #198754 !important;
}

.border-warning {
    border-color: #ffc107 !important;
}

.border-danger {
    border-color: #dc3545 !important;
}

/* تحسين مظهر النافذة المنبثقة */
.modal-content {
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.5rem 0.5rem 0 0;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 0.5rem 0.5rem;
}

/* تحسين الطباعة */
@media print {
    .modal-footer {
        display: none !important;
    }

    .btn-close {
        display: none !important;
    }

    .modal-header {
        background-color: #f8f9fa !important;
        color: #333 !important;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-users-cog text-primary me-2"></i>
                        إدارة المخلصين الجمركيين
                    </h2>
                    <p class="text-muted mb-0">إدارة بيانات المخلصين الجمركيين وتقييم أدائهم</p>
                </div>
                <div>
                    <a href="{{ url_for('shipments.add_customs_agent_page') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إضافة مخلص جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة المخلصين الجمركيين
                        <span class="badge bg-primary ms-2">{{ agents|length }}</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>كود المخلص</th>
                                    <th>اسم المخلص</th>
                                    <th>الشركة</th>
                                    <th>رقم الترخيص</th>
                                    <th>التخصص</th>
                                    <th>التقييم</th>
                                    <th>الأوامر</th>
                                    <th>معدل الإنجاز</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if agents %}
                                    {% for agent in agents %}
                                    <tr>
                                        <td><strong class="text-primary">{{ agent[1] }}</strong></td>
                                        <td>
                                            <div>
                                                <strong>{{ agent[2] }}</strong>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="fas fa-phone me-1"></i>{{ agent[6] or 'غير محدد' }}
                                                </small>
                                            </div>
                                        </td>
                                        <td>{{ agent[3] or 'غير محدد' }}</td>
                                        <td><span class="badge bg-info">{{ agent[4] }}</span></td>
                                        <td><span class="badge bg-secondary">{{ agent[9] or 'عام' }}</span></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="rating-stars me-2">
                                                    {% set rating = agent[10] or 0 %}
                                                    {% for i in range(1, 6) %}
                                                        {% if i <= rating %}
                                                            <i class="fas fa-star text-warning"></i>
                                                        {% else %}
                                                            <i class="far fa-star text-muted"></i>
                                                        {% endif %}
                                                    {% endfor %}
                                                </div>
                                                <small class="text-muted">{{ "%.1f"|format(agent[10] or 0) }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-center">
                                                <strong class="text-primary">{{ agent[17] or 0 }}</strong>
                                                <br>
                                                <small class="text-muted">إجمالي</small>
                                            </div>
                                        </td>
                                        <td>
                                            {% set completion_rate = ((agent[18] or 0) / (agent[17] or 1) * 100) if agent[17] and agent[17] > 0 else 0 %}
                                            <div class="text-center">
                                                <div class="progress" style="height: 8px;">
                                                    <div class="progress-bar bg-success" style="width: {{ completion_rate }}%"></div>
                                                </div>
                                                <small class="text-muted">{{ "%.0f"|format(completion_rate) }}%</small>
                                            </div>
                                        </td>
                                        <td>
                                            {% if agent[14] == 1 %}
                                                <span class="badge bg-success">نشط</span>
                                            {% else %}
                                                <span class="badge bg-danger">غير نشط</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary btn-sm"
                                                        onclick="viewAgent({{ agent[0] }})" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-success btn-sm"
                                                        onclick="editAgent({{ agent[0] }})" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-info btn-sm"
                                                        onclick="viewPerformance({{ agent[0] }})" title="تقرير الأداء">
                                                    <i class="fas fa-chart-line"></i>
                                                </button>
                                                {% if agent[14] == 1 %}
                                                <button type="button" class="btn btn-outline-warning btn-sm"
                                                        onclick="toggleAgentStatus({{ agent[0] }}, 0)" title="إلغاء التفعيل">
                                                    <i class="fas fa-pause"></i>
                                                </button>
                                                {% else %}
                                                <button type="button" class="btn btn-outline-success btn-sm"
                                                        onclick="toggleAgentStatus({{ agent[0] }}, 1)" title="تفعيل">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                                {% endif %}
                                                <button type="button" class="btn btn-outline-danger btn-sm"
                                                        onclick="deleteAgent({{ agent[0] }})" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="10" class="text-center py-4">
                                            <i class="fas fa-users fa-2x text-muted mb-2"></i>
                                            <br>
                                            <span class="text-muted">لا توجد مخلصين مسجلين</span>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}