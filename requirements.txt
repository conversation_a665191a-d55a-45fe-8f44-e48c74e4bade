# نظام المحاسبة المتقدم - متطلبات المشروع
# Advanced Accounting System - Project Requirements

# إطار العمل الأساسي - Core Framework
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Migrate==4.0.5
Flask-Login==0.6.3
Flask-WTF==1.1.1
WTForms==3.0.1

# متغيرات البيئة - Environment Variables
python-dotenv==1.1.1

# قاعدة البيانات - Database
SQLAlchemy==2.0.21

# دعم Oracle Database - Oracle Database Support
cx_Oracle==8.3.0
oracledb==1.4.2

# واجهة المستخدم - User Interface
Flask-Bootstrap==3.3.7.1
Jinja2==3.1.2

# التاريخ والوقت - Date and Time
python-dateutil==2.8.2
pytz==2023.3

# التقارير والتصدير - Reports and Export
reportlab==4.0.4
openpyxl==3.1.2
pandas==2.1.1
matplotlib==3.7.2
seaborn==0.12.2

# الأمان والتشفير - Security and Encryption
bcrypt==4.0.1
cryptography==41.0.4

# التحقق من صحة البيانات - Data Validation
marshmallow==3.20.1
email-validator==2.0.0

# إدارة الملفات - File Management
Pillow==10.0.0

# الاختبارات - Testing
pytest==7.4.2
pytest-flask==1.2.0

# التطوير - Development
python-dotenv==1.0.0

# مكتبات تصميم NetSuite Oracle - NetSuite Oracle Design Libraries
# CSS Framework Support
libsass==0.22.0
pyscss==1.4.0

# Advanced UI Components
flask-assets==2.1.0
webassets==2.0

# Icon and Font Management
fonttools==4.43.1

# Advanced Chart and Visualization
plotly==5.17.0
dash==2.14.2
dash-bootstrap-components==1.5.0

# Modern CSS Processing
cssmin==0.2.0
jsmin==3.0.1

# Color Management
webcolors==1.13

# NetSuite Oracle Advanced Design System - Installed
# تصميم NetSuite Oracle المتطور - مثبت

# UI Component Libraries - Installed
flask-admin==1.6.1
flask-restx==1.3.0

# Advanced Form Handling - Installed
wtforms-alchemy==0.19.0
wtforms-components==0.11.0

# Rich Text Editor Support - Installed
flask-ckeditor==1.0.0

# Advanced Table Components - Installed
flask-table==0.5.0

# Advanced Color Palette - Installed
colorthief==0.2.1

# Asset Management - Installed
flask-assets==2.1.0
webassets==2.0
cssmin==0.2.0
jsmin==3.0.1

# Additional Dependencies - Installed
validators==0.35.0
intervals==0.9.2
SQLAlchemy-Utils==0.41.2
Flask-Babel==4.0.0
Babel==2.17.0

# Note: Many JavaScript libraries will be included via CDN in templates
# ملاحظة: العديد من مكتبات JavaScript سيتم تضمينها عبر CDN في القوالب
