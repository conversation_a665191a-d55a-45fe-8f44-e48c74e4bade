{% extends "base.html" %}

{% block title %}اختبار الإعدادات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('settings.index') }}">الإعدادات</a></li>
                    <li class="breadcrumb-item active">اختبار الإعدادات</li>
                </ol>
            </nav>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-vial"></i> اختبار API الإعدادات</h4>
                </div>
                <div class="card-body">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>1. الحصول على الإعدادات الحالية</h5>
                            <button class="btn btn-info" onclick="getCurrentSettings()">جلب الإعدادات</button>
                            <div id="currentSettings" class="mt-3"></div>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>2. اختبار تحديث المظهر</h5>
                            <div class="mb-3">
                                <select id="testTheme" class="form-select">
                                    <option value="light">فاتح</option>
                                    <option value="dark">داكن</option>
                                    <option value="auto">تلقائي</option>
                                </select>
                            </div>
                            <button class="btn btn-success" onclick="testUpdateTheme()">تحديث المظهر</button>
                            <div id="updateResult" class="mt-3"></div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-12">
                            <h5>3. اختبار شامل للإعدادات</h5>
                            <button class="btn btn-primary" onclick="runFullTest()">تشغيل اختبار شامل</button>
                            <div id="fullTestResult" class="mt-3"></div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.result-box {
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}
.success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
.error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
.info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
.warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
</style>

<script>
async function getCurrentSettings() {
    const resultDiv = document.getElementById('currentSettings');
    resultDiv.innerHTML = '<div class="info result-box">جاري جلب الإعدادات...</div>';
    
    try {
        const response = await fetch('/settings/api/get-general-settings');
        const data = await response.json();
        
        if (data.success) {
            resultDiv.innerHTML = '<div class="success result-box"><strong>✅ الإعدادات الحالية:</strong><br><pre>' + 
                JSON.stringify(data.settings, null, 2) + '</pre></div>';
        } else {
            resultDiv.innerHTML = '<div class="error result-box">❌ فشل: ' + data.message + '</div>';
        }
    } catch (error) {
        resultDiv.innerHTML = '<div class="error result-box">❌ خطأ: ' + error.message + '</div>';
    }
}

async function testUpdateTheme() {
    const resultDiv = document.getElementById('updateResult');
    const theme = document.getElementById('testTheme').value;
    resultDiv.innerHTML = '<div class="info result-box">جاري تحديث المظهر إلى: ' + theme + '</div>';
    
    const settings = {
        language: 'ar',
        currency: 'SAR',
        timezone: 'Asia/Riyadh',
        date_format: 'DD/MM/YYYY',
        theme: theme,
        items_per_page: 25
    };
    
    try {
        const response = await fetch('/settings/api/update-general-settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(settings)
        });
        
        const data = await response.json();
        
        if (data.success) {
            resultDiv.innerHTML = '<div class="success result-box">✅ تم تحديث المظهر بنجاح إلى: ' + theme + '</div>';
            
            // التحقق من الحفظ
            setTimeout(async () => {
                const verifyResponse = await fetch('/settings/api/get-general-settings');
                const verifyData = await verifyResponse.json();
                
                if (verifyData.success && verifyData.settings.theme === theme) {
                    resultDiv.innerHTML += '<div class="success result-box">✅ تم التحقق: المظهر محفوظ بنجاح</div>';
                } else {
                    resultDiv.innerHTML += '<div class="error result-box">❌ فشل التحقق: المظهر لم يتم حفظه</div>';
                }
            }, 1000);
            
        } else {
            resultDiv.innerHTML = '<div class="error result-box">❌ فشل التحديث: ' + data.message + '</div>';
        }
    } catch (error) {
        resultDiv.innerHTML = '<div class="error result-box">❌ خطأ: ' + error.message + '</div>';
    }
}

async function runFullTest() {
    const resultDiv = document.getElementById('fullTestResult');
    resultDiv.innerHTML = '<div class="info result-box">🧪 بدء الاختبار الشامل...</div>';
    
    let testResults = [];
    
    // اختبار 1: جلب الإعدادات
    try {
        const response1 = await fetch('/settings/api/get-general-settings');
        const data1 = await response1.json();
        if (data1.success) {
            testResults.push('✅ اختبار جلب الإعدادات: نجح');
        } else {
            testResults.push('❌ اختبار جلب الإعدادات: فشل');
        }
    } catch (error) {
        testResults.push('❌ اختبار جلب الإعدادات: خطأ - ' + error.message);
    }
    
    // اختبار 2: تحديث المظهر إلى داكن
    try {
        const updateResponse = await fetch('/settings/api/update-general-settings', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                language: 'ar',
                currency: 'SAR',
                timezone: 'Asia/Riyadh',
                date_format: 'DD/MM/YYYY',
                theme: 'dark',
                items_per_page: 25
            })
        });
        
        const updateData = await updateResponse.json();
        if (updateData.success) {
            testResults.push('✅ اختبار تحديث المظهر: نجح');
            
            // التحقق من الحفظ
            await new Promise(resolve => setTimeout(resolve, 500));
            const verifyResponse = await fetch('/settings/api/get-general-settings');
            const verifyData = await verifyResponse.json();
            
            if (verifyData.success && verifyData.settings.theme === 'dark') {
                testResults.push('✅ اختبار التحقق من الحفظ: نجح');
            } else {
                testResults.push('❌ اختبار التحقق من الحفظ: فشل');
            }
        } else {
            testResults.push('❌ اختبار تحديث المظهر: فشل - ' + updateData.message);
        }
    } catch (error) {
        testResults.push('❌ اختبار تحديث المظهر: خطأ - ' + error.message);
    }
    
    // عرض النتائج
    const successCount = testResults.filter(r => r.startsWith('✅')).length;
    const totalCount = testResults.length;
    
    let resultClass = successCount === totalCount ? 'success' : 'warning';
    
    resultDiv.innerHTML = `
        <div class="${resultClass} result-box">
            <strong>🧪 نتائج الاختبار الشامل (${successCount}/${totalCount}):</strong><br>
            ${testResults.join('<br>')}
        </div>
    `;
}

// تشغيل اختبار أولي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    getCurrentSettings();
});
</script>
{% endblock %}
