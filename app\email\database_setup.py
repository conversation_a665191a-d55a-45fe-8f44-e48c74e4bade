# -*- coding: utf-8 -*-
"""
إعداد قاعدة البيانات لنظام البريد الإلكتروني
Email System Database Setup
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from database_manager import DatabaseManager
import logging

# إعداد السجلات
logger = logging.getLogger(__name__)

def create_email_tables():
    """إنشاء جداول نظام البريد الإلكتروني"""
    
    db = DatabaseManager()
    
    try:
        logger.info("🔧 بدء إنشاء جداول نظام البريد الإلكتروني...")
        
        # 1. جدول حسابات البريد الإلكتروني
        email_accounts_table = """
        CREATE TABLE email_accounts (
            id NUMBER PRIMARY KEY,
            user_id NUMBER NOT NULL,
            email_address VARCHAR2(255) NOT NULL UNIQUE,
            display_name VARCHAR2(255) NOT NULL,
            
            -- إعدادات الخادم
            smtp_server VARCHAR2(255) NOT NULL,
            smtp_port NUMBER DEFAULT 587,
            smtp_use_tls NUMBER(1) DEFAULT 1,
            smtp_use_ssl NUMBER(1) DEFAULT 0,
            
            imap_server VARCHAR2(255) NOT NULL,
            imap_port NUMBER DEFAULT 993,
            imap_use_ssl NUMBER(1) DEFAULT 1,
            
            -- بيانات المصادقة (مشفرة)
            password_encrypted BLOB NOT NULL,
            
            -- إعدادات إضافية
            signature CLOB,
            auto_reply_enabled NUMBER(1) DEFAULT 0,
            auto_reply_message CLOB,
            
            -- معلومات النظام
            is_active NUMBER(1) DEFAULT 1,
            is_default NUMBER(1) DEFAULT 0,
            last_sync DATE,
            created_at DATE DEFAULT SYSDATE,
            updated_at DATE DEFAULT SYSDATE,
            
            CONSTRAINT fk_email_accounts_user FOREIGN KEY (user_id) REFERENCES users(id)
        )
        """
        
        # 2. جدول مجلدات البريد الإلكتروني
        email_folders_table = """
        CREATE TABLE email_folders (
            id NUMBER PRIMARY KEY,
            account_id NUMBER NOT NULL,
            name VARCHAR2(255) NOT NULL,
            name_arabic VARCHAR2(255) NOT NULL,
            folder_type VARCHAR2(50) NOT NULL,
            parent_id NUMBER,
            
            -- إعدادات المجلد
            unread_count NUMBER DEFAULT 0,
            total_count NUMBER DEFAULT 0,
            auto_delete_days NUMBER,
            
            -- معلومات النظام
            is_system NUMBER(1) DEFAULT 0,
            is_active NUMBER(1) DEFAULT 1,
            sort_order NUMBER DEFAULT 0,
            created_at DATE DEFAULT SYSDATE,
            
            CONSTRAINT fk_email_folders_account FOREIGN KEY (account_id) REFERENCES email_accounts(id),
            CONSTRAINT fk_email_folders_parent FOREIGN KEY (parent_id) REFERENCES email_folders(id)
        )
        """
        
        # 3. جدول رسائل البريد الإلكتروني
        email_messages_table = """
        CREATE TABLE email_messages (
            id NUMBER PRIMARY KEY,
            account_id NUMBER NOT NULL,
            folder_id NUMBER NOT NULL,
            
            -- معرفات الرسالة
            message_id VARCHAR2(255) UNIQUE,
            thread_id VARCHAR2(255),
            in_reply_to VARCHAR2(255),
            
            -- معلومات الرسالة
            subject VARCHAR2(500),
            sender_email VARCHAR2(255) NOT NULL,
            sender_name VARCHAR2(255),
            
            -- المستقبلون
            to_emails CLOB,
            cc_emails CLOB,
            bcc_emails CLOB,
            
            -- محتوى الرسالة
            body_text CLOB,
            body_html CLOB,
            
            -- معلومات إضافية
            priority VARCHAR2(20) DEFAULT 'normal',
            size_bytes NUMBER DEFAULT 0,
            has_attachments NUMBER(1) DEFAULT 0,
            
            -- حالة الرسالة
            is_read NUMBER(1) DEFAULT 0,
            is_starred NUMBER(1) DEFAULT 0,
            is_important NUMBER(1) DEFAULT 0,
            is_spam NUMBER(1) DEFAULT 0,
            is_deleted NUMBER(1) DEFAULT 0,
            
            -- تواريخ
            sent_at DATE,
            received_at DATE,
            read_at DATE,
            created_at DATE DEFAULT SYSDATE,
            updated_at DATE DEFAULT SYSDATE,
            
            CONSTRAINT fk_email_messages_account FOREIGN KEY (account_id) REFERENCES email_accounts(id),
            CONSTRAINT fk_email_messages_folder FOREIGN KEY (folder_id) REFERENCES email_folders(id)
        )
        """
        
        # 4. جدول مرفقات البريد الإلكتروني
        email_attachments_table = """
        CREATE TABLE email_attachments (
            id NUMBER PRIMARY KEY,
            message_id NUMBER NOT NULL,
            
            -- معلومات الملف
            filename VARCHAR2(255) NOT NULL,
            content_type VARCHAR2(100),
            size_bytes NUMBER DEFAULT 0,
            
            -- تخزين الملف
            file_path VARCHAR2(500),
            content_id VARCHAR2(255),
            
            -- معلومات إضافية
            is_inline NUMBER(1) DEFAULT 0,
            created_at DATE DEFAULT SYSDATE,
            
            CONSTRAINT fk_email_attachments_message FOREIGN KEY (message_id) REFERENCES email_messages(id)
        )
        """
        
        # 5. جدول جهات اتصال البريد الإلكتروني
        email_contacts_table = """
        CREATE TABLE email_contacts (
            id NUMBER PRIMARY KEY,
            user_id NUMBER NOT NULL,
            
            -- معلومات الاتصال
            email_address VARCHAR2(255) NOT NULL,
            display_name VARCHAR2(255),
            first_name VARCHAR2(100),
            last_name VARCHAR2(100),
            
            -- معلومات إضافية
            company VARCHAR2(255),
            phone VARCHAR2(50),
            notes CLOB,
            
            -- إعدادات
            is_favorite NUMBER(1) DEFAULT 0,
            is_blocked NUMBER(1) DEFAULT 0,
            
            -- إحصائيات
            emails_sent NUMBER DEFAULT 0,
            emails_received NUMBER DEFAULT 0,
            last_contact DATE,
            
            -- معلومات النظام
            created_at DATE DEFAULT SYSDATE,
            updated_at DATE DEFAULT SYSDATE,
            
            CONSTRAINT fk_email_contacts_user FOREIGN KEY (user_id) REFERENCES users(id)
        )
        """
        
        # 6. جدول قوالب البريد الإلكتروني
        email_templates_table = """
        CREATE TABLE email_templates (
            id NUMBER PRIMARY KEY,
            user_id NUMBER NOT NULL,
            
            -- معلومات القالب
            name VARCHAR2(255) NOT NULL,
            description CLOB,
            category VARCHAR2(100),
            
            -- محتوى القالب
            subject_template VARCHAR2(500),
            body_template CLOB NOT NULL,
            
            -- إعدادات
            is_public NUMBER(1) DEFAULT 0,
            is_system NUMBER(1) DEFAULT 0,
            usage_count NUMBER DEFAULT 0,
            
            -- معلومات النظام
            created_at DATE DEFAULT SYSDATE,
            updated_at DATE DEFAULT SYSDATE,
            
            CONSTRAINT fk_email_templates_user FOREIGN KEY (user_id) REFERENCES users(id)
        )
        """
        
        # 7. جدول تصنيفات البريد الإلكتروني
        email_labels_table = """
        CREATE TABLE email_labels (
            id NUMBER PRIMARY KEY,
            user_id NUMBER NOT NULL,
            
            -- معلومات التصنيف
            name VARCHAR2(100) NOT NULL,
            color VARCHAR2(7) DEFAULT '#007bff',
            
            -- معلومات النظام
            created_at DATE DEFAULT SYSDATE,
            
            CONSTRAINT fk_email_labels_user FOREIGN KEY (user_id) REFERENCES users(id)
        )
        """
        
        # 8. جدول الربط بين الرسائل والتصنيفات
        email_message_labels_table = """
        CREATE TABLE email_message_labels (
            message_id NUMBER NOT NULL,
            label_id NUMBER NOT NULL,
            
            PRIMARY KEY (message_id, label_id),
            CONSTRAINT fk_email_msg_labels_message FOREIGN KEY (message_id) REFERENCES email_messages(id),
            CONSTRAINT fk_email_msg_labels_label FOREIGN KEY (label_id) REFERENCES email_labels(id)
        )
        """
        
        # 9. جدول قواعد تصفية البريد الإلكتروني
        email_rules_table = """
        CREATE TABLE email_rules (
            id NUMBER PRIMARY KEY,
            user_id NUMBER NOT NULL,
            
            -- معلومات القاعدة
            name VARCHAR2(255) NOT NULL,
            description CLOB,
            
            -- شروط القاعدة (JSON)
            conditions CLOB NOT NULL,
            actions CLOB NOT NULL,
            
            -- إعدادات
            is_active NUMBER(1) DEFAULT 1,
            priority NUMBER DEFAULT 0,
            
            -- إحصائيات
            applied_count NUMBER DEFAULT 0,
            last_applied DATE,
            
            -- معلومات النظام
            created_at DATE DEFAULT SYSDATE,
            updated_at DATE DEFAULT SYSDATE,
            
            CONSTRAINT fk_email_rules_user FOREIGN KEY (user_id) REFERENCES users(id)
        )
        """
        
        # قائمة الجداول للإنشاء
        tables = [
            ("email_accounts", email_accounts_table),
            ("email_folders", email_folders_table),
            ("email_messages", email_messages_table),
            ("email_attachments", email_attachments_table),
            ("email_contacts", email_contacts_table),
            ("email_templates", email_templates_table),
            ("email_labels", email_labels_table),
            ("email_message_labels", email_message_labels_table),
            ("email_rules", email_rules_table)
        ]
        
        # إنشاء الجداول
        for table_name, table_sql in tables:
            try:
                # فحص وجود الجدول
                check_query = """
                SELECT COUNT(*) FROM user_tables WHERE table_name = UPPER(:1)
                """
                result = db.execute_query(check_query, [table_name])
                
                if result[0][0] == 0:
                    # إنشاء الجدول
                    db.execute_update(table_sql)
                    logger.info(f"✅ تم إنشاء جدول {table_name}")
                else:
                    logger.info(f"⚠️ جدول {table_name} موجود مسبقاً")
                    
            except Exception as e:
                logger.error(f"❌ خطأ في إنشاء جدول {table_name}: {e}")
        
        # إنشاء Sequences
        sequences = [
            "email_accounts_seq",
            "email_folders_seq", 
            "email_messages_seq",
            "email_attachments_seq",
            "email_contacts_seq",
            "email_templates_seq",
            "email_labels_seq",
            "email_rules_seq"
        ]
        
        for seq_name in sequences:
            try:
                # فحص وجود Sequence
                check_seq = """
                SELECT COUNT(*) FROM user_sequences WHERE sequence_name = UPPER(:1)
                """
                result = db.execute_query(check_seq, [seq_name])
                
                if result[0][0] == 0:
                    # إنشاء Sequence
                    create_seq = f"CREATE SEQUENCE {seq_name} START WITH 1 INCREMENT BY 1"
                    db.execute_update(create_seq)
                    logger.info(f"✅ تم إنشاء sequence {seq_name}")
                else:
                    logger.info(f"⚠️ sequence {seq_name} موجود مسبقاً")
                    
            except Exception as e:
                logger.error(f"❌ خطأ في إنشاء sequence {seq_name}: {e}")
        
        # إنشاء فهارس للأداء
        indexes = [
            "CREATE INDEX idx_email_messages_account ON email_messages(account_id)",
            "CREATE INDEX idx_email_messages_folder ON email_messages(folder_id)",
            "CREATE INDEX idx_email_messages_sender ON email_messages(sender_email)",
            "CREATE INDEX idx_email_messages_received ON email_messages(received_at)",
            "CREATE INDEX idx_email_messages_read ON email_messages(is_read)",
            "CREATE INDEX idx_email_contacts_user ON email_contacts(user_id)",
            "CREATE INDEX idx_email_contacts_email ON email_contacts(email_address)",
            "CREATE INDEX idx_email_folders_account ON email_folders(account_id)"
        ]
        
        for index_sql in indexes:
            try:
                db.execute_update(index_sql)
                logger.info(f"✅ تم إنشاء فهرس")
            except Exception as e:
                if "already exists" not in str(e).lower():
                    logger.error(f"❌ خطأ في إنشاء فهرس: {e}")
        
        db.commit()
        logger.info("🎉 تم إنشاء جداول نظام البريد الإلكتروني بنجاح!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء جداول البريد الإلكتروني: {e}")
        db.rollback()
        return False
        
    finally:
        db.close()

if __name__ == "__main__":
    create_email_tables()
