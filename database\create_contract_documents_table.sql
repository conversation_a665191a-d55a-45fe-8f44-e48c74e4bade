-- إن<PERSON>اء جدول مستندات العقود
-- Contract Documents Table Creation Script

-- حذف الجدول إذا كان موجوداً (للاختبار فقط)
-- DROP TABLE contract_documents;

-- إن<PERSON><PERSON><PERSON> جدول مستندات العقود
CREATE TABLE contract_documents (
    id NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    contract_id NUMBER NOT NULL,
    title VARCHAR2(255) NOT NULL,
    type VARCHAR2(50) DEFAULT 'other',
    filename VARCHAR2(500) NOT NULL,
    original_filename VARCHAR2(255),
    file_size NUMBER DEFAULT 0,
    file_path VARCHAR2(1000),
    description CLOB,
    created_by VARCHAR2(100),
    created_at DATE DEFAULT SYSDATE,
    updated_by VARCHAR2(100),
    updated_at DATE,
    is_active NUMBER(1) DEFAULT 1,
    
    -- قيود المرجعية
    CONSTRAINT fk_contract_documents_contract 
        FOREIGN KEY (contract_id) 
        REFERENCES contracts(contract_id) 
        ON DELETE CASCADE,
    
    -- قيود التحقق
    CONSTRAINT chk_contract_documents_type 
        CHECK (type IN ('contract', 'attachment', 'amendment', 'correspondence', 'other')),
    
    CONSTRAINT chk_contract_documents_active 
        CHECK (is_active IN (0, 1))
);

-- إنشاء فهارس للأداء
CREATE INDEX idx_contract_documents_contract_id ON contract_documents(contract_id);
CREATE INDEX idx_contract_documents_type ON contract_documents(type);
CREATE INDEX idx_contract_documents_created_at ON contract_documents(created_at);
CREATE INDEX idx_contract_documents_active ON contract_documents(is_active);

-- إنشاء trigger للتحديث التلقائي لتاريخ التعديل
CREATE OR REPLACE TRIGGER trg_contract_documents_updated_at
    BEFORE UPDATE ON contract_documents
    FOR EACH ROW
BEGIN
    :NEW.updated_at := SYSDATE;
END;
/

-- إضافة تعليقات على الجدول والأعمدة
COMMENT ON TABLE contract_documents IS 'جدول مستندات العقود - يحتوي على جميع المستندات المرفقة بالعقود';

COMMENT ON COLUMN contract_documents.id IS 'معرف المستند الفريد';
COMMENT ON COLUMN contract_documents.contract_id IS 'معرف العقد المرتبط بالمستند';
COMMENT ON COLUMN contract_documents.title IS 'عنوان المستند';
COMMENT ON COLUMN contract_documents.type IS 'نوع المستند (contract, attachment, amendment, correspondence, other)';
COMMENT ON COLUMN contract_documents.filename IS 'اسم الملف المحفوظ في النظام';
COMMENT ON COLUMN contract_documents.original_filename IS 'اسم الملف الأصلي';
COMMENT ON COLUMN contract_documents.file_size IS 'حجم الملف بالبايت';
COMMENT ON COLUMN contract_documents.file_path IS 'مسار الملف في النظام';
COMMENT ON COLUMN contract_documents.description IS 'وصف المستند';
COMMENT ON COLUMN contract_documents.created_by IS 'المستخدم الذي أضاف المستند';
COMMENT ON COLUMN contract_documents.created_at IS 'تاريخ إضافة المستند';
COMMENT ON COLUMN contract_documents.updated_by IS 'المستخدم الذي عدل المستند';
COMMENT ON COLUMN contract_documents.updated_at IS 'تاريخ آخر تعديل';
COMMENT ON COLUMN contract_documents.is_active IS 'حالة المستند (1=نشط، 0=غير نشط)';

-- إدراج بيانات تجريبية (اختياري)
/*
INSERT INTO contract_documents (contract_id, title, type, filename, file_size, description, created_by)
VALUES (1, 'نسخة العقد الأصلية', 'contract', 'contract_1_20250805_120000_original.pdf', 1024000, 'نسخة العقد الأصلية الموقعة', 'admin');

INSERT INTO contract_documents (contract_id, title, type, filename, file_size, description, created_by)
VALUES (1, 'مرفق تقني', 'attachment', 'contract_1_20250805_120100_technical_specs.docx', 512000, 'المواصفات التقنية للأصناف', 'admin');

COMMIT;
*/

-- التحقق من إنشاء الجدول
SELECT 'تم إنشاء جدول contract_documents بنجاح' AS status FROM dual;

-- عرض هيكل الجدول
DESC contract_documents;
