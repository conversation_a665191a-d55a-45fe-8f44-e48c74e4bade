{% extends "base.html" %}

{% block title %}لوحة الأتمتة الاحترافية{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان الرئيسي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">🤖 لوحة الأتمتة الاحترافية</h2>
                    <p class="text-muted mb-0">نظام أتمتة متقدم لإنشاء وإرسال أوامر التسليم تلقائياً</p>
                </div>
                <div class="d-flex gap-2">
                    <button id="refreshBtn" class="btn btn-outline-primary">
                        <i class="fas fa-sync-alt me-1"></i>تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- حالة الخدمة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        حالة خدمة الأتمتة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="status-indicator me-3" id="statusIndicator">
                                    <div class="spinner-border spinner-border-sm text-secondary" role="status"></div>
                                </div>
                                <div>
                                    <h6 class="mb-1">حالة الخدمة</h6>
                                    <span id="serviceStatus" class="text-muted">جاري التحقق...</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <button id="startBtn" class="btn btn-success" disabled>
                                    <i class="fas fa-play me-1"></i>تشغيل
                                </button>
                                <button id="stopBtn" class="btn btn-danger" disabled>
                                    <i class="fas fa-stop me-1"></i>إيقاف
                                </button>
                                <button id="testBtn" class="btn btn-warning" disabled>
                                    <i class="fas fa-flask me-1"></i>اختبار
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="mb-2">
                        <i class="fas fa-calendar-day fa-2x text-primary"></i>
                    </div>
                    <h4 class="mb-1" id="todayOrders">-</h4>
                    <small class="text-muted">أوامر اليوم</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="mb-2">
                        <i class="fas fa-calendar-week fa-2x text-success"></i>
                    </div>
                    <h4 class="mb-1" id="weekOrders">-</h4>
                    <small class="text-muted">أوامر الأسبوع</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="mb-2">
                        <i class="fas fa-clock fa-2x text-warning"></i>
                    </div>
                    <h4 class="mb-1" id="pendingShipments">-</h4>
                    <small class="text-muted">شحنات معلقة</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="mb-2">
                        <i class="fas fa-business-time fa-2x text-info"></i>
                    </div>
                    <h6 class="mb-1" id="workingHours">-</h6>
                    <small class="text-muted">ساعات العمل</small>
                </div>
            </div>
        </div>
    </div>

    <!-- الإعدادات والسجلات -->
    <div class="row">
        <!-- إعدادات الأتمتة -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات الأتمتة
                    </h6>
                </div>
                <div class="card-body">
                    <form id="settingsForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">فترة الفحص (دقائق)</label>
                                <input type="number" class="form-control" id="checkInterval" min="1" max="60">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">أقصى أوامر لكل دورة</label>
                                <input type="number" class="form-control" id="maxOrders" min="1" max="50">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">بداية ساعات العمل</label>
                                <input type="time" class="form-control" id="workStart">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">نهاية ساعات العمل</label>
                                <input type="time" class="form-control" id="workEnd">
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="autoCreate">
                                <label class="form-check-label" for="autoCreate">
                                    إنشاء أوامر تلقائي
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="autoSend">
                                <label class="form-check-label" for="autoSend">
                                    إرسال إشعارات تلقائي
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="weekendEnabled">
                                <label class="form-check-label" for="weekendEnabled">
                                    العمل في نهاية الأسبوع
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>حفظ الإعدادات
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- السجلات -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        سجلات الأتمتة
                    </h6>
                    <button id="refreshLogsBtn" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div id="logsContainer" style="height: 400px; overflow-y: auto;">
                        <div class="text-center text-muted">
                            <i class="fas fa-spinner fa-spin"></i>
                            جاري تحميل السجلات...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الشحنات المعلقة -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-ship me-2"></i>
                        الشحنات المعلقة (تحتاج أوامر تسليم)
                    </h6>
                    <button id="refreshShipmentsBtn" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-sync-alt me-1"></i>تحديث
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الشحنة</th>
                                    <th>المخلص</th>
                                    <th>الهاتف</th>
                                    <th>ميناء التفريغ</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الوصول</th>
                                </tr>
                            </thead>
                            <tbody id="pendingShipmentsTable">
                                <tr>
                                    <td colspan="6" class="text-center text-muted">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        جاري تحميل الشحنات المعلقة...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toast للإشعارات -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="notificationToast" class="toast" role="alert">
        <div class="toast-header">
            <i id="toastIcon" class="fas fa-info-circle text-primary me-2"></i>
            <strong class="me-auto" id="toastTitle">إشعار</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" id="toastMessage">
            رسالة الإشعار
        </div>
    </div>
</div>

<style>
.status-indicator {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-running {
    background-color: #d4edda;
    border: 2px solid #28a745;
}

.status-stopped {
    background-color: #f8d7da;
    border: 2px solid #dc3545;
}

.status-error {
    background-color: #fff3cd;
    border: 2px solid #ffc107;
}

.log-entry {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    padding: 4px 8px;
    margin-bottom: 2px;
    border-radius: 4px;
    background-color: #f8f9fa;
    border-left: 3px solid #dee2e6;
}

.log-entry.info {
    border-left-color: #17a2b8;
}

.log-entry.success {
    border-left-color: #28a745;
}

.log-entry.warning {
    border-left-color: #ffc107;
}

.log-entry.error {
    border-left-color: #dc3545;
}

.bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // عناصر DOM
    const statusIndicator = document.getElementById('statusIndicator');
    const serviceStatus = document.getElementById('serviceStatus');
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');
    const testBtn = document.getElementById('testBtn');
    const refreshBtn = document.getElementById('refreshBtn');
    const settingsForm = document.getElementById('settingsForm');

    // تحميل البيانات الأولية
    loadAutomationStatus();
    loadAutomationSettings();
    loadStatistics();
    loadLogs();
    loadPendingShipments();

    // تحديث تلقائي كل 30 ثانية
    setInterval(() => {
        loadAutomationStatus();
        loadStatistics();
    }, 30000);

    // معالجات الأحداث
    startBtn.addEventListener('click', startAutomation);
    stopBtn.addEventListener('click', stopAutomation);
    testBtn.addEventListener('click', testAutomation);
    refreshBtn.addEventListener('click', refreshAll);
    settingsForm.addEventListener('submit', saveSettings);

    document.getElementById('refreshLogsBtn').addEventListener('click', loadLogs);
    document.getElementById('refreshShipmentsBtn').addEventListener('click', loadPendingShipments);

    // تحميل حالة الأتمتة
    async function loadAutomationStatus() {
        try {
            const response = await fetch('/api/automation/status');
            const data = await response.json();

            if (data.success) {
                updateStatusDisplay(data.status);
            } else {
                showNotification('خطأ في تحميل حالة الأتمتة', 'error');
            }
        } catch (error) {
            console.error('Error loading automation status:', error);
            showNotification('خطأ في الاتصال', 'error');
        }
    }

    // تحديث عرض الحالة
    function updateStatusDisplay(status) {
        const isRunning = status.running;
        const isWorking = status.working_hours;

        // تحديث المؤشر
        statusIndicator.innerHTML = '';
        statusIndicator.className = 'status-indicator me-3';

        if (isRunning) {
            statusIndicator.classList.add('status-running');
            statusIndicator.innerHTML = '<i class="fas fa-check text-success"></i>';
            serviceStatus.textContent = isWorking ? 'يعمل (ساعات العمل)' : 'يعمل (خارج ساعات العمل)';
            serviceStatus.className = 'text-success';
        } else {
            statusIndicator.classList.add('status-stopped');
            statusIndicator.innerHTML = '<i class="fas fa-times text-danger"></i>';
            serviceStatus.textContent = 'متوقف';
            serviceStatus.className = 'text-danger';
        }

        // تحديث الأزرار
        startBtn.disabled = isRunning;
        stopBtn.disabled = !isRunning;
        testBtn.disabled = false;

        // تحديث ساعات العمل
        document.getElementById('workingHours').textContent = isWorking ? 'نشط' : 'خارج العمل';
    }

    // بدء الأتمتة
    async function startAutomation() {
        try {
            startBtn.disabled = true;
            startBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التشغيل...';

            const response = await fetch('/api/automation/start', {
                method: 'POST'
            });
            const data = await response.json();

            if (data.success) {
                showNotification('تم بدء خدمة الأتمتة بنجاح', 'success');
                loadAutomationStatus();
            } else {
                showNotification(data.message, 'error');
            }
        } catch (error) {
            console.error('Error starting automation:', error);
            showNotification('خطأ في بدء الأتمتة', 'error');
        } finally {
            startBtn.innerHTML = '<i class="fas fa-play me-1"></i>تشغيل';
        }
    }

    // إيقاف الأتمتة
    async function stopAutomation() {
        try {
            stopBtn.disabled = true;
            stopBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الإيقاف...';

            const response = await fetch('/api/automation/stop', {
                method: 'POST'
            });
            const data = await response.json();

            if (data.success) {
                showNotification('تم إيقاف خدمة الأتمتة بنجاح', 'success');
                loadAutomationStatus();
            } else {
                showNotification(data.message, 'error');
            }
        } catch (error) {
            console.error('Error stopping automation:', error);
            showNotification('خطأ في إيقاف الأتمتة', 'error');
        } finally {
            stopBtn.innerHTML = '<i class="fas fa-stop me-1"></i>إيقاف';
        }
    }

    // اختبار الأتمتة
    async function testAutomation() {
        try {
            testBtn.disabled = true;
            testBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الاختبار...';

            const response = await fetch('/api/automation/test', {
                method: 'POST'
            });
            const data = await response.json();

            if (data.success) {
                showNotification('تم تشغيل دورة اختبار بنجاح', 'success');
                setTimeout(() => {
                    loadStatistics();
                    loadLogs();
                    loadPendingShipments();
                }, 2000);
            } else {
                showNotification(data.message, 'error');
            }
        } catch (error) {
            console.error('Error testing automation:', error);
            showNotification('خطأ في اختبار الأتمتة', 'error');
        } finally {
            testBtn.disabled = false;
            testBtn.innerHTML = '<i class="fas fa-flask me-1"></i>اختبار';
        }
    }

    // تحديث شامل
    function refreshAll() {
        loadAutomationStatus();
        loadStatistics();
        loadLogs();
        loadPendingShipments();
        showNotification('تم تحديث البيانات', 'info');
    }

    // عرض الإشعارات
    function showNotification(message, type = 'info') {
        const toast = document.getElementById('notificationToast');
        const toastIcon = document.getElementById('toastIcon');
        const toastTitle = document.getElementById('toastTitle');
        const toastMessage = document.getElementById('toastMessage');

        // تحديد الأيقونة واللون
        const config = {
            success: { icon: 'fas fa-check-circle text-success', title: 'نجح' },
            error: { icon: 'fas fa-exclamation-triangle text-danger', title: 'خطأ' },
            warning: { icon: 'fas fa-exclamation-circle text-warning', title: 'تحذير' },
            info: { icon: 'fas fa-info-circle text-primary', title: 'معلومات' }
        };

        const typeConfig = config[type] || config.info;

        toastIcon.className = typeConfig.icon;
        toastTitle.textContent = typeConfig.title;
        toastMessage.textContent = message;

        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }

    // تحميل الإعدادات
    async function loadAutomationSettings() {
        try {
            const response = await fetch('/api/automation/settings');
            const data = await response.json();

            if (data.success) {
                const settings = data.settings;
                document.getElementById('checkInterval').value = settings.check_interval_minutes || 5;
                document.getElementById('maxOrders').value = settings.max_orders_per_batch || 10;
                document.getElementById('workStart').value = settings.working_hours_start || '08:00';
                document.getElementById('workEnd').value = settings.working_hours_end || '18:00';
                document.getElementById('autoCreate').checked = settings.auto_create_orders || false;
                document.getElementById('autoSend').checked = settings.auto_send_notifications || false;
                document.getElementById('weekendEnabled').checked = settings.weekend_enabled || false;
            }
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }

    // حفظ الإعدادات
    async function saveSettings(e) {
        e.preventDefault();

        try {
            const settings = {
                check_interval_minutes: parseInt(document.getElementById('checkInterval').value),
                max_orders_per_batch: parseInt(document.getElementById('maxOrders').value),
                working_hours_start: document.getElementById('workStart').value,
                working_hours_end: document.getElementById('workEnd').value,
                auto_create_orders: document.getElementById('autoCreate').checked,
                auto_send_notifications: document.getElementById('autoSend').checked,
                weekend_enabled: document.getElementById('weekendEnabled').checked
            };

            const response = await fetch('/api/automation/settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(settings)
            });

            const data = await response.json();

            if (data.success) {
                showNotification('تم حفظ الإعدادات بنجاح', 'success');
            } else {
                showNotification(data.message, 'error');
            }
        } catch (error) {
            console.error('Error saving settings:', error);
            showNotification('خطأ في حفظ الإعدادات', 'error');
        }
    }

    // تحميل الإحصائيات
    async function loadStatistics() {
        try {
            const response = await fetch('/api/automation/statistics');
            const data = await response.json();

            if (data.success) {
                const stats = data.statistics;
                document.getElementById('todayOrders').textContent = stats.today_orders || 0;
                document.getElementById('weekOrders').textContent = stats.week_orders || 0;
                document.getElementById('pendingShipments').textContent = stats.pending_shipments || 0;
            }
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }

    // تحميل السجلات
    async function loadLogs() {
        try {
            const response = await fetch('/api/automation/logs');
            const data = await response.json();

            const container = document.getElementById('logsContainer');

            if (data.success && data.logs.length > 0) {
                container.innerHTML = data.logs.map(log => {
                    const logClass = getLogClass(log);
                    return `<div class="log-entry ${logClass}">${log}</div>`;
                }).join('');

                // التمرير إلى الأسفل
                container.scrollTop = container.scrollHeight;
            } else {
                container.innerHTML = '<div class="text-center text-muted">لا توجد سجلات متاحة</div>';
            }
        } catch (error) {
            console.error('Error loading logs:', error);
            document.getElementById('logsContainer').innerHTML = '<div class="text-center text-danger">خطأ في تحميل السجلات</div>';
        }
    }

    // تحديد نوع السجل
    function getLogClass(log) {
        if (log.includes('ERROR') || log.includes('❌')) return 'error';
        if (log.includes('WARNING') || log.includes('⚠️')) return 'warning';
        if (log.includes('SUCCESS') || log.includes('✅')) return 'success';
        return 'info';
    }

    // تحميل الشحنات المعلقة
    async function loadPendingShipments() {
        try {
            const response = await fetch('/api/automation/pending-shipments');
            const data = await response.json();

            const tbody = document.getElementById('pendingShipmentsTable');

            if (data.success && data.shipments.length > 0) {
                tbody.innerHTML = data.shipments.map(shipment => `
                    <tr>
                        <td>${shipment.shipment_number || 'غير محدد'}</td>
                        <td>${shipment.agent_name || 'غير محدد'}</td>
                        <td>${shipment.agent_mobile || shipment.agent_phone || 'غير محدد'}</td>
                        <td>${shipment.port_of_discharge || 'غير محدد'}</td>
                        <td>
                            <span class="badge bg-warning">${shipment.shipment_status || 'غير محدد'}</span>
                        </td>
                        <td>${shipment.arrival_date || 'غير محدد'}</td>
                    </tr>
                `).join('');
            } else {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center text-muted">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            لا توجد شحنات معلقة - جميع الشحنات لديها أوامر تسليم
                        </td>
                    </tr>
                `;
            }
        } catch (error) {
            console.error('Error loading pending shipments:', error);
            document.getElementById('pendingShipmentsTable').innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        خطأ في تحميل الشحنات المعلقة
                    </td>
                </tr>
            `;
        }
    }
});
</script>

{% endblock %}
