{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-truck me-2"></i>
                    {{ title }}
                </h2>
                <a href="{{ url_for('goods_receipt.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للقائمة
                </a>
            </div>

            <!-- Form -->
            <div class="card">
                <div class="card-body">
                    <form method="POST" novalidate>
                        {{ form.hidden_tag() }}
                        
                        <!-- معلومات أساسية -->
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                {{ form.receipt_number.label(class="form-label required") }}
                                {{ form.receipt_number(class="form-control") }}
                                {% if form.receipt_number.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.receipt_number.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                {{ form.purchase_order_id.label(class="form-label required") }}
                                {{ form.purchase_order_id(class="form-select") }}
                                {% if form.purchase_order_id.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.purchase_order_id.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                {{ form.received_by.label(class="form-label required") }}
                                {{ form.received_by(class="form-control") }}
                                {% if form.received_by.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.received_by.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                {{ form.receipt_date.label(class="form-label required") }}
                                {{ form.receipt_date(class="form-control") }}
                                {% if form.receipt_date.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.receipt_date.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                {{ form.delivery_note_number.label(class="form-label") }}
                                {{ form.delivery_note_number(class="form-control") }}
                            </div>

                            <div class="col-md-4 mb-3">
                                {{ form.quality_check_status.label(class="form-label") }}
                                {{ form.quality_check_status(class="form-select") }}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                {{ form.vehicle_number.label(class="form-label") }}
                                {{ form.vehicle_number(class="form-control") }}
                            </div>

                            <div class="col-md-4 mb-3">
                                {{ form.driver_name.label(class="form-label") }}
                                {{ form.driver_name(class="form-control") }}
                            </div>

                            <div class="col-md-4 mb-3">
                                {{ form.driver_phone.label(class="form-label") }}
                                {{ form.driver_phone(class="form-control") }}
                            </div>
                        </div>

                        <!-- عناصر الاستلام -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">عناصر الاستلام</h5>
                            </div>
                            <div class="card-body">
                                <div id="items-container">
                                    {% if form.items and form.items.entries %}
                                        {% for item_form in form.items %}
                                            <div class="item-row border rounded p-3 mb-3">
                                                <div class="d-flex justify-content-between align-items-center mb-3">
                                                    <h6 class="mb-0">عنصر رقم {{ loop.index }}</h6>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeItem(this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                                
                                                <div class="row">
                                                    <div class="col-md-4 mb-3">
                                                        <label class="form-label required">الصنف</label>
                                                        {{ item_form.item_id(class="form-select") }}
                                                    </div>
                                                    
                                                    <div class="col-md-2 mb-3">
                                                        <label class="form-label">الكمية المطلوبة</label>
                                                        {{ item_form.ordered_quantity(class="form-control", readonly=true) }}
                                                    </div>
                                                    
                                                    <div class="col-md-2 mb-3">
                                                        <label class="form-label required">الكمية المستلمة</label>
                                                        {{ item_form.received_quantity(class="form-control") }}
                                                    </div>
                                                    
                                                    <div class="col-md-2 mb-3">
                                                        <label class="form-label">الكمية المرفوضة</label>
                                                        {{ item_form.rejected_quantity(class="form-control") }}
                                                    </div>
                                                    
                                                    <div class="col-md-2 mb-3">
                                                        <label class="form-label">الوحدة</label>
                                                        {{ item_form.unit(class="form-control", readonly=true) }}
                                                    </div>
                                                    
                                                    <div class="col-md-6 mb-3">
                                                        <label class="form-label">حالة الجودة</label>
                                                        {{ item_form.quality_status(class="form-select") }}
                                                    </div>
                                                    
                                                    <div class="col-md-6 mb-3">
                                                        <label class="form-label">سبب الرفض</label>
                                                        {{ item_form.rejection_reason(class="form-control") }}
                                                    </div>
                                                    
                                                    <div class="col-md-12 mb-3">
                                                        <label class="form-label">ملاحظات</label>
                                                        {{ item_form.notes(class="form-control") }}
                                                    </div>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    {% else %}
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>
                                            لا توجد عناصر للاستلام. اختر أمر شراء أولاً أو أضف عناصر يدوياً.
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <button type="button" class="btn btn-outline-primary" onclick="addItem()">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة عنصر
                                </button>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="row mt-4">
                            <div class="col-md-6 mb-3">
                                {{ form.warehouse_location.label(class="form-label") }}
                                {{ form.warehouse_location(class="form-control") }}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                {{ form.status.label(class="form-label required") }}
                                {{ form.status(class="form-select") }}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                {{ form.notes.label(class="form-label") }}
                                {{ form.notes(class="form-control") }}
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    {{ form.submit(class="btn btn-primary") }}
                                    <a href="{{ url_for('goods_receipt.index') }}" class="btn btn-secondary">إلغاء</a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script>
function addItem() {
    alert('ميزة إضافة عنصر قيد التطوير');
}

function removeItem(button) {
    if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
        button.closest('.item-row').remove();
    }
}

// تحديث الكميات تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    // عند تغيير أمر الشراء، تحديث العناصر
    const purchaseOrderSelect = document.querySelector('select[name="purchase_order_id"]');
    if (purchaseOrderSelect) {
        purchaseOrderSelect.addEventListener('change', function() {
            if (this.value) {
                // يمكن إضافة AJAX لتحديث العناصر
                console.log('تم اختيار أمر الشراء:', this.value);
            }
        });
    }
    
    // حساب الكميات تلقائياً
    document.querySelectorAll('input[name*="received_quantity"], input[name*="rejected_quantity"]').forEach(function(input) {
        input.addEventListener('input', function() {
            const row = this.closest('.item-row');
            const receivedQty = parseFloat(row.querySelector('input[name*="received_quantity"]').value) || 0;
            const rejectedQty = parseFloat(row.querySelector('input[name*="rejected_quantity"]').value) || 0;
            const orderedQty = parseFloat(row.querySelector('input[name*="ordered_quantity"]').value) || 0;
            
            // التحقق من أن المجموع لا يتجاوز الكمية المطلوبة
            if (receivedQty + rejectedQty > orderedQty) {
                this.setCustomValidity('مجموع الكميات المستلمة والمرفوضة لا يمكن أن يتجاوز الكمية المطلوبة');
            } else {
                this.setCustomValidity('');
            }
        });
    });
});
</script>

<style>
.item-row {
    background-color: #f8f9fa;
}

.item-row:hover {
    background-color: #e9ecef;
}

.required::after {
    content: " *";
    color: red;
}
</style>
{% endblock %}
