
<!DOCTYPE html>
<html>
<head>
    <title>اختبار API البيانات</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>اختبار API البيانات</h1>
    <button onclick="testAPI()">اختبار API</button>
    <div id="result"></div>
    
    <script>
    function testAPI() {
        console.log('بدء اختبار API...');
        
        fetch('/purchase-orders/api/items/data')
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Data received:', data);
                document.getElementById('result').innerHTML = 
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = 
                    '<p style="color: red;">خطأ: ' + error + '</p>';
            });
    }
    </script>
</body>
</html>
        