/**
 * نظام إدارة كشوفات الحساب - JavaScript
 * Statements Management System - JavaScript
 */

// متغيرات عامة
let statementsData = {
    statements: [],
    filteredStatements: [],
    suppliers: [],
    currentPage: 1,
    itemsPerPage: 10,
    totalPages: 0,
    selectedTemplate: 'detailed',
    filters: {
        search: '',
        status: 'all',
        type: 'all',
        period: 'all',
        dateFrom: ''
    }
};

/**
 * تحميل بيانات الكشوفات
 */
function loadStatementsData() {
    showLoadingState();
    
    $.ajax({
        url: '/suppliers/api/statements',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                statementsData.statements = response.statements || [];
                statementsData.filteredStatements = [...statementsData.statements];
                updateStatistics(response.statistics || {});
                displayStatements();
                updatePagination();
            } else {
                showError('خطأ في تحميل بيانات الكشوفات: ' + response.message);
                loadSampleStatementsData();
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في تحميل بيانات الكشوفات:', error);
            showError('خطأ في الاتصال بالخادم');
            loadSampleStatementsData();
        }
    });
}

/**
 * تحميل بيانات تجريبية للكشوفات
 */
function loadSampleStatementsData() {
    const sampleStatements = [
        {
            statement_id: 1,
            statement_number: 'STMT20240001',
            supplier_name: 'شركة الخليج للتجارة',
            supplier_code: 'SUP001',
            statement_date: '2024-09-01',
            period_from: '2024-08-01',
            period_to: '2024-08-31',
            statement_type: 'DETAILED',
            status: 'SENT',
            opening_balance: 120000.00,
            closing_balance: 150000.00,
            total_debits: 85000.00,
            total_credits: 55000.00,
            transaction_count: 12,
            output_format: 'PDF',
            file_size: 245,
            generated_by: 'أحمد محمد',
            generated_date: '2024-09-01T10:30:00',
            sent_date: '2024-09-01T14:15:00'
        },
        {
            statement_id: 2,
            statement_number: 'STMT20240002',
            supplier_name: 'مؤسسة النور للخدمات',
            supplier_code: 'SUP002',
            statement_date: '2024-09-02',
            period_from: '2024-08-01',
            period_to: '2024-08-31',
            statement_type: 'SUMMARY',
            status: 'GENERATED',
            opening_balance: -15000.00,
            closing_balance: -25000.00,
            total_debits: 35000.00,
            total_credits: 45000.00,
            transaction_count: 8,
            output_format: 'EXCEL',
            file_size: 156,
            generated_by: 'فاطمة علي',
            generated_date: '2024-09-02T09:45:00'
        },
        {
            statement_id: 3,
            statement_number: 'STMT20240003',
            supplier_name: 'شركة البناء المتطور',
            supplier_code: 'SUP003',
            statement_date: '2024-09-03',
            period_from: '2024-08-01',
            period_to: '2024-08-31',
            statement_type: 'AGING',
            status: 'DRAFT',
            opening_balance: 75000.00,
            closing_balance: 82000.00,
            total_debits: 25000.00,
            total_credits: 18000.00,
            transaction_count: 6,
            output_format: 'PDF',
            generated_by: 'محمد سالم',
            generated_date: '2024-09-03T16:20:00'
        }
    ];
    
    const sampleStats = {
        total_statements: 3,
        sent_statements: 1,
        draft_statements: 1,
        this_month_statements: 3
    };
    
    statementsData.statements = sampleStatements;
    statementsData.filteredStatements = [...sampleStatements];
    updateStatistics(sampleStats);
    displayStatements();
    updatePagination();
}

/**
 * تحميل قائمة الموردين
 */
function loadSuppliersList() {
    $.ajax({
        url: '/suppliers/api/suppliers-for-statements',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                statementsData.suppliers = response.suppliers || [];
                populateSuppliersDropdown();
            } else {
                loadSampleSuppliers();
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في تحميل قائمة الموردين:', error);
            loadSampleSuppliers();
        }
    });
}

/**
 * تحميل موردين تجريبيين
 */
function loadSampleSuppliers() {
    const sampleSuppliers = [
        { account_id: 1, supplier_code: 'SUP001', supplier_name: 'شركة الخليج للتجارة' },
        { account_id: 2, supplier_code: 'SUP002', supplier_name: 'مؤسسة النور للخدمات' },
        { account_id: 3, supplier_code: 'SUP003', supplier_name: 'شركة البناء المتطور' }
    ];
    
    statementsData.suppliers = sampleSuppliers;
    populateSuppliersDropdown();
}

/**
 * ملء قائمة الموردين المنسدلة
 */
function populateSuppliersDropdown() {
    const dropdown = $('#supplierSelect');
    dropdown.empty().append('<option value="">اختر المورد</option>');
    
    statementsData.suppliers.forEach(supplier => {
        dropdown.append(`
            <option value="${supplier.account_id}">
                ${supplier.supplier_name} (${supplier.supplier_code})
            </option>
        `);
    });
}

/**
 * تحديث الإحصائيات
 */
function updateStatistics(stats) {
    $('#totalStatements').text(stats.total_statements || 0);
    $('#sentStatements').text(stats.sent_statements || 0);
    $('#draftStatements').text(stats.draft_statements || 0);
    $('#thisMonthStatements').text(stats.this_month_statements || 0);
}

/**
 * عرض الكشوفات
 */
function displayStatements() {
    const container = $('#statementsList');
    
    if (!statementsData.filteredStatements || statementsData.filteredStatements.length === 0) {
        showEmptyState();
        return;
    }
    
    // حساب البيانات للصفحة الحالية
    const startIndex = (statementsData.currentPage - 1) * statementsData.itemsPerPage;
    const endIndex = startIndex + statementsData.itemsPerPage;
    const pageStatements = statementsData.filteredStatements.slice(startIndex, endIndex);
    
    let html = '';
    pageStatements.forEach(statement => {
        html += createStatementCard(statement);
    });
    
    container.html(html);
    updatePaginationInfo();
}

/**
 * إنشاء بطاقة كشف
 */
function createStatementCard(statement) {
    const statusClass = getStatusClass(statement.status);
    const statusText = getStatusText(statement.status);
    const typeText = getTypeText(statement.statement_type);
    const balanceClass = statement.closing_balance >= 0 ? 'text-success' : 'text-danger';
    
    return `
        <div class="statement-card ${statusClass}">
            <div class="statement-header">
                <div class="statement-info">
                    <h6>${statement.supplier_name}</h6>
                    <small class="text-muted">
                        ${statement.statement_number} | ${statement.supplier_code}
                    </small>
                </div>
                <div class="statement-status">
                    <span class="status-badge status-${statusClass}">${statusText}</span>
                </div>
            </div>
            
            <div class="statement-details">
                <div class="detail-item">
                    <div class="detail-value">${typeText}</div>
                    <div class="detail-label">نوع الكشف</div>
                </div>
                <div class="detail-item">
                    <div class="detail-value">${formatDate(statement.period_from)} - ${formatDate(statement.period_to)}</div>
                    <div class="detail-label">فترة الكشف</div>
                </div>
                <div class="detail-item">
                    <div class="detail-value ${balanceClass}">
                        ${formatCurrency(statement.closing_balance)}
                    </div>
                    <div class="detail-label">الرصيد الختامي</div>
                </div>
                <div class="detail-item">
                    <div class="detail-value text-primary">
                        ${statement.transaction_count}
                    </div>
                    <div class="detail-label">عدد المعاملات</div>
                </div>
                <div class="detail-item">
                    <div class="detail-value text-info">
                        ${statement.output_format}
                    </div>
                    <div class="detail-label">صيغة الملف</div>
                </div>
                <div class="detail-item">
                    <div class="detail-value text-warning">
                        ${statement.file_size || 0} KB
                    </div>
                    <div class="detail-label">حجم الملف</div>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <small class="text-muted">
                        <i class="fas fa-user"></i> ${statement.generated_by}
                    </small>
                </div>
                <div class="col-md-6">
                    <small class="text-muted">
                        <i class="fas fa-calendar"></i> ${formatDateTime(statement.generated_date)}
                    </small>
                </div>
            </div>
            
            <div class="statement-actions">
                <button class="btn btn-primary btn-sm-custom" onclick="viewStatement(${statement.statement_id})">
                    <i class="fas fa-eye"></i> عرض
                </button>
                <button class="btn btn-success btn-sm-custom" onclick="downloadStatement(${statement.statement_id})">
                    <i class="fas fa-download"></i> تحميل
                </button>
                ${statement.status !== 'SENT' ? 
                    `<button class="btn btn-info btn-sm-custom" onclick="sendStatement(${statement.statement_id})">
                        <i class="fas fa-paper-plane"></i> إرسال
                    </button>` : ''
                }
                <button class="btn btn-warning btn-sm-custom" onclick="duplicateStatement(${statement.statement_id})">
                    <i class="fas fa-copy"></i> نسخ
                </button>
                <button class="btn btn-outline-secondary btn-sm-custom" onclick="editStatement(${statement.statement_id})">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                ${statement.status === 'DRAFT' ? 
                    `<button class="btn btn-danger btn-sm-custom" onclick="deleteStatement(${statement.statement_id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>` : ''
                }
            </div>
        </div>
    `;
}

/**
 * فلترة الكشوفات
 */
function filterStatements() {
    // جمع معايير الفلترة
    statementsData.filters = {
        search: $('#searchInput').val().toLowerCase(),
        status: $('#statusFilter').val(),
        type: $('#typeFilter').val(),
        period: $('#periodFilter').val(),
        dateFrom: $('#dateFrom').val()
    };
    
    // تطبيق الفلاتر
    statementsData.filteredStatements = statementsData.statements.filter(statement => {
        // فلتر البحث
        if (statementsData.filters.search) {
            const searchText = statementsData.filters.search;
            const matchesSearch = 
                statement.supplier_name.toLowerCase().includes(searchText) ||
                statement.statement_number.toLowerCase().includes(searchText) ||
                statement.supplier_code.toLowerCase().includes(searchText);
            if (!matchesSearch) return false;
        }
        
        // فلتر الحالة
        if (statementsData.filters.status !== 'all' && statement.status !== statementsData.filters.status) {
            return false;
        }
        
        // فلتر النوع
        if (statementsData.filters.type !== 'all' && statement.statement_type !== statementsData.filters.type) {
            return false;
        }
        
        // فلتر التاريخ
        if (statementsData.filters.dateFrom) {
            const statementDate = new Date(statement.statement_date);
            const filterDate = new Date(statementsData.filters.dateFrom);
            if (statementDate < filterDate) return false;
        }
        
        return true;
    });
    
    // إعادة تعيين الصفحة الحالية
    statementsData.currentPage = 1;
    
    // عرض النتائج
    displayStatements();
    updatePagination();
}

/**
 * مسح الفلاتر
 */
function clearFilters() {
    $('#searchInput').val('');
    $('#statusFilter').val('all');
    $('#typeFilter').val('all');
    $('#periodFilter').val('all');
    $('#dateFrom').val('');
    
    statementsData.filteredStatements = [...statementsData.statements];
    statementsData.currentPage = 1;
    
    displayStatements();
    updatePagination();
}

/**
 * عرض نافذة إنشاء كشف جديد
 */
function showGenerateStatementModal() {
    // تعيين القالب الافتراضي
    $('.template-card').removeClass('selected');
    $('.template-card[data-template="detailed"]').addClass('selected');
    statementsData.selectedTemplate = 'detailed';
    
    $('#generateStatementModal').modal('show');
}

/**
 * إنشاء كشف جديد
 */
function generateStatement() {
    const formData = {
        supplier_id: $('#supplierSelect').val(),
        period_from: $('#periodFrom').val(),
        period_to: $('#periodTo').val(),
        template_type: $('.template-card.selected').data('template'),
        output_format: $('#outputFormat').val(),
        language: $('#language').val(),
        include_opening_balance: $('#includeOpeningBalance').is(':checked'),
        include_aging_analysis: $('#includeAgingAnalysis').is(':checked'),
        include_payment_history: $('#includePaymentHistory').is(':checked')
    };
    
    // التحقق من البيانات المطلوبة
    if (!formData.supplier_id || !formData.period_from || !formData.period_to) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }
    
    showNotification('جاري إنشاء الكشف...', 'info');
    
    $.ajax({
        url: '/suppliers/api/statements/generate',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                showNotification('تم إنشاء الكشف بنجاح', 'success');
                $('#generateStatementModal').modal('hide');
                loadStatementsData(); // إعادة تحميل البيانات
            } else {
                showNotification('خطأ في إنشاء الكشف: ' + response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في إنشاء الكشف:', error);
            showNotification('خطأ في الاتصال بالخادم', 'error');
        }
    });
}

// دوال الإجراءات
function viewStatement(statementId) {
    window.open(`/suppliers/statements/${statementId}/view`, '_blank');
}

function downloadStatement(statementId) {
    window.location.href = `/suppliers/statements/${statementId}/download`;
}

function sendStatement(statementId) {
    if (confirm('هل أنت متأكد من إرسال هذا الكشف للمورد؟')) {
        showNotification('سيتم إرسال الكشف قريباً', 'info');
    }
}

function duplicateStatement(statementId) {
    showNotification('سيتم نسخ الكشف قريباً', 'info');
}

function editStatement(statementId) {
    showNotification('سيتم فتح نافذة التعديل قريباً', 'info');
}

function deleteStatement(statementId) {
    if (confirm('هل أنت متأكد من حذف هذا الكشف؟')) {
        showNotification('تم حذف الكشف', 'warning');
    }
}

function refreshStatements() {
    showNotification('جاري تحديث البيانات...', 'info');
    loadStatementsData();
}

function manageTemplates() {
    showNotification('سيتم فتح إدارة القوالب قريباً', 'info');
}

// دوال التصفح والصفحات (مشابهة لما في accounts_management.js)
function updatePaginationInfo() {
    const total = statementsData.filteredStatements.length;
    const startIndex = (statementsData.currentPage - 1) * statementsData.itemsPerPage + 1;
    const endIndex = Math.min(startIndex + statementsData.itemsPerPage - 1, total);
    
    $('#showingFrom').text(total > 0 ? startIndex : 0);
    $('#showingTo').text(total > 0 ? endIndex : 0);
    $('#totalRecords').text(total);
}

function updatePagination() {
    const total = statementsData.filteredStatements.length;
    statementsData.totalPages = Math.ceil(total / statementsData.itemsPerPage);
    
    const pagination = $('#pagination');
    let html = '';
    
    // زر السابق
    html += `
        <li class="page-item ${statementsData.currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${statementsData.currentPage - 1})">السابق</a>
        </li>
    `;
    
    // أرقام الصفحات
    for (let i = 1; i <= statementsData.totalPages; i++) {
        if (i === statementsData.currentPage || 
            i === 1 || 
            i === statementsData.totalPages || 
            (i >= statementsData.currentPage - 1 && i <= statementsData.currentPage + 1)) {
            html += `
                <li class="page-item ${i === statementsData.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `;
        } else if (i === statementsData.currentPage - 2 || i === statementsData.currentPage + 2) {
            html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    // زر التالي
    html += `
        <li class="page-item ${statementsData.currentPage === statementsData.totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${statementsData.currentPage + 1})">التالي</a>
        </li>
    `;
    
    pagination.html(html);
}

function changePage(page) {
    if (page < 1 || page > statementsData.totalPages) return;
    
    statementsData.currentPage = page;
    displayStatements();
    updatePagination();
}

// دوال مساعدة
function showLoadingState() {
    $('#statementsList').html(`
        <div class="loading-container">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `);
}

function showEmptyState() {
    $('#statementsList').html(`
        <div class="empty-state">
            <i class="fas fa-file-invoice"></i>
            <h5>لا توجد كشوفات</h5>
            <p class="text-muted">لم يتم العثور على كشوفات حساب مطابقة للمعايير المحددة</p>
            <button class="btn btn-primary" onclick="showGenerateStatementModal()">إنشاء كشف جديد</button>
        </div>
    `);
}

function showError(message) {
    showNotification(message, 'error');
}

function getStatusClass(status) {
    const statusMap = {
        'GENERATED': 'generated',
        'SENT': 'sent',
        'DRAFT': 'draft'
    };
    return statusMap[status] || 'draft';
}

function getStatusText(status) {
    const statusMap = {
        'GENERATED': 'مُنشأ',
        'SENT': 'مُرسل',
        'DRAFT': 'مسودة'
    };
    return statusMap[status] || status;
}

function getTypeText(type) {
    const typeMap = {
        'DETAILED': 'تفصيلي',
        'SUMMARY': 'ملخص',
        'AGING': 'تحليل الاستحقاقات'
    };
    return typeMap[type] || type;
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount || 0) + ' ر.س';
}

function formatDate(dateString) {
    if (!dateString) return 'غير محدد';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function formatDateTime(dateString) {
    if (!dateString) return 'غير محدد';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'});
}

function showNotification(message, type = 'info') {
    if (typeof showAlert === 'function') {
        showAlert(message, type);
    } else {
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}
