<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة الأتمتة التلقائية - النظام المحاسبي المتقدم</title>

    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <!-- تم إزالة Chart.js لأنه لم يعد مطلوباً -->

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);
            min-height: 100vh;
            color: #333;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
        }

        .dashboard-container {
            min-height: 100vh;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 1;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.1),
                0 8px 16px rgba(0, 0, 0, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s;
        }

        .card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow:
                0 30px 60px rgba(0, 0, 0, 0.15),
                0 12px 24px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        .card:hover::before {
            left: 100%;
        }

        .header-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            color: white;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 400;
        }

        .stat-card {
            text-align: center;
            padding: 30px;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .stat-number {
            font-size: 3.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .stat-number::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 3px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .stat-label {
            color: #64748b;
            font-size: 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-icon {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 2rem;
            color: rgba(102, 126, 234, 0.2);
            transition: all 0.3s ease;
        }

        .card:hover .stat-icon {
            color: rgba(102, 126, 234, 0.4);
            transform: scale(1.1) rotate(5deg);
        }

        .rule-card {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid rgba(102, 126, 234, 0.1);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            position: relative;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .rule-card::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 0 2px 2px 0;
        }

        .rule-card:hover {
            transform: translateX(5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
            border-color: rgba(102, 126, 234, 0.3);
        }

        .rule-title {
            font-weight: 700;
            color: #1e293b;
            font-size: 1.1rem;
            margin-bottom: 5px;
        }

        .rule-description {
            color: #64748b;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .agent-info {
            border-left: 4px solid #28a745;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            transition: all 0.3s ease;
            border: 1px solid rgba(40, 167, 69, 0.2);
        }

        .agent-info:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            transform: translateX(3px);
            border-color: rgba(40, 167, 69, 0.4);
        }

        .status-badge {
            padding: 8px 16px;
            border-radius: 50px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .activity-item {
            padding: 15px;
            border-radius: 12px;
            background: rgba(248, 250, 252, 0.8);
            border: 1px solid rgba(226, 232, 240, 0.5);
            margin-bottom: 15px;
            transition: all 0.3s ease;
            position: relative;
        }

        .activity-item:hover {
            background: rgba(248, 250, 252, 1);
            border-color: rgba(102, 126, 234, 0.2);
            transform: translateX(5px);
        }

        .activity-icon {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            margin-left: 10px;
            flex-shrink: 0;
        }

        .activity-icon.success {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .activity-icon.primary {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
        }

        .activity-icon.warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
        }

        .activity-icon.info {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            color: white;
        }

        .activity-time {
            font-size: 0.8rem;
            color: #94a3b8;
            font-weight: 500;
        }

        .activity-text {
            color: #475569;
            font-weight: 500;
            line-height: 1.4;
        }

        .form-switch .form-check-input {
            width: 3rem;
            height: 1.5rem;
            border-radius: 3rem;
            background-color: #e2e8f0;
            border: none;
            transition: all 0.3s ease;
        }

        .form-switch .form-check-input:checked {
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .form-switch .form-check-input:focus {
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
        }

        .form-check-label {
            font-weight: 600;
            color: #475569;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .form-check-label:hover {
            color: #667eea;
        }

        .btn-modern {
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-modern.btn-light {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
        }

        .btn-modern.btn-outline-light {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            backdrop-filter: blur(10px);
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .section-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 25px;
            position: relative;
            padding-bottom: 10px;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .section-icon {
            margin-left: 10px;
            color: #667eea;
        }

        /* تأثيرات الحركة */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .card {
            animation: fadeInUp 0.6s ease-out;
        }

        .card:nth-child(1) { animation-delay: 0.1s; }
        .card:nth-child(2) { animation-delay: 0.2s; }
        .card:nth-child(3) { animation-delay: 0.3s; }
        .card:nth-child(4) { animation-delay: 0.4s; }

        /* تم إزالة CSS الخاص بالرسوم البيانية */

        .card-title {
            color: #1e293b;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .card-title i {
            color: #667eea;
            margin-left: 8px;
        }

        /* تحسين البطاقات الإحصائية */
        .stat-card {
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .stat-change {
            margin-top: 8px;
        }

        /* استجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .dashboard-container {
                padding: 15px;
            }

            .stat-number {
                font-size: 2.5rem;
            }

            .page-title {
                font-size: 2rem;
            }


        }
    </style>




</head>




<body>
    <div class="dashboard-container">
        <div class="container-fluid">
            <!-- رأس الصفحة -->
            <div class="header-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="mb-2">
                            <i class="fas fa-robot me-3"></i>
                            لوحة الأتمتة التلقائية
                        </h1>
                        <p class="mb-0 opacity-75">إدارة ومراقبة عمليات الأتمتة الذكية في نظام أوامر التسليم</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-outline-light me-2" onclick="toggleSidebar()" style="border-radius: 12px;">
                            <i class="fas fa-bars me-2"></i>
                            القائمة الرئيسية
                        </button>
                        <a href="{{ url_for('shipments.add_automation_rule') }}" class="btn btn-light btn-modern me-2">
                            <i class="fas fa-plus me-2"></i>
                            إضافة قاعدة جديدة
                        </a>
                        <a href="{{ url_for('shipments.automation_settings') }}" class="btn btn-outline-light btn-modern">
                            <i class="fas fa-cog me-2"></i>
                            الإعدادات
                        </a>
                    </div>
                </div>
            </div>

            <!-- الإحصائيات المتطورة والملونة -->
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="card stat-card" style="background: linear-gradient(135deg, #007bff, #0056b3); color: white; border: none;">
                        <div class="stat-icon" style="background: rgba(255,255,255,0.2);">
                            <i class="fas fa-chart-line" style="color: white;"></i>
                        </div>
                        <div class="stat-number" style="color: white; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">{{ automation_data.statistics.total_automated_orders }}</div>
                        <div class="stat-label" style="color: rgba(255,255,255,0.9); font-weight: 500;">إجمالي الأوامر المؤتمتة</div>
                        <div class="stat-change">
                            <small style="color: rgba(255,255,255,0.8);">+12% هذا الشهر</small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card stat-card" style="background: linear-gradient(135deg, #28a745, #1e7e34); color: white; border: none;">
                        <div class="stat-icon" style="background: rgba(255,255,255,0.2);">
                            <i class="fas fa-percentage" style="color: white;"></i>
                        </div>
                        <div class="stat-number" style="color: white; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">{{ automation_data.statistics.success_rate }}%</div>
                        <div class="stat-label" style="color: rgba(255,255,255,0.9); font-weight: 500;">معدل نجاح الأتمتة</div>
                        <div class="stat-change">
                            <small style="color: rgba(255,255,255,0.8);">+2.5% تحسن</small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card stat-card" style="background: linear-gradient(135deg, #6f42c1, #5a32a3); color: white; border: none;">
                        <div class="stat-icon" style="background: rgba(255,255,255,0.2);">
                            <i class="fas fa-cogs" style="color: white;"></i>
                        </div>
                        <div class="stat-number" style="color: white; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">{{ automation_data.statistics.active_rules }}</div>
                        <div class="stat-label" style="color: rgba(255,255,255,0.9); font-weight: 500;">القواعد النشطة</div>
                        <div class="stat-change">
                            <small style="color: rgba(255,255,255,0.8);">{{ automation_data.statistics.active_rules }} من 8</small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card stat-card" style="background: linear-gradient(135deg, #fd7e14, #e55a00); color: white; border: none;">
                        <div class="stat-icon" style="background: rgba(255,255,255,0.2);">
                            <i class="fas fa-calendar-day" style="color: white;"></i>
                        </div>
                        <div class="stat-number" style="color: white; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">{{ automation_data.statistics.processed_today }}</div>
                        <div class="stat-label" style="color: rgba(255,255,255,0.9); font-weight: 500;">معالج اليوم</div>
                        <div class="stat-change">
                            <small style="color: rgba(255,255,255,0.8);">{{ automation_data.statistics.processed_today }} أمر</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تم إزالة جميع الرسوم البيانية لتبسيط الواجهة والتركيز على القواعد والأنشطة -->

            <div class="row">
                <!-- قواعد الأتمتة -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="section-title">
                                <i class="fas fa-list-ul me-2"></i>
                                قواعد الأتمتة النشطة
                            </h4>

                            {% for rule in automation_data.rules %}
                            <div class="rule-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="flex-grow-1">
                                        <div class="rule-title">
                                            {{ rule.name }}

                                            <!-- نوع القاعدة -->
                                            {% if rule.rule_type == 'SIMPLE_RULE' %}
                                                <span class="badge bg-primary ms-2">قاعدة بسيطة</span>
                                            {% elif rule.rule_type == 'CONDITION_ONLY' %}
                                                <span class="badge bg-info ms-2">شرط فقط</span>
                                            {% elif rule.rule_type == 'ACTION_ONLY' %}
                                                <span class="badge bg-success ms-2">إجراء فقط</span>
                                            {% elif rule.rule_type == 'SCHEDULED' %}
                                                <span class="badge bg-secondary ms-2">مجدولة</span>
                                            {% endif %}

                                            <!-- مستوى الأولوية -->
                                            {% if rule.priority_level == 1 %}
                                                <span class="badge bg-danger ms-2">عالية</span>
                                            {% elif rule.priority_level == 2 %}
                                                <span class="badge bg-warning ms-2">متوسطة</span>
                                            {% elif rule.priority_level == 3 %}
                                                <span class="badge bg-light text-dark ms-2">منخفضة</span>
                                            {% endif %}
                                        </div>
                                        <div class="rule-description">{{ rule.description }}</div>

                                        <!-- عرض معلومات المخلص إذا كانت موجودة -->
                                        {% if rule.agent_info %}
                                        <div class="agent-info mt-2 p-2 bg-light rounded">
                                            <small class="text-primary">
                                                <i class="fas fa-user-tie me-1"></i>
                                                {% if rule.agent_info.auto_selection %}
                                                    <strong>اختيار تلقائي</strong> ({{ rule.agent_info.selection_criteria }})
                                                {% else %}
                                                    <strong>{{ rule.agent_info.agent_name }}</strong>
                                                    {% if rule.agent_info.agent_code %}({{ rule.agent_info.agent_code }}){% endif %}
                                                {% endif %}

                                                {% if rule.agent_info.branch_name %}
                                                    | <i class="fas fa-building me-1"></i>{{ rule.agent_info.branch_name }}
                                                {% endif %}

                                                {% if rule.agent_info.port_name %}
                                                    | <i class="fas fa-ship me-1"></i>{{ rule.agent_info.port_name }}
                                                {% endif %}
                                            </small>

                                            {% if rule.agent_info.assignment_notes %}
                                            <div class="mt-1">
                                                <small class="text-muted">
                                                    <i class="fas fa-sticky-note me-1"></i>{{ rule.agent_info.assignment_notes }}
                                                </small>
                                            </div>
                                            {% endif %}
                                        </div>
                                        {% endif %}

                                        <small class="text-muted">
                                            نجح: {{ rule.success_count }} | فشل: {{ rule.failure_count }} | آخر تنفيذ: {{ rule.last_executed }}
                                        </small>
                                    </div>
                                    <div class="d-flex align-items-center gap-2">
                                        <span class="status-badge">{{ rule.status == 'active' and 'نشط' or 'معطل' }}</span>

                                        <!-- أزرار الإجراءات -->
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary btn-sm"
                                                    onclick="editRule({{ rule.id }})"
                                                    title="تعديل القاعدة">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm"
                                                    onclick="deleteRule({{ rule.id }}, '{{ rule.name }}')"
                                                    title="حذف القاعدة">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}

                            {% if not automation_data.rules %}
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-info-circle fa-2x mb-2"></i>
                                <p>لا توجد قواعد أتمتة نشطة حالياً</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

            <!-- سجل الأنشطة -->
            <div class="col-lg-4">
                <div class="automation-card">
                    <h6 class="mb-3">
                        <i class="fas fa-history text-info me-2"></i>
                        سجل الأنشطة الحديثة
                    </h6>
                    
                    {% for activity in automation_data.activities %}
                    <div class="activity-item">
                        <div class="d-flex align-items-center">
                            <div class="activity-icon {{ activity.icon_type }}">
                                {% if activity.icon_type == 'success' %}
                                    <i class="fas fa-check"></i>
                                {% elif activity.icon_type == 'primary' %}
                                    <i class="fas fa-user"></i>
                                {% elif activity.icon_type == 'warning' %}
                                    <i class="fas fa-bell"></i>
                                {% elif activity.icon_type == 'info' %}
                                    <i class="fas fa-star"></i>
                                {% else %}
                                    <i class="fas fa-cog"></i>
                                {% endif %}
                            </div>
                            <div>
                                <div class="activity-text">{{ activity.description }}</div>
                                <div class="activity-time">{{ activity.time_ago }}</div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}

                    {% if not automation_data.activities %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-history fa-2x mb-2"></i>
                        <p>لا توجد أنشطة حديثة</p>
                    </div>
                    {% endif %}

                    <div class="text-center mt-3">
                        <a href="#" class="btn btn-sm btn-outline-info">
                            عرض السجل الكامل
                        </a>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Automation Dashboard JS المتطور -->
    <script>


        // دالة إظهار الإشعارات المتطورة
        function showAdvancedNotification(message, type, icon) {
            const colors = {
                success: { bg: '#10b981', border: '#059669' },
                error: { bg: '#ef4444', border: '#dc2626' },
                warning: { bg: '#f59e0b', border: '#d97706' },
                info: { bg: '#06b6d4', border: '#0891b2' }
            };

            const color = colors[type] || colors.info;

            const notificationHtml = `
                <div class="advanced-notification" style="
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 9999;
                    background: ${color.bg};
                    color: white;
                    padding: 15px 20px;
                    border-radius: 12px;
                    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
                    border-left: 4px solid ${color.border};
                    min-width: 320px;
                    transform: translateX(400px);
                    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                    backdrop-filter: blur(10px);
                ">
                    <div class="d-flex align-items-center">
                        <i class="${icon} me-3" style="font-size: 1.2rem;"></i>
                        <div class="flex-grow-1">
                            <div style="font-weight: 600; margin-bottom: 2px;">${message}</div>
                            <div style="font-size: 0.85rem; opacity: 0.9;">النظام الذكي للأتمتة</div>
                        </div>
                        <button onclick="this.parentElement.parentElement.remove()" style="
                            background: none;
                            border: none;
                            color: white;
                            font-size: 1.2rem;
                            cursor: pointer;
                            opacity: 0.7;
                            transition: opacity 0.3s;
                        " onmouseover="this.style.opacity='1'" onmouseout="this.style.opacity='0.7'">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', notificationHtml);

            const notification = document.querySelector('.advanced-notification:last-of-type');

            // تأثير الظهور
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // إزالة تلقائية
            setTimeout(() => {
                notification.style.transform = 'translateX(400px)';
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 400);
            }, 4000);
        }

        // تحديث البيانات مع مؤشر تحميل
        function refreshDashboard() {
            const refreshBtn = document.querySelector('.refresh-btn');
            if (refreshBtn) {
                refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...';
                refreshBtn.disabled = true;
            }

            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // تحديث تلقائي كل دقيقة
        setInterval(refreshDashboard, 60000);

        // تأثيرات الحركة المتطورة
        function initAdvancedAnimations() {
            // تأثيرات الكروت
            document.querySelectorAll('.card').forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;

                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                    this.style.boxShadow = '0 30px 60px rgba(0, 0, 0, 0.15)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                    this.style.boxShadow = '';
                });
            });

            // تأثيرات قواعد الأتمتة
            document.querySelectorAll('.rule-card').forEach(rule => {
                rule.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(10px)';
                    this.style.boxShadow = '0 15px 35px rgba(102, 126, 234, 0.2)';
                });

                rule.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                    this.style.boxShadow = '';
                });
            });

            // تأثيرات الأنشطة
            document.querySelectorAll('.activity-item').forEach(activity => {
                activity.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(8px)';
                    this.style.backgroundColor = 'rgba(102, 126, 234, 0.1)';
                });

                activity.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                    this.style.backgroundColor = '';
                });
            });
        }

        // تشغيل التأثيرات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initAdvancedAnimations();

            // رسالة ترحيب
            setTimeout(() => {
                showAdvancedNotification('مرحباً بك في لوحة الأتمتة الذكية', 'info', 'fas fa-robot');
            }, 1000);
        });

        // إضافة مؤثرات صوتية (اختيارية)
        function playNotificationSound() {
            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            audio.volume = 0.1;
            audio.play().catch(() => {}); // تجاهل الأخطاء
        }

        // تأثيرات الخلفية المتحركة
        function createFloatingElements() {
            for (let i = 0; i < 5; i++) {
                const element = document.createElement('div');
                element.style.cssText = `
                    position: fixed;
                    width: 4px;
                    height: 4px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 0;
                    animation: float ${5 + Math.random() * 5}s infinite linear;
                    left: ${Math.random() * 100}%;
                    top: 100%;
                `;
                document.body.appendChild(element);

                setTimeout(() => {
                    element.remove();
                }, 10000);
            }
        }

        // إضافة CSS للتأثيرات
        const style = document.createElement('style');
        style.textContent = `
            @keyframes float {
                to {
                    transform: translateY(-100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // تشغيل العناصر المتحركة
        setInterval(createFloatingElements, 3000);
    </script>

    <!-- الشريط الجانبي -->
    <div id="sidebar" class="sidebar">
        <div class="sidebar-header">
            <button class="sidebar-close" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
            <div class="sidebar-title">النظام المحاسبي المتقدم</div>
            <div class="sidebar-subtitle">إدارة شاملة للشحنات والمخلصين</div>
        </div>

        <div class="sidebar-content">
            <!-- لوحة المعلومات الرئيسية -->
            <div class="sidebar-section">
                <div class="section-header">
                    <i class="fas fa-home" style="margin-left: 8px;"></i>
                    الصفحة الرئيسية
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('main.dashboard') }}" class="sidebar-menu-link">
                            <i class="fas fa-tachometer-alt sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">لوحة المعلومات الرئيسية</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- قسم الشحنات -->
            <div class="sidebar-section">
                <div class="section-header">
                    <i class="fas fa-ship" style="margin-left: 8px;"></i>
                    إدارة الشحنات
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('shipments.release_dashboard') }}" class="sidebar-menu-link">
                            <i class="fas fa-list sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">قائمة الشحنات</span>
                            <span class="sidebar-menu-badge">156</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('shipments.index') }}" class="sidebar-menu-link">
                            <i class="fas fa-plus sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">إضافة شحنة جديدة</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('shipments.index') }}" class="sidebar-menu-link">
                            <i class="fas fa-map-marker-alt sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">تتبع الشحنات</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('shipments.index') }}" class="sidebar-menu-link">
                            <i class="fas fa-chart-bar sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">تقارير الشحنات</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- قسم أوامر التسليم -->
            <div class="sidebar-section">
                <div class="section-header">
                    <i class="fas fa-truck" style="margin-left: 8px;"></i>
                    أوامر التسليم
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('shipments.delivery_orders_log') }}" class="sidebar-menu-link">
                            <i class="fas fa-clipboard-list sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">قائمة أوامر التسليم</span>
                            <span class="sidebar-menu-badge">89</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('shipments.delivery_orders_dashboard') }}" class="sidebar-menu-link">
                            <i class="fas fa-file-plus sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">إنشاء أمر تسليم</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('shipments.delivery_orders_log') }}" class="sidebar-menu-link">
                            <i class="fas fa-clock sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">الأوامر المعلقة</span>
                            <span class="sidebar-menu-badge">24</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- قسم المخلصين -->
            <div class="sidebar-section">
                <div class="section-header">
                    <i class="fas fa-users" style="margin-left: 8px;"></i>
                    إدارة المخلصين
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('shipments.index') }}" class="sidebar-menu-link">
                            <i class="fas fa-user-tie sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">قائمة المخلصين</span>
                            <span class="sidebar-menu-badge">45</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('shipments.index') }}" class="sidebar-menu-link">
                            <i class="fas fa-user-plus sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">إضافة مخلص جديد</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('shipments.index') }}" class="sidebar-menu-link">
                            <i class="fas fa-star sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">تقييم الأداء</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- قسم الأتمتة -->
            <div class="sidebar-section">
                <div class="section-header">
                    <i class="fas fa-robot" style="margin-left: 8px;"></i>
                    نظام الأتمتة
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('shipments.automation_dashboard') }}" class="sidebar-menu-link active">
                            <i class="fas fa-tachometer-alt sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">لوحة الأتمتة</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('shipments.automation_settings') }}" class="sidebar-menu-link">
                            <i class="fas fa-cog sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">إعدادات الأتمتة</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('shipments.add_automation_rule') }}" class="sidebar-menu-link">
                            <i class="fas fa-plus-circle sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">إضافة قاعدة أتمتة</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- قسم الإشعارات -->
            <div class="sidebar-section">
                <div class="section-header">
                    <i class="fas fa-bell" style="margin-left: 8px;"></i>
                    الإشعارات
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('shipments.notification_center') }}" class="sidebar-menu-link">
                            <i class="fas fa-bell sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">مركز الإشعارات</span>
                            <span class="sidebar-menu-badge">24</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="#" class="sidebar-menu-link" onclick="openNotificationSettings()">
                            <i class="fas fa-cog sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">إعدادات الإشعارات</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- قسم التقارير -->
            <div class="sidebar-section">
                <div class="section-header">
                    <i class="fas fa-chart-line" style="margin-left: 8px;"></i>
                    التقارير والإحصائيات
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('main.dashboard') }}" class="sidebar-menu-link">
                            <i class="fas fa-chart-pie sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">لوحة التحكم</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('main.dashboard') }}" class="sidebar-menu-link">
                            <i class="fas fa-dollar-sign sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">التقارير المالية</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('main.dashboard') }}" class="sidebar-menu-link">
                            <i class="fas fa-chart-bar sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">تقارير الأداء</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="quick-stats">
                <div class="quick-stats-grid">
                    <div class="quick-stat-item">
                        <span class="quick-stat-number">156</span>
                        <span class="quick-stat-label">شحنة نشطة</span>
                    </div>
                    <div class="quick-stat-item">
                        <span class="quick-stat-number">89</span>
                        <span class="quick-stat-label">أمر تسليم</span>
                    </div>
                    <div class="quick-stat-item">
                        <span class="quick-stat-number">45</span>
                        <span class="quick-stat-label">مخلص نشط</span>
                    </div>
                    <div class="quick-stat-item">
                        <span class="quick-stat-number">24</span>
                        <span class="quick-stat-label">إشعار جديد</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- خلفية الشريط الجانبي -->
    <div id="sidebar-overlay" class="sidebar-overlay" onclick="toggleSidebar()"></div>

    <!-- CSS للشريط الجانبي -->
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            right: -400px;
            width: 400px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: -10px 0 30px rgba(0, 0, 0, 0.1);
            z-index: 10001;
            transition: right 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            overflow-y: auto;
        }

        .sidebar.active {
            right: 0;
        }

        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .sidebar-header {
            padding: 30px 25px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
            pointer-events: none;
        }

        .sidebar-title {
            font-size: 1.5rem;
            font-weight: 800;
            margin-bottom: 5px;
            position: relative;
            z-index: 2;
        }

        .sidebar-subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        .sidebar-close {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            z-index: 3;
        }

        .sidebar-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .sidebar-content {
            padding: 0;
        }

        .sidebar-section {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .sidebar-section:last-child {
            border-bottom: none;
        }

        .section-header {
            padding: 20px 25px 15px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            font-weight: 700;
            color: #1e293b;
            font-size: 0.95rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu-item {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .sidebar-menu-item:last-child {
            border-bottom: none;
        }

        .sidebar-menu-link {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: #374151;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .sidebar-menu-link.active {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border-right: 3px solid #667eea;
        }

        .sidebar-menu-link::before {
            content: '';
            position: absolute;
            top: 0;
            right: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: right 0.5s;
        }

        .sidebar-menu-link:hover {
            background: rgba(102, 126, 234, 0.05);
            color: #667eea;
            transform: translateX(-5px);
        }

        .sidebar-menu-link:hover::before {
            right: 100%;
        }

        .sidebar-menu-icon {
            width: 20px;
            margin-left: 15px;
            text-align: center;
            color: #667eea;
        }

        .sidebar-menu-text {
            flex: 1;
            font-weight: 500;
        }

        .sidebar-menu-badge {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 600;
        }

        .sidebar-menu-arrow {
            color: #94a3b8;
            transition: transform 0.3s ease;
        }

        .sidebar-menu-link:hover .sidebar-menu-arrow {
            transform: translateX(-3px);
        }

        .quick-stats {
            padding: 20px 25px;
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
        }

        .quick-stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .quick-stat-item {
            background: white;
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .quick-stat-number {
            font-size: 1.5rem;
            font-weight: 800;
            color: #667eea;
            display: block;
            margin-bottom: 5px;
        }

        .quick-stat-label {
            font-size: 0.8rem;
            color: #6b7280;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 300px;
                right: -300px;
            }
        }
    </style>

    <!-- JavaScript للشريط الجانبي -->
    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');

            sidebar.classList.toggle('active');
            overlay.classList.toggle('active');

            // منع التمرير عند فتح الشريط الجانبي
            if (sidebar.classList.contains('active')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = 'auto';
            }
        }

        // إغلاق الشريط الجانبي عند الضغط على Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const sidebar = document.getElementById('sidebar');
                if (sidebar.classList.contains('active')) {
                    toggleSidebar();
                }
            }
        });

        // وظيفة إعدادات الإشعارات
        function openNotificationSettings() {
            alert('إعدادات الإشعارات - قريباً!');
        }
    </script>

    <!-- تم إزالة JavaScript الخاص بالرسوم البيانية -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تم إزالة كود الرسوم البيانية

            // تم إزالة جميع أكواد الرسوم البيانية

            // تم إزالة كل كود الرسوم البيانية
            /*
            new Chart(performanceCtx, {
                type: 'line',
                data: {
                    labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
                    datasets: [{
                        label: 'معدل النجاح %',
                        data: [95, 97, 94, 98, 96, 99, 97],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    aspectRatio: 1.5,
                    plugins: {
                        legend: {
                            labels: {
                                font: {
                                    family: 'Cairo',
                                    size: 12
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 90,
                            max: 100,
                            ticks: {
                                font: {
                                    family: 'Cairo'
                                }
                            }
                        },
                        x: {
                            ticks: {
                                font: {
                                    family: 'Cairo'
                                }
                            }
                        }
                    },
                    layout: {
                        padding: 10
                    }
                }
            });

            // 3. رسم معدل نجاح القواعد
            const successCtx = document.getElementById('rulesSuccessChart').getContext('2d');
            new Chart(successCtx, {
                type: 'bar',
                data: {
                    labels: ['إنشاء أوامر', 'تعيين مخلصين', 'إرسال إشعارات', 'تحديث تقييمات'],
                    datasets: [{
                        label: 'معدل النجاح %',
                        data: [98, 95, 99, 92],
                        backgroundColor: [
                            '#007bff',
                            '#28a745',
                            '#fd7e14',
                            '#6f42c1'
                        ],
                        borderRadius: 8,
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    aspectRatio: 1.5,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 85,
                            max: 100,
                            ticks: {
                                font: {
                                    family: 'Cairo'
                                }
                            }
                        },
                        x: {
                            ticks: {
                                font: {
                                    family: 'Cairo'
                                }
                            }
                        }
                    },
                    layout: {
                        padding: 10
                    }
                }
            });

            // 4. رسم الأوامر المعالجة يومياً
            const dailyCtx = document.getElementById('dailyProcessedChart').getContext('2d');
            new Chart(dailyCtx, {
                type: 'area',
                data: {
                    labels: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14'],
                    datasets: [{
                        label: 'أوامر معالجة',
                        data: [45, 52, 48, 61, 55, 67, 59, 72, 68, 75, 71, 78, 74, 82],
                        borderColor: '#6f42c1',
                        backgroundColor: 'rgba(111, 66, 193, 0.2)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    aspectRatio: 1.5,
                    plugins: {
                        legend: {
                            labels: {
                                font: {
                                    family: 'Cairo',
                                    size: 12
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                font: {
                                    family: 'Cairo'
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'آخر 14 يوم',
                                font: {
                                    family: 'Cairo'
                                }
                            },
                            ticks: {
                                font: {
                                    family: 'Cairo'
                                }
                            }
                        }
                    },
                    layout: {
                        padding: 10
                    }
                }
            });
            */

            // تأثيرات تحريك الأرقام في البطاقات
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalValue = parseInt(stat.textContent);
                let currentValue = 0;
                const increment = finalValue / 30;

                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        stat.textContent = finalValue;
                        clearInterval(timer);
                    } else {
                        stat.textContent = Math.floor(currentValue);
                    }
                }, 50);
            });
        });

        // دوال إدارة قواعد الأتمتة
        function editRule(ruleId) {
            console.log('✏️ تعديل القاعدة:', ruleId);
            window.location.href = `/shipments/automation/edit-rule/${ruleId}`;
        }

        function deleteRule(ruleId, ruleName) {
            console.log('🗑️ طلب حذف القاعدة:', ruleId, ruleName);

            // التأكد من الحذف
            const confirmMessage = `هل أنت متأكد من حذف قاعدة الأتمتة: "${ruleName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`;

            if (confirm(confirmMessage)) {
                // إظهار مؤشر التحميل
                const deleteBtn = event.target.closest('button');
                const originalContent = deleteBtn.innerHTML;
                deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                deleteBtn.disabled = true;

                // إرسال طلب الحذف
                fetch(`/shipments/automation/delete-rule/${ruleId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('📋 استجابة حذف القاعدة:', data);

                    if (data.success) {
                        // إظهار رسالة نجاح
                        showAdvancedNotification('تم حذف قاعدة الأتمتة بنجاح', 'success', 'fas fa-check');

                        // إعادة تحميل الصفحة لضمان تحديث البيانات من قاعدة البيانات
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);

                    } else {
                        // إظهار رسالة خطأ
                        showAdvancedNotification(data.message || 'حدث خطأ أثناء حذف قاعدة الأتمتة', 'error', 'fas fa-exclamation-triangle');
                        // إعادة الزر لحالته الأصلية
                        deleteBtn.innerHTML = originalContent;
                        deleteBtn.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ في حذف القاعدة:', error);
                    showAdvancedNotification('حدث خطأ في الاتصال بالخادم', 'error', 'fas fa-exclamation-triangle');
                    // إعادة الزر لحالته الأصلية
                    deleteBtn.innerHTML = originalContent;
                    deleteBtn.disabled = false;
                });
            }
        }

        function updateRulesCount() {
            const rulesContainer = document.querySelector('.rules-container');
            const ruleCards = rulesContainer ? rulesContainer.querySelectorAll('.rule-card') : [];
            const count = ruleCards.length;

            console.log('📊 تحديث عداد القواعد:', count);

            // تحديث العداد في الإحصائيات
            const activeRulesElement = document.querySelector('[data-stat="active-rules"]');
            if (activeRulesElement) {
                activeRulesElement.textContent = count;
            }

            // إظهار رسالة "لا توجد قواعد" إذا كان العدد صفر
            if (count === 0 && rulesContainer) {
                rulesContainer.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-robot fa-2x text-muted mb-2"></i>
                        <br>
                        <span class="text-muted">لا توجد قواعد أتمتة</span>
                        <br>
                        <button class="btn btn-primary btn-sm mt-2" onclick="window.location.href='/shipments/automation/add-rule'">
                            <i class="fas fa-plus me-1"></i>إضافة قاعدة جديدة
                        </button>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>

