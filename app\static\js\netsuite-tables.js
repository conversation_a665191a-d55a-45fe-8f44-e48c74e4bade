/**
 * NetSuite Tables JavaScript
 * وظائف الجداول المتقدمة بنمط NetSuite Oracle
 */

// ========== فئة الجدول المتقدم ==========
class NetSuiteTable {
    constructor(tableElement, options = {}) {
        this.table = tableElement;
        this.options = {
            sortable: true,
            searchable: true,
            pagination: true,
            pageSize: 10,
            selectable: false,
            ...options
        };
        
        this.data = [];
        this.filteredData = [];
        this.currentPage = 1;
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.searchTerm = '';
        
        this.init();
    }
    
    init() {
        this.extractData();
        this.createToolbar();
        this.createPagination();
        this.bindEvents();
        this.render();
    }
    
    // استخراج البيانات من الجدول
    extractData() {
        const rows = this.table.querySelectorAll('tbody tr');
        this.data = Array.from(rows).map((row, index) => {
            const cells = row.querySelectorAll('td');
            const rowData = {
                id: index,
                element: row,
                cells: Array.from(cells).map(cell => ({
                    element: cell,
                    text: cell.textContent.trim(),
                    html: cell.innerHTML
                }))
            };
            return rowData;
        });
        this.filteredData = [...this.data];
    }
    
    // إنشاء شريط الأدوات
    createToolbar() {
        if (!this.options.searchable) return;
        
        const container = this.table.closest('.ns-table-container');
        const toolbar = document.createElement('div');
        toolbar.className = 'ns-table-toolbar';
        
        toolbar.innerHTML = `
            <div class="ns-table-toolbar-left">
                <div class="ns-table-search">
                    <input type="text" placeholder="البحث في الجدول..." class="ns-table-search-input">
                    <i class="fas fa-search ns-table-search-icon"></i>
                </div>
            </div>
            <div class="ns-table-toolbar-right">
                <span class="ns-table-info">
                    عرض <span class="ns-table-showing">0</span> من <span class="ns-table-total">0</span> عنصر
                </span>
            </div>
        `;
        
        container.insertBefore(toolbar, this.table);
        this.toolbar = toolbar;
    }
    
    // إنشاء ترقيم الصفحات
    createPagination() {
        if (!this.options.pagination) return;
        
        const container = this.table.closest('.ns-table-container');
        const pagination = document.createElement('div');
        pagination.className = 'ns-table-pagination';
        
        pagination.innerHTML = `
            <div class="ns-pagination-info">
                صفحة <span class="ns-current-page">1</span> من <span class="ns-total-pages">1</span>
            </div>
            <div class="ns-pagination-controls">
                <button class="ns-pagination-btn" data-action="first" title="الصفحة الأولى">
                    <i class="fas fa-angle-double-right"></i>
                </button>
                <button class="ns-pagination-btn" data-action="prev" title="الصفحة السابقة">
                    <i class="fas fa-angle-right"></i>
                </button>
                <div class="ns-pagination-numbers"></div>
                <button class="ns-pagination-btn" data-action="next" title="الصفحة التالية">
                    <i class="fas fa-angle-left"></i>
                </button>
                <button class="ns-pagination-btn" data-action="last" title="الصفحة الأخيرة">
                    <i class="fas fa-angle-double-left"></i>
                </button>
            </div>
        `;
        
        container.appendChild(pagination);
        this.pagination = pagination;
    }
    
    // ربط الأحداث
    bindEvents() {
        // البحث
        if (this.toolbar) {
            const searchInput = this.toolbar.querySelector('.ns-table-search-input');
            searchInput.addEventListener('input', (e) => {
                this.search(e.target.value);
            });
        }
        
        // الفرز
        if (this.options.sortable) {
            const headers = this.table.querySelectorAll('thead th');
            headers.forEach((header, index) => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', () => {
                    this.sort(index);
                });
            });
        }
        
        // ترقيم الصفحات
        if (this.pagination) {
            this.pagination.addEventListener('click', (e) => {
                const button = e.target.closest('.ns-pagination-btn');
                if (button) {
                    const action = button.getAttribute('data-action');
                    const page = button.getAttribute('data-page');
                    
                    if (page) {
                        this.goToPage(parseInt(page));
                    } else {
                        this.handlePaginationAction(action);
                    }
                }
            });
        }
        
        // التحديد
        if (this.options.selectable) {
            this.table.addEventListener('click', (e) => {
                const row = e.target.closest('tbody tr');
                if (row) {
                    this.toggleRowSelection(row);
                }
            });
        }
    }
    
    // البحث
    search(term) {
        this.searchTerm = term.toLowerCase();
        this.filteredData = this.data.filter(row => {
            return row.cells.some(cell => 
                cell.text.toLowerCase().includes(this.searchTerm)
            );
        });
        this.currentPage = 1;
        this.render();
    }
    
    // الفرز
    sort(columnIndex) {
        const headers = this.table.querySelectorAll('thead th');
        const header = headers[columnIndex];
        
        // تحديد اتجاه الفرز
        if (this.sortColumn === columnIndex) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortDirection = 'asc';
        }
        
        this.sortColumn = columnIndex;
        
        // إزالة فئات الفرز من جميع الرؤوس
        headers.forEach(h => {
            h.classList.remove('sort-asc', 'sort-desc');
        });
        
        // إضافة فئة الفرز للرأس الحالي
        header.classList.add(`sort-${this.sortDirection}`);
        
        // فرز البيانات
        this.filteredData.sort((a, b) => {
            const aValue = a.cells[columnIndex].text;
            const bValue = b.cells[columnIndex].text;
            
            // محاولة تحويل إلى رقم
            const aNum = parseFloat(aValue);
            const bNum = parseFloat(bValue);
            
            let comparison = 0;
            
            if (!isNaN(aNum) && !isNaN(bNum)) {
                // مقارنة رقمية
                comparison = aNum - bNum;
            } else {
                // مقارنة نصية
                comparison = aValue.localeCompare(bValue, 'ar');
            }
            
            return this.sortDirection === 'asc' ? comparison : -comparison;
        });
        
        this.currentPage = 1;
        this.render();
    }
    
    // الانتقال إلى صفحة
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredData.length / this.options.pageSize);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.render();
        }
    }
    
    // التعامل مع أزرار ترقيم الصفحات
    handlePaginationAction(action) {
        const totalPages = Math.ceil(this.filteredData.length / this.options.pageSize);
        
        switch (action) {
            case 'first':
                this.goToPage(1);
                break;
            case 'prev':
                this.goToPage(this.currentPage - 1);
                break;
            case 'next':
                this.goToPage(this.currentPage + 1);
                break;
            case 'last':
                this.goToPage(totalPages);
                break;
        }
    }
    
    // تبديل تحديد الصف
    toggleRowSelection(row) {
        row.classList.toggle('row-selected');
        
        // إرسال حدث التحديد
        const event = new CustomEvent('rowSelectionChanged', {
            detail: {
                row: row,
                selected: row.classList.contains('row-selected'),
                data: this.getRowData(row)
            }
        });
        this.table.dispatchEvent(event);
    }
    
    // الحصول على بيانات الصف
    getRowData(row) {
        const rowIndex = Array.from(this.table.querySelectorAll('tbody tr')).indexOf(row);
        return this.data[rowIndex];
    }
    
    // عرض الجدول
    render() {
        this.renderTable();
        this.renderPagination();
        this.updateInfo();
    }
    
    // عرض بيانات الجدول
    renderTable() {
        const tbody = this.table.querySelector('tbody');
        tbody.innerHTML = '';
        
        const startIndex = (this.currentPage - 1) * this.options.pageSize;
        const endIndex = startIndex + this.options.pageSize;
        const pageData = this.filteredData.slice(startIndex, endIndex);
        
        pageData.forEach(rowData => {
            const row = rowData.element.cloneNode(true);
            tbody.appendChild(row);
        });
    }
    
    // عرض ترقيم الصفحات
    renderPagination() {
        if (!this.pagination) return;
        
        const totalPages = Math.ceil(this.filteredData.length / this.options.pageSize);
        const numbersContainer = this.pagination.querySelector('.ns-pagination-numbers');
        
        // تحديث معلومات الصفحة
        this.pagination.querySelector('.ns-current-page').textContent = this.currentPage;
        this.pagination.querySelector('.ns-total-pages').textContent = totalPages;
        
        // تحديث حالة الأزرار
        const firstBtn = this.pagination.querySelector('[data-action="first"]');
        const prevBtn = this.pagination.querySelector('[data-action="prev"]');
        const nextBtn = this.pagination.querySelector('[data-action="next"]');
        const lastBtn = this.pagination.querySelector('[data-action="last"]');
        
        firstBtn.disabled = this.currentPage === 1;
        prevBtn.disabled = this.currentPage === 1;
        nextBtn.disabled = this.currentPage === totalPages;
        lastBtn.disabled = this.currentPage === totalPages;
        
        // إنشاء أرقام الصفحات
        numbersContainer.innerHTML = '';
        const maxVisible = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
        let endPage = Math.min(totalPages, startPage + maxVisible - 1);
        
        if (endPage - startPage < maxVisible - 1) {
            startPage = Math.max(1, endPage - maxVisible + 1);
        }
        
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `ns-pagination-btn ${i === this.currentPage ? 'active' : ''}`;
            pageBtn.setAttribute('data-page', i);
            pageBtn.textContent = i;
            numbersContainer.appendChild(pageBtn);
        }
    }
    
    // تحديث معلومات الجدول
    updateInfo() {
        if (!this.toolbar) return;
        
        const startIndex = (this.currentPage - 1) * this.options.pageSize;
        const endIndex = Math.min(startIndex + this.options.pageSize, this.filteredData.length);
        
        this.toolbar.querySelector('.ns-table-showing').textContent = 
            this.filteredData.length > 0 ? `${startIndex + 1}-${endIndex}` : '0';
        this.toolbar.querySelector('.ns-table-total').textContent = this.filteredData.length;
    }
}

// ========== تهيئة الجداول تلقائياً ==========
document.addEventListener('DOMContentLoaded', () => {
    // تهيئة جميع الجداول التي تحتوي على فئة ns-table-enhanced
    document.querySelectorAll('.ns-table-enhanced').forEach(table => {
        new NetSuiteTable(table);
    });
});

// تصدير للاستخدام العام
window.NetSuiteTable = NetSuiteTable;
