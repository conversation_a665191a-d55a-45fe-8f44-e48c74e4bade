-- =====================================================
-- Views متقدمة للتكامل بين المشتريات والموردين والحوالات
-- Advanced Views for Purchase Orders Integration
-- =====================================================

-- 1. View شامل لأوامر الشراء مع معلومات الموردين والمدفوعات
CREATE OR REPLACE VIEW V_PURCHASE_ORDERS_DETAILED AS
SELECT 
    po.id as purchase_order_id,
    po.po_number,
    po.title,
    po.description,
    po.po_date,
    po.delivery_date,
    po.status as order_status,
    po.payment_status,
    po.delivery_status,
    po.payment_due_date,
    po.payment_terms_days,
    
    -- معلومات المورد
    po.supplier_id,
    s.supplier_code,
    s.name_ar as supplier_name,
    s.name_en as supplier_name_en,
    s.phone as supplier_phone,
    s.email as supplier_email,
    s.city as supplier_city,
    
    -- المبالغ المالية
    po.currency,
    c.symbol as currency_symbol,
    po.subtotal_amount,
    po.tax_amount,
    po.discount_amount,
    po.shipping_amount,
    po.other_charges,
    po.total_amount,
    po.total_amount_due,
    po.paid_amount,
    po.outstanding_amount,
    po.advance_payment_amount,
    po.final_payment_amount,
    
    -- نسب الدفع
    CASE 
        WHEN po.total_amount_due > 0 THEN 
            ROUND((po.paid_amount / po.total_amount_due) * 100, 2)
        ELSE 0 
    END as payment_percentage,
    
    -- حالة الدفع المحسوبة
    CASE 
        WHEN po.outstanding_amount <= 0 THEN 'PAID'
        WHEN po.paid_amount > 0 THEN 'PARTIAL'
        WHEN po.payment_due_date < SYSDATE THEN 'OVERDUE'
        ELSE 'PENDING'
    END as computed_payment_status,
    
    -- معلومات العقد
    po.contract_id,
    ct.contract_number,
    ct.supplier_name as contract_supplier,
    
    -- معلومات التسليم
    po.goods_received_date,
    po.goods_received_by,
    u1.full_name as received_by_name,
    
    -- معلومات الموافقة
    po.approved_date,
    po.approved_by,
    u2.full_name as approved_by_name,
    
    -- إحصائيات المدفوعات
    (SELECT COUNT(*) FROM PURCHASE_ORDER_PAYMENTS pop 
     WHERE pop.purchase_order_id = po.id) as payments_count,
    
    (SELECT COUNT(*) FROM PURCHASE_ORDER_PAYMENTS pop 
     WHERE pop.purchase_order_id = po.id 
     AND pop.payment_status = 'COMPLETED') as completed_payments_count,
    
    -- إحصائيات الاستلام
    (SELECT COUNT(*) FROM GOODS_RECEIPTS gr 
     WHERE gr.purchase_order_id = po.id) as receipts_count,
    
    (SELECT COUNT(*) FROM GOODS_RECEIPTS gr 
     WHERE gr.purchase_order_id = po.id 
     AND gr.receipt_status = 'COMPLETE') as completed_receipts_count,
    
    -- تواريخ النظام
    po.created_at,
    po.updated_at,
    po.created_by,
    u3.full_name as created_by_name

FROM PURCHASE_ORDERS po
LEFT JOIN SUPPLIERS s ON po.supplier_id = s.id
LEFT JOIN CURRENCIES c ON po.currency = c.code
LEFT JOIN CONTRACTS ct ON po.contract_id = ct.contract_id
LEFT JOIN USERS u1 ON po.goods_received_by = u1.id
LEFT JOIN USERS u2 ON po.approved_by = u2.id
LEFT JOIN USERS u3 ON po.created_by = u3.id;

-- 2. View لأوامر الشراء المستحقة الدفع
CREATE OR REPLACE VIEW V_PURCHASE_ORDERS_OUTSTANDING AS
SELECT 
    po.id as purchase_order_id,
    po.po_number,
    po.title,
    po.supplier_id,
    s.supplier_code,
    s.name_ar as supplier_name,
    
    -- معلومات الاستحقاق
    po.payment_due_date,
    po.payment_terms_days,
    CASE 
        WHEN po.payment_due_date < SYSDATE THEN SYSDATE - po.payment_due_date
        ELSE 0 
    END as days_overdue,
    
    CASE 
        WHEN po.payment_due_date >= SYSDATE THEN po.payment_due_date - SYSDATE
        ELSE 0 
    END as days_until_due,
    
    -- المبالغ
    po.currency,
    c.symbol as currency_symbol,
    po.total_amount_due,
    po.paid_amount,
    po.outstanding_amount,
    
    -- حالة الاستحقاق
    CASE 
        WHEN po.outstanding_amount <= 0 THEN 'مدفوع'
        WHEN po.payment_due_date < SYSDATE THEN 'متأخر'
        WHEN po.payment_due_date <= SYSDATE + 7 THEN 'مستحق قريباً'
        ELSE 'مستحق'
    END as due_status,
    
    -- أولوية الدفع
    CASE 
        WHEN po.payment_due_date < SYSDATE - 30 THEN 'عاجل جداً'
        WHEN po.payment_due_date < SYSDATE THEN 'عاجل'
        WHEN po.payment_due_date <= SYSDATE + 3 THEN 'مهم'
        ELSE 'عادي'
    END as payment_priority,
    
    -- معلومات إضافية
    po.po_date,
    po.delivery_date,
    po.status as order_status,
    po.payment_status,
    po.created_at

FROM PURCHASE_ORDERS po
JOIN SUPPLIERS s ON po.supplier_id = s.id
LEFT JOIN CURRENCIES c ON po.currency = c.code
WHERE po.outstanding_amount > 0
AND po.status NOT IN ('CANCELLED', 'REJECTED')
AND s.is_active = 1;

-- 3. View لملخص مدفوعات أوامر الشراء
CREATE OR REPLACE VIEW V_PURCHASE_ORDER_PAYMENTS_SUMMARY AS
SELECT 
    pop.purchase_order_id,
    po.po_number,
    po.supplier_id,
    s.name_ar as supplier_name,
    
    -- إحصائيات المدفوعات
    COUNT(pop.id) as total_payments,
    SUM(CASE WHEN pop.payment_status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_payments,
    SUM(CASE WHEN pop.payment_status = 'PENDING' THEN 1 ELSE 0 END) as pending_payments,
    
    -- المبالغ
    po.currency,
    po.total_amount_due,
    SUM(CASE WHEN pop.payment_status = 'COMPLETED' THEN pop.payment_amount ELSE 0 END) as total_paid,
    SUM(CASE WHEN pop.payment_status = 'PENDING' THEN pop.payment_amount ELSE 0 END) as total_pending,
    po.total_amount_due - SUM(CASE WHEN pop.payment_status = 'COMPLETED' THEN pop.payment_amount ELSE 0 END) as remaining_amount,
    
    -- نسب الإكمال
    CASE 
        WHEN po.total_amount_due > 0 THEN 
            ROUND((SUM(CASE WHEN pop.payment_status = 'COMPLETED' THEN pop.payment_amount ELSE 0 END) / po.total_amount_due) * 100, 2)
        ELSE 0 
    END as payment_completion_percentage,
    
    -- تواريخ مهمة
    MIN(pop.payment_requested_date) as first_payment_date,
    MAX(pop.payment_completed_date) as last_payment_date,
    
    -- حالة عامة
    CASE 
        WHEN SUM(CASE WHEN pop.payment_status = 'COMPLETED' THEN pop.payment_amount ELSE 0 END) >= po.total_amount_due THEN 'مكتمل'
        WHEN SUM(CASE WHEN pop.payment_status = 'COMPLETED' THEN pop.payment_amount ELSE 0 END) > 0 THEN 'جزئي'
        ELSE 'معلق'
    END as overall_payment_status

FROM PURCHASE_ORDER_PAYMENTS pop
JOIN PURCHASE_ORDERS po ON pop.purchase_order_id = po.id
JOIN SUPPLIERS s ON po.supplier_id = s.id
GROUP BY pop.purchase_order_id, po.po_number, po.supplier_id, s.name_ar, 
         po.currency, po.total_amount_due;

-- 4. View لتتبع حالة أوامر الشراء
CREATE OR REPLACE VIEW V_PURCHASE_ORDER_STATUS_TRACKING AS
SELECT 
    posl.purchase_order_id,
    po.po_number,
    po.supplier_id,
    s.name_ar as supplier_name,
    
    -- معلومات التغيير
    posl.status_type,
    posl.old_status,
    posl.new_status,
    posl.change_reason,
    posl.change_date,
    posl.changed_by,
    u.full_name as changed_by_name,
    
    -- معلومات إضافية
    posl.related_document_type,
    posl.related_document_id,
    posl.system_generated,
    
    -- ترتيب التغييرات
    ROW_NUMBER() OVER (PARTITION BY posl.purchase_order_id ORDER BY posl.change_date DESC) as change_sequence,
    
    -- هل هو آخر تغيير
    CASE 
        WHEN ROW_NUMBER() OVER (PARTITION BY posl.purchase_order_id ORDER BY posl.change_date DESC) = 1 THEN 'Y'
        ELSE 'N'
    END as is_latest_change

FROM PURCHASE_ORDER_STATUS_LOG posl
JOIN PURCHASE_ORDERS po ON posl.purchase_order_id = po.id
JOIN SUPPLIERS s ON po.supplier_id = s.id
LEFT JOIN USERS u ON posl.changed_by = u.id;

-- 5. View لإحصائيات الموردين من أوامر الشراء
CREATE OR REPLACE VIEW V_SUPPLIER_PURCHASE_STATISTICS AS
SELECT 
    s.id as supplier_id,
    s.supplier_code,
    s.name_ar as supplier_name,
    s.supplier_type,
    s.city,
    
    -- إحصائيات أوامر الشراء
    COUNT(po.id) as total_purchase_orders,
    COUNT(CASE WHEN po.status = 'COMPLETED' THEN 1 END) as completed_orders,
    COUNT(CASE WHEN po.status = 'PENDING' THEN 1 END) as pending_orders,
    COUNT(CASE WHEN po.status = 'CANCELLED' THEN 1 END) as cancelled_orders,
    
    -- إحصائيات مالية
    SUM(po.total_amount_due) as total_order_value,
    SUM(po.paid_amount) as total_paid_amount,
    SUM(po.outstanding_amount) as total_outstanding_amount,
    AVG(po.total_amount_due) as average_order_value,
    
    -- إحصائيات الدفع
    COUNT(CASE WHEN po.payment_status = 'PAID' THEN 1 END) as fully_paid_orders,
    COUNT(CASE WHEN po.payment_status = 'PARTIAL' THEN 1 END) as partially_paid_orders,
    COUNT(CASE WHEN po.payment_status = 'OVERDUE' THEN 1 END) as overdue_orders,
    
    -- معدلات الأداء
    CASE 
        WHEN COUNT(po.id) > 0 THEN 
            ROUND((COUNT(CASE WHEN po.status = 'COMPLETED' THEN 1 END) / COUNT(po.id)) * 100, 2)
        ELSE 0 
    END as completion_rate,
    
    CASE 
        WHEN COUNT(po.id) > 0 THEN 
            ROUND((COUNT(CASE WHEN po.payment_status = 'PAID' THEN 1 END) / COUNT(po.id)) * 100, 2)
        ELSE 0 
    END as payment_rate,
    
    -- تواريخ مهمة
    MIN(po.po_date) as first_order_date,
    MAX(po.po_date) as last_order_date,
    
    -- متوسط أيام الدفع
    AVG(CASE 
        WHEN po.payment_status = 'PAID' AND po.final_payment_date IS NOT NULL THEN 
            po.final_payment_date - po.po_date
        ELSE NULL 
    END) as average_payment_days

FROM SUPPLIERS s
LEFT JOIN PURCHASE_ORDERS po ON s.id = po.supplier_id
WHERE s.is_active = 1
GROUP BY s.id, s.supplier_code, s.name_ar, s.supplier_type, s.city;

-- 6. View للتكامل مع نظام الحوالات
CREATE OR REPLACE VIEW V_PURCHASE_ORDER_TRANSFERS_INTEGRATION AS
SELECT 
    po.id as purchase_order_id,
    po.po_number,
    po.supplier_id,
    s.name_ar as supplier_name,
    
    -- معلومات المدفوعات
    pop.id as payment_id,
    pop.payment_type,
    pop.payment_amount,
    pop.currency_code,
    pop.payment_status,
    
    -- معلومات الحوالة
    pop.transfer_request_id,
    tr.request_number as transfer_request_number,
    tr.status as transfer_request_status,
    tr.created_at as transfer_request_date,
    
    pop.transfer_id,
    t.transfer_number,
    t.status as transfer_status,
    t.execution_date as transfer_execution_date,
    
    -- معلومات الصراف/البنك
    tr.money_changer_bank_id,
    mcb.name_ar as money_changer_name,
    mcb.type as money_changer_type,
    
    -- معلومات المستفيد
    tr.beneficiary_id,
    b.name_ar as beneficiary_name,
    b.account_number as beneficiary_account,
    
    -- تتبع شامل للحالة
    CASE 
        WHEN t.status = 'COMPLETED' THEN 'مكتملة'
        WHEN t.status = 'EXECUTED' THEN 'منفذة'
        WHEN tr.status = 'APPROVED' THEN 'معتمدة'
        WHEN tr.status = 'PENDING' THEN 'معلقة'
        ELSE 'غير محددة'
    END as integration_status,
    
    -- تواريخ التتبع
    pop.payment_requested_date,
    pop.payment_approved_date,
    pop.payment_executed_date,
    pop.payment_completed_date

FROM PURCHASE_ORDERS po
JOIN SUPPLIERS s ON po.supplier_id = s.id
LEFT JOIN PURCHASE_ORDER_PAYMENTS pop ON po.id = pop.purchase_order_id
LEFT JOIN TRANSFER_REQUESTS tr ON pop.transfer_request_id = tr.id
LEFT JOIN TRANSFERS t ON pop.transfer_id = t.id
LEFT JOIN MONEY_CHANGERS_BANKS mcb ON tr.money_changer_bank_id = mcb.id
LEFT JOIN BENEFICIARIES b ON tr.beneficiary_id = b.id;

-- 7. إنشاء تعليقات للـ Views
COMMENT ON VIEW V_PURCHASE_ORDERS_DETAILED IS 'عرض شامل لأوامر الشراء مع تفاصيل الموردين والمدفوعات';
COMMENT ON VIEW V_PURCHASE_ORDERS_OUTSTANDING IS 'أوامر الشراء المستحقة الدفع';
COMMENT ON VIEW V_PURCHASE_ORDER_PAYMENTS_SUMMARY IS 'ملخص مدفوعات أوامر الشراء';
COMMENT ON VIEW V_PURCHASE_ORDER_STATUS_TRACKING IS 'تتبع حالة أوامر الشراء';
COMMENT ON VIEW V_SUPPLIER_PURCHASE_STATISTICS IS 'إحصائيات الموردين من أوامر الشراء';
COMMENT ON VIEW V_PURCHASE_ORDER_TRANSFERS_INTEGRATION IS 'التكامل بين أوامر الشراء ونظام الحوالات';

-- تم إنشاء Views التكامل بنجاح
-- Integration Views created successfully
