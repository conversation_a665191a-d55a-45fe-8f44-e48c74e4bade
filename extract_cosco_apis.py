#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
استخراج الـ API endpoints الحقيقية من JavaScript
"""

import re

def extract_real_apis():
    """استخراج الـ APIs الحقيقية"""
    
    print("🔍 استخراج الـ API endpoints الحقيقية من JavaScript")
    print("=" * 60)
    
    try:
        # قراءة الملف المحفوظ
        with open('cosco_main_js.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        print(f"📜 حجم الملف: {len(js_content)} حرف")
        
        # البحث عن CARGO_TRACKING patterns
        print("\n🔍 البحث عن CARGO_TRACKING patterns...")
        
        cargo_patterns = [
            r'CARGO_TRACKING_BK["\']?\s*:\s*["\']([^"\']+)["\']',
            r'CARGO_TRACKING_BL["\']?\s*:\s*["\']([^"\']+)["\']',
            r'CARGO_TRACKING_CNTRS["\']?\s*:\s*["\']([^"\']+)["\']',
            r'CARGO_TRACKING["\']?\s*:\s*["\']([^"\']+)["\']'
        ]
        
        found_cargo_apis = {}
        for pattern in cargo_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            if matches:
                api_name = pattern.split('[')[0]
                found_cargo_apis[api_name] = matches
                print(f"  ✅ {api_name}: {matches}")
        
        # البحث عن apiUrl object
        print("\n🔍 البحث عن apiUrl object...")
        
        # البحث عن تعريف apiUrl
        apiurl_patterns = [
            r'apiUrl\s*:\s*{([^}]+)}',
            r'\$apiUrl\s*=\s*{([^}]+)}',
            r'API_URL\s*=\s*{([^}]+)}'
        ]
        
        for pattern in apiurl_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE | re.DOTALL)
            if matches:
                print(f"  ✅ وجد apiUrl object: {len(matches[0])} حرف")
                
                # تحليل محتوى apiUrl
                analyze_apiurl_content(matches[0])
                break
        
        # البحث عن URLs مباشرة
        print("\n🔍 البحث عن URLs مباشرة...")
        
        url_patterns = [
            r'["\']([^"\']*cargoTracking[^"\']*)["\']',
            r'["\']([^"\']*cargo[^"\']*tracking[^"\']*)["\']',
            r'["\']([^"\']*tracking[^"\']*cargo[^"\']*)["\']'
        ]
        
        found_urls = set()
        for pattern in url_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            for match in matches:
                if len(match) > 10 and ('cargo' in match.lower() and 'tracking' in match.lower()):
                    found_urls.add(match)
        
        print(f"  📋 URLs found: {len(found_urls)}")
        for url in sorted(found_urls)[:10]:  # أول 10
            print(f"    - {url}")
        
        # البحث عن axios calls
        print("\n🔍 البحث عن axios calls...")
        
        axios_patterns = [
            r'\$axios\.(get|post)\([^,]+,\s*["\']([^"\']+)["\']',
            r'axios\.(get|post)\([^,]+,\s*["\']([^"\']+)["\']'
        ]
        
        found_axios = {}
        for pattern in axios_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            for method, url in matches:
                if 'cargo' in url.lower() or 'tracking' in url.lower():
                    if method.upper() not in found_axios:
                        found_axios[method.upper()] = []
                    found_axios[method.upper()].append(url)
        
        for method, urls in found_axios.items():
            print(f"  📤 {method}: {len(urls)} URLs")
            for url in sorted(set(urls))[:5]:  # أول 5
                print(f"    - {url}")
        
        # البحث عن search functions
        print("\n🔍 البحث عن search functions...")
        
        search_patterns = [
            r'(handleSearch|doSearch|performSearch|searchCargo)\s*[:\(]([^}]+)}',
            r'function\s+(handleSearch|doSearch|performSearch|searchCargo)\s*\([^)]*\)\s*{([^}]+)}'
        ]
        
        for pattern in search_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE | re.DOTALL)
            if matches:
                print(f"  🔧 وجد search functions: {len(matches)}")
                for match in matches[:2]:  # أول 2
                    func_name = match[0] if isinstance(match, tuple) else match
                    print(f"    - {func_name}")
        
        return found_cargo_apis, found_urls, found_axios
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return {}, set(), {}

def analyze_apiurl_content(content):
    """تحليل محتوى apiUrl object"""
    
    print("  🔍 تحليل محتوى apiUrl...")
    
    # البحث عن key-value pairs
    kv_pattern = r'([A-Z_]+)\s*:\s*["\']([^"\']+)["\']'
    matches = re.findall(kv_pattern, content, re.IGNORECASE)
    
    cargo_apis = {}
    for key, value in matches:
        if 'cargo' in key.lower() or 'tracking' in key.lower():
            cargo_apis[key] = value
    
    print(f"    📋 Cargo APIs found: {len(cargo_apis)}")
    for key, value in cargo_apis.items():
        print(f"      - {key}: {value}")
    
    return cargo_apis

def test_extracted_apis(apis, urls, axios_calls):
    """اختبار الـ APIs المستخرجة"""
    
    print(f"\n🧪 اختبار الـ APIs المستخرجة...")
    
    import requests
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    session = requests.Session()
    session.verify = False
    
    # زيارة الموقع للحصول على cookies
    session.get("https://elines.coscoshipping.com/ebusiness/cargoTracking", timeout=20)
    
    # تحديث headers
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'Origin': 'https://elines.coscoshipping.com',
        'Referer': 'https://elines.coscoshipping.com/ebusiness/cargoTracking'
    })
    
    # بيانات البحث
    search_data = {
        "bookingNo": "6425375050",
        "trackingType": "2"
    }
    
    # اختبار URLs المستخرجة
    test_urls = []
    
    # إضافة URLs من axios calls
    for method, url_list in axios_calls.items():
        for url in url_list[:3]:  # أول 3 من كل method
            if url.startswith('/'):
                full_url = f"https://elines.coscoshipping.com{url}"
            else:
                full_url = url
            test_urls.append((method, full_url))
    
    # إضافة URLs مباشرة
    for url in list(urls)[:5]:  # أول 5
        if url.startswith('/'):
            full_url = f"https://elines.coscoshipping.com{url}"
        else:
            full_url = url
        test_urls.append(('POST', full_url))
    
    print(f"  📋 سيتم اختبار {len(test_urls)} URLs...")
    
    for method, url in test_urls:
        try:
            print(f"  🔗 اختبار {method}: {url}")
            
            if method.upper() == 'POST':
                response = session.post(url, json=search_data, timeout=10)
            else:
                response = session.get(url, params=search_data, timeout=10)
            
            print(f"    Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"    ✅ JSON Response: {str(data)[:100]}...")
                    
                    # فحص إذا كانت البيانات تحتوي على معلومات مفيدة
                    if check_useful_data(data, "6425375050"):
                        print(f"    🎯 وجد بيانات مفيدة!")
                        return True
                        
                except:
                    print(f"    📄 Text Response: {response.text[:100]}...")
            
            elif response.status_code not in [404, 405]:
                print(f"    ⚠️ غير متوقع: {response.status_code}")
            
        except Exception as e:
            print(f"    ❌ خطأ: {e}")
    
    return False

def check_useful_data(data, booking_number):
    """فحص إذا كانت البيانات مفيدة"""
    
    if isinstance(data, dict):
        # البحث عن رقم الحجز
        data_str = str(data).lower()
        if booking_number in data_str:
            return True
        
        # البحث عن كلمات مفتاحية
        keywords = ['etd', 'eta', 'vessel', 'port', 'container', 'bl']
        for keyword in keywords:
            if keyword in data_str:
                return True
    
    return False

def main():
    """الاختبار الرئيسي"""
    
    print("=" * 80)
    print("🔍 استخراج واختبار الـ API endpoints الحقيقية")
    print("=" * 80)
    
    # استخراج APIs
    apis, urls, axios_calls = extract_real_apis()
    
    # اختبار APIs
    if apis or urls or axios_calls:
        success = test_extracted_apis(apis, urls, axios_calls)
        
        if success:
            print(f"\n🎯 نجح في العثور على API يعمل!")
        else:
            print(f"\n❌ لم يتم العثور على API يعمل")
    else:
        print(f"\n❌ لم يتم استخراج أي APIs")
    
    print(f"\n" + "=" * 80)
    print("✅ انتهى الاستخراج والاختبار")
    print("=" * 80)

if __name__ == "__main__":
    main()
