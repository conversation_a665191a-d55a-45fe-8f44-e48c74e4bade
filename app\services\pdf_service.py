"""
خدمة إنشاء ملفات PDF
لأوامر التسليم والمستندات الأخرى
"""

from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.platypus.flowables import HRFlowable
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os
from datetime import datetime
from typing import Dict, Optional
import io
import base64

# ترجمة النصوص العربية للإنجليزية لحل مشكلة العرض
ARABIC_TO_ENGLISH = {
    'شركة النقل والشحن المتطورة': 'Advanced Shipping & Transport Company',
    'أمر تسليم للمخلص الجمركي': 'Delivery Order for Customs Agent',
    'رقم الأمر': 'Order Number',
    'تاريخ الإصدار': 'Issue Date',
    'حالة الأمر': 'Order Status',
    'تفاصيل الشحنة': 'Shipment Details',
    'بيانات المخلص الجمركي': 'Customs Agent Information',
    'تفاصيل التسليم': 'Delivery Details',
    'تعليمات خاصة': 'Special Instructions',
    'البيان': 'Description',
    'القيمة': 'Value',
    'رقم التتبع': 'Tracking Number',
    'رقم الحجز': 'Booking Number',
    'نوع الشحنة': 'Shipment Type',
    'الوزن الإجمالي': 'Total Weight',
    'عدد الطرود': 'Number of Packages',
    'إجمالي التكلفة': 'Total Cost',
    'اسم المخلص': 'Agent Name',
    'اسم الشركة': 'Company Name',
    'رقم الترخيص': 'License Number',
    'رقم الهاتف': 'Phone Number',
    'البريد الإلكتروني': 'Email',
    'موقع التسليم': 'Delivery Location',
    'التاريخ المتوقع': 'Expected Date',
    'الأولوية': 'Priority',
    'التكلفة المقدرة': 'Estimated Cost',
    'الشخص المسؤول': 'Contact Person',
    'رقم الاتصال': 'Contact Number',
    'غير محدد': 'Not Specified',
    'مسودة': 'Draft',
    'مرسل': 'Sent',
    'قيد التنفيذ': 'In Progress',
    'مكتمل': 'Completed',
    'ملغي': 'Cancelled',
    'منخفض': 'Low',
    'عادي': 'Normal',
    'عالي': 'High',
    'عاجل': 'Urgent',
    'بحري': 'Sea Freight',
    'جوي': 'Air Freight',
    'بري': 'Land Freight',
    'كيلو': 'KG',
    'تاريخ الطباعة': 'Print Date',
    'العنوان': 'Address',
    'الهاتف': 'Phone',
    'الموقع الإلكتروني': 'Website'
}

class DeliveryOrderPDFService:
    """خدمة إنشاء PDF لأوامر التسليم"""

    def __init__(self):
        """تهيئة خدمة PDF"""
        self.setup_fonts()
        self.setup_styles()

    def translate_text(self, text: str) -> str:
        """ترجمة النص العربي للإنجليزية"""
        if not text or text.strip() == '':
            return text
        return ARABIC_TO_ENGLISH.get(text, text)
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # محاولة تحميل خط عربي (يمكن تحميله من Google Fonts)
            font_path = os.path.join('app', 'static', 'fonts', 'NotoSansArabic-Regular.ttf')
            if os.path.exists(font_path):
                pdfmetrics.registerFont(TTFont('Arabic', font_path))
                self.arabic_font = 'Arabic'
            else:
                # استخدام خط افتراضي يدعم Unicode
                self.arabic_font = 'Helvetica'
        except Exception as e:
            # في حالة فشل تحميل الخط، استخدم الخط الافتراضي
            self.arabic_font = 'Helvetica'
    
    def setup_styles(self):
        """إعداد أنماط النص"""
        self.styles = getSampleStyleSheet()
        
        # نمط العنوان الرئيسي
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontName=self.arabic_font,
            fontSize=18,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#1f4e79')
        )
        
        # نمط العنوان الفرعي
        self.subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=self.styles['Heading2'],
            fontName=self.arabic_font,
            fontSize=14,
            spaceAfter=12,
            alignment=TA_RIGHT,
            textColor=colors.HexColor('#2c5aa0')
        )
        
        # نمط النص العادي
        self.normal_style = ParagraphStyle(
            'CustomNormal',
            parent=self.styles['Normal'],
            fontName=self.arabic_font,
            fontSize=10,
            spaceAfter=6,
            alignment=TA_RIGHT
        )
        
        # نمط النص المركز
        self.center_style = ParagraphStyle(
            'CustomCenter',
            parent=self.styles['Normal'],
            fontName=self.arabic_font,
            fontSize=10,
            alignment=TA_CENTER
        )
    
    def create_header(self, order_data: Dict) -> list:
        """إنشاء رأس المستند"""
        elements = []
        
        # شعار الشركة (إذا كان متوفراً)
        logo_path = os.path.join('app', 'static', 'images', 'logo.png')
        if os.path.exists(logo_path):
            try:
                logo = Image(logo_path, width=2*inch, height=1*inch)
                logo.hAlign = 'CENTER'
                elements.append(logo)
                elements.append(Spacer(1, 12))
            except:
                pass
        
        # عنوان الشركة
        company_title = Paragraph(self.translate_text("شركة النقل والشحن المتطورة"), self.title_style)
        elements.append(company_title)

        # عنوان المستند
        doc_title = Paragraph(self.translate_text("أمر تسليم للمخلص الجمركي"), self.subtitle_style)
        elements.append(doc_title)

        # رقم الأمر وتاريخ الإصدار
        order_info = f"""
        <b>{self.translate_text("رقم الأمر")}:</b> {order_data.get('order_number', self.translate_text('غير محدد'))}<br/>
        <b>{self.translate_text("تاريخ الإصدار")}:</b> {order_data.get('created_date', datetime.now().strftime('%Y-%m-%d'))}<br/>
        <b>{self.translate_text("حالة الأمر")}:</b> {self.get_status_english(order_data.get('order_status', 'draft'))}
        """
        
        order_info_para = Paragraph(order_info, self.normal_style)
        elements.append(order_info_para)
        
        # خط فاصل
        elements.append(HRFlowable(width="100%", thickness=1, color=colors.HexColor('#1f4e79')))
        elements.append(Spacer(1, 20))
        
        return elements
    
    def create_shipment_details(self, order_data: Dict) -> list:
        """إنشاء تفاصيل الشحنة"""
        elements = []
        
        # عنوان القسم
        section_title = Paragraph(self.translate_text("تفاصيل الشحنة"), self.subtitle_style)
        elements.append(section_title)

        # جدول تفاصيل الشحنة
        shipment_data = [
            [self.translate_text('البيان'), self.translate_text('القيمة')],
            [self.translate_text('رقم التتبع'), order_data.get('tracking_number', self.translate_text('غير محدد'))],
            [self.translate_text('رقم الحجز'), order_data.get('booking_number', self.translate_text('غير محدد'))],
            [self.translate_text('نوع الشحنة'), self.translate_text(order_data.get('shipment_type', 'بحري'))],
            [self.translate_text('الوزن الإجمالي'), f"{order_data.get('total_weight', self.translate_text('غير محدد'))} {self.translate_text('كيلو')}" if order_data.get('total_weight') else self.translate_text('غير محدد')],
            [self.translate_text('عدد الطرود'), str(order_data.get('packages_count', self.translate_text('غير محدد')))],
            [self.translate_text('إجمالي التكلفة'), f"{order_data.get('declared_value', self.translate_text('غير محدد'))} {order_data.get('currency', 'SAR')}" if order_data.get('declared_value') else self.translate_text('غير محدد')],
        ]
        
        shipment_table = Table(shipment_data, colWidths=[3*inch, 3*inch])
        shipment_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#1f4e79')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        elements.append(shipment_table)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def create_customs_agent_details(self, order_data: Dict) -> list:
        """إنشاء تفاصيل المخلص الجمركي"""
        elements = []
        
        # عنوان القسم
        section_title = Paragraph(self.translate_text("بيانات المخلص الجمركي"), self.subtitle_style)
        elements.append(section_title)

        # جدول بيانات المخلص
        agent_data = [
            [self.translate_text('البيان'), self.translate_text('القيمة')],
            [self.translate_text('اسم المخلص'), order_data.get('agent_name', self.translate_text('غير محدد'))],
            [self.translate_text('اسم الشركة'), order_data.get('company_name', self.translate_text('غير محدد'))],
            [self.translate_text('رقم الترخيص'), order_data.get('license_number', self.translate_text('غير محدد'))],
            [self.translate_text('رقم الهاتف'), order_data.get('agent_phone', self.translate_text('غير محدد'))],
            [self.translate_text('البريد الإلكتروني'), order_data.get('agent_email', self.translate_text('غير محدد'))],
        ]
        
        agent_table = Table(agent_data, colWidths=[3*inch, 3*inch])
        agent_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2c5aa0')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        elements.append(agent_table)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def create_delivery_details(self, order_data: Dict) -> list:
        """إنشاء تفاصيل التسليم"""
        elements = []
        
        # عنوان القسم
        section_title = Paragraph(self.translate_text("تفاصيل التسليم"), self.subtitle_style)
        elements.append(section_title)

        # جدول تفاصيل التسليم
        delivery_data = [
            [self.translate_text('البيان'), self.translate_text('القيمة')],
            [self.translate_text('موقع التسليم'), order_data.get('delivery_location', self.translate_text('غير محدد'))],
            [self.translate_text('التاريخ المتوقع'), order_data.get('expected_completion_date', self.translate_text('غير محدد'))],
            [self.translate_text('الأولوية'), self.get_priority_english(order_data.get('priority', 'normal'))],
            [self.translate_text('التكلفة المقدرة'), f"{order_data.get('estimated_cost', self.translate_text('غير محدد'))} {order_data.get('currency', 'SAR')}" if order_data.get('estimated_cost') else self.translate_text('غير محدد')],
            [self.translate_text('الشخص المسؤول'), order_data.get('contact_person', self.translate_text('غير محدد'))],
            [self.translate_text('رقم الاتصال'), order_data.get('contact_phone', self.translate_text('غير محدد'))],
        ]
        
        delivery_table = Table(delivery_data, colWidths=[3*inch, 3*inch])
        delivery_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#28a745')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightgreen),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        elements.append(delivery_table)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def create_special_instructions(self, order_data: Dict) -> list:
        """إنشاء التعليمات الخاصة"""
        elements = []
        
        special_instructions = order_data.get('special_instructions', '').strip()
        if special_instructions:
            # عنوان القسم
            section_title = Paragraph(self.translate_text("تعليمات خاصة"), self.subtitle_style)
            elements.append(section_title)

            # نص التعليمات (نبقيه كما هو لأنه محتوى مخصص)
            instructions_para = Paragraph(special_instructions, self.normal_style)
            elements.append(instructions_para)
            elements.append(Spacer(1, 20))
        
        return elements
    
    def create_footer(self, order_data: Dict) -> list:
        """إنشاء تذييل المستند"""
        elements = []
        
        # خط فاصل
        elements.append(HRFlowable(width="100%", thickness=1, color=colors.HexColor('#1f4e79')))
        elements.append(Spacer(1, 12))
        
        # معلومات الشركة
        footer_text = f"""
        <b>{self.translate_text("شركة النقل والشحن المتطورة")}</b><br/>
        {self.translate_text("العنوان")}: Saudi Arabia - Riyadh<br/>
        {self.translate_text("الهاتف")}: +966 11 123 4567 | {self.translate_text("البريد الإلكتروني")}: <EMAIL><br/>
        {self.translate_text("الموقع الإلكتروني")}: www.shipping.com
        """

        footer_para = Paragraph(footer_text, self.center_style)
        elements.append(footer_para)

        # تاريخ الطباعة
        print_date = f"{self.translate_text('تاريخ الطباعة')}: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        print_date_para = Paragraph(print_date, self.center_style)
        elements.append(print_date_para)
        
        return elements
    
    def get_status_english(self, status: str) -> str:
        """ترجمة حالة الأمر للإنجليزية"""
        status_map = {
            'draft': 'Draft',
            'sent': 'Sent',
            'in_progress': 'In Progress',
            'completed': 'Completed',
            'cancelled': 'Cancelled'
        }
        return status_map.get(status, status.title())

    def get_priority_english(self, priority: str) -> str:
        """ترجمة الأولوية للإنجليزية"""
        priority_map = {
            'low': 'Low',
            'normal': 'Normal',
            'high': 'High',
            'urgent': 'Urgent'
        }
        return priority_map.get(priority, priority.title())
    
    def generate_delivery_order_pdf(self, order_data: Dict, output_path: Optional[str] = None) -> bytes:
        """إنشاء PDF لأمر التسليم"""
        
        # إنشاء buffer للـ PDF
        buffer = io.BytesIO()
        
        # إنشاء المستند
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        # إنشاء عناصر المستند
        elements = []
        
        # إضافة الأقسام
        elements.extend(self.create_header(order_data))
        elements.extend(self.create_shipment_details(order_data))
        elements.extend(self.create_customs_agent_details(order_data))
        elements.extend(self.create_delivery_details(order_data))
        elements.extend(self.create_special_instructions(order_data))
        elements.extend(self.create_footer(order_data))
        
        # بناء المستند
        doc.build(elements)
        
        # الحصول على البيانات
        pdf_data = buffer.getvalue()
        buffer.close()
        
        # حفظ في ملف إذا تم تحديد المسار
        if output_path:
            with open(output_path, 'wb') as f:
                f.write(pdf_data)
        
        return pdf_data


# إنشاء instance عام للخدمة
pdf_service = DeliveryOrderPDFService()


def generate_delivery_order_pdf(order_data: Dict, output_path: Optional[str] = None) -> bytes:
    """دالة مساعدة لإنشاء PDF لأمر التسليم"""
    return pdf_service.generate_delivery_order_pdf(order_data, output_path)
