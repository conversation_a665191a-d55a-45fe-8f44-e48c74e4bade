-- =====================================================
-- الإجراءات المخزنة للتكامل بين أوامر الشراء والموردين والحوالات
-- Stored Procedures for Purchase Orders Integration
-- =====================================================

-- 1. إجراء تحديث حالة مدفوعات أمر الشراء
CREATE OR REPLACE PROCEDURE UPDATE_PURCHASE_ORDER_PAYMENT_STATUS(
    p_purchase_order_id IN NUMBER,
    p_payment_amount IN NUMBER,
    p_payment_type IN VARCHAR2,
    p_transfer_request_id IN NUMBER,
    p_updated_by IN NUMBER
) AS
    v_total_paid NUMBER := 0;
    v_total_due NUMBER := 0;
    v_new_payment_status VARCHAR2(30);
    v_old_payment_status VARCHAR2(30);
BEGIN
    -- الحصول على الحالة الحالية والمبلغ المستحق
    SELECT payment_status, total_amount_due, paid_amount
    INTO v_old_payment_status, v_total_due, v_total_paid
    FROM PURCHASE_ORDERS
    WHERE id = p_purchase_order_id;
    
    -- إضافة المبلغ الجديد
    v_total_paid := v_total_paid + p_payment_amount;
    
    -- تحديد الحالة الجديدة
    IF v_total_paid >= v_total_due THEN
        v_new_payment_status := 'PAID';
    ELSIF v_total_paid > 0 THEN
        v_new_payment_status := 'PARTIAL';
    ELSE
        v_new_payment_status := 'PENDING';
    END IF;
    
    -- تحديث أمر الشراء
    UPDATE PURCHASE_ORDERS SET
        payment_status = v_new_payment_status,
        paid_amount = v_total_paid,
        outstanding_amount = v_total_due - v_total_paid,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_purchase_order_id;
    
    -- إنشاء معاملة في حساب المورد
    INSERT INTO SUPPLIER_TRANSACTIONS (
        supplier_id, transaction_type, reference_type, reference_id,
        reference_number, transaction_date, currency_code, original_amount,
        credit_amount, description, status, purchase_order_id,
        purchase_order_number, payment_request_id, created_date, created_by
    ) VALUES (
        (SELECT supplier_id FROM PURCHASE_ORDERS WHERE id = p_purchase_order_id),
        'PAYMENT', 'PURCHASE_ORDER', p_purchase_order_id,
        (SELECT po_number FROM PURCHASE_ORDERS WHERE id = p_purchase_order_id),
        CURRENT_TIMESTAMP,
        (SELECT currency FROM PURCHASE_ORDERS WHERE id = p_purchase_order_id),
        p_payment_amount, p_payment_amount,
        'دفعة ' || p_payment_type || ' لأمر الشراء',
        'COMPLETED', p_purchase_order_id,
        (SELECT po_number FROM PURCHASE_ORDERS WHERE id = p_purchase_order_id),
        p_transfer_request_id, CURRENT_TIMESTAMP, p_updated_by
    );
    
    -- تسجيل تغيير الحالة
    INSERT INTO PURCHASE_ORDER_STATUS_LOG (
        purchase_order_id, old_status, new_status, status_type,
        change_reason, changed_by, related_document_type, related_document_id
    ) VALUES (
        p_purchase_order_id, v_old_payment_status, v_new_payment_status, 'PAYMENT_STATUS',
        'تحديث حالة الدفع - مبلغ: ' || p_payment_amount, p_updated_by,
        'TRANSFER_REQUEST', p_transfer_request_id
    );
    
    -- تحديث رصيد المورد
    UPDATE SUPPLIER_BALANCES SET
        credit_amount = credit_amount + p_payment_amount,
        current_balance = current_balance - p_payment_amount,
        total_payments_count = total_payments_count + 1,
        last_payment_date = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
    WHERE supplier_id = (SELECT supplier_id FROM PURCHASE_ORDERS WHERE id = p_purchase_order_id)
    AND currency_code = (SELECT currency FROM PURCHASE_ORDERS WHERE id = p_purchase_order_id);
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END UPDATE_PURCHASE_ORDER_PAYMENT_STATUS;

-- 2. إجراء إنشاء طلب دفع متكامل لأوامر الشراء
CREATE OR REPLACE PROCEDURE CREATE_INTEGRATED_PAYMENT_REQUEST(
    p_supplier_id IN NUMBER,
    p_purchase_orders IN VARCHAR2, -- JSON array of purchase orders
    p_money_changer_id IN NUMBER,
    p_payment_purpose IN VARCHAR2,
    p_discount_amount IN NUMBER DEFAULT 0,
    p_tax_amount IN NUMBER DEFAULT 0,
    p_notes IN VARCHAR2 DEFAULT NULL,
    p_created_by IN NUMBER,
    p_transfer_request_id OUT NUMBER
) AS
    v_total_amount NUMBER := 0;
    v_net_amount NUMBER := 0;
    v_currency_code VARCHAR2(3);
    v_request_number VARCHAR2(50);
    v_beneficiary_id NUMBER;
    v_supplier_payment_id NUMBER;
BEGIN
    -- حساب إجمالي المبلغ من JSON
    -- هذا مثال مبسط - في الواقع نحتاج لتحليل JSON
    SELECT SUM(outstanding_amount), MAX(currency)
    INTO v_total_amount, v_currency_code
    FROM PURCHASE_ORDERS
    WHERE supplier_id = p_supplier_id
    AND payment_status IN ('PENDING', 'PARTIAL');
    
    -- حساب المبلغ الصافي
    v_net_amount := v_total_amount - p_discount_amount - p_tax_amount;
    
    -- الحصول على المستفيد
    SELECT id INTO v_beneficiary_id
    FROM BENEFICIARIES
    WHERE supplier_id = p_supplier_id AND is_active = 1
    ORDER BY is_default DESC, created_at DESC
    FETCH FIRST 1 ROWS ONLY;
    
    -- إنشاء رقم طلب فريد
    v_request_number := 'PO' || TO_CHAR(SYSDATE, 'YYYYMMDDHH24MISS') || LPAD(p_supplier_id, 3, '0');
    
    -- إنشاء طلب الحوالة
    INSERT INTO TRANSFER_REQUESTS (
        request_number, beneficiary_id, amount, currency, purpose, notes,
        branch_id, status, created_by, updated_by, total_amount,
        delivery_method, transfer_type, money_changer_bank_id,
        supplier_id, payment_type, discount_amount, tax_amount,
        net_payment_amount, created_at, updated_at
    ) VALUES (
        v_request_number, v_beneficiary_id, v_total_amount, v_currency_code,
        p_payment_purpose, p_notes, 1, 'pending', p_created_by, p_created_by,
        v_total_amount, 'bank_transfer', 'supplier_payment', p_money_changer_id,
        p_supplier_id, 'PURCHASE_ORDER_PAYMENT', p_discount_amount, p_tax_amount,
        v_net_amount, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    ) RETURNING id INTO p_transfer_request_id;
    
    -- إنشاء سجل في مدفوعات الموردين
    INSERT INTO SUPPLIER_PAYMENT_TRANSFERS (
        supplier_id, transfer_request_id, payment_amount, currency_code,
        payment_purpose, payment_status, payment_method,
        discount_applied, tax_withheld, net_amount_transferred,
        requested_date, created_at, created_by
    ) VALUES (
        p_supplier_id, p_transfer_request_id, v_total_amount, v_currency_code,
        p_payment_purpose, 'PENDING', 'BANK_TRANSFER',
        p_discount_amount, p_tax_amount, v_net_amount,
        SYSDATE, CURRENT_TIMESTAMP, p_created_by
    ) RETURNING id INTO v_supplier_payment_id;
    
    -- ربط أوامر الشراء بالدفعة
    -- هنا نحتاج لتحليل JSON وإنشاء سجلات في PURCHASE_ORDER_PAYMENTS
    -- مثال مبسط:
    FOR po_rec IN (
        SELECT id, outstanding_amount, currency
        FROM PURCHASE_ORDERS
        WHERE supplier_id = p_supplier_id
        AND payment_status IN ('PENDING', 'PARTIAL')
    ) LOOP
        INSERT INTO PURCHASE_ORDER_PAYMENTS (
            purchase_order_id, supplier_payment_transfer_id, transfer_request_id,
            payment_type, payment_amount, currency_code, payment_description,
            payment_status, requested_by, created_by
        ) VALUES (
            po_rec.id, v_supplier_payment_id, p_transfer_request_id,
            'FULL', po_rec.outstanding_amount, po_rec.currency,
            'دفعة كاملة لأمر الشراء', 'PENDING', p_created_by, p_created_by
        );
        
        -- تحديث حالة أمر الشراء
        UPDATE PURCHASE_ORDERS SET
            payment_status = 'PAYMENT_REQUESTED',
            updated_at = CURRENT_TIMESTAMP
        WHERE id = po_rec.id;
    END LOOP;
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END CREATE_INTEGRATED_PAYMENT_REQUEST;

-- 3. إجراء تحديث حالة التكامل عند تنفيذ الحوالة
CREATE OR REPLACE PROCEDURE UPDATE_INTEGRATION_ON_TRANSFER_EXECUTION(
    p_transfer_id IN NUMBER,
    p_transfer_status IN VARCHAR2,
    p_execution_date IN DATE,
    p_updated_by IN NUMBER
) AS
    v_transfer_request_id NUMBER;
    v_supplier_id NUMBER;
BEGIN
    -- الحصول على معرف طلب الحوالة
    SELECT transfer_request_id INTO v_transfer_request_id
    FROM TRANSFERS
    WHERE id = p_transfer_id;
    
    -- الحصول على معرف المورد
    SELECT supplier_id INTO v_supplier_id
    FROM TRANSFER_REQUESTS
    WHERE id = v_transfer_request_id;
    
    -- تحديث حالة مدفوعات أوامر الشراء
    UPDATE PURCHASE_ORDER_PAYMENTS SET
        transfer_id = p_transfer_id,
        payment_status = CASE 
            WHEN p_transfer_status = 'COMPLETED' THEN 'COMPLETED'
            WHEN p_transfer_status = 'EXECUTED' THEN 'EXECUTED'
            ELSE 'APPROVED'
        END,
        payment_executed_date = CASE 
            WHEN p_transfer_status IN ('EXECUTED', 'COMPLETED') THEN p_execution_date
            ELSE payment_executed_date
        END,
        payment_completed_date = CASE 
            WHEN p_transfer_status = 'COMPLETED' THEN p_execution_date
            ELSE payment_completed_date
        END,
        updated_at = CURRENT_TIMESTAMP
    WHERE transfer_request_id = v_transfer_request_id;
    
    -- تحديث حالة مدفوعات الموردين
    UPDATE SUPPLIER_PAYMENT_TRANSFERS SET
        transfer_id = p_transfer_id,
        payment_status = CASE 
            WHEN p_transfer_status = 'COMPLETED' THEN 'COMPLETED'
            WHEN p_transfer_status = 'EXECUTED' THEN 'EXECUTED'
            ELSE 'APPROVED'
        END,
        executed_date = CASE 
            WHEN p_transfer_status IN ('EXECUTED', 'COMPLETED') THEN p_execution_date
            ELSE executed_date
        END,
        completed_date = CASE 
            WHEN p_transfer_status = 'COMPLETED' THEN p_execution_date
            ELSE completed_date
        END,
        updated_at = CURRENT_TIMESTAMP
    WHERE transfer_request_id = v_transfer_request_id;
    
    -- إذا اكتملت الحوالة، تحديث أوامر الشراء والأرصدة
    IF p_transfer_status = 'COMPLETED' THEN
        -- تحديث أوامر الشراء
        FOR po_payment IN (
            SELECT purchase_order_id, payment_amount, currency_code
            FROM PURCHASE_ORDER_PAYMENTS
            WHERE transfer_request_id = v_transfer_request_id
        ) LOOP
            -- استدعاء إجراء تحديث حالة الدفع
            UPDATE_PURCHASE_ORDER_PAYMENT_STATUS(
                po_payment.purchase_order_id,
                po_payment.payment_amount,
                'COMPLETED',
                v_transfer_request_id,
                p_updated_by
            );
        END LOOP;
    END IF;
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END UPDATE_INTEGRATION_ON_TRANSFER_EXECUTION;

-- 4. إجراء إنشاء إيصال استلام البضائع
CREATE OR REPLACE PROCEDURE CREATE_GOODS_RECEIPT(
    p_purchase_order_id IN NUMBER,
    p_delivery_note_number IN VARCHAR2,
    p_invoice_number IN VARCHAR2,
    p_received_by IN NUMBER,
    p_receipt_notes IN VARCHAR2 DEFAULT NULL,
    p_receipt_id OUT NUMBER
) AS
    v_receipt_number VARCHAR2(50);
    v_supplier_id NUMBER;
BEGIN
    -- الحصول على معرف المورد
    SELECT supplier_id INTO v_supplier_id
    FROM PURCHASE_ORDERS
    WHERE id = p_purchase_order_id;
    
    -- إنشاء رقم إيصال فريد
    v_receipt_number := 'GR' || TO_CHAR(SYSDATE, 'YYYYMMDD') || LPAD(GOODS_RECEIPTS_SEQ.NEXTVAL, 4, '0');
    
    -- إنشاء إيصال الاستلام
    INSERT INTO GOODS_RECEIPTS (
        receipt_number, purchase_order_id, supplier_id, receipt_date,
        delivery_note_number, invoice_number, receipt_status, quality_status,
        received_by, receipt_notes, created_at, created_by
    ) VALUES (
        v_receipt_number, p_purchase_order_id, v_supplier_id, SYSDATE,
        p_delivery_note_number, p_invoice_number, 'PENDING', 'PENDING',
        p_received_by, p_receipt_notes, CURRENT_TIMESTAMP, p_received_by
    ) RETURNING id INTO p_receipt_id;
    
    -- تحديث حالة التسليم في أمر الشراء
    UPDATE PURCHASE_ORDERS SET
        delivery_status = 'RECEIVED',
        goods_received_date = SYSDATE,
        goods_received_by = p_received_by,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_purchase_order_id;
    
    -- تسجيل تغيير الحالة
    INSERT INTO PURCHASE_ORDER_STATUS_LOG (
        purchase_order_id, old_status, new_status, status_type,
        change_reason, changed_by, related_document_type, related_document_id
    ) VALUES (
        p_purchase_order_id, 
        (SELECT delivery_status FROM PURCHASE_ORDERS WHERE id = p_purchase_order_id),
        'RECEIVED', 'DELIVERY_STATUS',
        'تم استلام البضائع - إيصال رقم: ' || v_receipt_number,
        p_received_by, 'GOODS_RECEIPT', p_receipt_id
    );
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END CREATE_GOODS_RECEIPT;

-- 5. إجراء تحديث أرصدة الموردين من أوامر الشراء
CREATE OR REPLACE PROCEDURE UPDATE_SUPPLIER_BALANCES_FROM_PO(
    p_supplier_id IN NUMBER,
    p_currency_code IN VARCHAR2
) AS
    v_total_orders NUMBER := 0;
    v_total_paid NUMBER := 0;
    v_total_outstanding NUMBER := 0;
    v_orders_count NUMBER := 0;
    v_avg_payment_days NUMBER := 0;
BEGIN
    -- حساب الإحصائيات من أوامر الشراء
    SELECT 
        COUNT(*),
        SUM(total_amount_due),
        SUM(paid_amount),
        SUM(outstanding_amount),
        AVG(CASE 
            WHEN payment_status = 'PAID' AND final_payment_date IS NOT NULL 
            THEN final_payment_date - po_date
            ELSE NULL 
        END)
    INTO v_orders_count, v_total_orders, v_total_paid, v_total_outstanding, v_avg_payment_days
    FROM PURCHASE_ORDERS
    WHERE supplier_id = p_supplier_id
    AND currency = p_currency_code
    AND status NOT IN ('CANCELLED', 'REJECTED');
    
    -- تحديث أو إنشاء رصيد المورد
    MERGE INTO SUPPLIER_BALANCES sb
    USING (
        SELECT p_supplier_id as supplier_id, p_currency_code as currency_code FROM DUAL
    ) src ON (sb.supplier_id = src.supplier_id AND sb.currency_code = src.currency_code)
    WHEN MATCHED THEN
        UPDATE SET
            debit_amount = v_total_orders,
            credit_amount = v_total_paid,
            current_balance = v_total_outstanding,
            total_invoices_count = v_orders_count,
            total_payments_count = (
                SELECT COUNT(*) FROM PURCHASE_ORDER_PAYMENTS pop
                JOIN PURCHASE_ORDERS po ON pop.purchase_order_id = po.id
                WHERE po.supplier_id = p_supplier_id
                AND po.currency = p_currency_code
                AND pop.payment_status = 'COMPLETED'
            ),
            average_payment_days = v_avg_payment_days,
            updated_at = CURRENT_TIMESTAMP
    WHEN NOT MATCHED THEN
        INSERT (
            supplier_id, currency_code, opening_balance, debit_amount,
            credit_amount, current_balance, total_invoices_count,
            total_payments_count, average_payment_days, created_at, updated_at
        ) VALUES (
            p_supplier_id, p_currency_code, 0, v_total_orders,
            v_total_paid, v_total_outstanding, v_orders_count,
            0, v_avg_payment_days, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        );
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END UPDATE_SUPPLIER_BALANCES_FROM_PO;

-- تم إنشاء الإجراءات المخزنة للتكامل بنجاح
-- Integration stored procedures created successfully
