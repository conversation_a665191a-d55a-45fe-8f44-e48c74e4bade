#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خدمة الأتمتة الاحترافية المبسطة
Simplified Professional Automation Service
"""

import sys
import os
import time
import signal
import logging
from datetime import datetime

# إضافة مسار التطبيق
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """إعداد نظام السجلات المبسط"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )
    return logging.getLogger('AutomationService')

def signal_handler(signum, frame):
    """معالج إشارات النظام"""
    print(f"\n🛑 تم استلام إشارة إيقاف: {signum}")
    
    try:
        from app.services.professional_automation_service import stop_automation_service
        stop_automation_service()
        print("✅ تم إيقاف خدمة الأتمتة بنجاح")
    except Exception as e:
        print(f"❌ خطأ في إيقاف خدمة الأتمتة: {e}")
    
    sys.exit(0)

def main():
    """الدالة الرئيسية المبسطة"""
    logger = setup_logging()
    
    # تسجيل معالجات الإشارات
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("🤖 خدمة الأتمتة الاحترافية")
    print("=" * 40)
    
    try:
        # استيراد وبدء الخدمة
        from app.services.professional_automation_service import start_automation_service, get_automation_status
        
        print("🚀 بدء خدمة الأتمتة...")
        success = start_automation_service()
        
        if success:
            print("✅ تم بدء الخدمة بنجاح!")
            
            # عرض الإعدادات
            try:
                status = get_automation_status()
                settings = status.get('settings', {})
                print(f"⏱️  فترة الفحص: {settings.get('check_interval_minutes', 5)} دقائق")
                print(f"🕐 ساعات العمل: {settings.get('working_hours_start', '08:00')} - {settings.get('working_hours_end', '18:00')}")
                print(f"📦 أقصى أوامر: {settings.get('max_orders_per_batch', 10)} أمر/دورة")
                print(f"🔄 إنشاء تلقائي: {'نعم' if settings.get('auto_create_orders', True) else 'لا'}")
                print(f"📱 إرسال تلقائي: {'نعم' if settings.get('auto_send_notifications', True) else 'لا'}")
            except:
                pass
            
            print("\n🔄 الخدمة تعمل في الخلفية...")
            print("💡 للإيقاف: اضغط Ctrl+C")
            print("=" * 40)
            
            # حلقة مراقبة مبسطة
            while True:
                try:
                    time.sleep(300)  # فحص كل 5 دقائق
                    
                    # فحص بسيط للحالة
                    status = get_automation_status()
                    if status.get('running', False):
                        print(f"✅ {datetime.now().strftime('%H:%M:%S')} - الخدمة تعمل بشكل طبيعي")
                    else:
                        print(f"⚠️ {datetime.now().strftime('%H:%M:%S')} - محاولة إعادة تشغيل الخدمة...")
                        start_automation_service()
                        
                except KeyboardInterrupt:
                    print("\n🛑 تم طلب إيقاف الخدمة")
                    break
                except Exception as e:
                    print(f"⚠️ {datetime.now().strftime('%H:%M:%S')} - خطأ: {e}")
                    time.sleep(60)  # انتظار دقيقة عند الخطأ
                    
        else:
            print("❌ فشل في بدء خدمة الأتمتة")
            print("💡 تحقق من إعدادات قاعدة البيانات")
            sys.exit(1)
            
    except ImportError as e:
        print(f"❌ خطأ في استيراد الخدمة: {e}")
        print("💡 تأكد من تشغيل الأمر من مجلد المشروع الصحيح")
        sys.exit(1)
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        sys.exit(1)
    finally:
        print("\n🏁 انتهت خدمة الأتمتة")

if __name__ == "__main__":
    main()
