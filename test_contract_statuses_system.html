<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام حالات العقود</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .test-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .status-badge {
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
        
        .test-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .test-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .test-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">
            <i class="fas fa-cogs me-2"></i>
            اختبار نظام حالات العقود الجديد
        </h1>
        
        <!-- اختبار API -->
        <div class="test-section">
            <h3><i class="fas fa-server me-2"></i>اختبار API حالات العقود</h3>
            <button class="btn btn-primary" onclick="testContractStatusesAPI()">
                <i class="fas fa-play me-2"></i>اختبار API
            </button>
            <div id="apiTestResults" class="mt-3"></div>
        </div>

        <!-- عرض الحالات -->
        <div class="test-section">
            <h3><i class="fas fa-list me-2"></i>حالات العقود المتاحة</h3>
            <div id="statusesList" class="row">
                <div class="col-12 text-muted">
                    <i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...
                </div>
            </div>
        </div>

        <!-- اختبار قائمة التصفية -->
        <div class="test-section">
            <h3><i class="fas fa-filter me-2"></i>اختبار قائمة التصفية</h3>
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">حالة العقد</label>
                    <select class="form-select" id="testStatusFilter">
                        <option value="">جميع الحالات</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-info mt-4" onclick="testFilterPopulation()">
                        <i class="fas fa-sync me-2"></i>تحديث القائمة
                    </button>
                </div>
            </div>
            <div id="filterTestResults" class="mt-3"></div>
        </div>

        <!-- اختبار نموذج إضافة عقد -->
        <div class="test-section">
            <h3><i class="fas fa-plus me-2"></i>اختبار نموذج إضافة عقد</h3>
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">حالة العقد الجديد</label>
                    <select class="form-select" id="testNewContractStatus">
                        <option value="">اختر حالة العقد...</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-success mt-4" onclick="testNewContractForm()">
                        <i class="fas fa-check me-2"></i>اختبار النموذج
                    </button>
                </div>
            </div>
            <div id="formTestResults" class="mt-3"></div>
        </div>

        <!-- نتائج الاختبار الشاملة -->
        <div class="test-section">
            <h3><i class="fas fa-clipboard-check me-2"></i>نتائج الاختبار الشاملة</h3>
            <div id="overallResults">
                <div class="test-info">
                    <i class="fas fa-info-circle me-2"></i>
                    قم بتشغيل الاختبارات لرؤية النتائج
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let contractStatuses = [];
        let testResults = {
            api: false,
            display: false,
            filter: false,
            form: false
        };

        // تحميل الحالات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء اختبار نظام حالات العقود');
            testContractStatusesAPI();
        });

        // اختبار API حالات العقود
        function testContractStatusesAPI() {
            const resultsDiv = document.getElementById('apiTestResults');
            resultsDiv.innerHTML = '<div class="test-info"><i class="fas fa-spinner fa-spin me-2"></i>جاري اختبار API...</div>';

            fetch('/contracts/api/contract-statuses')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success && data.statuses) {
                        contractStatuses = data.statuses;
                        testResults.api = true;
                        
                        resultsDiv.innerHTML = `
                            <div class="test-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>نجح الاختبار!</strong> تم جلب ${data.statuses.length} حالة من API
                            </div>
                        `;
                        
                        // عرض الحالات
                        displayStatuses(data.statuses);
                        
                        // تحديث قوائم التصفية
                        populateTestFilters(data.statuses);
                        
                    } else {
                        throw new Error(data.message || 'استجابة غير صحيحة من API');
                    }
                })
                .catch(error => {
                    testResults.api = false;
                    resultsDiv.innerHTML = `
                        <div class="test-error">
                            <i class="fas fa-times-circle me-2"></i>
                            <strong>فشل الاختبار!</strong> ${error.message}
                        </div>
                    `;
                    console.error('❌ خطأ في اختبار API:', error);
                })
                .finally(() => {
                    updateOverallResults();
                });
        }

        // عرض الحالات
        function displayStatuses(statuses) {
            const statusesList = document.getElementById('statusesList');
            
            if (!statuses || statuses.length === 0) {
                statusesList.innerHTML = '<div class="col-12 text-muted">لا توجد حالات متاحة</div>';
                testResults.display = false;
                return;
            }

            let html = '';
            statuses.forEach(status => {
                html += `
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="${status.status_icon} me-2" style="color: ${status.status_color}"></i>
                                    ${status.status_name_ar}
                                </h6>
                                <p class="card-text">
                                    <small class="text-muted">الرمز: ${status.status_code}</small><br>
                                    <small class="text-muted">الترتيب: ${status.sort_order}</small>
                                </p>
                                <span class="status-badge" style="background-color: ${status.status_color}; color: white;">
                                    ${status.status_name_en}
                                </span>
                            </div>
                        </div>
                    </div>
                `;
            });

            statusesList.innerHTML = html;
            testResults.display = true;
            
            console.log(`✅ تم عرض ${statuses.length} حالة بنجاح`);
        }

        // ملء قوائم الاختبار
        function populateTestFilters(statuses) {
            // قائمة التصفية
            const filterSelect = document.getElementById('testStatusFilter');
            const formSelect = document.getElementById('testNewContractStatus');
            
            // مسح الخيارات الموجودة
            filterSelect.innerHTML = '<option value="">جميع الحالات</option>';
            formSelect.innerHTML = '<option value="">اختر حالة العقد...</option>';
            
            // إضافة الحالات
            statuses.forEach(status => {
                // قائمة التصفية
                const filterOption = document.createElement('option');
                filterOption.value = status.status_name_ar;
                filterOption.textContent = status.status_name_ar;
                filterSelect.appendChild(filterOption);
                
                // قائمة النموذج
                const formOption = document.createElement('option');
                formOption.value = status.status_name_ar;
                formOption.textContent = status.status_name_ar;
                if (status.status_code === 'DRAFT') {
                    formOption.selected = true;
                }
                formSelect.appendChild(formOption);
            });
            
            testResults.filter = true;
            testResults.form = true;
            
            console.log('✅ تم تحديث قوائم الاختبار');
        }

        // اختبار تحديث قائمة التصفية
        function testFilterPopulation() {
            const resultsDiv = document.getElementById('filterTestResults');
            
            if (contractStatuses.length > 0) {
                populateTestFilters(contractStatuses);
                resultsDiv.innerHTML = `
                    <div class="test-success">
                        <i class="fas fa-check-circle me-2"></i>
                        تم تحديث قائمة التصفية بـ ${contractStatuses.length} حالة
                    </div>
                `;
            } else {
                resultsDiv.innerHTML = `
                    <div class="test-error">
                        <i class="fas fa-times-circle me-2"></i>
                        لا توجد حالات متاحة للتحديث
                    </div>
                `;
            }
        }

        // اختبار نموذج إضافة عقد
        function testNewContractForm() {
            const resultsDiv = document.getElementById('formTestResults');
            const selectedValue = document.getElementById('testNewContractStatus').value;
            
            if (selectedValue) {
                resultsDiv.innerHTML = `
                    <div class="test-success">
                        <i class="fas fa-check-circle me-2"></i>
                        تم اختيار الحالة: <strong>${selectedValue}</strong>
                    </div>
                `;
            } else {
                resultsDiv.innerHTML = `
                    <div class="test-error">
                        <i class="fas fa-times-circle me-2"></i>
                        يرجى اختيار حالة من القائمة
                    </div>
                `;
            }
        }

        // تحديث النتائج الشاملة
        function updateOverallResults() {
            const resultsDiv = document.getElementById('overallResults');
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(result => result === true).length;
            
            let html = `
                <div class="row">
                    <div class="col-md-6">
                        <h5>إحصائيات الاختبار</h5>
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                API Test
                                <span class="badge ${testResults.api ? 'bg-success' : 'bg-danger'}">
                                    ${testResults.api ? 'نجح' : 'فشل'}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Display Test
                                <span class="badge ${testResults.display ? 'bg-success' : 'bg-danger'}">
                                    ${testResults.display ? 'نجح' : 'فشل'}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Filter Test
                                <span class="badge ${testResults.filter ? 'bg-success' : 'bg-danger'}">
                                    ${testResults.filter ? 'نجح' : 'فشل'}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Form Test
                                <span class="badge ${testResults.form ? 'bg-success' : 'bg-danger'}">
                                    ${testResults.form ? 'نجح' : 'فشل'}
                                </span>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <div class="text-center">
                            <div class="display-4 ${passedTests === totalTests ? 'text-success' : 'text-warning'}">
                                ${passedTests}/${totalTests}
                            </div>
                            <p class="lead">اختبارات ناجحة</p>
                        </div>
                    </div>
                </div>
            `;
            
            if (passedTests === totalTests) {
                html += `
                    <div class="test-success mt-3">
                        <i class="fas fa-trophy me-2"></i>
                        <strong>تهانينا!</strong> جميع الاختبارات نجحت. نظام حالات العقود يعمل بشكل صحيح.
                    </div>
                `;
            } else {
                html += `
                    <div class="test-error mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير!</strong> بعض الاختبارات فشلت. يرجى مراجعة الإعدادات.
                    </div>
                `;
            }
            
            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
