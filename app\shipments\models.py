"""
نماذج قاعدة البيانات لنظام إدارة الشحنات
Database Models for Shipment Management System
"""

from datetime import datetime
from enum import Enum
from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship
from app import db

class ShipmentStatus(Enum):
    """حالات الشحنة"""
    PENDING = "معلق"
    CONFIRMED = "مؤكد"
    PICKED_UP = "تم الاستلام"
    IN_TRANSIT = "في الطريق"
    OUT_FOR_DELIVERY = "خارج للتسليم"
    DELIVERED = "تم التسليم"
    RETURNED = "مرتجع"
    CANCELLED = "ملغي"
    DELAYED = "متأخر"
    LOST = "مفقود"

class ShipmentType(Enum):
    """أنواع الشحنات"""
    STANDARD = "عادي"
    EXPRESS = "سريع"
    OVERNIGHT = "ليلي"
    SAME_DAY = "نفس اليوم"
    INTERNATIONAL = "دولي"
    FRAGILE = "قابل للكسر"
    HAZARDOUS = "خطر"
    COLD_CHAIN = "سلسلة باردة"

class Shipment(db.Model):
    """نموذج الشحنة الرئيسي"""
    __tablename__ = 'shipments'
    
    id = Column(Integer, primary_key=True)
    tracking_number = Column(String(50), unique=True, nullable=False, index=True)
    reference_number = Column(String(100), index=True)
    
    # معلومات المرسل
    sender_name = Column(String(200), nullable=False)
    sender_phone = Column(String(20))
    sender_email = Column(String(100))
    sender_address = Column(Text)
    sender_city = Column(String(100))
    sender_country = Column(String(100))
    sender_postal_code = Column(String(20))
    sender_latitude = Column(Float)
    sender_longitude = Column(Float)
    
    # معلومات المستقبل
    recipient_name = Column(String(200), nullable=False)
    recipient_phone = Column(String(20), nullable=False)
    recipient_email = Column(String(100))
    recipient_address = Column(Text, nullable=False)
    recipient_city = Column(String(100), nullable=False)
    recipient_country = Column(String(100), nullable=False)
    recipient_postal_code = Column(String(20))
    recipient_latitude = Column(Float)
    recipient_longitude = Column(Float)
    
    # تفاصيل الشحنة
    shipment_type = Column(String(50), default=ShipmentType.STANDARD.value)
    status = Column(String(50), default=ShipmentStatus.PENDING.value, index=True)
    priority = Column(String(20), default="عادي")  # عادي، عالي، عاجل
    
    # الأبعاد والوزن
    weight = Column(Float)  # بالكيلوجرام
    length = Column(Float)  # بالسنتيمتر
    width = Column(Float)
    height = Column(Float)
    volume = Column(Float)  # محسوب تلقائياً
    
    # التكلفة والدفع
    declared_value = Column(Float)  # القيمة المعلنة
    shipping_cost = Column(Float)
    insurance_cost = Column(Float)
    total_cost = Column(Float)
    payment_method = Column(String(50))
    payment_status = Column(String(50), default="غير مدفوع")
    
    # التواريخ
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    pickup_date = Column(DateTime)
    expected_delivery_date = Column(DateTime)
    actual_delivery_date = Column(DateTime)
    
    # معلومات إضافية
    description = Column(Text)
    special_instructions = Column(Text)
    fragile = Column(Boolean, default=False)
    signature_required = Column(Boolean, default=False)
    
    # شركة الشحن والسائق
    carrier_id = Column(Integer, ForeignKey('carriers.id'))
    driver_id = Column(Integer, ForeignKey('drivers.id'))
    vehicle_id = Column(Integer, ForeignKey('vehicles.id'))
    
    # معلومات التتبع
    current_location = Column(String(200))
    current_latitude = Column(Float)
    current_longitude = Column(Float)
    estimated_arrival = Column(DateTime)
    
    # بيانات إضافية (JSON)
    extra_data = Column(JSON)
    
    # العلاقات
    carrier = relationship("Carrier", back_populates="shipments")
    driver = relationship("Driver", back_populates="shipments")
    vehicle = relationship("Vehicle", back_populates="shipments")
    tracking_events = relationship("TrackingEvent", back_populates="shipment", cascade="all, delete-orphan")
    notifications = relationship("ShipmentNotification", back_populates="shipment", cascade="all, delete-orphan")
    
    # فهارس
    __table_args__ = (
        db.Index('idx_shipment_status_date', 'status', 'created_at'),
        db.Index('idx_shipment_recipient', 'recipient_phone', 'recipient_city'),
        db.Index('idx_shipment_location', 'current_latitude', 'current_longitude'),
    )
    
    def __repr__(self):
        return f'<Shipment {self.tracking_number}>'
    
    @property
    def status_ar(self):
        """إرجاع الحالة بالعربية"""
        return self.status
    
    @property
    def progress_percentage(self):
        """حساب نسبة التقدم"""
        status_progress = {
            ShipmentStatus.PENDING.value: 0,
            ShipmentStatus.CONFIRMED.value: 10,
            ShipmentStatus.PICKED_UP.value: 25,
            ShipmentStatus.IN_TRANSIT.value: 50,
            ShipmentStatus.OUT_FOR_DELIVERY.value: 75,
            ShipmentStatus.DELIVERED.value: 100,
            ShipmentStatus.RETURNED.value: 100,
            ShipmentStatus.CANCELLED.value: 0,
        }
        return status_progress.get(self.status, 0)

class Carrier(db.Model):
    """نموذج شركة الشحن"""
    __tablename__ = 'carriers'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(200), nullable=False)
    code = Column(String(20), unique=True, nullable=False)
    contact_person = Column(String(200))
    phone = Column(String(20))
    email = Column(String(100))
    website = Column(String(200))
    api_endpoint = Column(String(500))
    api_key = Column(String(200))
    is_active = Column(Boolean, default=True)
    
    # العلاقات
    shipments = relationship("Shipment", back_populates="carrier")
    drivers = relationship("Driver", back_populates="carrier")
    vehicles = relationship("Vehicle", back_populates="carrier")
    
    def __repr__(self):
        return f'<Carrier {self.name}>'

class Driver(db.Model):
    """نموذج السائق"""
    __tablename__ = 'drivers'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(200), nullable=False)
    phone = Column(String(20), nullable=False)
    email = Column(String(100))
    license_number = Column(String(50))
    license_expiry = Column(DateTime)
    
    # معلومات الموقع
    current_latitude = Column(Float)
    current_longitude = Column(Float)
    last_location_update = Column(DateTime)
    
    # الحالة
    is_active = Column(Boolean, default=True)
    is_available = Column(Boolean, default=True)
    
    # شركة الشحن
    carrier_id = Column(Integer, ForeignKey('carriers.id'))
    
    # العلاقات
    carrier = relationship("Carrier", back_populates="drivers")
    shipments = relationship("Shipment", back_populates="driver")
    
    def __repr__(self):
        return f'<Driver {self.name}>'

class Vehicle(db.Model):
    """نموذج المركبة"""
    __tablename__ = 'vehicles'
    
    id = Column(Integer, primary_key=True)
    plate_number = Column(String(20), unique=True, nullable=False)
    make = Column(String(100))
    model = Column(String(100))
    year = Column(Integer)
    color = Column(String(50))
    
    # المواصفات
    max_weight = Column(Float)  # الحمولة القصوى
    max_volume = Column(Float)  # الحجم الأقصى
    fuel_type = Column(String(50))
    
    # معلومات الموقع
    current_latitude = Column(Float)
    current_longitude = Column(Float)
    last_location_update = Column(DateTime)
    
    # الحالة
    is_active = Column(Boolean, default=True)
    is_available = Column(Boolean, default=True)
    
    # شركة الشحن
    carrier_id = Column(Integer, ForeignKey('carriers.id'))
    
    # العلاقات
    carrier = relationship("Carrier", back_populates="vehicles")
    shipments = relationship("Shipment", back_populates="vehicle")
    
    def __repr__(self):
        return f'<Vehicle {self.plate_number}>'

class TrackingEvent(db.Model):
    """نموذج أحداث التتبع"""
    __tablename__ = 'tracking_events'

    id = Column(Integer, primary_key=True)
    shipment_id = Column(Integer, ForeignKey('shipments.id'), nullable=False)

    # تفاصيل الحدث
    event_type = Column(String(100), nullable=False)  # نوع الحدث
    event_description = Column(Text)  # وصف الحدث
    event_time = Column(DateTime, default=datetime.utcnow, nullable=False)

    # الموقع
    location = Column(String(200))
    latitude = Column(Float)
    longitude = Column(Float)

    # معلومات إضافية
    notes = Column(Text)
    created_by = Column(String(100))  # من أنشأ الحدث

    # العلاقات
    shipment = relationship("Shipment", back_populates="tracking_events")

    def __repr__(self):
        return f'<TrackingEvent {self.event_type} for {self.shipment_id}>'

class ShipmentNotification(db.Model):
    """نموذج إشعارات الشحنة"""
    __tablename__ = 'shipment_notifications'

    id = Column(Integer, primary_key=True)
    shipment_id = Column(Integer, ForeignKey('shipments.id'), nullable=False)

    # تفاصيل الإشعار
    notification_type = Column(String(50), nullable=False)  # SMS, EMAIL, PUSH
    recipient = Column(String(200), nullable=False)  # رقم الهاتف أو الإيميل
    subject = Column(String(200))
    message = Column(Text, nullable=False)

    # الحالة
    status = Column(String(50), default="معلق")  # معلق، مرسل، فشل
    sent_at = Column(DateTime)
    delivery_status = Column(String(50))

    # معلومات إضافية
    created_at = Column(DateTime, default=datetime.utcnow)
    retry_count = Column(Integer, default=0)
    error_message = Column(Text)

    # العلاقات
    shipment = relationship("Shipment", back_populates="notifications")

    def __repr__(self):
        return f'<Notification {self.notification_type} for {self.shipment_id}>'

class ShipmentRoute(db.Model):
    """نموذج مسار الشحنة"""
    __tablename__ = 'shipment_routes'

    id = Column(Integer, primary_key=True)
    shipment_id = Column(Integer, ForeignKey('shipments.id'), nullable=False)

    # نقاط المسار
    waypoints = Column(JSON)  # قائمة بنقاط المسار
    total_distance = Column(Float)  # المسافة الإجمالية بالكيلومتر
    estimated_duration = Column(Integer)  # الوقت المقدر بالدقائق

    # معلومات المسار
    route_type = Column(String(50), default="أسرع")  # أسرع، أقصر، اقتصادي
    traffic_conditions = Column(String(50))
    weather_conditions = Column(String(100))

    # التواريخ
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Route for Shipment {self.shipment_id}>'

class DeliveryAttempt(db.Model):
    """نموذج محاولات التسليم"""
    __tablename__ = 'delivery_attempts'

    id = Column(Integer, primary_key=True)
    shipment_id = Column(Integer, ForeignKey('shipments.id'), nullable=False)

    # تفاصيل المحاولة
    attempt_number = Column(Integer, nullable=False)
    attempt_date = Column(DateTime, default=datetime.utcnow)
    status = Column(String(50), nullable=False)  # نجح، فشل، لم يجد أحد

    # سبب الفشل
    failure_reason = Column(String(200))
    notes = Column(Text)

    # معلومات التسليم
    delivered_to = Column(String(200))  # اسم من استلم
    signature = Column(Text)  # التوقيع (base64)
    photo = Column(Text)  # صورة التسليم (base64)

    # الموقع
    delivery_latitude = Column(Float)
    delivery_longitude = Column(Float)

    # السائق
    driver_id = Column(Integer, ForeignKey('drivers.id'))

    def __repr__(self):
        return f'<DeliveryAttempt {self.attempt_number} for {self.shipment_id}>'
