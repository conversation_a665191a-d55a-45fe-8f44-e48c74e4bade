-- =====================================================
-- إصلاح Trigger الأتمتة للعمل مع CARGO_SHIPMENTS
-- Fix Automation Trigger for CARGO_SHIPMENTS
-- =====================================================

-- إنشاء trigger للأتمتة على جدول CARGO_SHIPMENTS
CREATE OR REPLACE TRIGGER trg_cargo_shipment_automation
    AFTER UPDATE OF shipment_status ON cargo_shipments
    FOR EACH ROW
    WHEN (OLD.shipment_status != NEW.shipment_status)
DECLARE
    v_count NUMBER;
BEGIN
    -- تسجيل تغيير الحالة في جدول المراقبة
    INSERT INTO shipment_status_changes (
        id, shipment_id, old_status, new_status, 
        changed_at, automation_processed
    ) VALUES (
        shipment_status_changes_seq.NEXTVAL,
        :NEW.id,
        :OLD.shipment_status,
        :NEW.shipment_status,
        CURRENT_TIMESTAMP,
        0  -- لم تتم المعالجة بعد
    );
    
    -- إضافة للطابور دائماً (سيتم فحص القواعد في معالج الطابور)
    INSERT INTO automation_queue (
        id, shipment_id, old_status, new_status,
        created_at, processed, processing_attempts
    ) VALUES (
        automation_queue_seq.NEXTVAL,
        :NEW.id,
        :OLD.shipment_status,
        :NEW.shipment_status,
        CURRENT_TIMESTAMP,
        0,
        0
    );
    
EXCEPTION
    WHEN OTHERS THEN
        -- تسجيل الخطأ دون إيقاف العملية الأساسية
        BEGIN
            INSERT INTO automation_errors (
                id, error_message, shipment_id, occurred_at
            ) VALUES (
                automation_errors_seq.NEXTVAL,
                SQLERRM,
                :NEW.id,
                CURRENT_TIMESTAMP
            );
        EXCEPTION
            WHEN OTHERS THEN
                NULL; -- تجاهل أخطاء تسجيل الأخطاء
        END;
END;
/

-- التأكد من وجود الجداول والـ sequences المطلوبة
-- فحص وجود جدول shipment_status_changes
DECLARE
    v_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_count
    FROM user_tables
    WHERE table_name = 'SHIPMENT_STATUS_CHANGES';
    
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE '
        CREATE TABLE shipment_status_changes (
            id NUMBER PRIMARY KEY,
            shipment_id NUMBER NOT NULL,
            old_status VARCHAR2(50),
            new_status VARCHAR2(50),
            changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            automation_processed NUMBER(1) DEFAULT 0,
            changed_by NUMBER DEFAULT 1
        )';
        
        EXECUTE IMMEDIATE 'CREATE SEQUENCE shipment_status_changes_seq START WITH 1 INCREMENT BY 1';
        
        DBMS_OUTPUT.PUT_LINE('✅ تم إنشاء جدول shipment_status_changes');
    END IF;
END;
/

-- فحص وجود جدول automation_queue
DECLARE
    v_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_count
    FROM user_tables
    WHERE table_name = 'AUTOMATION_QUEUE';
    
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE '
        CREATE TABLE automation_queue (
            id NUMBER PRIMARY KEY,
            shipment_id NUMBER NOT NULL,
            old_status VARCHAR2(50),
            new_status VARCHAR2(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed NUMBER(1) DEFAULT 0,
            processed_at TIMESTAMP,
            processing_attempts NUMBER DEFAULT 0,
            last_error CLOB
        )';
        
        EXECUTE IMMEDIATE 'CREATE SEQUENCE automation_queue_seq START WITH 1 INCREMENT BY 1';
        
        DBMS_OUTPUT.PUT_LINE('✅ تم إنشاء جدول automation_queue');
    END IF;
END;
/

-- فحص وجود جدول automation_errors
DECLARE
    v_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_count
    FROM user_tables
    WHERE table_name = 'AUTOMATION_ERRORS';
    
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE '
        CREATE TABLE automation_errors (
            id NUMBER PRIMARY KEY,
            error_message CLOB,
            shipment_id NUMBER,
            occurred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            resolved NUMBER(1) DEFAULT 0
        )';
        
        EXECUTE IMMEDIATE 'CREATE SEQUENCE automation_errors_seq START WITH 1 INCREMENT BY 1';
        
        DBMS_OUTPUT.PUT_LINE('✅ تم إنشاء جدول automation_errors');
    END IF;
END;
/

-- إنشاء فهارس للأداء
CREATE INDEX idx_auto_queue_processed ON automation_queue(processed) TABLESPACE USERS;
CREATE INDEX idx_auto_queue_shipment ON automation_queue(shipment_id) TABLESPACE USERS;
CREATE INDEX idx_status_changes_processed ON shipment_status_changes(automation_processed) TABLESPACE USERS;

-- تأكيد التغييرات
COMMIT;

-- عرض حالة الـ triggers
SELECT trigger_name, status, triggering_event, table_name
FROM user_triggers
WHERE table_name IN ('CARGO_SHIPMENTS', 'SHIPMENTS')
AND trigger_name LIKE '%AUTOMATION%'
ORDER BY table_name, trigger_name;

PROMPT =====================================================
PROMPT تم إصلاح Trigger الأتمتة بنجاح!
PROMPT الآن ستعمل الأتمتة مع جدول CARGO_SHIPMENTS
PROMPT =====================================================
