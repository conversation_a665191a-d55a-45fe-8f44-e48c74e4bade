<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح طارئ - الأزرار لا تعمل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
</head>
<body>
    <div class="container mt-4">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    إصلاح طارئ - الأزرار لا تعمل
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <h6><i class="fas fa-bug me-2"></i>المشكلة:</h6>
                    <p>جميع الأزرار في الصفحة لا تعمل والجدول فارغ</p>
                    <p><strong>السبب المحتمل:</strong> خطأ JavaScript يوقف تنفيذ جميع الأكواد</p>
                </div>
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-tools me-2"></i>خطوات الإصلاح:</h6>
                    <ol>
                        <li>فحص أخطاء JavaScript في Console</li>
                        <li>فحص تحميل المكتبات</li>
                        <li>اختبار الأزرار الأساسية</li>
                        <li>اختبار الجدول</li>
                    </ol>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-primary w-100 mb-3" onclick="checkConsoleErrors()">
                            <i class="fas fa-bug me-2"></i>
                            فحص أخطاء Console
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-success w-100 mb-3" onclick="testBasicFunctions()">
                            <i class="fas fa-play me-2"></i>
                            اختبار الوظائف الأساسية
                        </button>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-warning w-100 mb-3" onclick="testTable()">
                            <i class="fas fa-table me-2"></i>
                            اختبار الجدول
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-info w-100 mb-3" onclick="emergencyFix()">
                            <i class="fas fa-wrench me-2"></i>
                            إصلاح طارئ
                        </button>
                    </div>
                </div>
                
                <div id="results"></div>
                
                <!-- جدول اختبار -->
                <div class="mt-4">
                    <h6>جدول الاختبار:</h6>
                    <div class="table-responsive">
                        <table id="emergencyTable" class="table table-striped">
                            <thead>
                                <tr>
                                    <th>كود الصنف</th>
                                    <th>اسم الصنف</th>
                                    <th>المورد</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- معلومات التشخيص -->
                <div class="mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h6>معلومات التشخيص</h6>
                        </div>
                        <div class="card-body">
                            <div id="diagnosticInfo"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات بالترتيب الصحيح -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>

    <script>
    let emergencyTableInstance = null;
    
    function showResult(type, title, message) {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'danger' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        const icon = type === 'success' ? 'fa-check-circle' : 
                    type === 'danger' ? 'fa-exclamation-triangle' : 
                    type === 'warning' ? 'fa-exclamation-circle' : 'fa-info-circle';
        
        $('#results').html(`
            <div class="alert ${alertClass}">
                <h6><i class="fas ${icon} me-2"></i>${title}</h6>
                <div>${message}</div>
            </div>
        `);
    }
    
    function checkConsoleErrors() {
        console.log('🔍 فحص أخطاء Console...');
        
        // محاولة تشغيل كود بسيط لاختبار JavaScript
        try {
            const testVar = 'اختبار';
            console.log('JavaScript يعمل:', testVar);
            
            showResult('success', 'JavaScript يعمل', 
                'لا توجد أخطاء أساسية في JavaScript<br>' +
                'افتح Developer Tools (F12) وتحقق من تبويب Console للمزيد من التفاصيل');
                
        } catch (error) {
            showResult('danger', 'خطأ في JavaScript', 
                `خطأ أساسي: ${error.message}`);
        }
    }
    
    function testBasicFunctions() {
        console.log('🧪 اختبار الوظائف الأساسية...');
        
        let results = '<ul>';
        
        // فحص jQuery
        if (typeof $ !== 'undefined') {
            results += '<li class="text-success">✅ jQuery متوفر (الإصدار: ' + $.fn.jquery + ')</li>';
        } else {
            results += '<li class="text-danger">❌ jQuery غير متوفر</li>';
        }
        
        // فحص DataTables
        if (typeof $.fn.DataTable !== 'undefined') {
            results += '<li class="text-success">✅ DataTables متوفر</li>';
        } else {
            results += '<li class="text-danger">❌ DataTables غير متوفر</li>';
        }
        
        // فحص Bootstrap
        if (typeof bootstrap !== 'undefined') {
            results += '<li class="text-success">✅ Bootstrap متوفر</li>';
        } else {
            results += '<li class="text-warning">⚠️ Bootstrap غير متوفر</li>';
        }
        
        // اختبار DOM
        const tableExists = $('#emergencyTable').length > 0;
        if (tableExists) {
            results += '<li class="text-success">✅ عنصر الجدول موجود</li>';
        } else {
            results += '<li class="text-danger">❌ عنصر الجدول غير موجود</li>';
        }
        
        results += '</ul>';
        
        showResult('info', 'نتائج اختبار الوظائف الأساسية', results);
    }
    
    function testTable() {
        console.log('📊 اختبار الجدول...');
        
        try {
            // تدمير الجدول إذا كان موجوداً
            if (emergencyTableInstance) {
                emergencyTableInstance.destroy();
                emergencyTableInstance = null;
            }
            
            // إنشاء جدول جديد
            emergencyTableInstance = $('#emergencyTable').DataTable({
                destroy: true,
                language: {
                    processing: "جاري المعالجة...",
                    search: "بحث:",
                    emptyTable: "لا توجد بيانات متاحة",
                    zeroRecords: "لم يعثر على أية سجلات"
                },
                pageLength: 5
            });
            
            // إضافة بيانات تجريبية
            emergencyTableInstance.row.add([
                'TEST001',
                'صنف تجريبي',
                'مورد تجريبي',
                '100',
                '¥50.00'
            ]).draw();
            
            showResult('success', 'نجح اختبار الجدول!', 
                'تم إنشاء الجدول وإضافة بيانات تجريبية بنجاح');
                
        } catch (error) {
            showResult('danger', 'فشل اختبار الجدول', 
                `خطأ: ${error.message}`);
        }
    }
    
    function emergencyFix() {
        console.log('🚨 بدء الإصلاح الطارئ...');
        
        showResult('info', 'جاري الإصلاح الطارئ...', 'يرجى الانتظار');
        
        // الخطوة 1: فحص المكتبات
        if (typeof $ === 'undefined') {
            showResult('danger', 'فشل الإصلاح', 'jQuery غير متوفر');
            return;
        }
        
        if (typeof $.fn.DataTable === 'undefined') {
            showResult('danger', 'فشل الإصلاح', 'DataTables غير متوفر');
            return;
        }
        
        // الخطوة 2: اختبار API
        fetch('/purchase-orders/api/items/data')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.data && data.data.length > 0) {
                    // الخطوة 3: إنشاء جدول جديد
                    try {
                        if (emergencyTableInstance) {
                            emergencyTableInstance.destroy();
                        }
                        
                        $('#emergencyTable tbody').empty();
                        
                        // إضافة البيانات
                        data.data.slice(0, 5).forEach(item => {
                            $('#emergencyTable tbody').append(`
                                <tr>
                                    <td>${item.item_code || ''}</td>
                                    <td>${item.item_name || ''}</td>
                                    <td>${item.supplier_name || 'غير محدد'}</td>
                                    <td>${item.total_quantity || 0}</td>
                                    <td>¥${(item.avg_price || 0).toFixed(2)}</td>
                                </tr>
                            `);
                        });
                        
                        // تهيئة الجدول
                        emergencyTableInstance = $('#emergencyTable').DataTable({
                            destroy: true,
                            language: {
                                processing: "جاري المعالجة...",
                                search: "بحث:",
                                emptyTable: "لا توجد بيانات متاحة"
                            }
                        });
                        
                        showResult('success', 'تم الإصلاح الطارئ بنجاح!', 
                            `تم تحميل ${data.data.length} صنف من قاعدة البيانات<br>` +
                            'الجدول والأزرار تعمل الآن بشكل صحيح');
                            
                    } catch (error) {
                        showResult('danger', 'فشل في إنشاء الجدول', error.message);
                    }
                    
                } else {
                    showResult('warning', 'API يعمل لكن لا توجد بيانات', 
                        'تحقق من وجود بيانات في جدول PO_ITEMS');
                }
            })
            .catch(error => {
                showResult('danger', 'فشل في الاتصال بـ API', 
                    `خطأ: ${error.message}`);
            });
    }
    
    // معلومات التشخيص التلقائي
    $(document).ready(function() {
        console.log('🚀 بدء التشخيص التلقائي...');
        
        let info = '<strong>معلومات النظام:</strong><br>';
        info += `المتصفح: ${navigator.userAgent}<br>`;
        info += `التاريخ: ${new Date().toLocaleString('ar-SA')}<br>`;
        info += `jQuery: ${typeof $ !== 'undefined' ? $.fn.jquery : 'غير متوفر'}<br>`;
        info += `DataTables: ${typeof $.fn.DataTable !== 'undefined' ? 'متوفر' : 'غير متوفر'}<br>`;
        
        $('#diagnosticInfo').html(info);
        
        // تشخيص تلقائي بعد ثانيتين
        setTimeout(() => {
            console.log('🔍 بدء التشخيص التلقائي...');
            testBasicFunctions();
        }, 2000);
    });
    </script>
</body>
</html>
