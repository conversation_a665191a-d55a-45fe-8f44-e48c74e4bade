#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الموانئ الذكي المتكامل مع الشحن
Smart Ports System Integrated with Shipping
"""

from database_manager import DatabaseManager
from flask import current_app
import json
import re
from datetime import datetime
from typing import List, Dict, Optional

class SmartPortsEngine:
    """محرك الموانئ الذكي"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.world_ports_data = self._load_world_ports_from_database()
    
    def _load_world_ports_from_database(self):
        """تحميل بيانات الموانئ من قاعدة البيانات الموحدة"""
        try:
            current_app.logger.info("Loading ports from unified database...")

            ports_data = self.db_manager.execute_query("""
                SELECT id, port_code, port_name, port_name_arabic, country, country_arabic,
                       city, city_arabic, region, continent, latitude, longitude,
                       major_port, cargo_types, popularity_score
                FROM unified_ports
                WHERE is_active = 1
                ORDER BY popularity_score DESC, major_port DESC, port_name
            """)

            ports_list = []
            for port in ports_data or []:
                port_dict = {
                    "id": port[0],
                    "code": port[1],
                    "name": port[2],
                    "name_ar": port[3] if port[3] else "",
                    "country": port[4],
                    "country_ar": port[5] if port[5] else "",
                    "city": port[6],
                    "city_ar": port[7] if port[7] else "",
                    "region": port[8] if port[8] else "",
                    "continent": port[9] if port[9] else "",
                    "lat": float(port[10]) if port[10] else 0.0,
                    "lng": float(port[11]) if port[11] else 0.0,
                    "major": bool(port[12]),
                    "cargo_types": port[13] if port[13] else "",
                    "popularity_score": port[14] if port[14] else 0
                }
                ports_list.append(port_dict)

            current_app.logger.info(f"Loaded {len(ports_list)} ports from database")
            return ports_list

        except Exception as e:
            current_app.logger.error(f"Error loading ports from database: {e}")
            # العودة للبيانات المُعرَّفة مسبقاً كـ fallback
            return self._load_world_ports_data_fallback()

    def _load_world_ports_data_fallback(self):
        """تحميل بيانات الموانئ العالمية الشاملة (نسخة احتياطية)"""
        return [
            # موانئ اليمن
            {
                "code": "YEADE", "name": "Port of Aden", "name_ar": "ميناء عدن",
                "country": "Yemen", "country_ar": "اليمن", "city": "Aden", "city_ar": "عدن",
                "region": "Middle East", "continent": "Asia", "major": True,
                "lat": 12.7794, "lng": 45.0367, "cargo_types": "حاويات,بضائع عامة,ترانزيت,وقود"
            },
            {
                "code": "YEHOD", "name": "Port of Hodeidah", "name_ar": "ميناء الحديدة",
                "country": "Yemen", "country_ar": "اليمن", "city": "Hodeidah", "city_ar": "الحديدة",
                "region": "Middle East", "continent": "Asia", "major": True,
                "lat": 14.7978, "lng": 42.9545, "cargo_types": "حاويات,بضائع عامة,مواد غذائية"
            },
            {
                "code": "YEMUK", "name": "Port of Mukalla", "name_ar": "ميناء المكلا",
                "country": "Yemen", "country_ar": "اليمن", "city": "Mukalla", "city_ar": "المكلا",
                "region": "Middle East", "continent": "Asia", "major": False,
                "lat": 14.5425, "lng": 49.1242, "cargo_types": "بضائع عامة,أسماك,وقود"
            },
            {
                "code": "YEMOC", "name": "Port of Mocha", "name_ar": "ميناء المخا",
                "country": "Yemen", "country_ar": "اليمن", "city": "Mocha", "city_ar": "المخا",
                "region": "Middle East", "continent": "Asia", "major": False,
                "lat": 13.3186, "lng": 43.2486, "cargo_types": "بضائع عامة,قهوة,بضائع تقليدية"
            },
            {
                "code": "YENSH", "name": "Port of Nishtun", "name_ar": "ميناء نشطون",
                "country": "Yemen", "country_ar": "اليمن", "city": "Nishtun", "city_ar": "نشطون",
                "region": "Middle East", "continent": "Asia", "major": False,
                "lat": 15.8500, "lng": 52.2167, "cargo_types": "بضائع عامة,أسماك"
            },

            # موانئ السعودية
            {
                "code": "SARIG", "name": "King Abdulaziz Port", "name_ar": "ميناء الملك عبدالعزيز",
                "country": "Saudi Arabia", "country_ar": "السعودية", "city": "Dammam", "city_ar": "الدمام",
                "region": "Middle East", "continent": "Asia", "major": True,
                "lat": 26.3927, "lng": 50.1059, "cargo_types": "حاويات,بضائع عامة,بتروكيماويات"
            },
            {
                "code": "SAJED", "name": "King Abdullah Port", "name_ar": "ميناء الملك عبدالله",
                "country": "Saudi Arabia", "country_ar": "السعودية", "city": "Rabigh", "city_ar": "رابغ",
                "region": "Middle East", "continent": "Asia", "major": True,
                "lat": 22.7206, "lng": 39.0142, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "SAJUB", "name": "Jubail Commercial Port", "name_ar": "ميناء الجبيل التجاري",
                "country": "Saudi Arabia", "country_ar": "السعودية", "city": "Jubail", "city_ar": "الجبيل",
                "region": "Middle East", "continent": "Asia", "major": True,
                "lat": 27.0174, "lng": 49.6251, "cargo_types": "بتروكيماويات,بضائع عامة"
            },
            {
                "code": "SAYNB", "name": "Yanbu Commercial Port", "name_ar": "ميناء ينبع التجاري",
                "country": "Saudi Arabia", "country_ar": "السعودية", "city": "Yanbu", "city_ar": "ينبع",
                "region": "Middle East", "continent": "Asia", "major": True,
                "lat": 24.0889, "lng": 38.0617, "cargo_types": "بتروكيماويات,حاويات"
            },
            {
                "code": "AEJEA", "name": "Jebel Ali Port", "name_ar": "ميناء جبل علي",
                "country": "UAE", "country_ar": "الإمارات", "city": "Dubai", "city_ar": "دبي",
                "region": "Middle East", "continent": "Asia", "major": True,
                "lat": 25.0657, "lng": 55.1713, "cargo_types": "حاويات,بضائع عامة,ترانزيت"
            },
            {
                "code": "AEAUH", "name": "Khalifa Port", "name_ar": "ميناء خليفة",
                "country": "UAE", "country_ar": "الإمارات", "city": "Abu Dhabi", "city_ar": "أبوظبي",
                "region": "Middle East", "continent": "Asia", "major": True,
                "lat": 24.5373, "lng": 54.6077, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "KWKWI", "name": "Shuwaikh Port", "name_ar": "ميناء الشويخ",
                "country": "Kuwait", "country_ar": "الكويت", "city": "Kuwait City", "city_ar": "مدينة الكويت",
                "region": "Middle East", "continent": "Asia", "major": True,
                "lat": 29.3375, "lng": 47.9216, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "QADOH", "name": "Hamad Port", "name_ar": "ميناء حمد",
                "country": "Qatar", "country_ar": "قطر", "city": "Doha", "city_ar": "الدوحة",
                "region": "Middle East", "continent": "Asia", "major": True,
                "lat": 25.1742, "lng": 51.2808, "cargo_types": "حاويات,بضائع عامة"
            },

            # موانئ مصر
            {
                "code": "EGALY", "name": "Port of Alexandria", "name_ar": "ميناء الإسكندرية",
                "country": "Egypt", "country_ar": "مصر", "city": "Alexandria", "city_ar": "الإسكندرية",
                "region": "North Africa", "continent": "Africa", "major": True,
                "lat": 31.2001, "lng": 29.9187, "cargo_types": "حاويات,بضائع عامة,حبوب"
            },
            {
                "code": "EGPSD", "name": "Port Said Port", "name_ar": "ميناء بورسعيد",
                "country": "Egypt", "country_ar": "مصر", "city": "Port Said", "city_ar": "بورسعيد",
                "region": "North Africa", "continent": "Africa", "major": True,
                "lat": 31.2653, "lng": 32.3019, "cargo_types": "حاويات,بضائع عامة,ترانزيت"
            },
            {
                "code": "EGDAM", "name": "Damietta Port", "name_ar": "ميناء دمياط",
                "country": "Egypt", "country_ar": "مصر", "city": "Damietta", "city_ar": "دمياط",
                "region": "North Africa", "continent": "Africa", "major": True,
                "lat": 31.4165, "lng": 31.8133, "cargo_types": "حاويات,بضائع عامة,أثاث"
            },
            {
                "code": "EGSUZ", "name": "Suez Port", "name_ar": "ميناء السويس",
                "country": "Egypt", "country_ar": "مصر", "city": "Suez", "city_ar": "السويس",
                "region": "North Africa", "continent": "Africa", "major": False,
                "lat": 29.9668, "lng": 32.5498, "cargo_types": "بضائع عامة,بتروكيماويات"
            },
            {
                "code": "EGHUR", "name": "Hurghada Port", "name_ar": "ميناء الغردقة",
                "country": "Egypt", "country_ar": "مصر", "city": "Hurghada", "city_ar": "الغردقة",
                "region": "North Africa", "continent": "Africa", "major": False,
                "lat": 27.2579, "lng": 33.8116, "cargo_types": "سياحة,بضائع عامة"
            },

            # موانئ المغرب
            {
                "code": "MAAPT", "name": "Port of Casablanca", "name_ar": "ميناء الدار البيضاء",
                "country": "Morocco", "country_ar": "المغرب", "city": "Casablanca", "city_ar": "الدار البيضاء",
                "region": "North Africa", "continent": "Africa", "major": True,
                "lat": 33.6024, "lng": -7.6164, "cargo_types": "حاويات,بضائع عامة,فوسفات"
            },
            {
                "code": "MATAN", "name": "Tanger Med Port", "name_ar": "ميناء طنجة المتوسط",
                "country": "Morocco", "country_ar": "المغرب", "city": "Tangier", "city_ar": "طنجة",
                "region": "North Africa", "continent": "Africa", "major": True,
                "lat": 35.8781, "lng": -5.8077, "cargo_types": "حاويات,ترانزيت,سيارات"
            },
            {
                "code": "MAAGA", "name": "Port of Agadir", "name_ar": "ميناء أكادير",
                "country": "Morocco", "country_ar": "المغرب", "city": "Agadir", "city_ar": "أكادير",
                "region": "North Africa", "continent": "Africa", "major": False,
                "lat": 30.4278, "lng": -9.5981, "cargo_types": "أسماك,حمضيات,بضائع عامة"
            },

            # موانئ الجزائر
            {
                "code": "DZALG", "name": "Port of Algiers", "name_ar": "ميناء الجزائر",
                "country": "Algeria", "country_ar": "الجزائر", "city": "Algiers", "city_ar": "الجزائر",
                "region": "North Africa", "continent": "Africa", "major": True,
                "lat": 36.7631, "lng": 3.0557, "cargo_types": "حاويات,بضائع عامة,هيدروكربونات"
            },
            {
                "code": "DZORN", "name": "Port of Oran", "name_ar": "ميناء وهران",
                "country": "Algeria", "country_ar": "الجزائر", "city": "Oran", "city_ar": "وهران",
                "region": "North Africa", "continent": "Africa", "major": False,
                "lat": 35.7089, "lng": -0.6416, "cargo_types": "بضائع عامة,حديد,حبوب"
            },

            # موانئ تونس
            {
                "code": "TNTU", "name": "Port of Tunis", "name_ar": "ميناء تونس",
                "country": "Tunisia", "country_ar": "تونس", "city": "Tunis", "city_ar": "تونس",
                "region": "North Africa", "continent": "Africa", "major": True,
                "lat": 36.8485, "lng": 10.2372, "cargo_types": "حاويات,بضائع عامة,زيت زيتون"
            },
            {
                "code": "TNSFX", "name": "Port of Sfax", "name_ar": "ميناء صفاقس",
                "country": "Tunisia", "country_ar": "تونس", "city": "Sfax", "city_ar": "صفاقس",
                "region": "North Africa", "continent": "Africa", "major": False,
                "lat": 34.7406, "lng": 10.7603, "cargo_types": "فوسفات,زيت زيتون,بضائع عامة"
            },

            # موانئ ليبيا
            {
                "code": "LYTIP", "name": "Port of Tripoli", "name_ar": "ميناء طرابلس",
                "country": "Libya", "country_ar": "ليبيا", "city": "Tripoli", "city_ar": "طرابلس",
                "region": "North Africa", "continent": "Africa", "major": True,
                "lat": 32.8872, "lng": 13.1913, "cargo_types": "بضائع عامة,نفط,حبوب"
            },
            {
                "code": "LYBNZ", "name": "Port of Benghazi", "name_ar": "ميناء بنغازي",
                "country": "Libya", "country_ar": "ليبيا", "city": "Benghazi", "city_ar": "بنغازي",
                "region": "North Africa", "continent": "Africa", "major": False,
                "lat": 32.1181, "lng": 20.0680, "cargo_types": "بضائع عامة,نفط"
            },

            # موانئ السودان
            {
                "code": "SDPZU", "name": "Port Sudan", "name_ar": "بورتسودان",
                "country": "Sudan", "country_ar": "السودان", "city": "Port Sudan", "city_ar": "بورتسودان",
                "region": "North Africa", "continent": "Africa", "major": True,
                "lat": 19.6348, "lng": 37.2152, "cargo_types": "حاويات,بضائع عامة,نفط,حبوب"
            },

            # موانئ الأردن
            {
                "code": "JOAQJ", "name": "Port of Aqaba", "name_ar": "ميناء العقبة",
                "country": "Jordan", "country_ar": "الأردن", "city": "Aqaba", "city_ar": "العقبة",
                "region": "Middle East", "continent": "Asia", "major": True,
                "lat": 29.5267, "lng": 35.0081, "cargo_types": "حاويات,بضائع عامة,فوسفات,بوتاس"
            },

            # موانئ لبنان
            {
                "code": "LBBEY", "name": "Port of Beirut", "name_ar": "ميناء بيروت",
                "country": "Lebanon", "country_ar": "لبنان", "city": "Beirut", "city_ar": "بيروت",
                "region": "Middle East", "continent": "Asia", "major": True,
                "lat": 33.9018, "lng": 35.5149, "cargo_types": "حاويات,بضائع عامة,ترانزيت"
            },
            {
                "code": "LBTRI", "name": "Port of Tripoli", "name_ar": "ميناء طرابلس",
                "country": "Lebanon", "country_ar": "لبنان", "city": "Tripoli", "city_ar": "طرابلس",
                "region": "Middle East", "continent": "Asia", "major": False,
                "lat": 34.4367, "lng": 35.8497, "cargo_types": "بضائع عامة,نفط"
            },

            # موانئ سوريا
            {
                "code": "SYLAT", "name": "Port of Lattakia", "name_ar": "ميناء اللاذقية",
                "country": "Syria", "country_ar": "سوريا", "city": "Lattakia", "city_ar": "اللاذقية",
                "region": "Middle East", "continent": "Asia", "major": True,
                "lat": 35.5138, "lng": 35.7888, "cargo_types": "حاويات,بضائع عامة,حبوب"
            },
            {
                "code": "SYTAR", "name": "Port of Tartus", "name_ar": "ميناء طرطوس",
                "country": "Syria", "country_ar": "سوريا", "city": "Tartus", "city_ar": "طرطوس",
                "region": "Middle East", "continent": "Asia", "major": False,
                "lat": 34.8889, "lng": 35.8869, "cargo_types": "بضائع عامة,نفط"
            },

            # موانئ العراق
            {
                "code": "IQUMQ", "name": "Port of Umm Qasr", "name_ar": "ميناء أم قصر",
                "country": "Iraq", "country_ar": "العراق", "city": "Umm Qasr", "city_ar": "أم قصر",
                "region": "Middle East", "continent": "Asia", "major": True,
                "lat": 30.0356, "lng": 47.9217, "cargo_types": "حاويات,بضائع عامة,حبوب"
            },
            {
                "code": "IQKHO", "name": "Port of Khor Al-Zubair", "name_ar": "ميناء خور الزبير",
                "country": "Iraq", "country_ar": "العراق", "city": "Khor Al-Zubair", "city_ar": "خور الزبير",
                "region": "Middle East", "continent": "Asia", "major": False,
                "lat": 30.3833, "lng": 47.7167, "cargo_types": "نفط,بضائع عامة"
            },

            # موانئ إيران
            {
                "code": "IRBND", "name": "Bandar Abbas Port", "name_ar": "ميناء بندر عباس",
                "country": "Iran", "country_ar": "إيران", "city": "Bandar Abbas", "city_ar": "بندر عباس",
                "region": "Middle East", "continent": "Asia", "major": True,
                "lat": 27.1832, "lng": 56.2666, "cargo_types": "حاويات,بضائع عامة,نفط"
            },
            {
                "code": "IRIKM", "name": "Imam Khomeini Port", "name_ar": "ميناء الإمام الخميني",
                "country": "Iran", "country_ar": "إيران", "city": "Mahshahr", "city_ar": "ماهشهر",
                "region": "Middle East", "continent": "Asia", "major": True,
                "lat": 30.5586, "lng": 49.1519, "cargo_types": "بتروكيماويات,حاويات,بضائع عامة"
            },

            # موانئ تركيا الشاملة
            {
                "code": "TRIST", "name": "Port of Istanbul", "name_ar": "ميناء إسطنبول",
                "country": "Turkey", "country_ar": "تركيا", "city": "Istanbul", "city_ar": "إسطنبول",
                "region": "Marmara", "continent": "Europe", "major": True,
                "lat": 41.0082, "lng": 28.9784, "cargo_types": "حاويات,بضائع عامة,ترانزيت"
            },
            {
                "code": "TRIZM", "name": "Port of Izmir", "name_ar": "ميناء إزمير",
                "country": "Turkey", "country_ar": "تركيا", "city": "Izmir", "city_ar": "إزمير",
                "region": "Aegean", "continent": "Europe", "major": True,
                "lat": 38.4237, "lng": 27.1428, "cargo_types": "حاويات,بضائع عامة,منسوجات"
            },
            {
                "code": "TRMER", "name": "Port of Mersin", "name_ar": "ميناء مرسين",
                "country": "Turkey", "country_ar": "تركيا", "city": "Mersin", "city_ar": "مرسين",
                "region": "Mediterranean", "continent": "Asia", "major": True,
                "lat": 36.8000, "lng": 34.6333, "cargo_types": "حاويات,بضائع عامة,حبوب"
            },
            {
                "code": "TRISK", "name": "Port of Iskenderun", "name_ar": "ميناء إسكندرون",
                "country": "Turkey", "country_ar": "تركيا", "city": "Iskenderun", "city_ar": "إسكندرون",
                "region": "Mediterranean", "continent": "Asia", "major": True,
                "lat": 36.5875, "lng": 36.1742, "cargo_types": "حاويات,بضائع عامة,حديد"
            },
            {
                "code": "TRTRA", "name": "Port of Trabzon", "name_ar": "ميناء طرابزون",
                "country": "Turkey", "country_ar": "تركيا", "city": "Trabzon", "city_ar": "طرابزون",
                "region": "Black Sea", "continent": "Asia", "major": True,
                "lat": 41.0015, "lng": 39.7178, "cargo_types": "بضائع عامة,ترانزيت,شاي"
            },
            {
                "code": "TRZON", "name": "Port of Zonguldak", "name_ar": "ميناء زونغولداك",
                "country": "Turkey", "country_ar": "تركيا", "city": "Zonguldak", "city_ar": "زونغولداك",
                "region": "Black Sea", "continent": "Asia", "major": False,
                "lat": 41.4564, "lng": 31.7987, "cargo_types": "فحم,بضائع عامة"
            },
            {
                "code": "TRSAN", "name": "Port of Samsun", "name_ar": "ميناء سامسون",
                "country": "Turkey", "country_ar": "تركيا", "city": "Samsun", "city_ar": "سامسون",
                "region": "Black Sea", "continent": "Asia", "major": False,
                "lat": 41.2867, "lng": 36.3300, "cargo_types": "بضائع عامة,حبوب,تبغ"
            },
            {
                "code": "TRALI", "name": "Port of Aliaga", "name_ar": "ميناء علياغا",
                "country": "Turkey", "country_ar": "تركيا", "city": "Aliaga", "city_ar": "علياغا",
                "region": "Aegean", "continent": "Europe", "major": False,
                "lat": 38.8000, "lng": 26.9667, "cargo_types": "بتروكيماويات,نفط,بضائع عامة"
            },
            {
                "code": "TRTEK", "name": "Port of Tekirdag", "name_ar": "ميناء تكيرداغ",
                "country": "Turkey", "country_ar": "تركيا", "city": "Tekirdag", "city_ar": "تكيرداغ",
                "region": "Marmara", "continent": "Europe", "major": False,
                "lat": 40.9833, "lng": 27.5167, "cargo_types": "بضائع عامة,حبوب"
            },
            {
                "code": "TRBAN", "name": "Port of Bandirma", "name_ar": "ميناء بانديرما",
                "country": "Turkey", "country_ar": "تركيا", "city": "Bandirma", "city_ar": "بانديرما",
                "region": "Marmara", "continent": "Europe", "major": False,
                "lat": 40.3500, "lng": 27.9667, "cargo_types": "بضائع عامة,حبوب,زيت زيتون"
            },
            {
                "code": "TRCAN", "name": "Port of Canakkale", "name_ar": "ميناء تشاناكالي",
                "country": "Turkey", "country_ar": "تركيا", "city": "Canakkale", "city_ar": "تشاناكالي",
                "region": "Marmara", "continent": "Europe", "major": False,
                "lat": 40.1553, "lng": 26.4142, "cargo_types": "بضائع عامة,سيراميك"
            },
            {
                "code": "TRSIN", "name": "Port of Sinop", "name_ar": "ميناء سينوب",
                "country": "Turkey", "country_ar": "تركيا", "city": "Sinop", "city_ar": "سينوب",
                "region": "Black Sea", "continent": "Asia", "major": False,
                "lat": 42.0231, "lng": 35.1531, "cargo_types": "بضائع عامة,أسماك"
            },
            {
                "code": "TRORD", "name": "Port of Ordu", "name_ar": "ميناء أوردو",
                "country": "Turkey", "country_ar": "تركيا", "city": "Ordu", "city_ar": "أوردو",
                "region": "Black Sea", "continent": "Asia", "major": False,
                "lat": 40.9839, "lng": 37.8764, "cargo_types": "بضائع عامة,بندق"
            },
            {
                "code": "TRRIZ", "name": "Port of Rize", "name_ar": "ميناء ريزا",
                "country": "Turkey", "country_ar": "تركيا", "city": "Rize", "city_ar": "ريزا",
                "region": "Black Sea", "continent": "Asia", "major": False,
                "lat": 41.0201, "lng": 40.5234, "cargo_types": "بضائع عامة,شاي"
            },
            {
                "code": "TRART", "name": "Port of Artvin", "name_ar": "ميناء أرتفين",
                "country": "Turkey", "country_ar": "تركيا", "city": "Artvin", "city_ar": "أرتفين",
                "region": "Black Sea", "continent": "Asia", "major": False,
                "lat": 41.1828, "lng": 41.8183, "cargo_types": "بضائع عامة,خشب"
            },

            # موانئ روسيا
            {
                "code": "RUULU", "name": "Port of Vladivostok", "name_ar": "ميناء فلاديفوستوك",
                "country": "Russia", "country_ar": "روسيا", "city": "Vladivostok", "city_ar": "فلاديفوستوك",
                "region": "Far East", "continent": "Asia", "major": True,
                "lat": 43.1056, "lng": 131.8735, "cargo_types": "حاويات,بضائع عامة,أسماك"
            },
            {
                "code": "RULED", "name": "Port of St. Petersburg", "name_ar": "ميناء سانت بطرسبرغ",
                "country": "Russia", "country_ar": "روسيا", "city": "St. Petersburg", "city_ar": "سانت بطرسبرغ",
                "region": "Northwest", "continent": "Europe", "major": True,
                "lat": 59.9311, "lng": 30.3609, "cargo_types": "حاويات,بضائع عامة,نفط"
            },
            {
                "code": "RUNVS", "name": "Port of Novorossiysk", "name_ar": "ميناء نوفوروسيسك",
                "country": "Russia", "country_ar": "روسيا", "city": "Novorossiysk", "city_ar": "نوفوروسيسك",
                "region": "South", "continent": "Europe", "major": True,
                "lat": 44.7230, "lng": 37.7686, "cargo_types": "نفط,حبوب,بضائع عامة"
            },

            # موانئ أوكرانيا
            {
                "code": "UAODS", "name": "Port of Odessa", "name_ar": "ميناء أوديسا",
                "country": "Ukraine", "country_ar": "أوكرانيا", "city": "Odessa", "city_ar": "أوديسا",
                "region": "Black Sea", "continent": "Europe", "major": True,
                "lat": 46.4825, "lng": 30.7233, "cargo_types": "حاويات,حبوب,بضائع عامة"
            },

            # موانئ رومانيا
            {
                "code": "ROCND", "name": "Port of Constanta", "name_ar": "ميناء كونستانتا",
                "country": "Romania", "country_ar": "رومانيا", "city": "Constanta", "city_ar": "كونستانتا",
                "region": "Black Sea", "continent": "Europe", "major": True,
                "lat": 44.1598, "lng": 28.6348, "cargo_types": "حاويات,بضائع عامة,حبوب"
            },

            # موانئ بلغاريا
            {
                "code": "BGVAR", "name": "Port of Varna", "name_ar": "ميناء فارنا",
                "country": "Bulgaria", "country_ar": "بلغاريا", "city": "Varna", "city_ar": "فارنا",
                "region": "Black Sea", "continent": "Europe", "major": True,
                "lat": 43.2141, "lng": 27.9147, "cargo_types": "حاويات,بضائع عامة,حبوب"
            },

            # موانئ جورجيا
            {
                "code": "GEPTI", "name": "Port of Poti", "name_ar": "ميناء بوتي",
                "country": "Georgia", "country_ar": "جورجيا", "city": "Poti", "city_ar": "بوتي",
                "region": "Black Sea", "continent": "Asia", "major": True,
                "lat": 42.1481, "lng": 41.6681, "cargo_types": "حاويات,بضائع عامة,ترانزيت"
            },

            # موانئ الصين الشاملة
            {
                "code": "CNSHA", "name": "Port of Shanghai", "name_ar": "ميناء شنغهاي",
                "country": "China", "country_ar": "الصين", "city": "Shanghai", "city_ar": "شنغهاي",
                "region": "East Asia", "continent": "Asia", "major": True,
                "lat": 31.2304, "lng": 121.4737, "cargo_types": "حاويات,بضائع عامة,بضائع سائبة,بتروكيماويات"
            },
            {
                "code": "CNNGB", "name": "Port of Ningbo-Zhoushan", "name_ar": "ميناء نينغبو-تشوشان",
                "country": "China", "country_ar": "الصين", "city": "Ningbo", "city_ar": "نينغبو",
                "region": "East Asia", "continent": "Asia", "major": True,
                "lat": 29.8683, "lng": 121.5440, "cargo_types": "حاويات,بضائع عامة,بتروكيماويات,خام حديد"
            },
            {
                "code": "CNSZX", "name": "Port of Shenzhen", "name_ar": "ميناء شنزين",
                "country": "China", "country_ar": "الصين", "city": "Shenzhen", "city_ar": "شنزين",
                "region": "South China", "continent": "Asia", "major": True,
                "lat": 22.5431, "lng": 114.0579, "cargo_types": "حاويات,بضائع عامة,إلكترونيات"
            },
            {
                "code": "CNQIN", "name": "Port of Qingdao", "name_ar": "ميناء تشينغداو",
                "country": "China", "country_ar": "الصين", "city": "Qingdao", "city_ar": "تشينغداو",
                "region": "North China", "continent": "Asia", "major": True,
                "lat": 36.0986, "lng": 120.3719, "cargo_types": "حاويات,بضائع عامة,بضائع سائبة,خام حديد"
            },
            {
                "code": "CNGZH", "name": "Port of Guangzhou", "name_ar": "ميناء قوانغتشو",
                "country": "China", "country_ar": "الصين", "city": "Guangzhou", "city_ar": "قوانغتشو",
                "region": "South China", "continent": "Asia", "major": True,
                "lat": 23.1291, "lng": 113.2644, "cargo_types": "حاويات,بضائع عامة,سيارات"
            },
            {
                "code": "CNTXG", "name": "Port of Tianjin", "name_ar": "ميناء تيانجين",
                "country": "China", "country_ar": "الصين", "city": "Tianjin", "city_ar": "تيانجين",
                "region": "North China", "continent": "Asia", "major": True,
                "lat": 39.0842, "lng": 117.2009, "cargo_types": "حاويات,بضائع عامة,بضائع سائبة"
            },
            {
                "code": "CNDLC", "name": "Port of Dalian", "name_ar": "ميناء داليان",
                "country": "China", "country_ar": "الصين", "city": "Dalian", "city_ar": "داليان",
                "region": "Northeast China", "continent": "Asia", "major": True,
                "lat": 38.9140, "lng": 121.6147, "cargo_types": "حاويات,بضائع عامة,نفط,خام حديد"
            },
            {
                "code": "CNXMN", "name": "Port of Xiamen", "name_ar": "ميناء شيامن",
                "country": "China", "country_ar": "الصين", "city": "Xiamen", "city_ar": "شيامن",
                "region": "Southeast China", "continent": "Asia", "major": True,
                "lat": 24.4798, "lng": 118.0819, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "CNLYG", "name": "Port of Lianyungang", "name_ar": "ميناء ليانيونغانغ",
                "country": "China", "country_ar": "الصين", "city": "Lianyungang", "city_ar": "ليانيونغانغ",
                "region": "East China", "continent": "Asia", "major": True,
                "lat": 34.5964, "lng": 119.1665, "cargo_types": "حاويات,بضائع عامة,بضائع سائبة"
            },
            {
                "code": "CNZHA", "name": "Port of Zhanjiang", "name_ar": "ميناء تشانجيانغ",
                "country": "China", "country_ar": "الصين", "city": "Zhanjiang", "city_ar": "تشانجيانغ",
                "region": "South China", "continent": "Asia", "major": False,
                "lat": 21.1967, "lng": 110.4031, "cargo_types": "بضائع عامة,خام حديد,فحم"
            },
            {
                "code": "CNYTN", "name": "Port of Yantai", "name_ar": "ميناء يانتاي",
                "country": "China", "country_ar": "الصين", "city": "Yantai", "city_ar": "يانتاي",
                "region": "North China", "continent": "Asia", "major": False,
                "lat": 37.5365, "lng": 121.3997, "cargo_types": "حاويات,بضائع عامة,فواكه"
            },
            {
                "code": "CNRIC", "name": "Port of Rizhao", "name_ar": "ميناء ريتشاو",
                "country": "China", "country_ar": "الصين", "city": "Rizhao", "city_ar": "ريتشاو",
                "region": "East China", "continent": "Asia", "major": False,
                "lat": 35.4164, "lng": 119.4565, "cargo_types": "خام حديد,فحم,بضائع سائبة"
            },
            {
                "code": "CNTAO", "name": "Port of Taicang", "name_ar": "ميناء تايتشانغ",
                "country": "China", "country_ar": "الصين", "city": "Taicang", "city_ar": "تايتشانغ",
                "region": "East China", "continent": "Asia", "major": False,
                "lat": 31.6515, "lng": 121.1353, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "CNZJG", "name": "Port of Zhangjiagang", "name_ar": "ميناء تشانغجياغانغ",
                "country": "China", "country_ar": "الصين", "city": "Zhangjiagang", "city_ar": "تشانغجياغانغ",
                "region": "East China", "continent": "Asia", "major": False,
                "lat": 31.8759, "lng": 120.5553, "cargo_types": "بضائع سائبة,خام حديد,فحم"
            },
            {
                "code": "CNNTG", "name": "Port of Nantong", "name_ar": "ميناء نانتونغ",
                "country": "China", "country_ar": "الصين", "city": "Nantong", "city_ar": "نانتونغ",
                "region": "East China", "continent": "Asia", "major": False,
                "lat": 32.0116, "lng": 120.8560, "cargo_types": "حاويات,بضائع عامة,كيماويات"
            },
            {
                "code": "CNWZH", "name": "Port of Wenzhou", "name_ar": "ميناء وينتشو",
                "country": "China", "country_ar": "الصين", "city": "Wenzhou", "city_ar": "وينتشو",
                "region": "East China", "continent": "Asia", "major": False,
                "lat": 27.9944, "lng": 120.6986, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "CNFOC", "name": "Port of Fuzhou", "name_ar": "ميناء فوتشو",
                "country": "China", "country_ar": "الصين", "city": "Fuzhou", "city_ar": "فوتشو",
                "region": "Southeast China", "continent": "Asia", "major": False,
                "lat": 26.0745, "lng": 119.2965, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "CNHAK", "name": "Port of Haikou", "name_ar": "ميناء هايكو",
                "country": "China", "country_ar": "الصين", "city": "Haikou", "city_ar": "هايكو",
                "region": "South China", "continent": "Asia", "major": False,
                "lat": 20.0458, "lng": 110.3417, "cargo_types": "حاويات,بضائع عامة,مطاط"
            },
            {
                "code": "CNBEI", "name": "Port of Beihai", "name_ar": "ميناء بيهاي",
                "country": "China", "country_ar": "الصين", "city": "Beihai", "city_ar": "بيهاي",
                "region": "South China", "continent": "Asia", "major": False,
                "lat": 21.4733, "lng": 109.1201, "cargo_types": "بضائع عامة,أسماك"
            },
            {
                "code": "CNFZS", "name": "Port of Fangchenggang", "name_ar": "ميناء فانغتشنغغانغ",
                "country": "China", "country_ar": "الصين", "city": "Fangchenggang", "city_ar": "فانغتشنغغانغ",
                "region": "South China", "continent": "Asia", "major": False,
                "lat": 21.6847, "lng": 108.3548, "cargo_types": "بضائع سائبة,خام حديد"
            },
            {
                "code": "CNQZH", "name": "Port of Quanzhou", "name_ar": "ميناء تشوانتشو",
                "country": "China", "country_ar": "الصين", "city": "Quanzhou", "city_ar": "تشوانتشو",
                "region": "Southeast China", "continent": "Asia", "major": False,
                "lat": 24.8740, "lng": 118.6757, "cargo_types": "حاويات,بضائع عامة,منسوجات"
            },
            {
                "code": "CNJIN", "name": "Port of Jinzhou", "name_ar": "ميناء جينتشو",
                "country": "China", "country_ar": "الصين", "city": "Jinzhou", "city_ar": "جينتشو",
                "region": "Northeast China", "continent": "Asia", "major": False,
                "lat": 40.7608, "lng": 121.1308, "cargo_types": "بضائع سائبة,نفط,كيماويات"
            },
            {
                "code": "CNDND", "name": "Port of Dandong", "name_ar": "ميناء داندونغ",
                "country": "China", "country_ar": "الصين", "city": "Dandong", "city_ar": "داندونغ",
                "region": "Northeast China", "continent": "Asia", "major": False,
                "lat": 40.1244, "lng": 124.3944, "cargo_types": "بضائع عامة,خام حديد"
            },
            {
                "code": "CNYK", "name": "Port of Yingkou", "name_ar": "ميناء ينغكو",
                "country": "China", "country_ar": "الصين", "city": "Yingkou", "city_ar": "ينغكو",
                "region": "Northeast China", "continent": "Asia", "major": False,
                "lat": 40.6736, "lng": 122.2297, "cargo_types": "بضائع سائبة,حاويات"
            },
            {
                "code": "SGSIN", "name": "Port of Singapore", "name_ar": "ميناء سنغافورة",
                "country": "Singapore", "country_ar": "سنغافورة", "city": "Singapore", "city_ar": "سنغافورة",
                "region": "Southeast Asia", "continent": "Asia", "major": True,
                "lat": 1.2966, "lng": 103.8006, "cargo_types": "حاويات,بضائع عامة,ترانزيت,بتروكيماويات"
            },
            {
                "code": "HKHKG", "name": "Port of Hong Kong", "name_ar": "ميناء هونغ كونغ",
                "country": "Hong Kong", "country_ar": "هونغ كونغ", "city": "Hong Kong", "city_ar": "هونغ كونغ",
                "region": "East Asia", "continent": "Asia", "major": True,
                "lat": 22.3193, "lng": 114.1694, "cargo_types": "حاويات,بضائع عامة,ترانزيت"
            },
            
            # موانئ أوروبا الرئيسية
            {
                "code": "NLRTM", "name": "Port of Rotterdam", "name_ar": "ميناء روتردام",
                "country": "Netherlands", "country_ar": "هولندا", "city": "Rotterdam", "city_ar": "روتردام",
                "region": "Western Europe", "continent": "Europe", "major": True,
                "lat": 51.9225, "lng": 4.4792, "cargo_types": "حاويات,بضائع عامة,بتروكيماويات,بضائع سائبة"
            },
            {
                "code": "BEANR", "name": "Port of Antwerp", "name_ar": "ميناء أنتويرب",
                "country": "Belgium", "country_ar": "بلجيكا", "city": "Antwerp", "city_ar": "أنتويرب",
                "region": "Western Europe", "continent": "Europe", "major": True,
                "lat": 51.2194, "lng": 4.4025, "cargo_types": "حاويات,بضائع عامة,بتروكيماويات"
            },
            {
                "code": "DEHAM", "name": "Port of Hamburg", "name_ar": "ميناء هامبورغ",
                "country": "Germany", "country_ar": "ألمانيا", "city": "Hamburg", "city_ar": "هامبورغ",
                "region": "Western Europe", "continent": "Europe", "major": True,
                "lat": 53.5511, "lng": 9.9937, "cargo_types": "حاويات,بضائع عامة"
            },
            
            # موانئ أمريكا الشمالية الشاملة
            {
                "code": "USLAX", "name": "Port of Los Angeles", "name_ar": "ميناء لوس أنجلوس",
                "country": "United States", "country_ar": "الولايات المتحدة", "city": "Los Angeles", "city_ar": "لوس أنجلوس",
                "region": "West Coast", "continent": "North America", "major": True,
                "lat": 33.7361, "lng": -118.2922, "cargo_types": "حاويات,بضائع عامة,سيارات"
            },
            {
                "code": "USLGB", "name": "Port of Long Beach", "name_ar": "ميناء لونغ بيتش",
                "country": "United States", "country_ar": "الولايات المتحدة", "city": "Long Beach", "city_ar": "لونغ بيتش",
                "region": "West Coast", "continent": "North America", "major": True,
                "lat": 33.7701, "lng": -118.1937, "cargo_types": "حاويات,بضائع عامة,نفط"
            },
            {
                "code": "USNYC", "name": "Port of New York & New Jersey", "name_ar": "ميناء نيويورك ونيو جيرسي",
                "country": "United States", "country_ar": "الولايات المتحدة", "city": "New York", "city_ar": "نيويورك",
                "region": "East Coast", "continent": "North America", "major": True,
                "lat": 40.6892, "lng": -74.0445, "cargo_types": "حاويات,بضائع عامة,سيارات"
            },
            {
                "code": "USSAV", "name": "Port of Savannah", "name_ar": "ميناء سافانا",
                "country": "United States", "country_ar": "الولايات المتحدة", "city": "Savannah", "city_ar": "سافانا",
                "region": "East Coast", "continent": "North America", "major": True,
                "lat": 32.1313, "lng": -81.1437, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "USOAK", "name": "Port of Oakland", "name_ar": "ميناء أوكلاند",
                "country": "United States", "country_ar": "الولايات المتحدة", "city": "Oakland", "city_ar": "أوكلاند",
                "region": "West Coast", "continent": "North America", "major": True,
                "lat": 37.8044, "lng": -122.2711, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "USSEA", "name": "Port of Seattle", "name_ar": "ميناء سياتل",
                "country": "United States", "country_ar": "الولايات المتحدة", "city": "Seattle", "city_ar": "سياتل",
                "region": "West Coast", "continent": "North America", "major": True,
                "lat": 47.6062, "lng": -122.3321, "cargo_types": "حاويات,بضائع عامة,أسماك"
            },
            {
                "code": "USTAC", "name": "Port of Tacoma", "name_ar": "ميناء تاكوما",
                "country": "United States", "country_ar": "الولايات المتحدة", "city": "Tacoma", "city_ar": "تاكوما",
                "region": "West Coast", "continent": "North America", "major": True,
                "lat": 47.2529, "lng": -122.4443, "cargo_types": "حاويات,بضائع عامة,سيارات"
            },
            {
                "code": "USHOU", "name": "Port of Houston", "name_ar": "ميناء هيوستن",
                "country": "United States", "country_ar": "الولايات المتحدة", "city": "Houston", "city_ar": "هيوستن",
                "region": "Gulf Coast", "continent": "North America", "major": True,
                "lat": 29.7604, "lng": -95.3698, "cargo_types": "حاويات,بضائع عامة,نفط,كيماويات"
            },
            {
                "code": "USMIA", "name": "Port of Miami", "name_ar": "ميناء ميامي",
                "country": "United States", "country_ar": "الولايات المتحدة", "city": "Miami", "city_ar": "ميامي",
                "region": "East Coast", "continent": "North America", "major": True,
                "lat": 25.7617, "lng": -80.1918, "cargo_types": "حاويات,بضائع عامة,سياحة"
            },
            {
                "code": "USBAL", "name": "Port of Baltimore", "name_ar": "ميناء بالتيمور",
                "country": "United States", "country_ar": "الولايات المتحدة", "city": "Baltimore", "city_ar": "بالتيمور",
                "region": "East Coast", "continent": "North America", "major": True,
                "lat": 39.2904, "lng": -76.6122, "cargo_types": "حاويات,بضائع عامة,سيارات"
            },
            {
                "code": "CAVAN", "name": "Port of Vancouver", "name_ar": "ميناء فانكوفر",
                "country": "Canada", "country_ar": "كندا", "city": "Vancouver", "city_ar": "فانكوفر",
                "region": "West Coast", "continent": "North America", "major": True,
                "lat": 49.2827, "lng": -123.1207, "cargo_types": "حاويات,بضائع عامة,بضائع سائبة,خشب"
            },
            {
                "code": "CATOR", "name": "Port of Toronto", "name_ar": "ميناء تورونتو",
                "country": "Canada", "country_ar": "كندا", "city": "Toronto", "city_ar": "تورونتو",
                "region": "Great Lakes", "continent": "North America", "major": False,
                "lat": 43.6532, "lng": -79.3832, "cargo_types": "بضائع عامة,حبوب"
            },
            {
                "code": "CAHAL", "name": "Port of Halifax", "name_ar": "ميناء هاليفاكس",
                "country": "Canada", "country_ar": "كندا", "city": "Halifax", "city_ar": "هاليفاكس",
                "region": "Atlantic", "continent": "North America", "major": True,
                "lat": 44.6488, "lng": -63.5752, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "CAPRR", "name": "Port of Prince Rupert", "name_ar": "ميناء برينس روبرت",
                "country": "Canada", "country_ar": "كندا", "city": "Prince Rupert", "city_ar": "برينس روبرت",
                "region": "West Coast", "continent": "North America", "major": True,
                "lat": 54.3150, "lng": -130.3201, "cargo_types": "حاويات,بضائع سائبة,فحم"
            },

            # موانئ إثيوبيا
            {
                "code": "ETADD", "name": "Port of Addis Ababa", "name_ar": "ميناء أديس أبابا",
                "country": "Ethiopia", "country_ar": "إثيوبيا", "city": "Addis Ababa", "city_ar": "أديس أبابا",
                "region": "East Africa", "continent": "Africa", "major": False,
                "lat": 9.1450, "lng": 40.4897, "cargo_types": "بضائع عامة,قهوة"
            },

            # موانئ كينيا
            {
                "code": "KEMBA", "name": "Port of Mombasa", "name_ar": "ميناء مومباسا",
                "country": "Kenya", "country_ar": "كينيا", "city": "Mombasa", "city_ar": "مومباسا",
                "region": "East Africa", "continent": "Africa", "major": True,
                "lat": -4.0435, "lng": 39.6682, "cargo_types": "حاويات,بضائع عامة,شاي,قهوة"
            },

            # موانئ تنزانيا
            {
                "code": "TZDAR", "name": "Port of Dar es Salaam", "name_ar": "ميناء دار السلام",
                "country": "Tanzania", "country_ar": "تنزانيا", "city": "Dar es Salaam", "city_ar": "دار السلام",
                "region": "East Africa", "continent": "Africa", "major": True,
                "lat": -6.7924, "lng": 39.2083, "cargo_types": "حاويات,بضائع عامة"
            },

            # موانئ موزمبيق
            {
                "code": "MZMPM", "name": "Port of Maputo", "name_ar": "ميناء مابوتو",
                "country": "Mozambique", "country_ar": "موزمبيق", "city": "Maputo", "city_ar": "مابوتو",
                "region": "Southern Africa", "continent": "Africa", "major": True,
                "lat": -25.9692, "lng": 32.5732, "cargo_types": "حاويات,بضائع عامة,فحم"
            },

            # موانئ أنغولا
            {
                "code": "AOLAD", "name": "Port of Luanda", "name_ar": "ميناء لواندا",
                "country": "Angola", "country_ar": "أنغولا", "city": "Luanda", "city_ar": "لواندا",
                "region": "West Africa", "continent": "Africa", "major": True,
                "lat": -8.8390, "lng": 13.2894, "cargo_types": "نفط,بضائع عامة"
            },

            # موانئ غانا
            {
                "code": "GHACC", "name": "Port of Accra", "name_ar": "ميناء أكرا",
                "country": "Ghana", "country_ar": "غانا", "city": "Accra", "city_ar": "أكرا",
                "region": "West Africa", "continent": "Africa", "major": True,
                "lat": 5.6037, "lng": -0.1870, "cargo_types": "حاويات,بضائع عامة,كاكاو"
            },

            # موانئ ساحل العاج
            {
                "code": "CIABJ", "name": "Port of Abidjan", "name_ar": "ميناء أبيدجان",
                "country": "Ivory Coast", "country_ar": "ساحل العاج", "city": "Abidjan", "city_ar": "أبيدجان",
                "region": "West Africa", "continent": "Africa", "major": True,
                "lat": 5.2893, "lng": -4.0083, "cargo_types": "حاويات,بضائع عامة,كاكاو,قهوة"
            },

            # موانئ السنغال
            {
                "code": "SNDKR", "name": "Port of Dakar", "name_ar": "ميناء داكار",
                "country": "Senegal", "country_ar": "السنغال", "city": "Dakar", "city_ar": "داكار",
                "region": "West Africa", "continent": "Africa", "major": True,
                "lat": 14.6928, "lng": -17.4467, "cargo_types": "حاويات,بضائع عامة,أسماك"
            },

            # موانئ أفريقية إضافية
            {
                "code": "ZADUR", "name": "Port of Durban", "name_ar": "ميناء ديربان",
                "country": "South Africa", "country_ar": "جنوب أفريقيا", "city": "Durban", "city_ar": "ديربان",
                "region": "Southern Africa", "continent": "Africa", "major": True,
                "lat": -29.8587, "lng": 31.0218, "cargo_types": "حاويات,بضائع عامة,فحم"
            },
            {
                "code": "ZACPT", "name": "Port of Cape Town", "name_ar": "ميناء كيب تاون",
                "country": "South Africa", "country_ar": "جنوب أفريقيا", "city": "Cape Town", "city_ar": "كيب تاون",
                "region": "Southern Africa", "continent": "Africa", "major": True,
                "lat": -33.9249, "lng": 18.4241, "cargo_types": "حاويات,بضائع عامة,فواكه"
            },
            {
                "code": "NGLAG", "name": "Port of Lagos", "name_ar": "ميناء لاغوس",
                "country": "Nigeria", "country_ar": "نيجيريا", "city": "Lagos", "city_ar": "لاغوس",
                "region": "West Africa", "continent": "Africa", "major": True,
                "lat": 6.4474, "lng": 3.3903, "cargo_types": "حاويات,نفط,بضائع عامة"
            },

            # موانئ الهند الشاملة
            {
                "code": "INMUN", "name": "Jawaharlal Nehru Port", "name_ar": "ميناء جواهر لال نهرو",
                "country": "India", "country_ar": "الهند", "city": "Mumbai", "city_ar": "مومباي",
                "region": "West India", "continent": "Asia", "major": True,
                "lat": 18.9388, "lng": 72.9500, "cargo_types": "حاويات,بضائع عامة,بتروكيماويات"
            },
            {
                "code": "INCCU", "name": "Port of Chennai", "name_ar": "ميناء تشيناي",
                "country": "India", "country_ar": "الهند", "city": "Chennai", "city_ar": "تشيناي",
                "region": "South India", "continent": "Asia", "major": True,
                "lat": 13.0827, "lng": 80.2707, "cargo_types": "حاويات,بضائع عامة,سيارات"
            },
            {
                "code": "INKOC", "name": "Port of Kochi", "name_ar": "ميناء كوتشي",
                "country": "India", "country_ar": "الهند", "city": "Kochi", "city_ar": "كوتشي",
                "region": "South India", "continent": "Asia", "major": True,
                "lat": 9.9312, "lng": 76.2673, "cargo_types": "حاويات,بضائع عامة,توابل"
            },
            {
                "code": "INKAL", "name": "Port of Kolkata", "name_ar": "ميناء كولكاتا",
                "country": "India", "country_ar": "الهند", "city": "Kolkata", "city_ar": "كولكاتا",
                "region": "East India", "continent": "Asia", "major": True,
                "lat": 22.5726, "lng": 88.3639, "cargo_types": "حاويات,بضائع عامة,جوت"
            },
            {
                "code": "INVTZ", "name": "Port of Visakhapatnam", "name_ar": "ميناء فيساخاباتنام",
                "country": "India", "country_ar": "الهند", "city": "Visakhapatnam", "city_ar": "فيساخاباتنام",
                "region": "East India", "continent": "Asia", "major": True,
                "lat": 17.6868, "lng": 83.2185, "cargo_types": "حاويات,بضائع سائبة,خام حديد"
            },
            {
                "code": "INKAN", "name": "Port of Kandla", "name_ar": "ميناء كاندلا",
                "country": "India", "country_ar": "الهند", "city": "Kandla", "city_ar": "كاندلا",
                "region": "West India", "continent": "Asia", "major": True,
                "lat": 23.0225, "lng": 70.2208, "cargo_types": "حاويات,بضائع سائبة,نفط"
            },
            {
                "code": "INMRM", "name": "Port of Mormugao", "name_ar": "ميناء مورموغاو",
                "country": "India", "country_ar": "الهند", "city": "Goa", "city_ar": "غوا",
                "region": "West India", "continent": "Asia", "major": False,
                "lat": 15.4909, "lng": 73.8278, "cargo_types": "خام حديد,بضائع عامة"
            },
            {
                "code": "INMNG", "name": "Port of Mangalore", "name_ar": "ميناء مانغالور",
                "country": "India", "country_ar": "الهند", "city": "Mangalore", "city_ar": "مانغالور",
                "region": "South India", "continent": "Asia", "major": False,
                "lat": 12.9141, "lng": 74.8560, "cargo_types": "بضائع سائبة,نفط,كيماويات"
            },
            {
                "code": "INTUT", "name": "Port of Tuticorin", "name_ar": "ميناء توتيكورين",
                "country": "India", "country_ar": "الهند", "city": "Tuticorin", "city_ar": "توتيكورين",
                "region": "South India", "continent": "Asia", "major": False,
                "lat": 8.8932, "lng": 78.1348, "cargo_types": "حاويات,ملح,فحم"
            },
            {
                "code": "INPAR", "name": "Port of Paradip", "name_ar": "ميناء باراديب",
                "country": "India", "country_ar": "الهند", "city": "Paradip", "city_ar": "باراديب",
                "region": "East India", "continent": "Asia", "major": False,
                "lat": 20.3102, "lng": 86.6169, "cargo_types": "خام حديد,فحم,بضائع سائبة"
            },

            # موانئ باكستان
            {
                "code": "PKKAR", "name": "Port of Karachi", "name_ar": "ميناء كراتشي",
                "country": "Pakistan", "country_ar": "باكستان", "city": "Karachi", "city_ar": "كراتشي",
                "region": "South Asia", "continent": "Asia", "major": True,
                "lat": 24.8607, "lng": 67.0011, "cargo_types": "حاويات,بضائع عامة,نفط,قطن"
            },
            {
                "code": "PKGWA", "name": "Port of Gwadar", "name_ar": "ميناء جوادر",
                "country": "Pakistan", "country_ar": "باكستان", "city": "Gwadar", "city_ar": "جوادر",
                "region": "South Asia", "continent": "Asia", "major": True,
                "lat": 25.1216, "lng": 62.3254, "cargo_types": "حاويات,بضائع عامة,ترانزيت"
            },
            {
                "code": "PKQAS", "name": "Port Qasim", "name_ar": "ميناء قاسم",
                "country": "Pakistan", "country_ar": "باكستان", "city": "Karachi", "city_ar": "كراتشي",
                "region": "South Asia", "continent": "Asia", "major": True,
                "lat": 24.7136, "lng": 67.4551, "cargo_types": "حاويات,بضائع عامة,حديد,سيارات"
            },

            # موانئ بنغلاديش
            {
                "code": "BDCGP", "name": "Port of Chittagong", "name_ar": "ميناء شيتاغونغ",
                "country": "Bangladesh", "country_ar": "بنغلاديش", "city": "Chittagong", "city_ar": "شيتاغونغ",
                "region": "South Asia", "continent": "Asia", "major": True,
                "lat": 22.3569, "lng": 91.7832, "cargo_types": "حاويات,بضائع عامة,منسوجات,جوت"
            },
            {
                "code": "BDDAC", "name": "Port of Dhaka", "name_ar": "ميناء دكا",
                "country": "Bangladesh", "country_ar": "بنغلاديش", "city": "Dhaka", "city_ar": "دكا",
                "region": "South Asia", "continent": "Asia", "major": False,
                "lat": 23.8103, "lng": 90.4125, "cargo_types": "بضائع عامة,منسوجات"
            },

            # موانئ سريلانكا
            {
                "code": "LKCMB", "name": "Port of Colombo", "name_ar": "ميناء كولومبو",
                "country": "Sri Lanka", "country_ar": "سريلانكا", "city": "Colombo", "city_ar": "كولومبو",
                "region": "South Asia", "continent": "Asia", "major": True,
                "lat": 6.9271, "lng": 79.8612, "cargo_types": "حاويات,بضائع عامة,ترانزيت,شاي"
            },
            {
                "code": "LKHRI", "name": "Port of Hambantota", "name_ar": "ميناء هامبانتوتا",
                "country": "Sri Lanka", "country_ar": "سريلانكا", "city": "Hambantota", "city_ar": "هامبانتوتا",
                "region": "South Asia", "continent": "Asia", "major": True,
                "lat": 6.1240, "lng": 81.1185, "cargo_types": "حاويات,بضائع عامة,ترانزيت"
            },

            # موانئ ميانمار
            {
                "code": "MMRGN", "name": "Port of Yangon", "name_ar": "ميناء يانغون",
                "country": "Myanmar", "country_ar": "ميانمار", "city": "Yangon", "city_ar": "يانغون",
                "region": "Southeast Asia", "continent": "Asia", "major": True,
                "lat": 16.7967, "lng": 96.1610, "cargo_types": "حاويات,بضائع عامة,أرز"
            },

            # موانئ فيتنام
            {
                "code": "VNSGN", "name": "Port of Ho Chi Minh City", "name_ar": "ميناء هو تشي مين",
                "country": "Vietnam", "country_ar": "فيتنام", "city": "Ho Chi Minh City", "city_ar": "هو تشي مين",
                "region": "Southeast Asia", "continent": "Asia", "major": True,
                "lat": 10.8231, "lng": 106.6297, "cargo_types": "حاويات,بضائع عامة,أرز"
            },
            {
                "code": "VNHPH", "name": "Port of Haiphong", "name_ar": "ميناء هايفونغ",
                "country": "Vietnam", "country_ar": "فيتنام", "city": "Haiphong", "city_ar": "هايفونغ",
                "region": "Southeast Asia", "continent": "Asia", "major": True,
                "lat": 20.8449, "lng": 106.6881, "cargo_types": "حاويات,بضائع عامة,فحم"
            },

            # موانئ كمبوديا
            {
                "code": "KHPNH", "name": "Port of Phnom Penh", "name_ar": "ميناء بنوم بنه",
                "country": "Cambodia", "country_ar": "كمبوديا", "city": "Phnom Penh", "city_ar": "بنوم بنه",
                "region": "Southeast Asia", "continent": "Asia", "major": False,
                "lat": 11.5564, "lng": 104.9282, "cargo_types": "بضائع عامة,أرز"
            },
            {
                "code": "JPYOK", "name": "Port of Yokohama", "name_ar": "ميناء يوكوهاما",
                "country": "Japan", "country_ar": "اليابان", "city": "Yokohama", "city_ar": "يوكوهاما",
                "region": "East Asia", "continent": "Asia", "major": True,
                "lat": 35.4437, "lng": 139.6380, "cargo_types": "حاويات,بضائع عامة,سيارات"
            },
            {
                "code": "JPTYO", "name": "Port of Tokyo", "name_ar": "ميناء طوكيو",
                "country": "Japan", "country_ar": "اليابان", "city": "Tokyo", "city_ar": "طوكيو",
                "region": "East Asia", "continent": "Asia", "major": True,
                "lat": 35.6762, "lng": 139.6503, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "KRPUS", "name": "Port of Busan", "name_ar": "ميناء بوسان",
                "country": "South Korea", "country_ar": "كوريا الجنوبية", "city": "Busan", "city_ar": "بوسان",
                "region": "East Asia", "continent": "Asia", "major": True,
                "lat": 35.1796, "lng": 129.0756, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "THBKK", "name": "Port of Bangkok", "name_ar": "ميناء بانكوك",
                "country": "Thailand", "country_ar": "تايلاند", "city": "Bangkok", "city_ar": "بانكوك",
                "region": "Southeast Asia", "continent": "Asia", "major": True,
                "lat": 13.7563, "lng": 100.5018, "cargo_types": "حاويات,بضائع عامة,أرز"
            },
            {
                "code": "MYPEN", "name": "Port of Penang", "name_ar": "ميناء بينانغ",
                "country": "Malaysia", "country_ar": "ماليزيا", "city": "Penang", "city_ar": "بينانغ",
                "region": "Southeast Asia", "continent": "Asia", "major": True,
                "lat": 5.4164, "lng": 100.3327, "cargo_types": "حاويات,بضائع عامة,إلكترونيات"
            },
            {
                "code": "IDTPP", "name": "Port of Tanjung Priok", "name_ar": "ميناء تانجونغ بريوك",
                "country": "Indonesia", "country_ar": "إندونيسيا", "city": "Jakarta", "city_ar": "جاكرتا",
                "region": "Southeast Asia", "continent": "Asia", "major": True,
                "lat": -6.1045, "lng": 106.8827, "cargo_types": "حاويات,بضائع عامة,نفط"
            },
            {
                "code": "PHMNL", "name": "Port of Manila", "name_ar": "ميناء مانيلا",
                "country": "Philippines", "country_ar": "الفلبين", "city": "Manila", "city_ar": "مانيلا",
                "region": "Southeast Asia", "continent": "Asia", "major": True,
                "lat": 14.5995, "lng": 120.9842, "cargo_types": "حاويات,بضائع عامة"
            },

            # موانئ أوروبية إضافية
            {
                "code": "GBFXT", "name": "Port of Felixstowe", "name_ar": "ميناء فيليكستو",
                "country": "United Kingdom", "country_ar": "المملكة المتحدة", "city": "Felixstowe", "city_ar": "فيليكستو",
                "region": "Western Europe", "continent": "Europe", "major": True,
                "lat": 51.9642, "lng": 1.3518, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "FRLEH", "name": "Port of Le Havre", "name_ar": "ميناء لوهافر",
                "country": "France", "country_ar": "فرنسا", "city": "Le Havre", "city_ar": "لوهافر",
                "region": "Western Europe", "continent": "Europe", "major": True,
                "lat": 49.4944, "lng": 0.1079, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "ITGOA", "name": "Port of Genoa", "name_ar": "ميناء جنوة",
                "country": "Italy", "country_ar": "إيطاليا", "city": "Genoa", "city_ar": "جنوة",
                "region": "Southern Europe", "continent": "Europe", "major": True,
                "lat": 44.4056, "lng": 8.9463, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "ESALG", "name": "Port of Algeciras", "name_ar": "ميناء الجزيرة الخضراء",
                "country": "Spain", "country_ar": "إسبانيا", "city": "Algeciras", "city_ar": "الجزيرة الخضراء",
                "region": "Southern Europe", "continent": "Europe", "major": True,
                "lat": 36.1408, "lng": -5.4526, "cargo_types": "حاويات,بضائع عامة,ترانزيت"
            },
            {
                "code": "GRGPA", "name": "Port of Piraeus", "name_ar": "ميناء بيرايوس",
                "country": "Greece", "country_ar": "اليونان", "city": "Piraeus", "city_ar": "بيرايوس",
                "region": "Southern Europe", "continent": "Europe", "major": True,
                "lat": 37.9364, "lng": 23.6503, "cargo_types": "حاويات,بضائع عامة"
            },

            # موانئ الدول الاسكندنافية
            {
                "code": "SEGOT", "name": "Port of Gothenburg", "name_ar": "ميناء غوتنبرغ",
                "country": "Sweden", "country_ar": "السويد", "city": "Gothenburg", "city_ar": "غوتنبرغ",
                "region": "Scandinavia", "continent": "Europe", "major": True,
                "lat": 57.7089, "lng": 11.9746, "cargo_types": "حاويات,بضائع عامة,سيارات"
            },
            {
                "code": "SESTO", "name": "Port of Stockholm", "name_ar": "ميناء ستوكهولم",
                "country": "Sweden", "country_ar": "السويد", "city": "Stockholm", "city_ar": "ستوكهولم",
                "region": "Scandinavia", "continent": "Europe", "major": False,
                "lat": 59.3293, "lng": 18.0686, "cargo_types": "بضائع عامة,سيارات"
            },
            {
                "code": "NOOSL", "name": "Port of Oslo", "name_ar": "ميناء أوسلو",
                "country": "Norway", "country_ar": "النرويج", "city": "Oslo", "city_ar": "أوسلو",
                "region": "Scandinavia", "continent": "Europe", "major": False,
                "lat": 59.9139, "lng": 10.7522, "cargo_types": "بضائع عامة,نفط"
            },
            {
                "code": "NOBRG", "name": "Port of Bergen", "name_ar": "ميناء بيرغن",
                "country": "Norway", "country_ar": "النرويج", "city": "Bergen", "city_ar": "بيرغن",
                "region": "Scandinavia", "continent": "Europe", "major": False,
                "lat": 60.3913, "lng": 5.3221, "cargo_types": "بضائع عامة,أسماك,نفط"
            },
            {
                "code": "DKCPH", "name": "Port of Copenhagen", "name_ar": "ميناء كوبنهاغن",
                "country": "Denmark", "country_ar": "الدنمارك", "city": "Copenhagen", "city_ar": "كوبنهاغن",
                "region": "Scandinavia", "continent": "Europe", "major": True,
                "lat": 55.6761, "lng": 12.5683, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "DKAAR", "name": "Port of Aarhus", "name_ar": "ميناء آرهوس",
                "country": "Denmark", "country_ar": "الدنمارك", "city": "Aarhus", "city_ar": "آرهوس",
                "region": "Scandinavia", "continent": "Europe", "major": False,
                "lat": 56.1629, "lng": 10.2039, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "FIHEL", "name": "Port of Helsinki", "name_ar": "ميناء هلسنكي",
                "country": "Finland", "country_ar": "فنلندا", "city": "Helsinki", "city_ar": "هلسنكي",
                "region": "Scandinavia", "continent": "Europe", "major": True,
                "lat": 60.1699, "lng": 24.9384, "cargo_types": "حاويات,بضائع عامة,خشب"
            },

            # موانئ البلطيق
            {
                "code": "PLGDN", "name": "Port of Gdansk", "name_ar": "ميناء غدانسك",
                "country": "Poland", "country_ar": "بولندا", "city": "Gdansk", "city_ar": "غدانسك",
                "region": "Baltic", "continent": "Europe", "major": True,
                "lat": 54.3520, "lng": 18.6466, "cargo_types": "حاويات,بضائع عامة,فحم"
            },
            {
                "code": "PLGDY", "name": "Port of Gdynia", "name_ar": "ميناء غدينيا",
                "country": "Poland", "country_ar": "بولندا", "city": "Gdynia", "city_ar": "غدينيا",
                "region": "Baltic", "continent": "Europe", "major": False,
                "lat": 54.5189, "lng": 18.5305, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "LTRGA", "name": "Port of Riga", "name_ar": "ميناء ريغا",
                "country": "Latvia", "country_ar": "لاتفيا", "city": "Riga", "city_ar": "ريغا",
                "region": "Baltic", "continent": "Europe", "major": True,
                "lat": 56.9496, "lng": 24.1052, "cargo_types": "حاويات,بضائع عامة,خشب"
            },
            {
                "code": "EETAL", "name": "Port of Tallinn", "name_ar": "ميناء تالين",
                "country": "Estonia", "country_ar": "إستونيا", "city": "Tallinn", "city_ar": "تالين",
                "region": "Baltic", "continent": "Europe", "major": False,
                "lat": 59.4370, "lng": 24.7536, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "LTKLA", "name": "Port of Klaipeda", "name_ar": "ميناء كلايبيدا",
                "country": "Lithuania", "country_ar": "ليتوانيا", "city": "Klaipeda", "city_ar": "كلايبيدا",
                "region": "Baltic", "continent": "Europe", "major": False,
                "lat": 55.7033, "lng": 21.1443, "cargo_types": "حاويات,بضائع عامة,نفط"
            },

            # موانئ أمريكا الجنوبية
            {
                "code": "BRSSZ", "name": "Port of Santos", "name_ar": "ميناء سانتوس",
                "country": "Brazil", "country_ar": "البرازيل", "city": "Santos", "city_ar": "سانتوس",
                "region": "Southeast Brazil", "continent": "South America", "major": True,
                "lat": -23.9618, "lng": -46.3322, "cargo_types": "حاويات,بضائع عامة,بضائع سائبة"
            },
            {
                "code": "CLVAP", "name": "Port of Valparaiso", "name_ar": "ميناء فالبارايسو",
                "country": "Chile", "country_ar": "تشيلي", "city": "Valparaiso", "city_ar": "فالبارايسو",
                "region": "Central Chile", "continent": "South America", "major": True,
                "lat": -33.0472, "lng": -71.6127, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "ARBUE", "name": "Port of Buenos Aires", "name_ar": "ميناء بوينس آيرس",
                "country": "Argentina", "country_ar": "الأرجنتين", "city": "Buenos Aires", "city_ar": "بوينس آيرس",
                "region": "Rio de la Plata", "continent": "South America", "major": True,
                "lat": -34.6118, "lng": -58.3960, "cargo_types": "حاويات,بضائع عامة,لحوم"
            },

            # موانئ أوقيانوسيا
            {
                "code": "AUSYD", "name": "Port of Sydney", "name_ar": "ميناء سيدني",
                "country": "Australia", "country_ar": "أستراليا", "city": "Sydney", "city_ar": "سيدني",
                "region": "New South Wales", "continent": "Oceania", "major": True,
                "lat": -33.8688, "lng": 151.2093, "cargo_types": "حاويات,بضائع عامة"
            },
            {
                "code": "AUMEL", "name": "Port of Melbourne", "name_ar": "ميناء ملبورن",
                "country": "Australia", "country_ar": "أستراليا", "city": "Melbourne", "city_ar": "ملبورن",
                "region": "Victoria", "continent": "Oceania", "major": True,
                "lat": -37.8136, "lng": 144.9631, "cargo_types": "حاويات,بضائع عامة"
            }
        ]
    
    def search_ports(self, query: str = "", continent: str = "", region: str = "", 
                    major_only: bool = False, limit: int = 50) -> List[Dict]:
        """البحث الذكي في الموانئ"""
        try:
            results = []
            query_lower = query.lower() if query else ""
            
            for port in self.world_ports_data:
                # تطبيق الفلاتر
                if continent and port.get("continent", "") != continent:
                    continue
                
                if region and port.get("region", "") != region:
                    continue
                
                if major_only and not port.get("major", False):
                    continue
                
                # البحث النصي
                if query_lower:
                    searchable_text = " ".join([
                        port.get("code", ""),
                        port.get("name", ""),
                        port.get("name_ar", ""),
                        port.get("country", ""),
                        port.get("country_ar", ""),
                        port.get("city", ""),
                        port.get("city_ar", ""),
                        port.get("cargo_types", "")
                    ]).lower()
                    
                    if query_lower not in searchable_text:
                        continue
                
                # إضافة معرف فريد
                port_result = port.copy()
                port_result["id"] = hash(port["code"]) % 1000000
                results.append(port_result)
                
                if len(results) >= limit:
                    break
            
            return results
            
        except Exception as e:
            current_app.logger.error(f"Error searching ports: {e}")
            return []
    
    def get_port_by_code(self, port_code: str) -> Optional[Dict]:
        """جلب ميناء بالكود"""
        for port in self.world_ports_data:
            if port.get("code", "").upper() == port_code.upper():
                port_result = port.copy()
                port_result["id"] = hash(port["code"]) % 1000000
                return port_result
        return None
    
    def add_to_favorites(self, port_data: Dict, user_id: str = "system") -> bool:
        """إضافة ميناء إلى المفضلة"""
        try:
            # التحقق من وجود الميناء في الجدول الموحد وتحديث عداد الاستخدام
            existing = self.db_manager.execute_query(
                "SELECT id FROM unified_ports WHERE port_code = :port_code",
                {"port_code": port_data.get("code")}
            )

            if existing:
                # تحديث عداد الاستخدام في الجدول الموحد
                self.db_manager.execute_update(
                    "UPDATE unified_ports SET usage_count = usage_count + 1, last_updated = CURRENT_TIMESTAMP WHERE port_code = :port_code",
                    {"port_code": port_data.get("code")}
                )
            else:
                current_app.logger.warning(f"Port {port_data.get('code')} not found in unified_ports table")
            
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error adding port to favorites: {e}")
            return False
    
    def get_favorite_ports(self, limit: int = 20) -> List[Dict]:
        """جلب الموانئ المفضلة من الجدول الموحد"""
        try:
            ports_data = self.db_manager.execute_query("""
                SELECT port_code, port_name, country, city, usage_count,
                       port_name_arabic, country_arabic, city_arabic, major_port, popularity_score
                FROM unified_ports
                WHERE usage_count > 0
                ORDER BY usage_count DESC, last_updated DESC
                FETCH FIRST :limit ROWS ONLY
            """, {"limit": limit})
            
            results = []
            for port in ports_data or []:
                # إنشاء بيانات الميناء من الجدول الموحد
                port_dict = {
                    "id": hash(port[0]) % 1000000,
                    "code": port[0],
                    "name": port[1],
                    "name_ar": port[5] if port[5] else "",
                    "country": port[2],
                    "country_ar": port[6] if port[6] else "",
                    "city": port[3],
                    "city_ar": port[7] if port[7] else "",
                    "usage_count": port[4],
                    "major": bool(port[8]) if port[8] else False,
                    "popularity_score": port[9] if port[9] else 0
                }
                results.append(port_dict)
            
            return results
            
        except Exception as e:
            current_app.logger.error(f"Error getting favorite ports: {e}")
            return []
    
    def get_ai_recommendations(self, origin_port: str = None, destination_port: str = None, 
                             cargo_type: str = None) -> List[Dict]:
        """توصيات الذكاء الاصطناعي للموانئ"""
        try:
            recommendations = []
            
            # توصيات بناءً على نوع البضاعة
            if cargo_type:
                for port in self.world_ports_data:
                    cargo_types = port.get("cargo_types", "").lower()
                    if cargo_type.lower() in cargo_types and port.get("major", False):
                        port_result = port.copy()
                        port_result["id"] = hash(port["code"]) % 1000000
                        port_result["recommendation_reason"] = f"متخصص في {cargo_type}"
                        recommendations.append(port_result)
            
            # توصيات الموانئ الرئيسية إذا لم توجد توصيات محددة
            if not recommendations:
                major_ports = [p for p in self.world_ports_data if p.get("major", False)][:10]
                for port in major_ports:
                    port_result = port.copy()
                    port_result["id"] = hash(port["code"]) % 1000000
                    port_result["recommendation_reason"] = "ميناء رئيسي عالمي"
                    recommendations.append(port_result)
            
            return recommendations[:10]
            
        except Exception as e:
            current_app.logger.error(f"Error getting AI recommendations: {e}")
            return []
    
    def close(self):
        """إغلاق الاتصال"""
        if self.db_manager:
            self.db_manager.close()
