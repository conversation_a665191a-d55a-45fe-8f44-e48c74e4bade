-- نظام حالات الشحنة المتكامل
-- إن<PERSON>اء جدول حالات الشحنة

-- 1. إضافة عمود حالة الشحنة لجدول الشحنات الموجود
ALTER TABLE cargo_shipments ADD (
    shipment_status VARCHAR2(50) DEFAULT 'مسودة' NOT NULL,
    status_updated_at DATE DEFAULT SYSDATE,
    status_updated_by NUMBER,
    estimated_delivery_date DATE,
    actual_delivery_date DATE,
    tracking_notes CLOB
);

-- 2. إنشاء جدول تتبع تاريخ الحالات
CREATE TABLE shipment_status_history (
    id NUMBER PRIMARY KEY,
    shipment_id NUMBER NOT NULL,
    old_status VARCHAR2(50),
    new_status VARCHAR2(50) NOT NULL,
    status_date DATE DEFAULT SYSDATE,
    updated_by NUMBER,
    notes VARCHAR2(1000),
    location VARCHAR2(200),
    created_at DATE DEFAULT SYSDATE,
    CONSTRAINT fk_status_shipment FOREIGN KEY (shipment_id) REFERENCES cargo_shipments(id) ON DELETE CASCADE
);

-- 3. إنشاء sequence للتاريخ
CREATE SEQUENCE shipment_status_history_seq START WITH 1 INCREMENT BY 1;

-- 4. إنشاء جدول إعدادات الحالات
CREATE TABLE shipment_status_config (
    status_code VARCHAR2(50) PRIMARY KEY,
    status_name_ar VARCHAR2(100) NOT NULL,
    status_name_en VARCHAR2(100),
    status_color VARCHAR2(20) DEFAULT '#6c757d',
    status_icon VARCHAR2(50) DEFAULT 'fas fa-circle',
    status_order NUMBER DEFAULT 0,
    is_active NUMBER(1) DEFAULT 1,
    auto_update_rules CLOB,
    notification_enabled NUMBER(1) DEFAULT 0,
    created_at DATE DEFAULT SYSDATE
);

-- 5. إدراج الحالات الأساسية
INSERT INTO shipment_status_config VALUES ('draft', 'مسودة', 'Draft', '#6c757d', 'fas fa-edit', 1, 1, NULL, 0, SYSDATE);
INSERT INTO shipment_status_config VALUES ('confirmed', 'مؤكدة', 'Confirmed', '#007bff', 'fas fa-check-circle', 2, 1, NULL, 1, SYSDATE);
INSERT INTO shipment_status_config VALUES ('in_transit', 'قيد الشحن', 'In Transit', '#fd7e14', 'fas fa-ship', 3, 1, NULL, 1, SYSDATE);
INSERT INTO shipment_status_config VALUES ('arrived_port', 'وصلت للميناء', 'Arrived at Port', '#17a2b8', 'fas fa-anchor', 4, 1, NULL, 1, SYSDATE);
INSERT INTO shipment_status_config VALUES ('customs_clearance', 'قيد التخليص', 'Customs Clearance', '#ffc107', 'fas fa-truck', 5, 1, NULL, 1, SYSDATE);
INSERT INTO shipment_status_config VALUES ('ready_pickup', 'جاهزة للاستلام', 'Ready for Pickup', '#20c997', 'fas fa-box', 6, 1, NULL, 1, SYSDATE);
INSERT INTO shipment_status_config VALUES ('delivered', 'تم التسليم', 'Delivered', '#28a745', 'fas fa-check-double', 7, 1, NULL, 1, SYSDATE);
INSERT INTO shipment_status_config VALUES ('cancelled', 'ملغية', 'Cancelled', '#dc3545', 'fas fa-times-circle', 8, 1, NULL, 1, SYSDATE);
INSERT INTO shipment_status_config VALUES ('delayed', 'متأخرة', 'Delayed', '#721c24', 'fas fa-exclamation-triangle', 9, 1, NULL, 1, SYSDATE);
INSERT INTO shipment_status_config VALUES ('returned', 'معادة', 'Returned', '#e83e8c', 'fas fa-undo', 10, 1, NULL, 1, SYSDATE);

-- 6. إنشاء فهارس للأداء
CREATE INDEX idx_shipment_status ON cargo_shipments(shipment_status);
CREATE INDEX idx_status_history_shipment ON shipment_status_history(shipment_id);
CREATE INDEX idx_status_history_date ON shipment_status_history(status_date);

-- 7. إنشاء trigger لتسجيل تغييرات الحالة تلقائياً
CREATE OR REPLACE TRIGGER trg_shipment_status_history
    AFTER UPDATE OF shipment_status ON cargo_shipments
    FOR EACH ROW
BEGIN
    IF :OLD.shipment_status != :NEW.shipment_status THEN
        INSERT INTO shipment_status_history (
            id, shipment_id, old_status, new_status, 
            status_date, updated_by, notes
        ) VALUES (
            shipment_status_history_seq.NEXTVAL,
            :NEW.id,
            :OLD.shipment_status,
            :NEW.shipment_status,
            SYSDATE,
            :NEW.status_updated_by,
            'تم تحديث الحالة تلقائياً'
        );
    END IF;
END;
/

-- 8. إنشاء view لعرض الشحنات مع تفاصيل الحالة
CREATE OR REPLACE VIEW v_shipments_with_status AS
SELECT 
    cs.*,
    sc.status_name_ar,
    sc.status_name_en,
    sc.status_color,
    sc.status_icon,
    sc.status_order,
    (SELECT COUNT(*) FROM shipment_status_history WHERE shipment_id = cs.id) as status_changes_count,
    (SELECT MAX(status_date) FROM shipment_status_history WHERE shipment_id = cs.id) as last_status_change
FROM cargo_shipments cs
LEFT JOIN shipment_status_config sc ON cs.shipment_status = sc.status_code;

-- 9. إنشاء دالة لحساب التأخير
CREATE OR REPLACE FUNCTION get_shipment_delay_days(p_shipment_id NUMBER)
RETURN NUMBER
IS
    v_estimated_date DATE;
    v_current_status VARCHAR2(50);
    v_delay_days NUMBER := 0;
BEGIN
    SELECT estimated_delivery_date, shipment_status
    INTO v_estimated_date, v_current_status
    FROM cargo_shipments
    WHERE id = p_shipment_id;
    
    IF v_estimated_date IS NOT NULL AND v_current_status NOT IN ('delivered', 'cancelled') THEN
        v_delay_days := TRUNC(SYSDATE - v_estimated_date);
        IF v_delay_days < 0 THEN
            v_delay_days := 0;
        END IF;
    END IF;
    
    RETURN v_delay_days;
EXCEPTION
    WHEN OTHERS THEN
        RETURN 0;
END;
/

COMMIT;
