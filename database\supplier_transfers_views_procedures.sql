-- =====================================================
-- Views والإجراءات المساعدة لنظام التكامل
-- Integration Views and Procedures
-- =====================================================

-- 1. View شامل لمدفوعات الموردين مع تفاصيل الحوالات
CREATE OR REPLACE VIEW V_SUPPLIER_PAYMENTS_DETAILED AS
SELECT 
    spt.id as payment_id,
    spt.supplier_id,
    s.name_ar as supplier_name,
    s.name_en as supplier_name_en,
    s.supplier_code,
    spt.transfer_request_id,
    tr.request_number,
    spt.transfer_id,
    t.transfer_number,
    spt.payment_amount,
    spt.currency_code,
    c.symbol as currency_symbol,
    spt.exchange_rate,
    spt.base_currency_amount,
    spt.payment_purpose,
    spt.payment_status,
    spt.payment_method,
    spt.discount_applied,
    spt.tax_withheld,
    spt.commission_charged,
    spt.net_amount_transferred,
    spt.requested_date,
    spt.approved_date,
    spt.executed_date,
    spt.completed_date,
    
    -- تفاصيل طلب الحوالة
    tr.status as request_status,
    tr.approved_by as request_approved_by,
    tr.approved_at as request_approved_at,
    
    -- تفاصيل الحوالة المنفذة
    t.status as transfer_status,
    t.execution_date,
    t.executed_by,
    t.reference_number as transfer_reference,
    
    -- تفاصيل المستفيد
    b.beneficiary_name,
    b.bank_name,
    b.bank_account,
    b.iban,
    
    -- تفاصيل الصراف/البنك
    mcb.name as money_changer_name,
    mcb.type as money_changer_type,
    
    -- معلومات المنشئ
    u1.full_name as created_by_name,
    u2.full_name as approved_by_name,
    u3.full_name as executed_by_name
    
FROM SUPPLIER_PAYMENT_TRANSFERS spt
LEFT JOIN SUPPLIERS s ON spt.supplier_id = s.id
LEFT JOIN TRANSFER_REQUESTS tr ON spt.transfer_request_id = tr.id
LEFT JOIN TRANSFERS t ON spt.transfer_id = t.id
LEFT JOIN BENEFICIARIES b ON tr.beneficiary_id = b.id
LEFT JOIN MONEY_CHANGERS_BANKS mcb ON tr.money_changer_bank_id = mcb.id
LEFT JOIN CURRENCIES c ON spt.currency_code = c.code
LEFT JOIN USERS u1 ON spt.created_by = u1.id
LEFT JOIN USERS u2 ON tr.approved_by = u2.id
LEFT JOIN USERS u3 ON t.executed_by = u3.id;

-- 2. View لأرصدة الموردين الموحدة
CREATE OR REPLACE VIEW V_SUPPLIER_BALANCES_SUMMARY AS
SELECT 
    s.id as supplier_id,
    s.supplier_code,
    s.name_ar as supplier_name,
    s.name_en as supplier_name_en,
    s.supplier_type,
    s.city,
    s.phone,
    s.email,
    
    -- إجمالي الأرصدة بالعملة الأساسية
    SUM(sb.current_balance * c.exchange_rate) as total_balance_base_currency,
    
    -- عدد العملات المستخدمة
    COUNT(DISTINCT sb.currency_code) as currencies_count,
    
    -- آخر معاملة
    MAX(sb.last_transaction_date) as last_transaction_date,
    MAX(sb.last_payment_date) as last_payment_date,
    
    -- إحصائيات
    SUM(sb.total_invoices_count) as total_invoices,
    SUM(sb.total_payments_count) as total_payments,
    AVG(sb.average_payment_days) as avg_payment_days,
    
    -- حالة المورد
    CASE 
        WHEN SUM(sb.current_balance * c.exchange_rate) > 0 THEN 'دائن'
        WHEN SUM(sb.current_balance * c.exchange_rate) < 0 THEN 'مدين'
        ELSE 'متوازن'
    END as balance_status,
    
    -- تصنيف المخاطر
    CASE 
        WHEN AVG(sb.average_payment_days) > 60 THEN 'عالي المخاطر'
        WHEN AVG(sb.average_payment_days) > 30 THEN 'متوسط المخاطر'
        ELSE 'منخفض المخاطر'
    END as risk_category
    
FROM SUPPLIERS s
LEFT JOIN SUPPLIER_BALANCES sb ON s.id = sb.supplier_id
LEFT JOIN CURRENCIES c ON sb.currency_code = c.code
WHERE s.is_active = 1
GROUP BY s.id, s.supplier_code, s.name_ar, s.name_en, s.supplier_type, s.city, s.phone, s.email;

-- 3. View للفواتير المستحقة مع تفاصيل المدفوعات
CREATE OR REPLACE VIEW V_SUPPLIER_OUTSTANDING_INVOICES AS
SELECT 
    st.transaction_id,
    st.supplier_id,
    s.name_ar as supplier_name,
    s.supplier_code,
    st.reference_number as invoice_number,
    st.transaction_date as invoice_date,
    st.due_date,
    st.currency_code,
    st.original_amount,
    st.debit_amount,
    st.credit_amount,
    (st.debit_amount - st.credit_amount) as outstanding_amount,
    
    -- حالة الاستحقاق
    CASE 
        WHEN st.due_date < SYSDATE THEN 'متأخر'
        WHEN st.due_date <= SYSDATE + 7 THEN 'مستحق قريباً'
        WHEN st.due_date <= SYSDATE + 30 THEN 'مستحق'
        ELSE 'غير مستحق'
    END as due_status,
    
    -- عدد الأيام
    CASE 
        WHEN st.due_date < SYSDATE THEN TRUNC(SYSDATE - st.due_date)
        ELSE 0
    END as days_overdue,
    
    CASE 
        WHEN st.due_date > SYSDATE THEN TRUNC(st.due_date - SYSDATE)
        ELSE 0
    END as days_until_due,
    
    -- معلومات المدفوعات المرتبطة
    COUNT(spa.id) as payment_allocations_count,
    SUM(spa.allocated_amount) as total_allocated,
    
    -- معلومات العملة
    c.symbol as currency_symbol,
    c.exchange_rate,
    (st.debit_amount - st.credit_amount) * c.exchange_rate as outstanding_base_currency
    
FROM SUPPLIER_TRANSACTIONS st
JOIN SUPPLIERS s ON st.supplier_id = s.id
JOIN CURRENCIES c ON st.currency_code = c.code
LEFT JOIN SUPPLIER_PAYMENT_ALLOCATIONS spa ON st.transaction_id = spa.supplier_transaction_id
WHERE st.transaction_type = 'INVOICE'
AND st.status = 'ACTIVE'
AND (st.debit_amount - st.credit_amount) > 0
GROUP BY st.transaction_id, st.supplier_id, s.name_ar, s.supplier_code, 
         st.reference_number, st.transaction_date, st.due_date, st.currency_code,
         st.original_amount, st.debit_amount, st.credit_amount, c.symbol, c.exchange_rate;

-- 4. إجراء لتحديث أرصدة الموردين
CREATE OR REPLACE PROCEDURE UPDATE_SUPPLIER_BALANCE(
    p_supplier_id IN NUMBER,
    p_currency_code IN VARCHAR2
) AS
    v_opening_balance NUMBER(15,2) := 0;
    v_total_debits NUMBER(15,2) := 0;
    v_total_credits NUMBER(15,2) := 0;
    v_current_balance NUMBER(15,2) := 0;
    v_last_transaction_date DATE;
    v_last_payment_date DATE;
    v_invoices_count NUMBER := 0;
    v_payments_count NUMBER := 0;
    v_avg_payment_days NUMBER(5,2) := 0;
    v_balance_exists NUMBER := 0;
BEGIN
    -- التحقق من وجود رصيد للمورد والعملة
    SELECT COUNT(*) INTO v_balance_exists
    FROM SUPPLIER_BALANCES 
    WHERE supplier_id = p_supplier_id AND currency_code = p_currency_code;
    
    -- حساب إجمالي المدين والدائن
    SELECT 
        NVL(SUM(CASE WHEN transaction_type IN ('INVOICE', 'DEBIT_NOTE') THEN debit_amount ELSE 0 END), 0),
        NVL(SUM(CASE WHEN transaction_type IN ('PAYMENT', 'CREDIT_NOTE') THEN credit_amount ELSE 0 END), 0),
        MAX(CASE WHEN transaction_type != 'PAYMENT' THEN transaction_date END),
        MAX(CASE WHEN transaction_type = 'PAYMENT' THEN transaction_date END),
        SUM(CASE WHEN transaction_type = 'INVOICE' THEN 1 ELSE 0 END),
        SUM(CASE WHEN transaction_type = 'PAYMENT' THEN 1 ELSE 0 END)
    INTO v_total_debits, v_total_credits, v_last_transaction_date, v_last_payment_date,
         v_invoices_count, v_payments_count
    FROM SUPPLIER_TRANSACTIONS
    WHERE supplier_id = p_supplier_id 
    AND currency_code = p_currency_code 
    AND status = 'ACTIVE';
    
    -- حساب الرصيد الحالي
    v_current_balance := v_opening_balance + v_total_debits - v_total_credits;
    
    -- حساب متوسط أيام الدفع
    SELECT NVL(AVG(TRUNC(payment_date - invoice_date)), 0)
    INTO v_avg_payment_days
    FROM (
        SELECT 
            st1.transaction_date as invoice_date,
            MIN(st2.transaction_date) as payment_date
        FROM SUPPLIER_TRANSACTIONS st1
        LEFT JOIN SUPPLIER_TRANSACTIONS st2 ON st1.supplier_id = st2.supplier_id
            AND st2.transaction_type = 'PAYMENT'
            AND st2.transaction_date >= st1.transaction_date
        WHERE st1.supplier_id = p_supplier_id
        AND st1.currency_code = p_currency_code
        AND st1.transaction_type = 'INVOICE'
        AND st1.status = 'ACTIVE'
        GROUP BY st1.transaction_id, st1.transaction_date
        HAVING MIN(st2.transaction_date) IS NOT NULL
    );
    
    -- تحديث أو إدراج الرصيد
    IF v_balance_exists > 0 THEN
        UPDATE SUPPLIER_BALANCES SET
            debit_amount = v_total_debits,
            credit_amount = v_total_credits,
            current_balance = v_current_balance,
            last_transaction_date = v_last_transaction_date,
            last_payment_date = v_last_payment_date,
            total_invoices_count = v_invoices_count,
            total_payments_count = v_payments_count,
            average_payment_days = v_avg_payment_days,
            updated_at = CURRENT_TIMESTAMP
        WHERE supplier_id = p_supplier_id AND currency_code = p_currency_code;
    ELSE
        INSERT INTO SUPPLIER_BALANCES (
            supplier_id, currency_code, opening_balance, debit_amount, credit_amount,
            current_balance, last_transaction_date, last_payment_date,
            total_invoices_count, total_payments_count, average_payment_days,
            created_at, updated_at
        ) VALUES (
            p_supplier_id, p_currency_code, v_opening_balance, v_total_debits, v_total_credits,
            v_current_balance, v_last_transaction_date, v_last_payment_date,
            v_invoices_count, v_payments_count, v_avg_payment_days,
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        );
    END IF;
    
    COMMIT;
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END UPDATE_SUPPLIER_BALANCE;

-- 5. إجراء لمعالجة دفعة مورد مكتملة
CREATE OR REPLACE PROCEDURE PROCESS_COMPLETED_SUPPLIER_PAYMENT(
    p_transfer_id IN NUMBER
) AS
    v_supplier_id NUMBER;
    v_currency_code VARCHAR2(3);
    v_payment_amount NUMBER(15,2);
    v_supplier_payment_id NUMBER;
BEGIN
    -- الحصول على تفاصيل الدفعة
    SELECT spt.id, spt.supplier_id, spt.currency_code, spt.net_amount_transferred
    INTO v_supplier_payment_id, v_supplier_id, v_currency_code, v_payment_amount
    FROM SUPPLIER_PAYMENT_TRANSFERS spt
    WHERE spt.transfer_id = p_transfer_id;
    
    -- تحديث حالة الدفعة
    UPDATE SUPPLIER_PAYMENT_TRANSFERS SET
        payment_status = 'COMPLETED',
        completed_date = SYSDATE,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = v_supplier_payment_id;
    
    -- إنشاء معاملة دفع في حساب المورد
    INSERT INTO SUPPLIER_TRANSACTIONS (
        supplier_id, transaction_type, reference_type, reference_id,
        transaction_date, currency_code, original_amount, credit_amount,
        description, status, created_date, created_by
    ) VALUES (
        v_supplier_id, 'PAYMENT', 'TRANSFER', p_transfer_id,
        SYSDATE, v_currency_code, v_payment_amount, v_payment_amount,
        'دفعة عبر نظام الحوالات - رقم الحوالة: ' || p_transfer_id,
        'ACTIVE', CURRENT_TIMESTAMP, 1
    );
    
    -- تحديث رصيد المورد
    UPDATE_SUPPLIER_BALANCE(v_supplier_id, v_currency_code);
    
    COMMIT;
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END PROCESS_COMPLETED_SUPPLIER_PAYMENT;

-- 6. إنشاء تعليقات للـ Views والإجراءات
COMMENT ON VIEW V_SUPPLIER_PAYMENTS_DETAILED IS 'عرض شامل لمدفوعات الموردين مع تفاصيل الحوالات';
COMMENT ON VIEW V_SUPPLIER_BALANCES_SUMMARY IS 'ملخص أرصدة الموردين الموحدة';
COMMENT ON VIEW V_SUPPLIER_OUTSTANDING_INVOICES IS 'الفواتير المستحقة مع تفاصيل المدفوعات';

-- تم إنشاء Views والإجراءات المساعدة بنجاح
-- Views and procedures created successfully
