#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Routes الرئيسية لنظام الحوالات
Main Routes for Money Transfer System
"""

from flask import render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from . import transfers_bp
from database_manager import DatabaseManager
import logging

logger = logging.getLogger(__name__)

@transfers_bp.route('/')
@login_required
def index():
    """الصفحة الرئيسية لنظام الحوالات"""
    return redirect(url_for('transfers.dashboard'))

@transfers_bp.route('/dashboard')
@login_required
def dashboard():
    """لوحة تحكم الحوالات"""
    try:
        db = DatabaseManager()
        
        # إحصائيات سريعة
        stats = {
            'total_requests': 0,
            'pending_requests': 0,
            'approved_requests': 0,
            'executed_transfers': 0,
            'total_amount_today': 0,
            'total_commission_today': 0
        }
        
        # جلب الإحصائيات من قاعدة البيانات
        try:
            # إجمالي الطلبات
            result = db.execute_query("SELECT COUNT(*) FROM transfer_requests")
            if result:
                stats['total_requests'] = result[0][0]
            
            # الطلبات المعلقة
            result = db.execute_query("SELECT COUNT(*) FROM transfer_requests WHERE status = 'pending'")
            if result:
                stats['pending_requests'] = result[0][0]
            
            # الطلبات المعتمدة
            result = db.execute_query("SELECT COUNT(*) FROM transfer_requests WHERE status = 'approved'")
            if result:
                stats['approved_requests'] = result[0][0]
            
            # الحوالات المنفذة
            result = db.execute_query("SELECT COUNT(*) FROM transfers WHERE status = 'completed'")
            if result:
                stats['executed_transfers'] = result[0][0]
            
            # المبلغ الإجمالي اليوم من طلبات الحوالات
            result = db.execute_query("""
                SELECT COALESCE(SUM(AMOUNT), 0)
                FROM TRANSFER_REQUESTS
                WHERE TRUNC(CREATED_AT) = TRUNC(SYSDATE)
                AND STATUS = 'completed'
            """)
            if result:
                stats['total_amount_today'] = result[0][0]
            else:
                stats['total_amount_today'] = 0

            # العمولة الإجمالية اليوم (افتراضية)
            stats['total_commission_today'] = 0
                
        except Exception as e:
            logger.warning(f"خطأ في جلب الإحصائيات: {e}")
        
        # آخر الأنشطة
        recent_activities = []
        try:
            # تبسيط الاستعلام لتجنب مشاكل Oracle
            activities_query = """
            SELECT 'request' as type,
                   REQUEST_NUMBER as number,
                   AMOUNT,
                   CURRENCY,
                   CREATED_AT,
                   STATUS
            FROM TRANSFER_REQUESTS
            WHERE CREATED_AT >= SYSDATE - 7
            AND ROWNUM <= 10
            ORDER BY CREATED_AT DESC
            """
            
            result = db.execute_query(activities_query)
            if result:
                recent_activities = []
                for row in result:
                    try:
                        activity = {
                            'type': str(row[0]) if row[0] else 'request',
                            'number': str(row[1]) if row[1] else 'غير محدد',
                            'amount': float(row[2]) if row[2] else 0,
                            'currency': str(row[3]) if row[3] else 'TRY',
                            'date': row[4].strftime('%Y-%m-%d %H:%M:%S') if row[4] else '',
                            'status': str(row[5]) if row[5] else 'pending'
                        }
                        recent_activities.append(activity)
                    except Exception as e:
                        logger.warning(f"خطأ في معالجة نشاط: {e}")
                        continue
        except Exception as e:
            logger.warning(f"خطأ في جلب الأنشطة الأخيرة: {e}")
            recent_activities = []
        
        return render_template('transfers/dashboard.html', 
                             stats=stats, 
                             recent_activities=recent_activities)
        
    except Exception as e:
        logger.error(f"خطأ في لوحة تحكم الحوالات: {e}")
        flash('حدث خطأ في تحميل لوحة التحكم', 'error')
        return render_template('transfers/dashboard.html', 
                             stats={}, 
                             recent_activities=[])

@transfers_bp.route('/api/stats')
@login_required
def api_stats():
    """API للحصول على الإحصائيات"""
    try:
        db = DatabaseManager()
        
        # إحصائيات مفصلة
        stats = {}
        
        # إحصائيات الطلبات حسب الحالة
        request_stats = db.execute_query("""
            SELECT status, COUNT(*) as count, COALESCE(SUM(amount), 0) as total_amount
            FROM transfer_requests 
            GROUP BY status
        """)
        
        stats['requests_by_status'] = {}
        if request_stats:
            for row in request_stats:
                stats['requests_by_status'][row[0]] = {
                    'count': row[1],
                    'total_amount': float(row[2])
                }
        
        # إحصائيات الحوالات حسب الشهر
        monthly_stats = db.execute_query("""
            SELECT
                TO_CHAR(execution_date, 'YYYY-MM') as month,
                COUNT(*) as count,
                COALESCE(SUM(NVL(net_amount_sent, 0)), 0) as total_amount,
                COALESCE(SUM(NVL(actual_commission, 0)), 0) as total_commission
            FROM transfers
            WHERE execution_date >= SYSDATE - 365
            AND status = 'completed'
            GROUP BY TO_CHAR(execution_date, 'YYYY-MM')
            ORDER BY month DESC
        """)
        
        stats['monthly_transfers'] = []
        if monthly_stats:
            for row in monthly_stats:
                stats['monthly_transfers'].append({
                    'month': row[0],
                    'count': row[1],
                    'total_amount': float(row[2]),
                    'total_commission': float(row[3])
                })
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        logger.error(f"خطأ في API الإحصائيات: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

# استيراد routes الأرصدة الافتتاحية للصرافين/البنوك
try:
    from . import money_changers_opening_balances
    print("✅ تم تحميل نظام الأرصدة الافتتاحية للصرافين/البنوك")
except Exception as e:
    print(f"❌ خطأ في تحميل نظام الأرصدة الافتتاحية للصرافين/البنوك: {e}")

# استيراد routes أرصدة الصرافين/البنوك
try:
    from . import money_changers_balances
    print("✅ تم تحميل نظام إدارة أرصدة الصرافين/البنوك")
except Exception as e:
    print(f"❌ خطأ في تحميل نظام إدارة أرصدة الصرافين/البنوك: {e}")

@transfers_bp.route('/money-changers-balances')
@login_required
def money_changers_balances():
    """نافذة أرصدة الصرافين/البنوك - صورة طبق الأصل من نافذة أرصدة الموردين"""
    return render_template('transfers/money_changers_balances.html')
