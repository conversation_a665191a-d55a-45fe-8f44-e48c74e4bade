# -*- coding: utf-8 -*-
"""
إعداد قاعدة البيانات لنظام الإشعارات الفورية
Database Setup for Instant Notifications System
"""

from database_manager import DatabaseManager

def create_instant_notifications_tables():
    """إنشاء جداول نظام الإشعارات الفورية"""
    
    db = DatabaseManager()
    
    print("🔧 إنشاء جداول نظام الإشعارات الفورية...")
    
    # 1. جدول إعدادات الإشعارات
    notifications_settings_table = """
    CREATE TABLE instant_notification_settings (
        id NUMBER PRIMARY KEY,
        event_type VARCHAR2(100) NOT NULL,
        event_name_ar VARCHAR2(200) NOT NULL,
        event_name_en VARCHAR2(200),
        is_enabled NUMBER(1) DEFAULT 1,
        message_template CLOB NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by NUMBER DEFAULT 1,
        updated_by NUMBER DEFAULT 1
    )
    """
    
    # 2. جدول جهات الاتصال
    contacts_table = """
    CREATE TABLE instant_notification_contacts (
        id NUMBER PRIMARY KEY,
        contact_name VARCHAR2(200) NOT NULL,
        phone_number VARCHAR2(20) NOT NULL,
        contact_type VARCHAR2(50) DEFAULT 'general',
        is_active NUMBER(1) DEFAULT 1,
        notes VARCHAR2(500),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by NUMBER DEFAULT 1
    )
    """
    
    # 3. جدول ربط الإشعارات بجهات الاتصال
    notification_contact_mapping = """
    CREATE TABLE instant_notification_mapping (
        id NUMBER PRIMARY KEY,
        event_type VARCHAR2(100) NOT NULL,
        contact_id NUMBER NOT NULL,
        is_active NUMBER(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (contact_id) REFERENCES instant_notification_contacts(id)
    )
    """
    
    # 4. جدول إحصائيات الإرسال
    sending_stats_table = """
    CREATE TABLE instant_notification_stats (
        id NUMBER PRIMARY KEY,
        event_type VARCHAR2(100) NOT NULL,
        contact_id NUMBER,
        phone_number VARCHAR2(20),
        message_content CLOB,
        status VARCHAR2(20) DEFAULT 'pending',
        whatsapp_message_id VARCHAR2(100),
        sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        error_message VARCHAR2(500),
        shipment_id NUMBER,
        tracking_number VARCHAR2(100)
    )
    """
    
    # إنشاء Sequences (أسماء قصيرة لـ Oracle)
    sequences = [
        "CREATE SEQUENCE inst_notif_settings_seq START WITH 1 INCREMENT BY 1",
        "CREATE SEQUENCE inst_notif_contacts_seq START WITH 1 INCREMENT BY 1",
        "CREATE SEQUENCE inst_notif_mapping_seq START WITH 1 INCREMENT BY 1",
        "CREATE SEQUENCE inst_notif_stats_seq START WITH 1 INCREMENT BY 1"
    ]
    
    try:
        # إنشاء الجداول
        tables = [
            ("instant_notification_settings", notifications_settings_table),
            ("instant_notification_contacts", contacts_table),
            ("instant_notification_mapping", notification_contact_mapping),
            ("instant_notification_stats", sending_stats_table)
        ]
        
        for table_name, table_sql in tables:
            try:
                db.execute_update(table_sql)
                print(f"✅ تم إنشاء جدول {table_name}")
            except Exception as e:
                if "already exists" in str(e).lower() or "name is already used" in str(e).lower():
                    print(f"📋 جدول {table_name} موجود بالفعل")
                else:
                    print(f"❌ خطأ في إنشاء جدول {table_name}: {e}")
        
        # إنشاء Sequences
        for seq_sql in sequences:
            try:
                db.execute_update(seq_sql)
                print(f"✅ تم إنشاء sequence")
            except Exception as e:
                if "already exists" in str(e).lower() or "name is already used" in str(e).lower():
                    print(f"📋 Sequence موجود بالفعل")
                else:
                    print(f"❌ خطأ في إنشاء sequence: {e}")
        
        db.commit()
        
        # إدراج البيانات الافتراضية
        insert_default_data(db)
        
        print("🎉 تم إنشاء جداول نظام الإشعارات الفورية بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في إنشاء الجداول: {e}")
        return False

def insert_default_data(db):
    """إدراج البيانات الافتراضية"""
    
    print("📝 إدراج البيانات الافتراضية...")
    
    # إعدادات الإشعارات الافتراضية
    default_settings = [
        ('customs_clearance_notification', 'إشعار التخليص الجمركي', 'Customs Clearance Notification', 
         'تم تغيير حالة الشحنة {tracking_number} إلى قيد التخليص الجمركي'),
        ('delivery_notification', 'إشعار التسليم', 'Delivery Notification',
         'تم تسليم الشحنة {tracking_number} بنجاح ✅'),
        ('port_arrival_notification', 'إشعار وصول الميناء', 'Port Arrival Notification',
         'وصلت الشحنة {tracking_number} إلى {port_of_discharge} 🚢'),
        ('in_transit_notification', 'إشعار في الطريق', 'In Transit Notification',
         'الشحنة {tracking_number} في الطريق 🚛'),
        ('document_ready_notification', 'إشعار جاهزية الوثائق', 'Documents Ready Notification',
         'وثائق الشحنة {tracking_number} جاهزة للاستلام 📋')
    ]
    
    for event_type, name_ar, name_en, message in default_settings:
        try:
            # فحص إذا كان الإعداد موجود
            check_query = "SELECT COUNT(*) FROM instant_notification_settings WHERE event_type = :1"
            result = db.execute_query(check_query, [event_type])
            
            if result and result[0][0] == 0:
                # إدراج الإعداد الجديد
                insert_query = """
                INSERT INTO instant_notification_settings
                (id, event_type, event_name_ar, event_name_en, message_template, is_enabled)
                VALUES (inst_notif_settings_seq.NEXTVAL, :1, :2, :3, :4, 1)
                """
                
                db.execute_update(insert_query, [event_type, name_ar, name_en, message])
                print(f"✅ تم إدراج إعداد: {name_ar}")
            else:
                print(f"📋 إعداد موجود: {name_ar}")
                
        except Exception as e:
            print(f"❌ خطأ في إدراج إعداد {name_ar}: {e}")
    
    # جهة اتصال افتراضية
    try:
        check_contact = "SELECT COUNT(*) FROM instant_notification_contacts WHERE phone_number = '967774893877'"
        result = db.execute_query(check_contact)
        
        if result and result[0][0] == 0:
            insert_contact = """
            INSERT INTO instant_notification_contacts
            (id, contact_name, phone_number, contact_type, notes)
            VALUES (inst_notif_contacts_seq.NEXTVAL, 'الإدارة الرئيسية', '967774893877', 'admin', 'جهة الاتصال الافتراضية')
            """
            
            db.execute_update(insert_contact)
            print("✅ تم إدراج جهة الاتصال الافتراضية")
        else:
            print("📋 جهة الاتصال الافتراضية موجودة")
            
    except Exception as e:
        print(f"❌ خطأ في إدراج جهة الاتصال: {e}")
    
    db.commit()
    print("✅ تم إدراج البيانات الافتراضية بنجاح")

if __name__ == "__main__":
    create_instant_notifications_tables()
