-- =====================================================
-- نظام إدارة أرصدة الموردين المتقدم
-- Advanced Supplier Balance Management System
-- =====================================================

-- 1. جدول حسابات الموردين الرئيسي
CREATE TABLE SUPPLIER_ACCOUNTS (
    account_id NUMBER PRIMARY KEY,
    supplier_code VARCHAR2(50) NOT NULL,
    supplier_name NVARCHAR2(200) NOT NULL,
    account_number VARCHAR2(50) UNIQUE,
    
    -- معلومات الحساب
    account_type VARCHAR2(30) DEFAULT 'TRADE', -- TRADE, SERVICE, CONTRACTOR
    credit_limit NUMBER(15,2) DEFAULT 0,
    payment_terms_days NUMBER DEFAULT 30,
    discount_percentage NUMBER(5,2) DEFAULT 0,
    
    -- شروط الدفع
    payment_method VARCHAR2(30) DEFAULT 'BANK_TRANSFER',
    currency_code VARCHAR2(3) DEFAULT 'SAR',
    tax_number VARCHAR2(50),
    
    -- معلومات الاتصال
    contact_person NVARCHAR2(100),
    phone VARCHAR2(20),
    email VARCHAR2(100),
    address NVARCHAR2(500),
    
    -- حالة الحساب
    account_status VARCHAR2(20) DEFAULT 'ACTIVE', -- ACTIVE, SUSPENDED, CLOSED
    risk_rating VARCHAR2(20) DEFAULT 'LOW', -- LOW, MEDIUM, HIGH, CRITICAL
    
    -- تواريخ النظام
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_sa_created_by FOREIGN KEY (created_by) REFERENCES USERS(id),
    CONSTRAINT fk_sa_updated_by FOREIGN KEY (updated_by) REFERENCES USERS(id),
    
    -- فهارس
    CONSTRAINT uk_sa_supplier_code UNIQUE (supplier_code),
    CONSTRAINT uk_sa_account_number UNIQUE (account_number)
);

-- 2. جدول معاملات الموردين المفصل
CREATE TABLE SUPPLIER_TRANSACTIONS (
    transaction_id NUMBER PRIMARY KEY,
    account_id NUMBER NOT NULL,
    
    -- تفاصيل المعاملة
    transaction_type VARCHAR2(30) NOT NULL, -- INVOICE, PAYMENT, CREDIT_NOTE, DEBIT_NOTE, ADJUSTMENT, OPENING_BALANCE
    reference_type VARCHAR2(30), -- PURCHASE_ORDER, TRANSFER_REQUEST, MANUAL, SYSTEM
    reference_id NUMBER,
    reference_number VARCHAR2(100),
    
    -- التواريخ
    transaction_date DATE DEFAULT SYSDATE,
    due_date DATE,
    value_date DATE,
    
    -- المبالغ
    currency_code VARCHAR2(3) NOT NULL,
    original_amount NUMBER(15,2) NOT NULL,
    exchange_rate NUMBER(15,6) DEFAULT 1,
    base_currency_amount NUMBER(15,2),
    
    -- التصنيف المحاسبي
    debit_amount NUMBER(15,2) DEFAULT 0,
    credit_amount NUMBER(15,2) DEFAULT 0,
    running_balance NUMBER(15,2) DEFAULT 0,
    
    -- الوصف والملاحظات
    description NVARCHAR2(500),
    internal_notes CLOB,
    supplier_reference VARCHAR2(100),
    
    -- حالة المعاملة
    status VARCHAR2(20) DEFAULT 'POSTED', -- DRAFT, POSTED, CANCELLED, REVERSED
    reconciled CHAR(1) DEFAULT 'N',
    reconciliation_date DATE,
    
    -- معلومات الموافقة
    approved_by NUMBER,
    approved_date DATE,
    approval_notes NVARCHAR2(500),
    
    -- تواريخ النظام
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_st_account FOREIGN KEY (account_id) REFERENCES SUPPLIER_ACCOUNTS(account_id),
    CONSTRAINT fk_st_approved_by FOREIGN KEY (approved_by) REFERENCES USERS(id),
    CONSTRAINT fk_st_created_by FOREIGN KEY (created_by) REFERENCES USERS(id),
    CONSTRAINT fk_st_updated_by FOREIGN KEY (updated_by) REFERENCES USERS(id)
);

-- 3. جدول أرصدة الموردين الحية
CREATE TABLE SUPPLIER_BALANCES (
    balance_id NUMBER PRIMARY KEY,
    account_id NUMBER NOT NULL,
    currency_code VARCHAR2(3) NOT NULL,
    
    -- الأرصدة
    opening_balance NUMBER(15,2) DEFAULT 0,
    current_balance NUMBER(15,2) DEFAULT 0,
    available_balance NUMBER(15,2) DEFAULT 0, -- بعد خصم المحجوزات
    
    -- إجماليات الحركة
    total_debits NUMBER(15,2) DEFAULT 0,
    total_credits NUMBER(15,2) DEFAULT 0,
    ytd_debits NUMBER(15,2) DEFAULT 0, -- Year to Date
    ytd_credits NUMBER(15,2) DEFAULT 0,
    
    -- إحصائيات
    last_transaction_date DATE,
    last_payment_date DATE,
    largest_transaction NUMBER(15,2) DEFAULT 0,
    transaction_count NUMBER DEFAULT 0,
    
    -- تحليل الاستحقاقات
    current_due NUMBER(15,2) DEFAULT 0, -- مستحق حالياً
    overdue_1_30 NUMBER(15,2) DEFAULT 0, -- متأخر 1-30 يوم
    overdue_31_60 NUMBER(15,2) DEFAULT 0, -- متأخر 31-60 يوم
    overdue_61_90 NUMBER(15,2) DEFAULT 0, -- متأخر 61-90 يوم
    overdue_over_90 NUMBER(15,2) DEFAULT 0, -- متأخر أكثر من 90 يوم
    
    -- مؤشرات الأداء
    average_payment_days NUMBER(5,2) DEFAULT 0,
    payment_reliability_score NUMBER(3,2) DEFAULT 0, -- من 1 إلى 5
    
    -- تواريخ التحديث
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_reconciled DATE,
    
    -- المفاتيح الخارجية والقيود
    CONSTRAINT fk_sb_account FOREIGN KEY (account_id) REFERENCES SUPPLIER_ACCOUNTS(account_id),
    CONSTRAINT uk_sb_account_currency UNIQUE (account_id, currency_code)
);

-- 4. جدول دورات المطابقة
CREATE TABLE RECONCILIATION_CYCLES (
    cycle_id NUMBER PRIMARY KEY,
    cycle_name NVARCHAR2(100) NOT NULL,
    cycle_description NVARCHAR2(500),
    
    -- فترة المطابقة
    period_from DATE NOT NULL,
    period_to DATE NOT NULL,
    reconciliation_date DATE DEFAULT SYSDATE,
    
    -- نوع المطابقة
    cycle_type VARCHAR2(30) DEFAULT 'MONTHLY', -- DAILY, WEEKLY, MONTHLY, QUARTERLY, ANNUAL, ADHOC
    scope VARCHAR2(30) DEFAULT 'ALL', -- ALL, SELECTED, HIGH_VALUE, HIGH_RISK
    
    -- حالة الدورة
    status VARCHAR2(20) DEFAULT 'OPEN', -- OPEN, IN_PROGRESS, COMPLETED, CANCELLED
    
    -- إحصائيات
    total_accounts NUMBER DEFAULT 0,
    processed_accounts NUMBER DEFAULT 0,
    matched_accounts NUMBER DEFAULT 0,
    unmatched_accounts NUMBER DEFAULT 0,
    total_differences_amount NUMBER(15,2) DEFAULT 0,
    
    -- معلومات التنفيذ
    started_by NUMBER,
    started_date TIMESTAMP,
    completed_by NUMBER,
    completed_date TIMESTAMP,
    
    -- ملاحظات ومرفقات
    notes CLOB,
    attachment_path VARCHAR2(500),
    
    -- تواريخ النظام
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_rc_started_by FOREIGN KEY (started_by) REFERENCES USERS(id),
    CONSTRAINT fk_rc_completed_by FOREIGN KEY (completed_by) REFERENCES USERS(id),
    CONSTRAINT fk_rc_created_by FOREIGN KEY (created_by) REFERENCES USERS(id),
    CONSTRAINT fk_rc_updated_by FOREIGN KEY (updated_by) REFERENCES USERS(id)
);

-- 5. جدول عناصر المطابقة التفصيلية
CREATE TABLE RECONCILIATION_ITEMS (
    item_id NUMBER PRIMARY KEY,
    cycle_id NUMBER NOT NULL,
    account_id NUMBER NOT NULL,
    
    -- أرصدة النظام
    system_opening_balance NUMBER(15,2) DEFAULT 0,
    system_debits NUMBER(15,2) DEFAULT 0,
    system_credits NUMBER(15,2) DEFAULT 0,
    system_closing_balance NUMBER(15,2) DEFAULT 0,
    
    -- أرصدة المورد
    supplier_opening_balance NUMBER(15,2) DEFAULT 0,
    supplier_debits NUMBER(15,2) DEFAULT 0,
    supplier_credits NUMBER(15,2) DEFAULT 0,
    supplier_closing_balance NUMBER(15,2) DEFAULT 0,
    
    -- الفروقات
    opening_difference NUMBER(15,2) DEFAULT 0,
    debits_difference NUMBER(15,2) DEFAULT 0,
    credits_difference NUMBER(15,2) DEFAULT 0,
    closing_difference NUMBER(15,2) DEFAULT 0,
    total_difference NUMBER(15,2) DEFAULT 0,
    
    -- حالة المطابقة
    reconciliation_status VARCHAR2(20) DEFAULT 'PENDING', -- PENDING, MATCHED, UNMATCHED, INVESTIGATING, RESOLVED
    match_tolerance NUMBER(15,2) DEFAULT 0.01,
    auto_matched CHAR(1) DEFAULT 'N',
    
    -- معلومات كشف المورد
    supplier_statement_date DATE,
    supplier_statement_ref VARCHAR2(100),
    supplier_statement_file VARCHAR2(500),
    
    -- ملاحظات
    reconciliation_notes CLOB,
    system_notes CLOB,
    supplier_notes CLOB,
    
    -- معلومات المعالجة
    processed_by NUMBER,
    processed_date TIMESTAMP,
    approved_by NUMBER,
    approved_date TIMESTAMP,
    
    -- تواريخ النظام
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_ri_cycle FOREIGN KEY (cycle_id) REFERENCES RECONCILIATION_CYCLES(cycle_id),
    CONSTRAINT fk_ri_account FOREIGN KEY (account_id) REFERENCES SUPPLIER_ACCOUNTS(account_id),
    CONSTRAINT fk_ri_processed_by FOREIGN KEY (processed_by) REFERENCES USERS(id),
    CONSTRAINT fk_ri_approved_by FOREIGN KEY (approved_by) REFERENCES USERS(id)
);

-- إنشاء Sequences
CREATE SEQUENCE SUPPLIER_ACCOUNTS_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SUPPLIER_TRANSACTIONS_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SUPPLIER_BALANCES_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE RECONCILIATION_CYCLES_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE RECONCILIATION_ITEMS_SEQ START WITH 1 INCREMENT BY 1;

-- رسالة نجاح
SELECT 'تم إنشاء الجداول الأساسية لنظام إدارة أرصدة الموردين بنجاح!' as status FROM dual;

COMMIT;
