<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حسابات العمولات - نظام الفوجي</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/commission-opening-balances-style.css') }}" rel="stylesheet">
</head>
<body>

    <!-- Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="fas fa-calculator me-3"></i>
                        حسابات العمولات
                    </h1>
                    <p class="page-subtitle">
                        حساب وإدارة عمولات المندوبين مع نظام الموافقات الثلاثي
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{{ url_for('purchase_commissions.index') }}" class="btn btn-light btn-modern">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="breadcrumb-container">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('purchase_commissions.index') }}">عمولات المندوبين</a></li>
                    <li class="breadcrumb-item active" aria-current="page">حسابات العمولات</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="container">
        
        <!-- Control Panel -->
        <div class="control-panel">
            <h5>
                <i class="fas fa-cogs"></i>
                إجراءات سريعة
            </h5>
            <div class="row g-3">
                <div class="col-md-3">
                    <button type="button" class="btn btn-primary btn-modern w-100" onclick="calculateCommissions()">
                        <i class="fas fa-calculator"></i>
                        حساب العمولات
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-success btn-modern w-100" onclick="approveSelected()">
                        <i class="fas fa-check"></i>
                        اعتماد المحدد
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-warning btn-modern w-100" onclick="paySelected()">
                        <i class="fas fa-money-bill"></i>
                        دفع المحدد
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-info btn-modern w-100" onclick="exportData()">
                        <i class="fas fa-download"></i>
                        تصدير البيانات
                    </button>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="stats-container">
            <div class="row g-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon primary">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <div class="stat-value">{{ calculations|selectattr('status', 'equalto', 'calculated')|list|length }}</div>
                        <div class="stat-label">محسوبة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-value">{{ calculations|selectattr('status', 'equalto', 'approved')|list|length }}</div>
                        <div class="stat-label">معتمدة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon warning">
                            <i class="fas fa-money-bill"></i>
                        </div>
                        <div class="stat-value">{{ calculations|selectattr('status', 'equalto', 'paid')|list|length }}</div>
                        <div class="stat-label">مدفوعة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon info">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="stat-value">{{ "{:,.0f}".format(calculations|sum(attribute='commission_amount') or 0) }}</div>
                        <div class="stat-label">إجمالي العمولات</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Data Table -->
        <div class="data-table-container">
            <div class="data-table-header">
                <h5 class="data-table-title">
                    <i class="fas fa-table"></i>
                    حسابات العمولات
                </h5>
                <div>
                    <button class="btn btn-primary btn-modern btn-sm" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-modern" id="calculationsTable">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAll"></th>
                            <th>المندوب</th>
                            <th>الفترة</th>
                            <th>مبلغ العمولة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for calc in calculations %}
                        <tr>
                            <td><input type="checkbox" class="calc-checkbox" value="{{ calc.id }}"></td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon primary me-2" style="width: 40px; height: 40px; font-size: 1rem;">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div>
                                        <strong>{{ calc.rep_name or 'غير محدد' }}</strong>
                                        <br><small class="text-muted">كود: {{ calc.rep_code or 'غير محدد' }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <strong>{{ calc.rule_name or 'غير محدد' }}</strong>
                                <br><small class="text-muted">{{ calc.calculation_date.strftime('%Y-%m-%d') if calc.calculation_date else 'غير محدد' }}</small>
                            </td>
                            <td>
                                <strong class="text-success">{{ "{:,.2f}".format(calc.commission_amount or 0) }} ريال</strong>
                            </td>
                            <td>
                                {% if calc.status == 'calculated' %}
                                <span class="badge badge-modern bg-primary">محسوبة</span>
                                {% elif calc.status == 'approved' %}
                                <span class="badge badge-modern bg-success">معتمدة</span>
                                {% elif calc.status == 'paid' %}
                                <span class="badge badge-modern bg-warning">مدفوعة</span>
                                {% else %}
                                <span class="badge badge-modern bg-secondary">{{ calc.status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    {% if calc.status == 'calculated' %}
                                    <button class="btn btn-success btn-modern btn-sm" onclick="approveCalculation({{ calc.id }})">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    {% elif calc.status == 'approved' %}
                                    <button class="btn btn-warning btn-modern btn-sm" onclick="payCalculation({{ calc.id }})">
                                        <i class="fas fa-money-bill"></i>
                                    </button>
                                    {% endif %}
                                    <button class="btn btn-info btn-modern btn-sm" onclick="viewDetails({{ calc.id }})">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-danger btn-modern btn-sm" onclick="deleteCalculation({{ calc.id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function calculateCommissions() {
            showAlert('سيتم حساب العمولات قريباً', 'info');
        }

        function approveSelected() {
            const selected = getSelectedCalculations();
            if (selected.length === 0) {
                showAlert('يرجى تحديد العمولات المراد اعتمادها', 'warning');
                return;
            }
            showAlert(`تم اعتماد ${selected.length} عمولة بنجاح`, 'success');
        }

        function paySelected() {
            const selected = getSelectedCalculations();
            if (selected.length === 0) {
                showAlert('يرجى تحديد العمولات المراد دفعها', 'warning');
                return;
            }
            showAlert(`تم دفع ${selected.length} عمولة بنجاح`, 'success');
        }

        function getSelectedCalculations() {
            return Array.from(document.querySelectorAll('.calc-checkbox:checked')).map(cb => cb.value);
        }

        function approveCalculation(id) {
            if (confirm('هل أنت متأكد من اعتماد هذه العمولة؟')) {
                showAlert('تم اعتماد العمولة بنجاح', 'success');
            }
        }

        function payCalculation(id) {
            if (confirm('هل أنت متأكد من دفع هذه العمولة؟')) {
                showAlert('تم دفع العمولة بنجاح', 'success');
            }
        }

        function viewDetails(id) {
            showAlert('سيتم عرض التفاصيل قريباً', 'info');
        }

        function deleteCalculation(id) {
            if (confirm('هل أنت متأكد من حذف هذا الحساب؟')) {
                showAlert('تم حذف الحساب بنجاح', 'success');
            }
        }

        function refreshData() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            button.disabled = true;
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                showAlert('تم تحديث البيانات بنجاح', 'success');
            }, 2000);
        }

        function exportData() {
            showAlert('سيتم تصدير البيانات قريباً', 'info');
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-modern alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'info' ? 'info-circle' : 'exclamation-triangle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('.container').insertBefore(alert, document.querySelector('.control-panel'));
            
            setTimeout(() => {
                alert.remove();
            }, 3000);
        }

        // Select All functionality
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.calc-checkbox');
            checkboxes.forEach(cb => cb.checked = this.checked);
        });

        // تأثيرات hover للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(-5px)';
                });
            });
        });
    </script>

</body>
</html>
