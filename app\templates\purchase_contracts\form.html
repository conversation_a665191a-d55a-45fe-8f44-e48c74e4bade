{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <form method="POST" id="contractForm">
        {{ form.hidden_tag() }}
        
        <!-- شريط الأدوات -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-file-contract me-2"></i>
                                {{ title }}
                            </h5>
                            <div class="btn-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>حفظ
                                </button>
                                <a href="{{ url_for('purchase_contracts.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>إلغاء
                                </a>
                                {% if not is_new %}
                                <a href="{{ url_for('purchase_contracts.view', id=contract.id) }}" class="btn btn-outline-info">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- القسم الرئيسي - التبويبات -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <!-- التبويبات -->
                        <ul class="nav nav-tabs" id="contractTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" 
                                        data-bs-target="#basic" type="button" role="tab">
                                    <i class="fas fa-info-circle me-1"></i>المعلومات الأساسية
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="financial-tab" data-bs-toggle="tab" 
                                        data-bs-target="#financial" type="button" role="tab">
                                    <i class="fas fa-money-bill-wave me-1"></i>المعلومات المالية
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="additional-tab" data-bs-toggle="tab" 
                                        data-bs-target="#additional" type="button" role="tab">
                                    <i class="fas fa-file-alt me-1"></i>معلومات إضافية
                                </button>
                            </li>
                        </ul>

                        <!-- محتوى التبويبات -->
                        <div class="tab-content mt-3" id="contractTabsContent">
                            <!-- التبويب الأول: المعلومات الأساسية -->
                            <div class="tab-pane fade show active" id="basic" role="tabpanel">
                                <div class="row g-3">
                                    <!-- الصف الأول -->
                                    <div class="col-md-2">
                                        <label class="form-label">رقم الفرع</label>
                                        {{ form.branch_no(class="form-control") }}
                                        {% if form.branch_no.errors %}
                                            <div class="text-danger small">{{ form.branch_no.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">رقم العقد</label>
                                        {{ form.contract_no(class="form-control", readonly=not is_new) }}
                                        {% if form.contract_no.errors %}
                                            <div class="text-danger small">{{ form.contract_no.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">تسلسل العقد</label>
                                        {{ form.contract_serial(class="form-control") }}
                                        {% if form.contract_serial.errors %}
                                            <div class="text-danger small">{{ form.contract_serial.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">تاريخ العقد</label>
                                        {{ form.contract_date(class="form-control") }}
                                        {% if form.contract_date.errors %}
                                            <div class="text-danger small">{{ form.contract_date.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">الحالة</label>
                                        {{ form.status(class="form-select") }}
                                        {% if form.status.errors %}
                                            <div class="text-danger small">{{ form.status.errors[0] }}</div>
                                        {% endif %}
                                    </div>

                                    <!-- الصف الثاني -->
                                    <div class="col-md-3">
                                        <label class="form-label">تاريخ البداية</label>
                                        {{ form.contract_from_date(class="form-control") }}
                                        {% if form.contract_from_date.errors %}
                                            <div class="text-danger small">{{ form.contract_from_date.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">تاريخ النهاية</label>
                                        {{ form.contract_to_date(class="form-control") }}
                                        {% if form.contract_to_date.errors %}
                                            <div class="text-danger small">{{ form.contract_to_date.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">المورد</label>
                                        {{ form.supplier_id(class="form-select", onchange="loadSupplierData()") }}
                                        {% if form.supplier_id.errors %}
                                            <div class="text-danger small">{{ form.supplier_id.errors[0] }}</div>
                                        {% endif %}
                                    </div>

                                    <!-- الصف الثالث -->
                                    <div class="col-md-3">
                                        <label class="form-label">رقم المورد</label>
                                        {{ form.vendor_code(class="form-control") }}
                                        {% if form.vendor_code.errors %}
                                            <div class="text-danger small">{{ form.vendor_code.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-9">
                                        <label class="form-label">اسم المورد</label>
                                        {{ form.vendor_name(class="form-control") }}
                                        {% if form.vendor_name.errors %}
                                            <div class="text-danger small">{{ form.vendor_name.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- التبويب الثاني: المعلومات المالية -->
                            <div class="tab-pane fade" id="financial" role="tabpanel">
                                <div class="row g-3">
                                    <!-- الصف الأول -->
                                    <div class="col-md-3">
                                        <label class="form-label">العملة</label>
                                        {{ form.currency_code(class="form-select") }}
                                        {% if form.currency_code.errors %}
                                            <div class="text-danger small">{{ form.currency_code.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">سعر الصرف</label>
                                        {{ form.contract_rate(class="form-control") }}
                                        {% if form.contract_rate.errors %}
                                            <div class="text-danger small">{{ form.contract_rate.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">نوع العقد</label>
                                        {{ form.contract_type(class="form-select") }}
                                        {% if form.contract_type.errors %}
                                            <div class="text-danger small">{{ form.contract_type.errors[0] }}</div>
                                        {% endif %}
                                    </div>

                                    <!-- الصف الثاني -->
                                    <div class="col-md-4">
                                        <label class="form-label">مبلغ العقد</label>
                                        {{ form.contract_amount(class="form-control", onchange="calculateNetAmount()") }}
                                        {% if form.contract_amount.errors %}
                                            <div class="text-danger small">{{ form.contract_amount.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">مبلغ الخصم</label>
                                        {{ form.discount_amount(class="form-control", onchange="calculateNetAmount()") }}
                                        {% if form.discount_amount.errors %}
                                            <div class="text-danger small">{{ form.discount_amount.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">صافي المبلغ</label>
                                        {{ form.net_amount(class="form-control", readonly=true) }}
                                        {% if form.net_amount.errors %}
                                            <div class="text-danger small">{{ form.net_amount.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- التبويب الثالث: معلومات إضافية -->
                            <div class="tab-pane fade" id="additional" role="tabpanel">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">رقم المرجع</label>
                                        {{ form.reference_no(class="form-control") }}
                                        {% if form.reference_no.errors %}
                                            <div class="text-danger small">{{ form.reference_no.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">وصف العقد</label>
                                        {{ form.contract_desc(class="form-control", rows="4") }}
                                        {% if form.contract_desc.errors %}
                                            <div class="text-danger small">{{ form.contract_desc.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">ملاحظات</label>
                                        {{ form.contract_note(class="form-control", rows="4") }}
                                        {% if form.contract_note.errors %}
                                            <div class="text-danger small">{{ form.contract_note.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم التفاصيل -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-list me-1"></i>تفاصيل العقد
                            </h6>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="addDetailRow()">
                                <i class="fas fa-plus me-1"></i>إضافة سطر
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-sm mb-0" id="detailsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th width="40">م</th>
                                        <th width="100">رقم الصنف</th>
                                        <th width="200">اسم الصنف</th>
                                        <th width="60">الوحدة</th>
                                        <th width="80">العبوة</th>
                                        <th width="100">تاريخ الإنتاج</th>
                                        <th width="100">تاريخ الانتهاء</th>
                                        <th width="80">الكمية</th>
                                        <th width="80">السعر</th>
                                        <th width="100">الكمية المجانية</th>
                                        <th width="80">الخصم %</th>
                                        <th width="100">المجموع</th>
                                        <th width="150">البيان</th>
                                        <th width="60">إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="detailsTableBody">
                                    {% for detail_form in form.details %}
                                    <tr class="detail-row">
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            {{ detail_form.item_code(class="form-control form-control-sm") }}
                                            {{ detail_form.line_no() }}
                                        </td>
                                        <td>{{ detail_form.item_name(class="form-control form-control-sm") }}</td>
                                        <td>{{ detail_form.unit_code(class="form-control form-control-sm") }}</td>
                                        <td>{{ detail_form.pack_size(class="form-control form-control-sm") }}</td>
                                        <td>{{ detail_form.production_date(class="form-control form-control-sm") }}</td>
                                        <td>{{ detail_form.expiry_date(class="form-control form-control-sm") }}</td>
                                        <td>{{ detail_form.quantity(class="form-control form-control-sm", onchange="calculateLineTotal(this)") }}</td>
                                        <td>{{ detail_form.unit_price(class="form-control form-control-sm", onchange="calculateLineTotal(this)") }}</td>
                                        <td>{{ detail_form.free_quantity(class="form-control form-control-sm") }}</td>
                                        <td>{{ detail_form.discount_percent(class="form-control form-control-sm", onchange="calculateLineTotal(this)") }}</td>
                                        <td>{{ detail_form.line_total(class="form-control form-control-sm", readonly=true) }}</td>
                                        <td>{{ detail_form.line_description(class="form-control form-control-sm") }}</td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="removeDetailRow(this)" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
// حساب صافي المبلغ
function calculateNetAmount() {
    const contractAmount = parseFloat(document.getElementById('contract_amount').value) || 0;
    const discountAmount = parseFloat(document.getElementById('discount_amount').value) || 0;
    const netAmount = contractAmount - discountAmount;
    document.getElementById('net_amount').value = netAmount.toFixed(2);
}

// حساب إجمالي السطر
function calculateLineTotal(element) {
    const row = element.closest('tr');
    const quantity = parseFloat(row.querySelector('[name$="quantity"]').value) || 0;
    const unitPrice = parseFloat(row.querySelector('[name$="unit_price"]').value) || 0;
    const discountPercent = parseFloat(row.querySelector('[name$="discount_percent"]').value) || 0;
    
    let lineTotal = quantity * unitPrice;
    if (discountPercent > 0) {
        lineTotal = lineTotal * (1 - discountPercent / 100);
    }
    
    row.querySelector('[name$="line_total"]').value = lineTotal.toFixed(2);
}

// إضافة سطر جديد
function addDetailRow() {
    // سيتم تنفيذ هذه الوظيفة لاحقاً
    alert('سيتم إضافة هذه الوظيفة قريباً');
}

// حذف سطر
function removeDetailRow(button) {
    if (confirm('هل أنت متأكد من حذف هذا السطر؟')) {
        button.closest('tr').remove();
        updateRowNumbers();
    }
}

// تحديث أرقام الصفوف
function updateRowNumbers() {
    const rows = document.querySelectorAll('#detailsTableBody tr');
    rows.forEach((row, index) => {
        row.querySelector('td:first-child').textContent = index + 1;
    });
}

// تحميل بيانات المورد
function loadSupplierData() {
    const supplierId = document.getElementById('supplier_id').value;
    if (supplierId && supplierId !== '0') {
        fetch(`/purchase-contracts/api/suppliers`)
            .then(response => response.json())
            .then(suppliers => {
                const supplier = suppliers.find(s => s.id == supplierId);
                if (supplier) {
                    document.getElementById('vendor_code').value = supplier.code;
                    document.getElementById('vendor_name').value = supplier.name_ar;
                }
            });
    }
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    calculateNetAmount();
});
</script>
{% endblock %}
