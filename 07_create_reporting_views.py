#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إنشاء Views التقارير للنظام المحاسبي الموحد
Create Reporting Views for Unified Accounting System
"""

import sys
import os
sys.path.insert(0, os.getcwd())

from oracle_manager import OracleManager

def create_current_balances_view():
    """إنشاء View الأرصدة الحالية"""
    
    oracle = OracleManager()
    
    print("📊 إنشاء View الأرصدة الحالية (V_CURR_BAL)...")
    print("=" * 70)
    
    view_sql = """
    CREATE OR REPLACE VIEW V_CURR_BAL AS
    SELECT 
        bt.entity_type_code as ent_type,
        bt.entity_id as ent_id,
        bt.currency_code as curr,
        bt.branch_id as branch,
        SUM(bt.BAL) as curr_bal,
        SUM(bt.BAL_F) as curr_bal_base,
        COUNT(*) as txn_count,
        MAX(bt.document_date) as last_txn_date,
        MAX(bt.document_number) as last_doc_no
    FROM BALANCE_TRANSACTIONS bt
    WHERE bt.status = 'POSTED'
    GROUP BY bt.entity_type_code, bt.entity_id, bt.currency_code, bt.branch_id
    HAVING SUM(bt.BAL) != 0 OR COUNT(*) > 0
    """
    
    try:
        oracle.execute_update(view_sql)
        print("✅ تم إنشاء V_CURR_BAL بنجاح!")
        
        # إضافة تعليق على الـ View
        comment_sql = """
        COMMENT ON TABLE V_CURR_BAL IS 'Current balances view - الأرصدة الحالية'
        """
        oracle.execute_update(comment_sql)
        
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء V_CURR_BAL: {str(e)}")
        return False

def create_monthly_balances_view():
    """إنشاء View الأرصدة الشهرية"""
    
    oracle = OracleManager()
    
    print("\n📊 إنشاء View الأرصدة الشهرية (V_MONTH_BAL)...")
    print("=" * 70)
    
    view_sql = """
    CREATE OR REPLACE VIEW V_MONTH_BAL AS
    SELECT 
        bt.entity_type_code as ent_type,
        bt.entity_id as ent_id,
        bt.currency_code as curr,
        bt.branch_id as branch,
        bt.year_no as year_no,
        bt.month_no as month_no,
        SUM(bt.BAL) as month_bal,
        SUM(bt.BAL_F) as month_bal_base,
        COUNT(*) as txn_count,
        SUM(CASE WHEN bt.BAL > 0 THEN bt.BAL ELSE 0 END) as total_dr,
        SUM(CASE WHEN bt.BAL < 0 THEN ABS(bt.BAL) ELSE 0 END) as total_cr,
        MIN(bt.document_date) as first_txn_date,
        MAX(bt.document_date) as last_txn_date
    FROM BALANCE_TRANSACTIONS bt
    WHERE bt.status = 'POSTED'
    GROUP BY bt.entity_type_code, bt.entity_id, bt.currency_code, 
             bt.branch_id, bt.year_no, bt.month_no
    """
    
    try:
        oracle.execute_update(view_sql)
        print("✅ تم إنشاء V_MONTH_BAL بنجاح!")
        
        # إضافة تعليق على الـ View
        comment_sql = """
        COMMENT ON TABLE V_MONTH_BAL IS 'Monthly balances view - الأرصدة الشهرية'
        """
        oracle.execute_update(comment_sql)
        
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء V_MONTH_BAL: {str(e)}")
        return False

def create_entity_summary_view():
    """إنشاء View ملخص الكيانات"""
    
    oracle = OracleManager()
    
    print("\n📊 إنشاء View ملخص الكيانات (V_ENT_SUM)...")
    print("=" * 70)
    
    view_sql = """
    CREATE OR REPLACE VIEW V_ENT_SUM AS
    SELECT 
        bt.entity_type_code as ent_type,
        et.entity_name_ar as ent_name_ar,
        et.entity_name_en as ent_name_en,
        bt.currency_code as curr,
        bt.branch_id as branch,
        COUNT(DISTINCT bt.entity_id) as entity_count,
        COUNT(*) as total_txn,
        SUM(bt.BAL) as total_bal,
        SUM(bt.BAL_F) as total_bal_base,
        SUM(CASE WHEN bt.BAL > 0 THEN bt.BAL ELSE 0 END) as total_dr_bal,
        SUM(CASE WHEN bt.BAL < 0 THEN ABS(bt.BAL) ELSE 0 END) as total_cr_bal,
        AVG(bt.BAL) as avg_bal,
        MIN(bt.document_date) as first_txn_date,
        MAX(bt.document_date) as last_txn_date
    FROM BALANCE_TRANSACTIONS bt
    LEFT JOIN ENTITY_TYPES et ON bt.entity_type_code = et.entity_type_code
    WHERE bt.status = 'POSTED'
    GROUP BY bt.entity_type_code, et.entity_name_ar, et.entity_name_en,
             bt.currency_code, bt.branch_id
    """
    
    try:
        oracle.execute_update(view_sql)
        print("✅ تم إنشاء V_ENT_SUM بنجاح!")
        
        # إضافة تعليق على الـ View
        comment_sql = """
        COMMENT ON TABLE V_ENT_SUM IS 'Entity summary view - ملخص الكيانات'
        """
        oracle.execute_update(comment_sql)
        
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء V_ENT_SUM: {str(e)}")
        return False

def create_transaction_history_view():
    """إنشاء View تاريخ المعاملات"""
    
    oracle = OracleManager()
    
    print("\n📊 إنشاء View تاريخ المعاملات (V_TXN_HIST)...")
    print("=" * 70)
    
    view_sql = """
    CREATE OR REPLACE VIEW V_TXN_HIST AS
    SELECT 
        bt.id,
        bt.entity_type_code as ent_type,
        bt.entity_id as ent_id,
        bt.document_type_code as doc_type,
        bt.document_number as doc_no,
        bt.document_date as doc_date,
        bt.currency_code as curr,
        bt.debit_amount as dr_amt,
        bt.credit_amount as cr_amt,
        bt.BAL as balance,
        bt.BAL_F as balance_base,
        bt.exchange_rate as rate,
        bt.description as desc_text,
        bt.reference_number as ref_no,
        bt.status,
        bt.branch_id as branch,
        bt.year_no,
        bt.month_no,
        bt.created_date,
        bt.created_by,
        -- حساب الرصيد التراكمي
        SUM(bt.BAL) OVER (
            PARTITION BY bt.entity_type_code, bt.entity_id, bt.currency_code, bt.branch_id 
            ORDER BY bt.document_date, bt.id
            ROWS UNBOUNDED PRECEDING
        ) as running_bal
    FROM BALANCE_TRANSACTIONS bt
    WHERE bt.status = 'POSTED'
    """
    
    try:
        oracle.execute_update(view_sql)
        print("✅ تم إنشاء V_TXN_HIST بنجاح!")
        
        # إضافة تعليق على الـ View
        comment_sql = """
        COMMENT ON TABLE V_TXN_HIST IS 'Transaction history view with running balance - تاريخ المعاملات مع الرصيد التراكمي'
        """
        oracle.execute_update(comment_sql)
        
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء V_TXN_HIST: {str(e)}")
        return False

def create_branch_summary_view():
    """إنشاء View ملخص الفروع"""
    
    oracle = OracleManager()
    
    print("\n📊 إنشاء View ملخص الفروع (V_BRANCH_SUM)...")
    print("=" * 70)
    
    view_sql = """
    CREATE OR REPLACE VIEW V_BRANCH_SUM AS
    SELECT 
        bt.branch_id as branch,
        bt.currency_code as curr,
        COUNT(DISTINCT bt.entity_type_code) as entity_types,
        COUNT(DISTINCT CONCAT(bt.entity_type_code, bt.entity_id)) as total_entities,
        COUNT(*) as total_txn,
        SUM(bt.BAL) as total_bal,
        SUM(bt.BAL_F) as total_bal_base,
        SUM(CASE WHEN bt.BAL > 0 THEN bt.BAL ELSE 0 END) as total_dr_bal,
        SUM(CASE WHEN bt.BAL < 0 THEN ABS(bt.BAL) ELSE 0 END) as total_cr_bal,
        COUNT(DISTINCT bt.year_no) as active_years,
        MIN(bt.document_date) as first_txn_date,
        MAX(bt.document_date) as last_txn_date
    FROM BALANCE_TRANSACTIONS bt
    WHERE bt.status = 'POSTED'
    GROUP BY bt.branch_id, bt.currency_code
    """
    
    try:
        oracle.execute_update(view_sql)
        print("✅ تم إنشاء V_BRANCH_SUM بنجاح!")
        
        # إضافة تعليق على الـ View
        comment_sql = """
        COMMENT ON TABLE V_BRANCH_SUM IS 'Branch summary view - ملخص الفروع'
        """
        oracle.execute_update(comment_sql)
        
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء V_BRANCH_SUM: {str(e)}")
        return False

def test_views():
    """اختبار الـ Views المنشأة"""
    
    oracle = OracleManager()
    
    print("\n🧪 اختبار الـ Views المنشأة...")
    print("=" * 70)
    
    view_tests = [
        {
            "name": "V_CURR_BAL - الأرصدة الحالية",
            "query": "SELECT ent_type, COUNT(*) as entities, SUM(curr_bal) as total_bal FROM V_CURR_BAL GROUP BY ent_type ORDER BY ent_type"
        },
        {
            "name": "V_MONTH_BAL - الأرصدة الشهرية",
            "query": "SELECT year_no, month_no, COUNT(*) as records FROM V_MONTH_BAL GROUP BY year_no, month_no ORDER BY year_no, month_no"
        },
        {
            "name": "V_ENT_SUM - ملخص الكيانات",
            "query": "SELECT ent_type, entity_count, total_txn, total_bal FROM V_ENT_SUM ORDER BY ent_type"
        },
        {
            "name": "V_TXN_HIST - تاريخ المعاملات",
            "query": "SELECT ent_type, COUNT(*) as txn_count FROM V_TXN_HIST GROUP BY ent_type ORDER BY ent_type"
        },
        {
            "name": "V_BRANCH_SUM - ملخص الفروع",
            "query": "SELECT branch, curr, entity_types, total_entities, total_bal FROM V_BRANCH_SUM ORDER BY branch, curr"
        }
    ]
    
    successful_tests = 0
    
    for test in view_tests:
        try:
            print(f"\n   🧪 اختبار {test['name']}:")
            
            result = oracle.execute_query(test["query"])
            if result:
                print(f"      ✅ نجح الاختبار - عدد النتائج: {len(result)}")
                
                # عرض عينة من النتائج
                if len(result) > 0:
                    print(f"      عينة من النتائج:")
                    for i, row in enumerate(result[:3]):  # أول 3 نتائج
                        print(f"        {i+1}. {row}")
                    if len(result) > 3:
                        print(f"        ... و {len(result) - 3} نتيجة أخرى")
                
                successful_tests += 1
            else:
                print(f"      ⚠️ لا توجد نتائج")
                successful_tests += 1
                
        except Exception as e:
            print(f"      ❌ فشل الاختبار: {str(e)}")
    
    print(f"\nملخص اختبارات الـ Views:")
    print(f"   نجح: {successful_tests}/{len(view_tests)}")
    
    return successful_tests == len(view_tests)

def verify_views():
    """التحقق من الـ Views المنشأة"""
    
    oracle = OracleManager()
    
    print("\n✅ التحقق من الـ Views المنشأة...")
    print("=" * 70)
    
    # فحص وجود الـ Views
    views_query = """
    SELECT view_name, text_length, read_only
    FROM user_views
    WHERE view_name LIKE 'V_%'
    ORDER BY view_name
    """
    
    result = oracle.execute_query(views_query)
    if result:
        print("الـ Views المنشأة:")
        for row in result:
            readonly_icon = "🔒" if row[2] == "Y" else "📝"
            print(f"   ✅ {row[0]}: {row[1]} حرف {readonly_icon}")
    
    # فحص التعليقات
    comments_query = """
    SELECT table_name, comments
    FROM user_tab_comments
    WHERE table_name LIKE 'V_%'
    AND comments IS NOT NULL
    ORDER BY table_name
    """
    
    comments = oracle.execute_query(comments_query)
    if comments:
        print("\nتعليقات الـ Views:")
        for row in comments:
            print(f"   📝 {row[0]}: {row[1]}")

def create_view_usage_examples():
    """إنشاء أمثلة على استخدام الـ Views"""
    
    print("\n💡 أمثلة على استخدام الـ Views:")
    print("=" * 70)
    
    examples = [
        {
            "view": "V_CURR_BAL",
            "purpose": "الأرصدة الحالية",
            "example": "SELECT * FROM V_CURR_BAL WHERE ent_type = 'SUPPLIER' AND curr = 'USD'"
        },
        {
            "view": "V_MONTH_BAL",
            "purpose": "التقارير الشهرية",
            "example": "SELECT * FROM V_MONTH_BAL WHERE year_no = 2025 AND month_no = 9"
        },
        {
            "view": "V_ENT_SUM",
            "purpose": "ملخص أنواع الكيانات",
            "example": "SELECT ent_type, entity_count, total_bal FROM V_ENT_SUM ORDER BY total_bal DESC"
        },
        {
            "view": "V_TXN_HIST",
            "purpose": "تاريخ المعاملات مع الرصيد التراكمي",
            "example": "SELECT doc_no, balance, running_bal FROM V_TXN_HIST WHERE ent_type = 'SUPPLIER' AND ent_id = 1"
        },
        {
            "view": "V_BRANCH_SUM",
            "purpose": "ملخص الفروع",
            "example": "SELECT branch, total_entities, total_bal FROM V_BRANCH_SUM ORDER BY total_bal DESC"
        }
    ]
    
    for example in examples:
        print(f"\n   🎯 {example['view']} - {example['purpose']}:")
        print(f"      {example['example']}")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء إنشاء Views التقارير")
    print("=" * 80)
    
    try:
        views_created = 0
        
        # 1. إنشاء View الأرصدة الحالية
        if create_current_balances_view():
            views_created += 1
        
        # 2. إنشاء View الأرصدة الشهرية
        if create_monthly_balances_view():
            views_created += 1
        
        # 3. إنشاء View ملخص الكيانات
        if create_entity_summary_view():
            views_created += 1
        
        # 4. إنشاء View تاريخ المعاملات
        if create_transaction_history_view():
            views_created += 1
        
        # 5. إنشاء View ملخص الفروع
        if create_branch_summary_view():
            views_created += 1
        
        if views_created > 0:
            # 6. التحقق من النتائج
            verify_views()
            
            # 7. اختبار الـ Views
            test_views()
            
            # 8. أمثلة الاستخدام
            create_view_usage_examples()
            
            print(f"\n🎉 تم إكمال إنشاء Views التقارير بنجاح!")
            print(f"✅ تم إنشاء {views_created} Views")
            print("✅ المهمة wPB7M6Lbfxw4VZ9PdLSRQ9 مكتملة!")
            
            return True
        else:
            print("\n❌ لم يتم إنشاء أي Views")
            return False
            
    except Exception as e:
        print(f"\n❌ خطأ عام: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n" + "=" * 80)
        print("🏁 انتهى إنشاء Views التقارير بنجاح!")
    else:
        print("\n" + "=" * 80)
        print("🚫 فشل في إنشاء Views - يرجى مراجعة الأخطاء")
