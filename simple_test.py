#!/usr/bin/env python3
"""
اختبار مبسط لـ API إلغاء الحوالة
Simple Test for Cancel Transfer API
"""

import requests
import urllib3

# تعطيل تحذيرات SSL
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_simple():
    """اختبار مبسط"""
    
    print("🧪 اختبار مبسط لـ API إلغاء الحوالة")
    print("=" * 50)
    
    base_url = "https://localhost:5000"
    
    try:
        # اختبار الصفحة الرئيسية
        print("1. اختبار الصفحة الرئيسية:")
        response = requests.get(f"{base_url}/", verify=False, timeout=10)
        print(f"   📡 الاستجابة: {response.status_code}")
        print(f"   📄 نوع المحتوى: {response.headers.get('content-type', 'غير محدد')}")
        print(f"   📏 حجم المحتوى: {len(response.text)} حرف")
        
        # اختبار API إلغاء الحوالة
        print("\n2. اختبار API إلغاء الحوالة:")
        response = requests.get(f"{base_url}/transfers/accounting/check-cancellation/99999", verify=False, timeout=10)
        print(f"   📡 الاستجابة: {response.status_code}")
        print(f"   📄 نوع المحتوى: {response.headers.get('content-type', 'غير محدد')}")
        print(f"   📏 حجم المحتوى: {len(response.text)} حرف")
        print(f"   📋 المحتوى: {response.text[:200]}...")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"   ✅ JSON صحيح: {data}")
            except:
                print(f"   ❌ ليس JSON صحيح")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("   ❌ خطأ في الاتصال - Flask غير مشغل")
        return False
    except Exception as e:
        print(f"   ❌ خطأ: {str(e)}")
        return False

if __name__ == "__main__":
    test_simple()
