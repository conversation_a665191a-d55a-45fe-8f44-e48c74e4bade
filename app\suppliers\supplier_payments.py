# -*- coding: utf-8 -*-
"""
نظام مدفوعات الموردين المتكامل مع نظام الحوالات
Integrated Supplier Payments with Transfer System
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from app.database_manager import DatabaseManager
import logging

# إنشاء Blueprint
supplier_payments_bp = Blueprint('supplier_payments', __name__, url_prefix='/suppliers/payments')

logger = logging.getLogger(__name__)

@supplier_payments_bp.route('/')
@login_required
def index():
    """صفحة إدارة مدفوعات الموردين"""
    return render_template('suppliers/payments/index.html')

@supplier_payments_bp.route('/create/<int:supplier_id>')
@login_required
def create_payment(supplier_id):
    """إنشاء دفعة جديدة لمورد"""
    try:
        db = DatabaseManager()
        
        # الحصول على بيانات المورد
        supplier_query = """
        SELECT id, code, name_ar, name_en, default_currency, phone, email
        FROM SUPPLIERS 
        WHERE id = :1 AND is_active = 1
        """
        supplier_data = db.execute_query(supplier_query, [supplier_id])
        
        if not supplier_data:
            flash('المورد غير موجود', 'error')
            return redirect(url_for('suppliers.index'))
        
        supplier = supplier_data[0]
        
        # الحصول على الفواتير المستحقة
        outstanding_invoices_query = """
        SELECT 
            st.transaction_id,
            st.reference_number,
            st.transaction_date,
            st.due_date,
            st.currency_code,
            st.original_amount,
            st.debit_amount - st.credit_amount as outstanding_amount,
            CASE 
                WHEN st.due_date < SYSDATE THEN 'متأخر'
                WHEN st.due_date <= SYSDATE + 7 THEN 'مستحق قريباً'
                ELSE 'مستحق'
            END as status
        FROM SUPPLIER_TRANSACTIONS st
        WHERE st.supplier_id = :1 
        AND st.transaction_type = 'INVOICE'
        AND st.status = 'ACTIVE'
        AND (st.debit_amount - st.credit_amount) > 0
        ORDER BY st.due_date ASC
        """
        outstanding_invoices = db.execute_query(outstanding_invoices_query, [supplier_id])
        
        # الحصول على أرصدة المورد بالعملات المختلفة
        balances_query = """
        SELECT 
            sb.currency_code,
            sb.current_balance,
            c.symbol,
            c.name_ar
        FROM SUPPLIER_BALANCES sb
        JOIN CURRENCIES c ON sb.currency_code = c.code
        WHERE sb.supplier_id = :1 AND sb.current_balance != 0
        ORDER BY c.is_base_currency DESC, sb.current_balance DESC
        """
        balances = db.execute_query(balances_query, [supplier_id])
        
        # الحصول على الصرافين والبنوك المتاحة
        money_changers_query = """
        SELECT id, name_ar, name_en, type, supported_currencies
        FROM MONEY_CHANGERS_BANKS 
        WHERE is_active = 1
        ORDER BY type, name_ar
        """
        money_changers = db.execute_query(money_changers_query)
        
        # الحصول على العملات المتاحة
        currencies_query = """
        SELECT code, name_ar, symbol, is_base_currency
        FROM CURRENCIES 
        WHERE is_active = 1
        ORDER BY is_base_currency DESC, name_ar
        """
        currencies = db.execute_query(currencies_query)
        
        return render_template('suppliers/payments/create.html',
                             supplier=supplier,
                             outstanding_invoices=outstanding_invoices,
                             balances=balances,
                             money_changers=money_changers,
                             currencies=currencies)
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء صفحة الدفع: {e}")
        flash('حدث خطأ في تحميل البيانات', 'error')
        return redirect(url_for('suppliers.index'))

@supplier_payments_bp.route('/api/create-payment', methods=['POST'])
@login_required
def api_create_payment():
    """API لإنشاء طلب دفع مورد عبر الحوالات"""
    try:
        db = DatabaseManager()
        data = request.get_json()
        
        # التحقق من البيانات المطلوبة
        required_fields = ['supplier_id', 'amount', 'currency_code', 'money_changer_id', 'payment_purpose']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'success': False, 'message': f'الحقل {field} مطلوب'}), 400
        
        supplier_id = data['supplier_id']
        amount = float(data['amount'])
        currency_code = data['currency_code']
        money_changer_id = data['money_changer_id']
        payment_purpose = data['payment_purpose']
        invoice_numbers = data.get('invoice_numbers', [])
        notes = data.get('notes', '')
        
        # التحقق من وجود المورد
        supplier_check = db.execute_query("SELECT id, name_ar FROM SUPPLIERS WHERE id = :1", [supplier_id])
        if not supplier_check:
            return jsonify({'success': False, 'message': 'المورد غير موجود'}), 404
        
        supplier_name = supplier_check[0][1]
        
        # إنشاء مستفيد مؤقت للمورد في نظام الحوالات
        beneficiary_query = """
        INSERT INTO BENEFICIARIES (
            full_name, phone, email, bank_name, account_number, 
            country, city, address, beneficiary_type, supplier_id,
            created_at, created_by
        ) VALUES (
            :1, :2, :3, :4, :5, :6, :7, :8, 'SUPPLIER', :9, CURRENT_TIMESTAMP, :10
        )
        """
        
        # الحصول على بيانات المورد للمستفيد
        supplier_details = db.execute_query("""
            SELECT name_ar, phone, email, bank_name, bank_account, city, address
            FROM SUPPLIERS WHERE id = :1
        """, [supplier_id])[0]
        
        beneficiary_id = db.get_next_sequence_value('BENEFICIARIES_SEQ')
        db.execute_update(beneficiary_query, [
            supplier_details[0],  # full_name
            supplier_details[1],  # phone
            supplier_details[2],  # email
            supplier_details[3] or 'غير محدد',  # bank_name
            supplier_details[4] or 'غير محدد',  # account_number
            'السعودية',  # country
            supplier_details[5] or 'غير محدد',  # city
            supplier_details[6] or 'غير محدد',  # address
            supplier_id,
            current_user.id
        ])
        
        # إنشاء طلب حوالة باستخدام النظام الذكي
        try:
            # استخدام الدالة الذكية الجديدة
            number_result = db.execute_query("SELECT GENERATE_SMART_TR_NUMBER() FROM DUAL")
            request_number = number_result[0][0] if number_result and number_result[0][0] else None

            if not request_number:
                # في حالة فشل النظام الذكي، استخدم نمط مخصص للموردين
                from datetime import datetime
                current_year = datetime.now().year
                request_number = f"SP-{current_year}-{supplier_id:04d}"
        except Exception as e:
            logger.warning(f"فشل في استخدام النظام الذكي: {e}")
            # استخدام نمط مخصص للموردين
            from datetime import datetime
            current_year = datetime.now().year
            request_number = f"SP-{current_year}-{supplier_id:04d}"
        
        transfer_request_query = """
        INSERT INTO TRANSFER_REQUESTS (
            request_number, beneficiary_id, amount, currency, purpose, notes,
            branch_id, status, created_by, updated_by, total_amount, 
            delivery_method, transfer_type, money_changer_bank_id,
            supplier_id, payment_type, created_at, updated_at
        ) VALUES (
            :1, :2, :3, :4, :5, :6, 1, 'pending', :7, :8, :9, 
            'bank_transfer', 'supplier_payment', :10, :11, 'SUPPLIER_PAYMENT',
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        )
        """
        
        transfer_request_id = db.get_next_sequence_value('TRANSFER_REQUESTS_SEQ')
        db.execute_update(transfer_request_query, [
            request_number, beneficiary_id, amount, currency_code, payment_purpose, notes,
            current_user.id, current_user.id, amount, money_changer_id, supplier_id
        ])
        
        # إنشاء سجل ربط في جدول مدفوعات الموردين
        supplier_payment_query = """
        INSERT INTO SUPPLIER_PAYMENT_TRANSFERS (
            supplier_id, transfer_request_id, payment_amount, currency_code,
            payment_purpose, invoice_numbers, payment_status, created_at, created_by
        ) VALUES (
            :1, :2, :3, :4, :5, :6, 'PENDING', CURRENT_TIMESTAMP, :7
        )
        """
        
        import json
        db.execute_update(supplier_payment_query, [
            supplier_id, transfer_request_id, amount, currency_code,
            payment_purpose, json.dumps(invoice_numbers), current_user.id
        ])
        
        # إنشاء معاملة في حساب المورد
        supplier_transaction_query = """
        INSERT INTO SUPPLIER_TRANSACTIONS (
            supplier_id, transaction_type, reference_type, reference_id, reference_number,
            transaction_date, currency_code, original_amount, credit_amount,
            description, status, created_date, created_by
        ) VALUES (
            :1, 'PAYMENT_REQUEST', 'TRANSFER_REQUEST', :2, :3, CURRENT_TIMESTAMP,
            :4, :5, :6, :7, 'PENDING', CURRENT_TIMESTAMP, :8
        )
        """
        
        db.execute_update(supplier_transaction_query, [
            supplier_id, transfer_request_id, request_number, currency_code,
            amount, amount, f'طلب دفع للمورد {supplier_name} - {payment_purpose}', current_user.id
        ])
        
        return jsonify({
            'success': True,
            'message': 'تم إنشاء طلب الدفع بنجاح',
            'transfer_request_id': transfer_request_id,
            'request_number': request_number
        })
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء طلب الدفع: {e}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'}), 500

@supplier_payments_bp.route('/api/supplier-outstanding/<int:supplier_id>')
@login_required
def api_supplier_outstanding(supplier_id):
    """API للحصول على الفواتير المستحقة للمورد"""
    try:
        db = DatabaseManager()
        
        query = """
        SELECT 
            st.transaction_id,
            st.reference_number,
            st.transaction_date,
            st.due_date,
            st.currency_code,
            st.original_amount,
            st.debit_amount - st.credit_amount as outstanding_amount,
            st.description,
            CASE 
                WHEN st.due_date < SYSDATE THEN 'متأخر'
                WHEN st.due_date <= SYSDATE + 7 THEN 'مستحق قريباً'
                ELSE 'مستحق'
            END as status,
            TRUNC(SYSDATE - st.due_date) as days_overdue
        FROM SUPPLIER_TRANSACTIONS st
        WHERE st.supplier_id = :1 
        AND st.transaction_type = 'INVOICE'
        AND st.status = 'ACTIVE'
        AND (st.debit_amount - st.credit_amount) > 0
        ORDER BY st.due_date ASC
        """
        
        results = db.execute_query(query, [supplier_id])
        
        outstanding_invoices = []
        for row in results:
            outstanding_invoices.append({
                'transaction_id': row[0],
                'reference_number': row[1],
                'transaction_date': row[2].strftime('%Y-%m-%d') if row[2] else None,
                'due_date': row[3].strftime('%Y-%m-%d') if row[3] else None,
                'currency_code': row[4],
                'original_amount': float(row[5]),
                'outstanding_amount': float(row[6]),
                'description': row[7],
                'status': row[8],
                'days_overdue': int(row[9]) if row[9] else 0
            })
        
        return jsonify({
            'success': True,
            'outstanding_invoices': outstanding_invoices
        })
        
    except Exception as e:
        logger.error(f"خطأ في جلب الفواتير المستحقة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500
