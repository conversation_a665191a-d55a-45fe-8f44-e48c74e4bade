<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مركز الإشعارات المتقدم - النظام المحاسبي المتقدم</title>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
            position: relative;
        }

        /* خلفية متحركة */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
            animation: backgroundMove 20s linear infinite;
        }

        @keyframes backgroundMove {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-20px) translateY(-20px); }
        }

        .notification-container {
            min-height: 100vh;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        /* رأس الصفحة */
        .page-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(30px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            z-index: 2;
        }

        .header-info h1 {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            background: linear-gradient(45deg, #fff, #f0f9ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-info p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }

        .header-stats {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .stat-item {
            text-align: center;
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.2);
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 800;
            display: block;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* شريط التصفية */
        .filter-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .filter-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 20px;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .filter-tabs {
            display: flex;
            background: #f1f5f9;
            border-radius: 15px;
            padding: 5px;
            gap: 5px;
        }

        .filter-tab {
            padding: 12px 20px;
            border-radius: 12px;
            background: transparent;
            border: none;
            font-weight: 600;
            color: #64748b;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .filter-tab.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .search-box {
            position: relative;
            min-width: 300px;
        }

        .search-input {
            width: 100%;
            padding: 12px 45px 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: white;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #94a3b8;
            font-size: 1.1rem;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            padding: 12px 20px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #64748b, #475569);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        /* قائمة الإشعارات */
        .notifications-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .notification-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .notification-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.3s ease;
        }

        .notification-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .notification-card:hover::before {
            transform: scaleX(1);
        }

        .notification-header {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .notification-icon {
            width: 50px;
            height: 50px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: white;
            margin-left: 15px;
            flex-shrink: 0;
        }

        .notification-icon.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .notification-icon.warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .notification-icon.error {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        .notification-icon.info {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-size: 1.1rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .notification-message {
            color: #64748b;
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 12px;
        }

        .notification-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 0.8rem;
            color: #94a3b8;
        }

        .notification-time {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .notification-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 6px 12px;
            border-radius: 8px;
            border: none;
            font-size: 0.8rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn.read {
            background: #e2e8f0;
            color: #475569;
        }

        .action-btn.read:hover {
            background: #cbd5e1;
        }

        .action-btn.delete {
            background: #fee2e2;
            color: #dc2626;
        }

        .action-btn.delete:hover {
            background: #fecaca;
        }

        .notification-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-new {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        .badge-read {
            background: #e2e8f0;
            color: #64748b;
        }

        /* حالة فارغة */
        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: #64748b;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #cbd5e1;
        }

        .empty-state h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #374151;
        }

        .empty-state p {
            font-size: 1rem;
            line-height: 1.6;
        }

        /* تحميل */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 50%;
            border-top-color: #667eea;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* الشريط الجانبي */
        .sidebar {
            position: fixed;
            top: 0;
            right: -400px;
            width: 400px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: -10px 0 30px rgba(0, 0, 0, 0.1);
            z-index: 10001;
            transition: right 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            overflow-y: auto;
        }

        .sidebar.active {
            right: 0;
        }

        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .sidebar-header {
            padding: 30px 25px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
            pointer-events: none;
        }

        .sidebar-title {
            font-size: 1.5rem;
            font-weight: 800;
            margin-bottom: 5px;
            position: relative;
            z-index: 2;
        }

        .sidebar-subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        .sidebar-close {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            z-index: 3;
        }

        .sidebar-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .sidebar-content {
            padding: 0;
        }

        .sidebar-section {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .sidebar-section:last-child {
            border-bottom: none;
        }

        .section-header {
            padding: 20px 25px 15px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            font-weight: 700;
            color: #1e293b;
            font-size: 0.95rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu-item {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .sidebar-menu-item:last-child {
            border-bottom: none;
        }

        .sidebar-menu-link {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: #374151;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .sidebar-menu-link::before {
            content: '';
            position: absolute;
            top: 0;
            right: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: right 0.5s;
        }

        .sidebar-menu-link:hover {
            background: rgba(102, 126, 234, 0.05);
            color: #667eea;
            transform: translateX(-5px);
        }

        .sidebar-menu-link:hover::before {
            right: 100%;
        }

        .sidebar-menu-icon {
            width: 20px;
            margin-left: 15px;
            text-align: center;
            color: #667eea;
        }

        .sidebar-menu-text {
            flex: 1;
            font-weight: 500;
        }

        .sidebar-menu-badge {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 600;
        }

        .sidebar-menu-arrow {
            color: #94a3b8;
            transition: transform 0.3s ease;
        }

        .sidebar-menu-link:hover .sidebar-menu-arrow {
            transform: translateX(-3px);
        }

        .quick-stats {
            padding: 20px 25px;
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
        }

        .quick-stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .quick-stat-item {
            background: white;
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .quick-stat-number {
            font-size: 1.5rem;
            font-weight: 800;
            color: #667eea;
            display: block;
            margin-bottom: 5px;
        }

        .quick-stat-label {
            font-size: 0.8rem;
            color: #6b7280;
            font-weight: 500;
        }

        /* استجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .notification-container {
                padding: 15px;
            }

            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .header-stats {
                justify-content: center;
                flex-wrap: wrap;
            }

            .filter-content {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                min-width: auto;
            }

            .notifications-grid {
                grid-template-columns: 1fr;
            }

            .notification-header {
                flex-direction: column;
                gap: 10px;
            }

            .sidebar {
                width: 100%;
                right: -100%;
            }

            .quick-stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div class="notification-container">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-info">
                    <h1>
                        <i class="fas fa-bell" style="margin-left: 15px;"></i>
                        مركز الإشعارات المتقدم
                    </h1>
                    <p>إدارة ومتابعة جميع إشعارات النظام في مكان واحد</p>
                </div>
                <div class="header-stats">
                    <button class="btn btn-outline-light" onclick="toggleSidebar()" style="margin-left: 20px;">
                        <i class="fas fa-bars" style="margin-left: 8px;"></i>
                        القائمة الرئيسية
                    </button>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.unread if stats else 0 }}</span>
                        <span class="stat-label">جديد</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.total if stats else 0 }}</span>
                        <span class="stat-label">إجمالي</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.read_percentage if stats else 0 }}%</span>
                        <span class="stat-label">مقروء</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- شريط التصفية -->
        <div class="filter-bar">
            <div class="filter-content">
                <div class="filter-group">
                    <div class="filter-tabs">
                        <button class="filter-tab active" data-filter="all">
                            <i class="fas fa-list" style="margin-left: 8px;"></i>
                            الكل
                        </button>
                        <button class="filter-tab" data-filter="unread">
                            <i class="fas fa-envelope" style="margin-left: 8px;"></i>
                            غير مقروء
                        </button>
                        <button class="filter-tab" data-filter="important">
                            <i class="fas fa-star" style="margin-left: 8px;"></i>
                            مهم
                        </button>
                        <button class="filter-tab" data-filter="system">
                            <i class="fas fa-cog" style="margin-left: 8px;"></i>
                            النظام
                        </button>
                    </div>
                </div>

                <div class="search-box">
                    <input type="text" class="search-input" placeholder="البحث في الإشعارات..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="createNewNotification()">
                        <i class="fas fa-plus" style="margin-left: 8px;"></i>
                        إشعار جديد
                    </button>
                    <button class="btn btn-secondary" onclick="openNotificationSettings()">
                        <i class="fas fa-cog" style="margin-left: 8px;"></i>
                        إعدادات الإشعارات
                    </button>
                    <button class="btn btn-info" onclick="markAllAsRead()">
                        <i class="fas fa-check-double" style="margin-left: 8px;"></i>
                        تحديد الكل كمقروء
                    </button>
                    <button class="btn btn-secondary" onclick="refreshNotifications()">
                        <i class="fas fa-sync-alt" style="margin-left: 8px;"></i>
                        تحديث
                    </button>
                    <button class="btn btn-danger" onclick="deleteSelected()">
                        <i class="fas fa-trash" style="margin-left: 8px;"></i>
                        حذف المحدد
                    </button>
                </div>
            </div>
        </div>

        <!-- قائمة الإشعارات -->
        <div class="notifications-grid" id="notificationsGrid">
            {% if notifications %}
                {% for notification in notifications %}
                <div class="notification-card"
                     data-type="{{ notification.type }}"
                     data-read="{{ notification.is_read|lower }}"
                     data-id="{{ notification.id }}">
                    <div class="notification-badge {{ 'badge-read' if notification.is_read else 'badge-new' }}">
                        {{ 'مقروء' if notification.is_read else 'جديد' }}
                    </div>
                    <div class="notification-header">
                        <div class="notification-icon {{ notification.type }}">
                            {% if notification.type == 'success' %}
                                <i class="fas fa-check-circle"></i>
                            {% elif notification.type == 'warning' %}
                                <i class="fas fa-exclamation-triangle"></i>
                            {% elif notification.type == 'error' %}
                                <i class="fas fa-times-circle"></i>
                            {% elif notification.type == 'system' %}
                                <i class="fas fa-server"></i>
                            {% else %}
                                <i class="fas fa-info-circle"></i>
                            {% endif %}
                        </div>
                        <div class="notification-content">
                            <div class="notification-title">{{ get_arabic_title(notification.type, notification.title) }}</div>
                            <div class="notification-message">{{ get_arabic_message(notification.type, notification.message, notification.related_id) }}</div>
                            <div class="notification-meta">
                                <div class="notification-time">
                                    <i class="fas fa-clock"></i>
                                    {{ notification.time_ago }}
                                </div>
                                <div class="notification-actions">
                                    {% if not notification.is_read %}
                                        <button class="action-btn read" onclick="markAsRead(this, {{ notification.id }})">تحديد كمقروء</button>
                                    {% endif %}
                                    <button class="action-btn delete" onclick="deleteNotification(this, {{ notification.id }})">حذف</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% endif %}
        </div>

        <!-- حالة فارغة (مخفية افتراضياً) -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="fas fa-bell-slash"></i>
            <h3>لا توجد إشعارات</h3>
            <p>لم يتم العثور على إشعارات تطابق المعايير المحددة.<br>جرب تغيير المرشحات أو البحث بكلمات مختلفة.</p>
        </div>
    </div>

    <!-- الشريط الجانبي -->
    <div class="sidebar-overlay" id="sidebarOverlay" onclick="closeSidebar()"></div>
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <button class="sidebar-close" onclick="closeSidebar()">
                <i class="fas fa-times"></i>
            </button>
            <div class="sidebar-title">النظام المحاسبي المتقدم</div>
            <div class="sidebar-subtitle">إدارة شاملة للشحنات والمخلصين</div>
        </div>

        <div class="sidebar-content">
            <!-- لوحة المعلومات الرئيسية -->
            <div class="sidebar-section">
                <div class="section-header">
                    <i class="fas fa-home" style="margin-left: 8px;"></i>
                    الصفحة الرئيسية
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="/" class="sidebar-menu-link">
                            <i class="fas fa-tachometer-alt sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">لوحة المعلومات الرئيسية</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- قسم الشحنات -->
            <div class="sidebar-section">
                <div class="section-header">
                    <i class="fas fa-ship" style="margin-left: 8px;"></i>
                    إدارة الشحنات
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="{{ url_for('shipments.release_dashboard') }}" class="sidebar-menu-link">
                            <i class="fas fa-list sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">قائمة الشحنات</span>
                            <span class="sidebar-menu-badge">156</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="/shipments/add" class="sidebar-menu-link">
                            <i class="fas fa-plus sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">إضافة شحنة جديدة</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="/shipments/tracking" class="sidebar-menu-link">
                            <i class="fas fa-map-marker-alt sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">تتبع الشحنات</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="/shipments/reports" class="sidebar-menu-link">
                            <i class="fas fa-chart-bar sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">تقارير الشحنات</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- قسم أوامر التسليم -->
            <div class="sidebar-section">
                <div class="section-header">
                    <i class="fas fa-truck" style="margin-left: 8px;"></i>
                    أوامر التسليم
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="/delivery-orders" class="sidebar-menu-link">
                            <i class="fas fa-clipboard-list sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">قائمة أوامر التسليم</span>
                            <span class="sidebar-menu-badge">89</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="/delivery-orders/create" class="sidebar-menu-link">
                            <i class="fas fa-file-plus sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">إنشاء أمر تسليم</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="/delivery-orders/pending" class="sidebar-menu-link">
                            <i class="fas fa-clock sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">الأوامر المعلقة</span>
                            <span class="sidebar-menu-badge">24</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- قسم المخلصين -->
            <div class="sidebar-section">
                <div class="section-header">
                    <i class="fas fa-users" style="margin-left: 8px;"></i>
                    إدارة المخلصين
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="/agents" class="sidebar-menu-link">
                            <i class="fas fa-user-tie sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">قائمة المخلصين</span>
                            <span class="sidebar-menu-badge">45</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="/agents/add" class="sidebar-menu-link">
                            <i class="fas fa-user-plus sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">إضافة مخلص جديد</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="/agents/performance" class="sidebar-menu-link">
                            <i class="fas fa-star sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">تقييم الأداء</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- قسم الأتمتة -->
            <div class="sidebar-section">
                <div class="section-header">
                    <i class="fas fa-robot" style="margin-left: 8px;"></i>
                    نظام الأتمتة
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="/shipments/automation-dashboard" class="sidebar-menu-link">
                            <i class="fas fa-tachometer-alt sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">لوحة الأتمتة</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="/shipments/automation/settings" class="sidebar-menu-link">
                            <i class="fas fa-cog sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">إعدادات الأتمتة</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="/shipments/add-automation-rule" class="sidebar-menu-link">
                            <i class="fas fa-plus-circle sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">إضافة قاعدة أتمتة</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- قسم الإشعارات -->
            <div class="sidebar-section">
                <div class="section-header">
                    <i class="fas fa-bell" style="margin-left: 8px;"></i>
                    الإشعارات
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="/shipments/notification-center" class="sidebar-menu-link">
                            <i class="fas fa-bell sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">مركز الإشعارات</span>
                            <span class="sidebar-menu-badge">24</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="#" class="sidebar-menu-link" onclick="openNotificationSettings()">
                            <i class="fas fa-cog sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">إعدادات الإشعارات</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- قسم التقارير -->
            <div class="sidebar-section">
                <div class="section-header">
                    <i class="fas fa-chart-line" style="margin-left: 8px;"></i>
                    التقارير والإحصائيات
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="/reports/dashboard" class="sidebar-menu-link">
                            <i class="fas fa-chart-pie sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">لوحة التحكم</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="/reports/financial" class="sidebar-menu-link">
                            <i class="fas fa-dollar-sign sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">التقارير المالية</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="/reports/performance" class="sidebar-menu-link">
                            <i class="fas fa-chart-bar sidebar-menu-icon"></i>
                            <span class="sidebar-menu-text">تقارير الأداء</span>
                            <i class="fas fa-chevron-left sidebar-menu-arrow"></i>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="quick-stats">
                <div class="quick-stats-grid">
                    <div class="quick-stat-item">
                        <span class="quick-stat-number">156</span>
                        <span class="quick-stat-label">شحنة نشطة</span>
                    </div>
                    <div class="quick-stat-item">
                        <span class="quick-stat-number">89</span>
                        <span class="quick-stat-label">أمر تسليم</span>
                    </div>
                    <div class="quick-stat-item">
                        <span class="quick-stat-number">45</span>
                        <span class="quick-stat-label">مخلص نشط</span>
                    </div>
                    <div class="quick-stat-item">
                        <span class="quick-stat-number">98%</span>
                        <span class="quick-stat-label">معدل النجاح</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // متغيرات عامة
        let currentFilter = 'all';
        let searchTerm = '';

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeNotificationCenter();
            setupEventListeners();
            animateCards();
        });

        // تهيئة مركز الإشعارات
        function initializeNotificationCenter() {
            updateStats();
            filterNotifications();
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // تبديل التصفية
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    // إزالة الحالة النشطة من جميع التبويبات
                    document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                    // إضافة الحالة النشطة للتبويب المحدد
                    this.classList.add('active');

                    currentFilter = this.dataset.filter;
                    filterNotifications();
                });
            });

            // البحث
            document.getElementById('searchInput').addEventListener('input', function() {
                searchTerm = this.value.toLowerCase();
                filterNotifications();
            });

            // تأثيرات الحركة للكروت
            document.querySelectorAll('.notification-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        }

        // تصفية الإشعارات
        function filterNotifications() {
            const cards = document.querySelectorAll('.notification-card');
            let visibleCount = 0;

            cards.forEach(card => {
                const type = card.dataset.type;
                const isRead = card.dataset.read === 'true';
                const title = card.querySelector('.notification-title').textContent.toLowerCase();
                const message = card.querySelector('.notification-message').textContent.toLowerCase();

                let shouldShow = true;

                // تصفية حسب النوع
                if (currentFilter === 'unread' && isRead) {
                    shouldShow = false;
                } else if (currentFilter === 'system' && type !== 'system') {
                    shouldShow = false;
                } else if (currentFilter === 'important' && type === 'info') {
                    shouldShow = false;
                }

                // تصفية حسب البحث
                if (searchTerm && !title.includes(searchTerm) && !message.includes(searchTerm)) {
                    shouldShow = false;
                }

                if (shouldShow) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });

            // إظهار/إخفاء حالة فارغة
            const emptyState = document.getElementById('emptyState');
            const notificationsGrid = document.getElementById('notificationsGrid');

            if (visibleCount === 0) {
                notificationsGrid.style.display = 'none';
                emptyState.style.display = 'block';
            } else {
                notificationsGrid.style.display = 'grid';
                emptyState.style.display = 'none';
            }
        }

        // تحديث الإحصائيات
        function updateStats() {
            const cards = document.querySelectorAll('.notification-card');
            const unreadCards = document.querySelectorAll('.notification-card[data-read="false"]');
            const totalCount = cards.length;
            const unreadCount = unreadCards.length;
            const readPercentage = totalCount > 0 ? Math.round(((totalCount - unreadCount) / totalCount) * 100) : 0;

            // تحديث الأرقام في الرأس
            const statItems = document.querySelectorAll('.stat-item .stat-number');
            if (statItems.length >= 3) {
                statItems[0].textContent = unreadCount;
                statItems[1].textContent = totalCount;
                statItems[2].textContent = readPercentage + '%';
            }
        }

        // تحديد إشعار كمقروء
        function markAsRead(button, notificationId) {
            const card = button.closest('.notification-card');
            const badge = card.querySelector('.notification-badge');
            const readButton = card.querySelector('.action-btn.read');

            // تأثير تحميل
            button.disabled = true;
            button.innerHTML = '<div class="loading-spinner"></div>';

            // إرسال طلب للخادم
            fetch(`/shipments/notification-center/api/mark-read/${notificationId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث البيانات
                    card.dataset.read = 'true';

                    // تحديث المظهر
                    badge.textContent = 'مقروء';
                    badge.className = 'notification-badge badge-read';

                    // إزالة زر "تحديد كمقروء"
                    if (readButton) {
                        readButton.remove();
                    }

                    // تحديث الإحصائيات
                    updateStatsFromServer();

                    // إظهار رسالة نجاح
                    showNotification(data.message, 'success');
                } else {
                    showNotification(data.message, 'error');
                    button.disabled = false;
                    button.innerHTML = 'تحديد كمقروء';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('حدث خطأ في الاتصال', 'error');
                button.disabled = false;
                button.innerHTML = 'تحديد كمقروء';
            });
        }

        // حذف إشعار
        function deleteNotification(button, notificationId) {
            const card = button.closest('.notification-card');

            if (!confirm('هل تريد حذف هذا الإشعار؟')) {
                return;
            }

            // تأثير تحميل
            button.disabled = true;
            button.innerHTML = '<div class="loading-spinner"></div>';

            // إرسال طلب للخادم
            fetch(`/shipments/notification-center/api/delete/${notificationId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تأثير الاختفاء
                    card.style.transform = 'translateX(100%)';
                    card.style.opacity = '0';

                    setTimeout(() => {
                        card.remove();
                        updateStatsFromServer();
                        filterNotifications();
                    }, 300);

                    showNotification(data.message, 'success');
                } else {
                    showNotification(data.message, 'error');
                    button.disabled = false;
                    button.innerHTML = 'حذف';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('حدث خطأ في الاتصال', 'error');
                button.disabled = false;
                button.innerHTML = 'حذف';
            });
        }

        // تحديد جميع الإشعارات كمقروءة
        function markAllAsRead() {
            const unreadCards = document.querySelectorAll('.notification-card[data-read="false"]');

            if (unreadCards.length === 0) {
                showNotification('لا توجد إشعارات غير مقروءة', 'info');
                return;
            }

            // إرسال طلب للخادم
            fetch('/shipments/notification-center/api/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    unreadCards.forEach(card => {
                        const badge = card.querySelector('.notification-badge');
                        const readButton = card.querySelector('.action-btn.read');

                        card.dataset.read = 'true';
                        badge.textContent = 'مقروء';
                        badge.className = 'notification-badge badge-read';

                        if (readButton) {
                            readButton.remove();
                        }
                    });

                    updateStatsFromServer();
                    showNotification(data.message, 'success');
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('حدث خطأ في الاتصال', 'error');
            });
        }

        // تحديث الإشعارات
        function refreshNotifications() {
            const button = event.target;
            const originalText = button.innerHTML;

            // تأثير التحميل
            button.innerHTML = '<div class="loading-spinner"></div> جاري التحديث...';
            button.disabled = true;

            // إعادة تحميل الصفحة لجلب أحدث البيانات
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }

        // تحديث الإحصائيات من الخادم
        function updateStatsFromServer() {
            // إعادة حساب الإحصائيات من العناصر الموجودة
            const cards = document.querySelectorAll('.notification-card');
            const unreadCards = document.querySelectorAll('.notification-card[data-read="false"]');
            const totalCount = cards.length;
            const unreadCount = unreadCards.length;
            const readPercentage = totalCount > 0 ? Math.round(((totalCount - unreadCount) / totalCount) * 100) : 0;

            // تحديث الأرقام في الرأس
            const statItems = document.querySelectorAll('.stat-item .stat-number');
            if (statItems.length >= 3) {
                statItems[0].textContent = unreadCount;
                statItems[1].textContent = totalCount;
                statItems[2].textContent = readPercentage + '%';
            }
        }

        // حذف الإشعارات المحددة
        function deleteSelected() {
            const readCards = document.querySelectorAll('.notification-card[data-read="true"]');

            if (readCards.length === 0) {
                showNotification('لا توجد إشعارات مقروءة للحذف', 'warning');
                return;
            }

            if (confirm(`هل تريد حذف ${readCards.length} إشعار مقروء؟`)) {
                readCards.forEach((card, index) => {
                    setTimeout(() => {
                        card.style.transform = 'translateX(100%)';
                        card.style.opacity = '0';

                        setTimeout(() => {
                            card.remove();
                            if (index === readCards.length - 1) {
                                updateStats();
                                filterNotifications();
                            }
                        }, 300);
                    }, index * 100);
                });

                showNotification(`تم حذف ${readCards.length} إشعار`, 'success');
            }
        }

        // إنشاء إشعار جديد
        function createNewNotification() {
            showNewNotificationModal();
        }

        // فتح إعدادات الإشعارات
        function openNotificationSettings() {
            showNotificationSettingsModal();
        }

        // إظهار نافذة إنشاء إشعار جديد
        function showNewNotificationModal() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(10px);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 20px;
                    padding: 30px;
                    max-width: 500px;
                    width: 90%;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
                    transform: scale(0.9);
                    transition: transform 0.3s ease;
                ">
                    <h3 style="margin-bottom: 20px; color: #1e293b; font-weight: 700;">
                        <i class="fas fa-plus-circle" style="margin-left: 10px; color: #667eea;"></i>
                        إنشاء إشعار جديد
                    </h3>

                    <form id="newNotificationForm">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #374151;">نوع الإشعار</label>
                            <select id="notificationType" style="width: 100%; padding: 10px; border: 2px solid #e5e7eb; border-radius: 8px;">
                                <option value="success">نجاح</option>
                                <option value="warning">تحذير</option>
                                <option value="error">خطأ</option>
                                <option value="info">معلومات</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #374151;">عنوان الإشعار</label>
                            <input type="text" id="notificationTitle" placeholder="أدخل عنوان الإشعار..." style="width: 100%; padding: 10px; border: 2px solid #e5e7eb; border-radius: 8px;">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #374151;">محتوى الإشعار</label>
                            <textarea id="notificationMessage" rows="3" placeholder="أدخل محتوى الإشعار..." style="width: 100%; padding: 10px; border: 2px solid #e5e7eb; border-radius: 8px; resize: vertical;"></textarea>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: flex-end;">
                            <button type="button" onclick="closeModal()" style="
                                padding: 10px 20px;
                                border: 2px solid #e5e7eb;
                                background: white;
                                color: #6b7280;
                                border-radius: 8px;
                                cursor: pointer;
                                font-weight: 500;
                            ">إلغاء</button>
                            <button type="submit" style="
                                padding: 10px 20px;
                                border: none;
                                background: linear-gradient(135deg, #667eea, #764ba2);
                                color: white;
                                border-radius: 8px;
                                cursor: pointer;
                                font-weight: 500;
                            ">إنشاء الإشعار</button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // تأثير الظهور
            setTimeout(() => {
                modal.style.opacity = '1';
                modal.querySelector('div').style.transform = 'scale(1)';
            }, 10);

            // إغلاق عند النقر خارج النافذة
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });

            // معالجة النموذج
            document.getElementById('newNotificationForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const type = document.getElementById('notificationType').value;
                const title = document.getElementById('notificationTitle').value;
                const message = document.getElementById('notificationMessage').value;

                if (!title || !message) {
                    showNotification('يرجى ملء جميع الحقول', 'warning');
                    return;
                }

                // إرسال طلب إنشاء الإشعار للخادم
                fetch('/shipments/notification-center/api/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: type,
                        title: title,
                        message: message,
                        priority: 5
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        closeModal();
                        showNotification(data.message, 'success');
                        // إعادة تحميل الصفحة لإظهار الإشعار الجديد
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showNotification(data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('حدث خطأ في إنشاء الإشعار', 'error');
                });
            });

            window.closeModal = function() {
                modal.style.opacity = '0';
                modal.querySelector('div').style.transform = 'scale(0.9)';
                setTimeout(() => {
                    modal.remove();
                }, 300);
            };
        }

        // إظهار نافذة إعدادات الإشعارات
        function showNotificationSettingsModal() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(10px);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 20px;
                    padding: 30px;
                    max-width: 600px;
                    width: 90%;
                    max-height: 80vh;
                    overflow-y: auto;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
                    transform: scale(0.9);
                    transition: transform 0.3s ease;
                ">
                    <h3 style="margin-bottom: 20px; color: #1e293b; font-weight: 700;">
                        <i class="fas fa-cog" style="margin-left: 10px; color: #667eea;"></i>
                        إعدادات الإشعارات
                    </h3>

                    <div style="margin-bottom: 20px;">
                        <h4 style="color: #374151; margin-bottom: 15px;">إعدادات العرض</h4>

                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding: 15px; background: #f8fafc; border-radius: 10px;">
                            <div>
                                <div style="font-weight: 600; color: #1e293b;">إشعارات سطح المكتب</div>
                                <div style="font-size: 0.9rem; color: #6b7280;">إظهار إشعارات في نظام التشغيل</div>
                            </div>
                            <label style="position: relative; display: inline-block; width: 50px; height: 25px;">
                                <input type="checkbox" checked style="opacity: 0; width: 0; height: 0;">
                                <span style="
                                    position: absolute;
                                    cursor: pointer;
                                    top: 0;
                                    left: 0;
                                    right: 0;
                                    bottom: 0;
                                    background: #667eea;
                                    border-radius: 25px;
                                    transition: 0.4s;
                                "></span>
                                <span style="
                                    position: absolute;
                                    content: '';
                                    height: 19px;
                                    width: 19px;
                                    right: 3px;
                                    bottom: 3px;
                                    background: white;
                                    border-radius: 50%;
                                    transition: 0.4s;
                                "></span>
                            </label>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding: 15px; background: #f8fafc; border-radius: 10px;">
                            <div>
                                <div style="font-weight: 600; color: #1e293b;">الأصوات</div>
                                <div style="font-size: 0.9rem; color: #6b7280;">تشغيل أصوات عند وصول إشعارات جديدة</div>
                            </div>
                            <label style="position: relative; display: inline-block; width: 50px; height: 25px;">
                                <input type="checkbox" checked style="opacity: 0; width: 0; height: 0;">
                                <span style="
                                    position: absolute;
                                    cursor: pointer;
                                    top: 0;
                                    left: 0;
                                    right: 0;
                                    bottom: 0;
                                    background: #667eea;
                                    border-radius: 25px;
                                    transition: 0.4s;
                                "></span>
                                <span style="
                                    position: absolute;
                                    content: '';
                                    height: 19px;
                                    width: 19px;
                                    right: 3px;
                                    bottom: 3px;
                                    background: white;
                                    border-radius: 50%;
                                    transition: 0.4s;
                                "></span>
                            </label>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding: 15px; background: #f8fafc; border-radius: 10px;">
                            <div>
                                <div style="font-weight: 600; color: #1e293b;">التحديث التلقائي</div>
                                <div style="font-size: 0.9rem; color: #6b7280;">تحديث الإشعارات تلقائياً كل دقيقة</div>
                            </div>
                            <label style="position: relative; display: inline-block; width: 50px; height: 25px;">
                                <input type="checkbox" checked style="opacity: 0; width: 0; height: 0;">
                                <span style="
                                    position: absolute;
                                    cursor: pointer;
                                    top: 0;
                                    left: 0;
                                    right: 0;
                                    bottom: 0;
                                    background: #667eea;
                                    border-radius: 25px;
                                    transition: 0.4s;
                                "></span>
                                <span style="
                                    position: absolute;
                                    content: '';
                                    height: 19px;
                                    width: 19px;
                                    right: 3px;
                                    bottom: 3px;
                                    background: white;
                                    border-radius: 50%;
                                    transition: 0.4s;
                                "></span>
                            </label>
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <h4 style="color: #374151; margin-bottom: 15px;">إعدادات التصفية</h4>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #374151;">عدد الإشعارات المعروضة</label>
                            <select style="width: 100%; padding: 10px; border: 2px solid #e5e7eb; border-radius: 8px;">
                                <option value="10">10 إشعارات</option>
                                <option value="25" selected>25 إشعار</option>
                                <option value="50">50 إشعار</option>
                                <option value="100">100 إشعار</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #374151;">حذف الإشعارات تلقائياً بعد</label>
                            <select style="width: 100%; padding: 10px; border: 2px solid #e5e7eb; border-radius: 8px;">
                                <option value="7">7 أيام</option>
                                <option value="30" selected>30 يوم</option>
                                <option value="90">90 يوم</option>
                                <option value="0">عدم الحذف</option>
                            </select>
                        </div>
                    </div>

                    <div style="display: flex; gap: 10px; justify-content: flex-end;">
                        <button type="button" onclick="closeSettingsModal()" style="
                            padding: 10px 20px;
                            border: 2px solid #e5e7eb;
                            background: white;
                            color: #6b7280;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 500;
                        ">إلغاء</button>
                        <button type="button" onclick="saveNotificationSettings()" style="
                            padding: 10px 20px;
                            border: none;
                            background: linear-gradient(135deg, #667eea, #764ba2);
                            color: white;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 500;
                        ">حفظ الإعدادات</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // تأثير الظهور
            setTimeout(() => {
                modal.style.opacity = '1';
                modal.querySelector('div').style.transform = 'scale(1)';
            }, 10);

            // إغلاق عند النقر خارج النافذة
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeSettingsModal();
                }
            });

            window.closeSettingsModal = function() {
                modal.style.opacity = '0';
                modal.querySelector('div').style.transform = 'scale(0.9)';
                setTimeout(() => {
                    modal.remove();
                }, 300);
            };

            window.saveNotificationSettings = function() {
                showNotification('تم حفظ إعدادات الإشعارات بنجاح', 'success');
                closeSettingsModal();
            };
        }

        // إضافة كرت إشعار جديد
        function addNewNotificationCard(type, title, message) {
            const iconMap = {
                success: 'fas fa-check-circle',
                warning: 'fas fa-exclamation-triangle',
                error: 'fas fa-times-circle',
                info: 'fas fa-info-circle'
            };

            const newCard = document.createElement('div');
            newCard.className = 'notification-card';
            newCard.dataset.type = type;
            newCard.dataset.read = 'false';

            newCard.innerHTML = `
                <div class="notification-badge badge-new">جديد</div>
                <div class="notification-header">
                    <div class="notification-icon ${type}">
                        <i class="${iconMap[type]}"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-title">${title}</div>
                        <div class="notification-message">${message}</div>
                        <div class="notification-meta">
                            <div class="notification-time">
                                <i class="fas fa-clock"></i>
                                الآن
                            </div>
                            <div class="notification-actions">
                                <button class="action-btn read" onclick="markAsRead(this)">تحديد كمقروء</button>
                                <button class="action-btn delete" onclick="deleteNotification(this)">حذف</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // إضافة الكرت في المقدمة
            const grid = document.getElementById('notificationsGrid');
            grid.insertBefore(newCard, grid.firstChild);

            // تأثير الظهور
            newCard.style.opacity = '0';
            newCard.style.transform = 'translateY(-30px)';

            setTimeout(() => {
                newCard.style.transition = 'all 0.6s ease';
                newCard.style.opacity = '1';
                newCard.style.transform = 'translateY(0)';
            }, 100);

            // تحديث الإحصائيات
            updateStats();
            filterNotifications();
        }

        // إظهار رسالة إشعار
        function showNotification(message, type) {
            const colors = {
                success: '#10b981',
                error: '#ef4444',
                warning: '#f59e0b',
                info: '#06b6d4'
            };

            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                background: ${colors[type] || colors.info};
                color: white;
                padding: 15px 20px;
                border-radius: 12px;
                box-shadow: 0 10px 25px rgba(0,0,0,0.2);
                min-width: 300px;
                transform: translateX(400px);
                transition: all 0.4s ease;
                font-weight: 500;
            `;

            notification.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        background: none;
                        border: none;
                        color: white;
                        font-size: 1.2rem;
                        cursor: pointer;
                        margin-right: 10px;
                    ">×</button>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                notification.style.transform = 'translateX(400px)';
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 400);
            }, 3000);
        }

        // تأثيرات الحركة للكروت
        function animateCards() {
            const cards = document.querySelectorAll('.notification-card');

            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        }

        // تأثيرات خلفية متحركة
        function createFloatingElements() {
            for (let i = 0; i < 3; i++) {
                const element = document.createElement('div');
                element.style.cssText = `
                    position: fixed;
                    width: 6px;
                    height: 6px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 0;
                    animation: float ${8 + Math.random() * 4}s infinite linear;
                    left: ${Math.random() * 100}%;
                    top: 100%;
                `;
                document.body.appendChild(element);

                setTimeout(() => {
                    element.remove();
                }, 12000);
            }
        }

        // إضافة CSS للعناصر المتحركة
        const style = document.createElement('style');
        style.textContent = `
            @keyframes float {
                to {
                    transform: translateY(-100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // تشغيل العناصر المتحركة
        setInterval(createFloatingElements, 4000);

        // وظائف الشريط الجانبي
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            if (sidebar.classList.contains('active')) {
                closeSidebar();
            } else {
                openSidebar();
            }
        }

        function openSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.classList.add('active');
            overlay.classList.add('active');

            // منع التمرير في الخلفية
            document.body.style.overflow = 'hidden';

            // تأثير تحميل العناصر
            const menuItems = sidebar.querySelectorAll('.sidebar-menu-item');
            menuItems.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateX(30px)';

                setTimeout(() => {
                    item.style.transition = 'all 0.3s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateX(0)';
                }, index * 50);
            });
        }

        function closeSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.classList.remove('active');
            overlay.classList.remove('active');

            // إعادة تفعيل التمرير
            document.body.style.overflow = '';
        }

        // إغلاق الشريط الجانبي عند الضغط على Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeSidebar();
            }
        });

        // تأثيرات إضافية للشريط الجانبي
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover للعناصر
            const menuLinks = document.querySelectorAll('.sidebar-menu-link');
            menuLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(-8px)';
                });

                link.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                });
            });

            // تحديث الإحصائيات السريعة
            updateQuickStats();
        });

        // تحديث الإحصائيات السريعة
        function updateQuickStats() {
            // محاكاة جلب البيانات الحقيقية
            const stats = [
                { value: Math.floor(Math.random() * 200) + 100, label: 'شحنة نشطة' },
                { value: Math.floor(Math.random() * 100) + 50, label: 'أمر تسليم' },
                { value: Math.floor(Math.random() * 50) + 30, label: 'مخلص نشط' },
                { value: (Math.random() * 5 + 95).toFixed(1) + '%', label: 'معدل النجاح' }
            ];

            const statItems = document.querySelectorAll('.quick-stat-item');
            statItems.forEach((item, index) => {
                const numberElement = item.querySelector('.quick-stat-number');
                if (numberElement && stats[index]) {
                    // تأثير العد التصاعدي
                    animateNumber(numberElement, stats[index].value);
                }
            });
        }

        // تأثير العد التصاعدي للأرقام
        function animateNumber(element, targetValue) {
            const isPercentage = typeof targetValue === 'string' && targetValue.includes('%');
            const numericValue = isPercentage ? parseFloat(targetValue) : parseInt(targetValue);
            const currentValue = parseInt(element.textContent) || 0;

            const increment = (numericValue - currentValue) / 20;
            let current = currentValue;

            const timer = setInterval(() => {
                current += increment;
                if ((increment > 0 && current >= numericValue) || (increment < 0 && current <= numericValue)) {
                    current = numericValue;
                    clearInterval(timer);
                }

                element.textContent = isPercentage ? current.toFixed(1) + '%' : Math.floor(current);
            }, 50);
        }
    </script>

</body>
</html>
