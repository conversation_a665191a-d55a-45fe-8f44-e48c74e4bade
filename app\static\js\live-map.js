/**
 * نظام خريطة التتبع المباشر للشحنات
 * Live Shipment Tracking Map System
 */

class LiveShipmentMap {
    constructor(containerId) {
        this.containerId = containerId;
        this.map = null;
        this.markers = new Map();
        this.routes = new Map();
        this.isLive = false;
        this.updateInterval = null;
        this.shipments = [];
        
        // إعدادات الخريطة
        this.mapConfig = {
            center: [15.3694, 44.1910], // اليمن
            zoom: 6,
            minZoom: 2,
            maxZoom: 18
        };
        
        // ألوان حالات الشحنات
        this.statusColors = {
            'في الطريق': '#28a745',
            'متأخر': '#ffc107', 
            'وصل': '#6c757d',
            'مشكلة': '#dc3545',
            'قيد التحميل': '#17a2b8',
            'قيد التفريغ': '#fd7e14'
        };
        
        this.init();
    }
    
    async init() {
        try {
            await this.createMap();
            await this.loadShipments();
            this.setupControls();
            this.startLiveUpdates();
            console.log('✅ تم تهيئة خريطة التتبع المباشر');
        } catch (error) {
            console.error('❌ خطأ في تهيئة الخريطة:', error);
            this.showError('فشل في تحميل الخريطة');
        }
    }
    
    async createMap() {
        const container = document.getElementById(this.containerId);
        if (!container) {
            throw new Error(`Container ${this.containerId} not found`);
        }
        
        // إظهار مؤشر التحميل
        container.innerHTML = `
            <div class="map-loading">
                <div class="text-center">
                    <div class="spinner"></div>
                    <div>جاري تحميل الخريطة...</div>
                </div>
            </div>
        `;
        
        // التأكد من تحميل Leaflet
        if (typeof L === 'undefined') {
            await this.loadLeaflet();
        }
        
        // إنشاء الخريطة
        container.innerHTML = '<div id="mapInstance" style="height: 100%; width: 100%;"></div>';
        
        this.map = L.map('mapInstance', {
            center: this.mapConfig.center,
            zoom: this.mapConfig.zoom,
            minZoom: this.mapConfig.minZoom,
            maxZoom: this.mapConfig.maxZoom,
            zoomControl: false
        });
        
        // إضافة طبقة الخريطة
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors',
            maxZoom: 19
        }).addTo(this.map);
        
        // إضافة أدوات التحكم
        this.addMapControls();
    }
    
    async loadLeaflet() {
        return new Promise((resolve, reject) => {
            // تحميل CSS
            const cssLink = document.createElement('link');
            cssLink.rel = 'stylesheet';
            cssLink.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
            document.head.appendChild(cssLink);
            
            // تحميل JavaScript
            const script = document.createElement('script');
            script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
    
    addMapControls() {
        const container = document.getElementById(this.containerId);
        
        // مؤشر التحديث المباشر
        const liveIndicator = document.createElement('div');
        liveIndicator.className = 'live-indicator';
        liveIndicator.innerHTML = `
            <div class="pulse-dot"></div>
            <span>مباشر</span>
        `;
        container.appendChild(liveIndicator);
        
        // أدوات التحكم
        const controls = document.createElement('div');
        controls.className = 'map-controls';
        controls.innerHTML = `
            <button class="map-control-btn" onclick="liveMap.toggleLive()" title="تشغيل/إيقاف التحديث المباشر">
                <i class="fas fa-play"></i>
            </button>
            <button class="map-control-btn" onclick="liveMap.refreshData()" title="تحديث البيانات">
                <i class="fas fa-sync-alt"></i>
            </button>
            <button class="map-control-btn" onclick="liveMap.fitAllMarkers()" title="عرض جميع الشحنات">
                <i class="fas fa-expand-arrows-alt"></i>
            </button>
            <button class="map-control-btn" onclick="liveMap.toggleRoutes()" title="إظهار/إخفاء المسارات">
                <i class="fas fa-route"></i>
            </button>
        `;
        container.appendChild(controls);
        
        // إحصائيات الخريطة
        const stats = document.createElement('div');
        stats.className = 'map-stats';
        stats.id = 'mapStats';
        container.appendChild(stats);
        
        this.updateStats();
    }
    
    async loadShipments() {
        try {
            const response = await fetch('/shipments/api/live-tracking-data');
            const data = await response.json();
            
            if (data.success) {
                this.shipments = data.shipments || [];
                this.updateMarkers();
                this.updateStats();
            } else {
                console.error('فشل في جلب بيانات الشحنات:', data.message);
            }
        } catch (error) {
            console.error('خطأ في جلب بيانات الشحنات:', error);
            // استخدام بيانات تجريبية
            this.loadDemoData();
        }
    }
    
    loadDemoData() {
        // بيانات تجريبية للعرض
        this.shipments = [
            {
                id: 1,
                shipment_number: 'CRG20250812001',
                status: 'في الطريق',
                current_location: {
                    lat: 14.5995,
                    lng: 120.9842,
                    name: 'مانيلا، الفلبين'
                },
                destination: {
                    lat: 12.7797,
                    lng: 45.0365,
                    name: 'عدن، اليمن'
                },
                progress: 65,
                eta: '2025-08-20',
                container_count: 2
            },
            {
                id: 2,
                shipment_number: 'CRG20250812002',
                status: 'قيد التحميل',
                current_location: {
                    lat: 25.2048,
                    lng: 55.2708,
                    name: 'دبي، الإمارات'
                },
                destination: {
                    lat: 15.3694,
                    lng: 44.1910,
                    name: 'صنعاء، اليمن'
                },
                progress: 10,
                eta: '2025-08-25',
                container_count: 1
            },
            {
                id: 3,
                shipment_number: 'CRG20250812003',
                status: 'وصل',
                current_location: {
                    lat: 12.7797,
                    lng: 45.0365,
                    name: 'عدن، اليمن'
                },
                destination: {
                    lat: 12.7797,
                    lng: 45.0365,
                    name: 'عدن، اليمن'
                },
                progress: 100,
                eta: 'وصل',
                container_count: 3
            }
        ];
        
        this.updateMarkers();
        this.updateStats();
    }
    
    updateMarkers() {
        // مسح العلامات القديمة
        this.markers.forEach(marker => {
            this.map.removeLayer(marker);
        });
        this.markers.clear();
        
        // إضافة علامات جديدة
        this.shipments.forEach(shipment => {
            this.addShipmentMarker(shipment);
        });
    }
    
    addShipmentMarker(shipment) {
        const location = shipment.current_location;
        if (!location || !location.lat || !location.lng) return;
        
        // إنشاء أيقونة مخصصة
        const icon = L.divIcon({
            className: `shipment-marker ${this.getStatusClass(shipment.status)}`,
            html: `<i class="fas fa-ship" style="color: white; font-size: 10px; margin: 3px;"></i>`,
            iconSize: [20, 20],
            iconAnchor: [10, 10]
        });
        
        // إنشاء العلامة
        const marker = L.marker([location.lat, location.lng], { icon })
            .addTo(this.map);
        
        // إضافة نافذة المعلومات
        const popupContent = this.createPopupContent(shipment);
        marker.bindPopup(popupContent, {
            maxWidth: 300,
            className: 'shipment-popup'
        });
        
        // حفظ العلامة
        this.markers.set(shipment.id, marker);
        
        // إضافة المسار إذا كان متاحاً
        if (shipment.destination && shipment.status !== 'وصل') {
            this.addRoute(shipment);
        }
    }
    
    createPopupContent(shipment) {
        const statusClass = this.getStatusClass(shipment.status);
        return `
            <div class="shipment-popup">
                <div class="popup-header">
                    <strong>${shipment.shipment_number}</strong>
                </div>
                <div class="popup-content">
                    <div class="mb-2">
                        <span class="status-badge status-${statusClass}">${shipment.status}</span>
                    </div>
                    <div class="mb-1">
                        <i class="fas fa-map-marker-alt text-primary"></i>
                        <strong>الموقع الحالي:</strong><br>
                        <small>${shipment.current_location.name}</small>
                    </div>
                    <div class="mb-1">
                        <i class="fas fa-flag-checkered text-success"></i>
                        <strong>الوجهة:</strong><br>
                        <small>${shipment.destination.name}</small>
                    </div>
                    <div class="mb-1">
                        <i class="fas fa-percentage text-info"></i>
                        <strong>التقدم:</strong> ${shipment.progress}%
                    </div>
                    <div class="mb-1">
                        <i class="fas fa-clock text-warning"></i>
                        <strong>الوصول المتوقع:</strong> ${shipment.eta}
                    </div>
                    <div>
                        <i class="fas fa-cube text-secondary"></i>
                        <strong>الحاويات:</strong> ${shipment.container_count}
                    </div>
                </div>
            </div>
        `;
    }
    
    addRoute(shipment) {
        const start = shipment.current_location;
        const end = shipment.destination;
        
        if (!start || !end) return;
        
        const routeLine = L.polyline([
            [start.lat, start.lng],
            [end.lat, end.lng]
        ], {
            color: this.statusColors[shipment.status] || '#007bff',
            weight: 3,
            opacity: 0.7,
            dashArray: '5, 5'
        }).addTo(this.map);
        
        this.routes.set(shipment.id, routeLine);
    }
    
    getStatusClass(status) {
        const statusMap = {
            'في الطريق': 'in-transit',
            'متأخر': 'delayed',
            'وصل': 'arrived',
            'مشكلة': 'problem',
            'قيد التحميل': 'in-transit',
            'قيد التفريغ': 'in-transit'
        };
        return statusMap[status] || 'in-transit';
    }
    
    updateStats() {
        const statsContainer = document.getElementById('mapStats');
        if (!statsContainer) return;
        
        const stats = this.calculateStats();
        
        statsContainer.innerHTML = `
            <div class="stat-item">
                <div class="stat-color" style="background: #28a745;"></div>
                <span>في الطريق: ${stats.inTransit}</span>
            </div>
            <div class="stat-item">
                <div class="stat-color" style="background: #ffc107;"></div>
                <span>متأخر: ${stats.delayed}</span>
            </div>
            <div class="stat-item">
                <div class="stat-color" style="background: #6c757d;"></div>
                <span>وصل: ${stats.arrived}</span>
            </div>
            <div class="stat-item">
                <div class="stat-color" style="background: #dc3545;"></div>
                <span>مشاكل: ${stats.problems}</span>
            </div>
        `;
    }
    
    calculateStats() {
        return {
            inTransit: this.shipments.filter(s => s.status === 'في الطريق').length,
            delayed: this.shipments.filter(s => s.status === 'متأخر').length,
            arrived: this.shipments.filter(s => s.status === 'وصل').length,
            problems: this.shipments.filter(s => s.status === 'مشكلة').length
        };
    }
    
    // دوال التحكم
    toggleLive() {
        this.isLive = !this.isLive;
        const btn = document.querySelector('.map-control-btn i.fa-play, .map-control-btn i.fa-pause');
        const indicator = document.querySelector('.live-indicator');
        
        if (this.isLive) {
            btn.className = 'fas fa-pause';
            indicator.classList.add('active');
            this.startLiveUpdates();
        } else {
            btn.className = 'fas fa-play';
            indicator.classList.remove('active');
            this.stopLiveUpdates();
        }
    }
    
    startLiveUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        this.updateInterval = setInterval(() => {
            if (this.isLive) {
                this.loadShipments();
            }
        }, 30000); // تحديث كل 30 ثانية
    }
    
    stopLiveUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }
    
    refreshData() {
        const btn = document.querySelector('.map-control-btn .fa-sync-alt');
        btn.classList.add('fa-spin');
        
        this.loadShipments().finally(() => {
            btn.classList.remove('fa-spin');
        });
    }
    
    fitAllMarkers() {
        if (this.markers.size === 0) return;
        
        const group = new L.featureGroup(Array.from(this.markers.values()));
        this.map.fitBounds(group.getBounds().pad(0.1));
    }
    
    toggleRoutes() {
        const btn = document.querySelector('.map-control-btn .fa-route');
        const isVisible = btn.classList.contains('active');
        
        this.routes.forEach(route => {
            if (isVisible) {
                this.map.removeLayer(route);
            } else {
                this.map.addLayer(route);
            }
        });
        
        btn.classList.toggle('active');
    }
    
    showError(message) {
        const container = document.getElementById(this.containerId);
        container.innerHTML = `
            <div class="d-flex align-items-center justify-content-center h-100 text-danger">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle mb-3" style="font-size: 3rem;"></i>
                    <h5 class="mb-2">خطأ في تحميل الخريطة</h5>
                    <p>${message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-redo me-1"></i>
                        إعادة المحاولة
                    </button>
                </div>
            </div>
        `;
    }
}

// متغير عام للخريطة
let liveMap = null;

// تهيئة الخريطة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const mapContainer = document.getElementById('liveTrackingMap');
    if (mapContainer) {
        liveMap = new LiveShipmentMap('liveTrackingMap');
    }
});
