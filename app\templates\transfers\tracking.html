<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تتبع الحوالات المنفذة - نظام الفوجي</title>

    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom Styles -->
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --dark-bg: #2c3e50;
            --border-color: #dee2e6;
            --text-muted: #6c757d;
            --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        /* Professional Header */
        .dashboard-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0.5rem 0 0 0;
            font-weight: 300;
        }

        /* Enterprise Buttons */
        .btn-enterprise {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            border: none;
            box-shadow: var(--shadow);
        }

        .btn-primary-enterprise {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
            color: white;
        }

        .btn-primary-enterprise:hover {
            background: linear-gradient(135deg, #2980b9 0%, var(--secondary-color) 100%);
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .btn-secondary-enterprise {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            color: white;
        }

        .btn-secondary-enterprise:hover {
            background: linear-gradient(135deg, #7f8c8d 0%, #95a5a6 100%);
            transform: translateY(-2px);
        }

        /* Header Actions */
        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        /* Breadcrumb Navigation */
        .breadcrumb-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        .breadcrumb-custom {
            background: none;
            margin: 0;
            padding: 0;
            font-size: 0.95rem;
        }

        .breadcrumb-custom .breadcrumb-item {
            color: var(--text-muted);
            font-weight: 500;
        }

        .breadcrumb-custom .breadcrumb-item.active {
            color: var(--primary-color);
            font-weight: 600;
        }

        .breadcrumb-custom .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: var(--text-muted);
            font-weight: 600;
            font-size: 1.1rem;
        }

        .breadcrumb-custom a {
            color: var(--secondary-color);
            text-decoration: none;
            transition: all 0.3s ease;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
        }

        .breadcrumb-custom a:hover {
            color: var(--primary-color);
            background-color: rgba(52, 152, 219, 0.1);
            transform: translateY(-1px);
        }

        .breadcrumb-icon {
            margin-left: 0.5rem;
            font-size: 0.9rem;
        }

.tracking-card {
    border-radius: 1rem;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.tracking-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.status-pending {
    background-color: #ffc107;
    color: #212529;
}

.status-approved {
    background-color: #17a2b8;
    color: white;
}

.status-executed {
    background-color: #28a745;
    color: white;
}

.status-completed {
    background-color: #6f42c1;
    color: white;
}

.status-rejected {
    background-color: #dc3545;
    color: white;
}

.status-cancelled {
    background-color: #6c757d;
    color: white;
}

.progress-bar-container {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    transition: width 0.3s ease;
}

.progress-level-0 { width: 0%; background-color: #dc3545; }
.progress-level-1 { width: 25%; background-color: #ffc107; }
.progress-level-2 { width: 50%; background-color: #17a2b8; }
.progress-level-3 { width: 75%; background-color: #28a745; }
.progress-level-4 { width: 100%; background-color: #6f42c1; }

.search-filters {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #dee2e6;
}

.stats-card {
    border-radius: 1rem;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.amount-display {
    font-size: 1.1rem;
    font-weight: bold;
    color: #28a745;
}

.days-elapsed {
    font-weight: bold;
}

.days-elapsed.warning {
    color: #fd7e14;
}

        .days-elapsed.danger {
            color: #dc3545;
        }
    </style>
</head>
<body>
<!-- Professional Header -->
<div class="dashboard-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="page-title">
                    <i class="fas fa-route ms-3"></i>
                    تتبع الحوالات المنفذة
                </h1>
                <p class="page-subtitle">
                    تتبع شامل ومراقبة فورية للحوالات المنفذة مع فلترة متقدمة وتحليل تفصيلي
                </p>
            </div>
            <div class="col-lg-4">
                <div class="header-actions d-flex gap-2 justify-content-lg-end">
                    <button class="btn-enterprise btn-secondary-enterprise" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                    <div class="dropdown">
                        <button class="btn-enterprise btn-secondary-enterprise dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="exportToExcel()"><i class="fas fa-file-excel me-2"></i>Excel</a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportToPDF()"><i class="fas fa-file-pdf me-2"></i>PDF</a></li>
                            <li><a class="dropdown-item" href="#" onclick="printReport()"><i class="fas fa-print me-2"></i>طباعة</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="breadcrumb-container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb breadcrumb-custom">
                <li class="breadcrumb-item">
                    <a href="/" onclick="navigateToHome()">
                        <i class="fas fa-home breadcrumb-icon"></i>
                        الرئيسية
                    </a>
                </li>
                <li class="breadcrumb-item">
                    <a href="/transfers/dashboard" onclick="navigateToTransfersDashboard()">
                        <i class="fas fa-money-bill-transfer breadcrumb-icon"></i>
                        نظام الحوالات
                    </a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">
                    <i class="fas fa-route breadcrumb-icon"></i>
                    تتبع الحوالات المنفذة
                </li>
            </ol>
        </nav>
    </div>

    <!-- Statistics Cards - Hidden as requested -->
    <div class="row mb-4" style="display: none !important;" id="statisticsCards">
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <div class="text-success mb-2">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                    <h4 class="mb-1" id="totalExecutedTransfers">-</h4>
                    <p class="text-muted mb-0 small">إجمالي الحوالات المنفذة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <div class="text-primary mb-2">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                    <h4 class="mb-1" id="totalExecutedAmount">-</h4>
                    <p class="text-muted mb-0 small">إجمالي المبالغ المنفذة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <div class="text-info mb-2">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                    <h4 class="mb-1" id="totalSuppliers">-</h4>
                    <p class="text-muted mb-0 small">إجمالي الموردين المستخدمين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <div class="text-warning mb-2">
                        <i class="fas fa-calendar-day fa-2x"></i>
                    </div>
                    <h4 class="mb-1" id="todayExecutions">-</h4>
                    <p class="text-muted mb-0 small">تنفيذات اليوم</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Filters for Executed Transfers -->
    <div class="search-filters">
        <h6 class="mb-3">
            <i class="fas fa-filter me-2"></i>فلترة متقدمة للحوالات المنفذة
        </h6>
        <div class="row">
            <div class="col-md-3">
                <label for="searchTerm" class="form-label">البحث</label>
                <input type="text" class="form-control" id="searchTerm"
                       placeholder="رقم الطلب، المستفيد، أو مرجع التنفيذ">
            </div>
            <div class="col-md-2">
                <label for="branchFilter" class="form-label">الفرع</label>
                <select class="form-select" id="branchFilter">
                    <option value="">جميع الفروع</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="moneyChangerFilter" class="form-label">الصراف/البنك</label>
                <select class="form-select" id="moneyChangerFilter">
                    <option value="">جميع الصرافين والبنوك</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="supplierFilter" class="form-label">المورد</label>
                <select class="form-select" id="supplierFilter">
                    <option value="">جميع الموردين</option>
                </select>
            </div>
            <div class="col-md-1">
                <label for="dateFrom" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="dateFrom">
            </div>
            <div class="col-md-1">
                <label for="dateTo" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="dateTo">
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-primary w-100" onclick="applyFilters()">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Tracking Results Table -->
    <div class="row">
        <div class="col-12">
            <div class="card tracking-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table me-2"></i>نتائج التتبع
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الحوالة</th>
                                    <th>رقم الطلب</th>
                                    <th>الفرع</th>
                                    <th>الصراف/البنك</th>
                                    <th>المبلغ الأصلي</th>
                                    <th>المبلغ الموزع</th>
                                    <th>عدد الموردين</th>
                                    <th>مرجع التنفيذ</th>
                                    <th>تاريخ التنفيذ</th>
                                    <th>الموردين</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="trackingTableBody">
                                <tr>
                                    <td colspan="11" class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <p class="mt-2 mb-0">جاري تحميل بيانات التتبع...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Total Summary -->
                    <div class="card-footer bg-light">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="mb-0 text-muted">
                                    <i class="fas fa-calculator me-2"></i>
                                    إجمالي المبالغ المحولة في النتائج المعروضة:
                                </h6>
                            </div>
                            <div class="col-md-4 text-end">
                                <h5 class="mb-0 text-primary fw-bold" id="totalDisplayedAmount">
                                    <i class="fas fa-coins me-1"></i>
                                    <span id="totalAmountValue">0</span>
                                    <small class="text-muted ms-1" id="totalAmountCurrency">ريال</small>
                                </h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Track Single Request Modal -->
<div class="modal fade" id="trackModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-route text-info me-2"></i>
                    تتبع تفصيلي للطلب
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="trackModalBody">
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2 mb-0">جاري تحميل بيانات التتبع...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
let trackingData = [];
let filteredData = [];

$(document).ready(function() {
    loadTrackingData();
    loadStatistics();
    loadTrackingFilters();

    // إعداد أحداث البحث والفلترة المتقدمة
    $('#searchTerm').on('input', debounce(applyFilters, 300));
    $('#branchFilter, #moneyChangerFilter, #supplierFilter, #dateFrom, #dateTo').on('change', applyFilters);
});

// تحميل بيانات التتبع
function loadTrackingData() {
    console.log('🔄 تحميل بيانات التتبع...');

    const params = new URLSearchParams();

    // إضافة معاملات الفلترة المتقدمة
    const searchTerm = $('#searchTerm').val().trim();
    const branchFilter = $('#branchFilter').val();
    const moneyChangerFilter = $('#moneyChangerFilter').val();
    const supplierFilter = $('#supplierFilter').val();
    const dateFrom = $('#dateFrom').val();
    const dateTo = $('#dateTo').val();

    if (searchTerm) params.append('search', searchTerm);
    if (branchFilter) params.append('branch', branchFilter);
    if (moneyChangerFilter) params.append('money_changer', moneyChangerFilter);
    if (supplierFilter) params.append('supplier', supplierFilter);
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);

    $.get(`/transfers/api/track-requests?${params.toString()}`)
        .done(function(response) {
            if (response.success) {
                trackingData = response.data;
                filteredData = trackingData;
                displayTrackingData(filteredData);
                console.log(`✅ تم تحميل ${trackingData.length} طلب`);
            } else {
                showError('فشل في تحميل بيانات التتبع');
            }
        })
        .fail(function() {
            showError('حدث خطأ أثناء تحميل بيانات التتبع');
        });
}

// تحميل الإحصائيات
function loadStatistics() {
    console.log('🔄 بدء تحميل الإحصائيات...');
    $.get('/transfers/api/tracking-statistics')
        .done(function(response) {
            console.log('📊 استجابة الإحصائيات:', response);
            if (response.success) {
                updateStatistics(response.data);
                console.log('✅ تم تحديث الإحصائيات');
            } else {
                console.error('❌ فشل في تحميل الإحصائيات:', response.message);
            }
        })
        .fail(function(xhr, status, error) {
            console.error('🚫 خطأ في تحميل الإحصائيات:', error);
            console.error('📊 تفاصيل الخطأ:', xhr.responseText);
        });
}

// تحميل خيارات الفلترة المتقدمة
function loadTrackingFilters() {
    console.log('🔄 بدء تحميل فلاتر التتبع...');
    $.get('/transfers/api/tracking-filters')
        .done(function(response) {
            console.log('📡 استجابة فلاتر التتبع:', response);
            if (response.success) {
                const data = response.data;

                // تحميل الفروع
                const branchSelect = $('#branchFilter');
                console.log('🏢 عدد الفروع:', data.branches.length);
                data.branches.forEach(function(branch) {
                    branchSelect.append(`<option value="${branch.id}">${branch.name}</option>`);
                });

                // تحميل الصرافين والبنوك
                const moneyChangerSelect = $('#moneyChangerFilter');
                console.log('🏦 عدد الصرافين/البنوك:', data.money_changers.length);
                data.money_changers.forEach(function(mc) {
                    moneyChangerSelect.append(`<option value="${mc.id}">${mc.name} (${mc.type})</option>`);
                });

                // تحميل الموردين
                const supplierSelect = $('#supplierFilter');
                console.log('👥 عدد الموردين:', data.suppliers.length);
                data.suppliers.forEach(function(supplier) {
                    supplierSelect.append(`<option value="${supplier.id}">${supplier.name} - ${supplier.code}</option>`);
                });

                console.log('✅ تم تحميل فلاتر التتبع بنجاح');
            } else {
                console.error('❌ فشل في تحميل فلاتر التتبع:', response.message);
            }
        })
        .fail(function(xhr, status, error) {
            console.error('🚫 خطأ في تحميل فلاتر التتبع:', error);
            console.error('📊 تفاصيل الخطأ:', xhr.responseText);
        });
}

// عرض بيانات التتبع
function displayTrackingData(data) {
    const tbody = $('#trackingTableBody');
    tbody.empty();

    if (data.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="11" class="text-center py-4">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5>لا توجد حوالات منفذة</h5>
                    <p class="text-muted">لم يتم العثور على حوالات منفذة تطابق معايير البحث</p>
                </td>
            </tr>
        `);
        // إعادة تعيين الإجمالي إلى صفر
        $('#totalAmountValue').text('0');
        $('#totalAmountCurrency').text('ريال');
        return;
    }

    data.forEach(function(transfer) {
        const row = createTrackingRow(transfer);
        tbody.append(row);
    });

    // حساب إجمالي المبالغ المعروضة
    calculateDisplayedTotal(data);
}

// إنشاء صف تتبع للحوالات المنفذة
function createTrackingRow(transfer) {
    const daysClass = transfer.days_since_execution > 30 ? 'danger' :
                     transfer.days_since_execution > 14 ? 'warning' : 'success';

    // طباعة البيانات للتشخيص
    console.log('🔍 بيانات الحوالة:', {
        id: transfer.id,
        request_number: transfer.request_number,
        original_amount: transfer.original_amount,
        total_distributed_amount: transfer.total_distributed_amount,
        actual_suppliers_count: transfer.actual_suppliers_count,
        suppliers_list: transfer.suppliers_list
    });

    return `
        <tr data-transfer-id="${transfer.id}">
            <td>
                <strong class="text-primary">${transfer.transfer_number || 'غير محدد'}</strong>
            </td>
            <td>
                <strong>${transfer.request_number}</strong>
                <br><small class="text-muted">${formatDate(transfer.created_at)}</small>
            </td>
            <td>
                <div class="fw-bold text-primary">${transfer.branch_name}</div>
                <small class="text-muted">الفرع</small>
            </td>
            <td>
                <div class="fw-bold">${transfer.money_changer_bank_name}</div>
                <span class="badge ${transfer.transfer_type === 'بنك' ? 'bg-primary' : 'bg-success'} mt-1">
                    <i class="fas ${transfer.transfer_type === 'بنك' ? 'fa-university' : 'fa-exchange-alt'}"></i>
                    ${transfer.transfer_type || 'غير محدد'}
                </span>
            </td>
            <td>
                <span class="amount-display">${formatAmount(transfer.original_amount)} ${transfer.currency}</span>
            </td>
            <td>
                <span class="amount-display text-success">
                    ${transfer.total_distributed_amount ? formatAmount(transfer.total_distributed_amount) : '0'} ${transfer.currency}
                </span>
                ${transfer.total_distributed_amount && transfer.total_distributed_amount !== transfer.original_amount ?
                    '<br><small class="text-warning"><i class="fas fa-exclamation-triangle"></i> فرق في المبلغ</small>' : ''}
            </td>
            <td>
                <span class="badge bg-info">${transfer.actual_suppliers_count || 0}</span>
                ${transfer.total_suppliers && transfer.total_suppliers !== transfer.actual_suppliers_count ?
                    `<br><small class="text-muted">مخطط: ${transfer.total_suppliers}</small>` : ''}
            </td>
            <td>
                <code class="text-primary" title="المستفيد: ${transfer.beneficiary_name}">${transfer.execution_reference || 'غير محدد'}</code>
                <br><small class="text-muted">${transfer.execution_method || 'غير محدد'}</small>
                <br><small class="text-info" title="البنك: ${transfer.bank_name}"><i class="fas fa-user"></i> ${transfer.beneficiary_name}</small>
            </td>
            <td>
                <strong>${formatDate(transfer.execution_date)}</strong>
                <br><small class="text-${daysClass}">${transfer.days_since_execution} يوم مضى</small>
            </td>
            <td>
                <div class="suppliers-list" style="max-width: 200px; font-size: 0.85rem;">
                    ${transfer.suppliers_list ?
                        `<div class="text-success">${transfer.suppliers_list}</div>` :
                        '<div class="text-muted"><i class="fas fa-exclamation-circle"></i> لا توجد بيانات موردين</div>'
                    }
                </div>
            </td>
            <td>
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-outline-info"
                            onclick="showExecutionDetails(${transfer.id})" title="تفاصيل التنفيذ">
                        <i class="fas fa-cogs"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary"
                            onclick="viewRequestDetails(${transfer.id})" title="عرض الطلب">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </td>
        </tr>
    `;
}

// تحديث إحصائيات الحوالات المنفذة
function updateStatistics(stats) {
    console.log('📊 تحديث الإحصائيات:', stats);

    // تحديث كل إحصائية مع التحقق من وجود البيانات
    const totalExecuted = stats.total_executed || 0;
    const totalAmount = stats.total_amount || 0;
    const currencies = stats.currencies || 'ريال';
    const totalSuppliers = stats.total_suppliers || 0;
    const todayExecutions = stats.today_executions || 0;

    // تحديث العناصر في الصفحة
    $('#totalExecutedTransfers').text(totalExecuted);
    $('#totalExecutedAmount').text(formatAmount(totalAmount) + ' ' + currencies);
    $('#totalSuppliers').text(totalSuppliers);
    $('#todayExecutions').text(todayExecutions);

    console.log('✅ تم تحديث عناصر الإحصائيات:', {
        totalExecuted, totalAmount, currencies, totalSuppliers, todayExecutions
    });

    // إخفاء البطاقات بشكل دائم كما هو مطلوب
    $('#statisticsCards').hide();
    console.log('📊 تم إخفاء بطاقات الإحصائيات كما هو مطلوب');
}

// تطبيق الفلاتر
function applyFilters() {
    loadTrackingData(); // إعادة تحميل البيانات مع الفلاتر الجديدة
}

// عرض تفاصيل التتبع
function showTrackingDetails(requestNumber) {
    $('#trackModalBody').html(`
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 mb-0">جاري تحميل بيانات التتبع...</p>
        </div>
    `);

    $('#trackModal').modal('show');

    $.get(`/transfers/api/track-request/${requestNumber}`)
        .done(function(response) {
            if (response.success) {
                displayTrackingDetails(response.data);
            } else {
                $('#trackModalBody').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ${response.message}
                    </div>
                `);
            }
        })
        .fail(function() {
            $('#trackModalBody').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    حدث خطأ أثناء تحميل بيانات التتبع
                </div>
            `);
        });
}

// عرض تفاصيل التتبع في Modal
function displayTrackingDetails(data) {
    const request = data.request_info;
    const history = data.tracking_history;
    const execution = data.execution_data;

    let historyHtml = '';
    if (history && history.length > 0) {
        history.forEach(function(item) {
            const milestoneIcon = item.is_milestone ? 'fas fa-star text-warning' : 'fas fa-circle text-muted';
            historyHtml += `
                <div class="d-flex mb-3">
                    <div class="me-3">
                        <i class="${milestoneIcon}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${item.status_name || item.status_description}</h6>
                        <p class="text-muted mb-1">${formatDateTime(item.tracking_date)}</p>
                        ${item.notes ? `<p class="small mb-0">${item.notes}</p>` : ''}
                        ${item.location_info ? `<small class="text-info">${item.location_info}</small>` : ''}
                    </div>
                </div>
            `;
        });
    } else {
        historyHtml = '<p class="text-muted">لا يوجد سجل تتبع متاح</p>';
    }

    let executionHtml = '';
    if (execution) {
        executionHtml = `
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-play me-2"></i>بيانات التنفيذ</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>رقم المرجع:</strong> ${execution.execution_reference}
                        </div>
                        <div class="col-md-6">
                            <strong>تاريخ التنفيذ:</strong> ${formatDateTime(execution.execution_date)}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <strong>مبلغ التنفيذ:</strong> ${formatAmount(execution.execution_amount)} ${execution.execution_currency}
                        </div>
                        <div class="col-md-6">
                            <strong>المبلغ الصافي:</strong> ${formatAmount(execution.net_amount)} ${execution.execution_currency}
                        </div>
                    </div>
                    ${execution.receipt_number ? `<div class="mt-2"><strong>رقم الإيصال:</strong> ${execution.receipt_number}</div>` : ''}
                    ${execution.execution_notes ? `<div class="mt-2"><strong>ملاحظات:</strong> ${execution.execution_notes}</div>` : ''}
                </div>
            </div>
        `;
    }

    $('#trackModalBody').html(`
        <div class="row mb-3">
            <div class="col-md-6">
                <strong>رقم الطلب:</strong> ${request.request_number}
            </div>
            <div class="col-md-6">
                <strong>المستفيد:</strong> ${request.beneficiary_name}
            </div>
        </div>
        <div class="row mb-3">
            <div class="col-md-6">
                <strong>المبلغ:</strong> ${formatAmount(request.amount)} ${request.currency}
            </div>
            <div class="col-md-6">
                <strong>الحالة الحالية:</strong> <span class="badge status-badge status-${request.status}">${request.status_name}</span>
            </div>
        </div>

        <hr>

        <h6><i class="fas fa-history me-2"></i>سجل التتبع</h6>
        ${historyHtml}

        ${executionHtml}
    `);
}

// تحديث البيانات
function refreshData() {
    loadTrackingData();
    loadStatistics();
}

// تصدير البيانات
function exportData() {
    // يمكن تطوير هذه الوظيفة لاحقاً
    alert('وظيفة التصدير قيد التطوير');
}

// عرض تفاصيل الطلب
function viewRequestDetails(requestId) {
    window.open(`/transfers/edit-request/${requestId}`, '_blank');
}

// حساب إجمالي المبالغ المعروضة
function calculateDisplayedTotal(data) {
    let totalAmounts = {};

    data.forEach(function(transfer) {
        const amount = transfer.original_amount || 0;
        const currency = transfer.currency || 'ريال';

        if (!totalAmounts[currency]) {
            totalAmounts[currency] = 0;
        }
        totalAmounts[currency] += amount;
    });

    // عرض الإجمالي
    if (Object.keys(totalAmounts).length === 0) {
        $('#totalAmountValue').text('0');
        $('#totalAmountCurrency').text('ريال');
    } else if (Object.keys(totalAmounts).length === 1) {
        // عملة واحدة فقط
        const currency = Object.keys(totalAmounts)[0];
        const amount = totalAmounts[currency];
        $('#totalAmountValue').text(formatAmount(amount));
        $('#totalAmountCurrency').text(currency);
    } else {
        // عملات متعددة
        const totalText = Object.keys(totalAmounts).map(currency =>
            `${formatAmount(totalAmounts[currency])} ${currency}`
        ).join(' + ');
        $('#totalAmountValue').text(totalText);
        $('#totalAmountCurrency').text('');
    }

    console.log('💰 إجمالي المبالغ المعروضة:', totalAmounts);
}

// دوال مساعدة
function formatAmount(amount) {
    // عرض الأرقام بالإنجليزية مع فواصل
    return new Intl.NumberFormat('en-US').format(amount);
}

function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    // عرض التاريخ بالإنجليزية
    return date.toLocaleDateString('en-US');
}

function formatDateTime(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    // عرض التاريخ والوقت بالإنجليزية
    return date.toLocaleString('en-US');
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showError(message) {
    alert(message); // مؤقت
}

// دوال التصدير المحسنة
function exportToExcel() {
    alert('تصدير Excel - قيد التطوير');
}

function exportToPDF() {
    alert('تصدير PDF - قيد التطوير');
}

function printReport() {
    window.print();
}

// دوال التنقل في مسار التنقل
function navigateToHome() {
    window.location.href = '/';
}

function navigateToTransfersDashboard() {
    window.location.href = '/transfers/dashboard';
}
</script>

</body>
</html>
