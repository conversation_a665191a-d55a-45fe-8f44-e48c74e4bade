{% extends "base.html" %}

{% block title %}{% if edit_mode %}تعديل مخلص جمركي{% else %}إضافة مخلص جمركي جديد{% endif %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان الرئيسي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        {% if edit_mode %}
                            <i class="fas fa-edit text-primary me-2"></i>
                            تعديل مخلص جمركي
                        {% else %}
                            <i class="fas fa-plus text-primary me-2"></i>
                            إضافة مخلص جمركي جديد
                        {% endif %}
                    </h2>
                    <p class="text-muted mb-0">
                        {% if edit_mode %}
                            تعديل بيانات المخلص: {{ agent.agent_name }}
                        {% else %}
                            إضافة مخلص جمركي جديد إلى النظام
                        {% endif %}
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('shipments.customs_agents_management') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة لإدارة المخلصين
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة المخلص -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        {% if edit_mode %}
                            <i class="fas fa-user-edit me-2"></i>
                            تعديل بيانات المخلص
                        {% else %}
                            <i class="fas fa-user-plus me-2"></i>
                            بيانات المخلص الجديد
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <form id="addAgentForm" onsubmit="saveAgent(event)">
                        {% if edit_mode %}
                            <input type="hidden" name="agent_id" value="{{ agent.id }}">
                        {% endif %}
                        <div class="row">
                            <!-- المعلومات الأساسية -->
                            <div class="col-12 mb-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-info-circle me-1"></i>
                                    المعلومات الأساسية
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم المخلص <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="agentName" name="agent_name"
                                       value="{{ agent.agent_name if agent else '' }}" required>
                                <div class="invalid-feedback">يرجى إدخال اسم المخلص</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم الشركة</label>
                                <input type="text" class="form-control" id="companyName" name="company_name"
                                       value="{{ agent.company_name if agent else '' }}">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الترخيص <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="licenseNumber" name="license_number"
                                       value="{{ agent.license_number if agent else '' }}" required>
                                <div class="invalid-feedback">يرجى إدخال رقم الترخيص</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ انتهاء الترخيص</label>
                                <input type="date" class="form-control" id="licenseExpiryDate" name="license_expiry_date"
                                       value="{{ agent.license_expiry_date if agent else '' }}">
                            </div>
                            
                            <!-- معلومات الاتصال -->
                            <div class="col-12 mb-4 mt-3">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-phone me-1"></i>
                                    معلومات الاتصال
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       value="{{ agent.phone if agent else '' }}" required>
                                <div class="invalid-feedback">يرجى إدخال رقم الهاتف</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الجوال</label>
                                <input type="tel" class="form-control" id="mobile" name="mobile"
                                       value="{{ agent.mobile if agent else '' }}">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email"
                                       value="{{ agent.email if agent else '' }}">
                                <div class="invalid-feedback">يرجى إدخال بريد إلكتروني صحيح</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">التخصص <span class="text-danger">*</span></label>
                                <select class="form-select" id="specialization" name="specialization" required>
                                    <option value="">اختر التخصص...</option>
                                    <option value="عام" {{ 'selected' if agent and agent.specialization == 'عام' else '' }}>عام</option>
                                    <option value="سيارات" {{ 'selected' if agent and agent.specialization == 'سيارات' else '' }}>سيارات</option>
                                    <option value="أدوية" {{ 'selected' if agent and agent.specialization == 'أدوية' else '' }}>أدوية</option>
                                    <option value="أغذية" {{ 'selected' if agent and agent.specialization == 'أغذية' else '' }}>أغذية</option>
                                    <option value="كيماويات" {{ 'selected' if agent and agent.specialization == 'كيماويات' else '' }}>كيماويات</option>
                                    <option value="إلكترونيات" {{ 'selected' if agent and agent.specialization == 'إلكترونيات' else '' }}>إلكترونيات</option>
                                    <option value="منسوجات" {{ 'selected' if agent and agent.specialization == 'منسوجات' else '' }}>منسوجات</option>
                                    <option value="معادن" {{ 'selected' if agent and agent.specialization == 'معادن' else '' }}>معادن</option>
                                </select>
                                <div class="invalid-feedback">يرجى اختيار التخصص</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الفرع <span class="text-danger">*</span></label>
                                <select class="form-select" id="branchId" name="branch_id" required>
                                    <option value="">اختر الفرع...</option>
                                    <!-- سيتم تحميل الفروع ديناميكياً -->
                                </select>
                                <div class="invalid-feedback">يرجى اختيار الفرع</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">المنفذ الجمركي <span class="text-danger">*</span></label>
                                <select class="form-select" id="customsPortId" name="customs_port_id" required>
                                    <option value="">اختر المنفذ الجمركي...</option>
                                    <!-- سيتم تحميل المنافذ ديناميكياً -->
                                </select>
                                <div class="invalid-feedback">يرجى اختيار المنفذ الجمركي</div>
                            </div>
                            
                            <!-- العنوان والملاحظات -->
                            <div class="col-12 mb-4 mt-3">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    معلومات إضافية
                                </h6>
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="2"
                                          placeholder="العنوان الكامل للمخلص أو الشركة">{{ agent.address if agent else '' }}</textarea>
                            </div>
                            
                            <div class="col-12 mb-4">
                                <label class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"
                                          placeholder="أي ملاحظات إضافية عن المخلص">{{ agent.notes if agent else '' }}</textarea>
                            </div>
                        </div>
                        
                        <!-- أزرار الحفظ -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ url_for('shipments.customs_agents_management') }}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="button" class="btn btn-outline-primary" onclick="resetForm()">
                                        <i class="fas fa-undo me-1"></i>
                                        إعادة تعيين
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        {% if edit_mode %}
                                            تحديث المخلص
                                        {% else %}
                                            حفظ المخلص
                                        {% endif %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function saveAgent(event) {
    event.preventDefault();
    const isEditMode = document.querySelector('input[name="agent_id"]') !== null;
    console.log(isEditMode ? '💾 تحديث مخلص موجود' : '💾 حفظ مخلص جديد');
    
    const form = document.getElementById('addAgentForm');
    const formData = new FormData(form);
    
    // التحقق من البيانات المطلوبة
    const requiredFields = ['agent_name', 'license_number', 'phone', 'specialization', 'branch_id', 'customs_port_id'];
    let isValid = true;
    
    // إزالة أي رسائل خطأ سابقة
    form.querySelectorAll('.is-invalid').forEach(input => {
        input.classList.remove('is-invalid');
    });
    
    requiredFields.forEach(field => {
        const input = form.querySelector(`[name="${field}"]`);
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            isValid = false;
        }
    });
    
    // التحقق من البريد الإلكتروني إذا تم إدخاله
    const emailInput = form.querySelector('[name="email"]');
    if (emailInput.value.trim() && !isValidEmail(emailInput.value)) {
        emailInput.classList.add('is-invalid');
        isValid = false;
    }
    
    if (!isValid) {
        showMessage('يرجى ملء جميع الحقول المطلوبة بشكل صحيح', 'danger');
        
        // التركيز على أول حقل خاطئ
        const firstInvalidInput = form.querySelector('.is-invalid');
        if (firstInvalidInput) {
            firstInvalidInput.focus();
        }
        
        return;
    }
    
    // تحويل البيانات إلى JSON
    const data = {};
    formData.forEach((value, key) => {
        data[key] = value;
    });
    
    // إظهار مؤشر التحميل
    const saveBtn = form.querySelector('button[type="submit"]');
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = `<i class="fas fa-spinner fa-spin me-1"></i>جاري ${isEditMode ? 'التحديث' : 'الحفظ'}...`;
    saveBtn.disabled = true;

    // تحديد URL والطريقة حسب نوع العملية
    const agentId = data.agent_id;
    const url = isEditMode ? `/shipments/api/customs-agents/${agentId}` : '/shipments/api/customs-agents';
    const method = isEditMode ? 'PUT' : 'POST';

    // إرسال البيانات
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        console.log('📊 نتيجة الحفظ:', result);
        
        if (result.success) {
            const successMessage = isEditMode ? 'تم تحديث المخلص بنجاح' : 'تم إضافة المخلص بنجاح';
            showMessage(successMessage, 'success');

            // الانتقال إلى صفحة إدارة المخلصين بعد 2 ثانية
            setTimeout(() => {
                window.location.href = '/shipments/customs-agents-management';
            }, 2000);
        } else {
            const errorMessage = isEditMode ? 'خطأ في تحديث المخلص: ' : 'خطأ في إضافة المخلص: ';
            showMessage(errorMessage + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الشبكة:', error);
        showMessage('حدث خطأ في الاتصال بالخادم', 'danger');
    })
    .finally(() => {
        // إعادة تعيين زر الحفظ
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    });
}

function resetForm() {
    const form = document.getElementById('addAgentForm');
    form.reset();
    
    // إزالة أي رسائل خطأ
    form.querySelectorAll('.is-invalid').forEach(input => {
        input.classList.remove('is-invalid');
    });
    
    showMessage('تم إعادة تعيين النموذج', 'info');
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// وظيفة إظهار الرسائل
function showMessage(message, type = 'info') {
    // إزالة الرسائل السابقة
    const existingAlerts = document.querySelectorAll('.agent-alert');
    existingAlerts.forEach(alert => alert.remove());
    
    // إنشاء رسالة جديدة
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show agent-alert`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // إضافة الرسالة في أعلى الصفحة
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
        
        // إزالة الرسالة تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// تحميل الفروع من قاعدة البيانات
function loadBranches() {
    console.log('📋 تحميل قائمة الفروع...');

    fetch('/shipments/api/branches')
        .then(response => response.json())
        .then(data => {
            const branchSelect = document.getElementById('branchId');
            branchSelect.innerHTML = '<option value="">اختر الفرع...</option>';

            if (data.success && data.branches) {
                data.branches.forEach(branch => {
                    const option = document.createElement('option');
                    option.value = branch.brn_no;
                    option.textContent = branch.brn_lname;

                    // إذا كان في وضع التعديل، تحديد الفرع المحفوظ
                    {% if agent and agent.branch_id %}
                    if (branch.brn_no == {{ agent.branch_id }}) {
                        option.selected = true;
                    }
                    {% endif %}

                    branchSelect.appendChild(option);
                });
                console.log(`✅ تم تحميل ${data.branches.length} فرع`);
            } else {
                console.error('❌ فشل في تحميل الفروع:', data.message);
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل الفروع:', error);
        });
}

// تحميل المنافذ الجمركية من قاعدة البيانات
function loadCustomsPorts() {
    console.log('🚢 تحميل قائمة المنافذ الجمركية...');

    fetch('/shipments/api/customs-ports')
        .then(response => response.json())
        .then(data => {
            const portSelect = document.getElementById('customsPortId');
            portSelect.innerHTML = '<option value="">اختر المنفذ الجمركي...</option>';

            if (data.success && data.ports) {
                data.ports.forEach(port => {
                    const option = document.createElement('option');
                    option.value = port.id;
                    option.textContent = `${port.port_name_ar} (${port.port_code})`;

                    // إذا كان في وضع التعديل، تحديد المنفذ المحفوظ
                    {% if agent and agent.customs_port_id %}
                    if (port.id == {{ agent.customs_port_id }}) {
                        option.selected = true;
                    }
                    {% endif %}

                    portSelect.appendChild(option);
                });
                console.log(`✅ تم تحميل ${data.ports.length} منفذ جمركي`);
            } else {
                console.error('❌ فشل في تحميل المنافذ الجمركية:', data.message);
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل المنافذ الجمركية:', error);
        });
}

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    console.log('📝 تم تحميل صفحة إضافة مخلص جديد');

    // تحميل البيانات المرجعية
    loadBranches();
    loadCustomsPorts();

    // التركيز على أول حقل
    const firstInput = document.getElementById('agentName');
    if (firstInput) {
        firstInput.focus();
    }
});
</script>
{% endblock %}
