"""
خدمة تقارير الأرصدة والحوالات
Transfer and Balance Reports Service
"""

import logging
from typing import List, Dict, Any, Optional
from decimal import Decimal
from datetime import datetime, date
import cx_Oracle

from app.database import get_oracle_connection
from app.utils.exceptions import ValidationError, DatabaseError

logger = logging.getLogger(__name__)

class TransferReportsService:
    """خدمة تقارير الحوالات والأرصدة"""
    
    def __init__(self):
        self.db = get_oracle_connection()
    
    def get_balances_report(self, entity_type: str = None, currency: str = None,
                           include_zero_balances: bool = False) -> Dict[str, Any]:
        """
        تقرير الأرصدة الحالية
        
        Args:
            entity_type: نوع الكيان (SUPPLIER, MONEY_CHANGER, BANK)
            currency: العملة
            include_zero_balances: تضمين الأرصدة الصفرية
            
        Returns:
            Dict مع تقرير الأرصدة
        """
        try:
            query = """
                SELECT 
                    cb.entity_type_code,
                    cb.entity_id,
                    CASE cb.entity_type_code
                        WHEN 'SUPPLIER' THEN s.name
                        WHEN 'MONEY_CHANGER' THEN mc.name
                        WHEN 'BANK' THEN b.name
                    END as entity_name,
                    CASE cb.entity_type_code
                        WHEN 'SUPPLIER' THEN s.code
                        WHEN 'MONEY_CHANGER' THEN mc.code
                        WHEN 'BANK' THEN b.code
                    END as entity_code,
                    cb.currency_code,
                    cb.opening_balance,
                    cb.debit_amount,
                    cb.credit_amount,
                    cb.current_balance,
                    cb.total_transactions_count,
                    cb.last_transaction_date,
                    -- حساب الحوالات المعلقة للصرافين
                    CASE 
                        WHEN cb.entity_type_code = 'MONEY_CHANGER' THEN
                            NVL((SELECT SUM(amount) FROM transfers 
                                 WHERE money_changer_id = cb.entity_id 
                                 AND currency = cb.currency_code 
                                 AND status = 'approved'), 0)
                        ELSE 0
                    END as pending_transfers,
                    -- حساب الرصيد المتاح
                    CASE 
                        WHEN cb.entity_type_code = 'MONEY_CHANGER' THEN
                            cb.current_balance - NVL((SELECT SUM(amount) FROM transfers 
                                                     WHERE money_changer_id = cb.entity_id 
                                                     AND currency = cb.currency_code 
                                                     AND status = 'approved'), 0)
                        ELSE cb.current_balance
                    END as available_balance
                FROM CURRENT_BALANCES cb
                LEFT JOIN suppliers s ON cb.entity_type_code = 'SUPPLIER' AND cb.entity_id = s.id
                LEFT JOIN money_changers mc ON cb.entity_type_code = 'MONEY_CHANGER' AND cb.entity_id = mc.id
                LEFT JOIN banks b ON cb.entity_type_code = 'BANK' AND cb.entity_id = b.id
                WHERE 1=1
            """
            
            params = {}
            
            if entity_type:
                query += " AND cb.entity_type_code = :entity_type"
                params['entity_type'] = entity_type.upper()
            
            if currency:
                query += " AND cb.currency_code = :currency"
                params['currency'] = currency.upper()
            
            if not include_zero_balances:
                query += " AND cb.current_balance != 0"
            
            query += " ORDER BY cb.entity_type_code, entity_name, cb.currency_code"
            
            balances = self.db.fetch_all(query, params)
            
            # حساب الإحصائيات
            total_entities = len(balances)
            by_entity_type = {}
            by_currency = {}
            
            for balance in balances:
                entity_type = balance['ENTITY_TYPE_CODE']
                currency = balance['CURRENCY_CODE']
                current_balance = float(balance['CURRENT_BALANCE'] or 0)
                
                # تجميع حسب نوع الكيان
                if entity_type not in by_entity_type:
                    by_entity_type[entity_type] = {
                        'count': 0,
                        'total_balance': 0,
                        'positive_balances': 0,
                        'negative_balances': 0
                    }
                
                by_entity_type[entity_type]['count'] += 1
                by_entity_type[entity_type]['total_balance'] += current_balance
                
                if current_balance > 0:
                    by_entity_type[entity_type]['positive_balances'] += 1
                elif current_balance < 0:
                    by_entity_type[entity_type]['negative_balances'] += 1
                
                # تجميع حسب العملة
                if currency not in by_currency:
                    by_currency[currency] = {
                        'count': 0,
                        'total_balance': 0
                    }
                
                by_currency[currency]['count'] += 1
                by_currency[currency]['total_balance'] += current_balance
            
            return {
                'balances': balances,
                'summary': {
                    'total_entities': total_entities,
                    'by_entity_type': by_entity_type,
                    'by_currency': by_currency,
                    'generated_at': datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"خطأ في تقرير الأرصدة: {str(e)}")
            raise DatabaseError(f"فشل في إنشاء تقرير الأرصدة: {str(e)}")
    
    def get_transfers_summary_report(self, date_from: date = None, date_to: date = None,
                                   status: str = None, currency: str = None) -> Dict[str, Any]:
        """
        تقرير ملخص الحوالات
        
        Args:
            date_from: تاريخ البداية
            date_to: تاريخ النهاية
            status: حالة الحوالة
            currency: العملة
            
        Returns:
            Dict مع ملخص الحوالات
        """
        try:
            query = """
                SELECT 
                    t.id,
                    t.request_number,
                    t.beneficiary_name,
                    t.bank_name,
                    t.amount,
                    t.currency,
                    t.status,
                    t.priority_level,
                    t.created_at,
                    t.approved_at,
                    t.executed_at,
                    t.cancelled_at,
                    mc.name as money_changer_name,
                    t.supplier_distributions_count,
                    t.total_distributed_amount,
                    -- حساب الأيام منذ الإنشاء
                    TRUNC(SYSDATE - t.created_at) as days_since_creation,
                    -- حساب الأيام منذ الاعتماد
                    CASE 
                        WHEN t.approved_at IS NOT NULL THEN TRUNC(SYSDATE - t.approved_at)
                        ELSE NULL
                    END as days_since_approval,
                    -- حساب الأيام منذ التنفيذ
                    CASE 
                        WHEN t.executed_at IS NOT NULL THEN TRUNC(SYSDATE - t.executed_at)
                        ELSE NULL
                    END as days_since_execution
                FROM transfers t
                LEFT JOIN money_changers mc ON t.money_changer_id = mc.id
                WHERE 1=1
            """
            
            params = {}
            
            if date_from:
                query += " AND t.created_at >= :date_from"
                params['date_from'] = date_from
            
            if date_to:
                query += " AND t.created_at <= :date_to"
                params['date_to'] = date_to
            
            if status:
                query += " AND t.status = :status"
                params['status'] = status
            
            if currency:
                query += " AND t.currency = :currency"
                params['currency'] = currency.upper()
            
            query += " ORDER BY t.created_at DESC"
            
            transfers = self.db.fetch_all(query, params)
            
            # حساب الإحصائيات
            total_transfers = len(transfers)
            total_amount = sum(float(t['AMOUNT'] or 0) for t in transfers)
            
            by_status = {}
            by_currency = {}
            by_priority = {}
            
            for transfer in transfers:
                status = transfer['STATUS']
                currency = transfer['CURRENCY']
                priority = transfer['PRIORITY_LEVEL']
                amount = float(transfer['AMOUNT'] or 0)
                
                # تجميع حسب الحالة
                if status not in by_status:
                    by_status[status] = {'count': 0, 'total_amount': 0}
                by_status[status]['count'] += 1
                by_status[status]['total_amount'] += amount
                
                # تجميع حسب العملة
                if currency not in by_currency:
                    by_currency[currency] = {'count': 0, 'total_amount': 0}
                by_currency[currency]['count'] += 1
                by_currency[currency]['total_amount'] += amount
                
                # تجميع حسب الأولوية
                if priority not in by_priority:
                    by_priority[priority] = {'count': 0, 'total_amount': 0}
                by_priority[priority]['count'] += 1
                by_priority[priority]['total_amount'] += amount
            
            return {
                'transfers': transfers,
                'summary': {
                    'total_transfers': total_transfers,
                    'total_amount': total_amount,
                    'by_status': by_status,
                    'by_currency': by_currency,
                    'by_priority': by_priority,
                    'date_range': {
                        'from': date_from.isoformat() if date_from else None,
                        'to': date_to.isoformat() if date_to else None
                    },
                    'generated_at': datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"خطأ في تقرير ملخص الحوالات: {str(e)}")
            raise DatabaseError(f"فشل في إنشاء تقرير ملخص الحوالات: {str(e)}")
    
    def get_supplier_activity_report(self, supplier_id: int = None, 
                                   date_from: date = None, date_to: date = None) -> Dict[str, Any]:
        """
        تقرير نشاط الموردين
        
        Args:
            supplier_id: معرف المورد (اختياري)
            date_from: تاريخ البداية
            date_to: تاريخ النهاية
            
        Returns:
            Dict مع تقرير نشاط الموردين
        """
        try:
            query = """
                SELECT 
                    s.id as supplier_id,
                    s.name as supplier_name,
                    s.code as supplier_code,
                    tsd.currency_code,
                    COUNT(tsd.id) as transfers_count,
                    SUM(tsd.amount) as total_amount,
                    AVG(tsd.amount) as average_amount,
                    MIN(tsd.amount) as min_amount,
                    MAX(tsd.amount) as max_amount,
                    MIN(tsd.created_at) as first_transfer_date,
                    MAX(tsd.created_at) as last_transfer_date,
                    -- الرصيد الحالي
                    NVL(cb.current_balance, 0) as current_balance,
                    -- عدد المعاملات الإجمالي
                    NVL(cb.total_transactions_count, 0) as total_transactions
                FROM suppliers s
                LEFT JOIN transfer_supplier_distributions tsd ON s.id = tsd.supplier_id
                LEFT JOIN transfers t ON tsd.transfer_id = t.id AND t.status = 'executed'
                LEFT JOIN CURRENT_BALANCES cb ON s.id = cb.entity_id 
                    AND cb.entity_type_code = 'SUPPLIER' 
                    AND cb.currency_code = tsd.currency_code
                WHERE 1=1
            """
            
            params = {}
            
            if supplier_id:
                query += " AND s.id = :supplier_id"
                params['supplier_id'] = supplier_id
            
            if date_from:
                query += " AND tsd.created_at >= :date_from"
                params['date_from'] = date_from
            
            if date_to:
                query += " AND tsd.created_at <= :date_to"
                params['date_to'] = date_to
            
            query += """
                GROUP BY s.id, s.name, s.code, tsd.currency_code, cb.current_balance, cb.total_transactions_count
                HAVING COUNT(tsd.id) > 0
                ORDER BY total_amount DESC
            """
            
            suppliers = self.db.fetch_all(query, params)
            
            # حساب الإحصائيات
            total_suppliers = len(suppliers)
            total_transfers = sum(int(s['TRANSFERS_COUNT'] or 0) for s in suppliers)
            total_amount = sum(float(s['TOTAL_AMOUNT'] or 0) for s in suppliers)
            
            return {
                'suppliers': suppliers,
                'summary': {
                    'total_suppliers': total_suppliers,
                    'total_transfers': total_transfers,
                    'total_amount': total_amount,
                    'average_per_supplier': total_amount / total_suppliers if total_suppliers > 0 else 0,
                    'date_range': {
                        'from': date_from.isoformat() if date_from else None,
                        'to': date_to.isoformat() if date_to else None
                    },
                    'generated_at': datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"خطأ في تقرير نشاط الموردين: {str(e)}")
            raise DatabaseError(f"فشل في إنشاء تقرير نشاط الموردين: {str(e)}")
    
    def get_money_changer_activity_report(self, money_changer_id: int = None,
                                        date_from: date = None, date_to: date = None) -> Dict[str, Any]:
        """
        تقرير نشاط الصرافين
        
        Args:
            money_changer_id: معرف الصراف (اختياري)
            date_from: تاريخ البداية
            date_to: تاريخ النهاية
            
        Returns:
            Dict مع تقرير نشاط الصرافين
        """
        try:
            query = """
                SELECT 
                    mc.id as money_changer_id,
                    mc.name as money_changer_name,
                    mc.code as money_changer_code,
                    t.currency,
                    COUNT(t.id) as transfers_count,
                    SUM(t.amount) as total_amount,
                    AVG(t.amount) as average_amount,
                    MIN(t.amount) as min_amount,
                    MAX(t.amount) as max_amount,
                    MIN(t.executed_at) as first_transfer_date,
                    MAX(t.executed_at) as last_transfer_date,
                    -- الرصيد الحالي
                    NVL(cb.current_balance, 0) as current_balance,
                    -- الحوالات المعلقة
                    NVL((SELECT SUM(amount) FROM transfers 
                         WHERE money_changer_id = mc.id 
                         AND currency = t.currency 
                         AND status = 'approved'), 0) as pending_transfers,
                    -- الرصيد المتاح
                    NVL(cb.current_balance, 0) - NVL((SELECT SUM(amount) FROM transfers 
                                                     WHERE money_changer_id = mc.id 
                                                     AND currency = t.currency 
                                                     AND status = 'approved'), 0) as available_balance
                FROM money_changers mc
                LEFT JOIN transfers t ON mc.id = t.money_changer_id AND t.status = 'executed'
                LEFT JOIN CURRENT_BALANCES cb ON mc.id = cb.entity_id 
                    AND cb.entity_type_code = 'MONEY_CHANGER' 
                    AND cb.currency_code = t.currency
                WHERE 1=1
            """
            
            params = {}
            
            if money_changer_id:
                query += " AND mc.id = :money_changer_id"
                params['money_changer_id'] = money_changer_id
            
            if date_from:
                query += " AND t.executed_at >= :date_from"
                params['date_from'] = date_from
            
            if date_to:
                query += " AND t.executed_at <= :date_to"
                params['date_to'] = date_to
            
            query += """
                GROUP BY mc.id, mc.name, mc.code, t.currency, cb.current_balance
                HAVING COUNT(t.id) > 0
                ORDER BY total_amount DESC
            """
            
            money_changers = self.db.fetch_all(query, params)
            
            # حساب الإحصائيات
            total_money_changers = len(money_changers)
            total_transfers = sum(int(mc['TRANSFERS_COUNT'] or 0) for mc in money_changers)
            total_amount = sum(float(mc['TOTAL_AMOUNT'] or 0) for mc in money_changers)
            
            return {
                'money_changers': money_changers,
                'summary': {
                    'total_money_changers': total_money_changers,
                    'total_transfers': total_transfers,
                    'total_amount': total_amount,
                    'average_per_money_changer': total_amount / total_money_changers if total_money_changers > 0 else 0,
                    'date_range': {
                        'from': date_from.isoformat() if date_from else None,
                        'to': date_to.isoformat() if date_to else None
                    },
                    'generated_at': datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"خطأ في تقرير نشاط الصرافين: {str(e)}")
            raise DatabaseError(f"فشل في إنشاء تقرير نشاط الصرافين: {str(e)}")


    def get_daily_summary_report(self, target_date: date = None) -> Dict[str, Any]:
        """
        تقرير الملخص اليومي

        Args:
            target_date: التاريخ المطلوب (افتراضي: اليوم)

        Returns:
            Dict مع الملخص اليومي
        """
        try:
            if target_date is None:
                target_date = date.today()

            # الحصول على إحصائيات الحوالات لليوم
            query = """
                SELECT
                    currency,
                    COUNT(*) as total_transfers,
                    COUNT(CASE WHEN status = 'created' THEN 1 END) as created_count,
                    COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
                    COUNT(CASE WHEN status = 'executed' THEN 1 END) as executed_count,
                    COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_count,
                    SUM(amount) as total_amount,
                    SUM(CASE WHEN status = 'executed' THEN amount ELSE 0 END) as executed_amount,
                    SUM(CASE WHEN status = 'cancelled' THEN amount ELSE 0 END) as cancelled_amount,
                    AVG(amount) as average_amount
                FROM transfers
                WHERE DATE(created_at) = :target_date
                GROUP BY currency
                ORDER BY total_amount DESC
            """

            daily_stats = self.db.fetch_all(query, {'target_date': target_date})

            # الحصول على إحصائيات الأرصدة
            balance_query = """
                SELECT
                    entity_type_code,
                    currency_code,
                    COUNT(*) as entities_count,
                    SUM(current_balance) as total_balance,
                    SUM(CASE WHEN current_balance > 0 THEN current_balance ELSE 0 END) as positive_balance,
                    SUM(CASE WHEN current_balance < 0 THEN ABS(current_balance) ELSE 0 END) as negative_balance
                FROM CURRENT_BALANCES
                GROUP BY entity_type_code, currency_code
                ORDER BY entity_type_code, currency_code
            """

            balance_stats = self.db.fetch_all(balance_query)

            return {
                'date': target_date.isoformat(),
                'transfers_summary': daily_stats,
                'balances_summary': balance_stats,
                'generated_at': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"خطأ في تقرير الملخص اليومي: {str(e)}")
            raise DatabaseError(f"فشل في إنشاء تقرير الملخص اليومي: {str(e)}")


# إنشاء instance عام للاستخدام
transfer_reports = TransferReportsService()
