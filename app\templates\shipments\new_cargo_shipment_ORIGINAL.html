{% extends "base.html" %}

{% block content %}

<script>
// دالة فتح البحث المتقدم
function openAdvancedSupplierSearch(fieldType) {
    console.log(`🔍 فتح البحث المتقدم للحقل: ${fieldType}`);
    currentSearchField = fieldType;
    
    // التحقق من وجود Bootstrap Modal
    if (typeof $ === 'undefined' || typeof $.fn.modal === 'undefined') {
        console.error('❌ Bootstrap Modal غير متاح، فتح نافذة بسيطة');
        openSimpleSearchWindow(fieldType);
        return;
    }
    
    const modal = $('#advancedSupplierModal');
    if (modal.length === 0) {
        console.error('❌ لم يتم العثور على نافذة البحث المتقدم، فتح النافذة البسيطة');
        openSimpleSearchWindow(fieldType);
        return;
    }
    
    console.log('✅ فتح نافذة البحث المتقدم');
    modal.modal('show');
    loadAdvancedSearch();
}

// متغير لحفظ نوع الحقل الحالي
let currentSearchField = null;

function testSupplierSearch() {
    alert('اختبار البحث يعمل بشكل صحيح');
}

function selectSupplier(id, name, fieldType) {
    document.getElementById(fieldType + 'Id').value = id;
    document.getElementById(fieldType + 'Search').style.display = 'none';
    document.getElementById(fieldType + 'Selected').style.display = 'block';
    document.getElementById(fieldType + 'SelectedText').textContent = name;
    document.getElementById(fieldType + 'Results').style.display = 'none';
}

function clearSupplierSelection(fieldType) {
    document.getElementById(fieldType + 'Id').value = '';
    document.getElementById(fieldType + 'Search').style.display = 'block';
    document.getElementById(fieldType + 'Search').value = '';
    document.getElementById(fieldType + 'Selected').style.display = 'none';
}

// بحث بسيط يعمل
function setupSearch() {
    const shipperSearch = document.getElementById('shipperSearch');
    if (shipperSearch) {
        shipperSearch.addEventListener('input', function() {
            const query = this.value;
            if (query.length >= 2) {
                showSearchResults(query, 'shipper');
            } else {
                hideSearchResults('shipper');
            }
        });
    }
    
    const consigneeSearch = document.getElementById('consigneeSearch');
    if (consigneeSearch) {
        consigneeSearch.addEventListener('input', function() {
            const query = this.value;
            if (query.length >= 2) {
                showSearchResults(query, 'consignee');
            } else {
                hideSearchResults('consignee');
            }
        });
    }
}

function showSearchResults(query, fieldType) {
    const resultsDiv = document.getElementById(fieldType + 'Results');
    if (!resultsDiv) return;
    
    const mockResults = [
        {id: 1, name: 'شركة اكسو التركية', country: 'تركيا'},
        {id: 2, name: 'شركة يابايشينج-الصين', country: 'الصين'},
        {id: 3, name: 'شركة الخليج للتجارة', country: 'السعودية'}
    ];
    
    const filtered = mockResults.filter(s => s.name.includes(query) || s.country.includes(query));
    
    let html = '';
    filtered.forEach(supplier => {
        html += `<div class="dropdown-item" onclick="selectSupplier(${supplier.id}, '${supplier.name}', '${fieldType}')" style="cursor: pointer;">
            <strong>${supplier.name}</strong><br>
            <small>${supplier.country}</small>
        </div>`;
    });
    
    if (html) {
        resultsDiv.innerHTML = html;
        resultsDiv.style.display = 'block';
    } else {
        resultsDiv.innerHTML = '<div class="dropdown-item">لا توجد نتائج</div>';
        resultsDiv.style.display = 'block';
    }
}

function hideSearchResults(fieldType) {
    const resultsDiv = document.getElementById(fieldType + 'Results');
    if (resultsDiv) {
        resultsDiv.style.display = 'none';
    }
}

// تشغيل البحث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', setupSearch);

// دوال البحث المتقدم
function loadAdvancedSearch(page = 1) {
    console.log('🔍 تحميل البحث المتقدم...');
    
    const name = document.getElementById('advancedSearchName').value;
    const country = document.getElementById('advancedSearchCountry').value;
    const type = document.getElementById('advancedSearchType').value;
    const code = document.getElementById('advancedSearchCode').value;
    
    const resultsDiv = document.getElementById('advancedSearchResults');
    resultsDiv.innerHTML = '<div class="text-center p-4"><i class="fas fa-spinner fa-spin"></i> جاري البحث...</div>';
    
    // محاكاة البيانات للاختبار
    setTimeout(() => {
        const suppliers = [
            { id: 1, name: 'شركة اكسو التركية', country: 'تركيا', type: 'مصنع', code: 'AKS001' },
            { id: 2, name: 'شركة يابايشينج-الصين', country: 'الصين', type: 'مصنع', code: 'YAB001' },
            { id: 3, name: 'شركة الخليج للتجارة', country: 'السعودية', type: 'تاجر', code: 'GTC001' },
            { id: 4, name: 'مصنع النسيج المتطور', country: 'تركيا', type: 'مصنع', code: 'ATF001' },
            { id: 5, name: 'شركة التصدير الدولية', country: 'الإمارات', type: 'مصدر', code: 'IEC001' }
        ];
        
        // فلترة النتائج
        let filtered = suppliers;
        if (name) filtered = filtered.filter(s => s.name.includes(name));
        if (country) filtered = filtered.filter(s => s.country.includes(country));
        if (type) filtered = filtered.filter(s => s.type === type);
        if (code) filtered = filtered.filter(s => s.code.includes(code));
        
        displayAdvancedResults(filtered);
    }, 1000);
}

function displayAdvancedResults(suppliers) {
    const resultsDiv = document.getElementById('advancedSearchResults');
    
    if (suppliers.length === 0) {
        resultsDiv.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-search"></i><br>لا توجد نتائج</div>';
        return;
    }
    
    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>الاسم</th>
                        <th>الكود</th>
                        <th>البلد</th>
                        <th>النوع</th>
                        <th>اختيار</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    suppliers.forEach(supplier => {
        html += `
            <tr>
                <td><strong>${supplier.name}</strong></td>
                <td><span class="badge bg-secondary">${supplier.code}</span></td>
                <td>${supplier.country}</td>
                <td>${supplier.type}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-primary" 
                            onclick="selectSupplierFromAdvanced(${supplier.id}, '${supplier.name}')">
                        <i class="fas fa-check"></i> اختيار
                    </button>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    html += `<div class="text-center mt-3"><small class="text-muted">عرض ${suppliers.length} مورد</small></div>`;
    
    resultsDiv.innerHTML = html;
}

function selectSupplierFromAdvanced(id, name) {
    selectSupplier(id, name, currentSearchField);
    $('#advancedSupplierModal').modal('hide');
}

function clearAdvancedSearch() {
    document.getElementById('advancedSearchName').value = '';
    document.getElementById('advancedSearchCountry').value = '';
    document.getElementById('advancedSearchType').value = '';
    document.getElementById('advancedSearchCode').value = '';
    loadAdvancedSearch();
}

function openSimpleSearchWindow(fieldType) {
    alert(`البحث المتقدم للحقل: ${fieldType}\n\nالنافذة البسيطة قيد التطوير...`);
}

// حساب التكلفة الإجمالية
function calculateTotalCost() {
    const freightCost = parseFloat(document.querySelector('input[name="freight_cost"]').value) || 0;
    const otherCharges = parseFloat(document.querySelector('input[name="other_charges"]').value) || 0;
    const totalCost = freightCost + otherCharges;
    
    document.querySelector('input[name="total_cost"]').value = totalCost.toFixed(2);
}

// تهيئة حساب التكاليف
document.addEventListener('DOMContentLoaded', function() {
    // إضافة مستمعين لحقول التكلفة
    const freightCostInput = document.querySelector('input[name="freight_cost"]');
    const otherChargesInput = document.querySelector('input[name="other_charges"]');
    
    if (freightCostInput) {
        freightCostInput.addEventListener('input', calculateTotalCost);
    }
    
    if (otherChargesInput) {
        otherChargesInput.addEventListener('input', calculateTotalCost);
    }
    
    // حساب التكلفة الأولية
    calculateTotalCost();
    
    console.log('✅ تم تهيئة نظام حساب التكاليف');
});
</script>

<style>
.dropdown-menu {
    display: none;
    position: absolute;
    background: white;
    border: 1px solid #ccc;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
}

.dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}
</style>

<!-- محتوى النافذة الأصلية الكاملة -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title">
                        <i class="fas fa-ship me-2"></i>
                        إنشاء حجز شحنة حاويات جديدة
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-sm btn-warning" onclick="testSupplierSearch()">
                            <i class="fas fa-bug"></i> اختبار البحث
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    <form method="POST" action="{{ url_for('shipments.create_cargo_shipment') }}" id="cargoShipmentForm">

                        <!-- معلومات الحجز الأساسية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-clipboard-list me-2"></i>
                                    معلومات الحجز الأساسية
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-bold">رقم الحجز (Booking Number) *</label>
                                <input type="text" class="form-control" name="booking_number"
                                       placeholder="أدخل رقم الحجز..." required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-bold">رقم بوليصة الشحن (B/L Number) *</label>
                                <input type="text" class="form-control" name="bill_of_lading_number"
                                       placeholder="أدخل رقم البوليصة..." required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-bold">نوع الشحن *</label>
                                <select class="form-control" name="shipping_type" required>
                                    <option value="">اختر نوع الشحن</option>
                                    <option value="FCL">FCL - حاوية كاملة</option>
                                    <option value="LCL">LCL - حاوية مشتركة</option>
                                    <option value="Break Bulk">Break Bulk - بضائع عامة</option>
                                </select>
                            </div>
                        </div>

                        <!-- معلومات الموردين -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-users me-2"></i>
                                    معلومات الموردين
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <!-- الشاحن -->
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-bold">الشاحن (Shipper) *</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="shipperSearch"
                                           placeholder="ابحث عن الشاحن..." autocomplete="off">
                                    <input type="hidden" name="shipper_id" id="shipperId" required>
                                    <button type="button" class="btn btn-outline-secondary"
                                            onclick="openAdvancedSupplierSearch('shipper')" title="بحث متقدم">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                </div>
                                <div id="shipperResults" class="dropdown-menu w-100" style="max-height: 300px; overflow-y: auto;"></div>
                                <div id="shipperSelected" class="mt-2" style="display: none;">
                                    <div class="alert alert-success py-2">
                                        <i class="fas fa-check-circle me-2"></i>
                                        <span id="shipperSelectedText"></span>
                                        <button type="button" class="btn btn-sm btn-outline-danger float-end"
                                                onclick="clearSupplierSelection('shipper')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- المستلم -->
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-bold">المستلم (Consignee) *</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="consigneeSearch"
                                           placeholder="ابحث عن المستلم..." autocomplete="off">
                                    <input type="hidden" name="consignee_id" id="consigneeId" required>
                                    <button type="button" class="btn btn-outline-secondary"
                                            onclick="openAdvancedSupplierSearch('consignee')" title="بحث متقدم">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                </div>
                                <div id="consigneeResults" class="dropdown-menu w-100" style="max-height: 300px; overflow-y: auto;"></div>
                                <div id="consigneeSelected" class="mt-2" style="display: none;">
                                    <div class="alert alert-success py-2">
                                        <i class="fas fa-check-circle me-2"></i>
                                        <span id="consigneeSelectedText"></span>
                                        <button type="button" class="btn btn-sm btn-outline-danger float-end"
                                                onclick="clearSupplierSelection('consignee')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- جهة الإشعار -->
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-bold">جهة الإشعار (Notify Party)</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="notifyPartySearch"
                                           placeholder="ابحث عن جهة الإشعار..." autocomplete="off">
                                    <input type="hidden" name="notify_party_id" id="notifyPartyId">
                                    <button type="button" class="btn btn-outline-secondary"
                                            onclick="openAdvancedSupplierSearch('notifyParty')" title="بحث متقدم">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                </div>
                                <div id="notifyPartyResults" class="dropdown-menu w-100" style="max-height: 300px; overflow-y: auto;"></div>
                                <div id="notifyPartySelected" class="mt-2" style="display: none;">
                                    <div class="alert alert-success py-2">
                                        <i class="fas fa-check-circle me-2"></i>
                                        <span id="notifyPartySelectedText"></span>
                                        <button type="button" class="btn btn-sm btn-outline-danger float-end"
                                                onclick="clearSupplierSelection('notifyParty')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

{% endblock %}
