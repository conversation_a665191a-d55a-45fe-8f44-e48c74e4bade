-- إصلاح دالة CAN_CANCEL_TRANSFER (نسخة مبسطة)
-- Fix CAN_CANCEL_TRANSFER function (simplified version)

-- إنشاء أو تحديث دالة التحقق من إمكانية إلغاء الحوالة
CREATE OR REPLACE FUNCTION CAN_CANCEL_TRANSFER(
    p_transfer_id IN NUMBER
) RETURN VARCHAR2 AS
    v_status VARCHAR2(50);
    v_execution_date TIMESTAMP;
    v_days_since_execution NUMBER;
    v_result VARCHAR2(4000);
    v_transfer_number VARCHAR2(50);
    v_amount NUMBER(15,2);
BEGIN
    -- الحصول على معلومات الحوالة
    BEGIN
        SELECT status, execution_date, transfer_number, amount
        INTO v_status, v_execution_date, v_transfer_number, v_amount
        FROM transfers
        WHERE id = p_transfer_id;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RETURN 'ERROR: الحوالة غير موجودة';
    END;
    
    -- التحقق من الحالة (completed بدلاً من executed)
    IF v_status != 'completed' THEN
        RETURN 'ERROR: يمكن إلغاء الحوالات المكتملة فقط. الحالة الحالية: ' || v_status;
    END IF;
    
    -- التحقق من تاريخ التنفيذ
    IF v_execution_date IS NULL THEN
        RETURN 'ERROR: لا يوجد تاريخ تنفيذ للحوالة';
    END IF;
    
    -- حساب الأيام منذ التنفيذ
    v_days_since_execution := SYSDATE - v_execution_date;
    
    -- تحديد إمكانية الإلغاء
    IF v_days_since_execution > 30 THEN
        v_result := 'WARNING: مرت أكثر من 30 يوماً على التنفيذ (' || ROUND(v_days_since_execution, 1) || ' يوم)';
    ELSE
        v_result := 'OK: يمكن إلغاء الحوالة رقم ' || v_transfer_number || ' بمبلغ ' || v_amount;
    END IF;
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'ERROR: خطأ في النظام - ' || SQLERRM;
END;
/

-- عرض رسالة نجاح
SELECT 'تم تحديث دالة CAN_CANCEL_TRANSFER بنجاح' as result FROM DUAL;
