{% extends "base.html" %}

{% block title %}متغيرات نظام الموردين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        متغيرات نظام الموردين
                        <span class="badge bg-warning text-dark ms-2">القلب النابض للمشتريات</span>
                    </h4>
                    <p class="mb-0 mt-2">
                        <small>تعتبر هذه النافذة القلب الذي يعمل عليه نظام المشتريات - يجب توخي الدقة والحرص عند التعامل مع هذه المتغيرات</small>
                    </p>
                </div>
                <div class="card-body">
                    <form id="supplierVariablesForm">
                        <!-- أولاً: نظام الموردين -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-users me-2"></i>
                                    أولاً: نظام الموردين
                                </h5>
                            </div>
                        </div>

                        <div class="row">
                            <!-- طول رقم المورد -->
                            <div class="col-md-4 mb-3">
                                <label for="supp_id_len" class="form-label">طول رقم المورد</label>
                                <input type="number" class="form-control" id="supp_id_len" name="supp_id_len" 
                                       value="{{ variables.get('supp_id_len', 9) }}" min="1" max="15">
                                <div class="form-text">تحديد مدى رقم المورد (مثال: 9 يعني 9 أرقام كحد أقصى)</div>
                            </div>

                            <!-- الأرقام العشرية -->
                            <div class="col-md-4 mb-3">
                                <label for="decimal_places" class="form-label">الأرقام العشرية</label>
                                <input type="number" class="form-control" id="decimal_places" name="decimal_places" 
                                       value="{{ variables.get('decimal_places', 2) }}" min="0" max="6">
                                <div class="form-text">عدد الخانات بعد الرقم الصحيح</div>
                            </div>

                            <!-- نوع رقم المورد -->
                            <div class="col-md-4 mb-3">
                                <label for="supp_id_type" class="form-label">نوع رقم المورد</label>
                                <select class="form-select" id="supp_id_type" name="supp_id_type">
                                    <option value="numeric" {% if variables.get('supp_id_type') == 'numeric' %}selected{% endif %}>رقمي (تسلسل آلي)</option>
                                    <option value="alphanumeric" {% if variables.get('supp_id_type') == 'alphanumeric' %}selected{% endif %}>رقمي حرفي (تسلسل يدوي)</option>
                                </select>
                                <div class="form-text">اختيار نوع رقم المورد</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- ربط الموردين بالأستاذ العام -->
                            <div class="col-md-6 mb-3">
                                <label for="supp_gl_link" class="form-label">ربط الموردين بالأستاذ العام</label>
                                <select class="form-select" id="supp_gl_link" name="supp_gl_link">
                                    <option value="multiple" {% if variables.get('supp_gl_link') == 'multiple' %}selected{% endif %}>متعدد الحسابات</option>
                                    <option value="group" {% if variables.get('supp_gl_link') == 'group' %}selected{% endif %}>مجموعة الحسابات</option>
                                </select>
                                <div class="form-text">طريقة ربط حسابات الموردين بنظام الأستاذ العام</div>
                            </div>

                            <!-- تسلسل الوثيقة -->
                            <div class="col-md-6 mb-3">
                                <label for="doc_sequence" class="form-label">تسلسل الوثيقة</label>
                                <select class="form-select" id="doc_sequence" name="doc_sequence">
                                    <option value="auto_editable" {% if variables.get('doc_sequence') == 'auto_editable' %}selected{% endif %}>آلي يمكن تعديله</option>
                                    <option value="manual" {% if variables.get('doc_sequence') == 'manual' %}selected{% endif %}>يدوي</option>
                                    <option value="auto_readonly" {% if variables.get('doc_sequence') == 'auto_readonly' %}selected{% endif %}>آلي لا يمكن تعديله</option>
                                </select>
                                <div class="form-text">طريقة تسلسل المستندات</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- نوع تسلسل أرقام الموردين -->
                            <div class="col-md-6 mb-3">
                                <label for="supp_seq_type" class="form-label">نوع تسلسل أرقام الموردين</label>
                                <select class="form-select" id="supp_seq_type" name="supp_seq_type">
                                    <option value="general" {% if variables.get('supp_seq_type') == 'general' %}selected{% endif %}>عام</option>
                                    <option value="by_group" {% if variables.get('supp_seq_type') == 'by_group' %}selected{% endif %}>حسب المجموعة</option>
                                </select>
                                <div class="form-text">طريقة تسلسل أرقام الموردين</div>
                            </div>

                            <!-- إظهار التاريخ -->
                            <div class="col-md-6 mb-3">
                                <label for="date_display" class="form-label">إظهار التاريخ</label>
                                <select class="form-select" id="date_display" name="date_display">
                                    <option value="auto_editable" {% if variables.get('date_display') == 'auto_editable' %}selected{% endif %}>آلي يمكن تعديله</option>
                                    <option value="manual" {% if variables.get('date_display') == 'manual' %}selected{% endif %}>يدوي</option>
                                    <option value="auto_readonly" {% if variables.get('date_display') == 'auto_readonly' %}selected{% endif %}>آلي لا يمكن تعديله</option>
                                </select>
                                <div class="form-text">طريقة إظهار التاريخ في المستندات</div>
                            </div>
                        </div>

                        <!-- تسلسل المستندات -->
                        <div class="row">
                            <div class="col-12">
                                <h6 class="text-secondary mt-3 mb-3">تسلسل المستندات</h6>
                            </div>
                        </div>

                        <div class="row">
                            <!-- تسلسل طلبات الشراء -->
                            <div class="col-md-4 mb-3">
                                <label for="pr_sequence" class="form-label">تسلسل طلبات الشراء</label>
                                <select class="form-select" id="pr_sequence" name="pr_sequence">
                                    <option value="cumulative" {% if variables.get('pr_sequence') == 'cumulative' %}selected{% endif %}>تراكمي</option>
                                    <option value="by_type" {% if variables.get('pr_sequence') == 'by_type' %}selected{% endif %}>حسب النوع</option>
                                </select>
                            </div>

                            <!-- تسلسل أوامر الشراء -->
                            <div class="col-md-4 mb-3">
                                <label for="po_sequence" class="form-label">تسلسل أوامر الشراء</label>
                                <select class="form-select" id="po_sequence" name="po_sequence">
                                    <option value="cumulative" {% if variables.get('po_sequence') == 'cumulative' %}selected{% endif %}>تراكمي</option>
                                    <option value="by_type" {% if variables.get('po_sequence') == 'by_type' %}selected{% endif %}>حسب النوع</option>
                                </select>
                            </div>

                            <!-- تسلسل فواتير المشتريات -->
                            <div class="col-md-4 mb-3">
                                <label for="pi_sequence" class="form-label">تسلسل فواتير المشتريات</label>
                                <select class="form-select" id="pi_sequence" name="pi_sequence">
                                    <option value="cumulative" {% if variables.get('pi_sequence') == 'cumulative' %}selected{% endif %}>تراكمي</option>
                                    <option value="by_type" {% if variables.get('pi_sequence') == 'by_type' %}selected{% endif %}>النوع (محلي، خارجي)</option>
                                    <option value="by_payment" {% if variables.get('pi_sequence') == 'by_payment' %}selected{% endif %}>طريقة الدفع</option>
                                    <option value="by_cost_center" {% if variables.get('pi_sequence') == 'by_cost_center' %}selected{% endif %}>مراكز التكلفة</option>
                                    <option value="by_warehouse" {% if variables.get('pi_sequence') == 'by_warehouse' %}selected{% endif %}>المخزن</option>
                                    <option value="by_wh_cc" {% if variables.get('pi_sequence') == 'by_wh_cc' %}selected{% endif %}>المخزن ومركز التكلفة</option>
                                    <option value="by_invoice_type" {% if variables.get('pi_sequence') == 'by_invoice_type' %}selected{% endif %}>نوع الفاتورة</option>
                                    <option value="by_wh_inv_type" {% if variables.get('pi_sequence') == 'by_wh_inv_type' %}selected{% endif %}>المخزن ونوع الفاتورة</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <!-- تسلسل مردودات المشتريات -->
                            <div class="col-md-6 mb-3">
                                <label for="return_sequence" class="form-label">تسلسل مردودات المشتريات</label>
                                <select class="form-select" id="return_sequence" name="return_sequence">
                                    <option value="cumulative" {% if variables.get('return_sequence') == 'cumulative' %}selected{% endif %}>تراكمي</option>
                                    <option value="by_payment" {% if variables.get('return_sequence') == 'by_payment' %}selected{% endif %}>طريقة الدفع</option>
                                    <option value="by_cost_center" {% if variables.get('return_sequence') == 'by_cost_center' %}selected{% endif %}>مراكز التكلفة</option>
                                    <option value="by_warehouse" {% if variables.get('return_sequence') == 'by_warehouse' %}selected{% endif %}>المخزن</option>
                                    <option value="by_type" {% if variables.get('return_sequence') == 'by_type' %}selected{% endif %}>النوع</option>
                                    <option value="by_wh_type" {% if variables.get('return_sequence') == 'by_wh_type' %}selected{% endif %}>المخزن والنوع</option>
                                </select>
                            </div>

                            <!-- تسلسل إذن توريد مشتريات محلية -->
                            <div class="col-md-6 mb-3">
                                <label for="delivery_seq" class="form-label">تسلسل إذن توريد مشتريات محلية</label>
                                <select class="form-select" id="delivery_seq" name="delivery_seq">
                                    <option value="cumulative" {% if variables.get('delivery_seq') == 'cumulative' %}selected{% endif %}>تراكمي</option>
                                    <option value="by_type" {% if variables.get('delivery_seq') == 'by_type' %}selected{% endif %}>حسب النوع</option>
                                </select>
                            </div>
                        </div>

                        <!-- الكميات المجانية والخصومات -->
                        <div class="row">
                            <div class="col-12">
                                <h6 class="text-secondary mt-3 mb-3">الكميات المجانية والخصومات</h6>
                            </div>
                        </div>

                        <div class="row">
                            <!-- استخدام الكميات المجانية -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="use_free_qty" name="use_free_qty"
                                           {% if variables.get('use_free_qty') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="use_free_qty">
                                        استخدام الكميات المجانية
                                    </label>
                                </div>
                            </div>

                            <!-- إظهار نسبة الكمية المجانية -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="show_free_pct" name="show_free_pct"
                                           {% if variables.get('show_free_pct') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="show_free_pct">
                                        إظهار نسبة الكمية المجانية
                                    </label>
                                </div>
                            </div>

                            <!-- إرجاع نسبة الكمية المجانية في مردود المشتريات -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="return_free_pct" name="return_free_pct"
                                           {% if variables.get('return_free_pct') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="return_free_pct">
                                        إرجاع نسبة الكمية المجانية في المردود
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- تأثير الكميات المجانية -->
                            <div class="col-md-6 mb-3">
                                <label for="free_qty_effect" class="form-label">تأثير الكميات المجانية</label>
                                <select class="form-select" id="free_qty_effect" name="free_qty_effect">
                                    <option value="not_used" {% if variables.get('free_qty_effect') == 'not_used' %}selected{% endif %}>لا تستخدم</option>
                                    <option value="reduce_cost" {% if variables.get('free_qty_effect') == 'reduce_cost' %}selected{% endif %}>يؤثر على التكلفة بتخفيضها</option>
                                    <option value="transfer_to_account" {% if variables.get('free_qty_effect') == 'transfer_to_account' %}selected{% endif %}>يرحل إلى حساب الكميات المجانية</option>
                                </select>
                            </div>

                            <!-- استخدام الخصم على مستوى الأصناف -->
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="use_item_disc" name="use_item_disc"
                                           {% if variables.get('use_item_disc') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="use_item_disc">
                                        استخدام الخصم على مستوى الأصناف
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- نوع خصم الصنف -->
                            <div class="col-md-4 mb-3">
                                <label for="disc_type" class="form-label">نوع خصم الصنف</label>
                                <select class="form-select" id="disc_type" name="disc_type">
                                    <option value="percentage" {% if variables.get('disc_type') == 'percentage' %}selected{% endif %}>نسبة</option>
                                    <option value="amount" {% if variables.get('disc_type') == 'amount' %}selected{% endif %}>مبلغ</option>
                                    <option value="both" {% if variables.get('disc_type') == 'both' %}selected{% endif %}>نسبة/ مبلغ</option>
                                </select>
                            </div>

                            <!-- عدد الخصومات -->
                            <div class="col-md-4 mb-3">
                                <label for="disc_count" class="form-label">عدد الخصومات</label>
                                <select class="form-select" id="disc_count" name="disc_count">
                                    <option value="0" {% if variables.get('disc_count') == '0' %}selected{% endif %}>لا يوجد</option>
                                    <option value="1" {% if variables.get('disc_count') == '1' %}selected{% endif %}>خصم واحد</option>
                                    <option value="2" {% if variables.get('disc_count') == '2' %}selected{% endif %}>خصمين</option>
                                    <option value="3" {% if variables.get('disc_count') == '3' %}selected{% endif %}>ثلاثة</option>
                                </select>
                            </div>

                            <!-- نوع ترميز الخصم -->
                            <div class="col-md-4 mb-3">
                                <label for="disc_code_type" class="form-label">نوع ترميز الخصم</label>
                                <select class="form-select" id="disc_code_type" name="disc_code_type">
                                    <option value="not_used" {% if variables.get('disc_code_type') == 'not_used' %}selected{% endif %}>غير مستخدم</option>
                                    <option value="by_item" {% if variables.get('disc_code_type') == 'by_item' %}selected{% endif %}>حسب الصنف</option>
                                    <option value="by_supplier" {% if variables.get('disc_code_type') == 'by_supplier' %}selected{% endif %}>حسب المورد</option>
                                    <option value="by_item_supplier" {% if variables.get('disc_code_type') == 'by_item_supplier' %}selected{% endif %}>حسب الصنف والمورد</option>
                                    <option value="by_item_supplier_pricelist" {% if variables.get('disc_code_type') == 'by_item_supplier_pricelist' %}selected{% endif %}>حسب الصنف والمورد وقوائم الأسعار</option>
                                </select>
                            </div>
                        </div>

                        <!-- ثانياً: خيارات متقدمة -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mt-4">
                                    <i class="fas fa-cog me-2"></i>
                                    ثانياً: خيارات متقدمة
                                </h5>
                            </div>
                        </div>

                        <div class="row">
                            <!-- ربط الصنف بالمورد إجباري -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="mand_item_supp" name="mand_item_supp"
                                           {% if variables.get('mand_item_supp') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="mand_item_supp">
                                        ربط الصنف بالمورد إجباري
                                    </label>
                                </div>
                            </div>

                            <!-- ربط الصنف بأكثر من مورد -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="link_multi_supp" name="link_multi_supp"
                                           {% if variables.get('link_multi_supp') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="link_multi_supp">
                                        ربط الصنف بأكثر من مورد
                                    </label>
                                </div>
                            </div>

                            <!-- فحص ربط الصنف بالمورد -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check_item_supp" name="check_item_supp"
                                           {% if variables.get('check_item_supp') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="check_item_supp">
                                        فحص ربط الصنف بالمورد
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- المزيد من الخيارات المتقدمة -->
                        <div class="row">
                            <!-- إدخال سعر البيع من فاتورة المشتريات -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="sale_price_pur" name="sale_price_pur"
                                           {% if variables.get('sale_price_pur') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="sale_price_pur">
                                        إدخال سعر البيع من فاتورة المشتريات
                                    </label>
                                </div>
                            </div>

                            <!-- إدخال سعر البيع من أمر الشراء -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="sale_price_po" name="sale_price_po"
                                           {% if variables.get('sale_price_po') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="sale_price_po">
                                        إدخال سعر البيع من أمر الشراء
                                    </label>
                                </div>
                            </div>

                            <!-- السماح بتعديل المورد فى الفواتير -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="allow_supp_edit" name="allow_supp_edit"
                                           {% if variables.get('allow_supp_edit') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="allow_supp_edit">
                                        السماح بتعديل المورد فى الفواتير
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- ربط أمر الشراء بطلب الشراء -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="link_po_pr" name="link_po_pr"
                                           {% if variables.get('link_po_pr') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="link_po_pr">
                                        ربط أمر الشراء بطلب الشراء
                                    </label>
                                </div>
                            </div>

                            <!-- ربط الفاتورة بأمر الشراء -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="link_inv_po" name="link_inv_po"
                                           {% if variables.get('link_inv_po') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="link_inv_po">
                                        ربط الفاتورة بأمر الشراء
                                    </label>
                                </div>
                            </div>

                            <!-- استخدام فحص المشتريات -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="use_inspection" name="use_inspection"
                                           {% if variables.get('use_inspection') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="use_inspection">
                                        استخدام فحص المشتريات
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- تحديث أسعار الموردين آليا -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="auto_upd_prices" name="auto_upd_prices"
                                           {% if variables.get('auto_upd_prices') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="auto_upd_prices">
                                        تحديث أسعار الموردين آليا
                                    </label>
                                </div>
                            </div>

                            <!-- إظهار رقم صنف المورد -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="show_supp_item" name="show_supp_item"
                                           {% if variables.get('show_supp_item') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="show_supp_item">
                                        إظهار رقم صنف المورد
                                    </label>
                                </div>
                            </div>

                            <!-- استخدام قوائم أسعار الموردين -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="use_supp_lists" name="use_supp_lists"
                                           {% if variables.get('use_supp_lists') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="use_supp_lists">
                                        استخدام قوائم أسعار الموردين
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- المزيد من الخيارات المتقدمة -->
                        <div class="row">
                            <!-- التنبيه عند وجود طلب شراء سابق للصنف لم ينفذ -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="alert_exist_pr" name="alert_exist_pr"
                                           {% if variables.get('alert_exist_pr') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="alert_exist_pr">
                                        التنبيه عند وجود طلب شراء سابق لم ينفذ
                                    </label>
                                </div>
                            </div>

                            <!-- التنبيه عند وجود أمر شراء سابق للصنف لم ينفذ -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="alert_exist_po" name="alert_exist_po"
                                           {% if variables.get('alert_exist_po') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="alert_exist_po">
                                        التنبيه عند وجود أمر شراء سابق لم ينفذ
                                    </label>
                                </div>
                            </div>

                            <!-- السماح بإعادة شراء الرقم التسلسلي -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="allow_serial_rep" name="allow_serial_rep"
                                           {% if variables.get('allow_serial_rep') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="allow_serial_rep">
                                        السماح بإعادة شراء الرقم التسلسلي
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- استخدام بيانات التعبئة -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="use_pack_data" name="use_pack_data"
                                           {% if variables.get('use_pack_data') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="use_pack_data">
                                        استخدام بيانات التعبئة
                                    </label>
                                </div>
                            </div>

                            <!-- فحص المخازن المرتبطة بالفرع للطلبات والأوامر -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check_branch_wh" name="check_branch_wh"
                                           {% if variables.get('check_branch_wh') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="check_branch_wh">
                                        فحص المخازن المرتبطة بالفرع
                                    </label>
                                </div>
                            </div>

                            <!-- السماح باستخدام المستندات المحذوفة -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="allow_del_docs" name="allow_del_docs"
                                           {% if variables.get('allow_del_docs') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="allow_del_docs">
                                        السماح باستخدام المستندات المحذوفة
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- شهادة دخول -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="entry_cert" name="entry_cert"
                                           {% if variables.get('entry_cert') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="entry_cert">
                                        شهادة دخول
                                    </label>
                                </div>
                            </div>

                            <!-- السماح بالإنزال الجزئي لأوامر التوريد -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="allow_part_del" name="allow_part_del"
                                           {% if variables.get('allow_part_del') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="allow_part_del">
                                        السماح بالإنزال الجزئي لأوامر التوريد
                                    </label>
                                </div>
                            </div>

                            <!-- استخدام سعر البيع كسعر للشراء -->
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="use_sale_as_pur" name="use_sale_as_pur"
                                           {% if variables.get('use_sale_as_pur') == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="use_sale_as_pur">
                                        استخدام سعر البيع كسعر للشراء
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- آلية توزيع المصاريف -->
                            <div class="col-md-6 mb-3">
                                <label for="exp_dist_method" class="form-label">آلية توزيع المصاريف</label>
                                <select class="form-select" id="exp_dist_method" name="exp_dist_method">
                                    <option value="qty_only" {% if variables.get('exp_dist_method') == 'qty_only' %}selected{% endif %}>كميات فقط</option>
                                    <option value="qty_plus_free" {% if variables.get('exp_dist_method') == 'qty_plus_free' %}selected{% endif %}>كميات + كميات مجانية</option>
                                </select>
                            </div>

                            <!-- مستوى التسعيرة -->
                            <div class="col-md-6 mb-3">
                                <label for="sale_pr_level" class="form-label">مستوى التسعيرة</label>
                                <input type="number" class="form-control" id="sale_pr_level" name="sale_pr_level"
                                       value="{{ variables.get('sale_pr_level', 1) }}" min="1" max="10">
                                <div class="form-text">مستوى التسعيرة المناسبة (1-10)</div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ والإلغاء -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                        <i class="fas fa-undo me-2"></i>
                                        إعادة تعيين
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ المتغيرات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2 mb-0">جاري حفظ المتغيرات...</p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('supplierVariablesForm');
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        saveSupplierVariables();
    });

    function saveSupplierVariables() {
        loadingModal.show();

        const formData = new FormData(form);
        const data = {};

        for (let [key, value] of formData.entries()) {
            if (form.querySelector(`[name="${key}"]`).type === 'checkbox') {
                data[key] = form.querySelector(`[name="${key}"]`).checked ? 1 : 0;
            } else {
                data[key] = value;
            }
        }

        fetch('/purchase_settings/api/supplier_variables', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            loadingModal.hide();
            if (data.success) {
                showAlert('تم حفظ متغيرات الموردين بنجاح!', 'success');
            } else {
                showAlert('خطأ: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            loadingModal.hide();
            console.error('Error:', error);
            showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
        });
    }

    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('.container-fluid');
        container.insertBefore(alertDiv, container.firstChild);

        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }

    window.resetForm = function() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع القيم؟')) {
            form.reset();
        }
    };
});
</script>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2 mb-0">جاري حفظ المتغيرات...</p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('supplierVariablesForm');
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        saveSupplierVariables();
    });

    function saveSupplierVariables() {
        loadingModal.show();

        const formData = new FormData(form);
        const data = {};

        for (let [key, value] of formData.entries()) {
            if (form.querySelector(`[name="${key}"]`).type === 'checkbox') {
                data[key] = form.querySelector(`[name="${key}"]`).checked ? 1 : 0;
            } else {
                data[key] = value;
            }
        }

        fetch('/purchase_settings/api/supplier_variables', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            loadingModal.hide();
            if (data.success) {
                showAlert('تم حفظ متغيرات الموردين بنجاح!', 'success');
            } else {
                showAlert('خطأ: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            loadingModal.hide();
            console.error('Error:', error);
            showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
        });
    }

    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('.container-fluid');
        container.insertBefore(alertDiv, container.firstChild);

        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }

    window.resetForm = function() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع القيم؟')) {
            form.reset();
        }
    };
});
</script>
{% endblock %}
