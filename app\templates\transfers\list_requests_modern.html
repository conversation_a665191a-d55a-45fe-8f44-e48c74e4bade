<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة طلبات الحوالات - نظام الفوجي</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    
    <style>
        :root {
            --primary: #2c3e50;
            --secondary: #3498db;
            --success: #27ae60;
            --warning: #f39c12;
            --danger: #e74c3c;
            --info: #17a2b8;
            --light: #f8f9fa;
            --dark: #343a40;
            --surface: #ffffff;
            --border: #dee2e6;
            --text-primary: #2c3e50;
            --text-secondary: #6c757d;
            --text-muted: #adb5bd;
            --radius: 12px;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: var(--text-primary);
        }

        /* Header */
        .page-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-top: 0.5rem;
        }

        /* Breadcrumb */
        .breadcrumb-container {
            background: var(--surface);
            padding: 1rem 0;
            margin-bottom: 2rem;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
        }

        .breadcrumb {
            background: none;
            margin: 0;
            padding: 0;
        }

        .breadcrumb-item a {
            color: var(--secondary);
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb-item a:hover {
            color: var(--primary);
        }

        /* Cards */
        .card-modern {
            background: var(--surface);
            border: none;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .card-header-modern {
            background: linear-gradient(135deg, var(--light) 0%, #e9ecef 100%);
            border-bottom: 1px solid var(--border);
            padding: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Stats Cards */
        .stats-card {
            background: var(--surface);
            border-radius: var(--radius);
            padding: 2rem;
            text-align: center;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            border-left: 4px solid var(--secondary);
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stats-card.success {
            border-left-color: var(--success);
        }

        .stats-card.warning {
            border-left-color: var(--warning);
        }

        .stats-card.danger {
            border-left-color: var(--danger);
        }

        .stats-card.info {
            border-left-color: var(--info);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
            display: block;
        }

        .stats-label {
            font-size: 1rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .stats-icon {
            font-size: 3rem;
            opacity: 0.1;
            position: absolute;
            top: 1rem;
            left: 1rem;
        }

        /* Control Panel */
        .control-panel {
            background: var(--surface);
            border-radius: var(--radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .form-control-modern {
            border: 2px solid var(--border);
            border-radius: var(--radius);
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control-modern:focus {
            border-color: var(--secondary);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .btn-modern {
            border-radius: var(--radius);
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        /* Table */
        .table-container {
            background: var(--surface);
            border-radius: var(--radius);
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .table-modern {
            margin: 0;
            font-size: 0.9rem;
        }

        .table-modern thead th {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            border: none;
            padding: 1rem;
            font-weight: 600;
            text-align: center;
        }

        .table-modern tbody td {
            padding: 1rem;
            border-bottom: 1px solid var(--border);
            vertical-align: middle;
            text-align: center;
        }

        .table-modern tbody tr:hover {
            background: var(--light);
        }

        /* Status Badges */
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending {
            background: #ffeaa7;
            color: #d63031;
        }

        .status-approved {
            background: #55efc4;
            color: #00b894;
        }

        .status-rejected {
            background: #ff7675;
            color: #d63031;
        }

        .status-executed {
            background: #74b9ff;
            color: #0984e3;
        }

        /* Loading */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            background: white;
            padding: 2rem;
            border-radius: var(--radius);
            text-align: center;
        }

        /* Action Buttons */
        .action-btn {
            padding: 0.25rem 0.5rem;
            margin: 0 0.125rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            transform: scale(1.05);
        }

        .btn-view {
            background: var(--info);
            color: white;
        }

        .btn-edit {
            background: var(--warning);
            color: white;
        }

        .btn-delete {
            background: var(--danger);
            color: white;
        }

        .btn-approve {
            background: var(--success);
            color: white;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .page-title {
                font-size: 1.8rem;
            }
            
            .stats-card {
                margin-bottom: 1rem;
            }
            
            .control-panel {
                padding: 1rem;
            }
            
            .table-responsive {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="fas fa-exchange-alt me-3"></i>
                        إدارة طلبات الحوالات
                    </h1>
                    <p class="page-subtitle">
                        إدارة شاملة لجميع طلبات الحوالات المالية مع إمكانيات البحث والفلترة المتقدمة
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex justify-content-end gap-2">
                        <button class="btn btn-light btn-modern" onclick="refreshData()">
                            <i class="fas fa-sync-alt me-2"></i>تحديث
                        </button>
                        <a href="/transfers/new-request" class="btn btn-success btn-modern">
                            <i class="fas fa-plus me-2"></i>طلب جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="breadcrumb-container">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="/dashboard">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="/transfers/dashboard">
                            <i class="fas fa-exchange-alt me-1"></i>
                            نظام الحوالات
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <i class="fas fa-list me-1"></i>
                        قائمة طلبات الحوالات
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Statistics Cards -->
        <div class="row mb-4" id="statsContainer">
            <div class="col-md-3">
                <div class="stats-card success position-relative">
                    <i class="fas fa-file-alt stats-icon"></i>
                    <span class="stats-number" id="totalRequests">0</span>
                    <div class="stats-label">إجمالي الطلبات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card warning position-relative">
                    <i class="fas fa-clock stats-icon"></i>
                    <span class="stats-number" id="pendingRequests">0</span>
                    <div class="stats-label">طلبات معلقة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card info position-relative">
                    <i class="fas fa-check-circle stats-icon"></i>
                    <span class="stats-number" id="approvedRequests">0</span>
                    <div class="stats-label">طلبات معتمدة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card danger position-relative">
                    <i class="fas fa-dollar-sign stats-icon"></i>
                    <span class="stats-number" id="totalAmount">0</span>
                    <div class="stats-label">إجمالي المبالغ</div>
                </div>
            </div>
        </div>
