-- =====================================================
-- نظام عمولات مندوبي المشتريات - قاعدة البيانات
-- Purchase Representatives Commission System - Database Schema
-- =====================================================

-- 1. جدول المندوبين (purchase_representatives)
CREATE TABLE purchase_representatives (
    id NUMBER PRIMARY KEY,
    rep_code VARCHAR2(20) UNIQUE NOT NULL,
    rep_name VARCHAR2(200) NOT NULL,
    rep_name_en VARCHAR2(200),
    employee_id NUMBER, -- ربط مع جدول الموظفين
    department VARCHAR2(100),
    branch_id NUMBER,
    phone VARCHAR2(20),
    mobile VARCHAR2(20),
    email VARCHAR2(100),
    hire_date DATE,
    
    -- التخصص والأهداف
    specialization VARCHAR2(200), -- تخصص المندوب
    target_monthly_orders NUMBER DEFAULT 0, -- هدف شهري للطلبات
    target_monthly_quantity NUMBER DEFAULT 0, -- هدف شهري للكميات
    target_monthly_value NUMBER(15,2) DEFAULT 0, -- هدف شهري للقيمة
    
    -- إعدادات العمولة
    is_active NUMBER(1) DEFAULT 1,
    commission_eligible NUMBER(1) DEFAULT 1, -- مؤهل للعمولة
    commission_start_date DATE, -- تاريخ بداية استحقاق العمولة
    commission_end_date DATE, -- تاريخ انتهاء استحقاق العمولة
    
    -- معلومات إضافية
    notes CLOB,
    created_at DATE DEFAULT SYSDATE,
    created_by NUMBER,
    updated_at DATE,
    updated_by NUMBER,
    
    -- قيود
    CONSTRAINT chk_rep_active CHECK (is_active IN (0, 1)),
    CONSTRAINT chk_rep_eligible CHECK (commission_eligible IN (0, 1))
);

-- إنشاء sequence للمندوبين
CREATE SEQUENCE purchase_representatives_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- إنشاء trigger للـ ID التلقائي
CREATE OR REPLACE TRIGGER purchase_representatives_bir
    BEFORE INSERT ON purchase_representatives
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := purchase_representatives_seq.NEXTVAL;
    END IF;
    :NEW.created_at := SYSDATE;
END;
/

-- 2. جدول أنواع العمولات (commission_types)
CREATE TABLE commission_types (
    id NUMBER PRIMARY KEY,
    type_code VARCHAR2(20) UNIQUE NOT NULL,
    type_name VARCHAR2(200) NOT NULL,
    type_name_en VARCHAR2(200),
    calculation_method VARCHAR2(50) NOT NULL, -- FIXED, PERCENTAGE, TIERED, QUANTITY_FIXED, QUANTITY_TIERED, ITEM_BASED, SUPPLIER_BASED, SEASONAL
    description CLOB,
    supports_combination NUMBER(1) DEFAULT 1, -- يمكن دمجه مع أنواع أخرى
    is_active NUMBER(1) DEFAULT 1,
    display_order NUMBER DEFAULT 1,
    created_at DATE DEFAULT SYSDATE,
    created_by NUMBER,
    updated_at DATE,
    updated_by NUMBER,
    
    -- قيود
    CONSTRAINT chk_ct_method CHECK (calculation_method IN ('FIXED', 'PERCENTAGE', 'TIERED', 'QUANTITY_FIXED', 'QUANTITY_TIERED', 'ITEM_BASED', 'SUPPLIER_BASED', 'SEASONAL')),
    CONSTRAINT chk_ct_active CHECK (is_active IN (0, 1)),
    CONSTRAINT chk_ct_combination CHECK (supports_combination IN (0, 1))
);

-- إنشاء sequence لأنواع العمولات
CREATE SEQUENCE commission_types_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- إنشاء trigger للـ ID التلقائي
CREATE OR REPLACE TRIGGER commission_types_bir
    BEFORE INSERT ON commission_types
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := commission_types_seq.NEXTVAL;
    END IF;
    :NEW.created_at := SYSDATE;
END;
/

-- 3. جدول قواعد العمولات (commission_rules)
CREATE TABLE commission_rules (
    id NUMBER PRIMARY KEY,
    rule_name VARCHAR2(200) NOT NULL,
    rule_description CLOB,
    commission_type_id NUMBER NOT NULL,
    rep_id NUMBER, -- مندوب محدد (اختياري)
    supplier_id NUMBER, -- مورد محدد (اختياري)
    item_category VARCHAR2(100), -- فئة أصناف (اختياري)
    
    -- قواعد الحساب التقليدية
    fixed_amount NUMBER(15,2),
    percentage_rate NUMBER(5,2),
    min_order_value NUMBER(15,2),
    max_commission NUMBER(15,2),
    
    -- قواعد الحساب حسب الكمية (جديد)
    quantity_unit VARCHAR2(20), -- الوحدة: قطعة، كيلو، متر، لتر، صندوق، إلخ
    quantity_rate NUMBER(15,4), -- المعدل لكل وحدة
    min_quantity NUMBER(15,2), -- الحد الأدنى للكمية
    max_quantity NUMBER(15,2), -- الحد الأقصى للكمية
    quantity_tiers CLOB, -- JSON للشرائح المتدرجة حسب الكمية
    weight_based NUMBER(1) DEFAULT 0, -- حساب حسب الوزن
    volume_based NUMBER(1) DEFAULT 0, -- حساب حسب الحجم
    
    -- فترات الصلاحية
    effective_from DATE,
    effective_to DATE,
    
    -- شروط إضافية
    conditions CLOB, -- JSON للشروط المعقدة
    combination_rules CLOB, -- JSON لقواعد الدمج مع أنواع أخرى
    is_active NUMBER(1) DEFAULT 1,
    priority_order NUMBER DEFAULT 1,
    
    created_at DATE DEFAULT SYSDATE,
    created_by NUMBER,
    updated_at DATE,
    updated_by NUMBER,
    
    -- المفاتيح الأجنبية
    CONSTRAINT fk_cr_commission_type FOREIGN KEY (commission_type_id) REFERENCES commission_types(id),
    CONSTRAINT fk_cr_rep FOREIGN KEY (rep_id) REFERENCES purchase_representatives(id),
    
    -- قيود
    CONSTRAINT chk_cr_active CHECK (is_active IN (0, 1)),
    CONSTRAINT chk_cr_weight_based CHECK (weight_based IN (0, 1)),
    CONSTRAINT chk_cr_volume_based CHECK (volume_based IN (0, 1))
);

-- إنشاء sequence لقواعد العمولات
CREATE SEQUENCE commission_rules_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- إنشاء trigger للـ ID التلقائي
CREATE OR REPLACE TRIGGER commission_rules_bir
    BEFORE INSERT ON commission_rules
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := commission_rules_seq.NEXTVAL;
    END IF;
    :NEW.created_at := SYSDATE;
END;
/

-- 4. جدول حساب العمولات (commission_calculations)
CREATE TABLE commission_calculations (
    id NUMBER PRIMARY KEY,
    calculation_date DATE DEFAULT SYSDATE,
    rep_id NUMBER NOT NULL,
    purchase_order_id NUMBER NOT NULL,
    rule_id NUMBER NOT NULL,
    
    -- تفاصيل الحساب التقليدية
    order_value NUMBER(15,2),
    commission_rate NUMBER(5,2),
    commission_amount NUMBER(15,2),
    calculation_method VARCHAR2(50),
    
    -- تفاصيل الحساب حسب الكمية (جديد)
    total_quantity NUMBER(15,2),
    quantity_unit VARCHAR2(20),
    quantity_rate NUMBER(15,4),
    quantity_commission NUMBER(15,2),
    weight_total NUMBER(15,2),
    volume_total NUMBER(15,2),
    
    -- تفاصيل إضافية
    breakdown_details CLOB, -- JSON لتفاصيل الحساب
    combined_commission NUMBER(15,2), -- العمولة المجمعة من عدة قواعد
    
    -- حالة العمولة
    status VARCHAR2(20) DEFAULT 'CALCULATED', -- CALCULATED, APPROVED, PAID, CANCELLED
    approved_by NUMBER,
    approved_at DATE,
    paid_at DATE,
    payment_reference VARCHAR2(100),
    
    notes CLOB,
    created_at DATE DEFAULT SYSDATE,
    created_by NUMBER,
    updated_at DATE,
    updated_by NUMBER,
    
    -- المفاتيح الأجنبية
    CONSTRAINT fk_cc_rep FOREIGN KEY (rep_id) REFERENCES purchase_representatives(id),
    CONSTRAINT fk_cc_rule FOREIGN KEY (rule_id) REFERENCES commission_rules(id),
    
    -- قيود
    CONSTRAINT chk_cc_status CHECK (status IN ('CALCULATED', 'APPROVED', 'PAID', 'CANCELLED'))
);

-- إنشاء sequence لحساب العمولات
CREATE SEQUENCE commission_calculations_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- إنشاء trigger للـ ID التلقائي
CREATE OR REPLACE TRIGGER commission_calculations_bir
    BEFORE INSERT ON commission_calculations
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := commission_calculations_seq.NEXTVAL;
    END IF;
    :NEW.created_at := SYSDATE;
END;
/

-- 5. جدول مدفوعات العمولات (commission_payments)
CREATE TABLE commission_payments (
    id NUMBER PRIMARY KEY,
    payment_date DATE DEFAULT SYSDATE,
    rep_id NUMBER NOT NULL,
    calculation_ids CLOB, -- JSON array من IDs العمولات المدفوعة
    total_amount NUMBER(15,2) NOT NULL,
    currency VARCHAR2(10) DEFAULT 'SAR',
    
    -- ربط مع نظام الحوالات
    transfer_request_id NUMBER, -- ربط مع جدول طلبات الحوالات
    transfer_id NUMBER, -- ربط مع جدول الحوالات المنفذة
    
    -- تفاصيل الدفع
    payment_method VARCHAR2(50), -- TRANSFER, CASH, BANK, CHECK
    payment_reference VARCHAR2(100),
    bank_details CLOB, -- تفاصيل البنك إذا كان الدفع بنكي
    
    -- حالة الدفع
    status VARCHAR2(20) DEFAULT 'PENDING', -- PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED
    processed_by NUMBER,
    processed_at DATE,
    
    notes CLOB,
    created_at DATE DEFAULT SYSDATE,
    created_by NUMBER,
    updated_at DATE,
    updated_by NUMBER,
    
    -- المفاتيح الأجنبية
    CONSTRAINT fk_cp_rep FOREIGN KEY (rep_id) REFERENCES purchase_representatives(id),
    
    -- قيود
    CONSTRAINT chk_cp_status CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED')),
    CONSTRAINT chk_cp_method CHECK (payment_method IN ('TRANSFER', 'CASH', 'BANK', 'CHECK'))
);

-- إنشاء sequence لمدفوعات العمولات
CREATE SEQUENCE commission_payments_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- إنشاء trigger للـ ID التلقائي
CREATE OR REPLACE TRIGGER commission_payments_bir
    BEFORE INSERT ON commission_payments
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := commission_payments_seq.NEXTVAL;
    END IF;
    :NEW.created_at := SYSDATE;
END;
/

-- =====================================================
-- الفهارس لتحسين الأداء
-- Performance Indexes
-- =====================================================

-- فهارس جدول المندوبين
CREATE INDEX idx_rep_code ON purchase_representatives(rep_code);
CREATE INDEX idx_rep_active ON purchase_representatives(is_active);
CREATE INDEX idx_rep_eligible ON purchase_representatives(commission_eligible);
CREATE INDEX idx_rep_branch ON purchase_representatives(branch_id);
CREATE INDEX idx_rep_department ON purchase_representatives(department);

-- فهارس جدول أنواع العمولات
CREATE INDEX idx_ct_code ON commission_types(type_code);
CREATE INDEX idx_ct_method ON commission_types(calculation_method);
CREATE INDEX idx_ct_active ON commission_types(is_active);

-- فهارس جدول قواعد العمولات
CREATE INDEX idx_cr_type ON commission_rules(commission_type_id);
CREATE INDEX idx_cr_rep ON commission_rules(rep_id);
CREATE INDEX idx_cr_supplier ON commission_rules(supplier_id);
CREATE INDEX idx_cr_active ON commission_rules(is_active);
CREATE INDEX idx_cr_effective ON commission_rules(effective_from, effective_to);
CREATE INDEX idx_cr_priority ON commission_rules(priority_order);

-- فهارس جدول حساب العمولات
CREATE INDEX idx_cc_rep ON commission_calculations(rep_id);
CREATE INDEX idx_cc_po ON commission_calculations(purchase_order_id);
CREATE INDEX idx_cc_rule ON commission_calculations(rule_id);
CREATE INDEX idx_cc_status ON commission_calculations(status);
CREATE INDEX idx_cc_date ON commission_calculations(calculation_date);
CREATE INDEX idx_cc_approved ON commission_calculations(approved_at);

-- فهارس جدول مدفوعات العمولات
CREATE INDEX idx_cp_rep ON commission_payments(rep_id);
CREATE INDEX idx_cp_status ON commission_payments(status);
CREATE INDEX idx_cp_date ON commission_payments(payment_date);
CREATE INDEX idx_cp_transfer ON commission_payments(transfer_request_id);

-- =====================================================
-- البيانات الأولية - أنواع العمولات السبعة
-- Initial Data - Seven Commission Types
-- =====================================================

-- 1. العمولة الثابتة
INSERT INTO commission_types (type_code, type_name, type_name_en, calculation_method, description, display_order) VALUES
('FIXED', 'عمولة ثابتة', 'Fixed Commission', 'FIXED', 'مبلغ ثابت لكل أمر شراء معتمد', 1);

-- 2. العمولة النسبية
INSERT INTO commission_types (type_code, type_name, type_name_en, calculation_method, description, display_order) VALUES
('PERCENTAGE', 'عمولة نسبية', 'Percentage Commission', 'PERCENTAGE', 'نسبة مئوية من قيمة أمر الشراء', 2);

-- 3. العمولة المتدرجة
INSERT INTO commission_types (type_code, type_name, type_name_en, calculation_method, description, display_order) VALUES
('TIERED', 'عمولة متدرجة', 'Tiered Commission', 'TIERED', 'نسب مختلفة حسب شرائح قيمة الطلب', 3);

-- 4. العمولة الثابتة حسب الكمية
INSERT INTO commission_types (type_code, type_name, type_name_en, calculation_method, description, display_order) VALUES
('QUANTITY_FIXED', 'عمولة ثابتة حسب الكمية', 'Fixed Quantity Commission', 'QUANTITY_FIXED', 'مبلغ ثابت لكل وحدة مطلوبة', 4);

-- 5. العمولة المتدرجة حسب الكمية
INSERT INTO commission_types (type_code, type_name, type_name_en, calculation_method, description, display_order) VALUES
('QUANTITY_TIERED', 'عمولة متدرجة حسب الكمية', 'Tiered Quantity Commission', 'QUANTITY_TIERED', 'نسب أو مبالغ مختلفة حسب شرائح الكمية', 5);

-- 6. العمولة حسب الصنف
INSERT INTO commission_types (type_code, type_name, type_name_en, calculation_method, description, display_order) VALUES
('ITEM_BASED', 'عمولة حسب الصنف', 'Item-based Commission', 'ITEM_BASED', 'عمولات مختلفة لأصناف مختلفة', 6);

-- 7. العمولة حسب المورد
INSERT INTO commission_types (type_code, type_name, type_name_en, calculation_method, description, display_order) VALUES
('SUPPLIER_BASED', 'عمولة حسب المورد', 'Supplier-based Commission', 'SUPPLIER_BASED', 'عمولات مختلفة لموردين مختلفين', 7);

-- 8. العمولة الموسمية
INSERT INTO commission_types (type_code, type_name, type_name_en, calculation_method, description, display_order) VALUES
('SEASONAL', 'عمولة موسمية', 'Seasonal Commission', 'SEASONAL', 'عمولات خاصة لفترات محددة', 8);

-- =====================================================
-- بيانات تجريبية للمندوبين
-- Sample Representatives Data
-- =====================================================

-- مندوب أجهزة كمبيوتر
INSERT INTO purchase_representatives (rep_code, rep_name, rep_name_en, department, specialization, target_monthly_orders, target_monthly_quantity, target_monthly_value, phone, email) VALUES
('REP001', 'أحمد محمد الأحمد', 'Ahmed Mohammed Al-Ahmed', 'تقنية المعلومات', 'أجهزة كمبيوتر ومعدات تقنية', 50, 500, 200000, '+966501234567', '<EMAIL>');

-- مندوب مواد غذائية
INSERT INTO purchase_representatives (rep_code, rep_name, rep_name_en, department, specialization, target_monthly_orders, target_monthly_quantity, target_monthly_value, phone, email) VALUES
('REP002', 'فاطمة علي السالم', 'Fatima Ali Al-Salem', 'المشتريات العامة', 'مواد غذائية ومستهلكات', 80, 2000, 150000, '+966507654321', '<EMAIL>');

-- مندوب معدات طبية
INSERT INTO purchase_representatives (rep_code, rep_name, rep_name_en, department, specialization, target_monthly_orders, target_monthly_quantity, target_monthly_value, phone, email) VALUES
('REP003', 'محمد عبدالله الخالد', 'Mohammed Abdullah Al-Khalid', 'المعدات الطبية', 'أجهزة ومعدات طبية', 30, 200, 300000, '+966512345678', '<EMAIL>');

-- مندوب مواد خام
INSERT INTO purchase_representatives (rep_code, rep_name, rep_name_en, department, specialization, target_monthly_orders, target_monthly_quantity, target_monthly_value, phone, email) VALUES
('REP004', 'سارة أحمد المطيري', 'Sara Ahmed Al-Mutairi', 'المواد الخام', 'مواد خام وكيماويات', 25, 5000, 500000, '+966523456789', '<EMAIL>');

COMMIT;
