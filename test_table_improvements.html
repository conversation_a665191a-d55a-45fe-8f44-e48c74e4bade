
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحسينات الجدول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    
    <style>
        /* محاذاة عناوين الجدول لليمين */
        #testTable thead th {
            text-align: right !important;
            direction: rtl;
        }
        
        /* محاذاة محتوى الجدول */
        #testTable tbody td {
            text-align: right;
            direction: rtl;
        }
        
        /* الأعمدة الرقمية بالإنجليزية */
        #testTable tbody td:nth-child(5), /* الكمية */
        #testTable tbody td:nth-child(6), /* سعر الوحدة */
        #testTable tbody td:nth-child(7), /* إجمالي القيمة */
        #testTable tbody td:nth-child(9)  /* تاريخ الإنشاء */
        {
            direction: ltr;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>اختبار تحسينات الجدول</h1>
        
        <div class="alert alert-info">
            <h5>التحسينات المطبقة:</h5>
            <ul>
                <li>✅ محاذاة عناوين الأعمدة لليمين</li>
                <li>✅ عرض الأرقام بالإنجليزية (الكمية، القيمة، التاريخ)</li>
                <li>✅ عرض عمود الوحدة الصحيح</li>
            </ul>
        </div>
        
        <div class="table-responsive">
            <table id="testTable" class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr style="text-align: right;">
                        <th style="text-align: right;">كود الصنف</th>
                        <th style="text-align: right;">اسم الصنف</th>
                        <th style="text-align: right;">أمر الشراء</th>
                        <th style="text-align: right;">المورد</th>
                        <th style="text-align: right;">الكمية</th>
                        <th style="text-align: right;">سعر الوحدة</th>
                        <th style="text-align: right;">إجمالي القيمة</th>
                        <th style="text-align: right;">الوحدة</th>
                        <th style="text-align: right;">تاريخ الإنشاء</th>
                        <th style="text-align: right;">الحالة</th>
                    </tr>
                </thead>
                <tbody>
        
                    <tr>
                        <td>001-1023-</td>
                        <td><strong>حلوى سي سي عملاق جديد 12×30×22جم</strong></td>
                        <td><span class="badge bg-info">صنف من أمر PO-2025-0015</span></td>
                        <td>شركة وايسدوم هاوس</td>
                        <td>3,000</td>
                        <td>$115.00</td>
                        <td><strong>$345,000.00</strong></td>
                        <td>كرتون</td>
                        <td>2025-08-17</td>
                        <td><span class="badge bg-secondary">مسودة</span></td>
                    </tr>
            
                    <tr>
                        <td>001-1009-</td>
                        <td><strong>علكة ثلاجة العائلة 6*380*2جم</strong></td>
                        <td><span class="badge bg-info">صنف من أمر PO-2025-0013</span></td>
                        <td>شركة يابايشينج-الصين</td>
                        <td>2,111</td>
                        <td>$162.00</td>
                        <td><strong>$341,982.00</strong></td>
                        <td>كرتون</td>
                        <td>2025-08-17</td>
                        <td><span class="badge bg-secondary">مسودة</span></td>
                    </tr>
            
                    <tr>
                        <td>001-0604-</td>
                        <td><strong>علكة بوكر 12×200×3جم</strong></td>
                        <td><span class="badge bg-info">صنف من أمر PO-2025-0014</span></td>
                        <td>شركة يابايشينج-الصين</td>
                        <td>2,062</td>
                        <td>$148.00</td>
                        <td><strong>$305,176.00</strong></td>
                        <td>كرتون</td>
                        <td>2025-08-17</td>
                        <td><span class="badge bg-secondary">مسودة</span></td>
                    </tr>
            
                    <tr>
                        <td>001-0925-</td>
                        <td><strong>حلوى بودرة حامض سوبر 12*100*11جم</strong></td>
                        <td><span class="badge bg-info">صنف من أمر PO-2025-0017</span></td>
                        <td>شركة ياهوا فود كومبنى</td>
                        <td>980</td>
                        <td>$264.00</td>
                        <td><strong>$258,720.00</strong></td>
                        <td>كرتون</td>
                        <td>2025-08-17</td>
                        <td><span class="badge bg-secondary">مسودة</span></td>
                    </tr>
            
                    <tr>
                        <td>001-0792-</td>
                        <td><strong>حلوى كرسبي 12×200×3جم</strong></td>
                        <td><span class="badge bg-info">صنف من أمر PO-2025-0010</span></td>
                        <td>شركة رايسن</td>
                        <td>2,040</td>
                        <td>$123.00</td>
                        <td><strong>$250,920.00</strong></td>
                        <td>كرتون</td>
                        <td>2025-08-14</td>
                        <td><span class="badge bg-success">تم التسليم</span></td>
                    </tr>
            
                    <tr>
                        <td>001-0941-</td>
                        <td><strong>حلوى بودرة جامبو 12×48×15جم</strong></td>
                        <td><span class="badge bg-info">صنف من أمر PO-2025-0016</span></td>
                        <td>شركة ياهوا فود كومبنى</td>
                        <td>1,240</td>
                        <td>$136.00</td>
                        <td><strong>$168,640.00</strong></td>
                        <td>كرتون</td>
                        <td>2025-08-17</td>
                        <td><span class="badge bg-secondary">مسودة</span></td>
                    </tr>
            
                    <tr>
                        <td>001-1027-</td>
                        <td><strong>جيلي سليس صودا 24*30*20جم</strong></td>
                        <td><span class="badge bg-info">صنف من أمر PO-2025-0012</span></td>
                        <td>شركة يونجي-الصين</td>
                        <td>928</td>
                        <td>$135.00</td>
                        <td><strong>$125,280.00</strong></td>
                        <td>كرتون</td>
                        <td>2025-08-17</td>
                        <td><span class="badge bg-success">تم التسليم</span></td>
                    </tr>
            
                    <tr>
                        <td>001-0385-</td>
                        <td><strong>حلوى جيلي سمارت قلوب 16×30×35جم</strong></td>
                        <td><span class="badge bg-info">صنف من أمر PO-2025-0011</span></td>
                        <td>شركة يونجي-الصين</td>
                        <td>820</td>
                        <td>$100.00</td>
                        <td><strong>$82,000.00</strong></td>
                        <td>كرتون</td>
                        <td>2025-08-16</td>
                        <td><span class="badge bg-success">تم التسليم</span></td>
                    </tr>
            
                    <tr>
                        <td>001-0385-</td>
                        <td><strong>حلوى جيلي سمارت قلوب 16×30×35جم</strong></td>
                        <td><span class="badge bg-info">صنف من أمر PO-2025-0012</span></td>
                        <td>شركة يونجي-الصين</td>
                        <td>390</td>
                        <td>$100.00</td>
                        <td><strong>$39,000.00</strong></td>
                        <td>كرتون</td>
                        <td>2025-08-17</td>
                        <td><span class="badge bg-success">تم التسليم</span></td>
                    </tr>
            
                    <tr>
                        <td>001-1027-</td>
                        <td><strong>جيلي سليس صودا 24*30*20جم</strong></td>
                        <td><span class="badge bg-info">صنف من أمر PO-2025-0011</span></td>
                        <td>شركة يونجي-الصين</td>
                        <td>280</td>
                        <td>$135.00</td>
                        <td><strong>$37,800.00</strong></td>
                        <td>كرتون</td>
                        <td>2025-08-16</td>
                        <td><span class="badge bg-success">تم التسليم</span></td>
                    </tr>
            
                    <tr>
                        <td>001-1028-</td>
                        <td><strong>حلوى صودا بخاخ 18*30*25مل</strong></td>
                        <td><span class="badge bg-info">صنف من أمر PO-2025-0011</span></td>
                        <td>شركة يونجي-الصين</td>
                        <td>150</td>
                        <td>$153.00</td>
                        <td><strong>$22,950.00</strong></td>
                        <td>كرتون</td>
                        <td>2025-08-16</td>
                        <td><span class="badge bg-success">تم التسليم</span></td>
                    </tr>
            
                    <tr>
                        <td>001-1028-</td>
                        <td><strong>حلوى صودا بخاخ 18*30*25مل</strong></td>
                        <td><span class="badge bg-info">صنف من أمر PO-2025-0012</span></td>
                        <td>شركة يونجي-الصين</td>
                        <td>146</td>
                        <td>$153.00</td>
                        <td><strong>$22,338.00</strong></td>
                        <td>كرتون</td>
                        <td>2025-08-17</td>
                        <td><span class="badge bg-success">تم التسليم</span></td>
                    </tr>
            
                    <tr>
                        <td>001-0925-</td>
                        <td><strong>حلوى بودرة حامض سوبر 12*100*11جم</strong></td>
                        <td><span class="badge bg-info">صنف من أمر PO-2025-0016</span></td>
                        <td>شركة ياهوا فود كومبنى</td>
                        <td>5</td>
                        <td>$264.00</td>
                        <td><strong>$1,320.00</strong></td>
                        <td>كرتون</td>
                        <td>2025-08-17</td>
                        <td><span class="badge bg-secondary">مسودة</span></td>
                    </tr>
            
                </tbody>
            </table>
        </div>
        
        <div class="mt-4">
            <button class="btn btn-primary" onclick="testAPI()">اختبار API المحدث</button>
            <div id="apiResult" class="mt-3"></div>
        </div>
    </div>

    <script>
    $(document).ready(function() {
        $('#testTable').DataTable({
            language: {
                processing: "جاري المعالجة...",
                search: "بحث:",
                lengthMenu: "أظهر _MENU_ مدخلات",
                info: "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                infoEmpty: "يعرض 0 إلى 0 من أصل 0 سجل",
                infoFiltered: "(منتقاة من مجموع _MAX_ مُدخل)",
                loadingRecords: "جاري التحميل...",
                zeroRecords: "لم يعثر على أية سجلات",
                emptyTable: "لا توجد بيانات متاحة في الجدول",
                paginate: {
                    first: "الأول",
                    previous: "السابق",
                    next: "التالي",
                    last: "الأخير"
                }
            },
            pageLength: 10,
            order: [[6, 'desc']] // ترتيب حسب إجمالي القيمة
        });
    });
    
    function testAPI() {
        $('#apiResult').html('<div class="text-center"><div class="spinner-border"></div><br>جاري الاختبار...</div>');
        
        fetch('/purchase-orders/api/items/data')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    $('#apiResult').html(`
                        <div class="alert alert-success">
                            <h5>✅ API المحدث يعمل بنجاح!</h5>
                            <p>تم جلب ${data.data.length} صنف مع عمود الوحدة</p>
                            <p>عينة من الوحدات: ${data.data.slice(0,3).map(item => item.unit).join(', ')}</p>
                        </div>
                    `);
                } else {
                    $('#apiResult').html(`
                        <div class="alert alert-danger">
                            <h5>❌ خطأ في API</h5>
                            <p>${data.error}</p>
                        </div>
                    `);
                }
            })
            .catch(error => {
                $('#apiResult').html(`
                    <div class="alert alert-danger">
                        <h5>❌ خطأ في الشبكة</h5>
                        <p>${error}</p>
                    </div>
                `);
            });
    }
    </script>
</body>
</html>
        