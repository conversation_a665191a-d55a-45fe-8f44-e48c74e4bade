<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قواعد العمولات - نظام الفوجي</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/commission-opening-balances-style.css') }}" rel="stylesheet">
</head>
<body>

    <!-- Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="fas fa-cogs me-3"></i>
                        قواعد العمولات
                    </h1>
                    <p class="page-subtitle">
                        ربط المندوبين بأنواع العمولات وإعداد القواعد المتقدمة للحساب
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{{ url_for('purchase_commissions.index') }}" class="btn btn-light btn-modern">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="breadcrumb-container">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('purchase_commissions.index') }}">عمولات المندوبين</a></li>
                    <li class="breadcrumb-item active" aria-current="page">قواعد العمولات</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="container">

        <!-- Control Panel -->
        <div class="control-panel">
            <h5>
                <i class="fas fa-cogs"></i>
                إجراءات سريعة
            </h5>
            <div class="row g-3">
                <div class="col-md-3">
                    <button type="button" class="btn btn-primary btn-modern w-100" data-bs-toggle="modal" data-bs-target="#addRuleModal">
                        <i class="fas fa-plus"></i>
                        إضافة قاعدة جديدة
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-success btn-modern w-100" onclick="exportData()">
                        <i class="fas fa-download"></i>
                        تصدير البيانات
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-info btn-modern w-100" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث البيانات
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-warning btn-modern w-100" onclick="printReport()">
                        <i class="fas fa-print"></i>
                        طباعة التقرير
                    </button>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="stats-container">
            <div class="row g-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon primary">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="stat-value">{{ commission_rules|length }}</div>
                        <div class="stat-label">إجمالي القواعد</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-value">{{ commission_rules|selectattr('is_active', 'equalto', 1)|list|length }}</div>
                        <div class="stat-label">القواعد النشطة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon info">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-value">{{ commission_rules|map(attribute='representative_id')|unique|list|length }}</div>
                        <div class="stat-label">المندوبين المرتبطين</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon warning">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-value">{{ commission_rules|map(attribute='commission_type_id')|unique|list|length }}</div>
                        <div class="stat-label">أنواع العمولات</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Data Table -->
        <div class="data-table-container">
            <div class="data-table-header">
                <h5 class="data-table-title">
                    <i class="fas fa-table"></i>
                    قواعد العمولات المُعرفة
                </h5>
                <div>
                    <button class="btn btn-primary btn-modern btn-sm" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-modern" id="commissionRulesTable">
                    <thead>
                        <tr>
                            <th>المندوب</th>
                            <th>نوع العمولة</th>
                            <th>القيمة/النسبة</th>
                            <th>الحد الأدنى</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for rule in commission_rules %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon primary me-2" style="width: 40px; height: 40px; font-size: 1rem;">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div>
                                        <strong>{{ rule.rep_name or 'غير محدد' }}</strong>
                                        <br><small class="text-muted">كود: {{ rule.rep_code or 'غير محدد' }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge badge-modern bg-info">{{ rule.type_name or 'غير محدد' }}</span>
                            </td>
                            <td>
                                {% if rule.fixed_amount and rule.fixed_amount > 0 %}
                                <strong>{{ "{:,.2f}".format(rule.fixed_amount) }} ريال</strong>
                                {% elif rule.percentage_rate and rule.percentage_rate > 0 %}
                                <strong>{{ rule.percentage_rate }}%</strong>
                                {% elif rule.quantity_rate and rule.quantity_rate > 0 %}
                                <strong>{{ rule.quantity_rate }} / {{ rule.quantity_unit or 'وحدة' }}</strong>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if rule.min_order_value and rule.min_order_value > 0 %}
                                {{ "{:,.2f}".format(rule.min_order_value) }} ريال
                                {% else %}
                                <span class="text-muted">لا يوجد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if rule.is_active %}
                                <span class="badge badge-modern bg-success">نشط</span>
                                {% else %}
                                <span class="badge badge-modern bg-secondary">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('purchase_commissions.edit_commission_rule', rule_id=rule.id) }}"
                                       class="btn btn-warning btn-modern btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button class="btn btn-danger btn-modern btn-sm"
                                            onclick="deleteCommissionRule({{ rule.id }}, '{{ rule.rep_name or 'قاعدة العمولة' }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function refreshData() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            button.disabled = true;

            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                showAlert('تم تحديث البيانات بنجاح', 'success');
            }, 2000);
        }

        function exportData() {
            showAlert('سيتم تصدير البيانات قريباً', 'info');
        }

        function printReport() {
            window.print();
        }

        function deleteCommissionRule(id, repName) {
            if (confirm(`هل أنت متأكد من حذف قاعدة العمولة للمندوب "${repName}"؟`)) {
                // إضافة منطق الحذف هنا
                showAlert(`تم حذف قاعدة العمولة للمندوب "${repName}" بنجاح`, 'success');
            }
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-modern alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'info' ? 'info-circle' : 'exclamation-triangle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('.container').insertBefore(alert, document.querySelector('.control-panel'));

            setTimeout(() => {
                alert.remove();
            }, 3000);
        }

        // تأثيرات hover للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(-5px)';
                });
            });
        });
    </script>

</body>
</html>
