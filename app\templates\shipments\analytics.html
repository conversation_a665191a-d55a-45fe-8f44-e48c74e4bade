{% extends "base.html" %}

{% block content %}
<style>
.analytics-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    border: none;
}

.chart-container {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.metric-card {
    text-align: center;
    padding: 1.5rem;
    border-radius: 10px;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.metric-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0.5rem 0;
}

.metric-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.trend-up {
    color: #28a745;
}

.trend-down {
    color: #dc3545;
}

.filter-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}
</style>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-line text-primary me-2"></i>
                        التحليلات والتقارير
                    </h1>
                    <p class="text-muted mb-0">تحليلات شاملة لأداء الشحنات</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="exportReport()">
                        <i class="fas fa-download me-2"></i>
                        تصدير التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <div class="row align-items-end">
            <div class="col-md-3">
                <label class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="startDate" value="{{ start_date }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="endDate" value="{{ end_date }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">شركة الشحن</label>
                <select class="form-select" id="carrierFilter">
                    <option value="">جميع الشركات</option>
                    {% for carrier in carriers %}
                    <option value="{{ carrier.id }}">{{ carrier.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <button class="btn btn-primary w-100" onclick="updateAnalytics()">
                    <i class="fas fa-sync me-2"></i>
                    تحديث
                </button>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-card">
                <i class="fas fa-boxes text-primary fa-2x mb-2"></i>
                <div class="metric-value text-primary">{{ metrics.total_shipments or 0 }}</div>
                <div class="metric-label">إجمالي الشحنات</div>
                <small class="trend-up">
                    <i class="fas fa-arrow-up"></i>
                    +12% من الشهر الماضي
                </small>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-card">
                <i class="fas fa-percentage text-success fa-2x mb-2"></i>
                <div class="metric-value text-success">{{ "%.1f"|format(metrics.delivery_rate or 0) }}%</div>
                <div class="metric-label">معدل التسليم الناجح</div>
                <small class="trend-up">
                    <i class="fas fa-arrow-up"></i>
                    +2.5% من الشهر الماضي
                </small>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-card">
                <i class="fas fa-clock text-warning fa-2x mb-2"></i>
                <div class="metric-value text-warning">{{ "%.1f"|format(metrics.avg_delivery_time or 0) }}</div>
                <div class="metric-label">متوسط وقت التسليم (ساعة)</div>
                <small class="trend-down">
                    <i class="fas fa-arrow-down"></i>
                    -1.2 ساعة من الشهر الماضي
                </small>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-card">
                <i class="fas fa-money-bill text-info fa-2x mb-2"></i>
                <div class="metric-value text-info">{{ "{:,.0f}"|format(metrics.total_revenue or 0) }}</div>
                <div class="metric-label">إجمالي الإيرادات (ريال)</div>
                <small class="trend-up">
                    <i class="fas fa-arrow-up"></i>
                    +8.3% من الشهر الماضي
                </small>
            </div>
        </div>
    </div>

    <!-- Charts Row 1 -->
    <div class="row mb-4">
        <!-- Shipments Trend -->
        <div class="col-lg-8 mb-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-line me-2"></i>
                    اتجاه الشحنات
                </h5>
                <canvas id="shipmentsChart" height="300"></canvas>
            </div>
        </div>

        <!-- Status Distribution -->
        <div class="col-lg-4 mb-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع الحالات
                </h5>
                <canvas id="statusChart" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- Charts Row 2 -->
    <div class="row mb-4">
        <!-- Geographic Distribution -->
        <div class="col-lg-6 mb-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-map me-2"></i>
                    التوزيع الجغرافي
                </h5>
                <canvas id="geoChart" height="300"></canvas>
            </div>
        </div>

        <!-- Carrier Performance -->
        <div class="col-lg-6 mb-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-truck me-2"></i>
                    أداء شركات الشحن
                </h5>
                <canvas id="carrierChart" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- Performance Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        تفاصيل الأداء
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th>شركة الشحن</th>
                                    <th>إجمالي الشحنات</th>
                                    <th>تم التسليم</th>
                                    <th>معدل النجاح</th>
                                    <th>متوسط الوقت</th>
                                    <th>متوسط التكلفة</th>
                                    <th>التقييم</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for performance in carrier_performance %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="bg-primary rounded-circle p-2 me-2" style="width: 35px; height: 35px; display: flex; align-items: center; justify-content: center;">
                                                <span class="text-white fw-bold" style="font-size: 0.8rem;">{{ performance.carrier_name[0] if performance.carrier_name else 'C' }}</span>
                                            </div>
                                            {{ performance.carrier_name }}
                                        </div>
                                    </td>
                                    <td>{{ performance.total_shipments }}</td>
                                    <td>{{ performance.delivered_shipments }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if performance.delivery_rate >= 90 else 'warning' if performance.delivery_rate >= 70 else 'danger' }}">
                                            {{ "%.1f"|format(performance.delivery_rate) }}%
                                        </span>
                                    </td>
                                    <td>{{ "%.1f"|format(performance.avg_time) }} ساعة</td>
                                    <td>{{ "%.2f"|format(performance.avg_cost) }} ريال</td>
                                    <td>
                                        <div class="text-warning">
                                            {% for i in range(5) %}
                                                <i class="fas fa-star{{ '' if i < performance.rating else '-o' }}"></i>
                                            {% endfor %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// بيانات الرسوم البيانية
const chartData = {{ chart_data | tojson }};

// رسم بياني لاتجاه الشحنات
const shipmentsCtx = document.getElementById('shipmentsChart').getContext('2d');
new Chart(shipmentsCtx, {
    type: 'line',
    data: {
        labels: chartData.dates || [],
        datasets: [{
            label: 'عدد الشحنات',
            data: chartData.shipment_counts || [],
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4
        }, {
            label: 'الإيرادات (ريال)',
            data: chartData.revenues || [],
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                grid: {
                    drawOnChartArea: false,
                },
            }
        }
    }
});

// رسم بياني لتوزيع الحالات
const statusCtx = document.getElementById('statusChart').getContext('2d');
new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: chartData.status_labels || [],
        datasets: [{
            data: chartData.status_values || [],
            backgroundColor: [
                '#007bff',
                '#28a745',
                '#ffc107',
                '#dc3545',
                '#6c757d'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// رسم بياني للتوزيع الجغرافي
const geoCtx = document.getElementById('geoChart').getContext('2d');
new Chart(geoCtx, {
    type: 'bar',
    data: {
        labels: chartData.cities || [],
        datasets: [{
            label: 'عدد الشحنات',
            data: chartData.city_counts || [],
            backgroundColor: '#007bff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// رسم بياني لأداء شركات الشحن
const carrierCtx = document.getElementById('carrierChart').getContext('2d');
new Chart(carrierCtx, {
    type: 'bar',
    data: {
        labels: chartData.carrier_names || [],
        datasets: [{
            label: 'معدل النجاح (%)',
            data: chartData.carrier_rates || [],
            backgroundColor: '#28a745'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 100
            }
        }
    }
});

// تحديث التحليلات
function updateAnalytics() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const carrier = document.getElementById('carrierFilter').value;
    
    const params = new URLSearchParams({
        start_date: startDate,
        end_date: endDate,
        carrier_id: carrier
    });
    
    window.location.href = `{{ url_for('shipments.analytics') }}?${params}`;
}

// تصدير التقرير
function exportReport() {
    alert('تصدير التقرير - قريباً');
}

// تحديث التواريخ الافتراضية
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
    
    if (!document.getElementById('startDate').value) {
        document.getElementById('startDate').value = lastMonth.toISOString().split('T')[0];
    }
    if (!document.getElementById('endDate').value) {
        document.getElementById('endDate').value = today.toISOString().split('T')[0];
    }
});
</script>

{% endblock %}
