#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تحديث الكميات المنفذة والمتبقية في تفاصيل العقود
"""

import sys
import os
sys.path.append('.')

from oracle_manager import OracleManager

def main():
    try:
        print("🔧 تحديث الكميات المنفذة والمتبقية...")
        
        oracle_manager = OracleManager()
        
        # الخطوة 1: حساب الكميات المنفذة الفعلية لكل عقد
        print("\n📊 حساب الكميات المنفذة الفعلية...")
        
        calculate_query = """
        SELECT 
            cd.CONTRACT_ID,
            cd.ITEM_ID,
            cd.QUANTITY as CONTRACT_QTY,
            NVL(SUM(pi.QUANTITY), 0) as ACTUAL_EXECUTED_QTY,
            cd.QUANTITY - NVL(SUM(pi.QUANTITY), 0) as ACTUAL_REMAINING_QTY
        FROM CONTRACT_DETAILS cd
        LEFT JOIN PURCHASE_ORDERS po ON cd.CONTRACT_ID = po.CONTRACT_ID
        LEFT JOIN PO_ITEMS pi ON po.ID = pi.PO_ID 
            AND cd.ITEM_ID = pi.ITEM_CODE
        GROUP BY cd.CONTRACT_ID, cd.ITEM_ID, cd.QUANTITY
        ORDER BY cd.CONTRACT_ID, cd.ITEM_ID
        """
        
        calculated_results = oracle_manager.execute_query(calculate_query)
        
        print("\n" + "="*80)
        print("الكميات المحسوبة:")
        print("="*80)
        print(f"{'عقد':<5} {'كود الصنف':<15} {'الأصلية':<10} {'المنفذة':<10} {'المتبقية':<10}")
        print("-"*80)
        
        updates_needed = []
        
        for row in calculated_results:
            contract_id, item_id, contract_qty, actual_exec, actual_remain = row
            item_id_str = str(item_id) if item_id else "غير محدد"
            print(f"{contract_id:<5} {item_id_str[:15]:<15} {contract_qty:<10} {actual_exec:<10} {actual_remain:<10}")
            
            # إضافة للقائمة إذا كانت هناك حاجة للتحديث
            updates_needed.append({
                'contract_id': contract_id,
                'item_id': item_id,
                'executed_qty': actual_exec,
                'remaining_qty': actual_remain
            })
        
        print("-"*80)
        print(f"✅ تم حساب {len(updates_needed)} صنف")
        
        # الخطوة 2: تحديث قاعدة البيانات
        print("\n🔄 تحديث قاعدة البيانات...")
        
        update_sql = """
        UPDATE CONTRACT_DETAILS 
        SET EXECUTED_QUANTITY = :1,
            REMAINING_QUANTITY = :2,
            UPDATED_AT = SYSDATE
        WHERE CONTRACT_ID = :3 
        AND ITEM_ID = :4
        """
        
        updated_count = 0
        
        for update_data in updates_needed:
            try:
                params = [
                    update_data['executed_qty'],
                    update_data['remaining_qty'],
                    update_data['contract_id'],
                    update_data['item_id']
                ]
                
                result = oracle_manager.execute_update(update_sql, params)
                
                if result > 0:
                    updated_count += 1
                    print(f"✅ تم تحديث العقد {update_data['contract_id']} - الصنف {update_data['item_id']}")
                
            except Exception as e:
                print(f"❌ خطأ في تحديث العقد {update_data['contract_id']} - الصنف {update_data['item_id']}: {e}")
        
        print(f"\n✅ تم تحديث {updated_count} صنف بنجاح")
        
        # الخطوة 3: التحقق من النتائج
        print("\n🔍 التحقق من النتائج...")
        
        verification_query = """
        SELECT 
            cd.CONTRACT_ID,
            cd.ITEM_ID,
            cd.ITEM_NAME,
            cd.QUANTITY as ORIGINAL_QTY,
            cd.EXECUTED_QUANTITY,
            cd.REMAINING_QUANTITY,
            CASE 
                WHEN cd.EXECUTED_QUANTITY = 0 THEN 'لم ينفذ'
                WHEN cd.EXECUTED_QUANTITY < cd.QUANTITY THEN 'منفذ جزئياً'
                WHEN cd.EXECUTED_QUANTITY >= cd.QUANTITY THEN 'منفذ كلياً'
            END AS EXECUTION_STATUS
        FROM CONTRACT_DETAILS cd
        ORDER BY cd.CONTRACT_ID, cd.ITEM_ID
        """
        
        verification_results = oracle_manager.execute_query(verification_query)
        
        print("\n" + "="*100)
        print("النتائج النهائية:")
        print("="*100)
        print(f"{'عقد':<5} {'كود':<15} {'اسم الصنف':<25} {'الأصلية':<10} {'المنفذة':<10} {'المتبقية':<10} {'الحالة':<15}")
        print("-"*100)
        
        for row in verification_results:
            contract_id, item_id, item_name, orig_qty, exec_qty, remain_qty, status = row
            item_id_str = str(item_id) if item_id else "غير محدد"
            item_name_str = str(item_name) if item_name else "غير محدد"
            print(f"{contract_id:<5} {item_id_str[:15]:<15} {item_name_str[:25]:<25} {orig_qty:<10} {exec_qty:<10} {remain_qty:<10} {status:<15}")
        
        print("="*100)
        
        # الخطوة 4: تحديث حالة العقود
        print("\n🔄 تحديث حالة العقود...")
        
        # حساب حالة كل عقد
        contract_status_query = """
        SELECT 
            CONTRACT_ID,
            COUNT(*) as TOTAL_ITEMS,
            SUM(CASE WHEN EXECUTED_QUANTITY >= QUANTITY THEN 1 ELSE 0 END) as FULLY_EXECUTED_ITEMS,
            SUM(CASE WHEN EXECUTED_QUANTITY > 0 AND EXECUTED_QUANTITY < QUANTITY THEN 1 ELSE 0 END) as PARTIALLY_EXECUTED_ITEMS
        FROM CONTRACT_DETAILS
        GROUP BY CONTRACT_ID
        ORDER BY CONTRACT_ID
        """
        
        contract_statuses = oracle_manager.execute_query(contract_status_query)
        
        for row in contract_statuses:
            contract_id, total_items, fully_executed, partially_executed = row
            
            # تحديد الحالة الجديدة
            if fully_executed == total_items:
                new_status = 'FULLY_EXECUTED'
                status_ar = 'منفذ كلياً'
            elif partially_executed > 0 or fully_executed > 0:
                new_status = 'PARTIALLY_EXECUTED'
                status_ar = 'منفذ جزئياً'
            else:
                new_status = 'APPROVED'  # لم ينفذ أي شيء بعد
                status_ar = 'معتمد'
            
            # تحديث حالة العقد
            update_contract_sql = """
            UPDATE CONTRACTS 
            SET CONTRACT_STATUS = :1,
                UPDATED_AT = SYSDATE
            WHERE CONTRACT_ID = :2
            """
            
            try:
                result = oracle_manager.execute_update(update_contract_sql, [new_status, contract_id])
                if result > 0:
                    print(f"✅ تم تحديث حالة العقد {contract_id} إلى: {status_ar}")
            except Exception as e:
                print(f"❌ خطأ في تحديث حالة العقد {contract_id}: {e}")
        
        print("\n🎉 تم إنجاز تحديث الكميات المنفذة والمتبقية بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
