#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحليل عميق لموقع COSCO لفهم آلية البحث الحقيقية
"""

import requests
import urllib3
import re
import json
from datetime import datetime

# تعطيل تحذيرات SSL
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class COSCOAnalyzer:
    def __init__(self):
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
    def analyze_main_page(self):
        """تحليل الصفحة الرئيسية لفهم البنية"""
        print("🔍 تحليل الصفحة الرئيسية...")
        
        url = "https://elines.coscoshipping.com/ebusiness/cargoTracking"
        
        try:
            response = self.session.get(url, timeout=20)
            
            if response.status_code == 200:
                print(f"✅ تم الوصول للصفحة بنجاح")
                
                # البحث عن JavaScript files
                js_files = re.findall(r'src="([^"]*\.js[^"]*)"', response.text)
                print(f"📜 JavaScript files found: {len(js_files)}")
                for js in js_files[:5]:  # أول 5 ملفات
                    print(f"  - {js}")
                
                # البحث عن API endpoints
                api_patterns = [
                    r'api["\']?\s*:\s*["\']([^"\']+)["\']',
                    r'url["\']?\s*:\s*["\']([^"\']+)["\']',
                    r'endpoint["\']?\s*:\s*["\']([^"\']+)["\']',
                    r'/api/[^"\'\s]+',
                    r'/ebusiness/[^"\'\s]+'
                ]
                
                print(f"🔗 البحث عن API endpoints...")
                for pattern in api_patterns:
                    matches = re.findall(pattern, response.text, re.IGNORECASE)
                    if matches:
                        print(f"  Pattern '{pattern}': {matches[:3]}")
                
                # البحث عن forms
                forms = re.findall(r'<form[^>]*>(.*?)</form>', response.text, re.DOTALL | re.IGNORECASE)
                print(f"📝 Forms found: {len(forms)}")
                
                # البحث عن input fields
                inputs = re.findall(r'<input[^>]*name="([^"]*)"[^>]*>', response.text, re.IGNORECASE)
                print(f"📋 Input fields: {inputs}")
                
                # البحث عن CSRF tokens
                csrf_patterns = [
                    r'csrf["\']?\s*:\s*["\']([^"\']+)["\']',
                    r'_token["\']?\s*:\s*["\']([^"\']+)["\']',
                    r'name="_token"[^>]*value="([^"]*)"'
                ]
                
                print(f"🔑 البحث عن CSRF tokens...")
                for pattern in csrf_patterns:
                    matches = re.findall(pattern, response.text, re.IGNORECASE)
                    if matches:
                        print(f"  CSRF token found: {matches[0][:20]}...")
                
                return response.text
            else:
                print(f"❌ فشل في الوصول: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ خطأ: {e}")
            return None
    
    def analyze_network_requests(self):
        """محاولة تحليل طلبات الشبكة"""
        print("\n🌐 تحليل طلبات الشبكة...")
        
        # محاولة البحث بطرق مختلفة
        test_number = "TEST123456"
        
        # طريقة 1: GET مع parameters
        print("📤 اختبار GET مع parameters...")
        try:
            params = {'trackingNo': test_number, 'trackingType': '2'}
            response = self.session.get(
                "https://elines.coscoshipping.com/ebusiness/cargoTracking",
                params=params,
                timeout=15
            )
            print(f"  Status: {response.status_code}")
            print(f"  URL: {response.url}")
            
        except Exception as e:
            print(f"  خطأ: {e}")
        
        # طريقة 2: POST إلى cargoTrackingDetail
        print("📤 اختبار POST إلى cargoTrackingDetail...")
        try:
            data = {'trackingNo': test_number, 'trackingType': '2'}
            response = self.session.post(
                "https://elines.coscoshipping.com/ebusiness/cargoTracking/cargoTrackingDetail",
                data=data,
                timeout=15
            )
            print(f"  Status: {response.status_code}")
            
        except Exception as e:
            print(f"  خطأ: {e}")
        
        # طريقة 3: البحث عن API endpoints
        print("📤 اختبار API endpoints...")
        api_endpoints = [
            "/ebusiness/api/cargoTracking",
            "/api/tracking",
            "/ebusiness/cargoTracking/search",
            "/ebusiness/cargoTracking/query"
        ]
        
        for endpoint in api_endpoints:
            try:
                url = f"https://elines.coscoshipping.com{endpoint}"
                response = self.session.post(
                    url,
                    json={'trackingNo': test_number, 'trackingType': '2'},
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )
                print(f"  {endpoint}: {response.status_code}")
                
            except Exception as e:
                print(f"  {endpoint}: خطأ - {e}")
    
    def extract_javascript_logic(self, html_content):
        """استخراج منطق JavaScript"""
        print("\n🔧 تحليل JavaScript...")
        
        if not html_content:
            return
        
        # البحث عن دوال JavaScript مهمة
        js_functions = [
            r'function\s+(\w*track\w*)\s*\([^)]*\)\s*{([^}]+)}',
            r'function\s+(\w*search\w*)\s*\([^)]*\)\s*{([^}]+)}',
            r'function\s+(\w*query\w*)\s*\([^)]*\)\s*{([^}]+)}'
        ]
        
        for pattern in js_functions:
            matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                func_name, func_body = match
                print(f"📜 Function: {func_name}")
                print(f"   Body: {func_body[:100]}...")
        
        # البحث عن AJAX calls
        ajax_patterns = [
            r'\$\.ajax\s*\(\s*{([^}]+)}',
            r'\$\.post\s*\(\s*["\']([^"\']+)["\']',
            r'fetch\s*\(\s*["\']([^"\']+)["\']',
            r'XMLHttpRequest.*open\s*\(\s*["\']([^"\']+)["\']'
        ]
        
        print(f"🔄 البحث عن AJAX calls...")
        for pattern in ajax_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            if matches:
                print(f"  AJAX found: {matches[:2]}")

def main():
    print("=" * 80)
    print("🔬 تحليل عميق لموقع COSCO")
    print("=" * 80)
    
    analyzer = COSCOAnalyzer()
    
    # تحليل الصفحة الرئيسية
    html_content = analyzer.analyze_main_page()
    
    # تحليل طلبات الشبكة
    analyzer.analyze_network_requests()
    
    # تحليل JavaScript
    analyzer.extract_javascript_logic(html_content)
    
    print("\n" + "=" * 80)
    print("✅ انتهى التحليل")
    print("=" * 80)

if __name__ == "__main__":
    main()
