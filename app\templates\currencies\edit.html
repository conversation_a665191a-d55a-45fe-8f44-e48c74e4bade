<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل العملة - {{ currency.name_ar }}</title>
    
    <!-- Bootstrap 5.3 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 15px;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            margin: 0 auto;
            max-width: 1200px;
        }
        
        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 25px 30px;
            color: white;
            position: relative;
        }
        
        .breadcrumb-modern {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 8px 20px;
            margin-bottom: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 0.9rem;
        }
        
        .breadcrumb-modern .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-weight: 500;
        }
        
        .breadcrumb-modern .breadcrumb-item.active {
            color: white;
            font-weight: 600;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 8px;
            line-height: 1.2;
        }
        
        .page-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            margin: 0;
        }
        
        .content-section {
            padding: 25px 30px;
            background: white;
        }
        
        .info-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            background: white;
            border-radius: 12px;
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .info-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }
        
        .info-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
            flex-shrink: 0;
        }
        
        .info-content h6 {
            margin: 0 0 4px 0;
            font-size: 0.75rem;
            color: #64748b;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .info-content .value {
            font-weight: 700;
            color: #1e293b;
            font-size: 1rem;
            margin: 0;
        }
        
        .form-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border-left: 4px solid #667eea;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f5f9;
        }
        
        .section-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            font-size: 1.2rem;
            color: white;
            flex-shrink: 0;
        }
        
        .section-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0 0 4px 0;
        }
        
        .section-subtitle {
            color: #64748b;
            font-size: 0.9rem;
            margin: 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .form-control, .form-select {
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: #fafbfc;
            font-weight: 500;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }
        
        .form-check-modern {
            background: #f8fafc;
            border-radius: 12px;
            padding: 15px;
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }
        
        .form-check-modern:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        .form-check-input {
            width: 1.3rem;
            height: 1.3rem;
            border-radius: 6px;
            border: 2px solid #d1d5db;
        }
        
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .form-check-label {
            font-weight: 600;
            color: #374151;
            margin-right: 12px;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .preview-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 20px;
            color: white;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .preview-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .preview-amount {
            font-size: 2.2rem;
            font-weight: 800;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .preview-details {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 15px;
        }
        
        .preview-detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.85rem;
            font-weight: 500;
        }
        
        .preview-detail-item:last-child {
            margin-bottom: 0;
        }
        
        .preview-detail-value {
            font-weight: 700;
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 6px;
        }
        
        .action-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            position: sticky;
            top: 20px;
        }
        
        .btn-premium {
            padding: 12px 20px;
            border-radius: 10px;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            min-height: 45px;
            text-decoration: none;
            cursor: pointer;
        }
        
        .btn-premium:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            text-decoration: none;
        }
        
        .btn-save {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }
        
        .btn-reset {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            color: white;
        }
        
        .btn-delete {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }
        
        .btn-back {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 18px;
            height: 18px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
            }
            
            .header-section, .content-section {
                padding: 20px;
            }
            
            .page-title {
                font-size: 1.6rem;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .action-section {
                position: static;
                margin-top: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-modern">
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('main.dashboard') }}">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('currencies.index') }}">
                            <i class="fas fa-coins"></i> إدارة العملات
                        </a>
                    </li>
                    <li class="breadcrumb-item active">تعديل العملة</li>
                </ol>
            </nav>

            <h1 class="page-title">
                <i class="fas fa-edit"></i>
                تعديل العملة: {{ currency.name_ar }}
            </h1>
            <p class="page-subtitle">تحديث وتعديل بيانات العملة وإعداداتها</p>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <div class="row">
                <!-- العمود الأيسر - النموذج -->
                <div class="col-lg-8">
                    <!-- معلومات العملة الحالية -->
                    <div class="info-card">
                        <h6 class="mb-3" style="font-size: 1rem; font-weight: 700; color: #1e293b;">
                            <i class="fas fa-info-circle" style="margin-left: 8px; color: #667eea;"></i>
                            معلومات العملة الحالية
                        </h6>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                                    <i class="fas fa-code"></i>
                                </div>
                                <div class="info-content">
                                    <h6>كود العملة</h6>
                                    <div class="value">{{ currency.code }}</div>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                                    <i class="fas fa-toggle-{% if currency.is_active %}on{% else %}off{% endif %}"></i>
                                </div>
                                <div class="info-content">
                                    <h6>الحالة</h6>
                                    <div class="value">{% if currency.is_active %}نشطة{% else %}غير نشطة{% endif %}</div>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                    <i class="fas fa-star"></i>
                                </div>
                                <div class="info-content">
                                    <h6>النوع</h6>
                                    <div class="value">{% if currency.is_base_currency %}عملة أساسية{% else %}عملة عادية{% endif %}</div>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="info-content">
                                    <h6>سعر الصرف</h6>
                                    <div class="value">{{ "%.4f"|format(currency.exchange_rate) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- نموذج التعديل -->
                    <form id="editCurrencyForm">
                        <!-- المعلومات الأساسية -->
                        <div class="form-section">
                            <div class="section-header">
                                <div class="section-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div>
                                    <h3 class="section-title">المعلومات الأساسية</h3>
                                    <p class="section-subtitle">تحديث بيانات العملة الأساسية</p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-hashtag"></i> كود العملة
                                        </label>
                                        <input type="text" class="form-control" value="{{ currency.code }}" readonly>
                                        <small class="text-muted">لا يمكن تغيير كود العملة</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-dollar-sign"></i> رمز العملة *
                                        </label>
                                        <input type="text" class="form-control" name="symbol"
                                               value="{{ currency.symbol }}" onchange="updatePreview()" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-font"></i> الاسم بالعربية *
                                        </label>
                                        <input type="text" class="form-control" name="name_ar"
                                               value="{{ currency.name_ar }}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-globe"></i> الاسم بالإنجليزية *
                                        </label>
                                        <input type="text" class="form-control" name="name_en"
                                               value="{{ currency.name_en }}" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات التنسيق -->
                        <div class="form-section">
                            <div class="section-header">
                                <div class="section-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <div>
                                    <h3 class="section-title">إعدادات التنسيق</h3>
                                    <p class="section-subtitle">تخصيص طريقة عرض العملة</p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-sort-numeric-up"></i> المنازل العشرية
                                        </label>
                                        <select class="form-select" name="decimal_places" onchange="updatePreview()">
                                            <option value="0" {% if currency.decimal_places == 0 %}selected{% endif %}>0 - بدون كسور</option>
                                            <option value="1" {% if currency.decimal_places == 1 %}selected{% endif %}>1 - منزلة واحدة</option>
                                            <option value="2" {% if currency.decimal_places == 2 %}selected{% endif %}>2 - منزلتان</option>
                                            <option value="3" {% if currency.decimal_places == 3 %}selected{% endif %}>3 - ثلاث منازل</option>
                                            <option value="4" {% if currency.decimal_places == 4 %}selected{% endif %}>4 - أربع منازل</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-arrows-alt-h"></i> موضع الرمز
                                        </label>
                                        <select class="form-select" name="position" onchange="updatePreview()">
                                            <option value="before" {% if currency.position == 'before' %}selected{% endif %}>قبل المبلغ</option>
                                            <option value="after" {% if currency.position == 'after' %}selected{% endif %}>بعد المبلغ</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-grip-lines-vertical"></i> فاصل الآلاف
                                        </label>
                                        <select class="form-select" name="thousands_separator" onchange="updatePreview()">
                                            <option value="," {% if currency.thousands_separator == ',' %}selected{% endif %}>, (فاصلة)</option>
                                            <option value="." {% if currency.thousands_separator == '.' %}selected{% endif %}>. (نقطة)</option>
                                            <option value=" " {% if currency.thousands_separator == ' ' %}selected{% endif %}>(مسافة)</option>
                                            <option value="" {% if currency.thousands_separator == '' %}selected{% endif %}>بدون فاصل</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-circle"></i> فاصل العشرية
                                        </label>
                                        <select class="form-select" name="decimal_separator" onchange="updatePreview()">
                                            <option value="." {% if currency.decimal_separator == '.' %}selected{% endif %}>. (نقطة)</option>
                                            <option value="," {% if currency.decimal_separator == ',' %}selected{% endif %}>, (فاصلة)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات النظام -->
                        <div class="form-section">
                            <div class="section-header">
                                <div class="section-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                    <i class="fas fa-sliders-h"></i>
                                </div>
                                <div>
                                    <h3 class="section-title">إعدادات النظام</h3>
                                    <p class="section-subtitle">تحكم في حالة العملة ونوعها</p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check-modern">
                                        <input class="form-check-input" type="checkbox" name="is_active"
                                               {% if currency.is_active %}checked{% endif %} id="isActive">
                                        <label class="form-check-label" for="isActive">
                                            <i class="fas fa-power-off"></i> عملة نشطة
                                        </label>
                                        <small class="form-text text-muted d-block mt-1">
                                            العملات غير النشطة لا تظهر في القوائم
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check-modern">
                                        <input class="form-check-input" type="checkbox" name="is_base_currency"
                                               {% if currency.is_base_currency %}checked{% endif %} id="isBaseCurrency">
                                        <label class="form-check-label" for="isBaseCurrency">
                                            <i class="fas fa-star"></i> عملة أساسية
                                        </label>
                                        <small class="form-text text-muted d-block mt-1">
                                            العملة المرجعية لحساب أسعار الصرف
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- العمود الأيمن - المعاينة والأزرار -->
                <div class="col-lg-4">
                    <!-- معاينة العملة -->
                    <div class="preview-card">
                        <div class="preview-title">
                            <i class="fas fa-eye"></i> معاينة العملة
                        </div>
                        <div class="preview-amount" id="currencyPreview">
                            {% if currency.position == 'before' %}
                                {{ currency.symbol }}1,234.56
                            {% else %}
                                1,234.56 {{ currency.symbol }}
                            {% endif %}
                        </div>
                        <div class="preview-details">
                            <div class="preview-detail-item">
                                <span>المنازل العشرية:</span>
                                <span class="preview-detail-value" id="previewDecimals">{{ currency.decimal_places }}</span>
                            </div>
                            <div class="preview-detail-item">
                                <span>فاصل الآلاف:</span>
                                <span class="preview-detail-value" id="previewThousands">{{ currency.thousands_separator or 'بدون' }}</span>
                            </div>
                            <div class="preview-detail-item">
                                <span>فاصل العشرية:</span>
                                <span class="preview-detail-value" id="previewDecimal">{{ currency.decimal_separator }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="action-section">
                        <div class="d-grid gap-2">
                            <button type="button" class="btn-premium btn-save" onclick="saveCurrency()">
                                <i class="fas fa-save"></i> حفظ التغييرات
                            </button>

                            <button type="button" class="btn-premium btn-reset" onclick="resetForm()">
                                <i class="fas fa-undo"></i> إعادة تعيين
                            </button>

                            <a href="{{ url_for('currencies.index') }}" class="btn-premium btn-back">
                                <i class="fas fa-arrow-right"></i> العودة للقائمة
                            </a>

                            {% if not currency.is_base_currency %}
                            <button type="button" class="btn-premium btn-delete" onclick="deleteCurrency()">
                                <i class="fas fa-trash"></i> حذف العملة
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // تحديث المعاينة
        function updatePreview() {
            try {
                const previewElement = document.getElementById('currencyPreview');
                if (!previewElement) return;

                let symbol = '{{ currency.symbol }}';
                let position = '{{ currency.position }}';
                let decimalPlaces = {{ currency.decimal_places }};
                let thousandsSeparator = '{{ currency.thousands_separator }}';
                let decimalSeparator = '{{ currency.decimal_separator }}';

                const symbolInput = document.querySelector('input[name="symbol"]');
                const positionSelect = document.querySelector('select[name="position"]');
                const decimalPlacesSelect = document.querySelector('select[name="decimal_places"]');
                const thousandsSeparatorSelect = document.querySelector('select[name="thousands_separator"]');
                const decimalSeparatorSelect = document.querySelector('select[name="decimal_separator"]');

                if (symbolInput && symbolInput.value) symbol = symbolInput.value;
                if (positionSelect && positionSelect.value) position = positionSelect.value;
                if (decimalPlacesSelect && decimalPlacesSelect.value) decimalPlaces = parseInt(decimalPlacesSelect.value);
                if (thousandsSeparatorSelect && thousandsSeparatorSelect.value !== undefined) thousandsSeparator = thousandsSeparatorSelect.value;
                if (decimalSeparatorSelect && decimalSeparatorSelect.value) decimalSeparator = decimalSeparatorSelect.value;

                let amount = 1234.56;
                let formattedAmount = decimalPlaces === 0 ? Math.round(amount).toString() : amount.toFixed(decimalPlaces);

                if (thousandsSeparator) {
                    const parts = formattedAmount.split('.');
                    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator);
                    formattedAmount = parts.join(decimalSeparator);
                } else if (decimalSeparator !== '.') {
                    formattedAmount = formattedAmount.replace('.', decimalSeparator);
                }

                previewElement.style.opacity = '0.7';
                setTimeout(() => {
                    previewElement.innerHTML = position === 'before' ? `${symbol} ${formattedAmount}` : `${formattedAmount} ${symbol}`;
                    previewElement.style.opacity = '1';
                }, 150);

                const previewDecimals = document.getElementById('previewDecimals');
                const previewThousands = document.getElementById('previewThousands');
                const previewDecimal = document.getElementById('previewDecimal');

                if (previewDecimals) previewDecimals.textContent = decimalPlaces;
                if (previewThousands) previewThousands.textContent = thousandsSeparator || 'بدون';
                if (previewDecimal) previewDecimal.textContent = decimalSeparator;

            } catch (error) {
                console.error('خطأ في تحديث المعاينة:', error);
            }
        }

        // حفظ العملة
        function saveCurrency() {
            const form = document.getElementById('editCurrencyForm');
            if (!form) {
                showAlert('لم يتم العثور على النموذج', 'error');
                return;
            }

            const formData = new FormData(form);
            const isActiveCheckbox = document.querySelector('input[name="is_active"]');
            const isBaseCurrencyCheckbox = document.querySelector('input[name="is_base_currency"]');

            const data = {
                name_ar: formData.get('name_ar'),
                name_en: formData.get('name_en'),
                symbol: formData.get('symbol'),
                decimal_places: formData.get('decimal_places'),
                position: formData.get('position'),
                thousands_separator: formData.get('thousands_separator'),
                decimal_separator: formData.get('decimal_separator'),
                is_active: isActiveCheckbox ? (isActiveCheckbox.checked ? 1 : 0) : 1,
                is_base_currency: isBaseCurrencyCheckbox ? (isBaseCurrencyCheckbox.checked ? 1 : 0) : 0,
                exchange_rate: {{ currency.exchange_rate }}
            };

            const saveBtn = document.querySelector('.btn-save');
            if (!saveBtn) return;

            const originalContent = saveBtn.innerHTML;
            saveBtn.innerHTML = '<div class="loading-spinner"></div> جاري الحفظ...';
            saveBtn.disabled = true;

            fetch('{{ url_for("currencies.edit_currency", currency_id=currency.id) }}', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    saveBtn.innerHTML = '<i class="fas fa-check"></i> تم الحفظ!';
                    showAlert('تم حفظ التغييرات بنجاح! 🎉', 'success');
                    setTimeout(() => window.location.href = '{{ url_for("currencies.index") }}', 2000);
                } else {
                    showAlert(data.message || 'حدث خطأ غير معروف', 'error');
                    saveBtn.innerHTML = originalContent;
                    saveBtn.disabled = false;
                }
            })
            .catch(error => {
                showAlert('حدث خطأ في الاتصال', 'error');
                saveBtn.innerHTML = originalContent;
                saveBtn.disabled = false;
            });
        }

        // إعادة تعيين النموذج
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟')) {
                location.reload();
            }
        }

        // حذف العملة
        function deleteCurrency() {
            if (confirm('هل أنت متأكد من حذف هذه العملة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                fetch(`/currencies/delete/{{ currency.id }}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('تم حذف العملة بنجاح!', 'success');
                        setTimeout(() => window.location.href = '{{ url_for("currencies.index") }}', 1500);
                    } else {
                        showAlert(data.message, 'error');
                    }
                })
                .catch(() => showAlert('حدث خطأ في الاتصال', 'error'));
            }
        }

        // عرض الرسائل
        function showAlert(message, type = 'info') {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show"
                     style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px; border-radius: 10px;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', alertHtml);
            setTimeout(() => {
                const alert = document.querySelector('.alert');
                if (alert) alert.remove();
            }, 3000);
        }

        // تحديث المعاينة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', updatePreview);
    </script>
</body>
</html>
