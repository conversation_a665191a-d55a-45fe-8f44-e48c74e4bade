-- إن<PERSON><PERSON><PERSON> جدول وثائق طلبات الحوالات لـ Oracle
-- Transfer Request Documents Table for Oracle

-- إنشاء sequence للـ ID
CREATE SEQUENCE transfer_docs_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- حذ<PERSON> الجدول إذا كان موجوداً
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE transfer_request_documents CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -942 THEN
            RAISE;
        END IF;
END;
/

-- إنشاء الجدول
CREATE TABLE transfer_request_documents (
    id NUMBER DEFAULT transfer_docs_seq.NEXTVAL PRIMARY KEY,
    transfer_request_id NUMBER NOT NULL,
    document_type VARCHAR2(100) NOT NULL,
    document_name VARCHAR2(255),
    file_name VARCHAR2(255) NOT NULL,
    file_path VARCHAR2(500) NOT NULL,
    file_size NUMBER,
    mime_type VARCHAR2(100),
    uploaded_by VARCHAR2(100),
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes CLOB,

    -- مفتاح خارجي
    CONSTRAINT fk_transfer_docs_request
        FOREIGN KEY (transfer_request_id)
        REFERENCES TRANSFER_REQUESTS(id) ON DELETE CASCADE
);

-- إنشاء فهارس للأداء
CREATE INDEX idx_transfer_docs_request_id ON transfer_request_documents(transfer_request_id);
CREATE INDEX idx_transfer_docs_type ON transfer_request_documents(document_type);
CREATE INDEX idx_transfer_docs_uploaded_at ON transfer_request_documents(uploaded_at);

-- إدراج بيانات تجريبية (اختيارية)
-- INSERT INTO transfer_request_documents (
--     transfer_request_id, document_type, document_name, file_name,
--     file_path, file_size, mime_type, uploaded_by, notes
-- ) VALUES (
--     1, 'identity_document', 'هوية المرسل', 'id_card.pdf',
--     '/uploads/transfer_documents/abc123.pdf', 1024000, 'application/pdf',
--     'admin', 'نسخة من هوية المرسل'
-- );

COMMIT;
