-- إن<PERSON><PERSON><PERSON> جدول حالات العقود
-- تاريخ الإنشاء: 2025-01-02
-- الوصف: جدول منفصل لإدارة حالات العقود بشكل مرن

-- <PERSON><PERSON><PERSON><PERSON><PERSON> الجدول
CREATE TABLE CONTRACT_STATUSES (
    STATUS_ID NUMBER PRIMARY KEY,
    STATUS_CODE VARCHAR2(50) NOT NULL UNIQUE,
    STATUS_NAME_AR VARCHAR2(100) NOT NULL,
    STATUS_NAME_EN VARCHAR2(100) NOT NULL,
    STATUS_DESCRIPTION VARCHAR2(500),
    STATUS_COLOR VARCHAR2(20) DEFAULT '#6c757d',
    STATUS_ICON VARCHAR2(50) DEFAULT 'fas fa-circle',
    IS_ACTIVE NUMBER(1) DEFAULT 1 NOT NULL,
    SORT_ORDER NUMBER DEFAULT 0,
    CREATED_AT DATE DEFAULT SYSDATE,
    UPDATED_AT DATE DEFAULT SYSDATE
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> تسلسل للمعرف
CREATE SEQUENCE SEQ_CONTRACT_STATUSES
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- إنشاء مشغل للمعرف التلقائي
CREATE OR REPLACE TRIGGER TRG_CONTRACT_STATUSES_ID
    BEFORE INSERT ON CONTRACT_STATUSES
    FOR EACH ROW
BEGIN
    IF :NEW.STATUS_ID IS NULL THEN
        :NEW.STATUS_ID := SEQ_CONTRACT_STATUSES.NEXTVAL;
    END IF;
END;
/

-- إنشاء مشغل لتحديث تاريخ التعديل
CREATE OR REPLACE TRIGGER TRG_CONTRACT_STATUSES_UPDATE
    BEFORE UPDATE ON CONTRACT_STATUSES
    FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- إدراج الحالات الأساسية
INSERT INTO CONTRACT_STATUSES (STATUS_CODE, STATUS_NAME_AR, STATUS_NAME_EN, STATUS_DESCRIPTION, STATUS_COLOR, STATUS_ICON, SORT_ORDER) VALUES
('DRAFT', 'مسودة', 'Draft', 'العقد في مرحلة الإعداد ولم يتم اعتماده بعد', '#ffc107', 'fas fa-edit', 1);

INSERT INTO CONTRACT_STATUSES (STATUS_CODE, STATUS_NAME_AR, STATUS_NAME_EN, STATUS_DESCRIPTION, STATUS_COLOR, STATUS_ICON, SORT_ORDER) VALUES
('APPROVED', 'معتمد', 'Approved', 'العقد معتمد وجاهز للتنفيذ', '#28a745', 'fas fa-check-circle', 2);

INSERT INTO CONTRACT_STATUSES (STATUS_CODE, STATUS_NAME_AR, STATUS_NAME_EN, STATUS_DESCRIPTION, STATUS_COLOR, STATUS_ICON, SORT_ORDER) VALUES
('ACTIVE', 'نشط', 'Active', 'العقد نشط وقيد التنفيذ', '#007bff', 'fas fa-play-circle', 3);

INSERT INTO CONTRACT_STATUSES (STATUS_CODE, STATUS_NAME_AR, STATUS_NAME_EN, STATUS_DESCRIPTION, STATUS_COLOR, STATUS_ICON, SORT_ORDER) VALUES
('PARTIALLY_EXECUTED', 'منفذ جزئياً', 'Partially Executed', 'تم تنفيذ جزء من العقد', '#17a2b8', 'fas fa-tasks', 4);

INSERT INTO CONTRACT_STATUSES (STATUS_CODE, STATUS_NAME_AR, STATUS_NAME_EN, STATUS_DESCRIPTION, STATUS_COLOR, STATUS_ICON, SORT_ORDER) VALUES
('FULLY_EXECUTED', 'منفذ كلياً', 'Fully Executed', 'تم تنفيذ العقد بالكامل', '#28a745', 'fas fa-check-double', 5);

INSERT INTO CONTRACT_STATUSES (STATUS_CODE, STATUS_NAME_AR, STATUS_NAME_EN, STATUS_DESCRIPTION, STATUS_COLOR, STATUS_ICON, SORT_ORDER) VALUES
('SUSPENDED', 'معلق', 'Suspended', 'العقد معلق مؤقتاً', '#fd7e14', 'fas fa-pause-circle', 6);

INSERT INTO CONTRACT_STATUSES (STATUS_CODE, STATUS_NAME_AR, STATUS_NAME_EN, STATUS_DESCRIPTION, STATUS_COLOR, STATUS_ICON, SORT_ORDER) VALUES
('EXPIRED', 'منتهي الصلاحية', 'Expired', 'انتهت صلاحية العقد', '#dc3545', 'fas fa-calendar-times', 7);

INSERT INTO CONTRACT_STATUSES (STATUS_CODE, STATUS_NAME_AR, STATUS_NAME_EN, STATUS_DESCRIPTION, STATUS_COLOR, STATUS_ICON, SORT_ORDER) VALUES
('CANCELLED', 'ملغي', 'Cancelled', 'تم إلغاء العقد', '#6c757d', 'fas fa-ban', 8);

INSERT INTO CONTRACT_STATUSES (STATUS_CODE, STATUS_NAME_AR, STATUS_NAME_EN, STATUS_DESCRIPTION, STATUS_COLOR, STATUS_ICON, SORT_ORDER) VALUES
('REJECTED', 'مرفوض', 'Rejected', 'تم رفض العقد', '#dc3545', 'fas fa-times-circle', 9);

INSERT INTO CONTRACT_STATUSES (STATUS_CODE, STATUS_NAME_AR, STATUS_NAME_EN, STATUS_DESCRIPTION, STATUS_COLOR, STATUS_ICON, SORT_ORDER) VALUES
('UNDER_REVIEW', 'قيد المراجعة', 'Under Review', 'العقد قيد المراجعة والموافقة', '#ffc107', 'fas fa-search', 10);

-- إنشاء الفهارس
CREATE INDEX IDX_CONTRACT_STATUSES_CODE ON CONTRACT_STATUSES(STATUS_CODE);
CREATE INDEX IDX_CONTRACT_STATUSES_ACTIVE ON CONTRACT_STATUSES(IS_ACTIVE);
CREATE INDEX IDX_CONTRACT_STATUSES_SORT ON CONTRACT_STATUSES(SORT_ORDER);

-- إضافة قيود التحقق
ALTER TABLE CONTRACT_STATUSES ADD CONSTRAINT CHK_CONTRACT_STATUSES_ACTIVE 
    CHECK (IS_ACTIVE IN (0, 1));

-- إضافة التعليقات
COMMENT ON TABLE CONTRACT_STATUSES IS 'جدول حالات العقود - يحتوي على جميع الحالات المتاحة للعقود';
COMMENT ON COLUMN CONTRACT_STATUSES.STATUS_ID IS 'معرف الحالة (مفتاح أساسي)';
COMMENT ON COLUMN CONTRACT_STATUSES.STATUS_CODE IS 'رمز الحالة (فريد)';
COMMENT ON COLUMN CONTRACT_STATUSES.STATUS_NAME_AR IS 'اسم الحالة بالعربية';
COMMENT ON COLUMN CONTRACT_STATUSES.STATUS_NAME_EN IS 'اسم الحالة بالإنجليزية';
COMMENT ON COLUMN CONTRACT_STATUSES.STATUS_DESCRIPTION IS 'وصف الحالة';
COMMENT ON COLUMN CONTRACT_STATUSES.STATUS_COLOR IS 'لون الحالة (Bootstrap color class)';
COMMENT ON COLUMN CONTRACT_STATUSES.STATUS_ICON IS 'أيقونة الحالة (Font Awesome class)';
COMMENT ON COLUMN CONTRACT_STATUSES.IS_ACTIVE IS 'هل الحالة نشطة (1=نعم، 0=لا)';
COMMENT ON COLUMN CONTRACT_STATUSES.SORT_ORDER IS 'ترتيب العرض';

-- عرض النتائج
SELECT 
    STATUS_ID,
    STATUS_CODE,
    STATUS_NAME_AR,
    STATUS_NAME_EN,
    STATUS_COLOR,
    STATUS_ICON,
    SORT_ORDER
FROM CONTRACT_STATUSES
ORDER BY SORT_ORDER;

COMMIT;

-- إنشاء view لسهولة الاستعلام
CREATE OR REPLACE VIEW V_CONTRACT_STATUSES AS
SELECT 
    STATUS_ID,
    STATUS_CODE,
    STATUS_NAME_AR,
    STATUS_NAME_EN,
    STATUS_DESCRIPTION,
    STATUS_COLOR,
    STATUS_ICON,
    IS_ACTIVE,
    SORT_ORDER
FROM CONTRACT_STATUSES
WHERE IS_ACTIVE = 1
ORDER BY SORT_ORDER;

-- منح الصلاحيات
GRANT SELECT ON V_CONTRACT_STATUSES TO PUBLIC;
