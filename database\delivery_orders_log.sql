-- ===================================================================
-- إنشاء جدول سجل أوامر التسليم
-- Delivery Orders Log Table Creation
-- ===================================================================

-- 1. إنشاء sequence لمعرفات السجل
CREATE SEQUENCE delivery_orders_log_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- 2. إن<PERSON><PERSON>ء جدول سجل أوامر التسليم
CREATE TABLE delivery_orders_log (
    id NUMBER PRIMARY KEY,
    order_id NUMBER,
    order_number VARCHAR2(50),
    action VARCHAR2(50) NOT NULL, -- CREATED, UPDATED, DELETED, SENT, CANCELLED, COMPLETED
    action_by NUMBER,
    action_date DATE DEFAULT SYSDATE,
    old_status VARCHAR2(50),
    new_status VARCHAR2(50),
    notes CLOB,
    ip_address VARCHAR2(45),
    user_agent VARCHAR2(500),
    
    -- معلومات إضافية
    created_at DATE DEFAULT SYSDATE
);

-- 3. إنشاء فهارس للأداء
CREATE INDEX idx_delivery_orders_log_order_id ON delivery_orders_log(order_id);
CREATE INDEX idx_delivery_orders_log_action ON delivery_orders_log(action);
CREATE INDEX idx_delivery_orders_log_date ON delivery_orders_log(action_date);
CREATE INDEX idx_delivery_orders_log_user ON delivery_orders_log(action_by);

-- 4. إضافة تعليقات
COMMENT ON TABLE delivery_orders_log IS 'سجل جميع العمليات على أوامر التسليم';
COMMENT ON COLUMN delivery_orders_log.id IS 'معرف السجل';
COMMENT ON COLUMN delivery_orders_log.order_id IS 'معرف أمر التسليم';
COMMENT ON COLUMN delivery_orders_log.order_number IS 'رقم أمر التسليم';
COMMENT ON COLUMN delivery_orders_log.action IS 'نوع العملية';
COMMENT ON COLUMN delivery_orders_log.action_by IS 'معرف المستخدم الذي قام بالعملية';
COMMENT ON COLUMN delivery_orders_log.action_date IS 'تاريخ ووقت العملية';
COMMENT ON COLUMN delivery_orders_log.old_status IS 'الحالة السابقة';
COMMENT ON COLUMN delivery_orders_log.new_status IS 'الحالة الجديدة';
COMMENT ON COLUMN delivery_orders_log.notes IS 'ملاحظات إضافية';

-- 5. إنشاء trigger لتسجيل التغييرات تلقائياً (اختياري)
CREATE OR REPLACE TRIGGER trg_delivery_orders_audit
    AFTER INSERT OR UPDATE OR DELETE ON delivery_orders
    FOR EACH ROW
DECLARE
    v_action VARCHAR2(50);
    v_user_id NUMBER := 1; -- يمكن تحسينه لاحقاً
BEGIN
    -- تحديد نوع العملية
    IF INSERTING THEN
        v_action := 'CREATED';
    ELSIF UPDATING THEN
        v_action := 'UPDATED';
    ELSIF DELETING THEN
        v_action := 'DELETED';
    END IF;
    
    -- إدراج سجل في جدول السجل
    IF INSERTING OR UPDATING THEN
        INSERT INTO delivery_orders_log (
            id, order_id, order_number, action, action_by, 
            old_status, new_status, notes
        ) VALUES (
            delivery_orders_log_seq.NEXTVAL,
            :NEW.id,
            :NEW.order_number,
            v_action,
            v_user_id,
            :OLD.order_status,
            :NEW.order_status,
            CASE 
                WHEN INSERTING THEN 'تم إنشاء أمر التسليم'
                WHEN UPDATING THEN 'تم تحديث أمر التسليم'
            END
        );
    ELSIF DELETING THEN
        INSERT INTO delivery_orders_log (
            id, order_id, order_number, action, action_by, 
            old_status, notes
        ) VALUES (
            delivery_orders_log_seq.NEXTVAL,
            :OLD.id,
            :OLD.order_number,
            v_action,
            v_user_id,
            :OLD.order_status,
            'تم حذف أمر التسليم'
        );
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        -- تجاهل أخطاء السجل لعدم إيقاف العملية الأساسية
        NULL;
END;
/

-- 6. منح الصلاحيات (حسب الحاجة)
-- GRANT SELECT, INSERT ON delivery_orders_log TO app_user;

-- 7. إنشاء view لعرض السجل مع أسماء المستخدمين
CREATE OR REPLACE VIEW v_delivery_orders_log AS
SELECT 
    dol.id,
    dol.order_id,
    dol.order_number,
    dol.action,
    dol.action_by,
    u.username as action_by_name,
    dol.action_date,
    dol.old_status,
    dol.new_status,
    dol.notes,
    dol.created_at
FROM delivery_orders_log dol
LEFT JOIN users u ON dol.action_by = u.id
ORDER BY dol.action_date DESC;

COMMENT ON VIEW v_delivery_orders_log IS 'عرض سجل أوامر التسليم مع أسماء المستخدمين';

-- ===================================================================
-- انتهاء ملف إنشاء جدول سجل أوامر التسليم
-- ===================================================================
