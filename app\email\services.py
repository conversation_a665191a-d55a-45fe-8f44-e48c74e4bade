# -*- coding: utf-8 -*-
"""
خدمات البريد الإلكتروني
Email Services
"""

import smtplib
import imaplib
import poplib
import email
import json
import asyncio
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from email.header import decode_header
from typing import List, Dict, Optional, Tuple
import ssl
import socket
from cryptography.fernet import Fernet
from flask import current_app
import logging

# إعداد السجلات
logger = logging.getLogger(__name__)

class EmailEncryption:
    """خدمة تشفير كلمات المرور"""

    def __init__(self):
        # مفتاح التشفير (يجب تخزينه بشكل آمن في الإنتاج)
        try:
            self.key = current_app.config.get('EMAIL_ENCRYPTION_KEY', None)
        except RuntimeError:
            # إذا كنا خارج سياق التطبيق، استخدم مفتاح افتراضي
            self.key = None

        if not self.key:
            # إنشاء مفتاح جديد إذا لم يكن موجود
            self.key = Fernet.generate_key()

        self.cipher = Fernet(self.key)
    
    def encrypt_password(self, password: str) -> bytes:
        """تشفير كلمة المرور"""
        return self.cipher.encrypt(password.encode())
    
    def decrypt_password(self, encrypted_password: bytes) -> str:
        """فك تشفير كلمة المرور"""
        return self.cipher.decrypt(encrypted_password).decode()

class SMTPService:
    """خدمة إرسال البريد الإلكتروني"""
    
    def __init__(self, account_config: Dict):
        self.smtp_server = account_config['smtp_server']
        self.smtp_port = account_config['smtp_port']
        self.use_tls = account_config.get('smtp_use_tls', True)
        self.use_ssl = account_config.get('smtp_use_ssl', False)
        self.email = account_config['email_address']
        self.password = account_config['password']
        self.display_name = account_config.get('display_name', self.email)
    
    def connect(self) -> smtplib.SMTP:
        """الاتصال بخادم SMTP"""
        try:
            if self.use_ssl:
                server = smtplib.SMTP_SSL(self.smtp_server, self.smtp_port)
            else:
                server = smtplib.SMTP(self.smtp_server, self.smtp_port)
                if self.use_tls:
                    server.starttls()
            
            server.login(self.email, self.password)
            logger.info(f"✅ تم الاتصال بخادم SMTP: {self.smtp_server}")
            return server
            
        except Exception as e:
            logger.error(f"❌ خطأ في الاتصال بخادم SMTP: {e}")
            raise
    
    def send_email(self, to_emails: List[str], subject: str, body: str, 
                   cc_emails: List[str] = None, bcc_emails: List[str] = None,
                   attachments: List[Dict] = None, is_html: bool = True) -> bool:
        """إرسال رسالة بريد إلكتروني"""
        try:
            # إنشاء الرسالة
            msg = MIMEMultipart('alternative')
            msg['From'] = f"{self.display_name} <{self.email}>"
            msg['To'] = ', '.join(to_emails)
            msg['Subject'] = subject
            
            if cc_emails:
                msg['Cc'] = ', '.join(cc_emails)
            
            # إضافة المحتوى
            if is_html:
                msg.attach(MIMEText(body, 'html', 'utf-8'))
            else:
                msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # إضافة المرفقات
            if attachments:
                for attachment in attachments:
                    self._add_attachment(msg, attachment)
            
            # إرسال الرسالة
            server = self.connect()
            
            all_recipients = to_emails[:]
            if cc_emails:
                all_recipients.extend(cc_emails)
            if bcc_emails:
                all_recipients.extend(bcc_emails)
            
            server.send_message(msg, to_addrs=all_recipients)
            server.quit()
            
            logger.info(f"✅ تم إرسال الرسالة إلى {len(all_recipients)} مستقبل")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إرسال الرسالة: {e}")
            return False
    
    def _add_attachment(self, msg: MIMEMultipart, attachment: Dict):
        """إضافة مرفق للرسالة"""
        try:
            with open(attachment['file_path'], 'rb') as f:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(f.read())
                encoders.encode_base64(part)
                part.add_header(
                    'Content-Disposition',
                    f'attachment; filename= {attachment["filename"]}'
                )
                msg.attach(part)
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة المرفق {attachment['filename']}: {e}")

class IMAPService:
    """خدمة استقبال البريد الإلكتروني"""
    
    def __init__(self, account_config: Dict):
        self.imap_server = account_config['imap_server']
        self.imap_port = account_config['imap_port']
        self.use_ssl = account_config.get('imap_use_ssl', True)
        self.email = account_config['email_address']
        self.password = account_config['password']
    
    def connect(self) -> imaplib.IMAP4:
        """الاتصال بخادم IMAP"""
        try:
            if self.use_ssl:
                server = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            else:
                server = imaplib.IMAP4(self.imap_server, self.imap_port)
            
            server.login(self.email, self.password)
            logger.info(f"✅ تم الاتصال بخادم IMAP: {self.imap_server}")
            return server
            
        except Exception as e:
            logger.error(f"❌ خطأ في الاتصال بخادم IMAP: {e}")
            raise
    
    def get_folders(self) -> List[Dict]:
        """جلب قائمة المجلدات"""
        try:
            server = self.connect()
            status, folders = server.list()
            
            folder_list = []
            for folder in folders:
                folder_info = folder.decode().split('"')
                if len(folder_info) >= 3:
                    folder_name = folder_info[-2]
                    folder_list.append({
                        'name': folder_name,
                        'name_arabic': self._translate_folder_name(folder_name)
                    })
            
            server.logout()
            return folder_list
            
        except Exception as e:
            logger.error(f"❌ خطأ في جلب المجلدات: {e}")
            return []
    
    def get_emails(self, folder: str = 'INBOX', limit: int = 50, 
                   unread_only: bool = False) -> List[Dict]:
        """جلب الرسائل من مجلد معين"""
        try:
            server = self.connect()
            server.select(folder)
            
            # البحث عن الرسائل
            search_criteria = 'UNSEEN' if unread_only else 'ALL'
            status, messages = server.search(None, search_criteria)
            
            email_ids = messages[0].split()
            email_list = []
            
            # جلب آخر الرسائل
            for email_id in email_ids[-limit:]:
                email_data = self._fetch_email(server, email_id)
                if email_data:
                    email_list.append(email_data)
            
            server.logout()
            return email_list[::-1]  # ترتيب عكسي (الأحدث أولاً)
            
        except Exception as e:
            logger.error(f"❌ خطأ في جلب الرسائل: {e}")
            return []
    
    def _fetch_email(self, server: imaplib.IMAP4, email_id: bytes) -> Optional[Dict]:
        """جلب رسالة واحدة"""
        try:
            status, msg_data = server.fetch(email_id, '(RFC822)')
            email_body = msg_data[0][1]
            email_message = email.message_from_bytes(email_body)
            
            # استخراج معلومات الرسالة
            subject = self._decode_header(email_message['Subject'])
            sender = self._decode_header(email_message['From'])
            date_str = email_message['Date']
            
            # تحويل التاريخ
            try:
                date_obj = email.utils.parsedate_to_datetime(date_str)
            except:
                date_obj = datetime.now()
            
            # استخراج المحتوى
            body_text, body_html = self._extract_body(email_message)
            
            return {
                'id': email_id.decode(),
                'message_id': email_message['Message-ID'],
                'subject': subject,
                'sender': sender,
                'date': date_obj,
                'body_text': body_text,
                'body_html': body_html,
                'has_attachments': self._has_attachments(email_message)
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في جلب الرسالة {email_id}: {e}")
            return None
    
    def _decode_header(self, header: str) -> str:
        """فك تشفير عنوان الرسالة"""
        if not header:
            return ""
        
        decoded_parts = decode_header(header)
        decoded_string = ""
        
        for part, encoding in decoded_parts:
            if isinstance(part, bytes):
                if encoding:
                    decoded_string += part.decode(encoding)
                else:
                    decoded_string += part.decode('utf-8', errors='ignore')
            else:
                decoded_string += part
        
        return decoded_string
    
    def _extract_body(self, email_message) -> Tuple[str, str]:
        """استخراج محتوى الرسالة"""
        body_text = ""
        body_html = ""
        
        if email_message.is_multipart():
            for part in email_message.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))
                
                if "attachment" not in content_disposition:
                    if content_type == "text/plain":
                        body_text = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    elif content_type == "text/html":
                        body_html = part.get_payload(decode=True).decode('utf-8', errors='ignore')
        else:
            content_type = email_message.get_content_type()
            if content_type == "text/plain":
                body_text = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
            elif content_type == "text/html":
                body_html = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
        
        return body_text, body_html
    
    def _has_attachments(self, email_message) -> bool:
        """فحص وجود مرفقات"""
        if email_message.is_multipart():
            for part in email_message.walk():
                content_disposition = str(part.get("Content-Disposition"))
                if "attachment" in content_disposition:
                    return True
        return False
    
    def _translate_folder_name(self, folder_name: str) -> str:
        """ترجمة أسماء المجلدات"""
        translations = {
            'INBOX': 'صندوق الوارد',
            'Sent': 'المرسل',
            'Drafts': 'المسودات',
            'Trash': 'المحذوفات',
            'Spam': 'الرسائل المزعجة',
            'Junk': 'الرسائل المزعجة'
        }
        return translations.get(folder_name, folder_name)

class EmailManager:
    """مدير البريد الإلكتروني الرئيسي"""
    
    def __init__(self):
        self.encryption = EmailEncryption()
    
    def create_account(self, user_id: int, account_data: Dict) -> bool:
        """إنشاء حساب بريد إلكتروني جديد"""
        try:
            from app.email.models import EmailAccount, EmailFolder
            from app import db
            
            # تشفير كلمة المرور
            encrypted_password = self.encryption.encrypt_password(account_data['password'])
            
            # إنشاء الحساب
            account = EmailAccount(
                user_id=user_id,
                email_address=account_data['email_address'],
                display_name=account_data['display_name'],
                smtp_server=account_data['smtp_server'],
                smtp_port=account_data['smtp_port'],
                imap_server=account_data['imap_server'],
                imap_port=account_data['imap_port'],
                password_encrypted=encrypted_password
            )
            
            db.session.add(account)
            db.session.flush()  # للحصول على ID
            
            # إنشاء المجلدات الافتراضية
            default_folders = [
                {'name': 'INBOX', 'name_arabic': 'صندوق الوارد', 'type': 'inbox'},
                {'name': 'Sent', 'name_arabic': 'المرسل', 'type': 'sent'},
                {'name': 'Drafts', 'name_arabic': 'المسودات', 'type': 'drafts'},
                {'name': 'Trash', 'name_arabic': 'المحذوفات', 'type': 'trash'}
            ]
            
            for folder_data in default_folders:
                folder = EmailFolder(
                    account_id=account.id,
                    name=folder_data['name'],
                    name_arabic=folder_data['name_arabic'],
                    folder_type=folder_data['type'],
                    is_system=True
                )
                db.session.add(folder)
            
            db.session.commit()
            logger.info(f"✅ تم إنشاء حساب البريد: {account_data['email_address']}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء حساب البريد: {e}")
            db.session.rollback()
            return False
    
    def sync_account(self, account_id: int) -> bool:
        """مزامنة حساب البريد الإلكتروني"""
        try:
            from app.email.models import EmailAccount
            
            account = EmailAccount.query.get(account_id)
            if not account:
                return False
            
            # فك تشفير كلمة المرور
            password = self.encryption.decrypt_password(account.password_encrypted)
            
            # إعداد خدمة IMAP
            imap_config = {
                'imap_server': account.imap_server,
                'imap_port': account.imap_port,
                'imap_use_ssl': account.imap_use_ssl,
                'email_address': account.email_address,
                'password': password
            }
            
            imap_service = IMAPService(imap_config)
            
            # جلب الرسائل الجديدة
            emails = imap_service.get_emails(limit=100, unread_only=True)
            
            # حفظ الرسائل في قاعدة البيانات
            self._save_emails(account_id, emails)
            
            # تحديث وقت آخر مزامنة
            account.last_sync = datetime.utcnow()
            db.session.commit()
            
            logger.info(f"✅ تم مزامنة {len(emails)} رسالة للحساب {account.email_address}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في مزامنة الحساب: {e}")
            return False
    
    def _save_emails(self, account_id: int, emails: List[Dict]):
        """حفظ الرسائل في قاعدة البيانات"""
        try:
            from app.email.models import EmailMessage, EmailFolder
            from app import db
            
            # جلب مجلد صندوق الوارد
            inbox_folder = EmailFolder.query.filter_by(
                account_id=account_id, 
                folder_type='inbox'
            ).first()
            
            if not inbox_folder:
                return
            
            for email_data in emails:
                # فحص إذا كانت الرسالة موجودة مسبقاً
                existing = EmailMessage.query.filter_by(
                    message_id=email_data['message_id']
                ).first()
                
                if existing:
                    continue
                
                # إنشاء رسالة جديدة
                message = EmailMessage(
                    account_id=account_id,
                    folder_id=inbox_folder.id,
                    message_id=email_data['message_id'],
                    subject=email_data['subject'],
                    sender_email=email_data['sender'],
                    body_text=email_data['body_text'],
                    body_html=email_data['body_html'],
                    has_attachments=email_data['has_attachments'],
                    received_at=email_data['date']
                )
                
                db.session.add(message)
            
            db.session.commit()
            
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الرسائل: {e}")
            db.session.rollback()

# متغير لحفظ مثيل مدير البريد الإلكتروني
email_manager = None

def get_email_manager():
    """الحصول على مثيل مدير البريد الإلكتروني"""
    global email_manager
    if email_manager is None:
        email_manager = EmailManager()
    return email_manager
