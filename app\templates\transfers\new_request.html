{% extends "base.html" %}

{% block title %}طلب حوالة جديدة{% endblock %}

{% block extra_css %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<style>
    .section-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px 8px 0 0;
        margin-bottom: 0;
    }

    .section-body {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-top: none;
        border-radius: 0 0 8px 8px;
        padding: 25px;
    }

    .form-section {
        margin-bottom: 30px;
    }

    .sender-info {
        background: #e8f5e8;
        border: 1px solid #c3e6c3;
        border-radius: 8px;
        padding: 20px;
    }

    .readonly-field {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        color: #6c757d;
    }

    .beneficiary-section {
        border: 2px solid #007bff;
        border-radius: 8px;
        padding: 20px;
        background: #f0f8ff;
    }

    .required-field::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }



    .alert-info {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        border: none;
        color: white;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .select2-container--default .select2-selection--single:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .select2-container {
        width: 100% !important;
    }

    .select2-container--bootstrap-5 .select2-selection {
        min-height: 38px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="fas fa-plus-circle text-primary me-2"></i>
                طلب حوالة جديدة
            </h1>
            <p class="text-muted mb-0">إنشاء طلب حوالة مالية جديدة شاملة ومتقدمة</p>
        </div>
    </div>

    <!-- Main Form -->
    <form id="transferRequestForm" novalidate>
        <!-- القسم الأول: تفاصيل الحوالة -->
        <div class="form-section">
            <div class="section-header">
                <h5 class="mb-0">
                    <i class="fas fa-exchange-alt me-2"></i>
                    تفاصيل الحوالة
                </h5>
            </div>
            <div class="section-body">
                <div class="row">
                    <!-- الفرع -->
                    <div class="col-md-6 mb-3">
                        <label for="branch_id" class="form-label required-field">الفرع</label>
                        <select class="form-select" id="branch_id" name="branch_id" required>
                            <option value="">اختر الفرع...</option>
                        </select>
                        <div class="invalid-feedback">يرجى اختيار الفرع</div>
                    </div>

                    <!-- نوع التحويل -->
                    <div class="col-md-6 mb-3">
                        <label for="transfer_type" class="form-label required-field">نوع التحويل</label>
                        <select class="form-select" id="transfer_type" name="transfer_type" required>
                            <option value="">اختر نوع التحويل...</option>
                            <option value="bank">بنك</option>
                            <option value="money_changer">صراف</option>
                        </select>
                        <div class="invalid-feedback">يرجى اختيار نوع التحويل</div>
                    </div>

                    <!-- الصراف/البنك -->
                    <div class="col-md-6 mb-3">
                        <label for="money_changer_bank_id" class="form-label required-field">الصراف/البنك</label>
                        <select class="form-select" id="money_changer_bank_id" name="money_changer_bank_id" required disabled>
                            <option value="">اختر نوع التحويل أولاً...</option>
                        </select>
                        <div class="invalid-feedback">يرجى اختيار الصراف أو البنك</div>
                    </div>

                    <!-- مبلغ الحوالة -->
                    <div class="col-md-6 mb-3">
                        <label for="amount" class="form-label required-field">مبلغ الحوالة</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="amount" name="amount"
                                   step="0.01" min="0" required>
                            <span class="input-group-text" id="currency-symbol">$</span>
                        </div>
                        <div class="invalid-feedback">يرجى إدخال مبلغ الحوالة</div>
                    </div>

                    <!-- العملة -->
                    <div class="col-md-6 mb-3">
                        <label for="currency_id" class="form-label required-field">العملة</label>
                        <select class="form-select" id="currency_id" name="currency_id" required>
                            <option value="">اختر العملة...</option>
                        </select>
                        <div class="invalid-feedback">يرجى اختيار العملة</div>
                    </div>

                    <!-- الغرض من التحويل -->
                    <div class="col-md-6 mb-3">
                        <label for="purpose" class="form-label required-field">الغرض من التحويل</label>
                        <input type="text" class="form-control" id="purpose" name="purpose"
                               placeholder="مثال: دفع مستحقات مورد" required>
                        <div class="invalid-feedback">يرجى إدخال الغرض من التحويل</div>
                    </div>
                </div>

                <!-- العمولة (للعرض فقط) -->
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="commission_display" class="form-label">العمولة المتوقعة</label>
                        <div class="input-group">
                            <input type="text" class="form-control readonly-field" id="commission_display"
                                   readonly placeholder="سيتم حسابها تلقائياً">
                            <span class="input-group-text" id="commission-currency">$</span>
                        </div>
                        <small class="text-muted">العمولة تحسب تلقائياً بناءً على الصراف/البنك المختار</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- القسم الثاني: تفاصيل المرسل -->
        <div class="form-section">
            <div class="section-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-tie me-2"></i>
                    تفاصيل المرسل
                </h5>
            </div>
            <div class="section-body sender-info">
                <div class="alert alert-info mb-4">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>معلومة:</strong> بيانات المرسل ثابتة ومحددة مسبقاً لشركة الفجيحي
                </div>

                <div class="row">
                    <!-- اسم المرسل -->
                    <div class="col-md-6 mb-3">
                        <label for="sender_name" class="form-label">اسم المرسل</label>
                        <input type="text" class="form-control readonly-field" id="sender_name"
                               value="ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD" readonly>
                    </div>

                    <!-- العنوان -->
                    <div class="col-md-6 mb-3">
                        <label for="sender_address" class="form-label">العنوان</label>
                        <input type="text" class="form-control readonly-field" id="sender_address"
                               value="TAIZ STREET, ORPHAN BUIDING, NEAR ALBRKA STORES – SANA'A, YEMEN" readonly>
                    </div>

                    <!-- الشخص المسئول -->
                    <div class="col-md-6 mb-3">
                        <label for="responsible_person" class="form-label">الشخص المسئول</label>
                        <input type="text" class="form-control readonly-field" id="responsible_person"
                               value="NASHA'AT RASHAD QASIM ALDUBAEE" readonly>
                    </div>

                    <!-- الهاتف -->
                    <div class="col-md-6 mb-3">
                        <label for="sender_phone" class="form-label">الهاتف</label>
                        <input type="text" class="form-control readonly-field" id="sender_phone"
                               value="+9671616109" readonly>
                    </div>

                    <!-- الموبايل -->
                    <div class="col-md-6 mb-3">
                        <label for="sender_mobile" class="form-label">الموبايل</label>
                        <input type="text" class="form-control readonly-field" id="sender_mobile"
                               value="+967777161609" readonly>
                    </div>

                    <!-- الفاكس -->
                    <div class="col-md-6 mb-3">
                        <label for="sender_fax" class="form-label">الفاكس</label>
                        <input type="text" class="form-control readonly-field" id="sender_fax"
                               value="+9671615909" readonly>
                    </div>

                    <!-- صندوق البريد -->
                    <div class="col-md-6 mb-3">
                        <label for="sender_po_box" class="form-label">صندوق البريد</label>
                        <input type="text" class="form-control readonly-field" id="sender_po_box"
                               value="1903" readonly>
                    </div>
                </div>
            </div>
        </div>

        <!-- القسم الثالث: بيانات المستفيدين -->
        <div class="form-section">
            <div class="section-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    بيانات المستفيد
                </h5>
            </div>
            <div class="section-body">
                <div class="beneficiary-section">
                    <!-- خيار البحث عن مستفيد موجود -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-search me-2"></i>
                                <strong>البحث الذكي:</strong> ابحث عن مستفيد موجود بكتابة أي معلومة عنه
                            </div>
                            <label class="form-label">البحث الذكي عن مستفيد موجود:</label>
                            <input type="text" class="form-control" id="smart_beneficiary_search_new"
                                   placeholder="ابحث بالاسم، البنك، رقم الحساب، IBAN، الهاتف..."
                                   autocomplete="off">
                            <small class="form-text text-muted">
                                <i class="fas fa-lightbulb me-1"></i>
                                يمكنك البحث بأي معلومة: الاسم، البنك، رقم الحساب، IBAN، الهاتف، أو العنوان
                            </small>
                        </div>
                    </div>

                    <!-- نموذج بيانات المستفيد -->
                    <div id="beneficiaryForm">
                        <div class="row">
                            <!-- اسم المستفيد -->
                            <div class="col-md-6 mb-3">
                                <label for="beneficiary_name" class="form-label required-field">اسم المستفيد</label>
                                <input type="text" class="form-control" id="beneficiary_name"
                                       name="beneficiary_name" required>
                                <div class="invalid-feedback">يرجى إدخال اسم المستفيد</div>
                            </div>

                            <!-- عنوان المستفيد -->
                            <div class="col-md-6 mb-3">
                                <label for="beneficiary_address" class="form-label">عنوان المستفيد</label>
                                <input type="text" class="form-control" id="beneficiary_address"
                                       name="beneficiary_address">
                            </div>

                            <!-- نوع المستفيد -->
                            <div class="col-md-6 mb-3">
                                <label for="beneficiary_type" class="form-label">نوع المستفيد</label>
                                <select class="form-select" id="beneficiary_type" name="beneficiary_type">
                                    <option value="individual">فرد</option>
                                    <option value="supplier">مورد</option>
                                    <option value="vendor">بائع</option>
                                </select>
                            </div>

                            <!-- رقم الحساب البنكي -->
                            <div class="col-md-6 mb-3">
                                <label for="bank_account" class="form-label required-field">رقم الحساب البنكي</label>
                                <input type="text" class="form-control" id="bank_account"
                                       name="bank_account" required>
                                <div class="invalid-feedback">يرجى إدخال رقم الحساب البنكي</div>
                            </div>

                            <!-- اسم البنك -->
                            <div class="col-md-6 mb-3">
                                <label for="bank_name" class="form-label required-field">اسم البنك</label>
                                <input type="text" class="form-control" id="bank_name"
                                       name="bank_name" required>
                                <div class="invalid-feedback">يرجى إدخال اسم البنك</div>
                            </div>

                            <!-- فرع البنك -->
                            <div class="col-md-6 mb-3">
                                <label for="bank_branch" class="form-label">فرع البنك</label>
                                <input type="text" class="form-control" id="bank_branch"
                                       name="bank_branch">
                            </div>

                            <!-- دولة البنك -->
                            <div class="col-md-6 mb-3">
                                <label for="bank_country" class="form-label">دولة البنك</label>
                                <input type="text" class="form-control" id="bank_country"
                                       name="bank_country">
                            </div>

                            <!-- رقم IBAN -->
                            <div class="col-md-6 mb-3">
                                <label for="iban" class="form-label">رقم IBAN</label>
                                <input type="text" class="form-control" id="iban" name="iban">
                            </div>

                            <!-- رمز SWIFT -->
                            <div class="col-md-6 mb-3">
                                <label for="swift_code" class="form-label">رمز SWIFT</label>
                                <input type="text" class="form-control" id="swift_code" name="swift_code">
                            </div>

                            <!-- رقم الهوية -->
                            <div class="col-md-6 mb-3">
                                <label for="identification_number" class="form-label">رقم الهوية</label>
                                <input type="text" class="form-control" id="identification_number"
                                       name="identification_number">
                            </div>

                            <!-- رقم الهاتف -->
                            <div class="col-md-6 mb-3">
                                <label for="beneficiary_phone" class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" id="beneficiary_phone"
                                       name="beneficiary_phone">
                            </div>

                            <!-- البريد الإلكتروني -->
                            <div class="col-md-6 mb-3">
                                <label for="beneficiary_email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="beneficiary_email"
                                       name="beneficiary_email">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ملاحظات إضافية -->
        <div class="form-section">
            <div class="section-header">
                <h5 class="mb-0">
                    <i class="fas fa-sticky-note me-2"></i>
                    ملاحظات إضافية
                </h5>
            </div>
            <div class="section-body">
                <div class="row">
                    <div class="col-12">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="4"
                                  placeholder="أي ملاحظات إضافية حول الحوالة..."></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم المسودات المحفوظة -->
        <div class="form-section" id="draftsSection" style="display: none;">
            <div class="section-header">
                <h5 class="mb-0">
                    <i class="fas fa-folder-open me-2"></i>
                    المسودات المحفوظة
                </h5>
            </div>
            <div class="section-body">
                <div class="row">
                    <div class="col-12">
                        <div id="draftsList" class="list-group">
                            <!-- سيتم تحميل المسودات هنا -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار الحفظ -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <button type="button" class="btn btn-info" onclick="toggleDrafts()">
                            <i class="fas fa-folder-open me-2"></i>عرض المسودات
                        </button>
                    </div>
                    <div class="d-flex gap-3">
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                            <i class="fas fa-undo me-2"></i>إعادة تعيين
                        </button>
                        <button type="button" class="btn btn-outline-primary" onclick="saveDraft()">
                            <i class="fas fa-save me-2"></i>حفظ كمسودة
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>إرسال طلب الحوالة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mb-0">جاري معالجة الطلب...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    // تهيئة البيانات
    loadBranches();
    loadCurrencies();
    loadBeneficiaries();

    // ربط الأحداث
    $('#transfer_type').change(function() {
        loadMoneyChangersBanks();
    });

    $('#money_changer_bank_id').change(function() {
        calculateCommission();
    });

    $('#amount, #currency_id').change(function() {
        calculateCommission();
    });

    // ربط حدث تغيير المستفيد
    $('#existing_beneficiary').on('change', function() {
        console.log('🔄 تم تغيير المستفيد المختار (change event)');
        console.log('📊 القيمة الجديدة:', $(this).val());
        loadBeneficiaryData();
    });

    // ربط إضافي لـ Select2
    $('#existing_beneficiary').on('select2:select', function(e) {
        console.log('🔄 تم اختيار مستفيد من Select2 (select2:select event)');
        console.log('📊 بيانات Select2:', e.params.data);
        setTimeout(function() {
            loadBeneficiaryData();
        }, 100); // تأخير قصير للتأكد من تحديث القيمة
    });

    // ربط إضافي للقائمة العادية
    $('#existing_beneficiary').on('click change', function() {
        console.log('🔄 تم النقر أو التغيير على القائمة العادية');
        setTimeout(function() {
            loadBeneficiaryData();
        }, 50);
    });



    // تهيئة Select2 للمستفيدين
    $('#existing_beneficiary').select2({
        theme: 'bootstrap-5',
        placeholder: 'ابحث عن مستفيد موجود...',
        allowClear: true,
        minimumInputLength: 0,
        ajax: {
            url: '/transfers/api/beneficiaries',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                console.log('🔍 البحث عن:', params.term || 'جميع المستفيدين');
                return {
                    search: params.term || ''
                };
            },
            processResults: function (data, params) {
                console.log('📡 استجابة API المستفيدين:', data);

                if (data && data.success && data.data) {
                    console.log(`✅ تم جلب ${data.data.length} مستفيد`);
                    return {
                        results: data.data.map(function(item) {
                            return {
                                id: item.id,
                                text: item.beneficiary_name + ' - ' + item.bank_name + ' (' + item.bank_account + ')',
                                data: item
                            };
                        })
                    };
                } else {
                    console.error('❌ خطأ في استجابة API:', data);
                    return {
                        results: []
                    };
                }
            },
            transport: function (params, success, failure) {
                console.log('🚀 إرسال طلب Ajax:', params);

                var $request = $.ajax(params);

                $request.then(function(data) {
                    console.log('✅ نجح الطلب:', data);
                    success(data);
                });

                $request.fail(function(jqXHR, textStatus, errorThrown) {
                    console.error('❌ فشل الطلب:', {
                        status: jqXHR.status,
                        statusText: jqXHR.statusText,
                        responseText: jqXHR.responseText.substring(0, 200),
                        textStatus: textStatus,
                        errorThrown: errorThrown
                    });
                    failure();
                });

                return $request;
            },
            cache: true
        }
    });

    // معالجة إرسال النموذج
    $('#transferRequestForm').submit(function(e) {
        e.preventDefault();
        submitTransferRequest();
    });
});

// تحميل الفروع
function loadBranches() {
    $.get('/transfers/api/branches')
        .done(function(response) {
            if (response.success) {
                const select = $('#branch_id');
                select.empty().append('<option value="">اختر الفرع...</option>');

                response.data.forEach(function(branch) {
                    select.append(`<option value="${branch.id}">${branch.name_ar}</option>`);
                });
            }
        })
        .fail(function() {
            showAlert('خطأ في تحميل الفروع', 'danger');
        });
}

// تحميل العملات
function loadCurrencies() {
    $.get('/transfers/api/currencies')
        .done(function(response) {
            if (response.success) {
                const select = $('#currency_id');
                select.empty().append('<option value="">اختر العملة...</option>');

                response.data.forEach(function(currency) {
                    select.append(`<option value="${currency.id}" data-symbol="${currency.symbol}">${currency.name_ar} (${currency.code})</option>`);
                });

                // اختيار العملة الأساسية افتراضياً
                const baseCurrency = response.data.find(c => c.is_base_currency);
                if (baseCurrency) {
                    select.val(baseCurrency.id).trigger('change');
                }
            }
        })
        .fail(function() {
            showAlert('خطأ في تحميل العملات', 'danger');
        });
}

// تحميل الصرافين والبنوك
function loadMoneyChangersBanks() {
    const transferType = $('#transfer_type').val();
    const select = $('#money_changer_bank_id');

    if (!transferType) {
        select.prop('disabled', true).empty().append('<option value="">اختر نوع التحويل أولاً...</option>');
        return;
    }

    select.prop('disabled', false).empty().append('<option value="">جاري التحميل...</option>');

    $.get('/transfers/api/money-changers-banks', { type: transferType })
        .done(function(response) {
            if (response.success) {
                select.empty().append('<option value="">اختر ' + (transferType === 'bank' ? 'البنك' : 'الصراف') + '...</option>');

                response.data.forEach(function(item) {
                    select.append(`<option value="${item.id}" data-commission="${item.commission_rate}">${item.name}</option>`);
                });
            }
        })
        .fail(function() {
            showAlert('خطأ في تحميل ' + (transferType === 'bank' ? 'البنوك' : 'الصرافين'), 'danger');
            select.empty().append('<option value="">خطأ في التحميل</option>');
        });
}

// تحميل المستفيدين
function loadBeneficiaries() {
    console.log('🔄 بدء تحميل المستفيدين...');

    // تحميل أولي للمستفيدين لعرضهم في القائمة
    $.get('/transfers/api/beneficiaries')
        .done(function(response) {
            console.log('📡 استجابة تحميل المستفيدين:', response);

            if (response.success && response.data) {
                console.log(`✅ تم تحميل ${response.data.length} مستفيد بنجاح`);

                // إضافة المستفيدين كخيارات في القائمة العادية للاختبار
                const select = $('#existing_beneficiary');

                // مسح الخيارات الموجودة (عدا الخيار الافتراضي)
                select.find('option:not(:first)').remove();

                response.data.forEach(function(beneficiary) {
                    const optionText = beneficiary.beneficiary_name + ' - ' + beneficiary.bank_name;
                    const option = $('<option></option>')
                        .attr('value', beneficiary.id)
                        .text(optionText)
                        .data('beneficiary', beneficiary); // استخدام .data() بدلاً من data-attribute

                    select.append(option);

                    console.log(`➕ تم إضافة المستفيد: ${beneficiary.beneficiary_name}`);
                });

                // اختبار البيانات المحفوظة
                console.log('🧪 اختبار البيانات المحفوظة في القائمة:');
                select.find('option').each(function(index) {
                    if (index > 0) { // تجاهل الخيار الافتراضي
                        const optionData = $(this).data('beneficiary');
                        console.log(`  - الخيار ${index}: ${$(this).text()}`);
                        console.log(`    البيانات:`, optionData);
                    }
                });

                console.log('✅ تم إضافة المستفيدين للقائمة العادية');
            } else {
                console.error('❌ فشل في تحميل المستفيدين:', response.message || 'خطأ غير معروف');
                showAlert('فشل في تحميل المستفيدين: ' + (response.message || 'خطأ غير معروف'), 'danger');
            }
        })
        .fail(function(xhr, status, error) {
            console.error('❌ خطأ في تحميل المستفيدين:', {
                status: xhr.status,
                statusText: xhr.statusText,
                responseText: xhr.responseText ? xhr.responseText.substring(0, 200) : 'لا توجد استجابة',
                error: error
            });
            showAlert('خطأ في تحميل المستفيدين: ' + error, 'danger');
        });
}

// تحميل بيانات المستفيد المختار
function loadBeneficiaryData() {
    console.log('🔄 تحميل بيانات المستفيد...');

    const selectedValue = $('#existing_beneficiary').val();
    console.log('🎯 القيمة المختارة:', selectedValue);
    console.log('🎯 نوع القيمة:', typeof selectedValue);

    if (!selectedValue || selectedValue === '' || selectedValue === null) {
        console.log('❌ لم يتم اختيار مستفيد');
        clearBeneficiaryForm();
        return;
    }

    // محاولة الحصول على البيانات من Select2 أولاً
    let foundData = false;

    try {
        if (typeof $('#existing_beneficiary').select2 === 'function') {
            const selectedData = $('#existing_beneficiary').select2('data')[0];
            console.log('📊 بيانات Select2:', selectedData);

            if (selectedData && selectedData.data) {
                console.log('✅ تم الحصول على البيانات من Select2');
                fillBeneficiaryForm(selectedData.data);
                foundData = true;
                return;
            }
        }
    } catch (e) {
        console.log('⚠️ Select2 غير متاح:', e.message);
    }

    if (!foundData) {
        // محاولة الحصول على البيانات من القائمة العادية
        const selectedOption = $('#existing_beneficiary option:selected');
        console.log('📋 الخيار المختار:', selectedOption.length, 'عنصر');
        console.log('📋 نص الخيار:', selectedOption.text());
        console.log('📋 قيمة الخيار:', selectedOption.val());

        const beneficiaryData = selectedOption.data('beneficiary');
        console.log('📊 بيانات المستفيد من القائمة:', beneficiaryData);

        if (beneficiaryData) {
            console.log('✅ تم الحصول على البيانات من القائمة العادية');
            fillBeneficiaryForm(beneficiaryData);
            foundData = true;
        }
    }

    if (!foundData) {
        console.log('❌ لم يتم العثور على بيانات المستفيد، محاولة جلبها من API');

        // كحل أخير، جلب البيانات من API
        if (selectedValue && selectedValue !== '') {
            fetchBeneficiaryFromAPI(selectedValue);
        } else {
            console.log('❌ لا توجد قيمة صالحة لجلب البيانات من API');
            clearBeneficiaryForm();
        }
    }
}

// دالة مساعدة لملء نموذج المستفيد
function fillBeneficiaryForm(data) {
    console.log('📝 ملء نموذج المستفيد:', data);

    // ملء الحقول بالبيانات
    $('#beneficiary_name').val(data.beneficiary_name || '');
    $('#beneficiary_address').val(data.beneficiary_address || '');
    $('#beneficiary_type').val(data.type || 'individual');
    $('#bank_account').val(data.bank_account || '');
    $('#bank_name').val(data.bank_name || '');
    $('#bank_branch').val(data.bank_branch || '');
    $('#bank_country').val(data.bank_country || '');
    $('#iban').val(data.iban || '');
    $('#swift_code').val(data.swift_code || '');
    $('#identification_number').val(data.identification_number || '');
    $('#beneficiary_phone').val(data.phone || '');
    $('#beneficiary_email').val(data.email || '');

    // إزالة أي رسائل خطأ سابقة
    $('.is-invalid').removeClass('is-invalid');

    console.log('✅ تم ملء النموذج بنجاح');
}

// جلب بيانات المستفيد من API
function fetchBeneficiaryFromAPI(beneficiaryId) {
    console.log('🌐 جلب بيانات المستفيد من API:', beneficiaryId);

    $.get(`/transfers/api/beneficiary/${beneficiaryId}`)
        .done(function(response) {
            console.log('📡 استجابة API للمستفيد:', response);

            if (response.success && response.data) {
                console.log('✅ تم جلب بيانات المستفيد من API');
                fillBeneficiaryForm(response.data);
            } else {
                console.error('❌ فشل في جلب بيانات المستفيد:', response.message);
                showAlert('فشل في جلب بيانات المستفيد', 'warning');
            }
        })
        .fail(function(xhr, status, error) {
            console.error('❌ خطأ في جلب بيانات المستفيد:', error);
            showAlert('خطأ في جلب بيانات المستفيد', 'danger');
        });
}

// مسح نموذج المستفيد
function clearBeneficiaryForm() {
    console.log('🧹 مسح نموذج المستفيد');

    $('#beneficiaryForm input, #beneficiaryForm select').val('');
    $('#beneficiary_type').val('individual');

    // إزالة رسائل الخطأ
    $('.is-invalid').removeClass('is-invalid');

    console.log('✅ تم مسح النموذج');
}

// حساب العمولة
function calculateCommission() {
    const amount = parseFloat($('#amount').val()) || 0;
    const selectedOption = $('#money_changer_bank_id option:selected');
    const commissionRate = parseFloat(selectedOption.data('commission')) || 0;
    const currencySymbol = $('#currency_id option:selected').data('symbol') || '$';

    if (amount > 0 && commissionRate > 0) {
        const commission = (amount * commissionRate / 100).toFixed(2);
        $('#commission_display').val(commission);
        $('#currency-symbol, #commission-currency').text(currencySymbol);
    } else {
        $('#commission_display').val('');
    }
}

// إرسال طلب الحوالة
function submitTransferRequest() {
    console.log('📤 بدء إرسال طلب الحوالة...');

    if (!validateForm()) {
        console.log('❌ فشل التحقق من صحة النموذج');
        return;
    }

    const formData = {
        branch_id: $('#branch_id').val(),
        transfer_type: $('#transfer_type').val(),
        money_changer_bank_id: $('#money_changer_bank_id').val(),
        amount: parseFloat($('#amount').val()),
        currency_id: $('#currency_id').val(),
        purpose: $('#purpose').val(),
        notes: $('#notes').val(),
        beneficiary_data: {
            id: $('#smart_beneficiary_search_new').data('selected-id') || null,  // استخدام معرف المستفيد من البحث الذكي
            beneficiary_name: $('#beneficiary_name').val(),
            beneficiary_address: $('#beneficiary_address').val(),
            type: $('#beneficiary_type').val(),
            bank_account: $('#bank_account').val(),
            bank_name: $('#bank_name').val(),
            bank_branch: $('#bank_branch').val(),
            bank_country: $('#bank_country').val(),
            iban: $('#iban').val(),
            swift_code: $('#swift_code').val(),
            identification_number: $('#identification_number').val(),
            phone: $('#beneficiary_phone').val(),
            email: $('#beneficiary_email').val()
        }
    };

    console.log('📊 بيانات الطلب:', formData);

    $('#loadingModal').modal('show');
    console.log('⏳ تم عرض نافذة التحميل');

    $.ajax({
        url: '/transfers/api/transfer-request',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        timeout: 30000, // 30 ثانية timeout
        success: function(response) {
            console.log('✅ نجح الطلب:', response);
            $('#loadingModal').modal('hide');

            if (response.success) {
                // إظهار رسالة نجاح واضحة مع خيارات
                const successMessage = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>تم حفظ طلب الحوالة بنجاح!</strong><br>
                            <small class="text-muted">رقم الطلب: <strong>${response.request_number}</strong></small><br>
                            <small class="text-info">سيتم التوجيه لقائمة الطلبات خلال <span id="countdown">3</span> ثوان...</small>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="cancelRedirect()">
                                <i class="fas fa-plus"></i> طلب جديد
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" onclick="goToRequestsList()">
                                <i class="fas fa-list"></i> قائمة الطلبات
                            </button>
                        </div>
                    </div>
                `;

                showAlert(successMessage, 'success', false); // false = لا تخفي تلقائياً

                // عداد تنازلي
                let countdown = 3;
                window.countdownInterval = setInterval(function() {
                    countdown--;
                    const countdownElement = document.getElementById('countdown');
                    if (countdownElement) {
                        countdownElement.textContent = countdown;
                    }

                    if (countdown <= 0) {
                        clearInterval(window.countdownInterval);
                        window.countdownInterval = null;
                    }
                }, 1000);

                // إعادة توجيه تلقائية بعد 3 ثوان (أسرع)
                window.redirectTimeout = setTimeout(function() {
                    console.log('🔄 إعادة توجيه تلقائية لقائمة الحوالات...');

                    // إيقاف العداد التنازلي
                    if (window.countdownInterval) {
                        clearInterval(window.countdownInterval);
                        window.countdownInterval = null;
                    }

                    window.location.href = '/transfers/list-requests';
                }, 3000); // تم تقليل الوقت من 5 إلى 3 ثوان

                resetForm();
            } else {
                console.error('❌ فشل الطلب:', response.message);
                showAlert(response.message || 'حدث خطأ أثناء إنشاء الطلب', 'danger');
            }
        },
        error: function(xhr, textStatus, errorThrown) {
            console.error('❌ خطأ في الطلب:', {
                status: xhr.status,
                statusText: xhr.statusText,
                textStatus: textStatus,
                errorThrown: errorThrown,
                responseText: xhr.responseText ? xhr.responseText.substring(0, 500) : 'لا توجد استجابة'
            });

            $('#loadingModal').modal('hide');

            let errorMessage = 'حدث خطأ أثناء إنشاء الطلب';

            if (textStatus === 'timeout') {
                errorMessage = 'انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.';
            } else if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.status === 0) {
                errorMessage = 'فشل في الاتصال بالخادم. تحقق من الاتصال بالإنترنت.';
            } else if (xhr.status >= 500) {
                errorMessage = 'خطأ في الخادم. يرجى المحاولة لاحقاً.';
            } else if (xhr.status === 401) {
                errorMessage = 'انتهت جلسة تسجيل الدخول. يرجى تسجيل الدخول مرة أخرى.';
            }

            showAlert(errorMessage, 'danger');
        }
    });
}

// التحقق من صحة النموذج
function validateForm() {
    const form = document.getElementById('transferRequestForm');

    // إزالة التحقق السابق
    form.classList.remove('was-validated');

    // التحقق من الحقول المطلوبة
    let isValid = true;

    const requiredFields = ['branch_id', 'transfer_type', 'money_changer_bank_id',
                           'amount', 'currency_id', 'purpose', 'beneficiary_name',
                           'bank_account', 'bank_name'];

    requiredFields.forEach(function(fieldId) {
        const field = document.getElementById(fieldId);
        if (!field.value.trim()) {
            isValid = false;
            field.classList.add('is-invalid');
        } else {
            field.classList.remove('is-invalid');
        }
    });

    // التحقق من صحة المبلغ
    const amount = parseFloat($('#amount').val());
    if (amount <= 0) {
        $('#amount').addClass('is-invalid');
        isValid = false;
    }

    form.classList.add('was-validated');

    if (!isValid) {
        showAlert('يرجى ملء جميع الحقول المطلوبة بشكل صحيح', 'warning');
    }

    return isValid;
}

// إعادة تعيين النموذج
function resetForm() {
    console.log('🔄 إعادة تعيين النموذج...');

    document.getElementById('transferRequestForm').reset();

    // مسح البحث الذكي
    if (smartSearchNew) {
        smartSearchNew.clearSearch();
    }

    // إعادة تعيين الحقول الخاصة
    $('#money_changer_bank_id').prop('disabled', true);
    $('#commission_display').val('');
    $('.is-invalid').removeClass('is-invalid');
    $('.was-validated').removeClass('was-validated');

    console.log('✅ تم إعادة تعيين النموذج');
}

// إلغاء إعادة التوجيه التلقائية
function cancelRedirect() {
    console.log('🛑 إلغاء إعادة التوجيه التلقائية');

    // إيقاف التوجيه التلقائي
    if (window.redirectTimeout) {
        clearTimeout(window.redirectTimeout);
        window.redirectTimeout = null;
    }

    // إيقاف العداد التنازلي
    if (window.countdownInterval) {
        clearInterval(window.countdownInterval);
        window.countdownInterval = null;
    }

    // إخفاء رسالة النجاح
    $('.alert').fadeOut(300);

    // إظهار رسالة جديدة
    setTimeout(function() {
        showAlert('<i class="fas fa-plus-circle text-primary me-2"></i><strong>يمكنك الآن إنشاء طلب حوالة جديد</strong>', 'info');
    }, 400);
}

// الذهاب لقائمة الطلبات
function goToRequestsList() {
    console.log('📋 الذهاب لقائمة الطلبات...');

    // إيقاف التوجيه التلقائي
    if (window.redirectTimeout) {
        clearTimeout(window.redirectTimeout);
        window.redirectTimeout = null;
    }

    // إيقاف العداد التنازلي
    if (window.countdownInterval) {
        clearInterval(window.countdownInterval);
        window.countdownInterval = null;
    }

    // إظهار رسالة تحميل
    showAlert('<i class="fas fa-spinner fa-spin me-2"></i><strong>جاري التوجيه لقائمة الطلبات...</strong>', 'info');

    // التوجيه الفوري
    window.location.href = '/transfers/list-requests';
}

// حفظ كمسودة
function saveDraft() {
    console.log('💾 بدء حفظ المسودة...');

    // طلب اسم المسودة من المستخدم
    const draftName = prompt('أدخل اسم المسودة:', `مسودة ${new Date().toLocaleDateString('ar-SA')}`);

    if (!draftName) {
        console.log('❌ تم إلغاء حفظ المسودة');
        return;
    }

    // جمع بيانات النموذج
    const formData = {
        draft_name: draftName,
        branch_id: $('#branch_id').val() || null,
        transfer_type: $('#transfer_type').val() || null,
        money_changer_bank_id: $('#money_changer_bank_id').val() || null,
        amount: parseFloat($('#amount').val()) || null,
        currency_id: $('#currency_id').val() || null,
        purpose: $('#purpose').val() || null,
        notes: $('#notes').val() || null,
        beneficiary_data: {
            id: $('#existing_beneficiary').val() || null,
            beneficiary_name: $('#beneficiary_name').val() || null,
            beneficiary_address: $('#beneficiary_address').val() || null,
            type: $('#beneficiary_type').val() || null,
            bank_account: $('#bank_account').val() || null,
            bank_name: $('#bank_name').val() || null,
            bank_branch: $('#bank_branch').val() || null,
            bank_country: $('#bank_country').val() || null,
            iban: $('#iban').val() || null,
            swift_code: $('#swift_code').val() || null,
            identification_number: $('#identification_number').val() || null,
            phone: $('#beneficiary_phone').val() || null,
            email: $('#beneficiary_email').val() || null
        }
    };

    console.log('📊 بيانات المسودة:', formData);

    // إرسال الطلب
    $.ajax({
        url: '/transfers/api/drafts',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                console.log('✅ تم حفظ المسودة بنجاح');
                showAlert(`تم حفظ المسودة "${draftName}" بنجاح`, 'success');

                // تحديث قائمة المسودات
                loadDrafts();
            } else {
                console.error('❌ فشل في حفظ المسودة:', response.message);
                showAlert(response.message || 'فشل في حفظ المسودة', 'danger');
            }
        },
        error: function(xhr) {
            console.error('❌ خطأ في حفظ المسودة:', xhr);
            const response = xhr.responseJSON;
            showAlert(response?.message || 'حدث خطأ أثناء حفظ المسودة', 'danger');
        }
    });
}

// عرض التنبيهات
function showAlert(message, type = 'info', autoHide = true) {
    // إزالة التنبيهات السابقة
    $('.alert').remove();

    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // إضافة التنبيه في أعلى الصفحة
    $('.container-fluid').prepend(alertHtml);

    // إزالة التنبيه تلقائياً بعد 5 ثوان (إذا كان مطلوباً)
    if (autoHide) {
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
}

// ===== دوال المسودات =====

// تبديل عرض المسودات
function toggleDrafts() {
    const draftsSection = $('#draftsSection');
    const toggleBtn = $('button[onclick="toggleDrafts()"]');

    if (draftsSection.is(':visible')) {
        draftsSection.slideUp();
        toggleBtn.html('<i class="fas fa-folder-open me-2"></i>عرض المسودات');
    } else {
        draftsSection.slideDown();
        toggleBtn.html('<i class="fas fa-folder me-2"></i>إخفاء المسودات');
        loadDrafts();
    }
}

// تحميل قائمة المسودات
function loadDrafts() {
    console.log('📂 تحميل المسودات...');

    $.get('/transfers/api/drafts')
        .done(function(response) {
            console.log('📡 استجابة المسودات:', response);

            if (response.success) {
                displayDrafts(response.data);
            } else {
                console.error('❌ فشل في تحميل المسودات:', response.message);
                showAlert('فشل في تحميل المسودات', 'warning');
            }
        })
        .fail(function(xhr, status, error) {
            console.error('❌ خطأ في تحميل المسودات:', error);
            showAlert('خطأ في تحميل المسودات', 'danger');
        });
}

// عرض المسودات
function displayDrafts(drafts) {
    const draftsList = $('#draftsList');
    draftsList.empty();

    if (drafts.length === 0) {
        draftsList.html(`
            <div class="list-group-item text-center text-muted">
                <i class="fas fa-inbox fa-2x mb-2"></i>
                <p class="mb-0">لا توجد مسودات محفوظة</p>
            </div>
        `);
        return;
    }

    drafts.forEach(function(draft) {
        const draftItem = $(`
            <div class="list-group-item list-group-item-action">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">${draft.draft_name}</h6>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            آخر تحديث: ${new Date(draft.updated_at).toLocaleString('ar-SA')}
                        </small>
                    </div>
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="loadDraft(${draft.id})">
                            <i class="fas fa-upload me-1"></i>تحميل
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteDraft(${draft.id}, '${draft.draft_name}')">
                            <i class="fas fa-trash me-1"></i>حذف
                        </button>
                    </div>
                </div>
            </div>
        `);

        draftsList.append(draftItem);
    });

    console.log(`✅ تم عرض ${drafts.length} مسودة`);
}

// تحميل مسودة محددة
function loadDraft(draftId) {
    console.log('📥 تحميل المسودة:', draftId);

    if (!confirm('هل تريد تحميل هذه المسودة؟ سيتم استبدال البيانات الحالية.')) {
        return;
    }

    $.get(`/transfers/api/drafts/${draftId}`)
        .done(function(response) {
            console.log('📡 بيانات المسودة:', response);

            if (response.success) {
                fillFormWithDraft(response.data);
                showAlert('تم تحميل المسودة بنجاح', 'success');
                toggleDrafts(); // إخفاء قسم المسودات
            } else {
                console.error('❌ فشل في تحميل المسودة:', response.message);
                showAlert('فشل في تحميل المسودة', 'danger');
            }
        })
        .fail(function(xhr, status, error) {
            console.error('❌ خطأ في تحميل المسودة:', error);
            showAlert('خطأ في تحميل المسودة', 'danger');
        });
}

// ملء النموذج ببيانات المسودة
function fillFormWithDraft(draft) {
    console.log('📝 ملء النموذج بالمسودة:', draft);

    // تفاصيل الحوالة
    $('#branch_id').val(draft.branch_id).trigger('change');
    $('#transfer_type').val(draft.transfer_type).trigger('change');

    // انتظار تحميل الصرافين/البنوك ثم تعيين القيمة
    setTimeout(function() {
        $('#money_changer_bank_id').val(draft.money_changer_bank_id).trigger('change');
    }, 500);

    $('#amount').val(draft.amount);
    $('#currency_id').val(draft.currency_id).trigger('change');
    $('#purpose').val(draft.purpose);
    $('#notes').val(draft.notes);

    // بيانات المستفيد
    if (draft.beneficiary_data) {
        const beneficiary = draft.beneficiary_data;

        if (beneficiary.id) {
            $('#existing_beneficiary').val(beneficiary.id).trigger('change');
        }

        $('#beneficiary_name').val(beneficiary.beneficiary_name || '');
        $('#beneficiary_address').val(beneficiary.beneficiary_address || '');
        $('#beneficiary_type').val(beneficiary.type || 'individual');
        $('#bank_account').val(beneficiary.bank_account || '');
        $('#bank_name').val(beneficiary.bank_name || '');
        $('#bank_branch').val(beneficiary.bank_branch || '');
        $('#bank_country').val(beneficiary.bank_country || '');
        $('#iban').val(beneficiary.iban || '');
        $('#swift_code').val(beneficiary.swift_code || '');
        $('#identification_number').val(beneficiary.identification_number || '');
        $('#beneficiary_phone').val(beneficiary.phone || '');
        $('#beneficiary_email').val(beneficiary.email || '');
    }

    console.log('✅ تم ملء النموذج بالمسودة');
}

// حذف مسودة
function deleteDraft(draftId, draftName) {
    console.log('🗑️ حذف المسودة:', draftId);

    if (!confirm(`هل تريد حذف المسودة "${draftName}"؟`)) {
        return;
    }

    $.ajax({
        url: `/transfers/api/drafts/${draftId}`,
        method: 'DELETE',
        success: function(response) {
            if (response.success) {
                console.log('✅ تم حذف المسودة بنجاح');
                showAlert('تم حذف المسودة بنجاح', 'success');
                loadDrafts(); // إعادة تحميل القائمة
            } else {
                console.error('❌ فشل في حذف المسودة:', response.message);
                showAlert('فشل في حذف المسودة', 'danger');
            }
        },
        error: function(xhr) {
            console.error('❌ خطأ في حذف المسودة:', xhr);
            const response = xhr.responseJSON;
            showAlert(response?.message || 'حدث خطأ أثناء حذف المسودة', 'danger');
        }
    });
}

// تهيئة البحث الذكي للمستفيدين في صفحة الطلب الجديد
let smartSearchNew;

$(document).ready(function() {
    console.log('📋 تحميل صفحة الطلب الجديد...');

    // تأخير قصير للتأكد من تحميل جميع العناصر
    setTimeout(function() {
        // تهيئة البحث الذكي
        const searchInput = document.getElementById('smart_beneficiary_search_new');
        console.log('🔍 عنصر البحث:', searchInput);

        if (searchInput) {
            console.log('✅ تم العثور على عنصر البحث، بدء التهيئة...');

            smartSearchNew = new SmartBeneficiarySearch(searchInput, {
                minChars: 2,
                maxResults: 8,
                debounceDelay: 250,
                placeholder: 'ابحث بالاسم، البنك، رقم الحساب، IBAN، الهاتف...'
            });

            console.log('🎯 تم إنشاء البحث الذكي:', smartSearchNew);

        // عند اختيار مستفيد
        searchInput.addEventListener('beneficiarySelected', function(e) {
            const beneficiary = e.detail;
            console.log('🎯 تم اختيار مستفيد في الطلب الجديد:', beneficiary);
            console.log('📋 بيانات المستفيد المستلمة:', beneficiary);

            // ملء حقول المستفيد
            fillBeneficiaryFormNew(beneficiary);

            // إظهار رسالة نجاح
            showAlert(`تم اختيار المستفيد: ${beneficiary.beneficiary_name}`, 'success');
        });

            // عند مسح البحث
            searchInput.addEventListener('beneficiaryCleared', function(e) {
                console.log('🗑️ تم مسح البحث في الطلب الجديد');
                clearBeneficiaryFormNew();
            });
        } else {
            console.error('❌ لم يتم العثور على عنصر البحث smart_beneficiary_search_new');
        }
    }, 500); // تأخير 500ms
});

// ملء حقول المستفيد بالبيانات المختارة (للطلب الجديد)
function fillBeneficiaryFormNew(beneficiary) {
    console.log('🎯 ملء حقول المستفيد:', beneficiary);

    // ملء الحقول الأساسية وإطلاق أحداث التغيير
    $('#beneficiary_name').val(beneficiary.beneficiary_name || '').trigger('change').trigger('input');
    $('#beneficiary_address').val(beneficiary.beneficiary_address || '').trigger('change').trigger('input');
    $('#beneficiary_type').val(beneficiary.type || 'supplier').trigger('change');

    // ملء البيانات المصرفية وإطلاق أحداث التغيير
    $('#bank_account').val(beneficiary.bank_account || '').trigger('change').trigger('input');
    $('#bank_name').val(beneficiary.bank_name || '').trigger('change').trigger('input');
    $('#bank_branch').val(beneficiary.bank_branch || '').trigger('change').trigger('input');
    $('#bank_country').val(beneficiary.bank_country || '').trigger('change').trigger('input');
    $('#iban').val(beneficiary.iban || '').trigger('change').trigger('input');
    $('#swift_code').val(beneficiary.swift_code || '').trigger('change').trigger('input');

    // ملء البيانات الإضافية وإطلاق أحداث التغيير
    $('#identification_number').val(beneficiary.identification_number || '').trigger('change').trigger('input');
    $('#beneficiary_phone').val(beneficiary.phone || '').trigger('change').trigger('input');
    $('#beneficiary_email').val(beneficiary.email || '').trigger('change').trigger('input');

    // تحديث واجهة المستخدم
    updateFieldValidationNew();

    // إطلاق حدث تحديث النموذج العام
    $(document).trigger('beneficiaryDataUpdated', [beneficiary]);

    // تمرير سلس إلى حقول المستفيد
    $('html, body').animate({
        scrollTop: $('#beneficiaryForm').offset().top - 100
    }, 500);

    // حفظ معرف المستفيد للاستخدام لاحقاً
    $('#smart_beneficiary_search_new').data('selected-id', beneficiary.id);

    // إعادة تهيئة أي validation موجود
    if (typeof validateForm === 'function') {
        setTimeout(validateForm, 100);
    }

    // تحديث حالة النموذج
    $('#transferRequestForm').trigger('formUpdated');

    console.log('✅ تم ملء جميع الحقول وإطلاق الأحداث');
}

// مسح حقول المستفيد (للطلب الجديد)
function clearBeneficiaryFormNew() {
    console.log('🗑️ مسح حقول المستفيد');

    // مسح جميع حقول المستفيد - تصحيح أسماء الحقول
    $('#beneficiary_name, #beneficiary_address, #bank_account, #bank_name, #bank_branch, #bank_country, #iban, #swift_code, #identification_number, #beneficiary_phone, #beneficiary_email').val('');
    $('#beneficiary_type').val('supplier');

    // إزالة حالات التحقق
    $('.form-control').removeClass('is-invalid is-valid');

    // مسح معرف المستفيد المحفوظ
    $('#smart_beneficiary_search_new').removeData('selected-id');

    showAlert('تم مسح بيانات المستفيد', 'info');
}

// تحديث حالة التحقق من الحقول (للطلب الجديد)
function updateFieldValidationNew() {
    // إزالة حالات الخطأ السابقة
    $('.form-control').removeClass('is-invalid is-valid');

    // التحقق من الحقول المطلوبة
    const requiredFields = ['beneficiary_name'];
    requiredFields.forEach(fieldId => {
        const field = $(`#${fieldId}`);
        if (field.val().trim()) {
            field.addClass('is-valid');
        }
    });
}

</script>

<!-- تحميل JavaScript للبحث الذكي -->
<script src="{{ url_for('static', filename='js/smart-beneficiary-search.js') }}"></script>

{% endblock %}
