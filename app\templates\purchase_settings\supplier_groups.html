{% extends "base.html" %}

{% block title %}مجموعات الموردين{% endblock %}

{% block extra_css %}
<style>
    .supplier-groups-container {
        background: #f8f9fa;
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .page-header h1 {
        font-size: 2.5rem;
        font-weight: bold;
        margin: 0;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }
    
    .page-header p {
        font-size: 1.1rem;
        margin: 10px 0 0 0;
        opacity: 0.9;
    }
    
    .control-panel {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: 1px solid #e9ecef;
    }
    
    .btn-add-group {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }
    
    .btn-add-group:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        color: white;
    }
    
    .groups-table-container {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: 1px solid #e9ecef;
    }
    
    .table {
        margin: 0;
        border-radius: 10px;
        overflow: hidden;
    }
    
    .table thead th {
        background: linear-gradient(45deg, #495057, #6c757d);
        color: white;
        border: none;
        padding: 15px;
        font-weight: bold;
        text-align: center;
    }
    
    .table tbody td {
        padding: 12px 15px;
        vertical-align: middle;
        border-color: #e9ecef;
        text-align: center;
    }
    
    .table tbody tr:hover {
        background-color: #f8f9fa;
        transform: scale(1.01);
        transition: all 0.2s ease;
    }
    
    .badge-active {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 0.85rem;
    }
    
    .badge-inactive {
        background: linear-gradient(45deg, #dc3545, #c82333);
        color: white;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 0.85rem;
    }
    
    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: center;
    }
    
    .btn-action {
        padding: 6px 12px;
        border-radius: 20px;
        border: none;
        font-size: 0.85rem;
        transition: all 0.3s ease;
        min-width: 80px;
    }
    
    .btn-edit {
        background: linear-gradient(45deg, #ffc107, #ffb300);
        color: #212529;
    }
    
    .btn-edit:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
    }
    
    .btn-delete {
        background: linear-gradient(45deg, #dc3545, #c82333);
        color: white;
    }
    
    .btn-delete:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
    }
    
    .modal-content {
        border-radius: 15px;
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    
    .modal-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        border: none;
    }
    
    .modal-title {
        font-weight: bold;
    }
    
    .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .form-label {
        font-weight: bold;
        color: #495057;
        margin-bottom: 8px;
    }
    
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 50px;
    }
    
    .spinner-border {
        width: 3rem;
        height: 3rem;
        color: #667eea;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #dee2e6;
    }
    
    .search-box {
        border-radius: 25px;
        border: 2px solid #e9ecef;
        padding: 10px 20px;
        transition: all 0.3s ease;
    }
    
    .search-box:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    @media (max-width: 768px) {
        .page-header h1 {
            font-size: 2rem;
        }
        
        .control-panel {
            padding: 15px;
        }
        
        .groups-table-container {
            padding: 15px;
            overflow-x: auto;
        }
        
        .action-buttons {
            flex-direction: column;
            gap: 4px;
        }
        
        .btn-action {
            min-width: 60px;
            padding: 4px 8px;
            font-size: 0.75rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="supplier-groups-container">
    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1><i class="fas fa-users-cog"></i> مجموعات الموردين</h1>
                        <p>إدارة وتصنيف مجموعات الموردين المختلفة</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex align-items-center justify-content-end">
                            <i class="fas fa-layer-group fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- لوحة التحكم -->
        <div class="container">
            <div class="control-panel">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <button type="button" class="btn btn-add-group" onclick="showAddGroupModal()">
                            <i class="fas fa-plus"></i> إضافة مجموعة جديدة
                        </button>
                    </div>
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control search-box" id="searchInput" 
                                   placeholder="البحث في مجموعات الموردين...">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول مجموعات الموردين -->
            <div class="groups-table-container">
                <div class="loading-spinner" id="loadingSpinner">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-3">جاري تحميل مجموعات الموردين...</p>
                </div>

                <div id="groupsTableContainer" style="display: none;">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم المجموعة</th>
                                    <th>اسم المجموعة (عربي)</th>
                                    <th>اسم المجموعة (إنجليزي)</th>
                                    <th>رقم الحساب</th>
                                    <th>اسم الحساب</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="groupsTableBody">
                                <!-- سيتم ملء البيانات هنا بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="empty-state" id="emptyState" style="display: none;">
                    <i class="fas fa-users-slash"></i>
                    <h4>لا توجد مجموعات موردين</h4>
                    <p>لم يتم العثور على أي مجموعات موردين. انقر على "إضافة مجموعة جديدة" لإنشاء أول مجموعة.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة/تعديل مجموعة -->
<div class="modal fade" id="groupModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="groupModalTitle">
                    <i class="fas fa-plus"></i> إضافة مجموعة جديدة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="groupForm">
                    <input type="hidden" id="groupId" name="group_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="groupNameAr" class="form-label">
                                    <i class="fas fa-tag"></i> اسم المجموعة (عربي) *
                                </label>
                                <input type="text" class="form-control" id="groupNameAr" name="group_name_ar" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="groupNameEn" class="form-label">
                                    <i class="fas fa-tag"></i> اسم المجموعة (إنجليزي)
                                </label>
                                <input type="text" class="form-control" id="groupNameEn" name="group_name_en">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="accountNumber" class="form-label">
                                    <i class="fas fa-hashtag"></i> رقم الحساب
                                </label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="accountNumber" name="account_number">
                                    <button type="button" class="btn btn-outline-secondary" onclick="selectAccount()">
                                        <i class="fas fa-search"></i> F9
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="accountName" class="form-label">
                                    <i class="fas fa-file-signature"></i> اسم الحساب
                                </label>
                                <input type="text" class="form-control" id="accountName" name="account_name" readonly>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">
                            <i class="fas fa-sticky-note"></i> ملاحظات
                        </label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> إلغاء
                </button>
                <button type="button" class="btn btn-primary" onclick="saveGroup()">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// متغيرات عامة
let supplierGroups = [];
let currentGroupId = null;
let isEditMode = false;

// تحميل الصفحة
$(document).ready(function() {
    loadSupplierGroups();
    setupEventListeners();
});

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // البحث
    $('#searchInput').on('input', function() {
        filterGroups($(this).val());
    });

    // مفتاح F9 لاختيار الحساب
    $(document).on('keydown', function(e) {
        if (e.key === 'F9' && $('#groupModal').hasClass('show')) {
            e.preventDefault();
            selectAccount();
        }
    });
}

// تحميل مجموعات الموردين
function loadSupplierGroups() {
    $('#loadingSpinner').show();
    $('#groupsTableContainer').hide();
    $('#emptyState').hide();

    $.ajax({
        url: '/purchase_settings/api/supplier_groups',
        method: 'GET',
        xhrFields: {
            withCredentials: true
        },
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        success: function(response) {
            if (response.success) {
                supplierGroups = response.groups;
                displayGroups(supplierGroups);
            } else {
                showAlert('خطأ في تحميل البيانات: ' + response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            if (xhr.status === 302 || xhr.status === 401) {
                showAlert('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.', 'warning');
                setTimeout(function() {
                    window.location.href = '/auth/login';
                }, 2000);
            } else {
                showAlert('خطأ في الاتصال بالخادم', 'error');
                console.error('Error:', error);
            }
        },
        complete: function() {
            $('#loadingSpinner').hide();
        }
    });
}

// عرض مجموعات الموردين
function displayGroups(groups) {
    const tbody = $('#groupsTableBody');
    tbody.empty();

    if (groups.length === 0) {
        $('#emptyState').show();
        $('#groupsTableContainer').hide();
        return;
    }

    $('#emptyState').hide();
    $('#groupsTableContainer').show();

    groups.forEach(function(group) {
        const row = `
            <tr data-group-id="${group.group_id}">
                <td><strong>${group.group_id}</strong></td>
                <td>${group.group_name_ar}</td>
                <td>${group.group_name_en || '-'}</td>
                <td>${group.account_number || '-'}</td>
                <td>${group.account_name || '-'}</td>
                <td>
                    <span class="badge ${group.is_active ? 'badge-active' : 'badge-inactive'}">
                        ${group.is_active ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button type="button" class="btn btn-action btn-edit"
                                onclick="editGroup(${group.group_id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-action btn-delete"
                                onclick="deleteGroup(${group.group_id}, '${group.group_name_ar}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// فلترة المجموعات
function filterGroups(searchTerm) {
    if (!searchTerm) {
        displayGroups(supplierGroups);
        return;
    }

    const filteredGroups = supplierGroups.filter(group =>
        group.group_name_ar.includes(searchTerm) ||
        (group.group_name_en && group.group_name_en.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (group.account_number && group.account_number.includes(searchTerm)) ||
        (group.account_name && group.account_name.includes(searchTerm))
    );

    displayGroups(filteredGroups);
}

// إظهار نافذة إضافة مجموعة
function showAddGroupModal() {
    isEditMode = false;
    currentGroupId = null;

    $('#groupModalTitle').html('<i class="fas fa-plus"></i> إضافة مجموعة جديدة');
    $('#groupForm')[0].reset();
    $('#groupId').val('');

    $('#groupModal').modal('show');
}

// تعديل مجموعة
function editGroup(groupId) {
    isEditMode = true;
    currentGroupId = groupId;

    $('#groupModalTitle').html('<i class="fas fa-edit"></i> تعديل مجموعة الموردين');

    // البحث عن المجموعة
    const group = supplierGroups.find(g => g.group_id === groupId);
    if (!group) {
        showAlert('المجموعة غير موجودة', 'error');
        return;
    }

    // ملء النموذج
    $('#groupId').val(group.group_id);
    $('#groupNameAr').val(group.group_name_ar);
    $('#groupNameEn').val(group.group_name_en || '');
    $('#accountNumber').val(group.account_number || '');
    $('#accountName').val(group.account_name || '');
    $('#notes').val(group.notes || '');

    $('#groupModal').modal('show');
}

// حفظ المجموعة
function saveGroup() {
    const form = $('#groupForm')[0];
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const formData = {
        group_name_ar: $('#groupNameAr').val().trim(),
        group_name_en: $('#groupNameEn').val().trim(),
        account_number: $('#accountNumber').val().trim(),
        account_name: $('#accountName').val().trim(),
        notes: $('#notes').val().trim()
    };

    if (!formData.group_name_ar) {
        showAlert('اسم المجموعة باللغة العربية مطلوب', 'error');
        return;
    }

    const url = isEditMode ?
        `/purchase_settings/api/supplier_groups/${currentGroupId}` :
        '/purchase_settings/api/supplier_groups';

    const method = isEditMode ? 'PUT' : 'POST';

    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify(formData),
        xhrFields: {
            withCredentials: true
        },
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        success: function(response) {
            if (response.success) {
                showAlert(response.message, 'success');
                $('#groupModal').modal('hide');
                loadSupplierGroups();
            } else {
                showAlert('خطأ: ' + response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            if (xhr.status === 302 || xhr.status === 401) {
                showAlert('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.', 'warning');
                setTimeout(function() {
                    window.location.href = '/auth/login';
                }, 2000);
            } else {
                showAlert('خطأ في الاتصال بالخادم', 'error');
                console.error('Error:', error);
            }
        }
    });
}

// حذف مجموعة
function deleteGroup(groupId, groupName) {
    if (!confirm(`هل أنت متأكد من حذف مجموعة "${groupName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        return;
    }

    $.ajax({
        url: `/purchase_settings/api/supplier_groups/${groupId}`,
        method: 'DELETE',
        xhrFields: {
            withCredentials: true
        },
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        success: function(response) {
            if (response.success) {
                showAlert(response.message, 'success');
                loadSupplierGroups();
            } else {
                showAlert('خطأ: ' + response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            if (xhr.status === 302 || xhr.status === 401) {
                showAlert('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.', 'warning');
                setTimeout(function() {
                    window.location.href = '/auth/login';
                }, 2000);
            } else {
                showAlert('خطأ في الاتصال بالخادم', 'error');
                console.error('Error:', error);
            }
        }
    });
}

// اختيار حساب (F9)
function selectAccount() {
    // هنا يمكن إضافة نافذة اختيار الحساب من دليل الحسابات
    showAlert('سيتم تطوير نافذة اختيار الحساب قريباً', 'info');
}

// عرض رسالة
function showAlert(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // إزالة الرسائل السابقة
    $('.alert').remove();

    // إضافة الرسالة الجديدة
    $('body').prepend(alertHtml);

    // إزالة الرسالة تلقائياً بعد 5 ثوان
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
{% endblock %}
