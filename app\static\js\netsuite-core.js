/**
 * NetSuite Core JavaScript
 * الوظائف الأساسية لواجهة NetSuite Oracle
 */

// ========== إعدادات عامة ==========
const NetSuite = {
    config: {
        theme: localStorage.getItem('ns-theme') || 'light',
        language: localStorage.getItem('ns-language') || 'ar',
        sidebarCollapsed: localStorage.getItem('ns-sidebar-collapsed') === 'true'
    },
    
    // تهيئة النظام
    init() {
        this.initTheme();
        this.initSidebar();
        this.initDropdowns();
        this.initModals();
        this.initTabs();
        this.initTooltips();
        this.initNotifications();
        
        console.log('NetSuite UI initialized successfully');
    },
    
    // ========== إدارة المظهر ==========
    initTheme() {
        document.documentElement.setAttribute('data-theme', this.config.theme);
        
        // زر تبديل المظهر
        const themeToggle = document.querySelector('[data-theme-toggle]');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }
    },
    
    toggleTheme() {
        const currentTheme = this.config.theme;
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        this.config.theme = newTheme;
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('ns-theme', newTheme);
        
        // إرسال حدث تغيير المظهر
        document.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme: newTheme }
        }));
    },
    
    // ========== إدارة الشريط الجانبي ==========
    initSidebar() {
        const sidebar = document.querySelector('.ns-sidebar');
        const toggleBtn = document.querySelector('[data-sidebar-toggle]');
        const overlay = document.querySelector('.ns-sidebar-overlay');
        
        if (!sidebar) return;
        
        // تطبيق حالة الشريط الجانبي المحفوظة
        if (this.config.sidebarCollapsed) {
            sidebar.classList.add('collapsed');
        }
        
        // زر تبديل الشريط الجانبي
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => this.toggleSidebar());
        }
        
        // إغلاق الشريط الجانبي عند النقر على الخلفية
        if (overlay) {
            overlay.addEventListener('click', () => this.closeSidebar());
        }
        
        // إغلاق الشريط الجانبي عند الضغط على Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && sidebar.classList.contains('open')) {
                this.closeSidebar();
            }
        });
    },
    
    toggleSidebar() {
        const sidebar = document.querySelector('.ns-sidebar');
        const overlay = document.querySelector('.ns-sidebar-overlay');
        
        if (window.innerWidth <= 991) {
            // في الشاشات الصغيرة: فتح/إغلاق
            sidebar.classList.toggle('open');
            if (overlay) {
                overlay.classList.toggle('active');
            }
        } else {
            // في الشاشات الكبيرة: طي/توسيع
            sidebar.classList.toggle('collapsed');
            this.config.sidebarCollapsed = sidebar.classList.contains('collapsed');
            localStorage.setItem('ns-sidebar-collapsed', this.config.sidebarCollapsed);
        }
    },
    
    closeSidebar() {
        const sidebar = document.querySelector('.ns-sidebar');
        const overlay = document.querySelector('.ns-sidebar-overlay');
        
        sidebar.classList.remove('open');
        if (overlay) {
            overlay.classList.remove('active');
        }
    },
    
    // ========== القوائم المنسدلة ==========
    initDropdowns() {
        document.addEventListener('click', (e) => {
            const dropdownToggle = e.target.closest('[data-dropdown-toggle]');
            
            if (dropdownToggle) {
                e.preventDefault();
                const dropdown = dropdownToggle.closest('.ns-dropdown');
                this.toggleDropdown(dropdown);
            } else {
                // إغلاق جميع القوائم المنسدلة عند النقر خارجها
                this.closeAllDropdowns();
            }
        });
    },
    
    toggleDropdown(dropdown) {
        const isActive = dropdown.classList.contains('active');
        
        // إغلاق جميع القوائم المنسدلة الأخرى
        this.closeAllDropdowns();
        
        // تبديل حالة القائمة الحالية
        if (!isActive) {
            dropdown.classList.add('active');
        }
    },
    
    closeAllDropdowns() {
        document.querySelectorAll('.ns-dropdown.active').forEach(dropdown => {
            dropdown.classList.remove('active');
        });
    },
    
    // ========== النوافذ المنبثقة ==========
    initModals() {
        // أزرار فتح النوافذ المنبثقة
        document.addEventListener('click', (e) => {
            const modalTrigger = e.target.closest('[data-modal-target]');
            if (modalTrigger) {
                e.preventDefault();
                const targetId = modalTrigger.getAttribute('data-modal-target');
                this.openModal(targetId);
            }
            
            // أزرار إغلاق النوافذ المنبثقة
            const modalClose = e.target.closest('[data-modal-close]');
            if (modalClose) {
                e.preventDefault();
                this.closeModal();
            }
        });
        
        // إغلاق النافذة عند النقر على الخلفية
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('ns-modal-overlay')) {
                this.closeModal();
            }
        });
        
        // إغلاق النافذة عند الضغط على Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    },
    
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
            
            // التركيز على أول عنصر قابل للتفاعل
            const focusableElement = modal.querySelector('input, button, select, textarea, [tabindex]:not([tabindex="-1"])');
            if (focusableElement) {
                setTimeout(() => focusableElement.focus(), 100);
            }
        }
    },
    
    closeModal() {
        const activeModal = document.querySelector('.ns-modal-overlay.active');
        if (activeModal) {
            activeModal.classList.remove('active');
            document.body.style.overflow = '';
        }
    },
    
    // ========== التبويبات ==========
    initTabs() {
        document.addEventListener('click', (e) => {
            const tabButton = e.target.closest('.ns-tab-button');
            if (tabButton) {
                e.preventDefault();
                this.switchTab(tabButton);
            }
        });
    },
    
    switchTab(tabButton) {
        const tabContainer = tabButton.closest('.ns-tabs');
        const targetId = tabButton.getAttribute('data-tab-target');
        
        // إزالة الحالة النشطة من جميع التبويبات
        tabContainer.querySelectorAll('.ns-tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // إضافة الحالة النشطة للتبويب المحدد
        tabButton.classList.add('active');
        
        // إخفاء جميع محتويات التبويبات
        document.querySelectorAll('.ns-tab-content').forEach(content => {
            content.style.display = 'none';
        });
        
        // إظهار محتوى التبويب المحدد
        const targetContent = document.getElementById(targetId);
        if (targetContent) {
            targetContent.style.display = 'block';
        }
    },
    
    // ========== التلميحات ==========
    initTooltips() {
        // سيتم تطوير هذه الوظيفة لاحقاً
    },
    
    // ========== الإشعارات ==========
    initNotifications() {
        // إنشاء حاوي الإشعارات إذا لم يكن موجوداً
        if (!document.querySelector('.ns-notifications')) {
            const container = document.createElement('div');
            container.className = 'ns-notifications';
            document.body.appendChild(container);
        }
    },
    
    showNotification(message, type = 'info', duration = 5000) {
        const container = document.querySelector('.ns-notifications');
        const notification = document.createElement('div');
        
        notification.className = `ns-notification ns-notification-${type}`;
        notification.innerHTML = `
            <div class="ns-notification-content">
                <span class="ns-notification-message">${message}</span>
                <button class="ns-notification-close" onclick="NetSuite.closeNotification(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        container.appendChild(notification);
        
        // إظهار الإشعار
        setTimeout(() => notification.classList.add('show'), 100);
        
        // إخفاء الإشعار تلقائياً
        if (duration > 0) {
            setTimeout(() => this.closeNotification(notification), duration);
        }
        
        return notification;
    },
    
    closeNotification(element) {
        const notification = element.closest ? element.closest('.ns-notification') : element;
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }
};

// ========== تهيئة النظام عند تحميل الصفحة ==========
document.addEventListener('DOMContentLoaded', () => {
    NetSuite.init();
});

// ========== تصدير للاستخدام العام ==========
window.NetSuite = NetSuite;
