#!/usr/bin/env python3
"""
إضافة عمود رقم الفرع إلى جدول أوامر التسليم
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oracle_manager import get_oracle_manager

def add_branch_column():
    """إضافة عمود رقم الفرع إلى جدول أوامر التسليم"""
    
    oracle_mgr = get_oracle_manager()
    if not oracle_mgr.connect():
        print("❌ فشل الاتصال بقاعدة البيانات")
        return False
    
    try:
        print("🔍 فحص هيكل جدول DELIVERY_ORDERS...")
        
        # فحص هيكل الجدول الحالي
        check_columns_query = """
        SELECT column_name, data_type, data_length, nullable
        FROM user_tab_columns 
        WHERE table_name = 'DELIVERY_ORDERS'
        ORDER BY column_id
        """
        
        columns = oracle_mgr.execute_query(check_columns_query)
        
        print(f"\n📋 أعمدة جدول DELIVERY_ORDERS ({len(columns)} عمود):")
        branch_column_exists = False
        for col in columns:
            print(f"  - {col[0]}: {col[1]}({col[2]}) - Nullable: {col[3]}")
            if col[0] == 'BRANCH_ID':
                branch_column_exists = True
        
        if branch_column_exists:
            print("\n✅ عمود BRANCH_ID موجود بالفعل!")
            return True
        
        print("\n🔧 إضافة عمود BRANCH_ID...")
        
        # إضافة عمود رقم الفرع
        add_column_query = """
        ALTER TABLE delivery_orders 
        ADD (branch_id NUMBER(10) REFERENCES branches(BRN_NO))
        """
        
        oracle_mgr.execute_update(add_column_query)
        print("✅ تم إضافة عمود BRANCH_ID بنجاح!")
        
        # إضافة تعليق على العمود
        comment_query = """
        COMMENT ON COLUMN delivery_orders.branch_id IS 'رقم الفرع المسؤول عن أمر التسليم'
        """
        
        oracle_mgr.execute_update(comment_query)
        print("✅ تم إضافة تعليق على العمود!")
        
        # إضافة فهرس على العمود
        index_query = """
        CREATE INDEX idx_delivery_orders_branch_id 
        ON delivery_orders(branch_id)
        """
        
        try:
            oracle_mgr.execute_update(index_query)
            print("✅ تم إضافة فهرس على العمود!")
        except Exception as e:
            print(f"⚠️ تحذير: لم يتم إضافة الفهرس: {e}")
        
        # التحقق من النتيجة
        print("\n🔍 التحقق من النتيجة...")
        verify_query = """
        SELECT column_name, data_type, data_length, nullable
        FROM user_tab_columns 
        WHERE table_name = 'DELIVERY_ORDERS' AND column_name = 'BRANCH_ID'
        """
        
        result = oracle_mgr.execute_query(verify_query)
        if result:
            col = result[0]
            print(f"✅ تم التحقق: {col[0]}: {col[1]}({col[2]}) - Nullable: {col[3]}")
        else:
            print("❌ فشل في التحقق من إضافة العمود")
            return False
        
        print("\n🎉 تم إضافة عمود رقم الفرع بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة العمود: {e}")
        return False
    finally:
        oracle_mgr.disconnect()

if __name__ == "__main__":
    print("=" * 60)
    print("🏢 إضافة عمود رقم الفرع إلى جدول أوامر التسليم")
    print("=" * 60)
    
    success = add_branch_column()
    
    if success:
        print("\n✅ تمت العملية بنجاح!")
        print("📝 يمكنك الآن استخدام حقل رقم الفرع في أوامر التسليم")
    else:
        print("\n❌ فشلت العملية!")
    
    print("=" * 60)
