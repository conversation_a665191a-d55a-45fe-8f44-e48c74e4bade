#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مباشر لأمر الشراء PO-2025-0012
"""

import sys
sys.path.append('.')
from oracle_manager import OracleManager

def direct_fix():
    """إصلاح مباشر"""
    print('🔧 تحديث مباشر لأمر الشراء PO-2025-0012...')

    try:
        oracle = OracleManager()
        
        # تحديث مباشر لأمر الشراء PO-2025-0012
        update_query = """
            UPDATE PURCHASE_ORDERS
            SET STATUS = 'قيد التنفيذ',
                IS_USED = 1,
                UPDATED_AT = SYSDATE
            WHERE PO_NUMBER = 'PO-2025-0012'
        """
        
        oracle.execute_update(update_query)
        print('✅ تم تحديث PO-2025-0012 مباشرة إلى "قيد التنفيذ"')
        
        # إضافة سجل في التاريخ
        history_insert = """
            INSERT INTO po_status_history (
                id, po_id, old_status, new_status, 
                shipment_id, shipment_status, change_reason, auto_updated
            ) VALUES (
                po_hist_seq.NEXTVAL, 
                (SELECT ID FROM PURCHASE_ORDERS WHERE PO_NUMBER = 'PO-2025-0012'),
                'مسودة', 'قيد التنفيذ',
                (SELECT id FROM cargo_shipments WHERE tracking_number = 'COSU754360621'),
                'arrived_port', 'تحديث يدوي مباشر', 0
            )
        """
        
        oracle.execute_update(history_insert)
        print('✅ تم إضافة سجل في التاريخ')
        
        # التحقق من النتيجة
        check_query = """
            SELECT PO_NUMBER, STATUS, IS_USED
            FROM PURCHASE_ORDERS
            WHERE PO_NUMBER = 'PO-2025-0012'
        """
        
        result = oracle.execute_query(check_query)
        if result:
            po_number, status, is_used = result[0]
            print(f'📊 النتيجة: {po_number} = {status} (مستخدم: {is_used})')
        
        # فحص نهائي لجميع الشحنات
        print('\n📋 فحص نهائي لجميع الشحنات:')
        final_check = """
            SELECT 
                cs.tracking_number,
                ssc.status_name_ar,
                po.PO_NUMBER,
                po.STATUS
            FROM cargo_shipments cs
            LEFT JOIN shipment_status_config ssc ON cs.shipment_status = ssc.status_code
            LEFT JOIN PURCHASE_ORDERS po ON cs.purchase_order_id = po.ID
            ORDER BY cs.status_updated_at DESC
        """
        
        results = oracle.execute_query(final_check)
        for result in results:
            tracking, status_ar, po_number, po_status = result
            print(f'   📦 {tracking}: {status_ar} | {po_number}: {po_status}')
        
        oracle.close()
        print('\n🎉 تم الإصلاح المباشر بنجاح!')
        
    except Exception as e:
        print(f'❌ خطأ: {e}')

if __name__ == '__main__':
    direct_fix()
