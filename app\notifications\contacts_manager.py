"""
مدير جهات الاتصال للإشعارات
Notification Contacts Manager
"""

import json
import logging
from typing import List, Dict, Optional, Any
from datetime import datetime
from database_manager import DatabaseManager

logger = logging.getLogger(__name__)

class NotificationContactsManager:
    """مدير جهات الاتصال للإشعارات"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def get_all_contacts(self, contact_type: str = None, is_active: bool = True) -> List[Dict]:
        """جلب جميع جهات الاتصال"""
        try:
            base_query = """
                SELECT
                    id, contact_name, contact_type, phone_number, email_address,
                    whatsapp_number, company_name, department, position,
                    preferred_channels, is_vip, priority_level, is_active,
                    created_at, notes
                FROM notif_contacts
                WHERE 1=1
            """
            
            params = {}
            conditions = []
            
            if contact_type:
                conditions.append("contact_type = :contact_type")
                params['contact_type'] = contact_type
            
            if is_active is not None:
                conditions.append("is_active = :is_active")
                params['is_active'] = 1 if is_active else 0
            
            if conditions:
                base_query += " AND " + " AND ".join(conditions)
            
            base_query += " ORDER BY priority_level DESC, contact_name"
            
            results = self.db_manager.execute_query(base_query, params)
            
            contacts = []
            for row in results:
                # معالجة حقول CLOB
                notes = row[14]
                if hasattr(notes, 'read'):  # إذا كان LOB object
                    notes = notes.read() if notes else None

                contact = {
                    'id': row[0],
                    'contact_name': row[1],
                    'contact_type': row[2],
                    'phone_number': row[3],
                    'email_address': row[4],
                    'whatsapp_number': row[5],
                    'company_name': row[6],
                    'department': row[7],
                    'position': row[8],
                    'preferred_channels': row[9],
                    'is_vip': bool(row[10]),
                    'priority_level': row[11] if row[11] is not None else 5,
                    'is_active': bool(row[12]),
                    'created_at': row[13],
                    'notes': notes
                }
                contacts.append(contact)
            
            return contacts
            
        except Exception as e:
            logger.error(f"Error getting contacts: {e}")
            return []
    
    def get_contact_by_id(self, contact_id: int) -> Optional[Dict]:
        """جلب جهة اتصال بالمعرف"""
        try:
            query = """
                SELECT
                    id, contact_name, contact_type, phone_number, email_address,
                    whatsapp_number, company_name, department, position,
                    preferred_channels, notif_preferences, timezone,
                    language_pref, is_vip, priority_level, is_active,
                    created_at, notes
                FROM notif_contacts
                WHERE id = :contact_id
            """
            
            results = self.db_manager.execute_query(query, {'contact_id': contact_id})
            
            if results:
                row = results[0]

                # معالجة حقول CLOB
                notes = row[17]
                if hasattr(notes, 'read'):  # إذا كان LOB object
                    notes = notes.read() if notes else None

                notification_preferences = row[10]
                if hasattr(notification_preferences, 'read'):  # إذا كان LOB object
                    notification_preferences = notification_preferences.read() if notification_preferences else '{}'

                return {
                    'id': row[0],
                    'contact_name': row[1],
                    'contact_type': row[2],
                    'phone_number': row[3],
                    'email_address': row[4],
                    'whatsapp_number': row[5],
                    'company_name': row[6],
                    'department': row[7],
                    'position': row[8],
                    'preferred_channels': row[9],
                    'notification_preferences': notification_preferences,
                    'timezone': row[11],
                    'language_preference': row[12],
                    'is_vip': bool(row[13]),
                    'priority_level': row[14] if row[14] is not None else 5,
                    'is_active': bool(row[15]),
                    'created_at': row[16],
                    'notes': notes
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting contact {contact_id}: {e}")
            return None
    
    def create_contact(self, contact_data: Dict) -> bool:
        """إنشاء جهة اتصال جديدة"""
        try:
            # معالجة خاصة لحقول CLOB
            notes = contact_data.get('notes')
            if notes == '' or notes is None:
                notes = None  # Oracle يفضل NULL بدلاً من string فارغ للCLOB

            notification_preferences = contact_data.get('notification_preferences', {})
            if isinstance(notification_preferences, dict):
                notification_preferences = json.dumps(notification_preferences)

            query = """
                INSERT INTO notif_contacts (
                    id, contact_name, contact_type, phone_number, email_address,
                    whatsapp_number, company_name, department, position,
                    preferred_channels, notif_preferences, timezone,
                    language_pref, is_vip, priority_level, notes,
                    created_at, created_by
                ) VALUES (
                    notif_contacts_seq.NEXTVAL, :contact_name, :contact_type,
                    :phone_number, :email_address, :whatsapp_number, :company_name,
                    :department, :position, :preferred_channels, :notification_preferences,
                    :timezone, :language_preference, :is_vip, :priority_level, :notes,
                    SYSDATE, :created_by
                )
            """

            params = {
                'contact_name': contact_data.get('contact_name'),
                'contact_type': contact_data.get('contact_type', 'EXTERNAL'),
                'phone_number': contact_data.get('phone_number'),
                'email_address': contact_data.get('email_address'),
                'whatsapp_number': contact_data.get('whatsapp_number'),
                'company_name': contact_data.get('company_name'),
                'department': contact_data.get('department'),
                'position': contact_data.get('position'),
                'preferred_channels': contact_data.get('preferred_channels', 'SMS,EMAIL'),
                'notification_preferences': notification_preferences,
                'timezone': contact_data.get('timezone', 'Asia/Riyadh'),
                'language_preference': contact_data.get('language_preference', 'ar'),
                'is_vip': 1 if contact_data.get('is_vip', False) else 0,
                'priority_level': contact_data.get('priority_level', 5),
                'notes': notes,
                'created_by': contact_data.get('created_by', 1)
            }

            self.db_manager.execute_update(query, params)
            return True

        except Exception as e:
            logger.error(f"Error creating contact: {e}")
            return False
    
    def update_contact(self, contact_id: int, contact_data: Dict) -> bool:
        """تحديث جهة اتصال"""
        try:
            # معالجة خاصة لحقول CLOB
            notes = contact_data.get('notes')
            if notes == '' or notes is None:
                notes = None  # Oracle يفضل NULL بدلاً من string فارغ للCLOB

            notification_preferences = contact_data.get('notification_preferences', {})
            if isinstance(notification_preferences, dict):
                notification_preferences = json.dumps(notification_preferences)

            query = """
                UPDATE notif_contacts SET
                    contact_name = :contact_name,
                    contact_type = :contact_type,
                    phone_number = :phone_number,
                    email_address = :email_address,
                    whatsapp_number = :whatsapp_number,
                    company_name = :company_name,
                    department = :department,
                    position = :position,
                    preferred_channels = :preferred_channels,
                    notif_preferences = :notification_preferences,
                    timezone = :timezone,
                    language_pref = :language_preference,
                    is_vip = :is_vip,
                    priority_level = :priority_level,
                    notes = :notes,
                    updated_at = SYSDATE,
                    updated_by = :updated_by
                WHERE id = :contact_id
            """

            params = {
                'contact_id': contact_id,
                'contact_name': contact_data.get('contact_name'),
                'contact_type': contact_data.get('contact_type'),
                'phone_number': contact_data.get('phone_number'),
                'email_address': contact_data.get('email_address'),
                'whatsapp_number': contact_data.get('whatsapp_number'),
                'company_name': contact_data.get('company_name'),
                'department': contact_data.get('department'),
                'position': contact_data.get('position'),
                'preferred_channels': contact_data.get('preferred_channels'),
                'notification_preferences': notification_preferences,
                'timezone': contact_data.get('timezone', 'Asia/Riyadh'),
                'language_preference': contact_data.get('language_preference', 'ar'),
                'is_vip': 1 if contact_data.get('is_vip', False) else 0,
                'priority_level': contact_data.get('priority_level', 5),
                'notes': notes,
                'updated_by': contact_data.get('updated_by', 1)
            }

            self.db_manager.execute_update(query, params)
            return True

        except Exception as e:
            logger.error(f"Error updating contact {contact_id}: {e}")
            return False
    
    def delete_contact(self, contact_id: int) -> bool:
        """حذف جهة اتصال"""
        try:
            # حذف من المجموعات أولاً
            delete_groups_query = """
                DELETE FROM notif_contact_members
                WHERE contact_id = :contact_id
            """
            self.db_manager.execute_update(delete_groups_query, {'contact_id': contact_id})

            # حذف جهة الاتصال
            delete_query = """
                DELETE FROM notif_contacts
                WHERE id = :contact_id
            """
            self.db_manager.execute_update(delete_query, {'contact_id': contact_id})
            
            return True
            
        except Exception as e:
            logger.error(f"Error deleting contact {contact_id}: {e}")
            return False
    
    def get_contacts_by_type(self, contact_type: str) -> List[Dict]:
        """جلب جهات الاتصال حسب النوع"""
        return self.get_all_contacts(contact_type=contact_type)
    
    def get_vip_contacts(self) -> List[Dict]:
        """جلب جهات الاتصال المميزة"""
        try:
            query = """
                SELECT 
                    id, contact_name, contact_type, phone_number, email_address,
                    whatsapp_number, preferred_channels, priority_level
                FROM notif_contacts
                WHERE is_vip = 1 AND is_active = 1
                ORDER BY priority_level DESC, contact_name
            """
            
            results = self.db_manager.execute_query(query)
            
            contacts = []
            for row in results:
                contact = {
                    'id': row[0],
                    'contact_name': row[1],
                    'contact_type': row[2],
                    'phone_number': row[3],
                    'email_address': row[4],
                    'whatsapp_number': row[5],
                    'preferred_channels': row[6],
                    'priority_level': row[7] if row[7] is not None else 5
                }
                contacts.append(contact)
            
            return contacts
            
        except Exception as e:
            logger.error(f"Error getting VIP contacts: {e}")
            return []
    
    def search_contacts(self, search_term: str) -> List[Dict]:
        """البحث في جهات الاتصال"""
        try:
            query = """
                SELECT 
                    id, contact_name, contact_type, phone_number, email_address,
                    whatsapp_number, company_name, preferred_channels, priority_level
                FROM notif_contacts
                WHERE is_active = 1 AND (
                    UPPER(contact_name) LIKE UPPER(:search_term) OR
                    UPPER(phone_number) LIKE UPPER(:search_term) OR
                    UPPER(email_address) LIKE UPPER(:search_term) OR
                    UPPER(company_name) LIKE UPPER(:search_term)
                )
                ORDER BY priority_level DESC, contact_name
            """
            
            search_pattern = f"%{search_term}%"
            results = self.db_manager.execute_query(query, {'search_term': search_pattern})
            
            contacts = []
            for row in results:
                contact = {
                    'id': row[0],
                    'contact_name': row[1],
                    'contact_type': row[2],
                    'phone_number': row[3],
                    'email_address': row[4],
                    'whatsapp_number': row[5],
                    'company_name': row[6],
                    'preferred_channels': row[7],
                    'priority_level': row[8] if row[8] is not None else 5
                }
                contacts.append(contact)
            
            return contacts
            
        except Exception as e:
            logger.error(f"Error searching contacts: {e}")
            return []
    
    def get_contact_statistics(self) -> Dict:
        """جلب إحصائيات جهات الاتصال"""
        try:
            stats_query = """
                SELECT 
                    COUNT(*) as total_contacts,
                    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_contacts,
                    COUNT(CASE WHEN is_vip = 1 THEN 1 END) as vip_contacts,
                    COUNT(CASE WHEN contact_type = 'CUSTOMER' THEN 1 END) as customers,
                    COUNT(CASE WHEN contact_type = 'DRIVER' THEN 1 END) as drivers,
                    COUNT(CASE WHEN contact_type = 'AGENT' THEN 1 END) as agents,
                    COUNT(CASE WHEN contact_type = 'MANAGER' THEN 1 END) as managers
                FROM notif_contacts
            """
            
            results = self.db_manager.execute_query(stats_query)
            
            if results:
                row = results[0]
                return {
                    'total_contacts': row[0] or 0,
                    'active_contacts': row[1] or 0,
                    'vip_contacts': row[2] or 0,
                    'customers': row[3] or 0,
                    'drivers': row[4] or 0,
                    'agents': row[5] or 0,
                    'managers': row[6] or 0
                }
            
            return {}
            
        except Exception as e:
            logger.error(f"Error getting contact statistics: {e}")
            return {}
