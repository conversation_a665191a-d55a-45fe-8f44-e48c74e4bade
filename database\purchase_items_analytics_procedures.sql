-- =====================================================
-- إجراءات مخزنة لتحليل أصناف أوامر الشراء
-- Stored Procedures for Purchase Order Items Analytics
-- التاريخ: 2025-01-03
-- =====================================================

-- 1. إجراء حساب تحليل ABC للأصناف
CREATE OR REPLACE PROCEDURE SP_CALCULATE_ABC_ANALYSIS(
    p_period_months IN NUMBER DEFAULT 12,
    p_result OUT SYS_REFCURSOR
) AS
BEGIN
    OPEN p_result FOR
    WITH item_totals AS (
        SELECT 
            poi.ITEM_CODE,
            poi.ITEM_NAME,
            SUM(poi.TOTAL_PRICE) as total_value,
            SUM(poi.QUANTITY) as total_quantity,
            COUNT(*) as order_count,
            AVG(poi.UNIT_PRICE) as avg_price,
            COUNT(DISTINCT po.SUPPLIER_NAME) as supplier_count
        FROM PO_ITEMS poi
        JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
        WHERE po.STATUS != 'ملغي'
        AND po.PO_DATE >= ADD_MONTHS(SYSDATE, -p_period_months)
        GROUP BY poi.ITEM_CODE, poi.ITEM_NAME
    ),
    ranked_items AS (
        SELECT 
            *,
            SUM(total_value) OVER() as grand_total,
            SUM(total_value) OVER(ORDER BY total_value DESC ROWS UNBOUNDED PRECEDING) as cumulative_value,
            ROW_NUMBER() OVER(ORDER BY total_value DESC) as value_rank
        FROM item_totals
    )
    SELECT 
        ITEM_CODE,
        ITEM_NAME,
        total_value,
        total_quantity,
        order_count,
        supplier_count,
        ROUND(avg_price, 2) as avg_price,
        value_rank,
        ROUND(total_value / grand_total * 100, 2) as value_percentage,
        ROUND(cumulative_value / grand_total * 100, 2) as cumulative_percentage,
        CASE 
            WHEN cumulative_value / grand_total <= 0.8 THEN 'A'
            WHEN cumulative_value / grand_total <= 0.95 THEN 'B'
            ELSE 'C'
        END as abc_classification,
        CASE 
            WHEN cumulative_value / grand_total <= 0.8 THEN 'عالي القيمة - يحتاج مراقبة دقيقة'
            WHEN cumulative_value / grand_total <= 0.95 THEN 'متوسط القيمة - مراقبة عادية'
            ELSE 'منخفض القيمة - مراقبة أساسية'
        END as abc_description
    FROM ranked_items
    ORDER BY total_value DESC;
END;
/

-- 2. إجراء تحليل اتجاهات الأصناف
CREATE OR REPLACE PROCEDURE SP_ANALYZE_ITEM_TRENDS(
    p_item_code IN VARCHAR2 DEFAULT NULL,
    p_months_back IN NUMBER DEFAULT 12,
    p_result OUT SYS_REFCURSOR
) AS
BEGIN
    OPEN p_result FOR
    WITH monthly_data AS (
        SELECT 
            poi.ITEM_CODE,
            poi.ITEM_NAME,
            TO_CHAR(po.PO_DATE, 'YYYY-MM') as order_month,
            SUM(poi.TOTAL_PRICE) as monthly_value,
            SUM(poi.QUANTITY) as monthly_quantity,
            AVG(poi.UNIT_PRICE) as monthly_avg_price,
            COUNT(*) as monthly_orders
        FROM PO_ITEMS poi
        JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
        WHERE po.STATUS != 'ملغي'
        AND po.PO_DATE >= ADD_MONTHS(SYSDATE, -p_months_back)
        AND (p_item_code IS NULL OR poi.ITEM_CODE = p_item_code)
        GROUP BY poi.ITEM_CODE, poi.ITEM_NAME, TO_CHAR(po.PO_DATE, 'YYYY-MM')
    ),
    trend_analysis AS (
        SELECT 
            *,
            LAG(monthly_value) OVER (PARTITION BY ITEM_CODE ORDER BY order_month) as prev_month_value,
            LAG(monthly_avg_price) OVER (PARTITION BY ITEM_CODE ORDER BY order_month) as prev_month_price,
            AVG(monthly_value) OVER (PARTITION BY ITEM_CODE ORDER BY order_month ROWS 2 PRECEDING) as three_month_avg,
            STDDEV(monthly_value) OVER (PARTITION BY ITEM_CODE ORDER BY order_month ROWS 5 PRECEDING) as volatility
        FROM monthly_data
    )
    SELECT 
        ITEM_CODE,
        ITEM_NAME,
        order_month,
        monthly_value,
        monthly_quantity,
        ROUND(monthly_avg_price, 2) as monthly_avg_price,
        monthly_orders,
        ROUND(three_month_avg, 2) as three_month_avg,
        ROUND(volatility, 2) as volatility,
        CASE 
            WHEN prev_month_value IS NULL THEN 'N/A'
            WHEN monthly_value > prev_month_value * 1.1 THEN 'صاعد'
            WHEN monthly_value < prev_month_value * 0.9 THEN 'هابط'
            ELSE 'مستقر'
        END as trend_direction,
        CASE 
            WHEN prev_month_value IS NOT NULL THEN 
                ROUND((monthly_value - prev_month_value) / prev_month_value * 100, 2)
            ELSE NULL
        END as change_percentage
    FROM trend_analysis
    ORDER BY ITEM_CODE, order_month;
END;
/

-- 3. إجراء مقارنة أسعار الموردين
CREATE OR REPLACE PROCEDURE SP_COMPARE_SUPPLIER_PRICES(
    p_item_code IN VARCHAR2 DEFAULT NULL,
    p_months_back IN NUMBER DEFAULT 6,
    p_result OUT SYS_REFCURSOR
) AS
BEGIN
    OPEN p_result FOR
    WITH supplier_prices AS (
        SELECT 
            poi.ITEM_CODE,
            poi.ITEM_NAME,
            po.SUPPLIER_NAME,
            AVG(poi.UNIT_PRICE) as avg_price,
            MIN(poi.UNIT_PRICE) as min_price,
            MAX(poi.UNIT_PRICE) as max_price,
            SUM(poi.TOTAL_PRICE) as total_value,
            SUM(poi.QUANTITY) as total_quantity,
            COUNT(*) as order_count,
            MAX(po.PO_DATE) as last_order_date,
            AVG(po.DELIVERY_DATE - po.PO_DATE) as avg_delivery_days
        FROM PO_ITEMS poi
        JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
        WHERE po.STATUS != 'ملغي'
        AND po.PO_DATE >= ADD_MONTHS(SYSDATE, -p_months_back)
        AND (p_item_code IS NULL OR poi.ITEM_CODE = p_item_code)
        GROUP BY poi.ITEM_CODE, poi.ITEM_NAME, po.SUPPLIER_NAME
    ),
    price_ranking AS (
        SELECT 
            *,
            MIN(avg_price) OVER (PARTITION BY ITEM_CODE) as best_price,
            RANK() OVER (PARTITION BY ITEM_CODE ORDER BY avg_price) as price_rank,
            COUNT(*) OVER (PARTITION BY ITEM_CODE) as supplier_count
        FROM supplier_prices
    )
    SELECT 
        ITEM_CODE,
        ITEM_NAME,
        SUPPLIER_NAME,
        ROUND(avg_price, 2) as avg_price,
        ROUND(min_price, 2) as min_price,
        ROUND(max_price, 2) as max_price,
        ROUND(total_value, 2) as total_value,
        total_quantity,
        order_count,
        last_order_date,
        ROUND(avg_delivery_days, 1) as avg_delivery_days,
        price_rank,
        supplier_count,
        ROUND(best_price, 2) as best_price,
        ROUND(avg_price - best_price, 2) as price_difference,
        ROUND((avg_price - best_price) / best_price * 100, 2) as price_difference_percentage,
        CASE 
            WHEN price_rank = 1 THEN 'أفضل سعر'
            WHEN price_rank <= supplier_count * 0.3 THEN 'سعر جيد'
            WHEN price_rank <= supplier_count * 0.7 THEN 'سعر متوسط'
            ELSE 'سعر مرتفع'
        END as price_category
    FROM price_ranking
    ORDER BY ITEM_CODE, price_rank;
END;
/

-- 4. إجراء تحليل الموسمية
CREATE OR REPLACE PROCEDURE SP_ANALYZE_SEASONALITY(
    p_item_code IN VARCHAR2 DEFAULT NULL,
    p_years_back IN NUMBER DEFAULT 2,
    p_result OUT SYS_REFCURSOR
) AS
BEGIN
    OPEN p_result FOR
    WITH seasonal_data AS (
        SELECT 
            poi.ITEM_CODE,
            poi.ITEM_NAME,
            EXTRACT(MONTH FROM po.PO_DATE) as order_month,
            TO_CHAR(po.PO_DATE, 'Month', 'NLS_DATE_LANGUAGE=ARABIC') as month_name,
            EXTRACT(QUARTER FROM po.PO_DATE) as order_quarter,
            SUM(poi.TOTAL_PRICE) as total_value,
            SUM(poi.QUANTITY) as total_quantity,
            COUNT(*) as order_count,
            AVG(poi.UNIT_PRICE) as avg_price
        FROM PO_ITEMS poi
        JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
        WHERE po.STATUS != 'ملغي'
        AND po.PO_DATE >= ADD_MONTHS(SYSDATE, -p_years_back * 12)
        AND (p_item_code IS NULL OR poi.ITEM_CODE = p_item_code)
        GROUP BY poi.ITEM_CODE, poi.ITEM_NAME, EXTRACT(MONTH FROM po.PO_DATE), 
                 TO_CHAR(po.PO_DATE, 'Month', 'NLS_DATE_LANGUAGE=ARABIC'), 
                 EXTRACT(QUARTER FROM po.PO_DATE)
    ),
    seasonal_stats AS (
        SELECT 
            *,
            AVG(total_value) OVER (PARTITION BY ITEM_CODE) as yearly_avg_value,
            MAX(total_value) OVER (PARTITION BY ITEM_CODE) as max_month_value,
            MIN(total_value) OVER (PARTITION BY ITEM_CODE) as min_month_value
        FROM seasonal_data
    )
    SELECT 
        ITEM_CODE,
        ITEM_NAME,
        order_month,
        TRIM(month_name) as month_name,
        order_quarter,
        ROUND(total_value, 2) as total_value,
        total_quantity,
        order_count,
        ROUND(avg_price, 2) as avg_price,
        ROUND(yearly_avg_value, 2) as yearly_avg_value,
        ROUND(total_value / yearly_avg_value * 100, 2) as seasonal_index,
        CASE 
            WHEN total_value >= yearly_avg_value * 1.2 THEN 'موسم ذروة'
            WHEN total_value >= yearly_avg_value * 0.8 THEN 'موسم عادي'
            ELSE 'موسم منخفض'
        END as seasonal_category,
        RANK() OVER (PARTITION BY ITEM_CODE ORDER BY total_value DESC) as month_rank
    FROM seasonal_stats
    ORDER BY ITEM_CODE, order_month;
END;
/

-- 5. إجراء تحليل أداء الفئات
CREATE OR REPLACE PROCEDURE SP_ANALYZE_CATEGORY_PERFORMANCE(
    p_period_months IN NUMBER DEFAULT 12,
    p_result OUT SYS_REFCURSOR
) AS
BEGIN
    OPEN p_result FOR
    WITH category_performance AS (
        SELECT 
            COALESCE(ic.CATEGORY_NAME_AR, 'غير محدد') as category_name,
            COALESCE(ic.CATEGORY_CODE, 'UNKNOWN') as category_code,
            COUNT(DISTINCT poi.ITEM_CODE) as unique_items,
            COUNT(*) as total_orders,
            SUM(poi.QUANTITY) as total_quantity,
            SUM(poi.TOTAL_PRICE) as total_value,
            AVG(poi.UNIT_PRICE) as avg_unit_price,
            COUNT(DISTINCT po.SUPPLIER_NAME) as supplier_count,
            AVG(po.DELIVERY_DATE - po.PO_DATE) as avg_delivery_days
        FROM PO_ITEMS poi
        JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
        LEFT JOIN ITEM_CATEGORIES ic ON SUBSTR(poi.ITEM_CODE, 1, 3) = ic.CATEGORY_CODE -- ربط تقريبي
        WHERE po.STATUS != 'ملغي'
        AND po.PO_DATE >= ADD_MONTHS(SYSDATE, -p_period_months)
        GROUP BY ic.CATEGORY_NAME_AR, ic.CATEGORY_CODE
    ),
    category_stats AS (
        SELECT 
            *,
            SUM(total_value) OVER() as grand_total_value,
            RANK() OVER (ORDER BY total_value DESC) as value_rank
        FROM category_performance
    )
    SELECT 
        category_name,
        category_code,
        unique_items,
        total_orders,
        total_quantity,
        ROUND(total_value, 2) as total_value,
        ROUND(avg_unit_price, 2) as avg_unit_price,
        supplier_count,
        ROUND(avg_delivery_days, 1) as avg_delivery_days,
        value_rank,
        ROUND(total_value / grand_total_value * 100, 2) as value_percentage,
        CASE 
            WHEN value_rank <= 3 THEN 'فئة رئيسية'
            WHEN value_rank <= 6 THEN 'فئة مهمة'
            ELSE 'فئة ثانوية'
        END as category_importance
    FROM category_stats
    ORDER BY total_value DESC;
END;
/

-- 6. إجراء إنشاء تقرير شامل
CREATE OR REPLACE PROCEDURE SP_GENERATE_COMPREHENSIVE_REPORT(
    p_period_months IN NUMBER DEFAULT 12,
    p_summary_cursor OUT SYS_REFCURSOR,
    p_abc_cursor OUT SYS_REFCURSOR,
    p_trends_cursor OUT SYS_REFCURSOR,
    p_categories_cursor OUT SYS_REFCURSOR
) AS
BEGIN
    -- ملخص عام
    OPEN p_summary_cursor FOR
    SELECT 
        COUNT(DISTINCT poi.ITEM_CODE) as total_items,
        COUNT(*) as total_orders,
        SUM(poi.TOTAL_PRICE) as total_value,
        AVG(poi.UNIT_PRICE) as avg_unit_price,
        COUNT(DISTINCT po.SUPPLIER_NAME) as total_suppliers,
        COUNT(DISTINCT ic.CATEGORY_CODE) as total_categories,
        SYSDATE as report_date
    FROM PO_ITEMS poi
    JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
    LEFT JOIN ITEM_CATEGORIES ic ON SUBSTR(poi.ITEM_CODE, 1, 3) = ic.CATEGORY_CODE
    WHERE po.STATUS != 'ملغي'
    AND po.PO_DATE >= ADD_MONTHS(SYSDATE, -p_period_months);
    
    -- تحليل ABC
    SP_CALCULATE_ABC_ANALYSIS(p_period_months, p_abc_cursor);
    
    -- الاتجاهات
    SP_ANALYZE_ITEM_TRENDS(NULL, p_period_months, p_trends_cursor);
    
    -- أداء الفئات
    SP_ANALYZE_CATEGORY_PERFORMANCE(p_period_months, p_categories_cursor);
END;
/

-- 7. إجراء تحديث إحصائيات الأداء
CREATE OR REPLACE PROCEDURE SP_UPDATE_PERFORMANCE_STATS AS
BEGIN
    -- حذف الإحصائيات القديمة
    DELETE FROM ITEM_PERFORMANCE_ANALYTICS 
    WHERE PERIOD_END_DATE < ADD_MONTHS(SYSDATE, -13);
    
    -- إدراج إحصائيات جديدة للشهر الحالي
    INSERT INTO ITEM_PERFORMANCE_ANALYTICS (
        ITEM_CODE, ITEM_NAME, ANALYSIS_PERIOD, PERIOD_START_DATE, PERIOD_END_DATE,
        TOTAL_QUANTITY, TOTAL_ORDERS, AVG_QUANTITY_PER_ORDER, TOTAL_VALUE, AVG_UNIT_PRICE,
        SUPPLIER_COUNT, CALCULATED_BY
    )
    SELECT 
        poi.ITEM_CODE,
        poi.ITEM_NAME,
        'MONTHLY',
        TRUNC(SYSDATE, 'MM'),
        LAST_DAY(SYSDATE),
        SUM(poi.QUANTITY),
        COUNT(*),
        AVG(poi.QUANTITY),
        SUM(poi.TOTAL_PRICE),
        AVG(poi.UNIT_PRICE),
        COUNT(DISTINCT po.SUPPLIER_NAME),
        USER
    FROM PO_ITEMS poi
    JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
    WHERE po.STATUS != 'ملغي'
    AND po.PO_DATE >= TRUNC(SYSDATE, 'MM')
    AND po.PO_DATE <= LAST_DAY(SYSDATE)
    GROUP BY poi.ITEM_CODE, poi.ITEM_NAME;
    
    COMMIT;
END;
/

-- 8. إنشاء job لتحديث الإحصائيات تلقائياً
BEGIN
    DBMS_SCHEDULER.CREATE_JOB (
        job_name        => 'UPDATE_ITEM_PERFORMANCE_STATS',
        job_type        => 'PLSQL_BLOCK',
        job_action      => 'BEGIN SP_UPDATE_PERFORMANCE_STATS; END;',
        start_date      => SYSTIMESTAMP,
        repeat_interval => 'FREQ=DAILY; BYHOUR=2; BYMINUTE=0',
        enabled         => TRUE,
        comments        => 'تحديث إحصائيات أداء الأصناف يومياً'
    );
END;
/

PROMPT 'تم إنشاء الإجراءات المخزنة لتحليل أصناف أوامر الشراء بنجاح!';
