# -*- coding: utf-8 -*-
"""
خدمة PDF بسيطة تستخدم صفحة المعاينة الموجودة
Simple PDF Service using existing viewer page
"""

import os
import sys
import subprocess
import time
import requests
from datetime import datetime

class SimpleViewerPDF:
    """خدمة PDF بسيطة تستخدم صفحة المعاينة"""
    
    def __init__(self):
        """تهيئة الخدمة"""
        self.output_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'pdf')
        os.makedirs(self.output_dir, exist_ok=True)
        self.base_url = 'http://127.0.0.1:5000'
    
    def generate_pdf_from_viewer(self, delivery_order_id):
        """إنشاء PDF من صفحة المعاينة"""
        try:
            # إنشاء اسم الملف
            filename = f"delivery_order_viewer_{delivery_order_id}.pdf"
            filepath = os.path.join(self.output_dir, filename)
            
            # URL صفحة المعاينة
            viewer_url = f"{self.base_url}/shipments/delivery-order-viewer/{delivery_order_id}"
            
            print(f"🌐 استخدام صفحة المعاينة: {viewer_url}")
            
            # الطريقة 1: Chrome headless (الأفضل)
            if self._try_chrome_print(viewer_url, filepath):
                return filepath, "تم إنشاء PDF من صفحة المعاينة بنجاح"
            
            # الطريقة 2: wkhtmltopdf
            if self._try_wkhtmltopdf(viewer_url, filepath):
                return filepath, "تم إنشاء PDF باستخدام wkhtmltopdf"
            
            # الطريقة 3: حفظ HTML كملف مرجعي
            if self._save_html_reference(viewer_url, filepath, delivery_order_id):
                return filepath, "تم حفظ مرجع HTML للتحويل اليدوي"
            
            return None, "فشل في إنشاء PDF من صفحة المعاينة"
            
        except Exception as e:
            return None, f"خطأ في إنشاء PDF: {str(e)}"
    
    def _try_chrome_print(self, url, output_path):
        """محاولة استخدام Chrome للطباعة"""
        try:
            # البحث عن Chrome في مسارات مختلفة
            chrome_paths = [
                r'C:\Program Files\Google\Chrome\Application\chrome.exe',
                r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe',
                r'C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe'.format(os.getenv('USERNAME', '')),
                'chrome.exe',
                'google-chrome'
            ]
            
            chrome_path = None
            for path in chrome_paths:
                try:
                    if os.path.exists(path):
                        # اختبار تشغيل Chrome
                        result = subprocess.run([path, '--version'], capture_output=True, timeout=5)
                        if result.returncode == 0:
                            chrome_path = path
                            print(f"✅ تم العثور على Chrome: {path}")
                            break
                except:
                    continue
            
            if not chrome_path:
                print("❌ Chrome غير متاح")
                return False
            
            # أمر Chrome لإنشاء PDF مع إعدادات محسنة للصفحات العربية
            cmd = [
                chrome_path,
                '--headless',
                '--disable-gpu',
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--run-all-compositor-stages-before-draw',
                '--virtual-time-budget=15000',  # انتظار 15 ثانية للتحميل الكامل
                '--print-to-pdf=' + output_path,
                '--print-to-pdf-no-header',
                '--window-size=1920,1080',
                url
            ]
            
            print("🖨️ تشغيل Chrome لإنشاء PDF...")
            print(f"📄 الأمر: {' '.join(cmd[:5])}...")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=90)
            
            # فحص النتيجة
            if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:  # على الأقل 1KB
                file_size = os.path.getsize(output_path)
                print(f"✅ تم إنشاء PDF بنجاح! حجم الملف: {file_size} بايت ({file_size/1024:.1f} KB)")
                
                # فحص إضافي للتأكد أنه PDF حقيقي
                with open(output_path, 'rb') as f:
                    header = f.read(4)
                    if header == b'%PDF':
                        print("✅ الملف هو PDF حقيقي!")
                        return True
                    else:
                        print("❌ الملف ليس PDF صحيح!")
                        return False
            else:
                print(f"❌ فشل Chrome في إنشاء PDF")
                if result.stderr:
                    print(f"خطأ Chrome: {result.stderr}")
                if result.stdout:
                    print(f"مخرجات Chrome: {result.stdout}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ انتهت مهلة Chrome")
            return False
        except Exception as e:
            print(f"❌ خطأ في Chrome: {e}")
            return False
    
    def _try_wkhtmltopdf(self, url, output_path):
        """محاولة استخدام wkhtmltopdf"""
        try:
            cmd = [
                'wkhtmltopdf',
                '--page-size', 'A4',
                '--orientation', 'Portrait',
                '--margin-top', '0.75in',
                '--margin-right', '0.75in',
                '--margin-bottom', '0.75in',
                '--margin-left', '0.75in',
                '--encoding', 'UTF-8',
                '--javascript-delay', '5000',
                '--no-stop-slow-scripts',
                '--enable-local-file-access',
                url,
                output_path
            ]
            
            print("🖨️ محاولة استخدام wkhtmltopdf...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0 and os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"✅ تم إنشاء PDF باستخدام wkhtmltopdf! حجم الملف: {file_size} بايت")
                return True
            else:
                print(f"❌ فشل wkhtmltopdf: {result.stderr}")
                return False
                
        except (subprocess.TimeoutExpired, FileNotFoundError, Exception) as e:
            print(f"❌ wkhtmltopdf غير متاح: {e}")
            return False
    
    def _save_html_reference(self, url, output_path, delivery_order_id):
        """حفظ HTML كملف مرجعي مع تعليمات التحويل"""
        try:
            # تحميل HTML
            print("📄 تحميل HTML من صفحة المعاينة...")
            response = requests.get(url, timeout=15)
            if response.status_code != 200:
                print(f"❌ فشل تحميل الصفحة: {response.status_code}")
                return False
            
            # حفظ HTML كملف مؤقت
            html_filename = f"delivery_order_viewer_{delivery_order_id}.html"
            html_path = os.path.join(self.output_dir, html_filename)
            
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            print(f"✅ تم حفظ HTML: {html_path}")
            
            # إنشاء ملف تعليمات
            instructions_path = output_path.replace('.pdf', '_instructions.txt')
            with open(instructions_path, 'w', encoding='utf-8') as f:
                f.write(f"""تعليمات تحويل أمر التسليم إلى PDF:

الطريقة الأولى - استخدام المتصفح:
1. افتح الرابط التالي في المتصفح:
   {url}

2. اضغط على زر "📄 تحميل PDF" في الصفحة
   (الزر يستخدم html2canvas و jsPDF لإنشاء PDF مثالي)

3. سيتم تحميل الملف تلقائياً

الطريقة الثانية - الطباعة:
1. افتح نفس الرابط
2. اضغط Ctrl+P للطباعة
3. اختر "حفظ كـ PDF"

الطريقة الثالثة - الملف المحفوظ:
1. افتح الملف المحفوظ: {html_path}
2. استخدم نفس طرق الطباعة أعلاه

ملاحظة: الطريقة الأولى (زر تحميل PDF) تعطي أفضل النتائج
لأنها تستخدم نفس التقنية المصممة خصيصاً للصفحة.

تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
""")
            
            # نسخ الملف HTML كـ PDF مؤقتاً (للإشارة)
            import shutil
            shutil.copy2(html_path, output_path)
            
            print(f"✅ تم إنشاء ملف مرجعي: {instructions_path}")
            print("💡 يرجى استخدام زر 'تحميل PDF' في صفحة المعاينة للحصول على أفضل النتائج")
            
            return True
            
        except Exception as e:
            print(f"❌ فشل في حفظ HTML: {e}")
            return False
    
    def check_tools(self):
        """فحص الأدوات المتاحة"""
        tools = {
            'chrome': False,
            'wkhtmltopdf': False,
            'requests': True  # متاح دائماً
        }
        
        # فحص Chrome
        chrome_paths = [
            r'C:\Program Files\Google\Chrome\Application\chrome.exe',
            r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe',
            'chrome.exe'
        ]
        
        for path in chrome_paths:
            try:
                if os.path.exists(path):
                    result = subprocess.run([path, '--version'], capture_output=True, timeout=5)
                    if result.returncode == 0:
                        tools['chrome'] = True
                        break
            except:
                continue
        
        # فحص wkhtmltopdf
        try:
            result = subprocess.run(['wkhtmltopdf', '--version'], capture_output=True, timeout=5)
            tools['wkhtmltopdf'] = result.returncode == 0
        except:
            pass
        
        return tools


# إنشاء instance عام للخدمة
simple_viewer_pdf = SimpleViewerPDF()


def generate_pdf_from_viewer(delivery_order_id):
    """دالة مساعدة لإنشاء PDF من صفحة المعاينة"""
    return simple_viewer_pdf.generate_pdf_from_viewer(delivery_order_id)


def check_viewer_pdf_tools():
    """فحص أدوات إنشاء PDF المتاحة"""
    return simple_viewer_pdf.check_tools()
