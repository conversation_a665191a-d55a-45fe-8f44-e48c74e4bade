"""
نظام الأرصدة الافتتاحية للموردين
Supplier Opening Balances System
"""

from flask import Blueprint, render_template, request, jsonify, session, flash, redirect, url_for
from oracle_manager import get_oracle_manager
import logging
from datetime import datetime, date
import json
from decimal import Decimal
from werkzeug.utils import secure_filename
import os
# from .excel_templates import OpeningBalancesExcelTemplate

# إنشاء Blueprint
opening_balances_bp = Blueprint('opening_balances', __name__, url_prefix='/suppliers/opening_balances')

# إعداد التسجيل
logger = logging.getLogger(__name__)

class DecimalEncoder(json.JSONEncoder):
    """مشفر JSON للتعامل مع Decimal"""
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super(DecimalEncoder, self).default(obj)

@opening_balances_bp.route('/')
def index():
    """الصفحة الرئيسية لإدارة الأرصدة الافتتاحية"""
    try:
        return render_template('suppliers/opening_balances.html')
    except Exception as e:
        logger.error(f"خطأ في عرض صفحة الأرصدة الافتتاحية: {e}")
        flash('حدث خطأ في تحميل الصفحة', 'error')
        return redirect(url_for('suppliers.index'))

@opening_balances_bp.route('/api/suppliers')
def get_suppliers_for_opening_balances():
    """جلب قائمة الموردين المؤهلين لإدخال أرصدة افتتاحية"""
    try:
        oracle_mgr = get_oracle_manager()
        if not oracle_mgr.connect():
            return jsonify({'success': False, 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500
        
        # جلب الموردين النشطين
        query = """
        SELECT
            s.id,
            s.name_ar as name,
            s.supplier_code as code,
            s.supplier_type,
            s.phone,
            s.email,
            s.is_active,
            -- التحقق من وجود رصيد افتتاحي للفترة الحالية في النظام المركزي
            CASE
                WHEN EXISTS (
                    SELECT 1 FROM OPENING_BALANCES ob
                    WHERE ob.entity_type_code = 'SUPPLIER'
                    AND ob.entity_id = s.supplier_code
                    AND ob.fiscal_period_start_date = DATE '2024-01-01'
                    AND ob.is_active = 1
                ) THEN 1 ELSE 0
            END as has_opening_balance
        FROM SUPPLIERS s
        WHERE s.is_active = 1
        ORDER BY s.name_ar
        """
        
        suppliers = oracle_mgr.execute_query(query)
        oracle_mgr.disconnect()
        
        if suppliers:
            suppliers_list = []
            for supplier in suppliers:
                suppliers_list.append({
                    'id': supplier[0],
                    'name': supplier[1] or 'غير محدد',
                    'code': supplier[2] or f'SUP{supplier[0]:04d}',
                    'type': supplier[3] or 'عام',
                    'phone': supplier[4] or '',
                    'email': supplier[5] or '',
                    'is_active': supplier[6],
                    'has_opening_balance': bool(supplier[7])
                })
            
            return jsonify({
                'success': True,
                'suppliers': suppliers_list,
                'total_count': len(suppliers_list)
            })
        else:
            return jsonify({
                'success': True,
                'suppliers': [],
                'total_count': 0,
                'message': 'لا توجد موردين'
            })
            
    except Exception as e:
        logger.error(f"خطأ في جلب قائمة الموردين: {e}")
        return jsonify({'success': False, 'message': f'خطأ في جلب البيانات: {str(e)}'}), 500

@opening_balances_bp.route('/api/opening_balances')
def get_opening_balances():
    """جلب الأرصدة الافتتاحية الموجودة"""
    try:
        fiscal_date = request.args.get('fiscal_date', '2024-01-01')
        currency_code = request.args.get('currency_code', '')
        status_filter = request.args.get('status', 'all')
        supplier_id = request.args.get('supplier_id', '')
        
        oracle_mgr = get_oracle_manager()
        if not oracle_mgr.connect():
            return jsonify({'success': False, 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500
        
        # بناء الاستعلام للنظام المركزي
        where_conditions = [
            "ob.entity_type_code = 'SUPPLIER'",
            "ob.fiscal_period_start_date = TO_DATE(:fiscal_date, 'YYYY-MM-DD')",
            "ob.is_active = 1"
        ]

        params = {
            'fiscal_date': fiscal_date
        }

        # إضافة فلتر العملة فقط إذا كان محدداً
        if currency_code and currency_code.strip():
            where_conditions.append("ob.currency_code = :currency_code")
            params['currency_code'] = currency_code

        # إضافة فلتر المورد فقط إذا كان محدداً
        if supplier_id and supplier_id.strip():
            where_conditions.append("ob.entity_id = :supplier_id")
            params['supplier_id'] = int(supplier_id)

        if status_filter != 'all':
            where_conditions.append("ob.status = :status")
            params['status'] = status_filter.upper()

        query = f"""
        SELECT
            ob.id,
            ob.entity_id as supplier_id,
            s.name_ar as supplier_name,
            s.supplier_code,
            ob.opening_balance_amount,
            ob.balance_type,
            ob.currency_code,
            ob.exchange_rate,
            ob.base_currency_amount,
            ob.notes,
            ob.reference_document,
            ob.status,
            ob.created_date,
            NULL as reviewed_date,
            ob.approved_date,
            ob.posted_date,
            ob.fiscal_year
        FROM OPENING_BALANCES ob
        JOIN SUPPLIERS s ON ob.entity_id = s.supplier_code
        WHERE {' AND '.join(where_conditions)}
        ORDER BY s.name_ar
        """
        
        balances = oracle_mgr.execute_query(query, params)
        oracle_mgr.disconnect()
        
        if balances:
            balances_list = []
            for balance in balances:
                balances_list.append({
                    'id': balance[0],
                    'supplier_id': balance[1],
                    'supplier_name': balance[2] or 'غير محدد',
                    'supplier_code': balance[3] or f'SUP{balance[1]:04d}',
                    'opening_balance_amount': float(balance[4]) if balance[4] else 0,
                    'balance_type': balance[5],
                    'currency_code': balance[6],
                    'exchange_rate': float(balance[7]) if balance[7] else 1,
                    'base_currency_amount': float(balance[8]) if balance[8] else 0,
                    'notes': balance[9] or '',
                    'reference_document': balance[10] or '',
                    'status': balance[11],
                    'created_date': balance[12].isoformat() if balance[12] else None,
                    'reviewed_date': balance[13].isoformat() if balance[13] else None,
                    'approved_date': balance[14].isoformat() if balance[14] else None,
                    'posted_date': balance[15].isoformat() if balance[15] else None,
                    'fiscal_year': balance[16]
                })
            
            return jsonify({
                'success': True,
                'opening_balances': balances_list,
                'total_count': len(balances_list)
            })
        else:
            return jsonify({
                'success': True,
                'opening_balances': [],
                'total_count': 0,
                'message': 'لا توجد أرصدة افتتاحية'
            })
            
    except Exception as e:
        logger.error(f"خطأ في جلب الأرصدة الافتتاحية: {e}")
        return jsonify({'success': False, 'message': f'خطأ في جلب البيانات: {str(e)}'}), 500

@opening_balances_bp.route('/api/save_opening_balance', methods=['POST'])
def save_opening_balance():
    """حفظ أو تحديث رصيد افتتاحي"""
    try:
        data = request.get_json()
        
        # التحقق من البيانات المطلوبة
        required_fields = ['supplier_id', 'fiscal_date', 'currency_code', 'opening_balance_amount']
        for field in required_fields:
            if field not in data or data[field] is None:
                return jsonify({'success': False, 'message': f'الحقل {field} مطلوب'}), 400
        
        oracle_mgr = get_oracle_manager()
        if not oracle_mgr.connect():
            return jsonify({'success': False, 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500
        
        # التحقق من وجود رصيد مكرر في النظام المركزي
        check_query = """
        SELECT COUNT(*) FROM OPENING_BALANCES
        WHERE entity_type_code = 'SUPPLIER'
        AND entity_id = :supplier_id
        AND fiscal_period_start_date = TO_DATE(:fiscal_date, 'YYYY-MM-DD')
        AND currency_code = :currency_code
        AND is_active = 1
        """

        if 'id' in data and data['id']:
            check_query += " AND id != :id"

        check_params = {
            'supplier_id': data['supplier_id'],
            'fiscal_date': data['fiscal_date'],
            'currency_code': data['currency_code']
        }

        if 'id' in data and data['id']:
            check_params['id'] = data['id']
        
        existing_count = oracle_mgr.execute_query(check_query, check_params)
        
        if existing_count and existing_count[0][0] > 0:
            oracle_mgr.disconnect()
            return jsonify({'success': False, 'message': 'يوجد رصيد افتتاحي مسبق لهذا المورد في نفس الفترة والعملة'}), 400
        
        # حفظ أو تحديث الرصيد في النظام المركزي
        if 'id' in data and data['id']:
            # تحديث رصيد موجود
            update_query = """
            UPDATE OPENING_BALANCES
            SET opening_balance_amount = :amount,
                currency_code = :currency_code,
                exchange_rate = :exchange_rate,
                balance_type = :balance_type,
                notes = :notes,
                reference_document = :reference_doc,
                updated_by = :user_id,
                updated_date = CURRENT_TIMESTAMP
            WHERE id = :id
            """

            update_params = {
                'id': data['id'],
                'amount': data['opening_balance_amount'],
                'currency_code': data['currency_code'],
                'exchange_rate': data.get('exchange_rate', 1),
                'balance_type': data.get('balance_type', 'DEBIT'),
                'notes': data.get('notes', ''),
                'reference_doc': data.get('reference_document', ''),
                'user_id': session.get('user_id', 1)
            }

            oracle_mgr.execute_update(update_query, update_params)
            message = 'تم تحديث الرصيد الافتتاحي بنجاح'

        else:
            # إنشاء رصيد جديد - الحصول على SUPPLIER_CODE أولاً
            supplier_code_query = """
            SELECT supplier_code FROM SUPPLIERS WHERE id = :supplier_id
            """
            supplier_code_result = oracle_mgr.execute_query(supplier_code_query, {'supplier_id': data['supplier_id']})

            if not supplier_code_result:
                oracle_mgr.disconnect()
                return jsonify({'success': False, 'message': 'المورد غير موجود'}), 400

            supplier_code = supplier_code_result[0][0]

            insert_query = """
            INSERT INTO OPENING_BALANCES (
                entity_type_code, entity_id, fiscal_year, fiscal_period_start_date,
                currency_code, opening_balance_amount, exchange_rate, balance_type,
                notes, reference_document, created_by, status, document_type_code
            ) VALUES (
                'SUPPLIER', :supplier_code, EXTRACT(YEAR FROM TO_DATE(:fiscal_date, 'YYYY-MM-DD')),
                TO_DATE(:fiscal_date, 'YYYY-MM-DD'), :currency_code, :amount, :exchange_rate,
                :balance_type, :notes, :reference_doc, :user_id, 'DRAFT', 'OPENING_BALANCE'
            )
            """

            insert_params = {
                'supplier_code': supplier_code,
                'fiscal_date': data['fiscal_date'],
                'currency_code': data['currency_code'],
                'amount': data['opening_balance_amount'],
                'exchange_rate': data.get('exchange_rate', 1),
                'balance_type': data.get('balance_type', 'DEBIT'),
                'notes': data.get('notes', ''),
                'reference_doc': data.get('reference_document', ''),
                'user_id': session.get('user_id', 1)
            }

            oracle_mgr.execute_update(insert_query, insert_params)
            message = 'تم حفظ الرصيد الافتتاحي بنجاح'
        
        oracle_mgr.disconnect()
        
        return jsonify({
            'success': True,
            'message': message
        })
        
    except Exception as e:
        logger.error(f"خطأ في حفظ الرصيد الافتتاحي: {e}")
        return jsonify({'success': False, 'message': f'خطأ في حفظ البيانات: {str(e)}'}), 500

@opening_balances_bp.route('/api/delete_opening_balance/<int:balance_id>', methods=['DELETE'])
def delete_opening_balance(balance_id):
    """حذف رصيد افتتاحي"""
    try:
        oracle_mgr = get_oracle_manager()
        if not oracle_mgr.connect():
            return jsonify({'success': False, 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500

        # التحقق من حالة الرصيد في النظام المركزي
        status_query = "SELECT status FROM OPENING_BALANCES WHERE id = :id AND entity_type_code = 'SUPPLIER'"
        status_result = oracle_mgr.execute_query(status_query, {'id': balance_id})

        if not status_result:
            oracle_mgr.disconnect()
            return jsonify({'success': False, 'message': 'الرصيد الافتتاحي غير موجود'}), 404

        current_status = status_result[0][0]
        if current_status in ['APPROVED', 'POSTED']:
            oracle_mgr.disconnect()
            return jsonify({'success': False, 'message': 'لا يمكن حذف رصيد معتمد أو مرحل'}), 400

        # حذف الرصيد (تعطيل)
        delete_query = """
        UPDATE OPENING_BALANCES
        SET is_active = 0,
            updated_by = :user_id,
            updated_date = CURRENT_TIMESTAMP
        WHERE id = :id AND entity_type_code = 'SUPPLIER'
        """

        oracle_mgr.execute_update(delete_query, {
            'id': balance_id,
            'user_id': session.get('user_id', 1)
        })

        oracle_mgr.disconnect()

        return jsonify({
            'success': True,
            'message': 'تم حذف الرصيد الافتتاحي بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في حذف الرصيد الافتتاحي: {e}")
        return jsonify({'success': False, 'message': f'خطأ في حذف البيانات: {str(e)}'}), 500

@opening_balances_bp.route('/api/create_test_supplier', methods=['POST'])
def create_test_supplier():
    """إنشاء مورد تجريبي للاختبار"""
    try:
        oracle_mgr = get_oracle_manager()
        oracle_mgr.connect()

        # التحقق من وجود مورد برقم 1
        check_query = "SELECT COUNT(*) FROM SUPPLIERS WHERE id = 1"
        result = oracle_mgr.execute_query(check_query)

        if result and result[0][0] > 0:
            oracle_mgr.disconnect()
            return jsonify({
                'success': True,
                'message': 'المورد رقم 1 موجود بالفعل'
            })

        # إنشاء مورد تجريبي برقم 1
        oracle_mgr.execute_update("""
        INSERT INTO SUPPLIERS (id, name_ar, name_en, supplier_code, supplier_type,
                               phone, email, address, city, country,
                               tax_number, payment_terms, credit_limit, is_active,
                               created_at, updated_at)
        VALUES (1, :name_ar, :name_en, :supplier_code, :supplier_type,
                :phone, :email, :address, :city, :country,
                :tax_number, :payment_terms, :credit_limit, :is_active,
                CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        """, {
            'name_ar': 'مورد تجريبي للنظام المركزي',
            'name_en': 'Test Supplier for Central System',
            'supplier_code': 'TEST001',
            'supplier_type': 'تجريبي',
            'phone': '0501234567',
            'email': '<EMAIL>',
            'address': 'عنوان تجريبي',
            'city': 'الرياض',
            'country': 'السعودية',
            'tax_number': 'TEST123456789',
            'payment_terms': 30,
            'credit_limit': 100000,
            'is_active': 1
        })

        # إنشاء رصيد افتتاحي للمورد التجريبي
        opening_balance_query = """
        INSERT INTO OPENING_BALANCES (
            entity_type_code, entity_id, fiscal_year, fiscal_period_start_date,
            currency_code, opening_balance_amount, balance_type,
            status, document_type_code, created_by
        ) VALUES (
            'SUPPLIER', 1, 2024, DATE '2024-01-01', 'SAR', 25000, 'CREDIT',
            'POSTED', 'OPENING_BALANCE', 1
        )
        """

        try:
            oracle_mgr.execute_update(opening_balance_query)
        except Exception as e:
            if "unique constraint" not in str(e).lower():
                logger.warning(f"تحذير في إنشاء الرصيد الافتتاحي: {e}")

        # إنشاء رصيد جاري للمورد التجريبي
        current_balance_query = """
        INSERT INTO CURRENT_BALANCES (
            entity_type_code, entity_id, currency_code, opening_balance,
            debit_amount, credit_amount, current_balance, total_transactions_count,
            created_at, updated_at
        ) VALUES (
            'SUPPLIER', 1, 'SAR', 25000, 0, 0, 25000, 0,
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        )
        """

        try:
            oracle_mgr.execute_update(current_balance_query)
        except Exception as e:
            if "unique constraint" not in str(e).lower():
                logger.warning(f"تحذير في إنشاء الرصيد الجاري: {e}")

        oracle_mgr.commit()
        oracle_mgr.disconnect()

        return jsonify({
            'success': True,
            'message': 'تم إنشاء مورد تجريبي برقم 1 مع رصيد 25,000 ريال بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في إنشاء المورد التجريبي: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@opening_balances_bp.route('/api/sync_with_central_system', methods=['POST'])
def sync_with_central_system():
    """مزامنة الأرصدة مع النظام المركزي"""
    try:
        oracle_mgr = get_oracle_manager()
        if not oracle_mgr.connect():
            return jsonify({'success': False, 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500

        # إعادة حساب جميع الأرصدة الجارية للموردين
        sync_query = """
        MERGE INTO CURRENT_BALANCES cb
        USING (
            SELECT
                'SUPPLIER' as entity_type_code,
                ob.entity_id,
                ob.currency_code,
                SUM(ob.opening_balance_amount) as total_opening_balance,
                COALESCE(bt.total_debit, 0) as total_debit,
                COALESCE(bt.total_credit, 0) as total_credit,
                COALESCE(bt.transaction_count, 0) as transaction_count,
                bt.last_transaction_date
            FROM OPENING_BALANCES ob
            LEFT JOIN (
                SELECT
                    entity_type_code, entity_id, currency_code,
                    SUM(debit_amount) as total_debit,
                    SUM(credit_amount) as total_credit,
                    COUNT(*) as transaction_count,
                    MAX(document_date) as last_transaction_date
                FROM BALANCE_TRANSACTIONS
                WHERE entity_type_code = 'SUPPLIER' AND status = 'POSTED'
                GROUP BY entity_type_code, entity_id, currency_code
            ) bt ON ob.entity_type_code = bt.entity_type_code
                AND ob.entity_id = bt.entity_id
                AND ob.currency_code = bt.currency_code
            WHERE ob.entity_type_code = 'SUPPLIER'
            AND ob.status = 'POSTED'
            AND ob.is_active = 1
            GROUP BY ob.entity_id, ob.currency_code, bt.total_debit, bt.total_credit,
                     bt.transaction_count, bt.last_transaction_date
        ) src ON (cb.entity_type_code = src.entity_type_code
                  AND cb.entity_id = src.entity_id
                  AND cb.currency_code = src.currency_code)
        WHEN MATCHED THEN
            UPDATE SET
                opening_balance = src.total_opening_balance,
                debit_amount = src.total_debit,
                credit_amount = src.total_credit,
                current_balance = src.total_opening_balance + src.total_debit - src.total_credit,
                total_transactions_count = src.transaction_count,
                last_transaction_date = src.last_transaction_date,
                updated_at = CURRENT_TIMESTAMP
        WHEN NOT MATCHED THEN
            INSERT (entity_type_code, entity_id, currency_code, opening_balance,
                    debit_amount, credit_amount, current_balance, total_transactions_count,
                    last_transaction_date, created_at, updated_at)
            VALUES (src.entity_type_code, src.entity_id, src.currency_code, src.total_opening_balance,
                    src.total_debit, src.total_credit,
                    src.total_opening_balance + src.total_debit - src.total_credit,
                    src.transaction_count, src.last_transaction_date,
                    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        """

        result = oracle_mgr.execute_update(sync_query)
        oracle_mgr.commit()
        oracle_mgr.disconnect()

        return jsonify({
            'success': True,
            'message': f'تم مزامنة {result} رصيد مع النظام المركزي بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في المزامنة مع النظام المركزي: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@opening_balances_bp.route('/api/create_posting_procedure', methods=['POST'])
def create_posting_procedure():
    """إنشاء الإجراء المخزن لترحيل الأرصدة الافتتاحية"""
    try:
        oracle_mgr = get_oracle_manager()
        oracle_mgr.connect()

        # إنشاء الإجراء المخزن المصحح - لا يضاعف المبالغ
        procedure_sql = """
        CREATE OR REPLACE PROCEDURE POST_OPENING_BALANCES(
            p_fiscal_date IN DATE,
            p_currency_code IN VARCHAR2 DEFAULT NULL,
            p_posted_by IN NUMBER,
            p_posting_reference IN VARCHAR2 DEFAULT NULL
        ) AS
        BEGIN
            -- ترحيل الأرصدة الافتتاحية المعتمدة إلى جدول SUPPLIER_BALANCES
            MERGE INTO SUPPLIER_BALANCES sb
            USING (
                SELECT
                    sob.supplier_id,
                    sob.currency_code,
                    sob.opening_balance_amount,
                    sob.balance_type,
                    CURRENT_TIMESTAMP as updated_time,
                    p_posted_by as posted_by_user
                FROM SUPPLIER_OPENING_BALANCES sob
                WHERE sob.fiscal_period_start_date = p_fiscal_date
                AND sob.status = 'POSTED'
                AND sob.is_active = 1
                AND (p_currency_code IS NULL OR sob.currency_code = p_currency_code)
            ) src ON (sb.supplier_id = src.supplier_id AND sb.currency_code = src.currency_code)
            WHEN MATCHED THEN
                UPDATE SET
                    opening_balance = src.opening_balance_amount,
                    current_balance = src.opening_balance_amount,  -- تعيين الرصيد الجاري = الرصيد الافتتاحي فقط
                    updated_at = src.updated_time,
                    updated_by = src.posted_by_user
            WHEN NOT MATCHED THEN
                INSERT (
                    supplier_id,
                    currency_code,
                    opening_balance,
                    debit_amount,
                    credit_amount,
                    current_balance,
                    total_invoices_count,
                    total_payments_count,
                    created_at,
                    updated_at,
                    created_by,
                    updated_by
                )
                VALUES (
                    src.supplier_id,
                    src.currency_code,
                    src.opening_balance_amount,
                    0,  -- لا نضع المبلغ في debit_amount لتجنب المضاعفة
                    0,  -- لا نضع المبلغ في credit_amount لتجنب المضاعفة
                    src.opening_balance_amount,  -- الرصيد الجاري = الرصيد الافتتاحي فقط
                    0,
                    0,
                    src.updated_time,
                    src.updated_time,
                    src.posted_by_user,
                    src.posted_by_user
                );

            COMMIT;
        END;
        """

        oracle_mgr.execute_update(procedure_sql, {})
        oracle_mgr.disconnect()

        return jsonify({
            'success': True,
            'message': 'تم إنشاء إجراء ترحيل الأرصدة الافتتاحية بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في إنشاء الإجراء المخزن: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@opening_balances_bp.route('/api/fix_balance_type_trigger', methods=['POST'])
def fix_balance_type_trigger():
    """إصلاح الـ Trigger ليحترم balance_type المحدد من المستخدم"""
    try:
        oracle_mgr = get_oracle_manager()
        oracle_mgr.connect()

        # إنشاء الـ Trigger المحدث
        new_trigger_sql = """
        CREATE OR REPLACE TRIGGER SUP_OPENING_TRIGGER
        BEFORE INSERT OR UPDATE ON SUPPLIER_OPENING_BALANCES
        FOR EACH ROW
        BEGIN
            -- تعيين ID تلقائياً عند الإدراج
            IF INSERTING AND :NEW.id IS NULL THEN
                :NEW.id := SUP_OPENING_SEQ.NEXTVAL;
            END IF;

            -- تحديث تاريخ التعديل
            :NEW.updated_date := CURRENT_TIMESTAMP;

            -- حساب السنة المالية
            :NEW.fiscal_year := EXTRACT(YEAR FROM :NEW.fiscal_period_start_date);

            -- حساب المبلغ بالعملة الأساسية
            :NEW.base_currency_amount := :NEW.opening_balance_amount * NVL(:NEW.exchange_rate, 1);

            -- حماية: عدم تعديل balance_type إذا كان محدداً مسبقاً
            -- فقط تعيين قيمة افتراضية إذا كان فارغاً
            IF :NEW.balance_type IS NULL THEN
                IF :NEW.opening_balance_amount >= 0 THEN
                    :NEW.balance_type := 'DEBIT';
                ELSE
                    :NEW.balance_type := 'CREDIT';
                    :NEW.opening_balance_amount := ABS(:NEW.opening_balance_amount);
                END IF;
            END IF;
        END;
        """

        oracle_mgr.execute_update(new_trigger_sql, {})
        oracle_mgr.disconnect()

        return jsonify({
            'success': True,
            'message': 'تم إصلاح الـ Trigger بنجاح - الآن يحترم balance_type المحدد من المستخدم'
        })

    except Exception as e:
        logger.error(f"خطأ في إصلاح الـ Trigger: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@opening_balances_bp.route('/api/check_balance_type_protection', methods=['GET'])
def check_balance_type_protection():
    """فحص حماية balance_type في قاعدة البيانات"""
    try:
        oracle_mgr = get_oracle_manager()
        oracle_mgr.connect()

        # فحص بنية جدول الأرصدة الافتتاحية
        opening_table_structure = oracle_mgr.execute_query("""
        SELECT column_name, data_type, nullable
        FROM user_tab_columns
        WHERE table_name = 'SUPPLIER_OPENING_BALANCES'
        ORDER BY column_id
        """)

        # فحص بنية جدول الأرصدة الجارية
        balances_table_structure = oracle_mgr.execute_query("""
        SELECT column_name, data_type, nullable
        FROM user_tab_columns
        WHERE table_name = 'SUPPLIER_BALANCES'
        ORDER BY column_id
        """)

        # فحص محتويات جدول SUPPLIER_BALANCES
        balances_content = oracle_mgr.execute_query("""
        SELECT supplier_id, currency_code, current_balance, opening_balance,
               created_at, updated_at
        FROM SUPPLIER_BALANCES
        ORDER BY supplier_id, currency_code
        """)

        # فحص محتوى جدول الأرصدة الافتتاحية
        opening_balances_content = oracle_mgr.execute_query("""
        SELECT supplier_id, currency_code, opening_balance_amount, balance_type, status
        FROM SUPPLIER_OPENING_BALANCES
        ORDER BY supplier_id, currency_code
        """)

        # فحص الموردين في الأرصدة الافتتاحية مقابل جدول الموردين
        supplier_check = oracle_mgr.execute_query("""
        SELECT
            sob.supplier_id,
            sob.status,
            CASE WHEN s.id IS NOT NULL THEN 'موجود' ELSE 'غير موجود' END as supplier_exists,
            s.name as supplier_name
        FROM SUPPLIER_OPENING_BALANCES sob
        LEFT JOIN SUPPLIERS s ON sob.supplier_id = s.id
        ORDER BY sob.supplier_id
        """)

        # فحص جميع الحالات الموجودة
        status_check = oracle_mgr.execute_query("""
        SELECT DISTINCT status, COUNT(*) as count
        FROM SUPPLIER_OPENING_BALANCES
        GROUP BY status
        ORDER BY status
        """)

        trigger_check = oracle_mgr.execute_query("""
        SELECT trigger_name, trigger_type, triggering_event, status
        FROM user_triggers
        WHERE table_name = 'SUPPLIER_OPENING_BALANCES'
        """)

        # فحص محتوى الـ Trigger
        trigger_source = oracle_mgr.execute_query("""
        SELECT trigger_body
        FROM user_triggers
        WHERE table_name = 'SUPPLIER_OPENING_BALANCES'
        AND trigger_name = 'SUP_OPENING_TRIGGER'
        """)

        oracle_mgr.disconnect()

        return jsonify({
            'success': True,
            'opening_table_structure': opening_table_structure,
            'balances_table_structure': balances_table_structure,
            'balances_content': balances_content,
            'opening_balances_content': opening_balances_content,
            'supplier_check': supplier_check,
            'status_check': status_check,
            'triggers': trigger_check,
            'trigger_source': trigger_source
        })

    except Exception as e:
        logger.error(f"خطأ في فحص حماية balance_type: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@opening_balances_bp.route('/api/approve_opening_balances', methods=['POST'])
def approve_opening_balances():
    """اعتماد أو إلغاء اعتماد الأرصدة الافتتاحية باستخدام النظام المركزي"""
    try:
        data = request.get_json()
        balance_id = data.get('balance_id', '')  # معرف الرصيد المحدد (للعمليات الفردية)
        action = data.get('action', 'approve')  # approve أو unapprove

        if not balance_id:
            return jsonify({'success': False, 'message': 'معرف الرصيد مطلوب'}), 400

        # استخدام API النظام المركزي
        import requests
        from flask import current_app

        oracle_mgr = get_oracle_manager()
        if not oracle_mgr.connect():
            return jsonify({'success': False, 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500

        if action == 'approve':
            # اعتماد الرصيد الافتتاحي
            update_query = """
            UPDATE OPENING_BALANCES
            SET status = 'APPROVED',
                approved_by = :user_id,
                approved_date = CURRENT_TIMESTAMP,
                updated_by = :user_id,
                updated_date = CURRENT_TIMESTAMP
            WHERE id = :balance_id AND entity_type_code = 'SUPPLIER' AND status = 'DRAFT'
            """

            result = oracle_mgr.execute_update(update_query, {
                'balance_id': balance_id,
                'user_id': session.get('user_id', 1)
            })

            if result > 0:
                oracle_mgr.commit()
                oracle_mgr.disconnect()
                return jsonify({
                    'success': True,
                    'message': 'تم اعتماد الرصيد الافتتاحي بنجاح'
                })
            else:
                oracle_mgr.disconnect()
                return jsonify({'success': False, 'message': 'لم يتم العثور على رصيد قابل للاعتماد'}), 400
        else:
            # إلغاء اعتماد الرصيد الافتتاحي
            update_query = """
            UPDATE OPENING_BALANCES
            SET status = 'DRAFT',
                approved_by = NULL,
                approved_date = NULL,
                updated_by = :user_id,
                updated_date = CURRENT_TIMESTAMP
            WHERE id = :balance_id AND entity_type_code = 'SUPPLIER' AND status = 'APPROVED'
            """

            result = oracle_mgr.execute_update(update_query, {
                'balance_id': balance_id,
                'user_id': session.get('user_id', 1)
            })

            if result > 0:
                oracle_mgr.commit()
                oracle_mgr.disconnect()
                return jsonify({
                    'success': True,
                    'message': 'تم إلغاء اعتماد الرصيد الافتتاحي بنجاح'
                })
            else:
                oracle_mgr.disconnect()
                return jsonify({'success': False, 'message': 'لم يتم العثور على رصيد قابل لإلغاء الاعتماد'}), 400

        # تحديد الحالة المطلوبة حسب العملية
        if action == 'approve':
            source_status = 'DRAFT'
            target_status = 'APPROVED'
            operation_name = 'اعتماد'
        else:  # unapprove
            source_status = 'APPROVED'
            target_status = 'DRAFT'
            operation_name = 'إلغاء اعتماد'

        # بناء استعلام التحقق من وجود أرصدة في الحالة المطلوبة
        where_conditions = [
            "fiscal_period_start_date = TO_DATE(:fiscal_date, 'YYYY-MM-DD')",
            f"status = '{source_status}'",
            "is_active = 1"
        ]

        params = {
            'fiscal_date': fiscal_date
        }

        # إضافة فلتر العملة فقط إذا كان محدداً
        if currency_code and currency_code.strip():
            where_conditions.append("currency_code = :currency_code")
            params['currency_code'] = currency_code

        # إضافة فلتر المورد فقط إذا كان محدداً
        if supplier_id and supplier_id.strip():
            where_conditions.append("supplier_id = :supplier_id")
            params['supplier_id'] = supplier_id

        # إضافة فلتر الرصيد المحدد فقط إذا كان محدداً (للعمليات الفردية)
        if balance_id and str(balance_id).strip():
            where_conditions.append("id = :balance_id")
            params['balance_id'] = balance_id

        check_query = f"""
        SELECT COUNT(*) FROM SUPPLIER_OPENING_BALANCES
        WHERE {' AND '.join(where_conditions)}
        """

        record_count = oracle_mgr.execute_query(check_query, params)

        if not record_count or record_count[0][0] == 0:
            oracle_mgr.disconnect()
            return jsonify({'success': False, 'message': f'لا توجد أرصدة في حالة {source_status} لـ{operation_name}'}), 400

        # بناء استعلام التحديث
        update_where_conditions = [
            "fiscal_period_start_date = TO_DATE(:fiscal_date, 'YYYY-MM-DD')",
            f"status = '{source_status}'",
            "is_active = 1"
        ]

        update_params = {
            'fiscal_date': fiscal_date,
            'user_id': session.get('user_id', 1)
        }

        # إضافة فلتر العملة فقط إذا كان محدداً
        if currency_code and currency_code.strip():
            update_where_conditions.append("currency_code = :currency_code")
            update_params['currency_code'] = currency_code

        # إضافة فلتر المورد فقط إذا كان محدداً
        if supplier_id and supplier_id.strip():
            update_where_conditions.append("supplier_id = :supplier_id")
            update_params['supplier_id'] = supplier_id

        # إضافة فلتر الرصيد المحدد فقط إذا كان محدداً (للعمليات الفردية)
        if balance_id and str(balance_id).strip():
            update_where_conditions.append("id = :balance_id")
            update_params['balance_id'] = balance_id

        # تحديث حالة الأرصدة الافتتاحية مع حماية balance_type
        if action == 'approve':
            update_query = f"""
            UPDATE SUPPLIER_OPENING_BALANCES
            SET status = '{target_status}',
                updated_by = :user_id,
                updated_date = CURRENT_TIMESTAMP
                -- حماية صريحة: balance_type لا يتم تعديله هنا
            WHERE {' AND '.join(update_where_conditions)}
            """
        else:  # unapprove
            update_query = f"""
            UPDATE SUPPLIER_OPENING_BALANCES
            SET status = '{target_status}',
                updated_by = :user_id,
                updated_date = CURRENT_TIMESTAMP
                -- حماية صريحة: balance_type لا يتم تعديله هنا
            WHERE {' AND '.join(update_where_conditions)}
            """

        # تسجيل العملية للمراجعة
        logger.info(f"تحديث حالة الأرصدة: {action} - الشروط: {update_where_conditions}")

        # تحقق إضافي: التأكد من أن balance_type لا يتم تعديله في SET clause
        query_lower = str(update_query).lower()
        set_clause_start = query_lower.find('set')
        where_clause_start = query_lower.find('where')
        if set_clause_start != -1 and where_clause_start != -1:
            set_clause = query_lower[set_clause_start:where_clause_start]
            if 'balance_type =' in set_clause:
                logger.error("محاولة تعديل balance_type في عملية الاعتماد - تم منعها!")
                return jsonify({'success': False, 'message': 'خطأ: لا يمكن تعديل نوع الرصيد أثناء الاعتماد'}), 400

        result = oracle_mgr.execute_update(update_query, update_params)

        oracle_mgr.disconnect()

        return jsonify({
            'success': True,
            'message': f'تم {operation_name} {result} رصيد افتتاحي بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في اعتماد الأرصدة الافتتاحية: {e}")
        return jsonify({'success': False, 'message': f'خطأ في اعتماد البيانات: {str(e)}'}), 500

@opening_balances_bp.route('/api/post_opening_balances', methods=['POST'])
def post_opening_balances():
    """ترحيل أو إلغاء ترحيل الأرصدة الافتتاحية باستخدام النظام المركزي"""
    try:
        data = request.get_json()
        balance_id = data.get('balance_id', '')  # معرف الرصيد المحدد (للعمليات الفردية)
        action = data.get('action', 'post')  # post أو unpost

        if not balance_id:
            return jsonify({'success': False, 'message': 'معرف الرصيد مطلوب'}), 400

        oracle_mgr = get_oracle_manager()
        if not oracle_mgr.connect():
            return jsonify({'success': False, 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500

        if action == 'post':
            # ترحيل الرصيد الافتتاحي
            # أولاً التحقق من أن الرصيد معتمد
            check_query = """
            SELECT status, entity_id, currency_code, opening_balance_amount, balance_type
            FROM OPENING_BALANCES
            WHERE id = :balance_id AND entity_type_code = 'SUPPLIER'
            """

            check_result = oracle_mgr.execute_query(check_query, {'balance_id': balance_id})

            if not check_result:
                oracle_mgr.disconnect()
                return jsonify({'success': False, 'message': 'الرصيد الافتتاحي غير موجود'}), 404

            current_status, entity_id, currency_code, amount, balance_type = check_result[0]

            if current_status not in ['APPROVED', 'DRAFT']:
                oracle_mgr.disconnect()
                return jsonify({'success': False, 'message': f'لا يمكن ترحيل رصيد في حالة {current_status}'}), 400

            # تحديث حالة الترحيل
            update_query = """
            UPDATE OPENING_BALANCES
            SET status = 'POSTED',
                posted_by = :user_id,
                posted_date = CURRENT_TIMESTAMP,
                updated_by = :user_id,
                updated_date = CURRENT_TIMESTAMP
            WHERE id = :balance_id
            """

            oracle_mgr.execute_update(update_query, {
                'balance_id': balance_id,
                'user_id': session.get('user_id', 1)
            })

            # تحديث الرصيد الجاري في النظام المركزي
            # البحث عن الرصيد الجاري أو إنشاؤه
            current_balance_query = """
            SELECT id FROM CURRENT_BALANCES
            WHERE entity_type_code = 'SUPPLIER' AND entity_id = :entity_id AND currency_code = :currency_code
            """

            current_balance_result = oracle_mgr.execute_query(current_balance_query, {
                'entity_id': entity_id,
                'currency_code': currency_code
            })

            if current_balance_result:
                # تحديث الرصيد الموجود
                update_balance_query = """
                UPDATE CURRENT_BALANCES
                SET opening_balance = :amount,
                    current_balance = opening_balance + debit_amount - credit_amount,
                    updated_at = CURRENT_TIMESTAMP
                WHERE entity_type_code = 'SUPPLIER' AND entity_id = :entity_id AND currency_code = :currency_code
                """
                oracle_mgr.execute_update(update_balance_query, {
                    'amount': amount,
                    'entity_id': entity_id,
                    'currency_code': currency_code
                })
            else:
                # إنشاء رصيد جديد
                insert_balance_query = """
                INSERT INTO CURRENT_BALANCES (
                    entity_type_code, entity_id, currency_code, opening_balance,
                    debit_amount, credit_amount, current_balance, total_transactions_count,
                    created_at, updated_at
                ) VALUES (
                    'SUPPLIER', :entity_id, :currency_code, :amount,
                    0, 0, :amount, 0,
                    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                )
                """
                oracle_mgr.execute_update(insert_balance_query, {
                    'entity_id': entity_id,
                    'currency_code': currency_code,
                    'amount': amount
                })

            oracle_mgr.commit()
            oracle_mgr.disconnect()

            return jsonify({
                'success': True,
                'message': 'تم ترحيل الرصيد الافتتاحي بنجاح'
            })

        else:
            # إلغاء ترحيل الرصيد الافتتاحي
            update_query = """
            UPDATE OPENING_BALANCES
            SET status = 'APPROVED',
                posted_date = NULL,
                posted_by = NULL,
                updated_by = :user_id,
                updated_date = CURRENT_TIMESTAMP
            WHERE id = :balance_id AND entity_type_code = 'SUPPLIER' AND status = 'POSTED'
            """

            result = oracle_mgr.execute_update(update_query, {
                'balance_id': balance_id,
                'user_id': session.get('user_id', 1)
            })

            if result > 0:
                oracle_mgr.commit()
                oracle_mgr.disconnect()
                return jsonify({
                    'success': True,
                    'message': 'تم إلغاء ترحيل الرصيد الافتتاحي بنجاح'
                })
            else:
                oracle_mgr.disconnect()
                return jsonify({'success': False, 'message': 'لم يتم العثور على رصيد قابل لإلغاء الترحيل'}), 400

        # تحديد الحالة المطلوبة حسب العملية
        if action == 'post':
            source_status = 'APPROVED'
            target_status = 'POSTED'
            operation_name = 'ترحيل'
        else:  # unpost
            source_status = 'POSTED'
            target_status = 'APPROVED'
            operation_name = 'إلغاء ترحيل'

        # بناء استعلام التحقق من وجود أرصدة في الحالة المطلوبة
        where_conditions = [
            "fiscal_period_start_date = TO_DATE(:fiscal_date, 'YYYY-MM-DD')",
            f"status = '{source_status}'",
            "is_active = 1"
        ]

        params = {
            'fiscal_date': fiscal_date
        }

        # إضافة فلتر العملة فقط إذا كان محدداً
        if currency_code and currency_code.strip():
            where_conditions.append("currency_code = :currency_code")
            params['currency_code'] = currency_code

        # إضافة فلتر المورد فقط إذا كان محدداً
        if supplier_id and supplier_id.strip():
            where_conditions.append("supplier_id = :supplier_id")
            params['supplier_id'] = supplier_id

        # إضافة فلتر الرصيد المحدد فقط إذا كان محدداً (للعمليات الفردية)
        if balance_id and str(balance_id).strip():
            where_conditions.append("id = :balance_id")
            params['balance_id'] = balance_id

        check_query = f"""
        SELECT COUNT(*) FROM SUPPLIER_OPENING_BALANCES
        WHERE {' AND '.join(where_conditions)}
        """

        record_count = oracle_mgr.execute_query(check_query, params)

        if not record_count or record_count[0][0] == 0:
            oracle_mgr.disconnect()
            return jsonify({'success': False, 'message': f'لا توجد أرصدة في حالة {source_status} لـ{operation_name}'}), 400

        # إنشاء مرجع الترحيل
        if not posting_reference:
            posting_reference = f'OB{fiscal_date.replace("-", "")}{currency_code}'

        # ترحيل الأرصدة إلى جدول المعاملات
        post_query = """
        INSERT INTO SUPPLIER_TRANSACTIONS (
            supplier_id, transaction_type, reference_type, reference_id,
            transaction_date, currency_code, original_amount,
            debit_amount, credit_amount, description, status,
            created_date, created_by
        )
        SELECT
            sob.supplier_id,
            'OPENING_BALANCE',
            'FISCAL_PERIOD',
            sob.id,
            sob.fiscal_period_start_date,
            sob.currency_code,
            sob.opening_balance_amount,
            CASE WHEN sob.balance_type = 'DEBIT' THEN sob.opening_balance_amount ELSE 0 END,
            CASE WHEN sob.balance_type = 'CREDIT' THEN sob.opening_balance_amount ELSE 0 END,
            'رصيد افتتاحي للفترة المحاسبية ' || sob.fiscal_year ||
            CASE WHEN sob.notes IS NOT NULL THEN ' - ' || sob.notes ELSE '' END,
            'POSTED',
            CURRENT_TIMESTAMP,
            :user_id
        FROM SUPPLIER_OPENING_BALANCES sob
        WHERE sob.fiscal_period_start_date = TO_DATE(:fiscal_date, 'YYYY-MM-DD')
        AND sob.currency_code = :currency_code
        AND sob.status = 'APPROVED'
        AND sob.is_active = 1
        """

        oracle_mgr.execute_update(post_query, {
            'fiscal_date': fiscal_date,
            'currency_code': currency_code,
            'user_id': session.get('user_id', 1)
        })

        # بناء شروط التحديث الديناميكية
        update_where_conditions = [
            "fiscal_period_start_date = TO_DATE(:fiscal_date, 'YYYY-MM-DD')",
            "status = 'APPROVED'",
            "is_active = 1"
        ]

        update_params = {
            'fiscal_date': fiscal_date,
            'user_id': session.get('user_id', 1)
        }

        # إضافة فلتر العملة فقط إذا كان محدداً
        if currency_code and currency_code.strip():
            update_where_conditions.append("currency_code = :currency_code")
            update_params['currency_code'] = currency_code

        # إضافة فلتر المورد فقط إذا كان محدداً
        if supplier_id and supplier_id.strip():
            update_where_conditions.append("supplier_id = :supplier_id")
            update_params['supplier_id'] = supplier_id

        # إضافة فلتر الرصيد المحدد فقط إذا كان محدداً (للعمليات الفردية)
        if balance_id and str(balance_id).strip():
            update_where_conditions.append("id = :balance_id")
            update_params['balance_id'] = balance_id

        # تحديث حالة الأرصدة الافتتاحية مع حماية balance_type
        if action == 'post':
            update_status_query = f"""
            UPDATE SUPPLIER_OPENING_BALANCES
            SET status = 'POSTED',
                posted_date = CURRENT_TIMESTAMP,
                posted_by = :user_id,
                updated_by = :user_id,
                updated_date = CURRENT_TIMESTAMP
                -- حماية صريحة: balance_type لا يتم تعديله هنا
                -- ملاحظة: posting_reference غير موجود في الجدول
            WHERE {' AND '.join(update_where_conditions)}
            """
        else:  # unpost
            # تعديل شروط إلغاء الترحيل
            unpost_where_conditions = []
            for condition in update_where_conditions:
                if "status = 'APPROVED'" in condition:
                    unpost_where_conditions.append("status = 'POSTED'")
                else:
                    unpost_where_conditions.append(condition)

            update_status_query = f"""
            UPDATE SUPPLIER_OPENING_BALANCES
            SET status = 'APPROVED',
                posted_date = NULL,
                posted_by = NULL,
                updated_by = :user_id,
                updated_date = CURRENT_TIMESTAMP
                -- حماية صريحة: balance_type لا يتم تعديله هنا
                -- ملاحظة: posting_reference غير موجود في الجدول
            WHERE {' AND '.join(unpost_where_conditions)}
            """

        # تحقق إضافي: التأكد من أن balance_type لا يتم تعديله في SET clause
        query_lower = str(update_status_query).lower()
        set_clause_start = query_lower.find('set')
        where_clause_start = query_lower.find('where')
        if set_clause_start != -1 and where_clause_start != -1:
            set_clause = query_lower[set_clause_start:where_clause_start]
            if 'balance_type =' in set_clause:
                logger.error("محاولة تعديل balance_type في عملية الترحيل - تم منعها!")
                return jsonify({'success': False, 'message': 'خطأ: لا يمكن تعديل نوع الرصيد أثناء الترحيل'}), 400

        oracle_mgr.execute_update(update_status_query, update_params)

        # استدعاء الإجراء المخزن لترحيل الأرصدة إلى جدول SUPPLIER_BALANCES
        if action == 'post':
            try:
                # استدعاء الإجراء المخزن
                procedure_params = [
                    fiscal_date,  # p_fiscal_date
                    currency_code if currency_code else None,  # p_currency_code
                    session.get('user_id', 1),  # p_posted_by
                    posting_reference if posting_reference else None  # p_posting_reference
                ]

                # استدعاء الإجراء المخزن بالصيغة الصحيحة
                procedure_call = """
                BEGIN
                    POST_OPENING_BALANCES(
                        p_fiscal_date => TO_DATE(:fiscal_date, 'YYYY-MM-DD'),
                        p_currency_code => :currency_code,
                        p_posted_by => :posted_by,
                        p_posting_reference => :posting_reference
                    );
                END;
                """

                oracle_mgr.execute_update(procedure_call, {
                    'fiscal_date': fiscal_date,
                    'currency_code': currency_code if currency_code else None,
                    'posted_by': session.get('user_id', 1),
                    'posting_reference': posting_reference if posting_reference else None
                })
                logger.info(f"تم استدعاء إجراء ترحيل الأرصدة بنجاح: {procedure_params}")

            except Exception as proc_error:
                logger.error(f"خطأ في استدعاء إجراء الترحيل: {proc_error}")
                # لا نوقف العملية، فقط نسجل الخطأ

        oracle_mgr.disconnect()

        # تحديد نص الرسالة حسب العملية
        if action == 'post':
            message = f'تم ترحيل الأرصدة الافتتاحية بنجاح'
        else:
            message = f'تم إلغاء ترحيل الأرصدة الافتتاحية بنجاح'

        return jsonify({
            'success': True,
            'message': message,
            'posting_reference': posting_reference
        })

    except Exception as e:
        logger.error(f"خطأ في ترحيل الأرصدة الافتتاحية: {e}")
        return jsonify({'success': False, 'message': f'خطأ في ترحيل البيانات: {str(e)}'}), 500

@opening_balances_bp.route('/api/currencies')
def get_currencies():
    """جلب قائمة العملات النشطة"""
    try:
        oracle_mgr = get_oracle_manager()
        if not oracle_mgr.connect():
            return jsonify({'success': False, 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500

        query = """
        SELECT id, code, name_ar, name_en, symbol, exchange_rate, is_base_currency
        FROM CURRENCIES
        WHERE is_active = 1
        ORDER BY
            CASE WHEN is_base_currency = 1 THEN 0 ELSE 1 END,
            code
        """

        currencies = oracle_mgr.execute_query(query)
        oracle_mgr.disconnect()

        if currencies:
            currencies_list = []
            for currency in currencies:
                currencies_list.append({
                    'id': currency[0],
                    'code': currency[1],
                    'name_ar': currency[2],
                    'name_en': currency[3],
                    'symbol': currency[4],
                    'exchange_rate': float(currency[5]) if currency[5] else 1.0,
                    'is_base_currency': bool(currency[6])
                })

            return jsonify({
                'success': True,
                'currencies': currencies_list
            })
        else:
            return jsonify({
                'success': True,
                'currencies': []
            })

    except Exception as e:
        logger.error(f"خطأ في جلب العملات: {e}")
        return jsonify({'success': False, 'message': f'خطأ في جلب البيانات: {str(e)}'}), 500

@opening_balances_bp.route('/api/summary')
def get_opening_balances_summary():
    """جلب ملخص الأرصدة الافتتاحية"""
    try:
        fiscal_date = request.args.get('fiscal_date', '2024-01-01')
        currency_code = request.args.get('currency_code', '')
        supplier_id = request.args.get('supplier_id', '')

        oracle_mgr = get_oracle_manager()
        if not oracle_mgr.connect():
            return jsonify({'success': False, 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500

        # بناء استعلام الملخص للنظام المركزي
        where_conditions = [
            "entity_type_code = 'SUPPLIER'",
            "fiscal_period_start_date = TO_DATE(:fiscal_date, 'YYYY-MM-DD')",
            "is_active = 1"
        ]

        params = {
            'fiscal_date': fiscal_date
        }

        # إضافة فلتر العملة فقط إذا كان محدداً
        if currency_code and currency_code.strip():
            where_conditions.append("currency_code = :currency_code")
            params['currency_code'] = currency_code

        # إضافة فلتر المورد فقط إذا كان محدداً
        if supplier_id and supplier_id.strip():
            where_conditions.append("entity_id = :supplier_id")
            params['supplier_id'] = int(supplier_id)

        summary_query = f"""
        SELECT
            COUNT(*) as total_suppliers,
            SUM(CASE WHEN balance_type = 'DEBIT' THEN 1 ELSE 0 END) as debit_count,
            SUM(CASE WHEN balance_type = 'CREDIT' THEN 1 ELSE 0 END) as credit_count,
            SUM(CASE WHEN balance_type = 'DEBIT' THEN opening_balance_amount ELSE 0 END) as total_debit,
            SUM(CASE WHEN balance_type = 'CREDIT' THEN opening_balance_amount ELSE 0 END) as total_credit,
            SUM(CASE WHEN status = 'DRAFT' THEN 1 ELSE 0 END) as draft_count,
            SUM(CASE WHEN status = 'APPROVED' THEN 1 ELSE 0 END) as approved_count,
            SUM(CASE WHEN status = 'POSTED' THEN 1 ELSE 0 END) as posted_count
        FROM OPENING_BALANCES
        WHERE {' AND '.join(where_conditions)}
        """

        summary = oracle_mgr.execute_query(summary_query, params)

        oracle_mgr.disconnect()

        if summary:
            row = summary[0]
            total_debit = float(row[3]) if row[3] else 0
            total_credit = float(row[4]) if row[4] else 0

            return jsonify({
                'success': True,
                'summary': {
                    'total_suppliers': row[0] or 0,
                    'debit_count': row[1] or 0,
                    'credit_count': row[2] or 0,
                    'total_debit_amount': total_debit,
                    'total_credit_amount': total_credit,
                    'net_balance': total_debit - total_credit,
                    'draft_count': row[5] or 0,
                    'approved_count': row[6] or 0,
                    'posted_count': row[7] or 0
                }
            })
        else:
            return jsonify({
                'success': True,
                'summary': {
                    'total_suppliers': 0,
                    'debit_count': 0,
                    'credit_count': 0,
                    'total_debit_amount': 0,
                    'total_credit_amount': 0,
                    'net_balance': 0,
                    'draft_count': 0,
                    'approved_count': 0,
                    'posted_count': 0
                }
            })

    except Exception as e:
        logger.error(f"خطأ في جلب ملخص الأرصدة الافتتاحية: {e}")
        return jsonify({'success': False, 'message': f'خطأ في جلب البيانات: {str(e)}'}), 500

@opening_balances_bp.route('/api/download_template')
def download_excel_template():
    """تحميل قالب Excel للاستيراد"""
    return jsonify({'success': False, 'message': 'ميزة الاستيراد قيد التطوير'}), 501
    try:
        # إنشاء قالب Excel
        # template_generator = OpeningBalancesExcelTemplate()

        # جلب بيانات الموردين لإضافتها للقالب
        oracle_mgr = get_oracle_manager()
        suppliers_data = []

        if oracle_mgr.connect():
            query = """
            SELECT id, supplier_code, name_ar
            FROM SUPPLIERS
            WHERE is_active = 1
            ORDER BY name_ar
            """
            suppliers = oracle_mgr.execute_query(query)
            oracle_mgr.disconnect()

            if suppliers:
                suppliers_data = [
                    {
                        'id': supplier[0],
                        'code': supplier[1] or f'SUP{supplier[0]:04d}',
                        'name': supplier[2] or 'غير محدد'
                    }
                    for supplier in suppliers
                ]

        # إنشاء القالب
        template_file = template_generator.create_template(suppliers_data)

        # إرسال الملف
        from flask import send_file
        return send_file(
            template_file,
            as_attachment=True,
            download_name=f'قالب_الأرصدة_الافتتاحية_{datetime.now().strftime("%Y%m%d")}.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        logger.error(f"خطأ في تحميل قالب Excel: {e}")
        return jsonify({'success': False, 'message': f'خطأ في إنشاء القالب: {str(e)}'}), 500

@opening_balances_bp.route('/api/upload_excel', methods=['POST'])
def upload_excel_file():
    """رفع ملف Excel للاستيراد"""
    return jsonify({'success': False, 'message': 'ميزة الاستيراد قيد التطوير'}), 501
    try:
        # التحقق من وجود الملف
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'}), 400

        # التحقق من نوع الملف
        if not file.filename.lower().endswith(('.xlsx', '.xls')):
            return jsonify({'success': False, 'message': 'يجب أن يكون الملف من نوع Excel (.xlsx أو .xls)'}), 400

        # تحليل الملف
        template_generator = OpeningBalancesExcelTemplate()
        parse_result = template_generator.parse_excel_file(file)

        if not parse_result['success']:
            return jsonify(parse_result), 400

        # جلب بيانات الموردين للتحقق
        oracle_mgr = get_oracle_manager()
        suppliers_dict = {}

        if oracle_mgr.connect():
            query = "SELECT id, supplier_code, name_ar FROM SUPPLIERS WHERE is_active = 1"
            suppliers = oracle_mgr.execute_query(query)
            oracle_mgr.disconnect()

            if suppliers:
                suppliers_dict = {
                    (supplier[1] or f'SUP{supplier[0]:04d}'): {
                        'id': supplier[0],
                        'name': supplier[2] or 'غير محدد'
                    }
                    for supplier in suppliers
                }

        # التحقق من صحة السجلات
        valid_records, invalid_records = template_generator.validate_records(
            parse_result['records'], suppliers_dict
        )

        # حفظ البيانات في الجلسة للمعالجة اللاحقة
        session['excel_import_data'] = {
            'valid_records': valid_records,
            'invalid_records': invalid_records,
            'fiscal_date': request.form.get('fiscal_date', '2024-01-01'),
            'upload_time': datetime.now().isoformat()
        }

        return jsonify({
            'success': True,
            'valid_count': len(valid_records),
            'invalid_count': len(invalid_records),
            'total_count': len(parse_result['records']),
            'message': f'تم تحليل الملف بنجاح. {len(valid_records)} سجل صحيح، {len(invalid_records)} سجل يحتوي على أخطاء'
        })

    except Exception as e:
        logger.error(f"خطأ في رفع ملف Excel: {e}")
        return jsonify({'success': False, 'message': f'خطأ في معالجة الملف: {str(e)}'}), 500

@opening_balances_bp.route('/api/preview_import')
def preview_import():
    """معاينة البيانات المستوردة"""
    return jsonify({'success': False, 'message': 'ميزة الاستيراد قيد التطوير'}), 501
    try:
        import_data = session.get('excel_import_data')
        if not import_data:
            return jsonify({'success': False, 'message': 'لا توجد بيانات مستوردة للمعاينة'}), 400

        return jsonify({
            'success': True,
            'valid_records': import_data['valid_records'][:50],  # أول 50 سجل للمعاينة
            'invalid_records': import_data['invalid_records'],
            'fiscal_date': import_data['fiscal_date'],
            'upload_time': import_data['upload_time']
        })

    except Exception as e:
        logger.error(f"خطأ في معاينة البيانات المستوردة: {e}")
        return jsonify({'success': False, 'message': f'خطأ في معاينة البيانات: {str(e)}'}), 500

@opening_balances_bp.route('/api/confirm_import', methods=['POST'])
def confirm_import():
    """تأكيد استيراد البيانات"""
    return jsonify({'success': False, 'message': 'ميزة الاستيراد قيد التطوير'}), 501
    try:
        import_data = session.get('excel_import_data')
        if not import_data:
            return jsonify({'success': False, 'message': 'لا توجد بيانات مستوردة للتأكيد'}), 400

        valid_records = import_data['valid_records']
        fiscal_date = import_data['fiscal_date']

        if not valid_records:
            return jsonify({'success': False, 'message': 'لا توجد سجلات صحيحة للاستيراد'}), 400

        oracle_mgr = get_oracle_manager()
        if not oracle_mgr.connect():
            return jsonify({'success': False, 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500

        success_count = 0
        error_count = 0
        errors = []

        for record in valid_records:
            try:
                # التحقق من عدم وجود رصيد مكرر
                check_query = """
                SELECT COUNT(*) FROM SUPPLIER_OPENING_BALANCES
                WHERE supplier_id = :supplier_id
                AND fiscal_period_start_date = TO_DATE(:fiscal_date, 'YYYY-MM-DD')
                AND currency_code = :currency_code
                AND is_active = 1
                """

                existing_count = oracle_mgr.execute_query(check_query, {
                    'supplier_id': record['supplier_id'],
                    'fiscal_date': fiscal_date,
                    'currency_code': record['currency_code']
                })

                if existing_count and existing_count[0][0] > 0:
                    errors.append(f"رصيد مكرر للمورد {record['supplier_code']} بالعملة {record['currency_code']}")
                    error_count += 1
                    continue

                # إدراج الرصيد الجديد
                insert_query = """
                INSERT INTO SUPPLIER_OPENING_BALANCES (
                    supplier_id, fiscal_period_start_date, currency_code,
                    opening_balance_amount, exchange_rate, notes,
                    reference_document, created_by, status
                ) VALUES (
                    :supplier_id, TO_DATE(:fiscal_date, 'YYYY-MM-DD'), :currency_code,
                    :amount, :exchange_rate, :notes,
                    :reference_doc, :user_id, 'DRAFT'
                )
                """

                oracle_mgr.execute_update(insert_query, {
                    'supplier_id': record['supplier_id'],
                    'fiscal_date': fiscal_date,
                    'currency_code': record['currency_code'],
                    'amount': record['opening_balance_amount'],
                    'exchange_rate': record.get('exchange_rate', 1.0),
                    'notes': record.get('notes', 'مستورد من Excel'),
                    'reference_doc': record.get('reference_document', ''),
                    'user_id': session.get('user_id', 1)
                })

                success_count += 1

            except Exception as e:
                error_count += 1
                errors.append(f"خطأ في المورد {record.get('supplier_code', 'غير محدد')}: {str(e)}")

        oracle_mgr.disconnect()

        # حذف البيانات من الجلسة
        session.pop('excel_import_data', None)

        return jsonify({
            'success': True,
            'success_count': success_count,
            'error_count': error_count,
            'errors': errors[:10],  # أول 10 أخطاء فقط
            'message': f'تم استيراد {success_count} رصيد بنجاح. {error_count} خطأ.'
        })

    except Exception as e:
        logger.error(f"خطأ في تأكيد الاستيراد: {e}")
        return jsonify({'success': False, 'message': f'خطأ في الاستيراد: {str(e)}'}), 500

@opening_balances_bp.route('/api/download_validation_report')
def download_validation_report():
    """تحميل تقرير التحقق من البيانات"""
    return jsonify({'success': False, 'message': 'ميزة الاستيراد قيد التطوير'}), 501
    try:
        import_data = session.get('excel_import_data')
        if not import_data:
            return jsonify({'success': False, 'message': 'لا توجد بيانات للتقرير'}), 400

        template_generator = OpeningBalancesExcelTemplate()
        report_file = template_generator.create_validation_report(
            len(import_data['valid_records']),
            import_data['invalid_records']
        )

        from flask import send_file
        return send_file(
            report_file,
            as_attachment=True,
            download_name=f'تقرير_التحقق_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        logger.error(f"خطأ في تحميل تقرير التحقق: {e}")
        return jsonify({'success': False, 'message': f'خطأ في إنشاء التقرير: {str(e)}'}), 500
