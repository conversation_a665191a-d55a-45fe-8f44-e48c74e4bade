"""
وحدة التحكم الصوتي الذكي
AI Voice Control Module
"""

from flask import Blueprint

def create_voice_blueprint():
    """إنشاء blueprint للتحكم الصوتي"""
    try:
        from .voice_routes import bp
        return bp
    except ImportError as e:
        # إنشاء blueprint فارغ في حالة عدم توفر المكتبات
        bp = Blueprint('ai_voice', __name__, url_prefix='/ai-voice')
        
        @bp.route('/purchase-contracts')
        def voice_not_available():
            from flask import render_template
            return render_template('errors/voice_not_available.html')
        
        return bp
