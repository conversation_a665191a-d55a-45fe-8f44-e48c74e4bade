<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاحات لوحة الشحنات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">اختبار إصلاحات لوحة الشحنات</h1>
        
        <!-- اختبار البحث السريع -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-search me-2"></i>اختبار البحث السريع</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">البحث السريع</label>
                            <input type="text" class="form-control" id="quickSearch" placeholder="ابحث هنا..." onkeyup="performQuickSearch()">
                            <div class="form-text">
                                جرب البحث عن: "أحمد"، "جدة"، "1000" (رقم حجز)، "MSKU" (رقم حاوية)، "MSK" (خط شحن)
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <button class="btn btn-primary" onclick="toggleView()">
                                <i class="fas fa-exchange-alt me-1"></i>
                                <span id="viewText">بطاقات</span>
                            </button>
                        </div>
                        
                        <!-- عرض الجدول -->
                        <div id="tableView">
                            <h6>وضع الجدول</h6>
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>رقم التتبع</th>
                                        <th>المرسل</th>
                                        <th>الوجهة</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>TRK001</td>
                                        <td>أحمد محمد</td>
                                        <td>جدة</td>
                                        <td>قيد الشحن</td>
                                        <td>
                                            <button class="btn btn-sm btn-info" onclick="manageDocuments('TRK001')">
                                                <i class="fas fa-file-alt"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>TRK002</td>
                                        <td>فاطمة علي</td>
                                        <td>الرياض</td>
                                        <td>تم التسليم</td>
                                        <td>
                                            <button class="btn btn-sm btn-info" onclick="manageDocuments('TRK002')">
                                                <i class="fas fa-file-alt"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>TRK003</td>
                                        <td>محمد حسن</td>
                                        <td>الدمام</td>
                                        <td>معلق</td>
                                        <td>
                                            <button class="btn btn-sm btn-info" onclick="manageDocuments('TRK003')">
                                                <i class="fas fa-file-alt"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- عرض البطاقات -->
                        <div id="cardsView" style="display: none;">
                            <h6>وضع البطاقات</h6>
                            <div class="mobile-cards-container">
                                <!-- سيتم ملء البطاقات بـ JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- نتائج الاختبار -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-clipboard-check me-2"></i>نتائج الاختبار</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <p class="text-muted">قم بتجربة البحث والتبديل بين الأوضاع لرؤية النتائج</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentView = 'table';
        
        // دالة عرض الإشعارات
        function showNotification(message, type = 'info') {
            const typeClasses = {
                'success': 'alert-success',
                'warning': 'alert-warning',
                'error': 'alert-danger',
                'info': 'alert-info'
            };

            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${typeClasses[type] || 'alert-info'} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                <i class="fas fa-info-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 4000);
        }
        
        // تبديل العرض
        function toggleView() {
            const tableView = document.getElementById('tableView');
            const cardsView = document.getElementById('cardsView');
            const viewText = document.getElementById('viewText');
            
            if (currentView === 'table') {
                tableView.style.display = 'none';
                cardsView.style.display = 'block';
                currentView = 'cards';
                viewText.textContent = 'جدول';
                
                createMobileCards();
                showNotification('تم التبديل إلى وضع البطاقات', 'success');
                
                // تطبيق البحث الحالي
                setTimeout(() => {
                    performQuickSearch();
                }, 100);
            } else {
                tableView.style.display = 'block';
                cardsView.style.display = 'none';
                currentView = 'table';
                viewText.textContent = 'بطاقات';
                
                showNotification('تم التبديل إلى وضع الجدول', 'success');
                
                // تطبيق البحث الحالي
                setTimeout(() => {
                    performQuickSearch();
                }, 100);
            }
        }
        
        // إنشاء البطاقات
        function createMobileCards() {
            const container = document.querySelector('.mobile-cards-container');
            const tableRows = document.querySelectorAll('#tableView tbody tr');

            container.innerHTML = '';

            tableRows.forEach((row, index) => {
                const cells = row.querySelectorAll('td');
                const trackingNumber = cells[0].textContent;
                const sender = cells[1].textContent;
                const destination = cells[2].textContent;
                const status = cells[3].textContent;

                // محاكاة cargo_id (في الواقع سيأتي من الجدول)
                const cargoId = index + 175; // محاكاة معرف الشحنة

                // جمع جميع البيانات للبحث (محاكاة البيانات الإضافية)
                let allRowData = '';
                cells.forEach(cell => {
                    allRowData += ' ' + cell.textContent.toLowerCase().trim();
                });
                // إضافة بيانات إضافية للاختبار
                allRowData += ` رقم_الحجز_${index + 1000} خط_الشحن_MSK حاوية_MSKU123456${index} ميناء_جدة_الإسلامي`;

                const card = document.createElement('div');
                card.className = 'shipment-card card mb-3';

                // حفظ بيانات البحث في البطاقة
                card.setAttribute('data-search-content', allRowData);

                card.innerHTML = `
                    <div class="card-body">
                        <h6 class="card-title">${trackingNumber}</h6>
                        <p class="card-text">
                            <strong>المرسل:</strong> ${sender}<br>
                            <strong>الوجهة:</strong> ${destination}<br>
                            <strong>الحالة:</strong> ${status}<br>
                            <strong>معرف الشحنة:</strong> ${cargoId}<br>
                            <small class="text-muted">بيانات إضافية: رقم حجز ${index + 1000}، حاوية MSKU123456${index}</small>
                        </p>
                        <button class="btn btn-sm btn-info" onclick="manageDocuments('${cargoId}')">
                            <i class="fas fa-file-alt me-1"></i>
                            إدارة الوثائق
                        </button>
                    </div>
                `;

                container.appendChild(card);
            });
        }
        
        // البحث السريع
        function performQuickSearch() {
            const searchInput = document.getElementById('quickSearch');
            const searchTerm = searchInput.value.toLowerCase().trim();
            
            let visibleCount = 0;
            
            if (currentView === 'cards') {
                const cards = document.querySelectorAll('.shipment-card');
                cards.forEach(card => {
                    if (searchTerm === '') {
                        card.style.display = '';
                        visibleCount++;
                    } else {
                        // البحث في البيانات الكاملة المحفوظة أولاً
                        const searchData = card.getAttribute('data-search-content');
                        let found = false;

                        if (searchData && searchData.includes(searchTerm)) {
                            found = true;
                        } else {
                            // البحث الاحتياطي في النص المرئي
                            const cardText = card.textContent.toLowerCase();
                            if (cardText.includes(searchTerm)) {
                                found = true;
                            }
                        }

                        if (found) {
                            card.style.display = '';
                            visibleCount++;
                        } else {
                            card.style.display = 'none';
                        }
                    }
                });

                updateTestResults(`البحث المحسن في البطاقات: عرض ${visibleCount} من ${cards.length} بطاقة (يشمل البيانات المخفية)`);
            } else {
                const rows = document.querySelectorAll('#tableView tbody tr');
                rows.forEach(row => {
                    if (searchTerm === '') {
                        row.style.display = '';
                        visibleCount++;
                    } else {
                        const rowText = row.textContent.toLowerCase();
                        if (rowText.includes(searchTerm)) {
                            row.style.display = '';
                            visibleCount++;
                        } else {
                            row.style.display = 'none';
                        }
                    }
                });
                
                updateTestResults(`البحث في الجدول: عرض ${visibleCount} من ${rows.length} صف`);
            }
            
            if (visibleCount === 0 && searchTerm !== '') {
                showNotification(`لم يتم العثور على نتائج للبحث: "${searchTerm}"`, 'warning');
            } else if (searchTerm !== '') {
                showNotification(`تم العثور على ${visibleCount} نتيجة`, 'success');
            }
        }
        
        // إدارة الوثائق
        function manageDocuments(cargoId) {
            console.log('📁 إدارة وثائق الشحنة، cargo_id:', cargoId);

            if (!cargoId || cargoId === 'null' || cargoId === 'undefined') {
                showNotification('❌ معرف الشحنة غير متوفر', 'error');
                updateTestResults(`❌ فشل: معرف الشحنة غير متوفر`);
                return;
            }

            const documentsUrl = `/shipments/cargo/${cargoId}/documents`;
            showNotification(`فتح إدارة الوثائق للشحنة: ${cargoId}`, 'info');
            updateTestResults(`✅ تم النقر على إدارة الوثائق للشحنة: ${cargoId}<br>الرابط الصحيح: ${documentsUrl}`);

            // في الاختبار، لا نفتح النافذة فعلياً
            console.log('الرابط الصحيح:', documentsUrl);
        }
        
        // تحديث نتائج الاختبار
        function updateTestResults(message) {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            resultsDiv.innerHTML += `<div class="alert alert-info">[${timestamp}] ${message}</div>`;
        }
    </script>
</body>
</html>
