<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار طلبات الحوالات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>اختبار طلبات الحوالات</h1>
        <button id="loadData" class="btn btn-primary">تحميل البيانات</button>
        <div id="result" class="mt-3"></div>
    </div>

    <script>
        document.getElementById('loadData').addEventListener('click', async function() {
            try {
                const response = await fetch('/transfers/api/transfer-requests');
                const data = await response.json();
                
                const resultDiv = document.getElementById('result');
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            تم تحميل ${data.count} طلب بنجاح
                        </div>
                        <pre>${JSON.stringify(data.data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            خطأ: ${data.message}
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <div class="alert alert-danger">
                        خطأ في الشبكة: ${error.message}
                    </div>
                `;
            }
        });
    </script>
</body>
</html>