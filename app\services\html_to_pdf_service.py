"""
خدمة تحويل HTML إلى PDF للنصوص العربية
تستخدم HTML/CSS مع خطوط عربية ثم تحفظ كـ HTML
"""

import os
from datetime import datetime
from typing import Dict, Optional


class HTMLToPDFService:
    """خدمة تحويل HTML إلى PDF للنصوص العربية"""
    
    def __init__(self):
        """تهيئة الخدمة"""
        pass
    
    def create_delivery_order_html(self, order_data: Dict) -> str:
        """إنشاء HTML لأمر التسليم بالعربية"""

        # تحضير البيانات
        order_number = order_data.get('order_number', 'غير محدد')
        tracking_number = order_data.get('tracking_number', 'غير محدد')
        booking_number = order_data.get('booking_number', 'غير محدد')
        agent_name = order_data.get('agent_name', 'غير محدد')
        agent_phone = order_data.get('agent_phone', 'غير محدد')
        delivery_location = order_data.get('delivery_location', 'غير محدد')
        expected_date = order_data.get('expected_completion_date', 'غير محدد')
        status_arabic = self._get_status_arabic(order_data.get('order_status', 'draft'))
        current_date = datetime.now().strftime('%Y-%m-%d')
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M')

        html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أمر تسليم - {order_number}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;600;700&display=swap');
        
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
            text-align: right;
            background: white;
            padding: 20px;
        }}
        
        .container {{
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        
        .header {{
            text-align: center;
            border-bottom: 3px solid #1f4e79;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        
        .company-name {{
            font-size: 28px;
            font-weight: 700;
            color: #1f4e79;
            margin-bottom: 10px;
        }}
        
        .document-title {{
            font-size: 22px;
            font-weight: 600;
            color: #2c5aa0;
            margin-bottom: 15px;
        }}
        
        .section {{
            margin: 25px 0;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }}
        
        .section-title {{
            font-size: 18px;
            font-weight: 600;
            color: #1f4e79;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e3f2fd;
        }}
        
        .data-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }}
        
        .data-table th,
        .data-table td {{
            padding: 12px;
            text-align: right;
            border: 1px solid #ddd;
        }}
        
        .data-table th {{
            background-color: #1f4e79;
            color: white;
            font-weight: 600;
        }}
        
        .data-table tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}
        
        .data-table tr:hover {{
            background-color: #e3f2fd;
        }}
        
        .order-info {{
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }}
        
        .shipment-info {{
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
        }}
        
        .agent-info {{
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
        }}
        
        .delivery-info {{
            background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
        }}
        
        .status-badge {{
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }}
        
        .status-draft {{ background: #6c757d; color: white; }}
        .status-sent {{ background: #007bff; color: white; }}
        .status-in_progress {{ background: #ffc107; color: #212529; }}
        .status-completed {{ background: #28a745; color: white; }}
        .status-cancelled {{ background: #dc3545; color: white; }}
        
        .footer {{
            border-top: 2px solid #1f4e79;
            padding-top: 20px;
            margin-top: 30px;
            text-align: center;
            font-size: 14px;
            color: #6c757d;
        }}
        
        .company-info {{
            margin-bottom: 10px;
            line-height: 1.8;
        }}
        
        .print-date {{
            font-size: 12px;
            color: #adb5bd;
            margin-top: 10px;
        }}
        
        @media print {{
            body {{ margin: 0; padding: 10px; }}
            .container {{ box-shadow: none; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <!-- رأس المستند -->
        <div class="header">
            <div class="company-name">شركة النقل والشحن المتطورة</div>
            <div class="document-title">أمر تسليم للمخلص الجمركي</div>
        </div>
        
        <!-- معلومات الأمر -->
        <div class="section order-info">
            <h3 class="section-title">📋 معلومات الأمر</h3>
            <table class="data-table">
                <tr>
                    <th>البيان</th>
                    <th>القيمة</th>
                </tr>
                <tr>
                    <td>رقم الأمر</td>
                    <td><strong>{order_number}</strong></td>
                </tr>
                <tr>
                    <td>تاريخ الإصدار</td>
                    <td>{current_date}</td>
                </tr>
                <tr>
                    <td>حالة الأمر</td>
                    <td><span class="status-badge">{status_arabic}</span></td>
                </tr>
            </table>
        </div>
        
        <!-- معلومات الشحنة -->
        <div class="section shipment-info">
            <h3 class="section-title">📦 بيانات الشحنة</h3>
            <table class="data-table">
                <tr>
                    <th>البيان</th>
                    <th>القيمة</th>
                </tr>
                <tr>
                    <td>رقم التتبع</td>
                    <td><strong>{tracking_number}</strong></td>
                </tr>
                <tr>
                    <td>رقم الحجز</td>
                    <td><strong>{booking_number}</strong></td>
                </tr>
                <tr>
                    <td>اسم المرسل</td>
                    <td>{order_data.get('sender_name', 'غير محدد')}</td>
                </tr>
                <tr>
                    <td>اسم المستقبل</td>
                    <td>{order_data.get('receiver_name', 'غير محدد')}</td>
                </tr>
                <tr>
                    <td>ميناء الشحن</td>
                    <td>{order_data.get('origin_port', 'غير محدد')}</td>
                </tr>
                <tr>
                    <td>ميناء الوصول</td>
                    <td>{order_data.get('destination_port', 'غير محدد')}</td>
                </tr>
                <tr>
                    <td>نوع الشحنة</td>
                    <td>{order_data.get('shipment_type', 'غير محدد')}</td>
                </tr>
                <tr>
                    <td>الوزن الإجمالي</td>
                    <td>{order_data.get('total_weight', 'غير محدد')} كيلو</td>
                </tr>
                <tr>
                    <td>عدد الطرود</td>
                    <td>{order_data.get('packages_count', 'غير محدد')}</td>
                </tr>
                <tr>
                    <td>وصف البضاعة</td>
                    <td>{order_data.get('cargo_description', 'غير محدد')}</td>
                </tr>
                <tr>
                    <td>قيمة البضاعة</td>
                    <td>{order_data.get('cargo_value', 'غير محدد')} ريال</td>
                </tr>
                <tr>
                    <td>تاريخ الوصول</td>
                    <td>{order_data.get('arrival_date', 'غير محدد')}</td>
                </tr>
            </table>
        </div>
        

        


        <!-- التعليمات والتوقيع -->
        <div class="section delivery-info">
            <h3 class="section-title">📋 تعليمات مهمة</h3>
            <div style="padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; margin-bottom: 20px;">
                <p style="margin: 5px 0; font-weight: bold;">يرجى مراعاة ما يلي:</p>
                <p style="margin: 5px 0;">• التأكد من صحة جميع البيانات المذكورة أعلاه</p>
                <p style="margin: 5px 0;">• إحضار هذا المستند عند استلام البضاعة</p>
                <p style="margin: 5px 0;">• التواصل فوراً في حالة وجود أي خطأ في البيانات</p>
                <p style="margin: 5px 0;">• الالتزام بالمواعيد المحددة للتخليص</p>
            </div>

            <table class="data-table" style="margin-top: 20px;">
                <tr>
                    <th style="width: 50%; padding: 20px;">توقيع المستلم</th>
                    <th style="width: 50%; padding: 20px;">التاريخ</th>
                </tr>
                <tr>
                    <td style="height: 60px; border: 1px solid #ddd;"></td>
                    <td style="height: 60px; border: 1px solid #ddd;"></td>
                </tr>
            </table>
        </div>

        <!-- تذييل المستند -->
        <div class="footer">
            <div class="company-info">
                <strong>شركة النقل والشحن المتطورة</strong><br>
                العنوان: المملكة العربية السعودية - الرياض<br>
                الهاتف: +966 11 123 4567 | البريد الإلكتروني: <EMAIL><br>
                الموقع الإلكتروني: www.shipping.com
            </div>
            <div class="print-date">
                تاريخ الطباعة: {current_time}
            </div>
        </div>
    </div>
</body>
</html>
        """
        
        return html_content
    
    def _get_status_arabic(self, status):
        """ترجمة حالة الأمر للعربية"""
        status_map = {
            'draft': 'مسودة',
            'sent': 'مرسل',
            'in_progress': 'قيد التنفيذ',
            'completed': 'مكتمل',
            'cancelled': 'ملغي'
        }
        return status_map.get(status, status)
    
    def create_delivery_order_file(self, order_data: Dict) -> str:
        """إنشاء ملف HTML لأمر التسليم وحفظه"""
        
        # إنشاء HTML
        html_content = self.create_delivery_order_html(order_data)
        
        # حفظ HTML في سطح المكتب
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        html_filename = f"delivery_order_{order_data.get('order_number', 'unknown')}.html"
        html_path = os.path.join(desktop_path, html_filename)
        
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return html_path


# إنشاء instance عام
html_to_pdf_service = HTMLToPDFService()


def generate_html_delivery_order(order_data: Dict) -> str:
    """دالة مساعدة لإنشاء ملف HTML لأمر التسليم"""
    return html_to_pdf_service.create_delivery_order_file(order_data)
