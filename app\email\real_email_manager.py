"""
مدير البريد الإلكتروني الحقيقي - يتصل بخوادم البريد الفعلية
"""
import imaplib
import smtplib
import email
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime
import logging
from typing import List, Dict, Optional, Tuple
import re

logger = logging.getLogger(__name__)

class RealEmailManager:
    """مدير البريد الإلكتروني الحقيقي"""
    
    def __init__(self):
        self.connections = {}  # تخزين الاتصالات المفتوحة
    
    def test_smtp_connection(self, account: Dict) -> Tuple[bool, str]:
        """اختبار اتصال SMTP حقيقي"""
        try:
            if account['smtp_use_ssl']:
                server = smtplib.SMTP_SSL(account['smtp_server'], account['smtp_port'])
            else:
                server = smtplib.SMTP(account['smtp_server'], account['smtp_port'])
                if account['smtp_use_tls']:
                    server.starttls()
            
            server.login(account['email_address'], account['password'])
            server.quit()
            
            return True, "نجح اتصال SMTP"
            
        except smtplib.SMTPAuthenticationError:
            return False, "فشل في المصادقة - تحقق من البريد الإلكتروني وكلمة المرور"
        except smtplib.SMTPConnectError:
            return False, f"فشل الاتصال بخادم SMTP {account['smtp_server']}:{account['smtp_port']}"
        except Exception as e:
            return False, f"خطأ في SMTP: {str(e)}"
    
    def test_imap_connection(self, account: Dict) -> Tuple[bool, str]:
        """اختبار اتصال IMAP حقيقي"""
        try:
            if account['imap_use_ssl']:
                mail = imaplib.IMAP4_SSL(account['imap_server'], account['imap_port'])
            else:
                mail = imaplib.IMAP4(account['imap_server'], account['imap_port'])
            
            mail.login(account['email_address'], account['password'])
            mail.logout()
            
            return True, "نجح اتصال IMAP"
            
        except imaplib.IMAP4.error as e:
            if "authentication failed" in str(e).lower():
                return False, "فشل في المصادقة - تحقق من البريد الإلكتروني وكلمة المرور"
            return False, f"خطأ في IMAP: {str(e)}"
        except Exception as e:
            return False, f"فشل الاتصال بخادم IMAP {account['imap_server']}:{account['imap_port']}: {str(e)}"
    
    def test_connection(self, account: Dict) -> Dict:
        """اختبار الاتصال الكامل"""
        smtp_success, smtp_message = self.test_smtp_connection(account)
        imap_success, imap_message = self.test_imap_connection(account)
        
        overall_success = smtp_success and imap_success
        
        if overall_success:
            message = "✅ نجح الاتصال بجميع الخوادم"
        else:
            message = f"❌ {smtp_message} | {imap_message}"
        
        return {
            'success': overall_success,
            'message': message,
            'details': {
                'smtp': {'success': smtp_success, 'message': smtp_message},
                'imap': {'success': imap_success, 'message': imap_message}
            }
        }
    
    def send_email(self, account: Dict, to_emails: List[str], subject: str, 
                   body: str, cc_emails: List[str] = None, bcc_emails: List[str] = None,
                   attachments: List = None) -> Dict:
        """إرسال بريد إلكتروني حقيقي"""
        try:
            # إنشاء الرسالة
            msg = MIMEMultipart()
            msg['From'] = f"{account['display_name']} <{account['email_address']}>"
            msg['To'] = ', '.join(to_emails)
            msg['Subject'] = subject
            
            if cc_emails:
                msg['Cc'] = ', '.join(cc_emails)
            
            # إضافة النص
            msg.attach(MIMEText(body, 'html' if '<' in body else 'plain', 'utf-8'))
            
            # إضافة المرفقات
            if attachments:
                for attachment in attachments:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment['content'])
                    encoders.encode_base64(part)
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename= {attachment["filename"]}'
                    )
                    msg.attach(part)
            
            # الاتصال بخادم SMTP
            if account['smtp_use_ssl']:
                server = smtplib.SMTP_SSL(account['smtp_server'], account['smtp_port'])
            else:
                server = smtplib.SMTP(account['smtp_server'], account['smtp_port'])
                if account['smtp_use_tls']:
                    server.starttls()
            
            server.login(account['email_address'], account['password'])
            
            # إرسال الرسالة
            all_recipients = to_emails + (cc_emails or []) + (bcc_emails or [])
            text = msg.as_string()
            server.sendmail(account['email_address'], all_recipients, text)
            server.quit()
            
            return {
                'success': True,
                'message': f'تم إرسال الرسالة إلى {len(all_recipients)} مستقبل'
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في إرسال البريد: {e}")
            return {
                'success': False,
                'message': f'فشل في إرسال الرسالة: {str(e)}'
            }
    
    def fetch_emails(self, account: Dict, folder: str = 'INBOX', limit: int = 50) -> List[Dict]:
        """جلب الرسائل الحقيقية من الخادم"""
        try:
            # الاتصال بخادم IMAP
            if account['imap_use_ssl']:
                mail = imaplib.IMAP4_SSL(account['imap_server'], account['imap_port'])
            else:
                mail = imaplib.IMAP4(account['imap_server'], account['imap_port'])
            
            mail.login(account['email_address'], account['password'])
            mail.select(folder)
            
            # البحث عن الرسائل
            status, messages = mail.search(None, 'ALL')
            
            if status != 'OK':
                return []
            
            message_ids = messages[0].split()
            
            # جلب آخر الرسائل
            recent_ids = message_ids[-limit:] if len(message_ids) > limit else message_ids
            recent_ids.reverse()  # الأحدث أولاً
            
            emails = []
            
            for msg_id in recent_ids:
                try:
                    status, msg_data = mail.fetch(msg_id, '(RFC822)')
                    
                    if status != 'OK':
                        continue
                    
                    # تحليل الرسالة
                    email_message = email.message_from_bytes(msg_data[0][1])
                    
                    # استخراج البيانات
                    subject = self._decode_header(email_message.get('Subject', 'بدون موضوع'))
                    sender = self._decode_header(email_message.get('From', ''))
                    date_str = email_message.get('Date', '')
                    
                    # تحليل المرسل
                    sender_email, sender_name = self._parse_sender(sender)
                    
                    # تحليل التاريخ
                    received_date = self._parse_date(date_str)
                    
                    # استخراج النص
                    body = self._extract_body(email_message)
                    
                    # التحقق من المرفقات
                    has_attachments = self._has_attachments(email_message)
                    
                    # التحقق من حالة القراءة
                    is_read = '\\Seen' in str(msg_data)
                    
                    email_data = {
                        'server_id': msg_id.decode(),
                        'subject': subject,
                        'sender_email': sender_email,
                        'sender_name': sender_name,
                        'received_at': received_date,
                        'body_text': body,
                        'body_html': body,  # سيتم تحسينه لاحقاً
                        'is_read': is_read,
                        'is_important': False,  # سيتم تحديده لاحقاً
                        'has_attachments': has_attachments
                    }
                    
                    emails.append(email_data)
                    
                except Exception as e:
                    logger.error(f"خطأ في معالجة الرسالة {msg_id}: {e}")
                    continue
            
            mail.logout()
            return emails
            
        except Exception as e:
            logger.error(f"❌ خطأ في جلب الرسائل: {e}")
            return []
    
    def _decode_header(self, header: str) -> str:
        """فك تشفير رأس الرسالة"""
        if not header:
            return ''
        
        try:
            decoded_parts = email.header.decode_header(header)
            decoded_string = ''
            
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        decoded_string += part.decode(encoding)
                    else:
                        decoded_string += part.decode('utf-8', errors='ignore')
                else:
                    decoded_string += part
            
            return decoded_string
        except:
            return header
    
    def _parse_sender(self, sender: str) -> Tuple[str, str]:
        """تحليل بيانات المرسل"""
        if not sender:
            return '', ''
        
        # <AUTHOR> <EMAIL>
        match = re.search(r'(.+?)\s*<(.+?)>', sender)
        if match:
            name = match.group(1).strip().strip('"')
            email_addr = match.group(2).strip()
            return email_addr, name
        
        # إذا كان البريد فقط
        if '@' in sender:
            return sender.strip(), sender.strip()
        
        return '', sender
    
    def _parse_date(self, date_str: str) -> datetime:
        """تحليل تاريخ الرسالة"""
        try:
            # تحليل تاريخ RFC 2822
            return email.utils.parsedate_to_datetime(date_str)
        except:
            return datetime.now()
    
    def _extract_body(self, email_message) -> str:
        """استخراج نص الرسالة"""
        try:
            if email_message.is_multipart():
                for part in email_message.walk():
                    if part.get_content_type() == "text/plain":
                        payload = part.get_payload(decode=True)
                        if payload:
                            return payload.decode('utf-8', errors='ignore')
            else:
                payload = email_message.get_payload(decode=True)
                if payload:
                    return payload.decode('utf-8', errors='ignore')
        except:
            pass
        
        return 'لا يمكن عرض محتوى الرسالة'
    
    def _has_attachments(self, email_message) -> bool:
        """التحقق من وجود مرفقات"""
        try:
            if email_message.is_multipart():
                for part in email_message.walk():
                    if part.get_content_disposition() == 'attachment':
                        return True
        except:
            pass
        
        return False
    
    def get_folders(self, account: Dict) -> List[Dict]:
        """جلب قائمة المجلدات من الخادم"""
        try:
            if account['imap_use_ssl']:
                mail = imaplib.IMAP4_SSL(account['imap_server'], account['imap_port'])
            else:
                mail = imaplib.IMAP4(account['imap_server'], account['imap_port'])
            
            mail.login(account['email_address'], account['password'])
            
            # جلب قائمة المجلدات
            status, folders = mail.list()
            
            if status != 'OK':
                return []
            
            folder_list = []
            for folder in folders:
                # تحليل اسم المجلد
                folder_str = folder.decode()
                parts = folder_str.split('"')
                if len(parts) >= 3:
                    folder_name = parts[-2]
                    folder_list.append({
                        'name': folder_name,
                        'display_name': self._translate_folder_name(folder_name)
                    })
            
            mail.logout()
            return folder_list
            
        except Exception as e:
            logger.error(f"❌ خطأ في جلب المجلدات: {e}")
            return []
    
    def _translate_folder_name(self, folder_name: str) -> str:
        """ترجمة أسماء المجلدات الشائعة"""
        translations = {
            'INBOX': 'صندوق الوارد',
            'Sent': 'المرسل',
            'Drafts': 'المسودات',
            'Trash': 'المحذوفات',
            'Spam': 'الرسائل المزعجة',
            'Junk': 'الرسائل المزعجة'
        }
        
        return translations.get(folder_name, folder_name)
