# دليل المستخدم - نظام الترحيل المحاسبي للحوالات
## User Manual - Transfer Accounting System

---

## 📋 **نظرة عامة**

نظام الترحيل المحاسبي للحوالات هو نظام متكامل يدير العمليات المحاسبية للحوالات المالية بين الموردين والصرافين، مع ضمان دقة الترحيلات وسلامة الأرصدة.

### **الميزات الرئيسية:**
- ✅ ترحيل تلقائي للأرصدة عند تنفيذ الحوالات
- ✅ إمكانية إلغاء الحوالات وعكس الترحيلات
- ✅ التحقق من كفاية الأرصدة قبل التنفيذ
- ✅ تتبع شامل لجميع العمليات والأنشطة
- ✅ تقارير مفصلة للأرصدة والحوالات
- ✅ دعم عملات متعددة

---

## 🚀 **البدء السريع**

### **1. الوصول للنظام**
1. افتح المتصفح وانتقل إلى رابط النظام
2. سجل دخولك باستخدام بيانات المستخدم
3. انتقل إلى قسم "الحوالات" → "تنفيذ الحوالات"

### **2. عرض الحوالات المعتمدة**
- ستظهر قائمة بجميع الحوالات المعتمدة والجاهزة للتنفيذ
- يمكنك رؤية تفاصيل كل حوالة: المبلغ، المستفيد، البنك، الأولوية
- الأزرار المتاحة تختلف حسب حالة الحوالة

---

## 🔧 **العمليات الأساسية**

### **📤 تنفيذ الحوالة**

#### **الخطوات:**
1. **اختيار الحوالة**: انقر على زر "تنفيذ" (▶️) بجانب الحوالة المطلوبة
2. **اختيار الصراف**: حدد الصراف/البنك الذي سيقوم بالتحويل
3. **توزيع المبالغ**: وزع مبلغ الحوالة على الموردين المختلفين
4. **التحقق**: سيتم التحقق تلقائياً من:
   - كفاية رصيد الصراف
   - صحة توزيع المبالغ
   - وجود الموردين في النظام
5. **التنفيذ**: انقر على "تنفيذ الحوالة" لإتمام العملية

#### **ما يحدث عند التنفيذ:**
- 💰 **زيادة أرصدة الموردين** بالمبالغ المخصصة لهم
- 💸 **تقليل رصيد الصراف** بمبلغ الحوالة الكلي
- 📝 **تسجيل جميع العمليات** في سجل الأنشطة
- 🔄 **تحديث حالة الحوالة** إلى "منفذة"

### **❌ إلغاء الحوالة**

#### **متى يمكن الإلغاء:**
- ✅ الحوالة منفذة (executed)
- ✅ لم تمر فترة طويلة على التنفيذ
- ✅ لا توجد معاملات لاحقة تعتمد على هذه الحوالة

#### **الخطوات:**
1. **انقر على زر الإلغاء** (❌) بجانب الحوالة المنفذة
2. **التحقق التلقائي**: سيتحقق النظام من إمكانية الإلغاء
3. **إدخال السبب**: اكتب سبب الإلغاء (اختياري)
4. **التأكيد**: أكد عملية الإلغاء

#### **ما يحدث عند الإلغاء:**
- 🔄 **عكس جميع الترحيلات** المحاسبية
- 💰 **إرجاع المبالغ** من أرصدة الموردين
- 💸 **إرجاع المبلغ** لرصيد الصراف
- 📝 **تسجيل عملية الإلغاء** مع السبب
- 🔄 **تحديث حالة الحوالة** إلى "ملغاة"

### **👁️ عرض التفاصيل**

#### **معلومات الحوالة:**
- 📊 **البيانات الأساسية**: رقم الحوالة، المبلغ، العملة، الحالة
- 👥 **توزيعات الموردين**: قائمة بالموردين والمبالغ المخصصة
- 📈 **سجل الأنشطة**: تاريخ مفصل لجميع العمليات على الحوالة
- ⏰ **التواريخ المهمة**: الإنشاء، الاعتماد، التنفيذ، الإلغاء

---

## 📊 **التقارير والاستعلامات**

### **تقرير الأرصدة الحالية**
- 💰 **أرصدة الموردين**: الرصيد الحالي لكل مورد بكل عملة
- 🏦 **أرصدة الصرافين**: الرصيد المتاح والحوالات المعلقة
- 📈 **الإحصائيات**: إجمالي الأرصدة وتوزيعها

### **تقرير ملخص الحوالات**
- 📋 **قائمة الحوالات**: حسب الفترة الزمنية والحالة
- 📊 **الإحصائيات**: عدد الحوالات والمبالغ حسب الحالة
- 💱 **تجميع العملات**: ملخص لكل عملة على حدة

### **تقرير نشاط الموردين**
- 👥 **قائمة الموردين**: مع عدد الحوالات والمبالغ
- 📈 **الاتجاهات**: أكثر الموردين نشاطاً
- 💰 **الأرصدة**: الرصيد الحالي لكل مورد

### **تقرير نشاط الصرافين**
- 🏦 **قائمة الصرافين**: مع عدد الحوالات المنفذة
- 💸 **المبالغ المحولة**: إجمالي المبالغ لكل صراف
- 📊 **الرصيد المتاح**: الرصيد الحالي والحوالات المعلقة

---

## ⚠️ **التحذيرات والتنبيهات**

### **تحذيرات الرصيد:**
- 🔴 **رصيد غير كافي**: عندما يكون رصيد الصراف أقل من مبلغ الحوالة
- 🟡 **رصيد منخفض**: عندما يقترب الرصيد من الحد الأدنى
- 🟠 **حوالات معلقة**: عندما توجد حوالات معتمدة غير منفذة

### **تحذيرات التوزيع:**
- ❌ **عدم تطابق المبالغ**: مجموع التوزيعات لا يساوي مبلغ الحوالة
- ⚠️ **مورد غير موجود**: المورد المحدد غير مسجل في النظام
- 🔍 **بيانات ناقصة**: معلومات مطلوبة مفقودة

### **تحذيرات الإلغاء:**
- ⏰ **فترة طويلة**: مرت فترة طويلة على تنفيذ الحوالة
- 🔗 **معاملات لاحقة**: توجد معاملات أخرى تعتمد على هذه الحوالة
- 💰 **رصيد غير كافي**: رصيد المورد أقل من المبلغ المطلوب عكسه

---

## 🔒 **الأمان والصلاحيات**

### **مستويات الصلاحيات:**
1. **مشاهد**: يمكنه عرض الحوالات والتقارير فقط
2. **منفذ**: يمكنه تنفيذ الحوالات المعتمدة
3. **مدير**: يمكنه تنفيذ وإلغاء الحوالات
4. **مدير عام**: صلاحيات كاملة على النظام

### **سجل الأنشطة:**
- 📝 **تسجيل شامل**: جميع العمليات مسجلة مع التفاصيل
- 👤 **تتبع المستخدم**: من قام بكل عملية ومتى
- 🌐 **معلومات الجلسة**: عنوان IP ومعلومات المتصفح
- 🔍 **قابلية التدقيق**: إمكانية مراجعة جميع العمليات

---

## 🆘 **استكشاف الأخطاء وحلها**

### **مشاكل شائعة وحلولها:**

#### **"رصيد الصراف غير كافي"**
- ✅ **تحقق من الرصيد الحالي** للصراف
- ✅ **راجع الحوالات المعلقة** التي تؤثر على الرصيد
- ✅ **اختر صراف آخر** برصيد كافي

#### **"مجموع التوزيعات لا يطابق مبلغ الحوالة"**
- ✅ **راجع المبالغ المدخلة** للموردين
- ✅ **تأكد من صحة الحسابات** الرياضية
- ✅ **استخدم الآلة الحاسبة** للتحقق

#### **"لا يمكن إلغاء الحوالة"**
- ✅ **تحقق من حالة الحوالة** (يجب أن تكون منفذة)
- ✅ **راجع التحذيرات** المعروضة
- ✅ **تواصل مع المدير** إذا كانت العملية ضرورية

#### **"خطأ في الاتصال بالخادم"**
- ✅ **تحقق من الاتصال بالإنترنت**
- ✅ **حدث الصفحة** وحاول مرة أخرى
- ✅ **تواصل مع الدعم الفني** إذا استمر الخطأ

---

## 📞 **الدعم والمساعدة**

### **للحصول على المساعدة:**
1. **راجع هذا الدليل** أولاً للحلول الشائعة
2. **تحقق من سجل الأنشطة** لفهم ما حدث
3. **تواصل مع فريق الدعم** مع تفاصيل المشكلة
4. **احتفظ برقم الحوالة** عند الإبلاغ عن مشكلة

### **معلومات مهمة للدعم:**
- 🆔 **رقم الحوالة** المتأثرة
- ⏰ **وقت حدوث المشكلة**
- 👤 **اسم المستخدم** الذي واجه المشكلة
- 🖥️ **نوع المتصفح** المستخدم
- 📱 **رسالة الخطأ** الكاملة إن وجدت

---

## 🔄 **التحديثات والتطوير**

### **آخر التحديثات:**
- ✨ **نظام الترحيل المحاسبي الجديد**
- 🔍 **تحسين التحقق من الأرصدة**
- 📊 **تقارير محسنة ومفصلة**
- 🔒 **تعزيز الأمان وسجل الأنشطة**

### **التطويرات المستقبلية:**
- 📱 **تطبيق الهاتف المحمول**
- 🔔 **نظام الإشعارات التلقائية**
- 🤖 **الذكاء الاصطناعي للتنبؤ**
- 🌐 **واجهة برمجة التطبيقات المفتوحة**

---

*تم إعداد هذا الدليل بواسطة فريق تطوير نظام SASERP*  
*آخر تحديث: 2025-09-07*
