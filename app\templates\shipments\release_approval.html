{% extends "base.html" %}

{% block title %}اعتماد الإفراج{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-stamp me-2 text-danger"></i>
                        اعتماد الإفراج
                    </h2>
                    <p class="text-muted mb-0">اعتماد ورفض طلبات إفراج الشحنات</p>
                </div>
                <div>
                    <button class="btn btn-success me-2" onclick="bulkApprove()">
                        <i class="fas fa-check me-1"></i>
                        اعتماد متعدد
                    </button>
                    <button class="btn btn-danger" onclick="bulkReject()">
                        <i class="fas fa-times me-1"></i>
                        رفض متعدد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4>{{ approval_shipments|length }}</h4>
                    <p class="mb-0">في انتظار الاعتماد</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4>0</h4>
                    <p class="mb-0">تم الاعتماد اليوم</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-danger text-white">
                <div class="card-body text-center">
                    <i class="fas fa-times-circle fa-2x mb-2"></i>
                    <h4>0</h4>
                    <p class="mb-0">تم الرفض اليوم</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-percentage fa-2x mb-2"></i>
                    <h4>100%</h4>
                    <p class="mb-0">معدل الاعتماد</p>
                </div>
            </div>
        </div>
    </div>

    <!-- الشحنات المحتاجة للاعتماد -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list-check me-2"></i>
                            الشحنات المحتاجة للاعتماد
                        </h5>
                        <div>
                            <span class="badge bg-warning">{{ approval_shipments|length }} شحنة</span>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if approval_shipments %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>رقم التتبع</th>
                                    <th>المرسل</th>
                                    <th>المستلم</th>
                                    <th>حالة الإفراج</th>
                                    <th>حالة الشحنة</th>
                                    <th>ملاحظات</th>
                                    <th>تاريخ الطلب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for shipment in approval_shipments %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="approval-checkbox" value="{{ shipment[0] }}">
                                    </td>
                                    <td>
                                        <strong>{{ shipment[1] or 'غير محدد' }}</strong>
                                    </td>
                                    <td>{{ shipment[2] or 'غير محدد' }}</td>
                                    <td>{{ shipment[3] or 'غير محدد' }}</td>
                                    <td>
                                        <span class="badge" style="background-color: {{ shipment[6] or '#6c757d' }}; color: white;">
                                            {{ shipment[5] or 'غير محدد' }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ shipment[9] or 'غير محدد' }}
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ shipment[7][:50] + '...' if shipment[7] and shipment[7]|length > 50 else (shipment[7] or 'لا توجد ملاحظات') }}
                                        </small>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ shipment[8].strftime('%Y-%m-%d %H:%M') if shipment[8] else 'غير محدد' }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-success btn-sm" 
                                                    onclick="approveRelease({{ shipment[0] }})">
                                                <i class="fas fa-check"></i>
                                                اعتماد
                                            </button>
                                            <button class="btn btn-danger btn-sm" 
                                                    onclick="rejectRelease({{ shipment[0] }})">
                                                <i class="fas fa-times"></i>
                                                رفض
                                            </button>
                                            <button class="btn btn-info btn-sm" 
                                                    onclick="showApprovalDetails({{ shipment[0] }})">
                                                <i class="fas fa-eye"></i>
                                                تفاصيل
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                        <h4 class="text-muted">لا توجد شحنات تحتاج اعتماد</h4>
                        <p class="text-muted">جميع الشحنات تم اعتمادها أو لا تحتاج اعتماد حالياً</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة اعتماد الإفراج -->
<div id="approvalOverlay" class="custom-modal-overlay" style="display: none;">
    <div class="custom-modal">
        <div class="custom-modal-header">
            <h5>
                <i class="fas fa-check-circle me-2"></i>
                اعتماد الإفراج
            </h5>
            <button type="button" class="custom-close-btn" onclick="closeApprovalModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="custom-modal-body">
            <div id="approvalAlert" class="alert alert-info" style="display: none;">
                <i class="fas fa-info-circle me-2"></i>
                <span id="approvalMessage"></span>
            </div>
            
            <form id="approvalForm">
                <input type="hidden" id="approvalShipmentId" name="shipment_id">
                <input type="hidden" id="approvalAction" name="action">
                
                <div class="mb-3">
                    <label class="form-label">
                        <i class="fas fa-comment me-1"></i>
                        ملاحظات الاعتماد
                    </label>
                    <textarea class="form-control" name="approval_notes" rows="3" 
                              placeholder="أي ملاحظات على قرار الاعتماد..."></textarea>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="notifyCustomer" name="notify_customer" checked>
                        <label class="form-check-label" for="notifyCustomer">
                            إشعار العميل بالقرار
                        </label>
                    </div>
                </div>
            </form>
        </div>
        
        <div class="custom-modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeApprovalModal()">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </button>
            <button type="button" class="btn btn-success" id="submitApprovalBtn" onclick="submitApproval()">
                <i class="fas fa-check me-1"></i>
                تأكيد الاعتماد
            </button>
        </div>
    </div>
</div>

<!-- نافذة رفض الإفراج -->
<div id="rejectionOverlay" class="custom-modal-overlay" style="display: none;">
    <div class="custom-modal">
        <div class="custom-modal-header bg-danger">
            <h5>
                <i class="fas fa-times-circle me-2"></i>
                رفض الإفراج
            </h5>
            <button type="button" class="custom-close-btn" onclick="closeRejectionModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="custom-modal-body">
            <div id="rejectionAlert" class="alert alert-info" style="display: none;">
                <i class="fas fa-info-circle me-2"></i>
                <span id="rejectionMessage"></span>
            </div>
            
            <form id="rejectionForm">
                <input type="hidden" id="rejectionShipmentId" name="shipment_id">
                <input type="hidden" name="action" value="reject">
                
                <div class="mb-3">
                    <label class="form-label">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        سبب الرفض <span class="text-danger">*</span>
                    </label>
                    <select class="form-select" name="rejection_reason" required>
                        <option value="">اختر سبب الرفض</option>
                        <option value="incomplete_documents">مستندات غير مكتملة</option>
                        <option value="payment_issues">مشاكل في الدفع</option>
                        <option value="quality_issues">مشاكل في الجودة</option>
                        <option value="security_concerns">مخاوف أمنية</option>
                        <option value="policy_violation">مخالفة للسياسات</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">
                        <i class="fas fa-comment me-1"></i>
                        تفاصيل الرفض <span class="text-danger">*</span>
                    </label>
                    <textarea class="form-control" name="rejection_notes" rows="3" required
                              placeholder="يرجى توضيح أسباب الرفض بالتفصيل..."></textarea>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="notifyCustomerReject" name="notify_customer" checked>
                        <label class="form-check-label" for="notifyCustomerReject">
                            إشعار العميل بالرفض
                        </label>
                    </div>
                </div>
            </form>
        </div>
        
        <div class="custom-modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeRejectionModal()">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </button>
            <button type="button" class="btn btn-danger" id="submitRejectionBtn" onclick="submitRejection()">
                <i class="fas fa-times me-1"></i>
                تأكيد الرفض
            </button>
        </div>
    </div>
</div>

<script>
// تحديد الكل
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.approval-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// اعتماد الإفراج
function approveRelease(shipmentId) {
    document.getElementById('approvalShipmentId').value = shipmentId;
    document.getElementById('approvalAction').value = 'approve';
    
    const overlay = document.getElementById('approvalOverlay');
    overlay.style.display = 'flex';
    
    setTimeout(() => {
        overlay.classList.add('show');
    }, 10);
}

// رفض الإفراج
function rejectRelease(shipmentId) {
    document.getElementById('rejectionShipmentId').value = shipmentId;
    
    const overlay = document.getElementById('rejectionOverlay');
    overlay.style.display = 'flex';
    
    setTimeout(() => {
        overlay.classList.add('show');
    }, 10);
}

// إغلاق نافذة الاعتماد
function closeApprovalModal() {
    const overlay = document.getElementById('approvalOverlay');
    overlay.classList.remove('show');
    
    setTimeout(() => {
        overlay.style.display = 'none';
    }, 300);
}

// إغلاق نافذة الرفض
function closeRejectionModal() {
    const overlay = document.getElementById('rejectionOverlay');
    overlay.classList.remove('show');
    
    setTimeout(() => {
        overlay.style.display = 'none';
    }, 300);
}

// إرسال الاعتماد
function submitApproval() {
    const form = document.getElementById('approvalForm');
    const submitBtn = document.getElementById('submitApprovalBtn');
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري الاعتماد...';
    
    const formData = new FormData(form);
    formData.append('new_release_status', 'released');
    
    fetch('/shipments/api/update-release-status', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showApprovalAlert('تم اعتماد الإفراج بنجاح!', 'success');
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showApprovalAlert('خطأ: ' + (data.message || 'فشل في الاعتماد'), 'danger');
        }
    })
    .catch(error => {
        showApprovalAlert('حدث خطأ في الاتصال بالخادم', 'danger');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-check me-1"></i> تأكيد الاعتماد';
    });
}

// إرسال الرفض
function submitRejection() {
    const form = document.getElementById('rejectionForm');
    const submitBtn = document.getElementById('submitRejectionBtn');
    
    const reason = form.querySelector('[name="rejection_reason"]').value;
    const notes = form.querySelector('[name="rejection_notes"]').value;
    
    if (!reason || !notes.trim()) {
        showRejectionAlert('يرجى تحديد سبب الرفض وكتابة التفاصيل', 'warning');
        return;
    }
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري الرفض...';
    
    const formData = new FormData(form);
    formData.append('new_release_status', 'rejected');
    formData.append('release_notes', `سبب الرفض: ${reason}\nالتفاصيل: ${notes}`);
    
    fetch('/shipments/api/update-release-status', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showRejectionAlert('تم رفض الإفراج بنجاح!', 'success');
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showRejectionAlert('خطأ: ' + (data.message || 'فشل في الرفض'), 'danger');
        }
    })
    .catch(error => {
        showRejectionAlert('حدث خطأ في الاتصال بالخادم', 'danger');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-times me-1"></i> تأكيد الرفض';
    });
}

// عرض رسائل الاعتماد
function showApprovalAlert(message, type) {
    const alertDiv = document.getElementById('approvalAlert');
    const messageSpan = document.getElementById('approvalMessage');
    
    alertDiv.className = `alert alert-${type}`;
    messageSpan.textContent = message;
    alertDiv.style.display = 'block';
}

// عرض رسائل الرفض
function showRejectionAlert(message, type) {
    const alertDiv = document.getElementById('rejectionAlert');
    const messageSpan = document.getElementById('rejectionMessage');
    
    alertDiv.className = `alert alert-${type}`;
    messageSpan.textContent = message;
    alertDiv.style.display = 'block';
}

// عرض تفاصيل الاعتماد
function showApprovalDetails(shipmentId) {
    alert('تفاصيل الاعتماد - قريباً\nسيعرض جميع تفاصيل الشحنة والمستندات المطلوبة');
}

// اعتماد متعدد
function bulkApprove() {
    const selected = document.querySelectorAll('.approval-checkbox:checked');
    if (selected.length === 0) {
        alert('يرجى تحديد شحنة واحدة على الأقل');
        return;
    }
    
    alert('الاعتماد المتعدد - قريباً');
}

// رفض متعدد
function bulkReject() {
    const selected = document.querySelectorAll('.approval-checkbox:checked');
    if (selected.length === 0) {
        alert('يرجى تحديد شحنة واحدة على الأقل');
        return;
    }
    
    alert('الرفض المتعدد - قريباً');
}

// إعداد الأحداث
document.addEventListener('DOMContentLoaded', function() {
    // إعداد أحداث النوافذ
    ['approvalOverlay', 'rejectionOverlay'].forEach(overlayId => {
        const overlay = document.getElementById(overlayId);
        if (overlay) {
            overlay.addEventListener('click', function(e) {
                if (e.target === overlay) {
                    if (overlayId === 'approvalOverlay') {
                        closeApprovalModal();
                    } else {
                        closeRejectionModal();
                    }
                }
            });
        }
    });
    
    // إعداد مفتاح Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const approvalOverlay = document.getElementById('approvalOverlay');
            const rejectionOverlay = document.getElementById('rejectionOverlay');
            
            if (approvalOverlay && approvalOverlay.style.display !== 'none') {
                closeApprovalModal();
            } else if (rejectionOverlay && rejectionOverlay.style.display !== 'none') {
                closeRejectionModal();
            }
        }
    });
});
</script>

<style>
/* النافذة المخصصة */
.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.custom-modal-overlay.show {
    opacity: 1;
}

.custom-modal {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.7);
    transition: transform 0.3s ease;
}

.custom-modal-overlay.show .custom-modal {
    transform: scale(1);
}

.custom-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #dee2e6;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.custom-modal-header.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.custom-modal-header h5 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.custom-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.custom-close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.custom-modal-body {
    padding: 1.5rem;
}

.custom-modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0 0 12px 12px;
}
</style>
{% endblock %}
