#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Routes لإدارة وثائق الشحنات - Cargo Document Routes
"""

from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from oracle_manager import get_oracle_manager
from app.shipments.cloud_link_manager import cloud_link_manager
import logging
import hashlib
import time

logger = logging.getLogger(__name__)

# إنشاء blueprint لوثائق الشحنات
cargo_documents_bp = Blueprint('cargo_documents', __name__)

def safe_convert_lob(value):
    """تحويل آمن لقيم LOB"""
    if value is None:
        return None
    try:
        if hasattr(value, 'read'):
            return value.read().decode('utf-8')
        return str(value)
    except:
        return str(value) if value else None

@cargo_documents_bp.route('/shipments/cargo/documents/<int:document_id>/create-link', methods=['POST'])
@login_required
def create_cargo_document_share_link(document_id):
    """إنشاء رابط مشاركة لوثيقة شحنة"""
    logger.info(f"🔗 طلب إنشاء رابط للوثيقة {document_id}")
    try:
        oracle_manager = get_oracle_manager()

        # الحصول على نوع الخدمة
        data = request.get_json()
        service = data.get('service', 'nextcloud')  # nextcloud أو onedrive
        logger.info(f"📝 البيانات المستلمة: {data}")
        logger.info(f"🔧 نوع الخدمة: {service}")

        # التحقق من وجود الوثيقة
        doc_query = """
            SELECT id, file_path, document_name, share_link, nextcloud_share_link, onedrive_share_link
            FROM cargo_shipment_documents
            WHERE id = :document_id
        """
        doc_result = oracle_manager.execute_query(doc_query, {'document_id': document_id})

        if not doc_result:
            logger.warning(f"⚠️ الوثيقة {document_id} غير موجودة")
            return jsonify({
                'success': False,
                'message': 'الوثيقة غير موجودة'
            }), 404

        doc = doc_result[0]
        file_path = safe_convert_lob(doc[1])
        file_name = safe_convert_lob(doc[2])
        existing_general_link = safe_convert_lob(doc[3])
        existing_nextcloud_link = safe_convert_lob(doc[4])
        existing_onedrive_link = safe_convert_lob(doc[5])

        logger.info(f"📄 معلومات الوثيقة: {file_name}")
        logger.info(f"📁 مسار الملف: {file_path}")

        # التحقق من وجود رابط مسبق للخدمة المحددة
        existing_link = None
        if service == 'nextcloud' and existing_nextcloud_link:
            existing_link = existing_nextcloud_link.strip()
        elif service == 'onedrive' and existing_onedrive_link:
            existing_link = existing_onedrive_link.strip()

        if existing_link:
            logger.info(f"✅ رابط {service} موجود مسبقاً: {existing_link}")
            return jsonify({
                'success': True,
                'is_existing': True,
                'share_link': existing_link,
                'message': f'رابط {service} موجود مسبقاً'
            })

        logger.info(f"🔧 إنشاء رابط جديد للخدمة: {service}")

        # إنشاء رابط حقيقي
        share_link = None

        if service == 'nextcloud':
            # استخدام cloud_link_manager للـ Nextcloud
            result = cloud_link_manager.create_share_link(file_path, file_name, 'nextcloud')
            if result and result.get('success'):
                share_link = result.get('share_link')
        elif service == 'onedrive':
            # إنشاء رابط تحميل مباشر للـ OneDrive
            share_link = _create_direct_download_link_for_cargo(file_name, document_id)

        if not share_link:
            return jsonify({
                'success': False,
                'message': f'فشل في إنشاء رابط {service}'
            }), 500

        # تحديث قاعدة البيانات
        try:
            if service == 'nextcloud':
                update_query = """
                    UPDATE cargo_shipment_documents
                    SET nextcloud_share_link = :share_link,
                        nextcloud_service_info = :service,
                        nextcloud_created_at = SYSDATE
                    WHERE id = :document_id
                """
            else:  # onedrive
                update_query = """
                    UPDATE cargo_shipment_documents
                    SET onedrive_share_link = :share_link,
                        onedrive_service_info = :service,
                        onedrive_created_at = SYSDATE
                    WHERE id = :document_id
                """

            oracle_manager.execute_update(update_query, {
                'share_link': share_link,
                'service': service,
                'document_id': document_id
            })
            oracle_manager.commit()

            logger.info(f"✅ تم حفظ الرابط في قاعدة البيانات")
            
        except Exception as db_error:
            logger.warning(f"⚠️ فشل في تحديث قاعدة البيانات: {db_error}")
            # نكمل حتى لو فشل الحفظ

        logger.info(f"✅ تم إنشاء رابط {service} للوثيقة {document_id}")

        return jsonify({
            'success': True,
            'is_existing': False,
            'share_link': share_link,
            'message': f'تم إنشاء رابط {service} بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء رابط الوثيقة: {e}")
        import traceback
        logger.error(f"❌ تفاصيل الخطأ: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في إنشاء الرابط'
        }), 500

def _create_direct_download_link_for_cargo(file_name: str, document_id: int) -> str:
    """إنشاء رابط تحميل مباشر لوثائق الشحنات"""
    try:
        # إنشاء معرف فريد للملف
        unique_string = f"cargo_{document_id}_{file_name}_{int(time.time())}"
        file_hash = hashlib.sha256(unique_string.encode()).hexdigest()[:16]

        # إنشاء رابط تحميل مباشر
        base_url = "https://saserp.alfogehi.net:5000"
        share_link = f"{base_url}/shared/download/{file_hash}"

        logger.info(f"✅ تم إنشاء رابط تحميل مباشر للشحنة: {share_link}")
        return share_link

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء رابط التحميل المباشر: {e}")
        return None
