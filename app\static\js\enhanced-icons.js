// ملف JavaScript لتحسين الأيقونات والتأثيرات التفاعلية
// إصدار 2.0

document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل تحسينات الأيقونات');
    
    // إضافة تأثيرات للأيقونات عند التحميل
    enhanceIcons();
    
    // إضافة مستمعات الأحداث للأزرار
    addButtonEventListeners();
    
    // إضافة تأثيرات الحركة
    addAnimationEffects();
});

function enhanceIcons() {
    // البحث عن جميع الأيقونات وتحسينها
    const icons = document.querySelectorAll('.toolbar-btn i.fas');
    
    icons.forEach(icon => {
        // إضافة فئات CSS للتحسين
        icon.classList.add('enhanced-icon');
        
        // إضافة تأثير عند التمرير
        icon.parentElement.addEventListener('mouseenter', function() {
            icon.style.transform = 'scale(1.2)';
            icon.style.textShadow = '0 3px 6px rgba(0,0,0,0.3)';
        });
        
        icon.parentElement.addEventListener('mouseleave', function() {
            icon.style.transform = 'scale(1)';
            icon.style.textShadow = '0 1px 2px rgba(0,0,0,0.1)';
        });
    });
    
    console.log(`تم تحسين ${icons.length} أيقونة`);
}

function addButtonEventListeners() {
    // إضافة تأثيرات خاصة لأزرار محددة
    const buttons = {
        'fa-sync-alt': addSpinEffect,
        'fa-save': addPulseEffect,
        'fa-trash-alt': addShakeEffect,
        'fa-print': addBounceEffect,
        'fa-search': addZoomEffect,
        'fa-filter': addWiggleEffect,
        'fa-plus': addExpandEffect,
        'fa-edit': addWriteEffect
    };
    
    Object.keys(buttons).forEach(iconClass => {
        const elements = document.querySelectorAll(`.toolbar-btn i.${iconClass}`);
        elements.forEach(element => {
            element.parentElement.addEventListener('mouseenter', buttons[iconClass]);
        });
    });
}

function addAnimationEffects() {
    // إضافة تأثيرات CSS المتحركة
    const style = document.createElement('style');
    style.textContent = `
        .enhanced-icon {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }
        
        @keyframes spin-enhanced {
            from { transform: rotate(0deg) scale(1.2); }
            to { transform: rotate(360deg) scale(1.2); }
        }
        
        @keyframes pulse-enhanced {
            0%, 100% { transform: scale(1.2); }
            50% { transform: scale(1.4); }
        }
        
        @keyframes shake-enhanced {
            0%, 100% { transform: translateX(0) scale(1.2); }
            25% { transform: translateX(-3px) scale(1.2); }
            75% { transform: translateX(3px) scale(1.2); }
        }
        
        @keyframes bounce-enhanced {
            0%, 100% { transform: translateY(0) scale(1.2); }
            50% { transform: translateY(-5px) scale(1.2); }
        }
        
        @keyframes zoom-enhanced {
            0%, 100% { transform: scale(1.2); }
            50% { transform: scale(1.5); }
        }
        
        @keyframes wiggle-enhanced {
            0%, 100% { transform: rotate(0deg) scale(1.2); }
            25% { transform: rotate(-8deg) scale(1.2); }
            75% { transform: rotate(8deg) scale(1.2); }
        }
        
        @keyframes expand-enhanced {
            0%, 100% { transform: scale(1.2); }
            50% { transform: scale(1.6); }
        }
        
        @keyframes write-enhanced {
            0%, 100% { transform: rotate(0deg) scale(1.2); }
            25% { transform: rotate(-15deg) scale(1.3); }
            75% { transform: rotate(15deg) scale(1.3); }
        }
    `;
    document.head.appendChild(style);
}

// دوال التأثيرات المختلفة
function addSpinEffect() {
    this.querySelector('i').style.animation = 'spin-enhanced 1s linear infinite';
    setTimeout(() => {
        this.querySelector('i').style.animation = '';
    }, 2000);
}

function addPulseEffect() {
    this.querySelector('i').style.animation = 'pulse-enhanced 0.6s ease-in-out';
    setTimeout(() => {
        this.querySelector('i').style.animation = '';
    }, 600);
}

function addShakeEffect() {
    this.querySelector('i').style.animation = 'shake-enhanced 0.5s ease-in-out';
    setTimeout(() => {
        this.querySelector('i').style.animation = '';
    }, 500);
}

function addBounceEffect() {
    this.querySelector('i').style.animation = 'bounce-enhanced 0.6s ease-in-out';
    setTimeout(() => {
        this.querySelector('i').style.animation = '';
    }, 600);
}

function addZoomEffect() {
    this.querySelector('i').style.animation = 'zoom-enhanced 0.4s ease-in-out';
    setTimeout(() => {
        this.querySelector('i').style.animation = '';
    }, 400);
}

function addWiggleEffect() {
    this.querySelector('i').style.animation = 'wiggle-enhanced 0.5s ease-in-out';
    setTimeout(() => {
        this.querySelector('i').style.animation = '';
    }, 500);
}

function addExpandEffect() {
    this.querySelector('i').style.animation = 'expand-enhanced 0.4s ease-in-out';
    setTimeout(() => {
        this.querySelector('i').style.animation = '';
    }, 400);
}

function addWriteEffect() {
    this.querySelector('i').style.animation = 'write-enhanced 0.6s ease-in-out';
    setTimeout(() => {
        this.querySelector('i').style.animation = '';
    }, 600);
}

// إضافة تأثير لمعان للأزرار
function addShimmerEffect() {
    const buttons = document.querySelectorAll('.toolbar-btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.background = this.style.background + ', linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)';
        });
    });
}

// تشغيل التأثيرات
setTimeout(addShimmerEffect, 1000);

console.log('تم تحميل جميع تحسينات الأيقونات بنجاح!');
