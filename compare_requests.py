#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مقارنة الطلبين لمعرفة سبب فشل أحدهما
Compare requests to find why one fails
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oracle_manager import get_oracle_manager
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def compare_requests():
    """مقارنة الطلبين لمعرفة الفرق"""
    
    oracle_manager = get_oracle_manager()
    
    if not oracle_manager.connect():
        logger.error("❌ فشل الاتصال بقاعدة البيانات")
        return False
    
    try:
        # الطلب الذي يعمل
        working_request = "TR20250825001"
        # الطلب الذي لا يعمل
        failing_request = "TR20250829050345001"
        
        logger.info("🔍 مقارنة الطلبين:")
        logger.info(f"   ✅ الطلب الذي يعمل: {working_request}")
        logger.info(f"   ❌ الطلب الذي يفشل: {failing_request}")
        logger.info("=" * 60)
        
        # استعلام مفصل للطلبين
        detailed_query = """
        SELECT 
            tr.id, tr.request_number, tr.amount, tr.total_amount, tr.currency,
            tr.purpose, tr.notes, tr.status, tr.transfer_type, 
            tr.money_changer_bank_id, tr.branch_id, tr.beneficiary_id,
            tr.created_at, tr.updated_at, tr.created_by, tr.updated_by,
            b.beneficiary_name, b.type as beneficiary_type, b.bank_account,
            b.bank_name, b.bank_branch, b.bank_country, b.iban, b.swift_code,
            mcb.name as money_changer_name, mcb.type as money_changer_type,
            mcb.commission_rate,
            br.BRN_LNAME as branch_name
        FROM TRANSFER_REQUESTS tr
        LEFT JOIN BENEFICIARIES b ON tr.beneficiary_id = b.id
        LEFT JOIN MONEY_CHANGERS_BANKS mcb ON tr.money_changer_bank_id = mcb.id
        LEFT JOIN BRANCHES br ON tr.branch_id = br.BRN_NO
        WHERE tr.request_number IN (:1, :2)
        ORDER BY tr.request_number
        """
        
        result = oracle_manager.execute_query(detailed_query, [working_request, failing_request])
        
        if not result:
            logger.error("❌ لم يتم العثور على الطلبين")
            return False
        
        if len(result) != 2:
            logger.warning(f"⚠️ تم العثور على {len(result)} طلب فقط بدلاً من 2")
        
        # تحليل كل طلب
        for i, row in enumerate(result):
            request_type = "✅ الطلب الذي يعمل" if row[1] == working_request else "❌ الطلب الذي يفشل"
            
            logger.info(f"\n{request_type}: {row[1]}")
            logger.info(f"   ID: {row[0]}")
            logger.info(f"   المبلغ: {row[2]}")
            logger.info(f"   المبلغ الإجمالي: {row[3]}")
            logger.info(f"   العملة: {row[4]}")
            logger.info(f"   الغرض: {row[5]}")
            logger.info(f"   الملاحظات: {row[6]}")
            logger.info(f"   الحالة: {row[7]}")
            logger.info(f"   نوع التحويل: {row[8]}")
            logger.info(f"   معرف الصراف/البنك: {row[9]}")
            logger.info(f"   معرف الفرع: {row[10]}")
            logger.info(f"   معرف المستفيد: {row[11]}")
            logger.info(f"   تاريخ الإنشاء: {row[12]}")
            logger.info(f"   تاريخ التحديث: {row[13]}")
            logger.info(f"   منشئ بواسطة: {row[14]}")
            logger.info(f"   محدث بواسطة: {row[15]}")
            logger.info(f"   اسم المستفيد: {row[16]}")
            logger.info(f"   نوع المستفيد: {row[17]}")
            logger.info(f"   حساب البنك: {row[18]}")
            logger.info(f"   اسم البنك: {row[19]}")
            logger.info(f"   فرع البنك: {row[20]}")
            logger.info(f"   بلد البنك: {row[21]}")
            logger.info(f"   IBAN: {row[22]}")
            logger.info(f"   SWIFT: {row[23]}")
            logger.info(f"   اسم الصراف/البنك: {row[24]}")
            logger.info(f"   نوع الصراف/البنك: {row[25]}")
            logger.info(f"   معدل العمولة: {row[26]}")
            logger.info(f"   اسم الفرع: {row[27]}")
            logger.info("   " + "-" * 50)
        
        # فحص الفروق المحتملة
        logger.info("\n🔍 تحليل الفروق المحتملة:")
        
        if len(result) == 2:
            working_row = result[0] if result[0][1] == working_request else result[1]
            failing_row = result[1] if result[1][1] == failing_request else result[0]
            
            # مقارنة الحقول المهمة
            fields_to_compare = [
                (10, "معرف الفرع"),
                (9, "معرف الصراف/البنك"), 
                (11, "معرف المستفيد"),
                (6, "الملاحظات"),
                (5, "الغرض"),
                (4, "العملة")
            ]
            
            differences_found = False
            
            for field_index, field_name in fields_to_compare:
                working_value = working_row[field_index]
                failing_value = failing_row[field_index]
                
                if working_value != failing_value:
                    differences_found = True
                    logger.info(f"   🔍 {field_name}:")
                    logger.info(f"     ✅ الطلب الذي يعمل: {working_value}")
                    logger.info(f"     ❌ الطلب الذي يفشل: {failing_value}")
            
            if not differences_found:
                logger.info("   ✅ لا توجد فروق واضحة في الحقول الأساسية")
            
            # فحص خاص للملاحظات (LOB field)
            logger.info(f"\n🔍 فحص خاص لحقل الملاحظات:")
            working_notes = working_row[6]
            failing_notes = failing_row[6]
            
            logger.info(f"   ✅ ملاحظات الطلب الذي يعمل:")
            logger.info(f"     النوع: {type(working_notes)}")
            logger.info(f"     الطول: {len(str(working_notes)) if working_notes else 0}")
            logger.info(f"     القيمة: {repr(working_notes)}")
            
            logger.info(f"   ❌ ملاحظات الطلب الذي يفشل:")
            logger.info(f"     النوع: {type(failing_notes)}")
            logger.info(f"     الطول: {len(str(failing_notes)) if failing_notes else 0}")
            logger.info(f"     القيمة: {repr(failing_notes)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في المقارنة: {e}")
        return False
    
    finally:
        oracle_manager.disconnect()

if __name__ == "__main__":
    logger.info("🚀 بدء مقارنة الطلبين...")
    
    if compare_requests():
        logger.info("✅ انتهت المقارنة بنجاح")
    else:
        logger.error("❌ فشلت المقارنة")
