#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Oracle Application Starter - بدء التطبيق مع Oracle
"""

import os
import sys
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_oracle_status():
    """التحقق من حالة Oracle"""
    logger.info("🔍 التحقق من حالة Oracle...")

    try:
        # استخدام database_manager بدلاً من إنشاء oracle_manager جديد
        from database_manager import DatabaseManager
        db_manager = DatabaseManager()

        # اختبار استعلام
        result = db_manager.execute_query("SELECT 1 FROM DUAL")
        if result:
            logger.info("✅ Oracle Database متصل ويعمل بشكل صحيح")

            # التحقق من الجداول
            tables = ['users', 'branches']
            for table in tables:
                try:
                    count_result = db_manager.execute_query(f"SELECT COUNT(*) FROM {table}")
                    count = count_result[0][0] if count_result else 0
                    logger.info(f"✅ جدول {table}: {count} سجل")
                except Exception as e:
                    logger.warning(f"⚠️ جدول {table}: {e}")

            return True
        else:
            logger.error("❌ فشل في تنفيذ الاستعلام")
            
    except Exception as e:
        logger.error(f"❌ خطأ في التحقق من Oracle: {e}")
    
    return False

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🏢 نظام المحاسبة المتقدم - Oracle Edition")
    print("Advanced Accounting System - Oracle Edition")
    print("=" * 70)
    
    # قراءة التكوين من ملف .env
    config = None
    try:
        with open('.env', 'r', encoding='utf-8') as f:
            for line in f:
                if line.startswith('FLASK_CONFIG='):
                    config = line.strip().split('=')[1]
                    break
    except:
        config = os.environ.get('FLASK_CONFIG', 'development')

    if not config or 'oracle' not in config.lower():
        print("⚠️ تحذير: التكوين الحالي ليس Oracle")
        print(f"   التكوين الحالي: {config}")
        print("   يرجى تشغيل: python switch_database.py --oracle")
        return
    
    print(f"✅ التكوين: {config}")
    
    # التحقق من Oracle (اختياري)
    try:
        check_oracle_status()
    except Exception as e:
        logger.warning(f"⚠️ تحذير في التحقق من Oracle: {e}")
        logger.info("🔄 سيتم المتابعة مع تشغيل الخادم...")
    
    print("\n🎉 تم التحقق من Oracle بنجاح!")
    print("🚀 بدء تشغيل التطبيق...")
    print("\n🌐 التطبيق متاح على:")
    print("   - http://127.0.0.1:5000")
    print("   - http://localhost:5000")
    print("\n👤 بيانات تسجيل الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin")
    print("\n" + "=" * 70)
    
    # تشغيل التطبيق
    try:
        from app import create_app
        app = create_app()
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            use_reloader=False  # تعطيل إعادة التحميل لتجنب مشاكل JVM
        )
    except KeyboardInterrupt:
        print("\n🛑 إيقاف التطبيق...")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل التطبيق: {e}")

if __name__ == '__main__':
    main()
