# -*- coding: utf-8 -*-
"""
نماذج البيانات لنظام عمولات مندوبي المشتريات
Data Models for Purchase Representatives Commission System
"""

from dataclasses import dataclass
from typing import Optional, List, Dict, Any
from datetime import datetime
import json


@dataclass
class PurchaseRepresentative:
    """نموذج بيانات المندوب"""
    id: Optional[int] = None
    rep_code: str = ""
    rep_name: str = ""
    rep_name_en: str = ""
    employee_id: Optional[int] = None
    department: str = ""
    branch_id: Optional[int] = None
    phone: str = ""
    mobile: str = ""
    email: str = ""
    hire_date: Optional[datetime] = None
    
    # التخصص والأهداف
    specialization: str = ""
    target_monthly_orders: int = 0
    target_monthly_quantity: int = 0
    target_monthly_value: float = 0.0
    
    # إعدادات العمولة
    is_active: bool = True
    commission_eligible: bool = True
    commission_start_date: Optional[datetime] = None
    commission_end_date: Optional[datetime] = None
    
    # معلومات إضافية
    notes: str = ""
    created_at: Optional[datetime] = None
    created_by: Optional[int] = None
    updated_at: Optional[datetime] = None
    updated_by: Optional[int] = None


@dataclass
class CommissionType:
    """نموذج بيانات نوع العمولة"""
    id: Optional[int] = None
    type_code: str = ""
    type_name: str = ""
    type_name_en: str = ""
    calculation_method: str = ""  # FIXED, PERCENTAGE, TIERED, etc.
    description: str = ""
    supports_combination: bool = True
    is_active: bool = True
    display_order: int = 1
    created_at: Optional[datetime] = None
    created_by: Optional[int] = None
    updated_at: Optional[datetime] = None
    updated_by: Optional[int] = None


@dataclass
class CommissionRule:
    """نموذج بيانات قاعدة العمولة"""
    id: Optional[int] = None
    rule_name: str = ""
    rule_description: str = ""
    commission_type_id: int = 0
    rep_id: Optional[int] = None
    supplier_id: Optional[int] = None
    item_category: str = ""
    
    # قواعد الحساب التقليدية
    fixed_amount: Optional[float] = None
    percentage_rate: Optional[float] = None
    min_order_value: Optional[float] = None
    max_commission: Optional[float] = None
    
    # قواعد الحساب حسب الكمية
    quantity_unit: str = ""
    quantity_rate: Optional[float] = None
    min_quantity: Optional[float] = None
    max_quantity: Optional[float] = None
    quantity_tiers: str = ""  # JSON string
    weight_based: bool = False
    volume_based: bool = False
    
    # فترات الصلاحية
    effective_from: Optional[datetime] = None
    effective_to: Optional[datetime] = None
    
    # شروط إضافية
    conditions: str = ""  # JSON string
    combination_rules: str = ""  # JSON string
    is_active: bool = True
    priority_order: int = 1
    
    created_at: Optional[datetime] = None
    created_by: Optional[int] = None
    updated_at: Optional[datetime] = None
    updated_by: Optional[int] = None
    
    def get_quantity_tiers(self) -> List[Dict]:
        """استخراج شرائح الكمية من JSON"""
        if self.quantity_tiers:
            try:
                return json.loads(self.quantity_tiers)
            except:
                return []
        return []
    
    def set_quantity_tiers(self, tiers: List[Dict]):
        """تحديد شرائح الكمية كـ JSON"""
        self.quantity_tiers = json.dumps(tiers, ensure_ascii=False)
    
    def get_conditions(self) -> Dict:
        """استخراج الشروط من JSON"""
        if self.conditions:
            try:
                return json.loads(self.conditions)
            except:
                return {}
        return {}
    
    def set_conditions(self, conditions: Dict):
        """تحديد الشروط كـ JSON"""
        self.conditions = json.dumps(conditions, ensure_ascii=False)


@dataclass
class CommissionCalculation:
    """نموذج بيانات حساب العمولة"""
    id: Optional[int] = None
    calculation_date: Optional[datetime] = None
    rep_id: int = 0
    purchase_order_id: int = 0
    rule_id: int = 0
    
    # تفاصيل الحساب التقليدية
    order_value: Optional[float] = None
    commission_rate: Optional[float] = None
    commission_amount: Optional[float] = None
    calculation_method: str = ""
    
    # تفاصيل الحساب حسب الكمية
    total_quantity: Optional[float] = None
    quantity_unit: str = ""
    quantity_rate: Optional[float] = None
    quantity_commission: Optional[float] = None
    weight_total: Optional[float] = None
    volume_total: Optional[float] = None
    
    # تفاصيل إضافية
    breakdown_details: str = ""  # JSON string
    combined_commission: Optional[float] = None
    
    # حالة العمولة
    status: str = "CALCULATED"  # CALCULATED, APPROVED, PAID, CANCELLED
    approved_by: Optional[int] = None
    approved_at: Optional[datetime] = None
    paid_at: Optional[datetime] = None
    payment_reference: str = ""
    
    notes: str = ""
    created_at: Optional[datetime] = None
    created_by: Optional[int] = None
    updated_at: Optional[datetime] = None
    updated_by: Optional[int] = None
    
    def get_breakdown_details(self) -> Dict:
        """استخراج تفاصيل الحساب من JSON"""
        if self.breakdown_details:
            try:
                return json.loads(self.breakdown_details)
            except:
                return {}
        return {}
    
    def set_breakdown_details(self, details: Dict):
        """تحديد تفاصيل الحساب كـ JSON"""
        self.breakdown_details = json.dumps(details, ensure_ascii=False)


@dataclass
class CommissionPayment:
    """نموذج بيانات دفع العمولة"""
    id: Optional[int] = None
    payment_date: Optional[datetime] = None
    rep_id: int = 0
    calculation_ids: str = ""  # JSON array
    total_amount: float = 0.0
    currency: str = "SAR"
    
    # ربط مع نظام الحوالات
    transfer_request_id: Optional[int] = None
    transfer_id: Optional[int] = None
    
    # تفاصيل الدفع
    payment_method: str = "TRANSFER"  # TRANSFER, CASH, BANK, CHECK
    payment_reference: str = ""
    bank_details: str = ""  # JSON string
    
    # حالة الدفع
    status: str = "PENDING"  # PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED
    processed_by: Optional[int] = None
    processed_at: Optional[datetime] = None
    
    notes: str = ""
    created_at: Optional[datetime] = None
    created_by: Optional[int] = None
    updated_at: Optional[datetime] = None
    updated_by: Optional[int] = None
    
    def get_calculation_ids(self) -> List[int]:
        """استخراج معرفات العمولات من JSON"""
        if self.calculation_ids:
            try:
                return json.loads(self.calculation_ids)
            except:
                return []
        return []
    
    def set_calculation_ids(self, ids: List[int]):
        """تحديد معرفات العمولات كـ JSON"""
        self.calculation_ids = json.dumps(ids)
    
    def get_bank_details(self) -> Dict:
        """استخراج تفاصيل البنك من JSON"""
        if self.bank_details:
            try:
                return json.loads(self.bank_details)
            except:
                return {}
        return {}
    
    def set_bank_details(self, details: Dict):
        """تحديد تفاصيل البنك كـ JSON"""
        self.bank_details = json.dumps(details, ensure_ascii=False)


# قوائم الثوابت
COMMISSION_CALCULATION_METHODS = [
    'FIXED',
    'PERCENTAGE', 
    'TIERED',
    'QUANTITY_FIXED',
    'QUANTITY_TIERED',
    'ITEM_BASED',
    'SUPPLIER_BASED',
    'SEASONAL'
]

COMMISSION_STATUSES = [
    'CALCULATED',
    'APPROVED', 
    'PAID',
    'CANCELLED'
]

PAYMENT_METHODS = [
    'TRANSFER',
    'CASH',
    'BANK',
    'CHECK'
]

PAYMENT_STATUSES = [
    'PENDING',
    'PROCESSING',
    'COMPLETED',
    'FAILED',
    'CANCELLED'
]

QUANTITY_UNITS = [
    'قطعة',
    'كيلو',
    'متر',
    'لتر',
    'صندوق',
    'طن',
    'متر مكعب',
    'متر مربع'
]
