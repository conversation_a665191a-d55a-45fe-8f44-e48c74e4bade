#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لإضافة عمود IS_USED إلى جدول العقود
"""

import sys
import logging
from oracle_manager import get_oracle_manager

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def add_is_used_column():
    """إضافة عمود IS_USED إلى جدول العقود"""
    try:
        logger.info("🔄 بدء إضافة عمود IS_USED...")
        
        oracle = get_oracle_manager()
        oracle.connect()
        
        # التحقق من وجود العمود أولاً
        check_column_query = """
        SELECT COUNT(*) 
        FROM USER_TAB_COLUMNS 
        WHERE TABLE_NAME = 'CONTRACTS' AND COLUMN_NAME = 'IS_USED'
        """
        
        result = oracle.execute_query(check_column_query)
        column_exists = result[0][0] > 0 if result else False
        
        if column_exists:
            logger.info("✅ عمود IS_USED موجود بالفعل")
        else:
            logger.info("➕ إضافة عمود IS_USED...")
            
            # إضافة العمود
            add_column_sql = """
            ALTER TABLE contracts ADD (
                IS_USED NUMBER(1) DEFAULT 0 NOT NULL
            )
            """
            oracle.execute_update(add_column_sql)
            logger.info("✅ تم إضافة العمود")
            
            # إضافة قيد التحقق
            try:
                constraint_sql = """
                ALTER TABLE contracts ADD CONSTRAINT CHK_CONTRACTS_IS_USED 
                    CHECK (IS_USED IN (0, 1))
                """
                oracle.execute_update(constraint_sql)
                logger.info("✅ تم إضافة قيد التحقق")
            except Exception as e:
                logger.warning(f"⚠️ قيد التحقق موجود بالفعل: {e}")
            
            # إضافة فهرس
            try:
                index_sql = "CREATE INDEX IDX_CONTRACTS_IS_USED ON contracts(IS_USED)"
                oracle.execute_update(index_sql)
                logger.info("✅ تم إضافة الفهرس")
            except Exception as e:
                logger.warning(f"⚠️ الفهرس موجود بالفعل: {e}")
        
        # تحديث العقود الموجودة
        logger.info("🔄 تحديث العقود الموجودة...")
        
        update_sql = """
        UPDATE contracts 
        SET IS_USED = 1 
        WHERE contract_id IN (
            SELECT DISTINCT CONTRACT_ID 
            FROM PURCHASE_ORDERS 
            WHERE CONTRACT_ID IS NOT NULL
        )
        """
        
        rows_updated = oracle.execute_update(update_sql)
        logger.info(f"✅ تم تحديث {rows_updated} عقد")
        
        # عرض النتيجة
        logger.info("📊 عرض حالة العقود:")
        result_query = """
        SELECT contract_id, contract_number, supplier_name, IS_USED 
        FROM contracts 
        ORDER BY contract_id
        """
        
        contracts = oracle.execute_query(result_query)
        if contracts:
            logger.info("العقود الموجودة:")
            for contract in contracts:
                status = "مُستخدم" if contract[3] == 1 else "غير مُستخدم"
                logger.info(f"  - {contract[1]} | {contract[2]} | {status}")
        else:
            logger.info("لا توجد عقود في النظام")
        
        oracle.disconnect()
        logger.info("🎉 تم إكمال العملية بنجاح!")
        
    except Exception as e:
        logger.error(f"❌ خطأ في إضافة العمود: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = add_is_used_column()
    sys.exit(0 if success else 1)
