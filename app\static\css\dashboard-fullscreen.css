/* تصميم لوحة الشحنات بوضع ملء الشاشة */

/* إعادة تعيين الهوامش والحشو */
.dashboard-fullscreen {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* إخفاء الشريط الجانبي في وضع ملء الشاشة */
.dashboard-fullscreen .sidebar {
    display: none !important;
}

.dashboard-fullscreen .main-content {
    margin-left: 0 !important;
    width: 100% !important;
    padding: 0 !important;
}

/* شريط التنقل العلوي */
.shipments-navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

/* العلامة التجارية */
.nav-brand {
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    font-weight: 600;
    gap: 10px;
}

.nav-brand i {
    font-size: 1.5rem;
    color: #ffd700;
}

/* رابط العلامة التجارية */
.brand-link {
    color: inherit;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    padding: 5px 10px;
    border-radius: 8px;
}

.brand-link:hover {
    color: #fff;
    text-decoration: none;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.brand-link:hover i {
    color: #ffd700;
    transform: scale(1.1);
}

/* أزرار التنقل */
.nav-actions {
    display: flex;
    gap: 5px;
}

.btn-nav {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    padding: 8px 16px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-nav:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-nav.active {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.4);
    color: #fff;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    transform: translateY(-2px);
}

/* زر الصفحة الرئيسية */
.btn-home {
    background: rgba(255, 215, 0, 0.2) !important;
    border-color: rgba(255, 215, 0, 0.3) !important;
    color: #ffd700 !important;
    text-decoration: none;
}

.btn-home:hover {
    background: rgba(255, 215, 0, 0.3) !important;
    border-color: rgba(255, 215, 0, 0.5) !important;
    color: #fff !important;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
}

.btn-home i {
    color: #ffd700;
}

.btn-home:hover i {
    color: #fff;
    transform: scale(1.1);
}

/* أدوات التحكم */
.nav-tools {
    display: flex;
    gap: 10px;
}

.btn-tool {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-tool:hover {
    background: rgba(255,255,255,0.2);
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.btn-tool:active {
    transform: scale(0.95);
    background: rgba(255,255,255,0.3);
}

/* زر العودة */
.btn-back {
    background: rgba(255, 99, 71, 0.2) !important;
    border: 1px solid rgba(255, 99, 71, 0.3) !important;
    color: #ff6347 !important;
    text-decoration: none;
}

.btn-back:hover {
    background: rgba(255, 99, 71, 0.3) !important;
    border-color: rgba(255, 99, 71, 0.5) !important;
    color: #fff !important;
    text-decoration: none;
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(255, 99, 71, 0.4);
}

.btn-back i {
    color: #ff6347;
}

.btn-back:hover i {
    color: #fff;
}

/* المحتوى الرئيسي */
.dashboard-main {
    min-height: calc(100vh - 60px);
    background: #f8f9fa;
    position: relative;
    /* استغلال كامل المساحة - لا توجد مساحة محجوزة */
}

/* شريط الأدوات العائم - مخفي */
.floating-toolbar {
    display: none !important;
}

.floating-toolbar.hidden {
    transform: translateY(-50%) translateX(60px);
    opacity: 0;
}

.tool-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    position: relative;
}

.tool-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* تلميحات الأزرار */
.tool-btn::after {
    content: attr(data-tooltip);
    position: absolute;
    right: 45px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1002;
}

.tool-btn:hover::after {
    opacity: 1;
}

/* زر التبديل - مخفي */
.toolbar-toggle {
    display: none !important;
}

/* زر إظهار/إخفاء الشريط الجانبي */
.sidebar-toggle {
    position: fixed;
    top: 70px;
    left: 10px;
    width: 40px;
    height: 40px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 998;
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: #f8f9fa;
    transform: scale(1.1);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .nav-actions {
        display: none;
    }
    
    .nav-brand {
        font-size: 1rem;
    }
    
    .floating-toolbar {
        right: 10px;
        padding: 8px;
    }
    
    .tool-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .shipments-navbar {
        padding: 0 15px;
        height: 50px;
    }

    .nav-brand {
        font-size: 0.9rem;
    }

    .btn-tool {
        width: 35px;
        height: 35px;
    }

    .dashboard-main {
        min-height: calc(100vh - 50px);
        /* استغلال كامل المساحة في الموبايل */
    }
}

/* تأثيرات الانتقال */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تحسين عرض المحتوى */
.dashboard-fullscreen .container-fluid {
    padding: 20px;
    max-width: none;
}

/* إخفاء عناصر التنقل الأصلية */
.dashboard-fullscreen .breadcrumb,
.dashboard-fullscreen .page-header {
    display: none;
}

/* تحسين الجداول في الوضع الجديد - استغلال كامل المساحة */
.dashboard-fullscreen .table-responsive {
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    width: 100%;
    margin: 0;
}

/* تحسين عرض الجدول */
.dashboard-fullscreen .table {
    width: 100%;
    margin-bottom: 0;
}

/* تحسين المحتوى ليستغل كامل المساحة */
.dashboard-fullscreen .container-fluid {
    padding: 20px;
    max-width: none;
    width: 100%;
}

.dashboard-fullscreen .card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.08);
}

/* تحسين الأزرار */
.dashboard-fullscreen .btn {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.dashboard-fullscreen .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}
