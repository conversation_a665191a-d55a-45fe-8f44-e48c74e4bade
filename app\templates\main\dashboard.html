{% extends "base.html" %}

{% block title %}لوحة المعلومات - NetSuite Oracle{% endblock %}

{% block content %}

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.ns-card-real .ns-card-body .border {
    transition: all 0.3s ease;
}

.ns-card-real .ns-card-body .border:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.badge {
    font-size: 0.75rem;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}
</style>
<!-- NetSuite REAL Page Header -->
<div class="ns-page-header-real">
    <h1>
        <i class="fas fa-tachometer-alt"></i>
        لوحة المعلومات
    </h1>
</div>

<!-- NetSuite REAL Stats Cards -->
<div class="ns-stats-real">
    <div class="ns-stat-card-real" style="background: linear-gradient(135deg, #007bff, #0056b3); color: white; border: none;">
        <div class="ns-stat-icon" style="background: rgba(255,255,255,0.2);">
            <i class="fas fa-ship" style="color: white;"></i>
        </div>
        <div class="ns-stat-number" style="color: white; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">{{ stats.shipments.total }}</div>
        <div class="ns-stat-label" style="color: rgba(255,255,255,0.9); font-weight: 500;">إجمالي الشحنات</div>
        <div class="ns-stat-change">
            <small style="color: rgba(255,255,255,0.8);">{{ stats.shipments.delivered }} مسلم</small>
        </div>
    </div>

    <div class="ns-stat-card-real" style="background: linear-gradient(135deg, #28a745, #1e7e34); color: white; border: none;">
        <div class="ns-stat-icon" style="background: rgba(255,255,255,0.2);">
            <i class="fas fa-truck" style="color: white;"></i>
        </div>
        <div class="ns-stat-number" style="color: white; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">{{ stats.shipments.in_transit }}</div>
        <div class="ns-stat-label" style="color: rgba(255,255,255,0.9); font-weight: 500;">في الطريق</div>
        <div class="ns-stat-change">
            <small style="color: rgba(255,255,255,0.8);">{{ stats.shipments.pending }} معلق</small>
        </div>
    </div>

    <div class="ns-stat-card-real" style="background: linear-gradient(135deg, #6f42c1, #5a32a3); color: white; border: none;">
        <div class="ns-stat-icon" style="background: rgba(255,255,255,0.2);">
            <i class="fas fa-cube" style="color: white;"></i>
        </div>
        <div class="ns-stat-number" style="color: white; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">{{ stats.containers.total }}</div>
        <div class="ns-stat-label" style="color: rgba(255,255,255,0.9); font-weight: 500;">الحاويات</div>
        <div class="ns-stat-change">
            <small style="color: rgba(255,255,255,0.8);">{{ stats.containers.active }} نشط</small>
        </div>
    </div>

    <div class="ns-stat-card-real" style="background: linear-gradient(135deg, #fd7e14, #e55a00); color: white; border: none;">
        <div class="ns-stat-icon" style="background: rgba(255,255,255,0.2);">
            <i class="fas fa-user-tie" style="color: white;"></i>
        </div>
        <div class="ns-stat-number" style="color: white; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">{{ stats.agents.total }}</div>
        <div class="ns-stat-label" style="color: rgba(255,255,255,0.9); font-weight: 500;">المخلصين</div>
        <div class="ns-stat-change">
            <small style="color: rgba(255,255,255,0.8);">{{ stats.shipments.overdue }} متأخر</small>
        </div>
    </div>
</div>



<!-- إضافة رسومات بيانية من نظام الحوالات -->
<div class="row g-3 mb-3">
    <!-- رسم بياني لحالات الحوالات -->
    <div class="col-md-6">
        <div class="ns-card-real">
            <div class="ns-card-header">
                <i class="fas fa-chart-pie me-2"></i>
                توزيع حالات الحوالات
            </div>
            <div class="ns-card-body">
                <canvas id="transfersStatusChart" width="400" height="220"></canvas>
            </div>
        </div>
    </div>

    <!-- رسم بياني للحوالات الشهرية -->
    <div class="col-md-6">
        <div class="ns-card-real">
            <div class="ns-card-header">
                <i class="fas fa-chart-line me-2"></i>
                الحوالات الشهرية
            </div>
            <div class="ns-card-body">
                <canvas id="monthlyTransfersChart" width="400" height="220"></canvas>
            </div>
        </div>
    </div>
</div>



<!-- NetSuite REAL Charts Section -->
<div class="row g-3 mt-2">
    <!-- Shipments Status Chart -->
    <div class="col-md-6">
        <div class="ns-card-real">
            <div class="ns-card-header">
                <i class="fas fa-chart-pie me-2"></i>
                توزيع حالات الشحنات
            </div>
            <div class="ns-card-body">
                <canvas id="shipmentsStatusChart" width="400" height="220"></canvas>
            </div>
        </div>
    </div>

    <!-- Delivery Performance Chart -->
    <div class="col-md-6">
        <div class="ns-card-real">
            <div class="ns-card-header">
                <i class="fas fa-chart-bar me-2"></i>
                أداء التسليم الشهري
            </div>
            <div class="ns-card-body">
                <canvas id="deliveryPerformanceChart" width="400" height="220"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- تم إخفاء الرسوم البيانية الإضافية -->
<!-- تم إخفاء رسم الحاويات والمخلصين -->
<!-- تم إخفاء رسم اتجاه الشحنات الأسبوعي -->

{% endblock %}

{% block scripts %}
<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Shipments Status Pie Chart
    const shipmentsCtx = document.getElementById('shipmentsStatusChart').getContext('2d');
    new Chart(shipmentsCtx, {
        type: 'doughnut',
        data: {
            labels: ['مسلم', 'في الطريق', 'معلق', 'متأخر'],
            datasets: [{
                data: [
                    {{ stats.shipments.delivered }},
                    {{ stats.shipments.in_transit }},
                    {{ stats.shipments.pending }},
                    {{ stats.shipments.overdue }}
                ],
                backgroundColor: [
                    '#28a745',  // Green for delivered
                    '#17a2b8',  // Blue for in transit
                    '#ffc107',  // Yellow for pending
                    '#dc3545'   // Red for overdue
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 10,
                        usePointStyle: true,
                        boxWidth: 12,
                        font: {
                            family: 'Cairo, sans-serif',
                            size: 10
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed * 100) / total).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });

    // Delivery Performance Bar Chart
    const performanceCtx = document.getElementById('deliveryPerformanceChart').getContext('2d');
    new Chart(performanceCtx, {
        type: 'bar',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'معدل التسليم %',
                data: [85, 88, 92, 89, 94, {{ stats.operations.delivery_rate }}],
                backgroundColor: [
                    'rgba(0, 123, 255, 0.8)',
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(255, 193, 7, 0.8)',
                    'rgba(220, 53, 69, 0.8)',
                    'rgba(111, 66, 193, 0.8)',
                    'rgba(23, 162, 184, 0.8)'
                ],
                borderColor: [
                    '#007bff',
                    '#28a745',
                    '#ffc107',
                    '#dc3545',
                    '#6f42c1',
                    '#17a2b8'
                ],
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        padding: 8,
                        boxWidth: 12,
                        font: {
                            family: 'Cairo, sans-serif',
                            size: 10
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });

    // تم إخفاء رسم الحاويات والمخلصين
    // تم إخفاء رسم اتجاه الشحنات الأسبوعي

    // Update stats with animation
    const statNumbers = document.querySelectorAll('.ns-stat-number');
    statNumbers.forEach(stat => {
        const finalValue = parseInt(stat.textContent);
        let currentValue = 0;
        const increment = finalValue / 20;

        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
                stat.textContent = finalValue;
                clearInterval(timer);
            } else {
                stat.textContent = Math.floor(currentValue);
            }
        }, 50);
    });

    // تحميل بيانات الحوالات
    loadTransfersData();

    // تحميل بيانات الحوالات
    loadTransfersData();

    console.log('NetSuite REAL Dashboard with Charts loaded');
});

// دوال الحوالات
function loadTransfersData() {
    // تحميل بيانات الحوالات من API
    fetch('/transfers/api/stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateTransfersCharts(data.data);
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل بيانات الحوالات:', error);
            // عرض بيانات افتراضية في حالة الخطأ
            updateTransfersChartsDefault();
        });
}

function updateTransfersCharts(data) {
    // رسم بياني لحالات الحوالات
    const transfersStatusCtx = document.getElementById('transfersStatusChart').getContext('2d');

    const statusData = data.requests_by_status || {};
    const statusLabels = [];
    const statusCounts = [];
    const statusColors = [];

    const statusConfig = {
        'pending': { label: 'معلقة', color: '#ffc107' },
        'approved': { label: 'موافق عليها', color: '#17a2b8' },
        'executed': { label: 'منفذة', color: '#6f42c1' },
        'completed': { label: 'مكتملة', color: '#28a745' },
        'rejected': { label: 'مرفوضة', color: '#dc3545' }
    };

    Object.keys(statusData).forEach(status => {
        if (statusConfig[status]) {
            statusLabels.push(statusConfig[status].label);
            statusCounts.push(statusData[status].count);
            statusColors.push(statusConfig[status].color);
        }
    });

    new Chart(transfersStatusCtx, {
        type: 'doughnut',
        data: {
            labels: statusLabels,
            datasets: [{
                data: statusCounts,
                backgroundColor: statusColors,
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true,
                        font: {
                            family: 'Cairo, sans-serif',
                            size: 12
                        }
                    }
                }
            }
        }
    });

    // رسم بياني للحوالات الشهرية
    const monthlyTransfersCtx = document.getElementById('monthlyTransfersChart').getContext('2d');

    const monthlyData = data.monthly_transfers || [];
    const months = monthlyData.map(item => item.month);
    const counts = monthlyData.map(item => item.count);

    new Chart(monthlyTransfersCtx, {
        type: 'line',
        data: {
            labels: months,
            datasets: [{
                label: 'عدد الحوالات',
                data: counts,
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'الشهر',
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    },
                    ticks: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'عدد الحوالات',
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    },
                    ticks: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    labels: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                }
            }
        }
    });
}

function updateTransfersChartsDefault() {
    // بيانات افتراضية في حالة عدم توفر البيانات
    const defaultData = {
        requests_by_status: {
            pending: { count: 15, total_amount: 50000 },
            approved: { count: 8, total_amount: 30000 },
            completed: { count: 25, total_amount: 120000 },
            rejected: { count: 3, total_amount: 8000 }
        },
        monthly_transfers: [
            { month: '2024-09', count: 20, total_amount: 80000 },
            { month: '2024-10', count: 35, total_amount: 150000 },
            { month: '2024-11', count: 28, total_amount: 120000 },
            { month: '2024-12', count: 42, total_amount: 200000 }
        ]
    };

    updateTransfersCharts(defaultData);
}

// دوال الحوالات
function loadTransfersData() {
    // تحميل بيانات الحوالات من API
    fetch('/transfers/api/stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateTransfersCharts(data.data);
                updateTransfersStats(data.data);
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل بيانات الحوالات:', error);
            // عرض بيانات افتراضية في حالة الخطأ
            updateTransfersChartsDefault();
        });
}

function updateTransfersCharts(data) {
    // رسم بياني لحالات الحوالات
    const transfersStatusCtx = document.getElementById('transfersStatusChart').getContext('2d');

    const statusData = data.requests_by_status || {};
    const statusLabels = [];
    const statusCounts = [];
    const statusColors = [];

    const statusConfig = {
        'pending': { label: 'معلقة', color: '#ffc107' },
        'approved': { label: 'موافق عليها', color: '#17a2b8' },
        'executed': { label: 'منفذة', color: '#6f42c1' },
        'completed': { label: 'مكتملة', color: '#28a745' },
        'rejected': { label: 'مرفوضة', color: '#dc3545' }
    };

    Object.keys(statusData).forEach(status => {
        if (statusConfig[status]) {
            statusLabels.push(statusConfig[status].label);
            statusCounts.push(statusData[status].count);
            statusColors.push(statusConfig[status].color);
        }
    });

    new Chart(transfersStatusCtx, {
        type: 'doughnut',
        data: {
            labels: statusLabels,
            datasets: [{
                data: statusCounts,
                backgroundColor: statusColors,
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true,
                        font: {
                            family: 'Cairo, sans-serif',
                            size: 12
                        }
                    }
                }
            }
        }
    });

    // رسم بياني للحوالات الشهرية
    const monthlyTransfersCtx = document.getElementById('monthlyTransfersChart').getContext('2d');

    const monthlyData = data.monthly_transfers || [];
    const months = monthlyData.map(item => item.month);
    const counts = monthlyData.map(item => item.count);
    const amounts = monthlyData.map(item => item.total_amount);

    new Chart(monthlyTransfersCtx, {
        type: 'line',
        data: {
            labels: months,
            datasets: [{
                label: 'عدد الحوالات',
                data: counts,
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                yAxisID: 'y'
            }, {
                label: 'إجمالي المبلغ (بالآلاف)',
                data: amounts.map(amount => amount / 1000),
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'الشهر',
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    },
                    ticks: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'عدد الحوالات',
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    },
                    ticks: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'المبلغ (بالآلاف)',
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    labels: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                }
            }
        }
    });
}

function updateTransfersStats(data) {
    // تحديث الإحصائيات السريعة
    const statusData = data.requests_by_status || {};

    let totalTransfers = 0;
    let completedTransfers = statusData.completed ? statusData.completed.count : 0;
    let pendingTransfers = statusData.pending ? statusData.pending.count : 0;
    let totalAmount = 0;

    Object.values(statusData).forEach(status => {
        totalTransfers += status.count;
        totalAmount += status.total_amount;
    });

    document.getElementById('totalTransfers').textContent = totalTransfers.toLocaleString();
    document.getElementById('completedTransfers').textContent = completedTransfers.toLocaleString();
    document.getElementById('pendingTransfers').textContent = pendingTransfers.toLocaleString();
    document.getElementById('totalAmount').textContent = '$' + (totalAmount / 1000).toFixed(1) + 'K';
}

function updateTransfersChartsDefault() {
    // بيانات افتراضية في حالة عدم توفر البيانات
    const defaultData = {
        requests_by_status: {
            pending: { count: 15, total_amount: 50000 },
            approved: { count: 8, total_amount: 30000 },
            completed: { count: 25, total_amount: 120000 },
            rejected: { count: 3, total_amount: 8000 }
        },
        monthly_transfers: [
            { month: '2024-09', count: 20, total_amount: 80000 },
            { month: '2024-10', count: 35, total_amount: 150000 },
            { month: '2024-11', count: 28, total_amount: 120000 },
            { month: '2024-12', count: 42, total_amount: 200000 }
        ]
    };

    updateTransfersCharts(defaultData);
    updateTransfersStats(defaultData);
}
</script>
{% endblock %}

{% block styles %}
<style>
/* تحسينات للإحصائيات */
.ns-stat-change {
    margin-top: 0.5rem;
}

.ns-stat-change small {
    font-size: 0.75rem;
    font-weight: 500;
}

/* تصميم الأنشطة الأخيرة */
.recent-activities {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s ease;
}

.activity-item:hover {
    background-color: #f8f9fa;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    flex-shrink: 0;
}

.activity-icon i {
    color: white;
    font-size: 1rem;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.activity-desc {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.activity-meta {
    display: flex;
    align-items: center;
}

.activity-amount {
    text-align: left;
    color: #28a745;
    font-weight: 600;
}

/* تحسين الشارات */
.badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.375rem;
}

.badge-success {
    background-color: #28a745;
    color: white;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-secondary {
    background-color: #6c757d;
    color: white;
}

/* تحسين الجداول */
.table-responsive {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.table td {
    vertical-align: middle;
}

/* تحسين الأزرار */
.ns-btn-real {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.ns-btn-real:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* تحسين الكروت */
.ns-card-real {
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: box-shadow 0.2s ease;
}

.ns-card-real:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.ns-card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

/* تحسين كروت الإحصائيات - مضغوطة */
.ns-stat-card-real {
    border-radius: 0.8rem;
    transition: all 0.3s ease;
    border: none;
    padding: 1rem !important;
    margin-bottom: 1rem !important;
}

.ns-stat-card-real:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.2);
}

.ns-stat-icon {
    border-radius: 0.8rem;
    transition: transform 0.3s ease;
    width: 50px !important;
    height: 50px !important;
}

.ns-stat-card-real:hover .ns-stat-icon {
    transform: scale(1.05);
}

.ns-stat-number {
    font-size: 1.8rem !important;
    margin: 0.5rem 0 !important;
}

.ns-stat-label {
    font-size: 0.9rem !important;
    margin-bottom: 0.3rem !important;
}

.ns-stat-change small {
    font-size: 0.8rem !important;
}

/* تحسين التخطيط العام لتناسب الشاشة */
.ns-page-header-real {
    padding: 1rem 0 !important;
    margin-bottom: 1rem !important;
}

.ns-page-header-real h1 {
    font-size: 1.8rem !important;
    margin-bottom: 0 !important;
}

.ns-stats-real {
    margin-bottom: 1.5rem !important;
}

.ns-card-real {
    margin-bottom: 1.5rem !important;
}

.ns-card-header {
    padding: 0.8rem 1rem !important;
    font-size: 1rem !important;
}

.ns-card-body {
    padding: 1rem !important;
}

/* تقليل المسافات بين الصفوف */
.row {
    margin-bottom: 0.5rem !important;
}

.row.mt-3 {
    margin-top: 1rem !important;
}

/* تحسين ارتفاع الرسوم البيانية */
canvas {
    max-height: 250px !important;
}

/* تحسين المحتوى الرئيسي */
.ns-main-real {
    padding-top: 1rem !important;
}

/* إزالة المسافات الزائدة */
.container-fluid {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
}

/* ضمان عدم الحاجة لشريط التمرير */
body {
    overflow-x: hidden;
}

.ns-main-real {
    max-height: calc(100vh - 80px);
    overflow-y: auto;
}

/* تحسين للشاشات الصغيرة */
@media (max-height: 800px) {
    .ns-stat-number {
        font-size: 1.5rem !important;
    }

    .ns-stat-card-real {
        padding: 0.8rem !important;
    }

    canvas {
        max-height: 200px !important;
    }

    .ns-card-header {
        padding: 0.6rem 1rem !important;
        font-size: 0.9rem !important;
    }
}

/* تحسين للشاشات الكبيرة */
@media (min-height: 1000px) {
    canvas {
        max-height: 300px !important;
    }
}

/* ألوان البطاقات المختلفة */
.stat-card-blue {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    color: white !important;
}

.stat-card-green {
    background: linear-gradient(135deg, #28a745, #1e7e34) !important;
    color: white !important;
}

.stat-card-purple {
    background: linear-gradient(135deg, #6f42c1, #5a32a3) !important;
    color: white !important;
}

.stat-card-orange {
    background: linear-gradient(135deg, #fd7e14, #e55a00) !important;
    color: white !important;
}

/* تحسين النصوص على البطاقات الملونة */
.stat-card-blue .ns-stat-number,
.stat-card-green .ns-stat-number,
.stat-card-purple .ns-stat-number,
.stat-card-orange .ns-stat-number {
    color: white !important;
    font-weight: bold !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
}

.stat-card-blue .ns-stat-label,
.stat-card-green .ns-stat-label,
.stat-card-purple .ns-stat-label,
.stat-card-orange .ns-stat-label {
    color: rgba(255,255,255,0.9) !important;
    font-weight: 500 !important;
}

.stat-card-blue .ns-stat-icon i,
.stat-card-green .ns-stat-icon i,
.stat-card-purple .ns-stat-icon i,
.stat-card-orange .ns-stat-icon i {
    color: white !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
}

.stat-card-blue .text-light,
.stat-card-green .text-light,
.stat-card-purple .text-light,
.stat-card-orange .text-light {
    color: rgba(255,255,255,0.8) !important;
}

/* تأثيرات hover للبطاقات الملونة */
.stat-card-blue:hover {
    background: linear-gradient(135deg, #0056b3, #004085) !important;
    transform: translateY(-5px) !important;
    box-shadow: 0 10px 25px rgba(0, 123, 255, 0.3) !important;
}

.stat-card-green:hover {
    background: linear-gradient(135deg, #1e7e34, #155724) !important;
    transform: translateY(-5px) !important;
    box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3) !important;
}

.stat-card-purple:hover {
    background: linear-gradient(135deg, #5a32a3, #4c2a85) !important;
    transform: translateY(-5px) !important;
    box-shadow: 0 10px 25px rgba(111, 66, 193, 0.3) !important;
}

.stat-card-orange:hover {
    background: linear-gradient(135deg, #e55a00, #cc5200) !important;
    transform: translateY(-5px) !important;
    box-shadow: 0 10px 25px rgba(253, 126, 20, 0.3) !important;
}

/* تحسين الجدول */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* تحسين الرسوم البيانية */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

/* تحسينات خاصة بالرسومات البيانية الجديدة */
.ns-card-real canvas {
    max-height: 220px;
    border-radius: 0.5rem;
}

.ns-card-real .ns-card-body {
    position: relative;
    height: 260px;
    padding: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تحسين عرض الرسومات على الشاشات الصغيرة */
@media (max-width: 768px) {
    .ns-card-real .ns-card-body {
        height: 220px;
        padding: 0.5rem;
    }

    .ns-card-real canvas {
        max-height: 250px;
    }
}

/* تحسين ألوان عناوين الرسومات */
.ns-card-header i.fa-chart-pie,
.ns-card-header i.fa-chart-line,
.ns-card-header i.fa-chart-bar,
.ns-card-header i.fa-chart-area {
    color: #007bff;
}

/* تأثيرات hover للبطاقات */
.ns-card-real:hover {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* تحسين مظهر الرسومات البيانية */
.chart-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid #e9ecef;
}

.chart-card .ns-card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-bottom: none;
}

.chart-card .ns-card-header i {
    color: white;
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
    .ns-stats-real {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .activity-item {
        flex-direction: column;
        align-items: flex-start;
        text-align: right;
    }

    .activity-icon {
        margin: 0 0 0.5rem 0;
    }

    .activity-amount {
        margin-top: 0.5rem;
        text-align: right;
    }
}
</style>
{% endblock %}
