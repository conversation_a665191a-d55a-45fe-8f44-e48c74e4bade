<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح الشريط الجانبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-container { max-width: 800px; margin: 50px auto; padding: 20px; }
        .test-section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 10px; }
        .test-button { margin: 10px 5px; }
        .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-tools"></i> اختبار إصلاح الشريط الجانبي</h1>
        <p class="text-muted">اختبار للتأكد من أن جميع الأقسام تبدأ مطوية بشكل افتراضي</p>

        <div class="test-section">
            <h3><i class="fas fa-bug"></i> المشكلة المكتشفة</h3>
            <div class="status warning">
                <strong>المشكلة:</strong> بعض الأقسام في الشريط الجانبي تظهر مفتوحة رغم أنها يجب أن تكون مطوية افتراضياً
            </div>
            <div class="status info">
                <strong>السبب:</strong> تضارب بين نظامين مختلفين للطي والفتح + حالات محفوظة في localStorage
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-wrench"></i> الإصلاحات المطبقة</h3>
            <ul>
                <li>✅ <strong>توحيد نظام الطي والفتح</strong></li>
                <li>✅ <strong>ضمان الحالة الافتراضية (مطوي)</strong></li>
                <li>✅ <strong>إصلاح حفظ الحالات في localStorage</strong></li>
                <li>✅ <strong>إضافة وظائف لإعادة التعيين</strong></li>
            </ul>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-play"></i> اختبار الوظائف</h3>
            <p>استخدم هذه الأزرار لاختبار وظائف الشريط الجانبي:</p>
            
            <button class="btn btn-primary test-button" onclick="testEnsureCollapsed()">
                <i class="fas fa-compress"></i> ضمان طي جميع الأقسام
            </button>
            
            <button class="btn btn-warning test-button" onclick="testResetToDefault()">
                <i class="fas fa-undo"></i> إعادة تعيين للحالة الافتراضية
            </button>
            
            <button class="btn btn-danger test-button" onclick="testCollapseAll()">
                <i class="fas fa-times"></i> إغلاق الكل ومسح الحالات
            </button>
            
            <button class="btn btn-info test-button" onclick="testCheckLocalStorage()">
                <i class="fas fa-database"></i> فحص localStorage
            </button>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-clipboard-list"></i> نتائج الاختبار</h3>
            <div id="testResults">
                <div class="status info">
                    <i class="fas fa-info-circle"></i> اضغط على الأزرار أعلاه لبدء الاختبار
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-check-circle"></i> التحقق من الإصلاح</h3>
            <ol>
                <li><strong>افتح النظام الرئيسي</strong> في تبويب آخر</li>
                <li><strong>تحقق من الشريط الجانبي</strong> - يجب أن تكون جميع الأقسام مطوية</li>
                <li><strong>افتح بعض الأقسام</strong> وأعد تحميل الصفحة</li>
                <li><strong>تحقق مرة أخرى</strong> - يجب أن تعود الأقسام لحالتها المحفوظة</li>
                <li><strong>استخدم زر "إعادة تعيين"</strong> لإرجاع الكل للحالة الافتراضية</li>
            </ol>
        </div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultElement = document.createElement('div');
            resultElement.className = `status ${type}`;
            resultElement.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i> ${message}`;
            resultsDiv.appendChild(resultElement);
            
            // التمرير للنتيجة الجديدة
            resultElement.scrollIntoView({ behavior: 'smooth' });
        }

        function testEnsureCollapsed() {
            addResult('🔧 اختبار ضمان طي جميع الأقسام...', 'info');
            
            if (typeof window.parent.ensureAllSectionsCollapsed === 'function') {
                window.parent.ensureAllSectionsCollapsed();
                addResult('✅ تم تنفيذ وظيفة ضمان الطي بنجاح', 'success');
            } else {
                addResult('❌ وظيفة ضمان الطي غير متوفرة - تأكد من فتح النظام الرئيسي', 'warning');
            }
        }

        function testResetToDefault() {
            addResult('🔄 اختبار إعادة التعيين للحالة الافتراضية...', 'info');
            
            if (typeof window.parent.resetAllSectionsToDefault === 'function') {
                window.parent.resetAllSectionsToDefault();
                addResult('✅ تم إعادة تعيين جميع الأقسام للحالة الافتراضية', 'success');
            } else {
                addResult('❌ وظيفة إعادة التعيين غير متوفرة - تأكد من فتح النظام الرئيسي', 'warning');
            }
        }

        function testCollapseAll() {
            addResult('🗑️ اختبار إغلاق الكل ومسح الحالات...', 'info');
            
            if (typeof window.parent.collapseAllSections === 'function') {
                window.parent.collapseAllSections();
                addResult('✅ تم إغلاق جميع الأقسام ومسح الحالات المحفوظة', 'success');
            } else {
                addResult('❌ وظيفة إغلاق الكل غير متوفرة - تأكد من فتح النظام الرئيسي', 'warning');
            }
        }

        function testCheckLocalStorage() {
            addResult('🔍 فحص localStorage للحالات المحفوظة...', 'info');
            
            const navKeys = Object.keys(localStorage).filter(key => key.startsWith('nav-'));
            
            if (navKeys.length === 0) {
                addResult('✅ لا توجد حالات محفوظة في localStorage - الوضع الافتراضي', 'success');
            } else {
                addResult(`📊 تم العثور على ${navKeys.length} حالة محفوظة:`, 'info');
                navKeys.forEach(key => {
                    const value = localStorage.getItem(key);
                    addResult(`   - ${key}: ${value}`, 'info');
                });
            }
        }

        // رسالة ترحيب
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 تم تحميل صفحة اختبار إصلاح الشريط الجانبي', 'success');
            addResult('💡 استخدم الأزرار أعلاه لاختبار الوظائف المختلفة', 'info');
        });
    </script>
</body>
</html>
