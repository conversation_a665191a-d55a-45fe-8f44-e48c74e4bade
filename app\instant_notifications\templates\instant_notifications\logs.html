{% extends "base.html" %}

{% block title %}سجل الإشعارات الفورية{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-history text-secondary"></i>
            سجل الإشعارات الفورية
        </h1>
        <div>
            <button class="btn btn-info btn-sm" onclick="refreshLogs()">
                <i class="fas fa-sync-alt"></i> تحديث
            </button>
            <a href="{{ url_for('instant_notifications.dashboard') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter"></i>
                فلاتر البحث
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label for="statusFilter" class="form-label">حالة الإرسال</label>
                    <select class="form-select" id="statusFilter" onchange="filterLogs()">
                        <option value="">جميع الحالات</option>
                        <option value="success">نجح</option>
                        <option value="failed">فشل</option>
                        <option value="pending">في الانتظار</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="eventTypeFilter" class="form-label">نوع الحدث</label>
                    <select class="form-select" id="eventTypeFilter" onchange="filterLogs()">
                        <option value="">جميع الأنواع</option>
                        <option value="customs_clearance_notification">التخليص الجمركي</option>
                        <option value="delivery_notification">التسليم</option>
                        <option value="port_arrival_notification">وصول الميناء</option>
                        <option value="in_transit_notification">في الطريق</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="phoneFilter" class="form-label">رقم الهاتف</label>
                    <input type="text" class="form-control" id="phoneFilter" 
                           placeholder="967774893877" onkeyup="filterLogs()">
                </div>
                <div class="col-md-3">
                    <label for="trackingFilter" class="form-label">رقم التتبع</label>
                    <input type="text" class="form-control" id="trackingFilter" 
                           placeholder="COSU12345" onkeyup="filterLogs()">
                </div>
            </div>
        </div>
    </div>

    <!-- سجل الإشعارات -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-secondary">
                <i class="fas fa-list"></i>
                آخر 100 إشعار
            </h6>
        </div>
        <div class="card-body">
            {% if logs %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="logsTable">
                        <thead>
                            <tr>
                                <th>الوقت</th>
                                <th>نوع الحدث</th>
                                <th>رقم الهاتف</th>
                                <th>رقم التتبع</th>
                                <th>الحالة</th>
                                <th>الرسالة</th>
                                <th>معرف WhatsApp</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in logs %}
                            <tr class="log-row" 
                                data-status="{{ log.status }}" 
                                data-event-type="{{ log.event_type }}"
                                data-phone="{{ log.phone_number }}"
                                data-tracking="{{ log.tracking_number or '' }}">
                                <td>
                                    <small class="text-muted">
                                        {{ log.sent_at.strftime('%Y-%m-%d %H:%M:%S') if log.sent_at else 'غير محدد' }}
                                    </small>
                                </td>
                                <td>
                                    <span class="badge bg-info">
                                        {% if log.event_type == 'customs_clearance_notification' %}
                                            التخليص الجمركي
                                        {% elif log.event_type == 'delivery_notification' %}
                                            التسليم
                                        {% elif log.event_type == 'port_arrival_notification' %}
                                            وصول الميناء
                                        {% elif log.event_type == 'in_transit_notification' %}
                                            في الطريق
                                        {% else %}
                                            {{ log.event_type }}
                                        {% endif %}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ log.phone_number }}</span>
                                </td>
                                <td>
                                    {% if log.tracking_number %}
                                        <strong>{{ log.tracking_number }}</strong>
                                        {% if log.shipment_id %}
                                            <br><small class="text-muted">ID: {{ log.shipment_id }}</small>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if log.status == 'success' %}
                                        <span class="badge bg-success">نجح</span>
                                    {% elif log.status == 'failed' %}
                                        <span class="badge bg-danger">فشل</span>
                                        {% if log.error_message %}
                                            <br><small class="text-danger">{{ log.error_message }}</small>
                                        {% endif %}
                                    {% else %}
                                        <span class="badge bg-warning">{{ log.status or 'في الانتظار' }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="message-preview" style="max-width: 300px;">
                                        {{ log.message_content[:100] }}{% if log.message_content and log.message_content|length > 100 %}...{% endif %}
                                        {% if log.message_content %}
                                            <br><button class="btn btn-link btn-sm p-0" 
                                                       onclick="showFullMessage('{{ log.message_content|replace("'", "\\'") }}')">
                                                عرض كامل
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if log.whatsapp_message_id %}
                                        <small class="text-success">{{ log.whatsapp_message_id }}</small>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-secondary mb-3"></i>
                    <h5>لا توجد سجلات</h5>
                    <p class="text-muted">لم يتم إرسال أي إشعارات بعد</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row">
        <div class="col-md-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                رسائل ناجحة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="successCount">
                                {{ logs|selectattr("status", "equalto", "success")|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                رسائل فاشلة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="failedCount">
                                {{ logs|selectattr("status", "equalto", "failed")|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                في الانتظار
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="pendingCount">
                                {{ logs|rejectattr("status", "in", ["success", "failed"])|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                إجمالي الرسائل
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalCount">
                                {{ logs|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-envelope fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض الرسالة كاملة -->
<div class="modal fade" id="fullMessageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">محتوى الرسالة كاملاً</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <pre id="fullMessageContent" style="white-space: pre-wrap; margin: 0;"></pre>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
function filterLogs() {
    const statusFilter = document.getElementById('statusFilter').value.toLowerCase();
    const eventTypeFilter = document.getElementById('eventTypeFilter').value.toLowerCase();
    const phoneFilter = document.getElementById('phoneFilter').value.toLowerCase();
    const trackingFilter = document.getElementById('trackingFilter').value.toLowerCase();
    
    const rows = document.querySelectorAll('.log-row');
    let visibleCount = 0;
    
    rows.forEach(row => {
        const status = row.dataset.status.toLowerCase();
        const eventType = row.dataset.eventType.toLowerCase();
        const phone = row.dataset.phone.toLowerCase();
        const tracking = row.dataset.tracking.toLowerCase();
        
        const statusMatch = !statusFilter || status.includes(statusFilter);
        const eventTypeMatch = !eventTypeFilter || eventType.includes(eventTypeFilter);
        const phoneMatch = !phoneFilter || phone.includes(phoneFilter);
        const trackingMatch = !trackingFilter || tracking.includes(trackingFilter);
        
        if (statusMatch && eventTypeMatch && phoneMatch && trackingMatch) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    // تحديث العداد
    console.log(`عرض ${visibleCount} من ${rows.length} سجل`);
}

function showFullMessage(message) {
    document.getElementById('fullMessageContent').textContent = message;
    const modal = new bootstrap.Modal(document.getElementById('fullMessageModal'));
    modal.show();
}

function refreshLogs() {
    location.reload();
}

// تحديث تلقائي كل 30 ثانية
setInterval(() => {
    console.log('تحديث تلقائي للسجل...');
    // يمكن إضافة AJAX هنا لتحديث بدون إعادة تحميل
}, 30000);
</script>
{% endblock %}
