#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محاكاة السيناريو الحقيقي لحفظ الشحنة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def simulate_shipment_creation():
    """محاكاة عملية إنشاء الشحنة كما تحدث في الواقع"""
    print("🎭 محاكاة السيناريو الحقيقي...")
    
    try:
        from oracle_manager import get_oracle_manager
        db_manager = get_oracle_manager()
        
        if not db_manager.connect():
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        # 1. جلب معرف جديد من sequence (كما يحدث في الكود الحقيقي)
        print("\n🔢 جلب معرف جديد من sequence...")
        id_query = "SELECT cargo_shipments_seq.NEXTVAL FROM DUAL"
        id_result = db_manager.execute_query(id_query)
        cargo_shipment_id = id_result[0][0]
        print(f"✅ تم جلب معرف جديد: {cargo_shipment_id}")
        
        # 2. إنشاء بيانات شحنة كما في الواقع
        from datetime import datetime
        shipment_number = f"CRG{datetime.now().strftime('%Y%m%d%H%M%S')}"
        tracking_number = f"TRK{datetime.now().strftime('%H%M%S')}"
        
        print(f"📦 بيانات الشحنة:")
        print(f"  - معرف: {cargo_shipment_id}")
        print(f"  - رقم الشحنة: {shipment_number}")
        print(f"  - رقم التتبع: {tracking_number}")
        
        # 3. محاولة إدراج الشحنة بنفس SQL المستخدم في الكود الحقيقي
        print("\n🔥 محاولة إدراج الشحنة...")
        
        # نفس SQL من الكود الحقيقي
        shipment_insert_sql = """
            INSERT INTO cargo_shipments (
                id, shipment_number, bl_number, booking_number, shipper_id, consignee_id, notify_party_id,
                origin_port_id, destination_port_id, shipping_line_id, vessel_id,
                cargo_type, total_weight, net_weight, total_volume, total_packages, package_type,
                container_count, container_types, etd, eta,
                freight_cost, other_charges, total_cost, currency,
                customs_status, is_dangerous, temperature_controlled, created_by,
                purchase_order_id, tracking_number, vessel_name, voyage_number,
                container_seal_number, customs_declaration_number, insurance_policy_number,
                freight_forwarder, incoterms, port_of_loading, port_of_discharge,
                estimated_transit_time, shipping_type,
                cargo_description, special_instructions, shipping_instructions
            ) VALUES (
                :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13, :14, :15, :16, :17, :18, :19, :20,
                :21, :22, :23, :24, :25, :26, :27, :28, :29, :30, :31, :32, :33, :34, :35, :36, :37, :38, :39, :40, :41, :42,
                :43, :44, :45
            )
        """
        
        # نفس المعاملات من الكود الحقيقي (مع قيم تجريبية)
        shipment_params = [
            cargo_shipment_id,                                  # 1 - id
            shipment_number,                                    # 2 - shipment_number
            f"BL{datetime.now().strftime('%H%M%S')}",          # 3 - bl_number
            f"BK{datetime.now().strftime('%H%M%S')}",          # 4 - booking_number
            None,  # shipper_id
            None,  # consignee_id
            None,  # notify_party_id
            None,  # origin_port_id
            None,  # destination_port_id
            None,  # shipping_line_id
            None,  # vessel_id
            'عام',  # cargo_type
            None,  # total_weight
            None,  # net_weight
            None,  # total_volume
            None,  # total_packages
            None,  # package_type
            None,  # container_count
            None,  # container_types
            None,  # etd
            None,  # eta
            None,  # freight_cost
            None,  # other_charges
            None,  # total_cost
            'USD',  # currency
            'لم يتم التخليص',  # customs_status
            0,  # is_dangerous
            0,  # temperature_controlled
            'test_user',  # created_by
            None,  # purchase_order_id
            tracking_number,  # tracking_number
            'سفينة تجريبية',  # vessel_name
            'V001',  # voyage_number
            None,  # container_seal_number
            None,  # customs_declaration_number
            None,  # insurance_policy_number
            None,  # freight_forwarder
            None,  # incoterms
            None,  # port_of_loading
            None,  # port_of_discharge
            None,  # estimated_transit_time
            'FCL',  # shipping_type
            'وصف البضاعة',  # cargo_description
            'تعليمات خاصة',  # special_instructions
            'تعليمات الشحن'  # shipping_instructions
        ]
        
        print(f"🔍 عدد المعاملات: {len(shipment_params)} (يجب أن يكون 45)")
        
        try:
            rows_affected = db_manager.execute_update_no_commit(shipment_insert_sql, shipment_params)
            print(f"✅ تم إدراج الشحنة - {rows_affected} سجل متأثر")
            
            # التحقق الفوري
            verify_query = "SELECT COUNT(*) FROM cargo_shipments WHERE id = :1"
            verify_result = db_manager.execute_query(verify_query, [cargo_shipment_id])
            
            if verify_result and verify_result[0][0] > 0:
                print(f"✅ التحقق الفوري: الشحنة موجودة")
                
                # محاولة إدراج صنف
                print("\n📋 محاولة إدراج صنف...")
                item_id_query = "SELECT cargo_shipment_items_seq.NEXTVAL FROM DUAL"
                item_id_result = db_manager.execute_query(item_id_query)
                item_id = item_id_result[0][0]
                
                item_insert_sql = """
                    INSERT INTO cargo_shipment_items (
                        id, cargo_shipment_id, item_code, item_name, unit,
                        quantity, unit_price, total_price, container_id, recipient_id,
                        expiry_date, production_date, container_number, recipient_name, notes
                    ) VALUES (:1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13, :14, :15)
                """
                
                item_params = [
                    item_id,
                    cargo_shipment_id,
                    '001-TEST',
                    'صنف تجريبي',
                    'قطعة',
                    10.0,
                    100.0,
                    1000.0,
                    None,  # container_id
                    1,     # recipient_id
                    None,  # expiry_date
                    None,  # production_date
                    'TEST123',  # container_number
                    'مستلم تجريبي',  # recipient_name
                    'ملاحظات'  # notes
                ]
                
                try:
                    item_rows = db_manager.execute_update_no_commit(item_insert_sql, item_params)
                    print(f"✅ تم إدراج الصنف - {item_rows} سجل متأثر")
                    
                    # commit العملية
                    print("\n💾 حفظ العملية...")
                    db_manager.commit()
                    print("✅ تم حفظ العملية بنجاح")
                    
                    success = True
                    
                except Exception as e:
                    print(f"❌ خطأ في إدراج الصنف: {e}")
                    db_manager.rollback()
                    success = False
                    
            else:
                print(f"❌ التحقق الفوري فشل: الشحنة غير موجودة!")
                db_manager.rollback()
                success = False
                
        except Exception as e:
            print(f"❌ خطأ في إدراج الشحنة: {e}")
            print(f"🔍 تفاصيل الخطأ: {type(e)}")
            db_manager.rollback()
            success = False
        
        db_manager.close()
        return success
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء محاكاة السيناريو الحقيقي...")
    print("=" * 50)
    
    success = simulate_shipment_creation()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 المحاكاة نجحت - لا توجد مشكلة في الكود!")
    else:
        print("⚠️ المحاكاة فشلت - يوجد مشكلة في الكود")
