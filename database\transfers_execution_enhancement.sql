-- تحسين نظام تنفيذ الحوالات
-- Transfer Execution System Enhancement

-- 1. تحديث جدول TRANSFERS لإضافة حقول التنفيذ الجديدة (إذا لم تكن موجودة)
BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE transfers ADD execution_reference VARCHAR2(100)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN RAISE; END IF; -- تجاهل خطأ "العمود موجود"
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE transfers ADD execution_method VARCHAR2(50)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN RAISE; END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE transfers ADD execution_notes CLOB';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN RAISE; END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE transfers ADD total_suppliers NUMBER DEFAULT 1';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN RAISE; END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE transfers ADD execution_status VARCHAR2(20) DEFAULT ''pending''';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN RAISE; END IF;
END;
/

-- 2. إنشاء جدول موردين التنفيذ
CREATE TABLE transfer_execution_suppliers (
    id NUMBER PRIMARY KEY,
    transfer_id NUMBER NOT NULL,                 -- ربط بجدول TRANSFERS
    supplier_id NUMBER NOT NULL,                 -- ربط بجدول MONEY_CHANGERS_BANKS
    supplier_name VARCHAR2(200),                 -- اسم المورد (نسخة للأرشفة)
    supplier_type VARCHAR2(50),                  -- نوع المورد (نسخة للأرشفة)
    amount NUMBER(15,2) NOT NULL,                -- المبلغ المخصص لهذا المورد
    currency VARCHAR2(10) DEFAULT 'USD',         -- العملة
    exchange_rate NUMBER(10,4) DEFAULT 1.0000,   -- سعر الصرف
    commission NUMBER(15,2) DEFAULT 0,           -- العمولة
    supplier_reference VARCHAR2(100),            -- رقم المرجع من المورد
    supplier_notes VARCHAR2(1000),               -- ملاحظات خاصة بالمورد
    execution_order NUMBER DEFAULT 1,            -- ترتيب التنفيذ
    status VARCHAR2(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed')),
    executed_at TIMESTAMP,                       -- تاريخ التنفيذ الفعلي
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_exec_suppliers_transfer FOREIGN KEY (transfer_id) REFERENCES transfers(id),
    CONSTRAINT fk_exec_suppliers_supplier FOREIGN KEY (supplier_id) REFERENCES money_changers_banks(id),
    CONSTRAINT fk_exec_suppliers_user FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 3. إنشاء sequence و trigger للجدول الجديد
BEGIN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE transfer_exec_suppliers_seq START WITH 1 INCREMENT BY 1';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 THEN RAISE; END IF; -- تجاهل خطأ "الكائن موجود"
END;
/

CREATE OR REPLACE TRIGGER transfer_exec_suppliers_trigger
    BEFORE INSERT ON transfer_execution_suppliers
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := transfer_exec_suppliers_seq.NEXTVAL;
    END IF;
END;
/

-- 4. إنشاء فهارس للأداء
CREATE INDEX idx_exec_suppliers_transfer ON transfer_execution_suppliers(transfer_id);
CREATE INDEX idx_exec_suppliers_supplier ON transfer_execution_suppliers(supplier_id);
CREATE INDEX idx_exec_suppliers_status ON transfer_execution_suppliers(status);
CREATE INDEX idx_exec_suppliers_date ON transfer_execution_suppliers(executed_at);

-- 5. إنشاء view شامل لعرض تفاصيل التنفيذ
CREATE OR REPLACE VIEW v_transfer_execution_details AS
SELECT 
    t.id as transfer_id,
    t.transfer_number,
    t.execution_reference,
    t.execution_date,
    t.execution_method,
    t.execution_notes,
    t.total_suppliers,
    t.execution_status,
    
    -- معلومات الطلب الأصلي
    tr.request_number,
    tr.amount as original_amount,
    tr.currency as original_currency,
    
    -- معلومات المستفيد
    b.beneficiary_name,
    b.bank_name,
    b.bank_account,
    b.bank_branch,
    b.bank_country,
    
    -- معلومات الموردين
    tes.id as supplier_execution_id,
    tes.supplier_name,
    tes.supplier_type,
    tes.amount as supplier_amount,
    tes.exchange_rate,
    tes.commission,
    tes.supplier_reference,
    tes.supplier_notes,
    tes.execution_order,
    tes.status as supplier_status,
    tes.executed_at as supplier_executed_at,
    
    -- معلومات إضافية
    u.full_name as executed_by_name,
    t.created_at as transfer_created_at
    
FROM transfers t
LEFT JOIN transfer_requests tr ON t.request_id = tr.id
LEFT JOIN beneficiaries b ON tr.beneficiary_id = b.id
LEFT JOIN transfer_execution_suppliers tes ON t.id = tes.transfer_id
LEFT JOIN users u ON t.executed_by = u.id
ORDER BY t.id DESC, tes.execution_order;

-- 6. إنشاء trigger لتحديث إجمالي الموردين
CREATE OR REPLACE TRIGGER update_total_suppliers_trigger
    AFTER INSERT OR DELETE ON transfer_execution_suppliers
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        UPDATE transfers 
        SET total_suppliers = (
            SELECT COUNT(*) 
            FROM transfer_execution_suppliers 
            WHERE transfer_id = :NEW.transfer_id
        ),
        updated_execution_at = CURRENT_TIMESTAMP
        WHERE id = :NEW.transfer_id;
    END IF;
    
    IF DELETING THEN
        UPDATE transfers 
        SET total_suppliers = (
            SELECT COUNT(*) 
            FROM transfer_execution_suppliers 
            WHERE transfer_id = :OLD.transfer_id
        ),
        updated_execution_at = CURRENT_TIMESTAMP
        WHERE id = :OLD.transfer_id;
    END IF;
END;

-- 7. إضافة بيانات تجريبية للموردين
INSERT INTO money_changers_banks (id, name, type, contact_person, phone, commission_rate, is_active) VALUES
(100, 'البنك الأهلي السعودي - فرع الرياض', 'bank', 'أحمد محمد', '************', 1.5, 1);

INSERT INTO money_changers_banks (id, name, type, contact_person, phone, commission_rate, is_active) VALUES
(101, 'صرافة الراجحي - فرع جدة', 'money_changer', 'محمد علي', '************', 2.0, 1);

INSERT INTO money_changers_banks (id, name, type, contact_person, phone, commission_rate, is_active) VALUES
(102, 'بنك الرياض - فرع الدمام', 'bank', 'سارة أحمد', '************', 1.8, 1);

COMMIT;
