-- =====================================================
-- الإجراءات المخزنة لنظام مطابقة الأرصدة
-- Stored Procedures for Reconciliation System
-- =====================================================

-- 1. إجراء إنشاء دورة مطابقة جديدة
CREATE OR REPLACE PROCEDURE CREATE_RECONCILIATION_CYCLE(
    p_cycle_name IN VARCHAR2,
    p_period_from IN DATE,
    p_period_to IN DATE,
    p_cycle_type IN VARCHAR2 DEFAULT 'MONTHLY',
    p_created_by IN NUMBER,
    p_cycle_id OUT NUMBER
) AS
    v_cycle_description VARCHAR2(500);
BEGIN
    -- إنشاء وصف الدورة
    v_cycle_description := 'دورة مطابقة أرصدة الموردين للفترة من ' || 
                          TO_CHAR(p_period_from, 'DD/MM/YYYY') || 
                          ' إلى ' || TO_CHAR(p_period_to, 'DD/MM/YYYY');
    
    -- إدراج دورة جديدة
    INSERT INTO RECONCILIATION_CYCLES (
        cycle_name, cycle_description, reconciliation_date, period_from, period_to,
        cycle_type, status, created_by, created_date
    ) VALUES (
        p_cycle_name, v_cycle_description, SYSDATE, p_period_from, p_period_to,
        p_cycle_type, 'OPEN', p_created_by, CURRENT_TIMESTAMP
    ) RETURNING cycle_id INTO p_cycle_id;
    
    -- تسجيل النشاط
    INSERT INTO RECONCILIATION_ACTIVITY_LOG (
        cycle_id, activity_type, activity_description, performed_by
    ) VALUES (
        p_cycle_id, 'CYCLE_CREATED', 
        'تم إنشاء دورة مطابقة جديدة: ' || p_cycle_name,
        p_created_by
    );
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END CREATE_RECONCILIATION_CYCLE;

-- 2. إجراء بدء عملية المطابقة لدورة
CREATE OR REPLACE PROCEDURE START_RECONCILIATION_CYCLE(
    p_cycle_id IN NUMBER,
    p_started_by IN NUMBER
) AS
    v_supplier_count NUMBER := 0;
    v_cycle_status VARCHAR2(20);
BEGIN
    -- التحقق من حالة الدورة
    SELECT status INTO v_cycle_status
    FROM RECONCILIATION_CYCLES
    WHERE cycle_id = p_cycle_id;
    
    IF v_cycle_status != 'OPEN' THEN
        RAISE_APPLICATION_ERROR(-20001, 'لا يمكن بدء دورة مطابقة غير مفتوحة');
    END IF;
    
    -- تحديث حالة الدورة
    UPDATE RECONCILIATION_CYCLES SET
        status = 'IN_PROGRESS',
        started_by = p_started_by,
        started_date = CURRENT_TIMESTAMP,
        updated_by = p_started_by,
        updated_date = CURRENT_TIMESTAMP
    WHERE cycle_id = p_cycle_id;
    
    -- إنشاء سجلات مطابقة لجميع الموردين النشطين
    INSERT INTO SUPPLIER_RECONCILIATION (
        cycle_id, supplier_id, currency_code,
        system_opening_balance, system_debit_amount, system_credit_amount, system_closing_balance
    )
    SELECT 
        p_cycle_id,
        sb.supplier_id,
        sb.currency_code,
        sb.opening_balance,
        sb.debit_amount,
        sb.credit_amount,
        sb.current_balance
    FROM SUPPLIER_BALANCES sb
    JOIN SUPPLIERS s ON sb.supplier_id = s.id
    WHERE s.is_active = 1
    AND sb.current_balance != 0; -- فقط الموردين الذين لديهم أرصدة
    
    -- حساب عدد الموردين
    SELECT COUNT(*) INTO v_supplier_count
    FROM SUPPLIER_RECONCILIATION
    WHERE cycle_id = p_cycle_id;
    
    -- تحديث إحصائيات الدورة
    UPDATE RECONCILIATION_CYCLES SET
        total_suppliers = v_supplier_count
    WHERE cycle_id = p_cycle_id;
    
    -- تسجيل النشاط
    INSERT INTO RECONCILIATION_ACTIVITY_LOG (
        cycle_id, activity_type, activity_description, performed_by
    ) VALUES (
        p_cycle_id, 'RECONCILIATION_STARTED',
        'تم بدء عملية المطابقة لـ ' || v_supplier_count || ' مورد',
        p_started_by
    );
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END START_RECONCILIATION_CYCLE;

-- 3. إجراء تحديث بيانات كشف المورد
CREATE OR REPLACE PROCEDURE UPDATE_SUPPLIER_STATEMENT(
    p_reconciliation_id IN NUMBER,
    p_supplier_opening_balance IN NUMBER,
    p_supplier_debit_amount IN NUMBER,
    p_supplier_credit_amount IN NUMBER,
    p_supplier_closing_balance IN NUMBER,
    p_statement_date IN DATE,
    p_statement_reference IN VARCHAR2,
    p_updated_by IN NUMBER
) AS
    v_supplier_id NUMBER;
    v_cycle_id NUMBER;
BEGIN
    -- الحصول على معلومات المطابقة
    SELECT supplier_id, cycle_id INTO v_supplier_id, v_cycle_id
    FROM SUPPLIER_RECONCILIATION
    WHERE reconciliation_id = p_reconciliation_id;
    
    -- تحديث بيانات كشف المورد
    UPDATE SUPPLIER_RECONCILIATION SET
        supplier_opening_balance = p_supplier_opening_balance,
        supplier_debit_amount = p_supplier_debit_amount,
        supplier_credit_amount = p_supplier_credit_amount,
        supplier_closing_balance = p_supplier_closing_balance,
        supplier_statement_date = p_statement_date,
        supplier_statement_reference = p_statement_reference,
        updated_date = CURRENT_TIMESTAMP
    WHERE reconciliation_id = p_reconciliation_id;
    
    -- سيتم حساب الفروقات تلقائياً بواسطة الـ trigger
    
    -- تسجيل النشاط
    INSERT INTO RECONCILIATION_ACTIVITY_LOG (
        cycle_id, reconciliation_id, activity_type, activity_description, performed_by
    ) VALUES (
        v_cycle_id, p_reconciliation_id, 'STATEMENT_UPDATED',
        'تم تحديث بيانات كشف المورد رقم ' || v_supplier_id,
        p_updated_by
    );
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END UPDATE_SUPPLIER_STATEMENT;

-- 4. إجراء تحليل الفروقات التلقائي
CREATE OR REPLACE PROCEDURE ANALYZE_RECONCILIATION_DIFFERENCES(
    p_reconciliation_id IN NUMBER,
    p_analyzed_by IN NUMBER
) AS
    v_supplier_id NUMBER;
    v_cycle_id NUMBER;
    v_currency_code VARCHAR2(3);
    v_period_from DATE;
    v_period_to DATE;
    
    -- متغيرات للمعاملات
    CURSOR system_transactions IS
        SELECT transaction_id, transaction_date, reference_number, 
               original_amount, transaction_type, description
        FROM SUPPLIER_TRANSACTIONS st
        JOIN SUPPLIER_RECONCILIATION sr ON st.supplier_id = sr.supplier_id
        JOIN RECONCILIATION_CYCLES rc ON sr.cycle_id = rc.cycle_id
        WHERE sr.reconciliation_id = p_reconciliation_id
        AND st.transaction_date BETWEEN rc.period_from AND rc.period_to
        AND st.currency_code = sr.currency_code
        AND st.status = 'ACTIVE';
        
BEGIN
    -- الحصول على معلومات المطابقة
    SELECT sr.supplier_id, sr.cycle_id, sr.currency_code, rc.period_from, rc.period_to
    INTO v_supplier_id, v_cycle_id, v_currency_code, v_period_from, v_period_to
    FROM SUPPLIER_RECONCILIATION sr
    JOIN RECONCILIATION_CYCLES rc ON sr.cycle_id = rc.cycle_id
    WHERE sr.reconciliation_id = p_reconciliation_id;
    
    -- تحليل المعاملات وإنشاء سجلات الفروقات
    FOR trans IN system_transactions LOOP
        -- هنا يمكن إضافة منطق تحليل أكثر تعقيداً
        -- مثل مقارنة المعاملات مع كشف المورد
        
        -- مثال: إنشاء فرق للمعاملات الكبيرة
        IF ABS(trans.original_amount) > 10000 THEN
            INSERT INTO RECONCILIATION_DIFFERENCES (
                reconciliation_id, difference_type, difference_category,
                system_transaction_id, system_transaction_date, system_reference_number,
                system_amount, system_description,
                difference_reason, impact_level, identified_by
            ) VALUES (
                p_reconciliation_id, 'AMOUNT', trans.transaction_type,
                trans.transaction_id, trans.transaction_date, trans.reference_number,
                trans.original_amount, trans.description,
                'معاملة كبيرة تحتاج مراجعة', 'HIGH', p_analyzed_by
            );
        END IF;
    END LOOP;
    
    -- تسجيل النشاط
    INSERT INTO RECONCILIATION_ACTIVITY_LOG (
        cycle_id, reconciliation_id, activity_type, activity_description, performed_by
    ) VALUES (
        v_cycle_id, p_reconciliation_id, 'DIFFERENCES_ANALYZED',
        'تم تحليل الفروقات للمورد رقم ' || v_supplier_id,
        p_analyzed_by
    );
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END ANALYZE_RECONCILIATION_DIFFERENCES;

-- 5. إجراء إكمال دورة المطابقة
CREATE OR REPLACE PROCEDURE COMPLETE_RECONCILIATION_CYCLE(
    p_cycle_id IN NUMBER,
    p_completed_by IN NUMBER
) AS
    v_total_suppliers NUMBER;
    v_matched_suppliers NUMBER;
    v_unmatched_suppliers NUMBER;
    v_total_differences NUMBER(15,2);
BEGIN
    -- حساب الإحصائيات النهائية
    SELECT 
        COUNT(*),
        SUM(CASE WHEN reconciliation_status = 'MATCHED' THEN 1 ELSE 0 END),
        SUM(CASE WHEN reconciliation_status = 'UNMATCHED' THEN 1 ELSE 0 END),
        SUM(ABS(total_difference))
    INTO v_total_suppliers, v_matched_suppliers, v_unmatched_suppliers, v_total_differences
    FROM SUPPLIER_RECONCILIATION
    WHERE cycle_id = p_cycle_id;
    
    -- تحديث حالة الدورة
    UPDATE RECONCILIATION_CYCLES SET
        status = 'COMPLETED',
        total_suppliers = v_total_suppliers,
        matched_suppliers = v_matched_suppliers,
        unmatched_suppliers = v_unmatched_suppliers,
        total_differences_amount = v_total_differences,
        completed_by = p_completed_by,
        completed_date = CURRENT_TIMESTAMP,
        updated_by = p_completed_by,
        updated_date = CURRENT_TIMESTAMP
    WHERE cycle_id = p_cycle_id;
    
    -- تسجيل النشاط
    INSERT INTO RECONCILIATION_ACTIVITY_LOG (
        cycle_id, activity_type, activity_description, performed_by
    ) VALUES (
        p_cycle_id, 'CYCLE_COMPLETED',
        'تم إكمال دورة المطابقة - متطابق: ' || v_matched_suppliers || 
        ', غير متطابق: ' || v_unmatched_suppliers,
        p_completed_by
    );
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END COMPLETE_RECONCILIATION_CYCLE;

-- 6. إجراء إنشاء تعديل مطابقة
CREATE OR REPLACE PROCEDURE CREATE_RECONCILIATION_ADJUSTMENT(
    p_reconciliation_id IN NUMBER,
    p_difference_id IN NUMBER,
    p_adjustment_type IN VARCHAR2,
    p_adjustment_amount IN NUMBER,
    p_adjustment_currency IN VARCHAR2,
    p_adjustment_description IN VARCHAR2,
    p_adjustment_reason IN VARCHAR2,
    p_requested_by IN NUMBER,
    p_adjustment_id OUT NUMBER
) AS
    v_cycle_id NUMBER;
BEGIN
    -- الحصول على معرف الدورة
    SELECT cycle_id INTO v_cycle_id
    FROM SUPPLIER_RECONCILIATION
    WHERE reconciliation_id = p_reconciliation_id;
    
    -- إنشاء طلب التعديل
    INSERT INTO RECONCILIATION_ADJUSTMENTS (
        reconciliation_id, difference_id, adjustment_type,
        adjustment_amount, adjustment_currency, adjustment_description,
        adjustment_reason, requested_by, requested_date
    ) VALUES (
        p_reconciliation_id, p_difference_id, p_adjustment_type,
        p_adjustment_amount, p_adjustment_currency, p_adjustment_description,
        p_adjustment_reason, p_requested_by, CURRENT_TIMESTAMP
    ) RETURNING adjustment_id INTO p_adjustment_id;
    
    -- تسجيل النشاط
    INSERT INTO RECONCILIATION_ACTIVITY_LOG (
        cycle_id, reconciliation_id, activity_type, activity_description, performed_by
    ) VALUES (
        v_cycle_id, p_reconciliation_id, 'ADJUSTMENT_REQUESTED',
        'تم طلب تعديل بمبلغ ' || p_adjustment_amount || ' ' || p_adjustment_currency,
        p_requested_by
    );
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END CREATE_RECONCILIATION_ADJUSTMENT;

-- تم إنشاء الإجراءات المخزنة بنجاح
-- Stored procedures created successfully
