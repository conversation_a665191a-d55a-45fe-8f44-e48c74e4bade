{% extends "base.html" %}

{% block title %}بيانات العقود{% endblock %}

{% block extra_css %}
<style>
    .contracts-container {
        background: #f8f9fa;
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .page-header h1 {
        font-size: 2.5rem;
        font-weight: bold;
        margin: 0;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }
    
    .control-panel {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: 1px solid #e9ecef;
    }
    
    .btn-add-contract {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }
    
    .btn-add-contract:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        color: white;
    }

    .loading-spinner {
        display: none;
        text-align: center;
        padding: 50px;
    }
    
    .spinner-border {
        width: 3rem;
        height: 3rem;
        color: #667eea;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #dee2e6;
    }
    
    .search-box {
        border-radius: 25px;
        border: 2px solid #e9ecef;
        padding: 10px 20px;
        transition: all 0.3s ease;
    }
    
    .search-box:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<div class="contracts-container">
    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1><i class="fas fa-file-contract me-3"></i>بيانات العقود</h1>
                        <p class="mb-0 opacity-75">إدارة وعرض جميع العقود في النظام</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <!-- تم حذف زر إضافة عقد جديد -->
                    </div>
                </div>
            </div>
        </div>

        <!-- لوحة التحكم -->
        <div class="control-panel">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" id="searchInput" class="search-box form-control border-0" 
                               placeholder="البحث في العقود...">
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-outline-primary me-2" onclick="loadContracts()">
                        <i class="fas fa-sync-alt me-2"></i>تحديث
                    </button>
                </div>
            </div>
        </div>

        <!-- محتوى العقود -->
        <div class="control-panel">
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-3">جاري تحميل العقود...</p>
            </div>

            <div id="contractsTableContainer" style="display: none;">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم العقد</th>
                                <th>تاريخ العقد</th>
                                <th>المورد</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ النهاية</th>
                                <th>مبلغ العقد</th>
                                <th>حالة العقد</th>
                                <th>مستخدم</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="contractsTableBody">
                            <!-- سيتم ملء البيانات هنا بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="fas fa-file-contract"></i>
                <h4>لا توجد عقود</h4>
                <p>لم يتم العثور على أي عقود. انقر على "إضافة عقد جديد" لإنشاء أول عقد.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// متغيرات عامة
let contracts = [];

// تحميل الصفحة
$(document).ready(function() {
    loadContracts();
    setupEventListeners();
});

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // البحث
    $('#searchInput').on('input', function() {
        filterContracts($(this).val());
    });
}

// تحميل العقود
function loadContracts() {
    $('#loadingSpinner').show();
    $('#contractsTableContainer').hide();
    $('#emptyState').hide();
    
    $.ajax({
        url: '/contracts/api/contracts',
        method: 'GET',
        xhrFields: {
            withCredentials: true
        },
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        success: function(response) {
            if (response.success) {
                contracts = response.contracts;
                displayContracts(contracts);
            } else {
                showAlert('خطأ في تحميل البيانات: ' + response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            if (xhr.status === 302 || xhr.status === 401) {
                showAlert('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.', 'warning');
                setTimeout(function() {
                    window.location.href = '/auth/login';
                }, 2000);
            } else {
                showAlert('خطأ في الاتصال بالخادم', 'error');
                console.error('Error:', error);
            }
        },
        complete: function() {
            $('#loadingSpinner').hide();
        }
    });
}

// عرض العقود
function displayContracts(contractsList) {
    const tbody = $('#contractsTableBody');
    tbody.empty();
    
    if (contractsList.length === 0) {
        $('#emptyState').show();
        $('#contractsTableContainer').hide();
        return;
    }
    
    $('#emptyState').hide();
    $('#contractsTableContainer').show();
    
    contractsList.forEach(function(contract) {
        const row = createContractRow(contract);
        tbody.append(row);
    });
}

// إنشاء صف العقد
function createContractRow(contract) {
    const statusBadge = getStatusBadge(contract.contract_status);
    
    return `
        <tr>
            <td>${contract.contract_number || ''}</td>
            <td>${formatDate(contract.contract_date)}</td>
            <td>${contract.supplier_name || ''}</td>
            <td>${formatDate(contract.start_date)}</td>
            <td>${formatDate(contract.end_date)}</td>
            <td>${formatCurrency(contract.contract_amount, contract.currency_symbol)}</td>
            <td>${statusBadge}</td>
            <td>النظام</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <a href="/contracts/view/${contract.contract_id}" class="btn btn-outline-primary btn-sm" title="عرض">
                        <i class="fas fa-eye"></i>
                    </a>
                    <a href="/contracts/${contract.contract_id}/edit" class="btn btn-outline-secondary btn-sm" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </a>
                </div>
            </td>
        </tr>
    `;
}

// تنسيق حالة العقد
function getStatusBadge(status) {
    switch(status) {
        case 'ACTIVE':
            return '<span class="badge bg-success">نشط</span>';
        case 'DRAFT':
            return '<span class="badge bg-warning">مسودة</span>';
        case 'EXPIRED':
            return '<span class="badge bg-danger">منتهي</span>';
        default:
            return '<span class="badge bg-secondary">' + (status || 'غير محدد') + '</span>';
    }
}

// تنسيق التاريخ
function formatDate(dateStr) {
    if (!dateStr) return '';
    try {
        const date = new Date(dateStr);
        return date.toLocaleDateString('ar-SA');
    } catch (e) {
        return dateStr;
    }
}

// تنسيق العملة
function formatCurrency(amount, currencySymbol = 'ر.س') {
    if (!amount) return `0.00 ${currencySymbol}`;
    return parseFloat(amount).toLocaleString('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }) + ` ${currencySymbol}`;
}

// فلترة العقود
function filterContracts(searchTerm) {
    if (!searchTerm) {
        displayContracts(contracts);
        return;
    }
    
    const filtered = contracts.filter(contract => 
        (contract.contract_number && contract.contract_number.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (contract.supplier_name && contract.supplier_name.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    
    displayContracts(filtered);
}

// عرض التنبيهات
function showAlert(message, type = 'info') {
    // يمكن استخدام مكتبة تنبيهات مثل SweetAlert أو Bootstrap Toast
    alert(message);
}
</script>
{% endblock %}
