# -*- coding: utf-8 -*-
"""
المسارات الرئيسية للنظام المحاسبي
Main Routes for Accounting System
"""

from flask import render_template, request, session, redirect, url_for, flash, jsonify, make_response
from flask_login import login_required, current_user
from app.main import bp
from app import db
from app.models import *
from datetime import datetime, date
from database_manager import DatabaseManager
from sqlalchemy import func

@bp.route('/')
@bp.route('/index')
@login_required
def index():
    """الصفحة الرئيسية - تتطلب تسجيل دخول"""
    return redirect(url_for('main.dashboard'))

@bp.route('/dashboard')
@login_required
def dashboard():
    """لوحة المعلومات الرئيسية"""

    # استخدام Oracle JDBC للإحصائيات
    db_manager = DatabaseManager()

    try:
        # إحصائيات الشحنات الرئيسية
        total_shipments_result = db_manager.execute_query("SELECT COUNT(*) FROM cargo_shipments")
        total_shipments = total_shipments_result[0][0] if total_shipments_result else 0

        # الشحنات المسلمة
        delivered_result = db_manager.execute_query("SELECT COUNT(*) FROM cargo_shipments WHERE shipment_status = 'delivered'")
        delivered_shipments = delivered_result[0][0] if delivered_result else 0

        # الشحنات في الطريق
        in_transit_result = db_manager.execute_query("SELECT COUNT(*) FROM cargo_shipments WHERE shipment_status IN ('in_transit', 'arrived_port', 'customs_clearance')")
        in_transit_shipments = in_transit_result[0][0] if in_transit_result else 0

        # الشحنات المعلقة
        pending_result = db_manager.execute_query("SELECT COUNT(*) FROM cargo_shipments WHERE shipment_status IN ('pending', 'booked')")
        pending_shipments = pending_result[0][0] if pending_result else 0

        # إجمالي الحاويات
        containers_result = db_manager.execute_query("SELECT COUNT(*) FROM cargo_containers")
        total_containers = containers_result[0][0] if containers_result else 0

        # المخلصين النشطين
        agents_result = db_manager.execute_query("SELECT COUNT(*) FROM customs_agents WHERE is_active = 1")
        active_agents = agents_result[0][0] if agents_result else 0
    except:
        # قيم افتراضية في حالة الخطأ
        total_shipments = delivered_shipments = in_transit_shipments = pending_shipments = 0
        total_containers = active_agents = 0
    
    try:
        # أوامر التسليم الحديثة
        recent_orders_result = db_manager.execute_query("""
            SELECT id, order_number, contact_person, delivery_location, order_status, estimated_cost, created_date, expected_completion_date
            FROM delivery_orders
            ORDER BY created_date DESC
            FETCH FIRST 5 ROWS ONLY
        """)
        recent_orders = recent_orders_result if recent_orders_result else []

        # حساب الشحنات المتأخرة
        overdue_result = db_manager.execute_query("""
            SELECT COUNT(*) FROM cargo_shipments
            WHERE eta < SYSDATE AND shipment_status NOT IN ('delivered', 'cancelled')
        """)
        overdue_shipments = overdue_result[0][0] if overdue_result else 0

    except:
        # قيم افتراضية في حالة الخطأ
        recent_orders = []
        overdue_shipments = 0

    # قيم افتراضية للبيانات غير المتوفرة
    damaged_items = 0
    active_orders_list = []

    stats = {
        'shipments': {
            'total': total_shipments,
            'delivered': delivered_shipments,
            'in_transit': in_transit_shipments,
            'pending': pending_shipments,
            'overdue': overdue_shipments
        },
        'containers': {
            'total': total_containers,
            'active': in_transit_shipments  # الحاويات النشطة تقريباً = الشحنات في الطريق
        },
        'agents': {
            'total': active_agents,
            'active': active_agents
        },
        'operations': {
            'delivery_rate': round((delivered_shipments / total_shipments * 100) if total_shipments > 0 else 0, 1),
            'damaged_items': damaged_items
        }
    }
    
    return render_template('main/dashboard.html',
                         title='لوحة المعلومات',
                         stats=stats,
                         recent_orders=recent_orders,
                         active_orders=active_orders_list)

@bp.route('/set_language/<language>')
def set_language(language):
    """تغيير لغة النظام"""
    if language in ['ar', 'en']:
        session['language'] = language
        if language == 'ar':
            session['text_direction'] = 'rtl'
        else:
            session['text_direction'] = 'ltr'
        flash('تم تغيير اللغة بنجاح' if language == 'ar' else 'Language changed successfully', 'success')
    return redirect(request.referrer or url_for('main.index'))

@bp.route('/api/dashboard_stats')
@login_required
def api_dashboard_stats():
    """API لإحصائيات لوحة المعلومات"""
    
    # إحصائيات شهرية لطلبات الشراء
    monthly_requests = db.session.query(
        func.date_format(PurchaseRequest.created_at, '%Y-%m').label('month'),
        func.count(PurchaseRequest.id).label('count')
    ).group_by('month').order_by('month').limit(12).all()
    
    # إحصائيات حالة طلبات الشراء
    status_stats = db.session.query(
        PurchaseRequest.status,
        func.count(PurchaseRequest.id).label('count')
    ).group_by(PurchaseRequest.status).all()
    
    # أعلى الموردين طلباً
    top_suppliers = db.session.query(
        Supplier.name_ar,
        func.count(PurchaseRequest.id).label('count')
    ).join(PurchaseRequest).group_by(Supplier.id).order_by(
        func.count(PurchaseRequest.id).desc()
    ).limit(5).all()
    
    return jsonify({
        'monthly_requests': [{'month': r.month, 'count': r.count} for r in monthly_requests],
        'status_stats': [{'status': r.status, 'count': r.count} for r in status_stats],
        'top_suppliers': [{'name': r.name_ar, 'count': r.count} for r in top_suppliers]
    })


@bp.route('/netsuite-demo')
def netsuite_demo():
    """عرض تصميم NetSuite Oracle"""
    return render_template('netsuite_demo.html')


@bp.route('/manifest.json')
def manifest():
    """PWA Manifest File"""
    manifest_data = {
        "name": "النظام المحاسبي المتقدم",
        "short_name": "المحاسبة",
        "description": "نظام محاسبي متقدم للأجهزة المحمولة",
        "start_url": "/",
        "display": "standalone",
        "background_color": "#f5f7fa",
        "theme_color": "#667eea",
        "orientation": "portrait-primary",
        "scope": "/",
        "lang": "ar",
        "dir": "rtl",
        "categories": ["business", "finance", "productivity"],
        "icons": [
            {
                "src": "/static/icons/icon-72x72.png",
                "sizes": "72x72",
                "type": "image/png",
                "purpose": "any"
            },
            {
                "src": "/static/icons/icon-96x96.png",
                "sizes": "96x96",
                "type": "image/png",
                "purpose": "any"
            },
            {
                "src": "/static/icons/icon-128x128.png",
                "sizes": "128x128",
                "type": "image/png",
                "purpose": "any"
            },
            {
                "src": "/static/icons/icon-144x144.png",
                "sizes": "144x144",
                "type": "image/png",
                "purpose": "any"
            },
            {
                "src": "/static/icons/icon-152x152.png",
                "sizes": "152x152",
                "type": "image/png",
                "purpose": "any"
            },
            {
                "src": "/static/icons/icon-192x192.png",
                "sizes": "192x192",
                "type": "image/png",
                "purpose": "any maskable"
            },
            {
                "src": "/static/icons/icon-384x384.png",
                "sizes": "384x384",
                "type": "image/png",
                "purpose": "any"
            },
            {
                "src": "/static/icons/icon-512x512.png",
                "sizes": "512x512",
                "type": "image/png",
                "purpose": "any maskable"
            }
        ],
        "shortcuts": [
            {
                "name": "لوحة المعلومات",
                "short_name": "لوحة المعلومات",
                "description": "الوصول السريع للوحة المعلومات الرئيسية",
                "url": "/dashboard",
                "icons": [{"src": "/static/icons/icon-192x192.png", "sizes": "192x192"}]
            },
            {
                "name": "المخزون",
                "short_name": "المخزون",
                "description": "إدارة المخزون والأصناف",
                "url": "/inventory/",
                "icons": [{"src": "/static/icons/icon-192x192.png", "sizes": "192x192"}]
            }
        ],
        "prefer_related_applications": False,
        "related_applications": [],
        "id": "/",
        "launch_handler": {
            "client_mode": "navigate-existing"
        }
    }

    response = make_response(jsonify(manifest_data))
    response.headers['Content-Type'] = 'application/manifest+json'
    return response


@bp.route('/sw.js')
def service_worker():
    """Service Worker File"""
    response = make_response(render_template('sw.js'))
    response.headers['Content-Type'] = 'application/javascript'
    response.headers['Service-Worker-Allowed'] = '/'
    return response
