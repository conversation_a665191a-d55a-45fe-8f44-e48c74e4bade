<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📝 قوالب البريد الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            direction: rtl;
            color: #ffffff;
        }
        
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }
        
        .templates-container {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .template-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .template-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
            border-color: rgba(102, 126, 234, 0.5);
        }
        
        .template-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 0.5rem;
        }
        
        .template-subject {
            color: #a0a0a0;
            font-size: 0.9rem;
            margin-bottom: 0.8rem;
        }
        
        .template-preview {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            max-height: 100px;
            overflow: hidden;
            position: relative;
        }
        
        .template-preview::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 30px;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
        }
        
        .template-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: #c0c0c0;
        }
        
        .template-badge {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 15px;
            font-size: 0.7rem;
            font-weight: 600;
        }
        
        .public-badge {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }
        
        .no-templates {
            text-align: center;
            padding: 4rem 2rem;
        }
        
        .no-templates-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .back-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        
        .add-template-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            margin-bottom: 2rem;
        }
        
        .add-template-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
            color: white;
            text-decoration: none;
        }
        
        /* شريط التنقل */
        .navbar-custom {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }

        .navbar-brand {
            color: #ffffff !important;
            font-weight: 700;
            font-size: 1.5rem;
        }

        .navbar-nav .nav-link {
            color: #a0a0a0 !important;
            font-weight: 500;
            padding: 0.8rem 1.2rem !important;
            border-radius: 50px;
            transition: all 0.3s ease;
            margin: 0 0.2rem;
        }

        .navbar-nav .nav-link:hover {
            color: #ffffff !important;
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        .navbar-nav .nav-link.active {
            color: #ffffff !important;
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .navbar-toggler {
            border: none;
            color: #ffffff;
        }

        .navbar-toggler:focus {
            box-shadow: none;
        }

        .breadcrumb-custom {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
        }

        .breadcrumb-custom .breadcrumb {
            margin: 0;
            background: none;
            padding: 0;
        }

        .breadcrumb-custom .breadcrumb-item {
            color: #a0a0a0;
        }

        .breadcrumb-custom .breadcrumb-item.active {
            color: #ffffff;
            font-weight: 600;
        }

        .breadcrumb-custom .breadcrumb-item a {
            color: #667eea;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .breadcrumb-custom .breadcrumb-item a:hover {
            color: #ffffff;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }

            .page-title {
                font-size: 2rem;
            }

            .template-card {
                padding: 1rem;
            }

            .navbar-nav {
                text-align: center;
            }

            .navbar-nav .nav-link {
                margin: 0.2rem 0;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('email.inbox') }}">
                <i class="fas fa-envelope me-2"></i>
                نظام البريد الإلكتروني
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <i class="fas fa-bars"></i>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.inbox') }}">
                            <i class="fas fa-inbox me-1"></i>
                            صندوق الوارد
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.compose') }}">
                            <i class="fas fa-edit me-1"></i>
                            إنشاء رسالة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.contacts') }}">
                            <i class="fas fa-address-book me-1"></i>
                            دفتر العناوين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('email.templates') }}">
                            <i class="fas fa-file-alt me-1"></i>
                            القوالب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.analytics') }}">
                            <i class="fas fa-chart-line me-1"></i>
                            التحليلات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.settings') }}">
                            <i class="fas fa-cog me-1"></i>
                            الإعدادات
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user me-1"></i>
                            {{ current_user.username if current_user.is_authenticated else 'المستخدم' }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url_for('main.dashboard') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>لوحة المعلومات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <!-- Breadcrumb -->
        <div class="breadcrumb-custom">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('main.dashboard') }}">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('email.inbox') }}">
                            <i class="fas fa-envelope me-1"></i>
                            البريد الإلكتروني
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <i class="fas fa-file-alt me-1"></i>
                        القوالب
                    </li>
                </ol>
            </nav>
        </div>

        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-file-alt"></i>
                قوالب البريد الإلكتروني
            </h1>
            <p style="color: #a0a0a0; font-size: 1.1rem;">
                إدارة قوالب الرسائل الجاهزة
            </p>
        </div>
        
        <div class="templates-container">
            <!-- Add Template Button -->
            <div class="text-center">
                <button type="button" class="add-template-btn" data-bs-toggle="modal" data-bs-target="#addTemplateModal">
                    <i class="fas fa-plus"></i>
                    إنشاء قالب جديد
                </button>
            </div>
            
            {% if templates and templates|length > 0 %}
                <!-- Templates List -->
                <div class="row">
                    {% for template in templates %}
                    <div class="col-md-6 col-lg-4">
                        <div class="template-card">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div class="template-name">
                                    {{ template.name }}
                                </div>
                                <div>
                                    {% if template.is_public %}
                                        <span class="template-badge public-badge">عام</span>
                                    {% else %}
                                        <span class="template-badge">خاص</span>
                                    {% endif %}
                                </div>
                            </div>
                            
                            {% if template.subject %}
                            <div class="template-subject">
                                <i class="fas fa-tag me-1"></i>
                                {{ template.subject }}
                            </div>
                            {% endif %}
                            
                            {% if template.body_text %}
                            <div class="template-preview">
                                {{ template.body_text[:150] }}{% if template.body_text|length > 150 %}...{% endif %}
                            </div>
                            {% endif %}
                            
                            <div class="template-meta">
                                <div>
                                    {% if template.usage_count %}
                                        <i class="fas fa-eye me-1"></i>{{ template.usage_count }} استخدام
                                    {% endif %}
                                    {% if template.category %}
                                        <i class="fas fa-folder me-1 ms-2"></i>{{ template.category }}
                                    {% endif %}
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="previewExistingTemplate('{{ template.subject }}', '{{ template.body_text }}')"><i class="fas fa-eye me-2"></i>معاينة</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="fas fa-edit me-2"></i>تعديل</a></li>
                                        <li><a class="dropdown-item" href="#" onclick="useTemplate('{{ template.id }}')"><i class="fas fa-envelope me-2"></i>استخدام</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteTemplate('{{ template.id }}', '{{ template.name }}')"><i class="fas fa-trash me-2"></i>حذف</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <!-- No Templates Message -->
                <div class="no-templates">
                    <div class="no-templates-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 style="color: #ffffff; margin-bottom: 1rem;">لا توجد قوالب</h3>
                    <p style="color: #a0a0a0;">
                        لم يتم إنشاء أي قوالب بعد.<br>
                        ابدأ بإنشاء قوالب لرسائلك المتكررة لتوفير الوقت.
                    </p>
                </div>
            {% endif %}
        </div>
        
        <!-- Back Button -->
        <div class="text-center">
            <a href="{{ url_for('email.inbox') }}" class="back-btn">
                <i class="fas fa-arrow-right"></i>
                العودة إلى صندوق الوارد
            </a>
        </div>
    </div>

    <!-- Add Template Modal -->
    <div class="modal fade" id="addTemplateModal" tabindex="-1" aria-labelledby="addTemplateModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content" style="background: linear-gradient(135deg, #1a1a2e, #16213e); border: 1px solid rgba(255, 255, 255, 0.2);">
                <div class="modal-header" style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                    <h5 class="modal-title" id="addTemplateModalLabel" style="color: #ffffff;">
                        <i class="fas fa-file-plus me-2"></i>
                        إنشاء قالب جديد
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="addTemplateForm">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="template_name" class="form-label" style="color: #ffffff;">
                                    <i class="fas fa-tag me-1"></i>
                                    اسم القالب *
                                </label>
                                <input type="text" class="form-control" id="template_name" name="template_name" required
                                       style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); color: #ffffff;"
                                       placeholder="مثال: رسالة ترحيب">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="category" class="form-label" style="color: #ffffff;">
                                    <i class="fas fa-folder me-1"></i>
                                    الفئة
                                </label>
                                <select class="form-control" id="category" name="category"
                                        style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); color: #ffffff;">
                                    <option value="">اختر الفئة</option>
                                    <option value="ترحيب">ترحيب</option>
                                    <option value="تأكيد">تأكيد</option>
                                    <option value="تذكير">تذكير</option>
                                    <option value="شكر">شكر</option>
                                    <option value="إعلان">إعلان</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="subject_template" class="form-label" style="color: #ffffff;">
                                <i class="fas fa-envelope me-1"></i>
                                موضوع الرسالة *
                            </label>
                            <input type="text" class="form-control" id="subject_template" name="subject_template" required
                                   style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); color: #ffffff;"
                                   placeholder="مثال: مرحباً بك في موقعنا">
                        </div>
                        <div class="mb-3">
                            <label for="body_template" class="form-label" style="color: #ffffff;">
                                <i class="fas fa-file-text me-1"></i>
                                محتوى الرسالة *
                            </label>
                            <textarea class="form-control" id="body_template" name="body_template" rows="8" required
                                      style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); color: #ffffff;"
                                      placeholder="اكتب محتوى القالب هنا...&#10;&#10;يمكنك استخدام متغيرات مثل:&#10;{name} - اسم المستلم&#10;{email} - بريد المستلم&#10;{date} - التاريخ الحالي"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_public" name="is_public">
                                    <label class="form-check-label" for="is_public" style="color: #ffffff;">
                                        <i class="fas fa-globe me-1"></i>
                                        قالب عام (متاح لجميع المستخدمين)
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    القوالب العامة يمكن لجميع المستخدمين استخدامها
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer" style="border-top: 1px solid rgba(255, 255, 255, 0.1);">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </button>
                        <button type="button" class="btn btn-info me-2" onclick="previewTemplate()">
                            <i class="fas fa-eye me-1"></i>
                            معاينة
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ القالب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Preview Modal -->
    <div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="background: linear-gradient(135deg, #1a1a2e, #16213e); border: 1px solid rgba(255, 255, 255, 0.2);">
                <div class="modal-header" style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                    <h5 class="modal-title" id="previewModalLabel" style="color: #ffffff;">
                        <i class="fas fa-eye me-2"></i>
                        معاينة القالب
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="card" style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2);">
                        <div class="card-header" style="background: rgba(255, 255, 255, 0.1); border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <h6 class="mb-0" style="color: #ffffff;">
                                <i class="fas fa-envelope me-1"></i>
                                الموضوع: <span id="previewSubject"></span>
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="previewBody" style="color: #ffffff; white-space: pre-wrap; line-height: 1.6;"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="border-top: 1px solid rgba(255, 255, 255, 0.1);">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        إغلاق
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // معالج إضافة قالب جديد
        document.getElementById('addTemplateForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const templateData = {
                name: formData.get('template_name'),
                subject_template: formData.get('subject_template'),
                body_template: formData.get('body_template'),
                category: formData.get('category'),
                is_public: formData.get('is_public') ? true : false
            };

            // إرسال البيانات إلى الخادم
            fetch('/email/api/templates', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(templateData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إغلاق النموذج
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addTemplateModal'));
                    modal.hide();

                    // إعادة تحميل الصفحة لإظهار القالب الجديد
                    location.reload();
                } else {
                    alert('خطأ: ' + (data.message || 'فشل في إضافة القالب'));
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                alert('حدث خطأ في إضافة القالب');
            });
        });

        // دالة معاينة القالب
        function previewTemplate() {
            const subject = document.getElementById('subject_template').value;
            const body = document.getElementById('body_template').value;

            if (!subject || !body) {
                alert('يرجى ملء الموضوع والمحتوى أولاً');
                return;
            }

            document.getElementById('previewSubject').textContent = subject;
            document.getElementById('previewBody').textContent = body;

            const previewModal = new bootstrap.Modal(document.getElementById('previewModal'));
            previewModal.show();
        }

        // دالة حذف قالب
        function deleteTemplate(templateId, templateName) {
            if (confirm('هل أنت متأكد من حذف القالب "' + templateName + '"؟')) {
                fetch('/email/api/templates/' + templateId, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('خطأ: ' + (data.message || 'فشل في حذف القالب'));
                    }
                })
                .catch(error => {
                    console.error('خطأ:', error);
                    alert('حدث خطأ في حذف القالب');
                });
            }
        }

        // دالة استخدام القالب
        function useTemplate(templateId) {
            window.location.href = '/email/compose?template_id=' + templateId;
        }

        // دالة معاينة القوالب الموجودة
        function previewExistingTemplate(subject, body) {
            document.getElementById('previewSubject').textContent = subject || 'بدون موضوع';
            document.getElementById('previewBody').textContent = body || 'بدون محتوى';

            const previewModal = new bootstrap.Modal(document.getElementById('previewModal'));
            previewModal.show();
        }
    </script>
</body>
</html>
