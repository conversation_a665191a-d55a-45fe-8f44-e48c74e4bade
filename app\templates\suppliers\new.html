{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="ns-container-fluid">
    <!-- Header -->
    <div class="ns-header">
        <div class="ns-header-content">
            <h1 class="ns-header-title">
                <i class="fas fa-plus-circle ns-icon ns-icon-primary"></i>
                {{ title }}
            </h1>
            <div class="ns-header-actions">
                <a href="{{ url_for('suppliers.index') }}" class="ns-btn ns-btn-secondary">
                    <i class="fas fa-arrow-right ns-icon"></i>
                    العودة للموردين
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="ns-card" style="margin-top: 2rem;">
        <div class="ns-card-body">
            <form method="POST" novalidate>
                {{ form.hidden_tag() }}

                <!-- معلومات أساسية -->
                <div class="ns-card" style="margin-bottom: 2rem;">
                    <div class="ns-card-header">
                        <h3 class="ns-card-title">
                            <i class="fas fa-info-circle ns-icon ns-icon-primary"></i>
                            المعلومات الأساسية
                        </h3>
                    </div>
                    <div class="ns-card-body">
                        <div class="ns-row">
                            <div class="ns-col ns-col-4 ns-col-md-6 ns-col-sm-12">
                                <div class="ns-form-group">
                                    {{ form.code.label(class="ns-label ns-label-required") }}
                                    {{ form.code(class="ns-input") }}
                                    {% if form.code.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.code.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="ns-col ns-col-4 ns-col-md-6 ns-col-sm-12">
                                <div class="ns-form-group">
                                    {{ form.name.label(class="ns-label ns-label-required") }}
                                        {{ form.name(class="ns-input") }}
                                    {% if form.name.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.name.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="ns-col ns-col-4 ns-col-md-6 ns-col-sm-12">
                                <div class="ns-form-group">
                                    {{ form.contact_person.label(class="ns-label") }}
                                    {{ form.contact_person(class="ns-input") }}
                                    {% if form.contact_person.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.contact_person.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="ns-row">
                            <div class="ns-col ns-col-3 ns-col-md-6 ns-col-sm-12">
                                <div class="ns-form-group">
                                    {{ form.type.label(class="ns-label ns-label-required") }}
                                    {{ form.type(class="ns-select") }}
                                    {% if form.type.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.type.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="ns-col ns-col-3 ns-col-md-6 ns-col-sm-12">
                                <div class="ns-form-group">
                                    {{ form.category.label(class="ns-label ns-label-required") }}
                                    {{ form.category(class="ns-select") }}
                                    {% if form.category.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.category.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="ns-col ns-col-3 ns-col-md-6 ns-col-sm-12">
                                <div class="ns-form-group">
                                    {{ form.phone.label(class="ns-label") }}
                                    {{ form.phone(class="ns-input") }}
                                    {% if form.phone.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.phone.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="ns-col ns-col-3 ns-col-md-6 ns-col-sm-12">
                                <div class="ns-form-group">
                                    {{ form.mobile.label(class="ns-label") }}
                                    {{ form.mobile(class="ns-input") }}
                                    {% if form.mobile.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.mobile.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="ns-row">
                            <div class="ns-col ns-col-6 ns-col-md-12">
                                <div class="ns-form-group">
                                    {{ form.email.label(class="ns-label") }}
                                    {{ form.email(class="ns-input") }}
                                    {% if form.email.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.email.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="ns-col ns-col-6 ns-col-md-12">
                                <div class="ns-form-group">
                                    {{ form.website.label(class="ns-label") }}
                                    {{ form.website(class="ns-input") }}
                                    {% if form.website.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.website.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات العنوان -->
                <div class="ns-card" style="margin-bottom: 2rem;">
                    <div class="ns-card-header">
                        <h3 class="ns-card-title">
                            <i class="fas fa-map-marker-alt ns-icon ns-icon-primary"></i>
                            معلومات العنوان
                        </h3>
                    </div>
                    <div class="ns-card-body">
                        <div class="ns-row">
                            <div class="ns-col ns-col-12">
                                <div class="ns-form-group">
                                    {{ form.address.label(class="ns-label") }}
                                    {{ form.address(class="ns-textarea") }}
                                    {% if form.address.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.address.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="ns-row">
                            <div class="ns-col ns-col-4 ns-col-md-6 ns-col-sm-12">
                                <div class="ns-form-group">
                                    {{ form.city.label(class="ns-label") }}
                                    {{ form.city(class="ns-input") }}
                                    {% if form.city.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.city.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="ns-col ns-col-4 ns-col-md-6 ns-col-sm-12">
                                <div class="ns-form-group">
                                    {{ form.country.label(class="ns-label") }}
                                    {{ form.country(class="ns-input") }}
                                    {% if form.country.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.country.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="ns-col ns-col-4 ns-col-md-6 ns-col-sm-12">
                                <div class="ns-form-group">
                                    {{ form.postal_code.label(class="ns-label") }}
                                    {{ form.postal_code(class="ns-input") }}
                                    {% if form.postal_code.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.postal_code.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات قانونية ومالية -->
                <div class="ns-card" style="margin-bottom: 2rem;">
                    <div class="ns-card-header">
                        <h3 class="ns-card-title">
                            <i class="fas fa-file-contract ns-icon ns-icon-primary"></i>
                            المعلومات القانونية والمالية
                        </h3>
                    </div>
                    <div class="ns-card-body">
                        <div class="ns-row">
                            <div class="ns-col ns-col-4 ns-col-md-6 ns-col-sm-12">
                                <div class="ns-form-group">
                                    {{ form.tax_number.label(class="ns-label") }}
                                    {{ form.tax_number(class="ns-input") }}
                                    {% if form.tax_number.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.tax_number.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="ns-col ns-col-4 ns-col-md-6 ns-col-sm-12">
                                <div class="ns-form-group">
                                    {{ form.commercial_register.label(class="ns-label") }}
                                    {{ form.commercial_register(class="ns-input") }}
                                    {% if form.commercial_register.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.commercial_register.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="ns-col ns-col-4 ns-col-md-6 ns-col-sm-12">
                                <div class="ns-form-group">
                                    {{ form.payment_terms.label(class="ns-label") }}
                                    {{ form.payment_terms(class="ns-input") }}
                                    {% if form.payment_terms.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.payment_terms.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="ns-row">
                            <div class="ns-col ns-col-6 ns-col-md-12">
                                <div class="ns-form-group">
                                    {{ form.credit_limit.label(class="ns-label") }}
                                    {{ form.credit_limit(class="ns-input") }}
                                    {% if form.credit_limit.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.credit_limit.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="ns-col ns-col-6 ns-col-md-12">
                                <div class="ns-form-group">
                                    {{ form.rating.label(class="ns-label") }}
                                    {{ form.rating(class="ns-select") }}
                                    {% if form.rating.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.rating.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملاحظات وحالة -->
                <div class="ns-card" style="margin-bottom: 2rem;">
                    <div class="ns-card-header">
                        <h3 class="ns-card-title">
                            <i class="fas fa-sticky-note ns-icon ns-icon-primary"></i>
                            ملاحظات وحالة
                        </h3>
                    </div>
                    <div class="ns-card-body">
                        <div class="ns-row">
                            <div class="ns-col ns-col-12">
                                <div class="ns-form-group">
                                    {{ form.notes.label(class="ns-label") }}
                                    {{ form.notes(class="ns-textarea") }}
                                    {% if form.notes.errors %}
                                        <div class="ns-error-text">
                                            {% for error in form.notes.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="ns-row">
                            <div class="ns-col ns-col-12">
                                <div style="display: flex; align-items: center; gap: 0.5rem; margin-top: 1rem;">
                                    {{ form.is_active(style="width: auto; margin: 0;") }}
                                    {{ form.is_active.label(style="margin: 0; font-weight: 500;") }}
                                </div>
                                {% if form.is_active.errors %}
                                    <div class="ns-error-text">
                                        {% for error in form.is_active.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    {{ form.submit(class="ns-btn ns-btn-primary") }}
                    <a href="{{ url_for('suppliers.index') }}" class="ns-btn ns-btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>
</div>

{% block scripts %}
<script>
// Initialize new supplier form
document.addEventListener('DOMContentLoaded', function() {
    // Show welcome notification
    setTimeout(() => {
        nsNotifications.info('نموذج إضافة مورد جديد بتصميم NetSuite!', { duration: 3000 });
    }, 500);

    // Add form validation feedback
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            nsNotifications.info('جاري حفظ بيانات المورد...', { duration: 2000 });
        });
    }

    // Auto-generate supplier code if empty
    const codeInput = document.querySelector('input[name="code"]');
    const nameInput = document.querySelector('input[name="name"]');

    if (codeInput && nameInput && !codeInput.value) {
        nameInput.addEventListener('blur', function() {
            if (this.value && !codeInput.value) {
                // Generate simple code from name
                const code = 'SUP' + Date.now().toString().slice(-6);
                codeInput.value = code;
                nsNotifications.info('تم إنشاء كود المورد تلقائياً', { duration: 2000 });
            }
        });
    }

    console.log('NetSuite New Supplier form initialized');
});
</script>
{% endblock %}
