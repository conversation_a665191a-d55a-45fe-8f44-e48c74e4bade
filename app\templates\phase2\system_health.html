{% extends "base.html" %}

{% block title %}مراقبة صحة النظام - النظام المحاسبي المتقدم{% endblock %}

{% block extra_css %}
<style>
.system-health {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.health-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.health-card:hover {
    transform: translateY(-5px);
}

.status-healthy {
    color: #28a745;
}

.status-warning {
    color: #ffc107;
}

.status-error {
    color: #dc3545;
}

.component-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    border-left: 4px solid #28a745;
}

.component-warning {
    border-left-color: #ffc107;
}

.component-error {
    border-left-color: #dc3545;
}

.metric-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.metric-item:last-child {
    border-bottom: none;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-indicator.healthy {
    background-color: #28a745;
    animation: pulse-green 2s infinite;
}

.status-indicator.warning {
    background-color: #ffc107;
    animation: pulse-yellow 2s infinite;
}

.status-indicator.error {
    background-color: #dc3545;
    animation: pulse-red 2s infinite;
}

@keyframes pulse-green {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

@keyframes pulse-yellow {
    0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
}

@keyframes pulse-red {
    0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}
</style>
{% endblock %}

{% block content %}
<div class="system-health">
    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="health-card">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-1">
                        <i class="fas fa-heartbeat text-primary me-2"></i>
                        مراقبة صحة النظام
                    </h2>
                    <p class="text-muted mb-0">مراقبة حالة جميع مكونات المرحلة الثانية المتقدمة</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex align-items-center justify-content-end">
                        <span class="status-indicator {{ health_status.overall_status }}"></span>
                        <span class="me-3">
                            {% if health_status.overall_status == 'healthy' %}
                                <span class="text-success">النظام يعمل بشكل طبيعي</span>
                            {% elif health_status.overall_status == 'warning' %}
                                <span class="text-warning">تحذيرات في النظام</span>
                            {% else %}
                                <span class="text-danger">مشاكل في النظام</span>
                            {% endif %}
                        </span>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshHealth()">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- نظرة عامة على الحالة -->
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="health-card text-center">
                    <i class="fas fa-robot fa-2x text-primary mb-3"></i>
                    <h5>الأتمتة التلقائية</h5>
                    <span class="status-indicator healthy"></span>
                    <span class="text-success">يعمل بشكل طبيعي</span>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="health-card text-center">
                    <i class="fas fa-bell fa-2x text-info mb-3"></i>
                    <h5>نظام الإشعارات</h5>
                    <span class="status-indicator healthy"></span>
                    <span class="text-success">يعمل بشكل طبيعي</span>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="health-card text-center">
                    <i class="fas fa-user-tie fa-2x text-success mb-3"></i>
                    <h5>بوابة المخلص</h5>
                    <span class="status-indicator healthy"></span>
                    <span class="text-success">يعمل بشكل طبيعي</span>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="health-card text-center">
                    <i class="fas fa-chart-line fa-2x text-warning mb-3"></i>
                    <h5>التحليلات المتقدمة</h5>
                    <span class="status-indicator healthy"></span>
                    <span class="text-success">يعمل بشكل طبيعي</span>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- تفاصيل المكونات -->
            <div class="col-lg-8">
                <div class="health-card">
                    <h5 class="mb-4">
                        <i class="fas fa-cogs text-primary me-2"></i>
                        تفاصيل المكونات
                    </h5>
                    
                    <div class="component-card">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">
                                <i class="fas fa-robot me-2"></i>
                                نظام الأتمتة التلقائية
                            </h6>
                            <span class="badge bg-success">نشط</span>
                        </div>
                        <div class="metric-item">
                            <span>آخر نشاط:</span>
                            <span class="text-success">منذ 2 دقيقة</span>
                        </div>
                        <div class="metric-item">
                            <span>الأوامر المعالجة اليوم:</span>
                            <span>25</span>
                        </div>
                        <div class="metric-item">
                            <span>معدل النجاح:</span>
                            <span class="text-success">95.5%</span>
                        </div>
                    </div>

                    <div class="component-card">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">
                                <i class="fas fa-bell me-2"></i>
                                نظام الإشعارات المتقدم
                            </h6>
                            <span class="badge bg-success">نشط</span>
                        </div>
                        <div class="metric-item">
                            <span>آخر إشعار:</span>
                            <span class="text-success">منذ 30 ثانية</span>
                        </div>
                        <div class="metric-item">
                            <span>الإشعارات المرسلة اليوم:</span>
                            <span>89</span>
                        </div>
                        <div class="metric-item">
                            <span>معدل التسليم:</span>
                            <span class="text-success">98.2%</span>
                        </div>
                    </div>

                    <div class="component-card">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">
                                <i class="fas fa-user-tie me-2"></i>
                                بوابة المخلص الإلكترونية
                            </h6>
                            <span class="badge bg-success">نشط</span>
                        </div>
                        <div class="metric-item">
                            <span>الجلسات النشطة:</span>
                            <span>12</span>
                        </div>
                        <div class="metric-item">
                            <span>آخر تسجيل دخول:</span>
                            <span class="text-success">منذ 5 دقائق</span>
                        </div>
                        <div class="metric-item">
                            <span>وقت الاستجابة:</span>
                            <span class="text-success">0.8 ثانية</span>
                        </div>
                    </div>

                    <div class="component-card">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                التحليلات المتقدمة
                            </h6>
                            <span class="badge bg-success">نشط</span>
                        </div>
                        <div class="metric-item">
                            <span>آخر تحديث للبيانات:</span>
                            <span class="text-success">منذ 1 دقيقة</span>
                        </div>
                        <div class="metric-item">
                            <span>التقارير المُنشأة اليوم:</span>
                            <span>15</span>
                        </div>
                        <div class="metric-item">
                            <span>حالة قاعدة البيانات:</span>
                            <span class="text-success">متصلة</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات الأداء -->
            <div class="col-lg-4">
                <div class="health-card">
                    <h6 class="mb-3">
                        <i class="fas fa-tachometer-alt text-info me-2"></i>
                        مؤشرات الأداء
                    </h6>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span>استخدام المعالج</span>
                            <span>25%</span>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-success" style="width: 25%"></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span>استخدام الذاكرة</span>
                            <span>45%</span>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-info" style="width: 45%"></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span>استخدام القرص</span>
                            <span>60%</span>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-warning" style="width: 60%"></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span>وقت التشغيل</span>
                            <span>99.8%</span>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-success" style="width: 99.8%"></div>
                        </div>
                    </div>
                </div>

                <div class="health-card">
                    <h6 class="mb-3">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        التنبيهات الحديثة
                    </h6>
                    
                    <div class="alert alert-success alert-sm">
                        <i class="fas fa-check-circle me-2"></i>
                        تم تحديث النظام بنجاح
                        <br>
                        <small class="text-muted">منذ ساعة</small>
                    </div>

                    <div class="alert alert-info alert-sm">
                        <i class="fas fa-info-circle me-2"></i>
                        تم إنشاء نسخة احتياطية
                        <br>
                        <small class="text-muted">منذ 3 ساعات</small>
                    </div>

                    <div class="alert alert-warning alert-sm">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        استخدام القرص مرتفع
                        <br>
                        <small class="text-muted">منذ 6 ساعات</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديث الصفحة كل 30 ثانية
setInterval(() => {
    refreshHealth();
}, 30000);

function refreshHealth() {
    // محاكاة تحديث البيانات
    console.log('Refreshing system health...');
    
    // يمكن إضافة AJAX call هنا لجلب البيانات المحدثة
    fetch('/phase2/api/system-health')
        .then(response => response.json())
        .then(data => {
            console.log('Health data updated:', data);
            // تحديث الواجهة بالبيانات الجديدة
        })
        .catch(error => {
            console.error('Error fetching health data:', error);
        });
}

// تحديث مؤشرات الأداء بشكل عشوائي للعرض
function updatePerformanceMetrics() {
    const metrics = ['cpu', 'memory', 'disk', 'uptime'];
    metrics.forEach(metric => {
        const progressBar = document.querySelector(`.progress-bar.bg-${metric === 'cpu' ? 'success' : metric === 'memory' ? 'info' : metric === 'disk' ? 'warning' : 'success'}`);
        if (progressBar) {
            const randomValue = Math.random() * 100;
            progressBar.style.width = randomValue + '%';
            progressBar.parentElement.previousElementSibling.querySelector('span:last-child').textContent = Math.round(randomValue) + '%';
        }
    });
}

// تحديث المؤشرات كل 5 ثوان
setInterval(updatePerformanceMetrics, 5000);
</script>
{% endblock %}
