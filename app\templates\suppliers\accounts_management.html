<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة حسابات الموردين - نظام إدارة متقدم</title>

    <!-- Modern CSS Framework -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css" rel="stylesheet">

    <style>
        /* Enterprise Design System */
        :root {
            --primary: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary: #64748b;
            --success: #059669;
            --warning: #d97706;
            --danger: #dc2626;
            --info: #0891b2;
            --light: #f8fafc;
            --dark: #0f172a;
            --surface: #ffffff;
            --border: #e2e8f0;
            --text-primary: #0f172a;
            --text-secondary: #475569;
            --text-muted: #94a3b8;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -1px rgb(0 0 0 / 0.06);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
            --radius: 0.75rem;
        }

        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        body {
            background: var(--light);
            color: var(--text-primary);
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }

        /* Header */
        .dashboard-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 2.5rem 0;
            position: relative;
            overflow: hidden;
        }

        .dashboard-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="8" height="8" patternUnits="userSpaceOnUse"><path d="M 8 0 L 0 0 0 8" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.4;
        }

        .page-title {
            font-size: 2.75rem;
            font-weight: 800;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .page-subtitle {
            font-size: 1.125rem;
            opacity: 0.9;
            margin: 0.75rem 0 0 0;
            position: relative;
            z-index: 1;
        }

        .header-actions {
            position: relative;
            z-index: 1;
        }

        /* Breadcrumb Navigation */
        .breadcrumb-section {
            background: var(--surface);
            border-bottom: 1px solid var(--border);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }

        .breadcrumb-modern {
            background: none;
            padding: 0;
            margin: 0;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            list-style: none;
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
            font-size: 0.875rem;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: '/';
            color: var(--text-muted);
            margin: 0 0.75rem;
            font-weight: 400;
        }

        .breadcrumb-link {
            color: var(--text-secondary);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0.75rem;
            border-radius: var(--radius);
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .breadcrumb-link:hover {
            color: var(--primary);
            background: rgba(37, 99, 235, 0.1);
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: var(--text-primary);
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0.75rem;
            background: rgba(37, 99, 235, 0.1);
            border-radius: var(--radius);
        }

        .breadcrumb-item i {
            font-size: 0.75rem;
            opacity: 0.8;
        }

        /* Cards */
        .card-enterprise {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            box-shadow: var(--shadow-sm);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .card-enterprise:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
            border-color: var(--primary);
        }

        /* Metrics */
        .metrics-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .metric-card {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 1.75rem;
            position: relative;
            transition: all 0.3s ease;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-4px);
        }

        .metric-card:hover::before {
            transform: scaleX(1);
        }

        .metric-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.25rem;
        }

        .metric-icon {
            width: 52px;
            height: 52px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.375rem;
            color: white;
            background: var(--primary);
        }

        .metric-icon.success { background: var(--success); }
        .metric-icon.warning { background: var(--warning); }
        .metric-icon.danger { background: var(--danger); }
        .metric-icon.info { background: var(--info); }

        .metric-trend {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            font-size: 0.8rem;
            font-weight: 600;
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
        }

        .trend-positive {
            background: rgba(5, 150, 105, 0.1);
            color: var(--success);
        }

        .trend-negative {
            background: rgba(220, 38, 38, 0.1);
            color: var(--danger);
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--text-primary);
            margin: 0.75rem 0 0.5rem 0;
            line-height: 1;
        }

        .metric-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .metric-description {
            font-size: 0.8rem;
            color: var(--text-muted);
        }

        /* Charts */
        .charts-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin: 2rem 0;
        }

        @media (max-width: 1024px) {
            .charts-section {
                grid-template-columns: 1fr;
            }
        }

        .chart-card {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .chart-card:hover {
            box-shadow: var(--shadow-md);
            border-color: var(--primary);
        }

        .chart-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
        }

        .chart-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .chart-container {
            position: relative;
            height: 320px;
            width: 100%;
        }

        /* Buttons */
        .btn-enterprise {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary-enterprise {
            background: var(--primary);
            color: white;
        }

        .btn-primary-enterprise:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
            color: white;
        }

        .btn-secondary-enterprise {
            background: var(--surface);
            color: var(--text-secondary);
            border: 1px solid var(--border);
        }

        .btn-secondary-enterprise:hover {
            background: var(--light);
            border-color: var(--secondary);
            color: var(--text-primary);
        }

        /* Table */
        .table-enterprise {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
            background: var(--surface);
            table-layout: fixed;
        }

        .table-enterprise th {
            background: var(--light);
            padding: 1rem;
            text-align: right;
            font-weight: 600;
            color: var(--text-secondary);
            border-bottom: 1px solid var(--border);
            vertical-align: middle;
            white-space: nowrap;
        }

        .table-enterprise td {
            padding: 1rem;
            border-bottom: 1px solid var(--border);
            color: var(--text-primary);
            text-align: right;
            vertical-align: middle;
        }

        .table-enterprise tbody tr:hover {
            background: var(--light);
        }

        /* تحديد عرض الأعمدة */
        .table-enterprise th:nth-child(1),
        .table-enterprise td:nth-child(1) {
            width: 50px;
            text-align: center;
        }

        .table-enterprise th:nth-child(2),
        .table-enterprise td:nth-child(2) {
            width: 200px;
        }

        .table-enterprise th:nth-child(3),
        .table-enterprise td:nth-child(3) {
            width: 120px;
        }

        .table-enterprise th:nth-child(4),
        .table-enterprise td:nth-child(4) {
            width: 100px;
            text-align: center;
        }

        .table-enterprise th:nth-child(5),
        .table-enterprise td:nth-child(5) {
            width: 120px;
        }

        .table-enterprise th:nth-child(6),
        .table-enterprise td:nth-child(6) {
            width: 120px;
        }

        .table-enterprise th:nth-child(7),
        .table-enterprise td:nth-child(7) {
            width: 120px;
        }

        .table-enterprise th:nth-child(8),
        .table-enterprise td:nth-child(8) {
            width: 150px;
            text-align: center;
        }

        .supplier-name {
            font-weight: 600;
            color: var(--text-primary);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .supplier-code {
            font-size: 0.75rem;
            color: var(--text-muted);
            font-family: 'Monaco', monospace;
            direction: ltr;
            text-align: left;
        }

        .supplier-info {
            min-width: 180px;
        }

        .table-responsive {
            overflow-x: auto;
            border-radius: var(--radius);
        }

        .table-enterprise .actions-column {
            white-space: nowrap;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-active { background: rgba(5, 150, 105, 0.1); color: var(--success); }
        .status-inactive { background: rgba(220, 38, 38, 0.1); color: var(--danger); }
        .status-pending { background: rgba(217, 119, 6, 0.1); color: var(--warning); }

        /* Responsive */
        @media (max-width: 768px) {
            .metrics-container {
                grid-template-columns: 1fr;
            }

            .page-title {
                font-size: 2rem;
            }

            .header-actions {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Professional Header -->
    <div class="dashboard-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-users-cog ms-3"></i>
                        إدارة حسابات الموردين
                    </h1>
                    <p class="page-subtitle">
                        نظام شامل لإدارة وتنظيم حسابات الموردين مع أدوات متقدمة للمتابعة والتحليل
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions d-flex gap-2 justify-content-lg-end">
                        <button class="btn-enterprise btn-secondary-enterprise" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                        <div class="dropdown">
                            <button class="btn-enterprise btn-secondary-enterprise dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-download"></i>
                                تصدير
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel ms-2"></i>تقرير Excel
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf ms-2"></i>تقرير PDF
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportToCSV()">
                                    <i class="fas fa-file-csv ms-2"></i>بيانات CSV
                                </a></li>
                            </ul>
                        </div>
                        <button class="btn-enterprise btn-primary-enterprise" onclick="showAddSupplierModal()">
                            <i class="fas fa-plus"></i>
                            إضافة مورد جديد
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb Navigation -->
    <div class="breadcrumb-section">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb-modern">
                            <li class="breadcrumb-item">
                                <a href="/" class="breadcrumb-link">
                                    <i class="fas fa-home"></i>
                                    الرئيسية
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="/suppliers" class="breadcrumb-link">
                                    <i class="fas fa-truck"></i>
                                    إدارة الموردين
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">
                                <i class="fas fa-users-cog"></i>
                                إدارة حسابات الموردين
                            </li>
                        </ol>
                    </nav>
                </div>
                <div class="col-lg-4">
                    <div class="d-flex gap-2 justify-content-lg-end">
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-link me-1"></i>
                                صفحات ذات صلة
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="/suppliers/balances_management">
                                        <i class="fas fa-chart-line me-2"></i>
                                        إدارة أرصدة الموردين
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="/suppliers/transactions_management">
                                        <i class="fas fa-exchange-alt me-2"></i>
                                        إدارة معاملات الموردين
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="/suppliers/reconciliation_management">
                                        <i class="fas fa-balance-scale me-2"></i>
                                        مطابقة حسابات الموردين
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="/reports/suppliers">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        تقارير الموردين
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" onclick="window.print()">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Key Performance Metrics -->
        <div class="metrics-container">
            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="metric-trend trend-positive">
                        <i class="fas fa-arrow-up"></i>
                        +5.2%
                    </div>
                </div>
                <div class="metric-value" id="totalSuppliers">247</div>
                <div class="metric-label">إجمالي الموردين</div>
                <div class="metric-description">مقارنة بالشهر الماضي</div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-icon success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="metric-trend trend-positive">
                        <i class="fas fa-arrow-up"></i>
                        +2.1%
                    </div>
                </div>
                <div class="metric-value" id="activeSuppliers">198</div>
                <div class="metric-label">الموردين النشطين</div>
                <div class="metric-description">حسابات فعالة</div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-icon warning">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="metric-trend trend-negative">
                        <i class="fas fa-arrow-down"></i>
                        -1.8%
                    </div>
                </div>
                <div class="metric-value" id="pendingSuppliers">32</div>
                <div class="metric-label">حسابات معلقة</div>
                <div class="metric-description">تحتاج مراجعة</div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-icon danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="metric-trend trend-positive">
                        <i class="fas fa-arrow-down"></i>
                        -0.5%
                    </div>
                </div>
                <div class="metric-value" id="inactiveSuppliers">17</div>
                <div class="metric-label">حسابات غير نشطة</div>
                <div class="metric-description">تحتاج متابعة</div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-section">
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-pie"></i>
                        توزيع الموردين حسب النوع
                    </h3>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="refreshChart('supplierTypes')">تحديث</a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportChart('supplierTypes')">تصدير</a></li>
                        </ul>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="supplierTypesChart"></canvas>
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-line"></i>
                        اتجاه نمو الموردين
                    </h3>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="refreshChart('supplierGrowth')">تحديث</a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportChart('supplierGrowth')">تصدير</a></li>
                        </ul>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="supplierGrowthChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Suppliers Table -->
        <div class="card-enterprise">
            <div class="card-header d-flex justify-content-between align-items-center p-3">
                <h3 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    قائمة الموردين
                </h3>
                <div class="d-flex gap-2">
                    <div class="input-group" style="width: 300px;">
                        <input type="text" class="form-control" placeholder="البحث في الموردين..." id="searchInput">
                        <button class="btn btn-outline-secondary" type="button" onclick="searchSuppliers()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-filter"></i>
                            فلترة
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="filterSuppliers('all')">جميع الموردين</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterSuppliers('active')">النشطين فقط</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterSuppliers('inactive')">غير النشطين</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterSuppliers('pending')">المعلقين</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table-enterprise">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>معلومات المورد</th>
                            <th>نوع المورد</th>
                            <th>الحالة</th>
                            <th>تاريخ التسجيل</th>
                            <th>آخر معاملة</th>
                            <th>الرصيد</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="suppliersTableBody">
                        <!-- سيتم تحميل البيانات ديناميكياً -->
                    </tbody>
                </table>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center p-3">
                <div class="text-muted">
                    عرض <span id="showingFrom">1</span> إلى <span id="showingTo">20</span> من <span id="totalRecords">247</span> مورد
                </div>
                <nav>
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="previousPage()">السابق</a>
                        </li>
                        <li class="page-item active">
                            <a class="page-link" href="#">1</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="nextPage()">التالي</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Modern JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <script>
        // متغيرات عامة
        let suppliersData = [];
        let currentPage = 1;
        let totalPages = 1;
        let filteredData = [];

        // تهيئة الصفحة
        $(document).ready(function() {
            loadRealData();
        });

        // تحميل البيانات الحقيقية من الخادم
        function loadRealData() {
            // إظهار مؤشر التحميل
            showLoadingIndicator();

            $.ajax({
                url: '/suppliers/api/accounts_data',
                method: 'GET',
                success: function(data) {
                    hideLoadingIndicator();

                    // تحديث المقاييس
                    updateMetricsWithRealData(data.statistics);

                    // حفظ بيانات الموردين
                    suppliersData = data.suppliers;
                    filteredData = [...suppliersData];

                    // تحميل الجدول
                    loadSuppliersTable();

                    // تهيئة الرسوم البيانية بالبيانات الحقيقية
                    initializeChartsWithRealData(data.types_distribution, data.growth_trend);
                },
                error: function(xhr, status, error) {
                    hideLoadingIndicator();
                    console.error('خطأ في تحميل البيانات:', error);
                    showErrorMessage('فشل في تحميل البيانات. يرجى المحاولة مرة أخرى.');

                    // استخدام البيانات الافتراضية في حالة الخطأ
                    loadDefaultData();
                }
            });
        }

        // إظهار مؤشر التحميل
        function showLoadingIndicator() {
            const loadingHtml = `
                <div id="loadingIndicator" class="text-center p-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <div class="mt-2">جاري تحميل البيانات...</div>
                </div>
            `;
            $('#suppliersTableBody').html(loadingHtml);
        }

        // إخفاء مؤشر التحميل
        function hideLoadingIndicator() {
            $('#loadingIndicator').remove();
        }

        // إظهار رسالة خطأ
        function showErrorMessage(message) {
            const errorHtml = `
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                </div>
            `;
            $('#suppliersTableBody').html(errorHtml);
        }

        // تهيئة الرسوم البيانية بالبيانات الحقيقية
        function initializeChartsWithRealData(typesData, growthData) {
            // إعداد بيانات توزيع الموردين حسب النوع
            const typeLabels = typesData.map(item => item.type || 'غير محدد');
            const typeCounts = typesData.map(item => item.count);
            const typeColors = ['#2563eb', '#059669', '#d97706', '#dc2626', '#8b5cf6', '#f59e0b'];

            // رسم بياني لتوزيع الموردين حسب النوع
            const supplierTypesCtx = document.getElementById('supplierTypesChart').getContext('2d');
            new Chart(supplierTypesCtx, {
                type: 'doughnut',
                data: {
                    labels: typeLabels,
                    datasets: [{
                        data: typeCounts,
                        backgroundColor: typeColors.slice(0, typeLabels.length),
                        borderWidth: 0,
                        hoverOffset: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                font: {
                                    family: 'Inter'
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                                    return `${context.label}: ${context.parsed} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });

            // إعداد بيانات اتجاه النمو
            const growthLabels = growthData.map(item => {
                const [year, month] = item.month.split('-');
                const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                                  'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                return monthNames[parseInt(month) - 1];
            });
            const growthCounts = growthData.map(item => item.count);

            // رسم بياني لاتجاه نمو الموردين
            const supplierGrowthCtx = document.getElementById('supplierGrowthChart').getContext('2d');
            new Chart(supplierGrowthCtx, {
                type: 'line',
                data: {
                    labels: growthLabels,
                    datasets: [{
                        label: 'موردين جدد',
                        data: growthCounts,
                        borderColor: '#2563eb',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#2563eb',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 5,
                        pointHoverRadius: 7
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    return `شهر ${context[0].label}`;
                                },
                                label: function(context) {
                                    return `موردين جدد: ${context.parsed.y}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            },
                            ticks: {
                                stepSize: 1
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // تحميل البيانات الافتراضية في حالة الخطأ
        function loadDefaultData() {
            const defaultStats = {
                total_suppliers: 0,
                active_suppliers: 0,
                inactive_suppliers: 0,
                pending_suppliers: 0
            };

            updateMetricsWithRealData(defaultStats);

            const defaultTypes = [
                { type: 'تجاري', count: 0 },
                { type: 'خدمات', count: 0 }
            ];

            const defaultGrowth = [
                { month: '2024-01', count: 0 },
                { month: '2024-02', count: 0 }
            ];

            initializeChartsWithRealData(defaultTypes, defaultGrowth);

            suppliersData = [];
            filteredData = [];
            loadSuppliersTable();
        }

        // تحميل جدول الموردين بالبيانات الحقيقية
        function loadSuppliersTable() {
            const tbody = document.getElementById('suppliersTableBody');
            tbody.innerHTML = '';

            // حساب البيانات للصفحة الحالية
            const itemsPerPage = 20;
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageData = filteredData.slice(startIndex, endIndex);

            if (pageData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center p-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <div>لا توجد بيانات للعرض</div>
                        </td>
                    </tr>
                `;
                return;
            }

            pageData.forEach(supplier => {
                const statusClass = getStatusClass(supplier.status);
                const row = `
                    <tr>
                        <td>
                            <input type="checkbox" class="supplier-checkbox" value="${supplier.id}">
                        </td>
                        <td>
                            <div class="supplier-info">
                                <div class="supplier-name" title="${supplier.name}">${supplier.name}</div>
                                <div class="supplier-code">${supplier.code}</div>
                            </div>
                        </td>
                        <td>${supplier.type}</td>
                        <td>
                            <span class="status-badge ${statusClass}">
                                ${supplier.status}
                            </span>
                        </td>
                        <td>${formatDate(supplier.registration_date)}</td>
                        <td>${formatDate(supplier.last_transaction)}</td>
                        <td>${formatNumber(supplier.balance)} ريال</td>
                        <td class="actions-column">
                            <div class="d-flex gap-1 justify-content-center">
                                <button class="btn btn-sm btn-outline-primary" onclick="viewSupplier(${supplier.id})" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="editSupplier(${supplier.id})" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteSupplier(${supplier.id})" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });

            // تحديث معلومات الترقيم
            updatePaginationInfo();
        }

        // تحديد فئة CSS للحالة
        function getStatusClass(status) {
            switch(status) {
                case 'نشط': return 'status-active';
                case 'غير نشط': return 'status-inactive';
                case 'معلق': return 'status-pending';
                default: return 'status-inactive';
            }
        }

        // تنسيق التاريخ
        function formatDate(dateString) {
            if (!dateString || dateString === 'لا يوجد') return 'لا يوجد';

            try {
                const date = new Date(dateString);
                // عرض التاريخ بالتنسيق الإنجليزي
                return date.toLocaleDateString('en-GB', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                });
            } catch (e) {
                return dateString;
            }
        }

        // تنسيق الأرقام
        function formatNumber(number) {
            if (!number || number === 0) return '0';
            return parseFloat(number).toLocaleString('en-US');
        }

        // تحديث معلومات الترقيم
        function updatePaginationInfo() {
            const itemsPerPage = 20;
            const totalItems = filteredData.length;
            totalPages = Math.ceil(totalItems / itemsPerPage);

            const startIndex = (currentPage - 1) * itemsPerPage + 1;
            const endIndex = Math.min(currentPage * itemsPerPage, totalItems);

            // عرض الأرقام بالإنجليزية
            document.getElementById('showingFrom').textContent = startIndex.toLocaleString('en-US');
            document.getElementById('showingTo').textContent = endIndex.toLocaleString('en-US');
            document.getElementById('totalRecords').textContent = totalItems.toLocaleString('en-US');
        }

        // تحديث المقاييس بالبيانات الحقيقية
        function updateMetricsWithRealData(statistics) {
            // تحديث القيم مع تأثير العد التصاعدي
            animateCounter('totalSuppliers', statistics.total_suppliers);
            animateCounter('activeSuppliers', statistics.active_suppliers);
            animateCounter('pendingSuppliers', statistics.pending_suppliers);
            animateCounter('inactiveSuppliers', statistics.inactive_suppliers);

            // تحديث إجمالي السجلات في الجدول
            document.getElementById('totalRecords').textContent = statistics.total_suppliers;
        }

        // تأثير العد التصاعدي للأرقام
        function animateCounter(elementId, targetValue) {
            const element = document.getElementById(elementId);
            const startValue = 0;
            const duration = 1500; // مدة الأنيميشن بالميلي ثانية
            const startTime = performance.now();

            function updateCounter(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // استخدام easing function للحصول على تأثير سلس
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const currentValue = Math.floor(startValue + (targetValue - startValue) * easeOutQuart);

                // عرض الأرقام بالإنجليزية
                element.textContent = currentValue.toLocaleString('en-US');

                if (progress < 1) {
                    requestAnimationFrame(updateCounter);
                }
            }

            requestAnimationFrame(updateCounter);
        }

        // وظائف الإجراءات
        function refreshDashboard() {
            location.reload();
        }

        function showAddSupplierModal() {
            alert('سيتم فتح نافذة إضافة مورد جديد');
        }

        function viewSupplier(id) {
            window.location.href = `/suppliers/account_details/${id}`;
        }

        function editSupplier(id) {
            window.location.href = `/suppliers/edit/${id}`;
        }

        function deleteSupplier(id) {
            if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
                alert(`سيتم حذف المورد رقم ${id}`);
            }
        }

        function searchSuppliers() {
            const searchTerm = document.getElementById('searchInput').value;
            alert(`البحث عن: ${searchTerm}`);
        }

        function filterSuppliers(filter) {
            alert(`تطبيق فلتر: ${filter}`);
        }

        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.supplier-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        function exportToExcel() {
            alert('سيتم تصدير البيانات إلى Excel');
        }

        function exportToPDF() {
            alert('سيتم تصدير التقرير إلى PDF');
        }

        function exportToCSV() {
            alert('سيتم تصدير البيانات إلى CSV');
        }

        function previousPage() {
            alert('الصفحة السابقة');
        }

        function nextPage() {
            alert('الصفحة التالية');
        }

        function refreshChart(chartType) {
            alert(`تحديث الرسم البياني: ${chartType}`);
        }

        function exportChart(chartType) {
            alert(`تصدير الرسم البياني: ${chartType}`);
        }

        // تهيئة الرسوم البيانية بالبيانات الحقيقية
        function initializeChartsWithRealData(typesData, growthData) {
            // إعداد بيانات توزيع الموردين حسب النوع
            const typeLabels = typesData.map(item => item.type || 'غير محدد');
            const typeCounts = typesData.map(item => item.count);
            const typeColors = ['#2563eb', '#059669', '#d97706', '#dc2626', '#8b5cf6', '#f59e0b'];

            // رسم بياني لتوزيع الموردين حسب النوع
            const supplierTypesCtx = document.getElementById('supplierTypesChart').getContext('2d');
            new Chart(supplierTypesCtx, {
                type: 'doughnut',
                data: {
                    labels: typeLabels,
                    datasets: [{
                        data: typeCounts,
                        backgroundColor: typeColors.slice(0, typeLabels.length),
                        borderWidth: 0,
                        hoverOffset: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                font: {
                                    family: 'Inter'
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                                    return `${context.label}: ${context.parsed} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });

            // إعداد بيانات اتجاه النمو
            const growthLabels = growthData.map(item => {
                const [year, month] = item.month.split('-');
                const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                                  'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                return monthNames[parseInt(month) - 1];
            });
            const growthCounts = growthData.map(item => item.count);

            // رسم بياني لاتجاه نمو الموردين
            const supplierGrowthCtx = document.getElementById('supplierGrowthChart').getContext('2d');
            new Chart(supplierGrowthCtx, {
                type: 'line',
                data: {
                    labels: growthLabels,
                    datasets: [{
                        label: 'موردين جدد',
                        data: growthCounts,
                        borderColor: '#2563eb',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#2563eb',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 5,
                        pointHoverRadius: 7
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    return `شهر ${context[0].label}`;
                                },
                                label: function(context) {
                                    return `موردين جدد: ${context.parsed.y}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            },
                            ticks: {
                                stepSize: 1
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // تحميل البيانات الافتراضية في حالة الخطأ
        function loadDefaultData() {
            const defaultStats = {
                total_suppliers: 0,
                active_suppliers: 0,
                inactive_suppliers: 0,
                pending_suppliers: 0
            };

            updateMetricsWithRealData(defaultStats);

            const defaultTypes = [
                { type: 'تجاري', count: 0 },
                { type: 'خدمات', count: 0 }
            ];

            const defaultGrowth = [
                { month: '2024-01', count: 0 },
                { month: '2024-02', count: 0 }
            ];

            initializeChartsWithRealData(defaultTypes, defaultGrowth);

            suppliersData = [];
            filteredData = [];
            loadSuppliersTable();
        }
    </script>
</body>
</html>