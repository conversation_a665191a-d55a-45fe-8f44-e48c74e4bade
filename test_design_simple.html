<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التصميم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
            min-height: 100vh;
        }
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin: 2rem 0;
            text-align: center;
        }
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-header">
            <h1><i class="fas fa-shopping-cart me-3"></i>اختبار تصميم أوامر الشراء</h1>
            <p>إذا كنت ترى هذا التصميم، فإن CSS يعمل بنجاح!</p>
        </div>
        
        <div class="test-card">
            <h3><i class="fas fa-check-circle text-success me-2"></i>التصميم يعمل!</h3>
            <p>إذا كنت ترى الألوان والتدرجات، فإن ملف CSS يتم تحميله بنجاح.</p>
        </div>
        
        <div class="test-card">
            <h3><i class="fas fa-info-circle text-info me-2"></i>الخطوات التالية:</h3>
            <ol>
                <li>تأكد من أن الخادم يعمل</li>
                <li>امسح cache المتصفح (Ctrl+Shift+Delete)</li>
                <li>أعد تحميل صفحة أوامر الشراء (Ctrl+F5)</li>
                <li>تحقق من أن ملف CSS يتم تحميله في Developer Tools</li>
            </ol>
        </div>
    </div>
</body>
</html>