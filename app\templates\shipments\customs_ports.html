{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
/* نافذة منبثقة مخصصة بالكامل */
.custom-modal {
    display: none;
    position: fixed !important;
    z-index: 999999 !important;
    left: 0 !important;
    top: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background-color: rgba(0, 0, 0, 0.6) !important;
    overflow: auto;
    backdrop-filter: blur(2px);
}

.custom-modal.show {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

.custom-modal-content {
    background-color: #ffffff !important;
    margin: 0 !important;
    padding: 0;
    border: none;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4);
    position: relative;
    z-index: 1000000 !important;
    animation: modalSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.custom-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px 8px 0 0;
    position: relative;
}

.custom-modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.custom-modal-close {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.custom-modal-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.custom-modal-body {
    padding: 30px;
    max-height: 70vh;
    overflow-y: auto;
}

.custom-modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa;
    border-radius: 0 0 8px 8px;
    text-align: right;
}

.custom-modal-footer .btn {
    margin-left: 10px;
}

/* التأكد من أن جميع العناصر قابلة للتفاعل */
.custom-modal input,
.custom-modal select,
.custom-modal textarea,
.custom-modal button {
    pointer-events: auto !important;
    z-index: auto !important;
}

/* تحسين مظهر النموذج */
.custom-modal .form-control {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.custom-modal .form-control:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.custom-modal .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.custom-modal .form-check-input {
    margin-top: 0.3rem;
}

/* تحسين الأزرار */
.btn-custom-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
}

.btn-custom-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-custom-secondary {
    background-color: #6c757d;
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-custom-secondary:hover {
    background-color: #5a6268;
}

/* إخفاء أي عناصر قد تتداخل مع النافذة */
body.modal-open {
    overflow: hidden !important;
}

/* التأكد من أن النافذة فوق كل شيء */
.custom-modal * {
    box-sizing: border-box;
}

/* إصلاح أي مشاكل في التموضع */
.custom-modal-content * {
    position: relative;
}

/* منع أي تداخل مع الشريط الجانبي */
.custom-modal {
    isolation: isolate;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-anchor text-primary me-2"></i>
                إدارة المنافذ الجمركية
            </h1>
            <p class="text-muted mb-0">إدارة وتتبع المنافذ الجمركية والموانئ والمطارات</p>
        </div>
        <div>
            <button type="button" class="btn btn-primary" onclick="openAddPortModal()">
                <i class="fas fa-plus me-2"></i>إضافة منفذ جديد
            </button>
        </div>
    </div>

    <!-- بطاقات الإحصائيات -->
    <div class="row mb-4">
        <div class="col-xl-2 col-md-4 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي المنافذ
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_ports }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-anchor fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                المنافذ النشطة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.active_ports }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                موانئ بحرية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.sea_ports }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-ship fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                مطارات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.air_ports }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-plane fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 mb-4">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                منافذ برية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.land_ports }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-truck fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 mb-4">
            <div class="card border-left-dark shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-dark text-uppercase mb-1">
                                عمل 24 ساعة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.ports_24h }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات البحث والتصفية -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-search me-2"></i>البحث والتصفية
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="searchPort">البحث في المنافذ</label>
                        <input type="text" class="form-control" id="searchPort" placeholder="اسم المنفذ أو الكود...">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="filterType">نوع المنفذ</label>
                        <select class="form-control" id="filterType">
                            <option value="">جميع الأنواع</option>
                            <option value="SEA">ميناء بحري</option>
                            <option value="AIR">مطار</option>
                            <option value="LAND">منفذ بري</option>
                            <option value="DRY_PORT">ميناء جاف</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="filterCountry">الدولة</label>
                        <select class="form-control" id="filterCountry">
                            <option value="">جميع الدول</option>
                            <option value="اليمن">اليمن</option>
                            <option value="السعودية">السعودية</option>
                            <option value="الإمارات">الإمارات</option>
                            <option value="الكويت">الكويت</option>
                            <option value="قطر">قطر</option>
                            <option value="البحرين">البحرين</option>
                            <option value="عمان">عمان</option>
                            <option value="الأردن">الأردن</option>
                            <option value="مصر">مصر</option>
                            <option value="العراق">العراق</option>
                            <option value="لبنان">لبنان</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="filterStatus">الحالة</label>
                        <select class="form-control" id="filterStatus">
                            <option value="">جميع الحالات</option>
                            <option value="1">نشط</option>
                            <option value="0">غير نشط</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div class="d-flex">
                            <button type="button" class="btn btn-primary me-2" onclick="applyFilters()">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                                <i class="fas fa-times"></i> مسح
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول المنافذ الجمركية -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list me-2"></i>قائمة المنافذ الجمركية
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="portsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>كود المنفذ</th>
                            <th>اسم المنفذ</th>
                            <th>النوع</th>
                            <th>الدولة/المدينة</th>
                            <th>السلطة الجمركية</th>
                            <th>الخدمات</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for port in ports %}
                        <tr>
                            <td>
                                <span class="badge bg-primary">{{ port.port_code }}</span>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ port.port_name_ar }}</strong>
                                    {% if port.port_name_en %}
                                    <br><small class="text-muted">{{ port.port_name_en }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td data-type="{{ port.port_type }}">
                                {% if port.port_type == 'SEA' %}
                                <span class="badge bg-info"><i class="fas fa-ship me-1"></i>{{ port.port_type_display }}</span>
                                {% elif port.port_type == 'AIR' %}
                                <span class="badge bg-warning"><i class="fas fa-plane me-1"></i>{{ port.port_type_display }}</span>
                                {% elif port.port_type == 'LAND' %}
                                <span class="badge bg-secondary"><i class="fas fa-truck me-1"></i>{{ port.port_type_display }}</span>
                                {% else %}
                                <span class="badge bg-dark"><i class="fas fa-warehouse me-1"></i>{{ port.port_type_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div>
                                    <strong>{{ port.country }}</strong>
                                    <br><small>{{ port.city }}{% if port.region %} - {{ port.region }}{% endif %}</small>
                                </div>
                            </td>
                            <td>
                                <small>{{ port.customs_authority or 'غير محدد' }}</small>
                                {% if port.contact_phone %}
                                <br><small class="text-muted"><i class="fas fa-phone me-1"></i>{{ port.contact_phone }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <div class="d-flex flex-wrap gap-1">
                                    {% if port.has_customs_clearance %}
                                    <span class="badge bg-success" title="تخليص جمركي"><i class="fas fa-check"></i></span>
                                    {% endif %}
                                    {% if port.has_quarantine %}
                                    <span class="badge bg-warning" title="حجر صحي"><i class="fas fa-shield-alt"></i></span>
                                    {% endif %}
                                    {% if port.has_warehouse %}
                                    <span class="badge bg-info" title="مستودعات"><i class="fas fa-warehouse"></i></span>
                                    {% endif %}
                                    {% if port.is_24_hours %}
                                    <span class="badge bg-dark" title="24 ساعة"><i class="fas fa-clock"></i></span>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if port.is_active %}
                                <span class="badge bg-success">نشط</span>
                                {% else %}
                                <span class="badge bg-danger">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-info" 
                                            onclick="viewPort({{ port.id }})" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                            onclick="editPort({{ port.id }})" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            onclick="deletePort({{ port.id }}, '{{ port.port_name_ar }}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<!-- نهاية container الرئيسي -->

<script>
// إنشاء النافذة ديناميكياً
function createPortModal() {
    const modalHTML = `
        <div id="dynamicPortModal" style="
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background: rgba(0, 0, 0, 0.7) !important;
            z-index: 999999 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            backdrop-filter: blur(3px);
        ">
            <div style="
                background: white !important;
                border-radius: 12px !important;
                width: 90% !important;
                max-width: 800px !important;
                max-height: 90vh !important;
                overflow: hidden !important;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5) !important;
                animation: slideIn 0.3s ease-out !important;
            ">
                <div style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                    color: white !important;
                    padding: 20px !important;
                    display: flex !important;
                    justify-content: space-between !important;
                    align-items: center !important;
                ">
                    <h5 style="margin: 0 !important; font-size: 1.25rem !important;">
                        <i class="fas fa-plus me-2"></i>إضافة منفذ جمركي جديد
                    </h5>
                    <button onclick="closePortModal()" style="
                        background: none !important;
                        border: none !important;
                        color: white !important;
                        font-size: 24px !important;
                        cursor: pointer !important;
                        width: 30px !important;
                        height: 30px !important;
                        border-radius: 50% !important;
                        display: flex !important;
                        align-items: center !important;
                        justify-content: center !important;
                    ">&times;</button>
                </div>
                <div style="padding: 30px !important; max-height: 60vh !important; overflow-y: auto !important;">
                    <form id="dynamicPortForm" onsubmit="submitDynamicPortForm(event)">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">كود المنفذ *</label>
                                    <input type="text" class="form-control" id="dynPortCode" name="port_code" required placeholder="مثال: JEDDAH_SEA">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع المنفذ *</label>
                                    <select class="form-control" id="dynPortType" name="port_type" required>
                                        <option value="">اختر نوع المنفذ</option>
                                        <option value="SEA">ميناء بحري</option>
                                        <option value="AIR">مطار</option>
                                        <option value="LAND">منفذ بري</option>
                                        <option value="DRY_PORT">ميناء جاف</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المنفذ (عربي) *</label>
                                    <input type="text" class="form-control" id="dynPortNameAr" name="port_name_ar" required placeholder="ميناء جدة الإسلامي">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المنفذ (إنجليزي)</label>
                                    <input type="text" class="form-control" id="dynPortNameEn" name="port_name_en" placeholder="Jeddah Islamic Port">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الدولة *</label>
                                    <select class="form-control" id="dynCountry" name="country" required>
                                        <option value="">اختر الدولة</option>
                                        <option value="اليمن">الجمهورية اليمنية</option>
                                        <option value="السعودية">المملكة العربية السعودية</option>
                                        <option value="الإمارات">الإمارات العربية المتحدة</option>
                                        <option value="الكويت">دولة الكويت</option>
                                        <option value="قطر">دولة قطر</option>
                                        <option value="البحرين">مملكة البحرين</option>
                                        <option value="عمان">سلطنة عمان</option>
                                        <option value="الأردن">المملكة الأردنية الهاشمية</option>
                                        <option value="مصر">جمهورية مصر العربية</option>
                                        <option value="العراق">جمهورية العراق</option>
                                        <option value="لبنان">الجمهورية اللبنانية</option>
                                        <option value="سوريا">الجمهورية العربية السورية</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">المدينة *</label>
                                    <input type="text" class="form-control" id="dynCity" name="city" required placeholder="جدة">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">المنطقة</label>
                                    <input type="text" class="form-control" id="dynRegion" name="region" placeholder="مكة المكرمة">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">السلطة الجمركية</label>
                                    <input type="text" class="form-control" id="dynCustomsAuthority" name="customs_authority" placeholder="الهيئة العامة للجمارك">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">هاتف الاتصال</label>
                                    <input type="text" class="form-control" id="dynContactPhone" name="contact_phone" placeholder="+966-12-6361000">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="dynContactEmail" name="contact_email" placeholder="<EMAIL>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ساعات العمل</label>
                                    <input type="text" class="form-control" id="dynWorkingHours" name="working_hours" placeholder="08:00 - 16:00">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <label class="form-label">الخدمات المتاحة</label>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="dynHasCustomsClearance" name="has_customs_clearance" checked>
                                            <label class="form-check-label">تخليص جمركي</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="dynHasQuarantine" name="has_quarantine">
                                            <label class="form-check-label">حجر صحي</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="dynHasWarehouse" name="has_warehouse">
                                            <label class="form-check-label">مستودعات</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="dynIs24Hours" name="is_24_hours">
                                            <label class="form-check-label">عمل 24 ساعة</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- ملاحظات -->
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="dynNotes" name="notes" rows="3" placeholder="أي ملاحظات إضافية عن المنفذ..."></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div style="
                    padding: 20px 30px !important;
                    border-top: 1px solid #dee2e6 !important;
                    background: #f8f9fa !important;
                    text-align: right !important;
                ">
                    <button type="button" onclick="closePortModal()" style="
                        background: #6c757d !important;
                        color: white !important;
                        border: none !important;
                        padding: 10px 20px !important;
                        border-radius: 5px !important;
                        margin-left: 10px !important;
                        cursor: pointer !important;
                    ">إلغاء</button>
                    <button type="submit" form="dynamicPortForm" style="
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                        color: white !important;
                        border: none !important;
                        padding: 10px 20px !important;
                        border-radius: 5px !important;
                        cursor: pointer !important;
                    ">
                        <i class="fas fa-save me-2"></i>حفظ المنفذ
                    </button>
                </div>
            </div>
        </div>
    `;

    return modalHTML;
}

// فتح نافذة إضافة منفذ جديد
function openAddPortModal() {
    console.log('🚀 إنشاء نافذة جديدة ديناميكياً');

    // إزالة أي نافذة موجودة
    const existingModal = document.getElementById('dynamicPortModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إنشاء النافذة وإضافتها مباشرة لـ body
    const modalHTML = createPortModal();
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // منع التمرير
    document.body.style.overflow = 'hidden';

    // التركيز على أول حقل
    setTimeout(() => {
        const firstInput = document.getElementById('dynPortCode');
        if (firstInput) {
            firstInput.focus();
            console.log('✅ تم التركيز على حقل الكود');
        }
    }, 100);

    console.log('✅ تم إنشاء النافذة بنجاح');
}

// إغلاق النافذة الديناميكية
function closePortModal() {
    console.log('🔒 إغلاق النافذة الديناميكية');

    const modal = document.getElementById('dynamicPortModal');
    if (modal) {
        modal.remove();
    }

    // إعادة تفعيل التمرير
    document.body.style.overflow = 'auto';

    console.log('✅ تم إغلاق النافذة بنجاح');
}

// إرسال النموذج الديناميكي
function submitDynamicPortForm(event) {
    event.preventDefault();
    console.log('📤 إرسال النموذج الديناميكي');

    const form = document.getElementById('dynamicPortForm');
    const formData = new FormData(form);

    // تحويل البيانات إلى JSON
    const data = {};
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }

    // إضافة الحقول المنطقية
    data.has_customs_clearance = document.getElementById('dynHasCustomsClearance').checked ? 1 : 0;
    data.has_quarantine = document.getElementById('dynHasQuarantine').checked ? 1 : 0;
    data.has_warehouse = document.getElementById('dynHasWarehouse').checked ? 1 : 0;
    data.is_24_hours = document.getElementById('dynIs24Hours').checked ? 1 : 0;
    data.is_active = 1;

    // إضافة الحقول النصية الإضافية
    data.contact_email = document.getElementById('dynContactEmail').value || '';
    data.working_hours = document.getElementById('dynWorkingHours').value || '';
    data.notes = document.getElementById('dynNotes').value || '';

    console.log('📋 بيانات المنفذ:', data);

    // إرسال البيانات
    fetch('/shipments/api/customs-ports', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            closePortModal();
            alert('✅ تم إضافة المنفذ بنجاح!');
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            alert('❌ خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('❌ خطأ في إضافة المنفذ:', error);
        alert('❌ حدث خطأ في إضافة المنفذ');
    });
}

// إنشاء نافذة التعديل ديناميكياً
function createEditPortModal(portData) {
    const modalHTML = `
        <div id="editPortModal" style="
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background: rgba(0, 0, 0, 0.7) !important;
            z-index: 999999 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            backdrop-filter: blur(3px);
        ">
            <div style="
                background: white !important;
                border-radius: 12px !important;
                width: 90% !important;
                max-width: 800px !important;
                max-height: 90vh !important;
                overflow: hidden !important;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5) !important;
                animation: slideIn 0.3s ease-out !important;
            ">
                <div style="
                    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
                    color: white !important;
                    padding: 20px !important;
                    display: flex !important;
                    justify-content: space-between !important;
                    align-items: center !important;
                ">
                    <h5 style="margin: 0 !important; font-size: 1.25rem !important;">
                        <i class="fas fa-edit me-2"></i>تعديل منفذ جمركي: ${portData.port_name_ar}
                    </h5>
                    <button onclick="closeEditPortModal()" style="
                        background: none !important;
                        border: none !important;
                        color: white !important;
                        font-size: 24px !important;
                        cursor: pointer !important;
                        width: 30px !important;
                        height: 30px !important;
                        border-radius: 50% !important;
                        display: flex !important;
                        align-items: center !important;
                        justify-content: center !important;
                    ">&times;</button>
                </div>
                <div style="padding: 30px !important; max-height: 60vh !important; overflow-y: auto !important;">
                    <form id="editPortForm" onsubmit="submitEditPortForm(event, ${portData.id})">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">كود المنفذ *</label>
                                    <input type="text" class="form-control" id="editPortCode" name="port_code" required
                                           value="${portData.port_code}" placeholder="مثال: JEDDAH_SEA">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع المنفذ *</label>
                                    <select class="form-control" id="editPortType" name="port_type" required>
                                        <option value="">اختر نوع المنفذ</option>
                                        <option value="SEA" ${portData.port_type === 'SEA' ? 'selected' : ''}>ميناء بحري</option>
                                        <option value="AIR" ${portData.port_type === 'AIR' ? 'selected' : ''}>مطار</option>
                                        <option value="LAND" ${portData.port_type === 'LAND' ? 'selected' : ''}>منفذ بري</option>
                                        <option value="DRY_PORT" ${portData.port_type === 'DRY_PORT' ? 'selected' : ''}>ميناء جاف</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المنفذ (عربي) *</label>
                                    <input type="text" class="form-control" id="editPortNameAr" name="port_name_ar" required
                                           value="${portData.port_name_ar}" placeholder="ميناء جدة الإسلامي">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المنفذ (إنجليزي)</label>
                                    <input type="text" class="form-control" id="editPortNameEn" name="port_name_en"
                                           value="${portData.port_name_en || ''}" placeholder="Jeddah Islamic Port">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الدولة *</label>
                                    <select class="form-control" id="editCountry" name="country" required>
                                        <option value="">اختر الدولة</option>
                                        <option value="اليمن" ${portData.country === 'اليمن' ? 'selected' : ''}>الجمهورية اليمنية</option>
                                        <option value="السعودية" ${portData.country === 'السعودية' ? 'selected' : ''}>المملكة العربية السعودية</option>
                                        <option value="الإمارات" ${portData.country === 'الإمارات' ? 'selected' : ''}>الإمارات العربية المتحدة</option>
                                        <option value="الكويت" ${portData.country === 'الكويت' ? 'selected' : ''}>دولة الكويت</option>
                                        <option value="قطر" ${portData.country === 'قطر' ? 'selected' : ''}>دولة قطر</option>
                                        <option value="البحرين" ${portData.country === 'البحرين' ? 'selected' : ''}>مملكة البحرين</option>
                                        <option value="عمان" ${portData.country === 'عمان' ? 'selected' : ''}>سلطنة عمان</option>
                                        <option value="الأردن" ${portData.country === 'الأردن' ? 'selected' : ''}>المملكة الأردنية الهاشمية</option>
                                        <option value="مصر" ${portData.country === 'مصر' ? 'selected' : ''}>جمهورية مصر العربية</option>
                                        <option value="العراق" ${portData.country === 'العراق' ? 'selected' : ''}>جمهورية العراق</option>
                                        <option value="لبنان" ${portData.country === 'لبنان' ? 'selected' : ''}>الجمهورية اللبنانية</option>
                                        <option value="سوريا" ${portData.country === 'سوريا' ? 'selected' : ''}>الجمهورية العربية السورية</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">المدينة *</label>
                                    <input type="text" class="form-control" id="editCity" name="city" required
                                           value="${portData.city}" placeholder="جدة">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">المنطقة</label>
                                    <input type="text" class="form-control" id="editRegion" name="region"
                                           value="${portData.region || ''}" placeholder="مكة المكرمة">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">السلطة الجمركية</label>
                                    <input type="text" class="form-control" id="editCustomsAuthority" name="customs_authority"
                                           value="${portData.customs_authority || ''}" placeholder="الهيئة العامة للجمارك">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">هاتف الاتصال</label>
                                    <input type="text" class="form-control" id="editContactPhone" name="contact_phone"
                                           value="${portData.contact_phone || ''}" placeholder="+966-12-6361000">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="editContactEmail" name="contact_email"
                                           value="${portData.contact_email || ''}" placeholder="<EMAIL>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ساعات العمل</label>
                                    <input type="text" class="form-control" id="editWorkingHours" name="working_hours"
                                           value="${portData.working_hours || ''}" placeholder="08:00 - 16:00">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <label class="form-label">الخدمات المتاحة</label>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="editHasCustomsClearance"
                                                   name="has_customs_clearance" ${portData.has_customs_clearance ? 'checked' : ''}>
                                            <label class="form-check-label">تخليص جمركي</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="editHasQuarantine"
                                                   name="has_quarantine" ${portData.has_quarantine ? 'checked' : ''}>
                                            <label class="form-check-label">حجر صحي</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="editHasWarehouse"
                                                   name="has_warehouse" ${portData.has_warehouse ? 'checked' : ''}>
                                            <label class="form-check-label">مستودعات</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="editIs24Hours"
                                                   name="is_24_hours" ${portData.is_24_hours ? 'checked' : ''}>
                                            <label class="form-check-label">عمل 24 ساعة</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="editNotes" name="notes" rows="3"
                                              placeholder="أي ملاحظات إضافية عن المنفذ...">${portData.notes || ''}</textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div style="
                    padding: 20px 30px !important;
                    border-top: 1px solid #dee2e6 !important;
                    background: #f8f9fa !important;
                    text-align: right !important;
                ">
                    <button type="button" onclick="closeEditPortModal()" style="
                        background: #6c757d !important;
                        color: white !important;
                        border: none !important;
                        padding: 10px 20px !important;
                        border-radius: 5px !important;
                        margin-left: 10px !important;
                        cursor: pointer !important;
                    ">إلغاء</button>
                    <button type="submit" form="editPortForm" style="
                        background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
                        color: white !important;
                        border: none !important;
                        padding: 10px 20px !important;
                        border-radius: 5px !important;
                        cursor: pointer !important;
                    ">
                        <i class="fas fa-save me-2"></i>حفظ التعديلات
                    </button>
                </div>
            </div>
        </div>
    `;

    return modalHTML;
}

// فتح نافذة تعديل المنفذ
function openEditPortModal(portData) {
    console.log('🚀 إنشاء نافذة تعديل ديناميكياً');

    // إزالة أي نافذة موجودة
    const existingModal = document.getElementById('editPortModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إنشاء النافذة وإضافتها مباشرة لـ body
    const modalHTML = createEditPortModal(portData);
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // منع التمرير
    document.body.style.overflow = 'hidden';

    // التركيز على أول حقل
    setTimeout(() => {
        const firstInput = document.getElementById('editPortCode');
        if (firstInput) {
            firstInput.focus();
            console.log('✅ تم التركيز على حقل الكود');
        }
    }, 100);

    console.log('✅ تم إنشاء نافذة التعديل بنجاح');
}

// إغلاق نافذة التعديل
function closeEditPortModal() {
    console.log('🔒 إغلاق نافذة التعديل');

    const modal = document.getElementById('editPortModal');
    if (modal) {
        modal.remove();
    }

    // إعادة تفعيل التمرير
    document.body.style.overflow = 'auto';

    console.log('✅ تم إغلاق نافذة التعديل بنجاح');
}

// إرسال نموذج التعديل
function submitEditPortForm(event, portId) {
    event.preventDefault();
    console.log('📤 إرسال نموذج التعديل للمنفذ:', portId);

    const form = document.getElementById('editPortForm');
    const formData = new FormData(form);

    // تحويل البيانات إلى JSON
    const data = {};
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }

    // إضافة الحقول المنطقية
    data.has_customs_clearance = document.getElementById('editHasCustomsClearance').checked ? 1 : 0;
    data.has_quarantine = document.getElementById('editHasQuarantine').checked ? 1 : 0;
    data.has_warehouse = document.getElementById('editHasWarehouse').checked ? 1 : 0;
    data.is_24_hours = document.getElementById('editIs24Hours').checked ? 1 : 0;
    data.is_active = 1;

    // إضافة الحقول النصية الإضافية
    data.contact_email = document.getElementById('editContactEmail').value || '';
    data.working_hours = document.getElementById('editWorkingHours').value || '';
    data.notes = document.getElementById('editNotes').value || '';

    console.log('📋 بيانات التعديل:', data);

    // إرسال البيانات
    fetch(`/shipments/api/customs-ports/${portId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            closeEditPortModal();
            alert('✅ تم تعديل المنفذ بنجاح!');
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            alert('❌ خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('❌ خطأ في تعديل المنفذ:', error);
        alert('❌ حدث خطأ في تعديل المنفذ');
    });
}

// دالة مساعدة للتحقق من صحة البيانات
function validatePortData(data) {
    const required = ['port_code', 'port_name_ar', 'port_type', 'country', 'city'];
    for (let field of required) {
        if (!data[field] || data[field].trim() === '') {
            return `الحقل ${field} مطلوب`;
        }
    }
    return null;
}

// عرض تفاصيل المنفذ
function viewPort(portId) {
    console.log('عرض تفاصيل المنفذ:', portId);
    // يمكن إضافة نافذة منبثقة لعرض التفاصيل
    alert('سيتم إضافة نافذة عرض التفاصيل قريباً');
}

// تعديل المنفذ
function editPort(portId) {
    console.log('🔧 تعديل المنفذ:', portId);

    // جلب بيانات المنفذ أولاً
    fetch(`/shipments/api/customs-ports/${portId}`)
        .then(response => response.json())
        .then(portData => {
            if (portData.success) {
                openEditPortModal(portData.port);
            } else {
                alert('❌ خطأ في جلب بيانات المنفذ: ' + portData.message);
            }
        })
        .catch(error => {
            console.error('❌ خطأ في جلب بيانات المنفذ:', error);
            alert('❌ حدث خطأ في جلب بيانات المنفذ');
        });
}

// حذف المنفذ
function deletePort(portId, portName) {
    if (confirm(`هل أنت متأكد من حذف المنفذ: ${portName}؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        fetch(`/shipments/api/customs-ports/${portId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                // إعادة تحميل الصفحة
                location.reload();
                alert('تم حذف المنفذ بنجاح!');
            } else {
                alert('خطأ: ' + result.message);
            }
        })
        .catch(error => {
            console.error('خطأ في حذف المنفذ:', error);
            alert('حدث خطأ في حذف المنفذ');
        });
    }
}

// تطبيق المرشحات
function applyFilters() {
    const searchTerm = document.getElementById('searchPort').value.toLowerCase();
    const filterType = document.getElementById('filterType').value;
    const filterCountry = document.getElementById('filterCountry').value;
    const filterStatus = document.getElementById('filterStatus').value;

    const table = document.getElementById('portsTable');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.getElementsByTagName('td');

        if (cells.length > 0) {
            const portCode = cells[0].textContent.toLowerCase();
            const portName = cells[1].textContent.toLowerCase();
            const portType = cells[2].getAttribute('data-type') || '';
            const country = cells[3].textContent;
            const status = cells[6].textContent.includes('نشط') ? '1' : '0';

            let showRow = true;

            // فلترة النص
            if (searchTerm && !portCode.includes(searchTerm) && !portName.includes(searchTerm)) {
                showRow = false;
            }

            // فلترة النوع
            if (filterType && portType !== filterType) {
                showRow = false;
            }

            // فلترة الدولة
            if (filterCountry && !country.includes(filterCountry)) {
                showRow = false;
            }

            // فلترة الحالة
            if (filterStatus && status !== filterStatus) {
                showRow = false;
            }

            row.style.display = showRow ? '' : 'none';
        }
    }
}

// مسح المرشحات
function clearFilters() {
    document.getElementById('searchPort').value = '';
    document.getElementById('filterType').value = '';
    document.getElementById('filterCountry').value = '';
    document.getElementById('filterStatus').value = '';

    // إظهار جميع الصفوف
    const table = document.getElementById('portsTable');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

    for (let i = 0; i < rows.length; i++) {
        rows[i].style.display = '';
    }
}

// إضافة مستمعات الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // مستمع للبحث المباشر
    document.getElementById('searchPort').addEventListener('input', applyFilters);

    // مستمعات للمرشحات
    document.getElementById('filterType').addEventListener('change', applyFilters);
    document.getElementById('filterCountry').addEventListener('change', applyFilters);
    document.getElementById('filterStatus').addEventListener('change', applyFilters);

    // إغلاق النافذة بمفتاح Escape
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const addModal = document.getElementById('dynamicPortModal');
            const editModal = document.getElementById('editPortModal');

            if (addModal) {
                closePortModal();
            } else if (editModal) {
                closeEditPortModal();
            }
        }
    });

    console.log('🔧 تم تحميل صفحة المنافذ الجمركية');
    console.log('🎯 النافذة الديناميكية جاهزة للاستخدام');
});
</script>

<!-- سيتم إنشاء النافذة ديناميكياً بـ JavaScript -->

{% endblock %}
