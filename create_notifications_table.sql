-- إن<PERSON>اء جدول الإشعارات الحقيقي
-- Create Real Notifications Table

-- حذف الجدول إذا كان موجوداً
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE notifications CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE notifications_seq';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

-- إنشاء جدول الإشعارات
CREATE TABLE notifications (
    id NUMBER PRIMARY KEY,
    notification_type VARCHAR2(50) NOT NULL,
    title VARCHAR2(200) NOT NULL,
    message VARCHAR2(1000) NOT NULL,
    is_read NUMBER(1) DEFAULT 0,
    created_at DATE DEFAULT SYSDATE,
    read_at DATE,
    related_id VARCHAR2(100),
    related_type VARCHAR2(50),
    priority_level NUMBER DEFAULT 5,
    action_url VARCHAR2(500),
    created_by VARCHAR2(100) DEFAULT 'SYSTEM'
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> التسلسل
CREATE SEQUENCE notifications_seq START WITH 1 INCREMENT BY 1;

-- إنشاء فهارس للأداء
CREATE INDEX idx_notifications_type ON notifications(notification_type);
CREATE INDEX idx_notifications_read ON notifications(is_read);
CREATE INDEX idx_notifications_date ON notifications(created_at);
CREATE INDEX idx_notifications_related ON notifications(related_type, related_id);

-- إدراج بيانات تجريبية حقيقية
INSERT INTO notifications (id, notification_type, title, message, is_read, created_at, related_id, related_type, priority_level, action_url) VALUES 
(notifications_seq.NEXTVAL, 'success', 'تم إنشاء أمر تسليم بنجاح', 'تم إنشاء أمر التسليم رقم DO-2025-000156 للشحنة SH-2025-000089 بنجاح. تم تعيين المخلص أحمد محمد للتعامل مع هذا الأمر.', 0, SYSDATE - INTERVAL '5' MINUTE, 'DO-2025-000156', 'delivery_order', 6, '/delivery-orders/DO-2025-000156');

INSERT INTO notifications (id, notification_type, title, message, is_read, created_at, related_id, related_type, priority_level, action_url) VALUES 
(notifications_seq.NEXTVAL, 'warning', 'تأخير في التخليص الجمركي', 'الشحنة SH-2025-000087 متأخرة في التخليص الجمركي لأكثر من 48 ساعة. يرجى متابعة الأمر مع المخلص المسؤول.', 0, SYSDATE - INTERVAL '15' MINUTE, 'SH-2025-000087', 'shipment', 8, '/shipments/SH-2025-000087');

INSERT INTO notifications (id, notification_type, title, message, is_read, created_at, related_id, related_type, priority_level, action_url) VALUES 
(notifications_seq.NEXTVAL, 'error', 'فشل في إرسال الإشعار', 'فشل في إرسال إشعار SMS للعميل محمد أحمد بخصوص الشحنة SH-2025-000085. يرجى التحقق من رقم الهاتف والمحاولة مرة أخرى.', 0, SYSDATE - INTERVAL '30' MINUTE, 'SH-2025-000085', 'shipment', 7, '/shipments/SH-2025-000085');

INSERT INTO notifications (id, notification_type, title, message, is_read, created_at, related_id, related_type, priority_level, action_url) VALUES 
(notifications_seq.NEXTVAL, 'info', 'تحديث تقييم المخلص', 'تم تحديث تقييم المخلص سعد العلي إلى 4.8 نجوم بناءً على أدائه في آخر 10 أوامر تسليم.', 1, SYSDATE - INTERVAL '1' HOUR, 'AG-001', 'agent', 4, '/agents/AG-001');

INSERT INTO notifications (id, notification_type, title, message, is_read, created_at, related_id, related_type, priority_level, action_url) VALUES 
(notifications_seq.NEXTVAL, 'system', 'تحديث النظام مكتمل', 'تم تحديث النظام إلى الإصدار 2.1.5 بنجاح. تم إضافة ميزات جديدة لتحسين الأداء والأمان.', 1, SYSDATE - INTERVAL '3' HOUR, 'SYS-UPDATE-2.1.5', 'system', 5, '/system/updates');

INSERT INTO notifications (id, notification_type, title, message, is_read, created_at, related_id, related_type, priority_level, action_url) VALUES 
(notifications_seq.NEXTVAL, 'success', 'تم تنفيذ قاعدة أتمتة', 'تم تنفيذ قاعدة "إنشاء أمر تسليم تلقائي" بنجاح للشحنة SH-2025-000090. تم إنشاء أمر التسليم DO-2025-000157 تلقائياً.', 0, SYSDATE - INTERVAL '45' MINUTE, 'DO-2025-000157', 'automation', 6, '/shipments/automation-dashboard');

INSERT INTO notifications (id, notification_type, title, message, is_read, created_at, related_id, related_type, priority_level, action_url) VALUES 
(notifications_seq.NEXTVAL, 'warning', 'شحنة تحتاج متابعة', 'الشحنة SH-2025-000092 في حالة انتظار منذ أكثر من 24 ساعة. يرجى مراجعة الحالة واتخاذ الإجراء المناسب.', 0, SYSDATE - INTERVAL '2' HOUR, 'SH-2025-000092', 'shipment', 7, '/shipments/SH-2025-000092');

INSERT INTO notifications (id, notification_type, title, message, is_read, created_at, related_id, related_type, priority_level, action_url) VALUES 
(notifications_seq.NEXTVAL, 'info', 'وصول شحنة جديدة', 'وصلت الشحنة SH-2025-000093 إلى الميناء بنجاح. يمكن الآن البدء في إجراءات التخليص الجمركي.', 1, SYSDATE - INTERVAL '4' HOUR, 'SH-2025-000093', 'shipment', 5, '/shipments/SH-2025-000093');

INSERT INTO notifications (id, notification_type, title, message, is_read, created_at, related_id, related_type, priority_level, action_url) VALUES 
(notifications_seq.NEXTVAL, 'success', 'تم تسليم الشحنة بنجاح', 'تم تسليم الشحنة SH-2025-000084 للعميل بنجاح. تم إغلاق أمر التسليم DO-2025-000154.', 1, SYSDATE - INTERVAL '6' HOUR, 'SH-2025-000084', 'shipment', 6, '/shipments/SH-2025-000084');

INSERT INTO notifications (id, notification_type, title, message, is_read, created_at, related_id, related_type, priority_level, action_url) VALUES 
(notifications_seq.NEXTVAL, 'error', 'خطأ في معالجة الدفع', 'حدث خطأ في معالجة دفعة العميل للشحنة SH-2025-000091. يرجى مراجعة تفاصيل الدفع والمحاولة مرة أخرى.', 0, SYSDATE - INTERVAL '1' HOUR + INTERVAL '30' MINUTE, 'SH-2025-000091', 'payment', 8, '/payments/SH-2025-000091');

-- حفظ التغييرات
COMMIT;

-- عرض ملخص البيانات المدرجة
SELECT 
    notification_type,
    COUNT(*) as count,
    COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread_count
FROM notifications 
GROUP BY notification_type
ORDER BY notification_type;

-- عرض إجمالي الإحصائيات
SELECT 
    COUNT(*) as total_notifications,
    COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread_notifications,
    COUNT(CASE WHEN is_read = 1 THEN 1 END) as read_notifications,
    ROUND(COUNT(CASE WHEN is_read = 1 THEN 1 END) * 100.0 / COUNT(*), 1) as read_percentage
FROM notifications;
