#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية للإشعارات
Add Sample Notifications Data
"""

from database_manager import DatabaseManager
from datetime import datetime, timedelta
import random

def add_sample_notifications():
    """إضافة إشعارات تجريبية"""
    db_manager = DatabaseManager()
    
    try:
        print("🔔 بدء إضافة بيانات تجريبية للإشعارات...")
        
        # جلب الشحنات الموجودة
        shipments = db_manager.execute_query("SELECT id, tracking_number, recipient_phone, recipient_email FROM shipments")
        
        if not shipments:
            print("❌ لا توجد شحنات لإضافة إشعارات لها")
            return
        
        # أنواع الإشعارات وقوالبها
        notification_templates = [
            {
                'type': 'SMS',
                'subject': 'تأكيد إنشاء الشحنة',
                'message': 'تم إنشاء شحنتك بنجاح. رقم التتبع: {tracking_number}. يمكنك تتبعها على موقعنا.',
                'status': 'مرسل'
            },
            {
                'type': 'EMAIL',
                'subject': 'تأكيد استلام الشحنة',
                'message': 'تم استلام شحنتك رقم {tracking_number} من المرسل وهي الآن في الطريق إليك.',
                'status': 'مرسل'
            },
            {
                'type': 'SMS',
                'subject': 'الشحنة في الطريق',
                'message': 'شحنتك رقم {tracking_number} في الطريق إليك. الوقت المتوقع للوصول: غداً.',
                'status': 'مرسل'
            },
            {
                'type': 'SMS',
                'subject': 'خارج للتسليم',
                'message': 'شحنتك رقم {tracking_number} خارج للتسليم وستصل خلال 30 دقيقة تقريباً.',
                'status': 'مرسل'
            },
            {
                'type': 'EMAIL',
                'subject': 'تم تسليم الشحنة',
                'message': 'تم تسليم شحنتك رقم {tracking_number} بنجاح. شكراً لاختيارك خدماتنا.',
                'status': 'مرسل'
            },
            {
                'type': 'SMS',
                'subject': 'تأخير في التسليم',
                'message': 'نعتذر عن تأخير شحنتك رقم {tracking_number}. سيتم التسليم في أقرب وقت ممكن.',
                'status': 'معلق'
            },
            {
                'type': 'PUSH',
                'subject': 'إشعار فوري',
                'message': 'تحديث على شحنتك رقم {tracking_number}. تحقق من التطبيق للمزيد من التفاصيل.',
                'status': 'فشل'
            }
        ]
        
        notifications_added = 0
        
        # إضافة إشعارات لكل شحنة
        for shipment in shipments:
            shipment_id = shipment[0]
            tracking_number = shipment[1]
            recipient_phone = shipment[2]
            recipient_email = shipment[3]
            
            # اختيار عدد عشوائي من الإشعارات لكل شحنة (1-4)
            num_notifications = random.randint(1, 4)
            selected_templates = random.sample(notification_templates, num_notifications)
            
            for template in selected_templates:
                # تحديد المستقبل حسب نوع الإشعار
                if template['type'] == 'EMAIL':
                    recipient = recipient_email or '<EMAIL>'
                else:
                    recipient = recipient_phone or '0501234567'
                
                # تخصيص الرسالة
                message = template['message'].format(tracking_number=tracking_number)
                
                # تاريخ عشوائي في آخر 7 أيام
                created_date = datetime.now() - timedelta(days=random.randint(0, 7), 
                                                        hours=random.randint(0, 23),
                                                        minutes=random.randint(0, 59))
                
                # تاريخ الإرسال (إذا كان مرسل)
                sent_date = None
                if template['status'] == 'مرسل':
                    sent_date = created_date + timedelta(minutes=random.randint(1, 5))
                
                # إدراج الإشعار
                insert_sql = """
                    INSERT INTO shipment_notifications (
                        shipment_id, notification_type, recipient, subject, message, 
                        status, created_at, sent_at, retry_count
                    ) VALUES (
                        :shipment_id, :notification_type, :recipient, :subject, :message,
                        :status, :created_at, :sent_at, :retry_count
                    )
                """
                
                params = {
                    'shipment_id': shipment_id,
                    'notification_type': template['type'],
                    'recipient': recipient,
                    'subject': template['subject'],
                    'message': message,
                    'status': template['status'],
                    'created_at': created_date,
                    'sent_at': sent_date,
                    'retry_count': 0 if template['status'] != 'فشل' else random.randint(1, 3)
                }
                
                try:
                    db_manager.execute_update(insert_sql, params)
                    notifications_added += 1
                    print(f"✅ تم إضافة إشعار {template['type']} للشحنة {tracking_number}")
                    
                except Exception as e:
                    print(f"❌ خطأ في إضافة إشعار للشحنة {tracking_number}: {e}")
        
        print(f"\n🎉 تم إضافة {notifications_added} إشعار تجريبي بنجاح!")
        print("📊 يمكنك الآن رؤية الإشعارات في نظام إدارة الإشعارات")
        
        # إضافة إحصائيات
        total_notifications = db_manager.execute_query("SELECT COUNT(*) FROM shipment_notifications")
        total_count = total_notifications[0][0] if total_notifications else 0
        
        sent_notifications = db_manager.execute_query("SELECT COUNT(*) FROM shipment_notifications WHERE status = 'مرسل'")
        sent_count = sent_notifications[0][0] if sent_notifications else 0
        
        pending_notifications = db_manager.execute_query("SELECT COUNT(*) FROM shipment_notifications WHERE status = 'معلق'")
        pending_count = pending_notifications[0][0] if pending_notifications else 0
        
        failed_notifications = db_manager.execute_query("SELECT COUNT(*) FROM shipment_notifications WHERE status = 'فشل'")
        failed_count = failed_notifications[0][0] if failed_notifications else 0
        
        print(f"\n📈 إحصائيات الإشعارات:")
        print(f"   📧 إجمالي الإشعارات: {total_count}")
        print(f"   ✅ تم الإرسال: {sent_count}")
        print(f"   ⏳ في الانتظار: {pending_count}")
        print(f"   ❌ فشل الإرسال: {failed_count}")
        print(f"   📊 معدل النجاح: {(sent_count/total_count*100):.1f}%" if total_count > 0 else "   📊 معدل النجاح: 0%")
        
    except Exception as e:
        print(f"❌ خطأ عام في إضافة الإشعارات: {e}")
    
    finally:
        db_manager.close()

if __name__ == "__main__":
    add_sample_notifications()
