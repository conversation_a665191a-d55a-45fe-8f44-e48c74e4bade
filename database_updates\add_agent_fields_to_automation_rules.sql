-- =====================================================
-- تحديث جدول automation_rules لإضافة حقول اختيار المخلص
-- Database Update: Add Agent Selection Fields to automation_rules
-- التاريخ: 2025-08-20
-- =====================================================

-- إضافة حقول اختيار المخلص إلى جدول automation_rules
ALTER TABLE automation_rules ADD (
    -- معرف المخلص المختار
    selected_agent_id NUMBER,
    
    -- معايير الاختيار التلقائي للمخلص
    agent_selection_criteria VARCHAR2(50) DEFAULT 'rating',
    
    -- معرف فرع المخلص
    agent_branch_id NUMBER,
    
    -- معرف المنفذ الجمركي للمخلص
    agent_port_id NUMBER,
    
    -- هل الاختيار تلقائي؟ (1 = تلقائي، 0 = يدوي)
    auto_agent_selection NUMBER(1) DEFAULT 0,
    
    -- ملاحظات تعيين المخلص
    agent_assignment_notes CLOB,
    
    -- تاريخ آخر تحديث لإعدادات المخلص
    agent_settings_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- من قام بآخر تحديث لإعدادات المخلص
    agent_settings_updated_by NUMBER
);

-- إضافة تعليقات على الحقول الجديدة
COMMENT ON COLUMN automation_rules.selected_agent_id IS 'معرف المخلص المختار للقاعدة';
COMMENT ON COLUMN automation_rules.agent_selection_criteria IS 'معايير اختيار المخلص (rating, experience, availability, specialization)';
COMMENT ON COLUMN automation_rules.agent_branch_id IS 'معرف فرع المخلص المختار';
COMMENT ON COLUMN automation_rules.agent_port_id IS 'معرف المنفذ الجمركي للمخلص المختار';
COMMENT ON COLUMN automation_rules.auto_agent_selection IS 'هل اختيار المخلص تلقائي (1) أم يدوي (0)';
COMMENT ON COLUMN automation_rules.agent_assignment_notes IS 'ملاحظات إضافية حول تعيين المخلص';
COMMENT ON COLUMN automation_rules.agent_settings_updated_at IS 'تاريخ آخر تحديث لإعدادات المخلص';
COMMENT ON COLUMN automation_rules.agent_settings_updated_by IS 'معرف المستخدم الذي قام بآخر تحديث لإعدادات المخلص';

-- إضافة قيود المرجعية (Foreign Keys)
ALTER TABLE automation_rules ADD CONSTRAINT fk_automation_rules_agent 
    FOREIGN KEY (selected_agent_id) REFERENCES customs_agents(id);

ALTER TABLE automation_rules ADD CONSTRAINT fk_automation_rules_agent_branch 
    FOREIGN KEY (agent_branch_id) REFERENCES branches(brn_no);

ALTER TABLE automation_rules ADD CONSTRAINT fk_automation_rules_agent_port 
    FOREIGN KEY (agent_port_id) REFERENCES customs_ports(id);

ALTER TABLE automation_rules ADD CONSTRAINT fk_automation_rules_agent_updater 
    FOREIGN KEY (agent_settings_updated_by) REFERENCES users(id);

-- إضافة قيود التحقق (Check Constraints)
ALTER TABLE automation_rules ADD CONSTRAINT chk_auto_agent_selection 
    CHECK (auto_agent_selection IN (0, 1));

ALTER TABLE automation_rules ADD CONSTRAINT chk_agent_selection_criteria 
    CHECK (agent_selection_criteria IN ('rating', 'experience', 'availability', 'specialization'));

-- إنشاء فهرس لتحسين الأداء
CREATE INDEX idx_automation_rules_agent ON automation_rules(selected_agent_id);
CREATE INDEX idx_automation_rules_agent_branch ON automation_rules(agent_branch_id);
CREATE INDEX idx_automation_rules_agent_port ON automation_rules(agent_port_id);

-- إضافة trigger لتحديث تاريخ آخر تعديل تلقائياً
CREATE OR REPLACE TRIGGER trg_automation_rules_agent_update
    BEFORE UPDATE OF selected_agent_id, agent_selection_criteria, agent_branch_id, 
                     agent_port_id, auto_agent_selection, agent_assignment_notes
    ON automation_rules
    FOR EACH ROW
BEGIN
    :NEW.agent_settings_updated_at := CURRENT_TIMESTAMP;
    IF :NEW.agent_settings_updated_by IS NULL THEN
        :NEW.agent_settings_updated_by := 1; -- افتراضي للمستخدم الأول
    END IF;
END;
/

-- إضافة بيانات افتراضية للقواعد الموجودة
UPDATE automation_rules 
SET agent_selection_criteria = 'rating',
    auto_agent_selection = 1,
    agent_settings_updated_at = CURRENT_TIMESTAMP,
    agent_settings_updated_by = 1
WHERE action_type = 'CREATE_DELIVERY_ORDER_WITH_AGENT'
AND agent_selection_criteria IS NULL;

-- تأكيد التغييرات
COMMIT;

-- عرض ملخص التحديث
SELECT 'تم تحديث جدول automation_rules بنجاح' AS status,
       COUNT(*) AS total_rules,
       SUM(CASE WHEN action_type = 'CREATE_DELIVERY_ORDER_WITH_AGENT' THEN 1 ELSE 0 END) AS agent_related_rules
FROM automation_rules;

PROMPT =====================================================
PROMPT تم إضافة حقول اختيار المخلص بنجاح!
PROMPT =====================================================
