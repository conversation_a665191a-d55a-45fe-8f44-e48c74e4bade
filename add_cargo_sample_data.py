#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية لنظام شحن الحاويات والبضائع
Add Sample Data for Container & Cargo Shipping System
"""

from database_manager import DatabaseManager
from datetime import datetime, timedelta
import random

def add_cargo_sample_data():
    """إضافة بيانات تجريبية لنظام الحاويات"""
    db_manager = DatabaseManager()
    
    try:
        print("🚢 بدء إضافة بيانات تجريبية لنظام شحن الحاويات...")
        
        # إضافة عملاء تجريبيين
        print("🏢 إضافة عملاء تجريبيين...")
        
        customers_data = [
            ("CUST001", "شركة الرياض للتجارة", "أحمد محمد السالم", "0112345678", "<EMAIL>", "الرياض، المملكة العربية السعودية", "السعودية", "الرياض", "مصدر", "1234567890", "1010123456"),
            ("CUST002", "مؤسسة جدة للاستيراد والتصدير", "فاطمة علي الأحمد", "0126789012", "<EMAIL>", "جدة، المملكة العربية السعودية", "السعودية", "جدة", "مستورد", "2345678901", "2020234567"),
            ("CUST003", "شركة الإمارات للشحن", "محمد عبدالله الزعابي", "971501234567", "<EMAIL>", "دبي، الإمارات العربية المتحدة", "الإمارات", "دبي", "وكيل شحن", "3456789012", "3030345678"),
            ("CUST004", "Shanghai Trading Company", "Li Wei", "862112345678", "<EMAIL>", "Shanghai, China", "الصين", "شنغهاي", "مصدر", "4567890123", "4040456789"),
            ("CUST005", "Rotterdam Logistics B.V.", "Jan van der Berg", "31102345678", "<EMAIL>", "Rotterdam, Netherlands", "هولندا", "روتردام", "مستورد", "5678901234", "5050567890")
        ]
        
        for customer in customers_data:
            try:
                insert_sql = """
                    INSERT INTO customers (
                        customer_code, company_name, contact_person, phone, email, 
                        address, country, city, customer_type, tax_number, commercial_register
                    ) VALUES (
                        :customer_code, :company_name, :contact_person, :phone, :email,
                        :address, :country, :city, :customer_type, :tax_number, :commercial_register
                    )
                """
                
                params = {
                    'customer_code': customer[0],
                    'company_name': customer[1],
                    'contact_person': customer[2],
                    'phone': customer[3],
                    'email': customer[4],
                    'address': customer[5],
                    'country': customer[6],
                    'city': customer[7],
                    'customer_type': customer[8],
                    'tax_number': customer[9],
                    'commercial_register': customer[10]
                }
                
                db_manager.execute_update(insert_sql, params)
                print(f"✅ تم إضافة العميل: {customer[1]}")
                
            except Exception as e:
                print(f"❌ خطأ في إضافة العميل {customer[1]}: {e}")
        
        # إضافة سفن تجريبية
        print("\n⚓ إضافة سفن تجريبية...")
        
        vessels_data = [
            ("MSC OSCAR", "9744465", "حاويات", "ليبيريا", 19224, 197362, 395.4, 59.0, 1, 1),
            ("EVER GIVEN", "9811000", "حاويات", "بنما", 20124, 199629, 400.0, 58.8, 2, 2),
            ("COSCO SHIPPING UNIVERSE", "9795600", "حاويات", "الصين", 21237, 213442, 400.0, 58.6, 3, 3),
            ("HAPAG LLOYD BERLIN", "9863404", "حاويات", "ألمانيا", 23756, 228283, 399.9, 61.3, 4, 4),
            ("NSCSA RIYADH", "9900001", "حاويات", "السعودية", 15000, 150000, 350.0, 55.0, 5, 1)
        ]
        
        for vessel in vessels_data:
            try:
                insert_sql = """
                    INSERT INTO vessels (
                        vessel_name, imo_number, vessel_type, flag, capacity_teu, 
                        capacity_weight, length_meters, width_meters, shipping_line_id, current_port_id
                    ) VALUES (
                        :vessel_name, :imo_number, :vessel_type, :flag, :capacity_teu,
                        :capacity_weight, :length_meters, :width_meters, :shipping_line_id, :current_port_id
                    )
                """
                
                params = {
                    'vessel_name': vessel[0],
                    'imo_number': vessel[1],
                    'vessel_type': vessel[2],
                    'flag': vessel[3],
                    'capacity_teu': vessel[4],
                    'capacity_weight': vessel[5],
                    'length_meters': vessel[6],
                    'width_meters': vessel[7],
                    'shipping_line_id': vessel[8],
                    'current_port_id': vessel[9]
                }
                
                db_manager.execute_update(insert_sql, params)
                print(f"✅ تم إضافة السفينة: {vessel[0]}")
                
            except Exception as e:
                print(f"❌ خطأ في إضافة السفينة {vessel[0]}: {e}")
        
        # إضافة حاويات تجريبية
        print("\n📦 إضافة حاويات تجريبية...")
        
        container_types = ["20' Standard", "40' Standard", "40' High Cube", "20' Refrigerated", "40' Refrigerated"]
        container_sizes = [20, 40, 40, 20, 40]
        max_weights = [28200, 30480, 30480, 27700, 29600]
        
        for i in range(50):
            container_type_idx = random.randint(0, len(container_types) - 1)
            container_number = f"MSKU{random.randint(1000000, 9999999)}"
            
            try:
                insert_sql = """
                    INSERT INTO containers (
                        container_number, container_type, size_feet, max_weight,
                        current_weight, status, condition_status, current_port_id, owner_line_id
                    ) VALUES (
                        :container_number, :container_type, :size_feet, :max_weight,
                        :current_weight, :status, :condition_status, :current_port_id, :owner_line_id
                    )
                """
                
                params = {
                    'container_number': container_number,
                    'container_type': container_types[container_type_idx],
                    'size_feet': container_sizes[container_type_idx],
                    'max_weight': max_weights[container_type_idx],
                    'current_weight': random.randint(0, int(max_weights[container_type_idx] * 0.8)),
                    'status': random.choice(['فارغ', 'محمل', 'في النقل']),
                    'condition_status': random.choice(['ممتاز', 'جيد', 'مقبول']),
                    'current_port_id': random.randint(1, 5),
                    'owner_line_id': random.randint(1, 5)
                }
                
                db_manager.execute_update(insert_sql, params)
                
            except Exception as e:
                print(f"❌ خطأ في إضافة الحاوية {container_number}: {e}")
        
        print(f"✅ تم إضافة 50 حاوية تجريبية")
        
        # إضافة شحنات تجريبية
        print("\n📋 إضافة شحنات تجريبية...")
        
        cargo_descriptions = [
            "أجهزة إلكترونية متنوعة",
            "قطع غيار سيارات",
            "منتجات غذائية مجففة",
            "مواد بناء وإنشاءات",
            "منسوجات وملابس جاهزة",
            "معدات طبية",
            "أثاث منزلي ومكتبي",
            "مواد كيميائية صناعية",
            "منتجات بلاستيكية",
            "آلات ومعدات صناعية"
        ]
        
        cargo_types = ["عام", "خطرة", "مبردة", "سائلة", "معدات"]
        statuses = ["محجوز", "مؤكد", "محمل", "في الطريق", "وصل"]
        
        for i in range(20):
            shipment_number = f"CRG{datetime.now().strftime('%Y%m%d')}{str(i+1).zfill(3)}"
            booking_number = f"BKG{datetime.now().strftime('%Y%m%d')}{str(i+1).zfill(3)}"
            
            # تواريخ عشوائية
            booking_date = datetime.now() - timedelta(days=random.randint(1, 30))
            etd = booking_date + timedelta(days=random.randint(1, 7))
            eta = etd + timedelta(days=random.randint(7, 21))
            
            try:
                insert_sql = """
                    INSERT INTO cargo_shipments (
                        shipment_number, booking_number, shipper_id, consignee_id,
                        origin_port_id, destination_port_id, shipping_line_id,
                        cargo_type, total_weight, total_volume,
                        total_packages, package_type, booking_date, etd, eta,
                        status, freight_cost, other_charges, total_cost, currency,
                        is_dangerous, temperature_controlled, created_by,
                        cargo_description
                    ) VALUES (
                        :shipment_number, :booking_number, :shipper_id, :consignee_id,
                        :origin_port_id, :destination_port_id, :shipping_line_id,
                        :cargo_type, :total_weight, :total_volume,
                        :total_packages, :package_type, :booking_date, :etd, :eta,
                        :status, :freight_cost, :other_charges, :total_cost, :currency,
                        :is_dangerous, :temperature_controlled, :created_by,
                        :cargo_description
                    )
                """
                
                params = {
                    'shipment_number': shipment_number,
                    'booking_number': booking_number,
                    'shipper_id': random.randint(1, 5),
                    'consignee_id': random.randint(1, 5),
                    'origin_port_id': random.randint(1, 5),
                    'destination_port_id': random.randint(1, 5),
                    'shipping_line_id': random.randint(1, 5),
                    'cargo_description': random.choice(cargo_descriptions),
                    'cargo_type': random.choice(cargo_types),
                    'total_weight': round(random.uniform(1000, 25000), 2),
                    'total_volume': round(random.uniform(10, 100), 2),
                    'total_packages': random.randint(1, 500),
                    'package_type': random.choice(['صناديق', 'أكياس', 'براميل', 'منصات']),
                    'booking_date': booking_date,
                    'etd': etd,
                    'eta': eta,
                    'status': random.choice(statuses),
                    'freight_cost': round(random.uniform(1500, 5000), 2),
                    'other_charges': round(random.uniform(200, 800), 2),
                    'total_cost': round(random.uniform(1700, 5800), 2),
                    'currency': random.choice(['USD', 'SAR', 'EUR']),
                    'is_dangerous': random.choice([0, 0, 0, 1]),  # 25% احتمال مواد خطرة
                    'temperature_controlled': random.choice([0, 0, 1]),  # 33% احتمال تحكم حرارة
                    'created_by': 'system'
                }
                
                db_manager.execute_update(insert_sql, params)
                print(f"✅ تم إضافة الشحنة: {shipment_number}")
                
            except Exception as e:
                print(f"❌ خطأ في إضافة الشحنة {shipment_number}: {e}")
        
        print("\n🎉 تم إضافة جميع البيانات التجريبية بنجاح!")
        print("📊 ملخص البيانات المضافة:")
        
        # إحصائيات
        customers_count = db_manager.execute_query("SELECT COUNT(*) FROM customers")
        vessels_count = db_manager.execute_query("SELECT COUNT(*) FROM vessels")
        containers_count = db_manager.execute_query("SELECT COUNT(*) FROM containers")
        shipments_count = db_manager.execute_query("SELECT COUNT(*) FROM cargo_shipments")
        
        print(f"   🏢 العملاء: {customers_count[0][0] if customers_count else 0}")
        print(f"   ⚓ السفن: {vessels_count[0][0] if vessels_count else 0}")
        print(f"   📦 الحاويات: {containers_count[0][0] if containers_count else 0}")
        print(f"   📋 الشحنات: {shipments_count[0][0] if shipments_count else 0}")
        
    except Exception as e:
        print(f"❌ خطأ عام في إضافة البيانات: {e}")
    
    finally:
        db_manager.close()

if __name__ == "__main__":
    add_cargo_sample_data()
