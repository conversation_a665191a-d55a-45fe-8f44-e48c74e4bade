#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حساب عدد الأعمدة في SQL INSERT بدقة
"""

def count_sql_columns():
    """حساب عدد الأعمدة في SQL INSERT"""
    
    # الأعمدة من SQL الحالي
    sql_columns = [
        # المجموعة الأولى (36 عمود)
        'id', 'shipment_number', 'booking_number', 'bill_of_lading_number', 'shipping_type',
        'shipping_line_id', 'origin_port_id', 'destination_port_id', 'etd', 'eta', 'estimated_transit_time',
        'vessel_name', 'voyage_number', 'container_seal_number', 'tracking_number',
        'freight_forwarder', 'incoterms', 'port_of_loading', 'port_of_discharge',
        'customs_declaration_number', 'insurance_policy_number',
        'shipper_id', 'consignee_id', 'notify_party_id', 'purchase_order_id',
        'cargo_type', 'total_weight', 'net_weight', 'total_volume', 'total_packages', 'package_type',
        'is_dangerous', 'temperature_controlled',
        'currency', 'freight_cost', 'other_charges', 'total_cost',
        
        # الأعمدة الثابتة (2 عمود)
        'status', 'created_by',
        
        # المجموعة الثانية (16 عمود)
        'shipment_status', 'estimated_delivery_date', 'status_updated_by',
        'priority', 'actual_delivery_date', 'requires_release',
        'shipping_company', 'container_number', 'tracking_url',
        'courier_company', 'documents_tracking_number', 'documents_sent_date',
        'documents_recipient_name', 'documents_delivery_status',
        'release_status',
        
        # أعمدة CLOB (4 أعمدة)
        'shipping_instructions', 'cargo_description', 'special_instructions',
        'tracking_notes'
    ]
    
    print("📊 حساب أعمدة SQL INSERT:")
    print(f"المجموعة الأولى: 37 عمود")
    print(f"الأعمدة الثابتة: 2 عمود (status='محجوز', created_by='test_user')")
    print(f"المجموعة الثانية: 15 عمود")
    print(f"أعمدة CLOB: 4 أعمدة")
    print(f"المجموع الكلي: {len(sql_columns)} عمود")
    
    # حساب المعاملات المطلوبة
    variable_columns = len(sql_columns) - 2  # طرح الأعمدة الثابتة
    print(f"المعاملات المطلوبة: {variable_columns} معامل")
    
    return len(sql_columns), variable_columns

def count_values_parameters():
    """حساب معاملات VALUES"""
    
    print("\n📊 حساب معاملات VALUES:")
    print(f":1 إلى :37 = 37 معامل")
    print(f"'محجوز', 'test_user' = 2 قيمة ثابتة")
    print(f":40 إلى :56 = 17 معامل")
    print(f"المجموع: 37 + 2 + 17 = 56 معامل")
    
    return 56

if __name__ == "__main__":
    print("🔍 تحليل مشكلة عدم تطابق المعاملات")
    print("=" * 50)
    
    total_columns, required_params = count_sql_columns()
    actual_params = count_values_parameters()
    
    print("\n" + "=" * 50)
    print("📋 النتائج:")
    print(f"إجمالي الأعمدة في SQL: {total_columns}")
    print(f"المعاملات المطلوبة: {required_params}")
    print(f"المعاملات الفعلية: {actual_params}")
    
    if required_params == actual_params:
        print("✅ العدد متطابق!")
    else:
        difference = actual_params - required_params
        print(f"❌ عدم تطابق: {difference} معامل إضافي")
        
        if difference > 0:
            print(f"🔧 يجب حذف {difference} معامل من VALUES")
        else:
            print(f"🔧 يجب إضافة {abs(difference)} معامل إلى VALUES")
