#!/usr/bin/env python3
"""
نظام استخراج المرفقات المتقدم
"""

import sys
import os
import imaplib
import email
from email.header import decode_header
import hashlib

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_manager import DatabaseManager
import logging

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_real_attachments():
    """استخراج المرفقات الحقيقية من IMAP وحفظها"""
    try:
        db = DatabaseManager()
        
        # الحصول على إعدادات IMAP
        account_query = """
        SELECT id, email_address, imap_server, imap_port, password_encrypted
        FROM email_accounts 
        WHERE is_active = 1 AND is_default = 1
        ORDER BY id DESC
        """
        
        account_result = db.execute_query(account_query)
        if not account_result:
            logger.error("❌ لا يوجد حساب بريد نشط")
            return False
        
        account = account_result[0]
        account_id = account[0]
        email_address = account[1]
        imap_server = account[2]
        imap_port = account[3]
        password_lob = account[4]
        
        # قراءة كلمة المرور
        if hasattr(password_lob, 'read'):
            password = password_lob.read()
            if isinstance(password, bytes):
                password = password.decode('utf-8')
        else:
            password = str(password_lob)
        
        logger.info(f"📧 استخراج مرفقات من: {email_address}")
        
        # الاتصال بـ IMAP
        mail = imaplib.IMAP4_SSL(imap_server, imap_port)
        mail.login(email_address, password)
        mail.select('INBOX')
        
        # البحث عن رسائل تحتوي على مرفقات
        status, messages = mail.search(None, 'ALL')
        message_ids = messages[0].split()
        
        logger.info(f"📬 فحص {len(message_ids)} رسالة للمرفقات...")
        
        total_attachments = 0
        processed_messages = 0
        
        # فحص الرسائل الحديثة (آخر 20 رسالة)
        for msg_id in message_ids[-20:]:
            try:
                # جلب الرسالة
                status, msg_data = mail.fetch(msg_id, '(RFC822)')
                if status != 'OK':
                    continue
                
                email_message = email.message_from_bytes(msg_data[0][1])
                
                # معلومات الرسالة
                subject = decode_header(email_message["Subject"])[0][0]
                if isinstance(subject, bytes):
                    subject = subject.decode()
                
                sender = email_message.get("From")
                
                # التحقق من وجود الرسالة في قاعدة البيانات
                check_msg_query = """
                SELECT id FROM email_messages 
                WHERE subject = :1 AND sender_email LIKE :2 AND account_id = :3
                """
                sender_email = sender.split('<')[-1].replace('>', '').strip() if '<' in sender else sender
                
                existing_msg = db.execute_query(check_msg_query, [subject, f'%{sender_email}%', account_id])
                
                if not existing_msg:
                    # إنشاء الرسالة أولاً
                    insert_msg_query = """
                    INSERT INTO email_messages (
                        id, account_id, subject, sender_email, sender_name,
                        body_text, received_at, created_at, has_attachments
                    ) VALUES (
                        email_messages_seq.NEXTVAL, :1, :2, :3, :4, :5, SYSDATE, SYSDATE, 0
                    ) RETURNING id INTO :6
                    """
                    
                    cursor = db.connection.cursor()
                    message_id_var = cursor.var(int)
                    cursor.execute(insert_msg_query, [
                        account_id, subject, sender_email, sender,
                        "محتوى الرسالة", message_id_var
                    ])
                    message_id = message_id_var.getvalue()[0]
                    db.commit()
                    
                else:
                    message_id = existing_msg[0][0]
                
                # استخراج المرفقات
                if email_message.is_multipart():
                    message_attachments = 0
                    
                    for part in email_message.walk():
                        content_disposition = str(part.get("Content-Disposition", ""))
                        filename = part.get_filename()
                        
                        # التحقق من وجود مرفق
                        if filename or "attachment" in content_disposition:
                            try:
                                # فك تشفير اسم الملف
                                if filename:
                                    try:
                                        decoded_filename = decode_header(filename)[0]
                                        if isinstance(decoded_filename[0], bytes):
                                            filename = decoded_filename[0].decode(decoded_filename[1] or 'utf-8')
                                        else:
                                            filename = decoded_filename[0]
                                    except:
                                        pass
                                
                                # الحصول على محتوى الملف
                                file_data = part.get_payload(decode=True)
                                if file_data:
                                    content_type = part.get_content_type()
                                    file_size = len(file_data)
                                    
                                    # تنظيف اسم الملف
                                    safe_filename = "".join(c for c in filename if c.isalnum() or c in (' ', '.', '_', '-', '(', ')')).strip()
                                    if not safe_filename:
                                        safe_filename = f"attachment_{total_attachments + 1}"
                                    
                                    # إنشاء hash للملف لتجنب التكرار
                                    file_hash = hashlib.md5(file_data).hexdigest()
                                    
                                    # التحقق من وجود المرفق
                                    check_att_query = """
                                    SELECT id FROM email_attachments 
                                    WHERE message_id = :1 AND filename = :2
                                    """
                                    existing_att = db.execute_query(check_att_query, [message_id, safe_filename])
                                    
                                    if not existing_att:
                                        # حفظ المرفق
                                        insert_att_query = """
                                        INSERT INTO email_attachments (
                                            id, message_id, filename, content_type, 
                                            size_bytes, file_path, created_at
                                        ) VALUES (
                                            email_attachments_seq.NEXTVAL, :1, :2, :3, :4, :5, SYSDATE
                                        )
                                        """
                                        
                                        # حفظ الملف في مجلد مؤقت
                                        file_path = f"attachments/{message_id}_{safe_filename}"
                                        
                                        db.execute_update(insert_att_query, [
                                            message_id, safe_filename, content_type, 
                                            file_size, file_path
                                        ])
                                        
                                        message_attachments += 1
                                        total_attachments += 1
                                        
                                        size_mb = file_size / (1024 * 1024)
                                        logger.info(f"✅ حُفظ مرفق: {safe_filename} ({size_mb:.2f} MB)")
                                
                            except Exception as att_error:
                                logger.error(f"❌ خطأ في حفظ مرفق: {att_error}")
                    
                    # تحديث حالة المرفقات في الرسالة
                    if message_attachments > 0:
                        update_msg_query = "UPDATE email_messages SET has_attachments = 1 WHERE id = :1"
                        db.execute_update(update_msg_query, [message_id])
                        
                        logger.info(f"📧 رسالة '{subject[:30]}...' - {message_attachments} مرفق")
                
                processed_messages += 1
                
            except Exception as msg_error:
                logger.error(f"❌ خطأ في معالجة الرسالة {msg_id}: {msg_error}")
                continue
        
        db.commit()
        mail.close()
        mail.logout()
        
        logger.info(f"🎉 تم الانتهاء:")
        logger.info(f"📧 رسائل معالجة: {processed_messages}")
        logger.info(f"📎 مرفقات مستخرجة: {total_attachments}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    print("📎 استخراج المرفقات الحقيقية...")
    success = extract_real_attachments()
    
    if success:
        print("✅ تم بنجاح!")
    else:
        print("❌ فشل في العملية!")
        sys.exit(1)
