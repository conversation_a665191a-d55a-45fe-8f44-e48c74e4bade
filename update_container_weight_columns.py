#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from oracle_manager import OracleManager

def update_container_weight_columns():
    """تحديث أعمدة الوزن في جدول الحاويات"""
    oracle_mgr = OracleManager()
    
    try:
        print("📋 تحديث أعمدة الوزن في جدول CARGO_CONTAINERS...")
        
        # التحقق من وجود الأعمدة الحالية
        check_query = """
        SELECT column_name 
        FROM user_tab_columns 
        WHERE table_name = 'CARGO_CONTAINERS' 
        AND column_name IN ('WEIGHT_EMPTY', 'WEIGHT_LOADED', 'TOTAL_WEIGHT', 'NET_WEIGHT')
        ORDER BY column_name
        """
        
        existing_columns = oracle_mgr.execute_query(check_query)
        existing_column_names = [col[0] for col in existing_columns] if existing_columns else []
        
        print(f"📊 الأعمدة الموجودة: {existing_column_names}")
        
        # إذا كانت الأعمدة الجديدة موجودة بالفعل، لا نحتاج لفعل شيء
        if 'TOTAL_WEIGHT' in existing_column_names and 'NET_WEIGHT' in existing_column_names:
            print("✅ الأعمدة الجديدة موجودة بالفعل")
            
            # إذا كانت الأعمدة القديمة موجودة أيضاً، احذفها
            if 'WEIGHT_EMPTY' in existing_column_names:
                print("🗑️ حذف العمود القديم WEIGHT_EMPTY...")
                oracle_mgr.execute_query("ALTER TABLE CARGO_CONTAINERS DROP COLUMN WEIGHT_EMPTY")
                print("✅ تم حذف WEIGHT_EMPTY")
                
            if 'WEIGHT_LOADED' in existing_column_names:
                print("🗑️ حذف العمود القديم WEIGHT_LOADED...")
                oracle_mgr.execute_query("ALTER TABLE CARGO_CONTAINERS DROP COLUMN WEIGHT_LOADED")
                print("✅ تم حذف WEIGHT_LOADED")
                
            return True
        
        # إذا كانت الأعمدة القديمة موجودة، قم بإعادة تسميتها
        if 'WEIGHT_EMPTY' in existing_column_names and 'WEIGHT_LOADED' in existing_column_names:
            print("🔄 إعادة تسمية الأعمدة الموجودة...")
            
            # إعادة تسمية WEIGHT_EMPTY إلى TOTAL_WEIGHT
            rename_empty_query = """
            ALTER TABLE CARGO_CONTAINERS 
            RENAME COLUMN WEIGHT_EMPTY TO TOTAL_WEIGHT
            """
            oracle_mgr.execute_query(rename_empty_query)
            print("✅ تم إعادة تسمية WEIGHT_EMPTY إلى TOTAL_WEIGHT")
            
            # إعادة تسمية WEIGHT_LOADED إلى NET_WEIGHT
            rename_loaded_query = """
            ALTER TABLE CARGO_CONTAINERS 
            RENAME COLUMN WEIGHT_LOADED TO NET_WEIGHT
            """
            oracle_mgr.execute_query(rename_loaded_query)
            print("✅ تم إعادة تسمية WEIGHT_LOADED إلى NET_WEIGHT")
            
        else:
            # إنشاء الأعمدة الجديدة
            print("➕ إنشاء الأعمدة الجديدة...")
            
            if 'TOTAL_WEIGHT' not in existing_column_names:
                oracle_mgr.execute_query("ALTER TABLE CARGO_CONTAINERS ADD (TOTAL_WEIGHT NUMBER(15,3))")
                print("✅ تم إنشاء عمود TOTAL_WEIGHT")
                
            if 'NET_WEIGHT' not in existing_column_names:
                oracle_mgr.execute_query("ALTER TABLE CARGO_CONTAINERS ADD (NET_WEIGHT NUMBER(15,3))")
                print("✅ تم إنشاء عمود NET_WEIGHT")
        
        # إضافة تعليقات على الأعمدة
        comment_queries = [
            "COMMENT ON COLUMN CARGO_CONTAINERS.TOTAL_WEIGHT IS 'الوزن الإجمالي للحاوية بالكيلوجرام'",
            "COMMENT ON COLUMN CARGO_CONTAINERS.NET_WEIGHT IS 'الوزن الصافي للبضاعة في الحاوية بالكيلوجرام'"
        ]
        
        for query in comment_queries:
            try:
                oracle_mgr.execute_query(query)
            except Exception as e:
                print(f"⚠️ تحذير في إضافة التعليق: {e}")
        
        print("✅ تم إضافة التعليقات على الأعمدة")
        
        # التحقق من النتيجة النهائية
        verify_query = """
        SELECT column_name, data_type, nullable, data_precision, data_scale
        FROM user_tab_columns 
        WHERE table_name = 'CARGO_CONTAINERS' 
        AND column_name IN ('TOTAL_WEIGHT', 'NET_WEIGHT')
        ORDER BY column_name
        """
        
        final_columns = oracle_mgr.execute_query(verify_query)
        print("\n📊 الأعمدة النهائية:")
        for col in final_columns:
            nullable = "NULL" if col[2] == 'Y' else "NOT NULL"
            precision = f"({col[3]},{col[4]})" if col[3] else ""
            print(f"  {col[0]:<15} {col[1]}{precision:<10} {nullable}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث الأعمدة: {e}")
        return False

if __name__ == "__main__":
    update_container_weight_columns()
