"""
إعدادات WhatsApp Business API
"""

import os
from typing import Dict, Any

class WhatsAppConfig:
    """إعدادات WhatsApp Business API"""
    
    # إعدادات API الأساسية
    API_URL = os.getenv('WHATSAPP_API_URL', 'https://graph.facebook.com/v18.0')
    ACCESS_TOKEN = os.getenv('WHATSAPP_ACCESS_TOKEN', '')
    PHONE_NUMBER_ID = os.getenv('WHATSAPP_PHONE_NUMBER_ID', '')
    BUSINESS_ACCOUNT_ID = os.getenv('WHATSAPP_BUSINESS_ACCOUNT_ID', '')
    
    # إعدادات التطوير والاختبار
    TEST_MODE = os.getenv('WHATSAPP_TEST_MODE', 'true').lower() == 'true'
    
    # إعدادات الرسائل
    MESSAGE_TIMEOUT = int(os.getenv('WHATSAPP_MESSAGE_TIMEOUT', '30'))
    MAX_RETRIES = int(os.getenv('WHATSAPP_MAX_RETRIES', '3'))
    
    # إعدادات التحقق
    WEBHOOK_VERIFY_TOKEN = os.getenv('WHATSAPP_WEBHOOK_VERIFY_TOKEN', '')
    WEBHOOK_URL = os.getenv('WHATSAPP_WEBHOOK_URL', '')
    
    @classmethod
    def is_configured(cls) -> bool:
        """التحقق من تكوين الإعدادات الأساسية"""
        return bool(cls.ACCESS_TOKEN and cls.PHONE_NUMBER_ID)
    
    @classmethod
    def get_config_dict(cls) -> Dict[str, Any]:
        """الحصول على الإعدادات كقاموس"""
        return {
            'api_url': cls.API_URL,
            'access_token': cls.ACCESS_TOKEN,
            'phone_number_id': cls.PHONE_NUMBER_ID,
            'business_account_id': cls.BUSINESS_ACCOUNT_ID,
            'test_mode': cls.TEST_MODE,
            'message_timeout': cls.MESSAGE_TIMEOUT,
            'max_retries': cls.MAX_RETRIES,
            'webhook_verify_token': cls.WEBHOOK_VERIFY_TOKEN,
            'webhook_url': cls.WEBHOOK_URL,
            'is_configured': cls.is_configured()
        }


# قوالب الرسائل المعتمدة
WHATSAPP_TEMPLATES = {
    'delivery_order_notification': {
        'name': 'delivery_order_notification',
        'language': 'ar',
        'category': 'TRANSACTIONAL',
        'components': [
            {
                'type': 'HEADER',
                'format': 'TEXT',
                'text': 'أمر تسليم جديد'
            },
            {
                'type': 'BODY',
                'text': 'مرحباً {{1}},\n\nلديك أمر تسليم جديد:\n\n📋 رقم الأمر: {{2}}\n📦 رقم التتبع: {{3}}\n📍 موقع التسليم: {{4}}\n📅 التاريخ المتوقع: {{5}}\n\nيرجى مراجعة التفاصيل والرد بالموافقة.'
            },
            {
                'type': 'FOOTER',
                'text': 'نظام إدارة الشحنات'
            },
            {
                'type': 'BUTTONS',
                'buttons': [
                    {
                        'type': 'URL',
                        'text': 'عرض التفاصيل',
                        'url': '{{6}}'
                    }
                ]
            }
        ]
    },
    
    'delivery_status_update': {
        'name': 'delivery_status_update',
        'language': 'ar',
        'category': 'TRANSACTIONAL',
        'components': [
            {
                'type': 'HEADER',
                'format': 'TEXT',
                'text': 'تحديث حالة التسليم'
            },
            {
                'type': 'BODY',
                'text': 'مرحباً {{1}},\n\nتم تحديث حالة أمر التسليم {{2}}:\n\n🔄 الحالة الجديدة: {{3}}\n📅 تاريخ التحديث: {{4}}\n\n{{5}}'
            },
            {
                'type': 'FOOTER',
                'text': 'نظام إدارة الشحنات'
            }
        ]
    }
}

# رسائل الحالة
STATUS_MESSAGES = {
    'ar': {
        'sent': 'تم الإرسال',
        'delivered': 'تم التسليم',
        'read': 'تم القراءة',
        'failed': 'فشل الإرسال',
        'pending': 'قيد الإرسال'
    },
    'en': {
        'sent': 'Sent',
        'delivered': 'Delivered', 
        'read': 'Read',
        'failed': 'Failed',
        'pending': 'Pending'
    }
}

# أرقام الاختبار (لا ترسل رسائل حقيقية)
TEST_PHONE_NUMBERS = [
    '966500000000',
    '966511111111',
    '966522222222'
]

def get_whatsapp_config() -> Dict[str, Any]:
    """الحصول على إعدادات WhatsApp"""
    return WhatsAppConfig.get_config_dict()

def is_test_phone_number(phone: str) -> bool:
    """التحقق من كون الرقم رقم اختبار"""
    clean_phone = ''.join(c for c in phone if c.isdigit())
    return clean_phone in TEST_PHONE_NUMBERS
