{% extends "base.html" %}

{% block content %}
<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left side - Registration Form -->
        <div class="col-lg-7 d-flex align-items-center justify-content-center">
            <div class="w-100" style="max-width: 600px;">
                <div class="text-center mb-4">
                    <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
                    <h2 class="fw-bold">إنشاء حساب جديد</h2>
                    <p class="text-muted">انضم إلى النظام المحاسبي المتقدم</p>
                </div>
                
                <div class="card shadow-lg border-0">
                    <div class="card-body p-4">
                        <form method="POST" class="needs-validation" novalidate>
                            {{ form.hidden_tag() }}
                            
                            <div class="row">
                                <!-- Username and Email -->
                                <div class="col-md-6 mb-3">
                                    {{ form.username.label(class="form-label fw-semibold") }}
                                    {{ form.username(class="form-control") }}
                                    {% if form.username.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.username.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    {{ form.email.label(class="form-label fw-semibold") }}
                                    {{ form.email(class="form-control") }}
                                    {% if form.email.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.email.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <!-- Full Name -->
                                <div class="col-md-12 mb-3">
                                    {{ form.full_name.label(class="form-label fw-semibold") }}
                                    {{ form.full_name(class="form-control") }}
                                    {% if form.full_name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.full_name.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <!-- Department and Position -->
                                <div class="col-md-6 mb-3">
                                    {{ form.department.label(class="form-label fw-semibold") }}
                                    {{ form.department(class="form-control") }}
                                    {% if form.department.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.department.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    {{ form.position.label(class="form-label fw-semibold") }}
                                    {{ form.position(class="form-control") }}
                                    {% if form.position.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.position.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <!-- Phone -->
                                <div class="col-md-12 mb-3">
                                    {{ form.phone.label(class="form-label fw-semibold") }}
                                    {{ form.phone(class="form-control") }}
                                    {% if form.phone.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.phone.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <!-- Passwords -->
                                <div class="col-md-6 mb-3">
                                    {{ form.password.label(class="form-label fw-semibold") }}
                                    <div class="input-group">
                                        {{ form.password(class="form-control", id="password") }}
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye" id="eyeIcon"></i>
                                        </button>
                                    </div>
                                    {% if form.password.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.password.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">
                                        <small>يجب أن تكون كلمة المرور 6 أحرف على الأقل</small>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    {{ form.password2.label(class="form-label fw-semibold") }}
                                    <div class="input-group">
                                        {{ form.password2(class="form-control", id="password2") }}
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword2">
                                            <i class="fas fa-eye" id="eyeIcon2"></i>
                                        </button>
                                    </div>
                                    {% if form.password2.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.password2.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- Password Strength Indicator -->
                            <div class="mb-3">
                                <div class="password-strength">
                                    <div class="progress" style="height: 5px;">
                                        <div class="progress-bar" id="passwordStrengthBar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small class="text-muted" id="passwordStrengthText">قوة كلمة المرور</small>
                                </div>
                            </div>
                            
                            <!-- Terms and Conditions -->
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="termsCheck" required>
                                <label class="form-check-label" for="termsCheck">
                                    أوافق على <a href="#" class="text-primary">الشروط والأحكام</a> و <a href="#" class="text-primary">سياسة الخصوصية</a>
                                </label>
                                <div class="invalid-feedback">
                                    يجب الموافقة على الشروط والأحكام
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                {{ form.submit(class="btn btn-primary btn-lg") }}
                            </div>
                        </form>
                        
                        <div class="text-center mt-4">
                            <p class="mb-0">لديك حساب بالفعل؟ 
                                <a href="{{ url_for('auth.login') }}" class="text-primary text-decoration-none fw-semibold">
                                    تسجيل الدخول
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right side - Welcome Info -->
        <div class="col-lg-5 d-none d-lg-flex align-items-center justify-content-center" 
             style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="text-center text-white p-5">
                <i class="fas fa-users fa-5x mb-4 opacity-75"></i>
                <h1 class="display-5 fw-bold mb-4">انضم إلى فريقنا</h1>
                <p class="lead mb-4">
                    احصل على حساب مجاني واستمتع بجميع مميزات النظام المحاسبي المتقدم
                </p>
                
                <div class="row text-center mt-5">
                    <div class="col-12 mb-4">
                        <div class="d-flex align-items-center justify-content-center mb-3">
                            <i class="fas fa-shield-alt fa-2x me-3"></i>
                            <div class="text-start">
                                <h6 class="mb-0">أمان عالي</h6>
                                <small>حماية متقدمة لبياناتك</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-12 mb-4">
                        <div class="d-flex align-items-center justify-content-center mb-3">
                            <i class="fas fa-clock fa-2x me-3"></i>
                            <div class="text-start">
                                <h6 class="mb-0">متاح 24/7</h6>
                                <small>وصول دائم للنظام</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-12 mb-4">
                        <div class="d-flex align-items-center justify-content-center mb-3">
                            <i class="fas fa-headset fa-2x me-3"></i>
                            <div class="text-start">
                                <h6 class="mb-0">دعم فني</h6>
                                <small>مساعدة متخصصة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password visibility toggles
    const togglePassword = document.getElementById('togglePassword');
    const password = document.getElementById('password');
    const eyeIcon = document.getElementById('eyeIcon');
    
    const togglePassword2 = document.getElementById('togglePassword2');
    const password2 = document.getElementById('password2');
    const eyeIcon2 = document.getElementById('eyeIcon2');
    
    togglePassword.addEventListener('click', function() {
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
        password.setAttribute('type', type);
        
        if (type === 'password') {
            eyeIcon.classList.remove('fa-eye-slash');
            eyeIcon.classList.add('fa-eye');
        } else {
            eyeIcon.classList.remove('fa-eye');
            eyeIcon.classList.add('fa-eye-slash');
        }
    });
    
    togglePassword2.addEventListener('click', function() {
        const type = password2.getAttribute('type') === 'password' ? 'text' : 'password';
        password2.setAttribute('type', type);
        
        if (type === 'password') {
            eyeIcon2.classList.remove('fa-eye-slash');
            eyeIcon2.classList.add('fa-eye');
        } else {
            eyeIcon2.classList.remove('fa-eye');
            eyeIcon2.classList.add('fa-eye-slash');
        }
    });
    
    // Password strength checker
    const passwordStrengthBar = document.getElementById('passwordStrengthBar');
    const passwordStrengthText = document.getElementById('passwordStrengthText');
    
    password.addEventListener('input', function() {
        const value = this.value;
        let strength = 0;
        let text = 'ضعيف';
        let color = 'bg-danger';
        
        // Length check
        if (value.length >= 6) strength += 25;
        if (value.length >= 8) strength += 25;
        
        // Character variety checks
        if (/[a-z]/.test(value)) strength += 12.5;
        if (/[A-Z]/.test(value)) strength += 12.5;
        if (/[0-9]/.test(value)) strength += 12.5;
        if (/[^A-Za-z0-9]/.test(value)) strength += 12.5;
        
        // Update strength indicator
        if (strength >= 75) {
            text = 'قوي جداً';
            color = 'bg-success';
        } else if (strength >= 50) {
            text = 'قوي';
            color = 'bg-info';
        } else if (strength >= 25) {
            text = 'متوسط';
            color = 'bg-warning';
        }
        
        passwordStrengthBar.style.width = strength + '%';
        passwordStrengthBar.className = 'progress-bar ' + color;
        passwordStrengthText.textContent = text;
    });
    
    // Password match checker
    function checkPasswordMatch() {
        if (password2.value && password.value !== password2.value) {
            password2.setCustomValidity('كلمات المرور غير متطابقة');
            password2.classList.add('is-invalid');
        } else {
            password2.setCustomValidity('');
            password2.classList.remove('is-invalid');
        }
    }
    
    password.addEventListener('input', checkPasswordMatch);
    password2.addEventListener('input', checkPasswordMatch);
    
    // Form validation
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        
        form.classList.add('was-validated');
    });
    
    // Auto-focus on first field
    document.getElementById('username').focus();
    
    // Real-time validation feedback
    const inputs = form.querySelectorAll('input[required]');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
        
        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid') && this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });
});
</script>

<style>
.password-strength {
    margin-top: 5px;
}

.form-control.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.8-.8-.8-.8 1.4-******* 1.4-*******-.*******-1.4 1.4-.8-.8-1.4 1.4-.8-.8.8-.8-.8-.8z'/%3e%3c/svg%3e");
}

.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 0;
    }
    
    .col-lg-7 {
        padding: 2rem 1rem;
    }
}
</style>
{% endblock %}
