# 📊 النموذج الجدولي الاحترافي - Excel-like Table Component

## 🎯 نظرة عامة

مكون جدولي احترافي يوفر تجربة مشابهة تماماً لبرنامج Microsoft Excel داخل تطبيق الويب، مطور خصيصاً للتطبيقات العربية مع دعم كامل لـ RTL.

## ✨ المميزات الرئيسية

- 🔍 **بحث تلقائي** - بحث فوري في الأصناف أثناء الكتابة
- 📊 **حسابات تلقائية** - حساب الإجماليات والإحصائيات مباشرة
- ⌨️ **اختصارات لوحة المفاتيح** - تجربة مشابهة لـ Excel
- 💾 **حفظ تلقائي** - حفظ المسودات في localStorage
- 📱 **تصميم متجاوب** - يعمل على جميع الأجهزة
- 🌐 **دعم العربية** - دعم كامل لـ RTL والخطوط العربية
- 🎨 **قابل للتخصيص** - سهولة تخصيص الأعمدة والألوان
- 📋 **نسخ ولصق** - دعم كامل للنسخ واللصق من/إلى Excel

## 🛠️ التثبيت والإعداد

### 1. المتطلبات الأساسية:

```html
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- Handsontable -->
<link href="https://cdn.jsdelivr.net/npm/handsontable@12.4.0/dist/handsontable.full.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/handsontable@12.4.0/dist/handsontable.full.min.js"></script>

<!-- Bootstrap (اختياري) -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

<!-- Font Awesome (اختياري) -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
```

### 2. تضمين المكون:

```html
<script src="excel-table-component.js"></script>
```

## 🚀 الاستخدام السريع

### 1. HTML الأساسي:

```html
<div id="myTable"></div>
<div id="statistics"></div>
```

### 2. JavaScript:

```javascript
// إنشاء المكون
const table = new ExcelTableComponent('#myTable', {
    searchAPI: '/api/items/search',
    saveAPI: '/api/items/save',
    initialRows: 50
});

// الاستماع للأحداث
document.addEventListener('excelTable:statisticsUpdated', function(e) {
    const { totalRows, completedRows, totalQuantity, totalAmount } = e.detail;
    
    document.getElementById('statistics').innerHTML = `
        <p>إجمالي الصفوف: ${totalRows}</p>
        <p>صفوف مكتملة: ${completedRows}</p>
        <p>إجمالي الكمية: ${totalQuantity.toFixed(3)}</p>
        <p>إجمالي المبلغ: ${totalAmount.toFixed(3)} ر.س</p>
    `;
});

document.addEventListener('excelTable:saveSuccess', function(e) {
    alert('تم الحفظ بنجاح!');
});
```

## ⚙️ خيارات التكوين

```javascript
const options = {
    // الأعمدة المخصصة
    columns: [
        { key: 'code', title: 'رقم الصنف', type: 'autocomplete', width: 120 },
        { key: 'name', title: 'اسم الصنف', type: 'text', readOnly: true, width: 200 },
        { key: 'quantity', title: 'الكمية', type: 'numeric', width: 100 },
        // ... المزيد من الأعمدة
    ],
    
    // الإعدادات العامة
    initialRows: 20,           // عدد الصفوف الأولية
    height: 500,               // ارتفاع الجدول
    autoSave: true,            // الحفظ التلقائي
    autoSaveInterval: 30000,   // فترة الحفظ التلقائي (بالميلي ثانية)
    
    // APIs
    searchAPI: '/api/search',  // API البحث
    saveAPI: '/api/save',      // API الحفظ
    
    // اللغة والاتجاه
    rtl: true,                 // دعم RTL
    language: 'ar'             // اللغة
};

const table = new ExcelTableComponent('#container', options);
```

## 🎛️ الوظائف المتاحة

### إدارة البيانات:
```javascript
// إضافة صفوف
table.addRows(10);

// مسح المحدد
table.clearSelected();

// حذف الصفوف المحددة
table.deleteSelectedRows();

// حفظ البيانات
table.save();

// تحميل البيانات
table.loadData([
    { code: 'ITEM001', name: 'لابتوب', quantity: 5, price: 2500 },
    { code: 'ITEM002', name: 'طابعة', quantity: 2, price: 800 }
]);

// الحصول على البيانات الصحيحة
const validData = table.getValidData();
```

## 📡 الأحداث المتاحة

```javascript
// تحديث الإحصائيات
document.addEventListener('excelTable:statisticsUpdated', function(e) {
    console.log('إحصائيات جديدة:', e.detail);
});

// تغيير البيانات
document.addEventListener('excelTable:dataChanged', function(e) {
    console.log('تم تغيير البيانات:', e.detail.changes);
});

// نجاح الحفظ
document.addEventListener('excelTable:saveSuccess', function(e) {
    console.log('تم الحفظ بنجاح:', e.detail);
});

// خطأ في الحفظ
document.addEventListener('excelTable:saveError', function(e) {
    console.log('خطأ في الحفظ:', e.detail);
});

// إضافة صفوف
document.addEventListener('excelTable:rowsAdded', function(e) {
    console.log('تم إضافة صفوف:', e.detail.count);
});

// حذف صفوف
document.addEventListener('excelTable:rowsDeleted', function(e) {
    console.log('تم حذف صفوف:', e.detail.count);
});
```

## ⌨️ اختصارات لوحة المفاتيح

| الاختصار | الوظيفة |
|----------|---------|
| `Tab` | الانتقال للخلية التالية |
| `Shift + Tab` | الانتقال للخلية السابقة |
| `Enter` | الانتقال للصف التالي |
| `Ctrl + C` | نسخ |
| `Ctrl + V` | لصق |
| `Ctrl + Z` | تراجع |
| `Delete` | حذف المحتوى |
| `F2` | تحرير الخلية |
| `Ctrl + S` | حفظ البيانات |
| `Ctrl + Shift + A` | إضافة 10 صفوف |

## 🎨 التخصيص

### تخصيص الألوان:
```css
/* خلايا للقراءة فقط */
.readonly-cell {
    background-color: #f8f9fa !important;
    color: #6c757d;
}

/* خلايا محسوبة */
.calculated-cell {
    background-color: #e3f2fd !important;
    font-weight: 600;
}

/* خلايا غير صحيحة */
.handsontable .htInvalid {
    background-color: #ffebee !important;
    color: #c62828;
}
```

### تخصيص الأعمدة:
```javascript
const customColumns = [
    {
        key: 'product_id',
        title: 'معرف المنتج',
        type: 'text',
        width: 100
    },
    {
        key: 'category',
        title: 'الفئة',
        type: 'dropdown',
        source: ['إلكترونيات', 'مكتبية', 'منزلية'],
        width: 120
    },
    {
        key: 'discount',
        title: 'الخصم %',
        type: 'numeric',
        numericFormat: { pattern: '0.00%' },
        width: 100
    }
];
```

## 🔧 Backend API

### API البحث:
```python
@app.route('/api/items/search')
def search_items():
    query = request.args.get('q', '')
    
    # البحث في قاعدة البيانات
    items = Item.query.filter(
        Item.code.ilike(f'%{query}%') |
        Item.name.ilike(f'%{query}%')
    ).limit(50).all()
    
    return jsonify([{
        'id': item.id,
        'code': item.code,
        'name': item.name,
        'unit': item.unit,
        'price': float(item.price)
    } for item in items])
```

### API الحفظ:
```python
@app.route('/api/items/save', methods=['POST'])
def save_items():
    data = request.get_json()
    
    try:
        for item_data in data['items']:
            # حفظ البيانات في قاعدة البيانات
            item = ItemEntry(
                code=item_data['code'],
                name=item_data['name'],
                quantity=item_data['quantity'],
                price=item_data['price']
            )
            db.session.add(item)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم الحفظ بنجاح',
            'count': len(data['items'])
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500
```

## 📱 الاستجابة للشاشات المختلفة

```css
@media (max-width: 768px) {
    .handsontable {
        font-size: 12px;
    }
    
    .handsontable .htCore {
        min-width: 100%;
    }
}

@media (max-width: 480px) {
    .handsontable {
        font-size: 11px;
    }
}
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

1. **الجدول لا يظهر:**
   - تأكد من تحميل جميع المكتبات المطلوبة
   - تحقق من وجود العنصر المحدد في DOM

2. **البحث التلقائي لا يعمل:**
   - تحقق من صحة API endpoint
   - تأكد من إرجاع البيانات بالتنسيق الصحيح

3. **مشاكل RTL:**
   - أضف `dir="rtl"` للحاوي الرئيسي
   - تأكد من تحميل الخطوط العربية

## 📚 أمثلة إضافية

يمكنك العثور على أمثلة كاملة في:
- `Quick_Implementation_Template.html` - مثال كامل للاستخدام
- `Excel_Table_Component_Guide.md` - دليل شامل مفصل

## 📄 الترخيص

هذا المكون مطور للاستخدام في النظام المحاسبي السعودي.
للاستخدام التجاري، يرجى مراجعة ترخيص Handsontable.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى إنشاء Pull Request أو فتح Issue للاقتراحات والتحسينات.

---

**تم التطوير بواسطة:** فريق النظام المحاسبي السعودي  
**آخر تحديث:** أغسطس 2025
