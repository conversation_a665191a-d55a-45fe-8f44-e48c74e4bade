#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
اختبار نهائي لنظام العملات
Final Test for Currencies System
"""

import requests
import json
from datetime import datetime

def test_currencies_endpoints():
    """اختبار نقاط النهاية لنظام العملات"""
    
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 اختبار نقاط النهاية لنظام العملات...")
    print("=" * 50)
    
    # 1. اختبار الصفحة الرئيسية للعملات
    print("\n1️⃣ اختبار الصفحة الرئيسية للعملات:")
    try:
        response = requests.get(f"{base_url}/currencies/", timeout=10)
        print(f"   • Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ صفحة العملات تعمل بنجاح")
            
            # التحقق من وجود محتوى مهم
            content = response.text
            if "إدارة العملات" in content:
                print("   ✅ العنوان موجود")
            if "ريال سعودي" in content:
                print("   ✅ بيانات العملات موجودة")
            if "إحصائيات" in content or "stats" in content:
                print("   ✅ الإحصائيات موجودة")
                
        elif response.status_code == 302:
            print("   🔄 تم إعادة التوجيه (ربما للتسجيل)")
            print("   ℹ️  تحتاج لتسجيل الدخول أولاً")
        else:
            print(f"   ❌ خطأ: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
    
    # 2. اختبار API العملات
    print("\n2️⃣ اختبار API العملات:")
    try:
        response = requests.get(f"{base_url}/currencies/api/currencies", timeout=10)
        print(f"   • Status Code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('success'):
                    currencies = data.get('currencies', [])
                    print(f"   ✅ API يعمل بنجاح - {len(currencies)} عملة")
                    
                    # عرض بعض العملات
                    for currency in currencies[:3]:
                        print(f"      • {currency.get('code')}: {currency.get('name_ar')}")
                else:
                    print(f"   ❌ API فشل: {data.get('message')}")
            except json.JSONDecodeError:
                print("   ❌ استجابة غير صحيحة من API")
        else:
            print(f"   ❌ خطأ في API: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ خطأ في الاتصال مع API: {e}")
    
    # 3. اختبار محول العملات
    print("\n3️⃣ اختبار محول العملات:")
    try:
        params = {
            'amount': 100,
            'from_currency': 'SAR',
            'to_currency': 'USD'
        }
        response = requests.get(f"{base_url}/currencies/api/convert", params=params, timeout=10)
        print(f"   • Status Code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('success'):
                    print(f"   ✅ محول العملات يعمل")
                    print(f"      • {params['amount']} {params['from_currency']} = {data.get('converted_amount', 0):.2f} {params['to_currency']}")
                else:
                    print(f"   ❌ محول العملات فشل: {data.get('message')}")
            except json.JSONDecodeError:
                print("   ❌ استجابة غير صحيحة من محول العملات")
        else:
            print(f"   ❌ خطأ في محول العملات: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ خطأ في الاتصال مع محول العملات: {e}")
    
    # 4. اختبار صفحة أسعار الصرف
    print("\n4️⃣ اختبار صفحة أسعار الصرف:")
    try:
        response = requests.get(f"{base_url}/currencies/exchange-rates", timeout=10)
        print(f"   • Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ صفحة أسعار الصرف تعمل بنجاح")
        elif response.status_code == 302:
            print("   🔄 تم إعادة التوجيه (ربما للتسجيل)")
        else:
            print(f"   ❌ خطأ: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 انتهى اختبار نقاط النهاية")

def print_access_info():
    """طباعة معلومات الوصول"""
    print("\n🔗 معلومات الوصول لنظام العملات:")
    print("=" * 50)
    print("📍 الصفحة الرئيسية:")
    print("   http://127.0.0.1:5000/currencies/")
    print("\n📍 أسعار الصرف:")
    print("   http://127.0.0.1:5000/currencies/exchange-rates")
    print("\n📍 API العملات:")
    print("   http://127.0.0.1:5000/currencies/api/currencies")
    print("\n📍 محول العملات:")
    print("   http://127.0.0.1:5000/currencies/api/convert?amount=100&from_currency=SAR&to_currency=USD")
    print("\n📍 الوصول من الشريط الجانبي:")
    print("   الإعدادات → إدارة العملات")
    print("=" * 50)

def print_summary():
    """طباعة ملخص النظام"""
    print("\n📋 ملخص نظام إدارة العملات:")
    print("=" * 50)
    print("✅ تم إنشاء جداول العملات في Oracle")
    print("✅ تم إدراج 13 عملة أساسية")
    print("✅ تم إنشاء واجهات المستخدم")
    print("✅ تم إنشاء APIs للعملات")
    print("✅ تم إضافة محول العملات")
    print("✅ تم التكامل مع النظام الرئيسي")
    print("✅ تم إضافة الرابط في الشريط الجانبي")
    print("\n🌟 الميزات المتاحة:")
    print("   • إدارة العملات (إضافة، تعديل، حذف)")
    print("   • أسعار الصرف وتحديثها")
    print("   • محول العملات التفاعلي")
    print("   • تنسيق المبالغ حسب العملة")
    print("   • تاريخ تغييرات أسعار الصرف")
    print("   • إحصائيات شاملة")
    print("=" * 50)

if __name__ == '__main__':
    print("🚀 الاختبار النهائي لنظام العملات")
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # تشغيل الاختبارات
    test_currencies_endpoints()
    
    # طباعة معلومات الوصول
    print_access_info()
    
    # طباعة الملخص
    print_summary()
    
    print("\n🎉 نظام إدارة العملات جاهز للاستخدام!")
