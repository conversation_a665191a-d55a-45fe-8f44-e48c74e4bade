-- =====================================================
-- نظام الحوالات المتكامل - قاعدة البيانات
-- Integrated Money Transfer System - Database Schema
-- =====================================================

-- 1. جدول الصرافين والبنوك
CREATE TABLE money_changers_banks (
    id NUMBER PRIMARY KEY,
    name VARCHAR2(200) NOT NULL,
    type VARCHAR2(20) CHECK (type IN ('bank', 'money_changer')) NOT NULL,
    branch_id NUMBER,
    contact_person VARCHAR2(100),
    phone VARCHAR2(20),
    email VARCHAR2(100),
    address VARCHAR2(500),
    account_details CLOB,
    commission_rate NUMBER(5,2) DEFAULT 0,
    is_active NUMBER(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER
);

-- إنشاء sequence للجدول
CREATE SEQUENCE money_changers_banks_seq START WITH 1 INCREMENT BY 1;

-- إنشاء trigger للـ auto increment
CREATE OR REPLACE TRIGGER money_changers_banks_trigger
    BEFORE INSERT ON money_changers_banks
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := money_changers_banks_seq.NEXTVAL;
    END IF;
END;

-- إنشاء فهارس
CREATE INDEX idx_money_changers_name ON money_changers_banks(name);
CREATE INDEX idx_money_changers_type ON money_changers_banks(type);
CREATE INDEX idx_money_changers_active ON money_changers_banks(is_active);

-- 2. جدول المستفيدين
CREATE TABLE beneficiaries (
    id NUMBER PRIMARY KEY,
    beneficiary_name VARCHAR2(200) NOT NULL,
    beneficiary_address VARCHAR2(500),
    type VARCHAR2(20) CHECK (type IN ('supplier', 'vendor', 'individual')) NOT NULL,
    supplier_id NUMBER,
    bank_account VARCHAR2(50) NOT NULL,
    bank_name VARCHAR2(200) NOT NULL,
    bank_branch VARCHAR2(200),
    bank_country VARCHAR2(100),
    iban VARCHAR2(50),
    swift_code VARCHAR2(20),
    identification_number VARCHAR2(50),
    phone VARCHAR2(20),
    email VARCHAR2(100),
    is_active NUMBER(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER
);

-- إنشاء sequence للجدول
CREATE SEQUENCE beneficiaries_seq START WITH 1 INCREMENT BY 1;

-- إنشاء trigger للـ auto increment
CREATE OR REPLACE TRIGGER beneficiaries_trigger
    BEFORE INSERT ON beneficiaries
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := beneficiaries_seq.NEXTVAL;
    END IF;
END;

-- إنشاء فهارس
CREATE INDEX idx_beneficiaries_name ON beneficiaries(beneficiary_name);
CREATE INDEX idx_beneficiaries_account ON beneficiaries(bank_account);
CREATE INDEX idx_beneficiaries_type ON beneficiaries(type);
CREATE INDEX idx_beneficiaries_active ON beneficiaries(is_active);
CREATE UNIQUE INDEX idx_beneficiaries_account_unique ON beneficiaries(bank_account);

-- 3. جدول طلبات الحوالات
CREATE TABLE transfer_requests (
    id NUMBER PRIMARY KEY,
    request_number VARCHAR2(50) UNIQUE NOT NULL,
    beneficiary_id NUMBER NOT NULL,
    amount NUMBER(15,2) NOT NULL,
    currency VARCHAR2(10) DEFAULT 'USD',
    purpose VARCHAR2(500),
    notes CLOB,
    requested_by NUMBER NOT NULL,
    branch_id NUMBER,
    status VARCHAR2(20) CHECK (status IN ('pending', 'approved', 'rejected', 'executed')) DEFAULT 'pending',
    approved_by NUMBER,
    approved_at TIMESTAMP,
    rejection_reason VARCHAR2(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء sequence للجدول
CREATE SEQUENCE transfer_requests_seq START WITH 1 INCREMENT BY 1;

-- إنشاء trigger للـ auto increment وتوليد رقم الطلب
CREATE OR REPLACE TRIGGER transfer_requests_trigger
    BEFORE INSERT ON transfer_requests
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := transfer_requests_seq.NEXTVAL;
    END IF;
    
    IF :NEW.request_number IS NULL THEN
        :NEW.request_number := 'TR' || TO_CHAR(SYSDATE, 'YYYY') || 
                              LPAD(transfer_requests_seq.CURRVAL, 6, '0');
    END IF;
END;

-- إنشاء فهارس
CREATE INDEX idx_transfer_requests_number ON transfer_requests(request_number);
CREATE INDEX idx_transfer_requests_beneficiary ON transfer_requests(beneficiary_id);
CREATE INDEX idx_transfer_requests_status ON transfer_requests(status);
CREATE INDEX idx_transfer_requests_date ON transfer_requests(created_at);

-- 4. جدول الحوالات المنفذة
CREATE TABLE transfers (
    id NUMBER PRIMARY KEY,
    transfer_number VARCHAR2(50) UNIQUE NOT NULL,
    request_id NUMBER NOT NULL,
    money_changer_bank_id NUMBER NOT NULL,
    beneficiary_id NUMBER NOT NULL,
    amount NUMBER(15,2) NOT NULL,
    currency VARCHAR2(10) DEFAULT 'USD',
    exchange_rate NUMBER(10,4) DEFAULT 1,
    commission NUMBER(10,2) DEFAULT 0,
    net_amount NUMBER(15,2),
    reference_number VARCHAR2(100),
    execution_date TIMESTAMP,
    executed_by NUMBER,
    status VARCHAR2(20) CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')) DEFAULT 'pending',
    notes CLOB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء sequence للجدول
CREATE SEQUENCE transfers_seq START WITH 1 INCREMENT BY 1;

-- إنشاء trigger للـ auto increment وتوليد رقم الحوالة
CREATE OR REPLACE TRIGGER transfers_trigger
    BEFORE INSERT ON transfers
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := transfers_seq.NEXTVAL;
    END IF;
    
    IF :NEW.transfer_number IS NULL THEN
        :NEW.transfer_number := 'TF' || TO_CHAR(SYSDATE, 'YYYY') || 
                               LPAD(transfers_seq.CURRVAL, 6, '0');
    END IF;
    
    -- حساب المبلغ الصافي
    IF :NEW.net_amount IS NULL THEN
        :NEW.net_amount := :NEW.amount - NVL(:NEW.commission, 0);
    END IF;
END;

-- إنشاء فهارس
CREATE INDEX idx_transfers_number ON transfers(transfer_number);
CREATE INDEX idx_transfers_request ON transfers(request_id);
CREATE INDEX idx_transfers_money_changer ON transfers(money_changer_bank_id);
CREATE INDEX idx_transfers_beneficiary ON transfers(beneficiary_id);
CREATE INDEX idx_transfers_status ON transfers(status);
CREATE INDEX idx_transfers_date ON transfers(execution_date);

-- 5. جدول تتبع الحوالات
CREATE TABLE transfer_tracking (
    id NUMBER PRIMARY KEY,
    transfer_id NUMBER NOT NULL,
    status VARCHAR2(50) NOT NULL,
    description VARCHAR2(500),
    updated_by NUMBER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء sequence للجدول
CREATE SEQUENCE transfer_tracking_seq START WITH 1 INCREMENT BY 1;

-- إنشاء trigger للـ auto increment
CREATE OR REPLACE TRIGGER transfer_tracking_trigger
    BEFORE INSERT ON transfer_tracking
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := transfer_tracking_seq.NEXTVAL;
    END IF;
END;

-- إنشاء فهارس
CREATE INDEX idx_transfer_tracking_transfer ON transfer_tracking(transfer_id);
CREATE INDEX idx_transfer_tracking_status ON transfer_tracking(status);
CREATE INDEX idx_transfer_tracking_date ON transfer_tracking(created_at);

-- 6. جدول أسعار الصرف
CREATE TABLE exchange_rates (
    id NUMBER PRIMARY KEY,
    from_currency VARCHAR2(10) NOT NULL,
    to_currency VARCHAR2(10) NOT NULL,
    rate NUMBER(10,4) NOT NULL,
    effective_date DATE DEFAULT SYSDATE,
    is_active NUMBER(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER
);

-- إنشاء sequence للجدول
CREATE SEQUENCE exchange_rates_seq START WITH 1 INCREMENT BY 1;

-- إنشاء trigger للـ auto increment
CREATE OR REPLACE TRIGGER exchange_rates_trigger
    BEFORE INSERT ON exchange_rates
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := exchange_rates_seq.NEXTVAL;
    END IF;
END;

-- إنشاء فهارس
CREATE INDEX idx_exchange_rates_currencies ON exchange_rates(from_currency, to_currency);
CREATE INDEX idx_exchange_rates_date ON exchange_rates(effective_date);
CREATE INDEX idx_exchange_rates_active ON exchange_rates(is_active);

-- إضافة المفاتيح الخارجية (Foreign Keys)
ALTER TABLE money_changers_banks ADD CONSTRAINT fk_money_changers_branch 
    FOREIGN KEY (branch_id) REFERENCES branches(id);

ALTER TABLE beneficiaries ADD CONSTRAINT fk_beneficiaries_supplier 
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id);

ALTER TABLE transfer_requests ADD CONSTRAINT fk_transfer_requests_beneficiary 
    FOREIGN KEY (beneficiary_id) REFERENCES beneficiaries(id);

ALTER TABLE transfer_requests ADD CONSTRAINT fk_transfer_requests_user 
    FOREIGN KEY (requested_by) REFERENCES users(id);

ALTER TABLE transfer_requests ADD CONSTRAINT fk_transfer_requests_approver 
    FOREIGN KEY (approved_by) REFERENCES users(id);

ALTER TABLE transfers ADD CONSTRAINT fk_transfers_request 
    FOREIGN KEY (request_id) REFERENCES transfer_requests(id);

ALTER TABLE transfers ADD CONSTRAINT fk_transfers_money_changer 
    FOREIGN KEY (money_changer_bank_id) REFERENCES money_changers_banks(id);

ALTER TABLE transfers ADD CONSTRAINT fk_transfers_beneficiary 
    FOREIGN KEY (beneficiary_id) REFERENCES beneficiaries(id);

ALTER TABLE transfers ADD CONSTRAINT fk_transfers_executor 
    FOREIGN KEY (executed_by) REFERENCES users(id);

ALTER TABLE transfer_tracking ADD CONSTRAINT fk_transfer_tracking_transfer 
    FOREIGN KEY (transfer_id) REFERENCES transfers(id);

ALTER TABLE transfer_tracking ADD CONSTRAINT fk_transfer_tracking_user 
    FOREIGN KEY (updated_by) REFERENCES users(id);

-- إدراج بيانات تجريبية
INSERT INTO money_changers_banks (name, type, contact_person, phone, commission_rate) VALUES
('البنك الأهلي اليمني', 'bank', 'أحمد محمد', '************', 2.5);

INSERT INTO money_changers_banks (name, type, contact_person, phone, commission_rate) VALUES
('صرافة الحرمين', 'money_changer', 'محمد علي', '************', 1.5);

INSERT INTO beneficiaries (beneficiary_name, type, bank_account, bank_name, bank_country) VALUES
('شركة التجارة العالمية', 'supplier', '**********', 'البنك التجاري', 'الإمارات');

INSERT INTO beneficiaries (beneficiary_name, type, bank_account, bank_name, bank_country) VALUES
('علي أحمد محمد', 'individual', '**********', 'بنك دبي الإسلامي', 'الإمارات');

-- إدراج أسعار صرف تجريبية
INSERT INTO exchange_rates (from_currency, to_currency, rate) VALUES ('USD', 'YER', 250.00);
INSERT INTO exchange_rates (from_currency, to_currency, rate) VALUES ('YER', 'USD', 0.004);
INSERT INTO exchange_rates (from_currency, to_currency, rate) VALUES ('USD', 'AED', 3.67);
INSERT INTO exchange_rates (from_currency, to_currency, rate) VALUES ('AED', 'USD', 0.272);

COMMIT;
