#!/usr/bin/env python3
"""
تطبيق تغييرات قاعدة البيانات لإضافة أعمدة روابط المشاركة
Apply database changes for share link columns
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oracle_manager import OracleManager

def apply_share_link_columns():
    """تطبيق تغييرات قاعدة البيانات لإضافة أعمدة روابط المشاركة"""
    
    oracle_manager = OracleManager()
    if not oracle_manager.connect():
        print("❌ فشل الاتصال بقاعدة البيانات")
        return False
    
    try:
        print("🔧 تطبيق تغييرات قاعدة البيانات لروابط المشاركة...")
        
        # قائمة الأعمدة المطلوب إضافتها
        columns_to_add = [
            {
                'name': 'SHARE_LINK',
                'type': 'VARCHAR2(500)',
                'comment': 'رابط المشاركة السحابي للوثيقة'
            },
            {
                'name': 'SHARE_SERVICE',
                'type': 'VARCHAR2(50)',
                'comment': 'اسم الخدمة السحابية (OneDrive, Nextcloud, etc.)'
            },
            {
                'name': 'SHARE_CREATED_AT',
                'type': 'DATE',
                'comment': 'تاريخ إنشاء رابط المشاركة'
            },
            {
                'name': 'SHARE_EXPIRES_AT',
                'type': 'DATE',
                'comment': 'تاريخ انتهاء صلاحية رابط المشاركة'
            }
        ]
        
        # فحص الأعمدة الموجودة
        print("🔍 فحص الأعمدة الموجودة...")
        
        existing_columns_query = """
            SELECT column_name
            FROM user_tab_columns
            WHERE table_name = 'CARGO_SHIPMENT_DOCUMENTS'
            AND column_name IN ('SHARE_LINK', 'SHARE_SERVICE', 'SHARE_CREATED_AT', 'SHARE_EXPIRES_AT')
        """
        
        existing_columns = oracle_manager.execute_query(existing_columns_query, [])
        existing_column_names = [col[0] for col in existing_columns] if existing_columns else []
        
        print(f"📋 الأعمدة الموجودة: {existing_column_names}")
        
        # إضافة الأعمدة المفقودة
        columns_added = 0
        
        for column in columns_to_add:
            if column['name'] not in existing_column_names:
                try:
                    alter_query = f"ALTER TABLE cargo_shipment_documents ADD {column['name']} {column['type']}"
                    oracle_manager.execute_update(alter_query, [])
                    print(f"✅ تم إضافة العمود: {column['name']}")
                    columns_added += 1
                    
                    # إضافة التعليق
                    comment_query = f"COMMENT ON COLUMN cargo_shipment_documents.{column['name']} IS '{column['comment']}'"
                    oracle_manager.execute_update(comment_query, [])
                    
                except Exception as e:
                    print(f"❌ خطأ في إضافة العمود {column['name']}: {e}")
            else:
                print(f"⚠️ العمود {column['name']} موجود مسبقاً")
        
        # إنشاء فهرس على عمود share_service
        try:
            index_query = """
                SELECT index_name 
                FROM user_indexes 
                WHERE table_name = 'CARGO_SHIPMENT_DOCUMENTS' 
                AND index_name = 'IDX_CARGO_DOCS_SHARE_SERVICE'
            """
            
            existing_index = oracle_manager.execute_query(index_query, [])
            
            if not existing_index:
                create_index_query = """
                    CREATE INDEX idx_cargo_docs_share_service 
                    ON cargo_shipment_documents(share_service)
                """
                oracle_manager.execute_update(create_index_query, [])
                print("✅ تم إنشاء فهرس share_service")
            else:
                print("⚠️ فهرس share_service موجود مسبقاً")
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء الفهرس: {e}")
        
        # فحص النتيجة النهائية
        print(f"\n📊 ملخص التغييرات:")
        print(f"  - أعمدة مضافة: {columns_added}")
        
        # فحص هيكل الجدول النهائي
        final_structure_query = """
            SELECT column_name, data_type, nullable
            FROM user_tab_columns
            WHERE table_name = 'CARGO_SHIPMENT_DOCUMENTS'
            AND column_name LIKE 'SHARE_%'
            ORDER BY column_name
        """
        
        final_columns = oracle_manager.execute_query(final_structure_query, [])
        
        if final_columns:
            print(f"\n📋 أعمدة المشاركة النهائية:")
            for col in final_columns:
                print(f"  - {col[0]} ({col[1]}) - {'NULL' if col[2] == 'Y' else 'NOT NULL'}")
        
        print("\n🎉 تم تطبيق جميع التغييرات بنجاح!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تطبيق التغييرات: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        pass

if __name__ == "__main__":
    print("🔧 تطبيق تغييرات قاعدة البيانات لروابط المشاركة")
    print("=" * 60)
    
    apply_share_link_columns()
