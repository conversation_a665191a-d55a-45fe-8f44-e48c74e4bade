<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة مندوبي المشتريات - نظام الفوجي</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/commission-opening-balances-style.css') }}" rel="stylesheet">
</head>
<body>

    <!-- Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="fas fa-users me-3"></i>
                        إدارة مندوبي المشتريات
                    </h1>
                    <p class="page-subtitle">
                        إضافة وتعديل وإدارة بيانات مندوبي المشتريات وتخصصاتهم وأهدافهم
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{{ url_for('purchase_commissions.index') }}" class="btn btn-light btn-modern">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="breadcrumb-container">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('purchase_commissions.index') }}">عمولات المندوبين</a></li>
                    <li class="breadcrumb-item active" aria-current="page">إدارة المندوبين</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="container">

        <!-- Control Panel -->
        <div class="control-panel">
            <h5>
                <i class="fas fa-cogs"></i>
                إجراءات سريعة
            </h5>
            <div class="row g-3">
                <div class="col-md-3">
                    <button type="button" class="btn btn-primary btn-modern w-100" data-bs-toggle="modal" data-bs-target="#addRepModal">
                        <i class="fas fa-plus"></i>
                        إضافة مندوب جديد
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-success btn-modern w-100" onclick="exportData()">
                        <i class="fas fa-download"></i>
                        تصدير البيانات
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-info btn-modern w-100" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث البيانات
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-warning btn-modern w-100" onclick="printReport()">
                        <i class="fas fa-print"></i>
                        طباعة التقرير
                    </button>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="stats-container">
            <div class="row g-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon primary">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-value">{{ representatives|length }}</div>
                        <div class="stat-label">إجمالي المندوبين</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon success">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-value">{{ representatives|selectattr('is_active', 'equalto', true)|list|length }}</div>
                        <div class="stat-label">المندوبين النشطين</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon info">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-value">{{ representatives|selectattr('commission_eligible', 'equalto', 1)|list|length }}</div>
                        <div class="stat-label">مؤهلين للعمولة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon warning">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="stat-value">{{ representatives|map(attribute='specialization')|unique|list|length }}</div>
                        <div class="stat-label">التخصصات المختلفة</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Data Table -->
        <div class="data-table-container">
            <div class="data-table-header">
                <h5 class="data-table-title">
                    <i class="fas fa-table"></i>
                    قائمة المندوبين
                </h5>
                <div>
                    <button class="btn btn-primary btn-modern btn-sm" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-modern" id="representativesTable">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>الهاتف</th>
                            <th>التخصص</th>
                            <th>الحالة</th>
                            <th>مؤهل للعمولة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for rep in representatives %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon primary me-2" style="width: 40px; height: 40px; font-size: 1rem;">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div>
                                        <strong>{{ rep.rep_name }}</strong>
                                        {% if rep.email %}
                                        <br><small class="text-muted">{{ rep.email }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>{{ rep.phone or '-' }}</td>
                            <td>
                                <span class="badge badge-modern bg-info">{{ rep.specialization or 'عام' }}</span>
                            </td>
                            <td>
                                {% if rep.is_active %}
                                <span class="badge badge-modern bg-success">نشط</span>
                                {% else %}
                                <span class="badge badge-modern bg-secondary">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if rep.commission_eligible %}
                                <span class="badge badge-modern bg-primary">مؤهل</span>
                                {% else %}
                                <span class="badge badge-modern bg-warning">غير مؤهل</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('purchase_commissions.edit_representative', rep_id=rep.id) }}"
                                       class="btn btn-warning btn-modern btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ url_for('purchase_commissions.delete_representative', rep_id=rep.id) }}"
                                       class="btn btn-danger btn-modern btn-sm"
                                       onclick="return confirm('هل أنت متأكد من حذف المندوب {{ rep.rep_name }}؟')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>


    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function refreshData() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            button.disabled = true;

            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                showAlert('تم تحديث البيانات بنجاح', 'success');
            }, 2000);
        }

        function exportData() {
            showAlert('سيتم تصدير البيانات قريباً', 'info');
        }

        function printReport() {
            window.print();
        }



        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-modern alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'info' ? 'info-circle' : 'exclamation-triangle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('.container').insertBefore(alert, document.querySelector('.control-panel'));

            setTimeout(() => {
                alert.remove();
            }, 3000);
        }

        // تأثيرات hover للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(-5px)';
                });
            });
        });
    </script>

</body>
</html>
