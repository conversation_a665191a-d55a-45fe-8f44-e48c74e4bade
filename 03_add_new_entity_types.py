#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إضافة أنواع الكيانات الجديدة إلى ENTITY_TYPES
Add New Entity Types to ENTITY_TYPES Table
"""

import sys
import os
sys.path.insert(0, os.getcwd())

from oracle_manager import OracleManager

def check_existing_entity_types():
    """فحص أنواع الكيانات الموجودة"""
    
    oracle = OracleManager()
    
    print("🔍 فحص أنواع الكيانات الموجودة...")
    print("=" * 70)
    
    # فحص الأنواع الحالية
    query = """
    SELECT code, name_ar, name_en, is_active, created_date
    FROM ENTITY_TYPES
    ORDER BY code
    """
    
    result = oracle.execute_query(query)
    if result:
        print("أنواع الكيانات الحالية:")
        for row in result:
            status = "نشط" if row[3] == 1 else "غير نشط"
            print(f"   {row[0]}: {row[1]} ({row[2]}) - {status}")
    else:
        print("لا توجد أنواع كيانات")
    
    return result

def add_purchase_agent():
    """إضافة نوع كيان مندوبي المشتريات"""
    
    oracle = OracleManager()
    
    print("\n1️⃣ إضافة PURCHASE_AGENT...")
    print("-" * 50)
    
    # فحص إذا كان موجود
    check_query = "SELECT COUNT(*) FROM ENTITY_TYPES WHERE code = 'PURCHASE_AGENT'"
    exists = oracle.execute_query(check_query)
    
    if exists and exists[0][0] > 0:
        print("   ⚠️ PURCHASE_AGENT موجود مسبقاً")
        return True
    
    try:
        # الحصول على أكبر ID
        max_id_query = "SELECT NVL(MAX(id), 0) + 1 FROM ENTITY_TYPES"
        max_id_result = oracle.execute_query(max_id_query)
        new_id = max_id_result[0][0] if max_id_result else 1
        
        # إضافة النوع الجديد
        insert_query = """
        INSERT INTO ENTITY_TYPES (
            id, code, name_ar, name_en, description, 
            is_active, created_by, created_date, updated_by, updated_date
        ) VALUES (
            :id, 'PURCHASE_AGENT', 'مندوب مشتريات', 'Purchase Agent',
            'Purchase representative for procurement activities',
            1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP
        )
        """
        
        oracle.execute_update(insert_query, {"id": new_id})
        print(f"   ✅ تم إضافة PURCHASE_AGENT بـ ID: {new_id}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في إضافة PURCHASE_AGENT: {str(e)}")
        return False

def add_sales_agent():
    """إضافة نوع كيان مندوبي المبيعات"""
    
    oracle = OracleManager()
    
    print("\n2️⃣ إضافة SALES_AGENT...")
    print("-" * 50)
    
    # فحص إذا كان موجود
    check_query = "SELECT COUNT(*) FROM ENTITY_TYPES WHERE code = 'SALES_AGENT'"
    exists = oracle.execute_query(check_query)
    
    if exists and exists[0][0] > 0:
        print("   ⚠️ SALES_AGENT موجود مسبقاً")
        return True
    
    try:
        # الحصول على أكبر ID
        max_id_query = "SELECT NVL(MAX(id), 0) + 1 FROM ENTITY_TYPES"
        max_id_result = oracle.execute_query(max_id_query)
        new_id = max_id_result[0][0] if max_id_result else 1
        
        # إضافة النوع الجديد
        insert_query = """
        INSERT INTO ENTITY_TYPES (
            id, code, name_ar, name_en, description, 
            is_active, created_by, created_date, updated_by, updated_date
        ) VALUES (
            :id, 'SALES_AGENT', 'مندوب مبيعات', 'Sales Agent',
            'Sales representative for sales activities',
            1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP
        )
        """
        
        oracle.execute_update(insert_query, {"id": new_id})
        print(f"   ✅ تم إضافة SALES_AGENT بـ ID: {new_id}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في إضافة SALES_AGENT: {str(e)}")
        return False

def add_shipping_company():
    """إضافة نوع كيان شركات الشحن"""
    
    oracle = OracleManager()
    
    print("\n3️⃣ إضافة SHIPPING_COMPANY...")
    print("-" * 50)
    
    # فحص إذا كان موجود
    check_query = "SELECT COUNT(*) FROM ENTITY_TYPES WHERE code = 'SHIPPING_COMPANY'"
    exists = oracle.execute_query(check_query)
    
    if exists and exists[0][0] > 0:
        print("   ⚠️ SHIPPING_COMPANY موجود مسبقاً")
        return True
    
    try:
        # الحصول على أكبر ID
        max_id_query = "SELECT NVL(MAX(id), 0) + 1 FROM ENTITY_TYPES"
        max_id_result = oracle.execute_query(max_id_query)
        new_id = max_id_result[0][0] if max_id_result else 1
        
        # إضافة النوع الجديد
        insert_query = """
        INSERT INTO ENTITY_TYPES (
            id, code, name_ar, name_en, description, 
            is_active, created_by, created_date, updated_by, updated_date
        ) VALUES (
            :id, 'SHIPPING_COMPANY', 'شركة شحن', 'Shipping Company',
            'Shipping and logistics company for transportation services',
            1, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP
        )
        """
        
        oracle.execute_update(insert_query, {"id": new_id})
        print(f"   ✅ تم إضافة SHIPPING_COMPANY بـ ID: {new_id}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في إضافة SHIPPING_COMPANY: {str(e)}")
        return False

def verify_new_entity_types():
    """التحقق من أنواع الكيانات الجديدة"""
    
    oracle = OracleManager()
    
    print("\n✅ التحقق من أنواع الكيانات الجديدة...")
    print("=" * 70)
    
    # فحص الأنواع الجديدة
    new_types = ['PURCHASE_AGENT', 'SALES_AGENT', 'SHIPPING_COMPANY']
    
    query = """
    SELECT code, name_ar, name_en, description, is_active, created_date
    FROM ENTITY_TYPES
    WHERE code IN ('PURCHASE_AGENT', 'SALES_AGENT', 'SHIPPING_COMPANY')
    ORDER BY code
    """
    
    result = oracle.execute_query(query)
    if result:
        print("أنواع الكيانات الجديدة:")
        for row in result:
            status = "نشط" if row[4] == 1 else "غير نشط"
            print(f"   ✅ {row[0]}: {row[1]} ({row[2]})")
            print(f"      الوصف: {row[3]}")
            print(f"      الحالة: {status}")
            print(f"      تاريخ الإنشاء: {row[5]}")
            print()
    
    # إحصائيات
    total_query = "SELECT COUNT(*) FROM ENTITY_TYPES"
    total_count = oracle.execute_query(total_query)
    
    new_count_query = """
    SELECT COUNT(*) FROM ENTITY_TYPES 
    WHERE code IN ('PURCHASE_AGENT', 'SALES_AGENT', 'SHIPPING_COMPANY')
    """
    new_count = oracle.execute_query(new_count_query)
    
    if total_count and new_count:
        print(f"📊 إحصائيات:")
        print(f"   إجمالي أنواع الكيانات: {total_count[0][0]}")
        print(f"   الأنواع الجديدة المضافة: {new_count[0][0]}")
    
    return result

def create_sample_entities():
    """إنشاء عينات من الكيانات الجديدة للاختبار"""
    
    oracle = OracleManager()
    
    print("\n🧪 إنشاء عينات للاختبار...")
    print("=" * 70)
    
    # فحص إذا كانت الجداول الفرعية موجودة
    tables_to_check = [
        ('PURCHASE_AGENTS', 'PURCHASE_AGENT'),
        ('SALES_AGENTS', 'SALES_AGENT'),
        ('SHIPPING_COMPANIES', 'SHIPPING_COMPANY')
    ]
    
    for table_name, entity_type in tables_to_check:
        try:
            # فحص وجود الجدول
            check_table_query = """
            SELECT COUNT(*) FROM user_tables WHERE table_name = :table_name
            """
            table_exists = oracle.execute_query(check_table_query, {"table_name": table_name})
            
            if table_exists and table_exists[0][0] > 0:
                print(f"   ✅ جدول {table_name} موجود")
                
                # فحص عدد السجلات
                count_query = f"SELECT COUNT(*) FROM {table_name}"
                count_result = oracle.execute_query(count_query)
                if count_result:
                    print(f"      عدد السجلات: {count_result[0][0]}")
            else:
                print(f"   ⚠️ جدول {table_name} غير موجود - يمكن إنشاؤه لاحقاً")
                
        except Exception as e:
            print(f"   ❌ خطأ في فحص جدول {table_name}: {str(e)}")

def test_entity_types_integration():
    """اختبار تكامل أنواع الكيانات مع النظام"""
    
    oracle = OracleManager()
    
    print("\n🔗 اختبار التكامل مع النظام...")
    print("=" * 70)
    
    # اختبار إمكانية استخدام الأنواع الجديدة في BALANCE_TRANSACTIONS
    new_types = ['PURCHASE_AGENT', 'SALES_AGENT', 'SHIPPING_COMPANY']
    
    for entity_type in new_types:
        try:
            # محاولة إنشاء معاملة اختبار (بدون تنفيذ فعلي)
            test_query = """
            SELECT COUNT(*) FROM BALANCE_TRANSACTIONS 
            WHERE entity_type_code = :entity_type
            """
            
            result = oracle.execute_query(test_query, {"entity_type": entity_type})
            if result is not None:
                print(f"   ✅ {entity_type}: جاهز للاستخدام في BALANCE_TRANSACTIONS")
                print(f"      المعاملات الحالية: {result[0][0]}")
            
        except Exception as e:
            print(f"   ❌ {entity_type}: خطأ في الاختبار - {str(e)}")

def generate_usage_examples():
    """إنشاء أمثلة على الاستخدام"""
    
    print("\n💡 أمثلة على الاستخدام:")
    print("=" * 70)
    
    examples = [
        {
            "type": "PURCHASE_AGENT",
            "name": "مندوب مشتريات",
            "usage": "تسجيل مصاريف ومكافآت مندوبي المشتريات"
        },
        {
            "type": "SALES_AGENT", 
            "name": "مندوب مبيعات",
            "usage": "تسجيل عمولات ومصاريف مندوبي المبيعات"
        },
        {
            "type": "SHIPPING_COMPANY",
            "name": "شركة شحن", 
            "usage": "تسجيل مصاريف الشحن والنقل"
        }
    ]
    
    for example in examples:
        print(f"   🎯 {example['type']} ({example['name']}):")
        print(f"      الاستخدام: {example['usage']}")
        print(f"      مثال: إنشاء رصيد افتتاحي أو ترحيل معاملات")
        print()

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء إضافة أنواع الكيانات الجديدة")
    print("=" * 80)
    
    try:
        # 1. فحص الأنواع الموجودة
        existing_types = check_existing_entity_types()
        
        # 2. إضافة الأنواع الجديدة
        success_count = 0
        
        if add_purchase_agent():
            success_count += 1
            
        if add_sales_agent():
            success_count += 1
            
        if add_shipping_company():
            success_count += 1
        
        if success_count > 0:
            # 3. التحقق من النتائج
            verify_new_entity_types()
            
            # 4. إنشاء عينات للاختبار
            create_sample_entities()
            
            # 5. اختبار التكامل
            test_entity_types_integration()
            
            # 6. أمثلة الاستخدام
            generate_usage_examples()
            
            print("\n🎉 تم إكمال إضافة أنواع الكيانات الجديدة بنجاح!")
            print(f"✅ تم إضافة {success_count} أنواع كيانات جديدة")
            print("✅ المهمة dchjpJRykotw4cHB7YuFy4 مكتملة!")
            
            return True
        else:
            print("\n❌ لم يتم إضافة أي أنواع كيانات جديدة")
            return False
            
    except Exception as e:
        print(f"\n❌ خطأ عام: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n" + "=" * 80)
        print("🏁 انتهى إضافة أنواع الكيانات بنجاح - جاهز للمهمة التالية!")
    else:
        print("\n" + "=" * 80)
        print("🚫 فشل في إضافة أنواع الكيانات - يرجى مراجعة الأخطاء")
