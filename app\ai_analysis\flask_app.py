"""
تطبيق Flask لعرض نتائج التحليل الذكي
Flask App for Displaying AI Analysis Results
"""

from flask import Flask, render_template, jsonify, request
import json
import pandas as pd
import os
import glob
from datetime import datetime
import base64
from io import BytesIO
import matplotlib
matplotlib.use('Agg')  # استخدام backend غير تفاعلي
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# إعداد التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'ai_analysis_secret_key'

# إعداد الخطوط
plt.rcParams['font.family'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_latest_results():
    """تحميل أحدث نتائج التحليل"""
    try:
        # البحث عن أحدث ملف نتائج
        result_files = glob.glob("analysis_results_*.json")
        
        if not result_files:
            return None, None, None
        
        # ترتيب الملفات حسب التاريخ
        result_files.sort(reverse=True)
        latest_file = result_files[0]
        
        # تحميل النتائج
        with open(latest_file, 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        # تحميل عينة البيانات
        data_files = glob.glob("data_sample_*.csv")
        data_files.sort(reverse=True)
        
        if data_files:
            data = pd.read_csv(data_files[0])
        else:
            data = None
        
        # تحميل الملخص
        summary_files = glob.glob("analysis_summary_*.txt")
        summary_files.sort(reverse=True)
        
        if summary_files:
            with open(summary_files[0], 'r', encoding='utf-8') as f:
                summary = f.read()
        else:
            summary = None
        
        return results, data, summary
        
    except Exception as e:
        print(f"خطأ في تحميل النتائج: {str(e)}")
        return None, None, None

def create_chart_base64(fig):
    """تحويل الرسم البياني إلى base64"""
    img = BytesIO()
    fig.savefig(img, format='png', bbox_inches='tight', dpi=150)
    img.seek(0)
    plot_url = base64.b64encode(img.getvalue()).decode()
    plt.close(fig)
    return plot_url

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    results, data, summary = load_latest_results()
    
    if results is None:
        return render_template('no_data.html')
    
    # إعداد البيانات للعرض
    context = {
        'has_data': True,
        'summary': summary,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    
    # معلومات أساسية
    if 'basic' in results and 'data_overview' in results['basic']:
        overview = results['basic']['data_overview']
        context['overview'] = {
            'total_records': f"{overview.get('total_records', 0):,}",
            'total_columns': overview.get('total_columns', 0),
            'memory_usage': f"{overview.get('memory_usage', 0):.2f} MB"
        }
    
    # جودة البيانات
    if 'basic' in results and 'data_quality' in results['basic']:
        quality = results['basic']['data_quality']
        context['quality'] = {
            'duplicate_records': quality.get('duplicate_records', 0),
            'missing_values': sum(v for v in quality.get('missing_values', {}).values() if isinstance(v, (int, float)))
        }
    
    # التحليل المتقدم
    if 'advanced' in results:
        advanced = results['advanced']
        context['advanced'] = {}
        
        # كشف الشذوذ
        if 'anomaly_detection' in advanced:
            anomaly = advanced['anomaly_detection']
            context['advanced']['anomaly'] = {
                'total_anomalies': anomaly.get('total_anomalies', 0),
                'anomaly_percentage': f"{anomaly.get('anomaly_percentage', 0):.2f}%"
            }
        
        # التجميع
        if 'clustering_analysis' in advanced:
            clustering = advanced['clustering_analysis']
            if 'kmeans' in clustering:
                context['advanced']['clustering'] = {
                    'n_clusters': clustering['kmeans'].get('n_clusters', 0),
                    'inertia': f"{clustering['kmeans'].get('inertia', 0):.2f}"
                }
        
        # الارتباطات
        if 'correlation_analysis' in advanced:
            correlation = advanced['correlation_analysis']
            if 'strong_correlations' in correlation:
                context['advanced']['correlations'] = len(correlation['strong_correlations'])
        
        # التنبؤات
        if 'predictive_insights' in advanced:
            predictions = advanced['predictive_insights']
            if 'model_performance' in predictions:
                performance = predictions['model_performance']
                context['advanced']['predictions'] = {
                    'accuracy': f"{performance.get('accuracy_percentage', 0):.1f}%",
                    'r2_score': f"{performance.get('r2_score', 0):.3f}"
                }
    
    return render_template('dashboard.html', **context)

@app.route('/charts')
def charts():
    """صفحة الرسوم البيانية"""
    results, data, summary = load_latest_results()
    
    if data is None:
        return render_template('no_data.html')
    
    charts = {}
    
    try:
        # رسم توزيع أنواع البيانات
        data_types = data.dtypes.value_counts()
        
        fig, ax = plt.subplots(figsize=(10, 6))
        colors = plt.cm.Set3(np.linspace(0, 1, len(data_types)))
        
        bars = ax.bar(range(len(data_types)), data_types.values, color=colors)
        ax.set_xlabel('Data Types', fontweight='bold')
        ax.set_ylabel('Number of Columns', fontweight='bold')
        ax.set_title('Distribution of Data Types', fontsize=14, fontweight='bold')
        ax.set_xticks(range(len(data_types)))
        ax.set_xticklabels([str(dtype) for dtype in data_types.index], rotation=45)
        
        # إضافة قيم على الأعمدة
        for bar, value in zip(bars, data_types.values):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                   str(value), ha='center', va='bottom', fontweight='bold')
        
        ax.grid(True, alpha=0.3)
        plt.tight_layout()
        
        charts['data_types'] = create_chart_base64(fig)
        
        # رسم الارتباطات
        numeric_data = data.select_dtypes(include=[np.number])
        
        if len(numeric_data.columns) > 1:
            correlation_matrix = numeric_data.corr()
            
            fig, ax = plt.subplots(figsize=(12, 10))
            mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
            
            sns.heatmap(
                correlation_matrix, 
                mask=mask,
                annot=True, 
                cmap='RdBu_r', 
                center=0,
                square=True,
                fmt='.2f',
                cbar_kws={"shrink": .8},
                ax=ax
            )
            
            ax.set_title('Correlation Matrix', fontsize=16, fontweight='bold', pad=20)
            plt.tight_layout()
            
            charts['correlation'] = create_chart_base64(fig)
        
        # رسم التوزيعات الرقمية
        if len(numeric_data.columns) > 0:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('Numeric Data Distributions', fontsize=16, fontweight='bold')
            
            for i, column in enumerate(numeric_data.columns[:4]):
                row = i // 2
                col = i % 2
                
                clean_data = numeric_data[column].dropna()
                
                if len(clean_data) > 0:
                    axes[row, col].hist(clean_data, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
                    axes[row, col].set_title(f'Distribution of {column}', fontweight='bold')
                    axes[row, col].set_xlabel(column)
                    axes[row, col].set_ylabel('Frequency')
                    axes[row, col].grid(True, alpha=0.3)
            
            plt.tight_layout()
            charts['distributions'] = create_chart_base64(fig)
        
    except Exception as e:
        print(f"خطأ في إنشاء الرسوم البيانية: {str(e)}")
    
    return render_template('charts.html', charts=charts)

@app.route('/data')
def data_view():
    """صفحة عرض البيانات"""
    results, data, summary = load_latest_results()
    
    if data is None:
        return render_template('no_data.html')
    
    # عرض أول 50 صف
    sample_data = data.head(50)
    
    # تحويل البيانات إلى HTML table
    data_html = sample_data.to_html(classes='table table-striped table-hover', table_id='dataTable')
    
    context = {
        'data_html': data_html,
        'total_rows': len(data),
        'total_columns': len(data.columns),
        'showing_rows': len(sample_data)
    }
    
    return render_template('data.html', **context)

@app.route('/api/results')
def api_results():
    """API لإرجاع النتائج كـ JSON"""
    results, data, summary = load_latest_results()
    
    if results is None:
        return jsonify({'error': 'No results found'})
    
    return jsonify(results)

# إنشاء القوالب
def create_templates():
    """إنشاء ملفات القوالب"""
    
    # إنشاء مجلد القوالب
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    # القالب الأساسي
    base_template = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام التحليل الذكي{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .navbar-brand { font-weight: bold; }
        .card { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); margin-bottom: 20px; }
        .metric-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .chart-container { text-align: center; margin: 20px 0; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/"><i class="fas fa-robot"></i> نظام التحليل الذكي</a>
            <div class="navbar-nav">
                <a class="nav-link" href="/"><i class="fas fa-home"></i> الرئيسية</a>
                <a class="nav-link" href="/charts"><i class="fas fa-chart-bar"></i> الرسوم البيانية</a>
                <a class="nav-link" href="/data"><i class="fas fa-table"></i> البيانات</a>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        {% block content %}{% endblock %}
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>'''
    
    with open('templates/base.html', 'w', encoding='utf-8') as f:
        f.write(base_template)

if __name__ == '__main__':
    create_templates()
    print("🚀 تشغيل تطبيق التحليل الذكي...")
    print("🌐 يمكنك الوصول للتطبيق على: http://localhost:8080")
    print("⚠️ تطبيق التحليل الذكي منفصل عن التطبيق الرئيسي")
    app.run(debug=True, host='0.0.0.0', port=8080)
