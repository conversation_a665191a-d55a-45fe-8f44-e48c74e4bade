<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الأتمتة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 2rem;
            margin: 2rem auto;
            max-width: 600px;
        }
        .btn-test {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h2 class="text-center mb-4">
                <i class="fas fa-robot text-primary"></i>
                اختبار نظام الأتمتة
            </h2>
            
            <div class="row">
                <div class="col-md-6">
                    <label for="shipmentId" class="form-label">معرف الشحنة</label>
                    <input type="number" class="form-control" id="shipmentId" value="174">
                </div>
                <div class="col-md-6">
                    <label for="newStatus" class="form-label">الحالة الجديدة</label>
                    <select class="form-control" id="newStatus">
                        <option value="customs_clearance">التخليص الجمركي</option>
                        <option value="in_transit">في الطريق</option>
                        <option value="delivered">تم التسليم</option>
                        <option value="arrived_port">وصل الميناء</option>
                    </select>
                </div>
            </div>
            
            <div class="mt-3">
                <label for="notes" class="form-label">ملاحظات</label>
                <textarea class="form-control" id="notes" rows="2" placeholder="ملاحظات اختيارية...">اختبار نظام الأتمتة</textarea>
            </div>
            
            <div class="text-center mt-4">
                <button class="btn btn-test" onclick="testAutomation()">
                    <i class="fas fa-play"></i>
                    اختبار الأتمتة
                </button>
                
                <button class="btn btn-secondary ms-2" onclick="clearResults()">
                    <i class="fas fa-trash"></i>
                    مسح النتائج
                </button>
            </div>
            
            <div id="results" class="result-box" style="display: none;">
                <h5><i class="fas fa-chart-line"></i> نتائج الاختبار:</h5>
                <div id="resultContent"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function addResult(message, type = 'info') {
            const resultContent = document.getElementById('resultContent');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const resultDiv = document.createElement('div');
            resultDiv.className = `${type} mb-2`;
            resultDiv.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            resultContent.appendChild(resultDiv);
            
            // إظهار صندوق النتائج
            document.getElementById('results').style.display = 'block';
            
            // التمرير لأسفل
            resultContent.scrollTop = resultContent.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('resultContent').innerHTML = '';
            document.getElementById('results').style.display = 'none';
        }
        
        async function testAutomation() {
            const shipmentId = document.getElementById('shipmentId').value;
            const newStatus = document.getElementById('newStatus').value;
            const notes = document.getElementById('notes').value;
            
            if (!shipmentId) {
                addResult('❌ يرجى إدخال معرف الشحنة', 'error');
                return;
            }
            
            addResult(`🚀 بدء اختبار الأتمتة للشحنة ${shipmentId}...`, 'info');
            
            try {
                // إرسال طلب تحديث الحالة
                const response = await fetch('/shipments/update_status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        shipment_id: shipmentId,
                        new_status: newStatus,
                        notes: notes
                    })
                });
                
                addResult(`📡 تم إرسال الطلب - كود الاستجابة: ${response.status}`, 'info');
                
                const result = await response.json();
                
                if (result.success) {
                    addResult(`✅ تم تحديث حالة الشحنة بنجاح`, 'success');
                    addResult(`📋 رقم التتبع: ${result.tracking_number}`, 'info');
                    addResult(`🔄 الحالة: ${result.old_status} → ${result.new_status}`, 'info');
                    addResult(`🤖 يجب أن تصل رسالة WhatsApp الآن إلى 967774893877`, 'success');
                } else {
                    addResult(`❌ فشل التحديث: ${result.message}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في الاتصال: ${error.message}`, 'error');
            }
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🎯 صفحة اختبار الأتمتة جاهزة', 'success');
            addResult('📱 سيتم إرسال WhatsApp إلى: 967774893877', 'info');
        });
    </script>
</body>
</html>
