{% extends "base.html" %}

{% block title %}تحليل أصناف أوامر الشراء المتقدم{% endblock %}

{% block extra_css %}
<link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css" rel="stylesheet">
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .stats-card h3 {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stats-card p {
        margin: 0;
        opacity: 0.9;
    }
    
    .chart-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        position: relative;
        height: 350px;
        overflow: hidden;
    }

    .chart-container canvas {
        max-width: 100% !important;
        max-height: 300px !important;
        width: auto !important;
        height: auto !important;
        display: block;
        box-sizing: border-box;
    }

    /* منع تجاوز الرسوم البيانية للحدود */
    .chart-container > div {
        max-width: 100%;
        max-height: 100%;
        overflow: hidden;
    }

    /* تحسين عرض الرسوم البيانية على الشاشات الصغيرة */
    @media (max-width: 768px) {
        .chart-container {
            height: 300px;
        }

        .chart-container canvas {
            max-height: 250px !important;
        }
    }

    /* تحسين محاذاة الجداول */
    .table th {
        vertical-align: middle;
        white-space: nowrap;
        font-weight: 600;
    }

    .table td {
        vertical-align: middle;
    }

    /* تحسين عرض الأرقام في الجداول */
    .table .text-end {
        font-family: 'Courier New', monospace;
        font-weight: 500;
    }

    /* تحسين عرض الشارات */
    .badge {
        font-size: 0.75em;
        padding: 0.35em 0.65em;
    }

    /* تحسين عرض الجدول التنفيذي */
    #executiveTable_wrapper {
        width: 100%;
        overflow-x: auto;
    }

    #executiveTable {
        width: 100% !important;
        min-width: 1200px;
        table-layout: fixed;
    }

    #executiveTable th,
    #executiveTable td {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 8px 4px;
    }

    /* تحسين عرض الجداول على الشاشات الكبيرة */
    @media (min-width: 1200px) {
        #executiveTable {
            min-width: 100%;
        }

        .table-responsive {
            overflow-x: visible;
        }
    }

    /* تحسين عرض DataTables */
    .dataTables_wrapper .dataTables_scroll {
        overflow-x: auto;
    }

    .dataTables_wrapper .dataTables_scrollHead,
    .dataTables_wrapper .dataTables_scrollBody {
        overflow-x: auto;
    }

    /* تحسينات محاذاة جدول ABC */
    #abcTable {
        table-layout: fixed !important;
        width: 100% !important;
    }

    #abcTable th,
    #abcTable td {
        vertical-align: middle !important;
        padding: 8px 6px !important;
        word-wrap: break-word;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* محاذاة خاصة للأعمدة الجديدة */
    #abcTable th:nth-child(1),
    #abcTable td:nth-child(1) { text-align: center !important; } /* كود الصنف */

    #abcTable th:nth-child(2),
    #abcTable td:nth-child(2) { text-align: right !important; } /* اسم الصنف */

    #abcTable th:nth-child(3),
    #abcTable td:nth-child(3) { text-align: left !important; } /* القيمة الإجمالية */

    #abcTable th:nth-child(4),
    #abcTable td:nth-child(4) { text-align: center !important; } /* الكمية */

    #abcTable th:nth-child(5),
    #abcTable td:nth-child(5) { text-align: center !important; } /* عدد الطلبات */

    #abcTable th:nth-child(6),
    #abcTable td:nth-child(6) { text-align: left !important; } /* متوسط السعر */

    #abcTable th:nth-child(7),
    #abcTable td:nth-child(7) { text-align: center !important; } /* ترتيب */

    #abcTable th:nth-child(8),
    #abcTable td:nth-child(8) { text-align: left !important; } /* نسبة القيمة */

    #abcTable th:nth-child(9),
    #abcTable td:nth-child(9) { text-align: left !important; } /* النسبة التراكمية */

    #abcTable th:nth-child(10),
    #abcTable td:nth-child(10) { text-align: center !important; } /* تصنيف ABC */

    #abcTable th:nth-child(11),
    #abcTable td:nth-child(11) { text-align: right !important; } /* وصف التصنيف */

    /* تحسين عرض الشارات */
    #abcTable .badge {
        font-size: 0.75rem;
        padding: 0.25em 0.5em;
        font-weight: 600;
    }

    /* تحسين عرض النصوص الطويلة */
    #abcTable td:nth-child(3) {
        max-width: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    #abcTable td:nth-child(11) {
        font-size: 0.85rem;
        line-height: 1.2;
    }

    /* تحسين عرض جدول الموردين */
    #suppliersTable_wrapper {
        width: 100%;
        overflow-x: auto;
    }

    #suppliersTable {
        width: 100% !important;
        min-width: 1600px;
        table-layout: fixed;
    }

    #suppliersTable th,
    #suppliersTable td {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 8px 4px;
    }

    /* عرض أفضل لأسماء الأصناف */
    #suppliersTable td:nth-child(3) {
        white-space: normal;
        word-wrap: break-word;
        max-width: 200px;
    }

    /* تحسين عرض جدول ABC */
    #abcTable_wrapper {
        width: 100%;
        overflow-x: auto;
    }

    #abcTable {
        width: 1400px !important;
        min-width: 1400px !important;
        max-width: 1400px !important;
        table-layout: fixed !important;
    }

    #abcTable th,
    #abcTable td {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 8px 4px;
        box-sizing: border-box;
    }

    /* عرض أفضل لأسماء الأصناف في جدول ABC */
    #abcTable td:nth-child(2) {
        white-space: normal;
        word-wrap: break-word;
        max-width: 280px;
    }

    /* ضمان تطابق العناوين مع البيانات */
    #abcTable thead th {
        position: sticky;
        top: 0;
        background-color: var(--bs-dark) !important;
        z-index: 10;
    }

    /* منع تمدد الجدول */
    .table-responsive {
        overflow-x: auto;
        overflow-y: visible;
    }

    #abcTable_wrapper .dataTables_scrollHead,
    #abcTable_wrapper .dataTables_scrollBody {
        overflow-x: auto;
    }

    #abcTable_wrapper .dataTables_scrollHead table,
    #abcTable_wrapper .dataTables_scrollBody table {
        width: 1400px !important;
        table-layout: fixed !important;
    }
    
    .filter-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .abc-badge-A {
        background-color: #dc3545;
        color: white;
    }
    
    .abc-badge-B {
        background-color: #ffc107;
        color: black;
    }
    
    .abc-badge-C {
        background-color: #28a745;
        color: white;
    }
    
    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .btn-export {
        margin-left: 5px;
    }
    
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">📊 تحليل أصناف أوامر الشراء المتقدم</h1>
                    <p class="text-muted">تحليل شامل ومتقدم لأصناف أوامر الشراء مع إحصائيات تفصيلية</p>
                </div>
                <div class="dropdown">
                    <button class="btn btn-success dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown">
                        <i class="fas fa-download"></i> تصدير البيانات
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="exportToExcel(false)">
                            <i class="fas fa-file-excel text-success"></i> Excel - بيانات أساسية
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportToExcel(true)">
                            <i class="fas fa-file-excel text-success"></i> Excel - تقرير شامل
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="exportToCSV()">
                            <i class="fas fa-file-csv text-info"></i> CSV
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportToJSON()">
                            <i class="fas fa-file-code text-warning"></i> JSON
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="exportToPDF()">
                            <i class="fas fa-file-pdf text-danger"></i> PDF - تقرير متقدم
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="generateAdvancedPDFReport()">
                            <i class="fas fa-file-pdf text-danger"></i> PDF - تقرير تنفيذي
                        </a></li>
                    </ul>
                </div>
                <button class="btn btn-primary btn-export ms-2" onclick="printReport()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="btn btn-info btn-export ms-2" onclick="emailReport()">
                    <i class="fas fa-envelope"></i> إرسال بالبريد
                </button>
            </div>
        </div>
    </div>

    <!-- Dashboard Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <h3>{{ "{:,}".format(dashboard_data[0] or 0) }}</h3>
                <p>إجمالي الأصناف الفريدة</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h3>{{ "{:,}".format(dashboard_data[1] or 0) }}</h3>
                <p>إجمالي طلبات الأصناف</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h3>{{ "{:,.2f}".format(dashboard_data[2] or 0) }}</h3>
                <p>إجمالي قيمة الأصناف</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h3>{{ "{:,}".format(dashboard_data[3] or 0) }}</h3>
                <p>إجمالي الموردين</p>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="filter-section">
        <h5 class="mb-3">🔍 فلاتر البحث والتصفية</h5>
        <div class="row">
            <div class="col-md-3">
                <label for="supplierFilter" class="form-label">المورد</label>
                <input type="text" class="form-control" id="supplierFilter" placeholder="اسم المورد...">
            </div>
            <div class="col-md-3">
                <label for="categoryFilter" class="form-label">الفئة</label>
                <select class="form-select" id="categoryFilter">
                    <option value="">جميع الفئات</option>
                    {% for category in categories %}
                    <option value="{{ category[0] }}">{{ category[1] }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="dateFromFilter" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="dateFromFilter">
            </div>
            <div class="col-md-2">
                <label for="dateToFilter" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="dateToFilter">
            </div>
            <div class="col-md-2">
                <label for="abcFilter" class="form-label">تصنيف ABC</label>
                <select class="form-select" id="abcFilter">
                    <option value="">جميع التصنيفات</option>
                    <option value="A">A - عالي القيمة</option>
                    <option value="B">B - متوسط القيمة</option>
                    <option value="C">C - منخفض القيمة</option>
                </select>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <button class="btn btn-primary" onclick="applyFilters()">
                    <i class="fas fa-search"></i> تطبيق الفلاتر
                </button>
                <button class="btn btn-secondary" onclick="clearFilters()">
                    <i class="fas fa-times"></i> مسح الفلاتر
                </button>
                <button class="btn btn-info" onclick="refreshData()">
                    <i class="fas fa-sync"></i> تحديث البيانات
                </button>
            </div>
        </div>
    </div>

    <!-- Tabs Navigation -->
    <ul class="nav nav-tabs mb-4" id="analysisTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                📈 نظرة عامة
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="abc-tab" data-bs-toggle="tab" data-bs-target="#abc-analysis" type="button" role="tab">
                🔤 تحليل ABC
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="suppliers-tab" data-bs-toggle="tab" data-bs-target="#suppliers-analysis" type="button" role="tab">
                🏢 تحليل الموردين
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="trends-tab" data-bs-toggle="tab" data-bs-target="#trends-analysis" type="button" role="tab">
                📈 التطور الزمني
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="reports-tab" data-bs-toggle="tab" data-bs-target="#executive-reports" type="button" role="tab">
                📋 التقارير التنفيذية
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="analysisTabsContent">
        <!-- Overview Tab -->
        <div class="tab-pane fade show active" id="overview" role="tabpanel">
            <div class="row">
                <div class="col-md-8">
                    <div class="chart-container">
                        <h5>📊 أعلى 10 أصناف حسب القيمة</h5>
                        <div style="position: relative; height: 300px; width: 100%;">
                            <canvas id="topItemsChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="chart-container">
                        <h5>🥧 توزيع تصنيف ABC</h5>
                        <div style="position: relative; height: 300px; width: 100%;">
                            <canvas id="abcDistributionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <div class="chart-container">
                        <h5>📋 جدول الأصناف التفصيلي</h5>
                        <div class="loading-spinner" id="loadingSpinner">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="itemsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th class="text-center">كود الصنف</th>
                                        <th class="text-center">اسم الصنف</th>
                                        <th class="text-center">المورد</th>
                                        <th class="text-end">الكمية الإجمالية</th>
                                        <th class="text-end">متوسط السعر</th>
                                        <th class="text-end">القيمة الإجمالية</th>
                                        <th class="text-center">عدد الطلبات</th>
                                        <th class="text-center">آخر طلب</th>
                                        <th class="text-center">حالة التسليم</th>
                                        <th class="text-center">حالة الصلاحية</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ABC Analysis Tab -->
        <div class="tab-pane fade" id="abc-analysis" role="tabpanel">
            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5>📈 منحنى باريتو (80-20)</h5>
                        <div style="position: relative; height: 300px; width: 100%;">
                            <canvas id="paretoChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5>📊 إحصائيات تصنيف ABC</h5>
                        <div id="abcStats"></div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <div class="chart-container">
                        <h5>📋 جدول تحليل ABC</h5>
                        <div class="table-responsive" style="width: 100%; overflow-x: auto;">
                            <table class="table table-striped table-hover" id="abcTable" style="width: 1400px; min-width: 1400px; max-width: 1400px; table-layout: fixed;">
                                <thead class="table-dark">
                                    <tr>
                                        <th class="text-center align-middle" style="width: 8%;">كود الصنف</th>
                                        <th class="text-end align-middle" style="width: 20%;">اسم الصنف</th>
                                        <th class="text-end align-middle" style="width: 10%;">القيمة الإجمالية</th>
                                        <th class="text-center align-middle" style="width: 8%;">الكمية</th>
                                        <th class="text-center align-middle" style="width: 6%;">عدد الطلبات</th>
                                        <th class="text-end align-middle" style="width: 10%;">متوسط السعر</th>
                                        <th class="text-center align-middle" style="width: 6%;">ترتيب</th>
                                        <th class="text-end align-middle" style="width: 8%;">نسبة القيمة %</th>
                                        <th class="text-end align-middle" style="width: 8%;">النسبة التراكمية %</th>
                                        <th class="text-center align-middle" style="width: 6%;">تصنيف ABC</th>
                                        <th class="text-end align-middle" style="width: 20%;">وصف التصنيف</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Suppliers Analysis Tab -->
        <div class="tab-pane fade" id="suppliers-analysis" role="tabpanel">
            <div class="row">
                <div class="col-12">
                    <div class="chart-container">
                        <h5>🏢 تحليل أداء الموردين</h5>
                        <div class="table-responsive" style="width: 100%; overflow-x: auto;">
                            <table class="table table-striped table-hover" id="suppliersTable" style="width: 100%; min-width: 1600px;">
                                <thead class="table-dark">
                                    <tr>
                                        <th class="text-center" style="width: 12%;">اسم المورد</th>
                                        <th class="text-center" style="width: 8%;">كود المورد</th>
                                        <th class="text-center" style="width: 8%;">كود الصنف</th>
                                        <th class="text-start" style="width: 15%;">اسم الصنف</th>
                                        <th class="text-center" style="width: 8%;">عدد الطلبات</th>
                                        <th class="text-center" style="width: 8%;">الكمية الإجمالية</th>
                                        <th class="text-end" style="width: 8%;">متوسط السعر</th>
                                        <th class="text-end" style="width: 8%;">أقل سعر</th>
                                        <th class="text-end" style="width: 8%;">أعلى سعر</th>
                                        <th class="text-end" style="width: 10%;">القيمة الإجمالية</th>
                                        <th class="text-end" style="width: 8%;">الانحراف المعياري</th>
                                        <th class="text-center" style="width: 8%;">ترتيب المورد</th>
                                        <th class="text-center" style="width: 10%;">آخر طلب</th>
                                        <th class="text-center" style="width: 8%;">أيام التسليم</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Trends Analysis Tab -->
        <div class="tab-pane fade" id="trends-analysis" role="tabpanel">
            <!-- فلاتر التطور الزمني -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <label class="form-label">فترة التحليل</label>
                    <select class="form-select" id="trendsTimePeriod">
                        <option value="monthly">شهرياً</option>
                        <option value="quarterly">ربع سنوي</option>
                        <option value="yearly">سنوياً</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">عدد الفترات</label>
                    <select class="form-select" id="trendsPeriodsCount">
                        <option value="6">آخر 6 فترات</option>
                        <option value="12" selected>آخر 12 فترة</option>
                        <option value="24">آخر 24 فترة</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">نوع التحليل</label>
                    <select class="form-select" id="trendsAnalysisType">
                        <option value="value">القيمة الإجمالية</option>
                        <option value="quantity">الكمية الإجمالية</option>
                        <option value="orders">عدد الطلبات</option>
                        <option value="items">عدد الأصناف</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary d-block w-100" onclick="loadTrendsChart()">
                        <i class="fas fa-chart-line"></i> تحديث التحليل
                    </button>
                </div>
            </div>

            <!-- الرسوم البيانية -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="chart-container">
                        <h5>📈 التطور الزمني للمشتريات</h5>
                        <div style="position: relative; height: 400px; width: 100%;">
                            <canvas id="trendsChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="chart-container">
                        <h5>📊 ملخص الاتجاهات</h5>
                        <div id="trendsSummary">
                            <div class="text-center p-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                                <p class="mt-2 text-muted">جاري تحليل البيانات...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- مقارنة الفترات -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="chart-container">
                        <h5>📊 مقارنة الفترات</h5>
                        <div class="row" id="periodsComparison">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- أعلى الأصناف نمواً -->
            <div class="row mt-4">
                <div class="col-lg-6">
                    <div class="chart-container">
                        <h5>🚀 أعلى الأصناف نمواً</h5>
                        <div id="topGrowingItems">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="chart-container">
                        <h5>📉 الأصناف الأكثر تراجعاً</h5>
                        <div id="topDecliningItems">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Executive Reports Tab -->
        <div class="tab-pane fade" id="executive-reports" role="tabpanel">
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="reportPeriod" class="form-label">فترة التقرير</label>
                    <select class="form-select" id="reportPeriod">
                        <option value="monthly">شهري</option>
                        <option value="quarterly">ربعي</option>
                        <option value="yearly">سنوي</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="reportMonthsBack" class="form-label">عدد الأشهر السابقة</label>
                    <select class="form-select" id="reportMonthsBack">
                        <option value="6">6 أشهر</option>
                        <option value="12" selected>12 شهر</option>
                        <option value="24">24 شهر</option>
                        <option value="36">36 شهر</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary d-block w-100" onclick="loadExecutiveReport()">
                        <i class="fas fa-chart-line"></i> تحديث التقرير
                    </button>
                </div>
            </div>

            <!-- KPI Cards -->
            <div class="row mb-4" id="executiveKPIs">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <h4 id="kpiTotalItems">-</h4>
                            <p class="mb-0">إجمالي الأصناف</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <h4 id="kpiTotalSuppliers">-</h4>
                            <p class="mb-0">إجمالي الموردين</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <h4 id="kpiTotalValue">-</h4>
                            <p class="mb-0">إجمالي القيمة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <h4 id="kpiAvgPrice">-</h4>
                            <p class="mb-0">متوسط السعر</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Executive Summary Chart -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="chart-container">
                        <h5>📊 ملخص تنفيذي - الاتجاهات الزمنية</h5>
                        <div style="position: relative; height: 300px; width: 100%;">
                            <canvas id="executiveSummaryChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5>📈 مؤشرات الأداء الرئيسية</h5>
                        <div style="position: relative; height: 300px; width: 100%;">
                            <canvas id="performanceMetricsChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5>💰 تحليل الوفورات المحتملة</h5>
                        <div id="savingsAnalysis">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                                <p class="mt-2">جاري تحليل الوفورات...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Items and Suppliers -->
            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5>🏆 أعلى 5 أصناف</h5>
                        <div id="topItemsList" class="list-group">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5>🏢 أعلى 5 موردين</h5>
                        <div id="topSuppliersList" class="list-group">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Executive Table -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="chart-container">
                        <h5>📋 تقرير تنفيذي تفصيلي</h5>
                        <div class="table-responsive" style="width: 100%; overflow-x: auto;">
                            <table class="table table-striped table-hover" id="executiveTable" style="width: 100%; min-width: 1200px;">
                                <thead class="table-dark">
                                    <tr>
                                        <th class="text-center" style="width: 10%;">الفترة</th>
                                        <th class="text-center" style="width: 8%;">عدد الطلبات</th>
                                        <th class="text-center" style="width: 8%;">الأصناف الفريدة</th>
                                        <th class="text-center" style="width: 8%;">الموردين</th>
                                        <th class="text-end" style="width: 12%;">القيمة الإجمالية</th>
                                        <th class="text-end" style="width: 12%;">متوسط قيمة الطلب</th>
                                        <th class="text-center" style="width: 10%;">معدل الإنجاز %</th>
                                        <th class="text-center" style="width: 10%;">متوسط أيام التسليم</th>
                                        <th class="text-center" style="width: 11%;">التغيير في القيمة %</th>
                                        <th class="text-center" style="width: 11%;">التغيير في الطلبات %</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

<script>
// متغيرات عامة
let itemsTable;
let abcTable;
let suppliersTable;
let executiveTable;
let topItemsChart;
let abcDistributionChart;
let paretoChart;
let trendsChart;
let executiveSummaryChart;
let performanceMetricsChart;

// بيانات من الخادم
const topItemsData = {{ top_items | tojson | safe }};
const dashboardData = {{ dashboard_data | tojson | safe }};

// إعدادات Chart.js العامة لمنع التوسع
Chart.defaults.responsive = true;
Chart.defaults.maintainAspectRatio = false;
Chart.defaults.aspectRatio = 2;
Chart.defaults.layout = {
    padding: 10
};
Chart.defaults.plugins.legend.position = 'top';

$(document).ready(function() {
    // تهيئة الجداول
    initializeTables();
    
    // تهيئة الرسوم البيانية
    initializeCharts();
    
    // تحميل البيانات الأولية
    loadItemsData();
    loadABCData();
    loadSuppliersData();

    // تحميل التقارير التنفيذية (بتأخير بسيط)
    setTimeout(function() {
        loadExecutiveReport();

        // تحميل التطور الزمني إذا كان التبويب نشط
        const activeTab = $('#analysisTabs .nav-link.active').attr('data-bs-target');
        if (activeTab === '#trends-analysis') {
            console.log('🔄 تحميل التطور الزمني عند بدء التشغيل');
            loadTrendsChart();
        }
    }, 1500);
});

function initializeTables() {
    // جدول الأصناف الرئيسي
    itemsTable = $('#itemsTable').DataTable({
        responsive: true,
        pageLength: 25,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        order: [[5, 'desc']], // ترتيب حسب القيمة الإجمالية
        columnDefs: [
            {
                targets: [0, 1, 2], // كود الصنف، اسم الصنف، المورد
                className: 'text-center'
            },
            {
                targets: [3, 4, 5], // الكمية، السعر، القيمة
                className: 'text-end'
            },
            {
                targets: [6, 7, 8, 9], // عدد الطلبات، آخر طلب، حالة التسليم، حالة الصلاحية
                className: 'text-center'
            }
        ]
    });
    
    // جدول تحليل ABC
    abcTable = $('#abcTable').DataTable({
        responsive: false, // تعطيل responsive لتوسيع الجدول
        pageLength: 50,
        scrollX: true, // تمكين التمرير الأفقي
        autoWidth: false, // تعطيل العرض التلقائي
        fixedColumns: false, // تعطيل الأعمدة الثابتة
        columnDefs: [
            { "width": "8%", "targets": 0 },   // كود الصنف
            { "width": "20%", "targets": 1 },  // اسم الصنف
            { "width": "10%", "targets": 2 },  // القيمة الإجمالية
            { "width": "8%", "targets": 3 },   // الكمية
            { "width": "6%", "targets": 4 },   // عدد الطلبات
            { "width": "10%", "targets": 5 },  // متوسط السعر
            { "width": "6%", "targets": 6 },   // ترتيب
            { "width": "8%", "targets": 7 },   // نسبة القيمة
            { "width": "8%", "targets": 8 },   // النسبة التراكمية
            { "width": "6%", "targets": 9 },   // تصنيف ABC
            { "width": "20%", "targets": 10 }  // وصف التصنيف
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        order: [[6, 'asc']], // ترتيب حسب ترتيب القيمة (العمود السابع)
        drawCallback: function() {
            // إعادة تطبيق العرض الثابت بعد الرسم
            $('#abcTable').css({
                'width': '1400px',
                'min-width': '1400px',
                'max-width': '1400px',
                'table-layout': 'fixed'
            });
            $('#abcTable th, #abcTable td').css('vertical-align', 'middle');
        }
    });
    
    // جدول الموردين
    suppliersTable = $('#suppliersTable').DataTable({
        responsive: false, // تعطيل responsive لتوسيع الجدول
        pageLength: 25,
        scrollX: true, // تمكين التمرير الأفقي
        autoWidth: false, // تعطيل العرض التلقائي
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        order: [[9, 'desc']], // ترتيب حسب القيمة الإجمالية (العمود 9)
        columnDefs: [
            {
                targets: [0, 1, 2], // اسم المورد، كود المورد، كود الصنف
                className: 'text-center',
                width: '8%'
            },
            {
                targets: [3], // اسم الصنف
                className: 'text-start',
                width: '15%'
            },
            {
                targets: [4, 5], // عدد الطلبات، الكمية
                className: 'text-center',
                width: '8%'
            },
            {
                targets: [6, 7, 8, 9, 10], // الأسعار والقيمة والانحراف المعياري
                className: 'text-end',
                width: '8%'
            },
            {
                targets: [11], // ترتيب المورد
                className: 'text-center',
                width: '8%'
            },
            {
                targets: [12], // آخر طلب
                className: 'text-center',
                width: '10%'
            },
            {
                targets: [13], // أيام التسليم
                className: 'text-center',
                width: '8%'
            }
        ]
    });

    // جدول التقرير التنفيذي
    executiveTable = $('#executiveTable').DataTable({
        responsive: false, // تعطيل responsive لتوسيع الجدول
        pageLength: 12,
        scrollX: true, // تمكين التمرير الأفقي
        autoWidth: false, // تعطيل العرض التلقائي
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        order: [[0, 'desc']], // ترتيب حسب الفترة
        columnDefs: [
            {
                targets: [0], // عمود الفترة
                className: 'text-center',
                width: '10%'
            },
            {
                targets: [1, 2, 3], // عدد الطلبات، الأصناف، الموردين
                className: 'text-center',
                width: '8%'
            },
            {
                targets: [4, 5], // القيمة الإجمالية، متوسط قيمة الطلب
                className: 'text-end',
                width: '12%'
            },
            {
                targets: [6, 7], // معدل الإنجاز، أيام التسليم
                className: 'text-center',
                width: '10%'
            },
            {
                targets: [8, 9], // نسب التغيير
                className: 'text-center',
                width: '10%'
            }
        ]
    });
}

function initializeCharts() {
    // رسم أعلى الأصناف
    const topItemsCtx = document.getElementById('topItemsChart').getContext('2d');
    topItemsChart = new Chart(topItemsCtx, {
        type: 'bar',
        data: {
            labels: topItemsData.map(item => item[1]), // أسماء الأصناف
            datasets: [{
                label: 'القيمة الإجمالية',
                data: topItemsData.map(item => item[2]), // القيم
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(153, 102, 255, 0.8)',
                    'rgba(255, 159, 64, 0.8)',
                    'rgba(199, 199, 199, 0.8)',
                    'rgba(83, 102, 255, 0.8)',
                    'rgba(255, 99, 255, 0.8)',
                    'rgba(99, 255, 132, 0.8)'
                ],
                borderColor: [
                    'rgba(255, 99, 132, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 205, 86, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(153, 102, 255, 1)',
                    'rgba(255, 159, 64, 1)',
                    'rgba(199, 199, 199, 1)',
                    'rgba(83, 102, 255, 1)',
                    'rgba(255, 99, 255, 1)',
                    'rgba(99, 255, 132, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'القيمة: ' + context.parsed.y.toLocaleString('ar-SA', {
                                style: 'currency',
                                currency: 'SAR'
                            });
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString('ar-SA');
                        }
                    }
                },
                x: {
                    ticks: {
                        maxRotation: 45,
                        minRotation: 45
                    }
                }
            }
        }
    });

    // رسم توزيع ABC
    const abcCtx = document.getElementById('abcDistributionChart').getContext('2d');
    abcDistributionChart = new Chart(abcCtx, {
        type: 'doughnut',
        data: {
            labels: ['A - عالي القيمة', 'B - متوسط القيمة', 'C - منخفض القيمة'],
            datasets: [{
                data: [0, 0, 0], // سيتم تحديثها لاحقاً
                backgroundColor: [
                    'rgba(220, 53, 69, 0.8)',
                    'rgba(255, 193, 7, 0.8)',
                    'rgba(40, 167, 69, 0.8)'
                ],
                borderColor: [
                    'rgba(220, 53, 69, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(40, 167, 69, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });
}

function loadItemsData() {
    $('#loadingSpinner').show();
    
    $.ajax({
        url: '{{ url_for("purchase_orders.api_items_analytics_data") }}',
        method: 'GET',
        data: getFilterParams(),
        success: function(response) {
            if (response.success) {
                updateItemsTable(response.data);
            } else {
                showAlert('خطأ في تحميل البيانات: ' + response.message, 'error');
            }
        },
        error: function() {
            showAlert('خطأ في الاتصال بالخادم', 'error');
        },
        complete: function() {
            $('#loadingSpinner').hide();
        }
    });
}

function loadABCData() {
    $.ajax({
        url: '{{ url_for("purchase_orders.api_abc_analysis_data") }}',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                updateABCTable(response.data);
                updateABCChart(response.data);
                updateParetoChart(response.data);
            }
        },
        error: function() {
            showAlert('خطأ في تحميل بيانات تحليل ABC', 'error');
        }
    });
}

function loadSuppliersData() {
    $.ajax({
        url: '{{ url_for("purchase_orders.api_supplier_analysis_data") }}',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                updateSuppliersTable(response.data);
            }
        },
        error: function() {
            showAlert('خطأ في تحميل بيانات الموردين', 'error');
        }
    });
}

function updateItemsTable(data) {
    itemsTable.clear();

    data.forEach(function(item) {
        itemsTable.row.add([
            item.item_code,
            item.item_name,
            item.supplier_name,
            parseFloat(item.total_quantity || 0).toLocaleString('en-US', {minimumFractionDigits: 0, maximumFractionDigits: 0}),
            parseFloat(item.avg_unit_price || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}),
            parseFloat(item.total_value || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}),
            item.order_count || 0,
            item.last_order_date || '',
            getDeliveryStatusBadge(item.delivery_status),
            getExpiryStatusBadge(item.expiry_status)
        ]);
    });

    itemsTable.draw();
}

function updateABCTable(data) {
    console.log('🔍 بيانات ABC المستلمة:', data);
    console.log('📊 عدد السجلات:', data.length);

    if (data.length > 0) {
        console.log('📋 السجل الأول:', data[0]);
        console.log('🔑 مفاتيح البيانات:', Object.keys(data[0]));

        // فحص كل حقل في السجل الأول
        const firstItem = data[0];
        console.log('🔍 فحص الحقول:');
        console.log('  - abc_classification:', firstItem.abc_classification);
        console.log('  - ABC_CLASSIFICATION:', firstItem.ABC_CLASSIFICATION);
        console.log('  - abc_description:', firstItem.abc_description);
        console.log('  - ABC_DESCRIPTION:', firstItem.ABC_DESCRIPTION);
    }

    abcTable.clear();

    data.forEach(function(item, index) {
        // إنشاء تصنيف ABC إذا لم يكن موجوداً
        let abcClass = item.abc_classification || item.ABC_CLASSIFICATION;
        let abcDesc = item.abc_description || item.ABC_DESCRIPTION;

        // إذا لم يكن هناك تصنيف، أنشئه بناءً على النسبة التراكمية
        if (!abcClass || abcClass === null || abcClass === undefined) {
            const cumulative = parseFloat(item.cumulative_percentage || item.CUMULATIVE_PERCENTAGE || 0);
            if (cumulative <= 80) {
                abcClass = 'A';
                abcDesc = 'عالي القيمة';
            } else if (cumulative <= 95) {
                abcClass = 'B';
                abcDesc = 'متوسط القيمة';
            } else {
                abcClass = 'C';
                abcDesc = 'منخفض القيمة';
            }
        }

        console.log(`📝 السجل ${index + 1} - تصنيف ABC: ${abcClass}, الوصف: ${abcDesc}`);

        // إنشاء شارة ملونة للتصنيف
        let abcBadge = '';
        if (abcClass === 'A') {
            abcBadge = `<span class="badge badge-success">${abcClass}</span>`;
        } else if (abcClass === 'B') {
            abcBadge = `<span class="badge badge-warning">${abcClass}</span>`;
        } else if (abcClass === 'C') {
            abcBadge = `<span class="badge badge-danger">${abcClass}</span>`;
        } else {
            abcBadge = `<span class="badge badge-secondary">${abcClass || '-'}</span>`;
        }

        // التأكد من وجود جميع القيم وعرضها بشكل صحيح
        const totalQuantity = parseFloat(item.total_quantity || item.TOTAL_QUANTITY || 0);
        const orderCount = parseInt(item.order_count || item.ORDER_COUNT || 0);
        const totalValue = parseFloat(item.total_value || item.TOTAL_VALUE || 0);
        const avgPrice = parseFloat(item.avg_price || item.AVG_PRICE || 0);
        const valuePercentage = parseFloat(item.value_percentage || item.VALUE_PERCENTAGE || 0);
        const cumulativePercentage = parseFloat(item.cumulative_percentage || item.CUMULATIVE_PERCENTAGE || 0);

        console.log(`📊 السجل ${index + 1}:`, {
            itemCode: item.item_code || item.ITEM_CODE,
            totalQuantity,
            orderCount,
            totalValue,
            avgPrice,
            valuePercentage,
            cumulativePercentage
        });

        abcTable.row.add([
            item.item_code || item.ITEM_CODE || '-',           // كود الصنف
            item.item_name || item.ITEM_NAME || '-',           // اسم الصنف
            '¥' + totalValue.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}), // القيمة الإجمالية
            totalQuantity.toLocaleString('en-US'),             // الكمية
            orderCount > 0 ? orderCount : '0',                 // عدد الطلبات
            '¥' + avgPrice.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}), // متوسط السعر
            item.value_rank || item.VALUE_RANK || (index + 1), // ترتيب
            valuePercentage.toFixed(2) + '%',                  // نسبة القيمة
            cumulativePercentage.toFixed(2) + '%',             // النسبة التراكمية
            abcBadge,                                          // تصنيف ABC مع شارة ملونة
            abcDesc || 'غير محدد'                              // وصف التصنيف
        ]);
    });

    abcTable.draw();

    // إعادة تحجيم الجدول مع العرض الثابت
    setTimeout(function() {
        abcTable.columns.adjust();
        $('#abcTable').css({
            'width': '1400px',
            'min-width': '1400px',
            'max-width': '1400px',
            'table-layout': 'fixed'
        });
    }, 100);
}

function updateSuppliersTable(data) {
    suppliersTable.clear();

    data.forEach(function(item) {
        suppliersTable.row.add([
            item.supplier_name || '-',                    // اسم المورد
            item.supplier_code || '-',                    // كود المورد
            item.item_code || '-',                        // كود الصنف
            item.item_name || '-',                        // اسم الصنف
            item.order_count || 0,                        // عدد الطلبات
            parseFloat(item.total_quantity || 0).toLocaleString('en-US', {minimumFractionDigits: 0, maximumFractionDigits: 0}), // الكمية الإجمالية
            '¥' + parseFloat(item.avg_unit_price || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}), // متوسط السعر
            '¥' + parseFloat(item.min_unit_price || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}), // أقل سعر
            '¥' + parseFloat(item.max_unit_price || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}), // أعلى سعر
            '¥' + parseFloat(item.total_value || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}), // القيمة الإجمالية
            '¥' + parseFloat(item.price_std_deviation || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}), // الانحراف المعياري
            item.supplier_rank_for_item || '-',           // ترتيب المورد
            item.last_order_date || '-',                  // آخر طلب
            parseFloat(item.avg_delivery_days || 0).toFixed(1) + ' يوم' // أيام التسليم
        ]);
    });

    suppliersTable.draw();

    // إعادة تحجيم الجدول
    setTimeout(function() {
        suppliersTable.columns.adjust();
        $('#suppliersTable').css('width', '100%');
    }, 100);
}

function updateABCChart(data) {
    const abcCounts = { A: 0, B: 0, C: 0 };
    const abcValues = { A: 0, B: 0, C: 0 };

    data.forEach(item => {
        const classification = item.abc_classification;
        abcCounts[classification]++;
        abcValues[classification] += parseFloat(item.total_value || 0);
    });

    // تحديث الرسم البياني
    abcDistributionChart.data.datasets[0].data = [
        abcCounts.A,
        abcCounts.B,
        abcCounts.C
    ];
    abcDistributionChart.update();

    // تحديث إحصائيات ABC
    updateABCStats(abcCounts, abcValues, data.length);
}

function updateABCStats(counts, values, totalItems) {
    const totalValue = values.A + values.B + values.C;

    const statsHtml = `
        <div class="row">
            <div class="col-md-4 mb-3">
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white text-center">
                        <h6 class="mb-0">تصنيف A</h6>
                        <small>عالي القيمة</small>
                    </div>
                    <div class="card-body text-center">
                        <h4 class="text-danger mb-2">${counts.A}</h4>
                        <p class="mb-1"><small class="text-muted">عدد الأصناف</small></p>
                        <h5 class="text-success mb-2">¥${values.A.toLocaleString('en-US')}</h5>
                        <p class="mb-1"><small class="text-muted">القيمة الإجمالية</small></p>
                        <div class="mt-2">
                            <span class="badge bg-danger">${((counts.A / totalItems) * 100).toFixed(1)}%</span>
                            <small class="text-muted d-block">من الأصناف</small>
                        </div>
                        <div class="mt-1">
                            <span class="badge bg-success">${totalValue > 0 ? ((values.A / totalValue) * 100).toFixed(1) : 0}%</span>
                            <small class="text-muted d-block">من القيمة</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-3">
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark text-center">
                        <h6 class="mb-0">تصنيف B</h6>
                        <small>متوسط القيمة</small>
                    </div>
                    <div class="card-body text-center">
                        <h4 class="text-warning mb-2">${counts.B}</h4>
                        <p class="mb-1"><small class="text-muted">عدد الأصناف</small></p>
                        <h5 class="text-success mb-2">¥${values.B.toLocaleString('en-US')}</h5>
                        <p class="mb-1"><small class="text-muted">القيمة الإجمالية</small></p>
                        <div class="mt-2">
                            <span class="badge bg-warning text-dark">${((counts.B / totalItems) * 100).toFixed(1)}%</span>
                            <small class="text-muted d-block">من الأصناف</small>
                        </div>
                        <div class="mt-1">
                            <span class="badge bg-success">${totalValue > 0 ? ((values.B / totalValue) * 100).toFixed(1) : 0}%</span>
                            <small class="text-muted d-block">من القيمة</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-3">
                <div class="card border-info">
                    <div class="card-header bg-info text-white text-center">
                        <h6 class="mb-0">تصنيف C</h6>
                        <small>منخفض القيمة</small>
                    </div>
                    <div class="card-body text-center">
                        <h4 class="text-info mb-2">${counts.C}</h4>
                        <p class="mb-1"><small class="text-muted">عدد الأصناف</small></p>
                        <h5 class="text-success mb-2">¥${values.C.toLocaleString('en-US')}</h5>
                        <p class="mb-1"><small class="text-muted">القيمة الإجمالية</small></p>
                        <div class="mt-2">
                            <span class="badge bg-info">${((counts.C / totalItems) * 100).toFixed(1)}%</span>
                            <small class="text-muted d-block">من الأصناف</small>
                        </div>
                        <div class="mt-1">
                            <span class="badge bg-success">${totalValue > 0 ? ((values.C / totalValue) * 100).toFixed(1) : 0}%</span>
                            <small class="text-muted d-block">من القيمة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">📊 ملخص التحليل</h6>
                        <div class="row text-center">
                            <div class="col-md-3">
                                <h5 class="text-primary">${totalItems}</h5>
                                <small class="text-muted">إجمالي الأصناف</small>
                            </div>
                            <div class="col-md-3">
                                <h5 class="text-success">¥${totalValue.toLocaleString('en-US')}</h5>
                                <small class="text-muted">إجمالي القيمة</small>
                            </div>
                            <div class="col-md-3">
                                <h5 class="text-info">¥${totalItems > 0 ? (totalValue / totalItems).toLocaleString('en-US') : 0}</h5>
                                <small class="text-muted">متوسط قيمة الصنف</small>
                            </div>
                            <div class="col-md-3">
                                <h5 class="text-warning">${counts.A > 0 ? ((values.A / counts.A) / (totalValue / totalItems)).toFixed(1) : 0}x</h5>
                                <small class="text-muted">مضاعف قيمة A</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('#abcStats').html(statsHtml);
}

function updateParetoChart(data) {
    const paretoCtx = document.getElementById('paretoChart').getContext('2d');

    if (paretoChart) {
        paretoChart.destroy();
    }

    paretoChart = new Chart(paretoCtx, {
        type: 'bar',
        data: {
            labels: data.slice(0, 20).map(item => item.item_code),
            datasets: [{
                type: 'bar',
                label: 'القيمة الإجمالية',
                data: data.slice(0, 20).map(item => item.total_value),
                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                yAxisID: 'y'
            }, {
                type: 'line',
                label: 'النسبة التراكمية %',
                data: data.slice(0, 20).map(item => item.cumulative_percentage),
                borderColor: 'rgba(255, 99, 132, 1)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                fill: false,
                tension: 0.1,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 0) {
                                return 'القيمة: ' + context.parsed.y.toLocaleString('ar-SA');
                            } else {
                                return 'النسبة التراكمية: ' + context.parsed.y.toFixed(1) + '%';
                            }
                        }
                    }
                }
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'القيمة الإجمالية'
                    },
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString('ar-SA');
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'النسبة التراكمية %'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                    min: 0,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                },
                x: {
                    ticks: {
                        maxRotation: 45,
                        minRotation: 45
                    }
                }
            }
        }
    });
}

function loadTrendsChart() {
    const timePeriod = $('#trendsTimePeriod').val() || 'monthly';
    const periodsCount = $('#trendsPeriodsCount').val() || 12;
    const analysisType = $('#trendsAnalysisType').val() || 'value';

    console.log('🔄 تحميل التطور الزمني...', {timePeriod, periodsCount, analysisType});

    $.ajax({
        url: '{{ url_for("purchase_orders.api_monthly_trends_data") }}',
        method: 'GET',
        data: {
            months_back: periodsCount,
            period_type: timePeriod,
            analysis_type: analysisType
        },
        success: function(response) {
            console.log('📊 استجابة API التطور الزمني:', response);
            if (response.success) {
                console.log('📈 بيانات التطور الزمني:', response.data);
                updateTrendsChart(response.data, analysisType);
                updateTrendsSummary(response.data, analysisType);
                updatePeriodsComparison(response.data);
                updateGrowthAnalysis(response.data);
            } else {
                console.error('❌ خطأ في API:', response.message);
                showAlert('خطأ في تحميل بيانات التطور الزمني: ' + response.message, 'error');
            }
        },
        error: function() {
            showAlert('خطأ في تحميل بيانات التطور الزمني', 'error');
        }
    });
}

function updateTrendsChart(data, analysisType = 'value') {
    console.log('🎯 تحديث الرسم البياني للتطور الزمني:', {data, analysisType});

    const trendsCtx = document.getElementById('trendsChart').getContext('2d');

    if (trendsChart) {
        trendsChart.destroy();
    }

    if (!data || data.length === 0) {
        console.warn('⚠️ لا توجد بيانات للرسم البياني');
        return;
    }

    // ترتيب البيانات حسب الشهر
    const sortedData = data.sort((a, b) => a.order_month.localeCompare(b.order_month));
    const chartData = sortedData;

    console.log('📊 البيانات المرتبة:', chartData);

    // تحديد البيانات حسب نوع التحليل
    let datasets = [];
    let yAxisConfig = {};

    if (analysisType === 'value') {
        datasets = [{
            label: 'القيمة الإجمالية (¥)',
            data: chartData.map(item => item.monthly_value),
            borderColor: 'rgba(75, 192, 192, 1)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            fill: true,
            tension: 0.1
        }];
        yAxisConfig = {
            y: {
                beginAtZero: true,
                title: { display: true, text: 'القيمة (¥)' },
                ticks: { callback: function(value) { return value.toLocaleString('en-US'); } }
            }
        };
    } else if (analysisType === 'quantity') {
        datasets = [{
            label: 'الكمية الإجمالية',
            data: chartData.map(item => item.monthly_quantity),
            borderColor: 'rgba(255, 99, 132, 1)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            fill: true,
            tension: 0.1
        }];
        yAxisConfig = {
            y: {
                beginAtZero: true,
                title: { display: true, text: 'الكمية' },
                ticks: { callback: function(value) { return value.toLocaleString('en-US'); } }
            }
        };
    } else if (analysisType === 'orders') {
        datasets = [{
            label: 'عدد الطلبات',
            data: chartData.map(item => item.monthly_orders),
            borderColor: 'rgba(255, 206, 86, 1)',
            backgroundColor: 'rgba(255, 206, 86, 0.2)',
            fill: true,
            tension: 0.1
        }];
        yAxisConfig = {
            y: {
                beginAtZero: true,
                title: { display: true, text: 'عدد الطلبات' },
                ticks: { callback: function(value) { return value.toLocaleString('en-US'); } }
            }
        };
    } else if (analysisType === 'items') {
        datasets = [{
            label: 'عدد الأصناف الفريدة',
            data: chartData.map(item => item.unique_items),
            borderColor: 'rgba(153, 102, 255, 1)',
            backgroundColor: 'rgba(153, 102, 255, 0.2)',
            fill: true,
            tension: 0.1
        }];
        yAxisConfig = {
            y: {
                beginAtZero: true,
                title: { display: true, text: 'عدد الأصناف' },
                ticks: { callback: function(value) { return value.toLocaleString('en-US'); } }
            }
        };
    }

    trendsChart = new Chart(trendsCtx, {
        type: 'line',
        data: {
            labels: chartData.map(item => item.order_month),
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': ' + context.parsed.y.toLocaleString('en-US');
                        }
                    }
                },
                legend: {
                    position: 'top'
                }
            },
            scales: yAxisConfig
        }
    });
}

function updateTrendsSummary(data, analysisType) {
    console.log('📊 تحديث ملخص الاتجاهات:', {data, analysisType});

    if (!data || data.length < 1) {
        console.warn('⚠️ لا توجد بيانات:', data);
        $('#trendsSummary').html('<div class="text-muted text-center p-3">لا توجد بيانات متاحة</div>');
        return;
    }

    // ترتيب البيانات حسب الشهر
    const sortedData = data.sort((a, b) => a.order_month.localeCompare(b.order_month));
    const latest = sortedData[sortedData.length - 1];
    const previous = sortedData.length > 1 ? sortedData[sortedData.length - 2] : null;

    console.log('📈 البيانات للمقارنة:', {latest, previous, sortedData});

    let currentValue, previousValue, label, unit;

    switch(analysisType) {
        case 'value':
            currentValue = latest.monthly_value;
            previousValue = previous ? previous.monthly_value : 0;
            label = 'القيمة الإجمالية';
            unit = '¥';
            break;
        case 'quantity':
            currentValue = latest.monthly_quantity;
            previousValue = previous.monthly_quantity;
            label = 'الكمية الإجمالية';
            unit = '';
            break;
        case 'orders':
            currentValue = latest.monthly_orders;
            previousValue = previous.monthly_orders;
            label = 'عدد الطلبات';
            unit = '';
            break;
        case 'items':
            currentValue = latest.unique_items;
            previousValue = previous.unique_items;
            label = 'عدد الأصناف';
            unit = '';
            break;
    }

    let changePercent = 0;
    let changeClass = 'text-muted';
    let changeIcon = 'fa-minus';
    let changeText = 'لا توجد بيانات سابقة';

    if (previous && previousValue > 0) {
        changePercent = ((currentValue - previousValue) / previousValue * 100);
        changeClass = changePercent > 0 ? 'text-success' : changePercent < 0 ? 'text-danger' : 'text-muted';
        changeIcon = changePercent > 0 ? 'fa-arrow-up' : changePercent < 0 ? 'fa-arrow-down' : 'fa-minus';
        changeText = `${Math.abs(changePercent).toFixed(1)}%`;
    }

    console.log('💰 القيم المحسوبة:', {
        analysisType,
        currentValue,
        previousValue,
        changePercent,
        changeText,
        label,
        unit,
        latest,
        previous
    });

    const summaryHtml = `
        <div class="p-3">
            <div class="text-center mb-3">
                <h6 class="text-muted">${label}</h6>
                <h4 class="mb-0 text-primary">${unit}${currentValue.toLocaleString('en-US')}</h4>
                <small class="text-muted">الفترة الحالية (${latest.order_month})</small>
            </div>

            <div class="text-center mb-3">
                <span class="${changeClass}">
                    <i class="fas ${changeIcon}"></i>
                    ${changeText}
                </span>
                <br>
                <small class="text-muted">${previous ? 'مقارنة بالفترة السابقة' : 'لا توجد فترة سابقة للمقارنة'}</small>
            </div>

            <div class="border-top pt-3">
                <div class="row text-center">
                    <div class="col-6">
                        <small class="text-muted d-block">الفترة الحالية</small>
                        <strong class="text-primary">${latest.order_month}</strong>
                        <br>
                        <small class="text-success">${latest.monthly_orders} طلب</small>
                    </div>
                    <div class="col-6">
                        <small class="text-muted d-block">الفترة السابقة</small>
                        <strong class="text-secondary">${previous ? previous.order_month : 'غير متاح'}</strong>
                        <br>
                        <small class="text-info">${previous ? previous.monthly_orders + ' طلب' : 'لا توجد'}</small>
                    </div>
                </div>
            </div>

            ${previous ? `
            <div class="border-top pt-3 mt-3">
                <div class="row text-center">
                    <div class="col-6">
                        <small class="text-muted d-block">القيمة السابقة</small>
                        <strong>${unit}${previousValue.toLocaleString('en-US')}</strong>
                    </div>
                    <div class="col-6">
                        <small class="text-muted d-block">الفرق</small>
                        <strong class="${changeClass}">${unit}${(currentValue - previousValue).toLocaleString('en-US')}</strong>
                    </div>
                </div>
            </div>
            ` : ''}
        </div>
    `;

    $('#trendsSummary').html(summaryHtml);
}

function updatePeriodsComparison(data) {
    console.log('📊 تحديث مقارنة الفترات:', data);

    if (!data || data.length === 0) {
        $('#periodsComparison').html('<div class="text-muted text-center p-3">لا توجد بيانات للمقارنة</div>');
        return;
    }

    // ترتيب البيانات حسب الشهر
    const sortedData = data.sort((a, b) => a.order_month.localeCompare(b.order_month));

    let comparisonHtml = '';

    sortedData.forEach((period, index) => {
        const isLatest = index === sortedData.length - 1;
        const isPrevious = index === sortedData.length - 2;

        // حساب نسبة التغيير مقارنة بالفترة السابقة
        let changePercent = 0;
        let changeClass = 'text-muted';
        let changeIcon = 'fa-minus';

        if (index > 0) {
            const prevPeriod = sortedData[index - 1];
            if (prevPeriod.monthly_value > 0) {
                changePercent = ((period.monthly_value - prevPeriod.monthly_value) / prevPeriod.monthly_value * 100);
                changeClass = changePercent > 0 ? 'text-success' : changePercent < 0 ? 'text-danger' : 'text-muted';
                changeIcon = changePercent > 0 ? 'fa-arrow-up' : changePercent < 0 ? 'fa-arrow-down' : 'fa-minus';
            }
        }

        // تحديد نوع البطاقة
        let cardClass = 'border-secondary';
        let badgeClass = 'bg-secondary';
        let badgeText = 'فترة عادية';

        if (isLatest) {
            cardClass = 'border-primary';
            badgeClass = 'bg-primary';
            badgeText = 'الفترة الحالية';
        } else if (isPrevious) {
            cardClass = 'border-info';
            badgeClass = 'bg-info';
            badgeText = 'الفترة السابقة';
        }

        comparisonHtml += `
            <div class="col-lg-6 mb-3">
                <div class="card ${cardClass}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">${period.order_month}</h6>
                        <span class="badge ${badgeClass}">${badgeText}</span>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="border-end">
                                    <h5 class="text-primary mb-1">${period.monthly_orders}</h5>
                                    <small class="text-muted">طلبات</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <h5 class="text-success mb-1">${period.unique_items}</h5>
                                    <small class="text-muted">أصناف</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <h5 class="text-info mb-1">¥${period.monthly_value.toLocaleString('en-US')}</h5>
                                <small class="text-muted">القيمة</small>
                            </div>
                        </div>

                        ${index > 0 ? `
                        <div class="mt-3 pt-3 border-top">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">التغيير من الفترة السابقة:</small>
                                <span class="${changeClass}">
                                    <i class="fas ${changeIcon}"></i>
                                    ${Math.abs(changePercent).toFixed(1)}%
                                </span>
                            </div>
                        </div>
                        ` : ''}

                        <div class="mt-2">
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-primary" role="progressbar"
                                     style="width: ${(period.monthly_value / Math.max(...sortedData.map(p => p.monthly_value)) * 100)}%">
                                </div>
                            </div>
                            <small class="text-muted">نسبة من أعلى فترة</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    $('#periodsComparison').html(comparisonHtml);
}

function updateGrowthAnalysis(data) {
    console.log('📈 تحليل النمو والتراجع:', data);

    // تحميل بيانات الأصناف للنمو والتراجع
    $.ajax({
        url: '{{ url_for("purchase_orders.api_items_growth_analysis") }}',
        method: 'GET',
        data: {
            months_back: $('#trendsPeriodsCount').val() || 12
        },
        success: function(response) {
            if (response.success) {
                displayGrowthAnalysis(response.growing_items, response.declining_items);
            } else {
                $('#topGrowingItems').html('<div class="text-muted text-center p-3">خطأ في تحميل البيانات</div>');
                $('#topDecliningItems').html('<div class="text-muted text-center p-3">خطأ في تحميل البيانات</div>');
            }
        },
        error: function() {
            $('#topGrowingItems').html('<div class="text-muted text-center p-3">خطأ في الاتصال</div>');
            $('#topDecliningItems').html('<div class="text-muted text-center p-3">خطأ في الاتصال</div>');
        }
    });
}

function displayGrowthAnalysis(growingItems, decliningItems) {
    // عرض أعلى الأصناف نمواً
    let growingHtml = '';
    if (growingItems && growingItems.length > 0) {
        growingHtml = '<div class="list-group list-group-flush">';
        growingItems.slice(0, 5).forEach((item, index) => {
            const growthPercent = item.growth_percentage || 0;
            const rankBadge = index === 0 ? 'bg-warning text-dark' : 'bg-light text-dark';
            growingHtml += `
                <div class="list-group-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-1">
                                <span class="badge ${rankBadge} me-2">#${index + 1}</span>
                                <h6 class="mb-0">${item.item_name}</h6>
                            </div>
                            <small class="text-muted">كود: ${item.item_code}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-success rounded-pill mb-1">
                                <i class="fas fa-arrow-up"></i> ${growthPercent.toFixed(1)}%
                            </span>
                            <br>
                            <small class="text-success">¥${item.current_value.toLocaleString('en-US')}</small>
                        </div>
                    </div>
                    <div class="progress mt-2" style="height: 6px;">
                        <div class="progress-bar bg-success" role="progressbar"
                             style="width: ${Math.min(growthPercent, 100)}%">
                        </div>
                    </div>
                </div>
            `;
        });
        growingHtml += '</div>';
    } else {
        growingHtml = `
            <div class="text-center p-4">
                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                <h6 class="text-muted">لا توجد أصناف نامية</h6>
                <p class="text-muted small mb-0">لا توجد أصناف حققت نمواً يزيد عن 5% في الفترة المحددة</p>
            </div>
        `;
    }

    // عرض الأصناف الأكثر تراجعاً
    let decliningHtml = '';
    if (decliningItems && decliningItems.length > 0) {
        decliningHtml = '<div class="list-group list-group-flush">';
        decliningItems.slice(0, 5).forEach((item, index) => {
            const declinePercent = Math.abs(item.growth_percentage || 0);
            const rankBadge = index === 0 ? 'bg-warning text-dark' : 'bg-light text-dark';
            decliningHtml += `
                <div class="list-group-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-1">
                                <span class="badge ${rankBadge} me-2">#${index + 1}</span>
                                <h6 class="mb-0">${item.item_name}</h6>
                            </div>
                            <small class="text-muted">كود: ${item.item_code}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-danger rounded-pill mb-1">
                                <i class="fas fa-arrow-down"></i> ${declinePercent.toFixed(1)}%
                            </span>
                            <br>
                            <small class="text-danger">¥${item.current_value.toLocaleString('en-US')}</small>
                        </div>
                    </div>
                    <div class="progress mt-2" style="height: 6px;">
                        <div class="progress-bar bg-danger" role="progressbar"
                             style="width: ${Math.min(declinePercent, 100)}%">
                        </div>
                    </div>
                </div>
            `;
        });
        decliningHtml += '</div>';
    } else {
        decliningHtml = `
            <div class="text-center p-4">
                <i class="fas fa-chart-line-down fa-3x text-muted mb-3"></i>
                <h6 class="text-muted">لا توجد أصناف متراجعة</h6>
                <p class="text-muted small mb-0">لا توجد أصناف تراجعت بأكثر من 5% في الفترة المحددة</p>
            </div>
        `;
    }

    $('#topGrowingItems').html(growingHtml);
    $('#topDecliningItems').html(decliningHtml);
}

function getFilterParams() {
    return {
        supplier: $('#supplierFilter').val(),
        category: $('#categoryFilter').val(),
        date_from: $('#dateFromFilter').val(),
        date_to: $('#dateToFilter').val(),
        abc_class: $('#abcFilter').val()
    };
}

function applyFilters() {
    loadItemsData();
}

function clearFilters() {
    $('#supplierFilter').val('');
    $('#categoryFilter').val('');
    $('#dateFromFilter').val('');
    $('#dateToFilter').val('');
    $('#abcFilter').val('');
    loadItemsData();
}

function refreshData() {
    loadItemsData();
    loadABCData();
    loadSuppliersData();
    loadTrendsChart();
}

// تحميل الرسوم البيانية عند تغيير التبويبات
$('#analysisTabs button[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
    const target = $(e.target).attr('data-bs-target');

    if (target === '#trends-analysis') {
        console.log('🔄 تحميل تبويب التطور الزمني');
        loadTrendsChart();
    } else if (target === '#executive-reports' && !executiveSummaryChart) {
        loadExecutiveReport();
    }

    // إعادة تحجيم الرسوم البيانية بعد تغيير التبويب
    setTimeout(resizeCharts, 200);
});

function loadExecutiveReport() {
    const period = $('#reportPeriod').val() || 'monthly';
    const monthsBack = $('#reportMonthsBack').val() || 12;

    console.log('🔄 تحميل التقرير التنفيذي...', {period, monthsBack});

    // تحميل التقرير التنفيذي
    $.ajax({
        url: '{{ url_for("purchase_orders.api_executive_summary_report") }}',
        method: 'GET',
        data: {
            period: period,
            months_back: monthsBack
        },
        success: function(response) {
            console.log('✅ استجابة التقرير التنفيذي:', response);
            if (response.success) {
                updateExecutiveReport(response);
            } else {
                console.error('❌ خطأ في التقرير التنفيذي:', response.message);
                showAlert('خطأ في تحميل التقرير التنفيذي: ' + response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ خطأ في AJAX:', {xhr, status, error});
            showAlert('خطأ في الاتصال بالخادم: ' + error, 'error');
        }
    });

    // تحميل تحليل الوفورات
    loadSavingsAnalysis(monthsBack);
}

function updateExecutiveReport(data) {
    console.log('🔄 تحديث التقرير التنفيذي...', data);

    try {
        // تحديث KPIs
        if (data.kpi_data) {
            $('#kpiTotalItems').text(parseInt(data.kpi_data.total_unique_items || 0).toLocaleString('en-US'));
            $('#kpiTotalSuppliers').text(parseInt(data.kpi_data.total_suppliers || 0).toLocaleString('en-US'));
            $('#kpiTotalValue').text(parseFloat(data.kpi_data.grand_total_value || 0).toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }) + ' SAR');
            $('#kpiAvgPrice').text(parseFloat(data.kpi_data.overall_avg_price || 0).toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }) + ' SAR');

            // تحديث قوائم أعلى الأصناف والموردين
            updateTopLists(data.kpi_data);
        }

        // تحديث الرسوم البيانية والجداول
        if (data.period_data && data.period_data.length > 0) {
            console.log('✅ تحديث الرسوم البيانية والجداول...', data.period_data.length, 'فترة');

            // تحديث الرسم البياني التنفيذي
            updateExecutiveSummaryChart(data.period_data);

            // تحديث رسم مؤشرات الأداء
            updatePerformanceMetricsChart(data.period_data);

            // تحديث الجدول التنفيذي
            updateExecutiveTable(data.period_data);
        } else {
            console.warn('⚠️ لا توجد بيانات فترات');
            showAlert('لا توجد بيانات للفترة المحددة', 'warning');
        }

    } catch (error) {
        console.error('❌ خطأ في تحديث التقرير التنفيذي:', error);
        showAlert('خطأ في تحديث التقرير التنفيذي', 'error');
    }
}

function updateTopLists(kpiData) {
    // أعلى 5 أصناف
    const topItems = kpiData.top_5_items ? kpiData.top_5_items.split(', ') : [];
    let topItemsHtml = '';
    topItems.forEach((item, index) => {
        if (item.trim()) {
            topItemsHtml += `
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <span>${index + 1}. ${item}</span>
                    <span class="badge bg-primary rounded-pill">#${index + 1}</span>
                </div>
            `;
        }
    });
    $('#topItemsList').html(topItemsHtml || '<div class="text-muted text-center p-3">لا توجد بيانات</div>');

    // أعلى 5 موردين
    const topSuppliers = kpiData.top_5_suppliers ? kpiData.top_5_suppliers.split(', ') : [];
    let topSuppliersHtml = '';
    topSuppliers.forEach((supplier, index) => {
        if (supplier.trim()) {
            topSuppliersHtml += `
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <span>${index + 1}. ${supplier}</span>
                    <span class="badge bg-success rounded-pill">#${index + 1}</span>
                </div>
            `;
        }
    });
    $('#topSuppliersList').html(topSuppliersHtml || '<div class="text-muted text-center p-3">لا توجد بيانات</div>');
}

function updateExecutiveSummaryChart(periodData) {
    const ctx = document.getElementById('executiveSummaryChart').getContext('2d');

    if (executiveSummaryChart) {
        executiveSummaryChart.destroy();
    }

    executiveSummaryChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: periodData.map(item => item.period),
            datasets: [{
                label: 'القيمة الإجمالية',
                data: periodData.map(item => item.total_value),
                borderColor: 'rgba(75, 192, 192, 1)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                fill: true,
                tension: 0.1,
                yAxisID: 'y'
            }, {
                label: 'عدد الطلبات',
                data: periodData.map(item => item.total_orders),
                borderColor: 'rgba(255, 99, 132, 1)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                fill: false,
                tension: 0.1,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 0) {
                                return 'القيمة: ' + context.parsed.y.toLocaleString('ar-SA');
                            } else {
                                return 'الطلبات: ' + context.parsed.y.toLocaleString('ar-SA');
                            }
                        }
                    }
                }
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'القيمة الإجمالية'
                    },
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString('ar-SA');
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'عدد الطلبات'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString('ar-SA');
                        }
                    }
                }
            }
        }
    });
}

function updatePerformanceMetricsChart(periodData) {
    const ctx = document.getElementById('performanceMetricsChart').getContext('2d');

    if (performanceMetricsChart) {
        performanceMetricsChart.destroy();
    }

    performanceMetricsChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: periodData.map(item => item.period),
            datasets: [{
                label: 'معدل الإنجاز %',
                data: periodData.map(item => item.completion_rate),
                backgroundColor: 'rgba(40, 167, 69, 0.8)',
                borderColor: 'rgba(40, 167, 69, 1)',
                borderWidth: 1,
                yAxisID: 'y'
            }, {
                label: 'متوسط أيام التسليم',
                data: periodData.map(item => item.avg_delivery_days),
                backgroundColor: 'rgba(255, 193, 7, 0.8)',
                borderColor: 'rgba(255, 193, 7, 1)',
                borderWidth: 1,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 0) {
                                return 'معدل الإنجاز: ' + context.parsed.y.toFixed(1) + '%';
                            } else {
                                return 'أيام التسليم: ' + context.parsed.y.toFixed(1) + ' يوم';
                            }
                        }
                    }
                }
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'معدل الإنجاز %'
                    },
                    min: 0,
                    max: 100
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'متوسط أيام التسليم'
                    },
                    grid: {
                        drawOnChartArea: false,
                    }
                }
            }
        }
    });
}

function updateExecutiveTable(periodData) {
    executiveTable.clear();

    periodData.forEach(function(item) {
        const valueChangeClass = item.value_change_percentage > 0 ? 'text-success' :
                                item.value_change_percentage < 0 ? 'text-danger' : '';
        const ordersChangeClass = item.orders_change_percentage > 0 ? 'text-success' :
                                 item.orders_change_percentage < 0 ? 'text-danger' : '';

        executiveTable.row.add([
            item.period,
            parseInt(item.total_orders || 0).toLocaleString('en-US'),
            parseInt(item.unique_items || 0).toLocaleString('en-US'),
            parseInt(item.unique_suppliers || 0).toLocaleString('en-US'),
            parseFloat(item.total_value || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}),
            parseFloat(item.avg_order_value || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}),
            parseFloat(item.completion_rate || 0).toFixed(1) + '%',
            parseFloat(item.avg_delivery_days || 0).toFixed(1),
            `<span class="${valueChangeClass}">${item.value_change_percentage ? (item.value_change_percentage > 0 ? '+' : '') + item.value_change_percentage.toFixed(1) + '%' : 'N/A'}</span>`,
            `<span class="${ordersChangeClass}">${item.orders_change_percentage ? (item.orders_change_percentage > 0 ? '+' : '') + item.orders_change_percentage.toFixed(1) + '%' : 'N/A'}</span>`
        ]);
    });

    executiveTable.draw();

    // إعادة تحجيم الجدول
    setTimeout(function() {
        executiveTable.columns.adjust();
        $('#executiveTable').css('width', '100%');
    }, 100);
}

function loadSavingsAnalysis(monthsBack) {
    $.ajax({
        url: '{{ url_for("purchase_orders.api_savings_analysis_report") }}',
        method: 'GET',
        data: { months_back: monthsBack },
        success: function(response) {
            if (response.success) {
                updateSavingsAnalysis(response);
            } else {
                $('#savingsAnalysis').html('<div class="alert alert-warning">خطأ في تحميل تحليل الوفورات</div>');
            }
        },
        error: function() {
            $('#savingsAnalysis').html('<div class="alert alert-danger">خطأ في الاتصال بالخادم</div>');
        }
    });
}

function updateSavingsAnalysis(data) {
    const summary = data.summary;
    const opportunities = data.savings_opportunities.slice(0, 5); // أعلى 5 فرص وفورات

    let savingsHtml = `
        <div class="row mb-3">
            <div class="col-12">
                <div class="alert alert-info">
                    <h6 class="mb-2">💰 إجمالي الوفورات المحتملة</h6>
                    <h4 class="mb-0 text-primary">${parseFloat(summary.total_potential_savings || 0).toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    })} SAR</h4>
                    <small class="text-muted">${summary.savings_percentage_of_total.toFixed(1)}% من إجمالي المصروفات</small>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <h6 class="mb-3">🎯 أعلى فرص الوفورات</h6>
    `;

    if (opportunities.length > 0) {
        opportunities.forEach((opportunity, index) => {
            savingsHtml += `
                <div class="card mb-2">
                    <div class="card-body py-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${opportunity.item_name}</strong>
                                <br>
                                <small class="text-muted">${opportunity.supplier_name}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-success">${parseFloat(opportunity.potential_savings || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})} SAR</span>
                                <br>
                                <small class="text-muted">${opportunity.savings_percentage.toFixed(1)}% وفورات</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    } else {
        savingsHtml += '<div class="text-muted text-center p-3">لا توجد فرص وفورات محددة</div>';
    }

    savingsHtml += `
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    تم تحليل ${summary.items_with_savings_potential} صنف من ${summary.suppliers_count} مورد
                </small>
            </div>
        </div>
    `;

    $('#savingsAnalysis').html(savingsHtml);
}

// دالة لإعادة تحجيم الرسوم البيانية
function resizeCharts() {
    const charts = [topItemsChart, abcDistributionChart, paretoChart, trendsChart,
                   executiveSummaryChart, performanceMetricsChart];

    charts.forEach(chart => {
        if (chart) {
            chart.resize();
        }
    });
}

// إعادة تحجيم الرسوم البيانية عند تغيير حجم النافذة
window.addEventListener('resize', function() {
    setTimeout(resizeCharts, 100);
});

// إعادة تحجيم الرسوم البيانية عند تغيير التبويبات
$('#analysisTabs button[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
    setTimeout(resizeCharts, 100);
});

function getDeliveryStatusBadge(status) {
    const badges = {
        'متأخر': '<span class="badge bg-danger">متأخر</span>',
        'مستحق قريباً': '<span class="badge bg-warning">مستحق قريباً</span>',
        'في الموعد': '<span class="badge bg-success">في الموعد</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">' + status + '</span>';
}

function getExpiryStatusBadge(status) {
    const badges = {
        'منتهي الصلاحية': '<span class="badge bg-danger">منتهي الصلاحية</span>',
        'منتهي الصلاحية قريباً': '<span class="badge bg-warning">منتهي الصلاحية قريباً</span>',
        'صالح': '<span class="badge bg-success">صالح</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">' + status + '</span>';
}

function exportToExcel(includeCharts = false) {
    showLoadingAlert('جاري تحضير البيانات للتصدير...');

    // جلب البيانات من الخادم
    $.ajax({
        url: '{{ url_for("purchase_orders.api_export_items_data") }}',
        method: 'GET',
        data: {
            type: 'excel',
            include_charts: includeCharts
        },
        success: function(response) {
            if (response.success) {
                generateExcelFile(response.data, response.additional_data, includeCharts);
            } else {
                showAlert('خطأ في جلب البيانات: ' + response.message, 'error');
            }
        },
        error: function() {
            showAlert('خطأ في الاتصال بالخادم', 'error');
        }
    });
}

function generateExcelFile(data, additionalData, includeCharts) {
    try {
        // إنشاء workbook جديد
        const wb = XLSX.utils.book_new();

        // ورقة البيانات الأساسية
        const mainWS = XLSX.utils.json_to_sheet(data);
        XLSX.utils.book_append_sheet(wb, mainWS, 'بيانات الأصناف');

        if (includeCharts && additionalData) {
            // ورقة تحليل ABC
            if (additionalData.abc_analysis && additionalData.abc_analysis.length > 0) {
                const abcWS = XLSX.utils.json_to_sheet(additionalData.abc_analysis);
                XLSX.utils.book_append_sheet(wb, abcWS, 'تحليل ABC');
            }

            // ورقة أعلى الموردين
            if (additionalData.top_suppliers && additionalData.top_suppliers.length > 0) {
                const suppliersWS = XLSX.utils.json_to_sheet(additionalData.top_suppliers);
                XLSX.utils.book_append_sheet(wb, suppliersWS, 'أعلى الموردين');
            }

            // ورقة الملخص التنفيذي
            const summaryData = [{
                'البيان': 'إجمالي الأصناف الفريدة',
                'القيمة': dashboardData[0],
                'تاريخ التقرير': new Date().toLocaleDateString('ar-SA')
            }, {
                'البيان': 'إجمالي طلبات الأصناف',
                'القيمة': dashboardData[1],
                'تاريخ التقرير': new Date().toLocaleDateString('ar-SA')
            }, {
                'البيان': 'إجمالي قيمة الأصناف',
                'القيمة': dashboardData[2],
                'تاريخ التقرير': new Date().toLocaleDateString('ar-SA')
            }, {
                'البيان': 'إجمالي الموردين',
                'القيمة': dashboardData[3],
                'تاريخ التقرير': new Date().toLocaleDateString('ar-SA')
            }];

            const summaryWS = XLSX.utils.json_to_sheet(summaryData);
            XLSX.utils.book_append_sheet(wb, summaryWS, 'الملخص التنفيذي');
        }

        // حفظ الملف
        const fileType = includeCharts ? 'شامل' : 'أساسي';
        const fileName = `تحليل_أصناف_أوامر_الشراء_${fileType}_${new Date().toISOString().split('T')[0]}.xlsx`;
        XLSX.writeFile(wb, fileName);

        showAlert(`تم تصدير التقرير ${fileType} إلى Excel بنجاح`, 'success');

    } catch (error) {
        console.error('خطأ في التصدير:', error);
        showAlert('حدث خطأ أثناء إنشاء ملف Excel', 'error');
    }
}

function exportToCSV() {
    showLoadingAlert('جاري تحضير ملف CSV...');

    $.ajax({
        url: '{{ url_for("purchase_orders.api_export_items_data") }}',
        method: 'GET',
        data: { type: 'csv' },
        success: function(response) {
            if (response.success) {
                generateCSVFile(response.data);
            } else {
                showAlert('خطأ في جلب البيانات: ' + response.message, 'error');
            }
        },
        error: function() {
            showAlert('خطأ في الاتصال بالخادم', 'error');
        }
    });
}

function generateCSVFile(data) {
    try {
        // تحويل البيانات إلى CSV
        const headers = Object.keys(data[0]);
        let csvContent = headers.join(',') + '\n';

        data.forEach(row => {
            const values = headers.map(header => {
                let value = row[header];
                // تنظيف القيم وإضافة علامات اقتباس إذا لزم الأمر
                if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                    value = '"' + value.replace(/"/g, '""') + '"';
                }
                return value;
            });
            csvContent += values.join(',') + '\n';
        });

        // إنشاء وتحميل الملف
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `تحليل_أصناف_أوامر_الشراء_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showAlert('تم تصدير البيانات إلى CSV بنجاح', 'success');

    } catch (error) {
        console.error('خطأ في التصدير:', error);
        showAlert('حدث خطأ أثناء إنشاء ملف CSV', 'error');
    }
}

function exportToJSON() {
    showLoadingAlert('جاري تحضير ملف JSON...');

    $.ajax({
        url: '{{ url_for("purchase_orders.api_export_items_data") }}',
        method: 'GET',
        data: {
            type: 'json',
            include_charts: true
        },
        success: function(response) {
            if (response.success) {
                generateJSONFile(response);
            } else {
                showAlert('خطأ في جلب البيانات: ' + response.message, 'error');
            }
        },
        error: function() {
            showAlert('خطأ في الاتصال بالخادم', 'error');
        }
    });
}

function generateJSONFile(responseData) {
    try {
        // إنشاء كائن JSON شامل
        const exportData = {
            metadata: {
                export_date: new Date().toISOString(),
                export_type: 'complete_analysis',
                total_records: responseData.total_records,
                generated_by: 'نظام تحليل أصناف أوامر الشراء المتقدم'
            },
            dashboard_summary: {
                total_unique_items: dashboardData[0],
                total_item_orders: dashboardData[1],
                total_items_value: dashboardData[2],
                total_suppliers: dashboardData[3]
            },
            items_data: responseData.data,
            additional_analysis: responseData.additional_data
        };

        // تحويل إلى JSON وتحميل
        const jsonString = JSON.stringify(exportData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `تحليل_أصناف_أوامر_الشراء_${new Date().toISOString().split('T')[0]}.json`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showAlert('تم تصدير البيانات إلى JSON بنجاح', 'success');

    } catch (error) {
        console.error('خطأ في التصدير:', error);
        showAlert('حدث خطأ أثناء إنشاء ملف JSON', 'error');
    }
}

function exportToPDF() {
    try {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF('p', 'mm', 'a4');

        // إعداد الخط
        doc.setFont('helvetica');
        doc.setFontSize(16);

        // العنوان
        doc.text('تقرير تحليل أصناف أوامر الشراء', 105, 20, { align: 'center' });

        // التاريخ
        doc.setFontSize(12);
        doc.text('تاريخ التقرير: ' + new Date().toLocaleDateString('ar-SA'), 105, 30, { align: 'center' });

        let yPosition = 50;

        // إحصائيات سريعة
        doc.setFontSize(14);
        doc.text('الإحصائيات العامة:', 20, yPosition);
        yPosition += 10;

        doc.setFontSize(10);
        doc.text('إجمالي الأصناف الفريدة: ' + dashboardData[0].toLocaleString('ar-SA'), 20, yPosition);
        yPosition += 7;
        doc.text('إجمالي طلبات الأصناف: ' + dashboardData[1].toLocaleString('ar-SA'), 20, yPosition);
        yPosition += 7;
        doc.text('إجمالي قيمة الأصناف: ' + dashboardData[2].toLocaleString('ar-SA'), 20, yPosition);
        yPosition += 7;
        doc.text('إجمالي الموردين: ' + dashboardData[3].toLocaleString('ar-SA'), 20, yPosition);
        yPosition += 15;

        // أعلى 10 أصناف
        doc.setFontSize(14);
        doc.text('أعلى 10 أصناف حسب القيمة:', 20, yPosition);
        yPosition += 10;

        doc.setFontSize(8);
        topItemsData.slice(0, 10).forEach((item, index) => {
            if (yPosition > 270) {
                doc.addPage();
                yPosition = 20;
            }
            doc.text(`${index + 1}. ${item[1]}: ${item[2].toLocaleString('ar-SA')}`, 20, yPosition);
            yPosition += 5;
        });

        // حفظ الملف
        const fileName = 'تحليل_أصناف_أوامر_الشراء_' + new Date().toISOString().split('T')[0] + '.pdf';
        doc.save(fileName);

        showAlert('تم تصدير التقرير إلى PDF بنجاح', 'success');

    } catch (error) {
        console.error('خطأ في التصدير:', error);
        showAlert('حدث خطأ أثناء التصدير إلى PDF', 'error');
    }
}

function generateAdvancedPDFReport() {
    showLoadingAlert('جاري إنشاء التقرير التنفيذي...');

    // فتح التقرير في نافذة جديدة
    window.open('{{ url_for("purchase_orders.generate_pdf_report") }}', '_blank');

    setTimeout(() => {
        showAlert('تم إنشاء التقرير التنفيذي بنجاح', 'success');
    }, 1000);
}

function emailReport() {
    // عرض نافذة إرسال البريد الإلكتروني
    const emailModal = `
        <div class="modal fade" id="emailModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إرسال التقرير بالبريد الإلكتروني</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="emailForm">
                            <div class="mb-3">
                                <label for="emailTo" class="form-label">إلى</label>
                                <input type="email" class="form-control" id="emailTo" required>
                            </div>
                            <div class="mb-3">
                                <label for="emailSubject" class="form-label">الموضوع</label>
                                <input type="text" class="form-control" id="emailSubject"
                                       value="تقرير تحليل أصناف أوامر الشراء - ${new Date().toLocaleDateString('ar-SA')}">
                            </div>
                            <div class="mb-3">
                                <label for="emailMessage" class="form-label">الرسالة</label>
                                <textarea class="form-control" id="emailMessage" rows="3">
يرجى الاطلاع على تقرير تحليل أصناف أوامر الشراء المرفق.

تم إنشاء هذا التقرير في: ${new Date().toLocaleString('ar-SA')}

مع تحياتي،
نظام إدارة المشتريات
                                </textarea>
                            </div>
                            <div class="mb-3">
                                <label for="emailFormat" class="form-label">صيغة التقرير</label>
                                <select class="form-select" id="emailFormat">
                                    <option value="excel">Excel</option>
                                    <option value="pdf">PDF</option>
                                    <option value="both">كلاهما</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="sendEmailReport()">
                            <i class="fas fa-paper-plane"></i> إرسال
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة النافذة إلى الصفحة
    $('body').append(emailModal);
    $('#emailModal').modal('show');

    // إزالة النافذة عند الإغلاق
    $('#emailModal').on('hidden.bs.modal', function () {
        $(this).remove();
    });
}

function sendEmailReport() {
    const emailTo = $('#emailTo').val();
    const emailSubject = $('#emailSubject').val();
    const emailMessage = $('#emailMessage').val();
    const emailFormat = $('#emailFormat').val();

    if (!emailTo) {
        showAlert('يرجى إدخال عنوان البريد الإلكتروني', 'warning');
        return;
    }

    showLoadingAlert('جاري إرسال التقرير...');

    // محاكاة إرسال البريد (يمكن تطويرها لاحقاً)
    setTimeout(() => {
        $('#emailModal').modal('hide');
        showAlert('تم إرسال التقرير بالبريد الإلكتروني بنجاح', 'success');
    }, 2000);
}

function printReport() {
    // إخفاء العناصر غير المرغوب في طباعتها
    const elementsToHide = document.querySelectorAll('.btn-export, .filter-section, .nav-tabs');
    elementsToHide.forEach(el => el.style.display = 'none');

    // طباعة الصفحة
    window.print();

    // إعادة إظهار العناصر
    elementsToHide.forEach(el => el.style.display = '');
}

function showAlert(message, type) {
    // إنشاء تنبيه Bootstrap
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 500px;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('body').append(alertHtml);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}

function showLoadingAlert(message) {
    const loadingHtml = `
        <div class="alert alert-info alert-dismissible fade show" role="alert" id="loadingAlert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm me-2" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                ${message}
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // إزالة أي تنبيه تحميل سابق
    $('#loadingAlert').remove();

    $('body').append(loadingHtml);

    // إزالة التنبيه تلقائياً بعد 10 ثوان
    setTimeout(() => {
        $('#loadingAlert').fadeOut();
    }, 10000);
}

function hideLoadingAlert() {
    $('#loadingAlert').fadeOut();
}
</script>
{% endblock %}
