{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- شريط الأدوات العلوي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i>
                            طلبات الشراء
                        </h5>
                        <div class="btn-group">
                            <a href="{{ url_for('purchase_requests.new') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>طلب جديد
                            </a>
                            <a href="{{ url_for('purchase_requests.search_page') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-search me-1"></i>بحث متقدم
                            </a>
                            <a href="{{ url_for('purchase_requests.reports_page') }}" class="btn btn-outline-info">
                                <i class="fas fa-chart-bar me-1"></i>التقارير
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- شريط البحث السريع -->
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <input type="text" name="req_no" class="form-control" 
                                   placeholder="رقم الطلب" value="{{ request.args.get('req_no', '') }}">
                        </div>
                        <div class="col-md-3">
                            <input type="text" name="requester_name" class="form-control" 
                                   placeholder="اسم الطالب" value="{{ request.args.get('requester_name', '') }}">
                        </div>
                        <div class="col-md-2">
                            <select name="status" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="مسودة" {{ 'selected' if request.args.get('status') == 'مسودة' }}>مسودة</option>
                                <option value="معتمد" {{ 'selected' if request.args.get('status') == 'معتمد' }}>معتمد</option>
                                <option value="مرفوض" {{ 'selected' if request.args.get('status') == 'مرفوض' }}>مرفوض</option>
                                <option value="قيد المراجعة" {{ 'selected' if request.args.get('status') == 'قيد المراجعة' }}>قيد المراجعة</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select name="priority" class="form-select">
                                <option value="">جميع الأولويات</option>
                                <option value="عادي" {{ 'selected' if request.args.get('priority') == 'عادي' }}>عادي</option>
                                <option value="مهم" {{ 'selected' if request.args.get('priority') == 'مهم' }}>مهم</option>
                                <option value="عاجل" {{ 'selected' if request.args.get('priority') == 'عاجل' }}>عاجل</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-outline-primary w-100">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي الطلبات</h6>
                            <h3 class="stats-number">{{ stats.total_requests if stats else 0 }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file-alt fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">الطلبات المعتمدة</h6>
                            <h3 class="stats-number">{{ stats.approved_requests if stats else 0 }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">قيد المراجعة</h6>
                            <h3 class="stats-number">{{ stats.pending_requests if stats else 0 }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي القيمة</h6>
                            <h3 class="stats-number">{{ "{:,.2f}".format(stats.total_value) if stats else '0.00' }}</h3>
                            <small>ريال سعودي</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الطلبات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">قائمة طلبات الشراء</h6>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th width="80">م</th>
                                    <th width="120">رقم الطلب</th>
                                    <th width="100">تاريخ الطلب</th>
                                    <th>اسم الطالب</th>
                                    <th width="100">الحالة</th>
                                    <th width="100">الأولوية</th>
                                    <th width="120">إجمالي المبلغ</th>
                                    <th width="150">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if requests %}
                                    {% for request in requests %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            <strong>{{ request.req_no }}</strong>
                                        </td>
                                        <td>{{ request.req_date.strftime('%Y-%m-%d') if request.req_date else '-' }}</td>
                                        <td>{{ request.requester_name or '-' }}</td>
                                        <td>
                                            {% if request.req_status == 'معتمد' %}
                                                <span class="badge bg-success">{{ request.req_status }}</span>
                                            {% elif request.req_status == 'مرفوض' %}
                                                <span class="badge bg-danger">{{ request.req_status }}</span>
                                            {% elif request.req_status == 'قيد المراجعة' %}
                                                <span class="badge bg-warning">{{ request.req_status }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ request.req_status or 'مسودة' }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if request.priority == 'عاجل' %}
                                                <span class="badge bg-danger">{{ request.priority }}</span>
                                            {% elif request.priority == 'مهم' %}
                                                <span class="badge bg-warning">{{ request.priority }}</span>
                                            {% else %}
                                                <span class="badge bg-info">{{ request.priority or 'عادي' }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <strong>{{ "{:,.2f}".format(request.total_amount or 0) }} ريال</strong>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ url_for('purchase_requests.view', id=request.id) }}" 
                                                   class="btn btn-outline-primary" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ url_for('purchase_requests.edit', id=request.id) }}" 
                                                   class="btn btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{{ url_for('purchase_requests.delete', id=request.id) }}" 
                                                   class="btn btn-outline-danger" title="حذف"
                                                   onclick="return confirm('هل أنت متأكد من حذف هذا الطلب؟')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                                <p>لا توجد طلبات شراء</p>
                                                <a href="{{ url_for('purchase_requests.new') }}" class="btn btn-primary">
                                                    <i class="fas fa-plus me-1"></i>إنشاء طلب جديد
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- التصفح -->
    {% if pagination %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="تصفح الصفحات">
                <ul class="pagination justify-content-center">
                    {% if pagination.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('purchase_requests.index', page=pagination.prev_num, **request.args) }}">السابق</a>
                        </li>
                    {% endif %}
                    
                    {% for page_num in pagination.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != pagination.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('purchase_requests.index', page=page_num, **request.args) }}">{{ page_num }}</a>
                                </li>
                            {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                            {% endif %}
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if pagination.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('purchase_requests.index', page=pagination.next_num, **request.args) }}">التالي</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>

<style>
.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.badge {
    font-size: 0.75em;
}

.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}
</style>
{% endblock %}
