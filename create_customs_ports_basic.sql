-- إن<PERSON><PERSON><PERSON> جدول المنافذ الجمركية الأساسي فقط
-- ملف مبسط للبدء السريع

-- إن<PERSON>اء التسلسل
CREATE SEQUENCE customs_ports_seq START WITH 1 INCREMENT BY 1;

-- <PERSON><PERSON><PERSON><PERSON><PERSON> الجدول الأساسي
CREATE TABLE customs_ports (
    id NUMBER PRIMARY KEY,
    port_code VARCHAR2(20) UNIQUE NOT NULL,
    port_name_ar VARCHAR2(200) NOT NULL,
    port_name_en VARCHAR2(200),
    port_type VARCHAR2(50) NOT NULL, -- 'SEA', 'AIR', 'LAND', 'DRY_PORT'
    country VARCHAR2(100) NOT NULL,
    city VARCHAR2(100) NOT NULL,
    region VARCHAR2(100),
    customs_authority VARCHAR2(200),
    contact_phone VARCHAR2(50),
    contact_email VARCHAR2(100),
    is_active NUMBER(1) DEFAULT 1,
    is_24_hours NUMBER(1) DEFAULT 0,
    has_customs_clearance NUMBER(1) DEFAULT 1,
    has_quarantine NUMBER(1) DEFAULT 0,
    has_warehouse NUMBER(1) DEFAULT 0,
    working_hours VARCHAR2(200),
    notes CLOB,
    created_at DATE DEFAULT SYSDATE,
    updated_at DATE DEFAULT SYSDATE
);

-- إنشاء الفهارس الأساسية
CREATE INDEX idx_customs_ports_type ON customs_ports(port_type);
CREATE INDEX idx_customs_ports_country ON customs_ports(country);
CREATE INDEX idx_customs_ports_active ON customs_ports(is_active);

-- إدراج البيانات التجريبية
INSERT INTO customs_ports (
    id, port_code, port_name_ar, port_name_en, port_type, 
    country, city, region, customs_authority, contact_phone, 
    is_active, is_24_hours, has_customs_clearance, has_quarantine, has_warehouse
) VALUES (
    customs_ports_seq.NEXTVAL, 'JEDDAH_SEA', 'ميناء جدة الإسلامي', 'Jeddah Islamic Port', 'SEA',
    'السعودية', 'جدة', 'مكة المكرمة', 'الهيئة العامة للجمارك', '+966-12-6361000',
    1, 1, 1, 1, 1
);

INSERT INTO customs_ports (
    id, port_code, port_name_ar, port_name_en, port_type, 
    country, city, region, customs_authority, contact_phone, 
    is_active, is_24_hours, has_customs_clearance, has_quarantine, has_warehouse
) VALUES (
    customs_ports_seq.NEXTVAL, 'DAMMAM_SEA', 'ميناء الملك عبدالعزيز', 'King Abdulaziz Port', 'SEA',
    'السعودية', 'الدمام', 'المنطقة الشرقية', 'الهيئة العامة للجمارك', '+966-13-8576000',
    1, 1, 1, 1, 1
);

INSERT INTO customs_ports (
    id, port_code, port_name_ar, port_name_en, port_type, 
    country, city, region, customs_authority, contact_phone, 
    is_active, is_24_hours, has_customs_clearance, has_quarantine, has_warehouse
) VALUES (
    customs_ports_seq.NEXTVAL, 'RIYADH_AIR', 'مطار الملك خالد الدولي', 'King Khalid International Airport', 'AIR',
    'السعودية', 'الرياض', 'الرياض', 'الهيئة العامة للجمارك', '+966-11-2211000',
    1, 1, 1, 1, 1
);

INSERT INTO customs_ports (
    id, port_code, port_name_ar, port_name_en, port_type, 
    country, city, region, customs_authority, contact_phone, 
    is_active, is_24_hours, has_customs_clearance, has_quarantine, has_warehouse
) VALUES (
    customs_ports_seq.NEXTVAL, 'JEDDAH_AIR', 'مطار الملك عبدالعزيز الدولي', 'King Abdulaziz International Airport', 'AIR',
    'السعودية', 'جدة', 'مكة المكرمة', 'الهيئة العامة للجمارك', '+966-12-6844000',
    1, 1, 1, 1, 1
);

INSERT INTO customs_ports (
    id, port_code, port_name_ar, port_name_en, port_type, 
    country, city, region, customs_authority, contact_phone, 
    is_active, is_24_hours, has_customs_clearance, has_quarantine, has_warehouse
) VALUES (
    customs_ports_seq.NEXTVAL, 'HADITHA_LAND', 'منفذ الحديثة البري', 'Haditha Land Port', 'LAND',
    'السعودية', 'الحديثة', 'الحدود الشمالية', 'الهيئة العامة للجمارك', '+966-14-6221000',
    1, 0, 1, 0, 1
);

INSERT INTO customs_ports (
    id, port_code, port_name_ar, port_name_en, port_type, 
    country, city, region, customs_authority, contact_phone, 
    is_active, is_24_hours, has_customs_clearance, has_quarantine, has_warehouse
) VALUES (
    customs_ports_seq.NEXTVAL, 'YANBU_SEA', 'ميناء ينبع التجاري', 'Yanbu Commercial Port', 'SEA',
    'السعودية', 'ينبع', 'المدينة المنورة', 'الهيئة العامة للجمارك', '+966-14-3912000',
    1, 0, 1, 0, 1
);

INSERT INTO customs_ports (
    id, port_code, port_name_ar, port_name_en, port_type, 
    country, city, region, customs_authority, contact_phone, 
    is_active, is_24_hours, has_customs_clearance, has_quarantine, has_warehouse
) VALUES (
    customs_ports_seq.NEXTVAL, 'JIZAN_SEA', 'ميناء جازان', 'Jazan Port', 'SEA',
    'السعودية', 'جازان', 'جازان', 'الهيئة العامة للجمارك', '+966-17-3221000',
    1, 0, 1, 1, 1
);

COMMIT;

-- عرض النتائج
SELECT 'تم إنشاء جدول المنافذ الجمركية بنجاح!' as message FROM dual;
SELECT COUNT(*) as total_ports FROM customs_ports;
SELECT port_code, port_name_ar, port_type FROM customs_ports ORDER BY port_name_ar;
