#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحليل دقيق لآلية تحديث حالة أوامر الشراء
"""

import sys
sys.path.append('.')
from oracle_manager import OracleManager

def analyze_po_update_logic():
    """تحليل آلية تحديث حالة أوامر الشراء"""
    print('🔍 تحليل دقيق لآلية تحديث حالة أوامر الشراء...')

    try:
        oracle = OracleManager()
        
        # فحص الوضع الحالي مع تفاصيل أكثر
        detailed_analysis = """
            SELECT 
                po.ID,
                po.PO_NUMBER,
                po.STATUS as current_po_status,
                po.IS_USED,
                po.USED_AT,
                COUNT(cs.id) as total_shipments,
                MIN(cs.created_at) as first_shipment_date,
                MAX(cs.status_updated_at) as last_status_update
            FROM PURCHASE_ORDERS po
            LEFT JOIN cargo_shipments cs ON po.ID = cs.purchase_order_id
            GROUP BY po.ID, po.PO_NUMBER, po.STATUS, po.IS_USED, po.USED_AT
            ORDER BY po.ID
        """
        
        results = oracle.execute_query(detailed_analysis)
        
        print('📋 تحليل مفصل:')
        issues_found = []
        
        for result in results:
            po_id, po_number, current_status, is_used, used_at, total_shipments, first_shipment, last_update = result
            
            print(f'\n📦 {po_number}:')
            print(f'   الحالة الحالية: {current_status}')
            print(f'   مستخدم: {is_used}')
            print(f'   عدد الشحنات: {total_shipments}')
            
            # الحصول على تفاصيل الشحنات
            if total_shipments > 0:
                shipments_query = """
                    SELECT tracking_number, shipment_status, created_at, status_updated_at
                    FROM cargo_shipments
                    WHERE purchase_order_id = :1
                    ORDER BY created_at
                """
                shipments = oracle.execute_query(shipments_query, [po_id])
                
                print(f'   الشحنات:')
                for shipment in shipments:
                    tracking, status, created, updated = shipment
                    print(f'     - {tracking}: {status} (تم إنشاؤها: {created})')
                
                # الحصول على الحالة المتوقعة
                expected_query = """
                    SELECT m.po_status, cs.shipment_status, m.priority_order
                    FROM cargo_shipments cs
                    JOIN po_shipment_status_map m ON cs.shipment_status = m.shipment_status
                    WHERE cs.purchase_order_id = :1
                    AND m.auto_update = 1 AND m.is_active = 1
                    ORDER BY m.priority_order DESC, cs.status_updated_at DESC
                    FETCH FIRST 1 ROWS ONLY
                """
                expected_result = oracle.execute_query(expected_query, [po_id])
                
                if expected_result:
                    expected_status, ship_status, priority = expected_result[0]
                    print(f'   الحالة المتوقعة: {expected_status} (بناءً على شحنة: {ship_status})')
                else:
                    expected_status = 'غير محدد'
                    print(f'   الحالة المتوقعة: غير محدد (لا يوجد ربط)')
            else:
                expected_status = 'مسودة'
                print(f'   الحالة المتوقعة: مسودة (لا توجد شحنات)')
            
            # تحديد المشاكل
            po_issues = []
            if total_shipments == 0:
                if current_status != 'مسودة':
                    po_issues.append(f'يجب أن تكون الحالة "مسودة" وليس "{current_status}"')
                if is_used != 0:
                    po_issues.append(f'يجب أن يكون IS_USED = 0 وليس {is_used}')
            else:
                if expected_status != 'غير محدد' and current_status != expected_status:
                    po_issues.append(f'يجب أن تكون الحالة "{expected_status}" وليس "{current_status}"')
                if is_used != 1:
                    po_issues.append(f'يجب أن يكون IS_USED = 1 وليس {is_used}')
            
            if po_issues:
                print(f'   ❌ مشاكل:')
                for issue in po_issues:
                    print(f'      - {issue}')
                issues_found.extend([(po_number, issue) for issue in po_issues])
            else:
                print(f'   ✅ صحيح')
        
        # فحص بيانات الربط
        print(f'\n🔗 فحص بيانات الربط:')
        mapping_check = """
            SELECT shipment_status, po_status, priority_order, auto_update, is_active
            FROM po_shipment_status_map
            ORDER BY priority_order
        """
        
        mappings = oracle.execute_query(mapping_check)
        for mapping in mappings:
            ship_status, po_status, priority, auto_update, is_active = mapping
            status_icon = '✅' if auto_update == 1 and is_active == 1 else '❌'
            print(f'   {status_icon} {ship_status} → {po_status} (أولوية: {priority})')
        
        # فحص الـ Triggers
        print(f'\n⚡ فحص الـ Triggers:')
        triggers_check = """
            SELECT trigger_name, status, triggering_event
            FROM user_triggers
            WHERE trigger_name LIKE 'TRG_PO_STATUS%'
            ORDER BY trigger_name
        """
        
        triggers = oracle.execute_query(triggers_check)
        for trigger in triggers:
            name, status, event = trigger
            status_icon = '✅' if status == 'ENABLED' else '❌'
            print(f'   {status_icon} {name}: {event} ({status})')
        
        oracle.close()
        
        print(f'\n📊 ملخص التحليل:')
        print(f'   إجمالي المشاكل: {len(issues_found)}')
        
        if issues_found:
            print(f'   المشاكل المحددة:')
            for po_name, issue in issues_found:
                print(f'     - {po_name}: {issue}')
        else:
            print(f'   ✅ لا توجد مشاكل منطقية')
        
        return issues_found
        
    except Exception as e:
        print(f'❌ خطأ في التحليل: {e}')
        return []

def identify_root_cause():
    """تحديد السبب الجذري للمشكلة"""
    print('\n🔍 تحديد السبب الجذري للمشكلة...')
    
    try:
        oracle = OracleManager()
        
        # فحص تاريخ التحديثات
        print('📚 فحص تاريخ التحديثات:')
        history_check = """
            SELECT 
                po.PO_NUMBER,
                h.old_status,
                h.new_status,
                h.shipment_status,
                h.change_reason,
                h.changed_at,
                h.auto_updated
            FROM po_status_history h
            JOIN PURCHASE_ORDERS po ON h.po_id = po.ID
            ORDER BY h.changed_at DESC
            FETCH FIRST 10 ROWS ONLY
        """
        
        history = oracle.execute_query(history_check)
        for record in history:
            po_number, old_status, new_status, ship_status, reason, changed_at, auto_updated = record
            auto_icon = '🤖' if auto_updated == 1 else '👤'
            print(f'   {auto_icon} {po_number}: {old_status} → {new_status} ({ship_status}) - {reason}')
        
        # فحص الدالة الحالية
        print(f'\n🔧 فحص الدالة الحالية:')
        function_check = """
            SELECT object_name, status, last_ddl_time
            FROM user_objects
            WHERE object_type = 'FUNCTION'
            AND object_name = 'SYNC_PO_STATUS'
        """
        
        function_info = oracle.execute_query(function_check)
        if function_info:
            name, status, last_modified = function_info[0]
            status_icon = '✅' if status == 'VALID' else '❌'
            print(f'   {status_icon} {name}: {status} (آخر تعديل: {last_modified})')
        else:
            print(f'   ❌ الدالة غير موجودة')
        
        # اختبار الدالة مباشرة
        print(f'\n🧪 اختبار الدالة مباشرة:')
        test_po_query = """
            SELECT ID, PO_NUMBER, STATUS
            FROM PURCHASE_ORDERS
            ORDER BY ID
        """
        
        test_pos = oracle.execute_query(test_po_query)
        for po in test_pos:
            po_id, po_number, current_status = po
            
            try:
                # استدعاء الدالة
                function_result = oracle.execute_query(f"SELECT sync_po_status({po_id}) FROM dual")
                if function_result:
                    result_status = function_result[0][0]
                    
                    # التحقق من التحديث الفعلي
                    actual_result = oracle.execute_query(f"SELECT STATUS FROM PURCHASE_ORDERS WHERE ID = {po_id}")
                    actual_status = actual_result[0][0] if actual_result else None
                    
                    if result_status == actual_status:
                        print(f'   ✅ {po_number}: {current_status} → {result_status}')
                    else:
                        print(f'   ❌ {po_number}: دالة={result_status}, فعلي={actual_status}')
                
            except Exception as e:
                print(f'   ❌ {po_number}: خطأ في الدالة - {e}')
        
        oracle.close()
        
    except Exception as e:
        print(f'❌ خطأ في تحديد السبب: {e}')

def suggest_improvements():
    """اقتراح تحسينات"""
    print('\n💡 اقتراحات التحسين:')
    
    print('1. 🎯 تحسين منطق الأولوية:')
    print('   - استخدام آخر تحديث زمني بدلاً من الأولوية فقط')
    print('   - مراعاة حالات خاصة (ملغي، مرتجع)')
    
    print('2. 🔄 تحسين آلية التحديث:')
    print('   - التحقق من صحة البيانات قبل التحديث')
    print('   - إضافة آلية rollback في حالة الخطأ')
    
    print('3. 📊 تحسين المراقبة:')
    print('   - إضافة logs مفصلة')
    print('   - إنشاء تقارير دورية للتحقق من التطابق')
    
    print('4. 🛡️ تحسين الأمان:')
    print('   - التحقق من صحة المدخلات')
    print('   - منع التحديثات المتضاربة')

if __name__ == '__main__':
    # تحليل المشاكل
    issues = analyze_po_update_logic()
    
    # تحديد السبب الجذري
    identify_root_cause()
    
    # اقتراح التحسينات
    suggest_improvements()
    
    if issues:
        print(f'\n⚠️ تم العثور على {len(issues)} مشكلة تحتاج لإصلاح')
    else:
        print(f'\n✅ النظام يعمل بشكل صحيح')
