<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أنواع العمولات - نظام الفوجي</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/commission-opening-balances-style.css') }}" rel="stylesheet">
</head>
<body>

    <!-- Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="fas fa-percentage me-3"></i>
                        أنواع العمولات
                    </h1>
                    <p class="page-subtitle">
                        تهيئة وإدارة أنواع العمولات المختلفة وطرق حسابها
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{{ url_for('purchase_commissions.index') }}" class="btn btn-light btn-modern">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="breadcrumb-container">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('purchase_commissions.index') }}">عمولات المندوبين</a></li>
                    <li class="breadcrumb-item active" aria-current="page">أنواع العمولات</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="container">

        <!-- Control Panel -->
        <div class="control-panel">
            <h5>
                <i class="fas fa-cogs"></i>
                إجراءات سريعة
            </h5>
            <div class="row g-3">
                <div class="col-md-3">
                    <button type="button" class="btn btn-primary btn-modern w-100" data-bs-toggle="modal" data-bs-target="#addTypeModal">
                        <i class="fas fa-plus"></i>
                        إضافة نوع جديد
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-success btn-modern w-100" onclick="exportData()">
                        <i class="fas fa-download"></i>
                        تصدير البيانات
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-info btn-modern w-100" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث البيانات
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-warning btn-modern w-100" onclick="printReport()">
                        <i class="fas fa-print"></i>
                        طباعة التقرير
                    </button>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="stats-container">
            <div class="row g-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon primary">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="stat-value">{{ commission_types|length }}</div>
                        <div class="stat-label">إجمالي الأنواع</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-value">{{ commission_types|selectattr('is_active', 'equalto', true)|list|length }}</div>
                        <div class="stat-label">الأنواع النشطة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon info">
                            <i class="fas fa-cubes"></i>
                        </div>
                        <div class="stat-value">{{ commission_types|selectattr('calculation_method', 'equalto', 'QUANTITY')|list|length }}</div>
                        <div class="stat-label">أنواع الكمية</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon warning">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-value">{{ (commission_types|selectattr('calculation_method', 'equalto', 'PERCENTAGE')|list|length) + (commission_types|selectattr('calculation_method', 'equalto', 'TIERED')|list|length) + (commission_types|selectattr('calculation_method', 'equalto', 'FIXED')|list|length) }}</div>
                        <div class="stat-label">أنواع القيمة</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Data Table -->
        <div class="data-table-container">
            <div class="data-table-header">
                <h5 class="data-table-title">
                    <i class="fas fa-table"></i>
                    أنواع العمولات المتاحة
                </h5>
                <div>
                    <button class="btn btn-primary btn-modern btn-sm" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-modern" id="commissionTypesTable">
                    <thead>
                        <tr>
                            <th>النوع</th>
                            <th>طريقة الحساب</th>
                            <th>الوصف</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for type in commission_types %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon primary me-2" style="width: 40px; height: 40px; font-size: 1rem;">
                                        <i class="fas fa-percentage"></i>
                                    </div>
                                    <div>
                                        <strong>{{ type.type_name }}</strong>
                                        {% if type.type_name_en %}
                                        <br><small class="text-muted">{{ type.type_name_en }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if type.calculation_method == 'FIXED' %}
                                <span class="badge badge-modern bg-primary">ثابت</span>
                                {% elif type.calculation_method == 'PERCENTAGE' %}
                                <span class="badge badge-modern bg-success">نسبة مئوية</span>
                                {% elif type.calculation_method == 'TIERED' %}
                                <span class="badge badge-modern bg-info">متدرج</span>
                                {% elif type.calculation_method == 'QUANTITY' %}
                                <span class="badge badge-modern bg-warning">كمية</span>
                                {% else %}
                                <span class="badge badge-modern bg-secondary">{{ type.calculation_method }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ type.description or 'لا يوجد وصف' }}</small>
                            </td>
                            <td>
                                {% if type.is_active %}
                                <span class="badge badge-modern bg-success">نشط</span>
                                {% else %}
                                <span class="badge badge-modern bg-secondary">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-warning btn-modern btn-sm" onclick="editCommissionType({{ type.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger btn-modern btn-sm"
                                            onclick="deleteCommissionType({{ type.id }}, '{{ type.type_name }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function refreshData() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            button.disabled = true;

            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                showAlert('تم تحديث البيانات بنجاح', 'success');
            }, 2000);
        }

        function exportData() {
            showAlert('سيتم تصدير البيانات قريباً', 'info');
        }

        function printReport() {
            window.print();
        }

        function deleteCommissionType(id, name) {
            if (confirm(`هل أنت متأكد من حذف نوع العمولة "${name}"؟`)) {
                // إضافة منطق الحذف هنا
                showAlert(`تم حذف نوع العمولة "${name}" بنجاح`, 'success');
            }
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-modern alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'info' ? 'info-circle' : 'exclamation-triangle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('.container').insertBefore(alert, document.querySelector('.control-panel'));

            setTimeout(() => {
                alert.remove();
            }, 3000);
        }

        // تأثيرات hover للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(-5px)';
                });
            });
        });
    </script>

</body>
</html>
