"""
تطبيق Streamlit للتحليل الذكي لجدول ITEM_MOVEMENT
Streamlit App for Intelligent Analysis of ITEM_MOVEMENT Table
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import json
import sys
import os

# استيراد الوحدات المحلية
try:
    from database_connector import db_connector
    from intelligent_analyzer import ItemMovementAnalyzer
    from visualization_engine import VisualizationEngine
except ImportError as e:
    st.error(f"خطأ في استيراد الوحدات: {str(e)}")
    st.stop()

# إعداد الصفحة
st.set_page_config(
    page_title="🤖 التحليل الذكي لحركة الأصناف",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# إعداد CSS مخصص
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
        font-weight: bold;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #c3e6cb;
    }
    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #f5c6cb;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """الدالة الرئيسية للتطبيق"""
    
    # العنوان الرئيسي
    st.markdown('<h1 class="main-header">🤖 نظام التحليل الذكي لحركة الأصناف</h1>', unsafe_allow_html=True)
    
    # الشريط الجانبي
    with st.sidebar:
        st.header("⚙️ إعدادات التحليل")
        
        # معلومات الاتصال
        st.subheader("🔗 معلومات قاعدة البيانات")
        st.info(f"""
        **قاعدة البيانات:** ORCL  
        **المستخدم:** IAS20251  
        **الجدول:** ITEM_MOVEMENT
        """)
        
        # خيارات التحليل
        st.subheader("📊 خيارات التحليل")
        analysis_type = st.selectbox(
            "نوع التحليل:",
            ["التحليل الأساسي", "التحليل المتقدم", "التحليل الشامل"]
        )
        
        data_limit = st.number_input(
            "حد البيانات (0 = جميع البيانات):",
            min_value=0,
            max_value=100000,
            value=5000,
            step=1000
        )
        
        # زر بدء التحليل
        start_analysis = st.button("🚀 بدء التحليل", type="primary")
    
    # المحتوى الرئيسي
    if start_analysis:
        run_analysis(analysis_type, data_limit)
    else:
        show_welcome_screen()

def show_welcome_screen():
    """عرض شاشة الترحيب"""
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown("""
        ### 🎯 مرحباً بك في نظام التحليل الذكي
        
        هذا النظام يستخدم أحدث تقنيات الذكاء الاصطناعي لتحليل بيانات جدول **ITEM_MOVEMENT** وتقديم رؤى ذكية ومفيدة.
        
        #### 🔍 ما يمكن للنظام فعله:
        
        - **📊 التحليل الإحصائي الشامل** للبيانات
        - **🤖 كشف الأنماط والاتجاهات** باستخدام التعلم الآلي
        - **⚠️ اكتشاف الشذوذ والقيم غير الطبيعية**
        - **📈 التنبؤ بالاتجاهات المستقبلية**
        - **🎨 تصور البيانات التفاعلي**
        - **💡 توليد التوصيات الذكية**
        
        #### 🚀 للبدء:
        1. اختر نوع التحليل من الشريط الجانبي
        2. حدد حد البيانات المطلوب تحليلها
        3. اضغط على زر "بدء التحليل"
        """)
        
        # إحصائيات سريعة
        st.markdown("---")
        st.subheader("📈 إحصائيات سريعة")
        
        col_a, col_b, col_c, col_d = st.columns(4)
        
        with col_a:
            st.metric("🧠 نماذج الذكاء الاصطناعي", "15+")
        
        with col_b:
            st.metric("📊 أنواع التحليل", "25+")
        
        with col_c:
            st.metric("🎨 أنواع الرسوم البيانية", "20+")
        
        with col_d:
            st.metric("⚡ سرعة المعالجة", "عالية")

def run_analysis(analysis_type: str, data_limit: int):
    """تشغيل التحليل"""
    
    # شريط التقدم
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    try:
        # الخطوة 1: الاتصال بقاعدة البيانات
        status_text.text("🔗 جاري الاتصال بقاعدة البيانات...")
        progress_bar.progress(10)
        
        if not db_connector.connect():
            st.error("❌ فشل في الاتصال بقاعدة البيانات")
            return
        
        # الخطوة 2: تحميل البيانات
        status_text.text("📥 جاري تحميل البيانات...")
        progress_bar.progress(30)
        
        analyzer = ItemMovementAnalyzer(db_connector)
        
        if data_limit > 0:
            success = analyzer.load_data(limit=data_limit)
        else:
            success = analyzer.load_data()
        
        if not success:
            st.error("❌ فشل في تحميل البيانات")
            return
        
        # الخطوة 3: معالجة البيانات
        status_text.text("🔄 جاري معالجة البيانات...")
        progress_bar.progress(50)
        
        analyzer.preprocess_data()
        
        # الخطوة 4: التحليل
        status_text.text("🧠 جاري تشغيل التحليل الذكي...")
        progress_bar.progress(70)
        
        if analysis_type in ["التحليل الأساسي", "التحليل الشامل"]:
            basic_results = analyzer.basic_analysis()
        
        if analysis_type in ["التحليل المتقدم", "التحليل الشامل"]:
            advanced_results = analyzer.advanced_analysis()
        
        # الخطوة 5: إنشاء التصورات
        status_text.text("🎨 جاري إنشاء الرسوم البيانية...")
        progress_bar.progress(90)
        
        viz_engine = VisualizationEngine(analyzer)
        dashboard = viz_engine.create_dashboard()
        
        # الخطوة 6: عرض النتائج
        status_text.text("✅ تم الانتهاء من التحليل!")
        progress_bar.progress(100)
        
        # عرض النتائج
        display_results(analyzer, dashboard, analysis_type)
        
    except Exception as e:
        st.error(f"❌ حدث خطأ أثناء التحليل: {str(e)}")
    
    finally:
        # تنظيف الموارد
        db_connector.disconnect()
        status_text.empty()
        progress_bar.empty()

def display_results(analyzer, dashboard, analysis_type):
    """عرض نتائج التحليل"""
    
    st.success("✅ تم إكمال التحليل بنجاح!")
    
    # معلومات أساسية عن البيانات
    st.header("📊 نظرة عامة على البيانات")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "إجمالي الصفوف",
            f"{len(analyzer.processed_data):,}",
            delta=None
        )
    
    with col2:
        st.metric(
            "إجمالي الأعمدة",
            len(analyzer.processed_data.columns),
            delta=None
        )
    
    with col3:
        memory_mb = analyzer.processed_data.memory_usage(deep=True).sum() / 1024**2
        st.metric(
            "حجم البيانات",
            f"{memory_mb:.2f} MB",
            delta=None
        )
    
    with col4:
        duplicates = analyzer.processed_data.duplicated().sum()
        st.metric(
            "الصفوف المكررة",
            f"{duplicates:,}",
            delta=None
        )
    
    # عرض عينة من البيانات
    st.subheader("📋 عينة من البيانات")
    st.dataframe(analyzer.processed_data.head(10), use_container_width=True)
    
    # التحليل الأساسي
    if analysis_type in ["التحليل الأساسي", "التحليل الشامل"]:
        display_basic_analysis(analyzer)
    
    # التحليل المتقدم
    if analysis_type in ["التحليل المتقدم", "التحليل الشامل"]:
        display_advanced_analysis(analyzer)
    
    # الرسوم البيانية
    display_visualizations(dashboard)

def display_basic_analysis(analyzer):
    """عرض التحليل الأساسي"""
    
    st.header("📈 التحليل الأساسي")
    
    if 'basic' in analyzer.analysis_results:
        basic_results = analyzer.analysis_results['basic']
        
        # جودة البيانات
        st.subheader("🔍 جودة البيانات")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # القيم المفقودة
            missing_data = basic_results['data_quality']['missing_values']
            missing_df = pd.DataFrame(list(missing_data.items()), columns=['العمود', 'القيم المفقودة'])
            missing_df = missing_df[missing_df['القيم المفقودة'] > 0]
            
            if not missing_df.empty:
                st.write("**القيم المفقودة:**")
                st.dataframe(missing_df, use_container_width=True)
            else:
                st.success("✅ لا توجد قيم مفقودة")
        
        with col2:
            # أنواع البيانات
            data_types = basic_results['data_quality']['data_types']
            types_df = pd.DataFrame(list(data_types.items()), columns=['العمود', 'نوع البيانات'])
            st.write("**أنواع البيانات:**")
            st.dataframe(types_df, use_container_width=True)

def display_advanced_analysis(analyzer):
    """عرض التحليل المتقدم"""
    
    st.header("🤖 التحليل المتقدم بالذكاء الاصطناعي")
    
    if 'advanced' in analyzer.analysis_results:
        advanced_results = analyzer.analysis_results['advanced']
        
        # تحليل التجميع
        if 'clustering_analysis' in advanced_results:
            st.subheader("🎯 تحليل التجميع (Clustering)")
            clustering = advanced_results['clustering_analysis']
            
            if 'kmeans' in clustering:
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write("**K-Means Clustering:**")
                    st.write(f"عدد المجموعات: {clustering['kmeans']['n_clusters']}")
                    st.write(f"مؤشر الجودة: {clustering['kmeans']['inertia']:.2f}")
                
                with col2:
                    if 'dbscan' in clustering:
                        st.write("**DBSCAN Clustering:**")
                        st.write(f"عدد المجموعات: {clustering['dbscan']['n_clusters']}")
                        st.write(f"النقاط الشاذة: {clustering['dbscan']['noise_points']}")
        
        # كشف الشذوذ
        if 'anomaly_detection' in advanced_results:
            st.subheader("⚠️ كشف الشذوذ")
            anomaly = advanced_results['anomaly_detection']
            
            if 'total_anomalies' in anomaly:
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("إجمالي الشذوذ", anomaly['total_anomalies'])
                
                with col2:
                    st.metric("نسبة الشذوذ", f"{anomaly['anomaly_percentage']:.2f}%")
                
                with col3:
                    st.metric("متوسط درجة الشذوذ", f"{anomaly['anomaly_scores']['mean']:.3f}")

def display_visualizations(dashboard):
    """عرض الرسوم البيانية"""
    
    st.header("🎨 الرسوم البيانية التفاعلية")
    
    # الرسوم العامة
    if 'overview_charts' in dashboard:
        st.subheader("📊 نظرة عامة")
        
        overview = dashboard['overview_charts']
        
        for chart_name, chart_html in overview.items():
            if chart_html:
                st.components.v1.html(chart_html, height=500)
    
    # رسوم التوزيع
    if 'distribution_charts' in dashboard:
        st.subheader("📈 توزيع البيانات")
        
        distribution = dashboard['distribution_charts']
        
        # عرض الرسوم في أعمدة
        chart_items = list(distribution.items())
        for i in range(0, len(chart_items), 2):
            col1, col2 = st.columns(2)
            
            with col1:
                if i < len(chart_items):
                    chart_name, chart_html = chart_items[i]
                    if chart_html:
                        st.components.v1.html(chart_html, height=400)
            
            with col2:
                if i + 1 < len(chart_items):
                    chart_name, chart_html = chart_items[i + 1]
                    if chart_html:
                        st.components.v1.html(chart_html, height=400)
    
    # خريطة الارتباطات
    if 'correlation_heatmap' in dashboard and dashboard['correlation_heatmap']:
        st.subheader("🔥 خريطة الارتباطات")
        st.components.v1.html(dashboard['correlation_heatmap'], height=600)
    
    # السلاسل الزمنية
    if 'time_series_charts' in dashboard:
        st.subheader("⏰ السلاسل الزمنية")
        
        time_series = dashboard['time_series_charts']
        
        for chart_name, chart_html in time_series.items():
            if chart_html:
                st.components.v1.html(chart_html, height=500)

if __name__ == "__main__":
    main()
