# -*- coding: utf-8 -*-
"""
نماذج استلام البضائع
Goods Receipt Forms
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, DecimalField, DateField, SubmitField, BooleanField, FieldList, FormField
from wtforms.validators import DataRequired, Length, NumberRange, Optional
from wtforms.widgets import TextArea
from app.models import PurchaseOrder, Item, Supplier
from datetime import datetime

class GoodsReceiptItemForm(FlaskForm):
    """نموذج عنصر استلام البضائع"""

    item_id = SelectField(
        'الصنف',
        coerce=int,
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )

    ordered_quantity = DecimalField(
        'الكمية المطلوبة',
        validators=[DataRequired(), NumberRange(min=0)],
        places=2,
        render_kw={'class': 'form-control', 'readonly': True}
    )

    received_quantity = DecimalField(
        'الكمية المستلمة',
        validators=[DataRequired(), NumberRange(min=0)],
        places=2,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0'}
    )

    rejected_quantity = DecimalField(
        'الكمية المرفوضة',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        default=0,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0'}
    )

    unit = StringField(
        'الوحدة',
        validators=[Optional(), Length(max=20)],
        render_kw={'class': 'form-control', 'readonly': True}
    )

    unit_price = DecimalField(
        'سعر الوحدة',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0'}
    )

    quality_status = SelectField(
        'حالة الجودة',
        choices=[
            ('pending', 'في الانتظار'),
            ('passed', 'مقبول'),
            ('failed', 'مرفوض'),
            ('partial', 'مقبول جزئياً')
        ],
        default='pending',
        render_kw={'class': 'form-select'}
    )

    rejection_reason = TextAreaField(
        'سبب الرفض',
        validators=[Optional(), Length(max=500)],
        render_kw={'class': 'form-control', 'rows': 2, 'placeholder': 'سبب رفض البضاعة'}
    )

    batch_number = StringField(
        'رقم الدفعة',
        validators=[Optional(), Length(max=50)],
        render_kw={'class': 'form-control', 'placeholder': 'رقم الدفعة أو اللوط'}
    )

    expiry_date = DateField(
        'تاريخ الانتهاء',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )

    notes = TextAreaField(
        'ملاحظات',
        validators=[Optional(), Length(max=500)],
        render_kw={'class': 'form-control', 'rows': 2, 'placeholder': 'ملاحظات حول العنصر...'}
    )

    def __init__(self, *args, **kwargs):
        super(GoodsReceiptItemForm, self).__init__(*args, **kwargs)

        try:
            # تحديث خيارات الأصناف
            self.item_id.choices = [(0, 'اختر الصنف')] + [
                (item.id, f"{item.code} - {item.name_ar}")
                for item in Item.query.filter_by(is_active=True).order_by(Item.name_ar).all()
            ]
        except:
            self.item_id.choices = [(0, 'اختر الصنف')]

class GoodsReceiptForm(FlaskForm):
    """نموذج إنشاء استلام بضائع"""
    
    # معلومات أساسية
    receipt_number = StringField(
        'رقم الاستلام',
        validators=[DataRequired(), Length(min=1, max=50)],
        render_kw={'class': 'form-control', 'placeholder': 'سيتم إنشاؤه تلقائياً'}
    )

    purchase_order_id = SelectField(
        'أمر الشراء',
        coerce=int,
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )

    supplier_id = SelectField(
        'المورد',
        coerce=int,
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )

    receipt_date = DateField(
        'تاريخ الاستلام',
        validators=[DataRequired()],
        default=datetime.utcnow,
        render_kw={'class': 'form-control'}
    )

    delivery_note_number = StringField(
        'رقم إشعار التسليم',
        validators=[Optional(), Length(max=100)],
        render_kw={'class': 'form-control', 'placeholder': 'رقم إشعار التسليم من المورد'}
    )

    # معلومات الشحن والنقل
    vehicle_number = StringField(
        'رقم المركبة',
        validators=[Optional(), Length(max=50)],
        render_kw={'class': 'form-control', 'placeholder': 'رقم لوحة المركبة'}
    )

    driver_name = StringField(
        'اسم السائق',
        validators=[Optional(), Length(max=100)],
        render_kw={'class': 'form-control', 'placeholder': 'اسم سائق المركبة'}
    )

    driver_phone = StringField(
        'هاتف السائق',
        validators=[Optional(), Length(max=20)],
        render_kw={'class': 'form-control', 'placeholder': 'رقم هاتف السائق'}
    )
    
    # معلومات الاستلام
    received_by = StringField(
        'مستلم بواسطة',
        validators=[DataRequired(), Length(min=1, max=100)],
        render_kw={'class': 'form-control'}
    )

    warehouse_location = StringField(
        'موقع المستودع',
        validators=[Optional(), Length(max=100)],
        render_kw={'class': 'form-control', 'placeholder': 'المستودع أو الموقع'}
    )

    status = SelectField(
        'حالة الاستلام',
        choices=[
            ('pending', 'في الانتظار'),
            ('partial', 'استلام جزئي'),
            ('completed', 'مكتمل'),
            ('rejected', 'مرفوض')
        ],
        default='pending',
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )

    quality_check_status = SelectField(
        'حالة فحص الجودة',
        choices=[
            ('pending', 'في الانتظار'),
            ('passed', 'مقبول'),
            ('failed', 'مرفوض'),
            ('partial', 'مقبول جزئياً')
        ],
        default='pending',
        render_kw={'class': 'form-select'}
    )

    # عناصر الاستلام
    items = FieldList(FormField(GoodsReceiptItemForm), min_entries=0)

    notes = TextAreaField(
        'ملاحظات',
        validators=[Optional(), Length(max=1000)],
        widget=TextArea(),
        render_kw={'class': 'form-control', 'rows': 4, 'placeholder': 'ملاحظات حول الاستلام...'}
    )
    
    submit = SubmitField('إنشاء استلام البضائع', render_kw={'class': 'btn btn-primary'})
    
    def __init__(self, *args, **kwargs):
        super(GoodsReceiptForm, self).__init__(*args, **kwargs)

        try:
            # تحديث خيارات أوامر الشراء المؤكدة
            self.purchase_order_id.choices = [(0, 'اختر أمر الشراء (اختياري)')] + [
                (po.id, f"{po.order_number} - {po.supplier.name_ar if po.supplier else 'غير محدد'}")
                for po in PurchaseOrder.query.filter(
                    PurchaseOrder.status.in_(['confirmed', 'in_progress', 'partially_delivered'])
                ).all()
            ]
        except:
            self.purchase_order_id.choices = [(0, 'اختر أمر الشراء (اختياري)')]

        try:
            # تحديث خيارات الموردين
            self.supplier_id.choices = [(0, 'اختر المورد (اختياري)')] + [
                (supplier.id, supplier.name_ar)
                for supplier in Supplier.query.filter_by(is_active=True)
                .order_by(Supplier.name_ar).all()
            ]
        except:
            self.supplier_id.choices = [(0, 'اختر المورد (اختياري)')]

        # إضافة عنصر افتراضي إذا لم تكن هناك عناصر
        if not self.items.entries:
            self.items.append_entry()



class GoodsReceiptUpdateForm(FlaskForm):
    """نموذج تحديث استلام البضائع"""
    
    status = SelectField(
        'حالة الاستلام',
        choices=[
            ('draft', 'مسودة'),
            ('completed', 'مكتمل'),
            ('cancelled', 'ملغي')
        ],
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    quality_check_status = SelectField(
        'حالة فحص الجودة',
        choices=[
            ('pending', 'في الانتظار'),
            ('passed', 'مقبول'),
            ('failed', 'مرفوض'),
            ('partial', 'مقبول جزئياً')
        ],
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    storage_location = StringField(
        'موقع التخزين',
        validators=[Optional(), Length(max=100)],
        render_kw={'class': 'form-control'}
    )
    
    notes = TextAreaField(
        'ملاحظات',
        validators=[Optional(), Length(max=1000)],
        widget=TextArea(),
        render_kw={'class': 'form-control', 'rows': 4}
    )
    
    submit = SubmitField('تحديث الاستلام', render_kw={'class': 'btn btn-primary'})

class GoodsReceiptSearchForm(FlaskForm):
    """نموذج البحث في استلام البضائع"""
    
    search_term = StringField(
        'البحث',
        validators=[Optional(), Length(max=100)],
        render_kw={'class': 'form-control', 'placeholder': 'رقم الاستلام، أمر الشراء، أو المورد...'}
    )
    
    status = SelectField(
        'الحالة',
        choices=[
            ('', 'جميع الحالات'),
            ('draft', 'مسودة'),
            ('completed', 'مكتمل'),
            ('cancelled', 'ملغي')
        ],
        default='',
        render_kw={'class': 'form-select'}
    )
    
    quality_status = SelectField(
        'حالة الجودة',
        choices=[
            ('', 'جميع الحالات'),
            ('pending', 'في الانتظار'),
            ('passed', 'مقبول'),
            ('failed', 'مرفوض'),
            ('partial', 'مقبول جزئياً')
        ],
        default='',
        render_kw={'class': 'form-select'}
    )
    
    date_from = DateField(
        'من تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    date_to = DateField(
        'إلى تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    submit = SubmitField('بحث', render_kw={'class': 'btn btn-outline-primary'})
