# -*- coding: utf-8 -*-
"""
مسارات إدارة المخزون - الأصناف
Inventory Management Routes - Items
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, abort
from flask_login import login_required, current_user
from . import bp
from oracle_manager import get_oracle_manager
from datetime import datetime
import logging

# إعداد logger
logger = logging.getLogger(__name__)

def _safe_float(value):
    """تحويل آمن للأرقام"""
    try:
        if value is None:
            return 0.0
        if isinstance(value, (int, float)):
            return float(value)
        if isinstance(value, str):
            return float(value) if value.strip() else 0.0
        return 0.0
    except (ValueError, TypeError):
        return 0.0

@bp.route('/')
@login_required
def index():
    """صفحة قائمة الأصناف"""
    try:
        oracle_mgr = get_oracle_manager()

        # الحصول على معاملات البحث والفلترة
        page = request.args.get('page', 1, type=int)
        per_page = 20
        search_term = request.args.get('search', '').strip()

        # أولاً، دعنا نتحقق من هيكل الجدول الفعلي
        try:
            # استعلام لمعرفة أعمدة الجدول
            columns_query = """
            SELECT column_name
            FROM user_tab_columns
            WHERE table_name = 'ITEMS'
            ORDER BY column_id
            """
            columns_result = oracle_mgr.execute_query(columns_query)
            available_columns = [col[0] for col in columns_result] if columns_result else []
            logger.info(f"أعمدة جدول items المتاحة: {available_columns}")

            # بناء الاستعلام بناءً على الأعمدة المتاحة
            base_columns = ['id', 'item_code', 'name_ar', 'unit_of_measure', 'current_stock',
                          'minimum_stock', 'maximum_stock', 'unit_price', 'is_active',
                          'created_at', 'updated_at']

            # إضافة الأعمدة الاختيارية إذا كانت موجودة
            optional_columns = ['name_en', 'description', 'category_id']
            for col in optional_columns:
                if col.upper() in [c.upper() for c in available_columns]:
                    base_columns.append(col)

            columns_str = ', '.join(base_columns)
            base_query = f"""
            SELECT {columns_str}
            FROM items
            WHERE 1=1
            """

        except Exception as structure_error:
            logger.error(f"خطأ في فحص هيكل الجدول: {structure_error}")
            # استخدام استعلام أساسي آمن مع أسماء الأعمدة الصحيحة
            base_query = """
            SELECT id, item_code, name_ar, unit_of_measure, current_stock,
                   minimum_stock, maximum_stock, unit_price,
                   is_active, created_at, updated_at
            FROM items
            WHERE 1=1
            """

        # إضافة شروط البحث
        params = []
        if search_term:
            base_query += " AND (UPPER(name_ar) LIKE UPPER(:1) OR UPPER(item_code) LIKE UPPER(:2))"
            search_pattern = f"%{search_term}%"
            params.extend([search_pattern, search_pattern])

        # إضافة ترتيب
        base_query += " ORDER BY name_ar"

        # تنفيذ الاستعلام
        items_data = oracle_mgr.execute_query(base_query, params)

        # تحويل البيانات إلى قائمة - معالجة ديناميكية
        items_list = []
        if items_data:
            for item_data in items_data:
                # معالجة البيانات بناءً على عدد الأعمدة المتاحة
                item_dict = {
                    'id': item_data[0] if len(item_data) > 0 else 0,
                    'item_code': item_data[1] if len(item_data) > 1 else '',
                    'item_name_ar': item_data[2] if len(item_data) > 2 else '',
                    'item_name_en': item_data[3] if len(item_data) > 3 else '',
                    'description': item_data[4] if len(item_data) > 4 else '',
                    'unit_of_measure': item_data[3] if len(item_data) <= 6 else item_data[5],  # تعديل الفهرس
                    'current_stock': _safe_float(item_data[4] if len(item_data) <= 6 else item_data[6]),
                    'min_stock_level': _safe_float(item_data[5] if len(item_data) <= 6 else item_data[7]),
                    'max_stock_level': _safe_float(item_data[6] if len(item_data) <= 6 else item_data[8]),
                    'unit_price': _safe_float(item_data[7] if len(item_data) <= 6 else item_data[9]),
                    'category_id': item_data[10] if len(item_data) > 10 else None,
                    'location': item_data[11] if len(item_data) > 11 else '',
                    'barcode': item_data[12] if len(item_data) > 12 else '',
                    'is_active': item_data[-3] if len(item_data) >= 3 else 1,  # الثالث من الآخر
                    'created_at': str(item_data[-2]) if len(item_data) >= 2 and item_data[-2] else '',  # الثاني من الآخر
                    'updated_at': str(item_data[-1]) if len(item_data) >= 1 and item_data[-1] else ''   # الأخير
                }
                items_list.append(item_dict)

        # حساب pagination بسيط
        total_items = len(items_list)
        start_index = (page - 1) * per_page
        end_index = start_index + per_page
        items_page = items_list[start_index:end_index]

        # إنشاء pagination object بسيط
        class SimplePagination:
            def __init__(self, items, page, per_page, total, has_prev, has_next, prev_num, next_num):
                self.items = items
                self.page = page
                self.per_page = per_page
                self.total = total
                self.has_prev = has_prev
                self.has_next = has_next
                self.prev_num = prev_num
                self.next_num = next_num
                self.pages = (total + per_page - 1) // per_page

            def iter_pages(self, left_edge=2, left_current=2, right_current=3, right_edge=2):
                """إنشاء قائمة أرقام الصفحات للعرض"""
                last = self.pages
                for num in range(1, last + 1):
                    if num <= left_edge or \
                       (self.page - left_current - 1 < num < self.page + right_current) or \
                       num > last - right_edge:
                        yield num

        # إنشاء pagination
        has_prev = page > 1
        has_next = end_index < total_items
        prev_num = page - 1 if has_prev else None
        next_num = page + 1 if has_next else None

        items = SimplePagination(
            items=items_page,
            page=page,
            per_page=per_page,
            total=total_items,
            has_prev=has_prev,
            has_next=has_next,
            prev_num=prev_num,
            next_num=next_num
        )

        # إحصائيات بسيطة
        stats = {
            'total_items': total_items,
            'active_items': len([item for item in items_list if item['is_active']]),
            'low_stock_items': len([item for item in items_list if item['current_stock'] <= item['min_stock_level']]),
            'out_of_stock_items': len([item for item in items_list if item['current_stock'] <= 0])
        }

        return render_template('inventory/index.html',
                             items=items,
                             stats=stats,
                             search_term=search_term,
                             title='بيانات الأصناف')

    except Exception as e:
        logger.error(f"خطأ في عرض قائمة الأصناف: {e}")
        flash('خطأ في تحميل بيانات الأصناف', 'error')
        return render_template('inventory/index.html',
                             items=None,
                             stats={},
                             title='بيانات الأصناف')

@bp.route('/items/<int:item_id>')
@login_required
def view_item(item_id):
    """عرض تفاصيل الصنف"""
    try:
        oracle_mgr = get_oracle_manager()

        # الحصول على بيانات الصنف
        query = """
        SELECT i.id, i.item_code, i.item_name_ar, i.item_name_en,
               i.description, i.unit_of_measure, i.current_stock,
               i.min_stock_level, i.max_stock_level, i.unit_price,
               i.category_id, i.location, i.barcode, i.is_active,
               i.created_at, i.updated_at
        FROM items i
        WHERE i.id = :1
        """

        item_data = oracle_mgr.execute_query(query, [item_id])

        if not item_data:
            flash('الصنف غير موجود', 'error')
            return redirect(url_for('inventory.index'))

        # تحويل البيانات
        item_row = item_data[0]
        item = {
            'id': item_row[0],
            'item_code': item_row[1],
            'item_name_ar': item_row[2],
            'item_name_en': item_row[3],
            'description': item_row[4],
            'unit_of_measure': item_row[5],
            'current_stock': item_row[6] or 0,
            'min_stock_level': item_row[7] or 0,
            'max_stock_level': item_row[8] or 0,
            'unit_price': item_row[9] or 0,
            'category_id': item_row[10],
            'location': item_row[11],
            'barcode': item_row[12],
            'is_active': item_row[13],
            'created_at': item_row[14],
            'updated_at': item_row[15]
        }

        return render_template('inventory/view_item.html',
                             item=item,
                             title=f'الصنف: {item["item_name_ar"]}')

    except Exception as e:
        logger.error(f"خطأ في عرض تفاصيل الصنف: {e}")
        flash('خطأ في تحميل بيانات الصنف', 'error')
        return redirect(url_for('inventory.index'))

@bp.route('/import')
@login_required
def import_items():
    """صفحة استيراد الأصناف"""
    try:
        return render_template('inventory/import.html')
    except Exception as e:
        logger.error(f"خطأ في صفحة استيراد الأصناف: {e}")
        flash('حدث خطأ في تحميل الصفحة', 'error')
        return redirect(url_for('inventory.index'))

@bp.route('/import', methods=['POST'])
@login_required
def import_items_post():
    """معالجة استيراد الأصناف من قاعدة البيانات الخارجية"""
    try:
        oracle_manager = get_oracle_manager()

        # اختبار الاتصال
        test_query = "SELECT COUNT(*) FROM dual"
        test_result = oracle_manager.execute_query(test_query)
        logger.info(f"اختبار الاتصال: {test_result}")

        # أولاً، دعنا نتحقق من database links والجداول المتاحة
        try:
            # اختبار database links
            links_query = "SELECT db_link FROM user_db_links"
            links_result = oracle_manager.execute_query(links_query)
            logger.info(f"Database links المتاحة: {links_result}")

            # جرب أسماء مختلفة للـ database link
            possible_links = [
                "IAS20251.YEMENSOFT.COM",
                "IAS20251",
                "YEMENSOFT.COM",
                "IAS20251.YEMENSOFT"
            ]

            working_link = None
            available_tables = []

            for link in possible_links:
                try:
                    test_query = f"SELECT COUNT(*) FROM dual@{link}"
                    test_result = oracle_manager.execute_query(test_query)
                    if test_result:
                        working_link = link
                        logger.info(f"تم العثور على database link يعمل: {link}")

                        # البحث عن الجداول المتاحة
                        try:
                            tables_query = f"SELECT table_name FROM all_tables@{link} WHERE owner = 'IAS20251'"
                            tables_result = oracle_manager.execute_query(tables_query)
                            available_tables = [table[0] for table in tables_result] if tables_result else []
                            logger.info(f"الجداول المتاحة في {link}: {available_tables}")

                            # اختبار الوصول للجداول المطلوبة مع schema
                            test_mst_query = f"SELECT COUNT(*) FROM IAS20251.IAS_ITM_MST@{link} WHERE ROWNUM <= 1"
                            test_mst_result = oracle_manager.execute_query(test_mst_query)
                            logger.info(f"اختبار جدول IAS_ITM_MST مع schema: {test_mst_result}")

                            test_dtl_query = f"SELECT COUNT(*) FROM IAS20251.IAS_ITM_DTL@{link} WHERE ROWNUM <= 1"
                            test_dtl_result = oracle_manager.execute_query(test_dtl_query)
                            logger.info(f"اختبار جدول IAS_ITM_DTL مع schema: {test_dtl_result}")

                        except Exception as tables_error:
                            logger.warning(f"لا يمكن الحصول على قائمة الجداول من {link}: {tables_error}")
                            # جرب البحث بطريقة أخرى
                            try:
                                alt_tables_query = f"SELECT table_name FROM user_tables@{link}"
                                alt_tables_result = oracle_manager.execute_query(alt_tables_query)
                                available_tables = [table[0] for table in alt_tables_result] if alt_tables_result else []
                                logger.info(f"الجداول المتاحة (user_tables) في {link}: {available_tables}")
                            except:
                                logger.warning(f"لا يمكن الحصول على قائمة الجداول من {link}")
                        break
                except Exception as link_error:
                    logger.debug(f"Database link {link} لا يعمل: {link_error}")
                    continue

            if not working_link:
                flash('لا يمكن الوصول إلى قاعدة البيانات الخارجية. تحقق من إعدادات database link', 'error')
                return redirect(url_for('inventory.import_items'))

            # التحقق من وجود الجداول المطلوبة
            required_tables = ['IAS_ITM_MST', 'IAS_ITM_DTL']
            missing_tables = []

            for table in required_tables:
                if table not in available_tables:
                    missing_tables.append(table)

            if missing_tables:
                flash(f'الجداول التالية غير موجودة: {", ".join(missing_tables)}. الجداول المتاحة: {", ".join(available_tables[:10])}', 'error')
                return redirect(url_for('inventory.import_items'))

            # فحص الأعمدة المتاحة في الجدول المصدر
            try:
                columns_check_query = f"""
                SELECT column_name
                FROM all_tab_columns@{working_link}
                WHERE owner = 'IAS20251' AND table_name = 'IAS_ITM_MST'
                AND column_name IN ('I_SIZE', 'CATEGORY_ID', 'ITEM_SIZE', 'CAT_ID', 'ITM_SIZE')
                ORDER BY column_name
                """
                available_columns = oracle_manager.execute_query(columns_check_query)
                logger.info(f"الأعمدة المتاحة للحجم والفئة: {[col[0] for col in available_columns] if available_columns else 'لا توجد'}")
            except Exception as col_error:
                logger.warning(f"لا يمكن فحص الأعمدة: {col_error}")

        except Exception as test_error:
            logger.error(f"خطأ في اختبار database links: {test_error}")
            flash(f'خطأ في الاتصال بقاعدة البيانات الخارجية: {str(test_error)}', 'error')
            return redirect(url_for('inventory.import_items'))

        # استعلام الأصناف من الجدولين مع الشروط المطلوبة
        # استخدام schema صريح IAS20251 مع P_SIZE كـ ITEM_SIZE و G_CODE كـ CATEGORY_ID
        items_query = f"""
        SELECT
            mst.I_CODE,
            mst.I_NAME,
            mst.I_E_NAME,
            mst.G_CODE,
            dtl.ITM_UNT,
            dtl.P_SIZE
        FROM IAS20251.IAS_ITM_MST@{working_link} mst
        LEFT JOIN IAS20251.IAS_ITM_DTL@{working_link} dtl
            ON mst.I_CODE = dtl.I_CODE AND dtl.SALE_UNIT = 1
        WHERE mst.I_CODE NOT LIKE '%*'
        ORDER BY mst.I_CODE
        """

        items_data = oracle_manager.execute_query(items_query)

        if not items_data:
            flash('لم يتم العثور على أصناف للاستيراد', 'warning')
            return redirect(url_for('inventory.import_items'))

        # إحصائيات الاستيراد
        imported_count = 0
        updated_count = 0
        error_count = 0

        for item_data in items_data:
            try:
                # استخراج البيانات (6 أعمدة: I_CODE, I_NAME, I_E_NAME, G_CODE, ITM_UNT, P_SIZE)
                i_code = item_data[0]
                i_name = item_data[1] or ''
                i_e_name = item_data[2] or ''
                g_code = item_data[3] or ''  # هذا هو CATEGORY_ID
                itm_unt = item_data[4] or ''
                p_size = item_data[5] or ''  # هذا هو ITEM_SIZE

                # تنظيف البيانات
                item_code = str(i_code).strip() if i_code else ''
                name_ar = str(i_name).strip() if i_name else ''
                name_en = str(i_e_name).strip() if i_e_name else ''
                unit_of_measure = str(itm_unt).strip() if itm_unt else ''
                item_size = str(p_size).strip() if p_size else ''  # P_SIZE كـ ITEM_SIZE
                # G_CODE كـ CATEGORY_ID
                category_id_value = str(g_code).strip() if g_code else None
                # وصف فارغ لأن G_CODE يُستخدم كـ CATEGORY_ID
                description = ''

                # تخطي الأصناف بدون كود
                if not item_code:
                    continue

                # التحقق من وجود الصنف
                check_query = "SELECT id FROM items WHERE item_code = :1"
                existing_item = oracle_manager.execute_query(check_query, [item_code])

                if existing_item:
                    # تحديث الصنف الموجود مع الحقول الجديدة - إزالة description لتجنب مشكلة LOB
                    update_query = """
                    UPDATE items SET
                        name_ar = :1,
                        name_en = :2,
                        unit_of_measure = :3,
                        item_size = :4,
                        category_id = :5,
                        updated_at = SYSDATE
                    WHERE item_code = :6
                    """
                    oracle_manager.execute_update(update_query, [
                        name_ar, name_en, unit_of_measure, item_size, category_id_value, item_code
                    ])
                    updated_count += 1
                    logger.info(f"تم تحديث الصنف: {item_code} - الحجم: {item_size} - الفئة: {category_id_value}")
                else:
                    # إضافة صنف جديد - استخدام أسماء الأعمدة الصحيحة مع sequence للـ ID
                    # إزالة description لتجنب مشكلة LOB وإضافة الحقول الجديدة
                    insert_query = """
                    INSERT INTO items (id, item_code, name_ar, name_en,
                                     unit_of_measure, current_stock,
                                     minimum_stock, maximum_stock, unit_price,
                                     is_active, created_at, updated_at,
                                     item_size, category_id)
                    VALUES (items_seq.NEXTVAL, :1, :2, :3, :4, 0, 0, 0, 0, 1, SYSDATE, SYSDATE, :5, :6)
                    """
                    oracle_manager.execute_update(insert_query, [
                        item_code, name_ar, name_en, unit_of_measure, item_size, category_id_value
                    ])
                    imported_count += 1
                    logger.info(f"تم إضافة الصنف: {item_code} - الحجم: {item_size} - الفئة: {category_id_value}")

            except Exception as item_error:
                logger.error(f"خطأ في استيراد الصنف {i_code}: {item_error}")
                error_count += 1
                continue

        # رسالة النتيجة
        total_processed = len(items_data) if items_data else 0
        if imported_count > 0 or updated_count > 0:
            message = f"تم معالجة {total_processed} صنف: إضافة {imported_count} جديد وتحديث {updated_count} موجود"
            if error_count > 0:
                message += f" مع {error_count} خطأ"
            flash(message, 'success')
        else:
            flash(f'تم معالجة {total_processed} صنف ولكن لم يتم استيراد أي أصناف جديدة', 'info')

    except Exception as e:
        logger.error(f"خطأ في استيراد الأصناف: {e}")
        flash(f'حدث خطأ في استيراد الأصناف: {str(e)}', 'error')

    return redirect(url_for('inventory.import_items'))

@bp.route('/api/items')
@login_required
def api_items():
    """API للحصول على قائمة الأصناف"""
    try:
        oracle_mgr = get_oracle_manager()

        # الحصول على جميع الأصناف
        query = """
        SELECT i.id, i.item_code, i.item_name_ar, i.item_name_en,
               i.description, i.unit_of_measure, i.current_stock,
               i.min_stock_level, i.max_stock_level, i.unit_price,
               i.category_id, i.location, i.barcode, i.is_active,
               i.created_at, i.updated_at
        FROM items i
        ORDER BY i.item_name_ar
        """

        items_data = oracle_mgr.execute_query(query)

        # تحويل البيانات إلى JSON
        items_list = []
        if items_data:
            for item_data in items_data:
                item_dict = {
                    'id': item_data[0],
                    'item_code': item_data[1],
                    'item_name_ar': item_data[2],
                    'item_name_en': item_data[3],
                    'description': item_data[4],
                    'unit_of_measure': item_data[5],
                    'current_stock': float(item_data[6]) if item_data[6] else 0,
                    'min_stock_level': float(item_data[7]) if item_data[7] else 0,
                    'max_stock_level': float(item_data[8]) if item_data[8] else 0,
                    'unit_price': float(item_data[9]) if item_data[9] else 0,
                    'category_id': item_data[10],
                    'location': item_data[11],
                    'barcode': item_data[12],
                    'is_active': bool(item_data[13]),
                    'created_at': str(item_data[14]) if item_data[14] else '',
                    'updated_at': str(item_data[15]) if item_data[15] else ''
                }
                items_list.append(item_dict)

        return jsonify({
            'success': True,
            'items': items_list,
            'total': len(items_list)
        })

    except Exception as e:
        logger.error(f"خطأ في API الأصناف: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب بيانات الأصناف: {str(e)}'
        })






