-- إضافة عمود الفرع لجدول TRANSFERS

-- 1. إضافة عمود branch_id
BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE TRANSFERS ADD branch_id NUMBER';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN RAISE; END IF; -- تجاهل خطأ "العمود موجود"
END;
/

-- 2. تحديث الحوالات الموجودة بربطها بالفروع من TRANSFER_REQUESTS
UPDATE TRANSFERS t 
SET branch_id = (
    SELECT tr.branch_id 
    FROM TRANSFER_REQUESTS tr 
    WHERE tr.id = t.request_id
)
WHERE t.branch_id IS NULL;

-- 3. إضافة foreign key constraint
BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE TRANSFERS ADD CONSTRAINT fk_transfers_branch FOREIGN KEY (branch_id) REFERENCES BRANCHES(BRN_NO)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -2275 THEN RAISE; END IF; -- تجاهل خطأ "الـ constraint موجود"
END;
/

-- 4. التحقق من النتيجة
SELECT 'TRANSFERS with branch_id' as check_type, COUNT(*) as count
FROM TRANSFERS 
WHERE branch_id IS NOT NULL;

COMMIT;
