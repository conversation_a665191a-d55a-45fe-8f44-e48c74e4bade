<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة وثائق العقد - {{ contract.contract_number }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        
        .container-fluid {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content-section {
            padding: 30px;
        }
        
        .contract-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #3498db;
        }
        
        .stats-row {
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-top: 4px solid #3498db;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        
        .upload-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .documents-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .document-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #28a745;
            transition: all 0.3s ease;
        }
        
        .document-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .upload-area {
            border: 2px dashed #3498db;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            background: #e3f2fd;
            border-color: #2980b9;
        }
        
        .upload-area.dragover {
            background: #e3f2fd;
            border-color: #2980b9;
            transform: scale(1.02);
        }
        
        .btn-upload {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-upload:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
            color: white;
        }
        
        .btn-download {
            background: linear-gradient(135deg, #17a2b8, #138496);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85em;
            transition: all 0.3s ease;
        }
        
        .btn-download:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4);
            color: white;
        }
        
        .btn-delete {
            background: linear-gradient(135deg, #dc3545, #c82333);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85em;
            transition: all 0.3s ease;
        }
        
        .btn-delete:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
            color: white;
        }
        
        .no-documents {
            text-align: center;
            padding: 60px;
            color: #7f8c8d;
        }
        
        .progress-upload {
            display: none;
            margin-top: 15px;
        }

        /* تحسين أزرار الإجراءات */
        .btn-group-sm .btn {
            margin: 0;
            transition: all 0.2s ease;
            border-radius: 0.25rem !important;
        }

        .btn-group-sm .btn:first-child {
            border-top-left-radius: 0.375rem !important;
            border-bottom-left-radius: 0.375rem !important;
        }

        .btn-group-sm .btn:last-child {
            border-top-right-radius: 0.375rem !important;
            border-bottom-right-radius: 0.375rem !important;
        }

        .btn-group-sm .btn:hover {
            transform: translateY(-1px);
            z-index: 2;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        /* أيقونات الملفات */
        .file-item i.fa-file-pdf { color: #dc3545; }
        .file-item i.fa-file-word { color: #2b579a; }
        .file-item i.fa-file-excel { color: #217346; }
        .file-item i.fa-file-image { color: #17a2b8; }
        .file-item i.fa-file-archive { color: #ffc107; }
        .file-item i.fa-file-alt { color: #6c757d; }
        
        /* تنسيقات الملفات المختارة */
        .selected-files-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .file-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef !important;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .file-item:hover {
            background: #e9ecef;
            border-color: #dee2e6 !important;
        }

        .file-item .btn-outline-danger {
            border: none;
            background: transparent;
            color: #dc3545;
            padding: 2px 6px;
            font-size: 0.8em;
        }

        .file-item .btn-outline-danger:hover {
            background: #dc3545;
            color: white;
        }

        /* تحسين شريط التقدم */
        .progress-upload .progress {
            height: 8px;
            border-radius: 4px;
            background-color: #e9ecef;
        }

        .progress-upload .progress-bar {
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 4px;
            transition: width 0.3s ease;
            font-size: 0.7em;
            line-height: 8px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="header-section">
            <h1 class="mb-3">
                <i class="fas fa-folder-open me-3"></i>
                إدارة وثائق العقد
            </h1>
            <h4 class="mb-0">العقد رقم: {{ contract.contract_number }}</h4>
            <p class="mb-0 opacity-75">{{ contract.supplier_name }}</p>
        </div>
        
        <div class="content-section">
            <!-- معلومات العقد -->
            <div class="contract-info">
                <div class="row">
                    <div class="col-md-3">
                        <strong>رقم العقد:</strong><br>
                        <span class="text-primary">{{ contract.contract_number }}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>المورد:</strong><br>
                        {{ contract.supplier_name }}
                    </div>
                    <div class="col-md-3">
                        <strong>تاريخ العقد:</strong><br>
                        {{ contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else 'غير محدد' }}
                    </div>
                    <div class="col-md-3">
                        <strong>قيمة العقد:</strong><br>
                        <span class="text-success fw-bold">{{ "{:,.2f}".format(contract.contract_amount) }} ريال</span>
                    </div>
                </div>
            </div>
            
            <!-- إحصائيات الوثائق -->
            <div class="row stats-row">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number">{{ stats.total_documents }}</div>
                        <div class="stat-label">إجمالي الوثائق</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number">{{ stats.total_size_mb }}</div>
                        <div class="stat-label">الحجم (MB)</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number">{{ stats.uploaded_documents }}</div>
                        <div class="stat-label">وثائق مرفوعة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number">{{ stats.generated_documents }}</div>
                        <div class="stat-label">وثائق مُنشأة</div>
                    </div>
                </div>
            </div>

            <!-- قسم رفع الوثائق -->
            <div class="upload-section">
                <h5 class="mb-4">
                    <i class="fas fa-cloud-upload-alt me-2"></i>
                    رفع وثيقة جديدة
                </h5>

                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">نوع الوثيقة:</label>
                            <select class="form-select" name="document_type" required>
                                <option value="">اختر نوع الوثيقة</option>
                                <option value="contract">العقد الأصلي</option>
                                <option value="amendment">تعديل العقد</option>
                                <option value="invoice">فاتورة</option>
                                <option value="receipt">إيصال</option>
                                <option value="certificate">شهادة</option>
                                <option value="license">ترخيص</option>
                                <option value="insurance">تأمين</option>
                                <option value="guarantee">ضمان</option>
                                <option value="correspondence">مراسلات</option>
                                <option value="attachment">مرفق عام</option>
                                <option value="link">رابط</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">اسم الوثيقة:</label>
                            <input type="text" class="form-control" name="document_name"
                                   placeholder="اسم الوثيقة (اختياري)">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">ملاحظات:</label>
                            <input type="text" class="form-control" name="notes"
                                   placeholder="ملاحظات (اختياري)">
                        </div>
                    </div>

                    <div class="upload-area mt-3" id="uploadArea">
                        <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                        <h5>اسحب الملفات هنا أو انقر للاختيار</h5>
                        <p class="text-muted">الأنواع المدعومة: PDF, DOC, XLS, JPG, PNG, ZIP, RAR (حد أقصى 10 MB لكل ملف)</p>
                        <small class="text-info">💡 يمكنك اختيار عدة ملفات مرة واحدة للوثيقة نفسها</small>
                        <input type="file" id="fileInput" name="document_files"
                               accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.tiff,.txt,.csv,.zip,.rar"
                               style="display: none;" multiple required>
                        <button type="button" class="btn btn-upload" onclick="document.getElementById('fileInput').click()">
                            <i class="fas fa-folder-open me-2"></i>
                            اختيار ملفات
                        </button>
                        <div id="selectedFiles" class="mt-3" style="display: none;">
                            <h6 class="text-success mb-2">
                                <i class="fas fa-check-circle me-1"></i>
                                الملفات المختارة:
                            </h6>
                            <div id="filesList" class="selected-files-list"></div>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <span id="filesCount">0</span> ملف مختار |
                                    الحجم الإجمالي: <span id="totalSize">0 KB</span>
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="progress-upload">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted">جاري الرفع...</small>
                    </div>

                    <div class="text-center mt-3">
                        <button type="submit" class="btn btn-upload btn-lg" id="uploadButton">
                            <i class="fas fa-upload me-2"></i>
                            رفع الوثائق
                        </button>
                    </div>
                </form>
            </div>

            <!-- قسم الوثائق -->
            <div class="documents-section">
                <h5 class="mb-4">
                    <i class="fas fa-folder-open me-2"></i>
                    الوثائق المرفوعة
                </h5>

                <div id="documentsContainer">
                    {% if documents %}
                        {% for doc in documents %}
                        <div class="document-card document-row" data-doc-id="{{ doc.id }}">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file-alt fa-2x text-primary me-3"></i>
                                        <div>
                                            <div class="d-flex align-items-center gap-2 mb-1">
                                                <h6 class="mb-0 document-name">{{ doc.document_name }}</h6>
                                                <span class="badge bg-info">{{ doc.document_type }}</span>
                                            </div>
                                            <small class="text-muted">{{ doc.file_name }}</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <small class="file-size">
                                        <i class="fas fa-weight-hanging me-1"></i>
                                        {{ doc.file_size_mb }} MB
                                    </small><br>
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        {{ doc.uploaded_by }}
                                    </small><br>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ doc.uploaded_at.strftime('%Y-%m-%d %H:%M') if doc.uploaded_at else '' }}
                                    </small>
                                </div>
                                <div class="col-md-3 text-end">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-outline-secondary"
                                                onclick="viewDocumentDetails({{ doc.id }})"
                                                title="استعراض تفاصيل الوثيقة">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <a href="{{ url_for('contracts.download_contract_document', document_id=doc.id) }}"
                                           class="btn btn-outline-info" title="تحميل الوثيقة">
                                            <i class="fas fa-download"></i>
                                        </a>

                                        <!-- أزرار إنشاء الروابط -->
                                        <button class="btn btn-success btn-sm"
                                                onclick="createShareLink({{ doc.id }}, 'nextcloud')"
                                                title="إنشاء رابط Nextcloud">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.232 7.005c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zm7.553 0c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zM12.017 18.55c-2.69 0-4.923-1.273-6.096-3.234l1.431-.716c.79 1.431 2.334 2.39 4.665 2.39s3.875-.959 4.665-2.39l1.431.716c-1.173 1.961-3.406 3.234-6.096 3.234z"/>
                                            </svg>
                                            {% if doc.nextcloud_share_link %}
                                                <span class="badge bg-success rounded-pill ms-1">●</span>
                                            {% endif %}
                                        </button>

                                        <button class="btn btn-primary btn-sm"
                                                onclick="createShareLink({{ doc.id }}, 'onedrive')"
                                                title="إنشاء رابط OneDrive">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M8.9 2.9c-1.6 0-2.9 1.3-2.9 2.9 0 .2 0 .4.1.6C3.6 7.2 1.5 9.7 1.5 12.8c0 3.5 2.8 6.3 6.3 6.3h9.4c2.9 0 5.3-2.4 5.3-5.3 0-2.5-1.7-4.6-4-5.2-.1-3.1-2.7-5.7-5.8-5.7-1.9 0-3.6.9-4.6 2.3-.4-.2-.8-.3-1.2-.3zm0 1.5c.3 0 .6.1.8.2l.4.2.2-.4c.7-1.3 2.1-2.1 3.6-2.1 2.3 0 4.2 1.9 4.2 4.2v.8h.8c2.1 0 3.8 1.7 3.8 3.8s-1.7 3.8-3.8 3.8H7.8c-2.7 0-4.8-2.2-4.8-4.8 0-2.4 1.7-4.4 4-4.7l.5-.1-.1-.5c0-.2-.1-.4-.1-.6 0-.8.6-1.4 1.4-1.4z"/>
                                            </svg>
                                            {% if doc.onedrive_share_link %}
                                                <span class="badge bg-primary rounded-pill ms-1">●</span>
                                            {% endif %}
                                        </button>

                                        <!-- قائمة النسخ -->
                                        <div class="btn-group">
                                            <button class="btn btn-warning btn-sm dropdown-toggle"
                                                    data-bs-toggle="dropdown"
                                                    title="نسخ الروابط"
                                                    {% if not (doc.nextcloud_share_link or doc.onedrive_share_link) %}disabled{% endif %}>
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                {% if doc.nextcloud_share_link %}
                                                <li><a class="dropdown-item" onclick="copyShareLink('{{ doc.nextcloud_share_link }}')">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="#198754" class="me-2">
                                                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.620 0 11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.232 7.005c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zm7.553 0c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zM12.017 18.55c-2.69 0-4.923-1.273-6.096-3.234l1.431-.716c.79 1.431 2.334 2.39 4.665 2.39s3.875-.959 4.665-2.39l1.431.716c-1.173 1.961-3.406 3.234-6.096 3.234z"/>
                                                    </svg>
                                                    نسخ رابط Nextcloud
                                                </a></li>
                                                {% endif %}
                                                {% if doc.onedrive_share_link %}
                                                <li><a class="dropdown-item" onclick="copyShareLink('{{ doc.onedrive_share_link }}')">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="#0d6efd" class="me-2">
                                                        <path d="M8.9 2.9c-1.6 0-2.9 1.3-2.9 2.9 0 .2 0 .4.1.6C3.6 7.2 1.5 9.7 1.5 12.8c0 3.5 2.8 6.3 6.3 6.3h9.4c2.9 0 5.3-2.4 5.3-5.3 0-2.5-1.7-4.6-4-5.2-.1-3.1-2.7-5.7-5.8-5.7-1.9 0-3.6.9-4.6 2.3-.4-.2-.8-.3-1.2-.3zm0 1.5c.3 0 .6.1.8.2l.4.2.2-.4c.7-1.3 2.1-2.1 3.6-2.1 2.3 0 4.2 1.9 4.2 4.2v.8h.8c2.1 0 3.8 1.7 3.8 3.8s-1.7 3.8-3.8 3.8H7.8c-2.7 0-4.8-2.2-4.8-4.8 0-2.4 1.7-4.4 4-4.7l.5-.1-.1-.5c0-.2-.1-.4-.1-.6 0-.8.6-1.4 1.4-1.4z"/>
                                                    </svg>
                                                    نسخ رابط OneDrive
                                                </a></li>
                                                {% endif %}
                                                {% if not (doc.nextcloud_share_link or doc.onedrive_share_link) %}
                                                <li><span class="dropdown-item-text text-muted">لا توجد روابط متاحة</span></li>
                                                {% endif %}
                                            </ul>
                                        </div>

                                        <!-- قائمة الفتح -->
                                        <div class="btn-group">
                                            <button class="btn btn-secondary btn-sm dropdown-toggle"
                                                    data-bs-toggle="dropdown"
                                                    title="فتح الروابط"
                                                    {% if not (doc.nextcloud_share_link or doc.onedrive_share_link) %}disabled{% endif %}>
                                                <i class="fas fa-external-link-alt"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                {% if doc.nextcloud_share_link %}
                                                <li><a class="dropdown-item" onclick="openShareLink('{{ doc.nextcloud_share_link }}')">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="#198754" class="me-2">
                                                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.620 0 11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.232 7.005c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zm7.553 0c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zM12.017 18.55c-2.69 0-4.923-1.273-6.096-3.234l1.431-.716c.79 1.431 2.334 2.39 4.665 2.39s3.875-.959 4.665-2.39l1.431.716c-1.173 1.961-3.406 3.234-6.096 3.234z"/>
                                                    </svg>
                                                    فتح رابط Nextcloud
                                                </a></li>
                                                {% endif %}
                                                {% if doc.onedrive_share_link %}
                                                <li><a class="dropdown-item" onclick="openShareLink('{{ doc.onedrive_share_link }}')">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="#0d6efd" class="me-2">
                                                        <path d="M8.9 2.9c-1.6 0-2.9 1.3-2.9 2.9 0 .2 0 .4.1.6C3.6 7.2 1.5 9.7 1.5 12.8c0 3.5 2.8 6.3 6.3 6.3h9.4c2.9 0 5.3-2.4 5.3-5.3 0-2.5-1.7-4.6-4-5.2-.1-3.1-2.7-5.7-5.8-5.7-1.9 0-3.6.9-4.6 2.3-.4-.2-.8-.3-1.2-.3zm0 1.5c.3 0 .6.1.8.2l.4.2.2-.4c.7-1.3 2.1-2.1 3.6-2.1 2.3 0 4.2 1.9 4.2 4.2v.8h.8c2.1 0 3.8 1.7 3.8 3.8s-1.7 3.8-3.8 3.8H7.8c-2.7 0-4.8-2.2-4.8-4.8 0-2.4 1.7-4.4 4-4.7l.5-.1-.1-.5c0-.2-.1-.4-.1-.6 0-.8.6-1.4 1.4-1.4z"/>
                                                    </svg>
                                                    فتح رابط OneDrive
                                                </a></li>
                                                {% endif %}
                                                {% if not (doc.nextcloud_share_link or doc.onedrive_share_link) %}
                                                <li><span class="dropdown-item-text text-muted">لا توجد روابط متاحة</span></li>
                                                {% endif %}
                                            </ul>
                                        </div>

                                        <button class="btn btn-outline-danger"
                                                onclick="deleteDocument({{ doc.id }})"
                                                title="حذف الوثيقة">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="no-documents">
                            <i class="fas fa-folder-open fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد وثائق مرفوعة</h5>
                            <p class="text-muted">ابدأ برفع الوثائق الخاصة بهذا العقد</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- أزرار التنقل -->
            <div class="text-center mt-4">
                <a href="{{ url_for('contracts.index') }}" class="btn btn-secondary btn-lg me-3">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للعقود
                </a>
                <button class="btn btn-primary btn-lg" onclick="generateContractReport()">
                    <i class="fas fa-file-pdf me-2"></i>
                    إنشاء تقرير العقد
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // متغيرات عامة
        const contractId = {{ contract.contract_id }};
        let selectedFiles = [];

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تم تحميل صفحة إدارة وثائق العقد');
            initializeFileUpload();
        });

        // تهيئة رفع الملفات
        function initializeFileUpload() {
            const fileInput = document.getElementById('fileInput');
            const uploadArea = document.getElementById('uploadArea');

            // معالج اختيار الملفات
            fileInput.addEventListener('change', handleFileSelection);

            // معالجات السحب والإفلات
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleFileDrop);

            // معالج إرسال النموذج
            document.getElementById('uploadForm').addEventListener('submit', handleFormSubmit);
        }

        // معالجة اختيار الملفات
        function handleFileSelection(event) {
            const files = Array.from(event.target.files);
            updateSelectedFiles(files);
        }

        // معالجة السحب فوق المنطقة
        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }

        // معالجة مغادرة منطقة السحب
        function handleDragLeave(event) {
            event.currentTarget.classList.remove('dragover');
        }

        // معالجة إفلات الملفات
        function handleFileDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');

            const files = Array.from(event.dataTransfer.files);
            updateSelectedFiles(files);
        }

        // تحديث قائمة الملفات المختارة
        function updateSelectedFiles(files) {
            selectedFiles = files;
            displaySelectedFiles();
        }

        // عرض الملفات المختارة
        function displaySelectedFiles() {
            const selectedFilesDiv = document.getElementById('selectedFiles');
            const filesListDiv = document.getElementById('filesList');
            const filesCountSpan = document.getElementById('filesCount');
            const totalSizeSpan = document.getElementById('totalSize');

            if (selectedFiles.length === 0) {
                selectedFilesDiv.style.display = 'none';
                return;
            }

            selectedFilesDiv.style.display = 'block';

            let totalSize = 0;
            let filesHTML = '';

            selectedFiles.forEach((file, index) => {
                totalSize += file.size;
                const fileSize = (file.size / 1024).toFixed(1);
                const fileIcon = getFileIcon(file.name);

                filesHTML += `
                    <div class="file-item d-flex justify-content-between align-items-center p-2 mb-2">
                        <div class="d-flex align-items-center">
                            <i class="${fileIcon} me-2"></i>
                            <div>
                                <div class="fw-bold">${file.name}</div>
                                <small class="text-muted">${fileSize} KB</small>
                            </div>
                        </div>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeFile(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
            });

            filesListDiv.innerHTML = filesHTML;
            filesCountSpan.textContent = selectedFiles.length;
            totalSizeSpan.textContent = (totalSize / 1024).toFixed(1) + ' KB';
        }

        // الحصول على أيقونة الملف
        function getFileIcon(fileName) {
            const extension = fileName.split('.').pop().toLowerCase();

            switch (extension) {
                case 'pdf': return 'fas fa-file-pdf';
                case 'doc':
                case 'docx': return 'fas fa-file-word';
                case 'xls':
                case 'xlsx': return 'fas fa-file-excel';
                case 'jpg':
                case 'jpeg':
                case 'png':
                case 'gif': return 'fas fa-file-image';
                case 'zip':
                case 'rar': return 'fas fa-file-archive';
                default: return 'fas fa-file-alt';
            }
        }

        // حذف ملف من القائمة
        function removeFile(index) {
            selectedFiles.splice(index, 1);
            displaySelectedFiles();

            // تحديث input الملفات
            const fileInput = document.getElementById('fileInput');
            const dt = new DataTransfer();
            selectedFiles.forEach(file => dt.items.add(file));
            fileInput.files = dt.files;
        }

        // معالجة إرسال النموذج
        function handleFormSubmit(event) {
            event.preventDefault();

            if (selectedFiles.length === 0) {
                alert('يرجى اختيار ملف واحد على الأقل');
                return;
            }

            const formData = new FormData();
            const form = event.target;

            // إضافة بيانات النموذج
            formData.append('contract_id', contractId);
            formData.append('document_type', form.document_type.value);
            formData.append('document_name', form.document_name.value);
            formData.append('notes', form.notes.value);

            // إضافة الملفات
            selectedFiles.forEach(file => {
                formData.append('document_files', file);
            });

            // إظهار شريط التقدم
            showUploadProgress();

            // رفع الملفات
            uploadFiles(formData);
        }

        // إظهار شريط التقدم
        function showUploadProgress() {
            const progressDiv = document.querySelector('.progress-upload');
            const uploadButton = document.getElementById('uploadButton');

            progressDiv.style.display = 'block';
            uploadButton.disabled = true;
            uploadButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الرفع...';
        }

        // إخفاء شريط التقدم
        function hideUploadProgress() {
            const progressDiv = document.querySelector('.progress-upload');
            const uploadButton = document.getElementById('uploadButton');

            progressDiv.style.display = 'none';
            uploadButton.disabled = false;
            uploadButton.innerHTML = '<i class="fas fa-upload me-2"></i>رفع الوثائق';
        }

        // رفع الملفات
        function uploadFiles(formData) {
            fetch(`/contracts/${contractId}/documents/upload`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                hideUploadProgress();

                if (data.success) {
                    alert('تم رفع الوثائق بنجاح');
                    location.reload(); // إعادة تحميل الصفحة لإظهار الوثائق الجديدة
                } else {
                    alert('خطأ في رفع الوثائق: ' + data.message);
                }
            })
            .catch(error => {
                hideUploadProgress();
                console.error('خطأ في رفع الوثائق:', error);
                alert('حدث خطأ في رفع الوثائق');
            });
        }

        // عرض تفاصيل الوثيقة
        function viewDocumentDetails(documentId) {
            console.log('عرض تفاصيل الوثيقة:', documentId);
            // يمكن إضافة نافذة منبثقة لعرض التفاصيل
        }

        // حذف وثيقة
        function deleteDocument(documentId) {
            if (confirm('هل أنت متأكد من حذف هذه الوثيقة؟')) {
                fetch(`/contracts/documents/${documentId}/delete`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم حذف الوثيقة بنجاح');
                        location.reload();
                    } else {
                        alert('خطأ في حذف الوثيقة: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('خطأ في حذف الوثيقة:', error);
                    alert('حدث خطأ في حذف الوثيقة');
                });
            }
        }

        // إنشاء تقرير العقد
        function generateContractReport() {
            console.log('إنشاء تقرير العقد:', contractId);
            // يمكن إضافة وظيفة إنشاء تقرير PDF
            alert('سيتم إضافة هذه الميزة قريباً');
        }

        // اختبار تحميل الدوال
        console.log('✅ تم تحميل دوال إدارة الوثائق');

        // إنشاء رابط مشاركة (دعم خدمات متعددة)
        function createShareLink(documentId, service) {
            console.log('🔗 تم استدعاء createShareLink:', documentId, service);
            const serviceName = service === 'nextcloud' ? 'Nextcloud' : 'OneDrive';

            if (confirm(`هل تريد إنشاء رابط مشاركة ${serviceName} لهذه الوثيقة؟`)) {
                console.log('✅ تم تأكيد إنشاء الرابط');
                // إظهار مؤشر التحميل
                showToast(`جاري إنشاء رابط ${serviceName}...`, 'info', 'fas fa-spinner fa-spin');

                console.log('📡 إرسال طلب إلى:', `/contracts/documents/${documentId}/create-link`);

                fetch(`/contracts/documents/${documentId}/create-link`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        service: service
                    })
                })
                .then(response => {
                    console.log('📨 استجابة الخادم:', response);
                    return response.json();
                })
                .then(data => {
                    console.log('📄 بيانات الاستجابة:', data);
                    if (data.success) {
                        if (data.is_existing) {
                            showToast(`رابط ${serviceName} موجود مسبقاً`, 'warning', 'fas fa-info-circle');
                        } else {
                            showToast(`تم إنشاء رابط ${serviceName} بنجاح!`, 'success', 'fas fa-check-circle');
                        }

                        // نسخ الرابط للحافظة
                        navigator.clipboard.writeText(data.share_link).then(() => {
                            showToast('تم نسخ الرابط تلقائياً للحافظة', 'info', 'fas fa-copy');
                        });

                        // إعادة تحميل الصفحة لتحديث الأزرار
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        showToast(`خطأ في إنشاء رابط ${serviceName}: ` + data.message, 'danger', 'fas fa-exclamation-circle');
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ في الطلب:', error);
                    showToast(`حدث خطأ في إنشاء رابط ${serviceName}`, 'danger', 'fas fa-exclamation-triangle');
                });
            }
        }

        // نسخ رابط المشاركة
        function copyShareLink(shareLink) {
            if (shareLink && shareLink.trim() !== '') {
                navigator.clipboard.writeText(shareLink).then(() => {
                    showToast('تم نسخ رابط المشاركة بنجاح!', 'success', 'fas fa-check-circle');
                }).catch(err => {
                    showToast('فشل في نسخ الرابط: ' + err, 'danger', 'fas fa-exclamation-circle');
                });
            } else {
                showToast('رابط المشاركة غير متوفر', 'warning', 'fas fa-exclamation-triangle');
            }
        }

        // فتح رابط المشاركة في نافذة جديدة
        function openShareLink(shareLink) {
            if (shareLink && shareLink.trim() !== '') {
                window.open(shareLink, '_blank', 'noopener,noreferrer');
                showToast('تم فتح رابط المشاركة في نافذة جديدة', 'info', 'fas fa-external-link-alt');
            } else {
                showToast('رابط المشاركة غير متوفر', 'warning', 'fas fa-exclamation-triangle');
            }
        }

        // إظهار رسالة Toast
        function showToast(message, type = 'info', icon = 'fas fa-info-circle') {
            // إنشاء عنصر Toast
            const toastId = 'toast-' + Date.now();
            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="${icon} me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;

            // إضافة Toast للصفحة
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            // إظهار Toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, {
                autohide: true,
                delay: 4000
            });
            toast.show();

            // حذف Toast بعد الإخفاء
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }
    </script>
</body>
</html>
