{% extends "base.html" %}

{% block title %}ربط الإشعارات بجهات الاتصال{% endblock %}

{% block extra_css %}
<style>
.notification-card {
    border: 1px solid #e3e6f0;
    border-radius: 10px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.notification-card:hover {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.notification-card.disabled {
    opacity: 0.6;
    background-color: #f8f9fc;
}

.contact-item {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 5px;
    padding: 10px;
    margin: 5px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-item.inactive {
    opacity: 0.6;
    background-color: #fff3cd;
}

.add-contact-area {
    border: 2px dashed #e3e6f0;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    margin: 10px 0;
    transition: all 0.3s ease;
}

.add-contact-area:hover {
    border-color: #4e73df;
    background-color: #f8f9fc;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-link text-primary"></i>
            ربط الإشعارات بجهات الاتصال
        </h1>
        <div>
            <a href="{{ url_for('instant_notifications.contacts') }}" class="btn btn-info btn-sm">
                <i class="fas fa-users"></i> إدارة جهات الاتصال
            </a>
            <a href="{{ url_for('instant_notifications.dashboard') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>

    <!-- معلومات مفيدة -->
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i>
        <strong>معلومات:</strong> يمكنك ربط كل نوع إشعار بعدة جهات اتصال. ستصل الرسالة لجميع الجهات المرتبطة عند تشغيل الإشعار.
    </div>

    <!-- قائمة الإشعارات -->
    {% if notifications_map %}
        {% for event_type, notification in notifications_map.items() %}
        <div class="notification-card {{ 'disabled' if not notification.is_enabled else '' }}">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="m-0 font-weight-bold text-primary">
                        {{ notification.event_name_ar }}
                    </h6>
                    <small class="text-muted">{{ event_type }}</small>
                </div>
                <div>
                    <span class="badge {{ 'bg-success' if notification.is_enabled else 'bg-secondary' }}">
                        {{ 'مفعل' if notification.is_enabled else 'معطل' }}
                    </span>
                </div>
            </div>
            
            <div class="card-body">
                <!-- جهات الاتصال المرتبطة -->
                <div class="mb-3">
                    <label class="form-label"><strong>جهات الاتصال المرتبطة:</strong></label>
                    
                    {% if notification.contacts %}
                        {% for contact in notification.contacts %}
                        <div class="contact-item {{ 'inactive' if not contact.mapping_active else '' }}">
                            <div>
                                <strong>{{ contact.contact_name }}</strong>
                                <br>
                                <small class="text-muted">{{ contact.phone_number }}</small>
                            </div>
                            <div>
                                <span class="badge {{ 'bg-danger' if contact.contact_type == 'admin' else 'bg-info' }} me-2">
                                    {{ 'إدارة' if contact.contact_type == 'admin' else 'عام' }}
                                </span>
                                <button class="btn btn-danger btn-sm" 
                                        onclick="removeMapping({{ contact.mapping_id }}, '{{ contact.contact_name }}', '{{ notification.event_name_ar }}')">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            لا توجد جهات اتصال مرتبطة بهذا الإشعار
                        </div>
                    {% endif %}
                </div>

                <!-- إضافة جهة اتصال جديدة -->
                <div class="add-contact-area">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <select class="form-select" id="contact_{{ event_type }}">
                                <option value="">اختر جهة اتصال لإضافتها</option>
                                {% for contact in all_contacts %}
                                    {% set already_linked = contact.id in notification.contacts|map(attribute='contact_id')|list %}
                                    {% if not already_linked %}
                                    <option value="{{ contact.id }}">
                                        {{ contact.contact_name }} - {{ contact.phone_number }}
                                        ({{ 'إدارة' if contact.contact_type == 'admin' else 'عام' }})
                                    </option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-primary w-100" 
                                    onclick="addMapping('{{ event_type }}', '{{ notification.event_name_ar }}')">
                                <i class="fas fa-plus"></i> إضافة ربط
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="card shadow">
            <div class="card-body text-center py-5">
                <i class="fas fa-link fa-3x text-muted mb-3"></i>
                <h5>لا توجد إشعارات للربط</h5>
                <p class="text-muted">لم يتم العثور على أنواع إشعارات</p>
                <a href="{{ url_for('instant_notifications.settings') }}" class="btn btn-primary">
                    <i class="fas fa-cog"></i> إدارة الإشعارات
                </a>
            </div>
        </div>
    {% endif %}

    <!-- إحصائيات سريعة -->
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                أنواع الإشعارات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ notifications_map|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bell fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                إجمالي الروابط
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% set total_links = 0 %}
                                {% for event_type, notification in notifications_map.items() %}
                                    {% set total_links = total_links + notification.contacts|length %}
                                {% endfor %}
                                {{ total_links }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-link fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                جهات الاتصال المتاحة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ all_contacts|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function addMapping(eventType, eventNameAr) {
    const selectElement = document.getElementById(`contact_${eventType}`);
    const contactId = selectElement.value;
    
    if (!contactId) {
        alert('يرجى اختيار جهة اتصال');
        return;
    }
    
    const contactText = selectElement.options[selectElement.selectedIndex].text;
    
    if (confirm(`هل تريد ربط "${eventNameAr}" بـ "${contactText}"؟`)) {
        fetch('/instant-notifications/api/add_mapping', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                event_type: eventType,
                contact_id: contactId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم إضافة الربط بنجاح', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('فشل في إضافة الربط: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('خطأ في الاتصال: ' + error, 'error');
        });
    }
}

function removeMapping(mappingId, contactName, eventNameAr) {
    if (confirm(`هل تريد إلغاء ربط "${contactName}" من "${eventNameAr}"؟`)) {
        fetch(`/instant-notifications/api/remove_mapping/${mappingId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم حذف الربط بنجاح', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('فشل في حذف الربط: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('خطأ في الاتصال: ' + error, 'error');
        });
    }
}

function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 'alert-info';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
