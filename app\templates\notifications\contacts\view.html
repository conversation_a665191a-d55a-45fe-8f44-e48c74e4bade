{% extends "base.html" %}

{% block title %}عرض جهة الاتصال{% endblock %}

{% block extra_css %}
<style>
    .info-section {
        background: white;
        border-radius: 0.5rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }
    
    .section-title {
        color: #5a5c69;
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #e3e6f0;
    }
    
    .contact-header {
        background: linear-gradient(45deg, #4e73df, #224abe);
        color: white;
        border-radius: 0.5rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .contact-avatar {
        width: 80px;
        height: 80px;
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }
    
    .info-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f8f9fc;
    }
    
    .info-item:last-child {
        border-bottom: none;
    }
    
    .info-icon {
        width: 40px;
        height: 40px;
        background: #f8f9fc;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        color: #5a5c69;
    }
    
    .info-content {
        flex: 1;
    }
    
    .info-label {
        font-weight: 600;
        color: #5a5c69;
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
    }
    
    .info-value {
        color: #3a3b45;
        font-size: 1rem;
    }
    
    .priority-badge {
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        font-weight: 600;
        font-size: 0.875rem;
    }
    
    .priority-high {
        background: #e74a3b;
        color: white;
    }
    
    .priority-medium {
        background: #f39c12;
        color: white;
    }
    
    .priority-low {
        background: #28a745;
        color: white;
    }
    
    .channel-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        margin: 0.25rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    .channel-sms {
        background: #e3f2fd;
        color: #1976d2;
    }
    
    .channel-email {
        background: #f3e5f5;
        color: #7b1fa2;
    }
    
    .channel-whatsapp {
        background: #e8f5e8;
        color: #388e3c;
    }
    
    .vip-badge {
        background: linear-gradient(45deg, #f6c23e, #dda20a);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        font-weight: 600;
        border: none;
    }
    
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        font-weight: 600;
    }
    
    .status-active {
        background: #d4edda;
        color: #155724;
    }
    
    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }
    
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 3rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Loading Spinner -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-2">جاري تحميل بيانات جهة الاتصال...</p>
    </div>

    <!-- Main Content -->
    <div id="mainContent" style="display: none;">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="fas fa-user text-info me-2"></i>
                            عرض جهة الاتصال
                        </h1>
                        <p class="text-muted mb-0">عرض تفاصيل جهة الاتصال في نظام الإشعارات</p>
                    </div>
                    <div>
                        <a href="/notifications/contacts/" class="btn btn-secondary me-2">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للقائمة
                        </a>
                        <button class="btn btn-warning" onclick="editContact()">
                            <i class="fas fa-edit me-2"></i>
                            تعديل
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Header -->
        <div class="contact-header">
            <div class="row align-items-center">
                <div class="col-md-2 text-center">
                    <div class="contact-avatar mx-auto" id="contactAvatar">
                        <span id="contactInitial">N</span>
                    </div>
                </div>
                <div class="col-md-10">
                    <h2 class="mb-2" id="contactName">جهة الاتصال</h2>
                    <div class="d-flex flex-wrap align-items-center">
                        <span class="badge bg-light text-dark me-2 mb-2" id="contactType">نوع جهة الاتصال</span>
                        <span class="priority-badge me-2 mb-2" id="priorityBadge">أولوية متوسطة</span>
                        <span class="vip-badge me-2 mb-2" id="vipBadge" style="display: none;">
                            <i class="fas fa-star me-1"></i>VIP
                        </span>
                        <span class="status-badge mb-2" id="statusBadge">نشط</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="row">
            <!-- معلومات الاتصال -->
            <div class="col-lg-6">
                <div class="info-section">
                    <h4 class="section-title">
                        <i class="fas fa-phone me-2"></i>
                        معلومات الاتصال
                    </h4>
                    
                    <div class="info-item" id="phoneInfo" style="display: none;">
                        <div class="info-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">رقم الهاتف</div>
                            <div class="info-value" id="phoneNumber">-</div>
                        </div>
                    </div>
                    
                    <div class="info-item" id="emailInfo" style="display: none;">
                        <div class="info-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">البريد الإلكتروني</div>
                            <div class="info-value" id="emailAddress">-</div>
                        </div>
                    </div>
                    
                    <div class="info-item" id="whatsappInfo" style="display: none;">
                        <div class="info-icon">
                            <i class="fab fa-whatsapp text-success"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">رقم WhatsApp</div>
                            <div class="info-value" id="whatsappNumber">-</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات الشركة -->
            <div class="col-lg-6">
                <div class="info-section">
                    <h4 class="section-title">
                        <i class="fas fa-building me-2"></i>
                        معلومات الشركة
                    </h4>
                    
                    <div class="info-item" id="companyInfo" style="display: none;">
                        <div class="info-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">الشركة</div>
                            <div class="info-value" id="companyName">-</div>
                        </div>
                    </div>
                    
                    <div class="info-item" id="positionInfo" style="display: none;">
                        <div class="info-icon">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">المنصب</div>
                            <div class="info-value" id="position">-</div>
                        </div>
                    </div>
                    
                    <div class="info-item" id="departmentInfo" style="display: none;">
                        <div class="info-icon">
                            <i class="fas fa-sitemap"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">القسم</div>
                            <div class="info-value" id="department">-</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات الإشعارات -->
        <div class="row">
            <div class="col-12">
                <div class="info-section">
                    <h4 class="section-title">
                        <i class="fas fa-bell me-2"></i>
                        إعدادات الإشعارات
                    </h4>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-broadcast-tower"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">القنوات المفضلة</div>
                            <div class="info-value" id="preferredChannels">
                                <span class="channel-badge channel-sms">SMS</span>
                                <span class="channel-badge channel-email">Email</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ملاحظات -->
        <div class="row" id="notesSection" style="display: none;">
            <div class="col-12">
                <div class="info-section">
                    <h4 class="section-title">
                        <i class="fas fa-sticky-note me-2"></i>
                        ملاحظات
                    </h4>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-comment"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-value" id="notes">-</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات النظام -->
        <div class="row">
            <div class="col-12">
                <div class="info-section">
                    <h4 class="section-title">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات النظام
                    </h4>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-calendar-plus"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">تاريخ الإنشاء</div>
                            <div class="info-value" id="createdAt">-</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let contactId = null;

// تحميل بيانات جهة الاتصال
function loadContactData() {
    // الحصول على معرف جهة الاتصال من URL
    const urlParams = new URLSearchParams(window.location.search);
    contactId = urlParams.get('id');
    
    if (!contactId) {
        alert('❌ معرف جهة الاتصال مفقود');
        window.location.href = '/notifications/contacts/';
        return;
    }
    
    // عرض spinner التحميل
    document.getElementById('loadingSpinner').style.display = 'block';
    document.getElementById('mainContent').style.display = 'none';
    
    // جلب بيانات جهة الاتصال
    fetch(`/notifications/contacts/api/contacts/${contactId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.contact) {
            displayContactData(data.contact);
            document.getElementById('loadingSpinner').style.display = 'none';
            document.getElementById('mainContent').style.display = 'block';
        } else {
            alert('❌ لم يتم العثور على جهة الاتصال');
            window.location.href = '/notifications/contacts/';
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('❌ حدث خطأ في تحميل بيانات جهة الاتصال');
        window.location.href = '/notifications/contacts/';
    });
}

// عرض بيانات جهة الاتصال
function displayContactData(contact) {
    // معلومات الرأس
    document.getElementById('contactInitial').textContent = contact.contact_name ? contact.contact_name[0].toUpperCase() : 'N';
    document.getElementById('contactName').textContent = contact.contact_name || 'جهة الاتصال';
    
    // نوع جهة الاتصال
    const typeNames = {
        'CUSTOMER': 'عميل',
        'DRIVER': 'سائق', 
        'AGENT': 'مخلص جمركي',
        'MANAGER': 'مدير',
        'EXTERNAL': 'خارجي'
    };
    document.getElementById('contactType').textContent = typeNames[contact.contact_type] || contact.contact_type;
    
    // مستوى الأولوية
    const priorityBadge = document.getElementById('priorityBadge');
    const priority = contact.priority_level || 5;
    if (priority >= 8) {
        priorityBadge.className = 'priority-badge priority-high me-2 mb-2';
        priorityBadge.textContent = 'أولوية عالية';
    } else if (priority >= 5) {
        priorityBadge.className = 'priority-badge priority-medium me-2 mb-2';
        priorityBadge.textContent = 'أولوية متوسطة';
    } else {
        priorityBadge.className = 'priority-badge priority-low me-2 mb-2';
        priorityBadge.textContent = 'أولوية منخفضة';
    }
    
    // VIP
    if (contact.is_vip) {
        document.getElementById('vipBadge').style.display = 'inline-block';
    }
    
    // الحالة
    const statusBadge = document.getElementById('statusBadge');
    if (contact.is_active !== false) {
        statusBadge.className = 'status-badge status-active mb-2';
        statusBadge.textContent = 'نشط';
    } else {
        statusBadge.className = 'status-badge status-inactive mb-2';
        statusBadge.textContent = 'غير نشط';
    }
    
    // معلومات الاتصال
    if (contact.phone_number) {
        document.getElementById('phoneNumber').textContent = contact.phone_number;
        document.getElementById('phoneInfo').style.display = 'flex';
    }
    
    if (contact.email_address) {
        document.getElementById('emailAddress').textContent = contact.email_address;
        document.getElementById('emailInfo').style.display = 'flex';
    }
    
    if (contact.whatsapp_number) {
        document.getElementById('whatsappNumber').textContent = contact.whatsapp_number;
        document.getElementById('whatsappInfo').style.display = 'flex';
    }
    
    // معلومات الشركة
    if (contact.company_name) {
        document.getElementById('companyName').textContent = contact.company_name;
        document.getElementById('companyInfo').style.display = 'flex';
    }
    
    if (contact.position) {
        document.getElementById('position').textContent = contact.position;
        document.getElementById('positionInfo').style.display = 'flex';
    }
    
    if (contact.department) {
        document.getElementById('department').textContent = contact.department;
        document.getElementById('departmentInfo').style.display = 'flex';
    }
    
    // القنوات المفضلة
    const channelsContainer = document.getElementById('preferredChannels');
    channelsContainer.innerHTML = '';
    
    if (contact.preferred_channels) {
        const channels = contact.preferred_channels.split(',');
        channels.forEach(channel => {
            const badge = document.createElement('span');
            badge.className = `channel-badge channel-${channel.toLowerCase().trim()}`;
            badge.textContent = channel.trim();
            channelsContainer.appendChild(badge);
        });
    }
    
    // الملاحظات
    if (contact.notes) {
        document.getElementById('notes').textContent = contact.notes;
        document.getElementById('notesSection').style.display = 'block';
    }
    
    // تاريخ الإنشاء
    if (contact.created_at) {
        const createdDate = new Date(contact.created_at);
        document.getElementById('createdAt').textContent = createdDate.toLocaleDateString('ar-SA');
    }
}

// تعديل جهة الاتصال
function editContact() {
    window.location.href = `/notifications/contacts/edit?id=${contactId}`;
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadContactData();
});
</script>
{% endblock %}
