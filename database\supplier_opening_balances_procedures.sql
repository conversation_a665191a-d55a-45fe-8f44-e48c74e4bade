-- =====================================================
-- إجراءات الأرصدة الافتتاحية للموردين
-- Supplier Opening Balances Procedures
-- =====================================================

-- 1. إجراء إنشاء رصيد افتتاحي جديد
CREATE OR REPLACE PROCEDURE CREATE_OPENING_BALANCE(
    p_supplier_id IN NUMBER,
    p_fiscal_date IN DATE,
    p_currency_code IN VARCHAR2,
    p_balance_amount IN NUMBER,
    p_notes IN VARCHAR2 DEFAULT NULL,
    p_reference_doc IN VARCHAR2 DEFAULT NULL,
    p_created_by IN NUMBER,
    p_opening_balance_id OUT NUMBER
) AS
    v_existing_count NUMBER := 0;
BEGIN
    -- التحقق من عدم وجود رصيد مكرر
    SELECT COUNT(*)
    INTO v_existing_count
    FROM SUPPLIER_OPENING_BALANCES
    WHERE supplier_id = p_supplier_id
    AND fiscal_period_start_date = p_fiscal_date
    AND currency_code = p_currency_code
    AND is_active = 1;
    
    IF v_existing_count > 0 THEN
        RAISE_APPLICATION_ERROR(-20010, 'يوجد رصيد افتتاحي مسبق لهذا المورد في نفس الفترة والعملة');
    END IF;
    
    -- إنشاء الرصيد الجديد
    INSERT INTO SUPPLIER_OPENING_BALANCES (
        supplier_id, fiscal_period_start_date, currency_code,
        opening_balance_amount, notes, reference_document,
        created_by, status
    ) VALUES (
        p_supplier_id, p_fiscal_date, p_currency_code,
        p_balance_amount, p_notes, p_reference_doc,
        p_created_by, 'DRAFT'
    ) RETURNING id INTO p_opening_balance_id;
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END CREATE_OPENING_BALANCE;
/

-- 2. إجراء تحديث رصيد افتتاحي
CREATE OR REPLACE PROCEDURE UPDATE_OPENING_BALANCE(
    p_opening_balance_id IN NUMBER,
    p_balance_amount IN NUMBER DEFAULT NULL,
    p_notes IN VARCHAR2 DEFAULT NULL,
    p_reference_doc IN VARCHAR2 DEFAULT NULL,
    p_updated_by IN NUMBER
) AS
    v_current_status VARCHAR2(20);
BEGIN
    -- التحقق من حالة الرصيد
    SELECT status
    INTO v_current_status
    FROM SUPPLIER_OPENING_BALANCES
    WHERE id = p_opening_balance_id;
    
    IF v_current_status NOT IN ('DRAFT', 'UNDER_REVIEW') THEN
        RAISE_APPLICATION_ERROR(-20011, 'لا يمكن تعديل رصيد معتمد أو مرحل');
    END IF;
    
    -- تحديث البيانات
    UPDATE SUPPLIER_OPENING_BALANCES
    SET opening_balance_amount = NVL(p_balance_amount, opening_balance_amount),
        notes = NVL(p_notes, notes),
        reference_document = NVL(p_reference_doc, reference_document),
        updated_by = p_updated_by,
        updated_date = CURRENT_TIMESTAMP
    WHERE id = p_opening_balance_id;
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END UPDATE_OPENING_BALANCE;
/

-- 3. إجراء مراجعة الأرصدة الافتتاحية
CREATE OR REPLACE PROCEDURE REVIEW_OPENING_BALANCES(
    p_fiscal_date IN DATE,
    p_currency_code IN VARCHAR2,
    p_reviewed_by IN NUMBER,
    p_review_notes IN VARCHAR2 DEFAULT NULL,
    p_approve IN NUMBER DEFAULT 0 -- 0 = مراجعة فقط، 1 = مراجعة واعتماد
) AS
    v_draft_count NUMBER := 0;
BEGIN
    -- التحقق من وجود أرصدة في حالة مسودة
    SELECT COUNT(*)
    INTO v_draft_count
    FROM SUPPLIER_OPENING_BALANCES
    WHERE fiscal_period_start_date = p_fiscal_date
    AND currency_code = p_currency_code
    AND status = 'DRAFT'
    AND is_active = 1;
    
    IF v_draft_count = 0 THEN
        RAISE_APPLICATION_ERROR(-20012, 'لا توجد أرصدة في حالة مسودة للمراجعة');
    END IF;
    
    -- تحديث حالة الأرصدة
    IF p_approve = 1 THEN
        -- مراجعة واعتماد
        UPDATE SUPPLIER_OPENING_BALANCES
        SET status = 'APPROVED',
            reviewed_date = CURRENT_TIMESTAMP,
            reviewed_by = p_reviewed_by,
            reviewed_notes = p_review_notes,
            approved_date = CURRENT_TIMESTAMP,
            approved_by = p_reviewed_by,
            approval_notes = p_review_notes
        WHERE fiscal_period_start_date = p_fiscal_date
        AND currency_code = p_currency_code
        AND status = 'DRAFT'
        AND is_active = 1;
    ELSE
        -- مراجعة فقط
        UPDATE SUPPLIER_OPENING_BALANCES
        SET status = 'UNDER_REVIEW',
            reviewed_date = CURRENT_TIMESTAMP,
            reviewed_by = p_reviewed_by,
            reviewed_notes = p_review_notes
        WHERE fiscal_period_start_date = p_fiscal_date
        AND currency_code = p_currency_code
        AND status = 'DRAFT'
        AND is_active = 1;
    END IF;
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END REVIEW_OPENING_BALANCES;
/

-- 4. إجراء اعتماد الأرصدة الافتتاحية
CREATE OR REPLACE PROCEDURE APPROVE_OPENING_BALANCES(
    p_fiscal_date IN DATE,
    p_currency_code IN VARCHAR2,
    p_approved_by IN NUMBER,
    p_approval_notes IN VARCHAR2 DEFAULT NULL
) AS
    v_review_count NUMBER := 0;
BEGIN
    -- التحقق من وجود أرصدة تحت المراجعة
    SELECT COUNT(*)
    INTO v_review_count
    FROM SUPPLIER_OPENING_BALANCES
    WHERE fiscal_period_start_date = p_fiscal_date
    AND currency_code = p_currency_code
    AND status = 'UNDER_REVIEW'
    AND is_active = 1;
    
    IF v_review_count = 0 THEN
        RAISE_APPLICATION_ERROR(-20013, 'لا توجد أرصدة تحت المراجعة للاعتماد');
    END IF;
    
    -- اعتماد الأرصدة
    UPDATE SUPPLIER_OPENING_BALANCES
    SET status = 'APPROVED',
        approved_date = CURRENT_TIMESTAMP,
        approved_by = p_approved_by,
        approval_notes = p_approval_notes
    WHERE fiscal_period_start_date = p_fiscal_date
    AND currency_code = p_currency_code
    AND status = 'UNDER_REVIEW'
    AND is_active = 1;
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END APPROVE_OPENING_BALANCES;
/

-- 5. إجراء ترحيل الأرصدة الافتتاحية
CREATE OR REPLACE PROCEDURE POST_OPENING_BALANCES(
    p_fiscal_date IN DATE,
    p_currency_code IN VARCHAR2,
    p_posted_by IN NUMBER,
    p_posting_reference IN VARCHAR2 DEFAULT NULL
) AS
    v_approved_count NUMBER := 0;
    v_posting_ref VARCHAR2(100);
BEGIN
    -- التحقق من وجود أرصدة معتمدة
    SELECT COUNT(*)
    INTO v_approved_count
    FROM SUPPLIER_OPENING_BALANCES
    WHERE fiscal_period_start_date = p_fiscal_date
    AND currency_code = p_currency_code
    AND status = 'APPROVED'
    AND is_active = 1;
    
    IF v_approved_count = 0 THEN
        RAISE_APPLICATION_ERROR(-20014, 'لا توجد أرصدة معتمدة للترحيل');
    END IF;
    
    -- إنشاء مرجع الترحيل
    v_posting_ref := NVL(p_posting_reference, 'OB' || TO_CHAR(p_fiscal_date, 'YYYYMMDD') || '_' || p_currency_code);
    
    -- ترحيل الأرصدة إلى جدول المعاملات
    INSERT INTO SUPPLIER_TRANSACTIONS (
        supplier_id, transaction_type, reference_type, reference_id,
        transaction_date, currency_code, original_amount,
        debit_amount, credit_amount, description, status,
        created_date, created_by
    )
    SELECT 
        sob.supplier_id,
        'OPENING_BALANCE',
        'FISCAL_PERIOD',
        sob.id,
        sob.fiscal_period_start_date,
        sob.currency_code,
        sob.opening_balance_amount,
        CASE WHEN sob.balance_type = 'DEBIT' THEN sob.opening_balance_amount ELSE 0 END,
        CASE WHEN sob.balance_type = 'CREDIT' THEN sob.opening_balance_amount ELSE 0 END,
        'رصيد افتتاحي للفترة المحاسبية ' || TO_CHAR(sob.fiscal_period_start_date, 'YYYY') || 
        CASE WHEN sob.notes IS NOT NULL THEN ' - ' || sob.notes ELSE '' END,
        'POSTED',
        CURRENT_TIMESTAMP,
        p_posted_by
    FROM SUPPLIER_OPENING_BALANCES sob
    WHERE sob.fiscal_period_start_date = p_fiscal_date
    AND sob.currency_code = p_currency_code
    AND sob.status = 'APPROVED'
    AND sob.is_active = 1;
    
    -- تحديث حالة الأرصدة الافتتاحية
    UPDATE SUPPLIER_OPENING_BALANCES
    SET status = 'POSTED',
        posted_date = CURRENT_TIMESTAMP,
        posted_by = p_posted_by,
        posting_reference = v_posting_ref
    WHERE fiscal_period_start_date = p_fiscal_date
    AND currency_code = p_currency_code
    AND status = 'APPROVED'
    AND is_active = 1;
    
    -- تحديث أرصدة الموردين
    FOR rec IN (
        SELECT supplier_id, currency_code, 
               SUM(CASE WHEN balance_type = 'DEBIT' THEN opening_balance_amount ELSE -opening_balance_amount END) as net_amount
        FROM SUPPLIER_OPENING_BALANCES
        WHERE fiscal_period_start_date = p_fiscal_date
        AND currency_code = p_currency_code
        AND status = 'POSTED'
        AND is_active = 1
        GROUP BY supplier_id, currency_code
    ) LOOP
        -- تحديث أو إنشاء رصيد المورد
        MERGE INTO SUPPLIER_BALANCES sb
        USING (SELECT rec.supplier_id as supplier_id, rec.currency_code as currency_code FROM DUAL) src
        ON (sb.supplier_id = src.supplier_id AND sb.currency_code = src.currency_code)
        WHEN MATCHED THEN
            UPDATE SET
                opening_balance = rec.net_amount,
                current_balance = current_balance + rec.net_amount,
                updated_at = CURRENT_TIMESTAMP
        WHEN NOT MATCHED THEN
            INSERT (supplier_id, currency_code, opening_balance, current_balance)
            VALUES (rec.supplier_id, rec.currency_code, rec.net_amount, rec.net_amount);
    END LOOP;
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END POST_OPENING_BALANCES;
/

-- 6. إجراء إلغاء رصيد افتتاحي
CREATE OR REPLACE PROCEDURE CANCEL_OPENING_BALANCE(
    p_opening_balance_id IN NUMBER,
    p_cancelled_by IN NUMBER,
    p_cancel_reason IN VARCHAR2
) AS
    v_current_status VARCHAR2(20);
BEGIN
    -- التحقق من حالة الرصيد
    SELECT status
    INTO v_current_status
    FROM SUPPLIER_OPENING_BALANCES
    WHERE id = p_opening_balance_id;
    
    IF v_current_status = 'POSTED' THEN
        RAISE_APPLICATION_ERROR(-20015, 'لا يمكن إلغاء رصيد مرحل');
    END IF;
    
    -- إلغاء الرصيد
    UPDATE SUPPLIER_OPENING_BALANCES
    SET status = 'CANCELLED',
        updated_by = p_cancelled_by,
        updated_date = CURRENT_TIMESTAMP,
        notes = NVL(notes, '') || ' - ملغي: ' || p_cancel_reason
    WHERE id = p_opening_balance_id;
    
    -- تسجيل عملية الإلغاء
    INSERT INTO SUPPLIER_OPENING_BALANCES_AUDIT (
        audit_id, opening_balance_id, operation_type,
        operation_reason, operation_by
    ) VALUES (
        SUPPLIER_OPENING_AUDIT_SEQ.NEXTVAL, p_opening_balance_id, 'STATUS_CHANGE',
        'إلغاء الرصيد: ' || p_cancel_reason, p_cancelled_by
    );
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END CANCEL_OPENING_BALANCE;
/

-- 7. إجراء الحصول على ملخص الأرصدة
CREATE OR REPLACE PROCEDURE GET_OPENING_BALANCES_SUMMARY(
    p_fiscal_date IN DATE,
    p_currency_code IN VARCHAR2,
    p_cursor OUT SYS_REFCURSOR
) AS
BEGIN
    OPEN p_cursor FOR
    SELECT 
        obs.total_suppliers_count,
        obs.debit_balances_count,
        obs.credit_balances_count,
        obs.total_debit_amount,
        obs.total_credit_amount,
        obs.net_balance_amount,
        obs.draft_count,
        obs.approved_count,
        obs.posted_count,
        obs.last_calculated_date
    FROM OPENING_BALANCES_SUMMARY obs
    WHERE obs.fiscal_period_start_date = p_fiscal_date
    AND obs.currency_code = p_currency_code;
END GET_OPENING_BALANCES_SUMMARY;
/

-- إظهار رسالة نجاح
SELECT 'تم إنشاء الإجراءات المخزنة بنجاح!' AS status FROM DUAL;
