#!/usr/bin/env python3
"""
إضافة الأعمدة المفقودة لجدول email_messages
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_manager import DatabaseManager
import logging

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_missing_columns():
    """إضافة الأعمدة المفقودة لجدول email_messages"""
    try:
        db = DatabaseManager()
        
        # قائمة الأعمدة المطلوبة
        columns_to_add = [
            {
                'name': 'is_archived',
                'definition': 'NUMBER(1) DEFAULT 0',
                'description': 'حالة الأرشفة'
            },
            {
                'name': 'is_important',
                'definition': 'NUMBER(1) DEFAULT 0',
                'description': 'حالة الأهمية'
            },
            {
                'name': 'is_read',
                'definition': 'NUMBER(1) DEFAULT 0',
                'description': 'حالة القراءة'
            },
            {
                'name': 'has_attachments',
                'definition': 'NUMBER(1) DEFAULT 0',
                'description': 'وجود مرفقات'
            }
        ]
        
        for column in columns_to_add:
            try:
                # محاولة إضافة العمود
                query = f"ALTER TABLE email_messages ADD {column['name']} {column['definition']}"
                db.execute_update(query)
                db.commit()
                logger.info(f"✅ تم إضافة العمود {column['name']} - {column['description']}")
                
            except Exception as e:
                error_msg = str(e)
                if "ORA-01430" in error_msg:
                    logger.info(f"ℹ️ العمود {column['name']} موجود بالفعل")
                else:
                    logger.warning(f"⚠️ خطأ في إضافة العمود {column['name']}: {e}")
        
        # التحقق من الأعمدة الموجودة
        check_query = """
        SELECT column_name 
        FROM user_tab_columns 
        WHERE table_name = 'EMAIL_MESSAGES' 
        AND column_name IN ('IS_ARCHIVED', 'IS_IMPORTANT', 'IS_READ', 'HAS_ATTACHMENTS')
        ORDER BY column_name
        """
        
        result = db.execute_query(check_query)
        if result:
            logger.info("📋 الأعمدة الموجودة:")
            for row in result:
                logger.info(f"   - {row[0]}")
        
        logger.info("🎉 تم الانتهاء من إضافة الأعمدة المفقودة")
        
    except Exception as e:
        logger.error(f"❌ خطأ عام: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🔧 إضافة الأعمدة المفقودة لجدول email_messages...")
    success = add_missing_columns()
    
    if success:
        print("✅ تم بنجاح!")
    else:
        print("❌ فشل في العملية!")
        sys.exit(1)
