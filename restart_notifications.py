#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إعادة تشغيل نظام الإشعارات الفورية
Restart Instant Notifications System
"""

import os
import sys
import importlib
import time

def restart_instant_notifications():
    """إعادة تشغيل نظام الإشعارات الفورية"""
    print("🔄 إعادة تشغيل نظام الإشعارات الفورية...")
    
    try:
        # إيقاف النظام الحالي
        try:
            from app.services.instant_event_processor import stop_instant_processor
            stop_instant_processor()
            print("✅ تم إيقاف النظام القديم")
        except Exception as e:
            print(f"⚠️ تحذير في إيقاف النظام: {e}")
        
        # إعادة تحميل جميع الوحدات المتعلقة
        modules_to_reload = [
            'app.services.instant_event_processor',
            'app.services.green_whatsapp_service',
        ]
        
        for module_name in modules_to_reload:
            try:
                if module_name in sys.modules:
                    importlib.reload(sys.modules[module_name])
                    print(f"✅ تم إعادة تحميل {module_name}")
            except Exception as e:
                print(f"⚠️ خطأ في إعادة تحميل {module_name}: {e}")
        
        # انتظار قصير
        time.sleep(2)
        
        # بدء النظام الجديد
        from app.services.instant_event_processor import start_instant_processor
        start_instant_processor()
        print("✅ تم بدء النظام المحدث")
        
        # اختبار النظام
        print("\n🧪 اختبار النظام المحدث...")
        test_system()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعادة التشغيل: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system():
    """اختبار النظام المحدث"""
    try:
        from app.services.instant_event_processor import instant_processor
        
        if instant_processor:
            # بيانات اختبار
            test_data = {
                'booking_number': '6419244690',
                'shipment_id': 174,
                'old_status': 'in_transit',
                'new_status': 'delivered'
            }
            
            # اختبار دالة واحدة
            result = instant_processor._send_delivery_notification(test_data)
            
            if result:
                print("✅ النظام يعمل بشكل صحيح مع booking_number")
                return True
            else:
                print("❌ فشل في اختبار النظام")
                return False
        else:
            print("❌ معالج الأحداث غير متاح")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        return False

if __name__ == "__main__":
    success = restart_instant_notifications()
    if success:
        print("\n🎉 تم إعادة تشغيل النظام بنجاح!")
        print("💡 النظام الآن يستخدم booking_number من قاعدة البيانات")
    else:
        print("\n❌ فشل في إعادة تشغيل النظام")
        print("💡 يُنصح بإعادة تشغيل التطبيق الرئيسي يدوياً")
