#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نظام الموافقات والصلاحيات
Transfer Approvals Management System
"""

from flask import render_template, request as flask_request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from . import transfers_bp
from database_manager import DatabaseManager
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

@transfers_bp.route('/approvals')
@login_required
def approvals():
    """صفحة الموافقات - عرض الطلبات المعلقة للموافقة"""
    return render_template('transfers/approvals.html')

@transfers_bp.route('/pending-approvals')
@login_required
def pending_approvals():
    """صفحة الطلبات المعلقة للموافقة"""
    return render_template('transfers/pending_approvals.html')

# ===== APIs نظام الموافقات =====

@transfers_bp.route('/api/pending-requests', methods=['GET'])
@login_required
def api_get_pending_requests():
    """API للحصول على الطلبات المعلقة للموافقة"""
    try:
        db = DatabaseManager()

        # جلب الطلبات المعلقة مع تفاصيل كاملة
        query = """
        SELECT
            tr.id, tr.request_number, tr.amount, tr.currency, tr.purpose,
            tr.status, tr.created_at, tr.updated_at,
            b.beneficiary_name, b.bank_account, b.bank_name, b.bank_country,
            COALESCE(br.BRN_LNAME, 'فرع غير محدد') as branch_name,
            COALESCE(mcb.NAME, 'غير محدد') as money_changer_bank_name,
            COALESCE(mcb.TYPE, 'غير محدد') as transfer_type,
            'مستخدم النظام' as created_by_name,
            tr.priority_level,
            tr.risk_level
        FROM TRANSFER_REQUESTS tr
        LEFT JOIN BENEFICIARIES b ON tr.beneficiary_id = b.id
        LEFT JOIN BRANCHES br ON tr.branch_id = br.BRN_NO
        LEFT JOIN MONEY_CHANGERS_BANKS mcb ON tr.money_changer_bank_id = mcb.ID
        WHERE tr.status = 'pending'
        ORDER BY
            CASE
                WHEN tr.priority_level = 'urgent' THEN 1
                WHEN tr.priority_level = 'high' THEN 2
                WHEN tr.priority_level = 'normal' THEN 3
                ELSE 4
            END,
            tr.created_at ASC
        """

        result = db.execute_query(query)

        requests = []
        if result:
            for row in result:
                requests.append({
                    'id': int(row[0]) if row[0] else 0,
                    'request_number': str(row[1]) if row[1] else '',
                    'amount': float(row[2]) if row[2] else 0,
                    'currency': str(row[3]) if row[3] else '',
                    'purpose': str(row[4]) if row[4] else '',
                    'status': str(row[5]) if row[5] else '',
                    'created_at': row[6].strftime('%Y-%m-%d %H:%M:%S') if row[6] else None,
                    'updated_at': row[7].strftime('%Y-%m-%d %H:%M:%S') if row[7] else None,
                    'beneficiary_name': str(row[8]) if row[8] else '',
                    'bank_account': str(row[9]) if row[9] else '',
                    'bank_name': str(row[10]) if row[10] else '',
                    'bank_country': str(row[11]) if row[11] else '',
                    'branch_name': str(row[12]) if row[12] else '',
                    'money_changer_bank_name': str(row[13]) if row[13] else '',
                    'transfer_type': str(row[14]) if row[14] else '',
                    'created_by_name': str(row[15]) if row[15] else '',
                    'priority_level': str(row[16]) if row[16] else 'normal',
                    'risk_level': str(row[17]) if row[17] else 'low',
                    'days_pending': (datetime.now() - row[6]).days if row[6] else 0
                })

        return jsonify({
            'success': True,
            'data': requests,
            'total_count': len(requests)
        })

    except Exception as e:
        logger.error(f"خطأ في جلب الطلبات المعلقة: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب الطلبات المعلقة: {str(e)}'
        }), 500

@transfers_bp.route('/api/approve-request/<int:request_id>', methods=['POST'])
@login_required
def api_approve_request(request_id):
    """API لموافقة طلب حوالة"""
    try:
        db = DatabaseManager()

        # جلب البيانات من الطلب
        if flask_request.is_json:
            data = flask_request.get_json() or {}
        else:
            data = flask_request.form.to_dict()

        # التحقق من وجود الطلب وحالته
        check_query = "SELECT status, request_number FROM TRANSFER_REQUESTS WHERE id = :1"
        check_result = db.execute_query(check_query, [request_id])

        if not check_result:
            return jsonify({
                'success': False,
                'message': 'الطلب غير موجود'
            }), 404

        current_status = check_result[0][0]
        request_number = check_result[0][1]

        if current_status != 'pending':
            return jsonify({
                'success': False,
                'message': f'لا يمكن الموافقة على طلب في حالة: {current_status}'
            }), 400

        user_id = int(current_user.id) if current_user.id else 1
        approval_comment = data.get('comment', '')

        # تحديث حالة الطلب إلى معتمد (مع الأعمدة الموجودة فقط)
        update_query = """
        UPDATE TRANSFER_REQUESTS SET
            status = 'approved',
            approved_by = :1,
            approved_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = :2
        """

        db.execute_update(update_query, [
            user_id, request_id
        ])

        # تسجيل في سجل الموافقات (معطل مؤقتاً حتى إنشاء الجدول)
        try:
            # TODO: إنشاء جدول TRANSFER_APPROVAL_LOG
            logger.info(f"تم اعتماد الطلب {request_id} بواسطة المستخدم {user_id} - التعليق: {approval_comment}")
        except Exception as log_error:
            logger.warning(f"فشل في تسجيل سجل الموافقة: {log_error}")
            # لا نوقف العملية إذا فشل تسجيل السجل

        logger.info(f"تم اعتماد الطلب {request_number} بواسطة المستخدم {user_id}")

        return jsonify({
            'success': True,
            'message': f'تم اعتماد الطلب {request_number} بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في اعتماد الطلب {request_id}: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في اعتماد الطلب: {str(e)}'
        }), 500

@transfers_bp.route('/api/reject-request/<int:request_id>', methods=['POST'])
@login_required
def api_reject_request(request_id):
    """API لرفض طلب حوالة"""
    try:
        db = DatabaseManager()

        # جلب البيانات من الطلب
        if flask_request.is_json:
            data = flask_request.get_json() or {}
        else:
            data = flask_request.form.to_dict()

        # التحقق من وجود الطلب وحالته
        check_query = "SELECT status, request_number FROM TRANSFER_REQUESTS WHERE id = :1"
        check_result = db.execute_query(check_query, [request_id])

        if not check_result:
            return jsonify({
                'success': False,
                'message': 'الطلب غير موجود'
            }), 404

        current_status = check_result[0][0]
        request_number = check_result[0][1]

        if current_status != 'pending':
            return jsonify({
                'success': False,
                'message': f'لا يمكن رفض طلب في حالة: {current_status}'
            }), 400

        # التحقق من وجود سبب الرفض
        rejection_reason = data.get('reason', '').strip()
        if not rejection_reason:
            return jsonify({
                'success': False,
                'message': 'سبب الرفض مطلوب'
            }), 400

        user_id = int(current_user.id) if current_user.id else 1

        # تحديث حالة الطلب إلى مرفوض (مع الأعمدة الموجودة فقط)
        update_query = """
        UPDATE TRANSFER_REQUESTS SET
            status = 'rejected',
            rejected_by = :1,
            rejected_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = :2
        """

        db.execute_update(update_query, [
            user_id, request_id
        ])

        # تسجيل في سجل الموافقات (معطل مؤقتاً حتى إنشاء الجدول)
        try:
            # TODO: إنشاء جدول TRANSFER_APPROVAL_LOG
            logger.info(f"تم رفض الطلب {request_id} بواسطة المستخدم {user_id} - السبب: {rejection_reason}")
        except Exception as log_error:
            logger.warning(f"فشل في تسجيل سجل الرفض: {log_error}")
            # لا نوقف العملية إذا فشل تسجيل السجل

        logger.info(f"تم رفض الطلب {request_number} بواسطة المستخدم {user_id}")

        return jsonify({
            'success': True,
            'message': f'تم رفض الطلب {request_number}'
        })

    except Exception as e:
        logger.error(f"خطأ في رفض الطلب {request_id}: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في رفض الطلب: {str(e)}'
        }), 500

@transfers_bp.route('/api/approval-history/<int:request_id>', methods=['GET'])
@login_required
def api_get_approval_history(request_id):
    """API للحصول على تاريخ الموافقات لطلب محدد"""
    try:
        db = DatabaseManager()

        query = """
        SELECT
            al.action_type, al.action_at, al.comment_text, al.status_from, al.status_to,
            'مستخدم النظام' as action_by_name
        FROM TRANSFER_APPROVAL_LOG al
        WHERE al.request_id = :1
        ORDER BY al.action_at DESC
        """

        result = db.execute_query(query, [request_id])

        history = []
        if result:
            for row in result:
                action_type_ar = {
                    'approve': 'موافقة',
                    'reject': 'رفض',
                    'review': 'مراجعة',
                    'comment': 'تعليق'
                }.get(row[0], row[0])

                history.append({
                    'action_type': row[0],
                    'action_type_ar': action_type_ar,
                    'action_at': row[1].strftime('%Y-%m-%d %H:%M:%S') if row[1] else None,
                    'comment': str(row[2]) if row[2] else '',
                    'status_from': row[3],
                    'status_to': row[4],
                    'action_by_name': str(row[5]) if row[5] else 'مستخدم غير معروف'
                })

        return jsonify({
            'success': True,
            'data': history
        })

    except Exception as e:
        logger.error(f"خطأ في جلب تاريخ الموافقات: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب تاريخ الموافقات: {str(e)}'
        }), 500

@transfers_bp.route('/api/bulk-approve', methods=['POST'])
@login_required
def api_bulk_approve():
    """API للموافقة على عدة طلبات دفعة واحدة"""
    try:
        db = DatabaseManager()

        # جلب البيانات من الطلب
        if flask_request.is_json:
            data = flask_request.get_json() or {}
        else:
            data = flask_request.form.to_dict()

        request_ids = data.get('request_ids', [])
        comment = data.get('comment', '')

        if not request_ids:
            return jsonify({
                'success': False,
                'message': 'لم يتم تحديد أي طلبات'
            }), 400

        user_id = int(current_user.id) if current_user.id else 1
        approved_count = 0
        failed_requests = []

        for request_id in request_ids:
            try:
                # التحقق من حالة الطلب
                check_query = "SELECT status, request_number FROM TRANSFER_REQUESTS WHERE id = :1"
                check_result = db.execute_query(check_query, [request_id])

                if not check_result or check_result[0][0] != 'pending':
                    failed_requests.append({
                        'id': request_id,
                        'reason': 'الطلب غير موجود أو ليس في حالة انتظار'
                    })
                    continue

                # الموافقة على الطلب
                update_query = """
                UPDATE TRANSFER_REQUESTS SET
                    status = 'approved',
                    approved_by = :1,
                    approved_at = CURRENT_TIMESTAMP,
                    approval_comment = :2,
                    updated_by = :3,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = :4
                """

                db.execute_update(update_query, [
                    user_id, comment, user_id, request_id
                ])

                # تسجيل في سجل الموافقات
                log_query = """
                INSERT INTO TRANSFER_APPROVAL_LOG (
                    id, request_id, action_type, action_by, action_at, comment_text, status_from, status_to
                ) VALUES (APPROVAL_LOG_SEQ.NEXTVAL, :1, 'approve', :2, SYSDATE, :3, 'pending', 'approved')
                """

                db.execute_update(log_query, [
                    request_id, user_id, f"موافقة جماعية: {comment}"
                ])

                approved_count += 1

            except Exception as e:
                failed_requests.append({
                    'id': request_id,
                    'reason': str(e)
                })

        logger.info(f"تم اعتماد {approved_count} طلب بواسطة المستخدم {user_id}")

        return jsonify({
            'success': True,
            'message': f'تم اعتماد {approved_count} طلب بنجاح',
            'approved_count': approved_count,
            'failed_requests': failed_requests
        })

    except Exception as e:
        logger.error(f"خطأ في الموافقة الجماعية: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في الموافقة الجماعية: {str(e)}'
        }), 500
