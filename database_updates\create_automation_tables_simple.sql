-- إن<PERSON>ا<PERSON> جدول سجل تنفيذ الأتمتة
CREATE TABLE automation_execution_log (
    id NUMBER PRIMARY KEY,
    rule_id NUMBER NOT NULL,
    shipment_id NUMBER,
    trigger_condition VARCHAR2(100),
    condition_value VARCHAR2(500),
    action_type VARCHAR2(100),
    execution_status VARCHAR2(50) DEFAULT 'PENDING',
    execution_result CLOB,
    error_message CLOB,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    execution_duration NUMBER,
    created_by NUMBER DEFAULT 1
);

-- إنشاء sequence
CREATE SEQUENCE automation_execution_log_seq START WITH 1 INCREMENT BY 1;

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول مراقبة تغييرات الشحنات
CREATE TABLE shipment_status_changes (
    id NUMBER PRIMARY KEY,
    shipment_id NUMBER NOT NULL,
    old_status VARCHAR2(50),
    new_status VARCHAR2(50) NOT NULL,
    changed_by NUMBER,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    change_reason VARCHAR2(500),
    automation_processed NUMBER(1) DEFAULT 0
);

-- إنشاء sequence
CREATE SEQUENCE shipment_status_changes_seq START WITH 1 INCREMENT BY 1;

-- إنشاء فهارس
CREATE INDEX idx_auto_exec_rule ON automation_execution_log(rule_id);
CREATE INDEX idx_auto_exec_shipment ON automation_execution_log(shipment_id);
CREATE INDEX idx_status_change_shipment ON shipment_status_changes(shipment_id);
CREATE INDEX idx_status_change_processed ON shipment_status_changes(automation_processed);

COMMIT;
