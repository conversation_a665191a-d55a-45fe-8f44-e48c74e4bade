-- ========================================================================
-- إضافة الأعمدة الجديدة إلى جدول BALANCE_TRANSACTIONS
-- Add New Columns to BALANCE_TRANSACTIONS Table
-- ========================================================================
-- المهمة: إضافة الأعمدة الجديدة
-- Task: Add New Columns
-- UUID: 8Ve3nVKmBdnkNvD9WWDbxj
-- ========================================================================

-- فحص الجدول الحالي قبل التعديل
SELECT 'Current BALANCE_TRANSACTIONS structure:' as info FROM DUAL;

SELECT column_name, data_type, nullable, data_default
FROM user_tab_columns 
WHERE table_name = 'BALANCE_TRANSACTIONS'
ORDER BY column_id;

-- ========================================================================
-- إضافة الأعمدة الجديدة بالتسميات المختصرة
-- ========================================================================

PROMPT 'Adding new columns to BALANCE_TRANSACTIONS...'

-- إضافة الأعمدة الجديدة
ALTER TABLE BALANCE_TRANSACTIONS ADD (
    -- رصيد موحد (موجب للمدين، سالب للدائن)
    BAL NUMBER(15,2) DEFAULT 0 NOT NULL,
    
    -- رصيد بالعملة الأساسية مع سعر الصرف
    BAL_F NUMBER(15,2) DEFAULT 0 NOT NULL,
    
    -- رقم الشهر (1-12)
    MONTH_NO NUMBER(2) NOT NULL,
    
    -- رقم السنة
    YEAR_NO NUMBER(4) NOT NULL,
    
    -- رقم الفرع
    BRANCH_ID NUMBER DEFAULT 1 NOT NULL
);

PROMPT 'New columns added successfully!'

-- ========================================================================
-- تحديث البيانات الموجودة لملء الأعمدة الجديدة
-- ========================================================================

PROMPT 'Updating existing data with new column values...'

-- تحديث الأعمدة الجديدة للبيانات الموجودة
UPDATE BALANCE_TRANSACTIONS SET
    -- حساب الرصيد الموحد
    BAL = COALESCE(debit_amount, 0) - COALESCE(credit_amount, 0),
    
    -- حساب الرصيد بالعملة الأساسية (افتراض سعر صرف = 1)
    BAL_F = (COALESCE(debit_amount, 0) - COALESCE(credit_amount, 0)) * COALESCE(exchange_rate, 1),
    
    -- استخراج الشهر من تاريخ المستند
    MONTH_NO = EXTRACT(MONTH FROM document_date),
    
    -- استخراج السنة من تاريخ المستند
    YEAR_NO = EXTRACT(YEAR FROM document_date),
    
    -- تعيين فرع افتراضي
    BRANCH_ID = 1
WHERE BAL IS NULL OR MONTH_NO IS NULL OR YEAR_NO IS NULL;

COMMIT;

PROMPT 'Existing data updated successfully!'

-- ========================================================================
-- إضافة تعليقات على الأعمدة الجديدة
-- ========================================================================

COMMENT ON COLUMN BALANCE_TRANSACTIONS.BAL IS 'Unified balance: positive for debit, negative for credit';
COMMENT ON COLUMN BALANCE_TRANSACTIONS.BAL_F IS 'Balance in base currency with exchange rate';
COMMENT ON COLUMN BALANCE_TRANSACTIONS.MONTH_NO IS 'Month number (1-12) extracted from document_date';
COMMENT ON COLUMN BALANCE_TRANSACTIONS.YEAR_NO IS 'Year number extracted from document_date';
COMMENT ON COLUMN BALANCE_TRANSACTIONS.BRANCH_ID IS 'Branch identifier for multi-branch support';

-- ========================================================================
-- فحص النتيجة النهائية
-- ========================================================================

PROMPT 'Verifying the new structure...'

-- عرض البنية الجديدة
SELECT column_name, data_type, nullable, data_default
FROM user_tab_columns 
WHERE table_name = 'BALANCE_TRANSACTIONS'
AND column_name IN ('BAL', 'BAL_F', 'MONTH_NO', 'YEAR_NO', 'BRANCH_ID')
ORDER BY column_name;

-- عرض عينة من البيانات المحدثة
SELECT 
    id,
    document_number,
    document_date,
    debit_amount,
    credit_amount,
    BAL,
    BAL_F,
    MONTH_NO,
    YEAR_NO,
    BRANCH_ID
FROM BALANCE_TRANSACTIONS
WHERE ROWNUM <= 5
ORDER BY created_date DESC;

-- إحصائيات سريعة
SELECT 
    'Total records' as metric,
    COUNT(*) as value
FROM BALANCE_TRANSACTIONS
UNION ALL
SELECT 
    'Records with BAL calculated',
    COUNT(*)
FROM BALANCE_TRANSACTIONS
WHERE BAL IS NOT NULL
UNION ALL
SELECT 
    'Records with MONTH_NO/YEAR_NO',
    COUNT(*)
FROM BALANCE_TRANSACTIONS
WHERE MONTH_NO IS NOT NULL AND YEAR_NO IS NOT NULL;

PROMPT 'New columns added and populated successfully!'
PROMPT 'Task 8Ve3nVKmBdnkNvD9WWDbxj completed!'

-- ========================================================================
-- ملاحظات مهمة
-- ========================================================================
/*
الأعمدة المضافة:
1. BAL: رصيد موحد يجمع المدين والدائن بإشارة واحدة
2. BAL_F: نفس الرصيد مضروب في سعر الصرف للعملة الأساسية
3. MONTH_NO: رقم الشهر لتسهيل التقارير الشهرية
4. YEAR_NO: رقم السنة لتسهيل التقارير السنوية
5. BRANCH_ID: رقم الفرع لدعم الفروع المتعددة

المزايا:
- استعلامات أسرع للأرصدة (SUM(BAL) بدلاً من حسابات معقدة)
- تقارير شهرية وسنوية سريعة
- دعم الفروع المتعددة
- دعم العملات المتعددة مع أسعار الصرف
- توافق مع معايير التسميات المختصرة
*/
