# -*- coding: utf-8 -*-
"""
API endpoints لإنشاء PDF من صفحة المعاينة
PDF API endpoints using the viewer page
"""

from flask import Blueprint, request, jsonify, send_file, render_template
import os
import sys
import tempfile
from datetime import datetime

# إضافة مسار الخدمات
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'services'))

pdf_api = Blueprint('pdf_api', __name__)

@pdf_api.route('/api/delivery-order/<int:delivery_order_id>/pdf', methods=['GET'])
def generate_delivery_order_pdf_api(delivery_order_id):
    """API لإنشاء PDF من صفحة المعاينة"""
    try:
        from simple_viewer_pdf import generate_pdf_from_viewer
        
        # إنشاء PDF
        pdf_path, message = generate_pdf_from_viewer(delivery_order_id)
        
        if pdf_path and os.path.exists(pdf_path):
            # إرجاع الملف
            return send_file(
                pdf_path,
                as_attachment=True,
                download_name=f'delivery_order_{delivery_order_id}.pdf',
                mimetype='application/pdf'
            )
        else:
            return jsonify({
                'success': False,
                'message': message or 'فشل في إنشاء PDF'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في إنشاء PDF: {str(e)}'
        }), 500

@pdf_api.route('/api/delivery-order/<int:delivery_order_id>/pdf-url', methods=['GET'])
def get_delivery_order_pdf_url(delivery_order_id):
    """API للحصول على رابط PDF"""
    try:
        from simple_viewer_pdf import generate_pdf_from_viewer
        
        # إنشاء PDF
        pdf_path, message = generate_pdf_from_viewer(delivery_order_id)
        
        if pdf_path and os.path.exists(pdf_path):
            # إنشاء URL للملف
            filename = os.path.basename(pdf_path)
            pdf_url = f"/static/pdf/{filename}"
            
            return jsonify({
                'success': True,
                'pdf_url': pdf_url,
                'filename': filename,
                'message': message
            })
        else:
            return jsonify({
                'success': False,
                'message': message or 'فشل في إنشاء PDF'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في إنشاء PDF: {str(e)}'
        }), 500

@pdf_api.route('/delivery-order/<int:delivery_order_id>/pdf-viewer', methods=['GET'])
def delivery_order_pdf_viewer_enhanced(delivery_order_id):
    """صفحة معاينة محسنة مع إمكانية إنشاء PDF تلقائي"""
    try:
        # جلب بيانات أمر التسليم
        from database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        query = """
            SELECT 
                do.id,
                do.order_number,
                do.shipment_id,
                do.customs_agent_id,
                ca.agent_name,
                ca.phone,
                ca.mobile,
                ca.email,
                do.branch_id,
                b.brn_lname as branch_name,
                b.brn_ladd as branch_address,
                do.created_date,
                do.order_status,
                cs.shipment_number,
                cs.port_of_loading,
                cs.port_of_discharge,
                cs.shipment_status,
                cc.container_number,
                cc.container_type,
                cc.seal_number,
                cc.total_weight,
                cc.net_weight
            FROM delivery_orders do
            LEFT JOIN customs_agents ca ON do.customs_agent_id = ca.id
            LEFT JOIN branches b ON do.branch_id = b.brn_no
            LEFT JOIN cargo_shipments cs ON do.shipment_id = cs.id
            LEFT JOIN cargo_containers cc ON cs.id = cc.cargo_shipment_id
            WHERE do.id = :delivery_order_id
        """
        
        result = db_manager.execute_query(query, {'delivery_order_id': delivery_order_id})
        
        if not result:
            return "أمر التسليم غير موجود", 404
        
        row = result[0]
        order_data = {
            'id': row[0],
            'order_number': row[1],
            'shipment_id': row[2],
            'customs_agent_id': row[3],
            'agent_name': row[4],
            'agent_phone': row[5],
            'agent_mobile': row[6],
            'agent_email': row[7],
            'branch_id': row[8],
            'branch_name': row[9],
            'branch_address': row[10],
            'created_date': row[11],
            'order_status': row[12],
            'shipment_reference': row[13],
            'loading_port': row[14],
            'discharge_port': row[15],
            'shipment_status': row[16],
            'container_number': row[17],
            'container_type': row[18],
            'seal_number': row[19],
            'total_weight': row[20],
            'net_weight': row[21],
            # حقول إضافية
            'tracking_number': f"TRK-{row[2]}" if row[2] else 'غير محدد',
            'booking_number': f"BK-{row[2]}" if row[2] else 'غير محدد',
            'delivery_location': 'عدن، اليمن',
            'expected_completion_date': row[11],
        }
        
        db_manager.close()
        
        # فحص إذا كان المطلوب إنشاء PDF تلقائي
        auto_pdf = request.args.get('auto_pdf', 'false').lower() == 'true'
        
        # رندر الصفحة مع البيانات
        return render_template(
            'shipments/delivery_order_pdf_viewer.html',
            order=order_data,
            auto_pdf=auto_pdf
        )
        
    except Exception as e:
        return f"خطأ في تحميل أمر التسليم: {str(e)}", 500
