#!/usr/bin/env python3
"""
تحديث Client Secret الصحيح
Update Correct Client Secret
"""

import json
import os

def update_client_secret():
    """تحديث Client Secret"""
    
    print("🔧 تحديث Client Secret الصحيح")
    print("=" * 50)
    
    print("⚠️ المشكلة المكتشفة:")
    print("تم نسخ Client Secret ID بدلاً من Client Secret Value")
    print()
    print("🔍 الفرق:")
    print("❌ Client Secret ID: 8ed99a45-8be2-49c6-91c4-45d92e1559ab")
    print("✅ Client Secret Value: wR8Q~abcdefghijklmnopqrstuvwxyz1234567890")
    print()
    
    print("📋 خطوات الإصلاح في Azure Portal:")
    print("1. اذهب إلى التطبيق في Azure Portal")
    print("2. Certificates & secrets")
    print("3. Client secrets")
    print("4. احذف الـ Secret الحالي")
    print("5. انقر على + New client secret")
    print("6. Description: SASERP API Secret")
    print("7. Expires: 24 months")
    print("8. Add")
    print("9. انسخ VALUE (وليس ID) فوراً!")
    print()
    
    # قراءة الإعدادات الحالية
    config_path = os.path.join('app', 'shipments', 'cloud_config.json')
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف الإعدادات: {e}")
        return False
    
    print("📋 الإعدادات الحالية:")
    current_secret = config.get('onedrive', {}).get('client_secret', '')
    print(f"Client Secret الحالي: {current_secret}")
    print()
    
    # طلب Client Secret الجديد
    print("🔑 يرجى إدخال Client Secret الجديد:")
    print("(تأكد من نسخ VALUE وليس ID)")
    
    new_secret = input("Client Secret Value: ").strip()
    
    if not new_secret:
        print("❌ لم يتم إدخال Client Secret")
        return False
    
    if len(new_secret) < 20:
        print("⚠️ Client Secret قصير جداً - تأكد من نسخ VALUE الكامل")
        confirm = input("هل تريد المتابعة؟ (y/n): ").strip().lower()
        if confirm not in ['y', 'yes', 'نعم']:
            return False
    
    # تحديث الإعدادات
    config['onedrive']['client_secret'] = new_secret
    
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        
        print("✅ تم تحديث Client Secret بنجاح!")
        
        # إنشاء نسخة احتياطية
        backup_path = config_path + '.backup'
        with open(backup_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        
        print(f"💾 تم إنشاء نسخة احتياطية: {backup_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في حفظ الإعدادات: {e}")
        return False

def test_new_secret():
    """اختبار Client Secret الجديد"""
    
    print(f"\n🧪 اختبار Client Secret الجديد...")
    
    try:
        import sys
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # تشغيل اختبار OneDrive
        os.system("python test_onedrive_connection.py")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    print("🚨 تم اكتشاف مشكلة في Client Secret!")
    print()
    
    success = update_client_secret()
    
    if success:
        print(f"\n🎉 تم التحديث بنجاح!")
        
        test_choice = input("\nهل تريد اختبار الإعدادات الجديدة؟ (y/n): ").strip().lower()
        if test_choice in ['y', 'yes', 'نعم']:
            test_new_secret()
    else:
        print(f"\n❌ فشل في التحديث")
        
        print(f"\n💡 يمكنك:")
        print("1. إعادة المحاولة مع Client Secret صحيح")
        print("2. تفعيل وضع التجريب مؤقتاً")
        print("3. استخدام Nextcloud بدلاً من OneDrive")
