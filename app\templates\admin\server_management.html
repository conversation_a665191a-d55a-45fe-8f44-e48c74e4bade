{% extends "base.html" %}

{% block title %}إدارة الخادم والنشر{% endblock %}

{% block head %}
<!-- Toastr CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-server"></i>
                        إدارة الخادم والنشر
                    </h3>
                </div>
                <div class="card-body">

                    <!-- قائمة الخوادم المحفوظة -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>الخوادم المحفوظة</h5>
                            <div class="row">
                                <div class="col-md-8">
                                    <div id="servers-list" class="mb-3">
                                        <!-- قائمة الخوادم المحفوظة -->
                                        <div class="row">
                                            {% for server in servers %}
                                            <div class="col-md-6 mb-3">
                                                <div class="card border-left-primary">
                                                    <div class="card-body">
                                                        <div class="row no-gutters align-items-center">
                                                            <div class="col mr-2">
                                                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                                    {{ server.name or 'خادم بدون اسم' }}
                                                                </div>
                                                                <div class="h6 mb-0 font-weight-bold text-gray-800">
                                                                    {{ server.domain or 'لا يوجد نطاق' }}
                                                                </div>
                                                                <div class="text-xs text-gray-600">
                                                                    المنفذ: {{ server.port or 5000 }} | البيئة: {{ server.environment or 'development' }}
                                                                </div>
                                                                <div class="text-xs text-gray-500">
                                                                    تم الإنشاء: {{ server.created_at[:10] if server.created_at else 'غير محدد' }}
                                                                </div>
                                                                <div class="mt-2">
                                                                    {% if server.ssl_enabled %}
                                                                    <span class="badge badge-success">SSL مُفعّل</span>
                                                                    {% else %}
                                                                    <span class="badge badge-secondary">SSL معطل</span>
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                            <div class="col-auto">
                                                                <div class="btn-group-vertical">
                                                                    <button class="btn btn-sm btn-primary mb-1" onclick="loadServerConfig('{{ server.id }}')" title="تحميل الإعدادات">
                                                                        <i class="fas fa-download"></i>
                                                                    </button>
                                                                    <button class="btn btn-sm btn-warning mb-1" onclick="editServer('{{ server.id }}')" title="تعديل">
                                                                        <i class="fas fa-edit"></i>
                                                                    </button>
                                                                    <button class="btn btn-sm btn-danger" onclick="deleteServer('{{ server.id }}')" title="حذف">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {% else %}
                                            <div class="col-12">
                                                <div class="alert alert-info">
                                                    <i class="fas fa-info-circle"></i> لا توجد خوادم محفوظة
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <button class="btn btn-success btn-sm mb-2 d-block" onclick="showAddServerModal()">
                                        <i class="fas fa-plus"></i> إضافة خادم جديد
                                    </button>
                                    <button class="btn btn-info btn-sm mb-2 d-block" onclick="loadServersList()">
                                        <i class="fas fa-sync"></i> تحديث القائمة
                                    </button>
                                    <button class="btn btn-warning btn-sm mb-2 d-block" onclick="console.log('اختبار JavaScript'); alert('JavaScript يعمل!');">
                                        <i class="fas fa-bug"></i> اختبار JS
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- حالة الخادم -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>حالة الخادم</h5>
                            <div class="row" id="server-status">
                                <div class="col-md-2">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-info">
                                            <i class="fas fa-play"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">الخادم</span>
                                            <span class="info-box-number" id="server-running">جاري التحقق...</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-success">
                                            <i class="fas fa-globe"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">النطاق</span>
                                            <span class="info-box-number" id="domain-status">جاري التحقق...</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-warning">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">SSL</span>
                                            <span class="info-box-number" id="ssl-status">جاري التحقق...</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-primary">
                                            <i class="fas fa-cogs"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Nginx</span>
                                            <span class="info-box-number" id="nginx-status">جاري التحقق...</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-secondary">
                                            <i class="fas fa-tasks"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">PM2</span>
                                            <span class="info-box-number" id="pm2-status">جاري التحقق...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات النطاق -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>إعدادات النطاق</h5>
                                </div>
                                <div class="card-body">
                                    <form id="domain-form">
                                        <div class="form-group">
                                            <label>اسم النطاق</label>
                                            <input type="text" class="form-control" id="domain" 
                                                   value="{{ config.domain }}" 
                                                   placeholder="example.com">
                                            <small class="form-text text-muted">
                                                أدخل اسم النطاق بدون http:// أو https://
                                            </small>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label>منفذ الخادم</label>
                                            <input type="number" class="form-control" id="port" 
                                                   value="{{ config.port }}" min="1" max="65535">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label>عنوان الاستماع</label>
                                            <select class="form-control" id="host">
                                                <option value="127.0.0.1" {% if config.host == '127.0.0.1' %}selected{% endif %}>
                                                    127.0.0.1 (محلي فقط)
                                                </option>
                                                <option value="0.0.0.0" {% if config.host == '0.0.0.0' %}selected{% endif %}>
                                                    0.0.0.0 (جميع العناوين)
                                                </option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label>بيئة التشغيل</label>
                                            <select class="form-control" id="environment">
                                                <option value="development" {% if config.environment == 'development' %}selected{% endif %}>
                                                    التطوير (Development)
                                                </option>
                                                <option value="production" {% if config.environment == 'production' %}selected{% endif %}>
                                                    الإنتاج (Production)
                                                </option>
                                            </select>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>إعدادات SSL</h5>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" 
                                                   id="ssl-enabled" {% if config.ssl_enabled %}checked{% endif %}>
                                            <label class="custom-control-label" for="ssl-enabled">
                                                تفعيل SSL/HTTPS
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" 
                                                   id="auto-ssl" {% if config.auto_ssl %}checked{% endif %}>
                                            <label class="custom-control-label" for="auto-ssl">
                                                SSL تلقائي (Let's Encrypt)
                                            </label>
                                        </div>
                                        <small class="form-text text-muted">
                                            سيتم الحصول على شهادة SSL مجانية تلقائياً
                                        </small>
                                    </div>
                                    
                                    <div id="manual-ssl" style="display: none;">
                                        <div class="form-group">
                                            <label>مسار شهادة SSL</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="ssl-cert-path"
                                                       value="{{ config.ssl_cert_path }}"
                                                       placeholder="/path/to/certificate.crt">
                                                <div class="input-group-append">
                                                    <button class="btn btn-outline-secondary" type="button" onclick="browseCertFile()">
                                                        <i class="fas fa-folder-open"></i> استعراض
                                                    </button>
                                                </div>
                                            </div>
                                            <small class="form-text text-muted">
                                                اختر ملف الشهادة (.crt, .pem, .cer)
                                            </small>
                                        </div>

                                        <div class="form-group">
                                            <label>مسار مفتاح SSL</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="ssl-key-path"
                                                       value="{{ config.ssl_key_path }}"
                                                       placeholder="/path/to/private.key">
                                                <div class="input-group-append">
                                                    <button class="btn btn-outline-secondary" type="button" onclick="browseKeyFile()">
                                                        <i class="fas fa-folder-open"></i> استعراض
                                                    </button>
                                                </div>
                                            </div>
                                            <small class="form-text text-muted">
                                                اختر ملف المفتاح الخاص (.key, .pem)
                                            </small>
                                        </div>

                                        <!-- ملفات الاستعراض المخفية -->
                                        <input type="file" id="cert-file-input" accept=".crt,.pem,.cer" style="display: none;">
                                        <input type="file" id="key-file-input" accept=".key,.pem" style="display: none;">

                                        <!-- أزرار إدارة ملفات SSL -->
                                        <div class="form-group mt-3">
                                            <div class="btn-group mb-2" role="group">
                                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="uploadSSLFiles()">
                                                    <i class="fas fa-upload"></i> رفع ملفات SSL
                                                </button>
                                                <button type="button" class="btn btn-outline-info btn-sm" onclick="validateSSLFiles()">
                                                    <i class="fas fa-check-circle"></i> التحقق من الملفات
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearSSLFiles()">
                                                    <i class="fas fa-times"></i> مسح الاختيار
                                                </button>
                                            </div>
                                            <br>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-outline-success btn-sm" onclick="generateDomainSSL()">
                                                    <i class="fas fa-shield-alt"></i> إنشاء شهادة للنطاق
                                                </button>
                                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="generateTestSSL()">
                                                    <i class="fas fa-certificate"></i> إنشاء شهادة تجريبية
                                                </button>
                                            </div>
                                            <small class="form-text text-muted">
                                                رفع الملفات للخادم، فحص صحتها، أو إنشاء شهادة تجريبية للتطوير
                                            </small>
                                        </div>
                                    </div>
                                    
                                    <div id="ssl-info" class="mt-3"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الخدمات -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>إعدادات الخدمات</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" 
                                                       id="nginx-enabled" {% if config.nginx_enabled %}checked{% endif %}>
                                                <label class="custom-control-label" for="nginx-enabled">
                                                    تفعيل Nginx
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">
                                                خادم ويب عكسي للأداء والأمان
                                            </small>
                                        </div>
                                        
                                        <div class="col-md-3">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" 
                                                       id="pm2-enabled" {% if config.pm2_enabled %}checked{% endif %}>
                                                <label class="custom-control-label" for="pm2-enabled">
                                                    تفعيل PM2
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">
                                                مدير العمليات للتطبيقات
                                            </small>
                                        </div>
                                        
                                        <div class="col-md-3">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" 
                                                       id="backup-enabled" {% if config.backup_enabled %}checked{% endif %}>
                                                <label class="custom-control-label" for="backup-enabled">
                                                    النسخ الاحتياطي
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">
                                                نسخ احتياطية تلقائية
                                            </small>
                                        </div>
                                        
                                        <div class="col-md-3">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" 
                                                       id="monitoring-enabled" {% if config.monitoring_enabled %}checked{% endif %}>
                                                <label class="custom-control-label" for="monitoring-enabled">
                                                    المراقبة
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">
                                                مراقبة الأداء والأخطاء
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>إجراءات النشر</h5>
                                </div>
                                <div class="card-body">
                                    <div class="btn-group mr-2" role="group">
                                        <button type="button" class="btn btn-primary" onclick="saveConfig()">
                                            <i class="fas fa-save"></i> حفظ الإعدادات
                                        </button>
                                        <button type="button" class="btn btn-info" onclick="checkStatus()">
                                            <i class="fas fa-sync"></i> تحديث الحالة
                                        </button>
                                    </div>
                                    
                                    <div class="btn-group mr-2" role="group">
                                        <button type="button" class="btn btn-success" onclick="generateConfigs()">
                                            <i class="fas fa-cog"></i> إنشاء الإعدادات
                                        </button>
                                        <button type="button" class="btn btn-warning" onclick="deployApp()">
                                            <i class="fas fa-rocket"></i> نشر التطبيق
                                        </button>
                                    </div>
                                    
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-secondary" onclick="downloadConfigs()">
                                            <i class="fas fa-download"></i> تحميل الإعدادات
                                        </button>
                                        <button type="button" class="btn btn-info" onclick="showInstructions()">
                                            <i class="fas fa-question-circle"></i> تعليمات النشر
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" onclick="restartServer()">
                                            <i class="fas fa-redo"></i> إعادة تشغيل
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة خادم جديد -->
<div class="modal fade" id="addServerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة خادم جديد</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="addServerForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>اسم الخادم *</label>
                                <input type="text" class="form-control" id="new-server-name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>النطاق *</label>
                                <input type="text" class="form-control" id="new-server-domain" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>المنفذ</label>
                                <input type="number" class="form-control" id="new-server-port" value="5000">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>البيئة</label>
                                <select class="form-control" id="new-server-environment">
                                    <option value="development">تطوير</option>
                                    <option value="production">إنتاج</option>
                                    <option value="staging">اختبار</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label>وصف الخادم</label>
                                <textarea class="form-control" id="new-server-description" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="addNewServer()">إضافة الخادم</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal للإعدادات المُولدة -->
<div class="modal fade" id="configModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ملفات الإعدادات المُولدة</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="config-content"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Toastr JS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

<!-- Server Management JS -->
<script src="{{ url_for('static', filename='js/server-management.js') }}"></script>

<!-- تحذير SSL وحلول -->
<div id="ssl-warning" class="alert alert-info border-left-primary" style="display: none;">
    <div class="row align-items-center">
        <div class="col-auto">
            <i class="fas fa-shield-alt fa-2x text-primary"></i>
        </div>
        <div class="col">
            <h5 class="mb-1"><i class="fas fa-info-circle"></i> رسالة "اتصالك لا يتمتع بالخصوصية"</h5>
            <p class="mb-2">إذا ظهرت لك رسالة تحذير من المتصفح، فهذا طبيعي لأن الشهادة موقعة محلياً.</p>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-6">
            <div class="card border-left-success">
                <div class="card-body py-2">
                    <h6 class="text-success"><i class="fas fa-rocket"></i> الحل السريع (فوري):</h6>
                    <ol class="mb-0 small">
                        <li>اضغط <strong>"متقدم"</strong> أو <strong>"Advanced"</strong></li>
                        <li>اضغط <strong>"الانتقال إلى sas.alfogehi.net (غير آمن)"</strong></li>
                        <li><strong>أو اكتب "thisisunsafe"</strong> على صفحة التحذير مباشرة</li>
                    </ol>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card border-left-warning">
                <div class="card-body py-2">
                    <h6 class="text-warning"><i class="fas fa-certificate"></i> الحل الدائم:</h6>
                    <ol class="mb-0 small">
                        <li>شغل <code>install_ca.bat</code> كمدير</li>
                        <li>أعد تشغيل المتصفح</li>
                        <li>الموقع سيصبح آمناً نهائياً 🔒</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-3">
        <div class="alert alert-success py-2 mb-2">
            <small><i class="fas fa-check-circle"></i> <strong>الموقع آمن تماماً!</strong> التشفير فعال والبيانات محمية. المتصفح فقط يحتاج "إذن" للثقة في الشهادة المحلية.</small>
        </div>
        <button class="btn btn-sm btn-success" onclick="hideSSLWarning()">
            <i class="fas fa-check"></i> فهمت، أخفي هذا التحذير
        </button>
        <button class="btn btn-sm btn-info ml-2" onclick="showSSLDetails()">
            <i class="fas fa-info"></i> تفاصيل أكثر
        </button>
    </div>
</div>

<!-- تفاصيل SSL إضافية -->
<div id="ssl-details" class="alert alert-light" style="display: none;">
    <h6><i class="fas fa-certificate"></i> تفاصيل الشهادة:</h6>
    <div class="row">
        <div class="col-md-6">
            <ul class="list-unstyled small">
                <li><strong>النطاق:</strong> sas.alfogehi.net</li>
                <li><strong>التشفير:</strong> RSA 2048 بت</li>
                <li><strong>الصلاحية:</strong> سنة كاملة</li>
            </ul>
        </div>
        <div class="col-md-6">
            <ul class="list-unstyled small">
                <li><strong>النوع:</strong> موقعة محلياً</li>
                <li><strong>الأمان:</strong> تشفير كامل</li>
                <li><strong>الحالة:</strong> صالحة وآمنة ✅</li>
            </ul>
        </div>
    </div>
    <button class="btn btn-sm btn-secondary" onclick="hideSSLDetails()">
        <i class="fas fa-times"></i> إخفاء التفاصيل
    </button>
</div>

<script>
// فحص إذا كان الموقع يعمل على HTTPS مع شهادة self-signed
if (location.protocol === 'https:' && location.hostname !== 'localhost') {
    // إظهار تحذير SSL بعد تحميل الصفحة
    setTimeout(function() {
        document.getElementById('ssl-warning').style.display = 'block';
    }, 1000);
}

function hideSSLWarning() {
    document.getElementById('ssl-warning').style.display = 'none';
    localStorage.setItem('ssl-warning-hidden', 'true');
}

function showSSLDetails() {
    document.getElementById('ssl-details').style.display = 'block';
}

function hideSSLDetails() {
    document.getElementById('ssl-details').style.display = 'none';
}

// إخفاء التحذير إذا كان المستخدم أخفاه من قبل
if (localStorage.getItem('ssl-warning-hidden') === 'true') {
    document.getElementById('ssl-warning').style.display = 'none';
}

console.log('🔧 بدء اختبار JavaScript...');

// اختبار تحميل قائمة الخوادم مباشرة
setTimeout(function() {
    console.log('⏰ اختبار تحميل الخوادم بعد 2 ثانية...');

    fetch('/admin/api/servers')
        .then(response => {
            console.log('📡 استجابة API:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📋 بيانات API:', data);

            if (data.success && data.servers.length > 0) {
                const serversList = document.getElementById('servers-list');
                if (serversList) {
                    let html = '<div class="alert alert-success">تم العثور على ' + data.servers.length + ' خادم!</div>';
                    html += '<div class="row">';

                    data.servers.forEach(function(server) {
                        html += '<div class="col-md-6 mb-2">';
                        html += '<div class="card">';
                        html += '<div class="card-body">';
                        html += '<h6>' + (server.name || 'خادم بدون اسم') + '</h6>';
                        html += '<p>' + (server.domain || 'لا يوجد نطاق') + '</p>';
                        html += '<small>المنفذ: ' + (server.port || 5000) + '</small>';
                        html += '</div></div></div>';
                    });

                    html += '</div>';
                    serversList.innerHTML = html;
                    console.log('✅ تم عرض الخوادم بنجاح');
                } else {
                    console.error('❌ لم يتم العثور على عنصر servers-list');
                }
            } else {
                console.log('📭 لا توجد خوادم');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في API:', error);
        });
}, 2000);
</script>
{% endblock %}