# -*- coding: utf-8 -*-
"""
وحدة طلبات الشراء المحسنة
Enhanced Purchase Requests Module
"""

from flask import Blueprint

# إنشاء Blueprint للوحدة
bp = Blueprint('purchase_requests', __name__, url_prefix='/purchase_requests')

# استيراد المسارات
from app.purchase_requests.routes import purchase_requests_bp

# تسجيل Blueprint
def init_app(app):
    """تسجيل Blueprint في التطبيق"""
    app.register_blueprint(purchase_requests_bp)
