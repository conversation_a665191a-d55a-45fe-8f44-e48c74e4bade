#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل ونهائي لنظام إدارة وثائق طلبات الحوالات
Final Comprehensive Test for Transfer Documents Management System
"""

import requests
import urllib3
import io
import time

# تجاهل تحذيرات SSL
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_complete_document_system():
    """اختبار شامل لنظام إدارة الوثائق"""
    
    session = requests.Session()
    session.verify = False
    
    base_url = "https://localhost:5000"
    
    try:
        print("🔐 تسجيل الدخول...")
        login_response = session.post(
            f"{base_url}/auth/login",
            data={'username': 'admin', 'password': 'admin'},
            timeout=10,
            allow_redirects=True
        )
        
        if login_response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {login_response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح!")
        
        request_id = 50
        
        # 1. اختبار صفحة إدارة الوثائق
        print(f"\n📁 اختبار صفحة إدارة الوثائق للطلب {request_id}...")
        
        page_response = session.get(
            f"{base_url}/transfers/requests/{request_id}/documents",
            timeout=15
        )
        
        if page_response.status_code == 200:
            print("✅ صفحة إدارة الوثائق تعمل")
            
            content = page_response.text
            checks = {
                'عنوان الصفحة': 'إدارة وثائق طلب الحوالة' in content,
                'نموذج الرفع': 'uploadForm' in content,
                'أنواع الوثائق': 'identity_document' in content,
                'JavaScript': 'reloadDocumentsSection' in content,
                'إحصائيات': 'stat-card' in content
            }
            
            for check_name, result in checks.items():
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}")
        else:
            print(f"❌ فشل في تحميل صفحة إدارة الوثائق: {page_response.status_code}")
            return False
        
        # 2. اختبار API الإحصائيات
        print(f"\n📊 اختبار API الإحصائيات...")
        
        stats_response = session.get(
            f"{base_url}/transfers/requests/{request_id}/documents/stats",
            timeout=15
        )
        
        if stats_response.status_code == 200:
            stats_data = stats_response.json()
            if stats_data.get('success'):
                stats = stats_data.get('stats', {})
                print(f"✅ API الإحصائيات يعمل")
                print(f"   📄 عدد الوثائق: {stats.get('total_documents', 0)}")
                print(f"   💾 الحجم الإجمالي: {stats.get('total_size_mb', 0)} MB")
            else:
                print(f"❌ فشل API الإحصائيات: {stats_data.get('message')}")
                return False
        else:
            print(f"❌ فشل في API الإحصائيات: {stats_response.status_code}")
            return False
        
        # 3. اختبار API قائمة الوثائق
        print(f"\n📋 اختبار API قائمة الوثائق...")
        
        list_response = session.get(
            f"{base_url}/transfers/requests/{request_id}/documents/list",
            timeout=15
        )
        
        if list_response.status_code == 200:
            list_data = list_response.json()
            if list_data.get('success'):
                documents = list_data.get('documents', [])
                print(f"✅ API قائمة الوثائق يعمل")
                print(f"   📄 عدد الوثائق في القائمة: {len(documents)}")
                
                for i, doc in enumerate(documents[:3], 1):
                    print(f"   📄 وثيقة {i}: {doc.get('document_name', doc.get('file_name', 'غير محدد'))}")
            else:
                print(f"❌ فشل API قائمة الوثائق: {list_data.get('message')}")
                return False
        else:
            print(f"❌ فشل في API قائمة الوثائق: {list_response.status_code}")
            return False
        
        # 4. اختبار رفع وثيقة جديدة
        print(f"\n📤 اختبار رفع وثيقة جديدة...")
        
        test_content = f"""
وثيقة اختبار نهائية لنظام إدارة وثائق طلبات الحوالات

معلومات الوثيقة:
- النوع: إثبات مصدر الأموال
- الغرض: اختبار النظام النهائي
- التاريخ: {time.strftime('%Y-%m-%d %H:%M:%S')}
- الطلب: {request_id}

هذا اختبار شامل للنظام المتكامل.
        """.strip()
        
        files = {
            'document_file': ('final_test_document.txt', io.BytesIO(test_content.encode('utf-8')), 'text/plain')
        }
        
        data = {
            'document_type': 'source_of_funds',
            'document_name': 'إثبات مصدر الأموال - اختبار نهائي',
            'notes': 'وثيقة اختبار شامل للنظام المتكامل'
        }
        
        upload_response = session.post(
            f"{base_url}/transfers/requests/{request_id}/documents/upload",
            files=files,
            data=data,
            timeout=30
        )
        
        if upload_response.status_code == 200:
            upload_result = upload_response.json()
            if upload_result.get('success'):
                print("✅ رفع الوثيقة يعمل بنجاح")
                print(f"   📁 مسار الملف: {upload_result.get('file_path', 'غير محدد')}")
                print(f"   💾 حجم الملف: {upload_result.get('file_size', 0)} بايت")
            else:
                print(f"❌ فشل في رفع الوثيقة: {upload_result.get('message')}")
                return False
        else:
            print(f"❌ فشل في رفع الوثيقة: {upload_response.status_code}")
            return False
        
        # 5. اختبار التحديث التلقائي
        print(f"\n🔄 اختبار التحديث التلقائي...")
        
        time.sleep(2)  # انتظار قليل
        
        # جلب الإحصائيات المحدثة
        updated_stats_response = session.get(
            f"{base_url}/transfers/requests/{request_id}/documents/stats",
            timeout=15
        )
        
        if updated_stats_response.status_code == 200:
            updated_stats_data = updated_stats_response.json()
            if updated_stats_data.get('success'):
                updated_stats = updated_stats_data.get('stats', {})
                print("✅ التحديث التلقائي يعمل")
                print(f"   📄 عدد الوثائق المحدث: {updated_stats.get('total_documents', 0)}")
                print(f"   💾 الحجم المحدث: {updated_stats.get('total_size_mb', 0)} MB")
            else:
                print(f"❌ فشل في التحديث التلقائي: {updated_stats_data.get('message')}")
                return False
        else:
            print(f"❌ فشل في التحديث التلقائي: {updated_stats_response.status_code}")
            return False
        
        # 6. اختبار أزرار إدارة الوثائق في قائمة الطلبات
        print(f"\n🔗 اختبار أزرار إدارة الوثائق في قائمة الطلبات...")
        
        list_page_response = session.get(
            f"{base_url}/transfers/list-requests",
            timeout=15
        )
        
        if list_page_response.status_code == 200:
            list_content = list_page_response.text
            
            list_checks = {
                'زر إدارة الوثائق': 'manageDocuments' in list_content,
                'أيقونة الوثائق': 'fa-file-alt' in list_content,
                'رابط الوثائق': '/documents' in list_content
            }
            
            print("✅ قائمة الطلبات تحتوي على أزرار إدارة الوثائق")
            for check_name, result in list_checks.items():
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}")
        else:
            print(f"❌ فشل في تحميل قائمة الطلبات: {list_page_response.status_code}")
            return False
        
        # 7. اختبار أزرار إدارة الوثائق في الطلبات المعلقة
        print(f"\n⏳ اختبار أزرار إدارة الوثائق في الطلبات المعلقة...")
        
        pending_page_response = session.get(
            f"{base_url}/transfers/pending-requests",
            timeout=15
        )
        
        if pending_page_response.status_code == 200:
            pending_content = pending_page_response.text
            
            pending_checks = {
                'زر إدارة الوثائق': 'manageDocuments' in pending_content,
                'أيقونة الوثائق': 'fa-file-alt' in pending_content,
                'دالة JavaScript': 'function manageDocuments' in pending_content
            }
            
            print("✅ الطلبات المعلقة تحتوي على أزرار إدارة الوثائق")
            for check_name, result in pending_checks.items():
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}")
        else:
            print(f"❌ فشل في تحميل الطلبات المعلقة: {pending_page_response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    print("🚀 اختبار شامل ونهائي لنظام إدارة وثائق طلبات الحوالات")
    print("=" * 80)
    
    if test_complete_document_system():
        print("\n🎉 جميع الاختبارات نجحت بامتياز!")
        print("=" * 80)
        print("✅ نظام إدارة وثائق طلبات الحوالات مكتمل وجاهز للاستخدام")
        print("")
        print("🎯 المميزات المتاحة:")
        print("   📁 صفحة إدارة الوثائق متكاملة")
        print("   📤 رفع وثائق متعددة الأنواع")
        print("   📥 تحميل وحذف الوثائق")
        print("   📊 إحصائيات مفصلة ومحدثة")
        print("   🔄 تحديث تلقائي بدون إعادة تحميل")
        print("   🔗 أزرار إدارة في جميع الصفحات")
        print("   🎨 واجهة جميلة ومتجاوبة")
        print("   🔒 حماية وأمان متقدم")
        print("")
        print("🌐 للاستخدام:")
        print("   1. افتح: https://localhost:5000/transfers/list-requests")
        print("   2. انقر على زر 'إدارة الوثائق' 📁 بجانب أي طلب")
        print("   3. ارفع وثائقك واستمتع بالتحديث التلقائي!")
        print("")
        print("🎊 تهانينا! النظام جاهز للإنتاج!")
    else:
        print("\n❌ فشل بعض الاختبارات")
        print("يرجى مراجعة الأخطاء أعلاه وإصلاحها")
