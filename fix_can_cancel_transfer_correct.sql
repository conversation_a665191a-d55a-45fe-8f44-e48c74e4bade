-- إصلاح دالة CAN_CANCEL_TRANSFER مع بنية الجدول الصحيحة
-- Fix CAN_CANCEL_TRANSFER function with correct table structure

-- إنشاء أو تحديث دالة التحقق من إمكانية إلغاء الحوالة
CREATE OR REPLACE FUNCTION CAN_CANCEL_TRANSFER(
    p_transfer_id IN NUMBER
) RETURN VARCHAR2 AS
    v_status VARCHAR2(50);
    v_execution_date TIMESTAMP;
    v_days_since_execution NUMBER;
    v_result VARCHAR2(4000);
    v_transfer_number VARCHAR2(50);
    v_net_amount NUMBER(15,2);
    v_execution_status VARCHAR2(20);
BEGIN
    -- الحصول على معلومات الحوالة
    BEGIN
        SELECT status, execution_date, transfer_number, net_amount_sent, execution_status
        INTO v_status, v_execution_date, v_transfer_number, v_net_amount, v_execution_status
        FROM transfers
        WHERE id = p_transfer_id;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RETURN 'ERROR: الحوالة غير موجودة';
    END;
    
    -- التحقق من الحالة (completed أو executed)
    IF v_status NOT IN ('completed', 'executed') AND v_execution_status NOT IN ('completed', 'executed') THEN
        RETURN 'ERROR: يمكن إلغاء الحوالات المكتملة فقط. الحالة الحالية: ' || NVL(v_status, 'غير محدد') || 
               ' - حالة التنفيذ: ' || NVL(v_execution_status, 'غير محدد');
    END IF;
    
    -- التحقق من تاريخ التنفيذ
    IF v_execution_date IS NULL THEN
        RETURN 'ERROR: لا يوجد تاريخ تنفيذ للحوالة';
    END IF;
    
    -- حساب الأيام منذ التنفيذ
    v_days_since_execution := SYSDATE - v_execution_date;
    
    -- تحديد إمكانية الإلغاء
    IF v_days_since_execution > 30 THEN
        v_result := 'WARNING: مرت أكثر من 30 يوماً على التنفيذ (' || ROUND(v_days_since_execution, 1) || ' يوم)';
    ELSE
        v_result := 'OK: يمكن إلغاء الحوالة رقم ' || v_transfer_number || 
                   ' بمبلغ ' || NVL(v_net_amount, 0) || ' (منذ ' || ROUND(v_days_since_execution, 1) || ' يوم)';
    END IF;
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'ERROR: خطأ في النظام - ' || SQLERRM;
END;
/

-- اختبار الدالة المحدثة
DECLARE
    v_result VARCHAR2(4000);
BEGIN
    DBMS_OUTPUT.PUT_LINE('=== اختبار دالة CAN_CANCEL_TRANSFER المحدثة ===');
    
    -- اختبار حوالة غير موجودة
    v_result := CAN_CANCEL_TRANSFER(99999);
    DBMS_OUTPUT.PUT_LINE('نتيجة الاختبار للحوالة 99999: ' || v_result);
    
    -- اختبار حوالة أخرى
    v_result := CAN_CANCEL_TRANSFER(1);
    DBMS_OUTPUT.PUT_LINE('نتيجة الاختبار للحوالة 1: ' || v_result);
    
    DBMS_OUTPUT.PUT_LINE('=== انتهى الاختبار ===');
END;
/

-- عرض رسالة نجاح
SELECT 'تم تحديث دالة CAN_CANCEL_TRANSFER بنجاح' as result FROM DUAL;
