#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تنظيف جداول الموانئ وتوحيدها
Cleanup and Consolidate Ports Tables
"""

from database_manager import DatabaseManager

def cleanup_ports_tables():
    """تنظيف وتوحيد جداول الموانئ"""
    db_manager = DatabaseManager()
    
    try:
        print("🧹 بدء عملية تنظيف جداول الموانئ...")
        
        # 1. أولاً، نجمع البيانات من الجداول التي تحتوي على بيانات
        print("\n📊 جمع البيانات من الجداول الموجودة...")
        
        # جمع البيانات من WORLD_PORTS_COMPREHENSIVE (يحتوي على بيانات)
        comprehensive_data = db_manager.execute_query("""
            SELECT port_code, port_name, port_name_arabic, country, country_arabic,
                   city, city_arabic, region, continent, major_port, latitude, longitude,
                   cargo_types, popularity_score
            FROM world_ports_comprehensive
            WHERE is_active = 1
        """)
        
        print(f"✅ تم جمع {len(comprehensive_data or [])} ميناء من WORLD_PORTS_COMPREHENSIVE")
        
        # جمع البيانات من WORLD_PORTS
        world_ports_data = db_manager.execute_query("""
            SELECT port_code, port_name, port_name_arabic, country, country_arabic,
                   city, city_arabic, region, continent, major_port, latitude, longitude,
                   cargo_types, popularity_score
            FROM world_ports
            WHERE is_active = 1
        """)
        
        print(f"✅ تم جمع {len(world_ports_data or [])} ميناء من WORLD_PORTS")
        
        # جمع البيانات من PORTS
        ports_data = db_manager.execute_query("""
            SELECT port_code, port_name, NULL as port_name_arabic, country, NULL as country_arabic,
                   city, NULL as city_arabic, NULL as region, NULL as continent, 
                   0 as major_port, latitude, longitude, NULL as cargo_types, 0 as popularity_score
            FROM ports
            WHERE is_active = 1
        """)
        
        print(f"✅ تم جمع {len(ports_data or [])} ميناء من PORTS")
        
        # 2. إنشاء جدول موحد جديد
        print("\n🏗️ إنشاء جدول الموانئ الموحد الجديد...")
        
        # حذف الجدول إذا كان موجوداً
        try:
            db_manager.execute_update("DROP TABLE unified_ports CASCADE CONSTRAINTS")
            print("🗑️ تم حذف الجدول القديم")
        except:
            print("ℹ️ لا يوجد جدول قديم للحذف")
        
        # إنشاء الجدول الموحد الجديد
        create_table_sql = """
            CREATE TABLE unified_ports (
                id NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                port_code VARCHAR2(10) UNIQUE NOT NULL,
                port_name VARCHAR2(500) NOT NULL,
                port_name_arabic VARCHAR2(500),
                country VARCHAR2(100) NOT NULL,
                country_arabic VARCHAR2(100),
                city VARCHAR2(100) NOT NULL,
                city_arabic VARCHAR2(100),
                region VARCHAR2(100),
                continent VARCHAR2(50),
                major_port NUMBER(1) DEFAULT 0,
                latitude NUMBER(10,8),
                longitude NUMBER(11,8),
                cargo_types CLOB,
                popularity_score NUMBER(5,2) DEFAULT 0,
                usage_count NUMBER(10) DEFAULT 0,
                is_active NUMBER(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                data_source VARCHAR2(100),
                notes CLOB
            )
        """
        
        db_manager.execute_update(create_table_sql)
        print("✅ تم إنشاء جدول unified_ports")
        
        # إنشاء فهارس
        indexes = [
            "CREATE INDEX idx_unified_ports_code ON unified_ports(port_code)",
            "CREATE INDEX idx_unified_ports_name ON unified_ports(port_name)",
            "CREATE INDEX idx_unified_ports_country ON unified_ports(country)",
            "CREATE INDEX idx_unified_ports_major ON unified_ports(major_port)",
            "CREATE INDEX idx_unified_ports_popularity ON unified_ports(popularity_score)",
            "CREATE INDEX idx_unified_ports_active ON unified_ports(is_active)"
        ]
        
        for index_sql in indexes:
            try:
                db_manager.execute_update(index_sql)
            except:
                pass
        
        print("✅ تم إنشاء الفهارس")
        
        # 3. دمج البيانات في الجدول الموحد
        print("\n🔄 دمج البيانات في الجدول الموحد...")
        
        all_ports = []
        
        # إضافة البيانات من جميع الجداول
        for data_source, data in [
            ('WORLD_PORTS_COMPREHENSIVE', comprehensive_data or []),
            ('WORLD_PORTS', world_ports_data or []),
            ('PORTS', ports_data or [])
        ]:
            for port in data:
                all_ports.append({
                    'port_code': port[0],
                    'port_name': port[1],
                    'port_name_arabic': port[2],
                    'country': port[3],
                    'country_arabic': port[4],
                    'city': port[5],
                    'city_arabic': port[6],
                    'region': port[7],
                    'continent': port[8],
                    'major_port': port[9] if port[9] else 0,
                    'latitude': port[10],
                    'longitude': port[11],
                    'cargo_types': port[12],
                    'popularity_score': port[13] if port[13] else 0,
                    'data_source': data_source
                })
        
        # إزالة المكررات
        unique_ports = {}
        for port in all_ports:
            code = port['port_code']
            if code not in unique_ports or port['popularity_score'] > unique_ports[code]['popularity_score']:
                unique_ports[code] = port
        
        print(f"📊 تم العثور على {len(unique_ports)} ميناء فريد")
        
        # إدراج البيانات
        insert_sql = """
            INSERT INTO unified_ports (
                port_code, port_name, port_name_arabic, country, country_arabic,
                city, city_arabic, region, continent, major_port, latitude, longitude,
                popularity_score, data_source, cargo_types
            ) VALUES (
                :port_code, :port_name, :port_name_arabic, :country, :country_arabic,
                :city, :city_arabic, :region, :continent, :major_port, :latitude, :longitude,
                :popularity_score, :data_source, :cargo_types
            )
        """
        
        inserted_count = 0
        for port in unique_ports.values():
            try:
                db_manager.execute_update(insert_sql, port)
                inserted_count += 1
                
                if inserted_count % 50 == 0:
                    print(f"✅ تم إدراج {inserted_count} ميناء...")
                    
            except Exception as e:
                print(f"⚠️ خطأ في إدراج {port['port_code']}: {e}")
        
        print(f"🎉 تم إدراج {inserted_count} ميناء في الجدول الموحد")
        
        # 4. إنشاء جدول تصنيفات الموانئ المبسط
        print("\n📋 إنشاء جدول تصنيفات الموانئ...")
        
        try:
            db_manager.execute_update("DROP TABLE unified_port_classifications CASCADE CONSTRAINTS")
        except:
            pass
        
        classifications_sql = """
            CREATE TABLE unified_port_classifications (
                id NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                port_code VARCHAR2(10) NOT NULL,
                purpose VARCHAR2(20) NOT NULL CHECK (purpose IN ('origin', 'destination', 'both')),
                usage_count NUMBER DEFAULT 1,
                user_id VARCHAR2(100),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                CONSTRAINT uk_unified_port_class UNIQUE (port_code, purpose, user_id)
            )
        """
        
        db_manager.execute_update(classifications_sql)
        print("✅ تم إنشاء جدول unified_port_classifications")
        
        # 5. تحديث الكود ليستخدم الجداول الموحدة
        print("\n📝 ملخص التنظيف:")
        print("=" * 50)
        print(f"✅ تم إنشاء جدول موحد: unified_ports")
        print(f"📊 عدد الموانئ: {inserted_count}")
        print(f"✅ تم إنشاء جدول التصنيفات: unified_port_classifications")
        print(f"🗑️ يمكن الآن حذف الجداول القديمة بأمان")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التنظيف: {e}")
        return False
    
    finally:
        db_manager.close()

def update_application_code():
    """تحديث كود التطبيق ليستخدم الجداول الموحدة"""
    print("\n🔧 تحديث كود التطبيق...")
    
    updates_needed = [
        "تحديث app/shipments/routes.py",
        "تحديث استعلامات قاعدة البيانات",
        "تحديث النظام الذكي للموانئ",
        "اختبار النظام الجديد"
    ]
    
    for update in updates_needed:
        print(f"📝 {update}")
    
    print("\n💡 التوصيات:")
    print("1. استخدم unified_ports كجدول رئيسي")
    print("2. استخدم unified_port_classifications للتصنيفات")
    print("3. احذف الجداول القديمة بعد التأكد من عمل النظام")

if __name__ == "__main__":
    print("🚀 بدء عملية تنظيف جداول الموانئ...")
    
    success = cleanup_ports_tables()
    
    if success:
        print(f"\n🎉 تم تنظيف جداول الموانئ بنجاح!")
        update_application_code()
    else:
        print(f"\n❌ فشل في تنظيف الجداول")
    
    print(f"\n✨ انتهت عملية التنظيف!")
