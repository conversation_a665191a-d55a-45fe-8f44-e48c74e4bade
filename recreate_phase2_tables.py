#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعادة إنشاء جداول المرحلة الثانية المتقدمة
Recreate Phase 2 Advanced Tables
"""

from database_manager import DatabaseManager

def main():
    print("🗑️ حذف الجداول القديمة وإنشاء الجداول الجديدة للمرحلة الثانية")
    print("=" * 70)
    
    try:
        db = DatabaseManager()
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # 1. حذف الجدول القديم
        print("\n🗑️ حذف الجدول القديم...")
        try:
            db.execute_update("DROP TABLE SHIPMENT_NOTIFICATIONS CASCADE CONSTRAINTS")
            print("✅ تم حذف جدول SHIPMENT_NOTIFICATIONS")
        except Exception as e:
            print(f"⚠️ لم يتم العثور على الجدول أو تم حذفه مسبقاً: {e}")
        
        # 2. إنشاء جدول سجل الإشعارات الجديد
        print("\n📱 إنشاء جدول سجل الإشعارات المتقدم...")
        notifications_log_sql = """
        CREATE TABLE notifications_log (
            id NUMBER PRIMARY KEY,
            recipient_type VARCHAR2(50) NOT NULL,
            recipient_id NUMBER,
            recipient_name VARCHAR2(200),
            recipient_email VARCHAR2(100),
            recipient_phone VARCHAR2(20),
            template_key VARCHAR2(100) NOT NULL,
            notification_title VARCHAR2(500),
            notification_content CLOB,
            channels_attempted VARCHAR2(200),
            channels_successful VARCHAR2(200),
            channels_failed VARCHAR2(200),
            notification_data CLOB,
            priority VARCHAR2(20) DEFAULT 'normal',
            status VARCHAR2(20) DEFAULT 'PENDING',
            sent_at DATE DEFAULT SYSDATE,
            delivered_at DATE,
            read_at DATE,
            error_message CLOB,
            retry_count NUMBER DEFAULT 0,
            max_retries NUMBER DEFAULT 3,
            order_id NUMBER,
            shipment_id NUMBER,
            created_at DATE DEFAULT SYSDATE
        )
        """
        db.execute_update(notifications_log_sql)
        print("✅ تم إنشاء جدول notifications_log")
        
        # 3. إنشاء sequence للإشعارات
        try:
            db.execute_update("CREATE SEQUENCE notifications_log_seq START WITH 1 INCREMENT BY 1 NOCACHE")
            print("✅ تم إنشاء sequence notifications_log_seq")
        except Exception as e:
            print(f"⚠️ sequence موجود مسبقاً: {e}")
        
        # 4. إنشاء جدول الأتمتة
        print("\n🤖 إنشاء جدول سجل الأتمتة...")
        automation_log_sql = """
        CREATE TABLE automation_log (
            id NUMBER PRIMARY KEY,
            action VARCHAR2(100) NOT NULL,
            description CLOB,
            action_date DATE DEFAULT SYSDATE,
            system_user VARCHAR2(50) DEFAULT 'SYSTEM',
            shipment_id NUMBER,
            order_id NUMBER,
            agent_id NUMBER,
            execution_time NUMBER(10,3),
            status VARCHAR2(20) DEFAULT 'SUCCESS',
            error_message CLOB,
            created_at DATE DEFAULT SYSDATE
        )
        """
        db.execute_update(automation_log_sql)
        print("✅ تم إنشاء جدول automation_log")
        
        # 5. إنشاء sequence للأتمتة
        try:
            db.execute_update("CREATE SEQUENCE automation_log_seq START WITH 1 INCREMENT BY 1 NOCACHE")
            print("✅ تم إنشاء sequence automation_log_seq")
        except Exception as e:
            print(f"⚠️ sequence موجود مسبقاً: {e}")
        
        # 6. إنشاء جدول مراقبة صحة النظام
        print("\n💓 إنشاء جدول مراقبة صحة النظام...")
        system_health_sql = """
        CREATE TABLE system_health_monitoring (
            id NUMBER PRIMARY KEY,
            check_date DATE DEFAULT SYSDATE,
            component_name VARCHAR2(100) NOT NULL,
            component_status VARCHAR2(20) DEFAULT 'healthy',
            response_time NUMBER(10,3),
            cpu_usage NUMBER(5,2),
            memory_usage NUMBER(5,2),
            disk_usage NUMBER(5,2),
            active_processes NUMBER DEFAULT 0,
            pending_tasks NUMBER DEFAULT 0,
            completed_tasks NUMBER DEFAULT 0,
            failed_tasks NUMBER DEFAULT 0,
            health_details CLOB,
            error_message CLOB,
            created_at DATE DEFAULT SYSDATE
        )
        """
        db.execute_update(system_health_sql)
        print("✅ تم إنشاء جدول system_health_monitoring")
        
        # 7. إنشاء sequence لمراقبة الصحة
        try:
            db.execute_update("CREATE SEQUENCE system_health_seq START WITH 1 INCREMENT BY 1 NOCACHE")
            print("✅ تم إنشاء sequence system_health_seq")
        except Exception as e:
            print(f"⚠️ sequence موجود مسبقاً: {e}")
        
        # 8. إنشاء جدول الإشعارات المجدولة
        print("\n⏰ إنشاء جدول الإشعارات المجدولة...")
        scheduled_notifications_sql = """
        CREATE TABLE scheduled_notifications (
            id NUMBER PRIMARY KEY,
            order_id NUMBER,
            shipment_id NUMBER,
            recipient_id NUMBER,
            notification_type VARCHAR2(100) NOT NULL,
            template_key VARCHAR2(100) NOT NULL,
            scheduled_time DATE NOT NULL,
            channels VARCHAR2(200),
            notification_data CLOB,
            status VARCHAR2(20) DEFAULT 'PENDING',
            sent_at DATE,
            error_message CLOB,
            is_recurring NUMBER(1) DEFAULT 0,
            recurrence_pattern VARCHAR2(100),
            next_occurrence DATE,
            created_at DATE DEFAULT SYSDATE,
            created_by NUMBER
        )
        """
        db.execute_update(scheduled_notifications_sql)
        print("✅ تم إنشاء جدول scheduled_notifications")
        
        # 9. إنشاء sequence للإشعارات المجدولة
        try:
            db.execute_update("CREATE SEQUENCE scheduled_notifications_seq START WITH 1 INCREMENT BY 1 NOCACHE")
            print("✅ تم إنشاء sequence scheduled_notifications_seq")
        except Exception as e:
            print(f"⚠️ sequence موجود مسبقاً: {e}")
        
        # 10. إنشاء جدول قوالب الإشعارات
        print("\n📄 إنشاء جدول قوالب الإشعارات...")
        notification_templates_sql = """
        CREATE TABLE notification_templates (
            id NUMBER PRIMARY KEY,
            template_key VARCHAR2(100) UNIQUE NOT NULL,
            template_name VARCHAR2(200) NOT NULL,
            template_category VARCHAR2(50),
            sms_template CLOB,
            email_subject_template VARCHAR2(500),
            email_body_template CLOB,
            whatsapp_template CLOB,
            push_title_template VARCHAR2(200),
            push_body_template VARCHAR2(500),
            default_channels VARCHAR2(200),
            priority VARCHAR2(20) DEFAULT 'normal',
            is_active NUMBER(1) DEFAULT 1,
            template_variables CLOB,
            created_at DATE DEFAULT SYSDATE,
            created_by NUMBER,
            updated_at DATE,
            updated_by NUMBER
        )
        """
        db.execute_update(notification_templates_sql)
        print("✅ تم إنشاء جدول notification_templates")
        
        # 11. إنشاء sequence لقوالب الإشعارات
        try:
            db.execute_update("CREATE SEQUENCE notification_templates_seq START WITH 1 INCREMENT BY 1 NOCACHE")
            print("✅ تم إنشاء sequence notification_templates_seq")
        except Exception as e:
            print(f"⚠️ sequence موجود مسبقاً: {e}")
        
        # 12. إدراج قوالب الإشعارات الافتراضية
        print("\n📝 إدراج قوالب الإشعارات الافتراضية...")
        
        templates = [
            {
                'key': 'delivery_order_created',
                'name': 'إنشاء أمر تسليم جديد',
                'category': 'delivery',
                'sms': 'تم إنشاء أمر تسليم جديد رقم {order_number} للشحنة {tracking_number}',
                'email_subject': 'أمر تسليم جديد - {order_number}',
                'email_body': 'تم إنشاء أمر تسليم جديد رقم {order_number} للشحنة {tracking_number}. يرجى المراجعة والمتابعة.',
                'whatsapp': 'أمر تسليم جديد 📦\nرقم الأمر: {order_number}\nرقم التتبع: {tracking_number}\nالأولوية: {priority}',
                'channels': 'SMS,EMAIL,WHATSAPP'
            },
            {
                'key': 'deadline_reminder',
                'name': 'تذكير موعد نهائي',
                'category': 'reminder',
                'sms': 'تذكير: أمر التسليم {order_number} مطلوب إنجازه خلال {hours_remaining} ساعة',
                'email_subject': 'تذكير موعد نهائي - {order_number}',
                'email_body': 'يرجى إنجاز أمر التسليم {order_number} قبل {deadline_date}',
                'whatsapp': '⏰ تذكير موعد نهائي\nأمر التسليم: {order_number}\nالموعد النهائي: {deadline_date}\nالوقت المتبقي: {hours_remaining} ساعة',
                'channels': 'SMS,EMAIL,WHATSAPP'
            },
            {
                'key': 'shipment_arrived',
                'name': 'وصول شحنة',
                'category': 'shipment',
                'sms': 'وصلت الشحنة {tracking_number} إلى الميناء. تم إنشاء أمر تسليم تلقائياً.',
                'email_subject': 'وصول شحنة - {tracking_number}',
                'email_body': 'وصلت الشحنة {tracking_number} إلى {port_name}. تم إنشاء أمر تسليم تلقائياً رقم {order_number}.',
                'whatsapp': '🚢 وصول شحنة\nرقم التتبع: {tracking_number}\nالميناء: {port_name}\nأمر التسليم: {order_number}',
                'channels': 'SMS,EMAIL,WHATSAPP'
            }
        ]
        
        for template in templates:
            insert_template_sql = f"""
            INSERT INTO notification_templates (
                id, template_key, template_name, template_category,
                sms_template, email_subject_template, email_body_template,
                whatsapp_template, default_channels
            ) VALUES (
                notification_templates_seq.NEXTVAL, '{template['key']}', '{template['name']}', '{template['category']}',
                '{template['sms']}', '{template['email_subject']}', '{template['email_body']}',
                '{template['whatsapp']}', '{template['channels']}'
            )
            """
            db.execute_update(insert_template_sql)
            print(f"  ✅ تم إدراج قالب: {template['name']}")
        
        # 13. إنشاء فهارس للأداء
        print("\n🔍 إنشاء الفهارس...")
        indexes = [
            "CREATE INDEX idx_notifications_log_date ON notifications_log(sent_at)",
            "CREATE INDEX idx_notifications_log_status ON notifications_log(status)",
            "CREATE INDEX idx_automation_log_date ON automation_log(action_date)",
            "CREATE INDEX idx_system_health_date ON system_health_monitoring(check_date)",
            "CREATE INDEX idx_scheduled_notifications_time ON scheduled_notifications(scheduled_time)"
        ]
        
        for index_sql in indexes:
            try:
                db.execute_update(index_sql)
                print(f"  ✅ تم إنشاء فهرس")
            except Exception as e:
                print(f"  ⚠️ فهرس موجود مسبقاً: {e}")
        
        # 14. اختبار الجداول
        print("\n🧪 اختبار الجداول الجديدة...")
        test_tables = [
            'notifications_log',
            'automation_log', 
            'system_health_monitoring',
            'scheduled_notifications',
            'notification_templates'
        ]
        
        for table in test_tables:
            try:
                result = db.execute_query(f"SELECT COUNT(*) FROM {table}")
                count = result[0][0] if result else 0
                print(f"  ✅ جدول {table}: متاح ({count} سجل)")
            except Exception as e:
                print(f"  ❌ جدول {table}: خطأ - {e}")
        
        print("\n" + "=" * 70)
        print("🎉 تم إنشاء جميع جداول المرحلة الثانية بنجاح!")
        print("✅ النظام جاهز للاستخدام مع جميع المميزات المتقدمة")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
