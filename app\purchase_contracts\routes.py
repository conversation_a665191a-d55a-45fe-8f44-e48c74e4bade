# -*- coding: utf-8 -*-
"""
مسارات عقود الشراء
Purchase Contracts Routes
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, abort
from datetime import datetime, date
from decimal import Decimal
from sqlalchemy import and_, or_, desc
from app import db
from app.models import PurchaseContract, PurchaseContractDetail, Supplier, Item, User
from app.purchase_contracts import bp
from app.purchase_contracts.forms import PurchaseContractForm, PurchaseContractSearchForm


@bp.route('/')
def index():
    """عرض قائمة عقود الشراء"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # نموذج البحث
    search_form = PurchaseContractSearchForm()
    
    # بناء الاستعلام
    query = PurchaseContract.query
    
    # تطبيق فلاتر البحث
    if request.args.get('contract_no'):
        query = query.filter(PurchaseContract.contract_no.contains(request.args.get('contract_no')))
    
    if request.args.get('vendor_name'):
        query = query.filter(PurchaseContract.vendor_name.contains(request.args.get('vendor_name')))
    
    if request.args.get('status'):
        query = query.filter(PurchaseContract.status == request.args.get('status'))
    
    if request.args.get('contract_type'):
        query = query.filter(PurchaseContract.contract_type == request.args.get('contract_type'))
    
    if request.args.get('contract_date_from'):
        date_from = datetime.strptime(request.args.get('contract_date_from'), '%Y-%m-%d').date()
        query = query.filter(PurchaseContract.contract_date >= date_from)
    
    if request.args.get('contract_date_to'):
        date_to = datetime.strptime(request.args.get('contract_date_to'), '%Y-%m-%d').date()
        query = query.filter(PurchaseContract.contract_date <= date_to)
    
    if request.args.get('amount_from'):
        query = query.filter(PurchaseContract.net_amount >= Decimal(request.args.get('amount_from')))
    
    if request.args.get('amount_to'):
        query = query.filter(PurchaseContract.net_amount <= Decimal(request.args.get('amount_to')))
    
    # ترتيب النتائج
    query = query.order_by(desc(PurchaseContract.contract_date), desc(PurchaseContract.id))
    
    # تطبيق التصفح
    contracts = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # إحصائيات
    stats = {
        'total_contracts': PurchaseContract.query.count(),
        'active_contracts': PurchaseContract.query.filter_by(status='نشط').count(),
        'expired_contracts': PurchaseContract.query.filter_by(status='منتهي').count(),
        'total_value': db.session.query(db.func.sum(PurchaseContract.net_amount)).scalar() or 0
    }
    
    return render_template('purchase_contracts/index.html',
                         title='عقود الشراء',
                         contracts=contracts,
                         search_form=search_form,
                         stats=stats)


@bp.route('/new', methods=['GET', 'POST'])
def new():
    """إنشاء عقد شراء جديد"""
    form = PurchaseContractForm()
    
    # تحميل خيارات الموردين
    form.supplier_id.choices = [(0, 'اختر المورد')] + [
        (s.id, f"{s.code} - {s.name_ar}")
        for s in Supplier.query.filter_by(is_active=True).order_by(Supplier.name_ar).all()
    ]
    
    # إضافة سطر تفاصيل افتراضي
    if request.method == 'GET':
        if not form.details.entries:
            form.details.append_entry()
            # تحميل خيارات الأصناف للسطر الأول
            if form.details.entries:
                detail_form = form.details.entries[0]
                detail_form.item_id.choices = [(0, 'اختر الصنف')] + [
                    (item.id, f"{item.code} - {item.name_ar}")
                    for item in Item.query.filter_by(is_active=True).order_by(Item.name_ar).all()
                ]
    
    if form.validate_on_submit():
        # التأكد من وجود مستخدم افتراضي
        default_user = User.query.first()
        if not default_user:
            default_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='System Administrator',
                is_admin=True,
                is_active=True
            )
            default_user.set_password('admin123')
            db.session.add(default_user)
            db.session.commit()
        
        # إنشاء رقم العقد
        contract_no = generate_contract_number()
        
        # حساب صافي المبلغ
        net_amount = form.contract_amount.data - (form.discount_amount.data or 0)
        
        # إنشاء العقد
        contract = PurchaseContract(
            branch_no=form.branch_no.data,
            contract_no=contract_no,
            contract_serial=form.contract_serial.data,
            contract_date=form.contract_date.data,
            contract_from_date=form.contract_from_date.data,
            contract_to_date=form.contract_to_date.data,
            vendor_code=form.vendor_code.data,
            vendor_name=form.vendor_name.data,
            supplier_id=form.supplier_id.data if form.supplier_id.data else None,
            currency_code=form.currency_code.data,
            contract_rate=form.contract_rate.data,
            contract_type=form.contract_type.data,
            contract_amount=form.contract_amount.data,
            discount_amount=form.discount_amount.data or 0,
            net_amount=net_amount,
            reference_no=form.reference_no.data,
            contract_desc=form.contract_desc.data,
            status=form.status.data,
            contract_note=form.contract_note.data,
            created_by=default_user.id
        )
        
        db.session.add(contract)
        db.session.flush()  # للحصول على ID
        
        # إضافة تفاصيل العقد
        line_no = 1
        for detail_form in form.details:
            if detail_form.item_code.data:
                # حساب إجمالي السطر
                quantity = detail_form.quantity.data
                unit_price = detail_form.unit_price.data
                discount_percent = detail_form.discount_percent.data or 0
                
                line_total = quantity * unit_price
                if discount_percent > 0:
                    line_total = line_total * (1 - discount_percent / 100)
                
                detail = PurchaseContractDetail(
                    contract_id=contract.id,
                    line_no=line_no,
                    item_code=detail_form.item_code.data,
                    item_name=detail_form.item_name.data,
                    item_id=detail_form.item_id.data if detail_form.item_id.data else None,
                    unit_code=detail_form.unit_code.data,
                    pack_size=detail_form.pack_size.data,
                    production_date=detail_form.production_date.data,
                    expiry_date=detail_form.expiry_date.data,
                    quantity=quantity,
                    unit_price=unit_price,
                    free_quantity=detail_form.free_quantity.data or 0,
                    discount_percent=discount_percent,
                    line_total=line_total,
                    line_description=detail_form.line_description.data
                )
                
                db.session.add(detail)
                line_no += 1
        
        db.session.commit()
        
        flash(f'تم إنشاء عقد الشراء رقم {contract_no} بنجاح', 'success')
        return redirect(url_for('purchase_contracts.view', id=contract.id))
    
    return render_template('purchase_contracts/form.html',
                         title='عقد شراء جديد',
                         form=form,
                         is_new=True)


def generate_contract_number():
    """توليد رقم عقد الشراء"""
    today = date.today()
    prefix = f"PC{today.year}{today.month:02d}"
    
    # البحث عن آخر رقم
    last_contract = PurchaseContract.query.filter(
        PurchaseContract.contract_no.like(f"{prefix}%")
    ).order_by(desc(PurchaseContract.contract_no)).first()
    
    if last_contract:
        last_number = int(last_contract.contract_no[-4:])
        new_number = last_number + 1
    else:
        new_number = 1
    
    return f"{prefix}{new_number:04d}"


@bp.route('/<int:id>')
def view(id):
    """عرض تفاصيل عقد الشراء"""
    contract = PurchaseContract.query.get_or_404(id)
    
    return render_template('purchase_contracts/view.html',
                         title=f'عقد الشراء - {contract.contract_no}',
                         contract=contract)


@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
def edit(id):
    """تعديل عقد الشراء"""
    contract = PurchaseContract.query.get_or_404(id)
    form = PurchaseContractForm(obj=contract)
    
    # تحميل خيارات الموردين
    form.supplier_id.choices = [(0, 'اختر المورد')] + [
        (s.id, f"{s.code} - {s.name_ar}")
        for s in Supplier.query.filter_by(is_active=True).order_by(Supplier.name_ar).all()
    ]
    
    if request.method == 'GET':
        # تحميل تفاصيل العقد
        form.details.entries.clear()
        for detail in contract.details.order_by(PurchaseContractDetail.line_no):
            detail_form = form.details.append_entry()
            detail_form.line_no.data = detail.line_no
            detail_form.item_code.data = detail.item_code
            detail_form.item_name.data = detail.item_name
            detail_form.item_id.data = detail.item_id
            detail_form.unit_code.data = detail.unit_code
            detail_form.pack_size.data = detail.pack_size
            detail_form.production_date.data = detail.production_date
            detail_form.expiry_date.data = detail.expiry_date
            detail_form.quantity.data = detail.quantity
            detail_form.unit_price.data = detail.unit_price
            detail_form.free_quantity.data = detail.free_quantity
            detail_form.discount_percent.data = detail.discount_percent
            detail_form.line_total.data = detail.line_total
            detail_form.line_description.data = detail.line_description
            
            # تحميل خيارات الأصناف
            detail_form.item_id.choices = [(0, 'اختر الصنف')] + [
                (item.id, f"{item.code} - {item.name_ar}")
                for item in Item.query.filter_by(is_active=True).order_by(Item.name_ar).all()
            ]
    
    if form.validate_on_submit():
        # تحديث بيانات العقد
        contract.branch_no = form.branch_no.data
        contract.contract_serial = form.contract_serial.data
        contract.contract_date = form.contract_date.data
        contract.contract_from_date = form.contract_from_date.data
        contract.contract_to_date = form.contract_to_date.data
        contract.vendor_code = form.vendor_code.data
        contract.vendor_name = form.vendor_name.data
        contract.supplier_id = form.supplier_id.data if form.supplier_id.data else None
        contract.currency_code = form.currency_code.data
        contract.contract_rate = form.contract_rate.data
        contract.contract_type = form.contract_type.data
        contract.contract_amount = form.contract_amount.data
        contract.discount_amount = form.discount_amount.data or 0
        contract.net_amount = form.contract_amount.data - (form.discount_amount.data or 0)
        contract.reference_no = form.reference_no.data
        contract.contract_desc = form.contract_desc.data
        contract.status = form.status.data
        contract.contract_note = form.contract_note.data
        contract.updated_at = datetime.utcnow()
        
        # حذف التفاصيل القديمة
        PurchaseContractDetail.query.filter_by(contract_id=contract.id).delete()
        
        # إضافة التفاصيل الجديدة
        line_no = 1
        for detail_form in form.details:
            if detail_form.item_code.data:
                # حساب إجمالي السطر
                quantity = detail_form.quantity.data
                unit_price = detail_form.unit_price.data
                discount_percent = detail_form.discount_percent.data or 0
                
                line_total = quantity * unit_price
                if discount_percent > 0:
                    line_total = line_total * (1 - discount_percent / 100)
                
                detail = PurchaseContractDetail(
                    contract_id=contract.id,
                    line_no=line_no,
                    item_code=detail_form.item_code.data,
                    item_name=detail_form.item_name.data,
                    item_id=detail_form.item_id.data if detail_form.item_id.data else None,
                    unit_code=detail_form.unit_code.data,
                    pack_size=detail_form.pack_size.data,
                    production_date=detail_form.production_date.data,
                    expiry_date=detail_form.expiry_date.data,
                    quantity=quantity,
                    unit_price=unit_price,
                    free_quantity=detail_form.free_quantity.data or 0,
                    discount_percent=discount_percent,
                    line_total=line_total,
                    line_description=detail_form.line_description.data
                )
                
                db.session.add(detail)
                line_no += 1
        
        db.session.commit()
        
        flash(f'تم تحديث عقد الشراء رقم {contract.contract_no} بنجاح', 'success')
        return redirect(url_for('purchase_contracts.view', id=contract.id))
    
    return render_template('purchase_contracts/form.html',
                         title=f'تعديل عقد الشراء - {contract.contract_no}',
                         form=form,
                         contract=contract,
                         is_new=False)


@bp.route('/<int:id>/delete', methods=['POST'])
def delete(id):
    """حذف عقد الشراء"""
    contract = PurchaseContract.query.get_or_404(id)

    try:
        # حذف التفاصيل أولاً
        PurchaseContractDetail.query.filter_by(contract_id=contract.id).delete()

        # حذف العقد
        db.session.delete(contract)
        db.session.commit()

        flash(f'تم حذف عقد الشراء رقم {contract.contract_no} بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء حذف العقد', 'error')

    return redirect(url_for('purchase_contracts.index'))


@bp.route('/search')
def search():
    """البحث المتقدم في عقود الشراء"""
    form = PurchaseContractSearchForm()
    contracts = []

    if request.args:
        query = PurchaseContract.query

        # تطبيق فلاتر البحث
        if request.args.get('contract_no'):
            query = query.filter(PurchaseContract.contract_no.contains(request.args.get('contract_no')))

        if request.args.get('vendor_name'):
            query = query.filter(PurchaseContract.vendor_name.contains(request.args.get('vendor_name')))

        if request.args.get('status'):
            query = query.filter(PurchaseContract.status == request.args.get('status'))

        if request.args.get('contract_type'):
            query = query.filter(PurchaseContract.contract_type == request.args.get('contract_type'))

        if request.args.get('contract_date_from'):
            date_from = datetime.strptime(request.args.get('contract_date_from'), '%Y-%m-%d').date()
            query = query.filter(PurchaseContract.contract_date >= date_from)

        if request.args.get('contract_date_to'):
            date_to = datetime.strptime(request.args.get('contract_date_to'), '%Y-%m-%d').date()
            query = query.filter(PurchaseContract.contract_date <= date_to)

        if request.args.get('amount_from'):
            query = query.filter(PurchaseContract.net_amount >= Decimal(request.args.get('amount_from')))

        if request.args.get('amount_to'):
            query = query.filter(PurchaseContract.net_amount <= Decimal(request.args.get('amount_to')))

        contracts = query.order_by(desc(PurchaseContract.contract_date)).limit(100).all()

    return render_template('purchase_contracts/search.html',
                         title='البحث في عقود الشراء',
                         form=form,
                         contracts=contracts)


@bp.route('/api/suppliers')
def api_suppliers():
    """API للحصول على قائمة الموردين"""
    suppliers = Supplier.query.filter_by(is_active=True).order_by(Supplier.name_ar).all()
    return jsonify([{
        'id': s.id,
        'code': s.code,
        'name_ar': s.name_ar,
        'name_en': s.name_en or '',
        'phone': s.phone or '',
        'email': s.email or ''
    } for s in suppliers])


@bp.route('/api/items')
def api_items():
    """API للحصول على قائمة الأصناف"""
    items = Item.query.filter_by(is_active=True).order_by(Item.name_ar).all()
    return jsonify([{
        'id': i.id,
        'code': i.code,
        'name_ar': i.name_ar,
        'name_en': i.name_en or '',
        'unit_of_measure': i.unit_of_measure,
        'unit_price': float(i.unit_price)
    } for i in items])


@bp.route('/api/contract/<int:id>')
def api_contract(id):
    """API للحصول على تفاصيل عقد"""
    contract = PurchaseContract.query.get_or_404(id)

    return jsonify({
        'id': contract.id,
        'contract_no': contract.contract_no,
        'contract_date': contract.contract_date.isoformat(),
        'vendor_name': contract.vendor_name,
        'contract_type': contract.contract_type,
        'status': contract.status,
        'net_amount': float(contract.net_amount),
        'currency_code': contract.currency_code,
        'details': [{
            'line_no': d.line_no,
            'item_code': d.item_code,
            'item_name': d.item_name,
            'quantity': float(d.quantity),
            'unit_price': float(d.unit_price),
            'line_total': float(d.line_total)
        } for d in contract.details.order_by(PurchaseContractDetail.line_no)]
    })


@bp.route('/<int:id>/print')
def print_contract(id):
    """طباعة عقد الشراء"""
    contract = PurchaseContract.query.get_or_404(id)

    return render_template('purchase_contracts/print.html',
                         title=f'طباعة عقد الشراء - {contract.contract_no}',
                         contract=contract)


@bp.route('/reports')
def reports():
    """تقارير عقود الشراء"""
    # إحصائيات شاملة
    stats = {
        'total_contracts': PurchaseContract.query.count(),
        'active_contracts': PurchaseContract.query.filter_by(status='نشط').count(),
        'expired_contracts': PurchaseContract.query.filter_by(status='منتهي').count(),
        'cancelled_contracts': PurchaseContract.query.filter_by(status='ملغي').count(),
        'total_value': db.session.query(db.func.sum(PurchaseContract.net_amount)).scalar() or 0,
        'active_value': db.session.query(db.func.sum(PurchaseContract.net_amount)).filter(
            PurchaseContract.status == 'نشط').scalar() or 0
    }

    # العقود حسب النوع
    contract_types = db.session.query(
        PurchaseContract.contract_type,
        db.func.count(PurchaseContract.id).label('count'),
        db.func.sum(PurchaseContract.net_amount).label('total_value')
    ).group_by(PurchaseContract.contract_type).all()

    # العقود حسب الشهر (آخر 12 شهر)
    monthly_contracts = db.session.query(
        db.func.date_format(PurchaseContract.contract_date, '%Y-%m').label('month'),
        db.func.count(PurchaseContract.id).label('count'),
        db.func.sum(PurchaseContract.net_amount).label('total_value')
    ).filter(
        PurchaseContract.contract_date >= datetime.now().replace(day=1, month=1)
    ).group_by(
        db.func.date_format(PurchaseContract.contract_date, '%Y-%m')
    ).order_by('month').all()

    # أكبر الموردين
    top_suppliers = db.session.query(
        PurchaseContract.vendor_name,
        db.func.count(PurchaseContract.id).label('contract_count'),
        db.func.sum(PurchaseContract.net_amount).label('total_value')
    ).group_by(PurchaseContract.vendor_name).order_by(
        db.func.sum(PurchaseContract.net_amount).desc()
    ).limit(10).all()

    return render_template('purchase_contracts/reports.html',
                         title='تقارير عقود الشراء',
                         stats=stats,
                         contract_types=contract_types,
                         monthly_contracts=monthly_contracts,
                         top_suppliers=top_suppliers)
