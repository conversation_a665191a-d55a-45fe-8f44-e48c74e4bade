-- =====================================================
-- تكامل نظام المشتريات مع نظام الموردين والحوالات
-- Purchase Orders Integration with Suppliers and Transfers
-- =====================================================

-- 1. تعديل جدول أوامر الشراء لإضافة حقول التكامل
ALTER TABLE PURCHASE_ORDERS ADD (
    -- ربط مع نظام الموردين
    supplier_id NUMBER,
    
    -- حالة الدفع
    payment_status VARCHAR2(30) DEFAULT 'PENDING', -- PENDING, PARTIAL, PAID, OVERDUE
    payment_due_date DATE,
    payment_terms_days NUMBER DEFAULT 30,
    
    -- المبالغ المالية المفصلة
    subtotal_amount NUMBER(15,2) DEFAULT 0,
    tax_amount NUMBER(15,2) DEFAULT 0,
    discount_amount NUMBER(15,2) DEFAULT 0,
    shipping_amount NUMBER(15,2) DEFAULT 0,
    other_charges NUMBER(15,2) DEFAULT 0,
    total_amount_due NUMBER(15,2) DEFAULT 0,
    paid_amount NUMBER(15,2) DEFAULT 0,
    outstanding_amount NUMBER(15,2) DEFAULT 0,
    
    -- معلومات الدفع
    advance_payment_amount NUMBER(15,2) DEFAULT 0,
    advance_payment_date DATE,
    final_payment_amount NUMBER(15,2) DEFAULT 0,
    final_payment_date DATE,
    
    -- ربط مع العملات
    exchange_rate NUMBER(15,6) DEFAULT 1,
    base_currency_amount NUMBER(15,2),
    
    -- حالة التسليم والاستلام
    delivery_status VARCHAR2(30) DEFAULT 'PENDING', -- PENDING, PARTIAL, DELIVERED, RECEIVED
    goods_received_date DATE,
    goods_received_by NUMBER,
    
    -- معلومات إضافية للتكامل
    is_recurring CHAR(1) DEFAULT 'N',
    parent_po_id NUMBER,
    approval_workflow_id NUMBER,
    
    -- تواريخ مهمة
    approved_date DATE,
    approved_by NUMBER,
    cancelled_date DATE,
    cancelled_by NUMBER,
    cancellation_reason VARCHAR2(500)
);

-- إضافة المفاتيح الخارجية
ALTER TABLE PURCHASE_ORDERS ADD CONSTRAINT fk_po_supplier 
    FOREIGN KEY (supplier_id) REFERENCES SUPPLIERS(id);

ALTER TABLE PURCHASE_ORDERS ADD CONSTRAINT fk_po_approved_by 
    FOREIGN KEY (approved_by) REFERENCES USERS(id);

ALTER TABLE PURCHASE_ORDERS ADD CONSTRAINT fk_po_cancelled_by 
    FOREIGN KEY (cancelled_by) REFERENCES USERS(id);

ALTER TABLE PURCHASE_ORDERS ADD CONSTRAINT fk_po_received_by 
    FOREIGN KEY (goods_received_by) REFERENCES USERS(id);

-- 2. إنشاء جدول ربط أوامر الشراء بالمدفوعات
CREATE TABLE PURCHASE_ORDER_PAYMENTS (
    id NUMBER PRIMARY KEY,
    purchase_order_id NUMBER NOT NULL,
    supplier_payment_transfer_id NUMBER,
    transfer_request_id NUMBER,
    transfer_id NUMBER,
    
    -- تفاصيل الدفعة
    payment_type VARCHAR2(30) NOT NULL, -- ADVANCE, PARTIAL, FINAL, FULL
    payment_amount NUMBER(15,2) NOT NULL,
    currency_code VARCHAR2(3) NOT NULL,
    exchange_rate NUMBER(15,6) DEFAULT 1,
    base_currency_amount NUMBER(15,2),
    
    -- تفاصيل إضافية
    payment_percentage NUMBER(5,2), -- نسبة الدفعة من إجمالي أمر الشراء
    payment_description VARCHAR2(500),
    payment_reference VARCHAR2(100),
    
    -- حالة الدفعة
    payment_status VARCHAR2(20) DEFAULT 'PENDING', -- PENDING, APPROVED, EXECUTED, COMPLETED, CANCELLED
    
    -- تواريخ مهمة
    payment_due_date DATE,
    payment_requested_date DATE DEFAULT SYSDATE,
    payment_approved_date DATE,
    payment_executed_date DATE,
    payment_completed_date DATE,
    
    -- معلومات المعالجة
    requested_by NUMBER,
    approved_by NUMBER,
    executed_by NUMBER,
    
    -- ملاحظات
    notes CLOB,
    
    -- بيانات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_pop_purchase_order FOREIGN KEY (purchase_order_id) REFERENCES PURCHASE_ORDERS(id),
    CONSTRAINT fk_pop_supplier_payment FOREIGN KEY (supplier_payment_transfer_id) REFERENCES SUPPLIER_PAYMENT_TRANSFERS(id),
    CONSTRAINT fk_pop_transfer_request FOREIGN KEY (transfer_request_id) REFERENCES TRANSFER_REQUESTS(id),
    CONSTRAINT fk_pop_transfer FOREIGN KEY (transfer_id) REFERENCES TRANSFERS(id),
    CONSTRAINT fk_pop_currency FOREIGN KEY (currency_code) REFERENCES CURRENCIES(code),
    CONSTRAINT fk_pop_requested_by FOREIGN KEY (requested_by) REFERENCES USERS(id),
    CONSTRAINT fk_pop_approved_by FOREIGN KEY (approved_by) REFERENCES USERS(id),
    CONSTRAINT fk_pop_executed_by FOREIGN KEY (executed_by) REFERENCES USERS(id),
    CONSTRAINT fk_pop_created_by FOREIGN KEY (created_by) REFERENCES USERS(id),
    CONSTRAINT fk_pop_updated_by FOREIGN KEY (updated_by) REFERENCES USERS(id)
);

-- 3. إنشاء جدول تتبع حالة أوامر الشراء
CREATE TABLE PURCHASE_ORDER_STATUS_LOG (
    id NUMBER PRIMARY KEY,
    purchase_order_id NUMBER NOT NULL,
    old_status VARCHAR2(30),
    new_status VARCHAR2(30) NOT NULL,
    status_type VARCHAR2(20) NOT NULL, -- ORDER_STATUS, PAYMENT_STATUS, DELIVERY_STATUS
    change_reason VARCHAR2(500),
    change_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    changed_by NUMBER,
    
    -- معلومات إضافية
    related_document_type VARCHAR2(30), -- TRANSFER_REQUEST, GOODS_RECEIPT, INVOICE
    related_document_id NUMBER,
    system_generated CHAR(1) DEFAULT 'N',
    
    -- بيانات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_posl_purchase_order FOREIGN KEY (purchase_order_id) REFERENCES PURCHASE_ORDERS(id),
    CONSTRAINT fk_posl_changed_by FOREIGN KEY (changed_by) REFERENCES USERS(id)
);

-- 4. تعديل جدول SUPPLIER_TRANSACTIONS لربطه بأوامر الشراء
ALTER TABLE SUPPLIER_TRANSACTIONS ADD (
    purchase_order_id NUMBER,
    purchase_order_number VARCHAR2(50),
    po_item_id NUMBER,
    
    -- تفاصيل إضافية لأوامر الشراء
    delivery_date DATE,
    goods_received_date DATE,
    invoice_received_date DATE,
    
    -- ربط مع المدفوعات
    payment_request_id NUMBER,
    payment_status VARCHAR2(20) DEFAULT 'PENDING'
);

-- إضافة مفتاح خارجي لربط المعاملات بأوامر الشراء
ALTER TABLE SUPPLIER_TRANSACTIONS ADD CONSTRAINT fk_st_purchase_order 
    FOREIGN KEY (purchase_order_id) REFERENCES PURCHASE_ORDERS(id);

-- 5. إنشاء جدول ربط أوامر الشراء بالعقود
CREATE TABLE PURCHASE_ORDER_CONTRACTS (
    id NUMBER PRIMARY KEY,
    purchase_order_id NUMBER NOT NULL,
    contract_id NUMBER NOT NULL,
    
    -- تفاصيل الربط
    contract_amount_allocated NUMBER(15,2),
    contract_percentage_used NUMBER(5,2),
    
    -- حالة الربط
    allocation_status VARCHAR2(20) DEFAULT 'ACTIVE', -- ACTIVE, COMPLETED, CANCELLED
    allocation_date DATE DEFAULT SYSDATE,
    
    -- معلومات إضافية
    notes VARCHAR2(500),
    
    -- بيانات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_poc_purchase_order FOREIGN KEY (purchase_order_id) REFERENCES PURCHASE_ORDERS(id),
    CONSTRAINT fk_poc_contract FOREIGN KEY (contract_id) REFERENCES CONTRACTS(contract_id),
    CONSTRAINT fk_poc_created_by FOREIGN KEY (created_by) REFERENCES USERS(id),
    
    -- قيد فريد لمنع الربط المتكرر
    CONSTRAINT uk_po_contract UNIQUE (purchase_order_id, contract_id)
);

-- 6. إنشاء جدول استلام البضائع
CREATE TABLE GOODS_RECEIPTS (
    id NUMBER PRIMARY KEY,
    receipt_number VARCHAR2(50) UNIQUE NOT NULL,
    purchase_order_id NUMBER NOT NULL,
    supplier_id NUMBER NOT NULL,
    
    -- تفاصيل الاستلام
    receipt_date DATE DEFAULT SYSDATE,
    delivery_note_number VARCHAR2(100),
    invoice_number VARCHAR2(100),
    
    -- حالة الاستلام
    receipt_status VARCHAR2(20) DEFAULT 'PENDING', -- PENDING, PARTIAL, COMPLETE, REJECTED
    quality_status VARCHAR2(20) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED, CONDITIONAL
    
    -- معلومات الاستلام
    received_by NUMBER,
    inspected_by NUMBER,
    approved_by NUMBER,
    
    -- ملاحظات
    receipt_notes CLOB,
    quality_notes CLOB,
    rejection_reason VARCHAR2(500),
    
    -- بيانات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_gr_purchase_order FOREIGN KEY (purchase_order_id) REFERENCES PURCHASE_ORDERS(id),
    CONSTRAINT fk_gr_supplier FOREIGN KEY (supplier_id) REFERENCES SUPPLIERS(id),
    CONSTRAINT fk_gr_received_by FOREIGN KEY (received_by) REFERENCES USERS(id),
    CONSTRAINT fk_gr_inspected_by FOREIGN KEY (inspected_by) REFERENCES USERS(id),
    CONSTRAINT fk_gr_approved_by FOREIGN KEY (approved_by) REFERENCES USERS(id),
    CONSTRAINT fk_gr_created_by FOREIGN KEY (created_by) REFERENCES USERS(id),
    CONSTRAINT fk_gr_updated_by FOREIGN KEY (updated_by) REFERENCES USERS(id)
);

-- 7. إنشاء جدول تفاصيل استلام البضائع
CREATE TABLE GOODS_RECEIPT_ITEMS (
    id NUMBER PRIMARY KEY,
    goods_receipt_id NUMBER NOT NULL,
    po_item_id NUMBER NOT NULL,
    
    -- كميات
    ordered_quantity NUMBER(15,3) NOT NULL,
    delivered_quantity NUMBER(15,3) NOT NULL,
    received_quantity NUMBER(15,3) NOT NULL,
    rejected_quantity NUMBER(15,3) DEFAULT 0,
    accepted_quantity NUMBER(15,3) DEFAULT 0,
    
    -- معلومات الجودة
    quality_status VARCHAR2(20) DEFAULT 'PENDING',
    batch_number VARCHAR2(100),
    serial_numbers CLOB, -- JSON array للأرقام التسلسلية
    
    -- تواريخ
    production_date DATE,
    expiry_date DATE,
    
    -- ملاحظات
    item_notes VARCHAR2(500),
    rejection_reason VARCHAR2(500),
    
    -- بيانات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_gri_goods_receipt FOREIGN KEY (goods_receipt_id) REFERENCES GOODS_RECEIPTS(id),
    CONSTRAINT fk_gri_po_item FOREIGN KEY (po_item_id) REFERENCES PO_ITEMS(id),
    CONSTRAINT fk_gri_created_by FOREIGN KEY (created_by) REFERENCES USERS(id)
);

-- 8. إنشاء Sequences للجداول الجديدة
CREATE SEQUENCE PURCHASE_ORDER_PAYMENTS_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE PURCHASE_ORDER_STATUS_LOG_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE PURCHASE_ORDER_CONTRACTS_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE GOODS_RECEIPTS_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE GOODS_RECEIPT_ITEMS_SEQ START WITH 1 INCREMENT BY 1;

-- 9. إنشاء Triggers للـ Auto Increment
CREATE OR REPLACE TRIGGER purchase_order_payments_trigger
    BEFORE INSERT ON PURCHASE_ORDER_PAYMENTS
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := PURCHASE_ORDER_PAYMENTS_SEQ.NEXTVAL;
    END IF;
    
    -- حساب المبلغ بالعملة الأساسية
    :NEW.base_currency_amount := :NEW.payment_amount * NVL(:NEW.exchange_rate, 1);
    
    -- حساب نسبة الدفعة إذا لم تكن محددة
    IF :NEW.payment_percentage IS NULL THEN
        DECLARE
            v_total_amount NUMBER;
        BEGIN
            SELECT total_amount_due INTO v_total_amount
            FROM PURCHASE_ORDERS
            WHERE id = :NEW.purchase_order_id;
            
            IF v_total_amount > 0 THEN
                :NEW.payment_percentage := (:NEW.payment_amount / v_total_amount) * 100;
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                :NEW.payment_percentage := 0;
        END;
    END IF;
    
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;

CREATE OR REPLACE TRIGGER purchase_order_status_log_trigger
    BEFORE INSERT ON PURCHASE_ORDER_STATUS_LOG
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := PURCHASE_ORDER_STATUS_LOG_SEQ.NEXTVAL;
    END IF;
END;

CREATE OR REPLACE TRIGGER goods_receipts_trigger
    BEFORE INSERT ON GOODS_RECEIPTS
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := GOODS_RECEIPTS_SEQ.NEXTVAL;
    END IF;
    
    -- إنشاء رقم إيصال تلقائي إذا لم يكن محدد
    IF :NEW.receipt_number IS NULL THEN
        :NEW.receipt_number := 'GR' || TO_CHAR(SYSDATE, 'YYYYMMDD') || LPAD(GOODS_RECEIPTS_SEQ.CURRVAL, 4, '0');
    END IF;
    
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;

-- 10. إنشاء فهارس للأداء
CREATE INDEX idx_po_supplier_id ON PURCHASE_ORDERS(supplier_id);
CREATE INDEX idx_po_payment_status ON PURCHASE_ORDERS(payment_status);
CREATE INDEX idx_po_delivery_status ON PURCHASE_ORDERS(delivery_status);
CREATE INDEX idx_po_payment_due_date ON PURCHASE_ORDERS(payment_due_date);

CREATE INDEX idx_pop_purchase_order ON PURCHASE_ORDER_PAYMENTS(purchase_order_id);
CREATE INDEX idx_pop_payment_status ON PURCHASE_ORDER_PAYMENTS(payment_status);
CREATE INDEX idx_pop_payment_type ON PURCHASE_ORDER_PAYMENTS(payment_type);
CREATE INDEX idx_pop_due_date ON PURCHASE_ORDER_PAYMENTS(payment_due_date);

CREATE INDEX idx_posl_purchase_order ON PURCHASE_ORDER_STATUS_LOG(purchase_order_id);
CREATE INDEX idx_posl_change_date ON PURCHASE_ORDER_STATUS_LOG(change_date);
CREATE INDEX idx_posl_status_type ON PURCHASE_ORDER_STATUS_LOG(status_type);

CREATE INDEX idx_gr_purchase_order ON GOODS_RECEIPTS(purchase_order_id);
CREATE INDEX idx_gr_supplier ON GOODS_RECEIPTS(supplier_id);
CREATE INDEX idx_gr_receipt_date ON GOODS_RECEIPTS(receipt_date);
CREATE INDEX idx_gr_status ON GOODS_RECEIPTS(receipt_status);

-- تم إنشاء التكامل مع نظام المشتريات بنجاح
-- Purchase Orders Integration completed successfully
