#!/usr/bin/env python3
"""
تحديث قيد نوع الوثيقة في جدول contract_documents
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oracle_manager import get_oracle_manager

def update_constraint():
    """تحديث قيد نوع الوثيقة"""
    
    oracle_manager = get_oracle_manager()
    
    try:
        print("🔧 تحديث قيد نوع الوثيقة...")
        
        # حذف القيد القديم
        drop_constraint_sql = """
            ALTER TABLE contract_documents 
            DROP CONSTRAINT SYS_C00183019
        """
        
        try:
            oracle_manager.execute_update(drop_constraint_sql, [])
            print("✅ تم حذف القيد القديم")
        except Exception as e:
            print(f"⚠️ فشل في حذف القيد القديم: {e}")
        
        # إضافة القيد الجديد
        add_constraint_sql = """
            ALTER TABLE contract_documents 
            ADD CONSTRAINT chk_contract_document_type 
            CHECK (document_type IN ('attachment', 'link', 'contract', 'amendment', 'invoice', 'receipt', 'certificate', 'license', 'insurance', 'guarantee', 'correspondence', 'other'))
        """
        
        oracle_manager.execute_update(add_constraint_sql, [])
        oracle_manager.commit()
        
        print("✅ تم إضافة القيد الجديد بنجاح")
        
        # فحص القيد الجديد
        check_constraint_query = """
            SELECT constraint_name, search_condition
            FROM user_constraints 
            WHERE table_name = 'CONTRACT_DOCUMENTS' 
            AND constraint_type = 'C'
            AND constraint_name = 'CHK_CONTRACT_DOCUMENT_TYPE'
        """
        
        result = oracle_manager.execute_query(check_constraint_query, [])
        if result:
            print(f"📋 القيد الجديد: {result[0][0]}")
            print(f"📝 الشرط: {result[0][1]}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء تحديث قيد نوع الوثيقة...")
    success = update_constraint()
    
    if success:
        print("\n✅ تم الانتهاء بنجاح")
    else:
        print("\n❌ فشل في العملية")
        sys.exit(1)
