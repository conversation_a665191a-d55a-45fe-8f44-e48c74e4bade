{% extends "base.html" %}

{% block title %}إدارة الإفراج{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-clipboard-check me-2 text-primary"></i>
                        إدارة الإفراج
                    </h2>
                    <p class="text-muted mb-0">إدارة وتحديث حالات إفراج الشحنات</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" onclick="bulkReleaseUpdate()">
                        <i class="fas fa-edit me-1"></i>
                        تحديث متعدد
                    </button>
                    <button class="btn btn-success" onclick="autoReleaseUpdate()">
                        <i class="fas fa-magic me-1"></i>
                        تحديث ذكي
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إعدادات حالات الإفراج -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        حالات الإفراج المتاحة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for status in release_statuses %}
                        <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
                            <div class="card border h-100" style="border-color: {{ status[2] }}!important;">
                                <div class="card-body text-center p-3">
                                    <div class="mb-2">
                                        <i class="{{ status[3] }} fa-2x" style="color: {{ status[2] }}"></i>
                                    </div>
                                    <h6 class="mb-1">{{ status[1] }}</h6>
                                    <small class="text-muted">{{ status[6] or 'وصف غير متاح' }}</small>
                                    <div class="mt-2">
                                        {% if status[4] == 1 %}
                                        <span class="badge bg-success">نشط</span>
                                        {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                        {% endif %}
                                        
                                        {% if status[5] == 1 %}
                                        <span class="badge bg-warning">يتطلب اعتماد</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الشحنات القابلة للإدارة -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            الشحنات القابلة للإدارة
                        </h5>
                        <div>
                            <input type="text" class="form-control form-control-sm" 
                                   placeholder="البحث..." id="searchInput" style="width: 200px;">
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="shipmentsTable">
                            <thead class="table-light">
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>رقم التتبع</th>
                                    <th>المرسل</th>
                                    <th>المستلم</th>
                                    <th>حالة الإفراج</th>
                                    <th>حالة الشحنة</th>
                                    <th>آخر تحديث</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for shipment in shipments %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="shipment-checkbox" value="{{ shipment[0] }}">
                                    </td>
                                    <td>
                                        <strong>{{ shipment[1] or 'غير محدد' }}</strong>
                                    </td>
                                    <td>{{ shipment[2] or 'غير محدد' }}</td>
                                    <td>{{ shipment[3] or 'غير محدد' }}</td>
                                    <td>
                                        <span class="badge" style="background-color: {{ shipment[6] or '#6c757d' }}; color: white;">
                                            {{ shipment[5] or 'غير محدد' }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ shipment[9] or 'غير محدد' }}
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ shipment[7].strftime('%Y-%m-%d %H:%M') if shipment[7] else 'غير محدد' }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary btn-sm" 
                                                    onclick="quickReleaseUpdate({{ shipment[0] }}, '{{ shipment[4] }}')">
                                                <i class="fas fa-edit"></i>
                                                تحديث
                                            </button>
                                            <button class="btn btn-outline-info btn-sm" 
                                                    onclick="showReleaseHistory({{ shipment[0] }})">
                                                <i class="fas fa-history"></i>
                                                التاريخ
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة التحديث السريع -->
<div id="quickReleaseUpdateOverlay" class="custom-modal-overlay" style="display: none;">
    <div class="custom-modal">
        <div class="custom-modal-header">
            <h5>
                <i class="fas fa-edit me-2"></i>
                تحديث سريع للإفراج
            </h5>
            <button type="button" class="custom-close-btn" onclick="closeQuickReleaseModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="custom-modal-body">
            <div id="quickReleaseAlert" class="alert alert-info" style="display: none;">
                <i class="fas fa-info-circle me-2"></i>
                <span id="quickReleaseMessage"></span>
            </div>
            
            <form id="quickReleaseForm">
                <input type="hidden" id="quickShipmentId" name="shipment_id">
                
                <div class="mb-3">
                    <label class="form-label">حالة الإفراج الحالية</label>
                    <input type="text" class="form-control" id="currentReleaseStatus" readonly>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">حالة الإفراج الجديدة</label>
                    <select class="form-select" name="new_release_status" required>
                        <option value="">اختر الحالة الجديدة</option>
                        {% for status in release_statuses %}
                        {% if status[4] == 1 %}
                        <option value="{{ status[0] }}" style="color: {{ status[2] }}">
                            {{ status[1] }}
                        </option>
                        {% endif %}
                        {% endfor %}
                    </select>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">ملاحظات سريعة</label>
                    <textarea class="form-control" name="release_notes" rows="2" 
                              placeholder="ملاحظات اختيارية..."></textarea>
                </div>
            </form>
        </div>
        
        <div class="custom-modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeQuickReleaseModal()">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </button>
            <button type="button" class="btn btn-primary" id="submitQuickReleaseBtn" onclick="submitQuickReleaseUpdate()">
                <i class="fas fa-save me-1"></i>
                تحديث
            </button>
        </div>
    </div>
</div>

<script>
// البحث في الجدول
document.getElementById('searchInput').addEventListener('keyup', function() {
    const filter = this.value.toLowerCase();
    const rows = document.querySelectorAll('#shipmentsTable tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(filter) ? '' : 'none';
    });
});

// تحديد الكل
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.shipment-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSelectedCount();
}

// تحديث عدد المحدد
function updateSelectedCount() {
    const selected = document.querySelectorAll('.shipment-checkbox:checked').length;
    console.log(`تم تحديد ${selected} شحنة`);
}

// مراقبة تغيير التحديد
document.querySelectorAll('.shipment-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateSelectedCount);
});

// عرض تاريخ الإفراج
function showReleaseHistory(shipmentId) {
    alert('تاريخ الإفراج - قريباً\nسيعرض جميع تغييرات حالة الإفراج للشحنة');
}

// تحديث سريع للإفراج
function quickReleaseUpdate(shipmentId, currentStatus) {
    document.getElementById('quickShipmentId').value = shipmentId;
    document.getElementById('currentReleaseStatus').value = currentStatus;
    
    const overlay = document.getElementById('quickReleaseUpdateOverlay');
    overlay.style.display = 'flex';
    
    setTimeout(() => {
        overlay.classList.add('show');
    }, 10);
}

// إغلاق نافذة التحديث السريع
function closeQuickReleaseModal() {
    const overlay = document.getElementById('quickReleaseUpdateOverlay');
    overlay.classList.remove('show');
    
    setTimeout(() => {
        overlay.style.display = 'none';
    }, 300);
}

// إرسال التحديث السريع
function submitQuickReleaseUpdate() {
    const form = document.getElementById('quickReleaseForm');
    const submitBtn = document.getElementById('submitQuickReleaseBtn');
    
    const shipmentId = document.getElementById('quickShipmentId').value;
    const newStatus = form.querySelector('[name="new_release_status"]').value;
    
    if (!shipmentId || !newStatus) {
        showQuickReleaseAlert('يرجى اختيار حالة الإفراج الجديدة', 'warning');
        return;
    }
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري التحديث...';
    
    const formData = new FormData(form);
    
    fetch('/shipments/api/update-release-status', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showQuickReleaseAlert('تم تحديث حالة الإفراج بنجاح!', 'success');
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showQuickReleaseAlert('خطأ: ' + (data.message || 'فشل في تحديث الإفراج'), 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الطلب:', error);
        showQuickReleaseAlert('حدث خطأ في الاتصال بالخادم', 'danger');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-save me-1"></i> تحديث';
    });
}

// عرض رسائل التنبيه
function showQuickReleaseAlert(message, type) {
    const alertDiv = document.getElementById('quickReleaseAlert');
    const messageSpan = document.getElementById('quickReleaseMessage');
    
    alertDiv.className = `alert alert-${type}`;
    messageSpan.textContent = message;
    alertDiv.style.display = 'block';
}

// تحديث متعدد
function bulkReleaseUpdate() {
    const selected = document.querySelectorAll('.shipment-checkbox:checked');
    if (selected.length === 0) {
        alert('يرجى تحديد شحنة واحدة على الأقل');
        return;
    }
    
    alert('التحديث المتعدد للإفراج - قريباً');
}

// تحديث ذكي
function autoReleaseUpdate() {
    alert('التحديث الذكي للإفراج - قريباً\nسيقوم النظام بتحديث حالات الإفراج تلقائياً بناءً على القواعد المحددة');
}

// إعداد الأحداث
document.addEventListener('DOMContentLoaded', function() {
    const overlay = document.getElementById('quickReleaseUpdateOverlay');
    if (overlay) {
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                closeQuickReleaseModal();
            }
        });
    }
    
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const overlay = document.getElementById('quickReleaseUpdateOverlay');
            if (overlay && overlay.style.display !== 'none') {
                closeQuickReleaseModal();
            }
        }
    });
});
</script>

<style>
/* النافذة المخصصة */
.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.custom-modal-overlay.show {
    opacity: 1;
}

.custom-modal {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.7);
    transition: transform 0.3s ease;
}

.custom-modal-overlay.show .custom-modal {
    transform: scale(1);
}

.custom-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #dee2e6;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.custom-modal-header h5 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.custom-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.custom-close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.custom-modal-body {
    padding: 1.5rem;
}

.custom-modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0 0 12px 12px;
}
</style>
{% endblock %}
