// JavaScript للأجهزة المحمولة - النظام المحاسبي المتقدم
// Mobile JavaScript - Advanced Accounting System

class MobileApp {
    constructor() {
        this.isOnline = navigator.onLine;
        this.deferredPrompt = null;
        this.init();
    }

    init() {
        this.registerServiceWorker();
        this.setupPWA();
        this.setupOfflineHandling();
        this.setupTouchEvents();
        this.setupResponsiveFeatures();
        this.setupNotifications();
        this.setupDataSync();
    }

    // تسجيل Service Worker
    registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then((registration) => {
                        console.log('تم تسجيل SW بنجاح:', registration.scope);
                        
                        // التحقق من التحديثات
                        registration.addEventListener('updatefound', () => {
                            const newWorker = registration.installing;
                            newWorker.addEventListener('statechange', () => {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    this.showUpdateNotification();
                                }
                            });
                        });
                    })
                    .catch((error) => {
                        console.log('فشل تسجيل SW:', error);
                    });
            });
        }
    }

    // إعداد PWA
    setupPWA() {
        // معالجة حدث beforeinstallprompt
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('beforeinstallprompt event fired');
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallButton();
        });

        // معالجة تثبيت التطبيق
        window.addEventListener('appinstalled', () => {
            console.log('تم تثبيت التطبيق');
            this.hideInstallButton();
            this.showWelcomeMessage();
        });

        // فحص إمكانية التثبيت
        if (window.matchMedia('(display-mode: standalone)').matches) {
            console.log('التطبيق يعمل في وضع standalone');
            document.body.classList.add('standalone-mode');
        }

        // فحص دعم PWA
        this.checkPWASupport();

        // إضافة زر تثبيت يدوي للاختبار
        setTimeout(() => {
            if (!this.deferredPrompt && !this.isStandalone()) {
                this.showManualInstallButton();
            }
        }, 3000);
    }

    // فحص دعم PWA
    checkPWASupport() {
        const isSupported = 'serviceWorker' in navigator && 'PushManager' in window;
        console.log('PWA Support:', isSupported);

        if (!isSupported) {
            console.warn('PWA غير مدعوم في هذا المتصفح');
            return false;
        }

        return true;
    }

    // فحص إذا كان التطبيق يعمل في وضع standalone
    isStandalone() {
        return window.matchMedia('(display-mode: standalone)').matches ||
               window.navigator.standalone ||
               document.referrer.includes('android-app://');
    }

    // عرض زر التثبيت
    showInstallButton() {
        // إزالة أي زر تثبيت موجود
        const existingBtn = document.querySelector('.install-btn');
        if (existingBtn) {
            existingBtn.remove();
        }

        const installButton = document.createElement('button');
        installButton.className = 'btn btn-primary install-btn';
        installButton.innerHTML = '<i class="fas fa-download me-1"></i>تثبيت التطبيق';
        installButton.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            border-radius: 25px;
            padding: 10px 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            animation: pulse 2s infinite;
        `;

        installButton.addEventListener('click', () => {
            this.installApp();
        });

        document.body.appendChild(installButton);

        // إضافة CSS للأنيميشن
        if (!document.querySelector('#install-btn-styles')) {
            const style = document.createElement('style');
            style.id = 'install-btn-styles';
            style.textContent = `
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // تثبيت التطبيق
    installApp() {
        if (this.deferredPrompt) {
            this.deferredPrompt.prompt();
            this.deferredPrompt.userChoice.then((result) => {
                if (result.outcome === 'accepted') {
                    console.log('المستخدم وافق على التثبيت');
                    this.showNotification('تم التثبيت', 'تم تثبيت التطبيق بنجاح!');
                } else {
                    console.log('المستخدم رفض التثبيت');
                }
                this.deferredPrompt = null;
                this.hideInstallButton();
            });
        } else {
            // إذا لم يكن هناك deferredPrompt، اعرض تعليمات التثبيت اليدوي
            this.showManualInstallInstructions();
        }
    }

    // عرض زر التثبيت اليدوي
    showManualInstallButton() {
        if (this.isStandalone()) {
            return; // لا تعرض الزر إذا كان التطبيق مثبت بالفعل
        }

        const manualBtn = document.createElement('div');
        manualBtn.className = 'manual-install-btn alert alert-info position-fixed';
        manualBtn.style.cssText = `
            bottom: 20px;
            left: 20px;
            right: 20px;
            z-index: 999;
            border-radius: 10px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        `;
        manualBtn.innerHTML = `
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <strong>تثبيت التطبيق</strong><br>
                    <small>اضغط هنا لمعرفة كيفية تثبيت التطبيق</small>
                </div>
                <i class="fas fa-mobile-alt fa-2x text-primary"></i>
            </div>
        `;

        manualBtn.onclick = () => this.showManualInstallInstructions();
        document.body.appendChild(manualBtn);
    }

    // عرض تعليمات التثبيت اليدوي
    showManualInstallInstructions() {
        const isChrome = /Chrome/.test(navigator.userAgent);
        const isEdge = /Edg/.test(navigator.userAgent);
        const isSafari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);

        let instructions = '';

        if (isChrome || isEdge) {
            instructions = `
                <h5>تثبيت التطبيق في Chrome/Edge:</h5>
                <ol>
                    <li>اضغط على القائمة (⋮) في أعلى يمين المتصفح</li>
                    <li>اختر "تثبيت التطبيق" أو "Install App"</li>
                    <li>أو اضغط على أيقونة التثبيت في شريط العنوان</li>
                </ol>
            `;
        } else if (isSafari) {
            instructions = `
                <h5>تثبيت التطبيق في Safari:</h5>
                <ol>
                    <li>اضغط على زر المشاركة <i class="fas fa-share"></i></li>
                    <li>اختر "Add to Home Screen"</li>
                    <li>اضغط "Add" لتثبيت التطبيق</li>
                </ol>
            `;
        } else {
            instructions = `
                <h5>تثبيت التطبيق:</h5>
                <p>ابحث عن خيار "تثبيت التطبيق" أو "Add to Home Screen" في قائمة المتصفح</p>
            `;
        }

        this.showModal('تثبيت التطبيق', instructions);
    }

    // إخفاء زر التثبيت
    hideInstallButton() {
        const installBtn = document.querySelector('.install-btn');
        if (installBtn) {
            installBtn.remove();
        }
    }

    // إعداد معالجة عدم الاتصال
    setupOfflineHandling() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.showConnectionStatus('متصل', 'success');
            this.syncOfflineData();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showConnectionStatus('غير متصل', 'warning');
        });
    }

    // عرض حالة الاتصال
    showConnectionStatus(message, type) {
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-wifi me-2"></i>${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        const toastContainer = document.querySelector('.toast-container') || this.createToastContainer();
        toastContainer.appendChild(toast);

        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        setTimeout(() => toast.remove(), 3000);
    }

    // إنشاء حاوية التوست
    createToastContainer() {
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '1055';
        document.body.appendChild(container);
        return container;
    }

    // إعداد أحداث اللمس
    setupTouchEvents() {
        // تحسين النقر للأجهزة المحمولة
        document.addEventListener('touchstart', function() {}, {passive: true});

        // منع التكبير المزدوج
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);

        // إضافة دعم السحب للجداول
        this.setupTableSwipe();
    }

    // إعداد سحب الجداول
    setupTableSwipe() {
        const tables = document.querySelectorAll('.table-responsive');
        tables.forEach(table => {
            let startX = 0;
            let scrollLeft = 0;

            table.addEventListener('touchstart', (e) => {
                startX = e.touches[0].pageX - table.offsetLeft;
                scrollLeft = table.scrollLeft;
            });

            table.addEventListener('touchmove', (e) => {
                if (!startX) return;
                e.preventDefault();
                const x = e.touches[0].pageX - table.offsetLeft;
                const walk = (x - startX) * 2;
                table.scrollLeft = scrollLeft - walk;
            });

            table.addEventListener('touchend', () => {
                startX = 0;
            });
        });
    }

    // إعداد الميزات المتجاوبة
    setupResponsiveFeatures() {
        // تحسين القوائم المنسدلة للأجهزة المحمولة
        const dropdowns = document.querySelectorAll('.dropdown-toggle');
        dropdowns.forEach(dropdown => {
            dropdown.addEventListener('click', function(e) {
                if (window.innerWidth <= 768) {
                    e.stopPropagation();
                }
            });
        });

        // تحسين النماذج للأجهزة المحمولة
        this.optimizeForms();
    }

    // تحسين النماذج
    optimizeForms() {
        const inputs = document.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            // إضافة خصائص للأجهزة المحمولة
            if (input.type === 'number') {
                input.setAttribute('inputmode', 'numeric');
            } else if (input.type === 'email') {
                input.setAttribute('inputmode', 'email');
            } else if (input.type === 'tel') {
                input.setAttribute('inputmode', 'tel');
            }

            // تحسين التركيز
            input.addEventListener('focus', function() {
                if (window.innerWidth <= 768) {
                    setTimeout(() => {
                        this.scrollIntoView({behavior: 'smooth', block: 'center'});
                    }, 300);
                }
            });
        });
    }

    // إعداد الإشعارات
    setupNotifications() {
        if ('Notification' in window) {
            if (Notification.permission === 'default') {
                this.requestNotificationPermission();
            }
        }
    }

    // طلب إذن الإشعارات
    requestNotificationPermission() {
        Notification.requestPermission().then(permission => {
            if (permission === 'granted') {
                console.log('تم منح إذن الإشعارات');
                this.showNotification('مرحباً!', 'تم تفعيل الإشعارات بنجاح');
            }
        });
    }

    // عرض إشعار
    showNotification(title, body, options = {}) {
        if (Notification.permission === 'granted') {
            const notification = new Notification(title, {
                body: body,
                icon: '/static/icons/icon-192x192.png',
                badge: '/static/icons/badge-72x72.png',
                ...options
            });

            notification.onclick = function() {
                window.focus();
                notification.close();
            };

            setTimeout(() => notification.close(), 5000);
        }
    }

    // إعداد مزامنة البيانات
    setupDataSync() {
        // مزامنة البيانات عند العودة للاتصال
        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
            navigator.serviceWorker.ready.then(registration => {
                return registration.sync.register('background-sync');
            }).catch(err => {
                console.log('فشل تسجيل المزامنة:', err);
            });
        }
    }

    // مزامنة البيانات المحفوظة محلياً
    syncOfflineData() {
        const offlineData = localStorage.getItem('offlineData');
        if (offlineData) {
            const data = JSON.parse(offlineData);
            // إرسال البيانات للخادم
            this.sendDataToServer(data).then(() => {
                localStorage.removeItem('offlineData');
                this.showNotification('تم المزامنة', 'تم رفع البيانات المحفوظة محلياً');
            });
        }
    }

    // إرسال البيانات للخادم
    async sendDataToServer(data) {
        try {
            const response = await fetch('/api/sync', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            return response.json();
        } catch (error) {
            console.error('خطأ في إرسال البيانات:', error);
            throw error;
        }
    }

    // عرض رسالة الترحيب
    showWelcomeMessage() {
        this.showNotification(
            'مرحباً بك!',
            'تم تثبيت النظام المحاسبي بنجاح على جهازك'
        );
    }

    // عرض زر تثبيت يدوي
    showManualInstallButton() {
        const installButton = document.createElement('button');
        installButton.className = 'btn btn-success install-btn-manual';
        installButton.innerHTML = '<i class="fas fa-mobile-alt me-1"></i>تثبيت التطبيق';
        installButton.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 1000;
            border-radius: 25px;
            padding: 10px 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            animation: pulse 2s infinite;
        `;

        installButton.addEventListener('click', () => {
            this.showInstallInstructions();
        });

        document.body.appendChild(installButton);
    }

    // عرض تعليمات التثبيت
    showInstallInstructions() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تثبيت التطبيق</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <i class="fas fa-mobile-alt fa-3x text-primary mb-3"></i>
                        <h6>لتثبيت التطبيق على جهازك:</h6>
                        <div class="text-start mt-3">
                            <p><strong>في Chrome/Edge:</strong></p>
                            <ul>
                                <li>اضغط على القائمة (⋮)</li>
                                <li>اختر "تثبيت التطبيق" أو "Add to Home Screen"</li>
                            </ul>
                            <p><strong>في Safari (iOS):</strong></p>
                            <ul>
                                <li>اضغط على زر المشاركة</li>
                                <li>اختر "Add to Home Screen"</li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    }

    // عرض إشعار التحديث
    showUpdateNotification() {
        const updateBanner = document.createElement('div');
        updateBanner.className = 'alert alert-info alert-dismissible fade show';
        updateBanner.style.cssText = 'position: fixed; top: 0; left: 0; right: 0; z-index: 1060; margin: 0; border-radius: 0;';
        updateBanner.innerHTML = `
            <strong>تحديث متاح!</strong> يوجد إصدار جديد من التطبيق.
            <button type="button" class="btn btn-sm btn-outline-info ms-2" onclick="location.reload()">
                تحديث الآن
            </button>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.insertBefore(updateBanner, document.body.firstChild);
    }

    // عرض نافذة منبثقة
    showModal(title, content) {
        // إزالة أي نافذة منبثقة موجودة
        const existingModal = document.querySelector('.custom-modal');
        if (existingModal) {
            existingModal.remove();
        }

        const modal = document.createElement('div');
        modal.className = 'custom-modal position-fixed';
        modal.style.cssText = `
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 2000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        modal.innerHTML = `
            <div class="modal-content bg-white rounded p-4" style="max-width: 90%; max-height: 80%; overflow-y: auto;">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="mb-0">${title}</h4>
                    <button class="btn-close" onclick="this.closest('.custom-modal').remove()"></button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                <div class="text-end mt-3">
                    <button class="btn btn-secondary" onclick="this.closest('.custom-modal').remove()">إغلاق</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // إغلاق النافذة عند الضغط خارجها
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.mobileApp = new MobileApp();
});

// تصدير للاستخدام العام
window.MobileApp = MobileApp;
