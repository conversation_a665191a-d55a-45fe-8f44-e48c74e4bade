{% extends "base.html" %}

{% block content %}
<div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
    <div class="text-center">
        <div class="error-code mb-4">
            <h1 class="display-1 fw-bold text-danger">500</h1>
        </div>
        
        <div class="error-message mb-4">
            <h2 class="h3 mb-3">خطأ في الخادم</h2>
            <p class="lead text-muted mb-4">
                عذراً، حدث خطأ غير متوقع في الخادم. نحن نعمل على حل هذه المشكلة.
            </p>
        </div>
        
        <div class="error-illustration mb-4">
            <i class="fas fa-exclamation-triangle fa-5x text-warning opacity-75"></i>
        </div>
        
        <div class="error-actions">
            <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                <button onclick="location.reload()" class="btn btn-primary btn-lg">
                    <i class="fas fa-sync-alt me-2"></i>
                    إعادة المحاولة
                </button>
                
                <a href="{{ url_for('main.index') }}" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-home me-2"></i>
                    العودة للرئيسية
                </a>
                
                {% if current_user.is_authenticated %}
                    <a href="{{ url_for('main.dashboard') }}" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة المعلومات
                    </a>
                {% endif %}
            </div>
        </div>
        
        <div class="error-help mt-5">
            <div class="card border-0 bg-light">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-info-circle me-2"></i>
                        ماذا يمكنك فعله؟
                    </h6>
                    <ul class="list-unstyled mb-0 text-start">
                        <li class="mb-2">
                            <i class="fas fa-redo text-primary me-2"></i>
                            انتظر قليلاً ثم أعد المحاولة
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-refresh text-primary me-2"></i>
                            حدث الصفحة أو امسح ذاكرة التخزين المؤقت
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-bug text-primary me-2"></i>
                            أبلغ عن المشكلة إذا استمرت
                        </li>
                        <li>
                            <i class="fas fa-headset text-primary me-2"></i>
                            تواصل مع الدعم الفني للمساعدة
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="error-contact mt-4">
            <div class="card border-warning bg-warning bg-opacity-10">
                <div class="card-body">
                    <h6 class="card-title text-warning">
                        <i class="fas fa-envelope me-2"></i>
                        تحتاج مساعدة؟
                    </h6>
                    <p class="card-text mb-3">
                        إذا استمرت هذه المشكلة، يرجى التواصل مع فريق الدعم الفني
                    </p>
                    <div class="d-flex gap-2 justify-content-center">
                        <a href="mailto:<EMAIL>" class="btn btn-warning btn-sm">
                            <i class="fas fa-envelope me-1"></i>
                            إرسال بريد إلكتروني
                        </a>
                        <a href="tel:+966501234567" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-phone me-1"></i>
                            اتصال هاتفي
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add animation to error code
    const errorCode = document.querySelector('.error-code h1');
    errorCode.style.opacity = '0';
    errorCode.style.transform = 'scale(0.5)';
    errorCode.style.transition = 'all 0.5s ease';
    
    setTimeout(() => {
        errorCode.style.opacity = '1';
        errorCode.style.transform = 'scale(1)';
    }, 100);
    
    // Add shake animation to illustration
    const illustration = document.querySelector('.error-illustration i');
    illustration.style.animation = 'shake 1s ease-in-out infinite';
    
    // Auto-retry functionality
    let retryCount = 0;
    const maxRetries = 3;
    
    function autoRetry() {
        if (retryCount < maxRetries) {
            retryCount++;
            setTimeout(() => {
                location.reload();
            }, 5000 * retryCount); // Increasing delay
        }
    }
    
    // Show retry countdown
    function showRetryCountdown() {
        const retryButton = document.querySelector('.btn-primary');
        let countdown = 10;
        
        const interval = setInterval(() => {
            retryButton.innerHTML = `<i class="fas fa-sync-alt me-2"></i>إعادة المحاولة (${countdown})`;
            countdown--;
            
            if (countdown < 0) {
                clearInterval(interval);
                retryButton.innerHTML = '<i class="fas fa-sync-alt me-2"></i>إعادة المحاولة';
                location.reload();
            }
        }, 1000);
    }
    
    // Start countdown after 5 seconds
    setTimeout(showRetryCountdown, 5000);
    
    // Add CSS for shake animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
    `;
    document.head.appendChild(style);
});

// Error reporting function
function reportError() {
    const errorData = {
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        userId: '{{ current_user.id if current_user.is_authenticated else "anonymous" }}'
    };
    
    // Send error report to server
    fetch('/api/error-report', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorData)
    }).catch(err => {
        console.error('Failed to send error report:', err);
    });
}

// Automatically report error
reportError();
</script>

<style>
.error-code h1 {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.error-message h2 {
    color: #495057;
}

.error-actions .btn {
    min-width: 180px;
}

@media (max-width: 576px) {
    .error-code h1 {
        font-size: 6rem;
    }
    
    .error-actions .btn {
        min-width: auto;
        width: 100%;
    }
}
</style>
{% endblock %}
