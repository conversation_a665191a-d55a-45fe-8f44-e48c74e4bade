/**
 * نظام التكامل بين أوامر الشراء والموردين والحوالات - JavaScript
 * Purchase Orders Integration System - JavaScript
 */

// متغيرات عامة
let integrationData = {
    outstandingOrders: [],
    payments: [],
    suppliers: [],
    moneyChangers: [],
    dashboardStats: {}
};

/**
 * تحميل بيانات لوحة المعلومات
 */
function loadDashboardData() {
    // محاولة تحميل البيانات من API الأساسي
    $.ajax({
        url: '/suppliers/api/purchase-orders/dashboard-stats',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                updateDashboardStats(response.dashboard_stats);
                integrationData.dashboardStats = response.dashboard_stats;
                loadOutstandingOrders();
                loadPaymentsTracking();

                // إظهار رسالة تحذير إذا كان هناك خطأ
                if (response.error_message) {
                    console.warn('تحذير:', response.error_message);
                }
            } else {
                // في حالة فشل API الأساسي، استخدم API البديل
                loadDashboardDataFallback();
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في تحميل بيانات لوحة المعلومات:', error);
            // محاولة استخدام API البديل
            loadDashboardDataFallback();
        }
    });
}

/**
 * تحميل بيانات لوحة المعلومات - API بديل
 */
function loadDashboardDataFallback() {
    $.ajax({
        url: '/suppliers/api/purchase-orders/dashboard-stats-simple',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                updateDashboardStats(response.dashboard_stats);
                integrationData.dashboardStats = response.dashboard_stats;
                showNotification('تم تحميل البيانات الأساسية بنجاح', 'info');
            } else {
                // استخدام بيانات افتراضية
                loadDefaultDashboardData();
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في API البديل:', error);
            // استخدام بيانات افتراضية
            loadDefaultDashboardData();
        }
    });
}

/**
 * تحميل بيانات افتراضية
 */
function loadDefaultDashboardData() {
    const defaultStats = {
        general_statistics: {
            total_orders: 0,
            paid_orders: 0,
            pending_orders: 0,
            overdue_orders: 0
        },
        supplier_statistics: {
            active_suppliers: 0,
            avg_outstanding: 0
        }
    };

    updateDashboardStats(defaultStats);
    integrationData.dashboardStats = defaultStats;
    showNotification('تم تحميل البيانات الافتراضية', 'warning');
}

/**
 * تحديث إحصائيات لوحة المعلومات
 */
function updateDashboardStats(stats) {
    $('#totalOrders').text(stats.general_statistics.total_orders || 0);
    $('#paidOrders').text(stats.general_statistics.paid_orders || 0);
    $('#pendingOrders').text(stats.general_statistics.pending_orders || 0);
    $('#overdueOrders').text(stats.general_statistics.overdue_orders || 0);
}

/**
 * تحميل أوامر الشراء المستحقة
 */
function loadOutstandingOrders() {
    const supplierFilter = $('#supplierFilter').val();
    
    // إذا لم يتم اختيار مورد، نحمل جميع الأوامر المستحقة
    if (!supplierFilter) {
        loadAllOutstandingOrders();
        return;
    }
    
    $.ajax({
        url: `/api/suppliers/purchase-orders/outstanding/${supplierFilter}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                integrationData.outstandingOrders = response.outstanding_orders;
                populateOutstandingOrdersTable(response.outstanding_orders);
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في تحميل أوامر الشراء المستحقة:', error);
            showNotification('خطأ في تحميل أوامر الشراء المستحقة', 'error');
        }
    });
}

/**
 * تحميل جميع أوامر الشراء المستحقة
 */
function loadAllOutstandingOrders() {
    // نحتاج إلى API جديد لجلب جميع الأوامر المستحقة
    // سنستخدم مؤقتاً نفس API مع تمرير معرف فارغ
    outstandingOrdersTable.clear();
    
    // هنا يمكن إضافة API للحصول على جميع الأوامر المستحقة
    // أو تكرار الاستدعاء لكل مورد
    
    outstandingOrdersTable.draw();
}

/**
 * ملء جدول أوامر الشراء المستحقة
 */
function populateOutstandingOrdersTable(orders) {
    outstandingOrdersTable.clear();
    
    orders.forEach(order => {
        const statusBadge = getPaymentStatusBadge(order.due_status);
        const priorityBadge = getPriorityBadge(order.payment_priority);
        
        const actionsHtml = `
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-primary" onclick="viewPurchaseOrderDetails(${order.purchase_order_id})" 
                        title="عرض التفاصيل">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-outline-success" onclick="createPaymentForOrder(${order.purchase_order_id})" 
                        title="إنشاء دفعة">
                    <i class="fas fa-money-bill-wave"></i>
                </button>
                <button class="btn btn-outline-info" onclick="trackOrderPayments(${order.purchase_order_id})" 
                        title="تتبع المدفوعات">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        `;
        
        outstandingOrdersTable.row.add([
            order.po_number,
            order.supplier_name || 'غير محدد',
            order.title || '-',
            order.payment_due_date || '-',
            `<span class="amount-display">${formatCurrency(order.total_amount_due)} ${order.currency}</span>`,
            `<span class="amount-display paid-amount">${formatCurrency(order.paid_amount)} ${order.currency}</span>`,
            `<span class="amount-display outstanding-amount">${formatCurrency(order.outstanding_amount)} ${order.currency}</span>`,
            statusBadge,
            priorityBadge,
            actionsHtml
        ]);
    });
    
    outstandingOrdersTable.draw();
}

/**
 * تحميل تتبع المدفوعات
 */
function loadPaymentsTracking() {
    // سنحتاج إلى API جديد لجلب جميع المدفوعات
    // مؤقتاً سنترك الجدول فارغ
    paymentsTrackingTable.clear().draw();
}

/**
 * تحميل خيارات الموردين
 */
function loadSupplierOptions() {
    $.ajax({
        url: '/api/suppliers/suppliers',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                integrationData.suppliers = response.suppliers;
                populateSupplierOptions(response.suppliers);
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في تحميل قائمة الموردين:', error);
        }
    });
}

/**
 * ملء خيارات الموردين
 */
function populateSupplierOptions(suppliers) {
    const supplierFilter = $('#supplierFilter');
    const paymentSupplier = $('#paymentSupplier');
    
    // تنظيف الخيارات الموجودة
    supplierFilter.find('option:not(:first)').remove();
    paymentSupplier.find('option:not(:first)').remove();
    
    suppliers.forEach(supplier => {
        const option = `<option value="${supplier.id}">${supplier.name_ar} (${supplier.supplier_code})</option>`;
        supplierFilter.append(option);
        paymentSupplier.append(option);
    });
}

/**
 * تحميل خيارات الصرافين والبنوك
 */
function loadMoneyChangerOptions() {
    $.ajax({
        url: '/api/money-changers-banks',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                integrationData.moneyChangers = response.money_changers_banks;
                populateMoneyChangerOptions(response.money_changers_banks);
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في تحميل قائمة الصرافين والبنوك:', error);
        }
    });
}

/**
 * ملء خيارات الصرافين والبنوك
 */
function populateMoneyChangerOptions(moneyChangers) {
    const paymentMoneyChanger = $('#paymentMoneyChanger');
    paymentMoneyChanger.find('option:not(:first)').remove();
    
    moneyChangers.forEach(mc => {
        const option = `<option value="${mc.id}">${mc.name_ar} (${mc.type})</option>`;
        paymentMoneyChanger.append(option);
    });
}

/**
 * عرض modal إنشاء طلب دفع
 */
function showCreatePaymentModal() {
    selectedPurchaseOrders = [];
    $('#createPaymentForm')[0].reset();
    $('#outstandingOrdersContainer').html(`
        <div class="text-center py-3 text-muted">
            اختر المورد أولاً لعرض أوامر الشراء المستحقة
        </div>
    `);
    $('#createPaymentModal').modal('show');
}

/**
 * تحميل أوامر الشراء المستحقة للمورد المختار
 */
function loadSupplierOutstandingOrders() {
    const supplierId = $('#paymentSupplier').val();
    if (!supplierId) {
        $('#outstandingOrdersContainer').html(`
            <div class="text-center py-3 text-muted">
                اختر المورد أولاً لعرض أوامر الشراء المستحقة
            </div>
        `);
        return;
    }
    
    $('#outstandingOrdersContainer').html(`
        <div class="text-center py-3">
            <i class="fas fa-spinner fa-spin"></i> جاري تحميل أوامر الشراء المستحقة...
        </div>
    `);
    
    $.ajax({
        url: `/api/suppliers/purchase-orders/outstanding/${supplierId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                displaySupplierOutstandingOrders(response.outstanding_orders);
            } else {
                $('#outstandingOrdersContainer').html(`
                    <div class="text-center py-3 text-danger">
                        خطأ في تحميل أوامر الشراء: ${response.message}
                    </div>
                `);
            }
        },
        error: function(xhr, status, error) {
            $('#outstandingOrdersContainer').html(`
                <div class="text-center py-3 text-danger">
                    خطأ في تحميل أوامر الشراء المستحقة
                </div>
            `);
        }
    });
}

/**
 * عرض أوامر الشراء المستحقة للمورد
 */
function displaySupplierOutstandingOrders(orders) {
    if (orders.length === 0) {
        $('#outstandingOrdersContainer').html(`
            <div class="text-center py-3 text-muted">
                لا توجد أوامر شراء مستحقة لهذا المورد
            </div>
        `);
        return;
    }
    
    let html = '';
    orders.forEach(order => {
        const isSelected = selectedPurchaseOrders.some(po => po.purchase_order_id === order.purchase_order_id);
        const cardClass = isSelected ? 'po-selection-card selected' : 'po-selection-card';
        
        html += `
            <div class="${cardClass}" onclick="togglePurchaseOrderSelection(${order.purchase_order_id})">
                <div class="row">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center">
                            <input type="checkbox" class="form-check-input me-2" 
                                   ${isSelected ? 'checked' : ''} 
                                   onchange="togglePurchaseOrderSelection(${order.purchase_order_id})">
                            <div>
                                <h6 class="mb-1">${order.po_number} - ${order.title || 'بدون عنوان'}</h6>
                                <small class="text-muted">
                                    تاريخ الاستحقاق: ${order.payment_due_date || 'غير محدد'} | 
                                    ${getPaymentStatusBadge(order.due_status)}
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="mb-1">
                            <strong class="outstanding-amount">
                                ${formatCurrency(order.outstanding_amount)} ${order.currency}
                            </strong>
                        </div>
                        <small class="text-muted">
                            من أصل ${formatCurrency(order.total_amount_due)} ${order.currency}
                        </small>
                        <div class="mt-2">
                            <input type="number" class="form-control form-control-sm" 
                                   placeholder="مبلغ الدفعة" 
                                   max="${order.outstanding_amount}"
                                   onchange="updatePurchaseOrderAmount(${order.purchase_order_id}, this.value)"
                                   onclick="event.stopPropagation()">
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    $('#outstandingOrdersContainer').html(html);
}

/**
 * تبديل اختيار أمر الشراء
 */
function togglePurchaseOrderSelection(purchaseOrderId) {
    const existingIndex = selectedPurchaseOrders.findIndex(po => po.purchase_order_id === purchaseOrderId);
    
    if (existingIndex > -1) {
        // إزالة من الاختيار
        selectedPurchaseOrders.splice(existingIndex, 1);
    } else {
        // إضافة للاختيار
        const orderData = integrationData.outstandingOrders?.find(o => o.purchase_order_id === purchaseOrderId);
        if (orderData) {
            selectedPurchaseOrders.push({
                purchase_order_id: purchaseOrderId,
                payment_amount: orderData.outstanding_amount,
                currency: orderData.currency,
                payment_type: 'FULL'
            });
        }
    }
    
    // تحديث العرض
    loadSupplierOutstandingOrders();
    calculatePaymentTotals();
}

/**
 * تحديث مبلغ أمر الشراء
 */
function updatePurchaseOrderAmount(purchaseOrderId, amount) {
    const orderIndex = selectedPurchaseOrders.findIndex(po => po.purchase_order_id === purchaseOrderId);
    if (orderIndex > -1) {
        selectedPurchaseOrders[orderIndex].payment_amount = parseFloat(amount) || 0;
        selectedPurchaseOrders[orderIndex].payment_type = 
            amount >= selectedPurchaseOrders[orderIndex].outstanding_amount ? 'FULL' : 'PARTIAL';
        calculatePaymentTotals();
    }
}

/**
 * حساب إجماليات الدفع
 */
function calculatePaymentTotals() {
    const totalAmount = selectedPurchaseOrders.reduce((sum, po) => sum + po.payment_amount, 0);
    const discountAmount = parseFloat($('#paymentDiscount').val()) || 0;
    const taxAmount = parseFloat($('#paymentTax').val()) || 0;
    const netAmount = totalAmount - discountAmount - taxAmount;
    
    $('#paymentNetAmount').val(netAmount.toFixed(2));
}

/**
 * إنشاء طلب الدفع
 */
function createPaymentRequest() {
    if (selectedPurchaseOrders.length === 0) {
        showNotification('يرجى اختيار أمر شراء واحد على الأقل', 'warning');
        return;
    }
    
    const formData = {
        supplier_id: $('#paymentSupplier').val(),
        purchase_orders: selectedPurchaseOrders,
        money_changer_id: $('#paymentMoneyChanger').val(),
        payment_purpose: $('#paymentPurpose').val(),
        notes: $('#paymentNotes').val(),
        discount_amount: parseFloat($('#paymentDiscount').val()) || 0,
        tax_amount: parseFloat($('#paymentTax').val()) || 0
    };
    
    // التحقق من البيانات المطلوبة
    if (!formData.supplier_id || !formData.money_changer_id || !formData.payment_purpose) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }
    
    $.ajax({
        url: '/api/suppliers/purchase-orders/create-payment-request',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                showNotification('تم إنشاء طلب الدفع بنجاح', 'success');
                $('#createPaymentModal').modal('hide');
                refreshDashboard();
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في إنشاء طلب الدفع:', error);
            const errorMessage = xhr.responseJSON?.message || 'خطأ في إنشاء طلب الدفع';
            showNotification(errorMessage, 'error');
        }
    });
}

// دوال مساعدة
function getPaymentStatusBadge(status) {
    const statusMap = {
        'مدفوع': { class: 'status-paid', text: 'مدفوع' },
        'جزئي': { class: 'status-partial', text: 'جزئي' },
        'معلق': { class: 'status-pending', text: 'معلق' },
        'متأخر': { class: 'status-overdue', text: 'متأخر' },
        'مستحق قريباً': { class: 'status-pending', text: 'مستحق قريباً' },
        'مستحق': { class: 'status-pending', text: 'مستحق' }
    };
    
    const statusInfo = statusMap[status] || { class: 'status-pending', text: status };
    return `<span class="badge payment-status-badge ${statusInfo.class}">${statusInfo.text}</span>`;
}

function getPriorityBadge(priority) {
    const priorityMap = {
        'عاجل جداً': { class: 'priority-urgent', text: 'عاجل جداً' },
        'عاجل': { class: 'priority-high', text: 'عاجل' },
        'مهم': { class: 'priority-medium', text: 'مهم' },
        'عادي': { class: 'priority-normal', text: 'عادي' }
    };
    
    const priorityInfo = priorityMap[priority] || { class: 'priority-normal', text: priority };
    return `<span class="badge priority-badge ${priorityInfo.class}">${priorityInfo.text}</span>`;
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount || 0);
}

function filterOutstandingOrders() {
    loadOutstandingOrders();
}

function refreshDashboard() {
    loadDashboardData();
}

function showNotification(message, type = 'info') {
    // استخدام نظام الإشعارات الموجود في التطبيق
    if (typeof showAlert === 'function') {
        showAlert(message, type);
    } else {
        alert(message);
    }
}

// دوال إضافية سيتم تطويرها
function viewPurchaseOrderDetails(purchaseOrderId) {
    // سيتم تطويرها لاحقاً
    console.log('عرض تفاصيل أمر الشراء:', purchaseOrderId);
}

function createPaymentForOrder(purchaseOrderId) {
    // سيتم تطويرها لاحقاً
    console.log('إنشاء دفعة لأمر الشراء:', purchaseOrderId);
}

function trackOrderPayments(purchaseOrderId) {
    // سيتم تطويرها لاحقاً
    console.log('تتبع مدفوعات أمر الشراء:', purchaseOrderId);
}
