{% extends "base.html" %}

{% block title %}إدارة أوامر الشراء{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/voice-search.css') }}" rel="stylesheet">
<style>
    /* 🎨 التصميم الحديث الكامل */
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        min-height: 100vh !important;
    }
    
    .modern-container {
        padding: 2rem 0;
        background: transparent;
    }
    
    .modern-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .modern-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        color: white;
    }
    
    .modern-header p {
        font-size: 1.1rem;
        opacity: 0.9;
        font-weight: 400;
        color: white;
        margin-bottom: 0;
    }
    
    .modern-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .modern-card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border-left: 4px solid #667eea;
        position: relative;
        overflow: hidden;
        min-height: 120px;
        cursor: pointer;
    }
    
    .modern-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    }
    
    .modern-card:nth-child(1) { border-left-color: #28a745; }
    .modern-card:nth-child(2) { border-left-color: #ffc107; }
    .modern-card:nth-child(3) { border-left-color: #dc3545; }
    .modern-card:nth-child(4) { border-left-color: #17a2b8; }
    
    .modern-card .number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        display: block;
    }
    
    .modern-card .label {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .modern-panel {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
    }
    
    .modern-table {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
    }
    
    .modern-table table {
        width: 100%;
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
    }
    
    .modern-table thead th {
        background: linear-gradient(135deg, #343a40 0%, #495057 100%);
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.85rem;
        padding: 1rem;
        border: none;
    }
    
    .modern-table tbody tr {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border-bottom: 1px solid #e9ecef;
    }
    
    .modern-table tbody tr:hover {
        background: rgba(102, 126, 234, 0.05);
        transform: scale(1.01);
    }
    
    .modern-table tbody td {
        padding: 1rem;
        vertical-align: middle;
        border: none;
        font-size: 0.9rem;
    }
    
    .modern-form-control {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 0.75rem 1rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: white;
    }
    
    .modern-form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }
    
    .modern-btn {
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: none;
    }
    
    .modern-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .modern-breadcrumb {
        background: white;
        border-radius: 12px;
        padding: 1rem 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        margin-bottom: 2rem;
    }
    
    .modern-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    @media (max-width: 768px) {
        .modern-header h1 {
            font-size: 2rem;
        }
        
        .modern-card {
            margin-bottom: 1rem;
            min-height: 100px;
        }
        
        .modern-card .number {
            font-size: 1.8rem;
        }
        
        .modern-panel {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="modern-container">
    <div class="container-fluid px-3">
        <!-- Modern Header -->
        <div class="modern-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <div class="me-4">
                            <i class="fas fa-shopping-cart fa-4x opacity-75"></i>
                        </div>
                        <div>
                            <h1>إدارة أوامر الشراء</h1>
                            <p>إدارة شاملة لجميع أوامر الشراء والمشتريات من الموردين</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex justify-content-end align-items-center">
                        <div class="me-3">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="h4 mb-0 fw-bold">{{ stats.total_count if stats else 0 }}</div>
                                    <small class="opacity-75">إجمالي الأوامر</small>
                                </div>
                                <div class="col-6">
                                    <div class="h4 mb-0 fw-bold">{{ stats.confirmed_count if stats else 0 }}</div>
                                    <small class="opacity-75">مؤكدة</small>
                                </div>
                            </div>
                        </div>
                        <div class="vr opacity-50 me-3"></div>
                        <div>
                            <a href="{{ url_for('purchase_orders.new') }}" class="btn btn-light modern-btn">
                                <i class="fas fa-plus me-2"></i>إضافة أمر شراء جديد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb modern-breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{{ url_for('main.dashboard') }}" class="text-decoration-none text-primary">
                        <i class="fas fa-home me-1"></i>الرئيسية
                    </a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{{ url_for('main.dashboard') }}" class="text-decoration-none text-primary">
                        <i class="fas fa-cogs me-1"></i>إدارة النظام
                    </a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">
                    <i class="fas fa-shopping-cart me-1"></i>إدارة أوامر الشراء
                </li>
            </ol>
        </nav>

        <!-- Statistics Cards -->
        <div class="modern-stats">
            <div class="modern-card">
                <span class="number">{{ stats.total_count if stats else 0 }}</span>
                <div class="label">إجمالي الأوامر</div>
            </div>
            <div class="modern-card">
                <span class="number">{{ stats.draft_count if stats else 0 }}</span>
                <div class="label">المسودات</div>
            </div>
            <div class="modern-card">
                <span class="number">{{ stats.confirmed_count if stats else 0 }}</span>
                <div class="label">المؤكدة</div>
            </div>
            <div class="modern-card">
                <span class="number">{{ stats.used_count if stats else 0 }}</span>
                <div class="label">المُستخدمة</div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="modern-panel">
            <div class="row align-items-end">
                <div class="col-md-4">
                    <label class="form-label fw-bold">البحث السريع</label>
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" id="searchInput" class="form-control modern-form-control"
                               placeholder="البحث برقم الأمر، اسم المورد، أو المبلغ..."
                               onkeyup="performQuickSearch()">
                        <button type="button" class="btn btn-outline-primary voice-search-btn" id="voiceSearchBtn" title="البحث الصوتي">
                            <i class="fas fa-microphone text-primary"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">الحالة</label>
                    <select class="form-select modern-form-control" id="statusFilter" onchange="applyFilters()">
                        <option value="">جميع الحالات</option>
                        <option value="مسودة">مسودة</option>
                        <option value="مرسل">مرسل</option>
                        <option value="مؤكد">مؤكد</option>
                        <option value="ملغي">ملغي</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">من تاريخ</label>
                    <input type="date" class="form-control modern-form-control" id="dateFrom" onchange="applyFilters()">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">إلى تاريخ</label>
                    <input type="date" class="form-control modern-form-control" id="dateTo" onchange="applyFilters()">
                </div>
                <div class="col-md-2">
                    <div class="d-flex gap-2">
                        <button class="btn btn-secondary modern-btn" onclick="resetFilters()" title="إعادة تعيين">
                            <i class="fas fa-undo"></i>
                        </button>
                        <button class="btn btn-primary modern-btn" onclick="location.reload()" title="تحديث">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button type="button" class="btn btn-success modern-btn" onclick="exportData()" title="تصدير">
                            <i class="fas fa-file-excel"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Table -->
        <div class="modern-table">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>رقم الأمر</th>
                            <th>رقم العقد</th>
                            <th>المورد</th>
                            <th>تاريخ الأمر</th>
                            <th>تاريخ التسليم</th>
                            <th>القيمة الإجمالية</th>
                            <th>الحالة</th>
                            <th>مُستخدم</th>
                            <th>العمليات</th>
                        </tr>
                    </thead>
                    <tbody id="purchaseOrdersTableBody">
                        {% for po in purchase_orders %}
                        <tr>
                            <td><strong class="text-primary">{{ po[1] }}</strong></td>
                            <td>
                                {% if po[11] %}
                                    <span class="badge bg-info modern-badge">{{ po[11] }}</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>{{ po[2] or '-' }}</td>
                            <td>{{ po[3].strftime('%Y-%m-%d') if po[3] else '-' }}</td>
                            <td>{{ po[4].strftime('%Y-%m-%d') if po[4] else '-' }}</td>
                            <td>
                                <strong class="text-success">
                                    {{ "{:,.2f}".format(po[6]) if po[6] else '0.00' }}
                                    {{ po[13] if po[13] else po[7] or '' }}
                                </strong>
                            </td>
                            <td>
                                {% set status_class = 'secondary' %}
                                {% if po[5] == 'مؤكد' %}
                                    {% set status_class = 'success' %}
                                {% elif po[5] == 'مرسل' %}
                                    {% set status_class = 'info' %}
                                {% elif po[5] == 'ملغي' %}
                                    {% set status_class = 'danger' %}
                                {% endif %}
                                <span class="badge bg-{{ status_class }} modern-badge">{{ po[5] or 'مسودة' }}</span>
                            </td>
                            <td>
                                {% if po[8] %}
                                    <span class="badge bg-success modern-badge">مُستخدم</span>
                                {% else %}
                                    <span class="badge bg-secondary modern-badge">غير مُستخدم</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('purchase_orders.view', po_id=po[0]) }}" class="btn btn-outline-info btn-sm" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('purchase_orders.edit', po_id=po[0]) }}" class="btn btn-outline-warning btn-sm" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="/purchase-orders/{{ po[0] }}/documents" class="btn btn-outline-primary btn-sm" title="إدارة الوثائق">
                                        <i class="fas fa-file-alt"></i>
                                    </a>
                                    <button onclick="deletePO({{ po[0] }})" class="btn btn-outline-danger btn-sm" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎨 تم تحميل التصميم الحديث بنجاح!');
    
    // إضافة تفاعل للبطاقات
    const cards = document.querySelectorAll('.modern-card');
    cards.forEach(card => {
        card.addEventListener('click', function() {
            alert('تفاصيل الإحصائيات - سيتم تطويرها قريباً');
        });
    });
});

// دوال البحث والفلترة
function performQuickSearch() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const tableBody = document.getElementById('purchaseOrdersTableBody');
    const rows = tableBody.getElementsByTagName('tr');

    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const text = row.textContent.toLowerCase();

        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    }
}

function applyFilters() {
    const statusFilter = document.getElementById('statusFilter').value;
    const dateFrom = document.getElementById('dateFrom').value;
    const dateTo = document.getElementById('dateTo').value;
    const tableBody = document.getElementById('purchaseOrdersTableBody');
    const rows = tableBody.getElementsByTagName('tr');

    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        let showRow = true;

        // فلتر الحالة
        if (statusFilter) {
            const statusCell = row.cells[6]; // عمود الحالة
            if (statusCell && !statusCell.textContent.includes(statusFilter)) {
                showRow = false;
            }
        }

        // فلتر التاريخ
        if (dateFrom || dateTo) {
            const dateCell = row.cells[3]; // عمود تاريخ الأمر
            if (dateCell) {
                const rowDate = dateCell.textContent.trim();
                if (dateFrom && rowDate < dateFrom) showRow = false;
                if (dateTo && rowDate > dateTo) showRow = false;
            }
        }

        row.style.display = showRow ? '' : 'none';
    }
}

function exportData() {
    // تصدير البيانات إلى Excel
    const table = document.querySelector('.modern-table table');
    const rows = Array.from(table.rows);

    let csvContent = "data:text/csv;charset=utf-8,";

    rows.forEach(row => {
        const cells = Array.from(row.cells);
        const rowData = cells.map(cell => cell.textContent.trim()).join(',');
        csvContent += rowData + "\n";
    });

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "purchase_orders.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function deletePO(id) {
    if (confirm('هل أنت متأكد من حذف هذا الأمر؟\n\nهذا الإجراء لا يمكن التراجع عنه.')) {
        fetch(`/purchase_orders/delete/${id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء الحذف: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الحذف');
        });
    }
}

// دالة إعادة تعيين الفلاتر
function resetFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('dateFrom').value = '';
    document.getElementById('dateTo').value = '';

    const tableBody = document.getElementById('purchaseOrdersTableBody');
    const rows = tableBody.getElementsByTagName('tr');

    for (let i = 0; i < rows.length; i++) {
        rows[i].style.display = '';
    }
}

// تهيئة البحث الصوتي
document.addEventListener('DOMContentLoaded', function() {
    // إنشاء البحث الصوتي مع الزر الموجود
    const searchInput = document.getElementById('searchInput');
    const voiceButton = document.getElementById('voiceSearchBtn');

    if (searchInput && voiceButton) {
        const voiceSearch = new VoiceSearch({
            searchInput: searchInput,
            searchButton: voiceButton,
            onResult: function(text, confidence) {
                console.log('🎤 نتيجة البحث الصوتي:', text);
                searchInput.value = text;
                performQuickSearch(); // تطبيق البحث فوراً
            },
            onError: function(error) {
                console.error('خطأ في البحث الصوتي:', error);
            }
        });
    }
});
</script>

<!-- Voice Search Script -->
<script src="{{ url_for('static', filename='js/voice-search.js') }}"></script>
{% endblock %}
