<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل قاعدة الأتمتة - النظام المحاسبي المتقدم</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .edit-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .edit-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .edit-header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .edit-header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .edit-body {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
            display: block;
        }

        .form-control, .form-select {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn-group-custom {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn-custom {
            padding: 12px 30px;
            border-radius: 12px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary-custom {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary-custom:hover {
            background: #cbd5e0;
            transform: translateY(-2px);
        }

        .form-check {
            margin-top: 15px;
        }

        .form-check-input {
            width: 20px;
            height: 20px;
            margin-left: 10px;
        }

        .form-check-label {
            font-weight: 500;
            color: #4a5568;
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
        }

        @media (max-width: 768px) {
            .edit-container {
                margin: 0 15px;
            }
            
            .edit-body {
                padding: 20px;
            }
            
            .btn-group-custom {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="edit-container">
        <!-- Header -->
        <div class="edit-header">
            <h1><i class="fas fa-edit me-3"></i>تعديل قاعدة الأتمتة</h1>
            <p>قم بتعديل إعدادات قاعدة الأتمتة حسب احتياجاتك</p>
        </div>

        <!-- Body -->
        <div class="edit-body">
            <!-- عرض الرسائل -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }}">
                            <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- نموذج التعديل -->
            <form method="POST" action="{{ url_for('shipments.update_automation_rule', rule_id=rule.id) }}">
                <!-- اسم القاعدة -->
                <div class="form-group">
                    <label for="rule_name" class="form-label">
                        <i class="fas fa-tag me-2"></i>اسم القاعدة
                    </label>
                    <input type="text" class="form-control" id="rule_name" name="rule_name" 
                           value="{{ rule.rule_name }}" required>
                </div>

                <!-- نوع القاعدة -->
                <div class="form-group">
                    <label for="rule_type" class="form-label">
                        <i class="fas fa-cogs me-2"></i>نوع القاعدة
                    </label>
                    <select class="form-select" id="rule_type" name="rule_type" required onchange="updateRuleFields()">
                        <option value="">اختر نوع القاعدة</option>
                        <option value="SIMPLE_RULE" {{ 'selected' if rule.rule_type == 'SIMPLE_RULE' else '' }}>قاعدة بسيطة (شرط + إجراء)</option>
                        <option value="CONDITION_ONLY" {{ 'selected' if rule.rule_type == 'CONDITION_ONLY' else '' }}>شرط فقط (للمراقبة)</option>
                        <option value="ACTION_ONLY" {{ 'selected' if rule.rule_type == 'ACTION_ONLY' else '' }}>إجراء فقط (تنفيذ مباشر)</option>
                        <option value="SCHEDULED" {{ 'selected' if rule.rule_type == 'SCHEDULED' else '' }}>قاعدة مجدولة (مبنية على الوقت)</option>
                    </select>
                    <small class="form-text text-muted mt-2">
                        <span id="rule_type_description"></span>
                    </small>
                </div>

                <!-- شرط التشغيل -->
                <div class="form-group" id="trigger_condition_section">
                    <label for="trigger_condition" class="form-label">
                        <i class="fas fa-play-circle me-2"></i>شرط التشغيل
                    </label>
                    <select class="form-select" id="trigger_condition" name="trigger_condition" required onchange="updateConditionValue()">
                        <option value="">اختر نوع القاعدة أولاً</option>
                        <!-- سيتم تحميل الخيارات ديناميكياً حسب نوع القاعدة -->
                    </select>
                    <small class="form-text text-muted">الشرط الذي يؤدي لتشغيل القاعدة</small>
                </div>

                <!-- نوع الإجراء -->
                <div class="form-group" id="action_section">
                    <label for="action_type" class="form-label">
                        <i class="fas fa-bolt me-2"></i>نوع الإجراء
                    </label>
                    <select class="form-select" id="action_type" name="action_type" required onchange="toggleAgentSection()">
                        <option value="">اختر نوع الإجراء</option>
                        <option value="CREATE_DELIVERY_ORDER" {{ 'selected' if rule.action_type == 'CREATE_DELIVERY_ORDER' else '' }}>إنشاء أمر تسليم</option>
                        <option value="CREATE_DELIVERY_ORDER_WITH_AGENT" {{ 'selected' if rule.action_type == 'CREATE_DELIVERY_ORDER_WITH_AGENT' else '' }}>إنشاء أمر تسليم وتعيين مخلص</option>
                        <option value="ASSIGN_AGENT" {{ 'selected' if rule.action_type == 'ASSIGN_AGENT' else '' }}>تعيين مخلص</option>
                        <option value="SEND_NOTIFICATION" {{ 'selected' if rule.action_type == 'SEND_NOTIFICATION' else '' }}>إرسال إشعار</option>
                        <option value="UPDATE_RATINGS" {{ 'selected' if rule.action_type == 'UPDATE_RATINGS' else '' }}>تحديث التقييمات</option>
                    </select>
                    <small class="form-text text-muted">الإجراء الذي سيتم تنفيذه</small>
                </div>

                <!-- رسائل توضيحية -->
                <div class="alert alert-info" id="condition_only_message" style="display: none;">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>شرط فقط:</strong> هذه القاعدة ستراقب الشرط المحدد فقط دون تنفيذ أي إجراء تلقائي.
                </div>

                <div class="alert alert-warning" id="action_only_message" style="display: none;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>إجراء فقط:</strong> هذه القاعدة ستنفذ الإجراء مباشرة دون انتظار شرط معين.
                </div>

                <!-- قسم اختيار المخلص (يظهر عند اختيار إجراء متعلق بالمخلص) -->
                <div class="form-group" id="agent_selection_section" style="display: none;">
                    <div class="card" style="border: 2px solid #e2e8f0; border-radius: 12px; padding: 20px; background: #f8fafc;">
                        <h5 class="mb-3" style="color: #2d3748; font-weight: 600;">
                            <i class="fas fa-user-tie me-2"></i>إعدادات المخلص
                        </h5>

                        <div class="row">
                            <div class="col-md-6">
                                <label for="selected_agent" class="form-label">اختيار المخلص</label>
                                <select class="form-select" id="selected_agent" name="selected_agent">
                                    <option value="">اختر المخلص...</option>
                                    <option value="AUTO" {{ 'selected' if rule.auto_agent_selection else '' }}>اختيار تلقائي</option>
                                    <!-- سيتم تحميل المخلصين عبر JavaScript -->
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="agent_criteria" class="form-label">معايير الاختيار التلقائي</label>
                                <select class="form-select" id="agent_criteria" name="agent_criteria">
                                    <option value="rating" {{ 'selected' if rule.agent_selection_criteria == 'rating' else '' }}>الأعلى تقييماً</option>
                                    <option value="experience" {{ 'selected' if rule.agent_selection_criteria == 'experience' else '' }}>الأكثر خبرة</option>
                                    <option value="availability" {{ 'selected' if rule.agent_selection_criteria == 'availability' else '' }}>الأكثر توفراً</option>
                                    <option value="specialization" {{ 'selected' if rule.agent_selection_criteria == 'specialization' else '' }}>التخصص</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label for="agent_branch" class="form-label">الفرع</label>
                                <input type="text" class="form-control" id="agent_branch" name="agent_branch_display" readonly
                                       value="{{ rule.branch_name or '' }}">
                                <input type="hidden" id="agent_branch_id" name="agent_branch_id" value="{{ rule.agent_branch_id or '' }}">
                            </div>
                            <div class="col-md-6">
                                <label for="agent_port" class="form-label">المنفذ الجمركي</label>
                                <input type="text" class="form-control" id="agent_port" name="agent_port_display" readonly
                                       value="{{ rule.port_name or '' }}">
                                <input type="hidden" id="agent_port_id" name="agent_port_id" value="{{ rule.agent_port_id or '' }}">
                            </div>
                        </div>

                        <div class="mt-3">
                            <label for="agent_assignment_notes" class="form-label">ملاحظات تعيين المخلص</label>
                            <textarea class="form-control" id="agent_assignment_notes" name="agent_assignment_notes" rows="2"
                                      placeholder="ملاحظات إضافية حول تعيين المخلص...">{{ rule.agent_assignment_notes or '' }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- الوصف -->
                <div class="form-group">
                    <label for="description" class="form-label">
                        <i class="fas fa-align-left me-2"></i>الوصف
                    </label>
                    <textarea class="form-control" id="description" name="description" rows="3" 
                              placeholder="وصف مفصل لقاعدة الأتمتة">{{ rule.description }}</textarea>
                </div>

                <!-- حالة التفعيل -->
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                           {{ 'checked' if rule.is_active else '' }}>
                    <label class="form-check-label" for="is_active">
                        <i class="fas fa-toggle-on me-2"></i>تفعيل القاعدة
                    </label>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="btn-group-custom">
                    <button type="submit" class="btn-custom btn-primary-custom">
                        <i class="fas fa-save me-2"></i>حفظ التعديلات
                    </button>
                    <a href="{{ url_for('shipments.automation_dashboard') }}" class="btn-custom btn-secondary-custom">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تأثيرات تفاعلية وإدارة قسم المخلص
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير focus للحقول
            const inputs = document.querySelectorAll('.form-control, .form-select');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.style.transform = 'scale(1.02)';
                });

                input.addEventListener('blur', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // إدارة الحقول حسب نوع القاعدة
            updateRuleFields();

            // إدارة عرض قسم المخلص
            function toggleAgentSection() {
                const actionType = document.getElementById('action_type').value;
                const agentSection = document.getElementById('agent_selection_section');

                if (actionType === 'CREATE_DELIVERY_ORDER_WITH_AGENT' || actionType === 'ASSIGN_AGENT') {
                    agentSection.style.display = 'block';
                    loadCustomsAgents();
                } else {
                    agentSection.style.display = 'none';
                }
            }

            // عرض قسم المخلص عند تحميل الصفحة إذا كان مطلوباً
            toggleAgentSection();

            // تحميل قائمة المخلصين
            function loadCustomsAgents() {
                const selectedAgentSelect = document.getElementById('selected_agent');

                fetch('/shipments/api/customs-agents')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.agents) {
                        // الاحتفاظ بالخيارات الأساسية
                        const basicOptions = selectedAgentSelect.innerHTML;

                        // إضافة المخلصين
                        data.agents.forEach(agent => {
                            const option = document.createElement('option');
                            option.value = agent.id;
                            option.textContent = `${agent.agent_name} (${agent.agent_code}) - تقييم: ${agent.rating}/5`;

                            // تحديد المخلص المحفوظ
                            if ({{ rule.selected_agent_id or 'null' }} == agent.id) {
                                option.selected = true;
                            }

                            selectedAgentSelect.appendChild(option);
                        });

                        console.log('✅ تم تحميل المخلصين بنجاح');
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ في تحميل المخلصين:', error);
                });
            }

            // مراقبة تغيير المخلص المختار
            document.getElementById('selected_agent').addEventListener('change', function() {
                const agentId = this.value;

                if (agentId && agentId !== 'AUTO') {
                    // جلب معلومات المخلص
                    fetch(`/shipments/api/customs-agents/${agentId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.agent) {
                            const agent = data.agent;

                            // تحديث حقول الفرع والمنفذ
                            document.getElementById('agent_branch').value = agent.branch_name || 'غير محدد';
                            document.getElementById('agent_port').value = agent.port_name || 'غير محدد';
                            document.getElementById('agent_branch_id').value = agent.branch_id || '';
                            document.getElementById('agent_port_id').value = agent.customs_port_id || '';

                            console.log('✅ تم تحديث معلومات المخلص');
                        }
                    })
                    .catch(error => {
                        console.error('❌ خطأ في جلب معلومات المخلص:', error);
                    });
                } else {
                    // مسح الحقول
                    document.getElementById('agent_branch').value = '';
                    document.getElementById('agent_port').value = '';
                    document.getElementById('agent_branch_id').value = '';
                    document.getElementById('agent_port_id').value = '';
                }
            });
        });

        // دالة تحديث الحقول حسب نوع القاعدة
        function updateRuleFields() {
            const ruleType = document.getElementById('rule_type').value;
            const triggerCondition = document.getElementById('trigger_condition');
            const triggerConditionSection = document.getElementById('trigger_condition_section');
            const actionSection = document.getElementById('action_section');
            const actionType = document.getElementById('action_type');
            const conditionOnlyMessage = document.getElementById('condition_only_message');
            const actionOnlyMessage = document.getElementById('action_only_message');
            const agentSelectionSection = document.getElementById('agent_selection_section');

            // حفظ القيم الحالية
            const currentTriggerCondition = triggerCondition.value;
            const currentActionType = actionType.value;

            // مسح الخيارات السابقة
            triggerCondition.innerHTML = '<option value="">اختر شرط التشغيل</option>';

            if (!ruleType) {
                triggerCondition.innerHTML = '<option value="">اختر نوع القاعدة أولاً</option>';
                triggerConditionSection.style.display = 'block';
                actionSection.style.display = 'block';
                conditionOnlyMessage.style.display = 'none';
                actionOnlyMessage.style.display = 'none';
                return;
            }

            // إدارة إظهار/إخفاء الأقسام حسب نوع القاعدة
            if (ruleType === 'CONDITION_ONLY') {
                // شرط فقط
                triggerConditionSection.style.display = 'block';
                triggerCondition.required = true;
                actionSection.style.display = 'none';
                actionType.required = false;
                actionType.removeAttribute('required');
                actionType.value = '';
                conditionOnlyMessage.style.display = 'block';
                actionOnlyMessage.style.display = 'none';
                agentSelectionSection.style.display = 'none';
            } else if (ruleType === 'ACTION_ONLY') {
                // إجراء فقط
                triggerConditionSection.style.display = 'none';
                triggerCondition.required = false;
                triggerCondition.removeAttribute('required');
                triggerCondition.value = '';
                actionSection.style.display = 'block';
                actionType.required = true;
                actionType.setAttribute('required', 'required');
                conditionOnlyMessage.style.display = 'none';
                actionOnlyMessage.style.display = 'block';
            } else {
                // قاعدة بسيطة أو مجدولة
                triggerConditionSection.style.display = 'block';
                triggerCondition.required = true;
                triggerCondition.setAttribute('required', 'required');
                actionSection.style.display = 'block';
                actionType.required = true;
                actionType.setAttribute('required', 'required');
                conditionOnlyMessage.style.display = 'none';
                actionOnlyMessage.style.display = 'none';
            }

            // إضافة الخيارات حسب نوع القاعدة
            let options = [];
            let description = '';

            switch(ruleType) {
                case 'SIMPLE_RULE':
                    description = 'قاعدة كاملة تجمع بين الشرط والإجراء - الأكثر استخداماً';
                    options = [
                        { value: 'STATUS_CHANGE_ACTION', text: 'عند تغيير حالة الشحنة' },
                        { value: 'ARRIVAL_NOTIFICATION', text: 'إشعار عند الوصول' },
                        { value: 'AUTO_DELIVERY_WITH_AGENT', text: 'إنشاء أمر تسليم وتعيين مخلص عند الجاهزية' },
                        { value: 'PAYMENT_REMINDER', text: 'تذكير بالدفع' },
                        { value: 'CUSTOMS_ALERT', text: 'تنبيه التخليص الجمركي' }
                    ];
                    break;
                case 'CONDITION_ONLY':
                    description = 'مراقبة شرط معين دون تنفيذ إجراء تلقائي';
                    options = [
                        { value: 'SHIPMENT_ARRIVAL', text: 'وصول الشحنة' },
                        { value: 'STATUS_CHANGE', text: 'تغيير الحالة' },
                        { value: 'PAYMENT_STATUS', text: 'حالة الدفع' },
                        { value: 'CUSTOMS_STATUS', text: 'حالة التخليص الجمركي' }
                    ];
                    break;
                case 'ACTION_ONLY':
                    description = 'تنفيذ إجراء مباشر دون انتظار شرط معين';
                    // لا حاجة لشروط في هذا النوع
                    break;
                case 'SCHEDULED':
                    description = 'قاعدة مبنية على الوقت - تنفذ في أوقات محددة';
                    options = [
                        { value: 'DAILY_REPORT', text: 'تقرير يومي' },
                        { value: 'WEEKLY_SUMMARY', text: 'ملخص أسبوعي' },
                        { value: 'MONTHLY_ANALYSIS', text: 'تحليل شهري' },
                        { value: 'REMINDER_SCHEDULE', text: 'تذكيرات مجدولة' }
                    ];
                    break;
            }

            // تحديث الوصف
            document.getElementById('rule_type_description').textContent = description;

            // إضافة الخيارات للقائمة
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.text;
                if (option.value === currentTriggerCondition) {
                    optionElement.selected = true;
                }
                triggerCondition.appendChild(optionElement);
            });

            // استعادة القيم إذا كانت متوافقة
            if (currentActionType && actionSection.style.display !== 'none') {
                actionType.value = currentActionType;
                toggleAgentSection();
            }
        }

        // دالة تحديث قيمة الشرط (يمكن توسيعها لاحقاً)
        function updateConditionValue() {
            // يمكن إضافة منطق إضافي هنا حسب الحاجة
            console.log('تم تحديث شرط التشغيل');
        }
    </script>
</body>
</html>
