{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- شريط الأدوات العلوي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-{{ 'plus' if action == 'new' else 'edit' }} me-2"></i>
                            {{ title }}
                        </h5>
                        <div class="btn-group">
                            <a href="{{ url_for('purchase_requests.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج الطلب -->
    <form method="POST" id="purchaseRequestForm">
        <div class="row">
            <!-- المعلومات الأساسية -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">المعلومات الأساسية</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="req_no" class="form-label">رقم الطلب <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="req_no" name="req_no" 
                                           value="{{ request.req_no if request else '' }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="req_date" class="form-label">تاريخ الطلب <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="req_date" name="req_date" 
                                           value="{{ request.req_date.strftime('%Y-%m-%d') if request and request.req_date else '' }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="requester_name" class="form-label">اسم الطالب <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="requester_name" name="requester_name" 
                                           value="{{ request.requester_name if request else '' }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="department" class="form-label">القسم</label>
                                    <input type="text" class="form-control" id="department" name="department" 
                                           value="{{ request.department if request else '' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="req_status" class="form-label">الحالة</label>
                                    <select class="form-select" id="req_status" name="req_status">
                                        <option value="مسودة" {{ 'selected' if request and request.req_status == 'مسودة' else '' }}>مسودة</option>
                                        <option value="قيد المراجعة" {{ 'selected' if request and request.req_status == 'قيد المراجعة' else '' }}>قيد المراجعة</option>
                                        <option value="معتمد" {{ 'selected' if request and request.req_status == 'معتمد' else '' }}>معتمد</option>
                                        <option value="مرفوض" {{ 'selected' if request and request.req_status == 'مرفوض' else '' }}>مرفوض</option>
                                        <option value="ملغي" {{ 'selected' if request and request.req_status == 'ملغي' else '' }}>ملغي</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="priority" class="form-label">الأولوية</label>
                                    <select class="form-select" id="priority" name="priority">
                                        <option value="عادي" {{ 'selected' if request and request.priority == 'عادي' else '' }}>عادي</option>
                                        <option value="مهم" {{ 'selected' if request and request.priority == 'مهم' else '' }}>مهم</option>
                                        <option value="عاجل" {{ 'selected' if request and request.priority == 'عاجل' else '' }}>عاجل</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="purpose" class="form-label">الغرض من الطلب</label>
                                    <textarea class="form-control" id="purpose" name="purpose" rows="3">{{ request.purpose if request else '' }}</textarea>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3">{{ request.notes if request else '' }}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- المعلومات المالية -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">المعلومات المالية</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="total_amount" class="form-label">إجمالي المبلغ</label>
                            <input type="number" class="form-control" id="total_amount" name="total_amount" 
                                   step="0.01" value="{{ request.total_amount if request else '0.00' }}">
                        </div>
                        <div class="mb-3">
                            <label for="currency" class="form-label">العملة</label>
                            <select class="form-select" id="currency" name="currency">
                                <option value="ريال سعودي" {{ 'selected' if request and request.currency == 'ريال سعودي' else '' }}>ريال سعودي</option>
                                <option value="دولار أمريكي" {{ 'selected' if request and request.currency == 'دولار أمريكي' else '' }}>دولار أمريكي</option>
                                <option value="يورو" {{ 'selected' if request and request.currency == 'يورو' else '' }}>يورو</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="needed_date" class="form-label">تاريخ الحاجة</label>
                            <input type="date" class="form-control" id="needed_date" name="needed_date" 
                                   value="{{ request.needed_date.strftime('%Y-%m-%d') if request and request.needed_date else '' }}">
                        </div>
                    </div>
                </div>
                
                <!-- أزرار الحفظ -->
                <div class="card mt-3">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>حفظ الطلب
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                                <i class="fas fa-undo me-1"></i>إعادة تعيين
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- عناصر الطلب -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">عناصر الطلب</h6>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addItem()">
                                <i class="fas fa-plus me-1"></i>إضافة صنف
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="itemsTable">
                                <thead>
                                    <tr>
                                        <th width="50">م</th>
                                        <th width="100">كود الصنف</th>
                                        <th>اسم الصنف</th>
                                        <th width="80">الكمية</th>
                                        <th width="80">الوحدة</th>
                                        <th width="100">سعر الوحدة</th>
                                        <th width="100">الإجمالي</th>
                                        <th width="80">إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="itemsTableBody">
                                    {% if request and request.items %}
                                        {% for item in request.items %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td><input type="text" class="form-control form-control-sm" name="item_code[]" value="{{ item.item_code }}"></td>
                                            <td><input type="text" class="form-control form-control-sm" name="item_name[]" value="{{ item.item_name }}"></td>
                                            <td><input type="number" class="form-control form-control-sm" name="quantity[]" value="{{ item.quantity }}" step="0.01" onchange="calculateTotal(this)"></td>
                                            <td><input type="text" class="form-control form-control-sm" name="unit_name[]" value="{{ item.unit_name }}"></td>
                                            <td><input type="number" class="form-control form-control-sm" name="unit_price[]" value="{{ item.unit_price }}" step="0.01" onchange="calculateTotal(this)"></td>
                                            <td><input type="number" class="form-control form-control-sm" name="total_price[]" value="{{ item.total_price }}" step="0.01" readonly></td>
                                            <td><button type="button" class="btn btn-outline-danger btn-sm" onclick="removeItem(this)"><i class="fas fa-trash"></i></button></td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="8" class="text-center py-3">
                                                <span class="text-muted">لا توجد عناصر. اضغط "إضافة صنف" لإضافة عنصر جديد.</span>
                                            </td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
let itemCounter = {{ request.items|length if request and request.items else 0 }};

function addItem() {
    itemCounter++;
    const tbody = document.getElementById('itemsTableBody');
    
    // إزالة رسالة "لا توجد عناصر" إذا كانت موجودة
    const emptyRow = tbody.querySelector('tr td[colspan="8"]');
    if (emptyRow) {
        emptyRow.parentElement.remove();
    }
    
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${itemCounter}</td>
        <td><input type="text" class="form-control form-control-sm" name="item_code[]"></td>
        <td><input type="text" class="form-control form-control-sm" name="item_name[]"></td>
        <td><input type="number" class="form-control form-control-sm" name="quantity[]" step="0.01" onchange="calculateTotal(this)"></td>
        <td><input type="text" class="form-control form-control-sm" name="unit_name[]"></td>
        <td><input type="number" class="form-control form-control-sm" name="unit_price[]" step="0.01" onchange="calculateTotal(this)"></td>
        <td><input type="number" class="form-control form-control-sm" name="total_price[]" step="0.01" readonly></td>
        <td><button type="button" class="btn btn-outline-danger btn-sm" onclick="removeItem(this)"><i class="fas fa-trash"></i></button></td>
    `;
    
    tbody.appendChild(row);
}

function removeItem(button) {
    const row = button.closest('tr');
    row.remove();
    
    // إعادة ترقيم الصفوف
    const rows = document.querySelectorAll('#itemsTableBody tr');
    rows.forEach((row, index) => {
        const firstCell = row.querySelector('td:first-child');
        if (firstCell && !firstCell.hasAttribute('colspan')) {
            firstCell.textContent = index + 1;
        }
    });
    
    // إذا لم تعد هناك عناصر، أظهر رسالة
    if (rows.length === 0) {
        const tbody = document.getElementById('itemsTableBody');
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-3">
                    <span class="text-muted">لا توجد عناصر. اضغط "إضافة صنف" لإضافة عنصر جديد.</span>
                </td>
            </tr>
        `;
    }
    
    updateTotalAmount();
}

function calculateTotal(input) {
    const row = input.closest('tr');
    const quantity = parseFloat(row.querySelector('input[name="quantity[]"]').value) || 0;
    const unitPrice = parseFloat(row.querySelector('input[name="unit_price[]"]').value) || 0;
    const totalPrice = quantity * unitPrice;
    
    row.querySelector('input[name="total_price[]"]').value = totalPrice.toFixed(2);
    updateTotalAmount();
}

function updateTotalAmount() {
    let total = 0;
    const totalPriceInputs = document.querySelectorAll('input[name="total_price[]"]');
    
    totalPriceInputs.forEach(input => {
        total += parseFloat(input.value) || 0;
    });
    
    document.getElementById('total_amount').value = total.toFixed(2);
}

function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع البيانات المدخلة.')) {
        document.getElementById('purchaseRequestForm').reset();
        
        // إعادة تعيين جدول العناصر
        const tbody = document.getElementById('itemsTableBody');
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-3">
                    <span class="text-muted">لا توجد عناصر. اضغط "إضافة صنف" لإضافة عنصر جديد.</span>
                </td>
            </tr>
        `;
        itemCounter = 0;
    }
}

// تحديث التاريخ الافتراضي
document.addEventListener('DOMContentLoaded', function() {
    const reqDateInput = document.getElementById('req_date');
    if (!reqDateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        reqDateInput.value = today;
    }
});
</script>

<style>
.form-control-sm {
    font-size: 0.875rem;
}

.table td {
    vertical-align: middle;
}

.text-danger {
    color: #dc3545 !important;
}

.card-header h6 {
    color: #495057;
    font-weight: 600;
}
</style>
{% endblock %}
