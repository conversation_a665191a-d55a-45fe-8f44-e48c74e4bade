<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأتمتة الفعلية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 2rem;
            margin: 2rem auto;
            max-width: 800px;
        }
        .btn-test {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h2 class="text-center mb-4">
                <i class="fas fa-cogs text-primary"></i>
                اختبار الأتمتة الفعلية من الواجهة
            </h2>
            
            <div class="row">
                <div class="col-md-4">
                    <label for="shipmentId" class="form-label">معرف الشحنة</label>
                    <input type="number" class="form-control" id="shipmentId" value="174">
                </div>
                <div class="col-md-4">
                    <label for="newStatus" class="form-label">الحالة الجديدة</label>
                    <select class="form-control" id="newStatus">
                        <option value="customs_clearance">قيد التخليص الجمركي</option>
                        <option value="in_transit">في الطريق</option>
                        <option value="delivered">تم التسليم</option>
                        <option value="arrived_port">وصل الميناء</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <input type="text" class="form-control" id="notes" value="اختبار الأتمتة الفعلية">
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-6">
                    <button class="btn btn-test w-100" onclick="testApiEndpoint()">
                        <i class="fas fa-play"></i>
                        اختبار /api/update-status
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-success w-100" onclick="testUpdateEndpoint()">
                        <i class="fas fa-sync"></i>
                        اختبار /update_status
                    </button>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-md-4">
                    <button class="btn btn-info w-100" onclick="checkLogs()">
                        <i class="fas fa-file-alt"></i>
                        فحص السجلات
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-warning w-100" onclick="checkQueue()">
                        <i class="fas fa-list"></i>
                        فحص الطابور
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-secondary w-100" onclick="clearResults()">
                        <i class="fas fa-trash"></i>
                        مسح النتائج
                    </button>
                </div>
            </div>
            
            <div id="results" class="result-box" style="display: none;">
                <h5><i class="fas fa-chart-line"></i> نتائج الاختبار:</h5>
                <div id="resultContent"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function addResult(message, type = 'info') {
            const resultContent = document.getElementById('resultContent');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const resultDiv = document.createElement('div');
            resultDiv.className = `${type} mb-2`;
            resultDiv.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            resultContent.appendChild(resultDiv);
            
            document.getElementById('results').style.display = 'block';
            resultContent.scrollTop = resultContent.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('resultContent').innerHTML = '';
            document.getElementById('results').style.display = 'none';
        }
        
        async function testApiEndpoint() {
            const shipmentId = document.getElementById('shipmentId').value;
            const newStatus = document.getElementById('newStatus').value;
            const notes = document.getElementById('notes').value;
            
            addResult(`🧪 اختبار endpoint: /shipments/api/update-status`, 'info');
            addResult(`📋 البيانات: شحنة ${shipmentId} → ${newStatus}`, 'info');
            
            try {
                const formData = new FormData();
                formData.append('shipment_id', shipmentId);
                formData.append('new_status', newStatus);
                formData.append('notes', notes);
                formData.append('location', 'مطار صنعاء');
                
                const response = await fetch('/shipments/api/update-status', {
                    method: 'POST',
                    body: formData,
                    credentials: 'same-origin'
                });
                
                addResult(`📡 كود الاستجابة: ${response.status}`, 'info');
                
                const result = await response.json();
                
                if (result.success) {
                    addResult(`✅ نجح التحديث: ${result.message}`, 'success');
                    addResult(`🤖 تحقق من WhatsApp: 967774893877`, 'success');
                } else {
                    addResult(`❌ فشل التحديث: ${result.message}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في الاتصال: ${error.message}`, 'error');
            }
        }
        
        async function testUpdateEndpoint() {
            const shipmentId = document.getElementById('shipmentId').value;
            const newStatus = document.getElementById('newStatus').value;
            const notes = document.getElementById('notes').value;
            
            addResult(`🧪 اختبار endpoint: /shipments/update_status`, 'info');
            
            try {
                const response = await fetch('/shipments/update_status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        shipment_id: shipmentId,
                        new_status: newStatus,
                        notes: notes
                    })
                });
                
                addResult(`📡 كود الاستجابة: ${response.status}`, 'info');
                
                const result = await response.json();
                
                if (result.success) {
                    addResult(`✅ نجح التحديث: ${result.message}`, 'success');
                    addResult(`🤖 تحقق من WhatsApp: 967774893877`, 'success');
                } else {
                    addResult(`❌ فشل التحديث: ${result.message}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في الاتصال: ${error.message}`, 'error');
            }
        }
        
        async function checkLogs() {
            addResult(`🔍 فحص آخر سجلات التنفيذ...`, 'info');
            // يمكن إضافة endpoint لفحص السجلات
            addResult(`⚠️ فحص السجلات يتطلب إضافة endpoint خاص`, 'warning');
        }
        
        async function checkQueue() {
            addResult(`🔍 فحص طابور الأتمتة...`, 'info');
            // يمكن إضافة endpoint لفحص الطابور
            addResult(`⚠️ فحص الطابور يتطلب إضافة endpoint خاص`, 'warning');
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🎯 صفحة اختبار الأتمتة الفعلية جاهزة', 'success');
            addResult('📱 سيتم إرسال WhatsApp إلى: 967774893877', 'info');
            addResult('🔧 اختبر كلا الـ endpoints لمعرفة أيهما يعمل', 'info');
        });
    </script>
</body>
</html>
