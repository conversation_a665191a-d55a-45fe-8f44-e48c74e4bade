#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
استخراج الـ API endpoints الدقيقة من JavaScript
"""

import re

def extract_exact_apis():
    """استخراج الـ APIs الدقيقة"""
    
    print("🎯 استخراج الـ API endpoints الدقيقة")
    print("=" * 60)
    
    try:
        # قراءة الملف
        with open('cosco_main_js.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        print(f"📜 حجم الملف: {len(js_content)} حرف")
        
        # البحث عن التعريفات الدقيقة
        print("\n🔍 البحث عن التعريفات الدقيقة...")
        
        # البحث عن CARGOTRACKING_SEARCH patterns مع القيم
        patterns = [
            r'CARGOTRACKING_SEARCH_BY_BOOKING_GET\s*:\s*["\']([^"\']+)["\']',
            r'CARGOTRACKING_SEARCH_BY_BILL_GET\s*:\s*["\']([^"\']+)["\']',
            r'CARGOTRACKING_SEARCH_BY_CNTRS_GET\s*:\s*["\']([^"\']+)["\']'
        ]
        
        found_apis = {}
        for pattern in patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            if matches:
                api_name = pattern.split('\\s')[0]
                found_apis[api_name] = matches[0]
                print(f"  ✅ {api_name}: {matches[0]}")
        
        # إذا لم نجد بالطريقة الأولى، نبحث بطريقة أخرى
        if not found_apis:
            print("\n🔍 البحث بطريقة أخرى...")
            
            # البحث عن السياق حول CARGOTRACKING
            context_pattern = r'([^,]{0,100}CARGOTRACKING_SEARCH_BY_BOOKING_GET[^,]{0,100})'
            matches = re.findall(context_pattern, js_content, re.IGNORECASE)
            
            for match in matches[:5]:  # أول 5
                print(f"  📄 Context: {match}")
                
                # استخراج القيمة من السياق
                value_pattern = r'["\']([^"\']+)["\']'
                values = re.findall(value_pattern, match)
                for value in values:
                    if len(value) > 5 and ('/' in value or 'api' in value.lower()):
                        print(f"    🎯 Possible API: {value}")
        
        # البحث عن base URLs
        print("\n🔍 البحث عن base URLs...")
        
        base_patterns = [
            r'WEB_URL\s*:\s*["\']([^"\']+)["\']',
            r'BASE_URL\s*:\s*["\']([^"\']+)["\']',
            r'API_BASE\s*:\s*["\']([^"\']+)["\']'
        ]
        
        base_urls = {}
        for pattern in base_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            if matches:
                base_name = pattern.split('\\s')[0]
                base_urls[base_name] = matches[0]
                print(f"  🏠 {base_name}: {matches[0]}")
        
        # البحث عن service URLs
        print("\n🔍 البحث عن service URLs...")
        
        service_patterns = [
            r'CARGO_TRACKING_SERVICE\s*:\s*["\']([^"\']+)["\']',
            r'TRACKING_SERVICE\s*:\s*["\']([^"\']+)["\']'
        ]
        
        service_urls = {}
        for pattern in service_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            if matches:
                service_name = pattern.split('\\s')[0]
                service_urls[service_name] = matches[0]
                print(f"  🔧 {service_name}: {matches[0]}")
        
        # محاولة تجميع URLs كاملة
        print("\n🔗 تجميع URLs كاملة...")
        
        if base_urls and service_urls:
            for base_name, base_url in base_urls.items():
                for service_name, service_url in service_urls.items():
                    full_url = base_url + service_url
                    print(f"  🌐 {base_name} + {service_name}: {full_url}")
        
        # البحث عن endpoints محددة
        print("\n🔍 البحث عن endpoints محددة...")
        
        endpoint_patterns = [
            r'["\']([^"\']*cargoTracking[^"\']*search[^"\']*)["\']',
            r'["\']([^"\']*search[^"\']*cargoTracking[^"\']*)["\']',
            r'["\']([^"\']*cargo[^"\']*tracking[^"\']*api[^"\']*)["\']'
        ]
        
        found_endpoints = set()
        for pattern in endpoint_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            for match in matches:
                if len(match) > 10 and '/' in match:
                    found_endpoints.add(match)
        
        print(f"  📋 Endpoints found: {len(found_endpoints)}")
        for endpoint in sorted(found_endpoints)[:10]:
            print(f"    - {endpoint}")
        
        return found_apis, base_urls, service_urls, found_endpoints
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return {}, {}, {}, set()

def test_constructed_apis():
    """اختبار APIs مبنية بناءً على الاكتشافات"""
    
    print(f"\n🧪 اختبار APIs مبنية...")
    
    import requests
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    session = requests.Session()
    session.verify = False
    
    # زيارة الموقع للحصول على cookies
    print("📡 زيارة الموقع للحصول على cookies...")
    session.get("https://elines.coscoshipping.com/ebusiness/cargoTracking", timeout=20)
    
    # تحديث headers
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'Origin': 'https://elines.coscoshipping.com',
        'Referer': 'https://elines.coscoshipping.com/ebusiness/cargoTracking',
        'X-Requested-With': 'XMLHttpRequest'
    })
    
    # بناء URLs محتملة بناءً على الأنماط المكتشفة
    base_url = "https://elines.coscoshipping.com"
    
    test_apis = [
        # بناءً على الأنماط المكتشفة
        f"{base_url}/ebusiness/cargoTracking/search/booking",
        f"{base_url}/ebusiness/cargoTracking/search/bill", 
        f"{base_url}/ebusiness/cargoTracking/search/container",
        f"{base_url}/ebusiness/api/cargoTracking/search/booking",
        f"{base_url}/ebusiness/api/cargoTracking/search/bill",
        f"{base_url}/ebusiness/api/cargoTracking/search/container",
        f"{base_url}/api/cargoTracking/search/booking",
        f"{base_url}/api/cargoTracking/search/bill",
        f"{base_url}/api/cargoTracking/search/container",
        # أنماط أخرى
        f"{base_url}/ebusiness/cargoTracking/api/search",
        f"{base_url}/ebusiness/cargoTracking/query",
        f"{base_url}/ebusiness/rest/cargoTracking/search"
    ]
    
    # بيانات البحث
    search_data = {
        "bookingNo": "6425375050",
        "trackingType": "2"
    }
    
    print(f"  📋 سيتم اختبار {len(test_apis)} APIs...")
    
    for api_url in test_apis:
        try:
            print(f"  🔗 اختبار: {api_url}")
            
            # محاولة POST
            response = session.post(api_url, json=search_data, timeout=10)
            print(f"    POST Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"    ✅ JSON Response: {str(data)[:150]}...")
                    
                    # فحص البيانات
                    if check_response_data(data, "6425375050"):
                        print(f"    🎯 وجد بيانات مفيدة!")
                        
                        # حفظ النتيجة الناجحة
                        with open('cosco_successful_response.json', 'w', encoding='utf-8') as f:
                            import json
                            json.dump(data, f, indent=2, ensure_ascii=False)
                        print(f"    💾 تم حفظ النتيجة الناجحة")
                        
                        return True
                        
                except Exception as e:
                    print(f"    📄 Text Response: {response.text[:100]}...")
            
            elif response.status_code == 403:
                print(f"    🔒 محمي بـ authentication")
            elif response.status_code not in [404, 405]:
                print(f"    ⚠️ غير متوقع: {response.status_code}")
                print(f"    📄 Response: {response.text[:100]}...")
            
        except Exception as e:
            print(f"    ❌ خطأ: {e}")
    
    return False

def check_response_data(data, booking_number):
    """فحص البيانات المرجعة"""
    
    if isinstance(data, dict):
        data_str = str(data).lower()
        
        # فحص وجود رقم الحجز
        if booking_number in data_str:
            return True
        
        # فحص وجود كلمات مفتاحية
        keywords = ['etd', 'eta', 'vessel', 'port', 'container', 'bl', 'shantou', 'aden']
        found_keywords = [kw for kw in keywords if kw in data_str]
        
        if len(found_keywords) >= 2:  # على الأقل كلمتين مفتاحيتين
            return True
        
        # فحص structure البيانات
        if 'data' in data and 'content' in str(data):
            return True
    
    return False

def main():
    """الاختبار الرئيسي"""
    
    print("=" * 80)
    print("🎯 استخراج واختبار الـ API endpoints الدقيقة")
    print("=" * 80)
    
    # استخراج APIs
    apis, base_urls, service_urls, endpoints = extract_exact_apis()
    
    # اختبار APIs مبنية
    success = test_constructed_apis()
    
    if success:
        print(f"\n🎯 نجح في العثور على API يعمل!")
    else:
        print(f"\n❌ لم يتم العثور على API يعمل")
    
    print(f"\n" + "=" * 80)
    print("✅ انتهى الاستخراج والاختبار الدقيق")
    print("=" * 80)

if __name__ == "__main__":
    main()
