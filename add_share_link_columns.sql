-- إضافة أعمدة روابط المشاركة لجدول وثائق الشحنات
-- Add share link columns to cargo shipment documents table

-- التحقق من وجود الأعمدة أولاً
DECLARE
    column_exists NUMBER;
BEGIN
    -- فحص عمود share_link
    SELECT COUNT(*) INTO column_exists
    FROM user_tab_columns
    WHERE table_name = 'CARGO_SHIPMENT_DOCUMENTS' AND column_name = 'SHARE_LINK';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE cargo_shipment_documents ADD share_link VARCHAR2(500)';
        DBMS_OUTPUT.PUT_LINE('تم إضافة عمود share_link');
    ELSE
        DBMS_OUTPUT.PUT_LINE('عمود share_link موجود مسبقاً');
    END IF;
    
    -- فحص عمود share_service
    SELECT COUNT(*) INTO column_exists
    FROM user_tab_columns
    WHERE table_name = 'CARGO_SHIPMENT_DOCUMENTS' AND column_name = 'SHARE_SERVICE';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE cargo_shipment_documents ADD share_service VARCHAR2(50)';
        DBMS_OUTPUT.PUT_LINE('تم إضافة عمود share_service');
    ELSE
        DBMS_OUTPUT.PUT_LINE('عمود share_service موجود مسبقاً');
    END IF;
    
    -- فحص عمود share_created_at
    SELECT COUNT(*) INTO column_exists
    FROM user_tab_columns
    WHERE table_name = 'CARGO_SHIPMENT_DOCUMENTS' AND column_name = 'SHARE_CREATED_AT';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE cargo_shipment_documents ADD share_created_at DATE';
        DBMS_OUTPUT.PUT_LINE('تم إضافة عمود share_created_at');
    ELSE
        DBMS_OUTPUT.PUT_LINE('عمود share_created_at موجود مسبقاً');
    END IF;
    
    -- فحص عمود share_expires_at
    SELECT COUNT(*) INTO column_exists
    FROM user_tab_columns
    WHERE table_name = 'CARGO_SHIPMENT_DOCUMENTS' AND column_name = 'SHARE_EXPIRES_AT';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE cargo_shipment_documents ADD share_expires_at DATE';
        DBMS_OUTPUT.PUT_LINE('تم إضافة عمود share_expires_at');
    ELSE
        DBMS_OUTPUT.PUT_LINE('عمود share_expires_at موجود مسبقاً');
    END IF;
    
    COMMIT;
    DBMS_OUTPUT.PUT_LINE('تم تطبيق جميع التغييرات بنجاح');
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        DBMS_OUTPUT.PUT_LINE('خطأ: ' || SQLERRM);
        RAISE;
END;
/

-- إضافة تعليقات على الأعمدة الجديدة
COMMENT ON COLUMN cargo_shipment_documents.share_link IS 'رابط المشاركة السحابي للوثيقة';
COMMENT ON COLUMN cargo_shipment_documents.share_service IS 'اسم الخدمة السحابية (OneDrive, Nextcloud, etc.)';
COMMENT ON COLUMN cargo_shipment_documents.share_created_at IS 'تاريخ إنشاء رابط المشاركة';
COMMENT ON COLUMN cargo_shipment_documents.share_expires_at IS 'تاريخ انتهاء صلاحية رابط المشاركة';

-- إنشاء فهرس على عمود share_service للبحث السريع
CREATE INDEX IF NOT EXISTS idx_cargo_docs_share_service 
ON cargo_shipment_documents(share_service);

COMMIT;
