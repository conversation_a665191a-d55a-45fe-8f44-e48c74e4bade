# دليل التسجيل في WhatsApp Business API

## 🎯 الطريقة الموصى بها: Meta WhatsApp Cloud API

### الخطوة 1: إنشاء حساب Meta for Developers

1. **انتقل إلى الرابط:**
   ```
   https://developers.facebook.com/
   ```

2. **سجل دخول أو أنشئ حساب:**
   - استخدم حساب Facebook موجود
   - أو أنشئ حساب جديد بالبريد الإلكتروني

3. **التحقق من الهوية:**
   - قد يطلب منك رقم هاتف للتحقق
   - أدخل رقم هاتف صالح

### الخطوة 2: إنشاء تطبيق جديد

1. **اضغط "My Apps" في الأعلى**

2. **اضغط "Create App"**

3. **اختر نوع التطبيق:**
   ```
   ✅ Business (اختر هذا)
   ❌ Consumer
   ❌ Gaming
   ```

4. **أدخل معلومات التطبيق:**
   ```
   App Name: نظام إدارة الشحنات
   App Contact Email: <EMAIL>
   Business Manager Account: (اختياري)
   ```

5. **اضغط "Create App"**

### الخطوة 3: إضافة منتج WhatsApp

1. **في لوحة التطبيق، ابحث عن "WhatsApp"**

2. **اضغط "Set up" بجانب WhatsApp Business API**

3. **اختر Business Account:**
   - إذا كان لديك حساب: اختره
   - إذا لم يكن لديك: اضغط "Create a Business Account"

### الخطوة 4: إعداد رقم الهاتف

1. **في قسم "Phone Numbers":**
   - اضغط "Add phone number"
   - أدخل رقم هاتف تجاري (ليس شخصي)
   - **مهم**: الرقم يجب أن يكون جديد أو غير مستخدم في WhatsApp

2. **التحقق من الرقم:**
   - اختر طريقة التحقق (SMS أو مكالمة)
   - أدخل الكود المرسل

3. **إعداد الملف التجاري:**
   ```
   Business Name: اسم شركتك
   Category: Shipping & Logistics
   Description: خدمات الشحن والتخليص الجمركي
   Website: موقع شركتك (اختياري)
   ```

### الخطوة 5: الحصول على المعرفات المطلوبة

1. **في قسم "API Setup":**
   ```
   ✅ Temporary Access Token: نسخ هذا الرمز
   ✅ Phone Number ID: نسخ هذا المعرف
   ✅ WhatsApp Business Account ID: نسخ هذا المعرف
   ```

2. **حفظ المعلومات:**
   ```
   WHATSAPP_ACCESS_TOKEN=EAAxxxxxxxxxxxxxxxxx
   WHATSAPP_PHONE_NUMBER_ID=****************
   WHATSAPP_BUSINESS_ACCOUNT_ID=****************
   WHATSAPP_API_URL=https://graph.facebook.com/v18.0
   ```

### الخطوة 6: اختبار الإعداد

1. **في قسم "API Setup":**
   - أدخل رقم هاتف للاختبار
   - اضغط "Send Message"
   - تحقق من وصول الرسالة

2. **إذا وصلت الرسالة:**
   ```
   🎉 تهانينا! الإعداد نجح
   ```

## 🔧 إعداد النظام

### إضافة المتغيرات للنظام:

1. **أنشئ ملف `.env` في مجلد المشروع:**
   ```bash
   # WhatsApp Business API Configuration
   WHATSAPP_ACCESS_TOKEN=your_access_token_here
   WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id_here
   WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id_here
   WHATSAPP_API_URL=https://graph.facebook.com/v18.0
   WHATSAPP_TEST_MODE=false
   ```

2. **أو أضفها لمتغيرات النظام في Windows:**
   ```cmd
   setx WHATSAPP_ACCESS_TOKEN "your_access_token_here"
   setx WHATSAPP_PHONE_NUMBER_ID "your_phone_number_id_here"
   setx WHATSAPP_BUSINESS_ACCOUNT_ID "your_business_account_id_here"
   ```

### اختبار النظام:

```python
# اختبار سريع
from app.services.whatsapp_service import whatsapp_service

# التحقق من الإعدادات
print("WhatsApp configured:", whatsapp_service.is_configured())

# اختبار إرسال رسالة
success, message, msg_id = whatsapp_service.send_text_message(
    "+************", 
    "مرحباً! هذه رسالة اختبار من نظام إدارة الشحنات"
)

print(f"Success: {success}")
print(f"Message: {message}")
```

## 💰 التكلفة والحدود

### الخطة المجانية:
- ✅ 1000 رسالة مجانية شهرياً
- ✅ جميع الميزات الأساسية
- ✅ دعم الوسائط (صور، مستندات)

### بعد الحد المجاني:
- 💰 تكلفة منخفضة جداً لكل رسالة
- 📊 تقارير مفصلة عن الاستخدام
- 🔄 فوترة شهرية

## 🛡️ الأمان والامتثال

### متطلبات مهمة:
1. **استخدم HTTPS دائماً**
2. **احم Access Token كسر عالي الأمان**
3. **لا تشارك المعرفات مع أحد**
4. **راقب الاستخدام بانتظام**

### الامتثال لسياسات WhatsApp:
- ✅ لا ترسل رسائل عشوائية (spam)
- ✅ احصل على موافقة المستلمين
- ✅ استخدم قوالب معتمدة للرسائل التسويقية
- ✅ رد على استفسارات العملاء خلال 24 ساعة

## 🆘 حل المشاكل الشائعة

### مشكلة: "Phone number not eligible"
**الحل:**
- تأكد أن الرقم تجاري وليس شخصي
- تأكد أن الرقم غير مستخدم في WhatsApp شخصي
- جرب رقم آخر

### مشكلة: "Access token expired"
**الحل:**
- احصل على Permanent Access Token
- أو جدد الرمز المؤقت

### مشكلة: "Message not delivered"
**الحل:**
- تحقق من رقم المستلم
- تأكد أن المستلم لديه WhatsApp
- تحقق من حالة الرسالة في لوحة التحكم

## 📞 الدعم الفني

### Meta Support:
- 📧 developers.facebook.com/support
- 📚 developers.facebook.com/docs/whatsapp

### مجتمع المطورين:
- 💬 Facebook Developer Community
- 🌐 Stack Overflow (whatsapp-business-api)

---

**🎉 بمجرد إكمال هذه الخطوات، ستكون جاهزاً لإرسال أوامر التسليم عبر WhatsApp تلقائياً!**
