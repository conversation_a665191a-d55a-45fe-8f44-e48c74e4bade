{% extends "base.html" %}

{% block title %}إدارة جهات الاتصال للإشعارات{% endblock %}

{% block extra_css %}
<style>
    /* إصلاح مشكلة Modal المظلل */
    .modal {
        z-index: 9999 !important;
    }

    .modal-backdrop {
        z-index: 9998 !important;
    }

    .modal-dialog {
        z-index: 10000 !important;
    }

    /* إصلاح تداخل العناصر */
    body.modal-open {
        overflow: hidden;
    }

    .contact-card {
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        background: white;
    }
    
    .contact-card:hover {
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        transform: translateY(-2px);
    }
    
    .contact-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(45deg, #4e73df, #224abe);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 1.2rem;
    }
    
    .contact-type-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .priority-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        display: inline-block;
        margin-left: 0.5rem;
    }
    
    .priority-high { background-color: #e74a3b; }
    .priority-medium { background-color: #f39c12; }
    .priority-low { background-color: #28a745; }
    
    .vip-badge {
        background: linear-gradient(45deg, #f6c23e, #dda20a);
        color: white;
        border: none;
    }
    
    .stats-card {
        background: linear-gradient(45deg, #4e73df, #224abe);
        color: white;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .search-box {
        border-radius: 2rem;
        border: 1px solid #d1d3e2;
        padding: 0.5rem 1rem;
    }
    
    .filter-tabs .nav-link {
        border-radius: 2rem;
        margin-right: 0.5rem;
        border: 1px solid #d1d3e2;
    }
    
    .filter-tabs .nav-link.active {
        background: #4e73df;
        border-color: #4e73df;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-address-book text-primary me-2"></i>
                        إدارة جهات الاتصال للإشعارات
                    </h1>
                    <p class="text-muted mb-0">إدارة قاعدة بيانات جهات الاتصال لنظام الإشعارات</p>
                </div>
                <div>
                    <button class="btn btn-success me-2" onclick="openAddContactModal()">
                        <i class="fas fa-plus me-2"></i>
                        إضافة جهة اتصال (Modal)
                    </button>
                    <a href="/notifications/contacts/add" class="btn btn-primary me-2">
                        <i class="fas fa-plus me-2"></i>
                        إضافة جهة اتصال (صفحة منفصلة)
                    </a>
                    <button class="btn btn-info me-2" onclick="importContacts()">
                        <i class="fas fa-upload me-2"></i>
                        استيراد
                    </button>
                    <button class="btn btn-secondary" onclick="exportContacts()">
                        <i class="fas fa-download me-2"></i>
                        تصدير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                    <div>
                        <div class="h4 mb-0">{{ stats.total_contacts or 0 }}</div>
                        <div class="small">إجمالي جهات الاتصال</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-check-circle text-success fa-2x"></i>
                        </div>
                        <div>
                            <div class="h4 mb-0 text-success">{{ stats.active_contacts or 0 }}</div>
                            <div class="small text-muted">جهات اتصال نشطة</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-star text-warning fa-2x"></i>
                        </div>
                        <div>
                            <div class="h4 mb-0 text-warning">{{ stats.vip_contacts or 0 }}</div>
                            <div class="small text-muted">جهات اتصال مميزة</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-info">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-bell text-info fa-2x"></i>
                        </div>
                        <div>
                            <div class="h4 mb-0 text-info">{{ (stats.customers or 0) + (stats.drivers or 0) }}</div>
                            <div class="small text-muted">عملاء وسائقين</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="input-group">
                <input type="text" class="form-control search-box" id="searchInput" placeholder="البحث في جهات الاتصال...">
                <button class="btn btn-outline-primary" onclick="searchContacts()">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
        <div class="col-md-6">
            <ul class="nav nav-pills filter-tabs">
                <li class="nav-item">
                    <a class="nav-link active" href="#" onclick="filterContacts('all')">الكل</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="filterContacts('CUSTOMER')">العملاء</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="filterContacts('DRIVER')">السائقين</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="filterContacts('AGENT')">المخلصين</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="filterContacts('MANAGER')">المديرين</a>
                </li>
            </ul>
        </div>
    </div>

    <!-- Contacts List -->
    <div class="row" id="contactsList">
        {% for contact in contacts %}
        <div class="col-xl-4 col-lg-6 contact-item" data-type="{{ contact.contact_type }}">
            <div class="contact-card">
                <div class="d-flex align-items-start">
                    <div class="contact-avatar me-3">
                        {{ contact.contact_name[0].upper() if contact.contact_name else 'N' }}
                    </div>
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div>
                                <h6 class="mb-1">
                                    {{ contact.contact_name }}
                                    {% if contact.is_vip %}
                                    <span class="badge vip-badge ms-1">VIP</span>
                                    {% endif %}
                                    <span class="priority-indicator priority-{{ 'high' if (contact.priority_level or 5) >= 8 else 'medium' if (contact.priority_level or 5) >= 5 else 'low' }}"></span>
                                </h6>
                                <span class="badge contact-type-badge 
                                    {% if contact.contact_type == 'CUSTOMER' %}bg-primary
                                    {% elif contact.contact_type == 'DRIVER' %}bg-success
                                    {% elif contact.contact_type == 'AGENT' %}bg-warning
                                    {% elif contact.contact_type == 'MANAGER' %}bg-danger
                                    {% else %}bg-secondary{% endif %}">
                                    {% if contact.contact_type == 'CUSTOMER' %}عميل
                                    {% elif contact.contact_type == 'DRIVER' %}سائق
                                    {% elif contact.contact_type == 'AGENT' %}مخلص
                                    {% elif contact.contact_type == 'MANAGER' %}مدير
                                    {% else %}أخرى{% endif %}
                                </span>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="/notifications/contacts/edit?id={{ contact.id }}">
                                        <i class="fas fa-edit me-2"></i>تعديل
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="editContactModal({{ contact.id }})">
                                        <i class="fas fa-edit me-2"></i>تعديل (Modal)
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="viewContact({{ contact.id }})">
                                        <i class="fas fa-eye me-2"></i>عرض
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteContact({{ contact.id }}, '{{ contact.contact_name }}')">
                                        <i class="fas fa-trash me-2"></i>حذف
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="contact-details">
                            {% if contact.phone_number %}
                            <div class="mb-1">
                                <i class="fas fa-phone text-muted me-2"></i>
                                <small>{{ contact.phone_number }}</small>
                            </div>
                            {% endif %}
                            
                            {% if contact.email_address %}
                            <div class="mb-1">
                                <i class="fas fa-envelope text-muted me-2"></i>
                                <small>{{ contact.email_address }}</small>
                            </div>
                            {% endif %}
                            
                            {% if contact.whatsapp_number %}
                            <div class="mb-1">
                                <i class="fab fa-whatsapp text-success me-2"></i>
                                <small>{{ contact.whatsapp_number }}</small>
                            </div>
                            {% endif %}
                            
                            {% if contact.preferred_channels %}
                            <div class="mt-2">
                                <small class="text-muted">القنوات المفضلة:</small>
                                {% for channel in contact.preferred_channels.split(',') %}
                                <span class="badge bg-light text-dark ms-1">{{ channel.strip() }}</span>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Empty State -->
    <div class="row" id="emptyState" style="display: none;">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-address-book text-muted mb-3" style="font-size: 4rem;"></i>
                <h3 class="text-muted">لا توجد جهات اتصال</h3>
                <p class="text-muted">لم يتم العثور على جهات اتصال مطابقة للبحث</p>
                <button class="btn btn-primary" onclick="openAddContactModal()">
                    <i class="fas fa-plus me-2"></i>
                    إضافة جهة اتصال جديدة
                </button>
                <button class="btn btn-warning ms-2" onclick="testModal()">
                    <i class="fas fa-bug me-2"></i>
                    اختبار Modal
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Contact Modal -->
<div class="modal fade" id="addContactModal" tabindex="-1" aria-labelledby="addContactModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addContactModalLabel">
                    <i class="fas fa-plus me-2"></i>
                    إضافة جهة اتصال جديدة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="addContactForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم جهة الاتصال *</label>
                                <input type="text" class="form-control" name="contact_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع جهة الاتصال</label>
                                <select class="form-select" name="contact_type">
                                    <option value="CUSTOMER">عميل</option>
                                    <option value="DRIVER">سائق</option>
                                    <option value="AGENT">مخلص جمركي</option>
                                    <option value="MANAGER">مدير</option>
                                    <option value="EXTERNAL">خارجي</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" name="phone_number" placeholder="+966xxxxxxxxx">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" name="email_address">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم WhatsApp</label>
                                <input type="tel" class="form-control" name="whatsapp_number" placeholder="+966xxxxxxxxx">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الشركة</label>
                                <input type="text" class="form-control" name="company_name">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">القسم</label>
                                <input type="text" class="form-control" name="department">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المنصب</label>
                                <input type="text" class="form-control" name="position">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">القنوات المفضلة</label>
                                <div class="form-check-group">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" name="channels" value="SMS" checked>
                                        <label class="form-check-label">SMS</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" name="channels" value="EMAIL" checked>
                                        <label class="form-check-label">Email</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" name="channels" value="WHATSAPP">
                                        <label class="form-check-label">WhatsApp</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">مستوى الأولوية</label>
                                <select class="form-select" name="priority_level">
                                    <option value="10">عالية جداً (10)</option>
                                    <option value="8">عالية (8)</option>
                                    <option value="5" selected>متوسطة (5)</option>
                                    <option value="3">منخفضة (3)</option>
                                    <option value="1">منخفضة جداً (1)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_vip">
                                    <label class="form-check-label">
                                        جهة اتصال مميزة (VIP)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveContact()">
                    <i class="fas fa-save me-2"></i>حفظ
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Contact Modal -->
<div class="modal fade" id="editContactModal" tabindex="-1" aria-labelledby="editContactModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="editContactModalLabel">
                    <i class="fas fa-edit me-2"></i>
                    تعديل جهة الاتصال
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="editContactForm">
                    <input type="hidden" id="editContactId" name="contact_id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم جهة الاتصال *</label>
                                <input type="text" class="form-control" name="contact_name" id="editContactName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع جهة الاتصال</label>
                                <select class="form-select" name="contact_type" id="editContactType">
                                    <option value="CUSTOMER">عميل</option>
                                    <option value="DRIVER">سائق</option>
                                    <option value="AGENT">مخلص جمركي</option>
                                    <option value="MANAGER">مدير</option>
                                    <option value="EXTERNAL">خارجي</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" name="phone_number" id="editPhoneNumber" placeholder="+966xxxxxxxxx">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" name="email_address" id="editEmailAddress">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم WhatsApp</label>
                                <input type="tel" class="form-control" name="whatsapp_number" id="editWhatsappNumber" placeholder="+966xxxxxxxxx">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الشركة</label>
                                <input type="text" class="form-control" name="company_name" id="editCompanyName">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">القنوات المفضلة</label>
                                <div class="form-check-group">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" name="edit_channels" value="SMS" id="editChannelSMS">
                                        <label class="form-check-label" for="editChannelSMS">SMS</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" name="edit_channels" value="EMAIL" id="editChannelEmail">
                                        <label class="form-check-label" for="editChannelEmail">Email</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" name="edit_channels" value="WHATSAPP" id="editChannelWhatsapp">
                                        <label class="form-check-label" for="editChannelWhatsapp">WhatsApp</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">مستوى الأولوية</label>
                                <select class="form-select" name="priority_level" id="editPriorityLevel">
                                    <option value="10">عالية جداً (10)</option>
                                    <option value="8">عالية (8)</option>
                                    <option value="5">متوسطة (5)</option>
                                    <option value="3">منخفضة (3)</option>
                                    <option value="1">منخفضة جداً (1)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_vip" id="editIsVip">
                                    <label class="form-check-label" for="editIsVip">
                                        جهة اتصال مميزة (VIP)
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_active" id="editIsActive" checked>
                                    <label class="form-check-label" for="editIsActive">
                                        جهة اتصال نشطة
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" name="notes" id="editNotes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger me-2" onclick="deleteContactFromModal()">
                    <i class="fas fa-trash me-2"></i>حذف
                </button>
                <button type="button" class="btn btn-success" onclick="saveEditedContact()">
                    <i class="fas fa-save me-2"></i>حفظ التغييرات
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// متغيرات عامة
let allContacts = {{ contacts | tojson }};
let currentFilter = 'all';

// إصلاح مشكلة Modal
document.addEventListener('DOMContentLoaded', function() {
    // التأكد من تحميل Bootstrap
    if (typeof bootstrap !== 'undefined') {
        console.log('✅ Bootstrap محمل بنجاح');

        // إعداد Modal
        const addContactModal = document.getElementById('addContactModal');
        if (addContactModal) {
            // إزالة أي backdrop موجود
            const existingBackdrops = document.querySelectorAll('.modal-backdrop');
            existingBackdrops.forEach(backdrop => backdrop.remove());

            // إعداد Modal بشكل صحيح
            const modalInstance = new bootstrap.Modal(addContactModal, {
                backdrop: 'static',
                keyboard: false,
                focus: true
            });

            // إضافة event listeners
            addContactModal.addEventListener('show.bs.modal', function() {
                console.log('🔄 فتح Modal...');
                document.body.style.overflow = 'hidden';
            });

            addContactModal.addEventListener('shown.bs.modal', function() {
                console.log('✅ تم فتح Modal بنجاح');
                // التركيز على أول حقل
                const firstInput = addContactModal.querySelector('input[name="contact_name"]');
                if (firstInput) {
                    firstInput.focus();
                }
            });

            addContactModal.addEventListener('hide.bs.modal', function() {
                console.log('🔄 إغلاق Modal...');
                document.body.style.overflow = '';
            });

            addContactModal.addEventListener('hidden.bs.modal', function() {
                console.log('✅ تم إغلاق Modal');
                // إعادة تعيين النموذج
                const form = addContactModal.querySelector('#addContactForm');
                if (form) {
                    form.reset();
                }
            });
        }
    } else {
        console.error('❌ Bootstrap غير محمل!');
    }
});

// دالة لفتح Modal بشكل صحيح
function openAddContactModal() {
    console.log('🔄 محاولة فتح Modal...');
    const addContactModal = document.getElementById('addContactModal');
    if (addContactModal) {
        try {
            // إزالة أي backdrop موجود
            const existingBackdrops = document.querySelectorAll('.modal-backdrop');
            existingBackdrops.forEach(backdrop => backdrop.remove());

            // إنشاء Modal instance جديد
            const modalInstance = new bootstrap.Modal(addContactModal, {
                backdrop: 'static',
                keyboard: false,
                focus: true
            });

            modalInstance.show();
            console.log('✅ تم فتح Modal بنجاح');
        } catch (error) {
            console.error('❌ خطأ في فتح Modal:', error);
            // حل بديل
            addContactModal.style.display = 'block';
            addContactModal.classList.add('show');
            document.body.classList.add('modal-open');

            // إضافة backdrop يدوياً
            const backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop fade show';
            document.body.appendChild(backdrop);
        }
    } else {
        console.error('❌ لم يتم العثور على Modal');
    }
}

// دالة اختبار Modal
function testModal() {
    console.log('🧪 اختبار Modal...');

    // فحص Bootstrap
    if (typeof bootstrap === 'undefined') {
        alert('❌ Bootstrap غير محمل!');
        return;
    }

    // فحص Modal element
    const modal = document.getElementById('addContactModal');
    if (!modal) {
        alert('❌ Modal element غير موجود!');
        return;
    }

    // فحص z-index
    const computedStyle = window.getComputedStyle(modal);
    console.log('Modal z-index:', computedStyle.zIndex);

    // اختبار فتح Modal
    try {
        openAddContactModal();
        alert('✅ تم اختبار Modal بنجاح!');
    } catch (error) {
        alert('❌ خطأ في اختبار Modal: ' + error.message);
    }
}

// البحث في جهات الاتصال
function searchContacts() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    filterAndDisplayContacts(currentFilter, searchTerm);
}

// تصفية جهات الاتصال
function filterContacts(type) {
    currentFilter = type;
    
    // تحديث التبويبات
    document.querySelectorAll('.filter-tabs .nav-link').forEach(link => {
        link.classList.remove('active');
    });
    event.target.classList.add('active');
    
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    filterAndDisplayContacts(type, searchTerm);
}

// تصفية وعرض جهات الاتصال
function filterAndDisplayContacts(type, searchTerm = '') {
    const contactItems = document.querySelectorAll('.contact-item');
    let visibleCount = 0;
    
    contactItems.forEach(item => {
        const contactType = item.dataset.type;
        const contactText = item.textContent.toLowerCase();
        
        const typeMatch = type === 'all' || contactType === type;
        const searchMatch = searchTerm === '' || contactText.includes(searchTerm);
        
        if (typeMatch && searchMatch) {
            item.style.display = 'block';
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });
    
    // إظهار/إخفاء حالة فارغة
    const emptyState = document.getElementById('emptyState');
    if (visibleCount === 0) {
        emptyState.style.display = 'block';
    } else {
        emptyState.style.display = 'none';
    }
}

// حفظ جهة اتصال جديدة
function saveContact() {
    const form = document.getElementById('addContactForm');
    const formData = new FormData(form);
    
    // جمع القنوات المختارة
    const selectedChannels = [];
    document.querySelectorAll('input[name="channels"]:checked').forEach(checkbox => {
        selectedChannels.push(checkbox.value);
    });
    
    const contactData = {
        contact_name: formData.get('contact_name'),
        contact_type: formData.get('contact_type'),
        phone_number: formData.get('phone_number'),
        email_address: formData.get('email_address'),
        whatsapp_number: formData.get('whatsapp_number'),
        company_name: formData.get('company_name'),
        department: formData.get('department'),
        position: formData.get('position'),
        preferred_channels: selectedChannels.join(','),
        priority_level: parseInt(formData.get('priority_level')),
        is_vip: formData.get('is_vip') ? true : false,
        notes: formData.get('notes')
    };
    
    fetch('/notifications/contacts/api/contacts', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(contactData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('خطأ: ' + (data.message || 'فشل في حفظ جهة الاتصال'));
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ في حفظ جهة الاتصال');
    });
}

// حذف جهة اتصال
function deleteContact(contactId, contactName) {
    if (confirm('هل أنت متأكد من حذف جهة الاتصال "' + contactName + '"؟')) {
        fetch('/notifications/contacts/api/contacts/' + contactId, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('خطأ: ' + (data.message || 'فشل في حذف جهة الاتصال'));
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('حدث خطأ في حذف جهة الاتصال');
        });
    }
}

// تعديل جهة اتصال (صفحة منفصلة)
function editContact(contactId) {
    window.location.href = `/notifications/contacts/edit?id=${contactId}`;
}

// تعديل جهة اتصال (Modal)
function editContactModal(contactId) {
    console.log('🔄 فتح modal التعديل للمعرف:', contactId);

    // جلب بيانات جهة الاتصال
    fetch(`/notifications/contacts/api/contacts/${contactId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.contact) {
            populateEditModal(data.contact);

            // فتح Modal
            const editModal = document.getElementById('editContactModal');
            if (editModal) {
                const modalInstance = new bootstrap.Modal(editModal, {
                    backdrop: 'static',
                    keyboard: false,
                    focus: true
                });
                modalInstance.show();
            }
        } else {
            alert('❌ لم يتم العثور على جهة الاتصال');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('❌ حدث خطأ في تحميل بيانات جهة الاتصال');
    });
}

// ملء modal التعديل بالبيانات
function populateEditModal(contact) {
    document.getElementById('editContactId').value = contact.id;
    document.getElementById('editContactName').value = contact.contact_name || '';
    document.getElementById('editContactType').value = contact.contact_type || 'EXTERNAL';
    document.getElementById('editPhoneNumber').value = contact.phone_number || '';
    document.getElementById('editEmailAddress').value = contact.email_address || '';
    document.getElementById('editWhatsappNumber').value = contact.whatsapp_number || '';
    document.getElementById('editCompanyName').value = contact.company_name || '';
    document.getElementById('editPriorityLevel').value = contact.priority_level || 5;
    document.getElementById('editIsVip').checked = contact.is_vip || false;
    document.getElementById('editIsActive').checked = contact.is_active !== false;
    document.getElementById('editNotes').value = contact.notes || '';

    // ملء القنوات المفضلة
    const channels = contact.preferred_channels ? contact.preferred_channels.split(',') : [];
    document.querySelectorAll('input[name="edit_channels"]').forEach(checkbox => {
        checkbox.checked = channels.includes(checkbox.value);
    });
}

// حفظ التعديلات من Modal
function saveEditedContact() {
    const form = document.getElementById('editContactForm');
    const formData = new FormData(form);

    // جمع القنوات المختارة
    const selectedChannels = [];
    document.querySelectorAll('input[name="edit_channels"]:checked').forEach(checkbox => {
        selectedChannels.push(checkbox.value);
    });

    const contactData = {
        contact_name: formData.get('contact_name'),
        contact_type: formData.get('contact_type'),
        phone_number: formData.get('phone_number') || null,
        email_address: formData.get('email_address') || null,
        whatsapp_number: formData.get('whatsapp_number') || null,
        company_name: formData.get('company_name') || null,
        preferred_channels: selectedChannels.join(','),
        priority_level: parseInt(formData.get('priority_level')),
        is_vip: formData.get('is_vip') ? true : false,
        is_active: formData.get('is_active') ? true : false,
        notes: formData.get('notes') || null
    };

    const contactId = formData.get('contact_id');

    // التحقق من البيانات المطلوبة
    if (!contactData.contact_name) {
        alert('يرجى إدخال اسم جهة الاتصال');
        return;
    }

    // إرسال البيانات
    fetch(`/notifications/contacts/api/contacts/${contactId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(contactData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ تم تحديث جهة الاتصال بنجاح!');
            location.reload();
        } else {
            alert('❌ خطأ: ' + (data.message || 'فشل في تحديث جهة الاتصال'));
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('❌ حدث خطأ في تحديث جهة الاتصال');
    });
}

// حذف جهة الاتصال من Modal
function deleteContactFromModal() {
    const contactId = document.getElementById('editContactId').value;
    const contactName = document.getElementById('editContactName').value;

    if (confirm(`هل أنت متأكد من حذف جهة الاتصال "${contactName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        fetch(`/notifications/contacts/api/contacts/${contactId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ تم حذف جهة الاتصال بنجاح!');
                location.reload();
            } else {
                alert('❌ خطأ: ' + (data.message || 'فشل في حذف جهة الاتصال'));
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('❌ حدث خطأ في حذف جهة الاتصال');
        });
    }
}

// عرض جهة اتصال
function viewContact(contactId) {
    window.location.href = `/notifications/contacts/view?id=${contactId}`;
}

// استيراد جهات الاتصال
function importContacts() {
    alert('ميزة الاستيراد قيد التطوير');
}

// تصدير جهات الاتصال
function exportContacts() {
    fetch('/notifications/contacts/export')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم تصدير جهات الاتصال بنجاح');
        } else {
            alert('خطأ في التصدير: ' + data.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ في التصدير');
    });
}

// البحث عند الكتابة
document.getElementById('searchInput').addEventListener('input', searchContacts);
</script>
{% endblock %}
