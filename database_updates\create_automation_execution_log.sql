-- =====================================================
-- إنشاء جدول سجل تنفيذ الأتمتة
-- Create Automation Execution Log Table
-- التاريخ: 2025-08-20
-- =====================================================

-- إنشاء جدول سجل تنفيذ الأتمتة
CREATE TABLE automation_execution_log (
    id NUMBER PRIMARY KEY,
    rule_id NUMBER NOT NULL,
    shipment_id NUMBER,
    trigger_condition VARCHAR2(100),
    condition_value VARCHAR2(500),
    action_type VARCHAR2(100),
    execution_status VARCHAR2(50) DEFAULT 'PENDING',
    execution_result CLOB,
    error_message CLOB,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    execution_duration NUMBER, -- بالميلي ثانية
    created_by NUMBER DEFAULT 1,
    
    -- قيود مرجعية
    CONSTRAINT fk_auto_exec_rule FOREIGN KEY (rule_id) REFERENCES automation_rules(id),
    CONSTRAINT fk_auto_exec_shipment FOREIGN KEY (shipment_id) REFERENCES shipments(id),
    CONSTRAINT fk_auto_exec_user FOREIGN KEY (created_by) REFERENCES users(id),
    
    -- قيود التحقق
    CONSTRAINT chk_exec_status CHECK (execution_status IN ('PENDING', 'RUNNING', 'SUCCESS', 'FAILED', 'SKIPPED'))
);

-- إنشاء sequence للمعرف
CREATE SEQUENCE automation_execution_log_seq START WITH 1 INCREMENT BY 1;

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_auto_exec_rule ON automation_execution_log(rule_id);
CREATE INDEX idx_auto_exec_shipment ON automation_execution_log(shipment_id);
CREATE INDEX idx_auto_exec_status ON automation_execution_log(execution_status);
CREATE INDEX idx_auto_exec_date ON automation_execution_log(executed_at);

-- إضافة تعليقات
COMMENT ON TABLE automation_execution_log IS 'سجل تنفيذ قواعد الأتمتة';
COMMENT ON COLUMN automation_execution_log.rule_id IS 'معرف قاعدة الأتمتة';
COMMENT ON COLUMN automation_execution_log.shipment_id IS 'معرف الشحنة المتأثرة';
COMMENT ON COLUMN automation_execution_log.trigger_condition IS 'الشرط الذي أدى للتنفيذ';
COMMENT ON COLUMN automation_execution_log.condition_value IS 'قيمة الشرط';
COMMENT ON COLUMN automation_execution_log.action_type IS 'نوع الإجراء المنفذ';
COMMENT ON COLUMN automation_execution_log.execution_status IS 'حالة التنفيذ';
COMMENT ON COLUMN automation_execution_log.execution_result IS 'نتيجة التنفيذ';
COMMENT ON COLUMN automation_execution_log.error_message IS 'رسالة الخطأ إن وجدت';
COMMENT ON COLUMN automation_execution_log.execution_duration IS 'مدة التنفيذ بالميلي ثانية';

-- إنشاء جدول مراقبة تغييرات الشحنات
CREATE TABLE shipment_status_changes (
    id NUMBER PRIMARY KEY,
    shipment_id NUMBER NOT NULL,
    old_status VARCHAR2(50),
    new_status VARCHAR2(50) NOT NULL,
    changed_by NUMBER,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    change_reason VARCHAR2(500),
    automation_processed NUMBER(1) DEFAULT 0,
    
    -- قيود مرجعية
    CONSTRAINT fk_status_change_shipment FOREIGN KEY (shipment_id) REFERENCES shipments(id),
    CONSTRAINT fk_status_change_user FOREIGN KEY (changed_by) REFERENCES users(id),
    
    -- قيود التحقق
    CONSTRAINT chk_automation_processed CHECK (automation_processed IN (0, 1))
);

-- إنشاء sequence للمعرف
CREATE SEQUENCE shipment_status_changes_seq START WITH 1 INCREMENT BY 1;

-- إنشاء فهارس
CREATE INDEX idx_status_change_shipment ON shipment_status_changes(shipment_id);
CREATE INDEX idx_status_change_status ON shipment_status_changes(new_status);
CREATE INDEX idx_status_change_processed ON shipment_status_changes(automation_processed);
CREATE INDEX idx_status_change_date ON shipment_status_changes(changed_at);

-- إضافة تعليقات
COMMENT ON TABLE shipment_status_changes IS 'سجل تغييرات حالات الشحنات';
COMMENT ON COLUMN shipment_status_changes.automation_processed IS 'هل تم معالجة التغيير بواسطة الأتمتة (0=لا، 1=نعم)';

-- إنشاء trigger لتسجيل تغييرات حالات الشحنات
CREATE OR REPLACE TRIGGER trg_shipment_status_change
    AFTER UPDATE OF status ON shipments
    FOR EACH ROW
    WHEN (OLD.status != NEW.status)
BEGIN
    INSERT INTO shipment_status_changes (
        id, shipment_id, old_status, new_status, 
        changed_by, changed_at, automation_processed
    ) VALUES (
        shipment_status_changes_seq.NEXTVAL,
        :NEW.id,
        :OLD.status,
        :NEW.status,
        1, -- يمكن تحديثه لاحقاً لاستخدام المستخدم الحالي
        CURRENT_TIMESTAMP,
        0
    );
END;
/

-- تأكيد التغييرات
COMMIT;

-- عرض ملخص الإنشاء
SELECT 'تم إنشاء جداول مراقبة الأتمتة بنجاح' AS status FROM dual;

PROMPT =====================================================
PROMPT تم إنشاء نظام مراقبة الأتمتة بنجاح!
PROMPT =====================================================
