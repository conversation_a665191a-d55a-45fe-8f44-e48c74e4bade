#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
اختبار شامل للنظام المحاسبي الموحد
Comprehensive Testing for Unified Accounting System
"""

import sys
import os
sys.path.insert(0, os.getcwd())

from oracle_manager import OracleManager
import time

def test_database_structure():
    """اختبار بنية قاعدة البيانات"""
    
    oracle = OracleManager()
    
    print("🔍 اختبار بنية قاعدة البيانات...")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 0
    
    # اختبار وجود الأعمدة الجديدة
    print("\n1️⃣ فحص الأعمدة الجديدة:")
    new_columns = ['BAL', 'BAL_F', 'MONTH_NO', 'YEAR_NO', 'BRANCH_ID']
    
    for column in new_columns:
        total_tests += 1
        try:
            query = f"""
            SELECT COUNT(*) FROM user_tab_columns 
            WHERE table_name = 'BALANCE_TRANSACTIONS' AND column_name = '{column}'
            """
            result = oracle.execute_query(query)
            
            if result and result[0][0] > 0:
                print(f"   ✅ {column}: موجود")
                tests_passed += 1
            else:
                print(f"   ❌ {column}: غير موجود")
        except Exception as e:
            print(f"   ❌ {column}: خطأ - {str(e)}")
    
    # اختبار الفهارس
    print("\n2️⃣ فحص الفهارس المحسنة:")
    expected_indexes = ['IDX_BT_ENT_BAL', 'IDX_BT_PERIOD', 'IDX_BT_BRANCH', 'IDX_BT_DOC']
    
    for index in expected_indexes:
        total_tests += 1
        try:
            query = f"""
            SELECT COUNT(*) FROM user_indexes 
            WHERE index_name = '{index}' AND table_name = 'BALANCE_TRANSACTIONS'
            """
            result = oracle.execute_query(query)
            
            if result and result[0][0] > 0:
                print(f"   ✅ {index}: موجود")
                tests_passed += 1
            else:
                print(f"   ❌ {index}: غير موجود")
        except Exception as e:
            print(f"   ❌ {index}: خطأ - {str(e)}")
    
    # اختبار القيود
    print("\n3️⃣ فحص قيود قاعدة البيانات:")
    expected_constraints = ['CHK_BT_MONTH_NO', 'CHK_BT_YEAR_NO', 'CHK_BT_BRANCH_ID']
    
    for constraint in expected_constraints:
        total_tests += 1
        try:
            query = f"""
            SELECT COUNT(*) FROM user_constraints 
            WHERE constraint_name = '{constraint}' AND table_name = 'BALANCE_TRANSACTIONS'
            """
            result = oracle.execute_query(query)
            
            if result and result[0][0] > 0:
                print(f"   ✅ {constraint}: موجود")
                tests_passed += 1
            else:
                print(f"   ❌ {constraint}: غير موجود")
        except Exception as e:
            print(f"   ❌ {constraint}: خطأ - {str(e)}")
    
    print(f"\nنتيجة اختبار بنية قاعدة البيانات: {tests_passed}/{total_tests}")
    return tests_passed, total_tests

def test_entity_types():
    """اختبار أنواع الكيانات الجديدة"""
    
    oracle = OracleManager()
    
    print("\n👥 اختبار أنواع الكيانات الجديدة...")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 0
    
    new_entity_types = ['PURCHASE_AGENT', 'SALES_AGENT', 'SHIPPING_COMPANY']
    
    for entity_type in new_entity_types:
        total_tests += 1
        try:
            query = """
            SELECT COUNT(*) FROM ENTITY_TYPES 
            WHERE entity_type_code = :entity_type
            """
            result = oracle.execute_query(query, {"entity_type": entity_type})
            
            if result and result[0][0] > 0:
                print(f"   ✅ {entity_type}: موجود")
                tests_passed += 1
            else:
                print(f"   ❌ {entity_type}: غير موجود")
        except Exception as e:
            print(f"   ❌ {entity_type}: خطأ - {str(e)}")
    
    print(f"\nنتيجة اختبار أنواع الكيانات: {tests_passed}/{total_tests}")
    return tests_passed, total_tests

def test_packages():
    """اختبار الـ Packages"""
    
    oracle = OracleManager()
    
    print("\n📦 اختبار الـ Packages...")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 0
    
    # اختبار OB_PKG
    print("\n1️⃣ اختبار OB_PKG:")
    ob_functions = ['INSERT_BAL', 'GET_BAL', 'EXISTS_BAL', 'VALIDATE_BAL']
    
    for func in ob_functions:
        total_tests += 1
        try:
            query = f"""
            SELECT COUNT(*) FROM user_procedures 
            WHERE object_name = 'OB_PKG' AND procedure_name = '{func}'
            """
            result = oracle.execute_query(query)
            
            if result and result[0][0] > 0:
                print(f"   ✅ OB_PKG.{func}: موجود")
                tests_passed += 1
            else:
                print(f"   ❌ OB_PKG.{func}: غير موجود")
        except Exception as e:
            print(f"   ❌ OB_PKG.{func}: خطأ - {str(e)}")
    
    # اختبار BT_PKG
    print("\n2️⃣ اختبار BT_PKG:")
    bt_functions = ['POST_TXN', 'GET_BAL', 'REVERSE_TXN', 'VALIDATE_TXN']
    
    for func in bt_functions:
        total_tests += 1
        try:
            query = f"""
            SELECT COUNT(*) FROM user_procedures 
            WHERE object_name = 'BT_PKG' AND procedure_name = '{func}'
            """
            result = oracle.execute_query(query)
            
            if result and result[0][0] > 0:
                print(f"   ✅ BT_PKG.{func}: موجود")
                tests_passed += 1
            else:
                print(f"   ❌ BT_PKG.{func}: غير موجود")
        except Exception as e:
            print(f"   ❌ BT_PKG.{func}: خطأ - {str(e)}")
    
    print(f"\nنتيجة اختبار الـ Packages: {tests_passed}/{total_tests}")
    return tests_passed, total_tests

def test_views():
    """اختبار الـ Views"""
    
    oracle = OracleManager()
    
    print("\n👁️ اختبار الـ Views...")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 0
    
    expected_views = ['V_CURR_BAL', 'V_MONTH_BAL', 'V_ENT_SUM', 'V_TXN_HIST', 'V_BRANCH_SUM']
    
    for view in expected_views:
        total_tests += 1
        try:
            query = f"""
            SELECT COUNT(*) FROM user_views 
            WHERE view_name = '{view}'
            """
            result = oracle.execute_query(query)
            
            if result and result[0][0] > 0:
                print(f"   ✅ {view}: موجود")
                tests_passed += 1
                
                # اختبار تشغيل الـ View
                try:
                    test_query = f"SELECT COUNT(*) FROM {view}"
                    test_result = oracle.execute_query(test_query)
                    if test_result:
                        print(f"      📊 عدد السجلات: {test_result[0][0]}")
                except Exception as e:
                    print(f"      ⚠️ خطأ في تشغيل الـ View: {str(e)}")
            else:
                print(f"   ❌ {view}: غير موجود")
        except Exception as e:
            print(f"   ❌ {view}: خطأ - {str(e)}")
    
    print(f"\nنتيجة اختبار الـ Views: {tests_passed}/{total_tests}")
    return tests_passed, total_tests

def test_performance():
    """اختبار الأداء"""
    
    oracle = OracleManager()
    
    print("\n⚡ اختبار الأداء...")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 0
    
    performance_tests = [
        {
            "name": "استعلام الرصيد الحالي",
            "query": "SELECT BT_PKG.GET_BAL('SUPPLIER', 1, 'USD', 1) FROM DUAL",
            "max_time": 0.1  # 100ms
        },
        {
            "name": "تقرير الأرصدة الحالية",
            "query": "SELECT COUNT(*) FROM V_CURR_BAL",
            "max_time": 0.5  # 500ms
        },
        {
            "name": "تقرير شهري",
            "query": "SELECT COUNT(*) FROM V_MONTH_BAL WHERE year_no = 2025",
            "max_time": 0.5  # 500ms
        },
        {
            "name": "ملخص الكيانات",
            "query": "SELECT COUNT(*) FROM V_ENT_SUM",
            "max_time": 1.0  # 1 second
        }
    ]
    
    for test in performance_tests:
        total_tests += 1
        try:
            start_time = time.time()
            result = oracle.execute_query(test["query"])
            end_time = time.time()
            
            execution_time = end_time - start_time
            
            if execution_time <= test["max_time"]:
                print(f"   ✅ {test['name']}: {execution_time*1000:.1f}ms (ممتاز)")
                tests_passed += 1
            else:
                print(f"   ⚠️ {test['name']}: {execution_time*1000:.1f}ms (بطيء)")
                
        except Exception as e:
            print(f"   ❌ {test['name']}: خطأ - {str(e)}")
    
    print(f"\nنتيجة اختبار الأداء: {tests_passed}/{total_tests}")
    return tests_passed, total_tests

def test_functional_scenarios():
    """اختبار السيناريوهات الوظيفية"""
    
    oracle = OracleManager()
    
    print("\n🎯 اختبار السيناريوهات الوظيفية...")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 0
    
    # سيناريو 1: إدراج رصيد افتتاحي
    print("\n1️⃣ سيناريو إدراج رصيد افتتاحي:")
    total_tests += 1
    try:
        # محاولة إدراج رصيد افتتاحي جديد
        test_query = """
        BEGIN
            OB_PKG.INSERT_BAL(
                p_ent_type => 'SUPPLIER',
                p_ent_id => 999,
                p_curr => 'USD',
                p_amount => 10000,
                p_branch => 1,
                p_user => 1
            );
        END;
        """
        
        oracle.execute_update(test_query)
        
        # التحقق من الرصيد
        balance_query = "SELECT OB_PKG.GET_BAL('SUPPLIER', 999, 'USD', 1) FROM DUAL"
        balance = oracle.execute_query(balance_query)
        
        if balance and balance[0][0] == 10000:
            print("   ✅ إدراج وقراءة الرصيد الافتتاحي: نجح")
            tests_passed += 1
        else:
            print("   ❌ إدراج وقراءة الرصيد الافتتاحي: فشل")
            
    except Exception as e:
        if "already exists" in str(e).lower():
            print("   ✅ إدراج وقراءة الرصيد الافتتاحي: موجود مسبقاً")
            tests_passed += 1
        else:
            print(f"   ❌ إدراج وقراءة الرصيد الافتتاحي: خطأ - {str(e)}")
    
    # سيناريو 2: ترحيل معاملة
    print("\n2️⃣ سيناريو ترحيل معاملة:")
    total_tests += 1
    try:
        # ترحيل معاملة جديدة
        txn_query = """
        BEGIN
            BT_PKG.POST_TXN(
                p_ent_type => 'SUPPLIER',
                p_ent_id => 999,
                p_doc_type => 'TRANSFER',
                p_doc_no => 'TEST-FINAL-001',
                p_curr => 'USD',
                p_dr => 5000,
                p_cr => 0,
                p_desc => 'Final system test',
                p_branch => 1,
                p_user => 1
            );
        END;
        """
        
        oracle.execute_update(txn_query)
        
        # التحقق من الرصيد الجديد
        new_balance_query = "SELECT BT_PKG.GET_BAL('SUPPLIER', 999, 'USD', 1) FROM DUAL"
        new_balance = oracle.execute_query(new_balance_query)
        
        if new_balance and new_balance[0][0] == 15000:  # 10000 + 5000
            print("   ✅ ترحيل معاملة وتحديث الرصيد: نجح")
            tests_passed += 1
        else:
            print(f"   ❌ ترحيل معاملة وتحديث الرصيد: فشل (الرصيد: {new_balance[0][0] if new_balance else 'N/A'})")
            
    except Exception as e:
        if "already exists" in str(e).lower():
            print("   ✅ ترحيل معاملة وتحديث الرصيد: موجود مسبقاً")
            tests_passed += 1
        else:
            print(f"   ❌ ترحيل معاملة وتحديث الرصيد: خطأ - {str(e)}")
    
    # سيناريو 3: عرض التقارير
    print("\n3️⃣ سيناريو عرض التقارير:")
    total_tests += 1
    try:
        # عرض الرصيد من الـ View
        view_query = """
        SELECT curr_bal FROM V_CURR_BAL 
        WHERE ent_type = 'SUPPLIER' AND ent_id = 999 AND curr = 'USD'
        """
        
        view_result = oracle.execute_query(view_query)
        
        if view_result and len(view_result) > 0:
            print(f"   ✅ عرض التقارير من الـ Views: نجح (الرصيد: {view_result[0][0]})")
            tests_passed += 1
        else:
            print("   ❌ عرض التقارير من الـ Views: فشل")
            
    except Exception as e:
        print(f"   ❌ عرض التقارير من الـ Views: خطأ - {str(e)}")
    
    print(f"\nنتيجة اختبار السيناريوهات الوظيفية: {tests_passed}/{total_tests}")
    return tests_passed, total_tests

def test_data_integrity():
    """اختبار سلامة البيانات"""
    
    oracle = OracleManager()
    
    print("\n🛡️ اختبار سلامة البيانات...")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 0
    
    # اختبار تطابق الأرصدة
    print("\n1️⃣ اختبار تطابق الأرصدة:")
    total_tests += 1
    try:
        # مقارنة الأرصدة بين الطرق المختلفة
        direct_query = """
        SELECT entity_type_code, entity_id, currency_code, SUM(BAL) as direct_balance
        FROM BALANCE_TRANSACTIONS 
        WHERE status = 'POSTED'
        GROUP BY entity_type_code, entity_id, currency_code
        HAVING SUM(BAL) != 0
        """
        
        view_query = """
        SELECT ent_type, ent_id, curr, curr_bal as view_balance
        FROM V_CURR_BAL
        """
        
        direct_result = oracle.execute_query(direct_query)
        view_result = oracle.execute_query(view_query)
        
        if len(direct_result) == len(view_result):
            print("   ✅ تطابق عدد الأرصدة بين الاستعلام المباشر والـ View")
            tests_passed += 1
        else:
            print(f"   ❌ عدم تطابق عدد الأرصدة: مباشر={len(direct_result)}, View={len(view_result)}")
            
    except Exception as e:
        print(f"   ❌ اختبار تطابق الأرصدة: خطأ - {str(e)}")
    
    # اختبار القيود
    print("\n2️⃣ اختبار القيود:")
    constraint_tests = [
        {
            "name": "MONTH_NO خارج النطاق",
            "query": "INSERT INTO BALANCE_TRANSACTIONS (entity_type_code, entity_id, document_type_code, document_number, document_date, currency_code, month_no, year_no, branch_id) VALUES ('SUPPLIER', 9999, 'TEST', 'CONSTRAINT-TEST-1', SYSDATE, 'USD', 13, 2025, 1)",
            "should_fail": True
        },
        {
            "name": "BRANCH_ID سالب",
            "query": "INSERT INTO BALANCE_TRANSACTIONS (entity_type_code, entity_id, document_type_code, document_number, document_date, currency_code, month_no, year_no, branch_id) VALUES ('SUPPLIER', 9999, 'TEST', 'CONSTRAINT-TEST-2', SYSDATE, 'USD', 12, 2025, -1)",
            "should_fail": True
        }
    ]
    
    for test in constraint_tests:
        total_tests += 1
        try:
            oracle.execute_update(test["query"])
            
            if test["should_fail"]:
                print(f"   ❌ {test['name']}: فشل - كان يجب أن يرفض الإدراج")
            else:
                print(f"   ✅ {test['name']}: نجح")
                tests_passed += 1
                
        except Exception as e:
            if test["should_fail"]:
                print(f"   ✅ {test['name']}: نجح - رفض الإدراج كما متوقع")
                tests_passed += 1
            else:
                print(f"   ❌ {test['name']}: فشل - {str(e)}")
    
    print(f"\nنتيجة اختبار سلامة البيانات: {tests_passed}/{total_tests}")
    return tests_passed, total_tests

def generate_final_report():
    """إنشاء التقرير النهائي"""
    
    oracle = OracleManager()
    
    print("\n📋 التقرير النهائي للنظام...")
    print("=" * 70)
    
    # إحصائيات النظام
    try:
        # عدد المعاملات
        txn_count_query = "SELECT COUNT(*) FROM BALANCE_TRANSACTIONS"
        txn_count = oracle.execute_query(txn_count_query)
        
        # عدد أنواع الكيانات
        entity_types_query = "SELECT COUNT(*) FROM ENTITY_TYPES"
        entity_types_count = oracle.execute_query(entity_types_query)
        
        # عدد الفهارس
        indexes_query = "SELECT COUNT(*) FROM user_indexes WHERE table_name = 'BALANCE_TRANSACTIONS'"
        indexes_count = oracle.execute_query(indexes_query)
        
        # عدد الـ Views
        views_query = "SELECT COUNT(*) FROM user_views WHERE view_name LIKE 'V_%'"
        views_count = oracle.execute_query(views_query)
        
        # عدد الـ Packages
        packages_query = "SELECT COUNT(DISTINCT object_name) FROM user_objects WHERE object_type = 'PACKAGE' AND object_name IN ('OB_PKG', 'BT_PKG')"
        packages_count = oracle.execute_query(packages_query)
        
        print("📊 إحصائيات النظام:")
        print(f"   المعاملات: {txn_count[0][0] if txn_count else 'N/A'}")
        print(f"   أنواع الكيانات: {entity_types_count[0][0] if entity_types_count else 'N/A'}")
        print(f"   الفهارس: {indexes_count[0][0] if indexes_count else 'N/A'}")
        print(f"   الـ Views: {views_count[0][0] if views_count else 'N/A'}")
        print(f"   الـ Packages: {packages_count[0][0] if packages_count else 'N/A'}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الإحصائيات: {str(e)}")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء الاختبار الشامل للنظام المحاسبي الموحد")
    print("=" * 80)
    
    total_passed = 0
    total_tests = 0
    
    try:
        # 1. اختبار بنية قاعدة البيانات
        passed, tests = test_database_structure()
        total_passed += passed
        total_tests += tests
        
        # 2. اختبار أنواع الكيانات
        passed, tests = test_entity_types()
        total_passed += passed
        total_tests += tests
        
        # 3. اختبار الـ Packages
        passed, tests = test_packages()
        total_passed += passed
        total_tests += tests
        
        # 4. اختبار الـ Views
        passed, tests = test_views()
        total_passed += passed
        total_tests += tests
        
        # 5. اختبار الأداء
        passed, tests = test_performance()
        total_passed += passed
        total_tests += tests
        
        # 6. اختبار السيناريوهات الوظيفية
        passed, tests = test_functional_scenarios()
        total_passed += passed
        total_tests += tests
        
        # 7. اختبار سلامة البيانات
        passed, tests = test_data_integrity()
        total_passed += passed
        total_tests += tests
        
        # 8. التقرير النهائي
        generate_final_report()
        
        # النتيجة النهائية
        success_rate = (total_passed / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"\n🎯 النتيجة النهائية للاختبار الشامل:")
        print(f"   نجح: {total_passed}/{total_tests} ({success_rate:.1f}%)")
        
        if success_rate >= 90:
            print("🎉 النظام يعمل بشكل ممتاز!")
            print("✅ المهمة Ej8Uy8Uy8Uy8Uy8Uy8Uy9 مكتملة!")
            return True
        elif success_rate >= 75:
            print("⚠️ النظام يعمل بشكل جيد مع بعض التحسينات المطلوبة")
            return True
        else:
            print("❌ النظام يحتاج إلى مراجعة وإصلاحات")
            return False
            
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n" + "=" * 80)
        print("🏁 انتهى الاختبار الشامل بنجاح!")
        print("🎉 النظام المحاسبي الموحد جاهز للاستخدام!")
    else:
        print("\n" + "=" * 80)
        print("🚫 فشل الاختبار الشامل - يرجى مراجعة الأخطاء")
