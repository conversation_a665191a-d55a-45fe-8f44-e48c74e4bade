{% extends "base.html" %}

{% block content %}
<style>
.notification-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #007bff;
}

.notification-card.success {
    border-left-color: #28a745;
}

.notification-card.warning {
    border-left-color: #ffc107;
}

.notification-card.error {
    border-left-color: #dc3545;
}

.notification-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.notification-type-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
}

.stats-widget {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
}

.template-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
}

.template-card:hover {
    background: #e3f2fd;
    border-color: #2196f3;
}

.notification-preview {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.realtime-indicator {
    animation: pulse 2s infinite;
    color: #28a745;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.filter-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}
</style>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-bell text-primary me-2"></i>
                        نظام الإشعارات المتطور
                        <span class="realtime-indicator ms-2">
                            <i class="fas fa-circle"></i>
                        </span>
                    </h1>
                    <p class="text-muted mb-0">إدارة وإرسال الإشعارات للعملاء والسائقين</p>
                </div>
                <div>
                    <button class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#sendNotificationModal">
                        <i class="fas fa-paper-plane me-2"></i>
                        إرسال إشعار
                    </button>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTemplateModal">
                        <i class="fas fa-plus me-2"></i>
                        قالب جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Row -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-widget">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-paper-plane fa-2x"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1">إجمالي الإشعارات</h6>
                        <h3 class="mb-0">{{ stats.total_notifications or 0 }}</h3>
                        <small>اليوم: +{{ stats.today_notifications or 0 }}</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient rounded-circle p-3">
                                <i class="fas fa-check text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">تم الإرسال</h6>
                            <h3 class="mb-0">{{ stats.sent_notifications or 0 }}</h3>
                            <small class="text-success">{{ "%.1f"|format(stats.success_rate or 0) }}% نجاح</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient rounded-circle p-3">
                                <i class="fas fa-clock text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">في الانتظار</h6>
                            <h3 class="mb-0">{{ stats.pending_notifications or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-danger bg-gradient rounded-circle p-3">
                                <i class="fas fa-times text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">فشل الإرسال</h6>
                            <h3 class="mb-0">{{ stats.failed_notifications or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-section">
        <div class="row align-items-end">
            <div class="col-md-2">
                <label class="form-label">نوع الإشعار</label>
                <select class="form-select" id="typeFilter">
                    <option value="">جميع الأنواع</option>
                    <option value="SMS">رسائل SMS</option>
                    <option value="EMAIL">بريد إلكتروني</option>
                    <option value="PUSH">إشعارات فورية</option>
                    <option value="WHATSAPP">واتساب</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select class="form-select" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="مرسل">تم الإرسال</option>
                    <option value="معلق">في الانتظار</option>
                    <option value="فشل">فشل</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="startDate">
            </div>
            <div class="col-md-2">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="endDate">
            </div>
            <div class="col-md-2">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" id="searchInput" placeholder="رقم الهاتف أو البريد">
            </div>
            <div class="col-md-2">
                <button class="btn btn-primary w-100" onclick="filterNotifications()">
                    <i class="fas fa-search me-2"></i>
                    بحث
                </button>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <ul class="nav nav-tabs mb-4" id="notificationTabs">
        <li class="nav-item">
            <a class="nav-link active" data-bs-toggle="tab" href="#notifications-list">
                <i class="fas fa-list me-2"></i>
                قائمة الإشعارات
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" href="#templates">
                <i class="fas fa-file-alt me-2"></i>
                القوالب
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" href="#bulk-send">
                <i class="fas fa-broadcast-tower me-2"></i>
                الإرسال الجماعي
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" href="#analytics">
                <i class="fas fa-chart-bar me-2"></i>
                التحليلات
            </a>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content">
        <!-- Notifications List -->
        <div class="tab-pane fade show active" id="notifications-list">
            <div class="row">
                {% if notifications %}
                    {% for notification in notifications %}
                    <div class="col-lg-6 col-xl-4 mb-3">
                        <div class="card notification-card {{ 'success' if notification.status == 'مرسل' else 'warning' if notification.status == 'معلق' else 'error' }}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <span class="notification-type-badge bg-{{ 'primary' if notification.notification_type == 'SMS' else 'info' if notification.notification_type == 'EMAIL' else 'success' if notification.notification_type == 'PUSH' else 'warning' }}">
                                        {{ notification.notification_type }}
                                    </span>
                                    <span class="badge bg-{{ 'success' if notification.status == 'مرسل' else 'warning' if notification.status == 'معلق' else 'danger' }}">
                                        {{ notification.status }}
                                    </span>
                                </div>
                                
                                <h6 class="card-title">{{ notification.subject or 'بدون موضوع' }}</h6>
                                <p class="card-text text-muted small">{{ notification.message[:100] }}...</p>
                                
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        {{ notification.recipient }}
                                    </small>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ notification.created_at.strftime('%Y-%m-%d %H:%M') if notification.created_at else 'غير محدد' }}
                                    </small>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="viewNotification({{ notification.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        {% if notification.status == 'فشل' %}
                                        <button class="btn btn-outline-warning" onclick="resendNotification({{ notification.id }})">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-bell-slash text-muted mb-3" style="font-size: 4rem;"></i>
                            <h3 class="text-muted">لا توجد إشعارات</h3>
                            <p class="text-muted">لم يتم إرسال أي إشعارات بعد</p>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Templates -->
        <div class="tab-pane fade" id="templates">
            <div class="row">
                {% for template in notification_templates %}
                <div class="col-lg-6 mb-3">
                    <div class="template-card">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="mb-0">{{ template.name }}</h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editTemplate({{ template.id }})">
                                        <i class="fas fa-edit me-2"></i>تعديل
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="useTemplate({{ template.id }})">
                                        <i class="fas fa-paper-plane me-2"></i>استخدام
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteTemplate({{ template.id }})">
                                        <i class="fas fa-trash me-2"></i>حذف
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <p class="text-muted small mb-2">{{ template.description }}</p>
                        <div class="notification-preview">
                            <strong>{{ template.subject }}</strong>
                            <p class="mb-0 mt-1">{{ template.content[:150] }}...</p>
                        </div>
                        <div class="mt-2">
                            <span class="badge bg-secondary">{{ template.type }}</span>
                            <small class="text-muted ms-2">استخدم {{ template.usage_count or 0 }} مرة</small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Bulk Send -->
        <div class="tab-pane fade" id="bulk-send">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">الإرسال الجماعي للإشعارات</h5>
                </div>
                <div class="card-body">
                    <form id="bulkSendForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">نوع الإشعار</label>
                                <select class="form-select" name="notification_type" required>
                                    <option value="">اختر نوع الإشعار</option>
                                    <option value="SMS">رسائل SMS</option>
                                    <option value="EMAIL">بريد إلكتروني</option>
                                    <option value="PUSH">إشعارات فورية</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المستهدفين</label>
                                <select class="form-select" name="target_group" required>
                                    <option value="">اختر المجموعة المستهدفة</option>
                                    <option value="all_customers">جميع العملاء</option>
                                    <option value="active_shipments">عملاء الشحنات النشطة</option>
                                    <option value="drivers">جميع السائقين</option>
                                    <option value="custom">قائمة مخصصة</option>
                                </select>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label">موضوع الرسالة</label>
                                <input type="text" class="form-control" name="subject" placeholder="موضوع الإشعار">
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label">محتوى الرسالة</label>
                                <textarea class="form-control" name="message" rows="4" placeholder="اكتب محتوى الإشعار هنا..." required></textarea>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    إرسال جماعي
                                </button>
                                <button type="button" class="btn btn-outline-secondary ms-2" onclick="previewBulkMessage()">
                                    <i class="fas fa-eye me-2"></i>
                                    معاينة
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Analytics -->
        <div class="tab-pane fade" id="analytics">
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-0">
                            <h6 class="mb-0">معدل نجاح الإرسال</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="successRateChart" height="200"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-0">
                            <h6 class="mb-0">توزيع أنواع الإشعارات</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="typeDistributionChart" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Send Notification Modal -->
<div class="modal fade" id="sendNotificationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إرسال إشعار جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="sendNotificationForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">نوع الإشعار</label>
                            <select class="form-select" name="notification_type" required>
                                <option value="">اختر النوع</option>
                                <option value="SMS">رسالة SMS</option>
                                <option value="EMAIL">بريد إلكتروني</option>
                                <option value="PUSH">إشعار فوري</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">المستقبل</label>
                            <input type="text" class="form-control" name="recipient" placeholder="رقم الهاتف أو البريد الإلكتروني" required>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">الموضوع</label>
                            <input type="text" class="form-control" name="subject" placeholder="موضوع الإشعار">
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">الرسالة</label>
                            <textarea class="form-control" name="message" rows="4" placeholder="اكتب رسالتك هنا..." required></textarea>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="send_immediately" id="send_immediately" checked>
                                <label class="form-check-label" for="send_immediately">
                                    إرسال فوري
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-paper-plane me-2"></i>
                        إرسال
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// تهيئة الرسوم البيانية
document.addEventListener('DOMContentLoaded', function() {
    // رسم بياني لمعدل النجاح
    const successCtx = document.getElementById('successRateChart').getContext('2d');
    new Chart(successCtx, {
        type: 'line',
        data: {
            labels: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
            datasets: [{
                label: 'معدل النجاح (%)',
                data: [95, 92, 98, 94, 96, 93, 97],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });

    // رسم بياني لتوزيع الأنواع
    const typeCtx = document.getElementById('typeDistributionChart').getContext('2d');
    new Chart(typeCtx, {
        type: 'doughnut',
        data: {
            labels: ['SMS', 'EMAIL', 'PUSH', 'WHATSAPP'],
            datasets: [{
                data: [45, 30, 20, 5],
                backgroundColor: ['#007bff', '#17a2b8', '#28a745', '#25d366']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
});

// إرسال إشعار جديد
document.getElementById('sendNotificationForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('{{ url_for("shipments.send_notification") }}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إرسال الإشعار بنجاح');
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في إرسال الإشعار');
    });
});

// الإرسال الجماعي
document.getElementById('bulkSendForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    if (confirm('هل أنت متأكد من الإرسال الجماعي؟')) {
        alert('الإرسال الجماعي - قريباً');
    }
});

// وظائف أخرى
function filterNotifications() {
    alert('تصفية الإشعارات - قريباً');
}

function viewNotification(id) {
    alert(`عرض الإشعار ${id} - قريباً`);
}

function resendNotification(id) {
    if (confirm('هل تريد إعادة إرسال هذا الإشعار؟')) {
        alert(`إعادة إرسال الإشعار ${id} - قريباً`);
    }
}

function editTemplate(id) {
    alert(`تعديل القالب ${id} - قريباً`);
}

function useTemplate(id) {
    alert(`استخدام القالب ${id} - قريباً`);
}

function deleteTemplate(id) {
    if (confirm('هل أنت متأكد من حذف هذا القالب؟')) {
        alert(`حذف القالب ${id} - قريباً`);
    }
}

function previewBulkMessage() {
    alert('معاينة الرسالة الجماعية - قريباً');
}

// تحديث تلقائي كل 30 ثانية
setInterval(() => {
    // تحديث الإحصائيات
    fetch('{{ url_for("shipments.notification_stats") }}')
        .then(response => response.json())
        .then(data => {
            // تحديث الإحصائيات في الواجهة
        })
        .catch(error => console.error('Error updating stats:', error));
}, 30000);
</script>

{% endblock %}
