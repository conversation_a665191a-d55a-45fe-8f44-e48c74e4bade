<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة وثائق أمر الشراء - {{ purchase_order.po_number }}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .container-fluid {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .content-section {
            padding: 30px;
        }

        .po-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #3498db;
        }

        .stats-row {
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-top: 4px solid #3498db;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .upload-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .documents-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .document-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #28a745;
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .document-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-left-color: #20c997;
        }

        .upload-area {
            border: 2px dashed #3498db;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            background: #e3f2fd;
            border-color: #2980b9;
        }

        .upload-area.dragover {
            background: #e3f2fd;
            border-color: #2980b9;
            transform: scale(1.02);
        }

        .file-list {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .btn-upload {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-upload:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
            color: white;
        }

        .no-documents {
            text-align: center;
            padding: 60px;
            color: #7f8c8d;
        }

        .progress-upload {
            display: none;
            margin-top: 15px;
        }

        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }

        .form-select, .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-select:focus, .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
        }

        .btn-download {
            background: linear-gradient(135deg, #17a2b8, #138496);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85em;
            transition: all 0.3s ease;
        }

        .btn-download:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4);
            color: white;
        }

        .btn-delete {
            background: linear-gradient(135deg, #dc3545, #c82333);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85em;
            transition: all 0.3s ease;
        }

        .btn-delete:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
            color: white;
        }

        /* تحسين أزرار الإجراءات */
        .btn-group-sm .btn {
            margin: 0;
            transition: all 0.2s ease;
            border-radius: 0.25rem !important;
        }

        .btn-group-sm .btn:first-child {
            border-top-left-radius: 0.375rem !important;
            border-bottom-left-radius: 0.375rem !important;
        }

        .btn-group-sm .btn:last-child {
            border-top-right-radius: 0.375rem !important;
            border-bottom-right-radius: 0.375rem !important;
        }

        .btn-group-sm .btn:hover {
            transform: translateY(-1px);
            z-index: 2;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .dropdown-menu {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            border: none;
        }

        .dropdown-item {
            padding: 8px 16px;
            transition: all 0.2s ease;
        }

        .dropdown-item:hover {
            background: #f8f9fa;
            transform: translateX(5px);
        }

        .btn-back {
            background: linear-gradient(135deg, #6c757d, #5a6268);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-back:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(108, 117, 125, 0.4);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="header-section">
            <h1 class="mb-3">
                <i class="fas fa-shopping-cart me-3"></i>
                إدارة وثائق أمر الشراء
            </h1>
            <h4 class="mb-0">أمر الشراء رقم: {{ purchase_order.po_number }}</h4>
            <p class="mb-0 opacity-75">{{ purchase_order.supplier_name }}</p>
            <div class="mt-3">
                <a href="{{ url_for('purchase_orders.index') }}" class="btn-back">
                    <i class="fas fa-arrow-left me-2"></i>العودة لقائمة أوامر الشراء
                </a>
            </div>
        </div>

        <div class="content-section">
            <!-- معلومات أمر الشراء -->
            <div class="po-info">
                <div class="row">
                    <div class="col-md-3">
                        <strong>رقم الأمر:</strong><br>
                        <span class="text-primary">{{ purchase_order.po_number }}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>المورد:</strong><br>
                        {{ purchase_order.supplier_name }}
                    </div>
                    <div class="col-md-3">
                        <strong>تاريخ الأمر:</strong><br>
                        {% if purchase_order.po_date %}
                            {{ purchase_order.po_date.strftime('%Y-%m-%d') }}
                        {% else %}
                            غير محدد
                        {% endif %}
                    </div>
                    <div class="col-md-3">
                        <strong>قيمة الأمر:</strong><br>
                        <span class="text-success fw-bold">
                            {{ "{:,.2f}".format(purchase_order.total_amount or 0) }}
                            {{ purchase_order.currency or 'ريال' }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- إحصائيات الوثائق -->
            <div class="row stats-row">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number">{{ documents|length }}</div>
                        <div class="stat-label">إجمالي الوثائق</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number">0</div>
                        <div class="stat-label">الحجم (MB)</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number">{{ documents|length }}</div>
                        <div class="stat-label">وثائق مرفوعة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number">0</div>
                        <div class="stat-label">وثائق معلقة</div>
                    </div>
                </div>
            </div>

            <!-- منطقة رفع الوثائق -->
            <div class="upload-section">
                <h5 class="mb-3">
                    <i class="fas fa-cloud-upload-alt me-2"></i>
                    رفع وثيقة جديدة
                </h5>

                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">نوع الوثيقة:</label>
                            <select class="form-select" name="document_type" required>
                                <option value="">اختر نوع الوثيقة</option>
                                <option value="purchase_order">أمر الشراء الأصلي</option>
                                <option value="amendment">تعديل أمر الشراء</option>
                                <option value="invoice">فاتورة</option>
                                <option value="receipt">إيصال</option>
                                <option value="certificate">شهادة</option>
                                <option value="specification">مواصفات المنتج</option>
                                <option value="quality_report">تقرير جودة</option>
                                <option value="shipping_document">وثيقة شحن</option>
                                <option value="customs_document">وثيقة جمركية</option>
                                <option value="insurance">وثيقة تأمين</option>
                                <option value="correspondence">مراسلات</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">اسم الوثيقة:</label>
                            <input type="text" class="form-control" name="document_name"
                                   placeholder="اسم الوثيقة (اختياري)">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">ملاحظات:</label>
                            <input type="text" class="form-control" name="notes"
                                   placeholder="ملاحظات (اختياري)">
                        </div>
                    </div>

                    <div class="upload-area mt-3" id="uploadArea">
                        <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                        <h5>اسحب الملفات هنا أو اضغط للاختيار</h5>
                        <p class="text-muted">يدعم: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG (حد أقصى 10 ميجابايت)</p>
                        <input type="file" id="fileInput" name="files" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png" style="display: none;">
                        <button type="button" class="btn-upload" id="selectFilesBtn">
                            <i class="fas fa-plus me-2"></i>اختيار الملفات
                        </button>
                    </div>

                    <div class="mt-3 text-center">
                        <button type="submit" class="btn btn-success btn-lg" style="display: none;" id="uploadBtn">
                            <i class="fas fa-upload me-2"></i>رفع الوثائق
                        </button>
                    </div>

                    <div class="progress-upload">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted mt-1">جاري رفع الملفات...</small>
                    </div>
                </form>
            </div>

            <!-- قائمة الوثائق -->
            <div class="documents-section">
                <h5 class="mb-3">
                    <i class="fas fa-files me-2"></i>
                    الوثائق المرفقة ({{ documents|length }})
                </h5>

                {% if documents %}
                    {% for document in documents %}
                    <div class="document-card">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h6 class="mb-1">
                                    <i class="fas fa-file-alt me-2 text-primary"></i>
                                    {{ document.title }}
                                </h6>
                                <div class="mb-1">
                                    <span class="badge bg-info me-2">{{ document.document_type }}</span>
                                    <small class="text-muted">
                                        {% if document.original_filename %}
                                            {{ document.original_filename }}
                                        {% else %}
                                            {{ document.file_name }}
                                        {% endif %}
                                    </small>
                                </div>
                                {% if document.description or document.notes %}
                                    <p class="text-muted small mb-1">
                                        <i class="fas fa-sticky-note me-1"></i>
                                        {{ document.description or document.notes }}
                                    </p>
                                {% endif %}
                                <small class="text-muted">
                                    <i class="fas fa-hdd me-1"></i>
                                    الحجم:
                                    {% if document.file_size_mb %}
                                        {{ document.file_size_mb }} ميجابايت
                                    {% elif document.file_size %}
                                        {{ "%.2f"|format(document.file_size / 1024 / 1024) }} ميجابايت
                                    {% else %}
                                        0 ميجابايت
                                    {% endif %} |
                                    <i class="fas fa-calendar me-1"></i>
                                    تاريخ الرفع: {{ document.created_at.strftime('%Y-%m-%d %H:%M') if document.created_at else (document.uploaded_at.strftime('%Y-%m-%d %H:%M') if document.uploaded_at else '-') }} |
                                    <i class="fas fa-user me-1"></i>
                                    بواسطة: {{ document.created_by or document.uploaded_by }}
                                    {% if document.download_count %}
                                        | <i class="fas fa-download me-1"></i>
                                        {{ document.download_count }} تحميل
                                    {% endif %}
                                </small>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group btn-group-sm" role="group">
                                    <button class="btn btn-outline-secondary"
                                            onclick="viewDocumentDetails({{ document.id }})"
                                            title="استعراض تفاصيل الوثيقة">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-info"
                                            onclick="downloadDocument({{ document.id }})"
                                            title="تحميل الوثيقة">
                                        <i class="fas fa-download"></i>
                                    </button>

                                    <!-- أزرار إنشاء الروابط -->
                                    <button class="btn btn-success btn-sm"
                                            onclick="createShareLink({{ document.id }}, 'nextcloud')"
                                            title="إنشاء رابط Nextcloud">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.232 7.005c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zm7.553 0c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zM12.017 18.55c-2.69 0-4.923-1.273-6.096-3.234l1.431-.716c.79 1.431 2.334 2.39 4.665 2.39s3.875-.959 4.665-2.39l1.431.716c-1.173 1.961-3.406 3.234-6.096 3.234z"/>
                                        </svg>
                                        {% if document.nextcloud_share_link %}
                                            <span class="badge bg-success rounded-pill ms-1">●</span>
                                        {% endif %}
                                    </button>

                                    <button class="btn btn-primary btn-sm"
                                            onclick="createShareLink({{ document.id }}, 'onedrive')"
                                            title="إنشاء رابط OneDrive">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M8.9 2.9c-1.6 0-2.9 1.3-2.9 2.9 0 .2 0 .4.1.6C3.6 7.2 1.5 9.7 1.5 12.8c0 3.5 2.8 6.3 6.3 6.3h9.4c2.9 0 5.3-2.4 5.3-5.3 0-2.5-1.7-4.6-4-5.2-.1-3.1-2.7-5.7-5.8-5.7-1.9 0-3.6.9-4.6 2.3-.4-.2-.8-.3-1.2-.3zm0 1.5c.3 0 .6.1.8.2l.4.2.2-.4c.7-1.3 2.1-2.1 3.6-2.1 2.3 0 4.2 1.9 4.2 4.2v.8h.8c2.1 0 3.8 1.7 3.8 3.8s-1.7 3.8-3.8 3.8H7.8c-2.7 0-4.8-2.2-4.8-4.8 0-2.4 1.7-4.4 4-4.7l.5-.1-.1-.5c0-.2-.1-.4-.1-.6 0-.8.6-1.4 1.4-1.4z"/>
                                        </svg>
                                        {% if document.onedrive_share_link %}
                                            <span class="badge bg-primary rounded-pill ms-1">●</span>
                                        {% endif %}
                                    </button>

                                    <!-- قائمة النسخ -->
                                    <div class="btn-group">
                                        <button class="btn btn-warning btn-sm dropdown-toggle"
                                                data-bs-toggle="dropdown"
                                                title="نسخ الروابط"
                                                {% if not (document.nextcloud_share_link or document.onedrive_share_link) %}disabled{% endif %}>
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            {% if document.nextcloud_share_link %}
                                            <li><a class="dropdown-item" onclick="copyShareLink('{{ document.nextcloud_share_link }}')">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="#198754" class="me-2">
                                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.620 0 11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.232 7.005c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zm7.553 0c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zM12.017 18.55c-2.69 0-4.923-1.273-6.096-3.234l1.431-.716c.79 1.431 2.334 2.39 4.665 2.39s3.875-.959 4.665-2.39l1.431.716c-1.173 1.961-3.406 3.234-6.096 3.234z"/>
                                                </svg>
                                                نسخ رابط Nextcloud
                                            </a></li>
                                            {% endif %}
                                            {% if document.onedrive_share_link %}
                                            <li><a class="dropdown-item" onclick="copyShareLink('{{ document.onedrive_share_link }}')">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="#0d6efd" class="me-2">
                                                    <path d="M8.9 2.9c-1.6 0-2.9 1.3-2.9 2.9 0 .2 0 .4.1.6C3.6 7.2 1.5 9.7 1.5 12.8c0 3.5 2.8 6.3 6.3 6.3h9.4c2.9 0 5.3-2.4 5.3-5.3 0-2.5-1.7-4.6-4-5.2-.1-3.1-2.7-5.7-5.8-5.7-1.9 0-3.6.9-4.6 2.3-.4-.2-.8-.3-1.2-.3zm0 1.5c.3 0 .6.1.8.2l.4.2.2-.4c.7-1.3 2.1-2.1 3.6-2.1 2.3 0 4.2 1.9 4.2 4.2v.8h.8c2.1 0 3.8 1.7 3.8 3.8s-1.7 3.8-3.8 3.8H7.8c-2.7 0-4.8-2.2-4.8-4.8 0-2.4 1.7-4.4 4-4.7l.5-.1-.1-.5c0-.2-.1-.4-.1-.6 0-.8.6-1.4 1.4-1.4z"/>
                                                </svg>
                                                نسخ رابط OneDrive
                                            </a></li>
                                            {% endif %}
                                            {% if not (document.nextcloud_share_link or document.onedrive_share_link) %}
                                            <li><span class="dropdown-item-text text-muted">لا توجد روابط متاحة</span></li>
                                            {% endif %}
                                        </ul>
                                    </div>

                                    <!-- قائمة الفتح -->
                                    <div class="btn-group">
                                        <button class="btn btn-secondary btn-sm dropdown-toggle"
                                                data-bs-toggle="dropdown"
                                                title="فتح الروابط"
                                                {% if not (document.nextcloud_share_link or document.onedrive_share_link) %}disabled{% endif %}>
                                            <i class="fas fa-external-link-alt"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            {% if document.nextcloud_share_link %}
                                            <li><a class="dropdown-item" onclick="openShareLink('{{ document.nextcloud_share_link }}')">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="#198754" class="me-2">
                                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.620 0 11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.232 7.005c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zm7.553 0c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zM12.017 18.55c-2.69 0-4.923-1.273-6.096-3.234l1.431-.716c.79 1.431 2.334 2.39 4.665 2.39s3.875-.959 4.665-2.39l1.431.716c-1.173 1.961-3.406 3.234-6.096 3.234z"/>
                                                </svg>
                                                فتح رابط Nextcloud
                                            </a></li>
                                            {% endif %}
                                            {% if document.onedrive_share_link %}
                                            <li><a class="dropdown-item" onclick="openShareLink('{{ document.onedrive_share_link }}')">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="#0d6efd" class="me-2">
                                                    <path d="M8.9 2.9c-1.6 0-2.9 1.3-2.9 2.9 0 .2 0 .4.1.6C3.6 7.2 1.5 9.7 1.5 12.8c0 3.5 2.8 6.3 6.3 6.3h9.4c2.9 0 5.3-2.4 5.3-5.3 0-2.5-1.7-4.6-4-5.2-.1-3.1-2.7-5.7-5.8-5.7-1.9 0-3.6.9-4.6 2.3-.4-.2-.8-.3-1.2-.3zm0 1.5c.3 0 .6.1.8.2l.4.2.2-.4c.7-1.3 2.1-2.1 3.6-2.1 2.3 0 4.2 1.9 4.2 4.2v.8h.8c2.1 0 3.8 1.7 3.8 3.8s-1.7 3.8-3.8 3.8H7.8c-2.7 0-4.8-2.2-4.8-4.8 0-2.4 1.7-4.4 4-4.7l.5-.1-.1-.5c0-.2-.1-.4-.1-.6 0-.8.6-1.4 1.4-1.4z"/>
                                                </svg>
                                                فتح رابط OneDrive
                                            </a></li>
                                            {% endif %}
                                            {% if not (document.nextcloud_share_link or document.onedrive_share_link) %}
                                            <li><span class="dropdown-item-text text-muted">لا توجد روابط متاحة</span></li>
                                            {% endif %}
                                        </ul>
                                    </div>

                                    <button class="btn btn-outline-danger"
                                            onclick="deleteDocument({{ document.id }}, '{{ document.title }}')"
                                            title="حذف الوثيقة">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="no-documents">
                        <i class="fas fa-folder-open fa-4x mb-3"></i>
                        <h5>لا توجد وثائق مرفقة</h5>
                        <p>ابدأ برفع أول وثيقة لأمر الشراء هذا</p>
                    </div>
                {% endif %}
            </div>

            <!-- أزرار التنقل -->
            <div class="text-center mt-4">
                <a href="{{ url_for('purchase_orders.index') }}" class="btn btn-secondary btn-lg me-3">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة لأوامر الشراء
                </a>
                <button class="btn btn-primary btn-lg" onclick="generatePurchaseOrderReport()">
                    <i class="fas fa-file-pdf me-2"></i>
                    إنشاء تقرير أمر الشراء
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // وظائف إدارة الوثائق
        function downloadDocument(documentId) {
            window.location.href = `/purchase-orders/documents/${documentId}/download`;
        }

        function deleteDocument(documentId, documentTitle) {
            if (confirm('هل أنت متأكد من حذف الوثيقة "' + documentTitle + '"؟')) {
                fetch(`/purchase-orders/documents/${documentId}/delete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(data.message, 'success', 'fas fa-check-circle');
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        showToast('خطأ: ' + data.message, 'danger', 'fas fa-exclamation-circle');
                    }
                })
                .catch(error => {
                    console.error('خطأ في حذف الملف:', error);
                    showToast('حدث خطأ في حذف الملف. يرجى المحاولة مرة أخرى.', 'danger', 'fas fa-exclamation-triangle');
                });
            }
        }

        // استعراض تفاصيل الوثيقة
        function viewDocumentDetails(documentId) {
            showToast('جاري تحميل تفاصيل الوثيقة...', 'info', 'fas fa-spinner fa-spin');
            // يمكن إضافة modal لعرض التفاصيل
            setTimeout(() => {
                showToast('سيتم إضافة هذه الميزة قريباً', 'info', 'fas fa-info-circle');
            }, 1000);
        }

        // إنشاء رابط مشاركة (دعم خدمات متعددة)
        function createShareLink(documentId, service) {
            console.log('🔗 تم استدعاء createShareLink:', documentId, service);
            const serviceName = service === 'nextcloud' ? 'Nextcloud' : 'OneDrive';

            if (confirm(`هل تريد إنشاء رابط مشاركة ${serviceName} لهذه الوثيقة؟`)) {
                console.log('✅ تم تأكيد إنشاء الرابط');
                // إظهار مؤشر التحميل
                showToast(`جاري إنشاء رابط ${serviceName}...`, 'info', 'fas fa-spinner fa-spin');

                console.log('📡 إرسال طلب إلى:', `/purchase-orders/documents/${documentId}/create-link`);

                fetch(`/purchase-orders/documents/${documentId}/create-link`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        service: service
                    })
                })
                .then(response => response.json())
                .then(data => {
                    console.log('📄 بيانات الاستجابة:', data);
                    if (data.success) {
                        if (data.is_existing) {
                            showToast(`رابط ${serviceName} موجود مسبقاً`, 'warning', 'fas fa-info-circle');
                        } else {
                            showToast(`تم إنشاء رابط ${serviceName} بنجاح!`, 'success', 'fas fa-check-circle');
                        }

                        // نسخ الرابط للحافظة
                        navigator.clipboard.writeText(data.share_link).then(() => {
                            showToast('تم نسخ الرابط تلقائياً للحافظة', 'info', 'fas fa-copy');
                        });

                        // إعادة تحميل الصفحة لتحديث الأزرار
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        showToast(`خطأ في إنشاء رابط ${serviceName}: ` + data.message, 'danger', 'fas fa-exclamation-circle');
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ في الطلب:', error);
                    showToast(`حدث خطأ في إنشاء رابط ${serviceName}`, 'danger', 'fas fa-exclamation-triangle');
                });
            }
        }

        // نسخ رابط المشاركة
        function copyShareLink(shareLink) {
            if (shareLink && shareLink.trim() !== '') {
                navigator.clipboard.writeText(shareLink).then(() => {
                    showToast('تم نسخ رابط المشاركة بنجاح!', 'success', 'fas fa-check-circle');
                }).catch(err => {
                    showToast('فشل في نسخ الرابط: ' + err, 'danger', 'fas fa-exclamation-circle');
                });
            } else {
                showToast('رابط المشاركة غير متوفر', 'warning', 'fas fa-exclamation-triangle');
            }
        }

        // فتح رابط المشاركة في نافذة جديدة
        function openShareLink(shareLink) {
            if (shareLink && shareLink.trim() !== '') {
                window.open(shareLink, '_blank', 'noopener,noreferrer');
                showToast('تم فتح رابط المشاركة في نافذة جديدة', 'info', 'fas fa-external-link-alt');
            } else {
                showToast('رابط المشاركة غير متوفر', 'warning', 'fas fa-exclamation-triangle');
            }
        }

        // إنشاء تقرير أمر الشراء
        function generatePurchaseOrderReport() {
            const poId = window.location.pathname.split('/')[2]; // استخراج po_id من URL
            console.log('إنشاء تقرير أمر الشراء:', poId);
            showToast('جاري إنشاء تقرير أمر الشراء...', 'info', 'fas fa-spinner fa-spin');
            // يمكن إضافة وظيفة إنشاء تقرير PDF
            setTimeout(() => {
                showToast('سيتم إضافة هذه الميزة قريباً', 'info', 'fas fa-info-circle');
            }, 2000);
        }

        // إظهار رسالة Toast
        function showToast(message, type = 'info', icon = 'fas fa-info-circle') {
            // إنشاء عنصر Toast
            const toastId = 'toast-' + Date.now();
            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="${icon} me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;

            // إضافة Toast للصفحة
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            // تفعيل Toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, {
                autohide: true,
                delay: 4000
            });
            toast.show();

            // إزالة Toast بعد الإخفاء
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }

        // إعداد منطقة السحب والإفلات
        document.addEventListener('DOMContentLoaded', function() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const uploadBtn = document.getElementById('uploadBtn');
            const uploadForm = document.getElementById('uploadForm');
            const progressUpload = document.querySelector('.progress-upload');
            const selectFilesBtn = document.getElementById('selectFilesBtn');

            // النقر على زر اختيار الملفات
            selectFilesBtn.addEventListener('click', function(e) {
                e.stopPropagation(); // منع انتشار الحدث
                fileInput.click();
            });

            // النقر على منطقة الرفع (ولكن ليس على الزر)
            uploadArea.addEventListener('click', function(e) {
                // تجاهل النقر إذا كان على الزر
                if (e.target === selectFilesBtn || selectFilesBtn.contains(e.target)) {
                    return;
                }
                fileInput.click();
            });

            // عند اختيار الملفات
            fileInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    uploadBtn.style.display = 'inline-block';
                    updateFileList();
                }
            });

            // تحديث قائمة الملفات المختارة
            function updateFileList() {
                const files = fileInput.files;
                let fileNames = [];
                for (let i = 0; i < files.length; i++) {
                    fileNames.push(files[i].name);
                }

                // عرض أسماء الملفات
                const fileListHtml = '<div class="mt-2"><strong>الملفات المختارة:</strong><br>' +
                                    fileNames.map(name => '<small class="text-muted">• ' + name + '</small>').join('<br>') +
                                    '</div>';

                // إضافة قائمة الملفات تحت منطقة الرفع
                let existingList = uploadArea.querySelector('.file-list');
                if (existingList) {
                    existingList.remove();
                }

                const fileListDiv = document.createElement('div');
                fileListDiv.className = 'file-list';
                fileListDiv.innerHTML = fileListHtml;
                uploadArea.appendChild(fileListDiv);
            }

            // رفع الملفات
            uploadForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const documentType = uploadForm.document_type.value;
                const documentName = uploadForm.document_name.value;
                const notes = uploadForm.notes.value;
                const files = fileInput.files;

                if (!documentType) {
                    alert('يرجى اختيار نوع الوثيقة');
                    return;
                }

                if (files.length === 0) {
                    alert('يرجى اختيار ملف واحد على الأقل');
                    return;
                }

                // عرض شريط التقدم
                progressUpload.style.display = 'block';
                uploadBtn.disabled = true;

                // إنشاء FormData لرفع الملفات
                const formData = new FormData();
                formData.append('document_type', documentType);
                formData.append('document_name', documentName);
                formData.append('notes', notes);

                // إضافة الملفات
                for (let i = 0; i < files.length; i++) {
                    formData.append('files', files[i]);
                }

                // رفع الملفات باستخدام fetch
                const poId = window.location.pathname.split('/')[2]; // استخراج po_id من URL

                fetch(`/purchase-orders/${poId}/documents/upload`, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    progressUpload.style.display = 'none';
                    uploadBtn.disabled = false;

                    if (data.success) {
                        alert(data.message);

                        // إعادة تعيين النموذج
                        uploadForm.reset();
                        uploadBtn.style.display = 'none';

                        // إزالة قائمة الملفات
                        const fileList = uploadArea.querySelector('.file-list');
                        if (fileList) {
                            fileList.remove();
                        }

                        // إعادة تحميل الصفحة لعرض الوثائق الجديدة
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        alert('خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('خطأ في رفع الملفات:', error);
                    progressUpload.style.display = 'none';
                    uploadBtn.disabled = false;
                    alert('حدث خطأ في رفع الملفات. يرجى المحاولة مرة أخرى.');
                });
            });

            // السحب والإفلات
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    uploadBtn.style.display = 'inline-block';
                    updateFileList();
                }
            });
        });
    </script>
</body>
</html>