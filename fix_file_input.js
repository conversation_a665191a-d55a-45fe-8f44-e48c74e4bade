// حل مبسط لمشكلة fileInput
// Simple solution for fileInput issue

// انتظار تحميل DOM بالكامل
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 DOM loaded, initializing document upload...');
    
    // تأخير للتأكد من تحميل جميع العناصر
    setTimeout(function() {
        console.log('⏰ Starting initialization after delay...');
        
        // البحث عن العناصر
        let uploadArea = document.getElementById('uploadArea');
        let fileInput = document.getElementById('fileInput');
        
        console.log('🔍 Elements check:');
        console.log('  - uploadArea:', uploadArea);
        console.log('  - fileInput:', fileInput);
        
        // إذا لم يتم العثور على fileInput، ابحث بطرق أخرى
        if (!fileInput) {
            console.log('🔄 البحث عن fileInput بطرق أخرى...');
            
            // البحث بالاسم
            fileInput = document.querySelector('input[name="document_file"]');
            console.log('  - By name:', fileInput);
            
            // البحث بالنوع
            if (!fileInput) {
                fileInput = document.querySelector('input[type="file"]');
                console.log('  - By type:', fileInput);
            }
            
            // إنشاء العنصر إذا لم يوجد
            if (!fileInput && uploadArea) {
                console.log('🔧 إنشاء fileInput جديد...');
                fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.id = 'fileInput';
                fileInput.name = 'document_file';
                fileInput.accept = '.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.tiff,.txt,.csv';
                fileInput.style.display = 'none';
                fileInput.required = true;
                
                uploadArea.appendChild(fileInput);
                console.log('✅ تم إنشاء fileInput جديد');
            }
        }
        
        // تهيئة العناصر
        if (uploadArea && fileInput) {
            console.log('✅ تهيئة العناصر...');
            
            // وظيفة عرض الملف المختار
            function updateFileDisplay() {
                const file = fileInput.files && fileInput.files[0];
                const selectedFileName = document.getElementById('selectedFileName');
                const fileName = document.getElementById('fileName');
                
                if (file && selectedFileName && fileName) {
                    fileName.textContent = file.name + ' (' + (file.size / 1024 / 1024).toFixed(2) + ' MB)';
                    selectedFileName.style.display = 'block';
                    console.log('✅ تم اختيار الملف:', file.name);
                } else if (selectedFileName) {
                    selectedFileName.style.display = 'none';
                }
            }
            
            // إضافة event listeners
            fileInput.addEventListener('change', updateFileDisplay);
            
            // تحديث زر اختيار الملف
            const selectButton = uploadArea.querySelector('.btn-upload');
            if (selectButton) {
                selectButton.onclick = function() {
                    fileInput.click();
                };
            }
            
            console.log('✅ تم تهيئة العناصر بنجاح');
        } else {
            console.error('❌ فشل في تهيئة العناصر');
        }
        
        // تهيئة نموذج الرفع
        const uploadForm = document.getElementById('uploadForm');
        if (uploadForm) {
            uploadForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                console.log('📤 محاولة رفع الوثيقة...');
                
                // البحث عن العناصر مرة أخرى
                const currentFileInput = document.getElementById('fileInput') || 
                                       document.querySelector('input[name="document_file"]') || 
                                       document.querySelector('input[type="file"]');
                
                const documentType = document.querySelector('select[name="document_type"]');
                
                console.log('🔍 فحص العناصر:');
                console.log('  - currentFileInput:', currentFileInput);
                console.log('  - documentType:', documentType);
                
                if (!currentFileInput) {
                    alert('خطأ: لم يتم العثور على عنصر اختيار الملف');
                    return;
                }
                
                if (!documentType || !documentType.value) {
                    alert('يرجى اختيار نوع الوثيقة');
                    return;
                }
                
                if (!currentFileInput.files || currentFileInput.files.length === 0) {
                    alert('يرجى اختيار ملف للرفع');
                    return;
                }
                
                const file = currentFileInput.files[0];
                if (!file) {
                    alert('خطأ في الملف المحدد');
                    return;
                }
                
                // التحقق من حجم الملف
                const maxSize = 10 * 1024 * 1024; // 10 MB
                if (file.size > maxSize) {
                    alert('حجم الملف كبير جداً. الحد الأقصى 10 MB');
                    return;
                }
                
                console.log('✅ جميع الفحوصات نجحت، بدء الرفع...');
                
                // إنشاء FormData
                const formData = new FormData();
                formData.append('document_type', documentType.value);
                formData.append('document_name', document.querySelector('input[name="document_name"]')?.value || '');
                formData.append('notes', document.querySelector('input[name="notes"]')?.value || '');
                formData.append('document_file', file);
                
                // إظهار شريط التقدم
                const progressBar = document.querySelector('.progress-upload');
                if (progressBar) {
                    progressBar.style.display = 'block';
                }
                
                // رفع الملف
                const shipmentId = {{ shipment.id }};
                fetch(`/shipments/cargo/${shipmentId}/documents/upload`, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (progressBar) {
                        progressBar.style.display = 'none';
                    }
                    
                    if (data.success) {
                        alert('تم رفع الوثيقة بنجاح!');
                        location.reload();
                    } else {
                        alert('خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    if (progressBar) {
                        progressBar.style.display = 'none';
                    }
                    console.error('خطأ:', error);
                    alert('حدث خطأ في رفع الوثيقة');
                });
            });
        }
        
    }, 1000); // تأخير ثانية واحدة
});
