-- =====================================================
-- Triggers والفهارس لنظام إدارة أرصدة الموردين
-- Triggers and Indexes for Supplier Balance Management System
-- =====================================================

-- 1. Triggers للجداول الأساسية

-- Trigger لجدول SUPPLIER_ACCOUNTS
CREATE OR REPLACE TRIGGER supplier_accounts_trigger
    BEFORE INSERT ON SUPPLIER_ACCOUNTS
    FOR EACH ROW
BEGIN
    IF :NEW.account_id IS NULL THEN
        :NEW.account_id := SUPPLIER_ACCOUNTS_SEQ.NEXTVAL;
    END IF;
    
    -- إنشاء رقم حساب تلقائي إذا لم يكن محدد
    IF :NEW.account_number IS NULL THEN
        :NEW.account_number := 'SUP' || TO_CHAR(SYSDATE, 'YYYY') || LPAD(SUPPLIER_ACCOUNTS_SEQ.CURRVAL, 6, '0');
    END IF;
    
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;
/

-- Trigger لجدول SUPPLIER_TRANSACTIONS
CREATE OR REPLACE TRIGGER supplier_transactions_trigger
    BEFORE INSERT ON SUPPLIER_TRANSACTIONS
    FOR EACH ROW
BEGIN
    IF :NEW.transaction_id IS NULL THEN
        :NEW.transaction_id := SUPPLIER_TRANSACTIONS_SEQ.NEXTVAL;
    END IF;
    
    -- حساب المبلغ بالعملة الأساسية
    :NEW.base_currency_amount := :NEW.original_amount * NVL(:NEW.exchange_rate, 1);
    
    -- تحديد المبالغ المدينة والدائنة
    IF :NEW.transaction_type IN ('INVOICE', 'DEBIT_NOTE', 'OPENING_BALANCE') THEN
        :NEW.debit_amount := :NEW.original_amount;
        :NEW.credit_amount := 0;
    ELSE
        :NEW.debit_amount := 0;
        :NEW.credit_amount := :NEW.original_amount;
    END IF;
    
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;
/

-- Trigger لتحديث الرصيد الجاري بعد إدخال معاملة
CREATE OR REPLACE TRIGGER update_running_balance_trigger
    AFTER INSERT ON SUPPLIER_TRANSACTIONS
    FOR EACH ROW
DECLARE
    v_running_balance NUMBER(15,2) := 0;
BEGIN
    -- حساب الرصيد الجاري
    SELECT NVL(SUM(debit_amount - credit_amount), 0)
    INTO v_running_balance
    FROM SUPPLIER_TRANSACTIONS
    WHERE account_id = :NEW.account_id
    AND transaction_date <= :NEW.transaction_date
    AND status = 'POSTED';
    
    -- تحديث الرصيد الجاري في المعاملة
    UPDATE SUPPLIER_TRANSACTIONS
    SET running_balance = v_running_balance
    WHERE transaction_id = :NEW.transaction_id;
    
    -- تحديث جدول الأرصدة
    UPDATE_SUPPLIER_BALANCE(:NEW.account_id, :NEW.currency_code);
    
EXCEPTION
    WHEN OTHERS THEN
        -- تسجيل الخطأ دون إيقاف العملية
        NULL;
END;
/

-- Trigger لجدول SUPPLIER_BALANCES
CREATE OR REPLACE TRIGGER supplier_balances_trigger
    BEFORE INSERT ON SUPPLIER_BALANCES
    FOR EACH ROW
BEGIN
    IF :NEW.balance_id IS NULL THEN
        :NEW.balance_id := SUPPLIER_BALANCES_SEQ.NEXTVAL;
    END IF;
    
    -- حساب الرصيد المتاح
    :NEW.available_balance := :NEW.current_balance; -- يمكن تعديلها لاحقاً لتشمل المحجوزات
    
    :NEW.last_updated := CURRENT_TIMESTAMP;
END;
/

-- Trigger لجدول RECONCILIATION_CYCLES
CREATE OR REPLACE TRIGGER reconciliation_cycles_trigger
    BEFORE INSERT ON RECONCILIATION_CYCLES
    FOR EACH ROW
BEGIN
    IF :NEW.cycle_id IS NULL THEN
        :NEW.cycle_id := RECONCILIATION_CYCLES_SEQ.NEXTVAL;
    END IF;
    
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;
/

-- Trigger لجدول RECONCILIATION_ITEMS
CREATE OR REPLACE TRIGGER reconciliation_items_trigger
    BEFORE INSERT ON RECONCILIATION_ITEMS
    FOR EACH ROW
BEGIN
    IF :NEW.item_id IS NULL THEN
        :NEW.item_id := RECONCILIATION_ITEMS_SEQ.NEXTVAL;
    END IF;
    
    -- حساب الفروقات تلقائياً
    :NEW.opening_difference := NVL(:NEW.system_opening_balance, 0) - NVL(:NEW.supplier_opening_balance, 0);
    :NEW.debits_difference := NVL(:NEW.system_debits, 0) - NVL(:NEW.supplier_debits, 0);
    :NEW.credits_difference := NVL(:NEW.system_credits, 0) - NVL(:NEW.supplier_credits, 0);
    :NEW.closing_difference := NVL(:NEW.system_closing_balance, 0) - NVL(:NEW.supplier_closing_balance, 0);
    :NEW.total_difference := :NEW.opening_difference + :NEW.debits_difference + :NEW.credits_difference + :NEW.closing_difference;
    
    -- تحديد حالة المطابقة تلقائياً
    IF ABS(:NEW.total_difference) <= NVL(:NEW.match_tolerance, 0.01) THEN
        :NEW.reconciliation_status := 'MATCHED';
        :NEW.auto_matched := 'Y';
    ELSE
        :NEW.reconciliation_status := 'UNMATCHED';
        :NEW.auto_matched := 'N';
    END IF;
    
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;
/

-- Trigger لجدول RECONCILIATION_DIFFERENCES
CREATE OR REPLACE TRIGGER reconciliation_diff_trigger
    BEFORE INSERT ON RECONCILIATION_DIFFERENCES
    FOR EACH ROW
BEGIN
    IF :NEW.difference_id IS NULL THEN
        :NEW.difference_id := RECONCILIATION_DIFFERENCES_SEQ.NEXTVAL;
    END IF;
    
    -- حساب الفرق والنسبة المئوية
    :NEW.difference_amount := NVL(:NEW.expected_amount, 0) - NVL(:NEW.actual_amount, 0);
    
    IF NVL(:NEW.expected_amount, 0) != 0 THEN
        :NEW.difference_percentage := (:NEW.difference_amount / :NEW.expected_amount) * 100;
    END IF;
    
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;
/

-- Trigger لجدول RECONCILIATION_ADJUSTMENTS
CREATE OR REPLACE TRIGGER reconciliation_adj_trigger
    BEFORE INSERT ON RECONCILIATION_ADJUSTMENTS
    FOR EACH ROW
BEGIN
    IF :NEW.adjustment_id IS NULL THEN
        :NEW.adjustment_id := RECONCILIATION_ADJUSTMENTS_SEQ.NEXTVAL;
    END IF;
    
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;
/

-- Trigger لجدول STATEMENT_TEMPLATES
CREATE OR REPLACE TRIGGER statement_templates_trigger
    BEFORE INSERT ON STATEMENT_TEMPLATES
    FOR EACH ROW
BEGIN
    IF :NEW.template_id IS NULL THEN
        :NEW.template_id := STATEMENT_TEMPLATES_SEQ.NEXTVAL;
    END IF;
    
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;
/

-- Trigger لجدول STATEMENT_HISTORY
CREATE OR REPLACE TRIGGER statement_history_trigger
    BEFORE INSERT ON STATEMENT_HISTORY
    FOR EACH ROW
BEGIN
    IF :NEW.statement_id IS NULL THEN
        :NEW.statement_id := STATEMENT_HISTORY_SEQ.NEXTVAL;
    END IF;
    
    -- إنشاء رقم كشف تلقائي
    IF :NEW.statement_number IS NULL THEN
        :NEW.statement_number := 'STMT' || TO_CHAR(SYSDATE, 'YYYYMMDD') || LPAD(STATEMENT_HISTORY_SEQ.CURRVAL, 4, '0');
    END IF;
END;
/

-- Trigger لجدول PAYMENT_SCHEDULES
CREATE OR REPLACE TRIGGER payment_schedules_trigger
    BEFORE INSERT ON PAYMENT_SCHEDULES
    FOR EACH ROW
BEGIN
    IF :NEW.schedule_id IS NULL THEN
        :NEW.schedule_id := PAYMENT_SCHEDULES_SEQ.NEXTVAL;
    END IF;
    
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;
/

-- 2. إنشاء الفهارس للأداء

-- فهارس جدول SUPPLIER_ACCOUNTS
CREATE INDEX idx_sa_supplier_code ON SUPPLIER_ACCOUNTS(supplier_code);
CREATE INDEX idx_sa_account_status ON SUPPLIER_ACCOUNTS(account_status);
CREATE INDEX idx_sa_risk_rating ON SUPPLIER_ACCOUNTS(risk_rating);
CREATE INDEX idx_sa_created_date ON SUPPLIER_ACCOUNTS(created_date);

-- فهارس جدول SUPPLIER_TRANSACTIONS
CREATE INDEX idx_st_account_id ON SUPPLIER_TRANSACTIONS(account_id);
CREATE INDEX idx_st_transaction_type ON SUPPLIER_TRANSACTIONS(transaction_type);
CREATE INDEX idx_st_transaction_date ON SUPPLIER_TRANSACTIONS(transaction_date);
CREATE INDEX idx_st_due_date ON SUPPLIER_TRANSACTIONS(due_date);
CREATE INDEX idx_st_status ON SUPPLIER_TRANSACTIONS(status);
CREATE INDEX idx_st_reference ON SUPPLIER_TRANSACTIONS(reference_type, reference_id);
CREATE INDEX idx_st_reconciled ON SUPPLIER_TRANSACTIONS(reconciled);
CREATE INDEX idx_st_amount ON SUPPLIER_TRANSACTIONS(original_amount);

-- فهارس جدول SUPPLIER_BALANCES
CREATE INDEX idx_sb_account_id ON SUPPLIER_BALANCES(account_id);
CREATE INDEX idx_sb_currency ON SUPPLIER_BALANCES(currency_code);
CREATE INDEX idx_sb_current_balance ON SUPPLIER_BALANCES(current_balance);
CREATE INDEX idx_sb_last_updated ON SUPPLIER_BALANCES(last_updated);

-- فهارس جدول RECONCILIATION_CYCLES
CREATE INDEX idx_rc_status ON RECONCILIATION_CYCLES(status);
CREATE INDEX idx_rc_cycle_type ON RECONCILIATION_CYCLES(cycle_type);
CREATE INDEX idx_rc_period ON RECONCILIATION_CYCLES(period_from, period_to);
CREATE INDEX idx_rc_reconciliation_date ON RECONCILIATION_CYCLES(reconciliation_date);

-- فهارس جدول RECONCILIATION_ITEMS
CREATE INDEX idx_ri_cycle_id ON RECONCILIATION_ITEMS(cycle_id);
CREATE INDEX idx_ri_account_id ON RECONCILIATION_ITEMS(account_id);
CREATE INDEX idx_ri_status ON RECONCILIATION_ITEMS(reconciliation_status);
CREATE INDEX idx_ri_auto_matched ON RECONCILIATION_ITEMS(auto_matched);
CREATE INDEX idx_ri_total_difference ON RECONCILIATION_ITEMS(total_difference);

-- فهارس جدول RECONCILIATION_DIFFERENCES
CREATE INDEX idx_rd_item_id ON RECONCILIATION_DIFFERENCES(item_id);
CREATE INDEX idx_rd_cycle_id ON RECONCILIATION_DIFFERENCES(cycle_id);
CREATE INDEX idx_rd_type ON RECONCILIATION_DIFFERENCES(difference_type);
CREATE INDEX idx_rd_status ON RECONCILIATION_DIFFERENCES(investigation_status);
CREATE INDEX idx_rd_severity ON RECONCILIATION_DIFFERENCES(severity_level);
CREATE INDEX idx_rd_assigned_to ON RECONCILIATION_DIFFERENCES(assigned_to);

-- فهارس جدول RECONCILIATION_ADJUSTMENTS
CREATE INDEX idx_ra_item_id ON RECONCILIATION_ADJUSTMENTS(item_id);
CREATE INDEX idx_ra_account_id ON RECONCILIATION_ADJUSTMENTS(account_id);
CREATE INDEX idx_ra_type ON RECONCILIATION_ADJUSTMENTS(adjustment_type);
CREATE INDEX idx_ra_status ON RECONCILIATION_ADJUSTMENTS(adjustment_status);
CREATE INDEX idx_ra_requested_by ON RECONCILIATION_ADJUSTMENTS(requested_by);

-- فهارس جدول STATEMENT_TEMPLATES
CREATE INDEX idx_st_template_type ON STATEMENT_TEMPLATES(template_type);
CREATE INDEX idx_st_is_active ON STATEMENT_TEMPLATES(is_active);
CREATE INDEX idx_st_is_default ON STATEMENT_TEMPLATES(is_default);

-- فهارس جدول STATEMENT_HISTORY
CREATE INDEX idx_sh_account_id ON STATEMENT_HISTORY(account_id);
CREATE INDEX idx_sh_template_id ON STATEMENT_HISTORY(template_id);
CREATE INDEX idx_sh_statement_date ON STATEMENT_HISTORY(statement_date);
CREATE INDEX idx_sh_period ON STATEMENT_HISTORY(period_from, period_to);
CREATE INDEX idx_sh_sent_to_supplier ON STATEMENT_HISTORY(sent_to_supplier);

-- فهارس جدول PAYMENT_SCHEDULES
CREATE INDEX idx_ps_account_id ON PAYMENT_SCHEDULES(account_id);
CREATE INDEX idx_ps_schedule_type ON PAYMENT_SCHEDULES(schedule_type);
CREATE INDEX idx_ps_due_date ON PAYMENT_SCHEDULES(due_date);
CREATE INDEX idx_ps_status ON PAYMENT_SCHEDULES(schedule_status);
CREATE INDEX idx_ps_priority ON PAYMENT_SCHEDULES(priority_level);

-- رسالة نجاح
SELECT 'تم إنشاء جميع الـ Triggers والفهارس بنجاح!' as status FROM dual;

COMMIT;
