{% extends "base.html" %}

{% block title %}إدارة مدفوعات الموردين{% endblock %}

{% block extra_css %}
<link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css" rel="stylesheet">
<style>
    .payment-status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .status-pending { background-color: #ffc107; color: #000; }
    .status-approved { background-color: #17a2b8; color: #fff; }
    .status-executed { background-color: #fd7e14; color: #fff; }
    .status-completed { background-color: #28a745; color: #fff; }
    .status-cancelled { background-color: #dc3545; color: #fff; }
    
    .currency-symbol {
        font-weight: bold;
        color: #495057;
    }
    
    .amount-display {
        font-family: 'Courier New', monospace;
        font-weight: bold;
    }
    
    .stats-card {
        border-left: 4px solid;
        transition: transform 0.2s;
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
    }
    
    .stats-card.pending { border-left-color: #ffc107; }
    .stats-card.approved { border-left-color: #17a2b8; }
    .stats-card.completed { border-left-color: #28a745; }
    .stats-card.total { border-left-color: #6f42c1; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">💰 إدارة مدفوعات الموردين</h2>
                    <p class="text-muted mb-0">نظام متكامل لإدارة مدفوعات الموردين عبر الحوالات</p>
                </div>
                <div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createPaymentModal">
                        <i class="fas fa-plus"></i> إنشاء دفعة جديدة
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card pending">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">معلقة</h6>
                            <h3 class="mb-0" id="pendingCount">-</h3>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card approved">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">معتمدة</h6>
                            <h3 class="mb-0" id="approvedCount">-</h3>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card completed">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">مكتملة</h6>
                            <h3 class="mb-0" id="completedCount">-</h3>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-check-double fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card total">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">إجمالي المبلغ</h6>
                            <h3 class="mb-0" id="totalAmount">-</h3>
                        </div>
                        <div class="text-purple">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">🔍 فلاتر البحث</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">المورد</label>
                            <select class="form-select" id="supplierFilter">
                                <option value="">جميع الموردين</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="PENDING">معلقة</option>
                                <option value="APPROVED">معتمدة</option>
                                <option value="EXECUTED">منفذة</option>
                                <option value="COMPLETED">مكتملة</option>
                                <option value="CANCELLED">ملغية</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">العملة</label>
                            <select class="form-select" id="currencyFilter">
                                <option value="">جميع العملات</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="dateFromFilter">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="dateToFilter">
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-primary d-block w-100" onclick="applyFilters()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payments Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">📋 قائمة المدفوعات</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="paymentsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>المورد</th>
                                    <th>المبلغ</th>
                                    <th>العملة</th>
                                    <th>الغرض</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الطلب</th>
                                    <th>الصراف/البنك</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم تحميل البيانات عبر AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إنشاء دفعة جديدة -->
<div class="modal fade" id="createPaymentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">💰 إنشاء دفعة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createPaymentForm">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">المورد *</label>
                            <select class="form-select" id="supplierSelect" required>
                                <option value="">اختر المورد</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">الصراف/البنك *</label>
                            <select class="form-select" id="moneyChangerSelect" required>
                                <option value="">اختر الصراف أو البنك</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-4">
                            <label class="form-label">المبلغ *</label>
                            <input type="number" class="form-control" id="paymentAmount" step="0.01" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">العملة *</label>
                            <select class="form-select" id="currencySelect" required>
                                <option value="">اختر العملة</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">طريقة الدفع</label>
                            <select class="form-select" id="paymentMethod">
                                <option value="BANK_TRANSFER">حوالة بنكية</option>
                                <option value="MONEY_CHANGER">صراف</option>
                                <option value="CASH">نقداً</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-4">
                            <label class="form-label">خصم</label>
                            <input type="number" class="form-control" id="discountAmount" step="0.01" value="0">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">ضريبة مقتطعة</label>
                            <input type="number" class="form-control" id="taxAmount" step="0.01" value="0">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">المبلغ الصافي</label>
                            <input type="number" class="form-control" id="netAmount" readonly>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <label class="form-label">الغرض *</label>
                            <input type="text" class="form-control" id="paymentPurpose" required 
                                   placeholder="مثال: دفع فواتير شهر ديسمبر 2024">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="paymentNotes" rows="3" 
                                      placeholder="أي ملاحظات إضافية..."></textarea>
                        </div>
                    </div>
                    
                    <!-- قسم الفواتير المستحقة -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6>📄 الفواتير المستحقة</h6>
                            <div id="outstandingInvoicesContainer">
                                <p class="text-muted">اختر المورد أولاً لعرض الفواتير المستحقة</p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="createPayment()">
                    <i class="fas fa-save"></i> إنشاء الدفعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal تفاصيل الدفعة -->
<div class="modal fade" id="paymentDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">📋 تفاصيل الدفعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="paymentDetailsContent">
                <!-- سيتم تحميل التفاصيل عبر AJAX -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>

<script>
let paymentsTable;
let selectedSupplier = null;

$(document).ready(function() {
    initializeDataTable();
    loadInitialData();
    setupEventHandlers();
});

function initializeDataTable() {
    paymentsTable = $('#paymentsTable').DataTable({
        responsive: true,
        pageLength: 25,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        order: [[6, 'desc']], // ترتيب حسب تاريخ الطلب
        columnDefs: [
            { targets: [8], orderable: false } // عمود الإجراءات غير قابل للترتيب
        ]
    });
}

function loadInitialData() {
    loadDashboardStats();
    loadSuppliers();
    loadCurrencies();
    loadMoneyChangers();
    loadPayments();
}

function setupEventHandlers() {
    // حساب المبلغ الصافي عند تغيير المبلغ أو الخصم أو الضريبة
    $('#paymentAmount, #discountAmount, #taxAmount').on('input', calculateNetAmount);
    
    // تحميل الفواتير المستحقة عند اختيار المورد
    $('#supplierSelect').on('change', function() {
        selectedSupplier = $(this).val();
        if (selectedSupplier) {
            loadOutstandingInvoices(selectedSupplier);
        } else {
            $('#outstandingInvoicesContainer').html('<p class="text-muted">اختر المورد أولاً لعرض الفواتير المستحقة</p>');
        }
    });
}

function calculateNetAmount() {
    const amount = parseFloat($('#paymentAmount').val()) || 0;
    const discount = parseFloat($('#discountAmount').val()) || 0;
    const tax = parseFloat($('#taxAmount').val()) || 0;
    const netAmount = amount - discount - tax;
    $('#netAmount').val(netAmount.toFixed(2));
}

// سيتم إضافة باقي الدوال في الملف التالي...
</script>
{% endblock %}
