/*
NetSuite Oracle ERP Design System - REAL NetSuite Design
تصميم NetSuite Oracle الحقيقي الأصلي
*/

/* ========== NetSuite Oracle Real Colors ========== */
:root {
    /* NetSuite Real Colors - الألوان الحقيقية */
    --ns-header-blue: #1f4e79;
    --ns-header-blue-dark: #1a3f63;
    --ns-sidebar-bg: #f8f9fa;
    --ns-sidebar-border: #dee2e6;
    --ns-text-dark: #212529;
    --ns-text-muted: #6c757d;
    --ns-orange-accent: #fd7e14;
    
    /* Neutral Colors */
    --ns-gray-50: #f9fafb;
    --ns-gray-100: #f3f4f6;
    --ns-gray-200: #e5e7eb;
    --ns-gray-300: #d1d5db;
    --ns-gray-400: #9ca3af;
    --ns-gray-500: #6b7280;
    --ns-gray-600: #4b5563;
    --ns-gray-700: #374151;
    --ns-gray-800: #1f2937;
    --ns-gray-900: #111827;
    
    /* Status Colors */
    --ns-green: #10b981;
    --ns-red: #ef4444;
    --ns-yellow: #f59e0b;
    
    /* Spacing */
    --ns-space-1: 0.25rem;
    --ns-space-2: 0.5rem;
    --ns-space-3: 0.75rem;
    --ns-space-4: 1rem;
    --ns-space-5: 1.25rem;
    --ns-space-6: 1.5rem;
    --ns-space-8: 2rem;
    --ns-space-10: 2.5rem;
    --ns-space-12: 3rem;
    
    /* Border Radius */
    --ns-radius-sm: 0.25rem;
    --ns-radius: 0.375rem;
    --ns-radius-md: 0.5rem;
    --ns-radius-lg: 0.75rem;
    --ns-radius-xl: 1rem;
    
    /* Shadows */
    --ns-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --ns-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --ns-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --ns-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --ns-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* ========== إعادة تعيين الأساسيات ========== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--ns-gray-50);
    color: var(--ns-gray-900);
    line-height: 1.6;
    direction: rtl;
    text-align: right;
}

/* ========== الشريط العلوي ========== */
.navbar {
    background: linear-gradient(135deg, var(--ns-blue-primary) 0%, var(--ns-blue-secondary) 100%) !important;
    border-bottom: 1px solid var(--ns-blue-dark);
    box-shadow: var(--ns-shadow-lg);
    height: 64px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.navbar-brand {
    color: white !important;
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-brand:hover {
    color: var(--ns-orange-light) !important;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: var(--ns-radius);
    transition: all 0.2s ease;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.1);
}

.dropdown-menu {
    border: 1px solid var(--ns-gray-200);
    box-shadow: var(--ns-shadow-lg);
    border-radius: var(--ns-radius-lg);
}

.dropdown-item {
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: var(--ns-blue-light);
    color: var(--ns-blue-primary);
}

/* ========== الشريط الجانبي ========== */
.sidebar {
    position: fixed;
    top: 64px;
    right: 0;
    width: 280px;
    height: calc(100vh - 64px);
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
    border-left: 1px solid var(--ns-gray-200);
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    z-index: 999;
}

.sidebar .nav-link {
    color: var(--ns-gray-700) !important;
    padding: 0.875rem 1.5rem !important;
    border-radius: 0 !important;
    border-bottom: 1px solid var(--ns-gray-100);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
}

.sidebar .nav-link:hover {
    background: linear-gradient(90deg, var(--ns-blue-light) 0%, rgba(59, 130, 246, 0.1) 100%) !important;
    color: var(--ns-blue-primary) !important;
    border-right: 4px solid var(--ns-orange);
    transform: translateX(-4px);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.sidebar .nav-link.active {
    background: linear-gradient(90deg, var(--ns-blue-light) 0%, rgba(59, 130, 246, 0.15) 100%) !important;
    color: var(--ns-blue-primary) !important;
    border-right: 4px solid var(--ns-orange);
    font-weight: 600;
    box-shadow: 0 2px 12px rgba(59, 130, 246, 0.3);
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
    color: var(--ns-orange);
}

/* عناوين الأقسام */
.sidebar-heading {
    color: var(--ns-gray-500) !important;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 1rem 1.5rem 0.5rem;
    margin-top: 1rem;
}

/* ========== المحتوى الرئيسي ========== */
.main-content {
    margin-right: 280px;
    margin-top: 64px;
    padding: var(--ns-space-8);
    min-height: calc(100vh - 64px);
    background-color: var(--ns-gray-50);
}

/* ========== البطاقات ========== */
.card {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid var(--ns-gray-200);
    border-radius: var(--ns-radius-xl);
    box-shadow: var(--ns-shadow-sm);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--ns-blue-primary) 0%, var(--ns-orange) 100%);
}

.card:hover {
    box-shadow: var(--ns-shadow-xl);
    transform: translateY(-8px) scale(1.02);
    border-color: var(--ns-blue-light);
}

.card-header {
    background-color: var(--ns-gray-50);
    border-bottom: 1px solid var(--ns-gray-200);
    padding: 1rem 1.5rem;
    border-radius: var(--ns-radius-lg) var(--ns-radius-lg) 0 0;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--ns-gray-900);
    margin: 0;
}

.card-body {
    padding: 1.5rem;
}

/* ========== الأزرار ========== */
.btn {
    font-weight: 500;
    border-radius: var(--ns-radius);
    padding: 0.625rem 1.25rem;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.btn-primary {
    background: linear-gradient(135deg, var(--ns-blue-primary) 0%, var(--ns-blue-secondary) 100%);
    border: none;
    color: white;
    box-shadow: 0 4px 15px rgba(31, 78, 121, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--ns-blue-dark) 0%, var(--ns-blue-primary) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(31, 78, 121, 0.4);
}

.btn-secondary {
    background-color: var(--ns-gray-500);
    border-color: var(--ns-gray-500);
    color: white;
}

.btn-secondary:hover {
    background-color: var(--ns-gray-600);
    border-color: var(--ns-gray-600);
}

.btn-success {
    background-color: var(--ns-green);
    border-color: var(--ns-green);
    color: white;
}

.btn-warning {
    background-color: var(--ns-orange);
    border-color: var(--ns-orange);
    color: white;
}

.btn-danger {
    background-color: var(--ns-red);
    border-color: var(--ns-red);
    color: white;
}

/* ========== الجداول ========== */
.table {
    background-color: white;
    border-radius: var(--ns-radius-lg);
    overflow: hidden;
    box-shadow: var(--ns-shadow-sm);
}

.table thead th {
    background-color: var(--ns-gray-50);
    border-bottom: 2px solid var(--ns-gray-200);
    color: var(--ns-gray-700);
    font-weight: 600;
    padding: 1rem;
}

.table tbody td {
    padding: 1rem;
    border-bottom: 1px solid var(--ns-gray-100);
    vertical-align: middle;
}

.table tbody tr:hover {
    background: linear-gradient(90deg, var(--ns-blue-light) 0%, rgba(59, 130, 246, 0.05) 100%);
    transform: scale(1.01);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

/* ========== النماذج ========== */
.form-control {
    border: 1px solid var(--ns-gray-300);
    border-radius: var(--ns-radius);
    padding: 0.75rem;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: var(--ns-blue-primary);
    box-shadow: 0 0 0 3px rgba(31, 78, 121, 0.1);
    outline: none;
}

.form-label {
    font-weight: 500;
    color: var(--ns-gray-700);
    margin-bottom: 0.5rem;
}

/* ========== الإشعارات ========== */
.alert {
    border-radius: var(--ns-radius-lg);
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d1fae5;
    color: #065f46;
}

.alert-danger {
    background-color: #fee2e2;
    color: #991b1b;
}

.alert-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.alert-info {
    background-color: var(--ns-blue-light);
    color: var(--ns-blue-dark);
}

/* ========== التصميم المتجاوب ========== */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
}

/* ========== تحسينات إضافية ========== */
.text-primary {
    color: var(--ns-blue-primary) !important;
}

.text-secondary {
    color: var(--ns-gray-500) !important;
}

.text-success {
    color: var(--ns-green) !important;
}

.text-warning {
    color: var(--ns-orange) !important;
}

.text-danger {
    color: var(--ns-red) !important;
}

.bg-primary {
    background-color: var(--ns-blue-primary) !important;
}

.bg-light {
    background-color: var(--ns-gray-50) !important;
}

/* إخفاء scrollbar في الشريط الجانبي */
.sidebar::-webkit-scrollbar {
    width: 4px;
}

.sidebar::-webkit-scrollbar-track {
    background: var(--ns-gray-100);
}

.sidebar::-webkit-scrollbar-thumb {
    background: var(--ns-gray-300);
    border-radius: 2px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: var(--ns-gray-400);
}
