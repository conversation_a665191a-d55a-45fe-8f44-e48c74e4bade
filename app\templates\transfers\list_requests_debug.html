<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلبات الحوالات - نسخة التشخيص</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary: #2c3e50;
            --secondary: #3498db;
            --success: #27ae60;
            --warning: #f39c12;
            --danger: #e74c3c;
            --info: #17a2b8;
            --light: #ecf0f1;
            --dark: #2c3e50;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }

        .debug-panel h6 {
            margin: 0 0 10px 0;
            color: var(--primary);
        }

        .debug-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px;
            margin: 5px 0;
            font-family: monospace;
            font-size: 11px;
        }

        .debug-success { border-color: #28a745; background: #d4edda; }
        .debug-error { border-color: #dc3545; background: #f8d7da; }
        .debug-info { border-color: #17a2b8; background: #d1ecf1; }

        .page-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
        }

        .card-modern {
            background: white;
            border-radius: 15px;
            box-shadow: var(--shadow);
            border: none;
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .table-modern {
            margin: 0;
        }

        .table-modern thead th {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            border: none;
            padding: 1rem;
            font-weight: 600;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 0 2px;
            transition: all 0.3s ease;
        }

        .btn-view { background: var(--info); color: white; }
        .btn-edit { background: var(--warning); color: white; }
        .btn-delete { background: var(--danger); color: white; }
        .btn-approve { background: var(--success); color: white; }
        .btn-info { background: var(--info); color: white; }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Debug Panel -->
    <div class="debug-panel">
        <h6><i class="fas fa-bug"></i> لوحة التشخيص</h6>
        <button id="clearDebug" class="btn btn-sm btn-outline-secondary mb-2">مسح</button>
        <div id="debugLog"></div>
    </div>

    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid px-3">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-exchange-alt me-3"></i>
                        طلبات الحوالات - نسخة التشخيص
                    </h1>
                    <p class="mb-0 opacity-75">إدارة ومتابعة جميع طلبات التحويلات المالية مع تشخيص مباشر</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-lg" onclick="testAPI()">
                        <i class="fas fa-vial me-2"></i>اختبار API
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container-fluid px-3">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card-modern">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h3 class="mb-1" id="totalRequests">0</h3>
                                <p class="text-muted mb-0">إجمالي الطلبات</p>
                            </div>
                            <div class="text-primary">
                                <i class="fas fa-list fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card-modern">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h3 class="mb-1" id="pendingRequests">0</h3>
                                <p class="text-muted mb-0">طلبات معلقة</p>
                            </div>
                            <div class="text-warning">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card-modern">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h3 class="mb-1" id="approvedRequests">0</h3>
                                <p class="text-muted mb-0">طلبات معتمدة</p>
                            </div>
                            <div class="text-success">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card-modern">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h3 class="mb-1" id="totalAmount">0.00</h3>
                                <p class="text-muted mb-0">إجمالي المبالغ</p>
                            </div>
                            <div class="text-info">
                                <i class="fas fa-dollar-sign fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transfer Requests Table -->
        <div class="card-modern">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        قائمة طلبات الحوالات
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" onclick="loadData()">
                            <i class="fas fa-sync-alt me-1"></i>تحديث
                        </button>
                        <button class="btn btn-info" onclick="testAPI()">
                            <i class="fas fa-vial me-1"></i>اختبار
                        </button>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-modern">
                    <thead>
                        <tr>
                            <th>رقم الطلب</th>
                            <th>المستفيد</th>
                            <th>المبلغ</th>
                            <th>العملة</th>
                            <th>الفرع</th>
                            <th>الصراف</th>
                            <th>نوع الحوالة</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="transferRequestsTableBody">
                        <tr>
                            <td colspan="10" class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                                <div class="mt-2">جاري تحميل البيانات...</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div>جاري معالجة الطلب...</div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Global variables
        let transferRequestsData = [];
        let filteredData = [];

        // Debug functions
        function debugLog(message, type = 'info') {
            const debugLogDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = document.createElement('div');
            logEntry.className = `debug-log debug-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            debugLogDiv.appendChild(logEntry);
            debugLogDiv.scrollTop = debugLogDiv.scrollHeight;
            console.log(`[DEBUG] ${message}`);
        }

        function clearDebug() {
            document.getElementById('debugLog').innerHTML = '';
            console.clear();
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('تم تحميل الصفحة', 'info');
            loadData();
        });

        // Load data from API
        async function loadData() {
            try {
                debugLog('بدء تحميل البيانات من API', 'info');
                showLoading();
                
                const response = await fetch('/transfers/api/transfer-requests');
                debugLog(`استجابة الخادم: ${response.status} ${response.statusText}`, 'info');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                debugLog(`تم استلام البيانات: ${JSON.stringify(data)}`, 'info');
                
                if (data.success) {
                    transferRequestsData = data.data || [];
                    filteredData = [...transferRequestsData];
                    debugLog(`عدد السجلات: ${transferRequestsData.length}`, 'success');
                    updateStatistics();
                    renderTable();
                } else {
                    debugLog(`خطأ في البيانات: ${data.message}`, 'error');
                    showError('فشل في تحميل البيانات: ' + (data.message || 'خطأ غير معروف'));
                }
            } catch (error) {
                debugLog(`خطأ في تحميل البيانات: ${error.message}`, 'error');
                showError('حدث خطأ في تحميل البيانات: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        // Test API function
        async function testAPI() {
            debugLog('بدء اختبار API مباشر', 'info');
            try {
                const response = await fetch('/transfers/api/transfer-requests');
                const data = await response.json();
                
                if (data.success) {
                    debugLog(`اختبار API نجح: ${data.count} سجل`, 'success');
                    alert(`✅ اختبار API نجح!\nعدد السجلات: ${data.count}`);
                } else {
                    debugLog(`اختبار API فشل: ${data.message}`, 'error');
                    alert(`❌ اختبار API فشل!\nالرسالة: ${data.message}`);
                }
            } catch (error) {
                debugLog(`خطأ في اختبار API: ${error.message}`, 'error');
                alert(`❌ خطأ في اختبار API!\nالخطأ: ${error.message}`);
            }
        }

        // Update statistics
        function updateStatistics() {
            const stats = {
                total: transferRequestsData.length,
                pending: transferRequestsData.filter(r => r.status === 'pending').length,
                approved: transferRequestsData.filter(r => r.status === 'approved').length,
                totalAmount: transferRequestsData.reduce((sum, r) => sum + parseFloat(r.amount || 0), 0)
            };

            document.getElementById('totalRequests').textContent = stats.total;
            document.getElementById('pendingRequests').textContent = stats.pending;
            document.getElementById('approvedRequests').textContent = stats.approved;
            document.getElementById('totalAmount').textContent = formatCurrencyEnglish(stats.totalAmount);
            
            debugLog(`تم تحديث الإحصائيات: ${JSON.stringify(stats)}`, 'success');
        }

        // Render table
        function renderTable() {
            const tbody = document.getElementById('transferRequestsTableBody');
            
            if (filteredData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <div>لا توجد طلبات حوالات</div>
                        </td>
                    </tr>
                `;
                debugLog('لا توجد بيانات لعرضها', 'info');
                return;
            }

            tbody.innerHTML = filteredData.map(request => `
                <tr>
                    <td><strong class="text-primary">${request.request_number}</strong></td>
                    <td>${request.beneficiary_name}</td>
                    <td><strong>${formatCurrencyEnglish(request.amount)}</strong></td>
                    <td><span class="badge bg-secondary">${request.currency}</span></td>
                    <td>${request.branch_name || 'غير محدد'}</td>
                    <td>${request.money_changer_name || 'غير محدد'}</td>
                    <td>${getTransferTypeLabel(request.transfer_type)}</td>
                    <td><span class="badge bg-warning">${getStatusLabel(request.status)}</span></td>
                    <td>${formatDate(request.created_at)}</td>
                    <td>
                        <div class="d-flex gap-1">
                            <button class="action-btn btn-view" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn btn-edit" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn btn-info" title="إدارة الوثائق">
                                <i class="fas fa-file-alt"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
            
            debugLog(`تم عرض ${filteredData.length} سجل في الجدول`, 'success');
        }

        // Helper functions
        function getStatusLabel(status) {
            const labels = {
                'pending': 'معلق',
                'approved': 'معتمد',
                'rejected': 'مرفوض',
                'executed': 'منفذ'
            };
            return labels[status] || status;
        }

        function getTransferTypeLabel(type) {
            const labels = {
                'bank': 'بنكية',
                'cash': 'نقدية',
                'money_changer': 'صراف',
                'online': 'إلكترونية'
            };
            return labels[type] || type;
        }

        function formatCurrencyEnglish(amount) {
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount);
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('ar-SA');
        }

        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        function showError(message) {
            alert(message);
            debugLog(`خطأ: ${message}`, 'error');
        }

        // Event listeners
        document.getElementById('clearDebug').addEventListener('click', clearDebug);

        // Auto-load on page ready
        debugLog('تم تحميل JavaScript بنجاح', 'success');
    </script>
</body>
</html>
