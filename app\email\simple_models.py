# -*- coding: utf-8 -*-
"""
نماذج بسيطة لنظام البريد الإلكتروني
Simple Email System Models
"""

from datetime import datetime
from database_manager import DatabaseManager

class SimpleEmailAccount:
    """نموذج بسيط لحسابات البريد الإلكتروني"""
    
    @staticmethod
    def get_by_user_and_default(user_id, is_default=True):
        """جلب الحساب الافتراضي للمستخدم"""
        db = DatabaseManager()
        try:
            query = """
            SELECT id, user_id, email_address, display_name, smtp_server, smtp_port,
                   smtp_use_tls, smtp_use_ssl, imap_server, imap_port, imap_use_ssl,
                   password_encrypted, signature, auto_reply_enabled, auto_reply_message,
                   is_active, is_default, last_sync, created_at, updated_at
            FROM email_accounts 
            WHERE user_id = :1 AND is_default = :2
            """
            results = db.execute_query(query, [user_id, 1 if is_default else 0])
            
            if results:
                row = results[0]
                account = type('EmailAccount', (), {})()
                account.id = row[0]
                account.user_id = row[1]
                account.email_address = row[2]
                account.display_name = row[3]
                account.smtp_server = row[4]
                account.smtp_port = row[5]
                account.smtp_use_tls = bool(row[6])
                account.smtp_use_ssl = bool(row[7])
                account.imap_server = row[8]
                account.imap_port = row[9]
                account.imap_use_ssl = bool(row[10])
                account.password_encrypted = row[11]
                account.signature = row[12]
                account.auto_reply_enabled = bool(row[13])
                account.auto_reply_message = row[14]
                account.is_active = bool(row[15])
                account.is_default = bool(row[16])
                account.last_sync = row[17]
                account.created_at = row[18]
                account.updated_at = row[19]
                return account
            return None
        finally:
            db.close()
    
    @staticmethod
    def get_all_by_user(user_id):
        """جلب جميع حسابات المستخدم"""
        db = DatabaseManager()
        try:
            query = """
            SELECT id, user_id, email_address, display_name, smtp_server, smtp_port,
                   smtp_use_tls, smtp_use_ssl, imap_server, imap_port, imap_use_ssl,
                   password_encrypted, signature, auto_reply_enabled, auto_reply_message,
                   is_active, is_default, last_sync, created_at, updated_at
            FROM email_accounts 
            WHERE user_id = :1 
            ORDER BY is_default DESC, created_at DESC
            """
            results = db.execute_query(query, [user_id])
            
            accounts = []
            for row in results:
                account = type('EmailAccount', (), {})()
                account.id = row[0]
                account.user_id = row[1]
                account.email_address = row[2]
                account.display_name = row[3]
                account.smtp_server = row[4]
                account.smtp_port = row[5]
                account.smtp_use_tls = bool(row[6])
                account.smtp_use_ssl = bool(row[7])
                account.imap_server = row[8]
                account.imap_port = row[9]
                account.imap_use_ssl = bool(row[10])
                account.password_encrypted = row[11]
                account.signature = row[12]
                account.auto_reply_enabled = bool(row[13])
                account.auto_reply_message = row[14]
                account.is_active = bool(row[15])
                account.is_default = bool(row[16])
                account.last_sync = row[17]
                account.created_at = row[18]
                account.updated_at = row[19]
                accounts.append(account)
            return accounts
        finally:
            db.close()

class SimpleEmailFolder:
    """نموذج بسيط لمجلدات البريد الإلكتروني"""
    
    @staticmethod
    def get_by_account_and_type(account_id, folder_type):
        """جلب مجلد بنوع معين"""
        db = DatabaseManager()
        try:
            query = """
            SELECT id, account_id, name, name_arabic, folder_type, parent_id,
                   unread_count, total_count, auto_delete_days, is_system,
                   is_active, sort_order, created_at
            FROM email_folders 
            WHERE account_id = :1 AND folder_type = :2
            """
            results = db.execute_query(query, [account_id, folder_type])
            
            if results:
                row = results[0]
                folder = type('EmailFolder', (), {})()
                folder.id = row[0]
                folder.account_id = row[1]
                folder.name = row[2]
                folder.name_arabic = row[3]
                folder.folder_type = row[4]
                folder.parent_id = row[5]
                folder.unread_count = row[6] or 0
                folder.total_count = row[7] or 0
                folder.auto_delete_days = row[8]
                folder.is_system = bool(row[9])
                folder.is_active = bool(row[10])
                folder.sort_order = row[11] or 0
                folder.created_at = row[12]
                return folder
            return None
        finally:
            db.close()
    
    @staticmethod
    def get_all_by_account(account_id):
        """جلب جميع مجلدات الحساب"""
        db = DatabaseManager()
        try:
            query = """
            SELECT id, account_id, name, name_arabic, folder_type, parent_id,
                   unread_count, total_count, auto_delete_days, is_system,
                   is_active, sort_order, created_at
            FROM email_folders 
            WHERE account_id = :1 
            ORDER BY sort_order, name_arabic
            """
            results = db.execute_query(query, [account_id])
            
            folders = []
            for row in results:
                folder = type('EmailFolder', (), {})()
                folder.id = row[0]
                folder.account_id = row[1]
                folder.name = row[2]
                folder.name_arabic = row[3]
                folder.folder_type = row[4]
                folder.parent_id = row[5]
                folder.unread_count = row[6] or 0
                folder.total_count = row[7] or 0
                folder.auto_delete_days = row[8]
                folder.is_system = bool(row[9])
                folder.is_active = bool(row[10])
                folder.sort_order = row[11] or 0
                folder.created_at = row[12]
                folders.append(folder)
            return folders
        finally:
            db.close()

class SimpleEmailMessage:
    """نموذج بسيط لرسائل البريد الإلكتروني"""
    
    @staticmethod
    def get_by_account_and_folder(account_id, folder_id, limit=50):
        """جلب رسائل مجلد معين"""
        db = DatabaseManager()
        try:
            query = """
            SELECT id, account_id, folder_id, message_id, thread_id, in_reply_to,
                   subject, sender_email, sender_name, to_emails, cc_emails, bcc_emails,
                   body_text, body_html, priority, size_bytes, has_attachments,
                   is_read, is_starred, is_important, is_spam, is_deleted,
                   sent_at, received_at, read_at, created_at, updated_at
            FROM email_messages 
            WHERE account_id = :1 AND folder_id = :2 AND is_deleted = 0
            ORDER BY received_at DESC
            """
            
            # إضافة LIMIT في Oracle
            if limit:
                query = f"SELECT * FROM ({query}) WHERE ROWNUM <= :3"
                params = [account_id, folder_id, limit]
            else:
                params = [account_id, folder_id]
            
            results = db.execute_query(query, params)
            
            messages = []
            for row in results:
                message = type('EmailMessage', (), {})()
                message.id = row[0]
                message.account_id = row[1]
                message.folder_id = row[2]
                message.message_id = row[3]
                message.thread_id = row[4]
                message.in_reply_to = row[5]
                message.subject = row[6]
                message.sender_email = row[7]
                message.sender_name = row[8]
                message.to_emails = row[9]
                message.cc_emails = row[10]
                message.bcc_emails = row[11]
                message.body_text = row[12]
                message.body_html = row[13]
                message.priority = row[14]
                message.size_bytes = row[15] or 0
                message.has_attachments = bool(row[16])
                message.is_read = bool(row[17])
                message.is_starred = bool(row[18])
                message.is_important = bool(row[19])
                message.is_spam = bool(row[20])
                message.is_deleted = bool(row[21])
                message.sent_at = row[22]
                message.received_at = row[23]
                message.read_at = row[24]
                message.created_at = row[25]
                message.updated_at = row[26]
                messages.append(message)
            return messages
        finally:
            db.close()

class SimpleEmailContact:
    """نموذج بسيط لجهات اتصال البريد الإلكتروني"""

    def __init__(self):
        self.id = None
        self.user_id = None
        self.email_address = None
        self.display_name = None
        self.first_name = None
        self.last_name = None
        self.company = None
        self.phone = None
        self.notes = None
        self.is_favorite = False
        self.emails_sent = 0
        self.emails_received = 0
        self.last_contact = None
        self.created_at = None

    @staticmethod
    def get_all_by_user(user_id):
        """جلب جميع جهات اتصال المستخدم"""
        from database_manager import DatabaseManager
        db = DatabaseManager()

        try:
            contacts_query = """
            SELECT id, email_address, display_name, first_name, last_name,
                   company, phone, notes, is_favorite, emails_sent, emails_received,
                   last_contact, created_at
            FROM email_contacts
            WHERE user_id = :1
            ORDER BY display_name ASC, email_address ASC
            """
            contacts_result = db.execute_query(contacts_query, [user_id])

            contacts = []
            if contacts_result:
                for row in contacts_result:
                    contact = SimpleEmailContact()
                    contact.id = row[0]
                    contact.email_address = row[1]
                    contact.display_name = row[2] or row[1]
                    contact.first_name = row[3]
                    contact.last_name = row[4]
                    contact.company = row[5]
                    contact.phone = row[6]
                    contact.notes = row[7]
                    contact.is_favorite = bool(row[8])
                    contact.emails_sent = row[9] or 0
                    contact.emails_received = row[10] or 0
                    contact.last_contact = row[11]
                    contact.created_at = row[12]
                    contacts.append(contact)

            return contacts
        finally:
            db.close()

class SimpleEmailTemplate:
    """نموذج بسيط لقوالب البريد الإلكتروني"""

    def __init__(self):
        self.id = None
        self.user_id = None
        self.name = None
        self.subject = None
        self.body_text = None
        self.body_html = None
        self.description = None
        self.is_public = False
        self.category = None
        self.usage_count = 0
        self.created_at = None

    @staticmethod
    def get_by_user_and_public(user_id):
        """جلب قوالب المستخدم والقوالب العامة"""
        from database_manager import DatabaseManager
        db = DatabaseManager()

        try:
            templates_query = """
            SELECT id, name, subject_template, body_template, is_public,
                   category, usage_count, created_at
            FROM email_templates
            WHERE user_id = :1 OR is_public = 1
            ORDER BY name ASC
            """
            templates_result = db.execute_query(templates_query, [user_id])

            templates = []
            if templates_result:
                for row in templates_result:
                    template = SimpleEmailTemplate()
                    template.id = row[0]
                    template.name = row[1]
                    template.subject = row[2]  # subject_template
                    template.body_text = row[3]  # body_template
                    template.body_html = row[3]  # نفس body_template
                    template.description = None  # لا يوجد عمود description
                    template.is_public = bool(row[4])
                    template.category = row[5]
                    template.usage_count = row[6] or 0
                    template.created_at = row[7]
                    templates.append(template)

            return templates
        finally:
            db.close()
