<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار واجهة اختيار جهات الاتصال</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            padding: 2rem;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .selected-contacts-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            min-height: 100px;
            margin-top: 1rem;
        }
        
        .contact-tag {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            margin: 0.25rem;
            font-size: 0.875rem;
        }
        
        .contact-tag .remove-btn {
            margin-left: 0.5rem;
            cursor: pointer;
            opacity: 0.8;
        }
        
        .contact-tag .remove-btn:hover {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2 class="mb-4">
            <i class="fas fa-address-book me-2"></i>
            اختبار واجهة اختيار جهات الاتصال
        </h2>
        
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            هذه صفحة اختبار لواجهة اختيار جهات الاتصال. اضغط على الزر أدناه لفتح الواجهة.
        </div>
        
        <div class="mb-4">
            <button type="button" class="btn btn-primary btn-lg" onclick="openContactSelectorTest()">
                <i class="fas fa-users me-2"></i>
                فتح واجهة اختيار جهات الاتصال
            </button>
        </div>
        
        <div class="mb-3">
            <h5>جهات الاتصال المختارة:</h5>
            <div id="testSelectedContacts" class="selected-contacts-display">
                <p class="text-muted mb-0">لم يتم اختيار أي جهة اتصال بعد</p>
            </div>
        </div>
        
        <div class="mb-3">
            <h6>معلومات التشخيص:</h6>
            <div id="debugInfo" class="bg-light p-3 rounded">
                <small class="text-muted">سيتم عرض معلومات التشخيص هنا...</small>
            </div>
        </div>
        
        <div class="mt-4">
            <button type="button" class="btn btn-outline-secondary" onclick="clearTestSelection()">
                <i class="fas fa-trash me-2"></i>
                مسح الاختيار
            </button>
            <button type="button" class="btn btn-outline-info" onclick="showDebugInfo()">
                <i class="fas fa-bug me-2"></i>
                عرض معلومات التشخيص
            </button>
        </div>
    </div>

    <!-- تضمين واجهة اختيار جهات الاتصال -->
    <div class="modal fade" id="contactSelectorModal" tabindex="-1" aria-labelledby="contactSelectorModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="contactSelectorModalLabel">
                        <i class="fas fa-address-book me-2"></i>
                        اختيار جهات الاتصال للإشعارات
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <!-- فلاتر البحث -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <label class="form-label">نوع جهة الاتصال</label>
                            <select class="form-select" id="contactTypeFilter">
                                <option value="">جميع الأنواع</option>
                                <option value="CUSTOMER">العملاء</option>
                                <option value="DRIVER">السائقين</option>
                                <option value="AGENT">المخلصين الجمركيين</option>
                                <option value="MANAGER">المديرين</option>
                                <option value="EXTERNAL">خارجي</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">الأولوية</label>
                            <select class="form-select" id="priorityFilter">
                                <option value="">جميع المستويات</option>
                                <option value="8">أولوية عالية (8+)</option>
                                <option value="5">أولوية متوسطة (5+)</option>
                                <option value="1">جميع الأولويات</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">البحث</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="contactSearchInput" placeholder="البحث في الأسماء...">
                                <button class="btn btn-outline-primary" onclick="filterContacts()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- قائمة جهات الاتصال -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">
                                <i class="fas fa-users me-2"></i>
                                جهات الاتصال المتاحة
                                <span class="badge bg-secondary ms-2" id="contactsCount">0</span>
                            </h6>
                            <div>
                                <button class="btn btn-sm btn-outline-primary" onclick="selectAllContacts()">
                                    <i class="fas fa-check-square me-1"></i>
                                    تحديد الكل
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="clearAllContacts()">
                                    <i class="fas fa-square me-1"></i>
                                    إلغاء التحديد
                                </button>
                            </div>
                        </div>
                        
                        <div class="contact-list" id="contactsList" style="max-height: 400px; overflow-y: auto;">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>

                    <!-- جهات الاتصال المختارة -->
                    <div class="selected-contacts-section">
                        <h6 class="mb-3">
                            <i class="fas fa-check-circle me-2 text-success"></i>
                            جهات الاتصال المختارة
                            <span class="badge bg-success ms-2" id="selectedCount">0</span>
                        </h6>
                        <div class="selected-contacts" id="selectedContacts" style="min-height: 60px; max-height: 200px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem; padding: 1rem; background-color: #f8f9fa;">
                            <p class="text-muted mb-0" id="noSelectionMessage">لم يتم اختيار أي جهة اتصال بعد</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="confirmContactSelection()" id="confirmButton" disabled>
                        <i class="fas fa-check me-2"></i>
                        تأكيد الاختيار (<span id="confirmCount">0</span>)
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- JavaScript للاختبار -->
    <script>
        // بيانات تجريبية لجهات الاتصال
        const testContacts = [
            {
                id: 1,
                name: 'المدير العام - نشأت الفجيحي',
                type: 'MANAGER',
                phone: '+967777161609',
                email: '<EMAIL>',
                whatsapp: '+967777161609',
                company: 'شركة الفجيحي',
                position: 'المدير العام',
                is_vip: true,
                priority: 10
            },
            {
                id: 2,
                name: 'فاطمة أحمد - خدمة العملاء',
                type: 'CUSTOMER',
                phone: '+967771234567',
                email: '<EMAIL>',
                whatsapp: '+967771234567',
                company: 'شركة العملاء',
                position: 'ممثل خدمة العملاء',
                is_vip: false,
                priority: 8
            },
            {
                id: 3,
                name: 'محمد علي - سائق',
                type: 'DRIVER',
                phone: '+967779876543',
                email: '<EMAIL>',
                whatsapp: '+967779876543',
                company: 'شركة النقل',
                position: 'سائق',
                is_vip: false,
                priority: 6
            },
            {
                id: 4,
                name: 'عبدالله أحمد - مخلص جمركي',
                type: 'AGENT',
                phone: '+967775555555',
                email: '<EMAIL>',
                whatsapp: '+967775555555',
                company: 'مكتب التخليص',
                position: 'مخلص جمركي',
                is_vip: false,
                priority: 7
            }
        ];

        // متغيرات عامة للاختبار
        let testSelectedContacts = [];

        // فتح واجهة اختيار جهات الاتصال للاختبار
        function openContactSelectorTest() {
            console.log('فتح واجهة اختبار اختيار جهات الاتصال');
            
            // تعيين دالة callback
            window.onContactsSelected = function(selectedContacts) {
                console.log('تم استلام جهات الاتصال المختارة:', selectedContacts);
                testSelectedContacts = selectedContacts;
                displayTestSelectedContacts();
                updateDebugInfo();
            };
            
            // محاكاة تحميل البيانات
            allContacts = testContacts;
            filteredContacts = [...testContacts];
            
            // فتح Modal
            const modal = new bootstrap.Modal(document.getElementById('contactSelectorModal'));
            modal.show();
            
            // تهيئة الواجهة
            setTimeout(() => {
                initContactSelector();
                renderContacts();
                updateContactsCount();
            }, 100);
        }

        // عرض جهات الاتصال المختارة في الاختبار
        function displayTestSelectedContacts() {
            const container = document.getElementById('testSelectedContacts');
            
            if (testSelectedContacts.length === 0) {
                container.innerHTML = '<p class="text-muted mb-0">لم يتم اختيار أي جهة اتصال بعد</p>';
                return;
            }
            
            container.innerHTML = testSelectedContacts.map(contact => `
                <span class="contact-tag">
                    ${contact.name}
                    ${contact.is_vip ? '<i class="fas fa-star ms-1"></i>' : ''}
                    <span class="remove-btn" onclick="removeTestContact(${contact.id})">
                        <i class="fas fa-times"></i>
                    </span>
                </span>
            `).join('');
        }

        // إزالة جهة اتصال من الاختبار
        function removeTestContact(contactId) {
            testSelectedContacts = testSelectedContacts.filter(c => c.id !== contactId);
            displayTestSelectedContacts();
            updateDebugInfo();
        }

        // مسح الاختيار
        function clearTestSelection() {
            testSelectedContacts = [];
            displayTestSelectedContacts();
            updateDebugInfo();
        }

        // عرض معلومات التشخيص
        function showDebugInfo() {
            updateDebugInfo();
        }

        // تحديث معلومات التشخيص
        function updateDebugInfo() {
            const debugContainer = document.getElementById('debugInfo');
            const info = {
                'عدد جهات الاتصال المختارة': testSelectedContacts.length,
                'أسماء المختارة': testSelectedContacts.map(c => c.name),
                'معرفات المختارة': testSelectedContacts.map(c => c.id),
                'حالة زر التأكيد': document.getElementById('confirmButton')?.disabled ? 'معطل' : 'مفعل',
                'وقت آخر تحديث': new Date().toLocaleString('ar-SA')
            };
            
            debugContainer.innerHTML = Object.entries(info).map(([key, value]) => 
                `<div><strong>${key}:</strong> ${Array.isArray(value) ? value.join(', ') : value}</div>`
            ).join('');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo();
        });
    </script>
    
    <!-- تضمين كود واجهة اختيار جهات الاتصال -->
    <script>
        // نسخ الكود من automation_selector.html هنا...
        // (سيتم تضمينه من الملف الأصلي)
    </script>
</body>
</html>
