#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نظام الحوالات المتكامل
Integrated Money Transfer System

هذا الملف يحتوي على Blueprint لنظام الحوالات المتكامل
"""

from flask import Blueprint

# إنشاء Blueprint لنظام الحوالات
transfers_bp = Blueprint(
    'transfers', 
    __name__, 
    url_prefix='/transfers',
    template_folder='templates',
    static_folder='static'
)

# استيراد جميع routes بعد إنشاء Blueprint
def register_routes():
    """تسجيل جميع routes"""
    try:
        from . import routes
        from . import dashboard
        from . import money_changers
        from . import money_changers_opening_balances
        from . import beneficiaries
        from . import requests
        from . import execution
        from . import tracking
        from . import exchange_rates
        from . import approvals
        from . import reports
        from . import accounting_routes  # إضافة routes الترحيل المحاسبي
        return True
    except Exception as e:
        print(f"خطأ في تسجيل routes: {e}")
        return False

# تسجيل routes
register_routes()
