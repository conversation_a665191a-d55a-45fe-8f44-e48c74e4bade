{% extends "base.html" %}

{% block title %}إعدادات قاعدة البيانات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('settings.index') }}">الإعدادات</a></li>
                    <li class="breadcrumb-item active">قاعدة البيانات</li>
                </ol>
            </nav>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-database"></i> إعدادات قاعدة البيانات</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>معلومات:</strong> هذه الصفحة تعرض معلومات قاعدة البيانات الحالية وأدوات الإدارة.
                    </div>
                    
                    <div id="dbInfo">
                        <h5>معلومات الاتصال</h5>
                        <table class="table table-striped">
                            <tr>
                                <td><strong>حالة الاتصال:</strong></td>
                                <td>
                                    {% if database_info.connected %}
                                        <span class="badge bg-success">متصل</span>
                                    {% else %}
                                        <span class="badge bg-danger">غير متصل</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>اسم قاعدة البيانات:</strong></td>
                                <td>{{ database_info.database_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>عدد الجداول:</strong></td>
                                <td>{{ database_info.tables|length }}</td>
                            </tr>
                        </table>
                        
                        <h5 class="mt-4">إحصائيات الجداول</h5>
                        <div class="table-responsive">
                            <table class="table table-hover" id="tablesTable">
                                <thead>
                                    <tr>
                                        <th>اسم الجدول</th>
                                        <th>الاسم التقني</th>
                                        <th>عدد السجلات</th>
                                    </tr>
                                </thead>
                                <tbody id="tablesTableBody">
                                    {% if database_info.error %}
                                        <tr>
                                            <td colspan="3" class="text-center text-danger">خطأ: {{ database_info.error }}</td>
                                        </tr>
                                    {% elif database_info.tables %}
                                        {% for table in database_info.tables %}
                                        <tr>
                                            <td>{{ table.name }}</td>
                                            <td><code>{{ table.table }}</code></td>
                                            <td><span class="badge bg-primary">{{ table.count }}</span></td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="3" class="text-center text-muted">لا توجد جداول</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-tools"></i> أدوات الإدارة</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="testConnection()">
                            <i class="fas fa-plug"></i> اختبار الاتصال
                        </button>
                        <button class="btn btn-success" onclick="createBackup()">
                            <i class="fas fa-save"></i> نسخة احتياطية
                        </button>
                        <button class="btn btn-warning" onclick="optimizeDatabase()">
                            <i class="fas fa-cogs"></i> تحسين الأداء
                        </button>
                        <button class="btn btn-info" onclick="refreshStats()">
                            <i class="fas fa-sync-alt"></i> تحديث الإحصائيات
                        </button>
                    </div>
                    
                    <hr>
                    
                    <h6><i class="fas fa-exclamation-triangle text-warning"></i> منطقة الخطر</h6>
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-danger btn-sm" onclick="confirmDangerousAction('vacuum')">
                            <i class="fas fa-compress-arrows-alt"></i> ضغط قاعدة البيانات
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="fas fa-history"></i> النسخ الاحتياطية الأخيرة</h6>
                </div>
                <div class="card-body">
                    <div id="recentBackups">
                        <small class="text-muted">لا توجد نسخ احتياطية حديثة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد العمليات الخطيرة -->
<div class="modal fade" id="dangerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title"><i class="fas fa-exclamation-triangle"></i> تحذير</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p><strong>تحذير:</strong> هذه العملية قد تؤثر على أداء النظام.</p>
                <p>هل أنت متأكد من المتابعة؟</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDangerBtn">تأكيد</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحميل معلومات قاعدة البيانات عند تحميل الصفحة
console.log('تحميل JavaScript لصفحة إعدادات قاعدة البيانات...');

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM تم تحميله، بدء تحميل معلومات قاعدة البيانات...');
    loadDatabaseInfo();
});

// تحميل فوري أيضاً في حالة كان DOM محمل بالفعل
if (document.readyState === 'loading') {
    console.log('DOM لا يزال يتم تحميله...');
} else {
    console.log('DOM محمل بالفعل، تحميل معلومات قاعدة البيانات فوراً...');
    loadDatabaseInfo();
}

// تحميل معلومات قاعدة البيانات
async function loadDatabaseInfo() {
    console.log('بدء تحميل معلومات قاعدة البيانات...');

    try {
        const response = await fetch('/settings/api/database-status');
        console.log('استجابة API:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('بيانات API:', data);

        const statusElement = document.getElementById('connectionStatus');

        if (data.success && data.connected) {
            statusElement.textContent = 'متصل';
            statusElement.className = 'badge bg-success';

            document.getElementById('databaseName').textContent = data.database_url || 'غير محدد';
            document.getElementById('tableCount').textContent = data.tables ? data.tables.length : '0';

            // عرض إحصائيات الجداول
            if (data.tables && data.tables.length > 0) {
                const tbody = document.getElementById('tablesTableBody');
                tbody.innerHTML = '';

                data.tables.forEach(table => {
                    const row = tbody.insertRow();
                    row.innerHTML = `
                        <td>${table.name}</td>
                        <td><code>${table.table}</code></td>
                        <td><span class="badge bg-primary">${table.count}</span></td>
                    `;
                });
            } else {
                // لا توجد جداول
                const tbody = document.getElementById('tablesTableBody');
                tbody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">لا توجد جداول</td></tr>';
            }
        } else {
            statusElement.textContent = 'غير متصل';
            statusElement.className = 'badge bg-danger';

            document.getElementById('databaseName').textContent = 'غير متاح';
            document.getElementById('tableCount').textContent = '0';

            // عرض رسالة خطأ
            const tbody = document.getElementById('tablesTableBody');
            tbody.innerHTML = '<tr><td colspan="3" class="text-center text-danger">فشل الاتصال بقاعدة البيانات</td></tr>';
        }
    } catch (error) {
        console.error('خطأ في تحميل معلومات قاعدة البيانات:', error);

        // عرض رسالة خطأ للمستخدم
        const statusElement = document.getElementById('connectionStatus');
        statusElement.textContent = 'خطأ';
        statusElement.className = 'badge bg-danger';

        document.getElementById('databaseName').textContent = 'خطأ';
        document.getElementById('tableCount').textContent = '0';

        const tbody = document.getElementById('tablesTableBody');
        tbody.innerHTML = `<tr><td colspan="3" class="text-center text-danger">خطأ: ${error.message}</td></tr>`;
        document.getElementById('connectionStatus').textContent = 'خطأ';
        document.getElementById('connectionStatus').className = 'badge bg-warning';
    }
}

// اختبار الاتصال
async function testConnection() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاختبار...';
    btn.disabled = true;
    
    try {
        const response = await fetch('/settings/api/database-status');
        const data = await response.json();
        
        if (data.success && data.connected) {
            alert('✅ الاتصال بقاعدة البيانات يعمل بشكل صحيح');
        } else {
            alert('❌ فشل في الاتصال بقاعدة البيانات: ' + (data.error || 'خطأ غير معروف'));
        }
    } catch (error) {
        alert('❌ خطأ في اختبار الاتصال: ' + error.message);
    } finally {
        btn.innerHTML = originalText;
        btn.disabled = false;
    }
}

// إنشاء نسخة احتياطية
async function createBackup() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإنشاء...';
    btn.disabled = true;
    
    try {
        const response = await fetch('/settings/api/backup-database', {
            method: 'POST'
        });
        const data = await response.json();
        
        if (data.success) {
            alert('✅ تم إنشاء النسخة الاحتياطية بنجاح!\nالملف: ' + data.backup_file);
        } else {
            alert('❌ فشل في إنشاء النسخة الاحتياطية: ' + data.message);
        }
    } catch (error) {
        alert('❌ خطأ في إنشاء النسخة الاحتياطية: ' + error.message);
    } finally {
        btn.innerHTML = originalText;
        btn.disabled = false;
    }
}

// تحسين قاعدة البيانات
function optimizeDatabase() {
    alert('ميزة تحسين قاعدة البيانات قيد التطوير');
}

// تحديث الإحصائيات
function refreshStats() {
    loadDatabaseInfo();
    alert('تم تحديث الإحصائيات');
}

// تأكيد العمليات الخطيرة
function confirmDangerousAction(action) {
    const modal = new bootstrap.Modal(document.getElementById('dangerModal'));
    
    document.getElementById('confirmDangerBtn').onclick = function() {
        if (action === 'vacuum') {
            alert('ميزة ضغط قاعدة البيانات قيد التطوير');
        }
        modal.hide();
    };
    
    modal.show();
}
</script>
{% endblock %}
