#!/usr/bin/env python3
"""
شرح منطق حالة الاستخدام في العقود
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oracle_manager import get_oracle_manager

def explain_contract_usage_status():
    """شرح منطق حالة الاستخدام في العقود"""
    
    oracle_manager = get_oracle_manager()
    
    try:
        print("📋 شرح منطق حالة الاستخدام في العقود")
        print("=" * 60)
        
        # 1. شرح المنطق
        print("\n🔍 المنطق المستخدم:")
        print("   • العقد يصبح 'مُستخدم' (IS_USED = 1) عندما:")
        print("     - يتم ربطه بأمر شراء واحد أو أكثر")
        print("     - يوجد في جدول PURCHASE_ORDERS مع CONTRACT_ID")
        print("   • العقد يبقى 'غير مُستخدم' (IS_USED = 0) عندما:")
        print("     - لم يتم ربطه بأي أمر شراء")
        print("     - لا يوجد في جدول PURCHASE_ORDERS")
        
        # 2. فحص العقود الحالية
        print("\n📊 فحص العقود الحالية:")
        print("-" * 60)
        
        contracts_query = """
        SELECT c.CONTRACT_ID, c.CONTRACT_NUMBER, c.SUPPLIER_NAME, 
               c.IS_USED, c.CONTRACT_STATUS,
               COUNT(po.ID) as PURCHASE_ORDERS_COUNT
        FROM contracts c
        LEFT JOIN PURCHASE_ORDERS po ON c.CONTRACT_ID = po.CONTRACT_ID
        GROUP BY c.CONTRACT_ID, c.CONTRACT_NUMBER, c.SUPPLIER_NAME, 
                 c.IS_USED, c.CONTRACT_STATUS
        ORDER BY c.CONTRACT_ID
        """
        
        contracts_data = oracle_manager.execute_query(contracts_query, [])
        
        if contracts_data:
            print(f"{'ID':<5} {'رقم العقد':<15} {'المورد':<20} {'حالة الاستخدام':<15} {'عدد أوامر الشراء':<15}")
            print("-" * 80)
            
            for contract in contracts_data:
                contract_id = contract[0]
                contract_number = contract[1] or f"عقد-{contract_id}"
                supplier_name = (contract[2][:19] if contract[2] else 'غير محدد')
                is_used = contract[3]
                contract_status = contract[4] or 'DRAFT'
                po_count = contract[5] or 0
                
                usage_status = "مُستخدم ✅" if is_used == 1 else "غير مُستخدم ❌"
                
                print(f"{contract_id:<5} {contract_number:<15} {supplier_name:<20} {usage_status:<15} {po_count:<15}")
        else:
            print("❌ لا توجد عقود في النظام")
        
        # 3. فحص أوامر الشراء المرتبطة
        print("\n🛒 أوامر الشراء المرتبطة بالعقود:")
        print("-" * 60)
        
        po_query = """
        SELECT po.ID, po.PO_NUMBER, po.CONTRACT_ID, c.CONTRACT_NUMBER, 
               po.SUPPLIER_NAME, po.TOTAL_AMOUNT, po.STATUS
        FROM PURCHASE_ORDERS po
        LEFT JOIN contracts c ON po.CONTRACT_ID = c.CONTRACT_ID
        WHERE po.CONTRACT_ID IS NOT NULL
        ORDER BY po.CONTRACT_ID, po.ID
        """
        
        po_data = oracle_manager.execute_query(po_query, [])
        
        if po_data:
            print(f"{'أمر الشراء':<15} {'العقد':<15} {'المورد':<20} {'المبلغ':<15} {'الحالة':<15}")
            print("-" * 85)
            
            for po in po_data:
                po_number = po[1] or f"PO-{po[0]}"
                contract_number = po[3] or f"عقد-{po[2]}"
                supplier_name = (po[4][:19] if po[4] else 'غير محدد')
                total_amount = f"{po[5]:,.0f}" if po[5] else "0"
                status = po[6] or 'DRAFT'
                
                print(f"{po_number:<15} {contract_number:<15} {supplier_name:<20} {total_amount:<15} {status:<15}")
        else:
            print("❌ لا توجد أوامر شراء مرتبطة بعقود")
        
        # 4. فحص العقود غير المستخدمة
        print("\n🔍 العقود غير المستخدمة:")
        print("-" * 60)
        
        unused_query = """
        SELECT CONTRACT_ID, CONTRACT_NUMBER, SUPPLIER_NAME, CONTRACT_AMOUNT, CONTRACT_STATUS
        FROM contracts
        WHERE IS_USED = 0 OR IS_USED IS NULL
        ORDER BY CONTRACT_ID
        """
        
        unused_data = oracle_manager.execute_query(unused_query, [])
        
        if unused_data:
            print(f"{'ID':<5} {'رقم العقد':<15} {'المورد':<20} {'المبلغ':<15} {'الحالة':<15}")
            print("-" * 75)
            
            for contract in unused_data:
                contract_id = contract[0]
                contract_number = contract[1] or f"عقد-{contract_id}"
                supplier_name = (contract[2][:19] if contract[2] else 'غير محدد')
                amount = f"{contract[3]:,.0f}" if contract[3] else "0"
                status = contract[4] or 'DRAFT'
                
                print(f"{contract_id:<5} {contract_number:<15} {supplier_name:<20} {amount:<15} {status:<15}")
        else:
            print("✅ جميع العقود مُستخدمة")
        
        # 5. إحصائيات
        print("\n📈 إحصائيات:")
        print("-" * 60)
        
        stats_query = """
        SELECT 
            COUNT(*) as TOTAL_CONTRACTS,
            SUM(CASE WHEN IS_USED = 1 THEN 1 ELSE 0 END) as USED_CONTRACTS,
            SUM(CASE WHEN IS_USED = 0 OR IS_USED IS NULL THEN 1 ELSE 0 END) as UNUSED_CONTRACTS
        FROM contracts
        """
        
        stats_data = oracle_manager.execute_query(stats_query, [])
        
        if stats_data:
            total = stats_data[0][0]
            used = stats_data[0][1]
            unused = stats_data[0][2]
            
            used_percentage = (used / total * 100) if total > 0 else 0
            unused_percentage = (unused / total * 100) if total > 0 else 0
            
            print(f"📄 إجمالي العقود: {total}")
            print(f"✅ العقود المُستخدمة: {used} ({used_percentage:.1f}%)")
            print(f"❌ العقود غير المُستخدمة: {unused} ({unused_percentage:.1f}%)")
        
        # 6. كيفية تحديث الحالة
        print("\n🔄 كيفية تحديث حالة الاستخدام:")
        print("-" * 60)
        print("1. تلقائياً عند إنشاء أمر شراء جديد:")
        print("   - عند ربط أمر شراء بعقد، يتم تحديث IS_USED = 1")
        print("   - يحدث هذا في ملف: app/purchase_orders/routes.py")
        print("")
        print("2. يدوياً باستخدام SQL:")
        print("   UPDATE contracts SET IS_USED = 1")
        print("   WHERE contract_id IN (")
        print("       SELECT DISTINCT CONTRACT_ID")
        print("       FROM PURCHASE_ORDERS")
        print("       WHERE CONTRACT_ID IS NOT NULL")
        print("   );")
        print("")
        print("3. إعادة تعيين العقود غير المستخدمة:")
        print("   UPDATE contracts SET IS_USED = 0")
        print("   WHERE contract_id NOT IN (")
        print("       SELECT DISTINCT CONTRACT_ID")
        print("       FROM PURCHASE_ORDERS")
        print("       WHERE CONTRACT_ID IS NOT NULL")
        print("   );")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في شرح حالة الاستخدام: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء شرح منطق حالة الاستخدام في العقود...")
    success = explain_contract_usage_status()
    
    if success:
        print("\n✅ تم الانتهاء من الشرح بنجاح!")
    else:
        print("\n❌ فشل في الشرح")
        sys.exit(1)
