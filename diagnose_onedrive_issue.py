#!/usr/bin/env python3
"""
تشخيص مشاكل OneDrive
OneDrive Issues Diagnosis
"""

import requests
import json
import os
import sys

def diagnose_onedrive_config():
    """تشخيص إعدادات OneDrive"""
    
    print("🔍 تشخيص مشاكل OneDrive")
    print("=" * 50)
    
    # قراءة الإعدادات
    config_path = os.path.join('app', 'shipments', 'cloud_config.json')
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف الإعدادات: {e}")
        return False
    
    onedrive_config = config.get('onedrive', {})
    
    print("📋 الإعدادات الحالية:")
    print(f"  - Enabled: {onedrive_config.get('enabled')}")
    print(f"  - Client ID: {onedrive_config.get('client_id')}")
    print(f"  - Client Secret: {'***' if onedrive_config.get('client_secret') else 'None'}")
    print(f"  - Tenant ID: {onedrive_config.get('tenant_id')}")
    print(f"  - Redirect URI: {onedrive_config.get('redirect_uri')}")
    
    client_id = onedrive_config.get('client_id')
    client_secret = onedrive_config.get('client_secret')
    
    if not client_id or not client_secret:
        print("❌ Client ID أو Client Secret مفقود")
        return False
    
    print(f"\n🔍 تشخيص مفصل:")
    
    # فحص تنسيق Client ID
    if len(client_id) == 36 and client_id.count('-') == 4:
        print("✅ تنسيق Client ID صحيح")
    else:
        print("❌ تنسيق Client ID غير صحيح")
        print(f"   المتوقع: 12345678-1234-1234-1234-123456789012")
        print(f"   الحالي: {client_id}")
    
    # فحص تنسيق Client Secret
    if len(client_secret) > 10:
        print("✅ Client Secret يبدو صحيحاً")
    else:
        print("❌ Client Secret قصير جداً")
    
    # اختبار طلبات مختلفة
    print(f"\n🧪 اختبار طلبات مختلفة:")
    
    # اختبار 1: التحقق من وجود التطبيق
    print(f"\n1️⃣ اختبار وجود التطبيق...")
    test_app_exists(client_id)
    
    # اختبار 2: اختبار Client Secret
    print(f"\n2️⃣ اختبار Client Secret...")
    test_client_secret(client_id, client_secret)
    
    # اختبار 3: اختبار الصلاحيات
    print(f"\n3️⃣ اختبار الصلاحيات...")
    test_permissions(client_id, client_secret)
    
    return True

def test_app_exists(client_id):
    """اختبار وجود التطبيق"""
    try:
        # محاولة الحصول على معلومات التطبيق
        url = f"https://graph.microsoft.com/v1.0/applications"
        
        # هذا لن يعمل بدون token، لكن يمكننا اختبار الاستجابة
        response = requests.get(url, timeout=10)
        
        if response.status_code == 401:
            print("✅ التطبيق موجود (يحتاج مصادقة)")
        else:
            print(f"⚠️ استجابة غير متوقعة: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار وجود التطبيق: {e}")

def test_client_secret(client_id, client_secret):
    """اختبار Client Secret"""
    try:
        # اختبار مع endpoints مختلفة
        endpoints = [
            "https://login.microsoftonline.com/common/oauth2/v2.0/token",
            "https://login.microsoftonline.com/organizations/oauth2/v2.0/token"
        ]
        
        for endpoint in endpoints:
            print(f"   🔗 اختبار مع: {endpoint.split('/')[-3]}")
            
            data = {
                "client_id": client_id,
                "client_secret": client_secret,
                "scope": "https://graph.microsoft.com/.default",
                "grant_type": "client_credentials"
            }
            
            response = requests.post(endpoint, data=data, timeout=10)
            
            if response.status_code == 200:
                print("   ✅ Client Secret صحيح!")
                return True
            elif response.status_code == 401:
                try:
                    error_data = response.json()
                    error = error_data.get('error', '')
                    if 'invalid_client' in error:
                        print("   ❌ Client ID أو Client Secret غير صحيح")
                    elif 'unauthorized_client' in error:
                        print("   ❌ نوع التطبيق غير مدعوم")
                    else:
                        print(f"   ❌ خطأ مصادقة: {error}")
                except:
                    print(f"   ❌ خطأ 401: {response.text[:100]}")
            else:
                print(f"   ⚠️ استجابة غير متوقعة: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   📋 تفاصيل: {error_data.get('error', 'unknown')}")
                except:
                    print(f"   📄 Raw: {response.text[:100]}")
        
        return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار Client Secret: {e}")
        return False

def test_permissions(client_id, client_secret):
    """اختبار الصلاحيات"""
    try:
        # محاولة الحصول على token مع صلاحيات محددة
        url = "https://login.microsoftonline.com/organizations/oauth2/v2.0/token"
        
        data = {
            "client_id": client_id,
            "client_secret": client_secret,
            "scope": "https://graph.microsoft.com/.default",
            "grant_type": "client_credentials"
        }
        
        response = requests.post(url, data=data, timeout=10)
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get('access_token')
            
            if access_token:
                print("✅ تم الحصول على access token!")
                
                # اختبار الصلاحيات
                test_graph_api(access_token)
                return True
        else:
            print(f"❌ فشل في الحصول على token: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   📋 الخطأ: {error_data.get('error_description', 'unknown')}")
            except:
                pass
        
        return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الصلاحيات: {e}")
        return False

def test_graph_api(access_token):
    """اختبار Graph API"""
    try:
        # اختبار الوصول لـ OneDrive
        url = "https://graph.microsoft.com/v1.0/me/drive"
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ الوصول لـ OneDrive ناجح!")
            drive_data = response.json()
            print(f"   📁 Drive ID: {drive_data.get('id', 'unknown')}")
        elif response.status_code == 403:
            print("❌ ممنوع - تحقق من الصلاحيات في Azure Portal")
        else:
            print(f"❌ خطأ في الوصول لـ OneDrive: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار Graph API: {e}")

def suggest_fixes():
    """اقتراح حلول"""
    
    print(f"\n💡 اقتراحات الإصلاح:")
    print("=" * 50)
    
    print("1️⃣ تحقق من Azure Portal:")
    print("   - تأكد من صحة Client ID")
    print("   - أنشئ Client Secret جديد")
    print("   - تأكد من نوع التطبيق (Web app)")
    
    print("\n2️⃣ تحقق من الصلاحيات:")
    print("   - API permissions → Microsoft Graph")
    print("   - Application permissions (ليس Delegated)")
    print("   - Files.ReadWrite.All")
    print("   - Sites.ReadWrite.All")
    print("   - Grant admin consent")
    
    print("\n3️⃣ تحقق من نوع الحساب:")
    print("   - الحسابات الشخصية قد لا تدعم Client Credentials")
    print("   - جرب حساب مؤسسي إذا أمكن")
    print("   - أو استخدم وضع التجريب")
    
    print("\n4️⃣ بدائل:")
    print("   - تفعيل demo_mode في cloud_config.json")
    print("   - استخدام Nextcloud بدلاً من OneDrive")
    print("   - إعداد حساب Microsoft 365 للأعمال")

if __name__ == "__main__":
    success = diagnose_onedrive_config()
    
    if not success:
        suggest_fixes()
    
    print(f"\n🎯 الخلاصة:")
    print("إذا استمرت المشاكل، يُنصح بتفعيل وضع التجريب مؤقتاً")
    print("أو استخدام خدمة سحابية أخرى")
