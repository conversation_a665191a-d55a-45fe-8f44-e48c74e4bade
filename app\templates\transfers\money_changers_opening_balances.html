<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأرصدة الافتتاحية للصرافين/البنوك - نظام الفوجي</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    
    <style>
        :root {
            --primary: #2c3e50;
            --secondary: #3498db;
            --success: #27ae60;
            --warning: #f39c12;
            --danger: #e74c3c;
            --info: #17a2b8;
            --light: #f8f9fa;
            --dark: #343a40;
            --surface: #ffffff;
            --border: #dee2e6;
            --text-primary: #2c3e50;
            --text-secondary: #6c757d;
            --text-muted: #adb5bd;
            --radius: 12px;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: var(--text-primary);
        }

        /* Header */
        .page-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-top: 0.5rem;
        }

        /* Breadcrumb */
        .breadcrumb-container {
            background: var(--surface);
            padding: 1rem 0;
            margin-bottom: 2rem;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
        }

        .breadcrumb {
            background: none;
            margin: 0;
            padding: 0;
        }

        .breadcrumb-item a {
            color: var(--secondary);
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb-item a:hover {
            color: var(--primary);
        }

        /* Cards */
        .card-modern {
            background: var(--surface);
            border: none;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .card-header-modern {
            background: linear-gradient(135deg, var(--light) 0%, #e9ecef 100%);
            border-bottom: 1px solid var(--border);
            padding: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Stats Cards */
        .stats-card {
            background: var(--surface);
            border-radius: var(--radius);
            padding: 2rem;
            text-align: center;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            border-left: 4px solid var(--secondary);
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stats-card.success {
            border-left-color: var(--success);
        }

        .stats-card.warning {
            border-left-color: var(--warning);
        }

        .stats-card.danger {
            border-left-color: var(--danger);
        }

        .stats-card.info {
            border-left-color: var(--info);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
            display: block;
        }

        .stats-label {
            font-size: 1rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .stats-icon {
            font-size: 3rem;
            opacity: 0.1;
            position: absolute;
            top: 1rem;
            left: 1rem;
        }

        /* Control Panel */
        .control-panel {
            background: var(--surface);
            border-radius: var(--radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .form-control-modern {
            border: 2px solid var(--border);
            border-radius: var(--radius);
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control-modern:focus {
            border-color: var(--secondary);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .btn-modern {
            border-radius: var(--radius);
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        /* Table */
        .table-container {
            background: var(--surface);
            border-radius: var(--radius);
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .table-modern {
            margin: 0;
            font-size: 0.9rem;
        }

        .table-modern thead th {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            border: none;
            padding: 1rem;
            font-weight: 600;
            text-align: center;
        }

        .table-modern tbody td {
            padding: 1rem;
            border-bottom: 1px solid var(--border);
            vertical-align: middle;
            text-align: center;
        }

        .table-modern tbody tr:hover {
            background: var(--light);
        }

        /* Status Badges */
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-draft {
            background: #ffeaa7;
            color: #d63031;
        }

        .status-approved {
            background: #55efc4;
            color: #00b894;
        }

        .status-posted {
            background: #74b9ff;
            color: #0984e3;
        }

        /* Loading */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            background: white;
            padding: 2rem;
            border-radius: var(--radius);
            text-align: center;
        }

        /* Money Changer Search */
        .money-changer-search-container {
            position: relative;
        }

        .money-changer-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1050;
            background: white;
            border: 1px solid var(--border);
            border-radius: var(--radius);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            max-height: 300px;
            overflow-y: auto;
        }

        .money-changer-dropdown-content {
            padding: 0.5rem 0;
        }

        .money-changer-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
            border-bottom: 1px solid #f8f9fa;
            transition: all 0.2s ease;
        }

        .money-changer-item:hover {
            background: var(--light);
        }

        .money-changer-item:last-child {
            border-bottom: none;
        }

        .money-changer-item.selected {
            background: var(--primary);
            color: white;
        }

        .money-changer-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .money-changer-code {
            font-size: 0.85rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        .money-changer-item.selected .money-changer-name,
        .money-changer-item.selected .money-changer-code {
            color: white;
        }

        .no-results {
            padding: 1rem;
            text-align: center;
            color: var(--text-secondary);
            font-style: italic;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .page-title {
                font-size: 2rem;
            }

            .stats-card {
                margin-bottom: 1rem;
            }

            .table-responsive {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div class="mt-3">جاري تحميل البيانات...</div>
        </div>
    </div>

    <!-- Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="fas fa-balance-scale me-3"></i>
                        إدارة الأرصدة الافتتاحية للصرافين/البنوك
                    </h1>
                    <p class="page-subtitle">
                        إدخال ومراجعة واعتماد الأرصدة الافتتاحية للصرافين والبنوك للفترة المحاسبية الجديدة
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-modern" onclick="window.history.back()">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="breadcrumb-container">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="/dashboard">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="/transfers/dashboard">
                            <i class="fas fa-exchange-alt me-1"></i>
                            نظام الحوالات
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <i class="fas fa-calculator me-1"></i>
                        الأرصدة الافتتاحية للصرافين/البنوك
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Control Panel -->
        <div class="control-panel">
            <div class="row align-items-end">
                <div class="col-md-3">
                    <label class="form-label fw-bold">البحث السريع للصراف/البنك</label>
                    <div class="position-relative">
                        <input type="text" id="moneyChangerFilterSearch" class="form-control form-control-modern"
                               placeholder="ابحث عن صراف/بنك..." autocomplete="off">
                        <input type="hidden" id="moneyChangerFilterId">
                        <div class="dropdown-menu" id="moneyChangerFilterDropdown" style="display: none; width: 100%; max-height: 200px; overflow-y: auto;">
                        </div>
                        <div class="position-absolute top-50 end-0 translate-middle-y me-2">
                            <button type="button" id="clearMoneyChangerFilter" class="btn btn-sm btn-link text-muted p-0 me-2"
                                    style="display: none; border: none; background: none;"
                                    onclick="clearMoneyChangerFilter()" title="مسح الفلتر">
                                <i class="fas fa-times"></i>
                            </button>
                            <i class="fas fa-search text-muted"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">العملة</label>
                    <select id="currencyCode" class="form-control form-control-modern">
                        <option value="">جميع العملات</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">حالة الأرصدة</label>
                    <select id="statusFilter" class="form-control form-control-modern">
                        <option value="all">جميع الحالات</option>
                        <option value="draft">مسودة</option>
                        <option value="approved">معتمد</option>
                        <option value="posted">مرحل</option>
                    </select>
                </div>
                <div class="col-md-5">
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary btn-modern" onclick="loadData()">
                            <i class="fas fa-sync-alt me-2"></i>تحديث البيانات
                        </button>
                        <button class="btn btn-success btn-modern" onclick="showAddModal()">
                            <i class="fas fa-plus me-2"></i>إضافة رصيد
                        </button>
                        <button class="btn btn-info btn-modern" onclick="showImportModal()">
                            <i class="fas fa-file-excel me-2"></i>استيراد Excel
                        </button>
                        <button id="approveBtn" class="btn btn-warning btn-modern" onclick="handleApprovalAction()" title="عمليات جماعية">
                            <i id="approveIcon" class="fas fa-check me-2"></i><span id="approveText">اعتماد جماعي</span>
                        </button>
                        <button id="postBtn" class="btn btn-info btn-modern" onclick="handlePostingAction()" title="عمليات جماعية">
                            <i id="postIcon" class="fas fa-paper-plane me-2"></i><span id="postText">ترحيل جماعي</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4" id="statsContainer">
            <div class="col-md-3">
                <div class="stats-card success position-relative">
                    <i class="fas fa-building stats-icon"></i>
                    <span class="stats-number" id="totalMoneyChangers">0</span>
                    <div class="stats-label">إجمالي الصرافين/البنوك</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card info position-relative">
                    <i class="fas fa-arrow-up stats-icon"></i>
                    <span class="stats-number" id="totalDebit">0</span>
                    <div class="stats-label">إجمالي المدين</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card warning position-relative">
                    <i class="fas fa-arrow-down stats-icon"></i>
                    <span class="stats-number" id="totalCredit">0</span>
                    <div class="stats-label">إجمالي الدائن</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card danger position-relative">
                    <i class="fas fa-balance-scale stats-icon"></i>
                    <span class="stats-number" id="netBalance">0</span>
                    <div class="stats-label">صافي الرصيد</div>
                </div>
            </div>
        </div>

        <!-- Opening Balances Table -->
        <div class="card-modern">
            <div class="card-header-modern">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        قائمة الأرصدة الافتتاحية
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="exportToExcel()">
                            <i class="fas fa-file-excel me-1"></i>تصدير Excel
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="printTable()">
                            <i class="fas fa-print me-1"></i>طباعة
                        </button>
                    </div>
                </div>
            </div>
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-modern" id="openingBalancesTable">
                        <thead>
                            <tr>
                                <th>الصراف/البنك</th>
                                <th>كود الصراف/البنك</th>
                                <th>الرصيد الافتتاحي</th>
                                <th>نوع الرصيد</th>
                                <th>العملة</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="openingBalancesTableBody">
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                    <div class="mt-2">جاري تحميل البيانات...</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Modal -->
    <div class="modal fade" id="addEditModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">إضافة رصيد افتتاحي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="openingBalanceForm">
                        <input type="hidden" id="balanceId">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">الصراف/البنك *</label>
                                <div class="money-changer-search-container">
                                    <input type="text" id="moneyChangerSearch" class="form-control form-control-modern"
                                           placeholder="ابحث عن الصراف/البنك بالاسم أو الكود..." autocomplete="off">
                                    <input type="hidden" id="moneyChangerId" required>
                                    <div id="moneyChangerDropdown" class="money-changer-dropdown" style="display: none;">
                                        <div class="money-changer-dropdown-content">
                                            <!-- نتائج البحث ستظهر هنا -->
                                        </div>
                                    </div>
                                </div>
                                <small class="text-muted">اكتب اسم الصراف/البنك أو كوده للبحث السريع</small>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">العملة *</label>
                                <select id="modalCurrencyCode" class="form-control form-control-modern" required>
                                    <option value="">اختر العملة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">سعر الصرف</label>
                                <input type="number" id="exchangeRate" class="form-control form-control-modern"
                                       value="1" step="0.0001" min="0">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">نوع الرصيد *</label>
                                <select id="balanceType" class="form-control form-control-modern" required>
                                    <option value="">اختر نوع الرصيد</option>
                                    <option value="DEBIT">مدين (لنا على الصراف/البنك)</option>
                                    <option value="CREDIT">دائن (للصراف/البنك علينا)</option>
                                </select>
                                <small class="text-muted">مدين = مبلغ مستحق لنا، دائن = مبلغ مستحق للصراف/البنك</small>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">مبلغ الرصيد الافتتاحي *</label>
                                <input type="number" id="openingBalanceAmount" class="form-control form-control-modern"
                                       required step="0.01" min="0.01">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الفترة المحاسبية *</label>
                                <input type="date" id="modalFiscalDate" class="form-control form-control-modern" required>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">المستند المرجعي</label>
                                <input type="text" id="referenceDocument" class="form-control form-control-modern"
                                       placeholder="رقم المستند أو المرجع">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <label class="form-label">ملاحظات</label>
                                <textarea id="notes" class="form-control form-control-modern" rows="3"
                                          placeholder="ملاحظات إضافية (اختياري)"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveOpeningBalance()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <script>
        // متغيرات عامة
        let currentData = {
            moneyChangers: [],
            openingBalances: [],
            summary: {},
            currencies: []
        };

        let isFirstLoad = true;

        // متغيرات العمليات الجماعية
        let currentApprovalAction = 'approve'; // approve أو unapprove
        let currentPostingAction = 'post'; // post أو unpost

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            setupEventHandlers();
        });

        // إعداد event listeners للفلاتر
        function setupFilterEventListeners() {
            // فلتر العملة
            const currencyCodeSelect = document.getElementById('currencyCode');
            if (currencyCodeSelect) {
                currencyCodeSelect.addEventListener('change', function() {
                    console.log('Currency filter changed:', this.value);
                    loadData();
                });
            }

            // فلتر الحالة
            const statusFilterSelect = document.getElementById('statusFilter');
            if (statusFilterSelect) {
                statusFilterSelect.addEventListener('change', function() {
                    console.log('Status filter changed:', this.value);
                    loadData();
                });
            }

            // فلتر الصراف/البنك (يتم التعامل معه في اختيار الصراف)
            // سيتم تشغيل loadData() عند اختيار صراف من القائمة المنسدلة
        }

        // إعداد أحداث البحث والتفاعل
        function setupEventHandlers() {
            // إعداد event listeners للفلاتر
            setupFilterEventListeners();

            // البحث السريع للصراف/البنك في الفلتر
            const filterSearchInput = document.getElementById('moneyChangerFilterSearch');
            const filterDropdown = document.getElementById('moneyChangerFilterDropdown');

            if (filterSearchInput) {
                filterSearchInput.addEventListener('input', function() {
                    const query = this.value.toLowerCase();

                    if (query.length < 2) {
                        filterDropdown.style.display = 'none';
                        return;
                    }

                    const filtered = currentData.moneyChangers.filter(mc =>
                        mc.name.toLowerCase().includes(query) ||
                        (mc.type === 'bank' ? 'بنك' : 'صرافة').includes(query)
                    );

                    filterDropdown.innerHTML = '';
                    if (filtered.length === 0) {
                        filterDropdown.innerHTML = '<div class="dropdown-item text-muted">لا توجد نتائج</div>';
                    } else {
                        filtered.forEach(mc => {
                            const typeText = mc.type === 'bank' ? 'بنك' : 'صرافة';
                            const item = document.createElement('div');
                            item.className = 'dropdown-item money-changer-filter-item';
                            item.setAttribute('data-id', mc.id);
                            item.setAttribute('data-name', mc.name);
                            item.style.cursor = 'pointer';
                            item.innerHTML = `
                                <strong>${mc.name}</strong><br>
                                <small class="text-muted">${typeText}</small>
                            `;
                            filterDropdown.appendChild(item);
                        });
                    }
                    filterDropdown.style.display = 'block';
                });
            }

            // البحث السريع للصراف/البنك في النموذج
            const modalSearchInput = document.getElementById('moneyChangerSearch');
            const modalDropdown = document.getElementById('moneyChangerDropdown');

            if (modalSearchInput) {
                modalSearchInput.addEventListener('input', function() {
                    const query = this.value.toLowerCase();
                    const dropdownContent = modalDropdown.querySelector('.money-changer-dropdown-content');

                    if (query.length < 2) {
                        modalDropdown.style.display = 'none';
                        return;
                    }

                    const filtered = currentData.moneyChangers.filter(mc =>
                        mc.name.toLowerCase().includes(query) ||
                        (mc.type === 'bank' ? 'بنك' : 'صرافة').includes(query)
                    );

                    dropdownContent.innerHTML = '';
                    if (filtered.length === 0) {
                        dropdownContent.innerHTML = '<div class="no-results">لا توجد نتائج</div>';
                    } else {
                        filtered.forEach(mc => {
                            const typeText = mc.type === 'bank' ? 'بنك' : 'صرافة';
                            const item = document.createElement('div');
                            item.className = 'money-changer-item';
                            item.setAttribute('data-id', mc.id);
                            item.setAttribute('data-name', mc.name);
                            item.style.cursor = 'pointer';
                            item.innerHTML = `
                                <div class="money-changer-name">${mc.name}</div>
                                <div class="money-changer-code">${typeText}</div>
                            `;
                            dropdownContent.appendChild(item);
                        });
                    }
                    modalDropdown.style.display = 'block';
                });
            }

            // اختيار صراف/بنك من فلتر البحث
            document.addEventListener('click', function(e) {
                if (e.target.closest('.money-changer-filter-item')) {
                    const item = e.target.closest('.money-changer-filter-item');
                    const id = item.getAttribute('data-id');
                    const name = item.getAttribute('data-name');

                    document.getElementById('moneyChangerFilterId').value = id;
                    document.getElementById('moneyChangerFilterSearch').value = name;
                    document.getElementById('moneyChangerFilterDropdown').style.display = 'none';
                    document.getElementById('clearMoneyChangerFilter').style.display = 'inline-block';

                    // إعادة تحميل البيانات مع الفلتر الجديد
                    loadData();
                }
            });

            // اختيار صراف/بنك من نموذج الإضافة
            document.addEventListener('click', function(e) {
                if (e.target.closest('.money-changer-item')) {
                    const item = e.target.closest('.money-changer-item');
                    const id = item.getAttribute('data-id');
                    const name = item.getAttribute('data-name');

                    document.getElementById('moneyChangerId').value = id;
                    document.getElementById('moneyChangerSearch').value = name;
                    document.getElementById('moneyChangerDropdown').style.display = 'none';
                }
            });

            // إخفاء القوائم المنسدلة عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.money-changer-search-container')) {
                    document.querySelectorAll('.money-changer-dropdown').forEach(dropdown => {
                        dropdown.style.display = 'none';
                    });
                }
                if (!e.target.closest('#moneyChangerFilterSearch')) {
                    document.getElementById('moneyChangerFilterDropdown').style.display = 'none';
                }
            });
        }

        // تحميل البيانات
        async function loadData() {
            showLoading(true);

            try {
                // تحميل العملات فقط في التحميل الأول
                if (currentData.currencies.length === 0) {
                    await loadCurrencies();
                }

                // تحميل الصرافين/البنوك فقط في التحميل الأول
                if (currentData.moneyChangers.length === 0) {
                    await loadMoneyChangers();
                }

                // تحميل الأرصدة الافتتاحية
                await loadOpeningBalances();

                // تحميل الملخص
                await loadSummary();

                // تحديث حالة الأزرار
                updateButtonStates(currentData.openingBalances);

            } catch (error) {
                console.error('خطأ في تحميل البيانات:', error);
                showAlert('حدث خطأ في تحميل البيانات', 'error');
            } finally {
                showLoading(false);
            }
        }

        // تحميل العملات
        async function loadCurrencies() {
            try {
                const response = await fetch('/transfers/api/money-changers-opening-balances/currencies');
                const data = await response.json();

                if (data.success) {
                    currentData.currencies = data.currencies;
                    updateCurrencyDropdowns();
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('خطأ في تحميل العملات:', error);
                // استخدام العملات الافتراضية في حالة الخطأ
                currentData.currencies = [
                    {code: 'SAR', name_ar: 'ريال سعودي', symbol: 'ر.س', is_base_currency: true},
                    {code: 'USD', name_ar: 'دولار أمريكي', symbol: '$', is_base_currency: false},
                    {code: 'EUR', name_ar: 'يورو', symbol: '€', is_base_currency: false}
                ];
                updateCurrencyDropdowns();
            }
        }

        // تحديث قوائم العملات
        function updateCurrencyDropdowns() {
            const filterDropdown = document.getElementById('currencyCode');
            const modalDropdown = document.getElementById('modalCurrencyCode');

            // حفظ القيمة المختارة حالياً
            const currentFilterValue = filterDropdown.value;
            const currentModalValue = modalDropdown.value;

            // تحديث فلتر العملة
            filterDropdown.innerHTML = '';
            modalDropdown.innerHTML = '<option value="">اختر العملة</option>';

            // إضافة خيار "جميع العملات" للفلتر
            filterDropdown.innerHTML = '<option value="">جميع العملات</option>';

            currentData.currencies.forEach(currency => {
                const optionText = `${currency.name_ar} (${currency.code})`;

                // إضافة للفلتر
                const filterOption = document.createElement('option');
                filterOption.value = currency.code;
                filterOption.textContent = optionText;
                filterDropdown.appendChild(filterOption);

                // إضافة للنموذج
                const modalOption = document.createElement('option');
                modalOption.value = currency.code;
                modalOption.textContent = optionText;
                modalOption.dataset.symbol = currency.symbol;
                modalOption.dataset.exchangeRate = currency.exchange_rate;
                modalDropdown.appendChild(modalOption);
            });

            // استعادة القيم المختارة أو الاحتفاظ بـ "جميع العملات" كافتراضي
            if (currentFilterValue) {
                filterDropdown.value = currentFilterValue;
            } else {
                // الاحتفاظ بـ "جميع العملات" كافتراضي
                filterDropdown.value = '';
            }

            if (currentModalValue) {
                modalDropdown.value = currentModalValue;
            } else if (isFirstLoad) {
                // تعيين العملة الأساسية للنموذج فقط في التحميل الأول
                const baseCurrency = currentData.currencies.find(c => c.is_base_currency);
                if (baseCurrency) {
                    modalDropdown.value = baseCurrency.code;
                }
                isFirstLoad = false;
            }
        }

        // تحميل قائمة الصرافين/البنوك
        async function loadMoneyChangers() {
            try {
                const response = await fetch('/transfers/api/money-changers-opening-balances/money-changers');
                const data = await response.json();

                if (data.success) {
                    currentData.moneyChangers = data.money_changers;
                    updateMoneyChangersDropdown();
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('خطأ في تحميل الصرافين/البنوك:', error);
                throw error;
            }
        }

        // تحميل الأرصدة الافتتاحية
        async function loadOpeningBalances() {
            try {
                const moneyChangerFilterId = document.getElementById('moneyChangerFilterId').value;
                const currencyCode = document.getElementById('currencyCode').value;
                const statusFilter = document.getElementById('statusFilter').value;

                const params = new URLSearchParams({
                    fiscal_date: '2025-01-01', // تاريخ ثابت للفترة المحاسبية
                    currency_code: currencyCode,
                    status: statusFilter
                });

                // إضافة فلتر الصراف/البنك إذا كان محدداً
                if (moneyChangerFilterId) {
                    params.append('money_changer_id', moneyChangerFilterId);
                }

                const response = await fetch(`/transfers/api/money-changers-opening-balances/opening_balances?${params}`);
                const data = await response.json();

                if (data.success) {
                    currentData.openingBalances = data.opening_balances;
                    updateOpeningBalancesTable();
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('خطأ في تحميل الأرصدة الافتتاحية:', error);
                throw error;
            }
        }

        // تحميل الملخص
        async function loadSummary() {
            try {
                const moneyChangerFilterId = document.getElementById('moneyChangerFilterId').value;
                const currencyCode = document.getElementById('currencyCode').value;

                const params = new URLSearchParams({
                    fiscal_date: '2025-01-01', // تاريخ ثابت للفترة المحاسبية
                    currency_code: currencyCode
                });

                // إضافة فلتر الصراف/البنك إذا كان محدداً
                if (moneyChangerFilterId) {
                    params.append('money_changer_id', moneyChangerFilterId);
                }

                const response = await fetch(`/transfers/api/money-changers-opening-balances/summary?${params}`);
                const data = await response.json();

                if (data.success) {
                    currentData.summary = data.summary;
                    updateSummaryCards();
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('خطأ في تحميل الملخص:', error);
                throw error;
            }
        }

        // تحديث بطاقات الملخص
        function updateSummaryCards() {
            const summary = currentData.summary;

            document.getElementById('totalMoneyChangers').textContent = summary.total_money_changers || 0;
            document.getElementById('totalDebit').textContent = formatCurrency(summary.total_debit || 0);
            document.getElementById('totalCredit').textContent = formatCurrency(summary.total_credit || 0);
            document.getElementById('netBalance').textContent = formatCurrency(summary.net_balance || 0);
        }

        // تحديث جدول الأرصدة الافتتاحية
        function updateOpeningBalancesTable() {
            const tbody = document.getElementById('openingBalancesTableBody');
            tbody.innerHTML = '';

            if (currentData.openingBalances.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد أرصدة افتتاحية</p>
                        </td>
                    </tr>
                `;
                return;
            }

            currentData.openingBalances.forEach(balance => {
                const row = createTableRow(balance);
                tbody.appendChild(row);
            });
        }

        // إنشاء صف في الجدول
        function createTableRow(balance) {
            const row = document.createElement('tr');

            const statusBadge = getStatusBadge(balance.status);
            const balanceTypeBadge = balance.balance_type === 'DEBIT' ?
                '<span class="badge bg-warning">مدين</span>' :
                '<span class="badge bg-success">دائن</span>';

            row.innerHTML = `
                <td>
                    <strong>${balance.money_changer_name}</strong>
                </td>
                <td>
                    <span class="text-muted">${balance.money_changer_code || ''}</span>
                </td>
                <td class="text-end">
                    <strong>${formatCurrency(balance.opening_balance_amount)}</strong>
                </td>
                <td>
                    ${balanceTypeBadge}
                </td>
                <td>
                    <span class="badge bg-secondary">${balance.currency_code}</span>
                </td>
                <td>
                    ${statusBadge}
                </td>
                <td class="text-center">${formatDate(balance.created_date)}</td>
                <td>
                    <div class="btn-group" role="group">
                        ${getActionButtons(balance)}
                    </div>
                </td>
            `;

            return row;
        }

        // إنشاء أزرار الإجراءات
        function getActionButtons(balance) {
            let buttons = '';

            // أزرار الحالة (اعتماد/ترحيل) أولاً
            if (balance.status === 'DRAFT') {
                // مسودة: زر اعتماد
                buttons += `
                    <button class="btn btn-sm btn-warning" onclick="approveBalance(${balance.id})" title="اعتماد">
                        <i class="fas fa-check"></i>
                    </button>
                `;
            } else if (balance.status === 'APPROVED') {
                // معتمد: زر إلغاء اعتماد + زر ترحيل
                buttons += `
                    <button class="btn btn-sm btn-secondary" onclick="unapproveBalance(${balance.id})" title="إلغاء الاعتماد">
                        <i class="fas fa-undo"></i>
                    </button>
                    <button class="btn btn-sm btn-info" onclick="postBalance(${balance.id})" title="ترحيل">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                `;
            } else if (balance.status === 'POSTED') {
                // مرحل: زر إلغاء ترحيل
                buttons += `
                    <button class="btn btn-sm btn-secondary" onclick="unpostBalance(${balance.id})" title="إلغاء الترحيل">
                        <i class="fas fa-undo"></i>
                    </button>
                `;
            }

            // أزرار التحرير والحذف (للمسودة فقط)
            if (balance.status === 'DRAFT') {
                buttons += `
                    <button class="btn btn-sm btn-outline-primary" onclick="editBalance(${balance.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteBalance(${balance.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
            } else {
                // للحالات الأخرى: زر عرض فقط
                buttons += `
                    <button class="btn btn-sm btn-outline-info" onclick="viewBalance(${balance.id})" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                `;
            }

            return buttons;
        }

        // الحصول على شارة الحالة
        function getStatusBadge(status) {
            switch(status) {
                case 'DRAFT': return '<span class="status-badge status-draft">مسودة</span>';
                case 'APPROVED': return '<span class="status-badge status-approved">معتمد</span>';
                case 'POSTED': return '<span class="status-badge status-posted">مرحل</span>';
                default: return '<span class="status-badge status-draft">غير محدد</span>';
            }
        }

        // عرض/إخفاء شاشة التحميل
        function showLoading(show) {
            const overlay = document.getElementById('loadingOverlay');
            overlay.style.display = show ? 'flex' : 'none';
        }

        // عرض رسالة تنبيه
        function showAlert(message, type = 'info') {
            // يمكن تطوير هذه الدالة لاحقاً لعرض رسائل أفضل
            if (type === 'error') {
                alert('خطأ: ' + message);
            } else {
                alert(message);
            }
        }

        // تنسيق العملة (بالأرقام الإنجليزية)
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount);
        }

        // تنسيق التاريخ
        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        // عرض نموذج الإضافة
        function showAddModal() {
            document.getElementById('modalTitle').textContent = 'إضافة رصيد افتتاحي جديد';
            document.getElementById('balanceId').value = '';
            document.getElementById('openingBalanceForm').reset();
            document.getElementById('exchangeRate').value = '1';
            document.getElementById('moneyChangerId').value = '';
            document.getElementById('moneyChangerSearch').value = '';

            // تعيين التاريخ الافتراضي للفترة المحاسبية
            const today = new Date();
            const fiscalDate = `${today.getFullYear()}-01-01`; // بداية السنة المالية
            document.getElementById('modalFiscalDate').value = fiscalDate;

            const modal = new bootstrap.Modal(document.getElementById('addEditModal'));
            modal.show();
        }

        // حفظ الرصيد الافتتاحي
        function saveOpeningBalance() {
            const form = document.getElementById('openingBalanceForm');

            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            if (!document.getElementById('moneyChangerId').value) {
                showAlert('يرجى اختيار الصراف/البنك', 'error');
                return;
            }

            const data = {
                money_changer_id: document.getElementById('moneyChangerId').value,
                currency_code: document.getElementById('modalCurrencyCode').value,
                amount: parseFloat(document.getElementById('openingBalanceAmount').value),
                exchange_rate: parseFloat(document.getElementById('exchangeRate').value) || 1.0,
                balance_type: document.getElementById('balanceType').value,
                reference_document: document.getElementById('referenceDocument').value,
                fiscal_date: document.getElementById('modalFiscalDate').value,
                notes: document.getElementById('notes').value
            };

            const balanceId = document.getElementById('balanceId').value;
            const url = balanceId ?
                `/transfers/api/money-changers-opening-balances/update/${balanceId}` :
                '/transfers/api/money-changers-opening-balances/add';
            const method = balanceId ? 'PUT' : 'POST';

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message);
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addEditModal'));
                    modal.hide();
                    loadData();
                } else {
                    showAlert(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                showAlert('حدث خطأ في حفظ البيانات', 'error');
            });
        }

        // دوال أخرى (ستكمل لاحقاً)
        function updateMoneyChangersDropdown() {
            // سيتم تطويرها لاحقاً
        }

        function updateButtonStates() {
            // سيتم تطويرها لاحقاً
        }

        function editBalance(id) {
            showAlert('تعديل الرصيد - قيد التطوير');
        }

        function deleteBalance(id) {
            if (confirm('هل أنت متأكد من حذف هذا الرصيد؟')) {
                showAlert('حذف الرصيد - قيد التطوير');
            }
        }

        function viewBalance(id) {
            showAlert('عرض الرصيد - قيد التطوير');
        }

        function exportToExcel() {
            showAlert('تصدير Excel - قيد التطوير');
        }

        function printTable() {
            window.print();
        }

        function showImportModal() {
            showAlert('استيراد Excel - قيد التطوير');
        }

        // مسح فلتر الصراف/البنك
        function clearMoneyChangerFilter() {
            document.getElementById('moneyChangerFilterSearch').value = '';
            document.getElementById('moneyChangerFilterId').value = '';
            document.getElementById('clearMoneyChangerFilter').style.display = 'none';
            loadData();
        }

        // تحديث حالة الأزرار الجماعية
        function updateButtonStates(openingBalances) {
            const approveBtn = document.getElementById('approveBtn');
            const approveIcon = document.getElementById('approveIcon');
            const approveText = document.getElementById('approveText');
            const postBtn = document.getElementById('postBtn');
            const postIcon = document.getElementById('postIcon');
            const postText = document.getElementById('postText');

            // تحليل حالات الأرصدة
            let hasDraft = false;
            let hasApproved = false;
            let hasPosted = false;

            openingBalances.forEach(balance => {
                if (balance.status === 'DRAFT') hasDraft = true;
                else if (balance.status === 'APPROVED') hasApproved = true;
                else if (balance.status === 'POSTED') hasPosted = true;
            });

            // تحديث زر الاعتماد
            if (hasDraft && !hasApproved && !hasPosted) {
                // فقط مسودات: اعتماد
                currentApprovalAction = 'approve';
                approveIcon.className = 'fas fa-check me-2';
                approveText.textContent = 'اعتماد جماعي';
            } else if (!hasDraft && (hasApproved || hasPosted)) {
                // فقط معتمد أو مرحل: إلغاء اعتماد
                currentApprovalAction = 'unapprove';
                approveIcon.className = 'fas fa-undo me-2';
                approveText.textContent = 'إلغاء الاعتماد';
            } else {
                // مختلط: اعتماد الأرصدة
                currentApprovalAction = 'approve';
                approveIcon.className = 'fas fa-check me-2';
                approveText.textContent = 'اعتماد الأرصدة';
            }

            // تحديث زر الترحيل
            if (hasApproved && !hasPosted) {
                // فقط معتمد: ترحيل
                currentPostingAction = 'post';
                postIcon.className = 'fas fa-paper-plane me-2';
                postText.textContent = 'ترحيل جماعي';
            } else if (!hasApproved && hasPosted) {
                // فقط مرحل: إلغاء ترحيل
                currentPostingAction = 'unpost';
                postIcon.className = 'fas fa-undo me-2';
                postText.textContent = 'إلغاء الترحيل';
            } else {
                // مختلط أو فارغ: ترحيل الأرصدة
                currentPostingAction = 'post';
                postIcon.className = 'fas fa-paper-plane me-2';
                postText.textContent = 'ترحيل الأرصدة';
            }

            // تعطيل الأزرار حسب الحالة
            if (approveBtn) approveBtn.disabled = (!hasDraft && !hasApproved);
            if (postBtn) postBtn.disabled = (!hasApproved && !hasPosted);
        }

        // معالج الاعتماد الذكي
        async function handleApprovalAction() {
            const actionText = currentApprovalAction === 'approve' ? 'اعتماد' : 'إلغاء اعتماد';
            const confirmText = currentApprovalAction === 'approve' ?
                'هل أنت متأكد من اعتماد جميع الأرصدة الافتتاحية المسودة؟' :
                'هل أنت متأكد من إلغاء اعتماد جميع الأرصدة الافتتاحية المعتمدة؟';

            if (!confirm(confirmText)) {
                return;
            }

            try {
                const response = await fetch(`/transfers/api/money-changers-opening-balances/bulk-${currentApprovalAction}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        fiscal_date: '2025-01-01'
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showAlert(`تم ${actionText} الأرصدة الافتتاحية بنجاح`, 'success');
                    loadData();
                } else {
                    showAlert(result.message || `حدث خطأ أثناء ${actionText} الأرصدة الافتتاحية`, 'error');
                }
            } catch (error) {
                console.error(`Error ${currentApprovalAction} balances:`, error);
                showAlert(`حدث خطأ أثناء ${actionText} الأرصدة الافتتاحية`, 'error');
            }
        }

        // معالج الترحيل الذكي
        async function handlePostingAction() {
            const actionText = currentPostingAction === 'post' ? 'ترحيل' : 'إلغاء ترحيل';
            const confirmText = currentPostingAction === 'post' ?
                'هل أنت متأكد من ترحيل جميع الأرصدة الافتتاحية المعتمدة؟' :
                'هل أنت متأكد من إلغاء ترحيل جميع الأرصدة الافتتاحية المرحلة؟';

            if (!confirm(confirmText)) {
                return;
            }

            try {
                const response = await fetch(`/transfers/api/money-changers-opening-balances/bulk-${currentPostingAction}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        fiscal_date: '2025-01-01'
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showAlert(`تم ${actionText} الأرصدة الافتتاحية بنجاح`, 'success');
                    loadData();
                } else {
                    showAlert(result.message || `حدث خطأ أثناء ${actionText} الأرصدة الافتتاحية`, 'error');
                }
            } catch (error) {
                console.error(`Error ${currentPostingAction} balances:`, error);
                showAlert(`حدث خطأ أثناء ${actionText} الأرصدة الافتتاحية`, 'error');
            }
        }

        // دوال إدارة الأرصدة الافتتاحية

        // اعتماد رصيد افتتاحي
        async function approveBalance(balanceId) {
            if (!confirm('هل أنت متأكد من اعتماد هذا الرصيد الافتتاحي؟')) {
                return;
            }

            try {
                const response = await fetch(`/transfers/api/money-changers-opening-balances/${balanceId}/approve`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('تم اعتماد الرصيد الافتتاحي بنجاح', 'success');
                    loadData();
                } else {
                    showAlert(result.message || 'حدث خطأ أثناء اعتماد الرصيد الافتتاحي', 'error');
                }
            } catch (error) {
                console.error('Error approving balance:', error);
                showAlert('حدث خطأ أثناء اعتماد الرصيد الافتتاحي', 'error');
            }
        }

        // إلغاء اعتماد رصيد افتتاحي
        async function unapproveBalance(balanceId) {
            if (!confirm('هل أنت متأكد من إلغاء اعتماد هذا الرصيد الافتتاحي؟')) {
                return;
            }

            try {
                const response = await fetch(`/transfers/api/money-changers-opening-balances/${balanceId}/unapprove`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('تم إلغاء اعتماد الرصيد الافتتاحي بنجاح', 'success');
                    loadData();
                } else {
                    showAlert(result.message || 'حدث خطأ أثناء إلغاء اعتماد الرصيد الافتتاحي', 'error');
                }
            } catch (error) {
                console.error('Error unapproving balance:', error);
                showAlert('حدث خطأ أثناء إلغاء اعتماد الرصيد الافتتاحي', 'error');
            }
        }

        // ترحيل رصيد افتتاحي
        async function postBalance(balanceId) {
            if (!confirm('هل أنت متأكد من ترحيل هذا الرصيد الافتتاحي؟')) {
                return;
            }

            try {
                const response = await fetch(`/transfers/api/money-changers-opening-balances/${balanceId}/post`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('تم ترحيل الرصيد الافتتاحي بنجاح', 'success');
                    loadData();
                } else {
                    showAlert(result.message || 'حدث خطأ أثناء ترحيل الرصيد الافتتاحي', 'error');
                }
            } catch (error) {
                console.error('Error posting balance:', error);
                showAlert('حدث خطأ أثناء ترحيل الرصيد الافتتاحي', 'error');
            }
        }

        // إلغاء ترحيل رصيد افتتاحي
        async function unpostBalance(balanceId) {
            if (!confirm('هل أنت متأكد من إلغاء ترحيل هذا الرصيد الافتتاحي؟')) {
                return;
            }

            try {
                const response = await fetch(`/transfers/api/money-changers-opening-balances/${balanceId}/unpost`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('تم إلغاء ترحيل الرصيد الافتتاحي بنجاح', 'success');
                    loadData();
                } else {
                    showAlert(result.message || 'حدث خطأ أثناء إلغاء ترحيل الرصيد الافتتاحي', 'error');
                }
            } catch (error) {
                console.error('Error unposting balance:', error);
                showAlert('حدث خطأ أثناء إلغاء ترحيل الرصيد الافتتاحي', 'error');
            }
        }

        // حذف رصيد افتتاحي
        async function deleteBalance(balanceId) {
            if (!confirm('هل أنت متأكد من حذف هذا الرصيد الافتتاحي؟\nلا يمكن التراجع عن هذا الإجراء.')) {
                return;
            }

            try {
                const response = await fetch(`/transfers/api/money-changers-opening-balances/${balanceId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('تم حذف الرصيد الافتتاحي بنجاح', 'success');
                    loadData();
                } else {
                    showAlert(result.message || 'حدث خطأ أثناء حذف الرصيد الافتتاحي', 'error');
                }
            } catch (error) {
                console.error('Error deleting balance:', error);
                showAlert('حدث خطأ أثناء حذف الرصيد الافتتاحي', 'error');
            }
        }

        // تعديل رصيد افتتاحي
        async function editBalance(balanceId) {
            try {
                const response = await fetch(`/transfers/api/money-changers-opening-balances/${balanceId}`);
                const result = await response.json();

                if (result.success) {
                    const balance = result.data;

                    // ملء النموذج بالبيانات الحالية
                    document.getElementById('modalTitle').textContent = 'تعديل رصيد افتتاحي';
                    document.getElementById('balanceId').value = balance.id;
                    document.getElementById('moneyChangerId').value = balance.money_changer_id;
                    document.getElementById('moneyChangerSearch').value = balance.money_changer_name;
                    document.getElementById('modalCurrencyCode').value = balance.currency_code;
                    document.getElementById('openingBalanceAmount').value = balance.opening_balance_amount;
                    document.getElementById('exchangeRate').value = balance.exchange_rate;
                    document.getElementById('balanceType').value = balance.balance_type;
                    document.getElementById('modalFiscalDate').value = balance.fiscal_period_start_date;
                    document.getElementById('referenceDocument').value = balance.reference_document || '';
                    document.getElementById('notes').value = balance.notes || '';

                    const modal = new bootstrap.Modal(document.getElementById('addEditModal'));
                    modal.show();
                } else {
                    showAlert(result.message || 'حدث خطأ أثناء تحميل بيانات الرصيد الافتتاحي', 'error');
                }
            } catch (error) {
                console.error('Error loading balance:', error);
                showAlert('حدث خطأ أثناء تحميل بيانات الرصيد الافتتاحي', 'error');
            }
        }

        // عرض رصيد افتتاحي
        async function viewBalance(balanceId) {
            try {
                const response = await fetch(`/transfers/api/money-changers-opening-balances/${balanceId}`);
                const result = await response.json();

                if (result.success) {
                    const balance = result.data;

                    // ملء النموذج بالبيانات للعرض فقط
                    document.getElementById('modalTitle').textContent = 'عرض رصيد افتتاحي';
                    document.getElementById('balanceId').value = balance.id;
                    document.getElementById('moneyChangerId').value = balance.money_changer_id;
                    document.getElementById('moneyChangerSearch').value = balance.money_changer_name;
                    document.getElementById('modalCurrencyCode').value = balance.currency_code;
                    document.getElementById('openingBalanceAmount').value = balance.opening_balance_amount;
                    document.getElementById('exchangeRate').value = balance.exchange_rate;
                    document.getElementById('balanceType').value = balance.balance_type;
                    document.getElementById('modalFiscalDate').value = balance.fiscal_period_start_date;
                    document.getElementById('referenceDocument').value = balance.reference_document || '';
                    document.getElementById('notes').value = balance.notes || '';

                    // تعطيل جميع الحقول للعرض فقط
                    const formElements = document.querySelectorAll('#addEditModal input, #addEditModal select, #addEditModal textarea');
                    formElements.forEach(element => {
                        element.disabled = true;
                    });

                    // إخفاء أزرار الحفظ
                    document.getElementById('saveBtn').style.display = 'none';

                    const modal = new bootstrap.Modal(document.getElementById('addEditModal'));
                    modal.show();

                    // إعادة تفعيل الحقول عند إغلاق النموذج
                    modal._element.addEventListener('hidden.bs.modal', function () {
                        formElements.forEach(element => {
                            element.disabled = false;
                        });
                        document.getElementById('saveBtn').style.display = 'inline-block';
                    }, { once: true });
                } else {
                    showAlert(result.message || 'حدث خطأ أثناء تحميل بيانات الرصيد الافتتاحي', 'error');
                }
            } catch (error) {
                console.error('Error loading balance:', error);
                showAlert('حدث خطأ أثناء تحميل بيانات الرصيد الافتتاحي', 'error');
            }
        }
    </script>
</body>
</html>
