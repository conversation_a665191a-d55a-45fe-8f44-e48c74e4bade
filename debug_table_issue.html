
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة الجدول الفارغ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
</head>
<body>
    <div class="container mt-4">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-bug me-2"></i>
                    تشخيص مشكلة الجدول الفارغ
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6>خطوات التشخيص:</h6>
                    <ol>
                        <li>فحص تحميل jQuery</li>
                        <li>فحص تحميل DataTables</li>
                        <li>فحص API البيانات</li>
                        <li>فحص تهيئة الجدول</li>
                        <li>فحص تحميل البيانات</li>
                    </ol>
                </div>
                
                <div id="diagnostics"></div>
                
                <button class="btn btn-primary" onclick="runDiagnostics()">
                    <i class="fas fa-play me-2"></i>
                    بدء التشخيص
                </button>
                
                <div class="mt-4">
                    <h6>جدول الاختبار:</h6>
                    <table id="testTable" class="table table-striped">
                        <thead>
                            <tr>
                                <th>كود الصنف</th>
                                <th>اسم الصنف</th>
                                <th>المورد</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات بالترتيب الصحيح -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>

    <script>
    let diagnosticResults = [];
    let testTable = null;
    
    function addDiagnostic(step, status, message) {
        const icon = status === 'success' ? 'fa-check text-success' : 
                    status === 'error' ? 'fa-times text-danger' : 
                    'fa-exclamation-triangle text-warning';
        
        diagnosticResults.push(`
            <div class="d-flex align-items-center mb-2">
                <i class="fas ${icon} me-2"></i>
                <strong>${step}:</strong> ${message}
            </div>
        `);
        
        $('#diagnostics').html(diagnosticResults.join(''));
    }
    
    function runDiagnostics() {
        diagnosticResults = [];
        $('#diagnostics').html('<div class="text-center"><div class="spinner-border"></div><br>جاري التشخيص...</div>');
        
        setTimeout(() => {
            // الخطوة 1: فحص jQuery
            if (typeof $ !== 'undefined') {
                addDiagnostic('jQuery', 'success', `متوفر (الإصدار: ${$.fn.jquery})`);
            } else {
                addDiagnostic('jQuery', 'error', 'غير متوفر');
                return;
            }
            
            // الخطوة 2: فحص DataTables
            if (typeof $.fn.DataTable !== 'undefined') {
                addDiagnostic('DataTables', 'success', 'متوفر');
            } else {
                addDiagnostic('DataTables', 'error', 'غير متوفر');
                return;
            }
            
            // الخطوة 3: فحص API
            addDiagnostic('API', 'warning', 'جاري الفحص...');
            
            fetch('/purchase-orders/api/items/data')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success && data.data && data.data.length > 0) {
                        addDiagnostic('API', 'success', `يعمل بنجاح - ${data.data.length} صنف`);
                        
                        // الخطوة 4: تهيئة الجدول
                        try {
                            if (testTable) {
                                testTable.destroy();
                            }
                            
                            testTable = $('#testTable').DataTable({
                                destroy: true,
                                language: {
                                    emptyTable: "لا توجد بيانات",
                                    zeroRecords: "لا توجد نتائج"
                                }
                            });
                            
                            addDiagnostic('تهيئة الجدول', 'success', 'تم بنجاح');
                            
                            // الخطوة 5: تحميل البيانات
                            testTable.clear();
                            
                            data.data.slice(0, 5).forEach(item => {
                                testTable.row.add([
                                    item.item_code || '',
                                    item.item_name || '',
                                    item.supplier_name || 'غير محدد',
                                    item.total_quantity || 0,
                                    `¥${(item.avg_price || 0).toFixed(2)}`
                                ]);
                            });
                            
                            testTable.draw();
                            
                            addDiagnostic('تحميل البيانات', 'success', `تم تحميل ${data.data.length} صنف`);
                            
                        } catch (error) {
                            addDiagnostic('تهيئة الجدول', 'error', error.message);
                        }
                        
                    } else {
                        addDiagnostic('API', 'warning', 'يعمل لكن لا توجد بيانات');
                    }
                })
                .catch(error => {
                    addDiagnostic('API', 'error', `فشل: ${error.message}`);
                });
                
        }, 1000);
    }
    
    // تشخيص تلقائي عند تحميل الصفحة
    $(document).ready(function() {
        console.log('🚀 بدء التشخيص التلقائي...');
        setTimeout(runDiagnostics, 2000);
    });
    </script>
</body>
</html>
        