{% extends "base.html" %}

{% block content %}
<style>
.live-map-container {
    height: calc(100vh - 150px);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.map-controls {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 1000;
    background: white;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    min-width: 250px;
}

.shipment-marker {
    background: #007bff;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    border: 3px solid white;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

.driver-marker {
    background: #28a745;
    color: white;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    border: 3px solid white;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    animation: pulse 2s infinite;
}

.warehouse-marker {
    background: #ffc107;
    color: black;
    border-radius: 10px;
    padding: 5px 10px;
    font-size: 12px;
    font-weight: bold;
    border: 2px solid white;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

.legend {
    background: white;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: absolute;
    bottom: 20px;
    left: 20px;
    z-index: 1000;
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.legend-marker {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-left: 10px;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.stats-overlay {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: white;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    min-width: 200px;
}

.realtime-indicator {
    color: #28a745;
    animation: pulse 2s infinite;
}

.filter-toggle {
    margin-bottom: 10px;
}

.route-line {
    stroke: #007bff;
    stroke-width: 3;
    fill: none;
    stroke-dasharray: 5,5;
    animation: dash 1s linear infinite;
}

@keyframes dash {
    to {
        stroke-dashoffset: -10;
    }
}
</style>

<div class="container-fluid p-0">
    <!-- Header -->
    <div class="row mb-3 mx-2">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-map-marked-alt text-primary me-2"></i>
                        الخريطة المباشرة
                        <span class="realtime-indicator ms-2">
                            <i class="fas fa-circle"></i> مباشر
                        </span>
                    </h1>
                    <p class="text-muted mb-0">تتبع مباشر لجميع الشحنات والسائقين</p>
                </div>
                <div>
                    <button class="btn btn-outline-primary me-2" onclick="refreshMap()">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث
                    </button>
                    <button class="btn btn-primary" onclick="toggleFullscreen()">
                        <i class="fas fa-expand me-2"></i>
                        ملء الشاشة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Map Container -->
    <div class="position-relative">
        <div id="liveMap" class="live-map-container"></div>
        
        <!-- Map Controls -->
        <div class="map-controls">
            <h6 class="mb-3">
                <i class="fas fa-filter me-2"></i>
                عوامل التصفية
            </h6>
            
            <div class="filter-toggle">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="showShipments" checked onchange="toggleShipments()">
                    <label class="form-check-label" for="showShipments">
                        عرض الشحنات
                    </label>
                </div>
            </div>
            
            <div class="filter-toggle">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="showDrivers" checked onchange="toggleDrivers()">
                    <label class="form-check-label" for="showDrivers">
                        عرض السائقين
                    </label>
                </div>
            </div>
            
            <div class="filter-toggle">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="showWarehouses" checked onchange="toggleWarehouses()">
                    <label class="form-check-label" for="showWarehouses">
                        عرض المستودعات
                    </label>
                </div>
            </div>
            
            <div class="filter-toggle">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="showRoutes" onchange="toggleRoutes()">
                    <label class="form-check-label" for="showRoutes">
                        عرض المسارات
                    </label>
                </div>
            </div>
            
            <hr>
            
            <div class="mb-3">
                <label class="form-label">حالة الشحنة</label>
                <select class="form-select form-select-sm" id="statusFilter" onchange="filterByStatus()">
                    <option value="">جميع الحالات</option>
                    <option value="في الطريق">في الطريق</option>
                    <option value="خارج للتسليم">خارج للتسليم</option>
                    <option value="تم الاستلام">تم الاستلام</option>
                </select>
            </div>
            
            <div class="mb-3">
                <label class="form-label">شركة الشحن</label>
                <select class="form-select form-select-sm" id="carrierFilter" onchange="filterByCarrier()">
                    <option value="">جميع الشركات</option>
                    {% for carrier in carriers %}
                    <option value="{{ carrier.id }}">{{ carrier.name }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
        
        <!-- Stats Overlay -->
        <div class="stats-overlay">
            <h6 class="mb-3">
                <i class="fas fa-chart-bar me-2"></i>
                إحصائيات مباشرة
            </h6>
            
            <div class="mb-2">
                <small class="text-muted">الشحنات النشطة</small>
                <div class="h5 mb-0 text-primary" id="activeShipments">{{ stats.active_shipments or 0 }}</div>
            </div>
            
            <div class="mb-2">
                <small class="text-muted">السائقين المتاحين</small>
                <div class="h5 mb-0 text-success" id="availableDrivers">{{ stats.available_drivers or 0 }}</div>
            </div>
            
            <div class="mb-2">
                <small class="text-muted">في الطريق</small>
                <div class="h5 mb-0 text-warning" id="inTransit">{{ stats.in_transit or 0 }}</div>
            </div>
            
            <div class="mb-2">
                <small class="text-muted">خارج للتسليم</small>
                <div class="h5 mb-0 text-info" id="outForDelivery">{{ stats.out_for_delivery or 0 }}</div>
            </div>
            
            <hr>
            
            <div class="text-center">
                <small class="text-muted">آخر تحديث</small>
                <div id="lastUpdate">{{ moment().format('HH:mm:ss') }}</div>
            </div>
        </div>
        
        <!-- Legend -->
        <div class="legend">
            <h6 class="mb-3">
                <i class="fas fa-info-circle me-2"></i>
                دليل الرموز
            </h6>
            
            <div class="legend-item">
                <div class="legend-marker" style="background: #007bff;"></div>
                <small>شحنات في الطريق</small>
            </div>
            
            <div class="legend-item">
                <div class="legend-marker" style="background: #28a745;"></div>
                <small>سائقين متاحين</small>
            </div>
            
            <div class="legend-item">
                <div class="legend-marker" style="background: #ffc107; border-radius: 3px;"></div>
                <small>مستودعات</small>
            </div>
            
            <div class="legend-item">
                <div class="legend-marker" style="background: #dc3545;"></div>
                <small>تأخير في التسليم</small>
            </div>
        </div>
    </div>
</div>

<!-- Shipment Details Modal -->
<div class="modal fade" id="shipmentDetailsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الشحنة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="shipmentDetailsContent">
                <!-- سيتم ملؤها ديناميكياً -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="trackShipment()">تتبع مفصل</button>
            </div>
        </div>
    </div>
</div>

<script>
let map;
let shipmentMarkers = [];
let driverMarkers = [];
let warehouseMarkers = [];
let routeLines = [];

// بيانات تجريبية
const sampleData = {
    shipments: [
        {
            id: 1,
            tracking_number: 'SAS20250806001234',
            lat: 24.7136,
            lng: 46.6753,
            status: 'في الطريق',
            recipient: 'فاطمة سالم أحمد',
            destination: 'جدة'
        },
        {
            id: 2,
            tracking_number: 'SAS20250806001235',
            lat: 21.4858,
            lng: 39.1925,
            status: 'خارج للتسليم',
            recipient: 'خالد محمد القحطاني',
            destination: 'الرياض'
        }
    ],
    drivers: [
        {
            id: 1,
            name: 'أحمد محمد',
            lat: 24.7200,
            lng: 46.6800,
            status: 'متاح',
            phone: '0501234567'
        },
        {
            id: 2,
            name: 'محمد سالم',
            lat: 21.4900,
            lng: 39.2000,
            status: 'في مهمة',
            phone: '0509876543'
        }
    ],
    warehouses: [
        {
            id: 1,
            name: 'مستودع الرياض الرئيسي',
            lat: 24.7136,
            lng: 46.6753
        },
        {
            id: 2,
            name: 'مستودع جدة',
            lat: 21.4858,
            lng: 39.1925
        }
    ]
};

// تهيئة الخريطة
function initMap() {
    map = new google.maps.Map(document.getElementById('liveMap'), {
        zoom: 6,
        center: { lat: 24.7136, lng: 46.6753 }, // الرياض
        styles: [
            {
                featureType: 'all',
                elementType: 'geometry.fill',
                stylers: [{ color: '#f5f5f5' }]
            },
            {
                featureType: 'water',
                elementType: 'geometry',
                stylers: [{ color: '#e3f2fd' }]
            }
        ]
    });
    
    // إضافة العلامات
    addShipmentMarkers();
    addDriverMarkers();
    addWarehouseMarkers();
    
    // تحديث تلقائي كل 30 ثانية
    setInterval(updateLiveData, 30000);
}

// إضافة علامات الشحنات
function addShipmentMarkers() {
    sampleData.shipments.forEach(shipment => {
        const marker = new google.maps.Marker({
            position: { lat: shipment.lat, lng: shipment.lng },
            map: map,
            title: shipment.tracking_number,
            icon: {
                path: google.maps.SymbolPath.CIRCLE,
                scale: 8,
                fillColor: shipment.status === 'في الطريق' ? '#007bff' : '#17a2b8',
                fillOpacity: 1,
                strokeColor: '#ffffff',
                strokeWeight: 2
            }
        });
        
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div style="padding: 10px;">
                    <h6>${shipment.tracking_number}</h6>
                    <p><strong>المستقبل:</strong> ${shipment.recipient}</p>
                    <p><strong>الوجهة:</strong> ${shipment.destination}</p>
                    <p><strong>الحالة:</strong> ${shipment.status}</p>
                    <button class="btn btn-sm btn-primary" onclick="showShipmentDetails(${shipment.id})">
                        تفاصيل أكثر
                    </button>
                </div>
            `
        });
        
        marker.addListener('click', () => {
            infoWindow.open(map, marker);
        });
        
        shipmentMarkers.push(marker);
    });
}

// إضافة علامات السائقين
function addDriverMarkers() {
    sampleData.drivers.forEach(driver => {
        const marker = new google.maps.Marker({
            position: { lat: driver.lat, lng: driver.lng },
            map: map,
            title: driver.name,
            icon: {
                path: google.maps.SymbolPath.CIRCLE,
                scale: 10,
                fillColor: driver.status === 'متاح' ? '#28a745' : '#ffc107',
                fillOpacity: 1,
                strokeColor: '#ffffff',
                strokeWeight: 3
            }
        });
        
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div style="padding: 10px;">
                    <h6>${driver.name}</h6>
                    <p><strong>الحالة:</strong> ${driver.status}</p>
                    <p><strong>الهاتف:</strong> ${driver.phone}</p>
                    <button class="btn btn-sm btn-success" onclick="callDriver('${driver.phone}')">
                        اتصال
                    </button>
                </div>
            `
        });
        
        marker.addListener('click', () => {
            infoWindow.open(map, marker);
        });
        
        driverMarkers.push(marker);
    });
}

// إضافة علامات المستودعات
function addWarehouseMarkers() {
    sampleData.warehouses.forEach(warehouse => {
        const marker = new google.maps.Marker({
            position: { lat: warehouse.lat, lng: warehouse.lng },
            map: map,
            title: warehouse.name,
            icon: {
                path: google.maps.SymbolPath.BACKWARD_CLOSED_ARROW,
                scale: 6,
                fillColor: '#ffc107',
                fillOpacity: 1,
                strokeColor: '#ffffff',
                strokeWeight: 2
            }
        });
        
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div style="padding: 10px;">
                    <h6>${warehouse.name}</h6>
                    <p>مستودع رئيسي</p>
                </div>
            `
        });
        
        marker.addListener('click', () => {
            infoWindow.open(map, marker);
        });
        
        warehouseMarkers.push(marker);
    });
}

// وظائف التحكم
function toggleShipments() {
    const show = document.getElementById('showShipments').checked;
    shipmentMarkers.forEach(marker => {
        marker.setVisible(show);
    });
}

function toggleDrivers() {
    const show = document.getElementById('showDrivers').checked;
    driverMarkers.forEach(marker => {
        marker.setVisible(show);
    });
}

function toggleWarehouses() {
    const show = document.getElementById('showWarehouses').checked;
    warehouseMarkers.forEach(marker => {
        marker.setVisible(show);
    });
}

function toggleRoutes() {
    const show = document.getElementById('showRoutes').checked;
    // تنفيذ عرض/إخفاء المسارات
    alert('عرض المسارات - قريباً');
}

function filterByStatus() {
    const status = document.getElementById('statusFilter').value;
    // تنفيذ التصفية حسب الحالة
    alert(`تصفية حسب الحالة: ${status}`);
}

function filterByCarrier() {
    const carrier = document.getElementById('carrierFilter').value;
    // تنفيذ التصفية حسب شركة الشحن
    alert(`تصفية حسب شركة الشحن: ${carrier}`);
}

function refreshMap() {
    updateLiveData();
    alert('تم تحديث الخريطة');
}

function toggleFullscreen() {
    const mapContainer = document.getElementById('liveMap');
    if (mapContainer.requestFullscreen) {
        mapContainer.requestFullscreen();
    }
}

function updateLiveData() {
    // تحديث البيانات المباشرة
    document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString('ar-SA');
    
    // محاكاة تحديث الإحصائيات
    fetch('{{ url_for("shipments.live_map_data") }}')
        .then(response => response.json())
        .then(data => {
            // تحديث الإحصائيات
            if (data.stats) {
                document.getElementById('activeShipments').textContent = data.stats.active_shipments || 0;
                document.getElementById('availableDrivers').textContent = data.stats.available_drivers || 0;
                document.getElementById('inTransit').textContent = data.stats.in_transit || 0;
                document.getElementById('outForDelivery').textContent = data.stats.out_for_delivery || 0;
            }
        })
        .catch(error => console.error('Error updating live data:', error));
}

function showShipmentDetails(shipmentId) {
    // عرض تفاصيل الشحنة
    document.getElementById('shipmentDetailsContent').innerHTML = `
        <p>تفاصيل الشحنة ${shipmentId}</p>
        <p>سيتم تطوير هذه الميزة قريباً</p>
    `;
    new bootstrap.Modal(document.getElementById('shipmentDetailsModal')).show();
}

function callDriver(phone) {
    window.open(`tel:${phone}`);
}

function trackShipment() {
    alert('تتبع مفصل - قريباً');
}

// تهيئة الخريطة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // محاكاة تحميل Google Maps
    setTimeout(initMap, 1000);
});
</script>

{% endblock %}
