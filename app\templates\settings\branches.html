{% extends "base.html" %}

{% block title %}إدارة الفروع{% endblock %}

{% block extra_css %}
<style>
/* 🔧 إصلاح شامل للنافذة المظللة */
#branchModal {
    pointer-events: auto !important;
    opacity: 1 !important;
    z-index: 9999 !important;
}

#branchModal .modal-dialog {
    pointer-events: auto !important;
    opacity: 1 !important;
    z-index: 10000 !important;
}

#branchModal .modal-content {
    pointer-events: auto !important;
    opacity: 1 !important;
    background-color: white !important;
    z-index: 10001 !important;
}

#branchModal .modal-header,
#branchModal .modal-body,
#branchModal .modal-footer {
    pointer-events: auto !important;
    opacity: 1 !important;
    z-index: 10002 !important;
}

/* إجبار تفعيل جميع حقول النوافذ */
#branchModal input:not(#brn_no),
#branchModal textarea,
#branchModal select {
    pointer-events: auto !important;
    opacity: 1 !important;
    background-color: white !important;
    cursor: text !important;
    border: 1px solid #ced4da !important;
    color: #495057 !important;
    z-index: 10003 !important;
}

#branchModal button {
    pointer-events: auto !important;
    opacity: 1 !important;
    cursor: pointer !important;
    z-index: 10003 !important;
}

/* إزالة أي overlay أو mask */
.modal-backdrop {
    pointer-events: none !important;
}

/* إزالة أي تأثيرات تعطيل */
#branchModal * {
    -webkit-user-select: auto !important;
    -moz-user-select: auto !important;
    -ms-user-select: auto !important;
    user-select: auto !important;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>
                        إدارة الفروع
                    </h4>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#branchModal" onclick="openBranchModal()">
                        <i class="fas fa-plus me-1"></i>
                        إضافة فرع جديد
                    </button>
                </div>
                
                <div class="card-body">
                    <!-- جدول الفروع -->
                    <div class="table-responsive">
                        <table class="table table-hover" id="branchesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الفرع</th>
                                    <th>الشعار</th>
                                    <th>اسم الفرع</th>
                                    <th>اسم الفرع (إنجليزي)</th>
                                    <th>العنوان</th>
                                    <th>الهاتف</th>
                                    <th>كود الفرع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="branchesTableBody">
                                <tr>
                                    <td colspan="9" class="text-center">جاري التحميل...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة/تعديل الفرع -->
<div class="modal fade" id="branchModal" tabindex="-1" aria-labelledby="branchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="branchModalLabel">إضافة فرع جديد</h5>
                <button type="button" class="btn-close" onclick="closeModal()" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="branchForm">
                    <input type="hidden" id="branchId" name="branchId">
                    
                    <!-- المعلومات الأساسية -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">المعلومات الأساسية</h6>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <!-- حقل رقم الفرع يقابله العمود BRN_NO في جدول الفرع -->
                            <label for="brn_no" class="form-label">رقم الفرع</label>
                            <input type="number" class="form-control" id="brn_no" name="brn_no" readonly>
                            <small class="text-muted">يقابل العمود BRN_NO</small>
                        </div>
                        <div class="col-md-6">
                            <!-- حقل اسم الفرع يقابله العمود BRN_LNAME في جدول الفرع -->
                            <label for="brn_lname" class="form-label">اسم الفرع <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="brn_lname" name="brn_lname" required>
                            <small class="text-muted">يقابل العمود BRN_LNAME</small>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <!-- حقل اسم الفرع بالانجليزي يقابله العمود BRN_FNAME في جدول الفرع -->
                            <label for="brn_fname" class="form-label">اسم الفرع بالإنجليزي</label>
                            <input type="text" class="form-control" id="brn_fname" name="brn_fname">
                            <small class="text-muted">يقابل العمود BRN_FNAME</small>
                        </div>
                        <div class="col-md-6">
                            <!-- حقل عنوان الفرع يقابله العمود BRN_LADD في جدول الفرع -->
                            <label for="brn_ladd" class="form-label">عنوان الفرع</label>
                            <textarea class="form-control" id="brn_ladd" name="brn_ladd" rows="2"></textarea>
                            <small class="text-muted">يقابل العمود BRN_LADD</small>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <!-- حقل عنوان الفرع بالانجليزي يقابله العمود BRN_FADD في جدول الفرع -->
                            <label for="brn_fadd" class="form-label">عنوان الفرع بالإنجليزي</label>
                            <textarea class="form-control" id="brn_fadd" name="brn_fadd" rows="2"></textarea>
                            <small class="text-muted">يقابل العمود BRN_FADD</small>
                        </div>
                        <div class="col-md-6">
                            <!-- حقل رقم الهاتف يقابله العمود BRN_LTELE في جدول الفرع -->
                            <label for="brn_ltele" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control" id="brn_ltele" name="brn_ltele">
                            <small class="text-muted">يقابل العمود BRN_LTELE</small>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <!-- حقل رقم الهاتف بالانجليزي يقابله العمود BRN_FTELE في جدول الفرع -->
                            <label for="brn_ftele" class="form-label">رقم الهاتف بالإنجليزي</label>
                            <input type="text" class="form-control" id="brn_ftele" name="brn_ftele">
                            <small class="text-muted">يقابل العمود BRN_FTELE</small>
                        </div>
                        <div class="col-md-6">
                            <!-- حقل رقم الفاكس يقابله العمود BRN_LFAX في جدول الفرع -->
                            <label for="brn_lfax" class="form-label">رقم الفاكس</label>
                            <input type="text" class="form-control" id="brn_lfax" name="brn_lfax">
                            <small class="text-muted">يقابل العمود BRN_LFAX</small>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <!-- حقل رقم الفاكس بالانجليزي يقابله العمود BRN_FFAX في جدول الفرع -->
                            <label for="brn_ffax" class="form-label">رقم الفاكس بالإنجليزي</label>
                            <input type="text" class="form-control" id="brn_ffax" name="brn_ffax">
                            <small class="text-muted">يقابل العمود BRN_FFAX</small>
                        </div>
                        <div class="col-md-6">
                            <!-- حقل رقم صندوق البريد يقابله العمود BRN_LBOX في جدول الفرع -->
                            <label for="brn_lbox" class="form-label">رقم صندوق البريد</label>
                            <input type="text" class="form-control" id="brn_lbox" name="brn_lbox">
                            <small class="text-muted">يقابل العمود BRN_LBOX</small>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <!-- حقل رقم صندوق البريد بالانجليزي يقابله العمود BRN_FBOX في جدول الفرع -->
                            <label for="brn_fbox" class="form-label">رقم صندوق البريد بالإنجليزي</label>
                            <input type="text" class="form-control" id="brn_fbox" name="brn_fbox">
                            <small class="text-muted">يقابل العمود BRN_FBOX</small>
                        </div>
                        <div class="col-md-6">
                            <!-- حقل شعار الفرع يقابله العمود BRN_IMG في جدول الفرع -->
                            <label for="brn_img" class="form-label">شعار الفرع</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="brn_img" name="brn_img" placeholder="مسار الصورة" readonly>
                                <input type="file" class="d-none" id="brn_img_file" accept="image/*">
                                <button class="btn btn-outline-secondary" type="button" onclick="selectLogo()">
                                    <i class="fas fa-folder-open"></i> استعراض
                                </button>
                            </div>
                            <small class="text-muted">يقابل العمود BRN_IMG - اختر صورة الشعار</small>
                            <!-- معاينة الصورة -->
                            <div class="mt-2" id="logo_preview" style="display: none;">
                                <img id="logo_img" src="" alt="معاينة الشعار" class="img-thumbnail" style="max-width: 150px; max-height: 100px;">
                                <button type="button" class="btn btn-sm btn-danger ms-2" onclick="removeLogo()">
                                    <i class="fas fa-trash"></i> إزالة
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- إعدادات قاعدة البيانات -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">إعدادات قاعدة البيانات</h6>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <!-- حقل اسم المستخدم في قاعدة البيانات يقابله العمود BRN_CODE في جدول الفرع -->
                            <label for="brn_code" class="form-label">اسم المستخدم في قاعدة البيانات</label>
                            <input type="text" class="form-control" id="brn_code" name="brn_code">
                            <small class="text-muted">يقابل العمود BRN_CODE</small>
                        </div>
                        <div class="col-md-6">
                            <!-- حقل اسم DBLINKS المستخدمة في الاتصال يقابله العمود DBLINK_NAME في جدول الفرع -->
                            <label for="dblink_name" class="form-label">اسم DBLINKS المستخدمة في الاتصال</label>
                            <input type="text" class="form-control" id="dblink_name" name="dblink_name">
                            <small class="text-muted">يقابل العمود DBLINK_NAME</small>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-warning btn-sm" onclick="forceEnableAllFields()">
                    🔧 تفعيل الحقول
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveBranch()">
                    <i class="fas fa-save me-1"></i>
                    حفظ
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// متغيرات عامة
let currentBranchId = null;

// تحميل الفروع عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadBranches();

    // إضافة مراقب لإزالة التظليل عند فتح النافذة
    const branchModal = document.getElementById('branchModal');
    if (branchModal) {
        // عند فتح النافذة
        branchModal.addEventListener('shown.bs.modal', function() {
            console.log('🔧 النافذة مفتوحة - إزالة التظليل...');
            setTimeout(() => {
                forceEnableAllFields();
            }, 100);
            setTimeout(() => {
                forceEnableAllFields();
            }, 500);
        });

        // عند بداية فتح النافذة
        branchModal.addEventListener('show.bs.modal', function() {
            console.log('🔧 بداية فتح النافذة - تحضير التفعيل...');
            forceEnableAllFields();
        });
    }

    // مراقب مستمر لإزالة أي تظليل جديد
    setInterval(() => {
        const modal = document.getElementById('branchModal');
        if (modal && modal.classList.contains('show')) {
            // إزالة أي overlay جديد
            document.querySelectorAll('.overlay, .mask, .modal-overlay').forEach(overlay => {
                if (!overlay.classList.contains('modal-backdrop')) {
                    overlay.remove();
                }
            });

            // التأكد من تفعيل النافذة
            if (modal.style.pointerEvents === 'none' || modal.style.opacity !== '1') {
                forceEnableAllFields();
            }
        }
    }, 1000);
});

// دالة قوية لتفعيل جميع الحقول
function forceEnableAllFields() {
    console.log('🔧 تفعيل جميع الحقول بالقوة...');

    // تفعيل جميع الحقول في النافذة
    const modal = document.getElementById('branchModal');
    if (modal) {
        const fields = modal.querySelectorAll('input:not(#brn_no), textarea, select');
        fields.forEach(field => {
            // إزالة جميع أنواع التعطيل
            field.removeAttribute('disabled');
            field.removeAttribute('readonly');
            field.disabled = false;
            field.readOnly = false;

            // إزالة CSS المعطل
            field.style.removeProperty('pointer-events');
            field.style.removeProperty('opacity');
            field.style.removeProperty('background-color');
            field.style.removeProperty('cursor');

            // إزالة classes المعطلة
            field.classList.remove('disabled', 'readonly', 'form-control-plaintext');
            field.classList.add('form-control');

            console.log(`✅ تم تفعيل الحقل: ${field.id}`);
        });

        // تفعيل الأزرار
        const buttons = modal.querySelectorAll('button');
        buttons.forEach(btn => {
            btn.disabled = false;
            btn.style.removeProperty('pointer-events');
            btn.style.removeProperty('opacity');
        });
    }

    console.log('✅ تم تفعيل جميع الحقول بنجاح');
}

// تحميل قائمة الفروع
async function loadBranches() {
    try {
        const response = await fetch('/settings/api/branches');
        const data = await response.json();
        
        if (data.success) {
            displayBranches(data.branches);
        } else {
            showAlert('خطأ في تحميل الفروع: ' + data.message, 'danger');
        }
    } catch (error) {
        console.error('خطأ في تحميل الفروع:', error);
        showAlert('خطأ في الاتصال بالخادم', 'danger');
    }
}

// عرض الفروع في الجدول
function displayBranches(branches) {
    const tbody = document.getElementById('branchesTableBody');
    
    if (branches.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">لا توجد فروع</td></tr>';
        return;
    }
    
    tbody.innerHTML = branches.map(branch => `
        <tr>
            <td>${branch.brn_no || ''}</td>
            <td>
                ${branch.brn_img ?
                    `<img src="${branch.brn_img}" alt="شعار الفرع" class="img-thumbnail" style="max-width: 40px; max-height: 30px;">` :
                    '<span class="text-muted"><i class="fas fa-image"></i></span>'
                }
            </td>
            <td>${branch.brn_lname || ''}</td>
            <td>${branch.brn_fname || ''}</td>
            <td>${branch.brn_ladd || ''}</td>
            <td>${branch.brn_ltele || ''}</td>
            <td>${branch.brn_code || ''}</td>
            <td>
                ${branch.is_active ?
                    '<span class="badge bg-success">نشط</span>' :
                    '<span class="badge bg-danger">غير نشط</span>'
                }
            </td>
            <td>
                <button class="btn btn-sm btn-outline-primary me-1" onclick="editBranch(${branch.brn_no})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteBranch(${branch.brn_no})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// فتح نافذة إضافة فرع جديد
function openBranchModal() {
    currentBranchId = null;
    document.getElementById('branchModalLabel').textContent = 'إضافة فرع جديد';
    document.getElementById('branchForm').reset();
    document.getElementById('branchId').value = '';
    document.getElementById('brn_no').value = '';

    // إعادة تعيين معاينة الشعار
    document.getElementById('brn_img_file').value = '';
    document.getElementById('logo_preview').style.display = 'none';

    // تفعيل جميع الحقول
    enableFormFields();
}

// دالة تفعيل جميع حقول النموذج - حل مشكلة النافذة المظللة
function enableFormFields() {
    console.log('🔧 بدء إزالة التظليل وتفعيل النافذة...');

    // إزالة أي overlay أو mask يغطي النافذة
    document.querySelectorAll('.overlay, .mask, .modal-overlay').forEach(overlay => {
        overlay.remove();
    });

    // إزالة أي CSS يسبب التظليل
    document.querySelectorAll('*').forEach(element => {
        if (element.style.pointerEvents === 'none' && !element.classList.contains('modal-backdrop')) {
            element.style.pointerEvents = 'auto';
        }
    });

    const modal = document.getElementById('branchModal');
    if (modal) {
        // إزالة التظليل من النافذة
        modal.style.pointerEvents = 'auto';
        modal.style.opacity = '1';
        modal.style.zIndex = '9999';

        // إزالة التظليل من محتوى النافذة
        const modalDialog = modal.querySelector('.modal-dialog');
        const modalContent = modal.querySelector('.modal-content');

        if (modalDialog) {
            modalDialog.style.pointerEvents = 'auto';
            modalDialog.style.opacity = '1';
            modalDialog.style.zIndex = '10000';
        }

        if (modalContent) {
            modalContent.style.pointerEvents = 'auto';
            modalContent.style.opacity = '1';
            modalContent.style.backgroundColor = 'white';
            modalContent.style.zIndex = '10001';
        }

        // تفعيل جميع الحقول
        const allInputs = modal.querySelectorAll('input, textarea, select, button');
        allInputs.forEach(element => {
            // إزالة التعطيل
            element.disabled = false;
            element.readOnly = false;
            element.removeAttribute('disabled');
            if (element.id !== 'brn_no') {
                element.removeAttribute('readonly');
            }

            // إزالة التظليل
            element.style.pointerEvents = 'auto';
            element.style.opacity = '1';
            element.style.backgroundColor = 'white';
            element.style.cursor = element.tagName === 'BUTTON' ? 'pointer' : 'text';
            element.style.zIndex = '10003';

            // إزالة classes المعطلة
            element.classList.remove('disabled', 'readonly');
        });

        // تفعيل النموذج
        const form = document.getElementById('branchForm');
        if (form) {
            form.style.pointerEvents = 'auto';
            form.style.opacity = '1';
        }
    }

    console.log('✅ تم إزالة التظليل وتفعيل النافذة بنجاح');
}

// دالة إضافية للتأكد من التفعيل
function forceEnableAllFields() {
    enableFormFields();
}

// دالة إغلاق النافذة
function closeModal() {
    const modalElement = document.getElementById('branchModal');

    if (typeof bootstrap !== 'undefined') {
        const modal = bootstrap.Modal.getInstance(modalElement);
        if (modal) {
            modal.hide();
        }
    } else {
        // حل بديل بدون Bootstrap
        modalElement.style.display = 'none';
        modalElement.classList.remove('show');
        modalElement.style.paddingRight = '';
        document.body.classList.remove('modal-open');

        // إزالة backdrop
        const backdrop = document.getElementById('modal-backdrop-custom');
        if (backdrop) {
            backdrop.remove();
        }
    }
}

// دالة إضافية لإزالة أي تعطيل خفي
function forceEnableModal() {
    setTimeout(() => {
        console.log('🔨 فرض تفعيل النافذة...');

        // إزالة أي overlay أو mask
        const overlays = document.querySelectorAll('.modal-backdrop, .overlay, .mask');
        overlays.forEach(overlay => {
            if (overlay.style.pointerEvents === 'none') return; // لا نحذف backdrop العادي
            overlay.remove();
        });

        // تفعيل النافذة بالقوة
        enableFormFields();

        // إزالة أي event listeners تمنع التفاعل
        const modal = document.getElementById('branchModal');
        if (modal) {
            modal.onclick = null;
            modal.onmousedown = null;
            modal.onkeydown = null;
        }

        console.log('✅ تم فرض تفعيل النافذة');
    }, 200);
}

// تعديل فرع
async function editBranch(branchId) {
    try {
        const response = await fetch(`/settings/api/branches/${branchId}`);
        const data = await response.json();

        if (data.success) {
            const branch = data.branch;
            currentBranchId = branchId;

            // تعبئة النموذج بالبيانات
            document.getElementById('branchModalLabel').textContent = 'تعديل الفرع';
            document.getElementById('branchId').value = branch.brn_no;
            document.getElementById('brn_no').value = branch.brn_no;
            document.getElementById('brn_lname').value = branch.brn_lname || '';
            document.getElementById('brn_fname').value = branch.brn_fname || '';
            document.getElementById('brn_ladd').value = branch.brn_ladd || '';
            document.getElementById('brn_fadd').value = branch.brn_fadd || '';
            document.getElementById('brn_ltele').value = branch.brn_ltele || '';
            document.getElementById('brn_ftele').value = branch.brn_ftele || '';
            document.getElementById('brn_lfax').value = branch.brn_lfax || '';
            document.getElementById('brn_ffax').value = branch.brn_ffax || '';
            document.getElementById('brn_lbox').value = branch.brn_lbox || '';
            document.getElementById('brn_fbox').value = branch.brn_fbox || '';
            document.getElementById('brn_img').value = branch.brn_img || '';
            document.getElementById('brn_code').value = branch.brn_code || '';
            document.getElementById('dblink_name').value = branch.dblink_name || '';

            // عرض الشعار الموجود إن وجد
            if (branch.brn_img && branch.brn_img.trim() !== '') {
                document.getElementById('logo_img').src = branch.brn_img;
                document.getElementById('logo_preview').style.display = 'block';
            } else {
                document.getElementById('logo_preview').style.display = 'none';
            }

            // تفعيل جميع الحقول للتعديل (ما عدا رقم الفرع)
            enableFormFields();

            // فتح النافذة - حل مباشر
            const modalElement = document.getElementById('branchModal');

            // إذا كان Bootstrap متاح
            if (typeof bootstrap !== 'undefined') {
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
            } else {
                // حل بديل بدون Bootstrap
                modalElement.style.display = 'block';
                modalElement.classList.add('show');
                modalElement.style.paddingRight = '17px';
                document.body.classList.add('modal-open');

                // إضافة backdrop
                const backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                backdrop.id = 'modal-backdrop-custom';
                document.body.appendChild(backdrop);
            }

            // تفعيل الحقول فوراً وبعد فتح النافذة
            forceEnableAllFields();
            setTimeout(() => {
                forceEnableAllFields();
            }, 100);
            setTimeout(() => {
                forceEnableAllFields();
            }, 500);
        } else {
            showAlert('خطأ في تحميل بيانات الفرع: ' + data.message, 'danger');
        }
    } catch (error) {
        console.error('خطأ في تحميل بيانات الفرع:', error);
        showAlert('خطأ في الاتصال بالخادم', 'danger');
    }
}

// حفظ الفرع
async function saveBranch() {
    const form = document.getElementById('branchForm');
    const formData = new FormData(form);

    // التحقق من صحة البيانات
    if (!formData.get('brn_lname')) {
        showAlert('يرجى إدخال اسم الفرع', 'warning');
        return;
    }

    // تحويل البيانات إلى JSON مع الربط الصحيح للأعمدة
    const branchData = {
        brn_lname: formData.get('brn_lname'),        // يقابل العمود BRN_LNAME
        brn_fname: formData.get('brn_fname'),        // يقابل العمود BRN_FNAME
        brn_ladd: formData.get('brn_ladd'),          // يقابل العمود BRN_LADD
        brn_fadd: formData.get('brn_fadd'),          // يقابل العمود BRN_FADD
        brn_ltele: formData.get('brn_ltele'),        // يقابل العمود BRN_LTELE
        brn_ftele: formData.get('brn_ftele'),        // يقابل العمود BRN_FTELE
        brn_lfax: formData.get('brn_lfax'),          // يقابل العمود BRN_LFAX
        brn_ffax: formData.get('brn_ffax'),          // يقابل العمود BRN_FFAX
        brn_lbox: formData.get('brn_lbox'),          // يقابل العمود BRN_LBOX
        brn_fbox: formData.get('brn_fbox'),          // يقابل العمود BRN_FBOX
        brn_img: formData.get('brn_img'),            // يقابل العمود BRN_IMG
        brn_code: formData.get('brn_code'),          // يقابل العمود BRN_CODE
        dblink_name: formData.get('dblink_name')     // يقابل العمود DBLINK_NAME
    };

    try {
        const url = currentBranchId ?
            `/settings/api/branches/${currentBranchId}` :
            '/settings/api/branches';
        const method = currentBranchId ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(branchData)
        });

        const data = await response.json();

        if (data.success) {
            showAlert(currentBranchId ? 'تم تحديث الفرع بنجاح' : 'تم إضافة الفرع بنجاح', 'success');

            // إغلاق النافذة وإعادة تحميل البيانات
            const modal = bootstrap.Modal.getInstance(document.getElementById('branchModal'));
            modal.hide();
            loadBranches();
        } else {
            showAlert('خطأ في حفظ الفرع: ' + data.message, 'danger');
        }
    } catch (error) {
        console.error('خطأ في حفظ الفرع:', error);
        showAlert('خطأ في الاتصال بالخادم', 'danger');
    }
}

// حذف فرع
async function deleteBranch(branchId) {
    if (!confirm('هل أنت متأكد من حذف هذا الفرع؟')) {
        return;
    }

    try {
        const response = await fetch(`/settings/api/branches/${branchId}`, {
            method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
            showAlert('تم حذف الفرع بنجاح', 'success');
            loadBranches();
        } else {
            showAlert('خطأ في حذف الفرع: ' + data.message, 'danger');
        }
    } catch (error) {
        console.error('خطأ في حذف الفرع:', error);
        showAlert('خطأ في الاتصال بالخادم', 'danger');
    }
}

// دوال معالجة الشعار
function selectLogo() {
    document.getElementById('brn_img_file').click();
}

function removeLogo() {
    document.getElementById('brn_img').value = '';
    document.getElementById('brn_img_file').value = '';
    document.getElementById('logo_preview').style.display = 'none';
}

function previewLogo(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('logo_img').src = e.target.result;
            document.getElementById('logo_preview').style.display = 'block';
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// معالج تغيير ملف الشعار
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('brn_img_file');
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                // معاينة الصورة
                previewLogo(this);

                // رفع الصورة إلى الخادم
                uploadLogo(this.files[0]);
            }
        });
    }
});

// رفع الشعار إلى الخادم
async function uploadLogo(file) {
    const formData = new FormData();
    formData.append('logo', file);

    try {
        const response = await fetch('/settings/api/upload-logo', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            // تحديث مسار الصورة في الحقل
            document.getElementById('brn_img').value = result.file_path;
            showAlert('تم رفع الشعار بنجاح', 'success');
        } else {
            showAlert('خطأ في رفع الشعار: ' + result.message, 'danger');
        }
    } catch (error) {
        console.error('خطأ في رفع الشعار:', error);
        showAlert('خطأ في رفع الشعار', 'danger');
    }
}

// عرض رسالة تنبيه
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// استبدال دالة editBranch لاستخدام النافذة الجديدة
function editBranch(branchId) {
    openCustomModal(branchId);
}
</script>

<!-- نافذة تعديل جديدة بدون Bootstrap -->
<div id="customBranchModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 8px; width: 90%; max-width: 800px; max-height: 90%; overflow-y: auto; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
        <div style="padding: 20px; border-bottom: 1px solid #dee2e6; display: flex; justify-content: space-between; align-items: center;">
            <h5 id="customModalTitle" style="margin: 0; color: #333;">تعديل الفرع</h5>
            <button onclick="closeCustomModal()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #666;">&times;</button>
        </div>

        <div style="padding: 20px;">
            <form id="customBranchForm">
                <input type="hidden" id="customBranchId">

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم الفرع</label>
                        <input type="text" id="custom_brn_no" readonly style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: #f8f9fa;">
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم الفرع (عربي)</label>
                        <input type="text" id="custom_brn_lname" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم الفرع (إنجليزي)</label>
                        <input type="text" id="custom_brn_fname" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">العنوان (عربي)</label>
                        <input type="text" id="custom_brn_ladd" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">العنوان (إنجليزي)</label>
                        <input type="text" id="custom_brn_fadd" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">الهاتف (عربي)</label>
                        <input type="text" id="custom_brn_ltele" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">الهاتف (إنجليزي)</label>
                        <input type="text" id="custom_brn_ftele" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">الفاكس (عربي)</label>
                        <input type="text" id="custom_brn_lfax" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">الفاكس (إنجليزي)</label>
                        <input type="text" id="custom_brn_ffax" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">صندوق البريد (عربي)</label>
                        <input type="text" id="custom_brn_lbox" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">صندوق البريد (إنجليزي)</label>
                        <input type="text" id="custom_brn_fbox" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">كود قاعدة البيانات</label>
                        <input type="text" id="custom_brn_code" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم DBLINK</label>
                        <input type="text" id="custom_dblink_name" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                </div>

                <!-- حقل الشعار -->
                <div style="margin-top: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 4px; background: #f8f9fa;">
                    <h6 style="margin-bottom: 15px; color: #333;">شعار الفرع</h6>

                    <div style="display: grid; grid-template-columns: 1fr auto; gap: 10px; align-items: end;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">مسار الشعار</label>
                            <input type="text" id="custom_brn_img" placeholder="مسار الصورة" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>

                        <div>
                            <input type="file" id="custom_brn_img_file" accept="image/*" style="display: none;">
                            <button type="button" onclick="document.getElementById('custom_brn_img_file').click()" style="background: #28a745; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">
                                📁 استعراض
                            </button>
                        </div>
                    </div>

                    <!-- معاينة الشعار -->
                    <div id="custom_logo_preview" style="margin-top: 15px; text-align: center; display: none;">
                        <img id="custom_logo_img" style="max-width: 200px; max-height: 100px; border: 1px solid #ddd; border-radius: 4px;">
                        <div style="margin-top: 5px;">
                            <button type="button" onclick="removeCustomLogo()" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                                🗑️ إزالة
                            </button>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 20px; text-align: center;">
                    <button type="button" onclick="saveCustomBranch()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-right: 10px;">حفظ</button>
                    <button type="button" onclick="closeCustomModal()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// دوال النافذة الجديدة
function openCustomModal(branchId) {
    console.log('🔧 فتح النافذة الجديدة للفرع:', branchId);

    // جلب بيانات الفرع
    fetch(`/settings/api/branches/${branchId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const branch = data.branch;

                // تعبئة الحقول
                document.getElementById('customBranchId').value = branch.brn_no;
                document.getElementById('custom_brn_no').value = branch.brn_no;
                document.getElementById('custom_brn_lname').value = branch.brn_lname || '';
                document.getElementById('custom_brn_fname').value = branch.brn_fname || '';
                document.getElementById('custom_brn_ladd').value = branch.brn_ladd || '';
                document.getElementById('custom_brn_fadd').value = branch.brn_fadd || '';
                document.getElementById('custom_brn_ltele').value = branch.brn_ltele || '';
                document.getElementById('custom_brn_ftele').value = branch.brn_ftele || '';
                document.getElementById('custom_brn_lfax').value = branch.brn_lfax || '';
                document.getElementById('custom_brn_ffax').value = branch.brn_ffax || '';
                document.getElementById('custom_brn_lbox').value = branch.brn_lbox || '';
                document.getElementById('custom_brn_fbox').value = branch.brn_fbox || '';
                document.getElementById('custom_brn_code').value = branch.brn_code || '';
                document.getElementById('custom_dblink_name').value = branch.dblink_name || '';
                document.getElementById('custom_brn_img').value = branch.brn_img || '';

                // عرض الشعار إن وجد
                if (branch.brn_img && branch.brn_img.trim() !== '') {
                    document.getElementById('custom_logo_img').src = branch.brn_img;
                    document.getElementById('custom_logo_preview').style.display = 'block';
                } else {
                    document.getElementById('custom_logo_preview').style.display = 'none';
                }

                // فتح النافذة
                document.getElementById('customBranchModal').style.display = 'block';

                console.log('✅ تم فتح النافذة الجديدة بنجاح');
            } else {
                alert('خطأ في تحميل بيانات الفرع: ' + data.message);
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('خطأ في الاتصال بالخادم');
        });
}

function closeCustomModal() {
    document.getElementById('customBranchModal').style.display = 'none';
}

function saveCustomBranch() {
    const branchId = document.getElementById('customBranchId').value;

    const data = {
        brn_lname: document.getElementById('custom_brn_lname').value,
        brn_fname: document.getElementById('custom_brn_fname').value,
        brn_ladd: document.getElementById('custom_brn_ladd').value,
        brn_fadd: document.getElementById('custom_brn_fadd').value,
        brn_ltele: document.getElementById('custom_brn_ltele').value,
        brn_ftele: document.getElementById('custom_brn_ftele').value,
        brn_lfax: document.getElementById('custom_brn_lfax').value,
        brn_ffax: document.getElementById('custom_brn_ffax').value,
        brn_lbox: document.getElementById('custom_brn_lbox').value,
        brn_fbox: document.getElementById('custom_brn_fbox').value,
        brn_code: document.getElementById('custom_brn_code').value,
        dblink_name: document.getElementById('custom_dblink_name').value,
        brn_img: document.getElementById('custom_brn_img').value
    };

    fetch(`/settings/api/branches/${branchId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('تم حفظ التعديلات بنجاح');
            closeCustomModal();
            loadBranches(); // إعادة تحميل القائمة
        } else {
            alert('خطأ في الحفظ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('خطأ في الاتصال بالخادم');
    });
}

// دوال التعامل مع الشعار
function removeCustomLogo() {
    document.getElementById('custom_brn_img').value = '';
    document.getElementById('custom_brn_img_file').value = '';
    document.getElementById('custom_logo_preview').style.display = 'none';
}

// معالج تغيير ملف الشعار في النافذة الجديدة
document.addEventListener('DOMContentLoaded', function() {
    const customFileInput = document.getElementById('custom_brn_img_file');
    if (customFileInput) {
        customFileInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                // معاينة الصورة
                previewCustomLogo(this);

                // رفع الصورة إلى الخادم
                uploadCustomLogo(this.files[0]);
            }
        });
    }
});

function previewCustomLogo(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('custom_logo_img').src = e.target.result;
            document.getElementById('custom_logo_preview').style.display = 'block';
        };
        reader.readAsDataURL(input.files[0]);
    }
}

async function uploadCustomLogo(file) {
    const formData = new FormData();
    formData.append('logo', file);

    try {
        const response = await fetch('/settings/api/upload-logo', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            // تحديث مسار الصورة في الحقل
            document.getElementById('custom_brn_img').value = result.file_path;
            alert('تم رفع الشعار بنجاح');
        } else {
            alert('خطأ في رفع الشعار: ' + result.message);
        }
    } catch (error) {
        console.error('خطأ في رفع الشعار:', error);
        alert('خطأ في رفع الشعار');
    }
}
</script>

{% endblock %}
