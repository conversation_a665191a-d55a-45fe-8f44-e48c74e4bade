<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة عمولات مندوبي المشتريات - نظام الفوجي</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/commission-opening-balances-style.css') }}" rel="stylesheet">
</head>
<body>

    <!-- Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="fas fa-percentage me-3"></i>
                        إدارة عمولات مندوبي المشتريات
                    </h1>
                    <p class="page-subtitle">
                        نظام شامل لإدارة وحساب عمولات مندوبي المشتريات مع تقارير تحليلية متقدمة
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-modern" onclick="window.history.back()">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="breadcrumb-container">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item active" aria-current="page">عمولات مندوبي المشتريات</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="container">

        <!-- Control Panel -->
        <div class="control-panel">
            <h5>
                <i class="fas fa-cogs"></i>
                لوحة التحكم السريع
            </h5>
            <div class="row g-3">
                <div class="col-md-3">
                    <a href="{{ url_for('purchase_commissions.representatives') }}" class="btn btn-primary btn-modern w-100">
                        <i class="fas fa-users"></i>
                        إدارة المندوبين
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="{{ url_for('purchase_commissions.commission_types') }}" class="btn btn-info btn-modern w-100">
                        <i class="fas fa-tags"></i>
                        أنواع العمولات
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="{{ url_for('purchase_commissions.commission_rules') }}" class="btn btn-warning btn-modern w-100">
                        <i class="fas fa-cog"></i>
                        قواعد العمولات
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="{{ url_for('purchase_commissions.calculations') }}" class="btn btn-success btn-modern w-100">
                        <i class="fas fa-calculator"></i>
                        حساب العمولات
                    </a>
                </div>
            </div>
            <div class="row g-3 mt-2">
                <div class="col-md-3">
                    <a href="{{ url_for('purchase_commissions.reports') }}" class="btn btn-danger btn-modern w-100">
                        <i class="fas fa-chart-bar"></i>
                        التقارير
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="{{ url_for('purchase_commissions.purchase_orders') }}" class="btn btn-secondary btn-modern w-100">
                        <i class="fas fa-link"></i>
                        ربط أوامر الشراء
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="{{ url_for('purchase_commissions.dashboard') }}" class="btn btn-primary btn-modern w-100">
                        <i class="fas fa-tachometer-alt"></i>
                        لوحة التحكم
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="{{ url_for('purchase_commissions.notifications') }}" class="btn btn-info btn-modern w-100">
                        <i class="fas fa-bell"></i>
                        الإشعارات
                    </a>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="stats-container">
            <div class="row g-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon primary">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-value">{{ stats.representatives.total or 0 }}</div>
                        <div class="stat-label">المندوبين المسجلين</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon success">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <div class="stat-value">{{ "{:,.0f}".format(stats.commissions.total_amount or 0) }}</div>
                        <div class="stat-label">إجمالي العمولات (ر.س)</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon warning">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-value">{{ stats.commissions.pending_approval or 0 }}</div>
                        <div class="stat-label">في انتظار الاعتماد</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon info">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-value">{{ stats.commissions.paid or 0 }}</div>
                        <div class="stat-label">العمولات المدفوعة</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Data Table -->
        <div class="data-table-container">
            <div class="data-table-header">
                <h5 class="data-table-title">
                    <i class="fas fa-table"></i>
                    ملخص النظام
                </h5>
                <div>
                    <button class="btn btn-primary btn-modern btn-sm" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث البيانات
                    </button>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-modern">
                    <thead>
                        <tr>
                            <th>الوحدة</th>
                            <th>العدد</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <i class="fas fa-users text-primary me-2"></i>
                                المندوبين
                            </td>
                            <td>{{ stats.representatives.total or 0 }}</td>
                            <td>
                                <span class="badge badge-modern bg-success">نشط</span>
                            </td>
                            <td>
                                <a href="{{ url_for('purchase_commissions.representatives') }}" class="btn btn-primary btn-modern btn-sm">
                                    <i class="fas fa-eye"></i>
                                    عرض
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <i class="fas fa-tags text-success me-2"></i>
                                أنواع العمولات
                            </td>
                            <td>{{ stats.commission_types or 0 }}</td>
                            <td>
                                <span class="badge badge-modern bg-info">متاح</span>
                            </td>
                            <td>
                                <a href="{{ url_for('purchase_commissions.commission_types') }}" class="btn btn-success btn-modern btn-sm">
                                    <i class="fas fa-eye"></i>
                                    عرض
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <i class="fas fa-cog text-warning me-2"></i>
                                قواعد العمولات
                            </td>
                            <td>{{ stats.commission_rules or 0 }}</td>
                            <td>
                                <span class="badge badge-modern bg-warning">نشط</span>
                            </td>
                            <td>
                                <a href="{{ url_for('purchase_commissions.commission_rules') }}" class="btn btn-warning btn-modern btn-sm">
                                    <i class="fas fa-eye"></i>
                                    عرض
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <i class="fas fa-calculator text-info me-2"></i>
                                حسابات العمولات
                            </td>
                            <td>{{ stats.commissions.total_calculations or 0 }}</td>
                            <td>
                                <span class="badge badge-modern bg-primary">جاهز</span>
                            </td>
                            <td>
                                <a href="{{ url_for('purchase_commissions.calculations') }}" class="btn btn-info btn-modern btn-sm">
                                    <i class="fas fa-eye"></i>
                                    عرض
                                </a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function refreshData() {
            // إضافة تأثير التحميل
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            button.disabled = true;

            // محاكاة تحديث البيانات
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;

                // إظهار رسالة نجاح
                const alert = document.createElement('div');
                alert.className = 'alert alert-modern alert-success alert-dismissible fade show';
                alert.innerHTML = `
                    <i class="fas fa-check-circle me-2"></i>
                    تم تحديث البيانات بنجاح
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.querySelector('.container').insertBefore(alert, document.querySelector('.control-panel'));

                // إخفاء الرسالة بعد 3 ثوان
                setTimeout(() => {
                    alert.remove();
                }, 3000);
            }, 2000);
        }

        // تأثيرات hover للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(-5px)';
                });
            });
        });
    </script>

</body>
</html>


