{% extends "base.html" %}

{% block title %}تعديل المندوب{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-edit text-primary me-2"></i>
                        تعديل المندوب
                    </h2>
                    <p class="text-muted mb-0">تعديل بيانات المندوب: {{ representative.rep_name }}</p>
                </div>
                <div>
                    <a href="{{ url_for('purchase_commissions.representatives') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-edit me-2"></i>
                        تعديل بيانات المندوب
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">المعلومات الأساسية</h6>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="rep_code" class="form-label">كود المندوب *</label>
                                    <input type="text" class="form-control" id="rep_code" name="rep_code" 
                                           value="{{ representative.rep_code }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="rep_name" class="form-label">اسم المندوب *</label>
                                    <input type="text" class="form-control" id="rep_name" name="rep_name" 
                                           value="{{ representative.rep_name }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="rep_name_en" class="form-label">الاسم بالإنجليزية</label>
                                    <input type="text" class="form-control" id="rep_name_en" name="rep_name_en" 
                                           value="{{ representative.rep_name_en or '' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="specialization" class="form-label">التخصص</label>
                                    <input type="text" class="form-control" id="specialization" name="specialization" 
                                           value="{{ representative.specialization or '' }}" 
                                           placeholder="مثال: أجهزة كمبيوتر، مواد غذائية...">
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">معلومات الاتصال</h6>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="{{ representative.phone or '' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="{{ representative.email or '' }}">
                                </div>
                            </div>
                        </div>

                        <!-- Targets -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">الأهداف الشهرية</h6>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="target_monthly_orders" class="form-label">عدد الطلبات المستهدف</label>
                                    <input type="number" class="form-control" id="target_monthly_orders" 
                                           name="target_monthly_orders" min="0" 
                                           value="{{ representative.target_monthly_orders or 0 }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="target_monthly_quantity" class="form-label">الكمية المستهدفة</label>
                                    <input type="number" class="form-control" id="target_monthly_quantity" 
                                           name="target_monthly_quantity" min="0" step="0.01" 
                                           value="{{ representative.target_monthly_quantity or 0 }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="target_monthly_value" class="form-label">القيمة المستهدفة (ريال)</label>
                                    <input type="number" class="form-control" id="target_monthly_value" 
                                           name="target_monthly_value" min="0" step="0.01" 
                                           value="{{ representative.target_monthly_value or 0 }}">
                                </div>
                            </div>
                        </div>

                        <!-- Status and Settings -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">الحالة والإعدادات</h6>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           {% if representative.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        <strong>مندوب نشط</strong>
                                        <br><small class="text-muted">يمكن للمندوب النشط استلام طلبات جديدة</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="commission_eligible" 
                                           name="commission_eligible" {% if representative.commission_eligible %}checked{% endif %}>
                                    <label class="form-check-label" for="commission_eligible">
                                        <strong>مؤهل للعمولة</strong>
                                        <br><small class="text-muted">يحصل المندوب على عمولات من الطلبات</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">ملاحظات</h6>
                            </div>
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات إضافية</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3">{{ representative.notes or '' }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>
                                            حفظ التعديلات
                                        </button>
                                        <a href="{{ url_for('purchase_commissions.representatives') }}" 
                                           class="btn btn-secondary ms-2">
                                            <i class="fas fa-times me-2"></i>
                                            إلغاء
                                        </a>
                                    </div>
                                    <div>
                                        <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                            <i class="fas fa-trash me-2"></i>
                                            حذف المندوب
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكيد الحذف
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                    <h5>هل أنت متأكد من حذف هذا المندوب؟</h5>
                    <p class="text-muted">
                        <strong>{{ representative.rep_name }}</strong><br>
                        هذا الإجراء لا يمكن التراجع عنه!
                    </p>
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم حذف جميع البيانات المرتبطة بهذا المندوب
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </button>
                <form method="POST" action="{{ url_for('purchase_commissions.delete_representative', rep_id=representative.id) }}" 
                      style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        تأكيد الحذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const repCode = document.getElementById('rep_code').value;
    const repName = document.getElementById('rep_name').value;
    
    if (!repCode || !repName) {
        e.preventDefault();
        alert('كود المندوب والاسم مطلوبان');
        return false;
    }
});
</script>
{% endblock %}
