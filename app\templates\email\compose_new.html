<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء رسالة جديدة - البريد الإلكتروني</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        /* إعادة تعيين الأساسيات */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* متغيرات CSS للألوان والتأثيرات */
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ffa8a8 100%);
            --bg-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
            --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
            --shadow-medium: 0 15px 35px rgba(31, 38, 135, 0.2);
            --shadow-heavy: 0 25px 50px rgba(31, 38, 135, 0.15);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* خلفية الصفحة */
        body {
            background: var(--bg-gradient);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* دوائر الخلفية المتحركة */
        body::before {
            content: '';
            position: fixed;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-30px) rotate(120deg); }
            66% { transform: translateY(-60px) rotate(240deg); }
        }

        .compose-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 10px 20px;
            min-height: auto;
            display: flex;
            align-items: flex-start;
            justify-content: center;
            padding-top: 100px;
            padding-bottom: 20px;
        }

        .compose-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            box-shadow: var(--shadow-light);
            overflow: hidden;
            width: 100%;
            max-width: 1300px;
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(60px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* رأس النافذة */
        .compose-header {
            background: var(--primary-gradient);
            color: white;
            padding: 10px 25px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            overflow: hidden;
        }

        .compose-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
            z-index: 1;
        }

        .compose-title {
            font-size: 18px;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
            z-index: 2;
            position: relative;
        }

        .compose-title i {
            font-size: 32px;
            background: rgba(255, 255, 255, 0.2);
            padding: 12px;
            border-radius: 16px;
            backdrop-filter: blur(10px);
        }

        .header-actions {
            display: flex;
            gap: 12px;
            z-index: 2;
            position: relative;
        }

        .header-btn {
            padding: 12px 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .header-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            color: white;
        }

        /* جسم النافذة */
        .compose-body {
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
        }

        .compose-body {
            padding: 15px 25px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .form-group {
            margin-bottom: 15px;
            position: relative;
        }

        .form-label {
            font-weight: 700;
            color: #374151;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
        }

        .form-label i {
            color: #667eea;
            font-size: 18px;
        }

        .form-control {
            width: 100%;
            border: 2px solid #e0e6ed;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            transition: var(--transition);
            backdrop-filter: blur(10px);
            box-sizing: border-box;
            color: #2d3748;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .form-control:focus {
            border-color: #667eea;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.15), 0 4px 8px rgba(0,0,0,0.1);
            outline: none;
            transform: translateY(-1px);
        }

        .form-control:hover {
            border-color: rgba(102, 126, 234, 0.5);
            background: rgba(255, 255, 255, 0.9);
        }

        /* تصميم علامات المستلمين */
        .recipient-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            min-height: 50px;
            padding: 12px 16px;
            border: 2px solid #e0e6ed;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            backdrop-filter: blur(10px);
            transition: var(--transition);
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .recipient-tags:focus-within {
            border-color: #667eea;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.15), 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }

        .recipient-tag {
            background: var(--primary-gradient);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            animation: slideInScale 0.3s ease-out;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        @keyframes slideInScale {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .recipient-tag .remove {
            cursor: pointer;
            font-weight: bold;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
        }

        .recipient-tag .remove:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .recipient-input {
            border: 2px solid #e0e6ed;
            outline: none;
            flex: 1;
            min-width: 200px;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.95);
            font-size: 14px;
            color: #2d3748;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: var(--transition);
        }

        .recipient-input:focus {
            border-color: #667eea;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.15), 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }

        .recipient-input::placeholder {
            color: #9ca3af;
            font-style: italic;
        }

        /* شريط أدوات المحرر */
        .editor-toolbar {
            border: 2px solid #e0e6ed;
            border-bottom: none;
            border-radius: 8px 8px 0 0;
            padding: 12px 16px;
            background: rgba(248, 250, 252, 0.95);
            backdrop-filter: blur(15px);
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .toolbar-group {
            display: flex;
            gap: 8px;
            align-items: center;
            padding: 0 12px;
            border-right: 2px solid rgba(102, 126, 234, 0.2);
        }

        .toolbar-group:last-child {
            border-right: none;
        }

        .editor-btn {
            background: rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            padding: 10px 14px;
            cursor: pointer;
            transition: var(--transition);
            font-weight: 600;
            color: #374151;
            display: flex;
            align-items: center;
            gap: 6px;
            backdrop-filter: blur(10px);
        }

        .editor-btn:hover {
            background: rgba(102, 126, 234, 0.1);
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
        }

        .editor-btn.active {
            background: var(--primary-gradient);
            color: white;
            border-color: transparent;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .editor-btn i {
            font-size: 16px;
        }

        /* محرر النص */
        .email-editor {
            border: 2px solid #e0e6ed;
            border-top: none;
            border-radius: 0 0 8px 8px;
            min-height: 180px;
            max-height: 280px;
            overflow-y: auto;
            padding: 15px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            line-height: 1.5;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            transition: var(--transition);
            resize: vertical;
            color: #2d3748;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .email-editor:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.15), 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }

        /* أزرار الإجراءات */
        .compose-actions {
            display: flex;
            gap: 20px;
            justify-content: space-between;
            align-items: flex-start;
            padding: 20px 0;
            border-top: 2px solid rgba(255, 255, 255, 0.2);
            margin-top: 20px;
        }

        .actions-left {
            display: flex;
            gap: 16px;
            align-items: center;
        }

        .actions-right {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 16px 32px;
            border: none;
            border-radius: 16px;
            font-weight: 700;
            font-size: 16px;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 10px;
            text-decoration: none;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-success {
            background: var(--success-gradient);
            color: white;
            box-shadow: 0 8px 25px rgba(86, 171, 47, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-4px);
            box-shadow: 0 15px 35px rgba(86, 171, 47, 0.4);
        }

        .btn-success:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-warning {
            background: var(--warning-gradient);
            color: white;
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
        }

        .btn-warning:hover {
            transform: translateY(-4px);
            box-shadow: 0 15px 35px rgba(240, 147, 251, 0.4);
        }

        .btn-cancel {
            background: rgba(255, 255, 255, 0.8);
            color: #6b7280;
            border: 2px solid rgba(107, 114, 128, 0.3);
            backdrop-filter: blur(15px);
        }

        .btn-cancel:hover {
            background: rgba(107, 114, 128, 0.1);
            color: #374151;
            transform: translateY(-4px);
            box-shadow: 0 15px 35px rgba(107, 114, 128, 0.2);
        }

        .btn i {
            font-size: 18px;
        }

        /* رسائل التنبيه */
        .alert {
            padding: 16px 20px;
            border-radius: 12px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 600;
            backdrop-filter: blur(10px);
            animation: slideInRight 0.3s ease-out;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: #065f46;
            border: 2px solid rgba(16, 185, 129, 0.2);
        }

        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            color: #991b1b;
            border: 2px solid rgba(239, 68, 68, 0.2);
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            color: #92400e;
            border: 2px solid rgba(245, 158, 11, 0.2);
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* تنسيق المرفقات */
        .attachments-container {
            border: 2px dashed #e0e6ed;
            border-radius: 8px;
            padding: 15px;
            background: rgba(248, 250, 252, 0.8);
            transition: var(--transition);
        }

        .attachments-container:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .attachments-actions {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 10px;
        }

        .attachments-info {
            font-size: 14px;
            color: #6c757d;
        }

        .attachment-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: white;
            border: 1px solid #e0e6ed;
            border-radius: 8px;
            padding: 12px 15px;
            margin-bottom: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: var(--transition);
        }

        .attachment-item:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }

        .attachment-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .attachment-icon {
            width: 32px;
            height: 32px;
            background: var(--primary-gradient);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        .attachment-details {
            display: flex;
            flex-direction: column;
        }

        .attachment-name {
            font-weight: 600;
            color: #2d3748;
            font-size: 14px;
        }

        .attachment-size {
            font-size: 12px;
            color: #718096;
        }

        .attachment-remove {
            background: none;
            border: none;
            color: #e53e3e;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: var(--transition);
        }

        .attachment-remove:hover {
            background: rgba(229, 62, 62, 0.1);
        }

        /* تنسيق القائمة المنسدلة */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-toggle {
            position: relative;
            padding-right: 35px !important;
        }

        .dropdown-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: var(--transition);
            font-size: 12px;
        }

        .dropdown-toggle.active .dropdown-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border: 2px solid #e0e6ed;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            min-width: 280px;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 8px;
            max-height: 400px;
            overflow-y: auto;
        }

        .dropdown-menu.show {
            opacity: 1 !important;
            visibility: visible !important;
            transform: translateY(0) !important;
            display: block !important;
        }

        .dropdown-item {
            display: flex !important;
            align-items: center;
            gap: 12px;
            padding: 14px 18px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid #f1f5f9;
            color: #374151;
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .dropdown-item i {
            width: 18px;
            text-align: center;
            color: #667eea;
            font-size: 14px;
        }

        /* تنسيق الأزرار السريعة */
        .quick-actions-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: flex-end;
        }

        .quick-actions-row {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .quick-actions-buttons .btn-sm {
            padding: 8px 16px;
            font-size: 13px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.2s ease;
            white-space: nowrap;
            min-width: 130px;
            text-align: center;
        }

        .quick-actions-buttons .btn-sm:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.2);
        }

        .quick-actions-buttons .btn-sm:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .quick-actions-buttons .btn-sm i {
            margin-left: 4px;
            font-size: 11px;
        }

        /* ألوان مخصصة للأزرار */
        .btn-success {
            background: linear-gradient(135deg, #10b981, #059669);
            border: none;
            color: white;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #059669, #047857);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(16, 185, 129, 0.3);
        }

        .btn-info {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            border: none;
            color: white;
        }

        .btn-info:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(59, 130, 246, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            border: none;
            color: white;
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #d97706, #b45309);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(245, 158, 11, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            border: none;
            color: white;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #4b5563, #374151);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(107, 114, 128, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            border: none;
            color: white;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(239, 68, 68, 0.3);
        }

        .btn-light {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border: 1px solid #cbd5e1;
            color: #475569;
        }

        .btn-light:hover {
            background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(203, 213, 225, 0.3);
            color: #334155;
        }

        /* تأثيرات خاصة للأزرار */
        .quick-actions-buttons .btn-sm {
            position: relative;
            overflow: hidden;
        }

        .quick-actions-buttons .btn-sm::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .quick-actions-buttons .btn-sm:hover::before {
            left: 100%;
        }

        /* ألوان الأيقونات */
        .btn-success i { color: rgba(255,255,255,0.9); }
        .btn-info i { color: rgba(255,255,255,0.9); }
        .btn-warning i { color: rgba(255,255,255,0.9); }
        .btn-secondary i { color: rgba(255,255,255,0.9); }
        .btn-danger i { color: rgba(255,255,255,0.9); }
        .btn-light i { color: #475569; }

        /* شريط التنقل العلوي */
        .top-navigation {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            box-sizing: border-box;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .nav-back-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            background: var(--primary-gradient);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
        }

        .nav-back-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .nav-breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .breadcrumb-link {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #6b7280;
            text-decoration: none;
            padding: 6px 12px;
            border-radius: 8px;
            transition: var(--transition);
        }

        .breadcrumb-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .breadcrumb-separator {
            color: #d1d5db;
            font-size: 12px;
        }

        .breadcrumb-current {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #374151;
            font-weight: 600;
            padding: 6px 12px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
        }

        .nav-action-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            background: rgba(255, 255, 255, 0.8);
            color: #374151;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
        }

        .nav-action-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-1px);
        }

        /* التصميم المتجاوب */
        @media (max-width: 768px) {
            .top-navigation {
                flex-direction: column;
                gap: 16px;
                padding: 16px;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                width: 100%;
            }

            .nav-left, .nav-right {
                width: 100%;
                justify-content: center;
            }

            .nav-breadcrumb {
                display: none;
            }

            .nav-back-btn {
                width: 100%;
                justify-content: center;
            }

            .compose-container {
                padding-top: 140px;
            }

            .compose-actions {
                flex-direction: column;
                gap: 16px;
            }

            .actions-left, .actions-right {
                width: 100%;
                justify-content: center;
            }

            .quick-actions-buttons {
                align-items: center;
            }

            .quick-actions-row {
                flex-direction: column;
                gap: 8px;
                width: 100%;
            }

            .quick-actions-buttons .btn-sm {
                padding: 10px 12px;
                font-size: 12px;
                min-width: 100%;
            }

            .btn {
                padding: 14px 24px;
                font-size: 14px;
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <div class="top-navigation">
        <div class="nav-left">
            <button class="nav-back-btn" onclick="goToInbox()">
                <i class="fas fa-arrow-right"></i>
                العودة لصندوق الوارد
            </button>
            <div class="nav-breadcrumb">
                <a href="/dashboard" class="breadcrumb-link">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
                <i class="fas fa-chevron-left breadcrumb-separator"></i>
                <a href="/email/inbox" class="breadcrumb-link">
                    <i class="fas fa-envelope"></i>
                    البريد الإلكتروني
                </a>
                <i class="fas fa-chevron-left breadcrumb-separator"></i>
                <span class="breadcrumb-current">
                    <i class="fas fa-edit"></i>
                    إنشاء رسالة جديدة
                </span>
            </div>
        </div>
        <div class="nav-right">
            <button class="nav-action-btn" onclick="goToInbox()">
                <i class="fas fa-inbox"></i>
                صندوق الوارد
            </button>
            <a href="/email/settings" class="nav-action-btn" style="text-decoration: none;">
                <i class="fas fa-cog"></i>
                الإعدادات
            </a>
            <button class="nav-action-btn" onclick="goToDashboard()">
                <i class="fas fa-tachometer-alt"></i>
                لوحة التحكم
            </button>
        </div>
    </div>

    <div class="compose-container">
        <div class="compose-card">
            <!-- رأس الصفحة الاحترافي -->
            <div class="compose-header">
                <h1 class="compose-title">
                    <i class="fas fa-feather-alt"></i>
                    إنشاء رسالة جديدة
                </h1>
                <div class="header-actions">
                    <button class="header-btn" onclick="saveDraft()">
                        <i class="fas fa-save"></i>
                        حفظ كمسودة
                    </button>
                    <a href="/email/inbox" class="header-btn">
                        <i class="fas fa-arrow-right"></i>
                        العودة
                    </a>
                </div>
            </div>
            
            <!-- محتوى الرسالة -->
            <div class="compose-body">
                <form id="composeForm">
                    <!-- المستقبلون -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-user-friends"></i>
                            إلى:
                        </label>
                        <div class="recipient-tags" id="toRecipients" onclick="focusRecipientInput('to')">
                            <input type="text" class="recipient-input" id="toInput"
                                   placeholder="أدخل عناوين البريد الإلكتروني..."
                                   onkeydown="handleRecipientInput(event, 'to')"
                                   onblur="addRecipientOnBlur(event, 'to')">
                        </div>
                    </div>
                    
                    <!-- الموضوع -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-tag"></i>
                            الموضوع:
                        </label>
                        <input type="text" class="form-control" id="subject"
                               placeholder="موضوع الرسالة..." required>
                    </div>

                    <!-- المرفقات -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-paperclip"></i>
                            المرفقات:
                        </label>
                        <div class="attachments-container">
                            <input type="file" id="attachments" class="form-control" multiple accept="*/*" style="display: none;">
                            <div class="attachments-actions">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="document.getElementById('attachments').click()">
                                    <i class="fas fa-paperclip"></i>
                                    إضافة مرفق
                                </button>
                                <span class="attachments-info text-muted" id="attachments-info">لم يتم اختيار أي ملفات</span>
                            </div>
                            <div class="attachments-list mt-2" id="attachments-list"></div>
                        </div>
                    </div>
                    
                    <!-- محرر النص -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-edit"></i>
                            محتوى الرسالة:
                        </label>
                        
                        <!-- شريط الأدوات -->
                        <div class="editor-toolbar">
                            <div class="toolbar-group">
                                <button type="button" class="editor-btn" onclick="formatText('bold')">
                                    <i class="fas fa-bold"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('italic')">
                                    <i class="fas fa-italic"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('underline')">
                                    <i class="fas fa-underline"></i>
                                </button>
                            </div>
                            
                            <div class="toolbar-group">
                                <button type="button" class="editor-btn" onclick="formatText('justifyLeft')">
                                    <i class="fas fa-align-right"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('justifyCenter')">
                                    <i class="fas fa-align-center"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('justifyRight')">
                                    <i class="fas fa-align-left"></i>
                                </button>
                            </div>
                            
                            <div class="toolbar-group">
                                <button type="button" class="editor-btn" onclick="formatText('insertUnorderedList')">
                                    <i class="fas fa-list-ul"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('insertOrderedList')">
                                    <i class="fas fa-list-ol"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- المحرر -->
                        <div class="email-editor" contenteditable="true" id="emailContent"
                             placeholder="اكتب رسالتك هنا...">
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="compose-actions">
                        <div class="actions-left">
                            <button type="button" class="btn btn-success" onclick="sendEmail()">
                                <i class="fas fa-paper-plane"></i>
                                إرسال الرسالة
                            </button>
                            <button type="button" class="btn btn-warning" onclick="saveDraft()">
                                <i class="fas fa-save"></i>
                                حفظ كمسودة
                            </button>
                        </div>
                        <div class="actions-right">
                            <div class="quick-actions-buttons">
                                <div class="quick-actions-row">
                                    <button type="button" class="btn btn-success btn-sm" onclick="addTestRecipient()" title="إضافة مستلم تجريبي">
                                        <i class="fas fa-user-plus"></i>
                                        مستلم تجريبي
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm" onclick="addMultipleRecipients()" title="إضافة عدة مستلمين">
                                        <i class="fas fa-users"></i>
                                        عدة مستلمين
                                    </button>
                                    <button type="button" class="btn btn-warning btn-sm" onclick="insertTemplate()" title="إدراج قالب جاهز">
                                        <i class="fas fa-file-alt"></i>
                                        قالب جاهز
                                    </button>
                                </div>
                                <div class="quick-actions-row">
                                    <button type="button" class="btn btn-secondary btn-sm" onclick="previewEmail()" title="معاينة الرسالة">
                                        <i class="fas fa-eye"></i>
                                        معاينة الرسالة
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm" onclick="clearAllFields()" title="مسح جميع الحقول">
                                        <i class="fas fa-eraser"></i>
                                        مسح الكل
                                    </button>
                                    <button type="button" class="btn btn-light btn-sm" onclick="showEmailTips()" title="نصائح كتابة الرسائل">
                                        <i class="fas fa-lightbulb"></i>
                                        نصائح مفيدة
                                    </button>
                                </div>
                            </div>
                            <button type="button" class="btn btn-cancel" onclick="goBack()">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <div id="alertContainer" style="position: fixed; top: 20px; right: 20px; z-index: 2000;"></div>

    <script>
        // متغيرات عامة
        let recipients = {
            to: [],
            cc: [],
            bcc: []
        };
        let selectedFiles = [];

        // وظائف إدارة المرفقات
        document.getElementById('attachments').addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            selectedFiles = [...selectedFiles, ...files];
            updateAttachmentsList();
        });

        function updateAttachmentsList() {
            const attachmentsList = document.getElementById('attachments-list');
            const attachmentsInfo = document.getElementById('attachments-info');

            if (selectedFiles.length === 0) {
                attachmentsList.innerHTML = '';
                attachmentsInfo.textContent = 'لم يتم اختيار أي ملفات';
                return;
            }

            attachmentsInfo.textContent = `تم اختيار ${selectedFiles.length} ملف`;

            attachmentsList.innerHTML = selectedFiles.map((file, index) => `
                <div class="attachment-item">
                    <div class="attachment-info">
                        <div class="attachment-icon">
                            <i class="fas fa-file"></i>
                        </div>
                        <div class="attachment-details">
                            <div class="attachment-name">${file.name}</div>
                            <div class="attachment-size">${formatFileSize(file.size)}</div>
                        </div>
                    </div>
                    <button type="button" class="attachment-remove" onclick="removeAttachment(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `).join('');
        }

        function removeAttachment(index) {
            selectedFiles.splice(index, 1);
            updateAttachmentsList();
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // وظائف إدارة المستلمين
        function handleRecipientInput(event, type) {
            if (event.key === 'Enter' || event.key === ',' || event.key === ';') {
                event.preventDefault();
                addRecipient(type);
            } else if (event.key === 'Backspace' && event.target.value === '') {
                removeLastRecipient(type);
            }
        }

        function addRecipient(type) {
            // الحصول على الـ input من الحدث أو البحث عنه
            let input;
            if (event && event.target) {
                input = event.target;
            } else {
                input = document.querySelector(`#${type}Recipients .recipient-input`);
            }

            if (!input) {
                console.error('لم يتم العثور على حقل الإدخال');
                return;
            }

            const email = input.value.trim();
            console.log(`🔍 محاولة إضافة مستلم: ${email} إلى ${type}`);

            if (email && isValidEmail(email)) {
                // التحقق من عدم وجود المستلم مسبقاً
                if (!recipients[type].includes(email)) {
                    recipients[type].push(email);
                    console.log(`✅ تم إضافة ${email} إلى ${type}. المجموع: ${recipients[type].length}`);
                    updateRecipientsDisplay(type);
                    input.value = '';
                } else {
                    showAlert('هذا المستلم موجود بالفعل', 'warning');
                }
            } else if (email) {
                showAlert('عنوان بريد إلكتروني غير صحيح: ' + email, 'error');
            }
        }

        function updateRecipientsDisplay(type) {
            const container = document.getElementById(type + 'Recipients');
            const input = container.querySelector('.recipient-input');

            // مسح العلامات الموجودة
            const existingTags = container.querySelectorAll('.recipient-tag');
            existingTags.forEach(tag => tag.remove());

            // إضافة العلامات الجديدة
            recipients[type].forEach((email, index) => {
                const tag = document.createElement('span');
                tag.className = 'recipient-tag';
                tag.innerHTML = `
                    ${email}
                    <span class="remove" onclick="removeRecipient('${type}', ${index})">×</span>
                `;
                container.insertBefore(tag, input);
            });
        }

        function removeRecipient(type, index) {
            recipients[type].splice(index, 1);
            updateRecipientsDisplay(type);
        }

        function removeLastRecipient(type) {
            if (recipients[type].length > 0) {
                recipients[type].pop();
                updateRecipientsDisplay(type);
            }
        }

        function isValidEmail(email) {
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        }

        // إضافة مستلم عند فقدان التركيز
        function addRecipientOnBlur(event, type) {
            setTimeout(() => {
                const email = event.target.value.trim();
                if (email) {
                    addRecipient(type);
                }
            }, 100);
        }

        // التركيز على حقل الإدخال
        function focusRecipientInput(type) {
            const input = document.querySelector(`#${type}Recipients .recipient-input`);
            if (input) {
                input.focus();
            }
        }

        // تم إزالة كود القائمة المنسدلة واستبداله بأزرار منفصلة

        // إضافة مستلم تجريبي
        function addTestRecipient() {
            const testEmail = '<EMAIL>';
            console.log('🧪 إضافة مستلم تجريبي:', testEmail);

            if (!recipients.to.includes(testEmail)) {
                recipients.to.push(testEmail);
                updateRecipientsDisplay('to');
                showAlert('تم إضافة مستلم تجريبي: ' + testEmail, 'success');
                console.log('✅ تم إضافة المستلم التجريبي. المجموع:', recipients.to.length);
            } else {
                showAlert('المستلم التجريبي موجود بالفعل', 'warning');
                console.log('⚠️ المستلم التجريبي موجود بالفعل');
            }
        }

        // إضافة عدة مستلمين
        function addMultipleRecipients() {
            const emails = prompt('أدخل عناوين البريد مفصولة بفاصلة أو فاصلة منقوطة:\nمثال: <EMAIL>, <EMAIL>');
            if (emails && emails.trim()) {
                const emailList = emails.split(/[,;]/).map(email => email.trim()).filter(email => email);
                let addedCount = 0;

                emailList.forEach(email => {
                    if (email && !recipients.to.includes(email)) {
                        recipients.to.push(email);
                        addedCount++;
                    }
                });

                if (addedCount > 0) {
                    updateRecipientsDisplay('to');
                    showAlert(`تم إضافة ${addedCount} مستلم جديد`, 'success');
                } else {
                    showAlert('لم يتم إضافة أي مستلم جديد', 'warning');
                }
            }
        }

        // إدراج قالب جاهز
        function insertTemplate() {
            const templates = {
                'ترحيب': 'مرحباً بك،\n\nنتمنى لك يوماً سعيداً.\n\nمع أطيب التحيات',
                'شكر': 'شكراً لك على تواصلك معنا.\n\nنقدر اهتمامك ونتطلع للعمل معك.\n\nمع التقدير',
                'اعتذار': 'نعتذر عن أي إزعاج قد تسبب لك.\n\nنحن نعمل على حل المشكلة بأسرع وقت ممكن.\n\nشكراً لصبرك',
                'متابعة': 'أتابع معك بخصوص الموضوع المذكور.\n\nيرجى إعلامي إذا كنت تحتاج أي معلومات إضافية.\n\nبانتظار ردك'
            };

            const templateNames = Object.keys(templates);
            const choice = prompt('اختر قالب:\n' + templateNames.map((name, index) => `${index + 1}. ${name}`).join('\n'));

            if (choice && choice.trim()) {
                const index = parseInt(choice) - 1;
                if (index >= 0 && index < templateNames.length) {
                    const templateName = templateNames[index];
                    const templateContent = templates[templateName];
                    document.getElementById('emailContent').innerHTML = templateContent.replace(/\n/g, '<br>');
                    showAlert(`تم إدراج قالب: ${templateName}`, 'success');
                }
            }
        }

        // مسح جميع الحقول
        function clearAllFields() {
            if (confirm('هل أنت متأكد من مسح جميع الحقول؟')) {
                clearForm();
                showAlert('تم مسح جميع الحقول', 'info');
            }
        }

        // نصائح كتابة الرسائل
        function showEmailTips() {
            const tips = `📧 نصائح لكتابة رسائل فعالة:

1. 📝 اكتب موضوعاً واضحاً ومحدداً
2. 👋 ابدأ بتحية مناسبة
3. 🎯 اذهب مباشرة للنقطة الرئيسية
4. 📋 استخدم نقاط أو ترقيم للوضوح
5. 🙏 اختتم بطلب واضح أو شكر
6. ✅ راجع الرسالة قبل الإرسال
7. 📎 تأكد من المرفقات إن وجدت`;

            alert(tips);
        }

        // معاينة الرسالة
        function previewEmail() {
            const subject = document.getElementById('subject').value || 'بدون موضوع';
            const content = document.getElementById('emailContent').innerHTML || 'بدون محتوى';
            const toList = recipients.to.length > 0 ? recipients.to.join(', ') : 'لا يوجد مستلمين';

            const preview = `📧 معاينة الرسالة:

📬 إلى: ${toList}
📋 الموضوع: ${subject}
📝 المحتوى: ${content.replace(/<[^>]*>/g, '')}
📎 المرفقات: ${selectedFiles.length} ملف`;

            alert(preview);
        }

        // وظائف تنسيق النص
        function formatText(command) {
            document.execCommand(command, false, null);
            document.getElementById('emailContent').focus();
        }

        // إرسال الرسالة
        function sendEmail() {
            console.log('🚀 بدء إرسال الرسالة...');
            console.log('📊 حالة المستلمين:', recipients);

            // إضافة أي مستلم في حقل الإدخال قبل الإرسال
            const toInput = document.getElementById('toInput');
            if (toInput && toInput.value.trim()) {
                console.log('📝 إضافة مستلم من حقل الإدخال:', toInput.value.trim());
                addRecipient('to');
            }

            // التحقق من البيانات المطلوبة
            console.log('🔍 عدد المستلمين:', recipients.to.length);
            if (recipients.to.length === 0) {
                showAlert('يرجى إدخال مستقبل واحد على الأقل', 'error');
                console.log('❌ لا يوجد مستلمين');
                return;
            }

            const subject = document.getElementById('subject').value.trim();
            if (!subject) {
                showAlert('يرجى إدخال موضوع الرسالة', 'error');
                return;
            }

            const body = document.getElementById('emailContent').innerHTML.trim();
            if (!body || body === '<br>' || body === '') {
                showAlert('يرجى كتابة محتوى الرسالة', 'error');
                return;
            }

            // تعطيل الزر وإظهار التحميل
            const sendBtn = event.target;
            const originalText = sendBtn.innerHTML;
            sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
            sendBtn.disabled = true;

            // إعداد البيانات
            const emailData = {
                to_emails: recipients.to,
                cc_emails: recipients.cc || [],
                bcc_emails: recipients.bcc || [],
                subject: subject,
                body: body,
                is_html: true
            };

            console.log('📧 بيانات الرسالة:', emailData);
            console.log('📎 عدد الملفات المحددة:', selectedFiles.length);

            // إعداد FormData للمرفقات
            const formData = new FormData();

            // إضافة البيانات النصية
            formData.append('to_emails', JSON.stringify(emailData.to_emails));
            formData.append('cc_emails', JSON.stringify(emailData.cc_emails));
            formData.append('bcc_emails', JSON.stringify(emailData.bcc_emails));
            formData.append('subject', emailData.subject);
            formData.append('body', emailData.body);
            formData.append('is_html', emailData.is_html.toString());

            // إضافة المرفقات
            selectedFiles.forEach((file, index) => {
                console.log(`📎 إضافة مرفق ${index + 1}: ${file.name} (${file.size} بايت)`);
                formData.append(`attachment_${index}`, file);
            });

            // تحديد نوع الطلب حسب وجود مرفقات
            let fetchOptions;
            if (selectedFiles.length > 0) {
                console.log('📎 إرسال مع مرفقات باستخدام FormData');
                fetchOptions = {
                    method: 'POST',
                    body: formData
                    // لا نضع Content-Type header - المتصفح سيضعه تلقائياً مع boundary
                };
            } else {
                console.log('📧 إرسال بدون مرفقات باستخدام JSON');
                fetchOptions = {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(emailData)
                };
            }

            // إرسال الطلب للـ route المبسط
            fetch('/email/send-simple', fetchOptions)
            .then(response => {
                console.log('📡 استجابة HTTP:', response.status, response.statusText);
                console.log('📡 Headers:', [...response.headers.entries()]);
                return response.json();
            })
            .then(data => {
                console.log('📨 استجابة الخادم:', data);

                if (data.success) {
                    showAlert('✅ تم إرسال الرسالة بنجاح!', 'success');
                    console.log('✅ نجح الإرسال - إعادة توجيه للصندوق الوارد');
                    setTimeout(() => {
                        window.location.href = '/email/inbox';
                    }, 2000);
                } else {
                    console.error('❌ فشل الإرسال:', data.message);
                    showAlert('❌ فشل في إرسال الرسالة: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('❌ خطأ في الإرسال:', error);
                showAlert('❌ حدث خطأ في الاتصال بالخادم', 'error');
            })
            .finally(() => {
                sendBtn.innerHTML = originalText;
                sendBtn.disabled = false;
            });
        }

        // حفظ كمسودة
        function saveDraft() {
            console.log('💾 حفظ المسودة...');

            const subject = document.getElementById('subject').value.trim();
            const body = document.getElementById('emailContent').innerHTML.trim();

            if (!subject && !body && recipients.to.length === 0) {
                showAlert('لا يوجد محتوى لحفظه', 'warning');
                return;
            }

            const draftData = {
                to_emails: recipients.to,
                cc_emails: recipients.cc || [],
                bcc_emails: recipients.bcc || [],
                subject: subject,
                body: body,
                is_draft: true
            };

            fetch('/email/save-draft', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(draftData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('💾 تم حفظ المسودة بنجاح', 'success');
                } else {
                    showAlert('❌ فشل في حفظ المسودة: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('❌ خطأ في حفظ المسودة:', error);
                showAlert('❌ حدث خطأ في حفظ المسودة', 'error');
            });
        }

        // دوال التنقل
        function goToInbox() {
            if (hasUnsavedChanges()) {
                if (confirm('هل أنت متأكد من الخروج؟ سيتم فقدان أي تغييرات غير محفوظة.')) {
                    window.location.href = '/email/inbox';
                }
            } else {
                window.location.href = '/email/inbox';
            }
        }

        function goToSettings() {
            console.log('🔧 محاولة الذهاب للإعدادات...');
            const settingsUrl = '/email/settings';
            console.log('🔗 الرابط المستخدم:', settingsUrl);

            if (hasUnsavedChanges()) {
                if (confirm('هل أنت متأكد من الخروج؟ سيتم فقدان أي تغييرات غير محفوظة.')) {
                    console.log('✅ تأكيد الخروج - الذهاب للإعدادات');
                    // استخدام window.location.replace لتجنب مشاكل الـ cache
                    window.location.replace(settingsUrl);
                }
            } else {
                console.log('✅ لا توجد تغييرات - الذهاب للإعدادات مباشرة');
                // استخدام window.location.replace لتجنب مشاكل الـ cache
                window.location.replace(settingsUrl);
            }
        }

        function goToDashboard() {
            if (hasUnsavedChanges()) {
                if (confirm('هل أنت متأكد من الخروج؟ سيتم فقدان أي تغييرات غير محفوظة.')) {
                    window.location.href = '/dashboard';
                }
            } else {
                window.location.href = '/dashboard';
            }
        }

        // العودة للخلف (للزر القديم)
        function goBack() {
            goToInbox();
        }

        // فحص وجود تغييرات غير محفوظة
        function hasUnsavedChanges() {
            const subject = document.getElementById('subject').value.trim();
            const body = document.getElementById('emailContent').innerHTML.trim();
            const hasRecipients = recipients.to.length > 0 || recipients.cc.length > 0 || recipients.bcc.length > 0;
            const hasAttachments = selectedFiles.length > 0;

            return subject || (body && body !== '<br>' && body !== '') || hasRecipients || hasAttachments;
        }

        // عرض التنبيهات
        function showAlert(message, type = 'info') {
            const container = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;

            const icon = type === 'success' ? 'check-circle' :
                        type === 'error' ? 'exclamation-triangle' :
                        type === 'warning' ? 'exclamation-circle' : 'info-circle';

            alert.innerHTML = `
                <i class="fas fa-${icon}"></i>
                <span>${message}</span>
            `;

            container.appendChild(alert);

            // إزالة التنبيه بعد 5 ثوان
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 تم تحميل نافذة إنشاء الرسالة الاحترافية الجديدة!');

            // إضافة placeholder للمحرر
            const editor = document.getElementById('emailContent');
            editor.addEventListener('focus', function() {
                if (this.innerHTML.trim() === '') {
                    this.innerHTML = '';
                }
            });

            editor.addEventListener('blur', function() {
                if (this.innerHTML.trim() === '') {
                    this.innerHTML = '';
                }
            });
        });

        // حفظ تلقائي كل دقيقة
        setInterval(() => {
            const subject = document.getElementById('subject').value.trim();
            const body = document.getElementById('emailContent').innerHTML.trim();

            if (subject || body || recipients.to.length > 0) {
                console.log('💾 حفظ تلقائي...');
                saveDraft();
            }
        }, 60000); // كل دقيقة

        // مسح النموذج
        function clearForm() {
            // مسح المستلمين
            recipients = { to: [], cc: [], bcc: [] };
            updateRecipientsDisplay();

            // مسح الحقول
            document.getElementById('subject').value = '';
            document.getElementById('emailContent').innerHTML = '';

            // مسح المرفقات
            selectedFiles = [];
            document.getElementById('attachments').value = '';
            updateAttachmentsList();

            console.log('🧹 تم مسح النموذج');
        }
    </script>
</body>
</html>
