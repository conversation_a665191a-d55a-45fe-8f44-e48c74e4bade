<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط - طلبات الحوالات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        .info { border-color: #17a2b8; background: #d1ecf1; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2>🔍 اختبار بسيط - طلبات الحوالات</h2>
                
                <div class="card">
                    <div class="card-header">
                        <h5>اختبار API مباشر</h5>
                    </div>
                    <div class="card-body">
                        <button id="testBtn" class="btn btn-primary btn-lg">🧪 اختبار API الآن</button>
                        <button id="clearBtn" class="btn btn-warning ms-2">🗑️ مسح النتائج</button>
                        
                        <div id="status" class="debug-box info mt-3">
جاهز للاختبار...
                        </div>
                        
                        <div id="result" class="debug-box mt-3" style="display: none;">
                        </div>
                        
                        <div id="data" class="mt-3" style="display: none;">
                            <h6>البيانات المستلمة:</h6>
                            <div class="table-responsive">
                                <table class="table table-striped table-sm">
                                    <thead>
                                        <tr>
                                            <th>رقم الطلب</th>
                                            <th>المستفيد</th>
                                            <th>المبلغ</th>
                                            <th>العملة</th>
                                            <th>الفرع</th>
                                            <th>الصراف</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody id="dataTable">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('🚀 تم تحميل الصفحة');
        
        const statusDiv = document.getElementById('status');
        const resultDiv = document.getElementById('result');
        const dataDiv = document.getElementById('data');
        const dataTable = document.getElementById('dataTable');
        
        function updateStatus(message, type = 'info') {
            console.log('📊 الحالة:', message);
            statusDiv.textContent = message;
            statusDiv.className = `debug-box ${type}`;
        }
        
        function showResult(message, type = 'info') {
            console.log('📋 النتيجة:', message);
            resultDiv.textContent = message;
            resultDiv.className = `debug-box ${type}`;
            resultDiv.style.display = 'block';
        }
        
        function showData(data) {
            if (data && data.length > 0) {
                dataTable.innerHTML = data.map(request => `
                    <tr>
                        <td><strong>${request.request_number || 'غير محدد'}</strong></td>
                        <td>${request.beneficiary_name || 'غير محدد'}</td>
                        <td><strong>${parseFloat(request.amount || 0).toLocaleString('en-US', {minimumFractionDigits: 2})}</strong></td>
                        <td><span class="badge bg-secondary">${request.currency || 'غير محدد'}</span></td>
                        <td>${request.branch_name || 'غير محدد'}</td>
                        <td>${request.money_changer_name || 'غير محدد'}</td>
                        <td><span class="badge bg-warning">${request.status || 'غير محدد'}</span></td>
                    </tr>
                `).join('');
                dataDiv.style.display = 'block';
            } else {
                dataDiv.style.display = 'none';
            }
        }
        
        async function testAPI() {
            try {
                updateStatus('🔄 بدء الاختبار...', 'info');
                
                console.log('📡 إرسال طلب إلى API...');
                updateStatus('📡 إرسال طلب إلى /transfers/api/transfer-requests', 'info');
                
                const response = await fetch('/transfers/api/transfer-requests');
                
                console.log('📨 استجابة الخادم:', response);
                updateStatus(`📨 استجابة الخادم: ${response.status} ${response.statusText}`, 'info');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                console.log('🔄 تحليل JSON...');
                updateStatus('🔄 تحليل البيانات...', 'info');
                
                const data = await response.json();
                
                console.log('📊 البيانات المستلمة:', data);
                
                if (data.success) {
                    updateStatus(`✅ نجح الاختبار! تم تحميل ${data.count} طلب`, 'success');
                    showResult(`✅ نجح الاختبار!
📊 عدد الطلبات: ${data.count}
📋 البيانات متاحة: ${data.data ? 'نعم' : 'لا'}
🕒 وقت الاختبار: ${new Date().toLocaleString('ar-SA')}`, 'success');
                    
                    showData(data.data);
                } else {
                    updateStatus('❌ فشل الاختبار - API أرجع success: false', 'error');
                    showResult(`❌ فشل الاختبار!
📝 رسالة الخطأ: ${data.message || 'غير محدد'}
📊 البيانات: ${JSON.stringify(data, null, 2)}`, 'error');
                }
                
            } catch (error) {
                console.error('❌ خطأ في الاختبار:', error);
                updateStatus('❌ خطأ في الاختبار', 'error');
                showResult(`❌ خطأ في الاختبار!
📝 نوع الخطأ: ${error.name}
📋 رسالة الخطأ: ${error.message}
🔍 تفاصيل إضافية: ${error.stack ? 'متاحة في Console' : 'غير متاحة'}
🕒 وقت الخطأ: ${new Date().toLocaleString('ar-SA')}

💡 اقتراحات:
- تأكد من تشغيل الخادم
- تأكد من تسجيل الدخول
- افحص Network tab في أدوات المطور
- افحص Console للمزيد من التفاصيل`, 'error');
            }
        }
        
        function clearResults() {
            updateStatus('جاهز للاختبار...', 'info');
            resultDiv.style.display = 'none';
            dataDiv.style.display = 'none';
            console.clear();
        }
        
        // ربط الأحداث
        document.getElementById('testBtn').addEventListener('click', testAPI);
        document.getElementById('clearBtn').addEventListener('click', clearResults);
        
        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log('📄 تم تحميل الصفحة بالكامل');
            setTimeout(() => {
                console.log('⏰ بدء الاختبار التلقائي...');
                testAPI();
            }, 1000);
        });
        
        // تتبع الأخطاء
        window.addEventListener('error', function(e) {
            console.error('❌ خطأ JavaScript:', e);
            updateStatus('❌ خطأ JavaScript - افحص Console', 'error');
        });
        
        console.log('✅ تم تحميل JavaScript بنجاح');
    </script>
</body>
</html>
