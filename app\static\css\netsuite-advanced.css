/* 
NetSuite Oracle Advanced Components
مكونات NetSuite Oracle المتقدمة
*/

/* ========== NetSuite Variables ========== */
:root {
    /* NetSuite Oracle Official Colors */
    --ns-oracle-blue: #1f4e79;
    --ns-oracle-blue-light: #4a90e2;
    --ns-oracle-orange: #f97316;
    --ns-oracle-orange-light: #fb923c;
    
    /* Advanced Gradients */
    --ns-gradient-primary: linear-gradient(135deg, #1f4e79 0%, #2563eb 50%, #3b82f6 100%);
    --ns-gradient-secondary: linear-gradient(135deg, #f97316 0%, #ea580c 50%, #c2410c 100%);
    --ns-gradient-surface: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    --ns-gradient-glass: linear-gradient(145deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    
    /* Advanced Shadows */
    --ns-shadow-glass: 0 8px 32px rgba(31, 78, 121, 0.12);
    --ns-shadow-floating: 0 12px 40px rgba(0, 0, 0, 0.15);
    --ns-shadow-depth: 0 20px 60px rgba(31, 78, 121, 0.2);
    
    /* Animation Curves */
    --ns-ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
    --ns-ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --ns-ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* ========== Advanced Layout System ========== */
.ns-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.ns-grid {
    display: grid;
    gap: 2rem;
}

.ns-grid-auto {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.ns-flex {
    display: flex;
    gap: 1rem;
}

.ns-flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ========== Advanced Cards ========== */
.ns-card-advanced {
    background: var(--ns-gradient-surface);
    border: 1px solid rgba(31, 78, 121, 0.1);
    border-radius: 1rem;
    box-shadow: var(--ns-shadow-glass);
    transition: all 0.4s var(--ns-ease-smooth);
    position: relative;
    overflow: hidden;
}

.ns-card-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--ns-gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s var(--ns-ease-smooth);
}

.ns-card-advanced:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--ns-shadow-depth);
    border-color: rgba(31, 78, 121, 0.2);
}

.ns-card-advanced:hover::before {
    transform: scaleX(1);
}

.ns-card-glass {
    background: var(--ns-gradient-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1.5rem;
    box-shadow: var(--ns-shadow-glass);
}

/* ========== Advanced Buttons ========== */
.ns-btn-advanced {
    position: relative;
    padding: 1rem 2rem;
    border: none;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s var(--ns-ease-smooth);
    overflow: hidden;
    text-transform: none;
}

.ns-btn-primary-advanced {
    background: var(--ns-gradient-primary);
    color: white;
    box-shadow: var(--ns-shadow-glass);
}

.ns-btn-primary-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s var(--ns-ease-smooth);
}

.ns-btn-primary-advanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--ns-shadow-floating);
}

.ns-btn-primary-advanced:hover::before {
    left: 100%;
}

.ns-btn-secondary-advanced {
    background: var(--ns-gradient-secondary);
    color: white;
    box-shadow: 0 4px 14px rgba(249, 115, 22, 0.3);
}

.ns-btn-ghost-advanced {
    background: transparent;
    color: var(--ns-oracle-blue);
    border: 2px solid var(--ns-oracle-blue);
    position: relative;
    z-index: 1;
}

.ns-btn-ghost-advanced::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--ns-oracle-blue);
    transition: width 0.3s var(--ns-ease-smooth);
    z-index: -1;
}

.ns-btn-ghost-advanced:hover {
    color: white;
}

.ns-btn-ghost-advanced:hover::after {
    width: 100%;
}

/* ========== Advanced Navigation ========== */
.ns-navbar-advanced {
    background: var(--ns-gradient-primary);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--ns-shadow-glass);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 70px;
    display: flex;
    align-items: center;
    padding: 0 2rem;
}

.ns-navbar-brand-advanced {
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.ns-navbar-brand-advanced i {
    color: var(--ns-oracle-orange);
    font-size: 1.75rem;
}

.ns-navbar-nav-advanced {
    display: flex;
    gap: 2rem;
    margin-left: auto;
}

.ns-navbar-link-advanced {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s var(--ns-ease-smooth);
    position: relative;
}

.ns-navbar-link-advanced::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--ns-oracle-orange);
    transition: all 0.3s var(--ns-ease-smooth);
    transform: translateX(-50%);
}

.ns-navbar-link-advanced:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.ns-navbar-link-advanced:hover::after {
    width: 80%;
}

/* ========== Advanced Sidebar ========== */
.ns-sidebar-advanced {
    position: fixed;
    top: 70px;
    right: 0;
    width: 300px;
    height: calc(100vh - 70px);
    background: var(--ns-gradient-surface);
    border-left: 1px solid rgba(31, 78, 121, 0.1);
    box-shadow: var(--ns-shadow-glass);
    overflow-y: auto;
    z-index: 999;
    padding: 2rem 0;
}

.ns-sidebar-section-advanced {
    margin-bottom: 2rem;
}

.ns-sidebar-title-advanced {
    color: var(--ns-oracle-blue);
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    padding: 0 2rem;
    margin-bottom: 1rem;
}

.ns-sidebar-link-advanced {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 2rem;
    color: #6b7280;
    text-decoration: none;
    transition: all 0.3s var(--ns-ease-smooth);
    position: relative;
    border-right: 3px solid transparent;
}

.ns-sidebar-link-advanced i {
    width: 20px;
    text-align: center;
    color: var(--ns-oracle-orange);
    transition: all 0.3s var(--ns-ease-smooth);
}

.ns-sidebar-link-advanced:hover {
    background: linear-gradient(90deg, rgba(31, 78, 121, 0.05) 0%, rgba(31, 78, 121, 0.1) 100%);
    color: var(--ns-oracle-blue);
    border-right-color: var(--ns-oracle-orange);
    transform: translateX(-4px);
}

.ns-sidebar-link-advanced:hover i {
    transform: scale(1.2);
    color: var(--ns-oracle-blue);
}

.ns-sidebar-link-advanced.active {
    background: linear-gradient(90deg, rgba(31, 78, 121, 0.1) 0%, rgba(31, 78, 121, 0.15) 100%);
    color: var(--ns-oracle-blue);
    border-right-color: var(--ns-oracle-orange);
    font-weight: 600;
}

/* ========== Advanced Tables ========== */
.ns-table-advanced {
    width: 100%;
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--ns-shadow-glass);
    border: 1px solid rgba(31, 78, 121, 0.1);
}

.ns-table-advanced thead {
    background: var(--ns-gradient-primary);
    color: white;
}

.ns-table-advanced th {
    padding: 1.5rem 1rem;
    font-weight: 600;
    text-align: right;
    border: none;
    position: relative;
}

.ns-table-advanced th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--ns-oracle-orange);
}

.ns-table-advanced td {
    padding: 1.25rem 1rem;
    border-bottom: 1px solid rgba(31, 78, 121, 0.05);
    transition: all 0.2s var(--ns-ease-smooth);
}

.ns-table-advanced tbody tr {
    transition: all 0.3s var(--ns-ease-smooth);
}

.ns-table-advanced tbody tr:hover {
    background: linear-gradient(90deg, rgba(31, 78, 121, 0.02) 0%, rgba(31, 78, 121, 0.05) 100%);
    transform: scale(1.01);
    box-shadow: 0 2px 8px rgba(31, 78, 121, 0.1);
}

/* ========== Advanced Forms ========== */
.ns-form-group-advanced {
    margin-bottom: 2rem;
    position: relative;
}

.ns-input-advanced {
    width: 100%;
    padding: 1rem 1.5rem;
    border: 2px solid rgba(31, 78, 121, 0.1);
    border-radius: 0.75rem;
    background: white;
    font-size: 1rem;
    transition: all 0.3s var(--ns-ease-smooth);
    outline: none;
}

.ns-input-advanced:focus {
    border-color: var(--ns-oracle-blue);
    box-shadow: 0 0 0 4px rgba(31, 78, 121, 0.1);
    transform: translateY(-2px);
}

.ns-label-advanced {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--ns-oracle-blue);
    font-weight: 600;
    font-size: 0.875rem;
}

/* ========== Advanced Animations ========== */
@keyframes ns-fade-in-up {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes ns-slide-in-right {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes ns-scale-in {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes ns-pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.ns-animate-fade-in-up {
    animation: ns-fade-in-up 0.6s var(--ns-ease-smooth);
}

.ns-animate-slide-in-right {
    animation: ns-slide-in-right 0.6s var(--ns-ease-smooth);
}

.ns-animate-scale-in {
    animation: ns-scale-in 0.4s var(--ns-ease-smooth);
}

.ns-animate-pulse {
    animation: ns-pulse 2s infinite;
}

/* ========== Responsive Design ========== */
@media (max-width: 768px) {
    .ns-sidebar-advanced {
        transform: translateX(100%);
        transition: transform 0.3s var(--ns-ease-smooth);
    }
    
    .ns-sidebar-advanced.show {
        transform: translateX(0);
    }
    
    .ns-container {
        padding: 0 1rem;
    }
    
    .ns-grid {
        gap: 1rem;
    }
    
    .ns-navbar-advanced {
        padding: 0 1rem;
    }
    
    .ns-navbar-nav-advanced {
        display: none;
    }
}

/* ========== Utility Classes ========== */
.ns-text-primary { color: var(--ns-oracle-blue); }
.ns-text-secondary { color: var(--ns-oracle-orange); }
.ns-bg-primary { background: var(--ns-gradient-primary); }
.ns-bg-secondary { background: var(--ns-gradient-secondary); }
.ns-shadow-glass { box-shadow: var(--ns-shadow-glass); }
.ns-shadow-floating { box-shadow: var(--ns-shadow-floating); }
.ns-shadow-depth { box-shadow: var(--ns-shadow-depth); }
