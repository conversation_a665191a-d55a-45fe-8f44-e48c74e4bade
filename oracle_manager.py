#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Oracle Database Manager - مدير قاعدة البيانات Oracle
استخدام oracledb بدلاً من JDBC
"""

import os
import logging
from typing import Optional, Dict, Any, List, Tuple
import oracledb
from contextlib import contextmanager

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OracleManager:
    """مدير قاعدة البيانات Oracle"""
    
    def __init__(self):
        self.connection = None
        self.config = {
            'host': os.environ.get('ORACLE_HOST', 'localhost'),
            'port': int(os.environ.get('ORACLE_PORT', 1521)),
            'service_name': os.environ.get('ORACLE_SID', 'ORCL'),
            'user': os.environ.get('ORACLE_USERNAME', 'accounting_user'),
            'password': os.environ.get('ORACLE_PASSWORD', 'accounting_password')
        }
        
    def connect(self) -> bool:
        """الاتصال بقاعدة البيانات"""
        try:
            # بناء DSN
            dsn = f"{self.config['host']}:{self.config['port']}/{self.config['service_name']}"
            
            # الاتصال
            self.connection = oracledb.connect(
                user=self.config['user'],
                password=self.config['password'],
                dsn=dsn
            )
            
            # تعيين إعدادات الجلسة للنصوص العربية
            try:
                cursor = self.connection.cursor()
                cursor.execute("ALTER SESSION SET NLS_LANGUAGE='ARABIC'")
                cursor.execute("ALTER SESSION SET NLS_TERRITORY='EGYPT'")
                cursor.close()
                logger.info("✅ تم الاتصال بـ Oracle Database بنجاح مع دعم العربية")
            except Exception as e:
                logger.warning(f"⚠️ تعذر تعيين إعدادات NLS: {e}")
                logger.info("✅ تم الاتصال بـ Oracle Database بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل الاتصال بـ Oracle: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال"""
        if self.connection:
            try:
                self.connection.close()
                self.connection = None
                logger.info("✅ تم قطع الاتصال بـ Oracle")
            except Exception as e:
                logger.error(f"❌ خطأ في قطع الاتصال: {e}")
    
    @contextmanager
    def get_cursor(self):
        """الحصول على cursor مع إدارة تلقائية للموارد"""
        if not self.connection:
            if not self.connect():
                raise Exception("فشل في الاتصال بقاعدة البيانات")
        
        cursor = self.connection.cursor()
        try:
            yield cursor
        finally:
            cursor.close()
    
    def execute_query(self, sql: str, params: Optional[List] = None) -> List[Tuple]:
        """تنفيذ استعلام SELECT"""
        try:
            with self.get_cursor() as cursor:
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)
                
                result = cursor.fetchall()
                logger.info(f"✅ تم تنفيذ الاستعلام بنجاح: {len(result)} سجل")
                return result
                
        except Exception as e:
            logger.error(f"❌ خطأ في تنفيذ الاستعلام: {e}")
            logger.error(f"SQL: {sql}")
            logger.error(f"Params: {params}")
            return []
    
    def execute_update(self, sql: str, params: Optional[List] = None) -> int:
        """تنفيذ استعلام INSERT/UPDATE/DELETE مع commit تلقائي"""
        try:
            with self.get_cursor() as cursor:
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)

                self.connection.commit()
                rows_affected = cursor.rowcount
                logger.info(f"✅ تم تنفيذ التحديث بنجاح: {rows_affected} سجل متأثر")
                return rows_affected

        except Exception as e:
            logger.error(f"❌ خطأ في تنفيذ التحديث: {e}")
            logger.error(f"SQL: {sql}")
            logger.error(f"Params: {params}")
            if self.connection:
                self.connection.rollback()
            return 0

    def execute_update_no_commit(self, sql: str, params: Optional[List] = None) -> int:
        """تنفيذ استعلام INSERT/UPDATE/DELETE بدون commit تلقائي"""
        try:
            with self.get_cursor() as cursor:
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)

                rows_affected = cursor.rowcount
                logger.info(f"✅ تم تنفيذ التحديث بدون commit: {rows_affected} سجل متأثر")
                return rows_affected

        except Exception as e:
            logger.error(f"❌ خطأ في تنفيذ التحديث: {e}")
            logger.error(f"SQL: {sql}")
            logger.error(f"Params: {params}")
            raise e
    
    def execute_many(self, sql: str, params_list: List[List]) -> int:
        """تنفيذ استعلام متعدد"""
        try:
            with self.get_cursor() as cursor:
                cursor.executemany(sql, params_list)
                self.connection.commit()
                rows_affected = cursor.rowcount
                logger.info(f"✅ تم تنفيذ التحديث المتعدد بنجاح: {rows_affected} سجل متأثر")
                return rows_affected
                
        except Exception as e:
            logger.error(f"❌ خطأ في تنفيذ التحديث المتعدد: {e}")
            if self.connection:
                self.connection.rollback()
            return 0
    
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """الحصول على معلومات الجدول"""
        try:
            # الحصول على أعمدة الجدول
            columns_sql = """
                SELECT column_name, data_type, nullable, data_default
                FROM user_tab_columns 
                WHERE table_name = UPPER(:1)
                ORDER BY column_id
            """
            columns = self.execute_query(columns_sql, [table_name])
            
            # الحصول على عدد السجلات
            count_sql = f"SELECT COUNT(*) FROM {table_name}"
            count_result = self.execute_query(count_sql)
            count = count_result[0][0] if count_result else 0
            
            return {
                'table_name': table_name,
                'columns': [
                    {
                        'name': col[0],
                        'type': col[1],
                        'nullable': col[2] == 'Y',
                        'default': col[3]
                    } for col in columns
                ],
                'row_count': count
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على معلومات الجدول {table_name}: {e}")
            return {}
    
    def test_connection(self) -> Dict[str, Any]:
        """اختبار الاتصال"""
        try:
            # اختبار أساسي
            result = self.execute_query("SELECT SYSDATE FROM DUAL")
            if not result:
                return {'status': 'error', 'message': 'فشل في تنفيذ الاستعلام'}
            
            db_time = result[0][0]
            
            # اختبار الجداول
            tables_sql = "SELECT table_name FROM user_tables ORDER BY table_name"
            tables_result = self.execute_query(tables_sql)
            tables = [row[0] for row in tables_result]
            
            # معلومات قاعدة البيانات
            version_result = self.execute_query("SELECT * FROM v$version WHERE ROWNUM = 1")
            version = version_result[0][0] if version_result else "غير معروف"
            
            return {
                'status': 'success',
                'message': 'Oracle Database يعمل بشكل صحيح',
                'db_time': str(db_time),
                'version': version,
                'tables': tables,
                'table_count': len(tables)
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'خطأ في اختبار الاتصال: {str(e)}'
            }
    
    def create_user_if_not_exists(self, username: str, password: str, email: str, full_name: str, is_admin: bool = False):
        """إنشاء مستخدم إذا لم يكن موجوداً"""
        try:
            # التحقق من وجود المستخدم
            check_sql = "SELECT COUNT(*) FROM users WHERE username = :1"
            result = self.execute_query(check_sql, [username])
            
            if result and result[0][0] > 0:
                logger.info(f"المستخدم {username} موجود بالفعل")
                return True
            
            # إنشاء hash لكلمة المرور
            from werkzeug.security import generate_password_hash
            password_hash = generate_password_hash(password)
            
            # الحصول على أكبر ID
            max_id_sql = "SELECT NVL(MAX(id), 0) + 1 FROM users"
            max_id_result = self.execute_query(max_id_sql)
            new_id = max_id_result[0][0] if max_id_result else 1
            
            # إدراج المستخدم الجديد
            insert_sql = """
                INSERT INTO users (id, username, email, password_hash, full_name, is_admin, is_active, created_at)
                VALUES (:1, :2, :3, :4, :5, :6, 1, SYSDATE)
            """
            
            rows_affected = self.execute_update(insert_sql, [
                new_id, username, email, password_hash, full_name, 1 if is_admin else 0
            ])
            
            if rows_affected > 0:
                logger.info(f"✅ تم إنشاء المستخدم {username} بنجاح")
                return True
            else:
                logger.error(f"❌ فشل في إنشاء المستخدم {username}")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء المستخدم {username}: {e}")
            return False

    def commit(self):
        """تأكيد المعاملة"""
        try:
            if self.connection:
                self.connection.commit()
                logger.info("✅ تم تأكيد المعاملة")
        except Exception as e:
            logger.error(f"❌ خطأ في تأكيد المعاملة: {e}")
            raise e

    def rollback(self):
        """إلغاء المعاملة"""
        try:
            if self.connection:
                self.connection.rollback()
                logger.info("🔄 تم إلغاء المعاملة")
        except Exception as e:
            logger.error(f"❌ خطأ في إلغاء المعاملة: {e}")
            raise e

    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        try:
            if self.connection:
                self.connection.close()
                self.connection = None
                logger.info("✅ تم إغلاق الاتصال بقاعدة البيانات")
        except Exception as e:
            logger.error(f"❌ خطأ في إغلاق الاتصال: {e}")

# إنشاء مثيل عام
oracle_manager = OracleManager()

def get_oracle_manager() -> OracleManager:
    """الحصول على مدير Oracle"""
    return oracle_manager
