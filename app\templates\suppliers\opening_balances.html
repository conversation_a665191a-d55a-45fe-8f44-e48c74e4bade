<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأرصدة الافتتاحية للموردين - نظام الفوجي</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    
    <style>
        :root {
            --primary: #2c3e50;
            --secondary: #3498db;
            --success: #27ae60;
            --warning: #f39c12;
            --danger: #e74c3c;
            --info: #17a2b8;
            --light: #f8f9fa;
            --dark: #343a40;
            --surface: #ffffff;
            --border: #dee2e6;
            --text-primary: #2c3e50;
            --text-secondary: #6c757d;
            --text-muted: #adb5bd;
            --radius: 12px;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: var(--text-primary);
        }

        /* Header */
        .page-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-top: 0.5rem;
        }

        /* Breadcrumb */
        .breadcrumb-container {
            background: var(--surface);
            padding: 1rem 0;
            margin-bottom: 2rem;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
        }

        .breadcrumb {
            background: none;
            margin: 0;
            padding: 0;
        }

        .breadcrumb-item a {
            color: var(--secondary);
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb-item a:hover {
            color: var(--primary);
        }

        /* Cards */
        .card-modern {
            background: var(--surface);
            border: none;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .card-header-modern {
            background: linear-gradient(135deg, var(--light) 0%, #e9ecef 100%);
            border-bottom: 1px solid var(--border);
            padding: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Stats Cards */
        .stats-card {
            background: var(--surface);
            border-radius: var(--radius);
            padding: 2rem;
            text-align: center;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            border-left: 4px solid var(--secondary);
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stats-card.success {
            border-left-color: var(--success);
        }

        .stats-card.warning {
            border-left-color: var(--warning);
        }

        .stats-card.danger {
            border-left-color: var(--danger);
        }

        .stats-card.info {
            border-left-color: var(--info);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
            display: block;
        }

        .stats-label {
            font-size: 1rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .stats-icon {
            font-size: 3rem;
            opacity: 0.1;
            position: absolute;
            top: 1rem;
            left: 1rem;
        }

        /* Control Panel */
        .control-panel {
            background: var(--surface);
            border-radius: var(--radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .form-control-modern {
            border: 2px solid var(--border);
            border-radius: var(--radius);
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control-modern:focus {
            border-color: var(--secondary);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .btn-modern {
            border-radius: var(--radius);
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        /* Table */
        .table-container {
            background: var(--surface);
            border-radius: var(--radius);
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .table-modern {
            margin: 0;
            font-size: 0.9rem;
        }

        .table-modern thead th {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            border: none;
            padding: 1rem;
            font-weight: 600;
            text-align: center;
        }

        .table-modern tbody td {
            padding: 1rem;
            border-bottom: 1px solid var(--border);
            vertical-align: middle;
            text-align: center;
        }

        .table-modern tbody tr:hover {
            background: var(--light);
        }

        /* Status Badges */
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-draft {
            background: #ffeaa7;
            color: #d63031;
        }

        .status-approved {
            background: #55efc4;
            color: #00b894;
        }

        .status-posted {
            background: #74b9ff;
            color: #0984e3;
        }

        /* Loading */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            background: white;
            padding: 2rem;
            border-radius: var(--radius);
            text-align: center;
        }

        /* Import Steps */
        .step-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #dee2e6;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .step-item.active .step-number {
            background: var(--primary);
            color: white;
        }

        .step-item.completed .step-number {
            background: var(--success);
            color: white;
        }

        .step-text {
            font-size: 0.9rem;
            color: var(--text-secondary);
            text-align: center;
        }

        .step-line {
            flex: 1;
            height: 2px;
            background: #dee2e6;
            margin: 0 1rem;
            align-self: flex-start;
            margin-top: 20px;
        }

        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: var(--radius);
            padding: 3rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: var(--primary);
            background: var(--light);
        }

        .upload-area.dragover {
            border-color: var(--success);
            background: #d4edda;
        }

        /* Supplier Search */
        .supplier-search-container {
            position: relative;
        }

        .supplier-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1050;
            background: white;
            border: 1px solid var(--border);
            border-radius: var(--radius);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            max-height: 300px;
            overflow-y: auto;
        }

        .supplier-dropdown-content {
            padding: 0.5rem 0;
        }

        .supplier-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
            border-bottom: 1px solid #f8f9fa;
            transition: all 0.2s ease;
        }

        .supplier-item:hover {
            background: var(--light);
        }

        .supplier-item:last-child {
            border-bottom: none;
        }

        .supplier-item.selected {
            background: var(--primary);
            color: white;
        }

        .supplier-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .supplier-code {
            font-size: 0.85rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        .supplier-item.selected .supplier-name,
        .supplier-item.selected .supplier-code {
            color: white;
        }

        .no-results {
            padding: 1rem;
            text-align: center;
            color: var(--text-secondary);
            font-style: italic;
        }

        /* Validation Styles */
        .is-invalid {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
        }

        .is-invalid:focus {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
        }

        /* Breadcrumb */
        .breadcrumb-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }

        .breadcrumb {
            background: none;
            margin-bottom: 0;
            padding: 0;
        }

        .breadcrumb-item {
            font-size: 0.9rem;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "←";
            color: var(--text-secondary);
            margin: 0 0.5rem;
        }

        .breadcrumb-item a {
            color: var(--primary);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .breadcrumb-item a:hover {
            color: var(--secondary);
            text-decoration: underline;
        }

        .breadcrumb-item.active {
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .page-title {
                font-size: 2rem;
            }

            .stats-card {
                margin-bottom: 1rem;
            }

            .table-responsive {
                font-size: 0.8rem;
            }

            .step-item {
                font-size: 0.8rem;
            }

            .step-number {
                width: 30px;
                height: 30px;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div class="mt-3">جاري تحميل البيانات...</div>
        </div>
    </div>

    <!-- Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="fas fa-balance-scale me-3"></i>
                        إدارة الأرصدة الافتتاحية للموردين
                    </h1>
                    <p class="page-subtitle">
                        إدخال ومراجعة واعتماد الأرصدة الافتتاحية للموردين للفترة المحاسبية الجديدة
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-modern" onclick="window.history.back()">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="breadcrumb-container">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('main.dashboard') }}">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('suppliers.index') }}">
                            <i class="fas fa-building me-1"></i>
                            إدارة الموردين
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <i class="fas fa-calculator me-1"></i>
                        الأرصدة الافتتاحية
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Control Panel -->
        <div class="control-panel">
            <div class="row align-items-end">
                <div class="col-md-3">
                    <label class="form-label fw-bold">البحث السريع للمورد</label>
                    <div class="position-relative">
                        <input type="text" id="supplierFilterSearch" class="form-control form-control-modern"
                               placeholder="ابحث عن مورد..." autocomplete="off">
                        <input type="hidden" id="supplierFilterId">
                        <div class="dropdown-menu" id="supplierFilterDropdown" style="display: none; width: 100%; max-height: 200px; overflow-y: auto;">
                        </div>
                        <div class="position-absolute top-50 end-0 translate-middle-y me-2">
                            <button type="button" id="clearSupplierFilter" class="btn btn-sm btn-link text-muted p-0 me-2"
                                    style="display: none; border: none; background: none;"
                                    onclick="clearSupplierFilter()" title="مسح الفلتر">
                                <i class="fas fa-times"></i>
                            </button>
                            <i class="fas fa-search text-muted"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">العملة</label>
                    <select id="currencyCode" class="form-control form-control-modern">
                        <option value="">جميع العملات</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">حالة الأرصدة</label>
                    <select id="statusFilter" class="form-control form-control-modern">
                        <option value="all">جميع الحالات</option>
                        <option value="draft">مسودة</option>
                        <option value="approved">معتمد</option>
                        <option value="posted">مرحل</option>
                    </select>
                </div>
                <div class="col-md-5">
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary btn-modern" onclick="loadData()">
                            <i class="fas fa-sync-alt me-2"></i>تحديث البيانات
                        </button>
                        <button class="btn btn-success btn-modern" onclick="showAddModal()">
                            <i class="fas fa-plus me-2"></i>إضافة رصيد
                        </button>
                        <button class="btn btn-info btn-modern" onclick="showImportModal()">
                            <i class="fas fa-file-excel me-2"></i>استيراد Excel
                        </button>
                        <button id="approveBtn" class="btn btn-warning btn-modern" onclick="handleApprovalAction()" title="عمليات جماعية">
                            <i id="approveIcon" class="fas fa-check me-2"></i><span id="approveText">اعتماد جماعي</span>
                        </button>
                        <button id="postBtn" class="btn btn-info btn-modern" onclick="handlePostingAction()" title="عمليات جماعية">
                            <i id="postIcon" class="fas fa-paper-plane me-2"></i><span id="postText">ترحيل جماعي</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4" id="statsContainer">
            <div class="col-md-3">
                <div class="stats-card success position-relative">
                    <i class="fas fa-users stats-icon"></i>
                    <span class="stats-number" id="totalSuppliers">0</span>
                    <div class="stats-label">إجمالي الموردين</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card info position-relative">
                    <i class="fas fa-arrow-up stats-icon"></i>
                    <span class="stats-number" id="totalDebit">0</span>
                    <div class="stats-label">إجمالي المدين</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card warning position-relative">
                    <i class="fas fa-arrow-down stats-icon"></i>
                    <span class="stats-number" id="totalCredit">0</span>
                    <div class="stats-label">إجمالي الدائن</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card danger position-relative">
                    <i class="fas fa-balance-scale stats-icon"></i>
                    <span class="stats-number" id="netBalance">0</span>
                    <div class="stats-label">صافي الرصيد</div>
                </div>
            </div>
        </div>

        <!-- Opening Balances Table -->
        <div class="card-modern">
            <div class="card-header-modern">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        قائمة الأرصدة الافتتاحية
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="exportToExcel()">
                            <i class="fas fa-file-excel me-1"></i>تصدير Excel
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="printTable()">
                            <i class="fas fa-print me-1"></i>طباعة
                        </button>
                    </div>
                </div>
            </div>
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-modern" id="openingBalancesTable">
                        <thead>
                            <tr>
                                <th>المورد</th>
                                <th>كود المورد</th>
                                <th>الرصيد الافتتاحي</th>
                                <th>نوع الرصيد</th>
                                <th>العملة</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="openingBalancesTableBody">
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                    <div class="mt-2">جاري تحميل البيانات...</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Modal -->
    <div class="modal fade" id="addEditModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">إضافة رصيد افتتاحي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="openingBalanceForm">
                        <input type="hidden" id="balanceId">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">المورد *</label>
                                <div class="supplier-search-container">
                                    <input type="text" id="supplierSearch" class="form-control form-control-modern"
                                           placeholder="ابحث عن المورد بالاسم أو الكود..." autocomplete="off">
                                    <input type="hidden" id="supplierId" required>
                                    <div id="supplierDropdown" class="supplier-dropdown" style="display: none;">
                                        <div class="supplier-dropdown-content">
                                            <!-- نتائج البحث ستظهر هنا -->
                                        </div>
                                    </div>
                                </div>
                                <small class="text-muted">اكتب اسم المورد أو كوده للبحث السريع</small>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">العملة *</label>
                                <select id="modalCurrencyCode" class="form-control form-control-modern" required>
                                    <option value="">اختر العملة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">سعر الصرف</label>
                                <input type="number" id="exchangeRate" class="form-control form-control-modern"
                                       value="1" step="0.0001" min="0">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">نوع الرصيد *</label>
                                <select id="balanceType" class="form-control form-control-modern" required>
                                    <option value="">اختر نوع الرصيد</option>
                                    <option value="DEBIT">مدين (لنا على المورد)</option>
                                    <option value="CREDIT">دائن (للمورد علينا)</option>
                                </select>
                                <small class="text-muted">مدين = مبلغ مستحق لنا، دائن = مبلغ مستحق للمورد</small>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">مبلغ الرصيد الافتتاحي *</label>
                                <input type="number" id="openingBalanceAmount" class="form-control form-control-modern"
                                       required step="0.01" min="0.01">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">المستند المرجعي</label>
                                <input type="text" id="referenceDocument" class="form-control form-control-modern"
                                       placeholder="رقم المستند أو المرجع">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <label class="form-label">ملاحظات</label>
                                <textarea id="notes" class="form-control form-control-modern" rows="3"
                                          placeholder="ملاحظات إضافية (اختياري)"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveOpeningBalance()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Import Excel Modal -->
    <div class="modal fade" id="importModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-file-excel me-2"></i>
                        استيراد الأرصدة الافتتاحية من Excel
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- خطوات الاستيراد -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="step-item active" id="step1">
                                    <div class="step-number">1</div>
                                    <div class="step-text">تحميل القالب</div>
                                </div>
                                <div class="step-line"></div>
                                <div class="step-item" id="step2">
                                    <div class="step-number">2</div>
                                    <div class="step-text">رفع الملف</div>
                                </div>
                                <div class="step-line"></div>
                                <div class="step-item" id="step3">
                                    <div class="step-number">3</div>
                                    <div class="step-text">المعاينة</div>
                                </div>
                                <div class="step-line"></div>
                                <div class="step-item" id="step4">
                                    <div class="step-number">4</div>
                                    <div class="step-text">التأكيد</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- محتوى الخطوات -->
                    <div id="importStep1" class="import-step">
                        <div class="text-center">
                            <i class="fas fa-download fa-4x text-primary mb-3"></i>
                            <h4>الخطوة الأولى: تحميل قالب Excel</h4>
                            <p class="text-muted">قم بتحميل قالب Excel المحدد مسبقاً وملء البيانات</p>
                            <button class="btn btn-primary btn-lg" onclick="downloadTemplate()">
                                <i class="fas fa-download me-2"></i>
                                تحميل القالب
                            </button>
                        </div>
                    </div>

                    <div id="importStep2" class="import-step" style="display: none;">
                        <div class="text-center">
                            <i class="fas fa-upload fa-4x text-success mb-3"></i>
                            <h4>الخطوة الثانية: رفع ملف Excel</h4>
                            <p class="text-muted">اختر ملف Excel المملوء بالبيانات</p>

                            <div class="upload-area" id="uploadArea">
                                <i class="fas fa-cloud-upload-alt fa-3x mb-3"></i>
                                <p>اسحب الملف هنا أو انقر للاختيار</p>
                                <input type="file" id="excelFile" accept=".xlsx,.xls" style="display: none;">
                                <button class="btn btn-outline-primary" onclick="document.getElementById('excelFile').click()">
                                    اختيار ملف
                                </button>
                            </div>

                            <div id="uploadProgress" style="display: none;">
                                <div class="progress mt-3">
                                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <p class="mt-2">جاري رفع الملف...</p>
                            </div>
                        </div>
                    </div>

                    <div id="importStep3" class="import-step" style="display: none;">
                        <h4>الخطوة الثالثة: معاينة البيانات</h4>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="text-success" id="validCount">0</h5>
                                        <small>سجلات صحيحة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="text-danger" id="invalidCount">0</h5>
                                        <small>سجلات خاطئة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="text-info" id="totalCount">0</h5>
                                        <small>إجمالي السجلات</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive" style="max-height: 300px;">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>كود المورد</th>
                                        <th>اسم المورد</th>
                                        <th>العملة</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody id="previewTableBody">
                                </tbody>
                            </table>
                        </div>

                        <div class="text-center mt-3">
                            <button class="btn btn-warning me-2" onclick="downloadValidationReport()" id="downloadReportBtn" style="display: none;">
                                <i class="fas fa-file-excel me-2"></i>
                                تحميل تقرير الأخطاء
                            </button>
                        </div>
                    </div>

                    <div id="importStep4" class="import-step" style="display: none;">
                        <div class="text-center">
                            <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                            <h4>الخطوة الرابعة: تأكيد الاستيراد</h4>
                            <p class="text-muted">هل أنت متأكد من استيراد البيانات الصحيحة؟</p>

                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                سيتم استيراد السجلات الصحيحة فقط. لا يمكن التراجع عن هذه العملية.
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="nextStepBtn" onclick="nextImportStep()">التالي</button>
                    <button type="button" class="btn btn-success" id="confirmImportBtn" onclick="confirmImport()" style="display: none;">
                        تأكيد الاستيراد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <script>
        // متغيرات عامة
        let currentData = {
            suppliers: [],
            openingBalances: [],
            summary: {},
            currencies: []
        };

        let isFirstLoad = true;

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
        });

        // تحميل البيانات
        async function loadData() {
            showLoading(true);

            try {
                // تحميل العملات فقط في التحميل الأول
                if (currentData.currencies.length === 0) {
                    await loadCurrencies();
                }

                // تحميل الموردين فقط في التحميل الأول
                if (currentData.suppliers.length === 0) {
                    await loadSuppliers();
                }

                // تحميل الأرصدة الافتتاحية
                await loadOpeningBalances();

                // تحميل الملخص
                await loadSummary();

                // تحديث حالة الأزرار
                updateButtonStates(currentData.openingBalances);

            } catch (error) {
                console.error('خطأ في تحميل البيانات:', error);
                showAlert('حدث خطأ في تحميل البيانات', 'error');
            } finally {
                showLoading(false);
            }
        }

        // تحميل العملات
        async function loadCurrencies() {
            try {
                const response = await fetch('/suppliers/opening_balances/api/currencies');
                const data = await response.json();

                if (data.success) {
                    currentData.currencies = data.currencies;
                    updateCurrencyDropdowns();
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('خطأ في تحميل العملات:', error);
                // استخدام العملات الافتراضية في حالة الخطأ
                currentData.currencies = [
                    {code: 'SAR', name_ar: 'ريال سعودي', symbol: 'ر.س', is_base_currency: true},
                    {code: 'USD', name_ar: 'دولار أمريكي', symbol: '$', is_base_currency: false},
                    {code: 'EUR', name_ar: 'يورو', symbol: '€', is_base_currency: false}
                ];
                updateCurrencyDropdowns();
            }
        }

        // تحديث قوائم العملات
        function updateCurrencyDropdowns() {
            const filterDropdown = document.getElementById('currencyCode');
            const modalDropdown = document.getElementById('modalCurrencyCode');

            // حفظ القيمة المختارة حالياً
            const currentFilterValue = filterDropdown.value;
            const currentModalValue = modalDropdown.value;

            // تحديث فلتر العملة
            filterDropdown.innerHTML = '';
            modalDropdown.innerHTML = '<option value="">اختر العملة</option>';

            // إضافة خيار "جميع العملات" للفلتر
            filterDropdown.innerHTML = '<option value="">جميع العملات</option>';

            currentData.currencies.forEach(currency => {
                const optionText = `${currency.name_ar} (${currency.code})`;

                // إضافة للفلتر
                const filterOption = document.createElement('option');
                filterOption.value = currency.code;
                filterOption.textContent = optionText;
                filterDropdown.appendChild(filterOption);

                // إضافة للنموذج
                const modalOption = document.createElement('option');
                modalOption.value = currency.code;
                modalOption.textContent = optionText;
                modalOption.dataset.symbol = currency.symbol;
                modalOption.dataset.exchangeRate = currency.exchange_rate;
                modalDropdown.appendChild(modalOption);
            });

            // استعادة القيم المختارة أو الاحتفاظ بـ "جميع العملات" كافتراضي
            if (currentFilterValue) {
                filterDropdown.value = currentFilterValue;
            } else {
                // الاحتفاظ بـ "جميع العملات" كافتراضي
                filterDropdown.value = '';
            }

            if (currentModalValue) {
                modalDropdown.value = currentModalValue;
            } else if (isFirstLoad) {
                // تعيين العملة الأساسية للنموذج فقط في التحميل الأول
                const baseCurrency = currentData.currencies.find(c => c.is_base_currency);
                if (baseCurrency) {
                    modalDropdown.value = baseCurrency.code;
                }
                isFirstLoad = false;
            }
        }

        // تحميل قائمة الموردين
        async function loadSuppliers() {
            try {
                const response = await fetch('/suppliers/opening_balances/api/suppliers');
                const data = await response.json();

                if (data.success) {
                    currentData.suppliers = data.suppliers;
                    updateSuppliersDropdown();
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('خطأ في تحميل الموردين:', error);
                throw error;
            }
        }

        // تحميل الأرصدة الافتتاحية
        async function loadOpeningBalances() {
            try {
                const supplierFilterId = document.getElementById('supplierFilterId').value;
                const currencyCode = document.getElementById('currencyCode').value;
                const statusFilter = document.getElementById('statusFilter').value;

                const params = new URLSearchParams({
                    fiscal_date: '2024-01-01', // تاريخ ثابت للفترة المحاسبية
                    currency_code: currencyCode,
                    status: statusFilter
                });

                // إضافة فلتر المورد إذا كان محدداً
                if (supplierFilterId) {
                    params.append('supplier_id', supplierFilterId);
                }

                const response = await fetch(`/suppliers/opening_balances/api/opening_balances?${params}`);
                const data = await response.json();

                if (data.success) {
                    currentData.openingBalances = data.opening_balances;
                    updateOpeningBalancesTable();
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('خطأ في تحميل الأرصدة الافتتاحية:', error);
                throw error;
            }
        }

        // تحميل الملخص
        async function loadSummary() {
            try {
                const supplierFilterId = document.getElementById('supplierFilterId').value;
                const currencyCode = document.getElementById('currencyCode').value;

                const params = new URLSearchParams({
                    fiscal_date: '2024-01-01', // تاريخ ثابت للفترة المحاسبية
                    currency_code: currencyCode
                });

                // إضافة فلتر المورد إذا كان محدداً
                if (supplierFilterId) {
                    params.append('supplier_id', supplierFilterId);
                }

                const response = await fetch(`/suppliers/opening_balances/api/summary?${params}`);
                const data = await response.json();

                if (data.success) {
                    currentData.summary = data.summary;
                    updateSummaryCards();
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('خطأ في تحميل الملخص:', error);
                throw error;
            }
        }

        // متغيرات البحث السريع
        let filteredSuppliers = [];
        let selectedSupplierIndex = -1;

        // تحديث قائمة الموردين في النموذج
        function updateSuppliersDropdown() {
            // لا نحتاج لتحديث dropdown تقليدي بعد الآن
            // البيانات ستكون متاحة في currentData.suppliers للبحث السريع
            filteredSuppliers = currentData.suppliers;
        }

        // البحث السريع في الموردين
        function searchSuppliers(searchTerm) {
            if (!searchTerm.trim()) {
                filteredSuppliers = currentData.suppliers.slice(0, 50); // أول 50 مورد
            } else {
                const term = searchTerm.toLowerCase().trim();
                filteredSuppliers = currentData.suppliers.filter(supplier =>
                    supplier.name.toLowerCase().includes(term) ||
                    supplier.code.toLowerCase().includes(term)
                ).slice(0, 20); // أول 20 نتيجة
            }

            updateSupplierDropdown();
        }

        // تحديث قائمة نتائج البحث
        function updateSupplierDropdown() {
            const dropdown = document.getElementById('supplierDropdown');
            const content = dropdown.querySelector('.supplier-dropdown-content');

            content.innerHTML = '';
            selectedSupplierIndex = -1;

            if (filteredSuppliers.length === 0) {
                content.innerHTML = '<div class="no-results">لا توجد نتائج</div>';
            } else {
                filteredSuppliers.forEach((supplier, index) => {
                    const item = document.createElement('div');
                    item.className = 'supplier-item';
                    item.dataset.index = index;
                    item.innerHTML = `
                        <div class="supplier-name">${supplier.name}</div>
                        <div class="supplier-code">كود: ${supplier.code}</div>
                    `;

                    item.addEventListener('click', () => selectSupplier(supplier));
                    content.appendChild(item);
                });
            }

            dropdown.style.display = 'block';
        }

        // اختيار مورد
        function selectSupplier(supplier) {
            document.getElementById('supplierSearch').value = `${supplier.name} (${supplier.code})`;
            document.getElementById('supplierId').value = supplier.id;
            document.getElementById('supplierDropdown').style.display = 'none';

            // إضافة تأثير بصري للتأكيد
            const searchInput = document.getElementById('supplierSearch');
            searchInput.style.borderColor = 'var(--success)';
            searchInput.classList.remove('is-invalid');
            setTimeout(() => {
                searchInput.style.borderColor = '';
            }, 1000);
        }

        // البحث في الموردين للفلتر
        function searchSuppliersFilter() {
            const searchTerm = document.getElementById('supplierFilterSearch').value.toLowerCase();
            const dropdown = document.getElementById('supplierFilterDropdown');

            if (searchTerm.length < 2) {
                dropdown.style.display = 'none';
                return;
            }

            const filteredSuppliers = currentData.suppliers.filter(supplier =>
                supplier.name.toLowerCase().includes(searchTerm) ||
                supplier.code.toLowerCase().includes(searchTerm)
            );

            dropdown.innerHTML = '';

            if (filteredSuppliers.length === 0) {
                dropdown.innerHTML = '<div class="dropdown-item-text no-results">لا توجد نتائج</div>';
            } else {
                filteredSuppliers.slice(0, 10).forEach(supplier => {
                    const item = document.createElement('div');
                    item.className = 'dropdown-item';
                    item.innerHTML = `
                        <div class="supplier-item">
                            <div class="supplier-name">${supplier.name}</div>
                            <div class="supplier-code">${supplier.code}</div>
                        </div>
                    `;
                    item.onclick = () => selectSupplierFilter(supplier);
                    dropdown.appendChild(item);
                });
            }

            dropdown.style.display = 'block';
        }

        // اختيار مورد للفلتر
        function selectSupplierFilter(supplier) {
            document.getElementById('supplierFilterSearch').value = `${supplier.name} (${supplier.code})`;
            document.getElementById('supplierFilterId').value = supplier.id;
            document.getElementById('supplierFilterDropdown').style.display = 'none';

            // إظهار زر مسح الفلتر
            document.getElementById('clearSupplierFilter').style.display = 'inline-block';

            // تحديث البيانات فوراً
            refreshData();

            // إضافة تأثير بصري للتأكيد
            const searchInput = document.getElementById('supplierFilterSearch');
            searchInput.style.borderColor = 'var(--success)';
            setTimeout(() => {
                searchInput.style.borderColor = '';
            }, 1000);
        }

        // مسح فلتر المورد
        function clearSupplierFilter() {
            document.getElementById('supplierFilterSearch').value = '';
            document.getElementById('supplierFilterId').value = '';
            document.getElementById('supplierFilterDropdown').style.display = 'none';

            // إخفاء زر مسح الفلتر
            document.getElementById('clearSupplierFilter').style.display = 'none';

            // تحديث البيانات فوراً
            refreshData();

            // إضافة تأثير بصري للتأكيد
            const searchInput = document.getElementById('supplierFilterSearch');
            searchInput.style.borderColor = 'var(--info)';
            setTimeout(() => {
                searchInput.style.borderColor = '';
            }, 1000);
        }

        // إعادة تعيين الفلاتر
        function resetFilters() {
            // مسح فلتر المورد
            clearSupplierFilter();

            // إعادة تعيين باقي الفلاتر
            document.getElementById('currencyCode').value = '';
            document.getElementById('statusFilter').value = 'all';

            // تحديث البيانات
            refreshData();
        }

        // إخفاء القائمة
        function hideSupplierDropdown() {
            setTimeout(() => {
                document.getElementById('supplierDropdown').style.display = 'none';
            }, 200);
        }

        // التنقل بالكيبورد
        function navigateSuppliers(direction) {
            const items = document.querySelectorAll('.supplier-item');
            if (items.length === 0) return;

            // إزالة التحديد السابق
            if (selectedSupplierIndex >= 0) {
                items[selectedSupplierIndex].classList.remove('selected');
            }

            // تحديث الفهرس
            if (direction === 'down') {
                selectedSupplierIndex = Math.min(selectedSupplierIndex + 1, items.length - 1);
            } else if (direction === 'up') {
                selectedSupplierIndex = Math.max(selectedSupplierIndex - 1, 0);
            }

            // إضافة التحديد الجديد
            if (selectedSupplierIndex >= 0) {
                items[selectedSupplierIndex].classList.add('selected');
                items[selectedSupplierIndex].scrollIntoView({ block: 'nearest' });
            }
        }

        // اختيار المورد المحدد بـ Enter
        function selectCurrentSupplier() {
            if (selectedSupplierIndex >= 0 && filteredSuppliers[selectedSupplierIndex]) {
                selectSupplier(filteredSuppliers[selectedSupplierIndex]);
            }
        }

        // إنشاء أزرار الإجراءات حسب حالة الرصيد
        function getActionButtons(balance) {
            let buttons = '';

            // أزرار الحالة (اعتماد/ترحيل) أولاً
            if (balance.status === 'DRAFT') {
                // مسودة: زر اعتماد
                buttons += `
                    <button class="btn btn-sm btn-warning" onclick="approveBalance(${balance.id})" title="اعتماد">
                        <i class="fas fa-check"></i>
                    </button>
                `;
            } else if (balance.status === 'APPROVED') {
                // معتمد: زر إلغاء اعتماد + زر ترحيل
                buttons += `
                    <button class="btn btn-sm btn-secondary" onclick="unapproveBalance(${balance.id})" title="إلغاء الاعتماد">
                        <i class="fas fa-undo"></i>
                    </button>
                    <button class="btn btn-sm btn-info" onclick="postBalance(${balance.id})" title="ترحيل">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                `;
            } else if (balance.status === 'POSTED') {
                // مرحل: زر إلغاء ترحيل
                buttons += `
                    <button class="btn btn-sm btn-secondary" onclick="unpostBalance(${balance.id})" title="إلغاء الترحيل">
                        <i class="fas fa-undo"></i>
                    </button>
                `;
            }

            // أزرار التحرير والحذف (للمسودة فقط)
            if (balance.status === 'DRAFT') {
                buttons += `
                    <button class="btn btn-sm btn-outline-primary" onclick="editBalance(${balance.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteBalance(${balance.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
            } else {
                // للحالات الأخرى: زر عرض فقط
                buttons += `
                    <button class="btn btn-sm btn-outline-info" onclick="viewBalance(${balance.id})" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                `;
            }

            return buttons;
        }

        // تحديث جدول الأرصدة الافتتاحية
        function updateOpeningBalancesTable() {
            const tbody = document.getElementById('openingBalancesTableBody');

            if (currentData.openingBalances.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <div>لا توجد أرصدة افتتاحية</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = '';

            currentData.openingBalances.forEach(balance => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${balance.supplier_name}</td>
                    <td><code>${balance.supplier_code}</code></td>
                    <td class="text-end fw-bold">${formatNumber(balance.opening_balance_amount)} ${balance.currency_code}</td>
                    <td>
                        <span class="badge ${balance.balance_type === 'DEBIT' ? 'bg-success' : 'bg-warning'}">
                            ${balance.balance_type === 'DEBIT' ? 'مدين' : 'دائن'}
                        </span>
                    </td>
                    <td>${balance.currency_code}</td>
                    <td>
                        <span class="status-badge status-${balance.status.toLowerCase()}">
                            ${getStatusText(balance.status)}
                        </span>
                    </td>
                    <td>${formatDate(balance.created_date)}</td>
                    <td>
                        <div class="d-flex gap-1 justify-content-center flex-wrap">
                            ${getActionButtons(balance)}
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // تحديث بطاقات الملخص
        function updateSummaryCards() {
            const summary = currentData.summary;

            animateCounter('totalSuppliers', summary.total_suppliers || 0);
            animateCounter('totalDebit', summary.total_debit_amount || 0);
            animateCounter('totalCredit', summary.total_credit_amount || 0);
            animateCounter('netBalance', summary.net_balance || 0);
        }

        // تأثير العد التصاعدي
        function animateCounter(elementId, targetValue) {
            const element = document.getElementById(elementId);
            const startValue = 0;
            const duration = 1500;
            const startTime = performance.now();

            function updateCounter(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const currentValue = Math.floor(startValue + (targetValue - startValue) * easeOutQuart);

                if (elementId.includes('total') || elementId.includes('net')) {
                    element.textContent = formatNumber(currentValue);
                } else {
                    element.textContent = currentValue.toLocaleString('en-US');
                }

                if (progress < 1) {
                    requestAnimationFrame(updateCounter);
                }
            }

            requestAnimationFrame(updateCounter);
        }

        // عرض نموذج الإضافة
        function showAddModal() {
            document.getElementById('modalTitle').textContent = 'إضافة رصيد افتتاحي';
            document.getElementById('openingBalanceForm').reset();
            document.getElementById('balanceId').value = '';
            document.getElementById('modalCurrencyCode').value = document.getElementById('currencyCode').value;

            // تنظيف البحث السريع
            document.getElementById('supplierSearch').value = '';
            document.getElementById('supplierId').value = '';
            document.getElementById('supplierDropdown').style.display = 'none';

            const modal = new bootstrap.Modal(document.getElementById('addEditModal'));
            modal.show();
        }

        // تعديل رصيد
        function editBalance(balanceId) {
            const balance = currentData.openingBalances.find(b => b.id === balanceId);
            if (!balance) return;

            document.getElementById('modalTitle').textContent = 'تعديل رصيد افتتاحي';
            document.getElementById('balanceId').value = balance.id;

            // تعيين المورد في البحث السريع
            const supplier = currentData.suppliers.find(s => s.id === balance.supplier_id);
            if (supplier) {
                document.getElementById('supplierSearch').value = `${supplier.name} (${supplier.code})`;
                document.getElementById('supplierId').value = supplier.id;
            }

            document.getElementById('modalCurrencyCode').value = balance.currency_code;
            document.getElementById('exchangeRate').value = balance.exchange_rate;
            document.getElementById('balanceType').value = balance.balance_type || 'DEBIT';
            document.getElementById('openingBalanceAmount').value = Math.abs(balance.opening_balance_amount);
            document.getElementById('referenceDocument').value = balance.reference_document;
            document.getElementById('notes').value = balance.notes;
            document.getElementById('supplierDropdown').style.display = 'none';

            const modal = new bootstrap.Modal(document.getElementById('addEditModal'));
            modal.show();
        }

        // حفظ الرصيد الافتتاحي
        async function saveOpeningBalance() {
            try {
                const supplierIdValue = document.getElementById('supplierId').value;
                const balanceType = document.getElementById('balanceType').value;
                const amount = parseFloat(document.getElementById('openingBalanceAmount').value);
                const currencyCode = document.getElementById('modalCurrencyCode').value;

                // إزالة التأثيرات البصرية السابقة
                document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));

                // التحقق من البيانات الأساسية أولاً
                if (!supplierIdValue || supplierIdValue === '') {
                    document.getElementById('supplierSearch').classList.add('is-invalid');
                    showAlert('يرجى اختيار المورد من قائمة البحث', 'warning');
                    document.getElementById('supplierSearch').focus();
                    return;
                }

                if (!balanceType || balanceType === '') {
                    document.getElementById('balanceType').classList.add('is-invalid');
                    showAlert('يرجى اختيار نوع الرصيد (مدين أو دائن)', 'warning');
                    document.getElementById('balanceType').focus();
                    return;
                }

                if (!amount || amount <= 0 || isNaN(amount)) {
                    document.getElementById('openingBalanceAmount').classList.add('is-invalid');
                    showAlert('يرجى إدخال مبلغ صحيح أكبر من صفر', 'warning');
                    document.getElementById('openingBalanceAmount').focus();
                    return;
                }

                if (!currencyCode || currencyCode === '') {
                    document.getElementById('modalCurrencyCode').classList.add('is-invalid');
                    showAlert('يرجى اختيار العملة', 'warning');
                    document.getElementById('modalCurrencyCode').focus();
                    return;
                }

                // تحويل المبلغ حسب نوع الرصيد
                const finalAmount = balanceType === 'CREDIT' ? -Math.abs(amount) : Math.abs(amount);

                const formData = {
                    supplier_id: parseInt(supplierIdValue),
                    fiscal_date: '2024-01-01', // تاريخ ثابت للفترة المحاسبية
                    currency_code: currencyCode,
                    exchange_rate: parseFloat(document.getElementById('exchangeRate').value) || 1,
                    opening_balance_amount: finalAmount,
                    balance_type: balanceType,
                    reference_document: document.getElementById('referenceDocument').value,
                    notes: document.getElementById('notes').value
                };

                const balanceId = document.getElementById('balanceId').value;
                if (balanceId) {
                    formData.id = parseInt(balanceId);
                }

                const response = await fetch('/suppliers/opening_balances/api/save_opening_balance', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const data = await response.json();

                if (data.success) {
                    showAlert(data.message, 'success');
                    bootstrap.Modal.getInstance(document.getElementById('addEditModal')).hide();
                    await loadData();
                } else {
                    showAlert(data.message, 'error');
                }

            } catch (error) {
                console.error('خطأ في حفظ الرصيد:', error);
                showAlert('حدث خطأ في حفظ البيانات', 'error');
            }
        }

        // حذف رصيد
        async function deleteBalance(balanceId) {
            if (!confirm('هل أنت متأكد من حذف هذا الرصيد الافتتاحي؟')) {
                return;
            }

            try {
                const response = await fetch(`/suppliers/opening_balances/api/delete_opening_balance/${balanceId}`, {
                    method: 'DELETE'
                });

                const data = await response.json();

                if (data.success) {
                    showAlert(data.message, 'success');
                    await loadData();
                } else {
                    showAlert(data.message, 'error');
                }

            } catch (error) {
                console.error('خطأ في حذف الرصيد:', error);
                showAlert('حدث خطأ في حذف البيانات', 'error');
            }
        }

        // متغيرات لحالة الأزرار
        let currentApprovalAction = 'approve'; // approve أو unapprove
        let currentPostingAction = 'post'; // post أو unpost

        // تحديث حالة الأزرار حسب البيانات
        function updateButtonStates(data) {
            const approveBtn = document.getElementById('approveBtn');
            const approveIcon = document.getElementById('approveIcon');
            const approveText = document.getElementById('approveText');
            const postBtn = document.getElementById('postBtn');
            const postIcon = document.getElementById('postIcon');
            const postText = document.getElementById('postText');

            // تحليل حالات الأرصدة
            let hasDraft = false;
            let hasApproved = false;
            let hasPosted = false;

            if (data && data.length > 0) {
                data.forEach(item => {
                    if (item.status === 'DRAFT') hasDraft = true;
                    else if (item.status === 'APPROVED') hasApproved = true;
                    else if (item.status === 'POSTED') hasPosted = true;
                });
            }

            // تحديث زر الاعتماد
            if (hasApproved && !hasDraft) {
                // يوجد معتمد فقط - إظهار إلغاء الاعتماد
                currentApprovalAction = 'unapprove';
                approveBtn.className = 'btn btn-secondary btn-modern';
                approveIcon.className = 'fas fa-undo me-2';
                approveText.textContent = 'إلغاء الاعتماد';
            } else {
                // يوجد مسودة أو خليط - إظهار الاعتماد
                currentApprovalAction = 'approve';
                approveBtn.className = 'btn btn-warning btn-modern';
                approveIcon.className = 'fas fa-check me-2';
                approveText.textContent = 'اعتماد الأرصدة';
            }

            // تحديث زر الترحيل
            if (hasPosted && !hasApproved) {
                // يوجد مرحل فقط - إظهار إلغاء الترحيل
                currentPostingAction = 'unpost';
                postBtn.className = 'btn btn-secondary btn-modern';
                postIcon.className = 'fas fa-undo me-2';
                postText.textContent = 'إلغاء الترحيل';
            } else {
                // يوجد معتمد أو خليط - إظهار الترحيل
                currentPostingAction = 'post';
                postBtn.className = 'btn btn-info btn-modern';
                postIcon.className = 'fas fa-paper-plane me-2';
                postText.textContent = 'ترحيل الأرصدة';
            }

            // تعطيل الأزرار حسب الحالة
            approveBtn.disabled = (!hasDraft && !hasApproved);
            postBtn.disabled = (!hasApproved && !hasPosted);
        }

        // معالج الاعتماد الذكي
        async function handleApprovalAction() {
            const actionText = currentApprovalAction === 'approve' ? 'اعتماد' : 'إلغاء اعتماد';

            // التحقق من وجود فلتر مورد
            const supplierFilterId = document.getElementById('supplierFilterId').value;
            const supplierFilterName = document.getElementById('supplierFilterSearch').value;

            let confirmMessage = `هل أنت متأكد من ${actionText} الأرصدة`;
            if (supplierFilterId) {
                confirmMessage += ` للمورد: ${supplierFilterName}`;
            } else {
                confirmMessage += ' لجميع الموردين';
            }
            confirmMessage += '؟';

            const notes = prompt(`ملاحظات ${actionText} (اختياري):`);
            if (notes === null) return;

            // تأكيد إضافي مع توضيح النطاق
            if (!confirm(confirmMessage)) {
                return;
            }

            try {
                const response = await fetch('/suppliers/opening_balances/api/approve_opening_balances', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        fiscal_date: '2024-01-01',
                        currency_code: document.getElementById('currencyCode').value,
                        supplier_id: supplierFilterId,
                        approval_notes: notes,
                        action: currentApprovalAction
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showAlert(data.message, 'success');
                    await loadData();
                } else {
                    showAlert(data.message, 'error');
                }

            } catch (error) {
                console.error(`خطأ في ${actionText} الأرصدة:`, error);
                showAlert(`حدث خطأ في ${actionText} البيانات`, 'error');
            }
        }

        // الدالة القديمة للتوافق
        async function approveBalances() {
            await handleApprovalAction();
        }

        // معالج الترحيل الذكي
        async function handlePostingAction() {
            const actionText = currentPostingAction === 'post' ? 'ترحيل' : 'إلغاء ترحيل';

            // التحقق من وجود فلتر مورد
            const supplierFilterId = document.getElementById('supplierFilterId').value;
            const supplierFilterName = document.getElementById('supplierFilterSearch').value;

            const reference = prompt(`مرجع ${actionText} (اختياري):`);
            if (reference === null) return;

            // تأكيدات مختلفة حسب العملية مع توضيح النطاق
            let confirmMessage;
            if (currentPostingAction === 'post') {
                confirmMessage = `هل أنت متأكد من ترحيل الأرصدة الافتتاحية`;
                if (supplierFilterId) {
                    confirmMessage += ` للمورد: ${supplierFilterName}`;
                } else {
                    confirmMessage += ' لجميع الموردين';
                }
                confirmMessage += '؟ سيتم إنشاء قيود محاسبية.';
            } else {
                confirmMessage = `هل أنت متأكد من إلغاء ترحيل الأرصدة`;
                if (supplierFilterId) {
                    confirmMessage += ` للمورد: ${supplierFilterName}`;
                } else {
                    confirmMessage += ' لجميع الموردين';
                }
                confirmMessage += '؟ سيتم حذف القيود المحاسبية المرتبطة.';
            }

            if (!confirm(confirmMessage)) {
                return;
            }

            // تأكيد إضافي لإلغاء الترحيل
            if (currentPostingAction === 'unpost') {
                if (!confirm('تأكيد نهائي: سيتم إلغاء جميع القيود المحاسبية المرتبطة. هل تريد المتابعة؟')) {
                    return;
                }
            }

            try {
                const response = await fetch('/suppliers/opening_balances/api/post_opening_balances', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        fiscal_date: '2024-01-01',
                        currency_code: document.getElementById('currencyCode').value,
                        supplier_id: supplierFilterId,
                        posting_reference: reference,
                        action: currentPostingAction
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showAlert(data.message, 'success');
                    await loadData();
                } else {
                    showAlert(data.message, 'error');
                }

            } catch (error) {
                console.error(`خطأ في ${actionText} الأرصدة:`, error);
                showAlert(`حدث خطأ في ${actionText} البيانات`, 'error');
            }
        }

        // الدالة القديمة للتوافق
        async function postBalances() {
            await handlePostingAction();
        }

        // === دوال العمليات على الأرصدة الفردية ===

        // اعتماد رصيد واحد
        async function approveBalance(balanceId) {
            const notes = prompt('ملاحظات الاعتماد (اختياري):');
            if (notes === null) return;

            try {
                const response = await fetch('/suppliers/opening_balances/api/approve_opening_balances', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        fiscal_date: '2024-01-01',
                        balance_id: balanceId,
                        approval_notes: notes,
                        action: 'approve'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showAlert(data.message, 'success');
                    await loadData();
                } else {
                    showAlert(data.message, 'error');
                }

            } catch (error) {
                console.error('خطأ في اعتماد الرصيد:', error);
                showAlert('حدث خطأ في اعتماد الرصيد', 'error');
            }
        }

        // إلغاء اعتماد رصيد واحد
        async function unapproveBalance(balanceId) {
            const notes = prompt('ملاحظات إلغاء الاعتماد (اختياري):');
            if (notes === null) return;

            if (!confirm('هل أنت متأكد من إلغاء اعتماد هذا الرصيد؟')) {
                return;
            }

            try {
                const response = await fetch('/suppliers/opening_balances/api/approve_opening_balances', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        fiscal_date: '2024-01-01',
                        balance_id: balanceId,
                        approval_notes: notes,
                        action: 'unapprove'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showAlert(data.message, 'success');
                    await loadData();
                } else {
                    showAlert(data.message, 'error');
                }

            } catch (error) {
                console.error('خطأ في إلغاء اعتماد الرصيد:', error);
                showAlert('حدث خطأ في إلغاء اعتماد الرصيد', 'error');
            }
        }

        // ترحيل رصيد واحد
        async function postBalance(balanceId) {
            const reference = prompt('مرجع الترحيل (اختياري):');
            if (reference === null) return;

            if (!confirm('هل أنت متأكد من ترحيل هذا الرصيد؟ سيتم إنشاء قيود محاسبية.')) {
                return;
            }

            try {
                const response = await fetch('/suppliers/opening_balances/api/post_opening_balances', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        fiscal_date: '2024-01-01',
                        balance_id: balanceId,
                        posting_reference: reference,
                        action: 'post'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showAlert(data.message, 'success');
                    await loadData();
                } else {
                    showAlert(data.message, 'error');
                }

            } catch (error) {
                console.error('خطأ في ترحيل الرصيد:', error);
                showAlert('حدث خطأ في ترحيل الرصيد', 'error');
            }
        }

        // إلغاء ترحيل رصيد واحد
        async function unpostBalance(balanceId) {
            const reference = prompt('مرجع إلغاء الترحيل (اختياري):');
            if (reference === null) return;

            if (!confirm('هل أنت متأكد من إلغاء ترحيل هذا الرصيد؟ سيتم حذف القيود المحاسبية المرتبطة.')) {
                return;
            }

            if (!confirm('تأكيد نهائي: سيتم إلغاء جميع القيود المحاسبية المرتبطة. هل تريد المتابعة؟')) {
                return;
            }

            try {
                const response = await fetch('/suppliers/opening_balances/api/post_opening_balances', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        fiscal_date: '2024-01-01',
                        balance_id: balanceId,
                        posting_reference: reference,
                        action: 'unpost'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showAlert(data.message, 'success');
                    await loadData();
                } else {
                    showAlert(data.message, 'error');
                }

            } catch (error) {
                console.error('خطأ في إلغاء ترحيل الرصيد:', error);
                showAlert('حدث خطأ في إلغاء ترحيل الرصيد', 'error');
            }
        }

        // عرض تفاصيل رصيد
        function viewBalance(balanceId) {
            // يمكن إضافة مودال لعرض تفاصيل الرصيد
            showAlert('ميزة عرض التفاصيل ستتوفر قريباً', 'info');
        }

        // دوال مساعدة
        function showLoading(show) {
            document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
        }

        function showAlert(message, type) {
            alert(message);
        }

        function formatNumber(number) {
            return parseFloat(number).toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-GB', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        }

        function getStatusText(status) {
            const statusMap = {
                'DRAFT': 'مسودة',
                'APPROVED': 'معتمد',
                'POSTED': 'مرحل'
            };
            return statusMap[status] || status;
        }

        function exportToExcel() {
            showAlert('ميزة التصدير قيد التطوير', 'info');
        }

        function printTable() {
            window.print();
        }

        // متغيرات الاستيراد
        let currentImportStep = 1;
        let importData = null;

        // عرض نموذج الاستيراد
        function showImportModal() {
            currentImportStep = 1;
            updateImportStep();
            const modal = new bootstrap.Modal(document.getElementById('importModal'));
            modal.show();
        }

        // تحديث خطوة الاستيراد
        function updateImportStep() {
            // إخفاء جميع الخطوات
            for (let i = 1; i <= 4; i++) {
                document.getElementById(`importStep${i}`).style.display = 'none';
                document.getElementById(`step${i}`).classList.remove('active', 'completed');
            }

            // إظهار الخطوة الحالية
            document.getElementById(`importStep${currentImportStep}`).style.display = 'block';
            document.getElementById(`step${currentImportStep}`).classList.add('active');

            // تحديث الخطوات المكتملة
            for (let i = 1; i < currentImportStep; i++) {
                document.getElementById(`step${i}`).classList.add('completed');
            }

            // تحديث الأزرار
            const nextBtn = document.getElementById('nextStepBtn');
            const confirmBtn = document.getElementById('confirmImportBtn');

            if (currentImportStep === 4) {
                nextBtn.style.display = 'none';
                confirmBtn.style.display = 'inline-block';
            } else {
                nextBtn.style.display = 'inline-block';
                confirmBtn.style.display = 'none';

                if (currentImportStep === 1) {
                    nextBtn.textContent = 'التالي';
                    nextBtn.disabled = false;
                } else if (currentImportStep === 2) {
                    nextBtn.textContent = 'رفع الملف';
                    nextBtn.disabled = !document.getElementById('excelFile').files.length;
                } else if (currentImportStep === 3) {
                    nextBtn.textContent = 'التالي';
                    nextBtn.disabled = !importData || importData.valid_count === 0;
                }
            }
        }

        // الانتقال للخطوة التالية
        function nextImportStep() {
            if (currentImportStep === 2) {
                uploadExcelFile();
            } else if (currentImportStep === 3) {
                currentImportStep++;
                updateImportStep();
            } else {
                currentImportStep++;
                updateImportStep();
            }
        }

        // تحميل قالب Excel
        async function downloadTemplate() {
            try {
                const response = await fetch('/suppliers/opening_balances/api/download_template');

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `قالب_الأرصدة_الافتتاحية_${new Date().toISOString().split('T')[0]}.xlsx`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    showAlert('تم تحميل القالب بنجاح', 'success');
                    currentImportStep = 2;
                    updateImportStep();
                } else {
                    throw new Error('فشل في تحميل القالب');
                }
            } catch (error) {
                console.error('خطأ في تحميل القالب:', error);
                showAlert('حدث خطأ في تحميل القالب', 'error');
            }
        }

        // رفع ملف Excel
        async function uploadExcelFile() {
            const fileInput = document.getElementById('excelFile');
            const file = fileInput.files[0];

            if (!file) {
                showAlert('يرجى اختيار ملف Excel', 'warning');
                return;
            }

            try {
                showUploadProgress(true);

                const formData = new FormData();
                formData.append('file', file);
                formData.append('fiscal_date', '2024-01-01'); // تاريخ ثابت للفترة المحاسبية

                const response = await fetch('/suppliers/opening_balances/api/upload_excel', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    importData = data;
                    showAlert(data.message, 'success');
                    currentImportStep = 3;
                    updateImportStep();
                    await loadPreviewData();
                } else {
                    showAlert(data.message, 'error');
                }

            } catch (error) {
                console.error('خطأ في رفع الملف:', error);
                showAlert('حدث خطأ في رفع الملف', 'error');
            } finally {
                showUploadProgress(false);
            }
        }

        // تحميل بيانات المعاينة
        async function loadPreviewData() {
            try {
                const response = await fetch('/suppliers/opening_balances/api/preview_import');
                const data = await response.json();

                if (data.success) {
                    // تحديث الإحصائيات
                    document.getElementById('validCount').textContent = importData.valid_count;
                    document.getElementById('invalidCount').textContent = importData.invalid_count;
                    document.getElementById('totalCount').textContent = importData.total_count;

                    // تحديث جدول المعاينة
                    const tbody = document.getElementById('previewTableBody');
                    tbody.innerHTML = '';

                    // عرض السجلات الصحيحة
                    data.valid_records.forEach(record => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${record.supplier_code}</td>
                            <td>${record.supplier_name || 'غير محدد'}</td>
                            <td>${record.currency_code}</td>
                            <td class="text-end">${formatNumber(record.opening_balance_amount)}</td>
                            <td><span class="badge bg-success">صحيح</span></td>
                        `;
                        tbody.appendChild(row);
                    });

                    // عرض السجلات الخاطئة
                    data.invalid_records.forEach(invalid => {
                        const record = invalid.record;
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${record.supplier_code || ''}</td>
                            <td>${record.supplier_name || ''}</td>
                            <td>${record.currency_code || ''}</td>
                            <td class="text-end">${record.opening_balance_amount || ''}</td>
                            <td><span class="badge bg-danger" title="${invalid.errors.join(', ')}">خطأ</span></td>
                        `;
                        tbody.appendChild(row);
                    });

                    // إظهار زر تحميل التقرير إذا كان هناك أخطاء
                    if (importData.invalid_count > 0) {
                        document.getElementById('downloadReportBtn').style.display = 'inline-block';
                    }

                } else {
                    showAlert(data.message, 'error');
                }

            } catch (error) {
                console.error('خطأ في تحميل بيانات المعاينة:', error);
                showAlert('حدث خطأ في تحميل المعاينة', 'error');
            }
        }

        // تأكيد الاستيراد
        async function confirmImport() {
            try {
                showLoading(true);

                const response = await fetch('/suppliers/opening_balances/api/confirm_import', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    showAlert(data.message, 'success');
                    bootstrap.Modal.getInstance(document.getElementById('importModal')).hide();
                    await loadData();
                } else {
                    showAlert(data.message, 'error');
                }

            } catch (error) {
                console.error('خطأ في تأكيد الاستيراد:', error);
                showAlert('حدث خطأ في الاستيراد', 'error');
            } finally {
                showLoading(false);
            }
        }

        // تحميل تقرير التحقق
        async function downloadValidationReport() {
            try {
                const response = await fetch('/suppliers/opening_balances/api/download_validation_report');

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `تقرير_التحقق_${new Date().toISOString().split('T')[0]}.xlsx`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    showAlert('تم تحميل تقرير التحقق بنجاح', 'success');
                } else {
                    throw new Error('فشل في تحميل التقرير');
                }
            } catch (error) {
                console.error('خطأ في تحميل التقرير:', error);
                showAlert('حدث خطأ في تحميل التقرير', 'error');
            }
        }

        // إظهار تقدم الرفع
        function showUploadProgress(show) {
            document.getElementById('uploadProgress').style.display = show ? 'block' : 'none';
            document.getElementById('uploadArea').style.display = show ? 'none' : 'block';
        }

        // أحداث الملف
        document.getElementById('excelFile').addEventListener('change', function() {
            updateImportStep();
        });

        // أحداث السحب والإفلات
        const uploadArea = document.getElementById('uploadArea');

        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('excelFile').files = files;
                updateImportStep();
            }
        });

        // أحداث البحث السريع للموردين
        document.getElementById('supplierSearch').addEventListener('input', function() {
            searchSuppliers(this.value);
        });

        document.getElementById('supplierSearch').addEventListener('focus', function() {
            if (!this.value.trim()) {
                searchSuppliers(''); // إظهار أول 50 مورد
            } else {
                document.getElementById('supplierDropdown').style.display = 'block';
            }
        });

        document.getElementById('supplierSearch').addEventListener('blur', function() {
            hideSupplierDropdown();
        });

        document.getElementById('supplierSearch').addEventListener('keydown', function(e) {
            const dropdown = document.getElementById('supplierDropdown');
            if (dropdown.style.display === 'none') return;

            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    navigateSuppliers('down');
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    navigateSuppliers('up');
                    break;
                case 'Enter':
                    e.preventDefault();
                    selectCurrentSupplier();
                    break;
                case 'Escape':
                    e.preventDefault();
                    hideSupplierDropdown();
                    break;
            }
        });

        // تنظيف الحقول عند التغيير
        document.getElementById('balanceType').addEventListener('change', function() {
            this.classList.remove('is-invalid');
        });

        document.getElementById('openingBalanceAmount').addEventListener('input', function() {
            this.classList.remove('is-invalid');
        });

        document.getElementById('modalCurrencyCode').addEventListener('change', function() {
            this.classList.remove('is-invalid');
            const selectedOption = this.options[this.selectedIndex];
            const exchangeRate = selectedOption.dataset.exchangeRate || 1;
            document.getElementById('exchangeRate').value = parseFloat(exchangeRate).toFixed(4);
        });

        // دالة تحديث البيانات بدون إعادة تحميل العملات والموردين
        async function refreshData() {
            showLoading(true);

            try {
                // تحميل الأرصدة الافتتاحية
                await loadOpeningBalances();

                // تحميل الملخص
                await loadSummary();

            } catch (error) {
                console.error('خطأ في تحديث البيانات:', error);
                showAlert('حدث خطأ في تحديث البيانات', 'error');
            } finally {
                showLoading(false);
            }
        }

        // أحداث تغيير الفلاتر
        document.getElementById('currencyCode').addEventListener('change', refreshData);
        document.getElementById('statusFilter').addEventListener('change', refreshData);

        // أحداث البحث السريع للمورد في الفلتر
        document.getElementById('supplierFilterSearch').addEventListener('input', searchSuppliersFilter);
        document.getElementById('supplierFilterSearch').addEventListener('focus', searchSuppliersFilter);

        // إخفاء القائمة عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!e.target.closest('#supplierFilterSearch') && !e.target.closest('#supplierFilterDropdown')) {
                document.getElementById('supplierFilterDropdown').style.display = 'none';
            }
        });
    </script>
</body>
</html>
