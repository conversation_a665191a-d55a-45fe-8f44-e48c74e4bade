-- =====================================================
-- Trigger لنقل الأرصدة الافتتاحية تلقائياً عند الترحيل
-- Auto-sync Opening Balances to Current Balances Trigger
-- =====================================================

-- إنشاء trigger لنقل الأرصدة الافتتاحية للصرافين/البنوك تلقائياً عند تغيير الحالة إلى POSTED
CREATE OR REPLACE TRIGGER AUTO_SYNC_OPENING_BALANCES
    AFTER UPDATE OF status ON OPENING_BALANCES
    FOR EACH ROW
WHEN (NEW.entity_type_code = 'MONEY_CHANGER' AND NEW.status = 'POSTED' AND OLD.status != 'POSTED')
DECLARE
    v_count NUMBER;
BEGIN
    -- التحقق من عدم وجود السجل مسبقاً في CURRENT_BALANCES
    SELECT COUNT(*)
    INTO v_count
    FROM CURRENT_BALANCES
    WHERE entity_type_code = :NEW.entity_type_code
    AND entity_id = :NEW.entity_id
    AND currency_code = :NEW.currency_code;
    
    -- إذا لم يكن موجوداً، أضفه
    IF v_count = 0 THEN
        INSERT INTO CURRENT_BALANCES (
            entity_type_code,
            entity_id,
            currency_code,
            opening_balance,
            debit_amount,
            credit_amount,
            total_transactions_count,
            last_transaction_date,
            last_document_type,
            last_document_number,
            created_at,
            updated_at,
            created_by,
            updated_by
        ) VALUES (
            :NEW.entity_type_code,
            :NEW.entity_id,
            :NEW.currency_code,
            -- الرصيد الافتتاحي (موجب للمدين، سالب للدائن)
            CASE 
                WHEN :NEW.balance_type = 'DEBIT' THEN :NEW.opening_balance_amount
                WHEN :NEW.balance_type = 'CREDIT' THEN -:NEW.opening_balance_amount
                ELSE 0
            END,
            -- لا نضع مبالغ في debit_amount أو credit_amount لتجنب المضاعفة
            0,
            0,
            1,
            :NEW.posted_date,
            'OPENING_BALANCE',
            'OB-' || :NEW.id,
            :NEW.created_date,
            :NEW.posted_date,
            :NEW.created_by,
            :NEW.posted_by
        );
    END IF;
END;
/

-- إنشاء إجراء لمزامنة الأرصدة الافتتاحية الموجودة
CREATE OR REPLACE PROCEDURE SYNC_OPENING_TO_CURRENT AS
BEGIN
    -- نقل الأرصدة الافتتاحية المرحلة للصرافين التي لم يتم نقلها بعد
    INSERT INTO CURRENT_BALANCES (
        entity_type_code,
        entity_id,
        currency_code,
        opening_balance,
        debit_amount,
        credit_amount,
        total_transactions_count,
        last_transaction_date,
        last_document_type,
        last_document_number,
        created_at,
        updated_at,
        created_by,
        updated_by
    )
    SELECT 
        ob.entity_type_code,
        ob.entity_id,
        ob.currency_code,
        -- الرصيد الافتتاحي (موجب للمدين، سالب للدائن)
        CASE 
            WHEN ob.balance_type = 'DEBIT' THEN ob.opening_balance_amount
            WHEN ob.balance_type = 'CREDIT' THEN -ob.opening_balance_amount
            ELSE 0
        END as opening_balance,
        -- لا نضع مبالغ في debit_amount أو credit_amount لتجنب المضاعفة
        0 as debit_amount,
        0 as credit_amount,
        1 as total_transactions_count,
        ob.posted_date as last_transaction_date,
        'OPENING_BALANCE' as last_document_type,
        'OB-' || ob.id as last_document_number,
        ob.created_date,
        ob.posted_date,
        ob.created_by,
        ob.posted_by
    FROM OPENING_BALANCES ob
    WHERE ob.entity_type_code = 'MONEY_CHANGER'
    AND ob.status = 'POSTED'
    AND NOT EXISTS (
        SELECT 1 FROM CURRENT_BALANCES cb 
        WHERE cb.entity_type_code = ob.entity_type_code 
        AND cb.entity_id = ob.entity_id 
        AND cb.currency_code = ob.currency_code
    );
    
    COMMIT;
    
    DBMS_OUTPUT.PUT_LINE('تم نقل ' || SQL%ROWCOUNT || ' رصيد افتتاحي إلى CURRENT_BALANCES');
END;
/

-- تشغيل الإجراء لمزامنة الأرصدة الموجودة
EXEC SYNC_OPENING_TO_CURRENT;

-- رسالة تأكيد
SELECT 'تم إنشاء النظام التلقائي لنقل الأرصدة الافتتاحية بنجاح' AS MESSAGE FROM DUAL;
