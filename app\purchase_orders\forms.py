# -*- coding: utf-8 -*-
"""
نماذج أوامر الشراء
Purchase Orders Forms
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, DecimalField, IntegerField, DateField, SubmitField
from wtforms.validators import DataRequired, Length, NumberRange, Optional
from wtforms.widgets import TextArea
from app.models import Supplier, PurchaseRequest
from datetime import datetime, timedelta

class PurchaseOrderForm(FlaskForm):
    """نموذج إنشاء أمر شراء"""
    
    # معلومات أساسية
    purchase_request_id = SelectField(
        'طلب الشراء',
        coerce=int,
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    supplier_id = SelectField(
        'المورد',
        coerce=int,
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    # حقل الفرع - جديد
    branch_id = SelectField(
        'الفرع',
        coerce=int,
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )

    order_number = StringField(
        'رقم الأمر',
        validators=[DataRequired(), Length(min=1, max=50)],
        render_kw={'class': 'form-control', 'placeholder': 'سيتم إنشاؤه تلقائياً'}
    )
    
    # تواريخ
    order_date = DateField(
        'تاريخ الأمر',
        validators=[DataRequired()],
        default=datetime.utcnow,
        render_kw={'class': 'form-control'}
    )
    
    expected_delivery_date = DateField(
        'تاريخ التسليم المتوقع',
        validators=[DataRequired()],
        default=lambda: datetime.utcnow() + timedelta(days=7),
        render_kw={'class': 'form-control'}
    )
    
    # معلومات إضافية
    payment_terms = SelectField(
        'شروط الدفع',
        choices=[
            ('cash', 'نقداً'),
            ('30_days', '30 يوم'),
            ('60_days', '60 يوم'),
            ('90_days', '90 يوم'),
            ('advance', 'دفع مقدم')
        ],
        default='30_days',
        render_kw={'class': 'form-select'}
    )
    
    delivery_terms = SelectField(
        'شروط التسليم',
        choices=[
            ('pickup', 'استلام من المورد'),
            ('delivery', 'توصيل للموقع'),
            ('fob', 'FOB'),
            ('cif', 'CIF')
        ],
        default='delivery',
        render_kw={'class': 'form-select'}
    )

    supplier_invoice_number = StringField(
        'رقم فاتورة المورد',
        validators=[Optional(), Length(max=100)],
        render_kw={'class': 'form-control', 'placeholder': 'أدخل رقم فاتورة المورد'}
    )

    shipping_cost = DecimalField(
        'أجور الشحن',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        default=0,
        render_kw={'class': 'form-control', 'placeholder': '0.00', 'step': '0.01', 'min': '0'}
    )

    clearance_cost = DecimalField(
        'أجور التخليص',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        default=0,
        render_kw={'class': 'form-control', 'placeholder': '0.00', 'step': '0.01', 'min': '0'}
    )

    notes = TextAreaField(
        'ملاحظات',
        validators=[Optional(), Length(max=1000)],
        widget=TextArea(),
        render_kw={'class': 'form-control', 'rows': 4, 'placeholder': 'ملاحظات إضافية...'}
    )
    
    submit = SubmitField('إنشاء أمر الشراء', render_kw={'class': 'btn btn-primary'})
    
    def __init__(self, *args, **kwargs):
        super(PurchaseOrderForm, self).__init__(*args, **kwargs)

        # تحديث خيارات طلبات الشراء المعتمدة
        self.purchase_request_id.choices = [
            (pr.id, f"{pr.title} - {pr.request_number}")
            for pr in PurchaseRequest.query.filter_by(status='approved').all()
        ]

        # تحديث خيارات الموردين النشطين
        try:
            self.supplier_id.choices = [
                (s.id, s.name_ar)
                for s in Supplier.query.filter_by(is_active=True).all()
            ]
        except:
            self.supplier_id.choices = []

        # تحديث خيارات الفروع النشطة
        try:
            from oracle_manager import get_oracle_manager
            oracle_manager = get_oracle_manager()

            branches_query = """
            SELECT BRN_NO, BRN_LNAME
            FROM BRANCHES
            WHERE NVL(IS_ACTIVE, 1) = 1
            ORDER BY BRN_LNAME
            """

            branches_result = oracle_manager.execute_query(branches_query)

            if branches_result:
                self.branch_id.choices = [
                    (branch[0], branch[1])
                    for branch in branches_result
                ]
            else:
                # قيم افتراضية إذا لم توجد فروع
                self.branch_id.choices = [(21, 'الفرع الرئيسي')]

        except Exception as e:
            # قيم افتراضية في حالة الخطأ
            self.branch_id.choices = [(21, 'الفرع الرئيسي')]

class PurchaseOrderUpdateForm(FlaskForm):
    """نموذج تحديث أمر شراء"""

    # حقل الفرع - جديد
    branch_id = SelectField(
        'الفرع',
        coerce=int,
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )

    status = SelectField(
        'حالة الأمر',
        choices=[
            ('pending', 'في الانتظار'),
            ('confirmed', 'مؤكد'),
            ('in_progress', 'قيد التنفيذ'),
            ('partially_delivered', 'تم التسليم جزئياً'),
            ('delivered', 'تم التسليم'),
            ('cancelled', 'ملغي')
        ],
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    expected_delivery_date = DateField(
        'تاريخ التسليم المتوقع',
        validators=[DataRequired()],
        render_kw={'class': 'form-control'}
    )
    
    actual_delivery_date = DateField(
        'تاريخ التسليم الفعلي',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )

    supplier_invoice_number = StringField(
        'رقم فاتورة المورد',
        validators=[Optional(), Length(max=100)],
        render_kw={'class': 'form-control', 'placeholder': 'أدخل رقم فاتورة المورد'}
    )

    shipping_cost = DecimalField(
        'أجور الشحن',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        default=0,
        render_kw={'class': 'form-control', 'placeholder': '0.00', 'step': '0.01', 'min': '0'}
    )

    clearance_cost = DecimalField(
        'أجور التخليص',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        default=0,
        render_kw={'class': 'form-control', 'placeholder': '0.00', 'step': '0.01', 'min': '0'}
    )

    notes = TextAreaField(
        'ملاحظات',
        validators=[Optional(), Length(max=1000)],
        widget=TextArea(),
        render_kw={'class': 'form-control', 'rows': 4}
    )
    
    submit = SubmitField('تحديث الأمر', render_kw={'class': 'btn btn-primary'})

    def __init__(self, *args, **kwargs):
        super(PurchaseOrderUpdateForm, self).__init__(*args, **kwargs)

        # تحديث خيارات الفروع النشطة
        try:
            from oracle_manager import get_oracle_manager
            oracle_manager = get_oracle_manager()

            branches_query = """
            SELECT BRN_NO, BRN_LNAME
            FROM BRANCHES
            WHERE NVL(IS_ACTIVE, 1) = 1
            ORDER BY BRN_LNAME
            """

            branches_result = oracle_manager.execute_query(branches_query)

            if branches_result:
                self.branch_id.choices = [
                    (branch[0], branch[1])
                    for branch in branches_result
                ]
            else:
                # قيم افتراضية إذا لم توجد فروع
                self.branch_id.choices = [(21, 'الفرع الرئيسي')]

        except Exception as e:
            # قيم افتراضية في حالة الخطأ
            self.branch_id.choices = [(21, 'الفرع الرئيسي')]

class PurchaseOrderSearchForm(FlaskForm):
    """نموذج البحث في أوامر الشراء"""
    
    search_term = StringField(
        'البحث',
        validators=[Optional(), Length(max=100)],
        render_kw={'class': 'form-control', 'placeholder': 'رقم الأمر، المورد، أو الوصف...'}
    )
    
    status = SelectField(
        'الحالة',
        choices=[
            ('', 'جميع الحالات'),
            ('pending', 'في الانتظار'),
            ('confirmed', 'مؤكد'),
            ('in_progress', 'قيد التنفيذ'),
            ('partially_delivered', 'تم التسليم جزئياً'),
            ('delivered', 'تم التسليم'),
            ('cancelled', 'ملغي')
        ],
        default='',
        render_kw={'class': 'form-select'}
    )
    
    supplier_id = SelectField(
        'المورد',
        choices=[],
        render_kw={'class': 'form-select'}
    )
    
    date_from = DateField(
        'من تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    date_to = DateField(
        'إلى تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    submit = SubmitField('بحث', render_kw={'class': 'btn btn-outline-primary'})
    
    def __init__(self, *args, **kwargs):
        super(PurchaseOrderSearchForm, self).__init__(*args, **kwargs)
        
        # تحديث خيارات الموردين
        try:
            self.supplier_id.choices = [('', 'جميع الموردين')] + [
                (str(s.id), s.name_ar)
                for s in Supplier.query.filter_by(is_active=True).all()
            ]
        except:
            self.supplier_id.choices = [('', 'جميع الموردين')]
