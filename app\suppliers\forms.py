# -*- coding: utf-8 -*-
"""
نماذج إدارة الموردين
Suppliers Management Forms
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, DecimalField, IntegerField, SubmitField, BooleanField, DateField
from wtforms.validators import DataRequired, Length, NumberRange, Optional, Email
from wtforms.widgets import TextArea

class SupplierForm(FlaskForm):
    """نموذج إنشاء/تعديل مورد"""
    
    # معلومات أساسية
    name = StringField(
        'اسم المورد',
        validators=[DataRequired(), Length(min=1, max=200)],
        render_kw={'class': 'form-control', 'placeholder': 'اسم الشركة أو المورد'}
    )
    
    code = StringField(
        'كود المورد',
        validators=[Optional(), Length(max=50)],
        render_kw={'class': 'form-control', 'placeholder': 'كود فريد للمورد'}
    )
    
    type = SelectField(
        'نوع المورد',
        choices=[
            ('company', 'شركة'),
            ('individual', 'فرد'),
            ('government', 'جهة حكومية'),
            ('international', 'مورد دولي')
        ],
        default='company',
        render_kw={'class': 'form-select'}
    )
    
    category = SelectField(
        'فئة المورد',
        choices=[
            ('materials', 'مواد خام'),
            ('equipment', 'معدات'),
            ('services', 'خدمات'),
            ('office_supplies', 'مستلزمات مكتبية'),
            ('maintenance', 'صيانة'),
            ('other', 'أخرى')
        ],
        default='materials',
        render_kw={'class': 'form-select'}
    )
    
    # معلومات الاتصال
    contact_person = StringField(
        'الشخص المسؤول',
        validators=[Optional(), Length(max=100)],
        render_kw={'class': 'form-control', 'placeholder': 'اسم الشخص المسؤول'}
    )
    
    phone = StringField(
        'رقم الهاتف',
        validators=[Optional(), Length(max=20)],
        render_kw={'class': 'form-control', 'placeholder': '+966xxxxxxxxx'}
    )
    
    mobile = StringField(
        'رقم الجوال',
        validators=[Optional(), Length(max=20)],
        render_kw={'class': 'form-control', 'placeholder': '+966xxxxxxxxx'}
    )
    
    email = StringField(
        'البريد الإلكتروني',
        validators=[Optional(), Email(), Length(max=100)],
        render_kw={'class': 'form-control', 'placeholder': '<EMAIL>'}
    )
    
    website = StringField(
        'الموقع الإلكتروني',
        validators=[Optional(), Length(max=200)],
        render_kw={'class': 'form-control', 'placeholder': 'https://www.example.com'}
    )
    
    # العنوان
    address = TextAreaField(
        'العنوان',
        validators=[Optional(), Length(max=500)],
        render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'العنوان التفصيلي...'}
    )
    
    city = StringField(
        'المدينة',
        validators=[Optional(), Length(max=50)],
        render_kw={'class': 'form-control', 'placeholder': 'الرياض'}
    )
    
    country = StringField(
        'الدولة',
        validators=[Optional(), Length(max=50)],
        render_kw={'class': 'form-control', 'placeholder': 'المملكة العربية السعودية'}
    )
    
    postal_code = StringField(
        'الرمز البريدي',
        validators=[Optional(), Length(max=10)],
        render_kw={'class': 'form-control', 'placeholder': '12345'}
    )
    
    # معلومات مالية
    tax_number = StringField(
        'الرقم الضريبي',
        validators=[Optional(), Length(max=50)],
        render_kw={'class': 'form-control', 'placeholder': 'رقم التسجيل الضريبي'}
    )
    
    commercial_register = StringField(
        'السجل التجاري',
        validators=[Optional(), Length(max=50)],
        render_kw={'class': 'form-control', 'placeholder': 'رقم السجل التجاري'}
    )
    
    payment_terms = SelectField(
        'شروط الدفع',
        choices=[
            ('cash', 'نقداً'),
            ('30_days', '30 يوم'),
            ('60_days', '60 يوم'),
            ('90_days', '90 يوم'),
            ('advance', 'دفع مقدم')
        ],
        default='30_days',
        render_kw={'class': 'form-select'}
    )
    
    credit_limit = DecimalField(
        'حد الائتمان',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0'}
    )
    
    # تقييم المورد
    rating = SelectField(
        'التقييم',
        choices=[
            ('', 'غير مقيم'),
            ('excellent', 'ممتاز'),
            ('very_good', 'جيد جداً'),
            ('good', 'جيد'),
            ('fair', 'مقبول'),
            ('poor', 'ضعيف')
        ],
        default='',
        render_kw={'class': 'form-select'}
    )
    
    # معلومات إضافية
    notes = TextAreaField(
        'ملاحظات',
        validators=[Optional(), Length(max=1000)],
        widget=TextArea(),
        render_kw={'class': 'form-control', 'rows': 4, 'placeholder': 'ملاحظات إضافية حول المورد...'}
    )
    
    is_active = BooleanField(
        'نشط',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    submit = SubmitField('حفظ المورد', render_kw={'class': 'btn btn-primary'})

class SupplierSearchForm(FlaskForm):
    """نموذج البحث في الموردين"""
    
    search_term = StringField(
        'البحث',
        validators=[Optional(), Length(max=100)],
        render_kw={'class': 'form-control', 'placeholder': 'اسم المورد، الكود، أو الهاتف...'}
    )
    
    type = SelectField(
        'النوع',
        choices=[
            ('', 'جميع الأنواع'),
            ('company', 'شركة'),
            ('individual', 'فرد'),
            ('government', 'جهة حكومية'),
            ('international', 'مورد دولي')
        ],
        default='',
        render_kw={'class': 'form-select'}
    )
    
    category = SelectField(
        'الفئة',
        choices=[
            ('', 'جميع الفئات'),
            ('materials', 'مواد خام'),
            ('equipment', 'معدات'),
            ('services', 'خدمات'),
            ('office_supplies', 'مستلزمات مكتبية'),
            ('maintenance', 'صيانة'),
            ('other', 'أخرى')
        ],
        default='',
        render_kw={'class': 'form-select'}
    )
    
    rating = SelectField(
        'التقييم',
        choices=[
            ('', 'جميع التقييمات'),
            ('excellent', 'ممتاز'),
            ('very_good', 'جيد جداً'),
            ('good', 'جيد'),
            ('fair', 'مقبول'),
            ('poor', 'ضعيف')
        ],
        default='',
        render_kw={'class': 'form-select'}
    )
    
    is_active = SelectField(
        'الحالة',
        choices=[
            ('', 'الكل'),
            ('1', 'نشط'),
            ('0', 'غير نشط')
        ],
        default='',
        render_kw={'class': 'form-select'}
    )
    
    submit = SubmitField('بحث', render_kw={'class': 'btn btn-outline-primary'})

class ContractForm(FlaskForm):
    """نموذج عقد مع المورد"""
    
    supplier_id = SelectField(
        'المورد',
        coerce=int,
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    contract_number = StringField(
        'رقم العقد',
        validators=[DataRequired(), Length(min=1, max=50)],
        render_kw={'class': 'form-control', 'placeholder': 'رقم العقد'}
    )
    
    title = StringField(
        'عنوان العقد',
        validators=[DataRequired(), Length(min=1, max=200)],
        render_kw={'class': 'form-control', 'placeholder': 'عنوان العقد'}
    )
    
    description = TextAreaField(
        'وصف العقد',
        validators=[Optional(), Length(max=1000)],
        render_kw={'class': 'form-control', 'rows': 4, 'placeholder': 'وصف تفصيلي للعقد...'}
    )
    
    contract_type = SelectField(
        'نوع العقد',
        choices=[
            ('supply', 'توريد'),
            ('service', 'خدمة'),
            ('maintenance', 'صيانة'),
            ('framework', 'إطاري'),
            ('other', 'أخرى')
        ],
        default='supply',
        render_kw={'class': 'form-select'}
    )
    
    start_date = DateField(
        'تاريخ البداية',
        validators=[DataRequired()],
        render_kw={'class': 'form-control'}
    )
    
    end_date = DateField(
        'تاريخ النهاية',
        validators=[DataRequired()],
        render_kw={'class': 'form-control'}
    )
    
    contract_value = DecimalField(
        'قيمة العقد',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0'}
    )
    
    currency = SelectField(
        'العملة',
        choices=[
            ('SAR', 'ريال سعودي'),
            ('USD', 'دولار أمريكي'),
            ('EUR', 'يورو'),
            ('GBP', 'جنيه إسترليني')
        ],
        default='SAR',
        render_kw={'class': 'form-select'}
    )
    
    status = SelectField(
        'حالة العقد',
        choices=[
            ('draft', 'مسودة'),
            ('active', 'نشط'),
            ('expired', 'منتهي'),
            ('cancelled', 'ملغي')
        ],
        default='draft',
        render_kw={'class': 'form-select'}
    )
    
    terms_and_conditions = TextAreaField(
        'الشروط والأحكام',
        validators=[Optional(), Length(max=2000)],
        render_kw={'class': 'form-control', 'rows': 6, 'placeholder': 'الشروط والأحكام التفصيلية...'}
    )
    
    notes = TextAreaField(
        'ملاحظات',
        validators=[Optional(), Length(max=500)],
        render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات إضافية...'}
    )
    
    submit = SubmitField('حفظ العقد', render_kw={'class': 'btn btn-primary'})
    
    def __init__(self, *args, **kwargs):
        super(ContractForm, self).__init__(*args, **kwargs)
        
        # تحديث خيارات الموردين النشطين
        from app.models import Supplier
        try:
            self.supplier_id.choices = [
                (s.id, s.name_ar)
                for s in Supplier.query.filter_by(is_active=True).all()
            ]
        except:
            self.supplier_id.choices = []

class SupplierEvaluationForm(FlaskForm):
    """نموذج تقييم المورد"""
    
    supplier_id = SelectField(
        'المورد',
        coerce=int,
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    evaluation_period = StringField(
        'فترة التقييم',
        validators=[DataRequired(), Length(min=1, max=50)],
        render_kw={'class': 'form-control', 'placeholder': 'مثال: الربع الأول 2024'}
    )
    
    # معايير التقييم
    quality_score = SelectField(
        'جودة المنتجات/الخدمات',
        choices=[
            ('5', 'ممتاز (5)'),
            ('4', 'جيد جداً (4)'),
            ('3', 'جيد (3)'),
            ('2', 'مقبول (2)'),
            ('1', 'ضعيف (1)')
        ],
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    delivery_score = SelectField(
        'الالتزام بمواعيد التسليم',
        choices=[
            ('5', 'ممتاز (5)'),
            ('4', 'جيد جداً (4)'),
            ('3', 'جيد (3)'),
            ('2', 'مقبول (2)'),
            ('1', 'ضعيف (1)')
        ],
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    price_score = SelectField(
        'تنافسية الأسعار',
        choices=[
            ('5', 'ممتاز (5)'),
            ('4', 'جيد جداً (4)'),
            ('3', 'جيد (3)'),
            ('2', 'مقبول (2)'),
            ('1', 'ضعيف (1)')
        ],
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    service_score = SelectField(
        'جودة خدمة العملاء',
        choices=[
            ('5', 'ممتاز (5)'),
            ('4', 'جيد جداً (4)'),
            ('3', 'جيد (3)'),
            ('2', 'مقبول (2)'),
            ('1', 'ضعيف (1)')
        ],
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    communication_score = SelectField(
        'التواصل والاستجابة',
        choices=[
            ('5', 'ممتاز (5)'),
            ('4', 'جيد جداً (4)'),
            ('3', 'جيد (3)'),
            ('2', 'مقبول (2)'),
            ('1', 'ضعيف (1)')
        ],
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    # ملاحظات التقييم
    strengths = TextAreaField(
        'نقاط القوة',
        validators=[Optional(), Length(max=500)],
        render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'نقاط القوة لدى المورد...'}
    )
    
    weaknesses = TextAreaField(
        'نقاط الضعف',
        validators=[Optional(), Length(max=500)],
        render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'نقاط الضعف والتحسين المطلوب...'}
    )
    
    recommendations = TextAreaField(
        'التوصيات',
        validators=[Optional(), Length(max=500)],
        render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'توصيات للتعامل المستقبلي...'}
    )
    
    notes = TextAreaField(
        'ملاحظات إضافية',
        validators=[Optional(), Length(max=500)],
        render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات أخرى...'}
    )
    
    submit = SubmitField('حفظ التقييم', render_kw={'class': 'btn btn-primary'})
    
    def __init__(self, *args, **kwargs):
        super(SupplierEvaluationForm, self).__init__(*args, **kwargs)
        
        # تحديث خيارات الموردين النشطين
        from app.models import Supplier
        try:
            self.supplier_id.choices = [
                (s.id, s.name_ar)
                for s in Supplier.query.filter_by(is_active=True).all()
            ]
        except:
            self.supplier_id.choices = []
