<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار القوائم المنسدلة في النوافذ المنبثقة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        /* إصلاح عام لجميع القوائم المنسدلة */
        .form-select {
            padding-right: 2.5rem !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
            background-repeat: no-repeat !important;
            background-position: right 0.75rem center !important;
            background-size: 16px 12px !important;
            appearance: none !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
        }

        /* إصلاح خاص للقوائم المنسدلة في النوافذ المنبثقة */
        .modal .form-select {
            border: 2px solid #dee2e6 !important;
            border-radius: 8px !important;
            padding: 0.75rem 2.5rem 0.75rem 0.75rem !important;
            font-size: 1rem !important;
            font-weight: 500 !important;
            background-color: #f8f9fa !important;
        }

        .modal .form-select:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25) !important;
            background-color: #ffffff !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-window-restore text-primary me-2"></i>
                    اختبار القوائم المنسدلة في النوافذ المنبثقة
                </h1>

                <!-- أزرار فتح النوافذ -->
                <div class="test-section">
                    <h3><i class="fas fa-play-circle me-2"></i>اختبار النوافذ المنبثقة</h3>
                    <div class="d-flex gap-3">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#statusEditModal">
                            <i class="fas fa-edit me-2"></i>
                            تعديل حالة الشحنة
                        </button>
                        <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#releaseStatusEditModal">
                            <i class="fas fa-unlock-alt me-2"></i>
                            تعديل حالة الإفراج
                        </button>
                    </div>
                </div>

                <!-- نتائج الاختبار -->
                <div class="test-section">
                    <h3><i class="fas fa-clipboard-check me-2"></i>ما تم إصلاحه</h3>
                    
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle me-2"></i>القوائم المنسدلة المُصححة:</h5>
                        <ul class="mb-0">
                            <li>✅ <strong>#newStatus</strong> - قائمة الحالة الجديدة في نافذة تعديل حالة الشحنة</li>
                            <li>✅ <strong>#newReleaseStatus</strong> - قائمة حالة الإفراج الجديدة في نافذة تعديل حالة الإفراج</li>
                        </ul>
                    </div>

                    <div class="alert alert-info">
                        <h5><i class="fas fa-magic me-2"></i>التحسينات المطبقة:</h5>
                        <ul class="mb-0">
                            <li>🎨 <strong>مساحة إضافية</strong> - padding-right: 2.5rem</li>
                            <li>🎨 <strong>حدود محسنة</strong> - border: 2px solid</li>
                            <li>🎨 <strong>خلفية مميزة</strong> - background-color: #f8f9fa</li>
                            <li>🎨 <strong>تأثير التركيز</strong> - box-shadow ملون</li>
                            <li>🎨 <strong>سهم مخصص</strong> - SVG احترافي</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل حالة الشحنة -->
    <div class="modal fade" id="statusEditModal" tabindex="-1" aria-labelledby="statusEditModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="statusEditModalLabel">
                        <i class="fas fa-edit me-2"></i>
                        تعديل حالة الشحنة
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <form id="statusEditForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">رقم التتبع:</label>
                                <input type="text" class="form-control" value="CRG20250817201938" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">الحالة الحالية:</label>
                                <span class="badge bg-info fs-6">في الطريق</span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="newStatus" class="form-label fw-bold">الحالة الجديدة:</label>
                            <select class="form-select" id="newStatus" required>
                                <option value="">اختر الحالة الجديدة...</option>
                                <option value="draft">مسودة</option>
                                <option value="confirmed">مؤكدة</option>
                                <option value="in_transit">قيد الشحن</option>
                                <option value="arrived_port">وصلت للميناء</option>
                                <option value="customs_clearance">قيد التخليص</option>
                                <option value="ready_pickup">جاهزة للاستلام</option>
                                <option value="delivered">تم التسليم</option>
                                <option value="cancelled">ملغية</option>
                                <option value="delayed">متأخرة</option>
                                <option value="returned">معادة</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="statusNotes" class="form-label fw-bold">ملاحظات التحديث:</label>
                            <textarea class="form-control" id="statusNotes" rows="3" placeholder="أدخل ملاحظات حول تغيير الحالة (اختياري)"></textarea>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> سيتم تحديث حالة أمر الشراء المرتبط تلقائياً حسب الحالة الجديدة.
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </button>
                    <button type="button" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        حفظ التحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل حالة الإفراج -->
    <div class="modal fade" id="releaseStatusEditModal" tabindex="-1" aria-labelledby="releaseStatusEditModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="releaseStatusEditModalLabel">
                        <i class="fas fa-unlock-alt me-2"></i>
                        تعديل حالة الإفراج
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <form id="releaseStatusEditForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">رقم التتبع:</label>
                                <input type="text" class="form-control" value="CRG20250817201938" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">حالة الإفراج الحالية:</label>
                                <span class="badge bg-warning fs-6">في انتظار الإفراج</span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="newReleaseStatus" class="form-label fw-bold">حالة الإفراج الجديدة:</label>
                            <select class="form-select" id="newReleaseStatus" required>
                                <option value="">اختر حالة الإفراج الجديدة...</option>
                                <option value="pending">في انتظار الإفراج</option>
                                <option value="documents_review">مراجعة المستندات</option>
                                <option value="payment_verification">التحقق من المدفوعات</option>
                                <option value="quality_check">فحص الجودة</option>
                                <option value="customs_clearance">التخليص الجمركي</option>
                                <option value="ready_for_pickup">جاهز للاستلام</option>
                                <option value="released">تم الإفراج</option>
                                <option value="on_hold">معلق</option>
                                <option value="rejected">مرفوض</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="releaseStatusNotes" class="form-label fw-bold">ملاحظات الإفراج:</label>
                            <textarea class="form-control" id="releaseStatusNotes" rows="3" placeholder="أدخل ملاحظات حول تغيير حالة الإفراج (اختياري)"></textarea>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> تغيير حالة الإفراج قد يؤثر على إجراءات التخليص الجمركي والتسليم.
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </button>
                    <button type="button" class="btn btn-warning">
                        <i class="fas fa-save me-1"></i>
                        حفظ التحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
