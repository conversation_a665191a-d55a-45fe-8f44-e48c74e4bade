#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية لنظام الشحنات
Add Sample Data for Shipment System
"""

from database_manager import DatabaseManager
from datetime import datetime, timedelta
import random

def add_sample_shipments():
    """إضافة شحنات تجريبية"""
    db_manager = DatabaseManager()
    
    try:
        print("🚀 بدء إضافة بيانات تجريبية لنظام الشحنات...")
        
        # بيانات تجريبية للشحنات
        sample_shipments = [
            {
                'tracking_number': 'SAS20250806001234',
                'sender_name': 'أحمد محمد العلي',
                'sender_phone': '0501234567',
                'sender_email': '<EMAIL>',
                'sender_address': 'شارع الملك فهد، حي العليا',
                'sender_city': 'الرياض',
                'recipient_name': 'فاطمة سالم أحمد',
                'recipient_phone': '0509876543',
                'recipient_email': '<EMAIL>',
                'recipient_address': 'شارع الأمير محمد بن عبدالعزيز، حي الفيصلية',
                'recipient_city': 'جدة',
                'status': 'في الطريق',
                'shipment_type': 'سريع',
                'priority': 'عالي',
                'weight': 2.5,
                'declared_value': 500.00,
                'total_cost': 85.50,
                'current_location': 'مركز توزيع الرياض'
            },
            {
                'tracking_number': 'SAS20250806001235',
                'sender_name': 'نورا عبدالله الزهراني',
                'sender_phone': '0551234567',
                'sender_email': '<EMAIL>',
                'sender_address': 'شارع التحلية، حي السلامة',
                'sender_city': 'جدة',
                'recipient_name': 'خالد محمد القحطاني',
                'recipient_phone': '0559876543',
                'recipient_email': '<EMAIL>',
                'recipient_address': 'شارع الملك عبدالعزيز، حي المربع',
                'recipient_city': 'الرياض',
                'status': 'معلق',
                'shipment_type': 'عادي',
                'priority': 'عادي',
                'weight': 1.2,
                'declared_value': 200.00,
                'total_cost': 45.00,
                'current_location': 'مركز استلام جدة'
            },
            {
                'tracking_number': 'SAS20250806001236',
                'sender_name': 'محمد سعد الغامدي',
                'sender_phone': '0561234567',
                'sender_email': '<EMAIL>',
                'sender_address': 'شارع الملك فيصل، حي النزهة',
                'sender_city': 'الدمام',
                'recipient_name': 'سارة أحمد المطيري',
                'recipient_phone': '0569876543',
                'recipient_email': '<EMAIL>',
                'recipient_address': 'شارع الأمير سلطان، حي الروضة',
                'recipient_city': 'الرياض',
                'status': 'تم التسليم',
                'shipment_type': 'ليلي',
                'priority': 'عاجل',
                'weight': 0.8,
                'declared_value': 150.00,
                'total_cost': 95.00,
                'current_location': 'تم التسليم'
            },
            {
                'tracking_number': 'SAS20250806001237',
                'sender_name': 'عبدالرحمن علي الشهري',
                'sender_phone': '0571234567',
                'sender_email': '<EMAIL>',
                'sender_address': 'شارع الملك خالد، حي الصفا',
                'sender_city': 'مكة المكرمة',
                'recipient_name': 'هند محمد العتيبي',
                'recipient_phone': '0579876543',
                'recipient_email': '<EMAIL>',
                'recipient_address': 'شارع العروبة، حي الملز',
                'recipient_city': 'الرياض',
                'status': 'خارج للتسليم',
                'shipment_type': 'نفس اليوم',
                'priority': 'عاجل',
                'weight': 3.2,
                'declared_value': 800.00,
                'total_cost': 125.00,
                'current_location': 'مركز توزيع الرياض - خارج للتسليم'
            },
            {
                'tracking_number': 'SAS20250806001238',
                'sender_name': 'ريم سالم الحربي',
                'sender_phone': '0581234567',
                'sender_email': '<EMAIL>',
                'sender_address': 'شارع الأمير محمد، حي الشاطئ',
                'sender_city': 'الخبر',
                'recipient_name': 'عمر عبدالله النعيمي',
                'recipient_phone': '0589876543',
                'recipient_email': '<EMAIL>',
                'recipient_address': 'شارع التخصصي، حي المحمدية',
                'recipient_city': 'الرياض',
                'status': 'تم الاستلام',
                'shipment_type': 'قابل للكسر',
                'priority': 'عالي',
                'weight': 1.5,
                'declared_value': 300.00,
                'total_cost': 65.00,
                'current_location': 'مركز معالجة الخبر'
            }
        ]
        
        # إدراج البيانات
        for i, shipment in enumerate(sample_shipments, 1):
            try:
                # تحديد التواريخ
                created_date = datetime.now() - timedelta(days=random.randint(0, 7))
                expected_delivery = created_date + timedelta(days=random.randint(1, 3))
                
                if shipment['status'] == 'تم التسليم':
                    actual_delivery = created_date + timedelta(days=random.randint(1, 2))
                    pickup_date = created_date + timedelta(hours=random.randint(2, 8))
                elif shipment['status'] in ['في الطريق', 'خارج للتسليم', 'تم الاستلام']:
                    pickup_date = created_date + timedelta(hours=random.randint(2, 8))
                    actual_delivery = None
                else:
                    pickup_date = None
                    actual_delivery = None
                
                # إدراج الشحنة
                insert_sql = """
                    INSERT INTO shipments (
                        tracking_number, sender_name, sender_phone, sender_email, 
                        sender_address, sender_city, recipient_name, recipient_phone, 
                        recipient_email, recipient_address, recipient_city, status, 
                        shipment_type, priority, weight, declared_value, total_cost, 
                        current_location, created_at, pickup_date, expected_delivery_date, 
                        actual_delivery_date, sender_latitude, sender_longitude,
                        recipient_latitude, recipient_longitude
                    ) VALUES (
                        :tracking_number, :sender_name, :sender_phone, :sender_email,
                        :sender_address, :sender_city, :recipient_name, :recipient_phone,
                        :recipient_email, :recipient_address, :recipient_city, :status,
                        :shipment_type, :priority, :weight, :declared_value, :total_cost,
                        :current_location, :created_at, :pickup_date, :expected_delivery_date,
                        :actual_delivery_date, :sender_latitude, :sender_longitude,
                        :recipient_latitude, :recipient_longitude
                    )
                """
                
                # إحداثيات تقريبية للمدن السعودية
                city_coordinates = {
                    'الرياض': (24.7136, 46.6753),
                    'جدة': (21.4858, 39.1925),
                    'الدمام': (26.4207, 50.0888),
                    'مكة المكرمة': (21.3891, 39.8579),
                    'الخبر': (26.2172, 50.1971)
                }
                
                sender_coords = city_coordinates.get(shipment['sender_city'], (24.7136, 46.6753))
                recipient_coords = city_coordinates.get(shipment['recipient_city'], (24.7136, 46.6753))
                
                params = {
                    'tracking_number': shipment['tracking_number'],
                    'sender_name': shipment['sender_name'],
                    'sender_phone': shipment['sender_phone'],
                    'sender_email': shipment['sender_email'],
                    'sender_address': shipment['sender_address'],
                    'sender_city': shipment['sender_city'],
                    'recipient_name': shipment['recipient_name'],
                    'recipient_phone': shipment['recipient_phone'],
                    'recipient_email': shipment['recipient_email'],
                    'recipient_address': shipment['recipient_address'],
                    'recipient_city': shipment['recipient_city'],
                    'status': shipment['status'],
                    'shipment_type': shipment['shipment_type'],
                    'priority': shipment['priority'],
                    'weight': shipment['weight'],
                    'declared_value': shipment['declared_value'],
                    'total_cost': shipment['total_cost'],
                    'current_location': shipment['current_location'],
                    'created_at': created_date,
                    'pickup_date': pickup_date,
                    'expected_delivery_date': expected_delivery,
                    'actual_delivery_date': actual_delivery,
                    'sender_latitude': sender_coords[0],
                    'sender_longitude': sender_coords[1],
                    'recipient_latitude': recipient_coords[0],
                    'recipient_longitude': recipient_coords[1]
                }
                
                db_manager.execute_update(insert_sql, params)
                print(f"✅ تم إضافة الشحنة {i}: {shipment['tracking_number']}")
                
            except Exception as e:
                print(f"❌ خطأ في إضافة الشحنة {i}: {e}")
        
        print("\n🎉 تم إضافة البيانات التجريبية بنجاح!")
        print("📊 يمكنك الآن رؤية الشحنات في النظام")
        
    except Exception as e:
        print(f"❌ خطأ عام في إضافة البيانات: {e}")
    
    finally:
        db_manager.close()

if __name__ == "__main__":
    add_sample_shipments()
