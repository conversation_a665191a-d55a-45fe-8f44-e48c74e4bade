# -*- coding: utf-8 -*-
"""
إعداد نظام القواعد المرنة للإشعارات الفورية
Flexible Rules System Setup for Instant Notifications
"""

from database_manager import DatabaseManager

def create_flexible_rules_tables():
    """إنشاء جداول نظام القواعد المرنة"""
    
    db = DatabaseManager()
    
    print("🔧 إنشاء نظام القواعد المرنة...")
    
    # 1. جدول أنواع الأحداث
    event_types_table = """
    CREATE TABLE notification_event_types (
        id NUMBER PRIMARY KEY,
        event_code VARCHAR2(100) NOT NULL UNIQUE,
        event_name_ar VARCHAR2(200) NOT NULL,
        event_name_en VARCHAR2(200),
        event_category VARCHAR2(50) NOT NULL,
        description_ar CLOB,
        table_name VARCHAR2(100),
        trigger_field VARCHAR2(100),
        is_active NUMBER(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by NUMBER DEFAULT 1
    )
    """
    
    # 2. جدول القواعد المرنة
    flexible_rules_table = """
    CREATE TABLE notification_flexible_rules (
        id NUMBER PRIMARY KEY,
        rule_name VARCHAR2(200) NOT NULL,
        event_type_id NUMBER NOT NULL,
        condition_type VARCHAR2(50) NOT NULL,
        condition_field VARCHAR2(100),
        condition_operator VARCHAR2(20),
        condition_value VARCHAR2(500),
        additional_conditions CLOB,
        message_template CLOB NOT NULL,
        is_enabled NUMBER(1) DEFAULT 1,
        priority_level NUMBER DEFAULT 5,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by NUMBER DEFAULT 1,
        FOREIGN KEY (event_type_id) REFERENCES notification_event_types(id)
    )
    """
    
    # 3. جدول ربط القواعد بجهات الاتصال
    rule_contacts_table = """
    CREATE TABLE notification_rule_contacts (
        id NUMBER PRIMARY KEY,
        rule_id NUMBER NOT NULL,
        contact_id NUMBER,
        contact_group VARCHAR2(100),
        phone_number VARCHAR2(20),
        is_active NUMBER(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (rule_id) REFERENCES notification_flexible_rules(id),
        FOREIGN KEY (contact_id) REFERENCES instant_notification_contacts(id)
    )
    """
    
    # 4. جدول سجل تنفيذ القواعد
    rule_execution_log = """
    CREATE TABLE notification_rule_executions (
        id NUMBER PRIMARY KEY,
        rule_id NUMBER NOT NULL,
        event_data CLOB,
        execution_result VARCHAR2(20),
        message_sent CLOB,
        contacts_notified CLOB,
        execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        error_message VARCHAR2(1000),
        FOREIGN KEY (rule_id) REFERENCES notification_flexible_rules(id)
    )
    """
    
    # 5. جدول الشروط المعقدة
    complex_conditions_table = """
    CREATE TABLE notification_complex_conditions (
        id NUMBER PRIMARY KEY,
        rule_id NUMBER NOT NULL,
        condition_group NUMBER DEFAULT 1,
        field_name VARCHAR2(100) NOT NULL,
        operator_type VARCHAR2(20) NOT NULL,
        field_value VARCHAR2(500),
        logical_operator VARCHAR2(10) DEFAULT 'AND',
        condition_order NUMBER DEFAULT 1,
        is_active NUMBER(1) DEFAULT 1,
        FOREIGN KEY (rule_id) REFERENCES notification_flexible_rules(id)
    )
    """
    
    # إنشاء Sequences
    sequences = [
        "CREATE SEQUENCE notif_event_types_seq START WITH 1 INCREMENT BY 1",
        "CREATE SEQUENCE notif_flexible_rules_seq START WITH 1 INCREMENT BY 1",
        "CREATE SEQUENCE notif_rule_contacts_seq START WITH 1 INCREMENT BY 1",
        "CREATE SEQUENCE notif_rule_executions_seq START WITH 1 INCREMENT BY 1",
        "CREATE SEQUENCE notif_complex_conditions_seq START WITH 1 INCREMENT BY 1"
    ]
    
    try:
        # إنشاء الجداول
        tables = [
            ("notification_event_types", event_types_table),
            ("notification_flexible_rules", flexible_rules_table),
            ("notification_rule_contacts", rule_contacts_table),
            ("notification_rule_executions", rule_execution_log),
            ("notification_complex_conditions", complex_conditions_table)
        ]
        
        for table_name, table_sql in tables:
            try:
                db.execute_update(table_sql)
                print(f"✅ تم إنشاء جدول {table_name}")
            except Exception as e:
                if "already exists" in str(e).lower() or "name is already used" in str(e).lower():
                    print(f"📋 جدول {table_name} موجود بالفعل")
                else:
                    print(f"❌ خطأ في إنشاء جدول {table_name}: {e}")
        
        # إنشاء Sequences
        for seq_sql in sequences:
            try:
                db.execute_update(seq_sql)
                print(f"✅ تم إنشاء sequence")
            except Exception as e:
                if "already exists" in str(e).lower() or "name is already used" in str(e).lower():
                    print(f"📋 Sequence موجود بالفعل")
                else:
                    print(f"❌ خطأ في إنشاء sequence: {e}")
        
        db.commit()
        
        # إدراج البيانات الافتراضية
        insert_default_event_types(db)
        insert_sample_rules(db)
        
        print("🎉 تم إنشاء نظام القواعد المرنة بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في إنشاء النظام: {e}")
        return False

def insert_default_event_types(db):
    """إدراج أنواع الأحداث الافتراضية"""
    
    print("📝 إدراج أنواع الأحداث الافتراضية...")
    
    event_types = [
        # أحداث الشحنات
        ('shipment_status_change', 'تغيير حالة الشحنة', 'Shipment Status Change', 'shipment', 
         'عند تغيير حالة الشحنة', 'cargo_shipments', 'shipment_status'),
        ('shipment_created', 'إنشاء شحنة جديدة', 'New Shipment Created', 'shipment',
         'عند إنشاء شحنة جديدة', 'cargo_shipments', 'id'),
        ('shipment_delayed', 'تأخير الشحنة', 'Shipment Delayed', 'shipment',
         'عند تأخير الشحنة عن الموعد المحدد', 'cargo_shipments', 'estimated_arrival'),
        
        # أحداث زمنية
        ('document_expiry', 'انتهاء صلاحية الوثائق', 'Document Expiry', 'time_based',
         'عند اقتراب انتهاء صلاحية الوثائق', 'cargo_shipments', 'document_expiry_date'),
        ('payment_due', 'استحقاق دفعة', 'Payment Due', 'financial',
         'عند اقتراب موعد استحقاق دفعة', 'cargo_shipments', 'payment_due_date'),
        
        # أحداث مالية
        ('high_value_shipment', 'شحنة عالية القيمة', 'High Value Shipment', 'financial',
         'عند إنشاء شحنة تتجاوز قيمة معينة', 'cargo_shipments', 'total_value'),
        ('payment_received', 'استلام دفعة', 'Payment Received', 'financial',
         'عند استلام دفعة مالية', 'cargo_shipments', 'paid_amount'),
        
        # أحداث مخصصة
        ('custom_condition', 'شرط مخصص', 'Custom Condition', 'custom',
         'شرط مخصص يحدده المستخدم', '', ''),
        ('scheduled_reminder', 'تذكير مجدول', 'Scheduled Reminder', 'time_based',
         'تذكير في وقت محدد', '', '')
    ]
    
    for event_code, name_ar, name_en, category, desc_ar, table_name, trigger_field in event_types:
        try:
            # فحص إذا كان موجود
            check_query = "SELECT COUNT(*) FROM notification_event_types WHERE event_code = :1"
            result = db.execute_query(check_query, [event_code])
            
            if result and result[0][0] == 0:
                insert_query = """
                INSERT INTO notification_event_types 
                (id, event_code, event_name_ar, event_name_en, event_category, 
                 description_ar, table_name, trigger_field)
                VALUES (notif_event_types_seq.NEXTVAL, :1, :2, :3, :4, :5, :6, :7)
                """
                
                db.execute_update(insert_query, [event_code, name_ar, name_en, category, desc_ar, table_name, trigger_field])
                print(f"✅ تم إدراج نوع الحدث: {name_ar}")
            else:
                print(f"📋 نوع الحدث موجود: {name_ar}")
                
        except Exception as e:
            print(f"❌ خطأ في إدراج نوع الحدث {name_ar}: {e}")
    
    db.commit()

def insert_sample_rules(db):
    """إدراج قواعد نموذجية"""
    
    print("📝 إدراج قواعد نموذجية...")
    
    # جلب معرفات أنواع الأحداث
    try:
        # قاعدة للتخليص الجمركي
        customs_rule = """
        INSERT INTO notification_flexible_rules 
        (id, rule_name, event_type_id, condition_type, condition_field, 
         condition_operator, condition_value, message_template, priority_level)
        VALUES (notif_flexible_rules_seq.NEXTVAL, 
                'إشعار التخليص الجمركي المتقدم',
                (SELECT id FROM notification_event_types WHERE event_code = 'shipment_status_change'),
                'field_value', 'shipment_status', '=', 'customs_clearance',
                'تم تغيير حالة الشحنة {tracking_number} إلى قيد التخليص الجمركي. يرجى تجهيز الوثائق المطلوبة.',
                1)
        """
        
        # قاعدة للشحنات عالية القيمة
        high_value_rule = """
        INSERT INTO notification_flexible_rules 
        (id, rule_name, event_type_id, condition_type, condition_field, 
         condition_operator, condition_value, message_template, priority_level)
        VALUES (notif_flexible_rules_seq.NEXTVAL, 
                'تنبيه الشحنات عالية القيمة',
                (SELECT id FROM notification_event_types WHERE event_code = 'high_value_shipment'),
                'field_value', 'total_value', '>', '100000',
                '⚠️ تنبيه: شحنة عالية القيمة {tracking_number} - القيمة: {total_value}',
                1)
        """
        
        db.execute_update(customs_rule)
        db.execute_update(high_value_rule)
        
        print("✅ تم إدراج القواعد النموذجية")
        
    except Exception as e:
        print(f"❌ خطأ في إدراج القواعد النموذجية: {e}")
    
    db.commit()

if __name__ == "__main__":
    create_flexible_rules_tables()
