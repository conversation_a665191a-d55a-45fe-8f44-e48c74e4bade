"""
محلل أرصدة المخزون من جدول ITEM_MOVEMENT
Inventory Balance Analyzer from ITEM_MOVEMENT Table
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class InventoryBalanceAnalyzer:
    """محلل أرصدة المخزون"""
    
    def __init__(self, db_connector):
        """
        تهيئة محلل الأرصدة
        
        Args:
            db_connector: موصل قاعدة البيانات
        """
        self.db_connector = db_connector
        self.movement_data = None
        self.balance_data = None
        self.analysis_results = {}
        
    def load_movement_data(self, limit=None, date_from=None, date_to=None):
        """
        تحميل بيانات حركة المخزون
        
        Args:
            limit: حد عدد الصفوف
            date_from: من تاريخ
            date_to: إلى تاريخ
        """
        try:
            query = """
            SELECT
                i_code,
                a_desc as i_desc,
                i_qty,
                free_qty,
                i_cost,
                stk_cost,
                w_code,
                whg_code,
                i_date,
                in_out,
                doc_type,
                doc_no,
                expire_date,
                batch_no
            FROM ITEM_MOVEMENT
            WHERE 1=1
            """
            
            # إضافة فلتر التاريخ
            if date_from:
                query += f" AND i_date >= TO_DATE('{date_from}', 'YYYY-MM-DD')"
            if date_to:
                query += f" AND i_date <= TO_DATE('{date_to}', 'YYYY-MM-DD')"
            
            # ترتيب حسب التاريخ والصنف
            query += " ORDER BY i_code, i_date"
            
            # إضافة حد الصفوف
            if limit:
                query = f"SELECT * FROM ({query}) WHERE ROWNUM <= {limit}"
            
            self.movement_data = self.db_connector.execute_query(query)
            
            if not self.movement_data.empty:
                logger.info(f"تم تحميل {len(self.movement_data)} حركة مخزون")
                return True
            else:
                logger.warning("لا توجد بيانات حركة مخزون")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات الحركة: {str(e)}")
            return False
    
    def calculate_balances(self):
        """حساب أرصدة الأصناف"""
        try:
            if self.movement_data is None or self.movement_data.empty:
                logger.error("لا توجد بيانات حركة لحساب الأرصدة")
                return False
            
            # تنظيف البيانات
            df = self.movement_data.copy()
            
            # التأكد من أن الكميات رقمية
            df['i_qty'] = pd.to_numeric(df['i_qty'], errors='coerce').fillna(0)
            df['free_qty'] = pd.to_numeric(df['free_qty'], errors='coerce').fillna(0)
            df['i_cost'] = pd.to_numeric(df['i_cost'], errors='coerce').fillna(0)
            
            # تحديد نوع الحركة (وارد/صادر)
            # in_out = 1 يعني وارد، in_out = 0 يعني صادر
            df['movement_type'] = df['in_out'].apply(lambda x: 'وارد' if pd.notna(x) and x == 1 else 'صادر')

            # حساب الكمية الصافية (موجبة للوارد، سالبة للصادر)
            df['net_qty'] = df.apply(lambda row:
                row['i_qty'] if (pd.notna(row['in_out']) and row['in_out'] == 1) else -row['i_qty'], axis=1)
            
            # حساب الرصيد التراكمي لكل صنف
            balance_list = []
            
            for item_code in df['i_code'].unique():
                item_movements = df[df['i_code'] == item_code].sort_values('i_date')
                
                running_balance = 0
                running_cost = 0
                
                for _, movement in item_movements.iterrows():
                    running_balance += movement['net_qty']
                    
                    # حساب متوسط التكلفة المرجح
                    if (pd.notna(movement['in_out']) and movement['in_out'] == 1 and
                        pd.notna(movement['i_qty']) and movement['i_qty'] > 0):  # وارد
                        prev_balance = running_balance - movement['net_qty']
                        total_cost = running_cost * prev_balance + movement['i_cost'] * movement['i_qty']
                        running_cost = total_cost / running_balance if running_balance > 0 else 0
                    
                    balance_list.append({
                        'i_code': movement['i_code'],
                        'i_desc': movement['i_desc'],
                        'w_code': movement['w_code'],
                        'movement_date': movement['i_date'],
                        'movement_type': movement['movement_type'],
                        'movement_qty': movement['i_qty'],
                        'net_qty': movement['net_qty'],
                        'running_balance': running_balance,
                        'unit_cost': running_cost,
                        'total_value': running_balance * running_cost,
                        'doc_type': movement['doc_type'],
                        'doc_no': movement['doc_no'],
                        'batch_no': movement['batch_no'],
                        'expire_date': movement['expire_date']
                    })
            
            # تحويل إلى DataFrame
            balance_df = pd.DataFrame(balance_list)
            
            # الحصول على آخر رصيد لكل صنف
            current_balances = balance_df.groupby('i_code').last().reset_index()
            
            # إضافة إحصائيات إضافية
            item_stats = df.groupby('i_code').agg({
                'i_desc': 'first',
                'net_qty': ['sum', 'count'],
                'i_date': ['min', 'max'],
                'w_code': lambda x: ', '.join(str(val) for val in x.unique() if pd.notna(val))
            })
            
            # تسطيح الأعمدة
            item_stats.columns = ['item_desc', 'total_movement', 'movement_count', 'first_movement', 'last_movement', 'warehouses']
            item_stats = item_stats.reset_index()
            
            # دمج البيانات
            self.balance_data = current_balances.merge(
                item_stats[['i_code', 'total_movement', 'movement_count', 'first_movement', 'last_movement', 'warehouses']], 
                on='i_code', 
                how='left'
            )
            
            logger.info(f"تم حساب أرصدة {len(self.balance_data)} صنف")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حساب الأرصدة: {str(e)}")
            return False
    
    def analyze_inventory_status(self):
        """تحليل حالة المخزون"""
        try:
            if self.balance_data is None or self.balance_data.empty:
                logger.error("لا توجد بيانات أرصدة للتحليل")
                return {}
            
            analysis = {
                'summary': {
                    'total_items': len(self.balance_data),
                    'items_with_stock': len(self.balance_data[self.balance_data['running_balance'] > 0]),
                    'items_out_of_stock': len(self.balance_data[self.balance_data['running_balance'] <= 0]),
                    'items_negative_stock': len(self.balance_data[self.balance_data['running_balance'] < 0]),
                    'total_inventory_value': self.balance_data['total_value'].sum(),
                    'average_item_value': self.balance_data['total_value'].mean()
                },
                'top_items_by_quantity': self.balance_data.nlargest(10, 'running_balance')[
                    ['i_code', 'i_desc', 'running_balance', 'unit_cost', 'total_value']
                ].to_dict('records'),
                'top_items_by_value': self.balance_data.nlargest(10, 'total_value')[
                    ['i_code', 'i_desc', 'running_balance', 'unit_cost', 'total_value']
                ].to_dict('records'),
                'negative_stock_items': self.balance_data[self.balance_data['running_balance'] < 0][
                    ['i_code', 'i_desc', 'running_balance', 'unit_cost', 'total_value']
                ].to_dict('records'),
                'zero_stock_items': self.balance_data[self.balance_data['running_balance'] == 0][
                    ['i_code', 'i_desc', 'last_movement']
                ].to_dict('records')
            }
            
            # تحليل حسب المستودع
            if 'w_code' in self.balance_data.columns:
                warehouse_analysis = self.balance_data.groupby('w_code').agg({
                    'running_balance': 'sum',
                    'total_value': 'sum',
                    'i_code': 'count'
                }).round(2)
                warehouse_analysis.columns = ['total_quantity', 'total_value', 'item_count']
                analysis['warehouse_summary'] = warehouse_analysis.to_dict('index')
            
            self.analysis_results = analysis
            return analysis
            
        except Exception as e:
            logger.error(f"خطأ في تحليل حالة المخزون: {str(e)}")
            return {}
    
    def get_item_movement_history(self, item_code):
        """الحصول على تاريخ حركة صنف معين"""
        try:
            if self.movement_data is None:
                return pd.DataFrame()
            
            item_movements = self.movement_data[
                self.movement_data['i_code'] == item_code
            ].sort_values('i_date')
            
            return item_movements
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على تاريخ الحركة: {str(e)}")
            return pd.DataFrame()
    
    def export_balances_to_excel(self, filename=None):
        """تصدير الأرصدة إلى Excel"""
        try:
            if self.balance_data is None or self.balance_data.empty:
                logger.error("لا توجد بيانات أرصدة للتصدير")
                return False
            
            if filename is None:
                filename = f"inventory_balances_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # الأرصدة الحالية
                self.balance_data.to_excel(writer, sheet_name='الأرصدة الحالية', index=False)
                
                # ملخص التحليل
                if self.analysis_results:
                    summary_df = pd.DataFrame([self.analysis_results['summary']])
                    summary_df.to_excel(writer, sheet_name='ملخص المخزون', index=False)
                
                # الأصناف ذات الرصيد السالب
                negative_items = self.balance_data[self.balance_data['running_balance'] < 0]
                if not negative_items.empty:
                    negative_items.to_excel(writer, sheet_name='أرصدة سالبة', index=False)
                
                # أعلى الأصناف قيمة
                top_value_items = self.balance_data.nlargest(50, 'total_value')
                top_value_items.to_excel(writer, sheet_name='أعلى قيمة', index=False)
            
            logger.info(f"تم تصدير الأرصدة إلى {filename}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تصدير الأرصدة: {str(e)}")
            return False
