#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تكامل نظام ترحيل أوامر الشراء مع النظام الأساسي
Purchase Order Posting Integration with Main System
"""

import sys
sys.path.append('.')
from oracle_manager import oracle_manager

def post_purchase_order_to_balance(po_id):
    """
    ترحيل أمر شراء إلى CURRENT_BALANCES
    Post Purchase Order to CURRENT_BALANCES
    
    Args:
        po_id (int): معرف أمر الشراء
        
    Returns:
        dict: نتيجة العملية مع التفاصيل
    """
    
    result = {
        'success': False,
        'message': '',
        'po_number': '',
        'supplier_code': '',
        'amount': 0,
        'currency': '',
        'balance_id': None
    }
    
    try:
        # الحصول على بيانات أمر الشراء
        po_data = oracle_manager.execute_query("""
            SELECT SUPPLIER_CODE, TOTAL_AMOUNT, NVL(CURRENCY, 'USD'), 
                   PO_NUMBER, SUPPLIER_NAME
            FROM PURCHASE_ORDERS WHERE ID = :1
        """, [po_id])
        
        if not po_data:
            result['message'] = f'أمر الشراء {po_id} غير موجود'
            return result
        
        supplier_code, total_amount, currency, po_number, supplier_name = po_data[0]
        
        # تحديث معلومات النتيجة
        result['po_number'] = po_number
        result['supplier_code'] = supplier_code
        result['amount'] = total_amount
        result['currency'] = currency
        
        if not supplier_code:
            result['message'] = f'أمر الشراء {po_number} لا يحتوي على كود مورد'
            return result
        
        # تحويل كود المورد إلى رقم
        supplier_code_num = int(supplier_code)
        
        # البحث عن الرصيد الحالي
        balance_data = oracle_manager.execute_query("""
            SELECT ID, CURRENT_BALANCE, DEBIT_AMOUNT
            FROM CURRENT_BALANCES
            WHERE entity_type_code = 'SUPPLIER' 
            AND entity_id = :1 AND currency_code = :2
        """, [supplier_code_num, currency])
        
        if balance_data:
            # تحديث رصيد موجود
            balance_id, current_balance, debit_amount = balance_data[0]
            new_debit = (debit_amount or 0) + total_amount
            new_balance = (current_balance or 0) + total_amount
            
            oracle_manager.execute_update("""
                UPDATE CURRENT_BALANCES SET
                    DEBIT_AMOUNT = :1,
                    CURRENT_BALANCE = :2,
                    TOTAL_TRANSACTIONS_COUNT = NVL(TOTAL_TRANSACTIONS_COUNT, 0) + 1,
                    LAST_TRANSACTION_DATE = SYSDATE,
                    LAST_DOCUMENT_TYPE = 'PURCHASE_ORDER',
                    LAST_DOCUMENT_NUMBER = :3,
                    UPDATED_AT = CURRENT_TIMESTAMP,
                    UPDATED_BY = 1
                WHERE ID = :4
            """, [new_debit, new_balance, po_number, balance_id])
            
            result['balance_id'] = balance_id
            result['message'] = f'تم تحديث رصيد المورد {supplier_name} ({supplier_code})'
            
        else:
            # إنشاء رصيد جديد
            new_balance_id = oracle_manager.execute_query("SELECT CURRENT_BALANCES_SEQ.NEXTVAL FROM DUAL")[0][0]
            
            oracle_manager.execute_update("""
                INSERT INTO CURRENT_BALANCES (
                    ID, ENTITY_TYPE_CODE, ENTITY_ID, CURRENCY_CODE,
                    OPENING_BALANCE, DEBIT_AMOUNT, CREDIT_AMOUNT, CURRENT_BALANCE,
                    TOTAL_TRANSACTIONS_COUNT, LAST_TRANSACTION_DATE,
                    LAST_DOCUMENT_TYPE, LAST_DOCUMENT_NUMBER,
                    CREATED_AT, UPDATED_AT, CREATED_BY, UPDATED_BY
                ) VALUES (
                    :1, 'SUPPLIER', :2, :3, 0, :4, 0, :5, 1, SYSDATE,
                    'PURCHASE_ORDER', :6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1
                )
            """, [new_balance_id, supplier_code_num, currency, total_amount, total_amount, po_number])
            
            result['balance_id'] = new_balance_id
            result['message'] = f'تم إنشاء رصيد جديد للمورد {supplier_name} ({supplier_code})'
        
        # تسجيل العملية
        oracle_manager.execute_update("""
            INSERT INTO SYSTEM_LOGS (
                id, log_type, table_name, operation, record_id, log_message, created_at
            ) VALUES (
                SYSTEM_LOGS_SEQ.NEXTVAL, 'PO_POSTING', 'CURRENT_BALANCES', 'INSERT', :1,
                :2, SYSDATE
            )
        """, [po_id, f'ترحيل أمر الشراء {po_number} للمورد {supplier_code} بمبلغ {total_amount} {currency}'])
        
        oracle_manager.commit()
        result['success'] = True
        
    except Exception as e:
        oracle_manager.rollback()
        result['message'] = f'خطأ في الترحيل: {str(e)}'
        
        # تسجيل الخطأ
        try:
            oracle_manager.execute_update("""
                INSERT INTO SYSTEM_LOGS (
                    id, log_type, table_name, operation, record_id, log_message, created_at
                ) VALUES (
                    SYSTEM_LOGS_SEQ.NEXTVAL, 'PO_POSTING_ERROR', 'CURRENT_BALANCES', 'INSERT', :1,
                    :2, SYSDATE
                )
            """, [po_id, f'خطأ في ترحيل أمر الشراء: {str(e)}'])
            oracle_manager.commit()
        except:
            pass
    
    return result

def integrate_with_purchase_orders_routes():
    """دمج نظام الترحيل مع routes أوامر الشراء"""
    
    print("🔧 دمج نظام الترحيل مع routes أوامر الشراء")
    print("=" * 60)
    
    integration_code = '''
# إضافة هذا الكود إلى app/purchase_orders/routes.py

from po_posting_integration import post_purchase_order_to_balance

# في دالة save_purchase_order بعد حفظ أمر الشراء:
if po_id:
    # ترحيل أمر الشراء إلى أرصدة الموردين
    posting_result = post_purchase_order_to_balance(po_id)
    
    if posting_result['success']:
        logger.info(f"✅ تم ترحيل أمر الشراء {posting_result['po_number']} بنجاح")
        logger.info(f"💰 المبلغ: {posting_result['amount']} {posting_result['currency']}")
        logger.info(f"👤 المورد: {posting_result['supplier_code']}")
    else:
        logger.warning(f"⚠️ فشل في ترحيل أمر الشراء: {posting_result['message']}")

# في دالة update_purchase_order بعد تحديث أمر الشراء:
if result > 0:
    # إعادة ترحيل أمر الشراء المُحدث
    posting_result = post_purchase_order_to_balance(po_id)
    
    if posting_result['success']:
        logger.info(f"✅ تم إعادة ترحيل أمر الشراء المُحدث")
    else:
        logger.warning(f"⚠️ فشل في إعادة ترحيل أمر الشراء المُحدث")
'''
    
    print("📋 كود التكامل:")
    print(integration_code)
    
    print("\n💡 التعليمات:")
    print("   1. انسخ الكود أعلاه")
    print("   2. أضفه إلى app/purchase_orders/routes.py")
    print("   3. أعد تشغيل الخادم")
    print("   4. سيتم ترحيل أوامر الشراء تلقائياً")

def test_integration():
    """اختبار التكامل"""
    
    print("\n🧪 اختبار التكامل")
    print("=" * 60)
    
    # اختبار ترحيل أمر شراء موجود
    test_po_id = 1
    
    print(f"🔧 اختبار ترحيل أمر الشراء ID: {test_po_id}")
    
    result = post_purchase_order_to_balance(test_po_id)
    
    if result['success']:
        print("✅ نجح الاختبار!")
        print(f"   📄 رقم الأمر: {result['po_number']}")
        print(f"   👤 المورد: {result['supplier_code']}")
        print(f"   💰 المبلغ: {result['amount']} {result['currency']}")
        print(f"   📋 الرسالة: {result['message']}")
        
        # التحقق من الرصيد
        balance_check = oracle_manager.execute_query("""
            SELECT current_balance, debit_amount, last_document_number
            FROM CURRENT_BALANCES 
            WHERE entity_type_code = 'SUPPLIER' AND entity_id = :1
        """, [int(result['supplier_code'])])
        
        if balance_check:
            balance = balance_check[0]
            print(f"\n💰 تأكيد الرصيد:")
            print(f"   💰 الرصيد الحالي: {balance[0]}")
            print(f"   📈 إجمالي المدين: {balance[1]}")
            print(f"   📄 آخر مستند: {balance[2]}")
            
            return True
    else:
        print("❌ فشل الاختبار!")
        print(f"   📋 الرسالة: {result['message']}")
        return False

def show_posting_summary():
    """عرض ملخص نظام الترحيل"""
    
    print("\n📊 ملخص نظام الترحيل")
    print("=" * 60)
    
    try:
        # إحصائيات الترحيل
        posting_stats = oracle_manager.execute_query("""
            SELECT 
                COUNT(*) as total_suppliers,
                SUM(current_balance) as total_balance,
                COUNT(CASE WHEN last_document_type = 'PURCHASE_ORDER' THEN 1 END) as po_related
            FROM CURRENT_BALANCES 
            WHERE entity_type_code = 'SUPPLIER'
        """)
        
        if posting_stats:
            stats = posting_stats[0]
            print(f"📊 إحصائيات الترحيل:")
            print(f"   👥 إجمالي الموردين: {stats[0]}")
            print(f"   💰 إجمالي الأرصدة: {stats[1] or 0}")
            print(f"   📄 مرتبطة بأوامر الشراء: {stats[2]}")
        
        # آخر عمليات الترحيل
        recent_postings = oracle_manager.execute_query("""
            SELECT log_message, created_at
            FROM SYSTEM_LOGS 
            WHERE log_type = 'PO_POSTING'
            ORDER BY created_at DESC
            FETCH FIRST 5 ROWS ONLY
        """)
        
        if recent_postings:
            print(f"\n📝 آخر عمليات الترحيل:")
            for posting in recent_postings:
                print(f"   📋 {posting[0]} | 📅 {posting[1]}")
        
        print(f"\n🎯 حالة النظام:")
        print(f"   ✅ نظام الترقيم الذكي للـ ID يعمل")
        print(f"   ✅ نظام الترقيم الذكي للـ PO_NUMBER يعمل")
        print(f"   ✅ نظام ترحيل أوامر الشراء يعمل")
        print(f"   🎉 النظام متكامل وجاهز للاستخدام!")
        
    except Exception as e:
        print(f"❌ خطأ في عرض الملخص: {e}")

if __name__ == "__main__":
    print("🚀 تكامل نظام ترحيل أوامر الشراء")
    print("📅 التاريخ: 2025-09-06")
    print("🕒 الوقت: 00:15")
    
    # اختبار التكامل
    if test_integration():
        # عرض كود التكامل
        integrate_with_purchase_orders_routes()
        
        # عرض الملخص
        show_posting_summary()
        
        print("\n" + "=" * 60)
        print("🎉 تم إنشاء نظام الترحيل المتكامل بنجاح!")
        print("✅ أوامر الشراء تُرحل الآن إلى CURRENT_BALANCES")
        print("🔧 استخدم post_purchase_order_to_balance(po_id) للترحيل")
        print("📊 راجع CURRENT_BALANCES لمتابعة أرصدة الموردين")
        print("📝 راجع SYSTEM_LOGS لمتابعة عمليات الترحيل")
    else:
        print("\n❌ فشل في اختبار التكامل")
