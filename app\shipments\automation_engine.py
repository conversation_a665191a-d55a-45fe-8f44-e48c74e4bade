"""
نظام محرك الأتمتة
Automation Engine System
"""

import time
import json
from datetime import datetime
from database_manager import DatabaseManager
from flask import current_app


class AutomationEngine:
    """محرك تنفيذ قواعد الأتمتة"""
    
    def __init__(self):
        self.db_manager = None
        
    def get_db_manager(self):
        """الحصول على مدير قاعدة البيانات"""
        if not self.db_manager:
            self.db_manager = DatabaseManager()
        return self.db_manager
    
    def close_db(self):
        """إغلاق اتصال قاعدة البيانات"""
        if self.db_manager:
            self.db_manager.close()
            self.db_manager = None
    
    def log_execution(self, rule_id, shipment_id, trigger_condition, condition_value, 
                     action_type, status, result=None, error=None, duration=None):
        """تسجيل تنفيذ قاعدة أتمتة"""
        try:
            db = self.get_db_manager()
            
            insert_query = """
                INSERT INTO automation_execution_log (
                    id, rule_id, shipment_id, trigger_condition, condition_value,
                    action_type, execution_status, execution_result, error_message,
                    execution_duration, executed_at, created_by
                ) VALUES (
                    automation_execution_log_seq.NEXTVAL, :rule_id, :shipment_id, 
                    :trigger_condition, :condition_value, :action_type, :status,
                    :result, :error, :duration, CURRENT_TIMESTAMP, 1
                )
            """
            
            params = {
                'rule_id': rule_id,
                'shipment_id': shipment_id,
                'trigger_condition': trigger_condition,
                'condition_value': condition_value,
                'action_type': action_type,
                'status': status,
                'result': str(result) if result else None,
                'error': str(error) if error else None,
                'duration': duration
            }
            
            db.execute_update(insert_query, params)
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تسجيل تنفيذ الأتمتة: {e}")
            return False
    
    def get_active_rules(self):
        """جلب القواعد النشطة"""
        try:
            db = self.get_db_manager()
            
            query = """
                SELECT id, rule_name, rule_type, trigger_condition, action_type,
                       condition_value, priority_level, selected_agent_id,
                       agent_selection_criteria, auto_agent_selection, rule_config
                FROM automation_rules
                WHERE is_active = 1
                ORDER BY priority_level DESC, id
            """
            
            result = db.execute_query(query)
            
            rules = []
            if result:
                for row in result:
                    # معالجة rule_config إذا كان CLOB
                    rule_config = row[10] if len(row) > 10 else None
                    if rule_config and hasattr(rule_config, 'read'):
                        rule_config = rule_config.read()

                    rules.append({
                        'id': row[0],
                        'rule_name': row[1],
                        'rule_type': row[2],
                        'trigger_condition': row[3],
                        'action_type': row[4],
                        'condition_value': row[5],
                        'priority_level': row[6],
                        'selected_agent_id': row[7],
                        'agent_selection_criteria': row[8],
                        'auto_agent_selection': bool(row[9]) if row[9] else False,
                        'rule_config': rule_config
                    })
            
            return rules
            
        except Exception as e:
            print(f"❌ خطأ في جلب القواعد النشطة: {e}")
            return []
    
    def check_condition(self, rule, shipment_data):
        """فحص شرط القاعدة"""
        try:
            trigger_condition = rule['trigger_condition']
            condition_value = rule['condition_value']
            
            if trigger_condition == 'STATUS_CHANGE_ACTION':
                # فحص تغيير الحالة
                return shipment_data.get('new_status') == condition_value
            
            elif trigger_condition == 'STATUS_EQUALS':
                # فحص مساواة الحالة
                return shipment_data.get('current_status') == condition_value
            
            elif trigger_condition == 'STATUS_NOT_EQUALS':
                # فحص عدم مساواة الحالة
                return shipment_data.get('current_status') != condition_value
            
            # يمكن إضافة شروط أخرى هنا
            
            return False
            
        except Exception as e:
            print(f"❌ خطأ في فحص الشرط: {e}")
            return False
    
    def execute_action(self, rule, shipment_data):
        """تنفيذ إجراء القاعدة"""
        start_time = time.time()
        
        try:
            action_type = rule['action_type']
            shipment_id = shipment_data['shipment_id']
            
            if action_type == 'CREATE_DELIVERY_ORDER_WITH_AGENT':
                return self._create_delivery_order_with_agent(rule, shipment_data)
            
            elif action_type == 'ASSIGN_AGENT':
                return self._assign_agent(rule, shipment_data)
            
            elif action_type == 'SEND_NOTIFICATION':
                return self._send_notification(rule, shipment_data)
            
            elif action_type == 'UPDATE_RATINGS':
                return self._update_ratings(rule, shipment_data)

            elif action_type == 'SEND_WHATSAPP_NOTIFICATION':
                return self._send_whatsapp_notification(rule, shipment_data)

            else:
                return {
                    'success': False,
                    'message': f'نوع الإجراء غير مدعوم: {action_type}'
                }
                
        except Exception as e:
            duration = int((time.time() - start_time) * 1000)
            return {
                'success': False,
                'message': f'خطأ في تنفيذ الإجراء: {str(e)}',
                'duration': duration
            }
    
    def _create_delivery_order_with_agent(self, rule, shipment_data):
        """إنشاء أمر تسليم وتعيين مخلص"""
        try:
            db = self.get_db_manager()
            shipment_id = shipment_data['shipment_id']

            # التحقق من وجود الشحنة في CARGO_SHIPMENTS
            shipment_check_query = "SELECT id FROM cargo_shipments WHERE id = :shipment_id"
            shipment_exists = db.execute_query(shipment_check_query, {'shipment_id': shipment_id})

            if not shipment_exists:
                print(f"⚠️ الشحنة {shipment_id} غير موجودة في CARGO_SHIPMENTS، سيتم تسجيل النشاط فقط")
                return {
                    'success': True,
                    'message': f'تم تسجيل طلب إنشاء أمر تسليم للشحنة {shipment_id} (الشحنة غير موجودة في النظام الفرعي)',
                    'note': 'تم التسجيل كنشاط فقط'
                }

            # تحديد المخلص
            agent_id = None
            if rule['auto_agent_selection']:
                # اختيار تلقائي للمخلص
                agent_id = self._select_best_agent(rule['agent_selection_criteria'])
            else:
                # مخلص محدد مسبقاً
                agent_id = rule['selected_agent_id']

            if not agent_id:
                return {
                    'success': False,
                    'message': 'لم يتم العثور على مخلص مناسب'
                }
            
            # إنشاء أمر التسليم المتقدم
            # أولاً: الحصول على الرقم التسلسلي التالي
            next_id_query = "SELECT delivery_orders_seq.NEXTVAL FROM dual"
            next_id_result = db.execute_query(next_id_query)
            next_id = next_id_result[0][0] if next_id_result else 1

            # ثانياً: إنشاء رقم الأمر
            order_number = f"DO-{datetime.now().strftime('%Y%m%d')}-{next_id:06d}"

            # ثالثاً: جلب بيانات المخلص والفرع
            agent_query = """
                SELECT ca.agent_name, ca.branch_id, b.brn_lname, b.brn_ladd
                FROM customs_agents ca
                LEFT JOIN branches b ON ca.branch_id = b.brn_no
                WHERE ca.id = :agent_id
            """

            agent_result = db.execute_query(agent_query, {'agent_id': agent_id})

            if agent_result:
                agent_data = agent_result[0]
                agent_name = agent_data[0]
                branch_id = agent_data[1]
                branch_name = agent_data[2]
                branch_address = agent_data[3]

                print(f"📋 بيانات المخلص: {agent_name} - فرع: {branch_name}")
            else:
                # قيم افتراضية إذا لم توجد بيانات
                branch_id = None
                branch_name = "غير محدد"
                branch_address = "غير محدد"

            # رابعاً: إدراج أمر التسليم مع بيانات الفرع
            delivery_order_query = """
                INSERT INTO delivery_orders (
                    id, order_number, shipment_id, customs_agent_id, branch_id,
                    order_status, created_date, created_by, priority
                ) VALUES (
                    :order_id, :order_number, :shipment_id, :agent_id, :branch_id,
                    'pending', SYSDATE, 1, 'high'
                )
            """

            db.execute_update(delivery_order_query, {
                'order_id': next_id,
                'order_number': order_number,
                'shipment_id': shipment_id,
                'agent_id': agent_id,
                'branch_id': branch_id
            })
            
            return {
                'success': True,
                'message': f'تم إنشاء أمر تسليم {order_number} وتعيين المخلص {agent_id} من فرع {branch_name}',
                'order_number': order_number,
                'agent_id': agent_id,
                'branch_id': branch_id,
                'branch_name': branch_name,
                'order_id': next_id
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'خطأ في إنشاء أمر التسليم: {str(e)}'
            }
    
    def _assign_agent(self, rule, shipment_data):
        """تعيين مخلص للشحنة"""
        try:
            # منطق تعيين المخلص
            return {
                'success': True,
                'message': 'تم تعيين المخلص بنجاح'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'خطأ في تعيين المخلص: {str(e)}'
            }
    
    def _send_notification(self, rule, shipment_data):
        """إرسال إشعار حسب جهات الاتصال المحددة في القاعدة"""
        try:
            print(f"📧 بدء إرسال إشعار للقاعدة {rule['id']}")

            shipment_id = shipment_data['shipment_id']
            new_status = shipment_data['new_status']
            old_status = shipment_data['old_status']

            # جلب معلومات الشحنة الأساسية
            shipment_query = """
                SELECT tracking_number, documents_recipient_name
                FROM cargo_shipments
                WHERE id = :1
            """

            db = self.get_db_manager()
            shipment_result = db.execute_query(shipment_query, [shipment_id])

            if not shipment_result:
                return {
                    'success': False,
                    'message': 'لم يتم العثور على معلومات الشحنة'
                }

            tracking_number, recipient_name = shipment_result[0]

            # تحليل إعدادات القاعدة
            rule_config = {}
            if rule.get('rule_config'):
                try:
                    import json
                    if hasattr(rule['rule_config'], 'read'):
                        config_text = rule['rule_config'].read()
                    else:
                        config_text = str(rule['rule_config'])

                    rule_config = json.loads(config_text)
                    print(f"📋 إعدادات القاعدة: {rule_config}")
                except Exception as config_error:
                    print(f"⚠️ خطأ في تحليل إعدادات القاعدة: {config_error}")
                    rule_config = {}

            # جلب جهات الاتصال من إعدادات القاعدة
            contacts_to_notify = []

            print(f"🔍 فحص إعدادات القاعدة: {rule_config}")

            # 1. جهات الاتصال المخصصة
            custom_contacts = rule_config.get('selected_custom_contacts', [])
            print(f"📞 جهات الاتصال المخصصة الخام: {custom_contacts}")

            if custom_contacts:
                try:
                    if isinstance(custom_contacts, str):
                        custom_contacts = json.loads(custom_contacts)

                    if isinstance(custom_contacts, list):
                        for contact in custom_contacts:
                            if isinstance(contact, dict):
                                contact_info = {
                                    'name': contact.get('name', 'غير محدد'),
                                    'phone': contact.get('phone'),
                                    'email': contact.get('email')
                                }
                                contacts_to_notify.append(contact_info)
                                print(f"✅ تمت إضافة جهة اتصال: {contact_info}")

                    print(f"📞 إجمالي جهات الاتصال المخصصة: {len(contacts_to_notify)}")
                except Exception as e:
                    print(f"⚠️ خطأ في معالجة جهات الاتصال المخصصة: {e}")
                    import traceback
                    traceback.print_exc()

            # 2. مجموعات جهات الاتصال
            contact_groups = rule_config.get('selected_contact_groups', [])
            print(f"👥 مجموعات جهات الاتصال: {contact_groups}")

            if contact_groups:
                try:
                    if isinstance(contact_groups, str):
                        contact_groups = json.loads(contact_groups)

                    if isinstance(contact_groups, list):
                        for group in contact_groups:
                            group_contacts = self._get_contacts_by_group(group)
                            contacts_to_notify.extend(group_contacts)
                            print(f"👥 تمت إضافة {len(group_contacts)} جهة اتصال من مجموعة {group}")

                    print(f"👥 إجمالي جهات الاتصال من المجموعات: {len(contact_groups)} مجموعة")
                except Exception as e:
                    print(f"⚠️ خطأ في معالجة مجموعات جهات الاتصال: {e}")
                    import traceback
                    traceback.print_exc()

            print(f"📋 إجمالي جهات الاتصال للإشعار: {len(contacts_to_notify)}")

            # إذا لم توجد جهات اتصال محفوظة، لا نرسل إشعارات
            if not contacts_to_notify:
                print("⚠️ لم يتم العثور على جهات اتصال محفوظة في القاعدة")
                return {
                    'success': False,
                    'message': 'لا توجد جهات اتصال محفوظة في القاعدة لإرسال الإشعارات',
                    'notifications_sent': 0,
                    'errors': ['لا توجد جهات اتصال محفوظة']
                }

            # إعداد رسالة الإشعار
            message_template = rule_config.get('message_template',
                'تم تغيير حالة الشحنة {tracking_number} من {old_status} إلى {new_status}')

            message = message_template.format(
                tracking_number=tracking_number,
                old_status=old_status,
                new_status=new_status,
                recipient_name=recipient_name or 'غير محدد'
            )

            # قنوات الإشعار
            channels = rule_config.get('selected_notification_channels', ['SMS'])
            if isinstance(channels, str):
                channels = json.loads(channels)

            notifications_sent = 0
            errors = []

            print(f"📋 سيتم إرسال الإشعار لـ {len(contacts_to_notify)} جهة اتصال عبر {channels}")

            # إرسال الإشعارات لجميع جهات الاتصال
            for contact in contacts_to_notify:
                contact_name = contact.get('name', 'غير محدد')
                contact_phone = contact.get('phone')
                contact_email = contact.get('email')

                print(f"📞 إرسال إشعار لـ {contact_name}")

                # إرسال SMS
                if 'SMS' in channels and contact_phone:
                    try:
                        sms_result = self._send_sms_notification(contact_phone, message)
                        if sms_result:
                            notifications_sent += 1
                            print(f"✅ تم إرسال SMS إلى {contact_name} ({contact_phone})")
                        else:
                            errors.append(f"فشل إرسال SMS إلى {contact_name}")
                    except Exception as sms_error:
                        errors.append(f"خطأ في إرسال SMS لـ {contact_name}: {sms_error}")

                # إرسال EMAIL
                if 'EMAIL' in channels and contact_email:
                    try:
                        email_result = self._send_email_notification(
                            contact_email,
                            f"تحديث حالة الشحنة {tracking_number}",
                            message
                        )
                        if email_result:
                            notifications_sent += 1
                            print(f"✅ تم إرسال EMAIL إلى {contact_name} ({contact_email})")
                        else:
                            errors.append(f"فشل إرسال EMAIL إلى {contact_name}")
                    except Exception as email_error:
                        errors.append(f"خطأ في إرسال EMAIL لـ {contact_name}: {email_error}")

                # إرسال WhatsApp
                if 'WHATSAPP' in channels and contact_phone:
                    try:
                        whatsapp_result = self._send_whatsapp_notification_direct(contact_phone, message)
                        if whatsapp_result:
                            notifications_sent += 1
                            print(f"✅ تم إرسال WhatsApp إلى {contact_name} ({contact_phone})")
                        else:
                            errors.append(f"فشل إرسال WhatsApp إلى {contact_name}")
                    except Exception as whatsapp_error:
                        errors.append(f"خطأ في إرسال WhatsApp لـ {contact_name}: {whatsapp_error}")

            if notifications_sent > 0:
                return {
                    'success': True,
                    'message': f'تم إرسال {notifications_sent} إشعار بنجاح',
                    'notifications_sent': notifications_sent,
                    'errors': errors
                }
            else:
                return {
                    'success': False,
                    'message': f'فشل في إرسال الإشعارات: {"; ".join(errors)}',
                    'errors': errors
                }

        except Exception as e:
            print(f"❌ خطأ في إرسال الإشعار: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'message': f'خطأ في إرسال الإشعار: {str(e)}'
            }
    
    def _update_ratings(self, rule, shipment_data):
        """تحديث التقييمات"""
        try:
            # منطق تحديث التقييمات
            return {
                'success': True,
                'message': 'تم تحديث التقييمات بنجاح'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'خطأ في تحديث التقييمات: {str(e)}'
            }

    def _send_whatsapp_notification(self, rule, shipment_data):
        """إرسال إشعار واتساب لأمر التسليم - معطل لتجنب التكرار"""
        # تم تعطيل هذا النظام لتجنب التكرار مع خدمة الأتمتة المستقلة
        return {
            'success': True,
            'message': 'تم تخطي الإرسال - يتم استخدام خدمة الأتمتة المستقلة'
        }

        # الكود القديم معطل
        try:
            # تعيين متغيرات Green API
            import os
            import sys
            os.environ['GREEN_API_INSTANCE_ID'] = '7105306929'
            os.environ['GREEN_API_TOKEN'] = '0260de395caa4adfb2aa02d53511f9ffa19b29b41f094fd68c'
            os.environ['GREEN_API_URL'] = 'https://7105.api.greenapi.com'
            os.environ['GREEN_API_TEST_MODE'] = 'false'

            # استيراد خدمة الواتساب
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'services'))
            from green_whatsapp_service import green_whatsapp_service as whatsapp_service

            # البحث عن أمر التسليم المرتبط بالشحنة
            db = self.get_db_manager()

            delivery_order_query = """
                SELECT
                    do.id,
                    do.order_number,
                    do.shipment_id,
                    do.customs_agent_id,
                    ca.agent_name,
                    do.branch_id,
                    b.brn_lname as branch_name,
                    do.created_date,
                    do.order_status
                FROM delivery_orders do
                LEFT JOIN customs_agents ca ON do.customs_agent_id = ca.id
                LEFT JOIN branches b ON do.branch_id = b.brn_no
                WHERE do.shipment_id = :shipment_id
                ORDER BY do.created_date DESC
                FETCH FIRST 1 ROWS ONLY
            """

            delivery_result = db.execute_query(delivery_order_query, {
                'shipment_id': shipment_data['shipment_id']
            })

            if not delivery_result:
                return {
                    'success': False,
                    'message': 'لم يتم العثور على أمر تسليم للشحنة'
                }

            # تحضير بيانات أمر التسليم
            delivery_row = delivery_result[0]
            order_data = {
                'id': delivery_row[0],
                'order_number': delivery_row[1],
                'tracking_number': f"TRK-{delivery_row[2]}",
                'booking_number': f"BK-{delivery_row[2]}",
                'delivery_location': delivery_row[6] or 'عدن، اليمن',
                'expected_completion_date': str(delivery_row[7]),
                'contact_person': delivery_row[4],
                'priority': 'normal'
            }

            # جلب رقم هاتف المخلص
            agent_phone = self._get_agent_phone_for_whatsapp(delivery_row[3])

            if not agent_phone:
                return {
                    'success': False,
                    'message': f'رقم هاتف المخلص {delivery_row[4]} غير متوفر'
                }

            # إرسال رسالة الواتساب مع PDF
            success, message, msg_id = whatsapp_service.send_delivery_order(order_data, agent_phone, include_pdf=True)

            if success:
                # تسجيل نجاح الإرسال
                print(f"✅ تم إرسال أمر التسليم {order_data['order_number']} عبر الواتساب إلى {agent_phone}")
                return {
                    'success': True,
                    'message': f'تم إرسال أمر التسليم {order_data["order_number"]} عبر الواتساب إلى المخلص {delivery_row[4]}',
                    'whatsapp_message_id': msg_id,
                    'phone_number': agent_phone
                }
            else:
                return {
                    'success': False,
                    'message': f'فشل إرسال الواتساب للمخلص {delivery_row[4]}: {message}'
                }

        except Exception as e:
            print(f"❌ خطأ في إرسال إشعار الواتساب: {e}")
            return {
                'success': False,
                'message': f'خطأ في إرسال إشعار الواتساب: {str(e)}'
            }

    def _get_agent_phone_for_whatsapp(self, agent_id):
        """جلب رقم هاتف المخلص للواتساب"""
        try:
            db = self.get_db_manager()

            # البحث في جدول المخلصين
            agent_phone_query = """
                SELECT phone, mobile
                FROM customs_agents
                WHERE id = :agent_id
                AND is_active = 1
            """

            result = db.execute_query(agent_phone_query, {'agent_id': agent_id})

            if result:
                agent_row = result[0]
                # استخدام الجوال أولاً، ثم الهاتف العادي
                phone = agent_row[1] or agent_row[0]

                if phone:
                    # تنسيق رقم الهاتف لليمن
                    phone = str(phone).strip()
                    if phone.startswith('0'):
                        phone = '+967' + phone[1:]
                    elif not phone.startswith('+'):
                        phone = '+967' + phone

                    return phone

            # رقم افتراضي للاختبار
            print(f"⚠️ لم يتم العثور على رقم هاتف للمخلص {agent_id}")
            return None

        except Exception as e:
            print(f"❌ خطأ في جلب رقم هاتف المخلص: {e}")
            return None

    def _select_best_agent(self, criteria):
        """اختيار أفضل مخلص حسب المعايير المتقدمة"""
        try:
            db = self.get_db_manager()

            if criteria == 'rating':
                # اختيار حسب أعلى تقييم مع أقل عدد أوامر حالية
                query = """
                    SELECT ca.id, ca.agent_name, ca.rating, ca.total_orders,
                           ca.branch_id, b.brn_lname
                    FROM customs_agents ca
                    LEFT JOIN branches b ON ca.branch_id = b.brn_no
                    WHERE ca.is_active = 1
                    AND ca.rating IS NOT NULL
                    ORDER BY ca.rating DESC, ca.total_orders ASC, ca.average_completion_days ASC
                    FETCH FIRST 1 ROWS ONLY
                """
            elif criteria == 'experience':
                # اختيار حسب الخبرة (عدد الأوامر المكتملة)
                query = """
                    SELECT ca.id, ca.agent_name, ca.completed_orders, ca.average_completion_days,
                           ca.branch_id, b.brn_lname
                    FROM customs_agents ca
                    LEFT JOIN branches b ON ca.branch_id = b.brn_no
                    WHERE ca.is_active = 1
                    AND ca.completed_orders IS NOT NULL
                    ORDER BY ca.completed_orders DESC, ca.average_completion_days ASC, ca.rating DESC
                    FETCH FIRST 1 ROWS ONLY
                """
            elif criteria == 'speed':
                # اختيار حسب سرعة الإنجاز
                query = """
                    SELECT ca.id, ca.agent_name, ca.average_completion_days, ca.rating,
                           ca.branch_id, b.brn_lname
                    FROM customs_agents ca
                    LEFT JOIN branches b ON ca.branch_id = b.brn_no
                    WHERE ca.is_active = 1
                    AND ca.average_completion_days IS NOT NULL
                    ORDER BY ca.average_completion_days ASC, ca.rating DESC, ca.completed_orders DESC
                    FETCH FIRST 1 ROWS ONLY
                """
            else:
                # افتراضي: توازن بين التقييم والخبرة والسرعة
                query = """
                    SELECT ca.id, ca.agent_name,
                           (COALESCE(ca.rating, 0) * 0.4 +
                            COALESCE(ca.completed_orders, 0) * 0.3 +
                            (10 - COALESCE(ca.average_completion_days, 10)) * 0.3) as score,
                           ca.branch_id, b.brn_lname
                    FROM customs_agents ca
                    LEFT JOIN branches b ON ca.branch_id = b.brn_no
                    WHERE ca.is_active = 1
                    ORDER BY score DESC
                    FETCH FIRST 1 ROWS ONLY
                """

            result = db.execute_query(query)

            if result:
                agent_id = result[0][0]
                agent_name = result[0][1] if len(result[0]) > 1 else f"مخلص {agent_id}"
                branch_name = result[0][-1] if len(result[0]) > 4 else "غير محدد"  # آخر عمود هو branch_name
                print(f"🎯 تم اختيار المخلص: {agent_name} من فرع {branch_name} (ID: {agent_id}) حسب معيار: {criteria}")
                return agent_id
            else:
                # إذا لم يوجد مخلص حسب المعايير، اختر أي مخلص نشط
                fallback_query = "SELECT id FROM customs_agents WHERE is_active = 1 AND ROWNUM = 1"
                fallback_result = db.execute_query(fallback_query)

                if fallback_result:
                    agent_id = fallback_result[0][0]
                    print(f"⚠️ تم اختيار مخلص احتياطي: {agent_id}")
                    return agent_id
                else:
                    print("❌ لا يوجد مخلصون نشطون في النظام")
                    return None

        except Exception as e:
            print(f"❌ خطأ في اختيار المخلص: {e}")
            # محاولة اختيار أي مخلص متاح كحل طوارئ
            try:
                db = self.get_db_manager()
                emergency_query = "SELECT id FROM customs_agents WHERE ROWNUM = 1"
                emergency_result = db.execute_query(emergency_query)

                if emergency_result:
                    agent_id = emergency_result[0][0]
                    print(f"🚨 تم اختيار مخلص طوارئ: {agent_id}")
                    return agent_id
            except:
                pass

            return None
    
    def process_shipment_status_change(self, shipment_id, old_status, new_status):
        """معالجة تغيير حالة الشحنة"""
        print(f"🔄 معالجة تغيير حالة الشحنة {shipment_id}: {old_status} → {new_status}")
        
        try:
            # جلب القواعد النشطة
            rules = self.get_active_rules()
            
            if not rules:
                print("⚠️ لا توجد قواعد أتمتة نشطة")
                return
            
            # بيانات الشحنة
            shipment_data = {
                'shipment_id': shipment_id,
                'old_status': old_status,
                'new_status': new_status,
                'current_status': new_status
            }
            
            executed_count = 0
            
            # فحص كل قاعدة
            for rule in rules:
                try:
                    # فحص الشرط
                    if self.check_condition(rule, shipment_data):
                        print(f"✅ القاعدة {rule['id']} ({rule['rule_name']}) تطابق الشرط")
                        
                        # تسجيل بداية التنفيذ
                        self.log_execution(
                            rule['id'], shipment_id, rule['trigger_condition'],
                            rule['condition_value'], rule['action_type'], 'RUNNING'
                        )
                        
                        # تنفيذ الإجراء
                        start_time = time.time()
                        result = self.execute_action(rule, shipment_data)
                        duration = int((time.time() - start_time) * 1000)
                        
                        # تسجيل النتيجة
                        if result['success']:
                            self.log_execution(
                                rule['id'], shipment_id, rule['trigger_condition'],
                                rule['condition_value'], rule['action_type'], 'SUCCESS',
                                result['message'], None, duration
                            )
                            print(f"✅ تم تنفيذ القاعدة {rule['id']} بنجاح: {result['message']}")
                            executed_count += 1
                        else:
                            self.log_execution(
                                rule['id'], shipment_id, rule['trigger_condition'],
                                rule['condition_value'], rule['action_type'], 'FAILED',
                                None, result['message'], duration
                            )
                            print(f"❌ فشل تنفيذ القاعدة {rule['id']}: {result['message']}")
                    
                except Exception as e:
                    print(f"❌ خطأ في معالجة القاعدة {rule['id']}: {e}")
                    self.log_execution(
                        rule['id'], shipment_id, rule['trigger_condition'],
                        rule['condition_value'], rule['action_type'], 'FAILED',
                        None, str(e), None
                    )
            
            print(f"🎉 تم تنفيذ {executed_count} قاعدة من أصل {len(rules)} قاعدة")
            
        except Exception as e:
            print(f"❌ خطأ في معالجة تغيير حالة الشحنة: {e}")
        
        finally:
            self.close_db()

    def _send_sms_notification(self, phone_number, message):
        """إرسال SMS"""
        try:
            print(f"📱 إرسال SMS إلى {phone_number}: {message}")
            # هنا يمكن إضافة منطق إرسال SMS الفعلي
            # مثل استخدام Twilio أو أي خدمة SMS أخرى

            # محاكاة نجاح الإرسال
            return True

        except Exception as e:
            print(f"❌ خطأ في إرسال SMS: {e}")
            return False

    def _send_email_notification(self, email, subject, message):
        """إرسال EMAIL"""
        try:
            print(f"📧 إرسال EMAIL إلى {email}: {subject}")
            print(f"   المحتوى: {message}")
            # هنا يمكن إضافة منطق إرسال EMAIL الفعلي
            # مثل استخدام SMTP أو SendGrid

            # محاكاة نجاح الإرسال
            return True

        except Exception as e:
            print(f"❌ خطأ في إرسال EMAIL: {e}")
            return False

    def _send_whatsapp_notification_direct(self, phone_number, message):
        """إرسال WhatsApp مباشرة عبر Green API"""
        try:
            print(f"💬 إرسال WhatsApp إلى {phone_number}: {message}")

            import os
            import requests

            # إعدادات Green API
            instance_id = '7105306929'
            token = '0260de395caa4adfb2aa02d53511f9ffa19b29b41f094fd68c'

            # تنسيق رقم الهاتف
            formatted_phone = phone_number
            if formatted_phone.startswith('0'):
                formatted_phone = '967' + formatted_phone[1:]
            elif formatted_phone.startswith('+967'):
                formatted_phone = formatted_phone[1:]
            elif not formatted_phone.startswith('967'):
                formatted_phone = '967' + formatted_phone

            # إرسال الرسالة
            url = f'https://7105.api.greenapi.com/waInstance{instance_id}/sendMessage/{token}'

            payload = {
                'chatId': f'{formatted_phone}@c.us',
                'message': message
            }

            print(f"🔗 إرسال طلب إلى: {url}")
            print(f"📱 رقم مُنسق: {formatted_phone}")

            response = requests.post(url, json=payload, timeout=15)

            print(f"📡 استجابة الخادم: {response.status_code}")
            print(f"📄 محتوى الاستجابة: {response.text}")

            if response.status_code == 200:
                response_data = response.json()
                if response_data.get('idMessage'):
                    print(f"✅ تم إرسال WhatsApp بنجاح - ID: {response_data.get('idMessage')}")
                    return True
                else:
                    print(f"❌ فشل إرسال WhatsApp: {response_data}")
                    return False
            else:
                print(f"❌ فشل إرسال WhatsApp - كود الخطأ: {response.status_code}")
                print(f"❌ تفاصيل الخطأ: {response.text}")
                return False

        except Exception as e:
            print(f"❌ خطأ في إرسال WhatsApp: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _get_contacts_by_group(self, group_name):
        """جلب جهات الاتصال حسب المجموعة من قاعدة البيانات"""
        try:
            print(f"👥 جلب جهات اتصال مجموعة: {group_name}")

            # جلب جهات الاتصال من قاعدة البيانات
            query = """
                SELECT name, phone, email
                FROM contact_groups cg
                JOIN contacts c ON cg.contact_id = c.id
                WHERE cg.group_name = :1
                AND c.is_active = 1
            """

            try:
                db = self.get_db_manager()
                results = db.execute_query(query, [group_name])

                contacts = []
                if results:
                    for row in results:
                        name, phone, email = row
                        contacts.append({
                            'name': name,
                            'phone': phone,
                            'email': email
                        })

                print(f"📞 تم العثور على {len(contacts)} جهة اتصال في مجموعة {group_name} من قاعدة البيانات")

                # إذا لم توجد جهات اتصال في قاعدة البيانات، لا نستخدم أي جهات افتراضية
                # لأن المستخدم لم يحفظ أي جهات اتصال لهذه المجموعة
                if not contacts:
                    print(f"⚠️ لا توجد جهات اتصال محفوظة لمجموعة {group_name}")

                return contacts

            except Exception as db_error:
                print(f"❌ خطأ في قاعدة البيانات لمجموعة {group_name}: {db_error}")
                # في حالة خطأ قاعدة البيانات، لا نُرجع جهات افتراضية
                return []

        except Exception as e:
            print(f"❌ خطأ في جلب جهات اتصال المجموعة {group_name}: {e}")
            return []


# دالة مساعدة لتشغيل الأتمتة عند تحديث حالة الشحنة
def trigger_automation_on_status_change(shipment_id, old_status, new_status):
    """تشغيل الأتمتة عند تغيير حالة الشحنة"""
    try:
        print(f"🔄 تشغيل الأتمتة للشحنة {shipment_id}: {old_status} → {new_status}")

        # تسجيل التغيير في جدول المراقبة (اختياري)
        db_manager = DatabaseManager()

        # محاولة إدراج سجل التغيير (مع تجاهل الأخطاء)
        try:
            change_insert_query = """
                INSERT INTO shipment_status_changes (
                    id, shipment_id, old_status, new_status,
                    changed_at, automation_processed
                ) VALUES (
                    shipment_status_changes_seq.NEXTVAL, :shipment_id,
                    :old_status, :new_status, CURRENT_TIMESTAMP, 0
                )
            """

            db_manager.execute_update(change_insert_query, {
                'shipment_id': shipment_id,
                'old_status': old_status,
                'new_status': new_status
            })
            print(f"✅ تم تسجيل تغيير الحالة للشحنة {shipment_id}")
        except Exception as change_error:
            print(f"⚠️ تعذر تسجيل التغيير (سيتم المتابعة): {change_error}")
            # المتابعة بدون توقف

        print(f"📋 تم تسجيل تغيير الحالة للشحنة {shipment_id}: {old_status} → {new_status}")
        print(f"🔄 Database Trigger سيتولى إضافة العملية لطابور الأتمتة تلقائياً")

        # ملاحظة: Database Trigger سيضيف العملية للطابور تلقائياً
        # ومعالج الطابور سيتولى تنفيذ الأتمتة

        db_manager.close()

    except Exception as e:
        print(f"❌ خطأ في تشغيل الأتمتة: {e}")

# إنشاء مثيل عام من محرك الأتمتة
automation_engine = AutomationEngine()
