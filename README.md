# النظام المحاسبي المتقدم
## Advanced Accounting System

نظام محاسبي شامل ومتقدم مبني بلغة Python يدعم اللغة العربية بالكامل مع دعم RTL، مصمم لإدارة المشتريات والمخزون والموردين مع تقارير تحليلية متقدمة ونظام سير عمل ذكي.

## 🌟 المميزات الرئيسية

### 📋 إدارة طلبات الشراء
- إنشاء وتعديل طلبات الشراء
- ربط الطلبات بالعقود الموجودة
- نظام الموافقات والتصديقات متعدد المستويات
- تتبع حالة الطلبات في الوقت الفعلي
- طلبات سريعة للأصناف منخفضة المخزون

### 🛒 إدارة أوامر الشراء
- تحويل طلبات الشراء إلى أوامر تلقائياً
- ربط أوامر الشراء بالعقود والموردين
- إدارة التسليم والاستلام
- متابعة تنفيذ الأوامر ومراحل التسليم
- تتبع الكميات المطلوبة والمستلمة

### 📦 استلام البضائع
- تسجيل استلام البضائع مع التفاصيل الكاملة
- مطابقة الكميات المستلمة مع المطلوبة
- إدارة المرتجعات والعيوب
- فحص الجودة وتسجيل الملاحظات
- تحديث المخزون تلقائياً عند الاستلام

### 📊 إدارة المخزون
- إدارة شاملة للمواد والأصناف
- تتبع الكميات والحركات في الوقت الفعلي
- نظام الحد الأدنى والأقصى مع التنبيهات
- تقارير المخزون التفصيلية
- إدارة المواقع والمستودعات

### 🏢 إدارة الموردين
- ملفات الموردين التفصيلية والشاملة
- تقييم أداء الموردين
- إدارة العقود والاتفاقيات
- نظام التصنيف والاعتماد
- تتبع تاريخ التعاملات

### 💰 النظام المالي
- إدارة الفواتير والمدفوعات
- ربط المشتريات بالحسابات المالية
- تتبع الالتزامات المالية
- التقارير المالية التفصيلية
- إدارة العملات المتعددة

### 📈 التقارير والتحليل
- تقارير أداء المشتريات
- تحليل التكاليف والوفورات
- مؤشرات الأداء الرئيسية (KPIs)
- لوحات المعلومات التفاعلية
- تصدير التقارير بصيغ متعددة

### ⚙️ نظام سير العمل
- أتمتة عمليات الموافقة
- تتبع مراحل المعاملات
- إشعارات وتنبيهات تلقائية
- إدارة الصلاحيات المتقدمة
- قوالب سير عمل قابلة للتخصيص

## 🛠️ التقنيات المستخدمة

- **Backend**: Python 3.8+, Flask 2.3+
- **Database**: SQLAlchemy, PostgreSQL/SQLite
- **Frontend**: Bootstrap 5, JavaScript ES6+, Chart.js
- **Authentication**: Flask-Login, bcrypt
- **Forms**: Flask-WTF, WTForms
- **Migrations**: Flask-Migrate
- **Reports**: ReportLab, openpyxl, pandas
- **UI/UX**: Font Awesome, Google Fonts (Noto Sans Arabic)

## 📋 متطلبات النظام

- Python 3.8 أو أحدث
- pip (مدير حزم Python)
- قاعدة بيانات (SQLite للتطوير، PostgreSQL للإنتاج)

## 🚀 التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone https://github.com/your-username/advanced-accounting-system.git
cd advanced-accounting-system
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد متغيرات البيئة
```bash
# إنشاء ملف .env
cp .env.example .env

# تحرير الملف وإضافة الإعدادات المطلوبة
```

### 5. تهيئة قاعدة البيانات
```bash
python init_db.py
```

### 6. تشغيل النظام
```bash
python run.py
```

سيكون النظام متاحاً على: `http://localhost:5000`

## 👤 بيانات الدخول الافتراضية

### المدير
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

### مستخدم عادي
- **اسم المستخدم**: user
- **كلمة المرور**: user123

## 📁 هيكل المشروع

```
advanced-accounting-system/
├── app/                          # التطبيق الرئيسي
│   ├── __init__.py              # إعدادات التطبيق
│   ├── models.py                # نماذج قاعدة البيانات
│   ├── main/                    # الوحدة الرئيسية
│   ├── auth/                    # وحدة المصادقة
│   ├── purchase_requests/       # وحدة طلبات الشراء
│   ├── purchase_orders/         # وحدة أوامر الشراء
│   ├── inventory/               # وحدة المخزون
│   ├── suppliers/               # وحدة الموردين
│   ├── financial/               # وحدة النظام المالي
│   ├── reports/                 # وحدة التقارير
│   ├── workflow/                # وحدة سير العمل
│   ├── static/                  # الملفات الثابتة
│   │   ├── css/                 # ملفات CSS
│   │   ├── js/                  # ملفات JavaScript
│   │   └── images/              # الصور
│   └── templates/               # قوالب HTML
├── migrations/                   # ملفات الهجرة
├── config.py                    # إعدادات النظام
├── requirements.txt             # متطلبات Python
├── init_db.py                   # تهيئة قاعدة البيانات
├── run.py                       # تشغيل النظام
└── README.md                    # هذا الملف
```

## 🔧 الإعدادات

### متغيرات البيئة
```bash
# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///accounting_system.db

# المفتاح السري
SECRET_KEY=your-secret-key-here

# إعدادات البريد الإلكتروني
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# بيئة التشغيل
FLASK_ENV=development
```

## 📊 لقطات الشاشة

### لوحة المعلومات الرئيسية
![Dashboard](screenshots/dashboard.png)

### إدارة طلبات الشراء
![Purchase Requests](screenshots/purchase-requests.png)

### تفاصيل طلب الشراء
![Request Details](screenshots/request-details.png)

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **المطور**: [اسمك]
- **البريد الإلكتروني**: <EMAIL>
- **LinkedIn**: [ملفك الشخصي]
- **GitHub**: [حسابك على GitHub]

## 🙏 شكر وتقدير

- [Flask](https://flask.palletsprojects.com/) - إطار العمل الرئيسي
- [Bootstrap](https://getbootstrap.com/) - مكتبة CSS
- [Font Awesome](https://fontawesome.com/) - الأيقونات
- [Chart.js](https://www.chartjs.org/) - الرسوم البيانية
- [Google Fonts](https://fonts.google.com/) - الخطوط العربية

---

**ملاحظة**: هذا النظام في مرحلة التطوير المستمر. نرحب بملاحظاتكم واقتراحاتكم لتحسينه.
