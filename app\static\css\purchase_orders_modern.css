/* 
 * تصميم حديث لنافذة أوامر الشراء
 * Modern Design for Purchase Orders Window
 */

/* متغيرات CSS للألوان والتصميم */
:root {
    --primary: #667eea;
    --secondary: #764ba2;
    --success: #28a745;
    --info: #17a2b8;
    --warning: #ffc107;
    --danger: #dc3545;
    --light: #f8f9fa;
    --dark: #343a40;
    --surface: #ffffff;
    --text-primary: #2c3e50;
    --text-secondary: #6c757d;
    --border: #e9ecef;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 30px rgba(0,0,0,0.15);
    --radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تصميم الصفحة الرئيسي */
body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
    font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif !important;
    min-height: 100vh;
}

.page-container {
    background: transparent;
    min-height: 100vh;
    padding: 2rem 0;
}

/* Header الرئيسي */
.page-header {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    border-radius: var(--radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-lg);
    color: white;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 400;
    color: var(--text-primary);
}

/* Stats Cards */
.stats-card {
    background: var(--surface);
    border-radius: var(--radius);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    border-left: 4px solid var(--secondary);
    position: relative;
    overflow: hidden;
    min-height: 120px;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stats-card.success {
    border-left-color: var(--success);
}

.stats-card.warning {
    border-left-color: var(--warning);
}

.stats-card.danger {
    border-left-color: var(--danger);
}

.stats-card.info {
    border-left-color: var(--info);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    display: block;
}

.stats-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-icon {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 2rem;
    opacity: 0.1;
    color: var(--text-primary);
}

/* Control Panel */
.control-panel {
    background: var(--surface);
    border-radius: var(--radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border);
}

.form-control-modern {
    border: 2px solid var(--border);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: var(--transition);
    background: var(--surface);
}

.form-control-modern:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: var(--surface);
}

.btn-modern {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.btn-modern:active {
    transform: translateY(0);
}

/* Table Styles */
.table-container {
    background: var(--surface);
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    border: 1px solid var(--border);
}

.table-modern {
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.table-modern thead th {
    background: linear-gradient(135deg, var(--dark) 0%, #495057 100%);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
    padding: 1rem;
    border: none;
    position: relative;
}

.table-modern tbody tr {
    transition: var(--transition);
    border-bottom: 1px solid var(--border);
}

.table-modern tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

.table-modern tbody td {
    padding: 1rem;
    vertical-align: middle;
    border: none;
    font-size: 0.9rem;
}

/* Badges */
.badge-modern {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* تنسيق البطاقات متعددة العملات */
.currency-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    font-size: 0.9rem;
}

.currency-line:last-child {
    border-bottom: none;
}

.currency-code {
    font-weight: bold;
    background: rgba(255,255,255,0.2);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    min-width: 45px;
    text-align: center;
}

.currency-amount {
    font-weight: 600;
    font-family: 'Arial', 'Helvetica', sans-serif;
    direction: ltr;
}

.no-data {
    text-align: center;
    opacity: 0.7;
    font-style: italic;
    padding: 10px 0;
}

/* تحسين hover للبطاقات القابلة للنقر */
.stats-card[onclick]:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    cursor: pointer;
}

.stats-card[onclick]:active {
    transform: translateY(-2px);
}

/* تنسيق تفاصيل الإحصائيات */
.statistics-details {
    max-height: 500px;
    overflow-y: auto;
}

.currency-detail-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    background: #f8f9fa;
}

.currency-header {
    color: #495057;
    font-weight: bold;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 2px solid #dee2e6;
}

/* Loading Animation */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border);
    border-top: 4px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }

    .stats-card {
        margin-bottom: 1rem;
        min-height: 100px;
    }

    .table-responsive {
        font-size: 0.8rem;
    }

    .control-panel {
        padding: 1rem;
    }

    .page-header {
        padding: 1.5rem;
    }
}
