<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب حوالة مالية - {{ request.request_number }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* الحل النهائي البسيط */
        .form-container {
            width: 210mm !important;
            height: 297mm !important;
            margin: 0 auto !important;
            padding: 2mm 15mm 15mm 15mm !important;
            box-sizing: border-box !important;
            background: white !important;
        }

        .form-section {
            margin-bottom: 4px !important;
        }

        .section-header {
            padding: 3px 8px !important;
            font-size: 11px !important;
        }

        .section-body {
            padding: 5px !important;
        }

        .data-table td {
            padding: 2px 4px !important;
            font-size: 9px !important;
        }

        .form-header {
            margin-bottom: 3px !important;
            padding-bottom: 2px !important;
        }

        .form-footer {
            margin-top: 5px !important;
            padding: 3px !important;
            font-size: 8px !important;
            text-align: center !important;
        }

        @media print {
            @page { size: A4; margin: 0; }
            .form-container {
                width: 210mm !important;
                height: 297mm !important;
                margin: 0 !important;
                padding: 2mm 15mm 15mm 15mm !important;
                box-shadow: none !important;
            }
            body { margin: 0; padding: 0; }
            .no-print { display: none !important; }
        }
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }

        /* تحسينات خاصة بمقاس A4 */
        .form-section {
            margin-bottom: 15px; /* تقليل المسافات بين الأقسام */
        }

        .section-header {
            padding: 8px 15px; /* تقليل padding */
            font-size: 14px;
        }

        .section-body {
            padding: 12px; /* تقليل padding */
        }
        
        /* حاوي النموذج - مقاس A4 دقيق */
        .form-container {
            background: white;
            width: 210mm; /* A4 width بدقة */
            min-height: 297mm; /* A4 height بدقة */
            margin: 0 auto;
            padding: 15mm; /* هوامش A4 قياسية */
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 0; /* بدون زوايا مدورة للطباعة */
            box-sizing: border-box;
        }

        @media print {
            .form-container {
                box-shadow: none;
                border-radius: 0;
                margin: 0;
                padding: 2mm 15mm 15mm 15mm; /* هامش علوي 2mm للطباعة */
                width: 210mm;
                min-height: 297mm;
            }

            @page {
                size: A4;
                margin: 0;
            }

            body {
                margin: 0;
                padding: 0;
            }

            /* تقليصات إضافية للطباعة */
            .form-section {
                margin-bottom: 3px !important;
            }

            .section-header {
                padding: 2px 6px !important;
                font-size: 10px !important;
            }

            .section-body {
                padding: 4px !important;
            }

            .data-table td {
                padding: 1px 3px !important;
                font-size: 8px !important;
            }

            .date-info {
                margin: 2px 0 !important;
                padding: 2px !important;
                font-size: 7px !important;
            }
        }
        
        /* رأس النموذج - مضغوط */
        .form-header {
            border-bottom: 2px solid #2c3e50;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .branch-logo {
            width: 90px;
            height: 90px;
            border-radius: 10px;
            object-fit: contain;
            border: none;
            background: transparent;
            padding: 0;
        }

        .branch-logo-placeholder {
            width: 90px;
            height: 90px;
            background: #2c3e50;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            border: none;
        }

        .branch-info {
            flex: 1;
            text-align: center;
            margin: 0 15px;
        }

        .branch-details {
            text-align: right;
            font-size: 10px;
            color: #2c3e50;
            line-height: 1.2;
        }
        
        .form-title {
            font-size: 18px;
            font-weight: 700;
            color: #2c3e50;
            margin: 8px 0 5px;
        }

        .form-subtitle {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 10px;
        }

        .request-number {
            background: #ecf0f1;
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
            display: inline-block;
        }
        
        /* معلومات التاريخ والوقت - مضغوط */
        .date-info {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }

        .date-info-item {
            text-align: center;
            padding: 6px;
            background: white;
            border-radius: 3px;
            border: 1px solid #e9ecef;
        }
        
        /* أقسام النموذج */
        .form-section {
            margin-bottom: 25px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .section-header {
            background: #34495e;
            color: white;
            padding: 12px 20px;
            font-weight: 600;
            font-size: 16px;
        }
        
        .section-body {
            padding: 20px;
        }
        
        /* جدول البيانات */
        .data-table {
            width: 100%;
            margin-bottom: 0;
        }
        
        .data-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #eee;
            vertical-align: top;
        }
        
        .data-table td:first-child {
            font-weight: 600;
            color: #2c3e50;
            width: 30%;
            background: #f8f9fa;
        }
        
        .data-table td:last-child {
            color: #333;
        }

        /* محاذاة البيانات لليسار في أقسام محددة */
        .left-align-data td:last-child {
            text-align: left;
            direction: ltr;
        }
        
        /* تنسيق المبلغ */
        .amount-display {
            font-size: 20px;
            font-weight: 700;
            color: #27ae60;
            text-align: center;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
            border: 2px solid #27ae60;
        }
        
        /* قسم التوقيعات */
        .signatures-section {
            margin-top: 40px;
            border-top: 2px solid #bdc3c7;
            padding-top: 30px;
        }
        
        .signature-box {
            text-align: center;
            padding: 20px;
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            min-height: 80px;
        }
        
        .signature-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .signature-line {
            border-bottom: 1px solid #333;
            width: 200px;
            margin: 20px auto 10px;
            height: 40px;
        }
        
        /* ذيل النموذج */
        .form-footer {
            margin-top: 40px;
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #bdc3c7;
            color: #7f8c8d;
            font-size: 12px;
        }
        
        /* أزرار الطباعة */
        .print-buttons {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        /* تحسينات إضافية */
        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-approved { background: #d1ecf1; color: #0c5460; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .status-completed { background: #d4edda; color: #155724; }
        
        .qr-code-placeholder {
            width: 80px;
            height: 80px;
            border: 2px dashed #bdc3c7;
            display: none; /* مخفي مؤقتاً */
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            font-size: 12px;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- أزرار الطباعة -->
    <div class="print-buttons no-print">
        <button class="btn btn-primary btn-lg me-3" onclick="window.print()">
            <i class="fas fa-print me-2"></i>طباعة النموذج
        </button>
        <button class="btn btn-secondary btn-lg me-3" onclick="downloadPDF()">
            <i class="fas fa-download me-2"></i>تحميل PDF
        </button>
        <button class="btn btn-outline-secondary btn-lg" onclick="window.close()">
            <i class="fas fa-times me-2"></i>إغلاق
        </button>
    </div>

    <!-- النموذج الرئيسي -->
    <div class="form-container">
        <!-- رأس النموذج -->
        <div class="form-header">
            <div class="header-content">
                <!-- شعار الفرع -->
                <div>
                    {% if request.branch_logo and request.branch_logo != 'None' %}
                        <img src="{{ request.branch_logo }}" alt="شعار الفرع" class="branch-logo">
                    {% else %}
                        <div class="branch-logo-placeholder">
                            <i class="fas fa-building"></i>
                        </div>
                    {% endif %}
                </div>

                <!-- معلومات الفرع والنموذج -->
                <div class="branch-info">
                    <h1 class="form-title">{{ request.branch_name or 'شركة الفجيحي للتموينات والتجارة المحدودة' }}</h1>
                    {% if request.branch_name_en %}
                        <h2 style="font-size: 16px; color: #7f8c8d; margin: 5px 0;">{{ request.branch_name_en }}</h2>
                    {% endif %}
                    <p class="form-subtitle">نموذج طلب حوالة مالية</p>
                    <div class="request-number">رقم الطلب: {{ request.request_number }}</div>
                </div>

                <!-- بيانات الاتصال -->
                <div class="branch-details">
                    {% if request.branch_address_ar %}
                        <div><strong>العنوان:</strong><br>{{ request.branch_address_ar }}</div>
                    {% endif %}
                    {% if request.branch_phone_ar %}
                        <div style="margin-top: 8px;"><strong>الهاتف:</strong><br>{{ request.branch_phone_ar }}</div>
                    {% endif %}
                    {% if request.branch_address_en %}
                        <div style="margin-top: 8px; font-size: 10px; color: #95a5a6;">{{ request.branch_address_en }}</div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- معلومات التاريخ والوقت -->
        <div class="date-info">
            <div class="date-info-item">
                <div style="color: #6c757d; font-size: 12px; margin-bottom: 5px;">تاريخ الطلب</div>
                <div style="font-weight: 600; color: #2c3e50;">
                    {{ request.created_at.strftime('%Y/%m/%d') if request.created_at else 'غير محدد' }}
                </div>
            </div>
            <div class="date-info-item">
                <div style="color: #6c757d; font-size: 12px; margin-bottom: 5px;">وقت الطلب</div>
                <div style="font-weight: 600; color: #2c3e50;">
                    {{ request.created_at.strftime('%H:%M:%S') if request.created_at else 'غير محدد' }}
                </div>
            </div>
            <div class="date-info-item">
                <div style="color: #6c757d; font-size: 12px; margin-bottom: 5px;">حالة الطلب</div>
                <div>
                    <span class="status-badge status-{{ request.status }}">
                        {% if request.status == 'pending' %}معلق
                        {% elif request.status == 'approved' %}معتمد
                        {% elif request.status == 'rejected' %}مرفوض
                        {% elif request.status == 'completed' %}مكتمل
                        {% else %}{{ request.status }}{% endif %}
                    </span>
                </div>
            </div>
            <!-- QR Code مخفي مؤقتاً -->
            <div class="qr-code-placeholder" style="display: none;">
                QR Code<br>{{ request.request_number }}
            </div>
        </div>

        <!-- قسم بيانات الفرع والموظف - مخفي مؤقتاً -->
        <div class="form-section" style="display: none;">
            <div class="section-header">
                <i class="fas fa-building me-2"></i>بيانات الفرع والموظف المسؤول
            </div>
            <div class="section-body">
                <table class="data-table">
                    <tr>
                        <td>اسم الفرع:</td>
                        <td>{{ request.branch_name or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td>الموظف المسؤول:</td>
                        <td>{{ request.created_by_name or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td>تاريخ إنشاء الطلب:</td>
                        <td>{{ request.created_at.strftime('%Y/%m/%d - %H:%M:%S') if request.created_at else 'غير محدد' }}</td>
                    </tr>
                    {% if request.updated_at and request.updated_at != request.created_at %}
                    <tr>
                        <td>تاريخ آخر تحديث:</td>
                        <td>{{ request.updated_at.strftime('%Y/%m/%d - %H:%M:%S') }}</td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>

        <!-- قسم تفاصيل الحوالة المالية -->
        <div class="form-section">
            <div class="section-header">
                <i class="fas fa-money-bill-wave me-2"></i>تفاصيل الحوالة المالية
            </div>
            <div class="section-body">
                <table class="data-table">
                    <!-- نوع التحويل مخفي مؤقتاً -->
                    <tr style="display: none;">
                        <td>نوع التحويل:</td>
                        <td>
                            {% if request.transfer_type == 'bank' %}
                                <i class="fas fa-university me-1"></i>بنك
                            {% elif request.transfer_type == 'money_changer' %}
                                <i class="fas fa-exchange-alt me-1"></i>صراف
                            {% else %}{{ request.transfer_type or 'غير محدد' }}{% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td>الصراف/البنك المختار:</td>
                        <td><strong>{{ request.money_changer_bank_name or 'غير محدد' }}</strong></td>
                    </tr>
                    <tr>
                        <td>المبلغ المطلوب تحويله:</td>
                        <td>
                            <span style="font-size: 16px; font-weight: 700; color: #27ae60;">
                                {{ "{:,.2f}".format(request.amount) if request.amount else '0.00' }} {{ request.currency or 'USD' }}
                            </span>
                            <span style="font-size: 14px; color: #6c757d; font-style: italic; margin-right: 15px;">
                                (<span id="amount-in-words">جاري تحويل المبلغ إلى كتابة...</span>)
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td>الغرض من التحويل:</td>
                        <td>{{ request.purpose or 'غير محدد' }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- قسم بيانات المستفيد -->
        <div class="form-section">
            <div class="section-header">
                <i class="fas fa-user me-2"></i>بيانات المستفيد
            </div>
            <div class="section-body">
                <table class="data-table left-align-data">
                    <tr>
                        <td>
                            اسم المستفيد<br>
                            <small style="color: #6c757d; font-weight: normal;">Beneficiary Name</small>
                        </td>
                        <td><strong>{{ request.beneficiary_name or 'غير محدد' }}</strong></td>
                    </tr>
                    <tr>
                        <td>
                            عنوان المستفيد<br>
                            <small style="color: #6c757d; font-weight: normal;">Beneficiary Address</small>
                        </td>
                        <td>{{ request.beneficiary_address or 'غير محدد' }}</td>
                    </tr>
                    <!-- الحقول التالية مخفية مؤقتاً -->
                    <tr style="display: none;">
                        <td>نوع المستفيد:</td>
                        <td>
                            {% if request.beneficiary_type == 'individual' %}فرد
                            {% elif request.beneficiary_type == 'company' %}شركة
                            {% elif request.beneficiary_type == 'supplier' %}مورد
                            {% elif request.beneficiary_type == 'employee' %}موظف
                            {% else %}{{ request.beneficiary_type or 'غير محدد' }}{% endif %}
                        </td>
                    </tr>
                    <tr style="display: none;">
                        <td>رقم الهوية/السجل التجاري:</td>
                        <td>{{ request.identification_number or 'غير محدد' }}</td>
                    </tr>
                    <tr style="display: none;">
                        <td>رقم الهاتف:</td>
                        <td>{{ request.beneficiary_phone or 'غير محدد' }}</td>
                    </tr>
                    <tr style="display: none;">
                        <td>البريد الإلكتروني:</td>
                        <td>{{ request.beneficiary_email or 'غير محدد' }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- قسم البيانات المصرفية للمستفيد -->
        <div class="form-section">
            <div class="section-header">
                <i class="fas fa-university me-2"></i>البيانات المصرفية للمستفيد
            </div>
            <div class="section-body">
                <table class="data-table left-align-data">
                    <tr>
                        <td>اسم البنك:</td>
                        <td><strong>{{ request.bank_name or 'غير محدد' }}</strong></td>
                    </tr>
                    <tr>
                        <td>فرع البنك:</td>
                        <td>{{ request.bank_branch or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td>دولة البنك:</td>
                        <td>{{ request.bank_country or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td>رقم الحساب:</td>
                        <td><strong>{{ request.bank_account or 'غير محدد' }}</strong></td>
                    </tr>
                    <tr>
                        <td>رقم IBAN:</td>
                        <td>{{ request.iban or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td>رمز SWIFT:</td>
                        <td>{{ request.swift_code or 'غير محدد' }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- قسم بيانات المرسل -->
        <div class="form-section">
            <div class="section-header">
                <i class="fas fa-user-tie me-2"></i>بيانات المرسل
            </div>
            <div class="section-body">
                <table class="data-table left-align-data">
                    <tr>
                        <td>
                            اسم المرسل<br>
                            <small style="color: #6c757d; font-weight: normal;">Sender Name</small>
                        </td>
                        <td><strong>ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD</strong></td>
                    </tr>
                    <tr>
                        <td>
                            G.M<br>
                            <small style="color: #6c757d; font-weight: normal;">General Manager</small>
                        </td>
                        <td><strong>NASHA'AT RASHAD QASIM ALDUBAEE</strong></td>
                    </tr>
                    <tr>
                        <td>
                            عنوان المرسل<br>
                            <small style="color: #6c757d; font-weight: normal;">Sender Address</small>
                        </td>
                        <td>TAIZ STREET, ORPHAN BUIDING, NEAR ALBRKA STORES – SANA'A, YEMEN</td>
                    </tr>
                    <tr>
                        <td>
                            الهاتف / رقم ص.ب / الفاكس<br>
                            <small style="color: #6c757d; font-weight: normal;">Phone / P.O.Box / Fax</small>
                        </td>
                        <td>
                            <div style="display: flex; flex-wrap: wrap; gap: 15px; align-items: center;">
                                <span style="font-weight: 600;">
                                    <i class="fas fa-phone me-1" style="color: #28a745;"></i>
                                    {{ request.branch_phone_ar or 'رقم التلفون 616109-617010' }}
                                </span>
                                <span style="font-weight: 600;">
                                    <i class="fas fa-mailbox me-1" style="color: #007bff;"></i>
                                    P.O.Box: 15565
                                </span>
                                <span style="font-weight: 600;">
                                    <i class="fas fa-fax me-1" style="color: #6c757d;"></i>
                                    Fax: +967-1-617011
                                </span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            البريد الإلكتروني<br>
                            <small style="color: #6c757d; font-weight: normal;">Email</small>
                        </td>
                        <td>
                            <span style="font-weight: 600;">
                                <i class="fas fa-envelope me-1" style="color: #dc3545;"></i>
                                <EMAIL> , <EMAIL>
                            </span>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- نص إضافي بعد بيانات المرسل -->
        <div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
            <div style="text-align: right; line-height: 1.8; color: #333; font-size: 14px;">
                مرفق لكم صوره التحويل من البنك كمرجع.<br>
                وذلك بتقيد التحويل على حسابنا لديكم ,,,,,
            </div>
            <div style="text-align: left; margin-top: 15px; font-weight: 600; color: #2c3e50; font-size: 14px;">
                المدير العام
            </div>
            <div style="text-align: left; margin-top: 5px; font-weight: 700; color: #2c3e50; font-size: 16px;">
                نشأت رشاد قاسم الدبعى
            </div>
        </div>





        <!-- قسم الملاحظات - مخفي مؤقتاً -->
        {% if request.notes %}
        <div class="form-section" style="display: none;">
            <div class="section-header">
                <i class="fas fa-sticky-note me-2"></i>ملاحظات إضافية
            </div>
            <div class="section-body">
                <p style="margin: 0; padding: 10px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff;">
                    {{ request.notes }}
                </p>
            </div>
        </div>
        {% endif %}

        <!-- قسم التوقيعات والاعتمادات - مخفي مؤقتاً -->
        <div class="signatures-section" style="display: none;">
            <h4 style="text-align: center; margin-bottom: 30px; color: #2c3e50;">
                <i class="fas fa-pen-fancy me-2"></i>التوقيعات والاعتمادات
            </h4>

            <div class="row">
                <div class="col-md-4">
                    <div class="signature-box">
                        <div class="signature-label">مقدم الطلب</div>
                        <div class="signature-line"></div>
                        <div style="font-size: 12px; color: #7f8c8d;">
                            الاسم: ________________<br>
                            التوقيع والتاريخ
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="signature-box">
                        <div class="signature-label">موظف الاستقبال</div>
                        <div class="signature-line"></div>
                        <div style="font-size: 12px; color: #7f8c8d;">
                            الاسم: {{ request.created_by_name or '________________' }}<br>
                            التوقيع والتاريخ
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="signature-box">
                        <div class="signature-label">مدير الفرع</div>
                        <div class="signature-line"></div>
                        <div style="font-size: 12px; color: #7f8c8d;">
                            الاسم: ________________<br>
                            التوقيع والتاريخ
                        </div>
                    </div>
                </div>
            </div>

            <!-- قسم الاعتمادات -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="signature-box" style="background: #f8f9fa;">
                        <div class="signature-label" style="color: #28a745;">
                            <i class="fas fa-check-circle me-1"></i>اعتماد المدير المالي
                        </div>
                        <div class="signature-line"></div>
                        <div style="font-size: 12px; color: #7f8c8d;">
                            الاسم: ________________<br>
                            التوقيع والختم والتاريخ
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="signature-box" style="background: #f8f9fa;">
                        <div class="signature-label" style="color: #dc3545;">
                            <i class="fas fa-stamp me-1"></i>ختم الشركة
                        </div>
                        <div style="height: 60px; border: 2px dashed #dee2e6; border-radius: 5px; margin: 10px 0;"></div>
                        <div style="font-size: 12px; color: #7f8c8d;">
                            الختم الرسمي للشركة
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ذيل النموذج -->
        <div class="form-footer">
            <div class="row">
                <div class="col-md-4">
                    <strong>{{ request.branch_name or 'شركة الفجيحي للتموينات والتجارة المحدودة' }}</strong><br>
                    {% if request.branch_address_ar %}
                        <i class="fas fa-map-marker-alt me-1"></i>{{ request.branch_address_ar }}<br>
                    {% else %}
                        <i class="fas fa-map-marker-alt me-1"></i>صنعاء - الجمهورية اليمنية<br>
                    {% endif %}
                    {% if request.branch_phone_ar %}
                        <i class="fas fa-phone me-1"></i>{{ request.branch_phone_ar }}
                    {% else %}
                        <i class="fas fa-phone me-1"></i>+967 1 234567
                    {% endif %}
                </div>
                <div class="col-md-4 text-center">
                    <div style="font-size: 10px; color: #bdc3c7;">
                        تم إنشاء هذا النموذج تلقائياً بواسطة نظام إدارة الحوالات<br>
                        تاريخ الطباعة: <span id="print-date"></span>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    {% if request.branch_name_en %}
                        <div style="font-size: 11px; margin-bottom: 5px;">{{ request.branch_name_en }}</div>
                    {% endif %}
                    {% if request.branch_address_en %}
                        <div style="font-size: 10px; color: #7f8c8d; margin-bottom: 5px;">{{ request.branch_address_en }}</div>
                    {% endif %}
                    <strong>رقم المرجع: {{ request.request_number }}</strong>
                </div>
            </div>

            <div class="text-center mt-3" style="border-top: 1px solid #dee2e6; padding-top: 15px;">
                <small style="color: #6c757d;">
                    هذا النموذج صالح لمدة 30 يوماً من تاريخ الإصدار |
                    للاستفسارات يرجى الاتصال بخدمة العملاء |
                    جميع الحقوق محفوظة © <span id="current-year"></span>
                </small>
            </div>
        </div>
    </div>

    <!-- JavaScript للطباعة -->
    <script>
        // تحويل الأرقام إلى كتابة باللغة العربية
        function numberToArabicWords(num) {
            if (num === 0) return 'صفر';

            const ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
            const tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
            const teens = ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'];
            const hundreds = ['', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'];

            function convertHundreds(n) {
                let result = '';

                if (n >= 100) {
                    result += hundreds[Math.floor(n / 100)] + ' ';
                    n %= 100;
                }

                if (n >= 20) {
                    result += tens[Math.floor(n / 10)] + ' ';
                    n %= 10;
                    if (n > 0) result += ones[n] + ' ';
                } else if (n >= 10) {
                    result += teens[n - 10] + ' ';
                } else if (n > 0) {
                    result += ones[n] + ' ';
                }

                return result.trim();
            }

            let result = '';
            let scale = ['', 'ألف', 'مليون', 'مليار'];
            let scaleIndex = 0;

            while (num > 0) {
                let chunk = num % 1000;
                if (chunk > 0) {
                    let chunkWords = convertHundreds(chunk);
                    if (scaleIndex > 0) {
                        chunkWords += ' ' + scale[scaleIndex];
                    }
                    result = chunkWords + ' ' + result;
                }
                num = Math.floor(num / 1000);
                scaleIndex++;
            }

            return result.trim();
        }

        // تعيين التاريخ والوقت الحالي
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const printDate = now.getFullYear() + '/' +
                            String(now.getMonth() + 1).padStart(2, '0') + '/' +
                            String(now.getDate()).padStart(2, '0') + ' - ' +
                            String(now.getHours()).padStart(2, '0') + ':' +
                            String(now.getMinutes()).padStart(2, '0') + ':' +
                            String(now.getSeconds()).padStart(2, '0');

            document.getElementById('print-date').textContent = printDate;
            document.getElementById('current-year').textContent = now.getFullYear();

            // تحويل المبلغ إلى كتابة
            const amountElement = document.getElementById('amount-in-words');
            if (amountElement) {
                const amount = {{ request.amount if request.amount else 0 }};
                const currency = '{{ request.currency or "دولار أمريكي" }}';

                // تحديد اسم العملة بالعربية
                const currencyNames = {
                    'USD': 'دولار أمريكي',
                    'EUR': 'يورو',
                    'SAR': 'ريال سعودي',
                    'YER': 'ريال يمني',
                    'AED': 'درهم إماراتي',
                    'GBP': 'جنيه إسترليني'
                };

                const currencyName = currencyNames['{{ request.currency or "USD" }}'] || '{{ request.currency or "دولار أمريكي" }}';

                // فصل الجزء الصحيح والعشري
                const integerPart = Math.floor(amount);
                const decimalPart = Math.round((amount - integerPart) * 100);

                let amountInWords = numberToArabicWords(integerPart) + ' ' + currencyName;

                if (decimalPart > 0) {
                    amountInWords += ' و ' + numberToArabicWords(decimalPart) + ' سنت';
                }

                amountElement.textContent = amountInWords;
            }
        });

        // طباعة النموذج
        function printForm() {
            window.print();
        }

        // تحميل PDF احترافي مع معالجة أفضل للأخطاء
        function downloadPDF() {
            console.log('📥 تحميل PDF للطلب...');

            // الحصول على رقم الطلب من URL
            const urlParts = window.location.pathname.split('/');
            const requestId = urlParts[urlParts.length - 1];

            // إنشاء رابط التحميل
            const downloadUrl = `/transfers/download-pdf/${requestId}`;

            // إظهار رسالة تحميل
            const downloadBtn = document.querySelector('.print-buttons .btn-secondary');
            const originalText = downloadBtn.innerHTML;
            downloadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري إنشاء PDF...';
            downloadBtn.disabled = true;

            // استخدام fetch للتحكم أفضل في الأخطاء
            fetch(downloadUrl, {
                method: 'GET',
                credentials: 'same-origin'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.blob();
            })
            .then(blob => {
                // إنشاء رابط التحميل
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `transfer_request_{{ request.request_number }}.pdf`;

                // إضافة الرابط للصفحة وتفعيله
                document.body.appendChild(link);
                link.click();

                // تنظيف
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);

                console.log('✅ تم تحميل PDF بنجاح');

                // إظهار رسالة نجاح
                showAlert('تم تحميل PDF بنجاح', 'success');
            })
            .catch(error => {
                console.error('❌ خطأ في تحميل PDF:', error);
                showAlert('فشل في تحميل PDF. يرجى المحاولة مرة أخرى.', 'danger');
            })
            .finally(() => {
                // إعادة تعيين الزر
                setTimeout(() => {
                    downloadBtn.innerHTML = originalText;
                    downloadBtn.disabled = false;
                }, 1000);
            });
        }

        // دالة لإظهار التنبيهات
        function showAlert(message, type) {
            // إنشاء تنبيه Bootstrap
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.style.position = 'fixed';
            alertDiv.style.top = '20px';
            alertDiv.style.right = '20px';
            alertDiv.style.zIndex = '9999';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            // إزالة التنبيه بعد 5 ثوان
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }

        // طباعة تلقائية عند فتح الصفحة (اختياري)
        // window.onload = function() {
        //     setTimeout(function() {
        //         window.print();
        //     }, 1000);
        // };
    </script>
</body>
</html>
