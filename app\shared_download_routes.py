#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Routes للتحميل المشترك العام - Shared Download Routes
"""

from flask import Blueprint, send_file, current_app
from oracle_manager import get_oracle_manager
import logging
import os

logger = logging.getLogger(__name__)

# إنشاء blueprint للتحميل المشترك
shared_download_bp = Blueprint('shared_download', __name__)

def safe_convert_lob(value):
    """تحويل آمن لقيم LOB"""
    if value is None:
        return None
    try:
        if hasattr(value, 'read'):
            return value.read().decode('utf-8')
        return str(value)
    except:
        return str(value) if value else None

@shared_download_bp.route('/shared/download/<file_hash>')
def download_shared_file(file_hash):
    """تحميل ملف مشترك باستخدام hash - يعمل للعقود والشحنات"""
    try:
        logger.info(f"🔗 طلب تحميل ملف مشترك: {file_hash}")
        
        oracle_manager = get_oracle_manager()
        
        # البحث في جداول مختلفة
        tables_to_search = [
            {
                'table': 'contract_documents',
                'query': """
                    SELECT id, file_path, file_name, title, 'contract' as source_type
                    FROM contract_documents 
                    WHERE onedrive_share_link LIKE :search_pattern
                       OR nextcloud_share_link LIKE :search_pattern
                """
            },
            {
                'table': 'cargo_shipment_documents',
                'query': """
                    SELECT id, file_path, file_name, document_name as title, 'shipment' as source_type
                    FROM cargo_shipment_documents
                    WHERE onedrive_share_link LIKE :search_pattern
                       OR nextcloud_share_link LIKE :search_pattern
                       OR share_link LIKE :search_pattern
                """
            }
        ]
        
        search_pattern = f"%{file_hash}%"
        doc = None
        source_type = None
        
        # البحث في كل جدول
        for table_info in tables_to_search:
            try:
                # تحديد عدد المعاملات حسب الجدول
                if table_info['table'] == 'cargo_shipment_documents':
                    # جدول الشحنات يستخدم ثلاثة معاملات
                    result = oracle_manager.execute_query(table_info['query'], [search_pattern, search_pattern, search_pattern])
                else:
                    # جدول العقود يستخدم معاملين
                    result = oracle_manager.execute_query(table_info['query'], [search_pattern, search_pattern])

                if result:
                    doc = result[0]
                    source_type = safe_convert_lob(doc[4]) if len(doc) > 4 else table_info['table']
                    logger.info(f"📄 تم العثور على الملف في {source_type}")
                    break
            except Exception as e:
                logger.warning(f"⚠️ خطأ في البحث في {table_info['table']}: {e}")
                continue
        
        if not doc:
            logger.warning(f"⚠️ لم يتم العثور على ملف للـ hash: {file_hash}")
            return "الملف غير موجود أو انتهت صلاحية الرابط", 404
        
        file_path = safe_convert_lob(doc[1])
        file_name = safe_convert_lob(doc[2])
        title = safe_convert_lob(doc[3])
        
        logger.info(f"📄 تم العثور على الملف: {file_name} من {source_type}")
        
        # التحقق من وجود الملف
        if not file_path or not os.path.exists(file_path):
            logger.error(f"❌ الملف غير موجود: {file_path}")
            return "الملف غير موجود على الخادم", 404
        
        # إرسال الملف للتحميل
        return send_file(
            file_path,
            as_attachment=True,
            download_name=file_name or f"document_{file_hash}.pdf",
            mimetype='application/octet-stream'
        )
        
    except Exception as e:
        logger.error(f"❌ خطأ في تحميل الملف المشترك: {e}")
        return "حدث خطأ في تحميل الملف", 500
