-- إضا<PERSON>ة عمود IS_USED إلى جدول العقود
-- Add IS_USED column to contracts table

-- إضافة العمود الجديد
ALTER TABLE contracts ADD (
    IS_USED NUMBER(1) DEFAULT 0 NOT NULL
);

-- إضا<PERSON>ة قيد للتحقق من القيم (0 أو 1 فقط)
ALTER TABLE contracts ADD CONSTRAINT CHK_CONTRACTS_IS_USED 
    CHECK (IS_USED IN (0, 1));

-- إضافة تعليق على العمود
COMMENT ON COLUMN contracts.IS_USED IS 'هل تم استخدام العقد في أوامر الشراء (0=لا، 1=نعم)';

-- إنشاء فهرس للأداء
CREATE INDEX IDX_CONTRACTS_IS_USED ON contracts(IS_USED);

-- تحديث العقود الموجودة بناءً على استخدامها في أوامر الشراء
UPDATE contracts 
SET IS_USED = 1 
WHERE contract_id IN (
    SELECT DISTINCT CONTRACT_ID 
    FROM PURCHASE_ORDERS 
    WHERE CONTRACT_ID IS NOT NULL
);

-- تأكيد التغييرات
COMMIT;

-- عرض النتيجة
SELECT contract_id, contract_number, supplier_name, IS_USED 
FROM contracts 
ORDER BY contract_id;
