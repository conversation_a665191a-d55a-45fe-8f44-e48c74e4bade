-- =====================================================
-- الجداول المتقدمة لنظام إدارة أرصدة الموردين
-- Extended Tables for Supplier Balance Management System
-- =====================================================

-- 6. جدول الفروقات والتحقيقات
CREATE TABLE RECONCILIATION_DIFFERENCES (
    difference_id NUMBER PRIMARY KEY,
    item_id NUMBER NOT NULL,
    cycle_id NUMBER NOT NULL,
    
    -- تصنيف الفرق
    difference_type VARCHAR2(50) NOT NULL, -- TIMING, AMOUNT, MISSING_TRANSACTION, DUPLICATE, CLASSIFICATION
    difference_category VARCHAR2(30), -- INVOICE, PAYMENT, CREDIT_NOTE, DEBIT_NOTE, ADJUSTMENT
    severity_level VARCHAR2(20) DEFAULT 'MEDIUM', -- LOW, MEDIUM, HIGH, CRITICAL
    
    -- تفاصيل المعاملة في النظام
    system_transaction_id NUMBER,
    system_transaction_date DATE,
    system_reference VARCHAR2(100),
    system_amount NUMBER(15,2),
    system_description NVARCHAR2(500),
    
    -- تفاصيل المعاملة عند المورد
    supplier_transaction_date DATE,
    supplier_reference VARCHAR2(100),
    supplier_amount NUMBER(15,2),
    supplier_description NVARCHAR2(500),
    
    -- تحليل الفرق
    expected_amount NUMBER(15,2),
    actual_amount NUMBER(15,2),
    difference_amount NUMBER(15,2),
    difference_percentage NUMBER(5,2),
    
    -- أسباب الفرق
    difference_reason NVARCHAR2(500),
    root_cause VARCHAR2(200),
    impact_assessment CLOB,
    
    -- حالة التحقيق
    investigation_status VARCHAR2(20) DEFAULT 'OPEN', -- OPEN, INVESTIGATING, RESOLVED, CLOSED
    assigned_to NUMBER,
    priority VARCHAR2(20) DEFAULT 'MEDIUM', -- LOW, MEDIUM, HIGH, URGENT
    
    -- التسوية
    adjustment_required CHAR(1) DEFAULT 'N',
    adjustment_amount NUMBER(15,2),
    adjustment_description NVARCHAR2(500),
    
    -- الحل
    resolution_method VARCHAR2(100),
    resolution_notes CLOB,
    resolved_by NUMBER,
    resolved_date TIMESTAMP,
    
    -- تواريخ النظام
    identified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_rd_item FOREIGN KEY (item_id) REFERENCES RECONCILIATION_ITEMS(item_id),
    CONSTRAINT fk_rd_cycle FOREIGN KEY (cycle_id) REFERENCES RECONCILIATION_CYCLES(cycle_id),
    CONSTRAINT fk_rd_system_trans FOREIGN KEY (system_transaction_id) REFERENCES SUPPLIER_TRANSACTIONS(transaction_id),
    CONSTRAINT fk_rd_assigned_to FOREIGN KEY (assigned_to) REFERENCES USERS(id),
    CONSTRAINT fk_rd_resolved_by FOREIGN KEY (resolved_by) REFERENCES USERS(id),
    CONSTRAINT fk_rd_created_by FOREIGN KEY (created_by) REFERENCES USERS(id)
);

-- 7. جدول التعديلات والتسويات
CREATE TABLE RECONCILIATION_ADJUSTMENTS (
    adjustment_id NUMBER PRIMARY KEY,
    difference_id NUMBER,
    item_id NUMBER NOT NULL,
    account_id NUMBER NOT NULL,
    
    -- نوع التعديل
    adjustment_type VARCHAR2(30) NOT NULL, -- SYSTEM_CORRECTION, SUPPLIER_CORRECTION, TIMING_DIFFERENCE, WRITE_OFF
    adjustment_category VARCHAR2(30), -- INVOICE, PAYMENT, CREDIT_NOTE, DEBIT_NOTE
    
    -- تفاصيل التعديل
    adjustment_amount NUMBER(15,2) NOT NULL,
    adjustment_currency VARCHAR2(3) NOT NULL,
    adjustment_description NVARCHAR2(500) NOT NULL,
    adjustment_reason NVARCHAR2(500),
    
    -- المعاملة المرتبطة
    related_transaction_id NUMBER,
    new_transaction_created CHAR(1) DEFAULT 'N',
    new_transaction_id NUMBER,
    
    -- حالة التعديل
    adjustment_status VARCHAR2(20) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED, APPLIED, CANCELLED
    
    -- سير العمل
    approval_required CHAR(1) DEFAULT 'Y',
    approval_level NUMBER DEFAULT 1,
    current_approver NUMBER,
    
    -- طلب التعديل
    requested_by NUMBER NOT NULL,
    requested_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    request_notes CLOB,
    
    -- الموافقة
    approved_by NUMBER,
    approved_date TIMESTAMP,
    approval_notes CLOB,
    rejection_reason NVARCHAR2(500),
    
    -- التطبيق
    applied_by NUMBER,
    applied_date TIMESTAMP,
    application_notes CLOB,
    
    -- تواريخ النظام
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_ra_difference FOREIGN KEY (difference_id) REFERENCES RECONCILIATION_DIFFERENCES(difference_id),
    CONSTRAINT fk_ra_item FOREIGN KEY (item_id) REFERENCES RECONCILIATION_ITEMS(item_id),
    CONSTRAINT fk_ra_account FOREIGN KEY (account_id) REFERENCES SUPPLIER_ACCOUNTS(account_id),
    CONSTRAINT fk_ra_related_trans FOREIGN KEY (related_transaction_id) REFERENCES SUPPLIER_TRANSACTIONS(transaction_id),
    CONSTRAINT fk_ra_new_trans FOREIGN KEY (new_transaction_id) REFERENCES SUPPLIER_TRANSACTIONS(transaction_id),
    CONSTRAINT fk_ra_requested_by FOREIGN KEY (requested_by) REFERENCES USERS(id),
    CONSTRAINT fk_ra_approved_by FOREIGN KEY (approved_by) REFERENCES USERS(id),
    CONSTRAINT fk_ra_applied_by FOREIGN KEY (applied_by) REFERENCES USERS(id),
    CONSTRAINT fk_ra_current_approver FOREIGN KEY (current_approver) REFERENCES USERS(id)
);

-- 8. جدول قوالب كشوفات الحساب
CREATE TABLE STATEMENT_TEMPLATES (
    template_id NUMBER PRIMARY KEY,
    template_name NVARCHAR2(100) NOT NULL,
    template_type VARCHAR2(30) NOT NULL, -- DETAILED, SUMMARY, AGING, CUSTOM
    
    -- إعدادات القالب
    include_opening_balance CHAR(1) DEFAULT 'Y',
    include_transactions CHAR(1) DEFAULT 'Y',
    include_aging_analysis CHAR(1) DEFAULT 'N',
    include_payment_history CHAR(1) DEFAULT 'N',
    
    -- فترة التقرير
    default_period_type VARCHAR2(20) DEFAULT 'MONTHLY', -- DAILY, WEEKLY, MONTHLY, QUARTERLY, CUSTOM
    default_period_months NUMBER DEFAULT 1,
    
    -- تنسيق التقرير
    output_format VARCHAR2(20) DEFAULT 'PDF', -- PDF, EXCEL, WORD, HTML
    language_code VARCHAR2(5) DEFAULT 'AR',
    currency_display VARCHAR2(20) DEFAULT 'ORIGINAL', -- ORIGINAL, BASE, BOTH
    
    -- إعدادات العرض
    group_by_transaction_type CHAR(1) DEFAULT 'N',
    show_running_balance CHAR(1) DEFAULT 'Y',
    show_due_dates CHAR(1) DEFAULT 'Y',
    show_overdue_amounts CHAR(1) DEFAULT 'Y',
    
    -- الحقول المخصصة
    custom_fields CLOB, -- JSON format
    header_template CLOB,
    footer_template CLOB,
    
    -- حالة القالب
    is_active CHAR(1) DEFAULT 'Y',
    is_default CHAR(1) DEFAULT 'N',
    
    -- تواريخ النظام
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_st_created_by FOREIGN KEY (created_by) REFERENCES USERS(id),
    CONSTRAINT fk_st_updated_by FOREIGN KEY (updated_by) REFERENCES USERS(id)
);

-- 9. جدول تاريخ إصدار الكشوفات
CREATE TABLE STATEMENT_HISTORY (
    statement_id NUMBER PRIMARY KEY,
    account_id NUMBER NOT NULL,
    template_id NUMBER NOT NULL,
    
    -- فترة الكشف
    statement_date DATE DEFAULT SYSDATE,
    period_from DATE NOT NULL,
    period_to DATE NOT NULL,
    
    -- تفاصيل الكشف
    statement_number VARCHAR2(50) UNIQUE,
    statement_type VARCHAR2(30),
    output_format VARCHAR2(20),
    
    -- الأرصدة
    opening_balance NUMBER(15,2) DEFAULT 0,
    closing_balance NUMBER(15,2) DEFAULT 0,
    total_debits NUMBER(15,2) DEFAULT 0,
    total_credits NUMBER(15,2) DEFAULT 0,
    
    -- إحصائيات
    transaction_count NUMBER DEFAULT 0,
    overdue_amount NUMBER(15,2) DEFAULT 0,
    current_due NUMBER(15,2) DEFAULT 0,
    
    -- ملفات الإخراج
    file_path VARCHAR2(500),
    file_size NUMBER,
    file_hash VARCHAR2(64),
    
    -- حالة الإرسال
    sent_to_supplier CHAR(1) DEFAULT 'N',
    sent_date TIMESTAMP,
    sent_by NUMBER,
    delivery_method VARCHAR2(30), -- EMAIL, PORTAL, MANUAL
    
    -- تواريخ النظام
    generated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    generated_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_sh_account FOREIGN KEY (account_id) REFERENCES SUPPLIER_ACCOUNTS(account_id),
    CONSTRAINT fk_sh_template FOREIGN KEY (template_id) REFERENCES STATEMENT_TEMPLATES(template_id),
    CONSTRAINT fk_sh_sent_by FOREIGN KEY (sent_by) REFERENCES USERS(id),
    CONSTRAINT fk_sh_generated_by FOREIGN KEY (generated_by) REFERENCES USERS(id)
);

-- 10. جدول جداول المدفوعات
CREATE TABLE PAYMENT_SCHEDULES (
    schedule_id NUMBER PRIMARY KEY,
    account_id NUMBER NOT NULL,
    
    -- تفاصيل الجدولة
    schedule_name NVARCHAR2(100),
    schedule_type VARCHAR2(30) DEFAULT 'MANUAL', -- MANUAL, AUTO_GENERATED, RECURRING
    
    -- فترة الجدولة
    schedule_date DATE DEFAULT SYSDATE,
    period_from DATE NOT NULL,
    period_to DATE NOT NULL,
    
    -- تفاصيل الدفع
    total_amount NUMBER(15,2) NOT NULL,
    currency_code VARCHAR2(3) NOT NULL,
    payment_method VARCHAR2(30),
    
    -- الأولوية والاستحقاق
    priority_level VARCHAR2(20) DEFAULT 'MEDIUM', -- LOW, MEDIUM, HIGH, URGENT
    due_date DATE NOT NULL,
    early_payment_discount NUMBER(5,2) DEFAULT 0,
    late_payment_penalty NUMBER(5,2) DEFAULT 0,
    
    -- حالة الجدولة
    schedule_status VARCHAR2(20) DEFAULT 'PENDING', -- PENDING, APPROVED, EXECUTED, CANCELLED
    
    -- معلومات التنفيذ
    executed_amount NUMBER(15,2) DEFAULT 0,
    executed_date DATE,
    executed_by NUMBER,
    transfer_request_id NUMBER,
    
    -- ملاحظات
    notes CLOB,
    approval_notes CLOB,
    
    -- تواريخ النظام
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_ps_account FOREIGN KEY (account_id) REFERENCES SUPPLIER_ACCOUNTS(account_id),
    CONSTRAINT fk_ps_executed_by FOREIGN KEY (executed_by) REFERENCES USERS(id),
    CONSTRAINT fk_ps_created_by FOREIGN KEY (created_by) REFERENCES USERS(id),
    CONSTRAINT fk_ps_updated_by FOREIGN KEY (updated_by) REFERENCES USERS(id)
);

-- إنشاء Sequences للجداول الجديدة
CREATE SEQUENCE RECONCILIATION_DIFFERENCES_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE RECONCILIATION_ADJUSTMENTS_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE STATEMENT_TEMPLATES_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE STATEMENT_HISTORY_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE PAYMENT_SCHEDULES_SEQ START WITH 1 INCREMENT BY 1;

-- رسالة نجاح
SELECT 'تم إنشاء الجداول المتقدمة لنظام إدارة أرصدة الموردين بنجاح!' as status FROM dual;

COMMIT;
