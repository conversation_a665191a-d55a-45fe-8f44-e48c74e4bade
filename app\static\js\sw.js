// Service Worker للنظام المحاسبي المتقدم
// Advanced Accounting System Service Worker

const CACHE_NAME = 'accounting-system-v1.0.0';
const urlsToCache = [
    '/',
    '/static/css/bootstrap.min.css',
    '/static/js/bootstrap.bundle.min.js',
    '/static/js/app.js',
    '/static/icons/icon-192x192.png',
    '/static/icons/icon-512x512.png',
    '/dashboard',
    '/inventory/',
    '/suppliers/',
    '/purchase-requests/',
    '/purchase-orders/',
    '/purchase-contracts/',
    '/goods-receipt/',
    '/financial/',
    '/workflow/'
];

// تثبيت Service Worker
self.addEventListener('install', function(event) {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                console.log('تم فتح التخزين المؤقت');
                return cache.addAll(urlsToCache);
            })
    );
});

// تفعيل Service Worker
self.addEventListener('activate', function(event) {
    event.waitUntil(
        caches.keys().then(function(cacheNames) {
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    if (cacheName !== CACHE_NAME) {
                        console.log('حذف التخزين المؤقت القديم:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
});

// اعتراض الطلبات
self.addEventListener('fetch', function(event) {
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                // إرجاع النسخة المحفوظة إذا وجدت
                if (response) {
                    return response;
                }

                return fetch(event.request).then(
                    function(response) {
                        // التحقق من صحة الاستجابة
                        if(!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }

                        // نسخ الاستجابة
                        var responseToCache = response.clone();

                        caches.open(CACHE_NAME)
                            .then(function(cache) {
                                cache.put(event.request, responseToCache);
                            });

                        return response;
                    }
                );
            })
    );
});

// معالجة الرسائل من التطبيق الرئيسي
self.addEventListener('message', function(event) {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});

// إشعارات Push
self.addEventListener('push', function(event) {
    const options = {
        body: event.data ? event.data.text() : 'رسالة جديدة من النظام المحاسبي',
        icon: '/static/icons/icon-192x192.png',
        badge: '/static/icons/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: '2'
        },
        actions: [
            {
                action: 'explore',
                title: 'عرض التفاصيل',
                icon: '/static/icons/checkmark.png'
            },
            {
                action: 'close',
                title: 'إغلاق',
                icon: '/static/icons/xmark.png'
            }
        ]
    };

    event.waitUntil(
        self.registration.showNotification('النظام المحاسبي المتقدم', options)
    );
});

// معالجة النقر على الإشعارات
self.addEventListener('notificationclick', function(event) {
    event.notification.close();

    if (event.action === 'explore') {
        // فتح التطبيق
        event.waitUntil(clients.openWindow('/'));
    } else if (event.action === 'close') {
        // إغلاق الإشعار فقط
        event.notification.close();
    } else {
        // النقر على الإشعار نفسه
        event.waitUntil(clients.openWindow('/'));
    }
});

// مزامنة البيانات في الخلفية
self.addEventListener('sync', function(event) {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

function doBackgroundSync() {
    return new Promise(function(resolve, reject) {
        // مزامنة البيانات المحلية مع الخادم
        console.log('تم تنفيذ المزامنة في الخلفية');
        resolve();
    });
}

// معالجة الأخطاء
self.addEventListener('error', function(event) {
    console.error('خطأ في Service Worker:', event.error);
});

// معالجة الأخطاء غير المعالجة
self.addEventListener('unhandledrejection', function(event) {
    console.error('Promise مرفوض في Service Worker:', event.reason);
    event.preventDefault();
});
