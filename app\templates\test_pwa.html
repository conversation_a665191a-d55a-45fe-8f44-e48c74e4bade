<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- PWA Meta Tags -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="المحاسبة">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="application-name" content="المحاسبة">
    <meta name="msapplication-TileColor" content="#667eea">
    <meta name="theme-color" content="#667eea">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="192x192" href="/static/icons/icon-192x192.png">
    <link rel="apple-touch-icon" href="/static/icons/icon-192x192.png">

    <title>اختبار PWA - النظام المحاسبي المتقدم</title>
    
    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- PWA CSS -->
    <link href="/static/css/pwa.css" rel="stylesheet">
    
    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .feature-check {
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .feature-check.success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        
        .feature-check.error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        
        .status-icon {
            font-size: 1.5rem;
            margin-left: 10px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="test-card p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-mobile-alt fa-4x text-primary mb-3"></i>
                        <h1 class="h2 mb-3">اختبار PWA</h1>
                        <p class="lead text-muted">النظام المحاسبي المتقدم</p>
                    </div>

                    <!-- PWA Features Check -->
                    <div class="mb-4">
                        <h3 class="h4 mb-3">فحص ميزات PWA</h3>
                        
                        <div id="manifest-check" class="feature-check">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file-code status-icon text-warning"></i>
                                <div>
                                    <strong>Manifest File</strong>
                                    <div class="small text-muted">ملف إعدادات التطبيق</div>
                                </div>
                                <div class="ms-auto">
                                    <span class="badge bg-warning">جاري الفحص...</span>
                                </div>
                            </div>
                        </div>

                        <div id="sw-check" class="feature-check">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-cogs status-icon text-warning"></i>
                                <div>
                                    <strong>Service Worker</strong>
                                    <div class="small text-muted">العمل في الخلفية والتخزين المؤقت</div>
                                </div>
                                <div class="ms-auto">
                                    <span class="badge bg-warning">جاري الفحص...</span>
                                </div>
                            </div>
                        </div>

                        <div id="install-check" class="feature-check">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-download status-icon text-warning"></i>
                                <div>
                                    <strong>إمكانية التثبيت</strong>
                                    <div class="small text-muted">تثبيت التطبيق على الجهاز</div>
                                </div>
                                <div class="ms-auto">
                                    <span class="badge bg-warning">جاري الفحص...</span>
                                </div>
                            </div>
                        </div>

                        <div id="offline-check" class="feature-check">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-wifi status-icon text-warning"></i>
                                <div>
                                    <strong>العمل بدون إنترنت</strong>
                                    <div class="small text-muted">استخدام التطبيق بدون اتصال</div>
                                </div>
                                <div class="ms-auto">
                                    <span class="badge bg-warning">جاري الفحص...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Test Results -->
                    <div id="test-results" class="mb-4" style="display: none;">
                        <h3 class="h4 mb-3">نتائج الاختبار</h3>
                        <div class="alert alert-info">
                            <div id="results-content"></div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="text-center">
                        <button id="run-test" class="btn test-button me-3">
                            <i class="fas fa-play me-2"></i>تشغيل الاختبار
                        </button>
                        
                        <button id="install-app" class="btn btn-success" style="display: none;">
                            <i class="fas fa-download me-2"></i>تثبيت التطبيق
                        </button>
                        
                        <button id="manual-install" class="btn btn-outline-primary">
                            <i class="fas fa-question-circle me-2"></i>كيفية التثبيت
                        </button>
                    </div>

                    <!-- Instructions -->
                    <div class="mt-5">
                        <h3 class="h5 mb-3">تعليمات الاختبار:</h3>
                        <ol class="text-muted">
                            <li>اضغط على "تشغيل الاختبار" لفحص ميزات PWA</li>
                            <li>تأكد من أن جميع الميزات تعمل بشكل صحيح</li>
                            <li>ابحث عن أيقونة التثبيت في شريط العنوان</li>
                            <li>جرب تثبيت التطبيق واستخدامه</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Mobile JS -->
    <script src="/static/js/mobile.js"></script>
    
    <!-- Test Script -->
    <script>
        class PWATest {
            constructor() {
                this.deferredPrompt = null;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.checkPWASupport();
            }

            setupEventListeners() {
                document.getElementById('run-test').addEventListener('click', () => this.runTests());
                document.getElementById('install-app').addEventListener('click', () => this.installApp());
                document.getElementById('manual-install').addEventListener('click', () => this.showInstallInstructions());

                // PWA Events
                window.addEventListener('beforeinstallprompt', (e) => {
                    e.preventDefault();
                    this.deferredPrompt = e;
                    document.getElementById('install-app').style.display = 'inline-block';
                });

                window.addEventListener('appinstalled', () => {
                    console.log('تم تثبيت التطبيق');
                    document.getElementById('install-app').style.display = 'none';
                    this.showSuccess('تم تثبيت التطبيق بنجاح!');
                });
            }

            checkPWASupport() {
                // Check basic PWA support
                const hasServiceWorker = 'serviceWorker' in navigator;
                const hasManifest = 'manifest' in document.createElement('link');
                
                console.log('PWA Support Check:', {
                    serviceWorker: hasServiceWorker,
                    manifest: hasManifest
                });
            }

            async runTests() {
                document.getElementById('run-test').disabled = true;
                document.getElementById('run-test').innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الاختبار...';

                // Test Manifest
                await this.testManifest();
                
                // Test Service Worker
                await this.testServiceWorker();
                
                // Test Install Capability
                await this.testInstallCapability();
                
                // Test Offline Support
                await this.testOfflineSupport();

                document.getElementById('run-test').disabled = false;
                document.getElementById('run-test').innerHTML = '<i class="fas fa-play me-2"></i>تشغيل الاختبار';
                
                this.showResults();
            }

            async testManifest() {
                try {
                    const response = await fetch('/manifest.json');
                    if (response.ok) {
                        const manifest = await response.json();
                        this.updateFeatureStatus('manifest-check', true, 'Manifest متاح');
                        console.log('Manifest loaded:', manifest);
                    } else {
                        this.updateFeatureStatus('manifest-check', false, 'فشل في تحميل Manifest');
                    }
                } catch (error) {
                    this.updateFeatureStatus('manifest-check', false, 'خطأ في Manifest');
                    console.error('Manifest error:', error);
                }
            }

            async testServiceWorker() {
                if ('serviceWorker' in navigator) {
                    try {
                        const registration = await navigator.serviceWorker.register('/sw.js');
                        this.updateFeatureStatus('sw-check', true, 'Service Worker مسجل');
                        console.log('Service Worker registered:', registration);
                    } catch (error) {
                        this.updateFeatureStatus('sw-check', false, 'فشل تسجيل Service Worker');
                        console.error('Service Worker error:', error);
                    }
                } else {
                    this.updateFeatureStatus('sw-check', false, 'Service Worker غير مدعوم');
                }
            }

            async testInstallCapability() {
                if (this.deferredPrompt) {
                    this.updateFeatureStatus('install-check', true, 'التطبيق قابل للتثبيت');
                } else if (window.matchMedia('(display-mode: standalone)').matches) {
                    this.updateFeatureStatus('install-check', true, 'التطبيق مثبت بالفعل');
                } else {
                    this.updateFeatureStatus('install-check', false, 'التثبيت غير متاح حالياً');
                }
            }

            async testOfflineSupport() {
                if ('serviceWorker' in navigator && 'caches' in window) {
                    try {
                        const cacheNames = await caches.keys();
                        if (cacheNames.length > 0) {
                            this.updateFeatureStatus('offline-check', true, 'التخزين المؤقت متاح');
                        } else {
                            this.updateFeatureStatus('offline-check', false, 'لا يوجد تخزين مؤقت');
                        }
                    } catch (error) {
                        this.updateFeatureStatus('offline-check', false, 'خطأ في فحص التخزين المؤقت');
                    }
                } else {
                    this.updateFeatureStatus('offline-check', false, 'التخزين المؤقت غير مدعوم');
                }
            }

            updateFeatureStatus(elementId, success, message) {
                const element = document.getElementById(elementId);
                const badge = element.querySelector('.badge');
                const icon = element.querySelector('.status-icon');
                
                if (success) {
                    element.classList.add('success');
                    element.classList.remove('error');
                    badge.className = 'badge bg-success';
                    badge.textContent = '✓ يعمل';
                    icon.className = 'fas fa-check-circle status-icon text-success';
                } else {
                    element.classList.add('error');
                    element.classList.remove('success');
                    badge.className = 'badge bg-danger';
                    badge.textContent = '✗ لا يعمل';
                    icon.className = 'fas fa-times-circle status-icon text-danger';
                }
                
                console.log(`${elementId}: ${message}`);
            }

            showResults() {
                const resultsDiv = document.getElementById('test-results');
                const contentDiv = document.getElementById('results-content');
                
                const successCount = document.querySelectorAll('.feature-check.success').length;
                const totalCount = document.querySelectorAll('.feature-check').length;
                
                let resultText = `تم اختبار ${totalCount} ميزات، ${successCount} منها تعمل بشكل صحيح.`;
                
                if (successCount === totalCount) {
                    contentDiv.innerHTML = `<i class="fas fa-check-circle text-success me-2"></i>${resultText} التطبيق جاهز للتثبيت!`;
                    resultsDiv.className = 'mb-4 alert alert-success';
                } else {
                    contentDiv.innerHTML = `<i class="fas fa-exclamation-triangle text-warning me-2"></i>${resultText} قد تحتاج لإصلاح بعض المشاكل.`;
                    resultsDiv.className = 'mb-4 alert alert-warning';
                }
                
                resultsDiv.style.display = 'block';
            }

            installApp() {
                if (this.deferredPrompt) {
                    this.deferredPrompt.prompt();
                    this.deferredPrompt.userChoice.then((result) => {
                        if (result.outcome === 'accepted') {
                            console.log('المستخدم وافق على التثبيت');
                        } else {
                            console.log('المستخدم رفض التثبيت');
                        }
                        this.deferredPrompt = null;
                    });
                }
            }

            showInstallInstructions() {
                const isChrome = /Chrome/.test(navigator.userAgent);
                const isEdge = /Edg/.test(navigator.userAgent);
                
                let instructions = '';
                
                if (isChrome || isEdge) {
                    instructions = `
                        <h5>تثبيت التطبيق في Chrome/Edge:</h5>
                        <ol>
                            <li>ابحث عن أيقونة التثبيت في شريط العنوان</li>
                            <li>أو اضغط على القائمة (⋮) واختر "تثبيت التطبيق"</li>
                            <li>اتبع التعليمات لإكمال التثبيت</li>
                        </ol>
                    `;
                } else {
                    instructions = `
                        <h5>تثبيت التطبيق:</h5>
                        <p>ابحث عن خيار "تثبيت التطبيق" أو "Add to Home Screen" في قائمة المتصفح</p>
                    `;
                }
                
                alert(instructions);
            }

            showSuccess(message) {
                const alert = document.createElement('div');
                alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
                alert.style.cssText = 'top: 20px; right: 20px; z-index: 1050;';
                alert.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.appendChild(alert);
                
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 5000);
            }
        }

        // Initialize PWA Test
        document.addEventListener('DOMContentLoaded', () => {
            new PWATest();
        });
    </script>
</body>
</html>
