#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تنظيف أوامر الشراء اليتيمة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def cleanup_orphan_po():
    """تنظيف أوامر الشراء اليتيمة"""
    print("🧹 تنظيف أوامر الشراء اليتيمة...")
    
    try:
        from oracle_manager import get_oracle_manager
        db_manager = get_oracle_manager()
        
        if not db_manager.connect():
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        # العثور على أوامر الشراء اليتيمة
        orphan_query = """
            SELECT po.ID, po.PO_NUMBER, po.USED_IN_SHIPMENT_ID
            FROM PURCHASE_ORDERS po
            WHERE po.IS_USED = 1 
            AND po.USED_IN_SHIPMENT_ID IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 FROM cargo_shipments cs 
                WHERE cs.id = po.USED_IN_SHIPMENT_ID
            )
        """
        
        orphans = db_manager.execute_query(orphan_query)
        
        if not orphans:
            print("✅ لا توجد أوامر شراء يتيمة")
            db_manager.close()
            return True
        
        print(f"🔍 تم العثور على {len(orphans)} أمر شراء يتيم:")
        
        for orphan in orphans:
            po_id, po_number, missing_shipment_id = orphan
            print(f"   📋 {po_number} (ID: {po_id}) - مرتبط بشحنة غير موجودة: {missing_shipment_id}")
        
        # إعادة تعيين أوامر الشراء اليتيمة
        reset_query = """
            UPDATE PURCHASE_ORDERS 
            SET IS_USED = 0,
                USED_AT = NULL,
                USED_IN_SHIPMENT_ID = NULL,
                UPDATED_AT = SYSDATE
            WHERE IS_USED = 1 
            AND USED_IN_SHIPMENT_ID IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 FROM cargo_shipments cs 
                WHERE cs.id = USED_IN_SHIPMENT_ID
            )
        """
        
        result = db_manager.execute_update(reset_query)
        print(f"✅ تم إعادة تعيين {result} أمر شراء يتيم")
        
        db_manager.commit()
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التنظيف: {e}")
        try:
            if 'db_manager' in locals():
                db_manager.rollback()
                db_manager.close()
        except:
            pass
        return False

if __name__ == "__main__":
    cleanup_orphan_po()
