-- إن<PERSON><PERSON>ء جدول موردين التنفيذ
CREATE TABLE transfer_execution_suppliers (
    id NUMBER PRIMARY KEY,
    transfer_id NUMBER NOT NULL,
    supplier_id NUMBER NOT NULL,
    supplier_name VARCHAR2(200),
    supplier_type VARCHAR2(50),
    amount NUMBER(15,2) NOT NULL,
    currency VARCHAR2(10) DEFAULT 'USD',
    exchange_rate NUMBER(10,4) DEFAULT 1.0000,
    commission NUMBER(15,2) DEFAULT 0,
    supplier_reference VARCHAR2(100),
    supplier_notes VARCHAR2(1000),
    execution_order NUMBER DEFAULT 1,
    status VARCHAR2(20) DEFAULT 'pending',
    executed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER
);

-- إنشاء sequence
CREATE SEQUENCE transfer_exec_supp_seq START WITH 1 INCREMENT BY 1;

-- إنشاء trigger
CREATE OR REPLACE TRIGGER transfer_exec_supp_trigger
    BEFORE INSERT ON transfer_execution_suppliers
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := transfer_exec_supp_seq.NEXTVAL;
    END IF;
END;
/

-- إنشاء فهارس
CREATE INDEX idx_exec_supp_transfer ON transfer_execution_suppliers(transfer_id);
CREATE INDEX idx_exec_supp_supplier ON transfer_execution_suppliers(supplier_id);
CREATE INDEX idx_exec_supp_status ON transfer_execution_suppliers(status);

COMMIT;
