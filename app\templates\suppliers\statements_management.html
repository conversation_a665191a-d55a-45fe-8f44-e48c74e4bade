{% extends "base.html" %}

{% block title %}إدارة كشوفات الحساب{% endblock %}

{% block extra_css %}
<style>
    .statement-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border-left: 5px solid #007bff;
    }
    
    .statement-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    }
    
    .statement-card.generated {
        border-left-color: #28a745;
    }
    
    .statement-card.sent {
        border-left-color: #17a2b8;
    }
    
    .statement-card.draft {
        border-left-color: #ffc107;
    }
    
    .statement-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .statement-info h6 {
        margin: 0;
        color: #333;
        font-weight: 600;
    }
    
    .statement-info small {
        color: #666;
    }
    
    .statement-status {
        text-align: right;
    }
    
    .status-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }
    
    .status-generated { background: #d4edda; color: #155724; }
    .status-sent { background: #d1ecf1; color: #0c5460; }
    .status-draft { background: #fff3cd; color: #856404; }
    
    .statement-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .detail-item {
        text-align: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 10px;
    }
    
    .detail-value {
        font-size: 1.2rem;
        font-weight: bold;
        color: #007bff;
    }
    
    .detail-label {
        font-size: 0.85rem;
        color: #666;
        margin-top: 5px;
    }
    
    .statement-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }
    
    .btn-sm-custom {
        padding: 5px 12px;
        font-size: 0.85rem;
        border-radius: 15px;
    }
    
    .filters-section {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .stats-overview {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
    }
    
    .stat-card.green {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    .stat-card.orange {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }
    
    .stat-card.blue {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .template-selector {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .template-card {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .template-card:hover {
        border-color: #007bff;
        background-color: #f8f9fa;
    }
    
    .template-card.selected {
        border-color: #007bff;
        background-color: #e7f3ff;
    }
    
    .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 300px;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #ddd;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-file-invoice text-primary"></i> إدارة كشوفات الحساب</h2>
                    <p class="text-muted">إنشاء وإدارة كشوفات حساب الموردين مع تصدير متعدد الصيغ</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="showGenerateStatementModal()">
                        <i class="fas fa-plus"></i> إنشاء كشف جديد
                    </button>
                    <button class="btn btn-success" onclick="refreshStatements()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                    <button class="btn btn-info" onclick="manageTemplates()">
                        <i class="fas fa-cog"></i> إدارة القوالب
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Overview -->
    <div class="stats-overview">
        <div class="stat-card">
            <div class="stat-number" id="totalStatements">0</div>
            <div class="stat-label">إجمالي الكشوفات</div>
        </div>
        <div class="stat-card green">
            <div class="stat-number" id="sentStatements">0</div>
            <div class="stat-label">كشوفات مرسلة</div>
        </div>
        <div class="stat-card orange">
            <div class="stat-number" id="draftStatements">0</div>
            <div class="stat-label">مسودات</div>
        </div>
        <div class="stat-card blue">
            <div class="stat-number" id="thisMonthStatements">0</div>
            <div class="stat-label">كشوفات هذا الشهر</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" id="searchInput" placeholder="اسم المورد أو رقم الكشف...">
            </div>
            <div class="col-md-2">
                <label class="form-label">حالة الكشف</label>
                <select class="form-select" id="statusFilter">
                    <option value="all">جميع الحالات</option>
                    <option value="GENERATED">مُنشأ</option>
                    <option value="SENT">مُرسل</option>
                    <option value="DRAFT">مسودة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">نوع الكشف</label>
                <select class="form-select" id="typeFilter">
                    <option value="all">جميع الأنواع</option>
                    <option value="DETAILED">تفصيلي</option>
                    <option value="SUMMARY">ملخص</option>
                    <option value="AGING">تحليل الاستحقاقات</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الفترة</label>
                <select class="form-select" id="periodFilter">
                    <option value="all">جميع الفترات</option>
                    <option value="this_month">هذا الشهر</option>
                    <option value="last_month">الشهر الماضي</option>
                    <option value="this_quarter">هذا الربع</option>
                    <option value="this_year">هذا العام</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">تاريخ من</label>
                <input type="date" class="form-control" id="dateFrom">
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Statements List -->
    <div id="statementsList">
        <div class="loading-container">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <div class="d-flex justify-content-between align-items-center mt-4">
        <div>
            <span class="text-muted">عرض <span id="showingFrom">0</span> إلى <span id="showingTo">0</span> من <span id="totalRecords">0</span> سجل</span>
        </div>
        <nav>
            <ul class="pagination" id="pagination">
                <!-- سيتم إنشاؤها ديناميكياً -->
            </ul>
        </nav>
    </div>
</div>

<!-- Generate Statement Modal -->
<div class="modal fade" id="generateStatementModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء كشف حساب جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- Supplier Selection -->
                    <div class="col-md-6">
                        <div class="mb-4">
                            <h6>اختيار المورد</h6>
                            <select class="form-select" id="supplierSelect" required>
                                <option value="">اختر المورد</option>
                            </select>
                        </div>
                        
                        <div class="mb-4">
                            <h6>فترة الكشف</h6>
                            <div class="row">
                                <div class="col-6">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="periodFrom" required>
                                </div>
                                <div class="col-6">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="periodTo" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <h6>إعدادات الكشف</h6>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeOpeningBalance" checked>
                                <label class="form-check-label" for="includeOpeningBalance">
                                    تضمين الرصيد الافتتاحي
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeAgingAnalysis">
                                <label class="form-check-label" for="includeAgingAnalysis">
                                    تضمين تحليل الاستحقاقات
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includePaymentHistory">
                                <label class="form-check-label" for="includePaymentHistory">
                                    تضمين تاريخ المدفوعات
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Template Selection -->
                    <div class="col-md-6">
                        <div class="template-selector">
                            <h6>اختيار القالب</h6>
                            <div id="templatesList">
                                <div class="template-card" data-template="detailed">
                                    <h6>كشف تفصيلي</h6>
                                    <p class="text-muted mb-0">يعرض جميع المعاملات بالتفصيل</p>
                                </div>
                                <div class="template-card" data-template="summary">
                                    <h6>كشف ملخص</h6>
                                    <p class="text-muted mb-0">يعرض ملخص الأرصدة والإجماليات</p>
                                </div>
                                <div class="template-card" data-template="aging">
                                    <h6>تحليل الاستحقاقات</h6>
                                    <p class="text-muted mb-0">يركز على تحليل المبالغ المستحقة</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">صيغة التصدير</label>
                            <select class="form-select" id="outputFormat">
                                <option value="PDF">PDF</option>
                                <option value="EXCEL">Excel</option>
                                <option value="WORD">Word</option>
                                <option value="HTML">HTML</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">اللغة</label>
                            <select class="form-select" id="language">
                                <option value="AR">العربية</option>
                                <option value="EN">English</option>
                                <option value="BOTH">ثنائي اللغة</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="generateStatement()">إنشاء الكشف</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/statements_management.js') }}"></script>
<script>
$(document).ready(function() {
    // تحميل البيانات عند تحميل الصفحة
    loadStatementsData();
    loadSuppliersList();
    
    // ربط أحداث الفلاتر
    $('#searchInput, #statusFilter, #typeFilter, #periodFilter, #dateFrom').on('input change', function() {
        filterStatements();
    });
    
    // ربط أحداث اختيار القالب
    $('.template-card').click(function() {
        $('.template-card').removeClass('selected');
        $(this).addClass('selected');
    });
    
    // تعيين التواريخ الافتراضية
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    $('#periodFrom').val(formatDateForInput(firstDay));
    $('#periodTo').val(formatDateForInput(today));
});

function formatDateForInput(date) {
    return date.toISOString().split('T')[0];
}
</script>
{% endblock %}
