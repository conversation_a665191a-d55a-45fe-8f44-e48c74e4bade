"""
اختبار بسيط لحساب أرصدة المخزون
Simple Inventory Balance Test
"""

from database_connector import db_connector
import pandas as pd

def main():
    print("🧪 اختبار حساب أرصدة المخزون")
    print("=" * 50)
    
    try:
        # الاتصال بقاعدة البيانات
        if not db_connector.connect():
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # استعلام بسيط لحساب الأرصدة
        query = """
        SELECT 
            i_code,
            a_desc,
            SUM(CASE WHEN in_out = 1 THEN i_qty ELSE -i_qty END) as balance,
            COUNT(*) as movement_count,
            MAX(i_date) as last_movement
        FROM ITEM_MOVEMENT
        WHERE ROWNUM <= 1000
        GROUP BY i_code, a_desc
        ORDER BY balance DESC
        """
        
        print("📊 جاري حساب الأرصدة...")
        df = db_connector.execute_query(query)
        
        if not df.empty:
            print(f"✅ تم حساب أرصدة {len(df)} صنف")
            
            # عرض النتائج
            print("\n📋 أعلى 10 أصناف رصيداً:")
            print("-" * 80)
            print(f"{'كود الصنف':<15} {'اسم الصنف':<30} {'الرصيد':<10} {'عدد الحركات':<15}")
            print("-" * 80)
            
            for _, row in df.head(10).iterrows():
                item_name = str(row['A_DESC'])[:28] if pd.notna(row['A_DESC']) else 'غير محدد'
                print(f"{row['I_CODE']:<15} {item_name:<30} {row['BALANCE']:<10.2f} {row['MOVEMENT_COUNT']:<15}")
            
            # إحصائيات عامة
            total_items = len(df)
            positive_balance = len(df[df['BALANCE'] > 0])
            zero_balance = len(df[df['BALANCE'] == 0])
            negative_balance = len(df[df['BALANCE'] < 0])
            
            print(f"\n📊 ملخص الأرصدة:")
            print(f"   • إجمالي الأصناف: {total_items}")
            print(f"   • أرصدة موجبة: {positive_balance}")
            print(f"   • أرصدة صفر: {zero_balance}")
            print(f"   • أرصدة سالبة: {negative_balance}")
            
            # حفظ النتائج
            filename = f"simple_balances_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"\n💾 تم حفظ النتائج في: {filename}")
            
        else:
            print("❌ لا توجد بيانات")
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
    
    finally:
        db_connector.disconnect()
        print("🔌 تم قطع الاتصال")

if __name__ == "__main__":
    main()
