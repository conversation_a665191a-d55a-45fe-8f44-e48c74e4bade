#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إدارة المستفيدين
Beneficiaries Management
"""

from flask import render_template, request, jsonify, flash, redirect, url_for, session
from flask_login import login_required, current_user
from . import transfers_bp
from database_manager import DatabaseManager
import logging
import re

logger = logging.getLogger(__name__)

@transfers_bp.route('/beneficiaries')
@login_required
def beneficiaries():
    """صفحة إدارة المستفيدين"""
    try:
        db = DatabaseManager()

        # استعلام بسيط للمستفيدين
        beneficiaries_query = """
        SELECT
            b.id,
            b.beneficiary_name,
            b.beneficiary_address,
            b.type,
            b.bank_account,
            b.bank_name,
            b.bank_branch,
            b.bank_country,
            b.iban,
            b.swift_code,
            b.identification_number,
            b.phone,
            b.email,
            b.is_active,
            b.created_at
        FROM beneficiaries b
        ORDER BY b.beneficiary_name
        """
        
        result = db.execute_query(beneficiaries_query)
        beneficiaries_list = []
        
        if result:
            for row in result:
                beneficiaries_list.append({
                    'id': row[0],
                    'beneficiary_name': row[1] or '',
                    'beneficiary_address': row[2] or '',
                    'type': row[3] or '',
                    'bank_account': row[4] or '',
                    'bank_name': row[5] or '',
                    'bank_branch': row[6] or '',
                    'bank_country': row[7] or '',
                    'iban': row[8] or '',
                    'swift_code': row[9] or '',
                    'identification_number': row[10] or '',
                    'phone': row[11] or '',
                    'email': row[12] or '',
                    'is_active': bool(row[13]) if row[13] is not None else True,
                    'created_at': str(row[14]) if row[14] else '',
                    'transfer_count': 0,  # قيمة افتراضية
                    'total_amount': 0.0,  # قيمة افتراضية
                    'supplier_name': ''  # قيمة افتراضية
                })
        
        logger.info(f"تم جلب {len(beneficiaries_list)} مستفيد")

        return render_template('transfers/beneficiaries.html',
                             beneficiaries=beneficiaries_list)
        
    except Exception as e:
        logger.error(f"خطأ في صفحة المستفيدين: {e}")
        flash('حدث خطأ في تحميل البيانات', 'error')
        return render_template('transfers/beneficiaries.html',
                             beneficiaries=[])

@transfers_bp.route('/beneficiaries/<int:beneficiary_id>/details')
@login_required
def get_beneficiary_details(beneficiary_id):
    """جلب تفاصيل مستفيد محدد"""
    try:
        db = DatabaseManager()

        query = """
        SELECT
            id, beneficiary_name, beneficiary_address, type,
            bank_account, bank_name, bank_branch, bank_country,
            iban, swift_code, identification_number, phone,
            email, is_active, created_at
        FROM beneficiaries
        WHERE id = :beneficiary_id
        """

        result = db.execute_query(query, {'beneficiary_id': beneficiary_id})

        if result and len(result) > 0:
            row = result[0]
            beneficiary_data = {
                'id': row[0],
                'beneficiary_name': row[1],
                'beneficiary_address': row[2],
                'type': row[3],
                'bank_account': row[4],
                'bank_name': row[5],
                'bank_branch': row[6],
                'bank_country': row[7],
                'iban': row[8],
                'swift_code': row[9],
                'identification_number': row[10],
                'phone': row[11],
                'email': row[12],
                'is_active': bool(row[13]),
                'created_at': str(row[14]) if row[14] else None
            }

            return jsonify({
                'success': True,
                'data': beneficiary_data
            })
        else:
            return jsonify({
                'success': False,
                'message': 'المستفيد غير موجود'
            })

    except Exception as e:
        logger.error(f"خطأ في جلب تفاصيل المستفيد {beneficiary_id}: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في جلب البيانات'
        })

@transfers_bp.route('/beneficiaries/add', methods=['POST'])
@login_required
def add_beneficiary():
    """إضافة مستفيد جديد"""
    try:
        db = DatabaseManager()
        
        # استلام البيانات من النموذج
        data = request.get_json() if request.is_json else request.form
        
        beneficiary_name = data.get('beneficiary_name', '').strip()
        beneficiary_address = data.get('beneficiary_address', '').strip()
        type_value = data.get('type', '').strip()
        supplier_id = data.get('supplier_id')
        bank_account = data.get('bank_account', '').strip()
        bank_name = data.get('bank_name', '').strip()
        bank_branch = data.get('bank_branch', '').strip()
        bank_country = data.get('bank_country', '').strip()
        iban = data.get('iban', '').strip()
        swift_code = data.get('swift_code', '').strip()
        identification_number = data.get('identification_number', '').strip()
        phone = data.get('phone', '').strip()
        email = data.get('email', '').strip()
        
        # التحقق من البيانات المطلوبة
        if not beneficiary_name:
            return jsonify({'success': False, 'message': 'اسم المستفيد مطلوب'})
        
        if not type_value or type_value not in ['individual', 'company', 'supplier', 'vendor']:
            return jsonify({'success': False, 'message': 'نوع المستفيد غير صحيح'})
        
        # التحقق من الحقول المطلوبة
        if not bank_account:
            return jsonify({'success': False, 'message': 'رقم الحساب البنكي مطلوب'})

        if not bank_name:
            return jsonify({'success': False, 'message': 'اسم البنك مطلوب'})

        if not beneficiary_address:
            return jsonify({'success': False, 'message': 'العنوان مطلوب'})

        if not bank_country:
            return jsonify({'success': False, 'message': 'بلد البنك مطلوب'})

        # الحقول الاختيارية: phone, email, identification_number
        # باقي الحقول مطلوبة
        
        # التحقق من صحة البريد الإلكتروني
        if email and not is_valid_email(email):
            return jsonify({'success': False, 'message': 'البريد الإلكتروني غير صحيح'})
        
        # التحقق من صحة IBAN (مؤقتاً معطل)
        # if iban and not is_valid_iban(iban):
        #     return jsonify({'success': False, 'message': 'رقم IBAN غير صحيح'})
        
        # السماح بتكرار رقم الحساب (بعض المستفيدين يشتركون في نفس الحساب)
        # existing_query = "SELECT id FROM beneficiaries WHERE bank_account = :1"
        # existing = db.execute_query(existing_query, [bank_account])
        #
        # if existing:
        #     return jsonify({'success': False, 'message': 'رقم الحساب البنكي موجود مسبقاً'})
        
        # إدراج البيانات
        insert_query = """
        INSERT INTO beneficiaries
        (id, beneficiary_name, beneficiary_address, type, supplier_id, bank_account,
         bank_name, bank_branch, bank_country, iban, swift_code,
         identification_number, phone, email, is_active, created_at, updated_at)
        VALUES (BENEFICIARIES_SEQ.NEXTVAL, :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13, 1,
                CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        """

        logger.info("🔧 استخدام query الجديد مع BENEFICIARIES_SEQ.NEXTVAL")
        
        db.execute_update(insert_query, [
            beneficiary_name, beneficiary_address, type_value, supplier_id,
            bank_account, bank_name, bank_branch, bank_country, iban,
            swift_code, identification_number, phone, email
        ])
        
        db.commit()
        
        logger.info(f"تم إضافة مستفيد جديد: {beneficiary_name} بواسطة {current_user.username}")
        
        return jsonify({
            'success': True, 
            'message': f'تم إضافة المستفيد {beneficiary_name} بنجاح'
        })
        
    except Exception as e:
        logger.error(f"خطأ في إضافة مستفيد: {e}")
        return jsonify({
            'success': False, 
            'message': 'حدث خطأ في إضافة المستفيد'
        }), 500

@transfers_bp.route('/beneficiaries/<int:beneficiary_id>/edit', methods=['POST'])
@login_required
def edit_beneficiary(beneficiary_id):
    """تعديل مستفيد"""
    try:
        db = DatabaseManager()
        
        # التحقق من وجود المستفيد
        existing_query = "SELECT id FROM beneficiaries WHERE id = :1"
        existing = db.execute_query(existing_query, [beneficiary_id])
        
        if not existing:
            return jsonify({'success': False, 'message': 'المستفيد غير موجود'})
        
        # استلام البيانات من النموذج
        data = request.get_json() if request.is_json else request.form
        
        beneficiary_name = data.get('beneficiary_name', '').strip()
        beneficiary_address = data.get('beneficiary_address', '').strip()
        type_value = data.get('type', '').strip()
        supplier_id = data.get('supplier_id')
        bank_account = data.get('bank_account', '').strip()
        bank_name = data.get('bank_name', '').strip()
        bank_branch = data.get('bank_branch', '').strip()
        bank_country = data.get('bank_country', '').strip()
        iban = data.get('iban', '').strip()
        swift_code = data.get('swift_code', '').strip()
        identification_number = data.get('identification_number', '').strip()
        phone = data.get('phone', '').strip()
        email = data.get('email', '').strip()
        is_active = data.get('is_active', True)
        
        # التحقق من البيانات المطلوبة
        if not beneficiary_name:
            return jsonify({'success': False, 'message': 'اسم المستفيد مطلوب'})
        
        if not type_value or type_value not in ['individual', 'company', 'supplier', 'vendor']:
            return jsonify({'success': False, 'message': 'نوع المستفيد غير صحيح'})
        
        # التحقق من الحقول المطلوبة
        if not bank_account:
            return jsonify({'success': False, 'message': 'رقم الحساب البنكي مطلوب'})

        if not bank_name:
            return jsonify({'success': False, 'message': 'اسم البنك مطلوب'})

        if not beneficiary_address:
            return jsonify({'success': False, 'message': 'العنوان مطلوب'})

        if not bank_country:
            return jsonify({'success': False, 'message': 'بلد البنك مطلوب'})

        # الحقول الاختيارية: phone, email, identification_number
        
        # التحقق من صحة البريد الإلكتروني
        if email and not is_valid_email(email):
            return jsonify({'success': False, 'message': 'البريد الإلكتروني غير صحيح'})
        
        # التحقق من صحة IBAN (مؤقتاً معطل)
        # if iban and not is_valid_iban(iban):
        #     return jsonify({'success': False, 'message': 'رقم IBAN غير صحيح'})
        
        # السماح بتكرار رقم الحساب (بعض المستفيدين يشتركون في نفس الحساب)
        # duplicate_query = "SELECT id FROM beneficiaries WHERE bank_account = :1 AND id != :2"
        # duplicate = db.execute_query(duplicate_query, [bank_account, beneficiary_id])
        #
        # if duplicate:
        #     return jsonify({'success': False, 'message': 'رقم الحساب البنكي موجود مسبقاً'})
        
        # تحديث البيانات
        update_query = """
        UPDATE beneficiaries 
        SET beneficiary_name = :1, beneficiary_address = :2, type = :3, 
            supplier_id = :4, bank_account = :5, bank_name = :6, 
            bank_branch = :7, bank_country = :8, iban = :9, swift_code = :10,
            identification_number = :11, phone = :12, email = :13, 
            is_active = :14, updated_at = CURRENT_TIMESTAMP
        WHERE id = :15
        """
        
        db.execute_update(update_query, [
            beneficiary_name, beneficiary_address, type_value, supplier_id,
            bank_account, bank_name, bank_branch, bank_country, iban,
            swift_code, identification_number, phone, email,
            1 if is_active else 0, beneficiary_id
        ])
        
        db.commit()
        
        logger.info(f"تم تعديل مستفيد: {beneficiary_name} بواسطة {current_user.username}")
        
        return jsonify({
            'success': True, 
            'message': f'تم تعديل المستفيد {beneficiary_name} بنجاح'
        })
        
    except Exception as e:
        logger.error(f"خطأ في تعديل مستفيد: {e}")
        return jsonify({
            'success': False, 
            'message': 'حدث خطأ في تعديل المستفيد'
        }), 500

def is_valid_email(email):
    """التحقق من صحة البريد الإلكتروني"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def is_valid_iban(iban):
    """التحقق من صحة رقم IBAN - مرن"""
    if not iban or not iban.strip():
        return True  # IBAN اختياري

    # إزالة المسافات والرموز
    iban = re.sub(r'[^A-Z0-9]', '', iban.upper())

    # إذا كان أقل من 4 أحرف، نعتبره رقم حساب عادي
    if len(iban) < 4:
        return True

    # إذا بدأ بحرفين، نتحقق من صيغة IBAN
    if iban[:2].isalpha():
        # التحقق من الطول (15-34 حرف)
        if len(iban) < 15 or len(iban) > 34:
            return False

        # التحقق من أن الحرفين التاليين هما أرقام
        if not iban[2:4].isdigit():
            return False

    # إذا كان كله أرقام، نعتبره رقم حساب عادي
    return True

@transfers_bp.route('/beneficiaries/<int:beneficiary_id>/delete', methods=['POST'])
@login_required
def delete_beneficiary(beneficiary_id):
    """حذف مستفيد"""
    try:
        db = DatabaseManager()

        # التحقق من وجود المستفيد
        check_query = "SELECT beneficiary_name FROM beneficiaries WHERE id = :1"
        existing = db.execute_query(check_query, [beneficiary_id])

        if not existing:
            return jsonify({
                'success': False,
                'message': 'المستفيد غير موجود'
            })

        beneficiary_name = existing[0][0]

        # حذف المستفيد مباشرة (لا توجد جداول تحويلات حالياً)
        delete_query = "DELETE FROM beneficiaries WHERE id = :1"
        result = db.execute_update(delete_query, [beneficiary_id])

        if result > 0:
            # تسجيل العملية
            username = current_user.username if current_user.is_authenticated else 'غير معروف'
            logger.info(f"تم حذف المستفيد {beneficiary_id} - {beneficiary_name} بواسطة {username}")

            return jsonify({
                'success': True,
                'message': f'تم حذف المستفيد "{beneficiary_name}" بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في حذف المستفيد'
            })

    except Exception as e:
        logger.error(f"خطأ في حذف المستفيد {beneficiary_id}: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في حذف المستفيد'
        })

@transfers_bp.route('/beneficiaries/search')
@login_required
def search_beneficiaries():
    """البحث في المستفيدين"""
    try:
        db = DatabaseManager()
        
        query = request.args.get('q', '').strip()
        
        if not query:
            return jsonify({'success': True, 'data': []})
        
        search_query = """
        SELECT id, beneficiary_name, bank_account, bank_name
        FROM beneficiaries 
        WHERE is_active = 1 
        AND (LOWER(beneficiary_name) LIKE LOWER(:1) 
             OR bank_account LIKE :1
             OR LOWER(bank_name) LIKE LOWER(:1))
        ORDER BY beneficiary_name
        FETCH FIRST 10 ROWS ONLY
        """
        
        search_term = f'%{query}%'
        result = db.execute_query(search_query, [search_term])
        
        beneficiaries = []
        if result:
            for row in result:
                beneficiaries.append({
                    'id': row[0],
                    'name': row[1],
                    'account': row[2],
                    'bank': row[3]
                })
        
        return jsonify({
            'success': True,
            'data': beneficiaries
        })
        
    except Exception as e:
        logger.error(f"خطأ في البحث في المستفيدين: {e}")
        return jsonify({
            'success': False, 
            'message': 'حدث خطأ في البحث'
        }), 500
