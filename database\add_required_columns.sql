-- =====================================================
-- إضافة الحقول المطلوبة للجداول الموجودة
-- Add Required Columns to Existing Tables
-- =====================================================

-- إضافة حقول لجدول PURCHASE_ORDERS
ALTER TABLE PURCHASE_ORDERS ADD (
    supplier_id NUMBER,
    payment_status VARCHAR2(30) DEFAULT 'PENDING',
    payment_due_date DATE,
    payment_terms_days NUMBER DEFAULT 30,
    subtotal_amount NUMBER(15,2) DEFAULT 0,
    tax_amount NUMBER(15,2) DEFAULT 0,
    discount_amount NUMBER(15,2) DEFAULT 0,
    shipping_amount NUMBER(15,2) DEFAULT 0,
    other_charges NUMBER(15,2) DEFAULT 0,
    total_amount_due NUMBER(15,2) DEFAULT 0,
    paid_amount NUMBER(15,2) DEFAULT 0,
    outstanding_amount NUMBER(15,2) DEFAULT 0,
    advance_payment_amount NUMBER(15,2) DEFAULT 0,
    advance_payment_date DATE,
    final_payment_amount NUMBER(15,2) DEFAULT 0,
    final_payment_date DATE,
    exchange_rate NUMBER(15,6) DEFAULT 1,
    base_currency_amount NUMBER(15,2),
    delivery_status VARCHAR2(30) DEFAULT 'PENDING',
    goods_received_date DATE,
    goods_received_by NUMBER,
    is_recurring CHAR(1) DEFAULT 'N',
    parent_po_id NUMBER,
    approval_workflow_id NUMBER,
    approved_date DATE,
    approved_by NUMBER,
    cancelled_date DATE,
    cancelled_by NUMBER,
    cancellation_reason VARCHAR2(500)
);

-- إضافة حقول لجدول SUPPLIER_TRANSACTIONS
ALTER TABLE SUPPLIER_TRANSACTIONS ADD (
    purchase_order_id NUMBER,
    purchase_order_number VARCHAR2(50),
    po_item_id NUMBER,
    delivery_date DATE,
    goods_received_date DATE,
    invoice_received_date DATE,
    payment_request_id NUMBER,
    payment_status VARCHAR2(20) DEFAULT 'PENDING'
);

-- إنشاء فهارس للأداء
CREATE INDEX idx_po_supplier_id ON PURCHASE_ORDERS(supplier_id);
CREATE INDEX idx_po_payment_status ON PURCHASE_ORDERS(payment_status);
CREATE INDEX idx_po_delivery_status ON PURCHASE_ORDERS(delivery_status);
CREATE INDEX idx_po_payment_due_date ON PURCHASE_ORDERS(payment_due_date);

CREATE INDEX idx_pop_purchase_order ON PURCHASE_ORDER_PAYMENTS(purchase_order_id);
CREATE INDEX idx_pop_payment_status ON PURCHASE_ORDER_PAYMENTS(payment_status);
CREATE INDEX idx_pop_payment_type ON PURCHASE_ORDER_PAYMENTS(payment_type);
CREATE INDEX idx_pop_due_date ON PURCHASE_ORDER_PAYMENTS(payment_due_date);

CREATE INDEX idx_posl_purchase_order ON PURCHASE_ORDER_STATUS_LOG(purchase_order_id);
CREATE INDEX idx_posl_change_date ON PURCHASE_ORDER_STATUS_LOG(change_date);
CREATE INDEX idx_posl_status_type ON PURCHASE_ORDER_STATUS_LOG(status_type);

CREATE INDEX idx_gr_purchase_order ON GOODS_RECEIPTS(purchase_order_id);
CREATE INDEX idx_gr_supplier ON GOODS_RECEIPTS(supplier_id);
CREATE INDEX idx_gr_receipt_date ON GOODS_RECEIPTS(receipt_date);
CREATE INDEX idx_gr_status ON GOODS_RECEIPTS(receipt_status);

CREATE INDEX idx_rc_status ON RECONCILIATION_CYCLES(status);
CREATE INDEX idx_rc_date ON RECONCILIATION_CYCLES(reconciliation_date);
CREATE INDEX idx_rc_period ON RECONCILIATION_CYCLES(period_from, period_to);

CREATE INDEX idx_sr_cycle ON SUPPLIER_RECONCILIATION(cycle_id);
CREATE INDEX idx_sr_supplier ON SUPPLIER_RECONCILIATION(supplier_id);
CREATE INDEX idx_sr_status ON SUPPLIER_RECONCILIATION(reconciliation_status);
CREATE INDEX idx_sr_currency ON SUPPLIER_RECONCILIATION(currency_code);
CREATE INDEX idx_sr_difference ON SUPPLIER_RECONCILIATION(total_difference);

CREATE INDEX idx_rd_reconciliation ON RECONCILIATION_DIFFERENCES(reconciliation_id);
CREATE INDEX idx_rd_type ON RECONCILIATION_DIFFERENCES(difference_type);
CREATE INDEX idx_rd_resolved ON RECONCILIATION_DIFFERENCES(resolved);
CREATE INDEX idx_rd_amount ON RECONCILIATION_DIFFERENCES(difference_amount);

CREATE INDEX idx_ra_reconciliation ON RECONCILIATION_ADJUSTMENTS(reconciliation_id);
CREATE INDEX idx_ra_status ON RECONCILIATION_ADJUSTMENTS(adjustment_status);
CREATE INDEX idx_ra_type ON RECONCILIATION_ADJUSTMENTS(adjustment_type);

CREATE INDEX idx_ral_cycle ON RECONCILIATION_ACTIVITY_LOG(cycle_id);
CREATE INDEX idx_ral_date ON RECONCILIATION_ACTIVITY_LOG(performed_date);
CREATE INDEX idx_ral_type ON RECONCILIATION_ACTIVITY_LOG(activity_type);

CREATE INDEX idx_spe_supplier ON SUPPLIER_PERFORMANCE_EVALUATIONS(supplier_id);
CREATE INDEX idx_spe_period ON SUPPLIER_PERFORMANCE_EVALUATIONS(evaluation_period_from, evaluation_period_to);
CREATE INDEX idx_spe_rating ON SUPPLIER_PERFORMANCE_EVALUATIONS(overall_rating);
CREATE INDEX idx_spe_category ON SUPPLIER_PERFORMANCE_EVALUATIONS(performance_category);

CREATE INDEX idx_in_type ON INTEGRATION_NOTIFICATIONS(notification_type);
CREATE INDEX idx_in_entity ON INTEGRATION_NOTIFICATIONS(entity_type, entity_id);
CREATE INDEX idx_in_recipient ON INTEGRATION_NOTIFICATIONS(recipient_user_id);
CREATE INDEX idx_in_status ON INTEGRATION_NOTIFICATIONS(notification_status);
CREATE INDEX idx_in_priority ON INTEGRATION_NOTIFICATIONS(priority);

-- رسالة نجاح
SELECT 'تم إضافة جميع الحقول والفهارس بنجاح!' as status FROM dual;

COMMIT;
