# -*- coding: utf-8 -*-
"""
مولد PDF باستخدام المتصفح (يحاكي زر تحميل PDF الموجود)
Browser-based PDF Generator using existing HTML viewer
"""

import os
import sys
import requests
import time
import tempfile
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# إضافة مسار قاعدة البيانات
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class BrowserPDFGenerator:
    """مولد PDF باستخدام المتصفح"""
    
    def __init__(self):
        """تهيئة مولد PDF"""
        self.output_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'pdf')
        os.makedirs(self.output_dir, exist_ok=True)
        self.base_url = 'http://127.0.0.1:5000'
    
    def generate_delivery_order_pdf_with_browser(self, delivery_order_id):
        """إنشاء PDF باستخدام المتصفح (يحاكي زر تحميل PDF)"""
        try:
            # إنشاء اسم الملف
            filename = f"delivery_order_browser_{delivery_order_id}.pdf"
            filepath = os.path.join(self.output_dir, filename)
            
            # URL صفحة أمر التسليم
            html_url = f"{self.base_url}/shipments/delivery-order-viewer/{delivery_order_id}"
            
            # استخدام Selenium لمحاكاة المتصفح
            if self._generate_with_selenium(html_url, filepath):
                return filepath, "تم إنشاء PDF باستخدام المتصفح بنجاح"
            
            # استخدام Chrome headless مباشرة
            if self._generate_with_chrome_direct(html_url, filepath):
                return filepath, "تم إنشاء PDF باستخدام Chrome بنجاح"
            
            return None, "فشل في إنشاء PDF - المتصفح غير متاح"
            
        except Exception as e:
            return None, f"خطأ في إنشاء PDF باستخدام المتصفح: {str(e)}"
    
    def _generate_with_selenium(self, html_url, output_path):
        """إنشاء PDF باستخدام Selenium"""
        try:
            # إعداد Chrome options
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            # إعداد تحميل PDF
            prefs = {
                "printing.print_preview_sticky_settings.appState": {
                    "recentDestinations": [{
                        "id": "Save as PDF",
                        "origin": "local",
                        "account": ""
                    }],
                    "selectedDestinationId": "Save as PDF",
                    "version": 2
                },
                "savefile.default_directory": os.path.dirname(output_path)
            }
            chrome_options.add_experimental_option('prefs', prefs)
            chrome_options.add_argument('--kiosk-printing')
            
            # إنشاء driver
            driver = webdriver.Chrome(options=chrome_options)
            
            try:
                # فتح الصفحة
                print(f"🌐 فتح الصفحة: {html_url}")
                driver.get(html_url)
                
                # انتظار تحميل الصفحة
                wait = WebDriverWait(driver, 10)
                wait.until(EC.presence_of_element_located((By.ID, "delivery-order-content")))
                
                # انتظار إضافي لتحميل الخطوط والأنماط
                time.sleep(3)
                
                # البحث عن زر تحميل PDF وتنفيذه
                try:
                    # تنفيذ دالة downloadPDF() مباشرة
                    driver.execute_script("""
                        // إخفاء أزرار التحكم
                        var controls = document.querySelector('.controls');
                        if (controls) controls.style.display = 'none';
                        
                        // تنفيذ تحويل HTML إلى PDF
                        html2canvas(document.getElementById('delivery-order-content'), {
                            scale: 1.5,
                            useCORS: true,
                            allowTaint: true,
                            backgroundColor: '#ffffff'
                        }).then(function(canvas) {
                            const { jsPDF } = window.jspdf;
                            const pdf = new jsPDF('p', 'mm', 'a4');
                            
                            const imgData = canvas.toDataURL('image/png');
                            const imgWidth = 210;
                            const imgHeight = (canvas.height * imgWidth) / canvas.width;
                            
                            pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
                            
                            // حفظ PDF
                            pdf.save('delivery_order.pdf');
                            
                            // إشارة انتهاء العملية
                            window.pdfGenerated = true;
                        });
                    """)
                    
                    # انتظار انتهاء عملية إنشاء PDF
                    wait.until(lambda driver: driver.execute_script("return window.pdfGenerated === true"))
                    
                    print("✅ تم إنشاء PDF باستخدام Selenium")
                    return True
                    
                except Exception as e:
                    print(f"❌ فشل في تنفيذ JavaScript: {e}")
                    
                    # محاولة بديلة: استخدام print
                    driver.execute_script("window.print();")
                    time.sleep(2)
                    
                    return False
                
            finally:
                driver.quit()
                
        except Exception as e:
            print(f"❌ Selenium غير متاح: {e}")
            return False
    
    def _generate_with_chrome_direct(self, html_url, output_path):
        """إنشاء PDF باستخدام Chrome مباشرة"""
        try:
            import subprocess
            
            # البحث عن Chrome
            chrome_paths = [
                r'C:\Program Files\Google\Chrome\Application\chrome.exe',
                r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe',
                'chrome',
                'google-chrome'
            ]
            
            chrome_path = None
            for path in chrome_paths:
                try:
                    result = subprocess.run([path, '--version'], capture_output=True, timeout=5)
                    if result.returncode == 0:
                        chrome_path = path
                        break
                except:
                    continue
            
            if not chrome_path:
                return False
            
            cmd = [
                chrome_path,
                '--headless',
                '--disable-gpu',
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--print-to-pdf=' + output_path,
                '--print-to-pdf-no-header',
                '--run-all-compositor-stages-before-draw',
                '--virtual-time-budget=5000',  # انتظار أطول للخطوط
                html_url
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                print("✅ تم إنشاء PDF باستخدام Chrome مباشرة")
                return True
            else:
                print(f"❌ فشل Chrome: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Chrome غير متاح: {e}")
            return False
    
    def check_browser_availability(self):
        """فحص توفر المتصفحات"""
        browsers = {
            'selenium': False,
            'chrome': False
        }
        
        # فحص Selenium
        try:
            from selenium import webdriver
            browsers['selenium'] = True
        except ImportError:
            pass
        
        # فحص Chrome
        import subprocess
        chrome_paths = [
            r'C:\Program Files\Google\Chrome\Application\chrome.exe',
            r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe',
            'chrome',
            'google-chrome'
        ]
        
        for path in chrome_paths:
            try:
                result = subprocess.run([path, '--version'], capture_output=True, timeout=5)
                if result.returncode == 0:
                    browsers['chrome'] = True
                    break
            except:
                continue
        
        return browsers


# إنشاء instance عام للمولد
browser_pdf_generator = BrowserPDFGenerator()


def generate_delivery_order_pdf_with_browser(delivery_order_id):
    """دالة مساعدة لإنشاء PDF باستخدام المتصفح"""
    return browser_pdf_generator.generate_delivery_order_pdf_with_browser(delivery_order_id)


def check_browser_tools():
    """فحص أدوات المتصفح المتاحة"""
    return browser_pdf_generator.check_browser_availability()
