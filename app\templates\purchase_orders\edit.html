{% extends "base.html" %}

{% block title %}تعديل أمر الشراء{% endblock %}

{% block extra_css %}
<!-- 🛡️ تنسيقات حماية أوامر الشراء -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/po_protection.css') }}">
{% endblock %}

{% block content %}
<!-- 🛡️ تنبيه الحماية (مخفي افتراضياً) -->
<div id="protectionAlert" class="alert alert-warning protection-alert" style="display: none;">
    <div class="icon">🛡️</div>
    <div class="message">
        <strong>تحذير:</strong> هذا أمر شراء محمي!
        <br>
        <span id="protectionReason"></span>
    </div>
    <button type="button" class="close-btn" onclick="hideProtectionAlert()">×</button>
</div>
<div class="container-fluid px-3">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل أمر الشراء رقم: {{ purchase_order[1] }}
                    </h3>
                    <div>
                        <a href="{{ url_for('purchase_orders.view', po_id=purchase_order[0]) }}" class="btn btn-outline-info me-2">
                            <i class="fas fa-eye me-2"></i>
                            عرض
                        </a>
                        <a href="{{ url_for('purchase_orders.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- رسالة توضيحية -->
                    <div class="alert alert-warning mb-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                            <div>
                                <h6 class="alert-heading mb-1">تعديل أمر الشراء:</h6>
                                <p class="mb-0">
                                    يمكنك تعديل بيانات أمر الشراء والأصناف. تأكد من مراجعة جميع البيانات قبل الحفظ.
                                </p>
                            </div>
                        </div>
                    </div>

                    <form id="purchaseOrderForm">
                        <input type="hidden" id="poId" value="{{ purchase_order[0] }}">
                        
                        <!-- البيانات الأساسية -->
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    البيانات الأساسية
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- الصف الأول: الفرع + العنوان -->
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="branchId" class="form-label">الفرع *</label>
                                            <select class="form-select" id="branchId" name="branch_id" required>
                                                <option value="">اختر الفرع</option>
                                                {% if branches %}
                                                    {% for branch in branches %}
                                                        <option value="{{ branch.brn_no }}"
                                                                {% if branch.brn_no == (purchase_order[28] if purchase_order|length > 28 else 21) %}selected{% endif %}>
                                                            {{ branch.brn_lname }}
                                                        </option>
                                                    {% endfor %}
                                                {% else %}
                                                    <option value="21" selected>الفرع الرئيسي</option>
                                                {% endif %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">عنوان أمر الشراء *</label>
                                            <input type="text" class="form-control" id="title" name="title"
                                                   value="{{ purchase_order[5] or '' }}" required
                                                   placeholder="أدخل عنوان أمر الشراء...">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="poNumber" class="form-label">رقم أمر الشراء</label>
                                            <input type="text" class="form-control" id="poNumber" name="po_number" 
                                                   value="{{ purchase_order[1] }}" readonly>
                                            <small class="text-muted">يتم توليده تلقائياً</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="poDate" class="form-label">تاريخ الأمر *</label>
                                            <input type="date" class="form-control" id="poDate" name="po_date" 
                                                   value="{{ purchase_order[6].strftime('%Y-%m-%d') if purchase_order[6] else '' }}" required>
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف الثاني: العقد + المورد -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="contractId" class="form-label">العقد (اختياري)</label>
                                            <select class="form-select" id="contractId" name="contract_id" onchange="loadContractDetails()">
                                                <option value="">-- اختر العقد --</option>
                                                {% for contract in contracts %}
                                                <option value="{{ contract[0] }}" 
                                                        {% if purchase_order[2] == contract[0] %}selected{% endif %}>
                                                    {{ contract[1] }} - {{ contract[2] }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                            <small class="text-muted">اختر العقد لملء بيانات المورد تلقائياً</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="supplierName" class="form-label">اسم المورد *</label>
                                            <input type="text" class="form-control" id="supplierName" name="supplier_name" 
                                                   value="{{ purchase_order[4] or '' }}" required
                                                   placeholder="أدخل اسم المورد...">
                                            <input type="hidden" id="supplierCode" name="supplier_code"
                                                   value="{{ purchase_order[3] or '' }}">
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف الثالث: تاريخ التسليم + عنوان التسليم -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="deliveryDate" class="form-label">تاريخ التسليم المطلوب</label>
                                            <input type="date" class="form-control" id="deliveryDate" name="delivery_date"
                                                   value="{{ purchase_order[7].strftime('%Y-%m-%d') if purchase_order[7] else '' }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="deliveryAddress" class="form-label">عنوان التسليم</label>
                                            <input type="text" class="form-control" id="deliveryAddress" name="delivery_address"
                                                   value="{{ purchase_order[8] or '' }}"
                                                   placeholder="أدخل عنوان التسليم...">
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف الرابع: شروط الدفع + رقم فاتورة المورد -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="paymentTerms" class="form-label">شروط الدفع</label>
                                            <input type="text" class="form-control" id="paymentTerms" name="payment_terms"
                                                   value="{{ purchase_order[9] or '' }}"
                                                   placeholder="مثال: 30 يوم من تاريخ الاستلام">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="supplierInvoiceNumber" class="form-label">
                                                <i class="fas fa-file-invoice me-1"></i>
                                                رقم فاتورة المورد
                                            </label>
                                            <input type="text" class="form-control" id="supplierInvoiceNumber"
                                                   name="supplier_invoice_number"
                                                   value="{{ purchase_order[10] or '' }}"
                                                   placeholder="أدخل رقم فاتورة المورد">
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                <small>رقم الفاتورة الصادرة من المورد (اختياري)</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف الخامس: الحالة + الأولوية -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">الحالة *</label>
                                            <select class="form-select" id="status" name="status" required>
                                                <option value="مسودة" {% if purchase_order[15] == 'مسودة' %}selected{% endif %}>مسودة</option>
                                                <option value="معتمد" {% if purchase_order[15] == 'معتمد' %}selected{% endif %}>معتمد</option>
                                                <option value="مرسل" {% if purchase_order[15] == 'مرسل' %}selected{% endif %}>مرسل</option>
                                                <option value="مستلم جزئياً" {% if purchase_order[15] == 'مستلم جزئياً' %}selected{% endif %}>مستلم جزئياً</option>
                                                <option value="مستلم بالكامل" {% if purchase_order[15] == 'مستلم بالكامل' %}selected{% endif %}>مستلم بالكامل</option>
                                                <option value="ملغي" {% if purchase_order[15] == 'ملغي' %}selected{% endif %}>ملغي</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="priority" class="form-label">
                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                الأولوية
                                            </label>
                                            <select class="form-select" id="priority" name="priority">
                                                <option value="عادي" {% if purchase_order[11] == 'عادي' %}selected{% endif %}>عادي</option>
                                                <option value="مهم" {% if purchase_order[11] == 'مهم' %}selected{% endif %}>مهم</option>
                                                <option value="عاجل" {% if purchase_order[11] == 'عاجل' %}selected{% endif %}>عاجل</option>
                                                <option value="عاجل جداً" {% if purchase_order[11] == 'عاجل جداً' %}selected{% endif %}>عاجل جداً</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف الخامس: أجور الشحن + أجور التخليص -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="shippingCost" class="form-label">
                                                <i class="fas fa-shipping-fast me-1"></i>
                                                أجور الشحن
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="shippingCost"
                                                       name="shipping_cost" step="0.01" min="0"
                                                       value="{{ purchase_order[11] or '0.00' }}"
                                                       placeholder="0.00">
                                                <span class="input-group-text currency-symbol">{{ purchase_order[13] or 'ريال' }}</span>
                                            </div>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                <small>تكلفة شحن البضائع من المورد</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="clearanceCost" class="form-label">
                                                <i class="fas fa-file-import me-1"></i>
                                                أجور التخليص
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="clearanceCost"
                                                       name="clearance_cost" step="0.01" min="0"
                                                       value="{{ purchase_order[12] or '0.00' }}"
                                                       placeholder="0.00">
                                                <span class="input-group-text currency-symbol">{{ purchase_order[13] or 'ريال' }}</span>
                                            </div>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                <small>رسوم التخليص الجمركي والإجراءات</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف السادس: حالة الاستخدام -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="isUsed" name="is_used"
                                                       {% if purchase_order|length > 24 and purchase_order[24] == 1 %}checked{% endif %}
                                                       disabled>
                                                <label class="form-check-label" for="isUsed">
                                                    <i class="fas fa-shipping-fast me-2"></i>
                                                    <strong>مُستخدم في شحنة</strong>
                                                </label>
                                            </div>
                                            <small class="text-muted">
                                                {% if purchase_order|length > 24 and purchase_order[24] == 1 %}
                                                    <i class="fas fa-check-circle text-success me-1"></i>
                                                    تم استخدام هذا الأمر في شحنة
                                                    {% if purchase_order|length > 25 and purchase_order[25] %}
                                                        بتاريخ {{ purchase_order[25].strftime('%Y-%m-%d') }}
                                                    {% endif %}
                                                {% else %}
                                                    <i class="fas fa-clock text-warning me-1"></i>
                                                    لم يتم استخدام هذا الأمر في أي شحنة بعد
                                                {% endif %}
                                            </small>
                                        </div>
                                    </div>
                                    {% if purchase_order[27] == 1 and purchase_order[29] %}
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الشحنة المستخدم فيها</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-ship"></i>
                                                </span>
                                                <input type="text" class="form-control"
                                                       value="شحنة رقم {{ purchase_order[29] }}" readonly>
                                                <a href="/shipments/view/{{ purchase_order[29] }}"
                                                   class="btn btn-outline-primary" target="_blank" title="عرض الشحنة">
                                                    <i class="fas fa-external-link-alt"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- الصف الخامس: العملة + الأولوية + مدة التسليم -->
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="currency" class="form-label">
                                                <i class="fas fa-coins me-1"></i>العملة
                                            </label>
                                            <select class="form-select" id="currency" name="currency" onchange="updateCurrencyDisplay()">
                                                {% if currencies %}
                                                    {% for currency in currencies %}
                                                        <option value="{{ currency[1] }}"
                                                                data-symbol="{{ currency[4] }}"
                                                                data-name="{{ currency[2] }}"
                                                                {% if purchase_order[13] == currency[1] %}selected{% endif %}>
                                                            {{ currency[4] }} - {{ currency[2] }}
                                                            {% if currency[5] == 1 %} (العملة الأساسية){% endif %}
                                                        </option>
                                                    {% endfor %}
                                                {% else %}
                                                    <option value="SAR" data-symbol="﷼" data-name="ريال سعودي" {% if purchase_order[10] == 'SAR' %}selected{% endif %}>﷼ - ريال سعودي</option>
                                                    <option value="USD" data-symbol="$" data-name="دولار أمريكي" {% if purchase_order[10] == 'USD' %}selected{% endif %}>$ - دولار أمريكي</option>
                                                    <option value="EUR" data-symbol="€" data-name="يورو" {% if purchase_order[10] == 'EUR' %}selected{% endif %}>€ - يورو</option>
                                                {% endif %}
                                            </select>
                                            <small class="text-muted">
                                                <i class="fas fa-info-circle me-1"></i>
                                                العملة المستخدمة في هذا الأمر
                                            </small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="priority" class="form-label">الأولوية</label>
                                            <select class="form-select" id="priority" name="priority">
                                                <option value="عادي" {% if purchase_order[11] == 'عادي' %}selected{% endif %}>عادي</option>
                                                <option value="عاجل" {% if purchase_order[11] == 'عاجل' %}selected{% endif %}>عاجل</option>
                                                <option value="عاجل جداً" {% if purchase_order[11] == 'عاجل جداً' %}selected{% endif %}>عاجل جداً</option>
                                                <option value="منخفض" {% if purchase_order[11] == 'منخفض' %}selected{% endif %}>منخفض</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="expectedDeliveryDays" class="form-label">مدة التسليم (أيام)</label>
                                            <input type="number" class="form-control" id="expectedDeliveryDays"
                                                   name="expected_delivery_days" min="1" 
                                                   value="{{ purchase_order[12] or '' }}" placeholder="أيام">
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف السادس: الوصف والملاحظات -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="description" class="form-label">وصف الأمر</label>
                                            <input type="text" class="form-control" id="description" name="description"
                                                   value="{{ purchase_order[16] or '' }}"
                                                   placeholder="أدخل وصف أمر الشراء...">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="notes" class="form-label">ملاحظات إضافية</label>
                                            <input type="text" class="form-control" id="notes" name="notes"
                                                   value="{{ purchase_order[17] or '' }}"
                                                   placeholder="أي ملاحظات أو تعليمات خاصة...">
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف السابع: المبالغ المالية -->
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="grossAmount" class="form-label">
                                                <i class="fas fa-calculator me-1"></i>إجمالي الأصناف
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="grossAmount" name="gross_amount"
                                                       step="0.01" min="0" value="{{ purchase_order[19] or 0 }}" readonly>
                                                <span class="input-group-text currency-symbol">{{ purchase_order[13] or 'ريال' }}</span>
                                            </div>
                                            <small class="text-muted">يتم حسابه تلقائياً من الأصناف</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="discountAmount" class="form-label">
                                                <i class="fas fa-percentage me-1"></i>مبلغ الخصم
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="discountAmount" name="discount_amount"
                                                       step="0.01" min="0" value="{{ purchase_order[20] or 0 }}" onchange="calculateFinalAmounts()">
                                                <span class="input-group-text currency-symbol">{{ purchase_order[13] or 'ريال' }}</span>
                                            </div>
                                            <small class="text-muted">خصم إضافي على إجمالي الأمر</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="netAmount" class="form-label">
                                                <i class="fas fa-money-bill-wave me-1"></i>المبلغ الإجمالي النهائي
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="netAmount" name="net_amount"
                                                       step="0.01" min="0" value="{{ purchase_order[24] or 0 }}" readonly>
                                                <span class="input-group-text currency-symbol">{{ purchase_order[13] or 'ريال' }}</span>
                                            </div>
                                            <small class="text-muted">المبلغ النهائي بعد الخصم</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أصناف أمر الشراء -->
                        <div class="card mt-4">
                            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-boxes me-2"></i>
                                    أصناف أمر الشراء
                                </h5>
                                <button type="button" class="btn btn-light btn-sm" onclick="addNewItem()">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة صنف جديد
                                </button>

                            </div>
                            <div class="card-body">
                                <!-- جدول الأصناف -->
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="itemsTable">
                                        <thead class="table-dark">
                                            <tr>
                                                <th style="width: 10%">كود الصنف</th>
                                                <th style="width: 20%">اسم الصنف *</th>
                                                <th style="width: 10%">الوحدة</th>
                                                <th style="width: 8%">الكمية *</th>
                                                <th style="width: 10%">السعر *</th>
                                                <th style="width: 12%">تاريخ الإنتاج</th>
                                                <th style="width: 12%">تاريخ الانتهاء</th>
                                                <th style="width: 8%">خصم (%)</th>
                                                <th style="width: 10%">الإجمالي</th>
                                                <th style="width: 5%">العمليات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="itemsTableBody">
                                            {% for item in items %}
                                            <tr>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm item-code-input"
                                                           name="items[{{ loop.index0 }}][code]"
                                                           value="{{ item[1] or '' }}"
                                                           placeholder="كود الصنف أو F9 للبحث"
                                                           title="أدخل كود الصنف أو اضغط F9 للبحث">
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm"
                                                           name="items[{{ loop.index0 }}][name]"
                                                           value="{{ item[2] or '' }}" placeholder="اسم الصنف" required>
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control form-control-sm"
                                                           name="items[{{ loop.index0 }}][unit]"
                                                           value="{{ item[3] or '' }}" placeholder="الوحدة">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control form-control-sm"
                                                           name="items[{{ loop.index0 }}][quantity]"
                                                           value="{{ item[6] or '' }}" min="1" step="1"
                                                           placeholder="الكمية" onchange="calculateRowTotal(this)" required>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control form-control-sm"
                                                           name="items[{{ loop.index0 }}][price]"
                                                           value="{{ item[7] or '' }}" min="0" step="0.01"
                                                           placeholder="السعر" onchange="calculateRowTotal(this)" required>
                                                </td>
                                                <td>
                                                    <input type="date" class="form-control form-control-sm"
                                                           name="items[{{ loop.index0 }}][production_date]"
                                                           value="{{ item[4].strftime('%Y-%m-%d') if item[4] else '' }}">
                                                </td>
                                                <td>
                                                    <input type="date" class="form-control form-control-sm"
                                                           name="items[{{ loop.index0 }}][expiry_date]"
                                                           value="{{ item[5].strftime('%Y-%m-%d') if item[5] else '' }}">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control form-control-sm"
                                                           name="items[{{ loop.index0 }}][discount]"
                                                           value="0" min="0" max="100" step="0.01"
                                                           placeholder="خصم" onchange="calculateRowTotal(this)">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control form-control-sm"
                                                           name="items[{{ loop.index0 }}][total]"
                                                           value="{{ item[8] or '' }}" readonly>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-danger btn-sm"
                                                            onclick="removeItem(this)" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>

                                <!-- إجماليات الأصناف -->
                                <div class="row mt-3">
                                    <div class="col-md-8"></div>
                                    <div class="col-md-4">
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>المجموع الفرعي:</strong></td>
                                                <td class="text-end"><span id="subtotalAmount">{{ purchase_order[19] or 0 }}</span> <span class="currency-symbol">{{ purchase_order[13] or 'ريال' }}</span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>إجمالي الخصم:</strong></td>
                                                <td class="text-end"><span id="totalDiscount">{{ purchase_order[20] or 0 }}</span> <span class="currency-symbol">{{ purchase_order[13] or 'ريال' }}</span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>الضريبة:</strong></td>
                                                <td class="text-end"><span id="totalTax">0.00</span> <span class="currency-symbol">{{ purchase_order[13] or 'ريال' }}</span></td>
                                            </tr>
                                            <tr class="table-primary">
                                                <td><strong>الإجمالي النهائي:</strong></td>
                                                <td class="text-end"><strong><span id="grandTotal">{{ purchase_order[21] or 0 }}</span> <span class="currency-symbol">{{ purchase_order[13] or 'ريال' }}</span></strong></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>

                                <!-- ملاحظة طرق إدخال الأصناف -->
                                <div class="mt-3">
                                    <div class="alert alert-info alert-dismissible">
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                                        <div class="d-flex align-items-start">
                                            <i class="fas fa-lightbulb me-2 mt-1 text-warning"></i>
                                            <div>
                                                <strong class="text-primary">💡 نصائح لإدخال الأصناف:</strong>
                                                <div class="mt-2">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <small class="text-muted">
                                                                <i class="fas fa-keyboard me-1 text-success"></i>
                                                                <strong>الإدخال المباشر:</strong><br>
                                                                أدخل كود الصنف كاملاً وسيتم البحث تلقائياً
                                                            </small>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <small class="text-muted">
                                                                <i class="fas fa-search me-1 text-primary"></i>
                                                                <strong>البحث المتقدم:</strong><br>
                                                                اضغط <kbd>F9</kbd> في حقل كود الصنف لفتح نافذة البحث
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="card mt-4">
                            <div class="card-body text-center">
                                <button type="button" class="btn btn-success btn-lg me-3" onclick="updatePurchaseOrder()">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ التعديلات
                                </button>
                                <button type="button" class="btn btn-secondary btn-lg me-3" onclick="resetForm()">
                                    <i class="fas fa-undo me-2"></i>
                                    إعادة تعيين
                                </button>
                                <a href="{{ url_for('purchase_orders.view', po_id=purchase_order[0]) }}" class="btn btn-info btn-lg">
                                    <i class="fas fa-eye me-2"></i>
                                    عرض أمر الشراء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// نسخ الدوال من صفحة الإنشاء مع تعديلات للتحديث
function calculateRowTotal(element) {
    const row = element.closest('tr');
    const quantity = parseFloat(row.querySelector('input[name*="[quantity]"]').value) || 0;
    const price = parseFloat(row.querySelector('input[name*="[price]"]').value) || 0;
    const discountRate = parseFloat(row.querySelector('input[name*="[discount]"]').value) || 0;

    // حساب المجموع الفرعي
    const subtotal = quantity * price;

    // حساب الخصم
    const discountAmount = subtotal * (discountRate / 100);

    // الإجمالي النهائي (بعد الخصم فقط)
    const total = subtotal - discountAmount;

    // تحديث حقل الإجمالي
    row.querySelector('input[name*="[total]"]').value = total.toFixed(2);

    // إعادة حساب الإجماليات العامة
    calculateTotal();
}

function calculateTotal() {
    const rows = document.querySelectorAll('#itemsTableBody tr');
    let subtotal = 0;
    let totalDiscount = 0;
    let grandTotal = 0;

    rows.forEach(row => {
        const quantity = parseFloat(row.querySelector('input[name*="[quantity]"]').value) || 0;
        const price = parseFloat(row.querySelector('input[name*="[price]"]').value) || 0;
        const discountRate = parseFloat(row.querySelector('input[name*="[discount]"]').value) || 0;

        const rowSubtotal = quantity * price;
        const rowDiscount = rowSubtotal * (discountRate / 100);
        const rowTotal = rowSubtotal - rowDiscount;

        subtotal += rowSubtotal;
        totalDiscount += rowDiscount;
        grandTotal += rowTotal;
    });

    // الحصول على رمز العملة المحدد
    const currencySelect = document.getElementById('currency');
    const selectedOption = currencySelect.options[currencySelect.selectedIndex];
    const currencySymbol = selectedOption ? selectedOption.getAttribute('data-symbol') || 'ريال' : 'ريال';

    // تحديث العرض في جدول الإجماليات
    document.getElementById('subtotalAmount').textContent = subtotal.toFixed(2);
    document.getElementById('totalDiscount').textContent = totalDiscount.toFixed(2);
    document.getElementById('totalTax').textContent = '0.00'; // لا توجد ضريبة
    document.getElementById('grandTotal').textContent = grandTotal.toFixed(2);

    // تحديث رموز العملة
    const currencySymbols = document.querySelectorAll('.currency-symbol');
    currencySymbols.forEach(symbol => {
        symbol.textContent = currencySymbol;
    });

    // تحديث الحقول في البيانات الأساسية
    const grossAmount = grandTotal; // المبلغ الإجمالي = المجموع بعد خصم الأصناف
    document.getElementById('grossAmount').value = grossAmount.toFixed(2);

    // حساب صافي المبلغ (سيتم تحديثه في دالة calculateFinalAmounts)
    calculateFinalAmounts();
}

// حساب المبالغ النهائية (مع خصم إضافي وأجور الشحن والتخليص)
function calculateFinalAmounts() {
    const grossAmount = parseFloat(document.getElementById('grossAmount').value) || 0;
    const discountAmount = parseFloat(document.getElementById('discountAmount').value) || 0;
    const shippingCost = parseFloat(document.getElementById('shippingCost').value) || 0;
    const clearanceCost = parseFloat(document.getElementById('clearanceCost').value) || 0;

    // حساب صافي المبلغ بعد الخصم
    const netAmountAfterDiscount = grossAmount - discountAmount;

    // إضافة أجور الشحن والتخليص للمبلغ النهائي
    const finalAmount = netAmountAfterDiscount + shippingCost + clearanceCost;

    // تحديث المبلغ الإجمالي النهائي
    document.getElementById('netAmount').value = finalAmount.toFixed(2);

    // إضافة تأثير بصري للحقل
    const netAmountField = document.getElementById('netAmount');
    netAmountField.style.backgroundColor = '#d4edda';
    netAmountField.style.borderColor = '#28a745';
    netAmountField.style.fontWeight = 'bold';
}

function addNewItem() {
    const tbody = document.getElementById('itemsTableBody');
    const rowCount = tbody.children.length;

    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td>
            <input type="text" class="form-control form-control-sm item-code-input"
                   name="items[${rowCount}][code]"
                   placeholder="كود الصنف أو F9 للبحث"
                   title="أدخل كود الصنف أو اضغط F9 للبحث">
        </td>
        <td>
            <input type="text" class="form-control form-control-sm"
                   name="items[${rowCount}][name]" placeholder="اسم الصنف" required>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm"
                   name="items[${rowCount}][unit]" placeholder="الوحدة">
        </td>
        <td>
            <input type="number" class="form-control form-control-sm"
                   name="items[${rowCount}][quantity]" min="1" step="1"
                   placeholder="الكمية" onchange="calculateRowTotal(this)" required>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm"
                   name="items[${rowCount}][price]" min="0" step="0.01"
                   placeholder="السعر" onchange="calculateRowTotal(this)" required>
        </td>
        <td>
            <input type="date" class="form-control form-control-sm"
                   name="items[${rowCount}][production_date]">
        </td>
        <td>
            <input type="date" class="form-control form-control-sm"
                   name="items[${rowCount}][expiry_date]">
        </td>
        <td>
            <input type="number" class="form-control form-control-sm"
                   name="items[${rowCount}][discount]" value="0" min="0" max="100" step="0.01"
                   placeholder="خصم" onchange="calculateRowTotal(this)">
        </td>
        <td>
            <input type="number" class="form-control form-control-sm"
                   name="items[${rowCount}][total]" readonly>
        </td>
        <td>
            <button type="button" class="btn btn-danger btn-sm"
                    onclick="removeItem(this)" title="حذف">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;

    tbody.appendChild(newRow);
}

function removeItem(button) {
    const row = button.closest('tr');
    row.remove();
    calculateTotal();
}

function updatePurchaseOrder() {
    // جمع بيانات النموذج
    const formData = new FormData(document.getElementById('purchaseOrderForm'));
    const data = {};

    // جمع البيانات الأساسية
    for (let [key, value] of formData.entries()) {
        if (!key.startsWith('items[')) {
            data[key] = value;
        }
    }

    // جمع بيانات الأصناف
    const items = [];
    const rows = document.querySelectorAll('#itemsTableBody tr');

    rows.forEach(row => {
        const quantity = parseFloat(row.querySelector('input[name*="[quantity]"]').value) || 0;
        const price = parseFloat(row.querySelector('input[name*="[price]"]').value) || 0;
        const discountRate = parseFloat(row.querySelector('input[name*="[discount]"]').value) || 0;

        const subtotal = quantity * price;
        const discountAmount = subtotal * (discountRate / 100);
        const totalPrice = subtotal - discountAmount;

        const item = {
            code: row.querySelector('input[name*="[code]"]').value,
            name: row.querySelector('input[name*="[name]"]').value,
            unit: row.querySelector('input[name*="[unit]"]').value,
            production_date: row.querySelector('input[name*="[production_date]"]').value,
            expiry_date: row.querySelector('input[name*="[expiry_date]"]').value,
            quantity: quantity,
            price: price,
            discount_rate: discountRate,
            total_price: totalPrice
        };

        if (item.name && item.quantity > 0 && item.price > 0) {
            items.push(item);
        }
    });

    data.items = items;

    // حساب الإجماليات
    data.subtotal = parseFloat(document.getElementById('subtotalAmount').textContent) || 0;
    data.total_discount = parseFloat(document.getElementById('totalDiscount').textContent) || 0;
    data.total_amount = parseFloat(document.getElementById('grandTotal').textContent) || 0;

    // إضافة الحقول الجديدة
    data.gross_amount = parseFloat(document.getElementById('grossAmount').value) || 0;
    data.discount_amount = parseFloat(document.getElementById('discountAmount').value) || 0;
    data.net_amount = parseFloat(document.getElementById('netAmount').value) || 0;

    // إضافة حقل الفرع
    data.branch_id = parseInt(document.getElementById('branchId').value) || 21;

    // إرسال البيانات للخادم
    const poId = document.getElementById('poId').value;

    fetch(`/purchase-orders/api/update/${poId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم تحديث أمر الشراء بنجاح!');
            window.location.href = `/purchase-orders/view/${poId}`;
        } else {
            alert('فشل في تحديث أمر الشراء: ' + data.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ أثناء تحديث أمر الشراء');
    });
}

function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع التغييرات غير المحفوظة.')) {
        location.reload();
    }
}

// تحديث عرض العملة
function updateCurrencyDisplay() {
    const currencySelect = document.getElementById('currency');
    const selectedOption = currencySelect.options[currencySelect.selectedIndex];

    if (selectedOption) {
        const symbol = selectedOption.getAttribute('data-symbol');
        const name = selectedOption.getAttribute('data-name');

        console.log(`💰 تم تغيير العملة إلى: ${symbol} - ${name}`);

        // إعادة حساب الإجمالي مع العملة الجديدة
        calculateTotal();

        // تحديث رموز العملة في الجدول
        updateCurrencySymbolsInTable();
    }
}

// تحديث رموز العملة في جدول الأصناف
function updateCurrencySymbolsInTable() {
    const currencySelect = document.getElementById('currency');
    const selectedOption = currencySelect.options[currencySelect.selectedIndex];

    if (selectedOption) {
        const symbol = selectedOption.getAttribute('data-symbol') || 'ريال';

        console.log(`🔄 تحديث رموز العملة إلى: ${symbol}`);

        // تحديث رموز العملة في ملخص الإجمالي
        const totalLabels = document.querySelectorAll('.currency-symbol');
        totalLabels.forEach(label => {
            label.textContent = symbol;
        });

        // تحديث placeholder في حقول الأسعار
        const priceInputs = document.querySelectorAll('input[name*="[unit_price]"], input[name*="[price]"]');
        priceInputs.forEach(input => {
            const currentPlaceholder = input.getAttribute('placeholder') || '';
            if (currentPlaceholder && !currentPlaceholder.includes(symbol)) {
                // تحديث placeholder ليشمل رمز العملة
                input.setAttribute('placeholder', `0.00 ${symbol}`);
            }
        });

        console.log(`✅ تم تحديث ${totalLabels.length} رمز عملة و ${priceInputs.length} حقل سعر`);
    }
}

// تحميل الفروع للتعديل
async function loadBranchesForEdit() {
    console.log('🔄 بدء تحميل الفروع للتعديل...');

    try {
        const response = await fetch('/purchase-orders/api/branches');
        console.log('📡 استجابة API للتعديل:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const branches = await response.json();
        console.log('📋 الفروع المستلمة للتعديل:', branches);

        const branchSelect = document.getElementById('branchId');
        if (!branchSelect) {
            console.error('❌ لم يتم العثور على عنصر branchId في التعديل');
            return;
        }

        branchSelect.innerHTML = '<option value="">اختر الفرع</option>';

        if (branches && branches.length > 0) {
            branches.forEach(branch => {
                const option = document.createElement('option');
                option.value = branch.brn_no;
                option.textContent = branch.brn_lname;
                branchSelect.appendChild(option);
                console.log(`✅ تم إضافة فرع للتعديل: ${branch.brn_no} - ${branch.brn_lname}`);
            });

            // تعيين القيمة الحالية من البيانات
            const currentBranchId = {{ purchase_order[28] if purchase_order|length > 28 else 21 }};
            branchSelect.value = currentBranchId;
            console.log(`✅ تم تعيين الفرع الحالي: ${currentBranchId}`);
        } else {
            console.warn('⚠️ لا توجد فروع في الاستجابة للتعديل');
            branchSelect.innerHTML = '<option value="21">الفرع الرئيسي</option>';
            branchSelect.value = '21';
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل الفروع للتعديل:', error);

        // إضافة خيار افتراضي في حالة الخطأ
        const branchSelect = document.getElementById('branchId');
        if (branchSelect) {
            branchSelect.innerHTML = '<option value="21">الفرع الرئيسي</option>';
            branchSelect.value = '21';
            console.log('🔧 تم تعيين فرع افتراضي للتعديل بسبب الخطأ');
        }
    }
}

// حساب الإجماليات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    calculateTotal();

    // تحميل الفروع (احتياطي - البيانات محملة من الخادم)
    const branchSelect = document.getElementById('branchId');
    if (branchSelect && branchSelect.options.length <= 1) {
        console.log('🔄 تحميل الفروع للتعديل احتياطياً...');
        loadBranchesForEdit();
    } else {
        console.log('✅ الفروع محملة من الخادم للتعديل:', branchSelect.options.length - 1, 'فرع');
    }

    // تحديث عرض العملة عند تحميل الصفحة
    setTimeout(() => {
        updateCurrencyDisplay();
    }, 100);

    // 🛡️ فحص حماية أمر الشراء عند تحميل الصفحة
    checkPOProtectionOnEdit();
});

// 🛡️ فحص حماية أمر الشراء في صفحة التعديل
async function checkPOProtectionOnEdit() {
    const poId = {{ purchase_order[0] }};
    console.log('🛡️ فحص حماية أمر الشراء في صفحة التعديل:', poId);

    try {
        const response = await fetch(`/purchase-orders/api/check-protection/${poId}`);
        const result = await response.json();

        if (!result.success) {
            console.error('فشل في فحص الحماية:', result.message);
            return;
        }

        if (!result.can_edit) {
            // إظهار تنبيه الحماية
            showProtectionAlert(result);

            // تعطيل النموذج
            disableForm(result);

            console.log('🛡️ أمر الشراء محمي - تم تعطيل النموذج');
        } else {
            console.log('✅ أمر الشراء غير محمي - يمكن التعديل');
        }

    } catch (error) {
        console.error('خطأ في فحص الحماية:', error);
    }
}

// دالة إظهار تنبيه الحماية
function showProtectionAlert(protectionStatus) {
    const alertElement = document.getElementById('protectionAlert');
    const reasonElement = document.getElementById('protectionReason');

    if (alertElement && reasonElement) {
        reasonElement.textContent = protectionStatus.protection_reason;
        alertElement.style.display = 'flex';
        alertElement.classList.add('protection-fade-in');
    }
}

// دالة إخفاء تنبيه الحماية
function hideProtectionAlert() {
    const alertElement = document.getElementById('protectionAlert');
    if (alertElement) {
        alertElement.style.display = 'none';
    }
}

// دالة تعطيل النموذج
function disableForm(protectionStatus) {
    // تعطيل جميع حقول الإدخال
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.disabled = true;
        input.style.backgroundColor = '#f8f9fa';
        input.style.cursor = 'not-allowed';
    });

    // تعطيل الأزرار
    const buttons = document.querySelectorAll('button[type="button"], button[type="submit"]');
    buttons.forEach(button => {
        if (!button.onclick || !button.onclick.toString().includes('window.location')) {
            button.disabled = true;
            button.classList.add('btn-protected');
            button.style.cursor = 'not-allowed';
        }
    });

    // تعطيل زر الحفظ خصوصاً
    const saveBtn = document.querySelector('button[onclick*="updatePurchaseOrder"]');
    if (saveBtn) {
        saveBtn.disabled = true;
        saveBtn.innerHTML = '🔒 محمي من التعديل';
        saveBtn.classList.add('btn-protected');
        saveBtn.title = `لا يمكن حفظ التعديلات: ${protectionStatus.protection_reason}`;
    }

    // إضافة كلاس للنموذج
    const form = document.getElementById('purchaseOrderForm');
    if (form) {
        form.classList.add('form-protected');
    }

    // إضافة رسالة في أعلى النموذج
    const cardBody = document.querySelector('.card-body');
    if (cardBody) {
        const protectionNotice = document.createElement('div');
        protectionNotice.className = 'protection-notice';
        protectionNotice.innerHTML = `
            <strong>🛡️ هذا أمر شراء محمي</strong><br>
            ${protectionStatus.protection_reason}<br>
            <small>لا يمكن تعديل أو حذف أوامر الشراء المستخدمة في الشحنات لضمان سلامة البيانات.</small>
        `;
        cardBody.insertBefore(protectionNotice, cardBody.firstChild);
    }
}

// تعديل دالة updatePurchaseOrder لتتحقق من الحماية
const originalUpdatePurchaseOrder = window.updatePurchaseOrder;
window.updatePurchaseOrder = async function() {
    const poId = {{ purchase_order[0] }};

    try {
        // فحص الحماية قبل الحفظ
        const response = await fetch(`/purchase-orders/api/check-protection/${poId}`);
        const result = await response.json();

        if (!result.success) {
            alert('خطأ في فحص الحماية: ' + result.message);
            return;
        }

        if (!result.can_edit) {
            // إظهار تنبيه الحماية
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'warning',
                    title: '🛡️ أمر شراء محمي',
                    text: `لا يمكن تعديل أمر الشراء ${result.po_number}\n\nالسبب: ${result.protection_reason}`,
                    confirmButtonText: 'موافق'
                });
            } else {
                alert(`🛡️ أمر شراء محمي\n\nلا يمكن تعديل أمر الشراء ${result.po_number}\n\nالسبب: ${result.protection_reason}`);
            }
            return;
        }

        // إذا لم يكن محمي، متابعة الحفظ
        if (originalUpdatePurchaseOrder) {
            originalUpdatePurchaseOrder();
        }

    } catch (error) {
        console.error('خطأ في فحص الحماية:', error);
        alert('حدث خطأ في فحص الحماية');
    }
};

// ===== نظام البحث عن الأصناف =====

// متغير لحفظ الصف النشط
window.currentItemRow = null;

// تفعيل البحث بـ F9
document.addEventListener('keydown', function(e) {
    if (e.key === 'F9') {
        const activeElement = document.activeElement;
        if (activeElement && activeElement.classList.contains('item-code-input')) {
            e.preventDefault();
            window.currentItemRow = activeElement.closest('tr');
            openItemSearchModal();
        }
    }
});

// فتح نافذة بحث الأصناف (نافذة منفصلة)
function openItemSearchModal() {
    try {
        // إنشاء URL لنافذة البحث
        const searchUrl = '/purchase-orders/item-search-window';

        // فتح نافذة منفصلة
        const searchWindow = window.open(
            searchUrl,
            'itemSearchWindow',
            'width=1000,height=700,scrollbars=yes,resizable=yes,centerscreen=yes'
        );

        if (!searchWindow) {
            alert('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
            return;
        }

        console.log('🔍 تم فتح نافذة البحث عن الأصناف');
    } catch (error) {
        console.error('خطأ في فتح نافذة البحث:', error);
        alert('حدث خطأ في فتح نافذة البحث');
    }
}

// معالج استقبال البيانات من نافذة البحث المنفصلة
window.addEventListener('message', function(event) {
    if (event.origin !== window.location.origin) return;

    if (event.data.type === 'itemSelected') {
        const itemData = event.data;

        // التأكد من وجود الصف النشط
        if (window.currentItemRow) {
            const row = window.currentItemRow;

            // تحديث حقول الصنف
            const codeInput = row.querySelector('input[name*="[code]"]');
            const nameInput = row.querySelector('input[name*="[name]"]');
            const unitInput = row.querySelector('input[name*="[unit]"]');

            if (codeInput) codeInput.value = itemData.code || '';
            if (nameInput) nameInput.value = itemData.name || '';
            if (unitInput) unitInput.value = itemData.unit || '';

            // حساب الإجمالي
            calculateRowTotal(row);
            calculateTotal();

            // تركيز على الحقل التالي
            setTimeout(() => {
                const quantityInput = row.querySelector('input[name*="[quantity]"]');
                if (quantityInput) {
                    quantityInput.focus();
                    quantityInput.select();
                }
            }, 100);

            console.log('✅ تم اختيار الصنف:', itemData.name);
        }
    }
});

// تفعيل البحث المباشر عند blur في حقول كود الصنف
document.addEventListener('blur', function(e) {
    if (e.target && e.target.classList.contains('item-code-input')) {
        handleDirectItemSearch(e.target);
    }
}, true);

// معالجة البحث المباشر عن الأصناف
function handleDirectItemSearch(input) {
    const code = input.value.trim();
    if (!code) return;

    console.log('🔍 البحث المباشر عن الصنف بالكود:', code);

    // إرسال طلب البحث بالكود
    fetch(`/purchase-orders/api/get-item-by-code/${encodeURIComponent(code)}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.item) {
            const row = input.closest('tr');
            const item = data.item;

            // ملء بيانات الصنف
            const nameInput = row.querySelector('input[name*="[name]"]');
            const unitInput = row.querySelector('input[name*="[unit]"]');

            if (nameInput) nameInput.value = item.name || '';
            if (unitInput) unitInput.value = item.unit || '';

            // حساب الإجمالي
            calculateRowTotal(row);
            calculateTotal();

            console.log('✅ تم العثور على الصنف:', item.name);
        } else {
            console.log('⚠️ لم يتم العثور على الصنف بالكود:', code);
        }
    })
    .catch(error => {
        console.error('خطأ في البحث بالكود:', error);
    });
}

</script>

<!-- 🛡️ ملف JavaScript لنظام الحماية -->
<script src="{{ url_for('static', filename='js/po_protection.js') }}"></script>



{% endblock %}
