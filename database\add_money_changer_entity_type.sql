-- =====================================================
-- إضافة نوع كيان الصرافين/البنوك إلى النظام المركزي
-- Add Money Changers/Banks Entity Type to Central System
-- =====================================================

-- التحقق من وجود جدول ENTITY_TYPES
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*)
    INTO table_count
    FROM user_tables
    WHERE table_name = 'ENTITY_TYPES';
    
    IF table_count = 0 THEN
        DBMS_OUTPUT.PUT_LINE('⚠️ جدول ENTITY_TYPES غير موجود. يجب إنشاؤه أولاً.');
        RETURN;
    END IF;
    
    DBMS_OUTPUT.PUT_LINE('✅ جدول ENTITY_TYPES موجود');
END;
/

-- إضافة نوع كيان الصرافين/البنوك
DECLARE
    existing_count NUMBER;
BEGIN
    -- التحقق من عدم وجود النوع مسبقاً
    SELECT COUNT(*)
    INTO existing_count
    FROM ENTITY_TYPES
    WHERE entity_type_code = 'MONEY_CHANGER';
    
    IF existing_count > 0 THEN
        DBMS_OUTPUT.PUT_LINE('ℹ️ نوع كيان الصرافين/البنوك موجود بالفعل');
    ELSE
        -- إضافة نوع كيان جديد للصرافين/البنوك
        INSERT INTO ENTITY_TYPES (
            id,
            entity_type_code,
            entity_name_ar,
            entity_name_en,
            module_name,
            table_name,
            id_column,
            name_column,
            description_column,
            has_balances,
            has_transactions,
            default_currency,
            account_prefix,
            sort_order,
            is_active,
            created_date,
            created_by
        ) VALUES (
            6, -- ID جديد
            'MONEY_CHANGER',
            'الصرافين والبنوك',
            'Money Changers and Banks',
            'TRANSFERS',
            'money_changers_banks',
            'id',
            'name',
            'type',
            1, -- has_balances
            1, -- has_transactions
            'SAR',
            '6', -- account_prefix
            60, -- sort_order
            1, -- is_active
            CURRENT_TIMESTAMP,
            1 -- created_by
        );
        
        COMMIT;
        DBMS_OUTPUT.PUT_LINE('✅ تم إضافة نوع كيان الصرافين/البنوك بنجاح');
    END IF;
END;
/

-- التحقق من وجود جدول OPENING_BALANCES
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*)
    INTO table_count
    FROM user_tables
    WHERE table_name = 'OPENING_BALANCES';
    
    IF table_count = 0 THEN
        DBMS_OUTPUT.PUT_LINE('⚠️ جدول OPENING_BALANCES غير موجود. يجب إنشاؤه أولاً.');
    ELSE
        DBMS_OUTPUT.PUT_LINE('✅ جدول OPENING_BALANCES موجود ومتاح للاستخدام');
    END IF;
END;
/

-- إضافة فهرس للبحث السريع عن أرصدة الصرافين/البنوك
DECLARE
    index_count NUMBER;
BEGIN
    SELECT COUNT(*)
    INTO index_count
    FROM user_indexes
    WHERE index_name = 'IDX_OB_MONEY_CHANGER';
    
    IF index_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_OB_MONEY_CHANGER ON OPENING_BALANCES(entity_type_code, entity_id, fiscal_period_start_date) WHERE entity_type_code = ''MONEY_CHANGER''';
        DBMS_OUTPUT.PUT_LINE('✅ تم إنشاء فهرس البحث للصرافين/البنوك');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️ فهرس البحث للصرافين/البنوك موجود بالفعل');
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        -- في حالة عدم دعم WHERE clause في الفهرس، ننشئ فهرس عادي
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_OB_MONEY_CHANGER ON OPENING_BALANCES(entity_type_code, entity_id, fiscal_period_start_date)';
        DBMS_OUTPUT.PUT_LINE('✅ تم إنشاء فهرس البحث العادي للصرافين/البنوك');
END;
/

-- إضافة sequence للأرصدة الافتتاحية إذا لم يكن موجوداً
DECLARE
    seq_count NUMBER;
BEGIN
    SELECT COUNT(*)
    INTO seq_count
    FROM user_sequences
    WHERE sequence_name = 'OPENING_BALANCES_SEQ';
    
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE OPENING_BALANCES_SEQ START WITH 1 INCREMENT BY 1 NOCACHE';
        DBMS_OUTPUT.PUT_LINE('✅ تم إنشاء sequence للأرصدة الافتتاحية');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️ sequence الأرصدة الافتتاحية موجود بالفعل');
    END IF;
END;
/

-- إضافة trigger للـ auto increment إذا لم يكن موجوداً
DECLARE
    trigger_count NUMBER;
BEGIN
    SELECT COUNT(*)
    INTO trigger_count
    FROM user_triggers
    WHERE trigger_name = 'OPENING_BALANCES_TRG';
    
    IF trigger_count = 0 THEN
        EXECUTE IMMEDIATE '
        CREATE OR REPLACE TRIGGER OPENING_BALANCES_TRG
            BEFORE INSERT ON OPENING_BALANCES
            FOR EACH ROW
        BEGIN
            IF :NEW.id IS NULL THEN
                :NEW.id := OPENING_BALANCES_SEQ.NEXTVAL;
            END IF;
        END;';
        
        DBMS_OUTPUT.PUT_LINE('✅ تم إنشاء trigger للأرصدة الافتتاحية');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️ trigger الأرصدة الافتتاحية موجود بالفعل');
    END IF;
END;
/

-- إضافة بيانات تجريبية للصرافين/البنوك إذا لم تكن موجودة
DECLARE
    mc_count NUMBER;
BEGIN
    SELECT COUNT(*)
    INTO mc_count
    FROM money_changers_banks
    WHERE is_active = 1;
    
    IF mc_count = 0 THEN
        -- إضافة بيانات تجريبية
        INSERT INTO money_changers_banks (id, name, type, contact_person, phone, commission_rate, is_active, created_at, updated_at) VALUES
        (1, 'البنك الأهلي السعودي - فرع الرياض', 'bank', 'أحمد محمد', '************', 1.5, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        
        INSERT INTO money_changers_banks (id, name, type, contact_person, phone, commission_rate, is_active, created_at, updated_at) VALUES
        (2, 'صرافة الراجحي - فرع جدة', 'money_changer', 'محمد علي', '************', 2.0, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        
        INSERT INTO money_changers_banks (id, name, type, contact_person, phone, commission_rate, is_active, created_at, updated_at) VALUES
        (3, 'بنك الرياض - فرع الدمام', 'bank', 'سارة أحمد', '************', 1.8, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        
        COMMIT;
        DBMS_OUTPUT.PUT_LINE('✅ تم إضافة بيانات تجريبية للصرافين/البنوك');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️ بيانات الصرافين/البنوك موجودة بالفعل');
    END IF;
END;
/

-- إضافة رصيد افتتاحي تجريبي
DECLARE
    balance_count NUMBER;
BEGIN
    SELECT COUNT(*)
    INTO balance_count
    FROM OPENING_BALANCES
    WHERE entity_type_code = 'MONEY_CHANGER';
    
    IF balance_count = 0 THEN
        -- إضافة رصيد افتتاحي تجريبي
        INSERT INTO OPENING_BALANCES (
            entity_type_code,
            entity_id,
            fiscal_year,
            fiscal_period_start_date,
            currency_code,
            opening_balance_amount,
            balance_type,
            exchange_rate,
            status,
            document_type_code,
            notes,
            created_date,
            created_by,
            is_active
        ) VALUES (
            'MONEY_CHANGER',
            1, -- البنك الأهلي السعودي
            2024,
            DATE '2024-01-01',
            'SAR',
            50000.00,
            'DEBIT',
            1.0000,
            'DRAFT',
            'OPENING_BALANCE',
            'رصيد افتتاحي تجريبي للبنك الأهلي السعودي',
            CURRENT_TIMESTAMP,
            1,
            1
        );
        
        INSERT INTO OPENING_BALANCES (
            entity_type_code,
            entity_id,
            fiscal_year,
            fiscal_period_start_date,
            currency_code,
            opening_balance_amount,
            balance_type,
            exchange_rate,
            status,
            document_type_code,
            notes,
            created_date,
            created_by,
            is_active
        ) VALUES (
            'MONEY_CHANGER',
            2, -- صرافة الراجحي
            2024,
            DATE '2024-01-01',
            'USD',
            10000.00,
            'CREDIT',
            3.7500,
            'DRAFT',
            'OPENING_BALANCE',
            'رصيد افتتاحي تجريبي لصرافة الراجحي بالدولار',
            CURRENT_TIMESTAMP,
            1,
            1
        );
        
        COMMIT;
        DBMS_OUTPUT.PUT_LINE('✅ تم إضافة أرصدة افتتاحية تجريبية للصرافين/البنوك');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️ أرصدة افتتاحية للصرافين/البنوك موجودة بالفعل');
    END IF;
END;
/

-- عرض ملخص النتائج
SELECT 
    'تم إعداد نظام الأرصدة الافتتاحية للصرافين/البنوك بنجاح!' as status,
    (SELECT COUNT(*) FROM ENTITY_TYPES WHERE entity_type_code = 'MONEY_CHANGER') as entity_type_added,
    (SELECT COUNT(*) FROM money_changers_banks WHERE is_active = 1) as active_money_changers,
    (SELECT COUNT(*) FROM OPENING_BALANCES WHERE entity_type_code = 'MONEY_CHANGER') as opening_balances_count
FROM DUAL;
