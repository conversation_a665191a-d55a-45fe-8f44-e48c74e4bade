-- =====================================================
-- إجراءات الترحيل المحاسبي للحوالات
-- Transfer Accounting Procedures
-- =====================================================

-- 1. إجراء تنفيذ الحوالة وترحيل الأرصدة
CREATE OR REPLACE PROCEDURE EXECUTE_TRANSFER_ACCOUNTING(
    p_transfer_id IN NUMBER,
    p_money_changer_id IN NUMBER,
    p_total_amount IN NUMBER,
    p_currency_code IN VARCHAR2,
    p_supplier_distributions IN SYS.ODCIVARCHAR2LIST, -- JSON array of supplier distributions
    p_user_id IN NUMBER DEFAULT 1
) AS
    v_supplier_id NUMBER;
    v_supplier_amount NUMBER;
    v_distribution_count NUMBER := 0;
    v_total_distributed NUMBER := 0;
    v_json_obj JSON_OBJECT_T;
    v_json_array JSON_ARRAY_T;
    v_json_element JSON_ELEMENT_T;
    v_error_msg VARCHAR2(4000);
    
BEGIN
    -- بدء المعاملة
    SAVEPOINT transfer_start;
    
    -- التحقق من صحة البيانات
    IF p_transfer_id IS NULL OR p_money_changer_id IS NULL OR p_total_amount <= 0 THEN
        RAISE_APPLICATION_ERROR(-20001, 'بيانات الحوالة غير صحيحة');
    END IF;
    
    -- التحقق من وجود الحوالة
    IF NOT EXISTS (SELECT 1 FROM transfers WHERE id = p_transfer_id AND status = 'approved') THEN
        RAISE_APPLICATION_ERROR(-20002, 'الحوالة غير موجودة أو غير معتمدة');
    END IF;
    
    -- معالجة توزيعات الموردين
    IF p_supplier_distributions IS NOT NULL AND p_supplier_distributions.COUNT > 0 THEN
        FOR i IN 1..p_supplier_distributions.COUNT LOOP
            -- تحليل JSON للتوزيع
            v_json_obj := JSON_OBJECT_T.parse(p_supplier_distributions(i));
            v_supplier_id := v_json_obj.get_Number('supplier_id');
            v_supplier_amount := v_json_obj.get_Number('amount');
            
            -- التحقق من صحة البيانات
            IF v_supplier_id IS NULL OR v_supplier_amount <= 0 THEN
                RAISE_APPLICATION_ERROR(-20003, 'بيانات توزيع المورد غير صحيحة');
            END IF;
            
            -- ترحيل المبلغ للمورد (زيادة رصيد المورد - الجانب المدين)
            UPDATE_CURRENT_BALANCE(
                p_entity_type => 'SUPPLIER',
                p_entity_id => v_supplier_id,
                p_currency_code => p_currency_code,
                p_debit_amount => v_supplier_amount,
                p_credit_amount => 0,
                p_document_type => 'TRANSFER',
                p_document_number => 'TRF-' || p_transfer_id,
                p_description => 'تنفيذ حوالة رقم ' || p_transfer_id,
                p_user_id => p_user_id
            );
            
            -- حفظ تفاصيل التوزيع
            INSERT INTO transfer_supplier_distributions (
                transfer_id, supplier_id, amount, currency_code,
                created_at, created_by
            ) VALUES (
                p_transfer_id, v_supplier_id, v_supplier_amount, p_currency_code,
                CURRENT_TIMESTAMP, p_user_id
            );
            
            v_distribution_count := v_distribution_count + 1;
            v_total_distributed := v_total_distributed + v_supplier_amount;
        END LOOP;
    END IF;
    
    -- التحقق من تطابق المبالغ
    IF ABS(v_total_distributed - p_total_amount) > 0.01 THEN
        RAISE_APPLICATION_ERROR(-20004, 
            'مجموع التوزيعات (' || v_total_distributed || ') لا يطابق مبلغ الحوالة (' || p_total_amount || ')');
    END IF;
    
    -- ترحيل المبلغ من الصراف/البنك (تقليل رصيد الصراف - الجانب الدائن)
    UPDATE_CURRENT_BALANCE(
        p_entity_type => 'MONEY_CHANGER',
        p_entity_id => p_money_changer_id,
        p_currency_code => p_currency_code,
        p_debit_amount => 0,
        p_credit_amount => p_total_amount,
        p_document_type => 'TRANSFER',
        p_document_number => 'TRF-' || p_transfer_id,
        p_description => 'خصم حوالة رقم ' || p_transfer_id,
        p_user_id => p_user_id
    );
    
    -- تحديث حالة الحوالة
    UPDATE transfers SET
        status = 'executed',
        executed_at = CURRENT_TIMESTAMP,
        executed_by = p_user_id,
        money_changer_id = p_money_changer_id,
        supplier_distributions_count = v_distribution_count,
        total_distributed_amount = v_total_distributed
    WHERE id = p_transfer_id;
    
    -- تسجيل العملية في سجل الأنشطة
    INSERT INTO transfer_activity_log (
        transfer_id, activity_type, description, 
        created_at, created_by
    ) VALUES (
        p_transfer_id, 'EXECUTED', 
        'تم تنفيذ الحوالة وترحيل الأرصدة - الموردين: ' || v_distribution_count || ' - المبلغ: ' || p_total_amount,
        CURRENT_TIMESTAMP, p_user_id
    );
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK TO transfer_start;
        v_error_msg := 'خطأ في تنفيذ الحوالة: ' || SQLERRM;
        
        -- تسجيل الخطأ
        INSERT INTO transfer_activity_log (
            transfer_id, activity_type, description, 
            created_at, created_by
        ) VALUES (
            p_transfer_id, 'ERROR', v_error_msg,
            CURRENT_TIMESTAMP, p_user_id
        );
        COMMIT;
        
        RAISE_APPLICATION_ERROR(-20000, v_error_msg);
END;
/

-- 2. إجراء إلغاء الحوالة وعكس الترحيلات
CREATE OR REPLACE PROCEDURE CANCEL_TRANSFER_ACCOUNTING(
    p_transfer_id IN NUMBER,
    p_user_id IN NUMBER DEFAULT 1
) AS
    v_money_changer_id NUMBER;
    v_total_amount NUMBER;
    v_currency_code VARCHAR2(10);
    v_supplier_id NUMBER;
    v_supplier_amount NUMBER;
    v_error_msg VARCHAR2(4000);
    
    -- Cursor لقراءة توزيعات الموردين
    CURSOR supplier_distributions_cur IS
        SELECT supplier_id, amount
        FROM transfer_supplier_distributions
        WHERE transfer_id = p_transfer_id;
        
BEGIN
    -- بدء المعاملة
    SAVEPOINT cancel_start;
    
    -- الحصول على بيانات الحوالة
    SELECT money_changer_id, amount, currency
    INTO v_money_changer_id, v_total_amount, v_currency_code
    FROM transfers
    WHERE id = p_transfer_id AND status = 'executed';
    
    IF v_money_changer_id IS NULL THEN
        RAISE_APPLICATION_ERROR(-20005, 'الحوالة غير منفذة أو غير موجودة');
    END IF;
    
    -- عكس ترحيلات الموردين (تقليل أرصدة الموردين)
    FOR rec IN supplier_distributions_cur LOOP
        UPDATE_CURRENT_BALANCE(
            p_entity_type => 'SUPPLIER',
            p_entity_id => rec.supplier_id,
            p_currency_code => v_currency_code,
            p_debit_amount => 0,
            p_credit_amount => rec.amount, -- عكس العملية
            p_document_type => 'TRANSFER_CANCEL',
            p_document_number => 'CTRF-' || p_transfer_id,
            p_description => 'إلغاء حوالة رقم ' || p_transfer_id,
            p_user_id => p_user_id
        );
    END LOOP;
    
    -- عكس ترحيل الصراف/البنك (زيادة رصيد الصراف)
    UPDATE_CURRENT_BALANCE(
        p_entity_type => 'MONEY_CHANGER',
        p_entity_id => v_money_changer_id,
        p_currency_code => v_currency_code,
        p_debit_amount => v_total_amount, -- عكس العملية
        p_credit_amount => 0,
        p_document_type => 'TRANSFER_CANCEL',
        p_document_number => 'CTRF-' || p_transfer_id,
        p_description => 'إلغاء حوالة رقم ' || p_transfer_id,
        p_user_id => p_user_id
    );
    
    -- تحديث حالة الحوالة
    UPDATE transfers SET
        status = 'cancelled',
        cancelled_at = CURRENT_TIMESTAMP,
        cancelled_by = p_user_id
    WHERE id = p_transfer_id;
    
    -- تسجيل العملية في سجل الأنشطة
    INSERT INTO transfer_activity_log (
        transfer_id, activity_type, description,
        created_at, created_by
    ) VALUES (
        p_transfer_id, 'CANCELLED',
        'تم إلغاء الحوالة وعكس جميع الترحيلات المحاسبية',
        CURRENT_TIMESTAMP, p_user_id
    );
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK TO cancel_start;
        v_error_msg := 'خطأ في إلغاء الحوالة: ' || SQLERRM;
        
        -- تسجيل الخطأ
        INSERT INTO transfer_activity_log (
            transfer_id, activity_type, description,
            created_at, created_by
        ) VALUES (
            p_transfer_id, 'ERROR', v_error_msg,
            CURRENT_TIMESTAMP, p_user_id
        );
        COMMIT;
        
        RAISE_APPLICATION_ERROR(-20000, v_error_msg);
END;
/

-- 3. إجراء تحديث الأرصدة الجارية (يستخدم النظام الموجود)
CREATE OR REPLACE PROCEDURE UPDATE_CURRENT_BALANCE(
    p_entity_type IN VARCHAR2,
    p_entity_id IN NUMBER,
    p_currency_code IN VARCHAR2,
    p_debit_amount IN NUMBER DEFAULT 0,
    p_credit_amount IN NUMBER DEFAULT 0,
    p_document_type IN VARCHAR2,
    p_document_number IN VARCHAR2,
    p_description IN VARCHAR2,
    p_user_id IN NUMBER DEFAULT 1
) AS
    v_balance_id NUMBER;
    v_current_debit NUMBER := 0;
    v_current_credit NUMBER := 0;
    v_current_balance NUMBER := 0;
    v_transaction_count NUMBER := 0;
    
BEGIN
    -- البحث عن الرصيد الموجود
    BEGIN
        SELECT id, debit_amount, credit_amount, current_balance, total_transactions_count
        INTO v_balance_id, v_current_debit, v_current_credit, v_current_balance, v_transaction_count
        FROM CURRENT_BALANCES
        WHERE entity_type_code = p_entity_type
        AND entity_id = p_entity_id
        AND currency_code = p_currency_code;
        
        -- تحديث الرصيد الموجود
        UPDATE CURRENT_BALANCES SET
            debit_amount = v_current_debit + p_debit_amount,
            credit_amount = v_current_credit + p_credit_amount,
            current_balance = v_current_balance + p_debit_amount - p_credit_amount,
            total_transactions_count = v_transaction_count + 1,
            last_transaction_date = CURRENT_DATE,
            last_document_type = p_document_type,
            last_document_number = p_document_number,
            updated_at = CURRENT_TIMESTAMP,
            updated_by = p_user_id
        WHERE id = v_balance_id;
        
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            -- إنشاء رصيد جديد
            INSERT INTO CURRENT_BALANCES (
                entity_type_code, entity_id, currency_code,
                opening_balance, debit_amount, credit_amount, current_balance,
                total_transactions_count, last_transaction_date,
                last_document_type, last_document_number,
                created_at, updated_at, created_by, updated_by,
                description
            ) VALUES (
                p_entity_type, p_entity_id, p_currency_code,
                0, p_debit_amount, p_credit_amount, p_debit_amount - p_credit_amount,
                1, CURRENT_DATE,
                p_document_type, p_document_number,
                CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, p_user_id, p_user_id,
                p_description
            );
    END;
    
END;
/
