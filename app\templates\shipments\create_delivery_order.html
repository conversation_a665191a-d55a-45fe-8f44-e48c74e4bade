{% extends "base.html" %}

{% block title %}إنشاء أمر تسليم جديد{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان الرئيسي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-plus-square text-primary me-2"></i>
                        إنشاء أمر تسليم جديد
                    </h2>
                    <p class="text-muted mb-0">إنشاء أمر تسليم للمخلص الجمركي</p>
                </div>
                <div>
                    <a href="{{ url_for('shipments.delivery_orders_dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة للوحة الأوامر
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إنشاء أمر التسليم -->
    <div class="row">
        <div class="col-12">
            <form id="deliveryOrderForm" onsubmit="submitDeliveryOrder(event)">
                <!-- معلومات الشحنة -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-ship me-2"></i>
                            اختيار الشحنة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">رقم الفرع <span class="text-danger">*</span></label>
                                <select class="form-select" id="branchId" name="branch_id" required onchange="loadBranchDetails()">
                                    <option value="">اختر الفرع...</option>
                                </select>
                                <div class="form-text">الفرع المسؤول عن أمر التسليم</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الشحنة المؤهلة <span class="text-danger">*</span></label>
                                <select class="form-select" id="shipmentId" name="shipment_id" required onchange="loadShipmentDetails()">
                                    <option value="">اختر الشحنة...</option>
                                    {% for shipment in eligible_shipments %}
                                    <option value="{{ shipment[0] }}"
                                            data-tracking="{{ shipment[1] }}"
                                            data-booking="{{ shipment[2] }}"
                                            data-port="{{ shipment[3] }}"
                                            data-status="{{ shipment[4] }}">
                                        {{ shipment[1] }} - {{ shipment[2] }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">الشحنات التي وصلت الميناء أو قيد التخليص</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">تفاصيل الشحنة والفرع</label>
                                <div id="shipmentDetails" class="border rounded p-3 bg-light">
                                    <p class="text-muted mb-0">اختر فرع وشحنة لعرض التفاصيل</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات المخلص -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-user-tie me-2"></i>
                            اختيار المخلص
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المخلص الجمركي <span class="text-danger">*</span></label>
                                <select class="form-select" id="customsAgentId" name="customs_agent_id" required onchange="loadAgentDetails()">
                                    <option value="">اختر المخلص...</option>
                                    {% for agent in active_agents %}
                                    <option value="{{ agent[0] }}"
                                            data-name="{{ agent[2] }}"
                                            data-company="{{ agent[3] }}"
                                            data-specialization="{{ agent[4] }}"
                                            data-rating="{{ agent[5] }}">
                                        {{ agent[1] }} - {{ agent[2] }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تفاصيل المخلص</label>
                                <div id="agentDetails" class="border rounded p-3 bg-light">
                                    <p class="text-muted mb-0">اختر مخلص لعرض التفاصيل</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الأمر -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i>
                            تفاصيل أمر التسليم
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الأولوية</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="normal">عادي</option>
                                    <option value="high">عالي</option>
                                    <option value="urgent">عاجل</option>
                                    <option value="low">منخفض</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">التاريخ المتوقع للإنجاز</label>
                                <input type="date" class="form-control" id="expectedCompletionDate" name="expected_completion_date">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">التكلفة المتوقعة (ريال)</label>
                                <input type="number" class="form-control" id="estimatedCost" name="estimated_cost" 
                                       step="0.01" placeholder="0.00">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">مكان التسليم</label>
                                <input type="text" class="form-control" id="deliveryLocation" name="delivery_location" 
                                       placeholder="عنوان مكان التسليم">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">شخص الاتصال</label>
                                <input type="text" class="form-control" id="contactPerson" name="contact_person" 
                                       placeholder="اسم شخص الاتصال">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="contactPhone" name="contact_phone" 
                                       placeholder="رقم هاتف شخص الاتصال">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="contactEmail" name="contact_email" 
                                       placeholder="البريد الإلكتروني">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">تعليمات خاصة</label>
                            <textarea class="form-control" id="specialInstructions" name="special_instructions" 
                                      rows="3" placeholder="أي تعليمات خاصة للمخلص..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- الوثائق المطلوبة -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-file-upload me-2"></i>
                            الوثائق المطلوبة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>الوثائق الأساسية:</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="billOfLading" checked>
                                    <label class="form-check-label" for="billOfLading">
                                        بوليصة الشحن (Bill of Lading)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="commercialInvoice" checked>
                                    <label class="form-check-label" for="commercialInvoice">
                                        الفاتورة التجارية (Commercial Invoice)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="packingList" checked>
                                    <label class="form-check-label" for="packingList">
                                        قائمة التعبئة (Packing List)
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>وثائق إضافية:</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="certificateOfOrigin">
                                    <label class="form-check-label" for="certificateOfOrigin">
                                        شهادة المنشأ
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="insurancePolicy">
                                    <label class="form-check-label" for="insurancePolicy">
                                        وثيقة التأمين
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="customsDeclaration">
                                    <label class="form-check-label" for="customsDeclaration">
                                        البيان الجمركي
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                                <i class="fas fa-undo me-1"></i>
                                إعادة تعيين
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                حفظ كمسودة
                            </button>
                            <button type="button" class="btn btn-success" onclick="submitAndSend()">
                                <i class="fas fa-paper-plane me-1"></i>
                                حفظ وإرسال للمخلص
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function loadShipmentDetails() {
    const select = document.getElementById('shipmentId');
    const option = select.options[select.selectedIndex];
    const detailsDiv = document.getElementById('shipmentDetails');
    
    if (option.value) {
        detailsDiv.innerHTML = `
            <div class="row">
                <div class="col-6">
                    <strong>رقم التتبع:</strong><br>
                    <span class="text-primary">${option.dataset.tracking}</span>
                </div>
                <div class="col-6">
                    <strong>رقم الحجز:</strong><br>
                    <span class="text-info">${option.dataset.booking}</span>
                </div>
                <div class="col-6 mt-2">
                    <strong>ميناء التفريغ:</strong><br>
                    <span>${option.dataset.port || 'غير محدد'}</span>
                </div>
                <div class="col-6 mt-2">
                    <strong>حالة الشحنة:</strong><br>
                    <span class="badge bg-info">${option.dataset.status}</span>
                </div>
            </div>
        `;
    } else {
        detailsDiv.innerHTML = '<p class="text-muted mb-0">اختر شحنة لعرض التفاصيل</p>';
    }
}

function loadAgentDetails() {
    const select = document.getElementById('customsAgentId');
    const option = select.options[select.selectedIndex];
    const detailsDiv = document.getElementById('agentDetails');
    
    if (option.value) {
        const rating = parseFloat(option.dataset.rating) || 0;
        const stars = '★'.repeat(Math.floor(rating)) + '☆'.repeat(5 - Math.floor(rating));
        
        detailsDiv.innerHTML = `
            <div class="row">
                <div class="col-6">
                    <strong>اسم المخلص:</strong><br>
                    <span>${option.dataset.name}</span>
                </div>
                <div class="col-6">
                    <strong>الشركة:</strong><br>
                    <span>${option.dataset.company || 'غير محدد'}</span>
                </div>
                <div class="col-6 mt-2">
                    <strong>التخصص:</strong><br>
                    <span class="badge bg-secondary">${option.dataset.specialization}</span>
                </div>
                <div class="col-6 mt-2">
                    <strong>التقييم:</strong><br>
                    <span class="text-warning">${stars}</span> (${rating.toFixed(1)})
                </div>
            </div>
        `;
    } else {
        detailsDiv.innerHTML = '<p class="text-muted mb-0">اختر مخلص لعرض التفاصيل</p>';
    }
}

function submitDeliveryOrder(event) {
    event.preventDefault();
    console.log('💾 حفظ أمر التسليم كمسودة');

    // التحقق من صحة النموذج
    const form = document.getElementById('deliveryOrderForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // جمع البيانات
    const formData = new FormData(form);
    const data = {
        branch_id: parseInt(formData.get('branch_id')),
        shipment_id: parseInt(formData.get('shipment_id')),
        customs_agent_id: parseInt(formData.get('customs_agent_id')),
        delivery_location: formData.get('delivery_location'),
        special_instructions: formData.get('special_instructions'),
        estimated_cost: formData.get('estimated_cost'),
        currency: formData.get('currency') || 'SAR',
        contact_person: formData.get('contact_person'),
        contact_phone: formData.get('contact_phone'),
        contact_email: formData.get('contact_email'),
        expected_completion_date: formData.get('expected_completion_date'),
        priority: formData.get('priority') || 'normal',
        order_status: 'draft'
    };

    // التحقق من البيانات المطلوبة
    if (!data.branch_id || !data.shipment_id || !data.customs_agent_id || !data.delivery_location || !data.expected_completion_date) {
        showMessage('يرجى ملء جميع الحقول المطلوبة (الفرع، الشحنة، المخلص، موقع التسليم، التاريخ المتوقع)', 'warning');
        return;
    }

    // إظهار مؤشر التحميل
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الحفظ...';
    submitBtn.disabled = true;

    // إرسال البيانات
    fetch('/shipments/api/delivery-orders', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showMessage(`تم حفظ أمر التسليم بنجاح: ${result.order_number}`, 'success');

            // إعادة تعيين النموذج بعد 2 ثانية
            setTimeout(() => {
                resetForm();
                // الانتقال إلى لوحة أوامر التسليم
                window.location.href = '/shipments/delivery-orders-dashboard';
            }, 2000);
        } else {
            showMessage('خطأ في حفظ أمر التسليم: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الشبكة:', error);
        showMessage('حدث خطأ في الاتصال بالخادم', 'danger');
    })
    .finally(() => {
        // إعادة تعيين الزر
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function submitAndSend() {
    console.log('📤 حفظ وإرسال أمر التسليم');

    // التحقق من صحة النموذج
    const form = document.getElementById('deliveryOrderForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // جمع البيانات
    const formData = new FormData(form);
    const data = {
        branch_id: parseInt(formData.get('branch_id')),
        shipment_id: parseInt(formData.get('shipment_id')),
        customs_agent_id: parseInt(formData.get('customs_agent_id')),
        delivery_location: formData.get('delivery_location'),
        special_instructions: formData.get('special_instructions'),
        estimated_cost: formData.get('estimated_cost'),
        currency: formData.get('currency') || 'SAR',
        contact_person: formData.get('contact_person'),
        contact_phone: formData.get('contact_phone'),
        contact_email: formData.get('contact_email'),
        expected_completion_date: formData.get('expected_completion_date'),
        priority: formData.get('priority') || 'normal',
        order_status: 'draft'  // حفظ كمسودة أولاً للمعاينة
    };

    // التحقق من البيانات المطلوبة
    if (!data.branch_id || !data.shipment_id || !data.customs_agent_id || !data.delivery_location || !data.expected_completion_date) {
        showMessage('يرجى ملء جميع الحقول المطلوبة (الفرع، الشحنة، المخلص، موقع التسليم، التاريخ المتوقع)', 'warning');
        return;
    }

    // تأكيد الإرسال
    if (!confirm('هل أنت متأكد من إرسال أمر التسليم؟ لن يمكن تعديله بعد الإرسال.')) {
        return;
    }

    // إظهار مؤشر التحميل
    const sendBtn = document.querySelector('button[onclick="submitAndSend()"]');
    const originalText = sendBtn.innerHTML;
    sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الإرسال...';
    sendBtn.disabled = true;

    // إرسال البيانات
    fetch('/shipments/api/delivery-orders', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showMessage(`تم حفظ أمر التسليم بنجاح: ${result.order_number}`, 'success');

            // الانتقال إلى صفحة المعاينة للإرسال
            setTimeout(() => {
                window.location.href = `/shipments/delivery-order-preview/${result.order_id}`;
            }, 1500);
        } else {
            showMessage('خطأ في حفظ أمر التسليم: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الشبكة:', error);
        showMessage('حدث خطأ في الاتصال بالخادم', 'danger');
    })
    .finally(() => {
        // إعادة تعيين الزر
        sendBtn.innerHTML = originalText;
        sendBtn.disabled = false;
    });
}

function resetForm() {
    document.getElementById('deliveryOrderForm').reset();
    document.getElementById('shipmentDetails').innerHTML = '<p class="text-muted mb-0">اختر شحنة لعرض التفاصيل</p>';
    document.getElementById('agentDetails').innerHTML = '<p class="text-muted mb-0">اختر مخلص لعرض التفاصيل</p>';

    // إعادة تعيين التاريخ المتوقع
    const expectedDate = new Date();
    expectedDate.setDate(expectedDate.getDate() + 7);
    document.getElementById('expectedCompletionDate').value = expectedDate.toISOString().split('T')[0];
}

function showMessage(message, type = 'info') {
    // إنشاء عنصر التنبيه
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إضافة التنبيه للصفحة
    document.body.appendChild(alertDiv);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تحميل الفروع
async function loadBranches() {
    try {
        const response = await fetch('/settings/api/branches');
        const data = await response.json();

        if (data.success) {
            const branchSelect = document.getElementById('branchId');
            branchSelect.innerHTML = '<option value="">اختر الفرع...</option>';

            data.branches.forEach(branch => {
                const option = document.createElement('option');
                option.value = branch.brn_no;
                option.textContent = `${branch.brn_no} - ${branch.brn_lname}`;
                option.dataset.branchName = branch.brn_lname;
                option.dataset.branchCode = branch.brn_code;
                option.dataset.branchPhone = branch.brn_ltele;
                branchSelect.appendChild(option);
            });

            console.log('✅ تم تحميل الفروع بنجاح');
        } else {
            console.error('خطأ في تحميل الفروع:', data.message);
            showMessage('خطأ في تحميل الفروع', 'danger');
        }
    } catch (error) {
        console.error('خطأ في الاتصال:', error);
        showMessage('خطأ في الاتصال بالخادم', 'danger');
    }
}

// تحميل تفاصيل الفرع
function loadBranchDetails() {
    const branchSelect = document.getElementById('branchId');
    const shipmentSelect = document.getElementById('shipmentId');
    const detailsDiv = document.getElementById('shipmentDetails');

    const branchOption = branchSelect.options[branchSelect.selectedIndex];
    const shipmentOption = shipmentSelect.options[shipmentSelect.selectedIndex];

    let content = '';

    // تفاصيل الفرع
    if (branchOption.value) {
        content += `
            <div class="mb-3">
                <h6 class="text-primary mb-2">🏢 تفاصيل الفرع</h6>
                <div class="row">
                    <div class="col-6">
                        <strong>رقم الفرع:</strong><br>
                        <span class="text-info">${branchOption.value}</span>
                    </div>
                    <div class="col-6">
                        <strong>اسم الفرع:</strong><br>
                        <span class="text-info">${branchOption.dataset.branchName || 'غير محدد'}</span>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-6">
                        <strong>كود الفرع:</strong><br>
                        <span class="text-info">${branchOption.dataset.branchCode || 'غير محدد'}</span>
                    </div>
                    <div class="col-6">
                        <strong>هاتف الفرع:</strong><br>
                        <span class="text-info">${branchOption.dataset.branchPhone || 'غير محدد'}</span>
                    </div>
                </div>
            </div>
        `;
    }

    // تفاصيل الشحنة
    if (shipmentOption.value) {
        content += `
            <div class="mb-3">
                <h6 class="text-success mb-2">🚢 تفاصيل الشحنة</h6>
                <div class="row">
                    <div class="col-6">
                        <strong>رقم التتبع:</strong><br>
                        <span class="text-primary">${shipmentOption.dataset.tracking}</span>
                    </div>
                    <div class="col-6">
                        <strong>رقم الحجز:</strong><br>
                        <span class="text-primary">${shipmentOption.dataset.booking}</span>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-6">
                        <strong>ميناء التفريغ:</strong><br>
                        <span class="text-primary">${shipmentOption.dataset.port}</span>
                    </div>
                    <div class="col-6">
                        <strong>حالة الشحنة:</strong><br>
                        <span class="badge bg-info">${shipmentOption.dataset.status}</span>
                    </div>
                </div>
            </div>
        `;
    }

    if (!content) {
        content = '<p class="text-muted mb-0">اختر فرع وشحنة لعرض التفاصيل</p>';
    }

    detailsDiv.innerHTML = content;
}

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    console.log('📝 تم تحميل صفحة إنشاء أمر التسليم');

    // تحميل الفروع
    loadBranches();

    // تعيين التاريخ المتوقع (أسبوع من اليوم)
    const expectedDate = new Date();
    expectedDate.setDate(expectedDate.getDate() + 7);
    document.getElementById('expectedCompletionDate').value = expectedDate.toISOString().split('T')[0];
});
</script>
{% endblock %}
