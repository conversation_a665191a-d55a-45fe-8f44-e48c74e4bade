{% extends "base.html" %}

{% block title %}إدارة الصرافين والبنوك{% endblock %}

{% block head %}
<script>
// دوال الأزرار - تحميل فوري
function editMC(id) {
    console.log('تعديل صراف/بنك:', id);

    fetch('/transfers/money-changers/' + id + '/details')
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            const data = result.data;

            // ملء النموذج
            document.getElementById('moneyChangerId').value = data.id;
            document.getElementById('name').value = data.name || '';
            document.getElementById('type').value = data.type || '';
            document.getElementById('branchId').value = data.branch_id || '';
            document.getElementById('contactPerson').value = data.contact_person || '';
            document.getElementById('phone').value = data.phone || '';
            document.getElementById('email').value = data.email || '';
            document.getElementById('commissionRate').value = data.commission_rate || 0;
            document.getElementById('address').value = data.address || '';
            document.getElementById('accountDetails').value = data.account_details || '';
            document.getElementById('isActive').checked = data.is_active;

            // تحديث عنوان المودال
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>تعديل ' + data.name;
            document.getElementById('saveButtonText').textContent = 'تحديث البيانات';

            // تعيين متغير التعديل
            window.currentEditId = data.id;
            console.log('تم تعيين currentEditId إلى:', window.currentEditId);

            // فتح المودال
            const modal = new bootstrap.Modal(document.getElementById('addMoneyChangerModal'));
            modal.show();
        } else {
            alert('خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في جلب البيانات');
    });
}

function toggleMC(id, currentStatus) {
    const action = currentStatus ? 'إلغاء تفعيل' : 'تفعيل';

    if (confirm('هل أنت متأكد من ' + action + ' هذا الصراف/البنك؟')) {
        fetch('/transfers/money-changers/' + id + '/toggle-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert(result.message);
                location.reload();
            } else {
                alert('خطأ: ' + result.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تغيير الحالة');
        });
    }
}

// دالة تحديث البيانات
function updateMoneyChanger() {
    console.log('تحديث البيانات...');

    const data = {
        name: document.getElementById('name').value,
        type: document.getElementById('type').value,
        branch_id: document.getElementById('branchId').value,
        contact_person: document.getElementById('contactPerson').value,
        phone: document.getElementById('phone').value,
        email: document.getElementById('email').value,
        commission_rate: document.getElementById('commissionRate').value,
        address: document.getElementById('address').value,
        account_details: document.getElementById('accountDetails').value,
        is_active: document.getElementById('isActive').checked
    };

    console.log('البيانات المرسلة:', data);
    console.log('المعرف:', window.currentEditId);

    fetch('/transfers/money-changers/' + window.currentEditId + '/edit', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        console.log('النتيجة:', result);
        if (result.success) {
            alert('تم التحديث بنجاح!');
            location.reload();
        } else {
            alert('خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ في التحديث');
    });
}

// دالة إضافة صراف/بنك جديد
function saveMoneyChanger() {
    console.log('إضافة صراف/بنك جديد...');

    const data = {
        name: document.getElementById('name').value,
        type: document.getElementById('type').value,
        branch_id: document.getElementById('branchId').value,
        contact_person: document.getElementById('contactPerson').value,
        phone: document.getElementById('phone').value,
        email: document.getElementById('email').value,
        commission_rate: document.getElementById('commissionRate').value,
        address: document.getElementById('address').value,
        account_details: document.getElementById('accountDetails').value,
        is_active: document.getElementById('isActive').checked
    };

    console.log('البيانات المرسلة:', data);

    fetch('/transfers/money-changers/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        console.log('النتيجة:', result);
        if (result.success) {
            alert('تم الحفظ بنجاح!');
            location.reload();
        } else {
            alert('خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ في الحفظ');
    });
}

// دالة الحفظ القديمة (للحذف)
function saveMoneyChangerOld() {
    console.log('تم استدعاء دالة الحفظ');
    console.log('currentEditId:', window.currentEditId);

    const form = document.getElementById('moneyChangerForm');
    const formData = new FormData(form);

    // تحويل البيانات إلى JSON
    const data = {};
    formData.forEach((value, key) => {
        if (key === 'is_active') {
            data[key] = document.getElementById('isActive').checked;
        } else {
            data[key] = value;
        }
    });

    // التأكد من وجود الفرع
    if (!data.branch_id) {
        alert('الرجاء اختيار الفرع');
        return;
    }

    // تحديد URL والطريقة
    const isEdit = window.currentEditId !== null;
    console.log('حالة التعديل:', isEdit, 'المعرف:', window.currentEditId);

    const url = isEdit ?
        '/transfers/money-changers/' + window.currentEditId + '/edit' :
        '/transfers/money-changers/add';

    console.log('URL المستخدم:', url);

    // إرسال الطلب
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert(result.message);
            // إغلاق المودال
            const modal = bootstrap.Modal.getInstance(document.getElementById('addMoneyChangerModal'));
            if (modal) {
                modal.hide();
            }
            // إعادة تحميل الصفحة
            location.reload();
        } else {
            alert('خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الحفظ');
    });
}

// متغير عام
window.currentEditId = null;
</script>
{% endblock %}

{% block extra_css %}
<script>
// دوال بسيطة ومباشرة
function editMC(id) {
    console.log('تعديل صراف/بنك:', id);

    // جلب البيانات
    fetch('/transfers/money-changers/' + id + '/details')
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            const data = result.data;

            // ملء النموذج
            document.getElementById('moneyChangerId').value = data.id;
            document.getElementById('name').value = data.name || '';
            document.getElementById('type').value = data.type || '';
            document.getElementById('branchId').value = data.branch_id || '';
            document.getElementById('contactPerson').value = data.contact_person || '';
            document.getElementById('phone').value = data.phone || '';
            document.getElementById('email').value = data.email || '';
            document.getElementById('commissionRate').value = data.commission_rate || 0;
            document.getElementById('address').value = data.address || '';
            document.getElementById('accountDetails').value = data.account_details || '';
            document.getElementById('isActive').checked = data.is_active;

            // تحديث عنوان المودال
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>تعديل ' + data.name;
            document.getElementById('saveButtonText').textContent = 'تحديث البيانات';

            // تعيين متغير التعديل
            window.currentEditId = data.id;

            // فتح المودال
            const modal = new bootstrap.Modal(document.getElementById('addMoneyChangerModal'));
            modal.show();
        } else {
            alert('خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في جلب البيانات');
    });
}

function toggleMC(id, currentStatus) {
    const action = currentStatus ? 'إلغاء تفعيل' : 'تفعيل';

    if (confirm('هل أنت متأكد من ' + action + ' هذا الصراف/البنك؟')) {
        fetch('/transfers/money-changers/' + id + '/toggle-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert(result.message);
                location.reload();
            } else {
                alert('خطأ: ' + result.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تغيير الحالة');
        });
    }
}

function editMoneyChanger(id) {
    console.log('تعديل صراف/بنك:', id);

    // جلب البيانات
    fetch(`/transfers/money-changers/${id}/details`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            const data = result.data;

            // ملء النموذج
            document.getElementById('moneyChangerId').value = data.id;
            document.getElementById('name').value = data.name || '';
            document.getElementById('type').value = data.type || '';
            document.getElementById('branchId').value = data.branch_id || '';
            document.getElementById('contactPerson').value = data.contact_person || '';
            document.getElementById('phone').value = data.phone || '';
            document.getElementById('email').value = data.email || '';
            document.getElementById('commissionRate').value = data.commission_rate || 0;
            document.getElementById('address').value = data.address || '';
            document.getElementById('accountDetails').value = data.account_details || '';
            document.getElementById('isActive').checked = data.is_active;

            // تحديث عنوان المودال
            const modalTitle = document.getElementById('modalTitle');
            const typeIcon = data.type === 'bank' ? 'fa-university' : 'fa-exchange-alt';
            modalTitle.innerHTML = `<i class="fas ${typeIcon} me-2"></i>تعديل ${data.name}`;
            document.getElementById('saveButtonText').textContent = 'تحديث البيانات';

            // تعيين متغير التعديل
            window.currentEditId = data.id;

            // فتح المودال
            const modal = new bootstrap.Modal(document.getElementById('addMoneyChangerModal'));
            modal.show();
        } else {
            alert('خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في جلب البيانات');
    });
}

function viewDetails(id) {
    console.log('عرض تفاصيل:', id);
    alert('عرض التفاصيل للصراف/البنك رقم: ' + id);
}

function toggleStatus(id, currentStatus) {
    console.log('تغيير حالة:', id, currentStatus);
    const action = currentStatus ? 'إلغاء تفعيل' : 'تفعيل';

    if (confirm(`هل أنت متأكد من ${action} هذا الصراف/البنك؟`)) {
        fetch(`/transfers/money-changers/${id}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert(result.message);
                location.reload();
            } else {
                alert('خطأ: ' + result.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تغيير الحالة');
        });
    }
}

function deleteMoneyChanger(id) {
    console.log('حذف:', id);
    if (confirm('هل أنت متأكد من حذف هذا الصراف/البنك؟')) {
        fetch(`/transfers/money-changers/${id}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert(result.message);
                location.reload();
            } else {
                alert('خطأ: ' + result.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الحذف');
        });
    }
}

// متغير عام
window.currentEditId = null;
</script>

<style>
    .money-changer-card {
        border-radius: 15px;
        transition: all 0.3s ease;
        border: 1px solid #e3e6f0;
    }

    .money-changer-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .type-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
    }

    .type-bank {
        background: linear-gradient(45deg, #007bff, #0056b3);
        color: white;
    }

    .type-money-changer {
        background: linear-gradient(45deg, #28a745, #1e7e34);
        color: white;
    }

    .stats-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #5a5c69;
    }

    .stats-label {
        font-size: 0.8rem;
        color: #858796;
        text-transform: uppercase;
    }

    .action-btn {
        border-radius: 20px;
        padding: 0.25rem 0.75rem;
        font-size: 0.8rem;
        margin: 0.1rem;
    }

    .search-box {
        border-radius: 25px;
        border: 2px solid #e3e6f0;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
    }

    .search-box:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }

    .add-btn {
        background: linear-gradient(45deg, #4e73df, #224abe);
        border: none;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .add-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(78, 115, 223, 0.4);
        color: white;
    }

    .commission-rate {
        background: linear-gradient(45deg, #f6c23e, #dda20a);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-weight: bold;
        font-size: 0.8rem;
    }

    .status-active {
        color: #1cc88a;
    }

    .status-inactive {
        color: #e74a3b;
    }

    .modal-header {
        background: linear-gradient(45deg, #4e73df, #224abe);
        color: white;
        border-radius: 15px 15px 0 0;
    }

    .form-control {
        border-radius: 10px;
        border: 2px solid #e3e6f0;
        padding: 0.75rem 1rem;
    }

    .form-control:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }

    .btn-primary {
        background: linear-gradient(45deg, #4e73df, #224abe);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
    }

    .btn-success {
        background: linear-gradient(45deg, #1cc88a, #17a673);
        border: none;
        border-radius: 10px;
    }

    .btn-warning {
        background: linear-gradient(45deg, #f6c23e, #dda20a);
        border: none;
        border-radius: 10px;
    }

    .btn-danger {
        background: linear-gradient(45deg, #e74a3b, #c0392b);
        border: none;
        border-radius: 10px;
    }

    .table {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .table thead th {
        background: linear-gradient(45deg, #4e73df, #224abe);
        color: white;
        border: none;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background-color: #f8f9fc;
        transform: scale(1.01);
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #858796;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    /* إصلاح مشكلة المودال المظلل */
    .modal {
        z-index: 1055 !important;
    }

    .modal-backdrop {
        z-index: 1050 !important;
    }

    .modal-dialog {
        z-index: 1056 !important;
    }

    /* التأكد من أن المودال يظهر فوق كل شيء */
    .modal.show {
        display: block !important;
        z-index: 1055 !important;
    }

    .modal.show .modal-dialog {
        transform: none !important;
        z-index: 1056 !important;
    }

    /* إصلاح مشكلة التمرير */
    body.modal-open {
        overflow: hidden;
        padding-right: 0 !important;
    }

    /* التأكد من أن المحتوى قابل للنقر */
    .modal-content {
        position: relative;
        z-index: 1057 !important;
        pointer-events: auto !important;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        pointer-events: auto !important;
    }

    /* إصلاح إضافي للمودال */
    .modal-dialog {
        margin: 1.75rem auto;
        pointer-events: none;
    }

    .modal.fade .modal-dialog {
        transition: transform 0.3s ease-out;
        transform: translate(0, -50px);
    }

    .modal.show .modal-dialog {
        transform: none;
    }

    /* التأكد من أن النماذج تعمل */
    .modal input,
    .modal select,
    .modal textarea,
    .modal button {
        pointer-events: auto !important;
    }

    /* إزالة أي تداخل مع الشريط الجانبي */
    .ns-sidebar {
        z-index: 1040 !important;
    }

    /* تنسيق Header المتقدم */
    .header-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .breadcrumb {
        background: none;
        padding: 0;
        margin: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: #6c757d;
        font-weight: bold;
    }

    .breadcrumb-item a {
        color: #495057;
        transition: color 0.3s ease;
    }

    .breadcrumb-item a:hover {
        color: #007bff;
    }

    .breadcrumb-item.active {
        color: #6c757d;
        font-weight: 500;
    }

    /* تأثيرات الأزرار في Header */
    .btn-light {
        border: 2px solid rgba(255,255,255,0.3);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }

    .btn-light:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        border-color: rgba(255,255,255,0.5);
    }

    .btn-outline-light {
        border-color: rgba(255,255,255,0.5);
        color: white;
    }

    .btn-outline-light:hover {
        background-color: rgba(255,255,255,0.2);
        border-color: white;
        color: white;
        transform: translateY(-1px);
    }

    /* تحسين الإحصائيات في Header */
    .header-stat {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 1rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }

    /* تحسين الكروت */
    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .card:hover {
        box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }

    /* تنسيق Header المودال المبسط */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 1rem 1.5rem !important;
    }

    /* التأكد من ظهور النص */
    .modal-header h5,
    .modal-header small,
    .modal-header .text-white {
        color: white !important;
        z-index: 10 !important;
        position: relative !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* إصلاح مشكلة الخط */
    .modal-header * {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    }

    .modal-lg {
        max-width: 900px;
    }

    /* تحسين الأزرار */
    .btn {
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(0,0,0,0.15);
    }

    /* تحسين النموذج */
    .form-control, .form-select {
        border-radius: 6px;
        border: 1px solid #e0e6ed;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    /* تحسين التسميات */
    .form-label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .form-label.required::after {
        content: " *";
        color: #dc3545;
    }

    /* تحسين الكروت */
    .modal-content {
        border: none;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }

    .modal-header {
        border-radius: 12px 12px 0 0;
    }

    .modal-footer {
        border-radius: 0 0 12px 12px;
    }

    /* تحسين شريط التقدم */
    .progress {
        border-radius: 10px;
        overflow: hidden;
    }

    .progress-bar {
        transition: width 0.6s ease;
    }


</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header المتقدم -->
    <div class="row mb-4">
        <div class="col-12">
            <!-- Header الرئيسي -->
            <div class="card border-0 shadow-sm mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body text-white">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center mb-2">
                                <div class="me-3">
                                    <i class="fas fa-university fa-3x opacity-75"></i>
                                </div>
                                <div>
                                    <h1 class="h2 mb-1 fw-bold">إدارة الصرافين والبنوك</h1>
                                    <p class="mb-0 opacity-90">إدارة شاملة لقائمة الصرافين والبنوك المعتمدين لتنفيذ الحوالات المالية</p>
                                </div>
                            </div>

                            <!-- إحصائيات سريعة في Header -->
                            <div class="row mt-3">
                                <div class="col-md-3 col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-chart-bar me-2 fa-lg"></i>
                                        <div>
                                            <div class="h5 mb-0 fw-bold">{{ money_changers|length }}</div>
                                            <small class="opacity-75">إجمالي</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-check-circle me-2 fa-lg text-success"></i>
                                        <div>
                                            <div class="h5 mb-0 fw-bold">{{ money_changers|selectattr('is_active')|list|length }}</div>
                                            <small class="opacity-75">نشط</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-university me-2 fa-lg text-info"></i>
                                        <div>
                                            <div class="h5 mb-0 fw-bold">{{ money_changers|selectattr('type', 'equalto', 'bank')|list|length }}</div>
                                            <small class="opacity-75">بنوك</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-exchange-alt me-2 fa-lg text-warning"></i>
                                        <div>
                                            <div class="h5 mb-0 fw-bold">{{ money_changers|selectattr('type', 'equalto', 'money_changer')|list|length }}</div>
                                            <small class="opacity-75">صرافات</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 text-end">
                            <!-- أزرار الإجراءات -->
                            <div class="d-flex flex-column gap-2">
                                <button class="btn btn-light btn-lg shadow-sm" data-bs-toggle="modal" data-bs-target="#addMoneyChangerModal" onclick="
                                    window.currentEditId = null;
                                    document.getElementById('modalTitle').innerHTML = '<i class=&quot;fas fa-plus me-2&quot;></i>إضافة صراف/بنك جديد';
                                    document.getElementById('saveButtonText').textContent = 'حفظ';
                                    document.getElementById('moneyChangerForm').reset();
                                ">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة صراف/بنك جديد
                                </button>

                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-light" onclick="exportData()" title="تصدير البيانات">
                                        <i class="fas fa-download me-1"></i>
                                        تصدير
                                    </button>
                                    <button class="btn btn-outline-light" onclick="printReport()" title="طباعة التقرير">
                                        <i class="fas fa-print me-1"></i>
                                        طباعة
                                    </button>
                                    <button class="btn btn-outline-light" onclick="refreshPage()" title="تحديث الصفحة">
                                        <i class="fas fa-sync-alt me-1"></i>
                                        تحديث
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- شريط التنقل السريع -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body py-2">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item">
                                <a href="{{ url_for('main.dashboard') }}" class="text-decoration-none">
                                    <i class="fas fa-home me-1"></i>
                                    الرئيسية
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ url_for('transfers.dashboard') }}" class="text-decoration-none">
                                    <i class="fas fa-money-bill-transfer me-1"></i>
                                    نظام الحوالات
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">
                                <i class="fas fa-university me-1"></i>
                                إدارة الصرافين والبنوك
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات تفصيلية -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card money-changer-card text-center h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                            <i class="fas fa-chart-bar text-primary fa-2x"></i>
                        </div>
                    </div>
                    <div class="stats-number text-primary" id="totalCount">{{ money_changers|length }}</div>
                    <div class="stats-label">إجمالي الصرافين/البنوك</div>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar bg-primary" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card money-changer-card text-center h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="rounded-circle bg-success bg-opacity-10 p-3">
                            <i class="fas fa-check-circle text-success fa-2x"></i>
                        </div>
                    </div>
                    <div class="stats-number text-success" id="activeCount">
                        {{ money_changers|selectattr('is_active')|list|length }}
                    </div>
                    <div class="stats-label">نشط</div>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar bg-success" style="width: 80%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card money-changer-card text-center h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="rounded-circle bg-info bg-opacity-10 p-3">
                            <i class="fas fa-university text-info fa-2x"></i>
                        </div>
                    </div>
                    <div class="stats-number text-info" id="bankCount">
                        {{ money_changers|selectattr('type', 'equalto', 'bank')|list|length }}
                    </div>
                    <div class="stats-label">بنوك</div>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar bg-info" style="width: 60%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card money-changer-card text-center h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="rounded-circle bg-warning bg-opacity-10 p-3">
                            <i class="fas fa-exchange-alt text-warning fa-2x"></i>
                        </div>
                    </div>
                    <div class="stats-number text-warning" id="moneyChangerCount">
                        {{ money_changers|selectattr('type', 'equalto', 'money_changer')|list|length }}
                    </div>
                    <div class="stats-label">صرافات</div>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar bg-warning" style="width: 40%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات البحث والفلترة -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="input-group">
                <input type="text" class="form-control search-box" id="searchInput"
                       placeholder="البحث بالاسم أو رقم الهاتف...">
                <button class="btn btn-outline-primary" type="button" id="searchBtn">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="typeFilter">
                <option value="">جميع الأنواع</option>
                <option value="bank">بنوك</option>
                <option value="money_changer">صرافات</option>
            </select>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="statusFilter">
                <option value="">جميع الحالات</option>
                <option value="1">نشط</option>
                <option value="0">غير نشط</option>
            </select>
        </div>
    </div>

    <!-- جدول البيانات -->
    <div class="row">
        <div class="col-12">
            <div class="card money-changer-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الصرافين والبنوك
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if money_changers %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="moneyChangersTable">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>النوع</th>
                                    <th>الفرع</th>
                                    <th>الشخص المسؤول</th>
                                    <th>الهاتف</th>
                                    <th>نسبة العمولة</th>
                                    <th>عدد الحوالات</th>
                                    <th>إجمالي المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for money_changer in money_changers %}
                                <tr data-id="{{ money_changer.id }}"
                                    data-type="{{ money_changer.type }}"
                                    data-status="{{ money_changer.is_active|int }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <i class="fas fa-{{ 'university' if money_changer.type == 'bank' else 'exchange-alt' }}
                                                   text-{{ 'primary' if money_changer.type == 'bank' else 'success' }} fa-2x"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold">{{ money_changer.name }}</div>
                                                <small class="text-muted">{{ money_changer.email or 'لا يوجد بريد إلكتروني' }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="type-badge type-{{ money_changer.type }}">
                                            {{ 'بنك' if money_changer.type == 'bank' else 'صرافة' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-map-marker-alt text-info me-2"></i>
                                            <span>{{ money_changer.branch_name or 'غير محدد' }}</span>
                                        </div>
                                    </td>
                                    <td>{{ money_changer.contact_person or '-' }}</td>
                                    <td>
                                        {% if money_changer.phone %}
                                            <a href="tel:{{ money_changer.phone }}" class="text-decoration-none">
                                                <i class="fas fa-phone me-1"></i>
                                                {{ money_changer.phone }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="commission-rate">
                                            {{ money_changer.commission_rate|round(2) }}%
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ money_changer.transfer_count }}</span>
                                    </td>
                                    <td>
                                        <strong>${{ money_changer.total_amount|round(2) }}</strong>
                                    </td>
                                    <td>
                                        <i class="fas fa-circle status-{{ 'active' if money_changer.is_active else 'inactive' }} me-1"></i>
                                        {{ 'نشط' if money_changer.is_active else 'غير نشط' }}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-primary action-btn"
                                                    onclick="alert('عرض تفاصيل الصراف/البنك رقم: {{ money_changer.id }}')"
                                                    title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-warning action-btn"
                                                    onclick="
                                                    fetch('/transfers/money-changers/{{ money_changer.id }}/details')
                                                    .then(response => response.json())
                                                    .then(result => {
                                                        if (result.success) {
                                                            const data = result.data;
                                                            document.getElementById('moneyChangerId').value = data.id;
                                                            document.getElementById('name').value = data.name || '';
                                                            document.getElementById('type').value = data.type || '';
                                                            document.getElementById('branchId').value = data.branch_id || '';
                                                            document.getElementById('contactPerson').value = data.contact_person || '';
                                                            document.getElementById('phone').value = data.phone || '';
                                                            document.getElementById('email').value = data.email || '';
                                                            document.getElementById('commissionRate').value = data.commission_rate || 0;
                                                            document.getElementById('address').value = data.address || '';
                                                            document.getElementById('accountDetails').value = data.account_details || '';
                                                            document.getElementById('isActive').checked = data.is_active;
                                                            document.getElementById('modalTitle').innerHTML = '<i class=&quot;fas fa-edit me-2&quot;></i>تعديل ' + data.name;
                                                            document.getElementById('saveButtonText').textContent = 'تحديث البيانات';
                                                            window.currentEditId = data.id;
                                                            const modal = new bootstrap.Modal(document.getElementById('addMoneyChangerModal'));
                                                            modal.show();
                                                        } else {
                                                            alert('خطأ: ' + result.message);
                                                        }
                                                    })
                                                    .catch(error => {
                                                        console.error('Error:', error);
                                                        alert('حدث خطأ في جلب البيانات');
                                                    });
                                                    "
                                                    title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-{{ 'danger' if money_changer.is_active else 'success' }} action-btn"
                                                    onclick="
                                                    if (confirm('هل أنت متأكد من {{ 'إلغاء تفعيل' if money_changer.is_active else 'تفعيل' }} هذا الصراف/البنك؟')) {
                                                        fetch('/transfers/money-changers/{{ money_changer.id }}/toggle-status', {
                                                            method: 'POST',
                                                            headers: { 'Content-Type': 'application/json' }
                                                        })
                                                        .then(response => response.json())
                                                        .then(result => {
                                                            if (result.success) {
                                                                alert(result.message);
                                                                location.reload();
                                                            } else {
                                                                alert('خطأ: ' + result.message);
                                                            }
                                                        })
                                                        .catch(error => {
                                                            console.error('Error:', error);
                                                            alert('حدث خطأ في تغيير الحالة');
                                                        });
                                                    }
                                                    "
                                                    title="{{ 'إلغاء التفعيل' if money_changer.is_active else 'تفعيل' }}">
                                                <i class="fas fa-{{ 'ban' if money_changer.is_active else 'check' }}"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger action-btn"
                                                    onclick="
                                                    if (confirm('⚠️ تحذير: هل أنت متأكد من حذف هذا الصراف/البنك نهائياً؟\\n\\nهذا الإجراء لا يمكن التراجع عنه وسيؤثر على جميع الحوالات المرتبطة به.')) {
                                                        if (confirm('تأكيد نهائي: اضغط موافق لحذف الصراف/البنك نهائياً')) {
                                                            fetch('/transfers/money-changers/{{ money_changer.id }}/delete', {
                                                                method: 'DELETE',
                                                                headers: { 'Content-Type': 'application/json' }
                                                            })
                                                            .then(response => response.json())
                                                            .then(result => {
                                                                if (result.success) {
                                                                    alert('✅ ' + result.message);
                                                                    location.reload();
                                                                } else {
                                                                    alert('❌ خطأ: ' + result.message);
                                                                }
                                                            })
                                                            .catch(error => {
                                                                console.error('Error:', error);
                                                                alert('❌ حدث خطأ في عملية الحذف');
                                                            });
                                                        }
                                                    }
                                                    "
                                                    title="حذف نهائي">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="empty-state">
                        <i class="fas fa-university"></i>
                        <h4>لا توجد صرافات أو بنوك</h4>
                        <p>ابدأ بإضافة أول صراف أو بنك للنظام</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addMoneyChangerModal" onclick="
                            window.currentEditId = null;
                            document.getElementById('modalTitle').innerHTML = '<i class=&quot;fas fa-plus me-2&quot;></i>إضافة صراف/بنك جديد';
                            document.getElementById('saveButtonText').textContent = 'حفظ';
                            document.getElementById('moneyChangerForm').reset();
                        ">
                            <i class="fas fa-plus me-2"></i>
                            إضافة الآن
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال إضافة/تعديل صراف/بنك -->
<div class="modal fade" id="addMoneyChangerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Header بسيط ومضمون -->
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 20px;">
                <h5 class="modal-title" id="modalTitle" style="color: white !important; font-weight: bold; font-size: 1.3rem;">
                    <i class="fas fa-plus me-2"></i>
                    إضافة صراف/بنك جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close" style="filter: invert(1);"></button>
            </div>
            <div class="modal-body">
                <form id="moneyChangerForm">
                    <input type="hidden" id="moneyChangerId" name="id">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label required">
                                <i class="fas fa-building me-1"></i>
                                اسم الصراف/البنك
                            </label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="type" class="form-label required">
                                <i class="fas fa-tags me-1"></i>
                                النوع
                            </label>
                            <select class="form-select" id="type" name="type" required>
                                <option value="">اختر النوع</option>
                                <option value="bank">بنك</option>
                                <option value="money_changer">صرافة</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="branchId" class="form-label required">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                الفرع
                            </label>
                            <select class="form-select" id="branchId" name="branch_id" required>
                                <option value="">اختر الفرع</option>
                                {% for branch in branches %}
                                <option value="{{ branch.id }}">{{ branch.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="contactPerson" class="form-label">
                                <i class="fas fa-user me-1"></i>
                                الشخص المسؤول
                            </label>
                            <input type="text" class="form-control" id="contactPerson" name="contact_person">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone me-1"></i>
                                رقم الهاتف
                            </label>
                            <input type="tel" class="form-control" id="phone" name="phone">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-1"></i>
                                البريد الإلكتروني
                            </label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="commissionRate" class="form-label">
                                <i class="fas fa-percentage me-1"></i>
                                نسبة العمولة (%)
                            </label>
                            <input type="number" class="form-control" id="commissionRate" name="commission_rate"
                                   step="0.01" min="0" max="100" value="0">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            العنوان
                        </label>
                        <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="accountDetails" class="form-label">
                            <i class="fas fa-credit-card me-1"></i>
                            تفاصيل الحساب
                        </label>
                        <textarea class="form-control" id="accountDetails" name="account_details" rows="3"
                                  placeholder="أرقام الحسابات، تفاصيل التحويل، إلخ..."></textarea>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="isActive" name="is_active" checked>
                        <label class="form-check-label" for="isActive">
                            <i class="fas fa-check-circle me-1"></i>
                            نشط
                        </label>
                    </div>
                </form>
            </div>
            <!-- Footer مبسط وأنيق -->
            <div class="modal-footer bg-light border-0">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <!-- معلومات سريعة -->
                    <div class="d-flex align-items-center text-muted">
                        <i class="fas fa-info-circle me-2"></i>
                        <small>الحقول المطلوبة مميزة بـ *</small>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="fas fa-undo me-1"></i>
                            إعادة تعيين
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </button>
                        <button type="button" class="btn btn-success" onclick="
                            console.log('🔧 بدء عملية الحفظ...');
                            console.log('📝 currentEditId:', window.currentEditId);

                            const data = {
                                name: document.getElementById('name').value,
                                type: document.getElementById('type').value,
                                branch_id: document.getElementById('branchId').value,
                                contact_person: document.getElementById('contactPerson').value,
                                phone: document.getElementById('phone').value,
                                email: document.getElementById('email').value,
                                commission_rate: document.getElementById('commissionRate').value,
                                address: document.getElementById('address').value,
                                account_details: document.getElementById('accountDetails').value,
                                is_active: document.getElementById('isActive').checked
                            };

                            console.log('📤 البيانات المرسلة:', data);

                            const url = window.currentEditId ?
                                '/transfers/money-changers/' + window.currentEditId + '/edit' :
                                '/transfers/money-changers/add';

                            console.log('🌐 URL:', url);

                            fetch(url, {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify(data)
                            })
                            .then(response => {
                                console.log('📥 Response status:', response.status);
                                return response.json();
                            })
                            .then(result => {
                                console.log('✅ Response data:', result);
                                if (result.success) {
                                    alert(result.message);
                                    console.log('🔄 إعادة تحميل الصفحة...');
                                    location.reload();
                                } else {
                                    alert('خطأ: ' + result.message);
                                }
                            })
                            .catch(error => {
                                console.error('❌ خطأ في الطلب:', error);
                                alert('حدث خطأ');
                            });
                        ">
                            <i class="fas fa-save me-1"></i>
                            <span id="saveButtonText">حفظ</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال عرض التفاصيل -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>
                    تفاصيل الصراف/البنك
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailsContent">
                <div class="text-center py-4">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p class="mt-2">جاري تحميل التفاصيل...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// متغيرات عامة
let currentEditId = null;

// تعديل صراف/بنك - فتح المودال
window.editMoneyChanger = function(id) {
    currentEditId = id;

    // جلب البيانات
    fetch(`/transfers/money-changers/${id}/details`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            const data = result.data;

            // ملء النموذج
            document.getElementById('moneyChangerId').value = data.id;
            document.getElementById('name').value = data.name || '';
            document.getElementById('type').value = data.type || '';
            document.getElementById('branchId').value = data.branch_id || '';
            document.getElementById('contactPerson').value = data.contact_person || '';
            document.getElementById('phone').value = data.phone || '';
            document.getElementById('email').value = data.email || '';
            document.getElementById('commissionRate').value = data.commission_rate || 0;
            document.getElementById('address').value = data.address || '';
            document.getElementById('accountDetails').value = data.account_details || '';
            document.getElementById('isActive').checked = data.is_active;

            // تحديث عنوان المودال
            const modalTitle = document.getElementById('modalTitle');
            const typeIcon = data.type === 'bank' ? 'fa-university' : 'fa-exchange-alt';
            modalTitle.innerHTML = `<i class="fas ${typeIcon} me-2"></i>تعديل ${data.name}`;
            document.getElementById('saveButtonText').textContent = 'تحديث البيانات';

            // فتح المودال
            const modal = new bootstrap.Modal(document.getElementById('addMoneyChangerModal'));
            modal.show();
        } else {
            alert('خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في جلب البيانات');
    });
}



// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {

    // إضافة event listeners للأزرار

    // أزرار التعديل
    document.querySelectorAll('.edit-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            console.log('تعديل صراف/بنك:', id);

            window.currentEditId = id;

            // جلب البيانات
            fetch(`/transfers/money-changers/${id}/details`)
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    const data = result.data;

                    // ملء النموذج
                    document.getElementById('moneyChangerId').value = data.id;
                    document.getElementById('name').value = data.name || '';
                    document.getElementById('type').value = data.type || '';
                    document.getElementById('branchId').value = data.branch_id || '';
                    document.getElementById('contactPerson').value = data.contact_person || '';
                    document.getElementById('phone').value = data.phone || '';
                    document.getElementById('email').value = data.email || '';
                    document.getElementById('commissionRate').value = data.commission_rate || 0;
                    document.getElementById('address').value = data.address || '';
                    document.getElementById('accountDetails').value = data.account_details || '';
                    document.getElementById('isActive').checked = data.is_active;

                    // تحديث عنوان المودال
                    const modalTitle = document.getElementById('modalTitle');
                    const typeIcon = data.type === 'bank' ? 'fa-university' : 'fa-exchange-alt';
                    modalTitle.innerHTML = `<i class="fas ${typeIcon} me-2"></i>تعديل ${data.name}`;
                    document.getElementById('saveButtonText').textContent = 'تحديث البيانات';

                    // فتح المودال
                    const modal = new bootstrap.Modal(document.getElementById('addMoneyChangerModal'));
                    modal.show();
                } else {
                    alert('خطأ: ' + result.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في جلب البيانات');
            });
        });
    });

    // أزرار العرض
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            console.log('عرض تفاصيل:', id);
            alert('عرض التفاصيل للصراف/البنك رقم: ' + id);
        });
    });

    // أزرار تغيير الحالة
    document.querySelectorAll('.toggle-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const currentStatus = this.getAttribute('data-status') === '1';
            const action = currentStatus ? 'إلغاء تفعيل' : 'تفعيل';

            if (confirm(`هل أنت متأكد من ${action} هذا الصراف/البنك؟`)) {
                fetch(`/transfers/money-changers/${id}/toggle-status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        alert(result.message);
                        location.reload();
                    } else {
                        alert('خطأ: ' + result.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في تغيير الحالة');
                });
            }
        });
    });
    setupEventListeners();
    setupFilters();
    fixModalIssues();
    setupModalEnhancements();

    // إصلاح عنوان المودال عند الفتح
    const modal = document.getElementById('addMoneyChangerModal');
    if (modal) {
        modal.addEventListener('show.bs.modal', function() {
            setTimeout(() => {
                const modalTitle = document.getElementById('modalTitle');
                if (modalTitle) {
                    modalTitle.innerHTML = '<i class="fas fa-plus me-2"></i>إضافة صراف/بنك جديد';
                    modalTitle.style.color = 'white';
                    modalTitle.style.display = 'block';
                    modalTitle.style.visibility = 'visible';
                }
            }, 100);
        });
    }

    // إصلاح بسيط للمودال
    const modalButtons = document.querySelectorAll('[data-bs-toggle="modal"]');
    modalButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const targetModal = document.querySelector(this.getAttribute('data-bs-target'));
            if (targetModal) {
                targetModal.style.display = 'flex';
                targetModal.classList.add('show');
                document.body.style.overflow = 'hidden';
            }
        });
    });

    // إغلاق المودال
    const closeButtons = document.querySelectorAll('[data-bs-dismiss="modal"], .modal-backdrop');
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.display = 'none';
                modal.classList.remove('show');
            });
            document.body.style.overflow = '';
        });
    });
});

// تنظيف شامل للمودالات
function cleanupModals() {
    // إزالة جميع backdrop القديمة
    const backdrops = document.querySelectorAll('.modal-backdrop');
    backdrops.forEach(backdrop => backdrop.remove());

    // إزالة class modal-open من body
    document.body.classList.remove('modal-open');
    document.body.style.paddingRight = '';
    document.body.style.overflow = '';

    // إخفاء جميع المودالات
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.style.display = 'none';
        modal.classList.remove('show');
        modal.setAttribute('aria-hidden', 'true');
        modal.removeAttribute('aria-modal');
    });
}

// إصلاح مشاكل المودال
function fixModalIssues() {
    // التأكد من أن المودال يعمل بشكل صحيح
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('show.bs.modal', function() {
            // إزالة أي backdrop قديم
            const oldBackdrops = document.querySelectorAll('.modal-backdrop');
            oldBackdrops.forEach(backdrop => backdrop.remove());

            // التأكد من z-index صحيح
            this.style.zIndex = '1055';
            this.style.display = 'block';
        });

        modal.addEventListener('shown.bs.modal', function() {
            // التأكد من أن المودال قابل للتفاعل
            this.style.pointerEvents = 'auto';
            const modalContent = this.querySelector('.modal-content');
            if (modalContent) {
                modalContent.style.pointerEvents = 'auto';
            }
        });

        modal.addEventListener('hidden.bs.modal', function() {
            // تنظيف بعد إغلاق المودال
            this.style.display = 'none';
            document.body.classList.remove('modal-open');
            document.body.style.paddingRight = '';

            // إزالة أي backdrop متبقي
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
        });
    });
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // البحث
    document.getElementById('searchInput').addEventListener('input', filterTable);
    document.getElementById('searchBtn').addEventListener('click', filterTable);

    // الفلاتر
    document.getElementById('typeFilter').addEventListener('change', filterTable);
    document.getElementById('statusFilter').addEventListener('change', filterTable);

    // إعادة تعيين النموذج عند إغلاق المودال
    document.getElementById('addMoneyChangerModal').addEventListener('hidden.bs.modal', function() {
        resetForm();
    });
}

// إعداد الفلاتر
function setupFilters() {
    // يمكن إضافة المزيد من الفلاتر هنا
}

// فلترة الجدول
function filterTable() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const typeFilter = document.getElementById('typeFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;

    const rows = document.querySelectorAll('#moneyChangersTable tbody tr');

    rows.forEach(row => {
        const name = row.cells[0].textContent.toLowerCase();
        const phone = row.cells[3].textContent.toLowerCase();
        const type = row.getAttribute('data-type');
        const status = row.getAttribute('data-status');

        const matchesSearch = name.includes(searchTerm) || phone.includes(searchTerm);
        const matchesType = !typeFilter || type === typeFilter;
        const matchesStatus = !statusFilter || status === statusFilter;

        if (matchesSearch && matchesType && matchesStatus) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// إعادة تعيين النموذج
function resetForm() {
    document.getElementById('moneyChangerForm').reset();
    document.getElementById('moneyChangerId').value = '';

    // تحديث العنوان
    const modalTitle = document.getElementById('modalTitle');
    if (modalTitle) {
        modalTitle.innerHTML = '<i class="fas fa-plus me-2"></i>إضافة صراف/بنك جديد';
        modalTitle.style.color = 'white';
        modalTitle.style.fontWeight = 'bold';
    }

    const saveButton = document.getElementById('saveButtonText');
    if (saveButton) {
        saveButton.textContent = 'حفظ';
    }

    const isActiveCheckbox = document.getElementById('isActive');
    if (isActiveCheckbox) {
        isActiveCheckbox.checked = true;
    }

    // إعادة تعيين الفرع للخيار الأول
    const branchSelect = document.getElementById('branchId');
    if (branchSelect && branchSelect.options.length > 1) {
        branchSelect.selectedIndex = 1; // اختيار أول فرع متاح
    }

    window.currentEditId = null;
}



// حفظ الصراف/البنك
function saveMoneyChanger() {
    console.log('تم استدعاء دالة الحفظ');
    console.log('currentEditId:', window.currentEditId);

    const form = document.getElementById('moneyChangerForm');
    const formData = new FormData(form);

    // تحويل البيانات إلى JSON
    const data = {};
    formData.forEach((value, key) => {
        if (key === 'is_active') {
            data[key] = document.getElementById('isActive').checked;
        } else {
            data[key] = value;
        }
    });

    // التأكد من وجود الفرع
    if (!data.branch_id) {
        showAlert('error', 'الرجاء اختيار الفرع');
        return;
    }

    // تحديد URL والطريقة
    const isEdit = window.currentEditId !== null;
    console.log('حالة التعديل:', isEdit, 'المعرف:', window.currentEditId);

    const url = isEdit ?
        `/transfers/money-changers/${window.currentEditId}/edit` :
        '/transfers/money-changers/add';

    console.log('URL المستخدم:', url);

    // إرسال الطلب
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('success', result.message);
            bootstrap.Modal.getInstance(document.getElementById('addMoneyChangerModal')).hide();
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert('error', result.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'حدث خطأ في الاتصال');
    });
}





// عرض نموذج التعديل
function showEditForm(data) {
    // إخفاء الجدول
    document.querySelector('.table-responsive').style.display = 'none';

    // إظهار نموذج التعديل
    const formHtml = `
        <div id="inlineForm" class="card">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    تعديل ${data.name}
                </h5>
            </div>
            <div class="card-body">
                <form id="quickEditForm">
                    <input type="hidden" id="editId" value="${data.id}">
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label class="form-label">اسم الصراف/البنك *</label>
                            <input type="text" class="form-control" id="editName" value="${data.name || ''}" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">النوع *</label>
                            <select class="form-select" id="editType" required>
                                <option value="bank" ${data.type === 'bank' ? 'selected' : ''}>بنك</option>
                                <option value="money_changer" ${data.type === 'money_changer' ? 'selected' : ''}>صرافة</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الشخص المسؤول</label>
                            <input type="text" class="form-control" id="editContact" value="${data.contact_person || ''}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="editPhone" value="${data.phone || ''}">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="editEmail" value="${data.email || ''}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">نسبة العمولة (%)</label>
                            <input type="number" class="form-control" id="editCommission" step="0.01" min="0" max="100" value="${data.commission_rate || 0}">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">العنوان</label>
                        <textarea class="form-control" id="editAddress" rows="2">${data.address || ''}</textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">تفاصيل الحساب</label>
                        <textarea class="form-control" id="editAccountDetails" rows="3">${data.account_details || ''}</textarea>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="editActive" ${data.is_active ? 'checked' : ''}>
                        <label class="form-check-label" for="editActive">نشط</label>
                    </div>
                </form>
            </div>
            <div class="card-footer">
                <button type="button" class="btn btn-warning me-2" onclick="saveEditForm()">
                    <i class="fas fa-save me-1"></i>
                    تحديث
                </button>
                <button type="button" class="btn btn-secondary" onclick="cancelForm()">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
            </div>
        </div>
    `;

    // إدراج النموذج
    const container = document.querySelector('.card.money-changer-card .card-body');
    container.innerHTML = formHtml;
}

// حفظ نموذج التعديل
function saveEditForm() {
    const id = document.getElementById('editId').value;
    const data = {
        name: document.getElementById('editName').value,
        type: document.getElementById('editType').value,
        contact_person: document.getElementById('editContact').value,
        phone: document.getElementById('editPhone').value,
        email: document.getElementById('editEmail').value,
        commission_rate: document.getElementById('editCommission').value,
        address: document.getElementById('editAddress').value,
        account_details: document.getElementById('editAccountDetails').value,
        is_active: document.getElementById('editActive').checked
    };

    if (!data.name || !data.type) {
        alert('الرجاء ملء الحقول المطلوبة');
        return;
    }

    fetch(`/transfers/money-changers/${id}/edit`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('تم التحديث بنجاح!');
            location.reload();
        } else {
            alert('خطأ: ' + result.message);
        }
    })
    .catch(error => {
        alert('خطأ في الاتصال');
        console.error(error);
    });
}

// عرض التفاصيل
window.viewDetails = function(id) {
    // عرض التفاصيل مباشرة في الصفحة
    fetch(`/transfers/money-changers/${id}/details`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showDetailsInline(result.data);
        } else {
            alert('خطأ: ' + result.message);
        }
    })
    .catch(error => {
        alert('خطأ في جلب البيانات');
        console.error(error);
    });
}

// عرض التفاصيل في الصفحة
function showDetailsInline(data) {
    // إخفاء الجدول
    document.querySelector('.table-responsive').style.display = 'none';

    // عرض التفاصيل
    const detailsHtml = `
        <div id="inlineDetails" class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    تفاصيل ${data.name}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">المعلومات الأساسية</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>الاسم:</strong> ${data.name}</p>
                                <p><strong>النوع:</strong> ${data.type === 'bank' ? 'بنك' : 'صرافة'}</p>
                                <p><strong>الشخص المسؤول:</strong> ${data.contact_person || '-'}</p>
                                <p><strong>الهاتف:</strong> ${data.phone || '-'}</p>
                                <p><strong>البريد الإلكتروني:</strong> ${data.email || '-'}</p>
                                <p><strong>نسبة العمولة:</strong> ${data.commission_rate || 0}%</p>
                                <p><strong>الحالة:</strong>
                                    <span class="badge bg-${data.is_active ? 'success' : 'danger'}">
                                        ${data.is_active ? 'نشط' : 'غير نشط'}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">الإحصائيات</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>عدد الحوالات:</strong> ${data.total_transfers}</p>
                                <p><strong>إجمالي المبلغ:</strong> $${data.total_amount.toLocaleString()}</p>
                                <p><strong>إجمالي العمولة:</strong> $${data.total_commission.toLocaleString()}</p>
                                <p><strong>تاريخ الإنشاء:</strong> ${data.created_at}</p>
                                <p><strong>آخر تحديث:</strong> ${data.updated_at}</p>
                            </div>
                        </div>
                    </div>
                </div>
                ${data.address ? `
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">العنوان</h6>
                    </div>
                    <div class="card-body">
                        <p>${data.address}</p>
                    </div>
                </div>
                ` : ''}
                ${data.account_details ? `
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">تفاصيل الحساب</h6>
                    </div>
                    <div class="card-body">
                        <pre class="mb-0">${data.account_details}</pre>
                    </div>
                </div>
                ` : ''}
            </div>
            <div class="card-footer">
                <button type="button" class="btn btn-secondary" onclick="cancelForm()">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للقائمة
                </button>
            </div>
        </div>
    `;

    // إدراج التفاصيل
    const container = document.querySelector('.card.money-changer-card .card-body');
    container.innerHTML = detailsHtml;
}

    // جلب البيانات
    fetch(`/transfers/money-changers/${id}/details`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            const data = result.data;
            displayDetails(data);
        } else {
            document.getElementById('detailsContent').innerHTML = `
                <div class="text-center text-danger py-4">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <p>${result.message}</p>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('detailsContent').innerHTML = `
            <div class="text-center text-danger py-4">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <p>حدث خطأ في جلب البيانات</p>
            </div>
        `;
    });
}

// عرض التفاصيل في المودال
function displayDetails(data) {
    const content = `
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">المعلومات الأساسية</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>الاسم:</strong> ${data.name}</p>
                        <p><strong>النوع:</strong> ${data.type === 'bank' ? 'بنك' : 'صرافة'}</p>
                        <p><strong>الشخص المسؤول:</strong> ${data.contact_person || '-'}</p>
                        <p><strong>الهاتف:</strong> ${data.phone || '-'}</p>
                        <p><strong>البريد الإلكتروني:</strong> ${data.email || '-'}</p>
                        <p><strong>نسبة العمولة:</strong> ${data.commission_rate || 0}%</p>
                        <p><strong>الحالة:</strong>
                            <span class="badge bg-${data.is_active ? 'success' : 'danger'}">
                                ${data.is_active ? 'نشط' : 'غير نشط'}
                            </span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">الإحصائيات</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>عدد الحوالات:</strong> ${data.total_transfers}</p>
                        <p><strong>إجمالي المبلغ:</strong> $${data.total_amount.toLocaleString()}</p>
                        <p><strong>إجمالي العمولة:</strong> $${data.total_commission.toLocaleString()}</p>
                        <p><strong>تاريخ الإنشاء:</strong> ${data.created_at}</p>
                        <p><strong>آخر تحديث:</strong> ${data.updated_at}</p>
                    </div>
                </div>
            </div>
        </div>
        ${data.address ? `
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">العنوان</h6>
            </div>
            <div class="card-body">
                <p>${data.address}</p>
            </div>
        </div>
        ` : ''}
        ${data.account_details ? `
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">تفاصيل الحساب</h6>
            </div>
            <div class="card-body">
                <pre class="mb-0">${data.account_details}</pre>
            </div>
        </div>
        ` : ''}
    `;

    document.getElementById('detailsContent').innerHTML = content;
}

// تغيير حالة التفعيل
window.toggleStatus = function(id, currentStatus) {
    const action = currentStatus ? 'إلغاء تفعيل' : 'تفعيل';

    if (confirm(`هل أنت متأكد من ${action} هذا الصراف/البنك؟`)) {
        fetch(`/transfers/money-changers/${id}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showAlert('success', result.message);
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('error', result.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'حدث خطأ في الاتصال');
        });
    }
}

// حذف صراف/بنك
window.deleteMoneyChanger = function(id) {
    if (confirm('هل أنت متأكد من حذف هذا الصراف/البنك؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        fetch(`/transfers/money-changers/${id}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showAlert('success', result.message);
                // إعادة تحميل الصفحة
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showAlert('error', result.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'حدث خطأ في الحذف');
        });
    }
}

// عرض التنبيهات
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${iconClass} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // إضافة التنبيه في أعلى الصفحة
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // إزالة التنبيه بعد 5 ثوان
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

// تصدير البيانات
function exportData() {
    // جمع البيانات من الجدول
    const rows = document.querySelectorAll('#moneyChangersTable tbody tr');
    let csvContent = "الاسم,النوع,الفرع,الشخص المسؤول,الهاتف,نسبة العمولة,عدد الحوالات,إجمالي المبلغ,الحالة\n";

    rows.forEach(row => {
        if (row.style.display !== 'none') {
            const cells = row.querySelectorAll('td');
            const rowData = [
                cells[0].textContent.trim().replace(/\n\s+/g, ' '),
                cells[1].textContent.trim(),
                cells[2].textContent.trim(),
                cells[3].textContent.trim(),
                cells[4].textContent.trim(),
                cells[5].textContent.trim(),
                cells[6].textContent.trim(),
                cells[7].textContent.trim(),
                cells[8].textContent.trim()
            ];
            csvContent += rowData.join(',') + '\n';
        }
    });

    // تحميل الملف
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `money_changers_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showAlert('success', 'تم تصدير البيانات بنجاح');
}

// طباعة التقرير
function printReport() {
    const printWindow = window.open('', '_blank');
    const tableHtml = document.querySelector('#moneyChangersTable').outerHTML;

    printWindow.document.write(`
        <html>
        <head>
            <title>تقرير الصرافين والبنوك</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f2f2f2; font-weight: bold; }
                .header { text-align: center; margin-bottom: 20px; }
                .date { text-align: left; margin-bottom: 10px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تقرير الصرافين والبنوك</h1>
                <h3>نظام إدارة الحوالات</h3>
            </div>
            <div class="date">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</div>
            ${tableHtml}
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.print();
}

// تحديث الصفحة
function refreshPage() {
    // إضافة تأثير تحميل
    const refreshBtn = document.querySelector('[onclick="refreshPage()"]');
    const originalHtml = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التحديث...';
    refreshBtn.disabled = true;

    setTimeout(() => {
        location.reload();
    }, 1000);
}

// تحسينات المودال
function setupModalEnhancements() {
    // تتبع تقدم النموذج
    const formInputs = document.querySelectorAll('#addMoneyChangerModal input, #addMoneyChangerModal select, #addMoneyChangerModal textarea');
    const totalFields = formInputs.length;
    const requiredFields = document.querySelectorAll('#addMoneyChangerModal [required]').length;

    // تحديث العدادات
    document.getElementById('totalFields').textContent = totalFields;
    document.getElementById('requiredFields').textContent = requiredFields;

    // مراقبة التغييرات في النموذج
    formInputs.forEach(input => {
        input.addEventListener('input', updateFormProgress);
        input.addEventListener('change', updateFormProgress);
    });

    // تحديث العنوان حسب النوع
    const typeSelect = document.getElementById('type');
    if (typeSelect) {
        typeSelect.addEventListener('change', function() {
            const modalTitle = document.getElementById('modalTitle');
            const modalSubtitle = document.getElementById('modalSubtitle');

            if (this.value === 'bank') {
                modalTitle.innerHTML = '<i class="fas fa-university me-2"></i>إضافة بنك جديد';
                modalSubtitle.textContent = 'إضافة بنك جديد إلى قائمة مقدمي خدمات الحوالات';
            } else if (this.value === 'money_changer') {
                modalTitle.innerHTML = '<i class="fas fa-exchange-alt me-2"></i>إضافة صرافة جديدة';
                modalSubtitle.textContent = 'إضافة صرافة جديدة إلى قائمة مقدمي خدمات الحوالات';
            } else {
                modalTitle.innerHTML = '<i class="fas fa-plus me-2"></i>إضافة صراف/بنك جديد';
                modalSubtitle.textContent = 'إضافة صراف أو بنك جديد إلى قائمة مقدمي خدمات الحوالات';
            }
        });
    }
}

// تحديث تقدم النموذج
function updateFormProgress() {
    const formInputs = document.querySelectorAll('#addMoneyChangerModal input, #addMoneyChangerModal select, #addMoneyChangerModal textarea');
    const filledInputs = Array.from(formInputs).filter(input => {
        if (input.type === 'checkbox') {
            return input.checked;
        }
        return input.value.trim() !== '';
    });

    const progress = (filledInputs.length / formInputs.length) * 100;

    // تحديث شريط التقدم
    document.getElementById('formProgress').style.width = progress + '%';
    document.getElementById('bottomProgress').style.width = progress + '%';

    // تحديث العدادات
    document.getElementById('filledFields').textContent = filledInputs.length;

    // تحديث حالة الخطوات
    updateStepStatus(progress);
}

// تحديث حالة الخطوات
function updateStepStatus(progress) {
    const steps = document.querySelectorAll('.step-item');
    const dividers = document.querySelectorAll('.step-divider');

    // إعادة تعيين جميع الخطوات
    steps.forEach(step => {
        step.classList.remove('active', 'completed');
    });
    dividers.forEach(divider => {
        divider.classList.remove('completed');
    });

    // تحديد الخطوة النشطة حسب التقدم
    if (progress >= 75) {
        // الخطوة الثالثة
        steps[0].classList.add('completed');
        steps[1].classList.add('completed');
        steps[2].classList.add('active');
        dividers[0].classList.add('completed');
        dividers[1].classList.add('completed');
    } else if (progress >= 40) {
        // الخطوة الثانية
        steps[0].classList.add('completed');
        steps[1].classList.add('active');
        dividers[0].classList.add('completed');
    } else {
        // الخطوة الأولى
        steps[0].classList.add('active');
    }
}

// مسح النموذج
function clearForm() {
    if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
        document.getElementById('moneyChangerForm').reset();
        updateFormProgress();

        // إعادة تعيين العنوان
        document.getElementById('modalTitle').innerHTML = '<i class="fas fa-plus me-2"></i>إضافة صراف/بنك جديد';
        document.getElementById('modalSubtitle').textContent = 'إضافة صراف أو بنك جديد إلى قائمة مقدمي خدمات الحوالات';

        showAlert('info', 'تم مسح جميع البيانات');
    }
}

// تم تبسيط الكود

</script>
{% endblock %}
