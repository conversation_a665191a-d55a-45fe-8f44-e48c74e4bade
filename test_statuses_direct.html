<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الحالات مباشرة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">🧪 اختبار جلب الحالات مباشرة</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testStatusesEndpoint()">
                            جلب الحالات من قاعدة البيانات
                        </button>
                        
                        <div id="results" class="mt-4"></div>
                        
                        <div class="mt-4">
                            <label class="form-label">اختبار القائمة المنسدلة:</label>
                            <select class="form-select" id="testSelect">
                                <option value="">انقر على الزر أعلاه أولاً...</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testStatusesEndpoint() {
            console.log('🧪 بدء اختبار endpoint...');
            
            const resultsDiv = document.getElementById('results');
            const select = document.getElementById('testSelect');
            
            resultsDiv.innerHTML = '<div class="alert alert-info">جاري جلب الحالات...</div>';
            select.innerHTML = '<option value="">جاري التحميل...</option>';
            
            // إضافة timestamp لتجنب cache
            const timestamp = new Date().getTime();
            fetch(`/shipments/get_available_statuses?v=${timestamp}`)
                .then(response => {
                    console.log('📡 استجابة الخادم:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('📋 البيانات المستلمة:', data);
                    
                    let html = '';
                    
                    if (data.success && data.statuses) {
                        html += `<div class="alert alert-success">
                            ✅ تم جلب ${data.statuses.length} حالة بنجاح
                        </div>`;
                        
                        html += '<div class="table-responsive">';
                        html += '<table class="table table-striped">';
                        html += '<thead><tr><th>#</th><th>الكود</th><th>الاسم العربي</th><th>اللون</th></tr></thead>';
                        html += '<tbody>';
                        
                        // تعبئة القائمة المنسدلة
                        select.innerHTML = '<option value="">اختر حالة...</option>';
                        
                        data.statuses.forEach((status, index) => {
                            html += `<tr>
                                <td>${index + 1}</td>
                                <td><code>${status.code}</code></td>
                                <td><strong>${status.name}</strong></td>
                                <td><span class="badge" style="background-color: ${status.color}">${status.color}</span></td>
                            </tr>`;
                            
                            // إضافة إلى القائمة المنسدلة
                            const option = document.createElement('option');
                            option.value = status.code;
                            option.textContent = status.name;
                            select.appendChild(option);
                        });
                        
                        html += '</tbody></table></div>';
                        
                        // التحقق من وجود arrived_port
                        const arrivedPortExists = data.statuses.some(s => s.code === 'arrived_port');
                        if (arrivedPortExists) {
                            const arrivedPort = data.statuses.find(s => s.code === 'arrived_port');
                            html += `<div class="alert alert-success">
                                ✅ تم العثور على حالة "وصلت للميناء":<br>
                                الكود: <code>${arrivedPort.code}</code><br>
                                الاسم: <strong>${arrivedPort.name}</strong><br>
                                اللون: <span class="badge" style="background-color: ${arrivedPort.color}">${arrivedPort.color}</span>
                            </div>`;
                        } else {
                            html += '<div class="alert alert-danger">❌ لم يتم العثور على حالة "وصلت للميناء"</div>';
                        }
                        
                    } else {
                        html += `<div class="alert alert-danger">
                            ❌ فشل في جلب الحالات: ${data.message || 'خطأ غير معروف'}
                        </div>`;
                    }
                    
                    resultsDiv.innerHTML = html;
                })
                .catch(error => {
                    console.error('❌ خطأ:', error);
                    resultsDiv.innerHTML = `<div class="alert alert-danger">
                        ❌ خطأ في الاتصال: ${error.message}
                    </div>`;
                });
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log('🔄 تحميل الصفحة - بدء الاختبار التلقائي...');
            setTimeout(testStatusesEndpoint, 1000);
        });
    </script>
</body>
</html>
