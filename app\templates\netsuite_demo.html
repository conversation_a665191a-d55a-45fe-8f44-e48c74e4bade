{% extends "base.html" %}
{% set title = "عرض تصميم NetSuite Oracle" %}

{% block content %}
<div class="ns-container-fluid">
    <!-- Header Section -->
    <div class="ns-header">
        <div class="ns-header-content">
            <h1 class="ns-header-title">
                <i class="fas fa-palette ns-icon ns-icon-primary"></i>
                عرض تصميم NetSuite Oracle
            </h1>
            <div class="ns-header-actions">
                <button class="ns-btn ns-btn-primary" data-theme-toggle>
                    <i class="fas fa-moon ns-icon"></i>
                    تبديل المظهر
                </button>
                <button class="ns-btn ns-btn-orange" onclick="nsNotifications.info('مرحباً بك في النظام الجديد!')">
                    <i class="fas fa-bell ns-icon"></i>
                    إشعار تجريبي
                </button>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="ns-row" style="margin-top: 2rem;">
        <div class="ns-col ns-col-3">
            <div class="ns-card ns-hover-lift">
                <div class="ns-card-body">
                    <div class="ns-card-icon ns-card-icon-primary">
                        <i class="fas fa-users ns-icon"></i>
                    </div>
                    <h3 class="ns-text-primary">1,234</h3>
                    <p class="ns-text-secondary">إجمالي المستخدمين</p>
                </div>
            </div>
        </div>
        <div class="ns-col ns-col-3">
            <div class="ns-card ns-hover-lift">
                <div class="ns-card-body">
                    <div class="ns-card-icon ns-card-icon-success">
                        <i class="fas fa-chart-line ns-icon"></i>
                    </div>
                    <h3 class="ns-text-success">$45,678</h3>
                    <p class="ns-text-secondary">إجمالي المبيعات</p>
                </div>
            </div>
        </div>
        <div class="ns-col ns-col-3">
            <div class="ns-card ns-hover-lift">
                <div class="ns-card-body">
                    <div class="ns-card-icon ns-card-icon-warning">
                        <i class="fas fa-shopping-cart ns-icon"></i>
                    </div>
                    <h3 class="ns-text-warning">89</h3>
                    <p class="ns-text-secondary">طلبات معلقة</p>
                </div>
            </div>
        </div>
        <div class="ns-col ns-col-3">
            <div class="ns-card ns-hover-lift">
                <div class="ns-card-body">
                    <div class="ns-card-icon ns-card-icon-error">
                        <i class="fas fa-exclamation-triangle ns-icon"></i>
                    </div>
                    <h3 class="ns-text-error">12</h3>
                    <p class="ns-text-secondary">تنبيهات مهمة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Buttons Demo -->
    <div class="ns-card" style="margin-top: 2rem;">
        <div class="ns-card-header">
            <h3 class="ns-card-title">عرض الأزرار</h3>
        </div>
        <div class="ns-card-body">
            <div style="display: flex; gap: 1rem; flex-wrap: wrap; margin-bottom: 1rem;">
                <button class="ns-btn ns-btn-primary">
                    <i class="fas fa-plus ns-icon"></i>
                    إضافة جديد
                </button>
                <button class="ns-btn ns-btn-secondary">
                    <i class="fas fa-edit ns-icon"></i>
                    تعديل
                </button>
                <button class="ns-btn ns-btn-success">
                    <i class="fas fa-check ns-icon"></i>
                    حفظ
                </button>
                <button class="ns-btn ns-btn-warning">
                    <i class="fas fa-exclamation ns-icon"></i>
                    تحذير
                </button>
                <button class="ns-btn ns-btn-error">
                    <i class="fas fa-trash ns-icon"></i>
                    حذف
                </button>
                <button class="ns-btn ns-btn-orange">
                    <i class="fas fa-download ns-icon"></i>
                    تحميل
                </button>
            </div>
            <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                <button class="ns-btn ns-btn-primary ns-btn-sm">صغير</button>
                <button class="ns-btn ns-btn-primary">عادي</button>
                <button class="ns-btn ns-btn-primary ns-btn-lg">كبير</button>
                <button class="ns-btn ns-btn-primary ns-btn-xl">كبير جداً</button>
            </div>
        </div>
    </div>

    <!-- Forms Demo -->
    <div class="ns-row" style="margin-top: 2rem;">
        <div class="ns-col ns-col-6">
            <div class="ns-card">
                <div class="ns-card-header">
                    <h3 class="ns-card-title">نموذج تجريبي</h3>
                </div>
                <div class="ns-card-body">
                    <div class="ns-form-group">
                        <label class="ns-label ns-label-required">الاسم الكامل</label>
                        <input type="text" class="ns-input" placeholder="أدخل الاسم الكامل">
                    </div>
                    <div class="ns-form-group">
                        <label class="ns-label">البريد الإلكتروني</label>
                        <input type="email" class="ns-input" placeholder="<EMAIL>">
                    </div>
                    <div class="ns-form-group">
                        <label class="ns-label">الدولة</label>
                        <select class="ns-select">
                            <option>اختر الدولة</option>
                            <option>السعودية</option>
                            <option>الإمارات</option>
                            <option>الكويت</option>
                        </select>
                    </div>
                    <div class="ns-form-group">
                        <label class="ns-label">ملاحظات</label>
                        <textarea class="ns-textarea" placeholder="أدخل ملاحظاتك هنا..."></textarea>
                    </div>
                    <div style="display: flex; gap: 1rem;">
                        <button class="ns-btn ns-btn-primary">حفظ</button>
                        <button class="ns-btn ns-btn-secondary">إلغاء</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="ns-col ns-col-6">
            <div class="ns-card">
                <div class="ns-card-header">
                    <h3 class="ns-card-title">التنبيهات والشارات</h3>
                </div>
                <div class="ns-card-body">
                    <div class="ns-alert ns-alert-primary">
                        <i class="fas fa-info-circle ns-icon"></i>
                        هذا تنبيه معلوماتي بنمط NetSuite
                    </div>
                    <div class="ns-alert ns-alert-success">
                        <i class="fas fa-check-circle ns-icon"></i>
                        تم حفظ البيانات بنجاح
                    </div>
                    <div class="ns-alert ns-alert-warning">
                        <i class="fas fa-exclamation-triangle ns-icon"></i>
                        يرجى مراجعة البيانات المدخلة
                    </div>
                    <div class="ns-alert ns-alert-error">
                        <i class="fas fa-times-circle ns-icon"></i>
                        حدث خطأ أثناء معالجة الطلب
                    </div>
                    
                    <h5 style="margin-top: 1.5rem; margin-bottom: 1rem;">الشارات:</h5>
                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                        <span class="ns-badge ns-badge-primary">أساسي</span>
                        <span class="ns-badge ns-badge-success">نجح</span>
                        <span class="ns-badge ns-badge-warning">تحذير</span>
                        <span class="ns-badge ns-badge-error">خطأ</span>
                        <span class="ns-badge ns-badge-orange">برتقالي</span>
                        <span class="ns-badge ns-badge-gray">رمادي</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Table Demo -->
    <div class="ns-card" style="margin-top: 2rem;">
        <div class="ns-card-header">
            <h3 class="ns-card-title">جدول تجريبي متقدم</h3>
        </div>
        <div class="ns-table-container">
            <table class="ns-table ns-table-enhanced ns-table-sortable">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>الاسم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الحالة</th>
                        <th>تاريخ التسجيل</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="cell-number">001</td>
                        <td>أحمد محمد</td>
                        <td><EMAIL></td>
                        <td><span class="ns-badge ns-badge-success">نشط</span></td>
                        <td class="cell-date">2024-01-15</td>
                        <td class="cell-actions">
                            <div class="ns-table-actions">
                                <button class="ns-table-btn ns-table-btn-primary">
                                    <i class="fas fa-eye ns-icon"></i>
                                </button>
                                <button class="ns-table-btn ns-table-btn-secondary">
                                    <i class="fas fa-edit ns-icon"></i>
                                </button>
                                <button class="ns-table-btn ns-table-btn-error">
                                    <i class="fas fa-trash ns-icon"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="cell-number">002</td>
                        <td>فاطمة علي</td>
                        <td><EMAIL></td>
                        <td><span class="ns-badge ns-badge-warning">معلق</span></td>
                        <td class="cell-date">2024-01-14</td>
                        <td class="cell-actions">
                            <div class="ns-table-actions">
                                <button class="ns-table-btn ns-table-btn-primary">
                                    <i class="fas fa-eye ns-icon"></i>
                                </button>
                                <button class="ns-table-btn ns-table-btn-secondary">
                                    <i class="fas fa-edit ns-icon"></i>
                                </button>
                                <button class="ns-table-btn ns-table-btn-error">
                                    <i class="fas fa-trash ns-icon"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="cell-number">003</td>
                        <td>محمد سالم</td>
                        <td><EMAIL></td>
                        <td><span class="ns-badge ns-badge-error">محظور</span></td>
                        <td class="cell-date">2024-01-13</td>
                        <td class="cell-actions">
                            <div class="ns-table-actions">
                                <button class="ns-table-btn ns-table-btn-primary">
                                    <i class="fas fa-eye ns-icon"></i>
                                </button>
                                <button class="ns-table-btn ns-table-btn-secondary">
                                    <i class="fas fa-edit ns-icon"></i>
                                </button>
                                <button class="ns-table-btn ns-table-btn-error">
                                    <i class="fas fa-trash ns-icon"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Notification Buttons -->
    <div class="ns-card" style="margin-top: 2rem;">
        <div class="ns-card-header">
            <h3 class="ns-card-title">اختبار الإشعارات</h3>
        </div>
        <div class="ns-card-body">
            <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                <button class="ns-btn ns-btn-primary" onclick="nsNotifications.info('هذا إشعار معلوماتي')">
                    إشعار معلوماتي
                </button>
                <button class="ns-btn ns-btn-success" onclick="nsNotifications.success('تم الحفظ بنجاح!')">
                    إشعار نجاح
                </button>
                <button class="ns-btn ns-btn-warning" onclick="nsNotifications.warning('تحذير: يرجى المراجعة')">
                    إشعار تحذير
                </button>
                <button class="ns-btn ns-btn-error" onclick="nsNotifications.error('حدث خطأ في النظام')">
                    إشعار خطأ
                </button>
                <button class="ns-btn ns-btn-secondary" onclick="testProgressNotification()">
                    إشعار تقدم
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function testProgressNotification() {
    const progress = nsNotifications.progress('جاري تحميل البيانات...');
    let percent = 0;
    
    const interval = setInterval(() => {
        percent += 10;
        progress.updateProgress(percent);
        
        if (percent >= 100) {
            clearInterval(interval);
            progress.complete('تم تحميل البيانات بنجاح!');
        }
    }, 200);
}
</script>
{% endblock %}
