# دليل معايير التسميات المختصرة
# Short Naming Standards Guide

## 🎯 **الهدف من المعايير**

وضع قواعد موحدة للتسميات المختصرة المتوافقة مع Oracle لضمان:
- **التوافق**: مع جميع إصدارات Oracle (30 حرف كحد أقصى)
- **الوضوح**: أسماء مفهومة ومنطقية
- **الاتساق**: نمط موحد في جميع أنحاء النظام
- **سهولة الاستخدام**: تقليل الأخطاء وزيادة الكفاءة

---

## 📏 **حدود Oracle للأسماء**

| **إصدار Oracle** | **الحد الأقصى** | **التوصية** |
|------------------|-----------------|-------------|
| Oracle 11g وأقل | 30 حرف | 25 حرف (هامش أمان) |
| Oracle 12c وأحدث | 128 حرف | 30 حرف (للتوافق العكسي) |

---

## 🏗️ **قواعد التسمية العامة**

### **1️⃣ القواعد الأساسية:**
- **الطول الأقصى**: 25 حرف
- **الأحرف المسموحة**: A-Z, 0-9, _ (underscore)
- **البداية**: يجب أن تبدأ بحرف
- **لا مسافات**: استخدم _ بدلاً من المسافات
- **حالة الأحرف**: UPPER_CASE للثوابت، lower_case للمتغيرات

### **2️⃣ الاختصارات المعتمدة:**

| **الكلمة الكاملة** | **الاختصار** | **المعنى** |
|-------------------|-------------|-----------|
| BALANCE | BAL | رصيد |
| TRANSACTION | TXN | معاملة |
| ENTITY | ENT | كيان |
| CURRENCY | CURR | عملة |
| DOCUMENT | DOC | مستند |
| NUMBER | NO | رقم |
| AMOUNT | AMT | مبلغ |
| DEBIT | DR | مدين |
| CREDIT | CR | دائن |
| OPENING | OP | افتتاحي |
| CURRENT | CURR | حالي |
| MONTHLY | MONTH | شهري |
| YEARLY | YEAR | سنوي |
| BRANCH | BR | فرع |
| SUPPLIER | SUPP | مورد |
| CUSTOMER | CUST | عميل |
| MONEY_CHANGER | MC | صراف |

---

## 📦 **معايير الـ Packages**

### **1️⃣ أسماء الـ Packages:**
- **النمط**: `[DOMAIN]_PKG`
- **الطول الأقصى**: 15 حرف
- **أمثلة**:
  - `OB_PKG` - Opening Balances Package
  - `BT_PKG` - Balance Transactions Package
  - `RPT_PKG` - Reports Package
  - `UTIL_PKG` - Utilities Package

### **2️⃣ أسماء الإجراءات:**
- **النمط**: `[ACTION]_[OBJECT]`
- **الطول الأقصى**: 20 حرف
- **أمثلة**:
  - `INSERT_BAL` - إدراج رصيد
  - `UPDATE_BAL` - تعديل رصيد
  - `DELETE_BAL` - حذف رصيد
  - `GET_BAL` - الحصول على رصيد
  - `POST_TXN` - ترحيل معاملة
  - `REVERSE_TXN` - عكس معاملة

### **3️⃣ أسماء الدوال:**
- **النمط**: `GET_[OBJECT]` أو `CALC_[OBJECT]`
- **أمثلة**:
  - `GET_BAL` - الحصول على رصيد
  - `GET_MONTH_BAL` - رصيد شهري
  - `CALC_TOTAL` - حساب الإجمالي
  - `CHECK_STATUS` - فحص الحالة

---

## 🗃️ **معايير الجداول والأعمدة**

### **1️⃣ أسماء الأعمدة:**
- **النمط**: `[OBJECT]_[ATTRIBUTE]` أو اختصار مباشر
- **أمثلة**:

| **الاسم الكامل** | **الاختصار** | **الوصف** |
|-----------------|-------------|-----------|
| `BALANCE_AMOUNT` | `BAL` | مبلغ الرصيد |
| `BALANCE_FOREIGN` | `BAL_F` | رصيد بالعملة الأساسية |
| `MONTH_NUMBER` | `MONTH_NO` | رقم الشهر |
| `YEAR_NUMBER` | `YEAR_NO` | رقم السنة |
| `ENTITY_TYPE_CODE` | `ENT_TYPE` | نوع الكيان |
| `ENTITY_ID` | `ENT_ID` | معرف الكيان |
| `DOCUMENT_TYPE` | `DOC_TYPE` | نوع المستند |
| `DOCUMENT_NUMBER` | `DOC_NO` | رقم المستند |
| `CURRENCY_CODE` | `CURR` | رمز العملة |

### **2️⃣ أسماء الفهارس:**
- **النمط**: `IDX_[TABLE]_[COLUMNS]`
- **الطول الأقصى**: 25 حرف
- **أمثلة**:
  - `IDX_BT_ENT_BAL` - فهرس الكيان والرصيد
  - `IDX_BT_PERIOD` - فهرس الفترة
  - `IDX_BT_BRANCH` - فهرس الفرع
  - `IDX_BT_DOC` - فهرس المستند

---

## 👁️ **معايير الـ Views**

### **1️⃣ أسماء الـ Views:**
- **النمط**: `V_[PURPOSE]`
- **أمثلة**:
  - `V_CURR_BAL` - الأرصدة الحالية
  - `V_MONTH_BAL` - الأرصدة الشهرية
  - `V_ENT_SUM` - ملخص الكيانات
  - `V_TXN_HIST` - تاريخ المعاملات

### **2️⃣ أسماء أعمدة الـ Views:**
- استخدم نفس معايير أعمدة الجداول
- **أمثلة**:
  - `ent_type` - نوع الكيان
  - `ent_id` - معرف الكيان
  - `curr_bal` - الرصيد الحالي
  - `month_bal` - الرصيد الشهري

---

## 🔧 **معايير المعاملات (Parameters)**

### **1️⃣ أسماء المعاملات:**
- **النمط**: `p_[name]`
- **الطول الأقصى**: 15 حرف
- **أمثلة**:

| **الاسم الكامل** | **الاختصار** | **الوصف** |
|-----------------|-------------|-----------|
| `p_entity_type_code` | `p_ent_type` | نوع الكيان |
| `p_entity_id` | `p_ent_id` | معرف الكيان |
| `p_currency_code` | `p_curr` | رمز العملة |
| `p_balance_amount` | `p_amount` | مبلغ الرصيد |
| `p_document_type` | `p_doc_type` | نوع المستند |
| `p_document_number` | `p_doc_no` | رقم المستند |
| `p_debit_amount` | `p_dr` | مبلغ مدين |
| `p_credit_amount` | `p_cr` | مبلغ دائن |
| `p_branch_id` | `p_branch` | رقم الفرع |
| `p_user_id` | `p_user` | معرف المستخدم |

### **2️⃣ أسماء المتغيرات المحلية:**
- **النمط**: `v_[name]`
- **أمثلة**:
  - `v_bal` - رصيد
  - `v_count` - عدد
  - `v_total` - إجمالي
  - `v_status` - حالة

---

## 📋 **أمثلة تطبيقية**

### **1️⃣ مثال Package كامل:**
```sql
CREATE OR REPLACE PACKAGE OB_PKG AS
    PROCEDURE INSERT_BAL(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_amount IN NUMBER,
        p_branch IN NUMBER DEFAULT 1,
        p_user IN NUMBER DEFAULT 1
    );
    
    FUNCTION GET_BAL(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2
    ) RETURN NUMBER;
END OB_PKG;
```

### **2️⃣ مثال View:**
```sql
CREATE OR REPLACE VIEW V_CURR_BAL AS
SELECT 
    ent_type,
    ent_id,
    curr,
    SUM(bal) as curr_bal,
    COUNT(*) as txn_count
FROM BALANCE_TRANSACTIONS
GROUP BY ent_type, ent_id, curr;
```

### **3️⃣ مثال فهرس:**
```sql
CREATE INDEX IDX_BT_ENT_BAL 
ON BALANCE_TRANSACTIONS(ENT_TYPE, ENT_ID, CURR);
```

---

## ✅ **قائمة التحقق**

### **قبل إنشاء أي كائن، تأكد من:**
- [ ] الاسم أقل من 25 حرف
- [ ] يتبع النمط المحدد
- [ ] يستخدم الاختصارات المعتمدة
- [ ] واضح ومفهوم
- [ ] متسق مع باقي النظام
- [ ] لا يتعارض مع الكلمات المحجوزة في Oracle

### **اختبار التوافق:**
```sql
-- اختبار طول الاسم
SELECT LENGTH('اسم_الكائن') FROM DUAL;
-- يجب أن يكون أقل من أو يساوي 25

-- اختبار صحة الاسم
CREATE TABLE test_name_validity (id NUMBER);
-- إذا نجح، فالاسم صحيح
DROP TABLE test_name_validity;
```

---

## 🎯 **الفوائد المتوقعة**

### **1️⃣ فوائد تقنية:**
- ✅ توافق كامل مع Oracle
- ✅ تجنب مشاكل حدود الأسماء
- ✅ سهولة الصيانة

### **2️⃣ فوائد تطويرية:**
- ✅ سرعة في الكتابة
- ✅ تقليل الأخطاء
- ✅ معايير موحدة للفريق

### **3️⃣ فوائد تشغيلية:**
- ✅ أداء محسن
- ✅ سهولة التذكر
- ✅ وضوح في التوثيق

---

## 📚 **مراجع إضافية**

- [Oracle Database Object Naming Rules](https://docs.oracle.com/database/121/SQLRF/sql_elements008.htm)
- [Oracle Database Limits](https://docs.oracle.com/database/121/REFRN/GUID-685230CF-63F5-4C5A-B8B0-037C566BDA76.htm)
- [Best Practices for Oracle Database](https://www.oracle.com/database/technologies/best-practices.html)

---

**تم إعداد هذا الدليل لضمان التوافق والاتساق في جميع أنحاء النظام المحاسبي الموحد.**
