# -*- coding: utf-8 -*-
"""
مولد PDF بسيط وحقيقي
Simple Real PDF Generator
"""

import os
import sys
from datetime import datetime
from xhtml2pdf import pisa
import arabic_reshaper
from bidi.algorithm import get_display

# إضافة مسار قاعدة البيانات
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from database_manager import DatabaseManager

class SimpleRealPDF:
    """مولد PDF بسيط وحقيقي"""
    
    def __init__(self):
        """تهيئة مولد PDF"""
        self.output_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'pdf')
        os.makedirs(self.output_dir, exist_ok=True)
    
    def generate_simple_real_pdf(self, delivery_order_id):
        """إنشاء PDF بسيط وحقيقي"""
        try:
            # جلب بيانات أمر التسليم
            order_data = self._get_delivery_order_data(delivery_order_id)
            if not order_data:
                return None, "أمر التسليم غير موجود"
            
            # إنشاء اسم الملف
            filename = f"delivery_order_simple_{delivery_order_id}.pdf"
            filepath = os.path.join(self.output_dir, filename)
            
            # إنشاء HTML بسيط
            html_content = self._create_simple_html(order_data)
            
            # تحويل إلى PDF
            if self._convert_to_pdf(html_content, filepath):
                return filepath, "تم إنشاء PDF بسيط بنجاح"
            else:
                return None, "فشل في تحويل HTML إلى PDF"
            
        except Exception as e:
            return None, f"خطأ في إنشاء PDF: {str(e)}"
    
    def _get_delivery_order_data(self, delivery_order_id):
        """جلب بيانات أمر التسليم"""
        try:
            db_manager = DatabaseManager()
            
            query = """
                SELECT 
                    do.id,
                    do.order_number,
                    do.shipment_id,
                    do.customs_agent_id,
                    ca.agent_name,
                    ca.phone,
                    ca.mobile,
                    do.branch_id,
                    b.brn_lname as branch_name,
                    do.created_date,
                    do.order_status,
                    cs.shipment_number,
                    cs.port_of_loading,
                    cs.port_of_discharge,
                    cs.shipment_status
                FROM delivery_orders do
                LEFT JOIN customs_agents ca ON do.customs_agent_id = ca.id
                LEFT JOIN branches b ON do.branch_id = b.brn_no
                LEFT JOIN cargo_shipments cs ON do.shipment_id = cs.id
                WHERE do.id = :delivery_order_id
            """
            
            result = db_manager.execute_query(query, {'delivery_order_id': delivery_order_id})
            
            if result:
                row = result[0]
                return {
                    'id': row[0],
                    'order_number': row[1],
                    'shipment_id': row[2],
                    'customs_agent_id': row[3],
                    'agent_name': row[4],
                    'agent_phone': row[5],
                    'agent_mobile': row[6],
                    'branch_id': row[7],
                    'branch_name': row[8],
                    'created_date': row[9],
                    'order_status': row[10],
                    'shipment_number': row[11],
                    'origin_port': row[12],
                    'destination_port': row[13],
                    'shipment_status': row[14]
                }
            
            db_manager.close()
            return None
            
        except Exception as e:
            print(f"خطأ في جلب البيانات: {e}")
            return None
    
    def _create_simple_html(self, order_data):
        """إنشاء HTML بسيط للتحويل إلى PDF"""
        
        # معالجة النصوص العربية
        def format_arabic(text):
            if text and any('\u0600' <= char <= '\u06FF' for char in str(text)):
                reshaped = arabic_reshaper.reshape(str(text))
                return get_display(reshaped)
            return str(text) if text else ""
        
        html = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>أمر تسليم - {order_data['order_number']}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            margin: 20px;
            direction: rtl;
            color: #333;
        }}
        .header {{
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .company-name {{
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }}
        .document-title {{
            font-size: 20px;
            font-weight: bold;
            color: #dc3545;
            margin-bottom: 5px;
        }}
        .section {{
            margin-bottom: 25px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }}
        .section-title {{
            background-color: #f8f9fa;
            padding: 10px 15px;
            font-weight: bold;
            font-size: 16px;
            color: #495057;
            border-bottom: 1px solid #ddd;
        }}
        .section-content {{
            padding: 15px;
        }}
        .info-row {{
            display: table;
            width: 100%;
            margin-bottom: 10px;
        }}
        .info-label {{
            display: table-cell;
            width: 30%;
            font-weight: bold;
            padding: 8px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
        }}
        .info-value {{
            display: table-cell;
            width: 70%;
            padding: 8px;
            border: 1px solid #ddd;
        }}
        .signature-section {{
            margin-top: 40px;
            border-top: 2px solid #007bff;
            padding-top: 20px;
        }}
        .signature-box {{
            border: 1px solid #ddd;
            height: 80px;
            margin: 10px 0;
            padding: 10px;
        }}
        .footer {{
            text-align: center;
            margin-top: 30px;
            font-size: 12px;
            color: #6c757d;
        }}
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{format_arabic("شركة النقل والشحن المتطورة")}</div>
        <div class="document-title">{format_arabic("أمر تسليم - Delivery Order")}</div>
    </div>

    <div class="section">
        <div class="section-title">{format_arabic("معلومات أمر التسليم")}</div>
        <div class="section-content">
            <div class="info-row">
                <div class="info-label">{format_arabic("رقم الأمر")}</div>
                <div class="info-value">{order_data['order_number']}</div>
            </div>
            <div class="info-row">
                <div class="info-label">{format_arabic("تاريخ الإنشاء")}</div>
                <div class="info-value">{order_data['created_date']}</div>
            </div>
            <div class="info-row">
                <div class="info-label">{format_arabic("حالة الأمر")}</div>
                <div class="info-value">{format_arabic(order_data['order_status'])}</div>
            </div>
            <div class="info-row">
                <div class="info-label">{format_arabic("رقم الشحنة")}</div>
                <div class="info-value">{order_data['shipment_id']}</div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">{format_arabic("معلومات الشحنة")}</div>
        <div class="section-content">
            <div class="info-row">
                <div class="info-label">{format_arabic("رقم الشحنة")}</div>
                <div class="info-value">{order_data.get('shipment_number', 'غير محدد')}</div>
            </div>
            <div class="info-row">
                <div class="info-label">{format_arabic("ميناء المنشأ")}</div>
                <div class="info-value">{order_data.get('origin_port', 'غير محدد')}</div>
            </div>
            <div class="info-row">
                <div class="info-label">{format_arabic("ميناء الوصول")}</div>
                <div class="info-value">{order_data.get('destination_port', 'غير محدد')}</div>
            </div>
            <div class="info-row">
                <div class="info-label">{format_arabic("حالة الشحنة")}</div>
                <div class="info-value">{format_arabic(order_data.get('shipment_status', 'غير محدد'))}</div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">{format_arabic("معلومات المخلص الجمركي")}</div>
        <div class="section-content">
            <div class="info-row">
                <div class="info-label">{format_arabic("اسم المخلص")}</div>
                <div class="info-value">{format_arabic(order_data.get('agent_name', 'غير محدد'))}</div>
            </div>
            <div class="info-row">
                <div class="info-label">{format_arabic("الهاتف")}</div>
                <div class="info-value">{order_data.get('agent_phone', 'غير محدد')}</div>
            </div>
            <div class="info-row">
                <div class="info-label">{format_arabic("الجوال")}</div>
                <div class="info-value">{order_data.get('agent_mobile', 'غير محدد')}</div>
            </div>
            <div class="info-row">
                <div class="info-label">{format_arabic("الفرع")}</div>
                <div class="info-value">{format_arabic(order_data.get('branch_name', 'غير محدد'))}</div>
            </div>
        </div>
    </div>

    <div class="signature-section">
        <div class="section-title">{format_arabic("التوقيع والاعتماد")}</div>
        <div class="info-row">
            <div class="info-label">{format_arabic("توقيع المخلص")}</div>
            <div class="signature-box"></div>
        </div>
        <div class="info-row">
            <div class="info-label">{format_arabic("التاريخ")}</div>
            <div class="signature-box"></div>
        </div>
        <div class="info-row">
            <div class="info-label">{format_arabic("الختم")}</div>
            <div class="signature-box"></div>
        </div>
    </div>

    <div class="footer">
        <p>{format_arabic("تم إنشاء هذا المستند تلقائياً بواسطة نظام إدارة الشحنات")}</p>
        <p>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
</body>
</html>
        """
        
        return html
    
    def _convert_to_pdf(self, html_content, output_path):
        """تحويل HTML إلى PDF"""
        try:
            print("📄 تحويل HTML بسيط إلى PDF...")
            
            # إنشاء ملف PDF
            with open(output_path, 'wb') as result_file:
                # تحويل HTML إلى PDF
                pisa_status = pisa.CreatePDF(
                    html_content.encode('utf-8'),
                    dest=result_file,
                    encoding='utf-8'
                )
                
                # فحص النتيجة
                if not pisa_status.err:
                    file_size = os.path.getsize(output_path)
                    if file_size > 1000:  # على الأقل 1KB
                        print(f"✅ تم إنشاء PDF بنجاح! حجم الملف: {file_size} بايت")
                        
                        # فحص إضافي للتأكد أنه PDF حقيقي
                        with open(output_path, 'rb') as f:
                            header = f.read(4)
                            if header == b'%PDF':
                                print("✅ الملف هو PDF حقيقي!")
                                return True
                            else:
                                print("❌ الملف ليس PDF حقيقي!")
                                return False
                    else:
                        print("❌ الملف المُنشأ صغير جداً")
                        return False
                else:
                    print(f"❌ خطأ في xhtml2pdf: {pisa_status.err}")
                    return False
                    
        except Exception as e:
            print(f"❌ فشل في التحويل: {e}")
            return False


# إنشاء instance عام للمولد
simple_real_pdf = SimpleRealPDF()


def generate_simple_real_pdf(delivery_order_id):
    """دالة مساعدة لإنشاء PDF بسيط وحقيقي"""
    return simple_real_pdf.generate_simple_real_pdf(delivery_order_id)
