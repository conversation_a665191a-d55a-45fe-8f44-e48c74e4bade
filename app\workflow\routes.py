# -*- coding: utf-8 -*-
"""
مسارات نظام سير العمل
Workflow System Routes
"""

from flask import render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app.workflow import bp
from app import db
from app.models import WorkflowTemplate, WorkflowInstance, WorkflowStep, User, PurchaseRequest
from datetime import datetime
from sqlalchemy import or_, and_

@bp.route('/')
def index():
    """لوحة نظام سير العمل"""
    
    # الحصول على المهام المعلقة للمستخدم الحالي
    pending_tasks = WorkflowStep.query.filter(
        WorkflowStep.approver_user_id == current_user.id
    ).order_by(WorkflowStep.step_number).all()
    
    # الحصول على آخر سير العمل
    recent_workflows = WorkflowInstance.query.order_by(
        WorkflowInstance.created_at.desc()
    ).limit(10).all()
    
    # إحصائيات سير العمل
    workflow_stats = {
        'pending_tasks': len(pending_tasks),
        'active_workflows': WorkflowInstance.query.filter_by(status='active').count(),
        'completed_workflows': WorkflowInstance.query.filter_by(status='completed').count(),
        'total_templates': WorkflowTemplate.query.filter_by(is_active=True).count()
    }
    
    return render_template('workflow/index.html',
                         pending_tasks=pending_tasks,
                         recent_workflows=recent_workflows,
                         workflow_stats=workflow_stats,
                         title='نظام سير العمل')

@bp.route('/templates')
def templates():
    """قوالب سير العمل"""
    templates = WorkflowTemplate.query.filter_by(is_active=True).all()
    
    return render_template('workflow/templates.html',
                         templates=templates,
                         title='قوالب سير العمل')

@bp.route('/instances')
def instances():
    """مثيلات سير العمل"""
    page = request.args.get('page', 1, type=int)
    
    # فلاتر
    status = request.args.get('status')
    template_id = request.args.get('template_id')
    
    query = WorkflowInstance.query
    
    if status:
        query = query.filter(WorkflowInstance.status == status)
    
    if template_id:
        query = query.filter(WorkflowInstance.template_id == template_id)
    
    instances = query.order_by(WorkflowInstance.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('workflow/instances.html',
                         instances=instances,
                         title='مثيلات سير العمل')

@bp.route('/instances/<int:id>')
def view_instance(id):
    """عرض تفاصيل مثيل سير العمل"""
    instance = WorkflowInstance.query.get_or_404(id)
    
    # الحصول على خطوات سير العمل
    steps = WorkflowStep.query.filter_by(workflow_instance_id=id)\
        .order_by(WorkflowStep.step_order).all()
    
    return render_template('workflow/view_instance.html',
                         instance=instance,
                         steps=steps,
                         title=f'سير العمل: {instance.title}')

@bp.route('/tasks')
def my_tasks():
    """مهامي"""
    page = request.args.get('page', 1, type=int)
    
    # فلاتر
    status = request.args.get('status', 'pending')
    
    query = WorkflowStep.query.filter(WorkflowStep.assigned_to == current_user.id)
    
    if status:
        query = query.filter(WorkflowStep.status == status)
    
    tasks = query.order_by(WorkflowStep.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('workflow/my_tasks.html',
                         tasks=tasks,
                         title='مهامي')

@bp.route('/tasks/<int:id>/complete', methods=['POST'])
def complete_task(id):
    """إكمال مهمة"""
    step = WorkflowStep.query.get_or_404(id)
    
    # التحقق من الصلاحية
    if step.assigned_to != current_user.id:
        flash('ليس لديك صلاحية لإكمال هذه المهمة', 'error')
        return redirect(url_for('workflow.my_tasks'))
    
    if step.status != 'pending':
        flash('هذه المهمة مكتملة بالفعل', 'error')
        return redirect(url_for('workflow.my_tasks'))
    
    try:
        # تحديث حالة المهمة
        step.status = 'completed'
        step.completed_at = datetime.utcnow()
        step.completed_by = current_user.id
        step.notes = request.form.get('notes', '')
        
        # التحقق من إكمال جميع المهام في سير العمل
        workflow_instance = step.workflow_instance
        all_steps_completed = all(
            s.status == 'completed' 
            for s in workflow_instance.steps
        )
        
        if all_steps_completed:
            workflow_instance.status = 'completed'
            workflow_instance.completed_at = datetime.utcnow()
            
            # تنفيذ إجراءات ما بعد الإكمال
            if workflow_instance.entity_type == 'purchase_request':
                purchase_request = PurchaseRequest.query.get(workflow_instance.entity_id)
                if purchase_request:
                    purchase_request.status = 'approved'
                    purchase_request.approved_at = datetime.utcnow()
                    purchase_request.approved_by = current_user.id
        else:
            # تفعيل المهمة التالية
            next_step = WorkflowStep.query.filter(
                WorkflowStep.workflow_instance_id == workflow_instance.id,
                WorkflowStep.step_order > step.step_order,
                WorkflowStep.status == 'waiting'
            ).order_by(WorkflowStep.step_order).first()
            
            if next_step:
                next_step.status = 'pending'
                next_step.started_at = datetime.utcnow()
        
        db.session.commit()
        
        flash('تم إكمال المهمة بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء إكمال المهمة', 'error')
        print(f"Error completing task: {e}")
    
    return redirect(url_for('workflow.my_tasks'))

@bp.route('/tasks/<int:id>/reject', methods=['POST'])
def reject_task(id):
    """رفض مهمة"""
    step = WorkflowStep.query.get_or_404(id)
    
    # التحقق من الصلاحية
    if step.assigned_to != current_user.id:
        flash('ليس لديك صلاحية لرفض هذه المهمة', 'error')
        return redirect(url_for('workflow.my_tasks'))
    
    if step.status != 'pending':
        flash('لا يمكن رفض هذه المهمة', 'error')
        return redirect(url_for('workflow.my_tasks'))
    
    try:
        # تحديث حالة المهمة
        step.status = 'rejected'
        step.completed_at = datetime.utcnow()
        step.completed_by = current_user.id
        step.notes = request.form.get('rejection_reason', '')
        
        # تحديث حالة سير العمل
        workflow_instance = step.workflow_instance
        workflow_instance.status = 'rejected'
        workflow_instance.completed_at = datetime.utcnow()
        
        # تنفيذ إجراءات الرفض
        if workflow_instance.entity_type == 'purchase_request':
            purchase_request = PurchaseRequest.query.get(workflow_instance.entity_id)
            if purchase_request:
                purchase_request.status = 'rejected'
                purchase_request.rejection_reason = step.notes
        
        db.session.commit()
        
        flash('تم رفض المهمة', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء رفض المهمة', 'error')
        print(f"Error rejecting task: {e}")
    
    return redirect(url_for('workflow.my_tasks'))

@bp.route('/start-workflow', methods=['POST'])
def start_workflow():
    """بدء سير عمل جديد"""
    template_id = request.form.get('template_id')
    entity_type = request.form.get('entity_type')
    entity_id = request.form.get('entity_id')
    title = request.form.get('title')
    
    template = WorkflowTemplate.query.get_or_404(template_id)
    
    try:
        # إنشاء مثيل سير العمل
        workflow_instance = WorkflowInstance(
            template_id=template_id,
            title=title,
            entity_type=entity_type,
            entity_id=entity_id,
            status='active',
            created_by=current_user.id
        )
        
        db.session.add(workflow_instance)
        db.session.flush()  # للحصول على ID
        
        # إنشاء خطوات سير العمل
        template_steps = template.steps.order_by('step_order').all()
        
        for i, template_step in enumerate(template_steps):
            step = WorkflowStep(
                workflow_instance_id=workflow_instance.id,
                step_name=template_step.step_name,
                step_description=template_step.step_description,
                step_order=template_step.step_order,
                assigned_to=template_step.assigned_to,
                status='pending' if i == 0 else 'waiting',  # أول خطوة تكون معلقة
                started_at=datetime.utcnow() if i == 0 else None
            )
            db.session.add(step)
        
        db.session.commit()
        
        flash(f'تم بدء سير العمل: {title}', 'success')
        return jsonify({'success': True, 'workflow_id': workflow_instance.id})
        
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء بدء سير العمل', 'error')
        print(f"Error starting workflow: {e}")
        return jsonify({'success': False, 'error': str(e)})

def create_purchase_request_workflow(purchase_request_id):
    """إنشاء سير عمل لطلب الشراء"""
    
    # البحث عن قالب سير العمل لطلبات الشراء
    template = WorkflowTemplate.query.filter_by(
        entity_type='purchase_request',
        is_active=True
    ).first()
    
    if not template:
        return False
    
    purchase_request = PurchaseRequest.query.get(purchase_request_id)
    if not purchase_request:
        return False
    
    try:
        # إنشاء مثيل سير العمل
        workflow_instance = WorkflowInstance(
            template_id=template.id,
            title=f'موافقة طلب الشراء: {purchase_request.title}',
            entity_type='purchase_request',
            entity_id=purchase_request_id,
            status='active',
            created_by=purchase_request.created_by
        )
        
        db.session.add(workflow_instance)
        db.session.flush()
        
        # إنشاء خطوات سير العمل
        template_steps = template.steps.order_by('step_order').all()
        
        for i, template_step in enumerate(template_steps):
            step = WorkflowStep(
                workflow_instance_id=workflow_instance.id,
                step_name=template_step.step_name,
                step_description=template_step.step_description,
                step_order=template_step.step_order,
                assigned_to=template_step.assigned_to,
                status='pending' if i == 0 else 'waiting',
                started_at=datetime.utcnow() if i == 0 else None
            )
            db.session.add(step)
        
        # تحديث حالة طلب الشراء
        purchase_request.status = 'pending_approval'
        purchase_request.workflow_instance_id = workflow_instance.id
        
        db.session.commit()
        return True
        
    except Exception as e:
        db.session.rollback()
        print(f"Error creating workflow: {e}")
        return False

@bp.route('/api/workflow-data')
def workflow_data():
    """بيانات سير العمل للرسوم البيانية"""
    
    # إحصائيات حالة سير العمل
    status_stats = db.session.query(
        WorkflowInstance.status,
        func.count(WorkflowInstance.id).label('count')
    ).group_by(WorkflowInstance.status).all()
    
    # المهام حسب المستخدم
    user_tasks = db.session.query(
        User.username,
        func.count(WorkflowStep.id).label('task_count')
    ).join(WorkflowStep, User.id == WorkflowStep.assigned_to)\
     .filter(WorkflowStep.status == 'pending')\
     .group_by(User.id, User.username).all()
    
    data = {
        'workflow_status': [
            {'status': status, 'count': count} 
            for status, count in status_stats
        ],
        'user_tasks': [
            {'user': username, 'tasks': task_count}
            for username, task_count in user_tasks
        ]
    }
    
    return jsonify(data)
