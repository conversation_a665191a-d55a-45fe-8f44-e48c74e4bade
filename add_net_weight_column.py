#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from oracle_manager import OracleManager

def add_net_weight_column():
    """إضافة عمود الوزن الصافي إلى جدول الشحنات"""
    oracle_mgr = OracleManager()
    
    try:
        # التحقق من وجود العمود أولاً
        check_query = """
        SELECT COUNT(*) 
        FROM user_tab_columns 
        WHERE table_name = 'CARGO_SHIPMENTS' 
        AND column_name = 'NET_WEIGHT'
        """
        
        result = oracle_mgr.execute_query(check_query)
        column_exists = result[0][0] > 0 if result else False
        
        if column_exists:
            print("✅ عمود NET_WEIGHT موجود بالفعل في جدول CARGO_SHIPMENTS")
            return True
            
        print("📋 إضافة عمود الوزن الصافي إلى جدول CARGO_SHIPMENTS...")
        
        # إضافة العمود
        add_column_query = """
        ALTER TABLE CARGO_SHIPMENTS 
        ADD (NET_WEIGHT NUMBER(15,3))
        """
        
        oracle_mgr.execute_query(add_column_query)
        print("✅ تم إضافة عمود NET_WEIGHT بنجاح")
        
        # إضافة تعليق على العمود
        comment_query = """
        COMMENT ON COLUMN CARGO_SHIPMENTS.NET_WEIGHT 
        IS 'الوزن الصافي للبضاعة بالكيلوجرام'
        """
        
        oracle_mgr.execute_query(comment_query)
        print("✅ تم إضافة تعليق على العمود")
        
        # إنشاء فهرس للأداء (اختياري)
        index_query = """
        CREATE INDEX IDX_CARGO_SHIPMENTS_NET_WEIGHT 
        ON CARGO_SHIPMENTS(NET_WEIGHT)
        """
        
        try:
            oracle_mgr.execute_query(index_query)
            print("✅ تم إنشاء فهرس للعمود")
        except Exception as e:
            print(f"⚠️ تحذير: لم يتم إنشاء الفهرس: {e}")
        
        # التحقق من النتيجة
        verify_query = """
        SELECT column_name, data_type, nullable, data_length, data_precision, data_scale
        FROM user_tab_columns 
        WHERE table_name = 'CARGO_SHIPMENTS' 
        AND column_name IN ('TOTAL_WEIGHT', 'NET_WEIGHT')
        ORDER BY column_name
        """
        
        columns = oracle_mgr.execute_query(verify_query)
        print("\n📊 أعمدة الوزن في الجدول:")
        for col in columns:
            nullable = "NULL" if col[2] == 'Y' else "NOT NULL"
            precision = f"({col[4]},{col[5]})" if col[4] else ""
            print(f"  {col[0]:<15} {col[1]}{precision:<10} {nullable}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة العمود: {e}")
        return False
        
    finally:
        oracle_mgr.close()

if __name__ == "__main__":
    add_net_weight_column()
