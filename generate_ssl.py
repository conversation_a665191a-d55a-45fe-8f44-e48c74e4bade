#!/usr/bin/env python3
"""
إنشاء شهادة SSL جديدة للنطاق sas.alfogehi.net
"""

import os
from datetime import datetime, timedelta
from cryptography import x509
from cryptography.x509.oid import NameOID, ExtendedKeyUsageOID
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa

def generate_ssl_certificate(domain="sas.alfogehi.net"):
    """إنشاء شهادة SSL للنطاق المحدد"""
    
    print(f"🔐 إنشاء شهادة SSL للنطاق: {domain}")
    
    # إنشاء مجلدات SSL
    ssl_cert_dir = os.path.join('ssl', 'certs')
    ssl_key_dir = os.path.join('ssl', 'private')
    os.makedirs(ssl_cert_dir, exist_ok=True)
    os.makedirs(ssl_key_dir, exist_ok=True)
    
    # مسارات الملفات
    cert_path = os.path.join(ssl_cert_dir, f'{domain}.crt')
    key_path = os.path.join(ssl_key_dir, f'{domain}.key')
    
    print(f"📄 مسار الشهادة: {cert_path}")
    print(f"🔑 مسار المفتاح: {key_path}")
    
    # إنشاء المفتاح الخاص
    print("🔑 إنشاء المفتاح الخاص...")
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048,
    )
    
    # إنشاء الشهادة
    print("📜 إنشاء الشهادة...")
    subject = issuer = x509.Name([
        x509.NameAttribute(NameOID.COUNTRY_NAME, "SA"),
        x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Riyadh"),
        x509.NameAttribute(NameOID.LOCALITY_NAME, "Riyadh"),
        x509.NameAttribute(NameOID.ORGANIZATION_NAME, "SAS ERP System"),
        x509.NameAttribute(NameOID.ORGANIZATIONAL_UNIT_NAME, "IT Department"),
        x509.NameAttribute(NameOID.COMMON_NAME, domain),
    ])
    
    # تواريخ الصلاحية (أغسطس 2025)
    now = datetime(2025, 8, 23)  # التاريخ الحالي الصحيح
    valid_from = now
    valid_until = now + timedelta(days=365)  # صالحة حتى أغسطس 2026
    
    print(f"📅 صالحة من: {valid_from}")
    print(f"📅 صالحة حتى: {valid_until}")
    
    cert = x509.CertificateBuilder().subject_name(
        subject
    ).issuer_name(
        issuer
    ).public_key(
        private_key.public_key()
    ).serial_number(
        x509.random_serial_number()
    ).not_valid_before(
        valid_from
    ).not_valid_after(
        valid_until
    ).add_extension(
        x509.SubjectAlternativeName([
            x509.DNSName(domain),
            x509.DNSName(f"*.{domain}"),
            x509.DNSName("localhost"),
            x509.DNSName("127.0.0.1"),
        ]),
        critical=False,
    ).add_extension(
        x509.ExtendedKeyUsage([
            ExtendedKeyUsageOID.SERVER_AUTH,
            ExtendedKeyUsageOID.CLIENT_AUTH,
        ]),
        critical=True,
    ).add_extension(
        x509.KeyUsage(
            digital_signature=True,
            key_encipherment=True,
            key_agreement=False,
            key_cert_sign=False,
            crl_sign=False,
            content_commitment=False,
            data_encipherment=False,
            encipher_only=False,
            decipher_only=False,
        ),
        critical=True,
    ).sign(private_key, hashes.SHA256())
    
    # حفظ المفتاح الخاص
    print("💾 حفظ المفتاح الخاص...")
    with open(key_path, "wb") as f:
        f.write(private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        ))
    
    # حفظ الشهادة
    print("💾 حفظ الشهادة...")
    with open(cert_path, "wb") as f:
        f.write(cert.public_bytes(serialization.Encoding.PEM))
    
    # تحديث الصلاحيات
    try:
        os.chmod(cert_path, 0o644)  # قراءة للجميع
        os.chmod(key_path, 0o600)   # قراءة للمالك فقط
        print("🔒 تم تحديث صلاحيات الملفات")
    except:
        print("⚠️ تعذر تحديث صلاحيات الملفات (Windows)")
    
    print("✅ تم إنشاء شهادة SSL بنجاح!")
    print(f"📄 الشهادة: {cert_path}")
    print(f"🔑 المفتاح: {key_path}")
    print(f"⏰ صالحة حتى: {valid_until.strftime('%Y-%m-%d %H:%M:%S')} UTC")
    
    return cert_path, key_path

def update_server_config(domain, cert_path, key_path):
    """تحديث إعدادات الخادم"""
    import json
    
    config_file = 'config/server_config.json'
    
    # قراءة الإعدادات الحالية
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except:
        config = {}
    
    # تحديث إعدادات SSL
    config.update({
        'domain': domain,
        'ssl_enabled': True,
        'ssl_cert_path': cert_path,
        'ssl_key_path': key_path,
        'auto_ssl': False,
        'updated_at': datetime.now().isoformat()
    })
    
    # حفظ الإعدادات
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print("⚙️ تم تحديث إعدادات الخادم")

if __name__ == "__main__":
    domain = "sas.alfogehi.net"
    
    print("🚀 بدء إنشاء شهادة SSL جديدة...")
    print("=" * 50)
    
    try:
        cert_path, key_path = generate_ssl_certificate(domain)
        update_server_config(domain, cert_path, key_path)
        
        print("=" * 50)
        print("🎉 تم الانتهاء بنجاح!")
        print("📋 الخطوات التالية:")
        print("1. أعد تشغيل الخادم")
        print("2. جرب الوصول للموقع")
        print("3. أضف استثناء أمني في المتصفح")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
