الان قم بإنشاء نافذة عقد جديد حسب التالي
الاستخدام: تستخدم الشاشة في تسجيل بيانات العقود التي يتم توقيعها مع الموردين وربطها مع أوامر الشراء  حتى تصبح الدورة المستندية مكتملة وتحديد تاريخ بداية ونهاية كل عقد، وإمكانية التنبيه الآلي قبل نهاية العقد، مع إمكانية إضافة مبالغ إضافية على مبلغ العقد، وكذلك توفير إمكانية تنفيذ
العقد في فواتير المشتريات على دفعات معينة.
طريقة استخدام الشاشة
تستخدم الشاشة بعد النقر على زر إضافة على النحو التالي:
أولاً: البيانات الرئيسية
- رقم الفرع: يعرض النظام الفروع المحفوظة في جدول الفروع من قاعدة البيانات على هيئة قائمة منسدلة للاختيار منها.
- رقم العقد: يستخدم هذا الحقل للتعامل مع الرقم التسلسلي للعقد، ويستخدم بحسب تهيئة تسلسل الوثائق التي تمت في شاشة متغيرات الموردين وما إذا كان تسلسل آلي يمكن تعديله أو تسلسل آلي لا يمكن تعديله أو إدخال يدوي، ويعرض الحقل أيضاً رقم العقد الذي تم تجديده في شاشة تجديد العقود.
- التاريخ: يستخدم هذا الحقل للتعامل مع تاريخ العقد، ويستخدم بحسب تهيئة تاريخ الوثيقة الذي تم في شاشة متغيرات الموردين وما إذا كان التاريخ آلي يمكن تعديله أو آلي لا يمكن تعديله أو إدخال التاريخ يدوي.
- مجدد من عقد: يعرض النظام آلياً في هذا الحقل رقم العقد الأصلي إذا كان العقد الذي يتم استعراضه عبارة عن تجديد لعقد سابق.
- تاريخ بداية العقد: يتم في هذا الحقل إدخال تاريخ بداية العقد.
- تاريخ ذهاية العقد: يتم في هذا الحقل إدخال تاريخ نهاية العقد.
- تاريخ انتهاء التمديد: يظهر في هذا الحقل آلياً تاريخ انتهاء التمديد للعقد الذي يتم في تبويب تمديد الفترة.
- رقم المورد: هذا الحقل  حيث انه عند ضغط F9 سوف تعرض شاشة بحث انيقة لعرض بيانات الموردين الموجودة في جدول الموردين.
-اسم المورد:يعبئ تلقائيا عند اخيتار المورد
- العملة: يستخدم هذا الحقل لاختيار عملة العقد من قائمة العملات إذا كان المورد يستخدم أكثر من عملة مالم يعرض النظام العملة آلياً.
- سعر التحويل: يظهر في هذا الحقل سعر التحويل الخاص بالعملة الأجنبية إلى العملة المحلية مع إمكانية التعديل فيه حسب الحدود المحددة في شاشة بيانات العملات.
- البيان: يتم في هذا الحقل تسجيل البيان التوضيحي للعقد، وهذا الحقل اختياري وقد يكون إجباري عند تفعيل متغير (إدخال البيان إجباري) في شاشة المتغيرات.
- المرجع: يتم في هذا الحقل تسجيل رقم مرجع العقد -رقما أو حرفاً -كأن يكون رقم المستند اليدوي أو رقم ملف وغيرها من البيانات التي يرجع إليها المستخدم عند إصدار العملية أو التي يستفاد منها في عملية البحث، وهذا الحقل اختياري وقد يكون إجباري عند تفعيل متغير (إدخال رقم المرجع إجباري) في
- المبلغ: يُسجل في هذا الحقل قيمة العقد يدوياً بالعملة المحددة في حقل العملة.
-الخصم 
-صافي المبلغ
- مُستخدم من نوع checkbox و يتم التأشير عليه تلقائيا بمجرد انزال العقد في اوامر الشراء و حفظها اي اذا تم انزال بيانات العقد في اوامر الشراء و حفظها يتم التأشير عليه تلقائيا
- حالة العقد من نوع قائمة منسدلة تحتوي على الخيارات "مسودة - معتمد - منفذ جزئيا - منفذ كليا" و اليك تفاصيل كل خيار:
1- الخيار مسودة :في هذة الحالة يعتبر العقد غير ساري و لا يظهر او يمكن انزاله في اوامر الشراء حتى يتم تغيير الحاله.
2-الخيار معتمد :في هذة الحالة يعتبر العقد ساري المفعول و يمكن انزالة و استخدامة في اوامر الشراء. مع ملاحظة ان الخيار الاول"مسودة" و الخيار الثاني "معتمد" يتم تعديلة يدويا من قبل المستخدم.
3-الخيار منفذ جزئيا :هذة الخيار يتم تعديله تلقائيا بواسطة النظام و يتم تفعيل هذا الخيار اذا تم انزال جزء من كمية اصناف العقد او صنف من الاصناف دون البقية في اوامر الشراء و يجب فحص الاصناف و تفعيل الحالة عند حفظ امر الشراء.
4-الخيار منفذ كليا :هذة الخيار يتم تعديله تلقائيا بواسطة النظام و يتم تفعيل هذا الخيار اذا تم انزال جميع الاصناف و جميع الكميات الموجودة في العقد في امر الشراء و تفعيل الحالة عند حفظ امر الشراء.
ثانيًا: البيانات التفصيلية:
هذا القسم سوف تقوم بإنشائه حسب ما هو موضح في ملف Excel_Table_Component_Guide.md بالحرف.