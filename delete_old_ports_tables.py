#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حذف جداول الموانئ القديمة غير المستخدمة
Delete Old Unused Ports Tables
"""

from database_manager import DatabaseManager

def backup_important_data():
    """نسخ احتياطي للبيانات المهمة قبل الحذف"""
    db_manager = DatabaseManager()
    
    try:
        print("💾 إنشاء نسخة احتياطية للبيانات المهمة...")
        
        # نسخ احتياطي لتصنيفات الموانئ
        classifications_data = db_manager.execute_query("""
            SELECT port_code, purpose, usage_count, user_id, created_at, last_used
            FROM port_classifications
            WHERE usage_count > 0
        """)
        
        if classifications_data:
            print(f"📋 تم العثور على {len(classifications_data)} تصنيف للموانئ")
            
            # نقل التصنيفات إلى الجدول الموحد
            for classification in classifications_data:
                try:
                    insert_sql = """
                        INSERT INTO unified_port_classifications 
                        (port_code, purpose, usage_count, user_id, created_at, last_used)
                        VALUES (:port_code, :purpose, :usage_count, :user_id, :created_at, :last_used)
                    """
                    
                    params = {
                        'port_code': classification[0],
                        'purpose': classification[1],
                        'usage_count': classification[2] if classification[2] else 1,
                        'user_id': classification[3],
                        'created_at': classification[4],
                        'last_used': classification[5]
                    }
                    
                    db_manager.execute_update(insert_sql, params)
                    
                except Exception as e:
                    # تجاهل الأخطاء المكررة
                    if "unique constraint" not in str(e).lower():
                        print(f"⚠️ خطأ في نقل التصنيف {classification[0]}: {e}")
            
            print("✅ تم نقل التصنيفات إلى الجدول الموحد")
        else:
            print("ℹ️ لا توجد تصنيفات للنقل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في النسخ الاحتياطي: {e}")
        return False
    
    finally:
        db_manager.close()

def delete_old_tables():
    """حذف الجداول القديمة"""
    db_manager = DatabaseManager()
    
    # قائمة الجداول المراد حذفها
    tables_to_delete = [
        'AI_WORLD_PORTS',
        'PORT_AI_ANALYTICS', 
        'PORT_CLASSIFICATIONS',
        'PORTS',
        'SELECTED_PORTS',
        'USER_SELECTED_PORTS',
        'WORLD_PORTS',
        'WORLD_PORTS_COMPREHENSIVE'
    ]
    
    try:
        print("🗑️ بدء حذف الجداول القديمة...")
        
        deleted_count = 0
        
        for table_name in tables_to_delete:
            try:
                print(f"\n🔄 محاولة حذف جدول: {table_name}")
                
                # التحقق من وجود الجدول أولاً
                check_sql = """
                    SELECT COUNT(*) FROM user_tables 
                    WHERE table_name = :table_name
                """
                
                table_exists = db_manager.execute_query(check_sql, {'table_name': table_name})
                
                if table_exists and table_exists[0][0] > 0:
                    # حذف الجدول مع جميع القيود
                    drop_sql = f"DROP TABLE {table_name} CASCADE CONSTRAINTS"
                    db_manager.execute_update(drop_sql)
                    
                    print(f"✅ تم حذف جدول: {table_name}")
                    deleted_count += 1
                else:
                    print(f"ℹ️ الجدول {table_name} غير موجود")
                    
            except Exception as e:
                print(f"⚠️ خطأ في حذف {table_name}: {e}")
                # الاستمرار في حذف الجداول الأخرى
                continue
        
        print(f"\n🎉 تم حذف {deleted_count} جدول من أصل {len(tables_to_delete)}")
        
        return deleted_count > 0
        
    except Exception as e:
        print(f"❌ خطأ عام في حذف الجداول: {e}")
        return False
    
    finally:
        db_manager.close()

def verify_unified_system():
    """التحقق من سلامة النظام الموحد"""
    db_manager = DatabaseManager()
    
    try:
        print("\n🔍 التحقق من سلامة النظام الموحد...")
        
        # التحقق من جدول الموانئ الموحد
        ports_count = db_manager.execute_query("""
            SELECT COUNT(*) FROM unified_ports WHERE is_active = 1
        """)
        
        print(f"📊 عدد الموانئ في الجدول الموحد: {ports_count[0][0] if ports_count else 0}")
        
        # التحقق من جدول التصنيفات الموحد
        classifications_count = db_manager.execute_query("""
            SELECT COUNT(*) FROM unified_port_classifications
        """)
        
        print(f"📋 عدد التصنيفات في الجدول الموحد: {classifications_count[0][0] if classifications_count else 0}")
        
        # عرض عينة من البيانات
        sample_ports = db_manager.execute_query("""
            SELECT port_code, port_name, country, major_port, popularity_score
            FROM unified_ports 
            WHERE is_active = 1
            ORDER BY popularity_score DESC
            FETCH FIRST 5 ROWS ONLY
        """)
        
        if sample_ports:
            print("\n📄 عينة من الموانئ في النظام الموحد:")
            for port in sample_ports:
                major_text = "رئيسي" if port[3] else "عادي"
                print(f"   • {port[1]} ({port[0]}) - {port[2]} - {major_text} - نقاط: {port[4]}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False
    
    finally:
        db_manager.close()

def cleanup_sequences():
    """تنظيف المتسلسلات (Sequences) المرتبطة بالجداول المحذوفة"""
    db_manager = DatabaseManager()
    
    try:
        print("\n🔄 تنظيف المتسلسلات المرتبطة بالجداول المحذوفة...")
        
        # البحث عن المتسلسلات المرتبطة بالجداول المحذوفة
        sequences_sql = """
            SELECT sequence_name 
            FROM user_sequences 
            WHERE sequence_name LIKE '%PORT%' 
            AND sequence_name NOT LIKE '%UNIFIED%'
        """
        
        sequences = db_manager.execute_query(sequences_sql)
        
        if sequences:
            for seq in sequences:
                try:
                    seq_name = seq[0]
                    drop_seq_sql = f"DROP SEQUENCE {seq_name}"
                    db_manager.execute_update(drop_seq_sql)
                    print(f"✅ تم حذف المتسلسل: {seq_name}")
                except Exception as e:
                    print(f"⚠️ خطأ في حذف المتسلسل {seq_name}: {e}")
        else:
            print("ℹ️ لا توجد متسلسلات للحذف")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف المتسلسلات: {e}")
        return False
    
    finally:
        db_manager.close()

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء عملية حذف الجداول القديمة...")
    print("=" * 60)
    
    # 1. نسخ احتياطي للبيانات المهمة
    print("المرحلة 1: النسخ الاحتياطي")
    if not backup_important_data():
        print("❌ فشل في النسخ الاحتياطي - توقف العملية")
        return False
    
    # 2. التحقق من النظام الموحد قبل الحذف
    print("\nالمرحلة 2: التحقق من النظام الموحد")
    if not verify_unified_system():
        print("❌ مشكلة في النظام الموحد - توقف العملية")
        return False
    
    # 3. حذف الجداول القديمة
    print("\nالمرحلة 3: حذف الجداول القديمة")
    if not delete_old_tables():
        print("⚠️ مشاكل في حذف بعض الجداول")
    
    # 4. تنظيف المتسلسلات
    print("\nالمرحلة 4: تنظيف المتسلسلات")
    cleanup_sequences()
    
    # 5. التحقق النهائي
    print("\nالمرحلة 5: التحقق النهائي")
    verify_unified_system()
    
    print("\n" + "=" * 60)
    print("🎉 انتهت عملية التنظيف!")
    print("\n💡 النتائج:")
    print("✅ تم الاحتفاظ بجدول unified_ports (الجدول الرئيسي)")
    print("✅ تم الاحتفاظ بجدول unified_port_classifications (التصنيفات)")
    print("🗑️ تم حذف الجداول القديمة غير المستخدمة")
    print("🧹 تم تنظيف المتسلسلات")
    print("\n⚡ فوائد التنظيف:")
    print("• تحسين الأداء")
    print("• تبسيط الكود")
    print("• توفير مساحة التخزين")
    print("• سهولة الصيانة")
    
    return True

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n✨ تم التنظيف بنجاح!")
    else:
        print(f"\n❌ حدثت مشاكل أثناء التنظيف")
    
    print(f"\nيمكنك الآن اختبار النظام للتأكد من عمله بشكل صحيح.")
