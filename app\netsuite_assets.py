# -*- coding: utf-8 -*-
"""
NetSuite Oracle Assets Manager
مدير أصول NetSuite Oracle للتصميم المتطور
"""

from flask import Flask
from flask_assets import Environment, Bundle
import os

class NetSuiteAssetsManager:
    """مدير أصول NetSuite Oracle"""
    
    def __init__(self, app=None):
        self.app = app
        self.assets = None
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app: Flask):
        """تهيئة مدير الأصول مع التطبيق"""
        self.app = app
        self.assets = Environment(app)
        
        # إعداد مجلدات الأصول
        self.assets.directory = app.static_folder
        self.assets.url = app.static_url_path
        
        # تسجيل الحزم
        self._register_css_bundles()
        self._register_js_bundles()
        
        # إعداد البيئة
        self._setup_environment()
    
    def _register_css_bundles(self):
        """تسجيل حزم CSS"""
        
        # NetSuite Core CSS
        netsuite_core_css = Bundle(
            'css/netsuite.css',
            filters='cssmin',
            output='dist/css/netsuite-core.min.css'
        )
        
        # NetSuite Components CSS
        netsuite_components_css = Bundle(
            'css/components/buttons.css',
            'css/components/forms.css',
            'css/components/tables.css',
            'css/components/cards.css',
            'css/components/modals.css',
            'css/components/navigation.css',
            filters='cssmin',
            output='dist/css/netsuite-components.min.css'
        )
        
        # NetSuite Theme CSS
        netsuite_theme_css = Bundle(
            'css/themes/default.css',
            'css/themes/dark.css',
            'css/themes/colors.css',
            filters='cssmin',
            output='dist/css/netsuite-theme.min.css'
        )
        
        # NetSuite Responsive CSS
        netsuite_responsive_css = Bundle(
            'css/responsive/mobile.css',
            'css/responsive/tablet.css',
            'css/responsive/desktop.css',
            filters='cssmin',
            output='dist/css/netsuite-responsive.min.css'
        )
        
        # NetSuite Animation CSS
        netsuite_animation_css = Bundle(
            'css/animations/transitions.css',
            'css/animations/effects.css',
            'css/animations/keyframes.css',
            filters='cssmin',
            output='dist/css/netsuite-animations.min.css'
        )
        
        # تسجيل الحزم
        self.assets.register('netsuite_core_css', netsuite_core_css)
        self.assets.register('netsuite_components_css', netsuite_components_css)
        self.assets.register('netsuite_theme_css', netsuite_theme_css)
        self.assets.register('netsuite_responsive_css', netsuite_responsive_css)
        self.assets.register('netsuite_animation_css', netsuite_animation_css)
        
        # حزمة شاملة
        netsuite_all_css = Bundle(
            netsuite_core_css,
            netsuite_components_css,
            netsuite_theme_css,
            netsuite_responsive_css,
            netsuite_animation_css,
            output='dist/css/netsuite-all.min.css'
        )
        self.assets.register('netsuite_all_css', netsuite_all_css)
    
    def _register_js_bundles(self):
        """تسجيل حزم JavaScript"""
        
        # NetSuite Core JS
        netsuite_core_js = Bundle(
            'js/netsuite/core.js',
            'js/netsuite/utils.js',
            'js/netsuite/config.js',
            filters='jsmin',
            output='dist/js/netsuite-core.min.js'
        )
        
        # NetSuite Components JS
        netsuite_components_js = Bundle(
            'js/components/notifications.js',
            'js/components/modals.js',
            'js/components/tables.js',
            'js/components/forms.js',
            'js/components/charts.js',
            filters='jsmin',
            output='dist/js/netsuite-components.min.js'
        )
        
        # NetSuite UI JS
        netsuite_ui_js = Bundle(
            'js/ui/navigation.js',
            'js/ui/sidebar.js',
            'js/ui/theme.js',
            'js/ui/responsive.js',
            filters='jsmin',
            output='dist/js/netsuite-ui.min.js'
        )
        
        # NetSuite Features JS
        netsuite_features_js = Bundle(
            'js/features/search.js',
            'js/features/filters.js',
            'js/features/pagination.js',
            'js/features/export.js',
            filters='jsmin',
            output='dist/js/netsuite-features.min.js'
        )
        
        # تسجيل الحزم
        self.assets.register('netsuite_core_js', netsuite_core_js)
        self.assets.register('netsuite_components_js', netsuite_components_js)
        self.assets.register('netsuite_ui_js', netsuite_ui_js)
        self.assets.register('netsuite_features_js', netsuite_features_js)
        
        # حزمة شاملة
        netsuite_all_js = Bundle(
            netsuite_core_js,
            netsuite_components_js,
            netsuite_ui_js,
            netsuite_features_js,
            output='dist/js/netsuite-all.min.js'
        )
        self.assets.register('netsuite_all_js', netsuite_all_js)
    
    def _setup_environment(self):
        """إعداد بيئة الأصول"""
        
        # إعدادات التطوير
        if self.app.debug:
            self.assets.debug = True
            self.assets.auto_build = True
            self.assets.cache = False
        else:
            # إعدادات الإنتاج
            self.assets.debug = False
            self.assets.auto_build = False
            self.assets.cache = True
            
        # إنشاء المجلدات المطلوبة
        self._create_directories()
    
    def _create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        directories = [
            'static/dist',
            'static/dist/css',
            'static/dist/js',
            'static/css/components',
            'static/css/themes',
            'static/css/responsive',
            'static/css/animations',
            'static/js/netsuite',
            'static/js/components',
            'static/js/ui',
            'static/js/features'
        ]
        
        for directory in directories:
            full_path = os.path.join(self.app.root_path, directory)
            os.makedirs(full_path, exist_ok=True)
    
    def get_css_bundle(self, bundle_name='netsuite_all_css'):
        """الحصول على حزمة CSS"""
        return self.assets[bundle_name]
    
    def get_js_bundle(self, bundle_name='netsuite_all_js'):
        """الحصول على حزمة JavaScript"""
        return self.assets[bundle_name]
    
    def build_all(self):
        """بناء جميع الحزم"""
        try:
            for bundle_name in self.assets:
                bundle = self.assets[bundle_name]
                bundle.build()
            return True
        except Exception as e:
            self.app.logger.error(f"خطأ في بناء الحزم: {e}")
            return False
    
    def clear_cache(self):
        """مسح ذاكرة التخزين المؤقت"""
        try:
            self.assets.cache.clear()
            return True
        except Exception as e:
            self.app.logger.error(f"خطأ في مسح ذاكرة التخزين المؤقت: {e}")
            return False

# إنشاء مثيل عام
netsuite_assets = NetSuiteAssetsManager()

def init_netsuite_assets(app: Flask):
    """تهيئة مدير أصول NetSuite مع التطبيق"""
    netsuite_assets.init_app(app)
    return netsuite_assets

# دوال مساعدة للقوالب
def register_template_helpers(app: Flask):
    """تسجيل دوال مساعدة للقوالب"""
    
    @app.template_global()
    def netsuite_css(bundle_name='netsuite_all_css'):
        """إدراج CSS في القالب"""
        try:
            bundle = netsuite_assets.get_css_bundle(bundle_name)
            return bundle.urls()
        except:
            return ['/static/css/netsuite.css']
    
    @app.template_global()
    def netsuite_js(bundle_name='netsuite_all_js'):
        """إدراج JavaScript في القالب"""
        try:
            bundle = netsuite_assets.get_js_bundle(bundle_name)
            return bundle.urls()
        except:
            return ['/static/js/netsuite.js']
    
    @app.template_global()
    def netsuite_version():
        """إصدار NetSuite"""
        return "2024.1"
    
    @app.template_global()
    def netsuite_theme():
        """سمة NetSuite الحالية"""
        return "oracle-blue"

if __name__ == "__main__":
    # اختبار مدير الأصول
    from flask import Flask
    
    app = Flask(__name__)
    app.debug = True
    
    # تهيئة مدير الأصول
    assets_manager = init_netsuite_assets(app)
    register_template_helpers(app)
    
    print("✅ تم تهيئة مدير أصول NetSuite بنجاح")
    print(f"📁 مجلد الأصول: {assets_manager.assets.directory}")
    print(f"🔗 رابط الأصول: {assets_manager.assets.url}")
    
    # بناء الحزم
    if assets_manager.build_all():
        print("✅ تم بناء جميع الحزم بنجاح")
    else:
        print("❌ فشل في بناء الحزم")
