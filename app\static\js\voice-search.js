/**
 * نظام البحث الصوتي المتقدم
 * Advanced Voice Search System
 * 
 * يدعم اللغة العربية والإنجليزية مع معالجة ذكية للنصوص
 */

class VoiceSearch {
    constructor(options = {}) {
        this.searchInput = options.searchInput;
        this.searchButton = options.searchButton;
        this.onResult = options.onResult || this.defaultOnResult;
        this.onError = options.onError || this.defaultOnError;
        this.onStart = options.onStart || this.defaultOnStart;
        this.onEnd = options.onEnd || this.defaultOnEnd;
        
        // إعدادات التعرف على الصوت
        this.recognition = null;
        this.isListening = false;
        this.language = 'ar-SA'; // اللغة الافتراضية العربية
        
        this.initializeVoiceRecognition();
        this.setupEventListeners();
    }

    /**
     * تهيئة نظام التعرف على الصوت
     */
    initializeVoiceRecognition() {
        // التحقق من دعم المتصفح للبحث الصوتي
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            console.warn('البحث الصوتي غير مدعوم في هذا المتصفح');
            this.hideVoiceButton();
            this.showError('البحث الصوتي غير مدعوم في هذا المتصفح. يرجى استخدام Chrome أو Edge.');
            return;
        }

        // إنشاء كائن التعرف على الصوت
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();

        // إعدادات التعرف على الصوت
        this.recognition.continuous = false;
        this.recognition.interimResults = false;
        this.recognition.lang = this.language;
        this.recognition.maxAlternatives = 1;

        // معالجة النتائج
        this.recognition.onresult = (event) => {
            const result = event.results[0][0].transcript;
            const confidence = event.results[0][0].confidence;
            
            console.log('🎤 نتيجة البحث الصوتي:', result);
            console.log('🎯 مستوى الثقة:', confidence);
            
            // معالجة النص وتنظيفه
            const cleanedText = this.cleanSearchText(result);
            
            // تطبيق النتيجة
            this.onResult(cleanedText, confidence);
        };

        // معالجة الأخطاء
        this.recognition.onerror = (event) => {
            console.error('❌ خطأ في البحث الصوتي:', event.error);
            this.onError(event.error);
        };

        // بداية التسجيل
        this.recognition.onstart = () => {
            console.log('🎤 بدء البحث الصوتي');
            this.isListening = true;
            this.onStart();
        };

        // نهاية التسجيل
        this.recognition.onend = () => {
            console.log('🔇 انتهاء البحث الصوتي');
            this.isListening = false;
            this.onEnd();
        };
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        if (this.searchButton) {
            this.searchButton.addEventListener('click', () => {
                this.toggleVoiceSearch();
            });
        }
    }

    /**
     * تبديل حالة البحث الصوتي
     */
    toggleVoiceSearch() {
        if (!this.recognition) {
            this.showError('البحث الصوتي غير مدعوم في هذا المتصفح');
            return;
        }

        if (this.isListening) {
            this.stopListening();
        } else {
            this.startListening();
        }
    }

    /**
     * بدء الاستماع
     */
    startListening() {
        try {
            this.recognition.start();
        } catch (error) {
            console.error('خطأ في بدء البحث الصوتي:', error);
            this.onError(error.message);
        }
    }

    /**
     * إيقاف الاستماع
     */
    stopListening() {
        if (this.recognition && this.isListening) {
            this.recognition.stop();
        }
    }

    /**
     * تنظيف النص المُدخل صوتياً
     */
    cleanSearchText(text) {
        // إزالة المسافات الزائدة
        let cleaned = text.trim();
        
        // تحويل الأرقام المكتوبة بالعربية إلى أرقام
        const arabicNumbers = {
            'صفر': '0', 'واحد': '1', 'اثنان': '2', 'ثلاثة': '3', 'أربعة': '4',
            'خمسة': '5', 'ستة': '6', 'سبعة': '7', 'ثمانية': '8', 'تسعة': '9',
            'عشرة': '10', 'عشرون': '20', 'ثلاثون': '30', 'أربعون': '40',
            'خمسون': '50', 'ستون': '60', 'سبعون': '70', 'ثمانون': '80', 'تسعون': '90',
            'مائة': '100', 'ألف': '1000'
        };

        Object.keys(arabicNumbers).forEach(word => {
            const regex = new RegExp(word, 'gi');
            cleaned = cleaned.replace(regex, arabicNumbers[word]);
        });

        // معالجة كلمات البحث الشائعة
        const searchKeywords = {
            'ابحث عن': '',
            'أريد': '',
            'أبحث عن': '',
            'اعرض': '',
            'أعرض': '',
            'اعرض لي': '',
            'أعرض لي': '',
            'ابحث': '',
            'بحث': '',
            'search for': '',
            'find': '',
            'show': '',
            'display': ''
        };

        Object.keys(searchKeywords).forEach(keyword => {
            const regex = new RegExp(keyword, 'gi');
            cleaned = cleaned.replace(regex, searchKeywords[keyword]);
        });

        return cleaned.trim();
    }

    /**
     * تغيير اللغة
     */
    setLanguage(lang) {
        this.language = lang;
        if (this.recognition) {
            this.recognition.lang = lang;
        }
    }

    /**
     * إخفاء زر البحث الصوتي
     */
    hideVoiceButton() {
        if (this.searchButton) {
            this.searchButton.style.display = 'none';
        }
    }

    /**
     * عرض رسالة خطأ
     */
    showError(message) {
        // محاولة استخدام نظام الإشعارات الموجود في النظام
        if (typeof showAlert === 'function') {
            showAlert('🎤 ' + message, 'warning');
        } else if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'warning',
                title: 'البحث الصوتي',
                text: message,
                confirmButtonText: 'حسناً'
            });
        } else {
            // استخدام alert كحل أخير
            alert('🎤 ' + message);
        }

        // إضافة تأثير بصري للزر
        if (this.searchButton) {
            this.searchButton.classList.add('error');
            setTimeout(() => {
                this.searchButton.classList.remove('error');
            }, 2000);
        }
    }

    /**
     * الدوال الافتراضية
     */
    defaultOnResult(text, confidence) {
        if (this.searchInput) {
            this.searchInput.value = text;
            // تشغيل حدث input لتفعيل البحث
            this.searchInput.dispatchEvent(new Event('input', { bubbles: true }));
        }
    }

    defaultOnError(error) {
        let errorMessage = 'حدث خطأ في البحث الصوتي';
        
        switch (error) {
            case 'no-speech':
                errorMessage = 'لم يتم رصد أي صوت. حاول مرة أخرى.';
                break;
            case 'audio-capture':
                errorMessage = 'لا يمكن الوصول إلى الميكروفون.';
                break;
            case 'not-allowed':
                errorMessage = 'تم رفض الإذن للوصول إلى الميكروفون.';
                break;
            case 'network':
                errorMessage = 'خطأ في الشبكة. تحقق من الاتصال.';
                break;
        }
        
        this.showError(errorMessage);
    }

    defaultOnStart() {
        if (this.searchButton) {
            this.searchButton.classList.add('listening');
            this.searchButton.innerHTML = '<i class="fas fa-stop text-danger"></i>';
            this.searchButton.title = 'إيقاف البحث الصوتي';
        }
    }

    defaultOnEnd() {
        if (this.searchButton) {
            this.searchButton.classList.remove('listening');
            this.searchButton.innerHTML = '<i class="fas fa-microphone text-primary"></i>';
            this.searchButton.title = 'البحث الصوتي';
        }
    }
}

/**
 * دالة مساعدة لإنشاء زر البحث الصوتي
 */
function createVoiceSearchButton() {
    const button = document.createElement('button');
    button.type = 'button';
    button.className = 'btn btn-outline-primary voice-search-btn';
    button.innerHTML = '<i class="fas fa-microphone text-primary"></i>';
    button.title = 'البحث الصوتي';
    button.setAttribute('aria-label', 'البحث الصوتي');
    
    return button;
}

/**
 * دالة مساعدة لإضافة البحث الصوتي لحقل البحث
 * تتحقق من وجود زر موجود أولاً قبل إنشاء زر جديد
 */
function addVoiceSearchToInput(searchInputId, options = {}) {
    const searchInput = document.getElementById(searchInputId);
    if (!searchInput) {
        console.error('حقل البحث غير موجود:', searchInputId);
        return null;
    }

    // البحث عن زر موجود أولاً
    let voiceButton = searchInput.parentElement.querySelector('.voice-search-btn');

    if (!voiceButton) {
        // إنشاء زر البحث الصوتي إذا لم يكن موجوداً
        voiceButton = createVoiceSearchButton();

        // إضافة الزر بجانب حقل البحث
        const inputGroup = searchInput.closest('.input-group') || searchInput.parentElement;

        if (inputGroup.classList.contains('input-group')) {
            // إذا كان هناك input-group موجود
            inputGroup.appendChild(voiceButton);
        } else {
            // إنشاء input-group جديد
            const newInputGroup = document.createElement('div');
            newInputGroup.className = 'input-group';

            searchInput.parentElement.insertBefore(newInputGroup, searchInput);
            newInputGroup.appendChild(searchInput);
            newInputGroup.appendChild(voiceButton);
        }
    }

    // إنشاء كائن البحث الصوتي
    const voiceSearch = new VoiceSearch({
        searchInput: searchInput,
        searchButton: voiceButton,
        ...options
    });

    return voiceSearch;
}

// تصدير الكلاسات والدوال للاستخدام العام
window.VoiceSearch = VoiceSearch;
window.createVoiceSearchButton = createVoiceSearchButton;
window.addVoiceSearchToInput = addVoiceSearchToInput;
