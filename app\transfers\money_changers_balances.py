#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نظام إدارة أرصدة الصرافين/البنوك
Money Changers/Banks Balances Management System

يستخدم جدول CURRENT_BALANCES لعرض الأرصدة الجارية للصرافين والبنوك
Uses CURRENT_BALANCES table to display current balances for money changers and banks
"""

from flask import render_template, request, jsonify, session
from flask_login import login_required
from . import transfers_bp
from database_manager import DatabaseManager
import logging
from datetime import datetime, timedelta
import traceback

logger = logging.getLogger(__name__)

@transfers_bp.route('/api/money-changers-balances')
@login_required
def get_money_changers_balances_data():
    """API لجلب بيانات أرصدة الصرافين/البنوك من جدول CURRENT_BALANCES"""
    try:
        db_manager = DatabaseManager()
        
        # جلب الإحصائيات العامة
        statistics = get_balances_statistics(db_manager)
        
        # جلب قائمة الصرافين/البنوك مع أرصدتهم
        money_changers = get_money_changers_list(db_manager)
        
        # جلب بيانات الرسوم البيانية
        charts_data = get_charts_data(db_manager)
        
        return jsonify({
            'success': True,
            'statistics': statistics,
            'money_changers': money_changers,
            'charts': charts_data,
            'last_updated': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"خطأ في جلب بيانات أرصدة الصرافين/البنوك: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب البيانات: {str(e)}'
        }), 500

def get_balances_statistics(db_manager):
    """جلب الإحصائيات العامة للأرصدة"""
    try:
        # إحصائيات الأرصدة من جدول CURRENT_BALANCES
        stats_query = """
        SELECT
            COUNT(DISTINCT cb.entity_id) as total_money_changers,
            COALESCE(SUM(cb.current_balance), 0) as total_balance,
            COALESCE(SUM(CASE WHEN cb.current_balance > 0 THEN cb.current_balance ELSE 0 END), 0) as total_positive_balances,
            COALESCE(SUM(CASE WHEN cb.current_balance < 0 THEN ABS(cb.current_balance) ELSE 0 END), 0) as total_negative_balances,
            COUNT(CASE WHEN cb.current_balance > 0 THEN 1 END) as positive_count,
            COUNT(CASE WHEN cb.current_balance < 0 THEN 1 END) as negative_count,
            COUNT(CASE WHEN cb.current_balance = 0 THEN 1 END) as zero_count
        FROM CURRENT_BALANCES cb
        WHERE cb.entity_type_code = 'MONEY_CHANGER'
        AND cb.currency_code IS NOT NULL
        """

        # استعلام للحصول على العملة الرئيسية (الأكثر استخداماً)
        main_currency_query = """
        SELECT cb.currency_code, COUNT(*) as count
        FROM CURRENT_BALANCES cb
        WHERE cb.entity_type_code = 'MONEY_CHANGER'
        AND cb.currency_code IS NOT NULL
        GROUP BY cb.currency_code
        ORDER BY count DESC
        FETCH FIRST 1 ROWS ONLY
        """
        
        result = db_manager.execute_query(stats_query)

        # الحصول على العملة الرئيسية
        main_currency_result = db_manager.execute_query(main_currency_query)
        main_currency = 'USD'  # افتراضي
        if main_currency_result and main_currency_result[0] and main_currency_result[0][0]:
            main_currency = main_currency_result[0][0]

        if result and result[0]:
            stats = result[0]
            return {
                'total_money_changers': int(stats[0]) if stats[0] else 0,
                'total_balance': float(stats[1]) if stats[1] else 0.0,
                'total_positive_balances': float(stats[2]) if stats[2] else 0.0,
                'total_negative_balances': float(stats[3]) if stats[3] else 0.0,
                'positive_count': int(stats[4]) if stats[4] else 0,
                'negative_count': int(stats[5]) if stats[5] else 0,
                'zero_count': int(stats[6]) if stats[6] else 0,
                'main_currency': main_currency  # العملة الرئيسية الفعلية
            }
        else:
            return {
                'total_money_changers': 0,
                'total_balance': 0.0,
                'total_positive_balances': 0.0,
                'total_negative_balances': 0.0,
                'positive_count': 0,
                'negative_count': 0,
                'zero_count': 0,
                'main_currency': main_currency  # استخدام العملة الرئيسية المحددة
            }

    except Exception as e:
        logger.error(f"خطأ في جلب الإحصائيات: {e}")
        return {
            'total_money_changers': 0,
            'total_balance': 0.0,
            'total_positive_balances': 0.0,
            'total_negative_balances': 0.0,
            'positive_count': 0,
            'negative_count': 0,
            'zero_count': 0,
            'main_currency': 'USD'  # افتراضي في حالة الخطأ
        }

def get_money_changers_list(db_manager):
    """جلب قائمة الصرافين/البنوك مع أرصدتهم"""
    try:
        # استعلام للحصول على الصرافين/البنوك مع أرصدتهم من CURRENT_BALANCES
        query = """
        SELECT
            cb.entity_id,
            mcb.name,
            mcb.type as code,
            cb.currency_code,
            cb.current_balance,
            cb.opening_balance,
            cb.debit_amount,
            cb.credit_amount,
            cb.total_transactions_count,
            TO_CHAR(cb.last_transaction_date, 'YYYY-MM-DD') as last_transaction_date,
            cb.last_document_type,
            cb.last_document_number,
            cb.average_days,
            TO_CHAR(cb.updated_at, 'YYYY-MM-DD HH24:MI:SS') as last_updated,
            CASE
                WHEN mcb.is_active = 1 THEN 'نشط'
                WHEN mcb.is_active = 0 THEN 'غير نشط'
                ELSE 'غير محدد'
            END as status
        FROM CURRENT_BALANCES cb
        JOIN MONEY_CHANGERS_BANKS mcb ON cb.entity_id = mcb.id
        WHERE cb.entity_type_code = 'MONEY_CHANGER'
        AND cb.currency_code IS NOT NULL
        ORDER BY cb.current_balance DESC, mcb.name
        """

        results = db_manager.execute_query(query)

        money_changers = []
        if results:
            for row in results:
                money_changer = {
                    'id': int(row[0]) if row[0] else 0,
                    'name': row[1] or 'غير محدد',
                    'code': f"MC{row[0]:03d}" if row[0] else 'غير محدد',  # إنشاء كود تلقائي
                    'currency_code': row[3] or 'USD',
                    'current_balance': float(row[4]) if row[4] else 0.0,
                    'opening_balance': float(row[5]) if row[5] else 0.0,
                    'debit_amount': float(row[6]) if row[6] else 0.0,
                    'credit_amount': float(row[7]) if row[7] else 0.0,
                    'total_transactions_count': int(row[8]) if row[8] else 0,
                    'last_transaction_date': row[9],
                    'last_document_type': row[10],
                    'last_document_number': row[11],
                    'average_days': int(row[12]) if row[12] else 0,
                    'last_updated': row[13],
                    'status': row[14] or 'نشط'
                }
                money_changers.append(money_changer)

        return money_changers

    except Exception as e:
        logger.error(f"خطأ في جلب قائمة الصرافين/البنوك: {e}")
        return []

def get_charts_data(db_manager):
    """جلب بيانات الرسوم البيانية"""
    try:
        # توزيع الأرصدة حسب العملة
        currency_query = """
        SELECT 
            cb.currency_code,
            COALESCE(SUM(cb.current_balance), 0) as total_amount,
            COUNT(cb.entity_id) as count
        FROM CURRENT_BALANCES cb
        WHERE cb.entity_type_code = 'MONEY_CHANGER'
        AND cb.currency_code IS NOT NULL
        AND cb.current_balance != 0
        GROUP BY cb.currency_code
        ORDER BY total_amount DESC
        """
        
        currency_results = db_manager.execute_query(currency_query)
        
        by_currency = []
        if currency_results:
            for row in currency_results:
                by_currency.append({
                    'currency': row[0],
                    'amount': float(row[1]) if row[1] else 0.0,
                    'count': int(row[2]) if row[2] else 0
                })
        
        # اتجاهات الأرصدة (آخر 6 أشهر)
        trends_query = """
        SELECT 
            TO_CHAR(cb.updated_at, 'YYYY-MM') as month,
            COALESCE(SUM(cb.current_balance), 0) as total_balance
        FROM CURRENT_BALANCES cb
        WHERE cb.entity_type_code = 'MONEY_CHANGER'
        AND cb.updated_at >= ADD_MONTHS(SYSDATE, -6)
        GROUP BY TO_CHAR(cb.updated_at, 'YYYY-MM')
        ORDER BY month
        """
        
        trends_results = db_manager.execute_query(trends_query)
        
        trends = []
        if trends_results:
            for row in trends_results:
                month_name = get_arabic_month_name(row[0])
                trends.append({
                    'month': month_name,
                    'balance': float(row[1]) if row[1] else 0.0
                })
        
        # إذا لم توجد بيانات، استخدم بيانات فارغة
        if not by_currency:
            by_currency = []
        if not trends:
            trends = []

        return {
            'by_currency': by_currency,
            'trends': trends
        }

    except Exception as e:
        logger.error(f"خطأ في جلب بيانات الرسوم البيانية: {e}")
        return {
            'by_currency': [],
            'trends': []
        }

def get_arabic_month_name(month_str):
    """تحويل رقم الشهر إلى اسم عربي"""
    try:
        year, month = month_str.split('-')
        month_names = {
            '01': 'يناير', '02': 'فبراير', '03': 'مارس', '04': 'أبريل',
            '05': 'مايو', '06': 'يونيو', '07': 'يوليو', '08': 'أغسطس',
            '09': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
        }
        return month_names.get(month, month_str)
    except:
        return month_str

def get_default_statistics():
    """إحصائيات افتراضية"""
    return {
        'total_money_changers': 15,
        'total_balance': 2500000.00,
        'total_positive_balances': 1800000.00,
        'total_negative_balances': 700000.00,
        'positive_count': 10,
        'negative_count': 3,
        'zero_count': 2,
        'main_currency': 'SAR'
    }

def get_default_money_changers():
    """قائمة صرافين/بنوك افتراضية"""
    return [
        {
            'id': 1,
            'name': 'البنك الأهلي السعودي',
            'code': 'MC001',
            'currency_code': 'SAR',
            'current_balance': 500000.00,
            'opening_balance': 450000.00,
            'debit_amount': 150000.00,
            'credit_amount': 100000.00,
            'total_transactions_count': 125,
            'last_transaction_date': '2025-01-15',
            'last_document_type': 'TRANSFER',
            'last_document_number': 'TRF-2025-001',
            'average_days': 15,
            'last_updated': '2025-01-15 14:30:00',
            'status': 'نشط'
        },
        {
            'id': 2,
            'name': 'شركة الراجحي للصرافة',
            'code': 'MC002',
            'currency_code': 'USD',
            'current_balance': 75000.00,
            'opening_balance': 70000.00,
            'debit_amount': 25000.00,
            'credit_amount': 20000.00,
            'total_transactions_count': 89,
            'last_transaction_date': '2025-01-14',
            'last_document_type': 'EXCHANGE',
            'last_document_number': 'EXC-2025-045',
            'average_days': 12,
            'last_updated': '2025-01-14 16:45:00',
            'status': 'نشط'
        },
        {
            'id': 3,
            'name': 'بنك الرياض',
            'code': 'MC003',
            'currency_code': 'SAR',
            'current_balance': -25000.00,
            'opening_balance': 0.00,
            'debit_amount': 50000.00,
            'credit_amount': 75000.00,
            'total_transactions_count': 67,
            'last_transaction_date': '2025-01-13',
            'last_document_type': 'PAYMENT',
            'last_document_number': 'PAY-2025-123',
            'average_days': 18,
            'last_updated': '2025-01-13 11:20:00',
            'status': 'نشط'
        }
    ]

def get_default_currency_data():
    """بيانات العملات الافتراضية"""
    return [
        {'currency': 'SAR', 'amount': 1500000, 'count': 8},
        {'currency': 'USD', 'amount': 600000, 'count': 4},
        {'currency': 'EUR', 'amount': 300000, 'count': 2},
        {'currency': 'AED', 'amount': 100000, 'count': 1}
    ]

def get_default_trends_data():
    """بيانات الاتجاهات الافتراضية"""
    return [
        {'month': 'نوفمبر', 'balance': 2200000},
        {'month': 'ديسمبر', 'balance': 2350000},
        {'month': 'يناير', 'balance': 2500000}
    ]

# تسجيل النظام
print("✅ تم تحميل نظام إدارة أرصدة الصرافين/البنوك")
