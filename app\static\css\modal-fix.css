/* 
 * إصلاح نهائي ودائم لمشكلة المودالات المظللة
 * Final and permanent fix for grayed-out modal issue
 */

/* إعادة تعيين كامل للمودال */
.modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 1055 !important;
    display: none !important;
    width: 100% !important;
    height: 100% !important;
    overflow-x: hidden !important;
    overflow-y: auto !important;
    outline: 0 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
}

.modal.show {
    display: block !important;
}

.modal-dialog {
    position: relative !important;
    width: auto !important;
    margin: 0.5rem !important;
    pointer-events: none !important;
    z-index: 1056 !important;
}

.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out !important;
    transform: translate(0, -50px) !important;
}

.modal.show .modal-dialog {
    transform: none !important;
}

.modal-content {
    position: relative !important;
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
    pointer-events: auto !important;
    background-color: #fff !important;
    background-clip: padding-box !important;
    border: 1px solid rgba(0, 0, 0, 0.2) !important;
    border-radius: 0.3rem !important;
    outline: 0 !important;
    z-index: 1057 !important;
}

/* إزالة backdrop منفصل فقط */
.modal-backdrop.fade {
    display: none !important;
}

/* السماح لـ header بالظهور */
.modal-header {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 1rem 1.5rem !important;
    border-bottom: 1px solid #dee2e6 !important;
    border-top-left-radius: calc(0.3rem - 1px) !important;
    border-top-right-radius: calc(0.3rem - 1px) !important;
    background-color: inherit !important;
    z-index: 1059 !important;
}

/* التأكد من أن المحتوى قابل للتفاعل */
.modal input,
.modal select,
.modal textarea,
.modal button,
.modal .form-control,
.modal .form-select,
.modal .btn,
.modal .modal-header,
.modal .modal-title,
.modal .modal-body,
.modal .modal-footer,
.modal .btn-close {
    pointer-events: auto !important;
    z-index: 1058 !important;
    display: inherit !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* منع التمرير في الخلفية */
body.modal-open {
    overflow: hidden !important;
    padding-right: 0 !important;
}

/* إصلاح للشاشات الصغيرة */
@media (min-width: 576px) {
    .modal-dialog {
        max-width: 500px !important;
        margin: 1.75rem auto !important;
    }
}

@media (min-width: 992px) {
    .modal-lg {
        max-width: 800px !important;
    }
}

/* إصلاح خاص للشريط الجانبي */
.ns-sidebar {
    z-index: 1040 !important;
}

.ns-nav {
    z-index: 1041 !important;
}

/* إصلاح للقوائم المنسدلة داخل المودال */
.modal .dropdown-menu {
    z-index: 1060 !important;
}

/* إصلاح للتواريخ والوقت */
.modal .datepicker,
.modal .timepicker {
    z-index: 1061 !important;
}
