{% extends "base.html" %}

{% block title %}معاينة أمر التسليم{% endblock %}

{% block extra_css %}
<style>
.preview-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin: 20px 0;
}

.order-header {
    text-align: center;
    border-bottom: 3px solid #007bff;
    padding-bottom: 20px;
    margin-bottom: 30px;
}

.company-logo {
    max-height: 80px;
    margin-bottom: 15px;
}

.order-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 25px;
}

.detail-box {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.detail-label {
    font-weight: bold;
    color: #495057;
    font-size: 0.9em;
    margin-bottom: 5px;
}

.detail-value {
    color: #212529;
    font-size: 1.1em;
}

.whatsapp-preview {
    background: #e7f3ff;
    border: 2px dashed #007bff;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

.whatsapp-message {
    background: #dcf8c6;
    border-radius: 15px;
    padding: 15px;
    margin: 10px 0;
    position: relative;
    max-width: 400px;
}

.whatsapp-message::after {
    content: '';
    position: absolute;
    right: -10px;
    top: 10px;
    width: 0;
    height: 0;
    border-left: 10px solid #dcf8c6;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
}

.send-options {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

@media print {
    .no-print {
        display: none !important;
    }
    
    .preview-container {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان -->
    <div class="row mb-4 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-eye text-primary me-2"></i>
                        معاينة أمر التسليم
                    </h2>
                    <p class="text-muted mb-0">مراجعة أمر التسليم قبل الإرسال</p>
                </div>
                <div>
                    <button class="btn btn-outline-secondary me-2" onclick="window.history.back()">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة
                    </button>
                    <a href="{{ url_for('shipments.delivery_order_viewer', order_id=order.id) }}"
                       class="btn btn-primary me-2" target="_blank">
                        <i class="fas fa-file-pdf me-1"></i>
                        أمر التسليم العربي
                    </a>
                    <a href="{{ url_for('shipments.delivery_order_print', order_id=order.id) }}"
                       class="btn btn-info me-2" target="_blank">
                        <i class="fas fa-print me-1"></i>
                        طباعة مباشرة
                    </a>
                    <a href="{{ url_for('shipments.delivery_order_html', order_id=order.id) }}"
                       class="btn btn-success me-2" target="_blank">
                        <i class="fas fa-eye me-1"></i>
                        معاينة HTML
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- معاينة أمر التسليم -->
    <div class="preview-container">
        <!-- رأس الأمر -->
        <div class="order-header">
            {% if order.branch_logo %}
            <img src="{{ order.branch_logo }}" alt="شعار الفرع" class="company-logo">
            {% else %}
            <img src="{{ url_for('static', filename='images/logo.png') }}" alt="شعار الشركة" class="company-logo">
            {% endif %}
            <h3 class="text-primary mb-2">أمر تسليم للمخلص الجمركي</h3>
            <h4 class="text-secondary">{{ order.order_number }}</h4>
            <p class="text-muted mb-0">تاريخ الإصدار: {{ order.created_date }}</p>
            {% if order.branch_name %}
            <p class="text-info mb-0"><strong>الفرع:</strong> {{ order.branch_name }}</p>
            {% endif %}
        </div>

        <!-- تفاصيل الأمر -->
        <div class="order-details-grid">
            <!-- معلومات الشحنة -->
            <div class="detail-box">
                <div class="detail-label">معلومات الشحنة</div>
                <div class="detail-value">
                    <strong>رقم التتبع:</strong> {{ order.tracking_number }}<br>
                    <strong>رقم الحجز:</strong> {{ order.booking_number or 'غير محدد' }}
                </div>
            </div>

            <!-- معلومات المخلص -->
            <div class="detail-box">
                <div class="detail-label">المخلص الجمركي</div>
                <div class="detail-value">
                    <strong>الاسم:</strong> {{ order.agent_name }}<br>
                    <strong>الشركة:</strong> {{ order.company_name or 'غير محدد' }}
                </div>
            </div>

            <!-- موقع التسليم -->
            <div class="detail-box">
                <div class="detail-label">موقع التسليم</div>
                <div class="detail-value">{{ order.delivery_location }}</div>
            </div>

            <!-- التاريخ والأولوية -->
            <div class="detail-box">
                <div class="detail-label">التاريخ والأولوية</div>
                <div class="detail-value">
                    <strong>التاريخ المتوقع:</strong> {{ order.expected_completion_date }}<br>
                    <strong>الأولوية:</strong> 
                    {% if order.priority == 'urgent' %}
                        <span class="badge bg-danger">عاجل</span>
                    {% elif order.priority == 'high' %}
                        <span class="badge bg-warning">عالي</span>
                    {% elif order.priority == 'normal' %}
                        <span class="badge bg-primary">عادي</span>
                    {% else %}
                        <span class="badge bg-secondary">منخفض</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- التكلفة ومعلومات الاتصال -->
        {% if order.estimated_cost or order.contact_person %}
        <div class="row mb-4">
            {% if order.estimated_cost %}
            <div class="col-md-6">
                <div class="detail-box">
                    <div class="detail-label">التكلفة المقدرة</div>
                    <div class="detail-value">{{ order.estimated_cost }} {{ order.currency }}</div>
                </div>
            </div>
            {% endif %}
            
            {% if order.contact_person %}
            <div class="col-md-6">
                <div class="detail-box">
                    <div class="detail-label">معلومات الاتصال</div>
                    <div class="detail-value">
                        {% if order.contact_person %}<strong>الشخص:</strong> {{ order.contact_person }}<br>{% endif %}
                        {% if order.contact_phone %}<strong>الهاتف:</strong> {{ order.contact_phone }}<br>{% endif %}
                        {% if order.contact_email %}<strong>البريد:</strong> {{ order.contact_email }}{% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
        {% endif %}

        <!-- التعليمات الخاصة -->
        {% if order.special_instructions %}
        <div class="detail-box mb-4">
            <div class="detail-label">تعليمات خاصة</div>
            <div class="detail-value">{{ order.special_instructions }}</div>
        </div>
        {% endif %}
    </div>

    <!-- معاينة رسالة WhatsApp -->
    <div class="whatsapp-preview no-print">
        <h5 class="text-primary mb-3">
            <i class="fab fa-whatsapp me-2"></i>
            معاينة رسالة WhatsApp
        </h5>
        
        <div class="whatsapp-message">
            <div id="whatsappMessageContent">
                🏢 <strong>أمر تسليم جديد</strong><br><br>
                📋 <strong>رقم الأمر:</strong> {{ order.order_number }}<br>
                📦 <strong>رقم التتبع:</strong> {{ order.tracking_number }}<br>
                🚢 <strong>رقم الحجز:</strong> {{ order.booking_number or 'غير محدد' }}<br>
                📍 <strong>موقع التسليم:</strong> {{ order.delivery_location }}<br>
                📅 <strong>التاريخ المتوقع:</strong> {{ order.expected_completion_date }}<br>
                {% if order.estimated_cost %}💰 <strong>التكلفة المقدرة:</strong> {{ order.estimated_cost }} {{ order.currency }}<br>{% endif %}
                {% if order.contact_person %}👤 <strong>الشخص المسؤول:</strong> {{ order.contact_person }}<br>{% endif %}
                {% if order.contact_phone %}📞 <strong>رقم الاتصال:</strong> {{ order.contact_phone }}<br>{% endif %}
                <br>
                📄 <strong>المستند الرسمي:</strong><br>
                {{ request.url_root }}shipments/delivery-order-pdf/{{ order.id }}<br>
                <br>
                🔗 <strong>رابط التفاصيل الكاملة:</strong><br>
                {{ request.url_root }}shipments/delivery-order-preview/{{ order.id }}<br>
                <br>
                يرجى مراجعة المستند الرسمي والرد بالموافقة.<br>
                <br>
                ---<br>
                🏢 شركة النقل والشحن المتطورة<br>
                📧 تم الإرسال تلقائياً من نظام إدارة الشحنات
            </div>
        </div>
    </div>

    <!-- خيارات الإرسال -->
    <div class="send-options no-print">
        <h5 class="text-warning mb-3">
            <i class="fas fa-paper-plane me-2"></i>
            خيارات الإرسال
        </h5>
        
        <div class="row">
            <div class="col-md-6 mb-3">
                <label class="form-label">رقم WhatsApp للمخلص</label>
                <input type="tel" class="form-control" id="whatsappNumber" 
                       value="{{ agent_phone or '' }}" 
                       placeholder="مثال: +966501234567">
                <small class="text-muted">يجب أن يبدأ الرقم بـ + ورمز الدولة</small>
            </div>
            
            <div class="col-md-6 mb-3">
                <label class="form-label">طريقة الإرسال</label>
                <select class="form-select" id="sendMethod">
                    <option value="whatsapp_web">WhatsApp Web (فتح في المتصفح)</option>
                    <option value="whatsapp_api">WhatsApp Business API</option>
                    <option value="copy_message">نسخ الرسالة فقط</option>
                </select>
            </div>
        </div>

        <div class="d-flex gap-2 mt-3">
            <button class="btn btn-success" onclick="sendViaWhatsApp()">
                <i class="fab fa-whatsapp me-1"></i>
                إرسال عبر WhatsApp
            </button>
            
            <button class="btn btn-info" onclick="copyMessage()">
                <i class="fas fa-copy me-1"></i>
                نسخ الرسالة
            </button>
            
            <button class="btn btn-primary" onclick="markAsSent()">
                <i class="fas fa-check me-1"></i>
                تأكيد الإرسال
            </button>
        </div>
    </div>
</div>

<script>
// طباعة الأمر
function printOrder() {
    window.print();
}

// إرسال عبر WhatsApp
function sendViaWhatsApp() {
    const phone = document.getElementById('whatsappNumber').value.trim();
    const method = document.getElementById('sendMethod').value;
    const message = document.getElementById('whatsappMessageContent').innerText;
    
    if (!phone) {
        alert('يرجى إدخال رقم WhatsApp');
        return;
    }
    
    // تنظيف رقم الهاتف
    const cleanPhone = phone.replace(/[^\d+]/g, '');
    
    if (method === 'whatsapp_web') {
        // فتح WhatsApp Web
        const encodedMessage = encodeURIComponent(message);
        const whatsappUrl = `https://wa.me/${cleanPhone}?text=${encodedMessage}`;
        window.open(whatsappUrl, '_blank');
        
        // تأكيد الإرسال بعد 3 ثوان
        setTimeout(() => {
            if (confirm('هل تم إرسال الرسالة بنجاح؟')) {
                markAsSent();
            }
        }, 3000);
        
    } else if (method === 'whatsapp_api') {
        // إرسال عبر API (يتطلب تطوير إضافي)
        sendViaAPI(cleanPhone, message);
        
    } else if (method === 'copy_message') {
        copyMessage();
    }
}

// نسخ الرسالة
function copyMessage() {
    const message = document.getElementById('whatsappMessageContent').innerText;
    navigator.clipboard.writeText(message).then(() => {
        alert('تم نسخ الرسالة! يمكنك الآن لصقها في WhatsApp');
    }).catch(() => {
        // fallback للمتصفحات القديمة
        const textArea = document.createElement('textarea');
        textArea.value = message;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('تم نسخ الرسالة!');
    });
}

// تأكيد الإرسال
function markAsSent() {
    if (confirm('هل تم إرسال أمر التسليم بنجاح؟')) {
        // تحديث حالة الأمر إلى "sent"
        fetch(`/shipments/api/delivery-orders/{{ order.id }}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                order_status: 'sent',
                sent_via: 'whatsapp',
                sent_to: document.getElementById('whatsappNumber').value
            })
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert('تم تأكيد إرسال أمر التسليم بنجاح!');
                window.location.href = '/shipments/delivery-orders-dashboard';
            } else {
                alert('خطأ في تأكيد الإرسال: ' + result.message);
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('حدث خطأ في تأكيد الإرسال');
        });
    }
}

// إرسال عبر WhatsApp Business API
function sendViaAPI(phone, message) {
    console.log('📡 إرسال عبر WhatsApp Business API...');

    // إظهار مؤشر التحميل
    const sendBtn = document.querySelector('button[onclick="sendViaWhatsApp()"]');
    const originalText = sendBtn.innerHTML;
    sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الإرسال...';
    sendBtn.disabled = true;

    // إرسال الطلب للـ API
    fetch(`/shipments/api/delivery-orders/{{ order.id }}/send-whatsapp`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            phone: phone
        })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            // إظهار رسالة نجاح مع تفاصيل الإرسال
            let successMessage = `✅ تم إرسال أمر التسليم بنجاح عبر WhatsApp!\\n\\n`;
            successMessage += `📱 تم الإرسال إلى: ${result.phone}\\n`;

            if (result.message_id) {
                successMessage += `🆔 معرف الرسالة: ${result.message_id}\\n`;
            }

            successMessage += `\\n${result.message}`;

            alert(successMessage);

            // الانتقال إلى لوحة أوامر التسليم بعد 2 ثانية
            setTimeout(() => {
                window.location.href = '/shipments/delivery-orders-dashboard';
            }, 2000);

        } else {
            alert(`❌ فشل في إرسال الرسالة:\\n\\n${result.message}`);
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الشبكة:', error);
        alert('حدث خطأ في الاتصال بالخادم');
    })
    .finally(() => {
        // إعادة تعيين الزر
        sendBtn.innerHTML = originalText;
        sendBtn.disabled = false;
    });
}
</script>
{% endblock %}
