{% extends "base.html" %}

{% block title %}النظام الذكي لشركات الشحن{% endblock %}

{% block head %}
<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-robot me-2"></i>
                        النظام الذكي لشركات الشحن
                    </h2>
                    <p class="text-muted mb-0">اكتشف وتتبع شحناتك مع أكثر من 50 شركة شحن عالمية باستخدام الذكاء الاصطناعي</p>
                </div>
                <div>
                    <a href="{{ url_for('shipments.smart_shipping_data_view') }}" class="btn btn-info me-2">
                        <i class="fas fa-database me-1"></i>
                        عرض البيانات المحفوظة
                    </a>
                    <button class="btn btn-primary" onclick="refreshData()">
                        <i class="fas fa-sync-alt me-1"></i>
                        تحديث البيانات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="info-card bg-primary">
                <div class="info-icon">
                    <i class="fas fa-ship"></i>
                </div>
                <div class="info-details">
                    <h3 id="totalCompanies">0</h3>
                    <p>شركة شحن</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="info-card bg-success">
                <div class="info-icon">
                    <i class="fas fa-globe"></i>
                </div>
                <div class="info-details">
                    <h3>100+</h3>
                    <p>دولة مخدومة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="info-card bg-warning">
                <div class="info-icon">
                    <i class="fas fa-containers"></i>
                </div>
                <div class="info-details">
                    <h3 id="totalContainers">0</h3>
                    <p>حاوية متتبعة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="info-card bg-info">
                <div class="info-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="info-details">
                    <h3>24/7</h3>
                    <p>تتبع مستمر</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تتبع الحاوية بالذكاء الاصطناعي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-search me-2"></i>
                        تتبع الحاوية بالذكاء الاصطناعي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الحاوية</label>
                            <input type="text" class="form-control" id="containerInput" 
                                   placeholder="أدخل رقم الحاوية (مثل: MAEU1234567)">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">ربط بشحنة موجودة</label>
                            <select class="form-select" id="shipmentSelect">
                                <option value="">اختر شحنة (اختياري)</option>
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button class="btn btn-primary w-100" onclick="trackContainer()">
                                    <i class="fas fa-search me-1"></i>
                                    تتبع الآن
                                </button>
                            </div>
                        </div>
                    </div>
                    <div id="trackingResult" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- البحث والفلترة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        البحث والفلترة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">البحث في الشركات</label>
                            <input type="text" class="form-control" id="searchInput" 
                                   placeholder="ابحث بالاسم أو الكود...">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">المنطقة</label>
                            <select class="form-select" id="regionFilter">
                                <option value="">جميع المناطق</option>
                                <option value="Global">عالمية</option>
                                <option value="Asia">آسيا</option>
                                <option value="Middle East">الشرق الأوسط</option>
                                <option value="Mediterranean">البحر المتوسط</option>
                                <option value="Pacific">المحيط الهادئ</option>
                                <option value="US">الولايات المتحدة</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">نوع الحاوية</label>
                            <select class="form-select" id="containerTypeFilter">
                                <option value="">جميع الأنواع</option>
                                <option value="20GP">20 قدم عادية</option>
                                <option value="40GP">40 قدم عادية</option>
                                <option value="40HC">40 قدم عالية</option>
                                <option value="45HC">45 قدم عالية</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button class="btn btn-secondary w-100" onclick="clearFilters()">
                                    <i class="fas fa-times me-1"></i>
                                    مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- التحليلات والإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-6 mb-3">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        توزيع الشركات حسب المنطقة
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="regionsChart" width="400" height="200"></canvas>
                    <div id="regionsChartFallback" style="display: none;" class="text-center text-muted py-4">
                        <i class="fas fa-chart-pie fa-2x mb-2"></i>
                        <p>الرسم البياني غير متاح حالياً</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-3">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        أنواع الحاويات المدعومة
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="containerTypesChart" width="400" height="200"></canvas>
                    <div id="containerTypesChartFallback" style="display: none;" class="text-center text-muted py-4">
                        <i class="fas fa-chart-bar fa-2x mb-2"></i>
                        <p>الرسم البياني غير متاح حالياً</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة شركات الشحن -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        شركات الشحن المتاحة
                        <span class="badge bg-secondary ms-2" id="companiesCount">0</span>
                    </h5>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleView('grid')" id="gridViewBtn">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleView('list')" id="listViewBtn">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="companiesContainer">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الشحنات المرتبطة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-link me-2"></i>
                        الشحنات المرتبطة بشركات الشحن
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الشحنة</th>
                                    <th>شركة الشحن</th>
                                    <th>رقم الحاوية</th>
                                    <th>الحالة</th>
                                    <th>آخر تحديث</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="linkedShipmentsTable">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-card {
    background: linear-gradient(135deg, var(--bs-primary), var(--bs-primary-dark, #0056b3));
    color: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-card.bg-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.info-card.bg-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.info-card.bg-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
}

.info-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.info-details h3 {
    margin: 0;
    font-weight: 700;
    font-size: 2rem;
}

.info-details p {
    margin: 0;
    opacity: 0.9;
}

.company-card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    margin-bottom: 1rem;
    overflow: hidden;
}

.company-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.company-header {
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
}

.company-body {
    padding: 1rem;
}

.ai-badge {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    float: left;
}

.region-badge {
    background: #28a745;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.75rem;
    font-size: 0.7rem;
    margin: 0.125rem;
    display: inline-block;
}

.container-type-badge {
    background: #6c757d;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.75rem;
    font-size: 0.7rem;
    margin: 0.125rem;
    display: inline-block;
}

.supported-formats {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 0.25rem;
    padding: 0.5rem;
    font-family: monospace;
    font-size: 0.8rem;
    color: #1976d2;
}

.view-grid .company-card {
    height: 100%;
}

.view-list .company-card {
    display: flex;
    align-items: center;
}

.view-list .company-header {
    flex: 0 0 200px;
}

.view-list .company-body {
    flex: 1;
}
</style>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
let allCompanies = [];
let filteredCompanies = [];
let currentView = 'grid';
let regionsChart = null;
let containerTypesChart = null;

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تحميل النظام الذكي لشركات الشحن...');
    loadInitialData();
});

// تحميل البيانات الأولية
function loadInitialData() {
    Promise.all([
        loadShippingCompanies(),
        loadShipments(),
        loadLinkedShipments()
    ]).then(() => {
        console.log('✅ تم تحميل جميع البيانات');

        // تأخير قصير لضمان تحميل Chart.js
        setTimeout(() => {
            updateCharts();
        }, 500);
    }).catch(error => {
        console.error('❌ خطأ في تحميل البيانات:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ في التحميل',
            text: 'حدث خطأ في تحميل البيانات الأولية'
        });
    });
}

// تحميل شركات الشحن
function loadShippingCompanies() {
    return fetch('/shipments/api/shipping-companies')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.companies) {
                allCompanies = data.companies;
                filteredCompanies = allCompanies;
                displayCompanies();
                updateStats();
                console.log(`✅ تم تحميل ${allCompanies.length} شركة شحن`);
            } else {
                throw new Error(data.message || 'فشل في تحميل شركات الشحن');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل شركات الشحن:', error);
            // استخدام بيانات تجريبية في حالة الخطأ
            allCompanies = [
                {
                    "id": "MAEU", "name": "Maersk Line", "name_ar": "خط مايرسك",
                    "code": "MAEU", "website": "https://www.maersk.com",
                    "tracking_url": "https://www.maersk.com/tracking/{container}",
                    "supported_formats": ["MAEU", "MSKU"],
                    "regions": ["Global"], "container_types": ["20GP", "40GP", "40HC", "45HC"]
                },
                {
                    "id": "MSCU", "name": "MSC Mediterranean", "name_ar": "شركة الشحن المتوسطية",
                    "code": "MSCU", "website": "https://www.msc.com",
                    "tracking_url": "https://www.msc.com/track/{container}",
                    "supported_formats": ["MSCU", "MEDU"],
                    "regions": ["Global"], "container_types": ["20GP", "40GP", "40HC"]
                },
                {
                    "id": "CMAU", "name": "CMA CGM", "name_ar": "سي إم إيه سي جي إم",
                    "code": "CMAU", "website": "https://www.cma-cgm.com",
                    "tracking_url": "https://www.cma-cgm.com/tracking/{container}",
                    "supported_formats": ["CMAU", "CGMU"],
                    "regions": ["Global"], "container_types": ["20GP", "40GP", "40HC"]
                }
            ];
            filteredCompanies = allCompanies;
            displayCompanies();
            updateStats();
            console.log('🔄 تم استخدام البيانات التجريبية');
        });
}

// تحميل الشحنات
function loadShipments() {
    return fetch('/shipments/api/shipments-list')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.shipments) {
                populateShipmentsSelect(data.shipments);
                console.log(`✅ تم تحميل ${data.shipments.length} شحنة`);
            }
        })
        .catch(error => {
            console.warn('⚠️ لم يتم تحميل قائمة الشحنات:', error);
            // إضافة بيانات تجريبية
            populateShipmentsSelect([
                {id: 1, shipment_number: 'CRG20250809013251', shipping_company: 'Maersk Line'},
                {id: 2, shipment_number: 'CRG20250810174544', shipping_company: null},
                {id: 3, shipment_number: 'CRG20250810174740', shipping_company: 'MSC Mediterranean'}
            ]);
        });
}

// تحميل الشحنات المرتبطة
function loadLinkedShipments() {
    return fetch('/shipments/api/linked-shipments')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.shipments) {
                displayLinkedShipments(data.shipments);
                console.log(`✅ تم تحميل ${data.shipments.length} شحنة مرتبطة`);
            }
        })
        .catch(error => {
            console.warn('⚠️ لم يتم تحميل الشحنات المرتبطة:', error);
            // إضافة بيانات تجريبية
            displayLinkedShipments([
                {
                    id: 1,
                    shipment_number: 'CRG20250809013251',
                    shipping_company: 'Maersk Line',
                    container_number: 'MAEU1234567',
                    status: 'confirmed',
                    status_name: 'مؤكدة',
                    status_color: '#28a745',
                    tracking_url: 'https://www.maersk.com/tracking/MAEU1234567',
                    updated_at: '2025-01-11 15:30'
                }
            ]);
        });
}

// عرض شركات الشحن
function displayCompanies() {
    const container = document.getElementById('companiesContainer');
    
    if (filteredCompanies.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5>لا توجد نتائج</h5>
                <p class="text-muted">لم يتم العثور على شركات شحن تطابق معايير البحث</p>
            </div>
        `;
        return;
    }
    
    if (currentView === 'grid') {
        container.className = 'row view-grid';
        container.innerHTML = filteredCompanies.map(company => `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="company-card">
                    <div class="company-header">
                        <div class="ai-badge">
                            <i class="fas fa-robot me-1"></i>AI
                        </div>
                        <h6 class="mb-1">${company.name}</h6>
                        <small class="text-muted">${company.name_ar}</small>
                        <div class="mt-2">
                            <span class="badge bg-primary">${company.code}</span>
                        </div>
                    </div>
                    <div class="company-body">
                        <div class="mb-2">
                            <small class="text-muted">المناطق:</small><br>
                            ${company.regions.map(region => `<span class="region-badge">${region}</span>`).join('')}
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">أنواع الحاويات:</small><br>
                            ${company.container_types.map(type => `<span class="container-type-badge">${type}</span>`).join('')}
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">الأكواد المدعومة:</small>
                            <div class="supported-formats">${company.supported_formats.join(' • ')}</div>
                        </div>
                        <div class="d-grid gap-2">
                            <a href="${company.website}" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-globe me-1"></i>الموقع الرسمي
                            </a>
                            <button class="btn btn-primary btn-sm" onclick="selectCompany('${company.code}')">
                                <i class="fas fa-check me-1"></i>اختيار الشركة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    } else {
        container.className = 'view-list';
        container.innerHTML = filteredCompanies.map(company => `
            <div class="company-card mb-2">
                <div class="company-header">
                    <div class="ai-badge">AI</div>
                    <h6 class="mb-0">${company.name}</h6>
                    <small class="text-muted">${company.name_ar}</small>
                    <div class="mt-1">
                        <span class="badge bg-primary">${company.code}</span>
                    </div>
                </div>
                <div class="company-body">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <small class="text-muted">المناطق:</small>
                            ${company.regions.map(region => `<span class="region-badge">${region}</span>`).join('')}
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">الحاويات:</small>
                            ${company.container_types.map(type => `<span class="container-type-badge">${type}</span>`).join('')}
                        </div>
                        <div class="col-md-4">
                            <small class="text-muted">الأكواد:</small>
                            <div class="supported-formats">${company.supported_formats.join(' • ')}</div>
                        </div>
                        <div class="col-md-2">
                            <div class="btn-group w-100">
                                <a href="${company.website}" target="_blank" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-globe"></i>
                                </a>
                                <button class="btn btn-primary btn-sm" onclick="selectCompany('${company.code}')">
                                    <i class="fas fa-check"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }
}

// باقي الوظائف...
function trackContainer() {
    const containerNumber = document.getElementById('containerInput').value.trim();
    const shipmentId = document.getElementById('shipmentSelect').value;
    
    if (!containerNumber) {
        Swal.fire({
            icon: 'warning',
            title: 'تنبيه',
            text: 'يرجى إدخال رقم الحاوية'
        });
        return;
    }
    
    Swal.fire({
        title: 'جاري التحليل...',
        text: 'يرجى الانتظار بينما نحلل رقم الحاوية بالذكاء الاصطناعي',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
    
    fetch('/shipments/api/identify-shipping-company', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            container_number: containerNumber,
            shipment_id: shipmentId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.company) {
            const company = data.company;
            const confidence = Math.round(data.confidence * 100);
            
            Swal.fire({
                icon: 'success',
                title: 'تم التعرف على الشركة!',
                html: `
                    <div class="text-start">
                        <p><strong>الشركة:</strong> ${company.name} (${company.name_ar})</p>
                        <p><strong>الكود:</strong> ${company.code}</p>
                        <p><strong>دقة الذكاء الاصطناعي:</strong> ${confidence}%</p>
                        ${shipmentId ? '<p><strong>تم ربط الحاوية بالشحنة بنجاح</strong></p>' : ''}
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'تتبع الآن',
                cancelButtonText: 'إغلاق',
                confirmButtonColor: '#28a745'
            }).then((result) => {
                if (result.isConfirmed) {
                    const trackingUrl = company.tracking_url.replace('{container}', containerNumber);
                    window.open(trackingUrl, '_blank');
                }
            });
            
            // تحديث الشحنات المرتبطة
            if (shipmentId) {
                loadLinkedShipments();
            }
        } else {
            Swal.fire({
                icon: 'warning',
                title: 'لم يتم التعرف على الشركة',
                text: 'لم يتم التعرف على شركة الشحن. يرجى التحقق من رقم الحاوية.'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ في الاتصال',
            text: 'حدث خطأ في الاتصال بالخادم'
        });
    });
}

// تحديث الإحصائيات
function updateStats() {
    document.getElementById('totalCompanies').textContent = allCompanies.length;
    document.getElementById('companiesCount').textContent = filteredCompanies.length;
    
    // حساب إجمالي الحاويات المتتبعة (يمكن تحديثه من قاعدة البيانات)
    document.getElementById('totalContainers').textContent = '0'; // سيتم تحديثه لاحقاً
}

// تبديل العرض
function toggleView(view) {
    currentView = view;
    
    document.getElementById('gridViewBtn').classList.toggle('active', view === 'grid');
    document.getElementById('listViewBtn').classList.toggle('active', view === 'list');
    
    displayCompanies();
}

// تحديث البيانات
function refreshData() {
    loadInitialData();
}

// ملء قائمة الشحنات
function populateShipmentsSelect(shipments) {
    const select = document.getElementById('shipmentSelect');
    select.innerHTML = '<option value="">اختر شحنة (اختياري)</option>';

    shipments.forEach(shipment => {
        const option = document.createElement('option');
        option.value = shipment.id;
        option.textContent = `${shipment.shipment_number} - ${shipment.shipping_company || 'غير محدد'}`;
        select.appendChild(option);
    });
}

// عرض الشحنات المرتبطة
function displayLinkedShipments(shipments) {
    const tbody = document.getElementById('linkedShipmentsTable');

    if (shipments.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-muted py-4">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد شحنات مرتبطة بشركات الشحن حتى الآن
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = shipments.map(shipment => `
        <tr>
            <td>
                <a href="/shipments/cargo/edit/${shipment.id}" class="text-decoration-none">
                    ${shipment.shipment_number}
                </a>
            </td>
            <td>${shipment.shipping_company}</td>
            <td>
                <code class="bg-light px-2 py-1 rounded">${shipment.container_number}</code>
            </td>
            <td>
                <span class="badge" style="background-color: ${shipment.status_color};">
                    ${shipment.status_name || shipment.status}
                </span>
            </td>
            <td>
                <small class="text-muted">${shipment.updated_at}</small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <a href="/shipments/cargo/edit/${shipment.id}" class="btn btn-outline-primary btn-sm" title="عرض/تعديل الشحنة">
                        <i class="fas fa-eye"></i>
                    </a>
                    ${shipment.tracking_url ?
                        `<a href="${shipment.tracking_url}" target="_blank" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-external-link-alt"></i>
                        </a>` : ''
                    }
                </div>
            </td>
        </tr>
    `).join('');
}

// اختيار شركة
function selectCompany(companyCode) {
    const company = allCompanies.find(c => c.code === companyCode);
    if (!company) return;

    Swal.fire({
        title: `${company.name}`,
        html: `
            <div class="text-start">
                <p><strong>الاسم العربي:</strong> ${company.name_ar}</p>
                <p><strong>الكود:</strong> ${company.code}</p>
                <p><strong>المناطق:</strong> ${company.regions.join(', ')}</p>
                <p><strong>أنواع الحاويات:</strong> ${company.container_types.join(', ')}</p>
                <p><strong>الأكواد المدعومة:</strong> ${company.supported_formats.join(', ')}</p>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'زيارة الموقع',
        cancelButtonText: 'إغلاق',
        confirmButtonColor: '#007bff'
    }).then((result) => {
        if (result.isConfirmed) {
            window.open(company.website, '_blank');
        }
    });
}

// البحث والفلترة
function setupFilters() {
    document.getElementById('searchInput').addEventListener('input', debounce(filterCompanies, 300));
    document.getElementById('regionFilter').addEventListener('change', filterCompanies);
    document.getElementById('containerTypeFilter').addEventListener('change', filterCompanies);
}

function filterCompanies() {
    const searchQuery = document.getElementById('searchInput').value.toLowerCase();
    const regionFilter = document.getElementById('regionFilter').value;
    const containerTypeFilter = document.getElementById('containerTypeFilter').value;

    filteredCompanies = allCompanies.filter(company => {
        const matchesSearch = !searchQuery ||
            company.name.toLowerCase().includes(searchQuery) ||
            company.name_ar.includes(searchQuery) ||
            company.code.toLowerCase().includes(searchQuery);

        const matchesRegion = !regionFilter ||
            company.regions.includes(regionFilter);

        const matchesContainerType = !containerTypeFilter ||
            company.container_types.includes(containerTypeFilter);

        return matchesSearch && matchesRegion && matchesContainerType;
    });

    displayCompanies();
    updateStats();
}

function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('regionFilter').value = '';
    document.getElementById('containerTypeFilter').value = '';
    filterCompanies();
}

// تحديث الرسوم البيانية
function updateCharts() {
    // التحقق من تحميل Chart.js
    if (typeof Chart === 'undefined') {
        console.warn('⚠️ Chart.js غير محمل - سيتم تخطي الرسوم البيانية');
        return;
    }

    updateRegionsChart();
    updateContainerTypesChart();
}

function updateRegionsChart() {
    // التحقق من وجود Chart.js والعنصر
    if (typeof Chart === 'undefined') {
        console.warn('⚠️ Chart.js غير متاح');
        document.getElementById('regionsChart').style.display = 'none';
        document.getElementById('regionsChartFallback').style.display = 'block';
        return;
    }

    const chartElement = document.getElementById('regionsChart');
    if (!chartElement) {
        console.warn('⚠️ عنصر regionsChart غير موجود');
        return;
    }

    // إظهار الرسم البياني وإخفاء fallback
    chartElement.style.display = 'block';
    document.getElementById('regionsChartFallback').style.display = 'none';

    const ctx = chartElement.getContext('2d');

    // حساب توزيع المناطق
    const regionCounts = {};
    allCompanies.forEach(company => {
        company.regions.forEach(region => {
            regionCounts[region] = (regionCounts[region] || 0) + 1;
        });
    });

    if (regionsChart) {
        regionsChart.destroy();
    }

    regionsChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(regionCounts),
            datasets: [{
                data: Object.values(regionCounts),
                backgroundColor: [
                    '#007bff', '#28a745', '#ffc107', '#dc3545',
                    '#6f42c1', '#fd7e14', '#20c997', '#6c757d'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function updateContainerTypesChart() {
    // التحقق من وجود Chart.js والعنصر
    if (typeof Chart === 'undefined') {
        console.warn('⚠️ Chart.js غير متاح');
        document.getElementById('containerTypesChart').style.display = 'none';
        document.getElementById('containerTypesChartFallback').style.display = 'block';
        return;
    }

    const chartElement = document.getElementById('containerTypesChart');
    if (!chartElement) {
        console.warn('⚠️ عنصر containerTypesChart غير موجود');
        return;
    }

    // إظهار الرسم البياني وإخفاء fallback
    chartElement.style.display = 'block';
    document.getElementById('containerTypesChartFallback').style.display = 'none';

    const ctx = chartElement.getContext('2d');

    // حساب أنواع الحاويات
    const typeCounts = {};
    allCompanies.forEach(company => {
        company.container_types.forEach(type => {
            typeCounts[type] = (typeCounts[type] || 0) + 1;
        });
    });

    if (containerTypesChart) {
        containerTypesChart.destroy();
    }

    containerTypesChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: Object.keys(typeCounts),
            datasets: [{
                label: 'عدد الشركات',
                data: Object.values(typeCounts),
                backgroundColor: '#007bff',
                borderColor: '#0056b3',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

// دالة debounce للبحث
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تهيئة الفلاتر عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setupFilters();

    // تفعيل العرض الشبكي افتراضياً
    document.getElementById('gridViewBtn').classList.add('active');
});
</script>
{% endblock %}
