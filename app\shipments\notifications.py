"""
نظام الإشعارات المتقدم للشحنات
Advanced Notification System for Shipments
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from flask import current_app

# استيرادات اختيارية
try:
    import asyncio
except ImportError:
    asyncio = None

try:
    from celery import Celery
except ImportError:
    Celery = None

try:
    import websockets
except ImportError:
    websockets = None

# استيرادات محلية معطلة مؤقتاً لأننا نستخدم Oracle
# from .models import Shipment, ShipmentNotification, TrackingEvent
# from app import db

from .utils import send_notification
from database_manager import DatabaseManager

class NotificationManager:
    """مدير الإشعارات المتقدم"""
    
    def __init__(self):
        self.websocket_clients = set()
        self.notification_templates = self.load_notification_templates()
    
    def load_notification_templates(self):
        """تحميل قوالب الإشعارات"""
        return {
            'shipment_created': {
                'sms': 'تم إنشاء شحنتك بنجاح. رقم التتبع: {tracking_number}',
                'email': {
                    'subject': 'تأكيد إنشاء الشحنة',
                    'body': '''
                    عزيزي {recipient_name},
                    
                    تم إنشاء شحنتك بنجاح.
                    
                    تفاصيل الشحنة:
                    - رقم التتبع: {tracking_number}
                    - المرسل: {sender_name}
                    - المستقبل: {recipient_name}
                    - العنوان: {recipient_address}
                    
                    يمكنك تتبع شحنتك على الرابط التالي:
                    {tracking_url}
                    
                    شكراً لاختيارك خدماتنا.
                    '''
                }
            },
            'shipment_picked_up': {
                'sms': 'تم استلام شحنتك رقم {tracking_number} من المرسل وهي في الطريق إليك',
                'email': {
                    'subject': 'تم استلام شحنتك',
                    'body': 'تم استلام شحنتك رقم {tracking_number} من المرسل وهي الآن في الطريق إليك.'
                }
            },
            'shipment_in_transit': {
                'sms': 'شحنتك رقم {tracking_number} في الطريق. الموقع الحالي: {current_location}',
                'email': {
                    'subject': 'شحنتك في الطريق',
                    'body': 'شحنتك رقم {tracking_number} في الطريق إليك. الموقع الحالي: {current_location}'
                }
            },
            'shipment_out_for_delivery': {
                'sms': 'شحنتك رقم {tracking_number} خارج للتسليم وستصل خلال 30 دقيقة تقريباً',
                'email': {
                    'subject': 'شحنتك خارج للتسليم',
                    'body': 'شحنتك رقم {tracking_number} خارج للتسليم وستصل إليك قريباً.'
                }
            },
            'shipment_delivered': {
                'sms': 'تم تسليم شحنتك رقم {tracking_number} بنجاح. شكراً لاختيارك خدماتنا',
                'email': {
                    'subject': 'تم تسليم شحنتك بنجاح',
                    'body': '''
                    تم تسليم شحنتك رقم {tracking_number} بنجاح.
                    
                    تفاصيل التسليم:
                    - تاريخ التسليم: {delivery_date}
                    - تم التسليم إلى: {delivered_to}
                    
                    شكراً لاختيارك خدماتنا.
                    '''
                }
            },
            'shipment_delayed': {
                'sms': 'شحنتك رقم {tracking_number} متأخرة عن الموعد المحدد. نعتذر عن الإزعاج',
                'email': {
                    'subject': 'تأخير في شحنتك',
                    'body': 'نعتذر عن تأخير شحنتك رقم {tracking_number}. سيتم التسليم في أقرب وقت ممكن.'
                }
            },
            'delivery_attempt_failed': {
                'sms': 'فشلت محاولة تسليم شحنتك رقم {tracking_number}. السبب: {failure_reason}',
                'email': {
                    'subject': 'فشلت محاولة التسليم',
                    'body': 'فشلت محاولة تسليم شحنتك رقم {tracking_number}. السبب: {failure_reason}. سيتم المحاولة مرة أخرى.'
                }
            }
        }
    
    def send_notification(self, shipment_id, notification_type, template_key, **kwargs):
        """إرسال إشعار باستخدام القالب"""
        try:
            shipment = Shipment.query.get(shipment_id)
            if not shipment:
                return False
            
            template = self.notification_templates.get(template_key)
            if not template:
                return False
            
            # إعداد البيانات للقالب
            template_data = {
                'tracking_number': shipment.tracking_number,
                'sender_name': shipment.sender_name,
                'recipient_name': shipment.recipient_name,
                'recipient_address': shipment.recipient_address,
                'current_location': shipment.current_location,
                'tracking_url': f"https://yoursite.com/shipments/track/{shipment.tracking_number}",
                **kwargs
            }
            
            success = True
            
            # إرسال SMS
            if notification_type in ['SMS', 'ALL'] and shipment.recipient_phone:
                sms_message = template.get('sms', '').format(**template_data)
                if sms_message:
                    sms_success = self.send_sms_notification(
                        shipment_id, shipment.recipient_phone, sms_message
                    )
                    success = success and sms_success
            
            # إرسال Email
            if notification_type in ['EMAIL', 'ALL'] and shipment.recipient_email:
                email_template = template.get('email', {})
                if email_template:
                    subject = email_template.get('subject', '').format(**template_data)
                    body = email_template.get('body', '').format(**template_data)
                    email_success = self.send_email_notification(
                        shipment_id, shipment.recipient_email, subject, body
                    )
                    success = success and email_success
            
            # إرسال إشعار فوري عبر WebSocket
            self.send_realtime_notification(shipment_id, template_key, template_data)
            
            return success
            
        except Exception as e:
            current_app.logger.error(f"Error sending notification: {e}")
            return False
    
    def send_sms_notification(self, shipment_id, phone_number, message):
        """إرسال إشعار SMS"""
        try:
            notification = ShipmentNotification(
                shipment_id=shipment_id,
                notification_type='SMS',
                recipient=phone_number,
                message=message
            )
            
            db.session.add(notification)
            
            # إرسال SMS
            success = send_sms(phone_number, message)
            
            if success:
                notification.status = 'مرسل'
                notification.sent_at = datetime.utcnow()
            else:
                notification.status = 'فشل'
            
            db.session.commit()
            return success
            
        except Exception as e:
            current_app.logger.error(f"Error sending SMS notification: {e}")
            db.session.rollback()
            return False
    
    def send_email_notification(self, shipment_id, email, subject, message):
        """إرسال إشعار بريد إلكتروني"""
        try:
            notification = ShipmentNotification(
                shipment_id=shipment_id,
                notification_type='EMAIL',
                recipient=email,
                subject=subject,
                message=message
            )
            
            db.session.add(notification)
            
            # إرسال Email
            success = send_email(email, subject, message)
            
            if success:
                notification.status = 'مرسل'
                notification.sent_at = datetime.utcnow()
            else:
                notification.status = 'فشل'
            
            db.session.commit()
            return success
            
        except Exception as e:
            current_app.logger.error(f"Error sending email notification: {e}")
            db.session.rollback()
            return False
    
    def send_realtime_notification(self, shipment_id, event_type, data):
        """إرسال إشعار فوري عبر WebSocket"""
        try:
            notification_data = {
                'type': 'shipment_update',
                'shipment_id': shipment_id,
                'event_type': event_type,
                'timestamp': datetime.utcnow().isoformat(),
                'data': data
            }
            
            # إرسال للعملاء المتصلين
            self.broadcast_to_websockets(json.dumps(notification_data))
            
        except Exception as e:
            current_app.logger.error(f"Error sending realtime notification: {e}")
    
    def broadcast_to_websockets(self, message):
        """بث رسالة لجميع العملاء المتصلين عبر WebSocket"""
        try:
            if self.websocket_clients:
                # إرسال غير متزامن
                asyncio.create_task(self._broadcast_async(message))
                
        except Exception as e:
            current_app.logger.error(f"Error broadcasting to websockets: {e}")
    
    async def _broadcast_async(self, message):
        """بث غير متزامن للرسائل"""
        if self.websocket_clients:
            await asyncio.gather(
                *[client.send(message) for client in self.websocket_clients],
                return_exceptions=True
            )
    
    def add_websocket_client(self, websocket):
        """إضافة عميل WebSocket"""
        self.websocket_clients.add(websocket)
    
    def remove_websocket_client(self, websocket):
        """إزالة عميل WebSocket"""
        self.websocket_clients.discard(websocket)
    
    def schedule_delivery_reminders(self, shipment_id):
        """جدولة تذكيرات التسليم"""
        try:
            shipment = Shipment.query.get(shipment_id)
            if not shipment or not shipment.expected_delivery_date:
                return
            
            # تذكير قبل يوم من التسليم
            reminder_time = shipment.expected_delivery_date - timedelta(days=1)
            if reminder_time > datetime.utcnow():
                self.schedule_notification(
                    shipment_id,
                    reminder_time,
                    'delivery_reminder_1day',
                    'SMS'
                )
            
            # تذكير قبل ساعتين من التسليم
            reminder_time = shipment.expected_delivery_date - timedelta(hours=2)
            if reminder_time > datetime.utcnow():
                self.schedule_notification(
                    shipment_id,
                    reminder_time,
                    'delivery_reminder_2hours',
                    'SMS'
                )
            
        except Exception as e:
            current_app.logger.error(f"Error scheduling delivery reminders: {e}")
    
    def schedule_notification(self, shipment_id, send_time, template_key, notification_type):
        """جدولة إشعار للإرسال في وقت محدد"""
        try:
            # استخدام Celery لجدولة المهام
            from .tasks import send_scheduled_notification
            
            send_scheduled_notification.apply_async(
                args=[shipment_id, template_key, notification_type],
                eta=send_time
            )
            
        except Exception as e:
            current_app.logger.error(f"Error scheduling notification: {e}")

class AdvancedNotificationManager:
    """نظام الإشعارات المتقدم متعدد القنوات"""

    def __init__(self):
        self.db_manager = DatabaseManager()

        # إعدادات الإشعارات
        self.notification_config = {
            'sms_enabled': True,
            'email_enabled': True,
            'whatsapp_enabled': True,
            'push_enabled': True,
            'telegram_enabled': False,

            # إعدادات SMS
            'sms_provider': 'local',  # local, twilio, aws_sns
            'sms_api_key': '',
            'sms_sender_id': 'SASERP',

            # إعدادات WhatsApp
            'whatsapp_api_url': 'https://api.whatsapp.com/send',
            'whatsapp_token': '',

            # إعدادات البريد الإلكتروني
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'smtp_username': '',
            'smtp_password': '',
            'email_from': '<EMAIL>',

            # قوالب الإشعارات
            'templates': {
                'delivery_order_created': {
                    'sms': 'تم إنشاء أمر تسليم جديد رقم {order_number} للشحنة {tracking_number}',
                    'email': {
                        'subject': 'أمر تسليم جديد - {order_number}',
                        'body': 'تم إنشاء أمر تسليم جديد رقم {order_number} للشحنة {tracking_number}. يرجى المراجعة والمتابعة.'
                    },
                    'whatsapp': 'أمر تسليم جديد 📦\nرقم الأمر: {order_number}\nرقم التتبع: {tracking_number}\nالأولوية: {priority}',
                    'push': {
                        'title': 'أمر تسليم جديد',
                        'body': 'أمر رقم {order_number} - {tracking_number}',
                        'icon': 'delivery-icon.png'
                    }
                },
                'order_status_updated': {
                    'sms': 'تم تحديث حالة أمر التسليم {order_number} إلى: {new_status}',
                    'email': {
                        'subject': 'تحديث حالة أمر التسليم - {order_number}',
                        'body': 'تم تحديث حالة أمر التسليم {order_number} من {old_status} إلى {new_status}'
                    },
                    'whatsapp': 'تحديث حالة 🔄\nأمر التسليم: {order_number}\nالحالة الجديدة: {new_status}',
                    'push': {
                        'title': 'تحديث حالة الأمر',
                        'body': '{order_number} - {new_status}',
                        'icon': 'update-icon.png'
                    }
                },
                'deadline_reminder': {
                    'sms': 'تذكير: أمر التسليم {order_number} مطلوب إنجازه خلال {hours_remaining} ساعة',
                    'email': {
                        'subject': 'تذكير موعد نهائي - {order_number}',
                        'body': 'يرجى إنجاز أمر التسليم {order_number} قبل {deadline_date}'
                    },
                    'whatsapp': '⏰ تذكير موعد نهائي\nأمر التسليم: {order_number}\nالموعد النهائي: {deadline_date}\nالوقت المتبقي: {hours_remaining} ساعة',
                    'push': {
                        'title': 'تذكير موعد نهائي',
                        'body': '{order_number} - {hours_remaining} ساعة متبقية',
                        'icon': 'reminder-icon.png'
                    }
                },
                'overdue_alert': {
                    'sms': 'تنبيه: أمر التسليم {order_number} متأخر بـ {days_overdue} يوم',
                    'email': {
                        'subject': 'تنبيه تأخير - {order_number}',
                        'body': 'أمر التسليم {order_number} متأخر عن الموعد المحدد بـ {days_overdue} يوم. يرجى المتابعة العاجلة.'
                    },
                    'whatsapp': '🚨 تنبيه تأخير\nأمر التسليم: {order_number}\nمتأخر بـ: {days_overdue} يوم\nيرجى المتابعة العاجلة',
                    'push': {
                        'title': 'تنبيه تأخير',
                        'body': '{order_number} متأخر {days_overdue} يوم',
                        'icon': 'alert-icon.png'
                    }
                },
                'shipment_arrived': {
                    'sms': 'وصلت الشحنة {tracking_number} إلى الميناء. تم إنشاء أمر تسليم تلقائياً.',
                    'email': {
                        'subject': 'وصول شحنة - {tracking_number}',
                        'body': 'وصلت الشحنة {tracking_number} إلى {port_name}. تم إنشاء أمر تسليم تلقائياً رقم {order_number}.'
                    },
                    'whatsapp': '🚢 وصول شحنة\nرقم التتبع: {tracking_number}\nالميناء: {port_name}\nأمر التسليم: {order_number}',
                    'push': {
                        'title': 'وصول شحنة',
                        'body': '{tracking_number} وصلت إلى {port_name}',
                        'icon': 'ship-icon.png'
                    }
                }
            }
        }

    def send_multi_channel_notification(self, recipient_info: Dict, template_key: str,
                                      data: Dict, channels: List[str] = None) -> Dict:
        """إرسال إشعار متعدد القنوات"""
        try:
            if channels is None:
                channels = ['SMS', 'EMAIL']

            results = {
                'success': True,
                'sent_channels': [],
                'failed_channels': [],
                'errors': []
            }

            template = self.notification_config['templates'].get(template_key)
            if not template:
                return {
                    'success': False,
                    'message': f'قالب الإشعار {template_key} غير موجود'
                }

            # إرسال SMS
            if 'SMS' in channels and self.notification_config['sms_enabled']:
                sms_result = self._send_sms(recipient_info, template, data)
                if sms_result['success']:
                    results['sent_channels'].append('SMS')
                else:
                    results['failed_channels'].append('SMS')
                    results['errors'].append(f"SMS: {sms_result['message']}")

            # إرسال البريد الإلكتروني
            if 'EMAIL' in channels and self.notification_config['email_enabled']:
                email_result = self._send_email(recipient_info, template, data)
                if email_result['success']:
                    results['sent_channels'].append('EMAIL')
                else:
                    results['failed_channels'].append('EMAIL')
                    results['errors'].append(f"EMAIL: {email_result['message']}")

            # إرسال WhatsApp
            if 'WHATSAPP' in channels and self.notification_config['whatsapp_enabled']:
                whatsapp_result = self._send_whatsapp(recipient_info, template, data)
                if whatsapp_result['success']:
                    results['sent_channels'].append('WHATSAPP')
                else:
                    results['failed_channels'].append('WHATSAPP')
                    results['errors'].append(f"WHATSAPP: {whatsapp_result['message']}")

            # إرسال Push Notification
            if 'PUSH' in channels and self.notification_config['push_enabled']:
                push_result = self._send_push_notification(recipient_info, template, data)
                if push_result['success']:
                    results['sent_channels'].append('PUSH')
                else:
                    results['failed_channels'].append('PUSH')
                    results['errors'].append(f"PUSH: {push_result['message']}")

            # تسجيل الإشعار في قاعدة البيانات
            self._log_notification(recipient_info, template_key, data, results)

            # تحديد النجاح الإجمالي
            results['success'] = len(results['sent_channels']) > 0

            return results

        except Exception as e:
            logger.error(f"Error sending multi-channel notification: {e}")
            return {
                'success': False,
                'message': f'خطأ في إرسال الإشعار: {str(e)}',
                'sent_channels': [],
                'failed_channels': channels or [],
                'errors': [str(e)]
            }

    def _send_sms(self, recipient_info: Dict, template: Dict, data: Dict) -> Dict:
        """إرسال رسالة SMS"""
        try:
            phone = recipient_info.get('mobile') or recipient_info.get('phone')
            if not phone:
                return {'success': False, 'message': 'رقم الهاتف غير متوفر'}

            # تنسيق الرسالة
            message = template.get('sms', '').format(**data)

            # محاكاة إرسال SMS (يمكن تطوير التكامل الفعلي لاحقاً)
            logger.info(f"SMS sent to {phone}: {message}")

            return {
                'success': True,
                'message': 'تم إرسال الرسالة النصية بنجاح',
                'phone': phone,
                'content': message
            }

        except Exception as e:
            return {'success': False, 'message': f'خطأ في إرسال SMS: {str(e)}'}

    def _send_email(self, recipient_info: Dict, template: Dict, data: Dict) -> Dict:
        """إرسال بريد إلكتروني"""
        try:
            email = recipient_info.get('email')
            if not email:
                return {'success': False, 'message': 'البريد الإلكتروني غير متوفر'}

            email_template = template.get('email', {})
            subject = email_template.get('subject', '').format(**data)
            body = email_template.get('body', '').format(**data)

            # محاكاة إرسال البريد الإلكتروني (يمكن تطوير التكامل الفعلي لاحقاً)
            logger.info(f"Email sent to {email}: {subject}")

            return {
                'success': True,
                'message': 'تم إرسال البريد الإلكتروني بنجاح',
                'email': email,
                'subject': subject,
                'body': body
            }

        except Exception as e:
            return {'success': False, 'message': f'خطأ في إرسال البريد الإلكتروني: {str(e)}'}

    def _send_whatsapp(self, recipient_info: Dict, template: Dict, data: Dict) -> Dict:
        """إرسال رسالة WhatsApp"""
        try:
            phone = recipient_info.get('mobile') or recipient_info.get('phone')
            if not phone:
                return {'success': False, 'message': 'رقم الهاتف غير متوفر'}

            # تنسيق الرسالة
            message = template.get('whatsapp', '').format(**data)

            # محاكاة إرسال WhatsApp (يمكن تطوير التكامل الفعلي لاحقاً)
            logger.info(f"WhatsApp sent to {phone}: {message}")

            return {
                'success': True,
                'message': 'تم إرسال رسالة WhatsApp بنجاح',
                'phone': phone,
                'content': message
            }

        except Exception as e:
            return {'success': False, 'message': f'خطأ في إرسال WhatsApp: {str(e)}'}

    def _send_push_notification(self, recipient_info: Dict, template: Dict, data: Dict) -> Dict:
        """إرسال إشعار Push"""
        try:
            user_id = recipient_info.get('user_id')
            if not user_id:
                return {'success': False, 'message': 'معرف المستخدم غير متوفر'}

            push_template = template.get('push', {})
            title = push_template.get('title', '').format(**data)
            body = push_template.get('body', '').format(**data)
            icon = push_template.get('icon', 'default-icon.png')

            # محاكاة إرسال Push Notification (يمكن تطوير التكامل الفعلي لاحقاً)
            logger.info(f"Push notification sent to user {user_id}: {title}")

            return {
                'success': True,
                'message': 'تم إرسال الإشعار بنجاح',
                'user_id': user_id,
                'title': title,
                'body': body,
                'icon': icon
            }

        except Exception as e:
            return {'success': False, 'message': f'خطأ في إرسال الإشعار: {str(e)}'}

    def _log_notification(self, recipient_info: Dict, template_key: str, data: Dict, results: Dict):
        """تسجيل الإشعار في قاعدة البيانات"""
        try:
            log_query = """
                INSERT INTO notifications_log (
                    id, recipient_type, recipient_id, template_key,
                    channels_attempted, channels_successful, channels_failed,
                    notification_data, sent_at, status
                ) VALUES (
                    notifications_log_seq.NEXTVAL, :recipient_type, :recipient_id, :template_key,
                    :channels_attempted, :channels_successful, :channels_failed,
                    :notification_data, SYSDATE, :status
                )
            """

            self.db_manager.execute_update(log_query, {
                'recipient_type': recipient_info.get('type', 'user'),
                'recipient_id': recipient_info.get('id'),
                'template_key': template_key,
                'channels_attempted': ','.join(results.get('sent_channels', []) + results.get('failed_channels', [])),
                'channels_successful': ','.join(results.get('sent_channels', [])),
                'channels_failed': ','.join(results.get('failed_channels', [])),
                'notification_data': json.dumps(data, ensure_ascii=False),
                'status': 'SUCCESS' if results.get('success') else 'FAILED'
            })

        except Exception as e:
            logger.error(f"Error logging notification: {e}")

    def send_delivery_order_notification(self, order_id: int, template_key: str, channels: List[str] = None) -> Dict:
        """إرسال إشعار خاص بأمر التسليم"""
        try:
            # جلب تفاصيل أمر التسليم والمخلص
            order_query = """
                SELECT
                    do.order_number, do.priority, do.expected_completion_date,
                    do.order_status, do.created_date,
                    cs.tracking_number, cs.booking_number, cs.port_of_discharge,
                    ca.agent_name, ca.email, ca.mobile, ca.id as agent_id
                FROM delivery_orders do
                JOIN cargo_shipments cs ON do.shipment_id = cs.id
                JOIN customs_agents ca ON do.customs_agent_id = ca.id
                WHERE do.id = :order_id
            """

            order_result = self.db_manager.execute_query(order_query, {'order_id': order_id})
            if not order_result:
                return {'success': False, 'message': 'أمر التسليم غير موجود'}

            order_data = order_result[0]

            # تحضير بيانات الإشعار
            notification_data = {
                'order_number': order_data[0],
                'priority': order_data[1],
                'expected_date': order_data[2].strftime('%Y-%m-%d') if order_data[2] else '',
                'order_status': order_data[3],
                'created_date': order_data[4].strftime('%Y-%m-%d') if order_data[4] else '',
                'tracking_number': order_data[5],
                'booking_number': order_data[6],
                'port_name': order_data[7],
                'agent_name': order_data[8]
            }

            # معلومات المستلم (المخلص)
            recipient_info = {
                'type': 'agent',
                'id': order_data[11],
                'name': order_data[8],
                'email': order_data[9],
                'mobile': order_data[10]
            }

            # إرسال الإشعار
            return self.send_multi_channel_notification(
                recipient_info, template_key, notification_data, channels
            )

        except Exception as e:
            logger.error(f"Error sending delivery order notification: {e}")
            return {'success': False, 'message': f'خطأ في إرسال إشعار أمر التسليم: {str(e)}'}

    def send_bulk_notifications(self, recipients: List[Dict], template_key: str,
                               data: Dict, channels: List[str] = None) -> Dict:
        """إرسال إشعارات جماعية"""
        try:
            results = {
                'success': True,
                'total_recipients': len(recipients),
                'successful_sends': 0,
                'failed_sends': 0,
                'details': []
            }

            for recipient in recipients:
                result = self.send_multi_channel_notification(recipient, template_key, data, channels)

                if result['success']:
                    results['successful_sends'] += 1
                else:
                    results['failed_sends'] += 1

                results['details'].append({
                    'recipient': recipient.get('name', 'غير محدد'),
                    'success': result['success'],
                    'sent_channels': result.get('sent_channels', []),
                    'errors': result.get('errors', [])
                })

            results['success'] = results['successful_sends'] > 0

            return results

        except Exception as e:
            logger.error(f"Error sending bulk notifications: {e}")
            return {
                'success': False,
                'message': f'خطأ في إرسال الإشعارات الجماعية: {str(e)}',
                'total_recipients': len(recipients),
                'successful_sends': 0,
                'failed_sends': len(recipients)
            }

    def schedule_smart_reminders(self, order_id: int) -> Dict:
        """جدولة تذكيرات ذكية لأمر التسليم"""
        try:
            # جلب تفاصيل الأمر
            order_query = """
                SELECT order_number, expected_completion_date, priority, customs_agent_id
                FROM delivery_orders
                WHERE id = :order_id
            """

            order_result = self.db_manager.execute_query(order_query, {'order_id': order_id})
            if not order_result:
                return {'success': False, 'message': 'أمر التسليم غير موجود'}

            order_data = order_result[0]
            expected_date = order_data[1]
            priority = order_data[2]

            if not expected_date:
                return {'success': False, 'message': 'التاريخ المتوقع للإنجاز غير محدد'}

            # تحديد أوقات التذكيرات بناءً على الأولوية
            reminder_schedule = self._calculate_reminder_schedule(expected_date, priority)

            # جدولة التذكيرات
            scheduled_count = 0
            for reminder_time, reminder_type in reminder_schedule:
                if reminder_time > datetime.now():
                    schedule_result = self._schedule_reminder(order_id, reminder_time, reminder_type)
                    if schedule_result:
                        scheduled_count += 1

            return {
                'success': True,
                'scheduled_reminders': scheduled_count,
                'reminder_schedule': [
                    {
                        'time': time.strftime('%Y-%m-%d %H:%M'),
                        'type': reminder_type
                    }
                    for time, reminder_type in reminder_schedule
                ]
            }

        except Exception as e:
            logger.error(f"Error scheduling smart reminders: {e}")
            return {'success': False, 'message': f'خطأ في جدولة التذكيرات: {str(e)}'}

    def _calculate_reminder_schedule(self, expected_date: datetime, priority: str) -> List[Tuple]:
        """حساب جدول التذكيرات بناءً على الأولوية"""
        reminders = []

        if priority == 'urgent':
            # تذكيرات متكررة للأوامر العاجلة
            reminders.extend([
                (expected_date - timedelta(hours=4), 'deadline_reminder'),
                (expected_date - timedelta(hours=2), 'urgent_reminder'),
                (expected_date - timedelta(hours=1), 'final_reminder')
            ])
        elif priority == 'high':
            # تذكيرات للأوامر عالية الأولوية
            reminders.extend([
                (expected_date - timedelta(hours=8), 'deadline_reminder'),
                (expected_date - timedelta(hours=2), 'urgent_reminder')
            ])
        elif priority == 'normal':
            # تذكيرات للأوامر العادية
            reminders.extend([
                (expected_date - timedelta(days=1), 'deadline_reminder'),
                (expected_date - timedelta(hours=4), 'urgent_reminder')
            ])
        else:  # low priority
            # تذكير واحد للأوامر منخفضة الأولوية
            reminders.append((expected_date - timedelta(days=1), 'deadline_reminder'))

        return reminders

    def _schedule_reminder(self, order_id: int, reminder_time: datetime, reminder_type: str) -> bool:
        """جدولة تذكير محدد"""
        try:
            schedule_query = """
                INSERT INTO scheduled_notifications (
                    id, order_id, notification_type, scheduled_time,
                    template_key, channels, status
                ) VALUES (
                    scheduled_notifications_seq.NEXTVAL, :order_id, :notification_type,
                    :scheduled_time, :template_key, :channels, 'PENDING'
                )
            """

            # تحديد القنوات بناءً على نوع التذكير
            channels = ['SMS', 'EMAIL']
            if reminder_type in ['urgent_reminder', 'final_reminder']:
                channels.append('WHATSAPP')

            self.db_manager.execute_update(schedule_query, {
                'order_id': order_id,
                'notification_type': reminder_type,
                'scheduled_time': reminder_time,
                'template_key': 'deadline_reminder',
                'channels': ','.join(channels)
            })

            return True

        except Exception as e:
            logger.error(f"Error scheduling reminder: {e}")
            return False

    def process_scheduled_notifications(self) -> Dict:
        """معالجة الإشعارات المجدولة"""
        try:
            # البحث عن الإشعارات المستحقة
            pending_query = """
                SELECT
                    sn.id, sn.order_id, sn.template_key, sn.channels,
                    do.order_number, ca.agent_name, ca.email, ca.mobile, ca.id as agent_id
                FROM scheduled_notifications sn
                JOIN delivery_orders do ON sn.order_id = do.id
                JOIN customs_agents ca ON do.customs_agent_id = ca.id
                WHERE sn.scheduled_time <= SYSDATE
                AND sn.status = 'PENDING'
                ORDER BY sn.scheduled_time
            """

            pending_notifications = self.db_manager.execute_query(pending_query)

            results = {
                'success': True,
                'processed_count': 0,
                'successful_count': 0,
                'failed_count': 0,
                'details': []
            }

            for notification in pending_notifications:
                notification_id, order_id, template_key, channels_str = notification[:4]
                order_number, agent_name, email, mobile, agent_id = notification[4:]

                channels = channels_str.split(',') if channels_str else ['SMS']

                # تحضير بيانات الإشعار
                notification_data = {
                    'order_number': order_number,
                    'agent_name': agent_name,
                    'deadline_date': datetime.now().strftime('%Y-%m-%d'),
                    'hours_remaining': '24'  # يمكن حسابها بدقة أكثر
                }

                recipient_info = {
                    'type': 'agent',
                    'id': agent_id,
                    'name': agent_name,
                    'email': email,
                    'mobile': mobile
                }

                # إرسال الإشعار
                send_result = self.send_multi_channel_notification(
                    recipient_info, template_key, notification_data, channels
                )

                # تحديث حالة الإشعار المجدول
                status = 'SENT' if send_result['success'] else 'FAILED'
                update_query = """
                    UPDATE scheduled_notifications
                    SET status = :status, sent_at = SYSDATE,
                        error_message = :error_message
                    WHERE id = :notification_id
                """

                error_message = '; '.join(send_result.get('errors', [])) if not send_result['success'] else None

                self.db_manager.execute_update(update_query, {
                    'status': status,
                    'error_message': error_message,
                    'notification_id': notification_id
                })

                results['processed_count'] += 1
                if send_result['success']:
                    results['successful_count'] += 1
                else:
                    results['failed_count'] += 1

                results['details'].append({
                    'order_number': order_number,
                    'agent_name': agent_name,
                    'success': send_result['success'],
                    'channels': send_result.get('sent_channels', []),
                    'errors': send_result.get('errors', [])
                })

            return results

        except Exception as e:
            logger.error(f"Error processing scheduled notifications: {e}")
            return {
                'success': False,
                'message': f'خطأ في معالجة الإشعارات المجدولة: {str(e)}',
                'processed_count': 0,
                'successful_count': 0,
                'failed_count': 0
            }

    def get_notification_statistics(self, days: int = 30) -> Dict:
        """الحصول على إحصائيات الإشعارات"""
        try:
            stats = {}

            # إحصائيات الإرسال
            sending_stats_query = """
                SELECT
                    COUNT(*) as total_notifications,
                    COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) as successful_notifications,
                    COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed_notifications,
                    COUNT(CASE WHEN channels_successful LIKE '%SMS%' THEN 1 END) as sms_sent,
                    COUNT(CASE WHEN channels_successful LIKE '%EMAIL%' THEN 1 END) as email_sent,
                    COUNT(CASE WHEN channels_successful LIKE '%WHATSAPP%' THEN 1 END) as whatsapp_sent
                FROM notifications_log
                WHERE sent_at >= SYSDATE - :days
            """

            sending_stats = self.db_manager.execute_query(sending_stats_query, {'days': days})
            if sending_stats:
                row = sending_stats[0]
                stats['sending'] = {
                    'total': row[0] or 0,
                    'successful': row[1] or 0,
                    'failed': row[2] or 0,
                    'success_rate': (row[1] / row[0] * 100) if row[0] > 0 else 0,
                    'channels': {
                        'sms': row[3] or 0,
                        'email': row[4] or 0,
                        'whatsapp': row[5] or 0
                    }
                }

            # إحصائيات القوالب
            template_stats_query = """
                SELECT
                    template_key,
                    COUNT(*) as usage_count,
                    COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) as success_count
                FROM notifications_log
                WHERE sent_at >= SYSDATE - :days
                GROUP BY template_key
                ORDER BY usage_count DESC
            """

            template_stats = self.db_manager.execute_query(template_stats_query, {'days': days})
            stats['templates'] = [
                {
                    'template': row[0],
                    'usage_count': row[1],
                    'success_count': row[2],
                    'success_rate': (row[2] / row[1] * 100) if row[1] > 0 else 0
                }
                for row in template_stats
            ]

            return stats

        except Exception as e:
            logger.error(f"Error getting notification statistics: {e}")
            return {}

# مثيل عام للاستخدام
advanced_notification_manager = AdvancedNotificationManager()

# إنشاء مثيل عام من مدير الإشعارات
notification_manager = NotificationManager()
