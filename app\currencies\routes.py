# -*- coding: utf-8 -*-
"""
مسارات إدارة العملات
Currency Management Routes
"""

from flask import render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app.currencies import bp
from database_manager import db_manager
from datetime import datetime, date
from decimal import Decimal
import logging

# إعداد السجلات
logger = logging.getLogger(__name__)

@bp.route('/')
def index():
    """صفحة إدارة العملات الرئيسية"""
    try:
        # جلب جميع العملات
        currencies_query = """
        SELECT id, code, name_ar, name_en, symbol, exchange_rate,
               is_base_currency, is_active, decimal_places, position,
               thousands_separator, decimal_separator, created_at, updated_at
        FROM currencies
        ORDER BY is_base_currency DESC, code
        """
        currencies_data = db_manager.execute_query(currencies_query)

        # تحويل البيانات إلى قاموس
        currencies = []
        if currencies_data:
            for row in currencies_data:
                currency = {
                    'id': row[0],
                    'code': row[1],
                    'name_ar': row[2],
                    'name_en': row[3],
                    'symbol': row[4],
                    'exchange_rate': float(row[5]) if row[5] else 1.0,
                    'is_base_currency': bool(row[6]),
                    'is_active': bool(row[7]),
                    'decimal_places': row[8] or 2,
                    'position': row[9] or 'before',
                    'thousands_separator': row[10] or ',',
                    'decimal_separator': row[11] or '.',
                    'created_at': row[12],
                    'updated_at': row[13]
                }
                currencies.append(currency)

        # إحصائيات سريعة
        stats_queries = {
            'total_currencies': "SELECT COUNT(*) FROM currencies",
            'active_currencies': "SELECT COUNT(*) FROM currencies WHERE is_active = 1",
            'base_currency': "SELECT code, name_ar, symbol FROM currencies WHERE is_base_currency = 1",
            'last_update': "SELECT created_at FROM currency_exchange_rates ORDER BY created_at DESC"
        }

        stats = {}

        # إجمالي العملات
        total_result = db_manager.execute_query(stats_queries['total_currencies'])
        stats['total_currencies'] = total_result[0][0] if total_result else 0

        # العملات النشطة
        active_result = db_manager.execute_query(stats_queries['active_currencies'])
        stats['active_currencies'] = active_result[0][0] if active_result else 0

        # العملة الأساسية
        base_result = db_manager.execute_query(stats_queries['base_currency'])
        if base_result:
            stats['base_currency'] = {
                'code': base_result[0][0],
                'name_ar': base_result[0][1],
                'symbol': base_result[0][2]
            }
        else:
            stats['base_currency'] = None

        # آخر تحديث
        update_result = db_manager.execute_query(stats_queries['last_update'])
        if update_result:
            stats['last_update'] = {
                'created_at': update_result[0][0]
            }
        else:
            stats['last_update'] = None

        # إضافة رسائل تصحيح
        print(f"عدد العملات المجلبة: {len(currencies)}")
        if currencies:
            print(f"أول عملة: {currencies[0]}")

        return render_template('currencies/index.html',
                             currencies=currencies,
                             stats=stats)
    except Exception as e:
        logger.error(f"خطأ في عرض صفحة العملات: {e}")
        print(f"خطأ في عرض صفحة العملات: {e}")
        flash('حدث خطأ في تحميل البيانات', 'error')
        return redirect(url_for('settings.index'))

@bp.route('/add', methods=['GET', 'POST'])
@login_required
def add_currency():
    """إضافة عملة جديدة"""
    if request.method == 'POST':
        try:
            data = request.get_json() if request.is_json else request.form

            # التحقق من البيانات المطلوبة
            required_fields = ['code', 'name_ar', 'name_en', 'symbol']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({
                        'success': False,
                        'message': f'الحقل {field} مطلوب'
                    })

            # التحقق من عدم تكرار الكود
            check_query = "SELECT COUNT(*) FROM currencies WHERE code = :1"
            existing_count = db_manager.execute_query(check_query, [data['code'].upper()])

            if existing_count and existing_count[0][0] > 0:
                return jsonify({
                    'success': False,
                    'message': f'العملة بالكود {data["code"]} موجودة بالفعل'
                })

            # إذا كانت العملة الأساسية، إلغاء تفعيل العملات الأساسية الأخرى
            is_base_currency = int(data.get('is_base_currency', 0))
            if is_base_currency:
                update_base_query = "UPDATE currencies SET is_base_currency = 0 WHERE is_base_currency = 1"
                db_manager.execute_update(update_base_query)

            # إدراج العملة الجديدة
            insert_query = """
            INSERT INTO currencies (code, name_ar, name_en, symbol, exchange_rate,
                                   is_base_currency, is_active, decimal_places, position,
                                   thousands_separator, decimal_separator, created_by)
            VALUES (:1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12)
            """

            exchange_rate = float(data.get('exchange_rate', 1.0))
            if is_base_currency:
                exchange_rate = 1.0

            params = [
                data['code'].upper(),
                data['name_ar'],
                data['name_en'],
                data['symbol'],
                exchange_rate,
                is_base_currency,
                int(data.get('is_active', 1)),
                int(data.get('decimal_places', 2)),
                data.get('position', 'before'),
                data.get('thousands_separator', ','),
                data.get('decimal_separator', '.'),
                current_user.id
            ]

            result = db_manager.execute_update(insert_query, params)

            if result:
                # الحصول على معرف العملة الجديدة
                new_currency_query = "SELECT id FROM currencies WHERE code = :1"
                new_currency_result = db_manager.execute_query(new_currency_query, [data['code'].upper()])

                if new_currency_result and not is_base_currency:
                    # إضافة سجل سعر الصرف الأولي
                    currency_id = new_currency_result[0][0]

                    # الحصول على العملة الأساسية
                    base_currency_query = "SELECT id FROM currencies WHERE is_base_currency = 1"
                    base_currency_result = db_manager.execute_query(base_currency_query)

                    if base_currency_result:
                        base_currency_id = base_currency_result[0][0]

                        rate_insert_query = """
                        INSERT INTO currency_exchange_rates (currency_id, base_currency_id, exchange_rate, source, notes, created_by)
                        VALUES (:1, :2, :3, 'manual', 'سعر صرف أولي', :4)
                        """

                        db_manager.execute_update(rate_insert_query, [currency_id, base_currency_id, exchange_rate, current_user.id])

                return jsonify({
                    'success': True,
                    'message': 'تم إضافة العملة بنجاح'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'فشل في إضافة العملة'
                })

        except Exception as e:
            logger.error(f"خطأ في إضافة العملة: {e}")
            return jsonify({
                'success': False,
                'message': f'حدث خطأ في إضافة العملة: {str(e)}'
            })

    return render_template('currencies/add.html')

@bp.route('/edit/<int:currency_id>', methods=['GET', 'POST'])
@login_required
def edit_currency(currency_id):
    """تعديل عملة موجودة"""
    # الحصول على بيانات العملة
    currency_query = """
    SELECT id, code, name_ar, name_en, symbol, exchange_rate,
           is_base_currency, is_active, decimal_places, position,
           thousands_separator, decimal_separator
    FROM currencies
    WHERE id = :1
    """
    currency_data = db_manager.execute_query(currency_query, [currency_id])

    if not currency_data:
        flash('العملة غير موجودة', 'error')
        return redirect(url_for('currencies.index'))

    # تحويل البيانات إلى قاموس
    currency = {
        'id': currency_data[0][0],
        'code': currency_data[0][1],
        'name_ar': currency_data[0][2],
        'name_en': currency_data[0][3],
        'symbol': currency_data[0][4],
        'exchange_rate': currency_data[0][5],
        'is_base_currency': currency_data[0][6],
        'is_active': currency_data[0][7],
        'decimal_places': currency_data[0][8],
        'position': currency_data[0][9],
        'thousands_separator': currency_data[0][10],
        'decimal_separator': currency_data[0][11]
    }
    
    if request.method == 'POST':
        try:
            data = request.get_json() if request.is_json else request.form
            
            # التحقق من البيانات المطلوبة
            required_fields = ['name_ar', 'name_en', 'symbol']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({
                        'success': False,
                        'message': f'الحقل {field} مطلوب'
                    })
            
            # التعامل مع العملة الأساسية
            new_is_base = int(data.get('is_base_currency', 0))
            if new_is_base and not currency['is_base_currency']:
                # تحويل إلى عملة أساسية - إلغاء العملات الأساسية الأخرى
                update_base_query = "UPDATE currencies SET is_base_currency = 0 WHERE is_base_currency = 1"
                db_manager.execute_update(update_base_query)

            # تحديد سعر الصرف
            if new_is_base:
                exchange_rate = Decimal('1.0')
            else:
                # إذا لم تكن أساسية، استخدم السعر الحالي أو 1.0 كافتراضي
                exchange_rate = Decimal(str(data.get('exchange_rate', currency['exchange_rate'] or 1.0)))

            # تحديث البيانات
            update_query = """
            UPDATE currencies
            SET name_ar = :1, name_en = :2, symbol = :3, decimal_places = :4,
                position = :5, thousands_separator = :6, decimal_separator = :7,
                is_active = :8, is_base_currency = :9, exchange_rate = :10,
                updated_by = :11, updated_at = :12
            WHERE id = :13
            """

            params = [
                data['name_ar'],
                data['name_en'],
                data['symbol'],
                int(data.get('decimal_places', currency['decimal_places'])),
                data.get('position', currency['position']),
                data.get('thousands_separator', currency['thousands_separator']),
                data.get('decimal_separator', currency['decimal_separator']),
                int(data.get('is_active', currency['is_active'])),
                new_is_base,
                exchange_rate,
                current_user.id,
                datetime.utcnow(),
                currency_id
            ]

            result = db_manager.execute_update(update_query, params)

            if result:
                return jsonify({
                    'success': True,
                    'message': 'تم تحديث العملة بنجاح'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'فشل في تحديث العملة'
                })

        except Exception as e:
            logger.error(f"خطأ في تحديث العملة {currency_id}: {e}")
            import traceback
            traceback.print_exc()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ في تحديث العملة: {str(e)}'
            })

    return render_template('currencies/edit.html', currency=currency)

@bp.route('/delete/<int:currency_id>', methods=['POST'])
@login_required
def delete_currency(currency_id):
    """حذف عملة"""
    try:
        # التحقق من وجود العملة
        currency_query = "SELECT is_base_currency FROM currencies WHERE id = :1"
        currency_data = db_manager.execute_query(currency_query, [currency_id])

        if not currency_data:
            return jsonify({
                'success': False,
                'message': 'العملة غير موجودة'
            })

        # التحقق من عدم إمكانية حذف العملة الأساسية
        if currency_data[0][0]:  # is_base_currency
            return jsonify({
                'success': False,
                'message': 'لا يمكن حذف العملة الأساسية'
            })

        # حذف أسعار الصرف المرتبطة
        delete_rates_query1 = "DELETE FROM currency_exchange_rates WHERE currency_id = :1"
        delete_rates_query2 = "DELETE FROM currency_exchange_rates WHERE base_currency_id = :1"
        db_manager.execute_update(delete_rates_query1, [currency_id])
        db_manager.execute_update(delete_rates_query2, [currency_id])

        # حذف العملة
        delete_currency_query = "DELETE FROM currencies WHERE id = :1"
        result = db_manager.execute_update(delete_currency_query, [currency_id])
        
        if result:
            return jsonify({
                'success': True,
                'message': 'تم حذف العملة بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في حذف العملة'
            })

    except Exception as e:
        logger.error(f"خطأ في حذف العملة {currency_id}: {e}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في حذف العملة: {str(e)}'
        })

@bp.route('/toggle-status/<int:currency_id>', methods=['POST'])
@login_required
def toggle_currency_status(currency_id):
    """تفعيل/إلغاء تفعيل عملة"""
    try:
        # الحصول على بيانات العملة
        currency_query = "SELECT is_base_currency, is_active FROM currencies WHERE id = :1"
        currency_data = db_manager.execute_query(currency_query, [currency_id])

        if not currency_data:
            return jsonify({
                'success': False,
                'message': 'العملة غير موجودة'
            })

        is_base_currency = currency_data[0][0]
        is_active = currency_data[0][1]

        # التحقق من عدم إمكانية إلغاء تفعيل العملة الأساسية
        if is_base_currency and is_active:
            return jsonify({
                'success': False,
                'message': 'لا يمكن إلغاء تفعيل العملة الأساسية'
            })
        
        # تغيير الحالة
        new_status = 1 - is_active

        update_query = """
        UPDATE currencies
        SET is_active = :1, updated_by = :2, updated_at = :3
        WHERE id = :4
        """

        result = db_manager.execute_update(update_query, [
            new_status,
            current_user.id,
            datetime.utcnow(),
            currency_id
        ])

        if result:
            status_text = 'تم تفعيل' if new_status else 'تم إلغاء تفعيل'
            return jsonify({
                'success': True,
                'message': f'{status_text} العملة بنجاح',
                'is_active': bool(new_status)
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في تغيير حالة العملة'
            })

    except Exception as e:
        logger.error(f"خطأ في تغيير حالة العملة {currency_id}: {e}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في تغيير حالة العملة: {str(e)}'
        })

@bp.route('/exchange-rates')
@login_required
def exchange_rates():
    """صفحة إدارة أسعار الصرف"""
    try:
        # جلب العملات النشطة
        currencies = Currency.query.filter_by(is_active=1).order_by(Currency.code).all()
        
        # جلب آخر أسعار الصرف
        latest_rates = db.session.query(CurrencyExchangeRate).filter(
            CurrencyExchangeRate.id.in_(
                db.session.query(db.func.max(CurrencyExchangeRate.id))
                .group_by(CurrencyExchangeRate.currency_id)
            )
        ).all()
        
        return render_template('currencies/exchange_rates.html', 
                             currencies=currencies,
                             latest_rates=latest_rates)
    except Exception as e:
        logger.error(f"خطأ في عرض أسعار الصرف: {e}")
        flash('حدث خطأ في تحميل أسعار الصرف', 'error')
        return redirect(url_for('currencies.index'))

@bp.route('/update-exchange-rate', methods=['POST'])
@login_required
def update_exchange_rate():
    """تحديث سعر صرف عملة"""
    try:
        data = request.get_json() if request.is_json else request.form
        
        currency_id = data.get('currency_id')
        new_rate = data.get('exchange_rate')
        source = data.get('source', 'manual')
        notes = data.get('notes', '')
        
        if not currency_id or not new_rate:
            return jsonify({
                'success': False,
                'message': 'معرف العملة وسعر الصرف مطلوبان'
            })
        
        currency = Currency.query.get_or_404(currency_id)
        base_currency = Currency.get_base_currency()
        
        if not base_currency:
            return jsonify({
                'success': False,
                'message': 'لا توجد عملة أساسية محددة'
            })
        
        # تحديث سعر الصرف في جدول العملات
        currency.exchange_rate = Decimal(new_rate)
        currency.updated_by = current_user.id
        currency.updated_at = datetime.utcnow()
        
        # إضافة سجل في تاريخ أسعار الصرف
        exchange_rate_record = CurrencyExchangeRate(
            currency_id=currency.id,
            base_currency_id=base_currency.id,
            exchange_rate=Decimal(new_rate),
            source=source,
            notes=notes,
            created_by=current_user.id
        )
        
        db.session.add(exchange_rate_record)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم تحديث سعر الصرف بنجاح',
            'new_rate': float(new_rate)
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في تحديث سعر الصرف: {e}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في تحديث سعر الصرف: {str(e)}'
        })

@bp.route('/api/currencies')
def api_currencies():
    """API للحصول على قائمة العملات"""
    try:
        # جلب العملات النشطة
        currencies_query = """
        SELECT id, code, name_ar, name_en, symbol, exchange_rate,
               is_base_currency, is_active, decimal_places, position,
               thousands_separator, decimal_separator
        FROM currencies
        WHERE is_active = 1
        ORDER BY code
        """
        currencies_data = db_manager.execute_query(currencies_query)

        currencies = []
        if currencies_data:
            for row in currencies_data:
                currency = {
                    'id': row[0],
                    'code': row[1],
                    'name_ar': row[2],
                    'name_en': row[3],
                    'symbol': row[4],
                    'exchange_rate': float(row[5]) if row[5] else 1.0,
                    'is_base_currency': bool(row[6]),
                    'is_active': bool(row[7]),
                    'decimal_places': row[8] or 2,
                    'position': row[9] or 'before',
                    'thousands_separator': row[10] or ',',
                    'decimal_separator': row[11] or '.'
                }
                currencies.append(currency)

        return jsonify({
            'success': True,
            'currencies': currencies
        })
    except Exception as e:
        logger.error(f"خطأ في API العملات: {e}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في جلب العملات: {str(e)}'
        })

@bp.route('/api/convert')
def api_convert():
    """API لتحويل العملات"""
    try:
        amount = float(request.args.get('amount', 0))
        from_currency = request.args.get('from_currency')
        to_currency = request.args.get('to_currency')

        if not all([amount, from_currency, to_currency]):
            return jsonify({
                'success': False,
                'message': 'جميع المعاملات مطلوبة'
            })

        # جلب أسعار العملات
        rates_query = """
        SELECT code, exchange_rate, symbol, decimal_places, position
        FROM currencies
        WHERE code IN (:1, :2) AND is_active = 1
        """
        rates_data = db_manager.execute_query(rates_query, [from_currency, to_currency])

        if not rates_data or len(rates_data) < 2:
            return jsonify({
                'success': False,
                'message': 'إحدى العملات غير موجودة أو غير مفعلة'
            })

        # تنظيم البيانات
        rates = {}
        for row in rates_data:
            rates[row[0]] = {
                'exchange_rate': float(row[1]),
                'symbol': row[2],
                'decimal_places': row[3] or 2,
                'position': row[4] or 'before'
            }

        # التحويل عبر العملة الأساسية
        from_rate = rates[from_currency]['exchange_rate']
        to_rate = rates[to_currency]['exchange_rate']

        # تحويل إلى العملة الأساسية ثم إلى العملة الهدف
        base_amount = amount / from_rate
        converted_amount = base_amount * to_rate

        # تنسيق المبلغ
        to_currency_info = rates[to_currency]
        decimal_places = to_currency_info['decimal_places']
        symbol = to_currency_info['symbol']
        position = to_currency_info['position']

        formatted_amount = f"{converted_amount:.{decimal_places}f}"
        if position == 'before':
            formatted_display = f"{symbol} {formatted_amount}"
        else:
            formatted_display = f"{formatted_amount} {symbol}"

        return jsonify({
            'success': True,
            'original_amount': amount,
            'converted_amount': round(converted_amount, decimal_places),
            'from_currency': from_currency,
            'to_currency': to_currency,
            'formatted_amount': formatted_display
        })

    except Exception as e:
        logger.error(f"خطأ في تحويل العملة: {e}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في التحويل: {str(e)}'
        })

@bp.route('/set-base-currency/<int:currency_id>', methods=['POST'])
@login_required
def set_base_currency(currency_id):
    """تعيين عملة كعملة أساسية"""
    try:
        # التحقق من وجود العملة وحالتها
        currency_query = "SELECT name_ar, is_active FROM currencies WHERE id = :1"
        currency_data = db_manager.execute_query(currency_query, [currency_id])

        if not currency_data:
            return jsonify({
                'success': False,
                'message': 'العملة غير موجودة'
            })

        currency_name = currency_data[0][0]
        is_active = currency_data[0][1]

        if not is_active:
            return jsonify({
                'success': False,
                'message': 'لا يمكن تعيين عملة غير مفعلة كعملة أساسية'
            })

        # إلغاء تفعيل العملات الأساسية الأخرى
        update_others_query = "UPDATE currencies SET is_base_currency = 0 WHERE is_base_currency = 1"
        db_manager.execute_update(update_others_query)

        # تعيين العملة الجديدة كأساسية
        update_base_query = """
        UPDATE currencies
        SET is_base_currency = 1, exchange_rate = 1.0, updated_by = :1, updated_at = :2
        WHERE id = :3
        """

        result = db_manager.execute_update(update_base_query, [
            current_user.id,
            datetime.utcnow(),
            currency_id
        ])

        if result:
            return jsonify({
                'success': True,
                'message': f'تم تعيين {currency_name} كعملة أساسية'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في تعيين العملة الأساسية'
            })

    except Exception as e:
        logger.error(f"خطأ في تعيين العملة الأساسية {currency_id}: {e}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في تعيين العملة الأساسية: {str(e)}'
        })

@bp.route('/import-rates', methods=['POST'])
@login_required
def import_exchange_rates():
    """استيراد أسعار الصرف من مصدر خارجي"""
    try:
        # هذه الوظيفة يمكن تطويرها لاحقاً للاتصال بـ APIs خارجية
        # مثل البنك المركزي أو مواقع أسعار الصرف

        return jsonify({
            'success': False,
            'message': 'هذه الميزة قيد التطوير'
        })

    except Exception as e:
        logger.error(f"خطأ في استيراد أسعار الصرف: {e}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في استيراد أسعار الصرف: {str(e)}'
        })

@bp.route('/export-rates')
@login_required
def export_exchange_rates():
    """تصدير أسعار الصرف"""
    try:
        # هذه الوظيفة يمكن تطويرها لتصدير أسعار الصرف إلى Excel أو CSV

        return jsonify({
            'success': False,
            'message': 'هذه الميزة قيد التطوير'
        })

    except Exception as e:
        logger.error(f"خطأ في تصدير أسعار الصرف: {e}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في تصدير أسعار الصرف: {str(e)}'
        })
