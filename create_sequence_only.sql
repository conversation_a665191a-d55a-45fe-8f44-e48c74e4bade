-- إنشاء sequence للمنافذ الجمركية فقط
-- تشغيل سريع لحل مشكلة الـ sequence المفقود

-- التحقق من وجود الـ sequence وحذفه إذا كان موجود
BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE customs_ports_seq';
EXCEPTION
    WHEN OTHERS THEN 
        IF SQLCODE != -2289 THEN -- ORA-02289: sequence does not exist
            RAISE;
        END IF;
END;
/

-- إنشاء الـ sequence
CREATE SEQUENCE customs_ports_seq 
START WITH 1 
INCREMENT BY 1 
NOCACHE 
NOCYCLE;

-- اختبار الـ sequence
SELECT customs_ports_seq.NEXTVAL as next_id FROM dual;

-- إعادة تعيين الـ sequence للقيمة الصحيحة
DECLARE
    max_id NUMBER;
    sql_stmt VARCHAR2(200);
BEGIN
    -- جلب أكبر ID موجود
    SELECT NVL(MAX(id), 0) INTO max_id FROM customs_ports;
    
    -- إذا كان هناك بيانات، إعادة تعيين الـ sequence
    IF max_id > 0 THEN
        EXECUTE IMMEDIATE 'DROP SEQUENCE customs_ports_seq';
        sql_stmt := 'CREATE SEQUENCE customs_ports_seq START WITH ' || (max_id + 1) || ' INCREMENT BY 1 NOCACHE NOCYCLE';
        EXECUTE IMMEDIATE sql_stmt;
    END IF;
END;
/

-- رسالة تأكيد
SELECT 'تم إنشاء customs_ports_seq بنجاح!' as message FROM dual;
SELECT customs_ports_seq.CURRVAL as current_value FROM dual;

COMMIT;
