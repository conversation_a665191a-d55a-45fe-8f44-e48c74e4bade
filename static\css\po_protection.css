/**
 * 🛡️ تنسيقات نظام حماية أوامر الشراء
 * Purchase Order Protection Styles
 */

/* أزرار محمية */
.btn-protected {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: #fff !important;
    cursor: not-allowed !important;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.btn-protected:hover {
    background-color: #5a6268 !important;
    border-color: #545b62 !important;
    transform: none !important;
}

.btn-protected:focus,
.btn-protected:active {
    box-shadow: none !important;
    outline: none !important;
}

/* أيقونة الحماية */
.protection-icon {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    background-color: #ffc107 !important;
    color: #212529 !important;
    border: 1px solid #ffca2c;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    animation: protectionPulse 2s infinite;
}

.protection-icon:hover {
    background-color: #ffcd39 !important;
    transform: scale(1.05);
    transition: all 0.2s ease;
}

/* تأثير النبض للحماية */
@keyframes protectionPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
    }
}

/* صف أمر الشراء المحمي */
.po-row-protected {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.po-row-protected:hover {
    background-color: #ffeaa7;
}

/* بطاقة أمر الشراء المحمي */
.po-card-protected {
    border: 2px solid #ffc107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%);
    position: relative;
}

.po-card-protected::before {
    content: "🛡️";
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 1.5rem;
    opacity: 0.3;
    z-index: 1;
}

/* تنبيه الحماية */
.protection-alert {
    background-color: #fff3cd;
    border: 1px solid #ffc107;
    color: #856404;
    padding: 1rem;
    border-radius: 0.375rem;
    margin: 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.protection-alert .icon {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.protection-alert .message {
    flex-grow: 1;
}

.protection-alert .close-btn {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: #856404;
    opacity: 0.7;
}

.protection-alert .close-btn:hover {
    opacity: 1;
}

/* شارة الحالة */
.status-badge-protected {
    background-color: #ffc107;
    color: #212529;
    border: 1px solid #ffca2c;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

/* قائمة أوامر الشراء */
.po-list-item-protected {
    border-left: 4px solid #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
    transition: all 0.3s ease;
}

.po-list-item-protected:hover {
    background-color: rgba(255, 193, 7, 0.2);
    transform: translateX(2px);
}

/* أيقونات الحالة */
.protection-status-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    margin-left: 0.5rem;
}

.protection-status-icon.protected {
    background-color: #ffc107;
    color: #212529;
}

.protection-status-icon.unprotected {
    background-color: #28a745;
    color: #fff;
}

/* تلميح الحماية */
.protection-tooltip {
    position: relative;
    cursor: help;
}

.protection-tooltip::after {
    content: attr(data-protection-reason);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: #fff;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    max-width: 200px;
    white-space: normal;
    text-align: center;
}

.protection-tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* تنسيقات الجدول */
.table-protected-row {
    background-color: rgba(255, 193, 7, 0.1);
}

.table-protected-row td {
    border-top: 1px solid rgba(255, 193, 7, 0.3);
}

/* تنسيقات النموذج */
.form-protected {
    background-color: #fff3cd;
    border: 2px solid #ffc107;
    border-radius: 0.5rem;
    padding: 1.5rem;
    position: relative;
}

.form-protected .form-title {
    color: #856404;
    font-weight: 600;
    margin-bottom: 1rem;
}

.form-protected .protection-notice {
    background-color: #ffeaa7;
    border: 1px solid #ffc107;
    border-radius: 0.25rem;
    padding: 0.75rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: #856404;
}

/* تنسيقات متجاوبة */
@media (max-width: 768px) {
    .protection-icon {
        font-size: 0.625rem;
        padding: 0.125rem 0.25rem;
    }
    
    .btn-protected {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .protection-alert {
        padding: 0.75rem;
        font-size: 0.875rem;
    }
}

/* تأثيرات الحركة */
.protection-fade-in {
    animation: fadeIn 0.5s ease-in;
}

.protection-slide-in {
    animation: slideInFromRight 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInFromRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تنسيقات الطباعة */
@media print {
    .btn-protected,
    .protection-icon,
    .protection-alert {
        background-color: #f8f9fa !important;
        color: #333 !important;
        border: 1px solid #ccc !important;
    }
    
    .protection-icon::before {
        content: "[محمي] ";
    }
}
