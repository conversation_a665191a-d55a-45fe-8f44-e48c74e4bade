{% extends "base.html" %}

{% block title %}تفاصيل أمر التسليم - بوابة المخلص{% endblock %}

{% block extra_css %}
<style>
.order-details-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.details-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.order-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 25px;
}

.status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

.status-draft { background: #6c757d; color: white; }
.status-sent { background: #17a2b8; color: white; }
.status-in_progress { background: #ffc107; color: #212529; }
.status-completed { background: #28a745; color: white; }
.status-cancelled { background: #dc3545; color: white; }

.priority-badge {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.priority-normal { background: #e9ecef; color: #495057; }
.priority-high { background: #fff3cd; color: #856404; }
.priority-urgent { background: #f8d7da; color: #721c24; }

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #eee;
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #495057;
}

.info-value {
    color: #212529;
}

.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-update-status {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-update-status:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 20px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #667eea;
    border: 3px solid white;
    box-shadow: 0 0 0 3px #dee2e6;
}
</style>
{% endblock %}

{% block content %}
<div class="order-details-container">
    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="order-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3 class="mb-2">
                        <i class="fas fa-file-alt me-2"></i>
                        تفاصيل أمر التسليم
                    </h3>
                    <h4 class="mb-0">{{ order_data.order[1] }}</h4>
                </div>
                <div class="col-md-4 text-end">
                    <span class="status-badge status-{{ order_data.order[2] }}">
                        {% if order_data.order[2] == 'draft' %}مسودة
                        {% elif order_data.order[2] == 'sent' %}مُرسل
                        {% elif order_data.order[2] == 'in_progress' %}قيد التنفيذ
                        {% elif order_data.order[2] == 'completed' %}مكتمل
                        {% elif order_data.order[2] == 'cancelled' %}ملغي
                        {% else %}{{ order_data.order[2] }}
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- معلومات الأمر -->
            <div class="col-lg-8">
                <div class="details-card">
                    <h5 class="mb-4">
                        <i class="fas fa-info-circle text-primary me-2"></i>
                        معلومات الأمر
                    </h5>
                    
                    <div class="info-row">
                        <span class="info-label">رقم الأمر:</span>
                        <span class="info-value">{{ order_data.order[1] }}</span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">الحالة:</span>
                        <span class="info-value">
                            <span class="status-badge status-{{ order_data.order[2] }}">
                                {% if order_data.order[2] == 'draft' %}مسودة
                                {% elif order_data.order[2] == 'sent' %}مُرسل
                                {% elif order_data.order[2] == 'in_progress' %}قيد التنفيذ
                                {% elif order_data.order[2] == 'completed' %}مكتمل
                                {% elif order_data.order[2] == 'cancelled' %}ملغي
                                {% else %}{{ order_data.order[2] }}
                                {% endif %}
                            </span>
                        </span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">الأولوية:</span>
                        <span class="info-value">
                            <span class="priority-badge priority-{{ order_data.order[3] }}">
                                {% if order_data.order[3] == 'normal' %}عادية
                                {% elif order_data.order[3] == 'high' %}عالية
                                {% elif order_data.order[3] == 'urgent' %}عاجلة
                                {% else %}{{ order_data.order[3] }}
                                {% endif %}
                            </span>
                        </span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">تاريخ الإنشاء:</span>
                        <span class="info-value">{{ order_data.order[4].strftime('%Y-%m-%d %H:%M') if order_data.order[4] else 'غير محدد' }}</span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">الموعد المتوقع:</span>
                        <span class="info-value">{{ order_data.order[5].strftime('%Y-%m-%d') if order_data.order[5] else 'غير محدد' }}</span>
                    </div>
                    
                    {% if order_data.order[6] %}
                    <div class="info-row">
                        <span class="info-label">تاريخ الإنجاز:</span>
                        <span class="info-value">{{ order_data.order[6].strftime('%Y-%m-%d %H:%M') }}</span>
                    </div>
                    {% endif %}
                    
                    {% if order_data.order[7] %}
                    <div class="info-row">
                        <span class="info-label">تعليمات خاصة:</span>
                        <span class="info-value">{{ order_data.order[7] }}</span>
                    </div>
                    {% endif %}
                </div>

                <!-- معلومات الشحنة -->
                <div class="details-card">
                    <h5 class="mb-4">
                        <i class="fas fa-ship text-info me-2"></i>
                        معلومات الشحنة
                    </h5>
                    
                    <div class="info-row">
                        <span class="info-label">رقم التتبع:</span>
                        <span class="info-value"><strong>{{ order_data.order[17] or 'غير محدد' }}</strong></span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">رقم الشحنة:</span>
                        <span class="info-value">{{ order_data.order[18] or 'غير محدد' }}</span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">ميناء التحميل:</span>
                        <span class="info-value">{{ order_data.order[19] or 'غير محدد' }}</span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">ميناء التفريغ:</span>
                        <span class="info-value">{{ order_data.order[20] or 'غير محدد' }}</span>
                    </div>
                    
                    {% if order_data.order[22] %}
                    <div class="info-row">
                        <span class="info-label">وصف البضاعة:</span>
                        <span class="info-value">{{ order_data.order[22] }}</span>
                    </div>
                    {% endif %}
                    
                    {% if order_data.order[24] %}
                    <div class="info-row">
                        <span class="info-label">الوزن الإجمالي:</span>
                        <span class="info-value">{{ order_data.order[24] }} كجم</span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- الإجراءات والمعلومات الإضافية -->
            <div class="col-lg-4">
                <!-- أزرار الإجراءات -->
                <div class="details-card">
                    <h6 class="mb-3">
                        <i class="fas fa-cogs text-secondary me-2"></i>
                        الإجراءات المتاحة
                    </h6>
                    
                    <div class="action-buttons">
                        {% if order_data.order[2] == 'draft' %}
                        <button class="btn btn-update-status" onclick="updateStatus('sent')">
                            <i class="fas fa-paper-plane me-2"></i>
                            إرسال الأمر
                        </button>
                        {% elif order_data.order[2] == 'sent' %}
                        <button class="btn btn-update-status" onclick="updateStatus('in_progress')">
                            <i class="fas fa-play me-2"></i>
                            بدء التنفيذ
                        </button>
                        {% elif order_data.order[2] == 'in_progress' %}
                        <button class="btn btn-update-status" onclick="updateStatus('completed')">
                            <i class="fas fa-check me-2"></i>
                            إنجاز الأمر
                        </button>
                        {% endif %}
                        
                        <button class="btn btn-outline-primary">
                            <i class="fas fa-upload me-2"></i>
                            رفع وثيقة
                        </button>
                        
                        <button class="btn btn-outline-info">
                            <i class="fas fa-print me-2"></i>
                            طباعة
                        </button>
                    </div>
                </div>

                <!-- الوثائق -->
                <div class="details-card">
                    <h6 class="mb-3">
                        <i class="fas fa-folder text-warning me-2"></i>
                        الوثائق المطلوبة
                    </h6>
                    
                    {% if order_data.documents %}
                        {% for doc in order_data.documents %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>{{ doc[2] }}</span>
                            {% if doc[4] %}
                                <span class="badge bg-success">متوفر</span>
                            {% else %}
                                <span class="badge bg-warning">مطلوب</span>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">لا توجد وثائق مطلوبة</p>
                    {% endif %}
                </div>

                <!-- معلومات سريعة -->
                <div class="details-card">
                    <h6 class="mb-3">
                        <i class="fas fa-info text-info me-2"></i>
                        معلومات سريعة
                    </h6>
                    
                    <div class="small">
                        <div class="d-flex justify-content-between mb-2">
                            <span>ID الأمر:</span>
                            <span>{{ order_data.order[0] }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>ID الشحنة:</span>
                            <span>{{ order_data.order[16] }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>حالة الشحنة:</span>
                            <span>{{ order_data.order[21] or 'غير محدد' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار التنقل -->
        <div class="row">
            <div class="col-12">
                <div class="details-card">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('agent_portal.orders') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للأوامر
                        </a>
                        
                        <a href="{{ url_for('agent_portal.dashboard') }}" class="btn btn-outline-primary">
                            <i class="fas fa-home me-2"></i>
                            لوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateStatus(newStatus) {
    if (confirm('هل أنت متأكد من تحديث حالة الأمر؟')) {
        fetch('/agent-portal/api/update-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                order_id: {{ order_data.order[0] }},
                new_status: newStatus
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم تحديث حالة الأمر بنجاح');
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تحديث الحالة');
        });
    }
}
</script>
{% endblock %}
