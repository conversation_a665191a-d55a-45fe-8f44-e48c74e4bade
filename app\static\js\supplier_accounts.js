/**
 * نظام إدارة حسابات الموردين - JavaScript
 * Supplier Accounts Management System - JavaScript
 */

// متغيرات عامة
let accountsData = {
    accounts: [],
    filteredAccounts: [],
    currentPage: 1,
    itemsPerPage: 10,
    totalPages: 0,
    filters: {
        search: '',
        status: 'all',
        type: 'all',
        risk: 'all',
        currency: 'all'
    }
};

/**
 * تحميل بيانات الحسابات
 */
function loadAccountsData() {
    showLoadingState();
    
    $.ajax({
        url: '/suppliers/api/accounts',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                accountsData.accounts = response.accounts || [];
                accountsData.filteredAccounts = [...accountsData.accounts];
                updateStatistics(response.statistics || {});
                displayAccounts();
                updatePagination();
            } else {
                showError('خطأ في تحميل بيانات الحسابات: ' + response.message);
                showEmptyState();
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في تحميل بيانات الحسابات:', error);
            showError('خطأ في الاتصال بالخادم');
            loadSampleData(); // تحميل بيانات تجريبية للاختبار
        }
    });
}

/**
 * تحميل بيانات تجريبية للاختبار
 */
function loadSampleData() {
    const sampleAccounts = [
        {
            account_id: 1,
            supplier_code: 'SUP001',
            supplier_name: 'شركة الخليج للتجارة',
            account_number: 'SUP20240001',
            account_type: 'TRADE',
            account_status: 'ACTIVE',
            risk_rating: 'LOW',
            currency_code: 'SAR',
            credit_limit: 500000.00,
            current_balance: 150000.00,
            payment_terms_days: 30,
            contact_person: 'أحمد محمد',
            phone: '+************',
            email: '<EMAIL>',
            last_transaction_date: '2024-09-01',
            total_transactions: 45,
            avg_payment_days: 28.5
        },
        {
            account_id: 2,
            supplier_code: 'SUP002',
            supplier_name: 'مؤسسة النور للخدمات',
            account_number: 'SUP20240002',
            account_type: 'SERVICE',
            account_status: 'ACTIVE',
            risk_rating: 'MEDIUM',
            currency_code: 'SAR',
            credit_limit: 200000.00,
            current_balance: -25000.00,
            payment_terms_days: 15,
            contact_person: 'فاطمة علي',
            phone: '+************',
            email: '<EMAIL>',
            last_transaction_date: '2024-08-28',
            total_transactions: 32,
            avg_payment_days: 18.2
        },
        {
            account_id: 3,
            supplier_code: 'SUP003',
            supplier_name: 'شركة البناء المتطور',
            account_number: 'SUP20240003',
            account_type: 'CONTRACTOR',
            account_status: 'SUSPENDED',
            risk_rating: 'HIGH',
            currency_code: 'SAR',
            credit_limit: 1000000.00,
            current_balance: 75000.00,
            payment_terms_days: 45,
            contact_person: 'محمد سالم',
            phone: '+************',
            email: '<EMAIL>',
            last_transaction_date: '2024-09-03',
            total_transactions: 18,
            avg_payment_days: 52.1
        }
    ];
    
    const sampleStats = {
        total_accounts: 3,
        active_accounts: 2,
        high_risk_accounts: 1,
        suspended_accounts: 1
    };
    
    accountsData.accounts = sampleAccounts;
    accountsData.filteredAccounts = [...sampleAccounts];
    updateStatistics(sampleStats);
    displayAccounts();
    updatePagination();
}

/**
 * تحديث الإحصائيات
 */
function updateStatistics(stats) {
    $('#totalAccounts').text(stats.total_accounts || 0);
    $('#activeAccounts').text(stats.active_accounts || 0);
    $('#highRiskAccounts').text(stats.high_risk_accounts || 0);
    $('#suspendedAccounts').text(stats.suspended_accounts || 0);
}

/**
 * عرض الحسابات
 */
function displayAccounts() {
    const container = $('#accountsList');
    
    if (!accountsData.filteredAccounts || accountsData.filteredAccounts.length === 0) {
        showEmptyState();
        return;
    }
    
    // حساب البيانات للصفحة الحالية
    const startIndex = (accountsData.currentPage - 1) * accountsData.itemsPerPage;
    const endIndex = startIndex + accountsData.itemsPerPage;
    const pageAccounts = accountsData.filteredAccounts.slice(startIndex, endIndex);
    
    let html = '';
    pageAccounts.forEach(account => {
        html += createAccountCard(account);
    });
    
    container.html(html);
    updatePaginationInfo();
}

/**
 * إنشاء بطاقة حساب
 */
function createAccountCard(account) {
    const riskClass = getRiskClass(account.risk_rating);
    const statusClass = getStatusClass(account.account_status);
    const statusText = getStatusText(account.account_status);
    const riskText = getRiskText(account.risk_rating);
    const balanceClass = account.current_balance >= 0 ? 'text-success' : 'text-danger';
    
    return `
        <div class="account-card ${riskClass}">
            <div class="account-header">
                <div class="account-info">
                    <h5>${account.supplier_name}</h5>
                    <small class="text-muted">
                        ${account.supplier_code} | ${account.account_number}
                        <span class="risk-badge risk-${account.risk_rating.toLowerCase()}">${riskText}</span>
                    </small>
                </div>
                <div class="account-status">
                    <span class="status-badge status-${account.account_status.toLowerCase()}">${statusText}</span>
                </div>
            </div>
            
            <div class="account-metrics">
                <div class="metric-item">
                    <div class="metric-value ${balanceClass}">
                        ${formatCurrency(account.current_balance)}
                    </div>
                    <div class="metric-label">الرصيد الحالي</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value text-info">
                        ${formatCurrency(account.credit_limit)}
                    </div>
                    <div class="metric-label">حد الائتمان</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value text-primary">
                        ${account.total_transactions || 0}
                    </div>
                    <div class="metric-label">عدد المعاملات</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value text-warning">
                        ${account.avg_payment_days ? account.avg_payment_days.toFixed(1) : 0} يوم
                    </div>
                    <div class="metric-label">متوسط أيام الدفع</div>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <small class="text-muted">
                        <i class="fas fa-user"></i> ${account.contact_person || 'غير محدد'}
                    </small>
                </div>
                <div class="col-md-6">
                    <small class="text-muted">
                        <i class="fas fa-phone"></i> ${account.phone || 'غير محدد'}
                    </small>
                </div>
            </div>
            
            <div class="account-actions">
                <button class="btn btn-primary btn-sm-custom" onclick="viewAccountDetails(${account.account_id})">
                    <i class="fas fa-eye"></i> عرض التفاصيل
                </button>
                <button class="btn btn-success btn-sm-custom" onclick="viewTransactions(${account.account_id})">
                    <i class="fas fa-list"></i> المعاملات
                </button>
                <button class="btn btn-info btn-sm-custom" onclick="generateStatement(${account.account_id})">
                    <i class="fas fa-file-alt"></i> كشف حساب
                </button>
                <button class="btn btn-warning btn-sm-custom" onclick="editAccount(${account.account_id})">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                ${account.account_status === 'ACTIVE' ? 
                    `<button class="btn btn-danger btn-sm-custom" onclick="suspendAccount(${account.account_id})">
                        <i class="fas fa-pause"></i> تعليق
                    </button>` :
                    `<button class="btn btn-success btn-sm-custom" onclick="activateAccount(${account.account_id})">
                        <i class="fas fa-play"></i> تفعيل
                    </button>`
                }
            </div>
        </div>
    `;
}

/**
 * فلترة الحسابات
 */
function filterAccounts() {
    // جمع معايير الفلترة
    accountsData.filters = {
        search: $('#searchInput').val().toLowerCase(),
        status: $('#statusFilter').val(),
        type: $('#typeFilter').val(),
        risk: $('#riskFilter').val(),
        currency: $('#currencyFilter').val()
    };
    
    // تطبيق الفلاتر
    accountsData.filteredAccounts = accountsData.accounts.filter(account => {
        // فلتر البحث
        if (accountsData.filters.search) {
            const searchText = accountsData.filters.search;
            const matchesSearch = 
                account.supplier_name.toLowerCase().includes(searchText) ||
                account.supplier_code.toLowerCase().includes(searchText) ||
                account.account_number.toLowerCase().includes(searchText);
            if (!matchesSearch) return false;
        }
        
        // فلتر الحالة
        if (accountsData.filters.status !== 'all' && account.account_status !== accountsData.filters.status) {
            return false;
        }
        
        // فلتر النوع
        if (accountsData.filters.type !== 'all' && account.account_type !== accountsData.filters.type) {
            return false;
        }
        
        // فلتر المخاطر
        if (accountsData.filters.risk !== 'all' && account.risk_rating !== accountsData.filters.risk) {
            return false;
        }
        
        // فلتر العملة
        if (accountsData.filters.currency !== 'all' && account.currency_code !== accountsData.filters.currency) {
            return false;
        }
        
        return true;
    });
    
    // إعادة تعيين الصفحة الحالية
    accountsData.currentPage = 1;
    
    // عرض النتائج
    displayAccounts();
    updatePagination();
}

/**
 * مسح الفلاتر
 */
function clearFilters() {
    $('#searchInput').val('');
    $('#statusFilter').val('all');
    $('#typeFilter').val('all');
    $('#riskFilter').val('all');
    $('#currencyFilter').val('all');
    
    accountsData.filteredAccounts = [...accountsData.accounts];
    accountsData.currentPage = 1;
    
    displayAccounts();
    updatePagination();
}

/**
 * تحديث معلومات الصفحات
 */
function updatePaginationInfo() {
    const total = accountsData.filteredAccounts.length;
    const startIndex = (accountsData.currentPage - 1) * accountsData.itemsPerPage + 1;
    const endIndex = Math.min(startIndex + accountsData.itemsPerPage - 1, total);
    
    $('#showingFrom').text(total > 0 ? startIndex : 0);
    $('#showingTo').text(total > 0 ? endIndex : 0);
    $('#totalRecords').text(total);
}

/**
 * تحديث الصفحات
 */
function updatePagination() {
    const total = accountsData.filteredAccounts.length;
    accountsData.totalPages = Math.ceil(total / accountsData.itemsPerPage);
    
    const pagination = $('#pagination');
    let html = '';
    
    // زر السابق
    html += `
        <li class="page-item ${accountsData.currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${accountsData.currentPage - 1})">السابق</a>
        </li>
    `;
    
    // أرقام الصفحات
    for (let i = 1; i <= accountsData.totalPages; i++) {
        if (i === accountsData.currentPage || 
            i === 1 || 
            i === accountsData.totalPages || 
            (i >= accountsData.currentPage - 1 && i <= accountsData.currentPage + 1)) {
            html += `
                <li class="page-item ${i === accountsData.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `;
        } else if (i === accountsData.currentPage - 2 || i === accountsData.currentPage + 2) {
            html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    // زر التالي
    html += `
        <li class="page-item ${accountsData.currentPage === accountsData.totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${accountsData.currentPage + 1})">التالي</a>
        </li>
    `;
    
    pagination.html(html);
}

/**
 * تغيير الصفحة
 */
function changePage(page) {
    if (page < 1 || page > accountsData.totalPages) return;
    
    accountsData.currentPage = page;
    displayAccounts();
    updatePagination();
}

// دوال الإجراءات
function viewAccountDetails(accountId) {
    window.location.href = `/suppliers/account/${accountId}`;
}

function viewTransactions(accountId) {
    showNotification('سيتم فتح صفحة المعاملات قريباً', 'info');
}

function generateStatement(accountId) {
    showNotification('سيتم إنشاء كشف الحساب قريباً', 'info');
}

function editAccount(accountId) {
    showNotification('سيتم فتح نافذة التعديل قريباً', 'info');
}

function suspendAccount(accountId) {
    if (confirm('هل أنت متأكد من تعليق هذا الحساب؟')) {
        showNotification('تم تعليق الحساب', 'warning');
    }
}

function activateAccount(accountId) {
    if (confirm('هل أنت متأكد من تفعيل هذا الحساب؟')) {
        showNotification('تم تفعيل الحساب', 'success');
    }
}

function showCreateAccountModal() {
    $('#createAccountModal').modal('show');
}

function createAccount() {
    // جمع بيانات النموذج
    const formData = {
        supplier_code: $('#supplierCode').val(),
        supplier_name: $('#supplierName').val(),
        account_type: $('#accountType').val(),
        currency_code: $('#currencyCode').val(),
        credit_limit: $('#creditLimit').val(),
        payment_terms_days: $('#paymentTerms').val(),
        discount_percentage: $('#discountPercentage').val(),
        contact_person: $('#contactPerson').val(),
        phone: $('#phone').val(),
        email: $('#email').val(),
        tax_number: $('#taxNumber').val(),
        address: $('#address').val()
    };
    
    // التحقق من البيانات المطلوبة
    if (!formData.supplier_code || !formData.supplier_name) {
        showNotification('يرجى ملء الحقول المطلوبة', 'warning');
        return;
    }
    
    showNotification('سيتم إنشاء الحساب قريباً', 'info');
    $('#createAccountModal').modal('hide');
}

function refreshAccounts() {
    showNotification('جاري تحديث البيانات...', 'info');
    loadAccountsData();
}

function exportAccounts() {
    showNotification('سيتم تصدير البيانات قريباً', 'info');
}

// دوال مساعدة
function showLoadingState() {
    $('#accountsList').html(`
        <div class="loading-container">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `);
}

function showEmptyState() {
    $('#accountsList').html(`
        <div class="empty-state">
            <i class="fas fa-inbox"></i>
            <h5>لا توجد حسابات</h5>
            <p class="text-muted">لم يتم العثور على حسابات موردين مطابقة للمعايير المحددة</p>
            <button class="btn btn-primary" onclick="clearFilters()">مسح الفلاتر</button>
        </div>
    `);
}

function showError(message) {
    showNotification(message, 'error');
}

function getRiskClass(risk) {
    const riskMap = {
        'LOW': 'low-risk',
        'MEDIUM': 'medium-risk',
        'HIGH': 'high-risk',
        'CRITICAL': 'high-risk'
    };
    return riskMap[risk] || '';
}

function getStatusClass(status) {
    return status.toLowerCase();
}

function getStatusText(status) {
    const statusMap = {
        'ACTIVE': 'نشط',
        'SUSPENDED': 'معلق',
        'CLOSED': 'مغلق'
    };
    return statusMap[status] || status;
}

function getRiskText(risk) {
    const riskMap = {
        'LOW': 'منخفض',
        'MEDIUM': 'متوسط',
        'HIGH': 'عالي',
        'CRITICAL': 'حرج'
    };
    return riskMap[risk] || risk;
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount || 0) + ' ر.س';
}

function showNotification(message, type = 'info') {
    // استخدام نظام الإشعارات الموجود في التطبيق
    if (typeof showAlert === 'function') {
        showAlert(message, type);
    } else {
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}
