{% extends "base.html" %}

{% block title %}الطلبات المعلقة - اختبار{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="fas fa-clock text-warning me-2"></i>
                الطلبات المعلقة - اختبار
            </h1>
            <p class="text-muted mb-0">اختبار تحميل الطلبات المعلقة</p>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">قائمة الطلبات المعلقة</h5>
                    <button class="btn btn-primary btn-sm" onclick="testAPI()">اختبار API</button>
                </div>
                <div class="card-body">
                    <!-- حالة التحميل -->
                    <div id="loadingStatus" class="alert alert-info">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        جاري التحميل...
                    </div>
                    
                    <!-- النتائج -->
                    <div id="results" style="display: none;">
                        <h6>النتائج:</h6>
                        <pre id="apiResponse"></pre>
                    </div>
                    
                    <!-- الجدول -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>المستفيد</th>
                                    <th>المبلغ</th>
                                    <th>العملة</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody id="requestsTable">
                                <tr>
                                    <td colspan="5" class="text-center">جاري التحميل...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// اختبار jQuery
console.log('🔍 اختبار jQuery:', typeof $ !== 'undefined' ? 'متاح' : 'غير متاح');
console.log('🔍 اختبار document.ready...');

function testAPI() {
    console.log('🚀 بدء اختبار API...');
    
    // إظهار حالة التحميل
    document.getElementById('loadingStatus').style.display = 'block';
    document.getElementById('results').style.display = 'none';
    
    // اختبار مع fetch API (بدلاً من jQuery)
    fetch('/transfers/api/requests?status=pending')
        .then(response => {
            console.log('📥 استجابة HTTP:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📋 بيانات API:', data);
            
            // إخفاء التحميل وإظهار النتائج
            document.getElementById('loadingStatus').style.display = 'none';
            document.getElementById('results').style.display = 'block';
            document.getElementById('apiResponse').textContent = JSON.stringify(data, null, 2);
            
            // ملء الجدول
            fillTable(data.data || []);
        })
        .catch(error => {
            console.error('❌ خطأ في API:', error);
            
            document.getElementById('loadingStatus').innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>
                خطأ: ${error.message}
            `;
            document.getElementById('loadingStatus').className = 'alert alert-danger';
        });
}

function fillTable(requests) {
    console.log(`📋 ملء الجدول بـ ${requests.length} طلب`);
    
    const tbody = document.getElementById('requestsTable');
    tbody.innerHTML = '';
    
    if (requests.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center">لا توجد طلبات معلقة</td></tr>';
        return;
    }
    
    requests.forEach(request => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${request.request_number || 'غير محدد'}</td>
            <td>${request.beneficiary_name || 'غير محدد'}</td>
            <td>${request.amount || 0}</td>
            <td>${request.currency || 'TRY'}</td>
            <td><span class="badge bg-warning">${request.status || 'غير محدد'}</span></td>
        `;
        tbody.appendChild(row);
    });
    
    console.log('✅ تم ملء الجدول بنجاح');
}

// تشغيل تلقائي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 تم تحميل الصفحة');
    setTimeout(testAPI, 1000); // تأخير ثانية واحدة
});

// اختبار jQuery إذا كان متاحاً
if (typeof $ !== 'undefined') {
    $(document).ready(function() {
        console.log('✅ jQuery يعمل بشكل صحيح');
        setTimeout(testAPI, 2000); // تأخير ثانيتين
    });
} else {
    console.warn('⚠️ jQuery غير متاح');
}
</script>

{% endblock %}
