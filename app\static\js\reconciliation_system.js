/**
 * نظام مطابقة أرصدة الموردين - JavaScript
 * Supplier Balance Reconciliation System - JavaScript
 */

// متغيرات عامة
let reconciliationData = {
    cycles: [],
    reconciliations: [],
    summary: {}
};

/**
 * تحميل ملخص المطابقة
 */
function loadReconciliationSummary() {
    $.ajax({
        url: '/api/suppliers/reconciliation/reports/summary',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                updateSummaryCards(response.summary);
                reconciliationData.summary = response.summary;
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في تحميل ملخص المطابقة:', error);
            showNotification('خطأ في تحميل ملخص المطابقة', 'error');
        }
    });
}

/**
 * تحديث بطاقات الملخص
 */
function updateSummaryCards(summary) {
    $('#totalCycles').text(summary.total_cycles || 0);
    $('#matchedCount').text(summary.matched_count || 0);
    $('#unmatchedCount').text(summary.unmatched_count || 0);
    $('#totalDifferences').text(formatCurrency(summary.total_differences_amount || 0));
}

/**
 * تحميل دورات المطابقة
 */
function loadReconciliationCycles() {
    const statusFilter = $('#cycleStatusFilter').val();
    const dateFrom = $('#cycleDateFromFilter').val();
    const dateTo = $('#cycleDateToFilter').val();
    
    const params = new URLSearchParams();
    if (statusFilter) params.append('status', statusFilter);
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);
    
    $.ajax({
        url: '/api/suppliers/reconciliation/cycles?' + params.toString(),
        method: 'GET',
        success: function(response) {
            if (response.success) {
                reconciliationData.cycles = response.cycles;
                populateCyclesTable(response.cycles);
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في تحميل دورات المطابقة:', error);
            showNotification('خطأ في تحميل دورات المطابقة', 'error');
        }
    });
}

/**
 * ملء جدول دورات المطابقة
 */
function populateCyclesTable(cycles) {
    cyclesTable.clear();
    
    cycles.forEach(cycle => {
        const statusBadge = getCycleStatusBadge(cycle.status);
        const periodText = `${cycle.period_from} إلى ${cycle.period_to}`;
        const matchRate = cycle.total_suppliers > 0 ? 
            Math.round((cycle.matched_suppliers / cycle.total_suppliers) * 100) : 0;
        
        const actionsHtml = `
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-primary" onclick="viewCycleDetails(${cycle.cycle_id})" 
                        title="عرض التفاصيل">
                    <i class="fas fa-eye"></i>
                </button>
                ${cycle.status === 'OPEN' ? `
                    <button class="btn btn-outline-success" onclick="startReconciliationCycle(${cycle.cycle_id})" 
                            title="بدء المطابقة">
                        <i class="fas fa-play"></i>
                    </button>
                ` : ''}
                ${cycle.status === 'IN_PROGRESS' ? `
                    <button class="btn btn-outline-info" onclick="completeCycle(${cycle.cycle_id})" 
                            title="إكمال الدورة">
                        <i class="fas fa-check"></i>
                    </button>
                ` : ''}
                <button class="btn btn-outline-secondary" onclick="exportCycleReport(${cycle.cycle_id})" 
                        title="تصدير التقرير">
                    <i class="fas fa-download"></i>
                </button>
            </div>
        `;
        
        cyclesTable.row.add([
            cycle.cycle_name,
            cycle.reconciliation_date,
            periodText,
            getCycleTypeText(cycle.cycle_type),
            statusBadge,
            cycle.total_suppliers,
            `${cycle.matched_suppliers} (${matchRate}%)`,
            cycle.unmatched_suppliers,
            formatCurrency(cycle.total_differences_amount),
            actionsHtml
        ]);
    });
    
    cyclesTable.draw();
}

/**
 * إنشاء دورة مطابقة جديدة
 */
function createReconciliationCycle() {
    const formData = {
        cycle_name: $('#cycleName').val(),
        cycle_type: $('#cycleType').val(),
        period_from: $('#periodFrom').val(),
        period_to: $('#periodTo').val(),
        notes: $('#cycleNotes').val()
    };
    
    // التحقق من البيانات
    if (!formData.cycle_name || !formData.period_from || !formData.period_to) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }
    
    if (new Date(formData.period_from) >= new Date(formData.period_to)) {
        showNotification('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'warning');
        return;
    }
    
    $.ajax({
        url: '/api/suppliers/reconciliation/cycles',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                showNotification('تم إنشاء دورة المطابقة بنجاح', 'success');
                $('#createCycleModal').modal('hide');
                $('#createCycleForm')[0].reset();
                loadReconciliationCycles();
                loadReconciliationSummary();
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في إنشاء دورة المطابقة:', error);
            const errorMessage = xhr.responseJSON?.message || 'خطأ في إنشاء دورة المطابقة';
            showNotification(errorMessage, 'error');
        }
    });
}

/**
 * بدء عملية المطابقة
 */
function startReconciliationCycle(cycleId) {
    if (!confirm('هل أنت متأكد من بدء عملية المطابقة؟ سيتم إنشاء سجلات مطابقة لجميع الموردين النشطين.')) {
        return;
    }
    
    $.ajax({
        url: `/api/suppliers/reconciliation/cycles/${cycleId}/start`,
        method: 'POST',
        success: function(response) {
            if (response.success) {
                showNotification('تم بدء عملية المطابقة بنجاح', 'success');
                loadReconciliationCycles();
                loadReconciliationSummary();
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في بدء عملية المطابقة:', error);
            const errorMessage = xhr.responseJSON?.message || 'خطأ في بدء عملية المطابقة';
            showNotification(errorMessage, 'error');
        }
    });
}

/**
 * تحميل خيارات الدورات
 */
function loadCycleOptions() {
    $.ajax({
        url: '/api/suppliers/reconciliation/cycles',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const select = $('#selectedCycleFilter');
                select.empty().append('<option value="">اختر دورة المطابقة</option>');
                
                response.cycles.forEach(cycle => {
                    if (cycle.status !== 'OPEN') {
                        select.append(`<option value="${cycle.cycle_id}">${cycle.cycle_name}</option>`);
                    }
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في تحميل خيارات الدورات:', error);
        }
    });
}

/**
 * تحميل مطابقات دورة معينة
 */
function loadCycleReconciliations() {
    const cycleId = $('#selectedCycleFilter').val();
    if (!cycleId) {
        reconciliationsTable.clear().draw();
        return;
    }
    
    currentCycleId = cycleId;
    
    $.ajax({
        url: `/api/suppliers/reconciliation/cycles/${cycleId}/reconciliations`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                reconciliationData.reconciliations = response.reconciliations;
                populateReconciliationsTable(response.reconciliations);
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في تحميل مطابقات الدورة:', error);
            showNotification('خطأ في تحميل مطابقات الدورة', 'error');
        }
    });
}

/**
 * ملء جدول المطابقات التفصيلية
 */
function populateReconciliationsTable(reconciliations) {
    reconciliationsTable.clear();
    
    reconciliations.forEach(rec => {
        const statusBadge = getReconciliationStatusBadge(rec.reconciliation_status);
        const differenceClass = getDifferenceClass(rec.differences.total);
        const differenceText = `<span class="difference-amount ${differenceClass}">
            ${formatCurrency(rec.differences.total)} ${rec.currency_code}
        </span>`;
        
        const actionsHtml = `
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-primary" onclick="viewReconciliationDetails(${rec.reconciliation_id})" 
                        title="عرض التفاصيل">
                    <i class="fas fa-eye"></i>
                </button>
                ${rec.reconciliation_status === 'PENDING' || rec.reconciliation_status === 'UNMATCHED' ? `
                    <button class="btn btn-outline-warning" onclick="updateSupplierStatement(${rec.reconciliation_id})" 
                            title="تحديث كشف المورد">
                        <i class="fas fa-edit"></i>
                    </button>
                ` : ''}
                ${rec.differences_count > 0 ? `
                    <button class="btn btn-outline-info" onclick="analyzeDifferences(${rec.reconciliation_id})" 
                            title="تحليل الفروقات">
                        <i class="fas fa-search"></i>
                    </button>
                ` : ''}
            </div>
        `;
        
        reconciliationsTable.row.add([
            `${rec.supplier_name} (${rec.supplier_code})`,
            `${rec.currency_code} ${rec.currency_symbol}`,
            formatCurrency(rec.system_balances.closing),
            formatCurrency(rec.supplier_balances.closing),
            differenceText,
            statusBadge,
            `${rec.differences_count} (${rec.resolved_differences} محلولة)`,
            rec.supplier_statement_date || '-',
            actionsHtml
        ]);
    });
    
    reconciliationsTable.draw();
}

/**
 * عرض تفاصيل المطابقة
 */
function viewReconciliationDetails(reconciliationId) {
    currentReconciliationId = reconciliationId;
    
    // البحث عن المطابقة في البيانات المحملة
    const reconciliation = reconciliationData.reconciliations.find(r => r.reconciliation_id === reconciliationId);
    if (!reconciliation) {
        showNotification('لم يتم العثور على بيانات المطابقة', 'error');
        return;
    }
    
    // إنشاء محتوى التفاصيل
    const detailsHtml = createReconciliationDetailsHtml(reconciliation);
    $('#reconciliationDetailsContent').html(detailsHtml);
    
    // تحميل تفاصيل الفروقات
    loadReconciliationDifferences(reconciliationId);
    
    $('#reconciliationDetailsModal').modal('show');
}

/**
 * إنشاء HTML لتفاصيل المطابقة
 */
function createReconciliationDetailsHtml(reconciliation) {
    return `
        <div class="row">
            <div class="col-md-6">
                <h6>معلومات المورد</h6>
                <div class="balance-comparison">
                    <div class="balance-row">
                        <span>اسم المورد:</span>
                        <strong>${reconciliation.supplier_name}</strong>
                    </div>
                    <div class="balance-row">
                        <span>كود المورد:</span>
                        <strong>${reconciliation.supplier_code}</strong>
                    </div>
                    <div class="balance-row">
                        <span>العملة:</span>
                        <strong>${reconciliation.currency_code} ${reconciliation.currency_symbol}</strong>
                    </div>
                    <div class="balance-row">
                        <span>حالة المطابقة:</span>
                        ${getReconciliationStatusBadge(reconciliation.reconciliation_status)}
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <h6>مقارنة الأرصدة</h6>
                <div class="balance-comparison">
                    <div class="balance-row">
                        <span>رصيد افتتاحي (النظام):</span>
                        <strong>${formatCurrency(reconciliation.system_balances.opening)}</strong>
                    </div>
                    <div class="balance-row">
                        <span>رصيد افتتاحي (المورد):</span>
                        <strong>${formatCurrency(reconciliation.supplier_balances.opening)}</strong>
                    </div>
                    <div class="balance-row">
                        <span>الفرق الافتتاحي:</span>
                        <span class="difference-amount ${getDifferenceClass(reconciliation.differences.opening)}">
                            ${formatCurrency(reconciliation.differences.opening)}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-12">
                <h6>تفاصيل الحركات</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>البيان</th>
                                <th>النظام</th>
                                <th>المورد</th>
                                <th>الفرق</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>الرصيد الافتتاحي</td>
                                <td>${formatCurrency(reconciliation.system_balances.opening)}</td>
                                <td>${formatCurrency(reconciliation.supplier_balances.opening)}</td>
                                <td class="difference-amount ${getDifferenceClass(reconciliation.differences.opening)}">
                                    ${formatCurrency(reconciliation.differences.opening)}
                                </td>
                            </tr>
                            <tr>
                                <td>إجمالي المدين</td>
                                <td>${formatCurrency(reconciliation.system_balances.debit)}</td>
                                <td>${formatCurrency(reconciliation.supplier_balances.debit)}</td>
                                <td class="difference-amount ${getDifferenceClass(reconciliation.differences.debit)}">
                                    ${formatCurrency(reconciliation.differences.debit)}
                                </td>
                            </tr>
                            <tr>
                                <td>إجمالي الدائن</td>
                                <td>${formatCurrency(reconciliation.system_balances.credit)}</td>
                                <td>${formatCurrency(reconciliation.supplier_balances.credit)}</td>
                                <td class="difference-amount ${getDifferenceClass(reconciliation.differences.credit)}">
                                    ${formatCurrency(reconciliation.differences.credit)}
                                </td>
                            </tr>
                            <tr class="table-warning">
                                <td><strong>الرصيد الختامي</strong></td>
                                <td><strong>${formatCurrency(reconciliation.system_balances.closing)}</strong></td>
                                <td><strong>${formatCurrency(reconciliation.supplier_balances.closing)}</strong></td>
                                <td class="difference-amount ${getDifferenceClass(reconciliation.differences.total)}">
                                    <strong>${formatCurrency(reconciliation.differences.total)}</strong>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-12">
                <h6>تفاصيل الفروقات</h6>
                <div id="differencesDetails">
                    <div class="text-center py-3">
                        <i class="fas fa-spinner fa-spin"></i> جاري تحميل تفاصيل الفروقات...
                    </div>
                </div>
            </div>
        </div>
    `;
}

// دوال مساعدة
function getCycleStatusBadge(status) {
    const statusMap = {
        'OPEN': { class: 'cycle-open', text: 'مفتوحة' },
        'IN_PROGRESS': { class: 'cycle-in-progress', text: 'قيد التنفيذ' },
        'COMPLETED': { class: 'cycle-completed', text: 'مكتملة' },
        'CANCELLED': { class: 'cycle-cancelled', text: 'ملغية' }
    };
    
    const statusInfo = statusMap[status] || { class: 'cycle-open', text: status };
    return `<span class="badge cycle-status-badge ${statusInfo.class}">${statusInfo.text}</span>`;
}

function getReconciliationStatusBadge(status) {
    const statusMap = {
        'PENDING': { class: 'status-pending', text: 'معلقة' },
        'MATCHED': { class: 'status-matched', text: 'متطابقة' },
        'UNMATCHED': { class: 'status-unmatched', text: 'غير متطابقة' },
        'ADJUSTED': { class: 'status-adjusted', text: 'معدلة' },
        'APPROVED': { class: 'status-approved', text: 'معتمدة' }
    };
    
    const statusInfo = statusMap[status] || { class: 'status-pending', text: status };
    return `<span class="badge reconciliation-status-badge ${statusInfo.class}">${statusInfo.text}</span>`;
}

function getCycleTypeText(type) {
    const typeMap = {
        'MONTHLY': 'شهرية',
        'QUARTERLY': 'ربع سنوية',
        'YEARLY': 'سنوية',
        'ADHOC': 'خاصة'
    };
    return typeMap[type] || type;
}

function getDifferenceClass(amount) {
    if (amount > 0) return 'difference-positive';
    if (amount < 0) return 'difference-negative';
    return 'difference-zero';
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount || 0);
}

// دوال إضافية سيتم تطويرها
function applyCycleFilters() {
    loadReconciliationCycles();
}

function refreshData() {
    loadReconciliationSummary();
    loadReconciliationCycles();
    if (currentCycleId) {
        loadCycleReconciliations();
    }
}

function updatePeriodDates(cycleType) {
    // تحديث التواريخ بناءً على نوع الدورة
    const today = new Date();
    let fromDate, toDate;
    
    switch(cycleType) {
        case 'MONTHLY':
            fromDate = new Date(today.getFullYear(), today.getMonth(), 1);
            toDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            break;
        case 'QUARTERLY':
            const quarter = Math.floor(today.getMonth() / 3);
            fromDate = new Date(today.getFullYear(), quarter * 3, 1);
            toDate = new Date(today.getFullYear(), (quarter + 1) * 3, 0);
            break;
        case 'YEARLY':
            fromDate = new Date(today.getFullYear(), 0, 1);
            toDate = new Date(today.getFullYear(), 11, 31);
            break;
        default:
            return; // لا تغيير للنوع الخاص
    }
    
    $('#periodFrom').val(fromDate.toISOString().split('T')[0]);
    $('#periodTo').val(toDate.toISOString().split('T')[0]);
}

function showNotification(message, type = 'info') {
    // استخدام نظام الإشعارات الموجود في التطبيق
    if (typeof showAlert === 'function') {
        showAlert(message, type);
    } else {
        alert(message);
    }
}
