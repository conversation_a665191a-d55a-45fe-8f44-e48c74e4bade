"""
قوالب Excel للأرصدة الافتتاحية
Excel Templates for Opening Balances
"""

import pandas as pd
from io import BytesIO
import xlsxwriter
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class OpeningBalancesExcelTemplate:
    """فئة إنشاء قوالب Excel للأرصدة الافتتاحية"""
    
    def __init__(self):
        self.template_columns = [
            'كود المورد',
            'اسم المورد',
            'العملة',
            'مبلغ الرصيد الافتتاحي',
            'سعر الصرف',
            'المستند المرجعي',
            'ملاحظات'
        ]
        
        self.english_columns = [
            'supplier_code',
            'supplier_name',
            'currency_code',
            'opening_balance_amount',
            'exchange_rate',
            'reference_document',
            'notes'
        ]
    
    def create_template(self, suppliers_data=None):
        """إنشاء قالب Excel للاستيراد"""
        try:
            # إنشاء buffer للملف
            output = BytesIO()
            
            # إنشاء workbook
            workbook = xlsxwriter.Workbook(output, {'in_memory': True})
            
            # إنشاء الأوراق
            self._create_template_sheet(workbook, suppliers_data)
            self._create_instructions_sheet(workbook)
            self._create_validation_sheet(workbook)
            
            workbook.close()
            output.seek(0)
            
            return output
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء قالب Excel: {e}")
            raise
    
    def _create_template_sheet(self, workbook, suppliers_data):
        """إنشاء ورقة القالب الرئيسية"""
        worksheet = workbook.add_worksheet('الأرصدة الافتتاحية')
        
        # تنسيقات الخلايا
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#4472C4',
            'font_color': 'white',
            'align': 'center',
            'valign': 'vcenter',
            'border': 1,
            'font_size': 12
        })
        
        data_format = workbook.add_format({
            'align': 'center',
            'valign': 'vcenter',
            'border': 1,
            'font_size': 11
        })
        
        number_format = workbook.add_format({
            'align': 'center',
            'valign': 'vcenter',
            'border': 1,
            'num_format': '#,##0.00',
            'font_size': 11
        })
        
        # كتابة العناوين
        for col, header in enumerate(self.template_columns):
            worksheet.write(0, col, header, header_format)
        
        # تعيين عرض الأعمدة
        column_widths = [15, 30, 10, 20, 15, 20, 30]
        for col, width in enumerate(column_widths):
            worksheet.set_column(col, col, width)
        
        # إضافة بيانات الموردين إذا كانت متوفرة
        if suppliers_data:
            for row, supplier in enumerate(suppliers_data[:100], 1):  # أول 100 مورد
                worksheet.write(row, 0, supplier.get('code', ''), data_format)
                worksheet.write(row, 1, supplier.get('name', ''), data_format)
                worksheet.write(row, 2, 'SAR', data_format)
                worksheet.write(row, 3, 0.00, number_format)
                worksheet.write(row, 4, 1.0000, number_format)
                worksheet.write(row, 5, '', data_format)
                worksheet.write(row, 6, '', data_format)
        else:
            # إضافة صفوف فارغة للتعبئة
            for row in range(1, 101):
                for col in range(len(self.template_columns)):
                    if col == 2:  # العملة
                        worksheet.write(row, col, 'SAR', data_format)
                    elif col == 3:  # المبلغ
                        worksheet.write(row, col, 0.00, number_format)
                    elif col == 4:  # سعر الصرف
                        worksheet.write(row, col, 1.0000, number_format)
                    else:
                        worksheet.write(row, col, '', data_format)
        
        # إضافة تجميد للصف الأول
        worksheet.freeze_panes(1, 0)
        
        # إضافة فلتر تلقائي
        worksheet.autofilter(0, 0, 100, len(self.template_columns) - 1)
    
    def _create_instructions_sheet(self, workbook):
        """إنشاء ورقة التعليمات"""
        worksheet = workbook.add_worksheet('تعليمات الاستخدام')
        
        # تنسيقات
        title_format = workbook.add_format({
            'bold': True,
            'font_size': 16,
            'bg_color': '#D9E1F2',
            'align': 'center',
            'valign': 'vcenter'
        })
        
        header_format = workbook.add_format({
            'bold': True,
            'font_size': 14,
            'bg_color': '#E7E6E6',
            'align': 'right',
            'valign': 'vcenter'
        })
        
        text_format = workbook.add_format({
            'font_size': 11,
            'align': 'right',
            'valign': 'top',
            'text_wrap': True
        })
        
        # العنوان الرئيسي
        worksheet.merge_range('A1:F1', 'تعليمات استيراد الأرصدة الافتتاحية للموردين', title_format)
        
        # التعليمات
        instructions = [
            ('خطوات الاستيراد:', [
                '1. املأ البيانات في ورقة "الأرصدة الافتتاحية"',
                '2. تأكد من صحة أكواد الموردين',
                '3. تأكد من صحة رموز العملات',
                '4. احفظ الملف بصيغة Excel (.xlsx)',
                '5. ارفع الملف من خلال النظام'
            ]),
            ('الحقول المطلوبة:', [
                '• كود المورد: يجب أن يكون موجوداً في النظام',
                '• اسم المورد: للمراجعة فقط',
                '• العملة: SAR, USD, EUR',
                '• مبلغ الرصيد: رقم موجب للمدين، سالب للدائن',
                '• سعر الصرف: افتراضي 1 للريال السعودي'
            ]),
            ('ملاحظات مهمة:', [
                '• لا تحذف أو تعدل أسماء الأعمدة',
                '• استخدم الأرقام الإنجليزية فقط',
                '• تأكد من عدم وجود خلايا فارغة في الحقول المطلوبة',
                '• الحد الأقصى 1000 سجل في الملف الواحد',
                '• سيتم تجاهل الصفوف الفارغة تلقائياً'
            ]),
            ('رموز العملات المدعومة:', [
                'SAR - الريال السعودي',
                'USD - الدولار الأمريكي',
                'EUR - اليورو',
                'GBP - الجنيه الإسترليني',
                'AED - الدرهم الإماراتي'
            ])
        ]
        
        row = 3
        for section_title, items in instructions:
            worksheet.write(row, 0, section_title, header_format)
            row += 1
            
            for item in items:
                worksheet.write(row, 1, item, text_format)
                row += 1
            
            row += 1
        
        # تعيين عرض الأعمدة
        worksheet.set_column('A:A', 25)
        worksheet.set_column('B:F', 50)
        
        # تعيين ارتفاع الصفوف
        for i in range(50):
            worksheet.set_row(i, 20)
    
    def _create_validation_sheet(self, workbook):
        """إنشاء ورقة قواعد التحقق"""
        worksheet = workbook.add_worksheet('قواعد التحقق')
        
        # تنسيقات
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#70AD47',
            'font_color': 'white',
            'align': 'center',
            'valign': 'vcenter',
            'border': 1
        })
        
        data_format = workbook.add_format({
            'align': 'center',
            'valign': 'vcenter',
            'border': 1
        })
        
        # العناوين
        headers = ['الحقل', 'نوع البيانات', 'مطلوب', 'القيم المسموحة', 'مثال']
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)
        
        # قواعد التحقق
        validation_rules = [
            ['كود المورد', 'نص', 'نعم', 'يجب أن يكون موجوداً في النظام', 'SUP001'],
            ['اسم المورد', 'نص', 'لا', 'أي نص', 'شركة الفوجي'],
            ['العملة', 'نص', 'نعم', 'SAR, USD, EUR, GBP, AED', 'SAR'],
            ['مبلغ الرصيد', 'رقم', 'نعم', 'أي رقم (موجب أو سالب)', '50000.00'],
            ['سعر الصرف', 'رقم', 'لا', 'رقم موجب', '1.0000'],
            ['المستند المرجعي', 'نص', 'لا', 'أي نص', 'DOC-2024-001'],
            ['ملاحظات', 'نص', 'لا', 'أي نص', 'رصيد افتتاحي']
        ]
        
        for row, rule in enumerate(validation_rules, 1):
            for col, value in enumerate(rule):
                worksheet.write(row, col, value, data_format)
        
        # تعيين عرض الأعمدة
        worksheet.set_column('A:A', 20)
        worksheet.set_column('B:B', 15)
        worksheet.set_column('C:C', 10)
        worksheet.set_column('D:D', 30)
        worksheet.set_column('E:E', 20)
    
    def parse_excel_file(self, file_content):
        """تحليل ملف Excel المرفوع"""
        try:
            # قراءة الملف
            df = pd.read_excel(file_content, sheet_name='الأرصدة الافتتاحية')
            
            # تنظيف البيانات
            df = df.dropna(how='all')  # حذف الصفوف الفارغة تماماً
            
            # تحويل أسماء الأعمدة للإنجليزية
            column_mapping = dict(zip(self.template_columns, self.english_columns))
            df = df.rename(columns=column_mapping)
            
            # التحقق من وجود الأعمدة المطلوبة
            required_columns = ['supplier_code', 'currency_code', 'opening_balance_amount']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                raise ValueError(f"الأعمدة المطلوبة مفقودة: {missing_columns}")
            
            # تنظيف البيانات
            df['supplier_code'] = df['supplier_code'].astype(str).str.strip()
            df['currency_code'] = df['currency_code'].astype(str).str.strip().str.upper()
            df['opening_balance_amount'] = pd.to_numeric(df['opening_balance_amount'], errors='coerce')
            df['exchange_rate'] = pd.to_numeric(df['exchange_rate'], errors='coerce').fillna(1.0)
            
            # حذف الصفوف التي تحتوي على بيانات غير صحيحة
            df = df.dropna(subset=['supplier_code', 'currency_code', 'opening_balance_amount'])
            
            # تحويل إلى قائمة من القواميس
            records = df.to_dict('records')
            
            return {
                'success': True,
                'records': records,
                'total_count': len(records),
                'message': f'تم تحليل {len(records)} سجل بنجاح'
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل ملف Excel: {e}")
            return {
                'success': False,
                'records': [],
                'total_count': 0,
                'message': f'خطأ في تحليل الملف: {str(e)}'
            }
    
    def validate_records(self, records, suppliers_dict):
        """التحقق من صحة السجلات"""
        valid_records = []
        invalid_records = []
        
        valid_currencies = ['SAR', 'USD', 'EUR', 'GBP', 'AED']
        
        for i, record in enumerate(records):
            errors = []
            
            # التحقق من كود المورد
            supplier_code = record.get('supplier_code', '').strip()
            if not supplier_code:
                errors.append('كود المورد مطلوب')
            elif supplier_code not in suppliers_dict:
                errors.append(f'كود المورد غير موجود: {supplier_code}')
            
            # التحقق من العملة
            currency = record.get('currency_code', '').strip().upper()
            if not currency:
                errors.append('رمز العملة مطلوب')
            elif currency not in valid_currencies:
                errors.append(f'رمز العملة غير مدعوم: {currency}')
            
            # التحقق من المبلغ
            amount = record.get('opening_balance_amount')
            if amount is None or pd.isna(amount):
                errors.append('مبلغ الرصيد مطلوب')
            elif not isinstance(amount, (int, float)):
                errors.append('مبلغ الرصيد يجب أن يكون رقماً')
            
            # التحقق من سعر الصرف
            exchange_rate = record.get('exchange_rate', 1.0)
            if exchange_rate is None or pd.isna(exchange_rate) or exchange_rate <= 0:
                record['exchange_rate'] = 1.0
            
            if errors:
                invalid_records.append({
                    'row': i + 2,  # +2 لأن الصف الأول هو العناوين والفهرسة تبدأ من 0
                    'record': record,
                    'errors': errors
                })
            else:
                # إضافة معرف المورد
                record['supplier_id'] = suppliers_dict[supplier_code]['id']
                valid_records.append(record)
        
        return valid_records, invalid_records
    
    def create_validation_report(self, valid_count, invalid_records):
        """إنشاء تقرير التحقق"""
        try:
            output = BytesIO()
            workbook = xlsxwriter.Workbook(output, {'in_memory': True})
            
            # ورقة الملخص
            summary_sheet = workbook.add_worksheet('ملخص التحقق')
            
            # تنسيقات
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#4472C4',
                'font_color': 'white',
                'align': 'center',
                'font_size': 12
            })
            
            success_format = workbook.add_format({
                'bg_color': '#C6EFCE',
                'align': 'center',
                'font_size': 11
            })
            
            error_format = workbook.add_format({
                'bg_color': '#FFC7CE',
                'align': 'center',
                'font_size': 11
            })
            
            # كتابة الملخص
            summary_sheet.write('A1', 'نتائج التحقق من البيانات', header_format)
            summary_sheet.write('A3', 'السجلات الصحيحة:', header_format)
            summary_sheet.write('B3', valid_count, success_format)
            summary_sheet.write('A4', 'السجلات الخاطئة:', header_format)
            summary_sheet.write('B4', len(invalid_records), error_format)
            
            # ورقة الأخطاء
            if invalid_records:
                errors_sheet = workbook.add_worksheet('الأخطاء')
                
                headers = ['رقم الصف', 'كود المورد', 'العملة', 'المبلغ', 'الأخطاء']
                for col, header in enumerate(headers):
                    errors_sheet.write(0, col, header, header_format)
                
                for row, invalid in enumerate(invalid_records, 1):
                    record = invalid['record']
                    errors_sheet.write(row, 0, invalid['row'])
                    errors_sheet.write(row, 1, record.get('supplier_code', ''))
                    errors_sheet.write(row, 2, record.get('currency_code', ''))
                    errors_sheet.write(row, 3, record.get('opening_balance_amount', ''))
                    errors_sheet.write(row, 4, '; '.join(invalid['errors']))
            
            workbook.close()
            output.seek(0)
            
            return output
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير التحقق: {e}")
            raise
