{% extends "base.html" %}

{% block title %}مثال: قاعدة إشعار تلقائي{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-robot me-2"></i>
                        مثال: إنشاء قاعدة إشعار تلقائي عند تغيير حالة الشحنة
                    </h6>
                </div>
                <div class="card-body">
                    <form id="automationRuleForm">
                        <!-- معلومات القاعدة الأساسية -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label">اسم القاعدة</label>
                                <input type="text" class="form-control" id="ruleName" 
                                       value="إشعار تغيير حالة الشحنة" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الحدث المحفز</label>
                                <select class="form-select" id="triggerEvent" required>
                                    <option value="">اختر الحدث</option>
                                    <option value="shipment_status_changed" selected>تغيير حالة الشحنة</option>
                                    <option value="shipment_delivered">تسليم الشحنة</option>
                                    <option value="shipment_delayed">تأخير الشحنة</option>
                                    <option value="payment_received">استلام الدفعة</option>
                                </select>
                            </div>
                        </div>

                        <!-- شروط التفعيل -->
                        <div class="mb-4">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-filter me-2"></i>
                                شروط التفعيل
                            </h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label">الحالة الجديدة</label>
                                    <select class="form-select" id="newStatus">
                                        <option value="">جميع الحالات</option>
                                        <option value="delivered" selected>تم التسليم</option>
                                        <option value="in_transit">في الطريق</option>
                                        <option value="delayed">متأخر</option>
                                        <option value="cancelled">ملغي</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">نوع الشحنة</label>
                                    <select class="form-select" id="shipmentType">
                                        <option value="">جميع الأنواع</option>
                                        <option value="express">سريع</option>
                                        <option value="standard">عادي</option>
                                        <option value="vip">VIP</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">قيمة الشحنة (أكبر من)</label>
                                    <input type="number" class="form-control" id="minValue" 
                                           placeholder="مثال: 1000">
                                </div>
                            </div>
                        </div>

                        <!-- اختيار المستلمين -->
                        <div class="mb-4">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-users me-2"></i>
                                اختيار المستلمين
                            </h6>
                            
                            <!-- مجموعات جهات الاتصال -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">مجموعات جهات الاتصال</label>
                                    <div class="contact-groups-container border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">جهات اتصال مخصصة</label>
                                    <div class="d-flex align-items-center mb-2">
                                        <button type="button" class="btn btn-outline-primary select-custom-contacts-btn">
                                            <i class="fas fa-address-book me-2"></i>
                                            اختيار جهات اتصال
                                        </button>
                                        <span class="ms-2 text-muted" id="customContactsCount">0 محدد</span>
                                    </div>
                                    <div id="selectedContactsDisplay" class="border rounded p-3" style="min-height: 100px; max-height: 200px; overflow-y: auto;">
                                        <p class="text-muted">لم يتم اختيار أي جهة اتصال</p>
                                    </div>
                                </div>
                            </div>

                            <!-- المجموعات المختارة -->
                            <div class="mb-3">
                                <label class="form-label">المجموعات المختارة</label>
                                <div id="selectedGroupsDisplay" class="border rounded p-2" style="min-height: 50px;">
                                    <p class="text-muted">لم يتم اختيار أي مجموعة</p>
                                </div>
                            </div>
                        </div>

                        <!-- قنوات الإشعار -->
                        <div class="mb-4">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-paper-plane me-2"></i>
                                قنوات الإشعار
                            </h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input notification-channel-checkbox" 
                                               type="checkbox" value="SMS" id="channel_SMS" checked>
                                        <label class="form-check-label" for="channel_SMS">
                                            <i class="fas fa-sms text-success me-2"></i>
                                            رسائل SMS
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input notification-channel-checkbox" 
                                               type="checkbox" value="EMAIL" id="channel_EMAIL" checked>
                                        <label class="form-check-label" for="channel_EMAIL">
                                            <i class="fas fa-envelope text-primary me-2"></i>
                                            البريد الإلكتروني
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input notification-channel-checkbox" 
                                               type="checkbox" value="WHATSAPP" id="channel_WHATSAPP">
                                        <label class="form-check-label" for="channel_WHATSAPP">
                                            <i class="fab fa-whatsapp text-success me-2"></i>
                                            واتساب
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- قالب الرسالة -->
                        <div class="mb-4">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-comment-alt me-2"></i>
                                قالب الرسالة
                            </h6>
                            <div class="row">
                                <div class="col-md-8">
                                    <label class="form-label">نص الرسالة</label>
                                    <textarea class="form-control" id="messageTemplate" rows="4" 
                                              placeholder="أدخل نص الرسالة...">عزيزي العميل، تم تغيير حالة شحنتكم رقم {shipment_id} إلى "{new_status}". 
للاستفسار اتصل بنا على: {company_phone}

شركة الفجيحي للشحن</textarea>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">المتغيرات المتاحة</label>
                                    <div class="border rounded p-3" style="max-height: 150px; overflow-y: auto;">
                                        <small class="text-muted">
                                            <div><code>{shipment_id}</code> - رقم الشحنة</div>
                                            <div><code>{new_status}</code> - الحالة الجديدة</div>
                                            <div><code>{old_status}</code> - الحالة السابقة</div>
                                            <div><code>{customer_name}</code> - اسم العميل</div>
                                            <div><code>{company_phone}</code> - هاتف الشركة</div>
                                            <div><code>{tracking_url}</code> - رابط التتبع</div>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات إضافية -->
                        <div class="mb-4">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-cog me-2"></i>
                                إعدادات إضافية
                            </h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label">أولوية الإشعار</label>
                                    <select class="form-select" id="notificationPriority">
                                        <option value="1">منخفضة</option>
                                        <option value="5" selected>متوسطة</option>
                                        <option value="8">عالية</option>
                                        <option value="10">عاجلة</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">تأخير الإرسال (بالدقائق)</label>
                                    <input type="number" class="form-control" id="sendDelay" 
                                           value="0" min="0" max="1440">
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="isActive" checked>
                                        <label class="form-check-label" for="isActive">
                                            تفعيل القاعدة
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="button" class="btn btn-outline-primary" onclick="testRule()">
                                    <i class="fas fa-play me-2"></i>
                                    اختبار القاعدة
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="previewMessage()">
                                    <i class="fas fa-eye me-2"></i>
                                    معاينة الرسالة
                                </button>
                            </div>
                            <div>
                                <button type="button" class="btn btn-secondary" onclick="saveAsDraft()">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ كمسودة
                                </button>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-check me-2"></i>
                                    إنشاء القاعدة
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تضمين واجهة اختيار جهات الاتصال -->
{% include 'notifications/contacts/automation_selector.html' %}

<!-- Modal معاينة الرسالة -->
<div class="modal fade" id="messagePreviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة الرسالة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">الرسالة كما ستظهر للمستلم:</label>
                    <div class="border rounded p-3 bg-light" id="messagePreviewContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <strong>المستلمين المتوقعين:</strong>
                        <div id="expectedRecipients" class="mt-2">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                    <div class="col-md-6">
                        <strong>القنوات المختارة:</strong>
                        <div id="selectedChannels" class="mt-2">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/automation-contacts.js') }}"></script>
<script>
// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحديث عداد جهات الاتصال المخصصة
    const originalDisplayMethod = automationContacts.displaySelectedContacts;
    automationContacts.displaySelectedContacts = function() {
        originalDisplayMethod.call(this);
        document.getElementById('customContactsCount').textContent = 
            `${this.selectedContacts.length} محدد`;
    };
});

// اختبار القاعدة
function testRule() {
    const testData = {
        shipment_id: 'SH-2025-001',
        new_status: 'تم التسليم',
        old_status: 'في الطريق',
        customer_name: 'أحمد محمد',
        company_phone: '+967-1-234567',
        tracking_url: 'https://track.example.com/SH-2025-001'
    };

    const notificationData = automationContacts.createNotificationData('test_rule', testData);
    
    console.log('بيانات الاختبار:', notificationData);
    
    // محاكاة إرسال الإشعار
    automationContacts.sendNotification(notificationData)
        .then(result => {
            if (result.success) {
                alert(`تم إرسال ${result.sent_count} إشعار بنجاح!`);
            } else {
                alert(`فشل في الإرسال: ${result.error}`);
            }
        })
        .catch(error => {
            alert(`خطأ في الاختبار: ${error.message}`);
        });
}

// معاينة الرسالة
function previewMessage() {
    const template = document.getElementById('messageTemplate').value;
    const sampleData = {
        shipment_id: 'SH-2025-001',
        new_status: 'تم التسليم',
        old_status: 'في الطريق',
        customer_name: 'أحمد محمد',
        company_phone: '+967-1-234567',
        tracking_url: 'https://track.example.com/SH-2025-001'
    };

    const processedMessage = automationContacts.processMessageTemplate(template, sampleData);
    document.getElementById('messagePreviewContent').textContent = processedMessage;

    // عرض المستلمين المتوقعين
    const selectedGroups = Array.from(document.querySelectorAll('.contact-group-checkbox:checked'))
        .map(cb => cb.nextElementSibling.textContent.trim());
    
    document.getElementById('expectedRecipients').innerHTML = 
        selectedGroups.map(group => `<span class="badge bg-primary me-1">${group}</span>`).join('') +
        (automationContacts.selectedContacts.length > 0 ? 
            `<br><small class="text-muted">+ ${automationContacts.selectedContacts.length} جهة اتصال مخصصة</small>` : '');

    // عرض القنوات المختارة
    const selectedChannels = Array.from(document.querySelectorAll('.notification-channel-checkbox:checked'))
        .map(cb => cb.nextElementSibling.textContent.trim());
    
    document.getElementById('selectedChannels').innerHTML = 
        selectedChannels.map(channel => `<span class="badge bg-success me-1">${channel}</span>`).join('');

    // فتح Modal
    const modal = new bootstrap.Modal(document.getElementById('messagePreviewModal'));
    modal.show();
}

// حفظ كمسودة
function saveAsDraft() {
    const settings = automationContacts.exportSettings();
    const ruleData = {
        name: document.getElementById('ruleName').value,
        trigger_event: document.getElementById('triggerEvent').value,
        conditions: {
            new_status: document.getElementById('newStatus').value,
            shipment_type: document.getElementById('shipmentType').value,
            min_value: document.getElementById('minValue').value
        },
        notification_settings: settings,
        is_active: false,
        is_draft: true
    };

    console.log('حفظ المسودة:', ruleData);
    alert('تم حفظ القاعدة كمسودة!');
}

// إرسال النموذج
document.getElementById('automationRuleForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const settings = automationContacts.exportSettings();
    const ruleData = {
        name: document.getElementById('ruleName').value,
        trigger_event: document.getElementById('triggerEvent').value,
        conditions: {
            new_status: document.getElementById('newStatus').value,
            shipment_type: document.getElementById('shipmentType').value,
            min_value: document.getElementById('minValue').value
        },
        notification_settings: settings,
        priority: document.getElementById('notificationPriority').value,
        send_delay: document.getElementById('sendDelay').value,
        is_active: document.getElementById('isActive').checked,
        is_draft: false
    };

    console.log('إنشاء القاعدة:', ruleData);
    
    // هنا يمكن إرسال البيانات للخادم
    alert('تم إنشاء قاعدة الأتمتة بنجاح!');
});
</script>
{% endblock %}
