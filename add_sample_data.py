#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية لنظام ERP
"""

from database_manager import DatabaseManager
from datetime import datetime, timedelta
import random

def add_sample_data():
    """إضافة بيانات تجريبية للنظام"""
    db_manager = DatabaseManager()
    
    try:
        print("🚀 بدء إضافة البيانات التجريبية...")
        
        # 1. إضافة موردين تجريبيين
        suppliers_data = [
            ("شركة التقنية المتطورة", "تقنية", "الرياض", "0112345678", "<EMAIL>", "معتمد"),
            ("مؤسسة الأجهزة الذكية", "أجهزة", "جدة", "0123456789", "<EMAIL>", "معتمد"),
            ("شركة المكاتب العصرية", "مكتبية", "الدمام", "0134567890", "<EMAIL>", "قيد المراجعة"),
            ("مجموعة الحلول التقنية", "تقنية", "الخبر", "0145678901", "<EMAIL>", "معتمد"),
            ("شركة الأثاث المكتبي", "أثاث", "الطائف", "0156789012", "<EMAIL>", "معتمد")
        ]
        
        for supplier in suppliers_data:
            try:
                db_manager.execute_query("""
                    INSERT INTO suppliers (supplier_name, supplier_type, city, phone, email, status, is_active, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, 1, ?)
                """, supplier + (datetime.now(),))
                print(f"✅ تم إضافة المورد: {supplier[0]}")
            except Exception as e:
                print(f"❌ خطأ في إضافة المورد {supplier[0]}: {e}")
        
        # 2. إضافة أصناف تجريبية
        items_data = [
            ("لابتوب Dell Latitude", "أجهزة كمبيوتر", "جهاز", 2500.00, 10, 2),
            ("طابعة HP LaserJet", "طابعات", "جهاز", 800.00, 5, 1),
            ("شاشة Samsung 24 بوصة", "شاشات", "جهاز", 600.00, 8, 2),
            ("كيبورد لوجيتك", "ملحقات", "قطعة", 150.00, 20, 5),
            ("ماوس لاسلكي", "ملحقات", "قطعة", 80.00, 15, 3),
            ("كرسي مكتبي", "أثاث", "قطعة", 1200.00, 6, 1),
            ("مكتب خشبي", "أثاث", "قطعة", 2000.00, 3, 1),
            ("ورق A4", "مكتبية", "علبة", 25.00, 100, 10)
        ]
        
        for item in items_data:
            try:
                db_manager.execute_query("""
                    INSERT INTO items (item_name, category, unit, unit_price, quantity, min_quantity, is_active, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, 1, ?)
                """, item + (datetime.now(),))
                print(f"✅ تم إضافة الصنف: {item[0]}")
            except Exception as e:
                print(f"❌ خطأ في إضافة الصنف {item[0]}: {e}")
        
        # 3. إضافة طلبات شراء تجريبية
        request_types = ["أجهزة كمبيوتر", "أثاث مكتبي", "مستلزمات مكتبية", "أجهزة طباعة"]
        statuses = ["مرسل", "معتمد", "مرفوض", "قيد المراجعة"]
        requesters = ["أحمد محمد", "فاطمة علي", "محمد سالم", "نورا أحمد", "خالد عبدالله"]
        
        for i in range(15):
            req_type = random.choice(request_types)
            status = random.choice(statuses)
            requester = random.choice(requesters)
            amount = round(random.uniform(500, 10000), 2)
            created_date = datetime.now() - timedelta(days=random.randint(1, 30))
            
            try:
                db_manager.execute_query("""
                    INSERT INTO purchase_requests (requester_name, req_type, req_status, total_amount, created_at, description)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (requester, req_type, status, amount, created_date, f"طلب شراء {req_type} للقسم"))
                print(f"✅ تم إضافة طلب شراء #{i+1}")
            except Exception as e:
                print(f"❌ خطأ في إضافة طلب الشراء #{i+1}: {e}")
        
        print("\n🎉 تم إضافة جميع البيانات التجريبية بنجاح!")
        print("📊 يمكنك الآن رؤية البيانات الحقيقية في لوحة المعلومات")
        
    except Exception as e:
        print(f"❌ خطأ عام في إضافة البيانات: {e}")
    
    finally:
        db_manager.close()

if __name__ == "__main__":
    add_sample_data()
