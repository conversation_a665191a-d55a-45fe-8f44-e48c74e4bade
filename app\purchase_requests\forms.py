#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نماذج طلبات الشراء المحسنة - Enhanced Purchase Request Forms
"""

from flask_wtf import FlaskForm
from wtforms import (
    StringField, TextAreaField, SelectField, DateField, 
    DecimalField, IntegerField, FieldList, FormField, 
    HiddenField, SubmitField
)
from wtforms.validators import DataRequired, Length, NumberRange, Optional
from datetime import datetime, date

class PurchaseRequestItemForm(FlaskForm):
    """نموذج عنصر طلب الشراء - Purchase Request Item Form"""
    
    # الحقول الـ 7 حسب المواصفات
    line_number = IntegerField('رقم السطر', validators=[DataRequired()], default=1)
    item_code = StringField('كود الصنف', validators=[DataRequired(), Length(max=50)])
    item_name = StringField('اسم الصنف', validators=[DataRequired(), Length(max=200)])
    item_description = TextAreaField('الوصف', validators=[Optional(), Length(max=500)])
    quantity = DecimalField('الكمية', validators=[DataRequired(), NumberRange(min=0.001)], places=3)
    unit_name = StringField('الوحدة', validators=[DataRequired(), Length(max=50)], default='قطعة')
    unit_price = DecimalField('السعر', validators=[DataRequired(), NumberRange(min=0)], places=2)
    total_price = DecimalField('الإجمالي', validators=[Optional()], places=2, render_kw={'readonly': True})
    
    # معلومات إضافية
    specifications = TextAreaField('المواصفات', validators=[Optional(), Length(max=1000)])
    notes = TextAreaField('الملاحظات', validators=[Optional(), Length(max=500)])
    
    # حقول مخفية
    id = HiddenField()
    item_id = HiddenField()

class PurchaseRequestSearchForm(FlaskForm):
    """نموذج البحث المتقدم - Advanced Search Form (15 معيار)"""
    
    # القسم الأساسي (5 معايير)
    req_no = StringField('رقم الطلب', validators=[Optional(), Length(max=50)])
    req_serial = IntegerField('الرقم التسلسلي', validators=[Optional()])
    requester_name = StringField('اسم الطالب', validators=[Optional(), Length(max=100)])
    department_name = StringField('اسم القسم', validators=[Optional(), Length(max=100)])
    req_type = SelectField('نوع الطلب', choices=[
        ('', 'الكل'),
        ('عادي', 'عادي'),
        ('عاجل', 'عاجل'),
        ('طارئ', 'طارئ')
    ], validators=[Optional()])
    
    # القسم المالي (5 معايير)
    currency = SelectField('العملة', choices=[
        ('', 'الكل'),
        ('ريال', 'ريال سعودي'),
        ('دولار', 'دولار أمريكي'),
        ('يورو', 'يورو')
    ], validators=[Optional()])
    amount_from = DecimalField('المبلغ من', validators=[Optional(), NumberRange(min=0)], places=2)
    amount_to = DecimalField('المبلغ إلى', validators=[Optional(), NumberRange(min=0)], places=2)
    approval_status = SelectField('حالة الموافقة', choices=[
        ('', 'الكل'),
        ('في انتظار', 'في انتظار'),
        ('معتمد', 'معتمد'),
        ('مرفوض', 'مرفوض')
    ], validators=[Optional()])
    contract_no = StringField('رقم العقد', validators=[Optional(), Length(max=50)])
    
    # القسم الزمني (5 معايير)
    req_date_from = DateField('تاريخ الطلب من', validators=[Optional()])
    req_date_to = DateField('تاريخ الطلب إلى', validators=[Optional()])
    needed_date_from = DateField('تاريخ الحاجة من', validators=[Optional()])
    needed_date_to = DateField('تاريخ الحاجة إلى', validators=[Optional()])
    req_status = SelectField('حالة الطلب', choices=[
        ('', 'الكل'),
        ('مسودة', 'مسودة'),
        ('مرسل', 'مرسل'),
        ('معتمد', 'معتمد'),
        ('مرفوض', 'مرفوض')
    ], validators=[Optional()])
    
    # أزرار العمل
    search = SubmitField('بحث')
    clear = SubmitField('مسح')
    export = SubmitField('تصدير')

class ContractSelectionForm(FlaskForm):
    """نموذج اختيار العقد - Contract Selection Form"""
    
    # معايير البحث (8 معايير)
    contract_no = StringField('رقم العقد', validators=[Optional(), Length(max=50)])
    contract_title = StringField('عنوان العقد', validators=[Optional(), Length(max=200)])
    supplier_name = StringField('اسم المورد', validators=[Optional(), Length(max=100)])
    contract_status = SelectField('حالة العقد', choices=[
        ('', 'الكل'),
        ('نشط', 'نشط'),
        ('منتهي', 'منتهي'),
        ('معلق', 'معلق')
    ], validators=[Optional()])
    contract_type = SelectField('نوع العقد', choices=[
        ('', 'الكل'),
        ('توريد', 'توريد'),
        ('خدمات', 'خدمات'),
        ('صيانة', 'صيانة'),
        ('استشارات', 'استشارات')
    ], validators=[Optional()])
    amount_min = DecimalField('المبلغ الأدنى', validators=[Optional(), NumberRange(min=0)], places=2)
    amount_max = DecimalField('المبلغ الأعلى', validators=[Optional(), NumberRange(min=0)], places=2)
    currency = SelectField('العملة', choices=[
        ('', 'الكل'),
        ('ريال', 'ريال سعودي'),
        ('دولار', 'دولار أمريكي'),
        ('يورو', 'يورو')
    ], validators=[Optional()])
    
    # العقد المختار
    selected_contract_id = HiddenField()
    
    # أزرار العمل
    search = SubmitField('بحث')
    select = SubmitField('اختيار')
    cancel = SubmitField('إلغاء')

class PurchaseRequestForm(FlaskForm):
    """نموذج طلب الشراء الرئيسي - Main Purchase Request Form"""
    
    # تبويب المعلومات الأساسية (3 أعمدة × 6 صفوف)
    req_no = StringField('رقم الطلب', validators=[DataRequired(), Length(max=50)])
    req_serial = IntegerField('الرقم التسلسلي', validators=[DataRequired()])
    requester_name = StringField('اسم الطالب', validators=[DataRequired(), Length(max=100)])
    department_name = StringField('اسم القسم', validators=[DataRequired(), Length(max=100)])
    req_type = SelectField('نوع الطلب', choices=[
        ('عادي', 'عادي'),
        ('عاجل', 'عاجل'),
        ('طارئ', 'طارئ')
    ], validators=[DataRequired()], default='عادي')
    req_priority = SelectField('أولوية الطلب', choices=[
        ('عادي', 'عادي'),
        ('عاجل', 'عاجل'),
        ('طارئ', 'طارئ')
    ], validators=[DataRequired()], default='عادي')
    req_date = DateField('تاريخ الطلب', validators=[DataRequired()], default=date.today)
    needed_date = DateField('تاريخ الحاجة', validators=[DataRequired()])
    req_status = SelectField('حالة الطلب', choices=[
        ('مسودة', 'مسودة'),
        ('مرسل', 'مرسل'),
        ('معتمد', 'معتمد')
    ], validators=[DataRequired()], default='مسودة')
    
    # تبويب المعلومات المالية (3 أعمدة × 4 صفوف)
    currency = SelectField('العملة', choices=[
        ('ريال', 'ريال سعودي'),
        ('دولار', 'دولار أمريكي'),
        ('يورو', 'يورو')
    ], validators=[DataRequired()], default='ريال')
    total_amount = DecimalField('الإجمالي', validators=[Optional()], places=2, render_kw={'readonly': True})
    approval_status = SelectField('حالة الموافقة', choices=[
        ('في انتظار', 'في انتظار'),
        ('معتمد', 'معتمد'),
        ('مرفوض', 'مرفوض')
    ], validators=[DataRequired()], default='في انتظار')
    contract_no = StringField('رقم العقد', validators=[Optional(), Length(max=50)])
    contract_serial = IntegerField('رقم تسلسلي العقد', validators=[Optional()])
    contract_amount = DecimalField('مبلغ العقد', validators=[Optional()], places=2)
    
    # تبويب معلومات إضافية
    description = TextAreaField('الوصف', validators=[Optional(), Length(max=2000)], render_kw={'rows': 4})
    notes = TextAreaField('الملاحظات', validators=[Optional(), Length(max=1000)], render_kw={'rows': 3})
    attachments = StringField('المرفقات', validators=[Optional(), Length(max=500)])
    
    # قائمة العناصر
    items = FieldList(FormField(PurchaseRequestItemForm), min_entries=1)
    
    # حقول مخفية
    id = HiddenField()
    created_by = HiddenField()
    
    # أزرار العمل
    save = SubmitField('حفظ')
    save_and_send = SubmitField('حفظ وإرسال')
    cancel = SubmitField('إلغاء')
    
    def validate(self, extra_validators=None):
        """تحقق مخصص من صحة البيانات"""
        if not super().validate(extra_validators):
            return False
        
        # التحقق من تاريخ الحاجة
        if self.needed_date.data and self.req_date.data:
            if self.needed_date.data < self.req_date.data:
                self.needed_date.errors.append('تاريخ الحاجة يجب أن يكون بعد تاريخ الطلب')
                return False
        
        # التحقق من وجود عناصر
        if not any(item.item_name.data for item in self.items):
            self.items.errors.append('يجب إضافة عنصر واحد على الأقل')
            return False
        
        return True

class QuickNavigationForm(FlaskForm):
    """نموذج الانتقال السريع - Quick Navigation Form"""

    target_record = IntegerField('رقم السجل', validators=[DataRequired(), NumberRange(min=1)])
    go = SubmitField('انتقال')

class ExportForm(FlaskForm):
    """نموذج التصدير - Export Form"""

    export_format = SelectField('تنسيق التصدير', choices=[
        ('excel', 'Excel'),
        ('pdf', 'PDF'),
        ('csv', 'CSV')
    ], validators=[DataRequired()], default='excel')
    include_details = SelectField('تضمين التفاصيل', choices=[
        ('yes', 'نعم'),
        ('no', 'لا')
    ], validators=[DataRequired()], default='yes')
    export = SubmitField('تصدير')
