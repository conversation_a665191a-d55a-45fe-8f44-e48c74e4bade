-- إن<PERSON><PERSON><PERSON> جدول مستندات العقود
-- Create Contract Documents Table

-- حذ<PERSON> الجدول إذا كان موجوداً
DROP TABLE CONTRACT_DOCUMENTS CASCADE CONSTRAINTS;

-- <PERSON><PERSON><PERSON><PERSON><PERSON> الجدول
CREATE TABLE CONTRACT_DOCUMENTS (
    ID NUMBER PRIMARY KEY,
    CONTRACT_ID NUMBER NOT NULL,
    TITLE NVARCHAR2(200) NOT NULL,
    DESCRIPTION NVARCHAR2(1000),
    FILE_PATH NVARCHAR2(500),
    FILE_NAME NVARCHAR2(200),
    FILE_SIZE NUMBER,
    FILE_TYPE NVARCHAR2(10),
    URL NVARCHAR2(1000),
    DOCUMENT_TYPE NVARCHAR2(20) NOT NULL CHECK (DOCUMENT_TYPE IN ('attachment', 'link')),
    CREATED_BY NVARCHAR2(50) NOT NULL,
    CREATED_AT DATE DEFAULT SYSDATE,
    UPDATED_BY NVARCHAR2(50),
    UPDATED_AT DATE
);

-- إ<PERSON><PERSON><PERSON>ء sequence للمعرف الفريد
DROP SEQUENCE CONTRACT_DOCUMENTS_SEQ;
CREATE SEQUENCE CONTRACT_DOCUMENTS_SEQ
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- إنشاء trigger للمعرف التلقائي
CREATE OR REPLACE TRIGGER CONTRACT_DOCUMENTS_TRG
    BEFORE INSERT ON CONTRACT_DOCUMENTS
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := CONTRACT_DOCUMENTS_SEQ.NEXTVAL;
    END IF;
END;
/

-- إنشاء فهارس للأداء
CREATE INDEX IDX_CONTRACT_DOCS_CONTRACT_ID ON CONTRACT_DOCUMENTS(CONTRACT_ID);
CREATE INDEX IDX_CONTRACT_DOCS_TYPE ON CONTRACT_DOCUMENTS(DOCUMENT_TYPE);
CREATE INDEX IDX_CONTRACT_DOCS_CREATED ON CONTRACT_DOCUMENTS(CREATED_AT);

-- إضافة تعليقات على الجدول والأعمدة
COMMENT ON TABLE CONTRACT_DOCUMENTS IS 'جدول مستندات العقود - يحتوي على المرفقات والروابط المرتبطة بالعقود';

COMMENT ON COLUMN CONTRACT_DOCUMENTS.ID IS 'المعرف الفريد للمستند';
COMMENT ON COLUMN CONTRACT_DOCUMENTS.CONTRACT_ID IS 'معرف العقد المرتبط';
COMMENT ON COLUMN CONTRACT_DOCUMENTS.TITLE IS 'عنوان المستند';
COMMENT ON COLUMN CONTRACT_DOCUMENTS.DESCRIPTION IS 'وصف المستند';
COMMENT ON COLUMN CONTRACT_DOCUMENTS.FILE_PATH IS 'مسار الملف على الخادم (للمرفقات)';
COMMENT ON COLUMN CONTRACT_DOCUMENTS.FILE_NAME IS 'اسم الملف الأصلي (للمرفقات)';
COMMENT ON COLUMN CONTRACT_DOCUMENTS.FILE_SIZE IS 'حجم الملف بالبايت (للمرفقات)';
COMMENT ON COLUMN CONTRACT_DOCUMENTS.FILE_TYPE IS 'نوع الملف (للمرفقات)';
COMMENT ON COLUMN CONTRACT_DOCUMENTS.URL IS 'الرابط (للروابط)';
COMMENT ON COLUMN CONTRACT_DOCUMENTS.DOCUMENT_TYPE IS 'نوع المستند: attachment أو link';
COMMENT ON COLUMN CONTRACT_DOCUMENTS.CREATED_BY IS 'المستخدم الذي أنشأ المستند';
COMMENT ON COLUMN CONTRACT_DOCUMENTS.CREATED_AT IS 'تاريخ إنشاء المستند';
COMMENT ON COLUMN CONTRACT_DOCUMENTS.UPDATED_BY IS 'المستخدم الذي عدل المستند';
COMMENT ON COLUMN CONTRACT_DOCUMENTS.UPDATED_AT IS 'تاريخ آخر تعديل';

-- إدراج بيانات تجريبية (اختيارية)
INSERT INTO CONTRACT_DOCUMENTS (CONTRACT_ID, TITLE, DESCRIPTION, DOCUMENT_TYPE, URL, CREATED_BY) 
VALUES (1, 'رابط تجريبي', 'رابط لموقع الشركة', 'link', 'https://www.alfogehi.net', 'admin');

INSERT INTO CONTRACT_DOCUMENTS (CONTRACT_ID, TITLE, DESCRIPTION, DOCUMENT_TYPE, FILE_NAME, FILE_TYPE, CREATED_BY) 
VALUES (1, 'مرفق تجريبي', 'ملف PDF تجريبي', 'attachment', 'sample.pdf', 'pdf', 'admin');

-- تأكيد التغييرات
COMMIT;

-- عرض بنية الجدول
DESCRIBE CONTRACT_DOCUMENTS;

-- عرض البيانات التجريبية
SELECT * FROM CONTRACT_DOCUMENTS;
