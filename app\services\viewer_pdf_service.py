# -*- coding: utf-8 -*-
"""
خدمة PDF تستخدم نفس آلية صفحة المعاينة
PDF Service using the same mechanism as the viewer page
"""

import os
import sys
import requests
import time
import subprocess
from datetime import datetime

class ViewerPDFService:
    """خدمة PDF تستخدم نفس آلية صفحة المعاينة"""
    
    def __init__(self):
        """تهيئة الخدمة"""
        self.output_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'pdf')
        os.makedirs(self.output_dir, exist_ok=True)
        self.base_url = 'http://127.0.0.1:5000'
    
    def generate_pdf_like_viewer(self, delivery_order_id):
        """إنشاء PDF بنفس طريقة صفحة المعاينة"""
        try:
            print(f"🎯 محاولة إنشاء PDF لأمر التسليم {delivery_order_id}...")
            
            # إنشاء اسم الملف
            filename = f"delivery_order_like_viewer_{delivery_order_id}.pdf"
            filepath = os.path.join(self.output_dir, filename)
            
            # URL صفحة المعاينة
            viewer_url = f"{self.base_url}/shipments/delivery-order-viewer/{delivery_order_id}"
            
            print(f"🌐 صفحة المعاينة: {viewer_url}")
            
            # الطريقة 1: استخدام Chrome مع JavaScript
            if self._try_chrome_with_js(viewer_url, filepath):
                return filepath, "تم إنشاء PDF بنفس طريقة صفحة المعاينة"
            
            # الطريقة 2: استخدام Puppeteer (إذا كان متاح)
            if self._try_puppeteer(viewer_url, filepath):
                return filepath, "تم إنشاء PDF باستخدام Puppeteer"
            
            # الطريقة 3: Chrome headless عادي
            if self._try_chrome_simple(viewer_url, filepath):
                return filepath, "تم إنشاء PDF باستخدام Chrome"
            
            # الطريقة 4: إنشاء رابط للمعاينة
            return self._create_viewer_link(delivery_order_id, filepath)
            
        except Exception as e:
            return None, f"خطأ في إنشاء PDF: {str(e)}"
    
    def _try_chrome_with_js(self, viewer_url, output_path):
        """محاولة استخدام Chrome مع تنفيذ JavaScript"""
        try:
            # البحث عن Chrome
            chrome_paths = [
                r'C:\Program Files\Google\Chrome\Application\chrome.exe',
                r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe'
            ]
            
            chrome_path = None
            for path in chrome_paths:
                if os.path.exists(path):
                    chrome_path = path
                    break
            
            if not chrome_path:
                print("❌ Chrome غير متاح")
                return False
            
            print("🖨️ استخدام Chrome مع JavaScript...")
            
            # إنشاء ملف JavaScript مؤقت لتنفيذ downloadPDF
            js_script = f"""
            // انتظار تحميل الصفحة والمكتبات
            setTimeout(() => {{
                try {{
                    // التأكد من وجود المكتبات
                    if (typeof html2canvas === 'undefined' || typeof window.jspdf === 'undefined') {{
                        console.error('المكتبات غير محملة');
                        return;
                    }}
                    
                    // تنفيذ نفس دالة downloadPDF الموجودة في الصفحة
                    const element = document.getElementById('delivery-order-content');
                    if (!element) {{
                        console.error('عنصر المحتوى غير موجود');
                        return;
                    }}
                    
                    // إخفاء أزرار التحكم
                    const controls = document.querySelector('.controls');
                    if (controls) controls.style.display = 'none';
                    
                    html2canvas(element, {{
                        scale: 1.5,
                        useCORS: true,
                        allowTaint: true,
                        backgroundColor: '#ffffff'
                    }}).then(function(canvas) {{
                        const {{ jsPDF }} = window.jspdf;
                        const pdf = new jsPDF('p', 'mm', 'a4');
                        
                        const imgData = canvas.toDataURL('image/png');
                        const imgWidth = 210;
                        const imgHeight = (canvas.height * imgWidth) / canvas.width;
                        
                        pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
                        
                        // حفظ PDF
                        pdf.save('delivery_order_{delivery_order_id}.pdf');
                        
                        console.log('تم إنشاء PDF بنجاح');
                        
                        // إظهار أزرار التحكم
                        if (controls) controls.style.display = 'block';
                    }}).catch(function(error) {{
                        console.error('خطأ في إنشاء PDF:', error);
                    }});
                }} catch (error) {{
                    console.error('خطأ عام:', error);
                }}
            }}, 5000); // انتظار 5 ثوان
            """
            
            # كتابة الملف المؤقت
            temp_js = os.path.join(self.output_dir, 'temp_pdf_script.js')
            with open(temp_js, 'w', encoding='utf-8') as f:
                f.write(js_script)
            
            # تشغيل Chrome مع تنفيذ JavaScript
            cmd = [
                chrome_path,
                '--headless',
                '--disable-gpu',
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--virtual-time-budget=15000',
                '--run-all-compositor-stages-before-draw',
                f'--user-data-dir={self.output_dir}/chrome_temp',
                viewer_url
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            # تنظيف الملف المؤقت
            if os.path.exists(temp_js):
                os.remove(temp_js)
            
            # البحث عن الملف المُنشأ
            pdf_files = [f for f in os.listdir(self.output_dir) if f.endswith('.pdf') and 'delivery_order_' in f]
            
            if pdf_files:
                # نقل الملف إلى المسار المطلوب
                source_file = os.path.join(self.output_dir, pdf_files[0])
                if os.path.exists(source_file):
                    os.rename(source_file, output_path)
                    print("✅ تم إنشاء PDF بنجاح باستخدام Chrome + JavaScript")
                    return True
            
            print("❌ لم يتم إنشاء PDF باستخدام JavaScript")
            return False
            
        except Exception as e:
            print(f"❌ خطأ في Chrome + JavaScript: {e}")
            return False
    
    def _try_puppeteer(self, viewer_url, output_path):
        """محاولة استخدام Puppeteer (إذا كان متاح)"""
        try:
            # فحص إذا كان Node.js و Puppeteer متاحين
            node_result = subprocess.run(['node', '--version'], capture_output=True, timeout=5)
            if node_result.returncode != 0:
                print("❌ Node.js غير متاح")
                return False
            
            # إنشاء ملف Puppeteer مؤقت
            puppeteer_script = f"""
const puppeteer = require('puppeteer');

(async () => {{
    try {{
        const browser = await puppeteer.launch({{
            headless: true,
            args: ['--no-sandbox', '--disable-dev-shm-usage']
        }});
        
        const page = await browser.newPage();
        await page.goto('{viewer_url}', {{ waitUntil: 'networkidle2' }});
        
        // انتظار تحميل المكتبات
        await page.waitForFunction(() => {{
            return typeof html2canvas !== 'undefined' && typeof window.jspdf !== 'undefined';
        }}, {{ timeout: 10000 }});
        
        // تنفيذ دالة downloadPDF
        await page.evaluate(() => {{
            return new Promise((resolve) => {{
                const element = document.getElementById('delivery-order-content');
                const controls = document.querySelector('.controls');
                if (controls) controls.style.display = 'none';
                
                html2canvas(element, {{
                    scale: 1.5,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff'
                }}).then(function(canvas) {{
                    const {{ jsPDF }} = window.jspdf;
                    const pdf = new jsPDF('p', 'mm', 'a4');
                    
                    const imgData = canvas.toDataURL('image/png');
                    const imgWidth = 210;
                    const imgHeight = (canvas.height * imgWidth) / canvas.width;
                    
                    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
                    
                    // إرجاع PDF كـ base64
                    const pdfData = pdf.output('datauristring');
                    window.pdfData = pdfData;
                    
                    if (controls) controls.style.display = 'block';
                    resolve();
                }});
            }});
        }});
        
        // الحصول على بيانات PDF
        const pdfData = await page.evaluate(() => window.pdfData);
        
        await browser.close();
        
        // حفظ PDF
        const fs = require('fs');
        const base64Data = pdfData.replace(/^data:application\/pdf;filename=generated.pdf;base64,/, '');
        fs.writeFileSync('{output_path}', base64Data, 'base64');
        
        console.log('تم إنشاء PDF بنجاح باستخدام Puppeteer');
    }} catch (error) {{
        console.error('خطأ في Puppeteer:', error);
        process.exit(1);
    }}
}})();
            """
            
            # كتابة وتنفيذ ملف Puppeteer
            temp_js = os.path.join(self.output_dir, 'puppeteer_pdf.js')
            with open(temp_js, 'w', encoding='utf-8') as f:
                f.write(puppeteer_script)
            
            result = subprocess.run(['node', temp_js], capture_output=True, text=True, timeout=60)
            
            # تنظيف الملف المؤقت
            if os.path.exists(temp_js):
                os.remove(temp_js)
            
            if result.returncode == 0 and os.path.exists(output_path):
                print("✅ تم إنشاء PDF بنجاح باستخدام Puppeteer")
                return True
            else:
                print(f"❌ فشل Puppeteer: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Puppeteer غير متاح: {e}")
            return False
    
    def _try_chrome_simple(self, viewer_url, output_path):
        """محاولة استخدام Chrome العادي"""
        try:
            chrome_paths = [
                r'C:\Program Files\Google\Chrome\Application\chrome.exe',
                r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe'
            ]
            
            chrome_path = None
            for path in chrome_paths:
                if os.path.exists(path):
                    chrome_path = path
                    break
            
            if not chrome_path:
                return False
            
            cmd = [
                chrome_path,
                '--headless',
                '--disable-gpu',
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--virtual-time-budget=10000',
                '--print-to-pdf=' + output_path,
                '--print-to-pdf-no-header',
                viewer_url
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:
                print("✅ تم إنشاء PDF باستخدام Chrome العادي")
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ خطأ في Chrome العادي: {e}")
            return False
    
    def _create_viewer_link(self, delivery_order_id, output_path):
        """إنشاء رابط للمعاينة مع تعليمات"""
        try:
            viewer_url = f"{self.base_url}/shipments/delivery-order-viewer/{delivery_order_id}"
            
            # إنشاء ملف HTML يحتوي على رابط المعاينة
            html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>رابط أمر التسليم</title>
    <style>
        body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; }}
        .link {{ font-size: 18px; color: #007bff; text-decoration: none; }}
        .instructions {{ margin: 20px; padding: 20px; background: #f8f9fa; border-radius: 5px; }}
    </style>
</head>
<body>
    <h1>أمر التسليم رقم {delivery_order_id}</h1>
    
    <div class="instructions">
        <h2>📋 تعليمات تحميل PDF:</h2>
        <p>1. اضغط على الرابط أدناه لفتح صفحة المعاينة</p>
        <p>2. في صفحة المعاينة، اضغط على زر "📄 تحميل PDF"</p>
        <p>3. سيتم تحميل الملف تلقائياً بالتصميم الجميل</p>
    </div>
    
    <a href="{viewer_url}" target="_blank" class="link">
        🔗 فتح صفحة المعاينة وتحميل PDF
    </a>
    
    <p style="margin-top: 30px; color: #6c757d;">
        تم إنشاء هذا الرابط في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    </p>
</body>
</html>
            """
            
            # حفظ الملف
            html_path = output_path.replace('.pdf', '.html')
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"✅ تم إنشاء رابط المعاينة: {html_path}")
            return html_path, "تم إنشاء رابط لصفحة المعاينة - استخدم زر تحميل PDF في الصفحة"
            
        except Exception as e:
            return None, f"خطأ في إنشاء رابط المعاينة: {str(e)}"


# إنشاء instance عام للخدمة
viewer_pdf_service = ViewerPDFService()


def generate_pdf_like_viewer(delivery_order_id):
    """دالة مساعدة لإنشاء PDF بنفس طريقة صفحة المعاينة"""
    return viewer_pdf_service.generate_pdf_like_viewer(delivery_order_id)
