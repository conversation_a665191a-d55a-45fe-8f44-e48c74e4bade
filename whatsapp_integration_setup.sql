-- إعد<PERSON> قاعدة البيانات لدعم إرسال الواتساب

-- 1. جدول إعدادات الواتساب
CREATE TABLE whatsapp_config (
    id NUMBER PRIMARY KEY,
    config_name VARCHAR2(100) NOT NULL,
    api_endpoint VARCHAR2(500),
    access_token VARCHAR2(1000),
    phone_number_id VARCHAR2(50),
    business_account_id VARCHAR2(50),
    webhook_verify_token VARCHAR2(200),
    is_active NUMBER(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. جدول قوالب الرسائل
CREATE TABLE whatsapp_templates (
    id NUMBER PRIMARY KEY,
    template_name VARCHAR2(100) NOT NULL,
    template_type VARCHAR2(50), -- delivery_order, notification, reminder
    language_code VARCHAR2(10) DEFAULT 'ar',
    header_text CLOB,
    body_text CLOB NOT NULL,
    footer_text CLOB,
    button_text VARCHAR2(200),
    button_url VARCHAR2(500),
    is_active NUMBER(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. جدول سجل إرسال الواتساب
CREATE TABLE whatsapp_messages (
    id NUMBER PRIMARY KEY,
    delivery_order_id NUMBER,
    recipient_phone VARCHAR2(20) NOT NULL,
    recipient_name VARCHAR2(200),
    template_id NUMBER,
    message_content CLOB,
    message_status VARCHAR2(50), -- pending, sent, delivered, read, failed
    whatsapp_message_id VARCHAR2(100),
    sent_at TIMESTAMP,
    delivered_at TIMESTAMP,
    read_at TIMESTAMP,
    error_message CLOB,
    retry_count NUMBER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_whatsapp_delivery_order 
        FOREIGN KEY (delivery_order_id) REFERENCES delivery_orders(id),
    CONSTRAINT fk_whatsapp_template 
        FOREIGN KEY (template_id) REFERENCES whatsapp_templates(id)
);

-- 4. جدول أرقام الهواتف للعملاء/المخلصين
CREATE TABLE contact_phones (
    id NUMBER PRIMARY KEY,
    contact_type VARCHAR2(50), -- customer, agent, manager
    contact_id NUMBER NOT NULL, -- customer_id, agent_id, etc.
    phone_number VARCHAR2(20) NOT NULL,
    country_code VARCHAR2(5) DEFAULT '+967',
    is_whatsapp_enabled NUMBER(1) DEFAULT 1,
    is_primary NUMBER(1) DEFAULT 0,
    is_active NUMBER(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. تحديث جدول قواعد الأتمتة لدعم الواتساب
ALTER TABLE automation_rules ADD whatsapp_template_id NUMBER;
ALTER TABLE automation_rules ADD CONSTRAINT fk_automation_whatsapp_template 
    FOREIGN KEY (whatsapp_template_id) REFERENCES whatsapp_templates(id);

-- إنشاء sequences
CREATE SEQUENCE whatsapp_config_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE whatsapp_templates_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE whatsapp_messages_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE contact_phones_seq START WITH 1 INCREMENT BY 1;

-- إدراج بيانات تجريبية
INSERT INTO whatsapp_config (
    id, config_name, api_endpoint, access_token, phone_number_id, is_active
) VALUES (
    whatsapp_config_seq.NEXTVAL,
    'Meta WhatsApp Cloud API',
    'https://graph.facebook.com/v18.0',
    'YOUR_ACCESS_TOKEN_HERE',
    'YOUR_PHONE_NUMBER_ID_HERE',
    1
);

-- قالب رسالة أمر التسليم
INSERT INTO whatsapp_templates (
    id, template_name, template_type, body_text, is_active
) VALUES (
    whatsapp_templates_seq.NEXTVAL,
    'delivery_order_notification',
    'delivery_order',
    'مرحباً {customer_name}،

تم إنشاء أمر تسليم جديد:
📋 رقم الأمر: {order_number}
📦 الشحنة: {shipment_id}
👨‍💼 المخلص: {agent_name}
🏢 الفرع: {branch_name}
📅 التاريخ: {created_date}

سيتم التواصل معك قريباً لتنسيق عملية التسليم.

شكراً لثقتكم بنا 🙏',
    1
);

-- إضافة أرقام هواتف تجريبية للمخلصين
INSERT INTO contact_phones (
    id, contact_type, contact_id, phone_number, is_whatsapp_enabled, is_primary
) VALUES (
    contact_phones_seq.NEXTVAL,
    'agent',
    4, -- المخلص عمران مهيوب الخليدي
    '+967771234567',
    1,
    1
);

COMMIT;
