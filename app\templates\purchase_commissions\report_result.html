{% extends "base.html" %}

{% block title %}نتائج التقرير{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-chart-line text-primary me-2"></i>
                        نتائج التقرير
                    </h2>
                    <p class="text-muted mb-0">
                        {% if report_type == 'summary' %}تقرير ملخص العمولات
                        {% elif report_type == 'detailed' %}تقرير تفصيلي للعمولات
                        {% elif report_type == 'performance' %}تقرير أداء المندوبين
                        {% elif report_type == 'comparison' %}تقرير مقارنة الفترات
                        {% endif %}
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('purchase_commissions.reports') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للتقارير
                    </a>
                    <button onclick="window.print()" class="btn btn-primary ms-2">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                    <div class="btn-group ms-2" role="group">
                        <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-2"></i>
                            تصدير
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('purchase_commissions.export_excel', report_type=report_type) }}{{ build_export_params() }}">
                                    <i class="fas fa-file-excel me-2"></i>
                                    تصدير Excel
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('purchase_commissions.export_pdf', report_type=report_type) }}{{ build_export_params() }}">
                                    <i class="fas fa-file-pdf me-2"></i>
                                    تصدير PDF
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Applied -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-filter me-2"></i>
                        الفلاتر المطبقة
                    </h6>
                    <div class="row">
                        {% if filters.date_from %}
                        <div class="col-md-3">
                            <strong>من تاريخ:</strong> {{ filters.date_from }}
                        </div>
                        {% endif %}
                        {% if filters.date_to %}
                        <div class="col-md-3">
                            <strong>إلى تاريخ:</strong> {{ filters.date_to }}
                        </div>
                        {% endif %}
                        {% if filters.rep_id %}
                        <div class="col-md-3">
                            <strong>المندوب:</strong> محدد
                        </div>
                        {% endif %}
                        {% if filters.status %}
                        <div class="col-md-3">
                            <strong>الحالة:</strong> {{ filters.status }}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Report -->
    {% if report_type == 'summary' and report_data.data %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        ملخص العمولات حسب المندوب
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Totals -->
                    {% if report_data.totals %}
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary">{{ "{:,}".format(report_data.totals.total_calculations) }}</h4>
                                <small class="text-muted">إجمالي الحسابات</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success">{{ "{:,.2f}".format(report_data.totals.total_commission) }}</h4>
                                <small class="text-muted">إجمالي العمولات (ريال)</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info">{{ "{:,.2f}".format(report_data.totals.total_paid) }}</h4>
                                <small class="text-muted">المدفوع (ريال)</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning">{{ "{:,.2f}".format(report_data.totals.total_pending + report_data.totals.total_approved) }}</h4>
                                <small class="text-muted">في الانتظار (ريال)</small>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Data Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>المندوب</th>
                                    <th>عدد الحسابات</th>
                                    <th>إجمالي العمولة</th>
                                    <th>في الانتظار</th>
                                    <th>معتمدة</th>
                                    <th>مدفوعة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in report_data.data %}
                                <tr>
                                    <td>
                                        <strong>{{ item.rep_name }}</strong><br>
                                        <small class="text-muted">{{ item.rep_code }}</small>
                                    </td>
                                    <td>{{ item.calculations_count }}</td>
                                    <td>{{ "{:,.2f}".format(item.total_commission) }} ريال</td>
                                    <td>{{ "{:,.2f}".format(item.pending_amount) }} ريال</td>
                                    <td>{{ "{:,.2f}".format(item.approved_amount) }} ريال</td>
                                    <td>{{ "{:,.2f}".format(item.paid_amount) }} ريال</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Detailed Report -->
    {% if report_type == 'detailed' and report_data.data %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        التقرير التفصيلي للعمولات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المندوب</th>
                                    <th>قاعدة العمولة</th>
                                    <th>نوع العمولة</th>
                                    <th>قيمة الطلب</th>
                                    <th>معدل العمولة</th>
                                    <th>مبلغ العمولة</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in report_data.data %}
                                <tr>
                                    <td>{{ item.calculation_date.strftime('%Y-%m-%d') if item.calculation_date else '-' }}</td>
                                    <td>
                                        <strong>{{ item.rep_name }}</strong><br>
                                        <small class="text-muted">{{ item.rep_code }}</small>
                                    </td>
                                    <td>{{ item.rule_name }}</td>
                                    <td>{{ item.type_name }}</td>
                                    <td>{{ "{:,.2f}".format(item.order_value) }} ريال</td>
                                    <td>{{ "{:,.2f}".format(item.commission_rate) }}%</td>
                                    <td>{{ "{:,.2f}".format(item.commission_amount) }} ريال</td>
                                    <td>
                                        {% if item.status == 'calculated' %}
                                        <span class="badge bg-warning">محسوبة</span>
                                        {% elif item.status == 'approved' %}
                                        <span class="badge bg-success">معتمدة</span>
                                        {% elif item.status == 'paid' %}
                                        <span class="badge bg-primary">مدفوعة</span>
                                        {% elif item.status == 'rejected' %}
                                        <span class="badge bg-danger">مرفوضة</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Performance Report -->
    {% if report_type == 'performance' and report_data.data %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-trophy me-2"></i>
                        تقرير أداء المندوبين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>المندوب</th>
                                    <th>التخصص</th>
                                    <th>عدد الحسابات</th>
                                    <th>إجمالي العمولة</th>
                                    <th>متوسط العمولة</th>
                                    <th>إجمالي قيمة الطلبات</th>
                                    <th>معدل النجاح</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in report_data.data %}
                                <tr>
                                    <td>
                                        <strong>{{ item.rep_name }}</strong><br>
                                        <small class="text-muted">{{ item.rep_code }}</small>
                                    </td>
                                    <td>{{ item.specialization or '-' }}</td>
                                    <td>{{ item.total_calculations }}</td>
                                    <td>{{ "{:,.2f}".format(item.total_commission) }} ريال</td>
                                    <td>{{ "{:,.2f}".format(item.avg_commission) }} ريال</td>
                                    <td>{{ "{:,.2f}".format(item.total_orders_value) }} ريال</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: {{ item.success_rate }}%"
                                                 aria-valuenow="{{ item.success_rate }}" 
                                                 aria-valuemin="0" aria-valuemax="100">
                                                {{ "{:.1f}".format(item.success_rate) }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Comparison Report -->
    {% if report_type == 'comparison' and report_data.data %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        تقرير مقارنة الفترات (شهرياً)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الشهر</th>
                                    <th>عدد الحسابات</th>
                                    <th>إجمالي العمولة</th>
                                    <th>متوسط العمولة</th>
                                    <th>المبلغ المدفوع</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in report_data.data %}
                                <tr>
                                    <td>{{ item.month_year }}</td>
                                    <td>{{ item.calculations_count }}</td>
                                    <td>{{ "{:,.2f}".format(item.total_commission) }} ريال</td>
                                    <td>{{ "{:,.2f}".format(item.avg_commission) }} ريال</td>
                                    <td>{{ "{:,.2f}".format(item.paid_amount) }} ريال</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- No Data -->
    {% if not report_data.data %}
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد بيانات للعرض</h5>
                <p class="text-muted">لا توجد بيانات تطابق المعايير المحددة</p>
                <a href="{{ url_for('purchase_commissions.reports') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للتقارير
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
@media print {
    .btn, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>

<script>
// Build export parameters from filters
function buildExportParams() {
    const params = new URLSearchParams();

    {% if filters.date_from %}
    params.append('date_from', '{{ filters.date_from }}');
    {% endif %}

    {% if filters.date_to %}
    params.append('date_to', '{{ filters.date_to }}');
    {% endif %}

    {% if filters.rep_id %}
    params.append('rep_id', '{{ filters.rep_id }}');
    {% endif %}

    {% if filters.commission_type_id %}
    params.append('commission_type_id', '{{ filters.commission_type_id }}');
    {% endif %}

    {% if filters.status %}
    params.append('status', '{{ filters.status }}');
    {% endif %}

    return params.toString() ? '?' + params.toString() : '';
}

// Update export links with parameters
document.addEventListener('DOMContentLoaded', function() {
    const exportParams = buildExportParams();

    // Update Excel export link
    const excelLink = document.querySelector('a[href*="export_excel"]');
    if (excelLink && exportParams) {
        excelLink.href += exportParams;
    }

    // Update PDF export link
    const pdfLink = document.querySelector('a[href*="export_pdf"]');
    if (pdfLink && exportParams) {
        pdfLink.href += exportParams;
    }
});
</script>
{% endblock %}
