-- =====================================================
-- مشغلات وإجراءات الأرصدة الافتتاحية للموردين
-- Supplier Opening Balances Triggers & Procedures
-- =====================================================

-- 1. مشغل التحديث التلقائي للجدول الرئيسي
CREATE OR REPLACE TRIGGER supplier_opening_balances_trigger
    BEFORE INSERT OR UPDATE ON SUPPLIER_OPENING_BALANCES
    FOR EACH ROW
DECLARE
    v_old_values CLOB;
    v_new_values CLOB;
    v_changed_fields VARCHAR2(1000) := '';
BEGIN
    -- تعيين ID تلقائياً عند الإدراج
    IF INSERTING AND :NEW.id IS NULL THEN
        :NEW.id := SUPPLIER_OPENING_BALANCES_SEQ.NEXTVAL;
    END IF;
    
    -- تحديث تاريخ التعديل
    :NEW.updated_date := CURRENT_TIMESTAMP;
    
    -- حساب المبلغ بالعملة الأساسية (يتم تلقائياً بواسطة Generated Column)
    -- :NEW.base_currency_amount := :NEW.opening_balance_amount * NVL(:NEW.exchange_rate, 1);
    
    -- تحديد نوع الرصيد بناءً على المبلغ
    IF :NEW.opening_balance_amount > 0 THEN
        :NEW.balance_type := 'DEBIT';
    ELSIF :NEW.opening_balance_amount < 0 THEN
        :NEW.balance_type := 'CREDIT';
        :NEW.opening_balance_amount := ABS(:NEW.opening_balance_amount);
    END IF;
    
    -- تحديد السنة المالية
    -- :NEW.fiscal_year := EXTRACT(YEAR FROM :NEW.fiscal_period_start_date);
    
    -- تسجيل التغييرات للتدقيق (فقط عند التحديث)
    IF UPDATING THEN
        -- بناء JSON للقيم القديمة والجديدة
        v_old_values := '{';
        v_new_values := '{';
        
        -- فحص التغييرات في الحقول المهمة
        IF NVL(:OLD.opening_balance_amount, 0) != NVL(:NEW.opening_balance_amount, 0) THEN
            v_old_values := v_old_values || '"opening_balance_amount":' || NVL(:OLD.opening_balance_amount, 0) || ',';
            v_new_values := v_new_values || '"opening_balance_amount":' || NVL(:NEW.opening_balance_amount, 0) || ',';
            v_changed_fields := v_changed_fields || 'opening_balance_amount,';
        END IF;
        
        IF NVL(:OLD.currency_code, 'SAR') != NVL(:NEW.currency_code, 'SAR') THEN
            v_old_values := v_old_values || '"currency_code":"' || NVL(:OLD.currency_code, 'SAR') || '",';
            v_new_values := v_new_values || '"currency_code":"' || NVL(:NEW.currency_code, 'SAR') || '",';
            v_changed_fields := v_changed_fields || 'currency_code,';
        END IF;
        
        IF NVL(:OLD.status, 'DRAFT') != NVL(:NEW.status, 'DRAFT') THEN
            v_old_values := v_old_values || '"status":"' || NVL(:OLD.status, 'DRAFT') || '",';
            v_new_values := v_new_values || '"status":"' || NVL(:NEW.status, 'DRAFT') || '",';
            v_changed_fields := v_changed_fields || 'status,';
        END IF;
        
        -- إزالة الفاصلة الأخيرة وإغلاق JSON
        v_old_values := RTRIM(v_old_values, ',') || '}';
        v_new_values := RTRIM(v_new_values, ',') || '}';
        v_changed_fields := RTRIM(v_changed_fields, ',');
        
        -- إدراج سجل التدقيق إذا كان هناك تغييرات
        IF LENGTH(v_changed_fields) > 0 THEN
            INSERT INTO SUPPLIER_OPENING_BALANCES_AUDIT (
                audit_id, opening_balance_id, operation_type,
                old_values, new_values, changed_fields,
                operation_by
            ) VALUES (
                SUPPLIER_OPENING_AUDIT_SEQ.NEXTVAL, :NEW.id, 'UPDATE',
                v_old_values, v_new_values, v_changed_fields,
                :NEW.updated_by
            );
        END IF;
    END IF;
    
    -- تحديث رقم النسخة
    IF UPDATING THEN
        :NEW.version_number := NVL(:OLD.version_number, 1) + 1;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        -- تسجيل الخطأ وإعادة إثارته
        RAISE_APPLICATION_ERROR(-20001, 'خطأ في مشغل الأرصدة الافتتاحية: ' || SQLERRM);
END;
/

-- 2. مشغل تسجيل العمليات الجديدة
CREATE OR REPLACE TRIGGER supplier_opening_insert_audit_trigger
    AFTER INSERT ON SUPPLIER_OPENING_BALANCES
    FOR EACH ROW
BEGIN
    -- تسجيل عملية الإدراج في جدول التدقيق
    INSERT INTO SUPPLIER_OPENING_BALANCES_AUDIT (
        audit_id, opening_balance_id, operation_type,
        new_values, operation_by
    ) VALUES (
        SUPPLIER_OPENING_AUDIT_SEQ.NEXTVAL, :NEW.id, 'INSERT',
        '{"opening_balance_amount":' || :NEW.opening_balance_amount || 
        ',"currency_code":"' || :NEW.currency_code || 
        '","status":"' || :NEW.status || '"}',
        :NEW.created_by
    );
    
    -- تحديث ملخص الأرصدة
    UPDATE_OPENING_BALANCES_SUMMARY(:NEW.fiscal_period_start_date, :NEW.currency_code);
    
EXCEPTION
    WHEN OTHERS THEN
        -- تسجيل الخطأ دون إيقاف العملية الأساسية
        NULL;
END;
/

-- 3. مشغل تحديث الملخص عند التعديل
CREATE OR REPLACE TRIGGER supplier_opening_update_summary_trigger
    AFTER UPDATE ON SUPPLIER_OPENING_BALANCES
    FOR EACH ROW
BEGIN
    -- تحديث ملخص الأرصدة للفترة القديمة والجديدة
    IF :OLD.fiscal_period_start_date != :NEW.fiscal_period_start_date OR 
       :OLD.currency_code != :NEW.currency_code THEN
        UPDATE_OPENING_BALANCES_SUMMARY(:OLD.fiscal_period_start_date, :OLD.currency_code);
    END IF;
    
    UPDATE_OPENING_BALANCES_SUMMARY(:NEW.fiscal_period_start_date, :NEW.currency_code);
    
EXCEPTION
    WHEN OTHERS THEN
        NULL;
END;
/

-- 4. مشغل منع التعديل للأرصدة المعتمدة
CREATE OR REPLACE TRIGGER supplier_opening_protection_trigger
    BEFORE UPDATE OR DELETE ON SUPPLIER_OPENING_BALANCES
    FOR EACH ROW
BEGIN
    -- منع التعديل أو الحذف للأرصدة المعتمدة أو المرحلة
    IF :OLD.status IN ('APPROVED', 'POSTED') THEN
        IF DELETING THEN
            RAISE_APPLICATION_ERROR(-20002, 'لا يمكن حذف رصيد معتمد أو مرحل');
        ELSIF UPDATING THEN
            -- السماح فقط بتحديث حقول معينة
            IF :NEW.status != :OLD.status AND :NEW.status NOT IN ('CANCELLED') THEN
                RAISE_APPLICATION_ERROR(-20003, 'لا يمكن تعديل حالة رصيد معتمد إلا للإلغاء');
            END IF;
            
            -- منع تعديل المبلغ أو المورد
            IF :NEW.opening_balance_amount != :OLD.opening_balance_amount OR
               :NEW.supplier_id != :OLD.supplier_id OR
               :NEW.currency_code != :OLD.currency_code THEN
                RAISE_APPLICATION_ERROR(-20004, 'لا يمكن تعديل البيانات الأساسية لرصيد معتمد');
            END IF;
        END IF;
    END IF;
END;
/

-- 5. مشغل الفترات المحاسبية
CREATE OR REPLACE TRIGGER fiscal_periods_trigger
    BEFORE INSERT OR UPDATE ON FISCAL_PERIODS
    FOR EACH ROW
BEGIN
    IF INSERTING AND :NEW.id IS NULL THEN
        :NEW.id := FISCAL_PERIODS_SEQ.NEXTVAL;
    END IF;
    
    :NEW.updated_date := CURRENT_TIMESTAMP;
    
    -- التأكد من صحة التواريخ
    IF :NEW.start_date >= :NEW.end_date THEN
        RAISE_APPLICATION_ERROR(-20005, 'تاريخ بداية الفترة يجب أن يكون قبل تاريخ النهاية');
    END IF;
    
    -- تحديد السنة المالية
    :NEW.fiscal_year := EXTRACT(YEAR FROM :NEW.start_date);
END;
/

-- 6. مشغل ملخص الأرصدة
CREATE OR REPLACE TRIGGER opening_summary_trigger
    BEFORE INSERT OR UPDATE ON OPENING_BALANCES_SUMMARY
    FOR EACH ROW
BEGIN
    IF INSERTING AND :NEW.id IS NULL THEN
        :NEW.id := OPENING_BALANCES_SUMMARY_SEQ.NEXTVAL;
    END IF;
    
    :NEW.last_calculated_date := CURRENT_TIMESTAMP;
    
    -- حساب صافي الرصيد
    :NEW.net_balance_amount := NVL(:NEW.total_debit_amount, 0) - NVL(:NEW.total_credit_amount, 0);
END;
/

-- 7. إجراء تحديث ملخص الأرصدة
CREATE OR REPLACE PROCEDURE UPDATE_OPENING_BALANCES_SUMMARY(
    p_fiscal_date IN DATE,
    p_currency_code IN VARCHAR2
) AS
    v_total_suppliers NUMBER := 0;
    v_debit_count NUMBER := 0;
    v_credit_count NUMBER := 0;
    v_total_debit NUMBER := 0;
    v_total_credit NUMBER := 0;
    v_draft_count NUMBER := 0;
    v_approved_count NUMBER := 0;
    v_posted_count NUMBER := 0;
BEGIN
    -- حساب الإحصائيات
    SELECT 
        COUNT(*),
        SUM(CASE WHEN balance_type = 'DEBIT' THEN 1 ELSE 0 END),
        SUM(CASE WHEN balance_type = 'CREDIT' THEN 1 ELSE 0 END),
        SUM(CASE WHEN balance_type = 'DEBIT' THEN opening_balance_amount ELSE 0 END),
        SUM(CASE WHEN balance_type = 'CREDIT' THEN opening_balance_amount ELSE 0 END),
        SUM(CASE WHEN status = 'DRAFT' THEN 1 ELSE 0 END),
        SUM(CASE WHEN status = 'APPROVED' THEN 1 ELSE 0 END),
        SUM(CASE WHEN status = 'POSTED' THEN 1 ELSE 0 END)
    INTO 
        v_total_suppliers, v_debit_count, v_credit_count,
        v_total_debit, v_total_credit,
        v_draft_count, v_approved_count, v_posted_count
    FROM SUPPLIER_OPENING_BALANCES
    WHERE fiscal_period_start_date = p_fiscal_date
    AND currency_code = p_currency_code
    AND is_active = 1;
    
    -- تحديث أو إدراج الملخص
    MERGE INTO OPENING_BALANCES_SUMMARY obs
    USING (SELECT p_fiscal_date as fiscal_date, p_currency_code as currency FROM DUAL) src
    ON (obs.fiscal_period_start_date = src.fiscal_date AND obs.currency_code = src.currency)
    WHEN MATCHED THEN
        UPDATE SET
            total_suppliers_count = v_total_suppliers,
            debit_balances_count = v_debit_count,
            credit_balances_count = v_credit_count,
            total_debit_amount = v_total_debit,
            total_credit_amount = v_total_credit,
            draft_count = v_draft_count,
            approved_count = v_approved_count,
            posted_count = v_posted_count,
            last_calculated_date = CURRENT_TIMESTAMP
    WHEN NOT MATCHED THEN
        INSERT (
            fiscal_period_start_date, currency_code,
            total_suppliers_count, debit_balances_count, credit_balances_count,
            total_debit_amount, total_credit_amount,
            draft_count, approved_count, posted_count
        ) VALUES (
            p_fiscal_date, p_currency_code,
            v_total_suppliers, v_debit_count, v_credit_count,
            v_total_debit, v_total_credit,
            v_draft_count, v_approved_count, v_posted_count
        );
    
    COMMIT;
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END UPDATE_OPENING_BALANCES_SUMMARY;
/

-- إظهار رسالة نجاح
SELECT 'تم إنشاء المشغلات والإجراءات بنجاح!' AS status FROM DUAL;
