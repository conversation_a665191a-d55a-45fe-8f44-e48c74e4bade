<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قوي للتصميم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* تطبيق التصميم بقوة */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
            min-height: 100vh !important;
            direction: rtl !important;
        }
        
        .test-container {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            text-align: center;
        }
        
        .test-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-left: 4px solid #667eea;
            position: relative;
            overflow: hidden;
            min-height: 120px;
            cursor: pointer;
        }
        
        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }
        
        .test-card:nth-child(1) { border-left-color: #28a745; }
        .test-card:nth-child(2) { border-left-color: #ffc107; }
        .test-card:nth-child(3) { border-left-color: #dc3545; }
        .test-card:nth-child(4) { border-left-color: #17a2b8; }
        
        .test-card::before {
            position: absolute;
            top: 1rem;
            right: 1rem;
            font-size: 2rem;
            opacity: 0.1;
            color: #2c3e50;
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }
        
        .test-card:nth-child(1)::before { content: '\f07a'; }
        .test-card:nth-child(2)::before { content: '\f044'; }
        .test-card:nth-child(3)::before { content: '\f058'; }
        .test-card:nth-child(4)::before { content: '\f0c1'; }
        
        .test-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        .test-label {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .test-panel {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        .test-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        .test-table table {
            width: 100%;
            margin-bottom: 0;
            border-collapse: separate;
            border-spacing: 0;
        }
        
        .test-table thead th {
            background: linear-gradient(135deg, #343a40 0%, #495057 100%);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.85rem;
            padding: 1rem;
            border: none;
        }
        
        .test-table tbody tr {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-bottom: 1px solid #e9ecef;
        }
        
        .test-table tbody tr:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: scale(1.01);
        }
        
        .test-table tbody td {
            padding: 1rem;
            vertical-align: middle;
            border: none;
            font-size: 0.9rem;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            border: 1px solid #c3e6cb;
            text-align: center;
            font-weight: 600;
        }
        
        .instructions {
            background: #fff3cd;
            color: #856404;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-shopping-cart me-3"></i>اختبار التصميم الحديث</h1>
            <p>إذا كنت ترى هذا التصميم بوضوح، فإن CSS يعمل بنجاح!</p>
        </div>
        
        <div class="success-message">
            <i class="fas fa-check-circle me-2"></i>
            إذا كنت ترى الخلفية المتدرجة والألوان الجميلة، فإن التصميم يعمل!
        </div>
        
        <div class="test-stats">
            <div class="test-card">
                <span class="test-number">25</span>
                <div class="test-label">إجمالي الأوامر</div>
            </div>
            <div class="test-card">
                <span class="test-number">7</span>
                <div class="test-label">المسودات</div>
            </div>
            <div class="test-card">
                <span class="test-number">18</span>
                <div class="test-label">المؤكدة</div>
            </div>
            <div class="test-card">
                <span class="test-number">12</span>
                <div class="test-label">المُستخدمة</div>
            </div>
        </div>
        
        <div class="test-panel">
            <h3><i class="fas fa-filter me-2"></i>لوحة التحكم</h3>
            <p>هذا مثال على لوحة التحكم بالتصميم الحديث</p>
        </div>
        
        <div class="test-table">
            <table>
                <thead>
                    <tr>
                        <th>رقم الأمر</th>
                        <th>المورد</th>
                        <th>القيمة</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>PO-2024-001</strong></td>
                        <td>شركة التموين</td>
                        <td><strong>125,000 USD</strong></td>
                        <td><span style="background: #28a745; color: white; padding: 0.25rem 0.5rem; border-radius: 12px; font-size: 0.75rem;">مؤكد</span></td>
                    </tr>
                    <tr>
                        <td><strong>PO-2024-002</strong></td>
                        <td>مؤسسة الخدمات</td>
                        <td><strong>45,000 SAR</strong></td>
                        <td><span style="background: #ffc107; color: #212529; padding: 0.25rem 0.5rem; border-radius: 12px; font-size: 0.75rem;">مسودة</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="instructions">
            <h4><i class="fas fa-info-circle me-2"></i>التعليمات:</h4>
            <ol>
                <li>إذا كنت ترى هذا التصميم بوضوح، فإن المتصفح يدعم CSS</li>
                <li>اذهب إلى نافذة أوامر الشراء الفعلية</li>
                <li>اضغط Ctrl+F5 لإعادة التحميل</li>
                <li>إذا لم يظهر التصميم، أرسل screenshot</li>
            </ol>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 اختبار التصميم تم تحميله بنجاح');
            
            // إضافة تفاعل للبطاقات
            const cards = document.querySelectorAll('.test-card');
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    alert('البطاقة تعمل! التصميم التفاعلي يعمل بنجاح.');
                });
            });
        });
    </script>
</body>
</html>
