-- =====================================================
-- إنشاء الجداول المفقودة للتكامل
-- Create Missing Tables for Integration
-- =====================================================

-- 1. جدول معاملات الموردين
CREATE TABLE SUPPLIER_TRANSACTIONS (
    transaction_id NUMBER PRIMARY KEY,
    supplier_id NUMBER NOT NULL,
    transaction_type VARCHAR2(30) NOT NULL, -- INVOICE, PAYMENT, CREDIT_NOTE, DEBIT_NOTE, ADJUSTMENT
    reference_type VARCHAR2(50), -- PURCHASE_ORDER, TRANSFER_REQUEST, MANUAL
    reference_id NUMBER,
    reference_number VARCHAR2(100),
    transaction_date DATE DEFAULT SYSDATE,
    due_date DATE,
    
    -- المبالغ
    currency_code VARCHAR2(3) NOT NULL,
    original_amount NUMBER(15,2) NOT NULL,
    exchange_rate NUMBER(15,6) DEFAULT 1,
    base_currency_amount NUMBER(15,2),
    debit_amount NUMBER(15,2) DEFAULT 0,
    credit_amount NUMBER(15,2) DEFAULT 0,
    
    -- الوصف والملاحظات
    description VARCHAR2(500),
    notes CLOB,
    
    -- الحالة
    status VARCHAR2(20) DEFAULT 'ACTIVE', -- ACTIVE, CANCELLED, REVERSED
    
    -- معلومات أمر الشراء (الحقول الجديدة)
    purchase_order_id NUMBER,
    purchase_order_number VARCHAR2(50),
    po_item_id NUMBER,
    delivery_date DATE,
    goods_received_date DATE,
    invoice_received_date DATE,
    payment_request_id NUMBER,
    payment_status VARCHAR2(20) DEFAULT 'PENDING',
    
    -- معلومات النظام
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_st_supplier FOREIGN KEY (supplier_id) REFERENCES SUPPLIERS(id),
    CONSTRAINT fk_st_purchase_order FOREIGN KEY (purchase_order_id) REFERENCES PURCHASE_ORDERS(id),
    CONSTRAINT fk_st_created_by FOREIGN KEY (created_by) REFERENCES USERS(id),
    CONSTRAINT fk_st_updated_by FOREIGN KEY (updated_by) REFERENCES USERS(id)
);

-- 2. جدول أرصدة الموردين
CREATE TABLE SUPPLIER_BALANCES (
    id NUMBER PRIMARY KEY,
    supplier_id NUMBER NOT NULL,
    currency_code VARCHAR2(3) NOT NULL,
    
    -- الأرصدة
    opening_balance NUMBER(15,2) DEFAULT 0,
    debit_amount NUMBER(15,2) DEFAULT 0,
    credit_amount NUMBER(15,2) DEFAULT 0,
    current_balance NUMBER(15,2) DEFAULT 0,
    
    -- إحصائيات
    total_invoices_count NUMBER DEFAULT 0,
    total_payments_count NUMBER DEFAULT 0,
    last_transaction_date DATE,
    last_payment_date DATE,
    average_payment_days NUMBER(5,2),
    
    -- تواريخ النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER,
    
    -- المفاتيح الخارجية والقيود
    CONSTRAINT fk_sb_supplier FOREIGN KEY (supplier_id) REFERENCES SUPPLIERS(id),
    CONSTRAINT fk_sb_created_by FOREIGN KEY (created_by) REFERENCES USERS(id),
    CONSTRAINT fk_sb_updated_by FOREIGN KEY (updated_by) REFERENCES USERS(id),
    CONSTRAINT uk_supplier_currency UNIQUE (supplier_id, currency_code)
);

-- 3. جدول مدفوعات الموردين المتكاملة
CREATE TABLE SUPPLIER_PAYMENT_TRANSFERS (
    id NUMBER PRIMARY KEY,
    supplier_id NUMBER NOT NULL,
    transfer_request_id NUMBER,
    transfer_id NUMBER,
    
    -- تفاصيل الدفع
    payment_amount NUMBER(15,2) NOT NULL,
    currency_code VARCHAR2(3) NOT NULL,
    exchange_rate NUMBER(15,6) DEFAULT 1,
    base_currency_amount NUMBER(15,2),
    
    -- تفاصيل إضافية
    payment_purpose VARCHAR2(500),
    payment_method VARCHAR2(30) DEFAULT 'BANK_TRANSFER',
    discount_applied NUMBER(15,2) DEFAULT 0,
    tax_withheld NUMBER(15,2) DEFAULT 0,
    commission_charged NUMBER(15,2) DEFAULT 0,
    net_amount_transferred NUMBER(15,2),
    
    -- حالة الدفع
    payment_status VARCHAR2(20) DEFAULT 'PENDING', -- PENDING, APPROVED, EXECUTED, COMPLETED, CANCELLED
    
    -- تواريخ مهمة
    requested_date DATE DEFAULT SYSDATE,
    approved_date DATE,
    executed_date DATE,
    completed_date DATE,
    
    -- ملاحظات
    notes CLOB,
    rejection_reason VARCHAR2(500),
    
    -- معلومات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_spt_supplier FOREIGN KEY (supplier_id) REFERENCES SUPPLIERS(id),
    CONSTRAINT fk_spt_transfer_request FOREIGN KEY (transfer_request_id) REFERENCES TRANSFER_REQUESTS(id),
    CONSTRAINT fk_spt_transfer FOREIGN KEY (transfer_id) REFERENCES TRANSFERS(id),
    CONSTRAINT fk_spt_created_by FOREIGN KEY (created_by) REFERENCES USERS(id),
    CONSTRAINT fk_spt_updated_by FOREIGN KEY (updated_by) REFERENCES USERS(id)
);

-- 4. جدول تخصيص مدفوعات الموردين
CREATE TABLE SUPPLIER_PAYMENT_ALLOCATIONS (
    id NUMBER PRIMARY KEY,
    supplier_payment_transfer_id NUMBER NOT NULL,
    supplier_transaction_id NUMBER NOT NULL,
    allocated_amount NUMBER(15,2) NOT NULL,
    allocation_type VARCHAR2(30) DEFAULT 'FULL', -- FULL, PARTIAL
    allocation_date DATE DEFAULT SYSDATE,
    notes VARCHAR2(500),
    
    -- معلومات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_spa_payment FOREIGN KEY (supplier_payment_transfer_id) REFERENCES SUPPLIER_PAYMENT_TRANSFERS(id),
    CONSTRAINT fk_spa_transaction FOREIGN KEY (supplier_transaction_id) REFERENCES SUPPLIER_TRANSACTIONS(transaction_id),
    CONSTRAINT fk_spa_created_by FOREIGN KEY (created_by) REFERENCES USERS(id)
);

-- 5. جدول سجل حالة مدفوعات الموردين
CREATE TABLE SUPPLIER_PAYMENT_STATUS_LOG (
    id NUMBER PRIMARY KEY,
    payment_id NUMBER NOT NULL,
    old_status VARCHAR2(20),
    new_status VARCHAR2(20) NOT NULL,
    change_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    changed_by NUMBER,
    change_reason VARCHAR2(500),
    notes CLOB,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_spsl_payment FOREIGN KEY (payment_id) REFERENCES SUPPLIER_PAYMENT_TRANSFERS(id),
    CONSTRAINT fk_spsl_changed_by FOREIGN KEY (changed_by) REFERENCES USERS(id)
);

-- إنشاء Sequences للجداول الجديدة
CREATE SEQUENCE SUPPLIER_TRANSACTIONS_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SUPPLIER_BALANCES_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SUPPLIER_PAY_TRANSFERS_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SUPPLIER_PAY_ALLOC_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SUPPLIER_PAY_STATUS_SEQ START WITH 1 INCREMENT BY 1;

-- إنشاء Triggers
CREATE OR REPLACE TRIGGER supplier_transactions_trigger
    BEFORE INSERT ON SUPPLIER_TRANSACTIONS
    FOR EACH ROW
BEGIN
    IF :NEW.transaction_id IS NULL THEN
        :NEW.transaction_id := SUPPLIER_TRANSACTIONS_SEQ.NEXTVAL;
    END IF;
    
    -- حساب المبلغ بالعملة الأساسية
    :NEW.base_currency_amount := :NEW.original_amount * NVL(:NEW.exchange_rate, 1);
    
    -- تحديد المبالغ المدينة والدائنة
    IF :NEW.transaction_type IN ('INVOICE', 'DEBIT_NOTE') THEN
        :NEW.debit_amount := :NEW.original_amount;
        :NEW.credit_amount := 0;
    ELSE
        :NEW.debit_amount := 0;
        :NEW.credit_amount := :NEW.original_amount;
    END IF;
    
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER supplier_balances_trigger
    BEFORE INSERT ON SUPPLIER_BALANCES
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := SUPPLIER_BALANCES_SEQ.NEXTVAL;
    END IF;
    
    -- حساب الرصيد الحالي
    :NEW.current_balance := :NEW.opening_balance + :NEW.debit_amount - :NEW.credit_amount;
    
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER supplier_pay_transfers_trg
    BEFORE INSERT ON SUPPLIER_PAYMENT_TRANSFERS
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := SUPPLIER_PAY_TRANSFERS_SEQ.NEXTVAL;
    END IF;

    -- حساب المبلغ بالعملة الأساسية
    :NEW.base_currency_amount := :NEW.payment_amount * NVL(:NEW.exchange_rate, 1);

    -- حساب المبلغ الصافي
    :NEW.net_amount_transferred := :NEW.payment_amount -
                                   NVL(:NEW.discount_applied, 0) -
                                   NVL(:NEW.tax_withheld, 0) -
                                   NVL(:NEW.commission_charged, 0);

    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER supplier_pay_alloc_trg
    BEFORE INSERT ON SUPPLIER_PAYMENT_ALLOCATIONS
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := SUPPLIER_PAY_ALLOC_SEQ.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER supplier_pay_status_trg
    BEFORE INSERT ON SUPPLIER_PAYMENT_STATUS_LOG
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := SUPPLIER_PAY_STATUS_SEQ.NEXTVAL;
    END IF;
END;
/

-- إنشاء فهارس للأداء
CREATE INDEX idx_st_supplier ON SUPPLIER_TRANSACTIONS(supplier_id);
CREATE INDEX idx_st_type ON SUPPLIER_TRANSACTIONS(transaction_type);
CREATE INDEX idx_st_date ON SUPPLIER_TRANSACTIONS(transaction_date);
CREATE INDEX idx_st_purchase_order ON SUPPLIER_TRANSACTIONS(purchase_order_id);
CREATE INDEX idx_st_status ON SUPPLIER_TRANSACTIONS(status);

CREATE INDEX idx_sb_supplier ON SUPPLIER_BALANCES(supplier_id);
CREATE INDEX idx_sb_currency ON SUPPLIER_BALANCES(currency_code);

CREATE INDEX idx_spt_supplier ON SUPPLIER_PAYMENT_TRANSFERS(supplier_id);
CREATE INDEX idx_spt_status ON SUPPLIER_PAYMENT_TRANSFERS(payment_status);
CREATE INDEX idx_spt_transfer_request ON SUPPLIER_PAYMENT_TRANSFERS(transfer_request_id);
CREATE INDEX idx_spt_transfer ON SUPPLIER_PAYMENT_TRANSFERS(transfer_id);

CREATE INDEX idx_spa_payment ON SUPPLIER_PAYMENT_ALLOCATIONS(supplier_payment_transfer_id);
CREATE INDEX idx_spa_transaction ON SUPPLIER_PAYMENT_ALLOCATIONS(supplier_transaction_id);

CREATE INDEX idx_spsl_payment ON SUPPLIER_PAYMENT_STATUS_LOG(payment_id);
CREATE INDEX idx_spsl_date ON SUPPLIER_PAYMENT_STATUS_LOG(change_date);

-- رسالة نجاح
SELECT 'تم إنشاء الجداول المفقودة بنجاح!' as status FROM dual;

COMMIT;
