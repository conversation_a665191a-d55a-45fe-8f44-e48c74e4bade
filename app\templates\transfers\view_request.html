{% extends "base.html" %}

{% block title %}تفاصيل طلب الحوالة - {{ transfer_transfer_request.request_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-eye text-primary me-2"></i>
                        تفاصيل طلب الحوالة
                    </h1>
                    <p class="text-muted mb-0">رقم الطلب: <strong>{{ transfer_transfer_request.request_number }}</strong></p>
                </div>
                <div>
                    <a href="{{ url_for('transfers.list_requests') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> العودة للقائمة
                    </a>
                    <a href="{{ url_for('transfers.edit_request', request_id=transfer_transfer_request.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> تعديل
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات الطلب الأساسية -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الطلب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <strong>رقم الطلب:</strong>
                            <p class="text-primary">{{ transfer_request.request_number }}</p>
                        </div>
                        <div class="col-6">
                            <strong>الحالة:</strong>
                            <p>
                                {% if transfer_request.status == 'pending' %}
                                    <span class="badge bg-warning">معلق</span>
                                {% elif transfer_request.status == 'approved' %}
                                    <span class="badge bg-success">موافق عليه</span>
                                {% elif transfer_request.status == 'rejected' %}
                                    <span class="badge bg-danger">مرفوض</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ transfer_request.status }}</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-6">
                            <strong>المبلغ:</strong>
                            <p class="text-success fw-bold">{{ "{:,.2f}".format(transfer_request.amount) }} {{ transfer_request.currency or 'TRY' }}</p>
                        </div>
                        <div class="col-6">
                            <strong>نوع التحويل:</strong>
                            <p>
                                {% if transfer_request.money_changer_bank_type == 'bank' %}
                                    <span class="badge bg-info">بنك</span>
                                {% else %}
                                    <span class="badge bg-warning">صراف</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-6">
                            <strong>تاريخ الإنشاء:</strong>
                            <p>{{ transfer_request.created_at.strftime('%Y-%m-%d %H:%M') if transfer_request.created_at else 'غير محدد' }}</p>
                        </div>
                        <div class="col-6">
                            <strong>آخر تحديث:</strong>
                            <p>{{ transfer_request.updated_at.strftime('%Y-%m-%d %H:%M') if transfer_request.updated_at else 'غير محدد' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>
                        معلومات الفرع والصراف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <strong>الفرع:</strong>
                            <p>{{ transfer_request.branch_name or 'غير محدد' }}</p>
                        </div>
                        <div class="col-12">
                            <strong>الصراف/البنك:</strong>
                            <p>{{ transfer_request.money_changer_bank_name or 'غير محدد' }}</p>
                        </div>
                        <div class="col-12">
                            <strong>منشئ الطلب:</strong>
                            <p>{{ transfer_request.created_by_name or 'غير محدد' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات المستفيد -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>
                        معلومات المستفيد
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <strong>اسم المستفيد:</strong>
                            <p class="fw-bold">{{ transfer_request.beneficiary_name or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-4">
                            <strong>نوع المستفيد:</strong>
                            <p>{{ transfer_request.beneficiary_type or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-4">
                            <strong>رقم الهوية:</strong>
                            <p>{{ transfer_request.identification_number or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6">
                            <strong>الهاتف:</strong>
                            <p>{{ transfer_request.phone or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6">
                            <strong>البريد الإلكتروني:</strong>
                            <p>{{ transfer_request.email or 'غير محدد' }}</p>
                        </div>
                        <div class="col-12">
                            <strong>العنوان:</strong>
                            <p>{{ transfer_request.beneficiary_address or 'غير محدد' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- المعلومات المصرفية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-university me-2"></i>
                        المعلومات المصرفية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <strong>اسم البنك:</strong>
                            <p>{{ transfer_request.bank_name or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-4">
                            <strong>فرع البنك:</strong>
                            <p>{{ transfer_request.bank_branch or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-4">
                            <strong>بلد البنك:</strong>
                            <p>{{ transfer_request.bank_country or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6">
                            <strong>رقم الحساب:</strong>
                            <p class="font-monospace">{{ transfer_request.bank_account or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6">
                            <strong>IBAN:</strong>
                            <p class="font-monospace">{{ transfer_request.iban or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6">
                            <strong>SWIFT Code:</strong>
                            <p class="font-monospace">{{ transfer_request.swift_code or 'غير محدد' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الغرض والملاحظات -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clipboard me-2"></i>
                        الغرض من التحويل
                    </h5>
                </div>
                <div class="card-body">
                    <p>{{ transfer_request.purpose or 'غير محدد' }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sticky-note me-2"></i>
                        الملاحظات
                    </h5>
                </div>
                <div class="card-body">
                    <p>{{ transfer_request.notes or 'لا توجد ملاحظات' }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        الإجراءات المتاحة
                    </h5>
                </div>
                <div class="card-body text-center">
                    {% if transfer_request.status == 'pending' %}
                        <button class="btn btn-success me-2" onclick="approveRequest({{ transfer_request.id }})">
                            <i class="fas fa-check"></i> موافقة
                        </button>
                        <button class="btn btn-danger me-2" onclick="rejectRequest({{ transfer_request.id }})">
                            <i class="fas fa-times"></i> رفض
                        </button>
                    {% endif %}

                    <a href="{{ url_for('transfers.edit_request', request_id=transfer_request.id) }}" class="btn btn-warning me-2">
                        <i class="fas fa-edit"></i> تعديل
                    </a>
                    
                    <button class="btn btn-info me-2" onclick="printRequest()">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    
                    <a href="{{ url_for('transfers.list_requests') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// موافقة على الطلب
function approveRequest(requestId) {
    if (confirm('هل أنت متأكد من الموافقة على هذا الطلب؟')) {
        updateRequestStatus(requestId, 'approved');
    }
}

// رفض الطلب
function rejectRequest(requestId) {
    const reason = prompt('يرجى إدخال سبب الرفض:');
    if (reason) {
        updateRequestStatus(requestId, 'rejected', reason);
    }
}

// تحديث حالة الطلب
function updateRequestStatus(requestId, status, reason = null) {
    fetch(`/transfers/api/requests/${requestId}/status`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            status: status,
            reason: reason
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`تم ${status === 'approved' ? 'الموافقة على' : 'رفض'} الطلب بنجاح`);
            location.reload(); // إعادة تحميل الصفحة
        } else {
            alert('فشل في تحديث حالة الطلب: ' + data.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('خطأ في الاتصال بالخادم');
    });
}

// طباعة الطلب
function printRequest() {
    window.print();
}
</script>

{% endblock %}
