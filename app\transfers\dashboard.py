#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
لوحة تحكم نظام الحوالات
Money Transfer System Dashboard
"""

from flask import render_template, request, jsonify
from flask_login import login_required, current_user
from . import transfers_bp
from database_manager import DatabaseManager
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@transfers_bp.route('/dashboard/widgets')
@login_required
def dashboard_widgets():
    """ويدجت لوحة التحكم"""
    try:
        db = DatabaseManager()
        
        # إحصائيات اليوم
        today_stats = get_today_statistics(db)
        
        # إحصائيات الأسبوع
        week_stats = get_week_statistics(db)
        
        # إحصائيات الشهر
        month_stats = get_month_statistics(db)
        
        # أهم الصرافين/البنوك
        top_money_changers = get_top_money_changers(db)
        
        # الحوالات المعلقة
        pending_transfers = get_pending_transfers(db)
        
        # التنبيهات
        alerts = get_system_alerts(db)
        
        return jsonify({
            'success': True,
            'data': {
                'today': today_stats,
                'week': week_stats,
                'month': month_stats,
                'top_money_changers': top_money_changers,
                'pending_transfers': pending_transfers,
                'alerts': alerts
            }
        })
        
    except Exception as e:
        logger.error(f"خطأ في ويدجت لوحة التحكم: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

def get_today_statistics(db):
    """إحصائيات اليوم"""
    try:
        stats = {}
        
        # عدد الطلبات اليوم
        result = db.execute_query("""
            SELECT COUNT(*) FROM transfer_requests
            WHERE TRUNC(created_at) = TRUNC(SYSDATE)
        """)
        stats['requests_count'] = result[0][0] if result else 0
        
        # عدد الحوالات المنفذة اليوم
        result = db.execute_query("""
            SELECT COUNT(*) FROM TRANSFER_REQUESTS
            WHERE TRUNC(created_at) = TRUNC(SYSDATE)
            AND status = 'completed'
        """)
        stats['completed_transfers'] = result[0][0] if result else 0
        
        # إجمالي المبلغ اليوم
        result = db.execute_query("""
            SELECT COALESCE(SUM(amount), 0) FROM TRANSFER_REQUESTS
            WHERE TRUNC(created_at) = TRUNC(SYSDATE)
            AND status = 'completed'
        """)
        stats['total_amount'] = float(result[0][0]) if result else 0.0
        
        # إجمالي العمولة اليوم (لا يوجد عمود commission في TRANSFER_REQUESTS)
        result = db.execute_query("""
            SELECT 0 FROM DUAL
        """)
        stats['total_commission'] = float(result[0][0]) if result else 0.0
        
        return stats
        
    except Exception as e:
        logger.error(f"خطأ في إحصائيات اليوم: {e}")
        return {}

def get_week_statistics(db):
    """إحصائيات الأسبوع"""
    try:
        stats = {}
        
        # عدد الطلبات هذا الأسبوع
        result = db.execute_query("""
            SELECT COUNT(*) FROM transfer_requests
            WHERE created_at >= SYSDATE - 7
        """)
        stats['requests_count'] = result[0][0] if result else 0
        
        # عدد الحوالات المنفذة هذا الأسبوع
        result = db.execute_query("""
            SELECT COUNT(*) FROM TRANSFER_REQUESTS
            WHERE created_at >= SYSDATE - 7
            AND status = 'completed'
        """)
        stats['completed_transfers'] = result[0][0] if result else 0
        
        # إجمالي المبلغ هذا الأسبوع
        result = db.execute_query("""
            SELECT COALESCE(SUM(amount), 0) FROM TRANSFER_REQUESTS
            WHERE created_at >= SYSDATE - 7
            AND status = 'completed'
        """)
        stats['total_amount'] = float(result[0][0]) if result else 0.0
        
        return stats
        
    except Exception as e:
        logger.error(f"خطأ في إحصائيات الأسبوع: {e}")
        return {}

def get_month_statistics(db):
    """إحصائيات الشهر"""
    try:
        stats = {}
        
        # عدد الطلبات هذا الشهر
        result = db.execute_query("""
            SELECT COUNT(*) FROM transfer_requests
            WHERE created_at >= SYSDATE - 30
        """)
        stats['requests_count'] = result[0][0] if result else 0
        
        # عدد الحوالات المنفذة هذا الشهر
        result = db.execute_query("""
            SELECT COUNT(*) FROM TRANSFER_REQUESTS
            WHERE created_at >= SYSDATE - 30
            AND status = 'completed'
        """)
        stats['completed_transfers'] = result[0][0] if result else 0
        
        # إجمالي المبلغ هذا الشهر
        result = db.execute_query("""
            SELECT COALESCE(SUM(amount), 0) FROM TRANSFER_REQUESTS
            WHERE created_at >= SYSDATE - 30
            AND status = 'completed'
        """)
        stats['total_amount'] = float(result[0][0]) if result else 0.0
        
        return stats
        
    except Exception as e:
        logger.error(f"خطأ في إحصائيات الشهر: {e}")
        return {}

def get_top_money_changers(db):
    """أهم الصرافين/البنوك"""
    try:
        result = db.execute_query("""
            SELECT
                mcb.name,
                mcb.type,
                COUNT(tr.id) as transfer_count,
                COALESCE(SUM(tr.amount), 0) as total_amount,
                0 as total_commission
            FROM money_changers_banks mcb
            LEFT JOIN TRANSFER_REQUESTS tr ON mcb.id = tr.money_changer_bank_id
                AND tr.created_at >= SYSDATE - 30
                AND tr.status = 'completed'
            WHERE mcb.is_active = 1
            GROUP BY mcb.id, mcb.name, mcb.type
            ORDER BY total_amount DESC
            FETCH FIRST 5 ROWS ONLY
        """)
        
        if result:
            return [
                {
                    'name': row[0],
                    'type': row[1],
                    'transfer_count': row[2],
                    'total_amount': float(row[3]),
                    'total_commission': float(row[4])
                }
                for row in result
            ]
        
        return []
        
    except Exception as e:
        logger.error(f"خطأ في جلب أهم الصرافين: {e}")
        return []

def get_pending_transfers(db):
    """الحوالات المعلقة"""
    try:
        result = db.execute_query("""
            SELECT 
                tr.request_number,
                b.beneficiary_name,
                tr.amount,
                tr.currency,
                tr.created_at,
                tr.status
            FROM transfer_requests tr
            JOIN beneficiaries b ON tr.beneficiary_id = b.id
            WHERE tr.status IN ('pending', 'approved')
            ORDER BY tr.created_at ASC
            FETCH FIRST 10 ROWS ONLY
        """)
        
        if result:
            return [
                {
                    'request_number': row[0],
                    'beneficiary_name': row[1],
                    'amount': float(row[2]),
                    'currency': row[3],
                    'created_at': row[4].strftime('%Y-%m-%d %H:%M') if row[4] else '',
                    'status': row[5]
                }
                for row in result
            ]
        
        return []
        
    except Exception as e:
        logger.error(f"خطأ في جلب الحوالات المعلقة: {e}")
        return []

def get_system_alerts(db):
    """تنبيهات النظام"""
    try:
        alerts = []
        
        # طلبات معلقة لأكثر من 24 ساعة
        result = db.execute_query("""
            SELECT COUNT(*) FROM transfer_requests
            WHERE status = 'pending'
            AND created_at < SYSDATE - 1
        """)
        
        if result and result[0][0] > 0:
            alerts.append({
                'type': 'warning',
                'message': f'يوجد {result[0][0]} طلب معلق لأكثر من 24 ساعة',
                'action': 'pending_requests'
            })
        
        # طلبات مرفوضة اليوم
        result = db.execute_query("""
            SELECT COUNT(*) FROM TRANSFER_REQUESTS
            WHERE status = 'rejected'
            AND TRUNC(created_at) = TRUNC(SYSDATE)
        """)
        
        if result and result[0][0] > 0:
            alerts.append({
                'type': 'danger',
                'message': f'يوجد {result[0][0]} طلب مرفوض اليوم',
                'action': 'failed_transfers'
            })
        
        # صرافين غير نشطين
        result = db.execute_query("""
            SELECT COUNT(*) FROM money_changers_banks 
            WHERE is_active = 0
        """)
        
        if result and result[0][0] > 0:
            alerts.append({
                'type': 'info',
                'message': f'يوجد {result[0][0]} صراف/بنك غير نشط',
                'action': 'money_changers'
            })
        
        return alerts
        
    except Exception as e:
        logger.error(f"خطأ في جلب التنبيهات: {e}")
        return []
