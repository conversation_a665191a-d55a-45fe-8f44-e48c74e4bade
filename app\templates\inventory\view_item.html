{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-box text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">تفاصيل الصنف: {{ item.item_code }}</p>
                </div>
                <div>
                    <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Item Details -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الصنف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label text-muted">كود الصنف</label>
                            <div class="fw-bold text-primary fs-5">{{ item.item_code }}</div>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label text-muted">الحالة</label>
                            <div>
                                {% if item.is_active %}
                                <span class="badge bg-success fs-6">نشط</span>
                                {% else %}
                                <span class="badge bg-secondary fs-6">غير نشط</span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label text-muted">الاسم بالعربية</label>
                            <div class="fw-semibold">{{ item.item_name_ar or 'غير محدد' }}</div>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label text-muted">الاسم بالإنجليزية</label>
                            <div class="fw-semibold">{{ item.item_name_en or 'غير محدد' }}</div>
                        </div>
                        
                        <div class="col-12">
                            <label class="form-label text-muted">الوصف</label>
                            <div>{{ item.description or 'لا يوجد وصف' }}</div>
                        </div>
                        
                        <div class="col-md-4">
                            <label class="form-label text-muted">وحدة القياس</label>
                            <div>
                                <span class="badge bg-light text-dark">{{ item.unit_of_measure or 'غير محدد' }}</span>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <label class="form-label text-muted">الموقع</label>
                            <div>{{ item.location or 'غير محدد' }}</div>
                        </div>
                        
                        <div class="col-md-4">
                            <label class="form-label text-muted">الباركود</label>
                            <div>{{ item.barcode or 'غير محدد' }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Stock Information -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-warehouse me-2"></i>
                        معلومات المخزون
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label text-muted">المخزون الحالي</label>
                            <div class="d-flex align-items-center">
                                {% if item.current_stock <= 0 %}
                                <span class="badge bg-danger fs-5 me-2">{{ item.current_stock }}</span>
                                <small class="text-danger">نفد المخزون</small>
                                {% elif item.current_stock <= item.min_stock_level %}
                                <span class="badge bg-warning fs-5 me-2">{{ item.current_stock }}</span>
                                <small class="text-warning">مخزون منخفض</small>
                                {% else %}
                                <span class="badge bg-success fs-5 me-2">{{ item.current_stock }}</span>
                                <small class="text-success">متوفر</small>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-6">
                            <label class="form-label text-muted">الحد الأدنى</label>
                            <div class="fw-semibold">{{ item.min_stock_level or 0 }}</div>
                        </div>
                        
                        <div class="col-6">
                            <label class="form-label text-muted">الحد الأقصى</label>
                            <div class="fw-semibold">{{ item.max_stock_level or 'غير محدد' }}</div>
                        </div>
                        
                        <div class="col-12">
                            <label class="form-label text-muted">سعر الوحدة</label>
                            <div class="fw-bold text-success fs-5">{{ "%.2f"|format(item.unit_price or 0) }} ريال</div>
                        </div>
                        
                        {% if item.current_stock > 0 and item.unit_price > 0 %}
                        <div class="col-12">
                            <label class="form-label text-muted">إجمالي قيمة المخزون</label>
                            <div class="fw-bold text-primary fs-5">
                                {{ "%.2f"|format((item.current_stock or 0) * (item.unit_price or 0)) }} ريال
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Timestamps -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        التواريخ
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label text-muted">تاريخ الإنشاء</label>
                            <div>{{ item.created_at.strftime('%Y-%m-%d %H:%M') if item.created_at else 'غير محدد' }}</div>
                        </div>
                        
                        <div class="col-12">
                            <label class="form-label text-muted">آخر تحديث</label>
                            <div>{{ item.updated_at.strftime('%Y-%m-%d %H:%M') if item.updated_at else 'غير محدد' }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
