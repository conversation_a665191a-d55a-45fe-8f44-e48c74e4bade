<!doctype html><html lang="zh-CN"><head><meta charset="UTF-8"><link rel="icon" type="image/x-icon" href="/ebusiness/static/assets/favicon.ico"/><meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=0"><meta name="keywords" content="ä¸­è¿æµ·è¿éè£ç®±è¿è¾æéå¬å¸,ä¸­è¿æµ·è¿éè¿, COSCO SHIPPING Lines,ä¸­è¿éè£ç®±è¿è¾æéå¬å¸,ä¸­è¿éè¿,COSCON"><title>COSCO SHIPPING Lines</title><!--[if lt IE 11]>
    <link rel="stylesheet" href="/ebusiness/static/styles/iview.css">
    <link rel="stylesheet" href="/ebusiness/assets/css/global.css">
    <link rel="stylesheet" href="/ebusiness/assets/css/cover-iview.css">
    <link rel="stylesheet" href="/ebusiness/assets/iconfont/iconfont.css">
    <script type="text/javascript" src="static/scripts/now.js"></script>
    <![endif]--><!--[if lt IE 9]>
    <script type="text/javascript" src="static/scripts/browsers-not-supported.js"></script>
    <![endif]--><style media="print">/* æµè§å¨æå°æ¶ï¼å¨å±é½ä¸éè¦æ¾ç¤ºçclass */
        .head,.navi_login_reg,.footer,.noPrint{ display:none!important }
        /* æµè§å¨æå°æ¶ï¼âè´§ç©è·è¸ªâä¸éè¦æ¾ç¤ºçclass */
        .cargoTrackingTabPanel .ivu-tabs-nav{ display:none!important}</style><script defer="defer" src="/ebusiness/dist/main.c94d89.js"></script><link href="/ebusiness/dist/main.38908f.css" rel="stylesheet"></head><body><script>(function () { document.cookie = "HOY_TR=UEBGSWMCTKNXPQRY,851ABC74369DEF02,vstzjbhfkygldwum; max-age=31536000; path=/";document.cookie = "HBB_HC=ead63a6248254f366eb4e613ce56f96ebe4a5e64186209654f73dd45cf585738de3e032bff5b42641d141d6558e9f02909; max-age=600; path=/"; })()</script><script src="/_ws_sbu/sbu_hc.js"></script><div id="app"></div><script>(function(){
            var dom,doc,where,iframe = document.createElement('iframe');
            iframe.src = "javascript:false";
            (iframe.frameElement || iframe).style.cssText = "position:absolute;width: 0; height: 0; border: 0;z-index:-1;overflow:hidden";
                where = document.getElementsByTagName('script')[0];
                where.parentNode.insertBefore(iframe, where);

            try {
                doc = iframe.contentWindow.document;
            } catch(e) {
                dom = document.domain;
                iframe.src="javascript:var d=document.open();d.domain='"+dom+"';void(0);";
                doc = iframe.contentWindow.document;
            }
//             doc.open()._l = function() {
//                 var js = this.createElement("script");
//                 if(dom) this.domain = dom;
//                 js.id = "boomr-if-as";
//                 js.src = '//c.go-mpulse.net/boomerang/' + 'LJPS7-BUUNW-KE4V7-T4Y4K-99999';
//                 BOOMR_lstart=new Date().getTime();
//                 this.body.appendChild(js);
//             };
            doc.write('<body>');
            doc.close();
        })();
        var _hmt = _hmt || [];
          (function() {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?3dc23c86163f0d1cd70ef10ea94e0263";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
          })();</script><script>function propChat(region, email, companyNme) {
                loadLiveChatWidget();
                var execStartChat = function () {
                    if (!!window.ScriptReady) {
                        window.CountryName = region;
                        window.EmailAddress = email;
                        window.Name = companyNme;
                        Microsoft.Omnichannel.LiveChatWidget.SDK.startChat({
                            inNewWindow: false,
                            customContext: window.ContextProvider()
                        });
                    } else {
                        setTimeout(execStartChat, 1000);
                    }
                }
                execStartChat();
            }</script><script>function lcw() {
          return  {
      "styleProps": {
        "generalStyles": {
          "width": "1160px",
          "height": "700px",
          "bottom": "calc((100vh - 700px)/2)",
          "right": "calc((100vw - 1160px)/2)",
          "borderRadius": "16px 16px 16px 16px"
        }
      },
      "chatButtonProps": {
        "controlProps": {
          "hideChatButton": true //é»è®¤ä¸æ¾ç¤ºæ¬æµ®æé® trueéè falseæ¾ç¤º
        },
      },
      "headerProps": {
          "controlProps": {
          "hideMinimizeButton": false
        },
        "styleProps": {
          "generalStyleProps": {
            "background": "#0A5997",
            "borderRadius": "16px 16px 0 0"
          },
          "titleStyleProps": {
            "fontSize": "16px",
            "fontFamily": "Figtree, Arial",
            "fontWeight": "Bold",
            "color": "#FFFFFF"
          }
        }
      },
      "footerProps": {
        "styleProps": {
          "generalStyleProps": {
            "borderRadius": "0 0 16px 16px"
          },
          "downloadTranscriptButtonStyleProps": {
            "icon": {
              "color": "#0A5997"
            }
          },
          "emailTranscriptButtonStyleProps": {
            "icon": {
              "color": "#0A5997"
            }
          },
          "audioNotificationButtonStyleProps": {
            "icon": {
              "color": "#0A5997"
            }
          }
        }
      },
      "loadingPaneProps": {
        "styleProps": {
          "generalStyleProps": {
            "background": "#0A5997"
          },
          "iconImageProps": {
            src: "https://elines.coscoshipping.com/ebusiness/dist/img/loading.6df588.gif",
          }
        }
      },
      "webChatContainerProps": {
        "containerStyles": {
          "height": "100%",
          "width": "100%",
          "overflowY": "hidden",
          "linkColor": "#3399FF"
        },
       "adaptiveCardStyles": {
        "width": "800px",
        "height": "auto",
    },
        "webChatStyles": {
          "primaryFont": "Figtree, Arial",
          "backgroundColor": "#F2F4F5",
          "botAvatarBackgroundColor": "#000000",
          "botAvatarImage": "Mascot_Icon.svg",
          "bubbleBackground": "#0A599724",
          "bubbleBorderRadius": "16px 16px 16px 0px",
          "bubbleBorderWidth": 0,
          "bubbleTextColor": "#000000",
          "bubbleFromUserBackground": "#FFFFFF",
          "bubbleFromUserMaxWidth": "300px",
          "bubbleFromUserBorderRadius": "16px 16px 0px 16px",
          "bubbleFromUserBorderWidth": 1,
          "bubbleFromUserTextColor": "#000000"
        },
        "renderingMiddlewareProps": {
           "renderingMiddleware": (renderingMiddlewareProps) => (next) => (card) => {
            if (card.contentType === "application/vnd.microsoft.card.adaptive") {
                card.content.style = {
                    ...card.content.style,
                    width: "800px",
                };
            }
            return next(card);
        },
          "userMessageStyleProps": {
            "fontSize": "14px",
            "fontFamily": "Figtree, Arial",
            "color": "#000000"
          }
        ,
          "avatarStyleProps": {
            "backgroundColor": "#0A5997"
          },
          "avatarTextStyleProps": {
            "fontSize": "14px",
            "fontFamily": "Figtree, Arial"
          },
          "receivedMessageAnchorStyles": {
            "color": "#3399FF"
          }
        }
      },
      "draggableChatWidgetProps":{
        "disabled": false
      },
      "confirmationPaneProps": {
        "componentOverrides": {
          "title": "undefined",
          "subtitle": "undefined",
          "confirmButton": "undefined",
          "cancelButton": "undefined"
        },
        "styleProps": {
          "confirmButtonStyleProps": {
            "backgroundColor": "rgba(10,89,151,1)",
            "color": "#FFFFFF",
            "fontFamily": "Segoe UI, Arial, sans-serif",
            "fontSize": "14px",
            "fontWeight": "500",
            "height": "32px",
            "width": "80px"
          },
          "confirmButtonHoveredStyleProps": {
            "backgroundColor": "rgba(10,89,151,0.8)"
          },
          "confirmButtonFocusedStyleProps": {
            "backgroundColor": "rgba(10,89,151,1)",
            "border": "2px dotted #000"
          },
          "cancelButtonStyleProps": {
            "backgroundColor": "#FFFFFF",
            "fontFamily": "Segoe UI, Arial, sans-serif",
            "fontSize": "14px",
            "fontWeight": "500",
            "height": "32px",
            "width": "80px"
          },
          "cancelButtonHoveredStyleProps": {
            "backgroundColor": "#EFEFEF"
          },
          "cancelButtonFocusedStyleProps": {
            "border": "2px dotted #000"
          }
        },
        "confirmationPaneLocalizedTexts": {
          "CLOSE_CONFIRMATION_DIALOG_TITLE_FOR_EMAIL_AND_DOWNLOAD_TRANSCRIPT_ENABLED": "End the conversation",
          "CLOSE_CONFIRMATION_DIALOG_DESCRIPTION_FOR_EMAIL_AND_DOWNLOAD_TRANSCRIPT_ENABLED": "Are you sure you want to end the conversation? If you want to email and download the transcript, select Cancel and then select the download or email icon.",
          "CLOSE_CONFIRMATION_DIALOG_TITLE_FOR_EMAIL_TRANSCRIPT_ENABLED": "End the conversation",
          "CLOSE_CONFIRMATION_DIALOG_DESCRIPTION_FOR_EMAIL_TRANSCRIPT_ENABLED": "Are you sure you want to end the conversation? If you want to email the transcript, select Cancel and then select the email icon.",
          "CLOSE_CONFIRMATION_DIALOG_TITLE_FOR_DOWNLOAD_TRANSCRIPT_ENABLED": "End the conversation",
          "CLOSE_CONFIRMATION_DIALOG_DESCRIPTION_FOR_DOWNLOAD_TRANSCRIPT_ENABLED": "Are you sure you want to end the conversation? If you want to download the transcript, select Cancel and then select the download icon.",
          "CLOSE_CONFIRMATION_DIALOG_TITLE_DEFAULT": "End the conversation",
          "CLOSE_CONFIRMATION_DIALOG_DESCRIPTION_DEFAULT": "Are you sure you want to end the conversation? "
        }
      }
    }

        }</script><script>function loadLiveChatWidget() {
                // åæ£æ¥æ¯å¦å·²ç»å è½½ï¼é¿åéå¤å è½½
                if (document.getElementById("Microsoft_Omnichannel_LCWidget")) {
                    return;
                }

                // åå»º script åç´ 
                let script = document.createElement("script");
                script.id = "Microsoft_Omnichannel_LCWidget"; // è®¾ç½® IDï¼é¿åéå¤å è½½
                script.src = "https://oc-cdn-public-apj.azureedge.net/livechatwidget/scripts/LiveChatBootstrapper.js";
                script.setAttribute("v2", ""); // v2 æ è®°
                script.setAttribute("data-app-id", "9846bc6d-300c-4c66-9849-3f65ff2a1646");
                script.setAttribute("data-lcw-version", "prod");
                script.setAttribute("data-org-id", "cd6b06d3-93d4-ef11-b8e4-000d3a819695");
                script.setAttribute("data-org-url", "https://m-cd6b06d3-93d4-ef11-b8e4-000d3a819695.as.omnichannelengagementhub.com");
                script.setAttribute("data-customization-callback", "lcw");

                // çå¬èæ¬å è½½æå
                script.onload = function () {
                    console.log("Live Chat Widget å è½½å®æï¼");
                };

                // çå¬èæ¬å è½½å¤±è´¥
                script.onerror = function () {
                    console.log("Live Chat Widget å è½½å¤±è´¥ï¼");
                };

                // å° script æå¥å° <body> æ«å°¾
                document.body.appendChild(script);
            }

            window.ScriptReady = false;
            window.addEventListener("lcw:ready", function handleLivechatReadyEvent() {
                window.CountryName = 'malaysia';
                window.EmailAddress = '<EMAIL>';
                window.Name = 'XXX SAMPLE INFOMATION LTD.';
                window.ScriptReady = true;
                window.ContextProvider = function () {
                    return {
                        'Emotion': {'value': 'Default', 'isDisplayable': true},
                        'Intent': {'value': 'Default', 'isDisplayable': true},
                        'ExportImport': {'value': 'Default', 'isDisplayable': true},
                        'IssueType': {'value': 'Default', 'isDisplayable': true},
                        'Language': {'value': 'Default', 'isDisplayable': true},
                        'Country': {'value': 'Default', 'isDisplayable': true},

                        'CountryName': {'value': window.CountryName, 'isDisplayable': true},
                        'EmailAddress': {'value': window.EmailAddress, 'isDisplayable': true},
                        'Name': {'value': window.Name, 'isDisplayable': true},
                        'Channel': {'value': 'ebusiness', 'isDisplayable': true}
                    };
                };
            });</script></body></html>