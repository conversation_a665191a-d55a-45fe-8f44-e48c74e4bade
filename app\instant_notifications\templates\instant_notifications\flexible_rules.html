{% extends "base.html" %}

{% block title %}القواعد المرنة للإشعارات{% endblock %}

{% block extra_css %}
<style>
.rule-card {
    border: 1px solid #e3e6f0;
    border-radius: 10px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.rule-card:hover {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transform: translateY(-2px);
}

.rule-card.disabled {
    opacity: 0.6;
    background-color: #f8f9fc;
}

.priority-badge {
    font-size: 0.8em;
    padding: 4px 8px;
}

.condition-display {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 5px;
    padding: 10px;
    font-family: monospace;
    font-size: 0.9em;
}

.message-preview {
    background: #e7f3ff;
    border-left: 4px solid #007bff;
    padding: 10px;
    margin: 10px 0;
    border-radius: 0 5px 5px 0;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-cogs text-primary"></i>
            القواعد المرنة للإشعارات
        </h1>
        <div>
            <a href="{{ url_for('instant_notifications.create_flexible_rule') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> إنشاء قاعدة جديدة
            </a>
            <a href="{{ url_for('instant_notifications.dashboard') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي القواعد
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ rules|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                القواعد المفعلة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ rules|selectattr("is_enabled")|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                فئات الأحداث
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ rules|map(attribute='event_category')|unique|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tags fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                أولوية عالية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ rules|selectattr("priority_level", "le", 2)|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة القواعد -->
    {% if rules %}
        <div class="row">
            {% for rule in rules %}
            <div class="col-lg-6 mb-4">
                <div class="rule-card {{ 'disabled' if not rule.is_enabled else '' }}">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="m-0 font-weight-bold text-primary">
                                {{ rule.rule_name }}
                            </h6>
                            <small class="text-muted">{{ rule.event_name_ar }}</small>
                        </div>
                        <div>
                            <span class="priority-badge badge {{ 'bg-danger' if rule.priority_level <= 2 else 'bg-warning' if rule.priority_level <= 4 else 'bg-info' }}">
                                أولوية {{ rule.priority_level }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <!-- الشرط -->
                        <div class="mb-3">
                            <label class="form-label"><strong>الشرط:</strong></label>
                            <div class="condition-display">
                                {% if rule.condition_field %}
                                    {{ rule.condition_field }} {{ rule.condition_operator }} {{ rule.condition_value }}
                                {% else %}
                                    شرط عام
                                {% endif %}
                            </div>
                        </div>

                        <!-- معاينة الرسالة -->
                        <div class="mb-3">
                            <label class="form-label"><strong>الرسالة:</strong></label>
                            <div class="message-preview">
                                {{ rule.message_template[:100] }}{% if rule.message_template|length > 100 %}...{% endif %}
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="row text-center">
                            <div class="col-4">
                                <small class="text-muted">الفئة</small><br>
                                <span class="badge bg-secondary">{{ rule.event_category }}</span>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">الحالة</small><br>
                                <span class="badge {{ 'bg-success' if rule.is_enabled else 'bg-secondary' }}">
                                    {{ 'مفعل' if rule.is_enabled else 'معطل' }}
                                </span>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">تاريخ الإنشاء</small><br>
                                <small>{{ rule.created_at.strftime('%Y-%m-%d') if rule.created_at else 'غير محدد' }}</small>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="btn-group w-100" role="group">
                            <button class="btn btn-sm {{ 'btn-success' if not rule.is_enabled else 'btn-secondary' }}" 
                                    onclick="toggleRule({{ rule.id }}, {{ 'true' if not rule.is_enabled else 'false' }})">
                                <i class="fas {{ 'fa-play' if not rule.is_enabled else 'fa-pause' }}"></i>
                                {{ 'تفعيل' if not rule.is_enabled else 'تعطيل' }}
                            </button>
                            <button class="btn btn-info btn-sm" onclick="testRule({{ rule.id }})">
                                <i class="fas fa-paper-plane"></i> اختبار
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="editRule({{ rule.id }})">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteRule({{ rule.id }})">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="card shadow">
            <div class="card-body text-center py-5">
                <i class="fas fa-cogs fa-3x text-muted mb-3"></i>
                <h5>لا توجد قواعد مرنة</h5>
                <p class="text-muted">لم يتم إنشاء أي قواعد مرنة بعد</p>
                <a href="{{ url_for('instant_notifications.create_flexible_rule') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إنشاء أول قاعدة
                </a>
            </div>
        </div>
    {% endif %}

    <!-- معلومات مفيدة -->
    <div class="card shadow mt-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">
                <i class="fas fa-info-circle"></i>
                معلومات مفيدة حول القواعد المرنة
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">أنواع الشروط:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> <strong>يساوي (=)</strong> - مطابقة تامة</li>
                        <li><i class="fas fa-check text-success"></i> <strong>أكبر من (>)</strong> - للقيم الرقمية</li>
                        <li><i class="fas fa-check text-success"></i> <strong>أصغر من (<)</strong> - للقيم الرقمية</li>
                        <li><i class="fas fa-check text-success"></i> <strong>يحتوي على</strong> - للنصوص</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="text-success">مستويات الأولوية:</h6>
                    <ul class="list-unstyled">
                        <li><span class="badge bg-danger">1-2</span> أولوية عالية جداً</li>
                        <li><span class="badge bg-warning">3-4</span> أولوية متوسطة</li>
                        <li><span class="badge bg-info">5+</span> أولوية عادية</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleRule(ruleId, enable) {
    const action = enable ? 'تفعيل' : 'تعطيل';
    
    if (confirm(`هل تريد ${action} هذه القاعدة؟`)) {
        fetch(`/instant-notifications/api/toggle_rule/${ruleId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                is_enabled: enable
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(`تم ${action} القاعدة بنجاح`, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert(`فشل في ${action} القاعدة: ` + data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('خطأ في الاتصال: ' + error, 'error');
        });
    }
}

function testRule(ruleId) {
    if (confirm('هل تريد إرسال رسالة اختبار لهذه القاعدة؟')) {
        fetch(`/instant-notifications/api/test_rule/${ruleId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
            } else {
                showAlert('فشل في الاختبار: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('خطأ في الاتصال: ' + error, 'error');
        });
    }
}

function editRule(ruleId) {
    showAlert('ميزة التعديل ستكون متاحة قريباً', 'info');
}

function deleteRule(ruleId) {
    if (confirm('هل أنت متأكد من حذف هذه القاعدة؟ لا يمكن التراجع عن هذا الإجراء.')) {
        showAlert('ميزة الحذف ستكون متاحة قريباً', 'info');
    }
}

function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 'alert-info';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
