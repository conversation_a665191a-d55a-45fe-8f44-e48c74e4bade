<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح Modal</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            padding: 2rem;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .status-indicator {
            padding: 0.5rem 1rem;
            border-radius: 5px;
            margin: 0.5rem 0;
            font-weight: bold;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .test-button {
            margin: 0.5rem;
            min-width: 200px;
        }
        
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2 class="mb-4">
            <i class="fas fa-bug me-2"></i>
            اختبار إصلاح مشكلة Modal
        </h2>
        
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            هذه صفحة اختبار للتحقق من إصلاح مشكلة عدم إغلاق modal جهات الاتصال بشكل صحيح.
        </div>
        
        <!-- حالة الصفحة -->
        <div class="mb-4">
            <h5>حالة الصفحة:</h5>
            <div id="pageStatus" class="status-indicator status-success">
                <i class="fas fa-check me-2"></i>
                الصفحة جاهزة للاختبار
            </div>
        </div>
        
        <!-- أزرار الاختبار -->
        <div class="mb-4">
            <h5>اختبارات Modal:</h5>
            <div class="d-flex flex-wrap">
                <button type="button" class="btn btn-primary test-button" onclick="testOpenModal()">
                    <i class="fas fa-play me-2"></i>
                    اختبار فتح Modal
                </button>
                <button type="button" class="btn btn-success test-button" onclick="testConfirmSelection()">
                    <i class="fas fa-check me-2"></i>
                    اختبار تأكيد الاختيار
                </button>
                <button type="button" class="btn btn-warning test-button" onclick="testForceClose()">
                    <i class="fas fa-times me-2"></i>
                    اختبار الإغلاق القسري
                </button>
                <button type="button" class="btn btn-info test-button" onclick="checkModalState()">
                    <i class="fas fa-search me-2"></i>
                    فحص حالة Modal
                </button>
                <button type="button" class="btn btn-secondary test-button" onclick="cleanupAll()">
                    <i class="fas fa-broom me-2"></i>
                    تنظيف شامل
                </button>
            </div>
        </div>
        
        <!-- نتائج الاختبار -->
        <div class="mb-4">
            <h5>نتائج الاختبار:</h5>
            <div id="testResults">
                <p class="text-muted">لم يتم تشغيل أي اختبار بعد</p>
            </div>
        </div>
        
        <!-- معلومات التشخيص -->
        <div class="mb-4">
            <h5>معلومات التشخيص:</h5>
            <div id="debugInfo" class="debug-info">
                <div>جاري تحميل معلومات التشخيص...</div>
            </div>
        </div>
        
        <!-- تعليمات الاستخدام -->
        <div class="alert alert-secondary">
            <h6><i class="fas fa-lightbulb me-2"></i>تعليمات الاستخدام:</h6>
            <ol>
                <li>اضغط على "اختبار فتح Modal" لفتح modal جهات الاتصال</li>
                <li>اختر جهة اتصال واحدة على الأقل</li>
                <li>اضغط على "تأكيد الاختيار"</li>
                <li>تحقق من أن الصفحة لا تزال قابلة للاستخدام</li>
                <li>إذا تعطل Modal، استخدم "الإغلاق القسري"</li>
            </ol>
        </div>
    </div>

    <!-- Modal جهات الاتصال (نسخة مبسطة للاختبار) -->
    <div class="modal fade" id="contactSelectorModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">اختبار Modal جهات الاتصال</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        هذا modal اختبار. اختر أي خيار وجرب تأكيد الاختيار.
                    </div>
                    
                    <div class="mb-3">
                        <h6>جهات الاتصال التجريبية:</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="testContact1">
                            <label class="form-check-label" for="testContact1">
                                المدير العام - نشأت الفجيحي
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="testContact2">
                            <label class="form-check-label" for="testContact2">
                                فاطمة أحمد - خدمة العملاء
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h6>جهات الاتصال المختارة: <span id="selectedCount">0</span></h6>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="forceCloseModal()">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="confirmContactSelection()" id="confirmButton" disabled>
                        <i class="fas fa-check me-2"></i>
                        تأكيد الاختيار (<span id="confirmCount">0</span>)
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="forceCloseModal()" title="إغلاق قسري">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // متغيرات الاختبار
        let selectedContacts = [];
        let testResults = [];

        // دوال الاختبار
        function testOpenModal() {
            addTestResult('بدء اختبار فتح Modal...', 'info');
            
            try {
                cleanupPreviousModals();
                
                const modalElement = document.getElementById('contactSelectorModal');
                const modal = new bootstrap.Modal(modalElement, {
                    backdrop: 'static',
                    keyboard: true
                });
                
                modal.show();
                addTestResult('تم فتح Modal بنجاح', 'success');
                
                // إعداد event listeners للاختبار
                setupTestListeners();
                
            } catch (error) {
                addTestResult('خطأ في فتح Modal: ' + error.message, 'error');
            }
        }

        function testConfirmSelection() {
            addTestResult('اختبار تأكيد الاختيار...', 'info');
            
            // محاكاة اختيار جهة اتصال
            selectedContacts = [{ id: 1, name: 'اختبار' }];
            updateSelectionCount();
            
            // تأكيد الاختيار
            confirmContactSelection();
        }

        function testForceClose() {
            addTestResult('اختبار الإغلاق القسري...', 'info');
            forceCloseModal();
            addTestResult('تم تنفيذ الإغلاق القسري', 'success');
        }

        function checkModalState() {
            const modalElement = document.getElementById('contactSelectorModal');
            const backdrops = document.querySelectorAll('.modal-backdrop');
            const bodyClasses = document.body.classList;
            
            const state = {
                'Modal موجود': !!modalElement,
                'Modal مرئي': modalElement?.classList.contains('show'),
                'عدد Backdrops': backdrops.length,
                'Body modal-open': bodyClasses.contains('modal-open'),
                'Body overflow': document.body.style.overflow,
                'Modal instance': !!bootstrap.Modal.getInstance(modalElement)
            };
            
            updateDebugInfo(state);
            addTestResult('تم فحص حالة Modal - انظر معلومات التشخيص', 'info');
        }

        function cleanupAll() {
            addTestResult('تنظيف شامل...', 'info');
            cleanupPreviousModals();
            selectedContacts = [];
            updateSelectionCount();
            addTestResult('تم التنظيف الشامل', 'success');
        }

        // دوال المساعدة
        function setupTestListeners() {
            const checkboxes = document.querySelectorAll('#contactSelectorModal input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateSelectionCount();
                });
            });
        }

        function updateSelectionCount() {
            const checkboxes = document.querySelectorAll('#contactSelectorModal input[type="checkbox"]:checked');
            const count = checkboxes.length;
            
            document.getElementById('selectedCount').textContent = count;
            document.getElementById('confirmCount').textContent = count;
            document.getElementById('confirmButton').disabled = count === 0;
        }

        function confirmContactSelection() {
            addTestResult('تأكيد الاختيار...', 'info');
            
            try {
                // محاكاة callback
                if (window.onContactsSelected) {
                    window.onContactsSelected(selectedContacts);
                }
                
                // إغلاق Modal
                closeContactSelectorModal();
                addTestResult('تم تأكيد الاختيار وإغلاق Modal بنجاح', 'success');
                
            } catch (error) {
                addTestResult('خطأ في تأكيد الاختيار: ' + error.message, 'error');
                forceCloseModal();
            }
        }

        function closeContactSelectorModal() {
            const modalElement = document.getElementById('contactSelectorModal');
            
            try {
                const modalInstance = bootstrap.Modal.getInstance(modalElement);
                if (modalInstance) {
                    modalInstance.hide();
                } else {
                    const newModal = new bootstrap.Modal(modalElement);
                    newModal.hide();
                }
                
                setTimeout(() => {
                    if (modalElement.classList.contains('show')) {
                        forceCloseModal();
                    }
                }, 500);
                
            } catch (error) {
                forceCloseModal();
            }
        }

        function forceCloseModal() {
            try {
                const modalElement = document.getElementById('contactSelectorModal');
                if (modalElement) {
                    modalElement.classList.remove('show');
                    modalElement.style.display = 'none';
                    modalElement.setAttribute('aria-hidden', 'true');
                    modalElement.removeAttribute('aria-modal');
                }
                
                const backdrops = document.querySelectorAll('.modal-backdrop');
                backdrops.forEach(backdrop => backdrop.remove());
                
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
                
            } catch (error) {
                console.error('فشل في الإغلاق القسري:', error);
            }
        }

        function cleanupPreviousModals() {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
            
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
            
            const modalElement = document.getElementById('contactSelectorModal');
            if (modalElement) {
                const existingInstance = bootstrap.Modal.getInstance(modalElement);
                if (existingInstance) {
                    existingInstance.dispose();
                }
            }
        }

        function addTestResult(message, type) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const icon = type === 'success' ? 'check' : type === 'error' ? 'times' : 'info';
            const className = `status-${type === 'info' ? 'warning' : type}`;
            
            testResults.unshift({
                time: timestamp,
                message: message,
                type: type,
                icon: icon,
                className: className
            });
            
            // الاحتفاظ بآخر 10 نتائج فقط
            if (testResults.length > 10) {
                testResults = testResults.slice(0, 10);
            }
            
            updateTestResults();
        }

        function updateTestResults() {
            const container = document.getElementById('testResults');
            
            if (testResults.length === 0) {
                container.innerHTML = '<p class="text-muted">لم يتم تشغيل أي اختبار بعد</p>';
                return;
            }
            
            container.innerHTML = testResults.map(result => `
                <div class="status-indicator ${result.className}">
                    <i class="fas fa-${result.icon} me-2"></i>
                    [${result.time}] ${result.message}
                </div>
            `).join('');
        }

        function updateDebugInfo(data) {
            const container = document.getElementById('debugInfo');
            container.innerHTML = Object.entries(data).map(([key, value]) => 
                `<div><strong>${key}:</strong> ${value}</div>`
            ).join('');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            cleanupPreviousModals();
            checkModalState();
            addTestResult('تم تحميل صفحة الاختبار', 'success');
        });
    </script>
</body>
</html>
