#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إدارة طلبات الحوالات
Transfer Requests Management
"""

from flask import render_template, request as flask_request, jsonify, flash, redirect, url_for, session, send_file
from flask_login import login_required, current_user
from . import transfers_bp
from database_manager import DatabaseManager
import os
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

# تم حذف dashboard نهائياً - موجود في routes.py

@transfers_bp.route('/new-request')
@login_required
def new_request():
    """صفحة طلب حوالة جديدة"""
    return render_template('transfers/new_request.html')

@transfers_bp.route('/list-requests')
@login_required
def list_requests():
    """صفحة عرض قائمة طلبات الحوالات"""
    return render_template('transfers/list_requests_working.html')

@transfers_bp.route('/test-api')
@login_required
def test_api():
    """صفحة اختبار API"""
    return render_template('transfers/test_api.html')

@transfers_bp.route('/final-test')
@login_required
def final_test():
    """صفحة الاختبار النهائي"""
    return render_template('transfers/final_test.html')

@transfers_bp.route('/simple-debug')
@login_required
def simple_debug():
    """صفحة اختبار بسيطة للتشخيص"""
    return render_template('transfers/simple_debug.html')

@transfers_bp.route('/working')
@login_required
def working():
    """النافذة التي تعمل بدون مشاكل JavaScript"""
    return render_template('transfers/list_requests_working.html')

@transfers_bp.route('/requests/<int:request_id>/approve', methods=['POST'])
@login_required
def approve_request(request_id):
    """اعتماد طلب حوالة"""
    try:
        db = DatabaseManager()

        # التحقق من وجود الطلب وحالته
        check_query = """
        SELECT ID, STATUS, REQUEST_NUMBER
        FROM TRANSFER_REQUESTS
        WHERE ID = :1
        """

        result = db.execute_query(check_query, [request_id])

        if not result:
            return jsonify({
                'success': False,
                'message': 'طلب الحوالة غير موجود'
            }), 404

        current_status = result[0][1]
        request_number = result[0][2]

        if current_status != 'pending':
            return jsonify({
                'success': False,
                'message': f'لا يمكن اعتماد الطلب. الحالة الحالية: {current_status}'
            }), 400

        # تحديث حالة الطلب إلى معتمد
        update_query = """
        UPDATE TRANSFER_REQUESTS
        SET STATUS = 'approved',
            UPDATED_AT = SYSDATE
        WHERE ID = :1
        """

        db.execute_update(update_query, [request_id])
        db.commit()

        logger.info(f"تم اعتماد طلب الحوالة {request_number}")

        return jsonify({
            'success': True,
            'message': f'تم اعتماد طلب الحوالة {request_number} بنجاح',
            'request_id': request_id,
            'new_status': 'approved'
        })

    except Exception as e:
        logger.error(f"خطأ في اعتماد طلب الحوالة {request_id}: {e}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في اعتماد الطلب: {str(e)}'
        }), 500













@transfers_bp.route('/debug')
@login_required
def debug():
    """صفحة التشخيص المتقدمة"""
    return render_template('transfers/list_requests_debug.html')

@transfers_bp.route('/edit-request/<int:request_id>')
@login_required
def edit_request(request_id):
    """صفحة تعديل طلب حوالة"""
    try:
        db = DatabaseManager()

        # جلب الفروع
        branches_query = "SELECT BRN_NO, BRN_LNAME FROM BRANCHES ORDER BY BRN_LNAME"
        branches_result = db.execute_query(branches_query)
        branches = []
        if branches_result:
            for row in branches_result:
                branches.append({
                    'id': row[0],
                    'name': row[1]
                })

        # جلب الصرافين والبنوك
        changers_query = "SELECT ID, NAME, TYPE FROM MONEY_CHANGERS_BANKS ORDER BY NAME"
        changers_result = db.execute_query(changers_query)
        money_changers = []
        banks = []
        if changers_result:
            for row in changers_result:
                item = {
                    'id': row[0],
                    'name': row[1],
                    'type': row[2]
                }
                if row[2] == 'money_changer':
                    money_changers.append(item)
                elif row[2] == 'bank':
                    banks.append(item)

        # جلب العملات
        currencies_query = "SELECT ID, NAME_AR, CODE FROM CURRENCIES WHERE IS_ACTIVE = 1 ORDER BY NAME_AR"
        currencies_result = db.execute_query(currencies_query)
        currencies = []
        if currencies_result:
            for row in currencies_result:
                currencies.append({
                    'id': row[0],
                    'name': row[1],
                    'code': row[2]
                })

        return render_template('transfers/edit_request.html',
                             request_id=request_id,
                             branches=branches,
                             money_changers=money_changers,
                             banks=banks,
                             currencies=currencies)

    except Exception as e:
        logger.error(f"خطأ في تحميل صفحة تعديل الطلب {request_id}: {e}")
        flash(f'خطأ في تحميل الصفحة: {str(e)}', 'error')
        return redirect(url_for('transfers.list_transfer_requests'))

@transfers_bp.route('/pending-requests')
@login_required
def pending_requests():
    """صفحة الطلبات المعلقة"""
    return render_template('transfers/pending_requests.html')

@transfers_bp.route('/pending-requests-test')
@login_required
def pending_requests_test():
    """صفحة اختبار الطلبات المعلقة"""
    return render_template('transfers/pending_requests_simple.html')

@transfers_bp.route('/requests/<int:request_id>')
@login_required
def view_request(request_id):
    """صفحة عرض تفاصيل طلب الحوالة"""
    try:
        logger.info(f"📋 عرض تفاصيل طلب الحوالة ID: {request_id}")

        # جلب تفاصيل الطلب من API
        from flask import request as flask_request

        # استخدام API الموجود للحصول على التفاصيل
        db = DatabaseManager()

        # استعلام مبسط أولاً للحصول على بيانات الطلب الأساسية
        basic_query = """
        SELECT
            id, request_number, amount, currency, purpose,
            CASE
                WHEN notes IS NOT NULL THEN SUBSTR(notes, 1, 4000)
                ELSE NULL
            END as notes,
            status, created_at, updated_at, beneficiary_id, branch_id, money_changer_bank_id
        FROM TRANSFER_REQUESTS
        WHERE id = ?
        """

        logger.info(f"🔍 تنفيذ الاستعلام الأساسي للطلب {request_id}")
        basic_result = db.execute_query(basic_query, [request_id])

        if not basic_result or len(basic_result) == 0:
            logger.warning(f"❌ لم يتم العثور على الطلب {request_id} في الجدول الأساسي")
            flash(f'لم يتم العثور على طلب الحوالة رقم {request_id}', 'error')
            return redirect(url_for('transfers.list_requests'))

        request_data = basic_result[0]
        logger.info(f"✅ تم العثور على الطلب: {request_data}")

        # الحصول على بيانات المستفيد إذا كان موجوداً
        beneficiary_data = {}
        if request_data.get('beneficiary_id'):
            beneficiary_query = """
            SELECT beneficiary_name, beneficiary_address, type as beneficiary_type,
                   bank_account, bank_name, bank_branch, bank_country, iban, swift_code,
                   identification_number, phone, email
            FROM BENEFICIARIES WHERE id = ?
            """
            beneficiary_result = db.execute_query(beneficiary_query, [request_data['beneficiary_id']])
            if beneficiary_result and len(beneficiary_result) > 0:
                beneficiary_data = beneficiary_result[0]
                logger.info(f"✅ تم العثور على بيانات المستفيد")

        # الحصول على بيانات الفرع
        branch_name = 'فرع غير محدد'
        if request_data.get('branch_id'):
            branch_query = "SELECT BRN_LNAME FROM BRANCHES WHERE BRN_NO = ?"
            branch_result = db.execute_query(branch_query, [request_data['branch_id']])
            if branch_result and len(branch_result) > 0:
                branch_name = branch_result[0].get('BRN_LNAME', 'فرع غير محدد')

        # الحصول على بيانات الصراف/البنك
        money_changer_bank_name = 'غير محدد'
        money_changer_bank_type = 'money_changer'
        if request_data.get('money_changer_bank_id'):
            mcb_query = "SELECT NAME, TYPE FROM MONEY_CHANGERS_BANKS WHERE ID = ?"
            mcb_result = db.execute_query(mcb_query, [request_data['money_changer_bank_id']])
            if mcb_result and len(mcb_result) > 0:
                money_changer_bank_name = mcb_result[0].get('NAME', 'غير محدد')
                money_changer_bank_type = mcb_result[0].get('TYPE', 'money_changer')

        # دمج جميع البيانات
        complete_data = {
            **request_data,
            **beneficiary_data,
            'branch_name': branch_name,
            'created_by_name': 'مستخدم النظام',
            'money_changer_bank_name': money_changer_bank_name,
            'money_changer_bank_type': money_changer_bank_type
        }

        logger.info(f"✅ تم تجميع جميع بيانات الطلب: {complete_data.get('request_number')}")

        return render_template('transfers/view_request.html', transfer_request=complete_data)

    except Exception as e:
        logger.error(f"❌ خطأ في عرض تفاصيل الطلب {request_id}: {e}")
        flash(f'خطأ في تحميل تفاصيل الطلب: {str(e)}', 'error')
        return redirect(url_for('transfers.list_requests'))

# ===== APIs للحصول على البيانات =====

@transfers_bp.route('/api/branches')
@login_required
def api_get_branches():
    """API للحصول على قائمة الفروع"""
    try:
        db = DatabaseManager()

        query = """
        SELECT BRN_NO, BRN_LNAME, BRN_FNAME, BRN_CODE, IS_ACTIVE
        FROM BRANCHES
        WHERE NVL(IS_ACTIVE, 1) = 1
        ORDER BY BRN_LNAME
        """

        result = db.execute_query(query)

        branches = []
        if result:
            for row in result:
                branches.append({
                    'id': row[0],
                    'name_ar': row[1],
                    'name_en': row[2] if row[2] else row[1],
                    'code': row[3],
                    'is_active': row[4]
                })

        return jsonify({
            'success': True,
            'data': branches
        })

    except Exception as e:
        logger.error(f"خطأ في جلب الفروع: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب الفروع: {str(e)}'
        }), 500

@transfers_bp.route('/api/money-changers-banks')
@login_required
def api_get_money_changers_banks():
    """API للحصول على قائمة الصرافين والبنوك"""
    try:
        db = DatabaseManager()
        transfer_type = flask_request.args.get('type', '')  # 'bank' أو 'money_changer'

        query = """
        SELECT id, name, type, contact_person, phone, commission_rate
        FROM MONEY_CHANGERS_BANKS
        WHERE is_active = 1
        """

        params = []
        if transfer_type in ['bank', 'money_changer']:
            query += " AND type = :1"
            params.append(transfer_type)

        query += " ORDER BY name"

        result = db.execute_query(query, params)

        money_changers_banks = []
        if result:
            for row in result:
                money_changers_banks.append({
                    'id': row[0],
                    'name': row[1],
                    'type': row[2],
                    'contact_person': row[3],
                    'phone': row[4],
                    'commission_rate': float(row[5]) if row[5] else 0.0
                })

        return jsonify({
            'success': True,
            'data': money_changers_banks
        })

    except Exception as e:
        logger.error(f"خطأ في جلب الصرافين والبنوك: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب الصرافين والبنوك: {str(e)}'
        }), 500


@transfers_bp.route('/api/currencies')
@login_required
def api_get_currencies():
    """API للحصول على قائمة العملات"""
    try:
        db = DatabaseManager()

        query = """
        SELECT id, code, name_ar, name_en, symbol, exchange_rate, is_base_currency
        FROM CURRENCIES
        WHERE is_active = 1
        ORDER BY is_base_currency DESC, name_ar
        """

        result = db.execute_query(query)

        currencies = []
        if result:
            for row in result:
                currencies.append({
                    'id': row[0],
                    'code': row[1],
                    'name_ar': row[2],
                    'name_en': row[3],
                    'symbol': row[4],
                    'exchange_rate': float(row[5]) if row[5] else 1.0,
                    'is_base_currency': bool(row[6])
                })

        return jsonify({
            'success': True,
            'data': currencies
        })

    except Exception as e:
        logger.error(f"خطأ في جلب العملات: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب العملات: {str(e)}'
        }), 500

@transfers_bp.route('/api/beneficiaries')
@login_required
def api_get_beneficiaries():
    """API للحصول على قائمة المستفيدين"""
    try:
        db = DatabaseManager()
        search_term = flask_request.args.get('search', '').strip()

        query = """
        SELECT id, beneficiary_name, beneficiary_address, type, bank_account,
               bank_name, bank_branch, bank_country, iban, swift_code,
               identification_number, phone, email
        FROM BENEFICIARIES
        WHERE is_active = 1
        """

        params = []
        if search_term:
            query += """ AND (
                LOWER(beneficiary_name) LIKE LOWER(:1) OR
                LOWER(bank_name) LIKE LOWER(:2) OR
                bank_account LIKE :3 OR
                identification_number LIKE :4
            )"""
            search_pattern = f'%{search_term}%'
            params.extend([search_pattern, search_pattern, search_pattern, search_pattern])

        query += " ORDER BY beneficiary_name"

        result = db.execute_query(query, params)

        beneficiaries = []
        if result:
            for row in result:
                beneficiaries.append({
                    'id': row[0],
                    'beneficiary_name': row[1],
                    'beneficiary_address': row[2],
                    'type': row[3],
                    'bank_account': row[4],
                    'bank_name': row[5],
                    'bank_branch': row[6],
                    'bank_country': row[7],
                    'iban': row[8],
                    'swift_code': row[9],
                    'identification_number': row[10],
                    'phone': row[11],
                    'email': row[12]
                })

        return jsonify({
            'success': True,
            'data': beneficiaries
        })

    except Exception as e:
        logger.error(f"خطأ في جلب المستفيدين: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب المستفيدين: {str(e)}'
        }), 500

@transfers_bp.route('/api/beneficiaries/smart-search')
@login_required
def api_smart_search_beneficiaries():
    """API للبحث الذكي في المستفيدين مع اقتراحات فورية"""
    try:
        search_term = flask_request.args.get('q', '').strip()
        limit = int(flask_request.args.get('limit', 10))

        if not search_term or len(search_term) < 2:
            return jsonify([])

        db = DatabaseManager()

        # بحث ذكي محسن مع ترتيب حسب الأولوية
        query = """
        SELECT id, beneficiary_name, beneficiary_address, type,
               bank_account, bank_name, bank_branch, bank_country,
               iban, swift_code, identification_number, phone, email,
               -- حساب درجة التطابق للترتيب
               CASE
                   WHEN UPPER(beneficiary_name) = UPPER(:1) THEN 100
                   WHEN UPPER(beneficiary_name) LIKE UPPER(:2) THEN 95
                   WHEN UPPER(beneficiary_name) LIKE UPPER(:3) THEN 90
                   WHEN UPPER(bank_name) LIKE UPPER(:4) THEN 85
                   WHEN UPPER(bank_account) = UPPER(:5) THEN 88
                   WHEN UPPER(iban) = UPPER(:6) THEN 88
                   WHEN UPPER(identification_number) = UPPER(:7) THEN 87
                   WHEN UPPER(phone) LIKE UPPER(:8) THEN 80
                   WHEN UPPER(beneficiary_address) LIKE UPPER(:9) THEN 75
                   WHEN UPPER(email) LIKE UPPER(:10) THEN 75
                   ELSE 70
               END as relevance_score
        FROM BENEFICIARIES
        WHERE is_active = 1
        AND (
            UPPER(beneficiary_name) LIKE UPPER(:11) OR
            UPPER(bank_name) LIKE UPPER(:12) OR
            UPPER(bank_account) LIKE UPPER(:13) OR
            UPPER(iban) LIKE UPPER(:14) OR
            UPPER(beneficiary_address) LIKE UPPER(:15) OR
            UPPER(phone) LIKE UPPER(:16) OR
            UPPER(email) LIKE UPPER(:17) OR
            UPPER(identification_number) LIKE UPPER(:18) OR
            UPPER(bank_branch) LIKE UPPER(:19) OR
            UPPER(bank_country) LIKE UPPER(:20)
        )
        ORDER BY relevance_score DESC, beneficiary_name ASC
        """

        # إعداد معاملات البحث المختلفة
        exact_match = search_term
        starts_with = f"{search_term}%"
        contains = f"%{search_term}%"

        params = [
            exact_match,    # :1 - تطابق تام في الاسم
            starts_with,    # :2 - يبدأ بـ في الاسم
            contains,       # :3 - يحتوي في الاسم
            contains,       # :4 - يحتوي في اسم البنك
            exact_match,    # :5 - تطابق تام في رقم الحساب
            exact_match,    # :6 - تطابق تام في IBAN
            exact_match,    # :7 - تطابق تام في رقم الهوية
            contains,       # :8 - يحتوي في الهاتف
            contains,       # :9 - يحتوي في العنوان
            contains,       # :10 - يحتوي في الإيميل
            contains,       # :11-20 - البحث العام في جميع الحقول
            contains, contains, contains, contains,
            contains, contains, contains, contains, contains
        ]

        result = db.execute_query(query, params)

        suggestions = []
        if result:
            for row in result[:limit]:
                # إنشاء نص عرض غني للمستفيد
                display_name = row[1] or "غير محدد"

                # إضافة معلومات مفيدة للعرض
                info_parts = []

                if row[5]:  # اسم البنك
                    info_parts.append(f"🏦 {row[5]}")

                if row[4]:  # رقم الحساب
                    # عرض آخر 4 أرقام فقط للأمان
                    masked_account = f"****{row[4][-4:]}" if len(row[4]) > 4 else row[4]
                    info_parts.append(f"💳 {masked_account}")

                if row[7]:  # البلد
                    info_parts.append(f"🌍 {row[7]}")

                if row[3]:  # النوع
                    type_icon = "🏢" if row[3] == "supplier" else "👤"
                    info_parts.append(f"{type_icon} {row[3]}")

                # تجميع النص النهائي
                display_text = display_name
                if info_parts:
                    display_text += f" • {' • '.join(info_parts)}"

                suggestions.append({
                    'id': row[0],
                    'text': display_text,
                    'beneficiary_name': row[1],
                    'beneficiary_address': row[2],
                    'type': row[3],
                    'bank_account': row[4],
                    'bank_name': row[5],
                    'bank_branch': row[6],
                    'bank_country': row[7],
                    'iban': row[8],
                    'swift_code': row[9],
                    'identification_number': row[10],
                    'phone': row[11],
                    'email': row[12],
                    'relevance': row[13]
                })

        return jsonify(suggestions)

    except Exception as e:
        logger.error(f"خطأ في البحث الذكي للمستفيدين: {e}")
        return jsonify([]), 500

@transfers_bp.route('/api/transfer-requests', methods=['GET'])
@login_required
def api_transfer_requests():
    """API لجلب بيانات طلبات الحوالات"""
    try:
        db = DatabaseManager()

        # استعلام لجلب طلبات الحوالات مع بيانات المستفيدين والفروع والصرافين
        query = """
        SELECT
            tr.ID,
            SUBSTR(CAST(tr.REQUEST_NUMBER AS VARCHAR2(100)), 1, 100) as REQUEST_NUMBER,
            tr.AMOUNT,
            SUBSTR(CAST(tr.CURRENCY AS VARCHAR2(10)), 1, 10) as CURRENCY,
            SUBSTR(CAST(tr.PURPOSE AS VARCHAR2(500)), 1, 500) as PURPOSE,
            SUBSTR(CAST(tr.STATUS AS VARCHAR2(50)), 1, 50) as STATUS,
            SUBSTR(CAST(tr.TRANSFER_TYPE AS VARCHAR2(50)), 1, 50) as TRANSFER_TYPE,
            SUBSTR(CAST(tr.DELIVERY_METHOD AS VARCHAR2(50)), 1, 50) as DELIVERY_METHOD,
            TO_CHAR(tr.CREATED_AT, 'YYYY-MM-DD HH24:MI:SS') as CREATED_AT,
            TO_CHAR(tr.UPDATED_AT, 'YYYY-MM-DD HH24:MI:SS') as UPDATED_AT,
            tr.CREATED_BY,
            tr.UPDATED_BY,
            SUBSTR(CAST(tr.NOTES AS VARCHAR2(1000)), 1, 1000) as NOTES,
            -- بيانات المستفيد
            COALESCE(SUBSTR(CAST(b.BENEFICIARY_NAME AS VARCHAR2(200)), 1, 200), 'غير محدد') as BENEFICIARY_NAME,
            COALESCE(SUBSTR(CAST(b.BENEFICIARY_ADDRESS AS VARCHAR2(500)), 1, 500), 'غير محدد') as BENEFICIARY_ADDRESS,
            COALESCE(SUBSTR(CAST(b.PHONE AS VARCHAR2(50)), 1, 50), 'غير محدد') as BENEFICIARY_PHONE,
            -- بيانات الفرع (استخدام BRN_LNAME و BRN_NO)
            COALESCE(SUBSTR(CAST(br.BRN_LNAME AS VARCHAR2(200)), 1, 200), 'غير محدد') as BRANCH_NAME,
            -- بيانات الصراف (استخدام NAME و ID)
            COALESCE(SUBSTR(CAST(mcb.NAME AS VARCHAR2(200)), 1, 200), 'غير محدد') as MONEY_CHANGER_NAME
        FROM TRANSFER_REQUESTS tr
        LEFT JOIN BENEFICIARIES b ON tr.BENEFICIARY_ID = b.ID
        LEFT JOIN BRANCHES br ON tr.BRANCH_ID = br.BRN_NO
        LEFT JOIN MONEY_CHANGERS_BANKS mcb ON tr.MONEY_CHANGER_BANK_ID = mcb.ID
        ORDER BY tr.CREATED_AT DESC
        """

        results = db.execute_query(query)

        if results:
            # تحويل النتائج إلى قائمة من القواميس
            transfer_requests = []
            for row in results:
                # معالجة البيانات وتحويل LOB إلى string
                def safe_convert(value):
                    if value is None:
                        return None
                    # تحويل LOB إلى string
                    if hasattr(value, 'read'):
                        try:
                            return value.read().decode('utf-8') if value.read() else None
                        except:
                            return str(value) if value else None
                    # تحويل أي نوع آخر إلى string
                    return str(value) if value is not None else None

                transfer_request = {
                    'id': int(row[0]) if row[0] else 0,
                    'request_number': safe_convert(row[1]),
                    'amount': float(row[2]) if row[2] else 0,
                    'currency': safe_convert(row[3]),
                    'purpose': safe_convert(row[4]),
                    'status': safe_convert(row[5]),
                    'transfer_type': safe_convert(row[6]),
                    'delivery_method': safe_convert(row[7]),
                    'created_at': safe_convert(row[8]),
                    'updated_at': safe_convert(row[9]),
                    'created_by': int(row[10]) if row[10] else 0,
                    'updated_by': int(row[11]) if row[11] else 0,
                    'notes': safe_convert(row[12]),
                    'beneficiary_name': safe_convert(row[13]),
                    'beneficiary_address': safe_convert(row[14]),
                    'beneficiary_phone': safe_convert(row[15]),
                    'branch_name': safe_convert(row[16]),
                    'money_changer_name': safe_convert(row[17])
                }
                transfer_requests.append(transfer_request)

            return jsonify({
                'success': True,
                'data': transfer_requests,
                'count': len(transfer_requests)
            })
        else:
            return jsonify({
                'success': True,
                'data': [],
                'count': 0
            })

    except Exception as e:
        logger.error(f"خطأ في API طلبات الحوالات: {e}")
        logger.error(f"نوع الخطأ: {type(e).__name__}")

        # معالجة خاصة لخطأ LOB
        if "LOB" in str(e) or "JSON serializable" in str(e):
            error_message = "خطأ في تحويل البيانات - يرجى المحاولة مرة أخرى"
        else:
            error_message = f'حدث خطأ في جلب البيانات: {str(e)}'

        return jsonify({
            'success': False,
            'message': error_message,
            'error_type': type(e).__name__
        }), 500

@transfers_bp.route('/api/transfer-request', methods=['POST'])
@login_required
def api_create_transfer_request():
    """API لإنشاء طلب حوالة جديد"""
    try:
        db = DatabaseManager()
        data = flask_request.get_json()

        # الحصول على معرف المستخدم الحالي
        user_id = int(current_user.id) if current_user.id else 1

        # التحقق من البيانات المطلوبة
        required_fields = ['branch_id', 'transfer_type', 'money_changer_bank_id',
                          'amount', 'currency_id', 'purpose', 'beneficiary_data']

        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'success': False,
                    'message': f'الحقل {field} مطلوب'
                }), 400

        # معالجة بيانات المستفيد - حفظ البيانات المحدثة
        beneficiary_data = data['beneficiary_data']
        beneficiary_id = beneficiary_data.get('id')

        if not beneficiary_id:
            return jsonify({
                'success': False,
                'message': 'يجب اختيار مستفيد موجود'
            }), 400

        # تحديث بيانات المستفيد المحدثة
        try:
            update_query = """
            UPDATE BENEFICIARIES SET
                beneficiary_name = :1, beneficiary_address = :2, type = :3,
                bank_account = :4, bank_name = :5, bank_branch = :6, bank_country = :7,
                iban = :8, swift_code = :9, identification_number = :10, phone = :11, email = :12,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = :13
            """

            db.execute_update(update_query, [
                beneficiary_data.get('beneficiary_name', ''),
                beneficiary_data.get('beneficiary_address', ''),
                beneficiary_data.get('type', 'individual'),
                beneficiary_data.get('bank_account', ''),
                beneficiary_data.get('bank_name', ''),
                beneficiary_data.get('bank_branch', ''),
                beneficiary_data.get('bank_country', ''),
                beneficiary_data.get('iban', ''),
                beneficiary_data.get('swift_code', ''),
                beneficiary_data.get('identification_number', ''),
                beneficiary_data.get('phone', ''),
                beneficiary_data.get('email', ''),
                beneficiary_id
            ])
            logger.info(f"تم تحديث بيانات المستفيد: {beneficiary_id}")
        except Exception as e:
            logger.warning(f"تحذير: لم يتم تحديث بيانات المستفيد: {e}")


        # إنشاء رقم طلب فريد باستخدام النظام المتسلسل
        try:
            # استخدام النظام المتسلسل الجديد (بدون sequence)
            number_result = db.execute_query("SELECT GET_NEXT_TR_NUMBER() FROM DUAL")
            request_number = number_result[0][0] if number_result and number_result[0][0] else None
            
            if not request_number:
                # في حالة فشل النظام، استخدم الطريقة التقليدية
                from datetime import datetime
                current_year = datetime.now().year
                request_number = f"TR-{current_year}-0001"
        except Exception as e:
            logger.warning(f"فشل في استخدام النظام المتسلسل: {e}")
            # استخدام الطريقة التقليدية
            from datetime import datetime
            current_year = datetime.now().year
            request_number = f"TR-{current_year}-0001"

        # إنشاء طلب الحوالة مع حفظ بيانات الصراف
        request_query = """
        INSERT INTO TRANSFER_REQUESTS (
            request_number, beneficiary_id, amount, currency, purpose, notes,
            branch_id, status, created_by, updated_by, total_amount, delivery_method,
            transfer_type, money_changer_bank_id, created_at, updated_at
        ) VALUES (:1, :2, :3, :4, :5, :6, :7, 'pending', :8, :9, :10, 'bank_transfer', :11, :12, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        """

        # الحصول على رمز العملة
        currency_query = "SELECT code FROM CURRENCIES WHERE id = :1"
        currency_result = db.execute_query(currency_query, [data['currency_id']])
        currency_code = currency_result[0][0] if currency_result else 'USD'

        rows = db.execute_update(request_query, [
            request_number, beneficiary_id, data['amount'], currency_code, data['purpose'],
            data.get('notes', ''), data['branch_id'], user_id, user_id, data['amount'],
            data.get('transfer_type', 'bank'), data.get('money_changer_bank_id')
        ])

        if rows <= 0:
            return jsonify({'success': False, 'message': 'فشل حفظ طلب الحوالة'}), 500

        logger.info(f"تم إنشاء طلب حوالة جديد: {request_number} بواسطة المستخدم {user_id}")
        logger.info(f"تم حفظ بيانات الصراف: {data.get('money_changer_bank_id')} - نوع الحوالة: {data.get('transfer_type', 'bank')}")

        return jsonify({
            'success': True,
            'message': 'تم إنشاء طلب الحوالة بنجاح',
            'request_number': request_number,
            'beneficiary_id': beneficiary_id
        })

    except Exception as e:
        logger.error(f"خطأ في إنشاء طلب الحوالة: {e}")
        import traceback
        logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")

        # رسالة خطأ مفصلة للتشخيص
        error_message = str(e)
        if 'ORA-' in error_message:
            if 'ORA-00001' in error_message:
                error_message = 'خطأ: رقم الطلب مكرر'
            elif 'ORA-02291' in error_message:
                error_message = 'خطأ: بيانات مرجعية غير صحيحة (فرع، عملة، أو مستفيد غير موجود)'
            elif 'ORA-01400' in error_message:
                error_message = 'خطأ: حقل مطلوب فارغ'
            else:
                error_message = f'خطأ في قاعدة البيانات: {error_message}'

        return jsonify({
            'success': False,
            'message': error_message
        }), 500

@transfers_bp.route('/api/beneficiary/<int:beneficiary_id>')
@login_required
def api_get_beneficiary_details(beneficiary_id):
    """API للحصول على تفاصيل مستفيد محدد"""
    try:
        db = DatabaseManager()

        query = """
        SELECT id, beneficiary_name, beneficiary_address, type, bank_account,
               bank_name, bank_branch, bank_country, iban, swift_code,
               identification_number, phone, email
        FROM BENEFICIARIES
        WHERE id = :1 AND is_active = 1
        """

        result = db.execute_query(query, [beneficiary_id])

        if not result:
            return jsonify({
                'success': False,
                'message': 'المستفيد غير موجود'
            }), 404

        row = result[0]
        beneficiary = {
            'id': row[0],
            'beneficiary_name': row[1],
            'beneficiary_address': row[2],
            'type': row[3],
            'bank_account': row[4],
            'bank_name': row[5],
            'bank_branch': row[6],
            'bank_country': row[7],
            'iban': row[8],
            'swift_code': row[9],
            'identification_number': row[10],
            'phone': row[11],
            'email': row[12]
        }

        return jsonify({
            'success': True,
            'data': beneficiary
        })

    except Exception as e:
        logger.error(f"خطأ في جلب تفاصيل المستفيد: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب تفاصيل المستفيد: {str(e)}'
        }), 500

@transfers_bp.route('/api/commission-rate')
@login_required
def api_get_commission_rate():
    """API للحصول على معدل العمولة لصراف أو بنك محدد"""
    try:
        money_changer_bank_id = flask_request.args.get('id')

        if not money_changer_bank_id:
            return jsonify({
                'success': False,
                'message': 'معرف الصراف/البنك مطلوب'
            }), 400

        db = DatabaseManager()

        query = """
        SELECT commission_rate, name, type
        FROM MONEY_CHANGERS_BANKS
        WHERE id = :1 AND is_active = 1
        """

        result = db.execute_query(query, [money_changer_bank_id])

        if not result:
            return jsonify({
                'success': False,
                'message': 'الصراف/البنك غير موجود'
            }), 404

        row = result[0]

        return jsonify({
            'success': True,
            'data': {
                'commission_rate': float(row[0]) if row[0] else 0.0,
                'name': row[1],
                'type': row[2]
            }
        })

    except Exception as e:
        logger.error(f"خطأ في جلب معدل العمولة: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب معدل العمولة: {str(e)}'
        }), 500

# ===== APIs المسودات =====

@transfers_bp.route('/api/drafts', methods=['GET'])
@login_required
def api_get_drafts():
    """API للحصول على قائمة المسودات"""
    try:
        db = DatabaseManager()

        query = """
        SELECT id, draft_name, created_at, updated_at
        FROM TRANSFER_DRAFTS
        WHERE user_id = :1
        ORDER BY updated_at DESC
        """

        user_id = int(current_user.id) if current_user.id else 1
        result = db.execute_query(query, [user_id])

        drafts = []
        if result:
            for row in result:
                drafts.append({
                    'id': row[0],
                    'draft_name': row[1],
                    'created_at': row[2].strftime('%Y-%m-%d %H:%M:%S') if row[2] else None,
                    'updated_at': row[3].strftime('%Y-%m-%d %H:%M:%S') if row[3] else None
                })

        return jsonify({
            'success': True,
            'data': drafts
        })

    except Exception as e:
        logger.error(f"خطأ في جلب المسودات: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب المسودات: {str(e)}'
        }), 500

@transfers_bp.route('/api/drafts', methods=['POST'])
@login_required
def api_save_draft():
    """API لحفظ مسودة جديدة"""
    try:
        db = DatabaseManager()
        data = flask_request.get_json()

        # التحقق من البيانات المطلوبة
        if not data.get('draft_name'):
            return jsonify({
                'success': False,
                'message': 'اسم المسودة مطلوب'
            }), 400

        # تحويل بيانات المستفيد إلى JSON
        import json
        beneficiary_json = json.dumps(data.get('beneficiary_data', {}), ensure_ascii=False)

        # إدراج المسودة الجديدة
        insert_query = """
        INSERT INTO TRANSFER_DRAFTS (
            id, draft_name, user_id, branch_id, transfer_type, money_changer_bank_id,
            amount, currency_id, purpose, notes, beneficiary_data
        ) VALUES (
            TRANSFER_DRAFTS_SEQ.NEXTVAL, :1, :2, :3, :4, :5, :6, :7, :8, :9, :10
        )
        """

        user_id = int(current_user.id) if current_user.id else 1

        db.execute_update(insert_query, [
            data['draft_name'],
            user_id,
            data.get('branch_id'),
            data.get('transfer_type'),
            data.get('money_changer_bank_id'),
            data.get('amount'),
            data.get('currency_id'),
            data.get('purpose'),
            data.get('notes'),
            beneficiary_json
        ])

        # الحصول على ID المسودة الجديدة
        draft_id_query = "SELECT TRANSFER_DRAFTS_SEQ.CURRVAL FROM DUAL"
        draft_result = db.execute_query(draft_id_query)
        draft_id = draft_result[0][0] if draft_result else None

        logger.info(f"تم حفظ مسودة جديدة: {data['draft_name']} بواسطة المستخدم {user_id}")

        return jsonify({
            'success': True,
            'message': 'تم حفظ المسودة بنجاح',
            'draft_id': draft_id
        })

    except Exception as e:
        logger.error(f"خطأ في حفظ المسودة: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في حفظ المسودة: {str(e)}'
        }), 500

@transfers_bp.route('/api/drafts/<int:draft_id>', methods=['GET'])
@login_required
def api_get_draft(draft_id):
    """API للحصول على تفاصيل مسودة محددة"""
    try:
        db = DatabaseManager()

        query = """
        SELECT id, draft_name, branch_id, transfer_type, money_changer_bank_id,
               amount, currency_id, purpose, notes, beneficiary_data,
               created_at, updated_at
        FROM TRANSFER_DRAFTS
        WHERE id = :1 AND user_id = :2
        """

        user_id = int(current_user.id) if current_user.id else 1
        result = db.execute_query(query, [draft_id, user_id])

        if not result:
            return jsonify({
                'success': False,
                'message': 'المسودة غير موجودة'
            }), 404

        row = result[0]

        # تحويل بيانات المستفيد من JSON
        import json
        beneficiary_data = {}
        if row[9]:  # beneficiary_data
            try:
                beneficiary_data = json.loads(row[9])
            except:
                beneficiary_data = {}

        draft = {
            'id': row[0],
            'draft_name': row[1],
            'branch_id': row[2],
            'transfer_type': row[3],
            'money_changer_bank_id': row[4],
            'amount': float(row[5]) if row[5] else None,
            'currency_id': row[6],
            'purpose': row[7],
            'notes': row[8],
            'beneficiary_data': beneficiary_data,
            'created_at': row[10].strftime('%Y-%m-%d %H:%M:%S') if row[10] else None,
            'updated_at': row[11].strftime('%Y-%m-%d %H:%M:%S') if row[11] else None
        }

        return jsonify({
            'success': True,
            'data': draft
        })

    except Exception as e:
        logger.error(f"خطأ في جلب المسودة: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب المسودة: {str(e)}'
        }), 500

@transfers_bp.route('/api/drafts/<int:draft_id>', methods=['DELETE'])
@login_required
def api_delete_draft(draft_id):
    """API لحذف مسودة"""
    try:
        db = DatabaseManager()

        # التحقق من وجود المسودة وملكيتها للمستخدم
        check_query = """
        SELECT draft_name FROM TRANSFER_DRAFTS
        WHERE id = :1 AND user_id = :2
        """

        user_id = int(current_user.id) if current_user.id else 1
        check_result = db.execute_query(check_query, [draft_id, user_id])

        if not check_result:
            return jsonify({
                'success': False,
                'message': 'المسودة غير موجودة'
            }), 404

        # حذف المسودة
        delete_query = """
        DELETE FROM TRANSFER_DRAFTS
        WHERE id = :1 AND user_id = :2
        """

        db.execute_update(delete_query, [draft_id, user_id])

        logger.info(f"تم حذف المسودة {check_result[0][0]} بواسطة المستخدم {user_id}")

        return jsonify({
            'success': True,
            'message': 'تم حذف المسودة بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في حذف المسودة: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في حذف المسودة: {str(e)}'
        }), 500

# ===== APIs إدارة طلبات الحوالات =====

@transfers_bp.route('/api/requests', methods=['GET'])
@login_required
def api_get_transfer_requests():
    """API للحصول على قائمة طلبات الحوالات مع الفلترة"""
    try:
        db = DatabaseManager()

        # الحصول على معاملات الفلترة
        status = flask_request.args.get('status')
        branch_id = flask_request.args.get('branch_id')
        date_from = flask_request.args.get('date_from')
        date_to = flask_request.args.get('date_to')
        search = flask_request.args.get('search')

        # بناء الاستعلام مع الفلاتر
        base_query = """
        SELECT
            tr.id, tr.request_number, tr.amount, tr.currency, tr.purpose,
            CASE
                WHEN tr.notes IS NOT NULL THEN SUBSTR(tr.notes, 1, 4000)
                ELSE NULL
            END as notes,
            tr.status, tr.created_at, tr.updated_at,
            b.beneficiary_name, b.bank_account, b.bank_name, b.bank_branch, b.iban,
            COALESCE(br.BRN_LNAME, 'فرع غير محدد') as branch_name,
            'مستخدم النظام' as created_by_name,
            COALESCE(mcb.NAME, 'غير محدد') as money_changer_bank_name,
            COALESCE(mcb.TYPE, 'money_changer') as money_changer_bank_type
        FROM TRANSFER_REQUESTS tr
        LEFT JOIN BENEFICIARIES b ON tr.beneficiary_id = b.id
        LEFT JOIN BRANCHES br ON tr.branch_id = br.BRN_NO
        LEFT JOIN MONEY_CHANGERS_BANKS mcb ON tr.money_changer_bank_id = mcb.ID
        WHERE 1=1
        """

        # إضافة فلاتر
        params = []

        # فلتر الحالة - استعلام مباشر
        if status:
            base_query += f" AND UPPER(TRIM(tr.status)) = UPPER('{status.strip()}')"
            logger.info(f"🔍 فلتر الحالة: {status}")

        # فلتر الفرع
        if branch_id:
            base_query += f" AND tr.branch_id = {int(branch_id)}"
            logger.info(f"🏢 فلتر الفرع: {branch_id}")

        # فلتر التاريخ من
        if date_from:
            base_query += f" AND tr.created_at >= TO_DATE('{date_from}', 'YYYY-MM-DD')"
            logger.info(f"📅 فلتر التاريخ من: {date_from}")

        # فلتر التاريخ إلى
        if date_to:
            base_query += f" AND tr.created_at <= TO_DATE('{date_to}', 'YYYY-MM-DD') + 1"
            logger.info(f"📅 فلتر التاريخ إلى: {date_to}")

        # فلتر البحث
        if search:
            search_safe = search.replace("'", "''")  # حماية من SQL injection
            base_query += f" AND (UPPER(tr.request_number) LIKE UPPER('%{search_safe}%') OR UPPER(b.beneficiary_name) LIKE UPPER('%{search_safe}%'))"
            logger.info(f"🔍 فلتر البحث: {search}")

        base_query += " ORDER BY tr.created_at DESC"

        # تنفيذ الاستعلام
        logger.info(f"📋 تنفيذ استعلام طلبات الحوالات")
        logger.info(f"🔍 الفلاتر: status={status}, branch_id={branch_id}, search={search}")
        logger.info(f"📝 الاستعلام النهائي: {base_query}")

        result = db.execute_query(base_query)
        logger.info(f"نتائج الاستعلام: {len(result) if result else 0} سجل")

        requests = []
        if result:
            for row in result:
                # معالجة آمنة للبيانات مع تحويل LOB إلى string
                notes_value = row[5]
                if notes_value is not None:
                    # تحويل LOB إلى string إذا لزم الأمر
                    notes_value = str(notes_value) if hasattr(notes_value, 'read') else notes_value

                requests.append({
                    'id': int(row[0]) if row[0] else 0,
                    'request_number': str(row[1]) if row[1] else '',
                    'amount': float(row[2]) if row[2] else 0,
                    'currency': str(row[3]) if row[3] else '',
                    'purpose': str(row[4]) if row[4] else '',
                    'notes': notes_value,
                    'status': str(row[6]) if row[6] else '',
                    'created_at': row[7].strftime('%Y-%m-%d %H:%M:%S') if row[7] else None,
                    'updated_at': row[8].strftime('%Y-%m-%d %H:%M:%S') if row[8] else None,
                    'beneficiary_name': str(row[9]) if row[9] else '',
                    'bank_account': str(row[10]) if row[10] else '',
                    'bank_name': str(row[11]) if row[11] else '',
                    'bank_branch': str(row[12]) if row[12] else '',
                    'iban': str(row[13]) if row[13] else '',
                    'branch_name': str(row[14]) if row[14] else '',
                    'created_by_name': str(row[15]) if row[15] else '',
                    'money_changer_bank_name': str(row[16]) if row[16] else 'غير محدد',
                    'money_changer_bank_type': str(row[17]) if row[17] else 'money_changer'
                })

        # حساب الإحصائيات - مبسط جداً
        stats_query = """
        SELECT
            status,
            COUNT(*) as count,
            SUM(amount) as total_amount
        FROM TRANSFER_REQUESTS
        GROUP BY status
        """

        stats_result = db.execute_query(stats_query)

        statistics = {
            'pending': 0,
            'approved': 0,
            'rejected': 0,
            'processing': 0,
            'completed': 0,
            'total_amount': 0
        }

        if stats_result:
            for row in stats_result:
                status_key = row[0] if row[0] else 'pending'
                statistics[status_key] = row[1]
                statistics['total_amount'] += float(row[2]) if row[2] else 0

        return jsonify({
            'success': True,
            'data': requests,
            'statistics': statistics,
            'total_count': len(requests)
        })

    except Exception as e:
        logger.error(f"خطأ في جلب طلبات الحوالات: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب طلبات الحوالات: {str(e)}'
        }), 500

@transfers_bp.route('/api/requests/<int:request_id>', methods=['GET'])
@login_required
def api_get_transfer_request_details(request_id):
    """API للحصول على تفاصيل طلب حوالة محدد"""
    try:
        db = DatabaseManager()

        # أولاً، تحقق من وجود الطلب
        check_query = "SELECT COUNT(*) FROM TRANSFER_REQUESTS WHERE id = :1"
        check_result = db.execute_query(check_query, [request_id])

        if not check_result or check_result[0][0] == 0:
            return jsonify({
                'success': False,
                'message': 'الطلب غير موجود'
            }), 404

        # استعلام مبسط للتفاصيل - خطوة بخطوة
        # أولاً: جلب بيانات الطلب الأساسية مع البيانات المرجعية
        basic_query = """
        SELECT
            tr.id, tr.request_number, tr.amount, tr.currency, tr.purpose,
            CASE
                WHEN tr.notes IS NOT NULL THEN SUBSTR(tr.notes, 1, 4000)
                ELSE NULL
            END as notes,
            tr.status, tr.created_at, tr.updated_at, tr.beneficiary_id,
            tr.branch_id, tr.transfer_type, tr.money_changer_bank_id,
            COALESCE(br.BRN_LNAME, 'فرع غير محدد') as branch_name,
            COALESCE(mcb.NAME, 'غير محدد') as money_changer_bank_name,
            COALESCE(mcb.TYPE, 'غير محدد') as money_changer_bank_type
        FROM TRANSFER_REQUESTS tr
        LEFT JOIN BRANCHES br ON tr.branch_id = br.BRN_NO
        LEFT JOIN MONEY_CHANGERS_BANKS mcb ON tr.money_changer_bank_id = mcb.ID
        WHERE tr.id = :1
        """

        basic_result = db.execute_query(basic_query, [request_id])

        if not basic_result:
            return jsonify({
                'success': False,
                'message': 'الطلب غير موجود'
            }), 404

        basic_data = basic_result[0]

        # ثانياً: جلب بيانات المستفيد
        beneficiary_data = {}
        if basic_data[9]:  # beneficiary_id
            ben_query = """
            SELECT beneficiary_name, beneficiary_address, type,
                   bank_account, bank_name, bank_branch, bank_country,
                   iban, swift_code, identification_number, phone, email
            FROM BENEFICIARIES WHERE id = :1
            """
            ben_result = db.execute_query(ben_query, [basic_data[9]])
            if ben_result:
                ben_row = ben_result[0]
                beneficiary_data = {
                    'beneficiary_name': str(ben_row[0]) if ben_row[0] else '',
                    'beneficiary_address': str(ben_row[1]) if ben_row[1] else '',
                    'beneficiary_type': str(ben_row[2]) if ben_row[2] else '',
                    'bank_account': str(ben_row[3]) if ben_row[3] else '',
                    'bank_name': str(ben_row[4]) if ben_row[4] else '',
                    'bank_branch': str(ben_row[5]) if ben_row[5] else '',
                    'bank_country': str(ben_row[6]) if ben_row[6] else '',
                    'iban': str(ben_row[7]) if ben_row[7] else '',
                    'swift_code': str(ben_row[8]) if ben_row[8] else '',
                    'identification_number': str(ben_row[9]) if ben_row[9] else '',
                    'phone': str(ben_row[10]) if ben_row[10] else '',
                    'email': str(ben_row[11]) if ben_row[11] else ''
                }

        # تجميع البيانات مع معالجة آمنة للـ LOB
        notes_value = basic_data[5]
        if notes_value is not None:
            # تحويل LOB إلى string إذا لزم الأمر
            notes_value = str(notes_value) if hasattr(notes_value, 'read') else notes_value

        request_details = {
            'id': int(basic_data[0]) if basic_data[0] else 0,
            'request_number': str(basic_data[1]) if basic_data[1] else '',
            'amount': float(basic_data[2]) if basic_data[2] else 0,
            'currency': str(basic_data[3]) if basic_data[3] else '',
            'purpose': str(basic_data[4]) if basic_data[4] else '',
            'notes': notes_value,
            'status': str(basic_data[6]) if basic_data[6] else '',
            'created_at': basic_data[7].strftime('%Y-%m-%d %H:%M:%S') if basic_data[7] else None,
            'updated_at': basic_data[8].strftime('%Y-%m-%d %H:%M:%S') if basic_data[8] else None,
            'beneficiary_id': int(basic_data[9]) if basic_data[9] else None,
            'branch_id': int(basic_data[10]) if basic_data[10] else None,
            'transfer_type': str(basic_data[11]) if basic_data[11] else '',
            'money_changer_bank_id': int(basic_data[12]) if basic_data[12] else None,
            'branch_name': str(basic_data[13]) if basic_data[13] else 'فرع غير محدد',
            'money_changer_bank_name': str(basic_data[14]) if basic_data[14] else 'غير محدد',
            'money_changer_bank_type': str(basic_data[15]) if basic_data[15] else 'غير محدد',
            'created_by_name': 'مستخدم النظام',
            **beneficiary_data
        }

        logger.info(f"تم جلب تفاصيل الطلب ID: {request_id} بنجاح")

        return jsonify({
            'success': True,
            'data': request_details
        })

    except Exception as e:
        logger.error(f"خطأ في جلب تفاصيل الطلب: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب تفاصيل الطلب: {str(e)}'
        }), 500

@transfers_bp.route('/api/requests/<int:request_id>', methods=['PUT'])
@login_required
def api_update_transfer_request(request_id):
    """API لتحديث طلب حوالة"""
    try:
        logger.info(f"🔄 بدء تحديث طلب الحوالة ID: {request_id}")

        db = DatabaseManager()
        data = flask_request.get_json()

        logger.info(f"📤 البيانات المستلمة: {data}")

        # التحقق من وجود الطلب والحصول على البيانات الحالية
        check_query = "SELECT status, branch_id FROM TRANSFER_REQUESTS WHERE id = :1"
        check_result = db.execute_query(check_query, [request_id])

        if not check_result:
            logger.error(f"❌ الطلب {request_id} غير موجود")
            return jsonify({
                'success': False,
                'message': 'الطلب غير موجود'
            }), 404

        current_status = check_result[0][0]
        current_branch_id = check_result[0][1]
        logger.info(f"📋 حالة الطلب الحالية: {current_status}")
        logger.info(f"📋 الفرع الحالي: {current_branch_id}")

        # التحقق من إمكانية التعديل (فقط الطلبات المعلقة يمكن تعديلها)
        if current_status != 'pending':
            logger.warning(f"⚠️ لا يمكن تعديل الطلب في حالة: {current_status}")
            return jsonify({
                'success': False,
                'message': 'لا يمكن تعديل الطلب في هذه الحالة'
            }), 400

        # تحديث بيانات الطلب الأساسية
        user_id = int(current_user.id) if current_user.id else 1
        logger.info(f"👤 معرف المستخدم: {user_id}")

        # الحصول على رمز العملة
        currency_query = "SELECT code FROM CURRENCIES WHERE id = :1"
        currency_result = db.execute_query(currency_query, [data['currency_id']])
        currency_code = currency_result[0][0] if currency_result else 'USD'
        logger.info(f"💱 رمز العملة: {currency_code}")

        # حساب المبلغ الإجمالي (المبلغ + العمولة إن وجدت)
        amount = float(data['amount'])
        total_amount = amount  # افتراضياً، المبلغ الإجمالي = المبلغ الأساسي

        logger.info(f"💰 المبلغ الأساسي: {amount}")

        # إذا كان هناك صراف/بنك محدد، احسب العمولة
        if data.get('money_changer_bank_id'):
            commission_query = "SELECT commission_rate FROM MONEY_CHANGERS_BANKS WHERE id = :1"
            commission_result = db.execute_query(commission_query, [data.get('money_changer_bank_id')])

            if commission_result and commission_result[0][0]:
                commission_rate = float(commission_result[0][0])
                commission_amount = amount * (commission_rate / 100)
                total_amount = amount + commission_amount
                logger.info(f"💳 تم حساب العمولة: {commission_rate}% = {commission_amount}, المبلغ الإجمالي: {total_amount}")
            else:
                logger.info(f"💳 لا توجد عمولة للصراف/البنك {data.get('money_changer_bank_id')}")
        else:
            logger.info("💳 لا يوجد صراف/بنك محدد")

        logger.info(f"💰 المبلغ النهائي: {amount}, المبلغ الإجمالي النهائي: {total_amount}")

        # استعلام تحديث بسيط لتجنب مشاكل LOB
        update_request_query = """
        UPDATE TRANSFER_REQUESTS SET
            branch_id = :1,
            amount = :2,
            total_amount = :3,
            currency = :4,
            purpose = :5,
            notes = :6,
            transfer_type = :7,
            money_changer_bank_id = :8,
            updated_by = :9,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = :10
        """

        # استخدام branch_id الحالي بدلاً من الذي تم إرساله (لتجنب مشكلة قيد التكامل)
        branch_id_to_use = current_branch_id
        logger.info(f"🏢 استخدام الفرع الحالي: {branch_id_to_use}")

        logger.info(f"🔄 تنفيذ استعلام التحديث...")
        logger.info(f"📋 معاملات التحديث: branch_id={branch_id_to_use}, amount={amount}, total_amount={total_amount}, currency={currency_code}")

        # معالجة خاصة لحقل notes لتجنب مشاكل NULL/Empty
        notes_value = data.get('notes', '')
        if notes_value is None or notes_value == '':
            # إذا كان فارغاً، استخدم مسافة واحدة بدلاً من NULL
            notes_str = ' '
        else:
            # تحويل إلى string وقطع النص إذا كان طويلاً جداً
            notes_str = str(notes_value)[:4000]  # Oracle CLOB limit

        logger.info(f"📝 معالجة الملاحظات: النوع={type(notes_value)}, الطول={len(notes_str)}, القيمة='{notes_str}'")

        try:
            rows_affected = db.execute_update(update_request_query, [
                branch_id_to_use,
                amount,
                total_amount,
                currency_code,
                data['purpose'],
                notes_str,  # استخدام النص المعالج
                data.get('transfer_type'),
                data.get('money_changer_bank_id'),
                user_id,
                request_id
            ])

            logger.info(f"✅ تم تحديث {rows_affected} سجل")

            if rows_affected == 0:
                logger.warning(f"⚠️ لم يتم تحديث أي سجل للطلب {request_id}")
                return jsonify({
                    'success': False,
                    'message': 'لم يتم العثور على الطلب أو لا يمكن تحديثه'
                }), 404

        except Exception as update_error:
            logger.error(f"❌ خطأ في تحديث الطلب: {update_error}")
            return jsonify({
                'success': False,
                'message': f'خطأ في تحديث الطلب: {str(update_error)}'
            }), 500

        # تحديث بيانات المستفيد
        beneficiary_data = data.get('beneficiary_data', {})

        # الحصول على معرف المستفيد
        beneficiary_query = "SELECT beneficiary_id FROM TRANSFER_REQUESTS WHERE id = :1"
        beneficiary_result = db.execute_query(beneficiary_query, [request_id])

        if beneficiary_result and beneficiary_result[0][0]:
            beneficiary_id = beneficiary_result[0][0]

            # تحديث بيانات المستفيد
            update_beneficiary_query = """
            UPDATE BENEFICIARIES SET
                beneficiary_name = :1,
                beneficiary_address = :2,
                type = :3,
                bank_account = :4,
                bank_name = :5,
                bank_branch = :6,
                bank_country = :7,
                iban = :8,
                swift_code = :9,
                identification_number = :10,
                phone = :11,
                email = :12,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = :13
            """

            db.execute_update(update_beneficiary_query, [
                beneficiary_data.get('beneficiary_name'),
                beneficiary_data.get('beneficiary_address', ''),
                beneficiary_data.get('type', 'individual'),
                beneficiary_data.get('bank_account'),
                beneficiary_data.get('bank_name'),
                beneficiary_data.get('bank_branch', ''),
                beneficiary_data.get('bank_country', ''),
                beneficiary_data.get('iban', ''),
                beneficiary_data.get('swift_code', ''),
                beneficiary_data.get('identification_number', ''),
                beneficiary_data.get('phone', ''),
                beneficiary_data.get('email', ''),
                beneficiary_id
            ])

        logger.info(f"تم تحديث الطلب {request_id} بواسطة المستخدم {user_id}")

        return jsonify({
            'success': True,
            'message': 'تم تحديث الطلب بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في تحديث الطلب: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في تحديث الطلب: {str(e)}'
        }), 500

@transfers_bp.route('/api/requests/<int:request_id>', methods=['DELETE'])
@login_required
def api_delete_transfer_request(request_id):
    """API لحذف طلب حوالة"""
    try:
        db = DatabaseManager()

        # التحقق من وجود الطلب
        check_query = "SELECT status, request_number FROM TRANSFER_REQUESTS WHERE id = :1"
        check_result = db.execute_query(check_query, [request_id])

        if not check_result:
            return jsonify({
                'success': False,
                'message': 'الطلب غير موجود'
            }), 404

        current_status = check_result[0][0]
        request_number = check_result[0][1]

        # التحقق من إمكانية الحذف (فقط الطلبات المرفوضة أو المعلقة يمكن حذفها)
        if current_status not in ['pending', 'rejected']:
            return jsonify({
                'success': False,
                'message': f'لا يمكن حذف الطلب في حالة "{current_status}". يمكن حذف الطلبات المعلقة أو المرفوضة فقط.'
            }), 400

        # التحقق من وجود حوالات منفذة مرتبطة بهذا الطلب
        transfers_check_query = "SELECT COUNT(*) FROM TRANSFERS WHERE request_id = :1"
        transfers_result = db.execute_query(transfers_check_query, [request_id])

        if transfers_result and transfers_result[0][0] > 0:
            return jsonify({
                'success': False,
                'message': f'لا يمكن حذف الطلب لأنه مرتبط بـ {transfers_result[0][0]} حوالة منفذة'
            }), 400

        user_id = int(current_user.id) if current_user.id else 1

        # حذف الطلب
        delete_query = "DELETE FROM TRANSFER_REQUESTS WHERE id = :1"
        rows_affected = db.execute_update(delete_query, [request_id])

        if rows_affected > 0:
            logger.info(f"تم حذف طلب الحوالة {request_number} (ID: {request_id}) بواسطة المستخدم {user_id}")

            return jsonify({
                'success': True,
                'message': f'تم حذف طلب الحوالة {request_number} بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في حذف الطلب'
            }), 500

    except Exception as e:
        logger.error(f"خطأ في حذف طلب الحوالة: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في حذف الطلب: {str(e)}'
        }), 500

@transfers_bp.route('/api/requests/<int:request_id>/status', methods=['PUT'])
@login_required
def api_update_request_status(request_id):
    """API لتحديث حالة طلب الحوالة"""
    try:
        logger.info(f"🔄 تحديث حالة طلب الحوالة ID: {request_id}")

        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': 'لم يتم إرسال بيانات'
            }), 400

        new_status = data.get('status')
        reason = data.get('reason', '')

        if not new_status:
            return jsonify({
                'success': False,
                'message': 'الحالة الجديدة مطلوبة'
            }), 400

        # التحقق من صحة الحالة
        valid_statuses = ['pending', 'approved', 'rejected', 'completed']
        if new_status not in valid_statuses:
            return jsonify({
                'success': False,
                'message': f'حالة غير صحيحة. الحالات المسموحة: {", ".join(valid_statuses)}'
            }), 400

        db = DatabaseManager()

        # تحديث حالة الطلب
        update_query = """
        UPDATE TRANSFER_REQUESTS
        SET status = ?, updated_at = SYSDATE
        WHERE id = ?
        """

        result = db.execute_query(update_query, [new_status, request_id])

        if result is not None:
            logger.info(f"✅ تم تحديث حالة الطلب {request_id} إلى {new_status}")

            # إضافة سجل في تاريخ التحديثات إذا كان هناك سبب
            if reason and new_status == 'rejected':
                try:
                    history_query = """
                    INSERT INTO TRANSFER_REQUEST_HISTORY
                    (request_id, action, notes, created_at, created_by)
                    VALUES (?, ?, ?, SYSDATE, ?)
                    """

                    db.execute_query(history_query, [
                        request_id,
                        f'تم رفض الطلب',
                        f'سبب الرفض: {reason}',
                        session.get('user_id', 'system')
                    ])
                    logger.info(f"📝 تم إضافة سجل الرفض للطلب {request_id}")
                except Exception as e:
                    logger.warning(f"⚠️ فشل في إضافة سجل التاريخ: {e}")

            return jsonify({
                'success': True,
                'message': f'تم تحديث حالة الطلب إلى {new_status} بنجاح',
                'data': {
                    'request_id': request_id,
                    'new_status': new_status,
                    'reason': reason
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في تحديث حالة الطلب'
            }), 500

    except Exception as e:
        logger.error(f"❌ خطأ في تحديث حالة الطلب {request_id}: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في تحديث حالة الطلب: {str(e)}'
        }), 500

# ==================== نظام إدارة وثائق طلبات الحوالات ====================

@transfers_bp.route('/requests/<int:request_id>/documents')
@login_required
def transfer_documents(request_id):
    """صفحة إدارة وثائق طلب الحوالة"""
    try:
        logger.info(f"📋 محاولة عرض صفحة إدارة وثائق طلب الحوالة ID: {request_id}")

        # محاولة استيراد document_manager
        try:
            from .document_manager import document_manager
            logger.info("✅ تم استيراد document_manager بنجاح")
        except ImportError as e:
            logger.error(f"❌ فشل في استيراد document_manager: {e}")
            flash('خطأ في تحميل نظام إدارة الوثائق', 'error')
            return redirect(url_for('transfers.list_requests'))
        except Exception as e:
            logger.error(f"❌ خطأ عام في استيراد document_manager: {e}")
            flash('خطأ في تحميل نظام إدارة الوثائق', 'error')
            return redirect(url_for('transfers.list_requests'))

        logger.info(f"📋 عرض صفحة إدارة وثائق طلب الحوالة ID: {request_id}")

        db = DatabaseManager()

        # جلب معلومات طلب الحوالة
        request_query = """
        SELECT id, request_number, amount, currency, status,
               created_at, beneficiary_id, branch_id
        FROM TRANSFER_REQUESTS
        WHERE id = :1
        """

        request_result = db.execute_query(request_query, [request_id])

        if not request_result or len(request_result) == 0:
            logger.warning(f"⚠️ لم يتم العثور على طلب الحوالة رقم {request_id}")
            flash(f'لم يتم العثور على طلب الحوالة رقم {request_id}', 'error')
            db.close()
            return redirect(url_for('transfers.list_requests'))

        request_data = request_result[0]

        # جلب معلومات المستفيد
        beneficiary_name = 'غير محدد'
        if request_data[6]:  # beneficiary_id
            beneficiary_query = "SELECT beneficiary_name FROM BENEFICIARIES WHERE id = :1"
            beneficiary_result = db.execute_query(beneficiary_query, [request_data[6]])
            if beneficiary_result:
                beneficiary_name = beneficiary_result[0][0]

        # جلب معلومات الفرع
        branch_name = 'غير محدد'
        if request_data[7]:  # branch_id
            branch_query = "SELECT BRN_LNAME FROM BRANCHES WHERE BRN_NO = :1"
            branch_result = db.execute_query(branch_query, [request_data[7]])
            if branch_result:
                branch_name = branch_result[0][0]

        # تحويل التاريخ إلى نص آمن
        created_at = request_data[5]
        if created_at:
            try:
                if hasattr(created_at, 'strftime'):
                    created_at = created_at.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    created_at = str(created_at)
            except:
                created_at = str(created_at)
        else:
            created_at = ''

        # تجميع معلومات الطلب
        transfer_request = {
            'id': request_data[0],
            'request_number': request_data[1],
            'amount': request_data[2],
            'currency': request_data[3] or 'TRY',
            'status': request_data[4],
            'created_at': created_at,
            'beneficiary_name': beneficiary_name,
            'branch_name': branch_name
        }

        # جلب الوثائق
        try:
            documents = document_manager.get_request_documents(request_id)
            logger.info(f"✅ تم جلب {len(documents)} وثيقة للطلب {request_id}")
        except Exception as e:
            logger.error(f"❌ خطأ في جلب الوثائق: {e}")
            documents = []

        # جلب إحصائيات الوثائق
        try:
            stats = document_manager.get_document_stats(request_id)
            logger.info(f"✅ تم جلب إحصائيات الوثائق للطلب {request_id}")
        except Exception as e:
            logger.error(f"❌ خطأ في جلب إحصائيات الوثائق: {e}")
            stats = {'total': 0, 'by_type': {}}

        db.close()

        logger.info(f"✅ تم تحميل صفحة إدارة الوثائق للطلب {request_data[1]}")

        return render_template('transfers/transfer_documents.html',
                             transfer_request=transfer_request,
                             documents=documents,
                             stats=stats,
                             document_types=document_manager.DOCUMENT_TYPES)

    except Exception as e:
        logger.error(f"❌ خطأ في تحميل صفحة إدارة الوثائق: {e}")
        import traceback
        logger.error(f"❌ تفاصيل الخطأ: {traceback.format_exc()}")
        flash(f'خطأ في تحميل صفحة إدارة الوثائق: {str(e)}', 'error')
        return redirect(url_for('transfers.list_requests'))

# Route بديل للاختبار
@transfers_bp.route('/requests/<int:request_id>/documents-test')
@login_required
def transfer_documents_test(request_id):
    """صفحة اختبار إدارة الوثائق"""
    try:
        logger.info(f"🧪 اختبار صفحة إدارة الوثائق للطلب {request_id}")

        # إنشاء بيانات تجريبية
        transfer_request = {
            'id': request_id,
            'request_number': f'TR{request_id:06d}',
            'amount': 1000.00,
            'currency': 'TRY',
            'status': 'pending',
            'created_at': 'اليوم',
            'beneficiary_name': 'مستفيد تجريبي',
            'branch_name': 'فرع تجريبي'
        }

        documents = []
        stats = {'total': 0, 'by_type': {}}
        document_types = {
            'identity_document': 'وثيقة الهوية',
            'passport': 'جواز السفر',
            'bank_statement': 'كشف حساب بنكي',
            'other': 'أخرى'
        }

        return render_template('transfers/transfer_documents.html',
                             transfer_request=transfer_request,
                             documents=documents,
                             stats=stats,
                             document_types=document_types)

    except Exception as e:
        logger.error(f"❌ خطأ في صفحة الاختبار: {e}")
        return f"خطأ في صفحة الاختبار: {str(e)}", 500

@transfers_bp.route('/requests/<int:request_id>/documents/upload', methods=['POST'])
@login_required
def upload_transfer_document(request_id):
    """رفع وثيقة جديدة لطلب الحوالة"""
    try:
        from .document_manager import document_manager

        logger.info(f"📤 رفع وثيقة جديدة لطلب الحوالة ID: {request_id}")

        # التحقق من البيانات المطلوبة
        document_type = flask_request.form.get('document_type')
        document_name = flask_request.form.get('document_name', '')
        notes = flask_request.form.get('notes', '')

        if not document_type:
            return jsonify({'success': False, 'message': 'نوع الوثيقة مطلوب'})

        # التحقق من وجود ملف
        if 'document_file' not in flask_request.files:
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})

        file = flask_request.files['document_file']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})

        # التحقق من نوع الملف
        if not document_manager.is_allowed_file(file.filename):
            return jsonify({
                'success': False,
                'message': f'نوع الملف غير مدعوم. الأنواع المدعومة: {", ".join(document_manager.ALLOWED_EXTENSIONS)}'
            })

        # رفع الوثيقة
        result = document_manager.upload_document(
            transfer_request_id=request_id,
            document_type=document_type,
            file=file,
            document_name=document_name,
            notes=notes,
            uploaded_by=session.get('username', 'system')
        )

        logger.info(f"✅ نتيجة رفع الوثيقة: {result}")
        return jsonify(result)

    except Exception as e:
        import traceback
        logger.error(f"❌ خطأ في رفع الوثيقة: {e}")
        logger.error(f"❌ تفاصيل الخطأ: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'message': f'خطأ في رفع الوثيقة: {str(e)}'
        }), 500

@transfers_bp.route('/documents/<int:document_id>/delete', methods=['POST'])
@login_required
def delete_transfer_document(document_id):
    """حذف وثيقة طلب الحوالة"""
    try:
        from .document_manager import document_manager

        logger.info(f"🗑️ حذف وثيقة ID: {document_id}")

        result = document_manager.delete_document(
            document_id=document_id,
            user=session.get('username', 'system')
        )

        logger.info(f"✅ نتيجة حذف الوثيقة: {result}")
        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ خطأ في حذف الوثيقة: {e}")
        return jsonify({'success': False, 'message': str(e)})

@transfers_bp.route('/documents/<int:document_id>/download')
@login_required
def download_transfer_document(document_id):
    """تحميل وثيقة طلب الحوالة"""
    try:
        logger.info(f"⬇️ تحميل وثيقة ID: {document_id}")

        db = DatabaseManager()

        # جلب معلومات الوثيقة
        query = """
        SELECT file_path, file_name, document_name
        FROM transfer_request_documents
        WHERE id = :1
        """

        result = db.execute_query(query, [document_id])
        db.close()

        if not result:
            return "الوثيقة غير موجودة", 404

        file_path, file_name, document_name = result[0]

        if os.path.exists(file_path):
            download_name = f"{document_name}_{file_name}" if document_name else file_name
            return send_file(file_path, as_attachment=True, download_name=download_name)
        else:
            return "الملف غير موجود", 404

    except Exception as e:
        logger.error(f"❌ خطأ في تحميل الوثيقة {document_id}: {e}")
        return "خطأ في تحميل الوثيقة", 500

@transfers_bp.route('/documents/<int:document_id>/preview')
@login_required
def preview_transfer_document(document_id):
    """استعراض وثيقة طلب الحوالة"""
    try:
        logger.info(f"👁️ استعراض وثيقة ID: {document_id}")

        db = DatabaseManager()

        # جلب معلومات الوثيقة
        query = """
        SELECT file_path, file_name, document_name, mime_type
        FROM transfer_request_documents
        WHERE id = :1
        """

        result = db.execute_query(query, [document_id])

        logger.info(f"🔍 البحث عن وثيقة ID: {document_id}")
        logger.info(f"📋 نتيجة الاستعلام: {result}")

        if not result:
            # محاولة البحث بدون شرط is_deleted للتشخيص
            debug_query = "SELECT id, file_name, is_deleted FROM transfer_request_documents WHERE id = :1"
            debug_result = db.execute_query(debug_query, [document_id])
            logger.info(f"🔍 تشخيص الوثيقة: {debug_result}")

            db.close()
            return f"الوثيقة غير موجودة - ID: {document_id}", 404

        file_path, file_name, document_name, mime_type = result[0]

        logger.info(f"📁 مسار الملف: {file_path}")
        logger.info(f"📄 اسم الملف: {file_name}")
        logger.info(f"🏷️ نوع MIME: {mime_type}")
        logger.info(f"📂 هل الملف موجود: {os.path.exists(file_path)}")

        db.close()

        if os.path.exists(file_path):
            # تحديد نوع العرض حسب نوع الملف
            if mime_type and ('pdf' in mime_type.lower() or 'image' in mime_type.lower()):
                # عرض مباشر في المتصفح للـ PDF والصور
                logger.info(f"📄 عرض PDF/صورة: {file_path}")
                return send_file(file_path, mimetype=mime_type)
            else:
                # للملفات الأخرى، عرض في نافذة منبثقة أو تحميل
                logger.info(f"📁 عرض ملف آخر: {file_path}")
                return send_file(file_path, as_attachment=False)
        else:
            logger.error(f"❌ الملف غير موجود في المسار: {file_path}")
            return f"الملف غير موجود في المسار: {file_path}", 404

    except Exception as e:
        logger.error(f"❌ خطأ في استعراض الوثيقة {document_id}: {e}")
        return "خطأ في استعراض الوثيقة", 500

@transfers_bp.route('/documents/<int:document_id>/create-link', methods=['POST'])
@login_required
def create_transfer_document_share_link(document_id):
    """إنشاء رابط مشاركة لوثيقة طلب الحوالة"""
    try:
        from app.shipments.cloud_link_manager import cloud_link_manager

        logger.info(f"🔗 إنشاء رابط مشاركة للوثيقة ID: {document_id}")

        # جلب معلومات الوثيقة
        db = DatabaseManager()

        query = """
            SELECT trd.file_path, trd.file_name, trd.document_name,
                   tr.request_number
            FROM transfer_request_documents trd
            JOIN TRANSFER_REQUESTS tr ON trd.transfer_request_id = tr.id
            WHERE trd.id = :1
        """

        result = db.execute_query(query, [document_id])

        if not result:
            db.close()
            return jsonify({'success': False, 'message': 'الوثيقة غير موجودة'})

        file_path, file_name, document_name, request_number = result[0]

        # التحقق من وجود الملف
        if not os.path.exists(file_path):
            db.close()
            return jsonify({'success': False, 'message': 'ملف الوثيقة غير موجود'})

        # جلب الخدمة المطلوبة (nextcloud أو onedrive)
        service = flask_request.json.get('service', 'nextcloud') if flask_request.is_json else 'nextcloud'

        # التحقق من صحة الخدمة
        if service not in ['nextcloud', 'onedrive']:
            db.close()
            return jsonify({
                'success': False,
                'message': 'خدمة غير مدعومة. استخدم nextcloud أو onedrive'
            }), 400

        logger.info(f"📋 الخدمة المطلوبة: {service}")

        # التحقق من وجود رابط مسبق لنفس الخدمة
        check_query = f"""
            SELECT {service}_share_link
            FROM transfer_request_documents
            WHERE id = :1
        """

        existing_result = db.execute_query(check_query, [document_id])
        existing_link = existing_result[0][0] if existing_result and existing_result[0][0] else None

        if existing_link:
            logger.info(f"⚠️ رابط {service} موجود مسبقاً للوثيقة {document_id}")
            db.close()
            return jsonify({
                'success': True,
                'message': f'رابط {service} موجود مسبقاً',
                'share_link': existing_link,
                'service': service,
                'is_existing': True
            })

        # إنشاء اسم ملف مع معلومات طلب الحوالة (بدون تكرار)
        # استخراج امتداد الملف
        file_extension = os.path.splitext(file_name)[1] if file_name else ''
        # إنشاء اسم نظيف بدون تكرار
        safe_filename = f"{request_number}_{document_name}{file_extension}"

        # إنشاء رابط المشاركة
        result_data = cloud_link_manager.create_share_link(file_path, safe_filename, service)

        if 'error' in result_data:
            error_message = result_data['error']
            help_message = result_data.get('help', '')

            # رسائل خطأ مفصلة
            if 'لا توجد خدمات سحابية مفعلة' in error_message:
                error_message = 'لا توجد خدمات سحابية مفعلة حالياً.\n\n💡 الحلول المتاحة:\n• تفعيل Nextcloud بعد إصلاح إعدادات WebDAV\n• إعداد OneDrive كبديل\n• استخدام التحميل المباشر للوثائق'
            elif '401' in error_message or 'Unauthorized' in error_message:
                error_message = 'خطأ في المصادقة مع الخدمة السحابية.\n\n💡 يرجى:\n• التحقق من اسم المستخدم وكلمة المرور\n• إنشاء كلمة مرور تطبيق جديدة\n• التأكد من تفعيل المستخدم في Nextcloud'
            elif '429' in error_message or 'Too Many Requests' in error_message:
                error_message = 'الخادم مشغول حالياً (الكثير من الطلبات).\n\n💡 يرجى:\n• المحاولة مرة أخرى بعد دقيقة\n• تجنب النقر المتكرر\n• النظام سيعيد المحاولة تلقائياً'

            full_message = error_message
            if help_message:
                full_message += f'\n\nمساعدة: {help_message}'

            db.close()
            return jsonify({'success': False, 'message': full_message})

        # حفظ رابط المشاركة في قاعدة البيانات
        if result_data.get('success'):
            # تحديث العمود المناسب حسب الخدمة
            if service == 'nextcloud':
                update_query = """
                    UPDATE transfer_request_documents
                    SET nextcloud_share_link = :1,
                        nextcloud_service_info = :2,
                        nextcloud_created_at = SYSDATE
                    WHERE id = :3
                """
            else:  # onedrive
                update_query = """
                    UPDATE transfer_request_documents
                    SET onedrive_share_link = :1,
                        onedrive_service_info = :2,
                        onedrive_created_at = SYSDATE
                    WHERE id = :3
                """

            db.execute_update(update_query, [
                result_data['share_link'],
                result_data['service'],
                document_id
            ])

        db.close()

        return jsonify({
            'success': True,
            'message': f'تم إنشاء رابط {service} بنجاح',
            'share_link': result_data['share_link'],
            'service': service,
            'is_existing': False
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء رابط المشاركة: {e}")
        return jsonify({'success': False, 'message': 'حدث خطأ في إنشاء رابط المشاركة'})

@transfers_bp.route('/requests/<int:request_id>/documents/list')
@login_required
def get_transfer_documents_list(request_id):
    """جلب قائمة وثائق طلب الحوالة (API)"""
    try:
        from .document_manager import document_manager

        logger.info(f"📋 جلب قائمة وثائق طلب الحوالة ID: {request_id}")

        # جلب الوثائق
        documents = document_manager.get_request_documents(request_id)

        logger.info(f"✅ تم جلب {len(documents)} وثيقة للطلب {request_id}")

        return jsonify({
            'success': True,
            'documents': documents
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب قائمة الوثائق: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        })

@transfers_bp.route('/requests/<int:request_id>/documents/stats')
@login_required
def get_transfer_documents_stats(request_id):
    """جلب إحصائيات وثائق طلب الحوالة (API)"""
    try:
        from .document_manager import document_manager

        logger.info(f"📊 جلب إحصائيات وثائق طلب الحوالة ID: {request_id}")

        # جلب الإحصائيات
        stats = document_manager.get_document_stats(request_id)

        logger.info(f"✅ تم جلب إحصائيات الوثائق للطلب {request_id}")

        return jsonify({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب إحصائيات الوثائق: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        })

# ===== APIs البيانات المرجعية =====

@transfers_bp.route('/api/money-changers', methods=['GET'])
@login_required
def api_get_money_changers():
    """API للحصول على قائمة الصرافين"""
    try:
        db = DatabaseManager()

        query = "SELECT id, name FROM MONEY_CHANGERS_BANKS WHERE type = 'money_changer' AND is_active = 1 ORDER BY name"
        result = db.execute_query(query)

        money_changers = []
        if result:
            for row in result:
                money_changers.append({
                    'id': row[0],
                    'name': row[1]
                })

        return jsonify({
            'success': True,
            'data': money_changers
        })

    except Exception as e:
        logger.error(f"خطأ في جلب الصرافين: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب الصرافين: {str(e)}'
        }), 500

@transfers_bp.route('/api/banks', methods=['GET'])
@login_required
def api_get_banks():
    """API للحصول على قائمة البنوك"""
    try:
        db = DatabaseManager()

        query = "SELECT id, name FROM MONEY_CHANGERS_BANKS WHERE type = 'bank' AND is_active = 1 ORDER BY name"
        result = db.execute_query(query)

        banks = []
        if result:
            for row in result:
                banks.append({
                    'id': row[0],
                    'name': row[1]
                })

        return jsonify({
            'success': True,
            'data': banks
        })

    except Exception as e:
        logger.error(f"خطأ في جلب البنوك: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب البنوك: {str(e)}'
        }), 500

@transfers_bp.route('/print-request/<int:request_id>')
@login_required
def print_transfer_request(request_id):
    """طباعة نموذج طلب الحوالة"""
    try:
        db = DatabaseManager()

        # جلب بيانات الطلب مع جميع التفاصيل
        query = """
        SELECT
            tr.id, tr.request_number, tr.amount, tr.currency, tr.purpose,
            CASE
                WHEN tr.notes IS NOT NULL THEN SUBSTR(tr.notes, 1, 4000)
                ELSE NULL
            END as notes,
            tr.status, tr.created_at, tr.updated_at, tr.transfer_type,
            tr.money_changer_bank_id,
            b.beneficiary_name, b.beneficiary_address, b.type as beneficiary_type,
            b.bank_account, b.bank_name, b.bank_branch, b.bank_country,
            b.iban, b.swift_code, b.identification_number, b.phone, b.email,
            COALESCE(br.BRN_LNAME, 'فرع غير محدد') as branch_name,
            COALESCE(mcb.NAME, 'غير محدد') as money_changer_bank_name,
            COALESCE(mcb.TYPE, 'غير محدد') as money_changer_bank_type,
            'مستخدم النظام' as created_by_name,
            br.BRN_FNAME as branch_name_en,
            br.BRN_LADD as branch_address_ar,
            br.BRN_FADD as branch_address_en,
            br.BRN_LTELE as branch_phone_ar,
            br.BRN_FTELE as branch_phone_en,
            br.BRN_IMG as branch_logo
        FROM TRANSFER_REQUESTS tr
        LEFT JOIN BENEFICIARIES b ON tr.beneficiary_id = b.id
        LEFT JOIN BRANCHES br ON tr.branch_id = br.BRN_NO
        LEFT JOIN MONEY_CHANGERS_BANKS mcb ON tr.money_changer_bank_id = mcb.ID
        WHERE tr.id = :1
        """

        result = db.execute_query(query, [request_id])

        if not result:
            flash('الطلب غير موجود', 'error')
            return redirect(url_for('transfers.list_requests'))

        # تحويل البيانات إلى كائن
        row = result[0]
        request_data = {
            'id': row[0],
            'request_number': row[1],
            'amount': float(row[2]) if row[2] else 0,
            'currency': row[3],
            'purpose': row[4],
            'notes': str(row[5]) if row[5] else None,
            'status': row[6],
            'created_at': row[7],
            'updated_at': row[8],
            'transfer_type': row[9],
            'money_changer_bank_id': row[10],
            'beneficiary_name': row[11],
            'beneficiary_address': row[12],
            'beneficiary_type': row[13],
            'bank_account': row[14],
            'bank_name': row[15],
            'bank_branch': row[16],
            'bank_country': row[17],
            'iban': row[18],
            'swift_code': row[19],
            'identification_number': row[20],
            'beneficiary_phone': row[21],
            'beneficiary_email': row[22],
            'branch_name': row[23],
            'money_changer_bank_name': row[24],
            'money_changer_bank_type': row[25],
            'created_by_name': row[26],
            'branch_name_en': row[27],
            'branch_address_ar': row[28],
            'branch_address_en': row[29],
            'branch_phone_ar': row[30],
            'branch_phone_en': row[31],
            'branch_logo': row[32]
        }

        # تحويل إلى كائن
        from types import SimpleNamespace
        request_obj = SimpleNamespace(**request_data)

        return render_template('transfers/print_request.html', request=request_obj)

    except Exception as e:
        logger.error(f"خطأ في طباعة طلب الحوالة {request_id}: {e}")
        flash(f'خطأ في طباعة الطلب: {str(e)}', 'error')
        return redirect(url_for('transfers.list_requests'))
