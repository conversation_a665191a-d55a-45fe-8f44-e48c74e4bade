# -*- coding: utf-8 -*-
"""
API نظام مطابقة أرصدة الموردين
Supplier Balance Reconciliation API
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from app.database_manager import DatabaseManager
import logging
import json
from datetime import datetime, date
from decimal import Decimal

# إنشاء Blueprint
reconciliation_api_bp = Blueprint('reconciliation_api', __name__, url_prefix='/api/suppliers/reconciliation')

logger = logging.getLogger(__name__)

def decimal_default(obj):
    """تحويل Decimal إلى float للـ JSON"""
    if isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, (date, datetime)):
        return obj.isoformat()
    raise TypeError

@reconciliation_api_bp.route('/cycles', methods=['GET'])
@login_required
def get_reconciliation_cycles():
    """API للحصول على قائمة دورات المطابقة"""
    try:
        db = DatabaseManager()
        
        # معاملات البحث
        status_filter = request.args.get('status', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        
        # بناء الاستعلام
        base_query = """
        SELECT 
            cycle_id, cycle_name, cycle_description, reconciliation_date,
            period_from, period_to, cycle_type, status,
            total_suppliers, matched_suppliers, unmatched_suppliers,
            total_differences_amount, started_date, completed_date,
            created_by_name, completed_by_name
        FROM V_RECONCILIATION_SUMMARY
        WHERE 1=1
        """
        
        params = []
        param_counter = 1
        
        if status_filter:
            base_query += f" AND cycle_status = :{param_counter}"
            params.append(status_filter)
            param_counter += 1
        
        if date_from:
            base_query += f" AND reconciliation_date >= TO_DATE(:{param_counter}, 'YYYY-MM-DD')"
            params.append(date_from)
            param_counter += 1
        
        if date_to:
            base_query += f" AND reconciliation_date <= TO_DATE(:{param_counter}, 'YYYY-MM-DD')"
            params.append(date_to)
            param_counter += 1
        
        base_query += " ORDER BY reconciliation_date DESC"
        
        results = db.execute_query(base_query, params)
        
        cycles = []
        for row in results:
            cycles.append({
                'cycle_id': row[0],
                'cycle_name': row[1],
                'cycle_description': row[2],
                'reconciliation_date': row[3].strftime('%Y-%m-%d') if row[3] else None,
                'period_from': row[4].strftime('%Y-%m-%d') if row[4] else None,
                'period_to': row[5].strftime('%Y-%m-%d') if row[5] else None,
                'cycle_type': row[6],
                'status': row[7],
                'total_suppliers': int(row[8]) if row[8] else 0,
                'matched_suppliers': int(row[9]) if row[9] else 0,
                'unmatched_suppliers': int(row[10]) if row[10] else 0,
                'total_differences_amount': float(row[11]) if row[11] else 0,
                'started_date': row[12].strftime('%Y-%m-%d %H:%M:%S') if row[12] else None,
                'completed_date': row[13].strftime('%Y-%m-%d %H:%M:%S') if row[13] else None,
                'created_by_name': row[14],
                'completed_by_name': row[15]
            })
        
        return jsonify({
            'success': True,
            'cycles': cycles,
            'total_count': len(cycles)
        })
        
    except Exception as e:
        logger.error(f"خطأ في جلب دورات المطابقة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@reconciliation_api_bp.route('/cycles', methods=['POST'])
@login_required
def create_reconciliation_cycle():
    """API لإنشاء دورة مطابقة جديدة"""
    try:
        db = DatabaseManager()
        data = request.get_json()
        
        # التحقق من البيانات المطلوبة
        required_fields = ['cycle_name', 'period_from', 'period_to']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'success': False, 'message': f'الحقل {field} مطلوب'}), 400
        
        cycle_name = data['cycle_name']
        period_from = datetime.strptime(data['period_from'], '%Y-%m-%d').date()
        period_to = datetime.strptime(data['period_to'], '%Y-%m-%d').date()
        cycle_type = data.get('cycle_type', 'MONTHLY')
        
        # التحقق من عدم تداخل الفترات
        overlap_check = db.execute_query("""
            SELECT COUNT(*) FROM RECONCILIATION_CYCLES
            WHERE status IN ('OPEN', 'IN_PROGRESS')
            AND ((period_from <= :1 AND period_to >= :1) 
                 OR (period_from <= :2 AND period_to >= :2)
                 OR (period_from >= :1 AND period_to <= :2))
        """, [period_from, period_to])
        
        if overlap_check[0][0] > 0:
            return jsonify({
                'success': False, 
                'message': 'توجد دورة مطابقة أخرى مفتوحة لنفس الفترة'
            }), 400
        
        # إنشاء الدورة باستخدام الإجراء المخزن
        cycle_id = db.call_procedure_with_output(
            'CREATE_RECONCILIATION_CYCLE',
            [cycle_name, period_from, period_to, cycle_type, current_user.id]
        )
        
        return jsonify({
            'success': True,
            'message': 'تم إنشاء دورة المطابقة بنجاح',
            'cycle_id': cycle_id
        })
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء دورة المطابقة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@reconciliation_api_bp.route('/cycles/<int:cycle_id>/start', methods=['POST'])
@login_required
def start_reconciliation_cycle(cycle_id):
    """API لبدء عملية المطابقة"""
    try:
        db = DatabaseManager()
        
        # بدء عملية المطابقة باستخدام الإجراء المخزن
        db.execute_procedure('START_RECONCILIATION_CYCLE', [cycle_id, current_user.id])
        
        return jsonify({
            'success': True,
            'message': 'تم بدء عملية المطابقة بنجاح'
        })
        
    except Exception as e:
        logger.error(f"خطأ في بدء عملية المطابقة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@reconciliation_api_bp.route('/cycles/<int:cycle_id>/reconciliations')
@login_required
def get_cycle_reconciliations(cycle_id):
    """API للحصول على مطابقات دورة معينة"""
    try:
        db = DatabaseManager()
        
        query = """
        SELECT 
            reconciliation_id, supplier_id, supplier_code, supplier_name,
            currency_code, currency_symbol,
            system_opening_balance, system_debit_amount, system_credit_amount, system_closing_balance,
            supplier_opening_balance, supplier_debit_amount, supplier_credit_amount, supplier_closing_balance,
            opening_difference, debit_difference, credit_difference, closing_difference, total_difference,
            reconciliation_status, auto_matched, differences_count, resolved_differences,
            supplier_statement_date, supplier_statement_reference,
            reconciled_by_name, reconciled_date, approved_by_name, approved_date
        FROM V_SUPPLIER_RECONCILIATION_DETAILS
        WHERE cycle_id = :1
        ORDER BY ABS(total_difference) DESC, supplier_name
        """
        
        results = db.execute_query(query, [cycle_id])
        
        reconciliations = []
        for row in results:
            reconciliations.append({
                'reconciliation_id': row[0],
                'supplier_id': row[1],
                'supplier_code': row[2],
                'supplier_name': row[3],
                'currency_code': row[4],
                'currency_symbol': row[5],
                'system_balances': {
                    'opening': float(row[6]) if row[6] else 0,
                    'debit': float(row[7]) if row[7] else 0,
                    'credit': float(row[8]) if row[8] else 0,
                    'closing': float(row[9]) if row[9] else 0
                },
                'supplier_balances': {
                    'opening': float(row[10]) if row[10] else 0,
                    'debit': float(row[11]) if row[11] else 0,
                    'credit': float(row[12]) if row[12] else 0,
                    'closing': float(row[13]) if row[13] else 0
                },
                'differences': {
                    'opening': float(row[14]) if row[14] else 0,
                    'debit': float(row[15]) if row[15] else 0,
                    'credit': float(row[16]) if row[16] else 0,
                    'closing': float(row[17]) if row[17] else 0,
                    'total': float(row[18]) if row[18] else 0
                },
                'reconciliation_status': row[19],
                'auto_matched': bool(row[20] == 'Y'),
                'differences_count': int(row[21]) if row[21] else 0,
                'resolved_differences': int(row[22]) if row[22] else 0,
                'supplier_statement_date': row[23].strftime('%Y-%m-%d') if row[23] else None,
                'supplier_statement_reference': row[24],
                'reconciled_by_name': row[25],
                'reconciled_date': row[26].strftime('%Y-%m-%d %H:%M:%S') if row[26] else None,
                'approved_by_name': row[27],
                'approved_date': row[28].strftime('%Y-%m-%d %H:%M:%S') if row[28] else None
            })
        
        return jsonify({
            'success': True,
            'reconciliations': reconciliations,
            'total_count': len(reconciliations)
        })
        
    except Exception as e:
        logger.error(f"خطأ في جلب مطابقات الدورة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@reconciliation_api_bp.route('/reconciliations/<int:reconciliation_id>/statement', methods=['PUT'])
@login_required
def update_supplier_statement(reconciliation_id):
    """API لتحديث بيانات كشف المورد"""
    try:
        db = DatabaseManager()
        data = request.get_json()
        
        # التحقق من البيانات المطلوبة
        required_fields = ['supplier_opening_balance', 'supplier_debit_amount', 
                          'supplier_credit_amount', 'supplier_closing_balance']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'message': f'الحقل {field} مطلوب'}), 400
        
        # تحديث بيانات كشف المورد باستخدام الإجراء المخزن
        db.execute_procedure('UPDATE_SUPPLIER_STATEMENT', [
            reconciliation_id,
            float(data['supplier_opening_balance']),
            float(data['supplier_debit_amount']),
            float(data['supplier_credit_amount']),
            float(data['supplier_closing_balance']),
            datetime.strptime(data['statement_date'], '%Y-%m-%d').date() if data.get('statement_date') else None,
            data.get('statement_reference', ''),
            current_user.id
        ])
        
        return jsonify({
            'success': True,
            'message': 'تم تحديث بيانات كشف المورد بنجاح'
        })
        
    except Exception as e:
        logger.error(f"خطأ في تحديث بيانات كشف المورد: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@reconciliation_api_bp.route('/reconciliations/<int:reconciliation_id>/differences')
@login_required
def get_reconciliation_differences(reconciliation_id):
    """API للحصول على تفاصيل الفروقات"""
    try:
        db = DatabaseManager()
        
        query = """
        SELECT 
            difference_id, difference_type, difference_category,
            system_transaction_id, system_transaction_date, system_reference_number,
            system_amount, system_description,
            supplier_transaction_date, supplier_reference_number,
            supplier_amount, supplier_description,
            expected_amount, actual_amount, difference_amount, difference_percentage,
            difference_reason, root_cause, impact_level,
            adjustment_required, adjustment_amount, adjustment_description,
            resolved, resolution_method, resolution_notes,
            identified_date, resolved_date
        FROM RECONCILIATION_DIFFERENCES
        WHERE reconciliation_id = :1
        ORDER BY ABS(difference_amount) DESC, identified_date
        """
        
        results = db.execute_query(query, [reconciliation_id])
        
        differences = []
        for row in results:
            differences.append({
                'difference_id': row[0],
                'difference_type': row[1],
                'difference_category': row[2],
                'system_transaction': {
                    'id': row[3],
                    'date': row[4].strftime('%Y-%m-%d') if row[4] else None,
                    'reference': row[5],
                    'amount': float(row[6]) if row[6] else 0,
                    'description': row[7]
                },
                'supplier_transaction': {
                    'date': row[8].strftime('%Y-%m-%d') if row[8] else None,
                    'reference': row[9],
                    'amount': float(row[10]) if row[10] else 0,
                    'description': row[11]
                },
                'analysis': {
                    'expected_amount': float(row[12]) if row[12] else 0,
                    'actual_amount': float(row[13]) if row[13] else 0,
                    'difference_amount': float(row[14]) if row[14] else 0,
                    'difference_percentage': float(row[15]) if row[15] else 0,
                    'reason': row[16],
                    'root_cause': row[17],
                    'impact_level': row[18]
                },
                'adjustment': {
                    'required': bool(row[19] == 'Y'),
                    'amount': float(row[20]) if row[20] else 0,
                    'description': row[21]
                },
                'resolution': {
                    'resolved': bool(row[22] == 'Y'),
                    'method': row[23],
                    'notes': row[24]
                },
                'dates': {
                    'identified': row[25].strftime('%Y-%m-%d %H:%M:%S') if row[25] else None,
                    'resolved': row[26].strftime('%Y-%m-%d %H:%M:%S') if row[26] else None
                }
            })
        
        return jsonify({
            'success': True,
            'differences': differences,
            'total_count': len(differences)
        })

    except Exception as e:
        logger.error(f"خطأ في جلب تفاصيل الفروقات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@reconciliation_api_bp.route('/reconciliations/<int:reconciliation_id>/analyze', methods=['POST'])
@login_required
def analyze_reconciliation_differences(reconciliation_id):
    """API لتحليل الفروقات تلقائياً"""
    try:
        db = DatabaseManager()

        # تحليل الفروقات باستخدام الإجراء المخزن
        db.execute_procedure('ANALYZE_RECONCILIATION_DIFFERENCES', [reconciliation_id, current_user.id])

        return jsonify({
            'success': True,
            'message': 'تم تحليل الفروقات بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في تحليل الفروقات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@reconciliation_api_bp.route('/adjustments', methods=['POST'])
@login_required
def create_reconciliation_adjustment():
    """API لإنشاء طلب تعديل مطابقة"""
    try:
        db = DatabaseManager()
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        required_fields = ['reconciliation_id', 'adjustment_type', 'adjustment_amount',
                          'adjustment_currency', 'adjustment_description']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'success': False, 'message': f'الحقل {field} مطلوب'}), 400

        # إنشاء طلب التعديل باستخدام الإجراء المخزن
        adjustment_id = db.call_procedure_with_output(
            'CREATE_RECONCILIATION_ADJUSTMENT',
            [
                data['reconciliation_id'],
                data.get('difference_id'),
                data['adjustment_type'],
                float(data['adjustment_amount']),
                data['adjustment_currency'],
                data['adjustment_description'],
                data.get('adjustment_reason', ''),
                current_user.id
            ]
        )

        return jsonify({
            'success': True,
            'message': 'تم إنشاء طلب التعديل بنجاح',
            'adjustment_id': adjustment_id
        })

    except Exception as e:
        logger.error(f"خطأ في إنشاء طلب التعديل: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@reconciliation_api_bp.route('/cycles/<int:cycle_id>/complete', methods=['POST'])
@login_required
def complete_reconciliation_cycle(cycle_id):
    """API لإكمال دورة المطابقة"""
    try:
        db = DatabaseManager()

        # التحقق من إمكانية إكمال الدورة
        pending_reconciliations = db.execute_query("""
            SELECT COUNT(*) FROM SUPPLIER_RECONCILIATION
            WHERE cycle_id = :1 AND reconciliation_status = 'PENDING'
        """, [cycle_id])

        if pending_reconciliations[0][0] > 0:
            return jsonify({
                'success': False,
                'message': f'يوجد {pending_reconciliations[0][0]} مطابقة معلقة. يجب إكمال جميع المطابقات أولاً'
            }), 400

        # إكمال الدورة باستخدام الإجراء المخزن
        db.execute_procedure('COMPLETE_RECONCILIATION_CYCLE', [cycle_id, current_user.id])

        return jsonify({
            'success': True,
            'message': 'تم إكمال دورة المطابقة بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في إكمال دورة المطابقة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@reconciliation_api_bp.route('/reconciliations/<int:reconciliation_id>/approve', methods=['POST'])
@login_required
def approve_reconciliation(reconciliation_id):
    """API لاعتماد مطابقة"""
    try:
        db = DatabaseManager()
        data = request.get_json()

        # تحديث حالة المطابقة
        update_query = """
        UPDATE SUPPLIER_RECONCILIATION SET
            reconciliation_status = 'APPROVED',
            approved_by = :1,
            approved_date = CURRENT_TIMESTAMP,
            reconciliation_notes = :2,
            updated_date = CURRENT_TIMESTAMP
        WHERE reconciliation_id = :3
        """

        db.execute_update(update_query, [
            current_user.id,
            data.get('notes', ''),
            reconciliation_id
        ])

        # تسجيل النشاط
        cycle_id = db.execute_query(
            "SELECT cycle_id FROM SUPPLIER_RECONCILIATION WHERE reconciliation_id = :1",
            [reconciliation_id]
        )[0][0]

        activity_query = """
        INSERT INTO RECONCILIATION_ACTIVITY_LOG (
            cycle_id, reconciliation_id, activity_type, activity_description, performed_by
        ) VALUES (
            :1, :2, 'RECONCILIATION_APPROVED', 'تم اعتماد المطابقة', :3
        )
        """

        db.execute_update(activity_query, [cycle_id, reconciliation_id, current_user.id])

        return jsonify({
            'success': True,
            'message': 'تم اعتماد المطابقة بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في اعتماد المطابقة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@reconciliation_api_bp.route('/reports/summary')
@login_required
def get_reconciliation_summary_report():
    """API لتقرير ملخص المطابقة"""
    try:
        db = DatabaseManager()

        # معاملات التقرير
        cycle_id = request.args.get('cycle_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')

        # إحصائيات عامة
        summary_query = """
        SELECT
            COUNT(DISTINCT rc.cycle_id) as total_cycles,
            COUNT(sr.reconciliation_id) as total_reconciliations,
            SUM(CASE WHEN sr.reconciliation_status = 'MATCHED' THEN 1 ELSE 0 END) as matched_count,
            SUM(CASE WHEN sr.reconciliation_status = 'UNMATCHED' THEN 1 ELSE 0 END) as unmatched_count,
            SUM(CASE WHEN sr.reconciliation_status = 'APPROVED' THEN 1 ELSE 0 END) as approved_count,
            SUM(ABS(sr.total_difference)) as total_differences_amount,
            AVG(ABS(sr.total_difference)) as avg_difference_amount,
            MAX(ABS(sr.total_difference)) as max_difference_amount
        FROM RECONCILIATION_CYCLES rc
        LEFT JOIN SUPPLIER_RECONCILIATION sr ON rc.cycle_id = sr.cycle_id
        WHERE 1=1
        """

        params = []
        param_counter = 1

        if cycle_id:
            summary_query += f" AND rc.cycle_id = :{param_counter}"
            params.append(cycle_id)
            param_counter += 1

        if date_from:
            summary_query += f" AND rc.reconciliation_date >= TO_DATE(:{param_counter}, 'YYYY-MM-DD')"
            params.append(date_from)
            param_counter += 1

        if date_to:
            summary_query += f" AND rc.reconciliation_date <= TO_DATE(:{param_counter}, 'YYYY-MM-DD')"
            params.append(date_to)
            param_counter += 1

        summary_result = db.execute_query(summary_query, params)

        # إحصائيات حسب العملة
        currency_stats_query = """
        SELECT
            sr.currency_code,
            c.symbol,
            COUNT(sr.reconciliation_id) as reconciliations_count,
            SUM(CASE WHEN sr.reconciliation_status = 'MATCHED' THEN 1 ELSE 0 END) as matched_count,
            SUM(ABS(sr.total_difference)) as total_differences
        FROM SUPPLIER_RECONCILIATION sr
        JOIN CURRENCIES c ON sr.currency_code = c.code
        JOIN RECONCILIATION_CYCLES rc ON sr.cycle_id = rc.cycle_id
        WHERE 1=1
        """

        if cycle_id:
            currency_stats_query += f" AND rc.cycle_id = {cycle_id}"
        if date_from:
            currency_stats_query += f" AND rc.reconciliation_date >= TO_DATE('{date_from}', 'YYYY-MM-DD')"
        if date_to:
            currency_stats_query += f" AND rc.reconciliation_date <= TO_DATE('{date_to}', 'YYYY-MM-DD')"

        currency_stats_query += " GROUP BY sr.currency_code, c.symbol ORDER BY total_differences DESC"

        currency_stats = db.execute_query(currency_stats_query)

        # تجميع النتائج
        if summary_result:
            summary_data = summary_result[0]
            summary = {
                'total_cycles': int(summary_data[0]) if summary_data[0] else 0,
                'total_reconciliations': int(summary_data[1]) if summary_data[1] else 0,
                'matched_count': int(summary_data[2]) if summary_data[2] else 0,
                'unmatched_count': int(summary_data[3]) if summary_data[3] else 0,
                'approved_count': int(summary_data[4]) if summary_data[4] else 0,
                'total_differences_amount': float(summary_data[5]) if summary_data[5] else 0,
                'avg_difference_amount': float(summary_data[6]) if summary_data[6] else 0,
                'max_difference_amount': float(summary_data[7]) if summary_data[7] else 0
            }
        else:
            summary = {
                'total_cycles': 0, 'total_reconciliations': 0, 'matched_count': 0,
                'unmatched_count': 0, 'approved_count': 0, 'total_differences_amount': 0,
                'avg_difference_amount': 0, 'max_difference_amount': 0
            }

        currency_breakdown = []
        for row in currency_stats:
            currency_breakdown.append({
                'currency_code': row[0],
                'currency_symbol': row[1],
                'reconciliations_count': int(row[2]),
                'matched_count': int(row[3]),
                'total_differences': float(row[4]) if row[4] else 0
            })

        return jsonify({
            'success': True,
            'summary': summary,
            'currency_breakdown': currency_breakdown
        })

    except Exception as e:
        logger.error(f"خطأ في تقرير ملخص المطابقة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500
