{% extends "base.html" %}

{% block title %}إدارة المستفيدين{% endblock %}

{% block head %}
{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
    }

    /* تحسين بطاقات الإحصائيات الجديدة */
    .beneficiary-card {
        border-radius: 15px;
        transition: all 0.3s ease;
        border: 1px solid #e3e6f0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .beneficiary-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stats-label {
        font-size: 0.9rem;
        color: #6c757d;
        font-weight: 500;
    }

    /* تحسين شريط التقدم */
    .progress {
        border-radius: 10px;
        overflow: hidden;
        background-color: #f8f9fa;
    }

    .progress-bar {
        transition: width 0.6s ease;
    }

    .action-btn {
        margin: 2px;
        border-radius: 8px;
        transition: all 0.2s ease;
        min-width: 32px;
        height: 32px;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .btn-outline-danger:hover {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
    }

    .action-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    /* تحسين قسم البحث */
    .search-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .input-group .input-group-text {
        background: white;
        border-right: none;
    }

    .input-group .form-control {
        border-left: none;
    }

    .input-group .form-control:focus {
        box-shadow: none;
        border-color: #ced4da;
    }

    .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    #resultsCount {
        font-size: 0.9rem;
        padding: 8px 12px;
    }

    /* تأثيرات البحث الصوتي */
    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.3; }
    }

    #voiceSearchBtn {
        transition: all 0.3s ease;
        min-width: 45px;
    }

    #voiceSearchBtn:hover {
        transform: scale(1.05);
    }

    #voiceSearchBtn.btn-danger {
        animation: pulse-red 1.5s infinite;
    }

    @keyframes pulse-red {
        0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
        100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
    }

    #voiceStatus {
        background: rgba(0, 123, 255, 0.1);
        border: 1px solid rgba(0, 123, 255, 0.2);
        border-radius: 8px;
        padding: 8px 12px;
        margin-top: 8px;
    }

    #recordingIndicator {
        font-size: 0.8rem;
    }

    .header-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
    }

    /* تنسيق Breadcrumb الجميل */
    .breadcrumb {
        background: none;
        padding: 0;
        margin: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: #6c757d;
        font-weight: bold;
    }

    .breadcrumb-item a {
        color: #495057;
        transition: color 0.3s ease;
    }

    .breadcrumb-item a:hover {
        color: #007bff;
    }

    .breadcrumb-item.active {
        color: #6c757d;
        font-weight: 500;
    }

    /* تحسينات Header الرئيسي */
    .main-header-card {
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .main-header-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
    }

    .stats-icon {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .btn-gradient {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        transition: all 0.3s ease;
    }

    .btn-gradient:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    }

    /* إجبار النافذة على الظهور */
    #addBeneficiaryModal {
        z-index: 9999 !important;
    }

    #addBeneficiaryModal .modal-dialog {
        z-index: 9999 !important;
        position: relative !important;
    }

    #addBeneficiaryModal .modal-content {
        z-index: 9999 !important;
        position: relative !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    #addBeneficiaryModal .modal-header {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        z-index: 10000 !important;
        position: relative !important;
    }

    #addBeneficiaryModal .modal-title {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        color: white !important;
    }

    /* تحسين حجم الحقول */
    .form-control-lg, .form-select-lg {
        padding: 12px 16px;
        font-size: 1.1rem;
        border-radius: 8px;
        border: 2px solid #e3e6f0;
        transition: all 0.3s ease;
    }

    .form-control-lg:focus, .form-select-lg:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        transform: translateY(-1px);
    }

    /* فرض العرض 900 بكسل بقوة */
    #addBeneficiaryModal {
        --bs-modal-width: 900px !important;
    }

    #addBeneficiaryModal .modal-dialog {
        width: 900px !important;
        max-width: 900px !important;
        min-width: 900px !important;
        margin: 30px auto !important;
    }

    #addBeneficiaryModal .modal-content {
        width: 900px !important;
        min-width: 900px !important;
        max-width: 900px !important;
        height: 760px !important;
    }

    /* Class مخصص للعرض 900 */
    .modal-custom-900 {
        width: 900px !important;
        max-width: 900px !important;
        min-width: 900px !important;
    }

    /* إلغاء جميع قيود Bootstrap للعرض */
    @media (min-width: 576px) {
        .modal-custom-900 {
            max-width: 900px !important;
            width: 900px !important;
            min-width: 900px !important;
        }
    }

    @media (min-width: 992px) {
        .modal-custom-900 {
            max-width: 900px !important;
            width: 900px !important;
            min-width: 900px !important;
        }
    }

    @media (min-width: 1200px) {
        .modal-custom-900 {
            max-width: 900px !important;
            width: 900px !important;
            min-width: 900px !important;
        }
    }

    /* تحسين scroll bar */
    #addBeneficiaryModal .modal-body::-webkit-scrollbar {
        width: 8px;
    }

    #addBeneficiaryModal .modal-body::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    #addBeneficiaryModal .modal-body::-webkit-scrollbar-thumb {
        background: #667eea;
        border-radius: 4px;
    }

    #addBeneficiaryModal .modal-body::-webkit-scrollbar-thumb:hover {
        background: #5a6fd8;
    }

    .modal-icon {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .modal-header {
        padding: 25px 30px;
        border-radius: 0.5rem 0.5rem 0 0;
    }

    .modal-title {
        font-weight: 600;
        font-size: 1.5rem;
    }

    .modal-content {
        border-radius: 15px;
        overflow: hidden;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header المتقدم - نسخة من الصرافين -->
    <div class="row mb-4">
        <div class="col-12">
            <!-- Header الرئيسي -->
            <div class="card border-0 shadow-sm mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body text-white">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center mb-2">
                                <div class="me-3">
                                    <i class="fas fa-users fa-3x opacity-75"></i>
                                </div>
                                <div>
                                    <h1 class="h2 mb-1 fw-bold">إدارة المستفيدين</h1>
                                    <p class="mb-0 opacity-90">إدارة شاملة لقائمة المستفيدين من التحويلات المالية</p>
                                </div>
                            </div>

                            <!-- إحصائيات سريعة في Header -->
                            <div class="row mt-3">
                                <div class="col-md-3 col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-chart-bar me-2 fa-lg"></i>
                                        <div>
                                            <div class="h5 mb-0 fw-bold">{{ beneficiaries|length }}</div>
                                            <small class="opacity-75">إجمالي</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-check-circle me-2 fa-lg text-success"></i>
                                        <div>
                                            <div class="h5 mb-0 fw-bold">{{ beneficiaries|selectattr('is_active')|list|length }}</div>
                                            <small class="opacity-75">نشط</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user me-2 fa-lg text-info"></i>
                                        <div>
                                            <div class="h5 mb-0 fw-bold">{{ beneficiaries|selectattr('type', 'equalto', 'individual')|list|length }}</div>
                                            <small class="opacity-75">أفراد</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-building me-2 fa-lg text-warning"></i>
                                        <div>
                                            <div class="h5 mb-0 fw-bold">{{ beneficiaries|selectattr('type', 'equalto', 'company')|list|length }}</div>
                                            <small class="opacity-75">شركات</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 text-end">
                            <div class="d-grid gap-2">
                                <button class="btn btn-light btn-lg shadow-sm" data-bs-toggle="modal" data-bs-target="#addBeneficiaryModal" onclick="
                                    window.currentEditId = null;
                                    document.getElementById('modalTitle').innerHTML = '<i class=&quot;fas fa-plus me-2&quot;></i>إضافة مستفيد جديد';
                                    document.getElementById('saveButtonText').textContent = 'حفظ';
                                    document.getElementById('beneficiaryForm').reset();
                                ">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة مستفيد جديد
                                </button>
                                <button class="btn btn-outline-light">
                                    <i class="fas fa-download me-2"></i>
                                    تصدير البيانات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- شريط التنقل السريع (Breadcrumb) -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body py-2">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('main.dashboard') }}" class="text-decoration-none">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('transfers.dashboard') }}" class="text-decoration-none">
                            <i class="fas fa-money-bill-transfer me-1"></i>
                            نظام الحوالات
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <i class="fas fa-users me-1"></i>
                        إدارة المستفيدين
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- إحصائيات تفصيلية - نسخة من الصرافين -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card beneficiary-card text-center h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                            <i class="fas fa-chart-bar text-primary fa-2x"></i>
                        </div>
                    </div>
                    <div class="stats-number text-primary" id="totalCount">{{ beneficiaries|length }}</div>
                    <div class="stats-label">إجمالي المستفيدين</div>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar bg-primary" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card beneficiary-card text-center h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="rounded-circle bg-success bg-opacity-10 p-3">
                            <i class="fas fa-check-circle text-success fa-2x"></i>
                        </div>
                    </div>
                    <div class="stats-number text-success" id="activeCount">
                        {{ beneficiaries|selectattr('is_active')|list|length }}
                    </div>
                    <div class="stats-label">نشط</div>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar bg-success" style="width: 80%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card beneficiary-card text-center h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="rounded-circle bg-info bg-opacity-10 p-3">
                            <i class="fas fa-user text-info fa-2x"></i>
                        </div>
                    </div>
                    <div class="stats-number text-info" id="individualCount">
                        {{ beneficiaries|selectattr('type', 'equalto', 'individual')|list|length }}
                    </div>
                    <div class="stats-label">أفراد</div>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar bg-info" style="width: 60%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card beneficiary-card text-center h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="rounded-circle bg-warning bg-opacity-10 p-3">
                            <i class="fas fa-building text-warning fa-2x"></i>
                        </div>
                    </div>
                    <div class="stats-number text-warning" id="companyCount">
                        {{ beneficiaries|selectattr('type', 'equalto', 'company')|list|length }}
                    </div>
                    <div class="stats-label">شركات</div>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar bg-warning" style="width: 40%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم البحث والتصفية -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-search me-2"></i>
                البحث والتصفية
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- البحث النصي -->
                <div class="col-lg-4 col-md-6 mb-3">
                    <label class="form-label fw-bold">البحث السريع</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="searchInput" placeholder="ابحث في الاسم، البنك، رقم الحساب... أو استخدم البحث الصوتي">
                        <button class="btn btn-outline-primary" type="button" id="voiceSearchBtn" onclick="toggleVoiceSearch()" title="البحث الصوتي">
                            <i class="fas fa-microphone" id="voiceIcon"></i>
                        </button>
                        <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div id="voiceStatus" class="mt-2" style="display: none;">
                        <small class="text-muted">
                            <i class="fas fa-circle text-danger me-1" id="recordingIndicator"></i>
                            <span id="voiceStatusText">جاري الاستماع...</span>
                        </small>
                    </div>
                </div>

                <!-- تصفية النوع -->
                <div class="col-lg-2 col-md-3 mb-3">
                    <label class="form-label fw-bold">النوع</label>
                    <select class="form-select" id="typeFilter">
                        <option value="">جميع الأنواع</option>
                        <option value="individual">أفراد</option>
                        <option value="company">شركات</option>
                    </select>
                </div>

                <!-- تصفية الحالة -->
                <div class="col-lg-2 col-md-3 mb-3">
                    <label class="form-label fw-bold">الحالة</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>

                <!-- تصفية البلد -->
                <div class="col-lg-2 col-md-6 mb-3">
                    <label class="form-label fw-bold">البلد</label>
                    <select class="form-select" id="countryFilter">
                        <option value="">جميع البلدان</option>
                        {% set countries = beneficiaries|map(attribute='bank_country')|unique|list %}
                        {% for country in countries %}
                            {% if country %}
                                <option value="{{ country }}">{{ country }}</option>
                            {% endif %}
                        {% endfor %}
                    </select>
                </div>

                <!-- تصفية البنك -->
                <div class="col-lg-2 col-md-6 mb-3">
                    <label class="form-label fw-bold">البنك</label>
                    <select class="form-select" id="bankFilter">
                        <option value="">جميع البنوك</option>
                        {% set banks = beneficiaries|map(attribute='bank_name')|unique|list %}
                        {% for bank in banks %}
                            {% if bank %}
                                <option value="{{ bank }}">{{ bank[:30] }}{% if bank|length > 30 %}...{% endif %}</option>
                            {% endif %}
                        {% endfor %}
                    </select>
                </div>
            </div>

            <!-- أزرار إضافية -->
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <button class="btn btn-outline-primary btn-sm" onclick="resetFilters()">
                                <i class="fas fa-undo me-1"></i>
                                إعادة تعيين
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="exportFiltered()">
                                <i class="fas fa-download me-1"></i>
                                تصدير النتائج
                            </button>
                        </div>
                        <div>
                            <span class="badge bg-info" id="resultsCount">
                                {{ beneficiaries|length }} مستفيد
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة المستفيدين -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                قائمة المستفيدين
            </h5>
        </div>
        <div class="card-body p-0">
            {% if beneficiaries %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>النوع</th>
                            <th>الهاتف</th>
                            <th>البنك</th>
                            <th>رقم الحساب</th>
                            <th>عدد التحويلات</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for beneficiary in beneficiaries %}
                        <tr data-country="{{ beneficiary.bank_country or '' }}"
                            data-bank="{{ beneficiary.bank_name or '' }}"
                            data-type="{{ beneficiary.type or '' }}">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                        <i class="fas fa-{{ 'building' if beneficiary.type == 'company' else 'user' }}"></i>
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ beneficiary.beneficiary_name }}</div>
                                        <small class="text-muted">{{ beneficiary.beneficiary_address[:50] }}...</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{ 'info' if beneficiary.type == 'individual' else 'warning' }}">
                                    {{ 'فرد' if beneficiary.type == 'individual' else 'شركة' }}
                                </span>
                            </td>
                            <td>{{ beneficiary.phone or '-' }}</td>
                            <td>{{ beneficiary.bank_name or '-' }}</td>
                            <td>{{ beneficiary.bank_account or '-' }}</td>
                            <td>
                                <span class="badge bg-secondary">{{ beneficiary.transfer_count }}</span>
                            </td>
                            <td>
                                <span class="badge bg-{{ 'success' if beneficiary.is_active else 'danger' }}">
                                    {{ 'نشط' if beneficiary.is_active else 'غير نشط' }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-warning action-btn"
                                            data-bs-toggle="modal" data-bs-target="#addBeneficiaryModal"
                                            onclick="editBeneficiary({{ beneficiary.id }})"
                                            title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-{{ 'danger' if beneficiary.is_active else 'success' }} action-btn"
                                            onclick="toggleBeneficiaryStatus({{ beneficiary.id }}, {{ beneficiary.is_active|int }})"
                                            title="{{ 'إلغاء التفعيل' if beneficiary.is_active else 'تفعيل' }}">
                                        <i class="fas fa-{{ 'ban' if beneficiary.is_active else 'check' }}"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger action-btn"
                                            onclick="deleteBeneficiary({{ beneficiary.id }}, '{{ beneficiary.beneficiary_name }}')"
                                            title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h4>لا يوجد مستفيدين</h4>
                <p class="text-muted">ابدأ بإضافة أول مستفيد للنظام</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBeneficiaryModal" onclick="
                    window.currentEditId = null;
                    document.getElementById('modalTitle').innerHTML = '<i class=&quot;fas fa-plus me-2&quot;></i>إضافة مستفيد جديد';
                    document.getElementById('saveButtonText').textContent = 'حفظ';
                    document.getElementById('beneficiaryForm').reset();
                ">
                    <i class="fas fa-plus me-2"></i>
                    إضافة الآن
                </button>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- نافذة المستفيدين - 900×760 بكسل -->
<div class="modal fade" id="addBeneficiaryModal" tabindex="-1">
    <div class="modal-dialog modal-custom-900" style="width: 900px !important; max-width: 900px !important; min-width: 900px !important; margin: 30px auto !important;">
        <div class="modal-content" style="width: 900px !important; height: 760px !important; min-width: 900px !important; max-width: 900px !important;">
            <!-- Header مضمون 100% -->
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 20px; position: relative; z-index: 1060;">
                <h5 class="modal-title" id="modalTitle" style="color: white !important; font-weight: bold; font-size: 1.3rem; margin: 0; display: block;">
                    <i class="fas fa-plus me-2"></i>
                    إضافة مستفيد جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" style="filter: invert(1); position: relative; z-index: 1061;"></button>
            </div>

            <!-- Body -->
            <div class="modal-body" style="height: 600px; overflow-y: auto; padding: 20px;">
                <form id="beneficiaryForm">
                    <input type="hidden" id="beneficiaryId">

                    <!-- المعلومات الأساسية -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-user me-2"></i>المعلومات الأساسية</h6>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <label class="form-label fw-bold">اسم المستفيد *</label>
                                    <input type="text" class="form-control" id="beneficiaryName" required placeholder="أدخل اسم المستفيد الكامل">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label fw-bold">نوع المستفيد *</label>
                                    <select class="form-select" id="beneficiaryType" required>
                                        <option value="">اختر النوع</option>
                                        <option value="individual">فرد</option>
                                        <option value="company">شركة</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label class="form-label fw-bold">رقم الهاتف <small class="text-muted">(اختياري)</small></label>
                                    <input type="tel" class="form-control" id="phone" placeholder="+967 xxx xxx xxx">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label fw-bold">البريد الإلكتروني <small class="text-muted">(اختياري)</small></label>
                                    <input type="email" class="form-control" id="email" placeholder="<EMAIL>">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label fw-bold">البلد *</label>
                                    <input type="text" class="form-control" id="bankCountry" placeholder="بلد المستفيد" required>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label class="form-label fw-bold">رقم الهوية <small class="text-muted">(اختياري)</small></label>
                                    <input type="text" class="form-control" id="identificationNumber" placeholder="رقم الهوية أو الجواز">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <label class="form-label fw-bold">العنوان التفصيلي *</label>
                                    <textarea class="form-control form-control-lg" id="beneficiaryAddress" rows="3" placeholder="أدخل العنوان التفصيلي للمستفيد (الشارع، المدينة، المنطقة...)" required></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات البنك -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-university me-2"></i>معلومات البنك</h6>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-lg-6">
                                    <label class="form-label fw-bold">اسم البنك *</label>
                                    <input type="text" class="form-control form-control-lg" id="bankName" placeholder="أدخل اسم البنك الكامل" required>
                                </div>
                                <div class="col-lg-6">
                                    <label class="form-label fw-bold">فرع البنك *</label>
                                    <input type="text" class="form-control form-control-lg" id="bankBranch" placeholder="اسم الفرع أو المنطقة" required>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-lg-4">
                                    <label class="form-label fw-bold">رقم الحساب *</label>
                                    <input type="text" class="form-control form-control-lg" id="bankAccount" placeholder="رقم الحساب البنكي" required>
                                    <small class="form-text text-muted">يمكن لعدة مستفيدين مشاركة نفس رقم الحساب</small>
                                </div>
                                <div class="col-lg-4">
                                    <label class="form-label fw-bold">IBAN *</label>
                                    <input type="text" class="form-control form-control-lg" id="iban" placeholder="رقم IBAN الدولي" required>
                                </div>
                                <div class="col-lg-4">
                                    <label class="form-label fw-bold">Swift Code *</label>
                                    <input type="text" class="form-control form-control-lg" id="swiftCode" placeholder="رمز Swift للبنك" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الإعدادات -->
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-cog me-2"></i>الإعدادات</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="isActive" checked>
                                <label class="form-check-label fw-bold" for="isActive">
                                    <i class="fas fa-toggle-on me-1"></i>
                                    حساب نشط
                                </label>
                                <small class="form-text text-muted d-block">يمكن للمستفيد استقبال التحويلات</small>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Footer -->
            <div class="modal-footer" style="padding: 15px 25px; border-top: 1px solid #dee2e6;">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>إلغاء
                </button>
                <button type="button" class="btn btn-success" onclick="saveBeneficiary()">
                    <i class="fas fa-save me-2"></i>
                    <span id="saveButtonText">حفظ</span>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// البحث والتصفية المتقدم
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const typeFilter = document.getElementById('typeFilter');
    const statusFilter = document.getElementById('statusFilter');
    const countryFilter = document.getElementById('countryFilter');
    const bankFilter = document.getElementById('bankFilter');
    const table = document.querySelector('table tbody');
    const resultsCount = document.getElementById('resultsCount');

    if (searchInput && table) {
        function filterTable() {
            const searchTerm = searchInput.value.toLowerCase();
            const typeValue = typeFilter ? typeFilter.value : '';
            const statusValue = statusFilter ? statusFilter.value : '';
            const countryValue = countryFilter ? countryFilter.value : '';
            const bankValue = bankFilter ? bankFilter.value : '';

            const rows = table.querySelectorAll('tr');
            let visibleCount = 0;

            rows.forEach(row => {
                const cells = row.cells;
                if (cells.length < 7) return;

                // استخراج النصوص
                const name = cells[0].textContent.toLowerCase();
                const type = cells[1].textContent;
                const phone = cells[2].textContent.toLowerCase();
                const bank = cells[3].textContent.toLowerCase();
                const account = cells[4].textContent.toLowerCase();
                const status = cells[6].textContent;

                // فحص البحث النصي (في الاسم، البنك، رقم الحساب، الهاتف)
                const matchesSearch = !searchTerm ||
                    name.includes(searchTerm) ||
                    bank.includes(searchTerm) ||
                    account.includes(searchTerm) ||
                    phone.includes(searchTerm);

                // فحص النوع
                const matchesType = !typeValue ||
                    (typeValue === 'individual' && type.includes('فرد')) ||
                    (typeValue === 'company' && type.includes('شركة'));

                // فحص الحالة
                const matchesStatus = !statusValue ||
                    (statusValue === 'active' && status.includes('نشط')) ||
                    (statusValue === 'inactive' && status.includes('غير نشط'));

                // فحص البلد (من البيانات المخفية)
                const countryData = row.getAttribute('data-country') || '';
                const matchesCountry = !countryValue || countryData.includes(countryValue);

                // فحص البنك
                const matchesBank = !bankValue || bank.includes(bankValue.toLowerCase());

                // إظهار/إخفاء الصف
                const isVisible = matchesSearch && matchesType && matchesStatus && matchesCountry && matchesBank;
                row.style.display = isVisible ? '' : 'none';

                if (isVisible) visibleCount++;
            });

            // تحديث عداد النتائج
            if (resultsCount) {
                resultsCount.textContent = visibleCount + ' مستفيد';
                resultsCount.className = visibleCount > 0 ? 'badge bg-info' : 'badge bg-warning';
            }
        }

        // ربط الأحداث
        searchInput.addEventListener('input', filterTable);
        if (typeFilter) typeFilter.addEventListener('change', filterTable);
        if (statusFilter) statusFilter.addEventListener('change', filterTable);
        if (countryFilter) countryFilter.addEventListener('change', filterTable);
        if (bankFilter) bankFilter.addEventListener('change', filterTable);
    }
});

// دوال إضافية للتصفية
function clearSearch() {
    document.getElementById('searchInput').value = '';
    filterTable();
}

function resetFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('typeFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('countryFilter').value = '';
    document.getElementById('bankFilter').value = '';

    // إعادة تشغيل التصفية
    const event = new Event('input');
    document.getElementById('searchInput').dispatchEvent(event);
}

function exportFiltered() {
    // جمع الصفوف المرئية
    const visibleRows = Array.from(document.querySelectorAll('table tbody tr'))
        .filter(row => row.style.display !== 'none');

    if (visibleRows.length === 0) {
        alert('لا توجد نتائج للتصدير');
        return;
    }

    // إنشاء CSV
    let csv = 'الاسم,النوع,الهاتف,البنك,رقم الحساب,الحالة\\n';

    visibleRows.forEach(row => {
        const cells = row.cells;
        const name = cells[0].textContent.trim().replace(/\\s+/g, ' ');
        const type = cells[1].textContent.trim();
        const phone = cells[2].textContent.trim();
        const bank = cells[3].textContent.trim();
        const account = cells[4].textContent.trim();
        const status = cells[6].textContent.trim();

        csv += `"${name}","${type}","${phone}","${bank}","${account}","${status}"\\n`;
    });

    // تحميل الملف
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'المستفيدين_المصفاة_' + new Date().toISOString().split('T')[0] + '.csv';
    link.click();
}

// دوال إدارة المستفيدين
function editBeneficiary(id) {
    console.log('تعديل مستفيد:', id);

    fetch('/transfers/beneficiaries/' + id + '/details')
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            const data = result.data;

            // ملء النموذج
            document.getElementById('beneficiaryId').value = data.id;
            document.getElementById('beneficiaryName').value = data.beneficiary_name || '';
            document.getElementById('beneficiaryAddress').value = data.beneficiary_address || '';
            document.getElementById('beneficiaryType').value = data.type || '';
            document.getElementById('bankAccount').value = data.bank_account || '';
            document.getElementById('bankName').value = data.bank_name || '';
            document.getElementById('bankBranch').value = data.bank_branch || '';
            document.getElementById('bankCountry').value = data.bank_country || '';
            document.getElementById('iban').value = data.iban || '';
            document.getElementById('swiftCode').value = data.swift_code || '';
            document.getElementById('identificationNumber').value = data.identification_number || '';
            document.getElementById('phone').value = data.phone || '';
            document.getElementById('email').value = data.email || '';
            document.getElementById('isActive').checked = data.is_active;

            // تحديث عنوان المودال
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>تعديل ' + data.beneficiary_name;
            document.getElementById('saveButtonText').textContent = 'تحديث البيانات';

            // تعيين متغير التعديل
            window.currentEditId = data.id;

            // فتح المودال
            const modal = new bootstrap.Modal(document.getElementById('addBeneficiaryModal'));
            modal.show();
        } else {
            alert('خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في جلب البيانات');
    });
}

function toggleBeneficiaryStatus(id, currentStatus) {
    const action = currentStatus ? 'إلغاء تفعيل' : 'تفعيل';

    if (confirm('هل أنت متأكد من ' + action + ' هذا المستفيد؟')) {
        fetch('/transfers/beneficiaries/' + id + '/toggle-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert(result.message);
                location.reload();
            } else {
                alert('خطأ: ' + result.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تغيير الحالة');
        });
    }
}

function deleteBeneficiary(id, name) {
    // تأكيد مزدوج للحذف
    if (confirm('⚠️ تحذير: هل أنت متأكد من حذف المستفيد؟\n\n' +
                '📝 اسم المستفيد: ' + name + '\n' +
                '🔢 رقم المستفيد: ' + id + '\n\n' +
                '❌ هذا الإجراء لا يمكن التراجع عنه!')) {

        if (confirm('🚨 تأكيد نهائي: اضغط موافق لحذف المستفيد نهائياً')) {

            // إظهار مؤشر التحميل
            const deleteButtons = document.querySelectorAll(`button[onclick*="deleteBeneficiary(${id}"]`);
            deleteButtons.forEach(btn => {
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                btn.disabled = true;
            });

            fetch('/transfers/beneficiaries/' + id + '/delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    alert('✅ ' + result.message);
                    // إخفاء الصف بتأثير بصري
                    const row = document.querySelector(`button[onclick*="deleteBeneficiary(${id}"]`).closest('tr');
                    if (row) {
                        row.style.transition = 'all 0.5s ease';
                        row.style.opacity = '0';
                        row.style.transform = 'translateX(-100%)';
                        setTimeout(() => location.reload(), 500);
                    } else {
                        location.reload();
                    }
                } else {
                    alert('❌ خطأ: ' + result.message);
                    // إعادة تعيين الأزرار
                    deleteButtons.forEach(btn => {
                        btn.innerHTML = '<i class="fas fa-trash"></i>';
                        btn.disabled = false;
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('❌ حدث خطأ في الحذف: ' + error.message);
                // إعادة تعيين الأزرار
                deleteButtons.forEach(btn => {
                    btn.innerHTML = '<i class="fas fa-trash"></i>';
                    btn.disabled = false;
                });
            });
        }
    }
}

// دالة الحفظ
function saveBeneficiary() {
    const data = {
        beneficiary_name: document.getElementById('beneficiaryName').value,
        beneficiary_address: document.getElementById('beneficiaryAddress').value,
        type: document.getElementById('beneficiaryType').value,
        phone: document.getElementById('phone').value,
        email: document.getElementById('email').value,
        identification_number: document.getElementById('identificationNumber').value,
        bank_name: document.getElementById('bankName').value,
        bank_branch: document.getElementById('bankBranch').value,
        bank_account: document.getElementById('bankAccount').value,
        bank_country: document.getElementById('bankCountry').value,
        iban: document.getElementById('iban').value,
        swift_code: document.getElementById('swiftCode').value,
        is_active: document.getElementById('isActive').checked
    };

    // التحقق من البيانات المطلوبة
    if (!data.beneficiary_name || !data.type) {
        alert('الرجاء ملء الحقول المطلوبة');
        return;
    }

    const url = window.currentEditId ?
        '/transfers/beneficiaries/' + window.currentEditId + '/edit' :
        '/transfers/beneficiaries/add';

    // إظهار مؤشر التحميل
    const saveBtn = document.querySelector('#addBeneficiaryModal .btn-success');
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    saveBtn.disabled = true;

    fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert(result.message);
            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('addBeneficiaryModal'));
            modal.hide();
            // إعادة تحميل الصفحة
            setTimeout(() => location.reload(), 500);
        } else {
            alert('خطأ: ' + result.message);
        }
    })
    .catch(error => {
        alert('حدث خطأ في الاتصال');
        console.error(error);
    })
    .finally(() => {
        // إعادة تعيين الزر
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    });
}

// دالة التعديل مع جلب البيانات
function editBeneficiary(id) {
    console.log('🔧 بدء تعديل مستفيد:', id);

    // تحديث عنوان النافذة
    document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>تعديل مستفيد';
    document.getElementById('saveButtonText').textContent = 'تحديث';

    // تعيين ID للتعديل
    window.currentEditId = id;

    // جلب بيانات المستفيد
    const url = '/transfers/beneficiaries/' + id + '/details';
    console.log('📡 جلب البيانات من:', url);

    fetch(url)
    .then(response => {
        console.log('📥 استجابة الخادم:', response.status);
        return response.json();
    })
    .then(result => {
        console.log('📋 البيانات المستلمة:', result);

        if (result.success) {
            const data = result.data;
            console.log('✅ بيانات المستفيد:', data);

            // انتظار قصير للتأكد من تحميل النافذة
            setTimeout(() => {
                console.log('🔍 فحص الحقول بعد التأخير...');

            // فحص وجود الحقول
            const fields = [
                'beneficiaryId', 'beneficiaryName', 'beneficiaryAddress', 'beneficiaryType',
                'bankAccount', 'bankName', 'bankBranch', 'bankCountry',
                'iban', 'swiftCode', 'identificationNumber', 'phone', 'email', 'isActive'
            ];

            fields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (!element) {
                    console.error('❌ الحقل غير موجود:', fieldId);
                } else {
                    console.log('✅ الحقل موجود:', fieldId);
                }
            });

            // ملء النموذج
            const beneficiaryId = document.getElementById('beneficiaryId');
            if (beneficiaryId) {
                beneficiaryId.value = data.id || '';
                console.log('✅ تم ملء ID:', data.id);
            } else {
                console.error('❌ حقل beneficiaryId غير موجود');
            }

            const beneficiaryName = document.getElementById('beneficiaryName');
            if (beneficiaryName) {
                beneficiaryName.value = data.beneficiary_name || '';
                console.log('✅ تم ملء الاسم:', data.beneficiary_name);
            } else {
                console.error('❌ حقل beneficiaryName غير موجود');
            }

            const beneficiaryAddress = document.getElementById('beneficiaryAddress');
            if (beneficiaryAddress) {
                beneficiaryAddress.value = data.beneficiary_address || '';
                console.log('✅ تم ملء العنوان');
            } else {
                console.error('❌ حقل beneficiaryAddress غير موجود');
            }

            const beneficiaryType = document.getElementById('beneficiaryType');
            if (beneficiaryType) {
                // تحويل قيم النوع من قاعدة البيانات إلى قيم النموذج
                let typeValue = data.type || '';

                // تحويل القيم القديمة إلى الجديدة
                if (typeValue === 'supplier' || typeValue === 'vendor') {
                    typeValue = 'company';
                } else if (typeValue === 'individual') {
                    typeValue = 'individual';
                }

                beneficiaryType.value = typeValue;
                console.log('✅ تم ملء النوع:', data.type, '→', typeValue);
            } else {
                console.error('❌ حقل beneficiaryType غير موجود');
            }
            if (document.getElementById('bankAccount')) {
                document.getElementById('bankAccount').value = data.bank_account || '';
            }
            if (document.getElementById('bankName')) {
                document.getElementById('bankName').value = data.bank_name || '';
            }
            if (document.getElementById('bankBranch')) {
                document.getElementById('bankBranch').value = data.bank_branch || '';
            }
            if (document.getElementById('bankCountry')) {
                document.getElementById('bankCountry').value = data.bank_country || '';
            }
            if (document.getElementById('iban')) {
                document.getElementById('iban').value = data.iban || '';
            }
            if (document.getElementById('swiftCode')) {
                document.getElementById('swiftCode').value = data.swift_code || '';
            }
            if (document.getElementById('identificationNumber')) {
                document.getElementById('identificationNumber').value = data.identification_number || '';
            }
            if (document.getElementById('phone')) {
                document.getElementById('phone').value = data.phone || '';
            }
            if (document.getElementById('email')) {
                document.getElementById('email').value = data.email || '';
            }
            // باقي الحقول
            const bankAccount = document.getElementById('bankAccount');
            if (bankAccount) {
                bankAccount.value = data.bank_account || '';
                console.log('✅ تم ملء رقم الحساب');
            }

            const bankName = document.getElementById('bankName');
            if (bankName) {
                bankName.value = data.bank_name || '';
                console.log('✅ تم ملء اسم البنك');
            }

            const bankBranch = document.getElementById('bankBranch');
            if (bankBranch) {
                bankBranch.value = data.bank_branch || '';
            }

            const bankCountry = document.getElementById('bankCountry');
            if (bankCountry) {
                bankCountry.value = data.bank_country || '';
            }

            const iban = document.getElementById('iban');
            if (iban) {
                iban.value = data.iban || '';
            }

            const swiftCode = document.getElementById('swiftCode');
            if (swiftCode) {
                swiftCode.value = data.swift_code || '';
            }

            const identificationNumber = document.getElementById('identificationNumber');
            if (identificationNumber) {
                identificationNumber.value = data.identification_number || '';
            }

            const phone = document.getElementById('phone');
            if (phone) {
                phone.value = data.phone || '';
            }

            const email = document.getElementById('email');
            if (email) {
                email.value = data.email || '';
            }

            const isActive = document.getElementById('isActive');
            if (isActive) {
                isActive.checked = data.is_active || false;
                console.log('✅ تم ملء الحالة:', data.is_active);
            }

            console.log('✅ تم تحميل جميع البيانات بنجاح');

            }, 100); // إغلاق setTimeout
        } else {
            console.error('❌ خطأ من الخادم:', result.message);
            alert('خطأ في جلب البيانات: ' + result.message);
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الشبكة:', error);
        alert('حدث خطأ في جلب البيانات: ' + error.message);
    });
}

function saveBeneficiary() {
    // تحويل نوع المستفيد للتوافق مع قاعدة البيانات
    let typeValue = document.getElementById('beneficiaryType').value;

    // تحويل القيم من النموذج إلى قاعدة البيانات
    if (typeValue === 'company') {
        typeValue = 'supplier'; // تحويل شركة إلى supplier
    } else if (typeValue === 'individual') {
        typeValue = 'individual'; // يبقى كما هو
    }

    console.log('📤 إرسال النوع:', document.getElementById('beneficiaryType').value, '→', typeValue);

    const data = {
        beneficiary_name: document.getElementById('beneficiaryName').value,
        beneficiary_address: document.getElementById('beneficiaryAddress').value,
        type: typeValue,
        phone: document.getElementById('phone').value,
        email: document.getElementById('email').value,
        identification_number: document.getElementById('identificationNumber').value,
        bank_name: document.getElementById('bankName').value,
        bank_branch: document.getElementById('bankBranch').value,
        bank_account: document.getElementById('bankAccount').value,
        bank_country: document.getElementById('bankCountry').value,
        iban: document.getElementById('iban').value,
        swift_code: document.getElementById('swiftCode').value,
        is_active: document.getElementById('isActive').checked
    };

    if (!data.beneficiary_name || !data.type) {
        alert('الرجاء ملء الحقول المطلوبة');
        return;
    }

    const url = window.currentEditId ?
        '/transfers/beneficiaries/' + window.currentEditId + '/edit' :
        '/transfers/beneficiaries/add';

    fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert(result.message);
            location.reload();
        } else {
            alert('خطأ: ' + result.message);
        }
    })
    .catch(error => {
        alert('حدث خطأ');
        console.error(error);
    });
}

// متغير عام
window.currentEditId = null;

// البحث الصوتي
let recognition = null;
let isListening = false;

// دالة إصلاح الأرقام في النص الصوتي
function fixNumbers(text) {
    if (!text) return '';

    // إصلاح الأرقام المتباعدة (مثل: "1 2 3 4" → "1234")
    // البحث عن سلاسل من الأرقام المفصولة بمسافات
    text = text.replace(/(\d)\s+(\d)/g, '$1$2');

    // إصلاح الأرقام المتباعدة بشكل متكرر (للحالات الطويلة)
    for (let i = 0; i < 5; i++) {
        text = text.replace(/(\d)\s+(\d)/g, '$1$2');
    }

    // تحويل الأرقام العربية والإنجليزية المكتوبة إلى أرقام
    const spokenNumbers = {
        // العربية
        'صفر': '0', 'واحد': '1', 'اثنان': '2', 'اثنين': '2', 'ثلاثة': '3',
        'أربعة': '4', 'خمسة': '5', 'ستة': '6', 'سبعة': '7', 'ثمانية': '8',
        'تسعة': '9', 'عشرة': '10',
        // الإنجليزية
        'zero': '0', 'one': '1', 'two': '2', 'three': '3', 'four': '4',
        'five': '5', 'six': '6', 'seven': '7', 'eight': '8', 'nine': '9', 'ten': '10'
    };

    Object.keys(spokenNumbers).forEach(word => {
        const regex = new RegExp('\\b' + word + '\\b', 'gi');
        text = text.replace(regex, spokenNumbers[word]);
    });

    // معالجة خاصة للأرقام الطويلة المنطوقة
    // مثل: "واحد اثنان ثلاثة أربعة" → "1234"
    const digitPattern = /\b(صفر|واحد|اثنان|اثنين|ثلاثة|أربعة|خمسة|ستة|سبعة|ثمانية|تسعة|zero|one|two|three|four|five|six|seven|eight|nine)\b/gi;

    if (digitPattern.test(text)) {
        // إذا كان النص يحتوي على أرقام منطوقة متتالية، نحولها لرقم واحد
        let numbers = text.match(digitPattern);
        if (numbers && numbers.length > 1) {
            let numberString = '';
            numbers.forEach(num => {
                numberString += spokenNumbers[num.toLowerCase()] || num;
            });

            // استبدال الأرقام المنطوقة بالرقم المدمج
            let tempText = text;
            numbers.forEach(num => {
                tempText = tempText.replace(new RegExp('\\b' + num + '\\b', 'i'), '');
            });
            text = tempText.trim() + ' ' + numberString;
        }
    }

    // إصلاح الأرقام بعد التحويل مرة أخرى
    text = text.replace(/(\d)\s+(\d)/g, '$1$2');

    // معالجة حالات خاصة للأرقام
    // إزالة المسافات حول الأرقام في السياق
    text = text.replace(/\s*(\d+)\s*/g, ' $1 ').trim();

    // إصلاح المسافات المتعددة
    text = text.replace(/\s+/g, ' ');

    return text;
}

// تهيئة البحث الصوتي
function initVoiceSearch() {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        recognition = new SpeechRecognition();

        // دعم اللغات المتعددة
        recognition.lang = 'ar-SA'; // العربية السعودية كافتراضي

        // يمكن تغيير اللغة حسب المحتوى
        const userLang = navigator.language || navigator.userLanguage;
        if (userLang.startsWith('en')) {
            recognition.lang = 'en-US';
        } else if (userLang.startsWith('ar')) {
            recognition.lang = 'ar-SA';
        }
        recognition.continuous = false;
        recognition.interimResults = false;
        recognition.maxAlternatives = 1;

        recognition.onstart = function() {
            console.log('🎤 بدء الاستماع...');
            isListening = true;
            updateVoiceUI(true);
        };

        recognition.onresult = function(event) {
            let transcript = event.results[0][0].transcript;
            console.log('🎤 النص المسموع:', transcript);

            // إزالة النقطة من النهاية فقط
            transcript = transcript.replace(/\.$/, '').trim();

            // إصلاح الأرقام المتباعدة
            transcript = fixNumbers(transcript);
            console.log('🎤 النص بعد إصلاح الأرقام:', transcript);

            // وضع النص في حقل البحث
            document.getElementById('searchInput').value = transcript;

            // تشغيل البحث
            const searchEvent = new Event('input');
            document.getElementById('searchInput').dispatchEvent(searchEvent);

            // إظهار رسالة نجاح
            showVoiceMessage('تم البحث عن: ' + transcript, 'success');
        };

        recognition.onerror = function(event) {
            console.error('❌ خطأ في البحث الصوتي:', event.error);
            let errorMessage = 'حدث خطأ في البحث الصوتي';

            switch(event.error) {
                case 'no-speech':
                    errorMessage = 'لم يتم سماع أي صوت. حاول مرة أخرى.';
                    break;
                case 'audio-capture':
                    errorMessage = 'لا يمكن الوصول للميكروفون. تحقق من الإعدادات.';
                    break;
                case 'not-allowed':
                    errorMessage = 'تم رفض الإذن للوصول للميكروفون.';
                    break;
                case 'network':
                    errorMessage = 'خطأ في الشبكة. تحقق من الاتصال.';
                    break;
            }

            showVoiceMessage(errorMessage, 'error');
        };

        recognition.onend = function() {
            console.log('🎤 انتهاء الاستماع');
            isListening = false;
            updateVoiceUI(false);
        };

        return true;
    } else {
        console.warn('❌ البحث الصوتي غير مدعوم في هذا المتصفح');
        return false;
    }
}

// تبديل البحث الصوتي
function toggleVoiceSearch() {
    if (!recognition) {
        if (!initVoiceSearch()) {
            alert('❌ البحث الصوتي غير مدعوم في هذا المتصفح.\nيرجى استخدام Chrome أو Edge أو Safari.');
            return;
        }
    }

    if (isListening) {
        recognition.stop();
    } else {
        recognition.start();
    }
}

// تحديث واجهة البحث الصوتي
function updateVoiceUI(listening) {
    const voiceBtn = document.getElementById('voiceSearchBtn');
    const voiceIcon = document.getElementById('voiceIcon');
    const voiceStatus = document.getElementById('voiceStatus');
    const statusText = document.getElementById('voiceStatusText');
    const indicator = document.getElementById('recordingIndicator');

    if (listening) {
        voiceBtn.className = 'btn btn-danger';
        voiceIcon.className = 'fas fa-microphone-slash';
        voiceBtn.title = 'إيقاف البحث الصوتي';
        voiceStatus.style.display = 'block';
        statusText.textContent = 'جاري الاستماع... تحدث الآن';
        indicator.className = 'fas fa-circle text-danger me-1';

        // تأثير الوميض
        indicator.style.animation = 'blink 1s infinite';
    } else {
        voiceBtn.className = 'btn btn-outline-primary';
        voiceIcon.className = 'fas fa-microphone';
        voiceBtn.title = 'البحث الصوتي';
        voiceStatus.style.display = 'none';
        indicator.style.animation = 'none';
    }
}

// إظهار رسالة البحث الصوتي
function showVoiceMessage(message, type) {
    const voiceStatus = document.getElementById('voiceStatus');
    const statusText = document.getElementById('voiceStatusText');
    const indicator = document.getElementById('recordingIndicator');

    voiceStatus.style.display = 'block';
    statusText.textContent = message;

    if (type === 'success') {
        indicator.className = 'fas fa-check-circle text-success me-1';
    } else if (type === 'error') {
        indicator.className = 'fas fa-exclamation-circle text-danger me-1';
    }

    // إخفاء الرسالة بعد 3 ثوان
    setTimeout(() => {
        voiceStatus.style.display = 'none';
    }, 3000);
}

// تهيئة البحث الصوتي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // فحص دعم البحث الصوتي
    if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
        const voiceBtn = document.getElementById('voiceSearchBtn');
        if (voiceBtn) {
            voiceBtn.style.display = 'none';
        }
    }
});
</script>

{% endblock %}
