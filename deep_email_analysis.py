#!/usr/bin/env python3
"""
تحليل عميق لبنية الرسائل والمرفقات
"""

import sys
import os
import imaplib
import email
from email.header import decode_header

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_manager import DatabaseManager
import logging

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_email_structure():
    """تحليل عميق لبنية الرسائل"""
    try:
        db = DatabaseManager()
        
        # الحصول على إعدادات IMAP
        account_query = """
        SELECT email_address, imap_server, imap_port, password_encrypted
        FROM email_accounts 
        WHERE is_active = 1 AND is_default = 1
        ORDER BY id DESC
        """
        
        account_result = db.execute_query(account_query)
        if not account_result:
            logger.error("❌ لا يوجد حساب بريد نشط")
            return False
        
        account = account_result[0]
        email_address = account[0]
        imap_server = account[1]
        imap_port = account[2]
        password_lob = account[3]

        # قراءة كلمة المرور من LOB
        if hasattr(password_lob, 'read'):
            password = password_lob.read()
            if isinstance(password, bytes):
                password = password.decode('utf-8')
        else:
            password = str(password_lob)
        
        logger.info(f"📧 تحليل حساب: {email_address}")
        logger.info(f"🔗 خادم: {imap_server}:{imap_port}")
        
        # الاتصال بـ IMAP
        try:
            mail = imaplib.IMAP4_SSL(imap_server, imap_port)
            mail.login(email_address, password)
            mail.select('INBOX')
            
            logger.info("✅ تم الاتصال بـ IMAP بنجاح")
            
            # البحث عن رسائل حديثة
            status, messages = mail.search(None, 'ALL')
            if status != 'OK':
                logger.error("❌ فشل في البحث عن الرسائل")
                return False
            
            message_ids = messages[0].split()
            logger.info(f"📬 عدد الرسائل: {len(message_ids)}")
            
            # تحليل آخر 5 رسائل
            analyzed_count = 0
            attachment_count = 0
            
            for msg_id in message_ids[-5:]:  # آخر 5 رسائل
                try:
                    # جلب الرسالة
                    status, msg_data = mail.fetch(msg_id, '(RFC822)')
                    if status != 'OK':
                        continue
                    
                    # تحليل الرسالة
                    email_message = email.message_from_bytes(msg_data[0][1])
                    
                    # معلومات أساسية
                    subject = decode_header(email_message["Subject"])[0][0]
                    if isinstance(subject, bytes):
                        subject = subject.decode()
                    
                    sender = email_message.get("From")
                    
                    logger.info(f"\n📧 تحليل رسالة: {subject[:50]}...")
                    logger.info(f"👤 من: {sender}")
                    logger.info(f"🔢 معرف: {msg_id.decode()}")
                    
                    # تحليل البنية
                    logger.info(f"📋 نوع المحتوى: {email_message.get_content_type()}")
                    logger.info(f"🔀 متعدد الأجزاء: {email_message.is_multipart()}")
                    
                    if email_message.is_multipart():
                        parts = list(email_message.walk())
                        logger.info(f"🧩 عدد الأجزاء: {len(parts)}")
                        
                        part_attachments = 0
                        for i, part in enumerate(parts):
                            content_type = part.get_content_type()
                            content_disposition = str(part.get("Content-Disposition", ""))
                            filename = part.get_filename()
                            
                            logger.info(f"  📄 جزء {i}: {content_type}")
                            logger.info(f"    📎 Content-Disposition: {content_disposition}")
                            logger.info(f"    📁 اسم الملف: {filename}")
                            
                            # التحقق من المرفقات
                            if filename or "attachment" in content_disposition:
                                part_attachments += 1
                                attachment_count += 1
                                
                                if filename:
                                    # فك تشفير اسم الملف
                                    try:
                                        decoded_filename = decode_header(filename)[0]
                                        if isinstance(decoded_filename[0], bytes):
                                            filename = decoded_filename[0].decode(decoded_filename[1] or 'utf-8')
                                        else:
                                            filename = decoded_filename[0]
                                    except:
                                        pass
                                    
                                    # حجم المرفق
                                    payload = part.get_payload(decode=True)
                                    size = len(payload) if payload else 0
                                    size_mb = size / (1024 * 1024)
                                    
                                    logger.info(f"    ✅ مرفق: {filename}")
                                    logger.info(f"    📊 الحجم: {size_mb:.2f} MB")
                                    logger.info(f"    🎯 النوع: {content_type}")
                        
                        logger.info(f"📎 إجمالي المرفقات في هذه الرسالة: {part_attachments}")
                    
                    analyzed_count += 1
                    
                except Exception as msg_error:
                    logger.error(f"❌ خطأ في تحليل الرسالة {msg_id}: {msg_error}")
                    continue
            
            logger.info(f"\n🎯 ملخص التحليل:")
            logger.info(f"📧 رسائل محللة: {analyzed_count}")
            logger.info(f"📎 مرفقات مكتشفة: {attachment_count}")
            
            mail.close()
            mail.logout()
            
        except Exception as imap_error:
            logger.error(f"❌ خطأ في IMAP: {imap_error}")
            return False
        
    except Exception as e:
        logger.error(f"❌ خطأ عام: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🔬 تحليل عميق لبنية الرسائل والمرفقات...")
    analyze_email_structure()
