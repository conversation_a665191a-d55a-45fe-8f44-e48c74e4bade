{% extends "base.html" %}

{% block title %}لا توجد بيانات - نظام التحليل الذكي{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card text-center">
            <div class="card-body py-5">
                <i class="fas fa-database fa-5x text-muted mb-4"></i>
                <h2 class="text-muted mb-4">لا توجد بيانات للعرض</h2>
                <p class="lead text-muted mb-4">
                    يبدو أنه لم يتم تشغيل التحليل بعد أو أن ملفات النتائج غير موجودة.
                </p>
                
                <div class="alert alert-info text-start">
                    <h6><i class="fas fa-info-circle"></i> للبدء في التحليل:</h6>
                    <ol class="mb-0">
                        <li>تأكد من وجود اتصال بقاعدة البيانات</li>
                        <li>شغل الأمر: <code>python run_analysis.py</code></li>
                        <li>انتظر حتى اكتمال التحليل</li>
                        <li>أعد تحميل هذه الصفحة</li>
                    </ol>
                </div>
                
                <div class="mt-4">
                    <button onclick="location.reload()" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-sync-alt"></i> إعادة تحميل
                    </button>
                    <a href="/" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-home"></i> العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card bg-light">
            <div class="card-body">
                <h6><i class="fas fa-question-circle"></i> هل تحتاج مساعدة؟</h6>
                <p class="mb-0">
                    تأكد من أن جميع المكتبات مثبتة بشكل صحيح وأن معلومات قاعدة البيانات صحيحة.
                    يمكنك مراجعة ملف <code>README.md</code> للحصول على تعليمات مفصلة.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
