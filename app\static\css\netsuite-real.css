/* 
NetSuite Oracle REAL Design - Exact Copy
تصميم NetSuite Oracle الحقيقي - نسخة طبق الأصل
*/

/* ========== Reset & Base ========== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #212529;
    direction: rtl;
    text-align: right;
    font-size: 15px;
    line-height: 1.4;
}

/* ========== NetSuite Header - REAL ========== */
.ns-header-real {
    background-color: #1f4e79;
    height: 50px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    padding: 0 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.ns-header-real .ns-logo {
    color: white;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.ns-header-real .ns-logo i {
    color: #fd7e14;
    font-size: 18px;
}

.ns-header-real .ns-user-menu {
    margin-left: auto;
    color: white;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.ns-header-real .ns-user-menu .dropdown-toggle {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.ns-header-real .ns-user-menu .dropdown-toggle:hover {
    background-color: rgba(255,255,255,0.1);
}

/* ========== NetSuite Sidebar - REAL ========== */
.ns-sidebar-real {
    position: fixed !important;
    top: 50px !important;
    right: 0 !important;
    width: 250px !important;
    height: calc(100vh - 50px) !important;
    background-color: #f8f9fa !important;
    border-left: 1px solid #dee2e6 !important;
    overflow-y: auto !important;
    z-index: 999 !important;
    padding: 0 !important;
    display: block !important;
    visibility: visible !important;
}

.ns-sidebar-real .ns-nav-section {
    border-bottom: 1px solid #dee2e6;
}

.ns-sidebar-real .ns-nav-header {
    background-color: #e9ecef;
    padding: 8px 15px;
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ========== Collapsible Sidebar Styles ========== */
.collapsible-header {
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    user-select: none;
}

.collapsible-header:hover {
    background-color: #dee2e6 !important;
    color: #212529 !important;
}

.collapsible-header.collapsed {
    background-color: #f8f9fa !important;
}

.toggle-icon {
    transition: transform 0.3s ease;
    font-size: 10px;
    color: #6c757d;
}

.collapsible-header.collapsed .toggle-icon {
    transform: rotate(-90deg);
}

.ns-nav-items {
    overflow: hidden;
    transition: all 0.3s ease;
    max-height: 1000px;
    opacity: 1;
}

.ns-nav-items.collapsed {
    max-height: 0;
    opacity: 0;
    padding: 0;
    margin: 0;
}

.ns-nav-items .ns-nav-item {
    padding-right: 30px !important;
    border-right: 3px solid transparent;
    transition: all 0.2s ease;
}

.ns-nav-items .ns-nav-item:hover {
    background-color: #f1f3f4 !important;
    border-right-color: #007bff;
    padding-right: 27px !important;
}

.ns-nav-items .ns-nav-item.active {
    background-color: #e3f2fd !important;
    border-right-color: #1976d2;
    color: #1976d2 !important;
}

/* ========== Sidebar Controls ========== */
.sidebar-controls {
    padding: 10px 15px;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
    text-align: center;
}

.sidebar-controls .btn {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 4px;
}

.sidebar-controls .btn:hover {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

/* ========== Search Box ========== */
.sidebar-search-container {
    padding: 10px 15px;
    border-bottom: 1px solid #dee2e6;
    background-color: #ffffff;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    right: 10px;
    color: #6c757d;
    font-size: 14px;
    z-index: 2;
}

.search-input {
    width: 100%;
    padding: 8px 35px 8px 35px;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    font-size: 13px;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #007bff;
    background-color: #ffffff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.clear-search {
    position: absolute;
    left: 10px;
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    font-size: 12px;
    z-index: 2;
}

.clear-search:hover {
    color: #dc3545;
}

.search-highlight {
    background-color: #fff3cd !important;
    border-right-color: #ffc107 !important;
}

.no-search-results {
    padding: 20px;
    text-align: center;
}

/* ========== Theme Selector ========== */
.sidebar-theme-container {
    padding: 8px 15px;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

.theme-selector {
    display: flex;
    align-items: center;
    gap: 8px;
}

.theme-label {
    font-size: 11px;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.theme-select {
    flex: 1;
    padding: 4px 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 11px;
    background-color: #ffffff;
}

/* ========== Favorites ========== */
.favorite-icon {
    font-size: 12px;
    color: #ffc107;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: auto;
}

.favorite-icon:hover {
    transform: scale(1.2);
    color: #ffb300;
}

.favorite-icon.active {
    color: #ffc107;
}

.favorites-section {
    background-color: #fff8e1;
    border-bottom: 2px solid #ffc107;
}

.favorites-section .ns-nav-header {
    background-color: #ffc107;
    color: #212529;
}

.favorite-item {
    background-color: #fffbf0 !important;
}

/* ========== Themes ========== */
/* Default Theme */
.ns-sidebar-real.theme-default {
    background-color: #f8f9fa;
}

/* Dark Theme */
.ns-sidebar-real.theme-dark {
    background-color: #2c3e50;
    color: #ecf0f1;
}

.theme-dark .ns-nav-header {
    background-color: #34495e !important;
    color: #ecf0f1 !important;
}

.theme-dark .ns-nav-item {
    color: #bdc3c7 !important;
}

.theme-dark .ns-nav-item:hover {
    background-color: #34495e !important;
    color: #ecf0f1 !important;
}

.theme-dark .sidebar-controls,
.theme-dark .sidebar-search-container,
.theme-dark .sidebar-theme-container {
    background-color: #34495e;
    border-color: #4a5f7a;
}

.theme-dark .search-input {
    background-color: #34495e;
    border-color: #4a5f7a;
    color: #ecf0f1;
}

/* Blue Theme */
.ns-sidebar-real.theme-blue {
    background: linear-gradient(180deg, #e3f2fd 0%, #bbdefb 100%);
}

.theme-blue .ns-nav-header {
    background: linear-gradient(135deg, #1976d2, #1565c0) !important;
    color: white !important;
}

.theme-blue .ns-nav-item:hover {
    background-color: #e1f5fe !important;
    border-right-color: #1976d2;
}

/* Green Theme */
.ns-sidebar-real.theme-green {
    background: linear-gradient(180deg, #e8f5e8 0%, #c8e6c9 100%);
}

.theme-green .ns-nav-header {
    background: linear-gradient(135deg, #388e3c, #2e7d32) !important;
    color: white !important;
}

.theme-green .ns-nav-item:hover {
    background-color: #f1f8e9 !important;
    border-right-color: #388e3c;
}

/* Purple Theme */
.ns-sidebar-real.theme-purple {
    background: linear-gradient(180deg, #f3e5f5 0%, #e1bee7 100%);
}

.theme-purple .ns-nav-header {
    background: linear-gradient(135deg, #7b1fa2, #6a1b9a) !important;
    color: white !important;
}

.theme-purple .ns-nav-item:hover {
    background-color: #fce4ec !important;
    border-right-color: #7b1fa2;
}

/* ========== Mobile Responsive ========== */
@media (max-width: 768px) {
    .ns-sidebar-real {
        width: 100% !important;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        z-index: 9999;
    }

    .ns-sidebar-real.show {
        transform: translateX(0);
    }

    .ns-main-real {
        margin-right: 0 !important;
    }

    .sidebar-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .sidebar-controls::before {
        content: "القوائم";
        font-weight: 600;
        color: #495057;
    }

    .search-input {
        font-size: 16px; /* منع التكبير في iOS */
    }

    .ns-nav-item {
        padding: 12px 15px !important;
        font-size: 14px;
    }

    .favorite-icon {
        font-size: 14px;
    }
}

/* ========== Sidebar Overlay for Mobile ========== */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9998;
}

@media (max-width: 768px) {
    .sidebar-overlay.active {
        display: block;
    }
}

/* ========== Enhanced Icons ========== */
.ns-nav-header i {
    color: #495057;
    font-size: 14px;
}

.theme-dark .ns-nav-header i {
    color: #ecf0f1;
}

.theme-blue .ns-nav-header i,
.theme-green .ns-nav-header i,
.theme-purple .ns-nav-header i {
    color: white;
}

/* ========== Smooth Animations ========== */
.ns-nav-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ns-nav-item:hover {
    transform: translateX(-3px);
}

.collapsible-header {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toggle-icon {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ========== Accessibility ========== */
.ns-nav-item:focus,
.collapsible-header:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.search-input:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.ns-sidebar-real .ns-nav-item {
    display: block;
    padding: 10px 15px;
    color: #495057;
    text-decoration: none;
    font-size: 13px;
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.2s;
    position: relative;
}

.ns-sidebar-real .ns-nav-item:hover {
    background-color: #e3f2fd;
    color: #1976d2;
    text-decoration: none;
}

.ns-sidebar-real .ns-nav-item.active {
    background-color: #e3f2fd;
    color: #1976d2;
    font-weight: 600;
    border-right: 3px solid #1976d2;
}

.ns-sidebar-real .ns-nav-item i {
    width: 16px;
    margin-left: 8px;
    font-size: 14px;
    color: #fd7e14;
}

/* ========== NetSuite Main Content - REAL ========== */
.ns-main-real {
    margin-right: 250px !important;
    margin-top: 50px !important;
    padding: 20px !important;
    min-height: calc(100vh - 50px) !important;
    background-color: #ffffff !important;
    position: relative !important;
    z-index: 1 !important;
}

/* ========== NetSuite Page Header - REAL ========== */
.ns-page-header-real {
    background-color: #ffffff;
    padding: 15px 0;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 20px;
}

.ns-page-header-real h1 {
    font-size: 24px;
    font-weight: 400;
    color: #212529;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.ns-page-header-real h1 i {
    color: #1f4e79;
    font-size: 22px;
}

/* ========== NetSuite Cards - REAL ========== */
.ns-card-real {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.ns-card-real .ns-card-header {
    background-color: #f8f9fa;
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    font-size: 16px;
    color: #495057;
}

.ns-card-real .ns-card-body {
    padding: 15px;
}

/* ========== NetSuite Tables - REAL ========== */
.ns-table-real {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
}

.ns-table-real thead {
    background-color: #f8f9fa;
}

.ns-table-real th {
    padding: 10px 8px;
    text-align: right;
    font-weight: 600;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    border-left: 1px solid #dee2e6;
    font-size: 14px;
}

.ns-table-real td {
    padding: 8px;
    border-bottom: 1px solid #f1f3f4;
    border-left: 1px solid #f1f3f4;
    color: #212529;
}

.ns-table-real tbody tr:hover {
    background-color: #f8f9fa;
}

/* ========== NetSuite Buttons - REAL ========== */
.ns-btn-real {
    display: inline-block;
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 400;
    text-align: center;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s;
}

.ns-btn-primary-real {
    background-color: #1f4e79;
    border-color: #1f4e79;
    color: #ffffff;
}

.ns-btn-primary-real:hover {
    background-color: #1a3f63;
    border-color: #1a3f63;
    color: #ffffff;
    text-decoration: none;
}

.ns-btn-secondary-real {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #ffffff;
}

.ns-btn-success-real {
    background-color: #28a745;
    border-color: #28a745;
    color: #ffffff;
}

.ns-btn-outline-real {
    background-color: transparent;
    border-color: #1f4e79;
    color: #1f4e79;
}

.ns-btn-outline-real:hover {
    background-color: #1f4e79;
    color: #ffffff;
    text-decoration: none;
}

/* ========== NetSuite Forms - REAL ========== */
.ns-form-real .form-group {
    margin-bottom: 15px;
}

.ns-form-real label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    font-size: 14px;
    color: #495057;
}

.ns-form-real .form-control {
    width: 100%;
    padding: 6px 8px;
    font-size: 14px;
    border: 1px solid #ced4da;
    border-radius: 3px;
    background-color: #ffffff;
    color: #495057;
}

.ns-form-real .form-control:focus {
    border-color: #1f4e79;
    outline: none;
    box-shadow: 0 0 0 2px rgba(31, 78, 121, 0.1);
}

/* ========== NetSuite Alerts - REAL ========== */
.ns-alert-real {
    padding: 10px 15px;
    margin-bottom: 15px;
    border: 1px solid transparent;
    border-radius: 3px;
    font-size: 13px;
}

.ns-alert-success-real {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.ns-alert-danger-real {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.ns-alert-warning-real {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.ns-alert-info-real {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* ========== NetSuite Stats Cards - REAL ========== */
.ns-stats-real {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.ns-stat-card-real {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.ns-stat-card-real .ns-stat-number {
    font-size: 28px;
    font-weight: 600;
    color: #1f4e79;
    margin-bottom: 5px;
}

.ns-stat-card-real .ns-stat-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ns-stat-card-real .ns-stat-icon {
    font-size: 24px;
    color: #fd7e14;
    margin-bottom: 10px;
}

/* ========== Force Sidebar Visibility ========== */
body {
    overflow-x: hidden;
}

/* Ensure sidebar is always visible on desktop */
@media (min-width: 769px) {
    .ns-sidebar-real {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        transform: translateX(0) !important;
    }
}

/* ========== Responsive Design ========== */
@media (max-width: 768px) {
    .ns-sidebar-real {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }

    .ns-sidebar-real.show {
        transform: translateX(0);
    }

    .ns-main-real {
        margin-right: 0 !important;
    }

    .ns-stats-real {
        grid-template-columns: 1fr;
    }
}

/* ========== Utility Classes ========== */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 5px; }
.mb-2 { margin-bottom: 10px; }
.mb-3 { margin-bottom: 15px; }
.mt-3 { margin-top: 15px; }
.p-3 { padding: 15px; }
.d-flex { display: flex; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }

/* ========== FORCE SIDEBAR DISPLAY ========== */
/* This ensures the sidebar is always visible */
.ns-sidebar-real {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: fixed !important;
    top: 50px !important;
    right: 0 !important;
    width: 250px !important;
    height: calc(100vh - 50px) !important;
    background-color: #f8f9fa !important;
    border-left: 1px solid #dee2e6 !important;
    z-index: 1000 !important;
    overflow-y: auto !important;
}

/* Force main content margin */
.ns-main-real {
    margin-right: 250px !important;
    margin-top: 50px !important;
    padding: 20px !important;
    background-color: #ffffff !important;
    min-height: calc(100vh - 50px) !important;
}
