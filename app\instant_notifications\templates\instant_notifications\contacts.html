{% extends "base.html" %}

{% block title %}إدارة جهات الاتصال{% endblock %}

{% block extra_head %}
<script>
// دوال النافذة الجديدة - في الـ head لتُحمل أولاً
function addContact() {
    document.getElementById('addContactWindow').style.display = 'flex';
    document.body.style.overflow = 'hidden';
    setTimeout(() => {
        document.getElementById('newContactName').focus();
    }, 100);
}

function closeAddContact() {
    document.getElementById('addContactWindow').style.display = 'none';
    document.body.style.overflow = '';
    // مسح الحقول
    document.getElementById('newContactName').value = '';
    document.getElementById('newContactPhone').value = '';
    document.getElementById('newContactType').value = '';
    document.getElementById('newContactNotes').value = '';
}

function saveNewContact() {
    const name = document.getElementById('newContactName').value.trim();
    const phone = document.getElementById('newContactPhone').value.trim();
    const type = document.getElementById('newContactType').value;
    const notes = document.getElementById('newContactNotes').value.trim();

    // الحصول على معرف النوع
    const typeSelect = document.getElementById('newContactType');
    const selectedOption = typeSelect.options[typeSelect.selectedIndex];
    const typeId = selectedOption ? selectedOption.dataset.typeId : null;

    if (!name || !phone || !type) {
        alert('⚠️ يرجى إدخال جميع البيانات المطلوبة');
        return;
    }

    // تعطيل الزر أثناء الحفظ
    const saveBtn = event.target;
    const originalText = saveBtn.innerHTML;
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-left: 8px;"></i>جاري الحفظ...';

    fetch('/instant-notifications/api/add_contact', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            contact_name: name,
            phone_number: phone,
            contact_type: type,
            contact_type_id: typeId,
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeAddContact();
            alert('✅ تم إضافة جهة الاتصال بنجاح!');
            setTimeout(() => location.reload(), 500);
        } else {
            alert('❌ فشل في الإضافة: ' + data.message);
            saveBtn.disabled = false;
            saveBtn.innerHTML = originalText;
        }
    })
    .catch(error => {
        alert('❌ خطأ في الاتصال: ' + error);
        saveBtn.disabled = false;
        saveBtn.innerHTML = originalText;
    });
}

// دالة بسيطة تعمل مضمونة
function addNewContact() {
    const name = prompt('اسم جهة الاتصال:');
    if (!name) return;

    const phone = prompt('رقم الهاتف (مع رمز الدولة):');
    if (!phone) return;

    const typeChoice = prompt('اختر نوع جهة الاتصال:\\n1- الإدارة العليا\\n2- المديرين\\n3- الموظفين\\n4- خدمة العملاء\\n5- العملاء\\n\\nأدخل الرقم:');

    const types = { '1': 'admin', '2': 'manager', '3': 'employee', '4': 'customer_service', '5': 'client' };
    const typeIds = { '1': '1', '2': '2', '3': '4', '4': '5', '5': '10' };

    if (!typeChoice || !types[typeChoice]) {
        alert('يرجى اختيار نوع صحيح');
        return;
    }

    const notes = prompt('الملاحظات (اختياري):') || '';

    fetch('/instant-notifications/api/add_contact', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            contact_name: name,
            phone_number: phone,
            contact_type: types[typeChoice],
            contact_type_id: typeIds[typeChoice],
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ تم إضافة جهة الاتصال بنجاح!');
            location.reload();
        } else {
            alert('❌ فشل في الإضافة: ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ خطأ في الاتصال: ' + error);
    });
}

// إجبار النافذة على الظهور
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('addContactModal');
    if (modal) {
        modal.style.zIndex = '999999';
        modal.addEventListener('show.bs.modal', function() {
            this.style.zIndex = '999999';
            document.body.style.overflow = 'hidden';
        });
        modal.addEventListener('hidden.bs.modal', function() {
            document.body.style.overflow = '';
        });
    }
});
</script>

<script>
// دالة إضافة جهة اتصال - تُحمل مع الصفحة
function openAddContactDialog() {
    // إنشاء نافذة مخصصة
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        z-index: 99999;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: fadeIn 0.3s ease;
    `;

    const dialog = document.createElement('div');
    dialog.style.cssText = `
        background: white;
        border-radius: 15px;
        padding: 0;
        max-width: 500px;
        width: 90%;
        max-height: 85vh;
        overflow-y: auto;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        animation: slideIn 0.3s ease;
    `;

    dialog.innerHTML = \`
        <div style="padding: 25px; border-bottom: 1px solid #e3e6f0; background: linear-gradient(135deg, #007bff, #0056b3); color: white; border-radius: 15px 15px 0 0;">
            <h4 style="margin: 0; font-weight: 600;">
                <i class="fas fa-user-plus" style="margin-left: 10px;"></i>
                إضافة جهة اتصال جديدة
            </h4>
        </div>

        <div style="padding: 25px;">
            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333; font-size: 14px;">اسم جهة الاتصال *</label>
                <input type="text" id="newContactName" style="
                    width: 100%;
                    padding: 14px;
                    border: 2px solid #e3e6f0;
                    border-radius: 10px;
                    font-size: 15px;
                    transition: all 0.3s;
                    box-sizing: border-box;
                " placeholder="أدخل اسم جهة الاتصال" required
                onfocus="this.style.borderColor='#007bff'; this.style.boxShadow='0 0 0 3px rgba(0,123,255,0.1)'"
                onblur="this.style.borderColor='#e3e6f0'; this.style.boxShadow='none'">
            </div>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333; font-size: 14px;">رقم الهاتف *</label>
                <input type="text" id="newPhoneNumber" style="
                    width: 100%;
                    padding: 14px;
                    border: 2px solid #e3e6f0;
                    border-radius: 10px;
                    font-size: 15px;
                    transition: all 0.3s;
                    box-sizing: border-box;
                " placeholder="967774893877" required
                onfocus="this.style.borderColor='#007bff'; this.style.boxShadow='0 0 0 3px rgba(0,123,255,0.1)'"
                onblur="this.style.borderColor='#e3e6f0'; this.style.boxShadow='none'">
                <small style="color: #6c757d; font-size: 13px; margin-top: 5px; display: block;">
                    <i class="fas fa-info-circle"></i> أدخل الرقم مع رمز الدولة (مثال: 967774893877)
                </small>
            </div>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333; font-size: 14px;">نوع جهة الاتصال *</label>
                <select id="newContactType" style="
                    width: 100%;
                    padding: 14px;
                    border: 2px solid #e3e6f0;
                    border-radius: 10px;
                    font-size: 15px;
                    background: white;
                    box-sizing: border-box;
                " required>
                    <option value="">اختر نوع جهة الاتصال</option>
                    <option value="admin" data-type-id="1">👑 الإدارة العليا</option>
                    <option value="manager" data-type-id="2">👔 المديرين</option>
                    <option value="supervisor" data-type-id="3">✅ المشرفين</option>
                    <option value="employee" data-type-id="4">👤 الموظفين</option>
                    <option value="customer_service" data-type-id="5">🎧 خدمة العملاء</option>
                    <option value="accounting" data-type-id="6">🧮 المحاسبة</option>
                    <option value="warehouse" data-type-id="7">🏪 المستودعات</option>
                    <option value="transport" data-type-id="8">🚛 النقل والشحن</option>
                    <option value="customs" data-type-id="9">📋 التخليص الجمركي</option>
                    <option value="client" data-type-id="10">🤝 العملاء</option>
                    <option value="supplier" data-type-id="11">🏭 الموردين</option>
                    <option value="emergency" data-type-id="12">🚨 الطوارئ</option>
                </select>
            </div>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333; font-size: 14px;">الملاحظات</label>
                <textarea id="newContactNotes" style="
                    width: 100%;
                    height: 90px;
                    padding: 14px;
                    border: 2px solid #e3e6f0;
                    border-radius: 10px;
                    font-size: 15px;
                    resize: vertical;
                    font-family: inherit;
                    box-sizing: border-box;
                " placeholder="ملاحظات اختيارية..."
                onfocus="this.style.borderColor='#007bff'; this.style.boxShadow='0 0 0 3px rgba(0,123,255,0.1)'"
                onblur="this.style.borderColor='#e3e6f0'; this.style.boxShadow='none'"></textarea>
            </div>
        </div>

        <div style="padding: 25px; border-top: 1px solid #e3e6f0; text-align: center; background: #f8f9fc; border-radius: 0 0 15px 15px;">
            <button onclick="saveNewContact()" style="
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                border: none;
                padding: 14px 28px;
                border-radius: 10px;
                margin-left: 10px;
                cursor: pointer;
                font-weight: 600;
                font-size: 15px;
                transition: all 0.3s;
                box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(40, 167, 69, 0.4)'"
               onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(40, 167, 69, 0.3)'">
                <i class="fas fa-save" style="margin-left: 8px;"></i> حفظ جهة الاتصال
            </button>
            <button onclick="closeNewContactDialog()" style="
                background: #6c757d;
                color: white;
                border: none;
                padding: 14px 28px;
                border-radius: 10px;
                cursor: pointer;
                font-size: 15px;
                transition: all 0.3s;
            " onmouseover="this.style.background='#5a6268'" onmouseout="this.style.background='#6c757d'">
                <i class="fas fa-times" style="margin-left: 8px;"></i> إلغاء
            </button>
        </div>
    \`;

    // إضافة CSS للأنيميشن
    const style = document.createElement('style');
    style.textContent = \`
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
    \`;
    document.head.appendChild(style);

    overlay.appendChild(dialog);
    document.body.appendChild(overlay);

    // التركيز على الحقل الأول
    setTimeout(() => {
        document.getElementById('newContactName').focus();
    }, 300);

    // حفظ مرجع للنافذة
    window.currentContactDialog = overlay;
}

// دالة الحفظ
function saveNewContact() {
    const contactName = document.getElementById('newContactName').value.trim();
    const phoneNumber = document.getElementById('newPhoneNumber').value.trim();
    const contactType = document.getElementById('newContactType').value;
    const notes = document.getElementById('newContactNotes').value.trim();

    // الحصول على معرف نوع جهة الاتصال
    const contactTypeSelect = document.getElementById('newContactType');
    const selectedOption = contactTypeSelect.options[contactTypeSelect.selectedIndex];
    const contactTypeId = selectedOption ? selectedOption.dataset.typeId : null;

    if (!contactName || !phoneNumber || !contactType) {
        alert('⚠️ يرجى إدخال جميع البيانات المطلوبة');
        return;
    }

    // التحقق من تنسيق رقم الهاتف
    if (!/^(\+?967|967)\d{9}$/.test(phoneNumber.replace(/\s/g, ''))) {
        alert('❌ تنسيق رقم الهاتف غير صحيح. يجب أن يبدأ بـ 967 ويحتوي على 12 رقم');
        return;
    }

    // تعطيل الزر أثناء الحفظ
    const saveBtn = event.target;
    const originalText = saveBtn.innerHTML;
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-left: 8px;"></i> جاري الحفظ...';
    saveBtn.style.background = '#6c757d';

    fetch('/instant-notifications/api/add_contact', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            contact_name: contactName,
            phone_number: phoneNumber,
            contact_type: contactType,
            contact_type_id: contactTypeId,
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeNewContactDialog();
            alert('✅ تم إضافة جهة الاتصال بنجاح!');
            setTimeout(() => location.reload(), 500);
        } else {
            alert('❌ فشل في إضافة جهة الاتصال: ' + data.message);
            saveBtn.disabled = false;
            saveBtn.innerHTML = originalText;
            saveBtn.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
        }
    })
    .catch(error => {
        alert('❌ خطأ في الاتصال: ' + error);
        saveBtn.disabled = false;
        saveBtn.innerHTML = originalText;
        saveBtn.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
    });
}

// دالة الإغلاق
function closeNewContactDialog() {
    if (window.currentContactDialog) {
        window.currentContactDialog.style.animation = 'fadeOut 0.3s ease';
        setTimeout(() => {
            if (window.currentContactDialog && window.currentContactDialog.parentNode) {
                document.body.removeChild(window.currentContactDialog);
            }
            window.currentContactDialog = null;
        }, 300);
    }
}
</script>
{% endblock %}

{% block extra_css %}
<style>
/* Bootstrap 5.1.3 - لا يحتاج إصلاحات معقدة */
.modal {
    z-index: 1050;
}

.modal-backdrop {
    z-index: 1040;
}

/* تحسين مظهر أنواع جهات الاتصال */
.contact-type-preview {
    padding: 10px;
    border-radius: 5px;
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    margin-top: 10px;
}

.contact-card {
    border: 1px solid #e3e6f0;
    border-radius: 10px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.contact-card:hover {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transform: translateY(-2px);
}

.contact-card.inactive {
    opacity: 0.6;
    background-color: #f8f9fc;
}

/* أنيميشن للنافذة */
@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-address-book text-info"></i>
            إدارة جهات الاتصال
        </h1>
        <div>
            <button class="btn btn-primary btn-sm" onclick="document.getElementById('addContactWindow').style.display='flex'; document.body.style.overflow='hidden'; setTimeout(() => document.getElementById('newContactName').focus(), 100);">
                <i class="fas fa-plus"></i> إضافة جهة اتصال
            </button>
            <a href="{{ url_for('instant_notifications.dashboard') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>

    <!-- جهات الاتصال -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">
                <i class="fas fa-users"></i>
                قائمة جهات الاتصال
            </h6>
        </div>
        <div class="card-body">
            {% if contacts %}
                <div class="table-responsive">
                    <table class="table table-bordered" id="contactsTable">
                        <thead>
                            <tr>
                                <th>اسم جهة الاتصال</th>
                                <th>رقم الهاتف</th>
                                <th>النوع</th>
                                <th>الحالة</th>
                                <th>الملاحظات</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for contact in contacts %}
                            <tr>
                                <td>
                                    <strong>{{ contact.contact_name }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ contact.phone_number }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-primary {{ contact.color_class }}">
                                        <i class="{{ contact.icon_class }}"></i>
                                        {{ contact.type_name_ar }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge {{ 'bg-success' if contact.is_active else 'bg-secondary' }}">
                                        {{ 'نشط' if contact.is_active else 'غير نشط' }}
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">{{ contact.notes or 'لا توجد ملاحظات' }}</small>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ contact.created_at.strftime('%Y-%m-%d') if contact.created_at else 'غير محدد' }}
                                    </small>
                                </td>
                                <td>
                                    <button class="btn btn-success btn-sm"
                                            onclick="
                                                const phoneNumber = '{{ contact.phone_number }}';
                                                const contactName = '{{ contact.contact_name }}';
                                                const testMessage = '🧪 رسالة اختبار إلى ' + contactName + '\\nالوقت: ' + new Date().toLocaleString('ar');

                                                if (confirm('هل تريد إرسال رسالة اختبار إلى ' + contactName + '؟\\n\\nالرقم: ' + phoneNumber + '\\nالرسالة: ' + testMessage)) {
                                                    fetch('/instant-notifications/api/test_message', {
                                                        method: 'POST',
                                                        headers: {'Content-Type': 'application/json'},
                                                        body: JSON.stringify({
                                                            phone_number: phoneNumber,
                                                            message: testMessage
                                                        })
                                                    })
                                                    .then(response => response.json())
                                                    .then(data => {
                                                        if (data.success) {
                                                            alert('✅ تم إرسال رسالة الاختبار بنجاح');
                                                        } else {
                                                            alert('❌ فشل في الإرسال: ' + data.message);
                                                        }
                                                    })
                                                    .catch(error => {
                                                        alert('❌ خطأ في الاتصال: ' + error);
                                                    });
                                                }
                                            ">
                                        <i class="fas fa-paper-plane"></i> اختبار
                                    </button>
                                    <button class="btn btn-warning btn-sm"
                                            onclick="
                                                document.getElementById('editContactId').value = '{{ contact.id }}';
                                                document.getElementById('editContactName').value = '{{ contact.contact_name }}';
                                                document.getElementById('editContactPhone').value = '{{ contact.phone_number }}';
                                                document.getElementById('editContactType').value = '{{ contact.contact_type }}';
                                                document.getElementById('editContactNotes').value = '{{ contact.notes or '' }}';
                                                document.getElementById('editContactWindow').style.display = 'flex';
                                                document.body.style.overflow = 'hidden';
                                                setTimeout(() => document.getElementById('editContactName').focus(), 100);
                                            ">
                                        <i class="fas fa-edit"></i> تعديل
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-address-book fa-3x text-info mb-3"></i>
                    <h5>لا توجد جهات اتصال</h5>
                    <p class="text-muted">لم يتم إضافة أي جهات اتصال بعد</p>
                    <button class="btn btn-primary" onclick="document.getElementById('addContactWindow').style.display='flex'; document.body.style.overflow='hidden'; setTimeout(() => document.getElementById('newContactName').focus(), 100);">
                        <i class="fas fa-plus"></i> إضافة جهة اتصال جديدة
                    </button>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- معلومات مفيدة -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-warning">
                <i class="fas fa-lightbulb"></i>
                معلومات مفيدة
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">تنسيق أرقام الهواتف:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> 967774893877 (مع رمز الدولة)</li>
                        <li><i class="fas fa-check text-success"></i> +967774893877 (مع علامة +)</li>
                        <li><i class="fas fa-times text-danger"></i> 0774893877 (بدون رمز الدولة)</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="text-info">أنواع جهات الاتصال:</h6>
                    <ul class="list-unstyled">
                        <li><span class="badge bg-danger">إدارة</span> - للمديرين والمسؤولين</li>
                        <li><span class="badge bg-info">عام</span> - للمستخدمين العاديين</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة جهة اتصال الجديدة -->
<div id="addContactWindow" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.6); z-index: 99999; align-items: center; justify-content: center;">
    <div style="background: white; border-radius: 12px; width: 90%; max-width: 500px; box-shadow: 0 20px 40px rgba(0,0,0,0.3);">
        <!-- Header -->
        <div style="padding: 20px; background: linear-gradient(135deg, #007bff, #0056b3); color: white; border-radius: 12px 12px 0 0; display: flex; justify-content: space-between; align-items: center;">
            <h4 style="margin: 0;">
                <i class="fas fa-user-plus" style="margin-left: 10px;"></i>
                إضافة جهة اتصال جديدة
            </h4>
            <button onclick="document.getElementById('addContactWindow').style.display='none'; document.body.style.overflow=''; document.getElementById('newContactName').value=''; document.getElementById('newContactPhone').value=''; document.getElementById('newContactType').value=''; document.getElementById('newContactNotes').value='';" style="background: none; border: none; color: white; font-size: 24px; cursor: pointer; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center;">×</button>
        </div>

        <!-- Body -->
        <div style="padding: 25px;">

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">اسم جهة الاتصال *</label>
                <input type="text" id="newContactName" style="width: 100%; padding: 12px; border: 2px solid #e3e6f0; border-radius: 8px; box-sizing: border-box; font-size: 14px;" placeholder="أدخل اسم جهة الاتصال" required>
            </div>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">رقم الهاتف *</label>
                <input type="text" id="newContactPhone" style="width: 100%; padding: 12px; border: 2px solid #e3e6f0; border-radius: 8px; box-sizing: border-box; font-size: 14px;" placeholder="967774893877" required>
                <small style="color: #6c757d; font-size: 12px; margin-top: 5px; display: block;">أدخل الرقم مع رمز الدولة (مثال: 967774893877)</small>
            </div>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">نوع جهة الاتصال *</label>
                <select id="newContactType" style="width: 100%; padding: 12px; border: 2px solid #e3e6f0; border-radius: 8px; box-sizing: border-box; font-size: 14px; background: white;" required>
                    <option value="">اختر نوع جهة الاتصال</option>
                    <option value="admin" data-type-id="1">الإدارة العليا</option>
                    <option value="manager" data-type-id="2">المديرين</option>
                    <option value="supervisor" data-type-id="3">المشرفين</option>
                    <option value="employee" data-type-id="4">الموظفين</option>
                    <option value="customer_service" data-type-id="5">خدمة العملاء</option>
                    <option value="accounting" data-type-id="6">المحاسبة</option>
                    <option value="warehouse" data-type-id="7">المستودعات</option>
                    <option value="transport" data-type-id="8">النقل والشحن</option>
                    <option value="customs" data-type-id="9">التخليص الجمركي</option>
                    <option value="client" data-type-id="10">العملاء</option>
                    <option value="supplier" data-type-id="11">الموردين</option>
                    <option value="emergency" data-type-id="12">الطوارئ</option>
                </select>
            </div>

            <div style="margin-bottom: 25px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">الملاحظات</label>
                <textarea id="newContactNotes" style="width: 100%; height: 80px; padding: 12px; border: 2px solid #e3e6f0; border-radius: 8px; box-sizing: border-box; font-size: 14px; resize: vertical; font-family: inherit;" placeholder="ملاحظات اختيارية..."></textarea>
            </div>
        </div>

        <!-- Footer -->
        <div style="padding: 20px; border-top: 1px solid #e3e6f0; text-align: center; background: #f8f9fc; border-radius: 0 0 12px 12px;">
            <button onclick="
                const name = document.getElementById('newContactName').value.trim();
                const phone = document.getElementById('newContactPhone').value.trim();
                const type = document.getElementById('newContactType').value;
                const notes = document.getElementById('newContactNotes').value.trim();
                const typeSelect = document.getElementById('newContactType');
                const selectedOption = typeSelect.options[typeSelect.selectedIndex];
                const typeId = selectedOption ? selectedOption.dataset.typeId : null;

                if (!name || !phone || !type) {
                    alert('⚠️ يرجى إدخال جميع البيانات المطلوبة');
                    return;
                }

                this.disabled = true;
                this.innerHTML = '<i class=\'fas fa-spinner fa-spin\'></i> جاري الحفظ...';

                fetch('/instant-notifications/api/add_contact', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        contact_name: name,
                        phone_number: phone,
                        contact_type: type,
                        contact_type_id: typeId,
                        notes: notes
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('addContactWindow').style.display='none';
                        document.body.style.overflow='';
                        alert('✅ تم إضافة جهة الاتصال بنجاح!');
                        location.reload();
                    } else {
                        alert('❌ فشل: ' + data.message);
                        this.disabled = false;
                        this.innerHTML = '<i class=\'fas fa-save\'></i> حفظ جهة الاتصال';
                    }
                })
                .catch(error => {
                    alert('❌ خطأ: ' + error);
                    this.disabled = false;
                    this.innerHTML = '<i class=\'fas fa-save\'></i> حفظ جهة الاتصال';
                });
            " style="background: linear-gradient(135deg, #28a745, #20c997); color: white; border: none; padding: 12px 24px; border-radius: 8px; margin-left: 10px; cursor: pointer; font-weight: 600; font-size: 14px;">
                <i class="fas fa-save" style="margin-left: 8px;"></i>حفظ جهة الاتصال
            </button>
            <button onclick="document.getElementById('addContactWindow').style.display='none'; document.body.style.overflow=''; document.getElementById('newContactName').value=''; document.getElementById('newContactPhone').value=''; document.getElementById('newContactType').value=''; document.getElementById('newContactNotes').value='';" style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-size: 14px;">
                <i class="fas fa-times" style="margin-left: 8px;"></i>إلغاء
            </button>
        </div>
    </div>
</div>

<!-- نافذة تعديل جهة اتصال -->
<div id="editContactWindow" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.6); z-index: 99999; align-items: center; justify-content: center;">
    <div style="background: white; border-radius: 12px; width: 90%; max-width: 500px; box-shadow: 0 20px 40px rgba(0,0,0,0.3);">
        <!-- Header -->
        <div style="padding: 20px; background: linear-gradient(135deg, #ffc107, #e0a800); color: white; border-radius: 12px 12px 0 0; display: flex; justify-content: space-between; align-items: center;">
            <h4 style="margin: 0;">
                <i class="fas fa-edit" style="margin-left: 10px;"></i>
                تعديل جهة اتصال
            </h4>
            <button onclick="document.getElementById('editContactWindow').style.display='none'; document.body.style.overflow='';" style="background: none; border: none; color: white; font-size: 24px; cursor: pointer; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center;">×</button>
        </div>

        <!-- Body -->
        <div style="padding: 25px;">
            <input type="hidden" id="editContactId">

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">اسم جهة الاتصال *</label>
                <input type="text" id="editContactName" style="width: 100%; padding: 12px; border: 2px solid #e3e6f0; border-radius: 8px; box-sizing: border-box; font-size: 14px;" placeholder="أدخل اسم جهة الاتصال" required>
            </div>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">رقم الهاتف *</label>
                <input type="text" id="editContactPhone" style="width: 100%; padding: 12px; border: 2px solid #e3e6f0; border-radius: 8px; box-sizing: border-box; font-size: 14px;" placeholder="967774893877" required>
                <small style="color: #6c757d; font-size: 12px; margin-top: 5px; display: block;">أدخل الرقم مع رمز الدولة (مثال: 967774893877)</small>
            </div>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">نوع جهة الاتصال *</label>
                <select id="editContactType" style="width: 100%; padding: 12px; border: 2px solid #e3e6f0; border-radius: 8px; box-sizing: border-box; font-size: 14px; background: white;" required>
                    <option value="">اختر نوع جهة الاتصال</option>
                    <option value="admin" data-type-id="1">الإدارة العليا</option>
                    <option value="manager" data-type-id="2">المديرين</option>
                    <option value="supervisor" data-type-id="3">المشرفين</option>
                    <option value="employee" data-type-id="4">الموظفين</option>
                    <option value="customer_service" data-type-id="5">خدمة العملاء</option>
                    <option value="accounting" data-type-id="6">المحاسبة</option>
                    <option value="warehouse" data-type-id="7">المستودعات</option>
                    <option value="transport" data-type-id="8">النقل والشحن</option>
                    <option value="customs" data-type-id="9">التخليص الجمركي</option>
                    <option value="client" data-type-id="10">العملاء</option>
                    <option value="supplier" data-type-id="11">الموردين</option>
                    <option value="emergency" data-type-id="12">الطوارئ</option>
                </select>
            </div>

            <div style="margin-bottom: 25px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">الملاحظات</label>
                <textarea id="editContactNotes" style="width: 100%; height: 80px; padding: 12px; border: 2px solid #e3e6f0; border-radius: 8px; box-sizing: border-box; font-size: 14px; resize: vertical; font-family: inherit;" placeholder="ملاحظات اختيارية..."></textarea>
            </div>
        </div>

        <!-- Footer -->
        <div style="padding: 20px; border-top: 1px solid #e3e6f0; text-align: center; background: #f8f9fc; border-radius: 0 0 12px 12px;">
            <button onclick="
                const id = document.getElementById('editContactId').value;
                const name = document.getElementById('editContactName').value.trim();
                const phone = document.getElementById('editContactPhone').value.trim();
                const type = document.getElementById('editContactType').value;
                const notes = document.getElementById('editContactNotes').value.trim();
                const typeSelect = document.getElementById('editContactType');
                const selectedOption = typeSelect.options[typeSelect.selectedIndex];
                const typeId = selectedOption ? selectedOption.dataset.typeId : null;

                if (!name || !phone || !type) {
                    alert('⚠️ يرجى إدخال جميع البيانات المطلوبة');
                    return;
                }

                this.disabled = true;
                this.innerHTML = '<i class=\'fas fa-spinner fa-spin\'></i> جاري التحديث...';

                fetch('/instant-notifications/api/update_contact/' + id, {
                    method: 'PUT',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        contact_name: name,
                        phone_number: phone,
                        contact_type: type,
                        contact_type_id: typeId,
                        notes: notes
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('editContactWindow').style.display='none';
                        document.body.style.overflow='';
                        alert('✅ تم تحديث جهة الاتصال بنجاح!');
                        location.reload();
                    } else {
                        alert('❌ فشل في التحديث: ' + data.message);
                        this.disabled = false;
                        this.innerHTML = '<i class=\'fas fa-save\'></i> حفظ التعديل';
                    }
                })
                .catch(error => {
                    alert('❌ خطأ: ' + error);
                    this.disabled = false;
                    this.innerHTML = '<i class=\'fas fa-save\'></i> حفظ التعديل';
                });
            " style="background: linear-gradient(135deg, #ffc107, #e0a800); color: white; border: none; padding: 12px 24px; border-radius: 8px; margin-left: 10px; cursor: pointer; font-weight: 600; font-size: 14px;">
                <i class="fas fa-save" style="margin-left: 8px;"></i>حفظ التعديل
            </button>
            <button onclick="document.getElementById('editContactWindow').style.display='none'; document.body.style.overflow='';" style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-size: 14px;">
                <i class="fas fa-times" style="margin-left: 8px;"></i>إلغاء
            </button>
        </div>
    </div>
</div>

<script>
// دالة إضافة جهة اتصال - تعمل فوراً
window.openAddContactDialog = function() {
    // إنشاء نافذة مخصصة مباشرة
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 99999;
        display: flex;
        align-items: center;
        justify-content: center;
    `;

    const dialog = document.createElement('div');
    dialog.style.cssText = `
        background: white;
        border-radius: 10px;
        padding: 0;
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    `;

    dialog.innerHTML = \`
        <div style="padding: 20px; border-bottom: 1px solid #e3e6f0; background: #007bff; color: white; border-radius: 10px 10px 0 0;">
            <h5 style="margin: 0;">
                <i class="fas fa-user-plus"></i>
                إضافة جهة اتصال جديدة
            </h5>
        </div>

        <div style="padding: 20px;">
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">اسم جهة الاتصال *</label>
                <input type="text" id="quickContactName" style="
                    width: 100%;
                    padding: 12px;
                    border: 2px solid #ddd;
                    border-radius: 8px;
                    font-size: 14px;
                    transition: border-color 0.3s;
                " placeholder="أدخل اسم جهة الاتصال" required>
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">رقم الهاتف *</label>
                <input type="text" id="quickPhoneNumber" style="
                    width: 100%;
                    padding: 12px;
                    border: 2px solid #ddd;
                    border-radius: 8px;
                    font-size: 14px;
                    transition: border-color 0.3s;
                " placeholder="967774893877" required>
                <small style="color: #666; font-size: 12px;">أدخل الرقم مع رمز الدولة (مثال: 967774893877)</small>
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">نوع جهة الاتصال *</label>
                <select id="quickContactType" style="
                    width: 100%;
                    padding: 12px;
                    border: 2px solid #ddd;
                    border-radius: 8px;
                    font-size: 14px;
                " required>
                    <option value="">اختر نوع جهة الاتصال</option>
                    <option value="admin" data-type-id="1">الإدارة العليا</option>
                    <option value="manager" data-type-id="2">المديرين</option>
                    <option value="employee" data-type-id="4">الموظفين</option>
                    <option value="customer_service" data-type-id="5">خدمة العملاء</option>
                    <option value="client" data-type-id="10">العملاء</option>
                </select>
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">الملاحظات</label>
                <textarea id="quickContactNotes" style="
                    width: 100%;
                    height: 80px;
                    padding: 12px;
                    border: 2px solid #ddd;
                    border-radius: 8px;
                    font-size: 14px;
                    resize: vertical;
                " placeholder="ملاحظات اختيارية..."></textarea>
            </div>
        </div>

        <div style="padding: 20px; border-top: 1px solid #e3e6f0; text-align: center; background: #f8f9fc; border-radius: 0 0 10px 10px;">
            <button onclick="saveQuickContact()" style="
                background: #28a745;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                margin-right: 10px;
                cursor: pointer;
                font-weight: bold;
                font-size: 14px;
                transition: background-color 0.3s;
            " onmouseover="this.style.background='#218838'" onmouseout="this.style.background='#28a745'">
                <i class="fas fa-save"></i> حفظ جهة الاتصال
            </button>
            <button onclick="closeQuickContactDialog()" style="
                background: #6c757d;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                transition: background-color 0.3s;
            " onmouseover="this.style.background='#5a6268'" onmouseout="this.style.background='#6c757d'">
                <i class="fas fa-times"></i> إلغاء
            </button>
        </div>
    \`;

    overlay.appendChild(dialog);
    document.body.appendChild(overlay);

    // التركيز على الحقل الأول
    setTimeout(() => {
        document.getElementById('quickContactName').focus();
    }, 100);

    // حفظ مرجع للنافذة
    window.quickContactDialog = overlay;
};

function showAddContactModal() {
    // محاولة استخدام Bootstrap Modal أولاً
    try {
        document.getElementById('addContactForm').reset();
        const modal = new bootstrap.Modal(document.getElementById('addContactModal'));
        modal.show();

        // التأكد من أن النافذة ظاهرة
        setTimeout(() => {
            const modalElement = document.getElementById('addContactModal');
            if (modalElement && !modalElement.classList.contains('show')) {
                // إذا فشل Bootstrap Modal، استخدم النافذة المخصصة
                showCustomAddContactModal();
            }
        }, 500);

    } catch (error) {
        console.log('فشل Bootstrap Modal، استخدام النافذة المخصصة');
        showCustomAddContactModal();
    }
}

function showCustomAddContactModal() {
    // إنشاء نافذة مخصصة
    const overlay = document.createElement('div');
    overlay.style.cssText = \`
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 99999;
        display: flex;
        align-items: center;
        justify-content: center;
    \`;

    const dialog = document.createElement('div');
    dialog.style.cssText = \`
        background: white;
        border-radius: 10px;
        padding: 0;
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    \`;

    dialog.innerHTML = \`
        <div style="padding: 20px; border-bottom: 1px solid #e3e6f0;">
            <h5 style="margin: 0; color: #333;">
                <i class="fas fa-user-plus" style="color: #007bff;"></i>
                إضافة جهة اتصال جديدة
            </h5>
        </div>

        <div style="padding: 20px;">
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم جهة الاتصال *</label>
                <input type="text" id="customContactName" style="
                    width: 100%;
                    padding: 10px;
                    border: 2px solid #ddd;
                    border-radius: 5px;
                    font-family: Arial, sans-serif;
                " placeholder="أدخل اسم جهة الاتصال" required>
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم الهاتف *</label>
                <input type="text" id="customPhoneNumber" style="
                    width: 100%;
                    padding: 10px;
                    border: 2px solid #ddd;
                    border-radius: 5px;
                    font-family: Arial, sans-serif;
                " placeholder="967774893877" required>
                <small style="color: #666;">أدخل الرقم مع رمز الدولة (مثال: 967774893877)</small>
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">نوع جهة الاتصال *</label>
                <select id="customContactType" style="
                    width: 100%;
                    padding: 10px;
                    border: 2px solid #ddd;
                    border-radius: 5px;
                    font-family: Arial, sans-serif;
                " required>
                    <option value="">اختر نوع جهة الاتصال</option>
                    <option value="admin" data-type-id="1">الإدارة العليا</option>
                    <option value="manager" data-type-id="2">المديرين</option>
                    <option value="employee" data-type-id="4">الموظفين</option>
                    <option value="customer_service" data-type-id="5">خدمة العملاء</option>
                    <option value="client" data-type-id="10">العملاء</option>
                </select>
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">الملاحظات</label>
                <textarea id="customContactNotes" style="
                    width: 100%;
                    height: 80px;
                    padding: 10px;
                    border: 2px solid #ddd;
                    border-radius: 5px;
                    font-family: Arial, sans-serif;
                    resize: vertical;
                " placeholder="ملاحظات اختيارية..."></textarea>
            </div>
        </div>

        <div style="padding: 20px; border-top: 1px solid #e3e6f0; text-align: center;">
            <button onclick="saveCustomContact()" style="
                background: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                margin-right: 10px;
                cursor: pointer;
                font-weight: bold;
            ">
                <i class="fas fa-save"></i> حفظ جهة الاتصال
            </button>
            <button onclick="closeCustomContactModal()" style="
                background: #6c757d;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
            ">
                <i class="fas fa-times"></i> إلغاء
            </button>
        </div>
    \`;

    overlay.appendChild(dialog);
    document.body.appendChild(overlay);

    // التركيز على الحقل الأول
    setTimeout(() => {
        document.getElementById('customContactName').focus();
    }, 100);

    // حفظ مرجع للنافذة
    window.customContactModalOverlay = overlay;
}

function saveContact() {
    const contactName = document.getElementById('contactName').value.trim();
    const phoneNumber = document.getElementById('phoneNumber').value.trim();
    const contactType = document.getElementById('contactType').value;
    const notes = document.getElementById('contactNotes').value.trim();
    
    if (!contactName || !phoneNumber) {
        showAlert('يرجى إدخال اسم جهة الاتصال ورقم الهاتف', 'error');
        return;
    }
    
    // التحقق من تنسيق رقم الهاتف
    if (!/^(\+?967|967)\d{9}$/.test(phoneNumber.replace(/\s/g, ''))) {
        showAlert('تنسيق رقم الهاتف غير صحيح. يجب أن يبدأ بـ 967 ويحتوي على 12 رقم', 'error');
        return;
    }
    
    fetch('/instant-notifications/api/add_contact', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            contact_name: contactName,
            phone_number: phoneNumber,
            contact_type: contactType,
            contact_type_id: getSelectedContactTypeId(),
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('addContactModal'));
            modal.hide();
            
            showAlert('تم إضافة جهة الاتصال بنجاح', 'success');
            
            // إعادة تحميل الصفحة بعد ثانيتين
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showAlert('خطأ في الإضافة: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showAlert('خطأ في الاتصال: ' + error, 'error');
    });
}

function testContact(phoneNumber, contactName) {
    const testMessage = `🧪 رسالة اختبار إلى ${contactName}\nالوقت: ${new Date().toLocaleString('ar')}`;
    
    if (confirm(`هل تريد إرسال رسالة اختبار إلى ${contactName}؟\n\nالرقم: ${phoneNumber}\nالرسالة: ${testMessage}`)) {
        fetch('/instant-notifications/api/test_message', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                phone_number: phoneNumber,
                message: testMessage
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(`تم إرسال رسالة اختبار بنجاح إلى ${contactName}!`, 'success');
            } else {
                showAlert(`فشل في إرسال رسالة اختبار إلى ${contactName}: ` + data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('خطأ في الاتصال: ' + error, 'error');
        });
    }
}

function editContact(contactId) {
    showAlert('ميزة التعديل ستكون متاحة قريباً', 'info');
}

function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 'alert-info';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

function updateContactTypePreview() {
    const select = document.getElementById('contactType');
    const preview = document.getElementById('contactTypePreview');
    const selectedOption = select.options[select.selectedIndex];

    if (selectedOption && selectedOption.value) {
        const icon = selectedOption.dataset.icon;
        const color = selectedOption.dataset.color;
        const typeName = selectedOption.text;

        preview.innerHTML = `
            <div class="alert alert-light">
                <i class="${icon} ${color}"></i>
                <strong>معاينة:</strong> ${typeName}
            </div>
        `;
    } else {
        preview.innerHTML = '';
    }
}

function getSelectedContactTypeId() {
    const select = document.getElementById('contactType');
    const selectedOption = select.options[select.selectedIndex];
    return selectedOption ? selectedOption.dataset.typeId : null;
}

function saveCustomContact() {
    const contactName = document.getElementById('customContactName').value.trim();
    const phoneNumber = document.getElementById('customPhoneNumber').value.trim();
    const contactType = document.getElementById('customContactType').value;
    const notes = document.getElementById('customContactNotes').value.trim();

    // الحصول على معرف نوع جهة الاتصال
    const contactTypeSelect = document.getElementById('customContactType');
    const selectedOption = contactTypeSelect.options[contactTypeSelect.selectedIndex];
    const contactTypeId = selectedOption ? selectedOption.dataset.typeId : null;

    if (!contactName || !phoneNumber || !contactType) {
        alert('يرجى إدخال جميع البيانات المطلوبة');
        return;
    }

    // التحقق من تنسيق رقم الهاتف
    if (!/^(\+?967|967)\d{9}$/.test(phoneNumber.replace(/\s/g, ''))) {
        alert('تنسيق رقم الهاتف غير صحيح. يجب أن يبدأ بـ 967 ويحتوي على 12 رقم');
        return;
    }

    fetch('/instant-notifications/api/add_contact', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            contact_name: contactName,
            phone_number: phoneNumber,
            contact_type: contactType,
            contact_type_id: contactTypeId,
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeCustomContactModal();
            showAlert('تم إضافة جهة الاتصال بنجاح', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            alert('فشل في إضافة جهة الاتصال: ' + data.message);
        }
    })
    .catch(error => {
        alert('خطأ في الاتصال: ' + error);
    });
}

function closeCustomContactModal() {
    if (window.customContactModalOverlay) {
        document.body.removeChild(window.customContactModalOverlay);
        window.customContactModalOverlay = null;
    }
}

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    // ربط أزرار إضافة جهة الاتصال
    const addContactBtn = document.getElementById('addContactBtn');
    const addContactBtnEmpty = document.getElementById('addContactBtnEmpty');

    if (addContactBtn) {
        addContactBtn.addEventListener('click', showAddContactModal);
    }

    if (addContactBtnEmpty) {
        addContactBtnEmpty.addEventListener('click', showAddContactModal);
    }

    console.log('تم تحميل صفحة جهات الاتصال بنجاح');
});

// دوال النافذة السريعة
window.saveQuickContact = function() {
    const contactName = document.getElementById('quickContactName').value.trim();
    const phoneNumber = document.getElementById('quickPhoneNumber').value.trim();
    const contactType = document.getElementById('quickContactType').value;
    const notes = document.getElementById('quickContactNotes').value.trim();

    // الحصول على معرف نوع جهة الاتصال
    const contactTypeSelect = document.getElementById('quickContactType');
    const selectedOption = contactTypeSelect.options[contactTypeSelect.selectedIndex];
    const contactTypeId = selectedOption ? selectedOption.dataset.typeId : null;

    if (!contactName || !phoneNumber || !contactType) {
        alert('يرجى إدخال جميع البيانات المطلوبة');
        return;
    }

    // التحقق من تنسيق رقم الهاتف
    if (!/^(\+?967|967)\d{9}$/.test(phoneNumber.replace(/\s/g, ''))) {
        alert('تنسيق رقم الهاتف غير صحيح. يجب أن يبدأ بـ 967 ويحتوي على 12 رقم');
        return;
    }

    // تعطيل الزر أثناء الحفظ
    const saveBtn = event.target;
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

    fetch('/instant-notifications/api/add_contact', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            contact_name: contactName,
            phone_number: phoneNumber,
            contact_type: contactType,
            contact_type_id: contactTypeId,
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeQuickContactDialog();
            alert('تم إضافة جهة الاتصال بنجاح!');
            setTimeout(() => location.reload(), 500);
        } else {
            alert('فشل في إضافة جهة الاتصال: ' + data.message);
            saveBtn.disabled = false;
            saveBtn.innerHTML = '<i class="fas fa-save"></i> حفظ جهة الاتصال';
        }
    })
    .catch(error => {
        alert('خطأ في الاتصال: ' + error);
        saveBtn.disabled = false;
        saveBtn.innerHTML = '<i class="fas fa-save"></i> حفظ جهة الاتصال';
    });
};

window.closeQuickContactDialog = function() {
    if (window.quickContactDialog) {
        document.body.removeChild(window.quickContactDialog);
        window.quickContactDialog = null;
    }
};

// حل بسيط وفعال
document.addEventListener('DOMContentLoaded', function() {
    const addBtn = document.getElementById('addContactBtn');
    const addBtnEmpty = document.getElementById('addContactBtnEmpty');

    function addContactHandler() {
            // إنشاء نافذة بسيطة
            const name = prompt('اسم جهة الاتصال:');
            if (!name) return;

            const phone = prompt('رقم الهاتف (مع رمز الدولة):');
            if (!phone) return;

            const types = {
                '1': 'admin',
                '2': 'manager',
                '3': 'employee',
                '4': 'customer_service',
                '5': 'client'
            };

            const typeChoice = prompt('اختر نوع جهة الاتصال:\\n1- الإدارة العليا\\n2- المديرين\\n3- الموظفين\\n4- خدمة العملاء\\n5- العملاء\\n\\nأدخل الرقم:');

            if (!typeChoice || !types[typeChoice]) {
                alert('يرجى اختيار نوع صحيح');
                return;
            }

            const notes = prompt('الملاحظات (اختياري):') || '';

            // إرسال البيانات
            fetch('/instant-notifications/api/add_contact', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contact_name: name,
                    phone_number: phone,
                    contact_type: types[typeChoice],
                    contact_type_id: typeChoice,
                    notes: notes
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ تم إضافة جهة الاتصال بنجاح!');
                    location.reload();
                } else {
                    alert('❌ فشل في الإضافة: ' + data.message);
                }
            })
            .catch(error => {
                alert('❌ خطأ في الاتصال: ' + error);
            });
    }

    if (addBtn) {
        addBtn.addEventListener('click', addContactHandler);
    }

    if (addBtnEmpty) {
        addBtnEmpty.addEventListener('click', addContactHandler);
    }
});

// دالة بسيطة تعمل مضمونة
function addNewContact() {
    const name = prompt('اسم جهة الاتصال:');
    if (!name) return;

    const phone = prompt('رقم الهاتف (مع رمز الدولة):');
    if (!phone) return;

    const typeChoice = prompt('اختر نوع جهة الاتصال:\n1- الإدارة العليا\n2- المديرين\n3- الموظفين\n4- خدمة العملاء\n5- العملاء\n\nأدخل الرقم:');

    const types = { '1': 'admin', '2': 'manager', '3': 'employee', '4': 'customer_service', '5': 'client' };
    const typeIds = { '1': '1', '2': '2', '3': '4', '4': '5', '5': '10' };

    if (!typeChoice || !types[typeChoice]) {
        alert('يرجى اختيار نوع صحيح');
        return;
    }

    const notes = prompt('الملاحظات (اختياري):') || '';

    fetch('/instant-notifications/api/add_contact', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            contact_name: name,
            phone_number: phone,
            contact_type: types[typeChoice],
            contact_type_id: typeIds[typeChoice],
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ تم إضافة جهة الاتصال بنجاح!');
            location.reload();
        } else {
            alert('❌ فشل في الإضافة: ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ خطأ في الاتصال: ' + error);
    });
}

// Bootstrap 5.1.3 - يعمل بشكل طبيعي
document.addEventListener('DOMContentLoaded', function() {
    console.log('Bootstrap 5.1.3 loaded - Modal should work normally');

    // التركيز على أول حقل عند فتح النافذة
    const modalElement = document.getElementById('addContactModal');
    if (modalElement) {
        modalElement.addEventListener('shown.bs.modal', function() {
            const firstInput = this.querySelector('#contactName');
            if (firstInput) {
                firstInput.focus();
            }
        });
    }
});

// نافذة مخصصة تعمل 100%
function showCustomModal() {
    // إنشاء overlay
    const overlay = document.createElement('div');
    overlay.id = 'customModalOverlay';
    overlay.style.cssText = \`
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        z-index: 99999;
        display: flex;
        align-items: center;
        justify-content: center;
    \`;

    // إنشاء النافذة
    const modal = document.createElement('div');
    modal.style.cssText = \`
        background: white;
        border-radius: 12px;
        width: 90%;
        max-width: 500px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    \`;

    modal.innerHTML = \`
        <div style="padding: 20px; background: #007bff; color: white; border-radius: 12px 12px 0 0;">
            <h4 style="margin: 0; display: flex; align-items: center; justify-content: space-between;">
                <span><i class="fas fa-user-plus"></i> إضافة جهة اتصال جديدة</span>
                <button onclick="closeCustomModal()" style="background: none; border: none; color: white; font-size: 24px; cursor: pointer;">×</button>
            </h4>
        </div>

        <form id="customContactForm" style="padding: 25px;">
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم جهة الاتصال *</label>
                <input type="text" id="customContactName" required style="width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 5px; box-sizing: border-box;" placeholder="أدخل اسم جهة الاتصال">
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم الهاتف *</label>
                <input type="text" id="customPhoneNumber" required style="width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 5px; box-sizing: border-box;" placeholder="967774893877">
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">نوع جهة الاتصال *</label>
                <select id="customContactType" required style="width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 5px; box-sizing: border-box;">
                    <option value="">اختر النوع</option>
                    <option value="admin" data-type-id="1">الإدارة العليا</option>
                    <option value="manager" data-type-id="2">المديرين</option>
                    <option value="employee" data-type-id="4">الموظفين</option>
                    <option value="customer_service" data-type-id="5">خدمة العملاء</option>
                    <option value="client" data-type-id="10">العملاء</option>
                </select>
            </div>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">الملاحظات</label>
                <textarea id="customContactNotes" style="width: 100%; height: 60px; padding: 10px; border: 2px solid #ddd; border-radius: 5px; box-sizing: border-box;" placeholder="ملاحظات اختيارية"></textarea>
            </div>

            <div style="text-align: center;">
                <button type="submit" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin-right: 10px; cursor: pointer;">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <button type="button" onclick="closeCustomModal()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                    <i class="fas fa-times"></i> إلغاء
                </button>
            </div>
        </form>
    \`;

    overlay.appendChild(modal);
    document.body.appendChild(overlay);
    document.body.style.overflow = 'hidden';

    setTimeout(() => document.getElementById('customContactName').focus(), 100);

    document.getElementById('customContactForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveCustomContact();
    });
}

function closeCustomModal() {
    const overlay = document.getElementById('customModalOverlay');
    if (overlay) {
        document.body.removeChild(overlay);
        document.body.style.overflow = '';
    }
}

function saveCustomContact() {
    const name = document.getElementById('customContactName').value.trim();
    const phone = document.getElementById('customPhoneNumber').value.trim();
    const type = document.getElementById('customContactType').value;
    const notes = document.getElementById('customContactNotes').value.trim();

    const typeSelect = document.getElementById('customContactType');
    const selectedOption = typeSelect.options[typeSelect.selectedIndex];
    const typeId = selectedOption ? selectedOption.dataset.typeId : null;

    if (!name || !phone || !type) {
        alert('يرجى إدخال جميع البيانات المطلوبة');
        return;
    }

    const submitBtn = document.querySelector('#customContactForm button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

    fetch('/instant-notifications/api/add_contact', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            contact_name: name,
            phone_number: phone,
            contact_type: type,
            contact_type_id: typeId,
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeCustomModal();
            alert('✅ تم إضافة جهة الاتصال بنجاح!');
            location.reload();
        } else {
            alert('❌ فشل: ' + data.message);
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ';
        }
    })
    .catch(error => {
        alert('❌ خطأ: ' + error);
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ';
    });
}

// دوال النافذة الجديدة - بسيطة ومضمونة
function addContact() {
    document.getElementById('addContactWindow').style.display = 'flex';
    document.body.style.overflow = 'hidden';
    setTimeout(() => {
        document.getElementById('newContactName').focus();
    }, 100);
}

function closeAddContact() {
    document.getElementById('addContactWindow').style.display = 'none';
    document.body.style.overflow = '';
    // مسح الحقول
    document.getElementById('newContactName').value = '';
    document.getElementById('newContactPhone').value = '';
    document.getElementById('newContactType').value = '';
    document.getElementById('newContactNotes').value = '';
}

function saveNewContact() {
    const name = document.getElementById('newContactName').value.trim();
    const phone = document.getElementById('newContactPhone').value.trim();
    const type = document.getElementById('newContactType').value;
    const notes = document.getElementById('newContactNotes').value.trim();

    // الحصول على معرف النوع
    const typeSelect = document.getElementById('newContactType');
    const selectedOption = typeSelect.options[typeSelect.selectedIndex];
    const typeId = selectedOption ? selectedOption.dataset.typeId : null;

    if (!name || !phone || !type) {
        alert('⚠️ يرجى إدخال جميع البيانات المطلوبة');
        return;
    }

    // تعطيل الزر أثناء الحفظ
    const saveBtn = event.target;
    const originalText = saveBtn.innerHTML;
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-left: 8px;"></i>جاري الحفظ...';

    fetch('/instant-notifications/api/add_contact', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            contact_name: name,
            phone_number: phone,
            contact_type: type,
            contact_type_id: typeId,
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeAddContact();
            alert('✅ تم إضافة جهة الاتصال بنجاح!');
            setTimeout(() => location.reload(), 500);
        } else {
            alert('❌ فشل في الإضافة: ' + data.message);
            saveBtn.disabled = false;
            saveBtn.innerHTML = originalText;
        }
    })
    .catch(error => {
        alert('❌ خطأ في الاتصال: ' + error);
        saveBtn.disabled = false;
        saveBtn.innerHTML = originalText;
    });
}
</script>
{% endblock %}
