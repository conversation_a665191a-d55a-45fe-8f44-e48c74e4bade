#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
جلب البيانات الحقيقية لنظام الأتمتة
Real Data for Automation System
"""

from datetime import datetime, timedelta
import logging
from database_manager import DatabaseManager

logger = logging.getLogger(__name__)

class AutomationDataProvider:
    """مزود البيانات الحقيقية للأتمتة"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def get_real_dashboard_data(self):
        """جلب البيانات الحقيقية للوحة الأتمتة"""
        try:
            # جلب الإحصائيات الحقيقية
            statistics = self._get_real_statistics()
            
            # جلب القواعد الحقيقية
            rules = self._get_real_rules()
            
            # جلب الأنشطة الحقيقية
            activities = self._get_real_activities()
            
            # جلب الإعدادات الحقيقية
            settings = self._get_real_settings()
            
            return {
                'statistics': statistics,
                'rules': rules,
                'activities': activities,
                'settings': settings
            }
            
        except Exception as e:
            logger.error(f"Error getting real dashboard data: {e}")
            return self._get_fallback_data()
    
    def _get_real_statistics(self):
        """جلب الإحصائيات الحقيقية"""
        try:
            # إحصائيات أوامر التسليم
            delivery_query = """
                SELECT 
                    COUNT(*) as total_orders,
                    COUNT(CASE WHEN order_status = 'completed' THEN 1 END) as completed_orders,
                    COUNT(CASE WHEN TRUNC(created_date) = TRUNC(SYSDATE) THEN 1 END) as today_orders
                FROM delivery_orders
                WHERE created_date >= SYSDATE - 30
            """
            
            delivery_result = self.db_manager.execute_query(delivery_query)
            
            if delivery_result:
                total_orders = delivery_result[0][0] or 0
                completed_orders = delivery_result[0][1] or 0
                today_orders = delivery_result[0][2] or 0
                
                # حساب معدل النجاح
                success_rate = (completed_orders / total_orders * 100) if total_orders > 0 else 0
            else:
                total_orders = completed_orders = today_orders = 0
                success_rate = 0
            
            # عدد القواعد النشطة من جدول الأتمتة (إذا وجد)
            try:
                rules_query = "SELECT COUNT(*) FROM automation_rules WHERE is_active = 1"
                rules_result = self.db_manager.execute_query(rules_query)
                active_rules = rules_result[0][0] if rules_result else 4
            except:
                active_rules = 4  # افتراضي
            
            # إجراءات الأتمتة اليوم
            try:
                automation_query = """
                    SELECT COUNT(*) FROM automation_log 
                    WHERE TRUNC(action_date) = TRUNC(SYSDATE)
                """
                automation_result = self.db_manager.execute_query(automation_query)
                processed_today = automation_result[0][0] if automation_result else today_orders
            except:
                processed_today = today_orders
            
            return {
                'total_automated_orders': total_orders,
                'success_rate': round(success_rate, 1),
                'active_rules': active_rules,
                'processed_today': processed_today
            }
            
        except Exception as e:
            logger.error(f"Error getting real statistics: {e}")
            return {
                'total_automated_orders': 0,
                'success_rate': 0.0,
                'active_rules': 0,
                'processed_today': 0
            }
    
    def _get_real_rules(self):
        """جلب قواعد الأتمتة الحقيقية"""
        try:
            # محاولة جلب من جدول automation_rules مع بيانات المخلص
            query = """
                SELECT
                    ar.id, ar.rule_name, ar.rule_type, ar.trigger_condition, ar.action_type,
                    ar.success_count, ar.failure_count, ar.last_executed, ar.priority_level,
                    ar.selected_agent_id, ar.agent_selection_criteria, ar.auto_agent_selection,
                    ar.agent_assignment_notes,
                    ca.agent_name, ca.agent_code,
                    b.brn_lname as branch_name,
                    cp.port_name_ar as port_name
                FROM automation_rules ar
                LEFT JOIN customs_agents ca ON ar.selected_agent_id = ca.id
                LEFT JOIN branches b ON ar.agent_branch_id = b.brn_no
                LEFT JOIN customs_ports cp ON ar.agent_port_id = cp.id
                WHERE ar.is_active = 1
                ORDER BY ar.priority_level DESC, ar.rule_name
            """

            results = self.db_manager.execute_query(query)

            rules = []
            for row in results:
                rule_type = row[2]
                trigger_condition = row[3]
                action_type = row[4]
                priority_level = row[8]
                selected_agent_id = row[9]
                agent_selection_criteria = row[10]
                auto_agent_selection = row[11]
                agent_assignment_notes = row[12]
                agent_name = row[13]
                agent_code = row[14]
                branch_name = row[15]
                port_name = row[16]

                # تحديد وصف القاعدة بناءً على النوع
                if rule_type == 'CONDITION_ONLY':
                    description = f"شرط مراقبة: {self._get_condition_description(trigger_condition)}"
                elif rule_type == 'ACTION_ONLY':
                    description = f"إجراء: {self._get_action_description(action_type)}"
                else:
                    description = self._get_rule_description(action_type)

                # إضافة معلومات المخلص للوصف إذا كان موجوداً
                if action_type == 'CREATE_DELIVERY_ORDER_WITH_AGENT' and agent_name:
                    agent_info = f"المخلص: {agent_name}"
                    if branch_name:
                        agent_info += f" - {branch_name}"
                    if port_name:
                        agent_info += f" ({port_name})"
                    description += f" | {agent_info}"

                rules.append({
                    'id': row[0],
                    'name': row[1],
                    'description': description,
                    'status': 'active',
                    'success_count': row[5] or 0,
                    'failure_count': row[6] or 0,
                    'last_executed': row[7].strftime('%Y-%m-%d %H:%M') if row[7] else 'لم يتم التنفيذ',
                    'rule_type': rule_type,
                    'action_type': action_type,
                    'priority_level': priority_level,
                    # بيانات المخلص الجديدة
                    'agent_info': {
                        'selected_agent_id': selected_agent_id,
                        'agent_name': agent_name,
                        'agent_code': agent_code,
                        'branch_name': branch_name,
                        'port_name': port_name,
                        'auto_selection': bool(auto_agent_selection),
                        'selection_criteria': agent_selection_criteria,
                        'assignment_notes': agent_assignment_notes
                    } if action_type == 'CREATE_DELIVERY_ORDER_WITH_AGENT' else None
                })
            
            # إرجاع القواعد من قاعدة البيانات فقط (حتى لو كانت فارغة)
            logger.info(f"Found {len(rules)} rules in database")
            return rules

        except Exception as e:
            logger.error(f"Error getting real rules: {e}")
            # إرجاع قائمة فارغة إذا حدث خطأ
            return []

    def _get_default_rules(self):
        """قواعد افتراضية للعرض"""
        return [
            {
                'id': 1,
                'name': 'إنشاء أمر تسليم تلقائي',
                'description': 'عند وصول الشحنة للميناء يتم إنشاء أمر التسليم تلقائياً',
                'status': 'active',
                'success_count': 45,
                'failure_count': 2,
                'last_executed': datetime.now().strftime('%Y-%m-%d %H:%M')
            },
            {
                'id': 2,
                'name': 'اختيار المخلص الأمثل',
                'description': 'يتم اختيار المخلص تلقائياً بناءً على التخصص والتقييم',
                'status': 'active',
                'success_count': 38,
                'failure_count': 1,
                'last_executed': (datetime.now() - timedelta(minutes=5)).strftime('%Y-%m-%d %H:%M')
            },
            {
                'id': 3,
                'name': 'إرسال إشعارات تلقائية',
                'description': 'إرسال إشعارات للعملاء عند تحديث حالة الشحنة',
                'status': 'active',
                'success_count': 67,
                'failure_count': 3,
                'last_executed': (datetime.now() - timedelta(minutes=15)).strftime('%Y-%m-%d %H:%M')
            },
            {
                'id': 4,
                'name': 'تحديث تقييم المخلصين',
                'description': 'تحديث تقييم المخلصين تلقائياً بناءً على الأداء',
                'status': 'active',
                'success_count': 23,
                'failure_count': 1,
                'last_executed': (datetime.now() - timedelta(hours=2)).strftime('%Y-%m-%d %H:%M')
            }
        ]
    
    def _get_real_activities(self):
        """جلب الأنشطة الحقيقية من سجل تنفيذ الأتمتة"""
        try:
            query = """
                SELECT
                    ael.rule_id,
                    ar.rule_name,
                    ael.execution_status,
                    ael.execution_result,
                    ael.executed_at,
                    ael.shipment_id
                FROM automation_execution_log ael
                LEFT JOIN automation_rules ar ON ael.rule_id = ar.id
                ORDER BY ael.executed_at DESC
                FETCH FIRST 10 ROWS ONLY
            """

            results = self.db_manager.execute_query(query)
            
            activities = []
            for row in results:
                time_ago = self._calculate_time_ago(row[4])  # executed_at
                icon_type = self._get_activity_icon_type(row[2])  # execution_status

                # تكوين وصف النشاط
                rule_name = row[1] or f"قاعدة {row[0]}"
                status_text = "نجح" if row[2] == 'SUCCESS' else "فشل" if row[2] == 'FAILED' else "قيد التنفيذ"
                description = f"{rule_name} - شحنة {row[5]} - {status_text}"

                if row[3]:  # execution_result
                    description += f": {row[3]}"

                activities.append({
                    'action': row[2],  # execution_status
                    'description': description,
                    'time_ago': time_ago,
                    'icon_type': icon_type,
                    'status': row[2]  # execution_status
                })
            
            return activities
            
        except Exception as e:
            logger.error(f"Error getting real activities: {e}")
            # أنشطة افتراضية
            return [
                {
                    'action': 'AUTO_CREATE_ORDER',
                    'description': 'تم إنشاء أمر تسليم تلقائي',
                    'time_ago': 'منذ 5 دقائق',
                    'icon_type': 'success',
                    'status': 'SUCCESS'
                },
                {
                    'action': 'ASSIGN_AGENT',
                    'description': 'تم تعيين مخلص تلقائياً',
                    'time_ago': 'منذ 12 دقيقة',
                    'icon_type': 'primary',
                    'status': 'SUCCESS'
                }
            ]
    
    def _get_real_settings(self):
        """جلب الإعدادات الحقيقية"""
        try:
            query = """
                SELECT setting_key, setting_value 
                FROM automation_settings 
                WHERE is_active = 1
            """
            
            results = self.db_manager.execute_query(query)
            
            settings = {}
            for row in results:
                settings[row[0]] = row[1] == '1'
            
            return {
                'auto_create_orders': settings.get('auto_create_orders', True),
                'auto_assign_agents': settings.get('auto_assign_agents', True),
                'auto_send_notifications': settings.get('auto_send_notifications', True),
                'auto_update_ratings': settings.get('auto_update_ratings', True)
            }
            
        except Exception as e:
            logger.error(f"Error getting real settings: {e}")
            return {
                'auto_create_orders': True,
                'auto_assign_agents': True,
                'auto_send_notifications': True,
                'auto_update_ratings': True
            }
    
    def _get_rule_description(self, action_type):
        """الحصول على وصف القاعدة"""
        descriptions = {
            'CREATE_DELIVERY_ORDER_WITH_AGENT': 'عند وصول الشحنة للميناء يتم إنشاء أمر التسليم وتعيين مخلص تلقائياً',
            'AUTO_DELIVERY_WITH_AGENT': 'إنشاء أمر تسليم وتعيين مخلص عند جاهزية الشحنة',
            'SEND_NOTIFICATION': 'إرسال إشعارات فورية عند تغيير حالة الأمر',
            'UPDATE_RATINGS': 'تحديث تقييمات المخلصين تلقائياً بناءً على الأداء',
            'UPDATE_STATUS': 'تحديث حالة الشحنة تلقائياً حسب المعايير المحددة',
            'GENERATE_REPORT': 'إنشاء تقارير دورية عن أداء الشحنات والمخلصين',
            'SCHEDULE_TASK': 'جدولة مهام متكررة حسب الجدول الزمني المحدد',
            'NONE': 'قاعدة أتمتة مخصصة'
        }
        return descriptions.get(action_type, 'قاعدة أتمتة')

    def _get_condition_description(self, trigger_condition):
        """توليد وصف للشرط بناءً على نوع الشرط"""
        descriptions = {
            'STATUS_EQUALS': 'عندما تكون الحالة مساوية لقيمة محددة',
            'STATUS_NOT_EQUALS': 'عندما تكون الحالة غير مساوية لقيمة محددة',
            'DATE_BEFORE': 'عندما يكون التاريخ قبل تاريخ محدد',
            'DATE_AFTER': 'عندما يكون التاريخ بعد تاريخ محدد',
            'AMOUNT_GREATER': 'عندما يكون المبلغ أكبر من قيمة محددة',
            'AMOUNT_LESS': 'عندما يكون المبلغ أقل من قيمة محددة',
            'AGENT_RATING': 'عندما يكون تقييم المخلص ضمن نطاق محدد',
            'CONTAINER_TYPE': 'عندما يكون نوع الحاوية مطابق لنوع محدد',
            'NONE': 'شرط مخصص'
        }
        return descriptions.get(trigger_condition, 'شرط مخصص')

    def _get_action_description(self, action_type):
        """توليد وصف للإجراء بناءً على نوع الإجراء"""
        descriptions = {
            'CREATE_DELIVERY_ORDER_WITH_AGENT': 'إنشاء أمر تسليم وتعيين مخلص',
            'AUTO_DELIVERY_WITH_AGENT': 'إنشاء أمر تسليم وتعيين مخلص تلقائياً',
            'SEND_NOTIFICATION': 'إرسال إشعار',
            'UPDATE_STATUS': 'تحديث حالة الشحنة',
            'GENERATE_REPORT': 'إنشاء تقرير',
            'UPDATE_RATINGS': 'تحديث التقييمات',
            'SCHEDULE_TASK': 'جدولة مهمة',
            'NONE': 'إجراء مخصص'
        }
        return descriptions.get(action_type, 'إجراء مخصص')
    
    def _get_activity_icon_type(self, status):
        """تحديد نوع أيقونة النشاط حسب حالة التنفيذ"""
        icon_types = {
            'SUCCESS': 'success',
            'FAILED': 'danger',
            'PENDING': 'warning',
            'IN_PROGRESS': 'primary',
            # للتوافق مع الأنواع القديمة
            'AUTO_CREATE_ORDER': 'success',
            'ASSIGN_AGENT': 'primary',
            'SEND_NOTIFICATION': 'warning',
            'UPDATE_RATINGS': 'info'
        }
        return icon_types.get(status, 'primary')
    
    def _calculate_time_ago(self, action_date):
        """حساب الوقت النسبي"""
        try:
            now = datetime.now()
            diff = now - action_date
            
            if diff.days > 0:
                return f'منذ {diff.days} يوم'
            elif diff.seconds > 3600:
                hours = diff.seconds // 3600
                return f'منذ {hours} ساعة'
            elif diff.seconds > 60:
                minutes = diff.seconds // 60
                return f'منذ {minutes} دقيقة'
            else:
                return 'منذ لحظات'
        except:
            return 'غير محدد'
    
    def update_setting(self, setting_key, value):
        """تحديث إعداد في قاعدة البيانات"""
        try:
            # تحويل القيمة المنطقية إلى نص
            str_value = '1' if value else '0'

            # محاولة تحديث الإعداد
            update_query = """
                UPDATE automation_settings
                SET setting_value = :value, updated_at = SYSDATE
                WHERE setting_key = :key
            """

            result = self.db_manager.execute_update(update_query, {
                'key': setting_key,
                'value': str_value
            })

            # إذا لم يتم العثور على الإعداد، أنشئه
            if result == 0:
                insert_query = """
                    INSERT INTO automation_settings
                    (id, setting_key, setting_value, description, created_at, updated_at)
                    VALUES (automation_settings_seq.NEXTVAL, :key, :value, :desc, SYSDATE, SYSDATE)
                """

                descriptions = {
                    'auto_create_orders': 'إنشاء أوامر التسليم تلقائياً',
                    'auto_assign_agents': 'تعيين المخلصين تلقائياً',
                    'auto_send_notifications': 'إرسال الإشعارات تلقائياً',
                    'auto_update_ratings': 'تحديث تقييمات المخلصين تلقائياً'
                }

                self.db_manager.execute_update(insert_query, {
                    'key': setting_key,
                    'value': str_value,
                    'desc': descriptions.get(setting_key, 'إعداد أتمتة')
                })

            return True

        except Exception as e:
            logger.error(f"Error updating setting {setting_key}: {e}")
            return False

    def get_detailed_settings(self):
        """جلب إعدادات مفصلة للصفحة"""
        try:
            query = """
                SELECT setting_key, setting_value, description, updated_at
                FROM automation_settings
                WHERE is_active = 1
                ORDER BY setting_key
            """

            results = self.db_manager.execute_query(query)

            settings = []
            for row in results:
                settings.append({
                    'key': row[0],
                    'value': row[1] == '1',
                    'description': row[2],
                    'updated_at': row[3].strftime('%Y-%m-%d %H:%M') if row[3] else 'غير محدد'
                })

            return settings

        except Exception as e:
            logger.error(f"Error getting detailed settings: {e}")
            return []

    def save_new_rule(self, rule_data):
        """حفظ قاعدة أتمتة جديدة"""
        try:
            insert_query = """
                INSERT INTO automation_rules (
                    id, rule_name, rule_type, trigger_condition,
                    action_type, rule_config, is_active,
                    created_at, updated_at
                ) VALUES (
                    automation_rules_seq.NEXTVAL, :rule_name, :rule_type,
                    :trigger_condition, :action_type, :rule_config,
                    :is_active, SYSDATE, SYSDATE
                )
            """

            # إنشاء تكوين القاعدة
            rule_config = {
                'description': rule_data.get('description', ''),
                'created_by': 'user',
                'auto_generated': False
            }

            params = {
                'rule_name': rule_data['rule_name'],
                'rule_type': rule_data['rule_type'],
                'trigger_condition': rule_data['trigger_condition'],
                'action_type': rule_data['action_type'],
                'rule_config': str(rule_config),
                'is_active': 1 if rule_data.get('is_active', True) else 0
            }

            self.db_manager.execute_update(insert_query, params)
            return True

        except Exception as e:
            logger.error(f"Error saving new rule: {e}")
            return False

    def get_rule_by_id(self, rule_id):
        """جلب قاعدة أتمتة بالمعرف"""
        try:
            # أولاً محاولة جلب من قاعدة البيانات مع بيانات المخلص
            query = """
                SELECT ar.id, ar.rule_name, ar.rule_type, ar.trigger_condition,
                       ar.action_type, ar.is_active, ar.priority_level,
                       ar.success_count, ar.failure_count, ar.last_executed,
                       ar.selected_agent_id, ar.agent_selection_criteria,
                       ar.agent_branch_id, ar.agent_port_id, ar.auto_agent_selection,
                       ar.agent_assignment_notes,
                       ca.agent_name, ca.agent_code,
                       b.brn_lname as branch_name,
                       cp.port_name_ar as port_name
                FROM automation_rules ar
                LEFT JOIN customs_agents ca ON ar.selected_agent_id = ca.id
                LEFT JOIN branches b ON ar.agent_branch_id = b.brn_no
                LEFT JOIN customs_ports cp ON ar.agent_port_id = cp.id
                WHERE ar.id = :rule_id
            """

            result = self.db_manager.execute_query(query, {'rule_id': rule_id})

            if result:
                row = result[0]
                return {
                    'id': row[0],
                    'rule_name': row[1],
                    'rule_type': row[2],
                    'trigger_condition': row[3],
                    'action_type': row[4],
                    'description': self._get_rule_description(row[4]),
                    'is_active': bool(row[5]),
                    'priority_level': row[6],
                    'success_count': row[7] or 0,
                    'failure_count': row[8] or 0,
                    'last_executed': row[9],
                    # بيانات المخلص الجديدة
                    'selected_agent_id': row[10],
                    'agent_selection_criteria': row[11],
                    'agent_branch_id': row[12],
                    'agent_port_id': row[13],
                    'auto_agent_selection': bool(row[14]) if row[14] is not None else False,
                    'agent_assignment_notes': row[15],
                    'agent_name': row[16],
                    'agent_code': row[17],
                    'branch_name': row[18],
                    'port_name': row[19]
                }

            # إذا لم توجد في قاعدة البيانات، جلب من القواعد الافتراضية
            default_rules = self._get_default_rules()
            for rule in default_rules:
                if rule['id'] == rule_id:
                    return {
                        'id': rule['id'],
                        'rule_name': rule['name'],
                        'rule_type': 'ACTION',
                        'trigger_condition': 'SHIPMENT_ARRIVAL',
                        'action_type': self._get_action_type_from_name(rule['name']),
                        'description': rule['description'],
                        'is_active': rule['status'] == 'active',
                        'priority_level': 1,
                        'success_count': rule['success_count'],
                        'failure_count': rule['failure_count'],
                        'last_executed': rule['last_executed']
                    }

            return None

        except Exception as e:
            logger.error(f"Error getting rule by id {rule_id}: {e}")
            return None

    def _get_action_type_from_name(self, rule_name):
        """تحديد نوع الإجراء من اسم القاعدة"""
        if 'إنشاء أمر تسليم' in rule_name:
            return 'CREATE_DELIVERY_ORDER'
        elif 'اختيار المخلص' in rule_name:
            return 'ASSIGN_AGENT'
        elif 'إرسال إشعارات' in rule_name:
            return 'SEND_NOTIFICATION'
        elif 'تحديث تقييم' in rule_name:
            return 'UPDATE_RATINGS'
        else:
            return 'CREATE_DELIVERY_ORDER'

    def update_rule(self, rule_id, rule_data):
        """تحديث قاعدة أتمتة"""
        try:
            # التحقق من وجود القاعدة في قاعدة البيانات
            check_query = "SELECT COUNT(*) FROM automation_rules WHERE id = :rule_id"
            check_result = self.db_manager.execute_query(check_query, {'rule_id': rule_id})

            if check_result and check_result[0][0] > 0:
                # تحديث قاعدة موجودة مع حقول المخلص
                query = """
                    UPDATE automation_rules
                    SET rule_name = :rule_name,
                        rule_type = :rule_type,
                        trigger_condition = :trigger_condition,
                        action_type = :action_type,
                        is_active = :is_active,
                        updated_at = SYSDATE,
                        selected_agent_id = :selected_agent_id,
                        agent_selection_criteria = :agent_selection_criteria,
                        agent_branch_id = :agent_branch_id,
                        agent_port_id = :agent_port_id,
                        auto_agent_selection = :auto_agent_selection,
                        agent_assignment_notes = :agent_assignment_notes,
                        agent_settings_updated_at = SYSDATE,
                        agent_settings_updated_by = :user_id
                    WHERE id = :rule_id
                """

                params = {
                    'rule_id': rule_id,
                    'rule_name': rule_data['rule_name'],
                    'rule_type': rule_data['rule_type'],
                    'trigger_condition': rule_data['trigger_condition'],
                    'action_type': rule_data['action_type'],
                    'is_active': 1 if rule_data['is_active'] else 0,
                    'selected_agent_id': rule_data.get('selected_agent_id'),
                    'agent_selection_criteria': rule_data.get('agent_selection_criteria', 'rating'),
                    'agent_branch_id': rule_data.get('agent_branch_id'),
                    'agent_port_id': rule_data.get('agent_port_id'),
                    'auto_agent_selection': rule_data.get('auto_agent_selection', 0),
                    'agent_assignment_notes': rule_data.get('agent_assignment_notes', ''),
                    'user_id': 1  # يمكن تحديثه لاحقاً لاستخدام current_user.id
                }

                result = self.db_manager.execute_query(query, params)

                if result is not None:
                    logger.info(f"Automation rule {rule_id} updated successfully")
                    return True
            else:
                # إنشاء قاعدة جديدة للقواعد الافتراضية
                insert_query = """
                    INSERT INTO automation_rules
                    (id, rule_name, rule_type, trigger_condition, action_type,
                     is_active, priority_level, created_date, updated_date)
                    VALUES (:rule_id, :rule_name, :rule_type, :trigger_condition,
                            :action_type, :is_active, 1, SYSDATE, SYSDATE)
                """

                params = {
                    'rule_id': rule_id,
                    'rule_name': rule_data['rule_name'],
                    'rule_type': rule_data['rule_type'],
                    'trigger_condition': rule_data['trigger_condition'],
                    'action_type': rule_data['action_type'],
                    'is_active': 1 if rule_data['is_active'] else 0
                }

                result = self.db_manager.execute_query(insert_query, params)

                if result is not None:
                    logger.info(f"Automation rule {rule_id} created successfully")
                    return True

            return False

        except Exception as e:
            logger.error(f"Error updating rule {rule_id}: {e}")
            return False

    def delete_rule(self, rule_id):
        """حذف قاعدة أتمتة"""
        try:
            # التحقق من وجود القاعدة أولاً
            check_query = "SELECT COUNT(*) FROM automation_rules WHERE id = :rule_id"
            check_result = self.db_manager.execute_query(check_query, {'rule_id': rule_id})

            if not check_result or check_result[0][0] == 0:
                logger.warning(f"Rule {rule_id} not found in database")
                return False

            # حذف القاعدة مباشرة (لا نحتاج لحذف سجلات automation_log لأنها لا تحتوي على rule_id)
            delete_rule_query = "DELETE FROM automation_rules WHERE id = :rule_id"
            rows_affected = self.db_manager.execute_update(delete_rule_query, {'rule_id': rule_id})

            if rows_affected and rows_affected > 0:
                logger.info(f"Automation rule {rule_id} deleted successfully")
                return True
            else:
                logger.warning(f"No rows affected when deleting rule {rule_id}")
                return False

        except Exception as e:
            logger.error(f"Error deleting rule {rule_id}: {e}")
            return False

    def _get_fallback_data(self):
        """بيانات احتياطية في حالة الخطأ"""
        return {
            'statistics': {
                'total_automated_orders': 0,
                'success_rate': 0.0,
                'active_rules': 0,
                'processed_today': 0
            },
            'rules': [],
            'activities': [],
            'settings': {
                'auto_create_orders': True,
                'auto_assign_agents': True,
                'auto_send_notifications': True,
                'auto_update_ratings': True
            }
        }

# إنشاء مثيل عام
automation_data_provider = AutomationDataProvider()
