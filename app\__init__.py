# -*- coding: utf-8 -*-
"""
النظام المحاسبي المتقدم
Advanced Accounting System Package
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_wtf.csrf import CSRFProtect
from flask_compress import Compress
from flask_caching import Cache
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_mail import Mail
from flask_moment import Moment
from flask_socketio import Socket<PERSON>
from flask_assets import Environment
from config import config
# # from oracle_jdbc import OracleJDBCManager  # استخدام oracledb بدلاً من JDBC  # معطل مؤقتاً
from app.netsuite_assets import NetSuiteAssetsManager, register_template_helpers
import os

# تهيئة الإضافات
db = SQLAlchemy()
# oracle_db = OracleJDBCManager()  # مدير Oracle JDBC - معطل مؤقتاً
login_manager = LoginManager()
csrf = CSRFProtect()
compress = Compress()
cache = Cache()
mail = Mail()
moment = Moment()
socketio = SocketIO()
assets = Environment()
netsuite_assets = NetSuiteAssetsManager()

def create_app(config_name=None):
    """إنشاء تطبيق Flask"""
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # تهيئة الإضافات
    # تعطيل SQLAlchemy عند استخدام Oracle
    if 'oracle' not in config_name.lower():
        db.init_app(app)

    # تهيئة Oracle Database باستخدام oracledb
    if 'oracle' in config_name.lower():
        app.logger.info("🔧 تهيئة Oracle Database...")
        try:
            import oracledb
            app.logger.info("✅ تم تحميل oracledb بنجاح")
        except ImportError:
            app.logger.error("❌ فشل في تحميل oracledb")

    login_manager.init_app(app)
    # csrf.init_app(app)  # معطل مؤقتاً للاختبار

    # تهيئة مكتبات الأجهزة المحمولة
    compress.init_app(app)
    cache.init_app(app)
    CORS(app)
    mail.init_app(app)
    moment.init_app(app)
    socketio.init_app(app, cors_allowed_origins="*")

    # تهيئة نظام الأصول
    assets.init_app(app)
    from .assets import init_assets
    init_assets(app)

    # تهيئة مدير أصول NetSuite المتقدم
    netsuite_assets.init_app(app)
    register_template_helpers(app)

    # تهيئة Rate Limiting مع storage backend
    limiter = Limiter(
        key_func=get_remote_address,
        default_limits=["200 per day", "50 per hour"],
        storage_uri="memory://"  # استخدام memory storage صراحة لإزالة التحذير
    )
    limiter.init_app(app)
    
    # إعدادات تسجيل الدخول
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'

    # معالج تحميل المستخدم
    @login_manager.user_loader
    def load_user(user_id):
        if 'oracle' in config_name.lower():
            # استخدام Oracle Database
            try:
                from oracle_manager import get_oracle_manager
                oracle_mgr = get_oracle_manager()

                # التحقق من نوع المستخدم
                if str(user_id).startswith('agent_'):
                    # مستخدم مخلص
                    agent_id = str(user_id).replace('agent_', '')
                    query = "SELECT id, agent_code, agent_name, email, phone FROM customs_agents WHERE id = :1 AND is_active = 1"
                    result = oracle_mgr.execute_query(query, [int(agent_id)])

                    if result:
                        # إنشاء agent user object
                        from flask_login import UserMixin

                        class AgentUser(UserMixin):
                            def __init__(self, agent_data):
                                self.id = f"agent_{agent_data[0]}"
                                self.agent_id = agent_data[0]
                                self.username = agent_data[1]
                                self.agent_code = agent_data[1]
                                self.full_name = agent_data[2]
                                self.email = agent_data[3] if agent_data[3] else ''
                                self.phone = agent_data[4] if agent_data[4] else ''
                                self.is_admin = False
                                self.user_type = 'agent'

                            def get_id(self):
                                return self.id

                        agent_data = result[0]
                        return AgentUser(agent_data)
                else:
                    # مستخدم عادي
                    query = "SELECT id, username, email, full_name, is_admin FROM users WHERE id = :1"
                    result = oracle_mgr.execute_query(query, [int(user_id)])

                    if result:
                        # إنشاء user object بسيط
                        from flask_login import UserMixin

                        class SimpleUser(UserMixin):
                            def __init__(self, user_data):
                                self.id = str(user_data[0])  # Flask-Login يحتاج string
                                self.username = str(user_data[1]) if user_data[1] else ''
                                self.email = str(user_data[2]) if user_data[2] else ''
                                self.full_name = str(user_data[3]) if user_data[3] else ''
                                self.user_type = 'user'
                                # تحويل is_admin بحذر
                                try:
                                    admin_value = user_data[4] if user_data[4] is not None else 0
                                    self.is_admin = bool(int(admin_value))
                                except (ValueError, TypeError):
                                    self.is_admin = False

                            def get_id(self):
                                return self.id

                        user_data = result[0]
                        return SimpleUser(user_data)
            except Exception as e:
                app.logger.error(f"خطأ في تحميل المستخدم من Oracle: {e}")
            return None
        else:
            # استخدام SQLAlchemy
            from app.models import User
            return User.query.get(int(user_id))

    # تسجيل المخططات
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)

    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')

    from app.purchase_requests.routes import purchase_requests_bp
    app.register_blueprint(purchase_requests_bp)

    from app.purchase_orders import bp as purchase_orders_bp
    app.register_blueprint(purchase_orders_bp, url_prefix='/purchase-orders')

    from app.purchase_contracts import bp as purchase_contracts_bp
    app.register_blueprint(purchase_contracts_bp, url_prefix='/purchase-contracts')

    from app.goods_receipt import bp as goods_receipt_bp
    app.register_blueprint(goods_receipt_bp, url_prefix='/goods-receipt')

    from app.inventory import bp as inventory_bp
    app.register_blueprint(inventory_bp, url_prefix='/inventory')

    from app.suppliers import bp as suppliers_bp
    app.register_blueprint(suppliers_bp, url_prefix='/suppliers')

    # تسجيل نظام الأرصدة الافتتاحية للموردين
    try:
        from app.suppliers.opening_balances import opening_balances_bp
        app.register_blueprint(opening_balances_bp)
        app.logger.info("✅ تم تسجيل نظام الأرصدة الافتتاحية للموردين")
    except Exception as e:
        app.logger.error(f"❌ خطأ في تسجيل نظام الأرصدة الافتتاحية: {e}")

    # تسجيل النظام المركزي للأرصدة والمعاملات
    try:
        from app.central_balances import central_balances_bp
        app.register_blueprint(central_balances_bp)
        app.logger.info("✅ تم تسجيل النظام المركزي للأرصدة والمعاملات")
    except Exception as e:
        app.logger.error(f"❌ خطأ في تسجيل النظام المركزي للأرصدة: {e}")

    from app.contracts import bp as contracts_bp
    app.register_blueprint(contracts_bp, url_prefix='/contracts')

    # تسجيل blueprint التحميل المشترك
    from app.shared_download_routes import shared_download_bp
    app.register_blueprint(shared_download_bp)

    # تسجيل blueprint وثائق الشحنات
    from app.shipments.cargo_document_routes import cargo_documents_bp
    app.register_blueprint(cargo_documents_bp)

    # إضافة route لخدمة الملفات المرفوعة
    @app.route('/uploads/<filename>')
    def uploaded_file(filename):
        from flask import send_from_directory
        upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
        return send_from_directory(upload_folder, filename)



    from app.financial import bp as financial_bp
    app.register_blueprint(financial_bp, url_prefix='/financial')

    from app.reports import bp as reports_bp
    app.register_blueprint(reports_bp, url_prefix='/reports')

    from app.workflow import bp as workflow_bp
    app.register_blueprint(workflow_bp, url_prefix='/workflow')

    # تسجيل وحدة التحكم الصوتي الذكي
    from app.ai_voice import create_voice_blueprint
    voice_bp = create_voice_blueprint()
    app.register_blueprint(voice_bp)

    # تسجيل وحدة الإعدادات والتهيئة
    from app.settings import bp as settings_bp
    app.register_blueprint(settings_bp, url_prefix='/settings')

    # تسجيل وحدة إدارة الخادم والنشر
    from app.admin.server_management import server_bp
    app.register_blueprint(server_bp, url_prefix='/admin')

    # تسجيل وحدة إدارة العملات
    from app.currencies import bp as currencies_bp
    app.register_blueprint(currencies_bp, url_prefix='/currencies')

    # تسجيل وحدة إعدادات المشتريات
    from app.purchase_settings.routes import purchase_settings
    app.register_blueprint(purchase_settings)

    # تسجيل نظام عمولات مندوبي المشتريات
    try:
        from app.purchase_commissions import purchase_commissions_bp
        app.register_blueprint(purchase_commissions_bp)
        app.logger.info("✅ تم تسجيل نظام عمولات مندوبي المشتريات")
    except Exception as e:
        app.logger.error(f"❌ خطأ في تسجيل نظام عمولات المندوبين: {e}")

    # تسجيل نظام إدارة الشحنات والتتبع المتطور
    from app.shipments import shipments_bp
    app.register_blueprint(shipments_bp, url_prefix='/shipments')

    # تسجيل API خدمات PDF
    from app.routes.pdf_api import pdf_api
    app.register_blueprint(pdf_api)

    # تسجيل API الأتمتة الاحترافية
    from app.routes.professional_automation_api import professional_automation_api
    app.register_blueprint(professional_automation_api)

    # تسجيل نظام البريد الإلكتروني المتكامل
    from app.email import bp as email_bp
    app.register_blueprint(email_bp, url_prefix='/email')

    # تسجيل نظام إدارة جهات الاتصال للإشعارات
    try:
        from app.notifications.contacts_routes import contacts_bp
        app.register_blueprint(contacts_bp)
        app.logger.info("✅ تم تسجيل نظام إدارة جهات الاتصال للإشعارات")
    except Exception as e:
        app.logger.error(f"❌ خطأ في تسجيل نظام جهات الاتصال: {e}")

    # تسجيل نظام الإشعارات الفورية الجديد
    try:
        from app.instant_notifications import instant_notifications_bp
        app.register_blueprint(instant_notifications_bp)
        app.logger.info("✅ تم تسجيل نظام الإشعارات الفورية الجديد")
    except Exception as e:
        app.logger.error(f"❌ خطأ في تسجيل نظام الإشعارات الفورية: {e}")

    # تسجيل نظام الحوالات المتكامل
    try:
        from app.transfers import transfers_bp
        app.register_blueprint(transfers_bp)
        app.logger.info("✅ تم تسجيل نظام الحوالات المتكامل")

        # تسجيل نظام الترحيل المحاسبي للحوالات
        from app.transfers.accounting_routes import accounting_bp
        app.register_blueprint(accounting_bp)
        app.logger.info("✅ تم تسجيل نظام الترحيل المحاسبي للحوالات")
    except Exception as e:
        app.logger.error(f"❌ خطأ في تسجيل نظام الحوالات: {e}")

    # تسجيل المرحلة الثانية المتقدمة
    try:
        # بوابة المخلص الإلكترونية
        from app.shipments.agent_portal import agent_portal_bp
        app.register_blueprint(agent_portal_bp)

        # التحليلات المتقدمة
        from app.shipments.advanced_analytics import analytics_bp
        app.register_blueprint(analytics_bp)

        # تكامل المرحلة الثانية
        from app.shipments.phase2_integration import phase2_bp
        app.register_blueprint(phase2_bp)

        app.logger.info("✅ تم تحميل المرحلة الثانية المتقدمة بنجاح")
    except ImportError as e:
        app.logger.warning(f"⚠️ لم يتم تحميل بعض مكونات المرحلة الثانية: {e}")
    except Exception as e:
        app.logger.error(f"❌ خطأ في تحميل المرحلة الثانية: {e}")

    # تسجيل نظام التحليلات المحاسبية
    try:
        from app.analytics import bp as accounting_analytics_bp
        app.register_blueprint(accounting_analytics_bp)
        app.logger.info("✅ تم تحميل نظام التحليلات المحاسبية بنجاح")
    except ImportError as e:
        app.logger.warning(f"⚠️ لم يتم تحميل نظام التحليلات المحاسبية: {e}")
    except Exception as e:
        app.logger.error(f"❌ خطأ في تحميل نظام التحليلات المحاسبية: {e}")

    # معالج اللغة
    @app.before_request
    def before_request():
        """معالجة ما قبل الطلب"""
        from flask import session
        # تحديد اللغة
        if 'language' not in session:
            session['language'] = app.config.get('DEFAULT_LANGUAGE', 'ar')

        # تحديد اتجاه النص
        if session.get('language') == 'ar':
            session['text_direction'] = 'rtl'
        else:
            session['text_direction'] = 'ltr'

    # معالج السياق العام
    @app.context_processor
    def inject_conf_vars():
        """حقن متغيرات السياق العامة"""
        from flask import session
        from flask_login import current_user
        from app.models import UserSettings

        # الحصول على إعدادات المستخدم إذا كان مسجل الدخول
        user_settings = None
        if current_user.is_authenticated:
            # استخدام Oracle Manager للحصول على إعدادات المستخدم
            try:
                if 'oracle' in config_name.lower():
                    from oracle_manager import get_oracle_manager
                    oracle_mgr = get_oracle_manager()

                    # التحقق من نوع المستخدم
                    if hasattr(current_user, 'user_type') and current_user.user_type == 'agent':
                        # المخلصين لا يحتاجون إعدادات مستخدم، استخدام الافتراضية
                        class SimpleUserSettings:
                            def __init__(self):
                                self.language = 'ar'
                                self.currency = 'SAR'
                                self.timezone = 'Asia/Riyadh'
                                self.date_format = 'DD/MM/YYYY'
                                self.theme = 'light'
                                self.items_per_page = 25
                        user_settings = SimpleUserSettings()
                    else:
                        # مستخدم عادي
                        query = "SELECT language, currency, timezone, date_format, theme, items_per_page FROM user_settings WHERE user_id = :1"
                        result = oracle_mgr.execute_query(query, [current_user.id])
                        if result:
                            settings_data = result[0]
                            # إنشاء object بسيط للإعدادات
                            class SimpleUserSettings:
                                def __init__(self, data):
                                    self.language = data[0] if data[0] else 'ar'
                                    self.currency = data[1] if data[1] else 'SAR'
                                    self.timezone = data[2] if data[2] else 'Asia/Riyadh'
                                    self.date_format = data[3] if data[3] else 'DD/MM/YYYY'
                                    self.theme = data[4] if data[4] else 'light'
                                    self.items_per_page = data[5] if data[5] else 25
                            user_settings = SimpleUserSettings(settings_data)
                else:
                    # استخدام SQLAlchemy للبيئات الأخرى
                    user_settings = db.session.query(UserSettings).filter_by(user_id=current_user.id).first()
            except Exception as e:
                app.logger.error(f"خطأ في جلب إعدادات المستخدم: {e}")
                user_settings = None

        # تحديد اللغة والعملة حسب إعدادات المستخدم أو الافتراضية
        current_language = 'ar'
        current_currency = 'SAR'
        currency_symbol = 'ر.س'
        currency_object = None

        if user_settings:
            current_language = user_settings.language
            current_currency = user_settings.currency

            # البحث عن العملة في قاعدة البيانات مع معالجة الأخطاء
            try:
                from app.models import Currency
                currency_object = Currency.query.filter_by(code=current_currency, is_active=1).first()

                if currency_object:
                    currency_symbol = currency_object.symbol
                else:
                    # العودة للعملة الأساسية إذا لم توجد العملة
                    base_currency = Currency.get_base_currency()
                    if base_currency:
                        current_currency = base_currency.code
                        currency_symbol = base_currency.symbol
                        currency_object = base_currency
                    else:
                        # قيم افتراضية إذا لم توجد عملة أساسية
                        current_currency = 'SAR'
                        currency_symbol = 'ر.س'
            except Exception:
                # في حالة عدم توفر جدول العملات أو خطأ في قاعدة البيانات
                if current_currency == 'USD':
                    currency_symbol = '$'
                elif current_currency == 'EUR':
                    currency_symbol = '€'
                elif current_currency == 'AED':
                    currency_symbol = 'د.إ'
                else:
                    currency_symbol = 'ر.س'
        else:
            # للمستخدمين غير المسجلين، استخدام العملة الأساسية
            try:
                from app.models import Currency
                base_currency = Currency.get_base_currency()
                if base_currency:
                    current_currency = base_currency.code
                    currency_symbol = base_currency.symbol
                    currency_object = base_currency
            except Exception:
                # قيم افتراضية في حالة عدم توفر جدول العملات
                current_currency = 'SAR'
                currency_symbol = 'ر.س'

        return {
            'LANGUAGES': app.config.get('LANGUAGES', {'ar': 'العربية', 'en': 'English'}),
            'CURRENT_LANGUAGE': current_language,
            'TEXT_DIRECTION': 'rtl' if current_language == 'ar' else 'ltr',
            'CURRENCY_SYMBOL': currency_symbol,
            'DEFAULT_CURRENCY': current_currency,
            'CURRENCY_OBJECT': currency_object,
            'USER_SETTINGS': user_settings
        }

    # دالة مساعدة لتنسيق العملات في القوالب
    @app.template_filter('format_currency')
    def format_currency_filter(amount, currency_code=None):
        """تنسيق المبلغ حسب إعدادات العملة"""
        if amount is None:
            return "0"

        try:
            # تنسيق بسيط مع رمز العملة
            formatted = f"{float(amount):,.2f}"

            # إضافة رمز العملة حسب الكود
            if currency_code:
                if currency_code == 'USD':
                    return f"$ {formatted}"
                elif currency_code == 'EUR':
                    return f"€ {formatted}"
                elif currency_code == 'AED':
                    return f"{formatted} د.إ"
                elif currency_code == 'SAR':
                    return f"{formatted} ر.س"
                else:
                    return f"{formatted} {currency_code}"
            else:
                return f"{formatted} ر.س"

        except Exception:
            # في حالة الخطأ، إرجاع تنسيق بسيط
            return str(amount)

    # دالة مساعدة لتحويل العملات في القوالب
    @app.template_filter('convert_currency')
    def convert_currency_filter(amount, from_currency, to_currency):
        """تحويل مبلغ من عملة إلى أخرى"""
        if amount is None:
            return 0

        try:
            # تحويل بسيط باستخدام أسعار ثابتة مؤقتاً
            if from_currency == to_currency:
                return amount

            # أسعار صرف مؤقتة (يجب استبدالها بقاعدة البيانات لاحقاً)
            rates = {
                'SAR': 1.0,
                'USD': 3.75,
                'EUR': 4.1,
                'AED': 1.02
            }

            if from_currency in rates and to_currency in rates:
                # تحويل عبر الريال السعودي كعملة أساسية
                sar_amount = float(amount) / rates[from_currency]
                return sar_amount * rates[to_currency]
            else:
                return amount

        except Exception:
            return amount

    # معالج الأخطاء
    @app.errorhandler(404)
    def not_found_error(error):
        """معالج خطأ 404"""
        from flask import render_template
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_error(error):
        """معالج خطأ 500"""
        from flask import render_template
        db.session.rollback()
        return render_template('errors/500.html'), 500

    # تشغيل الأتمتة تلقائياً عند بدء الخادم مع إعادة المحاولة
    def start_automation_with_retry():
        """بدء الأتمتة مع إعادة المحاولة"""
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                from app.services.flask_automation_service import flask_automation_service

                # التأكد من الإعدادات المحسنة
                enhanced_settings = {
                    'auto_create_orders': True,
                    'auto_send_notifications': True,
                    'check_interval_minutes': 2,  # فحص كل دقيقتين
                    'working_hours_start': '00:00',
                    'working_hours_end': '23:59',
                    'weekend_enabled': True,
                    'max_orders_per_batch': 10,
                    'retry_failed_orders': True,
                    'retry_attempts': 5,
                    'always_enabled_for_testing': True
                }

                flask_automation_service.update_settings(enhanced_settings)

                if not flask_automation_service.running:
                    success = flask_automation_service.start_service()
                    if success:
                        app.logger.info("🤖 تم تشغيل الأتمتة تلقائياً!")
                        return True
                    else:
                        app.logger.warning(f"⚠️ فشل في تشغيل الأتمتة - المحاولة {attempt + 1}")
                else:
                    app.logger.info("🤖 الأتمتة تعمل بالفعل!")
                    return True

            except Exception as e:
                app.logger.warning(f"⚠️ خطأ في تشغيل الأتمتة - المحاولة {attempt + 1}: {e}")

            if attempt < max_attempts - 1:
                import time
                time.sleep(2)  # انتظار ثانيتين قبل إعادة المحاولة

        app.logger.error("❌ فشل في تشغيل الأتمتة بعد عدة محاولات")
        return False

    # تشغيل الأتمتة في thread منفصل لتجنب تأخير بدء التطبيق
    import threading
    automation_thread = threading.Thread(target=start_automation_with_retry, daemon=True)
    automation_thread.start()

    # بدء معالج الأحداث الفورية مع التطبيق
    def start_instant_processor_with_retry():
        """بدء معالج الأحداث الفورية مع إعادة المحاولة"""
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                from app.services.instant_event_processor import start_instant_processor
                start_instant_processor()
                app.logger.info("🚀 تم بدء معالج الأحداث الفورية مع التطبيق")
                return True
            except Exception as e:
                app.logger.warning(f"⚠️ خطأ في بدء معالج الأحداث - المحاولة {attempt + 1}: {e}")
                if attempt < max_attempts - 1:
                    import time
                    time.sleep(2)

        app.logger.error("❌ فشل في بدء معالج الأحداث بعد عدة محاولات")
        return False

    # تشغيل معالج الأحداث في thread منفصل
    instant_processor_thread = threading.Thread(target=start_instant_processor_with_retry, daemon=True)
    instant_processor_thread.start()

    return app
