#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
تحديث قيود قاعدة البيانات للنظام المحاسبي الموحد
Update Database Constraints for Unified Accounting System
"""

import sys
import os
sys.path.insert(0, os.getcwd())

from oracle_manager import OracleManager

def check_existing_constraints():
    """فحص القيود الموجودة"""
    
    oracle = OracleManager()
    
    print("🔍 فحص القيود الموجودة على BALANCE_TRANSACTIONS...")
    print("=" * 70)
    
    # فحص القيود الحالية
    constraints_query = """
    SELECT constraint_name, constraint_type, status, validated
    FROM user_constraints 
    WHERE table_name = 'BALANCE_TRANSACTIONS'
    ORDER BY constraint_type, constraint_name
    """
    
    result = oracle.execute_query(constraints_query)
    if result:
        print("القيود الحالية:")
        constraint_types = {
            'P': 'Primary Key',
            'R': 'Foreign Key', 
            'C': 'Check Constraint',
            'U': 'Unique',
            'N': 'Not Null'
        }
        
        for row in result:
            constraint_type = constraint_types.get(row[1], row[1])
            status_icon = "✅" if row[2] == "ENABLED" else "❌"
            print(f"   {status_icon} {row[0]}: {constraint_type} ({row[2]})")
    
    return result

def add_check_constraints():
    """إضافة قيود التحقق للأعمدة الجديدة"""
    
    oracle = OracleManager()
    
    print("\n🔧 إضافة قيود التحقق للأعمدة الجديدة...")
    print("=" * 70)
    
    # قيود التحقق المطلوب إضافتها
    check_constraints = [
        {
            "name": "CHK_BT_MONTH_NO",
            "condition": "MONTH_NO BETWEEN 1 AND 12",
            "description": "التأكد من أن رقم الشهر بين 1 و 12"
        },
        {
            "name": "CHK_BT_YEAR_NO", 
            "condition": "YEAR_NO BETWEEN 1900 AND 2100",
            "description": "التأكد من أن رقم السنة ضمن نطاق معقول"
        },
        {
            "name": "CHK_BT_BRANCH_ID",
            "condition": "BRANCH_ID > 0",
            "description": "التأكد من أن رقم الفرع موجب"
        },
        {
            "name": "CHK_BT_BAL_CONSISTENCY",
            "condition": "(DEBIT_AMOUNT > 0 AND CREDIT_AMOUNT = 0 AND BAL > 0) OR (CREDIT_AMOUNT > 0 AND DEBIT_AMOUNT = 0 AND BAL < 0) OR (DEBIT_AMOUNT = 0 AND CREDIT_AMOUNT = 0 AND BAL = 0)",
            "description": "التأكد من تطابق BAL مع DEBIT_AMOUNT و CREDIT_AMOUNT"
        }
    ]
    
    successful_constraints = 0
    
    for constraint in check_constraints:
        try:
            print(f"\n   إضافة {constraint['name']}...")
            
            # فحص إذا كان القيد موجود
            check_query = """
            SELECT COUNT(*) FROM user_constraints 
            WHERE constraint_name = :name AND table_name = 'BALANCE_TRANSACTIONS'
            """
            exists = oracle.execute_query(check_query, {"name": constraint["name"]})
            
            if exists and exists[0][0] > 0:
                print(f"      ⚠️ القيد {constraint['name']} موجود مسبقاً")
                successful_constraints += 1
                continue
            
            # إضافة القيد
            add_constraint_query = f"""
            ALTER TABLE BALANCE_TRANSACTIONS 
            ADD CONSTRAINT {constraint['name']} 
            CHECK ({constraint['condition']})
            """
            
            oracle.execute_update(add_constraint_query)
            print(f"      ✅ تم إضافة القيد {constraint['name']}")
            print(f"         الوصف: {constraint['description']}")
            successful_constraints += 1
            
        except Exception as e:
            print(f"      ❌ خطأ في إضافة القيد {constraint['name']}: {str(e)}")
    
    print(f"\nملخص إضافة قيود التحقق:")
    print(f"   نجح: {successful_constraints}/{len(check_constraints)}")
    
    return successful_constraints

def add_foreign_key_constraints():
    """إضافة قيود المفاتيح الأجنبية"""
    
    oracle = OracleManager()
    
    print("\n🔗 إضافة قيود المفاتيح الأجنبية...")
    print("=" * 70)
    
    # قيود المفاتيح الأجنبية
    foreign_keys = [
        {
            "name": "FK_BT_BRANCH",
            "column": "BRANCH_ID",
            "ref_table": "BRANCHES",
            "ref_column": "ID",
            "description": "ربط مع جدول الفروع"
        }
    ]
    
    successful_fks = 0
    
    for fk in foreign_keys:
        try:
            print(f"\n   إضافة {fk['name']}...")
            
            # فحص وجود الجدول المرجعي
            table_check_query = """
            SELECT COUNT(*) FROM user_tables WHERE table_name = :table_name
            """
            table_exists = oracle.execute_query(table_check_query, {"table_name": fk["ref_table"]})
            
            if not table_exists or table_exists[0][0] == 0:
                print(f"      ⚠️ الجدول المرجعي {fk['ref_table']} غير موجود - تخطي القيد")
                continue
            
            # فحص إذا كان القيد موجود
            fk_check_query = """
            SELECT COUNT(*) FROM user_constraints 
            WHERE constraint_name = :name AND table_name = 'BALANCE_TRANSACTIONS'
            """
            exists = oracle.execute_query(fk_check_query, {"name": fk["name"]})
            
            if exists and exists[0][0] > 0:
                print(f"      ⚠️ القيد {fk['name']} موجود مسبقاً")
                successful_fks += 1
                continue
            
            # إضافة المفتاح الأجنبي
            add_fk_query = f"""
            ALTER TABLE BALANCE_TRANSACTIONS 
            ADD CONSTRAINT {fk['name']} 
            FOREIGN KEY ({fk['column']}) 
            REFERENCES {fk['ref_table']}({fk['ref_column']})
            """
            
            oracle.execute_update(add_fk_query)
            print(f"      ✅ تم إضافة المفتاح الأجنبي {fk['name']}")
            print(f"         الوصف: {fk['description']}")
            successful_fks += 1
            
        except Exception as e:
            print(f"      ❌ خطأ في إضافة المفتاح الأجنبي {fk['name']}: {str(e)}")
    
    print(f"\nملخص إضافة المفاتيح الأجنبية:")
    print(f"   نجح: {successful_fks}/{len(foreign_keys)}")
    
    return successful_fks

def add_not_null_constraints():
    """إضافة قيود عدم القبول بـ NULL للأعمدة المهمة"""
    
    oracle = OracleManager()
    
    print("\n🚫 تحديث قيود NULL للأعمدة المهمة...")
    print("=" * 70)
    
    # الأعمدة التي يجب أن تكون NOT NULL
    not_null_columns = [
        {
            "column": "MONTH_NO",
            "description": "رقم الشهر مطلوب"
        },
        {
            "column": "YEAR_NO", 
            "description": "رقم السنة مطلوب"
        },
        {
            "column": "BRANCH_ID",
            "description": "رقم الفرع مطلوب"
        }
    ]
    
    successful_updates = 0
    
    for col in not_null_columns:
        try:
            print(f"\n   تحديث {col['column']} إلى NOT NULL...")
            
            # فحص إذا كان العمود يحتوي على NULL
            null_check_query = f"""
            SELECT COUNT(*) FROM BALANCE_TRANSACTIONS 
            WHERE {col['column']} IS NULL
            """
            null_count = oracle.execute_query(null_check_query)
            
            if null_count and null_count[0][0] > 0:
                print(f"      ⚠️ يوجد {null_count[0][0]} سجل بقيم NULL - يجب تحديثها أولاً")
                
                # تحديث القيم NULL
                if col['column'] == 'MONTH_NO':
                    update_query = f"""
                    UPDATE BALANCE_TRANSACTIONS 
                    SET {col['column']} = EXTRACT(MONTH FROM document_date)
                    WHERE {col['column']} IS NULL
                    """
                elif col['column'] == 'YEAR_NO':
                    update_query = f"""
                    UPDATE BALANCE_TRANSACTIONS 
                    SET {col['column']} = EXTRACT(YEAR FROM document_date)
                    WHERE {col['column']} IS NULL
                    """
                elif col['column'] == 'BRANCH_ID':
                    update_query = f"""
                    UPDATE BALANCE_TRANSACTIONS 
                    SET {col['column']} = 1
                    WHERE {col['column']} IS NULL
                    """
                
                oracle.execute_update(update_query)
                print(f"      ✅ تم تحديث القيم NULL")
            
            # إضافة قيد NOT NULL
            alter_query = f"""
            ALTER TABLE BALANCE_TRANSACTIONS 
            MODIFY {col['column']} NOT NULL
            """
            
            oracle.execute_update(alter_query)
            print(f"      ✅ تم تطبيق NOT NULL على {col['column']}")
            print(f"         الوصف: {col['description']}")
            successful_updates += 1
            
        except Exception as e:
            if "already NOT NULL" in str(e) or "cannot be modified" in str(e):
                print(f"      ⚠️ العمود {col['column']} بالفعل NOT NULL")
                successful_updates += 1
            else:
                print(f"      ❌ خطأ في تحديث {col['column']}: {str(e)}")
    
    print(f"\nملخص تحديث قيود NULL:")
    print(f"   نجح: {successful_updates}/{len(not_null_columns)}")
    
    return successful_updates

def verify_constraints():
    """التحقق من القيود المضافة"""
    
    oracle = OracleManager()
    
    print("\n✅ التحقق من القيود المضافة...")
    print("=" * 70)
    
    # فحص جميع القيود
    all_constraints_query = """
    SELECT constraint_name, constraint_type, status, validated
    FROM user_constraints 
    WHERE table_name = 'BALANCE_TRANSACTIONS'
    AND constraint_name LIKE 'CHK_BT_%' OR constraint_name LIKE 'FK_BT_%'
    ORDER BY constraint_name
    """
    
    result = oracle.execute_query(all_constraints_query)
    if result:
        print("القيود الجديدة:")
        constraint_types = {
            'P': 'Primary Key',
            'R': 'Foreign Key', 
            'C': 'Check Constraint',
            'U': 'Unique'
        }
        
        for row in result:
            constraint_type = constraint_types.get(row[1], row[1])
            status_icon = "✅" if row[2] == "ENABLED" else "❌"
            validated_icon = "✅" if row[3] == "VALIDATED" else "⚠️"
            print(f"   {status_icon} {row[0]}: {constraint_type}")
            print(f"      الحالة: {row[2]} | التحقق: {row[3]} {validated_icon}")

def test_constraints():
    """اختبار القيود المضافة"""
    
    oracle = OracleManager()
    
    print("\n🧪 اختبار القيود المضافة...")
    print("=" * 70)
    
    # اختبارات القيود
    constraint_tests = [
        {
            "name": "اختبار MONTH_NO خارج النطاق",
            "query": """
            INSERT INTO BALANCE_TRANSACTIONS (
                entity_type_code, entity_id, document_type_code, document_number,
                document_date, currency_code, month_no, year_no, branch_id
            ) VALUES (
                'SUPPLIER', 999, 'TEST', 'TEST-CONSTRAINT-1',
                SYSDATE, 'USD', 13, 2025, 1
            )
            """,
            "should_fail": True,
            "expected_error": "CHK_BT_MONTH_NO"
        },
        {
            "name": "اختبار YEAR_NO خارج النطاق", 
            "query": """
            INSERT INTO BALANCE_TRANSACTIONS (
                entity_type_code, entity_id, document_type_code, document_number,
                document_date, currency_code, month_no, year_no, branch_id
            ) VALUES (
                'SUPPLIER', 999, 'TEST', 'TEST-CONSTRAINT-2',
                SYSDATE, 'USD', 12, 1800, 1
            )
            """,
            "should_fail": True,
            "expected_error": "CHK_BT_YEAR_NO"
        },
        {
            "name": "اختبار BRANCH_ID سالب",
            "query": """
            INSERT INTO BALANCE_TRANSACTIONS (
                entity_type_code, entity_id, document_type_code, document_number,
                document_date, currency_code, month_no, year_no, branch_id
            ) VALUES (
                'SUPPLIER', 999, 'TEST', 'TEST-CONSTRAINT-3',
                SYSDATE, 'USD', 12, 2025, -1
            )
            """,
            "should_fail": True,
            "expected_error": "CHK_BT_BRANCH_ID"
        }
    ]
    
    successful_tests = 0
    
    for test in constraint_tests:
        try:
            print(f"\n   🧪 {test['name']}:")
            
            oracle.execute_update(test["query"])
            
            if test["should_fail"]:
                print(f"      ❌ فشل الاختبار - كان يجب أن يفشل الإدراج")
                # حذف السجل المدرج خطأً
                oracle.execute_update("DELETE FROM BALANCE_TRANSACTIONS WHERE entity_id = 999")
            else:
                print(f"      ✅ نجح الاختبار - تم الإدراج بنجاح")
                successful_tests += 1
                
        except Exception as e:
            if test["should_fail"] and test["expected_error"] in str(e):
                print(f"      ✅ نجح الاختبار - فشل الإدراج كما متوقع")
                print(f"         الخطأ: {test['expected_error']}")
                successful_tests += 1
            else:
                print(f"      ❌ فشل الاختبار: {str(e)}")
    
    print(f"\nملخص اختبارات القيود:")
    print(f"   نجح: {successful_tests}/{len(constraint_tests)}")
    
    return successful_tests

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء تحديث قيود قاعدة البيانات")
    print("=" * 80)
    
    try:
        # 1. فحص القيود الموجودة
        existing_constraints = check_existing_constraints()
        
        # 2. إضافة قيود التحقق
        check_count = add_check_constraints()
        
        # 3. إضافة المفاتيح الأجنبية
        fk_count = add_foreign_key_constraints()
        
        # 4. تحديث قيود NULL
        null_count = add_not_null_constraints()
        
        # 5. التحقق من النتائج
        verify_constraints()
        
        # 6. اختبار القيود
        test_count = test_constraints()
        
        total_success = check_count + fk_count + null_count
        
        if total_success > 0:
            print("\n🎉 تم إكمال تحديث قيود قاعدة البيانات بنجاح!")
            print(f"✅ إجمالي القيود المضافة/المحدثة: {total_success}")
            print("✅ المهمة 7gsrVGA35b45xQi6yUC3j7 مكتملة!")
            
            return True
        else:
            print("\n⚠️ لم يتم إضافة قيود جديدة - ربما موجودة مسبقاً")
            return True
            
    except Exception as e:
        print(f"\n❌ خطأ عام: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n" + "=" * 80)
        print("🏁 انتهى تحديث قيود قاعدة البيانات بنجاح!")
    else:
        print("\n" + "=" * 80)
        print("🚫 فشل في تحديث القيود - يرجى مراجعة الأخطاء")
