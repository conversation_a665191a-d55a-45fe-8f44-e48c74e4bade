{% extends "base.html" %}

{% block title %}معلومات النظام{% endblock %}

{% block extra_css %}
<style>
    .info-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .info-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    
    .metric-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
    }
    
    .metric-item:last-child {
        border-bottom: none;
    }
    
    .metric-label {
        font-weight: 600;
        color: #2c3e50;
    }
    
    .metric-value {
        color: #27ae60;
        font-weight: bold;
    }
    
    .progress-custom {
        height: 20px;
        border-radius: 10px;
        background-color: #ecf0f1;
    }
    
    .progress-bar-custom {
        border-radius: 10px;
        transition: width 0.6s ease;
    }
    
    .status-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .status-online {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-warning {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .status-error {
        background-color: #f8d7da;
        color: #721c24;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('settings.index') }}">الإعدادات</a></li>
                    <li class="breadcrumb-item active">معلومات النظام</li>
                </ol>
            </nav>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="info-header text-center">
                <h2><i class="fas fa-server"></i> معلومات النظام</h2>
                <p class="mb-0">مراقبة شاملة لحالة الخادم والأداء</p>
            </div>
        </div>
    </div>
    
    <!-- حالة النظام العامة -->
    <div class="row">
        <div class="col-md-3">
            <div class="info-card text-center">
                <i class="fas fa-heartbeat fa-3x text-success mb-3"></i>
                <h5>حالة النظام</h5>
                <span class="status-badge status-online" id="systemStatus">متصل</span>
            </div>
        </div>
        <div class="col-md-3">
            <div class="info-card text-center">
                <i class="fas fa-database fa-3x text-info mb-3"></i>
                <h5>قاعدة البيانات</h5>
                <span class="status-badge" id="dbStatus">جاري التحقق...</span>
            </div>
        </div>
        <div class="col-md-3">
            <div class="info-card text-center">
                <i class="fas fa-memory fa-3x text-warning mb-3"></i>
                <h5>الذاكرة</h5>
                <span class="metric-value" id="memoryPercent">--</span>
            </div>
        </div>
        <div class="col-md-3">
            <div class="info-card text-center">
                <i class="fas fa-hdd fa-3x text-primary mb-3"></i>
                <h5>مساحة القرص</h5>
                <span class="metric-value" id="diskPercent">--</span>
            </div>
        </div>
    </div>
    
    <!-- معلومات النظام التفصيلية -->
    <div class="row">
        <div class="col-md-6">
            <div class="info-card">
                <h5><i class="fas fa-desktop"></i> معلومات النظام</h5>
                <div id="systemInfo">
                    <div class="metric-item">
                        <span class="metric-label">نظام التشغيل:</span>
                        <span class="metric-value" id="platform">--</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">إصدار النظام:</span>
                        <span class="metric-value" id="platformVersion">--</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">المعمارية:</span>
                        <span class="metric-value" id="architecture">--</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">المعالج:</span>
                        <span class="metric-value" id="processor">--</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">إصدار Python:</span>
                        <span class="metric-value" id="pythonVersion">--</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="info-card">
                <h5><i class="fas fa-flask"></i> معلومات التطبيق</h5>
                <div id="appInfo">
                    <div class="metric-item">
                        <span class="metric-label">وضع التطوير:</span>
                        <span class="metric-value" id="debugMode">--</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">البيئة:</span>
                        <span class="metric-value" id="environment">--</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">المفتاح السري:</span>
                        <span class="metric-value" id="secretKey">--</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">قاعدة البيانات:</span>
                        <span class="metric-value" id="databaseName">--</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- استخدام الموارد -->
    <div class="row">
        <div class="col-md-6">
            <div class="info-card">
                <h5><i class="fas fa-memory"></i> استخدام الذاكرة</h5>
                <div id="memoryInfo">
                    <div class="metric-item">
                        <span class="metric-label">إجمالي الذاكرة:</span>
                        <span class="metric-value" id="totalMemory">-- GB</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">المستخدم:</span>
                        <span class="metric-value" id="usedMemory">-- GB</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">المتاح:</span>
                        <span class="metric-value" id="availableMemory">-- GB</span>
                    </div>
                    <div class="mt-3">
                        <div class="progress progress-custom">
                            <div class="progress-bar progress-bar-custom bg-warning" id="memoryProgressBar" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="info-card">
                <h5><i class="fas fa-hdd"></i> استخدام القرص</h5>
                <div id="diskInfo">
                    <div class="metric-item">
                        <span class="metric-label">إجمالي المساحة:</span>
                        <span class="metric-value" id="totalDisk">-- GB</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">المستخدم:</span>
                        <span class="metric-value" id="usedDisk">-- GB</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">المتاح:</span>
                        <span class="metric-value" id="freeDisk">-- GB</span>
                    </div>
                    <div class="mt-3">
                        <div class="progress progress-custom">
                            <div class="progress-bar progress-bar-custom bg-primary" id="diskProgressBar" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- أزرار التحكم -->
    <div class="row">
        <div class="col-12">
            <div class="info-card text-center">
                <button class="btn btn-primary me-2" onclick="refreshSystemInfo()">
                    <i class="fas fa-sync-alt"></i> تحديث المعلومات
                </button>
                <button class="btn btn-success me-2" onclick="downloadSystemReport()">
                    <i class="fas fa-download"></i> تحميل تقرير النظام
                </button>
                <button class="btn btn-warning" onclick="clearSystemCache()">
                    <i class="fas fa-broom"></i> مسح التخزين المؤقت
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحميل معلومات النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadSystemInfo();
    loadDatabaseStatus();
    
    // تحديث المعلومات كل دقيقة
    setInterval(loadSystemInfo, 60000);
});

// تحميل معلومات النظام
async function loadSystemInfo() {
    try {
        const response = await fetch('/settings/api/system-status');
        const data = await response.json();
        
        if (data.success) {
            // معلومات النظام
            document.getElementById('platform').textContent = data.system.platform;
            document.getElementById('platformVersion').textContent = data.system.platform_version;
            document.getElementById('architecture').textContent = data.system.architecture;
            document.getElementById('processor').textContent = data.system.processor || 'غير محدد';
            document.getElementById('pythonVersion').textContent = data.system.python_version;
            
            // معلومات التطبيق
            document.getElementById('debugMode').textContent = data.application.debug_mode ? 'مفعل' : 'معطل';
            document.getElementById('environment').textContent = data.application.environment;
            document.getElementById('secretKey').textContent = data.application.secret_key_set ? 'محدد' : 'غير محدد';
            document.getElementById('databaseName').textContent = data.application.database_url;
            
            // معلومات الذاكرة
            document.getElementById('totalMemory').textContent = data.memory.total + ' GB';
            document.getElementById('usedMemory').textContent = data.memory.used + ' GB';
            document.getElementById('availableMemory').textContent = data.memory.available + ' GB';
            document.getElementById('memoryPercent').textContent = data.memory.percentage + '%';
            document.getElementById('memoryProgressBar').style.width = data.memory.percentage + '%';
            
            // معلومات القرص
            document.getElementById('totalDisk').textContent = data.disk.total + ' GB';
            document.getElementById('usedDisk').textContent = data.disk.used + ' GB';
            document.getElementById('freeDisk').textContent = data.disk.free + ' GB';
            document.getElementById('diskPercent').textContent = data.disk.percentage + '%';
            document.getElementById('diskProgressBar').style.width = data.disk.percentage + '%';
            
            // تحديث ألوان شريط التقدم حسب الاستخدام
            updateProgressBarColor('memoryProgressBar', data.memory.percentage);
            updateProgressBarColor('diskProgressBar', data.disk.percentage);
            
        } else {
            console.error('فشل في تحميل معلومات النظام:', data.message);
        }
    } catch (error) {
        console.error('خطأ في تحميل معلومات النظام:', error);
    }
}

// تحميل حالة قاعدة البيانات
async function loadDatabaseStatus() {
    try {
        const response = await fetch('/settings/api/database-status');
        const data = await response.json();
        
        const dbStatusElement = document.getElementById('dbStatus');
        
        if (data.success && data.connected) {
            dbStatusElement.textContent = 'متصل';
            dbStatusElement.className = 'status-badge status-online';
        } else {
            dbStatusElement.textContent = 'غير متصل';
            dbStatusElement.className = 'status-badge status-error';
        }
    } catch (error) {
        console.error('خطأ في فحص قاعدة البيانات:', error);
        document.getElementById('dbStatus').textContent = 'خطأ';
        document.getElementById('dbStatus').className = 'status-badge status-error';
    }
}

// تحديث لون شريط التقدم حسب النسبة
function updateProgressBarColor(elementId, percentage) {
    const element = document.getElementById(elementId);
    
    if (percentage < 50) {
        element.className = 'progress-bar progress-bar-custom bg-success';
    } else if (percentage < 80) {
        element.className = 'progress-bar progress-bar-custom bg-warning';
    } else {
        element.className = 'progress-bar progress-bar-custom bg-danger';
    }
}

// تحديث معلومات النظام
function refreshSystemInfo() {
    loadSystemInfo();
    loadDatabaseStatus();
    alert('تم تحديث معلومات النظام');
}

// تحميل تقرير النظام
function downloadSystemReport() {
    alert('ميزة تحميل تقرير النظام قيد التطوير');
}

// مسح التخزين المؤقت
async function clearSystemCache() {
    try {
        const response = await fetch('/settings/api/clear-cache', {
            method: 'POST'
        });
        const data = await response.json();
        
        if (data.success) {
            alert('تم مسح التخزين المؤقت بنجاح');
        } else {
            alert('خطأ: ' + data.message);
        }
    } catch (error) {
        alert('خطأ في مسح التخزين المؤقت');
    }
}
</script>
{% endblock %}
