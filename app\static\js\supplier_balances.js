/**
 * نظام إدارة أرصدة الموردين المتقدم - JavaScript
 * Advanced Supplier Balance Management System - JavaScript
 */

// متغيرات عامة
let balancesData = {
    dashboard: {},
    suppliers: [],
    charts: {},
    filters: {
        period: 'current_month',
        currency: 'all',
        balanceStatus: 'all',
        supplierType: 'all'
    }
};

/**
 * تحميل لوحة معلومات الأرصدة
 */
function loadBalancesDashboard() {
    showLoadingSpinner();
    
    // جمع معايير الفلترة
    const filters = {
        period: $('#periodFilter').val(),
        currency: $('#currencyFilter').val(),
        balance_status: $('#balanceStatusFilter').val(),
        supplier_type: $('#supplierTypeFilter').val()
    };
    
    $.ajax({
        url: '/suppliers/api/balances/dashboard',
        method: 'GET',
        data: filters,
        success: function(response) {
            if (response.success) {
                balancesData.dashboard = response.dashboard;
                updateDashboardStats(response.dashboard);
                loadBalancesCharts(response.dashboard);
                loadSuppliersList(response.dashboard.suppliers || []);
            } else {
                showNotification('خطأ في تحميل بيانات الأرصدة: ' + response.message, 'error');
                loadDefaultDashboard();
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في تحميل لوحة معلومات الأرصدة:', error);
            showNotification('خطأ في الاتصال بالخادم', 'error');
            loadDefaultDashboard();
        }
    });
}

/**
 * تحديث إحصائيات لوحة المعلومات
 */
function updateDashboardStats(dashboard) {
    const stats = dashboard.statistics || {};
    const charts = dashboard.charts || {};
    const currencies = charts.by_currency || [];

    // تحديد العملة الرئيسية أو عرض "متنوع" إذا كان هناك عملات متعددة
    let mainCurrency = 'متنوع';
    if (currencies.length === 1) {
        mainCurrency = currencies[0].code || currencies[0].currency || 'غير محدد';
    } else if (currencies.length > 1) {
        mainCurrency = 'عملات متعددة';
    }

    // تحديث البطاقات الرئيسية مع التنسيق المحسن
    $('#totalBalance').text(formatCurrency(stats.total_balance || 0));
    $('#totalOutstanding').text(formatCurrency(stats.total_outstanding || 0));
    $('#totalSuppliers').text(stats.total_suppliers || 0);
    $('#overdueAmount').text(formatCurrency(stats.overdue_amount || 0));

    // تحديث تسميات البطاقات بالعملات الصحيحة
    $('#totalBalanceLabel').text(`إجمالي الأرصدة (${mainCurrency})`);
    $('#totalOutstandingLabel').text(`إجمالي المستحقات (${mainCurrency})`);
    $('#overdueAmountLabel').text(`المبالغ المتأخرة (${mainCurrency})`);

    // تحديث تحليل الاستحقاقات مع البيانات الحقيقية والعملات الصحيحة
    const aging = stats.aging_analysis || {};
    const mainCurrency = dashboard.main_currency || 'متنوع';
    $('#agingCurrent').text(formatCurrency(aging.current_due || 0) + ' ' + mainCurrency);
    $('#aging30').text(formatCurrency(aging.overdue_1_30 || 0) + ' ' + mainCurrency);
    $('#aging60').text(formatCurrency(aging.overdue_31_60 || 0) + ' ' + mainCurrency);
    $('#aging90').text(formatCurrency(aging.overdue_61_90 || 0) + ' ' + mainCurrency);
    $('#agingOver90').text(formatCurrency(aging.overdue_over_90 || 0) + ' ' + mainCurrency);

    // إضافة تأثيرات بصرية للبطاقات
    animateCounters();

    // تحديث ألوان البطاقات حسب القيم
    updateCardColors(stats);
}

/**
 * تحريك العدادات للتأثير البصري
 */
function animateCounters() {
    $('.stat-number').each(function() {
        const $this = $(this);
        const countTo = parseFloat($this.text().replace(/[^\d.-]/g, ''));

        if (!isNaN(countTo)) {
            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'swing',
                step: function() {
                    if (countTo > 1000) {
                        $this.text(formatCurrency(Math.floor(this.countNum)));
                    } else {
                        $this.text(Math.floor(this.countNum));
                    }
                },
                complete: function() {
                    if (countTo > 1000) {
                        $this.text(formatCurrency(countTo));
                    } else {
                        $this.text(countTo);
                    }
                }
            });
        }
    });
}

/**
 * تحديث ألوان البطاقات حسب القيم
 */
function updateCardColors(stats) {
    const totalBalance = stats.total_balance || 0;
    const overdueAmount = stats.overdue_amount || 0;
    
    // بطاقة إجمالي الأرصدة
    const balanceCard = $('#totalBalance').closest('.balance-card');
    if (totalBalance > 0) {
        balanceCard.removeClass('negative neutral').addClass('positive');
    } else if (totalBalance < 0) {
        balanceCard.removeClass('positive neutral').addClass('negative');
    } else {
        balanceCard.removeClass('positive negative').addClass('neutral');
    }
    
    // بطاقة المبالغ المتأخرة
    const overdueCard = $('#overdueAmount').closest('.balance-card');
    if (overdueAmount > 100000) {
        overdueCard.removeClass('positive neutral').addClass('negative');
    } else if (overdueAmount > 50000) {
        overdueCard.removeClass('positive negative').addClass('neutral');
    } else {
        overdueCard.removeClass('negative neutral').addClass('positive');
    }
}

/**
 * تحميل الرسوم البيانية
 */
function loadBalancesCharts(dashboard) {
    const chartsData = dashboard.charts || {};
    
    // رسم بياني لتوزيع الأرصدة حسب العملة
    createBalancesByCurrencyChart(chartsData.by_currency || []);
    
    // رسم بياني لاتجاهات الأرصدة
    createBalancesTrendsChart(chartsData.trends || []);
}

/**
 * إنشاء رسم بياني لتوزيع الأرصدة حسب العملة
 */
function createBalancesByCurrencyChart(data) {
    const ctx = document.getElementById('balancesByCurrencyChart').getContext('2d');
    
    // تدمير الرسم البياني السابق إذا كان موجود
    if (balancesData.charts.currencyChart) {
        balancesData.charts.currencyChart.destroy();
    }
    
    const chartData = {
        labels: data.map(item => item.currency || 'غير محدد'),
        datasets: [{
            data: data.map(item => Math.abs(item.amount || 0)),
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    };
    
    balancesData.charts.currencyChart = new Chart(ctx, {
        type: 'doughnut',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true,
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = formatCurrency(context.parsed);
                            return label + ': ' + value;
                        }
                    }
                }
            },
            elements: {
                arc: {
                    borderWidth: 2
                }
            }
        }
    });
}

/**
 * إنشاء رسم بياني لاتجاهات الأرصدة
 */
function createBalancesTrendsChart(data) {
    const ctx = document.getElementById('balancesTrendsChart').getContext('2d');
    
    // تدمير الرسم البياني السابق إذا كان موجود
    if (balancesData.charts.trendsChart) {
        balancesData.charts.trendsChart.destroy();
    }
    
    const chartData = {
        labels: data.map(item => item.month || ''),
        datasets: [
            {
                label: 'إجمالي الأرصدة',
                data: data.map(item => item.total_balance || 0),
                borderColor: '#36A2EB',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                fill: true,
                tension: 0.4
            },
            {
                label: 'المبالغ المستحقة',
                data: data.map(item => item.outstanding_amount || 0),
                borderColor: '#FF6384',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                fill: true,
                tension: 0.4
            }
        ]
    };
    
    balancesData.charts.trendsChart = new Chart(ctx, {
        type: 'line',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.dataset.label || '';
                            const value = formatCurrency(context.parsed.y);
                            return label + ': ' + value;
                        }
                    }
                }
            }
        }
    });
}

/**
 * تحميل قائمة الموردين
 */
function loadSuppliersList(suppliers) {
    balancesData.suppliers = suppliers;
    displaySuppliersList(suppliers);
}

/**
 * عرض قائمة الموردين بتصميم محسن
 */
function displaySuppliersList(suppliers) {
    const container = $('#suppliersList');

    if (!suppliers || suppliers.length === 0) {
        container.html(`
            <div class="text-center py-5">
                <i class="fas fa-building fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد بيانات موردين متاحة</h5>
                <p class="text-muted">تحقق من الفلاتر أو قم بإضافة موردين جدد</p>
            </div>
        `);
        return;
    }

    let html = '<div class="row">';
    suppliers.forEach((supplier, index) => {
        const balanceClass = getBalanceClass(supplier.current_balance);
        const riskBadge = getRiskBadge(supplier.risk_rating);
        const paymentScore = supplier.payment_score || 0;
        const overdueAmount = supplier.overdue_amount || 0;

        html += `
            <div class="col-lg-6 col-xl-4 mb-3">
                <div class="balance-card ${balanceClass}" onclick="viewSupplierDetails(${supplier.account_id})" style="cursor: pointer;">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="flex-grow-1">
                            <h6 class="mb-1" style="color: #2c3e50; font-weight: 600;">
                                ${supplier.supplier_name || 'غير محدد'}
                            </h6>
                            <small class="text-muted">
                                <i class="fas fa-tag me-1"></i>${supplier.supplier_code || ''}
                                <span class="ms-2">${getAccountTypeText(supplier.account_type)}</span>
                            </small>
                        </div>
                        <div class="text-end">
                            ${riskBadge}
                        </div>
                    </div>

                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <div class="stat-number ${balanceClass}" style="font-size: 1.4rem;">
                                ${formatCurrency(supplier.current_balance || 0)}
                            </div>
                            <small class="text-muted">الرصيد الحالي</small>
                        </div>
                        <div class="col-6">
                            <div class="stat-number" style="font-size: 1.4rem; color: ${overdueAmount > 0 ? '#dc3545' : '#28a745'};">
                                ${formatCurrency(overdueAmount)}
                            </div>
                            <small class="text-muted">متأخرات</small>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            آخر معاملة: ${formatDate(supplier.last_transaction_date)}
                        </small>
                        <div class="d-flex align-items-center">
                            <small class="text-muted me-2">تقييم الدفع:</small>
                            <div class="d-flex">
                                ${generateStarRating(paymentScore)}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';

    container.html(html);
}

/**
 * إنشاء تقييم نجوم للمورد
 */
function generateStarRating(score) {
    let stars = '';
    const fullStars = Math.floor(score);
    const hasHalfStar = score % 1 >= 0.5;

    for (let i = 0; i < 5; i++) {
        if (i < fullStars) {
            stars += '<i class="fas fa-star text-warning"></i>';
        } else if (i === fullStars && hasHalfStar) {
            stars += '<i class="fas fa-star-half-alt text-warning"></i>';
        } else {
            stars += '<i class="far fa-star text-muted"></i>';
        }
    }

    return stars;
}

/**
 * الحصول على نص نوع الحساب
 */
function getAccountTypeText(type) {
    const typeMap = {
        'TRADE': 'تجاري',
        'SERVICE': 'خدمي',
        'CONTRACTOR': 'مقاول'
    };
    return typeMap[type] || type;
}

/**
 * فلترة قائمة الموردين
 */
function filterSuppliersList(searchTerm) {
    if (!searchTerm) {
        displaySuppliersList(balancesData.suppliers);
        return;
    }
    
    const filtered = balancesData.suppliers.filter(supplier => {
        const name = (supplier.supplier_name || '').toLowerCase();
        const code = (supplier.supplier_code || '').toLowerCase();
        const search = searchTerm.toLowerCase();
        
        return name.includes(search) || code.includes(search);
    });
    
    displaySuppliersList(filtered);
}

/**
 * عرض تفاصيل المورد
 */
function viewSupplierDetails(accountId) {
    // سيتم تطويرها في المرحلة التالية
    showNotification('سيتم فتح تفاصيل المورد قريباً', 'info');
}

/**
 * تحديث لوحة المعلومات
 */
function refreshDashboard() {
    showNotification('جاري تحديث البيانات...', 'info');
    loadBalancesDashboard();
}

/**
 * عرض modal إنشاء دورة مطابقة
 */
function showReconciliationModal() {
    // تعيين التواريخ الافتراضية
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    
    $('#periodFrom').val(formatDateForInput(firstDay));
    $('#periodTo').val(formatDateForInput(lastDay));
    $('#cycleName').val('مطابقة ' + formatMonth(today));
    
    $('#reconciliationModal').modal('show');
}

/**
 * إنشاء دورة مطابقة جديدة
 */
function createReconciliationCycle() {
    const formData = {
        cycle_name: $('#cycleName').val(),
        cycle_type: $('#cycleType').val(),
        period_from: $('#periodFrom').val(),
        period_to: $('#periodTo').val(),
        scope: $('#reconciliationScope').val(),
        notes: $('#reconciliationNotes').val()
    };
    
    // التحقق من البيانات المطلوبة
    if (!formData.cycle_name || !formData.period_from || !formData.period_to) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }
    
    $.ajax({
        url: '/suppliers/api/reconciliation/create-cycle',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                showNotification('تم إنشاء دورة المطابقة بنجاح', 'success');
                $('#reconciliationModal').modal('hide');
                // يمكن إضافة تحديث للبيانات هنا
            } else {
                showNotification('خطأ في إنشاء دورة المطابقة: ' + response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في إنشاء دورة المطابقة:', error);
            showNotification('خطأ في الاتصال بالخادم', 'error');
        }
    });
}

/**
 * إنشاء التقارير
 */
function generateReports() {
    // سيتم تطويرها في المرحلة التالية
    showNotification('سيتم إضافة التقارير قريباً', 'info');
}

/**
 * تحميل البيانات الافتراضية في حالة الخطأ
 */
function loadDefaultDashboard() {
    const defaultStats = {
        total_balance: 0,
        total_outstanding: 0,
        total_suppliers: 0,
        overdue_amount: 0,
        aging_analysis: {
            current_due: 0,
            overdue_1_30: 0,
            overdue_31_60: 0,
            overdue_61_90: 0,
            overdue_over_90: 0
        }
    };
    
    updateDashboardStats({ statistics: defaultStats });
    displaySuppliersList([]);
}

/**
 * عرض مؤشر التحميل
 */
function showLoadingSpinner() {
    $('#suppliersList').html(`
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `);
}

// دوال مساعدة
function getBalanceClass(balance) {
    if (balance > 0) return 'balance-positive';
    if (balance < 0) return 'balance-negative';
    return 'balance-zero';
}

function getRiskBadge(risk) {
    const riskMap = {
        'LOW': '<span class="badge bg-success">منخفض المخاطر</span>',
        'MEDIUM': '<span class="badge bg-warning">متوسط المخاطر</span>',
        'HIGH': '<span class="badge bg-danger">عالي المخاطر</span>',
        'CRITICAL': '<span class="badge bg-dark">حرج</span>'
    };
    return riskMap[risk] || '';
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount || 0);
}

function formatDate(dateString) {
    if (!dateString) return 'غير محدد';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function formatDateForInput(date) {
    return date.toISOString().split('T')[0];
}

function formatMonth(date) {
    const months = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return months[date.getMonth()] + ' ' + date.getFullYear();
}

function showNotification(message, type = 'info') {
    // استخدام نظام الإشعارات الموجود في التطبيق
    if (typeof showAlert === 'function') {
        showAlert(message, type);
    } else {
        // إنشاء إشعار مخصص
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';

        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                <strong>${message}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        $('body').append(alertHtml);

        // إزالة الإشعار تلقائياً بعد 5 ثوان
        setTimeout(() => {
            $('.alert').fadeOut();
        }, 5000);
    }
}

// دوال إضافية للواجهة المحسنة
function clearAllFilters() {
    $('#periodFilter').val('current_month');
    $('#currencyFilter').val('all');
    $('#balanceStatusFilter').val('all');
    $('#supplierTypeFilter').val('all');
    $('#riskFilter').val('all');
    $('#supplierSearch').val('');

    showNotification('تم مسح جميع الفلاتر', 'info');
    loadBalancesDashboard();
}

function exportSuppliersData() {
    showNotification('جاري تصدير بيانات الموردين...', 'info');
    // محاكاة عملية التصدير
    setTimeout(() => {
        showNotification('تم تصدير البيانات بنجاح', 'success');
    }, 2000);
}

function addNewSupplier() {
    window.location.href = '/suppliers/accounts_management';
}

function exportBalancesReport() {
    showNotification('جاري إنشاء تقرير الأرصدة...', 'info');
    setTimeout(() => {
        showNotification('تم إنشاء التقرير بنجاح', 'success');
    }, 2000);
}

function exportAgingReport() {
    showNotification('جاري إنشاء تقرير تحليل الاستحقاقات...', 'info');
    setTimeout(() => {
        showNotification('تم إنشاء التقرير بنجاح', 'success');
    }, 2000);
}

function exportTrendsReport() {
    showNotification('جاري إنشاء تقرير الاتجاهات...', 'info');
    setTimeout(() => {
        showNotification('تم إنشاء التقرير بنجاح', 'success');
    }, 2000);
}
