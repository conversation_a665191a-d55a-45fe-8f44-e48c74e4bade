{% extends "base_netsuite_real.html" %}

{% block title %}لوحة المعلومات - NetSuite Oracle{% endblock %}

{% block content %}
<!-- NetSuite REAL Page Header -->
<div class="ns-page-header-real">
    <h1>
        <i class="fas fa-tachometer-alt"></i>
        لوحة المعلومات
    </h1>
</div>

<!-- NetSuite REAL Stats Cards -->
<div class="ns-stats-real">
    <div class="ns-stat-card-real">
        <div class="ns-stat-icon">
            <i class="fas fa-users"></i>
        </div>
        <div class="ns-stat-number">5</div>
        <div class="ns-stat-label">المستخدمين</div>
    </div>
    
    <div class="ns-stat-card-real">
        <div class="ns-stat-icon">
            <i class="fas fa-building"></i>
        </div>
        <div class="ns-stat-number">0</div>
        <div class="ns-stat-label">الموردين</div>
    </div>
    
    <div class="ns-stat-card-real">
        <div class="ns-stat-icon">
            <i class="fas fa-shopping-cart"></i>
        </div>
        <div class="ns-stat-number">0</div>
        <div class="ns-stat-label">أوامر الشراء</div>
    </div>
    
    <div class="ns-stat-card-real">
        <div class="ns-stat-icon">
            <i class="fas fa-boxes"></i>
        </div>
        <div class="ns-stat-number">0</div>
        <div class="ns-stat-label">عناصر المخزون</div>
    </div>
</div>

<!-- NetSuite REAL Cards Grid -->
<div class="row">
    <div class="col-md-6">
        <!-- Recent Activities Card -->
        <div class="ns-card-real">
            <div class="ns-card-header">
                <i class="fas fa-clock me-2"></i>
                الأنشطة الأخيرة
            </div>
            <div class="ns-card-body">
                <div class="text-center text-muted">
                    <i class="fas fa-info-circle mb-2" style="font-size: 24px;"></i>
                    <p>لا توجد أنشطة حديثة</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <!-- Quick Actions Card -->
        <div class="ns-card-real">
            <div class="ns-card-header">
                <i class="fas fa-bolt me-2"></i>
                إجراءات سريعة
            </div>
            <div class="ns-card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('suppliers.create') }}" class="ns-btn-real ns-btn-primary-real">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مورد جديد
                    </a>
                    <a href="{{ url_for('purchase_orders.create') }}" class="ns-btn-real ns-btn-outline-real">
                        <i class="fas fa-shopping-cart me-2"></i>
                        إنشاء أمر شراء
                    </a>
                    <a href="{{ url_for('inventory.add_item') }}" class="ns-btn-real ns-btn-outline-real">
                        <i class="fas fa-box me-2"></i>
                        إضافة عنصر مخزون
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- NetSuite REAL Recent Data Table -->
<div class="ns-card-real">
    <div class="ns-card-header">
        <i class="fas fa-table me-2"></i>
        البيانات الحديثة
    </div>
    <div class="ns-card-body">
        <table class="ns-table-real">
            <thead>
                <tr>
                    <th>النوع</th>
                    <th>الوصف</th>
                    <th>التاريخ</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>مورد</td>
                    <td>شركة التقنية المتطورة</td>
                    <td>2024-01-15</td>
                    <td><span class="badge bg-success">نشط</span></td>
                    <td>
                        <a href="#" class="ns-btn-real ns-btn-outline-real" style="padding: 3px 8px; font-size: 11px;">
                            <i class="fas fa-eye"></i>
                        </a>
                    </td>
                </tr>
                <tr>
                    <td>أمر شراء</td>
                    <td>أجهزة كمبيوتر</td>
                    <td>2024-01-14</td>
                    <td><span class="badge bg-warning">قيد المراجعة</span></td>
                    <td>
                        <a href="#" class="ns-btn-real ns-btn-outline-real" style="padding: 3px 8px; font-size: 11px;">
                            <i class="fas fa-edit"></i>
                        </a>
                    </td>
                </tr>
                <tr>
                    <td>عنصر مخزون</td>
                    <td>لابتوب Dell</td>
                    <td>2024-01-13</td>
                    <td><span class="badge bg-info">متوفر</span></td>
                    <td>
                        <a href="#" class="ns-btn-real ns-btn-outline-real" style="padding: 3px 8px; font-size: 11px;">
                            <i class="fas fa-box"></i>
                        </a>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- NetSuite REAL System Info -->
<div class="row mt-3">
    <div class="col-md-12">
        <div class="ns-card-real">
            <div class="ns-card-header">
                <i class="fas fa-info-circle me-2"></i>
                معلومات النظام
            </div>
            <div class="ns-card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>إصدار النظام:</strong><br>
                        <span class="text-muted">NetSuite Oracle v2024.1</span>
                    </div>
                    <div class="col-md-3">
                        <strong>قاعدة البيانات:</strong><br>
                        <span class="text-muted">Oracle Database 19c</span>
                    </div>
                    <div class="col-md-3">
                        <strong>آخر تحديث:</strong><br>
                        <span class="text-muted">{{ moment().format('YYYY-MM-DD HH:mm') }}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>حالة النظام:</strong><br>
                        <span class="badge bg-success">يعمل بشكل طبيعي</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update stats with animation
    const statNumbers = document.querySelectorAll('.ns-stat-number');
    statNumbers.forEach(stat => {
        const finalValue = parseInt(stat.textContent);
        let currentValue = 0;
        const increment = finalValue / 20;
        
        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
                stat.textContent = finalValue;
                clearInterval(timer);
            } else {
                stat.textContent = Math.floor(currentValue);
            }
        }, 50);
    });
    
    console.log('NetSuite REAL Dashboard loaded');
});
</script>
{% endblock %}
