<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أمر تسليم - {{ order.order_number }}</title>
    
    <!-- خط عربي من Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS للرسائل -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- مكتبة jsPDF للتحويل إلى PDF -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
            text-align: right;
            background: #f8f9fa;
        }
        
        .controls {
            position: fixed;
            top: 5px;
            left: 20px;
            z-index: 1000;
            background: white;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-warning:hover {
            background: #e0a800;
        }
        
        .container {
            max-width: 210mm;
            width: 210mm;
            min-height: 297mm;
            margin: 10px auto 20px;
            padding: 15mm;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            font-size: 12px;
            line-height: 1.4;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #1f4e79;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .company-name {
            font-size: 18px;
            font-weight: 700;
            color: #1f4e79;
            margin-bottom: 5px;
        }

        .document-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c5aa0;
            margin-bottom: 8px;
        }
        
        .order-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .order-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            text-align: center;
        }

        .info-item .info-label {
            font-weight: 600;
            color: #495057;
            font-size: 11px;
            margin-bottom: 3px;
        }

        .info-item .info-value {
            font-size: 13px;
            color: #212529;
        }
        
        .section {
            margin-bottom: 15px;
        }

        .section-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f4e79;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            font-size: 11px;
        }

        .data-table th {
            background: #1f4e79;
            color: white;
            padding: 8px;
            text-align: center;
            font-weight: 600;
            font-size: 11px;
        }

        .data-table td {
            padding: 6px 8px;
            border: 1px solid #dee2e6;
            text-align: center;
            vertical-align: middle;
        }
        
        .data-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .shipment-table th { background: #28a745; }
        .agent-table th { background: #17a2b8; }
        .delivery-table th { background: #ffc107; color: #212529; }
        
        .special-instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }

        .special-instructions h4 {
            color: #856404;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .footer {
            border-top: 1px solid #1f4e79;
            padding-top: 10px;
            margin-top: 15px;
            text-align: center;
            font-size: 10px;
            color: #6c757d;
        }
        
        .status-badge, .priority-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .status-draft { background: #6c757d; color: white; }
        .status-sent { background: #007bff; color: white; }
        .status-in_progress { background: #ffc107; color: #212529; }
        .status-completed { background: #28a745; color: white; }
        .status-cancelled { background: #dc3545; color: white; }
        
        .priority-low { background: #6c757d; color: white; }
        .priority-normal { background: #007bff; color: white; }
        .priority-high { background: #ffc107; color: #212529; }
        .priority-urgent { background: #dc3545; color: white; }
        
        @media print {
            .controls { display: none; }
            body {
                background: white;
                margin: 0;
                padding: 0;
            }
            .container {
                margin: 0;
                padding: 5mm 10mm 10mm 10mm;
                box-shadow: none;
                border-radius: 0;
                max-width: none;
                width: 210mm;
                min-height: 297mm;
                font-size: 11px;
            }
            .header {
                margin-bottom: 10px;
                padding-bottom: 8px;
            }
            .section {
                margin-bottom: 12px;
            }
            .data-table {
                font-size: 10px;
            }
            .data-table th {
                padding: 6px;
            }
            .data-table td {
                padding: 4px 6px;
            }
            .footer {
                margin-top: 10px;
                padding-top: 8px;
                font-size: 9px;
            }
        }
    </style>
</head>
<body>
    <!-- أزرار التحكم -->
    <div class="controls">
        <button class="btn-primary" onclick="downloadPDF()">
            📄 تحميل PDF
        </button>
        <button class="btn-success" onclick="window.print()">
            🖨️ طباعة
        </button>
        <button class="btn-info" onclick="window.close()">
            ❌ إغلاق
        </button>
    </div>

    <!-- محتوى أمر التسليم -->
    <div class="container" id="delivery-order-content">
        <!-- رأس المستند -->
        <div class="header">
            {% if order.branch_logo %}
            <div style="text-align: center; margin-bottom: 10px;">
                <img src="{{ order.branch_logo }}" alt="شعار الفرع" style="max-height: 60px; max-width: 200px;">
            </div>
            {% endif %}
            <div class="company-name">
                {{ order.branch_name or 'شركة النقل والشحن المتطورة' }}
                {% if order.branch_name_en %}
                <br><span style="font-size: 14px; color: #666;">{{ order.branch_name_en }}</span>
                {% endif %}
            </div>
            <div class="document-title">أمر تسليم للمخلص الجمركي</div>
            {% if order.branch_address or order.branch_phone %}
            <div style="font-size: 12px; color: #666; margin-top: 5px;">
                {% if order.branch_address %}{{ order.branch_address }}{% endif %}
                {% if order.branch_address and order.branch_phone %} | {% endif %}
                {% if order.branch_phone %}هاتف: {{ order.branch_phone }}{% endif %}
                {% if order.branch_fax %} | فاكس: {{ order.branch_fax }}{% endif %}
            </div>
            {% endif %}
        </div>
        
        <!-- معلومات الأمر الأساسية -->
        <div class="order-info">
            <div class="order-info-grid">
                <div class="info-item">
                    <div class="info-label">رقم الأمر</div>
                    <div class="info-value">{{ order.order_number or 'غير محدد' }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">تاريخ الإصدار</div>
                    <div class="info-value">{{ order.created_date or 'غير محدد' }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">حالة الأمر</div>
                    <div class="info-value">
                        <span class="status-badge status-{{ order.order_status or 'draft' }}">
                            {% if order.order_status == 'draft' %}مسودة
                            {% elif order.order_status == 'sent' %}مرسل
                            {% elif order.order_status == 'in_progress' %}قيد التنفيذ
                            {% elif order.order_status == 'completed' %}مكتمل
                            {% elif order.order_status == 'cancelled' %}ملغي
                            {% else %}{{ order.order_status }}{% endif %}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- المعلومات الأساسية للشحنة -->
        <div class="section">
            <h3 class="section-title">📦 المعلومات الأساسية للشحنة</h3>
            <table class="data-table shipment-table">
                <thead>
                    <tr><th style="width: 40%;">البيان</th><th style="width: 60%;">القيمة</th></tr>
                </thead>
                <tbody>
                    <tr><td>رقم التتبع</td><td><strong>{{ order.tracking_number or 'غير محدد' }}</strong></td></tr>
                    <tr><td>رقم الحجز</td><td><strong>{{ order.booking_number or 'غير محدد' }}</strong></td></tr>
                    <tr><td>نوع الشحنة</td><td>{{ order.shipment_type or 'بحري' }}</td></tr>
                    <tr><td>الوزن الإجمالي</td><td>{{ order.total_weight or 'غير محدد' }}{% if order.total_weight %} كيلو{% endif %}</td></tr>
                    <tr><td>الوزن الصافي</td><td>{{ order.net_weight or 'غير محدد' }}{% if order.net_weight %} كيلو{% endif %}</td></tr>
                    <tr><td>عدد الطرود</td><td>{{ order.packages_count or 'غير محدد' }}</td></tr>
                    <tr><td>وصف البضاعة</td><td>{{ order.cargo_description or 'غير محدد' }}</td></tr>
                    <tr><td>موقع التسليم</td><td><strong>{{ order.delivery_location or 'غير محدد' }}</strong></td></tr>
                    <tr><td>التاريخ المطلوب للتخليص</td><td><strong>{{ order.expected_completion_date or 'غير محدد' }}</strong></td></tr>
                </tbody>
            </table>
        </div>
        
        <!-- بيانات الشحنة التفصيلية -->
        <div class="section">
            <h3 class="section-title">🚢 بيانات الشحنة التفصيلية</h3>
            <table class="data-table agent-table">
                <thead>
                    <tr><th style="width: 40%;">البيان</th><th style="width: 60%;">القيمة</th></tr>
                </thead>
                <tbody>
                    <tr><td>رقم بوليصة الشحن (B/L)</td><td><strong>{{ order.bl_number or 'غير محدد' }}</strong></td></tr>
                    <tr><td>رقم الحاوية</td><td><strong>{{ order.container_number or 'غير محدد' }}</strong></td></tr>
                    <tr><td>نوع الحاوية</td><td>{{ order.container_type or 'غير محدد' }}</td></tr>
                    <tr><td>حجم الحاوية</td><td>{{ order.container_size or 'غير محدد' }}</td></tr>
                    <tr><td>رقم الختم</td><td><strong>{{ order.seal_number or 'غير محدد' }}</strong></td></tr>
                    <tr><td>شركة الشحن</td><td>{{ order.shipping_company_name or 'غير محدد' }}</td></tr>
                    <tr><td>ميناء الشحن</td><td>{{ order.loading_port or 'غير محدد' }}</td></tr>
                    <tr><td>ميناء الوصول</td><td>{{ order.discharge_port or 'غير محدد' }}</td></tr>
                    <tr><td>اسم السفينة</td><td>{{ order.vessel_name or 'غير محدد' }}</td></tr>
                    <tr><td>رقم الرحلة</td><td>{{ order.voyage_number or 'غير محدد' }}</td></tr>
                </tbody>
            </table>
        </div>
        

        
        <!-- التعليمات الخاصة -->
        {% if order.special_instructions %}
        <div class="special-instructions">
            <h4>📝 تعليمات خاصة</h4>
            <p>{{ order.special_instructions }}</p>
        </div>
        {% endif %}
        
        <!-- تذييل المستند -->
        <div class="footer">
            <div>
                <strong>{{ order.branch_name or 'شركة النقل والشحن المتطورة' }}</strong>
                {% if order.branch_phone %} | الهاتف: {{ order.branch_phone }}{% endif %}
                {% if order.branch_fax %} | فاكس: {{ order.branch_fax }}{% endif %}
                {% if order.branch_po_box %} | ص.ب: {{ order.branch_po_box }}{% endif %}
            </div>
            {% if order.branch_address %}
            <div style="margin-top: 3px;">
                العنوان: {{ order.branch_address }}
            </div>
            {% endif %}
            <div style="margin-top: 5px;">
                تاريخ الطباعة: {{ datetime.now().strftime('%Y-%m-%d %H:%M') }}
                {% if order.branch_code %} | كود الفرع: {{ order.branch_code }}{% endif %}
            </div>
        </div>
    </div>

    <script>
        function downloadPDF() {
            // إخفاء أزرار التحكم مؤقتاً
            document.querySelector('.controls').style.display = 'none';

            // استخدام html2canvas لتحويل HTML إلى صورة ثم PDF
            html2canvas(document.getElementById('delivery-order-content'), {
                scale: 1.5,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff'
            }).then(function(canvas) {
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF('p', 'mm', 'a4');

                const imgData = canvas.toDataURL('image/png');
                const imgWidth = 210; // A4 width in mm
                const imgHeight = (canvas.height * imgWidth) / canvas.width;

                // إضافة الصورة مباشرة بدون تقسيم - صفحة واحدة فقط
                pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

                // تحميل PDF
                pdf.save('delivery_order_{{ order.order_number }}.pdf');

                // إظهار أزرار التحكم مرة أخرى
                document.querySelector('.controls').style.display = 'block';
            }).catch(function(error) {
                console.error('خطأ في إنشاء PDF:', error);
                alert('حدث خطأ في إنشاء PDF. يرجى استخدام خيار الطباعة بدلاً من ذلك.');

                // إظهار أزرار التحكم مرة أخرى
                document.querySelector('.controls').style.display = 'block';
            });
        }
    </script>
</body>
</html>
