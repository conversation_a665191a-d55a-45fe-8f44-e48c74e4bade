/* 
NetSuite Oracle Color Palette
لوحة ألوان NetSuite Oracle الرسمية
*/

:root {
    /* ========== NetSuite Primary Colors ========== */
    /* الألوان الأساسية لـ NetSuite */
    
    /* NetSuite Blue Family */
    --ns-blue-50: #eff6ff;
    --ns-blue-100: #dbeafe;
    --ns-blue-200: #bfdbfe;
    --ns-blue-300: #93c5fd;
    --ns-blue-400: #60a5fa;
    --ns-blue-500: #3b82f6;  /* Primary Blue */
    --ns-blue-600: #2563eb;
    --ns-blue-700: #1d4ed8;
    --ns-blue-800: #1e40af;
    --ns-blue-900: #1e3a8a;
    --ns-blue-950: #172554;
    
    /* NetSuite Oracle Blue (Official) */
    --ns-oracle-blue-light: #4a90e2;
    --ns-oracle-blue: #1f4e79;
    --ns-oracle-blue-dark: #1a3f63;
    --ns-oracle-blue-darker: #15334d;
    
    /* NetSuite Orange Family */
    --ns-orange-50: #fff7ed;
    --ns-orange-100: #ffedd5;
    --ns-orange-200: #fed7aa;
    --ns-orange-300: #fdba74;
    --ns-orange-400: #fb923c;
    --ns-orange-500: #f97316;  /* Primary Orange */
    --ns-orange-600: #ea580c;
    --ns-orange-700: #c2410c;
    --ns-orange-800: #9a3412;
    --ns-orange-900: #7c2d12;
    --ns-orange-950: #431407;
    
    /* ========== Neutral Colors ========== */
    /* الألوان المحايدة */
    
    /* Gray Scale */
    --ns-gray-50: #f9fafb;
    --ns-gray-100: #f3f4f6;
    --ns-gray-200: #e5e7eb;
    --ns-gray-300: #d1d5db;
    --ns-gray-400: #9ca3af;
    --ns-gray-500: #6b7280;
    --ns-gray-600: #4b5563;
    --ns-gray-700: #374151;
    --ns-gray-800: #1f2937;
    --ns-gray-900: #111827;
    --ns-gray-950: #030712;
    
    /* Slate Scale (Cool Gray) */
    --ns-slate-50: #f8fafc;
    --ns-slate-100: #f1f5f9;
    --ns-slate-200: #e2e8f0;
    --ns-slate-300: #cbd5e1;
    --ns-slate-400: #94a3b8;
    --ns-slate-500: #64748b;
    --ns-slate-600: #475569;
    --ns-slate-700: #334155;
    --ns-slate-800: #1e293b;
    --ns-slate-900: #0f172a;
    --ns-slate-950: #020617;
    
    /* ========== Status Colors ========== */
    /* ألوان الحالة */
    
    /* Success Green */
    --ns-success-50: #f0fdf4;
    --ns-success-100: #dcfce7;
    --ns-success-200: #bbf7d0;
    --ns-success-300: #86efac;
    --ns-success-400: #4ade80;
    --ns-success-500: #22c55e;
    --ns-success-600: #16a34a;
    --ns-success-700: #15803d;
    --ns-success-800: #166534;
    --ns-success-900: #14532d;
    --ns-success-950: #052e16;
    
    /* Warning Yellow */
    --ns-warning-50: #fefce8;
    --ns-warning-100: #fef9c3;
    --ns-warning-200: #fef08a;
    --ns-warning-300: #fde047;
    --ns-warning-400: #facc15;
    --ns-warning-500: #eab308;
    --ns-warning-600: #ca8a04;
    --ns-warning-700: #a16207;
    --ns-warning-800: #854d0e;
    --ns-warning-900: #713f12;
    --ns-warning-950: #422006;
    
    /* Error Red */
    --ns-error-50: #fef2f2;
    --ns-error-100: #fee2e2;
    --ns-error-200: #fecaca;
    --ns-error-300: #fca5a5;
    --ns-error-400: #f87171;
    --ns-error-500: #ef4444;
    --ns-error-600: #dc2626;
    --ns-error-700: #b91c1c;
    --ns-error-800: #991b1b;
    --ns-error-900: #7f1d1d;
    --ns-error-950: #450a0a;
    
    /* Info Cyan */
    --ns-info-50: #ecfeff;
    --ns-info-100: #cffafe;
    --ns-info-200: #a5f3fc;
    --ns-info-300: #67e8f9;
    --ns-info-400: #22d3ee;
    --ns-info-500: #06b6d4;
    --ns-info-600: #0891b2;
    --ns-info-700: #0e7490;
    --ns-info-800: #155e75;
    --ns-info-900: #164e63;
    --ns-info-950: #083344;
    
    /* ========== Semantic Colors ========== */
    /* الألوان الدلالية */
    
    /* Primary Colors */
    --ns-primary: var(--ns-oracle-blue);
    --ns-primary-light: var(--ns-oracle-blue-light);
    --ns-primary-dark: var(--ns-oracle-blue-dark);
    --ns-primary-contrast: #ffffff;
    
    /* Secondary Colors */
    --ns-secondary: var(--ns-orange-500);
    --ns-secondary-light: var(--ns-orange-400);
    --ns-secondary-dark: var(--ns-orange-600);
    --ns-secondary-contrast: #ffffff;
    
    /* Accent Colors */
    --ns-accent: var(--ns-blue-500);
    --ns-accent-light: var(--ns-blue-400);
    --ns-accent-dark: var(--ns-blue-600);
    --ns-accent-contrast: #ffffff;
    
    /* ========== Background Colors ========== */
    /* ألوان الخلفية */
    
    --ns-bg-primary: #ffffff;
    --ns-bg-secondary: var(--ns-gray-50);
    --ns-bg-tertiary: var(--ns-gray-100);
    --ns-bg-accent: var(--ns-blue-50);
    --ns-bg-overlay: rgba(0, 0, 0, 0.5);
    
    /* ========== Text Colors ========== */
    /* ألوان النص */
    
    --ns-text-primary: var(--ns-gray-900);
    --ns-text-secondary: var(--ns-gray-600);
    --ns-text-tertiary: var(--ns-gray-500);
    --ns-text-disabled: var(--ns-gray-400);
    --ns-text-inverse: #ffffff;
    --ns-text-link: var(--ns-blue-600);
    --ns-text-link-hover: var(--ns-blue-700);
    
    /* ========== Border Colors ========== */
    /* ألوان الحدود */
    
    --ns-border-primary: var(--ns-gray-200);
    --ns-border-secondary: var(--ns-gray-300);
    --ns-border-accent: var(--ns-blue-200);
    --ns-border-focus: var(--ns-blue-500);
    --ns-border-error: var(--ns-error-500);
    --ns-border-success: var(--ns-success-500);
    --ns-border-warning: var(--ns-warning-500);
    
    /* ========== Shadow Colors ========== */
    /* ألوان الظلال */
    
    --ns-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --ns-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --ns-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --ns-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --ns-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --ns-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --ns-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    
    /* Colored Shadows */
    --ns-shadow-blue: 0 4px 14px 0 rgba(59, 130, 246, 0.2);
    --ns-shadow-orange: 0 4px 14px 0 rgba(249, 115, 22, 0.2);
    --ns-shadow-success: 0 4px 14px 0 rgba(34, 197, 94, 0.2);
    --ns-shadow-error: 0 4px 14px 0 rgba(239, 68, 68, 0.2);
    --ns-shadow-warning: 0 4px 14px 0 rgba(234, 179, 8, 0.2);
    
    /* ========== Gradient Colors ========== */
    /* الألوان المتدرجة */
    
    --ns-gradient-primary: linear-gradient(135deg, var(--ns-oracle-blue) 0%, var(--ns-blue-600) 100%);
    --ns-gradient-secondary: linear-gradient(135deg, var(--ns-orange-500) 0%, var(--ns-orange-600) 100%);
    --ns-gradient-accent: linear-gradient(135deg, var(--ns-blue-500) 0%, var(--ns-blue-600) 100%);
    --ns-gradient-success: linear-gradient(135deg, var(--ns-success-500) 0%, var(--ns-success-600) 100%);
    --ns-gradient-warning: linear-gradient(135deg, var(--ns-warning-500) 0%, var(--ns-warning-600) 100%);
    --ns-gradient-error: linear-gradient(135deg, var(--ns-error-500) 0%, var(--ns-error-600) 100%);
    --ns-gradient-info: linear-gradient(135deg, var(--ns-info-500) 0%, var(--ns-info-600) 100%);
    
    /* Background Gradients */
    --ns-gradient-bg-light: linear-gradient(135deg, #ffffff 0%, var(--ns-gray-50) 100%);
    --ns-gradient-bg-subtle: linear-gradient(135deg, var(--ns-gray-50) 0%, var(--ns-gray-100) 100%);
    --ns-gradient-bg-hero: linear-gradient(135deg, var(--ns-oracle-blue) 0%, var(--ns-blue-700) 100%);
    
    /* ========== Interactive Colors ========== */
    /* ألوان التفاعل */
    
    /* Hover States */
    --ns-hover-primary: var(--ns-oracle-blue-dark);
    --ns-hover-secondary: var(--ns-orange-600);
    --ns-hover-accent: var(--ns-blue-600);
    --ns-hover-bg: var(--ns-gray-50);
    --ns-hover-border: var(--ns-gray-300);
    
    /* Active States */
    --ns-active-primary: var(--ns-oracle-blue-darker);
    --ns-active-secondary: var(--ns-orange-700);
    --ns-active-accent: var(--ns-blue-700);
    --ns-active-bg: var(--ns-gray-100);
    --ns-active-border: var(--ns-gray-400);
    
    /* Focus States */
    --ns-focus-ring: 0 0 0 3px rgba(59, 130, 246, 0.1);
    --ns-focus-ring-error: 0 0 0 3px rgba(239, 68, 68, 0.1);
    --ns-focus-ring-success: 0 0 0 3px rgba(34, 197, 94, 0.1);
    --ns-focus-ring-warning: 0 0 0 3px rgba(234, 179, 8, 0.1);
    
    /* ========== Component Specific Colors ========== */
    /* ألوان مخصصة للمكونات */
    
    /* Navigation */
    --ns-nav-bg: var(--ns-gradient-primary);
    --ns-nav-text: #ffffff;
    --ns-nav-hover: rgba(255, 255, 255, 0.1);
    --ns-nav-active: rgba(255, 255, 255, 0.2);
    
    /* Sidebar */
    --ns-sidebar-bg: var(--ns-gradient-bg-light);
    --ns-sidebar-text: var(--ns-text-secondary);
    --ns-sidebar-hover: var(--ns-blue-50);
    --ns-sidebar-active: var(--ns-blue-100);
    --ns-sidebar-border: var(--ns-border-primary);
    
    /* Cards */
    --ns-card-bg: #ffffff;
    --ns-card-border: var(--ns-border-primary);
    --ns-card-shadow: var(--ns-shadow);
    --ns-card-hover-shadow: var(--ns-shadow-lg);
    
    /* Tables */
    --ns-table-bg: #ffffff;
    --ns-table-header-bg: var(--ns-gray-50);
    --ns-table-border: var(--ns-border-primary);
    --ns-table-hover: var(--ns-blue-50);
    --ns-table-stripe: var(--ns-gray-50);
    
    /* Forms */
    --ns-input-bg: #ffffff;
    --ns-input-border: var(--ns-border-secondary);
    --ns-input-focus-border: var(--ns-border-focus);
    --ns-input-error-border: var(--ns-border-error);
    --ns-input-disabled-bg: var(--ns-gray-100);
    --ns-input-disabled-text: var(--ns-text-disabled);
    
    /* Buttons */
    --ns-btn-primary-bg: var(--ns-gradient-primary);
    --ns-btn-primary-text: #ffffff;
    --ns-btn-primary-hover: var(--ns-hover-primary);
    --ns-btn-primary-shadow: var(--ns-shadow-blue);
    
    --ns-btn-secondary-bg: var(--ns-gradient-secondary);
    --ns-btn-secondary-text: #ffffff;
    --ns-btn-secondary-hover: var(--ns-hover-secondary);
    --ns-btn-secondary-shadow: var(--ns-shadow-orange);
    
    /* Alerts */
    --ns-alert-success-bg: var(--ns-success-50);
    --ns-alert-success-text: var(--ns-success-800);
    --ns-alert-success-border: var(--ns-success-200);
    
    --ns-alert-warning-bg: var(--ns-warning-50);
    --ns-alert-warning-text: var(--ns-warning-800);
    --ns-alert-warning-border: var(--ns-warning-200);
    
    --ns-alert-error-bg: var(--ns-error-50);
    --ns-alert-error-text: var(--ns-error-800);
    --ns-alert-error-border: var(--ns-error-200);
    
    --ns-alert-info-bg: var(--ns-info-50);
    --ns-alert-info-text: var(--ns-info-800);
    --ns-alert-info-border: var(--ns-info-200);
}

/* ========== Dark Theme Colors ========== */
/* ألوان السمة الداكنة */

[data-theme="dark"] {
    /* Override colors for dark theme */
    --ns-bg-primary: var(--ns-gray-900);
    --ns-bg-secondary: var(--ns-gray-800);
    --ns-bg-tertiary: var(--ns-gray-700);
    --ns-bg-accent: var(--ns-blue-900);
    
    --ns-text-primary: var(--ns-gray-100);
    --ns-text-secondary: var(--ns-gray-300);
    --ns-text-tertiary: var(--ns-gray-400);
    --ns-text-disabled: var(--ns-gray-500);
    
    --ns-border-primary: var(--ns-gray-700);
    --ns-border-secondary: var(--ns-gray-600);
    
    --ns-card-bg: var(--ns-gray-800);
    --ns-sidebar-bg: var(--ns-gray-800);
    --ns-table-bg: var(--ns-gray-800);
    --ns-table-header-bg: var(--ns-gray-700);
    --ns-input-bg: var(--ns-gray-700);
}

/* ========== High Contrast Theme ========== */
/* سمة التباين العالي */

[data-theme="high-contrast"] {
    --ns-bg-primary: #000000;
    --ns-bg-secondary: #ffffff;
    --ns-text-primary: #ffffff;
    --ns-text-secondary: #000000;
    --ns-border-primary: #ffffff;
    --ns-primary: #ffff00;
    --ns-secondary: #ff00ff;
}
