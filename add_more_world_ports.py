#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة المزيد من الموانئ العالمية
Add More World Ports
"""

from database_manager import DatabaseManager
import random

def generate_more_world_ports():
    """توليد المزيد من الموانئ العالمية"""
    
    more_ports = []
    
    # موانئ جنوب أفريقيا 🇿🇦
    south_africa_ports = [
        {"code": "ZADUR", "name": "Durban", "name_ar": "ديربان", "city": "Durban", "country": "South Africa", "country_ar": "جنوب أفريقيا", "major": True},
        {"code": "ZACPT", "name": "Cape Town", "name_ar": "كيب تاون", "city": "Cape Town", "country": "South Africa", "country_ar": "جنوب أفريقيا", "major": True},
        {"code": "<PERSON><PERSON><PERSON>", "name": "Port Elizabeth", "name_ar": "بورت إليزابيث", "city": "Port Elizabeth", "country": "South Africa", "country_ar": "جنوب أفريقيا", "major": True},
        {"code": "ZARIG", "name": "Richards Bay", "name_ar": "ريتشاردز باي", "city": "Richards Bay", "country": "South Africa", "country_ar": "جنوب أفريقيا", "major": True},
        {"code": "ZASAL", "name": "Saldanha", "name_ar": "سالدانها", "city": "Saldanha", "country": "South Africa", "country_ar": "جنوب أفريقيا", "major": False},
        {"code": "ZAEAS", "name": "East London", "name_ar": "إيست لندن", "city": "East London", "country": "South Africa", "country_ar": "جنوب أفريقيا", "major": False},
    ]
    
    for port in south_africa_ports:
        more_ports.append({
            "code": port["code"], "name": port["name"], "name_ar": port["name_ar"],
            "country": port["country"], "country_ar": port["country_ar"],
            "city": port["city"], "city_ar": port["name_ar"],
            "region": "Southern Africa", "continent": "Africa", "major": port["major"],
            "lat": round(random.uniform(-35.0, -22.0), 4),
            "lng": round(random.uniform(16.0, 33.0), 4),
            "cargo_types": "حاويات,فحم,خام حديد,سيارات,فواكه"
        })
    
    # موانئ نيجيريا 🇳🇬
    nigeria_ports = [
        {"code": "NGLAG", "name": "Lagos", "name_ar": "لاغوس", "city": "Lagos", "country": "Nigeria", "country_ar": "نيجيريا", "major": True},
        {"code": "NGTIN", "name": "Tin Can Island", "name_ar": "جزيرة تين كان", "city": "Lagos", "country": "Nigeria", "country_ar": "نيجيريا", "major": True},
        {"code": "NGAPP", "name": "Apapa", "name_ar": "أبابا", "city": "Lagos", "country": "Nigeria", "country_ar": "نيجيريا", "major": True},
        {"code": "NGPHE", "name": "Port Harcourt", "name_ar": "بورت هاركورت", "city": "Port Harcourt", "country": "Nigeria", "country_ar": "نيجيريا", "major": True},
        {"code": "NGWAR", "name": "Warri", "name_ar": "واري", "city": "Warri", "country": "Nigeria", "country_ar": "نيجيريا", "major": False},
        {"code": "NGCAL", "name": "Calabar", "name_ar": "كالابار", "city": "Calabar", "country": "Nigeria", "country_ar": "نيجيريا", "major": False},
    ]
    
    for port in nigeria_ports:
        more_ports.append({
            "code": port["code"], "name": port["name"], "name_ar": port["name_ar"],
            "country": port["country"], "country_ar": port["country_ar"],
            "city": port["city"], "city_ar": port["name_ar"],
            "region": "West Africa", "continent": "Africa", "major": port["major"],
            "lat": round(random.uniform(4.0, 14.0), 4),
            "lng": round(random.uniform(3.0, 15.0), 4),
            "cargo_types": "نفط,حاويات,بضائع عامة,كاكاو"
        })
    
    # موانئ كينيا 🇰🇪
    kenya_ports = [
        {"code": "KEMBA", "name": "Mombasa", "name_ar": "مومباسا", "city": "Mombasa", "country": "Kenya", "country_ar": "كينيا", "major": True},
        {"code": "KEKIS", "name": "Kisumu", "name_ar": "كيسومو", "city": "Kisumu", "country": "Kenya", "country_ar": "كينيا", "major": False},
    ]
    
    for port in kenya_ports:
        more_ports.append({
            "code": port["code"], "name": port["name"], "name_ar": port["name_ar"],
            "country": port["country"], "country_ar": port["country_ar"],
            "city": port["city"], "city_ar": port["name_ar"],
            "region": "East Africa", "continent": "Africa", "major": port["major"],
            "lat": round(random.uniform(-5.0, 1.0), 4),
            "lng": round(random.uniform(34.0, 41.0), 4),
            "cargo_types": "حاويات,شاي,قهوة,بضائع عامة"
        })
    
    # موانئ تنزانيا 🇹🇿
    tanzania_ports = [
        {"code": "TZDAR", "name": "Dar es Salaam", "name_ar": "دار السلام", "city": "Dar es Salaam", "country": "Tanzania", "country_ar": "تنزانيا", "major": True},
        {"code": "TZMTW", "name": "Mtwara", "name_ar": "متوارا", "city": "Mtwara", "country": "Tanzania", "country_ar": "تنزانيا", "major": False},
        {"code": "TZTAN", "name": "Tanga", "name_ar": "تانغا", "city": "Tanga", "country": "Tanzania", "country_ar": "تنزانيا", "major": False},
    ]
    
    for port in tanzania_ports:
        more_ports.append({
            "code": port["code"], "name": port["name"], "name_ar": port["name_ar"],
            "country": port["country"], "country_ar": port["country_ar"],
            "city": port["city"], "city_ar": port["name_ar"],
            "region": "East Africa", "continent": "Africa", "major": port["major"],
            "lat": round(random.uniform(-11.0, -1.0), 4),
            "lng": round(random.uniform(29.0, 40.0), 4),
            "cargo_types": "حاويات,بضائع عامة,قطن"
        })
    
    # موانئ غانا 🇬🇭
    ghana_ports = [
        {"code": "GHACC", "name": "Tema", "name_ar": "تيما", "city": "Tema", "country": "Ghana", "country_ar": "غانا", "major": True},
        {"code": "GHTAM", "name": "Takoradi", "name_ar": "تاكورادي", "city": "Takoradi", "country": "Ghana", "country_ar": "غانا", "major": True},
    ]
    
    for port in ghana_ports:
        more_ports.append({
            "code": port["code"], "name": port["name"], "name_ar": port["name_ar"],
            "country": port["country"], "country_ar": port["country_ar"],
            "city": port["city"], "city_ar": port["name_ar"],
            "region": "West Africa", "continent": "Africa", "major": port["major"],
            "lat": round(random.uniform(4.0, 11.0), 4),
            "lng": round(random.uniform(-4.0, 1.0), 4),
            "cargo_types": "كاكاو,ذهب,حاويات,بضائع عامة"
        })
    
    # موانئ ساحل العاج 🇨🇮
    ivory_coast_ports = [
        {"code": "CIABJ", "name": "Abidjan", "name_ar": "أبيدجان", "city": "Abidjan", "country": "Ivory Coast", "country_ar": "ساحل العاج", "major": True},
        {"code": "CISAN", "name": "San Pedro", "name_ar": "سان بيدرو", "city": "San Pedro", "country": "Ivory Coast", "country_ar": "ساحل العاج", "major": False},
    ]
    
    for port in ivory_coast_ports:
        more_ports.append({
            "code": port["code"], "name": port["name"], "name_ar": port["name_ar"],
            "country": port["country"], "country_ar": port["country_ar"],
            "city": port["city"], "city_ar": port["name_ar"],
            "region": "West Africa", "continent": "Africa", "major": port["major"],
            "lat": round(random.uniform(4.0, 11.0), 4),
            "lng": round(random.uniform(-9.0, -2.0), 4),
            "cargo_types": "كاكاو,قهوة,حاويات,بضائع عامة"
        })
    
    # موانئ السنغال 🇸🇳
    senegal_ports = [
        {"code": "SNDKR", "name": "Dakar", "name_ar": "داكار", "city": "Dakar", "country": "Senegal", "country_ar": "السنغال", "major": True},
    ]
    
    for port in senegal_ports:
        more_ports.append({
            "code": port["code"], "name": port["name"], "name_ar": port["name_ar"],
            "country": port["country"], "country_ar": port["country_ar"],
            "city": port["city"], "city_ar": port["name_ar"],
            "region": "West Africa", "continent": "Africa", "major": port["major"],
            "lat": round(random.uniform(12.0, 17.0), 4),
            "lng": round(random.uniform(-18.0, -11.0), 4),
            "cargo_types": "أسماك,فوسفات,حاويات,بضائع عامة"
        })
    
    # موانئ أنغولا 🇦🇴
    angola_ports = [
        {"code": "AOLAD", "name": "Luanda", "name_ar": "لواندا", "city": "Luanda", "country": "Angola", "country_ar": "أنغولا", "major": True},
        {"code": "AOLAB", "name": "Lobito", "name_ar": "لوبيتو", "city": "Lobito", "country": "Angola", "country_ar": "أنغولا", "major": True},
        {"code": "AONAM", "name": "Namibe", "name_ar": "ناميبي", "city": "Namibe", "country": "Angola", "country_ar": "أنغولا", "major": False},
    ]
    
    for port in angola_ports:
        more_ports.append({
            "code": port["code"], "name": port["name"], "name_ar": port["name_ar"],
            "country": port["country"], "country_ar": port["country_ar"],
            "city": port["city"], "city_ar": port["name_ar"],
            "region": "Southern Africa", "continent": "Africa", "major": port["major"],
            "lat": round(random.uniform(-18.0, -4.0), 4),
            "lng": round(random.uniform(11.0, 18.0), 4),
            "cargo_types": "نفط,ماس,حديد,بضائع عامة"
        })
    
    # موانئ موزمبيق 🇲🇿
    mozambique_ports = [
        {"code": "MZMPM", "name": "Maputo", "name_ar": "مابوتو", "city": "Maputo", "country": "Mozambique", "country_ar": "موزمبيق", "major": True},
        {"code": "MZBEI", "name": "Beira", "name_ar": "بيرا", "city": "Beira", "country": "Mozambique", "country_ar": "موزمبيق", "major": True},
        {"code": "MZNAC", "name": "Nacala", "name_ar": "ناكالا", "city": "Nacala", "country": "Mozambique", "country_ar": "موزمبيق", "major": False},
    ]
    
    for port in mozambique_ports:
        more_ports.append({
            "code": port["code"], "name": port["name"], "name_ar": port["name_ar"],
            "country": port["country"], "country_ar": port["country_ar"],
            "city": port["city"], "city_ar": port["name_ar"],
            "region": "Southern Africa", "continent": "Africa", "major": port["major"],
            "lat": round(random.uniform(-27.0, -10.0), 4),
            "lng": round(random.uniform(32.0, 41.0), 4),
            "cargo_types": "فحم,ألمنيوم,حاويات,بضائع عامة"
        })
    
    # موانئ إثيوبيا (عبر جيبوتي) 🇪🇹
    ethiopia_ports = [
        {"code": "ETADD", "name": "Addis Ababa (via Djibouti)", "name_ar": "أديس أبابا (عبر جيبوتي)", "city": "Addis Ababa", "country": "Ethiopia", "country_ar": "إثيوبيا", "major": False},
    ]
    
    for port in ethiopia_ports:
        more_ports.append({
            "code": port["code"], "name": port["name"], "name_ar": port["name_ar"],
            "country": port["country"], "country_ar": port["country_ar"],
            "city": port["city"], "city_ar": port["name_ar"],
            "region": "East Africa", "continent": "Africa", "major": port["major"],
            "lat": round(random.uniform(3.0, 15.0), 4),
            "lng": round(random.uniform(33.0, 48.0), 4),
            "cargo_types": "قهوة,جلود,بضائع عامة"
        })
    
    # موانئ أستراليا 🇦🇺
    australia_ports = [
        {"code": "AUSYD", "name": "Sydney", "name_ar": "سيدني", "city": "Sydney", "country": "Australia", "country_ar": "أستراليا", "major": True},
        {"code": "AUMEL", "name": "Melbourne", "name_ar": "ملبورن", "city": "Melbourne", "country": "Australia", "country_ar": "أستراليا", "major": True},
        {"code": "AUBNE", "name": "Brisbane", "name_ar": "بريسبان", "city": "Brisbane", "country": "Australia", "country_ar": "أستراليا", "major": True},
        {"code": "AUFRE", "name": "Fremantle", "name_ar": "فريمانتل", "city": "Perth", "country": "Australia", "country_ar": "أستراليا", "major": True},
        {"code": "AUADE", "name": "Adelaide", "name_ar": "أديلايد", "city": "Adelaide", "country": "Australia", "country_ar": "أستراليا", "major": False},
        {"code": "AUDAR", "name": "Darwin", "name_ar": "داروين", "city": "Darwin", "country": "Australia", "country_ar": "أستراليا", "major": False},
    ]
    
    for port in australia_ports:
        more_ports.append({
            "code": port["code"], "name": port["name"], "name_ar": port["name_ar"],
            "country": port["country"], "country_ar": port["country_ar"],
            "city": port["city"], "city_ar": port["name_ar"],
            "region": "Oceania", "continent": "Australia", "major": port["major"],
            "lat": round(random.uniform(-43.0, -10.0), 4),
            "lng": round(random.uniform(113.0, 154.0), 4),
            "cargo_types": "خام حديد,فحم,حاويات,لحوم,صوف"
        })
    
    # موانئ نيوزيلندا 🇳🇿
    new_zealand_ports = [
        {"code": "NZAKL", "name": "Auckland", "name_ar": "أوكلاند", "city": "Auckland", "country": "New Zealand", "country_ar": "نيوزيلندا", "major": True},
        {"code": "NZTRG", "name": "Tauranga", "name_ar": "تاورانغا", "city": "Tauranga", "country": "New Zealand", "country_ar": "نيوزيلندا", "major": True},
        {"code": "NZWEL", "name": "Wellington", "name_ar": "ويلينغتون", "city": "Wellington", "country": "New Zealand", "country_ar": "نيوزيلندا", "major": False},
        {"code": "NZLYT", "name": "Lyttelton", "name_ar": "ليتلتون", "city": "Christchurch", "country": "New Zealand", "country_ar": "نيوزيلندا", "major": False},
    ]
    
    for port in new_zealand_ports:
        more_ports.append({
            "code": port["code"], "name": port["name"], "name_ar": port["name_ar"],
            "country": port["country"], "country_ar": port["country_ar"],
            "city": port["city"], "city_ar": port["name_ar"],
            "region": "Oceania", "continent": "Australia", "major": port["major"],
            "lat": round(random.uniform(-47.0, -34.0), 4),
            "lng": round(random.uniform(166.0, 179.0), 4),
            "cargo_types": "منتجات ألبان,لحوم,صوف,حاويات"
        })
    
    # موانئ كندا 🇨🇦
    canada_ports = [
        {"code": "CAVAN", "name": "Vancouver", "name_ar": "فانكوفر", "city": "Vancouver", "country": "Canada", "country_ar": "كندا", "major": True},
        {"code": "CAHAL", "name": "Halifax", "name_ar": "هاليفاكس", "city": "Halifax", "country": "Canada", "country_ar": "كندا", "major": True},
        {"code": "CAPRR", "name": "Prince Rupert", "name_ar": "برينس روبرت", "city": "Prince Rupert", "country": "Canada", "country_ar": "كندا", "major": True},
        {"code": "CAMTR", "name": "Montreal", "name_ar": "مونتريال", "city": "Montreal", "country": "Canada", "country_ar": "كندا", "major": True},
        {"code": "CATOR", "name": "Toronto", "name_ar": "تورونتو", "city": "Toronto", "country": "Canada", "country_ar": "كندا", "major": False},
        {"code": "CAQBC", "name": "Quebec", "name_ar": "كيبيك", "city": "Quebec", "country": "Canada", "country_ar": "كندا", "major": False},
    ]
    
    for port in canada_ports:
        more_ports.append({
            "code": port["code"], "name": port["name"], "name_ar": port["name_ar"],
            "country": port["country"], "country_ar": port["country_ar"],
            "city": port["city"], "city_ar": port["name_ar"],
            "region": "North America", "continent": "North America", "major": port["major"],
            "lat": round(random.uniform(42.0, 70.0), 4),
            "lng": round(random.uniform(-141.0, -52.0), 4),
            "cargo_types": "حبوب,خشب,حاويات,بضائع عامة"
        })
    
    # موانئ المكسيك 🇲🇽
    mexico_ports = [
        {"code": "MXVER", "name": "Veracruz", "name_ar": "فيراكروز", "city": "Veracruz", "country": "Mexico", "country_ar": "المكسيك", "major": True},
        {"code": "MXMAN", "name": "Manzanillo", "name_ar": "مانزانيلو", "city": "Manzanillo", "country": "Mexico", "country_ar": "المكسيك", "major": True},
        {"code": "MXLZC", "name": "Lazaro Cardenas", "name_ar": "لازارو كارديناس", "city": "Lazaro Cardenas", "country": "Mexico", "country_ar": "المكسيك", "major": True},
        {"code": "MXALT", "name": "Altamira", "name_ar": "ألتاميرا", "city": "Altamira", "country": "Mexico", "country_ar": "المكسيك", "major": True},
        {"code": "MXENS", "name": "Ensenada", "name_ar": "إنسينادا", "city": "Ensenada", "country": "Mexico", "country_ar": "المكسيك", "major": False},
    ]
    
    for port in mexico_ports:
        more_ports.append({
            "code": port["code"], "name": port["name"], "name_ar": port["name_ar"],
            "country": port["country"], "country_ar": port["country_ar"],
            "city": port["city"], "city_ar": port["name_ar"],
            "region": "North America", "continent": "North America", "major": port["major"],
            "lat": round(random.uniform(14.0, 33.0), 4),
            "lng": round(random.uniform(-118.0, -86.0), 4),
            "cargo_types": "نفط,حاويات,سيارات,بضائع عامة"
        })
    
    print(f"✅ تم إنشاء {len(more_ports)} ميناء إضافي من جميع أنحاء العالم")
    return more_ports

def insert_more_world_ports():
    """إدراج المزيد من الموانئ العالمية في قاعدة البيانات"""
    db_manager = DatabaseManager()
    
    try:
        print("🌍 بدء إضافة المزيد من الموانئ العالمية...")
        
        # توليد الموانئ الإضافية
        more_ports = generate_more_world_ports()
        
        print(f"📊 بدء إدراج {len(more_ports)} ميناء في قاعدة البيانات...")
        
        inserted_count = 0
        updated_count = 0
        
        for port in more_ports:
            try:
                # التحقق من وجود الميناء
                existing = db_manager.execute_query(
                    "SELECT id FROM world_ports_comprehensive WHERE port_code = :port_code",
                    {'port_code': port['code']}
                )
                
                if existing:
                    # تحديث الميناء الموجود
                    update_sql = """
                        UPDATE world_ports_comprehensive 
                        SET port_name = :port_name, country = :country, city = :city,
                            port_name_arabic = :port_name_arabic, country_arabic = :country_arabic,
                            city_arabic = :city_arabic, region = :region, continent = :continent,
                            major_port = :major_port, latitude = :latitude, longitude = :longitude,
                            popularity_score = :popularity_score, cargo_types = :cargo_types,
                            last_updated = CURRENT_TIMESTAMP
                        WHERE port_code = :port_code
                    """
                    
                    popularity_score = 100 if port.get('major', False) else 50
                    
                    db_manager.execute_update(update_sql, {
                        'port_code': port['code'],
                        'port_name': port['name'],
                        'country': port['country'],
                        'city': port['city'],
                        'port_name_arabic': port.get('name_ar', ''),
                        'country_arabic': port.get('country_ar', ''),
                        'city_arabic': port.get('city_ar', ''),
                        'region': port.get('region', ''),
                        'continent': port.get('continent', ''),
                        'major_port': 1 if port.get('major', False) else 0,
                        'latitude': port.get('lat'),
                        'longitude': port.get('lng'),
                        'popularity_score': popularity_score,
                        'cargo_types': port.get('cargo_types', '')
                    })
                    updated_count += 1
                    
                else:
                    # إدراج ميناء جديد
                    insert_sql = """
                        INSERT INTO world_ports_comprehensive (
                            port_code, port_name, country, city,
                            port_name_arabic, country_arabic, city_arabic, 
                            region, continent, major_port, latitude, longitude,
                            popularity_score, cargo_types, is_active, created_at
                        ) VALUES (
                            :port_code, :port_name, :country, :city,
                            :port_name_arabic, :country_arabic, :city_arabic,
                            :region, :continent, :major_port, :latitude, :longitude,
                            :popularity_score, :cargo_types, 1, CURRENT_TIMESTAMP
                        )
                    """
                    
                    popularity_score = 100 if port.get('major', False) else 50
                    
                    db_manager.execute_update(insert_sql, {
                        'port_code': port['code'],
                        'port_name': port['name'],
                        'country': port['country'],
                        'city': port['city'],
                        'port_name_arabic': port.get('name_ar', ''),
                        'country_arabic': port.get('country_ar', ''),
                        'city_arabic': port.get('city_ar', ''),
                        'region': port.get('region', ''),
                        'continent': port.get('continent', ''),
                        'major_port': 1 if port.get('major', False) else 0,
                        'latitude': port.get('lat'),
                        'longitude': port.get('lng'),
                        'popularity_score': popularity_score,
                        'cargo_types': port.get('cargo_types', '')
                    })
                    inserted_count += 1
                
                if (inserted_count + updated_count) % 25 == 0:
                    print(f"✅ تم معالجة {inserted_count + updated_count} ميناء...")
                    
            except Exception as e:
                print(f"⚠️ خطأ في معالجة الميناء {port['code']}: {e}")
                continue
        
        print(f"🎉 تم الانتهاء! تم إدراج {inserted_count} ميناء جديد وتحديث {updated_count} ميناء موجود")
        print(f"📊 إجمالي الموانئ المضافة: {inserted_count + updated_count}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إدراج الموانئ: {e}")
        return False
    
    finally:
        db_manager.close()

if __name__ == "__main__":
    print("🚀 بدء إضافة المزيد من الموانئ العالمية...")
    
    success = insert_more_world_ports()
    
    if success:
        print(f"\n🎉 تم إضافة المزيد من الموانئ العالمية بنجاح!")
        print("📋 الآن لديك موانئ شاملة من:")
        print("   🌍 أفريقيا: جنوب أفريقيا، نيجيريا، كينيا، تنزانيا، غانا")
        print("   🌏 أوقيانوسيا: أستراليا، نيوزيلندا")
        print("   🌎 أمريكا الشمالية: كندا، المكسيك")
        print("   📊 المجموع: أكثر من 300 ميناء حول العالم!")
    else:
        print("\n❌ فشل في إضافة الموانئ")
