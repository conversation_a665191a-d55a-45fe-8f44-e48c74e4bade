-- =====================================================
-- الإجراءات المخزنة لنظام إدارة أرصدة الموردين
-- Stored Procedures for Supplier Balance Management System
-- =====================================================

-- 1. إجراء تحديث رصيد المورد
CREATE OR REPLACE PROCEDURE UPDATE_SUPPLIER_BALANCE(
    p_account_id IN NUMBER,
    p_currency_code IN VARCHAR2
) AS
    v_opening_balance NUMBER(15,2) := 0;
    v_total_debits NUMBER(15,2) := 0;
    v_total_credits NUMBER(15,2) := 0;
    v_current_balance NUMBER(15,2) := 0;
    v_last_transaction_date DATE;
    v_last_payment_date DATE;
    v_transaction_count NUMBER := 0;
    v_largest_transaction NUMBER(15,2) := 0;
    
    -- متغيرات تحليل الاستحقاقات
    v_current_due NUMBER(15,2) := 0;
    v_overdue_1_30 NUMBER(15,2) := 0;
    v_overdue_31_60 NUMBER(15,2) := 0;
    v_overdue_61_90 NUMBER(15,2) := 0;
    v_overdue_over_90 NUMBER(15,2) := 0;
    
    -- متغيرات مؤشرات الأداء
    v_avg_payment_days NUMBER(5,2) := 0;
    v_payment_count NUMBER := 0;
    v_total_payment_days NUMBER := 0;
BEGIN
    -- حساب الإجماليات
    SELECT 
        SUM(CASE WHEN transaction_type = 'OPENING_BALANCE' THEN debit_amount - credit_amount ELSE 0 END),
        SUM(debit_amount),
        SUM(credit_amount),
        SUM(debit_amount - credit_amount),
        MAX(transaction_date),
        COUNT(*),
        MAX(original_amount)
    INTO v_opening_balance, v_total_debits, v_total_credits, v_current_balance,
         v_last_transaction_date, v_transaction_count, v_largest_transaction
    FROM SUPPLIER_TRANSACTIONS
    WHERE account_id = p_account_id
    AND currency_code = p_currency_code
    AND status = 'POSTED';
    
    -- حساب آخر تاريخ دفع
    SELECT MAX(transaction_date)
    INTO v_last_payment_date
    FROM SUPPLIER_TRANSACTIONS
    WHERE account_id = p_account_id
    AND currency_code = p_currency_code
    AND transaction_type IN ('PAYMENT', 'CREDIT_NOTE')
    AND status = 'POSTED';
    
    -- تحليل الاستحقاقات
    SELECT 
        SUM(CASE WHEN due_date >= SYSDATE THEN debit_amount - credit_amount ELSE 0 END),
        SUM(CASE WHEN due_date < SYSDATE AND due_date >= SYSDATE - 30 THEN debit_amount - credit_amount ELSE 0 END),
        SUM(CASE WHEN due_date < SYSDATE - 30 AND due_date >= SYSDATE - 60 THEN debit_amount - credit_amount ELSE 0 END),
        SUM(CASE WHEN due_date < SYSDATE - 60 AND due_date >= SYSDATE - 90 THEN debit_amount - credit_amount ELSE 0 END),
        SUM(CASE WHEN due_date < SYSDATE - 90 THEN debit_amount - credit_amount ELSE 0 END)
    INTO v_current_due, v_overdue_1_30, v_overdue_31_60, v_overdue_61_90, v_overdue_over_90
    FROM SUPPLIER_TRANSACTIONS
    WHERE account_id = p_account_id
    AND currency_code = p_currency_code
    AND status = 'POSTED'
    AND transaction_type IN ('INVOICE', 'DEBIT_NOTE')
    AND debit_amount - credit_amount > 0;
    
    -- حساب متوسط أيام الدفع
    SELECT COUNT(*), AVG(transaction_date - due_date)
    INTO v_payment_count, v_avg_payment_days
    FROM SUPPLIER_TRANSACTIONS
    WHERE account_id = p_account_id
    AND currency_code = p_currency_code
    AND transaction_type IN ('PAYMENT', 'CREDIT_NOTE')
    AND status = 'POSTED'
    AND due_date IS NOT NULL;
    
    -- تحديث أو إنشاء رصيد المورد
    MERGE INTO SUPPLIER_BALANCES sb
    USING (
        SELECT p_account_id as account_id, p_currency_code as currency_code FROM DUAL
    ) src ON (sb.account_id = src.account_id AND sb.currency_code = src.currency_code)
    WHEN MATCHED THEN
        UPDATE SET
            opening_balance = v_opening_balance,
            current_balance = v_current_balance,
            available_balance = v_current_balance,
            total_debits = v_total_debits,
            total_credits = v_total_credits,
            last_transaction_date = v_last_transaction_date,
            last_payment_date = v_last_payment_date,
            largest_transaction = v_largest_transaction,
            transaction_count = v_transaction_count,
            current_due = v_current_due,
            overdue_1_30 = v_overdue_1_30,
            overdue_31_60 = v_overdue_31_60,
            overdue_61_90 = v_overdue_61_90,
            overdue_over_90 = v_overdue_over_90,
            average_payment_days = v_avg_payment_days,
            last_updated = CURRENT_TIMESTAMP
    WHEN NOT MATCHED THEN
        INSERT (
            account_id, currency_code, opening_balance, current_balance, available_balance,
            total_debits, total_credits, last_transaction_date, last_payment_date,
            largest_transaction, transaction_count, current_due, overdue_1_30,
            overdue_31_60, overdue_61_90, overdue_over_90, average_payment_days,
            last_updated
        ) VALUES (
            p_account_id, p_currency_code, v_opening_balance, v_current_balance, v_current_balance,
            v_total_debits, v_total_credits, v_last_transaction_date, v_last_payment_date,
            v_largest_transaction, v_transaction_count, v_current_due, v_overdue_1_30,
            v_overdue_31_60, v_overdue_61_90, v_overdue_over_90, v_avg_payment_days,
            CURRENT_TIMESTAMP
        );
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END UPDATE_SUPPLIER_BALANCE;

-- 2. إجراء إنشاء دورة مطابقة جديدة
CREATE OR REPLACE PROCEDURE CREATE_RECONCILIATION_CYCLE(
    p_cycle_name IN VARCHAR2,
    p_period_from IN DATE,
    p_period_to IN DATE,
    p_cycle_type IN VARCHAR2 DEFAULT 'MONTHLY',
    p_scope IN VARCHAR2 DEFAULT 'ALL',
    p_created_by IN NUMBER,
    p_cycle_id OUT NUMBER
) AS
    v_total_accounts NUMBER := 0;
BEGIN
    -- إنشاء دورة المطابقة
    INSERT INTO RECONCILIATION_CYCLES (
        cycle_name, period_from, period_to, cycle_type, scope,
        status, created_by, created_date
    ) VALUES (
        p_cycle_name, p_period_from, p_period_to, p_cycle_type, p_scope,
        'OPEN', p_created_by, CURRENT_TIMESTAMP
    ) RETURNING cycle_id INTO p_cycle_id;
    
    -- حساب عدد الحسابات المشمولة
    IF p_scope = 'ALL' THEN
        SELECT COUNT(*)
        INTO v_total_accounts
        FROM SUPPLIER_ACCOUNTS
        WHERE account_status = 'ACTIVE';
    ELSIF p_scope = 'HIGH_VALUE' THEN
        SELECT COUNT(DISTINCT sa.account_id)
        INTO v_total_accounts
        FROM SUPPLIER_ACCOUNTS sa
        JOIN SUPPLIER_BALANCES sb ON sa.account_id = sb.account_id
        WHERE sa.account_status = 'ACTIVE'
        AND ABS(sb.current_balance) > 10000;
    ELSIF p_scope = 'HIGH_RISK' THEN
        SELECT COUNT(*)
        INTO v_total_accounts
        FROM SUPPLIER_ACCOUNTS
        WHERE account_status = 'ACTIVE'
        AND risk_rating IN ('HIGH', 'CRITICAL');
    END IF;
    
    -- تحديث عدد الحسابات في الدورة
    UPDATE RECONCILIATION_CYCLES
    SET total_accounts = v_total_accounts
    WHERE cycle_id = p_cycle_id;
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END CREATE_RECONCILIATION_CYCLE;

-- 3. إجراء معالجة عنصر مطابقة
CREATE OR REPLACE PROCEDURE PROCESS_RECONCILIATION_ITEM(
    p_cycle_id IN NUMBER,
    p_account_id IN NUMBER,
    p_supplier_opening_balance IN NUMBER,
    p_supplier_debits IN NUMBER,
    p_supplier_credits IN NUMBER,
    p_supplier_closing_balance IN NUMBER,
    p_supplier_statement_date IN DATE,
    p_supplier_statement_ref IN VARCHAR2,
    p_processed_by IN NUMBER,
    p_item_id OUT NUMBER
) AS
    v_system_opening_balance NUMBER(15,2) := 0;
    v_system_debits NUMBER(15,2) := 0;
    v_system_credits NUMBER(15,2) := 0;
    v_system_closing_balance NUMBER(15,2) := 0;
    v_period_from DATE;
    v_period_to DATE;
BEGIN
    -- الحصول على فترة المطابقة
    SELECT period_from, period_to
    INTO v_period_from, v_period_to
    FROM RECONCILIATION_CYCLES
    WHERE cycle_id = p_cycle_id;
    
    -- حساب أرصدة النظام للفترة المحددة
    SELECT 
        SUM(CASE WHEN transaction_date < v_period_from THEN debit_amount - credit_amount ELSE 0 END),
        SUM(CASE WHEN transaction_date BETWEEN v_period_from AND v_period_to THEN debit_amount ELSE 0 END),
        SUM(CASE WHEN transaction_date BETWEEN v_period_from AND v_period_to THEN credit_amount ELSE 0 END),
        SUM(debit_amount - credit_amount)
    INTO v_system_opening_balance, v_system_debits, v_system_credits, v_system_closing_balance
    FROM SUPPLIER_TRANSACTIONS
    WHERE account_id = p_account_id
    AND status = 'POSTED'
    AND transaction_date <= v_period_to;
    
    -- إنشاء عنصر المطابقة
    INSERT INTO RECONCILIATION_ITEMS (
        cycle_id, account_id,
        system_opening_balance, system_debits, system_credits, system_closing_balance,
        supplier_opening_balance, supplier_debits, supplier_credits, supplier_closing_balance,
        supplier_statement_date, supplier_statement_ref,
        processed_by, processed_date
    ) VALUES (
        p_cycle_id, p_account_id,
        v_system_opening_balance, v_system_debits, v_system_credits, v_system_closing_balance,
        p_supplier_opening_balance, p_supplier_debits, p_supplier_credits, p_supplier_closing_balance,
        p_supplier_statement_date, p_supplier_statement_ref,
        p_processed_by, CURRENT_TIMESTAMP
    ) RETURNING item_id INTO p_item_id;
    
    -- تحديث عداد الحسابات المعالجة
    UPDATE RECONCILIATION_CYCLES
    SET processed_accounts = processed_accounts + 1
    WHERE cycle_id = p_cycle_id;
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END PROCESS_RECONCILIATION_ITEM;

-- 4. إجراء إنشاء كشف حساب
CREATE OR REPLACE PROCEDURE GENERATE_STATEMENT(
    p_account_id IN NUMBER,
    p_template_id IN NUMBER,
    p_period_from IN DATE,
    p_period_to IN DATE,
    p_generated_by IN NUMBER,
    p_statement_id OUT NUMBER
) AS
    v_opening_balance NUMBER(15,2) := 0;
    v_closing_balance NUMBER(15,2) := 0;
    v_total_debits NUMBER(15,2) := 0;
    v_total_credits NUMBER(15,2) := 0;
    v_transaction_count NUMBER := 0;
    v_overdue_amount NUMBER(15,2) := 0;
    v_current_due NUMBER(15,2) := 0;
    v_statement_number VARCHAR2(50);
BEGIN
    -- حساب الأرصدة والإحصائيات
    SELECT 
        SUM(CASE WHEN transaction_date < p_period_from THEN debit_amount - credit_amount ELSE 0 END),
        SUM(CASE WHEN transaction_date BETWEEN p_period_from AND p_period_to THEN debit_amount ELSE 0 END),
        SUM(CASE WHEN transaction_date BETWEEN p_period_from AND p_period_to THEN credit_amount ELSE 0 END),
        COUNT(CASE WHEN transaction_date BETWEEN p_period_from AND p_period_to THEN 1 END)
    INTO v_opening_balance, v_total_debits, v_total_credits, v_transaction_count
    FROM SUPPLIER_TRANSACTIONS
    WHERE account_id = p_account_id
    AND status = 'POSTED'
    AND transaction_date <= p_period_to;
    
    v_closing_balance := v_opening_balance + v_total_debits - v_total_credits;
    
    -- حساب المبالغ المستحقة والمتأخرة
    SELECT 
        SUM(CASE WHEN due_date >= SYSDATE THEN debit_amount - credit_amount ELSE 0 END),
        SUM(CASE WHEN due_date < SYSDATE THEN debit_amount - credit_amount ELSE 0 END)
    INTO v_current_due, v_overdue_amount
    FROM SUPPLIER_TRANSACTIONS
    WHERE account_id = p_account_id
    AND status = 'POSTED'
    AND transaction_type IN ('INVOICE', 'DEBIT_NOTE')
    AND debit_amount - credit_amount > 0;
    
    -- إنشاء رقم كشف فريد
    v_statement_number := 'STMT' || TO_CHAR(SYSDATE, 'YYYYMMDD') || LPAD(STATEMENT_HISTORY_SEQ.NEXTVAL, 4, '0');
    
    -- إنشاء سجل الكشف
    INSERT INTO STATEMENT_HISTORY (
        account_id, template_id, statement_date, period_from, period_to,
        statement_number, opening_balance, closing_balance, total_debits, total_credits,
        transaction_count, overdue_amount, current_due, generated_by, generated_date
    ) VALUES (
        p_account_id, p_template_id, SYSDATE, p_period_from, p_period_to,
        v_statement_number, v_opening_balance, v_closing_balance, v_total_debits, v_total_credits,
        v_transaction_count, v_overdue_amount, v_current_due, p_generated_by, CURRENT_TIMESTAMP
    ) RETURNING statement_id INTO p_statement_id;
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END GENERATE_STATEMENT;

-- رسالة نجاح
SELECT 'تم إنشاء الإجراءات المخزنة بنجاح!' as status FROM dual;

COMMIT;
