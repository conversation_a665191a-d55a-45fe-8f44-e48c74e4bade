#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت لإضافة عمود حالة العقد خطوة بخطوة
"""

import sys
import os
sys.path.append('.')

from oracle_manager import OracleManager

def main():
    try:
        print("🔧 بدء إضافة عمود حالة العقد...")
        
        oracle_manager = OracleManager()
        
        # الخطوة 1: إضافة العمود
        print("\n📝 الخطوة 1: إضافة عمود CONTRACT_STATUS...")
        try:
            sql1 = "ALTER TABLE CONTRACTS ADD (CONTRACT_STATUS VARCHAR2(20) DEFAULT 'DRAFT' NOT NULL)"
            oracle_manager.execute_update(sql1)
            print("✅ تم إضافة العمود بنجاح")
        except Exception as e:
            if "already exists" in str(e) or "ORA-00955" in str(e):
                print("⚠️ العمود موجود مسبقاً")
            else:
                print(f"❌ خطأ في إضافة العمود: {e}")
                return 1
        
        # الخطوة 2: إضافة قيد التحقق
        print("\n🔒 الخطوة 2: إضافة قيد التحقق...")
        try:
            sql2 = "ALTER TABLE CONTRACTS ADD CONSTRAINT CHK_CONTRACT_STATUS CHECK (CONTRACT_STATUS IN ('DRAFT', 'APPROVED', 'PARTIALLY_EXECUTED', 'FULLY_EXECUTED'))"
            oracle_manager.execute_update(sql2)
            print("✅ تم إضافة قيد التحقق بنجاح")
        except Exception as e:
            if "already exists" in str(e) or "ORA-00955" in str(e):
                print("⚠️ قيد التحقق موجود مسبقاً")
            else:
                print(f"❌ خطأ في إضافة قيد التحقق: {e}")
        
        # الخطوة 3: إضافة فهرس
        print("\n📊 الخطوة 3: إضافة فهرس للأداء...")
        try:
            sql3 = "CREATE INDEX IDX_CONTRACTS_STATUS ON CONTRACTS(CONTRACT_STATUS)"
            oracle_manager.execute_update(sql3)
            print("✅ تم إضافة الفهرس بنجاح")
        except Exception as e:
            if "already exists" in str(e) or "ORA-00955" in str(e):
                print("⚠️ الفهرس موجود مسبقاً")
            else:
                print(f"❌ خطأ في إضافة الفهرس: {e}")
        
        # الخطوة 4: إضافة تعليق
        print("\n💬 الخطوة 4: إضافة تعليق للعمود...")
        try:
            sql4 = "COMMENT ON COLUMN CONTRACTS.CONTRACT_STATUS IS 'حالة العقد: DRAFT=مسودة, APPROVED=معتمد, PARTIALLY_EXECUTED=منفذ جزئياً, FULLY_EXECUTED=منفذ كلياً'"
            oracle_manager.execute_update(sql4)
            print("✅ تم إضافة التعليق بنجاح")
        except Exception as e:
            print(f"⚠️ تحذير في إضافة التعليق: {e}")
        
        # الخطوة 5: تحديث العقود الموجودة
        print("\n🔄 الخطوة 5: تحديث العقود الموجودة...")
        try:
            sql5 = """UPDATE CONTRACTS 
                     SET CONTRACT_STATUS = CASE 
                         WHEN IS_USED = 1 THEN 'APPROVED'
                         ELSE 'DRAFT'
                     END"""
            result = oracle_manager.execute_update(sql5)
            print(f"✅ تم تحديث {result} عقد")
        except Exception as e:
            print(f"❌ خطأ في تحديث العقود: {e}")
        
        # الخطوة 6: عرض النتائج
        print("\n📊 الخطوة 6: عرض النتائج...")
        try:
            query = """
            SELECT 
                CONTRACT_ID,
                CONTRACT_NUMBER,
                CONTRACT_STATUS,
                IS_USED,
                CASE CONTRACT_STATUS
                    WHEN 'DRAFT' THEN 'مسودة'
                    WHEN 'APPROVED' THEN 'معتمد'
                    WHEN 'PARTIALLY_EXECUTED' THEN 'منفذ جزئياً'
                    WHEN 'FULLY_EXECUTED' THEN 'منفذ كلياً'
                END AS STATUS_ARABIC
            FROM CONTRACTS
            ORDER BY CONTRACT_ID
            """
            
            results = oracle_manager.execute_query(query)
            
            print("\n" + "="*80)
            print(f"{'ID':<5} {'رقم العقد':<15} {'الحالة':<20} {'مُستخدم':<10} {'الحالة بالعربية':<15}")
            print("="*80)
            
            for row in results:
                contract_id, contract_number, status, is_used, status_arabic = row
                used_text = "نعم" if is_used == 1 else "لا"
                print(f"{contract_id:<5} {contract_number:<15} {status:<20} {used_text:<10} {status_arabic:<15}")
            
            print("="*80)
            print(f"✅ تم العثور على {len(results)} عقد")
            
        except Exception as e:
            print(f"❌ خطأ في عرض النتائج: {e}")
        
        print("\n🎉 تم إنجاز إضافة عمود حالة العقد بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
