/**
 * نظام إدارة مدفوعات الموردين
 * Supplier Payments Management System
 */

// متغيرات عامة
let currentFilters = {};
let outstandingInvoices = [];

/**
 * تحميل إحصائيات لوحة المعلومات
 */
function loadDashboardStats() {
    $.ajax({
        url: '/api/suppliers/transfers/reports/dashboard-stats',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const stats = response.stats;
                $('#pendingCount').text(stats.payments.pending);
                $('#approvedCount').text(stats.payments.approved);
                $('#completedCount').text(stats.payments.completed);
                $('#totalAmount').text(formatCurrency(stats.payments.total_amount_base));
            }
        },
        error: function() {
            showAlert('خطأ في تحميل الإحصائيات', 'error');
        }
    });
}

/**
 * تحميل قائمة الموردين
 */
function loadSuppliers() {
    $.ajax({
        url: '/api/suppliers',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const suppliers = response.suppliers;
                const supplierSelect = $('#supplierSelect');
                const supplierFilter = $('#supplierFilter');
                
                supplierSelect.empty().append('<option value="">اختر المورد</option>');
                supplierFilter.empty().append('<option value="">جميع الموردين</option>');
                
                suppliers.forEach(function(supplier) {
                    const option = `<option value="${supplier.id}">${supplier.name_ar} (${supplier.supplier_code})</option>`;
                    supplierSelect.append(option);
                    supplierFilter.append(option);
                });
            }
        },
        error: function() {
            showAlert('خطأ في تحميل قائمة الموردين', 'error');
        }
    });
}

/**
 * تحميل قائمة العملات
 */
function loadCurrencies() {
    $.ajax({
        url: '/api/suppliers/transfers/currencies',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const currencies = response.currencies;
                const currencySelect = $('#currencySelect');
                const currencyFilter = $('#currencyFilter');
                
                currencySelect.empty().append('<option value="">اختر العملة</option>');
                currencyFilter.empty().append('<option value="">جميع العملات</option>');
                
                currencies.forEach(function(currency) {
                    const option = `<option value="${currency.code}">${currency.name_ar} (${currency.symbol})</option>`;
                    currencySelect.append(option);
                    currencyFilter.append(option);
                });
            }
        },
        error: function() {
            showAlert('خطأ في تحميل قائمة العملات', 'error');
        }
    });
}

/**
 * تحميل قائمة الصرافين والبنوك
 */
function loadMoneyChangers() {
    $.ajax({
        url: '/api/suppliers/transfers/money-changers',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const moneyChangers = response.money_changers;
                const moneyChangerSelect = $('#moneyChangerSelect');
                
                moneyChangerSelect.empty().append('<option value="">اختر الصراف أو البنك</option>');
                
                moneyChangers.forEach(function(changer) {
                    const typeText = changer.type === 'bank' ? 'بنك' : 'صراف';
                    const option = `<option value="${changer.id}">${changer.name} (${typeText})</option>`;
                    moneyChangerSelect.append(option);
                });
            }
        },
        error: function() {
            showAlert('خطأ في تحميل قائمة الصرافين', 'error');
        }
    });
}

/**
 * تحميل الفواتير المستحقة للمورد
 */
function loadOutstandingInvoices(supplierId) {
    $.ajax({
        url: `/api/suppliers/transfers/outstanding-invoices/${supplierId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                outstandingInvoices = response.outstanding_invoices;
                displayOutstandingInvoices(outstandingInvoices);
            }
        },
        error: function() {
            $('#outstandingInvoicesContainer').html('<p class="text-danger">خطأ في تحميل الفواتير المستحقة</p>');
        }
    });
}

/**
 * عرض الفواتير المستحقة
 */
function displayOutstandingInvoices(invoices) {
    const container = $('#outstandingInvoicesContainer');
    
    if (invoices.length === 0) {
        container.html('<p class="text-success">لا توجد فواتير مستحقة لهذا المورد</p>');
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-sm table-striped">';
    html += '<thead><tr><th>رقم الفاتورة</th><th>تاريخ الاستحقاق</th><th>المبلغ المستحق</th><th>الحالة</th><th>تخصيص</th></tr></thead><tbody>';
    
    invoices.forEach(function(invoice) {
        const statusClass = getInvoiceStatusClass(invoice.due_status);
        const daysText = invoice.days_overdue > 0 ? `متأخر ${invoice.days_overdue} يوم` : 
                        invoice.days_until_due > 0 ? `باقي ${invoice.days_until_due} يوم` : 'مستحق اليوم';
        
        html += `<tr>
            <td>${invoice.invoice_number}</td>
            <td>${formatDate(invoice.due_date)}</td>
            <td class="amount-display">${invoice.currency_symbol} ${formatNumber(invoice.outstanding_amount)}</td>
            <td><span class="badge ${statusClass}">${daysText}</span></td>
            <td>
                <div class="form-check">
                    <input class="form-check-input invoice-checkbox" type="checkbox" 
                           value="${invoice.transaction_id}" data-amount="${invoice.outstanding_amount}">
                    <label class="form-check-label">تخصيص</label>
                </div>
            </td>
        </tr>`;
    });
    
    html += '</tbody></table></div>';
    html += '<div class="mt-2"><small class="text-muted">يمكنك اختيار الفواتير التي تريد تخصيص الدفعة لها</small></div>';
    
    container.html(html);
}

/**
 * تحميل قائمة المدفوعات
 */
function loadPayments() {
    // بناء معاملات الاستعلام
    const params = new URLSearchParams(currentFilters);
    
    $.ajax({
        url: `/api/suppliers/transfers/payments-list?${params.toString()}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                displayPayments(response.payments);
            }
        },
        error: function() {
            showAlert('خطأ في تحميل قائمة المدفوعات', 'error');
        }
    });
}

/**
 * عرض قائمة المدفوعات في الجدول
 */
function displayPayments(payments) {
    paymentsTable.clear();
    
    payments.forEach(function(payment) {
        const statusBadge = getPaymentStatusBadge(payment.payment_status);
        const amount = `${payment.currency_symbol} ${formatNumber(payment.payment_amount)}`;
        const requestDate = formatDateTime(payment.requested_date);
        
        const actions = `
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-primary" onclick="viewPaymentDetails(${payment.payment_id})" title="عرض التفاصيل">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-outline-info" onclick="trackPayment('${payment.request_number}')" title="تتبع الحوالة">
                    <i class="fas fa-route"></i>
                </button>
            </div>
        `;
        
        paymentsTable.row.add([
            payment.request_number,
            payment.supplier_name,
            amount,
            payment.currency_code,
            payment.payment_purpose,
            statusBadge,
            requestDate,
            payment.money_changer_name || '-',
            actions
        ]);
    });
    
    paymentsTable.draw();
}

/**
 * إنشاء دفعة جديدة
 */
function createPayment() {
    const formData = {
        supplier_id: $('#supplierSelect').val(),
        amount: parseFloat($('#paymentAmount').val()),
        currency_code: $('#currencySelect').val(),
        money_changer_id: $('#moneyChangerSelect').val(),
        payment_purpose: $('#paymentPurpose').val(),
        notes: $('#paymentNotes').val(),
        discount_amount: parseFloat($('#discountAmount').val()) || 0,
        tax_amount: parseFloat($('#taxAmount').val()) || 0,
        payment_method: $('#paymentMethod').val(),
        invoice_numbers: getSelectedInvoices()
    };
    
    // التحقق من البيانات المطلوبة
    if (!formData.supplier_id || !formData.amount || !formData.currency_code || 
        !formData.money_changer_id || !formData.payment_purpose) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }
    
    $.ajax({
        url: '/api/suppliers/transfers/create-payment-request',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                showAlert('تم إنشاء طلب الدفع بنجاح', 'success');
                $('#createPaymentModal').modal('hide');
                $('#createPaymentForm')[0].reset();
                loadPayments();
                loadDashboardStats();
            } else {
                showAlert(response.message, 'error');
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            showAlert(response?.message || 'حدث خطأ في إنشاء طلب الدفع', 'error');
        }
    });
}

/**
 * الحصول على الفواتير المحددة
 */
function getSelectedInvoices() {
    const selectedInvoices = [];
    $('.invoice-checkbox:checked').each(function() {
        selectedInvoices.push({
            transaction_id: parseInt($(this).val()),
            allocated_amount: parseFloat($(this).data('amount'))
        });
    });
    return selectedInvoices;
}

/**
 * عرض تفاصيل الدفعة
 */
function viewPaymentDetails(paymentId) {
    $.ajax({
        url: `/api/suppliers/transfers/payment-details/${paymentId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                displayPaymentDetailsModal(response.payment_data);
            }
        },
        error: function() {
            showAlert('خطأ في تحميل تفاصيل الدفعة', 'error');
        }
    });
}

/**
 * عرض modal تفاصيل الدفعة
 */
function displayPaymentDetailsModal(paymentData) {
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>معلومات الدفعة</h6>
                <table class="table table-sm">
                    <tr><td>رقم الطلب:</td><td>${paymentData.request_number}</td></tr>
                    <tr><td>رقم الحوالة:</td><td>${paymentData.transfer_number || 'لم يتم التنفيذ بعد'}</td></tr>
                    <tr><td>المورد:</td><td>${paymentData.supplier_name}</td></tr>
                    <tr><td>المبلغ:</td><td>${paymentData.currency_symbol} ${formatNumber(paymentData.payment_amount)}</td></tr>
                    <tr><td>الحالة:</td><td>${getPaymentStatusBadge(paymentData.payment_status)}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>التواريخ المهمة</h6>
                <table class="table table-sm">
                    <tr><td>تاريخ الطلب:</td><td>${formatDateTime(paymentData.requested_date)}</td></tr>
                    <tr><td>تاريخ الاعتماد:</td><td>${formatDateTime(paymentData.approved_date) || 'لم يتم الاعتماد'}</td></tr>
                    <tr><td>تاريخ التنفيذ:</td><td>${formatDateTime(paymentData.executed_date) || 'لم يتم التنفيذ'}</td></tr>
                    <tr><td>تاريخ الإكمال:</td><td>${formatDateTime(paymentData.completed_date) || 'لم يتم الإكمال'}</td></tr>
                </table>
            </div>
        </div>
    `;
    
    $('#paymentDetailsContent').html(content);
    $('#paymentDetailsModal').modal('show');
}

/**
 * تطبيق الفلاتر
 */
function applyFilters() {
    currentFilters = {
        supplier_id: $('#supplierFilter').val(),
        status: $('#statusFilter').val(),
        currency: $('#currencyFilter').val(),
        date_from: $('#dateFromFilter').val(),
        date_to: $('#dateToFilter').val()
    };
    
    // إزالة الفلاتر الفارغة
    Object.keys(currentFilters).forEach(key => {
        if (!currentFilters[key]) {
            delete currentFilters[key];
        }
    });
    
    loadPayments();
}

/**
 * تحديث البيانات
 */
function refreshData() {
    loadDashboardStats();
    loadPayments();
    showAlert('تم تحديث البيانات', 'success');
}

/**
 * تتبع الحوالة
 */
function trackPayment(requestNumber) {
    // فتح صفحة تتبع الحوالات في تبويب جديد
    window.open(`/transfers/tracking?request=${requestNumber}`, '_blank');
}

// دوال مساعدة للتنسيق
function getPaymentStatusBadge(status) {
    const statusMap = {
        'PENDING': { text: 'معلقة', class: 'status-pending' },
        'APPROVED': { text: 'معتمدة', class: 'status-approved' },
        'EXECUTED': { text: 'منفذة', class: 'status-executed' },
        'COMPLETED': { text: 'مكتملة', class: 'status-completed' },
        'CANCELLED': { text: 'ملغية', class: 'status-cancelled' }
    };
    
    const statusInfo = statusMap[status] || { text: status, class: 'bg-secondary' };
    return `<span class="badge payment-status-badge ${statusInfo.class}">${statusInfo.text}</span>`;
}

function getInvoiceStatusClass(status) {
    const statusMap = {
        'متأخر': 'bg-danger',
        'مستحق قريباً': 'bg-warning',
        'مستحق': 'bg-info',
        'غير مستحق': 'bg-secondary'
    };
    return statusMap[status] || 'bg-secondary';
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 2
    }).format(amount);
}

function formatNumber(number) {
    return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(number);
}

function formatDate(dateString) {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('ar-SA');
}

function formatDateTime(dateString) {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString('ar-SA');
}

function showAlert(message, type) {
    // يمكن استخدام مكتبة إشعارات مثل Toastr أو SweetAlert
    const alertClass = type === 'success' ? 'alert-success' : 
                     type === 'warning' ? 'alert-warning' : 
                     type === 'error' ? 'alert-danger' : 'alert-info';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // إضافة الإشعار في أعلى الصفحة
    $('.container-fluid').prepend(alertHtml);
    
    // إزالة الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}
