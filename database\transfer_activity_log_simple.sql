-- =====================================================
-- جدول سجل أنشطة الحوالات (مبسط)
-- Transfer Activity Log Table (Simple)
-- =====================================================

-- 1. حذف الكائنات الموجودة إذا كانت موجودة
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE transfer_activity_log CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -942 THEN
            RAISE;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE transfer_activity_log_seq';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -2289 THEN
            RAISE;
        END IF;
END;
/

-- 2. إنشاء الجدول
CREATE TABLE transfer_activity_log (
    id NUMBER PRIMARY KEY,
    transfer_id NUMBER NOT NULL,
    activity_type VARCHAR2(50) NOT NULL,
    description CLOB,
    old_status VARCHAR2(50),
    new_status VARCHAR2(50),
    amount_before NUMBER(15,2),
    amount_after NUMBER(15,2),
    currency_code VARCHAR2(10),
    entity_type VARCHAR2(50),
    entity_id NUMBER,
    entity_name VARCHAR2(200),
    operation_details CLOB,
    error_message CLOB,
    execution_time_ms NUMBER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    ip_address VARCHAR2(45),
    user_agent VARCHAR2(500),
    session_id VARCHAR2(100)
);

-- 3. إضافة القيود
ALTER TABLE transfer_activity_log ADD CONSTRAINT fk_tal_transfer FOREIGN KEY (transfer_id) REFERENCES transfers(id);
ALTER TABLE transfer_activity_log ADD CONSTRAINT chk_tal_activity_type CHECK (activity_type IN ('EXECUTION', 'CANCELLATION', 'VALIDATION', 'DISTRIBUTION', 'STATUS_CHANGE', 'AMOUNT_CHANGE', 'ERROR', 'INFO'));

-- 4. إنشاء sequence
CREATE SEQUENCE transfer_activity_log_seq START WITH 1 INCREMENT BY 1 NOCACHE;

-- 5. إنشاء trigger للـ ID التلقائي
CREATE OR REPLACE TRIGGER tal_id_trigger
    BEFORE INSERT ON transfer_activity_log
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := transfer_activity_log_seq.NEXTVAL;
    END IF;
    
    IF :NEW.created_at IS NULL THEN
        :NEW.created_at := CURRENT_TIMESTAMP;
    END IF;
END;
/

-- 6. إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_tal_transfer ON transfer_activity_log(transfer_id);
CREATE INDEX idx_tal_activity_type ON transfer_activity_log(activity_type);
CREATE INDEX idx_tal_created_at ON transfer_activity_log(created_at);
CREATE INDEX idx_tal_created_by ON transfer_activity_log(created_by);
CREATE INDEX idx_tal_entity ON transfer_activity_log(entity_type, entity_id);

-- 7. إنشاء view لعرض السجل مع التفاصيل
CREATE OR REPLACE VIEW v_transfer_activity_log AS
SELECT 
    tal.*,
    t.transfer_number,
    t.status as current_transfer_status,
    CASE tal.entity_type
        WHEN 'SUPPLIER' THEN (SELECT name_ar FROM suppliers WHERE id = tal.entity_id)
        WHEN 'MONEY_CHANGER' THEN (SELECT name FROM money_changers WHERE id = tal.entity_id)
        WHEN 'BANK' THEN (SELECT name FROM banks WHERE id = tal.entity_id)
        ELSE tal.entity_name
    END as resolved_entity_name
FROM transfer_activity_log tal
JOIN transfers t ON tal.transfer_id = t.id;

-- 8. إنشاء إجراء لتسجيل النشاط
CREATE OR REPLACE PROCEDURE LOG_TRANSFER_ACTIVITY(
    p_transfer_id IN NUMBER,
    p_activity_type IN VARCHAR2,
    p_description IN CLOB DEFAULT NULL,
    p_old_status IN VARCHAR2 DEFAULT NULL,
    p_new_status IN VARCHAR2 DEFAULT NULL,
    p_amount_before IN NUMBER DEFAULT NULL,
    p_amount_after IN NUMBER DEFAULT NULL,
    p_currency_code IN VARCHAR2 DEFAULT NULL,
    p_entity_type IN VARCHAR2 DEFAULT NULL,
    p_entity_id IN NUMBER DEFAULT NULL,
    p_entity_name IN VARCHAR2 DEFAULT NULL,
    p_operation_details IN CLOB DEFAULT NULL,
    p_error_message IN CLOB DEFAULT NULL,
    p_execution_time_ms IN NUMBER DEFAULT NULL,
    p_created_by IN NUMBER DEFAULT 1,
    p_ip_address IN VARCHAR2 DEFAULT NULL,
    p_user_agent IN VARCHAR2 DEFAULT NULL,
    p_session_id IN VARCHAR2 DEFAULT NULL
) AS
BEGIN
    INSERT INTO transfer_activity_log (
        transfer_id,
        activity_type,
        description,
        old_status,
        new_status,
        amount_before,
        amount_after,
        currency_code,
        entity_type,
        entity_id,
        entity_name,
        operation_details,
        error_message,
        execution_time_ms,
        created_by,
        ip_address,
        user_agent,
        session_id
    ) VALUES (
        p_transfer_id,
        p_activity_type,
        p_description,
        p_old_status,
        p_new_status,
        p_amount_before,
        p_amount_after,
        p_currency_code,
        p_entity_type,
        p_entity_id,
        p_entity_name,
        p_operation_details,
        p_error_message,
        p_execution_time_ms,
        p_created_by,
        p_ip_address,
        p_user_agent,
        p_session_id
    );
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END;
/

-- 9. إنشاء إجراء لتنظيف السجلات القديمة
CREATE OR REPLACE PROCEDURE CLEANUP_OLD_ACTIVITY_LOGS(
    p_days_to_keep IN NUMBER DEFAULT 365
) AS
    v_cutoff_date DATE;
    v_deleted_count NUMBER;
BEGIN
    v_cutoff_date := SYSDATE - p_days_to_keep;
    
    DELETE FROM transfer_activity_log
    WHERE created_at < v_cutoff_date;
    
    v_deleted_count := SQL%ROWCOUNT;
    
    COMMIT;
    
    DBMS_OUTPUT.PUT_LINE('Deleted ' || v_deleted_count || ' old activity log records');
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END;
/

-- 10. إنشاء view لإحصائيات الأنشطة
CREATE OR REPLACE VIEW v_activity_statistics AS
SELECT 
    activity_type,
    COUNT(*) as total_count,
    COUNT(DISTINCT transfer_id) as unique_transfers,
    COUNT(DISTINCT created_by) as unique_users,
    MIN(created_at) as first_activity,
    MAX(created_at) as last_activity,
    AVG(execution_time_ms) as avg_execution_time_ms
FROM transfer_activity_log
GROUP BY activity_type
ORDER BY total_count DESC;

-- 11. إنشاء view للأنشطة الحديثة
CREATE OR REPLACE VIEW v_recent_activities AS
SELECT 
    tal.*,
    t.transfer_number,
    CASE tal.entity_type
        WHEN 'SUPPLIER' THEN (SELECT name_ar FROM suppliers WHERE id = tal.entity_id)
        WHEN 'MONEY_CHANGER' THEN (SELECT name FROM money_changers WHERE id = tal.entity_id)
        WHEN 'BANK' THEN (SELECT name FROM banks WHERE id = tal.entity_id)
        ELSE tal.entity_name
    END as resolved_entity_name
FROM transfer_activity_log tal
JOIN transfers t ON tal.transfer_id = t.id
WHERE tal.created_at >= SYSDATE - 7
ORDER BY tal.created_at DESC;

-- 12. إضافة comments للتوثيق
COMMENT ON TABLE transfer_activity_log IS 'Transfer activity log table';
COMMENT ON COLUMN transfer_activity_log.id IS 'Unique activity log ID';
COMMENT ON COLUMN transfer_activity_log.transfer_id IS 'Transfer ID';
COMMENT ON COLUMN transfer_activity_log.activity_type IS 'Type of activity';
COMMENT ON COLUMN transfer_activity_log.description IS 'Activity description';
COMMENT ON COLUMN transfer_activity_log.old_status IS 'Previous status';
COMMENT ON COLUMN transfer_activity_log.new_status IS 'New status';
COMMENT ON COLUMN transfer_activity_log.amount_before IS 'Amount before change';
COMMENT ON COLUMN transfer_activity_log.amount_after IS 'Amount after change';
COMMENT ON COLUMN transfer_activity_log.entity_type IS 'Entity type (SUPPLIER, MONEY_CHANGER, BANK)';
COMMENT ON COLUMN transfer_activity_log.entity_id IS 'Entity ID';
COMMENT ON COLUMN transfer_activity_log.operation_details IS 'Detailed operation information';
COMMENT ON COLUMN transfer_activity_log.error_message IS 'Error message if any';
COMMENT ON COLUMN transfer_activity_log.execution_time_ms IS 'Execution time in milliseconds';

COMMIT;

SELECT 'Transfer activity log table created successfully' as result FROM DUAL;
