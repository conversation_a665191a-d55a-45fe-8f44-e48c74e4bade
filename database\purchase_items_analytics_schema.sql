-- =====================================================
-- نظام تحليل أصناف أوامر الشراء المتقدم
-- Advanced Purchase Order Items Analytics System
-- التاريخ: 2025-01-03
-- =====================================================

-- 1. إنشاء جدول فئات الأصناف المحسن
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE ITEM_CATEGORIES CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

CREATE TABLE ITEM_CATEGORIES (
    ID NUMBER PRIMARY KEY,
    CATEGORY_CODE VARCHAR2(20) UNIQUE NOT NULL,
    CATEGORY_NAME_AR NVARCHAR2(100) NOT NULL,
    CATEGORY_NAME_EN VARCHAR2(100),
    PARENT_CATEGORY_ID NUMBER,
    DESCRIPTION_AR NCLOB,
    DESCRIPTION_EN CLOB,
    IS_ACTIVE NUMBER(1) DEFAULT 1,
    SORT_ORDER NUMBER DEFAULT 0,
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CREATED_BY NUMBER,
    UPDATED_BY NUMBER,

    CONSTRAINT FK_CATEGORY_PARENT FOREIGN KEY (PARENT_CATEGORY_ID)
        REFERENCES ITEM_CATEGORIES(ID)
);

-- 2. إنشاء sequence للفئات
BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE ITEM_CATEGORIES_SEQ';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

CREATE SEQUENCE ITEM_CATEGORIES_SEQ START WITH 1 INCREMENT BY 1;

-- 3. إنشاء trigger للفئات
CREATE OR REPLACE TRIGGER TRG_ITEM_CATEGORIES_ID
    BEFORE INSERT ON ITEM_CATEGORIES
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := ITEM_CATEGORIES_SEQ.NEXTVAL;
    END IF;
    :NEW.UPDATED_AT := CURRENT_TIMESTAMP;
END;
/

-- 4. إنشاء جدول تحليل أداء الأصناف
CREATE TABLE ITEM_PERFORMANCE_ANALYTICS (
    ID NUMBER PRIMARY KEY,
    ITEM_CODE VARCHAR2(50) NOT NULL,
    ITEM_NAME NVARCHAR2(200) NOT NULL,
    CATEGORY_ID NUMBER,
    ANALYSIS_PERIOD VARCHAR2(20) NOT NULL, -- MONTHLY, QUARTERLY, YEARLY
    PERIOD_START_DATE DATE NOT NULL,
    PERIOD_END_DATE DATE NOT NULL,
    
    -- إحصائيات الكمية
    TOTAL_QUANTITY NUMBER(15,3) DEFAULT 0,
    TOTAL_ORDERS NUMBER DEFAULT 0,
    AVG_QUANTITY_PER_ORDER NUMBER(15,3) DEFAULT 0,
    MIN_QUANTITY NUMBER(15,3) DEFAULT 0,
    MAX_QUANTITY NUMBER(15,3) DEFAULT 0,
    
    -- إحصائيات السعر
    AVG_UNIT_PRICE NUMBER(15,2) DEFAULT 0,
    MIN_UNIT_PRICE NUMBER(15,2) DEFAULT 0,
    MAX_UNIT_PRICE NUMBER(15,2) DEFAULT 0,
    PRICE_VARIANCE NUMBER(15,2) DEFAULT 0,
    
    -- إحصائيات القيمة
    TOTAL_VALUE NUMBER(15,2) DEFAULT 0,
    AVG_ORDER_VALUE NUMBER(15,2) DEFAULT 0,
    VALUE_PERCENTAGE NUMBER(5,2) DEFAULT 0, -- نسبة من إجمالي المشتريات
    
    -- تحليل ABC
    ABC_CLASSIFICATION VARCHAR2(1), -- A, B, C
    ABC_RANK NUMBER,
    
    -- إحصائيات الموردين
    SUPPLIER_COUNT NUMBER DEFAULT 0,
    PRIMARY_SUPPLIER_NAME NVARCHAR2(200),
    PRIMARY_SUPPLIER_PERCENTAGE NUMBER(5,2) DEFAULT 0,
    
    -- مؤشرات الأداء
    REORDER_FREQUENCY NUMBER(5,2) DEFAULT 0, -- مرات الطلب في الفترة
    LEAD_TIME_AVG NUMBER(5,2) DEFAULT 0, -- متوسط وقت التسليم
    QUALITY_SCORE NUMBER(3,1) DEFAULT 0, -- نقاط الجودة
    
    -- بيانات النظام
    CALCULATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CALCULATED_BY NUMBER,
    
    CONSTRAINT FK_ITEM_PERF_CATEGORY FOREIGN KEY (CATEGORY_ID) 
        REFERENCES ITEM_CATEGORIES(ID),
    CONSTRAINT UK_ITEM_PERF_PERIOD UNIQUE (ITEM_CODE, ANALYSIS_PERIOD, PERIOD_START_DATE)
);

-- 5. إنشاء sequence لتحليل الأداء
BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE ITEM_PERFORMANCE_SEQ';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

CREATE SEQUENCE ITEM_PERFORMANCE_SEQ START WITH 1 INCREMENT BY 1;

-- 6. إنشاء trigger لتحليل الأداء
CREATE OR REPLACE TRIGGER TRG_ITEM_PERFORMANCE_ID
    BEFORE INSERT ON ITEM_PERFORMANCE_ANALYTICS
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := ITEM_PERFORMANCE_SEQ.NEXTVAL;
    END IF;
END;
/

-- 7. إنشاء جدول تحليل اتجاهات الأصناف
CREATE TABLE ITEM_TREND_ANALYTICS (
    ID NUMBER PRIMARY KEY,
    ITEM_CODE VARCHAR2(50) NOT NULL,
    TREND_TYPE VARCHAR2(20) NOT NULL, -- PRICE, QUANTITY, DEMAND, SEASONAL
    TREND_DIRECTION VARCHAR2(10), -- UP, DOWN, STABLE, VOLATILE
    TREND_STRENGTH NUMBER(3,1) DEFAULT 0, -- قوة الاتجاه من 0 إلى 10
    CONFIDENCE_LEVEL NUMBER(3,1) DEFAULT 0, -- مستوى الثقة
    
    -- بيانات الاتجاه
    START_DATE DATE NOT NULL,
    END_DATE DATE NOT NULL,
    START_VALUE NUMBER(15,2),
    END_VALUE NUMBER(15,2),
    CHANGE_PERCENTAGE NUMBER(5,2),
    VOLATILITY_INDEX NUMBER(5,2),
    
    -- التوقعات
    PREDICTED_NEXT_VALUE NUMBER(15,2),
    PREDICTION_DATE DATE,
    PREDICTION_CONFIDENCE NUMBER(3,1),
    
    -- بيانات النظام
    CALCULATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CALCULATED_BY NUMBER,
    
    CONSTRAINT UK_ITEM_TREND UNIQUE (ITEM_CODE, TREND_TYPE, START_DATE)
);

-- 8. إنشاء sequence لتحليل الاتجاهات
BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE ITEM_TREND_SEQ';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

CREATE SEQUENCE ITEM_TREND_SEQ START WITH 1 INCREMENT BY 1;

-- 9. إنشاء trigger لتحليل الاتجاهات
CREATE OR REPLACE TRIGGER TRG_ITEM_TREND_ID
    BEFORE INSERT ON ITEM_TREND_ANALYTICS
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := ITEM_TREND_SEQ.NEXTVAL;
    END IF;
END;
/

-- 10. إنشاء جدول مقارنة أسعار الموردين
CREATE TABLE SUPPLIER_PRICE_COMPARISON (
    ID NUMBER PRIMARY KEY,
    ITEM_CODE VARCHAR2(50) NOT NULL,
    SUPPLIER_NAME NVARCHAR2(200) NOT NULL,
    UNIT_PRICE NUMBER(15,2) NOT NULL,
    CURRENCY VARCHAR2(10) DEFAULT 'SAR',
    PRICE_DATE DATE NOT NULL,
    QUANTITY_RANGE_MIN NUMBER(15,3),
    QUANTITY_RANGE_MAX NUMBER(15,3),
    LEAD_TIME_DAYS NUMBER,
    QUALITY_RATING NUMBER(3,1),
    PAYMENT_TERMS VARCHAR2(100),
    
    -- مؤشرات المقارنة
    PRICE_RANK NUMBER, -- ترتيب السعر بين الموردين
    IS_BEST_PRICE NUMBER(1) DEFAULT 0,
    PRICE_DIFFERENCE_FROM_BEST NUMBER(15,2),
    PRICE_DIFFERENCE_PERCENTAGE NUMBER(5,2),
    
    -- بيانات النظام
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CREATED_BY NUMBER,
    
    CONSTRAINT UK_SUPPLIER_PRICE UNIQUE (ITEM_CODE, SUPPLIER_NAME, PRICE_DATE)
);

-- 11. إنشاء sequence لمقارنة الأسعار
BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE SUPPLIER_PRICE_COMP_SEQ';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

CREATE SEQUENCE SUPPLIER_PRICE_COMP_SEQ START WITH 1 INCREMENT BY 1;

-- 12. إنشاء trigger لمقارنة الأسعار
CREATE OR REPLACE TRIGGER TRG_SUPPLIER_PRICE_ID
    BEFORE INSERT ON SUPPLIER_PRICE_COMPARISON
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := SUPPLIER_PRICE_COMP_SEQ.NEXTVAL;
    END IF;
    :NEW.UPDATED_AT := CURRENT_TIMESTAMP;
END;
/

-- 13. إنشاء فهارس للأداء
CREATE INDEX IDX_ITEM_PERF_CODE ON ITEM_PERFORMANCE_ANALYTICS(ITEM_CODE);
CREATE INDEX IDX_ITEM_PERF_PERIOD ON ITEM_PERFORMANCE_ANALYTICS(ANALYSIS_PERIOD, PERIOD_START_DATE);
CREATE INDEX IDX_ITEM_PERF_CATEGORY ON ITEM_PERFORMANCE_ANALYTICS(CATEGORY_ID);
CREATE INDEX IDX_ITEM_PERF_ABC ON ITEM_PERFORMANCE_ANALYTICS(ABC_CLASSIFICATION);

CREATE INDEX IDX_ITEM_TREND_CODE ON ITEM_TREND_ANALYTICS(ITEM_CODE);
CREATE INDEX IDX_ITEM_TREND_TYPE ON ITEM_TREND_ANALYTICS(TREND_TYPE);
CREATE INDEX IDX_ITEM_TREND_DATE ON ITEM_TREND_ANALYTICS(START_DATE, END_DATE);

CREATE INDEX IDX_SUPPLIER_PRICE_ITEM ON SUPPLIER_PRICE_COMPARISON(ITEM_CODE);
CREATE INDEX IDX_SUPPLIER_PRICE_SUPPLIER ON SUPPLIER_PRICE_COMPARISON(SUPPLIER_NAME);
CREATE INDEX IDX_SUPPLIER_PRICE_DATE ON SUPPLIER_PRICE_COMPARISON(PRICE_DATE);

-- 14. إدراج فئات أساسية للأصناف
INSERT INTO ITEM_CATEGORIES (CATEGORY_CODE, CATEGORY_NAME_AR, CATEGORY_NAME_EN, SORT_ORDER) VALUES
('RAW_MAT', 'مواد خام', 'Raw Materials', 1);

INSERT INTO ITEM_CATEGORIES (CATEGORY_CODE, CATEGORY_NAME_AR, CATEGORY_NAME_EN, SORT_ORDER) VALUES
('EQUIPMENT', 'معدات', 'Equipment', 2);

INSERT INTO ITEM_CATEGORIES (CATEGORY_CODE, CATEGORY_NAME_AR, CATEGORY_NAME_EN, SORT_ORDER) VALUES
('OFFICE_SUP', 'مستلزمات مكتبية', 'Office Supplies', 3);

INSERT INTO ITEM_CATEGORIES (CATEGORY_CODE, CATEGORY_NAME_AR, CATEGORY_NAME_EN, SORT_ORDER) VALUES
('MAINTENANCE', 'صيانة', 'Maintenance', 4);

INSERT INTO ITEM_CATEGORIES (CATEGORY_CODE, CATEGORY_NAME_AR, CATEGORY_NAME_EN, SORT_ORDER) VALUES
('SERVICES', 'خدمات', 'Services', 5);

INSERT INTO ITEM_CATEGORIES (CATEGORY_CODE, CATEGORY_NAME_AR, CATEGORY_NAME_EN, SORT_ORDER) VALUES
('OTHER', 'أخرى', 'Other', 99);

COMMIT;

-- 15. إضافة تعليقات للجداول
COMMENT ON TABLE ITEM_CATEGORIES IS 'جدول فئات الأصناف المحسن';
COMMENT ON TABLE ITEM_PERFORMANCE_ANALYTICS IS 'جدول تحليل أداء الأصناف';
COMMENT ON TABLE ITEM_TREND_ANALYTICS IS 'جدول تحليل اتجاهات الأصناف';
COMMENT ON TABLE SUPPLIER_PRICE_COMPARISON IS 'جدول مقارنة أسعار الموردين';

PROMPT 'تم إنشاء جداول تحليل أصناف أوامر الشراء بنجاح!';
