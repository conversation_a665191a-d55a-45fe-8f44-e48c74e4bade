"""
نظام التحليلات والتقارير للشحنات
Analytics and Reporting System for Shipments
"""

from datetime import datetime, timedelta
from flask import current_app
import json

# استيرادات اختيارية
try:
    import pandas as pd
except ImportError:
    pd = None

try:
    import numpy as np
except ImportError:
    np = None

try:
    import plotly.graph_objects as go
    import plotly.express as px
except ImportError:
    go = None
    px = None

# استيرادات SQLAlchemy معطلة مؤقتاً لأننا نستخدم Oracle
# from sqlalchemy import func, and_, or_
# from .models import Shipment, TrackingEvent, Carrier, Driver, Vehicle, DeliveryAttempt
# from app import db

class ShipmentAnalytics:
    """فئة تحليلات الشحنات"""
    
    def __init__(self):
        pass
    
    def get_dashboard_stats(self, start_date=None, end_date=None):
        """إحصائيات لوحة المعلومات"""
        try:
            # تحديد الفترة الزمنية
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()
            
            # الاستعلامات الأساسية
            base_query = Shipment.query.filter(
                Shipment.created_at.between(start_date, end_date)
            )
            
            # الإحصائيات العامة
            total_shipments = base_query.count()
            delivered_shipments = base_query.filter_by(status='تم التسليم').count()
            in_transit_shipments = base_query.filter(
                Shipment.status.in_(['في الطريق', 'خارج للتسليم'])
            ).count()
            pending_shipments = base_query.filter_by(status='معلق').count()
            
            # معدل التسليم الناجح
            delivery_rate = (delivered_shipments / total_shipments * 100) if total_shipments > 0 else 0
            
            # متوسط وقت التسليم
            avg_delivery_time = self.calculate_average_delivery_time(start_date, end_date)
            
            # إجمالي الإيرادات
            total_revenue = base_query.with_entities(
                func.sum(Shipment.total_cost)
            ).scalar() or 0
            
            # أكثر المدن شحناً
            top_cities = base_query.with_entities(
                Shipment.recipient_city,
                func.count(Shipment.id).label('count')
            ).group_by(Shipment.recipient_city).order_by(
                func.count(Shipment.id).desc()
            ).limit(5).all()
            
            return {
                'total_shipments': total_shipments,
                'delivered_shipments': delivered_shipments,
                'in_transit_shipments': in_transit_shipments,
                'pending_shipments': pending_shipments,
                'delivery_rate': round(delivery_rate, 2),
                'avg_delivery_time': avg_delivery_time,
                'total_revenue': round(total_revenue, 2),
                'top_cities': [{'city': city, 'count': count} for city, count in top_cities]
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting dashboard stats: {e}")
            return {}
    
    def calculate_average_delivery_time(self, start_date, end_date):
        """حساب متوسط وقت التسليم"""
        try:
            delivered_shipments = Shipment.query.filter(
                and_(
                    Shipment.created_at.between(start_date, end_date),
                    Shipment.status == 'تم التسليم',
                    Shipment.actual_delivery_date.isnot(None)
                )
            ).all()
            
            if not delivered_shipments:
                return 0
            
            total_hours = 0
            count = 0
            
            for shipment in delivered_shipments:
                if shipment.pickup_date and shipment.actual_delivery_date:
                    delivery_time = shipment.actual_delivery_date - shipment.pickup_date
                    total_hours += delivery_time.total_seconds() / 3600
                    count += 1
            
            return round(total_hours / count, 2) if count > 0 else 0
            
        except Exception as e:
            current_app.logger.error(f"Error calculating average delivery time: {e}")
            return 0
    
    def get_shipment_trends(self, days=30):
        """اتجاهات الشحنات"""
        try:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            # جلب البيانات اليومية
            daily_data = db.session.query(
                func.date(Shipment.created_at).label('date'),
                func.count(Shipment.id).label('count'),
                func.sum(Shipment.total_cost).label('revenue')
            ).filter(
                Shipment.created_at.between(start_date, end_date)
            ).group_by(
                func.date(Shipment.created_at)
            ).order_by('date').all()
            
            # تحويل إلى قوائم للرسم البياني
            dates = [str(row.date) for row in daily_data]
            counts = [row.count for row in daily_data]
            revenues = [float(row.revenue or 0) for row in daily_data]
            
            return {
                'dates': dates,
                'shipment_counts': counts,
                'revenues': revenues
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting shipment trends: {e}")
            return {'dates': [], 'shipment_counts': [], 'revenues': []}
    
    def get_status_distribution(self, start_date=None, end_date=None):
        """توزيع حالات الشحنات"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()
            
            status_data = db.session.query(
                Shipment.status,
                func.count(Shipment.id).label('count')
            ).filter(
                Shipment.created_at.between(start_date, end_date)
            ).group_by(Shipment.status).all()
            
            return [
                {'status': status, 'count': count}
                for status, count in status_data
            ]
            
        except Exception as e:
            current_app.logger.error(f"Error getting status distribution: {e}")
            return []
    
    def get_carrier_performance(self, start_date=None, end_date=None):
        """أداء شركات الشحن"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()
            
            carrier_stats = db.session.query(
                Carrier.name,
                func.count(Shipment.id).label('total_shipments'),
                func.sum(
                    func.case([(Shipment.status == 'تم التسليم', 1)], else_=0)
                ).label('delivered_shipments'),
                func.avg(Shipment.total_cost).label('avg_cost')
            ).join(
                Shipment, Carrier.id == Shipment.carrier_id
            ).filter(
                Shipment.created_at.between(start_date, end_date)
            ).group_by(Carrier.id, Carrier.name).all()
            
            performance_data = []
            for carrier in carrier_stats:
                delivery_rate = (
                    carrier.delivered_shipments / carrier.total_shipments * 100
                    if carrier.total_shipments > 0 else 0
                )
                
                performance_data.append({
                    'carrier_name': carrier.name,
                    'total_shipments': carrier.total_shipments,
                    'delivered_shipments': carrier.delivered_shipments,
                    'delivery_rate': round(delivery_rate, 2),
                    'avg_cost': round(float(carrier.avg_cost or 0), 2)
                })
            
            return performance_data
            
        except Exception as e:
            current_app.logger.error(f"Error getting carrier performance: {e}")
            return []
    
    def get_geographic_distribution(self, start_date=None, end_date=None):
        """التوزيع الجغرافي للشحنات"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()
            
            city_data = db.session.query(
                Shipment.recipient_city,
                func.count(Shipment.id).label('count'),
                func.sum(Shipment.total_cost).label('revenue')
            ).filter(
                Shipment.created_at.between(start_date, end_date)
            ).group_by(
                Shipment.recipient_city
            ).order_by(
                func.count(Shipment.id).desc()
            ).all()
            
            return [
                {
                    'city': city,
                    'shipment_count': count,
                    'revenue': float(revenue or 0)
                }
                for city, count, revenue in city_data
            ]
            
        except Exception as e:
            current_app.logger.error(f"Error getting geographic distribution: {e}")
            return []
    
    def get_delivery_performance_metrics(self, start_date=None, end_date=None):
        """مقاييس أداء التسليم"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()
            
            # الشحنات المسلمة في الوقت المحدد
            on_time_deliveries = Shipment.query.filter(
                and_(
                    Shipment.created_at.between(start_date, end_date),
                    Shipment.status == 'تم التسليم',
                    Shipment.actual_delivery_date <= Shipment.expected_delivery_date
                )
            ).count()
            
            # إجمالي الشحنات المسلمة
            total_delivered = Shipment.query.filter(
                and_(
                    Shipment.created_at.between(start_date, end_date),
                    Shipment.status == 'تم التسليم'
                )
            ).count()
            
            # معدل التسليم في الوقت المحدد
            on_time_rate = (
                on_time_deliveries / total_delivered * 100
                if total_delivered > 0 else 0
            )
            
            # محاولات التسليم الفاشلة
            failed_attempts = DeliveryAttempt.query.join(Shipment).filter(
                and_(
                    Shipment.created_at.between(start_date, end_date),
                    DeliveryAttempt.status == 'فشل'
                )
            ).count()
            
            # متوسط محاولات التسليم
            avg_attempts = db.session.query(
                func.avg(
                    db.session.query(func.count(DeliveryAttempt.id))
                    .filter(DeliveryAttempt.shipment_id == Shipment.id)
                    .scalar_subquery()
                )
            ).filter(
                and_(
                    Shipment.created_at.between(start_date, end_date),
                    Shipment.status == 'تم التسليم'
                )
            ).scalar() or 0
            
            return {
                'on_time_deliveries': on_time_deliveries,
                'total_delivered': total_delivered,
                'on_time_rate': round(on_time_rate, 2),
                'failed_attempts': failed_attempts,
                'avg_attempts': round(float(avg_attempts), 2)
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting delivery performance metrics: {e}")
            return {}
    
    def generate_plotly_chart(self, chart_type, data, title=""):
        """إنشاء رسم بياني باستخدام Plotly"""
        try:
            if chart_type == 'line':
                fig = go.Figure()
                fig.add_trace(go.Scatter(
                    x=data.get('x', []),
                    y=data.get('y', []),
                    mode='lines+markers',
                    name=data.get('name', 'البيانات')
                ))
                
            elif chart_type == 'bar':
                fig = go.Figure()
                fig.add_trace(go.Bar(
                    x=data.get('x', []),
                    y=data.get('y', []),
                    name=data.get('name', 'البيانات')
                ))
                
            elif chart_type == 'pie':
                fig = go.Figure()
                fig.add_trace(go.Pie(
                    labels=data.get('labels', []),
                    values=data.get('values', []),
                    name=data.get('name', 'البيانات')
                ))
            
            else:
                return None
            
            fig.update_layout(
                title=title,
                font=dict(family="Arial", size=12),
                showlegend=True
            )
            
            return fig.to_json()
            
        except Exception as e:
            current_app.logger.error(f"Error generating plotly chart: {e}")
            return None
    
    def export_data_to_excel(self, start_date=None, end_date=None):
        """تصدير البيانات إلى Excel"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()
            
            # جلب بيانات الشحنات
            shipments = Shipment.query.filter(
                Shipment.created_at.between(start_date, end_date)
            ).all()
            
            # تحويل إلى DataFrame
            data = []
            for shipment in shipments:
                data.append({
                    'رقم التتبع': shipment.tracking_number,
                    'المرسل': shipment.sender_name,
                    'المستقبل': shipment.recipient_name,
                    'المدينة': shipment.recipient_city,
                    'الحالة': shipment.status,
                    'نوع الشحنة': shipment.shipment_type,
                    'الوزن': shipment.weight,
                    'التكلفة': shipment.total_cost,
                    'تاريخ الإنشاء': shipment.created_at,
                    'تاريخ التسليم المتوقع': shipment.expected_delivery_date,
                    'تاريخ التسليم الفعلي': shipment.actual_delivery_date
                })
            
            df = pd.DataFrame(data)
            
            # حفظ في ملف Excel
            filename = f"shipments_report_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.xlsx"
            filepath = f"/tmp/{filename}"
            
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='الشحنات', index=False)
                
                # إضافة ورقة الإحصائيات
                stats = self.get_dashboard_stats(start_date, end_date)
                stats_df = pd.DataFrame([stats])
                stats_df.to_excel(writer, sheet_name='الإحصائيات', index=False)
            
            return filepath
            
        except Exception as e:
            current_app.logger.error(f"Error exporting data to Excel: {e}")
            return None
