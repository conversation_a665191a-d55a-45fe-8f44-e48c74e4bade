#!/usr/bin/env python3
"""
البحث عن جداول الشحنات وفحص أعمدتها
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oracle_manager import OracleManager

def find_shipment_tables():
    """البحث عن جداول الشحنات"""
    
    oracle_manager = OracleManager()
    if not oracle_manager.connect():
        print("❌ فشل الاتصال بقاعدة البيانات")
        return False
    
    try:
        print("🔍 البحث عن جداول الشحنات...")
        
        # البحث عن جداول الشحنات
        tables_query = """
            SELECT table_name 
            FROM user_tables 
            WHERE table_name LIKE '%SHIPMENT%' OR table_name LIKE '%CARGO%'
            ORDER BY table_name
        """
        
        tables = oracle_manager.execute_query(tables_query, [])
        
        if tables:
            print(f"\n📋 الجداول الموجودة المتعلقة بالشحنات ({len(tables)}):")
            for table in tables:
                print(f"  - {table[0]}")
                
            # فحص أعمدة كل جدول
            for table in tables:
                table_name = table[0]
                print(f"\n🔍 فحص أعمدة جدول {table_name}...")
                
                columns_query = """
                    SELECT column_name, data_type, nullable
                    FROM user_tab_columns 
                    WHERE table_name = :1
                    ORDER BY column_id
                """
                
                columns = oracle_manager.execute_query(columns_query, [table_name])
                
                if columns:
                    print(f"📋 الجدول يحتوي على {len(columns)} عمود:")
                    print("-" * 60)
                    for col in columns:
                        nullable = "NULL" if col[2] == 'Y' else "NOT NULL"
                        print(f"{col[0]:<30} {col[1]:<15} {nullable}")
                        
                    # فحص عينة من البيانات
                    try:
                        sample_query = f"SELECT COUNT(*) FROM {table_name}"
                        count = oracle_manager.execute_query(sample_query, [])
                        if count:
                            print(f"📊 عدد السجلات: {count[0][0]}")
                    except Exception as e:
                        print(f"⚠️ خطأ في عد السجلات: {e}")
                else:
                    print("❌ لا يحتوي على أعمدة")
        else:
            print("❌ لم يتم العثور على جداول الشحنات")
            
            # البحث في جميع الجداول
            print("\n🔍 البحث في جميع الجداول...")
            all_tables_query = "SELECT table_name FROM user_tables ORDER BY table_name"
            all_tables = oracle_manager.execute_query(all_tables_query, [])
            
            if all_tables:
                print(f"📋 جميع الجداول الموجودة ({len(all_tables)}):")
                for table in all_tables:
                    print(f"  - {table[0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في البحث: {e}")
        return False
    
    finally:
        pass

if __name__ == "__main__":
    print("🔍 البحث عن جداول الشحنات")
    print("=" * 50)
    
    find_shipment_tables()
