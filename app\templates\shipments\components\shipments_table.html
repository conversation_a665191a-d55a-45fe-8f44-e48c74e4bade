<!-- تحسينات CSS للأزرار -->
<style>
/* === إصلاح فوري وقوي للوضع الداكن === */

/* إصلاح الحاويات الرئيسية */
body.dark-mode .shipments-table-component,
html.dark-mode .shipments-table-component,
.dark-mode .shipments-table-component {
    background-color: #2d3748 !important;
    color: #ffffff !important;
    border-color: #4a5568 !important;
}

/* === إصلاح شامل للجوال === */
@media (max-width: 768px) {
    /* الحاوية الرئيسية للجدول */
    .shipments-table-component {
        width: 100vw !important;
        max-width: 100vw !important;
        margin: 0 !important;
        padding: 0 !important;
        border-radius: 0 !important;
        overflow-x: hidden !important;
        box-sizing: border-box !important;
    }

    /* حاوية الجدول المتجاوبة */
    .table-responsive {
        width: 100vw !important;
        max-width: 100vw !important;
        margin: 0 !important;
        padding: 0 !important;
        border-radius: 0 !important;
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
        box-sizing: border-box !important;
    }

    /* الجدول نفسه */
    .table, #shipmentsTable {
        width: 100% !important;
        min-width: 800px !important; /* عرض أدنى للتمرير الأفقي */
        margin: 0 !important;
        border-collapse: collapse !important;
        font-size: 0.8rem !important;
        box-sizing: border-box !important;
    }

    /* خلايا الجدول */
    .table th,
    .table td {
        padding: 8px 6px !important;
        font-size: 0.75rem !important;
        white-space: nowrap !important;
        border: 1px solid #dee2e6 !important;
        min-width: 80px !important;
        max-width: 150px !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        box-sizing: border-box !important;
    }

    /* رؤوس الجدول */
    .table thead th {
        background-color: #f8f9fa !important;
        font-weight: bold !important;
        font-size: 0.7rem !important;
        padding: 10px 6px !important;
        position: sticky !important;
        top: 0 !important;
        z-index: 10 !important;
    }

    /* إخفاء أعمدة أقل أهمية */
    .table th:nth-child(n+8),
    .table td:nth-child(n+8) {
        display: none !important;
    }

    /* تذييل الجدول */
    .table-footer {
        width: 100vw !important;
        max-width: 100vw !important;
        margin: 0 !important;
        padding: 10px !important;
        border-radius: 0 !important;
        box-sizing: border-box !important;
    }

    /* الشارات في الجوال */
    .table .badge {
        font-size: 0.65rem !important;
        padding: 2px 6px !important;
        border-radius: 4px !important;
    }

    /* الأزرار في الجوال */
    .table .btn {
        padding: 2px 6px !important;
        font-size: 0.65rem !important;
        border-radius: 4px !important;
    }

    /* === الوضع الداكن للجوال === */
    body.dark-mode .shipments-table-component,
    html.dark-mode .shipments-table-component {
        background-color: #2d3748 !important;
        color: #ffffff !important;
    }

    body.dark-mode .table-responsive,
    html.dark-mode .table-responsive {
        background-color: #2d3748 !important;
        color: #ffffff !important;
    }

    body.dark-mode .table,
    body.dark-mode #shipmentsTable,
    html.dark-mode .table,
    html.dark-mode #shipmentsTable {
        background-color: #2d3748 !important;
        color: #ffffff !important;
        border-color: #4a5568 !important;
    }

    body.dark-mode .table th,
    body.dark-mode .table td,
    html.dark-mode .table th,
    html.dark-mode .table td {
        background-color: #2d3748 !important;
        color: #ffffff !important;
        border-color: #4a5568 !important;
    }

    body.dark-mode .table thead th,
    html.dark-mode .table thead th {
        background-color: #1a202c !important;
        color: #ffffff !important;
        border-color: #4a5568 !important;
    }

    body.dark-mode .table tbody tr:nth-child(even),
    body.dark-mode .table tbody tr:nth-child(even) td,
    html.dark-mode .table tbody tr:nth-child(even),
    html.dark-mode .table tbody tr:nth-child(even) td {
        background-color: #374151 !important;
    }

    body.dark-mode .table-footer,
    html.dark-mode .table-footer {
        background-color: #2d3748 !important;
        color: #ffffff !important;
        border-color: #4a5568 !important;
    }

    body.dark-mode .bg-light,
    html.dark-mode .bg-light {
        background-color: #1a202c !important;
        color: #ffffff !important;
    }

    body.dark-mode .text-muted,
    html.dark-mode .text-muted {
        color: #a0aec0 !important;
    }
}

body.dark-mode .table-responsive,
html.dark-mode .table-responsive,
.dark-mode .table-responsive {
    background-color: #2d3748 !important;
    color: #ffffff !important;
    border-color: #4a5568 !important;
}

body.dark-mode .table-footer,
html.dark-mode .table-footer,
.dark-mode .table-footer {
    background-color: #2d3748 !important;
    color: #ffffff !important;
    border-color: #4a5568 !important;
}

/* إصلاح الجدول نفسه */
body.dark-mode table,
body.dark-mode .table,
html.dark-mode table,
html.dark-mode .table,
.dark-mode table,
.dark-mode .table {
    background-color: #2d3748 !important;
    color: #ffffff !important;
    border-color: #4a5568 !important;
}

body.dark-mode table *,
body.dark-mode .table *,
html.dark-mode table *,
html.dark-mode .table *,
.dark-mode table *,
.dark-mode .table * {
    background-color: inherit !important;
    color: inherit !important;
    border-color: #4a5568 !important;
}

body.dark-mode thead,
body.dark-mode thead th,
html.dark-mode thead,
html.dark-mode thead th,
.dark-mode thead,
.dark-mode thead th {
    background-color: #1a202c !important;
    color: #ffffff !important;
    border-color: #4a5568 !important;
}

body.dark-mode tbody,
body.dark-mode tbody tr,
body.dark-mode tbody td,
html.dark-mode tbody,
html.dark-mode tbody tr,
html.dark-mode tbody td,
.dark-mode tbody,
.dark-mode tbody tr,
.dark-mode tbody td {
    background-color: #2d3748 !important;
    color: #ffffff !important;
    border-color: #4a5568 !important;
}

body.dark-mode tbody tr:nth-child(even),
html.dark-mode tbody tr:nth-child(even),
.dark-mode tbody tr:nth-child(even) {
    background-color: #374151 !important;
}

/* إصلاح فئات Bootstrap المحددة */
body.dark-mode .bg-light,
html.dark-mode .bg-light,
.dark-mode .bg-light {
    background-color: #1a202c !important;
    color: #ffffff !important;
}

body.dark-mode .text-muted,
html.dark-mode .text-muted,
.dark-mode .text-muted {
    color: #a0aec0 !important;
}

/* إصلاح محدد لرأس الجدول */
body.dark-mode thead.bg-light,
html.dark-mode thead.bg-light,
.dark-mode thead.bg-light,
body.dark-mode .table thead,
html.dark-mode .table thead,
.dark-mode .table thead {
    background-color: #1a202c !important;
    color: #ffffff !important;
}

/* إصلاح محدد لتذييل الجدول */
body.dark-mode .table-footer.bg-light,
html.dark-mode .table-footer.bg-light,
.dark-mode .table-footer.bg-light {
    background-color: #2d3748 !important;
    color: #ffffff !important;
}

body.dark-mode tbody tr:nth-child(even) td,
html.dark-mode tbody tr:nth-child(even) td,
.dark-mode tbody tr:nth-child(even) td {
    background-color: #374151 !important;
}

body.dark-mode tbody tr:hover,
body.dark-mode tbody tr:hover td,
html.dark-mode tbody tr:hover,
html.dark-mode tbody tr:hover td,
.dark-mode tbody tr:hover,
.dark-mode tbody tr:hover td {
    background-color: #4a5568 !important;
}

/* فرض قوي على جميع العناصر */
body.dark-mode .table-responsive,
html.dark-mode .table-responsive,
.dark-mode .table-responsive {
    background-color: #2d3748 !important;
}

body.dark-mode .shipments-table-component,
html.dark-mode .shipments-table-component,
.dark-mode .shipments-table-component {
    background-color: #2d3748 !important;
    color: #ffffff !important;
}
</style>

<style>
/* === الوضع الداكن - إصلاح مباشر وقوي === */
body.dark-mode .shipments-table-component {
    background: #2d3748 !important;
    color: #ffffff !important;
}

body.dark-mode .shipments-table-component .table,
body.dark-mode .shipments-table-component table {
    background-color: #2d3748 !important;
    color: #ffffff !important;
    border-color: #4a5568 !important;
}

body.dark-mode .shipments-table-component .table thead,
body.dark-mode .shipments-table-component .table thead th,
body.dark-mode .shipments-table-component table thead,
body.dark-mode .shipments-table-component table thead th {
    background-color: #1a202c !important;
    color: #ffffff !important;
    border-color: #4a5568 !important;
}

body.dark-mode .shipments-table-component .table tbody,
body.dark-mode .shipments-table-component .table tbody tr,
body.dark-mode .shipments-table-component .table tbody td,
body.dark-mode .shipments-table-component table tbody,
body.dark-mode .shipments-table-component table tbody tr,
body.dark-mode .shipments-table-component table tbody td {
    background-color: #2d3748 !important;
    color: #ffffff !important;
    border-color: #4a5568 !important;
}

body.dark-mode .shipments-table-component .table tbody tr:nth-child(even),
body.dark-mode .shipments-table-component table tbody tr:nth-child(even) {
    background-color: #374151 !important;
}

body.dark-mode .shipments-table-component .table tbody tr:hover,
body.dark-mode .shipments-table-component table tbody tr:hover {
    background-color: #4a5568 !important;
}

/* تحسين أزرار الإجراءات في النافذة الحديثة */
.shipments-table-component .btn-group {
    display: flex !important;
    flex-wrap: nowrap !important;
    gap: 2px;
    align-items: center;
}

.shipments-table-component .btn-group .btn {
    flex-shrink: 0;
    min-width: auto;
    height: 28px;
    width: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.25rem 0.4rem;
    font-size: 0.75rem;
    white-space: nowrap;
}

.shipments-table-component .btn-group .btn i {
    font-size: 0.75rem;
}

.shipments-table-component .btn-group .btn:hover {
    transform: scale(1.05);
    z-index: 1;
}

/* تحسين عمود الإجراءات - بعد إخفاء عمود رقم التتبع */
.shipments-table-component th:last-child,
.shipments-table-component td:last-child {
    white-space: nowrap;
    min-width: 280px; /* مساحة أكبر بعد إخفاء عمود رقم التتبع */
    padding: 0.5rem 0.75rem;
}

/* تحسين header عمود الإجراءات */
.shipments-table-component th:last-child {
    background-color: #f8f9fa !important;
    font-weight: 600;
    color: #495057;
}

/* === الوضع الداكن لجدول الشحنات === */
body.dark-mode .shipments-table-component table,
body.dark-mode .shipments-table-component .table {
    background-color: #2d3748 !important;
    color: #ffffff !important;
}

body.dark-mode .shipments-table-component thead,
body.dark-mode .shipments-table-component thead th {
    background-color: #1a202c !important;
    color: #ffffff !important;
    border-color: #4a5568 !important;
}

body.dark-mode .shipments-table-component th:last-child {
    background-color: #1a202c !important;
    color: #ffffff !important;
}

body.dark-mode .shipments-table-component tbody,
body.dark-mode .shipments-table-component tbody tr,
body.dark-mode .shipments-table-component tbody td {
    background-color: #2d3748 !important;
    color: #ffffff !important;
    border-color: #4a5568 !important;
}

body.dark-mode .shipments-table-component tbody tr:nth-child(even) {
    background-color: #374151 !important;
}

body.dark-mode .shipments-table-component tbody tr:hover {
    background-color: #4a5568 !important;
}

/* أزرار الإجراءات في الوضع الداكن */
body.dark-mode .shipments-table-component .btn-outline-primary {
    border-color: #63b3ed !important;
    color: #63b3ed !important;
    background-color: transparent !important;
}

body.dark-mode .shipments-table-component .btn-outline-primary:hover {
    background-color: #63b3ed !important;
    color: #1a202c !important;
}

body.dark-mode .shipments-table-component .btn-outline-warning {
    border-color: #fbb040 !important;
    color: #fbb040 !important;
    background-color: transparent !important;
}

body.dark-mode .shipments-table-component .btn-outline-warning:hover {
    background-color: #fbb040 !important;
    color: #1a202c !important;
}

body.dark-mode .shipments-table-component .btn-outline-info {
    border-color: #4fd1c7 !important;
    color: #4fd1c7 !important;
    background-color: transparent !important;
}

body.dark-mode .shipments-table-component .btn-outline-info:hover {
    background-color: #4fd1c7 !important;
    color: #1a202c !important;
}

body.dark-mode .shipments-table-component .btn-outline-secondary {
    border-color: #a0aec0 !important;
    color: #a0aec0 !important;
    background-color: transparent !important;
}

body.dark-mode .shipments-table-component .btn-outline-secondary:hover {
    background-color: #a0aec0 !important;
    color: #1a202c !important;
}

body.dark-mode .shipments-table-component .btn-outline-success {
    border-color: #68d391 !important;
    color: #68d391 !important;
    background-color: transparent !important;
}

body.dark-mode .shipments-table-component .btn-outline-success:hover {
    background-color: #68d391 !important;
    color: #1a202c !important;
}

body.dark-mode .shipments-table-component .btn-outline-danger {
    border-color: #fc8181 !important;
    color: #fc8181 !important;
    background-color: transparent !important;
}

body.dark-mode .shipments-table-component .btn-outline-danger:hover {
    background-color: #fc8181 !important;
    color: #1a202c !important;
}

/* شارات الحالة في الوضع الداكن - ألوان مميزة ومشرقة */
body.dark-mode .shipments-table-component .badge,
body.dark-mode .badge,
html.dark-mode .badge {
    color: #ffffff !important;
    font-weight: 600 !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

/* شارة النجاح - أخضر مشرق */
body.dark-mode .shipments-table-component .badge.bg-success,
body.dark-mode .badge.bg-success,
html.dark-mode .badge.bg-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%) !important;
    border-color: #68d391 !important;
    color: #ffffff !important;
}

/* شارة التحذير - أصفر/برتقالي مشرق */
body.dark-mode .shipments-table-component .badge.bg-warning,
body.dark-mode .badge.bg-warning,
html.dark-mode .badge.bg-warning {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%) !important;
    border-color: #f6ad55 !important;
    color: #ffffff !important;
}

/* شارة المعلومات - أزرق مشرق */
body.dark-mode .shipments-table-component .badge.bg-info,
body.dark-mode .badge.bg-info,
html.dark-mode .badge.bg-info {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
    border-color: #63b3ed !important;
    color: #ffffff !important;
}

/* شارة الخطر - أحمر مشرق */
body.dark-mode .shipments-table-component .badge.bg-danger,
body.dark-mode .badge.bg-danger,
html.dark-mode .badge.bg-danger {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%) !important;
    border-color: #fc8181 !important;
    color: #ffffff !important;
}

/* شارة ثانوية - رمادي مشرق */
body.dark-mode .shipments-table-component .badge.bg-secondary,
body.dark-mode .badge.bg-secondary,
html.dark-mode .badge.bg-secondary {
    background: linear-gradient(135deg, #718096 0%, #4a5568 100%) !important;
    border-color: #a0aec0 !important;
    color: #ffffff !important;
}

/* شارة أساسية - أزرق داكن */
body.dark-mode .shipments-table-component .badge.bg-primary,
body.dark-mode .badge.bg-primary,
html.dark-mode .badge.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-color: #90cdf4 !important;
    color: #ffffff !important;
}

/* تأثيرات hover للشارات في الوضع الداكن */
body.dark-mode .badge:hover,
html.dark-mode .badge:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4) !important;
    transition: all 0.2s ease !important;
}

body.dark-mode .badge.bg-success:hover,
html.dark-mode .badge.bg-success:hover {
    background: linear-gradient(135deg, #68d391 0%, #48bb78 100%) !important;
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.4) !important;
}

body.dark-mode .badge.bg-warning:hover,
html.dark-mode .badge.bg-warning:hover {
    background: linear-gradient(135deg, #f6ad55 0%, #ed8936 100%) !important;
    box-shadow: 0 4px 12px rgba(237, 137, 54, 0.4) !important;
}

body.dark-mode .badge.bg-info:hover,
html.dark-mode .badge.bg-info:hover {
    background: linear-gradient(135deg, #63b3ed 0%, #4299e1 100%) !important;
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4) !important;
}

body.dark-mode .badge.bg-danger:hover,
html.dark-mode .badge.bg-danger:hover {
    background: linear-gradient(135deg, #fc8181 0%, #f56565 100%) !important;
    box-shadow: 0 4px 12px rgba(245, 101, 101, 0.4) !important;
}

body.dark-mode .badge.bg-secondary:hover,
html.dark-mode .badge.bg-secondary:hover {
    background: linear-gradient(135deg, #a0aec0 0%, #718096 100%) !important;
    box-shadow: 0 4px 12px rgba(113, 128, 150, 0.4) !important;
}

body.dark-mode .badge.bg-primary:hover,
html.dark-mode .badge.bg-primary:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%) !important;
    box-shadow: 0 4px 12px rgba(118, 75, 162, 0.4) !important;
}

/* روابط في الوضع الداكن */
body.dark-mode .shipments-table-component a {
    color: #63b3ed !important;
}

body.dark-mode .shipments-table-component a:hover {
    color: #90cdf4 !important;
}

/* نصوص صغيرة في الوضع الداكن */
body.dark-mode .shipments-table-component .small,
body.dark-mode .shipments-table-component small,
body.dark-mode .shipments-table-component .text-muted {
    color: #a0aec0 !important;
}

/* إصلاح إضافي شامل للجدول */
body.dark-mode .table-responsive {
    background-color: #2d3748 !important;
}

body.dark-mode .table-responsive .table {
    background-color: #2d3748 !important;
    color: #ffffff !important;
}

body.dark-mode .table-responsive .table thead th {
    background-color: #1a202c !important;
    color: #ffffff !important;
    border-color: #4a5568 !important;
}

body.dark-mode .table-responsive .table tbody td {
    background-color: #2d3748 !important;
    color: #ffffff !important;
    border-color: #4a5568 !important;
}

body.dark-mode .table-responsive .table tbody tr:nth-child(even) td {
    background-color: #374151 !important;
}

body.dark-mode .table-responsive .table tbody tr:hover td {
    background-color: #4a5568 !important;
}

/* فرض الوضع الداكن على جميع عناصر الجدول */
body.dark-mode table,
body.dark-mode .table {
    background-color: #2d3748 !important;
    color: #ffffff !important;
}

body.dark-mode table thead,
body.dark-mode table thead th,
body.dark-mode .table thead,
body.dark-mode .table thead th {
    background-color: #1a202c !important;
    color: #ffffff !important;
    border-color: #4a5568 !important;
}

body.dark-mode table tbody,
body.dark-mode table tbody tr,
body.dark-mode table tbody td,
body.dark-mode .table tbody,
body.dark-mode .table tbody tr,
body.dark-mode .table tbody td {
    background-color: #2d3748 !important;
    color: #ffffff !important;
    border-color: #4a5568 !important;
}

body.dark-mode table tbody tr:nth-child(even),
body.dark-mode .table tbody tr:nth-child(even) {
    background-color: #374151 !important;
}

body.dark-mode table tbody tr:hover,
body.dark-mode .table tbody tr:hover {
    background-color: #4a5568 !important;
}

/* تحسين عرض الجدول */
.shipments-table-component table {
    table-layout: auto;
    width: 100%;
    min-width: 1400px; /* ضمان عرض كافي للأزرار */
}

/* تحسين hover للصف مع عمود الإجراءات الثابت */
.shipments-table-component tbody tr:hover td:last-child {
    background-color: #f8f9fa;
}

/* تحسين responsive للأزرار */
@media (max-width: 768px) {
    .shipments-table-component .btn-group .btn {
        width: 28px;
        height: 24px;
        padding: 0.125rem 0.25rem;
    }

    .shipments-table-component .btn-group .btn i {
        font-size: 0.7rem;
    }

    .shipments-table-component th:last-child,
    .shipments-table-component td:last-child {
        min-width: 280px;
        box-shadow: -1px 0 2px rgba(0,0,0,0.1);
    }
}
</style>

<!-- مكون جدول الشحنات -->
<div class="shipments-table-component">
    {% if recent_shipments %}
        <div class="table-responsive">
            <table id="shipmentsTable" class="table table-hover mb-0">
                <thead class="bg-light">
                    <tr>
                        <th>تاريخ الشحنة</th>
                        <!-- تم إخفاء عمود رقم التتبع لتوفير مساحة للأزرار -->
                        <!-- <th>رقم التتبع</th> -->
                        <th>المرسل</th>
                        <th>تفاصيل البضاعة</th>
                        <th>عدد الطرود</th>
                        <!-- <th>الوزن الإجمالي</th> -->
                        <!-- <th>الوزن الصافي</th> -->
                        <th>ميناء الوصول</th>
                        <th>رقم البوليصة</th>
                        <th>أرقام الحاويات</th>
                        <th>عدد الحاويات</th>
                        <th>خط الشحن</th>
                        <th>حالة الشحنة</th>
                        <th>حالة الإفراج</th>
                        <th>التقدم</th>
                        <th style="min-width: 280px;">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for shipment in recent_shipments %}
                    <!-- شحنة {{ loop.index }}: ID={{ shipment.id }}, رقم التتبع={{ shipment.tracking_number }} -->
                    <tr>
                        <td class="text-muted small">
                            {{ shipment.shipment_date.strftime('%Y-%m-%d') if shipment.shipment_date else 'غير محدد' }}
                        </td>
                        <!-- تم إخفاء خلية رقم التتبع لتوفير مساحة -->
                        <!-- <td>
                            <span class="tracking-number fw-bold text-primary">{{ shipment.tracking_number }}</span>
                        </td> -->
                        <td class="text-muted">
                            {{ shipment.sender_name or 'غير محدد' }}
                        </td>
                        <td>
                            <div class="cargo-description" style="max-width: 200px;">
                                {% if shipment.cargo_description %}
                                    <span class="text-truncate d-block" title="{{ shipment.cargo_description }}">
                                        {{ shipment.cargo_description[:50] }}{% if shipment.cargo_description|length > 50 %}...{% endif %}
                                    </span>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="text-center">
                            {% set package_count = shipment.total_packages or 0 %}
                            {% if package_count == 0 %}
                                <span class="badge bg-secondary">{{ package_count }}</span>
                            {% elif package_count <= 5 %}
                                <span class="badge bg-success">{{ package_count }}</span>
                            {% elif package_count <= 20 %}
                                <span class="badge bg-info">{{ package_count }}</span>
                            {% elif package_count <= 50 %}
                                <span class="badge bg-warning">{{ package_count }}</span>
                            {% else %}
                                <span class="badge bg-danger">{{ package_count }}</span>
                            {% endif %}
                        </td>
                        <!-- تم إخفاء خلايا الوزن الإجمالي والوزن الصافي -->
                        <!-- <td class="text-center">
                            {% if shipment.total_weight %}
                                <span class="badge bg-primary">
                                    <i class="fas fa-weight-hanging me-1"></i>
                                    {{ "%.1f"|format(shipment.total_weight) }} كجم
                                </span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td> -->
                        <!-- <td class="text-center">
                            {% if shipment.net_weight %}
                                <span class="badge bg-success">
                                    <i class="fas fa-balance-scale me-1"></i>
                                    {{ "%.1f"|format(shipment.net_weight) }} كجم
                                </span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td> -->
                        <td class="text-muted">
                            {{ shipment.destination_port or 'غير محدد' }}
                        </td>
                        <td class="text-muted small">
                            {{ shipment.bill_of_lading_number or 'غير محدد' }}
                        </td>
                        <td>
                            {% if shipment.container_numbers %}
                                <div class="container-numbers" style="max-width: 150px;">
                                    <span class="text-truncate d-block small" title="{{ shipment.container_numbers }}">
                                        {{ shipment.container_numbers[:20] }}{% if shipment.container_numbers|length > 20 %}...{% endif %}
                                    </span>
                                </div>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% set container_count = shipment.container_count or 0 %}
                            {% if container_count == 0 %}
                                <span class="badge bg-secondary">{{ container_count }}</span>
                            {% elif container_count == 1 %}
                                <span class="badge bg-success">{{ container_count }}</span>
                            {% elif container_count <= 3 %}
                                <span class="badge bg-info">{{ container_count }}</span>
                            {% elif container_count <= 10 %}
                                <span class="badge bg-warning">{{ container_count }}</span>
                            {% else %}
                                <span class="badge bg-danger">{{ container_count }}</span>
                            {% endif %}
                        </td>
                        <td class="text-muted small">
                            {{ shipment.shipping_line or 'غير محدد' }}
                        </td>
                        <td>
                            {% set status_class = {
                                'مسودة': 'secondary',
                                'مؤكد': 'primary',
                                'في الطريق': 'warning',
                                'وصلت للميناء': 'info',
                                'قيد التخليص': 'warning',
                                'خارج للتسليم': 'warning',
                                'جاهز للاستلام': 'info',
                                'تم التسليم': 'success',
                                'ملغي': 'danger',
                                'مرتجع': 'secondary',
                                'متأخر': 'danger'
                            } %}
                            <span class="badge bg-{{ status_class.get(shipment.shipment_status_display, 'secondary') }} status-badge clickable"
                                  data-shipment-id="{{ shipment.id }}"
                                  data-tracking-number="{{ shipment.tracking_number }}"
                                  data-current-status="{{ shipment.shipment_status }}"
                                  data-current-status-display="{{ shipment.shipment_status_display }}"
                                  onclick="openStatusModal(this)"
                                  title="انقر لتعديل الحالة">
                                <i class="fas fa-edit me-1" style="font-size: 0.8em;"></i>
                                {{ shipment.shipment_status_display or 'غير محدد' }}
                            </span>
                        </td>
                        <td>
                            {% set release_class = {
                                'في انتظار الإفراج': 'warning',
                                'مراجعة المستندات': 'info',
                                'التحقق من المدفوعات': 'info',
                                'فحص الجودة': 'info',
                                'معتمد للإفراج': 'primary',
                                'تم الإفراج': 'success',
                                'محجوز مؤقت': 'warning',
                                'مرفوض الإفراج': 'danger'
                            } %}
                            <span class="badge bg-{{ release_class.get(shipment.release_status_display, 'secondary') }} clickable-status"
                                  title="انقر لتعديل حالة الإفراج"
                                  onclick="openReleaseStatusModal(this)"
                                  data-shipment-id="{{ shipment.id }}"
                                  data-tracking-number="{{ shipment.tracking_number }}"
                                  data-current-release-status="{{ shipment.release_status or 'pending' }}"
                                  data-current-release-status-display="{{ shipment.release_status_display or 'في انتظار الإفراج' }}"
                                  style="cursor: pointer;">
                                <i class="fas fa-unlock-alt me-1"></i>
                                {{ shipment.release_status_display or 'غير محدد' }}
                            </span>
                        </td>
                        <td>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: {{ shipment.progress_percentage or 0 }}%"
                                     aria-valuenow="{{ shipment.progress_percentage or 0 }}" 
                                     aria-valuemin="0" aria-valuemax="100">
                                    {{ shipment.progress_percentage or 0 }}%
                                </div>
                            </div>
                        </td>
                        <td style="white-space: nowrap; min-width: 280px;">
                            <!-- جميع الأزرار في مجموعة واحدة - مساحة أكبر بعد إخفاء عمود رقم التتبع -->
                            <div class="btn-group btn-group-sm" role="group" style="display: flex; flex-wrap: nowrap; gap: 2px;">
                                <!-- عرض التفاصيل -->
                                <button type="button" class="btn btn-outline-primary"
                                        onclick="viewShipmentDetails('{{ shipment.id }}')"
                                        title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                    <span class="icon-fallback">👁️</span>
                                </button>

                                <!-- تعديل الشحنة -->
                                <a href="{{ url_for('shipments.new_cargo_shipment', shipment_id=shipment.id) }}"
                                   class="btn btn-outline-warning"
                                   title="تعديل الشحنة">
                                    <i class="fas fa-edit"></i>
                                    <span class="icon-fallback">✏️</span>
                                </a>

                                {% if shipment.cargo_id %}
                                <!-- إدارة الوثائق -->
                                <a href="{{ url_for('shipments.cargo_documents', shipment_id=shipment.cargo_id) }}"
                                   class="btn btn-outline-info"
                                   title="إدارة الوثائق">
                                    <i class="fas fa-file-alt"></i>
                                    <span class="icon-fallback">📄</span>
                                </a>

                                <!-- تتبع الأصناف -->
                                <a href="{{ url_for('shipments.items_tracking', shipment_id=shipment.id) }}"
                                   class="btn btn-outline-secondary"
                                   title="تتبع الأصناف">
                                    <i class="fas fa-boxes"></i>
                                    <span class="icon-fallback">📦</span>
                                </a>
                                {% endif %}

                                <!-- الخريطة -->
                                <button type="button" class="btn btn-outline-info"
                                        onclick="showShipmentMap('{{ shipment.tracking_number }}')"
                                        title="عرض على الخريطة">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span class="icon-fallback">🗺️</span>
                                </button>

                                <!-- إرسال إشعار -->
                                <button type="button" class="btn btn-outline-success"
                                        onclick="sendNotification('{{ shipment.id }}')"
                                        title="إرسال إشعار">
                                    <i class="fas fa-bell"></i>
                                    <span class="icon-fallback">🔔</span>
                                </button>

                                <!-- حذف الشحنة -->
                                <button type="button" class="btn btn-outline-danger"
                                        onclick="deleteShipment('{{ shipment.id }}', '{{ shipment.tracking_number }}')"
                                        title="حذف الشحنة">
                                    <i class="fas fa-trash"></i>
                                    <span class="icon-fallback">🗑️</span>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- معلومات إضافية -->
        <div class="table-footer mt-3 p-3 bg-light rounded">
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        عرض {{ recent_shipments|length }} شحنة من إجمالي {{ stats.total or 0 }} شحنة
                    </small>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        آخر تحديث: {{ moment().format('YYYY-MM-DD HH:mm') }}
                    </small>
                </div>
            </div>
        </div>
    {% else %}
        <!-- رسالة عدم وجود بيانات -->
        <div class="text-center py-5">
            <div class="mb-3">
                <i class="fas fa-ship fa-3x text-muted"></i>
            </div>
            <h5 class="text-muted">لا توجد شحنات للعرض</h5>
            <p class="text-muted">لم يتم العثور على أي شحنات تطابق المعايير المحددة</p>
            <div class="mt-3">
                <a href="{{ url_for('shipments.new_cargo_shipment') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة شحنة جديدة
                </a>
                <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetAllFilters()">
                    <i class="fas fa-undo me-2"></i>
                    إعادة تعيين المرشحات
                </button>
            </div>
        </div>
    {% endif %}
</div>

<style>
/* تحسينات CSS لجدول الشحنات */
.shipments-table-component .table {
    font-size: 0.9rem;
}

.shipments-table-component .table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    white-space: nowrap;
    padding: 12px 8px;
}

.shipments-table-component .table td {
    padding: 12px 8px;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.shipments-table-component .table tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

.shipments-table-component .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
}

.shipments-table-component .progress {
    border-radius: 10px;
    background-color: #e9ecef;
}

.shipments-table-component .progress-bar {
    border-radius: 10px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.shipments-table-component .btn-group .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.shipments-table-component .cargo-description,
.shipments-table-component .container-numbers {
    cursor: help;
}

.shipments-table-component .table-footer {
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa !important;
}

@media (max-width: 768px) {
    /* تحسين الجدول للجوال */
    .shipments-table-component {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
    }

    .shipments-table-component .table {
        font-size: 0.75rem !important;
        min-width: 800px !important; /* عرض أدنى للتمرير الأفقي */
    }

    .shipments-table-component .table th,
    .shipments-table-component .table td {
        padding: 6px 4px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }

    /* تحسين الأزرار */
    .shipments-table-component .btn-group {
        flex-direction: column !important;
        gap: 2px !important;
    }

    .shipments-table-component .btn-group .btn {
        padding: 0.2rem 0.4rem !important;
        font-size: 0.7rem !important;
        margin-bottom: 2px !important;
        width: 100% !important;
    }

    /* إخفاء أعمدة أقل أهمية */
    .shipments-table-component .table th:nth-child(7),
    .shipments-table-component .table td:nth-child(7),
    .shipments-table-component .table th:nth-child(8),
    .shipments-table-component .table td:nth-child(8),
    .shipments-table-component .table th:nth-child(9),
    .shipments-table-component .table td:nth-child(9) {
        display: none !important;
    }

    /* تحسين البادجات */
    .shipments-table-component .badge {
        font-size: 0.6rem !important;
        padding: 0.2rem 0.3rem !important;
    }

    /* تحسين شريط التقدم */
    .shipments-table-component .progress {
        height: 8px !important;
    }

    /* تحسين الفلاتر */
    .filters-section .row {
        margin: 0 !important;
    }

    .filters-section .col-md-3,
    .filters-section .col-md-4 {
        padding: 2px !important;
        margin-bottom: 8px !important;
    }

    .filters-section .form-control {
        font-size: 0.8rem !important;
        padding: 0.3rem 0.5rem !important;
    }

    .filters-section .btn {
        padding: 0.3rem 0.6rem !important;
        font-size: 0.8rem !important;
        width: 100% !important;
        margin-bottom: 5px !important;
    }
}

@media (max-width: 576px) {
    /* شاشات صغيرة جداً */
    .shipments-table-component .table {
        font-size: 0.7rem !important;
        min-width: 600px !important;
    }

    .shipments-table-component .table th,
    .shipments-table-component .table td {
        padding: 4px 2px !important;
        max-width: 80px !important;
    }

    /* إخفاء المزيد من الأعمدة */
    .shipments-table-component .table th:nth-child(n+6),
    .shipments-table-component .table td:nth-child(n+6) {
        display: none !important;
    }

    .shipments-table-component .btn-group .btn {
        padding: 0.15rem 0.3rem !important;
        font-size: 0.65rem !important;
    }

    .shipments-table-component .badge {
        font-size: 0.55rem !important;
        padding: 0.15rem 0.25rem !important;
    }

    /* تحسين الفلاتر للشاشات الصغيرة */
    .filters-section .col-md-3,
    .filters-section .col-md-4 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
    }
}

    /* تنسيق خلية الحالة القابلة للنقر */
    .status-badge.clickable {
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
    }

    .status-badge.clickable:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        opacity: 0.9;
    }

    .status-badge.clickable:active {
        transform: scale(0.98);
    }
}
</style>

<!-- JavaScript محذوف لتحسين الأداء -->
