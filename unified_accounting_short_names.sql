-- ========================================================================
-- نظام محاسبي موحد - تسميات مختصرة متوافقة مع Oracle
-- Unified Accounting System - Oracle Compatible Short Names
-- ========================================================================

-- ========================================================================
-- تعديل بنية BALANCE_TRANSACTIONS مع أسماء مختصرة
-- ========================================================================

-- إضافة الأعمدة الجديدة بأسماء مختصرة
ALTER TABLE BALANCE_TRANSACTIONS ADD (
    BAL NUMBER(15,2) DEFAULT 0,              -- رصيد موحد (بدلاً من BALANCE)
    BAL_F NUMBER(15,2) DEFAULT 0,            -- رصيد بالعملة الأساسية (بدلاً من BALANCE_F)
    MONTH_NO NUMBER(2),                      -- رقم الشهر (بدلاً من MONTH_NUMBER)
    YEAR_NO NUMBER(4),                       -- رقم السنة (بدلاً من YEAR_NUMBER)
    BRANCH_ID NUMBER DEFAULT 1               -- رقم الفرع
);

-- فهارس محسنة بأسماء مختصرة
CREATE INDEX IDX_BT_ENT_BAL ON BALANCE_TRANSACTIONS(ENTITY_TYPE_CODE, ENTITY_ID, CURRENCY_CODE);
CREATE INDEX IDX_BT_PERIOD ON BALANCE_TRANSACTIONS(YEAR_NO, MONTH_NO);
CREATE INDEX IDX_BT_BRANCH ON BALANCE_TRANSACTIONS(BRANCH_ID);
CREATE INDEX IDX_BT_DOC ON BALANCE_TRANSACTIONS(DOCUMENT_TYPE_CODE, DOCUMENT_NUMBER);

-- ========================================================================
-- Package الأرصدة الافتتاحية - أسماء مختصرة
-- ========================================================================

CREATE OR REPLACE PACKAGE OB_PKG AS
    
    -- إدراج رصيد افتتاحي
    PROCEDURE INSERT_BAL(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_amount IN NUMBER,
        p_branch IN NUMBER DEFAULT 1,
        p_year IN NUMBER DEFAULT EXTRACT(YEAR FROM SYSDATE),
        p_user IN NUMBER DEFAULT 1
    );
    
    -- تعديل رصيد افتتاحي
    PROCEDURE UPDATE_BAL(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_amount IN NUMBER,
        p_branch IN NUMBER DEFAULT 1,
        p_year IN NUMBER DEFAULT EXTRACT(YEAR FROM SYSDATE),
        p_user IN NUMBER DEFAULT 1
    );
    
    -- حذف رصيد افتتاحي
    PROCEDURE DELETE_BAL(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_branch IN NUMBER DEFAULT 1,
        p_year IN NUMBER DEFAULT EXTRACT(YEAR FROM SYSDATE)
    );
    
    -- الحصول على رصيد افتتاحي
    FUNCTION GET_BAL(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_branch IN NUMBER DEFAULT 1,
        p_year IN NUMBER DEFAULT EXTRACT(YEAR FROM SYSDATE)
    ) RETURN NUMBER;
    
END OB_PKG;
/

-- ========================================================================
-- Package ترحيل الأرصدة - أسماء مختصرة
-- ========================================================================

CREATE OR REPLACE PACKAGE BT_PKG AS
    
    -- ترحيل معاملة
    PROCEDURE POST_TXN(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_doc_type IN VARCHAR2,
        p_doc_no IN VARCHAR2,
        p_doc_date IN DATE,
        p_curr IN VARCHAR2,
        p_dr IN NUMBER DEFAULT 0,
        p_cr IN NUMBER DEFAULT 0,
        p_rate IN NUMBER DEFAULT 1,
        p_desc IN VARCHAR2 DEFAULT NULL,
        p_branch IN NUMBER DEFAULT 1,
        p_user IN NUMBER DEFAULT 1
    );
    
    -- عكس معاملة
    PROCEDURE REVERSE_TXN(
        p_orig_doc IN VARCHAR2,
        p_rev_doc IN VARCHAR2,
        p_reason IN VARCHAR2,
        p_user IN NUMBER DEFAULT 1
    );
    
    -- الحصول على الرصيد الحالي
    FUNCTION GET_BAL(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_branch IN NUMBER DEFAULT 1,
        p_date IN DATE DEFAULT SYSDATE
    ) RETURN NUMBER;
    
    -- الحصول على رصيد شهري
    FUNCTION GET_MONTH_BAL(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_year IN NUMBER,
        p_month IN NUMBER,
        p_branch IN NUMBER DEFAULT 1
    ) RETURN NUMBER;
    
    -- الحصول على تاريخ الرصيد
    PROCEDURE GET_BAL_HIST(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_from IN DATE,
        p_to IN DATE,
        p_branch IN NUMBER DEFAULT 1,
        p_cursor OUT SYS_REFCURSOR
    );
    
END BT_PKG;
/

-- ========================================================================
-- Views بأسماء مختصرة
-- ========================================================================

-- عرض الأرصدة الحالية
CREATE OR REPLACE VIEW V_CURR_BAL AS
SELECT 
    entity_type_code as ent_type,
    entity_id as ent_id,
    currency_code as curr,
    branch_id as branch,
    SUM(bal) as curr_bal,
    SUM(bal_f) as curr_bal_f,
    COUNT(*) as txn_count,
    MAX(document_date) as last_txn_date
FROM BALANCE_TRANSACTIONS
GROUP BY entity_type_code, entity_id, currency_code, branch_id;

-- عرض الأرصدة الشهرية
CREATE OR REPLACE VIEW V_MONTH_BAL AS
SELECT 
    entity_type_code as ent_type,
    entity_id as ent_id,
    currency_code as curr,
    branch_id as branch,
    year_no as year,
    month_no as month,
    SUM(bal) as month_bal,
    SUM(bal_f) as month_bal_f,
    COUNT(*) as month_txn_count
FROM BALANCE_TRANSACTIONS
GROUP BY entity_type_code, entity_id, currency_code, branch_id, year_no, month_no;

-- ========================================================================
-- تنفيذ Package الأرصدة الافتتاحية
-- ========================================================================

CREATE OR REPLACE PACKAGE BODY OB_PKG AS

    PROCEDURE INSERT_BAL(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_amount IN NUMBER,
        p_branch IN NUMBER DEFAULT 1,
        p_year IN NUMBER DEFAULT EXTRACT(YEAR FROM SYSDATE),
        p_user IN NUMBER DEFAULT 1
    ) AS
        v_doc_no VARCHAR2(30);
    BEGIN
        v_doc_no := 'OB-' || p_year || '-' || p_ent_type || '-' || p_ent_id;
        
        INSERT INTO BALANCE_TRANSACTIONS (
            entity_type_code, entity_id, document_type_code,
            document_number, document_date, currency_code,
            debit_amount, credit_amount, bal, bal_f,
            month_no, year_no, branch_id,
            description, status, created_by, created_date
        ) VALUES (
            p_ent_type, p_ent_id, 'OPENING_BAL',
            v_doc_no, DATE '2024-01-01', p_curr,
            CASE WHEN p_amount > 0 THEN p_amount ELSE 0 END,
            CASE WHEN p_amount < 0 THEN ABS(p_amount) ELSE 0 END,
            p_amount, p_amount,
            1, p_year, p_branch,
            'Opening balance',
            'POSTED', p_user, CURRENT_TIMESTAMP
        );
        
        COMMIT;
    END INSERT_BAL;

    PROCEDURE UPDATE_BAL(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_amount IN NUMBER,
        p_branch IN NUMBER DEFAULT 1,
        p_year IN NUMBER DEFAULT EXTRACT(YEAR FROM SYSDATE),
        p_user IN NUMBER DEFAULT 1
    ) AS
        v_old_bal NUMBER;
        v_adj NUMBER;
        v_doc_no VARCHAR2(30);
    BEGIN
        v_old_bal := GET_BAL(p_ent_type, p_ent_id, p_curr, p_branch, p_year);
        v_adj := p_amount - v_old_bal;
        
        IF v_adj != 0 THEN
            v_doc_no := 'OB-ADJ-' || p_year || '-' || p_ent_id;
            
            INSERT INTO BALANCE_TRANSACTIONS (
                entity_type_code, entity_id, document_type_code,
                document_number, document_date, currency_code,
                debit_amount, credit_amount, bal, bal_f,
                month_no, year_no, branch_id,
                description, status, created_by, created_date
            ) VALUES (
                p_ent_type, p_ent_id, 'OB_ADJ',
                v_doc_no, SYSDATE, p_curr,
                CASE WHEN v_adj > 0 THEN v_adj ELSE 0 END,
                CASE WHEN v_adj < 0 THEN ABS(v_adj) ELSE 0 END,
                v_adj, v_adj,
                EXTRACT(MONTH FROM SYSDATE), p_year, p_branch,
                'Opening balance adjustment',
                'POSTED', p_user, CURRENT_TIMESTAMP
            );
            
            COMMIT;
        END IF;
    END UPDATE_BAL;

    PROCEDURE DELETE_BAL(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_branch IN NUMBER DEFAULT 1,
        p_year IN NUMBER DEFAULT EXTRACT(YEAR FROM SYSDATE)
    ) AS
        v_curr_bal NUMBER;
        v_doc_no VARCHAR2(30);
    BEGIN
        v_curr_bal := GET_BAL(p_ent_type, p_ent_id, p_curr, p_branch, p_year);
        
        IF v_curr_bal != 0 THEN
            v_doc_no := 'OB-DEL-' || p_year || '-' || p_ent_id;
            
            INSERT INTO BALANCE_TRANSACTIONS (
                entity_type_code, entity_id, document_type_code,
                document_number, document_date, currency_code,
                debit_amount, credit_amount, bal, bal_f,
                month_no, year_no, branch_id,
                description, status, created_by, created_date
            ) VALUES (
                p_ent_type, p_ent_id, 'OB_DEL',
                v_doc_no, SYSDATE, p_curr,
                CASE WHEN v_curr_bal < 0 THEN ABS(v_curr_bal) ELSE 0 END,
                CASE WHEN v_curr_bal > 0 THEN v_curr_bal ELSE 0 END,
                -v_curr_bal, -v_curr_bal,
                EXTRACT(MONTH FROM SYSDATE), p_year, p_branch,
                'Opening balance deletion',
                'POSTED', 1, CURRENT_TIMESTAMP
            );
            
            COMMIT;
        END IF;
    END DELETE_BAL;

    FUNCTION GET_BAL(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_branch IN NUMBER DEFAULT 1,
        p_year IN NUMBER DEFAULT EXTRACT(YEAR FROM SYSDATE)
    ) RETURN NUMBER AS
        v_bal NUMBER := 0;
    BEGIN
        SELECT NVL(SUM(bal), 0)
        INTO v_bal
        FROM BALANCE_TRANSACTIONS
        WHERE entity_type_code = p_ent_type
        AND entity_id = p_ent_id
        AND currency_code = p_curr
        AND branch_id = p_branch
        AND year_no = p_year
        AND document_type_code IN ('OPENING_BAL', 'OB_ADJ');
        
        RETURN v_bal;
    EXCEPTION
        WHEN OTHERS THEN
            RETURN 0;
    END GET_BAL;

END OB_PKG;
/

-- ========================================================================
-- تنفيذ Package ترحيل الأرصدة
-- ========================================================================

CREATE OR REPLACE PACKAGE BODY BT_PKG AS

    PROCEDURE POST_TXN(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_doc_type IN VARCHAR2,
        p_doc_no IN VARCHAR2,
        p_doc_date IN DATE,
        p_curr IN VARCHAR2,
        p_dr IN NUMBER DEFAULT 0,
        p_cr IN NUMBER DEFAULT 0,
        p_rate IN NUMBER DEFAULT 1,
        p_desc IN VARCHAR2 DEFAULT NULL,
        p_branch IN NUMBER DEFAULT 1,
        p_user IN NUMBER DEFAULT 1
    ) AS
        v_bal NUMBER;
        v_bal_f NUMBER;
        v_month NUMBER;
        v_year NUMBER;
    BEGIN
        v_bal := NVL(p_dr, 0) - NVL(p_cr, 0);
        v_bal_f := v_bal * NVL(p_rate, 1);
        v_month := EXTRACT(MONTH FROM p_doc_date);
        v_year := EXTRACT(YEAR FROM p_doc_date);

        INSERT INTO BALANCE_TRANSACTIONS (
            entity_type_code, entity_id, document_type_code,
            document_number, document_date, currency_code,
            debit_amount, credit_amount, exchange_rate,
            bal, bal_f, month_no, year_no, branch_id,
            description, status, created_by, created_date
        ) VALUES (
            p_ent_type, p_ent_id, p_doc_type,
            p_doc_no, p_doc_date, p_curr,
            NVL(p_dr, 0), NVL(p_cr, 0), NVL(p_rate, 1),
            v_bal, v_bal_f, v_month, v_year, p_branch,
            p_desc, 'POSTED', p_user, CURRENT_TIMESTAMP
        );

        COMMIT;
    END POST_TXN;

    PROCEDURE REVERSE_TXN(
        p_orig_doc IN VARCHAR2,
        p_rev_doc IN VARCHAR2,
        p_reason IN VARCHAR2,
        p_user IN NUMBER DEFAULT 1
    ) AS
        CURSOR c_orig IS
            SELECT * FROM BALANCE_TRANSACTIONS
            WHERE document_number = p_orig_doc
            AND status = 'POSTED';
    BEGIN
        FOR rec IN c_orig LOOP
            INSERT INTO BALANCE_TRANSACTIONS (
                entity_type_code, entity_id, document_type_code,
                document_number, document_date, currency_code,
                debit_amount, credit_amount, exchange_rate,
                bal, bal_f, month_no, year_no, branch_id,
                description, reference_number, status, created_by, created_date
            ) VALUES (
                rec.entity_type_code, rec.entity_id, rec.document_type_code || '_REV',
                p_rev_doc, SYSDATE, rec.currency_code,
                rec.credit_amount, rec.debit_amount, rec.exchange_rate,
                -rec.bal, -rec.bal_f,
                EXTRACT(MONTH FROM SYSDATE), EXTRACT(YEAR FROM SYSDATE), rec.branch_id,
                'Reversal: ' || p_reason, p_orig_doc,
                'POSTED', p_user, CURRENT_TIMESTAMP
            );
        END LOOP;

        COMMIT;
    END REVERSE_TXN;

    FUNCTION GET_BAL(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_branch IN NUMBER DEFAULT 1,
        p_date IN DATE DEFAULT SYSDATE
    ) RETURN NUMBER AS
        v_bal NUMBER := 0;
    BEGIN
        SELECT NVL(SUM(bal), 0)
        INTO v_bal
        FROM BALANCE_TRANSACTIONS
        WHERE entity_type_code = p_ent_type
        AND entity_id = p_ent_id
        AND currency_code = p_curr
        AND branch_id = p_branch
        AND document_date <= p_date
        AND status = 'POSTED';

        RETURN v_bal;
    EXCEPTION
        WHEN OTHERS THEN
            RETURN 0;
    END GET_BAL;

    FUNCTION GET_MONTH_BAL(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_year IN NUMBER,
        p_month IN NUMBER,
        p_branch IN NUMBER DEFAULT 1
    ) RETURN NUMBER AS
        v_bal NUMBER := 0;
    BEGIN
        SELECT NVL(SUM(bal), 0)
        INTO v_bal
        FROM BALANCE_TRANSACTIONS
        WHERE entity_type_code = p_ent_type
        AND entity_id = p_ent_id
        AND currency_code = p_curr
        AND branch_id = p_branch
        AND year_no = p_year
        AND month_no = p_month
        AND status = 'POSTED';

        RETURN v_bal;
    EXCEPTION
        WHEN OTHERS THEN
            RETURN 0;
    END GET_MONTH_BAL;

    PROCEDURE GET_BAL_HIST(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_from IN DATE,
        p_to IN DATE,
        p_branch IN NUMBER DEFAULT 1,
        p_cursor OUT SYS_REFCURSOR
    ) AS
    BEGIN
        OPEN p_cursor FOR
            SELECT
                document_date,
                document_type_code,
                document_number,
                debit_amount,
                credit_amount,
                bal,
                SUM(bal) OVER (ORDER BY document_date, created_date) as running_bal,
                description
            FROM BALANCE_TRANSACTIONS
            WHERE entity_type_code = p_ent_type
            AND entity_id = p_ent_id
            AND currency_code = p_curr
            AND branch_id = p_branch
            AND document_date BETWEEN p_from AND p_to
            AND status = 'POSTED'
            ORDER BY document_date, created_date;
    END GET_BAL_HIST;

END BT_PKG;
/
