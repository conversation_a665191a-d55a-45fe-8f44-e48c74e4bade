<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تنفيذ الحوالات - نظام الفوجي</title>

    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Voice Search CSS -->
    <link href="{{ url_for('static', filename='css/voice-search.css') }}" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom Styles -->
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --dark-bg: #2c3e50;
            --border-color: #dee2e6;
            --text-muted: #6c757d;
            --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        /* Professional Header */
        .dashboard-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0.5rem 0 0 0;
            font-weight: 300;
        }

        /* Enterprise Buttons */
        .btn-enterprise {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            border: none;
            box-shadow: var(--shadow);
        }

        .btn-primary-enterprise {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
            color: white;
        }

        .btn-primary-enterprise:hover {
            background: linear-gradient(135deg, #2980b9 0%, var(--secondary-color) 100%);
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .btn-secondary-enterprise {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            color: white;
        }

        .btn-secondary-enterprise:hover {
            background: linear-gradient(135deg, #7f8c8d 0%, #95a5a6 100%);
            transform: translateY(-2px);
        }

        /* Header Actions */
        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        /* Breadcrumb Navigation */
        .breadcrumb-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        .breadcrumb-custom {
            background: none;
            margin: 0;
            padding: 0;
            font-size: 0.95rem;
        }

        .breadcrumb-custom .breadcrumb-item {
            color: var(--text-muted);
            font-weight: 500;
        }

        .breadcrumb-custom .breadcrumb-item.active {
            color: var(--primary-color);
            font-weight: 600;
        }

        .breadcrumb-custom .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: var(--text-muted);
            font-weight: 600;
            font-size: 1.1rem;
        }

        .breadcrumb-custom a {
            color: var(--secondary-color);
            text-decoration: none;
            transition: all 0.3s ease;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
        }

        .breadcrumb-custom a:hover {
            color: var(--primary-color);
            background-color: rgba(52, 152, 219, 0.1);
            transform: translateY(-1px);
        }

        .breadcrumb-icon {
            margin-left: 0.5rem;
            font-size: 0.9rem;
        }

        /* Enhanced Execution Cards */
        .execution-card {
            background: white;
            border-radius: 16px;
            box-shadow: var(--shadow-lg);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .execution-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 1.5rem 3rem rgba(0, 0, 0, 0.2);
        }

        .priority-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            font-weight: 600;
        }

        .priority-urgent {
            background: linear-gradient(135deg, var(--danger-color), #c0392b);
            color: white;
        }

        .priority-normal {
            background: linear-gradient(135deg, var(--success-color), #229954);
            color: white;
        }

        /* Enhanced Execution Form */
        .execution-form {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .form-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .amount-display {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--success-color);
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .days-since {
            font-weight: 600;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            font-size: 0.9rem;
        }

        .days-since.urgent {
            background: linear-gradient(135deg, var(--danger-color), #c0392b);
            color: white;
        }

        .days-since.warning {
            background: linear-gradient(135deg, var(--warning-color), #e67e22);
            color: white;
        }

        .days-since.normal {
            background: linear-gradient(135deg, var(--success-color), #229954);
            color: white;
        }

        /* Enhanced Filter Controls */
        .filter-controls {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: 0.75rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        /* Enhanced Stats Cards */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stats-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--secondary-color), var(--success-color));
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 1.5rem 3rem rgba(0, 0, 0, 0.2);
        }

        .stats-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stats-icon.primary {
            background: linear-gradient(135deg, var(--primary-color), #34495e);
        }

        .stats-icon.success {
            background: linear-gradient(135deg, var(--success-color), #229954);
        }

        .stats-icon.warning {
            background: linear-gradient(135deg, var(--warning-color), #e67e22);
        }

        .stats-icon.danger {
            background: linear-gradient(135deg, var(--danger-color), #c0392b);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin: 0.5rem 0;
        }

        .stats-label {
            font-size: 1rem;
            color: var(--text-muted);
            font-weight: 500;
        }

        /* Enhanced Modal Styles */
        .modal-xl .modal-body {
            max-height: calc(85vh - 120px);
            overflow-y: auto;
            padding: 2rem;
            background: #f8f9fa;
        }

        .modal-xl .modal-header {
            padding: 1.5rem 2rem;
            border-bottom: 2px solid var(--border-color);
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
        }

        .modal-xl .modal-footer {
            padding: 1.5rem 2rem;
            border-top: 2px solid var(--border-color);
            background: white;
        }

        .info-item {
            padding: 1rem;
            border-radius: 12px;
            background: white;
            margin-bottom: 1rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        .info-label {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .info-value {
            color: var(--text-muted);
            font-size: 1.1rem;
        }

        /* Enhanced Supplier Rows */
        .supplier-row {
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            background: white;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }

        .supplier-row:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* Enhanced Form Controls */
        .modal-xl .form-label {
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        .modal-xl .form-control,
        .modal-xl .form-select {
            padding: 0.75rem;
            font-size: 0.9rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .modal-xl .card-body {
            padding: 1.5rem;
        }

        .modal-xl .alert {
            padding: 0.75rem;
            margin-bottom: 1rem;
            border-radius: 8px;
        }

        .supplier-row .remove-supplier {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: var(--danger-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .distribution-summary {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow);
        }

        .amount-input {
            font-weight: 600;
            font-size: 1.1rem;
        }

        .amount-input:focus {
            border-color: var(--success-color);
            box-shadow: 0 0 0 0.2rem rgba(39, 174, 96, 0.25);
        }

        /* Enhanced Action Buttons */
        .btn-group {
            display: flex;
            flex-wrap: wrap;
            gap: 2px;
        }

        .btn-group .btn {
            margin: 0;
            flex: 0 0 auto;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            border-radius: 6px;
            min-width: 35px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        /* تأكد من ظهور الأزرار */
        td .btn-group {
            min-width: 200px;
            display: flex !important;
            flex-wrap: wrap;
        }

        td .dropdown {
            margin-top: 5px;
            width: 100%;
        }

        /* تحسين عرض الجدول */
        .table td {
            vertical-align: middle;
            padding: 1rem 0.75rem;
        }

        .table th:last-child,
        .table td:last-child {
            min-width: 220px;
            width: 220px;
        }

        /* تحسين الأزرار الصغيرة */
        .btn-sm i {
            font-size: 0.875rem;
        }

        .btn-group .btn-sm {
            white-space: nowrap;
        }

        .btn-outline-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), #e67e22);
            border-color: var(--warning-color);
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #e67e22, var(--warning-color));
            transform: translateY(-1px);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color), #c0392b);
            border-color: var(--danger-color);
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #c0392b, var(--danger-color));
            transform: translateY(-1px);
        }

        /* Dropdown Menu Enhancements */
        .dropdown-menu {
            border-radius: 8px;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
        }

        .dropdown-item {
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
            transform: translateX(5px);
        }

        .dropdown-item i {
            width: 20px;
            text-align: center;
        }

        /* Modal Enhancements */
        .modal-content {
            border-radius: 16px;
            border: none;
            box-shadow: var(--shadow-lg);
        }

        .modal-header {
            border-radius: 16px 16px 0 0;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-footer {
            border-radius: 0 0 16px 16px;
            border-top: 1px solid var(--border-color);
        }

        /* Alert Styling */
        .alert {
            border-radius: 12px;
            border: none;
            box-shadow: var(--shadow);
        }

        .alert-warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da 0%, #fab1a0 100%);
            color: #721c24;
        }
    </style>
</head>
<body>
<!-- Professional Header -->
<div class="dashboard-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="page-title">
                    <i class="fas fa-play-circle ms-3"></i>
                    تنفيذ الحوالات
                </h1>
                <p class="page-subtitle">
                    تنفيذ وإدارة الحوالات المالية مع توزيع ذكي على الموردين وتتبع شامل للعمليات
                </p>
            </div>
            <div class="col-lg-4">
                <div class="header-actions d-flex gap-2 justify-content-lg-end">
                    <button class="btn-enterprise btn-secondary-enterprise" onclick="refreshExecutionData()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                    <div class="dropdown">
                        <button class="btn-enterprise btn-secondary-enterprise dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="exportExecutionReport()"><i class="fas fa-file-excel me-2"></i>تقرير Excel</a></li>
                            <li><a class="dropdown-item" href="#" onclick="printExecutionList()"><i class="fas fa-print me-2"></i>طباعة</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="breadcrumb-container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb breadcrumb-custom">
                <li class="breadcrumb-item">
                    <a href="/" onclick="navigateToHome()">
                        <i class="fas fa-home breadcrumb-icon"></i>
                        الرئيسية
                    </a>
                </li>
                <li class="breadcrumb-item">
                    <a href="/transfers/dashboard" onclick="navigateToTransfersDashboard()">
                        <i class="fas fa-money-bill-transfer breadcrumb-icon"></i>
                        نظام الحوالات
                    </a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">
                    <i class="fas fa-play-circle breadcrumb-icon"></i>
                    تنفيذ الحوالات
                </li>
            </ol>
        </nav>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-container">
        <div class="stats-card">
            <div class="stats-header">
                <div class="stats-icon primary">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
            <div class="stats-number" id="approvedCount">-</div>
            <div class="stats-label">طلبات معتمدة</div>
        </div>
        <div class="stats-card">
            <div class="stats-header">
                <div class="stats-icon warning">
                    <i class="fas fa-hourglass-half"></i>
                </div>
            </div>
            <div class="stats-number" id="pendingExecutionCount">-</div>
            <div class="stats-label">في انتظار التنفيذ</div>
        </div>
        <div class="stats-card">
            <div class="stats-header">
                <div class="stats-icon success">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
            <div class="stats-number" id="executedTodayCount">-</div>
            <div class="stats-label">منفذة اليوم</div>
        </div>
        <div class="stats-card">
            <div class="stats-header">
                <div class="stats-icon danger">
                    <i class="fas fa-dollar-sign"></i>
                </div>
            </div>
            <div class="stats-number" id="totalAmountToday">-</div>
            <div class="stats-label">إجمالي المبالغ</div>
        </div>
    </div>

    <!-- Filter Controls -->
    <div class="filter-controls">
        <div class="section-header">
            <h5 class="section-title">
                <i class="fas fa-filter me-2"></i>
                فلترة وبحث
            </h5>
        </div>
        <div class="filter-row">
            <div class="filter-group">
                <label for="priorityFilter">الأولوية</label>
                <select class="form-select" id="priorityFilter" onchange="applyFilters()">
                    <option value="">جميع الأولويات</option>
                    <option value="urgent">عاجل</option>
                    <option value="high">عالي</option>
                    <option value="normal">عادي</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="statusFilter">الحالة</label>
                <select class="form-select" id="statusFilter" onchange="applyFilters()">
                    <option value="">جميع الحالات</option>
                    <option value="approved">معتمد</option>
                    <option value="pending_execution">في انتظار التنفيذ</option>
                    <option value="executed">منفذ</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="searchInput">البحث</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" placeholder="رقم الطلب أو اسم العميل..." onkeyup="applyFilters()">
                    <button type="button" class="btn btn-outline-primary voice-search-btn" id="voiceSearchBtn" title="البحث الصوتي">
                        <i class="fas fa-microphone text-primary"></i>
                    </button>
                </div>
            </div>
            <div class="filter-group">
                <label>&nbsp;</label>
                <button class="btn btn-primary w-100" onclick="clearFilters()">
                    <i class="fas fa-times me-2"></i>مسح الفلاتر
                </button>
            </div>
        </div>
    </div>

    <!-- Approved Requests Table -->
    <div class="execution-card">
        <div class="section-header">
            <h5 class="section-title">
                <i class="fas fa-list me-2"></i>الطلبات المعتمدة للتنفيذ
            </h5>
        </div>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                    <tr>
                        <th style="border: none; padding: 1rem; font-weight: 600; color: var(--primary-color);">رقم الطلب</th>
                        <th style="border: none; padding: 1rem; font-weight: 600; color: var(--primary-color);">المستفيد</th>
                        <th style="border: none; padding: 1rem; font-weight: 600; color: var(--primary-color);">المبلغ</th>
                        <th style="border: none; padding: 1rem; font-weight: 600; color: var(--primary-color);">الغرض</th>
                        <th style="border: none; padding: 1rem; font-weight: 600; color: var(--primary-color);">الفرع</th>
                        <th style="border: none; padding: 1rem; font-weight: 600; color: var(--primary-color);">الأولوية</th>
                        <th style="border: none; padding: 1rem; font-weight: 600; color: var(--primary-color);">أيام منذ الاعتماد</th>
                        <th style="border: none; padding: 1rem; font-weight: 600; color: var(--primary-color); min-width: 200px;">الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="requestsTableBody">
                    <!-- سيتم ملء البيانات هنا بواسطة JavaScript -->
                </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Execution Wide Modal -->
<div class="modal fade" id="executionModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-xl" style="max-width: 95%; width: 95%;">
        <div class="modal-content" style="height: 85vh; max-height: 85vh;">
            <div class="modal-header bg-primary text-white">
                <h4 class="modal-title">
                    <i class="fas fa-play-circle me-2"></i>
                    تنفيذ الحوالة - نافذة كاملة
                </h4>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-4" style="background: #f8f9fa;">
                <!-- معلومات الحوالة الأساسية - مضغوطة -->
                <div class="row mb-2">
                    <div class="col-12">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-white py-2">
                                <h6 class="mb-0">
                                    <i class="fas fa-info-circle text-info me-2"></i>
                                    معلومات الحوالة
                                </h6>
                            </div>
                            <div class="card-body py-2">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="info-item">
                                            <label class="text-muted small">رقم الطلب</label>
                                            <div class="fw-bold" id="execRequestNumber">-</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="info-item">
                                            <label class="text-muted small">المستفيد</label>
                                            <div class="fw-bold" id="execBeneficiary">-</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="info-item">
                                            <label class="text-muted small">المبلغ الأصلي</label>
                                            <div class="fw-bold text-success" id="execOriginalAmount">-</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="info-item">
                                            <label class="text-muted small">البنك المستلم</label>
                                            <div class="fw-bold" id="execBankName">-</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- اختيار الموردين - مضغوط -->
                <div class="row mb-2">
                    <div class="col-12">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-white py-2 d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-users text-warning me-2"></i>
                                    اختيار الموردين للتنفيذ
                                </h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="addSupplierRow()">
                                    <i class="fas fa-plus me-1"></i>إضافة مورد
                                </button>
                            </div>
                            <div class="card-body py-2">
                                <div class="alert alert-info py-2 mb-2">
                                    <i class="fas fa-info-circle me-2"></i>
                                    يمكنك توزيع الحوالة على موردين أو أكثر. المجموع يجب أن يساوي المبلغ الأصلي.
                                </div>

                                <div id="suppliersContainer">
                                    <!-- سيتم إضافة صفوف الموردين هنا ديناميكياً -->
                                </div>

                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <div class="alert alert-secondary py-2 mb-2">
                                            <strong>المبلغ الأصلي:</strong> <span id="originalAmountDisplay" class="text-success fw-bold">0.00</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="alert alert-warning py-2 mb-2">
                                            <strong>إجمالي التوزيع:</strong> <span id="totalDistributionDisplay" class="fw-bold">0.00</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- تفاصيل التنفيذ العامة - مضغوطة -->
                <div class="row mb-2">
                    <div class="col-12">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-white py-2">
                                <h6 class="mb-0">
                                    <i class="fas fa-cogs text-success me-2"></i>
                                    تفاصيل التنفيذ العامة
                                </h6>
                            </div>
                            <div class="card-body py-2">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="executionReference" class="form-label">
                                            <i class="fas fa-hashtag me-1"></i>رقم المرجع الرئيسي
                                        </label>
                                        <input type="text" class="form-control" id="executionReference"
                                               placeholder="أدخل رقم المرجع من النظام">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="executionDate" class="form-label">
                                            <i class="fas fa-calendar me-1"></i>تاريخ التنفيذ
                                        </label>
                                        <input type="datetime-local" class="form-control" id="executionDate">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="executionMethod" class="form-label">
                                            <i class="fas fa-route me-1"></i>طريقة التنفيذ
                                        </label>
                                        <select class="form-select" id="executionMethod">
                                            <option value="bank_transfer">تحويل بنكي</option>
                                            <option value="cash_pickup">استلام نقدي</option>
                                            <option value="mobile_wallet">محفظة إلكترونية</option>
                                            <option value="mixed">مختلط</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <label for="executionNotes" class="form-label">
                                            <i class="fas fa-sticky-note me-1"></i>ملاحظات التنفيذ العامة
                                        </label>
                                        <textarea class="form-control" id="executionNotes" rows="2"
                                                  placeholder="أضف ملاحظات عامة حول التنفيذ..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer bg-light">
                <div class="d-flex justify-content-between w-100">
                    <div>
                        <button type="button" class="btn btn-outline-secondary" onclick="resetExecutionForm()">
                            <i class="fas fa-undo me-2"></i>إعادة تعيين
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="previewExecution()">
                            <i class="fas fa-eye me-2"></i>معاينة
                        </button>
                    </div>
                    <div>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </button>
                        <button type="button" class="btn btn-success btn-lg" onclick="confirmExecution()">
                            <i class="fas fa-play me-2"></i>تأكيد التنفيذ
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تعديل الطلب -->
<div class="modal fade" id="editRequestModal" tabindex="-1" aria-labelledby="editRequestModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%); color: white;">
                <h5 class="modal-title" id="editRequestModalLabel">
                    <i class="fas fa-edit me-2"></i>تعديل طلب الحوالة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه:</strong> تعديل الطلب سيؤثر على حالة التنفيذ. تأكد من صحة البيانات قبل الحفظ.
                </div>

                <form id="editRequestForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editBeneficiaryName" class="form-label">اسم المستفيد</label>
                                <input type="text" class="form-control" id="editBeneficiaryName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editAmount" class="form-label">المبلغ</label>
                                <input type="number" class="form-control" id="editAmount" step="0.01" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editPurpose" class="form-label">الغرض</label>
                                <select class="form-select" id="editPurpose" required>
                                    <option value="">اختر الغرض</option>
                                    <option value="family_support">إعالة أسرة</option>
                                    <option value="medical">علاج طبي</option>
                                    <option value="education">تعليم</option>
                                    <option value="business">تجاري</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editPriority" class="form-label">الأولوية</label>
                                <select class="form-select" id="editPriority" required>
                                    <option value="normal">عادي</option>
                                    <option value="high">عالي</option>
                                    <option value="urgent">عاجل</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="editNotes" class="form-label">ملاحظات التعديل</label>
                        <textarea class="form-control" id="editNotes" rows="3" placeholder="اذكر سبب التعديل..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>إلغاء
                </button>
                <button type="button" class="btn btn-warning" onclick="saveRequestChanges()">
                    <i class="fas fa-save me-2"></i>حفظ التعديلات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد الإلغاء -->
<div class="modal fade" id="cancelRequestModal" tabindex="-1" aria-labelledby="cancelRequestModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%); color: white;">
                <h5 class="modal-title" id="cancelRequestModalLabel">
                    <i class="fas fa-times-circle me-2"></i>إلغاء طلب الحوالة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                </div>

                <div class="mb-3">
                    <h6>تفاصيل الطلب المراد إلغاؤه:</h6>
                    <div class="card bg-light">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6"><strong>رقم الطلب:</strong></div>
                                <div class="col-6" id="cancelRequestNumber">-</div>
                            </div>
                            <div class="row">
                                <div class="col-6"><strong>المستفيد:</strong></div>
                                <div class="col-6" id="cancelBeneficiaryName">-</div>
                            </div>
                            <div class="row">
                                <div class="col-6"><strong>المبلغ:</strong></div>
                                <div class="col-6" id="cancelAmount">-</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="cancelReason" class="form-label">سبب الإلغاء <span class="text-danger">*</span></label>
                    <select class="form-select" id="cancelReason" required>
                        <option value="">اختر سبب الإلغاء</option>
                        <option value="customer_request">طلب من العميل</option>
                        <option value="insufficient_funds">عدم توفر أموال</option>
                        <option value="incorrect_data">بيانات خاطئة</option>
                        <option value="duplicate_request">طلب مكرر</option>
                        <option value="regulatory_issues">مشاكل تنظيمية</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="cancelNotes" class="form-label">ملاحظات إضافية</label>
                    <textarea class="form-control" id="cancelNotes" rows="3" placeholder="تفاصيل إضافية حول سبب الإلغاء..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-arrow-left me-2"></i>تراجع
                </button>
                <button type="button" class="btn btn-danger" onclick="confirmCancelRequest()">
                    <i class="fas fa-times-circle me-2"></i>تأكيد الإلغاء
                </button>
            </div>
        </div>
    </div>
</div>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
let approvedRequests = [];
let currentRequestId = null;
let currentRequestData = null;
let supplierRowCounter = 0;
let availableSuppliers = [];
let isDataLoaded = false; // متغير لتتبع حالة التحميل
let isLoadingData = false; // متغير لمنع التحميل المتزامن

$(document).ready(function() {
    loadApprovedRequests();
    loadAvailableSuppliers();

    // تعيين التاريخ الحالي
    const now = new Date();
    const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
    $('#executionDate').val(localDateTime);
});

// تحميل الطلبات المعتمدة
function loadApprovedRequests(forceReload = false) {
    // تجنب إعادة التحميل غير الضرورية
    if (isDataLoaded && !forceReload) {
        console.log('📋 البيانات محملة مسبقاً، تجاهل إعادة التحميل');
        return;
    }

    // تجنب التحميل المتزامن
    if (isLoadingData) {
        console.log('⏳ التحميل قيد التنفيذ، تجاهل الطلب الجديد');
        return;
    }

    isLoadingData = true;
    console.log('🔄 تحميل الطلبات المعتمدة...');

    // عرض رسالة التحميل
    showLoadingMessage();

    // محاولة تحميل البيانات الحقيقية أولاً
    console.log('🌐 إرسال طلب إلى:', '/transfers/api/approved-requests');

    $.ajax({
        url: '/transfers/api/approved-requests',
        method: 'GET',
        timeout: 10000, // 10 ثواني timeout
        dataType: 'json'
    })
        .done(function(response) {
            console.log('📡 استجابة الخادم الكاملة:', response);
            console.log('📡 نوع الاستجابة:', typeof response);
            console.log('📡 حالة النجاح:', response.success);
            console.log('📡 البيانات:', response.data);

            if (response.success) {
                // استخدام البيانات الحقيقية حتى لو كانت فارغة
                approvedRequests = response.data || [];
                displayRequests(approvedRequests);
                updateStatistics();
                isDataLoaded = true;
                console.log(`✅ تم تحميل ${approvedRequests.length} طلب معتمد من الخادم`);

                // إذا كانت البيانات فارغة، أظهر رسالة
                if (approvedRequests.length === 0) {
                    console.log('📋 لا توجد طلبات معتمدة في النظام - سيتم عرض رسالة للمستخدم');
                } else {
                    console.log('📋 تم العثور على طلبات معتمدة:', approvedRequests.map(r => r.request_number));
                }
            } else {
                console.log('❌ فشل في تحميل البيانات:', response.message || 'خطأ غير معروف');
                approvedRequests = [];
                displayRequests(approvedRequests);
                updateStatistics();
                isDataLoaded = true;
            }
        })
        .fail(function(xhr, status, error) {
            console.error('❌ فشل تحميل البيانات من الخادم:', {
                status: xhr.status,
                statusText: xhr.statusText,
                error: error,
                url: '/transfers/api/approved-requests',
                readyState: xhr.readyState,
                responseText: xhr.responseText
            });

            // تحديد نوع الخطأ
            let errorMessage = 'فشل في تحميل البيانات من الخادم';
            if (xhr.status === 404) {
                errorMessage = 'API endpoint غير موجود (404)';
            } else if (xhr.status === 500) {
                errorMessage = 'خطأ في الخادم (500)';
            } else if (xhr.status === 0) {
                errorMessage = 'لا يمكن الوصول للخادم';
            } else if (status === 'timeout') {
                errorMessage = 'انتهت مهلة الاتصال بالخادم';
            }

            // في حالة الفشل، عرض رسالة خطأ
            showErrorMessage(errorMessage);
            approvedRequests = [];
            updateStatistics();
            isDataLoaded = true;
        })
        .always(function() {
            isLoadingData = false; // إنهاء حالة التحميل في جميع الحالات
        });
}

// عرض رسالة التحميل
function showLoadingMessage() {
    const tbody = $('#requestsTableBody');
    tbody.html(`
        <tr>
            <td colspan="8" class="text-center py-5">
                <div class="d-flex flex-column align-items-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <h6 class="text-muted">جاري تحميل الطلبات المعتمدة...</h6>
                    <p class="text-muted small mb-0">يرجى الانتظار</p>
                </div>
            </td>
        </tr>
    `);
}

// عرض رسالة خطأ
function showErrorMessage(message) {
    const tbody = $('#requestsTableBody');
    tbody.html(`
        <tr>
            <td colspan="8" class="text-center py-5">
                <div class="d-flex flex-column align-items-center">
                    <i class="fas fa-exclamation-triangle fa-4x text-danger mb-3"></i>
                    <h5 class="text-danger">خطأ في تحميل البيانات</h5>
                    <p class="text-muted mb-3">${message}</p>
                    <button class="btn btn-primary btn-sm" onclick="loadApprovedRequests(true)">
                        <i class="fas fa-sync-alt me-2"></i>إعادة المحاولة
                    </button>
                </div>
            </td>
        </tr>
    `);
}

// عرض الطلبات في الجدول
function displayRequests(requests) {
    console.log('📋 عرض الطلبات في الجدول:', requests);

    const tbody = $('#requestsTableBody');

    // مسح المحتوى السابق دائماً
    tbody.empty();
    console.log('🧹 تم مسح محتوى الجدول السابق');

    if (requests.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="8" class="text-center py-5">
                    <div class="d-flex flex-column align-items-center">
                        <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد طلبات معتمدة للتنفيذ</h5>
                        <p class="text-muted mb-3">لا توجد طلبات حوالات معتمدة ومتاحة للتنفيذ حالياً</p>
                        <button class="btn btn-primary btn-sm" onclick="loadApprovedRequests(true)">
                            <i class="fas fa-sync-alt me-2"></i>تحديث البيانات
                        </button>
                    </div>
                </td>
            </tr>
        `);
        return;
    }

    requests.forEach(function(request, index) {
        console.log(`🔧 معالجة الطلب ${index + 1}:`, request);

        try {
            const row = createRequestRow(request);
            tbody.append(row);
            console.log(`✅ تم إضافة الصف ${index + 1} بنجاح`);
        } catch (error) {
            console.error(`❌ خطأ في إنشاء الصف ${index + 1}:`, error);
        }
    });

    console.log('✅ تم عرض جميع الطلبات في الجدول');

    // تحقق فوري من الأزرار
    setTimeout(() => {
        const buttons = $('#requestsTableBody button');
        const links = $('#requestsTableBody a');
        console.log('🔍 عدد الأزرار:', buttons.length);
        console.log('🔍 عدد الروابط:', links.length);
        console.log('🔍 إجمالي العناصر التفاعلية:', buttons.length + links.length);

        if (buttons.length === 0) {
            console.error('❌ لا توجد أزرار في الجدول!');
            console.log('🔍 محتوى الجدول:', $('#requestsTableBody').html().substring(0, 500));
        } else {
            console.log('✅ الأزرار موجودة وتعمل');
        }
    }, 100);
}

// إنشاء صف طلب - نسخة مبسطة
function createRequestRow(request) {
    console.log('🔧 إنشاء صف للطلب:', request);

    // استخراج البيانات مع قيم افتراضية
    const id = request.id || Math.random();
    const requestNumber = request.request_number || request.id || 'REQ-' + id;
    const beneficiaryName = request.beneficiary_name || request.beneficiary || 'غير محدد';
    const bankName = request.bank_name || request.bank || 'غير محدد';
    const amount = request.amount || 0;
    const currency = request.currency || 'SAR';
    const purpose = request.purpose || request.transfer_purpose || 'غير محدد';
    const branchName = request.branch_name || request.branch || 'غير محدد';
    const status = request.status || 'approved';

    console.log('✅ البيانات المستخرجة:', { id, requestNumber, beneficiaryName, status });
    console.log('🔧 حالة الحوالة:', status, '- الأزرار ستكون:',
        status === 'executed' ? 'تنفيذ معطل + إلغاء مفعل' :
        status === 'cancelled' ? 'تنفيذ مفعل + إلغاء معطل' :
        'تنفيذ مفعل + إلغاء معطل');

    // تحديد حالة الحوالة وإنشاء الأزرار المناسبة
    let executeButton = '';
    let cancelButton = '';
    let statusBadge = '';

    if (status === 'executed') {
        // حوالة منفذة: زر التنفيذ معطل + زر الإلغاء مفعل
        executeButton = `<button class="btn btn-sm btn-success me-1" disabled title="تم التنفيذ">
                            <i class="fas fa-check"></i> منفذ
                        </button>`;
        cancelButton = `<button class="btn btn-sm btn-danger" onclick="cancelTransfer(${id})" title="إلغاء الحوالة">
                            <i class="fas fa-times"></i> إلغاء
                        </button>`;
        statusBadge = `<span class="badge bg-success">منفذة</span>`;
    } else if (status === 'cancelled') {
        // حوالة ملغاة: زر التنفيذ مفعل + زر الإلغاء معطل
        executeButton = `<a href="/transfers/execute-transfer?id=${id}" class="btn btn-sm btn-primary me-1" title="تنفيذ الحوالة">
                            <i class="fas fa-play"></i> تنفيذ
                        </a>`;
        cancelButton = `<button class="btn btn-sm btn-secondary" disabled title="تم الإلغاء">
                            <i class="fas fa-ban"></i> ملغاة
                        </button>`;
        statusBadge = `<span class="badge bg-danger">ملغاة</span>`;
    } else {
        // حوالة غير منفذة: زر التنفيذ مفعل + زر الإلغاء معطل
        executeButton = `<a href="/transfers/execute-transfer?id=${id}" class="btn btn-sm btn-primary me-1" title="تنفيذ الحوالة">
                            <i class="fas fa-play"></i> تنفيذ
                        </a>`;
        cancelButton = `<button class="btn btn-sm btn-outline-danger" disabled title="لا يمكن الإلغاء قبل التنفيذ">
                            <i class="fas fa-times"></i> إلغاء
                        </button>`;
        statusBadge = `<span class="badge bg-warning">معتمدة</span>`;
    }

    // إنشاء HTML مع الأزرار المناسبة
    const html = `
        <tr data-request-id="${id}">
            <td><strong>${requestNumber}</strong><br><small class="text-muted">${statusBadge}</small></td>
            <td>${beneficiaryName}<br><small class="text-muted">${bankName}</small></td>
            <td><strong>${amount} ${currency}</strong></td>
            <td>${purpose}</td>
            <td>${branchName}</td>
            <td><span class="badge bg-primary">عادي</span></td>
            <td>1 يوم</td>
            <td style="min-width: 200px;">
                <div class="d-flex gap-1">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewRequestDetails(${id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${executeButton}
                    ${cancelButton}
                </div>
            </td>
        </tr>
    `;

    console.log('🔧 HTML المُنشأ:', html.substring(0, 100) + '...');
    return html;
}

// دوال الأزرار المحدثة
async function viewRequestDetails(id) {
    try {
        // الحصول على تفاصيل الحوالة
        const response = await fetch(`/transfers/accounting/details/${id}`);
        const result = await response.json();

        if (!result.success) {
            alert('خطأ في الحصول على تفاصيل الحوالة: ' + result.message);
            return;
        }

        const details = result.data;
        const transferInfo = details.transfer_info;
        const distributions = details.supplier_distributions || [];
        const activities = details.activity_log || [];

        // إنشاء نافذة منبثقة لعرض التفاصيل
        let detailsHtml = `
            <div class="modal fade" id="transferDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-info-circle me-2"></i>تفاصيل الحوالة رقم ${transferInfo.id}
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <!-- معلومات الحوالة الأساسية -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0"><i class="fas fa-file-alt me-2"></i>معلومات الحوالة</h6>
                                        </div>
                                        <div class="card-body">
                                            <table class="table table-sm">
                                                <tr><td><strong>رقم الحوالة:</strong></td><td>${transferInfo.id}</td></tr>
                                                <tr><td><strong>المبلغ:</strong></td><td>${transferInfo.amount} ${transferInfo.currency}</td></tr>
                                                <tr><td><strong>الحالة:</strong></td><td><span class="badge bg-${getStatusColor(transferInfo.status)}">${getStatusText(transferInfo.status)}</span></td></tr>
                                                <tr><td><strong>تاريخ التنفيذ:</strong></td><td>${transferInfo.executed_at || 'غير منفذ'}</td></tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0"><i class="fas fa-users me-2"></i>توزيعات الموردين</h6>
                                        </div>
                                        <div class="card-body">
                                            ${distributions.length > 0 ? `
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>المورد</th>
                                                            <th>المبلغ</th>
                                                            <th>النسبة</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        ${distributions.map(dist => `
                                                            <tr>
                                                                <td>${dist.supplier_name}</td>
                                                                <td>${dist.amount} ${dist.currency_code}</td>
                                                                <td>${dist.percentage_of_total || 0}%</td>
                                                            </tr>
                                                        `).join('')}
                                                    </tbody>
                                                </table>
                                            ` : '<p class="text-muted">لا توجد توزيعات</p>'}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- سجل الأنشطة -->
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0"><i class="fas fa-history me-2"></i>سجل الأنشطة</h6>
                                </div>
                                <div class="card-body">
                                    ${activities.length > 0 ? `
                                        <div class="timeline" style="max-height: 300px; overflow-y: auto;">
                                            ${activities.map(activity => `
                                                <div class="timeline-item mb-3">
                                                    <div class="d-flex">
                                                        <div class="flex-shrink-0">
                                                            <i class="fas fa-${getActivityIcon(activity.activity_type)} text-${getActivityColor(activity.activity_type)}"></i>
                                                        </div>
                                                        <div class="flex-grow-1 ms-3">
                                                            <h6 class="mb-1">${activity.activity_type_ar || activity.activity_type}</h6>
                                                            <p class="mb-1">${activity.description}</p>
                                                            <small class="text-muted">
                                                                ${activity.created_at} - ${activity.user_full_name || 'النظام'}
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>
                                            `).join('')}
                                        </div>
                                    ` : '<p class="text-muted">لا توجد أنشطة مسجلة</p>'}
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إزالة النافذة السابقة إن وجدت
        const existingModal = document.getElementById('transferDetailsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // إضافة النافذة الجديدة
        document.body.insertAdjacentHTML('beforeend', detailsHtml);

        // عرض النافذة
        const modal = new bootstrap.Modal(document.getElementById('transferDetailsModal'));
        modal.show();

    } catch (error) {
        console.error('خطأ في عرض تفاصيل الحوالة:', error);
        alert('حدث خطأ في الحصول على تفاصيل الحوالة');
    }
}

// دوال مساعدة للتفاصيل
function getStatusColor(status) {
    const colors = {
        'approved': 'warning',
        'executed': 'success',
        'cancelled': 'danger',
        'rejected': 'danger'
    };
    return colors[status] || 'secondary';
}

function getStatusText(status) {
    const texts = {
        'approved': 'معتمد',
        'executed': 'منفذ',
        'cancelled': 'ملغى',
        'rejected': 'مرفوض'
    };
    return texts[status] || status;
}

function getActivityIcon(activityType) {
    const icons = {
        'CREATED': 'plus',
        'APPROVED': 'check',
        'EXECUTED': 'play',
        'CANCELLED': 'times',
        'ERROR': 'exclamation-triangle',
        'BALANCE_UPDATED': 'exchange-alt'
    };
    return icons[activityType] || 'info';
}

function getActivityColor(activityType) {
    const colors = {
        'CREATED': 'info',
        'APPROVED': 'success',
        'EXECUTED': 'primary',
        'CANCELLED': 'danger',
        'ERROR': 'danger',
        'BALANCE_UPDATED': 'warning'
    };
    return colors[activityType] || 'secondary';
}

async function cancelTransfer(id) {
    try {
        // أولاً التحقق من إمكانية الإلغاء
        const checkResponse = await fetch(`/transfers/accounting/check-cancellation/${id}`);
        const checkResult = await checkResponse.json();

        if (!checkResult.success) {
            alert('خطأ في التحقق من إمكانية الإلغاء: ' + checkResult.message);
            return;
        }

        const canCancel = checkResult.data.can_cancel;
        const message = checkResult.data.message;
        const warnings = checkResult.data.warnings || [];

        if (!canCancel) {
            alert('لا يمكن إلغاء هذه الحوالة:\n' + message);
            return;
        }

        // إعداد رسالة التأكيد
        let confirmMessage = 'هل تريد إلغاء الحوالة المنفذة رقم: ' + id + '؟\n\n';
        confirmMessage += 'ملاحظة: سيتم عكس جميع الترحيلات المحاسبية وإعادة تفعيل إمكانية التنفيذ.\n\n';

        if (warnings.length > 0) {
            confirmMessage += 'تحذيرات:\n';
            warnings.forEach(warning => {
                confirmMessage += '• ' + warning + '\n';
            });
            confirmMessage += '\n';
        }

        // طلب سبب الإلغاء
        const cancellationReason = prompt(confirmMessage + 'يرجى إدخال سبب الإلغاء (اختياري):');

        if (cancellationReason === null) {
            // المستخدم ألغى العملية
            return;
        }

        // إرسال طلب إلغاء الحوالة للخادم
        const response = await fetch('/transfers/accounting/cancel', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                transfer_id: id,
                cancellation_reason: cancellationReason || undefined
            })
        });

        const result = await response.json();

        if (result.success) {
            let successMessage = 'تم إلغاء الحوالة رقم: ' + id + ' بنجاح\n';
            successMessage += 'المبلغ المُلغى: ' + result.cancelled_amount + ' ' + result.currency + '\n';

            if (result.warnings && result.warnings.length > 0) {
                successMessage += '\nتحذيرات:\n';
                result.warnings.forEach(warning => {
                    successMessage += '• ' + warning + '\n';
                });
            }

            alert(successMessage);

            // إعادة تحميل البيانات لتحديث حالة الأزرار
            loadApprovedRequests(true);
        } else {
            let errorMessage = 'فشل في إلغاء الحوالة: ' + result.message;
            if (result.error_type) {
                errorMessage += '\nنوع الخطأ: ' + result.error_type;
            }
            alert(errorMessage);
        }

    } catch (error) {
        console.error('خطأ في إلغاء الحوالة:', error);
        alert('حدث خطأ في الاتصال بالخادم');
    }
}

// تحديث الإحصائيات
function updateStatistics() {
    const totalApproved = approvedRequests.length;
    const pendingExecution = approvedRequests.filter(r => r.status !== 'executed').length;
    const executedToday = approvedRequests.filter(r => r.status === 'executed').length;
    const totalAmount = approvedRequests.reduce((sum, r) => sum + (r.amount || 0), 0);

    // تحديث البطاقات الإحصائية
    document.getElementById('approvedCount').textContent = totalApproved;
    document.getElementById('pendingExecutionCount').textContent = pendingExecution;
    document.getElementById('executedTodayCount').textContent = executedToday;
    document.getElementById('totalAmountToday').textContent = formatAmount(totalAmount) + ' ريال';

    console.log('📊 تم تحديث الإحصائيات:', {
        totalApproved,
        pendingExecution,
        executedToday,
        totalAmount
    });
}

// عرض تفاصيل الطلب
function viewRequestDetails(requestId) {
    const request = approvedRequests.find(r => r.id == requestId);
    if (!request) {
        showAlert('لم يتم العثور على الطلب!', 'danger');
        return;
    }

    // إنشاء نافذة منبثقة لعرض التفاصيل
    const detailsHtml = `
        <div class="modal fade" id="requestDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-info-circle me-2"></i>تفاصيل الطلب رقم ${request.request_number}
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label">اسم المستفيد</div>
                                    <div class="info-value">${request.beneficiary_name}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label">المبلغ</div>
                                    <div class="info-value">${formatAmount(request.amount)} ${request.currency}</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label">البنك</div>
                                    <div class="info-value">${request.bank_name}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label">الغرض</div>
                                    <div class="info-value">${request.purpose}</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label">الفرع</div>
                                    <div class="info-value">${request.branch_name}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label">الأولوية</div>
                                    <div class="info-value">${request.priority_level}</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label">تاريخ الاعتماد</div>
                                    <div class="info-value">${formatDate(request.approved_at)}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label">أيام منذ الاعتماد</div>
                                    <div class="info-value">${request.days_since_approval} يوم</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة النافذة السابقة إن وجدت
    $('#requestDetailsModal').remove();

    // إضافة النافذة الجديدة
    $('body').append(detailsHtml);

    // فتح النافذة
    const modal = new bootstrap.Modal(document.getElementById('requestDetailsModal'));
    modal.show();
}

// دوال مساعدة للتنسيق
function formatAmount(amount) {
    if (!amount) return '0';
    return new Intl.NumberFormat('ar-SA').format(amount);
}

function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// تحميل الموردين المتاحين
function loadAvailableSuppliers() {
    $.get('/transfers/api/suppliers')
        .done(function(response) {
            if (response.success) {
                availableSuppliers = response.data;
                console.log('تم تحميل الموردين:', availableSuppliers.length);
            }
        })
        .fail(function() {
            console.error('فشل في تحميل قائمة الموردين');
            availableSuppliers = [
                {id: 1, name: 'مورد افتراضي 1', type: 'bank'},
                {id: 2, name: 'مورد افتراضي 2', type: 'exchange'}
            ];
        });
}

// عرض modal التنفيذ الكامل
function showExecutionModal(requestId) {
    const request = approvedRequests.find(r => r.id === requestId);
    if (!request) return;

    currentRequestId = requestId;
    currentRequestData = request;

    // ملء معلومات الحوالة
    $('#execRequestNumber').text(request.request_number);
    $('#execBeneficiary').text(request.beneficiary_name);
    $('#execOriginalAmount').text(`${formatAmount(request.amount)} ${request.currency}`);
    $('#execBankName').text(request.bank_name);
    $('#originalAmountDisplay').text(`${formatAmount(request.amount)} ${request.currency}`);

    // إعادة تعيين النموذج
    resetExecutionForm();

    // إضافة مورد افتراضي
    addSupplierRow();

    $('#executionModal').modal('show');
}

// إضافة صف مورد جديد
function addSupplierRow() {
    supplierRowCounter++;
    const supplierId = `supplier_${supplierRowCounter}`;

    const supplierOptions = availableSuppliers.map(supplier =>
        `<option value="${supplier.id}">${supplier.name} (${supplier.type})</option>`
    ).join('');

    const supplierRow = `
        <div class="supplier-row" id="${supplierId}">
            <button type="button" class="btn btn-sm btn-outline-danger remove-supplier"
                    onclick="removeSupplierRow('${supplierId}')">
                <i class="fas fa-times"></i>
            </button>

            <div class="row mb-3">
                <div class="col-md-4">
                    <label class="form-label">
                        <i class="fas fa-building me-1"></i>المورد
                    </label>
                    <select class="form-select supplier-select" name="supplier_id" required>
                        <option value="">اختر المورد...</option>
                        ${supplierOptions}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">
                        <i class="fas fa-money-bill me-1"></i>المبلغ
                    </label>
                    <input type="number" class="form-control amount-input supplier-amount"
                           name="amount" step="0.01" placeholder="0.00"
                           onchange="calculateDistribution()" required>
                </div>
                <div class="col-md-3">
                    <label class="form-label">
                        <i class="fas fa-percentage me-1"></i>سعر الصرف
                    </label>
                    <input type="number" class="form-control" name="exchange_rate"
                           step="0.0001" value="1.0000" placeholder="1.0000">
                </div>
                <div class="col-md-2">
                    <label class="form-label">
                        <i class="fas fa-coins me-1"></i>العمولة
                    </label>
                    <input type="number" class="form-control" name="commission"
                           step="0.01" value="0.00" placeholder="0.00">
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">
                        <i class="fas fa-hashtag me-1"></i>رقم المرجع
                    </label>
                    <input type="text" class="form-control" name="reference"
                           placeholder="رقم المرجع من المورد">
                </div>
                <div class="col-md-6">
                    <label class="form-label">
                        <i class="fas fa-sticky-note me-1"></i>ملاحظات
                    </label>
                    <input type="text" class="form-control" name="notes"
                           placeholder="ملاحظات خاصة بهذا المورد">
                </div>
            </div>
        </div>
    `;

    $('#suppliersContainer').append(supplierRow);
    calculateDistribution();
}

// حذف صف مورد
function removeSupplierRow(supplierId) {
    if ($('.supplier-row').length > 1) {
        $(`#${supplierId}`).remove();
        calculateDistribution();
    } else {
        showAlert('يجب أن يكون هناك مورد واحد على الأقل', 'warning');
    }
}

// حساب توزيع المبالغ
function calculateDistribution() {
    let totalDistribution = 0;

    $('.supplier-amount').each(function() {
        const amount = parseFloat($(this).val()) || 0;
        totalDistribution += amount;
    });

    const originalAmount = currentRequestData ? currentRequestData.amount : 0;
    const currency = currentRequestData ? currentRequestData.currency : '';

    $('#totalDistributionDisplay').text(`${formatAmount(totalDistribution)} ${currency}`);

    // تغيير لون التنبيه حسب المطابقة
    const distributionAlert = $('#totalDistributionDisplay').closest('.alert');
    distributionAlert.removeClass('alert-warning alert-success alert-danger');

    if (totalDistribution === originalAmount) {
        distributionAlert.addClass('alert-success');
    } else if (totalDistribution > originalAmount) {
        distributionAlert.addClass('alert-danger');
    } else {
        distributionAlert.addClass('alert-warning');
    }
}

// إعادة تعيين النموذج
function resetExecutionForm() {
    $('#executionReference').val('');
    $('#executionNotes').val('');
    $('#suppliersContainer').empty();
    supplierRowCounter = 0;
    calculateDistribution();
}

// معاينة التنفيذ
function previewExecution() {
    const suppliers = collectSuppliersData();
    const executionData = {
        request_id: currentRequestId,
        reference: $('#executionReference').val(),
        execution_date: $('#executionDate').val(),
        execution_method: $('#executionMethod').val(),
        notes: $('#executionNotes').val(),
        suppliers: suppliers
    };

    let previewHtml = `
        <div class="modal fade" id="previewModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">معاينة التنفيذ</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <h6>معلومات عامة:</h6>
                        <p><strong>رقم المرجع:</strong> ${executionData.reference}</p>
                        <p><strong>تاريخ التنفيذ:</strong> ${executionData.execution_date}</p>
                        <p><strong>طريقة التنفيذ:</strong> ${executionData.execution_method}</p>

                        <h6>الموردين:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>المورد</th>
                                        <th>المبلغ</th>
                                        <th>سعر الصرف</th>
                                        <th>العمولة</th>
                                        <th>المرجع</th>
                                    </tr>
                                </thead>
                                <tbody>
    `;

    suppliers.forEach(supplier => {
        const supplierName = availableSuppliers.find(s => s.id == supplier.supplier_id)?.name || 'غير محدد';
        previewHtml += `
            <tr>
                <td>${supplierName}</td>
                <td>${formatAmount(supplier.amount)}</td>
                <td>${supplier.exchange_rate}</td>
                <td>${formatAmount(supplier.commission)}</td>
                <td>${supplier.reference}</td>
            </tr>
        `;
    });

    previewHtml += `
                                </tbody>
                            </table>
                        </div>

                        <h6>ملاحظات:</h6>
                        <p>${executionData.notes || 'لا توجد ملاحظات'}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة المعاينة السابقة إن وجدت
    $('#previewModal').remove();

    // إضافة المعاينة الجديدة
    $('body').append(previewHtml);
    $('#previewModal').modal('show');
}

// جمع بيانات الموردين
function collectSuppliersData() {
    const suppliers = [];

    $('.supplier-row').each(function() {
        const row = $(this);
        const supplier = {
            supplier_id: row.find('[name="supplier_id"]').val(),
            amount: parseFloat(row.find('[name="amount"]').val()) || 0,
            exchange_rate: parseFloat(row.find('[name="exchange_rate"]').val()) || 1,
            commission: parseFloat(row.find('[name="commission"]').val()) || 0,
            reference: row.find('[name="reference"]').val(),
            notes: row.find('[name="notes"]').val()
        };

        if (supplier.supplier_id && supplier.amount > 0) {
            suppliers.push(supplier);
        }
    });

    return suppliers;
}

// تأكيد التنفيذ المحسن مع الموردين المتعددين
function confirmExecution() {
    if (!currentRequestId) return;

    // التحقق من صحة البيانات
    const reference = $('#executionReference').val().trim();
    if (!reference) {
        showAlert('يرجى إدخال رقم المرجع الرئيسي', 'danger');
        return;
    }

    const suppliers = collectSuppliersData();
    if (suppliers.length === 0) {
        showAlert('يرجى إضافة مورد واحد على الأقل', 'danger');
        return;
    }

    // التحقق من مطابقة المبالغ
    const totalDistribution = suppliers.reduce((sum, s) => sum + s.amount, 0);
    const originalAmount = currentRequestData.amount;

    if (Math.abs(totalDistribution - originalAmount) > 0.01) {
        showAlert(`إجمالي التوزيع (${formatAmount(totalDistribution)}) لا يطابق المبلغ الأصلي (${formatAmount(originalAmount)})`, 'danger');
        return;
    }

    // التحقق من اختيار الموردين
    for (let supplier of suppliers) {
        if (!supplier.supplier_id) {
            showAlert('يرجى اختيار جميع الموردين', 'danger');
            return;
        }
    }

    const executionData = {
        reference: reference,
        execution_date: $('#executionDate').val(),
        execution_method: $('#executionMethod').val(),
        notes: $('#executionNotes').val(),
        suppliers: suppliers
    };

    // تأكيد التنفيذ
    if (!confirm(`هل أنت متأكد من تنفيذ الحوالة رقم ${currentRequestData.request_number}؟\n\nسيتم توزيع المبلغ على ${suppliers.length} مورد(ين).`)) {
        return;
    }

    // إرسال البيانات
    $.ajax({
        url: `/transfers/api/execute-request/${currentRequestId}`,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(executionData),
        success: function(response) {
            if (response.success) {
                showSuccess('تم تنفيذ الحوالة بنجاح');
                $('#executionModal').modal('hide');
                loadApprovedRequests(true); // إعادة تحميل القائمة
            } else {
                showAlert('فشل في تنفيذ الحوالة: ' + response.message, 'danger');
            }
        },
        error: function() {
            showAlert('حدث خطأ أثناء تنفيذ الحوالة', 'danger');
        }
    });
}

// فلترة الطلبات
function filterRequests() {
    const priorityFilter = $('#priorityFilter').val();

    let filteredRequests = approvedRequests;

    if (priorityFilter) {
        filteredRequests = filteredRequests.filter(r => r.priority_level === priorityFilter);
    }

    displayRequests(filteredRequests);
}

// تحديث البيانات
function refreshData() {
    loadApprovedRequests(true); // إجبار إعادة التحميل
}

// دوال مساعدة (تم نقل formatAmount إلى مكان آخر)

function showSuccess(message) {
    alert(message); // مؤقت
}

function showError(message) {
    alert(message); // مؤقت
}

// عرض رسالة نجاح
function showSuccess(message) {
    showAlert(message, 'success');
}

// عرض رسالة خطأ
function showError(message) {
    showAlert(message, 'danger');
}

// عرض تنبيه مخصص
function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('body').append(alertHtml);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        $('.alert').last().fadeOut();
    }, 5000);
}

// دوال التنقل في مسار التنقل
function navigateToHome() {
    window.location.href = '/';
}

function navigateToTransfersDashboard() {
    window.location.href = '/transfers/dashboard';
}

// دوال التصدير والطباعة
function exportExecutionReport() {
    alert('تصدير تقرير Excel - قيد التطوير');
}

function printExecutionList() {
    window.print();
}

// دالة تحديث البيانات
function refreshExecutionData() {
    loadApprovedRequests(true); // إجبار إعادة التحميل
    updateStatistics();
}

// دوال الفلترة المحسنة
function applyFilters() {
    const priority = document.getElementById('priorityFilter').value;
    const status = document.getElementById('statusFilter').value;
    const search = document.getElementById('searchInput').value.toLowerCase();

    // تطبيق الفلاتر على الجدول
    filterRequests();
}

function clearFilters() {
    document.getElementById('priorityFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('searchInput').value = '';
    applyFilters();
}

// ========== دوال التعديل والإلغاء ==========

/**
 * فتح نافذة تعديل الطلب
 */
function editRequest(requestId) {
    // البحث عن الطلب في البيانات
    const request = approvedRequests.find(r => r.id == requestId);
    if (!request) {
        showAlert('لم يتم العثور على الطلب!', 'danger');
        return;
    }

    // ملء البيانات في النموذج
    document.getElementById('editBeneficiaryName').value = request.beneficiary_name || '';
    document.getElementById('editAmount').value = request.amount || '';
    document.getElementById('editPurpose').value = request.purpose || '';
    document.getElementById('editPriority').value = request.priority || 'normal';
    document.getElementById('editNotes').value = '';

    // حفظ معرف الطلب للاستخدام عند الحفظ
    document.getElementById('editRequestForm').setAttribute('data-request-id', requestId);

    // فتح النافذة
    const modal = new bootstrap.Modal(document.getElementById('editRequestModal'));
    modal.show();
}

/**
 * حفظ تعديلات الطلب
 */
async function saveRequestChanges() {
    const form = document.getElementById('editRequestForm');
    const requestId = form.getAttribute('data-request-id');

    // التحقق من صحة البيانات
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // جمع البيانات
    const formData = {
        beneficiary_name: document.getElementById('editBeneficiaryName').value,
        amount: parseFloat(document.getElementById('editAmount').value),
        purpose: document.getElementById('editPurpose').value,
        priority: document.getElementById('editPriority').value,
        edit_notes: document.getElementById('editNotes').value
    };

    try {
        // إرسال طلب التعديل
        const response = await fetch(`/transfers/api/requests/${requestId}/edit`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            showAlert('تم تعديل الطلب بنجاح!', 'success');

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('editRequestModal'));
            modal.hide();

            // تحديث الجدول
            loadApprovedRequests(true);

            // تسجيل العملية في السجل
            logActivity('edit_request', `تم تعديل الطلب رقم ${requestId}`, 'warning');

        } else {
            showAlert(result.message || 'حدث خطأ أثناء تعديل الطلب!', 'danger');
        }

    } catch (error) {
        console.error('خطأ في تعديل الطلب:', error);
        showAlert('حدث خطأ في الاتصال بالخادم!', 'danger');
    }
}

/**
 * فتح نافذة تأكيد إلغاء الطلب
 */
function cancelRequest(requestId) {
    // البحث عن الطلب في البيانات
    const request = approvedRequests.find(r => r.id == requestId);
    if (!request) {
        showAlert('لم يتم العثور على الطلب!', 'danger');
        return;
    }

    // ملء تفاصيل الطلب في النافذة
    document.getElementById('cancelRequestNumber').textContent = request.request_number || requestId;
    document.getElementById('cancelBeneficiaryName').textContent = request.beneficiary_name || '-';
    document.getElementById('cancelAmount').textContent = `${request.amount || 0} ${request.currency || 'SAR'}`;

    // مسح النموذج
    document.getElementById('cancelReason').value = '';
    document.getElementById('cancelNotes').value = '';

    // حفظ معرف الطلب
    document.getElementById('cancelRequestModal').setAttribute('data-request-id', requestId);

    // فتح النافذة
    const modal = new bootstrap.Modal(document.getElementById('cancelRequestModal'));
    modal.show();
}

/**
 * تأكيد إلغاء الطلب
 */
async function confirmCancelRequest() {
    const modal = document.getElementById('cancelRequestModal');
    const requestId = modal.getAttribute('data-request-id');
    const reason = document.getElementById('cancelReason').value;
    const notes = document.getElementById('cancelNotes').value;

    // التحقق من اختيار سبب الإلغاء
    if (!reason) {
        showAlert('يجب اختيار سبب الإلغاء!', 'warning');
        return;
    }

    try {
        // إرسال طلب الإلغاء
        const response = await fetch(`/transfers/api/requests/${requestId}/cancel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                reason: reason,
                notes: notes
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('تم إلغاء الطلب بنجاح!', 'success');

            // إغلاق النافذة
            const modalInstance = bootstrap.Modal.getInstance(modal);
            modalInstance.hide();

            // تحديث الجدول
            loadApprovedRequests(true);
            updateStatistics();

            // تسجيل العملية في السجل
            logActivity('cancel_request', `تم إلغاء الطلب رقم ${requestId} - السبب: ${reason}`, 'danger');

        } else {
            showAlert(result.message || 'حدث خطأ أثناء إلغاء الطلب!', 'danger');
        }

    } catch (error) {
        console.error('خطأ في إلغاء الطلب:', error);
        showAlert('حدث خطأ في الاتصال بالخادم!', 'danger');
    }
}

/**
 * تسجيل النشاط في السجل
 */
function logActivity(action, description, type = 'info') {
    const activity = {
        action: action,
        description: description,
        type: type,
        timestamp: new Date().toISOString(),
        user: 'المستخدم الحالي' // يجب استبدالها بالمستخدم الفعلي
    };

    // إرسال إلى الخادم لحفظ السجل
    fetch('/transfers/api/activity-log', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(activity)
    }).catch(error => {
        console.error('خطأ في تسجيل النشاط:', error);
    });
}

// ========== الإجراءات الإضافية ==========

/**
 * نسخ طلب حوالة
 */
function duplicateRequest(requestId) {
    const request = approvedRequests.find(r => r.id == requestId);
    if (!request) {
        showAlert('لم يتم العثور على الطلب!', 'danger');
        return;
    }

    if (confirm('هل تريد إنشاء نسخة من هذا الطلب؟')) {
        // إنشاء نسخة من الطلب مع بيانات جديدة
        const duplicatedData = {
            beneficiary_name: request.beneficiary_name,
            amount: request.amount,
            purpose: request.purpose,
            bank_name: request.bank_name,
            priority: 'normal', // الأولوية الافتراضية للنسخة
            notes: `نسخة من الطلب رقم ${request.request_number}`
        };

        // فتح نافذة طلب جديد مع البيانات المنسوخة
        window.open(`/transfers/new-request?duplicate_data=${encodeURIComponent(JSON.stringify(duplicatedData))}`, '_blank');
    }
}

/**
 * طباعة تفاصيل الطلب
 */
function printRequest(requestId) {
    const request = approvedRequests.find(r => r.id == requestId);
    if (!request) {
        showAlert('لم يتم العثور على الطلب!', 'danger');
        return;
    }

    // فتح نافذة طباعة مخصصة
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>طلب حوالة رقم ${request.request_number}</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; }
                .content { margin: 20px 0; }
                .row { display: flex; justify-content: space-between; margin: 10px 0; }
                .label { font-weight: bold; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>طلب حوالة مالية</h1>
                <h2>رقم الطلب: ${request.request_number}</h2>
            </div>
            <div class="content">
                <div class="row">
                    <span class="label">اسم المستفيد:</span>
                    <span>${request.beneficiary_name}</span>
                </div>
                <div class="row">
                    <span class="label">المبلغ:</span>
                    <span>${request.amount} ${request.currency}</span>
                </div>
                <div class="row">
                    <span class="label">البنك:</span>
                    <span>${request.bank_name}</span>
                </div>
                <div class="row">
                    <span class="label">الغرض:</span>
                    <span>${request.purpose}</span>
                </div>
                <div class="row">
                    <span class="label">الفرع:</span>
                    <span>${request.branch_name}</span>
                </div>
                <div class="row">
                    <span class="label">تاريخ الاعتماد:</span>
                    <span>${formatDate(request.approved_at)}</span>
                </div>
            </div>
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

/**
 * تغيير أولوية الطلب
 */
async function changeRequestPriority(requestId) {
    const request = approvedRequests.find(r => r.id == requestId);
    if (!request) {
        showAlert('لم يتم العثور على الطلب!', 'danger');
        return;
    }

    const newPriority = prompt(`الأولوية الحالية: ${request.priority_level}\nأدخل الأولوية الجديدة (urgent/high/normal):`, request.priority_level);

    if (newPriority && ['urgent', 'high', 'normal'].includes(newPriority)) {
        try {
            const response = await fetch(`/transfers/api/requests/${requestId}/priority`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ priority: newPriority })
            });

            const result = await response.json();

            if (result.success) {
                showAlert('تم تغيير الأولوية بنجاح!', 'success');
                loadApprovedRequests(true);
                logActivity('change_priority', `تم تغيير أولوية الطلب رقم ${requestId} إلى ${newPriority}`, 'info');
            } else {
                showAlert(result.message || 'حدث خطأ أثناء تغيير الأولوية!', 'danger');
            }
        } catch (error) {
            console.error('خطأ في تغيير الأولوية:', error);
            showAlert('حدث خطأ في الاتصال بالخادم!', 'danger');
        }
    }
}

/**
 * إضافة ملاحظة للطلب
 */
async function addRequestNote(requestId) {
    const note = prompt('أدخل الملاحظة:');

    if (note && note.trim()) {
        try {
            const response = await fetch(`/transfers/api/requests/${requestId}/notes`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ note: note.trim() })
            });

            const result = await response.json();

            if (result.success) {
                showAlert('تم إضافة الملاحظة بنجاح!', 'success');
                logActivity('add_note', `تم إضافة ملاحظة للطلب رقم ${requestId}`, 'info');
            } else {
                showAlert(result.message || 'حدث خطأ أثناء إضافة الملاحظة!', 'danger');
            }
        } catch (error) {
            console.error('خطأ في إضافة الملاحظة:', error);
            showAlert('حدث خطأ في الاتصال بالخادم!', 'danger');
        }
    }
}
</script>

<!-- Voice Search Script -->
<script src="{{ url_for('static', filename='js/voice-search.js') }}"></script>
<script>
    // تهيئة البحث الصوتي
    document.addEventListener('DOMContentLoaded', function() {
        // إنشاء البحث الصوتي مع الزر الموجود
        const searchInput = document.getElementById('searchInput');
        const voiceButton = document.getElementById('voiceSearchBtn');

        if (searchInput && voiceButton) {
            const voiceSearch = new VoiceSearch({
                searchInput: searchInput,
                searchButton: voiceButton,
                onResult: function(text, confidence) {
                    console.log('🎤 نتيجة البحث الصوتي:', text);
                    searchInput.value = text;
                    applyFilters(); // تطبيق البحث فوراً
                },
                onError: function(error) {
                    console.error('خطأ في البحث الصوتي:', error);
                }
            });
        }
    });
</script>

                </tbody>
            </table>
        </div>
    </div>
</div>

</body>
</html>
