
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الميزات المتقدمة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h1>اختبار الميزات المتقدمة الجديدة</h1>
        
        <div class="alert alert-success">
            <h5>🚀 الميزات الجديدة:</h5>
            <ul>
                <li><strong>رموز العملات:</strong> ¥ بدلاً من CNY</li>
                <li><strong>إحصائيات متعددة العملات:</strong> عرض تفصيلي لكل عملة</li>
                <li><strong>البحث الصوتي:</strong> بحث بالصوت باللغة العربية</li>
            </ul>
        </div>
        
        <!-- اختبار رموز العملات -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>💰 اختبار رموز العملات</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label>كود العملة:</label>
                            <select id="currencyCode" class="form-select">
                                <option value="CNY">CNY - يوان صيني</option>
                                <option value="USD">USD - دولار أمريكي</option>
                                <option value="SAR">SAR - ريال سعودي</option>
                                <option value="EUR">EUR - يورو</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label>المبلغ:</label>
                            <input type="number" id="amount" class="form-control" value="345000">
                        </div>
                        <button class="btn btn-primary" onclick="testCurrencySymbol()">اختبار التحويل</button>
                        <div id="currencyResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>🎤 اختبار البحث الصوتي</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label>نتيجة البحث الصوتي:</label>
                            <div class="input-group">
                                <input type="text" id="voiceResult" class="form-control" placeholder="اضغط على المايكروفون وتحدث...">
                                <button class="btn btn-outline-secondary" type="button" id="testVoiceBtn" onclick="testVoiceSearch()">
                                    <i class="fas fa-microphone" id="testVoiceIcon"></i>
                                </button>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <small>
                                <i class="fas fa-info-circle"></i>
                                قل شيئاً مثل "حلوى" أو "علكة" أو "شركة رايسن"
                            </small>
                        </div>
                        <div id="voiceStatus" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- إحصائيات العملات -->
        <div class="mt-4">
            <div class="card">
                <div class="card-header">
                    <h5>📊 إحصائيات العملات</h5>
                </div>
                <div class="card-body">
                    <div class="row">
        
                        <div class="col-md-4 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-primary">¥2,011,526</h4>
                                    <p class="mb-0">CNY</p>
                                    <small class="text-muted">8 أمر</small>
                                </div>
                            </div>
                        </div>
                
                    </div>
                </div>
            </div>
        </div>
        
        <!-- جدول الأصناف مع رموز العملات -->
        <div class="mt-4">
            <div class="card">
                <div class="card-header">
                    <h5>📋 الأصناف مع رموز العملات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>اسم الصنف</th>
                                    <th>سعر الوحدة</th>
                                    <th>إجمالي القيمة</th>
                                    <th>العملة</th>
                                </tr>
                            </thead>
                            <tbody>
        
                                <tr>
                                    <td><strong>حلوى سي سي عملاق جديد 12×30×22جم</strong></td>
                                    <td>¥115.00</td>
                                    <td><strong>¥345,000.00</strong></td>
                                    <td><span class="badge bg-info">CNY</span></td>
                                </tr>
                
                                <tr>
                                    <td><strong>علكة ثلاجة العائلة 6*380*2جم</strong></td>
                                    <td>¥162.00</td>
                                    <td><strong>¥341,982.00</strong></td>
                                    <td><span class="badge bg-info">CNY</span></td>
                                </tr>
                
                                <tr>
                                    <td><strong>علكة بوكر 12×200×3جم</strong></td>
                                    <td>¥148.00</td>
                                    <td><strong>¥305,176.00</strong></td>
                                    <td><span class="badge bg-info">CNY</span></td>
                                </tr>
                
                                <tr>
                                    <td><strong>حلوى بودرة حامض سوبر 12*100*11جم</strong></td>
                                    <td>¥264.00</td>
                                    <td><strong>¥258,720.00</strong></td>
                                    <td><span class="badge bg-info">CNY</span></td>
                                </tr>
                
                                <tr>
                                    <td><strong>حلوى كرسبي 12×200×3جم</strong></td>
                                    <td>¥123.00</td>
                                    <td><strong>¥250,920.00</strong></td>
                                    <td><span class="badge bg-info">CNY</span></td>
                                </tr>
                
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <button class="btn btn-success" onclick="testRealSystem()">اختبار النظام الحقيقي</button>
            <div id="systemResult" class="mt-3"></div>
        </div>
    </div>

    <script>
    // خريطة العملات
    const currencySymbols = {
        'CNY': '¥',
        'USD': '$',
        'EUR': '€',
        'SAR': '﷼',
        'AED': 'د.إ'
    };
    
    // متغيرات البحث الصوتي
    let recognition;
    let isListening = false;
    
    // تهيئة البحث الصوتي
    $(document).ready(function() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            recognition = new SpeechRecognition();
            
            recognition.lang = 'ar-SA';
            recognition.continuous = false;
            recognition.interimResults = false;
            
            recognition.onstart = function() {
                isListening = true;
                $('#testVoiceIcon').removeClass('fa-microphone').addClass('fa-microphone-slash text-danger');
                $('#testVoiceBtn').removeClass('btn-outline-secondary').addClass('btn-danger');
                $('#voiceStatus').html('<div class="alert alert-info">🎤 جاري الاستماع...</div>');
            };
            
            recognition.onresult = function(event) {
                const transcript = event.results[0][0].transcript;
                $('#voiceResult').val(transcript);
                $('#voiceStatus').html(`<div class="alert alert-success">✅ تم التعرف على: "${transcript}"</div>`);
            };
            
            recognition.onerror = function(event) {
                $('#voiceStatus').html(`<div class="alert alert-danger">❌ خطأ: ${event.error}</div>`);
                stopVoiceSearch();
            };
            
            recognition.onend = function() {
                stopVoiceSearch();
            };
        } else {
            $('#testVoiceBtn').prop('disabled', true);
            $('#voiceStatus').html('<div class="alert alert-warning">❌ البحث الصوتي غير مدعوم في هذا المتصفح</div>');
        }
    });
    
    function testCurrencySymbol() {
        const code = $('#currencyCode').val();
        const amount = parseFloat($('#amount').val());
        const symbol = currencySymbols[code] || code;
        
        $('#currencyResult').html(`
            <div class="alert alert-success">
                <strong>النتيجة:</strong><br>
                ${code} ${amount.toLocaleString()} → <strong>${symbol}${amount.toLocaleString()}</strong>
            </div>
        `);
    }
    
    function testVoiceSearch() {
        if (!recognition) {
            $('#voiceStatus').html('<div class="alert alert-danger">❌ البحث الصوتي غير مدعوم</div>');
            return;
        }
        
        if (isListening) {
            recognition.stop();
            return;
        }
        
        try {
            recognition.start();
        } catch (error) {
            $('#voiceStatus').html(`<div class="alert alert-danger">❌ خطأ: ${error}</div>`);
        }
    }
    
    function stopVoiceSearch() {
        isListening = false;
        $('#testVoiceIcon').removeClass('fa-microphone-slash text-danger').addClass('fa-microphone');
        $('#testVoiceBtn').removeClass('btn-danger').addClass('btn-outline-secondary');
    }
    
    function testRealSystem() {
        $('#systemResult').html('<div class="text-center"><div class="spinner-border"></div><br>جاري اختبار النظام...</div>');
        
        fetch('/purchase-orders/api/items/data')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.length > 0) {
                    const sampleItem = data.data[0];
                    const symbol = currencySymbols[sampleItem.currency] || sampleItem.currency;
                    
                    $('#systemResult').html(`
                        <div class="alert alert-success">
                            <h5>✅ النظام يعمل مع رموز العملات!</h5>
                            <p><strong>عينة:</strong> ${sampleItem.item_name}</p>
                            <p><strong>السعر:</strong> ${symbol}${sampleItem.avg_price}</p>
                            <p><strong>القيمة:</strong> ${symbol}${sampleItem.total_value.toLocaleString()}</p>
                        </div>
                    `);
                } else {
                    $('#systemResult').html('<div class="alert alert-danger">❌ فشل في جلب البيانات</div>');
                }
            })
            .catch(error => {
                $('#systemResult').html(`<div class="alert alert-danger">❌ خطأ: ${error}</div>`);
            });
    }
    </script>
</body>
</html>
        