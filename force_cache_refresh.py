#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إجبار تحديث cache المتصفح
"""

import time

def force_cache_refresh():
    """إجبار تحديث cache المتصفح"""
    print('🔄 إجبار تحديث cache المتصفح...')
    
    print('📋 التحديثات المطبقة:')
    print('✅ إضافة console.log للتتبع')
    print('✅ إضافة timestamp لـ fetch request')
    print('✅ إضافة رسالة تأكيد تحديث JavaScript')
    print('✅ تحسين معالجة الأخطاء')
    
    print('\n💡 كيفية التأكد من التحديث:')
    print('1. افتح صفحة الشحنات: http://127.0.0.1:5000/shipments/dashboard')
    print('2. اضغط F12 لفتح Developer Tools')
    print('3. انتقل إلى تبويب Console')
    print('4. ابحث عن الرسالة: "✅ تم تحميل JavaScript المحدث - إصدار 2024-01-16"')
    print('5. انقر على خلية حالة شحنة')
    print('6. راقب الرسائل في Console:')
    print('   - "🔧 فتح نافذة تعديل الحالة..."')
    print('   - "🔄 تحميل الحالات المتاحة من قاعدة البيانات..."')
    print('   - "📡 استجابة الخادم: 200"')
    print('   - "✅ تم جلب X حالة من قاعدة البيانات"')
    
    print('\n🔧 إذا لم تظهر الرسائل:')
    print('1. اضغط Ctrl+F5 لإعادة تحميل قسري')
    print('2. أو اضغط Ctrl+Shift+R')
    print('3. أو امسح cache المتصفح من Settings')
    
    print('\n📊 الحالات المتوقعة في النافذة المنبثقة:')
    expected_statuses = [
        'مسودة',
        'مؤكدة', 
        'قيد الشحن',
        'وصلت للميناء',
        'قيد التخليص',
        'جاهزة للاستلام',
        'تم التسليم',
        'ملغية',
        'متأخرة',
        'معادة'
    ]
    
    for i, status in enumerate(expected_statuses, 1):
        print(f'   {i:2d}. {status}')
    
    print('\n⚠️ ملاحظة مهمة:')
    print('إذا كانت الحالات لا تزال غير صحيحة، فهذا يعني أن:')
    print('1. المتصفح يستخدم cache قديم')
    print('2. أو أن هناك مشكلة في endpoint')
    print('3. أو أن قاعدة البيانات تحتوي على حالات مختلفة')

def check_database_statuses():
    """فحص الحالات في قاعدة البيانات"""
    print('\n🔍 فحص الحالات في قاعدة البيانات...')
    
    try:
        import sys
        sys.path.append('.')
        from oracle_manager import OracleManager
        
        oracle = OracleManager()
        
        statuses_query = """
            SELECT status_code, status_name_ar, is_active
            FROM shipment_status_config
            ORDER BY status_order, status_code
        """
        
        statuses = oracle.execute_query(statuses_query)
        
        print('📋 الحالات في قاعدة البيانات:')
        active_count = 0
        
        for status in statuses:
            code, name_ar, is_active = status
            active_icon = '✅' if is_active == 1 else '❌'
            print(f'   {active_icon} {code} → {name_ar}')
            
            if is_active == 1:
                active_count += 1
        
        print(f'\n📊 إجمالي الحالات النشطة: {active_count}')
        
        oracle.close()
        
    except Exception as e:
        print(f'❌ خطأ في فحص قاعدة البيانات: {e}')

if __name__ == '__main__':
    force_cache_refresh()
    check_database_statuses()
    
    print('\n' + '='*50)
    print('🎯 الخطوات التالية:')
    print('1. افتح المتصفح واضغط Ctrl+F5')
    print('2. افتح Developer Tools (F12)')
    print('3. انقر على خلية حالة شحنة')
    print('4. راقب Console للتأكد من التحديث')
    print('5. تحقق من الحالات في النافذة المنبثقة')
