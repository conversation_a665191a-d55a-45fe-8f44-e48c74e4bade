#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحسين نظام ترجمة حالات الشحنات
"""

import sys
sys.path.append('.')
from oracle_manager import OracleManager

def create_status_translation_function():
    """إنشاء دالة لترجمة حالات الشحنات من قاعدة البيانات"""
    print('🔧 إنشاء دالة ترجمة حالات الشحنات...')

    try:
        oracle = OracleManager()
        
        # إنشاء دالة لترجمة حالة الشحنة
        translation_function_sql = """
        CREATE OR REPLACE FUNCTION get_shipment_status_translation(p_status_code VARCHAR2)
        RETURN VARCHAR2
        IS
            v_status_name_ar VARCHAR2(100);
        BEGIN
            SELECT status_name_ar INTO v_status_name_ar
            FROM shipment_status_config
            WHERE status_code = p_status_code
            AND is_active = 1;
            
            RETURN v_status_name_ar;
            
        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                RETURN p_status_code;
            WHEN OTHERS THEN
                RETURN p_status_code;
        END;
        """
        
        oracle.execute_update(translation_function_sql)
        print('✅ تم إنشاء دالة ترجمة حالات الشحنات')
        
        # إنشاء view محسن للشحنات مع الترجمة
        enhanced_view_sql = """
        CREATE OR REPLACE VIEW v_shipments_enhanced AS
        SELECT 
            cs.*,
            ssc.status_name_ar as shipment_status_display,
            ssc.status_name_en,
            ssc.status_color,
            ssc.status_icon,
            po.PO_NUMBER,
            po.STATUS as po_status
        FROM cargo_shipments cs
        LEFT JOIN shipment_status_config ssc ON cs.shipment_status = ssc.status_code
        LEFT JOIN PURCHASE_ORDERS po ON cs.purchase_order_id = po.ID
        """
        
        oracle.execute_update(enhanced_view_sql)
        print('✅ تم إنشاء View محسن للشحنات')
        
        oracle.close()
        return True
        
    except Exception as e:
        print(f'❌ خطأ في إنشاء الدالة: {e}')
        return False

def test_translation_function():
    """اختبار دالة الترجمة"""
    print('\n🧪 اختبار دالة الترجمة...')
    
    try:
        oracle = OracleManager()
        
        # اختبار الدالة على حالات مختلفة
        test_statuses = ['arrived_port', 'delivered', 'in_transit', 'draft', 'unknown_status']
        
        for status in test_statuses:
            try:
                result = oracle.execute_query(f"SELECT get_shipment_status_translation('{status}') FROM dual")
                if result:
                    translation = result[0][0]
                    print(f'   📝 {status} → {translation}')
            except Exception as e:
                print(f'   ❌ خطأ في ترجمة {status}: {e}')
        
        # اختبار View المحسن
        print('\n📋 اختبار View المحسن:')
        view_test = """
            SELECT 
                tracking_number,
                shipment_status,
                shipment_status_display,
                status_color,
                PO_NUMBER,
                po_status
            FROM v_shipments_enhanced
            ORDER BY id DESC
        """
        
        results = oracle.execute_query(view_test)
        for result in results:
            tracking, status_code, status_display, color, po_number, po_status = result
            print(f'   📦 {tracking}: {status_code} → {status_display} [{color}] | {po_number}: {po_status}')
        
        oracle.close()
        
    except Exception as e:
        print(f'❌ خطأ في الاختبار: {e}')

def update_routes_to_use_database():
    """تحديث routes.py لاستخدام قاعدة البيانات للترجمة"""
    print('\n💡 اقتراح تحسين routes.py:')
    
    print("""
    يمكن تحسين routes.py باستخدام الاستعلام التالي بدلاً من الترجمة اليدوية:
    
    # بدلاً من الترجمة اليدوية:
    shipment_status_display = {
        'draft': 'مسودة',
        'confirmed': 'مؤكد',
        ...
    }.get(shipment_status, shipment_status or 'غير محدد')
    
    # استخدم الاستعلام:
    SELECT 
        cs.*,
        ssc.status_name_ar as shipment_status_display,
        ssc.status_color,
        ssc.status_icon
    FROM cargo_shipments cs
    LEFT JOIN shipment_status_config ssc ON cs.shipment_status = ssc.status_code
    
    هذا سيضمن:
    ✅ ترجمة تلقائية لجميع الحالات
    ✅ عدم الحاجة لتحديث الكود عند إضافة حالات جديدة
    ✅ اتساق الترجمات في جميع أنحاء النظام
    ✅ إمكانية تخصيص الألوان والأيقونات من قاعدة البيانات
    """)

def verify_all_translations():
    """التحقق من جميع الترجمات"""
    print('\n🔍 التحقق من جميع ترجمات الحالات...')
    
    try:
        oracle = OracleManager()
        
        # فحص جميع الحالات في قاعدة البيانات
        all_statuses_query = """
            SELECT 
                status_code,
                status_name_ar,
                status_name_en,
                status_color,
                is_active
            FROM shipment_status_config
            ORDER BY status_code
        """
        
        statuses = oracle.execute_query(all_statuses_query)
        
        print('📋 جميع حالات الشحنات المتاحة:')
        for status in statuses:
            code, name_ar, name_en, color, is_active = status
            active_icon = '✅' if is_active == 1 else '❌'
            print(f'   {active_icon} {code} → {name_ar} ({name_en}) [{color}]')
        
        # فحص الحالات المستخدمة فعلياً
        used_statuses_query = """
            SELECT DISTINCT shipment_status, COUNT(*) as count
            FROM cargo_shipments
            GROUP BY shipment_status
            ORDER BY count DESC
        """
        
        used_statuses = oracle.execute_query(used_statuses_query)
        
        print(f'\n📊 الحالات المستخدمة فعلياً:')
        for status in used_statuses:
            status_code, count = status
            
            # التحقق من وجود ترجمة
            translation_query = f"SELECT status_name_ar FROM shipment_status_config WHERE status_code = '{status_code}'"
            translation = oracle.execute_query(translation_query)
            
            if translation:
                status_name = translation[0][0]
                print(f'   ✅ {status_code} → {status_name} ({count} شحنة)')
            else:
                print(f'   ❌ {status_code} → غير مترجم ({count} شحنة)')
        
        oracle.close()
        
    except Exception as e:
        print(f'❌ خطأ في التحقق: {e}')

if __name__ == '__main__':
    # إنشاء دالة الترجمة
    create_status_translation_function()
    
    print('\n' + '='*50)
    # اختبار دالة الترجمة
    test_translation_function()
    
    print('\n' + '='*50)
    # اقتراح تحسين routes.py
    update_routes_to_use_database()
    
    print('\n' + '='*50)
    # التحقق من جميع الترجمات
    verify_all_translations()
