"""
لوحة تحكم أرصدة المخزون
Inventory Balance Dashboard
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import json

# استيراد الوحدات المحلية
try:
    from database_connector import db_connector
    from inventory_balance_analyzer import InventoryBalanceAnalyzer
except ImportError as e:
    st.error(f"خطأ في استيراد الوحدات: {str(e)}")
    st.stop()

# إعداد الصفحة
st.set_page_config(
    page_title="🏪 نظام أرصدة المخزون",
    page_icon="🏪",
    layout="wide",
    initial_sidebar_state="expanded"
)

# إعداد CSS مخصص
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #2E8B57;
        text-align: center;
        margin-bottom: 2rem;
        font-weight: bold;
    }
    .metric-card {
        background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
        color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        text-align: center;
    }
    .warning-card {
        background: linear-gradient(135deg, #FF6B6B 0%, #FF4757 100%);
        color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        text-align: center;
    }
    .info-card {
        background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
        color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        text-align: center;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """الدالة الرئيسية للتطبيق"""
    
    # العنوان الرئيسي
    st.markdown('<h1 class="main-header">🏪 نظام تحليل أرصدة المخزون</h1>', unsafe_allow_html=True)
    st.markdown('<p style="text-align: center; color: #666;">تحليل شامل لأرصدة الأصناف من جدول ITEM_MOVEMENT</p>', unsafe_allow_html=True)
    
    # الشريط الجانبي
    with st.sidebar:
        st.header("⚙️ إعدادات التحليل")
        
        # معلومات الاتصال
        st.subheader("🔗 معلومات قاعدة البيانات")
        st.info(f"""
        **قاعدة البيانات:** ORCL  
        **المستخدم:** IAS20251  
        **الجدول:** ITEM_MOVEMENT
        """)
        
        # خيارات التحليل
        st.subheader("📊 خيارات التحليل")
        
        # فلتر التاريخ
        use_date_filter = st.checkbox("تطبيق فلتر التاريخ")
        date_from = None
        date_to = None
        
        if use_date_filter:
            col1, col2 = st.columns(2)
            with col1:
                date_from = st.date_input("من تاريخ", value=datetime.now() - timedelta(days=30))
            with col2:
                date_to = st.date_input("إلى تاريخ", value=datetime.now())
        
        # حد البيانات
        data_limit = st.number_input(
            "حد البيانات (0 = جميع البيانات):",
            min_value=0,
            max_value=100000,
            value=0,
            step=1000
        )
        
        # زر بدء التحليل
        start_analysis = st.button("🚀 تحليل الأرصدة", type="primary")
    
    # المحتوى الرئيسي
    if start_analysis:
        run_inventory_analysis(data_limit, date_from, date_to)
    else:
        show_welcome_screen()

def show_welcome_screen():
    """عرض شاشة الترحيب"""
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown("""
        ### 🎯 مرحباً بك في نظام تحليل أرصدة المخزون
        
        هذا النظام مصمم خصيصاً لتحليل أرصدة الأصناف من جدول **ITEM_MOVEMENT** وتقديم تقارير شاملة عن حالة المخزون.
        
        #### 🔍 ما يمكن للنظام فعله:
        
        - **📊 حساب الأرصدة الحالية** لجميع الأصناف
        - **📈 تتبع حركة المخزون** الواردة والصادرة
        - **💰 حساب قيمة المخزون** بالتكلفة المرجحة
        - **⚠️ اكتشاف الأرصدة السالبة** والأصناف المنتهية
        - **🏪 تحليل المخزون حسب المستودع**
        - **📋 تصدير التقارير** إلى Excel
        
        #### 🚀 للبدء:
        1. اختر فلتر التاريخ (اختياري)
        2. حدد حد البيانات المطلوب تحليلها
        3. اضغط على زر "تحليل الأرصدة"
        """)
        
        # إحصائيات سريعة
        st.markdown("---")
        st.subheader("📈 مميزات النظام")
        
        col_a, col_b, col_c = st.columns(3)
        
        with col_a:
            st.metric("🎯 دقة الحساب", "100%", help="حساب دقيق للأرصدة")
        
        with col_b:
            st.metric("⚡ سرعة المعالجة", "عالية", help="معالجة سريعة للبيانات")
        
        with col_c:
            st.metric("📊 شمولية التقارير", "كاملة", help="تقارير شاملة ومفصلة")

def run_inventory_analysis(data_limit, date_from, date_to):
    """تشغيل تحليل الأرصدة"""
    
    # شريط التقدم
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    try:
        # الخطوة 1: الاتصال بقاعدة البيانات
        status_text.text("🔗 جاري الاتصال بقاعدة البيانات...")
        progress_bar.progress(10)
        
        if not db_connector.connect():
            st.error("❌ فشل في الاتصال بقاعدة البيانات")
            return
        
        # الخطوة 2: تحميل بيانات الحركة
        status_text.text("📥 جاري تحميل بيانات حركة المخزون...")
        progress_bar.progress(30)
        
        analyzer = InventoryBalanceAnalyzer(db_connector)
        
        # تحويل التواريخ إلى نص إذا كانت موجودة
        date_from_str = date_from.strftime('%Y-%m-%d') if date_from else None
        date_to_str = date_to.strftime('%Y-%m-%d') if date_to else None
        
        success = analyzer.load_movement_data(
            limit=data_limit if data_limit > 0 else None,
            date_from=date_from_str,
            date_to=date_to_str
        )
        
        if not success:
            st.error("❌ فشل في تحميل بيانات الحركة")
            return
        
        # الخطوة 3: حساب الأرصدة
        status_text.text("🧮 جاري حساب أرصدة الأصناف...")
        progress_bar.progress(60)
        
        if not analyzer.calculate_balances():
            st.error("❌ فشل في حساب الأرصدة")
            return
        
        # الخطوة 4: تحليل حالة المخزون
        status_text.text("📊 جاري تحليل حالة المخزون...")
        progress_bar.progress(80)
        
        analysis_results = analyzer.analyze_inventory_status()
        
        # الخطوة 5: عرض النتائج
        status_text.text("✅ تم الانتهاء من التحليل!")
        progress_bar.progress(100)
        
        # عرض النتائج
        display_inventory_results(analyzer, analysis_results)
        
    except Exception as e:
        st.error(f"❌ حدث خطأ أثناء التحليل: {str(e)}")
    
    finally:
        # تنظيف الموارد
        db_connector.disconnect()
        status_text.empty()
        progress_bar.empty()

def display_inventory_results(analyzer, analysis_results):
    """عرض نتائج تحليل المخزون"""
    
    st.success("✅ تم إكمال تحليل الأرصدة بنجاح!")
    
    if not analysis_results or 'summary' not in analysis_results:
        st.warning("⚠️ لا توجد نتائج تحليل للعرض")
        return
    
    summary = analysis_results['summary']
    
    # معلومات أساسية عن المخزون
    st.header("📊 ملخص المخزون العام")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown(f"""
        <div class="metric-card">
            <h3>{summary['total_items']:,}</h3>
            <p>إجمالي الأصناف</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown(f"""
        <div class="info-card">
            <h3>{summary['items_with_stock']:,}</h3>
            <p>أصناف متوفرة</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown(f"""
        <div class="warning-card">
            <h3>{summary['items_out_of_stock']:,}</h3>
            <p>أصناف منتهية</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        st.markdown(f"""
        <div class="warning-card">
            <h3>{summary['items_negative_stock']:,}</h3>
            <p>أرصدة سالبة</p>
        </div>
        """, unsafe_allow_html=True)
    
    # قيمة المخزون
    st.header("💰 قيمة المخزون")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.metric(
            "إجمالي قيمة المخزون",
            f"{summary['total_inventory_value']:,.2f}",
            help="القيمة الإجمالية لجميع الأصناف المتوفرة"
        )
    
    with col2:
        st.metric(
            "متوسط قيمة الصنف",
            f"{summary['average_item_value']:,.2f}",
            help="متوسط قيمة الصنف الواحد"
        )
    
    # عرض الأرصدة الحالية
    st.header("📋 الأرصدة الحالية")
    
    if analyzer.balance_data is not None and not analyzer.balance_data.empty:
        # فلتر البيانات
        col1, col2 = st.columns(2)
        
        with col1:
            show_filter = st.selectbox(
                "عرض:",
                ["جميع الأصناف", "الأصناف المتوفرة", "الأصناف المنتهية", "الأرصدة السالبة"]
            )
        
        with col2:
            search_item = st.text_input("البحث عن صنف:", placeholder="أدخل كود أو اسم الصنف")
        
        # تطبيق الفلاتر
        filtered_data = analyzer.balance_data.copy()
        
        if show_filter == "الأصناف المتوفرة":
            filtered_data = filtered_data[filtered_data['running_balance'] > 0]
        elif show_filter == "الأصناف المنتهية":
            filtered_data = filtered_data[filtered_data['running_balance'] == 0]
        elif show_filter == "الأرصدة السالبة":
            filtered_data = filtered_data[filtered_data['running_balance'] < 0]
        
        if search_item:
            filtered_data = filtered_data[
                filtered_data['i_code'].str.contains(search_item, case=False, na=False) |
                filtered_data['i_desc'].str.contains(search_item, case=False, na=False)
            ]
        
        # عرض الجدول
        if not filtered_data.empty:
            # تنسيق الأعمدة للعرض
            display_columns = [
                'i_code', 'i_desc', 'running_balance', 'unit_cost', 
                'total_value', 'movement_count', 'last_movement'
            ]
            
            # إعادة تسمية الأعمدة
            column_names = {
                'i_code': 'كود الصنف',
                'i_desc': 'اسم الصنف',
                'running_balance': 'الرصيد الحالي',
                'unit_cost': 'تكلفة الوحدة',
                'total_value': 'إجمالي القيمة',
                'movement_count': 'عدد الحركات',
                'last_movement': 'آخر حركة'
            }
            
            display_data = filtered_data[display_columns].rename(columns=column_names)
            
            # تنسيق الأرقام
            display_data['الرصيد الحالي'] = display_data['الرصيد الحالي'].round(2)
            display_data['تكلفة الوحدة'] = display_data['تكلفة الوحدة'].round(2)
            display_data['إجمالي القيمة'] = display_data['إجمالي القيمة'].round(2)
            
            st.dataframe(
                display_data,
                use_container_width=True,
                height=400
            )
            
            # إحصائيات الفلتر الحالي
            st.info(f"📊 عدد الأصناف المعروضة: {len(display_data):,}")
            
        else:
            st.warning("⚠️ لا توجد أصناف تطابق المعايير المحددة")

if __name__ == "__main__":
    main()
