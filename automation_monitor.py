#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقب الأتمتة التلقائية
Automation Monitor Service

هذه الخدمة تراقب نظام الأتمتة وتضمن عمله باستمرار
"""

import sys
import os
import time
import threading
import logging
from datetime import datetime, timedelta

# إضافة مسار التطبيق
sys.path.append('.')
sys.path.append('app')

def setup_logging():
    """إعداد نظام السجلات"""
    log_dir = os.path.join(os.path.dirname(__file__), 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'automation_monitor.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger('AutomationMonitor')

class AutomationMonitor:
    """مراقب الأتمتة التلقائية"""
    
    def __init__(self):
        self.logger = setup_logging()
        self.running = False
        self.check_interval = 60  # فحص كل دقيقة
        self.last_queue_check = None
        self.last_service_restart = None
        self.restart_cooldown = 300  # 5 دقائق بين إعادة التشغيل
        
    def start_monitoring(self):
        """بدء المراقبة"""
        self.running = True
        self.logger.info("🔍 بدء مراقبة نظام الأتمتة التلقائية")
        
        while self.running:
            try:
                self.check_automation_health()
                time.sleep(self.check_interval)
            except Exception as e:
                self.logger.error(f"❌ خطأ في مراقبة الأتمتة: {e}")
                time.sleep(60)
    
    def check_automation_health(self):
        """فحص صحة نظام الأتمتة"""
        try:
            # فحص حالة الخدمات
            flask_status = self.check_flask_automation()
            professional_status = self.check_professional_automation()
            
            # فحص طابور الأتمتة
            queue_status = self.check_automation_queue()
            
            # تحديد إذا كانت هناك حاجة لإعادة تشغيل
            needs_restart = False
            
            if not flask_status['running'] and not professional_status['running']:
                self.logger.warning("⚠️ جميع خدمات الأتمتة متوقفة")
                needs_restart = True
            
            if queue_status['pending_count'] > 0:
                self.logger.warning(f"⚠️ يوجد {queue_status['pending_count']} عنصر معلق في طابور الأتمتة")
                
                # إذا كان هناك عناصر معلقة لأكثر من 10 دقائق
                if queue_status['oldest_pending']:
                    age_minutes = (datetime.now() - queue_status['oldest_pending']).total_seconds() / 60
                    if age_minutes > 10:
                        self.logger.warning(f"⚠️ أقدم عنصر معلق منذ {age_minutes:.1f} دقيقة")
                        needs_restart = True
            
            # إعادة تشغيل الخدمات إذا لزم الأمر
            if needs_restart:
                self.restart_automation_services()
            else:
                self.logger.info("✅ نظام الأتمتة يعمل بشكل طبيعي")
                
        except Exception as e:
            self.logger.error(f"❌ خطأ في فحص صحة الأتمتة: {e}")
    
    def check_flask_automation(self):
        """فحص خدمة الأتمتة المدمجة"""
        try:
            from app.services.flask_automation_service import flask_automation_service
            status = flask_automation_service.get_service_status()
            return status
        except Exception as e:
            self.logger.error(f"❌ خطأ في فحص الخدمة المدمجة: {e}")
            return {'running': False, 'thread_alive': False}
    
    def check_professional_automation(self):
        """فحص خدمة الأتمتة الاحترافية"""
        try:
            from app.services.professional_automation_service import get_automation_status
            status = get_automation_status()
            return status
        except Exception as e:
            self.logger.error(f"❌ خطأ في فحص الخدمة الاحترافية: {e}")
            return {'running': False, 'thread_alive': False}
    
    def check_automation_queue(self):
        """فحص طابور الأتمتة"""
        try:
            from database_manager import DatabaseManager
            db_manager = DatabaseManager()
            
            # فحص العناصر المعلقة
            queue_query = """
                SELECT COUNT(*) as pending_count,
                       MIN(created_at) as oldest_pending
                FROM automation_queue
                WHERE processed = 0
            """
            
            result = db_manager.execute_query(queue_query)
            pending_count = result[0][0] if result else 0
            oldest_pending = result[0][1] if result and result[0][1] else None
            
            db_manager.close()
            
            return {
                'pending_count': pending_count,
                'oldest_pending': oldest_pending
            }
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في فحص طابور الأتمتة: {e}")
            return {'pending_count': 0, 'oldest_pending': None}
    
    def restart_automation_services(self):
        """إعادة تشغيل خدمات الأتمتة"""
        try:
            # فحص cooldown
            if self.last_service_restart:
                time_since_restart = (datetime.now() - self.last_service_restart).total_seconds()
                if time_since_restart < self.restart_cooldown:
                    remaining = self.restart_cooldown - time_since_restart
                    self.logger.info(f"⏳ انتظار {remaining:.0f} ثانية قبل إعادة التشغيل")
                    return False
            
            self.logger.info("🔄 إعادة تشغيل خدمات الأتمتة...")
            
            # إيقاف الخدمات الحالية
            try:
                from app.services.flask_automation_service import flask_automation_service
                if flask_automation_service.running:
                    flask_automation_service.stop_service()
                    self.logger.info("🛑 تم إيقاف الخدمة المدمجة")
            except Exception as e:
                self.logger.warning(f"⚠️ خطأ في إيقاف الخدمة المدمجة: {e}")
            
            # انتظار قصير
            time.sleep(2)
            
            # إعادة تعيين العناصر الفاشلة
            self.reset_failed_queue_items()
            
            # بدء الخدمة المدمجة
            try:
                from app.services.flask_automation_service import flask_automation_service
                
                # تحديث الإعدادات
                enhanced_settings = {
                    'auto_create_orders': True,
                    'auto_send_notifications': True,
                    'check_interval_minutes': 2,
                    'working_hours_start': '00:00',
                    'working_hours_end': '23:59',
                    'weekend_enabled': True,
                    'max_orders_per_batch': 10,
                    'retry_failed_orders': True,
                    'retry_attempts': 5,
                    'always_enabled_for_testing': True
                }
                
                flask_automation_service.update_settings(enhanced_settings)
                
                success = flask_automation_service.start_service()
                if success:
                    self.logger.info("✅ تم إعادة تشغيل الخدمة المدمجة بنجاح")
                    self.last_service_restart = datetime.now()
                    return True
                else:
                    self.logger.error("❌ فشل في إعادة تشغيل الخدمة المدمجة")
                    
            except Exception as e:
                self.logger.error(f"❌ خطأ في إعادة تشغيل الخدمة المدمجة: {e}")
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في إعادة تشغيل خدمات الأتمتة: {e}")
            return False
    
    def reset_failed_queue_items(self):
        """إعادة تعيين العناصر الفاشلة في طابور الأتمتة"""
        try:
            from database_manager import DatabaseManager
            db_manager = DatabaseManager()
            
            reset_query = """
                UPDATE automation_queue 
                SET processing_attempts = 0,
                    processed = 0,
                    processed_at = NULL
                WHERE processing_attempts >= 3 
                AND processed = 0
            """
            
            result = db_manager.execute_update(reset_query)
            if result > 0:
                self.logger.info(f"🔄 تم إعادة تعيين {result} عنصر فاشل في طابور الأتمتة")
            
            db_manager.close()
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في إعادة تعيين طابور الأتمتة: {e}")
    
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        self.running = False
        self.logger.info("🛑 تم إيقاف مراقبة الأتمتة")

def main():
    """الدالة الرئيسية"""
    monitor = AutomationMonitor()
    
    try:
        monitor.start_monitoring()
    except KeyboardInterrupt:
        monitor.logger.info("🛑 تم إيقاف المراقبة بواسطة المستخدم")
        monitor.stop_monitoring()
    except Exception as e:
        monitor.logger.error(f"❌ خطأ في مراقب الأتمتة: {e}")

if __name__ == "__main__":
    main()
