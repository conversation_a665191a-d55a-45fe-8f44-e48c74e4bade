"""
عارض بسيط لنتائج التحليل الذكي
Simple Analysis Results Viewer
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from datetime import datetime
import os
import glob

# إعداد الخطوط العربية
plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_latest_results():
    """تحميل أحدث نتائج التحليل"""
    
    # البحث عن أحدث ملف نتائج
    result_files = glob.glob("analysis_results_*.json")
    
    if not result_files:
        print("❌ لا توجد ملفات نتائج")
        return None, None, None
    
    # ترتيب الملفات حسب التاريخ
    result_files.sort(reverse=True)
    latest_file = result_files[0]
    
    print(f"📂 تحميل النتائج من: {latest_file}")
    
    # تحميل النتائج
    with open(latest_file, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    # تحميل عينة البيانات
    data_files = glob.glob("data_sample_*.csv")
    data_files.sort(reverse=True)
    
    if data_files:
        data = pd.read_csv(data_files[0])
        print(f"📊 تحميل البيانات من: {data_files[0]}")
    else:
        data = None
    
    # تحميل الملخص
    summary_files = glob.glob("analysis_summary_*.txt")
    summary_files.sort(reverse=True)
    
    if summary_files:
        with open(summary_files[0], 'r', encoding='utf-8') as f:
            summary = f.read()
        print(f"📋 تحميل الملخص من: {summary_files[0]}")
    else:
        summary = None
    
    return results, data, summary

def display_basic_analysis(results):
    """عرض التحليل الأساسي"""
    
    print("\n" + "="*60)
    print("📊 التحليل الأساسي للبيانات")
    print("="*60)
    
    if 'basic' not in results:
        print("❌ لا توجد نتائج تحليل أساسي")
        return
    
    basic = results['basic']
    
    # معلومات عامة
    if 'data_overview' in basic:
        overview = basic['data_overview']
        print(f"\n📈 معلومات عامة:")
        print(f"   • إجمالي الصفوف: {overview.get('total_records', 'غير محدد'):,}")
        print(f"   • إجمالي الأعمدة: {overview.get('total_columns', 'غير محدد')}")
        print(f"   • حجم البيانات: {overview.get('memory_usage', 0):.2f} MB")
        
        if 'date_range' in overview:
            print(f"   • نطاق التواريخ: {overview['date_range']}")
    
    # جودة البيانات
    if 'data_quality' in basic:
        quality = basic['data_quality']
        print(f"\n🔍 جودة البيانات:")
        print(f"   • الصفوف المكررة: {quality.get('duplicate_records', 0):,}")
        
        # القيم المفقودة
        missing = quality.get('missing_values', {})
        missing_count = sum(v for v in missing.values() if isinstance(v, (int, float)))
        print(f"   • إجمالي القيم المفقودة: {missing_count:,}")
        
        # أنواع البيانات
        data_types = quality.get('data_types', {})
        type_counts = {}
        for dtype in data_types.values():
            type_counts[dtype] = type_counts.get(dtype, 0) + 1
        
        print(f"   • توزيع أنواع البيانات:")
        for dtype, count in type_counts.items():
            print(f"     - {dtype}: {count} عمود")

def display_advanced_analysis(results):
    """عرض التحليل المتقدم"""
    
    print("\n" + "="*60)
    print("🤖 التحليل المتقدم بالذكاء الاصطناعي")
    print("="*60)
    
    if 'advanced' not in results:
        print("❌ لا توجد نتائج تحليل متقدم")
        return
    
    advanced = results['advanced']
    
    # تحليل التجميع
    if 'clustering_analysis' in advanced:
        clustering = advanced['clustering_analysis']
        print(f"\n🎯 تحليل التجميع:")
        
        if 'kmeans' in clustering:
            kmeans = clustering['kmeans']
            print(f"   • K-Means:")
            print(f"     - عدد المجموعات: {kmeans.get('n_clusters', 'غير محدد')}")
            print(f"     - مؤشر الجودة: {kmeans.get('inertia', 0):.2f}")
            
            if 'cluster_distribution' in kmeans:
                print(f"     - توزيع المجموعات:")
                for cluster, count in kmeans['cluster_distribution'].items():
                    print(f"       * المجموعة {cluster}: {count} عنصر")
        
        if 'dbscan' in clustering:
            dbscan = clustering['dbscan']
            print(f"   • DBSCAN:")
            print(f"     - عدد المجموعات: {dbscan.get('n_clusters', 'غير محدد')}")
            print(f"     - النقاط الشاذة: {dbscan.get('noise_points', 0)}")
    
    # كشف الشذوذ
    if 'anomaly_detection' in advanced:
        anomaly = advanced['anomaly_detection']
        print(f"\n⚠️ كشف الشذوذ:")
        print(f"   • إجمالي الحالات الشاذة: {anomaly.get('total_anomalies', 0):,}")
        print(f"   • نسبة الشذوذ: {anomaly.get('anomaly_percentage', 0):.2f}%")
        
        if 'anomaly_scores' in anomaly:
            scores = anomaly['anomaly_scores']
            print(f"   • درجات الشذوذ:")
            print(f"     - المتوسط: {scores.get('mean', 0):.3f}")
            print(f"     - الانحراف المعياري: {scores.get('std', 0):.3f}")
            print(f"     - أدنى درجة: {scores.get('min', 0):.3f}")
            print(f"     - أعلى درجة: {scores.get('max', 0):.3f}")
    
    # تحليل الارتباطات
    if 'correlation_analysis' in advanced:
        correlation = advanced['correlation_analysis']
        print(f"\n🔗 تحليل الارتباطات:")
        
        if 'strong_correlations' in correlation:
            strong_corr = correlation['strong_correlations']
            print(f"   • الارتباطات القوية ({len(strong_corr)} ارتباط):")
            
            for i, corr in enumerate(strong_corr[:5]):  # أول 5 ارتباطات
                print(f"     {i+1}. {corr['variable1']} ↔ {corr['variable2']}")
                print(f"        معامل الارتباط: {corr['correlation']:.3f} ({corr['strength']})")
    
    # التنبؤات
    if 'predictive_insights' in advanced:
        predictions = advanced['predictive_insights']
        print(f"\n🔮 التنبؤات:")
        
        if 'target_variable' in predictions:
            print(f"   • المتغير المستهدف: {predictions['target_variable']}")
            
            if 'model_performance' in predictions:
                performance = predictions['model_performance']
                print(f"   • أداء النموذج:")
                print(f"     - دقة التنبؤ: {performance.get('accuracy_percentage', 0):.1f}%")
                print(f"     - معامل التحديد (R²): {performance.get('r2_score', 0):.3f}")
                print(f"     - الجذر التربيعي لمتوسط مربع الخطأ: {performance.get('rmse', 0):.3f}")
            
            if 'top_features' in predictions:
                print(f"   • أهم المتغيرات المؤثرة:")
                for i, (feature, importance) in enumerate(predictions['top_features']):
                    print(f"     {i+1}. {feature}: {importance:.3f}")

def create_visualizations(data):
    """إنشاء رسوم بيانية بسيطة"""
    
    if data is None:
        print("❌ لا توجد بيانات للتصور")
        return
    
    print("\n" + "="*60)
    print("🎨 إنشاء الرسوم البيانية")
    print("="*60)
    
    # إنشاء مجلد للرسوم
    if not os.path.exists('charts'):
        os.makedirs('charts')
    
    # الأعمدة الرقمية
    numeric_columns = data.select_dtypes(include=[np.number]).columns
    
    if len(numeric_columns) > 0:
        print(f"📊 إنشاء رسوم للأعمدة الرقمية ({len(numeric_columns)} عمود)...")
        
        # رسم توزيع أول 4 أعمدة رقمية
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('توزيع البيانات الرقمية', fontsize=16, fontweight='bold')
        
        for i, column in enumerate(numeric_columns[:4]):
            row = i // 2
            col = i % 2
            
            # تنظيف البيانات
            clean_data = data[column].dropna()
            
            if len(clean_data) > 0:
                axes[row, col].hist(clean_data, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
                axes[row, col].set_title(f'توزيع {column}', fontweight='bold')
                axes[row, col].set_xlabel(column)
                axes[row, col].set_ylabel('التكرار')
                axes[row, col].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('charts/numeric_distributions.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("   ✅ تم حفظ رسم التوزيعات: charts/numeric_distributions.png")
    
    # رسم مصفوفة الارتباط
    if len(numeric_columns) > 1:
        print("🔥 إنشاء خريطة الارتباطات...")
        
        correlation_matrix = data[numeric_columns].corr()
        
        plt.figure(figsize=(12, 10))
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        
        sns.heatmap(
            correlation_matrix, 
            mask=mask,
            annot=True, 
            cmap='RdBu_r', 
            center=0,
            square=True,
            fmt='.2f',
            cbar_kws={"shrink": .8}
        )
        
        plt.title('مصفوفة الارتباطات بين المتغيرات', fontsize=16, fontweight='bold', pad=20)
        plt.tight_layout()
        plt.savefig('charts/correlation_heatmap.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("   ✅ تم حفظ خريطة الارتباطات: charts/correlation_heatmap.png")
    
    # إحصائيات أنواع البيانات
    print("📈 إنشاء رسم أنواع البيانات...")
    
    data_types = data.dtypes.value_counts()
    
    plt.figure(figsize=(10, 6))
    colors = plt.cm.Set3(np.linspace(0, 1, len(data_types)))
    
    bars = plt.bar(range(len(data_types)), data_types.values, color=colors)
    plt.xlabel('أنواع البيانات', fontweight='bold')
    plt.ylabel('عدد الأعمدة', fontweight='bold')
    plt.title('توزيع أنواع البيانات في الجدول', fontsize=14, fontweight='bold')
    plt.xticks(range(len(data_types)), [str(dtype) for dtype in data_types.index], rotation=45)
    
    # إضافة قيم على الأعمدة
    for bar, value in zip(bars, data_types.values):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                str(value), ha='center', va='bottom', fontweight='bold')
    
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('charts/data_types_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("   ✅ تم حفظ رسم أنواع البيانات: charts/data_types_distribution.png")

def main():
    """الدالة الرئيسية"""
    
    print("🤖 عارض نتائج التحليل الذكي لجدول ITEM_MOVEMENT")
    print("=" * 60)
    
    # تحميل النتائج
    results, data, summary = load_latest_results()
    
    if results is None:
        print("❌ لا توجد نتائج للعرض")
        return
    
    # عرض الملخص
    if summary:
        print("\n📋 ملخص التحليل:")
        print("-" * 40)
        print(summary)
    
    # عرض التحليل الأساسي
    display_basic_analysis(results)
    
    # عرض التحليل المتقدم
    display_advanced_analysis(results)
    
    # إنشاء الرسوم البيانية
    create_visualizations(data)
    
    print("\n" + "="*60)
    print("🎉 تم الانتهاء من عرض النتائج!")
    print("📁 تم حفظ الرسوم البيانية في مجلد: charts/")
    print("="*60)

if __name__ == "__main__":
    main()
