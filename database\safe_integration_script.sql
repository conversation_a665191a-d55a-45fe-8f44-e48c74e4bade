-- =====================================================
-- سكريبت آمن للتكامل بين أوامر الشراء والموردين والحوالات
-- Safe Integration Script for Purchase Orders, Suppliers and Transfers
-- =====================================================

-- تعطيل الأخطاء المؤقتة
WHENEVER SQLERROR CONTINUE;

-- 1. إنشاء جدول ربط أوامر الشراء بالمدفوعات (إذا لم يكن موجود)
CREATE TABLE PURCHASE_ORDER_PAYMENTS (
    id NUMBER PRIMARY KEY,
    purchase_order_id NUMBER NOT NULL,
    supplier_payment_transfer_id NUMBER,
    transfer_request_id NUMBER,
    transfer_id NUMBER,
    
    -- تفا<PERSON>يل الدفعة
    payment_type VARCHAR2(30) NOT NULL, -- ADVANCE, PARTIAL, FINAL, FULL
    payment_amount NUMBER(15,2) NOT NULL,
    currency_code VARCHAR2(3) NOT NULL,
    exchange_rate NUMBER(15,6) DEFAULT 1,
    base_currency_amount NUMBER(15,2),
    
    -- تفاصيل إضافية
    payment_percentage NUMBER(5,2), -- نسبة الدفعة من إجمالي أمر الشراء
    payment_description VARCHAR2(500),
    payment_reference VARCHAR2(100),
    
    -- حالة الدفعة
    payment_status VARCHAR2(20) DEFAULT 'PENDING', -- PENDING, APPROVED, EXECUTED, COMPLETED, CANCELLED
    
    -- تواريخ مهمة
    payment_due_date DATE,
    payment_requested_date DATE DEFAULT SYSDATE,
    payment_approved_date DATE,
    payment_executed_date DATE,
    payment_completed_date DATE,
    
    -- معلومات المعالجة
    requested_by NUMBER,
    approved_by NUMBER,
    executed_by NUMBER,
    
    -- ملاحظات
    notes CLOB,
    
    -- بيانات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER
);

-- 2. إنشاء جدول تتبع حالة أوامر الشراء (إذا لم يكن موجود)
CREATE TABLE PURCHASE_ORDER_STATUS_LOG (
    id NUMBER PRIMARY KEY,
    purchase_order_id NUMBER NOT NULL,
    old_status VARCHAR2(30),
    new_status VARCHAR2(30) NOT NULL,
    status_type VARCHAR2(20) NOT NULL, -- ORDER_STATUS, PAYMENT_STATUS, DELIVERY_STATUS
    change_reason VARCHAR2(500),
    change_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    changed_by NUMBER,
    
    -- معلومات إضافية
    related_document_type VARCHAR2(30), -- TRANSFER_REQUEST, GOODS_RECEIPT, INVOICE
    related_document_id NUMBER,
    system_generated CHAR(1) DEFAULT 'N',
    
    -- بيانات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. إنشاء جدول استلام البضائع (إذا لم يكن موجود)
CREATE TABLE GOODS_RECEIPTS (
    id NUMBER PRIMARY KEY,
    receipt_number VARCHAR2(50) UNIQUE NOT NULL,
    purchase_order_id NUMBER NOT NULL,
    supplier_id NUMBER NOT NULL,
    
    -- تفاصيل الاستلام
    receipt_date DATE DEFAULT SYSDATE,
    delivery_note_number VARCHAR2(100),
    invoice_number VARCHAR2(100),
    
    -- حالة الاستلام
    receipt_status VARCHAR2(20) DEFAULT 'PENDING', -- PENDING, PARTIAL, COMPLETE, REJECTED
    quality_status VARCHAR2(20) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED, CONDITIONAL
    
    -- معلومات الاستلام
    received_by NUMBER,
    inspected_by NUMBER,
    approved_by NUMBER,
    
    -- ملاحظات
    receipt_notes CLOB,
    quality_notes CLOB,
    rejection_reason VARCHAR2(500),
    
    -- بيانات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER
);

-- 4. إضافة حقول للجداول الموجودة (بأمان)
-- إضافة حقول لجدول PURCHASE_ORDERS
BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD payment_status VARCHAR2(30) DEFAULT ''PENDING''';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN -- العمود موجود بالفعل
            RAISE;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD payment_due_date DATE';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            RAISE;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD outstanding_amount NUMBER(15,2) DEFAULT 0';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            RAISE;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PURCHASE_ORDERS ADD paid_amount NUMBER(15,2) DEFAULT 0';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            RAISE;
        END IF;
END;
/

-- إضافة حقول لجدول SUPPLIER_TRANSACTIONS
BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE SUPPLIER_TRANSACTIONS ADD purchase_order_id NUMBER';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            RAISE;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE SUPPLIER_TRANSACTIONS ADD purchase_order_number VARCHAR2(50)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN
            RAISE;
        END IF;
END;
/

-- 5. إنشاء Sequences (إذا لم تكن موجودة)
DECLARE
    seq_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'PURCHASE_ORDER_PAYMENTS_SEQ';
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE PURCHASE_ORDER_PAYMENTS_SEQ START WITH 1 INCREMENT BY 1';
    END IF;
END;
/

DECLARE
    seq_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'PURCHASE_ORDER_STATUS_LOG_SEQ';
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE PURCHASE_ORDER_STATUS_LOG_SEQ START WITH 1 INCREMENT BY 1';
    END IF;
END;
/

DECLARE
    seq_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'GOODS_RECEIPTS_SEQ';
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE GOODS_RECEIPTS_SEQ START WITH 1 INCREMENT BY 1';
    END IF;
END;
/

-- 6. إنشاء Triggers
CREATE OR REPLACE TRIGGER purchase_order_payments_trigger
    BEFORE INSERT ON PURCHASE_ORDER_PAYMENTS
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := PURCHASE_ORDER_PAYMENTS_SEQ.NEXTVAL;
    END IF;
    
    -- حساب المبلغ بالعملة الأساسية
    :NEW.base_currency_amount := :NEW.payment_amount * NVL(:NEW.exchange_rate, 1);
    
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER purchase_order_status_log_trigger
    BEFORE INSERT ON PURCHASE_ORDER_STATUS_LOG
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := PURCHASE_ORDER_STATUS_LOG_SEQ.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER goods_receipts_trigger
    BEFORE INSERT ON GOODS_RECEIPTS
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := GOODS_RECEIPTS_SEQ.NEXTVAL;
    END IF;
    
    -- إنشاء رقم إيصال تلقائي إذا لم يكن محدد
    IF :NEW.receipt_number IS NULL THEN
        :NEW.receipt_number := 'GR' || TO_CHAR(SYSDATE, 'YYYYMMDD') || LPAD(GOODS_RECEIPTS_SEQ.CURRVAL, 4, '0');
    END IF;
    
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

-- 7. إنشاء فهارس للأداء
CREATE INDEX idx_pop_purchase_order ON PURCHASE_ORDER_PAYMENTS(purchase_order_id);
CREATE INDEX idx_pop_payment_status ON PURCHASE_ORDER_PAYMENTS(payment_status);
CREATE INDEX idx_posl_purchase_order ON PURCHASE_ORDER_STATUS_LOG(purchase_order_id);
CREATE INDEX idx_gr_purchase_order ON GOODS_RECEIPTS(purchase_order_id);

-- 8. إنشاء View أساسي للتكامل
CREATE OR REPLACE VIEW V_PURCHASE_ORDERS_INTEGRATION AS
SELECT 
    po.id as purchase_order_id,
    po.po_number,
    po.title,
    po.supplier_id,
    s.name_ar as supplier_name,
    po.total_amount,
    po.currency,
    po.payment_status,
    po.outstanding_amount,
    po.paid_amount,
    po.payment_due_date,
    po.status as order_status,
    
    -- عدد المدفوعات
    (SELECT COUNT(*) FROM PURCHASE_ORDER_PAYMENTS pop 
     WHERE pop.purchase_order_id = po.id) as payments_count,
    
    -- آخر دفعة
    (SELECT MAX(payment_completed_date) FROM PURCHASE_ORDER_PAYMENTS pop 
     WHERE pop.purchase_order_id = po.id 
     AND pop.payment_status = 'COMPLETED') as last_payment_date,
    
    po.created_at,
    po.updated_at

FROM PURCHASE_ORDERS po
LEFT JOIN SUPPLIERS s ON po.supplier_id = s.id;

-- إعادة تفعيل معالجة الأخطاء
WHENEVER SQLERROR EXIT FAILURE;

-- رسالة نجاح
SELECT 'تم تنفيذ السكريبت الأساسي بنجاح!' as status FROM dual;

COMMIT;
