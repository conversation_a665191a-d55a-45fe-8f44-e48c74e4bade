# دليل إعداد WhatsApp Business API

## 🎯 المتطلبات الأساسية:

### 1️⃣ **إنشاء حساب WhatsApp Business**
- انتقل إلى: https://business.whatsapp.com/
- أنشئ حساب جديد أو استخدم حساب موجود
- تحقق من رقم الهاتف التجاري

### 2️⃣ **إعداد Meta for Developers**
- انتقل إلى: https://developers.facebook.com/
- أنشئ تطبيق جديد من نوع "Business"
- أضف منتج "WhatsApp Business API"

### 3️⃣ **الحصول على المعرفات المطلوبة**
```
WHATSAPP_ACCESS_TOKEN=EAAxxxxxxxxxxxxxxx
WHATSAPP_PHONE_NUMBER_ID=****************
WHATSAPP_BUSINESS_ACCOUNT_ID=****************
WHATSAPP_API_URL=https://graph.facebook.com/v18.0
WHATSAPP_TEST_MODE=false
```

## 🔧 **خطوات التفعيل:**

### الخطوة 1: إضافة متغيرات البيئة
```bash
# في ملف .env أو متغيرات النظام
WHATSAPP_ACCESS_TOKEN=your_access_token_here
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id_here
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id_here
WHATSAPP_TEST_MODE=true  # للاختبار أولاً
```

### الخطوة 2: إضافة إجراء الواتساب للأتمتة
```python
# في automation_engine.py
def _send_whatsapp_notification(self, rule, shipment_data):
    """إرسال إشعار واتساب"""
    try:
        from app.services.whatsapp_service import whatsapp_service
        
        # جلب بيانات أمر التسليم
        delivery_order_id = shipment_data.get('delivery_order_id')
        if not delivery_order_id:
            return {'success': False, 'message': 'معرف أمر التسليم غير متوفر'}
        
        # جلب بيانات الأمر
        order_data = self._get_delivery_order_data(delivery_order_id)
        if not order_data:
            return {'success': False, 'message': 'بيانات أمر التسليم غير متوفرة'}
        
        # جلب رقم هاتف المخلص
        agent_phone = self._get_agent_phone(order_data['customs_agent_id'])
        if not agent_phone:
            return {'success': False, 'message': 'رقم هاتف المخلص غير متوفر'}
        
        # إرسال الرسالة
        success, message, msg_id = whatsapp_service.send_delivery_order(order_data, agent_phone)
        
        if success:
            return {
                'success': True,
                'message': f'تم إرسال أمر التسليم عبر الواتساب: {message}',
                'whatsapp_message_id': msg_id
            }
        else:
            return {
                'success': False,
                'message': f'فشل إرسال الواتساب: {message}'
            }
            
    except Exception as e:
        return {
            'success': False,
            'message': f'خطأ في إرسال الواتساب: {str(e)}'
        }
```

### الخطوة 3: إضافة قاعدة جديدة
```sql
INSERT INTO automation_rules (
    id, rule_name, rule_type, trigger_condition, condition_value,
    action_type, is_active, priority_level, created_by
) VALUES (
    automation_rules_seq.NEXTVAL,
    'إرسال أمر التسليم عبر الواتساب',
    'NOTIFICATION',
    'DELIVERY_ORDER_CREATED',
    'any',
    'SEND_WHATSAPP_NOTIFICATION',
    1,
    2,
    1
);
```

## 📱 **اختبار النظام:**

### 1️⃣ **اختبار الخدمة:**
```python
from app.services.whatsapp_service import whatsapp_service

# اختبار الإعدادات
print("WhatsApp configured:", whatsapp_service.is_configured())

# اختبار إرسال رسالة
order_data = {
    'id': 123,
    'order_number': 'DO-20250821-000001',
    'tracking_number': 'TRK123456',
    'delivery_location': 'عدن، اليمن'
}

success, message, msg_id = whatsapp_service.send_delivery_order(
    order_data, 
    '+967771234567'
)

print(f"Success: {success}")
print(f"Message: {message}")
print(f"Message ID: {msg_id}")
```

### 2️⃣ **اختبار الأتمتة:**
```python
# تحديث حالة شحنة لتشغيل الأتمتة
UPDATE cargo_shipments 
SET shipment_status = 'arrived_port' 
WHERE id = 175;

# مراقبة السجلات
SELECT * FROM automation_execution_log 
WHERE shipment_id = 175 
ORDER BY executed_at DESC;
```

## 🎯 **النتيجة المتوقعة:**

عند تحديث حالة الشحنة إلى "وصلت للميناء":
1. ✅ **Database Trigger** يضيف للطابور
2. ✅ **معالج الطابور** ينفذ القاعدة الأولى (إنشاء أمر التسليم)
3. ✅ **معالج الطابور** ينفذ القاعدة الثانية (إرسال الواتساب)
4. ✅ **المخلص يستلم** رسالة واتساب بتفاصيل أمر التسليم

## 🔒 **الأمان:**
- استخدم HTTPS دائماً
- احم access token كسر عالي الأمان
- فعل webhook للتحقق من حالة الرسائل
- راقب استخدام API لتجنب تجاوز الحدود

## 💰 **التكلفة:**
- **Meta WhatsApp Cloud API**: مجاني للاستخدام المحدود
- **الرسائل المجانية**: 1000 رسالة شهرياً
- **بعد ذلك**: تكلفة منخفضة جداً لكل رسالة
