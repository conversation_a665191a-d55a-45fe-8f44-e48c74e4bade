{% extends "base.html" %}

{% block title %}أوامر الشراء{% endblock %}

{% block extra_css %}
<!-- 🛡️ تنسيقات حماية أوامر الشراء -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/po_protection.css') }}">
<!-- 🎨 التصميم الحديث لأوامر الشراء -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/purchase_orders_modern.css') }}?v={{ moment().format('YYYYMMDDHHmmss') if moment else '20250908' }}{{ range(1, 10000) | random }}">
<style>
    /* 🎨 تطبيق التصميم الحديث مباشرة مع !important */

    /* إعادة تعيين الخلفية */
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        min-height: 100vh !important;
    }

    .container-fluid {
        background: transparent !important;
        padding: 2rem 0 !important;
    }

    /* إخفاء التصميم القديم */
    .purchase-orders-container {
        background: transparent !important;
    }

    /* تطبيق التصميم الجديد على Header */
    .card.border-0.shadow-sm.mb-4 {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-radius: 12px !important;
        padding: 2rem !important;
        margin-bottom: 2rem !important;
        box-shadow: 0 10px 30px rgba(0,0,0,0.15) !important;
        color: white !important;
        border: none !important;
    }

    .card.border-0.shadow-sm.mb-4 .card-body {
        background: transparent !important;
        color: white !important;
        padding: 0 !important;
    }

    .card.border-0.shadow-sm.mb-4 h1 {
        font-size: 2.5rem !important;
        font-weight: 700 !important;
        margin-bottom: 0.5rem !important;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        color: white !important;
    }

    .card.border-0.shadow-sm.mb-4 p {
        font-size: 1.1rem !important;
        opacity: 0.9 !important;
        font-weight: 400 !important;
        color: white !important;
    }

    /* تطبيق التصميم على بطاقات الإحصائيات */
    .card.border-0.shadow-sm.h-100 {
        background: white !important;
        border-radius: 12px !important;
        padding: 2rem !important;
        text-align: center !important;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        border-left: 4px solid #667eea !important;
        position: relative !important;
        overflow: hidden !important;
        min-height: 120px !important;
        border: none !important;
    }

    .card.border-0.shadow-sm.h-100:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 10px 30px rgba(0,0,0,0.15) !important;
        cursor: pointer !important;
    }

    .card.border-0.shadow-sm.h-100 .card-body {
        background: transparent !important;
        padding: 0 !important;
    }

    .card.border-0.shadow-sm.h-100 h4 {
        font-size: 2.5rem !important;
        font-weight: 700 !important;
        color: #2c3e50 !important;
        margin-bottom: 0.5rem !important;
        display: block !important;
    }

    .card.border-0.shadow-sm.h-100 small {
        color: #6c757d !important;
        font-size: 0.9rem !important;
        font-weight: 500 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }

    /* تطبيق التصميم على لوحة التحكم */
    .control-panel {
        background: white !important;
        border-radius: 12px !important;
        padding: 2rem !important;
        margin-bottom: 2rem !important;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
        border: 1px solid #e9ecef !important;
    }

    /* تطبيق التصميم على الجدول */
    .card.border-0.shadow-sm:last-child {
        background: white !important;
        border-radius: 12px !important;
        overflow: hidden !important;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
        border: 1px solid #e9ecef !important;
    }

    .card.border-0.shadow-sm:last-child .card-body {
        padding: 0 !important;
        background: transparent !important;
    }

    .table {
        margin-bottom: 0 !important;
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }

    .table-dark {
        background: linear-gradient(135deg, #343a40 0%, #495057 100%) !important;
        color: white !important;
    }

    .table-dark th {
        font-weight: 600 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
        font-size: 0.85rem !important;
        padding: 1rem !important;
        border: none !important;
        background: transparent !important;
        color: white !important;
    }

    .table tbody tr {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        border-bottom: 1px solid #e9ecef !important;
    }

    .table tbody tr:hover {
        background: rgba(102, 126, 234, 0.05) !important;
        transform: scale(1.01) !important;
    }

    .table tbody td {
        padding: 1rem !important;
        vertical-align: middle !important;
        border: none !important;
        font-size: 0.9rem !important;
    }

    /* تحسين الحقول */
    .form-control, .form-select {
        border: 2px solid #e9ecef !important;
        border-radius: 8px !important;
        padding: 0.75rem 1rem !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        background: white !important;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea !important;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
        background: white !important;
    }

    /* تحسين الأزرار */
    .btn {
        border-radius: 8px !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 600 !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        border: none !important;
    }

    .btn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
    }

    .btn:active {
        transform: translateY(0) !important;
    }

    /* تحسين Breadcrumb */
    .breadcrumb {
        background: white !important;
        border-radius: 12px !important;
        padding: 1rem 1.5rem !important;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
        border: 1px solid #e9ecef !important;
        margin-bottom: 2rem !important;
    }

    /* تحسين الشارات */
    .badge {
        padding: 0.5rem 1rem !important;
        border-radius: 20px !important;
        font-weight: 600 !important;
        font-size: 0.75rem !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }

    /* إضافة أيقونات للبطاقات */
    .card.border-0.shadow-sm.h-100:nth-child(1)::before {
        content: '\f07a' !important;
        font-family: 'Font Awesome 6 Free' !important;
        font-weight: 900 !important;
        position: absolute !important;
        top: 1rem !important;
        right: 1rem !important;
        font-size: 2rem !important;
        opacity: 0.1 !important;
        color: #2c3e50 !important;
    }

    .card.border-0.shadow-sm.h-100:nth-child(2)::before {
        content: '\f044' !important;
        font-family: 'Font Awesome 6 Free' !important;
        font-weight: 900 !important;
        position: absolute !important;
        top: 1rem !important;
        right: 1rem !important;
        font-size: 2rem !important;
        opacity: 0.1 !important;
        color: #2c3e50 !important;
    }

    .card.border-0.shadow-sm.h-100:nth-child(3)::before {
        content: '\f058' !important;
        font-family: 'Font Awesome 6 Free' !important;
        font-weight: 900 !important;
        position: absolute !important;
        top: 1rem !important;
        right: 1rem !important;
        font-size: 2rem !important;
        opacity: 0.1 !important;
        color: #2c3e50 !important;
    }

    .card.border-0.shadow-sm.h-100:nth-child(4)::before {
        content: '\f0c1' !important;
        font-family: 'Font Awesome 6 Free' !important;
        font-weight: 900 !important;
        position: absolute !important;
        top: 1rem !important;
        right: 1rem !important;
        font-size: 2rem !important;
        opacity: 0.1 !important;
        color: #2c3e50 !important;
    }

    /* تحسين responsive */
    @media (max-width: 768px) {
        .card.border-0.shadow-sm.mb-4 h1 {
            font-size: 2rem !important;
        }

        .card.border-0.shadow-sm.h-100 {
            margin-bottom: 1rem !important;
            min-height: 100px !important;
        }

        .card.border-0.shadow-sm.h-100 h4 {
            font-size: 1.8rem !important;
        }

        .control-panel {
            padding: 1rem !important;
        }

        .table-responsive {
            font-size: 0.8rem !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <div class="container-fluid px-3">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <div class="me-4">
                            <i class="fas fa-shopping-cart fa-4x opacity-75"></i>
                        </div>
                        <div>
                            <h1 class="page-title">إدارة أوامر الشراء</h1>
                            <p class="page-subtitle mb-0">إدارة شاملة لجميع أوامر الشراء والمشتريات من الموردين</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex justify-content-end align-items-center">
                        <div class="me-3">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="h4 mb-0 fw-bold">{{ stats.total_count if stats else 0 }}</div>
                                    <small class="opacity-75">إجمالي الأوامر</small>
                                </div>
                                <div class="col-6">
                                    <div class="h4 mb-0 fw-bold">{{ stats.confirmed_count if stats else 0 }}</div>
                                    <small class="opacity-75">مؤكدة</small>
                                </div>
                            </div>
                        </div>
                        <div class="vr opacity-50 me-3"></div>
                        <div>
                            <a href="{{ url_for('purchase_orders.new') }}" class="btn btn-light btn-modern">
                                <i class="fas fa-plus me-2"></i>إضافة أمر شراء جديد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Breadcrumb Navigation -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-white rounded-3 px-3 py-2 shadow-sm border">
                        <li class="breadcrumb-item">
                            <a href="{{ url_for('main.dashboard') }}" class="text-decoration-none text-primary">
                                <i class="fas fa-home me-1"></i>الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ url_for('main.dashboard') }}" class="text-decoration-none text-primary">
                                <i class="fas fa-cogs me-1"></i>إدارة النظام
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-shopping-cart me-1"></i>
                            إدارة أوامر الشراء
                        </li>
                    </ol>
                </nav>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4" id="statsContainer">
            <div class="col-md-3">
                <div class="stats-card success position-relative" onclick="showStatisticsDetails()" style="cursor: pointer;" title="انقر لعرض التفاصيل">
                    <i class="fas fa-shopping-cart stats-icon"></i>
                    <span class="stats-number" id="totalOrders">0</span>
                    <div class="stats-label">إجمالي الأوامر</div>
                    <small class="text-muted mt-1">انقر للتفاصيل</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card info position-relative" onclick="showStatisticsDetails()" style="cursor: pointer;" title="انقر لعرض التفاصيل">
                    <i class="fas fa-money-bill-wave stats-icon"></i>
                    <span class="stats-number" id="totalValue">0</span>
                    <div class="stats-label">إجمالي القيمة</div>
                    <small class="text-muted mt-1">انقر للتفاصيل</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card warning position-relative" onclick="showStatisticsDetails()" style="cursor: pointer;" title="انقر لعرض التفاصيل">
                    <i class="fas fa-check-circle stats-icon"></i>
                    <span class="stats-number" id="confirmedOrders">0</span>
                    <div class="stats-label">الأوامر المؤكدة</div>
                    <small class="text-muted mt-1">انقر للتفاصيل</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card danger position-relative" onclick="showStatisticsDetails()" style="cursor: pointer;" title="انقر لعرض التفاصيل">
                    <i class="fas fa-link stats-icon"></i>
                    <span class="stats-number" id="usedOrders">0</span>
                    <div class="stats-label">الأوامر المُستخدمة</div>
                    <small class="text-muted mt-1">انقر للتفاصيل</small>
                </div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="control-panel">
            <div class="row align-items-end">
                <div class="col-md-3">
                    <label class="form-label fw-bold">البحث السريع</label>
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" id="searchInput" class="form-control-modern"
                               placeholder="البحث برقم الأمر، اسم المورد، أو المبلغ..."
                               onkeyup="performQuickSearch()">
                        <button class="btn btn-outline-secondary btn-modern" type="button" onclick="clearSearch()" title="مسح البحث">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">الحالة</label>
                    <select class="form-control-modern" id="statusFilter" onchange="applyFilters()">
                        <option value="">جميع الحالات</option>
                        <option value="مسودة">مسودة</option>
                        <option value="مرسل">مرسل</option>
                        <option value="مؤكد">مؤكد</option>
                        <option value="ملغي">ملغي</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">من تاريخ</label>
                    <input type="date" class="form-control-modern" id="dateFrom" onchange="applyFilters()">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">إلى تاريخ</label>
                    <input type="date" class="form-control-modern" id="dateTo" onchange="applyFilters()">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">الاستخدام</label>
                    <select class="form-control-modern" id="usageFilter" onchange="applyFilters()">
                        <option value="">جميع الأوامر</option>
                        <option value="used">مُستخدمة</option>
                        <option value="unused">غير مُستخدمة</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary btn-modern" onclick="loadData()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button type="button" class="btn btn-info btn-modern" onclick="exportToExcel()">
                            <i class="fas fa-file-excel"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-modern" onclick="clearAllFilters()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Purchase Orders Table -->
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-modern" id="purchaseOrdersTable">
                    <thead>
                        <tr>
                            <th>رقم الأمر</th>
                            <th>رقم العقد</th>
                            <th>المورد</th>
                            <th>تاريخ الأمر</th>
                            <th>تاريخ التسليم</th>
                            <th>القيمة الإجمالية</th>
                            <th>الحالة</th>
                            <th>مُستخدم</th>
                            <th>العمليات</th>
                        </tr>
                    </thead>
                            <tbody>
                                {% for po in purchase_orders %}
                                <tr data-po-id="{{ po[0] }}" class="po-row">
                                    <td>
                                        <strong class="text-primary po-number">{{ po[1] }}</strong>
                                    </td>
                                    <td>
                                        {% if po[11] %}
                                            <span class="badge bg-info">{{ po[11] }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ po[2] or '-' }}</td>
                                    <td>
                                        {% if po[3] %}
                                            {{ po[3].strftime('%Y-%m-%d') }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if po[4] %}
                                            {{ po[4].strftime('%Y-%m-%d') }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong class="text-success">
                                            {{ "{:,.2f}".format(po[6] or 0) }}
                                            {% if po[13] %}
                                                {{ po[13] }}
                                            {% else %}
                                                ريال
                                            {% endif %}
                                        </strong>
                                    </td>
                                    <td>
                                        {% set status_class = {
                                            'مسودة': 'secondary',
                                            'مرسل': 'warning',
                                            'مؤكد': 'success',
                                            'ملغي': 'danger'
                                        } %}
                                        <span class="badge bg-{{ status_class.get(po[5], 'secondary') }}">
                                            {{ po[5] or 'غير محدد' }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        {% if po|length > 8 and po[8] == 1 %}
                                            <span class="badge bg-success" title="تم استخدام هذا الأمر في شحنة">
                                                <i class="fas fa-check-circle me-1"></i>
                                                مُستخدم
                                            </span>
                                            {% if po|length > 10 and po[10] %}
                                                <br>
                                                <small class="text-muted">
                                                    <a href="/shipments/view/{{ po[10] }}" target="_blank" class="text-decoration-none">
                                                        <i class="fas fa-ship me-1"></i>
                                                        شحنة {{ po[10] }}
                                                    </a>
                                                </small>
                                            {% endif %}
                                        {% else %}
                                            <span class="badge bg-warning" title="لم يتم استخدام هذا الأمر في أي شحنة">
                                                <i class="fas fa-clock me-1"></i>
                                                غير مُستخدم
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('purchase_orders.view', po_id=po[0]) }}"
                                               class="btn btn-outline-primary" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-warning edit-po-btn"
                                                    data-po-id="{{ po[0] }}" onclick="editPO({{ po[0] }})" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button"
                                                    class="btn btn-outline-info btn-sm"
                                                    title="إدارة وثائق أمر الشراء"
                                                    onclick="window.location.href='/purchase-orders/{{ po[0] }}/documents'">
                                                <i class="fas fa-folder-open"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger delete-po-btn"
                                                    data-po-id="{{ po[0] }}" onclick="deletePO({{ po[0] }})" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-shopping-cart fa-3x mb-3 d-block"></i>
                                        لا توجد أوامر شراء حالياً
                                        <br>
                                        <a href="{{ url_for('purchase_orders.new') }}" class="btn btn-primary mt-2">
                                            إضافة أول أمر شراء
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// وظائف JavaScript مطابقة للعقود
function applyFilters() {
    // تطبيق الفلاتر
    console.log('تطبيق الفلاتر...');
}

function exportData() {
    // تصدير البيانات
    console.log('تصدير البيانات...');
}

// 🛡️ دالة تعديل أمر الشراء مع فحص الحماية
async function editPO(poId) {
    console.log('🔧 محاولة تعديل أمر الشراء:', poId);

    try {
        // فحص الحماية أولاً
        const response = await fetch(`/purchase-orders/api/check-protection/${poId}`);
        const result = await response.json();

        if (!result.success) {
            alert('خطأ في فحص الحماية: ' + result.message);
            return;
        }

        // فحص إمكانية التعديل
        if (!result.can_edit) {
            // إظهار تنبيه الحماية
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'warning',
                    title: '🛡️ أمر شراء محمي',
                    text: `لا يمكن تعديل أمر الشراء ${result.po_number}\n\nالسبب: ${result.protection_reason}`,
                    confirmButtonText: 'موافق'
                });
            } else {
                alert(`🛡️ أمر شراء محمي\n\nلا يمكن تعديل أمر الشراء ${result.po_number}\n\nالسبب: ${result.protection_reason}`);
            }
            return;
        }

        // إذا لم يكن محمي، متابعة التعديل
        console.log('✅ أمر الشراء غير محمي - متابعة التعديل');
        window.location.href = `/purchase-orders/edit/${poId}`;

    } catch (error) {
        console.error('خطأ في فحص الحماية:', error);
        alert('حدث خطأ في فحص الحماية. سيتم المتابعة بحذر.');
        // في حالة الخطأ، السماح بالمتابعة (fallback)
        window.location.href = `/purchase-orders/edit/${poId}`;
    }
}

// 🛡️ دالة حذف أمر الشراء مع فحص الحماية
async function deletePO(poId) {
    console.log('🗑️ محاولة حذف أمر الشراء:', poId);

    try {
        // فحص الحماية أولاً
        const response = await fetch(`/purchase-orders/api/check-protection/${poId}`);
        const result = await response.json();

        if (!result.success) {
            alert('خطأ في فحص الحماية: ' + result.message);
            return;
        }

        // فحص إمكانية الحذف
        if (!result.can_delete) {
            // إظهار تنبيه الحماية
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'warning',
                    title: '🛡️ أمر شراء محمي',
                    text: `لا يمكن حذف أمر الشراء ${result.po_number}\n\nالسبب: ${result.protection_reason}`,
                    confirmButtonText: 'موافق'
                });
            } else {
                alert(`🛡️ أمر شراء محمي\n\nلا يمكن حذف أمر الشراء ${result.po_number}\n\nالسبب: ${result.protection_reason}`);
            }
            return;
        }

        // إذا لم يكن محمي، طلب التأكيد
        const confirmDelete = confirm(`هل أنت متأكد من حذف أمر الشراء ${result.po_number}؟\n\nسيتم حذف جميع الأصناف المرتبطة به أيضاً.\n\nهذا الإجراء لا يمكن التراجع عنه.`);

        if (!confirmDelete) {
            return;
        }

        // إظهار مؤشر التحميل
        const deleteBtn = document.querySelector(`button[onclick="deletePO(${poId})"]`);
        const originalContent = deleteBtn.innerHTML;
        deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        deleteBtn.disabled = true;

        // إرسال طلب الحذف
        const deleteResponse = await fetch(`/purchase-orders/api/delete/${poId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        const deleteResult = await deleteResponse.json();

        if (deleteResult.success) {
            // إظهار رسالة نجاح
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'success',
                    title: 'تم الحذف بنجاح',
                    text: 'تم حذف أمر الشراء بنجاح',
                    timer: 2000,
                    showConfirmButton: false
                });
            } else {
                alert('تم حذف أمر الشراء بنجاح');
            }

            // إزالة الصف من الجدول
            const row = deleteBtn.closest('tr');
            row.style.transition = 'opacity 0.3s';
            row.style.opacity = '0';
            setTimeout(() => {
                row.remove();
            }, 300);
        } else {
            throw new Error(deleteResult.message || 'فشل في حذف أمر الشراء');
        }

    } catch (error) {
        console.error('خطأ في حذف أمر الشراء:', error);

        // إظهار رسالة الخطأ
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'error',
                title: 'فشل في الحذف',
                text: error.message || 'حدث خطأ أثناء حذف أمر الشراء',
                confirmButtonText: 'موافق'
            });
        } else {
            alert('فشل في حذف أمر الشراء: ' + (error.message || 'حدث خطأ غير متوقع'));
        }

        // استعادة الزر
        const deleteBtn = document.querySelector(`button[onclick="deletePO(${poId})"]`);
        if (deleteBtn) {
            deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
            deleteBtn.disabled = false;
        }
    }
}

// دالة لتحديث ترقيم الصفوف بعد الحذف
function updateRowNumbers() {
    const rows = document.querySelectorAll('#purchaseOrdersTable tbody tr');
    rows.forEach((row, index) => {
        const numberCell = row.querySelector('td:first-child');
        if (numberCell) {
            numberCell.textContent = index + 1;
        }
    });
}

// دالة لإظهار الرسائل
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // إضافة الرسالة في أعلى الصفحة
    const container = document.querySelector('.container-fluid');
    const alertDiv = document.createElement('div');
    alertDiv.innerHTML = alertHtml;
    container.insertBefore(alertDiv.firstElementChild, container.firstElementChild);

    // إزالة الرسالة تلقائياً بعد 5 ثوان
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

// البحث المباشر
document.getElementById('searchInput').addEventListener('input', function() {
    // تطبيق البحث
    console.log('البحث:', this.value);
});

// 🛡️ تهيئة نظام حماية أوامر الشراء
document.addEventListener('DOMContentLoaded', function() {
    console.log('🛡️ تهيئة نظام حماية أوامر الشراء...');

    // فحص الحماية لجميع أوامر الشراء في الصفحة
    checkAllPOProtection();
});

// دالة فحص حماية جميع أوامر الشراء
async function checkAllPOProtection() {
    const poRows = document.querySelectorAll('[data-po-id]');
    const poIds = Array.from(poRows).map(row => parseInt(row.dataset.poId));

    if (poIds.length === 0) {
        console.log('لا توجد أوامر شراء للفحص');
        return;
    }

    console.log(`🔍 فحص حماية ${poIds.length} أمر شراء...`);

    try {
        const response = await fetch('/purchase-orders/api/protection-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ po_ids: poIds })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            // تطبيق الحماية على العناصر
            Object.entries(result.protection_status).forEach(([poId, status]) => {
                applyProtectionToRow(parseInt(poId), status);
            });
            console.log('✅ تم تطبيق الحماية على جميع أوامر الشراء');
        } else {
            console.error('فشل في فحص حماية أوامر الشراء:', result.message);
        }

    } catch (error) {
        console.error('خطأ في فحص حماية أوامر الشراء:', error);
    }
}

// دالة تطبيق الحماية على صف أمر الشراء
function applyProtectionToRow(poId, protectionStatus) {
    const poRow = document.querySelector(`[data-po-id="${poId}"]`);
    if (!poRow) return;

    const editBtn = poRow.querySelector('.edit-po-btn');
    const deleteBtn = poRow.querySelector('.delete-po-btn');
    const poNumberElement = poRow.querySelector('.po-number');

    if (protectionStatus.protection_level === 'full') {
        // تطبيق الحماية الكاملة
        protectButton(editBtn, 'تعديل', protectionStatus);
        protectButton(deleteBtn, 'حذف', protectionStatus);

        // إضافة أيقونة الحماية
        addProtectionIcon(poNumberElement, protectionStatus);

        // إضافة كلاس للصف
        poRow.classList.add('po-row-protected');

        console.log(`🛡️ تم حماية أمر الشراء ${protectionStatus.po_number}`);
    }
}

// دالة حماية زر
function protectButton(button, action, protectionStatus) {
    if (!button) return;

    button.disabled = true;
    button.classList.add('btn-protected');
    button.innerHTML = `🔒 ${action} محمي`;
    button.title = `لا يمكن ${action} أمر الشراء: ${protectionStatus.protection_reason}`;

    // إضافة تنسيق مخصص
    button.style.backgroundColor = '#6c757d';
    button.style.borderColor = '#6c757d';
    button.style.cursor = 'not-allowed';
}

// دالة إضافة أيقونة الحماية
function addProtectionIcon(poNumberElement, protectionStatus) {
    if (!poNumberElement) return;

    // إزالة الأيقونة الموجودة إن وجدت
    const existingIcon = poNumberElement.querySelector('.protection-icon');
    if (existingIcon) {
        existingIcon.remove();
    }

    const protectionIcon = document.createElement('span');
    protectionIcon.className = 'protection-icon badge badge-warning ms-2';
    protectionIcon.innerHTML = '🛡️ محمي';
    protectionIcon.title = protectionStatus.protection_reason;

    poNumberElement.appendChild(protectionIcon);
}

// دوال البحث والتصفية الجديدة
function performQuickSearch() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const rows = document.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function clearSearch() {
    document.getElementById('searchInput').value = '';
    performQuickSearch();
}

function applyFilters() {
    const statusFilter = document.getElementById('statusFilter').value;
    const dateFrom = document.getElementById('dateFrom').value;
    const dateTo = document.getElementById('dateTo').value;
    const usageFilter = document.getElementById('usageFilter').value;
    const rows = document.querySelectorAll('tbody tr');

    rows.forEach(row => {
        let showRow = true;

        // تصفية الحالة
        if (statusFilter) {
            const statusCell = row.cells[6]; // عمود الحالة
            if (statusCell && !statusCell.textContent.includes(statusFilter)) {
                showRow = false;
            }
        }

        // تصفية الاستخدام
        if (usageFilter) {
            const usageCell = row.cells[7]; // عمود مُستخدم
            const isUsed = usageCell && usageCell.textContent.includes('✅');
            if ((usageFilter === 'used' && !isUsed) || (usageFilter === 'unused' && isUsed)) {
                showRow = false;
            }
        }

        // تصفية التاريخ
        if (dateFrom || dateTo) {
            const dateCell = row.cells[3]; // عمود تاريخ الأمر
            if (dateCell) {
                const rowDate = dateCell.textContent.trim();
                if (dateFrom && rowDate < dateFrom) showRow = false;
                if (dateTo && rowDate > dateTo) showRow = false;
            }
        }

        row.style.display = showRow ? '' : 'none';
    });
}

function clearAllFilters() {
    document.getElementById('statusFilter').value = '';
    document.getElementById('dateFrom').value = '';
    document.getElementById('dateTo').value = '';
    document.getElementById('usageFilter').value = '';
    document.getElementById('searchInput').value = '';

    // إظهار جميع الصفوف
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach(row => {
        row.style.display = '';
    });
}

// جعل البطاقات تفاعلية
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎨 تطبيق التصميم التفاعلي...');

    // إضافة تفاعل للبطاقات
    const statsCards = document.querySelectorAll('.card.border-0.shadow-sm.h-100');
    statsCards.forEach((card, index) => {
        card.style.cursor = 'pointer';
        card.addEventListener('click', function() {
            showStatsDetails(index);
        });

        // إضافة نص "انقر للتفاصيل"
        const cardBody = card.querySelector('.card-body');
        if (cardBody && !cardBody.querySelector('.click-hint')) {
            const hint = document.createElement('small');
            hint.className = 'text-muted mt-1 click-hint';
            hint.textContent = 'انقر للتفاصيل';
            hint.style.display = 'block';
            cardBody.appendChild(hint);
        }
    });

    // تطبيق التصميم على العناصر الجديدة
    applyModernDesign();
});

function showStatsDetails(cardIndex) {
    const cardNames = ['إجمالي الأوامر', 'المسودات', 'المؤكدة', 'المُستخدمة'];
    const cardName = cardNames[cardIndex] || 'الإحصائيات';

    alert(`تفاصيل ${cardName}\n\nسيتم تطوير هذه الميزة قريباً لعرض تفاصيل شاملة ومتعددة العملات.`);
}

function applyModernDesign() {
    // تطبيق تصميم إضافي على العناصر
    const tables = document.querySelectorAll('.table');
    tables.forEach(table => {
        table.style.borderCollapse = 'separate';
        table.style.borderSpacing = '0';
    });

    // تحسين الأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    console.log('✅ تم تطبيق التصميم التفاعلي بنجاح');
}

// متغيرات البيانات
let purchaseOrdersData = [];

// تحميل البيانات
function loadData() {
    console.log('🔄 بدء تحميل بيانات أوامر الشراء...');
    showLoading(true);

    const params = new URLSearchParams();

    const statusFilter = document.getElementById('statusFilter').value;
    const dateFrom = document.getElementById('dateFrom').value;
    const dateTo = document.getElementById('dateTo').value;
    const usageFilter = document.getElementById('usageFilter').value;

    if (statusFilter) params.append('status', statusFilter);
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);
    if (usageFilter) params.append('usage', usageFilter);

    fetch(`/purchase-orders/api/data?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                purchaseOrdersData = data.data;
                updateTable();
                updateStatistics();
            } else {
                showAlert('خطأ في تحميل البيانات: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل البيانات:', error);
            showAlert('خطأ في تحميل البيانات', 'danger');
        })
        .finally(() => {
            showLoading(false);
        });
}

// تحديث الجدول
function updateTable() {
    console.log('📊 بدء تحديث الجدول...');
    const tbody = document.querySelector('#purchaseOrdersTable tbody');

    console.log(`📋 عدد السجلات لعرضها: ${purchaseOrdersData.length}`);

    if (purchaseOrdersData.length === 0) {
        console.log('📭 لا توجد بيانات لعرضها');
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <div>لا توجد أوامر شراء</div>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = '';

    purchaseOrdersData.forEach(order => {
        const row = document.createElement('tr');
        row.className = 'po-row';
        row.setAttribute('data-po-id', order.id);

        const statusClass = getStatusBadgeClass(order.status);
        const usageClass = order.is_used ? 'success' : 'secondary';

        row.innerHTML = `
            <td>
                <strong class="text-primary">${order.po_number}</strong>
            </td>
            <td>
                ${order.contract_number ? `<span class="badge bg-info">${order.contract_number}</span>` : '<span class="text-muted">-</span>'}
            </td>
            <td>${order.supplier_name || '-'}</td>
            <td>${order.po_date ? new Date(order.po_date).toLocaleDateString('ar-SA') : '-'}</td>
            <td>${order.delivery_date ? new Date(order.delivery_date).toLocaleDateString('ar-SA') : '-'}</td>
            <td>
                <strong class="text-success">
                    ${formatNumber(order.total_amount || 0)}
                    ${order.currency_symbol || order.currency || ''}
                </strong>
            </td>
            <td>
                <span class="badge badge-modern bg-${statusClass}">${order.status || 'غير محدد'}</span>
            </td>
            <td>
                <span class="badge badge-modern bg-${usageClass}">${order.is_used ? 'مُستخدم' : 'غير مُستخدم'}</span>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <a href="/purchase-orders/view/${order.id}" class="btn btn-outline-info btn-sm" title="عرض">
                        <i class="fas fa-eye"></i>
                    </a>
                    <a href="/purchase-orders/edit/${order.id}" class="btn btn-outline-warning btn-sm" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </a>
                    <button onclick="deletePO(${order.id})" class="btn btn-outline-danger btn-sm" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;

        tbody.appendChild(row);
    });
}

// تحديث الإحصائيات (مرتبطة بالفلترة ومتعددة العملات)
function updateStatistics() {
    console.log('📊 تحديث الإحصائيات متعددة العملات...');

    // عرض مؤشر التحميل
    showStatsLoading(true);

    // حساب الإحصائيات حسب العملة
    const currencyStats = {};
    let totalOrders = 0;
    let confirmedOrders = 0;
    let usedOrders = 0;

    purchaseOrdersData.forEach(order => {
        totalOrders++;

        if (order.status === 'مؤكد' || order.status === 'confirmed') {
            confirmedOrders++;
        }

        if (order.is_used) {
            usedOrders++;
        }

        const currency = order.currency || 'CNY';
        if (!currencyStats[currency]) {
            currencyStats[currency] = {
                total_value: 0,
                count: 0
            };
        }

        currencyStats[currency].total_value += (order.total_amount || 0);
        currencyStats[currency].count++;
    });

    console.log('💰 إحصائيات العملات:', currencyStats);

    // عرض الإحصائيات متعددة العملات
    displayMultiCurrencyStats(currencyStats);

    // إجمالي عدد الأوامر
    document.getElementById('totalOrders').textContent = totalOrders;
    document.getElementById('confirmedOrders').textContent = confirmedOrders;
    document.getElementById('usedOrders').textContent = usedOrders;

    // إخفاء مؤشر التحميل
    showStatsLoading(false);
}

// عرض الإحصائيات متعددة العملات
function displayMultiCurrencyStats(currencyStats) {
    const currencies = Object.keys(currencyStats).sort();

    // إذا لم توجد عملات، عرض رسالة
    if (currencies.length === 0) {
        const noDataMessage = '<div class="no-data">لا توجد بيانات مطابقة للفلترة</div>';
        document.getElementById('totalValue').innerHTML = noDataMessage;
        return;
    }

    // تحديث بطاقة القيمة الإجمالية
    const valueContent = currencies.map(currency => {
        const stats = currencyStats[currency];
        if (stats.total_value > 0) {
            return `<div class="currency-line">
                <span class="currency-code">${currency}</span>
                <span class="currency-amount">${formatNumber(stats.total_value)}</span>
            </div>`;
        }
        return '';
    }).filter(line => line).join('');

    document.getElementById('totalValue').innerHTML = valueContent ||
        '<div class="no-data">لا توجد قيم</div>';
}

// عرض/إخفاء مؤشر تحميل الإحصائيات
function showStatsLoading(show) {
    const loadingHtml = '<div class="spinner-border spinner-border-sm" role="status"></div>';
    const elements = ['totalValue'];

    elements.forEach(id => {
        const element = document.getElementById(id);
        if (show && element) {
            element.innerHTML = loadingHtml;
        }
    });
}

// عرض تفاصيل الإحصائيات
function showStatisticsDetails() {
    const currencyStats = {};

    purchaseOrdersData.forEach(order => {
        const currency = order.currency || 'CNY';
        if (!currencyStats[currency]) {
            currencyStats[currency] = {
                total_value: 0,
                count: 0,
                confirmed: 0,
                used: 0
            };
        }

        currencyStats[currency].total_value += (order.total_amount || 0);
        currencyStats[currency].count++;

        if (order.status === 'مؤكد' || order.status === 'confirmed') {
            currencyStats[currency].confirmed++;
        }

        if (order.is_used) {
            currencyStats[currency].used++;
        }
    });

    let detailsHtml = '<div class="statistics-details">';
    detailsHtml += '<h5 class="mb-3">تفاصيل الإحصائيات حسب العملة</h5>';

    Object.keys(currencyStats).sort().forEach(currency => {
        const stats = currencyStats[currency];

        detailsHtml += `
            <div class="currency-detail-card mb-3">
                <h6 class="currency-header">${currency}</h6>
                <div class="row">
                    <div class="col-md-3">
                        <small class="text-muted">إجمالي القيمة</small>
                        <div class="fw-bold text-success">${formatNumber(stats.total_value)}</div>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">عدد الأوامر</small>
                        <div class="fw-bold text-info">${stats.count}</div>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">المؤكدة</small>
                        <div class="fw-bold text-warning">${stats.confirmed}</div>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">المُستخدمة</small>
                        <div class="fw-bold text-danger">${stats.used}</div>
                    </div>
                </div>
            </div>
        `;
    });

    detailsHtml += '</div>';

    // عرض في modal
    const modal = new bootstrap.Modal(document.getElementById('statisticsModal'));
    document.getElementById('statisticsModalBody').innerHTML = detailsHtml;
    modal.show();
}

// دوال مساعدة
function getStatusBadgeClass(status) {
    switch(status) {
        case 'مسودة': return 'secondary';
        case 'مرسل': return 'info';
        case 'مؤكد': return 'success';
        case 'ملغي': return 'danger';
        default: return 'secondary';
    }
}

function formatNumber(num) {
    return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(num);
}

function showLoading(show) {
    // يمكن إضافة مؤشر تحميل عام هنا
}

function showAlert(message, type) {
    // يمكن إضافة نظام تنبيهات هنا
    console.log(`${type.toUpperCase()}: ${message}`);
}

function exportToExcel() {
    // يمكن إضافة وظيفة التصدير هنا
    console.log('تصدير إلى Excel...');
}

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadData();
});
</script>

<!-- Modal تفاصيل الإحصائيات -->
<div class="modal fade" id="statisticsModal" tabindex="-1" aria-labelledby="statisticsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statisticsModalLabel">تفاصيل الإحصائيات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="statisticsModalBody">
                <!-- سيتم ملء المحتوى ديناميكياً -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- 🛡️ ملف JavaScript لنظام الحماية -->
<script src="{{ url_for('static', filename='js/po_protection.js') }}"></script>
{% endblock %}
