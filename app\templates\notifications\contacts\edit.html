{% extends "base.html" %}

{% block title %}تعديل جهة الاتصال{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: white;
        border-radius: 0.5rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }
    
    .section-title {
        color: #5a5c69;
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #e3e6f0;
    }
    
    .required-field::after {
        content: " *";
        color: #e74a3b;
    }
    
    .form-check-group {
        background: #f8f9fc;
        padding: 1rem;
        border-radius: 0.35rem;
        border: 1px solid #e3e6f0;
    }
    
    .priority-indicator {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: inline-block;
        margin-left: 0.5rem;
    }
    
    .priority-high { background-color: #e74a3b; }
    .priority-medium { background-color: #f39c12; }
    .priority-low { background-color: #28a745; }
    
    .contact-info-card {
        background: linear-gradient(45deg, #4e73df, #224abe);
        color: white;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Loading Spinner -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-2">جاري تحميل بيانات جهة الاتصال...</p>
    </div>

    <!-- Main Content -->
    <div id="mainContent" style="display: none;">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="fas fa-user-edit text-warning me-2"></i>
                            تعديل جهة الاتصال
                        </h1>
                        <p class="text-muted mb-0">تعديل بيانات جهة الاتصال في نظام الإشعارات</p>
                    </div>
                    <div>
                        <a href="/notifications/contacts/" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Info Card -->
        <div class="contact-info-card">
            <div class="d-flex align-items-center">
                <div class="contact-avatar me-3" style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; font-weight: bold;">
                    <span id="contactInitial">N</span>
                </div>
                <div>
                    <h4 class="mb-1" id="contactNameDisplay">جهة الاتصال</h4>
                    <p class="mb-0 opacity-75" id="contactTypeDisplay">نوع جهة الاتصال</p>
                </div>
            </div>
        </div>

        <!-- Form -->
        <form id="editContactForm" onsubmit="updateContact(event)">
            <input type="hidden" id="contactId" name="contact_id">
            
            <!-- معلومات أساسية -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-user me-2"></i>
                    المعلومات الأساسية
                </h4>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">اسم جهة الاتصال</label>
                            <input type="text" class="form-control" name="contact_name" id="contact_name" required 
                                   placeholder="مثال: أحمد محمد - مدير العمليات">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">نوع جهة الاتصال</label>
                            <select class="form-select" name="contact_type" id="contact_type">
                                <option value="CUSTOMER">عميل</option>
                                <option value="DRIVER">سائق</option>
                                <option value="AGENT">مخلص جمركي</option>
                                <option value="MANAGER">مدير</option>
                                <option value="EXTERNAL">خارجي</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الشركة</label>
                            <input type="text" class="form-control" name="company_name" id="company_name" 
                                   placeholder="اسم الشركة أو المؤسسة">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">المنصب</label>
                            <input type="text" class="form-control" name="position" id="position" 
                                   placeholder="المنصب أو الوظيفة">
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات الاتصال -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-phone me-2"></i>
                    معلومات الاتصال
                </h4>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" name="phone_number" id="phone_number" 
                                   placeholder="+966xxxxxxxxx">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email_address" id="email_address" 
                                   placeholder="<EMAIL>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">رقم WhatsApp</label>
                            <input type="tel" class="form-control" name="whatsapp_number" id="whatsapp_number" 
                                   placeholder="+966xxxxxxxxx">
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات الإشعارات -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-bell me-2"></i>
                    إعدادات الإشعارات
                </h4>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">القنوات المفضلة</label>
                            <div class="form-check-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="channels" value="SMS" id="channel_sms">
                                    <label class="form-check-label" for="channel_sms">
                                        <i class="fas fa-sms text-primary me-2"></i>
                                        الرسائل النصية (SMS)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="channels" value="EMAIL" id="channel_email">
                                    <label class="form-check-label" for="channel_email">
                                        <i class="fas fa-envelope text-info me-2"></i>
                                        البريد الإلكتروني
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="channels" value="WHATSAPP" id="channel_whatsapp">
                                    <label class="form-check-label" for="channel_whatsapp">
                                        <i class="fab fa-whatsapp text-success me-2"></i>
                                        واتساب
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="channels" value="PUSH" id="channel_push">
                                    <label class="form-check-label" for="channel_push">
                                        <i class="fas fa-bell text-warning me-2"></i>
                                        الإشعارات المباشرة
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">مستوى الأولوية</label>
                            <select class="form-select" name="priority_level" id="priority_level" onchange="updatePriorityIndicator(this.value)">
                                <option value="10">عالية جداً (10)</option>
                                <option value="8">عالية (8)</option>
                                <option value="5">متوسطة (5)</option>
                                <option value="3">منخفضة (3)</option>
                                <option value="1">منخفضة جداً (1)</option>
                            </select>
                            <div class="mt-2">
                                <span class="priority-indicator priority-medium" id="priorityIndicator"></span>
                                <small class="text-muted ms-2">مؤشر الأولوية</small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_vip" id="is_vip">
                                <label class="form-check-label" for="is_vip">
                                    <i class="fas fa-star text-warning me-2"></i>
                                    جهة اتصال مميزة (VIP)
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    جهة اتصال نشطة
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ملاحظات -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-sticky-note me-2"></i>
                    ملاحظات إضافية
                </h4>
                
                <div class="mb-3">
                    <label class="form-label">ملاحظات</label>
                    <textarea class="form-control" name="notes" id="notes" rows="4" 
                              placeholder="أي ملاحظات أو معلومات إضافية عن جهة الاتصال..."></textarea>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="form-section">
                <div class="d-flex justify-content-between">
                    <a href="/notifications/contacts/" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>
                        إلغاء
                    </a>
                    <div>
                        <button type="button" class="btn btn-outline-danger me-2" onclick="deleteContact()">
                            <i class="fas fa-trash me-2"></i>
                            حذف جهة الاتصال
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
let contactId = null;

// تحميل بيانات جهة الاتصال
function loadContactData() {
    // الحصول على معرف جهة الاتصال من URL
    const urlParams = new URLSearchParams(window.location.search);
    contactId = urlParams.get('id');
    
    if (!contactId) {
        alert('❌ معرف جهة الاتصال مفقود');
        window.location.href = '/notifications/contacts/';
        return;
    }
    
    document.getElementById('contactId').value = contactId;
    
    // عرض spinner التحميل
    document.getElementById('loadingSpinner').style.display = 'block';
    document.getElementById('mainContent').style.display = 'none';
    
    // جلب بيانات جهة الاتصال
    fetch(`/notifications/contacts/api/contacts/${contactId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.contact) {
            populateForm(data.contact);
            document.getElementById('loadingSpinner').style.display = 'none';
            document.getElementById('mainContent').style.display = 'block';
        } else {
            alert('❌ لم يتم العثور على جهة الاتصال');
            window.location.href = '/notifications/contacts/';
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('❌ حدث خطأ في تحميل بيانات جهة الاتصال');
        window.location.href = '/notifications/contacts/';
    });
}

// ملء النموذج بالبيانات
function populateForm(contact) {
    // تحديث معلومات العرض
    document.getElementById('contactInitial').textContent = contact.contact_name ? contact.contact_name[0].toUpperCase() : 'N';
    document.getElementById('contactNameDisplay').textContent = contact.contact_name || 'جهة الاتصال';
    
    const typeNames = {
        'CUSTOMER': 'عميل',
        'DRIVER': 'سائق', 
        'AGENT': 'مخلص جمركي',
        'MANAGER': 'مدير',
        'EXTERNAL': 'خارجي'
    };
    document.getElementById('contactTypeDisplay').textContent = typeNames[contact.contact_type] || contact.contact_type;
    
    // ملء الحقول
    document.getElementById('contact_name').value = contact.contact_name || '';
    document.getElementById('contact_type').value = contact.contact_type || 'EXTERNAL';
    document.getElementById('company_name').value = contact.company_name || '';
    document.getElementById('position').value = contact.position || '';
    document.getElementById('phone_number').value = contact.phone_number || '';
    document.getElementById('email_address').value = contact.email_address || '';
    document.getElementById('whatsapp_number').value = contact.whatsapp_number || '';
    document.getElementById('priority_level').value = contact.priority_level || 5;
    document.getElementById('is_vip').checked = contact.is_vip || false;
    document.getElementById('is_active').checked = contact.is_active !== false;
    document.getElementById('notes').value = contact.notes || '';
    
    // ملء القنوات المفضلة
    const channels = contact.preferred_channels ? contact.preferred_channels.split(',') : [];
    document.querySelectorAll('input[name="channels"]').forEach(checkbox => {
        checkbox.checked = channels.includes(checkbox.value);
    });
    
    // تحديث مؤشر الأولوية
    updatePriorityIndicator(contact.priority_level || 5);
}

// تحديث مؤشر الأولوية
function updatePriorityIndicator(priority) {
    const indicator = document.getElementById('priorityIndicator');
    if (indicator) {
        indicator.className = 'priority-indicator ';
        if (priority >= 8) {
            indicator.className += 'priority-high';
        } else if (priority >= 5) {
            indicator.className += 'priority-medium';
        } else {
            indicator.className += 'priority-low';
        }
    }
}

// تحديث جهة الاتصال
function updateContact(event) {
    event.preventDefault();
    
    const form = document.getElementById('editContactForm');
    const formData = new FormData(form);
    
    // جمع القنوات المختارة
    const selectedChannels = [];
    document.querySelectorAll('input[name="channels"]:checked').forEach(checkbox => {
        selectedChannels.push(checkbox.value);
    });
    
    const contactData = {
        contact_name: formData.get('contact_name'),
        contact_type: formData.get('contact_type'),
        phone_number: formData.get('phone_number') || null,
        email_address: formData.get('email_address') || null,
        whatsapp_number: formData.get('whatsapp_number') || null,
        company_name: formData.get('company_name') || null,
        position: formData.get('position') || null,
        preferred_channels: selectedChannels.join(','),
        priority_level: parseInt(formData.get('priority_level')),
        is_vip: formData.get('is_vip') ? true : false,
        is_active: formData.get('is_active') ? true : false,
        notes: formData.get('notes') || null
    };
    
    // التحقق من البيانات المطلوبة
    if (!contactData.contact_name) {
        alert('يرجى إدخال اسم جهة الاتصال');
        return;
    }
    
    // إرسال البيانات
    fetch(`/notifications/contacts/api/contacts/${contactId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(contactData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ تم تحديث جهة الاتصال بنجاح!');
            window.location.href = '/notifications/contacts/';
        } else {
            alert('❌ خطأ: ' + (data.message || 'فشل في تحديث جهة الاتصال'));
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('❌ حدث خطأ في تحديث جهة الاتصال');
    });
}

// حذف جهة الاتصال
function deleteContact() {
    const contactName = document.getElementById('contact_name').value;
    if (confirm(`هل أنت متأكد من حذف جهة الاتصال "${contactName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        fetch(`/notifications/contacts/api/contacts/${contactId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ تم حذف جهة الاتصال بنجاح!');
                window.location.href = '/notifications/contacts/';
            } else {
                alert('❌ خطأ: ' + (data.message || 'فشل في حذف جهة الاتصال'));
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('❌ حدث خطأ في حذف جهة الاتصال');
        });
    }
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadContactData();
});
</script>
{% endblock %}
