<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة وثائق طلب الحوالة - {{ transfer_request.request_number }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        
        .container-fluid {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content-section {
            padding: 30px;
        }
        
        .request-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #3498db;
        }
        
        .stats-row {
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-top: 4px solid #3498db;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        
        .upload-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .documents-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .document-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #28a745;
            transition: all 0.3s ease;
        }
        
        .document-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .document-type-badge {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 3px 10px;
            border-radius: 12px;
            font-size: 0.75em;
            font-weight: 500;
            white-space: nowrap;
            box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
            transition: all 0.2s ease;
        }

        .document-type-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(52, 152, 219, 0.4);
        }

        /* ألوان مختلفة لأنواع الوثائق */
        .document-type-badge.identity-document {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .document-type-badge.passport {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .document-type-badge.bank-statement {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .document-type-badge.salary-certificate {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }

        .document-type-badge.employment-letter {
            background: linear-gradient(135deg, #34495e, #2c3e50);
        }

        .document-type-badge.other {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
        }
        
        .upload-area {
            border: 2px dashed #3498db;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            background: #e3f2fd;
            border-color: #2980b9;
        }
        
        .upload-area.dragover {
            background: #e3f2fd;
            border-color: #2980b9;
            transform: scale(1.02);
        }
        
        .btn-upload {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-upload:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
            color: white;
        }
        
        .btn-download {
            background: linear-gradient(135deg, #17a2b8, #138496);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85em;
            transition: all 0.3s ease;
        }
        
        .btn-download:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4);
            color: white;
        }
        
        .btn-delete {
            background: linear-gradient(135deg, #dc3545, #c82333);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85em;
            transition: all 0.3s ease;
        }
        
        .btn-delete:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
            color: white;
        }
        
        .no-documents {
            text-align: center;
            padding: 60px;
            color: #7f8c8d;
        }
        
        .progress-upload {
            display: none;
            margin-top: 15px;
        }

        /* تحسين أزرار الإجراءات */
        .btn-group-sm .btn {
            margin: 0;
            transition: all 0.2s ease;
            border-radius: 0.25rem !important;
        }

        .btn-group-sm .btn:first-child {
            border-top-left-radius: 0.375rem !important;
            border-bottom-left-radius: 0.375rem !important;
        }

        .btn-group-sm .btn:last-child {
            border-top-right-radius: 0.375rem !important;
            border-bottom-right-radius: 0.375rem !important;
        }

        .btn-group-sm .btn:hover {
            transform: translateY(-1px);
            z-index: 2;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        /* تحسين شكل الوثائق */
        .document-row {
            transition: all 0.3s ease;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }

        .document-row:hover {
            background: #ffffff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        /* أيقونات الملفات */
        .file-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .file-icon.pdf { color: #dc3545; }
        .file-icon.doc { color: #007bff; }
        .file-icon.xls { color: #28a745; }
        .file-icon.img { color: #ffc107; }
        .file-icon.default { color: #6c757d; }

        /* تحسين الرسائل المنبثقة */
        .alert.position-fixed {
            animation: slideInRight 0.3s ease-out;
            transition: all 0.3s ease;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header Section -->
        <div class="header-section">
            <h1 class="mb-3">
                <i class="fas fa-file-alt me-3"></i>
                إدارة وثائق طلب الحوالة
            </h1>
            <p class="lead mb-0">
                رقم الطلب: {{ transfer_request.request_number }}
            </p>
        </div>
        
        <!-- Content Section -->
        <div class="content-section">
            <!-- معلومات طلب الحوالة -->
            <div class="request-info">
                <div class="row">
                    <div class="col-md-3">
                        <strong>رقم الطلب:</strong><br>
                        <span class="text-primary">{{ transfer_request.request_number }}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>الحالة:</strong><br>
                        {% if transfer_request.status == 'pending' %}
                            <span class="badge bg-warning">معلق</span>
                        {% elif transfer_request.status == 'approved' %}
                            <span class="badge bg-success">موافق عليه</span>
                        {% elif transfer_request.status == 'rejected' %}
                            <span class="badge bg-danger">مرفوض</span>
                        {% else %}
                            <span class="badge bg-secondary">{{ transfer_request.status }}</span>
                        {% endif %}
                    </div>
                    <div class="col-md-3">
                        <strong>المبلغ:</strong><br>
                        <span class="text-success fw-bold">{{ "{:,.2f}".format(transfer_request.amount) }} {{ transfer_request.currency }}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>المستفيد:</strong><br>
                        {{ transfer_request.beneficiary_name }}
                    </div>
                </div>
            </div>
            
            <!-- إحصائيات الوثائق -->
            <div class="row stats-row">
                <div class="col-md-6">
                    <div class="stat-card">
                        <div class="stat-number">{{ stats.total_documents }}</div>
                        <div class="stat-label">إجمالي الوثائق</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="stat-card">
                        <div class="stat-number">{{ stats.total_size_mb }}</div>
                        <div class="stat-label">الحجم (MB)</div>
                    </div>
                </div>
            </div>

            <!-- قسم رفع الوثائق -->
            <div class="upload-section">
                <h5 class="mb-4">
                    <i class="fas fa-cloud-upload-alt me-2"></i>
                    رفع وثيقة جديدة
                </h5>

                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">نوع الوثيقة:</label>
                            <select class="form-select" name="document_type" required>
                                <option value="">اختر نوع الوثيقة</option>
                                {% for key, value in document_types.items() %}
                                <option value="{{ key }}">{{ value }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">اسم الوثيقة:</label>
                            <input type="text" class="form-control" name="document_name"
                                   placeholder="اسم الوثيقة (اختياري)">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">ملاحظات:</label>
                            <input type="text" class="form-control" name="notes"
                                   placeholder="ملاحظات (اختياري)">
                        </div>
                    </div>

                    <div class="upload-area mt-3" id="uploadArea">
                        <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                        <h5>اسحب الملفات هنا أو انقر للاختيار</h5>
                        <p class="text-muted">الأنواع المدعومة: PDF, DOC, XLS, JPG, PNG, ZIP, RAR (حد أقصى 10 MB لكل ملف)</p>
                        <input type="file" id="fileInput" name="document_file"
                               accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.tiff,.txt,.csv,.zip,.rar"
                               style="display: none;" required>
                        <button type="button" class="btn btn-upload" onclick="document.getElementById('fileInput').click()">
                            <i class="fas fa-folder-open me-2"></i>
                            اختيار ملف
                        </button>
                        <div id="selectedFile" class="mt-3" style="display: none;">
                            <h6 class="text-success mb-2">
                                <i class="fas fa-check-circle me-1"></i>
                                الملف المختار:
                            </h6>
                            <div id="fileInfo" class="alert alert-info"></div>
                        </div>
                    </div>

                    <div class="progress-upload">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted">جاري الرفع...</small>
                    </div>

                    <div class="text-center mt-3">
                        <button type="submit" class="btn btn-upload btn-lg" id="uploadButton">
                            <i class="fas fa-upload me-2"></i>
                            رفع الوثيقة
                        </button>
                    </div>
                </form>
            </div>

            <!-- قسم الوثائق -->
            <div class="documents-section">
                <h5 class="mb-4">
                    <i class="fas fa-folder-open me-2"></i>
                    الوثائق المرفوعة
                </h5>

                <div id="documentsContainer">
                    {% if documents %}
                        {% for doc in documents %}
                        <div class="document-card document-row" data-doc-id="{{ doc.id }}">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file-alt fa-2x text-primary me-3"></i>
                                        <div>
                                            <div class="d-flex align-items-center gap-2 mb-1">
                                                <h6 class="mb-0">{{ doc.document_name or doc.file_name }}</h6>
                                                <span class="document-type-badge {{ doc.document_type|lower|replace('_', '-') }}">
                                                    {{ doc.document_type_name }}
                                                </span>
                                            </div>
                                            <small class="text-muted">{{ doc.file_name }}</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">
                                        <i class="fas fa-weight-hanging me-1"></i>
                                        {{ doc.file_size_mb }} MB
                                    </small><br>
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        {{ doc.uploaded_by }}
                                    </small><br>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ doc.uploaded_at if doc.uploaded_at else '' }}
                                    </small>
                                </div>
                                <div class="col-md-3 text-end">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <!-- أزرار الاستعراض والتحميل -->
                                        <button class="btn btn-outline-primary"
                                                onclick="previewDocument({{ doc.id }}, '{{ doc.file_name }}', '{{ doc.mime_type or '' }}')"
                                                title="استعراض الوثيقة">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <a href="{{ url_for('transfers.download_transfer_document', document_id=doc.id) }}"
                                           class="btn btn-outline-info" title="تحميل الوثيقة">
                                            <i class="fas fa-download"></i>
                                        </a>

                                        <!-- أزرار إنشاء الروابط -->
                                        <button class="btn btn-success btn-sm"
                                                onclick="createShareLink({{ doc.id }}, 'nextcloud')"
                                                title="إنشاء رابط Nextcloud">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.232 7.005c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zm7.553 0c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zM12.017 18.55c-2.69 0-4.923-1.273-6.096-3.234l1.431-.716c.79 1.431 2.334 2.39 4.665 2.39s3.875-.959 4.665-2.39l1.431.716c-1.173 1.961-3.406 3.234-6.096 3.234z"/>
                                            </svg>
                                            {% if doc.nextcloud_share_link %}
                                                <span class="badge bg-success rounded-pill ms-1">●</span>
                                            {% endif %}
                                        </button>

                                        <button class="btn btn-primary btn-sm"
                                                onclick="createShareLink({{ doc.id }}, 'onedrive')"
                                                title="إنشاء رابط OneDrive">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M8.9 2.9c-1.6 0-2.9 1.3-2.9 2.9 0 .2 0 .4.1.6C3.6 7.2 1.5 9.7 1.5 12.8c0 3.5 2.8 6.3 6.3 6.3h9.4c2.9 0 5.3-2.4 5.3-5.3 0-2.5-1.7-4.6-4-5.2-.1-3.1-2.7-5.7-5.8-5.7-1.9 0-3.6.9-4.6 2.3-.4-.2-.8-.3-1.2-.3zm0 1.5c.3 0 .6.1.8.2l.4.2.2-.4c.7-1.3 2.1-2.1 3.6-2.1 2.3 0 4.2 1.9 4.2 4.2v.8h.8c2.1 0 3.8 1.7 3.8 3.8s-1.7 3.8-3.8 3.8H7.8c-2.7 0-4.8-2.2-4.8-4.8 0-2.4 1.7-4.4 4-4.7l.5-.1-.1-.5c0-.2-.1-.4-.1-.6 0-.8.6-1.4 1.4-1.4z"/>
                                            </svg>
                                            {% if doc.onedrive_share_link %}
                                                <span class="badge bg-primary rounded-pill ms-1">●</span>
                                            {% endif %}
                                        </button>

                                        <!-- قائمة النسخ -->
                                        <div class="btn-group">
                                            <button class="btn btn-warning btn-sm dropdown-toggle"
                                                    data-bs-toggle="dropdown"
                                                    title="نسخ الروابط"
                                                    {% if not (doc.nextcloud_share_link or doc.onedrive_share_link) %}disabled{% endif %}>
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                {% if doc.nextcloud_share_link %}
                                                <li><a class="dropdown-item" onclick="copyShareLink('{{ doc.nextcloud_share_link }}')">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="#198754" class="me-2">
                                                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.232 7.005c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zm7.553 0c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zM12.017 18.55c-2.69 0-4.923-1.273-6.096-3.234l1.431-.716c.79 1.431 2.334 2.39 4.665 2.39s3.875-.959 4.665-2.39l1.431.716c-1.173 1.961-3.406 3.234-6.096 3.234z"/>
                                                    </svg>
                                                    نسخ رابط Nextcloud
                                                </a></li>
                                                {% endif %}
                                                {% if doc.onedrive_share_link %}
                                                <li><a class="dropdown-item" onclick="copyShareLink('{{ doc.onedrive_share_link }}')">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="#0d6efd" class="me-2">
                                                        <path d="M8.9 2.9c-1.6 0-2.9 1.3-2.9 2.9 0 .2 0 .4.1.6C3.6 7.2 1.5 9.7 1.5 12.8c0 3.5 2.8 6.3 6.3 6.3h9.4c2.9 0 5.3-2.4 5.3-5.3 0-2.5-1.7-4.6-4-5.2-.1-3.1-2.7-5.7-5.8-5.7-1.9 0-3.6.9-4.6 2.3-.4-.2-.8-.3-1.2-.3zm0 1.5c.3 0 .6.1.8.2l.4.2.2-.4c.7-1.3 2.1-2.1 3.6-2.1 2.3 0 4.2 1.9 4.2 4.2v.8h.8c2.1 0 3.8 1.7 3.8 3.8s-1.7 3.8-3.8 3.8H7.8c-2.7 0-4.8-2.2-4.8-4.8 0-2.4 1.7-4.4 4-4.7l.5-.1-.1-.5c0-.2-.1-.4-.1-.6 0-.8.6-1.4 1.4-1.4z"/>
                                                    </svg>
                                                    نسخ رابط OneDrive
                                                </a></li>
                                                {% endif %}
                                                {% if not (doc.nextcloud_share_link or doc.onedrive_share_link) %}
                                                <li><span class="dropdown-item-text text-muted">لا توجد روابط متاحة</span></li>
                                                {% endif %}
                                            </ul>
                                        </div>

                                        <!-- قائمة الفتح -->
                                        <div class="btn-group">
                                            <button class="btn btn-secondary btn-sm dropdown-toggle"
                                                    data-bs-toggle="dropdown"
                                                    title="فتح الروابط"
                                                    {% if not (doc.nextcloud_share_link or doc.onedrive_share_link) %}disabled{% endif %}>
                                                <i class="fas fa-external-link-alt"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                {% if doc.nextcloud_share_link %}
                                                <li><a class="dropdown-item" onclick="openShareLink('{{ doc.nextcloud_share_link }}')">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="#198754" class="me-2">
                                                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.232 7.005c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zm7.553 0c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zM12.017 18.55c-2.69 0-4.923-1.273-6.096-3.234l1.431-.716c.79 1.431 2.334 2.39 4.665 2.39s3.875-.959 4.665-2.39l1.431.716c-1.173 1.961-3.406 3.234-6.096 3.234z"/>
                                                    </svg>
                                                    فتح رابط Nextcloud
                                                </a></li>
                                                {% endif %}
                                                {% if doc.onedrive_share_link %}
                                                <li><a class="dropdown-item" onclick="openShareLink('{{ doc.onedrive_share_link }}')">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="#0d6efd" class="me-2">
                                                        <path d="M8.9 2.9c-1.6 0-2.9 1.3-2.9 2.9 0 .2 0 .4.1.6C3.6 7.2 1.5 9.7 1.5 12.8c0 3.5 2.8 6.3 6.3 6.3h9.4c2.9 0 5.3-2.4 5.3-5.3 0-2.5-1.7-4.6-4-5.2-.1-3.1-2.7-5.7-5.8-5.7-1.9 0-3.6.9-4.6 2.3-.4-.2-.8-.3-1.2-.3zm0 1.5c.3 0 .6.1.8.2l.4.2.2-.4c.7-1.3 2.1-2.1 3.6-2.1 2.3 0 4.2 1.9 4.2 4.2v.8h.8c2.1 0 3.8 1.7 3.8 3.8s-1.7 3.8-3.8 3.8H7.8c-2.7 0-4.8-2.2-4.8-4.8 0-2.4 1.7-4.4 4-4.7l.5-.1-.1-.5c0-.2-.1-.4-.1-.6 0-.8.6-1.4 1.4-1.4z"/>
                                                    </svg>
                                                    فتح رابط OneDrive
                                                </a></li>
                                                {% endif %}
                                                {% if not (doc.nextcloud_share_link or doc.onedrive_share_link) %}
                                                <li><span class="dropdown-item-text text-muted">لا توجد روابط متاحة</span></li>
                                                {% endif %}
                                            </ul>
                                        </div>

                                        <!-- زر الحذف -->
                                        <button class="btn btn-outline-danger"
                                                onclick="deleteDocument({{ doc.id }})"
                                                title="حذف الوثيقة">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {% if doc.notes and doc.notes != 'None' %}
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-sticky-note me-1"></i>
                                    {{ doc.notes }}
                                </small>
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="no-documents">
                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                            <h5>لا توجد وثائق مرفوعة</h5>
                            <p>ابدأ برفع الوثائق المطلوبة لطلب الحوالة</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- أزرار التنقل -->
            <div class="text-center mt-4">
                <a href="{{ url_for('transfers.list_requests') }}" class="btn btn-secondary btn-lg me-3">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة لقائمة الطلبات
                </a>
                <a href="{{ url_for('transfers.view_request', request_id=transfer_request.id) }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-eye me-2"></i>
                    عرض تفاصيل الطلب
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تم تحميل صفحة إدارة وثائق طلب الحوالة');

            const requestId = {{ transfer_request.id }};
            const fileInput = document.getElementById('fileInput');
            const uploadForm = document.getElementById('uploadForm');
            const selectedFile = document.getElementById('selectedFile');
            const fileInfo = document.getElementById('fileInfo');
            const uploadButton = document.getElementById('uploadButton');

            // عرض معلومات الملف المختار
            fileInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const file = this.files[0];
                    const fileSizeMB = (file.size / 1024 / 1024).toFixed(2);

                    // تحديد أيقونة الملف
                    const extension = file.name.split('.').pop().toLowerCase();
                    let icon = 'fas fa-file';
                    if (['pdf'].includes(extension)) icon = 'fas fa-file-pdf text-danger';
                    else if (['doc', 'docx'].includes(extension)) icon = 'fas fa-file-word text-primary';
                    else if (['xls', 'xlsx'].includes(extension)) icon = 'fas fa-file-excel text-success';
                    else if (['jpg', 'jpeg', 'png', 'gif', 'tiff'].includes(extension)) icon = 'fas fa-file-image text-info';
                    else if (['zip', 'rar'].includes(extension)) icon = 'fas fa-file-archive text-warning';

                    fileInfo.innerHTML = `
                        <div class="d-flex align-items-center">
                            <i class="${icon} fa-2x me-3"></i>
                            <div>
                                <div class="fw-bold">${file.name}</div>
                                <small class="text-muted">${fileSizeMB} MB</small>
                            </div>
                        </div>
                    `;

                    selectedFile.style.display = 'block';
                    uploadButton.innerHTML = `
                        <i class="fas fa-upload me-2"></i>
                        رفع ${file.name}
                    `;
                } else {
                    selectedFile.style.display = 'none';
                    uploadButton.innerHTML = `
                        <i class="fas fa-upload me-2"></i>
                        رفع الوثيقة
                    `;
                }
            });

            // رفع الوثيقة
            uploadForm.addEventListener('submit', function(e) {
                e.preventDefault();

                console.log('📤 بدء عملية رفع الوثيقة...');

                const documentType = document.querySelector('select[name="document_type"]');
                const documentName = document.querySelector('input[name="document_name"]');
                const notes = document.querySelector('input[name="notes"]');

                console.log('📋 بيانات النموذج:', {
                    documentType: documentType.value,
                    documentName: documentName.value,
                    notes: notes.value,
                    fileSelected: fileInput.files && fileInput.files.length > 0
                });

                if (!documentType.value) {
                    alert('يرجى اختيار نوع الوثيقة');
                    return;
                }

                if (!fileInput.files || fileInput.files.length === 0) {
                    alert('يرجى اختيار ملف للرفع');
                    return;
                }

                const formData = new FormData();
                formData.append('document_type', documentType.value);
                formData.append('document_name', documentName.value);
                formData.append('notes', notes.value);
                formData.append('document_file', fileInput.files[0]);

                console.log('📦 تحضير FormData للإرسال...');
                console.log('🎯 URL الهدف:', `/transfers/requests/${requestId}/documents/upload`);

                // إظهار شريط التقدم
                const progressBar = document.querySelector('.progress-upload');
                const progressBarInner = progressBar.querySelector('.progress-bar');
                progressBar.style.display = 'block';

                // تعطيل زر الرفع
                uploadButton.disabled = true;
                uploadButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الرفع...';

                console.log('🚀 إرسال الطلب...');

                fetch(`/transfers/requests/${requestId}/documents/upload`, {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    console.log('📊 استجابة الخادم:', response.status, response.statusText);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
                    }

                    // تحقق من نوع المحتوى
                    const contentType = response.headers.get('content-type');
                    console.log('📋 نوع المحتوى:', contentType);

                    if (!contentType || !contentType.includes('application/json')) {
                        throw new Error('الاستجابة ليست JSON صحيح');
                    }

                    return response.json();
                })
                .then(data => {
                    console.log('📋 بيانات الاستجابة:', data);

                    // إخفاء شريط التقدم
                    progressBar.style.display = 'none';

                    // إعادة تفعيل زر الرفع
                    uploadButton.disabled = false;
                    uploadButton.innerHTML = '<i class="fas fa-upload me-2"></i>رفع الوثيقة';

                    if (data && data.success) {
                        showToast('تم رفع الوثيقة بنجاح!', 'success', 'fas fa-check-circle');

                        // إعادة تعيين النموذج
                        uploadForm.reset();
                        selectedFile.style.display = 'none';

                        // إعادة تحميل الصفحة بعد ثانيتين لإظهار الوثيقة الجديدة
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    } else {
                        const errorMessage = data && data.message ? data.message : 'خطأ غير معروف';
                        showToast('خطأ في رفع الوثيقة: ' + errorMessage, 'danger', 'fas fa-exclamation-circle');
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ في رفع الوثيقة:', error);

                    // إخفاء شريط التقدم
                    progressBar.style.display = 'none';

                    // إعادة تفعيل زر الرفع
                    uploadButton.disabled = false;
                    uploadButton.innerHTML = '<i class="fas fa-upload me-2"></i>رفع الوثيقة';

                    // رسالة خطأ مفصلة
                    let errorMessage = 'حدث خطأ في رفع الوثيقة';
                    if (error.message) {
                        errorMessage += ': ' + error.message;
                    }

                    showToast(errorMessage, 'danger', 'fas fa-exclamation-triangle');
                });
            });
        });

        // إعادة تحميل قسم الوثائق
        function reloadDocumentsSection() {
            console.log('🔄 إعادة تحميل قسم الوثائق...');

            fetch(`/transfers/requests/${requestId}/documents/list`, {
                method: 'GET'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateDocumentsDisplay(data.documents);
                    console.log('✅ تم تحديث قسم الوثائق');
                } else {
                    console.error('❌ فشل في تحميل الوثائق:', data.message);
                }
            })
            .catch(error => {
                console.error('❌ خطأ في تحميل الوثائق:', error);
                // في حالة الخطأ، أعد تحميل الصفحة كاملة
                setTimeout(() => {
                    location.reload();
                }, 1000);
            });
        }

        // تحديث عرض الوثائق
        function updateDocumentsDisplay(documents) {
            const container = document.getElementById('documentsContainer');

            if (!documents || documents.length === 0) {
                container.innerHTML = `
                    <div class="no-documents">
                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                        <h5>لا توجد وثائق مرفوعة</h5>
                        <p>ابدأ برفع الوثائق المطلوبة لطلب الحوالة</p>
                    </div>
                `;
                return;
            }

            let html = '';
            documents.forEach(doc => {
                // التاريخ يأتي كـ string جاهز للعرض
                const uploadedAt = doc.uploaded_at || '';
                const notes = doc.notes && doc.notes !== 'None' ? doc.notes : '';

                html += `
                    <div class="document-card document-row" data-doc-id="${doc.id}">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file-alt fa-2x text-primary me-3"></i>
                                    <div>
                                        <div class="d-flex align-items-center gap-2 mb-1">
                                            <h6 class="mb-0">${doc.document_name || doc.file_name}</h6>
                                            <span class="document-type-badge ${doc.document_type.toLowerCase().replace('_', '-')}">
                                                ${doc.document_type_name}
                                            </span>
                                        </div>
                                        <small class="text-muted">${doc.file_name}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">
                                    <i class="fas fa-weight-hanging me-1"></i>
                                    ${doc.file_size_mb} MB
                                </small><br>
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i>
                                    ${doc.uploaded_by}
                                </small><br>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    ${uploadedAt}
                                </small>
                            </div>
                            <div class="col-md-3 text-end">
                                <div class="btn-group btn-group-sm" role="group">
                                    <!-- أزرار الاستعراض والتحميل -->
                                    <button class="btn btn-outline-primary"
                                            onclick="previewDocument(${doc.id}, '${doc.file_name}', '${doc.mime_type || ''}')"
                                            title="استعراض الوثيقة">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <a href="/transfers/documents/${doc.id}/download"
                                       class="btn btn-outline-info" title="تحميل الوثيقة">
                                        <i class="fas fa-download"></i>
                                    </a>

                                    <!-- أزرار إنشاء الروابط -->
                                    <button class="btn btn-success btn-sm"
                                            onclick="createShareLink(${doc.id}, 'nextcloud')"
                                            title="إنشاء رابط Nextcloud">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.232 7.005c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zm7.553 0c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zM12.017 18.55c-2.69 0-4.923-1.273-6.096-3.234l1.431-.716c.79 1.431 2.334 2.39 4.665 2.39s3.875-.959 4.665-2.39l1.431.716c-1.173 1.961-3.406 3.234-6.096 3.234z"/>
                                        </svg>
                                        ${doc.nextcloud_share_link ? '<span class="badge bg-success rounded-pill ms-1">●</span>' : ''}
                                    </button>

                                    <button class="btn btn-primary btn-sm"
                                            onclick="createShareLink(${doc.id}, 'onedrive')"
                                            title="إنشاء رابط OneDrive">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M8.9 2.9c-1.6 0-2.9 1.3-2.9 2.9 0 .2 0 .4.1.6C3.6 7.2 1.5 9.7 1.5 12.8c0 3.5 2.8 6.3 6.3 6.3h9.4c2.9 0 5.3-2.4 5.3-5.3 0-2.5-1.7-4.6-4-5.2-.1-3.1-2.7-5.7-5.8-5.7-1.9 0-3.6.9-4.6 2.3-.4-.2-.8-.3-1.2-.3zm0 1.5c.3 0 .6.1.8.2l.4.2.2-.4c.7-1.3 2.1-2.1 3.6-2.1 2.3 0 4.2 1.9 4.2 4.2v.8h.8c2.1 0 3.8 1.7 3.8 3.8s-1.7 3.8-3.8 3.8H7.8c-2.7 0-4.8-2.2-4.8-4.8 0-2.4 1.7-4.4 4-4.7l.5-.1-.1-.5c0-.2-.1-.4-.1-.6 0-.8.6-1.4 1.4-1.4z"/>
                                        </svg>
                                        ${doc.onedrive_share_link ? '<span class="badge bg-primary rounded-pill ms-1">●</span>' : ''}
                                    </button>

                                    <!-- قائمة النسخ -->
                                    <div class="btn-group">
                                        <button class="btn btn-warning btn-sm dropdown-toggle"
                                                data-bs-toggle="dropdown"
                                                title="نسخ الروابط"
                                                ${!(doc.nextcloud_share_link || doc.onedrive_share_link) ? 'disabled' : ''}>
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            ${doc.nextcloud_share_link ? `
                                            <li><a class="dropdown-item" onclick="copyShareLink('${doc.nextcloud_share_link}')">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="#198754" class="me-2">
                                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.232 7.005c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zm7.553 0c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zM12.017 18.55c-2.69 0-4.923-1.273-6.096-3.234l1.431-.716c.79 1.431 2.334 2.39 4.665 2.39s3.875-.959 4.665-2.39l1.431.716c-1.173 1.961-3.406 3.234-6.096 3.234z"/>
                                                </svg>
                                                نسخ رابط Nextcloud
                                            </a></li>` : ''}
                                            ${doc.onedrive_share_link ? `
                                            <li><a class="dropdown-item" onclick="copyShareLink('${doc.onedrive_share_link}')">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="#0d6efd" class="me-2">
                                                    <path d="M8.9 2.9c-1.6 0-2.9 1.3-2.9 2.9 0 .2 0 .4.1.6C3.6 7.2 1.5 9.7 1.5 12.8c0 3.5 2.8 6.3 6.3 6.3h9.4c2.9 0 5.3-2.4 5.3-5.3 0-2.5-1.7-4.6-4-5.2-.1-3.1-2.7-5.7-5.8-5.7-1.9 0-3.6.9-4.6 2.3-.4-.2-.8-.3-1.2-.3zm0 1.5c.3 0 .6.1.8.2l.4.2.2-.4c.7-1.3 2.1-2.1 3.6-2.1 2.3 0 4.2 1.9 4.2 4.2v.8h.8c2.1 0 3.8 1.7 3.8 3.8s-1.7 3.8-3.8 3.8H7.8c-2.7 0-4.8-2.2-4.8-4.8 0-2.4 1.7-4.4 4-4.7l.5-.1-.1-.5c0-.2-.1-.4-.1-.6 0-.8.6-1.4 1.4-1.4z"/>
                                                </svg>
                                                نسخ رابط OneDrive
                                            </a></li>` : ''}
                                            ${!(doc.nextcloud_share_link || doc.onedrive_share_link) ? `
                                            <li><span class="dropdown-item-text text-muted">لا توجد روابط متاحة</span></li>` : ''}
                                        </ul>
                                    </div>

                                    <!-- قائمة الفتح -->
                                    <div class="btn-group">
                                        <button class="btn btn-secondary btn-sm dropdown-toggle"
                                                data-bs-toggle="dropdown"
                                                title="فتح الروابط"
                                                ${!(doc.nextcloud_share_link || doc.onedrive_share_link) ? 'disabled' : ''}>
                                            <i class="fas fa-external-link-alt"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            ${doc.nextcloud_share_link ? `
                                            <li><a class="dropdown-item" onclick="openShareLink('${doc.nextcloud_share_link}')">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="#198754" class="me-2">
                                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.232 7.005c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zm7.553 0c.79 0 1.431.641 1.431 1.431s-.641 1.431-1.431 1.431-1.431-.641-1.431-1.431.641-1.431 1.431-1.431zM12.017 18.55c-2.69 0-4.923-1.273-6.096-3.234l1.431-.716c.79 1.431 2.334 2.39 4.665 2.39s3.875-.959 4.665-2.39l1.431.716c-1.173 1.961-3.406 3.234-6.096 3.234z"/>
                                                </svg>
                                                فتح رابط Nextcloud
                                            </a></li>` : ''}
                                            ${doc.onedrive_share_link ? `
                                            <li><a class="dropdown-item" onclick="openShareLink('${doc.onedrive_share_link}')">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="#0d6efd" class="me-2">
                                                    <path d="M8.9 2.9c-1.6 0-2.9 1.3-2.9 2.9 0 .2 0 .4.1.6C3.6 7.2 1.5 9.7 1.5 12.8c0 3.5 2.8 6.3 6.3 6.3h9.4c2.9 0 5.3-2.4 5.3-5.3 0-2.5-1.7-4.6-4-5.2-.1-3.1-2.7-5.7-5.8-5.7-1.9 0-3.6.9-4.6 2.3-.4-.2-.8-.3-1.2-.3zm0 1.5c.3 0 .6.1.8.2l.4.2.2-.4c.7-1.3 2.1-2.1 3.6-2.1 2.3 0 4.2 1.9 4.2 4.2v.8h.8c2.1 0 3.8 1.7 3.8 3.8s-1.7 3.8-3.8 3.8H7.8c-2.7 0-4.8-2.2-4.8-4.8 0-2.4 1.7-4.4 4-4.7l.5-.1-.1-.5c0-.2-.1-.4-.1-.6 0-.8.6-1.4 1.4-1.4z"/>
                                                </svg>
                                                فتح رابط OneDrive
                                            </a></li>` : ''}
                                            ${!(doc.nextcloud_share_link || doc.onedrive_share_link) ? `
                                            <li><span class="dropdown-item-text text-muted">لا توجد روابط متاحة</span></li>` : ''}
                                        </ul>
                                    </div>

                                    <!-- زر الحذف -->
                                    <button class="btn btn-outline-danger"
                                            onclick="deleteDocument(${doc.id})"
                                            title="حذف الوثيقة">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        ${notes ? `
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-sticky-note me-1"></i>
                                ${notes}
                            </small>
                        </div>
                        ` : ''}
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // تحديث الإحصائيات
        function updateDocumentStats() {
            fetch(`/transfers/requests/${requestId}/documents/stats`, {
                method: 'GET'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('📊 تحديث الإحصائيات:', data.stats);

                    // تحديث عدد الوثائق - البحث بطريقة أكثر دقة
                    const statsCards = document.querySelectorAll('.stat-card');
                    if (statsCards.length >= 2) {
                        // الكارت الأول - عدد الوثائق
                        const totalDocsElement = statsCards[0].querySelector('.stat-number');
                        if (totalDocsElement) {
                            totalDocsElement.textContent = data.stats.total_documents;
                            console.log('✅ تم تحديث عدد الوثائق:', data.stats.total_documents);
                        }

                        // الكارت الثاني - الحجم الإجمالي
                        const totalSizeElement = statsCards[1].querySelector('.stat-number');
                        if (totalSizeElement) {
                            totalSizeElement.textContent = data.stats.total_size_mb;
                            console.log('✅ تم تحديث الحجم:', data.stats.total_size_mb);
                        }
                    }
                } else {
                    console.error('❌ فشل في جلب الإحصائيات:', data.message);
                }
            })
            .catch(error => {
                console.error('❌ خطأ في تحديث الإحصائيات:', error);
            });
        }

        // إنشاء رابط مشاركة (دعم خدمات متعددة)
        function createShareLink(documentId, service) {
            const serviceName = service === 'nextcloud' ? 'Nextcloud' : 'OneDrive';

            if (confirm(`هل تريد إنشاء رابط مشاركة ${serviceName} لهذه الوثيقة؟`)) {
                // إظهار مؤشر التحميل
                showToast(`جاري إنشاء رابط ${serviceName}...`, 'info', 'fas fa-spinner fa-spin');

                fetch(`/transfers/documents/${documentId}/create-link`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        service: service
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.is_existing) {
                            showToast(`رابط ${serviceName} موجود مسبقاً`, 'warning', 'fas fa-info-circle');
                        } else {
                            showToast(`تم إنشاء رابط ${serviceName} بنجاح!`, 'success', 'fas fa-check-circle');
                        }

                        // نسخ الرابط للحافظة
                        navigator.clipboard.writeText(data.share_link).then(() => {
                            showToast('تم نسخ الرابط تلقائياً للحافظة', 'info', 'fas fa-copy');
                        });

                        // إعادة تحميل الصفحة لتحديث الأزرار
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        showToast(`خطأ في إنشاء رابط ${serviceName}: ` + data.message, 'danger', 'fas fa-exclamation-circle');
                    }
                })
                .catch(error => {
                    console.error('خطأ:', error);
                    showToast(`حدث خطأ في إنشاء رابط ${serviceName}`, 'danger', 'fas fa-exclamation-triangle');
                });
            }
        }

        // نسخ رابط المشاركة
        function copyShareLink(shareLink) {
            if (shareLink && shareLink.trim() !== '') {
                navigator.clipboard.writeText(shareLink).then(() => {
                    showToast('تم نسخ رابط المشاركة بنجاح!', 'success', 'fas fa-check-circle');
                }).catch(err => {
                    showToast('فشل في نسخ الرابط: ' + err, 'danger', 'fas fa-exclamation-circle');
                });
            } else {
                showToast('رابط المشاركة غير متوفر', 'warning', 'fas fa-exclamation-triangle');
            }
        }

        // فتح رابط المشاركة في نافذة جديدة
        function openShareLink(shareLink) {
            if (shareLink && shareLink.trim() !== '') {
                window.open(shareLink, '_blank', 'noopener,noreferrer');
                showToast('تم فتح رابط المشاركة في نافذة جديدة', 'info', 'fas fa-external-link-alt');
            } else {
                showToast('رابط المشاركة غير متوفر', 'warning', 'fas fa-exclamation-triangle');
            }
        }









        // حذف وثيقة
        function deleteDocument(documentId) {
            if (confirm('هل أنت متأكد من حذف هذه الوثيقة؟\n\nلا يمكن التراجع عن هذا الإجراء.')) {
                const deleteButton = document.querySelector(`[onclick="deleteDocument(${documentId})"]`);
                const originalContent = deleteButton.innerHTML;
                deleteButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                deleteButton.disabled = true;

                fetch(`/transfers/documents/${documentId}/delete`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('تم حذف الوثيقة بنجاح', 'success', 'fas fa-trash');

                        // إزالة صف الوثيقة بتأثير بصري
                        const documentRow = deleteButton.closest('.document-row');
                        if (documentRow) {
                            documentRow.style.transition = 'all 0.3s ease';
                            documentRow.style.opacity = '0';
                            documentRow.style.transform = 'translateX(-100%)';
                            setTimeout(() => {
                                documentRow.remove();
                                // إعادة تحميل الصفحة لتحديث الإحصائيات
                                setTimeout(() => {
                                    location.reload();
                                }, 1000);
                            }, 300);
                        }
                    } else {
                        showToast('خطأ في حذف الوثيقة: ' + data.message, 'danger', 'fas fa-exclamation-circle');
                        deleteButton.innerHTML = originalContent;
                        deleteButton.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('خطأ:', error);
                    showToast('حدث خطأ في حذف الوثيقة', 'danger', 'fas fa-exclamation-triangle');
                    deleteButton.innerHTML = originalContent;
                    deleteButton.disabled = false;
                });
            }
        }

        // استعراض وثيقة
        function previewDocument(documentId, fileName, mimeType) {
            console.log('👁️ استعراض وثيقة:', documentId, fileName, mimeType);

            const previewUrl = `/transfers/documents/${documentId}/preview`;

            // تحديد طريقة العرض حسب نوع الملف
            if (mimeType && (mimeType.includes('pdf') || mimeType.includes('image'))) {
                // للـ PDF والصور - فتح في نافذة جديدة
                const previewWindow = window.open(previewUrl, '_blank', 'width=1000,height=800,scrollbars=yes,resizable=yes');

                if (!previewWindow) {
                    alert('يرجى السماح بفتح النوافذ المنبثقة لاستعراض الوثيقة');
                }
            } else {
                // للملفات الأخرى - عرض في modal أو تحميل
                showDocumentPreviewModal(documentId, fileName, previewUrl);
            }
        }

        // عرض modal لاستعراض الوثائق
        function showDocumentPreviewModal(documentId, fileName, previewUrl) {
            // إنشاء modal ديناميكي
            const modalHtml = `
                <div class="modal fade" id="documentPreviewModal" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-eye me-2"></i>
                                    استعراض الوثيقة: ${fileName}
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body text-center">
                                <div class="mb-3">
                                    <i class="fas fa-file-alt fa-5x text-primary mb-3"></i>
                                    <h6>اسم الملف: ${fileName}</h6>
                                </div>
                                <div class="d-grid gap-2">
                                    <a href="${previewUrl}" target="_blank" class="btn btn-primary btn-lg">
                                        <i class="fas fa-external-link-alt me-2"></i>
                                        فتح في نافذة جديدة
                                    </a>
                                    <a href="/transfers/documents/${documentId}/download" class="btn btn-outline-info">
                                        <i class="fas fa-download me-2"></i>
                                        تحميل الوثيقة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // إزالة modal سابق إن وجد
            const existingModal = document.getElementById('documentPreviewModal');
            if (existingModal) {
                existingModal.remove();
            }

            // إضافة modal جديد
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // عرض modal
            const modal = new bootstrap.Modal(document.getElementById('documentPreviewModal'));
            modal.show();

            // إزالة modal بعد الإغلاق
            document.getElementById('documentPreviewModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }

        // دالة عامة لإظهار الرسائل
        function showToast(message, type = 'success', icon = 'fas fa-info-circle') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';
            toast.innerHTML = `
                <i class="${icon} me-2"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(toast);

            // إزالة الرسالة بعد 4 ثوان
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => toast.remove(), 300);
                }
            }, 4000);
        }
    </script>
</body>
</html>
