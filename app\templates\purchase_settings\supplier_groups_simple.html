{% extends "base.html" %}

{% block title %}مجموعات الموردين{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-users-cog"></i> مجموعات الموردين
                    </h4>
                </div>
                <div class="card-body">
                    
                    <!-- حالة التحميل -->
                    <div id="loadingSpinner" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-3">جاري تحميل مجموعات الموردين...</p>
                    </div>
                    
                    <!-- جدول البيانات -->
                    <div id="dataContainer" style="display: none;">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>رقم المجموعة</th>
                                        <th>اسم المجموعة (عربي)</th>
                                        <th>اسم المجموعة (إنجليزي)</th>
                                        <th>رقم الحساب</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody id="dataTableBody">
                                    <!-- سيتم ملء البيانات هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- حالة الخطأ -->
                    <div id="errorContainer" style="display: none;" class="alert alert-danger">
                        <h5>خطأ في تحميل البيانات</h5>
                        <p id="errorMessage"></p>
                        <button class="btn btn-outline-danger" onclick="loadData()">
                            <i class="fas fa-redo"></i> إعادة المحاولة
                        </button>
                    </div>
                    
                    <!-- حالة فارغة -->
                    <div id="emptyContainer" style="display: none;" class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5>لا توجد مجموعات موردين</h5>
                        <p class="text-muted">لم يتم العثور على أي مجموعات موردين</p>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
console.log('🔧 تحميل JavaScript لمجموعات الموردين...');

// التحقق من وجود jQuery
if (typeof $ === 'undefined') {
    console.error('❌ jQuery غير محمل!');
    document.getElementById('loadingSpinner').style.display = 'none';
    document.getElementById('errorContainer').style.display = 'block';
    document.getElementById('errorMessage').textContent = 'jQuery غير محمل. يرجى إعادة تحميل الصفحة.';
} else {
    console.log('✅ jQuery محمل بنجاح');
}

// تحميل البيانات عند جاهزية الصفحة
$(document).ready(function() {
    console.log('📄 الصفحة جاهزة، بدء تحميل البيانات...');
    loadData();
});

function loadData() {
    console.log('🔄 بدء تحميل مجموعات الموردين...');
    
    // إخفاء جميع الحاويات
    $('#loadingSpinner').show();
    $('#dataContainer').hide();
    $('#errorContainer').hide();
    $('#emptyContainer').hide();
    
    $.ajax({
        url: '/purchase_settings/api/supplier_groups',
        method: 'GET',
        xhrFields: {
            withCredentials: true
        },
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        timeout: 10000, // 10 ثوان
        success: function(response) {
            console.log('✅ تم تحميل البيانات بنجاح:', response);
            
            $('#loadingSpinner').hide();
            
            if (response.success && response.groups && response.groups.length > 0) {
                displayData(response.groups);
                $('#dataContainer').show();
            } else {
                $('#emptyContainer').show();
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ خطأ في تحميل البيانات:', {
                status: xhr.status,
                statusText: xhr.statusText,
                error: error,
                response: xhr.responseText
            });
            
            $('#loadingSpinner').hide();
            $('#errorContainer').show();
            
            let errorMsg = 'خطأ غير معروف';
            if (xhr.status === 401 || xhr.status === 302) {
                errorMsg = 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.';
            } else if (xhr.status === 0) {
                errorMsg = 'فشل في الاتصال بالخادم. تحقق من الاتصال بالإنترنت.';
            } else {
                errorMsg = `خطأ ${xhr.status}: ${xhr.statusText}`;
            }
            
            $('#errorMessage').text(errorMsg);
        }
    });
}

function displayData(groups) {
    console.log('📊 عرض البيانات:', groups);
    
    const tbody = $('#dataTableBody');
    tbody.empty();
    
    groups.forEach(function(group) {
        const statusBadge = group.is_active ? 
            '<span class="badge bg-success">نشط</span>' : 
            '<span class="badge bg-secondary">غير نشط</span>';
        
        const row = `
            <tr>
                <td><strong>${group.group_id}</strong></td>
                <td>${group.group_name_ar || '-'}</td>
                <td>${group.group_name_en || '-'}</td>
                <td>${group.account_number || '-'}</td>
                <td>${statusBadge}</td>
            </tr>
        `;
        
        tbody.append(row);
    });
    
    console.log(`✅ تم عرض ${groups.length} مجموعة`);
}
</script>
{% endblock %}
