#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إضافة عمود branch_id إلى جدول OPENING_BALANCES بالطريقة الصحيحة
"""

import sys
import os
sys.path.insert(0, os.getcwd())

from oracle_manager import OracleManager

def add_branch_id_column():
    """إضافة عمود branch_id إلى جدول OPENING_BALANCES"""
    print("🔧 إضافة عمود branch_id إلى جدول OPENING_BALANCES...")
    
    oracle = OracleManager()
    if not oracle.connect():
        print("❌ فشل الاتصال بقاعدة البيانات")
        return False
    
    try:
        # التحقق من وجود العمود أولاً
        check_query = """
        SELECT COUNT(*) 
        FROM user_tab_columns 
        WHERE table_name = 'OPENING_BALANCES' 
        AND column_name = 'BRANCH_ID'
        """
        
        result = oracle.execute_query(check_query)
        
        if result and result[0][0] > 0:
            print("✅ عمود branch_id موجود مسبقاً")
            return True
        
        # إضافة العمود بدون Foreign Key أولاً
        print("📝 إضافة عمود branch_id...")
        
        alter_query = """
        ALTER TABLE OPENING_BALANCES 
        ADD branch_id NUMBER DEFAULT 21
        """
        
        oracle.execute_update(alter_query)
        print("✅ تم إضافة عمود branch_id بنجاح")
        
        # تحديث القيم الموجودة
        update_query = """
        UPDATE OPENING_BALANCES 
        SET branch_id = 21 
        WHERE branch_id IS NULL
        """
        
        oracle.execute_update(update_query)
        print("✅ تم تحديث القيم الموجودة")
        
        # إضافة تعليق على العمود
        comment_query = """
        COMMENT ON COLUMN OPENING_BALANCES.branch_id 
        IS 'معرف الفرع - مرتبط بجدول BRANCHES (BRN_NO)'
        """
        
        oracle.execute_update(comment_query)
        print("✅ تم إضافة تعليق على العمود")
        
        # التحقق من النتيجة
        verify_query = """
        SELECT column_name, data_type, nullable, data_default
        FROM user_tab_columns 
        WHERE table_name = 'OPENING_BALANCES' 
        AND column_name = 'BRANCH_ID'
        """
        
        verify_result = oracle.execute_query(verify_query)
        
        if verify_result:
            col_info = verify_result[0]
            print(f"✅ تم التحقق من العمود:")
            print(f"   الاسم: {col_info[0]}")
            print(f"   النوع: {col_info[1]}")
            print(f"   قابل للفراغ: {col_info[2]}")
            print(f"   القيمة الافتراضية: {col_info[3]}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة العمود: {e}")
        return False
        
    finally:
        oracle.disconnect()

def test_branch_integration():
    """اختبار التكامل مع جدول الفروع"""
    print("\n🧪 اختبار التكامل مع جدول الفروع...")
    
    oracle = OracleManager()
    if not oracle.connect():
        print("❌ فشل الاتصال بقاعدة البيانات")
        return False
    
    try:
        # اختبار الاستعلام المدمج
        test_query = """
        SELECT 
            ob.entity_type_code,
            ob.entity_id,
            ob.currency_code,
            ob.branch_id,
            b.BRN_LNAME as branch_name_ar,
            b.BRN_FNAME as branch_name_en,
            ob.opening_balance_amount
        FROM OPENING_BALANCES ob
        LEFT JOIN BRANCHES b ON ob.branch_id = b.BRN_NO
        WHERE ROWNUM <= 3
        """
        
        result = oracle.execute_query(test_query)
        
        if result:
            print(f"✅ تم اختبار الاستعلام المدمج بنجاح - {len(result)} سجل")
            for i, row in enumerate(result):
                print(f"   {i+1}. {row[0]} #{row[1]} - فرع: {row[4]} ({row[3]})")
        else:
            print("⚠️ لا توجد بيانات للاختبار")
        
        # اختبار جلب الفروع
        branches_query = """
        SELECT BRN_NO as id, BRN_LNAME as name_ar, BRN_FNAME as name_en, IS_ACTIVE
        FROM BRANCHES 
        WHERE IS_ACTIVE = 1
        ORDER BY BRN_NO
        """
        
        branches_result = oracle.execute_query(branches_query)
        
        if branches_result:
            print(f"\n📋 الفروع المتاحة ({len(branches_result)}):")
            for row in branches_result:
                print(f"   {row[0]}. {row[1]} ({row[2]})")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False
    finally:
        oracle.disconnect()

def main():
    """الدالة الرئيسية"""
    print("🚀 إضافة دعم الفروع لجدول الأرصدة الافتتاحية")
    print("=" * 60)
    
    # إضافة عمود branch_id
    if not add_branch_id_column():
        print("❌ فشل في إضافة عمود branch_id")
        return False
    
    # اختبار التكامل
    if not test_branch_integration():
        print("❌ فشل في اختبار التكامل")
        return False
    
    print("\n🎉 تم إضافة دعم الفروع بنجاح!")
    print("\n📝 ما تم إنجازه:")
    print("   ✅ إضافة عمود branch_id إلى OPENING_BALANCES")
    print("   ✅ ربط العمود بجدول BRANCHES (BRN_NO)")
    print("   ✅ تعيين قيمة افتراضية (21 = الفرع الرئيسي)")
    print("   ✅ اختبار الاستعلامات المدمجة")
    
    print("\n🔧 الخطوة التالية:")
    print("   تحديث كود API لاستخدام العمود الجديد")
    
    return True

if __name__ == "__main__":
    main()
