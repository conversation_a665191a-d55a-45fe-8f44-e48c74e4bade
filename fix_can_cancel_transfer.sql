-- إصلاح دالة CAN_CANCEL_TRANSFER لتتوافق مع بنية جدول transfers الفعلية
-- Fix CAN_CANCEL_TRANSFER function to match actual transfers table structure

-- إنشاء أو تحديث دالة التحقق من إمكانية إلغاء الحوالة
CREATE OR REPLACE FUNCTION CAN_CANCEL_TRANSFER(
    p_transfer_id IN NUMBER
) RETURN VARCHAR2 AS
    v_status VARCHAR2(50);
    v_execution_date TIMESTAMP;
    v_days_since_execution NUMBER;
    v_has_subsequent_transactions NUMBER := 0;
    v_result VARCHAR2(4000);
    v_transfer_number VARCHAR2(50);
    v_amount NUMBER(15,2);
BEGIN
    -- الحصول على معلومات الحوالة
    BEGIN
        SELECT status, execution_date, transfer_number, amount
        INTO v_status, v_execution_date, v_transfer_number, v_amount
        FROM transfers
        WHERE id = p_transfer_id;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RETURN 'ERROR: الحوالة غير موجودة';
    END;
    
    -- التحقق من الحالة (completed بدلاً من executed)
    IF v_status != 'completed' THEN
        RETURN 'ERROR: يمكن إلغاء الحوالات المكتملة فقط. الحالة الحالية: ' || v_status;
    END IF;
    
    -- التحقق من تاريخ التنفيذ
    IF v_execution_date IS NULL THEN
        RETURN 'ERROR: لا يوجد تاريخ تنفيذ للحوالة';
    END IF;
    
    -- حساب الأيام منذ التنفيذ
    v_days_since_execution := SYSDATE - v_execution_date;
    
    -- التحقق من وجود معاملات لاحقة للموردين (إذا كان جدول التوزيعات موجود)
    BEGIN
        SELECT COUNT(*) INTO v_has_subsequent_transactions
        FROM CURRENT_BALANCES cb
        JOIN transfer_supplier_distributions tsd ON cb.entity_id = tsd.supplier_id
        WHERE tsd.transfer_id = p_transfer_id
        AND cb.entity_type_code = 'SUPPLIER'
        AND cb.last_transaction_date > v_execution_date;
    EXCEPTION
        WHEN OTHERS THEN
            -- إذا لم تكن الجداول موجودة، تجاهل هذا التحقق
            v_has_subsequent_transactions := 0;
    END;
    
    -- تحديد إمكانية الإلغاء
    IF v_days_since_execution > 30 THEN
        v_result := 'WARNING: مرت أكثر من 30 يوماً على التنفيذ (' || ROUND(v_days_since_execution, 1) || ' يوم)';
    ELSIF v_has_subsequent_transactions > 0 THEN
        v_result := 'WARNING: توجد معاملات لاحقة للموردين (' || v_has_subsequent_transactions || ' معاملة)';
    ELSE
        v_result := 'OK: يمكن إلغاء الحوالة رقم ' || v_transfer_number || ' بمبلغ ' || v_amount;
    END IF;
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'ERROR: خطأ في النظام - ' || SQLERRM;
END;
/

-- اختبار الدالة المحدثة
DECLARE
    v_result VARCHAR2(4000);
BEGIN
    DBMS_OUTPUT.PUT_LINE('=== اختبار دالة CAN_CANCEL_TRANSFER المحدثة ===');
    
    -- اختبار حوالة غير موجودة
    v_result := CAN_CANCEL_TRANSFER(99999);
    DBMS_OUTPUT.PUT_LINE('نتيجة الاختبار للحوالة 99999: ' || v_result);
    
    -- اختبار حوالة أخرى
    v_result := CAN_CANCEL_TRANSFER(1);
    DBMS_OUTPUT.PUT_LINE('نتيجة الاختبار للحوالة 1: ' || v_result);
    
    DBMS_OUTPUT.PUT_LINE('=== انتهى الاختبار ===');
END;
/

-- عرض رسالة نجاح
SELECT 'تم تحديث دالة CAN_CANCEL_TRANSFER بنجاح' as result FROM DUAL;
