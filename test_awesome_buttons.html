
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأزرار الرائعة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="card border-0 shadow-sm mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="card-body text-white">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-magic fa-3x opacity-75"></i>
                            </div>
                            <div>
                                <h1 class="h2 mb-1 fw-bold">اختبار الأزرار الرائعة</h1>
                                <p class="mb-0 opacity-90">اختبار جميع الأزرار الجديدة المضافة فوق جدول الأصناف</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإحصائيات السريعة -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <h3 class="mb-0 text-primary">13</h3>
                        <small class="text-muted">إجمالي الأصناف</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <h3 class="mb-0 text-success">14,152.0</h3>
                        <small class="text-muted">إجمالي الكمية</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <h3 class="mb-0 text-info">¥2,001,126</h3>
                        <small class="text-muted">إجمالي القيمة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <h3 class="mb-0 text-warning">5</h3>
                        <small class="text-muted">عدد الموردين</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول مع الأزرار الرائعة -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        جدول الأصناف التفصيلي مع الأزرار الرائعة
                    </h5>
                    <div class="d-flex gap-2">
                        <!-- مجموعة التصدير -->
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToExcel()" title="تصدير إلى Excel">
                                <i class="fas fa-file-excel me-1"></i>
                                Excel
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="exportToPDF()" title="تصدير إلى PDF">
                                <i class="fas fa-file-pdf me-1"></i>
                                PDF
                            </button>
                        </div>
                        
                        <!-- مجموعة العمليات -->
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshData()" title="تحديث البيانات">
                                <i class="fas fa-sync-alt me-1"></i>
                                تحديث
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="resetFilters()" title="مسح جميع الفلاتر">
                                <i class="fas fa-times me-1"></i>
                                مسح الفلاتر
                            </button>
                        </div>
                        
                        <!-- مجموعة العرض -->
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="toggleTableView()" title="تبديل عرض الجدول">
                                <i class="fas fa-expand-alt me-1"></i>
                                ملء الشاشة
                            </button>
                            <button type="button" class="btn btn-outline-warning btn-sm" onclick="showTableStats()" title="إحصائيات الجدول">
                                <i class="fas fa-chart-bar me-1"></i>
                                إحصائيات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="awesomeTable" class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>كود الصنف</th>
                                <th>اسم الصنف</th>
                                <th>المورد</th>
                                <th>الكمية</th>
                                <th>سعر الوحدة</th>
                                <th>إجمالي القيمة</th>
                            </tr>
                        </thead>
                        <tbody>
        
                            <tr>
                                <td>001-1023-</td>
                                <td><strong>حلوى سي سي عملاق جديد 12×30×22جم</strong></td>
                                <td>شركة وايسدوم هاوس</td>
                                <td>3,000.0</td>
                                <td>¥115.00</td>
                                <td><strong>¥345,000.00</strong></td>
                            </tr>
            
                            <tr>
                                <td>001-1009-</td>
                                <td><strong>علكة ثلاجة العائلة 6*380*2جم</strong></td>
                                <td>شركة يابايشينج-الصين</td>
                                <td>2,111.0</td>
                                <td>¥162.00</td>
                                <td><strong>¥341,982.00</strong></td>
                            </tr>
            
                            <tr>
                                <td>001-0604-</td>
                                <td><strong>علكة بوكر 12×200×3جم</strong></td>
                                <td>شركة يابايشينج-الصين</td>
                                <td>2,062.0</td>
                                <td>¥148.00</td>
                                <td><strong>¥305,176.00</strong></td>
                            </tr>
            
                            <tr>
                                <td>001-0925-</td>
                                <td><strong>حلوى بودرة حامض سوبر 12*100*11جم</strong></td>
                                <td>شركة ياهوا فود كومبنى</td>
                                <td>980.0</td>
                                <td>¥264.00</td>
                                <td><strong>¥258,720.00</strong></td>
                            </tr>
            
                            <tr>
                                <td>001-0792-</td>
                                <td><strong>حلوى كرسبي 12×200×3جم</strong></td>
                                <td>شركة رايسن</td>
                                <td>2,040.0</td>
                                <td>¥123.00</td>
                                <td><strong>¥250,920.00</strong></td>
                            </tr>
            
                            <tr>
                                <td>001-0941-</td>
                                <td><strong>حلوى بودرة جامبو 12×48×15جم</strong></td>
                                <td>شركة ياهوا فود كومبنى</td>
                                <td>1,240.0</td>
                                <td>¥136.00</td>
                                <td><strong>¥168,640.00</strong></td>
                            </tr>
            
                            <tr>
                                <td>001-1027-</td>
                                <td><strong>جيلي سليس صودا 24*30*20جم</strong></td>
                                <td>شركة يونجي-الصين</td>
                                <td>928.0</td>
                                <td>¥135.00</td>
                                <td><strong>¥125,280.00</strong></td>
                            </tr>
            
                            <tr>
                                <td>001-0385-</td>
                                <td><strong>حلوى جيلي سمارت قلوب 16×30×35جم</strong></td>
                                <td>شركة يونجي-الصين</td>
                                <td>820.0</td>
                                <td>¥100.00</td>
                                <td><strong>¥82,000.00</strong></td>
                            </tr>
            
                            <tr>
                                <td>001-0385-</td>
                                <td><strong>حلوى جيلي سمارت قلوب 16×30×35جم</strong></td>
                                <td>شركة يونجي-الصين</td>
                                <td>390.0</td>
                                <td>¥100.00</td>
                                <td><strong>¥39,000.00</strong></td>
                            </tr>
            
                            <tr>
                                <td>001-1027-</td>
                                <td><strong>جيلي سليس صودا 24*30*20جم</strong></td>
                                <td>شركة يونجي-الصين</td>
                                <td>280.0</td>
                                <td>¥135.00</td>
                                <td><strong>¥37,800.00</strong></td>
                            </tr>
            
                            <tr>
                                <td>001-1028-</td>
                                <td><strong>حلوى صودا بخاخ 18*30*25مل</strong></td>
                                <td>شركة يونجي-الصين</td>
                                <td>150.0</td>
                                <td>¥153.00</td>
                                <td><strong>¥22,950.00</strong></td>
                            </tr>
            
                            <tr>
                                <td>001-1028-</td>
                                <td><strong>حلوى صودا بخاخ 18*30*25مل</strong></td>
                                <td>شركة يونجي-الصين</td>
                                <td>146.0</td>
                                <td>¥153.00</td>
                                <td><strong>¥22,338.00</strong></td>
                            </tr>
            
                            <tr>
                                <td>001-0925-</td>
                                <td><strong>حلوى بودرة حامض سوبر 12*100*11جم</strong></td>
                                <td>شركة ياهوا فود كومبنى</td>
                                <td>5.0</td>
                                <td>¥264.00</td>
                                <td><strong>¥1,320.00</strong></td>
                            </tr>
            
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- نتائج الاختبار -->
        <div class="mt-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clipboard-check me-2"></i>
                        نتائج اختبار الأزرار
                    </h5>
                </div>
                <div class="card-body">
                    <div id="testResults">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            اضغط على الأزرار أعلاه لاختبارها
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    let awesomeTable = null;
    let isFullScreen = false;
    
    // تهيئة الجدول
    $(document).ready(function() {
        awesomeTable = $('#awesomeTable').DataTable({
            language: {
                processing: "جاري المعالجة...",
                search: "بحث:",
                lengthMenu: "أظهر _MENU_ مدخلات",
                info: "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                paginate: {
                    first: "الأول",
                    previous: "السابق",
                    next: "التالي",
                    last: "الأخير"
                }
            },
            pageLength: 10,
            order: [[5, 'desc']]
        });
    });
    
    function showResult(type, title, message) {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'danger' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        const icon = type === 'success' ? 'fa-check-circle' : 
                    type === 'danger' ? 'fa-exclamation-triangle' : 
                    type === 'warning' ? 'fa-exclamation-circle' : 'fa-info-circle';
        
        $('#testResults').html(`
            <div class="alert ${{alertClass}}">
                <h6><i class="fas ${{icon}} me-2"></i>${{title}}</h6>
                <div>${{message}}</div>
            </div>
        `);
    }
    
    // دوال الأزرار
    function exportToExcel() {
        showResult('success', 'تصدير Excel', 'تم اختبار زر تصدير Excel بنجاح!');
    }
    
    function exportToPDF() {
        showResult('success', 'تصدير PDF', 'تم اختبار زر تصدير PDF بنجاح!');
    }
    
    function refreshData() {
        showResult('info', 'تحديث البيانات', 'تم اختبار زر تحديث البيانات بنجاح!');
    }
    
    function resetFilters() {
        showResult('warning', 'مسح الفلاتر', 'تم اختبار زر مسح الفلاتر بنجاح!');
    }
    
    function toggleTableView() {
        const tableCard = document.querySelector('.card.border-0.shadow-sm:last-of-type');
        const toggleBtn = event.target.closest('button');
        const icon = toggleBtn.querySelector('i');
        
        if (!isFullScreen) {
            tableCard.style.position = 'fixed';
            tableCard.style.top = '0';
            tableCard.style.left = '0';
            tableCard.style.width = '100vw';
            tableCard.style.height = '100vh';
            tableCard.style.zIndex = '9999';
            tableCard.style.backgroundColor = 'white';
            tableCard.style.overflow = 'auto';
            
            icon.className = 'fas fa-compress-alt me-1';
            isFullScreen = true;
            
            showResult('info', 'ملء الشاشة', 'تم تفعيل وضع ملء الشاشة!');
        } else {
            tableCard.style.position = '';
            tableCard.style.top = '';
            tableCard.style.left = '';
            tableCard.style.width = '';
            tableCard.style.height = '';
            tableCard.style.zIndex = '';
            tableCard.style.backgroundColor = '';
            tableCard.style.overflow = '';
            
            icon.className = 'fas fa-expand-alt me-1';
            isFullScreen = false;
            
            showResult('info', 'إلغاء ملء الشاشة', 'تم إلغاء وضع ملء الشاشة!');
        }
    }
    
    function showTableStats() {
        const data = awesomeTable.rows().data().toArray();
        const totalItems = data.length;
        
        showResult('success', 'إحصائيات الجدول', 
            `إجمالي الأصناف: ${{totalItems}}<br>` +
            `تم اختبار زر الإحصائيات بنجاح!`);
    }
    </script>
</body>
</html>
        