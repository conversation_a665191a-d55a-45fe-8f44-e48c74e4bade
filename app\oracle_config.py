# -*- coding: utf-8 -*-
"""
Oracle Database Configuration for NetSuite-style ERP
تكوين قاعدة بيانات Oracle لنظام ERP بنمط NetSuite
"""

import os
import cx_Oracle
from sqlalchemy import create_engine, event
from sqlalchemy.pool import NullPool
import logging

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OracleConfig:
    """تكوين Oracle Database"""
    
    # معلومات الاتصال
    ORACLE_HOST = os.environ.get('ORACLE_HOST', 'localhost')
    ORACLE_PORT = os.environ.get('ORACLE_PORT', '1521')
    ORACLE_SERVICE_NAME = os.environ.get('ORACLE_SERVICE_NAME', 'XEPDB1')
    ORACLE_USERNAME = os.environ.get('ORACLE_USERNAME', 'saserp')
    ORACLE_PASSWORD = os.environ.get('ORACLE_PASSWORD', 'password')
    
    # إعدادات الاتصال المتقدمة
    ORACLE_POOL_SIZE = int(os.environ.get('ORACLE_POOL_SIZE', '10'))
    ORACLE_MAX_OVERFLOW = int(os.environ.get('ORACLE_MAX_OVERFLOW', '20'))
    ORACLE_POOL_TIMEOUT = int(os.environ.get('ORACLE_POOL_TIMEOUT', '30'))
    ORACLE_POOL_RECYCLE = int(os.environ.get('ORACLE_POOL_RECYCLE', '3600'))
    
    @classmethod
    def get_oracle_url(cls):
        """إنشاء URL الاتصال بـ Oracle"""
        return f"oracle+cx_oracle://{cls.ORACLE_USERNAME}:{cls.ORACLE_PASSWORD}@{cls.ORACLE_HOST}:{cls.ORACLE_PORT}/?service_name={cls.ORACLE_SERVICE_NAME}"
    
    @classmethod
    def create_oracle_engine(cls):
        """إنشاء محرك Oracle"""
        try:
            # تكوين Oracle Client
            if not hasattr(cx_Oracle, 'version'):
                cx_Oracle.init_oracle_client()
            
            # إنشاء المحرك
            engine = create_engine(
                cls.get_oracle_url(),
                pool_size=cls.ORACLE_POOL_SIZE,
                max_overflow=cls.ORACLE_MAX_OVERFLOW,
                pool_timeout=cls.ORACLE_POOL_TIMEOUT,
                pool_recycle=cls.ORACLE_POOL_RECYCLE,
                echo=False,  # تعيين True للتصحيح
                poolclass=NullPool if os.environ.get('FLASK_ENV') == 'development' else None
            )
            
            # إضافة مستمعات الأحداث
            @event.listens_for(engine, "connect")
            def set_oracle_session_config(dbapi_connection, connection_record):
                """تكوين جلسة Oracle"""
                with dbapi_connection.cursor() as cursor:
                    # تعيين التنسيق للتواريخ
                    cursor.execute("ALTER SESSION SET NLS_DATE_FORMAT = 'YYYY-MM-DD HH24:MI:SS'")
                    cursor.execute("ALTER SESSION SET NLS_TIMESTAMP_FORMAT = 'YYYY-MM-DD HH24:MI:SS.FF'")
                    
                    # تعيين الترميز للعربية
                    cursor.execute("ALTER SESSION SET NLS_LANGUAGE = 'ARABIC'")
                    cursor.execute("ALTER SESSION SET NLS_TERRITORY = 'SAUDI ARABIA'")
                    
                    # تحسين الأداء
                    cursor.execute("ALTER SESSION SET OPTIMIZER_MODE = ALL_ROWS")
                    cursor.execute("ALTER SESSION SET QUERY_REWRITE_ENABLED = TRUE")
            
            logger.info("تم إنشاء محرك Oracle بنجاح")
            return engine
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء محرك Oracle: {e}")
            raise

class OracleTableNames:
    """أسماء الجداول في Oracle"""
    
    # جداول النظام الأساسية
    USERS = "SAS_USERS"
    ROLES = "SAS_ROLES"
    PERMISSIONS = "SAS_PERMISSIONS"
    USER_ROLES = "SAS_USER_ROLES"
    
    # جداول الموردين
    SUPPLIERS = "SAS_SUPPLIERS"
    SUPPLIER_CONTACTS = "SAS_SUPPLIER_CONTACTS"
    SUPPLIER_EVALUATIONS = "SAS_SUPPLIER_EVALUATIONS"
    SUPPLIER_CONTRACTS = "SAS_SUPPLIER_CONTRACTS"
    
    # جداول المشتريات
    PURCHASE_REQUESTS = "SAS_PURCHASE_REQUESTS"
    PURCHASE_REQUEST_ITEMS = "SAS_PURCHASE_REQUEST_ITEMS"
    PURCHASE_ORDERS = "SAS_PURCHASE_ORDERS"
    PURCHASE_ORDER_ITEMS = "SAS_PURCHASE_ORDER_ITEMS"
    
    # جداول المخزون
    INVENTORY_ITEMS = "SAS_INVENTORY_ITEMS"
    INVENTORY_TRANSACTIONS = "SAS_INVENTORY_TRANSACTIONS"
    INVENTORY_LOCATIONS = "SAS_INVENTORY_LOCATIONS"
    STOCK_MOVEMENTS = "SAS_STOCK_MOVEMENTS"
    
    # جداول المالية
    ACCOUNTS = "SAS_ACCOUNTS"
    JOURNAL_ENTRIES = "SAS_JOURNAL_ENTRIES"
    JOURNAL_ENTRY_LINES = "SAS_JOURNAL_ENTRY_LINES"
    INVOICES = "SAS_INVOICES"
    INVOICE_LINES = "SAS_INVOICE_LINES"
    
    # جداول التقارير
    REPORTS = "SAS_REPORTS"
    REPORT_PARAMETERS = "SAS_REPORT_PARAMETERS"
    REPORT_SCHEDULES = "SAS_REPORT_SCHEDULES"

class OracleSequences:
    """تسلسلات Oracle"""
    
    # تسلسلات الجداول الأساسية
    USER_SEQ = "SAS_USER_SEQ"
    SUPPLIER_SEQ = "SAS_SUPPLIER_SEQ"
    PURCHASE_REQUEST_SEQ = "SAS_PURCHASE_REQUEST_SEQ"
    PURCHASE_ORDER_SEQ = "SAS_PURCHASE_ORDER_SEQ"
    INVENTORY_ITEM_SEQ = "SAS_INVENTORY_ITEM_SEQ"
    TRANSACTION_SEQ = "SAS_TRANSACTION_SEQ"
    ACCOUNT_SEQ = "SAS_ACCOUNT_SEQ"
    JOURNAL_ENTRY_SEQ = "SAS_JOURNAL_ENTRY_SEQ"
    INVOICE_SEQ = "SAS_INVOICE_SEQ"
    REPORT_SEQ = "SAS_REPORT_SEQ"

class OracleViews:
    """عروض Oracle"""
    
    # عروض التقارير
    SUPPLIER_SUMMARY = "SAS_V_SUPPLIER_SUMMARY"
    PURCHASE_SUMMARY = "SAS_V_PURCHASE_SUMMARY"
    INVENTORY_SUMMARY = "SAS_V_INVENTORY_SUMMARY"
    FINANCIAL_SUMMARY = "SAS_V_FINANCIAL_SUMMARY"
    
    # عروض لوحة المعلومات
    DASHBOARD_STATS = "SAS_V_DASHBOARD_STATS"
    RECENT_ACTIVITIES = "SAS_V_RECENT_ACTIVITIES"
    LOW_STOCK_ITEMS = "SAS_V_LOW_STOCK_ITEMS"
    PENDING_APPROVALS = "SAS_V_PENDING_APPROVALS"

class OraclePackages:
    """حزم Oracle المخصصة"""
    
    # حزمة إدارة الموردين
    SUPPLIER_PKG = "SAS_SUPPLIER_PKG"
    
    # حزمة إدارة المشتريات
    PURCHASE_PKG = "SAS_PURCHASE_PKG"
    
    # حزمة إدارة المخزون
    INVENTORY_PKG = "SAS_INVENTORY_PKG"
    
    # حزمة التقارير
    REPORTS_PKG = "SAS_REPORTS_PKG"
    
    # حزمة الأمان
    SECURITY_PKG = "SAS_SECURITY_PKG"

def test_oracle_connection():
    """اختبار الاتصال بـ Oracle"""
    try:
        engine = OracleConfig.create_oracle_engine()
        
        with engine.connect() as connection:
            result = connection.execute("SELECT 'Oracle Connection Successful' as status FROM DUAL")
            row = result.fetchone()
            logger.info(f"نتيجة الاختبار: {row[0]}")
            return True
            
    except Exception as e:
        logger.error(f"فشل في الاتصال بـ Oracle: {e}")
        return False

def create_oracle_schema():
    """إنشاء مخطط Oracle"""
    try:
        engine = OracleConfig.create_oracle_engine()
        
        # سكريبت إنشاء الجداول الأساسية
        create_tables_sql = """
        -- إنشاء جدول المستخدمين
        CREATE TABLE SAS_USERS (
            ID NUMBER PRIMARY KEY,
            USERNAME VARCHAR2(50) UNIQUE NOT NULL,
            EMAIL VARCHAR2(100) UNIQUE NOT NULL,
            PASSWORD_HASH VARCHAR2(255) NOT NULL,
            FULL_NAME NVARCHAR2(100) NOT NULL,
            DEPARTMENT NVARCHAR2(50),
            POSITION NVARCHAR2(50),
            IS_ACTIVE NUMBER(1) DEFAULT 1,
            CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- إنشاء جدول الموردين
        CREATE TABLE SAS_SUPPLIERS (
            ID NUMBER PRIMARY KEY,
            CODE VARCHAR2(20) UNIQUE NOT NULL,
            NAME_AR NVARCHAR2(200) NOT NULL,
            NAME_EN VARCHAR2(200),
            CONTACT_PERSON NVARCHAR2(100),
            PHONE VARCHAR2(20),
            MOBILE VARCHAR2(20),
            EMAIL VARCHAR2(100),
            WEBSITE VARCHAR2(200),
            ADDRESS NVARCHAR2(500),
            CITY NVARCHAR2(100),
            COUNTRY NVARCHAR2(100),
            POSTAL_CODE VARCHAR2(20),
            TAX_NUMBER VARCHAR2(50),
            COMMERCIAL_REGISTER VARCHAR2(50),
            TYPE VARCHAR2(20) CHECK (TYPE IN ('local', 'international')),
            CATEGORY VARCHAR2(20) CHECK (CATEGORY IN ('goods', 'services', 'both')),
            PAYMENT_TERMS NVARCHAR2(200),
            CREDIT_LIMIT NUMBER(15,2),
            RATING NUMBER(1) CHECK (RATING BETWEEN 1 AND 5),
            NOTES NCLOB,
            IS_ACTIVE NUMBER(1) DEFAULT 1,
            IS_APPROVED NUMBER(1) DEFAULT 0,
            CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        with engine.connect() as connection:
            # تنفيذ السكريبت
            for statement in create_tables_sql.split(';'):
                if statement.strip():
                    try:
                        connection.execute(statement)
                        logger.info("تم تنفيذ العبارة بنجاح")
                    except Exception as e:
                        if "already exists" not in str(e).lower():
                            logger.warning(f"تحذير في تنفيذ العبارة: {e}")
            
            connection.commit()
            logger.info("تم إنشاء مخطط Oracle بنجاح")
            return True
            
    except Exception as e:
        logger.error(f"خطأ في إنشاء مخطط Oracle: {e}")
        return False

# إعدادات البيئة المطلوبة
REQUIRED_ENV_VARS = [
    'ORACLE_HOST',
    'ORACLE_PORT', 
    'ORACLE_SERVICE_NAME',
    'ORACLE_USERNAME',
    'ORACLE_PASSWORD'
]

def check_oracle_environment():
    """فحص متغيرات البيئة المطلوبة"""
    missing_vars = []
    for var in REQUIRED_ENV_VARS:
        if not os.environ.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.warning(f"متغيرات البيئة المفقودة: {missing_vars}")
        logger.info("سيتم استخدام القيم الافتراضية")
    
    return len(missing_vars) == 0

if __name__ == "__main__":
    # اختبار التكوين
    print("🔍 فحص تكوين Oracle...")
    
    if check_oracle_environment():
        print("✅ متغيرات البيئة متوفرة")
    else:
        print("⚠️ بعض متغيرات البيئة مفقودة - سيتم استخدام القيم الافتراضية")
    
    print("🔗 اختبار الاتصال...")
    if test_oracle_connection():
        print("✅ تم الاتصال بـ Oracle بنجاح")
        
        print("🏗️ إنشاء المخطط...")
        if create_oracle_schema():
            print("✅ تم إنشاء المخطط بنجاح")
        else:
            print("❌ فشل في إنشاء المخطط")
    else:
        print("❌ فشل في الاتصال بـ Oracle")
