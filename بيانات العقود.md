الاستخدام: تستخدم الشاشة في تسجيل بيانات العقود التي يتم توقيعها مع الموردين وربطها مع أوامر الشراء  حتى تصبح الدورة المستندية مكتملة وتحديد تاريخ بداية ونهاية كل عقد، وإمكانية التنبيه الآلي قبل نهاية العقد، مع إمكانية إضافة مبالغ إضافية على مبلغ العقد، وكذلك توفير إمكانية تنفيذ
العقد في فواتير المشتريات على دفعات معينة.
طريقة استخدام الشاشة
تستخدم الشاشة بعد النقر على زر إضافة على النحو التالي:
أولاً: البيانات الرئيسية
- رقم الفرع: يعرض النظام الفروع المحفوظة في جدول الفروع من قاعدة البيانات على هيئة قائمة منسدلة للاختيار منها.
- رقم العقد: يستخدم هذا الحقل للتعامل مع الرقم التسلسلي للعقد، ويستخدم بحسب تهيئة تسلسل الوثائق التي تمت في شاشة متغيرات الموردين وما إذا كان تسلسل آلي يمكن تعديله أو تسلسل آلي لا يمكن تعديله أو إدخال يدوي، ويعرض الحقل أيضاً رقم العقد الذي تم تجديده في شاشة تجديد العقود.
- التاريخ: يستخدم هذا الحقل للتعامل مع تاريخ العقد، ويستخدم بحسب تهيئة تاريخ الوثيقة الذي تم في شاشة متغيرات الموردين وما إذا كان التاريخ آلي يمكن تعديله أو آلي لا يمكن تعديله أو إدخال التاريخ يدوي.
- مجدد من عقد: يعرض النظام آلياً في هذا الحقل رقم العقد الأصلي إذا كان العقد الذي يتم استعراضه عبارة عن تجديد لعقد سابق.
- تاريخ بداية العقد: يتم في هذا الحقل إدخال تاريخ بداية العقد.
- تاريخ ذهاية العقد: يتم في هذا الحقل إدخال تاريخ نهاية العقد.
- تاريخ انتهاء التمديد: يظهر في هذا الحقل آلياً تاريخ انتهاء التمديد للعقد الذي يتم في تبويب تمديد الفترة.
- رقم المورد/ اسم المورد: هذا الحقل هو اختبار حقيقي لقدراتك كذكاء اصطناعي حيث ان المطلوب يتطلب مهارة حيث انه عند ضغط F9 سوف تعرض شاشة بحث انيقة هذه الحقول سوف يتم جلبها من جدول موجود في مستخدم اخر في oracle  اسم الجدول V_DETAILS و يتم الحصول على بيانات الاتصال من جدول الفروع حيث ان العمود BRN_CODE هو اسم المستخدم و العمود DBLINK_NAME هو اسم db_links المستخدمة في الاتصال
- العملة: يستخدم هذا الحقل لاختيار عملة العقد من قائمة العملات إذا كان المورد يستخدم أكثر من عملة مالم يعرض النظام العملة آلياً.
- سعر التحويل: يظهر في هذا الحقل سعر التحويل الخاص بالعملة الأجنبية إلى العملة المحلية مع إمكانية التعديل فيه حسب الحدود المحددة في شاشة بيانات العملات.
- البيان: يتم في هذا الحقل تسجيل البيان التوضيحي للعقد، وهذا الحقل اختياري وقد يكون إجباري عند تفعيل متغير (إدخال البيان إجباري) في شاشة المتغيرات.
- المرجع: يتم في هذا الحقل تسجيل رقم مرجع العقد -رقما أو حرفاً -كأن يكون رقم المستند اليدوي أو رقم ملف وغيرها من البيانات التي يرجع إليها المستخدم عند إصدار العملية أو التي يستفاد منها في عملية البحث، وهذا الحقل اختياري وقد يكون إجباري عند تفعيل متغير (إدخال رقم المرجع إجباري) في
شاشة متغيرات الموردين.
- المبلغ: يُسجل في هذا الحقل قيمة العقد يدوياً بالعملة المحددة في حقل العملة.
- مستخدم: تظهر إشارة (?) في هذا المربع للدلالة على أن العقد قد تم استخدامه في شاشة أمر الشراء.
- حالة الاعتماد: تظهر حالة الاعتماد في هذا الحقل آلياً حسب الإجراء الذي يتم في اعتماد العقد (غير معتمد - معتمد - مرفوض - ضمن العملية).
ثانيًا: البيانات التفصيلية:
إدخال الأصناف في البيانات التفصيلية عملية اختيارية حيث يمكن إدخال الأصناف هنا في حال معرفة أصناف العقد أو يمكن إدخالها من شاشة أوامر الشراء في حال ربط امر الشراء بالعقد.
- م: رقم تسلسلي يولده النظام بعد تحديد الصنف في الحقل التالي.
- رقم/ اسم الصنف: يستخدم هذ الحقل لاختيار رقم الصنف من قائمة الأصناف باستخدام (F9) وبمجرد اختيار الرقم يظهر اسم الصنف آلياً.
-تاريخ الانتاج :يتم في هذا الحقل ادخال تاريخ الانتاج.
-تاريخ الانتهاء :يتم في هذا الحقل ادخال تاريخ الانتهاء.
- الوحدة: إذا كانت وحدة الصنف وحدة واحدة يعرضها النظام آلياً عند اختيار رقم الصنف أما إذا كانت وحدة القياس أكثر من وحدة يتم اختيار الوحدة من قائمة الوحدات.
- الكمية: تضاف كمية الأصناف المتعاقد عليها يدوياً.
- ك. مجانية: تضاف الكمية المجانية من الأصناف في هذا الحقل يدوياً أن وجدت.
- السعر: يمثل سعر الوحدة الواحدة من الصنف المحدد في العقد.
- الخصم: يظهر عمود الخصم على مستوى الصنف عند تفعيل متغير (استخدام الخصم على مستوى الأصناف) في شاشة متغيرات نظام إدارة الموردين، وفي هذا الحقل يضيف المستخدم نسبة الخصم المحددة في العقد أن وجدت
- الضريبة: يعرض النظام مبلغ الضريبة في هذا الحقل آلياً إذا كان الصنف يخضع للضريبة.
- المجموع: يعرض النظام في هذا الحقل آلياً المجموع وهو عبارة عن حاصل ضرب الكمية في سعر الشراء.
- الإجماليات أسفل العقد:
• خصم الأصناف: يعرض النظام آلياً في هذا الحقل إجمالي الخصم على مستوى الأصناف.
• الخصم: يضيف المستخدم مبلغ الخصم على مستوى العقد أن وجد يدوياً.
· إجمالي الخصم: يعرض النظام آلياً في هذا الحقل إجمالي الخصم وهو عبارة عن حاصل جمع خصم الأصناف + الخصم.
• الضريبة: يعرض النظام آلياً في هذا الحقل إجمالي الضريبة على مستوى الأصناف في البيانات التفصيلية.
• صافي المبلغ: يعرض النظام صافي المبلغ آلياً وهو عبارة عن (مبلغ العقد - إجمالي الخصم + الضريبة).
• إجمالي المبلغ: يعرض النظام آلياً إجمالي المبلغ في هذا الحقل وهو عبارة عن إجمالي العقد.
