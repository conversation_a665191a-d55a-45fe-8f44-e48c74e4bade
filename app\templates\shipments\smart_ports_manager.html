{% extends "base.html" %}

{% block title %}نظام الموانئ الذكي{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h2 class="card-title mb-0">
                        <i class="fas fa-anchor me-3"></i>
                        نظام الموانئ الذكي العالمي
                    </h2>
                    <p class="card-text mt-2">ابحث واختر من آلاف الموانئ حول العالم</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-search me-2"></i>
                        البحث المتقدم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">البحث</label>
                            <input type="text" class="form-control" id="searchInput" 
                                   placeholder="اسم الميناء، الكود، البلد، أو المدينة...">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">القارة</label>
                            <select class="form-select" id="continentFilter">
                                <option value="">جميع القارات</option>
                                <option value="Asia">آسيا</option>
                                <option value="Europe">أوروبا</option>
                                <option value="North America">أمريكا الشمالية</option>
                                <option value="South America">أمريكا الجنوبية</option>
                                <option value="Africa">أفريقيا</option>
                                <option value="Oceania">أوقيانوسيا</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">المنطقة</label>
                            <select class="form-select" id="regionFilter">
                                <option value="">جميع المناطق</option>
                                <option value="Middle East">الشرق الأوسط</option>
                                <option value="East Asia">شرق آسيا</option>
                                <option value="Southeast Asia">جنوب شرق آسيا</option>
                                <option value="Western Europe">أوروبا الغربية</option>
                                <option value="Eastern Europe">أوروبا الشرقية</option>
                                <option value="West Coast">الساحل الغربي</option>
                                <option value="East Coast">الساحل الشرقي</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">النوع</label>
                            <select class="form-select" id="typeFilter">
                                <option value="">جميع الأنواع</option>
                                <option value="1">موانئ رئيسية</option>
                                <option value="0">موانئ ثانوية</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">الغرض</label>
                            <select class="form-select" id="purposeFilter">
                                <option value="">جميع الأغراض</option>
                                <option value="origin">موانئ الشحن</option>
                                <option value="destination">موانئ الوصول</option>
                                <option value="both">موانئ مختلطة</option>
                            </select>
                        </div>
                        <div class="col-md-1 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-primary w-100" onclick="searchPorts()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Quick Filters -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex flex-wrap gap-2">
                                <!-- فلاتر التصنيف -->
                                <button class="btn btn-outline-success btn-sm" onclick="quickFilter('origin')">
                                    <i class="fas fa-ship me-1"></i>
                                    موانئ الشحن
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="quickFilter('destination')">
                                    <i class="fas fa-anchor me-1"></i>
                                    موانئ الوصول
                                </button>

                                <!-- فلاتر جغرافية -->
                                <button class="btn btn-outline-primary btn-sm" onclick="quickSearch('Middle East')">
                                    الشرق الأوسط
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="quickSearch('major')">
                                    الموانئ الرئيسية
                                </button>
                                <button class="btn btn-outline-warning btn-sm" onclick="showFavorites()">
                                    <i class="fas fa-heart me-1"></i>
                                    المفضلة
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="showRecommendations()">
                                    <i class="fas fa-brain me-1"></i>
                                    التوصيات الذكية
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                                    <i class="fas fa-eraser me-1"></i>
                                    مسح الفلاتر
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        نتائج البحث
                        <span class="badge bg-primary ms-2" id="resultsCount">0 ميناء</span>
                    </h5>
                    <div>
                        <button class="btn btn-success btn-sm" onclick="addCustomPort()">
                            <i class="fas fa-plus me-2"></i>
                            إضافة ميناء مخصص
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <!-- Loading -->
                    <div id="loadingSection" class="text-center p-5" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري البحث...</span>
                        </div>
                        <div class="mt-3">جاري البحث في موانئ العالم...</div>
                    </div>
                    
                    <!-- No Results -->
                    <div id="noResultsSection" class="text-center p-5" style="display: none;">
                        <i class="fas fa-search text-muted mb-3" style="font-size: 3rem;"></i>
                        <h5 class="text-muted">لا توجد نتائج</h5>
                        <p class="text-muted">جرب البحث بكلمات مختلفة أو قم بتوسيع معايير البحث</p>
                    </div>
                    
                    <!-- Results Grid -->
                    <div id="resultsGrid" class="p-3">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة ميناء مخصص -->
<div class="modal fade" id="customPortModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة ميناء مخصص</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="customPortForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">كود الميناء *</label>
                            <input type="text" class="form-control" name="port_code" maxlength="10" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم الميناء *</label>
                            <input type="text" class="form-control" name="port_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">البلد *</label>
                            <input type="text" class="form-control" name="country" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">المدينة *</label>
                            <input type="text" class="form-control" name="city" required>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.port-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.port-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0,123,255,0.15);
    transform: translateY(-2px);
}

.port-card.selected {
    border-color: #28a745;
    background: #f8fff9;
}

.port-code {
    background: #007bff;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.port-major {
    background: #ffc107;
    color: #000;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.7rem;
    font-weight: bold;
}

.port-favorite {
    background: #28a745;
    color: white;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.7rem;
}

.port-location {
    color: #6c757d;
    font-size: 0.9rem;
}

.port-details {
    font-size: 0.8rem;
    color: #6c757d;
}

.quick-filter-active {
    background: #007bff !important;
    color: white !important;
}
</style>

<script>
let allPorts = [];
let currentSelection = null;

// تحميل الموانئ عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من فتح النافذة من نافذة حجز الشحنة
    const parentWindow = localStorage.getItem('parentWindow');
    const searchType = localStorage.getItem('portSearchType');

    if (parentWindow === 'cargo_shipment' && searchType) {
        // إظهار مؤشر بصري
        const typeText = searchType === 'origin' ? 'الشحن' : 'الوصول';
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-info alert-dismissible fade show mb-3';
        alertDiv.innerHTML = `
            <i class="fas fa-info-circle me-2"></i>
            <strong>وضع الاختيار:</strong> يتم البحث عن ميناء ${typeText}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // إدراج في بداية المحتوى
        const container = document.querySelector('.container-fluid');
        container.insertBefore(alertDiv, container.firstChild);

        // تفعيل الفلتر المناسب
        if (searchType === 'origin') {
            document.getElementById('purposeFilter').value = 'origin';
        } else if (searchType === 'destination') {
            document.getElementById('purposeFilter').value = 'destination';
        }
    }

    loadInitialPorts();
});

// تحميل الموانئ الأولية
function loadInitialPorts() {
    searchPorts();
}

// البحث في الموانئ
function searchPorts() {
    const searchTerm = document.getElementById('searchInput').value;
    const continent = document.getElementById('continentFilter').value;
    const region = document.getElementById('regionFilter').value;
    const type = document.getElementById('typeFilter').value;
    
    showLoading(true);
    
    const params = new URLSearchParams();
    if (searchTerm) params.append('search', searchTerm);
    if (continent) params.append('continent', continent);
    if (region) params.append('region', region);
    if (type) params.append('port_type', type);
    
    fetch(`/shipments/api/search-ports?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allPorts = data.ports || [];
                displayPorts(allPorts);
                updateResultsCount(allPorts.length);
            } else {
                showError('خطأ في البحث: ' + (data.message || 'خطأ غير معروف'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('خطأ في الاتصال بالخادم');
        })
        .finally(() => {
            showLoading(false);
        });
}

// عرض الموانئ
function displayPorts(ports) {
    const container = document.getElementById('resultsGrid');
    const noResults = document.getElementById('noResultsSection');
    
    if (!ports || ports.length === 0) {
        container.innerHTML = '';
        noResults.style.display = 'block';
        return;
    }
    
    noResults.style.display = 'none';
    
    const html = ports.map(port => `
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="port-card">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div>
                        <span class="port-code">${port.port_code}</span>
                        ${port.major_port ? '<span class="port-major ms-2">رئيسي</span>' : ''}
                    </div>
                    <button class="btn btn-sm btn-outline-warning" onclick="addToFavorites(${port.id}, '${port.port_code}')">
                        <i class="fas fa-heart"></i>
                    </button>
                </div>

                <h6 class="mb-1">${port.port_name}</h6>
                ${port.port_name_arabic ? `<div class="text-muted small mb-1">${port.port_name_arabic}</div>` : ''}

                <div class="port-location mb-2">
                    <i class="fas fa-map-marker-alt me-1"></i>
                    ${port.city}, ${port.country}
                    ${port.region ? `<span class="text-muted">• ${port.region}</span>` : ''}
                </div>

                ${port.cargo_types ? `
                    <div class="port-details mb-3">
                        <i class="fas fa-boxes me-1"></i>
                        ${port.cargo_types}
                    </div>
                ` : ''}

                <!-- أزرار الاختيار المباشر -->
                <div class="port-actions d-flex gap-2">
                    <button class="btn btn-success btn-sm flex-fill" onclick="selectPortAs(${port.id}, '${port.port_code}', '${port.port_name}', '${port.country}', '${port.city}', 'origin')">
                        <i class="fas fa-ship me-1"></i>
                        ميناء شحن
                    </button>
                    <button class="btn btn-info btn-sm flex-fill" onclick="selectPortAs(${port.id}, '${port.port_code}', '${port.port_name}', '${port.country}', '${port.city}', 'destination')">
                        <i class="fas fa-anchor me-1"></i>
                        ميناء وصول
                    </button>
                </div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = `<div class="row">${html}</div>`;
}

// اختيار ميناء مع تحديد الغرض
function selectPortAs(id, code, name, country, city, purpose) {
    currentSelection = { id, code, name, country, city, purpose };

    // إضافة إلى المفضلة تلقائياً
    addToFavorites(id, code);

    // حفظ التصنيف في قاعدة البيانات
    savePortClassification(id, code, purpose);

    // إظهار رسالة نجاح
    const purposeText = purpose === 'origin' ? 'ميناء شحن' : 'ميناء وصول';
    showSuccess(`تم اختيار ${name} كـ ${purposeText}`);

    // حفظ الاختيار في قاعدة البيانات
    fetch('/shipments/api/save-port-selection', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            port_id: id,
            port_code: code,
            port_name: name,
            country: country,
            city: city,
            purpose: purpose
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ تم حفظ الاختيار بنجاح');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في حفظ الاختيار:', error);
    });

    // إرسال البيانات للنافذة الأصلية إذا كانت مفتوحة من نافذة حجز الشحنة
    const parentWindow = localStorage.getItem('parentWindow');
    const searchType = localStorage.getItem('portSearchType');

    if (parentWindow === 'cargo_shipment' && window.opener) {
        window.opener.postMessage({
            type: 'portSelected',
            portId: id,
            portCode: code,
            portName: name,
            country: country,
            city: city,
            searchType: searchType || purpose
        }, '*');

        // إغلاق النافذة بعد ثانيتين
        setTimeout(() => {
            window.close();
        }, 2000);
    }

    // إضافة تأثير بصري
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check me-1"></i>تم الاختيار';
    button.classList.add('btn-outline-success');

    setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('btn-outline-success');
    }, 2000);
}

// اختيار ميناء عام (للتوافق مع الكود القديم)
function selectPort(id, code, name, country, city) {
    currentSelection = { id, code, name, country, city };
    addToFavorites(id, code);
    showSuccess(`تم اختيار ${name} - ${country}`);
}

// إضافة إلى المفضلة
function addToFavorites(portId, portCode) {
    fetch('/shipments/api/add-favorite-port', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
            port_id: portId,
            port_code: portCode 
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('تم إضافة الميناء إلى المفضلة');
        } else {
            showError('خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('خطأ في إضافة الميناء');
    });
}

// حفظ تصنيف الميناء
function savePortClassification(portId, portCode, purpose) {
    fetch('/shipments/api/classify-port', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            port_id: portId,
            port_code: portCode,
            purpose: purpose
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(`تم تصنيف الميناء كـ ${purpose}`);
        } else {
            console.error('خطأ في تصنيف الميناء:', data.message);
        }
    })
    .catch(error => {
        console.error('Error classifying port:', error);
    });
}

// فلترة سريعة حسب الغرض
function quickFilter(purpose) {
    // إزالة التفعيل من جميع الأزرار
    document.querySelectorAll('.btn-outline-success, .btn-outline-info').forEach(btn => {
        btn.classList.remove('quick-filter-active');
    });

    // تفعيل الزر المختار
    event.target.classList.add('quick-filter-active');

    // تحديث فلتر الغرض
    document.getElementById('purposeFilter').value = purpose;

    // تشغيل البحث
    searchPorts();
}

// البحث السريع
function quickSearch(filter) {
    // إزالة التفعيل من جميع الأزرار
    document.querySelectorAll('.btn-outline-primary').forEach(btn => {
        btn.classList.remove('quick-filter-active');
    });

    // تفعيل الزر المختار
    event.target.classList.add('quick-filter-active');

    if (filter === 'major') {
        document.getElementById('typeFilter').value = '1';
    } else if (filter === 'Middle East') {
        document.getElementById('regionFilter').value = 'Middle East';
    }

    searchPorts();
}

// عرض المفضلة
function showFavorites() {
    showLoading(true);
    
    fetch('/shipments/api/favorite-ports')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allPorts = data.ports || [];
                displayPorts(allPorts);
                updateResultsCount(allPorts.length);
            } else {
                showError('خطأ في جلب المفضلة: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('خطأ في جلب المفضلة');
        })
        .finally(() => {
            showLoading(false);
        });
}

// عرض التوصيات
function showRecommendations() {
    showLoading(true);
    
    fetch('/shipments/api/ai-port-recommendations')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allPorts = data.recommendations || [];
                displayPorts(allPorts);
                updateResultsCount(allPorts.length);
            } else {
                showError('خطأ في جلب التوصيات: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('خطأ في جلب التوصيات');
        })
        .finally(() => {
            showLoading(false);
        });
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('continentFilter').value = '';
    document.getElementById('regionFilter').value = '';
    document.getElementById('typeFilter').value = '';
    document.getElementById('purposeFilter').value = '';

    document.querySelectorAll('.quick-filter-active').forEach(btn => {
        btn.classList.remove('quick-filter-active');
    });

    searchPorts();
}

// إضافة ميناء مخصص
function addCustomPort() {
    const modal = new bootstrap.Modal(document.getElementById('customPortModal'));
    modal.show();
}

// حفظ ميناء مخصص
document.getElementById('customPortForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('/shipments/api/add-custom-port', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('تم إضافة الميناء المخصص بنجاح');
            bootstrap.Modal.getInstance(document.getElementById('customPortModal')).hide();
            searchPorts(); // إعادة تحميل القائمة
        } else {
            showError('خطأ في إضافة الميناء: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('خطأ في إضافة الميناء');
    });
});

// وظائف مساعدة
function showLoading(show) {
    document.getElementById('loadingSection').style.display = show ? 'block' : 'none';
}

function updateResultsCount(count) {
    document.getElementById('resultsCount').textContent = `${count} ميناء`;
}

function showSuccess(message) {
    alert('✅ ' + message);
}

function showError(message) {
    alert('❌ ' + message);
}

// البحث المباشر
document.getElementById('searchInput').addEventListener('input', function() {
    clearTimeout(window.searchTimeout);
    window.searchTimeout = setTimeout(() => {
        searchPorts();
    }, 500);
});
</script>
{% endblock %}
