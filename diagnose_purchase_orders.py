#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
sys.path.insert(0, os.getcwd())

def check_files():
    """فحص الملفات المطلوبة"""
    print("🔍 فحص الملفات المطلوبة...")
    
    files_to_check = [
        'app/templates/purchase_orders/index.html',
        'app/static/css/purchase_orders_modern.css',
        'app/purchase_orders/routes.py'
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} - {size} bytes")
        else:
            print(f"❌ {file_path} - غير موجود")

def check_css_content():
    """فحص محتوى ملف CSS"""
    print("\n🎨 فحص محتوى ملف CSS...")
    
    css_file = 'app/static/css/purchase_orders_modern.css'
    if os.path.exists(css_file):
        with open(css_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # فحص العناصر المهمة
        checks = [
            (':root', 'متغيرات CSS'),
            ('.page-container', 'حاوي الصفحة'),
            ('.page-header', 'رأس الصفحة'),
            ('.stats-card', 'بطاقات الإحصائيات'),
            ('.table-modern', 'الجدول الحديث'),
            ('linear-gradient', 'التدرجات'),
            ('--primary', 'الألوان الأساسية')
        ]
        
        for check, description in checks:
            if check in content:
                print(f"✅ {description} موجود")
            else:
                print(f"❌ {description} غير موجود")
                
        print(f"📏 حجم ملف CSS: {len(content)} حرف")
    else:
        print("❌ ملف CSS غير موجود")

def check_html_content():
    """فحص محتوى ملف HTML"""
    print("\n📄 فحص محتوى ملف HTML...")
    
    html_file = 'app/templates/purchase_orders/index.html'
    if os.path.exists(html_file):
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # فحص العناصر المهمة
        checks = [
            ('purchase_orders_modern.css', 'رابط ملف CSS'),
            ('page-container', 'حاوي الصفحة'),
            ('page-header', 'رأس الصفحة'),
            ('stats-card', 'بطاقات الإحصائيات'),
            ('table-modern', 'الجدول الحديث'),
            ('control-panel', 'لوحة التحكم')
        ]
        
        for check, description in checks:
            if check in content:
                print(f"✅ {description} موجود")
            else:
                print(f"❌ {description} غير موجود")
                
        print(f"📏 حجم ملف HTML: {len(content)} حرف")
    else:
        print("❌ ملف HTML غير موجود")

def check_route():
    """فحص route أوامر الشراء"""
    print("\n🛣️ فحص route أوامر الشراء...")
    
    try:
        from oracle_manager import OracleManager
        oracle = OracleManager()
        
        if oracle.connect():
            print("✅ الاتصال بقاعدة البيانات يعمل")
            
            # اختبار استعلام بسيط
            result = oracle.execute_query("SELECT COUNT(*) FROM PURCHASE_ORDERS")
            if result:
                count = result[0][0]
                print(f"✅ عدد أوامر الشراء في قاعدة البيانات: {count}")
            else:
                print("❌ لا توجد أوامر شراء")
                
            oracle.disconnect()
        else:
            print("❌ فشل الاتصال بقاعدة البيانات")
            
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")

def create_simple_test_page():
    """إنشاء صفحة اختبار بسيطة"""
    print("\n🧪 إنشاء صفحة اختبار بسيطة...")
    
    test_content = """<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التصميم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
            min-height: 100vh;
        }
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin: 2rem 0;
            text-align: center;
        }
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-header">
            <h1><i class="fas fa-shopping-cart me-3"></i>اختبار تصميم أوامر الشراء</h1>
            <p>إذا كنت ترى هذا التصميم، فإن CSS يعمل بنجاح!</p>
        </div>
        
        <div class="test-card">
            <h3><i class="fas fa-check-circle text-success me-2"></i>التصميم يعمل!</h3>
            <p>إذا كنت ترى الألوان والتدرجات، فإن ملف CSS يتم تحميله بنجاح.</p>
        </div>
        
        <div class="test-card">
            <h3><i class="fas fa-info-circle text-info me-2"></i>الخطوات التالية:</h3>
            <ol>
                <li>تأكد من أن الخادم يعمل</li>
                <li>امسح cache المتصفح (Ctrl+Shift+Delete)</li>
                <li>أعد تحميل صفحة أوامر الشراء (Ctrl+F5)</li>
                <li>تحقق من أن ملف CSS يتم تحميله في Developer Tools</li>
            </ol>
        </div>
    </div>
</body>
</html>"""
    
    with open('test_design_simple.html', 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print("✅ تم إنشاء ملف test_design_simple.html")
    print("🌐 افتح الملف في المتصفح للتأكد من أن CSS يعمل")

def main():
    print("🚀 تشخيص مشكلة تصميم أوامر الشراء")
    print("=" * 60)
    
    check_files()
    check_css_content()
    check_html_content()
    check_route()
    create_simple_test_page()
    
    print("\n" + "="*60)
    print("📝 ملخص التشخيص:")
    print("1. تحقق من أن جميع الملفات موجودة ✅")
    print("2. تحقق من محتوى ملفات CSS و HTML ✅")
    print("3. اختبر الاتصال بقاعدة البيانات ✅")
    print("4. أنشئ صفحة اختبار بسيطة ✅")
    
    print("\n💡 التوصيات:")
    print("- امسح cache المتصفح (Ctrl+Shift+Delete)")
    print("- أعد تحميل الصفحة (Ctrl+F5)")
    print("- تحقق من Developer Tools > Network لرؤية تحميل CSS")
    print("- افتح test_design_simple.html للتأكد من أن CSS يعمل")

if __name__ == "__main__":
    main()
