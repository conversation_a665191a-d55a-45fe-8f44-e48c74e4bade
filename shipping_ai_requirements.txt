# متطلبات النظام الذكي لشركات الشحن
# Smart Shipping Companies System Requirements

scikit-learn>=1.3.0
pandas>=2.0.0
numpy>=1.24.0
nltk>=3.8
fuzzywuzzy>=0.18.0
python-Levenshtein>=0.21.0
requests>=2.31.0
beautifulsoup4>=4.12.0
selenium>=4.15.0
aiohttp>=3.8.0
lxml>=4.9.0
html5lib>=1.1
cssselect>=1.2.0
redis>=4.5.0
cachetools>=5.3.0
cryptography>=41.0.0
pycryptodome>=3.18.0
python-dateutil>=2.8.0
pytz>=2023.3
Pillow>=10.0.0
qrcode>=7.4.0
python-barcode>=0.14.0
openpyxl>=3.1.0
xlsxwriter>=3.1.0
reportlab>=4.0.0
urllib3>=2.0.0
certifi>=2023.7.0
concurrent-futures>=3.1.1
asyncio>=3.4.3
validators>=0.22.0
email-validator>=2.0.0
transformers>=4.30.0
torch>=2.0.0
sentence-transformers>=2.2.0
sqlalchemy>=2.0.0
alembic>=1.11.0
httpx>=0.24.0
playwright>=1.37.0
geopy>=2.3.0
folium>=0.14.0
plotly>=5.15.0
matplotlib>=3.7.0
seaborn>=0.12.0
