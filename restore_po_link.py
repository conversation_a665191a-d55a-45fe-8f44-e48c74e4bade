#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعادة ربط أمر الشراء بالشحنة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def restore_po_link():
    """إعادة ربط أمر الشراء بالشحنة"""
    print("🔗 إعادة ربط أمر الشراء بالشحنة...")
    
    try:
        from oracle_manager import get_oracle_manager
        db_manager = get_oracle_manager()
        
        if not db_manager.connect():
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        shipment_id = 141
        po_id = 61
        
        # ربط الشحنة بأمر الشراء
        update_shipment_query = """
            UPDATE cargo_shipments 
            SET purchase_order_id = :1,
                updated_at = SYSDATE
            WHERE id = :2
        """
        
        db_manager.execute_update(update_shipment_query, [po_id, shipment_id])
        print(f"✅ تم ربط الشحنة {shipment_id} بأمر الشراء {po_id}")
        
        # ربط أمر الشراء بالشحنة
        update_po_query = """
            UPDATE PURCHASE_ORDERS 
            SET IS_USED = 1,
                USED_AT = SYSDATE,
                USED_IN_SHIPMENT_ID = :1,
                UPDATED_AT = SYSDATE
            WHERE ID = :2
        """
        
        db_manager.execute_update(update_po_query, [shipment_id, po_id])
        print(f"✅ تم ربط أمر الشراء {po_id} بالشحنة {shipment_id}")
        
        db_manager.commit()
        print("💾 تم حفظ التغييرات")
        
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الربط: {e}")
        try:
            if 'db_manager' in locals():
                db_manager.rollback()
                db_manager.close()
        except:
            pass
        return False

if __name__ == "__main__":
    restore_po_link()
