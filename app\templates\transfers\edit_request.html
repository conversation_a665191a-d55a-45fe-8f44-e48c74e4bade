{% extends "base.html" %}

{% block title %}تعديل طلب الحوالة{% endblock %}

{% block extra_css %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<style>
.form-section {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    overflow: hidden;
}

.section-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 1.5rem;
    margin: 0;
}

.section-body {
    padding: 1.5rem;
}

.required-field::after {
    content: " *";
    color: #dc3545;
}

.btn-add-beneficiary {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.btn-add-beneficiary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    color: white;
}

.alert-info {
    background-color: #e3f2fd;
    border-color: #2196f3;
    color: #1976d2;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">
                <i class="fas fa-edit me-2 text-primary"></i>
                تعديل طلب الحوالة
            </h2>
            <p class="text-muted mb-0">تعديل تفاصيل طلب الحوالة المالية</p>
        </div>
        <div>
            <a href="{{ url_for('transfers.list_requests') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
            </a>
        </div>
    </div>

    <!-- Request Info Alert -->
    <div class="alert alert-info" role="alert" id="requestInfo">
        <i class="fas fa-info-circle me-2"></i>
        <strong>معلومات الطلب:</strong> <span id="requestNumber">جاري التحميل...</span>
        - <strong>الحالة:</strong> <span id="requestStatus">جاري التحميل...</span>
        - <strong>تاريخ الإنشاء:</strong> <span id="requestDate">جاري التحميل...</span>
    </div>

    <form id="editRequestForm" novalidate>
        <!-- تفاصيل الحوالة -->
        <div class="form-section">
            <div class="section-header">
                <h5 class="mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    تفاصيل الحوالة
                </h5>
            </div>
            <div class="section-body">
                <div class="row">
                    <!-- الفرع -->
                    <div class="col-md-6 mb-3">
                        <label for="branch_id" class="form-label required-field">الفرع</label>
                        <select class="form-select" id="branch_id" name="branch_id" required>
                            <option value="">اختر الفرع...</option>
                        </select>
                        <div class="invalid-feedback">يرجى اختيار الفرع</div>
                    </div>

                    <!-- نوع التحويل -->
                    <div class="col-md-6 mb-3">
                        <label for="transfer_type" class="form-label required-field">نوع التحويل</label>
                        <select class="form-select" id="transfer_type" name="transfer_type" required>
                            <option value="">اختر نوع التحويل...</option>
                            <option value="bank">بنك</option>
                            <option value="money_changer">صراف</option>
                        </select>
                        <div class="invalid-feedback">يرجى اختيار نوع التحويل</div>
                    </div>

                    <!-- الصراف/البنك -->
                    <div class="col-md-6 mb-3">
                        <label for="money_changer_bank_id" class="form-label required-field">الصراف/البنك</label>
                        <select class="form-select" id="money_changer_bank_id" name="money_changer_bank_id" required disabled>
                            <option value="">اختر الصراف أو البنك...</option>
                        </select>
                        <div class="invalid-feedback">يرجى اختيار الصراف أو البنك</div>
                    </div>

                    <!-- المبلغ -->
                    <div class="col-md-6 mb-3">
                        <label for="amount" class="form-label required-field">المبلغ</label>
                        <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="1" required>
                        <div class="invalid-feedback">يرجى إدخال مبلغ صحيح</div>
                    </div>

                    <!-- العملة -->
                    <div class="col-md-6 mb-3">
                        <label for="currency_id" class="form-label required-field">العملة</label>
                        <select class="form-select" id="currency_id" name="currency_id" required>
                            <option value="">اختر العملة...</option>
                        </select>
                        <div class="invalid-feedback">يرجى اختيار العملة</div>
                    </div>

                    <!-- الغرض -->
                    <div class="col-md-6 mb-3">
                        <label for="purpose" class="form-label required-field">الغرض من التحويل</label>
                        <input type="text" class="form-control" id="purpose" name="purpose" required>
                        <div class="invalid-feedback">يرجى إدخال الغرض من التحويل</div>
                    </div>

                    <!-- ملاحظات -->
                    <div class="col-12 mb-3">
                        <label for="notes" class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- بيانات المستفيد -->
        <div class="form-section" id="beneficiaryForm">
            <div class="section-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    بيانات المستفيد
                </h5>
            </div>
            <div class="section-body">
                <!-- خيار البحث عن مستفيد موجود -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>تغيير المستفيد:</strong> يمكنك البحث عن مستفيد آخر أو تعديل بيانات المستفيد الحالي
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <label class="form-label">البحث الذكي عن مستفيد آخر:</label>
                                <input type="text" class="form-control" id="smart_beneficiary_search"
                                       placeholder="ابحث بالاسم، البنك، رقم الحساب، IBAN، الهاتف..."
                                       autocomplete="off">
                                <small class="form-text text-muted">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    يمكنك البحث بأي معلومة عن المستفيد: الاسم، البنك، رقم الحساب، IBAN، الهاتف، أو العنوان
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نموذج بيانات المستفيد -->
                <div id="beneficiaryFormFields">
                    <div class="row">
                    <!-- اسم المستفيد -->
                    <div class="col-md-6 mb-3">
                        <label for="beneficiary_name" class="form-label required-field">اسم المستفيد</label>
                        <input type="text" class="form-control" id="beneficiary_name" name="beneficiary_name" required>
                        <div class="invalid-feedback">يرجى إدخال اسم المستفيد</div>
                    </div>

                    <!-- عنوان المستفيد -->
                    <div class="col-md-6 mb-3">
                        <label for="beneficiary_address" class="form-label">عنوان المستفيد</label>
                        <input type="text" class="form-control" id="beneficiary_address" name="beneficiary_address">
                    </div>

                    <!-- نوع المستفيد -->
                    <div class="col-md-6 mb-3">
                        <label for="beneficiary_type" class="form-label">نوع المستفيد</label>
                        <select class="form-select" id="beneficiary_type" name="beneficiary_type">
                            <option value="individual">فرد</option>
                            <option value="company">شركة</option>
                            <option value="supplier">مورد</option>
                            <option value="employee">موظف</option>
                        </select>
                    </div>

                    <!-- رقم الحساب -->
                    <div class="col-md-6 mb-3">
                        <label for="bank_account" class="form-label required-field">رقم الحساب</label>
                        <input type="text" class="form-control" id="bank_account" name="bank_account" required>
                        <div class="invalid-feedback">يرجى إدخال رقم الحساب</div>
                    </div>

                    <!-- اسم البنك -->
                    <div class="col-md-6 mb-3">
                        <label for="bank_name" class="form-label required-field">اسم البنك</label>
                        <input type="text" class="form-control" id="bank_name" name="bank_name" required>
                        <div class="invalid-feedback">يرجى إدخال اسم البنك</div>
                    </div>

                    <!-- فرع البنك -->
                    <div class="col-md-6 mb-3">
                        <label for="bank_branch" class="form-label">فرع البنك</label>
                        <input type="text" class="form-control" id="bank_branch" name="bank_branch">
                    </div>

                    <!-- دولة البنك -->
                    <div class="col-md-6 mb-3">
                        <label for="bank_country" class="form-label">دولة البنك</label>
                        <input type="text" class="form-control" id="bank_country" name="bank_country">
                    </div>

                    <!-- IBAN -->
                    <div class="col-md-6 mb-3">
                        <label for="iban" class="form-label">IBAN</label>
                        <input type="text" class="form-control" id="iban" name="iban">
                    </div>

                    <!-- SWIFT Code -->
                    <div class="col-md-6 mb-3">
                        <label for="swift_code" class="form-label">SWIFT Code</label>
                        <input type="text" class="form-control" id="swift_code" name="swift_code">
                    </div>

                    <!-- رقم الهوية -->
                    <div class="col-md-6 mb-3">
                        <label for="identification_number" class="form-label">رقم الهوية/السجل التجاري</label>
                        <input type="text" class="form-control" id="identification_number" name="identification_number">
                    </div>

                    <!-- رقم الهاتف -->
                    <div class="col-md-6 mb-3">
                        <label for="beneficiary_phone" class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="beneficiary_phone" name="beneficiary_phone">
                    </div>

                    <!-- البريد الإلكتروني -->
                    <div class="col-md-6 mb-3">
                        <label for="beneficiary_email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="beneficiary_email" name="beneficiary_email">
                    </div>
                </div>
                </div> <!-- إغلاق beneficiaryFormFields -->
            </div>
        </div>

        <!-- أزرار الحفظ -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-end gap-3">
                    <a href="{{ url_for('transfers.list_requests') }}" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>حفظ التعديلات
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mb-0">جاري حفظ التعديلات...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Select2 JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
let requestId = null;

$(document).ready(function() {
    console.log('🚀 تهيئة صفحة تعديل الطلب...');
    
    // الحصول على ID الطلب من URL
    const pathParts = window.location.pathname.split('/');
    requestId = pathParts[pathParts.length - 1];
    
    console.log('📋 معرف الطلب:', requestId);
    
    // تحميل البيانات الأولية بالتسلسل
    loadBranches().then(() => {
        return loadCurrencies();
    }).then(() => {
        return loadBeneficiaries();
    }).then(() => {
        return loadRequestData();
    }).catch((error) => {
        console.error('❌ خطأ في تحميل البيانات:', error);
        showAlert('حدث خطأ في تحميل البيانات', 'danger');
    });
    
    // ربط أحداث النموذج
    $('#transfer_type').change(function() {
        loadMoneyChangersOrBanks();
    });

    // تأخير تهيئة Select2 حتى يتم تحميل البيانات
    setTimeout(function() {
        // تهيئة Select2 للمستفيدين مع البحث
        $('#existing_beneficiary').select2({
            theme: 'bootstrap-5',
            placeholder: 'ابحث عن مستفيد موجود...',
            allowClear: true,
            width: '100%',
            language: {
                noResults: function() {
                    return "لا توجد نتائج";
                },
                searching: function() {
                    return "جاري البحث...";
                },
                loadingMore: function() {
                    return "جاري تحميل المزيد...";
                }
            }
        });

        console.log('✅ تم تهيئة Select2 للمستفيدين');
    }, 1000);

    $('#existing_beneficiary').change(function() {
        const selectedBeneficiaryId = $(this).val();
        if (selectedBeneficiaryId) {
            loadBeneficiaryData(selectedBeneficiaryId);
        }
    });

    $('#editRequestForm').submit(function(e) {
        e.preventDefault();
        console.log('📝 تم إرسال النموذج - بدء عملية التحديث');
        updateRequest();
    });
});

// تحميل بيانات الطلب
function loadRequestData() {
    console.log('📥 تحميل بيانات الطلب...');

    return $.get(`/transfers/api/requests/${requestId}`)
        .done(function(response) {
            if (response.success) {
                console.log('✅ تم تحميل بيانات الطلب:', response.data);
                fillFormWithRequestData(response.data);
            } else {
                console.error('❌ فشل في تحميل بيانات الطلب:', response.message);
                showAlert('فشل في تحميل بيانات الطلب: ' + response.message, 'danger');
            }
        })
        .fail(function(xhr) {
            console.error('❌ خطأ في تحميل بيانات الطلب:', xhr);
            showAlert('حدث خطأ أثناء تحميل بيانات الطلب', 'danger');
        });
}

// ملء النموذج ببيانات الطلب
function fillFormWithRequestData(data) {
    console.log('📝 ملء النموذج ببيانات الطلب:', data);

    // تحديث معلومات الطلب في الأعلى
    $('#requestNumber').text(data.request_number || 'غير محدد');
    $('#requestStatus').text(getStatusText(data.status || 'pending'));
    $('#requestDate').text(data.created_at ? new Date(data.created_at).toLocaleString('ar-SA') : 'غير محدد');

    // ملء تفاصيل الحوالة
    if (data.branch_id) {
        $('#branch_id').val(data.branch_id);
        console.log('✅ تم تعيين الفرع:', data.branch_id);
    } else {
        console.log('⚠️ لا يوجد فرع محدد');
    }

    if (data.transfer_type) {
        $('#transfer_type').val(data.transfer_type);
        console.log('✅ تم تعيين نوع التحويل:', data.transfer_type);
        // تحميل الصرافين/البنوك بناءً على النوع
        loadMoneyChangersOrBanks().then(() => {
            if (data.money_changer_bank_id) {
                $('#money_changer_bank_id').val(data.money_changer_bank_id);
                console.log('✅ تم تعيين الصراف/البنك:', data.money_changer_bank_id);
            }
        });
    } else {
        console.log('⚠️ لا يوجد نوع تحويل محدد');
    }

    if (data.amount) {
        $('#amount').val(data.amount);
        console.log('✅ تم تعيين المبلغ:', data.amount);
    }

    // البحث عن العملة بالكود إذا لم يكن هناك currency_id
    if (data.currency_id) {
        $('#currency_id').val(data.currency_id);
        console.log('✅ تم تعيين العملة بالـ ID:', data.currency_id);
    } else if (data.currency) {
        // انتظار قليل للتأكد من تحميل العملات
        setTimeout(() => {
            const currencyOption = $('#currency_id option').filter(function() {
                return $(this).text().includes(data.currency);
            });
            if (currencyOption.length > 0) {
                $('#currency_id').val(currencyOption.val());
                console.log('✅ تم تعيين العملة بالكود:', data.currency, '-> ID:', currencyOption.val());
            } else {
                console.log('⚠️ لم يتم العثور على العملة:', data.currency);
                console.log('📋 العملات المتاحة:', $('#currency_id option').map(function() { return $(this).text(); }).get());
            }
        }, 500);
    }

    $('#purpose').val(data.purpose || '');
    $('#notes').val(data.notes || '');

    // ملء بيانات المستفيد
    $('#beneficiary_name').val(data.beneficiary_name || '');
    $('#beneficiary_address').val(data.beneficiary_address || '');
    $('#beneficiary_type').val(data.beneficiary_type || 'individual');
    $('#bank_account').val(data.bank_account || '');
    $('#bank_name').val(data.bank_name || '');
    $('#bank_branch').val(data.bank_branch || '');
    $('#bank_country').val(data.bank_country || '');
    $('#iban').val(data.iban || '');
    $('#swift_code').val(data.swift_code || '');
    $('#identification_number').val(data.identification_number || '');
    $('#beneficiary_phone').val(data.phone || '');
    $('#beneficiary_email').val(data.email || '');

    console.log('📋 بيانات المستفيد:');
    console.log('  - الاسم:', data.beneficiary_name);
    console.log('  - العنوان:', data.beneficiary_address);
    console.log('  - النوع:', data.beneficiary_type);
    console.log('  - رقم الحساب:', data.bank_account);
    console.log('  - اسم البنك:', data.bank_name);

    console.log('✅ تم ملء النموذج بنجاح');
}

// تحديث الطلب
function updateRequest() {
    console.log('💾 تحديث الطلب...');

    if (!validateForm()) {
        console.log('❌ فشل في التحقق من صحة النموذج');
        return;
    }

    const formData = {
        branch_id: $('#branch_id').val(),
        transfer_type: $('#transfer_type').val(),
        money_changer_bank_id: $('#money_changer_bank_id').val(),
        amount: parseFloat($('#amount').val()),
        currency_id: $('#currency_id').val(),
        purpose: $('#purpose').val(),
        notes: $('#notes').val(),
        beneficiary_data: {
            beneficiary_name: $('#beneficiary_name').val(),
            beneficiary_address: $('#beneficiary_address').val(),
            type: $('#beneficiary_type').val(),
            bank_account: $('#bank_account').val(),
            bank_name: $('#bank_name').val(),
            bank_branch: $('#bank_branch').val(),
            bank_country: $('#bank_country').val(),
            iban: $('#iban').val(),
            swift_code: $('#swift_code').val(),
            identification_number: $('#identification_number').val(),
            phone: $('#beneficiary_phone').val(),
            email: $('#beneficiary_email').val()
        }
    };

    console.log('📤 البيانات المرسلة:', formData);
    console.log('💰 المبلغ الجديد:', formData.amount);
    
    $('#loadingModal').modal('show');

    console.log('🌐 إرسال طلب AJAX إلى:', `/transfers/api/requests/${requestId}`);

    $.ajax({
        url: `/transfers/api/requests/${requestId}`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        timeout: 30000, // 30 ثانية timeout
        success: function(response) {
            console.log('✅ استجابة الخادم:', response);
            $('#loadingModal').modal('hide');

            if (response.success) {
                console.log('🎉 تم التحديث بنجاح!');
                showAlert('تم تحديث الطلب بنجاح', 'success');

                // إعادة توجيه فورية
                console.log('🔄 إعادة توجيه إلى قائمة الطلبات...');
                setTimeout(function() {
                    try {
                        window.location.href = '/transfers/list-requests';
                        console.log('✅ تم تنفيذ إعادة التوجيه');
                    } catch (error) {
                        console.error('❌ خطأ في إعادة التوجيه:', error);
                        // محاولة بديلة
                        window.location.replace('/transfers/list-requests');
                    }
                }, 1000);
            } else {
                console.error('❌ فشل في التحديث:', response.message);
                showAlert('فشل في تحديث الطلب: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ خطأ في الطلب:', xhr);
            console.error('❌ Status:', status);
            console.error('❌ Error:', error);
            $('#loadingModal').modal('hide');

            if (status === 'timeout') {
                showAlert('انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.', 'warning');
            } else {
                const response = xhr.responseJSON;
                showAlert(response?.message || 'حدث خطأ أثناء تحديث الطلب', 'danger');
            }
        },
        complete: function() {
            console.log('🏁 انتهى طلب AJAX');
            // التأكد من إخفاء modal في جميع الحالات
            setTimeout(function() {
                $('#loadingModal').modal('hide');
            }, 500);
        }
    });
}

// دوال مساعدة محسنة
function loadBranches() {
    console.log('🏢 تحميل الفروع...');
    return $.get('/transfers/api/branches')
        .done(function(response) {
            if (response.success) {
                const branchSelect = $('#branch_id');
                branchSelect.empty().append('<option value="">اختر الفرع...</option>');
                response.data.forEach(function(branch) {
                    const branchName = branch.name_ar || branch.name || 'فرع غير محدد';
                    branchSelect.append(`<option value="${branch.id}">${branchName}</option>`);
                });
                console.log('✅ تم تحميل الفروع:', response.data.length);
            } else {
                console.error('❌ فشل في تحميل الفروع:', response.message);
            }
        })
        .fail(function() {
            console.error('❌ خطأ في تحميل الفروع');
        });
}

function loadCurrencies() {
    console.log('💰 تحميل العملات...');
    return $.get('/transfers/api/currencies')
        .done(function(response) {
            if (response.success) {
                const currencySelect = $('#currency_id');
                currencySelect.empty().append('<option value="">اختر العملة...</option>');
                response.data.forEach(function(currency) {
                    const currencyName = currency.name || currency.name_ar || 'عملة غير محددة';
                    const currencyCode = currency.code || '';
                    const displayText = currencyCode ? `${currencyName} (${currencyCode})` : currencyName;
                    currencySelect.append(`<option value="${currency.id}">${displayText}</option>`);
                });
                console.log('✅ تم تحميل العملات:', response.data.length);
            } else {
                console.error('❌ فشل في تحميل العملات:', response.message);
            }
        })
        .fail(function() {
            console.error('❌ خطأ في تحميل العملات');
        });
}

function loadMoneyChangersOrBanks() {
    const transferType = $('#transfer_type').val();
    const select = $('#money_changer_bank_id');

    console.log('🏦 تحميل الصرافين/البنوك لنوع:', transferType);

    select.prop('disabled', true).empty().append('<option value="">جاري التحميل...</option>');

    if (transferType) {
        const endpoint = '/transfers/api/money-changers-banks';

        return $.get(endpoint)
            .done(function(response) {
                select.empty().append('<option value="">اختر...</option>');

                if (response.success) {
                    // فلترة البيانات حسب النوع
                    const filteredData = response.data.filter(function(item) {
                        return item.type === transferType;
                    });

                    filteredData.forEach(function(item) {
                        select.append(`<option value="${item.id}">${item.name}</option>`);
                    });
                    select.prop('disabled', false);
                    console.log('✅ تم تحميل الصرافين/البنوك:', filteredData.length, 'من النوع:', transferType);
                } else {
                    console.error('❌ فشل في تحميل الصرافين/البنوك:', response.message);
                }
            })
            .fail(function() {
                console.error('❌ خطأ في تحميل الصرافين/البنوك');
                select.empty().append('<option value="">خطأ في التحميل</option>');
            });
    } else {
        select.empty().append('<option value="">اختر نوع التحويل أولاً</option>');
        return Promise.resolve();
    }
}

function validateForm() {
    const form = document.getElementById('editRequestForm');
    form.classList.add('was-validated');
    return form.checkValidity();
}

function getStatusText(status) {
    const statusMap = {
        'pending': 'معلق',
        'approved': 'معتمد',
        'rejected': 'مرفوض',
        'processing': 'قيد المعالجة',
        'completed': 'مكتمل'
    };
    return statusMap[status] || status;
}

// تحميل قائمة المستفيدين
function loadBeneficiaries() {
    console.log('👥 تحميل قائمة المستفيدين...');

    return $.get('/transfers/api/beneficiaries')
        .done(function(response) {
            if (response.success) {
                const select = $('#existing_beneficiary');
                select.empty().append('<option value="">ابحث عن مستفيد موجود...</option>');

                response.data.forEach(function(beneficiary) {
                    const displayText = `${beneficiary.beneficiary_name} - ${beneficiary.bank_name || 'غير محدد'}`;
                    select.append(`<option value="${beneficiary.id}">${displayText}</option>`);
                });

                // تحديث Select2 بعد إضافة البيانات
                select.trigger('change');

                console.log('✅ تم تحميل المستفيدين:', response.data.length);
            } else {
                console.error('❌ فشل في تحميل المستفيدين:', response.message);
            }
        })
        .fail(function(xhr, status, error) {
            console.error('❌ خطأ في تحميل المستفيدين:', error);
            console.error('Response:', xhr.responseText);
            showAlert('فشل في تحميل قائمة المستفيدين', 'warning');
        });
}

// تحميل بيانات مستفيد محدد
function loadBeneficiaryData(beneficiaryId) {
    console.log('👤 تحميل بيانات المستفيد:', beneficiaryId);

    $.get(`/transfers/api/beneficiary/${beneficiaryId}`)
        .done(function(response) {
            if (response.success) {
                const data = response.data;
                console.log('✅ تم تحميل بيانات المستفيد:', data);

                // ملء النموذج ببيانات المستفيد
                $('#beneficiary_name').val(data.beneficiary_name || '');
                $('#beneficiary_address').val(data.beneficiary_address || '');
                $('#beneficiary_type').val(data.type || 'individual');
                $('#bank_account').val(data.bank_account || '');
                $('#bank_name').val(data.bank_name || '');
                $('#bank_branch').val(data.bank_branch || '');
                $('#bank_country').val(data.bank_country || '');
                $('#iban').val(data.iban || '');
                $('#swift_code').val(data.swift_code || '');
                $('#identification_number').val(data.identification_number || '');
                $('#beneficiary_phone').val(data.phone || '');
                $('#beneficiary_email').val(data.email || '');

                showAlert('تم تحميل بيانات المستفيد بنجاح', 'success');
            } else {
                console.error('❌ فشل في تحميل بيانات المستفيد:', response.message);
                showAlert('فشل في تحميل بيانات المستفيد: ' + response.message, 'danger');
            }
        })
        .fail(function() {
            console.error('❌ خطأ في تحميل بيانات المستفيد');
            showAlert('حدث خطأ أثناء تحميل بيانات المستفيد', 'danger');
        });
}

// مسح اختيار المستفيد
function clearBeneficiarySelection() {
    console.log('🗑️ مسح اختيار المستفيد');

    $('#existing_beneficiary').val('').trigger('change');

    // لا نمسح الحقول، فقط نعيد تعيين القائمة المنسدلة
    showAlert('تم مسح اختيار المستفيد. يمكنك الآن تعديل البيانات يدوياً أو اختيار مستفيد آخر.', 'info');
}

function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('.container-fluid').prepend(alertHtml);

    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

// تهيئة البحث الذكي للمستفيدين
let smartSearch;

$(document).ready(function() {
    // تهيئة البحث الذكي
    const searchInput = document.getElementById('smart_beneficiary_search');
    if (searchInput) {
        smartSearch = new SmartBeneficiarySearch(searchInput, {
            minChars: 2,
            maxResults: 8,
            debounceDelay: 250,
            placeholder: 'ابحث بالاسم، البنك، رقم الحساب، IBAN، الهاتف...'
        });

        // عند اختيار مستفيد
        searchInput.addEventListener('beneficiarySelected', function(e) {
            const beneficiary = e.detail;
            console.log('🎯 تم اختيار مستفيد:', beneficiary);

            // ملء حقول المستفيد
            fillBeneficiaryFields(beneficiary);

            // إظهار رسالة نجاح
            showAlert(`تم اختيار المستفيد: ${beneficiary.beneficiary_name}`, 'success');
        });

        // عند مسح البحث
        searchInput.addEventListener('beneficiaryCleared', function(e) {
            console.log('🗑️ تم مسح البحث');
            showAlert('تم مسح اختيار المستفيد', 'info');
        });
    }
});

// ملء حقول المستفيد بالبيانات المختارة
function fillBeneficiaryFields(beneficiary) {
    // ملء الحقول الأساسية
    $('#beneficiary_name').val(beneficiary.beneficiary_name || '');
    $('#beneficiary_address').val(beneficiary.beneficiary_address || '');
    $('#beneficiary_type').val(beneficiary.type || 'supplier');

    // ملء البيانات المصرفية
    $('#bank_account').val(beneficiary.bank_account || '');
    $('#bank_name').val(beneficiary.bank_name || '');
    $('#bank_branch').val(beneficiary.bank_branch || '');
    $('#bank_country').val(beneficiary.bank_country || '');
    $('#iban').val(beneficiary.iban || '');
    $('#swift_code').val(beneficiary.swift_code || '');

    // ملء البيانات الإضافية - تصحيح أسماء الحقول
    $('#identification_number').val(beneficiary.identification_number || '');
    $('#beneficiary_phone').val(beneficiary.phone || '');  // تصحيح: beneficiary_phone
    $('#beneficiary_email').val(beneficiary.email || '');  // تصحيح: beneficiary_email

    // تحديث واجهة المستخدم
    updateFieldValidation();

    // تمرير سلس إلى حقول المستفيد
    $('html, body').animate({
        scrollTop: $('#beneficiaryFormFields').offset().top - 100
    }, 500);
}

// تحديث حالة التحقق من الحقول
function updateFieldValidation() {
    // إزالة حالات الخطأ السابقة
    $('.form-control').removeClass('is-invalid is-valid');

    // التحقق من الحقول المطلوبة
    const requiredFields = ['beneficiary_name'];
    requiredFields.forEach(fieldId => {
        const field = $(`#${fieldId}`);
        if (field.val().trim()) {
            field.addClass('is-valid');
        }
    });
}

// مسح اختيار المستفيد (تحديث الدالة الموجودة)
function clearBeneficiarySelection() {
    console.log('🗑️ مسح اختيار المستفيد');

    if (smartSearch) {
        smartSearch.clearSearch();
    }

    // مسح جميع حقول المستفيد
    $('#beneficiaryFormFields input').val('');
    $('#beneficiary_type').val('supplier');

    // إزالة حالات التحقق
    $('.form-control').removeClass('is-invalid is-valid');

    showAlert('تم مسح جميع بيانات المستفيد', 'info');
}
</script>

<!-- تحميل JavaScript للبحث الذكي -->
<script src="{{ url_for('static', filename='js/smart-beneficiary-search.js') }}"></script>

{% endblock %}
