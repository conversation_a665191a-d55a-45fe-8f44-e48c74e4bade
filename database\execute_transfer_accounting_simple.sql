-- =====================================================
-- إجراء تنفيذ الحوالة المحاسبي (مبسط)
-- Execute Transfer Accounting Procedure (Simple)
-- =====================================================

-- 1. إنشاء إجراء تنفيذ الحوالة المحاسبي
CREATE OR REPLACE PROCEDURE EXECUTE_TRANSFER_ACCOUNTING(
    p_transfer_id IN NUMBER,
    p_money_changer_id IN NUMBER,
    p_total_amount IN NUMBER,
    p_currency_code IN VARCHAR2,
    p_supplier_distributions IN CLOB,
    p_user_id IN NUMBER DEFAULT 1,
    p_ip_address IN VARCHAR2 DEFAULT NULL,
    p_session_id IN VARCHAR2 DEFAULT NULL
) AS
    v_start_time TIMESTAMP := CURRENT_TIMESTAMP;
    v_execution_time NUMBER;
    v_json_array JSON_ARRAY_T;
    v_json_obj JSON_OBJECT_T;
    v_supplier_id NUMBER;
    v_supplier_amount NUMBER;
    v_supplier_name VARCHAR2(200);
    v_money_changer_balance NUMBER;
    v_total_distributed NUMBER := 0;
    v_error_msg VARCHAR2(4000);
    
BEGIN
    -- بداية المعاملة
    SAVEPOINT start_transfer_execution;
    
    -- تسجيل بداية العملية
    LOG_TRANSFER_ACTIVITY(
        p_transfer_id => p_transfer_id,
        p_activity_type => 'EXECUTION',
        p_description => 'بدء تنفيذ الحوالة المحاسبي',
        p_operation_details => 'Money Changer ID: ' || p_money_changer_id || ', Total Amount: ' || p_total_amount || ' ' || p_currency_code,
        p_created_by => p_user_id,
        p_ip_address => p_ip_address,
        p_session_id => p_session_id
    );
    
    -- 1. التحقق من صحة البيانات
    IF p_transfer_id IS NULL OR p_transfer_id <= 0 THEN
        RAISE_APPLICATION_ERROR(-20001, 'معرف الحوالة غير صحيح');
    END IF;
    
    IF p_money_changer_id IS NULL OR p_money_changer_id <= 0 THEN
        RAISE_APPLICATION_ERROR(-20002, 'معرف الصراف غير صحيح');
    END IF;
    
    IF p_total_amount IS NULL OR p_total_amount <= 0 THEN
        RAISE_APPLICATION_ERROR(-20003, 'مبلغ الحوالة غير صحيح');
    END IF;
    
    IF p_supplier_distributions IS NULL THEN
        RAISE_APPLICATION_ERROR(-20004, 'توزيعات الموردين مطلوبة');
    END IF;
    
    -- 2. التحقق من رصيد الصراف
    BEGIN
        SELECT current_balance INTO v_money_changer_balance
        FROM CURRENT_BALANCES
        WHERE entity_type_code = 'MONEY_CHANGER' 
        AND entity_id = p_money_changer_id 
        AND currency_code = p_currency_code;
        
        IF v_money_changer_balance < p_total_amount THEN
            RAISE_APPLICATION_ERROR(-20005, 'رصيد الصراف غير كافي. الرصيد الحالي: ' || v_money_changer_balance || ', المطلوب: ' || p_total_amount);
        END IF;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RAISE_APPLICATION_ERROR(-20006, 'رصيد الصراف غير موجود');
    END;
    
    -- 3. معالجة توزيعات الموردين
    BEGIN
        v_json_array := JSON_ARRAY_T.parse(p_supplier_distributions);
        
        FOR i IN 0 .. v_json_array.get_size - 1 LOOP
            v_json_obj := JSON_OBJECT_T(v_json_array.get(i));
            v_supplier_id := v_json_obj.get_number('supplier_id');
            v_supplier_amount := v_json_obj.get_number('amount');
            
            -- التحقق من وجود المورد
            BEGIN
                SELECT name_ar INTO v_supplier_name
                FROM suppliers
                WHERE id = v_supplier_id;
            EXCEPTION
                WHEN NO_DATA_FOUND THEN
                    RAISE_APPLICATION_ERROR(-20007, 'المورد غير موجود: ' || v_supplier_id);
            END;
            
            -- إضافة التوزيع
            INSERT INTO transfer_supplier_dist (
                transfer_id,
                supplier_id,
                amount,
                currency_code,
                created_by
            ) VALUES (
                p_transfer_id,
                v_supplier_id,
                v_supplier_amount,
                p_currency_code,
                p_user_id
            );
            
            -- تحديث رصيد المورد (مدين - زيادة)
            MERGE INTO CURRENT_BALANCES cb
            USING (SELECT 1 as dummy FROM dual) d
            ON (cb.entity_type_code = 'SUPPLIER' AND cb.entity_id = v_supplier_id AND cb.currency_code = p_currency_code)
            WHEN MATCHED THEN
                UPDATE SET
                    debit_amount = debit_amount + v_supplier_amount,
                    current_balance = current_balance + v_supplier_amount,
                    total_transactions_count = total_transactions_count + 1,
                    last_transaction_date = CURRENT_TIMESTAMP,
                    updated_at = CURRENT_TIMESTAMP,
                    updated_by = p_user_id
            WHEN NOT MATCHED THEN
                INSERT (
                    entity_type_code, entity_id, currency_code,
                    opening_balance, debit_amount, credit_amount, current_balance,
                    total_transactions_count, last_transaction_date,
                    created_at, updated_at, created_by, updated_by,
                    description
                ) VALUES (
                    'SUPPLIER', v_supplier_id, p_currency_code,
                    0, v_supplier_amount, 0, v_supplier_amount,
                    1, CURRENT_TIMESTAMP,
                    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, p_user_id, p_user_id,
                    'رصيد المورد: ' || v_supplier_name
                );
            
            -- تسجيل النشاط
            LOG_TRANSFER_ACTIVITY(
                p_transfer_id => p_transfer_id,
                p_activity_type => 'DISTRIBUTION',
                p_description => 'توزيع مبلغ على المورد: ' || v_supplier_name,
                p_amount_after => v_supplier_amount,
                p_currency_code => p_currency_code,
                p_entity_type => 'SUPPLIER',
                p_entity_id => v_supplier_id,
                p_entity_name => v_supplier_name,
                p_created_by => p_user_id,
                p_ip_address => p_ip_address,
                p_session_id => p_session_id
            );
            
            v_total_distributed := v_total_distributed + v_supplier_amount;
        END LOOP;
        
    EXCEPTION
        WHEN OTHERS THEN
            RAISE_APPLICATION_ERROR(-20008, 'خطأ في معالجة توزيعات الموردين: ' || SQLERRM);
    END;
    
    -- 4. التحقق من تطابق المبالغ
    IF ABS(v_total_distributed - p_total_amount) > 0.01 THEN
        RAISE_APPLICATION_ERROR(-20009, 'مجموع التوزيعات (' || v_total_distributed || ') لا يطابق مبلغ الحوالة (' || p_total_amount || ')');
    END IF;
    
    -- 5. تحديث رصيد الصراف (دائن - تقليل)
    UPDATE CURRENT_BALANCES SET
        credit_amount = credit_amount + p_total_amount,
        current_balance = current_balance - p_total_amount,
        total_transactions_count = total_transactions_count + 1,
        last_transaction_date = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP,
        updated_by = p_user_id
    WHERE entity_type_code = 'MONEY_CHANGER' 
    AND entity_id = p_money_changer_id 
    AND currency_code = p_currency_code;
    
    -- 6. تحديث حالة الحوالة
    UPDATE transfers SET
        status = 'executed',
        execution_date = CURRENT_TIMESTAMP,
        executed_by = p_user_id,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_transfer_id;
    
    -- حساب وقت التنفيذ
    v_execution_time := EXTRACT(SECOND FROM (CURRENT_TIMESTAMP - v_start_time)) * 1000;
    
    -- تسجيل نجاح العملية
    LOG_TRANSFER_ACTIVITY(
        p_transfer_id => p_transfer_id,
        p_activity_type => 'EXECUTION',
        p_description => 'تم تنفيذ الحوالة بنجاح',
        p_old_status => 'approved',
        p_new_status => 'executed',
        p_amount_after => p_total_amount,
        p_currency_code => p_currency_code,
        p_operation_details => 'Suppliers: ' || v_json_array.get_size || ', Total Distributed: ' || v_total_distributed,
        p_execution_time_ms => v_execution_time,
        p_created_by => p_user_id,
        p_ip_address => p_ip_address,
        p_session_id => p_session_id
    );
    
    -- تأكيد المعاملة
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        -- التراجع عن جميع التغييرات
        ROLLBACK TO start_transfer_execution;
        
        v_error_msg := SQLERRM;
        v_execution_time := EXTRACT(SECOND FROM (CURRENT_TIMESTAMP - v_start_time)) * 1000;
        
        -- تسجيل الخطأ
        LOG_TRANSFER_ACTIVITY(
            p_transfer_id => p_transfer_id,
            p_activity_type => 'ERROR',
            p_description => 'فشل في تنفيذ الحوالة',
            p_error_message => v_error_msg,
            p_execution_time_ms => v_execution_time,
            p_created_by => p_user_id,
            p_ip_address => p_ip_address,
            p_session_id => p_session_id
        );
        
        COMMIT; -- تأكيد تسجيل الخطأ فقط
        
        -- إعادة رفع الخطأ
        RAISE;
END;
/

-- 2. إنشاء دالة للتحقق من رصيد الصراف
CREATE OR REPLACE FUNCTION CHECK_MONEY_CHANGER_BALANCE(
    p_money_changer_id IN NUMBER,
    p_amount IN NUMBER,
    p_currency_code IN VARCHAR2
) RETURN VARCHAR2 AS
    v_current_balance NUMBER;
    v_result VARCHAR2(4000);
BEGIN
    -- الحصول على الرصيد الحالي
    SELECT current_balance INTO v_current_balance
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'MONEY_CHANGER' 
    AND entity_id = p_money_changer_id 
    AND currency_code = p_currency_code;
    
    -- التحقق من كفاية الرصيد
    IF v_current_balance >= p_amount THEN
        v_result := 'OK: الرصيد كافي. الرصيد الحالي: ' || v_current_balance || ', المطلوب: ' || p_amount || ', المتبقي: ' || (v_current_balance - p_amount);
    ELSE
        v_result := 'ERROR: الرصيد غير كافي. الرصيد الحالي: ' || v_current_balance || ', المطلوب: ' || p_amount || ', النقص: ' || (p_amount - v_current_balance);
    END IF;
    
    RETURN v_result;
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN 'ERROR: رصيد الصراف غير موجود';
    WHEN OTHERS THEN
        RETURN 'ERROR: خطأ في التحقق من الرصيد: ' || SQLERRM;
END;
/

-- 3. إنشاء دالة للتحقق من توزيعات الموردين
CREATE OR REPLACE FUNCTION VALIDATE_SUPPLIER_DISTRIBUTIONS(
    p_transfer_id IN NUMBER,
    p_supplier_distributions IN CLOB
) RETURN VARCHAR2 AS
    v_json_array JSON_ARRAY_T;
    v_json_obj JSON_OBJECT_T;
    v_supplier_id NUMBER;
    v_supplier_amount NUMBER;
    v_total_distributed NUMBER := 0;
    v_transfer_amount NUMBER;
    v_supplier_count NUMBER;
    v_result VARCHAR2(4000);
BEGIN
    -- الحصول على مبلغ الحوالة
    SELECT NVL(net_amount_sent, NVL(net_amount_received, 0)) INTO v_transfer_amount
    FROM transfers
    WHERE id = p_transfer_id;
    
    -- معالجة JSON
    v_json_array := JSON_ARRAY_T.parse(p_supplier_distributions);
    
    FOR i IN 0 .. v_json_array.get_size - 1 LOOP
        v_json_obj := JSON_OBJECT_T(v_json_array.get(i));
        v_supplier_id := v_json_obj.get_number('supplier_id');
        v_supplier_amount := v_json_obj.get_number('amount');
        
        -- التحقق من وجود المورد
        SELECT COUNT(*) INTO v_supplier_count
        FROM suppliers
        WHERE id = v_supplier_id;
        
        IF v_supplier_count = 0 THEN
            RETURN 'ERROR: المورد غير موجود: ' || v_supplier_id;
        END IF;
        
        -- التحقق من صحة المبلغ
        IF v_supplier_amount <= 0 THEN
            RETURN 'ERROR: مبلغ المورد غير صحيح: ' || v_supplier_amount;
        END IF;
        
        v_total_distributed := v_total_distributed + v_supplier_amount;
    END LOOP;
    
    -- التحقق من تطابق المبالغ
    IF ABS(v_total_distributed - v_transfer_amount) > 0.01 THEN
        RETURN 'ERROR: مجموع التوزيعات (' || v_total_distributed || ') لا يطابق مبلغ الحوالة (' || v_transfer_amount || ')';
    END IF;
    
    v_result := 'OK: التوزيعات صحيحة. عدد الموردين: ' || v_json_array.get_size || ', إجمالي المبلغ: ' || v_total_distributed;
    RETURN v_result;
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN 'ERROR: الحوالة غير موجودة';
    WHEN OTHERS THEN
        RETURN 'ERROR: خطأ في التحقق من التوزيعات: ' || SQLERRM;
END;
/

COMMIT;

SELECT 'Execute transfer accounting procedures created successfully' as result FROM DUAL;
