<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ربط أوامر الشراء - نظام الفوجي</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/commission-opening-balances-style.css') }}" rel="stylesheet">
</head>
<body>

    <!-- Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="fas fa-link me-3"></i>
                        ربط أوامر الشراء
                    </h1>
                    <p class="page-subtitle">
                        إدارة ربط أوامر الشراء مع مندوبي المشتريات وحساب العمولات
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{{ url_for('purchase_commissions.index') }}" class="btn btn-light btn-modern">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="breadcrumb-container">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('purchase_commissions.index') }}">عمولات المندوبين</a></li>
                    <li class="breadcrumb-item active" aria-current="page">ربط أوامر الشراء</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="container">
        
        <!-- Control Panel -->
        <div class="control-panel">
            <h5>
                <i class="fas fa-cogs"></i>
                إجراءات سريعة
            </h5>
            <div class="row g-3">
                <div class="col-md-3">
                    <button type="button" class="btn btn-primary btn-modern w-100" data-bs-toggle="modal" data-bs-target="#assignOrderModal">
                        <i class="fas fa-plus"></i>
                        ربط أمر جديد
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-success btn-modern w-100" onclick="calculateAllCommissions()">
                        <i class="fas fa-calculator"></i>
                        حساب جميع العمولات
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-info btn-modern w-100" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث البيانات
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-warning btn-modern w-100" onclick="exportData()">
                        <i class="fas fa-download"></i>
                        تصدير البيانات
                    </button>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="stats-container">
            <div class="row g-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon primary">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-value">{{ stats.total_orders or 0 }}</div>
                        <div class="stat-label">إجمالي الأوامر</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon success">
                            <i class="fas fa-link"></i>
                        </div>
                        <div class="stat-value">{{ stats.total_assignments or 0 }}</div>
                        <div class="stat-label">إجمالي الربط</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon info">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <div class="stat-value">{{ stats.calculated_count or 0 }}</div>
                        <div class="stat-label">محسوبة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon warning">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="stat-value">{{ "{:,.0f}".format(stats.total_commission_amount or 0) }}</div>
                        <div class="stat-label">إجمالي العمولات</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Data Table -->
        <div class="data-table-container">
            <div class="data-table-header">
                <h5 class="data-table-title">
                    <i class="fas fa-table"></i>
                    أوامر الشراء المربوطة
                </h5>
                <div>
                    <button class="btn btn-primary btn-modern btn-sm" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-modern" id="ordersTable">
                    <thead>
                        <tr>
                            <th>رقم الأمر</th>
                            <th>المندوب</th>
                            <th>نسبة العمولة</th>
                            <th>قيمة الأمر</th>
                            <th>العمولة المحسوبة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon primary me-2" style="width: 40px; height: 40px; font-size: 1rem;">
                                        <i class="fas fa-file-invoice"></i>
                                    </div>
                                    <div>
                                        <strong>{{ order[0] }}</strong>
                                        <br><small class="text-muted">{{ order[4].strftime('%Y-%m-%d') if order[4] else 'غير محدد' }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ order[1] }}</strong>
                                    <br><small class="text-muted">{{ order[2] }}</small>
                                </div>
                            </td>
                            <td>
                                <span class="badge badge-modern bg-info">{{ order[3] or 100 }}%</span>
                            </td>
                            <td>
                                <strong class="text-success">{{ "{:,.2f}".format(order[5] or 0) }} ريال</strong>
                            </td>
                            <td>
                                {% if order[6] %}
                                <strong class="text-warning">{{ "{:,.2f}".format(order[6]) }} ريال</strong>
                                {% else %}
                                <span class="text-muted">غير محسوبة</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if order[7] == 'calculated' %}
                                <span class="badge badge-modern bg-primary">محسوبة</span>
                                {% elif order[7] == 'approved' %}
                                <span class="badge badge-modern bg-success">معتمدة</span>
                                {% elif order[7] == 'paid' %}
                                <span class="badge badge-modern bg-warning">مدفوعة</span>
                                {% else %}
                                <span class="badge badge-modern bg-secondary">غير محسوبة</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    {% if not order[6] %}
                                    <a href="{{ url_for('purchase_commissions.auto_calculate_commission', order_id=order[0], rep_id=1) }}"
                                       class="btn btn-success btn-modern btn-sm" title="حساب العمولة">
                                        <i class="fas fa-calculator"></i>
                                    </a>
                                    {% endif %}
                                    <button class="btn btn-info btn-modern btn-sm" onclick="viewOrderDetails({{ order[0] }})" title="التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-warning btn-modern btn-sm" onclick="editAssignment({{ order[0] }}, 1)" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger btn-modern btn-sm" onclick="deleteAssignment({{ order[0] }}, 1)" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

    </div>

    <!-- Assign Order Modal -->
    <div class="modal fade" id="assignOrderModal" tabindex="-1" aria-labelledby="assignOrderModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="assignOrderModalLabel">
                        <i class="fas fa-plus me-2"></i>
                        ربط أمر شراء جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="{{ url_for('purchase_commissions.assign_order') }}">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="purchase_order_id" class="form-label">رقم أمر الشراء *</label>
                            <input type="text" class="form-control" id="purchase_order_id" name="purchase_order_id" required>
                        </div>
                        <div class="mb-3">
                            <label for="rep_id" class="form-label">المندوب *</label>
                            <select class="form-select" id="rep_id" name="rep_id" required>
                                <option value="">اختر المندوب</option>
                                {% for rep in representatives %}
                                <option value="{{ rep[0] }}">{{ rep[1] }} ({{ rep[2] }})</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="commission_percentage" class="form-label">نسبة العمولة (%)</label>
                            <input type="number" class="form-control" id="commission_percentage" name="commission_percentage" 
                                   value="100" min="0" max="100" step="0.01">
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ الربط
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function calculateAllCommissions() {
            showAlert('سيتم حساب جميع العمولات قريباً', 'info');
        }

        function refreshData() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            button.disabled = true;
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                showAlert('تم تحديث البيانات بنجاح', 'success');
                location.reload();
            }, 2000);
        }

        function exportData() {
            showAlert('سيتم تصدير البيانات قريباً', 'info');
        }

        function viewOrderDetails(orderId) {
            showAlert(`سيتم عرض تفاصيل الأمر ${orderId} قريباً`, 'info');
        }

        function editAssignment(orderId, repId) {
            showAlert(`سيتم تعديل ربط الأمر ${orderId} قريباً`, 'info');
        }

        function deleteAssignment(orderId, repId) {
            if (confirm(`هل أنت متأكد من حذف ربط الأمر ${orderId}؟`)) {
                showAlert('تم حذف الربط بنجاح', 'success');
            }
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-modern alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'info' ? 'info-circle' : 'exclamation-triangle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('.container').insertBefore(alert, document.querySelector('.control-panel'));
            
            setTimeout(() => {
                alert.remove();
            }, 3000);
        }

        // تأثيرات hover للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(-5px)';
                });
            });
        });
    </script>

</body>
</html>
