{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-plus-circle me-2"></i>
                    {{ title }}
                </h2>
                <a href="{{ url_for('inventory.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للمخزون
                </a>
            </div>

            <!-- Form -->
            <div class="card">
                <div class="card-body">
                    <form method="POST" novalidate>
                        {{ form.hidden_tag() }}
                        
                        <!-- معلومات أساسية -->
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                {{ form.code.label(class="form-label required") }}
                                {{ form.code(class="form-control") }}
                                {% if form.code.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.code.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                {{ form.name.label(class="form-label required") }}
                                {{ form.name(class="form-control") }}
                                {% if form.name.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.name.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                {{ form.category_id.label(class="form-label") }}
                                {{ form.category_id(class="form-select") }}
                                {% if form.category_id.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.category_id.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                {{ form.description.label(class="form-label") }}
                                {{ form.description(class="form-control") }}
                                {% if form.description.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.description.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- معلومات الوحدة والمخزون -->
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                {{ form.unit.label(class="form-label required") }}
                                {{ form.unit(class="form-control") }}
                                {% if form.unit.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.unit.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                {{ form.current_stock.label(class="form-label") }}
                                {{ form.current_stock(class="form-control") }}
                                {% if form.current_stock.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.current_stock.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                {{ form.minimum_stock.label(class="form-label") }}
                                {{ form.minimum_stock(class="form-control") }}
                                {% if form.minimum_stock.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.minimum_stock.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                {{ form.maximum_stock.label(class="form-label") }}
                                {{ form.maximum_stock(class="form-control") }}
                                {% if form.maximum_stock.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.maximum_stock.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- معلومات التكلفة والسعر -->
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                {{ form.reorder_point.label(class="form-label") }}
                                {{ form.reorder_point(class="form-control") }}
                                {% if form.reorder_point.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.reorder_point.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                {{ form.standard_cost.label(class="form-label") }}
                                {{ form.standard_cost(class="form-control") }}
                                {% if form.standard_cost.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.standard_cost.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                {{ form.location.label(class="form-label") }}
                                {{ form.location(class="form-control") }}
                                {% if form.location.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.location.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                {{ form.barcode.label(class="form-label") }}
                                {{ form.barcode(class="form-control") }}
                                {% if form.barcode.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.barcode.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- حالة الصنف -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.is_active(class="form-check-input") }}
                                    {{ form.is_active.label(class="form-check-label") }}
                                </div>
                                {% if form.is_active.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.is_active.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    {{ form.submit(class="btn btn-primary") }}
                                    <a href="{{ url_for('inventory.index') }}" class="btn btn-secondary">إلغاء</a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Help Section -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    إرشادات إضافة الصنف
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>الحقول المطلوبة:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>كود الصنف (يجب أن يكون فريد)</li>
                            <li><i class="fas fa-check text-success me-2"></i>اسم الصنف</li>
                            <li><i class="fas fa-check text-success me-2"></i>وحدة القياس</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>نصائح:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>استخدم كود واضح ومفهوم</li>
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>حدد الحد الأدنى لتنبيهات المخزون</li>
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>أضف وصف مفصل للصنف</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.required::after {
    content: " *";
    color: red;
}
</style>
{% endblock %}
