{% extends "base.html" %}

{% block title %}تتبع أصناف الشحنة - {{ shipment.shipment_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-boxes me-2"></i>
                        تتبع أصناف الشحنة
                    </h2>
                    <p class="text-muted mb-0">
                        الشحنة: {{ shipment.shipment_number }} - 
                        <span class="badge" style="background-color: {{ shipment.status_color }};">
                            <i class="{{ shipment.status_icon }} me-1"></i>
                            {{ shipment.status_name }}
                        </span>
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('shipments.dashboard_fullscreen') }}"
                       class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة للشحنات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الأصناف -->
    {% if stats %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        إحصائيات حالات الأصناف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for stat in stats %}
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="stat-card" style="border-left: 4px solid {{ stat.status_color }};">
                                <div class="stat-icon" style="color: {{ stat.status_color }};">
                                    <i class="{{ stat.status_icon }}"></i>
                                </div>
                                <div class="stat-details">
                                    <h4>{{ stat.item_count }}</h4>
                                    <p>{{ stat.status_name }}</p>
                                    <small>{{ stat.percentage }}%</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- قائمة الأصناف -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        أصناف الشحنة ({{ items|length }} صنف)
                    </h5>
                </div>
                <div class="card-body">
                    {% if items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم الصنف</th>
                                    <th>الكمية</th>
                                    <th>الحالة</th>
                                    <th>الموقع</th>
                                    <th>آخر تحديث</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in items %}
                                <tr id="item-row-{{ item.id }}" {% if item.is_damaged %}class="table-danger"{% endif %}>
                                    <td>
                                        <strong>{{ item.item_name }}</strong>
                                        {% if item.is_damaged %}
                                        <br><small class="text-danger">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            تالف: {{ item.damage_notes }}
                                        </small>
                                        {% endif %}
                                    </td>
                                    <td>{{ item.quantity }} {{ item.unit or '' }}</td>
                                    <td>
                                        <span class="badge" style="background-color: {{ item.status_color }};">
                                            <i class="{{ item.status_icon }} me-1"></i>
                                            {{ item.status_name }}
                                        </span>
                                    </td>
                                    <td>{{ item.item_location or 'غير محدد' }}</td>
                                    <td>
                                        {% if item.status_updated_at %}
                                        {{ item.status_updated_at.strftime('%Y-%m-%d %H:%M') }}
                                        {% else %}
                                        -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" 
                                                    onclick="updateItemStatus({{ item.id }})"
                                                    title="تحديث الحالة">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-info" 
                                                    onclick="viewItemHistory({{ item.id }})"
                                                    title="عرض التاريخ">
                                                <i class="fas fa-history"></i>
                                            </button>
                                            {% if not item.is_damaged %}
                                            <button class="btn btn-outline-danger" 
                                                    onclick="markItemDamaged({{ item.id }})"
                                                    title="تمييز كتالف">
                                                <i class="fas fa-exclamation-triangle"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                        <h5>لا توجد أصناف في هذه الشحنة</h5>
                        <p class="text-muted">لم يتم إضافة أي أصناف لهذه الشحنة بعد</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تحديث حالة الصنف -->
<div class="modal fade" id="updateStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث حالة الصنف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="updateStatusForm">
                    <input type="hidden" id="itemId">
                    
                    <div class="mb-3">
                        <label for="newStatus" class="form-label">الحالة الجديدة</label>
                        <select class="form-select" id="newStatus" required>
                            <option value="">اختر الحالة</option>
                            {% for status in available_statuses %}
                            <option value="{{ status[0] }}" data-color="{{ status[2] }}" data-icon="{{ status[3] }}">
                                {{ status[1] }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="itemLocation" class="form-label">الموقع (اختياري)</label>
                        <input type="text" class="form-control" id="itemLocation" placeholder="أدخل الموقع الحالي">
                    </div>
                    
                    <div class="mb-3">
                        <label for="statusNotes" class="form-label">ملاحظات (اختياري)</label>
                        <textarea class="form-control" id="statusNotes" rows="3" placeholder="أدخل أي ملاحظات"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="submitStatusUpdate()">تحديث</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal عرض تاريخ الصنف -->
<div class="modal fade" id="historyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تاريخ حالات الصنف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="historyContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تمييز كتالف -->
<div class="modal fade" id="damageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تمييز الصنف كتالف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="damageForm">
                    <input type="hidden" id="damageItemId">
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        هذا الإجراء سيقوم بتمييز الصنف كتالف وتغيير حالته تلقائياً
                    </div>
                    
                    <div class="mb-3">
                        <label for="damageNotes" class="form-label">وصف التلف *</label>
                        <textarea class="form-control" id="damageNotes" rows="4" 
                                  placeholder="اكتب وصفاً مفصلاً عن التلف..." required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" onclick="submitDamageReport()">تأكيد التلف</button>
            </div>
        </div>
    </div>
</div>

<style>
.stat-card {
    background: white;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    font-size: 2rem;
    width: 60px;
    text-align: center;
}

.stat-details h4 {
    margin: 0;
    font-weight: 700;
}

.stat-details p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.stat-details small {
    color: #28a745;
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,0.075);
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}
</style>

<script>
let currentItemId = null;

function updateItemStatus(itemId) {
    currentItemId = itemId;
    document.getElementById('itemId').value = itemId;
    
    // إعادة تعيين النموذج
    document.getElementById('updateStatusForm').reset();
    document.getElementById('itemId').value = itemId;
    
    // فتح المودال
    new bootstrap.Modal(document.getElementById('updateStatusModal')).show();
}

function submitStatusUpdate() {
    const itemId = document.getElementById('itemId').value;
    const newStatus = document.getElementById('newStatus').value;
    const location = document.getElementById('itemLocation').value;
    const notes = document.getElementById('statusNotes').value;
    
    if (!newStatus) {
        alert('يرجى اختيار الحالة الجديدة');
        return;
    }
    
    fetch(`/shipments/api/item/${itemId}/update-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            status: newStatus,
            location: location,
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم تحديث حالة الصنف بنجاح');
            location.reload();
        } else {
            alert('خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في تحديث حالة الصنف');
    });
}

function viewItemHistory(itemId) {
    // فتح المودال
    new bootstrap.Modal(document.getElementById('historyModal')).show();
    
    // جلب التاريخ
    fetch(`/shipments/api/item/${itemId}/history`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayHistory(data.history);
        } else {
            document.getElementById('historyContent').innerHTML = 
                '<div class="alert alert-danger">خطأ في تحميل التاريخ</div>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('historyContent').innerHTML = 
            '<div class="alert alert-danger">حدث خطأ في تحميل التاريخ</div>';
    });
}

function displayHistory(history) {
    let html = '';
    
    if (history.length === 0) {
        html = '<div class="alert alert-info">لا يوجد تاريخ لهذا الصنف</div>';
    } else {
        html = '<div class="timeline">';
        history.forEach(item => {
            html += `
                <div class="timeline-item">
                    <div class="timeline-marker" style="background-color: ${item.new_status_color};">
                        <i class="${item.new_status_icon}"></i>
                    </div>
                    <div class="timeline-content">
                        <h6>${item.new_status_name}</h6>
                        <p class="text-muted mb-1">${new Date(item.status_date).toLocaleString('ar-SA')}</p>
                        ${item.location ? `<p><i class="fas fa-map-marker-alt me-1"></i>${item.location}</p>` : ''}
                        ${item.notes ? `<p>${item.notes}</p>` : ''}
                        ${item.auto_updated ? '<small class="badge bg-info">تحديث تلقائي</small>' : '<small class="badge bg-primary">تحديث يدوي</small>'}
                    </div>
                </div>
            `;
        });
        html += '</div>';
    }
    
    document.getElementById('historyContent').innerHTML = html;
}

function markItemDamaged(itemId) {
    document.getElementById('damageItemId').value = itemId;
    document.getElementById('damageNotes').value = '';
    
    new bootstrap.Modal(document.getElementById('damageModal')).show();
}

function submitDamageReport() {
    const itemId = document.getElementById('damageItemId').value;
    const damageNotes = document.getElementById('damageNotes').value;
    
    if (!damageNotes.trim()) {
        alert('يرجى إدخال وصف التلف');
        return;
    }
    
    fetch(`/shipments/api/item/${itemId}/mark-damaged`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            damage_notes: damageNotes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم تمييز الصنف كتالف');
            location.reload();
        } else {
            alert('خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في تمييز الصنف كتالف');
    });
}
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

.timeline-content {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 3px solid #dee2e6;
}

.timeline-content h6 {
    margin-bottom: 0.5rem;
    color: #495057;
}
</style>
{% endblock %}
