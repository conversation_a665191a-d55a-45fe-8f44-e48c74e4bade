<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نموذج جدولي سريع - Excel-like Table</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Handsontable CSS -->
    <link href="https://cdn.jsdelivr.net/npm/handsontable@12.4.0/dist/handsontable.full.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            direction: rtl;
            text-align: right;
        }
        
        .handsontable {
            direction: ltr;
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 14px;
        }
        
        .handsontable td {
            text-align: center;
            vertical-align: middle;
            border: 1px solid #ddd;
        }
        
        .handsontable th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-weight: 600;
            text-align: center;
            border: 1px solid #ddd;
        }
        
        .readonly-cell {
            background-color: #f8f9fa !important;
            color: #6c757d;
        }
        
        .calculated-cell {
            background-color: #e3f2fd !important;
            font-weight: 600;
        }
        
        .handsontable .htInvalid {
            background-color: #ffebee !important;
            color: #c62828;
        }
        
        .toolbar-btn {
            background: #0d6efd;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0.25rem;
        }
        
        .toolbar-btn:hover {
            background: #0b5ed7;
            transform: translateY(-1px);
        }
        
        .toolbar-btn.success {
            background: #198754;
        }
        
        .toolbar-btn.warning {
            background: #ffc107;
            color: #000;
        }
        
        .toolbar-btn.danger {
            background: #dc3545;
        }
        
        .stats-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .stat-item {
            display: inline-block;
            margin: 0 1rem;
            font-weight: 600;
        }
        
        .stat-value {
            color: #0d6efd;
        }
    </style>
</head>
<body>
    <div class="container-fluid" dir="rtl">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3 text-center">
                    <i class="fas fa-table me-2"></i>
                    نموذج جدولي احترافي - Excel-like Table
                </h1>
            </div>
        </div>
        
        <!-- Toolbar -->
        <div class="row mb-3">
            <div class="col-12 text-center">
                <button class="toolbar-btn" onclick="addRows(10)">
                    <i class="fas fa-plus me-1"></i>
                    إضافة 10 صفوف
                </button>
                <button class="toolbar-btn" onclick="addRows(50)">
                    <i class="fas fa-plus me-1"></i>
                    إضافة 50 صف
                </button>
                <button class="toolbar-btn warning" onclick="clearSelected()">
                    <i class="fas fa-eraser me-1"></i>
                    مسح المحدد
                </button>
                <button class="toolbar-btn danger" onclick="deleteRows()">
                    <i class="fas fa-trash me-1"></i>
                    حذف الصفوف
                </button>
                <button class="toolbar-btn success" onclick="saveData()">
                    <i class="fas fa-save me-1"></i>
                    حفظ البيانات
                </button>
            </div>
        </div>
        
        <!-- Table Container -->
        <div class="row">
            <div class="col-12">
                <div id="excelTable" style="height: 500px; overflow: auto;"></div>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="row">
            <div class="col-12">
                <div class="stats-container">
                    <div class="stat-item">
                        <i class="fas fa-table text-primary"></i>
                        إجمالي الصفوف: <span class="stat-value" id="totalRows">0</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-check-circle text-success"></i>
                        صفوف مكتملة: <span class="stat-value" id="completedRows">0</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-calculator text-info"></i>
                        إجمالي الكمية: <span class="stat-value" id="totalQuantity">0.000</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-money-bill text-warning"></i>
                        إجمالي المبلغ: <span class="stat-value" id="totalAmount">0.000 ر.س</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Handsontable JS -->
    <script src="https://cdn.jsdelivr.net/npm/handsontable@12.4.0/dist/handsontable.full.min.js"></script>

    <script>
        let hot; // Handsontable instance
        let itemsCache = {}; // كاش للأصناف
        
        // بيانات تجريبية للأصناف
        const sampleItems = {
            'ITEM001': { id: 1, code: 'ITEM001', name: 'لابتوب HP', unit: 'قطعة', price: 2500.000 },
            'ITEM002': { id: 2, code: 'ITEM002', name: 'طابعة Canon', unit: 'قطعة', price: 800.000 },
            'ITEM003': { id: 3, code: 'ITEM003', name: 'ورق A4', unit: 'علبة', price: 25.000 },
            'ITEM004': { id: 4, code: 'ITEM004', name: 'قلم حبر', unit: 'قطعة', price: 2.500 },
            'ITEM005': { id: 5, code: 'ITEM005', name: 'كيبورد لاسلكي', unit: 'قطعة', price: 150.000 }
        };
        
        // إعداد الجدول
        const tableConfig = {
            data: generateEmptyData(20),
            colHeaders: [
                'رقم الصنف',
                'اسم الصنف',
                'الوحدة',
                'الكمية',
                'السعر',
                'الإجمالي',
                'تاريخ الإنتاج',
                'تاريخ الانتهاء',
                'ملاحظات'
            ],
            columns: [
                {
                    // رقم الصنف - مع بحث تلقائي
                    type: 'autocomplete',
                    source: function(query, process) {
                        searchItems(query, process);
                    },
                    strict: false,
                    allowInvalid: true,
                    className: 'htMiddle htCenter'
                },
                {
                    // اسم الصنف - للقراءة فقط
                    type: 'text',
                    readOnly: true,
                    className: 'htMiddle htCenter readonly-cell'
                },
                {
                    // الوحدة - للقراءة فقط
                    type: 'text',
                    readOnly: true,
                    className: 'htMiddle htCenter readonly-cell'
                },
                {
                    // الكمية - رقمي
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0,0.000'
                    },
                    className: 'htMiddle htCenter'
                },
                {
                    // السعر - رقمي
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0,0.000'
                    },
                    className: 'htMiddle htCenter'
                },
                {
                    // الإجمالي - محسوب تلقائياً
                    type: 'numeric',
                    numericFormat: {
                        pattern: '0,0.000'
                    },
                    readOnly: true,
                    className: 'htMiddle htCenter calculated-cell'
                },
                {
                    // تاريخ الإنتاج
                    type: 'date',
                    dateFormat: 'YYYY-MM-DD',
                    className: 'htMiddle htCenter'
                },
                {
                    // تاريخ الانتهاء
                    type: 'date',
                    dateFormat: 'YYYY-MM-DD',
                    className: 'htMiddle htCenter'
                },
                {
                    // ملاحظات
                    type: 'text',
                    className: 'htMiddle htCenter'
                }
            ],
            rowHeaders: true,
            colWidths: [120, 200, 80, 100, 100, 120, 120, 120, 150],
            height: 500,
            licenseKey: 'non-commercial-and-evaluation',
            
            selectionMode: 'multiple',
            fillHandle: {
                direction: 'vertical',
                autoInsertRow: true
            },
            
            contextMenu: {
                items: {
                    'row_above': { name: 'إدراج صف أعلى' },
                    'row_below': { name: 'إدراج صف أسفل' },
                    'remove_row': { name: 'حذف الصف' },
                    'separator1': '---------',
                    'copy': { name: 'نسخ' },
                    'cut': { name: 'قص' },
                    'paste': { name: 'لصق' }
                }
            },
            
            afterChange: function(changes, source) {
                if (source !== 'loadData' && changes) {
                    handleCellChanges(changes);
                }
            },
            
            beforeKeyDown: function(event) {
                handleKeyboardShortcuts(event);
            }
        };
        
        // تهيئة الصفحة
        $(document).ready(function() {
            // تهيئة الجدول
            const container = document.getElementById('excelTable');
            hot = new Handsontable(container, tableConfig);
            
            // تحميل البيانات التجريبية
            itemsCache = sampleItems;
            
            // تحديث الإحصائيات
            updateStatistics();
            
            showAlert('تم تحميل النموذج الجدولي بنجاح! جرب كتابة ITEM001 في أول خلية.', 'success');
        });
        
        // إنشاء بيانات فارغة
        function generateEmptyData(rows) {
            const data = [];
            for (let i = 0; i < rows; i++) {
                data.push(['', '', '', 0, 0, 0, '', '', '']);
            }
            return data;
        }
        
        // البحث في الأصناف
        function searchItems(query, process) {
            if (!query || query.length < 1) {
                process([]);
                return;
            }
            
            const results = Object.keys(itemsCache).filter(code => 
                code.toLowerCase().includes(query.toLowerCase()) ||
                itemsCache[code].name.toLowerCase().includes(query.toLowerCase())
            ).slice(0, 10);
            
            process(results);
        }
        
        // معالجة تغييرات الخلايا
        function handleCellChanges(changes) {
            changes.forEach(([row, prop, oldValue, newValue]) => {
                if (prop === 0 && newValue && itemsCache[newValue]) {
                    // تم تحديد صنف - ملء البيانات التلقائية
                    const item = itemsCache[newValue];
                    hot.setDataAtCell([
                        [row, 1, item.name],
                        [row, 2, item.unit],
                        [row, 4, item.price]
                    ]);
                    calculateRowTotal(row);
                } else if (prop === 3 || prop === 4) {
                    // تم تغيير الكمية أو السعر
                    calculateRowTotal(row);
                }
            });
            updateStatistics();
        }
        
        // حساب إجمالي الصف
        function calculateRowTotal(row) {
            const quantity = hot.getDataAtCell(row, 3) || 0;
            const price = hot.getDataAtCell(row, 4) || 0;
            const total = quantity * price;
            hot.setDataAtCell(row, 5, total);
        }
        
        // تحديث الإحصائيات
        function updateStatistics() {
            const data = hot.getData();
            let totalRows = 0;
            let completedRows = 0;
            let totalQuantity = 0;
            let totalAmount = 0;
            
            data.forEach(row => {
                if (row[0]) {
                    totalRows++;
                    if (row[0] && row[1] && row[3] > 0) {
                        completedRows++;
                    }
                    totalQuantity += parseFloat(row[3]) || 0;
                    totalAmount += parseFloat(row[5]) || 0;
                }
            });
            
            $('#totalRows').text(totalRows);
            $('#completedRows').text(completedRows);
            $('#totalQuantity').text(totalQuantity.toFixed(3));
            $('#totalAmount').text(totalAmount.toFixed(3) + ' ر.س');
        }
        
        // إضافة صفوف
        function addRows(count) {
            const currentData = hot.getData();
            const newRows = generateEmptyData(count);
            hot.loadData([...currentData, ...newRows]);
            showAlert(`تم إضافة ${count} صف جديد`, 'success');
        }
        
        // مسح المحدد
        function clearSelected() {
            const selected = hot.getSelected();
            if (selected && selected.length > 0) {
                const [row1, col1, row2, col2] = selected[0];
                for (let row = row1; row <= row2; row++) {
                    for (let col = col1; col <= col2; col++) {
                        hot.setDataAtCell(row, col, '');
                    }
                }
                updateStatistics();
                showAlert('تم مسح البيانات المحددة', 'info');
            } else {
                showAlert('يرجى تحديد الخلايا المراد مسحها', 'warning');
            }
        }
        
        // حذف الصفوف
        function deleteRows() {
            const selected = hot.getSelected();
            if (selected && selected.length > 0) {
                const [row1, , row2] = selected[0];
                const rowsToDelete = Math.abs(row2 - row1) + 1;
                if (confirm(`هل أنت متأكد من حذف ${rowsToDelete} صف؟`)) {
                    hot.alter('remove_row', Math.min(row1, row2), rowsToDelete);
                    updateStatistics();
                    showAlert(`تم حذف ${rowsToDelete} صف`, 'success');
                }
            } else {
                showAlert('يرجى تحديد الصفوف المراد حذفها', 'warning');
            }
        }
        
        // حفظ البيانات
        function saveData() {
            const data = hot.getData();
            const validRows = data.filter(row => row[0] && row[1] && row[3] > 0);
            
            if (validRows.length === 0) {
                showAlert('لا توجد بيانات صحيحة للحفظ', 'warning');
                return;
            }
            
            // محاكاة الحفظ
            console.log('البيانات المراد حفظها:', validRows);
            showAlert(`تم حفظ ${validRows.length} صف بنجاح!`, 'success');
        }
        
        // معالجة اختصارات لوحة المفاتيح
        function handleKeyboardShortcuts(event) {
            if (event.ctrlKey && event.keyCode === 83) { // Ctrl + S
                event.preventDefault();
                saveData();
            }
        }
        
        // عرض التنبيهات
        function showAlert(message, type) {
            const alertDiv = $(`
                <div class="alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed" 
                     style="top: 20px; right: 20px; z-index: 10000; min-width: 300px;" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `);
            
            $('body').append(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
    </script>
</body>
</html>
