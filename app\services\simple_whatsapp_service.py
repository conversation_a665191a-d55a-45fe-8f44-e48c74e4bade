"""
خدمة واتساب بسيطة بدون مكتبات معقدة
تركز على إرسال الرسائل النصية فقط
"""

import os
import urllib.parse
from datetime import datetime
from typing import Dict


class SimpleWhatsAppService:
    """خدمة واتساب بسيطة"""
    
    def __init__(self):
        """تهيئة الخدمة"""
        pass
    
    def create_whatsapp_message(self, order_data: Dict) -> str:
        """إنشاء رسالة واتساب نصية"""
        
        message = f"""🚚 أمر تسليم جديد

📋 رقم الأمر: {order_data.get('order_number', 'غير محدد')}
📦 رقم التتبع: {order_data.get('tracking_number', 'غير محدد')}
🚢 رقم الحجز: {order_data.get('booking_number', 'غير محدد')}
📍 موقع التسليم: {order_data.get('delivery_location', 'غير محدد')}
📅 التاريخ المطلوب: {order_data.get('expected_completion_date', 'غير محدد')}

💡 يرجى استخدام زر "معاينة" في النظام لتحميل أمر التسليم كـ PDF

شكراً لتعاونكم 🙏"""
        
        return message
    
    def create_whatsapp_url(self, phone_number: str, message: str) -> str:
        """إنشاء رابط واتساب ويب"""
        
        # تنظيف رقم الهاتف
        clean_phone = self.clean_phone_number(phone_number)
        
        # ترميز الرسالة
        encoded_message = urllib.parse.quote(message)
        
        # إنشاء الرابط
        whatsapp_url = f"https://web.whatsapp.com/send?phone={clean_phone}&text={encoded_message}"
        
        return whatsapp_url
    
    def clean_phone_number(self, phone: str) -> str:
        """تنظيف رقم الهاتف"""
        if not phone:
            return ""
        
        # إزالة المسافات والرموز
        clean_phone = ''.join(filter(str.isdigit, phone))
        
        # إضافة رمز الدولة إذا لم يكن موجود
        if clean_phone.startswith('5') and len(clean_phone) == 9:
            clean_phone = '966' + clean_phone
        elif clean_phone.startswith('05'):
            clean_phone = '966' + clean_phone[1:]
        elif not clean_phone.startswith('966'):
            clean_phone = '966' + clean_phone
        
        return clean_phone


# إنشاء instance عام
simple_whatsapp_service = SimpleWhatsAppService()


def create_simple_whatsapp_message(order_data: Dict) -> str:
    """دالة مساعدة لإنشاء رسالة واتساب"""
    return simple_whatsapp_service.create_whatsapp_message(order_data)


def create_simple_whatsapp_url(phone_number: str, message: str) -> str:
    """دالة مساعدة لإنشاء رابط واتساب"""
    return simple_whatsapp_service.create_whatsapp_url(phone_number, message)
