#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
تنفيذ إضافة الأعمدة الجديدة إلى BALANCE_TRANSACTIONS
Execute Adding New Columns to BALANCE_TRANSACTIONS
"""

import sys
import os
sys.path.insert(0, os.getcwd())

from oracle_manager import OracleManager

def check_current_structure():
    """فحص البنية الحالية للجدول"""
    
    oracle = OracleManager()
    
    print("🔍 فحص البنية الحالية لجدول BALANCE_TRANSACTIONS...")
    print("=" * 70)
    
    # فحص الأعمدة الحالية
    query = """
    SELECT column_name, data_type, nullable, data_default
    FROM user_tab_columns 
    WHERE table_name = 'BALANCE_TRANSACTIONS'
    ORDER BY column_id
    """
    
    result = oracle.execute_query(query)
    if result:
        print("الأعمدة الحالية:")
        for row in result:
            nullable = 'NULL' if row[2] == 'Y' else 'NOT NULL'
            default = f', افتراضي: {row[3]}' if row[3] else ''
            print(f"   {row[0]}: {row[1]} ({nullable}{default})")
    
    # فحص وجود الأعمدة الجديدة
    new_columns = ['BAL', 'BAL_F', 'MONTH_NO', 'YEAR_NO', 'BRANCH_ID']
    existing_new_columns = []
    
    for row in result:
        if row[0] in new_columns:
            existing_new_columns.append(row[0])
    
    print(f"\nالأعمدة الجديدة الموجودة: {existing_new_columns}")
    print(f"الأعمدة المطلوب إضافتها: {[col for col in new_columns if col not in existing_new_columns]}")
    
    return existing_new_columns

def add_new_columns():
    """إضافة الأعمدة الجديدة"""
    
    oracle = OracleManager()
    
    print("\n🔧 إضافة الأعمدة الجديدة...")
    print("=" * 70)
    
    try:
        # إضافة الأعمدة الجديدة
        alter_query = """
        ALTER TABLE BALANCE_TRANSACTIONS ADD (
            BAL NUMBER(15,2) DEFAULT 0,
            BAL_F NUMBER(15,2) DEFAULT 0,
            MONTH_NO NUMBER(2),
            YEAR_NO NUMBER(4),
            BRANCH_ID NUMBER DEFAULT 1
        )
        """
        
        oracle.execute_update(alter_query)
        print("✅ تم إضافة الأعمدة الجديدة بنجاح!")
        
        return True
        
    except Exception as e:
        if "already exists" in str(e).lower() or "name is already used" in str(e).lower():
            print("⚠️ الأعمدة موجودة مسبقاً")
            return True
        else:
            print(f"❌ خطأ في إضافة الأعمدة: {str(e)}")
            return False

def update_existing_data():
    """تحديث البيانات الموجودة"""
    
    oracle = OracleManager()
    
    print("\n📊 تحديث البيانات الموجودة...")
    print("=" * 70)
    
    try:
        # تحديث الأعمدة الجديدة للبيانات الموجودة
        update_query = """
        UPDATE BALANCE_TRANSACTIONS SET
            BAL = COALESCE(debit_amount, 0) - COALESCE(credit_amount, 0),
            BAL_F = (COALESCE(debit_amount, 0) - COALESCE(credit_amount, 0)) * COALESCE(exchange_rate, 1),
            MONTH_NO = EXTRACT(MONTH FROM document_date),
            YEAR_NO = EXTRACT(YEAR FROM document_date),
            BRANCH_ID = COALESCE(BRANCH_ID, 1)
        WHERE BAL IS NULL OR MONTH_NO IS NULL OR YEAR_NO IS NULL
        """
        
        oracle.execute_update(update_query)
        print("✅ تم تحديث البيانات الموجودة بنجاح!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث البيانات: {str(e)}")
        return False

def add_column_comments():
    """إضافة تعليقات على الأعمدة"""
    
    oracle = OracleManager()
    
    print("\n📝 إضافة تعليقات على الأعمدة...")
    print("=" * 70)
    
    comments = [
        ("BAL", "Unified balance: positive for debit, negative for credit"),
        ("BAL_F", "Balance in base currency with exchange rate"),
        ("MONTH_NO", "Month number (1-12) extracted from document_date"),
        ("YEAR_NO", "Year number extracted from document_date"),
        ("BRANCH_ID", "Branch identifier for multi-branch support")
    ]
    
    try:
        for column, comment in comments:
            comment_query = f"""
            COMMENT ON COLUMN BALANCE_TRANSACTIONS.{column} IS '{comment}'
            """
            oracle.execute_update(comment_query)
        
        print("✅ تم إضافة التعليقات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة التعليقات: {str(e)}")
        return False

def verify_results():
    """التحقق من النتائج"""
    
    oracle = OracleManager()
    
    print("\n✅ التحقق من النتائج...")
    print("=" * 70)
    
    # فحص البنية الجديدة
    print("1️⃣ الأعمدة الجديدة:")
    structure_query = """
    SELECT column_name, data_type, nullable, data_default
    FROM user_tab_columns 
    WHERE table_name = 'BALANCE_TRANSACTIONS'
    AND column_name IN ('BAL', 'BAL_F', 'MONTH_NO', 'YEAR_NO', 'BRANCH_ID')
    ORDER BY column_name
    """
    
    structure = oracle.execute_query(structure_query)
    if structure:
        for row in structure:
            nullable = 'NULL' if row[2] == 'Y' else 'NOT NULL'
            default = f', افتراضي: {row[3]}' if row[3] else ''
            print(f"   ✅ {row[0]}: {row[1]} ({nullable}{default})")
    
    # عرض عينة من البيانات
    print("\n2️⃣ عينة من البيانات المحدثة:")
    sample_query = """
    SELECT 
        id,
        document_number,
        TO_CHAR(document_date, 'YYYY-MM-DD') as doc_date,
        debit_amount,
        credit_amount,
        BAL,
        BAL_F,
        MONTH_NO,
        YEAR_NO,
        BRANCH_ID
    FROM BALANCE_TRANSACTIONS
    WHERE ROWNUM <= 5
    ORDER BY created_date DESC
    """
    
    sample = oracle.execute_query(sample_query)
    if sample:
        print("   ID | مستند | تاريخ | مدين | دائن | رصيد | رصيد_أساسي | شهر | سنة | فرع")
        print("   " + "-" * 80)
        for row in sample:
            print(f"   {row[0]} | {row[1]} | {row[2]} | {row[3]} | {row[4]} | {row[5]} | {row[6]} | {row[7]} | {row[8]} | {row[9]}")
    
    # إحصائيات
    print("\n3️⃣ إحصائيات:")
    stats_queries = [
        ("إجمالي السجلات", "SELECT COUNT(*) FROM BALANCE_TRANSACTIONS"),
        ("سجلات بـ BAL محسوب", "SELECT COUNT(*) FROM BALANCE_TRANSACTIONS WHERE BAL IS NOT NULL"),
        ("سجلات بـ MONTH_NO/YEAR_NO", "SELECT COUNT(*) FROM BALANCE_TRANSACTIONS WHERE MONTH_NO IS NOT NULL AND YEAR_NO IS NOT NULL"),
        ("سجلات بـ BRANCH_ID", "SELECT COUNT(*) FROM BALANCE_TRANSACTIONS WHERE BRANCH_ID IS NOT NULL")
    ]
    
    for desc, query in stats_queries:
        result = oracle.execute_query(query)
        if result:
            print(f"   {desc}: {result[0][0]}")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء تنفيذ إضافة الأعمدة الجديدة إلى BALANCE_TRANSACTIONS")
    print("=" * 80)
    
    try:
        # 1. فحص البنية الحالية
        existing_columns = check_current_structure()
        
        # 2. إضافة الأعمدة الجديدة
        if add_new_columns():
            
            # 3. تحديث البيانات الموجودة
            if update_existing_data():
                
                # 4. إضافة التعليقات
                add_column_comments()
                
                # 5. التحقق من النتائج
                verify_results()
                
                print("\n🎉 تم إكمال إضافة الأعمدة الجديدة بنجاح!")
                print("✅ المهمة 8Ve3nVKmBdnkNvD9WWDbxj مكتملة!")
                
                return True
            else:
                print("\n❌ فشل في تحديث البيانات")
                return False
        else:
            print("\n❌ فشل في إضافة الأعمدة")
            return False
            
    except Exception as e:
        print(f"\n❌ خطأ عام: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n" + "=" * 80)
        print("🏁 انتهى التنفيذ بنجاح - جاهز للمهمة التالية!")
    else:
        print("\n" + "=" * 80)
        print("🚫 فشل التنفيذ - يرجى مراجعة الأخطاء")
