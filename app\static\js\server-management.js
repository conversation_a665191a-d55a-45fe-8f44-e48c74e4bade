// ===== دوال إدارة الخادم =====

// إعدادات toastr
if (typeof toastr !== 'undefined') {
    toastr.options = {
        "closeButton": true,
        "debug": false,
        "newestOnTop": true,
        "progressBar": true,
        "positionClass": "toast-top-right",
        "preventDuplicates": false,
        "onclick": null,
        "showDuration": "300",
        "hideDuration": "1000",
        "timeOut": "5000",
        "extendedTimeOut": "1000",
        "showEasing": "swing",
        "hideEasing": "linear",
        "showMethod": "fadeIn",
        "hideMethod": "fadeOut"
    };
}

// تحديث حالة الخادم
function checkStatus() {
    if (typeof toastr !== 'undefined') {
        toastr.info('جاري فحص حالة الخادم...');
    }
    
    fetch('/admin/api/server/status')
        .then(response => response.json())
        .then(data => {
            updateStatusDisplay(data);
            if (typeof toastr !== 'undefined') {
                toastr.success('تم تحديث حالة الخادم');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            if (typeof toastr !== 'undefined') {
                toastr.error('خطأ في فحص حالة الخادم');
            }
        });
}

// تحديث عرض الحالة
function updateStatusDisplay(status) {
    // تحديث حالة الخادم
    const serverElement = document.getElementById('server-running');
    if (serverElement) {
        serverElement.textContent = status.server_running ? 'يعمل' : 'متوقف';
        serverElement.className = status.server_running ? 'text-success' : 'text-danger';
    }
    
    // تحديث حالة النطاق
    const domainElement = document.getElementById('domain-status');
    if (domainElement) {
        domainElement.textContent = status.domain_configured ? 'مُكوّن' : 'غير مُكوّن';
        domainElement.className = status.domain_configured ? 'text-success' : 'text-warning';
    }
    
    // تحديث حالة SSL
    const sslElement = document.getElementById('ssl-status');
    if (sslElement) {
        sslElement.textContent = status.ssl_enabled ? 'مُفعّل' : 'معطل';
        sslElement.className = status.ssl_enabled ? 'text-success' : 'text-danger';
    }
    
    // تحديث حالة Nginx
    const nginxElement = document.getElementById('nginx-status');
    if (nginxElement) {
        nginxElement.textContent = status.nginx_running ? 'يعمل' : 'متوقف';
        nginxElement.className = status.nginx_running ? 'text-success' : 'text-danger';
    }
    
    // تحديث حالة PM2
    const pm2Element = document.getElementById('pm2-status');
    if (pm2Element) {
        pm2Element.textContent = status.pm2_running ? 'يعمل' : 'متوقف';
        pm2Element.className = status.pm2_running ? 'text-success' : 'text-danger';
    }
    
    // عرض معلومات إضافية إذا كانت متوفرة
    if (status.domain_status && status.domain_status.ip && typeof toastr !== 'undefined') {
        toastr.info(`عنوان IP للنطاق: ${status.domain_status.ip}`);
    }
    
    if (status.ssl_status && status.ssl_status.valid && status.ssl_status.days_until_expiry && typeof toastr !== 'undefined') {
        const days = status.ssl_status.days_until_expiry;
        if (days < 30) {
            toastr.warning(`شهادة SSL ستنتهي خلال ${days} يوم`);
        }
    }
}

// حفظ الإعدادات
function saveConfig() {
    const config = {
        domain: document.getElementById('domain').value,
        port: parseInt(document.getElementById('port').value),
        host: document.getElementById('host').value,
        environment: document.getElementById('environment').value,
        ssl_enabled: document.getElementById('ssl-enabled').checked,
        auto_ssl: document.getElementById('auto-ssl').checked,
        ssl_cert_path: document.getElementById('ssl-cert-path').value,
        ssl_key_path: document.getElementById('ssl-key-path').value,
        nginx_enabled: document.getElementById('nginx-enabled').checked,
        pm2_enabled: document.getElementById('pm2-enabled').checked,
        backup_enabled: document.getElementById('backup-enabled').checked,
        monitoring_enabled: document.getElementById('monitoring-enabled').checked
    };

    if (typeof toastr !== 'undefined') {
        toastr.info('جاري حفظ الإعدادات...');
    }

    fetch('/admin/api/server/config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (typeof toastr !== 'undefined') {
                toastr.success(data.message);
            }
        } else {
            if (typeof toastr !== 'undefined') {
                toastr.error(data.message);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (typeof toastr !== 'undefined') {
            toastr.error('خطأ في حفظ الإعدادات');
        }
    });
}

// إنشاء ملفات الإعدادات
function generateConfigs() {
    if (typeof toastr !== 'undefined') {
        toastr.info('جاري إنشاء ملفات الإعدادات...');
    }
    
    fetch('/admin/api/server/generate-configs')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showConfigModal(data);
                if (typeof toastr !== 'undefined') {
                    toastr.success('تم إنشاء ملفات الإعدادات بنجاح');
                }
            } else {
                if (typeof toastr !== 'undefined') {
                    toastr.error(data.message);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            if (typeof toastr !== 'undefined') {
                toastr.error('خطأ في إنشاء ملفات الإعدادات');
            }
        });
}

// عرض modal الإعدادات
function showConfigModal(data) {
    const content = `
        <div class="row">
            <div class="col-12">
                <h6>إعدادات Nginx:</h6>
                <div class="card">
                    <div class="card-body">
                        <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 300px; overflow-y: auto;"><code>${data.nginx_config || 'لا توجد إعدادات Nginx'}</code></pre>
                        <button class="btn btn-sm btn-outline-primary mt-2" onclick="copyToClipboard('${btoa(data.nginx_config || '')}', 'nginx')">
                            <i class="fas fa-copy"></i> نسخ إعدادات Nginx
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-12 mt-3">
                <h6>إعدادات PM2:</h6>
                <div class="card">
                    <div class="card-body">
                        <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 300px; overflow-y: auto;"><code>${JSON.stringify(data.pm2_config, null, 2)}</code></pre>
                        <button class="btn btn-sm btn-outline-primary mt-2" onclick="copyToClipboard('${btoa(JSON.stringify(data.pm2_config, null, 2))}', 'pm2')">
                            <i class="fas fa-copy"></i> نسخ إعدادات PM2
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('config-content').innerHTML = content;
    
    // استخدام Bootstrap 5 API
    const modal = new bootstrap.Modal(document.getElementById('configModal'));
    modal.show();
}

// عرض modal التعليمات
function showInstructionsModal(instructions) {
    const content = `
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto; white-space: pre-wrap;">${instructions}</pre>
                        <button class="btn btn-sm btn-outline-primary mt-2" onclick="copyToClipboard('${btoa(instructions)}', 'instructions')">
                            <i class="fas fa-copy"></i> نسخ التعليمات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('config-content').innerHTML = content;
    
    // استخدام Bootstrap 5 API
    const modal = new bootstrap.Modal(document.getElementById('configModal'));
    modal.show();
}

// نسخ النص إلى الحافظة
function copyToClipboard(encodedText, type) {
    try {
        const text = atob(encodedText);
        navigator.clipboard.writeText(text).then(function() {
            if (typeof toastr !== 'undefined') {
                toastr.success(`تم نسخ ${type === 'nginx' ? 'إعدادات Nginx' : type === 'pm2' ? 'إعدادات PM2' : 'التعليمات'} إلى الحافظة`);
            }
        }, function(err) {
            console.error('خطأ في النسخ: ', err);
            if (typeof toastr !== 'undefined') {
                toastr.error('فشل في نسخ النص');
            }
        });
    } catch (error) {
        console.error('خطأ في فك التشفير: ', error);
        if (typeof toastr !== 'undefined') {
            toastr.error('خطأ في معالجة النص');
        }
    }
}

// تحميل الإعدادات
function downloadConfigs() {
    if (typeof toastr !== 'undefined') {
        toastr.info('جاري تحضير ملفات الإعدادات للتحميل...');
    }
    
    fetch('/admin/api/server/download-configs')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (typeof toastr !== 'undefined') {
                    toastr.success(data.message);
                }
            } else {
                if (typeof toastr !== 'undefined') {
                    toastr.error(data.message);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            if (typeof toastr !== 'undefined') {
                toastr.error('خطأ في تحميل الإعدادات');
            }
        });
}

// عرض تعليمات النشر
function showInstructions() {
    if (typeof toastr !== 'undefined') {
        toastr.info('جاري تحميل تعليمات النشر...');
    }
    
    fetch('/admin/api/server/instructions')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // عرض التعليمات في modal
                showInstructionsModal(data.instructions);
                if (typeof toastr !== 'undefined') {
                    toastr.success('تم تحميل التعليمات');
                }
            } else {
                if (typeof toastr !== 'undefined') {
                    toastr.error(data.message);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            if (typeof toastr !== 'undefined') {
                toastr.error('خطأ في تحميل التعليمات');
            }
        });
}

// نشر التطبيق
function deployApp() {
    if (typeof toastr !== 'undefined') {
        toastr.info('جاري بدء عملية النشر...');
    }
    
    fetch('/admin/api/server/deploy')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (typeof toastr !== 'undefined') {
                    toastr.success(data.message);
                }
            } else {
                if (typeof toastr !== 'undefined') {
                    toastr.error(data.message);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            if (typeof toastr !== 'undefined') {
                toastr.error('خطأ في عملية النشر');
            }
        });
}

// إعداد معالجات الأحداث
function setupEventHandlers() {
    // معالج تغيير SSL التلقائي
    const autoSslCheckbox = document.getElementById('auto-ssl');
    if (autoSslCheckbox) {
        autoSslCheckbox.addEventListener('change', toggleManualSSL);
    }

    // معالج تغيير تفعيل SSL
    const sslEnabledCheckbox = document.getElementById('ssl-enabled');
    if (sslEnabledCheckbox) {
        sslEnabledCheckbox.addEventListener('change', function() {
            const sslSection = document.querySelector('#auto-ssl').closest('.form-group').parentElement;
            if (sslSection) {
                sslSection.style.opacity = this.checked ? '1' : '0.5';
            }
        });
    }

    // معالجات ملفات SSL
    const certFileInput = document.getElementById('cert-file-input');
    if (certFileInput) {
        certFileInput.addEventListener('change', handleCertFileSelect);
    }

    const keyFileInput = document.getElementById('key-file-input');
    if (keyFileInput) {
        keyFileInput.addEventListener('change', handleKeyFileSelect);
    }
}

// إظهار/إخفاء إعدادات SSL اليدوية
function toggleManualSSL() {
    const autoSslCheckbox = document.getElementById('auto-ssl');
    const manualSsl = document.getElementById('manual-ssl');

    if (autoSslCheckbox && manualSsl) {
        manualSsl.style.display = autoSslCheckbox.checked ? 'none' : 'block';
    }
}

// استعراض ملف شهادة SSL
function browseCertFile() {
    const fileInput = document.getElementById('cert-file-input');
    if (fileInput) {
        fileInput.click();
    }
}

// استعراض ملف مفتاح SSL
function browseKeyFile() {
    const fileInput = document.getElementById('key-file-input');
    if (fileInput) {
        fileInput.click();
    }
}

// معالجة اختيار ملف الشهادة
function handleCertFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        const certPathInput = document.getElementById('ssl-cert-path');
        if (certPathInput) {
            // في بيئة الويب، نعرض اسم الملف فقط
            // في التطبيق الحقيقي، يجب رفع الملف إلى الخادم
            certPathInput.value = `/etc/ssl/certs/${file.name}`;

            if (typeof toastr !== 'undefined') {
                toastr.info(`تم اختيار ملف الشهادة: ${file.name}`);
                toastr.warning('تذكر: يجب رفع الملف إلى الخادم في المسار المحدد');
            }
        }
    }
}

// معالجة اختيار ملف المفتاح
function handleKeyFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        const keyPathInput = document.getElementById('ssl-key-path');
        if (keyPathInput) {
            // في بيئة الويب، نعرض اسم الملف فقط
            // في التطبيق الحقيقي، يجب رفع الملف إلى الخادم
            keyPathInput.value = `/etc/ssl/private/${file.name}`;

            if (typeof toastr !== 'undefined') {
                toastr.info(`تم اختيار ملف المفتاح: ${file.name}`);
                toastr.warning('تذكر: يجب رفع الملف إلى الخادم في المسار المحدد');
            }
        }
    }
}

// رفع ملفات SSL إلى الخادم
function uploadSSLFiles() {
    const certFile = document.getElementById('cert-file-input').files[0];
    const keyFile = document.getElementById('key-file-input').files[0];

    if (!certFile || !keyFile) {
        if (typeof toastr !== 'undefined') {
            toastr.error('يرجى اختيار ملفي الشهادة والمفتاح');
        }
        return;
    }

    const formData = new FormData();
    formData.append('cert_file', certFile);
    formData.append('key_file', keyFile);

    if (typeof toastr !== 'undefined') {
        toastr.info('جاري رفع ملفات SSL...');
    }

    fetch('/admin/api/server/upload-ssl', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (typeof toastr !== 'undefined') {
                toastr.success('تم رفع ملفات SSL بنجاح');
            }

            // تحديث مسارات الملفات
            document.getElementById('ssl-cert-path').value = data.cert_path;
            document.getElementById('ssl-key-path').value = data.key_path;
        } else {
            if (typeof toastr !== 'undefined') {
                toastr.error(data.message || 'فشل في رفع ملفات SSL');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (typeof toastr !== 'undefined') {
            toastr.error('خطأ في رفع ملفات SSL');
        }
    });
}

// التحقق من صحة ملفات SSL
function validateSSLFiles() {
    const certFile = document.getElementById('cert-file-input').files[0];
    const keyFile = document.getElementById('key-file-input').files[0];

    if (!certFile || !keyFile) {
        if (typeof toastr !== 'undefined') {
            toastr.error('يرجى اختيار ملفي الشهادة والمفتاح أولاً');
        }
        return;
    }

    // فحص امتدادات الملفات
    const certExt = certFile.name.split('.').pop().toLowerCase();
    const keyExt = keyFile.name.split('.').pop().toLowerCase();

    const validCertExts = ['crt', 'pem', 'cer'];
    const validKeyExts = ['key', 'pem'];

    let isValid = true;
    let messages = [];

    if (!validCertExts.includes(certExt)) {
        isValid = false;
        messages.push(`امتداد ملف الشهادة غير صالح: .${certExt}`);
    }

    if (!validKeyExts.includes(keyExt)) {
        isValid = false;
        messages.push(`امتداد ملف المفتاح غير صالح: .${keyExt}`);
    }

    // فحص حجم الملفات
    if (certFile.size > 10 * 1024 * 1024) { // 10MB
        isValid = false;
        messages.push('حجم ملف الشهادة كبير جداً (أكثر من 10MB)');
    }

    if (keyFile.size > 10 * 1024 * 1024) { // 10MB
        isValid = false;
        messages.push('حجم ملف المفتاح كبير جداً (أكثر من 10MB)');
    }

    if (typeof toastr !== 'undefined') {
        if (isValid) {
            toastr.success(`✅ الملفات صالحة:
            📄 الشهادة: ${certFile.name} (${(certFile.size / 1024).toFixed(1)} KB)
            🔑 المفتاح: ${keyFile.name} (${(keyFile.size / 1024).toFixed(1)} KB)`);
        } else {
            toastr.error('❌ مشاكل في الملفات:\n' + messages.join('\n'));
        }
    }

    return isValid;
}

// مسح اختيار ملفات SSL
function clearSSLFiles() {
    const certInput = document.getElementById('cert-file-input');
    const keyInput = document.getElementById('key-file-input');
    const certPath = document.getElementById('ssl-cert-path');
    const keyPath = document.getElementById('ssl-key-path');

    if (certInput) certInput.value = '';
    if (keyInput) keyInput.value = '';
    if (certPath) certPath.value = '';
    if (keyPath) keyPath.value = '';

    if (typeof toastr !== 'undefined') {
        toastr.info('تم مسح اختيار ملفات SSL');
    }
}

// إنشاء شهادة SSL للنطاق المحدد
function generateDomainSSL() {
    const domain = document.getElementById('domain').value;

    if (!domain) {
        if (typeof toastr !== 'undefined') {
            toastr.error('يرجى إدخال اسم النطاق أولاً');
        }
        return;
    }

    // إزالة البروتوكول من النطاق
    const cleanDomain = domain.replace('https://', '').replace('http://', '');

    if (typeof toastr !== 'undefined') {
        toastr.info(`جاري إنشاء شهادة SSL للنطاق ${cleanDomain}...`);
    }

    fetch('/admin/api/server/generate-domain-ssl', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ domain: cleanDomain })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (typeof toastr !== 'undefined') {
                toastr.success(`تم إنشاء شهادة SSL للنطاق ${data.domain} بنجاح`);
                toastr.info('تذكر: هذه شهادة موقعة ذاتياً. للإنتاج استخدم Let\'s Encrypt أو شهادة من CA معتمد');
            }

            // تحديث مسارات الملفات
            document.getElementById('ssl-cert-path').value = data.cert_path;
            document.getElementById('ssl-key-path').value = data.key_path;

            // تفعيل SSL
            document.getElementById('ssl-enabled').checked = true;

            // إظهار رسالة إرشادية
            if (typeof toastr !== 'undefined') {
                setTimeout(() => {
                    toastr.warning('لحل مشكلة "غير آمن" في المتصفح، احفظ الإعدادات وأعد تشغيل الخادم');
                }, 2000);
            }
        } else {
            if (typeof toastr !== 'undefined') {
                toastr.error(data.message || 'فشل في إنشاء شهادة SSL للنطاق');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (typeof toastr !== 'undefined') {
            toastr.error('خطأ في إنشاء شهادة SSL للنطاق');
        }
    });
}

// إنشاء شهادة SSL تجريبية (للتطوير)
function generateTestSSL() {
    const domain = document.getElementById('domain').value;

    if (!domain) {
        if (typeof toastr !== 'undefined') {
            toastr.error('يرجى إدخال اسم النطاق أولاً');
        }
        return;
    }

    if (typeof toastr !== 'undefined') {
        toastr.info('جاري إنشاء شهادة SSL تجريبية...');
    }

    fetch('/admin/api/server/generate-test-ssl', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ domain: domain })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (typeof toastr !== 'undefined') {
                toastr.success('تم إنشاء شهادة SSL تجريبية بنجاح');
            }

            // تحديث مسارات الملفات
            document.getElementById('ssl-cert-path').value = data.cert_path;
            document.getElementById('ssl-key-path').value = data.key_path;
        } else {
            if (typeof toastr !== 'undefined') {
                toastr.error(data.message || 'فشل في إنشاء شهادة SSL تجريبية');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (typeof toastr !== 'undefined') {
            toastr.error('خطأ في إنشاء شهادة SSL تجريبية');
        }
    });
}

// تحديث الحالة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل قائمة الخوادم
    loadServersList();

    // فحص الحالة الأولي
    checkStatus();

    // تحديث دوري للحالة
    setInterval(checkStatus, 30000); // كل 30 ثانية

    // إعداد معالجات الأحداث
    setupEventHandlers();

    // إعداد إظهار/إخفاء SSL اليدوي
    toggleManualSSL();
});

// ===== إدارة الخوادم المتعددة =====

// تحميل قائمة الخوادم
function loadServersList() {
    console.log('🔄 بدء تحميل قائمة الخوادم...');

    fetch('/admin/api/servers')
        .then(response => {
            console.log('📡 استجابة الخادم:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📋 بيانات الخوادم:', data);
            if (data.success) {
                displayServersList(data.servers);
                if (typeof toastr !== 'undefined') {
                    toastr.success(`تم تحميل ${data.servers.length} خادم`);
                }
            } else {
                console.error('❌ فشل في تحميل الخوادم:', data.message);
                if (typeof toastr !== 'undefined') {
                    toastr.error('فشل في تحميل قائمة الخوادم');
                }
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل الخوادم:', error);
            if (typeof toastr !== 'undefined') {
                toastr.error('خطأ في تحميل قائمة الخوادم');
            }
        });
}

// عرض قائمة الخوادم
function displayServersList(servers) {
    console.log('🖥️ عرض قائمة الخوادم:', servers);
    const serversList = document.getElementById('servers-list');

    if (!serversList) {
        console.error('❌ لم يتم العثور على عنصر servers-list');
        return;
    }

    if (servers.length === 0) {
        console.log('📭 لا توجد خوادم محفوظة');
        serversList.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> لا توجد خوادم محفوظة
            </div>
        `;
        return;
    }

    let html = '<div class="row">';
    servers.forEach(server => {
        const createdDate = new Date(server.created_at).toLocaleDateString('ar-SA');
        const statusBadge = server.ssl_enabled ?
            '<span class="badge badge-success">SSL مُفعّل</span>' :
            '<span class="badge badge-secondary">SSL معطل</span>';

        html += `
            <div class="col-md-6 mb-3">
                <div class="card border-left-primary">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    ${server.name || 'خادم بدون اسم'}
                                </div>
                                <div class="h6 mb-0 font-weight-bold text-gray-800">
                                    ${server.domain || 'لا يوجد نطاق'}
                                </div>
                                <div class="text-xs text-gray-600">
                                    المنفذ: ${server.port || 5000} | البيئة: ${server.environment || 'development'}
                                </div>
                                <div class="text-xs text-gray-500">
                                    تم الإنشاء: ${createdDate}
                                </div>
                                <div class="mt-2">
                                    ${statusBadge}
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="btn-group-vertical">
                                    <button class="btn btn-sm btn-primary mb-1" onclick="loadServerConfig('${server.id}')" title="تحميل الإعدادات">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-sm btn-warning mb-1" onclick="editServer('${server.id}')" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteServer('${server.id}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';

    serversList.innerHTML = html;
}

// عرض modal إضافة خادم جديد
function showAddServerModal() {
    // مسح النموذج
    document.getElementById('addServerForm').reset();

    // عرض modal
    const modal = new bootstrap.Modal(document.getElementById('addServerModal'));
    modal.show();
}

// إضافة خادم جديد
function addNewServer() {
    const serverData = {
        name: document.getElementById('new-server-name').value,
        domain: document.getElementById('new-server-domain').value,
        port: parseInt(document.getElementById('new-server-port').value) || 5000,
        environment: document.getElementById('new-server-environment').value,
        description: document.getElementById('new-server-description').value,
        host: '0.0.0.0',
        ssl_enabled: false,
        auto_ssl: false,
        nginx_enabled: false,
        pm2_enabled: false,
        backup_enabled: false,
        monitoring_enabled: false
    };

    // التحقق من البيانات المطلوبة
    if (!serverData.name || !serverData.domain) {
        if (typeof toastr !== 'undefined') {
            toastr.error('اسم الخادم والنطاق مطلوبان');
        }
        return;
    }

    if (typeof toastr !== 'undefined') {
        toastr.info('جاري إضافة الخادم...');
    }

    fetch('/admin/api/servers', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(serverData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (typeof toastr !== 'undefined') {
                toastr.success(data.message);
            }

            // إخفاء modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addServerModal'));
            modal.hide();

            // تحديث قائمة الخوادم
            loadServersList();
        } else {
            if (typeof toastr !== 'undefined') {
                toastr.error(data.message);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (typeof toastr !== 'undefined') {
            toastr.error('خطأ في إضافة الخادم');
        }
    });
}

// تحميل إعدادات خادم محدد
function loadServerConfig(serverId) {
    if (typeof toastr !== 'undefined') {
        toastr.info('جاري تحميل إعدادات الخادم...');
    }

    fetch(`/admin/api/servers/${serverId}/load`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (typeof toastr !== 'undefined') {
                toastr.success(data.message);
            }

            // إعادة تحميل الصفحة لعرض الإعدادات الجديدة
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            if (typeof toastr !== 'undefined') {
                toastr.error(data.message);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (typeof toastr !== 'undefined') {
            toastr.error('خطأ في تحميل إعدادات الخادم');
        }
    });
}

// حذف خادم
function deleteServer(serverId) {
    if (!confirm('هل أنت متأكد من حذف هذا الخادم؟')) {
        return;
    }

    if (typeof toastr !== 'undefined') {
        toastr.info('جاري حذف الخادم...');
    }

    fetch(`/admin/api/servers/${serverId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (typeof toastr !== 'undefined') {
                toastr.success(data.message);
            }

            // تحديث قائمة الخوادم
            loadServersList();
        } else {
            if (typeof toastr !== 'undefined') {
                toastr.error(data.message);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (typeof toastr !== 'undefined') {
            toastr.error('خطأ في حذف الخادم');
        }
    });
}

// تعديل خادم (مؤقتاً - يمكن تطويره لاحقاً)
function editServer(serverId) {
    if (typeof toastr !== 'undefined') {
        toastr.info('ميزة التعديل قيد التطوير. يمكنك تحميل الإعدادات وتعديلها ثم حفظها كخادم جديد.');
    }
}

// إعادة تشغيل الخادم
function restartServer() {
    if (!confirm('هل أنت متأكد من إعادة تشغيل الخادم؟ سيتم قطع الاتصال مؤقتاً.')) {
        return;
    }

    if (typeof toastr !== 'undefined') {
        toastr.warning('سيتم إعادة تشغيل الخادم خلال 3 ثوانٍ...');
        toastr.info('سيتم إعادة تحميل الصفحة تلقائياً بعد إعادة التشغيل');
    }

    fetch('/admin/api/server/restart', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (typeof toastr !== 'undefined') {
                toastr.success(data.message);
            }

            // إعادة تحميل الصفحة بعد 5 ثوانٍ
            setTimeout(() => {
                if (typeof toastr !== 'undefined') {
                    toastr.info('جاري إعادة تحميل الصفحة...');
                }
                location.reload();
            }, 5000);
        } else {
            if (typeof toastr !== 'undefined') {
                toastr.error(data.message);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (typeof toastr !== 'undefined') {
            toastr.error('خطأ في إعادة تشغيل الخادم');
        }
    });
}
