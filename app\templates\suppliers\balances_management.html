<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة أرصدة الموردين - لوحة المعلومات المتقدمة</title>

    <!-- Modern CSS Framework -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css" rel="stylesheet">

    <style>
        /* Enterprise Design System */
        :root {
            --primary: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary: #64748b;
            --success: #059669;
            --warning: #d97706;
            --danger: #dc2626;
            --info: #0891b2;
            --light: #f8fafc;
            --dark: #0f172a;
            --surface: #ffffff;
            --border: #e2e8f0;
            --text-primary: #0f172a;
            --text-secondary: #475569;
            --text-muted: #94a3b8;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -1px rgb(0 0 0 / 0.06);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
            --radius: 0.75rem;
        }

        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        body {
            background: var(--light);
            color: var(--text-primary);
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }

        /* Header */
        .dashboard-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 2.5rem 0;
            position: relative;
            overflow: hidden;
        }

        .dashboard-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="8" height="8" patternUnits="userSpaceOnUse"><path d="M 8 0 L 0 0 0 8" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.4;
        }

        .page-title {
            font-size: 2.75rem;
            font-weight: 800;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .page-subtitle {
            font-size: 1.125rem;
            opacity: 0.9;
            margin: 0.75rem 0 0 0;
            position: relative;
            z-index: 1;
        }

        .header-actions {
            position: relative;
            z-index: 1;
        }

        /* Cards */
        .card-enterprise {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            box-shadow: var(--shadow-sm);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .card-enterprise:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
            border-color: var(--primary);
        }

        /* Metrics */
        .metrics-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .metric-card {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 1.75rem;
            position: relative;
            transition: all 0.3s ease;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-4px);
        }

        .metric-card:hover::before {
            transform: scaleX(1);
        }

        .metric-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.25rem;
        }

        .metric-icon {
            width: 52px;
            height: 52px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.375rem;
            color: white;
            background: var(--primary);
        }

        .metric-icon.success { background: var(--success); }
        .metric-icon.warning { background: var(--warning); }
        .metric-icon.danger { background: var(--danger); }
        .metric-icon.info { background: var(--info); }

        .metric-trend {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            font-size: 0.8rem;
            font-weight: 600;
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
        }

        .trend-positive {
            background: rgba(5, 150, 105, 0.1);
            color: var(--success);
        }

        .trend-negative {
            background: rgba(220, 38, 38, 0.1);
            color: var(--danger);
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--text-primary);
            margin: 0.75rem 0 0.5rem 0;
            line-height: 1;
        }

        .metric-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .metric-description {
            font-size: 0.8rem;
            color: var(--text-muted);
        }

        /* Charts */
        .charts-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin: 2rem 0;
        }

        @media (max-width: 1024px) {
            .charts-section {
                grid-template-columns: 1fr;
            }
        }

        .chart-card {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .chart-card:hover {
            box-shadow: var(--shadow-md);
            border-color: var(--primary);
        }

        .chart-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
        }

        .chart-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .chart-container {
            position: relative;
            height: 320px;
            width: 100%;
        }

        /* Buttons */
        .btn-enterprise {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary-enterprise {
            background: var(--primary);
            color: white;
        }

        .btn-primary-enterprise:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
            color: white;
        }

        .btn-secondary-enterprise {
            background: var(--surface);
            color: var(--text-secondary);
            border: 1px solid var(--border);
        }

        .btn-secondary-enterprise:hover {
            background: var(--light);
            border-color: var(--secondary);
            color: var(--text-primary);
        }

        /* Table */
        .table-enterprise {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
            background: var(--surface);
            table-layout: fixed; /* إصلاح عرض الأعمدة */
        }

        .table-enterprise th {
            background: var(--light);
            padding: 1rem;
            text-align: center; /* توسيط العناوين */
            font-weight: 600;
            color: var(--text-secondary);
            border-bottom: 1px solid var(--border);
            vertical-align: middle; /* محاذاة عمودية */
            white-space: nowrap; /* منع كسر النص */
        }

        .table-enterprise td {
            padding: 1rem;
            border-bottom: 1px solid var(--border);
            color: var(--text-primary);
            text-align: center; /* توسيط البيانات */
            vertical-align: middle; /* محاذاة عمودية */
        }

        .table-enterprise tbody tr:hover {
            background: var(--light);
        }

        /* تحديد عرض الأعمدة لضمان الاصطفاف */
        .table-enterprise th:nth-child(1),
        .table-enterprise td:nth-child(1) {
            width: 20%; /* عمود المورد */
            text-align: right; /* محاذاة يمين للنص العربي */
        }

        .table-enterprise th:nth-child(2),
        .table-enterprise td:nth-child(2) {
            width: 12%; /* عمود نوع الحساب */
        }

        .table-enterprise th:nth-child(3),
        .table-enterprise td:nth-child(3) {
            width: 15%; /* عمود الرصيد الحالي */
        }

        .table-enterprise th:nth-child(4),
        .table-enterprise td:nth-child(4) {
            width: 15%; /* عمود المبلغ المتأخر */
        }

        .table-enterprise th:nth-child(5),
        .table-enterprise td:nth-child(5) {
            width: 12%; /* عمود تقييم المخاطر */
        }

        .table-enterprise th:nth-child(6),
        .table-enterprise td:nth-child(6) {
            width: 16%; /* عمود آخر نشاط */
        }

        .table-enterprise th:nth-child(7),
        .table-enterprise td:nth-child(7) {
            width: 10%; /* عمود الإجراءات */
        }

        .supplier-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
            text-align: right; /* محاذاة يمين للنص العربي */
            line-height: 1.2;
        }

        .supplier-code {
            font-size: 0.75rem;
            color: var(--text-muted);
            font-family: 'Monaco', monospace;
            text-align: right; /* محاذاة يمين للكود */
            direction: ltr; /* اتجاه من اليسار لليمين للأرقام */
        }

        .balance-positive {
            color: var(--success);
            font-weight: 600;
        }

        .balance-negative {
            color: var(--danger);
            font-weight: 600;
        }

        /* تحسين عرض شارات الحالة */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-align: center;
            display: inline-block;
            min-width: 60px;
        }

        .status-low {
            background-color: #d4edda;
            color: #155724;
        }

        .status-medium {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-high {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-low { background: rgba(5, 150, 105, 0.1); color: var(--success); }
        .status-medium { background: rgba(217, 119, 6, 0.1); color: var(--warning); }
        .status-high { background: rgba(220, 38, 38, 0.1); color: var(--danger); }

        /* Loading */
        .loading-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 3rem;
            color: var(--text-muted);
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--border);
            border-top: 3px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Breadcrumb Navigation */
        .breadcrumb-section {
            background: var(--surface);
            border-bottom: 1px solid var(--border);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }

        .breadcrumb-modern {
            background: none;
            padding: 0;
            margin: 0;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            list-style: none;
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
            font-size: 0.875rem;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: '/';
            color: var(--text-muted);
            margin: 0 0.75rem;
            font-weight: 400;
        }

        .breadcrumb-link {
            color: var(--text-secondary);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0.75rem;
            border-radius: var(--radius);
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .breadcrumb-link:hover {
            color: var(--primary);
            background: rgba(37, 99, 235, 0.1);
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: var(--text-primary);
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0.75rem;
            background: rgba(37, 99, 235, 0.1);
            border-radius: var(--radius);
        }

        .breadcrumb-item i {
            font-size: 0.75rem;
            opacity: 0.8;
        }

        .breadcrumb-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .breadcrumb-actions .btn {
            font-size: 0.8rem;
            padding: 0.375rem 0.75rem;
            border-radius: var(--radius);
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .breadcrumb-actions .btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }

        .breadcrumb-actions .dropdown-menu {
            border: 1px solid var(--border);
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
            padding: 0.5rem 0;
        }

        .breadcrumb-actions .dropdown-item {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            color: var(--text-secondary);
            transition: all 0.2s ease;
        }

        .breadcrumb-actions .dropdown-item:hover {
            background: var(--light);
            color: var(--primary);
        }

        .breadcrumb-actions .dropdown-item i {
            width: 16px;
            text-align: center;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .metrics-container {
                grid-template-columns: 1fr;
            }

            .page-title {
                font-size: 2rem;
            }

            .header-actions {
                flex-direction: column;
                gap: 0.5rem;
            }

            .breadcrumb-modern {
                font-size: 0.8rem;
            }

            .breadcrumb-item + .breadcrumb-item::before {
                margin: 0 0.5rem;
            }

            .breadcrumb-link,
            .breadcrumb-item.active {
                padding: 0.375rem 0.5rem;
            }

            .breadcrumb-actions {
                flex-direction: column;
                gap: 0.5rem;
                margin-top: 1rem;
            }

            .breadcrumb-actions .btn {
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Professional Header -->
    <div class="dashboard-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-chart-line ms-3"></i>
                        إدارة أرصدة الموردين
                    </h1>
                    <p class="page-subtitle">
                        تحليلات متقدمة ومراقبة فورية لأرصدة حسابات الموردين مع تحليل شامل للاستحقاقات
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions d-flex gap-2 justify-content-lg-end">
                        <button class="btn-enterprise btn-secondary-enterprise" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                        <div class="dropdown">
                            <button class="btn-enterprise btn-secondary-enterprise dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-download"></i>
                                تصدير
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel ms-2"></i>تقرير Excel
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf ms-2"></i>تقرير PDF
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportToCSV()">
                                    <i class="fas fa-file-csv ms-2"></i>بيانات CSV
                                </a></li>
                            </ul>
                        </div>
                        <button class="btn-enterprise btn-primary-enterprise" onclick="showReconciliationModal()">
                            <i class="fas fa-plus"></i>
                            مطابقة جديدة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb Navigation -->
    <div class="breadcrumb-section">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb-modern">
                            <li class="breadcrumb-item">
                                <a href="/" class="breadcrumb-link">
                                    <i class="fas fa-home"></i>
                                    الرئيسية
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="/suppliers" class="breadcrumb-link">
                                    <i class="fas fa-truck"></i>
                                    إدارة الموردين
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="/suppliers/accounts_management" class="breadcrumb-link">
                                    <i class="fas fa-users-cog"></i>
                                    إدارة حسابات الموردين
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">
                                <i class="fas fa-chart-line"></i>
                                إدارة أرصدة الموردين
                            </li>
                        </ol>
                    </nav>
                </div>
                <div class="col-lg-4">
                    <div class="breadcrumb-actions d-flex gap-2 justify-content-lg-end">
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-link me-1"></i>
                                صفحات ذات صلة
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="/suppliers/accounts_management">
                                        <i class="fas fa-users-cog me-2"></i>
                                        إدارة حسابات الموردين
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="/suppliers/transactions_management">
                                        <i class="fas fa-exchange-alt me-2"></i>
                                        إدارة معاملات الموردين
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="/suppliers/reconciliation_management">
                                        <i class="fas fa-balance-scale me-2"></i>
                                        مطابقة حسابات الموردين
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="/reports/suppliers">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        تقارير الموردين
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" onclick="window.print()">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Key Performance Metrics -->
        <div class="metrics-container">
            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="metric-trend trend-positive">
                        <i class="fas fa-arrow-up"></i>
                        +12.5%
                    </div>
                </div>
                <div class="metric-value" id="totalBalance">
                    {% if data and data.statistics %}
                        {{ "{:,.2f}".format(data.statistics.total_balance) }}
                    {% else %}
                        0.00
                    {% endif %}
                </div>
                <div class="metric-label" id="totalBalanceLabel">إجمالي الأرصدة</div>
                <div class="metric-description">مقارنة بالشهر الماضي</div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-icon danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="metric-trend trend-negative">
                        <i class="fas fa-arrow-down"></i>
                        -3.2%
                    </div>
                </div>
                <div class="metric-value" id="totalOutstanding">
                    {% if data and data.statistics %}
                        {{ "{:,.2f}".format(data.statistics.total_outstanding) }}
                    {% else %}
                        0.00
                    {% endif %}
                </div>
                <div class="metric-label" id="totalOutstandingLabel">إجمالي المستحقات</div>
                <div class="metric-description">مدفوعات معلقة</div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-icon success">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="metric-trend trend-positive">
                        <i class="fas fa-arrow-up"></i>
                        +2
                    </div>
                </div>
                <div class="metric-value" id="totalSuppliers">
                    {% if data and data.statistics %}
                        {{ data.statistics.total_suppliers }}
                    {% else %}
                        0
                    {% endif %}
                </div>
                <div class="metric-label">الموردين النشطين</div>
                <div class="metric-description">موردين جدد هذا الشهر</div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-icon warning">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="metric-trend trend-positive">
                        <i class="fas fa-arrow-up"></i>
                        +8.7%
                    </div>
                </div>
                <div class="metric-value" id="overdueAmount">
                    {% if data and data.statistics %}
                        {{ "{:,.2f}".format(data.statistics.overdue_amount) }}
                    {% else %}
                        0.00
                    {% endif %}
                </div>
                <div class="metric-label" id="overdueAmountLabel">المبالغ المتأخرة</div>
                <div class="metric-description">تحتاج متابعة فورية</div>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div class="card-enterprise mb-4">
            <div class="p-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3 class="h5 mb-0">
                        <i class="fas fa-filter ms-2"></i>
                        فلاتر متقدمة
                    </h3>
                    <button class="btn-enterprise btn-secondary-enterprise" onclick="clearAllFilters()">
                        <i class="fas fa-times"></i>
                        مسح الكل
                    </button>
                </div>
                <div class="row g-3">
                    <div class="col-md-2">
                        <label class="form-label">الفترة الزمنية</label>
                        <select class="form-select" id="periodFilter">
                            <option value="current_month">الشهر الحالي</option>
                            <option value="last_month">الشهر الماضي</option>
                            <option value="current_quarter">الربع الحالي</option>
                            <option value="current_year">السنة الحالية</option>
                            <option value="custom">فترة مخصصة</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">العملة</label>
                        <select class="form-select" id="currencyFilter">
                            <option value="all">جميع العملات</option>
                            <!-- سيتم تحميل العملات من قاعدة البيانات -->
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">حالة الرصيد</label>
                        <select class="form-select" id="balanceStatusFilter">
                            <option value="all">جميع الحالات</option>
                            <option value="creditor">دائن (نحن مدينون)</option>
                            <option value="debtor">مدين (هو مدين لنا)</option>
                            <option value="zero">رصيد صفر</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">نوع المورد</label>
                        <select class="form-select" id="supplierTypeFilter">
                            <option value="all">جميع الأنواع</option>
                            <!-- سيتم تحميل الأنواع من قاعدة البيانات -->
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">تقييم المخاطر</label>
                        <select class="form-select" id="riskFilter">
                            <option value="all">جميع التقييمات</option>
                            <option value="LOW">مخاطر منخفضة</option>
                            <option value="MEDIUM">مخاطر متوسطة</option>
                            <option value="HIGH">مخاطر عالية</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">البحث السريع</label>
                        <input type="text" class="form-control" id="supplierSearch"
                               placeholder="اسم المورد أو الكود..."
                               autocomplete="off">
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Analytics -->
        <div class="charts-section">
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-pie"></i>
                        توزيع الأرصدة حسب العملة
                    </h3>
                    <div class="d-flex gap-1">
                        <button class="btn btn-sm btn-outline-secondary" onclick="refreshChart('currency')">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="exportChart('currency')">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="balancesByCurrencyChart"></canvas>
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-clock"></i>
                        تحليل الاستحقاقات
                    </h3>
                    <div class="d-flex gap-1">
                        <button class="btn btn-sm btn-outline-secondary" onclick="refreshAgingAnalysis()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="exportAgingReport()">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                        <span class="fw-medium">مستحق حالياً</span>
                        <span class="fw-bold text-success" id="agingCurrent">
                            {% if data and data.statistics and data.statistics.aging_analysis %}
                                {{ "{:,.2f}".format(data.statistics.aging_analysis.current_due) }} ريال
                            {% else %}
                                0.00 ريال
                            {% endif %}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                        <span class="fw-medium">متأخر 1-30 يوم</span>
                        <span class="fw-bold text-warning" id="aging30">
                            {% if data and data.statistics and data.statistics.aging_analysis %}
                                {{ "{:,.2f}".format(data.statistics.aging_analysis.overdue_1_30) }} ريال
                            {% else %}
                                0.00 ريال
                            {% endif %}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                        <span class="fw-medium">متأخر 31-60 يوم</span>
                        <span class="fw-bold text-warning" id="aging60">
                            {% if data and data.statistics and data.statistics.aging_analysis %}
                                {{ "{:,.2f}".format(data.statistics.aging_analysis.overdue_31_60) }} ريال
                            {% else %}
                                0.00 ريال
                            {% endif %}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                        <span class="fw-medium">متأخر 61-90 يوم</span>
                        <span class="fw-bold text-danger" id="aging90">
                            {% if data and data.statistics and data.statistics.aging_analysis %}
                                {{ "{:,.2f}".format(data.statistics.aging_analysis.overdue_61_90) }} ريال
                            {% else %}
                                0.00 ريال
                            {% endif %}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center p-3 bg-light rounded">
                        <span class="fw-medium">متأخر أكثر من 90 يوم</span>
                        <span class="fw-bold text-danger" id="agingOver90">
                            {% if data and data.statistics and data.statistics.aging_analysis %}
                                {{ "{:,.2f}".format(data.statistics.aging_analysis.overdue_over_90) }} ريال
                            {% else %}
                                0.00 ريال
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Balance Trends Chart -->
        <div class="card-enterprise mb-4">
            <div class="chart-header p-4 pb-0">
                <h3 class="chart-title">
                    <i class="fas fa-chart-line"></i>
                    اتجاهات الأرصدة (آخر 12 شهر)
                </h3>
                <div class="d-flex gap-1">
                    <button class="btn btn-sm btn-outline-secondary" onclick="refreshChart('trends')">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="exportChart('trends')">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
            <div class="chart-container p-4">
                <canvas id="balancesTrendsChart"></canvas>
            </div>
        </div>

        <!-- Suppliers Table -->
        <div class="card-enterprise">
            <div class="d-flex justify-content-between align-items-center p-4 border-bottom">
                <h3 class="h5 mb-0">
                    <i class="fas fa-building ms-2"></i>
                    حسابات الموردين والأرصدة
                </h3>
                <div class="d-flex gap-2">
                    <button class="btn-enterprise btn-secondary-enterprise" onclick="exportSuppliersData()">
                        <i class="fas fa-download"></i>
                        تصدير البيانات
                    </button>
                    <button class="btn-enterprise btn-primary-enterprise" onclick="addNewSupplier()">
                        <i class="fas fa-plus"></i>
                        إضافة مورد
                    </button>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table-enterprise" id="suppliersTable">
                    <thead>
                        <tr>
                            <th style="width: 20%; text-align: right;">المورد</th>
                            <th style="width: 12%; text-align: center;">نوع الحساب</th>
                            <th style="width: 15%; text-align: center;">الرصيد الحالي</th>
                            <th style="width: 15%; text-align: center;">المبلغ المتأخر</th>
                            <th style="width: 12%; text-align: center;">تقييم المخاطر</th>
                            <th style="width: 16%; text-align: center;">آخر نشاط</th>
                            <th style="width: 10%; text-align: center;">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="suppliersTableBody">
                        {% if data and data.suppliers %}
                            {% for supplier in data.suppliers %}
                            <tr>
                                <td style="width: 20%; text-align: right; vertical-align: middle;">
                                    <div class="supplier-name">{{ supplier.supplier_name }}</div>
                                    <div class="supplier-code">{{ supplier.supplier_code }}</div>
                                </td>
                                <td style="width: 12%; text-align: center; vertical-align: middle;">{{ supplier.supplier_type }}</td>
                                <td style="width: 15%; text-align: center; vertical-align: middle;" class="{% if supplier.balance_amount > 0 %}balance-positive{% elif supplier.balance_amount < 0 %}balance-negative{% endif %}">
                                    {{ "{:,.2f}".format(supplier.balance_amount) }} {{ supplier.currency_code }}
                                </td>
                                <td style="width: 15%; text-align: center; vertical-align: middle;" class="{% if supplier.overdue_amount > 0 %}balance-negative{% else %}balance-positive{% endif %}">
                                    {{ "{:,.2f}".format(supplier.overdue_amount) }} {{ supplier.currency_code }}
                                </td>
                                <td style="width: 12%; text-align: center; vertical-align: middle;">
                                    <span class="status-badge {% if supplier.risk_rating == 'منخفض' %}status-low{% elif supplier.risk_rating == 'متوسط' %}status-medium{% else %}status-high{% endif %}">
                                        {{ supplier.risk_rating }}
                                    </span>
                                </td>
                                <td style="width: 16%; text-align: center; vertical-align: middle;">{{ supplier.last_transaction_date }}</td>
                                <td style="width: 10%; text-align: center; vertical-align: middle;">
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewSupplierDetails({{ supplier.supplier_id }})">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="loading-state">
                                        <i class="fas fa-database fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد بيانات موردين متاحة</p>
                                        <small class="text-muted">تحقق من الاتصال بقاعدة البيانات أو أضف موردين جدد</small>
                                    </div>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Modern JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>

    <script>
        // Enterprise Dashboard JavaScript
        $(document).ready(function() {
            initializeDashboard();
            loadFiltersData();
            loadRealData();
            setupEventListeners();

            // تحديث فوري للبطاقات بالعملات الصحيحة
            setTimeout(function() {
                loadDataFromAPI();
            }, 1000);
        });

        function initializeDashboard() {
            console.log('Enterprise Supplier Balance Dashboard initialized');
            animateMetrics();
        }

        function loadRealData() {
            // جلب البيانات الحقيقية من الخادم
            {% if data %}
                // تحديث الرسوم البيانية بالبيانات الحقيقية
                updateDashboardWithRealData({{ data|tojson }});
                loadChartsWithRealData({{ data|tojson }});
            {% else %}
                // جلب البيانات عبر API
                loadDataFromAPI();
            {% endif %}
        }

        function loadFiltersData() {
            // تحميل العملات من قاعدة البيانات
            $.ajax({
                url: '/currencies/api/currencies',
                method: 'GET',
                success: function(response) {
                    if (response.success && response.currencies) {
                        const currencyFilter = $('#currencyFilter');
                        currencyFilter.find('option:not(:first)').remove();

                        response.currencies.forEach(function(currency) {
                            currencyFilter.append(`<option value="${currency.code}">${currency.name_ar} (${currency.code})</option>`);
                        });
                    }
                },
                error: function() {
                    console.log('تعذر تحميل العملات، سيتم استخدام القيم الافتراضية');
                }
            });

            // تحميل أنواع الموردين
            $.ajax({
                url: '/suppliers/api/supplier-types',
                method: 'GET',
                success: function(response) {
                    if (response.success && response.types) {
                        const typeFilter = $('#supplierTypeFilter');
                        typeFilter.find('option:not(:first)').remove();

                        response.types.forEach(function(type) {
                            typeFilter.append(`<option value="${type}">${type}</option>`);
                        });
                    }
                },
                error: function() {
                    console.log('تعذر تحميل أنواع الموردين، سيتم استخدام القيم الافتراضية');
                    // إضافة أنواع افتراضية
                    const typeFilter = $('#supplierTypeFilter');
                    const defaultTypes = ['مورد تجاري', 'مورد خدمات', 'مقاول', 'مورد مواد خام'];
                    defaultTypes.forEach(function(type) {
                        typeFilter.append(`<option value="${type}">${type}</option>`);
                    });
                }
            });
        }

        function loadDataFromAPI() {
            $.ajax({
                url: '/suppliers/api/balances/dashboard',
                method: 'GET',
                data: {
                    period: $('#periodFilter').val(),
                    currency: $('#currencyFilter').val(),
                    balance_status: $('#balanceStatusFilter').val(),
                    supplier_type: $('#supplierTypeFilter').val(),
                    risk_rating: $('#riskFilter').val(),
                    search: $('#supplierSearch').val()
                },
                success: function(response) {
                    if (response.success) {
                        updateDashboardWithRealData(response.data);
                        loadChartsWithRealData(response.data);
                    } else {
                        showNotification('خطأ في جلب البيانات: ' + response.error, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('خطأ في API:', error);
                    showNotification('خطأ في الاتصال بالخادم', 'error');
                }
            });
        }

        function updateDashboardWithRealData(data) {
            // استخدام نفس منطق updateDashboardStats من supplier_balances.js
            if (typeof updateDashboardStats === 'function') {
                updateDashboardStats(data);
            } else {
                // تحديث الإحصائيات مع العملات الصحيحة
                const stats = data.statistics || {};
                const charts = data.charts || {};
                const currencies = charts.by_currency || [];

                // تحديد العملة الرئيسية
                let mainCurrency = 'متنوع';
                if (currencies.length === 1) {
                    mainCurrency = currencies[0].code || currencies[0].currency || 'غير محدد';
                } else if (currencies.length > 1) {
                    mainCurrency = 'عملات متعددة';
                }

                // تحديث البطاقات
                $('#totalBalance').text(formatNumber(stats.total_balance || 0));
                $('#totalOutstanding').text(formatNumber(stats.total_outstanding || 0));
                $('#totalSuppliers').text(stats.total_suppliers || 0);
                $('#overdueAmount').text(formatNumber(stats.overdue_amount || 0));

                // تحديث تسميات البطاقات بالعملات الصحيحة
                $('#totalBalanceLabel').text(`إجمالي الأرصدة (${mainCurrency})`);
                $('#totalOutstandingLabel').text(`إجمالي المستحقات (${mainCurrency})`);
                $('#overdueAmountLabel').text(`المبالغ المتأخرة (${mainCurrency})`);
            }

            // تحديث جدول الموردين
            updateSuppliersTable(data.suppliers || []);
        }

        function updateSuppliersTable(suppliers) {
            const tbody = $('#suppliersTableBody');
            tbody.empty();

            if (suppliers.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="loading-state">
                                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد نتائج مطابقة للفلاتر المحددة</p>
                                <small class="text-muted">جرب تغيير معايير البحث أو الفلاتر</small>
                            </div>
                        </td>
                    </tr>
                `);
                return;
            }

            suppliers.forEach(supplier => {
                const balanceClass = supplier.balance_amount > 0 ? 'balance-positive' :
                                   supplier.balance_amount < 0 ? 'balance-negative' : '';
                const overdueClass = supplier.overdue_amount > 0 ? 'balance-negative' : 'balance-positive';
                const riskClass = supplier.risk_rating === 'منخفض' ? 'status-low' :
                                 supplier.risk_rating === 'متوسط' ? 'status-medium' : 'status-high';

                tbody.append(`
                    <tr>
                        <td>
                            <div class="supplier-name">${supplier.supplier_name}</div>
                            <div class="supplier-code">${supplier.supplier_code}</div>
                        </td>
                        <td>${supplier.supplier_type}</td>
                        <td class="${balanceClass}">
                            ${formatNumber(supplier.balance_amount)} ${supplier.currency_code}
                        </td>
                        <td class="${overdueClass}">
                            ${formatNumber(supplier.overdue_amount)} ${supplier.currency_code}
                        </td>
                        <td>
                            <span class="status-badge ${riskClass}">
                                ${supplier.risk_rating}
                            </span>
                        </td>
                        <td>${supplier.last_transaction_date}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewSupplierDetails(${supplier.supplier_id})">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                `);
            });
        }

        function animateMetrics() {
            $('.metric-value').each(function() {
                const $this = $(this);
                const countTo = parseFloat($this.text().replace(/[^\d.-]/g, ''));

                if (!isNaN(countTo)) {
                    $({ countNum: 0 }).animate({
                        countNum: countTo
                    }, {
                        duration: 2000,
                        easing: 'swing',
                        step: function() {
                            $this.text(formatNumber(Math.floor(this.countNum)));
                        },
                        complete: function() {
                            $this.text(formatNumber(countTo));
                        }
                    });
                }
            });
        }

        function formatNumber(num) {
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(num);
        }

        function loadChartsWithRealData(data) {
            // Currency Distribution Chart
            const currencyCtx = document.getElementById('balancesByCurrencyChart').getContext('2d');

            let currencyLabels = [];
            let currencyData = [];
            let currencyColors = ['#2563eb', '#059669', '#d97706', '#dc2626', '#8b5cf6'];

            if (data.charts && data.charts.by_currency) {
                currencyLabels = data.charts.by_currency.map(item => item.currency);
                currencyData = data.charts.by_currency.map(item => item.amount);
            } else {
                currencyLabels = ['لا توجد بيانات'];
                currencyData = [1];
                currencyColors = ['#e5e7eb'];
            }

            new Chart(currencyCtx, {
                type: 'doughnut',
                data: {
                    labels: currencyLabels,
                    datasets: [{
                        data: currencyData,
                        backgroundColor: currencyColors.slice(0, currencyData.length),
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = formatNumber(context.parsed);
                                    return label + ': ' + value + ' ريال';
                                }
                            }
                        }
                    }
                }
            });

            // Trends Chart
            const trendsCtx = document.getElementById('balancesTrendsChart').getContext('2d');

            let trendsLabels = [];
            let balanceData = [];
            let outstandingData = [];

            if (data.charts && data.charts.trends) {
                trendsLabels = data.charts.trends.map(item => item.month);
                balanceData = data.charts.trends.map(item => item.total_balance);
                outstandingData = data.charts.trends.map(item => item.outstanding_amount);
            } else {
                trendsLabels = ['لا توجد بيانات'];
                balanceData = [0];
                outstandingData = [0];
            }

            new Chart(trendsCtx, {
                type: 'line',
                data: {
                    labels: trendsLabels,
                    datasets: [{
                        label: 'إجمالي الأرصدة',
                        data: balanceData,
                        borderColor: '#2563eb',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'إجمالي المستحقات',
                        data: outstandingData,
                        borderColor: '#dc2626',
                        backgroundColor: 'rgba(220, 38, 38, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = formatNumber(context.parsed.y);
                                    return label + ': ' + value + ' ريال';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatNumber(value) + ' ريال';
                                }
                            }
                        }
                    }
                }
            });
        }

        function setupEventListeners() {
            $('#periodFilter, #currencyFilter, #balanceStatusFilter, #supplierTypeFilter, #riskFilter').on('change', applyFilters);

            // البحث السريع الفوري
            $('#supplierSearch').on('input', debounce(function() {
                const searchTerm = $(this).val().trim();
                if (searchTerm.length >= 2 || searchTerm.length === 0) {
                    applyFilters();
                }
            }, 200));

            // مسح البحث عند الضغط على Escape
            $('#supplierSearch').on('keydown', function(e) {
                if (e.key === 'Escape') {
                    $(this).val('');
                    applyFilters();
                }
            });
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        function applyFilters() {
            console.log('تطبيق الفلاتر...');
            showNotification('جاري تطبيق الفلاتر...', 'info');

            // إظهار مؤشر التحميل
            $('#suppliersTableBody').html(`
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <div class="loading-state">
                            <div class="spinner"></div>
                            <p>جاري تحميل البيانات المفلترة...</p>
                        </div>
                    </td>
                </tr>
            `);

            // جلب البيانات المفلترة
            loadDataFromAPI();
        }

        function refreshDashboard() {
            showNotification('جاري تحديث بيانات لوحة المعلومات...', 'info');
            loadDataFromAPI();
        }

        function exportToExcel() {
            showNotification('جاري التصدير إلى Excel...', 'info');
        }

        function exportToPDF() {
            showNotification('جاري التصدير إلى PDF...', 'info');
        }

        function exportToCSV() {
            showNotification('جاري التصدير إلى CSV...', 'info');
        }

        function showReconciliationModal() {
            window.location.href = '/suppliers/reconciliation_management';
        }

        function clearAllFilters() {
            $('#periodFilter').val('current_month');
            $('#currencyFilter').val('all');
            $('#balanceStatusFilter').val('all');
            $('#supplierTypeFilter').val('all');
            $('#riskFilter').val('all');
            $('#supplierSearch').val('');
            showNotification('تم مسح جميع الفلاتر', 'success');
        }

        function refreshChart(chartType) {
            showNotification(`جاري تحديث الرسم البياني...`, 'info');
        }

        function exportChart(chartType) {
            showNotification(`جاري تصدير الرسم البياني...`, 'info');
        }

        function refreshAgingAnalysis() {
            showNotification('جاري تحديث تحليل الاستحقاقات...', 'info');
        }

        function exportAgingReport() {
            showNotification('جاري تصدير تقرير الاستحقاقات...', 'info');
        }

        function exportSuppliersData() {
            showNotification('جاري تصدير بيانات الموردين...', 'info');
        }

        function addNewSupplier() {
            window.location.href = '/suppliers/accounts_management';
        }

        // وظائف مسار التنقل
        function navigateToSuppliers() {
            window.location.href = '/suppliers';
        }

        function navigateToAccountsManagement() {
            window.location.href = '/suppliers/accounts_management';
        }

        function navigateToTransactionsManagement() {
            window.location.href = '/suppliers/transactions_management';
        }

        function navigateToReconciliation() {
            window.location.href = '/suppliers/reconciliation_management';
        }

        function navigateToReports() {
            window.location.href = '/reports/suppliers';
        }

        // تحسين تجربة المستخدم
        $(document).ready(function() {
            // إضافة تأثيرات hover للروابط
            $('.breadcrumb-link').hover(
                function() {
                    $(this).find('i').addClass('fa-bounce');
                },
                function() {
                    $(this).find('i').removeClass('fa-bounce');
                }
            );

            // تحديث العنوان عند التنقل
            document.title = 'إدارة أرصدة الموردين - نظام إدارة الموردين';

            // تحسين عرض الجدول
            initializeTableAlignment();
        });

        function initializeTableAlignment() {
            // التأكد من اصطفاف الأعمدة
            const table = $('#suppliersTable');

            if (table.length) {
                // إضافة فئات CSS للمحاذاة
                table.find('thead th').each(function(index) {
                    $(this).addClass('table-header-' + index);
                });

                table.find('tbody td').each(function(index) {
                    const colIndex = $(this).index();
                    $(this).addClass('table-cell-' + colIndex);
                });

                // تطبيق محاذاة موحدة
                table.find('th, td').css({
                    'box-sizing': 'border-box',
                    'padding': '12px 8px',
                    'white-space': 'nowrap',
                    'overflow': 'hidden',
                    'text-overflow': 'ellipsis'
                });

                // محاذاة خاصة لعمود المورد
                table.find('th:first-child, td:first-child').css({
                    'text-align': 'right',
                    'white-space': 'normal'
                });

                // محاذاة باقي الأعمدة
                table.find('th:not(:first-child), td:not(:first-child)').css({
                    'text-align': 'center'
                });
            }
        }

        function viewSupplierDetails(supplierId) {
            window.location.href = `/suppliers/account_details/${supplierId}`;
        }

        function showNotification(message, type = 'info') {
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            }[type] || 'alert-info';

            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
                     style="top: 20px; right: 20px; z-index: 9999; min-width: 300px; border-radius: 12px; box-shadow: var(--shadow-lg);">
                    <strong>${message}</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            $('body').append(alertHtml);

            setTimeout(() => {
                $('.alert').fadeOut();
            }, 4000);
        }
    </script>
</body>
</html>