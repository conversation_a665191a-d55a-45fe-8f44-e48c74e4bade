#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار COSCO باستخدام Selenium لمحاكاة متصفح حقيقي
"""

import time
import json
from datetime import datetime

def test_with_selenium():
    """اختبار باستخدام Selenium"""
    try:
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.webdriver.chrome.options import Options
        
        print("🚀 بدء اختبار Selenium...")
        
        # إعداد Chrome options
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # تشغيل بدون واجهة
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # إنشاء driver
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)
        
        try:
            print("📡 الوصول لموقع COSCO...")
            driver.get("https://elines.coscoshipping.com/ebusiness/cargoTracking")
            
            print("⏳ انتظار تحميل الصفحة...")
            time.sleep(5)
            
            print(f"📊 العنوان: {driver.title}")
            print(f"📊 URL الحالي: {driver.current_url}")
            
            # البحث عن حقول الإدخال
            print("🔍 البحث عن حقول الإدخال...")
            
            # محاولة العثور على حقل رقم التتبع
            possible_selectors = [
                "input[name='trackingNo']",
                "input[name='trackingNumber']",
                "input[name='containerNo']",
                "input[name='bookingNo']",
                "input[placeholder*='tracking']",
                "input[placeholder*='container']",
                "input[placeholder*='booking']",
                "input[type='text']"
            ]
            
            tracking_input = None
            for selector in possible_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        print(f"✅ وجد حقل: {selector} ({len(elements)} عنصر)")
                        tracking_input = elements[0]
                        break
                except:
                    continue
            
            if tracking_input:
                print("📝 إدخال رقم تجريبي...")
                tracking_input.clear()
                tracking_input.send_keys("TEST123456")
                
                # البحث عن زر البحث
                print("🔍 البحث عن زر البحث...")
                search_selectors = [
                    "button[type='submit']",
                    "input[type='submit']",
                    "button:contains('Search')",
                    "button:contains('Track')",
                    ".search-btn",
                    ".track-btn"
                ]
                
                search_button = None
                for selector in search_selectors:
                    try:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            print(f"✅ وجد زر: {selector}")
                            search_button = elements[0]
                            break
                    except:
                        continue
                
                if search_button:
                    print("🔍 الضغط على زر البحث...")
                    search_button.click()
                    
                    print("⏳ انتظار النتائج...")
                    time.sleep(10)
                    
                    # تحليل النتائج
                    print("📊 تحليل النتائج...")
                    page_source = driver.page_source
                    
                    # البحث عن كلمات مفتاحية
                    keywords = ['ETD', 'ETA', 'vessel', 'voyage', 'container', 'no result', 'not found']
                    found_keywords = []
                    
                    for keyword in keywords:
                        if keyword.lower() in page_source.lower():
                            found_keywords.append(keyword)
                    
                    print(f"🔍 كلمات مفتاحية موجودة: {found_keywords}")
                    
                    # حفظ النتائج
                    with open('cosco_selenium_result.html', 'w', encoding='utf-8') as f:
                        f.write(page_source)
                    print("💾 تم حفظ النتائج في cosco_selenium_result.html")
                    
                else:
                    print("❌ لم يتم العثور على زر البحث")
            else:
                print("❌ لم يتم العثور على حقل الإدخال")
            
            # تحليل network requests
            print("🌐 تحليل network requests...")
            logs = driver.get_log('performance')
            
            api_requests = []
            for log in logs:
                message = json.loads(log['message'])
                if message['message']['method'] == 'Network.requestWillBeSent':
                    url = message['message']['params']['request']['url']
                    if 'api' in url.lower() or 'tracking' in url.lower():
                        api_requests.append(url)
            
            print(f"📡 API requests found: {len(api_requests)}")
            for req in api_requests[:5]:
                print(f"  - {req}")
            
        finally:
            driver.quit()
            
        return True
        
    except ImportError:
        print("❌ Selenium غير مثبت. تثبيت باستخدام: pip install selenium")
        return False
    except Exception as e:
        print(f"❌ خطأ في Selenium: {e}")
        return False

def test_without_selenium():
    """اختبار بدون Selenium - طريقة بديلة"""
    print("\n🔄 اختبار بدون Selenium...")
    
    import requests
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    session = requests.Session()
    session.verify = False
    
    # محاولة تقليد browser behavior
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1'
    })
    
    try:
        print("📡 الوصول للموقع...")
        response = session.get("https://elines.coscoshipping.com/ebusiness/cargoTracking", timeout=20)
        
        if response.status_code == 200:
            print("✅ تم الوصول بنجاح")
            
            # محاولة البحث عن form action
            import re
            form_actions = re.findall(r'<form[^>]*action="([^"]*)"', response.text, re.IGNORECASE)
            print(f"📝 Form actions: {form_actions}")
            
            # محاولة البحث عن JavaScript API calls
            api_calls = re.findall(r'fetch\s*\(\s*["\']([^"\']+)["\']', response.text, re.IGNORECASE)
            api_calls.extend(re.findall(r'\$\.post\s*\(\s*["\']([^"\']+)["\']', response.text, re.IGNORECASE))
            
            print(f"📡 API calls found: {api_calls}")
            
            return True
        else:
            print(f"❌ فشل: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def main():
    print("=" * 80)
    print("🧪 اختبار شامل لموقع COSCO")
    print("=" * 80)
    
    # محاولة Selenium أولاً
    selenium_success = test_with_selenium()
    
    # إذا فشل Selenium، جرب الطريقة البديلة
    if not selenium_success:
        test_without_selenium()
    
    print("\n" + "=" * 80)
    print("✅ انتهى الاختبار")
    print("=" * 80)

if __name__ == "__main__":
    main()
