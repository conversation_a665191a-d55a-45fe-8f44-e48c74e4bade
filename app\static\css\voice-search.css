/**
 * أنماط البحث الصوتي
 * Voice Search Styles
 */

/* زر البحث الصوتي */
.voice-search-btn {
    border: 1px solid #007bff;
    background: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0 0.375rem 0.375rem 0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.voice-search-btn:hover {
    background: #f8f9fa;
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,123,255,0.2);
}

.voice-search-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0,123,255,0.2);
}

/* حالة الاستماع */
.voice-search-btn.listening {
    background: #fff3cd;
    border-color: #ffc107;
    animation: pulse-listening 1.5s infinite;
}

.voice-search-btn.listening:hover {
    background: #fff3cd;
    border-color: #e0a800;
}

/* تأثير النبض أثناء الاستماع */
@keyframes pulse-listening {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
    }
}

/* تأثير الموجة الصوتية */
.voice-search-btn.listening::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, rgba(255, 193, 7, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: sound-wave 1s infinite;
}

@keyframes sound-wave {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.5;
    }
}

/* أيقونة الميكروفون */
.voice-search-btn i {
    font-size: 1rem;
    transition: all 0.3s ease;
}

.voice-search-btn:hover i {
    transform: scale(1.1);
}

.voice-search-btn.listening i {
    color: #ffc107 !important;
    animation: microphone-glow 1s infinite alternate;
}

@keyframes microphone-glow {
    from {
        text-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
    }
    to {
        text-shadow: 0 0 10px rgba(255, 193, 7, 0.8);
    }
}

/* تحسين مجموعة الإدخال */
.input-group .voice-search-btn {
    border-left: none;
    margin-left: -1px;
}

.input-group .form-control:focus + .voice-search-btn {
    border-color: #80bdff;
}

/* تأثيرات إضافية للتفاعل */
.voice-search-btn:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.voice-search-btn.listening:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    .voice-search-btn {
        padding: 0.4rem 0.6rem;
    }
    
    .voice-search-btn i {
        font-size: 0.9rem;
    }
}

/* تأثير التحميل */
.voice-search-btn.processing {
    background: #e3f2fd;
    border-color: #2196f3;
    pointer-events: none;
}

.voice-search-btn.processing i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* رسائل الحالة */
.voice-search-status {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0 0 0.375rem 0.375rem;
    font-size: 0.75rem;
    text-align: center;
    z-index: 1000;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.voice-search-status.show {
    opacity: 1;
    transform: translateY(0);
}

/* تحسين الوصولية */
.voice-search-btn[aria-pressed="true"] {
    background: #fff3cd;
    border-color: #ffc107;
}

/* تأثيرات خاصة للنجاح */
.voice-search-btn.success {
    background: #d4edda;
    border-color: #28a745;
    animation: success-flash 0.5s ease;
}

@keyframes success-flash {
    0%, 100% {
        background: #d4edda;
    }
    50% {
        background: #c3e6cb;
    }
}

.voice-search-btn.success i {
    color: #28a745 !important;
}

/* تأثيرات خاصة للخطأ */
.voice-search-btn.error {
    background: #f8d7da;
    border-color: #dc3545;
    animation: error-shake 0.5s ease;
}

@keyframes error-shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-2px);
    }
    75% {
        transform: translateX(2px);
    }
}

.voice-search-btn.error i {
    color: #dc3545 !important;
}

/* تحسين التباين للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .voice-search-btn {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .voice-search-btn:hover {
        background: #4a5568;
        border-color: #718096;
    }
    
    .voice-search-btn.listening {
        background: #744210;
        border-color: #d69e2e;
    }
}

/* تحسين للطباعة */
@media print {
    .voice-search-btn {
        display: none !important;
    }
}
