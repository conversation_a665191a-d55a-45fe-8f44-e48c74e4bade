#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إضافة عمود branch_id إلى جدول OPENING_BALANCES
Add branch_id column to OPENING_BALANCES table
"""

import sys
import os
sys.path.insert(0, os.getcwd())

from oracle_manager import OracleManager

def add_branch_id_column():
    """إضافة عمود branch_id إلى جدول OPENING_BALANCES"""
    print("🔧 إضافة عمود branch_id إلى جدول OPENING_BALANCES...")
    
    oracle = OracleManager()
    if not oracle.connect():
        print("❌ فشل الاتصال بقاعدة البيانات")
        return False
    
    try:
        # التحقق من وجود العمود أولاً
        check_query = """
        SELECT COUNT(*) 
        FROM user_tab_columns 
        WHERE table_name = 'OPENING_BALANCES' 
        AND column_name = 'BRANCH_ID'
        """
        
        result = oracle.execute_query(check_query)
        
        if result and result[0][0] > 0:
            print("✅ عمود branch_id موجود مسبقاً")
            return True
        
        # إضافة العمود
        print("📝 إضافة عمود branch_id...")
        
        alter_query = """
        ALTER TABLE OPENING_BALANCES 
        ADD (
            branch_id NUMBER DEFAULT 1 NOT NULL,
            CONSTRAINT fk_opening_balances_branch 
            FOREIGN KEY (branch_id) 
            REFERENCES BRANCHES(id)
        )
        """
        
        oracle.execute_update(alter_query)
        print("✅ تم إضافة عمود branch_id بنجاح")
        
        # إضافة تعليق على العمود
        comment_query = """
        COMMENT ON COLUMN OPENING_BALANCES.branch_id 
        IS 'معرف الفرع - مرتبط بجدول BRANCHES'
        """
        
        oracle.execute_update(comment_query)
        print("✅ تم إضافة تعليق على العمود")
        
        # التحقق من النتيجة
        verify_query = """
        SELECT column_name, data_type, nullable, data_default
        FROM user_tab_columns 
        WHERE table_name = 'OPENING_BALANCES' 
        AND column_name = 'BRANCH_ID'
        """
        
        verify_result = oracle.execute_query(verify_query)
        
        if verify_result:
            col_info = verify_result[0]
            print(f"✅ تم التحقق من العمود:")
            print(f"   الاسم: {col_info[0]}")
            print(f"   النوع: {col_info[1]}")
            print(f"   قابل للفراغ: {col_info[2]}")
            print(f"   القيمة الافتراضية: {col_info[3]}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة العمود: {e}")
        
        # محاولة إضافة العمود بدون Foreign Key إذا فشل
        try:
            print("🔄 محاولة إضافة العمود بدون Foreign Key...")
            
            simple_alter_query = """
            ALTER TABLE OPENING_BALANCES 
            ADD branch_id NUMBER DEFAULT 1
            """
            
            oracle.execute_update(simple_alter_query)
            print("✅ تم إضافة عمود branch_id بدون Foreign Key")
            
            return True
            
        except Exception as e2:
            print(f"❌ فشل في إضافة العمود: {e2}")
            return False
        
    finally:
        oracle.disconnect()

def check_branches_table():
    """التحقق من وجود جدول BRANCHES"""
    print("\n🔍 التحقق من جدول BRANCHES...")
    
    oracle = OracleManager()
    if not oracle.connect():
        print("❌ فشل الاتصال بقاعدة البيانات")
        return False
    
    try:
        # التحقق من وجود الجدول
        check_query = """
        SELECT COUNT(*) 
        FROM user_tables 
        WHERE table_name = 'BRANCHES'
        """
        
        result = oracle.execute_query(check_query)
        
        if result and result[0][0] > 0:
            print("✅ جدول BRANCHES موجود")
            
            # جلب عينة من البيانات
            sample_query = """
            SELECT id, name_ar, name_en, is_active
            FROM BRANCHES 
            WHERE ROWNUM <= 5
            ORDER BY id
            """
            
            sample_result = oracle.execute_query(sample_query)
            
            if sample_result:
                print(f"📊 يحتوي على {len(sample_result)} فرع (عينة):")
                for row in sample_result:
                    print(f"   {row[0]}. {row[1]} ({row[2]}) - نشط: {row[3]}")
            else:
                print("⚠️ الجدول فارغ")
            
            return True
        else:
            print("❌ جدول BRANCHES غير موجود")
            return create_branches_table(oracle)
            
    except Exception as e:
        print(f"❌ خطأ في فحص جدول BRANCHES: {e}")
        return False
    finally:
        oracle.disconnect()

def create_branches_table(oracle):
    """إنشاء جدول BRANCHES إذا لم يكن موجوداً"""
    print("🔧 إنشاء جدول BRANCHES...")
    
    try:
        create_query = """
        CREATE TABLE BRANCHES (
            id NUMBER PRIMARY KEY,
            branch_code VARCHAR2(20) UNIQUE NOT NULL,
            name_ar VARCHAR2(200) NOT NULL,
            name_en VARCHAR2(200),
            address_ar VARCHAR2(500),
            address_en VARCHAR2(500),
            city VARCHAR2(100),
            phone VARCHAR2(50),
            email VARCHAR2(100),
            manager_name VARCHAR2(100),
            is_active NUMBER(1) DEFAULT 1,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by NUMBER,
            updated_date TIMESTAMP,
            updated_by NUMBER
        )
        """
        
        oracle.execute_update(create_query)
        print("✅ تم إنشاء جدول BRANCHES")
        
        # إنشاء sequence للمعرفات
        sequence_query = """
        CREATE SEQUENCE branches_seq
        START WITH 1
        INCREMENT BY 1
        NOCACHE
        """
        
        try:
            oracle.execute_update(sequence_query)
            print("✅ تم إنشاء sequence للفروع")
        except:
            print("⚠️ sequence موجود مسبقاً")
        
        # إدراج فروع افتراضية
        insert_query = """
        INSERT INTO BRANCHES (id, branch_code, name_ar, name_en, city, is_active)
        VALUES (1, 'MAIN', 'الفرع الرئيسي', 'Main Branch', 'الرياض', 1)
        """
        
        oracle.execute_update(insert_query)
        
        insert_query2 = """
        INSERT INTO BRANCHES (id, branch_code, name_ar, name_en, city, is_active)
        VALUES (2, 'JED', 'فرع جدة', 'Jeddah Branch', 'جدة', 1)
        """
        
        oracle.execute_update(insert_query2)
        
        print("✅ تم إدراج فروع افتراضية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول BRANCHES: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 إضافة دعم الفروع لجدول الأرصدة الافتتاحية")
    print("=" * 60)
    
    # التحقق من جدول الفروع أولاً
    if not check_branches_table():
        print("❌ فشل في التحقق من جدول BRANCHES")
        return False
    
    # إضافة عمود branch_id
    if not add_branch_id_column():
        print("❌ فشل في إضافة عمود branch_id")
        return False
    
    print("\n🎉 تم إضافة دعم الفروع بنجاح!")
    print("\n📝 ما تم إنجازه:")
    print("   ✅ التحقق من وجود جدول BRANCHES")
    print("   ✅ إضافة عمود branch_id إلى OPENING_BALANCES")
    print("   ✅ ربط العمود بجدول BRANCHES")
    print("   ✅ تعيين قيمة افتراضية (1 = الفرع الرئيسي)")
    
    return True

if __name__ == "__main__":
    main()
