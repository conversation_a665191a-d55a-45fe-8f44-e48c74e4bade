# -*- coding: utf-8 -*-
"""
مسارات النظام المالي
Financial System Routes
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, abort
from flask_login import login_required, current_user
from app.financial import bp
from app import db
from app.models import Invoice, Payment, Budget, Expense
from app.financial.forms import InvoiceForm, PaymentForm, BudgetForm, ExpenseForm, FinancialSearchForm
from datetime import datetime, timedelta
from sqlalchemy import or_, and_, func
from decimal import Decimal

@bp.route('/')
def index():
    """لوحة المعلومات المالية"""
    
    # إحصائيات مالية سريعة
    current_month = datetime.now().replace(day=1)
    next_month = (current_month + timedelta(days=32)).replace(day=1)
    
    stats = {
        'total_invoices': Invoice.query.count(),
        'pending_invoices': Invoice.query.filter_by(status='sent').count(),
        'paid_invoices': Invoice.query.filter_by(status='paid').count(),
        'monthly_expenses': db.session.query(func.sum(Expense.amount))\
            .filter(Expense.expense_date >= current_month,
                   Expense.expense_date < next_month).scalar() or 0,
        'total_outstanding': db.session.query(func.sum(Invoice.remaining_amount))\
            .filter(Invoice.status.in_(['sent', 'approved'])).scalar() or 0,
        'monthly_payments': db.session.query(func.sum(Payment.amount))\
            .filter(Payment.payment_date >= current_month,
                   Payment.payment_date < next_month).scalar() or 0
    }
    
    # آخر المعاملات
    recent_invoices = Invoice.query.order_by(Invoice.created_at.desc()).limit(5).all()
    recent_payments = Payment.query.order_by(Payment.created_at.desc()).limit(5).all()
    recent_expenses = Expense.query.order_by(Expense.created_at.desc()).limit(5).all()
    
    # الفواتير المستحقة
    overdue_invoices = Invoice.query.filter(
        Invoice.due_date < datetime.now().date(),
        Invoice.status.in_(['sent', 'approved']),
        Invoice.remaining_amount > 0
    ).all()
    
    return render_template('financial/index.html',
                         stats=stats,
                         recent_invoices=recent_invoices,
                         recent_payments=recent_payments,
                         recent_expenses=recent_expenses,
                         overdue_invoices=overdue_invoices,
                         title='النظام المالي')

@bp.route('/invoices')
def invoices():
    """صفحة الفواتير"""
    page = request.args.get('page', 1, type=int)
    search_form = FinancialSearchForm()
    
    # بناء الاستعلام الأساسي
    query = Invoice.query
    
    # تطبيق الفلاتر
    if request.args.get('search_term'):
        search_term = f"%{request.args.get('search_term')}%"
        query = query.filter(
            or_(
                Invoice.invoice_number.like(search_term),
                Invoice.notes.like(search_term)
            )
        )
    
    if request.args.get('status'):
        query = query.filter(Invoice.status == request.args.get('status'))
    
    if request.args.get('date_from'):
        date_from = datetime.strptime(request.args.get('date_from'), '%Y-%m-%d').date()
        query = query.filter(Invoice.invoice_date >= date_from)
    
    if request.args.get('date_to'):
        date_to = datetime.strptime(request.args.get('date_to'), '%Y-%m-%d').date()
        query = query.filter(Invoice.invoice_date <= date_to)
    
    # ترتيب النتائج
    query = query.order_by(Invoice.created_at.desc())
    
    # تطبيق التصفح
    invoices = query.paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('financial/invoices.html',
                         invoices=invoices,
                         search_form=search_form,
                         title='الفواتير')

@bp.route('/invoices/new', methods=['GET', 'POST'])
def new_invoice():
    """إنشاء فاتورة جديدة"""
    form = InvoiceForm()
    
    if form.validate_on_submit():
        try:
            # التحقق من عدم تكرار رقم الفاتورة
            existing_invoice = Invoice.query.filter_by(invoice_number=form.invoice_number.data).first()
            if existing_invoice:
                flash('رقم الفاتورة موجود بالفعل', 'error')
                return render_template('financial/new_invoice.html', form=form, title='إنشاء فاتورة جديدة')
            
            # حساب المبلغ الإجمالي
            total_amount = form.subtotal.data + (form.tax_amount.data or 0) - (form.discount_amount.data or 0)
            
            # إنشاء الفاتورة
            invoice = Invoice(
                invoice_number=form.invoice_number.data,
                supplier_id=form.supplier_id.data,
                purchase_order_id=form.purchase_order_id.data if form.purchase_order_id.data else None,
                invoice_date=form.invoice_date.data,
                due_date=form.due_date.data,
                subtotal=form.subtotal.data,
                tax_amount=form.tax_amount.data or 0,
                discount_amount=form.discount_amount.data or 0,
                total_amount=total_amount,
                remaining_amount=total_amount,
                currency=form.currency.data,
                status=form.status.data,
                notes=form.notes.data,
                created_by=current_user.id
            )
            
            db.session.add(invoice)
            db.session.commit()
            
            flash(f'تم إنشاء الفاتورة {invoice.invoice_number} بنجاح', 'success')
            return redirect(url_for('financial.view_invoice', id=invoice.id))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إنشاء الفاتورة', 'error')
            print(f"Error creating invoice: {e}")
    
    return render_template('financial/new_invoice.html',
                         form=form,
                         title='إنشاء فاتورة جديدة')

@bp.route('/invoices/<int:id>')
def view_invoice(id):
    """عرض تفاصيل الفاتورة"""
    invoice = Invoice.query.get_or_404(id)
    
    # الحصول على الدفعات المرتبطة
    payments = Payment.query.filter_by(invoice_id=id).order_by(Payment.payment_date.desc()).all()
    
    return render_template('financial/view_invoice.html',
                         invoice=invoice,
                         payments=payments,
                         title=f'الفاتورة: {invoice.invoice_number}')

@bp.route('/payments')
def payments():
    """صفحة الدفعات"""
    page = request.args.get('page', 1, type=int)
    
    # بناء الاستعلام الأساسي
    query = Payment.query
    
    # تطبيق الفلاتر
    if request.args.get('search_term'):
        search_term = f"%{request.args.get('search_term')}%"
        query = query.filter(
            or_(
                Payment.payment_number.like(search_term),
                Payment.reference_number.like(search_term),
                Payment.notes.like(search_term)
            )
        )
    
    if request.args.get('date_from'):
        date_from = datetime.strptime(request.args.get('date_from'), '%Y-%m-%d').date()
        query = query.filter(Payment.payment_date >= date_from)
    
    if request.args.get('date_to'):
        date_to = datetime.strptime(request.args.get('date_to'), '%Y-%m-%d').date()
        query = query.filter(Payment.payment_date <= date_to)
    
    # ترتيب النتائج
    query = query.order_by(Payment.created_at.desc())
    
    # تطبيق التصفح
    payments = query.paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('financial/payments.html',
                         payments=payments,
                         title='الدفعات')

@bp.route('/payments/new', methods=['GET', 'POST'])
def new_payment():
    """تسجيل دفعة جديدة"""
    form = PaymentForm()
    
    if form.validate_on_submit():
        try:
            # التحقق من الفاتورة والمبلغ المتبقي
            invoice = Invoice.query.get(form.invoice_id.data)
            if not invoice:
                flash('الفاتورة غير موجودة', 'error')
                return render_template('financial/new_payment.html', form=form, title='تسجيل دفعة جديدة')
            
            if form.amount.data > invoice.remaining_amount:
                flash('المبلغ المدفوع أكبر من المبلغ المتبقي', 'error')
                return render_template('financial/new_payment.html', form=form, title='تسجيل دفعة جديدة')
            
            # إنشاء الدفعة
            payment = Payment(
                payment_number=form.payment_number.data,
                invoice_id=form.invoice_id.data,
                payment_date=form.payment_date.data,
                amount=form.amount.data,
                payment_method=form.payment_method.data,
                reference_number=form.reference_number.data,
                bank_name=form.bank_name.data,
                notes=form.notes.data,
                created_by=current_user.id
            )
            
            db.session.add(payment)
            
            # تحديث المبلغ المتبقي في الفاتورة
            invoice.remaining_amount -= payment.amount
            invoice.paid_amount = (invoice.paid_amount or 0) + payment.amount
            
            # تحديث حالة الفاتورة
            if invoice.remaining_amount <= 0:
                invoice.status = 'paid'
                invoice.paid_date = payment.payment_date
            
            invoice.updated_at = datetime.utcnow()
            
            db.session.commit()
            
            flash(f'تم تسجيل الدفعة {payment.payment_number} بنجاح', 'success')
            return redirect(url_for('financial.view_invoice', id=invoice.id))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تسجيل الدفعة', 'error')
            print(f"Error creating payment: {e}")
    
    return render_template('financial/new_payment.html',
                         form=form,
                         title='تسجيل دفعة جديدة')

@bp.route('/expenses')
def expenses():
    """صفحة المصروفات"""
    page = request.args.get('page', 1, type=int)
    
    # بناء الاستعلام الأساسي
    query = Expense.query
    
    # تطبيق الفلاتر
    if request.args.get('search_term'):
        search_term = f"%{request.args.get('search_term')}%"
        query = query.filter(
            or_(
                Expense.expense_number.like(search_term),
                Expense.description.like(search_term),
                Expense.notes.like(search_term)
            )
        )
    
    if request.args.get('category'):
        query = query.filter(Expense.category == request.args.get('category'))
    
    if request.args.get('date_from'):
        date_from = datetime.strptime(request.args.get('date_from'), '%Y-%m-%d').date()
        query = query.filter(Expense.expense_date >= date_from)
    
    if request.args.get('date_to'):
        date_to = datetime.strptime(request.args.get('date_to'), '%Y-%m-%d').date()
        query = query.filter(Expense.expense_date <= date_to)
    
    # ترتيب النتائج
    query = query.order_by(Expense.created_at.desc())
    
    # تطبيق التصفح
    expenses = query.paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('financial/expenses.html',
                         expenses=expenses,
                         title='المصروفات')

@bp.route('/expenses/new', methods=['GET', 'POST'])
def new_expense():
    """تسجيل مصروف جديد"""
    form = ExpenseForm()
    
    if form.validate_on_submit():
        try:
            # إنشاء المصروف
            expense = Expense(
                expense_number=form.expense_number.data,
                description=form.description.data,
                category=form.category.data,
                amount=form.amount.data,
                expense_date=form.expense_date.data,
                supplier_id=form.supplier_id.data if form.supplier_id.data else None,
                payment_method=form.payment_method.data,
                receipt_number=form.receipt_number.data,
                notes=form.notes.data,
                created_by=current_user.id
            )
            
            db.session.add(expense)
            db.session.commit()
            
            flash(f'تم تسجيل المصروف {expense.expense_number} بنجاح', 'success')
            return redirect(url_for('financial.expenses'))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تسجيل المصروف', 'error')
            print(f"Error creating expense: {e}")
    
    return render_template('financial/new_expense.html',
                         form=form,
                         title='تسجيل مصروف جديد')

@bp.route('/budgets')
def budgets():
    """صفحة الميزانيات"""
    budgets = Budget.query.order_by(Budget.created_at.desc()).all()
    
    return render_template('financial/budgets.html',
                         budgets=budgets,
                         title='الميزانيات')

@bp.route('/budgets/new', methods=['GET', 'POST'])
def new_budget():
    """إنشاء ميزانية جديدة"""
    form = BudgetForm()
    
    if form.validate_on_submit():
        try:
            # إنشاء الميزانية
            budget = Budget(
                name=form.name.data,
                description=form.description.data,
                budget_type=form.budget_type.data,
                start_date=form.start_date.data,
                end_date=form.end_date.data,
                total_budget=form.total_budget.data,
                currency=form.currency.data,
                status=form.status.data,
                notes=form.notes.data,
                created_by=current_user.id
            )
            
            db.session.add(budget)
            db.session.commit()
            
            flash(f'تم إنشاء الميزانية {budget.name} بنجاح', 'success')
            return redirect(url_for('financial.budgets'))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إنشاء الميزانية', 'error')
            print(f"Error creating budget: {e}")
    
    return render_template('financial/new_budget.html',
                         form=form,
                         title='إنشاء ميزانية جديدة')

@bp.route('/reports/financial-summary')
def financial_summary():
    """تقرير الملخص المالي"""
    # تحديد الفترة (آخر 12 شهر)
    end_date = datetime.now().date()
    start_date = end_date.replace(year=end_date.year - 1)
    
    # إحصائيات الفواتير
    invoice_stats = {
        'total_invoices': Invoice.query.filter(
            Invoice.invoice_date >= start_date,
            Invoice.invoice_date <= end_date
        ).count(),
        'total_amount': db.session.query(func.sum(Invoice.total_amount)).filter(
            Invoice.invoice_date >= start_date,
            Invoice.invoice_date <= end_date
        ).scalar() or 0,
        'paid_amount': db.session.query(func.sum(Invoice.paid_amount)).filter(
            Invoice.invoice_date >= start_date,
            Invoice.invoice_date <= end_date
        ).scalar() or 0,
        'outstanding_amount': db.session.query(func.sum(Invoice.remaining_amount)).filter(
            Invoice.status.in_(['sent', 'approved']),
            Invoice.invoice_date >= start_date,
            Invoice.invoice_date <= end_date
        ).scalar() or 0
    }
    
    # إحصائيات المصروفات
    expense_stats = {
        'total_expenses': Expense.query.filter(
            Expense.expense_date >= start_date,
            Expense.expense_date <= end_date
        ).count(),
        'total_amount': db.session.query(func.sum(Expense.amount)).filter(
            Expense.expense_date >= start_date,
            Expense.expense_date <= end_date
        ).scalar() or 0
    }
    
    return render_template('financial/financial_summary.html',
                         invoice_stats=invoice_stats,
                         expense_stats=expense_stats,
                         start_date=start_date,
                         end_date=end_date,
                         title='الملخص المالي')
