-- =====================================================
-- إنشاء جميع الجداول المطلوبة للتكامل
-- Create All Required Tables for Integration
-- =====================================================

-- 1. جدول ربط أوامر الشراء بالمدفوعات
CREATE TABLE PURCHASE_ORDER_PAYMENTS (
    id NUMBER PRIMARY KEY,
    purchase_order_id NUMBER NOT NULL,
    supplier_payment_transfer_id NUMBER,
    transfer_request_id NUMBER,
    transfer_id NUMBER,
    payment_type VARCHAR2(30) NOT NULL,
    payment_amount NUMBER(15,2) NOT NULL,
    currency_code VARCHAR2(3) NOT NULL,
    exchange_rate NUMBER(15,6) DEFAULT 1,
    payment_status VARCHAR2(20) DEFAULT 'PENDING',
    payment_due_date DATE,
    payment_requested_date DATE DEFAULT SYSDATE,
    payment_approved_date DATE,
    payment_executed_date DATE,
    payment_completed_date DATE,
    requested_by NUMBER,
    approved_by NUMBER,
    executed_by NUMBER,
    notes CLOB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER
);

-- 2. جدول تتبع حالة أوامر الشراء
CREATE TABLE PURCHASE_ORDER_STATUS_LOG (
    id NUMBER PRIMARY KEY,
    purchase_order_id NUMBER NOT NULL,
    old_status VARCHAR2(30),
    new_status VARCHAR2(30) NOT NULL,
    status_type VARCHAR2(20) NOT NULL,
    change_reason VARCHAR2(500),
    change_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    changed_by NUMBER,
    related_document_type VARCHAR2(30),
    related_document_id NUMBER,
    system_generated CHAR(1) DEFAULT 'N',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. جدول استلام البضائع
CREATE TABLE GOODS_RECEIPTS (
    id NUMBER PRIMARY KEY,
    receipt_number VARCHAR2(50) UNIQUE NOT NULL,
    purchase_order_id NUMBER NOT NULL,
    supplier_id NUMBER NOT NULL,
    receipt_date DATE DEFAULT SYSDATE,
    delivery_note_number VARCHAR2(100),
    invoice_number VARCHAR2(100),
    receipt_status VARCHAR2(20) DEFAULT 'PENDING',
    quality_status VARCHAR2(20) DEFAULT 'PENDING',
    received_by NUMBER,
    inspected_by NUMBER,
    approved_by NUMBER,
    receipt_notes CLOB,
    quality_notes CLOB,
    rejection_reason VARCHAR2(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER
);

-- 4. جدول تفاصيل استلام البضائع
CREATE TABLE GOODS_RECEIPT_ITEMS (
    id NUMBER PRIMARY KEY,
    goods_receipt_id NUMBER NOT NULL,
    po_item_id NUMBER NOT NULL,
    ordered_quantity NUMBER(15,3) NOT NULL,
    delivered_quantity NUMBER(15,3) NOT NULL,
    received_quantity NUMBER(15,3) NOT NULL,
    rejected_quantity NUMBER(15,3) DEFAULT 0,
    accepted_quantity NUMBER(15,3) DEFAULT 0,
    quality_status VARCHAR2(20) DEFAULT 'PENDING',
    batch_number VARCHAR2(100),
    serial_numbers CLOB,
    production_date DATE,
    expiry_date DATE,
    item_notes VARCHAR2(500),
    rejection_reason VARCHAR2(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER
);

-- 5. جدول دورات المطابقة
CREATE TABLE RECONCILIATION_CYCLES (
    cycle_id NUMBER PRIMARY KEY,
    cycle_name VARCHAR2(100) NOT NULL,
    cycle_description VARCHAR2(500),
    reconciliation_date DATE NOT NULL,
    period_from DATE NOT NULL,
    period_to DATE NOT NULL,
    cycle_type VARCHAR2(30) DEFAULT 'MONTHLY',
    status VARCHAR2(20) DEFAULT 'OPEN',
    total_suppliers NUMBER DEFAULT 0,
    matched_suppliers NUMBER DEFAULT 0,
    unmatched_suppliers NUMBER DEFAULT 0,
    total_differences_amount NUMBER(15,2) DEFAULT 0,
    started_by NUMBER,
    started_date TIMESTAMP,
    completed_by NUMBER,
    completed_date TIMESTAMP,
    notes CLOB,
    attachment_path VARCHAR2(500),
    created_by NUMBER NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by NUMBER,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 6. جدول مطابقة الأرصدة التفصيلي
CREATE TABLE SUPPLIER_RECONCILIATION (
    reconciliation_id NUMBER PRIMARY KEY,
    cycle_id NUMBER NOT NULL,
    supplier_id NUMBER NOT NULL,
    currency_code VARCHAR2(3) NOT NULL,
    system_opening_balance NUMBER(15,2) DEFAULT 0,
    system_debit_amount NUMBER(15,2) DEFAULT 0,
    system_credit_amount NUMBER(15,2) DEFAULT 0,
    system_closing_balance NUMBER(15,2) DEFAULT 0,
    supplier_opening_balance NUMBER(15,2) DEFAULT 0,
    supplier_debit_amount NUMBER(15,2) DEFAULT 0,
    supplier_credit_amount NUMBER(15,2) DEFAULT 0,
    supplier_closing_balance NUMBER(15,2) DEFAULT 0,
    opening_difference NUMBER(15,2) DEFAULT 0,
    debit_difference NUMBER(15,2) DEFAULT 0,
    credit_difference NUMBER(15,2) DEFAULT 0,
    closing_difference NUMBER(15,2) DEFAULT 0,
    total_difference NUMBER(15,2) DEFAULT 0,
    reconciliation_status VARCHAR2(20) DEFAULT 'PENDING',
    match_tolerance NUMBER(15,2) DEFAULT 0.01,
    auto_matched CHAR(1) DEFAULT 'N',
    supplier_statement_date DATE,
    supplier_statement_reference VARCHAR2(100),
    supplier_statement_file_path VARCHAR2(500),
    reconciliation_notes CLOB,
    system_notes CLOB,
    supplier_notes CLOB,
    reconciled_by NUMBER,
    reconciled_date TIMESTAMP,
    approved_by NUMBER,
    approved_date TIMESTAMP,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 7. جدول تفاصيل الفروقات
CREATE TABLE RECONCILIATION_DIFFERENCES (
    difference_id NUMBER PRIMARY KEY,
    reconciliation_id NUMBER NOT NULL,
    difference_type VARCHAR2(50) NOT NULL,
    difference_category VARCHAR2(30),
    system_transaction_id NUMBER,
    system_transaction_date DATE,
    system_reference_number VARCHAR2(100),
    system_amount NUMBER(15,2),
    system_description VARCHAR2(500),
    supplier_transaction_date DATE,
    supplier_reference_number VARCHAR2(100),
    supplier_amount NUMBER(15,2),
    supplier_description VARCHAR2(500),
    expected_amount NUMBER(15,2),
    actual_amount NUMBER(15,2),
    difference_amount NUMBER(15,2),
    difference_percentage NUMBER(5,2),
    difference_reason VARCHAR2(500),
    root_cause VARCHAR2(200),
    impact_level VARCHAR2(20) DEFAULT 'MEDIUM',
    adjustment_required CHAR(1) DEFAULT 'N',
    adjustment_amount NUMBER(15,2),
    adjustment_description VARCHAR2(500),
    resolved CHAR(1) DEFAULT 'N',
    resolution_method VARCHAR2(100),
    resolution_notes CLOB,
    identified_by NUMBER,
    identified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_by NUMBER,
    resolved_date TIMESTAMP,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 8. جدول تعديلات المطابقة
CREATE TABLE RECONCILIATION_ADJUSTMENTS (
    adjustment_id NUMBER PRIMARY KEY,
    reconciliation_id NUMBER NOT NULL,
    difference_id NUMBER,
    adjustment_type VARCHAR2(30) NOT NULL,
    adjustment_amount NUMBER(15,2) NOT NULL,
    adjustment_currency VARCHAR2(3) NOT NULL,
    adjustment_description VARCHAR2(500) NOT NULL,
    adjustment_reason VARCHAR2(500),
    related_transaction_id NUMBER,
    new_transaction_created CHAR(1) DEFAULT 'N',
    adjustment_status VARCHAR2(20) DEFAULT 'PENDING',
    approval_required CHAR(1) DEFAULT 'Y',
    requested_by NUMBER NOT NULL,
    requested_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    approved_by NUMBER,
    approved_date TIMESTAMP,
    applied_by NUMBER,
    applied_date TIMESTAMP,
    request_notes CLOB,
    approval_notes CLOB,
    rejection_reason VARCHAR2(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 9. جدول سجل أنشطة المطابقة
CREATE TABLE RECONCILIATION_ACTIVITY (
    log_id NUMBER PRIMARY KEY,
    cycle_id NUMBER,
    reconciliation_id NUMBER,
    activity_type VARCHAR2(50) NOT NULL,
    activity_description VARCHAR2(500) NOT NULL,
    old_value VARCHAR2(200),
    new_value VARCHAR2(200),
    affected_field VARCHAR2(100),
    performed_by NUMBER NOT NULL,
    performed_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR2(45),
    user_agent VARCHAR2(500),
    additional_data CLOB
);

-- 10. جدول تقييم أداء الموردين
CREATE TABLE SUPPLIER_PERFORMANCE (
    id NUMBER PRIMARY KEY,
    supplier_id NUMBER NOT NULL,
    evaluation_period_from DATE NOT NULL,
    evaluation_period_to DATE NOT NULL,
    quality_rating NUMBER(3,2),
    delivery_rating NUMBER(3,2),
    price_rating NUMBER(3,2),
    service_rating NUMBER(3,2),
    compliance_rating NUMBER(3,2),
    overall_rating NUMBER(3,2),
    performance_category VARCHAR2(20),
    total_orders NUMBER DEFAULT 0,
    on_time_deliveries NUMBER DEFAULT 0,
    quality_issues NUMBER DEFAULT 0,
    payment_delays NUMBER DEFAULT 0,
    on_time_delivery_rate NUMBER(5,2),
    quality_acceptance_rate NUMBER(5,2),
    payment_compliance_rate NUMBER(5,2),
    evaluation_notes CLOB,
    improvement_recommendations CLOB,
    evaluated_by NUMBER,
    evaluation_date DATE DEFAULT SYSDATE,
    approved_by NUMBER,
    approval_date DATE,
    evaluation_status VARCHAR2(20) DEFAULT 'DRAFT',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER
);

-- 11. جدول إشعارات التكامل
CREATE TABLE INTEGRATION_NOTIFICATIONS (
    id NUMBER PRIMARY KEY,
    notification_type VARCHAR2(50) NOT NULL,
    entity_type VARCHAR2(30) NOT NULL,
    entity_id NUMBER NOT NULL,
    title VARCHAR2(200) NOT NULL,
    message CLOB NOT NULL,
    priority VARCHAR2(20) DEFAULT 'MEDIUM',
    recipient_user_id NUMBER,
    recipient_role VARCHAR2(50),
    notification_status VARCHAR2(20) DEFAULT 'PENDING',
    sent_date TIMESTAMP,
    read_date TIMESTAMP,
    action_required CHAR(1) DEFAULT 'N',
    action_url VARCHAR2(500),
    expiry_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER
);

-- إنشاء Sequences
CREATE SEQUENCE PURCHASE_ORDER_PAYMENTS_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE PURCHASE_ORDER_STATUS_LOG_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE GOODS_RECEIPTS_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE GOODS_RECEIPT_ITEMS_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE RECONCILIATION_CYCLES_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SUPPLIER_RECONCILIATION_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE RECONCILIATION_DIFFERENCES_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE RECONCILIATION_ADJUSTMENTS_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE RECONCILIATION_ACTIVITY_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SUPPLIER_PERFORMANCE_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE INTEGRATION_NOTIFICATIONS_SEQ START WITH 1 INCREMENT BY 1;

-- رسالة نجاح
SELECT 'تم إنشاء جميع الجداول بنجاح!' as status FROM dual;

COMMIT;
