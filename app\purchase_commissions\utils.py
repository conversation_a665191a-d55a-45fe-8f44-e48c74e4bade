# -*- coding: utf-8 -*-
"""
دوال مساعدة لنظام عمولات مندوبي المشتريات
Utility Functions for Purchase Representatives Commission System
"""

import json
import logging
from datetime import datetime, date
from typing import Dict, List, Any, Optional, Union
from decimal import Decimal
from database_manager import DatabaseManager
from app.purchase_commissions.models import *

# إعداد نظام السجلات
logger = logging.getLogger(__name__)


class CommissionUtils:
    """فئة الدوال المساعدة للعمولات"""
    
    @staticmethod
    def format_currency(amount: Union[float, Decimal, None], currency: str = "SAR") -> str:
        """تنسيق المبلغ كعملة"""
        if amount is None:
            return "0.00"
        
        try:
            amount = float(amount)
            if currency == "SAR":
                return f"{amount:,.2f} ر.س"
            elif currency == "USD":
                return f"${amount:,.2f}"
            else:
                return f"{amount:,.2f} {currency}"
        except:
            return "0.00"
    
    @staticmethod
    def format_percentage(rate: Union[float, Decimal, None]) -> str:
        """تنسيق النسبة المئوية"""
        if rate is None:
            return "0%"
        
        try:
            rate = float(rate)
            return f"{rate:.2f}%"
        except:
            return "0%"
    
    @staticmethod
    def format_quantity(quantity: Union[float, Decimal, None], unit: str = "") -> str:
        """تنسيق الكمية مع الوحدة"""
        if quantity is None:
            return "0"
        
        try:
            quantity = float(quantity)
            if unit:
                return f"{quantity:,.2f} {unit}"
            else:
                return f"{quantity:,.2f}"
        except:
            return "0"
    
    @staticmethod
    def validate_commission_rule(rule_data: Dict) -> Dict[str, Any]:
        """التحقق من صحة بيانات قاعدة العمولة"""
        errors = []
        warnings = []
        
        # التحقق من الحقول المطلوبة
        required_fields = ['rule_name', 'commission_type_id']
        for field in required_fields:
            if not rule_data.get(field):
                errors.append(f"الحقل '{field}' مطلوب")
        
        # التحقق من نوع العمولة
        calculation_method = rule_data.get('calculation_method', '')
        
        if calculation_method == 'FIXED':
            if not rule_data.get('fixed_amount'):
                errors.append("المبلغ الثابت مطلوب للعمولة الثابتة")
        
        elif calculation_method == 'PERCENTAGE':
            if not rule_data.get('percentage_rate'):
                errors.append("النسبة المئوية مطلوبة للعمولة النسبية")
            elif float(rule_data.get('percentage_rate', 0)) > 100:
                warnings.append("النسبة المئوية أكبر من 100%")
        
        elif calculation_method in ['QUANTITY_FIXED', 'QUANTITY_TIERED']:
            if not rule_data.get('quantity_unit'):
                errors.append("وحدة القياس مطلوبة للعمولة حسب الكمية")
            if calculation_method == 'QUANTITY_FIXED' and not rule_data.get('quantity_rate'):
                errors.append("معدل الكمية مطلوب للعمولة الثابتة حسب الكمية")
        
        # التحقق من التواريخ
        effective_from = rule_data.get('effective_from')
        effective_to = rule_data.get('effective_to')
        
        if effective_from and effective_to:
            try:
                from_date = datetime.strptime(effective_from, '%Y-%m-%d') if isinstance(effective_from, str) else effective_from
                to_date = datetime.strptime(effective_to, '%Y-%m-%d') if isinstance(effective_to, str) else effective_to
                
                if from_date >= to_date:
                    errors.append("تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
            except:
                errors.append("تنسيق التاريخ غير صحيح")
        
        return {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    @staticmethod
    def parse_quantity_tiers(tiers_json: str) -> List[Dict]:
        """تحليل شرائح الكمية من JSON"""
        try:
            if not tiers_json:
                return []
            
            tiers = json.loads(tiers_json)
            
            # التحقق من صحة البيانات
            valid_tiers = []
            for tier in tiers:
                if all(key in tier for key in ['min_quantity', 'rate']):
                    valid_tiers.append({
                        'min_quantity': float(tier['min_quantity']),
                        'max_quantity': float(tier.get('max_quantity', float('inf'))),
                        'rate': float(tier['rate'])
                    })
            
            # ترتيب الشرائح حسب الحد الأدنى
            valid_tiers.sort(key=lambda x: x['min_quantity'])
            
            return valid_tiers
            
        except Exception as e:
            logger.error(f"خطأ في تحليل شرائح الكمية: {e}")
            return []
    
    @staticmethod
    def create_quantity_tiers_json(tiers: List[Dict]) -> str:
        """إنشاء JSON لشرائح الكمية"""
        try:
            return json.dumps(tiers, ensure_ascii=False)
        except Exception as e:
            logger.error(f"خطأ في إنشاء JSON لشرائح الكمية: {e}")
            return "[]"
    
    @staticmethod
    def get_commission_status_text(status: str) -> str:
        """الحصول على نص حالة العمولة"""
        status_map = {
            'CALCULATED': 'محسوبة',
            'APPROVED': 'معتمدة',
            'PAID': 'مدفوعة',
            'CANCELLED': 'ملغية'
        }
        return status_map.get(status, status)
    
    @staticmethod
    def get_payment_status_text(status: str) -> str:
        """الحصول على نص حالة الدفع"""
        status_map = {
            'PENDING': 'في الانتظار',
            'PROCESSING': 'قيد المعالجة',
            'COMPLETED': 'مكتملة',
            'FAILED': 'فشلت',
            'CANCELLED': 'ملغية'
        }
        return status_map.get(status, status)
    
    @staticmethod
    def get_calculation_method_text(method: str) -> str:
        """الحصول على نص طريقة الحساب"""
        method_map = {
            'FIXED': 'ثابتة',
            'PERCENTAGE': 'نسبية',
            'TIERED': 'متدرجة',
            'QUANTITY_FIXED': 'ثابتة حسب الكمية',
            'QUANTITY_TIERED': 'متدرجة حسب الكمية',
            'ITEM_BASED': 'حسب الصنف',
            'SUPPLIER_BASED': 'حسب المورد',
            'SEASONAL': 'موسمية'
        }
        return method_map.get(method, method)
    
    @staticmethod
    def calculate_monthly_target_progress(rep_id: int, db_manager: DatabaseManager) -> Dict:
        """حساب تقدم المندوب نحو الأهداف الشهرية"""
        try:
            # جلب أهداف المندوب
            target_query = """
                SELECT target_monthly_orders, target_monthly_quantity, target_monthly_value
                FROM purchase_representatives 
                WHERE id = :rep_id
            """
            target_result = db_manager.fetch_one(target_query, {'rep_id': rep_id})
            
            if not target_result:
                return {}
            
            target_orders, target_quantity, target_value = target_result
            
            # حساب الإنجاز الحالي هذا الشهر
            current_query = """
                SELECT 
                    COUNT(*) as current_orders,
                    SUM(total_quantity) as current_quantity,
                    SUM(order_value) as current_value
                FROM commission_calculations 
                WHERE rep_id = :rep_id
                AND EXTRACT(MONTH FROM calculation_date) = EXTRACT(MONTH FROM SYSDATE)
                AND EXTRACT(YEAR FROM calculation_date) = EXTRACT(YEAR FROM SYSDATE)
            """
            current_result = db_manager.fetch_one(current_query, {'rep_id': rep_id})
            
            if current_result:
                current_orders, current_quantity, current_value = current_result
                current_orders = current_orders or 0
                current_quantity = current_quantity or 0
                current_value = current_value or 0
            else:
                current_orders = current_quantity = current_value = 0
            
            # حساب النسب المئوية
            progress = {
                'orders': {
                    'current': current_orders,
                    'target': target_orders or 0,
                    'percentage': (current_orders / target_orders * 100) if target_orders else 0
                },
                'quantity': {
                    'current': current_quantity,
                    'target': target_quantity or 0,
                    'percentage': (current_quantity / target_quantity * 100) if target_quantity else 0
                },
                'value': {
                    'current': current_value,
                    'target': target_value or 0,
                    'percentage': (current_value / target_value * 100) if target_value else 0
                }
            }
            
            return progress
            
        except Exception as e:
            logger.error(f"خطأ في حساب تقدم الأهداف للمندوب {rep_id}: {e}")
            return {}
    
    @staticmethod
    def generate_commission_breakdown(calculation: CommissionCalculation) -> Dict:
        """إنشاء تفصيل حساب العمولة"""
        try:
            breakdown = {
                'calculation_id': calculation.id,
                'rep_id': calculation.rep_id,
                'purchase_order_id': calculation.purchase_order_id,
                'calculation_date': calculation.calculation_date.isoformat() if calculation.calculation_date else None,
                'method': calculation.calculation_method,
                'components': []
            }
            
            # إضافة مكونات العمولة
            if calculation.commission_amount:
                breakdown['components'].append({
                    'type': 'value_based',
                    'description': 'عمولة على القيمة',
                    'base_amount': calculation.order_value,
                    'rate': calculation.commission_rate,
                    'commission': calculation.commission_amount
                })
            
            if calculation.quantity_commission:
                breakdown['components'].append({
                    'type': 'quantity_based',
                    'description': 'عمولة على الكمية',
                    'quantity': calculation.total_quantity,
                    'unit': calculation.quantity_unit,
                    'rate': calculation.quantity_rate,
                    'commission': calculation.quantity_commission
                })
            
            # إجمالي العمولة
            breakdown['total_commission'] = calculation.combined_commission or calculation.commission_amount or 0
            
            return breakdown
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء تفصيل العمولة: {e}")
            return {}
    
    @staticmethod
    def is_date_in_range(check_date: datetime, start_date: Optional[datetime], end_date: Optional[datetime]) -> bool:
        """التحقق من وجود التاريخ ضمن النطاق المحدد"""
        if start_date and check_date < start_date:
            return False
        if end_date and check_date > end_date:
            return False
        return True
    
    @staticmethod
    def convert_to_datetime(date_input: Union[str, date, datetime, None]) -> Optional[datetime]:
        """تحويل المدخل إلى datetime"""
        if date_input is None:
            return None
        
        if isinstance(date_input, datetime):
            return date_input
        
        if isinstance(date_input, date):
            return datetime.combine(date_input, datetime.min.time())
        
        if isinstance(date_input, str):
            try:
                return datetime.strptime(date_input, '%Y-%m-%d')
            except:
                try:
                    return datetime.strptime(date_input, '%Y-%m-%d %H:%M:%S')
                except:
                    return None
        
        return None
