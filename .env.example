# إعدادات النظام المحاسبي المتقدم
# Advanced Accounting System Configuration

# بيئة التشغيل (development, testing, production)
FLASK_ENV=development
FLASK_CONFIG=development

# المفتاح السري للتطبيق
SECRET_KEY=مفتاح-سري-للنظام-المحاسبي-2024-غير-هذا-في-الإنتاج

# إعدادات قاعدة البيانات
# للتطوير - SQLite
DATABASE_URL=sqlite:///accounting_system.db

# للإنتاج - PostgreSQL
# DATABASE_URL=postgresql://username:password@localhost/accounting_db

# إعدادات البريد الإلكتروني
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# بريد المدير للإشعارات
ADMIN_EMAIL=<EMAIL>

# إعدادات الأمان
WTF_CSRF_ENABLED=true
WTF_CSRF_TIME_LIMIT=3600

# إعدادات الجلسة
PERMANENT_SESSION_LIFETIME=28800

# إعدادات الملفات
MAX_CONTENT_LENGTH=********
UPLOAD_FOLDER=uploads
REPORTS_FOLDER=reports
BACKUP_FOLDER=backups

# إعدادات اللغة والمنطقة
DEFAULT_LANGUAGE=ar
BABEL_DEFAULT_LOCALE=ar
BABEL_DEFAULT_TIMEZONE=Asia/Riyadh

# إعدادات العملة
DEFAULT_CURRENCY=SAR
CURRENCY_SYMBOL=ر.س

# إعدادات التطوير
DEBUG=true
TESTING=false

# إعدادات قاعدة البيانات للاختبار
TEST_DATABASE_URL=sqlite:///:memory:

# إعدادات Redis (للجلسات والتخزين المؤقت)
# REDIS_URL=redis://localhost:6379/0

# إعدادات Celery (للمهام الخلفية)
# CELERY_BROKER_URL=redis://localhost:6379/0
# CELERY_RESULT_BACKEND=redis://localhost:6379/0

# إعدادات التسجيل
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# إعدادات النسخ الاحتياطي
BACKUP_ENABLED=true
BACKUP_SCHEDULE=daily
BACKUP_RETENTION_DAYS=30

# إعدادات الإشعارات
NOTIFICATIONS_ENABLED=true
SMS_PROVIDER=
SMS_API_KEY=
SMS_SENDER_ID=

# إعدادات التكامل
# API Keys للخدمات الخارجية
EXTERNAL_API_KEY=
PAYMENT_GATEWAY_KEY=

# إعدادات الأداء
CACHE_TYPE=simple
CACHE_DEFAULT_TIMEOUT=300

# إعدادات الأمان المتقدمة
FORCE_HTTPS=false
SESSION_COOKIE_SECURE=false
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Lax
