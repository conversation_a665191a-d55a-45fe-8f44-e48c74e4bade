#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Oracle JDBC Connection Manager
مدير اتصال Oracle JDBC
"""

import os
import jpype
import jpype.imports
from jpype.types import *
import logging

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OracleJDBCManager:
    """مدير اتصال Oracle JDBC"""
    
    def __init__(self):
        self.connection = None
        self.is_jvm_started = False
        
    def start_jvm(self):
        """بدء JVM"""
        if jpype.isJVMStarted():
            logger.info("✅ JVM already started")
            self.is_jvm_started = True
            return True
            
        try:
            # البحث عن Oracle JDBC Driver
            oracle_jdbc_paths = [
                r'.\drivers\ojdbc11.jar',  # المسار الجديد المحلي
                r'E:\oracle\product\11.2.4\dbhome_1\jdbc\lib\ojdbc6.jar',
                r'E:\oracle\product\11.2.0\dbhome_1\jdbc\lib\ojdbc6.jar',
                r'C:\app\oracle\product\21.0.0\dbhome_1\jdbc\lib\ojdbc11.jar',
                r'C:\app\oracle\product\19.0.0\dbhome_1\jdbc\lib\ojdbc8.jar',
                r'C:\oracle\instantclient_21_3\ojdbc11.jar',
                r'C:\oracle\instantclient_19_3\ojdbc8.jar',
                r'.\ojdbc6.jar',
                r'.\ojdbc11.jar',
                r'.\ojdbc8.jar'
            ]
            
            jdbc_jar = None
            for path in oracle_jdbc_paths:
                if os.path.exists(path):
                    jdbc_jar = path
                    logger.info(f"✅ Found Oracle JDBC: {path}")
                    break
            
            if not jdbc_jar:
                logger.error("❌ Oracle JDBC Driver not found")
                logger.info("💡 Download ojdbc11.jar from Oracle website")
                return False
            
            # بدء JVM مع JDBC Driver
            jpype.startJVM(jpype.getDefaultJVMPath(), f"-Djava.class.path={jdbc_jar}")
            self.is_jvm_started = True

            # استيراد Java classes بعد بدء JVM
            import java.sql

            logger.info("✅ JVM started successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start JVM: {e}")
            return False
    
    def connect(self, host='localhost', port=1521, sid='ORCL', username='accounting_user', password='accounting_password'):
        """الاتصال بقاعدة البيانات"""
        if not self.start_jvm():
            return False
            
        try:
            # استيراد Java classes
            from java.sql import DriverManager
            from oracle.jdbc import OracleDriver
            
            # تسجيل Oracle Driver
            DriverManager.registerDriver(OracleDriver())
            
            # بناء connection string
            url = f"jdbc:oracle:thin:@{host}:{port}:{sid}"
            logger.info(f"🔗 Connecting to: {url}")
            
            # الاتصال
            self.connection = DriverManager.getConnection(url, username, password)
            logger.info("✅ Connected to Oracle Database")
            return True
            
        except Exception as e:
            logger.error(f"❌ Connection failed: {e}")
            return False
    
    def execute_query(self, sql, params=None):
        """تنفيذ استعلام"""
        if not self.connection:
            logger.error("❌ No database connection")
            return None

        try:
            logger.info(f"🔍 Executing query: {sql}")
            logger.info(f"🔍 Parameters: {params}")

            statement = None
            prepared_statement = None

            if params:
                # استخدام PreparedStatement مع المعاملات
                prepared_statement = self.connection.prepareStatement(sql)

                # تعيين المعاملات
                for i, param in enumerate(params):
                    logger.info(f"🔍 Setting param {i+1}: {param} (type: {type(param)})")
                    if param is None:
                        prepared_statement.setNull(i + 1, java.sql.Types.VARCHAR)
                    elif isinstance(param, str):
                        prepared_statement.setString(i + 1, param)
                    elif isinstance(param, int):
                        prepared_statement.setInt(i + 1, param)
                    elif isinstance(param, float):
                        prepared_statement.setDouble(i + 1, param)
                    else:
                        prepared_statement.setString(i + 1, str(param))

                result_set = prepared_statement.executeQuery()
            else:
                # استخدام Statement عادي بدون معاملات
                statement = self.connection.createStatement()
                result_set = statement.executeQuery(sql)

            # استخراج النتائج
            results = []
            meta_data = result_set.getMetaData()
            column_count = meta_data.getColumnCount()
            logger.info(f"🔍 Column count: {column_count}")

            row_count = 0
            while result_set.next():
                row = []
                for i in range(1, column_count + 1):
                    value = result_set.getString(i)
                    row.append(value)
                results.append(row)
                row_count += 1

            logger.info(f"🔍 Retrieved {row_count} rows")

            # إغلاق الموارد
            result_set.close()
            if prepared_statement:
                prepared_statement.close()
            if statement:
                statement.close()

            return results

        except Exception as e:
            logger.error(f"❌ Query failed: {e}")
            import traceback
            logger.error(f"❌ Traceback: {traceback.format_exc()}")
            return None
    
    def execute_update(self, sql, params=None):
        """تنفيذ تحديث مع دعم المعاملات"""
        if not self.connection:
            logger.error("❌ No database connection")
            return False

        try:
            if params:
                # استخدام PreparedStatement مع المعاملات
                prepared_statement = self.connection.prepareStatement(sql)

                # تعيين المعاملات
                for i, param in enumerate(params):
                    if param is None:
                        prepared_statement.setNull(i + 1, java.sql.Types.VARCHAR)
                    elif isinstance(param, str):
                        prepared_statement.setString(i + 1, param)
                    elif isinstance(param, int):
                        prepared_statement.setInt(i + 1, param)
                    elif isinstance(param, float):
                        prepared_statement.setDouble(i + 1, param)
                    else:
                        prepared_statement.setString(i + 1, str(param))

                rows_affected = prepared_statement.executeUpdate()
                prepared_statement.close()
            else:
                # استخدام Statement عادي بدون معاملات
                statement = self.connection.createStatement()
                rows_affected = statement.executeUpdate(sql)
                statement.close()

            logger.info(f"✅ Updated {rows_affected} rows")
            return True

        except Exception as e:
            logger.error(f"❌ Update failed: {e}")
            return False
    
    def commit(self):
        """حفظ التغييرات"""
        if self.connection:
            try:
                self.connection.commit()
                logger.info("✅ Transaction committed")
                return True
            except Exception as e:
                logger.error(f"❌ Commit failed: {e}")
                return False
        return False
    
    def rollback(self):
        """التراجع عن التغييرات"""
        if self.connection:
            try:
                self.connection.rollback()
                logger.info("✅ Transaction rolled back")
                return True
            except Exception as e:
                logger.error(f"❌ Rollback failed: {e}")
                return False
        return False
    
    def close(self):
        """إغلاق الاتصال"""
        if self.connection:
            try:
                self.connection.close()
                logger.info("✅ Connection closed")
            except Exception as e:
                logger.error(f"❌ Close failed: {e}")
        
        if self.is_jvm_started and jpype.isJVMStarted():
            try:
                jpype.shutdownJVM()
                logger.info("✅ JVM shutdown")
            except Exception as e:
                logger.error(f"❌ JVM shutdown failed: {e}")

def test_oracle_jdbc():
    """اختبار Oracle JDBC"""
    print("🚀 اختبار Oracle JDBC Connection")
    print("=" * 40)
    
    oracle = OracleJDBCManager()
    
    # الاتصال
    if oracle.connect():
        print("✅ الاتصال نجح!")
        
        # اختبار استعلام
        print("\n🔍 اختبار الاستعلام...")
        result = oracle.execute_query("SELECT 1 FROM DUAL")
        if result:
            print(f"✅ النتيجة: {result[0][0]}")
        
        # التحقق من الجداول
        print("\n📊 التحقق من الجداول...")
        tables = oracle.execute_query("SELECT table_name FROM user_tables ORDER BY table_name")
        if tables:
            print(f"✅ عدد الجداول: {len(tables)}")
            for table in tables[:5]:  # أول 5 جداول
                print(f"  - {table[0]}")
        else:
            print("❌ لا توجد جداول")
        
        oracle.close()
        return True
    else:
        print("❌ فشل الاتصال")
        return False

def create_oracle_tables_jdbc():
    """إنشاء الجداول باستخدام JDBC"""
    print("🔧 إنشاء جداول Oracle باستخدام JDBC")
    print("=" * 45)
    
    oracle = OracleJDBCManager()
    
    if not oracle.connect():
        print("❌ فشل الاتصال")
        return False
    
    # جداول للإنشاء
    tables_sql = [
        # جدول المستخدمين
        """
        CREATE TABLE users (
            id NUMBER(10) GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            username VARCHAR2(80) UNIQUE NOT NULL,
            email VARCHAR2(120) UNIQUE NOT NULL,
            password_hash VARCHAR2(255) NOT NULL,
            full_name VARCHAR2(200) NOT NULL,
            department VARCHAR2(100),
            position VARCHAR2(100),
            phone VARCHAR2(20),
            is_active NUMBER(1) DEFAULT 1,
            is_admin NUMBER(1) DEFAULT 0,
            created_at DATE DEFAULT SYSDATE,
            last_login DATE
        )
        """,
        
        # جدول طلبات الشراء
        """
        CREATE TABLE purchase_requests (
            id NUMBER(10) GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            req_no VARCHAR2(50) UNIQUE NOT NULL,
            req_serial NUMBER(10) NOT NULL,
            requester_name VARCHAR2(100) NOT NULL,
            department_name VARCHAR2(100) NOT NULL,
            req_type VARCHAR2(50) DEFAULT 'عادي',
            req_priority VARCHAR2(20) DEFAULT 'عادي',
            req_date DATE DEFAULT SYSDATE,
            needed_date DATE NOT NULL,
            req_status VARCHAR2(50) DEFAULT 'مسودة',
            currency VARCHAR2(10) DEFAULT 'ريال',
            total_amount NUMBER(15,2) DEFAULT 0,
            approval_status VARCHAR2(50) DEFAULT 'في الانتظار',
            created_by NUMBER(10),
            created_at DATE DEFAULT SYSDATE,
            updated_at DATE DEFAULT SYSDATE
        )
        """
    ]
    
    table_names = ['users', 'purchase_requests']
    
    try:
        for i, sql in enumerate(tables_sql):
            table_name = table_names[i]
            
            # التحقق من وجود الجدول
            check_result = oracle.execute_query(f"SELECT COUNT(*) FROM user_tables WHERE table_name = UPPER('{table_name}')")
            
            if check_result and int(check_result[0][0]) == 0:
                # إنشاء الجدول
                if oracle.execute_update(sql):
                    print(f"✅ تم إنشاء جدول {table_name}")
                else:
                    print(f"❌ فشل إنشاء جدول {table_name}")
            else:
                print(f"✅ جدول {table_name} موجود")
        
        # إنشاء مستخدم admin
        admin_check = oracle.execute_query("SELECT COUNT(*) FROM users WHERE username = 'admin'")
        if admin_check and int(admin_check[0][0]) == 0:
            admin_sql = """
            INSERT INTO users (username, email, password_hash, full_name, is_admin, is_active)
            VALUES ('admin', '<EMAIL>', 'pbkdf2:sha256:600000$salt$hash', 'مدير النظام', 1, 1)
            """
            if oracle.execute_update(admin_sql):
                print("✅ تم إنشاء مستخدم admin")
        else:
            print("✅ مستخدم admin موجود")
        
        # حفظ التغييرات
        oracle.commit()
        print("✅ تم حفظ جميع التغييرات")
        
        oracle.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        oracle.rollback()
        oracle.close()
        return False

if __name__ == '__main__':
    print("🚀 Oracle JDBC Manager للنظام المحاسبي")
    print("=" * 50)
    
    # اختبار الاتصال
    if test_oracle_jdbc():
        print("\n🎉 الاتصال يعمل!")
        
        # إنشاء الجداول
        if create_oracle_tables_jdbc():
            print("\n🎉 تم إعداد قاعدة البيانات بنجاح!")
            print("\n🚀 يمكنك الآن تشغيل التطبيق")
        else:
            print("\n❌ فشل في إنشاء الجداول")
    else:
        print("\n❌ فشل الاتصال")
