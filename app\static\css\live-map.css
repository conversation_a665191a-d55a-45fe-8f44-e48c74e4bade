/* خريطة التتبع المباشر */
.live-map-container {
    height: 400px;
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

#liveMap {
    height: 100%;
    width: 100%;
    z-index: 1;
}

/* أيقونات الشحنات على الخريطة */
.shipment-marker {
    background: #007bff;
    border: 3px solid #fff;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.shipment-marker.in-transit {
    background: #28a745;
    animation: pulse 2s infinite;
}

.shipment-marker.delayed {
    background: #ffc107;
}

.shipment-marker.arrived {
    background: #6c757d;
}

.shipment-marker.problem {
    background: #dc3545;
    animation: blink 1s infinite;
}

/* تأثيرات الحركة */
@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* نافذة معلومات الشحنة */
.shipment-popup {
    min-width: 250px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.shipment-popup .popup-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 10px;
    margin: -10px -10px 10px -10px;
    border-radius: 4px 4px 0 0;
}

.shipment-popup .popup-content {
    padding: 5px 0;
}

.shipment-popup .status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: bold;
    text-transform: uppercase;
}

.shipment-popup .status-in-transit {
    background: #d4edda;
    color: #155724;
}

.shipment-popup .status-delayed {
    background: #fff3cd;
    color: #856404;
}

.shipment-popup .status-arrived {
    background: #d1ecf1;
    color: #0c5460;
}

.shipment-popup .status-problem {
    background: #f8d7da;
    color: #721c24;
}

/* أدوات التحكم في الخريطة */
.map-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.map-control-btn {
    background: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 8px;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s;
}

.map-control-btn:hover {
    background: #f8f9fa;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.map-control-btn.active {
    background: #007bff;
    color: white;
}

/* مؤشر التحديث المباشر */
.live-indicator {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.9);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.live-indicator.active {
    background: rgba(40, 167, 69, 0.9);
    color: white;
}

.live-indicator .pulse-dot {
    width: 8px;
    height: 8px;
    background: #28a745;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

/* إحصائيات الخريطة */
.map-stats {
    position: absolute;
    bottom: 10px;
    left: 10px;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    font-size: 0.85rem;
}

.map-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 3px;
}

.map-stats .stat-item:last-child {
    margin-bottom: 0;
}

.map-stats .stat-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .live-map-container {
        height: 300px;
    }
    
    .map-controls {
        top: 5px;
        right: 5px;
    }
    
    .map-control-btn {
        padding: 6px;
        font-size: 0.9rem;
    }
    
    .map-stats {
        bottom: 5px;
        left: 5px;
        padding: 8px;
        font-size: 0.8rem;
    }
    
    .live-indicator {
        top: 5px;
        left: 5px;
        padding: 4px 8px;
        font-size: 0.75rem;
    }
}

/* تحميل الخريطة */
.map-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f8f9fa;
    color: #6c757d;
}

.map-loading .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e9ecef;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* خطوط المسار */
.route-line {
    stroke: #007bff;
    stroke-width: 3;
    stroke-opacity: 0.7;
    fill: none;
    stroke-dasharray: 5, 5;
    animation: dash 20s linear infinite;
}

@keyframes dash {
    to {
        stroke-dashoffset: -100;
    }
}

/* نقاط الموانئ */
.port-marker {
    background: #17a2b8;
    border: 3px solid #fff;
    border-radius: 4px;
    width: 16px;
    height: 16px;
    transform: rotate(45deg);
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.port-marker::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    background: #fff;
    border-radius: 1px;
}
