{% extends "base.html" %}

{% block content %}
<style>
.driver-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.driver-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.driver-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.status-online {
    background: #28a745;
    animation: pulse 2s infinite;
}

.status-offline {
    background: #6c757d;
}

.status-busy {
    background: #ffc107;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

.live-tracking {
    background: #e3f2fd;
    border-radius: 10px;
    padding: 1rem;
}
</style>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-users text-primary me-2"></i>
                        إدارة السائقين
                    </h1>
                    <p class="text-muted mb-0">إدارة السائقين والتتبع المباشر</p>
                </div>
                <div>
                    <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addDriverModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة سائق جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient rounded-circle p-3">
                                <i class="fas fa-users text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">إجمالي السائقين</h6>
                            <h3 class="mb-0">{{ stats.total_drivers or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient rounded-circle p-3">
                                <i class="fas fa-circle text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">متاحين</h6>
                            <h3 class="mb-0">{{ stats.available_drivers or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient rounded-circle p-3">
                                <i class="fas fa-truck text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">في مهمة</h6>
                            <h3 class="mb-0">{{ stats.busy_drivers or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-danger bg-gradient rounded-circle p-3">
                                <i class="fas fa-times text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">غير متاحين</h6>
                            <h3 class="mb-0">{{ stats.offline_drivers or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Drivers List -->
    <div class="row">
        {% if drivers %}
            {% for driver in drivers %}
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="card driver-card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-start mb-3">
                            <div class="driver-avatar me-3">
                                {{ driver.name.split()[0][0] if driver.name else 'D' }}
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-1">{{ driver.name }}</h5>
                                <p class="text-muted mb-2">
                                    <span class="status-indicator status-{{ 'online' if driver.is_available else 'offline' }}"></span>
                                    {{ 'متاح' if driver.is_available else 'غير متاح' }}
                                </p>
                                <small class="text-muted">
                                    <i class="fas fa-id-card me-1"></i>
                                    {{ driver.license_number or 'غير محدد' }}
                                </small>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editDriver({{ driver.id }})">
                                        <i class="fas fa-edit me-2"></i>تعديل
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="trackDriver({{ driver.id }})">
                                        <i class="fas fa-map-marker-alt me-2"></i>تتبع
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="assignShipment({{ driver.id }})">
                                        <i class="fas fa-plus me-2"></i>تعيين شحنة
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteDriver({{ driver.id }})">
                                        <i class="fas fa-trash me-2"></i>حذف
                                    </a></li>
                                </ul>
                            </div>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted d-block">
                                <i class="fas fa-phone me-1"></i>
                                {{ driver.phone }}
                            </small>
                            {% if driver.email %}
                            <small class="text-muted d-block">
                                <i class="fas fa-envelope me-1"></i>
                                {{ driver.email }}
                            </small>
                            {% endif %}
                            {% if driver.current_location %}
                            <small class="text-muted d-block">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ driver.current_location }}
                            </small>
                            {% endif %}
                        </div>

                        {% if driver.current_shipment %}
                        <div class="live-tracking mb-3">
                            <h6 class="mb-2">
                                <i class="fas fa-shipping-fast me-1"></i>
                                الشحنة الحالية
                            </h6>
                            <p class="mb-1"><strong>{{ driver.current_shipment.tracking_number }}</strong></p>
                            <small class="text-muted">إلى: {{ driver.current_shipment.recipient_city }}</small>
                        </div>
                        {% endif %}

                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="mb-0">{{ driver.total_deliveries or 0 }}</h6>
                                    <small class="text-muted">تسليم</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="mb-0">{{ "%.1f"|format(driver.rating or 0) }}</h6>
                                    <small class="text-muted">تقييم</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <h6 class="mb-0">{{ "%.1f"|format(driver.avg_time or 0) }}ساعة</h6>
                                <small class="text-muted">متوسط</small>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary btn-sm flex-fill" onclick="viewProfile({{ driver.id }})">
                                <i class="fas fa-user me-1"></i>
                                الملف الشخصي
                            </button>
                            <button class="btn btn-outline-success btn-sm flex-fill" onclick="callDriver('{{ driver.phone }}')">
                                <i class="fas fa-phone me-1"></i>
                                اتصال
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-users text-muted mb-3" style="font-size: 4rem;"></i>
                    <h3 class="text-muted">لا يوجد سائقين</h3>
                    <p class="text-muted">لم يتم إضافة أي سائقين بعد</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDriverModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة سائق جديد
                    </button>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- Add Driver Modal -->
<div class="modal fade" id="addDriverModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة سائق جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addDriverForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم السائق *</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الهاتف *</label>
                            <input type="tel" class="form-control" name="phone" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الرخصة</label>
                            <input type="text" class="form-control" name="license_number">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ انتهاء الرخصة</label>
                            <input type="date" class="form-control" name="license_expiry">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">شركة الشحن</label>
                            <select class="form-select" name="carrier_id">
                                <option value="">اختر شركة الشحن</option>
                                {% for carrier in carriers %}
                                <option value="{{ carrier.id }}">{{ carrier.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-12">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                                <label class="form-check-label" for="is_active">سائق نشط</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" name="is_available" id="is_available" checked>
                                <label class="form-check-label" for="is_available">متاح للعمل</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// إضافة سائق جديد
document.getElementById('addDriverForm').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('إضافة سائق جديد - قريباً');
});

// عرض الملف الشخصي
function viewProfile(driverId) {
    alert(`عرض ملف السائق ${driverId} - قريباً`);
}

// تتبع السائق
function trackDriver(driverId) {
    alert(`تتبع السائق ${driverId} - قريباً`);
}

// تعديل السائق
function editDriver(driverId) {
    alert(`تعديل السائق ${driverId} - قريباً`);
}

// تعيين شحنة
function assignShipment(driverId) {
    alert(`تعيين شحنة للسائق ${driverId} - قريباً`);
}

// اتصال بالسائق
function callDriver(phone) {
    window.open(`tel:${phone}`);
}

// حذف السائق
function deleteDriver(driverId) {
    if (confirm('هل أنت متأكد من حذف هذا السائق؟')) {
        alert(`حذف السائق ${driverId} - قريباً`);
    }
}
</script>

{% endblock %}
