#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
عرض توضيحي للنظام المحاسبي الموحد
Unified Accounting System Demo
"""

import sys
import os
sys.path.insert(0, os.getcwd())

from oracle_manager import OracleManager

def demo_unified_accounting_system():
    """عرض توضيحي للنظام المحاسبي الموحد"""
    
    oracle = OracleManager()
    
    print("🎯 عرض توضيحي للنظام المحاسبي الموحد")
    print("=" * 80)
    
    # 1. إنشاء الأرصدة الافتتاحية
    print("\n1️⃣ إنشاء الأرصدة الافتتاحية...")
    
    # رصيد افتتاحي لمورد
    try:
        opening_balance_query = """
        BEGIN
            OPENING_BALANCES_PKG.INSERT_OPENING_BALANCE(
                p_entity_type_code => 'SUPPLIER',
                p_entity_id => 1,
                p_currency_code => 'USD',
                p_balance_amount => 10000,
                p_branch_id => 1,
                p_year_number => 2025,
                p_user_id => 1
            );
        END;
        """
        
        oracle.execute_update(opening_balance_query)
        print("✅ تم إنشاء رصيد افتتاحي للمورد 1: 10,000 USD")
        
    except Exception as e:
        print(f"⚠️ رصيد المورد موجود مسبقاً: {str(e)}")
    
    # رصيد افتتاحي لصراف
    try:
        opening_balance_mc_query = """
        BEGIN
            OPENING_BALANCES_PKG.INSERT_OPENING_BALANCE(
                p_entity_type_code => 'MONEY_CHANGER',
                p_entity_id => 2,
                p_currency_code => 'USD',
                p_balance_amount => 50000,
                p_branch_id => 1,
                p_year_number => 2025,
                p_user_id => 1
            );
        END;
        """
        
        oracle.execute_update(opening_balance_mc_query)
        print("✅ تم إنشاء رصيد افتتاحي للصراف 2: 50,000 USD")
        
    except Exception as e:
        print(f"⚠️ رصيد الصراف موجود مسبقاً: {str(e)}")
    
    # 2. ترحيل معاملات جديدة
    print("\n2️⃣ ترحيل معاملات جديدة...")
    
    # معاملة حوالة للمورد
    try:
        transfer_query = """
        BEGIN
            BALANCE_TRANSACTIONS_PKG.POST_TRANSACTION(
                p_entity_type_code => 'SUPPLIER',
                p_entity_id => 1,
                p_document_type_code => 'TRANSFER',
                p_document_number => 'TRF-DEMO-001',
                p_document_date => SYSDATE,
                p_currency_code => 'USD',
                p_debit_amount => 5000,
                p_credit_amount => 0,
                p_exchange_rate => 1,
                p_description => 'Demo transfer to supplier',
                p_branch_id => 1,
                p_user_id => 1
            );
        END;
        """
        
        oracle.execute_update(transfer_query)
        print("✅ تم ترحيل حوالة للمورد 1: +5,000 USD")
        
    except Exception as e:
        print(f"❌ خطأ في ترحيل الحوالة: {str(e)}")
    
    # معاملة خصم من الصراف
    try:
        debit_mc_query = """
        BEGIN
            BALANCE_TRANSACTIONS_PKG.POST_TRANSACTION(
                p_entity_type_code => 'MONEY_CHANGER',
                p_entity_id => 2,
                p_document_type_code => 'TRANSFER',
                p_document_number => 'TRF-DEMO-001',
                p_document_date => SYSDATE,
                p_currency_code => 'USD',
                p_debit_amount => 0,
                p_credit_amount => 5000,
                p_exchange_rate => 1,
                p_description => 'Demo transfer from money changer',
                p_branch_id => 1,
                p_user_id => 1
            );
        END;
        """
        
        oracle.execute_update(debit_mc_query)
        print("✅ تم خصم من الصراف 2: -5,000 USD")
        
    except Exception as e:
        print(f"❌ خطأ في خصم الصراف: {str(e)}")
    
    # 3. عرض الأرصدة الحالية
    print("\n3️⃣ عرض الأرصدة الحالية...")
    
    # رصيد المورد
    supplier_balance_query = """
    SELECT BALANCE_TRANSACTIONS_PKG.GET_CURRENT_BALANCE(
        'SUPPLIER', 1, 'USD', 1, SYSDATE
    ) FROM DUAL
    """
    
    supplier_balance = oracle.execute_query(supplier_balance_query)
    if supplier_balance:
        print(f"💰 رصيد المورد 1: {supplier_balance[0][0]:,.2f} USD")
    
    # رصيد الصراف
    mc_balance_query = """
    SELECT BALANCE_TRANSACTIONS_PKG.GET_CURRENT_BALANCE(
        'MONEY_CHANGER', 2, 'USD', 1, SYSDATE
    ) FROM DUAL
    """
    
    mc_balance = oracle.execute_query(mc_balance_query)
    if mc_balance:
        print(f"💰 رصيد الصراف 2: {mc_balance[0][0]:,.2f} USD")
    
    # 4. عرض تاريخ المعاملات
    print("\n4️⃣ عرض تاريخ المعاملات...")
    
    # تاريخ معاملات المورد
    print("\n📋 تاريخ معاملات المورد 1:")
    supplier_history_query = """
    SELECT document_date, document_type_code, document_number,
           debit_amount, credit_amount, balance, description
    FROM BALANCE_TRANSACTIONS
    WHERE entity_type_code = 'SUPPLIER'
    AND entity_id = 1
    AND currency_code = 'USD'
    ORDER BY document_date, created_date
    """
    
    supplier_history = oracle.execute_query(supplier_history_query)
    if supplier_history:
        for row in supplier_history:
            date, doc_type, doc_num, debit, credit, balance, desc = row
            amount_str = f"+{debit:,.2f}" if debit > 0 else f"-{credit:,.2f}"
            print(f"   {date.strftime('%Y-%m-%d')}: {doc_type} {doc_num} = {amount_str} USD")
            print(f"     الوصف: {desc}")
    
    # 5. اختبار عكس المعاملة
    print("\n5️⃣ اختبار عكس المعاملة...")
    
    try:
        reverse_query = """
        BEGIN
            BALANCE_TRANSACTIONS_PKG.REVERSE_TRANSACTION(
                p_original_document_number => 'TRF-DEMO-001',
                p_reversal_document_number => 'REV-TRF-DEMO-001',
                p_reversal_reason => 'Demo reversal test',
                p_user_id => 1
            );
        END;
        """
        
        oracle.execute_update(reverse_query)
        print("✅ تم عكس المعاملة TRF-DEMO-001")
        
        # عرض الأرصدة بعد العكس
        supplier_balance_after = oracle.execute_query(supplier_balance_query)
        mc_balance_after = oracle.execute_query(mc_balance_query)
        
        if supplier_balance_after and mc_balance_after:
            print(f"💰 رصيد المورد 1 بعد العكس: {supplier_balance_after[0][0]:,.2f} USD")
            print(f"💰 رصيد الصراف 2 بعد العكس: {mc_balance_after[0][0]:,.2f} USD")
        
    except Exception as e:
        print(f"❌ خطأ في عكس المعاملة: {str(e)}")
    
    # 6. عرض إحصائيات النظام
    print("\n6️⃣ إحصائيات النظام...")
    
    stats_query = """
    SELECT 
        COUNT(*) as total_transactions,
        COUNT(DISTINCT entity_type_code) as entity_types,
        COUNT(DISTINCT document_type_code) as document_types,
        SUM(CASE WHEN balance > 0 THEN balance ELSE 0 END) as total_debits,
        SUM(CASE WHEN balance < 0 THEN ABS(balance) ELSE 0 END) as total_credits
    FROM BALANCE_TRANSACTIONS
    WHERE status = 'POSTED'
    """
    
    stats = oracle.execute_query(stats_query)
    if stats:
        total_trans, entity_types, doc_types, total_debits, total_credits = stats[0]
        print(f"📊 إجمالي المعاملات: {total_trans}")
        print(f"📊 أنواع الكيانات: {entity_types}")
        print(f"📊 أنواع المستندات: {doc_types}")
        print(f"📊 إجمالي المدين: {total_debits:,.2f}")
        print(f"📊 إجمالي الدائن: {total_credits:,.2f}")
        print(f"📊 الفرق: {(total_debits - total_credits):,.2f}")

def show_system_advantages():
    """عرض مزايا النظام الموحد"""
    
    print("\n🌟 مزايا النظام المحاسبي الموحد:")
    print("=" * 80)
    
    advantages = [
        "🎯 مصدر واحد للحقيقة - لا تضارب في البيانات",
        "📊 تتبع تاريخي كامل لكل معاملة",
        "🌍 دعم متعدد العملات مع أسعار الصرف",
        "🏢 دعم الفروع المتعددة",
        "📅 تقارير شهرية وسنوية محسنة",
        "🔄 إمكانية عكس المعاملات بسهولة",
        "⚡ استعلامات مرنة ومتقدمة",
        "🛡️ أمان وتتبع كامل للتغييرات",
        "📈 قابلية التوسع لكيانات جديدة",
        "🔧 صيانة مبسطة ونظام موحد"
    ]
    
    for advantage in advantages:
        print(f"   {advantage}")

if __name__ == "__main__":
    try:
        demo_unified_accounting_system()
        show_system_advantages()
        
        print("\n🎉 انتهى العرض التوضيحي للنظام المحاسبي الموحد!")
        print("✅ النظام جاهز للتطبيق والتطوير")
        
    except Exception as e:
        print(f"❌ خطأ في العرض التوضيحي: {str(e)}")
    
    print("\n" + "=" * 80)
    print("🏁 انتهى العرض")
