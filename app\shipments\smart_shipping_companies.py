#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النظام الذكي لشركات الشحن العالمية
Smart Global Shipping Companies System with AI
"""

import re
import json
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from urllib.parse import urljoin, urlparse
import logging

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ShippingCompany:
    """فئة شركة الشحن"""
    id: str
    name: str
    name_ar: str
    code: str
    website: str
    tracking_url: str
    api_endpoint: Optional[str]
    supported_formats: List[str]
    regions: List[str]
    container_types: List[str]
    is_active: bool = True
    ai_confidence: float = 0.0

@dataclass
class TrackingResult:
    """نتيجة التتبع"""
    container_number: str
    company: ShippingCompany
    status: str
    location: str
    estimated_arrival: Optional[datetime]
    last_update: datetime
    events: List[Dict]
    confidence: float

class SmartShippingCompaniesEngine:
    """محرك النظام الذكي لشركات الشحن"""
    
    def __init__(self):
        """تهيئة المحرك"""
        self.companies = self._load_shipping_companies()
        self.ai_patterns = self._load_ai_patterns()
        
    def _load_shipping_companies(self) -> List[ShippingCompany]:
        """تحميل قاعدة بيانات شركات الشحن العالمية"""
        companies_data = [
            # شركات الشحن الرئيسية
            {
                "id": "MAEU", "name": "Maersk Line", "name_ar": "خط مايرسك",
                "code": "MAEU", "website": "https://www.maersk.com",
                "tracking_url": "https://www.maersk.com/tracking/{container}",
                "api_endpoint": "https://api.maersk.com/track",
                "supported_formats": ["MAEU", "MSKU", "MSCU"],
                "regions": ["Global"], "container_types": ["20GP", "40GP", "40HC", "45HC"]
            },
            {
                "id": "MSCU", "name": "MSC Mediterranean Shipping Company", "name_ar": "شركة الشحن المتوسطية",
                "code": "MSCU", "website": "https://www.msc.com",
                "tracking_url": "https://www.msc.com/track-a-shipment?agencyPath=msc&trackingNumber={container}",
                "api_endpoint": "https://api.msc.com/tracking",
                "supported_formats": ["MSCU", "MEDU"],
                "regions": ["Global"], "container_types": ["20GP", "40GP", "40HC", "45HC"]
            },
            {
                "id": "CMAU", "name": "CMA CGM", "name_ar": "سي إم إيه سي جي إم",
                "code": "CMAU", "website": "https://www.cma-cgm.com",
                "tracking_url": "https://www.cma-cgm.com/ebusiness/tracking/{container}",
                "api_endpoint": "https://api.cma-cgm.com/track",
                "supported_formats": ["CMAU", "CGMU"],
                "regions": ["Global"], "container_types": ["20GP", "40GP", "40HC", "45HC"]
            },
            {
                "id": "COSCO", "name": "COSCO Shipping", "name_ar": "كوسكو للشحن",
                "code": "COSU", "website": "https://www.cosco-shipping.com",
                "tracking_url": "https://elines.coscoshipping.com/ebusiness/cargoTracking/{container}",
                "api_endpoint": "https://api.cosco.com/tracking",
                "supported_formats": ["COSU", "CCOS"],
                "regions": ["Asia", "Global"], "container_types": ["20GP", "40GP", "40HC", "45HC"]
            },
            {
                "id": "HAPAG", "name": "Hapag-Lloyd", "name_ar": "هاباغ لويد",
                "code": "HLCU", "website": "https://www.hapag-lloyd.com",
                "tracking_url": "https://www.hapag-lloyd.com/en/online-business/track/track-by-container-solution.html?container={container}",
                "api_endpoint": "https://api.hapag-lloyd.com/tracking",
                "supported_formats": ["HLCU", "HLBU"],
                "regions": ["Global"], "container_types": ["20GP", "40GP", "40HC", "45HC"]
            },
            {
                "id": "EVERGREEN", "name": "Evergreen Marine", "name_ar": "إيفرجرين مارين",
                "code": "EGLV", "website": "https://www.evergreen-marine.com",
                "tracking_url": "https://www.shipmentlink.com/servlet/TDB1_CargoTracking.do?container={container}",
                "api_endpoint": "https://api.evergreen.com/tracking",
                "supported_formats": ["EGLV", "EGHU"],
                "regions": ["Asia", "Global"], "container_types": ["20GP", "40GP", "40HC", "45HC"]
            },
            {
                "id": "OOCL", "name": "Orient Overseas Container Line", "name_ar": "خط الحاويات الشرقي",
                "code": "OOLU", "website": "https://www.oocl.com",
                "tracking_url": "https://www.oocl.com/eng/ourservices/eservices/cargotracking/Pages/cargotracking.aspx?container={container}",
                "api_endpoint": "https://api.oocl.com/tracking",
                "supported_formats": ["OOLU", "OOCU"],
                "regions": ["Asia", "Global"], "container_types": ["20GP", "40GP", "40HC", "45HC"]
            },
            {
                "id": "YANG_MING", "name": "Yang Ming Marine Transport", "name_ar": "يانغ مينغ للنقل البحري",
                "code": "YMLU", "website": "https://www.yangming.com",
                "tracking_url": "https://www.yangming.com/e-service/Track_Trace/track_trace_cargo_tracking.aspx?container={container}",
                "api_endpoint": "https://api.yangming.com/tracking",
                "supported_formats": ["YMLU", "YMLH"],
                "regions": ["Asia", "Global"], "container_types": ["20GP", "40GP", "40HC", "45HC"]
            },
            {
                "id": "HMM", "name": "Hyundai Merchant Marine", "name_ar": "هيونداي التجارية البحرية",
                "code": "HDMU", "website": "https://www.hmm21.com",
                "tracking_url": "https://www.hmm21.com/cms/business/ebiz/trackTrace/trackTrace/index.jsp?container={container}",
                "api_endpoint": "https://api.hmm.com/tracking",
                "supported_formats": ["HDMU", "HMMU"],
                "regions": ["Asia", "Global"], "container_types": ["20GP", "40GP", "40HC", "45HC"]
            },
            {
                "id": "ONE", "name": "Ocean Network Express", "name_ar": "شبكة المحيط السريع",
                "code": "ONEU", "website": "https://www.one-line.com",
                "tracking_url": "https://ecomm.one-line.com/one-ecom/manage-shipment/track?container={container}",
                "api_endpoint": "https://api.one-line.com/tracking",
                "supported_formats": ["ONEU", "ONEY"],
                "regions": ["Asia", "Global"], "container_types": ["20GP", "40GP", "40HC", "45HC"]
            },
            # شركات الشحن الإقليمية - الشرق الأوسط
            {
                "id": "UASC", "name": "United Arab Shipping Company", "name_ar": "الشركة العربية المتحدة للنقل البحري",
                "code": "UASU", "website": "https://www.uasc.net",
                "tracking_url": "https://www.uasc.net/en/our-services/track-and-trace?container={container}",
                "api_endpoint": "https://api.uasc.net/tracking",
                "supported_formats": ["UASU", "UACU"],
                "regions": ["Middle East", "Asia"], "container_types": ["20GP", "40GP", "40HC"]
            },
            {
                "id": "ARKAS", "name": "Arkas Line", "name_ar": "خط أركاس",
                "code": "ARKU", "website": "https://www.arkasline.com.tr",
                "tracking_url": "https://www.arkasline.com.tr/cargo-tracking?container={container}",
                "api_endpoint": "https://api.arkas.com/tracking",
                "supported_formats": ["ARKU", "ARKL"],
                "regions": ["Mediterranean", "Black Sea"], "container_types": ["20GP", "40GP", "40HC"]
            },
            # شركات الشحن الأمريكية
            {
                "id": "MATSON", "name": "Matson Navigation", "name_ar": "ماتسون للملاحة",
                "code": "MATS", "website": "https://www.matson.com",
                "tracking_url": "https://www.matson.com/shipment-tracking.html?container={container}",
                "api_endpoint": "https://api.matson.com/tracking",
                "supported_formats": ["MATS", "MATU"],
                "regions": ["Pacific", "US"], "container_types": ["20GP", "40GP", "40HC", "45HC"]
            }
        ]
        
        companies = []
        for data in companies_data:
            company = ShippingCompany(**data)
            companies.append(company)
            
        logger.info(f"تم تحميل {len(companies)} شركة شحن")
        return companies
    
    def _load_ai_patterns(self) -> Dict:
        """تحميل أنماط الذكاء الاصطناعي للتعرف على أرقام الحاويات"""
        return {
            "container_patterns": [
                r"^[A-Z]{4}[0-9]{7}$",  # النمط القياسي
                r"^[A-Z]{3}U[0-9]{7}$",  # نمط الحاويات
                r"^[A-Z]{4}[0-9]{6}[0-9]$",  # نمط بديل
            ],
            "company_codes": {
                "MAEU": ["MAEU", "MSKU", "MSCU"],
                "MSCU": ["MSCU", "MEDU"],
                "CMAU": ["CMAU", "CGMU"],
                "COSU": ["COSU", "CCOS"],
                "HLCU": ["HLCU", "HLBU"],
                "EGLV": ["EGLV", "EGHU"],
                "OOLU": ["OOLU", "OOCU"],
                "YMLU": ["YMLU", "YMLH"],
                "HDMU": ["HDMU", "HMMU"],
                "ONEU": ["ONEU", "ONEY"],
                "UASU": ["UASU", "UACU"],
                "ARKU": ["ARKU", "ARKL"],
                "MATS": ["MATS", "MATU"]
            }
        }
    
    def identify_shipping_company(self, container_number: str) -> Tuple[Optional[ShippingCompany], float]:
        """تحديد شركة الشحن بناءً على رقم الحاوية باستخدام الذكاء الاصطناعي"""
        if not container_number or len(container_number) < 4:
            return None, 0.0
        
        container_number = container_number.upper().strip()
        
        # استخراج كود الشركة من أول 4 أحرف
        company_code = container_number[:4]
        
        # البحث المباشر
        for company in self.companies:
            if company_code in company.supported_formats:
                return company, 1.0
        
        # البحث الضبابي (Fuzzy Search)
        best_match = None
        best_score = 0.0
        
        for company in self.companies:
            for format_code in company.supported_formats:
                # حساب التشابه
                similarity = self._calculate_similarity(company_code, format_code)
                if similarity > best_score and similarity > 0.7:
                    best_score = similarity
                    best_match = company
        
        return best_match, best_score
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """حساب التشابه بين نصين"""
        try:
            from fuzzywuzzy import fuzz
            return fuzz.ratio(str1, str2) / 100.0
        except ImportError:
            # حساب بسيط للتشابه
            if str1 == str2:
                return 1.0
            elif str1[:3] == str2[:3]:
                return 0.8
            elif str1[:2] == str2[:2]:
                return 0.6
            else:
                return 0.0
    
    def validate_container_number(self, container_number: str) -> bool:
        """التحقق من صحة رقم الحاوية"""
        if not container_number:
            return False
        
        container_number = container_number.upper().strip()
        
        # التحقق من الطول
        if len(container_number) != 11:
            return False
        
        # التحقق من النمط
        for pattern in self.ai_patterns["container_patterns"]:
            if re.match(pattern, container_number):
                return True
        
        return False
    
    def get_all_companies(self) -> List[ShippingCompany]:
        """جلب جميع شركات الشحن"""
        return self.companies
    
    def search_companies(self, query: str) -> List[ShippingCompany]:
        """البحث في شركات الشحن"""
        if not query:
            return self.companies
        
        query = query.lower()
        results = []
        
        for company in self.companies:
            # البحث في الاسم الإنجليزي والعربي والكود
            if (query in company.name.lower() or 
                query in company.name_ar or 
                query in company.code.lower()):
                results.append(company)
        
        return results
    
    def get_company_by_code(self, code: str) -> Optional[ShippingCompany]:
        """جلب شركة بالكود"""
        for company in self.companies:
            if company.code.upper() == code.upper():
                return company
        return None
    
    def close(self):
        """إغلاق المحرك"""
        logger.info("تم إغلاق محرك شركات الشحن الذكي")

# مثيل عام للاستخدام
smart_shipping_engine = SmartShippingCompaniesEngine()
