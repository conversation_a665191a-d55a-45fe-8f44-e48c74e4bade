<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البحث عن مورد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .search-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
            max-width: 100%;
            margin: 0 auto;
        }
        
        .search-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .table-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
            cursor: pointer;
        }
        
        .btn-select {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
        }
        
        .btn-select:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .search-controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="search-container">
        <div class="search-header">
            <h3><i class="fas fa-search me-2"></i>البحث عن مورد</h3>
            <p class="mb-0">اختر المورد المطلوب من القائمة أدناه</p>
        </div>
        
        <!-- شريط البحث -->
        <div class="search-controls">
            <div class="row">
                <div class="col-md-4">
                    <label for="searchCode" class="form-label">كود المورد</label>
                    <input type="text" class="form-control" id="searchCode" placeholder="ابحث بالكود...">
                </div>
                <div class="col-md-4">
                    <label for="searchName" class="form-label">اسم المورد</label>
                    <input type="text" class="form-control" id="searchName" placeholder="ابحث بالاسم...">
                </div>
                <div class="col-md-4">
                    <label for="searchType" class="form-label">نوع المورد</label>
                    <select class="form-select" id="searchType">
                        <option value="">جميع الأنواع</option>
                        <option value="company">شركة</option>
                        <option value="individual">فرد</option>
                        <option value="government">جهة حكومية</option>
                        <option value="international">مورد دولي</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-12">
                    <button type="button" class="btn btn-primary" onclick="searchSuppliers()">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearSearch()">
                        <i class="fas fa-times me-2"></i>مسح
                    </button>
                    <button type="button" class="btn btn-warning" onclick="window.close()">
                        <i class="fas fa-times-circle me-2"></i>إغلاق
                    </button>
                </div>
            </div>
        </div>

        <!-- نتائج البحث -->
        <div id="searchResults">
            <div class="text-center">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <p class="text-muted">اضغط "بحث" لعرض الموردين</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // البحث عن الموردين
        function searchSuppliers() {
            const searchData = {
                code: document.getElementById('searchCode').value.trim(),
                name: document.getElementById('searchName').value.trim(),
                type: document.getElementById('searchType').value
            };
            
            const resultsDiv = document.getElementById('searchResults');
            
            // عرض مؤشر التحميل
            resultsDiv.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري البحث...</span>
                    </div>
                    <p class="mt-2">جاري البحث عن الموردين...</p>
                </div>
            `;
            
            // إرسال طلب البحث
            fetch('/purchase-orders/api/search-suppliers', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(searchData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayResults(data.suppliers);
                } else {
                    resultsDiv.innerHTML = `
                        <div class="alert alert-warning text-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ${data.message || 'لم يتم العثور على موردين'}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('خطأ في البحث:', error);
                resultsDiv.innerHTML = `
                    <div class="alert alert-danger text-center">
                        <i class="fas fa-times-circle me-2"></i>
                        حدث خطأ أثناء البحث عن الموردين
                    </div>
                `;
            });
        }

        // عرض النتائج
        function displayResults(suppliers) {
            const resultsDiv = document.getElementById('searchResults');
            
            if (!suppliers || suppliers.length === 0) {
                resultsDiv.innerHTML = `
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle me-2"></i>
                        لم يتم العثور على موردين مطابقين لمعايير البحث
                    </div>
                `;
                return;
            }
            
            let html = `
                <div class="table-container">
                    <table class="table table-hover mb-0">
                        <thead class="table-primary">
                            <tr>
                                <th>كود المورد</th>
                                <th>اسم المورد</th>
                                <th>النوع</th>
                                <th>الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>الحالة</th>
                                <th>اختيار</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            suppliers.forEach(function(supplier) {
                const statusBadge = supplier.is_active ? 
                    '<span class="badge bg-success">نشط</span>' : 
                    '<span class="badge bg-secondary">غير نشط</span>';
                    
                html += `
                    <tr onclick="selectSupplier('${supplier.code}', '${supplier.name}')">
                        <td><strong>${supplier.code || 'غير محدد'}</strong></td>
                        <td>${supplier.name || 'غير محدد'}</td>
                        <td>${getTypeLabel(supplier.type)}</td>
                        <td>${supplier.phone || '-'}</td>
                        <td>${supplier.email || '-'}</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button type="button" class="btn btn-select" 
                                    onclick="selectSupplier('${supplier.code}', '${supplier.name}')">
                                <i class="fas fa-check me-1"></i>اختيار
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            html += `
                        </tbody>
                    </table>
                </div>
                <div class="mt-3 text-center">
                    <small class="text-muted">تم العثور على ${suppliers.length} مورد</small>
                </div>
            `;
            
            resultsDiv.innerHTML = html;
        }

        // اختيار مورد
        function selectSupplier(code, name) {
            // إرسال البيانات للنافذة الأصلية
            if (window.opener) {
                window.opener.postMessage({
                    type: 'supplierSelected',
                    code: code,
                    name: name
                }, window.location.origin);
            }
            
            // إغلاق النافذة
            window.close();
        }

        // مسح البحث
        function clearSearch() {
            document.getElementById('searchCode').value = '';
            document.getElementById('searchName').value = '';
            document.getElementById('searchType').value = '';
            
            document.getElementById('searchResults').innerHTML = `
                <div class="text-center">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <p class="text-muted">اضغط "بحث" لعرض الموردين</p>
                </div>
            `;
        }

        // تسميات الأنواع
        function getTypeLabel(type) {
            const types = {
                'company': 'شركة',
                'individual': 'فرد',
                'government': 'جهة حكومية',
                'international': 'مورد دولي'
            };
            return types[type] || 'غير محدد';
        }

        // البحث عند الضغط على Enter
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchSuppliers();
            }
        });

        // تحميل جميع الموردين عند فتح الصفحة
        window.addEventListener('load', function() {
            searchSuppliers();
        });
    </script>
</body>
</html>
