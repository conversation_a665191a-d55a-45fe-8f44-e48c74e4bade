"""
إدارة الصرافين والبنوك - نسخة مبسطة
"""

from flask import render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from . import transfers_bp
from database_manager import DatabaseManager
import logging

logger = logging.getLogger(__name__)

@transfers_bp.route('/money-changers')
@login_required
def money_changers():
    """صفحة إدارة الصرافين والبنوك"""
    try:
        db = DatabaseManager()
        
        # استعلام مع ربط جدول الفروع
        query = """
        SELECT
            mcb.id, mcb.name, mcb.type, mcb.branch_id, mcb.contact_person,
            mcb.phone, mcb.email, mcb.commission_rate, mcb.is_active,
            COALESCE(b.brn_lname, 'غير محدد') as branch_name
        FROM money_changers_banks mcb
        LEFT JOIN branches b ON mcb.branch_id = b.brn_no
        ORDER BY mcb.name
        """
        result = db.execute_query(query)
        
        money_changers_list = []
        
        if result:
            # دالة مساعدة لتحويل LOB إلى نص
            def safe_str(value):
                if value is None:
                    return ''
                try:
                    if hasattr(value, 'read'):
                        return value.read()
                    return str(value)
                except:
                    return str(value) if value else ''

            for row in result:
                # معالجة آمنة للعمولة
                try:
                    commission_rate = float(row[7]) if row[7] is not None else 0.0
                except (ValueError, TypeError):
                    commission_rate = 0.0

                money_changers_list.append({
                    'id': row[0],
                    'name': safe_str(row[1]),
                    'type': safe_str(row[2]),
                    'branch_id': row[3],
                    'contact_person': safe_str(row[4]),
                    'phone': safe_str(row[5]),
                    'email': safe_str(row[6]),
                    'commission_rate': commission_rate,
                    'is_active': bool(row[8]) if row[8] is not None else True,
                    'created_at': 'اليوم',
                    'transfer_count': 0,
                    'total_amount': 0.0,
                    'branch_name': safe_str(row[9])  # اسم الفرع من الاستعلام
                })
        
        # جلب الفروع من جدول BRANCHES
        branches = []
        try:
            branches_query = "SELECT brn_no, brn_lname FROM branches WHERE is_active = 1 ORDER BY brn_lname"
            branches_result = db.execute_query(branches_query)
            if branches_result:
                branches = [{'id': row[0], 'name': row[1]} for row in branches_result]
            else:
                # فرع افتراضي إذا لم توجد فروع
                branches = [{'id': 1, 'name': 'الفرع الرئيسي'}]
        except Exception as branches_error:
            logger.warning(f"خطأ في جلب الفروع: {branches_error}")
            branches = [{'id': 1, 'name': 'الفرع الرئيسي'}]
        
        print(f"DEBUG: تم جلب {len(money_changers_list)} صراف/بنك")
        logger.info(f"تم عرض {len(money_changers_list)} صراف/بنك")

        # طباعة عينة من البيانات للتحقق
        if money_changers_list:
            sample = money_changers_list[0]
            logger.info(f"عينة من البيانات: {sample['name']} - الفرع: {sample['branch_name']}")
        
        return render_template('transfers/money_changers.html', 
                             money_changers=money_changers_list,
                             branches=branches)
        
    except Exception as e:
        print(f"DEBUG ERROR: {e}")
        import traceback
        traceback.print_exc()
        logger.error(f"خطأ: {e}")
        flash('حدث خطأ في تحميل البيانات', 'error')

        return render_template('transfers/money_changers.html',
                             money_changers=[],
                             branches=[{'id': 1, 'name': 'الفرع الرئيسي'}])

@transfers_bp.route('/money-changers/add', methods=['POST'])
@login_required
def add_money_changer():
    """إضافة صراف/بنك جديد"""
    try:
        data = request.get_json()
        
        db = DatabaseManager()
        
        insert_query = """
        INSERT INTO money_changers_banks
        (id, name, type, branch_id, contact_person, phone, email, commission_rate,
         address, account_details, is_active, created_at, updated_at)
        VALUES (money_changers_banks_seq.NEXTVAL, :1, :2, :3, :4, :5, :6, :7, :8, :9, :10,
                CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        """

        db.execute_update(insert_query, [
            data.get('name'),
            data.get('type'),
            data.get('branch_id'),
            data.get('contact_person'),
            data.get('phone'),
            data.get('email'),
            float(data.get('commission_rate', 0)),
            data.get('address'),
            data.get('account_details'),
            1 if data.get('is_active') else 0
        ])
        
        db.commit()
        
        return jsonify({'success': True, 'message': 'تم إضافة الصراف/البنك بنجاح'})
        
    except Exception as e:
        logger.error(f"خطأ في إضافة صراف/بنك: {e}")
        return jsonify({'success': False, 'message': str(e)})

@transfers_bp.route('/money-changers/<int:id>/details')
@login_required
def money_changer_details(id):
    """جلب تفاصيل صراف/بنك"""
    try:
        db = DatabaseManager()
        # استعلام محدد للأعمدة لتجنب مشاكل الترتيب
        query = """
        SELECT id, name, type, branch_id, contact_person, phone, email,
               commission_rate, address, account_details, is_active,
               created_at, updated_at
        FROM money_changers_banks
        WHERE id = :1
        """
        result = db.execute_query(query, [id])

        if result:
            row = result[0]

            # معالجة آمنة للعمولة
            try:
                commission_rate = float(row[7]) if row[7] is not None else 0.0
            except (ValueError, TypeError):
                commission_rate = 0.0

            # دالة مساعدة لتحويل LOB إلى نص
            def safe_str(value):
                if value is None:
                    return ''
                try:
                    # إذا كان LOB، قراءة المحتوى
                    if hasattr(value, 'read'):
                        return value.read()
                    return str(value)
                except:
                    return str(value) if value else ''

            data = {
                'id': row[0],
                'name': safe_str(row[1]),
                'type': safe_str(row[2]),
                'branch_id': row[3],
                'contact_person': safe_str(row[4]),
                'phone': safe_str(row[5]),
                'email': safe_str(row[6]),
                'commission_rate': commission_rate,
                'address': safe_str(row[8]),
                'account_details': safe_str(row[9]),
                'is_active': bool(row[10]),
                'created_at': safe_str(row[11]),
                'updated_at': safe_str(row[12]),
                'total_transfers': 0,
                'total_amount': 0.0,
                'total_commission': 0.0
            }
            return jsonify({'success': True, 'data': data})
        else:
            return jsonify({'success': False, 'message': 'غير موجود'})

    except Exception as e:
        logger.error(f"خطأ في جلب التفاصيل: {e}")
        return jsonify({'success': False, 'message': str(e)})

@transfers_bp.route('/money-changers/<int:id>/edit', methods=['POST'])
@login_required
def edit_money_changer(id):
    """تعديل صراف/بنك"""
    try:
        data = request.get_json()
        db = DatabaseManager()

        # تحديث الحقول العادية أولاً (بدون CLOB)
        query = """
        UPDATE money_changers_banks
        SET name = :1, type = :2, branch_id = :3, contact_person = :4, phone = :5,
            email = :6, commission_rate = :7, address = :8,
            is_active = :9, updated_at = CURRENT_TIMESTAMP
        WHERE id = :10
        """

        db.execute_update(query, [
            data.get('name'),
            data.get('type'),
            data.get('branch_id'),
            data.get('contact_person'),
            data.get('phone'),
            data.get('email'),
            float(data.get('commission_rate', 0)) if data.get('commission_rate') else 0.0,
            data.get('address'),
            1 if data.get('is_active') else 0,
            id
        ])

        # تحديث CLOB منفصل إذا كان موجود
        account_details = data.get('account_details')
        if account_details is not None:
            clob_query = """
            UPDATE money_changers_banks
            SET account_details = :1
            WHERE id = :2
            """
            db.execute_update(clob_query, [str(account_details), id])

        # Oracle يحفظ التغييرات تلقائياً
        return jsonify({'success': True, 'message': 'تم تحديث الصراف/البنك بنجاح'})

    except Exception as e:
        logger.error(f"خطأ في التعديل: {e}")
        return jsonify({'success': False, 'message': str(e)})

@transfers_bp.route('/money-changers/<int:id>/toggle-status', methods=['POST'])
@login_required
def toggle_money_changer_status(id):
    """تغيير حالة صراف/بنك"""
    try:
        db = DatabaseManager()

        # جلب الحالة الحالية
        current_status = db.execute_query("SELECT is_active FROM money_changers_banks WHERE id = :1", [id])
        if current_status:
            new_status = 0 if current_status[0][0] else 1
            db.execute_update("UPDATE money_changers_banks SET is_active = :1 WHERE id = :2", [new_status, id])
            db.commit()

            status_text = 'تم التفعيل' if new_status else 'تم الإلغاء'
            return jsonify({'success': True, 'message': status_text})
        else:
            return jsonify({'success': False, 'message': 'غير موجود'})

    except Exception as e:
        logger.error(f"خطأ في تغيير الحالة: {e}")
        return jsonify({'success': False, 'message': str(e)})

@transfers_bp.route('/money-changers/<int:id>/delete', methods=['POST', 'DELETE'])
@login_required
def delete_money_changer(id):
    """حذف صراف/بنك"""
    try:
        db = DatabaseManager()
        # التحقق من وجود حوالات مرتبطة بهذا الصراف/البنك
        check_query = "SELECT COUNT(*) FROM TRANSFER_REQUESTS WHERE money_changer_bank_id = :1"
        result = db.execute_query(check_query, [id])

        if result and result[0][0] > 0:
            return jsonify({
                'success': False,
                'message': f'لا يمكن حذف هذا الصراف/البنك لأنه مرتبط بـ {result[0][0]} طلب حوالة. يمكنك إلغاء تفعيله بدلاً من الحذف.'
            })

        # حذف الصراف/البنك
        db.execute_update("DELETE FROM money_changers_banks WHERE id = :1", [id])

        return jsonify({'success': True, 'message': 'تم حذف الصراف/البنك بنجاح'})

    except Exception as e:
        logger.error(f"خطأ في الحذف: {e}")
        return jsonify({'success': False, 'message': str(e)})
