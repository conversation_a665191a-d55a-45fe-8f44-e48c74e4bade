# -*- coding: utf-8 -*-
"""
نماذج إدارة المخزون
Inventory Management Forms
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, DecimalField, IntegerField, SubmitField, BooleanField
from wtforms.validators import DataRequired, Length, NumberRange, Optional
from wtforms.widgets import TextArea
from app.models import ItemCategory
from decimal import Decimal

class ItemForm(FlaskForm):
    """نموذج إنشاء/تعديل صنف"""
    
    # معلومات أساسية
    code = StringField(
        'كود الصنف',
        validators=[DataRequired(), Length(min=1, max=50)],
        render_kw={'class': 'form-control', 'placeholder': 'كود فريد للصنف'}
    )
    
    name = StringField(
        'اسم الصنف',
        validators=[DataRequired(), Length(min=1, max=200)],
        render_kw={'class': 'form-control', 'placeholder': 'اسم الصنف'}
    )
    
    description = TextAreaField(
        'الوصف',
        validators=[Optional(), Length(max=1000)],
        widget=TextArea(),
        render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف تفصيلي للصنف...'}
    )
    
    category_id = SelectField(
        'الفئة',
        coerce=int,
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    # معلومات الوحدة والقياس
    unit = StringField(
        'وحدة القياس',
        validators=[DataRequired(), Length(min=1, max=20)],
        render_kw={'class': 'form-control', 'placeholder': 'قطعة، كيلو، متر، إلخ'}
    )
    
    # معلومات المخزون
    current_stock = DecimalField(
        'الرصيد الحالي',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        default=0,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0'}
    )
    
    minimum_stock = DecimalField(
        'الحد الأدنى للمخزون',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        default=0,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0'}
    )
    
    maximum_stock = DecimalField(
        'الحد الأقصى للمخزون',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0'}
    )
    
    reorder_point = DecimalField(
        'نقطة إعادة الطلب',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0'}
    )
    
    # معلومات التكلفة
    standard_cost = DecimalField(
        'التكلفة المعيارية',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0'}
    )
    
    average_cost = DecimalField(
        'متوسط التكلفة',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0', 'readonly': True}
    )
    
    # معلومات إضافية
    location = StringField(
        'الموقع',
        validators=[Optional(), Length(max=100)],
        render_kw={'class': 'form-control', 'placeholder': 'موقع التخزين'}
    )
    
    barcode = StringField(
        'الباركود',
        validators=[Optional(), Length(max=50)],
        render_kw={'class': 'form-control', 'placeholder': 'رقم الباركود'}
    )
    
    is_active = BooleanField(
        'نشط',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    submit = SubmitField('حفظ الصنف', render_kw={'class': 'btn btn-primary'})
    
    def __init__(self, *args, **kwargs):
        super(ItemForm, self).__init__(*args, **kwargs)
        
        # تحديث خيارات الفئات
        try:
            self.category_id.choices = [
                (c.id, c.name_ar)
                for c in ItemCategory.query.filter_by(is_active=True).all()
            ]
        except:
            self.category_id.choices = []

class ItemCategoryForm(FlaskForm):
    """نموذج إنشاء/تعديل فئة الأصناف"""
    
    name = StringField(
        'اسم الفئة',
        validators=[DataRequired(), Length(min=1, max=100)],
        render_kw={'class': 'form-control', 'placeholder': 'اسم الفئة'}
    )
    
    description = TextAreaField(
        'الوصف',
        validators=[Optional(), Length(max=500)],
        render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف الفئة...'}
    )
    
    is_active = BooleanField(
        'نشط',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    submit = SubmitField('حفظ الفئة', render_kw={'class': 'btn btn-primary'})

class StockMovementForm(FlaskForm):
    """نموذج حركة المخزون"""
    
    item_id = SelectField(
        'الصنف',
        coerce=int,
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    movement_type = SelectField(
        'نوع الحركة',
        choices=[
            ('in', 'وارد'),
            ('out', 'صادر'),
            ('adjustment', 'تسوية'),
            ('transfer', 'نقل')
        ],
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    quantity = DecimalField(
        'الكمية',
        validators=[DataRequired(), NumberRange(min=0.01)],
        places=2,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0.01'}
    )
    
    unit_price = DecimalField(
        'سعر الوحدة',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0'}
    )
    
    reference_type = SelectField(
        'نوع المرجع',
        choices=[
            ('manual', 'يدوي'),
            ('purchase_order', 'أمر شراء'),
            ('goods_receipt', 'استلام بضائع'),
            ('adjustment', 'تسوية'),
            ('transfer', 'نقل')
        ],
        default='manual',
        render_kw={'class': 'form-select'}
    )
    
    reference_number = StringField(
        'رقم المرجع',
        validators=[Optional(), Length(max=50)],
        render_kw={'class': 'form-control', 'placeholder': 'رقم المرجع إن وجد'}
    )
    
    notes = TextAreaField(
        'ملاحظات',
        validators=[Optional(), Length(max=500)],
        render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات حول الحركة...'}
    )
    
    submit = SubmitField('تسجيل الحركة', render_kw={'class': 'btn btn-primary'})
    
    def __init__(self, *args, **kwargs):
        super(StockMovementForm, self).__init__(*args, **kwargs)
        
        # تحديث خيارات الأصناف النشطة
        from app.models import Item
        try:
            self.item_id.choices = [
                (item.id, f"{item.code} - {item.name_ar}")
                for item in Item.query.filter_by(is_active=True).all()
            ]
        except:
            self.item_id.choices = []

class InventorySearchForm(FlaskForm):
    """نموذج البحث في المخزون"""
    
    search_term = StringField(
        'البحث',
        validators=[Optional(), Length(max=100)],
        render_kw={'class': 'form-control', 'placeholder': 'كود الصنف، الاسم، أو الوصف...'}
    )
    
    category_id = SelectField(
        'الفئة',
        coerce=int,
        choices=[],
        render_kw={'class': 'form-select'}
    )
    
    stock_status = SelectField(
        'حالة المخزون',
        choices=[
            ('', 'جميع الحالات'),
            ('in_stock', 'متوفر'),
            ('low_stock', 'منخفض'),
            ('out_of_stock', 'نفد'),
            ('overstock', 'زائد')
        ],
        default='',
        render_kw={'class': 'form-select'}
    )
    
    is_active = SelectField(
        'الحالة',
        choices=[
            ('', 'الكل'),
            ('1', 'نشط'),
            ('0', 'غير نشط')
        ],
        default='',
        render_kw={'class': 'form-select'}
    )
    
    submit = SubmitField('بحث', render_kw={'class': 'btn btn-outline-primary'})
    
    def __init__(self, *args, **kwargs):
        super(InventorySearchForm, self).__init__(*args, **kwargs)
        
        # تحديث خيارات الفئات
        try:
            self.category_id.choices = [('', 'جميع الفئات')] + [
                (c.id, c.name_ar)
                for c in ItemCategory.query.filter_by(is_active=True).all()
            ]
        except:
            self.category_id.choices = [('', 'جميع الفئات')]

class StockAdjustmentForm(FlaskForm):
    """نموذج تسوية المخزون"""
    
    item_id = SelectField(
        'الصنف',
        coerce=int,
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    current_stock = DecimalField(
        'الرصيد الحالي',
        validators=[DataRequired(), NumberRange(min=0)],
        places=2,
        render_kw={'class': 'form-control', 'readonly': True}
    )
    
    actual_stock = DecimalField(
        'الرصيد الفعلي',
        validators=[DataRequired(), NumberRange(min=0)],
        places=2,
        render_kw={'class': 'form-control', 'step': '0.01', 'min': '0'}
    )
    
    reason = SelectField(
        'سبب التسوية',
        choices=[
            ('physical_count', 'جرد فعلي'),
            ('damage', 'تلف'),
            ('theft', 'فقدان'),
            ('expiry', 'انتهاء صلاحية'),
            ('system_error', 'خطأ نظام'),
            ('other', 'أخرى')
        ],
        validators=[DataRequired()],
        render_kw={'class': 'form-select'}
    )
    
    notes = TextAreaField(
        'ملاحظات',
        validators=[DataRequired(), Length(min=1, max=500)],
        render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'اشرح سبب التسوية...'}
    )
    
    submit = SubmitField('تنفيذ التسوية', render_kw={'class': 'btn btn-warning'})
    
    def __init__(self, *args, **kwargs):
        super(StockAdjustmentForm, self).__init__(*args, **kwargs)
        
        # تحديث خيارات الأصناف النشطة
        from app.models import Item
        try:
            self.item_id.choices = [
                (item.id, f"{item.code} - {item.name_ar} (الرصيد: {item.current_stock or 0})")
                for item in Item.query.filter_by(is_active=True).all()
            ]
        except:
            self.item_id.choices = []
