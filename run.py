# -*- coding: utf-8 -*-
"""
تشغيل النظام المحاسبي المتقدم
Run Advanced Accounting System
"""

import os
from dotenv import load_dotenv
from app import create_app

# تحميل متغيرات البيئة من ملف .env
load_dotenv()

# إعداد متغيرات البيئة لـ Oracle
os.environ['ORACLE_SID'] = 'orcl'
os.environ['ORACLE_USERNAME'] = 'accounting_user'
os.environ['ORACLE_PASSWORD'] = 'accounting_password'

# إنشاء التطبيق
app = create_app()

# إضافة routes إعادة تحميل الإشعارات
try:
    from reload_notifications_in_app import register_reload_routes
    register_reload_routes(app)
except Exception as e:
    print(f"⚠️ تحذير: لم يتم تحميل routes إعادة التحميل: {e}")

# إعداد shell context
@app.shell_context_processor
def make_shell_context():
    """إعداد سياق shell للتطبيق"""
    return {
        'app': app
    }

if __name__ == '__main__':
    import json

    # تحميل إعدادات الخادم
    config_file = 'config/server_config.json'
    server_config = {}

    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                server_config = json.load(f)
        except:
            pass

    # إعدادات افتراضية
    host = server_config.get('host', '0.0.0.0')
    port = server_config.get('port', 5000)
    ssl_enabled = server_config.get('ssl_enabled', False)
    ssl_cert_path = server_config.get('ssl_cert_path', '')
    ssl_key_path = server_config.get('ssl_key_path', '')

    print(f"📋 إعدادات الخادم:")
    print(f"   المضيف: {host}")
    print(f"   المنفذ: {port}")
    print(f"   SSL مفعل: {ssl_enabled}")
    if ssl_enabled:
        print(f"   مسار الشهادة: {ssl_cert_path}")
        print(f"   مسار المفتاح: {ssl_key_path}")

    # تشغيل الخادم
    if ssl_enabled and ssl_cert_path and ssl_key_path:
        # التحقق من وجود ملفات SSL
        if os.path.exists(ssl_cert_path) and os.path.exists(ssl_key_path):
            print(f"🔒 تشغيل الخادم مع SSL على https://{host}:{port}")
            print(f"📄 الشهادة: {ssl_cert_path}")
            print(f"🔑 المفتاح: {ssl_key_path}")

            try:
                # إنشاء SSL context
                import ssl
                context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
                context.load_cert_chain(ssl_cert_path, ssl_key_path)

                print("✅ تم تحميل شهادات SSL بنجاح")
                print(f"🌐 الموقع متاح على: https://{host}:{port}")

                app.run(
                    debug=True,
                    host=host,
                    port=port,
                    ssl_context=context
                )
            except Exception as e:
                print(f"❌ خطأ في تحميل SSL: {e}")
                print(f"🔓 تشغيل الخادم بدون SSL على http://{host}:{port}")
                app.run(debug=True, host=host, port=port)
        else:
            print(f"❌ ملفات SSL غير موجودة:")
            print(f"   الشهادة: {ssl_cert_path} ({'موجود' if os.path.exists(ssl_cert_path) else 'غير موجود'})")
            print(f"   المفتاح: {ssl_key_path} ({'موجود' if os.path.exists(ssl_key_path) else 'غير موجود'})")
            print(f"🔓 تشغيل الخادم بدون SSL على http://{host}:{port}")
            app.run(debug=True, host=host, port=port)
    else:
        print(f"🔓 تشغيل الخادم بدون SSL على http://{host}:{port}")
        app.run(debug=True, host=host, port=port)
