#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مزامنة حالات أوامر الشراء الموجودة مع حالات الشحنات
"""

import sys
sys.path.append('.')
from oracle_manager import OracleManager

def sync_existing_po_statuses():
    """مزامنة حالات أوامر الشراء الموجودة"""
    print('🔄 بدء مزامنة حالات أوامر الشراء الموجودة...')

    try:
        oracle = OracleManager()
        
        # البحث عن جميع أوامر الشراء المرتبطة بشحنات
        query = """
            SELECT DISTINCT
                po.ID as po_id,
                po.PO_NUMBER,
                po.STATUS as current_po_status,
                cs.id as shipment_id,
                cs.tracking_number,
                cs.shipment_status as current_shipment_status,
                mapping.po_status as expected_po_status
            FROM PURCHASE_ORDERS po
            JOIN cargo_shipments cs ON po.ID = cs.purchase_order_id
            JOIN po_shipment_status_map mapping ON cs.shipment_status = mapping.shipment_status
            WHERE mapping.auto_update = 1 
            AND mapping.is_active = 1
            AND po.STATUS != mapping.po_status
            ORDER BY po.ID
        """
        
        mismatched_orders = oracle.execute_query(query)
        
        if not mismatched_orders:
            print('✅ جميع أوامر الشراء متزامنة مع حالات الشحنات')
            oracle.close()
            return True
        
        print(f'📋 وجدت {len(mismatched_orders)} أمر شراء يحتاج لتحديث:')
        
        updated_count = 0
        error_count = 0
        
        for order in mismatched_orders:
            po_id = order[0]
            po_number = order[1]
            current_po_status = order[2]
            shipment_id = order[3]
            tracking_number = order[4]
            shipment_status = order[5]
            expected_po_status = order[6]
            
            print(f'\n🔄 تحديث أمر الشراء {po_number}:')
            print(f'   📦 الشحنة: {tracking_number} (حالة: {shipment_status})')
            print(f'   📊 الحالة الحالية: {current_po_status} → المطلوبة: {expected_po_status}')
            
            try:
                # تحديث حالة أمر الشراء
                update_query = """
                    UPDATE PURCHASE_ORDERS
                    SET STATUS = :1,
                        UPDATED_AT = SYSDATE
                    WHERE ID = :2
                """
                
                oracle.execute_update(update_query, [expected_po_status, po_id])
                
                # إضافة سجل في تاريخ التحديثات
                history_query = """
                    INSERT INTO po_status_history (
                        id, po_id, old_status, new_status, 
                        shipment_id, shipment_status, change_reason, auto_updated
                    ) VALUES (
                        po_hist_seq.NEXTVAL, :1, :2, :3,
                        :4, :5, 'مزامنة أولية للبيانات الموجودة', 1
                    )
                """
                
                oracle.execute_update(history_query, [
                    po_id, current_po_status, expected_po_status,
                    shipment_id, shipment_status
                ])
                
                print(f'   ✅ تم التحديث بنجاح')
                updated_count += 1
                
            except Exception as e:
                print(f'   ❌ خطأ في التحديث: {e}')
                error_count += 1
        
        print(f'\n📊 ملخص العملية:')
        print(f'   ✅ تم تحديث: {updated_count} أمر شراء')
        print(f'   ❌ أخطاء: {error_count} أمر شراء')
        
        # التحقق من النتائج
        print(f'\n🔍 التحقق من النتائج...')
        verification_query = """
            SELECT COUNT(*) as remaining_mismatches
            FROM PURCHASE_ORDERS po
            JOIN cargo_shipments cs ON po.ID = cs.purchase_order_id
            JOIN po_shipment_status_map mapping ON cs.shipment_status = mapping.shipment_status
            WHERE mapping.auto_update = 1 
            AND mapping.is_active = 1
            AND po.STATUS != mapping.po_status
        """
        
        remaining = oracle.execute_query(verification_query)
        remaining_count = remaining[0][0] if remaining else 0
        
        if remaining_count == 0:
            print('✅ جميع أوامر الشراء أصبحت متزامنة!')
        else:
            print(f'⚠️ لا يزال هناك {remaining_count} أمر شراء غير متزامن')
        
        oracle.close()
        print('\n🎉 تمت عملية المزامنة!')
        return True
        
    except Exception as e:
        print(f'❌ خطأ في المزامنة: {e}')
        return False

def show_sync_status():
    """عرض حالة المزامنة الحالية"""
    print('📊 حالة المزامنة الحالية:')
    
    try:
        oracle = OracleManager()
        
        # إجمالي أوامر الشراء المرتبطة بشحنات
        total_query = """
            SELECT COUNT(DISTINCT po.ID) as total_linked_pos
            FROM PURCHASE_ORDERS po
            JOIN cargo_shipments cs ON po.ID = cs.purchase_order_id
        """
        
        total_result = oracle.execute_query(total_query)
        total_linked = total_result[0][0] if total_result else 0
        
        # أوامر الشراء المتزامنة
        synced_query = """
            SELECT COUNT(DISTINCT po.ID) as synced_pos
            FROM PURCHASE_ORDERS po
            JOIN cargo_shipments cs ON po.ID = cs.purchase_order_id
            JOIN po_shipment_status_map mapping ON cs.shipment_status = mapping.shipment_status
            WHERE mapping.auto_update = 1 
            AND mapping.is_active = 1
            AND po.STATUS = mapping.po_status
        """
        
        synced_result = oracle.execute_query(synced_query)
        synced_count = synced_result[0][0] if synced_result else 0
        
        # أوامر الشراء غير المتزامنة
        unsynced_count = total_linked - synced_count
        sync_percentage = (synced_count / total_linked * 100) if total_linked > 0 else 0
        
        print(f'📦 إجمالي أوامر الشراء المرتبطة: {total_linked}')
        print(f'✅ متزامنة: {synced_count} ({sync_percentage:.1f}%)')
        print(f'⚠️ غير متزامنة: {unsynced_count}')
        
        if unsynced_count > 0:
            # عرض تفاصيل غير المتزامنة
            details_query = """
                SELECT 
                    po.PO_NUMBER,
                    po.STATUS as current_po_status,
                    cs.tracking_number,
                    cs.shipment_status,
                    mapping.po_status as expected_po_status
                FROM PURCHASE_ORDERS po
                JOIN cargo_shipments cs ON po.ID = cs.purchase_order_id
                JOIN po_shipment_status_map mapping ON cs.shipment_status = mapping.shipment_status
                WHERE mapping.auto_update = 1 
                AND mapping.is_active = 1
                AND po.STATUS != mapping.po_status
                ORDER BY po.PO_NUMBER
                FETCH FIRST 5 ROWS ONLY
            """
            
            details = oracle.execute_query(details_query)
            if details:
                print(f'\n📋 أمثلة على أوامر الشراء غير المتزامنة:')
                for detail in details:
                    print(f'   {detail[0]}: {detail[1]} → {detail[4]} (شحنة: {detail[3]})')
                
                if unsynced_count > 5:
                    print(f'   ... و {unsynced_count - 5} أمر آخر')
        
        oracle.close()
        
    except Exception as e:
        print(f'❌ خطأ في عرض الحالة: {e}')

if __name__ == '__main__':
    show_sync_status()
    print('\n' + '='*50)
    sync_existing_po_statuses()
    print('\n' + '='*50)
    show_sync_status()
