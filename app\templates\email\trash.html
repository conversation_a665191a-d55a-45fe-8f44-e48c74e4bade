<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗑️ سلة المحذوفات - نظام البريد الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            direction: rtl;
            color: #ffffff;
            overflow-x: hidden;
        }
        
        /* شريط التنقل */
        .navbar-custom {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .navbar-brand {
            color: #ffffff !important;
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .navbar-nav .nav-link {
            color: #a0a0a0 !important;
            font-weight: 500;
            padding: 0.8rem 1.2rem !important;
            border-radius: 50px;
            transition: all 0.3s ease;
            margin: 0 0.2rem;
        }
        
        .navbar-nav .nav-link:hover {
            color: #ffffff !important;
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }
        
        .navbar-nav .nav-link.active {
            color: #ffffff !important;
            background: linear-gradient(135deg, #dc3545, #c82333);
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
        }
        
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .page-title {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #dc3545, #c82333, #bd2130);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            text-shadow: 0 0 30px rgba(220, 53, 69, 0.5);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #dc3545, #c82333);
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(220, 53, 69, 0.3);
        }
        
        .stat-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #dc3545, #c82333);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #a0a0a0;
            font-size: 0.9rem;
        }
        
        .emails-container {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .email-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
            border-left: 4px solid #dc3545;
        }
        
        .email-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(220, 53, 69, 0.2);
            border-color: rgba(220, 53, 69, 0.5);
        }
        
        .email-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .email-sender {
            font-weight: 600;
            color: #ffffff;
            font-size: 1rem;
        }
        
        .email-date {
            color: #a0a0a0;
            font-size: 0.9rem;
        }
        
        .email-subject {
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        
        .email-preview {
            color: #c0c0c0;
            font-size: 0.9rem;
            line-height: 1.4;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
        
        .email-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .action-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #ffffff;
            padding: 0.4rem 0.8rem;
            border-radius: 8px;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: rgba(220, 53, 69, 0.3);
            border-color: rgba(220, 53, 69, 0.5);
            color: #ffffff;
        }
        
        .no-emails {
            text-align: center;
            padding: 4rem 2rem;
        }
        
        .no-emails-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 1rem;
        }
        
        .back-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        
        .empty-trash-btn {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
            margin-bottom: 2rem;
        }
        
        .empty-trash-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(220, 53, 69, 0.4);
            color: white;
            text-decoration: none;
        }
        
        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-trash me-2"></i>
                سلة المحذوفات
            </h1>
            <p style="color: #a0a0a0; font-size: 1.1rem;">
                <i class="fas fa-info-circle me-1"></i>
                الرسائل المحذوفة - يمكن استعادتها أو حذفها نهائياً
            </p>
        </div>
        
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-trash"></i>
                </div>
                <div class="stat-number">{{ stats.total_deleted or 0 }}</div>
                <div class="stat-label">إجمالي المحذوفة</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stat-number">{{ stats.deleted_today or 0 }}</div>
                <div class="stat-label">محذوفة اليوم</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-hdd"></i>
                </div>
                <div class="stat-number">{{ "%.1f"|format((stats.total_size or 0) / 1024) }} KB</div>
                <div class="stat-label">المساحة المستخدمة</div>
            </div>
        </div>
        
        <!-- Empty Trash Button -->
        {% if emails and emails|length > 0 %}
        <div class="text-center">
            <button class="empty-trash-btn" onclick="emptyTrash()">
                <i class="fas fa-trash-alt"></i>
                إفراغ سلة المحذوفات نهائياً
            </button>
        </div>
        {% endif %}
        
        <!-- Emails Container -->
        <div class="emails-container">
            {% if emails and emails|length > 0 %}
                <!-- Emails List -->
                {% for email in emails %}
                <div class="email-item">
                    <div class="email-header">
                        <div class="email-sender">
                            <i class="fas fa-user me-1"></i>
                            من: {{ email.sender_email }}
                        </div>
                        <div class="email-date">
                            <i class="fas fa-clock me-1"></i>
                            حُذفت: {{ email.deleted_date.strftime('%Y-%m-%d %H:%M') if email.deleted_date else 'غير محدد' }}
                        </div>
                    </div>
                    <div class="email-subject">
                        {{ email.subject }}
                        {% if email.has_attachments %}
                            <i class="fas fa-paperclip ms-2 text-warning"></i>
                        {% endif %}
                    </div>
                    <div class="email-preview">
                        {{ email.body_text[:200] }}{% if email.body_text|length > 200 %}...{% endif %}
                    </div>
                    <div class="email-actions">
                        <button class="action-btn" onclick="restoreEmail('{{ email.id }}')">
                            <i class="fas fa-undo me-1"></i>
                            استعادة
                        </button>
                        <button class="action-btn" onclick="deleteEmailPermanently('{{ email.id }}')">
                            <i class="fas fa-trash-alt me-1"></i>
                            حذف نهائي
                        </button>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <!-- No Emails Message -->
                <div class="no-emails">
                    <div class="no-emails-icon">
                        <i class="fas fa-trash"></i>
                    </div>
                    <h3 style="color: #ffffff; margin-bottom: 1rem;">سلة المحذوفات فارغة</h3>
                    <p style="color: #a0a0a0;">
                        لا توجد رسائل محذوفة.<br>
                        هذا أمر جيد! سلة المحذوفات نظيفة.
                    </p>
                </div>
            {% endif %}
        </div>
        
        <!-- Back Button -->
        <div class="text-center">
            <a href="{{ url_for('email.inbox') }}" class="back-btn">
                <i class="fas fa-arrow-right"></i>
                العودة إلى صندوق الوارد
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function restoreEmail(emailId) {
            if (confirm('هل تريد استعادة هذه الرسالة إلى صندوق الوارد؟')) {
                // TODO: إضافة API لاستعادة الرسالة
                alert('سيتم إضافة هذه الميزة قريباً');
            }
        }
        
        function deleteEmailPermanently(emailId) {
            if (confirm('هل أنت متأكد من الحذف النهائي؟ لن يمكن استعادة الرسالة بعد ذلك!')) {
                // TODO: إضافة API للحذف النهائي
                alert('سيتم إضافة هذه الميزة قريباً');
            }
        }
        
        function emptyTrash() {
            if (confirm('هل أنت متأكد من إفراغ سلة المحذوفات نهائياً؟ سيتم حذف جميع الرسائل نهائياً!')) {
                // TODO: إضافة API لإفراغ سلة المحذوفات
                alert('سيتم إضافة هذه الميزة قريباً');
            }
        }
    </script>
</body>
</html>
