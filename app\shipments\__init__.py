"""
نظام إدارة الشحنات والتتبع المتطور
Advanced Shipment Management & Tracking System

هذا النظام يوفر:
- إدارة شاملة للشحنات
- تتبع مباشر ولحظي للشحنات
- خرائط تفاعلية مع GPS
- إشعارات فورية
- تحليلات متقدمة
- تكامل مع شركات الشحن
- API متطور للتكامل الخارجي
"""

from flask import Blueprint

# إنشاء Blueprint للشحنات
shipments_bp = Blueprint('shipments', __name__, url_prefix='/shipments')

# استيراد الـ routes
from . import routes, models, utils, tracking, notifications, analytics, professional_automation_routes

# بدء معالج الأتمتة التلقائية
try:
    from .automation_processor import automation_processor
    print("🤖 تم تحميل معالج الأتمتة التلقائية")
except Exception as e:
    print(f"⚠️ خطأ في تحميل معالج الأتمتة: {e}")
