{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-project-diagram me-2"></i>
                    {{ title }}
                </h2>
                <div class="d-flex gap-2">
                    <a href="#" onclick="alert('ميزة إنشاء قالب جديد قيد التطوير')" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        قالب جديد
                    </a>
                    <a href="#" onclick="alert('ميزة إعدادات سير العمل قيد التطوير')" class="btn btn-outline-secondary">
                        <i class="fas fa-cog me-2"></i>
                        الإعدادات
                    </a>
                </div>
            </div>

            <!-- Workflow Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">المهام المعلقة</h6>
                                    <h3 class="mb-0">{{ pending_tasks|length }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">المهام المكتملة</h6>
                                    <h3 class="mb-0">{{ completed_tasks|length }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">قوالب سير العمل</h6>
                                    <h3 class="mb-0">{{ workflow_templates|length }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-sitemap fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">العمليات النشطة</h6>
                                    <h3 class="mb-0">{{ active_workflows|length }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-play-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                إجراءات سريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-2">
                                    <a href="#" onclick="alert('ميزة المهام المعلقة قيد التطوير')" class="btn btn-outline-primary w-100 mb-2">
                                        <i class="fas fa-tasks me-2"></i>
                                        المهام المعلقة
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a href="#" onclick="alert('ميزة قوالب سير العمل قيد التطوير')" class="btn btn-outline-success w-100 mb-2">
                                        <i class="fas fa-sitemap me-2"></i>
                                        القوالب
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a href="#" onclick="alert('ميزة العمليات النشطة قيد التطوير')" class="btn btn-outline-warning w-100 mb-2">
                                        <i class="fas fa-play-circle me-2"></i>
                                        العمليات النشطة
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a href="#" onclick="alert('ميزة التقارير قيد التطوير')" class="btn btn-outline-info w-100 mb-2">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        التقارير
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a href="#" onclick="alert('ميزة الموافقات قيد التطوير')" class="btn btn-outline-danger w-100 mb-2">
                                        <i class="fas fa-stamp me-2"></i>
                                        الموافقات
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a href="#" onclick="alert('ميزة الإعدادات قيد التطوير')" class="btn btn-outline-secondary w-100 mb-2">
                                        <i class="fas fa-cog me-2"></i>
                                        الإعدادات
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Tasks -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>
                                المهام المعلقة
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if pending_tasks %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>اسم الخطوة</th>
                                                <th>الدور المطلوب</th>
                                                <th>المهلة الزمنية</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for task in pending_tasks %}
                                                <tr>
                                                    <td>{{ task.step_name }}</td>
                                                    <td>
                                                        {% if task.approver_role %}
                                                            <span class="badge bg-info">{{ task.approver_role }}</span>
                                                        {% else %}
                                                            <span class="text-muted">غير محدد</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        {% if task.timeout_hours %}
                                                            {{ task.timeout_hours }} ساعة
                                                        {% else %}
                                                            <span class="text-muted">غير محدد</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-outline-success" onclick="alert('ميزة الموافقة قيد التطوير')" title="موافقة">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                            <button class="btn btn-outline-danger" onclick="alert('ميزة الرفض قيد التطوير')" title="رفض">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                            <button class="btn btn-outline-info" onclick="alert('ميزة التفاصيل قيد التطوير')" title="تفاصيل">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="text-center py-3">
                                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                    <p class="text-muted">لا توجد مهام معلقة</p>
                                    <small class="text-muted">جميع المهام المخصصة لك مكتملة</small>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-sitemap me-2"></i>
                                قوالب سير العمل
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if workflow_templates %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>اسم القالب</th>
                                                <th>النوع</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for template in workflow_templates %}
                                                <tr>
                                                    <td>{{ template.name }}</td>
                                                    <td>
                                                        {% if template.workflow_type == 'purchase_request' %}
                                                            <span class="badge bg-primary">طلب شراء</span>
                                                        {% elif template.workflow_type == 'expense_approval' %}
                                                            <span class="badge bg-warning">موافقة مصروف</span>
                                                        {% elif template.workflow_type == 'supplier_approval' %}
                                                            <span class="badge bg-info">موافقة مورد</span>
                                                        {% else %}
                                                            <span class="badge bg-secondary">{{ template.workflow_type }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        {% if template.is_active %}
                                                            <span class="badge bg-success">نشط</span>
                                                        {% else %}
                                                            <span class="badge bg-secondary">غير نشط</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-outline-primary" onclick="alert('ميزة عرض القالب قيد التطوير')" title="عرض">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button class="btn btn-outline-secondary" onclick="alert('ميزة تعديل القالب قيد التطوير')" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="text-center py-3">
                                    <i class="fas fa-sitemap fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">لا توجد قوالب سير عمل</p>
                                    <button class="btn btn-sm btn-primary" onclick="alert('ميزة إنشاء قالب جديد قيد التطوير')">
                                        إنشاء قالب جديد
                                    </button>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Workflows -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-play-circle me-2"></i>
                                العمليات النشطة
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if active_workflows %}
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>عنوان العملية</th>
                                                <th>النوع</th>
                                                <th>الحالة</th>
                                                <th>تاريخ البدء</th>
                                                <th>المرحلة الحالية</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for workflow in active_workflows %}
                                                <tr>
                                                    <td>{{ workflow.title }}</td>
                                                    <td>
                                                        {% if workflow.workflow_type == 'purchase_request' %}
                                                            <span class="badge bg-primary">طلب شراء</span>
                                                        {% elif workflow.workflow_type == 'expense_approval' %}
                                                            <span class="badge bg-warning">موافقة مصروف</span>
                                                        {% else %}
                                                            <span class="badge bg-secondary">{{ workflow.workflow_type }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        {% if workflow.status == 'active' %}
                                                            <span class="badge bg-success">نشط</span>
                                                        {% elif workflow.status == 'pending' %}
                                                            <span class="badge bg-warning">في الانتظار</span>
                                                        {% elif workflow.status == 'completed' %}
                                                            <span class="badge bg-info">مكتمل</span>
                                                        {% else %}
                                                            <span class="badge bg-secondary">{{ workflow.status }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ workflow.created_at.strftime('%Y-%m-%d') if workflow.created_at else '-' }}</td>
                                                    <td>{{ workflow.current_step or 'غير محدد' }}</td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-outline-primary" onclick="alert('ميزة عرض العملية قيد التطوير')" title="عرض">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button class="btn btn-outline-info" onclick="alert('ميزة تتبع العملية قيد التطوير')" title="تتبع">
                                                                <i class="fas fa-route"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="text-center py-5">
                                    <i class="fas fa-play-circle fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد عمليات نشطة</h5>
                                    <p class="text-muted">جميع العمليات مكتملة أو لم يتم بدء أي عملية بعد</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
