-- =====================================================
-- نظام الترقيم الذكي لأوامر الشراء
-- Smart Purchase Order Numbering System
-- =====================================================
-- 
-- المشكلة: النظام الحالي يستخدم Oracle Sequence مما يؤدي إلى:
-- 1. فجوات في الترقيم عند الحذف
-- 2. عدم إعادة استخدام الأرقام المحذوفة
-- 3. عشوائية في الترقيم
-- 
-- الحل: نظام ترقيم ذكي يعيد استخدام الأرقام المحذوفة
-- =====================================================

-- 1. إنشاء دالة للبحث عن أصغر رقم متاح
CREATE OR REPLACE FUNCTION GET_NEXT_AVAILABLE_PO_ID RETURN NUMBER IS
    v_next_id NUMBER;
    v_max_id NUMBER;
BEGIN
    -- الحصول على أكبر رقم موجود
    SELECT NVL(MAX(id), 0) INTO v_max_id FROM PURCHASE_ORDERS;
    
    -- إذا كان الجدول فارغ، ابدأ من 1
    IF v_max_id = 0 THEN
        RETURN 1;
    END IF;
    
    -- البحث عن أصغر فجوة في الترقيم
    SELECT MIN(missing_id) INTO v_next_id
    FROM (
        -- إنشاء سلسلة من 1 إلى أكبر رقم + 1
        SELECT LEVEL AS missing_id
        FROM DUAL
        CONNECT BY LEVEL <= v_max_id + 1
        MINUS
        -- طرح الأرقام الموجودة فعلاً
        SELECT id FROM PURCHASE_ORDERS
    );
    
    -- إذا لم توجد فجوات، استخدم الرقم التالي
    IF v_next_id IS NULL THEN
        v_next_id := v_max_id + 1;
    END IF;
    
    RETURN v_next_id;
    
EXCEPTION
    WHEN OTHERS THEN
        -- في حالة حدوث خطأ، استخدم الطريقة التقليدية
        SELECT NVL(MAX(id), 0) + 1 INTO v_next_id FROM PURCHASE_ORDERS;
        RETURN v_next_id;
END;
/

-- 2. إنشاء دالة محسنة للأداء (للجداول الكبيرة)
CREATE OR REPLACE FUNCTION GET_SMART_PO_ID_OPTIMIZED RETURN NUMBER IS
    v_next_id NUMBER;
    v_count NUMBER;
BEGIN
    -- فحص عدد السجلات لتحديد الطريقة المناسبة
    SELECT COUNT(*) INTO v_count FROM PURCHASE_ORDERS;
    
    -- للجداول الصغيرة (أقل من 1000 سجل)
    IF v_count < 1000 THEN
        RETURN GET_NEXT_AVAILABLE_PO_ID();
    END IF;
    
    -- للجداول الكبيرة: البحث المحدود عن الفجوات
    BEGIN
        -- البحث عن فجوة في أول 100 رقم
        SELECT MIN(gap_id) INTO v_next_id
        FROM (
            SELECT id + 1 AS gap_id
            FROM PURCHASE_ORDERS
            WHERE id <= 100
            AND id + 1 NOT IN (
                SELECT id FROM PURCHASE_ORDERS WHERE id <= 101
            )
            AND ROWNUM = 1
        );
        
        -- إذا لم توجد فجوة في أول 100، استخدم الرقم التالي
        IF v_next_id IS NULL THEN
            SELECT NVL(MAX(id), 0) + 1 INTO v_next_id FROM PURCHASE_ORDERS;
        END IF;
        
        RETURN v_next_id;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- في حالة الخطأ، استخدم الطريقة التقليدية
            SELECT NVL(MAX(id), 0) + 1 INTO v_next_id FROM PURCHASE_ORDERS;
            RETURN v_next_id;
    END;
END;
/

-- 3. تعديل الـ Trigger ليستخدم النظام الذكي
CREATE OR REPLACE TRIGGER PURCHASE_ORDERS_SMART_TRG
    BEFORE INSERT ON PURCHASE_ORDERS
    FOR EACH ROW
BEGIN
    -- إذا لم يتم تحديد ID، استخدم النظام الذكي
    IF :NEW.id IS NULL THEN
        :NEW.id := GET_NEXT_AVAILABLE_PO_ID();
    END IF;
    
    -- تسجيل العملية للمراجعة
    INSERT INTO SYSTEM_LOGS (
        log_type, 
        table_name, 
        operation, 
        record_id, 
        log_message, 
        created_at
    ) VALUES (
        'SMART_NUMBERING',
        'PURCHASE_ORDERS',
        'INSERT',
        :NEW.id,
        'تم تعيين رقم ذكي: ' || :NEW.id,
        SYSDATE
    );
    
EXCEPTION
    WHEN OTHERS THEN
        -- في حالة فشل النظام الذكي، استخدم الطريقة التقليدية
        IF :NEW.id IS NULL THEN
            :NEW.id := PURCHASE_ORDERS_SEQ.NEXTVAL;
        END IF;
END;
/

-- 4. إنشاء جدول لتسجيل عمليات الترقيم (اختياري)
CREATE TABLE PO_NUMBERING_LOG (
    id NUMBER PRIMARY KEY,
    po_id NUMBER NOT NULL,
    operation_type VARCHAR2(20) NOT NULL, -- INSERT, DELETE, REUSE
    old_id NUMBER,
    new_id NUMBER,
    operation_date DATE DEFAULT SYSDATE,
    notes VARCHAR2(500)
);

-- إنشاء sequence للـ log
CREATE SEQUENCE PO_NUMBERING_LOG_SEQ START WITH 1 INCREMENT BY 1;

-- 5. إنشاء trigger للـ log
CREATE OR REPLACE TRIGGER PO_NUMBERING_LOG_TRG
    BEFORE INSERT ON PO_NUMBERING_LOG
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := PO_NUMBERING_LOG_SEQ.NEXTVAL;
    END IF;
END;
/

-- 6. دالة لإعادة تنظيم الأرقام الموجودة (اختياري - للصيانة)
CREATE OR REPLACE PROCEDURE REORGANIZE_PO_NUMBERS AS
    CURSOR po_cursor IS
        SELECT id, ROWNUM as new_id
        FROM (SELECT id FROM PURCHASE_ORDERS ORDER BY id);
    
    v_updates_count NUMBER := 0;
BEGIN
    -- تعطيل الـ trigger مؤقتاً
    EXECUTE IMMEDIATE 'ALTER TRIGGER PURCHASE_ORDERS_SMART_TRG DISABLE';
    
    -- إعادة ترقيم السجلات
    FOR po_rec IN po_cursor LOOP
        IF po_rec.id != po_rec.new_id THEN
            UPDATE PURCHASE_ORDERS 
            SET id = po_rec.new_id 
            WHERE id = po_rec.id;
            
            v_updates_count := v_updates_count + 1;
            
            -- تسجيل العملية
            INSERT INTO PO_NUMBERING_LOG (
                po_id, operation_type, old_id, new_id, notes
            ) VALUES (
                po_rec.new_id, 'REORGANIZE', po_rec.id, po_rec.new_id,
                'إعادة تنظيم الترقيم'
            );
        END IF;
    END LOOP;
    
    -- إعادة تفعيل الـ trigger
    EXECUTE IMMEDIATE 'ALTER TRIGGER PURCHASE_ORDERS_SMART_TRG ENABLE';
    
    COMMIT;
    
    DBMS_OUTPUT.PUT_LINE('تم إعادة تنظيم ' || v_updates_count || ' سجل');
    
EXCEPTION
    WHEN OTHERS THEN
        -- إعادة تفعيل الـ trigger في حالة الخطأ
        EXECUTE IMMEDIATE 'ALTER TRIGGER PURCHASE_ORDERS_SMART_TRG ENABLE';
        ROLLBACK;
        RAISE;
END;
/

-- 7. دالة لاختبار النظام
CREATE OR REPLACE PROCEDURE TEST_SMART_NUMBERING AS
    v_test_id NUMBER;
    v_expected_id NUMBER := 1;
BEGIN
    DBMS_OUTPUT.PUT_LINE('🧪 اختبار نظام الترقيم الذكي');
    DBMS_OUTPUT.PUT_LINE('================================');
    
    -- اختبار الحصول على أصغر رقم متاح
    FOR i IN 1..5 LOOP
        v_test_id := GET_NEXT_AVAILABLE_PO_ID();
        DBMS_OUTPUT.PUT_LINE('الرقم المتاح التالي: ' || v_test_id);
        
        -- محاكاة إدراج سجل
        INSERT INTO PURCHASE_ORDERS (id, po_number, total_amount)
        VALUES (v_test_id, 'TEST-' || v_test_id, 1000);
    END LOOP;
    
    DBMS_OUTPUT.PUT_LINE('✅ تم إنشاء 5 أوامر شراء تجريبية');
    
    -- حذف أمر في المنتصف
    DELETE FROM PURCHASE_ORDERS WHERE po_number = 'TEST-3';
    DBMS_OUTPUT.PUT_LINE('🗑️ تم حذف أمر الشراء رقم 3');
    
    -- اختبار إعادة الاستخدام
    v_test_id := GET_NEXT_AVAILABLE_PO_ID();
    DBMS_OUTPUT.PUT_LINE('الرقم التالي بعد الحذف: ' || v_test_id || ' (يجب أن يكون 3)');
    
    -- تنظيف البيانات التجريبية
    DELETE FROM PURCHASE_ORDERS WHERE po_number LIKE 'TEST-%';
    COMMIT;
    
    DBMS_OUTPUT.PUT_LINE('🧹 تم تنظيف البيانات التجريبية');
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        DBMS_OUTPUT.PUT_LINE('❌ خطأ في الاختبار: ' || SQLERRM);
END;
/

-- 8. رسائل النجاح
SELECT '✅ تم إنشاء نظام الترقيم الذكي بنجاح!' as status FROM dual;
SELECT '🎯 الآن سيتم إعادة استخدام الأرقام المحذوفة تلقائياً' as info FROM dual;
SELECT '🔧 لاختبار النظام، شغل: EXEC TEST_SMART_NUMBERING;' as test_command FROM dual;

COMMIT;
