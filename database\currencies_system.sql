-- نظام إدارة العملات
-- Currency Management System

-- 1. إنشاء جدول العملات
CREATE TABLE currencies (
    id NUMBER DEFAULT currencies_seq.NEXTVAL PRIMARY KEY,
    code VARCHAR2(3) UNIQUE NOT NULL,
    name_ar VARCHAR2(100) NOT NULL,
    name_en VARCHAR2(100) NOT NULL,
    symbol VARCHAR2(10) NOT NULL,
    exchange_rate NUMBER(15,6) DEFAULT 1.000000,
    is_base_currency NUMBER(1) DEFAULT 0,
    is_active NUMBER(1) DEFAULT 1,
    decimal_places NUMBER(2) DEFAULT 2,
    position VARCHAR2(10) DEFAULT 'before', -- before/after symbol position
    thousands_separator VARCHAR2(5) DEFAULT ',',
    decimal_separator VARCHAR2(5) DEFAULT '.',
    created_at DATE DEFAULT SYSDATE,
    updated_at DATE DEFAULT SYSDATE,
    created_by NUMBER,
    updated_by NUMBER,
    
    CONSTRAINT chk_currency_base CHECK (is_base_currency IN (0, 1)),
    CONSTRAINT chk_currency_active CHECK (is_active IN (0, 1)),
    CONSTRAINT chk_currency_decimal_places CHECK (decimal_places BETWEEN 0 AND 6),
    CONSTRAINT chk_currency_position CHECK (position IN ('before', 'after'))
);

-- 2. إنشاء sequence للعملات
CREATE SEQUENCE currencies_seq START WITH 1 INCREMENT BY 1;

-- 3. إنشاء جدول تاريخ أسعار الصرف
CREATE TABLE currency_exchange_rates (
    id NUMBER DEFAULT currency_exchange_rates_seq.NEXTVAL PRIMARY KEY,
    currency_id NUMBER NOT NULL,
    base_currency_id NUMBER NOT NULL,
    exchange_rate NUMBER(15,6) NOT NULL,
    rate_date DATE DEFAULT SYSDATE,
    source VARCHAR2(100), -- manual, api, bank
    notes CLOB,
    created_at DATE DEFAULT SYSDATE,
    created_by NUMBER,
    
    CONSTRAINT fk_currency_rate_currency FOREIGN KEY (currency_id) 
        REFERENCES currencies(id) ON DELETE CASCADE,
    CONSTRAINT fk_currency_rate_base FOREIGN KEY (base_currency_id) 
        REFERENCES currencies(id) ON DELETE CASCADE
);

-- 4. إنشاء sequence لأسعار الصرف
CREATE SEQUENCE currency_exchange_rates_seq START WITH 1 INCREMENT BY 1;

-- 5. إنشاء فهارس للأداء
CREATE INDEX idx_currencies_code ON currencies(code);
CREATE INDEX idx_currencies_active ON currencies(is_active);
CREATE INDEX idx_currencies_base ON currencies(is_base_currency);
CREATE INDEX idx_exchange_rates_currency ON currency_exchange_rates(currency_id);
CREATE INDEX idx_exchange_rates_date ON currency_exchange_rates(rate_date);

-- 6. إدراج البيانات الأساسية للعملات الشائعة
INSERT INTO currencies (code, name_ar, name_en, symbol, exchange_rate, is_base_currency, is_active, decimal_places, position) VALUES
('SAR', 'ريال سعودي', 'Saudi Riyal', 'ر.س', 1.000000, 1, 1, 2, 'after');

INSERT INTO currencies (code, name_ar, name_en, symbol, exchange_rate, is_base_currency, is_active, decimal_places, position) VALUES
('USD', 'دولار أمريكي', 'US Dollar', '$', 3.750000, 0, 1, 2, 'before');

INSERT INTO currencies (code, name_ar, name_en, symbol, exchange_rate, is_base_currency, is_active, decimal_places, position) VALUES
('EUR', 'يورو', 'Euro', '€', 4.100000, 0, 1, 2, 'before');

INSERT INTO currencies (code, name_ar, name_en, symbol, exchange_rate, is_base_currency, is_active, decimal_places, position) VALUES
('AED', 'درهم إماراتي', 'UAE Dirham', 'د.إ', 1.020000, 0, 1, 2, 'after');

INSERT INTO currencies (code, name_ar, name_en, symbol, exchange_rate, is_base_currency, is_active, decimal_places, position) VALUES
('KWD', 'دينار كويتي', 'Kuwaiti Dinar', 'د.ك', 12.250000, 0, 1, 3, 'after');

INSERT INTO currencies (code, name_ar, name_en, symbol, exchange_rate, is_base_currency, is_active, decimal_places, position) VALUES
('BHD', 'دينار بحريني', 'Bahraini Dinar', 'د.ب', 9.950000, 0, 1, 3, 'after');

INSERT INTO currencies (code, name_ar, name_en, symbol, exchange_rate, is_base_currency, is_active, decimal_places, position) VALUES
('QAR', 'ريال قطري', 'Qatari Riyal', 'ر.ق', 1.030000, 0, 1, 2, 'after');

INSERT INTO currencies (code, name_ar, name_en, symbol, exchange_rate, is_base_currency, is_active, decimal_places, position) VALUES
('OMR', 'ريال عماني', 'Omani Rial', 'ر.ع', 9.750000, 0, 1, 3, 'after');

INSERT INTO currencies (code, name_ar, name_en, symbol, exchange_rate, is_base_currency, is_active, decimal_places, position) VALUES
('JOD', 'دينار أردني', 'Jordanian Dinar', 'د.أ', 5.290000, 0, 1, 3, 'after');

INSERT INTO currencies (code, name_ar, name_en, symbol, exchange_rate, is_base_currency, is_active, decimal_places, position) VALUES
('EGP', 'جنيه مصري', 'Egyptian Pound', 'ج.م', 0.120000, 0, 1, 2, 'after');

INSERT INTO currencies (code, name_ar, name_en, symbol, exchange_rate, is_base_currency, is_active, decimal_places, position) VALUES
('GBP', 'جنيه إسترليني', 'British Pound', '£', 4.750000, 0, 1, 2, 'before');

INSERT INTO currencies (code, name_ar, name_en, symbol, exchange_rate, is_base_currency, is_active, decimal_places, position) VALUES
('JPY', 'ين ياباني', 'Japanese Yen', '¥', 0.025000, 0, 1, 0, 'before');

INSERT INTO currencies (code, name_ar, name_en, symbol, exchange_rate, is_base_currency, is_active, decimal_places, position) VALUES
('CNY', 'يوان صيني', 'Chinese Yuan', '¥', 0.520000, 0, 1, 2, 'before');

-- 7. إدراج أسعار الصرف الأولية
INSERT INTO currency_exchange_rates (currency_id, base_currency_id, exchange_rate, source, notes)
SELECT c1.id, c2.id, c1.exchange_rate, 'manual', 'سعر صرف أولي'
FROM currencies c1, currencies c2
WHERE c1.code != 'SAR' AND c2.code = 'SAR';

-- 8. إنشاء trigger لتحديث تاريخ التعديل
CREATE OR REPLACE TRIGGER trg_currencies_updated_at
    BEFORE UPDATE ON currencies
    FOR EACH ROW
BEGIN
    :NEW.updated_at := SYSDATE;
END;
/

-- 9. إنشاء view لعرض العملات مع آخر أسعار الصرف
CREATE OR REPLACE VIEW v_currencies_with_rates AS
SELECT 
    c.id,
    c.code,
    c.name_ar,
    c.name_en,
    c.symbol,
    c.exchange_rate,
    c.is_base_currency,
    c.is_active,
    c.decimal_places,
    c.position,
    c.thousands_separator,
    c.decimal_separator,
    c.created_at,
    c.updated_at,
    cer.rate_date as last_rate_update,
    cer.source as rate_source
FROM currencies c
LEFT JOIN (
    SELECT 
        currency_id,
        exchange_rate,
        rate_date,
        source,
        ROW_NUMBER() OVER (PARTITION BY currency_id ORDER BY rate_date DESC) as rn
    FROM currency_exchange_rates
) cer ON c.id = cer.currency_id AND cer.rn = 1
ORDER BY c.is_base_currency DESC, c.code;

-- 10. إنشاء function لتحويل العملات
CREATE OR REPLACE FUNCTION convert_currency(
    p_amount IN NUMBER,
    p_from_currency IN VARCHAR2,
    p_to_currency IN VARCHAR2
) RETURN NUMBER IS
    v_from_rate NUMBER;
    v_to_rate NUMBER;
    v_result NUMBER;
BEGIN
    -- الحصول على سعر صرف العملة المصدر
    SELECT exchange_rate INTO v_from_rate
    FROM currencies
    WHERE code = p_from_currency AND is_active = 1;
    
    -- الحصول على سعر صرف العملة الهدف
    SELECT exchange_rate INTO v_to_rate
    FROM currencies
    WHERE code = p_to_currency AND is_active = 1;
    
    -- حساب التحويل
    v_result := (p_amount / v_from_rate) * v_to_rate;
    
    RETURN v_result;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN NULL;
    WHEN OTHERS THEN
        RETURN NULL;
END convert_currency;
/

-- 11. إنشاء function لتنسيق العملة
CREATE OR REPLACE FUNCTION format_currency(
    p_amount IN NUMBER,
    p_currency_code IN VARCHAR2
) RETURN VARCHAR2 IS
    v_symbol VARCHAR2(10);
    v_position VARCHAR2(10);
    v_decimal_places NUMBER;
    v_thousands_separator VARCHAR2(5);
    v_decimal_separator VARCHAR2(5);
    v_formatted_amount VARCHAR2(100);
BEGIN
    -- الحصول على معلومات العملة
    SELECT symbol, position, decimal_places, thousands_separator, decimal_separator
    INTO v_symbol, v_position, v_decimal_places, v_thousands_separator, v_decimal_separator
    FROM currencies
    WHERE code = p_currency_code AND is_active = 1;
    
    -- تنسيق المبلغ
    v_formatted_amount := TO_CHAR(p_amount, 
        'FM999' || v_thousands_separator || '999' || v_thousands_separator || '990' ||
        CASE WHEN v_decimal_places > 0 THEN v_decimal_separator || RPAD('0', v_decimal_places, '0') ELSE '' END
    );
    
    -- إضافة رمز العملة
    IF v_position = 'before' THEN
        RETURN v_symbol || ' ' || v_formatted_amount;
    ELSE
        RETURN v_formatted_amount || ' ' || v_symbol;
    END IF;
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN TO_CHAR(p_amount, 'FM999,999,990.00');
    WHEN OTHERS THEN
        RETURN TO_CHAR(p_amount, 'FM999,999,990.00');
END format_currency;
/

COMMIT;
