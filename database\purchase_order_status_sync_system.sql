-- ===============================================
-- نظام مزامنة حالات أوامر الشراء مع الشحنات
-- Purchase Order Status Synchronization System
-- ===============================================

-- 1. إنشاء جدول ربط حالات الشحنات بحالات أوامر الشراء
CREATE TABLE po_shipment_status_map (
    id NUMBER PRIMARY KEY,
    shipment_status VARCHAR2(50) NOT NULL,
    po_status VARCHAR2(50) NOT NULL,
    auto_update NUMBER(1) DEFAULT 1,
    priority_order NUMBER DEFAULT 0,
    description_ar VARCHAR2(200),
    description_en VARCHAR2(200),
    created_at DATE DEFAULT SYSDATE,
    updated_at DATE DEFAULT SYSDATE,
    is_active NUMBER(1) DEFAULT 1,
    CONSTRAINT fk_map_ship_status FOREIGN KEY (shipment_status)
        REFERENCES shipment_status_config(status_code),
    CONSTRAINT uk_po_status_map UNIQUE (shipment_status, po_status)
);

-- 2. إنشاء sequence للجدول
CREATE SEQUENCE po_status_map_seq START WITH 1 INCREMENT BY 1;

-- 3. إنشاء trigger لتعيين ID تلقائياً
CREATE OR REPLACE TRIGGER trg_po_map_id
    BEFORE INSERT ON po_shipment_status_map
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := po_status_map_seq.NEXTVAL;
    END IF;
END;
/

-- 4. إدراج البيانات الأساسية لربط الحالات
INSERT INTO po_shipment_status_map (
    shipment_status, po_status, auto_update, priority_order,
    description_ar, description_en
) VALUES
('draft', 'مسودة', 1, 1, 'حالة مسودة - لم يتم تأكيد الشحنة بعد', 'Draft status - shipment not confirmed yet');

INSERT INTO po_shipment_status_map (
    shipment_status, po_status, auto_update, priority_order,
    description_ar, description_en
) VALUES
('confirmed', 'مؤكد', 1, 2, 'تم تأكيد الشحنة وأمر الشراء', 'Shipment and purchase order confirmed');

INSERT INTO po_shipment_status_map (
    shipment_status, po_status, auto_update, priority_order,
    description_ar, description_en
) VALUES
('in_transit', 'قيد التنفيذ', 1, 3, 'الشحنة في الطريق - أمر الشراء قيد التنفيذ', 'Shipment in transit - purchase order in progress');

INSERT INTO po_shipment_status_map (
    shipment_status, po_status, auto_update, priority_order,
    description_ar, description_en
) VALUES
('arrived_port', 'قيد التنفيذ', 1, 4, 'وصلت الشحنة للميناء - أمر الشراء قيد التنفيذ', 'Shipment arrived at port - purchase order in progress');

INSERT INTO po_shipment_status_map (
    shipment_status, po_status, auto_update, priority_order,
    description_ar, description_en
) VALUES
('customs_clearance', 'قيد التنفيذ', 1, 5, 'الشحنة قيد التخليص الجمركي', 'Shipment under customs clearance');

INSERT INTO po_shipment_status_map (
    shipment_status, po_status, auto_update, priority_order,
    description_ar, description_en
) VALUES
('out_for_delivery', 'قيد التنفيذ', 1, 6, 'الشحنة خارجة للتسليم', 'Shipment out for delivery');

INSERT INTO po_shipment_status_map (
    shipment_status, po_status, auto_update, priority_order,
    description_ar, description_en
) VALUES
('delivered', 'تم التسليم', 1, 7, 'تم تسليم الشحنة وإكمال أمر الشراء', 'Shipment delivered - purchase order completed');

INSERT INTO po_shipment_status_map (
    shipment_status, po_status, auto_update, priority_order,
    description_ar, description_en
) VALUES
('cancelled', 'ملغي', 1, 8, 'تم إلغاء الشحنة وأمر الشراء', 'Shipment and purchase order cancelled');

INSERT INTO po_shipment_status_map (
    shipment_status, po_status, auto_update, priority_order,
    description_ar, description_en
) VALUES
('returned', 'مرتجع', 1, 9, 'تم إرجاع الشحنة', 'Shipment returned');

INSERT INTO po_shipment_status_map (
    shipment_status, po_status, auto_update, priority_order,
    description_ar, description_en
) VALUES
('delayed', 'متأخر', 1, 10, 'الشحنة متأخرة عن الموعد المحدد', 'Shipment delayed');

-- 5. إنشاء جدول تاريخ تحديثات حالات أوامر الشراء
CREATE TABLE po_status_history (
    id NUMBER PRIMARY KEY,
    po_id NUMBER NOT NULL,
    old_status VARCHAR2(50),
    new_status VARCHAR2(50) NOT NULL,
    shipment_id NUMBER,
    shipment_status VARCHAR2(50),
    change_reason VARCHAR2(100) DEFAULT 'تحديث تلقائي من الشحنة',
    changed_by NUMBER,
    changed_at DATE DEFAULT SYSDATE,
    auto_updated NUMBER(1) DEFAULT 1,
    notes CLOB,
    CONSTRAINT fk_po_hist_po_id FOREIGN KEY (po_id)
        REFERENCES PURCHASE_ORDERS(ID),
    CONSTRAINT fk_po_hist_ship_id FOREIGN KEY (shipment_id)
        REFERENCES cargo_shipments(id)
);

-- 6. إنشاء sequence لتاريخ حالات أوامر الشراء
CREATE SEQUENCE po_hist_seq START WITH 1 INCREMENT BY 1;

-- 7. إنشاء trigger لتعيين ID تلقائياً لتاريخ الحالات
CREATE OR REPLACE TRIGGER trg_po_hist_id
    BEFORE INSERT ON po_status_history
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := po_hist_seq.NEXTVAL;
    END IF;
END;
/

-- 8. إنشاء الـ Trigger الرئيسي لتحديث حالة أمر الشراء تلقائياً
CREATE OR REPLACE TRIGGER trg_auto_update_po_status
    AFTER UPDATE OF shipment_status ON cargo_shipments
    FOR EACH ROW
WHEN (NEW.purchase_order_id IS NOT NULL AND OLD.shipment_status != NEW.shipment_status)
DECLARE
    v_new_po_status VARCHAR2(50);
    v_old_po_status VARCHAR2(50);
    v_mapping_count NUMBER;
    v_po_exists NUMBER;
BEGIN
    -- التحقق من وجود أمر الشراء
    SELECT COUNT(*) INTO v_po_exists
    FROM PURCHASE_ORDERS
    WHERE ID = :NEW.purchase_order_id;
    
    IF v_po_exists = 0 THEN
        RETURN; -- أمر الشراء غير موجود
    END IF;
    
    -- الحصول على الحالة الحالية لأمر الشراء
    SELECT STATUS INTO v_old_po_status
    FROM PURCHASE_ORDERS
    WHERE ID = :NEW.purchase_order_id;
    
    -- التحقق من وجود ربط للحالة الجديدة
    SELECT COUNT(*) INTO v_mapping_count
    FROM po_shipment_status_map
    WHERE shipment_status = :NEW.shipment_status
    AND auto_update = 1
    AND is_active = 1;

    IF v_mapping_count > 0 THEN
        -- الحصول على حالة أمر الشراء المقابلة
        SELECT po_status INTO v_new_po_status
        FROM po_shipment_status_map
        WHERE shipment_status = :NEW.shipment_status
        AND auto_update = 1
        AND is_active = 1
        ORDER BY priority_order
        FETCH FIRST 1 ROWS ONLY;
        
        -- تحديث حالة أمر الشراء إذا كانت مختلفة
        IF v_old_po_status != v_new_po_status THEN
            UPDATE PURCHASE_ORDERS
            SET STATUS = v_new_po_status,
                UPDATED_AT = SYSDATE
            WHERE ID = :NEW.purchase_order_id;
            
            -- إضافة سجل في تاريخ التحديثات
            INSERT INTO po_status_history (
                po_id, old_status, new_status,
                shipment_id, shipment_status, change_reason,
                changed_by, auto_updated, notes
            ) VALUES (
                :NEW.purchase_order_id, v_old_po_status, v_new_po_status,
                :NEW.id, :NEW.shipment_status, 'تحديث تلقائي من الشحنة',
                :NEW.status_updated_by, 1,
                'تم تحديث حالة أمر الشراء تلقائياً من ' || v_old_po_status || ' إلى ' || v_new_po_status || ' بناءً على حالة الشحنة: ' || :NEW.shipment_status
            );
        END IF;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        -- تسجيل الخطأ دون إيقاف العملية
        INSERT INTO system_logs (log_level, message, created_at)
        VALUES ('ERROR', 'خطأ في تحديث حالة أمر الشراء: ' || SQLERRM, SYSDATE);
END;
/

-- 9. إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_po_map_ship ON po_shipment_status_map(shipment_status);
CREATE INDEX idx_po_map_po ON po_shipment_status_map(po_status);
CREATE INDEX idx_po_hist_po_id ON po_status_history(po_id);
CREATE INDEX idx_po_hist_date ON po_status_history(changed_at);

-- 10. إنشاء view لعرض حالات أوامر الشراء مع الشحنات
CREATE OR REPLACE VIEW v_po_shipment_status AS
SELECT
    po.ID as purchase_order_id,
    po.PO_NUMBER,
    po.STATUS as current_po_status,
    cs.id as shipment_id,
    cs.tracking_number,
    cs.shipment_status as current_shipment_status,
    ssc.status_name_ar as shipment_status_name,
    ssc.status_color as shipment_status_color,
    mapping.po_status as expected_po_status,
    CASE
        WHEN po.STATUS = mapping.po_status THEN 'متزامن'
        ELSE 'غير متزامن'
    END as sync_status,
    po.UPDATED_AT as po_last_updated,
    cs.status_updated_at as shipment_last_updated
FROM PURCHASE_ORDERS po
LEFT JOIN cargo_shipments cs ON po.ID = cs.purchase_order_id
LEFT JOIN shipment_status_config ssc ON cs.shipment_status = ssc.status_code
LEFT JOIN po_shipment_status_map mapping ON cs.shipment_status = mapping.shipment_status
WHERE mapping.auto_update = 1 AND mapping.is_active = 1;

COMMIT;

-- رسالة نجاح
SELECT 'تم إنشاء نظام مزامنة حالات أوامر الشراء بنجاح!' as result FROM dual;
