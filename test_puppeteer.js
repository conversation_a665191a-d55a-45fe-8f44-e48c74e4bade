
const puppeteer = require('puppeteer-core');

(async () => {
  try {
    const browser = await puppeteer.launch({
      executablePath: 'C:/Program Files/Google/Chrome/Application/chrome.exe',
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--ignore-certificate-errors',
        '--ignore-ssl-errors',
        '--allow-running-insecure-content',
        '--disable-features=VizDisplayCompositor'
      ],
      timeout: 60000
    });

    const page = await browser.newPage();
    
    // تعيين timeout أطول
    page.setDefaultTimeout(60000);
    page.setDefaultNavigationTimeout(60000);
    
    // تجاهل أخطاء SSL
    await page.setExtraHTTPHeaders({
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
    });
    
    console.log(' Puppeteer معد بنجاح');
    
    await browser.close();
    
  } catch (error) {
    console.error(' خطأ في Puppeteer:', error.message);
    process.exit(1);
  }
})();
