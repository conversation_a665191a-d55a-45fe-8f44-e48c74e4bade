#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الحصول على OneDrive Token
Get OneDrive Token
"""

import json
import requests

def main():
    print('🔄 تبديل الكود بـ Access Token...')

    # إعدادات OneDrive
    client_id = 'bc80f97a-588b-4a29-bf5b-28f8bf8bdf0f'
    client_secret = '****************************************'
    redirect_uri = 'https://sas.alfogehi.net:5000/auth/onedrive/callback'
    auth_code = 'M.C547_BAY.2.U.569b0103-e550-1dce-dd94-fb7fad2e19d0'

    # طلب Token
    token_url = 'https://login.microsoftonline.com/common/oauth2/v2.0/token'
    data = {
        'client_id': client_id,
        'client_secret': client_secret,
        'code': auth_code,
        'redirect_uri': redirect_uri,
        'grant_type': 'authorization_code'
    }

    try:
        response = requests.post(token_url, data=data)
        print(f'📊 استجابة الخادم: {response.status_code}')
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get('access_token')
            
            if access_token:
                print('✅ تم الحصول على Access Token!')
                print(f'🔑 Token: {access_token[:50]}...')
                
                # اختبار Token
                print('🧪 اختبار الاتصال بـ OneDrive...')
                headers = {"Authorization": f"Bearer {access_token}"}
                test_response = requests.get(
                    'https://graph.microsoft.com/v1.0/me/drive',
                    headers=headers
                )
                
                if test_response.status_code == 200:
                    drive_info = test_response.json()
                    print('🎉 نجح الاتصال بـ OneDrive!')
                    owner_name = drive_info.get('owner', {}).get('user', {}).get('displayName', 'غير محدد')
                    print(f'📁 المالك: {owner_name}')
                    
                    # حفظ Token
                    with open('onedrive_token.json', 'w', encoding='utf-8') as f:
                        json.dump(token_data, f, indent=2, ensure_ascii=False)
                    
                    print('💾 تم حفظ Token في onedrive_token.json')
                    print('✅ OneDrive جاهز للاستخدام!')
                    
                    return True
                    
                else:
                    print(f'❌ فشل اختبار Token: {test_response.status_code}')
                    print(test_response.text)
            else:
                print('❌ لم يتم الحصول على Access Token')
                print(response.text)
        else:
            print(f'❌ فشل في الحصول على Token: {response.status_code}')
            print(response.text)
            
    except Exception as e:
        print(f'❌ خطأ: {e}')
    
    return False

if __name__ == "__main__":
    if main():
        print("\n🎯 الخطوة التالية:")
        print("قم بتحديث إعدادات النظام لاستخدام Token الجديد")
    else:
        print("\n❌ فشل في إعداد OneDrive")
