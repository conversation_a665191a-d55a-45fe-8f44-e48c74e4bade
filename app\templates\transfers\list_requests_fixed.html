<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة طلبات الحوالات - نظام الفوجي</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    
    <style>
        :root {
            --primary: #2c3e50;
            --secondary: #3498db;
            --success: #27ae60;
            --warning: #f39c12;
            --danger: #e74c3c;
            --info: #17a2b8;
            --light: #f8f9fa;
            --dark: #343a40;
            --surface: #ffffff;
            --border: #dee2e6;
            --text-primary: #2c3e50;
            --text-secondary: #6c757d;
            --text-muted: #adb5bd;
            --radius: 12px;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: var(--text-primary);
        }

        /* Header */
        .page-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-top: 0.5rem;
        }

        /* Breadcrumb */
        .breadcrumb-container {
            background: var(--surface);
            padding: 1rem 0;
            margin-bottom: 2rem;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
        }

        .breadcrumb {
            background: none;
            margin: 0;
            padding: 0;
        }

        .breadcrumb-item a {
            color: var(--secondary);
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb-item a:hover {
            color: var(--primary);
        }

        /* Cards */
        .card-modern {
            background: var(--surface);
            border: none;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .card-header-modern {
            background: linear-gradient(135deg, var(--light) 0%, #e9ecef 100%);
            border-bottom: 1px solid var(--border);
            padding: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Stats Cards */
        .stats-card {
            background: var(--surface);
            border-radius: var(--radius);
            padding: 2rem;
            text-align: center;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            border-left: 4px solid var(--secondary);
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stats-card.success {
            border-left-color: var(--success);
        }

        .stats-card.warning {
            border-left-color: var(--warning);
        }

        .stats-card.danger {
            border-left-color: var(--danger);
        }

        .stats-card.info {
            border-left-color: var(--info);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
            display: block;
        }

        .stats-label {
            font-size: 1rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .stats-icon {
            font-size: 3rem;
            opacity: 0.1;
            position: absolute;
            top: 1rem;
            left: 1rem;
        }

        /* Control Panel */
        .control-panel {
            background: var(--surface);
            border-radius: var(--radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .form-control-modern {
            border: 2px solid var(--border);
            border-radius: var(--radius);
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control-modern:focus {
            border-color: var(--secondary);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .btn-modern {
            border-radius: var(--radius);
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        /* Table */
        .table-container {
            background: var(--surface);
            border-radius: var(--radius);
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .table-modern {
            margin: 0;
            font-size: 0.9rem;
        }

        .table-modern thead th {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            border: none;
            padding: 1rem;
            font-weight: 600;
            text-align: center;
        }

        .table-modern tbody td {
            padding: 1rem;
            border-bottom: 1px solid var(--border);
            vertical-align: middle;
            text-align: center;
        }

        .table-modern tbody tr:hover {
            background: var(--light);
        }

        /* Status Badges */
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending {
            background: #ffeaa7;
            color: #d63031;
        }

        .status-approved {
            background: #55efc4;
            color: #00b894;
        }

        .status-rejected {
            background: #ff7675;
            color: #d63031;
        }

        .status-executed {
            background: #74b9ff;
            color: #0984e3;
        }

        /* Loading */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            background: white;
            padding: 2rem;
            border-radius: var(--radius);
            text-align: center;
        }

        /* Action Buttons */
        .action-btn {
            padding: 0.25rem 0.5rem;
            margin: 0 0.125rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            transform: scale(1.05);
        }

        .btn-view {
            background: var(--info);
            color: white;
        }

        .btn-edit {
            background: var(--warning);
            color: white;
        }

        .btn-delete {
            background: var(--danger);
            color: white;
        }

        .btn-approve {
            background: var(--success);
            color: white;
        }

        .btn-info {
            background: var(--info);
            color: white;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .page-title {
                font-size: 1.8rem;
            }
            
            .stats-card {
                margin-bottom: 1rem;
            }
            
            .control-panel {
                padding: 1rem;
            }
            
            .table-responsive {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid px-3">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="fas fa-exchange-alt me-3"></i>
                        إدارة طلبات الحوالات
                    </h1>
                    <p class="page-subtitle">
                        إدارة شاملة لجميع طلبات الحوالات المالية مع إمكانيات البحث والفلترة المتقدمة
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex justify-content-end gap-2">
                        <button class="btn btn-light btn-modern" onclick="refreshData()">
                            <i class="fas fa-sync-alt me-2"></i>تحديث
                        </button>
                        <a href="/transfers/new-request" class="btn btn-success btn-modern">
                            <i class="fas fa-plus me-2"></i>طلب جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="breadcrumb-container">
        <div class="container-fluid px-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="/dashboard">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="/transfers/dashboard">
                            <i class="fas fa-exchange-alt me-1"></i>
                            نظام الحوالات
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <i class="fas fa-list me-1"></i>
                        قائمة طلبات الحوالات
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container-fluid px-3">
        <!-- Statistics Cards -->
        <div class="row mb-4" id="statsContainer">
            <div class="col-md-3">
                <div class="stats-card success position-relative">
                    <i class="fas fa-file-alt stats-icon"></i>
                    <span class="stats-number" id="totalRequests">0</span>
                    <div class="stats-label">إجمالي الطلبات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card warning position-relative">
                    <i class="fas fa-clock stats-icon"></i>
                    <span class="stats-number" id="pendingRequests">0</span>
                    <div class="stats-label">طلبات معلقة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card info position-relative">
                    <i class="fas fa-check-circle stats-icon"></i>
                    <span class="stats-number" id="approvedRequests">0</span>
                    <div class="stats-label">طلبات معتمدة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card danger position-relative">
                    <i class="fas fa-dollar-sign stats-icon"></i>
                    <span class="stats-number" id="totalAmount">0</span>
                    <div class="stats-label">إجمالي المبالغ</div>
                </div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="control-panel">
            <div class="row align-items-end">
                <div class="col-lg-3 col-md-6 mb-3">
                    <label class="form-label fw-bold">البحث السريع</label>
                    <div class="position-relative">
                        <input type="text" id="searchInput" class="form-control form-control-modern"
                               placeholder="ابحث برقم الطلب أو المستفيد..." autocomplete="off">
                        <div class="position-absolute top-50 end-0 translate-middle-y me-2">
                            <i class="fas fa-search text-muted"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-3">
                    <label class="form-label fw-bold">الحالة</label>
                    <select id="statusFilter" class="form-control form-control-modern">
                        <option value="">جميع الحالات</option>
                        <option value="pending">معلق</option>
                        <option value="approved">معتمد</option>
                        <option value="rejected">مرفوض</option>
                        <option value="executed">منفذ</option>
                    </select>
                </div>
                <div class="col-lg-2 col-md-6 mb-3">
                    <label class="form-label fw-bold">العملة</label>
                    <select id="currencyFilter" class="form-control form-control-modern">
                        <option value="">جميع العملات</option>
                        <option value="USD">دولار أمريكي</option>
                        <option value="EUR">يورو</option>
                        <option value="SAR">ريال سعودي</option>
                        <option value="AED">درهم إماراتي</option>
                    </select>
                </div>
                <div class="col-lg-2 col-md-6 mb-3">
                    <label class="form-label fw-bold">الفرع</label>
                    <select id="branchFilter" class="form-control form-control-modern">
                        <option value="">جميع الفروع</option>
                    </select>
                </div>
                <div class="col-lg-2 col-md-6 mb-3">
                    <label class="form-label fw-bold">الصراف</label>
                    <select id="moneyChangerFilter" class="form-control form-control-modern">
                        <option value="">جميع الصرافين</option>
                    </select>
                </div>
                <div class="col-lg-1 col-md-6 mb-3">
                    <label class="form-label fw-bold">نوع</label>
                    <select id="typeFilter" class="form-control form-control-modern">
                        <option value="">الكل</option>
                        <option value="bank">بنكية</option>
                        <option value="cash">نقدية</option>
                        <option value="money_changer">صراف</option>
                    </select>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-12">
                    <div class="d-flex gap-2 flex-wrap">
                        <button class="btn btn-primary btn-modern" onclick="loadData()">
                            <i class="fas fa-sync-alt me-2"></i>تحديث
                        </button>
                        <button class="btn btn-success btn-modern" onclick="exportToExcel()">
                            <i class="fas fa-file-excel me-2"></i>تصدير
                        </button>
                        <button class="btn btn-info btn-modern" onclick="printTable()">
                            <i class="fas fa-print me-2"></i>طباعة
                        </button>
                        <button class="btn btn-warning btn-modern" onclick="clearFilters()">
                            <i class="fas fa-eraser me-2"></i>مسح الفلاتر
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transfer Requests Table -->
        <div class="card-modern">
            <div class="card-header-modern">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        قائمة طلبات الحوالات
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-warning btn-sm" onclick="bulkApprove()">
                            <i class="fas fa-check me-1"></i>اعتماد جماعي
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="bulkReject()">
                            <i class="fas fa-times me-1"></i>رفض جماعي
                        </button>
                    </div>
                </div>
            </div>
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-modern" id="transferRequestsTable">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>رقم الطلب</th>
                                <th>المستفيد</th>
                                <th>المبلغ</th>
                                <th>العملة</th>
                                <th>الفرع</th>
                                <th>الصراف</th>
                                <th>نوع الحوالة</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="transferRequestsTableBody">
                            <tr>
                                <td colspan="11" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                    <div class="mt-2">جاري تحميل البيانات...</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div>جاري معالجة الطلب...</div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Global variables
        let transferRequestsData = []; console.log('تم تحميل النافذة');
        let filteredData = [];

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            initializeEventListeners();
        });

        // Initialize event listeners
        function initializeEventListeners() {
            // Search input
            document.getElementById('searchInput').addEventListener('input', filterData);
            
            // Filter selects
            document.getElementById('statusFilter').addEventListener('change', filterData);
            document.getElementById('currencyFilter').addEventListener('change', filterData);
            document.getElementById('branchFilter').addEventListener('change', filterData);
            document.getElementById('moneyChangerFilter').addEventListener('change', filterData);
            document.getElementById('typeFilter').addEventListener('change', filterData);
            
            // Select all checkbox
            document.getElementById('selectAll').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
                checkboxes.forEach(cb => cb.checked = this.checked);
            });
        }

        // Load data from API
        // Load data from API
        async function loadData() {
            try {
                console.log('بدء تحميل البيانات...');
                showLoading();
                
                const response = await fetch('/transfers/api/transfer-requests');
                console.log('استجابة الخادم:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('البيانات المستلمة:', data);
                
                if (data.success) {
                    transferRequestsData = data.data || [];
                    filteredData = [...transferRequestsData];
                    console.log('عدد السجلات:', transferRequestsData.length);
                    updateStatistics();
                    updateFilterOptions();
                    renderTable();
                } else {
                    console.error('خطأ في البيانات:', data.message);
                    showError('فشل في تحميل البيانات: ' + (data.message || 'خطأ غير معروف'));
                }
            } catch (error) {
                console.error('خطأ في تحميل البيانات:', error);
                showError('حدث خطأ في تحميل البيانات: ' + error.message);
                
                // عرض بيانات تجريبية للاختبار
                transferRequestsData = [
                    {
                        id: 1,
                        request_number: 'TR-2025-0001',
                        beneficiary_name: 'مستفيد تجريبي',
                        amount: 1000,
                        currency: 'USD',
                        transfer_type: 'bank',
                        status: 'pending',
                        created_at: '2025-09-09 00:16:18'
                    }
                ];
                filteredData = [...transferRequestsData];
                updateStatistics();
                renderTable();
            } finally {
                hideLoading();
            }
        } else {
                    showError('فشل في تحميل البيانات: ' + data.message);
                }
            } catch (error) {
                console.error('Error loading data:', error);
                showError('حدث خطأ في تحميل البيانات');
            } finally {
                hideLoading();
            }
        }

        // Update statistics
        function updateStatistics() {
            const stats = {
                total: transferRequestsData.length,
                pending: transferRequestsData.filter(r => r.status === 'pending').length,
                approved: transferRequestsData.filter(r => r.status === 'approved').length,
                totalAmount: transferRequestsData.reduce((sum, r) => sum + parseFloat(r.amount || 0), 0)
            };

            document.getElementById('totalRequests').textContent = stats.total;
            document.getElementById('pendingRequests').textContent = stats.pending;
            document.getElementById('approvedRequests').textContent = stats.approved;
            document.getElementById('totalAmount').textContent = formatCurrencyEnglish(stats.totalAmount);
        }

        // Update filter options based on loaded data
        function updateFilterOptions() {
            // Update branch filter
            const branches = [...new Set(transferRequestsData.map(r => r.branch_name).filter(b => b && b !== 'غير محدد'))];
            const branchFilter = document.getElementById('branchFilter');
            branchFilter.innerHTML = '<option value="">جميع الفروع</option>';
            branches.forEach(branch => {
                branchFilter.innerHTML += `<option value="${branch}">${branch}</option>`;
            });

            // Update money changer filter
            const moneyChangers = [...new Set(transferRequestsData.map(r => r.money_changer_name).filter(m => m && m !== 'غير محدد'))];
            const moneyChangerFilter = document.getElementById('moneyChangerFilter');
            moneyChangerFilter.innerHTML = '<option value="">جميع الصرافين</option>';
            moneyChangers.forEach(moneyChanger => {
                moneyChangerFilter.innerHTML += `<option value="${moneyChanger}">${moneyChanger}</option>`;
            });
        }

        // Filter data
        function filterData() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const currencyFilter = document.getElementById('currencyFilter').value;
            const branchFilter = document.getElementById('branchFilter').value;
            const moneyChangerFilter = document.getElementById('moneyChangerFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;

            filteredData = transferRequestsData.filter(request => {
                const matchesSearch = !searchTerm ||
                    request.request_number.toLowerCase().includes(searchTerm) ||
                    request.beneficiary_name.toLowerCase().includes(searchTerm);

                const matchesStatus = !statusFilter || request.status === statusFilter;
                const matchesCurrency = !currencyFilter || request.currency === currencyFilter;
                const matchesBranch = !branchFilter || request.branch_name === branchFilter;
                const matchesMoneyChanger = !moneyChangerFilter || request.money_changer_name === moneyChangerFilter;
                const matchesType = !typeFilter || request.transfer_type === typeFilter;

                return matchesSearch && matchesStatus && matchesCurrency && matchesBranch && matchesMoneyChanger && matchesType;
            });

            renderTable();
        }

        // Clear all filters
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('currencyFilter').value = '';
            document.getElementById('branchFilter').value = '';
            document.getElementById('moneyChangerFilter').value = '';
            document.getElementById('typeFilter').value = '';
            filterData();
        }

        // Render table
        function renderTable() {
            const tbody = document.getElementById('transferRequestsTableBody');
            
            if (filteredData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="11" class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <div>لا توجد طلبات حوالات</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredData.map(request => `
                <tr>
                    <td>
                        <input type="checkbox" class="form-check-input" value="${request.id}">
                    </td>
                    <td>
                        <strong class="text-primary">${request.request_number}</strong>
                    </td>
                    <td>${request.beneficiary_name}</td>
                    <td>
                        <strong>${formatCurrencyEnglish(request.amount)}</strong>
                    </td>
                    <td>
                        <span class="badge bg-secondary">${request.currency}</span>
                    </td>
                    <td>${request.branch_name || 'غير محدد'}</td>
                    <td>${request.money_changer_name || 'غير محدد'}</td>
                    <td>${getTransferTypeLabel(request.transfer_type)}</td>
                    <td>
                        <span class="status-badge status-${request.status}">
                            ${getStatusLabel(request.status)}
                        </span>
                    </td>
                    <td>${formatDate(request.created_at)}</td>
                    <td>
                        <div class="d-flex gap-1 flex-wrap">
                            <button class="action-btn btn-view" onclick="viewRequest(${request.id})" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn btn-edit" onclick="editRequest(${request.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn btn-info" onclick="manageDocuments(${request.id})" title="إدارة الوثائق">
                                <i class="fas fa-file-alt"></i>
                            </button>
                            ${request.status === 'pending' ? `
                                <button class="action-btn btn-approve" onclick="approveRequest(${request.id})" title="اعتماد">
                                    <i class="fas fa-check"></i>
                                </button>
                            ` : ''}
                            <button class="action-btn btn-delete" onclick="deleteRequest(${request.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // Helper functions
        function getStatusLabel(status) {
            const labels = {
                'pending': 'معلق',
                'approved': 'معتمد',
                'rejected': 'مرفوض',
                'executed': 'منفذ'
            };
            return labels[status] || status;
        }

        function getTransferTypeLabel(type) {
            const labels = {
                'bank': 'بنكية',
                'cash': 'نقدية',
                'online': 'إلكترونية'
            };
            return labels[type] || type;
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount);
        }

        function formatCurrencyEnglish(amount) {
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount);
        }

        function formatDate(dateString) {
            // تنسيق التاريخ الميلادي بأرقام إنجليزية
            const date = new Date(dateString);
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');

            return `${day}/${month}/${year} ${hours}:${minutes}`;
        }

        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        function showError(message) {
            alert(message); // يمكن استبدالها بنظام إشعارات أفضل
        }

        // Action functions
        function refreshData() {
            loadData();
        }

        function viewRequest(id) {
            window.open(`/transfers/view-request/${id}`, '_blank');
        }

        function editRequest(id) {
            window.location.href = `/transfers/edit-request/${id}`;
        }

        function manageDocuments(id) {
            window.open(`/transfers/requests/${id}/documents`, '_blank');
        }

        function deleteRequest(id) {
            if (confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
                // API call to delete
                console.log('Delete request:', id);
            }
        }

        function approveRequest(id) {
            if (confirm('هل أنت متأكد من اعتماد هذا الطلب؟')) {
                // API call to approve
                console.log('Approve request:', id);
            }
        }

        function bulkApprove() {
            const selected = getSelectedRequests();
            if (selected.length === 0) {
                alert('يرجى اختيار طلبات للاعتماد');
                return;
            }
            if (confirm(`هل أنت متأكد من اعتماد ${selected.length} طلب؟`)) {
                console.log('Bulk approve:', selected);
            }
        }

        function bulkReject() {
            const selected = getSelectedRequests();
            if (selected.length === 0) {
                alert('يرجى اختيار طلبات للرفض');
                return;
            }
            if (confirm(`هل أنت متأكد من رفض ${selected.length} طلب؟`)) {
                console.log('Bulk reject:', selected);
            }
        }

        function getSelectedRequests() {
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        function exportToExcel() {
            console.log('Export to Excel');
            // تنفيذ تصدير Excel
        }

        function printTable() {
            window.print();
        }
    </script>
</body>
</html>
