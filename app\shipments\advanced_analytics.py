#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التقارير والإحصائيات المتقدمة
Advanced Analytics and Reporting System

هذا النظام يوفر:
- لوحة المعلومات التنفيذية
- تقارير الأداء والكفاءة
- التحليلات التنبؤية
- تقارير مالية متقدمة
- مؤشرات الأداء الرئيسية (KPIs)
- تحليل الاتجاهات
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json
import pandas as pd
import numpy as np
from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required
from database_manager import DatabaseManager

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# إنشاء Blueprint للتحليلات
analytics_bp = Blueprint('analytics', __name__, url_prefix='/analytics')

class AdvancedAnalyticsManager:
    """مدير التحليلات والتقارير المتقدمة"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        
        # إعدادات التحليلات
        self.analytics_config = {
            'default_period_days': 30,
            'trend_analysis_periods': [7, 30, 90, 365],
            'performance_thresholds': {
                'excellent': 95,
                'good': 85,
                'average': 70,
                'poor': 50
            },
            'kpi_targets': {
                'on_time_delivery_rate': 95,
                'document_completion_rate': 98,
                'agent_satisfaction_rate': 90,
                'average_completion_days': 3
            }
        }
    
    def get_executive_dashboard_data(self, period_days: int = 30) -> Dict:
        """جلب بيانات لوحة المعلومات التنفيذية"""
        try:
            dashboard_data = {}
            
            # مؤشرات الأداء الرئيسية (KPIs)
            kpis = self._calculate_kpis(period_days)
            dashboard_data['kpis'] = kpis
            
            # إحصائيات الأوامر
            orders_stats = self._get_orders_statistics(period_days)
            dashboard_data['orders_stats'] = orders_stats
            
            # إحصائيات المخلصين
            agents_stats = self._get_agents_statistics(period_days)
            dashboard_data['agents_stats'] = agents_stats
            
            # التحليل المالي
            financial_analysis = self._get_financial_analysis(period_days)
            dashboard_data['financial_analysis'] = financial_analysis
            
            # تحليل الاتجاهات
            trends_analysis = self._get_trends_analysis(period_days)
            dashboard_data['trends_analysis'] = trends_analysis
            
            # التوزيع الجغرافي
            geographic_distribution = self._get_geographic_distribution(period_days)
            dashboard_data['geographic_distribution'] = geographic_distribution
            
            return dashboard_data
            
        except Exception as e:
            logger.error(f"Error getting executive dashboard data: {e}")
            return {}
    
    def _calculate_kpis(self, period_days: int) -> Dict:
        """حساب مؤشرات الأداء الرئيسية"""
        try:
            kpis_query = """
                SELECT 
                    COUNT(*) as total_orders,
                    COUNT(CASE WHEN order_status = 'completed' THEN 1 END) as completed_orders,
                    COUNT(CASE WHEN order_status = 'completed' AND actual_completion_date <= expected_completion_date THEN 1 END) as on_time_orders,
                    COUNT(CASE WHEN expected_completion_date < SYSDATE AND order_status NOT IN ('completed', 'cancelled') THEN 1 END) as overdue_orders,
                    AVG(CASE 
                        WHEN order_status = 'completed' 
                        THEN actual_completion_date - created_date
                        ELSE NULL 
                    END) as avg_completion_days,
                    COUNT(CASE WHEN documents_status = 'complete' THEN 1 END) as complete_documents_orders,
                    COUNT(DISTINCT customs_agent_id) as active_agents
                FROM delivery_orders
                WHERE created_date >= SYSDATE - :period_days
            """
            
            kpis_result = self.db_manager.execute_query(kpis_query, {'period_days': period_days})
            
            if kpis_result:
                row = kpis_result[0]
                total_orders = row[0] or 0
                completed_orders = row[1] or 0
                on_time_orders = row[2] or 0
                overdue_orders = row[3] or 0
                avg_completion_days = float(row[4] or 0)
                complete_documents_orders = row[5] or 0
                active_agents = row[6] or 0
                
                # حساب المؤشرات
                on_time_rate = (on_time_orders / completed_orders * 100) if completed_orders > 0 else 0
                completion_rate = (completed_orders / total_orders * 100) if total_orders > 0 else 0
                document_completion_rate = (complete_documents_orders / total_orders * 100) if total_orders > 0 else 0
                overdue_rate = (overdue_orders / total_orders * 100) if total_orders > 0 else 0
                
                return {
                    'total_orders': total_orders,
                    'completed_orders': completed_orders,
                    'completion_rate': round(completion_rate, 2),
                    'on_time_delivery_rate': round(on_time_rate, 2),
                    'document_completion_rate': round(document_completion_rate, 2),
                    'overdue_rate': round(overdue_rate, 2),
                    'average_completion_days': round(avg_completion_days, 2),
                    'active_agents': active_agents,
                    'performance_score': self._calculate_overall_performance_score({
                        'completion_rate': completion_rate,
                        'on_time_rate': on_time_rate,
                        'document_completion_rate': document_completion_rate,
                        'avg_completion_days': avg_completion_days
                    })
                }
            
            return {}
            
        except Exception as e:
            logger.error(f"Error calculating KPIs: {e}")
            return {}
    
    def _get_orders_statistics(self, period_days: int) -> Dict:
        """جلب إحصائيات الأوامر المفصلة"""
        try:
            # إحصائيات حسب الحالة
            status_stats_query = """
                SELECT 
                    order_status,
                    COUNT(*) as count,
                    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
                FROM delivery_orders
                WHERE created_date >= SYSDATE - :period_days
                GROUP BY order_status
                ORDER BY count DESC
            """
            
            status_stats = self.db_manager.execute_query(status_stats_query, {'period_days': period_days})
            
            # إحصائيات حسب الأولوية
            priority_stats_query = """
                SELECT 
                    priority,
                    COUNT(*) as count,
                    COUNT(CASE WHEN order_status = 'completed' THEN 1 END) as completed,
                    AVG(CASE 
                        WHEN order_status = 'completed' 
                        THEN actual_completion_date - created_date
                        ELSE NULL 
                    END) as avg_completion_days
                FROM delivery_orders
                WHERE created_date >= SYSDATE - :period_days
                GROUP BY priority
                ORDER BY 
                    CASE priority 
                        WHEN 'urgent' THEN 1
                        WHEN 'high' THEN 2
                        WHEN 'normal' THEN 3
                        WHEN 'low' THEN 4
                    END
            """
            
            priority_stats = self.db_manager.execute_query(priority_stats_query, {'period_days': period_days})
            
            # الأوامر اليومية
            daily_orders_query = """
                SELECT 
                    TRUNC(created_date) as order_date,
                    COUNT(*) as total_orders,
                    COUNT(CASE WHEN order_status = 'completed' THEN 1 END) as completed_orders
                FROM delivery_orders
                WHERE created_date >= SYSDATE - :period_days
                GROUP BY TRUNC(created_date)
                ORDER BY order_date
            """
            
            daily_orders = self.db_manager.execute_query(daily_orders_query, {'period_days': period_days})
            
            return {
                'status_distribution': [
                    {'status': row[0], 'count': row[1], 'percentage': row[2]}
                    for row in status_stats or []
                ],
                'priority_analysis': [
                    {
                        'priority': row[0],
                        'count': row[1],
                        'completed': row[2],
                        'completion_rate': (row[2] / row[1] * 100) if row[1] > 0 else 0,
                        'avg_completion_days': float(row[3] or 0)
                    }
                    for row in priority_stats or []
                ],
                'daily_trends': [
                    {
                        'date': row[0].strftime('%Y-%m-%d'),
                        'total_orders': row[1],
                        'completed_orders': row[2],
                        'completion_rate': (row[2] / row[1] * 100) if row[1] > 0 else 0
                    }
                    for row in daily_orders or []
                ]
            }
            
        except Exception as e:
            logger.error(f"Error getting orders statistics: {e}")
            return {}
    
    def _get_agents_statistics(self, period_days: int) -> Dict:
        """جلب إحصائيات المخلصين"""
        try:
            # أداء المخلصين
            agents_performance_query = """
                SELECT 
                    ca.id,
                    ca.agent_name,
                    ca.specialization,
                    ca.rating,
                    COUNT(do.id) as total_orders,
                    COUNT(CASE WHEN do.order_status = 'completed' THEN 1 END) as completed_orders,
                    COUNT(CASE WHEN do.expected_completion_date < SYSDATE AND do.order_status NOT IN ('completed', 'cancelled') THEN 1 END) as overdue_orders,
                    AVG(CASE 
                        WHEN do.order_status = 'completed' 
                        THEN do.actual_completion_date - do.created_date
                        ELSE NULL 
                    END) as avg_completion_days
                FROM customs_agents ca
                LEFT JOIN delivery_orders do ON ca.id = do.customs_agent_id 
                    AND do.created_date >= SYSDATE - :period_days
                WHERE ca.is_active = 1
                GROUP BY ca.id, ca.agent_name, ca.specialization, ca.rating
                HAVING COUNT(do.id) > 0
                ORDER BY completed_orders DESC, ca.rating DESC
            """
            
            agents_performance = self.db_manager.execute_query(
                agents_performance_query, 
                {'period_days': period_days}
            )
            
            # إحصائيات التخصصات
            specialization_stats_query = """
                SELECT 
                    ca.specialization,
                    COUNT(DISTINCT ca.id) as agents_count,
                    COUNT(do.id) as total_orders,
                    COUNT(CASE WHEN do.order_status = 'completed' THEN 1 END) as completed_orders,
                    AVG(ca.rating) as avg_rating
                FROM customs_agents ca
                LEFT JOIN delivery_orders do ON ca.id = do.customs_agent_id 
                    AND do.created_date >= SYSDATE - :period_days
                WHERE ca.is_active = 1
                GROUP BY ca.specialization
                ORDER BY total_orders DESC
            """
            
            specialization_stats = self.db_manager.execute_query(
                specialization_stats_query,
                {'period_days': period_days}
            )
            
            return {
                'top_performers': [
                    {
                        'agent_id': row[0],
                        'agent_name': row[1],
                        'specialization': row[2],
                        'rating': float(row[3] or 0),
                        'total_orders': row[4],
                        'completed_orders': row[5],
                        'overdue_orders': row[6],
                        'completion_rate': (row[5] / row[4] * 100) if row[4] > 0 else 0,
                        'avg_completion_days': float(row[7] or 0)
                    }
                    for row in agents_performance or []
                ],
                'specialization_analysis': [
                    {
                        'specialization': row[0],
                        'agents_count': row[1],
                        'total_orders': row[2],
                        'completed_orders': row[3],
                        'completion_rate': (row[3] / row[2] * 100) if row[2] > 0 else 0,
                        'avg_rating': float(row[4] or 0)
                    }
                    for row in specialization_stats or []
                ]
            }
            
        except Exception as e:
            logger.error(f"Error getting agents statistics: {e}")
            return {}
    
    def _get_financial_analysis(self, period_days: int) -> Dict:
        """التحليل المالي"""
        try:
            financial_query = """
                SELECT 
                    COUNT(*) as total_orders,
                    SUM(NVL(estimated_cost, 0)) as total_estimated_cost,
                    SUM(NVL(actual_cost, 0)) as total_actual_cost,
                    AVG(NVL(estimated_cost, 0)) as avg_estimated_cost,
                    AVG(NVL(actual_cost, 0)) as avg_actual_cost,
                    COUNT(CASE WHEN actual_cost > estimated_cost THEN 1 END) as over_budget_orders,
                    SUM(CASE WHEN actual_cost > estimated_cost THEN actual_cost - estimated_cost ELSE 0 END) as total_overrun
                FROM delivery_orders
                WHERE created_date >= SYSDATE - :period_days
                AND order_status = 'completed'
            """
            
            financial_result = self.db_manager.execute_query(financial_query, {'period_days': period_days})
            
            if financial_result:
                row = financial_result[0]
                total_orders = row[0] or 0
                total_estimated = float(row[1] or 0)
                total_actual = float(row[2] or 0)
                avg_estimated = float(row[3] or 0)
                avg_actual = float(row[4] or 0)
                over_budget_orders = row[5] or 0
                total_overrun = float(row[6] or 0)
                
                return {
                    'total_orders': total_orders,
                    'total_estimated_cost': total_estimated,
                    'total_actual_cost': total_actual,
                    'cost_variance': total_actual - total_estimated,
                    'cost_variance_percentage': ((total_actual - total_estimated) / total_estimated * 100) if total_estimated > 0 else 0,
                    'avg_estimated_cost': avg_estimated,
                    'avg_actual_cost': avg_actual,
                    'over_budget_orders': over_budget_orders,
                    'over_budget_rate': (over_budget_orders / total_orders * 100) if total_orders > 0 else 0,
                    'total_cost_overrun': total_overrun
                }
            
            return {}
            
        except Exception as e:
            logger.error(f"Error getting financial analysis: {e}")
            return {}

    def _get_trends_analysis(self, period_days: int) -> Dict:
        """تحليل الاتجاهات"""
        try:
            # اتجاهات الأداء الأسبوعية
            weekly_trends_query = """
                SELECT
                    TRUNC(created_date, 'IW') as week_start,
                    COUNT(*) as total_orders,
                    COUNT(CASE WHEN order_status = 'completed' THEN 1 END) as completed_orders,
                    COUNT(CASE WHEN order_status = 'completed' AND actual_completion_date <= expected_completion_date THEN 1 END) as on_time_orders,
                    AVG(CASE
                        WHEN order_status = 'completed'
                        THEN actual_completion_date - created_date
                        ELSE NULL
                    END) as avg_completion_days
                FROM delivery_orders
                WHERE created_date >= SYSDATE - :period_days
                GROUP BY TRUNC(created_date, 'IW')
                ORDER BY week_start
            """

            weekly_trends = self.db_manager.execute_query(weekly_trends_query, {'period_days': period_days})

            # تحليل نمو الأوامر
            growth_analysis = self._calculate_growth_rates(period_days)

            # التنبؤ بالاتجاهات المستقبلية
            future_predictions = self._predict_future_trends(weekly_trends)

            return {
                'weekly_performance': [
                    {
                        'week_start': row[0].strftime('%Y-%m-%d'),
                        'total_orders': row[1],
                        'completed_orders': row[2],
                        'on_time_orders': row[3],
                        'completion_rate': (row[2] / row[1] * 100) if row[1] > 0 else 0,
                        'on_time_rate': (row[3] / row[2] * 100) if row[2] > 0 else 0,
                        'avg_completion_days': float(row[4] or 0)
                    }
                    for row in weekly_trends or []
                ],
                'growth_analysis': growth_analysis,
                'future_predictions': future_predictions
            }

        except Exception as e:
            logger.error(f"Error getting trends analysis: {e}")
            return {}

    def _get_geographic_distribution(self, period_days: int) -> Dict:
        """التوزيع الجغرافي للأوامر"""
        try:
            geographic_query = """
                SELECT
                    cs.port_of_discharge,
                    COUNT(do.id) as total_orders,
                    COUNT(CASE WHEN do.order_status = 'completed' THEN 1 END) as completed_orders,
                    AVG(CASE
                        WHEN do.order_status = 'completed'
                        THEN do.actual_completion_date - do.created_date
                        ELSE NULL
                    END) as avg_completion_days
                FROM delivery_orders do
                JOIN cargo_shipments cs ON do.shipment_id = cs.id
                WHERE do.created_date >= SYSDATE - :period_days
                GROUP BY cs.port_of_discharge
                ORDER BY total_orders DESC
            """

            geographic_result = self.db_manager.execute_query(geographic_query, {'period_days': period_days})

            return {
                'port_distribution': [
                    {
                        'port': row[0],
                        'total_orders': row[1],
                        'completed_orders': row[2],
                        'completion_rate': (row[2] / row[1] * 100) if row[1] > 0 else 0,
                        'avg_completion_days': float(row[3] or 0)
                    }
                    for row in geographic_result or []
                ]
            }

        except Exception as e:
            logger.error(f"Error getting geographic distribution: {e}")
            return {}

    def _calculate_overall_performance_score(self, metrics: Dict) -> float:
        """حساب نقاط الأداء الإجمالية"""
        try:
            # أوزان المؤشرات
            weights = {
                'completion_rate': 0.3,
                'on_time_rate': 0.3,
                'document_completion_rate': 0.2,
                'avg_completion_days': 0.2
            }

            # تطبيع النقاط (0-100)
            normalized_scores = {}

            # معدل الإنجاز
            normalized_scores['completion_rate'] = min(100, metrics.get('completion_rate', 0))

            # معدل التسليم في الوقت المحدد
            normalized_scores['on_time_rate'] = min(100, metrics.get('on_time_rate', 0))

            # معدل اكتمال الوثائق
            normalized_scores['document_completion_rate'] = min(100, metrics.get('document_completion_rate', 0))

            # متوسط أيام الإنجاز (عكسي - كلما قل كان أفضل)
            avg_days = metrics.get('avg_completion_days', 0)
            target_days = self.analytics_config['kpi_targets']['average_completion_days']
            if avg_days <= target_days:
                normalized_scores['avg_completion_days'] = 100
            else:
                normalized_scores['avg_completion_days'] = max(0, 100 - (avg_days - target_days) * 10)

            # حساب النقاط المرجحة
            weighted_score = sum(
                normalized_scores[metric] * weights[metric]
                for metric in weights.keys()
            )

            return round(weighted_score, 2)

        except Exception as e:
            logger.error(f"Error calculating performance score: {e}")
            return 0.0

    def _calculate_growth_rates(self, period_days: int) -> Dict:
        """حساب معدلات النمو"""
        try:
            # مقارنة الفترة الحالية مع الفترة السابقة
            current_period_query = """
                SELECT COUNT(*) as orders_count
                FROM delivery_orders
                WHERE created_date >= SYSDATE - :period_days
            """

            previous_period_query = """
                SELECT COUNT(*) as orders_count
                FROM delivery_orders
                WHERE created_date >= SYSDATE - :period_days * 2
                AND created_date < SYSDATE - :period_days
            """

            current_result = self.db_manager.execute_query(current_period_query, {'period_days': period_days})
            previous_result = self.db_manager.execute_query(previous_period_query, {'period_days': period_days})

            current_count = current_result[0][0] if current_result else 0
            previous_count = previous_result[0][0] if previous_result else 0

            if previous_count > 0:
                growth_rate = ((current_count - previous_count) / previous_count) * 100
            else:
                growth_rate = 100 if current_count > 0 else 0

            return {
                'current_period_orders': current_count,
                'previous_period_orders': previous_count,
                'growth_rate': round(growth_rate, 2),
                'growth_direction': 'up' if growth_rate > 0 else 'down' if growth_rate < 0 else 'stable'
            }

        except Exception as e:
            logger.error(f"Error calculating growth rates: {e}")
            return {}

    def _predict_future_trends(self, historical_data: List) -> Dict:
        """التنبؤ بالاتجاهات المستقبلية"""
        try:
            if not historical_data or len(historical_data) < 3:
                return {'prediction_available': False}

            # استخراج البيانات للتحليل
            weeks = list(range(len(historical_data)))
            orders_counts = [row[1] for row in historical_data]
            completion_rates = [(row[2] / row[1] * 100) if row[1] > 0 else 0 for row in historical_data]

            # حساب الاتجاه الخطي البسيط
            orders_trend = np.polyfit(weeks, orders_counts, 1) if len(weeks) > 1 else [0, 0]
            completion_trend = np.polyfit(weeks, completion_rates, 1) if len(weeks) > 1 else [0, 0]

            # التنبؤ للأسابيع القادمة
            next_weeks = [len(weeks), len(weeks) + 1, len(weeks) + 2, len(weeks) + 3]
            predicted_orders = [orders_trend[0] * week + orders_trend[1] for week in next_weeks]
            predicted_completion_rates = [completion_trend[0] * week + completion_trend[1] for week in next_weeks]

            return {
                'prediction_available': True,
                'orders_trend_slope': float(orders_trend[0]),
                'completion_trend_slope': float(completion_trend[0]),
                'predicted_orders': [max(0, int(orders)) for orders in predicted_orders],
                'predicted_completion_rates': [max(0, min(100, rate)) for rate in predicted_completion_rates],
                'trend_direction': {
                    'orders': 'increasing' if orders_trend[0] > 0 else 'decreasing' if orders_trend[0] < 0 else 'stable',
                    'completion': 'improving' if completion_trend[0] > 0 else 'declining' if completion_trend[0] < 0 else 'stable'
                }
            }

        except Exception as e:
            logger.error(f"Error predicting future trends: {e}")
            return {'prediction_available': False}

    def generate_performance_report(self, period_days: int = 30, agent_id: int = None) -> Dict:
        """إنشاء تقرير الأداء الشامل"""
        try:
            report = {
                'report_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'period_days': period_days,
                'report_type': 'agent_performance' if agent_id else 'overall_performance'
            }

            if agent_id:
                # تقرير أداء مخلص محدد
                report.update(self._generate_agent_performance_report(agent_id, period_days))
            else:
                # تقرير الأداء العام
                report.update(self.get_executive_dashboard_data(period_days))

            # إضافة التوصيات
            report['recommendations'] = self._generate_recommendations(report)

            return report

        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return {}

    def _generate_agent_performance_report(self, agent_id: int, period_days: int) -> Dict:
        """إنشاء تقرير أداء مخلص محدد"""
        try:
            agent_query = """
                SELECT
                    ca.agent_name, ca.specialization, ca.rating,
                    COUNT(do.id) as total_orders,
                    COUNT(CASE WHEN do.order_status = 'completed' THEN 1 END) as completed_orders,
                    COUNT(CASE WHEN do.order_status = 'completed' AND do.actual_completion_date <= do.expected_completion_date THEN 1 END) as on_time_orders,
                    COUNT(CASE WHEN do.expected_completion_date < SYSDATE AND do.order_status NOT IN ('completed', 'cancelled') THEN 1 END) as overdue_orders,
                    AVG(CASE
                        WHEN do.order_status = 'completed'
                        THEN do.actual_completion_date - do.created_date
                        ELSE NULL
                    END) as avg_completion_days,
                    SUM(NVL(do.actual_cost, 0)) as total_revenue
                FROM customs_agents ca
                LEFT JOIN delivery_orders do ON ca.id = do.customs_agent_id
                    AND do.created_date >= SYSDATE - :period_days
                WHERE ca.id = :agent_id
                GROUP BY ca.agent_name, ca.specialization, ca.rating
            """

            agent_result = self.db_manager.execute_query(agent_query, {
                'agent_id': agent_id,
                'period_days': period_days
            })

            if agent_result:
                row = agent_result[0]
                total_orders = row[3] or 0
                completed_orders = row[4] or 0
                on_time_orders = row[5] or 0

                return {
                    'agent_info': {
                        'name': row[0],
                        'specialization': row[1],
                        'rating': float(row[2] or 0)
                    },
                    'performance_metrics': {
                        'total_orders': total_orders,
                        'completed_orders': completed_orders,
                        'on_time_orders': on_time_orders,
                        'overdue_orders': row[6] or 0,
                        'completion_rate': (completed_orders / total_orders * 100) if total_orders > 0 else 0,
                        'on_time_rate': (on_time_orders / completed_orders * 100) if completed_orders > 0 else 0,
                        'avg_completion_days': float(row[7] or 0),
                        'total_revenue': float(row[8] or 0)
                    }
                }

            return {}

        except Exception as e:
            logger.error(f"Error generating agent performance report: {e}")
            return {}

    def _generate_recommendations(self, report_data: Dict) -> List[Dict]:
        """إنشاء التوصيات بناءً على بيانات التقرير"""
        try:
            recommendations = []

            # تحليل مؤشرات الأداء وإنشاء التوصيات
            kpis = report_data.get('kpis', {})

            # توصيات معدل التسليم في الوقت المحدد
            on_time_rate = kpis.get('on_time_delivery_rate', 0)
            if on_time_rate < 80:
                recommendations.append({
                    'type': 'urgent',
                    'category': 'delivery_performance',
                    'title': 'تحسين معدل التسليم في الوقت المحدد',
                    'description': f'معدل التسليم في الوقت المحدد ({on_time_rate:.1f}%) أقل من المستوى المطلوب',
                    'actions': [
                        'مراجعة عمليات التخطيط والجدولة',
                        'تحسين التواصل مع المخلصين',
                        'تطوير نظام التنبيهات المبكرة'
                    ]
                })

            # توصيات اكتمال الوثائق
            doc_completion_rate = kpis.get('document_completion_rate', 0)
            if doc_completion_rate < 90:
                recommendations.append({
                    'type': 'important',
                    'category': 'documentation',
                    'title': 'تحسين معدل اكتمال الوثائق',
                    'description': f'معدل اكتمال الوثائق ({doc_completion_rate:.1f}%) يحتاج لتحسين',
                    'actions': [
                        'تدريب المخلصين على متطلبات الوثائق',
                        'تطوير قوائم مراجعة رقمية',
                        'تحسين واجهة رفع الوثائق'
                    ]
                })

            # توصيات الأوامر المتأخرة
            overdue_rate = kpis.get('overdue_rate', 0)
            if overdue_rate > 10:
                recommendations.append({
                    'type': 'urgent',
                    'category': 'time_management',
                    'title': 'تقليل الأوامر المتأخرة',
                    'description': f'نسبة الأوامر المتأخرة ({overdue_rate:.1f}%) مرتفعة',
                    'actions': [
                        'مراجعة تقديرات أوقات الإنجاز',
                        'تحسين توزيع الأعباء على المخلصين',
                        'تطوير نظام المتابعة اليومية'
                    ]
                })

            return recommendations

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return []

# مثيل عام للاستخدام
analytics_manager = AdvancedAnalyticsManager()

# ===== Routes للتحليلات =====

@analytics_bp.route('/executive-dashboard')
@login_required
def executive_dashboard():
    """لوحة المعلومات التنفيذية"""
    try:
        period = int(request.args.get('period', 30))
        dashboard_data = analytics_manager.get_executive_dashboard_data(period)
        return render_template('analytics/executive_dashboard.html',
                             dashboard_data=dashboard_data,
                             period=period)
    except Exception as e:
        logger.error(f"Error in executive dashboard: {e}")
        return jsonify({'error': str(e)}), 500

@analytics_bp.route('/api/dashboard-data')
@login_required
def api_dashboard_data():
    """API لجلب بيانات لوحة المعلومات"""
    try:
        period = int(request.args.get('period', 30))
        dashboard_data = analytics_manager.get_executive_dashboard_data(period)
        return jsonify(dashboard_data)
    except Exception as e:
        logger.error(f"Error getting dashboard data: {e}")
        return jsonify({'error': str(e)}), 500
