#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الإشعارات الحقيقي
Real Notification Manager
"""

from datetime import datetime, timedelta
import logging
from database_manager import DatabaseManager

logger = logging.getLogger(__name__)

class NotificationManager:
    """مدير الإشعارات الحقيقي"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def get_all_notifications(self, limit=50, filter_type=None, search_term=None):
        """جلب جميع الإشعارات مع التصفية"""
        try:
            # بناء الاستعلام الأساسي
            base_query = """
                SELECT 
                    n.id,
                    n.notification_type,
                    n.title,
                    n.message,
                    n.is_read,
                    n.created_at,
                    n.related_id,
                    n.related_type,
                    n.priority_level,
                    n.action_url
                FROM notifications n
                WHERE 1=1
            """
            
            params = {}
            conditions = []
            
            # تصفية حسب النوع
            if filter_type and filter_type != 'all':
                if filter_type == 'unread':
                    conditions.append("n.is_read = 0")
                elif filter_type == 'important':
                    conditions.append("n.priority_level >= 7")
                elif filter_type == 'system':
                    conditions.append("n.notification_type = 'system'")
            
            # البحث في العنوان والمحتوى
            if search_term:
                conditions.append("(UPPER(n.title) LIKE UPPER(:search) OR UPPER(n.message) LIKE UPPER(:search))")
                params['search'] = f'%{search_term}%'
            
            # إضافة الشروط للاستعلام
            if conditions:
                base_query += " AND " + " AND ".join(conditions)
            
            # ترتيب وحد
            base_query += " ORDER BY n.created_at DESC"
            if limit:
                base_query += f" FETCH FIRST {limit} ROWS ONLY"
            
            results = self.db_manager.execute_query(base_query, params)
            
            notifications = []
            for row in results:
                notifications.append({
                    'id': row[0],
                    'type': row[1],
                    'title': row[2],
                    'message': row[3],
                    'is_read': bool(row[4]),
                    'created_at': row[5],
                    'related_id': row[6],
                    'related_type': row[7],
                    'priority_level': row[8] or 5,
                    'action_url': row[9],
                    'time_ago': self._calculate_time_ago(row[5])
                })
            
            return notifications
            
        except Exception as e:
            logger.error(f"Error getting notifications: {e}")
            return []
    
    def get_notification_stats(self):
        """جلب إحصائيات الإشعارات"""
        try:
            stats_query = """
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread,
                    COUNT(CASE WHEN is_read = 1 THEN 1 END) as read,
                    COUNT(CASE WHEN created_at >= SYSDATE - 1 THEN 1 END) as today
                FROM notifications
            """
            
            result = self.db_manager.execute_query(stats_query)
            
            if result:
                total = result[0][0] or 0
                unread = result[0][1] or 0
                read = result[0][2] or 0
                today = result[0][3] or 0
                
                read_percentage = (read / total * 100) if total > 0 else 0
                
                return {
                    'total': total,
                    'unread': unread,
                    'read': read,
                    'today': today,
                    'read_percentage': round(read_percentage, 1)
                }
            
            return {'total': 0, 'unread': 0, 'read': 0, 'today': 0, 'read_percentage': 0}
            
        except Exception as e:
            logger.error(f"Error getting notification stats: {e}")
            return {'total': 0, 'unread': 0, 'read': 0, 'today': 0, 'read_percentage': 0}
    
    def create_notification(self, notification_type, title, message, related_id=None, 
                          related_type=None, priority_level=5, action_url=None):
        """إنشاء إشعار جديد"""
        try:
            insert_query = """
                INSERT INTO notifications (
                    id, notification_type, title, message, is_read,
                    created_at, related_id, related_type, priority_level, action_url
                ) VALUES (
                    notifications_seq.NEXTVAL, :type, :title, :message, 0,
                    SYSDATE, :related_id, :related_type, :priority, :action_url
                )
            """
            
            params = {
                'type': notification_type,
                'title': title,
                'message': message,
                'related_id': related_id,
                'related_type': related_type,
                'priority': priority_level,
                'action_url': action_url
            }
            
            self.db_manager.execute_update(insert_query, params)
            return True
            
        except Exception as e:
            logger.error(f"Error creating notification: {e}")
            return False
    
    def mark_as_read(self, notification_id):
        """تحديد إشعار كمقروء"""
        try:
            update_query = """
                UPDATE notifications 
                SET is_read = 1, read_at = SYSDATE 
                WHERE id = :id
            """
            
            result = self.db_manager.execute_update(update_query, {'id': notification_id})
            return result > 0
            
        except Exception as e:
            logger.error(f"Error marking notification as read: {e}")
            return False
    
    def mark_all_as_read(self):
        """تحديد جميع الإشعارات كمقروءة"""
        try:
            update_query = """
                UPDATE notifications 
                SET is_read = 1, read_at = SYSDATE 
                WHERE is_read = 0
            """
            
            result = self.db_manager.execute_update(update_query)
            return result
            
        except Exception as e:
            logger.error(f"Error marking all notifications as read: {e}")
            return 0
    
    def delete_notification(self, notification_id):
        """حذف إشعار"""
        try:
            delete_query = "DELETE FROM notifications WHERE id = :id"
            result = self.db_manager.execute_update(delete_query, {'id': notification_id})
            return result > 0
            
        except Exception as e:
            logger.error(f"Error deleting notification: {e}")
            return False
    
    def delete_read_notifications(self):
        """حذف الإشعارات المقروءة"""
        try:
            delete_query = "DELETE FROM notifications WHERE is_read = 1"
            result = self.db_manager.execute_update(delete_query)
            return result
            
        except Exception as e:
            logger.error(f"Error deleting read notifications: {e}")
            return 0
    
    def create_system_notifications(self):
        """إنشاء إشعارات النظام التلقائية"""
        try:
            # إشعارات من أوامر التسليم الحديثة
            delivery_orders_query = """
                SELECT id, created_date, shipment_id, order_number
                FROM delivery_orders
                WHERE created_date >= SYSDATE - 1
                AND TO_CHAR(id) NOT IN (
                    SELECT related_id FROM notifications
                    WHERE related_type = 'delivery_order'
                    AND notification_type = 'success'
                    AND related_id IS NOT NULL
                )
                ORDER BY created_date DESC
                FETCH FIRST 5 ROWS ONLY
            """

            delivery_results = self.db_manager.execute_query(delivery_orders_query)

            for row in delivery_results:
                order_id = row[0]
                created_date = row[1]
                shipment_id = row[2]
                order_number = row[3] if len(row) > 3 else f'DO-{order_id}'

                self.create_notification(
                    notification_type='success',
                    title='تم إنشاء أمر تسليم بنجاح',
                    message=f'تم إنشاء أمر التسليم رقم {order_number} للشحنة {shipment_id} بنجاح.',
                    related_id=str(order_id),
                    related_type='delivery_order',
                    priority_level=6,
                    action_url=f'/delivery-orders/{order_id}'
                )
            
            # إشعارات من الشحنات المتأخرة
            delayed_shipments_query = """
                SELECT id, eta, status, shipment_number
                FROM cargo_shipments
                WHERE eta < SYSDATE - 2
                AND (status IS NULL OR status NOT IN ('تم التسليم', 'مكتمل', 'delivered', 'completed'))
                AND TO_CHAR(id) NOT IN (
                    SELECT related_id FROM notifications
                    WHERE related_type = 'shipment'
                    AND notification_type = 'warning'
                    AND created_at >= SYSDATE - 1
                    AND related_id IS NOT NULL
                )
                FETCH FIRST 3 ROWS ONLY
            """

            delayed_results = self.db_manager.execute_query(delayed_shipments_query)

            for row in delayed_results:
                shipment_id = row[0]
                expected_date = row[1]
                status = row[2]
                shipment_number = row[3] if len(row) > 3 else f'SH-{shipment_id}'

                if expected_date:
                    days_delayed = (datetime.now() - expected_date).days
                else:
                    days_delayed = 0

                self.create_notification(
                    notification_type='warning',
                    title='تأخير في الشحنة',
                    message=f'الشحنة {shipment_number} متأخرة لأكثر من {days_delayed} يوم عن الموعد المتوقع.',
                    related_id=str(shipment_id),
                    related_type='shipment',
                    priority_level=8,
                    action_url=f'/shipments/{shipment_id}'
                )
            
            # إشعارات من تحديثات المخلصين
            agent_updates_query = """
                SELECT id, agent_name, rating, updated_at
                FROM customs_agents
                WHERE updated_at >= SYSDATE - 1
                AND TO_CHAR(id) NOT IN (
                    SELECT related_id FROM notifications
                    WHERE related_type = 'agent'
                    AND notification_type = 'info'
                    AND created_at >= SYSDATE - 1
                    AND related_id IS NOT NULL
                )
                FETCH FIRST 3 ROWS ONLY
            """

            agent_results = self.db_manager.execute_query(agent_updates_query)

            for row in agent_results:
                agent_id = row[0]
                agent_name = row[1]
                rating = row[2] or 0

                self.create_notification(
                    notification_type='info',
                    title='تحديث تقييم المخلص',
                    message=f'تم تحديث تقييم المخلص {agent_name} إلى {rating:.1f} نجوم.',
                    related_id=str(agent_id),
                    related_type='agent',
                    priority_level=4,
                    action_url=f'/agents/{agent_id}'
                )
            
            return True
            
        except Exception as e:
            logger.error(f"Error creating system notifications: {e}")
            return False
    
    def _calculate_time_ago(self, notification_date):
        """حساب الوقت النسبي"""
        try:
            if not notification_date:
                return 'غير محدد'
                
            now = datetime.now()
            diff = now - notification_date
            
            if diff.days > 0:
                return f'منذ {diff.days} يوم'
            elif diff.seconds > 3600:
                hours = diff.seconds // 3600
                return f'منذ {hours} ساعة'
            elif diff.seconds > 60:
                minutes = diff.seconds // 60
                return f'منذ {minutes} دقيقة'
            else:
                return 'منذ لحظات'
                
        except Exception as e:
            logger.error(f"Error calculating time ago: {e}")
            return 'غير محدد'
    
    def get_notification_icon_type(self, notification_type):
        """تحديد نوع أيقونة الإشعار"""
        icon_map = {
            'success': 'success',
            'warning': 'warning', 
            'error': 'error',
            'info': 'info',
            'system': 'info'
        }
        return icon_map.get(notification_type, 'info')

# إنشاء مثيل عام
notification_manager = NotificationManager()
