#!/usr/bin/env python3
"""
الحصول على OneDrive User token النهائي
"""

import sys
import os
import requests
import json

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def get_user_token_final():
    """الحصول على User token باستخدام الكود الجديد"""
    
    # الكود الجديد
    auth_code = "6ed9bd3c-02d8-4826-ba85-b5a0b2573874"
    
    # قراءة الإعدادات
    config_path = "app/shipments/cloud_config.json"
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        onedrive_config = config.get('onedrive', {})
        client_id = onedrive_config.get('client_id')
        client_secret = onedrive_config.get('client_secret')
        tenant_id = onedrive_config.get('tenant_id')
        redirect_uri = onedrive_config.get('redirect_uri', 'https://sas.alfogehi.net:5000/auth/onedrive/callback')
        
        print(f"🔄 تبديل الكود الجديد بـ User access token...")
        print(f"📋 Code: {auth_code}")
        
        # تبديل الكود بـ access token
        token_url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token"
        
        token_data = {
            'grant_type': 'authorization_code',
            'client_id': client_id,
            'client_secret': client_secret,
            'code': auth_code,
            'redirect_uri': redirect_uri,
            'scope': 'Files.ReadWrite Files.ReadWrite.All offline_access'
        }
        
        response = requests.post(token_url, data=token_data, timeout=30)
        
        print(f"📊 استجابة تبديل الكود: {response.status_code}")
        
        if response.status_code == 200:
            token_response = response.json()
            access_token = token_response.get('access_token')
            refresh_token = token_response.get('refresh_token')
            
            if access_token:
                print(f"✅ تم الحصول على User access token")
                print(f"📄 Token: {access_token[:50]}...")
                
                # اختبار الـ token مع /me/drive
                test_url = "https://graph.microsoft.com/v1.0/me/drive"
                headers = {
                    'Authorization': f'Bearer {access_token}',
                    'Content-Type': 'application/json'
                }
                
                test_response = requests.get(test_url, headers=headers, timeout=10)
                
                print(f"📊 اختبار /me/drive: {test_response.status_code}")
                
                if test_response.status_code == 200:
                    drive_info = test_response.json()
                    print(f"✅ User token يعمل مع OneDrive!")
                    print(f"📁 Drive ID: {drive_info.get('id', 'غير متوفر')}")
                    print(f"👤 Owner: {drive_info.get('owner', {}).get('user', {}).get('displayName', 'غير متوفر')}")
                    print(f"🌐 Web URL: {drive_info.get('webUrl', 'غير متوفر')}")
                    
                    # اختبار رفع ملف تجريبي
                    print(f"\n🧪 اختبار رفع ملف تجريبي...")
                    test_upload_result = test_file_upload(access_token)
                    
                    if test_upload_result:
                        print(f"✅ الرفع الحقيقي يعمل!")
                        
                        # حفظ الـ User token
                        config['onedrive']['access_token'] = access_token
                        config['onedrive']['refresh_token'] = refresh_token
                        config['onedrive']['token_type'] = 'user'
                        
                        with open(config_path, 'w', encoding='utf-8') as f:
                            json.dump(config, f, indent=2, ensure_ascii=False)
                        
                        print(f"💾 تم حفظ User token")
                        return True
                    else:
                        print(f"❌ الرفع الحقيقي لا يعمل")
                        return False
                else:
                    print(f"❌ فشل في اختبار /me/drive: {test_response.status_code}")
                    print(f"📄 Response: {test_response.text}")
                    return False
            else:
                print(f"❌ لم يتم الحصول على access token")
                return False
        else:
            print(f"❌ فشل في تبديل الكود: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def test_file_upload(access_token):
    """اختبار رفع ملف تجريبي"""
    try:
        # إنشاء ملف تجريبي
        test_content = "هذا ملف تجريبي لاختبار OneDrive"
        test_filename = "test_onedrive_upload.txt"
        
        with open(test_filename, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # رفع الملف
        upload_url = f"https://graph.microsoft.com/v1.0/me/drive/root:/{test_filename}:/content"
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/octet-stream"
        }
        
        with open(test_filename, 'rb') as f:
            response = requests.put(upload_url, data=f, headers=headers, timeout=30)
        
        print(f"📊 نتيجة الرفع: {response.status_code}")
        
        if response.status_code in [200, 201]:
            file_data = response.json()
            file_id = file_data.get("id")
            web_url = file_data.get("webUrl")
            
            print(f"✅ تم رفع الملف بنجاح!")
            print(f"🆔 File ID: {file_id}")
            print(f"🌐 Web URL: {web_url}")
            
            # حذف الملف التجريبي
            os.remove(test_filename)
            
            return True
        else:
            print(f"❌ فشل في رفع الملف: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
            # حذف الملف التجريبي
            if os.path.exists(test_filename):
                os.remove(test_filename)
            
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الرفع: {e}")
        
        # حذف الملف التجريبي
        if os.path.exists(test_filename):
            os.remove(test_filename)
        
        return False

if __name__ == "__main__":
    print("🚀 بدء الحصول على OneDrive User token النهائي...")
    success = get_user_token_final()
    
    if success:
        print("\n✅ تم إعداد OneDrive User token بنجاح")
        print("🎉 الآن يمكن الرفع الحقيقي لـ OneDrive!")
    else:
        print("\n❌ فشل في إعداد OneDrive User token")
        sys.exit(1)
