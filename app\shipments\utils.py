"""
أدوات مساعدة لنظام إدارة الشحنات
Utility functions for Shipment Management System
"""

import uuid
import random
import string
import io
import base64
from datetime import datetime, timedelta
from flask import current_app
import json

# استيراد اختياري للمكتبات الخارجية
try:
    import requests
except ImportError:
    requests = None

try:
    import qrcode
except ImportError:
    qrcode = None

try:
    from geopy.geocoders import Nominatim
    from geopy.distance import geodesic
except ImportError:
    Nominatim = None
    geodesic = None

try:
    import googlemaps
except ImportError:
    googlemaps = None

def generate_tracking_number(prefix="SAS"):
    """إنشاء رقم تتبع فريد"""
    timestamp = datetime.now().strftime("%Y%m%d")
    random_part = ''.join(random.choices(string.digits, k=6))
    return f"{prefix}{timestamp}{random_part}"

def calculate_shipping_cost(shipment):
    """حساب تكلفة الشحن"""
    try:
        base_cost = 25.0  # التكلفة الأساسية
        
        # تكلفة الوزن (5 ريال لكل كيلو)
        weight_cost = (shipment.weight or 0) * 5.0
        
        # تكلفة الحجم (10 ريال لكل متر مكعب)
        volume_cost = (shipment.volume or 0) * 10.0
        
        # تكلفة المسافة
        distance_cost = 0
        if shipment.sender_latitude and shipment.sender_longitude and \
           shipment.recipient_latitude and shipment.recipient_longitude:
            distance = calculate_distance(
                (shipment.sender_latitude, shipment.sender_longitude),
                (shipment.recipient_latitude, shipment.recipient_longitude)
            )
            distance_cost = distance * 0.5  # 0.5 ريال لكل كيلومتر
        
        # تكلفة إضافية حسب نوع الشحنة
        type_multiplier = {
            'عادي': 1.0,
            'سريع': 1.5,
            'ليلي': 2.0,
            'نفس اليوم': 3.0,
            'دولي': 5.0,
            'قابل للكسر': 1.3,
            'خطر': 2.5,
            'سلسلة باردة': 2.0
        }
        
        multiplier = type_multiplier.get(shipment.shipment_type, 1.0)
        
        # تكلفة إضافية للأولوية
        priority_cost = {
            'عادي': 0,
            'عالي': 15,
            'عاجل': 30
        }
        
        priority_addition = priority_cost.get(shipment.priority, 0)
        
        # التكلفة الإجمالية
        total_cost = (base_cost + weight_cost + volume_cost + distance_cost) * multiplier + priority_addition
        
        # تكلفة التأمين (1% من القيمة المعلنة)
        if shipment.declared_value:
            insurance_cost = shipment.declared_value * 0.01
            total_cost += insurance_cost
        
        return round(total_cost, 2)
        
    except Exception as e:
        current_app.logger.error(f"Error calculating shipping cost: {e}")
        return 25.0  # التكلفة الافتراضية

def get_location_coordinates(address):
    """الحصول على إحداثيات الموقع من العنوان"""
    try:
        # محاولة استخدام Google Maps API أولاً
        if GOOGLEMAPS_AVAILABLE and hasattr(current_app.config, 'GOOGLE_MAPS_API_KEY'):
            gmaps = googlemaps.Client(key=current_app.config['GOOGLE_MAPS_API_KEY'])
            geocode_result = gmaps.geocode(address)

            if geocode_result:
                location = geocode_result[0]['geometry']['location']
                return {
                    'lat': location['lat'],
                    'lng': location['lng']
                }

        # استخدام Nominatim كبديل
        if GEOPY_AVAILABLE:
            geolocator = Nominatim(user_agent="saserp_shipment_system")
            location = geolocator.geocode(address)

            if location:
                return {
                    'lat': location.latitude,
                    'lng': location.longitude
                }

        # إذا لم تكن المكتبات متاحة، إرجاع إحداثيات افتراضية للرياض
        current_app.logger.warning(f"Geolocation libraries not available, using default coordinates for {address}")
        return {
            'lat': 24.7136,
            'lng': 46.6753
        }

    except Exception as e:
        current_app.logger.error(f"Error getting coordinates for {address}: {e}")
        return None

def calculate_distance(point1, point2):
    """حساب المسافة بين نقطتين بالكيلومتر"""
    try:
        return geodesic(point1, point2).kilometers
    except Exception as e:
        current_app.logger.error(f"Error calculating distance: {e}")
        return 0

def send_notification(shipment_id, notification_type, recipient, message, subject=None):
    """إرسال إشعار"""
    try:
        from .models import ShipmentNotification
        from app import db
        
        notification = ShipmentNotification(
            shipment_id=shipment_id,
            notification_type=notification_type,
            recipient=recipient,
            subject=subject,
            message=message
        )
        
        db.session.add(notification)
        
        # محاولة إرسال الإشعار فوراً
        success = False
        
        if notification_type == 'SMS':
            success = send_sms(recipient, message)
        elif notification_type == 'EMAIL':
            success = send_email(recipient, subject or 'إشعار شحنة', message)
        elif notification_type == 'PUSH':
            success = send_push_notification(recipient, message)
        
        if success:
            notification.status = 'مرسل'
            notification.sent_at = datetime.utcnow()
        else:
            notification.status = 'فشل'
        
        db.session.commit()
        return success
        
    except Exception as e:
        current_app.logger.error(f"Error sending notification: {e}")
        return False

def send_sms(phone_number, message):
    """إرسال رسالة SMS"""
    try:
        # هنا يمكن تكامل مع خدمة SMS مثل Twilio أو STC
        # مثال باستخدام Twilio
        if hasattr(current_app.config, 'TWILIO_ACCOUNT_SID'):
            from twilio.rest import Client
            
            client = Client(
                current_app.config['TWILIO_ACCOUNT_SID'],
                current_app.config['TWILIO_AUTH_TOKEN']
            )
            
            message = client.messages.create(
                body=message,
                from_=current_app.config['TWILIO_PHONE_NUMBER'],
                to=phone_number
            )
            
            return True
        
        # محاكاة الإرسال للتطوير
        current_app.logger.info(f"SMS sent to {phone_number}: {message}")
        return True
        
    except Exception as e:
        current_app.logger.error(f"Error sending SMS to {phone_number}: {e}")
        return False

def send_email(email, subject, message):
    """إرسال بريد إلكتروني"""
    try:
        # هنا يمكن تكامل مع خدمة البريد الإلكتروني
        # مثال باستخدام SendGrid
        if hasattr(current_app.config, 'SENDGRID_API_KEY'):
            import sendgrid
            from sendgrid.helpers.mail import Mail
            
            sg = sendgrid.SendGridAPIClient(api_key=current_app.config['SENDGRID_API_KEY'])
            
            mail = Mail(
                from_email=current_app.config.get('MAIL_DEFAULT_SENDER', '<EMAIL>'),
                to_emails=email,
                subject=subject,
                html_content=f"<p>{message}</p>"
            )
            
            response = sg.send(mail)
            return response.status_code == 202
        
        # محاكاة الإرسال للتطوير
        current_app.logger.info(f"Email sent to {email}: {subject} - {message}")
        return True
        
    except Exception as e:
        current_app.logger.error(f"Error sending email to {email}: {e}")
        return False

def send_push_notification(device_token, message):
    """إرسال إشعار push"""
    try:
        # هنا يمكن تكامل مع Firebase Cloud Messaging
        # محاكاة الإرسال للتطوير
        current_app.logger.info(f"Push notification sent to {device_token}: {message}")
        return True
        
    except Exception as e:
        current_app.logger.error(f"Error sending push notification: {e}")
        return False

def create_qr_code(data):
    """إنشاء QR Code"""
    try:
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(data)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        
        # تحويل إلى base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        buffer.seek(0)
        
        qr_code_base64 = base64.b64encode(buffer.getvalue()).decode()
        return f"data:image/png;base64,{qr_code_base64}"
        
    except Exception as e:
        current_app.logger.error(f"Error creating QR code: {e}")
        return None

def generate_barcode(data, barcode_type='code128'):
    """إنشاء باركود"""
    try:
        from barcode import get_barcode_class
        from barcode.writer import ImageWriter
        
        barcode_class = get_barcode_class(barcode_type)
        barcode = barcode_class(data, writer=ImageWriter())
        
        buffer = io.BytesIO()
        barcode.write(buffer)
        buffer.seek(0)
        
        barcode_base64 = base64.b64encode(buffer.getvalue()).decode()
        return f"data:image/png;base64,{barcode_base64}"
        
    except Exception as e:
        current_app.logger.error(f"Error creating barcode: {e}")
        return None

def estimate_delivery_time(shipment):
    """تقدير وقت التسليم"""
    try:
        base_hours = 24  # الوقت الأساسي بالساعات
        
        # حساب المسافة
        if shipment.sender_latitude and shipment.sender_longitude and \
           shipment.recipient_latitude and shipment.recipient_longitude:
            distance = calculate_distance(
                (shipment.sender_latitude, shipment.sender_longitude),
                (shipment.recipient_latitude, shipment.recipient_longitude)
            )
            
            # إضافة وقت حسب المسافة (ساعة لكل 100 كم)
            distance_hours = distance / 100
            base_hours += distance_hours
        
        # تعديل حسب نوع الشحنة
        type_hours = {
            'عادي': 0,
            'سريع': -12,
            'ليلي': -18,
            'نفس اليوم': -20,
            'دولي': 72,
            'قابل للكسر': 6,
            'خطر': 12,
            'سلسلة باردة': 0
        }
        
        base_hours += type_hours.get(shipment.shipment_type, 0)
        
        # تعديل حسب الأولوية
        priority_hours = {
            'عادي': 0,
            'عالي': -6,
            'عاجل': -12
        }
        
        base_hours += priority_hours.get(shipment.priority, 0)
        
        # التأكد من أن الوقت لا يقل عن ساعة واحدة
        base_hours = max(1, base_hours)
        
        return datetime.utcnow() + timedelta(hours=base_hours)
        
    except Exception as e:
        current_app.logger.error(f"Error estimating delivery time: {e}")
        return datetime.utcnow() + timedelta(hours=24)
