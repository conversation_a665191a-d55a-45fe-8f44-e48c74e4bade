/**
 * تفاصيل حساب المورد - JavaScript
 * Supplier Account Details - JavaScript
 */

// متغيرات عامة
let accountData = {
    account: {},
    transactions: [],
    chart: null
};

/**
 * تحميل تفاصيل الحساب
 */
function loadAccountDetails(accountId) {
    $.ajax({
        url: `/suppliers/api/account/${accountId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                accountData.account = response.account;
                displayAccountHeader(response.account);
                displayAccountInfo(response.account);
                displayAccountStats(response.account.statistics || {});
                loadTransactions(accountId);
                loadBalanceTrends(accountId);
            } else {
                showError('خطأ في تحميل تفاصيل الحساب: ' + response.message);
                loadSampleAccountData(accountId);
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في تحميل تفاصيل الحساب:', error);
            showError('خطأ في الاتصال بالخادم');
            loadSampleAccountData(accountId);
        }
    });
}

/**
 * تحميل بيانات تجريبية للحساب
 */
function loadSampleAccountData(accountId) {
    const sampleAccount = {
        account_id: accountId,
        supplier_code: 'SUP001',
        supplier_name: 'شركة الخليج للتجارة',
        account_number: 'SUP20240001',
        account_type: 'TRADE',
        account_status: 'ACTIVE',
        risk_rating: 'LOW',
        currency_code: 'SAR',
        credit_limit: 500000.00,
        current_balance: 150000.00,
        payment_terms_days: 30,
        contact_person: 'أحمد محمد',
        phone: '+************',
        email: '<EMAIL>',
        address: 'الرياض، المملكة العربية السعودية',
        tax_number: '*********',
        created_date: '2024-01-15',
        last_transaction_date: '2024-09-01',
        statistics: {
            total_transactions: 45,
            avg_payment_days: 28.5,
            total_debits: 2500000.00,
            total_credits: 2350000.00,
            largest_transaction: 75000.00,
            payment_reliability_score: 4.2
        }
    };
    
    accountData.account = sampleAccount;
    displayAccountHeader(sampleAccount);
    displayAccountInfo(sampleAccount);
    displayAccountStats(sampleAccount.statistics);
    loadSampleTransactions();
    loadSampleBalanceTrends();
}

/**
 * عرض رأس الحساب
 */
function displayAccountHeader(account) {
    $('#accountName').text(account.supplier_name);
    $('#accountCode').text(`كود المورد: ${account.supplier_code}`);
    $('#accountNumber').text(`رقم الحساب: ${account.account_number}`);
    
    // حالة الحساب
    const statusClass = getStatusClass(account.account_status);
    const statusText = getStatusText(account.account_status);
    $('#accountStatus').html(`<span class="status-badge status-${statusClass}">${statusText}</span>`);
    
    // تقييم المخاطر
    const riskClass = getRiskClass(account.risk_rating);
    const riskText = getRiskText(account.risk_rating);
    $('#riskRating').html(`<span class="risk-badge risk-${riskClass}">${riskText}</span>`);
}

/**
 * عرض معلومات الحساب
 */
function displayAccountInfo(account) {
    const balanceClass = getBalanceClass(account.current_balance);
    
    const infoHtml = `
        <div class="info-item">
            <span class="info-label">نوع الحساب</span>
            <span class="info-value">${getAccountTypeText(account.account_type)}</span>
        </div>
        <div class="info-item">
            <span class="info-label">الرصيد الحالي</span>
            <span class="info-value ${balanceClass}">${formatCurrency(account.current_balance)} ${account.currency_code}</span>
        </div>
        <div class="info-item">
            <span class="info-label">حد الائتمان</span>
            <span class="info-value">${formatCurrency(account.credit_limit)} ${account.currency_code}</span>
        </div>
        <div class="info-item">
            <span class="info-label">شروط الدفع</span>
            <span class="info-value">${account.payment_terms_days} يوم</span>
        </div>
        <div class="info-item">
            <span class="info-label">الشخص المسؤول</span>
            <span class="info-value">${account.contact_person || 'غير محدد'}</span>
        </div>
        <div class="info-item">
            <span class="info-label">رقم الهاتف</span>
            <span class="info-value">${account.phone || 'غير محدد'}</span>
        </div>
        <div class="info-item">
            <span class="info-label">البريد الإلكتروني</span>
            <span class="info-value">${account.email || 'غير محدد'}</span>
        </div>
        <div class="info-item">
            <span class="info-label">الرقم الضريبي</span>
            <span class="info-value">${account.tax_number || 'غير محدد'}</span>
        </div>
    `;
    
    $('#accountInfo').html(infoHtml);
}

/**
 * عرض إحصائيات الحساب
 */
function displayAccountStats(stats) {
    const statsHtml = `
        <div class="info-item">
            <span class="info-label">عدد المعاملات</span>
            <span class="info-value">${stats.total_transactions || 0}</span>
        </div>
        <div class="info-item">
            <span class="info-label">متوسط أيام الدفع</span>
            <span class="info-value">${stats.avg_payment_days ? stats.avg_payment_days.toFixed(1) : 0} يوم</span>
        </div>
        <div class="info-item">
            <span class="info-label">إجمالي المدين</span>
            <span class="info-value">${formatCurrency(stats.total_debits || 0)}</span>
        </div>
        <div class="info-item">
            <span class="info-label">إجمالي الدائن</span>
            <span class="info-value">${formatCurrency(stats.total_credits || 0)}</span>
        </div>
        <div class="info-item">
            <span class="info-label">أكبر معاملة</span>
            <span class="info-value">${formatCurrency(stats.largest_transaction || 0)}</span>
        </div>
        <div class="info-item">
            <span class="info-label">نقاط الموثوقية</span>
            <span class="info-value">${stats.payment_reliability_score ? stats.payment_reliability_score.toFixed(1) : 0}/5</span>
        </div>
    `;
    
    $('#accountStats').html(statsHtml);
}

/**
 * تحميل المعاملات
 */
function loadTransactions(accountId) {
    $.ajax({
        url: `/suppliers/api/account/${accountId}/transactions`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                accountData.transactions = response.transactions;
                displayTransactions(response.transactions);
            } else {
                showError('خطأ في تحميل المعاملات: ' + response.message);
                loadSampleTransactions();
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في تحميل المعاملات:', error);
            loadSampleTransactions();
        }
    });
}

/**
 * تحميل معاملات تجريبية
 */
function loadSampleTransactions() {
    const sampleTransactions = [
        {
            transaction_id: 1,
            transaction_type: 'INVOICE',
            transaction_date: '2024-09-01',
            due_date: '2024-10-01',
            amount: 75000.00,
            currency: 'SAR',
            description: 'فاتورة شراء مواد خام',
            reference_number: 'INV-2024-001',
            status: 'POSTED'
        },
        {
            transaction_id: 2,
            transaction_type: 'PAYMENT',
            transaction_date: '2024-08-28',
            amount: -50000.00,
            currency: 'SAR',
            description: 'دفعة نقدية',
            reference_number: 'PAY-2024-015',
            status: 'POSTED'
        },
        {
            transaction_id: 3,
            transaction_type: 'CREDIT_NOTE',
            transaction_date: '2024-08-25',
            amount: -5000.00,
            currency: 'SAR',
            description: 'إشعار دائن - خصم كمية',
            reference_number: 'CN-2024-003',
            status: 'POSTED'
        }
    ];
    
    accountData.transactions = sampleTransactions;
    displayTransactions(sampleTransactions);
}

/**
 * عرض المعاملات
 */
function displayTransactions(transactions) {
    if (!transactions || transactions.length === 0) {
        $('#transactionsList').html(`
            <div class="text-center py-4 text-muted">
                <i class="fas fa-inbox fa-3x mb-3"></i>
                <p>لا توجد معاملات</p>
            </div>
        `);
        return;
    }
    
    let html = '';
    transactions.forEach(transaction => {
        const typeClass = getTransactionTypeClass(transaction.transaction_type);
        const typeText = getTransactionTypeText(transaction.transaction_type);
        const amountClass = transaction.amount >= 0 ? 'text-danger' : 'text-success';
        
        html += `
            <div class="transaction-row">
                <div class="row align-items-center">
                    <div class="col-md-2">
                        <span class="transaction-type ${typeClass}">${typeText}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>${transaction.description}</strong><br>
                        <small class="text-muted">${transaction.reference_number || ''}</small>
                    </div>
                    <div class="col-md-2">
                        <span class="text-muted">${formatDate(transaction.transaction_date)}</span>
                        ${transaction.due_date ? `<br><small>استحقاق: ${formatDate(transaction.due_date)}</small>` : ''}
                    </div>
                    <div class="col-md-2 text-end">
                        <span class="fw-bold ${amountClass}">
                            ${formatCurrency(Math.abs(transaction.amount))} ${transaction.currency}
                        </span>
                    </div>
                    <div class="col-md-2 text-end">
                        <span class="badge bg-success">مرحل</span>
                    </div>
                    <div class="col-md-1 text-end">
                        <button class="btn btn-sm btn-outline-primary" onclick="viewTransactionDetails(${transaction.transaction_id})">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    });
    
    $('#transactionsList').html(html);
}

/**
 * تحميل اتجاهات الرصيد
 */
function loadBalanceTrends(accountId) {
    loadSampleBalanceTrends();
}

/**
 * تحميل اتجاهات رصيد تجريبية
 */
function loadSampleBalanceTrends() {
    const sampleData = [
        { month: 'أبريل', balance: 120000 },
        { month: 'مايو', balance: 135000 },
        { month: 'يونيو', balance: 142000 },
        { month: 'يوليو', balance: 138000 },
        { month: 'أغسطس', balance: 145000 },
        { month: 'سبتمبر', balance: 150000 }
    ];
    
    createBalanceTrendsChart(sampleData);
}

/**
 * إنشاء رسم بياني لاتجاهات الرصيد
 */
function createBalanceTrendsChart(data) {
    const ctx = document.getElementById('balanceTrendsChart').getContext('2d');
    
    if (accountData.chart) {
        accountData.chart.destroy();
    }
    
    accountData.chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.map(item => item.month),
            datasets: [{
                label: 'رصيد الحساب',
                data: data.map(item => item.balance),
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'الرصيد: ' + formatCurrency(context.parsed.y);
                        }
                    }
                }
            }
        }
    });
}

// دوال الإجراءات
function editAccount() {
    showNotification('سيتم فتح نافذة تعديل الحساب قريباً', 'info');
}

function generateStatement() {
    showNotification('سيتم إنشاء كشف الحساب قريباً', 'info');
}

function createPayment() {
    showNotification('سيتم فتح نافذة إنشاء الدفعة قريباً', 'info');
}

function viewReconciliation() {
    showNotification('سيتم فتح صفحة المطابقة قريباً', 'info');
}

function loadAllTransactions() {
    showNotification('سيتم عرض جميع المعاملات قريباً', 'info');
}

function addTransaction() {
    $('#addTransactionModal').modal('show');
}

function saveTransaction() {
    const formData = {
        transaction_type: $('#transactionType').val(),
        amount: $('#transactionAmount').val(),
        transaction_date: $('#transactionDate').val(),
        due_date: $('#dueDate').val(),
        currency: $('#transactionCurrency').val(),
        reference_number: $('#referenceNumber').val(),
        description: $('#transactionDescription').val()
    };
    
    if (!formData.transaction_type || !formData.amount || !formData.transaction_date) {
        showNotification('يرجى ملء الحقول المطلوبة', 'warning');
        return;
    }
    
    showNotification('سيتم حفظ المعاملة قريباً', 'info');
    $('#addTransactionModal').modal('hide');
}

function viewTransactionDetails(transactionId) {
    showNotification('سيتم عرض تفاصيل المعاملة قريباً', 'info');
}

// دوال مساعدة
function getStatusClass(status) {
    return status.toLowerCase();
}

function getStatusText(status) {
    const statusMap = {
        'ACTIVE': 'نشط',
        'SUSPENDED': 'معلق',
        'CLOSED': 'مغلق'
    };
    return statusMap[status] || status;
}

function getRiskClass(risk) {
    return risk.toLowerCase();
}

function getRiskText(risk) {
    const riskMap = {
        'LOW': 'منخفض المخاطر',
        'MEDIUM': 'متوسط المخاطر',
        'HIGH': 'عالي المخاطر',
        'CRITICAL': 'حرج'
    };
    return riskMap[risk] || risk;
}

function getAccountTypeText(type) {
    const typeMap = {
        'TRADE': 'تجاري',
        'SERVICE': 'خدمي',
        'CONTRACTOR': 'مقاول'
    };
    return typeMap[type] || type;
}

function getBalanceClass(balance) {
    if (balance > 0) return 'balance-positive';
    if (balance < 0) return 'balance-negative';
    return 'balance-zero';
}

function getTransactionTypeClass(type) {
    const typeMap = {
        'INVOICE': 'type-invoice',
        'PAYMENT': 'type-payment',
        'CREDIT_NOTE': 'type-credit',
        'DEBIT_NOTE': 'type-debit'
    };
    return typeMap[type] || 'type-invoice';
}

function getTransactionTypeText(type) {
    const typeMap = {
        'INVOICE': 'فاتورة',
        'PAYMENT': 'دفعة',
        'CREDIT_NOTE': 'إشعار دائن',
        'DEBIT_NOTE': 'إشعار مدين',
        'ADJUSTMENT': 'تسوية'
    };
    return typeMap[type] || type;
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount || 0);
}

function formatDate(dateString) {
    if (!dateString) return 'غير محدد';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function showError(message) {
    showNotification(message, 'error');
}

function showNotification(message, type = 'info') {
    if (typeof showAlert === 'function') {
        showAlert(message, type);
    } else {
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}
