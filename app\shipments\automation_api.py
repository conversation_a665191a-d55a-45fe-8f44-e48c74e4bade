#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API نظام الأتمتة التلقائية
Automation System API
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
import logging
from flask import current_app
from .automation_models import AutomationLog, AutomationSettings, AutomationRules, AutomationStatistics
from .automation import DeliveryOrderAutomation

logger = logging.getLogger(__name__)

class AutomationAPI:
    """واجهة برمجة التطبيقات لنظام الأتمتة"""
    
    def __init__(self):
        self.log_model = AutomationLog()
        self.settings_model = AutomationSettings()
        self.rules_model = AutomationRules()
        self.stats_model = AutomationStatistics()
        self.automation_engine = DeliveryOrderAutomation()
    
    def get_dashboard_data(self) -> Dict:
        """جلب بيانات لوحة الأتمتة"""
        try:
            # الإحصائيات اليومية
            daily_stats = self.stats_model.get_daily_stats()
            
            # الإحصائيات الإجمالية
            overall_stats = self.stats_model.get_overall_stats()
            
            # القواعد النشطة
            active_rules = self.rules_model.get_active_rules()
            
            # الأنشطة الحديثة
            recent_activities = self.log_model.get_recent_activities(10)
            
            # الإعدادات الحالية
            current_settings = self.settings_model.get_all_settings()
            
            # تنسيق البيانات للعرض
            dashboard_data = {
                'statistics': {
                    'total_automated_orders': overall_stats.get('total_automated_orders', 0),
                    'success_rate': daily_stats.get('success_rate', 0),
                    'active_rules': len(active_rules),
                    'processed_today': daily_stats.get('processed_today', 0)
                },
                'rules': self._format_rules_for_display(active_rules),
                'activities': self._format_activities_for_display(recent_activities),
                'settings': {
                    'auto_create_orders': current_settings.get('auto_create_orders', '0') == '1',
                    'auto_assign_agents': current_settings.get('auto_assign_agents', '0') == '1',
                    'auto_send_notifications': current_settings.get('auto_send_notifications', '0') == '1',
                    'auto_update_ratings': current_settings.get('auto_update_ratings', '0') == '1'
                }
            }
            
            return dashboard_data
            
        except Exception as e:
            logger.error(f"Error getting dashboard data: {e}")
            return self._get_default_dashboard_data()
    
    def update_automation_setting(self, setting_key: str, value: bool) -> Dict:
        """تحديث إعداد الأتمتة"""
        try:
            # تحويل القيمة المنطقية إلى نص
            str_value = '1' if value else '0'
            
            # تحديث الإعداد
            success = self.settings_model.update_setting(setting_key, str_value)
            
            if success:
                # تسجيل الإجراء
                self.log_model.log_action(
                    action='SETTING_UPDATE',
                    description=f'تم تحديث إعداد {setting_key} إلى {value}',
                    status='SUCCESS'
                )
                
                return {
                    'success': True,
                    'message': 'تم تحديث الإعداد بنجاح'
                }
            else:
                return {
                    'success': False,
                    'message': 'فشل في تحديث الإعداد'
                }
                
        except Exception as e:
            logger.error(f"Error updating setting {setting_key}: {e}")
            return {
                'success': False,
                'message': f'خطأ في تحديث الإعداد: {str(e)}'
            }
    
    def trigger_manual_automation(self, action_type: str, **kwargs) -> Dict:
        """تشغيل إجراء أتمتة يدوياً"""
        try:
            if action_type == 'update_agent_ratings':
                # تحديث تقييمات المخلصين
                self.automation_engine._update_agent_ratings()
                
                self.log_model.log_action(
                    action='MANUAL_RATING_UPDATE',
                    description='تم تحديث تقييمات المخلصين يدوياً',
                    status='SUCCESS'
                )
                
                return {
                    'success': True,
                    'message': 'تم تحديث تقييمات المخلصين بنجاح'
                }
                
            elif action_type == 'check_overdue_orders':
                # فحص الأوامر المتأخرة
                self.automation_engine._check_overdue_orders()
                
                self.log_model.log_action(
                    action='MANUAL_OVERDUE_CHECK',
                    description='تم فحص الأوامر المتأخرة يدوياً',
                    status='SUCCESS'
                )
                
                return {
                    'success': True,
                    'message': 'تم فحص الأوامر المتأخرة بنجاح'
                }
                
            else:
                return {
                    'success': False,
                    'message': 'نوع الإجراء غير مدعوم'
                }
                
        except Exception as e:
            logger.error(f"Error triggering manual automation {action_type}: {e}")
            return {
                'success': False,
                'message': f'خطأ في تنفيذ الإجراء: {str(e)}'
            }
    
    def get_automation_reports(self, period: str = 'week') -> Dict:
        """جلب تقارير الأتمتة"""
        try:
            if period == 'today':
                start_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                end_date = start_date + timedelta(days=1)
            elif period == 'week':
                start_date = datetime.now() - timedelta(days=7)
                end_date = datetime.now()
            elif period == 'month':
                start_date = datetime.now() - timedelta(days=30)
                end_date = datetime.now()
            else:
                start_date = datetime.now() - timedelta(days=7)
                end_date = datetime.now()
            
            # جلب إحصائيات الفترة
            stats = self.stats_model.get_daily_stats()
            
            return {
                'period': period,
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d'),
                'statistics': stats
            }
            
        except Exception as e:
            logger.error(f"Error getting automation reports: {e}")
            return {}
    
    def _format_rules_for_display(self, rules: List[Dict]) -> List[Dict]:
        """تنسيق القواعد للعرض"""
        formatted_rules = []
        
        for rule in rules:
            # تحديد وصف القاعدة
            description = self._get_rule_description(rule)
            
            formatted_rules.append({
                'id': rule['id'],
                'name': rule['rule_name'],
                'description': description,
                'status': 'active',
                'success_count': rule['success_count'],
                'failure_count': rule['failure_count'],
                'last_executed': rule['last_executed'].strftime('%Y-%m-%d %H:%M') if rule['last_executed'] else 'لم يتم التنفيذ'
            })
        
        return formatted_rules
    
    def _format_activities_for_display(self, activities: List[Dict]) -> List[Dict]:
        """تنسيق الأنشطة للعرض"""
        formatted_activities = []
        
        for activity in activities:
            # تحديد نوع الأيقونة
            icon_type = self._get_activity_icon(activity['action'])
            
            # تحديد الوقت النسبي
            time_ago = self._get_time_ago(activity['action_date'])
            
            formatted_activities.append({
                'action': activity['action'],
                'description': activity['description'],
                'time_ago': time_ago,
                'icon_type': icon_type,
                'status': activity['status']
            })
        
        return formatted_activities
    
    def _get_rule_description(self, rule: Dict) -> str:
        """الحصول على وصف القاعدة"""
        descriptions = {
            'CREATE_DELIVERY_ORDER': 'عند وصول الشحنة للميناء يتم إنشاء أمر التسليم تلقائياً',
            'ASSIGN_AGENT': 'يتم اختيار المخلص تلقائياً بناءً على التخصص والتقييم والخبرة',
            'SEND_NOTIFICATION': 'إرسال إشعارات فورية عند تغيير حالة الأمر أو حدوث تحديثات مهمة',
            'UPDATE_RATINGS': 'تحديث تقييمات المخلصين تلقائياً يومياً بناءً على الأداء والسرعة'
        }
        
        return descriptions.get(rule['action_type'], rule['rule_name'])
    
    def _get_activity_icon(self, action: str) -> str:
        """تحديد نوع الأيقونة للنشاط"""
        icon_types = {
            'AUTO_CREATE_ORDER': 'success',
            'ASSIGN_AGENT': 'primary',
            'SEND_NOTIFICATION': 'warning',
            'UPDATE_RATINGS': 'info',
            'SETTING_UPDATE': 'secondary'
        }
        
        return icon_types.get(action, 'primary')
    
    def _get_time_ago(self, action_date: datetime) -> str:
        """حساب الوقت النسبي"""
        now = datetime.now()
        diff = now - action_date
        
        if diff.days > 0:
            return f'منذ {diff.days} يوم'
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f'منذ {hours} ساعة'
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f'منذ {minutes} دقيقة'
        else:
            return 'منذ لحظات'
    
    def _get_default_dashboard_data(self) -> Dict:
        """بيانات افتراضية في حالة الخطأ"""
        return {
            'statistics': {
                'total_automated_orders': 0,
                'success_rate': 0,
                'active_rules': 0,
                'processed_today': 0
            },
            'rules': [],
            'activities': [],
            'settings': {
                'auto_create_orders': True,
                'auto_assign_agents': True,
                'auto_send_notifications': True,
                'auto_update_ratings': True
            }
        }

# إنشاء مثيل عام للاستخدام
automation_api = AutomationAPI()
