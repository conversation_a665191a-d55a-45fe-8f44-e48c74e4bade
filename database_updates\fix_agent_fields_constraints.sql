-- =====================================================
-- إصلاح قيود وفهارس حقول المخلص (أسماء مختصرة)
-- Fix Agent Fields Constraints with Shorter Names
-- التاريخ: 2025-08-20
-- =====================================================

-- حذف القيود والفهارس الموجودة (إن وجدت) لتجنب التضارب
BEGIN
    -- حذف القيود المرجعية
    EXECUTE IMMEDIATE 'ALTER TABLE automation_rules DROP CONSTRAINT fk_auto_rules_agent';
    EXCEPTION WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE automation_rules DROP CONSTRAINT fk_auto_rules_branch';
    EXCEPTION WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE automation_rules DROP CONSTRAINT fk_auto_rules_port';
    EXCEPTION WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE automation_rules DROP CONSTRAINT fk_auto_rules_updater';
    EXCEPTION WHEN OTHERS THEN NULL;
END;
/

-- حذف قيود التحقق
BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE automation_rules DROP CONSTRAINT chk_auto_agent_sel';
    EXCEPTION WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE automation_rules DROP CONSTRAINT chk_agent_criteria';
    EXCEPTION WHEN OTHERS THEN NULL;
END;
/

-- حذف الفهارس
BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX idx_auto_rules_agent';
    EXCEPTION WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX idx_auto_rules_branch';
    EXCEPTION WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX idx_auto_rules_port';
    EXCEPTION WHEN OTHERS THEN NULL;
END;
/

-- حذف الـ trigger
BEGIN
    EXECUTE IMMEDIATE 'DROP TRIGGER trg_auto_rules_agent_upd';
    EXCEPTION WHEN OTHERS THEN NULL;
END;
/

-- إضافة القيود المرجعية بأسماء مختصرة
ALTER TABLE automation_rules ADD CONSTRAINT fk_auto_rules_agent 
    FOREIGN KEY (selected_agent_id) REFERENCES customs_agents(id);

ALTER TABLE automation_rules ADD CONSTRAINT fk_auto_rules_branch 
    FOREIGN KEY (agent_branch_id) REFERENCES branches(brn_no);

ALTER TABLE automation_rules ADD CONSTRAINT fk_auto_rules_port 
    FOREIGN KEY (agent_port_id) REFERENCES customs_ports(id);

ALTER TABLE automation_rules ADD CONSTRAINT fk_auto_rules_updater 
    FOREIGN KEY (agent_settings_updated_by) REFERENCES users(id);

-- إضافة قيود التحقق بأسماء مختصرة
ALTER TABLE automation_rules ADD CONSTRAINT chk_auto_agent_sel 
    CHECK (auto_agent_selection IN (0, 1));

ALTER TABLE automation_rules ADD CONSTRAINT chk_agent_criteria 
    CHECK (agent_selection_criteria IN ('rating', 'experience', 'availability', 'specialization'));

-- إنشاء فهارس بأسماء مختصرة
CREATE INDEX idx_auto_rules_agent ON automation_rules(selected_agent_id);
CREATE INDEX idx_auto_rules_branch ON automation_rules(agent_branch_id);
CREATE INDEX idx_auto_rules_port ON automation_rules(agent_port_id);

-- إنشاء trigger بأسماء مختصرة
CREATE OR REPLACE TRIGGER trg_auto_rules_agent_upd
    BEFORE UPDATE OF selected_agent_id, agent_selection_criteria, agent_branch_id, 
                     agent_port_id, auto_agent_selection, agent_assignment_notes
    ON automation_rules
    FOR EACH ROW
BEGIN
    :NEW.agent_settings_updated_at := CURRENT_TIMESTAMP;
    IF :NEW.agent_settings_updated_by IS NULL THEN
        :NEW.agent_settings_updated_by := 1; -- افتراضي للمستخدم الأول
    END IF;
END;
/

-- تأكيد التغييرات
COMMIT;

-- عرض ملخص الإصلاح
SELECT 'تم إصلاح قيود ومؤشرات حقول المخلص بنجاح' AS status FROM dual;

PROMPT =====================================================
PROMPT تم إصلاح جميع القيود والفهارس بنجاح!
PROMPT =====================================================
