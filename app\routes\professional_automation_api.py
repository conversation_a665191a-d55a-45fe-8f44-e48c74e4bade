# -*- coding: utf-8 -*-
"""
API endpoints للتحكم في نظام الأتمتة الاحترافي
Professional Automation Control API
"""

from flask import Blueprint, request, jsonify
from datetime import datetime
import sys
import os

# إضافة مسار الخدمات
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'services'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# محاولة استيراد الخدمة مسبقاً
try:
    from app.services.flask_automation_service import flask_automation_service
    SERVICE_AVAILABLE = True
except ImportError:
    try:
        from flask_automation_service import flask_automation_service
        SERVICE_AVAILABLE = True
    except ImportError:
        SERVICE_AVAILABLE = False
        flask_automation_service = None

professional_automation_api = Blueprint('professional_automation_api', __name__)

@professional_automation_api.route('/api/automation/health', methods=['GET'])
def health_check():
    """فحص صحة API"""
    return jsonify({
        'success': True,
        'message': 'API الأتمتة يعمل بشكل طبيعي',
        'service_available': SERVICE_AVAILABLE,
        'timestamp': str(datetime.now())
    })

@professional_automation_api.route('/api/automation/simple-start', methods=['POST'])
def simple_start():
    """بدء مبسط للأتمتة"""
    return jsonify({
        'success': True,
        'message': 'تم تفعيل الأتمتة المبسطة',
        'status': 'simulated_running'
    })

@professional_automation_api.route('/api/automation/start', methods=['POST'])
def start_automation_api():
    """بدء خدمة الأتمتة"""
    if not SERVICE_AVAILABLE or not flask_automation_service:
        return jsonify({
            'success': False,
            'message': 'خدمة الأتمتة غير متاحة في هذا الوقت',
            'status': 'unavailable'
        }), 503

    try:
        success = flask_automation_service.start_service()

        if success:
            return jsonify({
                'success': True,
                'message': 'تم بدء خدمة الأتمتة بنجاح',
                'status': 'running'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في بدء خدمة الأتمتة',
                'status': 'stopped'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في بدء خدمة الأتمتة: {str(e)}',
            'status': 'error'
        }), 500

@professional_automation_api.route('/api/automation/stop', methods=['POST'])
def stop_automation_api():
    """إيقاف خدمة الأتمتة"""
    if not SERVICE_AVAILABLE:
        return jsonify({
            'success': False,
            'message': 'خدمة الأتمتة غير متاحة في هذا الوقت',
            'status': 'unavailable'
        }), 503

    try:
        success = stop_automation_service()

        if success:
            return jsonify({
                'success': True,
                'message': 'تم إيقاف خدمة الأتمتة بنجاح',
                'status': 'stopped'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في إيقاف خدمة الأتمتة',
                'status': 'unknown'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في إيقاف خدمة الأتمتة: {str(e)}',
            'status': 'error'
        }), 500

@professional_automation_api.route('/api/automation/status', methods=['GET'])
def get_automation_status_api():
    """الحصول على حالة خدمة الأتمتة"""
    if not SERVICE_AVAILABLE or not flask_automation_service:
        return jsonify({
            'success': True,
            'status': {
                'running': False,
                'thread_alive': False,
                'working_hours': True,
                'next_run': None,
                'settings': {
                    'auto_create_orders': True,
                    'auto_send_notifications': True,
                    'check_interval_minutes': 5,
                    'working_hours_start': '08:00',
                    'working_hours_end': '18:00',
                    'weekend_enabled': False,
                    'max_orders_per_batch': 10
                }
            },
            'message': 'خدمة الأتمتة غير متاحة (إعدادات افتراضية)'
        })

    try:
        status = flask_automation_service.get_service_status()

        return jsonify({
            'success': True,
            'status': status,
            'message': 'تم جلب حالة الأتمتة بنجاح'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب حالة الأتمتة: {str(e)}',
            'status': {'running': False, 'error': str(e)}
        }), 500

@professional_automation_api.route('/api/automation/settings', methods=['GET'])
def get_automation_settings_api():
    """الحصول على إعدادات الأتمتة"""
    # إعدادات افتراضية
    default_settings = {
        'auto_create_orders': True,
        'auto_send_notifications': True,
        'check_interval_minutes': 5,
        'working_hours_start': '08:00',
        'working_hours_end': '18:00',
        'weekend_enabled': False,
        'max_orders_per_batch': 10
    }

    if not SERVICE_AVAILABLE or not flask_automation_service:
        return jsonify({
            'success': True,
            'settings': default_settings,
            'message': 'إعدادات افتراضية (الخدمة غير متاحة)'
        })

    try:
        settings = flask_automation_service.settings

        return jsonify({
            'success': True,
            'settings': settings,
            'message': 'تم جلب إعدادات الأتمتة بنجاح'
        })

    except Exception as e:
        return jsonify({
            'success': True,
            'settings': default_settings,
            'message': f'إعدادات افتراضية (خطأ: {str(e)})'
        })

@professional_automation_api.route('/api/automation/settings', methods=['POST'])
def update_automation_settings_api():
    """تحديث إعدادات الأتمتة"""
    if not SERVICE_AVAILABLE or not flask_automation_service:
        return jsonify({
            'success': False,
            'message': 'خدمة الأتمتة غير متاحة للتحديث'
        }), 503

    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': 'لا توجد بيانات للتحديث'
            }), 400

        success = flask_automation_service.update_settings(data)

        if success:
            return jsonify({
                'success': True,
                'message': 'تم تحديث إعدادات الأتمتة بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في تحديث إعدادات الأتمتة'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في تحديث إعدادات الأتمتة: {str(e)}'
        }), 500

@professional_automation_api.route('/api/automation/test', methods=['POST'])
def test_automation_cycle_api():
    """اختبار دورة الأتمتة يدوياً"""
    if not SERVICE_AVAILABLE or not flask_automation_service:
        return jsonify({
            'success': False,
            'message': 'خدمة الأتمتة غير متاحة للاختبار'
        }), 503

    try:
        # تشغيل دورة واحدة للاختبار
        flask_automation_service.process_automation_cycle()

        return jsonify({
            'success': True,
            'message': 'تم تشغيل دورة اختبار الأتمتة بنجاح'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في اختبار الأتمتة: {str(e)}'
        }), 500

@professional_automation_api.route('/api/automation/pending-shipments', methods=['GET'])
def get_pending_shipments_api():
    """الحصول على الشحنات المعلقة"""
    if not SERVICE_AVAILABLE or not flask_automation_service:
        return jsonify({
            'success': True,
            'shipments': [],
            'count': 0,
            'message': 'خدمة الأتمتة غير متاحة'
        })

    try:
        shipments = flask_automation_service.get_pending_shipments()

        return jsonify({
            'success': True,
            'shipments': shipments,
            'count': len(shipments),
            'message': f'تم العثور على {len(shipments)} شحنة معلقة'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب الشحنات المعلقة: {str(e)}',
            'shipments': [],
            'count': 0
        }), 500

@professional_automation_api.route('/api/automation/logs', methods=['GET'])
def get_automation_logs():
    """الحصول على سجلات الأتمتة"""
    try:
        log_file = os.path.join(os.path.dirname(__file__), '..', '..', 'logs', 'automation.log')
        
        if not os.path.exists(log_file):
            return jsonify({
                'success': True,
                'logs': [],
                'message': 'لا توجد سجلات متاحة'
            })
        
        # قراءة آخر 100 سطر من السجل
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            recent_logs = lines[-100:] if len(lines) > 100 else lines
        
        return jsonify({
            'success': True,
            'logs': [line.strip() for line in recent_logs],
            'total_lines': len(lines),
            'message': f'تم جلب آخر {len(recent_logs)} سطر من السجلات'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب السجلات: {str(e)}',
            'logs': []
        }), 500

@professional_automation_api.route('/api/automation/statistics', methods=['GET'])
def get_automation_statistics():
    """الحصول على إحصائيات الأتمتة"""
    try:
        from database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # إحصائيات أوامر التسليم المُنشأة تلقائياً اليوم (Oracle syntax)
        today_query = """
            SELECT COUNT(*) as today_orders
            FROM delivery_orders
            WHERE TRUNC(created_date) = TRUNC(SYSDATE)
        """

        # إحصائيات هذا الأسبوع (Oracle syntax)
        week_query = """
            SELECT COUNT(*) as week_orders
            FROM delivery_orders
            WHERE created_date >= SYSDATE - 7
        """

        # إحصائيات الشحنات المعلقة (Oracle syntax) - مطابق لاستعلام الأتمتة
        pending_query = """
            SELECT COUNT(*) as pending_shipments
            FROM cargo_shipments
            WHERE shipment_status = 'arrived_port'
            AND id NOT IN (
                SELECT DISTINCT shipment_id
                FROM delivery_orders
                WHERE shipment_id IS NOT NULL
            )
        """
        
        today_result = db_manager.execute_query(today_query)
        week_result = db_manager.execute_query(week_query)
        pending_result = db_manager.execute_query(pending_query)
        
        statistics = {
            'today_orders': today_result[0][0] if today_result else 0,
            'week_orders': week_result[0][0] if week_result else 0,
            'pending_shipments': pending_result[0][0] if pending_result else 0
        }
        
        db_manager.close()
        
        return jsonify({
            'success': True,
            'statistics': statistics,
            'message': 'تم جلب إحصائيات الأتمتة بنجاح'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب إحصائيات الأتمتة: {str(e)}',
            'statistics': {}
        }), 500
