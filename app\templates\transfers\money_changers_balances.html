<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة أرصدة الصرافين/البنوك - نظام الفوجي</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom Styles -->
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --dark-bg: #2c3e50;
            --border-color: #dee2e6;
            --text-muted: #6c757d;
            --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        /* Professional Header */
        .dashboard-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0.5rem 0 0 0;
            font-weight: 300;
        }

        /* Enterprise Buttons */
        .btn-enterprise {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            border: none;
            box-shadow: var(--shadow);
        }

        .btn-primary-enterprise {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
            color: white;
        }

        .btn-primary-enterprise:hover {
            background: linear-gradient(135deg, #2980b9 0%, var(--secondary-color) 100%);
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .btn-secondary-enterprise {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            color: white;
        }

        .btn-secondary-enterprise:hover {
            background: linear-gradient(135deg, #7f8c8d 0%, #95a5a6 100%);
            transform: translateY(-2px);
        }

        /* Header Actions */
        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        /* Metrics Container */
        .metrics-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        /* Metric Cards */
        .metric-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--secondary-color), var(--success-color));
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 1.5rem 3rem rgba(0, 0, 0, 0.2);
        }

        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            background: linear-gradient(135deg, var(--secondary-color), var(--info-color));
        }

        .metric-trend {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .trend-positive {
            background: rgba(39, 174, 96, 0.1);
            color: var(--success-color);
        }

        .trend-negative {
            background: rgba(231, 76, 60, 0.1);
            color: var(--danger-color);
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin: 0.5rem 0;
            line-height: 1;
        }

        .metric-label {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-muted);
            margin-bottom: 0.25rem;
        }

        .metric-description {
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        /* Balance Card Colors */
        .balance-card.positive .metric-value {
            color: var(--success-color);
        }

        .balance-card.negative .metric-value {
            color: var(--danger-color);
        }

        .balance-card.neutral .metric-value {
            color: var(--warning-color);
        }

        /* Charts Container */
        .charts-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .chart-card {
            background: white;
            border-radius: 16px;
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .chart-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chart-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-color);
            margin: 0;
        }

        /* Money Changers Table */
        .money-changers-container {
            background: white;
            border-radius: 16px;
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .table-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-color);
            margin: 0;
        }

        .table-actions {
            display: flex;
            gap: 0.5rem;
        }

        /* Custom Table */
        .money-changers-table {
            width: 100%;
            margin: 0;
        }

        .money-changers-table th {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            font-weight: 600;
            padding: 1rem;
            text-align: center;
            border: none;
            font-size: 0.95rem;
        }

        .money-changers-table td {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
            vertical-align: middle;
        }

        .money-changers-table tbody tr:hover {
            background-color: rgba(52, 152, 219, 0.05);
            transition: background-color 0.3s ease;
        }

        /* Balance Status */
        .balance-positive {
            color: var(--success-color);
            font-weight: 600;
        }

        .balance-negative {
            color: var(--danger-color);
            font-weight: 600;
        }

        .balance-zero {
            color: var(--text-muted);
            font-weight: 500;
        }

        /* Currency Badge */
        .currency-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            background: linear-gradient(135deg, var(--info-color), var(--secondary-color));
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .charts-container {
                grid-template-columns: 1fr;
            }
            
            .metrics-container {
                grid-template-columns: 1fr;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .header-actions {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        /* Loading Animation */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--secondary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Alert Styles */
        .alert-custom {
            border-radius: 12px;
            border: none;
            box-shadow: var(--shadow);
            margin-bottom: 1rem;
        }

        /* Breadcrumb Navigation */
        .breadcrumb-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        .breadcrumb-custom {
            background: none;
            margin: 0;
            padding: 0;
            font-size: 0.95rem;
        }

        .breadcrumb-custom .breadcrumb-item {
            color: var(--text-secondary);
            font-weight: 500;
        }

        .breadcrumb-custom .breadcrumb-item.active {
            color: var(--primary-color);
            font-weight: 600;
        }

        .breadcrumb-custom .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: var(--text-muted);
            font-weight: 600;
            font-size: 1.1rem;
        }

        .breadcrumb-custom a {
            color: var(--secondary-color);
            text-decoration: none;
            transition: all 0.3s ease;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
        }

        .breadcrumb-custom a:hover {
            color: var(--primary-color);
            background-color: rgba(52, 152, 219, 0.1);
            transform: translateY(-1px);
        }

        .breadcrumb-icon {
            margin-left: 0.5rem;
            font-size: 0.9rem;
        }

        /* Filter Controls */
        .filter-controls {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }

        .filter-group label {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: 0.75rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
    </style>
</head>
<body>
    <!-- Professional Header -->
    <div class="dashboard-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-chart-line ms-3"></i>
                        إدارة أرصدة الصرافين/البنوك
                    </h1>
                    <p class="page-subtitle">
                        تحليلات متقدمة ومراقبة فورية لأرصدة حسابات الصرافين والبنوك مع تحليل شامل للاستحقاقات
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions d-flex gap-2 justify-content-lg-end">
                        <button class="btn-enterprise btn-secondary-enterprise" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                        <div class="dropdown">
                            <button class="btn-enterprise btn-secondary-enterprise dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-download"></i>
                                تصدير
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="exportToExcel()"><i class="fas fa-file-excel me-2"></i>Excel</a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportToPDF()"><i class="fas fa-file-pdf me-2"></i>PDF</a></li>
                                <li><a class="dropdown-item" href="#" onclick="printReport()"><i class="fas fa-print me-2"></i>طباعة</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Breadcrumb Navigation -->
        <div class="breadcrumb-container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-custom">
                    <li class="breadcrumb-item">
                        <a href="/" onclick="navigateToHome()">
                            <i class="fas fa-home breadcrumb-icon"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="/transfers/dashboard" onclick="navigateToTransfersDashboard()">
                            <i class="fas fa-money-bill-transfer breadcrumb-icon"></i>
                            نظام الحوالات
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <i class="fas fa-chart-line breadcrumb-icon"></i>
                        إدارة أرصدة الصرافين/البنوك
                    </li>
                </ol>
            </nav>
        </div>

        <!-- Filter Controls -->
        <div class="filter-controls">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="currencyFilter">العملة</label>
                    <select class="form-select" id="currencyFilter" onchange="applyFilters()">
                        <option value="">جميع العملات</option>
                        <!-- سيتم تحميل العملات ديناميكياً من قاعدة البيانات -->
                    </select>
                </div>
                <div class="filter-group">
                    <label for="balanceTypeFilter">نوع الرصيد</label>
                    <select class="form-select" id="balanceTypeFilter" onchange="applyFilters()">
                        <option value="">جميع الأنواع</option>
                        <option value="positive">أرصدة موجبة</option>
                        <option value="negative">أرصدة سالبة</option>
                        <option value="zero">أرصدة صفر</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="searchFilter">البحث</label>
                    <input type="text" class="form-control" id="searchFilter" placeholder="ابحث عن صراف أو بنك..." onkeyup="applyFilters()">
                </div>
                <div class="filter-group">
                    <button class="btn btn-primary w-100" onclick="applyFilters()">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </div>
        </div>

        <!-- Key Performance Metrics -->
        <div class="metrics-container">
            <div class="metric-card balance-card">
                <div class="metric-header">
                    <div class="metric-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="metric-trend trend-positive">
                        <i class="fas fa-arrow-up"></i>
                        +12.5%
                    </div>
                </div>
                <div class="metric-value" id="totalBalance">0.00</div>
                <div class="metric-label" id="totalBalanceLabel">إجمالي الأرصدة</div>
                <div class="metric-description">مقارنة بالشهر الماضي</div>
            </div>

            <div class="metric-card balance-card">
                <div class="metric-header">
                    <div class="metric-icon">
                        <i class="fas fa-university"></i>
                    </div>
                    <div class="metric-trend trend-positive">
                        <i class="fas fa-arrow-up"></i>
                        +5
                    </div>
                </div>
                <div class="metric-value" id="totalMoneyChangers">0</div>
                <div class="metric-label">إجمالي الصرافين/البنوك</div>
                <div class="metric-description">العدد الإجمالي النشط</div>
            </div>

            <div class="metric-card balance-card">
                <div class="metric-header">
                    <div class="metric-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <div class="metric-trend trend-positive">
                        <i class="fas fa-arrow-up"></i>
                        +8.2%
                    </div>
                </div>
                <div class="metric-value" id="totalPositiveBalances">0.00</div>
                <div class="metric-label" id="totalPositiveBalancesLabel">الأرصدة الموجبة</div>
                <div class="metric-description">إجمالي الأرصدة المدينة</div>
            </div>

            <div class="metric-card balance-card">
                <div class="metric-header">
                    <div class="metric-icon">
                        <i class="fas fa-minus-circle"></i>
                    </div>
                    <div class="metric-trend trend-negative">
                        <i class="fas fa-arrow-down"></i>
                        -3.1%
                    </div>
                </div>
                <div class="metric-value" id="totalNegativeBalances">0.00</div>
                <div class="metric-label" id="totalNegativeBalancesLabel">الأرصدة السالبة</div>
                <div class="metric-description">إجمالي الأرصدة الدائنة</div>
            </div>
        </div>

        <!-- Money Changers Table -->
        <div class="money-changers-container">
            <div class="table-header">
                <h3 class="table-title">
                    <i class="fas fa-university"></i>
                    قائمة أرصدة الصرافين/البنوك
                </h3>
                <div class="table-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshTable()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="exportTableToExcel()">
                        <i class="fas fa-file-excel"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="exportTableToPDF()">
                        <i class="fas fa-file-pdf"></i>
                    </button>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table money-changers-table" id="moneyChangersTable">
                    <thead>
                        <tr>
                            <th>الصراف/البنك</th>
                            <th>الكود</th>
                            <th>العملة</th>
                            <th>الرصيد الجاري</th>
                            <th>آخر معاملة</th>
                            <th>عدد المعاملات</th>
                            <th>متوسط الأيام</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody id="moneyChangersTableBody">
                        <!-- سيتم ملء البيانات بواسطة JavaScript -->
                        <tr>
                            <td colspan="8" class="text-center">
                                <div class="loading">
                                    <div class="spinner"></div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Charts Container -->
        <div class="charts-container">
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-coins"></i>
                        توزيع الأرصدة حسب العملة
                    </h3>
                    <div class="d-flex gap-1">
                        <button class="btn btn-sm btn-outline-secondary" onclick="refreshCurrencyChart()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="exportCurrencyChart()">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
                <div class="p-3">
                    <canvas id="currencyChart" width="400" height="300"></canvas>
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-line"></i>
                        اتجاهات الأرصدة
                    </h3>
                    <div class="d-flex gap-1">
                        <button class="btn btn-sm btn-outline-secondary" onclick="refreshTrendsChart()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="exportTrendsChart()">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
                <div class="p-3">
                    <canvas id="trendsChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // متغيرات عامة
        let dashboardData = {};
        let filteredData = [];
        let currencyChart = null;
        let trendsChart = null;

        // تحميل البيانات عند تحميل الصفحة
        $(document).ready(function() {
            loadCurrencies();
            loadDashboardData();
        });

        /**
         * تحميل قائمة العملات من قاعدة البيانات
         */
        async function loadCurrencies() {
            try {
                // إظهار مؤشر التحميل
                const currencyFilter = $('#currencyFilter');
                currencyFilter.html('<option value="">جاري تحميل العملات...</option>');

                const response = await fetch('/transfers/api/currencies');

                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.data) {
                        console.log(`✅ تم تحميل ${result.data.length} عملة بنجاح`);
                        populateCurrencyFilter(result.data);
                    } else {
                        console.error('خطأ في تحميل العملات:', result.message);
                        // استخدام عملات افتراضية في حالة الخطأ
                        populateCurrencyFilter(getDefaultCurrencies());
                    }
                } else {
                    console.error('HTTP Error:', response.status);
                    populateCurrencyFilter(getDefaultCurrencies());
                }
            } catch (error) {
                console.error('خطأ في تحميل العملات:', error);
                console.log('⚠️ سيتم استخدام العملات الافتراضية');
                populateCurrencyFilter(getDefaultCurrencies());
            }
        }

        /**
         * ملء قائمة العملات في الفلتر
         */
        function populateCurrencyFilter(currencies) {
            const currencyFilter = $('#currencyFilter');

            // الاحتفاظ بالخيار الافتراضي
            currencyFilter.html('<option value="">جميع العملات</option>');

            // ترتيب العملات: العملة الأساسية أولاً، ثم باقي العملات أبجدياً
            const sortedCurrencies = currencies.sort((a, b) => {
                // العملة الأساسية أولاً
                if (a.is_base_currency && !b.is_base_currency) return -1;
                if (!a.is_base_currency && b.is_base_currency) return 1;

                // ترتيب أبجدي للباقي
                return a.name_ar.localeCompare(b.name_ar, 'ar');
            });

            // إضافة العملات
            sortedCurrencies.forEach(currency => {
                const baseCurrencyIndicator = currency.is_base_currency ? ' ⭐' : '';
                const symbol = currency.symbol ? ` ${currency.symbol}` : '';
                const optionText = `${currency.name_ar} (${currency.code})${symbol}${baseCurrencyIndicator}`;
                currencyFilter.append(`<option value="${currency.code}">${optionText}</option>`);
            });
        }

        /**
         * عملات افتراضية في حالة فشل تحميل البيانات
         */
        function getDefaultCurrencies() {
            return [
                { code: 'SAR', name_ar: 'ريال سعودي', symbol: 'ر.س', is_base_currency: true },
                { code: 'USD', name_ar: 'دولار أمريكي', symbol: '$', is_base_currency: false },
                { code: 'EUR', name_ar: 'يورو', symbol: '€', is_base_currency: false },
                { code: 'AED', name_ar: 'درهم إماراتي', symbol: 'د.إ', is_base_currency: false },
                { code: 'KWD', name_ar: 'دينار كويتي', symbol: 'د.ك', is_base_currency: false },
                { code: 'QAR', name_ar: 'ريال قطري', symbol: 'ر.ق', is_base_currency: false },
                { code: 'OMR', name_ar: 'ريال عماني', symbol: 'ر.ع', is_base_currency: false },
                { code: 'BHD', name_ar: 'دينار بحريني', symbol: 'د.ب', is_base_currency: false },
                { code: 'JOD', name_ar: 'دينار أردني', symbol: 'د.أ', is_base_currency: false },
                { code: 'EGP', name_ar: 'جنيه مصري', symbol: 'ج.م', is_base_currency: false }
            ];
        }

        /**
         * تحميل بيانات لوحة التحكم
         */
        async function loadDashboardData() {
            try {
                showLoading();

                // تحميل البيانات الحقيقية من API
                const response = await fetch('/transfers/api/money-changers-balances');

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        dashboardData = {
                            statistics: result.statistics || {},
                            money_changers: result.money_changers || [],
                            charts: result.charts || {}
                        };
                    } else {
                        console.error('API Error:', result.message);
                        dashboardData = getDefaultData();
                    }
                } else {
                    console.error('HTTP Error:', response.status);
                    dashboardData = getDefaultData();
                }

                updateDashboard(dashboardData);
                hideLoading();

            } catch (error) {
                console.error('خطأ في تحميل البيانات:', error);
                // استخدام بيانات تجريبية في حالة الخطأ
                dashboardData = getDefaultData();
                updateDashboard(dashboardData);
                hideLoading();
            }
        }

        /**
         * بيانات تجريبية افتراضية
         */
        function getDefaultData() {
            return {
                statistics: {
                    total_balance: 2500000.00,
                    total_money_changers: 15,
                    total_positive_balances: 1800000.00,
                    total_negative_balances: 700000.00,
                    main_currency: 'SAR'
                },
                money_changers: [
                    {
                        id: 1,
                        name: 'البنك الأهلي السعودي',
                        code: 'MC001',
                        currency_code: 'SAR',
                        current_balance: 500000.00,
                        last_transaction_date: '2025-01-15',
                        total_transactions_count: 125,
                        average_days: 15,
                        status: 'نشط'
                    },
                    {
                        id: 2,
                        name: 'شركة الراجحي للصرافة',
                        code: 'MC002',
                        currency_code: 'USD',
                        current_balance: 75000.00,
                        last_transaction_date: '2025-01-14',
                        total_transactions_count: 89,
                        average_days: 12,
                        status: 'نشط'
                    },
                    {
                        id: 3,
                        name: 'بنك الرياض',
                        code: 'MC003',
                        currency_code: 'SAR',
                        current_balance: -25000.00,
                        last_transaction_date: '2025-01-13',
                        total_transactions_count: 67,
                        average_days: 18,
                        status: 'نشط'
                    },
                    {
                        id: 4,
                        name: 'شركة الأمان للصرافة',
                        code: 'MC004',
                        currency_code: 'EUR',
                        current_balance: 45000.00,
                        last_transaction_date: '2025-01-12',
                        total_transactions_count: 43,
                        average_days: 20,
                        status: 'نشط'
                    },
                    {
                        id: 5,
                        name: 'البنك السعودي الفرنسي',
                        code: 'MC005',
                        currency_code: 'SAR',
                        current_balance: 320000.00,
                        last_transaction_date: '2025-01-11',
                        total_transactions_count: 156,
                        average_days: 14,
                        status: 'نشط'
                    }
                ],
                charts: {
                    by_currency: [
                        { currency: 'SAR', amount: 1500000, count: 8 },
                        { currency: 'USD', amount: 600000, count: 4 },
                        { currency: 'EUR', amount: 300000, count: 2 },
                        { currency: 'AED', amount: 100000, count: 1 }
                    ],
                    trends: [
                        { month: 'يناير', balance: 2200000 },
                        { month: 'فبراير', balance: 2350000 },
                        { month: 'مارس', balance: 2500000 }
                    ]
                }
            };
        }

        /**
         * تحديث لوحة التحكم
         */
        function updateDashboard(data) {
            updateStatistics(data.statistics);
            updateMoneyChangersTable(data.money_changers);
            loadBalancesCharts(data);
            filteredData = data.money_changers;
        }

        /**
         * تحديث الإحصائيات
         */
        function updateStatistics(stats) {
            const mainCurrency = stats.main_currency || 'SAR';

            // تحديث البطاقات (استخدام formatCurrencyAbs للإحصائيات لإظهار القيم المطلقة)
            $('#totalBalance').text(formatCurrencyAbs(stats.total_balance || 0));
            $('#totalMoneyChangers').text(stats.total_money_changers || 0);
            $('#totalPositiveBalances').text(formatCurrencyAbs(stats.total_positive_balances || 0));
            $('#totalNegativeBalances').text(formatCurrencyAbs(stats.total_negative_balances || 0));

            // تحديث تسميات البطاقات بالعملات الصحيحة
            $('#totalBalanceLabel').text(`إجمالي الأرصدة (${mainCurrency})`);
            $('#totalPositiveBalancesLabel').text(`الأرصدة الموجبة (${mainCurrency})`);
            $('#totalNegativeBalancesLabel').text(`الأرصدة السالبة (${mainCurrency})`);

            // إضافة تأثيرات بصرية للبطاقات
            animateCounters();

            // تحديث ألوان البطاقات حسب القيم
            updateCardColors(stats);
        }

        /**
         * تحديث ألوان البطاقات حسب القيم
         */
        function updateCardColors(stats) {
            const totalBalance = stats.total_balance || 0;
            const positiveBalance = stats.total_positive_balances || 0;
            const negativeBalance = stats.total_negative_balances || 0;

            // بطاقة إجمالي الأرصدة
            const totalCard = $('#totalBalance').closest('.balance-card');
            if (totalBalance > 1000000) {
                totalCard.removeClass('negative neutral').addClass('positive');
            } else if (totalBalance > 500000) {
                totalCard.removeClass('positive negative').addClass('neutral');
            } else {
                totalCard.removeClass('positive neutral').addClass('negative');
            }

            // بطاقة الأرصدة الموجبة
            const positiveCard = $('#totalPositiveBalances').closest('.balance-card');
            if (positiveBalance > 1000000) {
                positiveCard.removeClass('negative neutral').addClass('positive');
            } else if (positiveBalance > 500000) {
                positiveCard.removeClass('positive negative').addClass('neutral');
            } else {
                positiveCard.removeClass('positive neutral').addClass('negative');
            }

            // بطاقة الأرصدة السالبة
            const negativeCard = $('#totalNegativeBalances').closest('.balance-card');
            if (negativeBalance > 500000) {
                negativeCard.removeClass('positive neutral').addClass('negative');
            } else if (negativeBalance > 200000) {
                negativeCard.removeClass('positive negative').addClass('neutral');
            } else {
                negativeCard.removeClass('negative neutral').addClass('positive');
            }
        }

        /**
         * تحميل الرسوم البيانية
         */
        function loadBalancesCharts(dashboard) {
            const chartsData = dashboard.charts || {};

            // رسم بياني لتوزيع الأرصدة حسب العملة
            createBalancesByCurrencyChart(chartsData.by_currency || []);

            // رسم بياني لاتجاهات الأرصدة
            createBalancesTrendsChart(chartsData.trends || []);
        }

        /**
         * إنشاء رسم بياني لتوزيع الأرصدة حسب العملة
         */
        function createBalancesByCurrencyChart(data) {
            const ctx = document.getElementById('currencyChart').getContext('2d');

            if (currencyChart) {
                currencyChart.destroy();
            }

            currencyChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: data.map(item => item.currency),
                    datasets: [{
                        data: data.map(item => item.amount),
                        backgroundColor: [
                            '#3498db',
                            '#2ecc71',
                            '#f39c12',
                            '#e74c3c',
                            '#9b59b6',
                            '#1abc9c'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const currency = context.label;
                                    const amount = formatCurrency(context.parsed);
                                    return `${currency}: ${amount}`;
                                }
                            }
                        }
                    }
                }
            });
        }

        /**
         * إنشاء رسم بياني لاتجاهات الأرصدة
         */
        function createBalancesTrendsChart(data) {
            const ctx = document.getElementById('trendsChart').getContext('2d');

            if (trendsChart) {
                trendsChart.destroy();
            }

            trendsChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.map(item => item.month),
                    datasets: [{
                        label: 'إجمالي الأرصدة',
                        data: data.map(item => item.balance),
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#3498db',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `الرصيد: ${formatCurrency(context.parsed.y)}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        }
                    }
                }
            });
        }

        /**
         * تحديث جدول الصرافين/البنوك
         */
        function updateMoneyChangersTable(moneyChangers) {
            const tbody = $('#moneyChangersTableBody');
            tbody.empty();

            if (!moneyChangers || moneyChangers.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="8" class="text-center text-muted">
                            <i class="fas fa-info-circle me-2"></i>
                            لا توجد بيانات للعرض
                        </td>
                    </tr>
                `);
                return;
            }

            moneyChangers.forEach(moneyChanger => {
                const balanceClass = getBalanceClass(moneyChanger.current_balance);
                const balanceFormatted = formatCurrency(moneyChanger.current_balance);
                const lastTransactionFormatted = formatDate(moneyChanger.last_transaction_date);

                tbody.append(`
                    <tr>
                        <td>
                            <strong>${moneyChanger.name}</strong>
                        </td>
                        <td>${moneyChanger.code}</td>
                        <td>
                            <span class="currency-badge">${moneyChanger.currency_code}</span>
                        </td>
                        <td class="${balanceClass}">
                            <strong>${balanceFormatted}</strong>
                        </td>
                        <td>${lastTransactionFormatted}</td>
                        <td>
                            <span class="badge bg-info">${moneyChanger.total_transactions_count || 0}</span>
                        </td>
                        <td>${moneyChanger.average_days || 0} يوم</td>
                        <td>
                            <span class="badge bg-success">${moneyChanger.status || 'نشط'}</span>
                        </td>
                    </tr>
                `);
            });
        }

        /**
         * تحديد فئة CSS للرصيد
         */
        function getBalanceClass(balance) {
            if (balance > 0) return 'balance-positive';
            if (balance < 0) return 'balance-negative';
            return 'balance-zero';
        }

        /**
         * تنسيق العملة (بالأرقام الإنجليزية مع الإشارة)
         */
        function formatCurrency(amount) {
            if (amount === null || amount === undefined) return '0.00';
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount); // إزالة Math.abs() للحفاظ على الإشارة السالبة
        }

        /**
         * تنسيق العملة بدون إشارة (للإحصائيات)
         */
        function formatCurrencyAbs(amount) {
            if (amount === null || amount === undefined) return '0.00';
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(Math.abs(amount));
        }

        /**
         * تنسيق التاريخ (بالتاريخ الميلادي والأرقام الإنجليزية)
         */
        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-GB'); // DD/MM/YYYY format with English numbers
        }

        /**
         * تطبيق الفلاتر
         */
        function applyFilters() {
            const currencyFilter = $('#currencyFilter').val();
            const balanceTypeFilter = $('#balanceTypeFilter').val();
            const searchFilter = $('#searchFilter').val().toLowerCase();

            let filtered = dashboardData.money_changers || [];

            // فلتر العملة
            if (currencyFilter) {
                filtered = filtered.filter(mc => mc.currency_code === currencyFilter);
            }

            // فلتر نوع الرصيد
            if (balanceTypeFilter) {
                filtered = filtered.filter(mc => {
                    const balance = mc.current_balance || 0;
                    switch (balanceTypeFilter) {
                        case 'positive': return balance > 0;
                        case 'negative': return balance < 0;
                        case 'zero': return balance === 0;
                        default: return true;
                    }
                });
            }

            // فلتر البحث
            if (searchFilter) {
                filtered = filtered.filter(mc =>
                    mc.name.toLowerCase().includes(searchFilter) ||
                    mc.code.toLowerCase().includes(searchFilter)
                );
            }

            filteredData = filtered;
            updateMoneyChangersTable(filtered);
        }

        /**
         * تحديث العدادات بتأثير متحرك
         */
        function animateCounters() {
            $('.metric-value').each(function() {
                const $this = $(this);
                const countTo = parseFloat($this.text().replace(/[^\d.-]/g, '')) || 0;

                $({ countNum: 0 }).animate({
                    countNum: countTo
                }, {
                    duration: 2000,
                    easing: 'swing',
                    step: function() {
                        if ($this.attr('id') === 'totalMoneyChangers') {
                            $this.text(Math.floor(this.countNum));
                        } else {
                            $this.text(formatCurrency(this.countNum));
                        }
                    },
                    complete: function() {
                        if ($this.attr('id') === 'totalMoneyChangers') {
                            $this.text(Math.floor(this.countNum));
                        } else {
                            $this.text(formatCurrency(this.countNum));
                        }
                    }
                });
            });
        }

        /**
         * إظهار حالة التحميل
         */
        function showLoading() {
            $('.metric-value').text('...');
            $('#moneyChangersTableBody').html(`
                <tr>
                    <td colspan="8" class="text-center">
                        <div class="loading">
                            <div class="spinner"></div>
                        </div>
                    </td>
                </tr>
            `);
        }

        /**
         * إخفاء حالة التحميل
         */
        function hideLoading() {
            // سيتم استبدال حالة التحميل بالبيانات الفعلية
        }

        /**
         * تحديث لوحة التحكم
         */
        function refreshDashboard() {
            loadCurrencies();
            loadDashboardData();
        }

        /**
         * تحديث الجدول
         */
        function refreshTable() {
            updateMoneyChangersTable(filteredData);
        }

        /**
         * تحديث رسم العملات
         */
        function refreshCurrencyChart() {
            if (dashboardData.charts && dashboardData.charts.by_currency) {
                createBalancesByCurrencyChart(dashboardData.charts.by_currency);
            }
        }

        /**
         * تحديث رسم الاتجاهات
         */
        function refreshTrendsChart() {
            if (dashboardData.charts && dashboardData.charts.trends) {
                createBalancesTrendsChart(dashboardData.charts.trends);
            }
        }

        /**
         * تصدير إلى Excel
         */
        function exportToExcel() {
            alert('تصدير Excel - قيد التطوير');
        }

        /**
         * تصدير إلى PDF
         */
        function exportToPDF() {
            alert('تصدير PDF - قيد التطوير');
        }

        /**
         * طباعة التقرير
         */
        function printReport() {
            window.print();
        }

        /**
         * تصدير رسم العملات
         */
        function exportCurrencyChart() {
            if (currencyChart) {
                const url = currencyChart.toBase64Image();
                const link = document.createElement('a');
                link.download = 'currency-chart.png';
                link.href = url;
                link.click();
            }
        }

        /**
         * تصدير رسم الاتجاهات
         */
        function exportTrendsChart() {
            if (trendsChart) {
                const url = trendsChart.toBase64Image();
                const link = document.createElement('a');
                link.download = 'trends-chart.png';
                link.href = url;
                link.click();
            }
        }

        /**
         * تصدير الجدول إلى Excel
         */
        function exportTableToExcel() {
            alert('تصدير جدول Excel - قيد التطوير');
        }

        /**
         * تصدير الجدول إلى PDF
         */
        function exportTableToPDF() {
            alert('تصدير جدول PDF - قيد التطوير');
        }

        /**
         * دوال التنقل في مسار التنقل
         */
        function navigateToHome() {
            window.location.href = '/';
        }

        function navigateToTransfersDashboard() {
            window.location.href = '/transfers/dashboard';
        }
    </script>
</body>
</html>
