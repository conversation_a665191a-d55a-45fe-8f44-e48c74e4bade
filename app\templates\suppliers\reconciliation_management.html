{% extends "base.html" %}

{% block title %}نظام المطابقة المتطور{% endblock %}

{% block extra_css %}
<style>
    .reconciliation-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border-left: 5px solid #007bff;
    }
    
    .reconciliation-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    }
    
    .reconciliation-card.completed {
        border-left-color: #28a745;
    }
    
    .reconciliation-card.in-progress {
        border-left-color: #ffc107;
    }
    
    .reconciliation-card.open {
        border-left-color: #17a2b8;
    }
    
    .cycle-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .cycle-info h6 {
        margin: 0;
        color: #333;
        font-weight: 600;
    }
    
    .cycle-info small {
        color: #666;
    }
    
    .cycle-status {
        text-align: right;
    }
    
    .status-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }
    
    .status-open { background: #d1ecf1; color: #0c5460; }
    .status-in-progress { background: #fff3cd; color: #856404; }
    .status-completed { background: #d4edda; color: #155724; }
    .status-cancelled { background: #f8d7da; color: #721c24; }
    
    .cycle-metrics {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .metric-item {
        text-align: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 10px;
    }
    
    .metric-value {
        font-size: 1.3rem;
        font-weight: bold;
        color: #007bff;
    }
    
    .metric-label {
        font-size: 0.8rem;
        color: #666;
        margin-top: 5px;
    }
    
    .progress-section {
        margin-bottom: 20px;
    }
    
    .progress-label {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
        font-size: 0.9rem;
        color: #666;
    }
    
    .cycle-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }
    
    .btn-sm-custom {
        padding: 5px 12px;
        font-size: 0.85rem;
        border-radius: 15px;
    }
    
    .filters-section {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .stats-overview {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
    }
    
    .stat-card.green {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    .stat-card.orange {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }
    
    .stat-card.red {
        background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .differences-summary {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    }
    
    .difference-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #eee;
    }
    
    .difference-item:last-child {
        border-bottom: none;
    }
    
    .difference-type {
        font-weight: 500;
        color: #333;
    }
    
    .difference-amount {
        font-weight: bold;
        font-size: 1.1rem;
    }
    
    .difference-positive { color: #28a745; }
    .difference-negative { color: #dc3545; }
    .difference-zero { color: #6c757d; }
    
    .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 300px;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #ddd;
    }
    
    .workflow-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    }
    
    .workflow-step {
        display: flex;
        align-items: center;
        padding: 15px;
        margin-bottom: 10px;
        border-radius: 10px;
        background: #f8f9fa;
        position: relative;
    }
    
    .workflow-step.active {
        background: #e7f3ff;
        border-left: 4px solid #007bff;
    }
    
    .workflow-step.completed {
        background: #d4edda;
        border-left: 4px solid #28a745;
    }
    
    .step-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        background: #007bff;
        color: white;
        font-weight: bold;
    }
    
    .step-icon.completed {
        background: #28a745;
    }
    
    .step-content h6 {
        margin: 0;
        color: #333;
    }
    
    .step-content small {
        color: #666;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-balance-scale-right text-primary"></i> نظام المطابقة المتطور</h2>
                    <p class="text-muted">مطابقة تلقائية لأرصدة الموردين مع كشف الفروقات وسير عمل الموافقات</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="showCreateCycleModal()">
                        <i class="fas fa-plus"></i> دورة مطابقة جديدة
                    </button>
                    <button class="btn btn-success" onclick="refreshReconciliation()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                    <button class="btn btn-info" onclick="showReportsModal()">
                        <i class="fas fa-chart-bar"></i> التقارير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Overview -->
    <div class="stats-overview">
        <div class="stat-card">
            <div class="stat-number" id="totalCycles">0</div>
            <div class="stat-label">إجمالي الدورات</div>
        </div>
        <div class="stat-card green">
            <div class="stat-number" id="completedCycles">0</div>
            <div class="stat-label">دورات مكتملة</div>
        </div>
        <div class="stat-card orange">
            <div class="stat-number" id="activeCycles">0</div>
            <div class="stat-label">دورات نشطة</div>
        </div>
        <div class="stat-card red">
            <div class="stat-number" id="totalDifferences">0</div>
            <div class="stat-label">إجمالي الفروقات</div>
        </div>
    </div>

    <!-- Current Workflow Status -->
    <div class="workflow-section">
        <h5><i class="fas fa-tasks"></i> سير العمل الحالي</h5>
        <div id="workflowSteps">
            <div class="workflow-step completed">
                <div class="step-icon completed">1</div>
                <div class="step-content">
                    <h6>إنشاء دورة المطابقة</h6>
                    <small>تم إنشاء دورة مطابقة شهر سبتمبر 2024</small>
                </div>
            </div>
            <div class="workflow-step active">
                <div class="step-icon">2</div>
                <div class="step-content">
                    <h6>جمع البيانات</h6>
                    <small>جاري جمع كشوفات الموردين وبيانات النظام</small>
                </div>
            </div>
            <div class="workflow-step">
                <div class="step-icon">3</div>
                <div class="step-content">
                    <h6>المطابقة التلقائية</h6>
                    <small>مطابقة الأرصدة وكشف الفروقات</small>
                </div>
            </div>
            <div class="workflow-step">
                <div class="step-icon">4</div>
                <div class="step-content">
                    <h6>مراجعة الفروقات</h6>
                    <small>مراجعة وتحليل الفروقات المكتشفة</small>
                </div>
            </div>
            <div class="workflow-step">
                <div class="step-icon">5</div>
                <div class="step-content">
                    <h6>الموافقة والإقفال</h6>
                    <small>موافقة المدير وإقفال دورة المطابقة</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Differences Summary -->
    <div class="differences-summary">
        <h5><i class="fas fa-exclamation-triangle"></i> ملخص الفروقات</h5>
        <div id="differencesSummary">
            <div class="difference-item">
                <span class="difference-type">فروقات التوقيت</span>
                <span class="difference-amount difference-positive">+15,000.00 ر.س</span>
            </div>
            <div class="difference-item">
                <span class="difference-type">فروقات المبالغ</span>
                <span class="difference-amount difference-negative">-8,500.00 ر.س</span>
            </div>
            <div class="difference-item">
                <span class="difference-type">معاملات مفقودة</span>
                <span class="difference-amount difference-negative">-12,000.00 ر.س</span>
            </div>
            <div class="difference-item">
                <span class="difference-type">معاملات مكررة</span>
                <span class="difference-amount difference-positive">+3,200.00 ر.س</span>
            </div>
            <div class="difference-item">
                <strong class="difference-type">صافي الفروقات</strong>
                <strong class="difference-amount difference-negative">-2,300.00 ر.س</strong>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" id="searchInput" placeholder="اسم الدورة أو المورد...">
            </div>
            <div class="col-md-2">
                <label class="form-label">حالة الدورة</label>
                <select class="form-select" id="statusFilter">
                    <option value="all">جميع الحالات</option>
                    <option value="OPEN">مفتوحة</option>
                    <option value="IN_PROGRESS">قيد التنفيذ</option>
                    <option value="COMPLETED">مكتملة</option>
                    <option value="CANCELLED">ملغية</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">نوع الدورة</label>
                <select class="form-select" id="typeFilter">
                    <option value="all">جميع الأنواع</option>
                    <option value="MONTHLY">شهرية</option>
                    <option value="QUARTERLY">ربع سنوية</option>
                    <option value="ANNUAL">سنوية</option>
                    <option value="ADHOC">خاصة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">نطاق المطابقة</label>
                <select class="form-select" id="scopeFilter">
                    <option value="all">جميع النطاقات</option>
                    <option value="ALL">جميع الموردين</option>
                    <option value="HIGH_VALUE">عالي القيمة</option>
                    <option value="HIGH_RISK">عالي المخاطر</option>
                    <option value="SELECTED">موردين محددين</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">تاريخ من</label>
                <input type="date" class="form-control" id="dateFrom">
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Reconciliation Cycles List -->
    <div id="reconciliationList">
        <div class="loading-container">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <div class="d-flex justify-content-between align-items-center mt-4">
        <div>
            <span class="text-muted">عرض <span id="showingFrom">0</span> إلى <span id="showingTo">0</span> من <span id="totalRecords">0</span> سجل</span>
        </div>
        <nav>
            <ul class="pagination" id="pagination">
                <!-- سيتم إنشاؤها ديناميكياً -->
            </ul>
        </nav>
    </div>
</div>

<!-- Create Cycle Modal -->
<div class="modal fade" id="createCycleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء دورة مطابقة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createCycleForm">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">اسم الدورة *</label>
                            <input type="text" class="form-control" id="cycleName" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">نوع الدورة</label>
                            <select class="form-select" id="cycleType">
                                <option value="MONTHLY">شهرية</option>
                                <option value="QUARTERLY">ربع سنوية</option>
                                <option value="ANNUAL">سنوية</option>
                                <option value="ADHOC">خاصة</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">من تاريخ *</label>
                            <input type="date" class="form-control" id="periodFrom" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">إلى تاريخ *</label>
                            <input type="date" class="form-control" id="periodTo" required>
                        </div>
                    </div>
                    <div class="mt-3">
                        <label class="form-label">نطاق المطابقة</label>
                        <select class="form-select" id="reconciliationScope">
                            <option value="ALL">جميع الموردين</option>
                            <option value="HIGH_VALUE">الموردين عالي القيمة</option>
                            <option value="HIGH_RISK">الموردين عالي المخاطر</option>
                            <option value="SELECTED">موردين محددين</option>
                        </select>
                    </div>
                    <div class="mt-3">
                        <label class="form-label">حد التسامح (ر.س)</label>
                        <input type="number" class="form-control" id="toleranceAmount" step="0.01" value="0.01">
                    </div>
                    <div class="mt-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="cycleNotes" rows="3"></textarea>
                    </div>
                    <div class="mt-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoProcess" checked>
                            <label class="form-check-label" for="autoProcess">
                                تشغيل المطابقة التلقائية فور الإنشاء
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="createReconciliationCycle()">إنشاء الدورة</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/reconciliation_management.js') }}"></script>
<script>
$(document).ready(function() {
    // تحميل البيانات عند تحميل الصفحة
    loadReconciliationData();
    
    // ربط أحداث الفلاتر
    $('#searchInput, #statusFilter, #typeFilter, #scopeFilter, #dateFrom').on('input change', function() {
        filterReconciliation();
    });
    
    // تعيين التواريخ الافتراضية
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    $('#periodFrom').val(formatDateForInput(firstDay));
    $('#periodTo').val(formatDateForInput(today));
    $('#cycleName').val('مطابقة ' + formatMonth(today));
});

function formatDateForInput(date) {
    return date.toISOString().split('T')[0];
}

function formatMonth(date) {
    const months = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return months[date.getMonth()] + ' ' + date.getFullYear();
}
</script>
{% endblock %}
