{% extends "base.html" %}

{% block title %}لوحة إفراج شركة الشحن{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-unlock-alt me-2 text-warning"></i>
                        لوحة إفراج شركة الشحن
                    </h2>
                    <p class="text-muted mb-0">إدارة ومتابعة عمليات إفراج الشحنات</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt me-1"></i>
                        تحديث
                    </button>
                    <button class="btn btn-success" onclick="exportReleaseReport()">
                        <i class="fas fa-download me-1"></i>
                        تصدير التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات حالات الإفراج -->
    <div class="row mb-4">
        {% if release_stats %}
            {% for stat in release_stats %}
            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
                <div class="card border-0 shadow-sm h-100 release-stat-card">
                    <div class="card-body text-center">
                        <div class="mb-2">
                            <i class="{{ stat[2] or 'fas fa-circle' }} fa-2x" style="color: {{ stat[1] or '#6c757d' }}"></i>
                        </div>
                        <h4 class="mb-1" style="color: {{ stat[1] or '#6c757d' }}">{{ stat[3] or 0 }}</h4>
                        <p class="text-muted mb-0 small">{{ stat[0] or 'غير محدد' }}</p>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد إحصائيات إفراج متاحة حالياً
                </div>
            </div>
        {% endif %}
    </div>

    <!-- الشحنات المحتاجة للإفراج والمحجوزة -->
    <div class="row">
        <!-- الشحنات المحتاجة للإفراج -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-hourglass-half me-2"></i>
                        الشحنات المحتاجة للإفراج
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم التتبع</th>
                                    <th>المرسل</th>
                                    <th>المستلم</th>
                                    <th>حالة الإفراج</th>
                                    <th>حالة الشحنة</th>
                                    <th>آخر تحديث</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if pending_releases %}
                                    {% for shipment in pending_releases %}
                                    <tr>
                                        <td>
                                            <strong>{{ shipment[1] or 'غير محدد' }}</strong>
                                        </td>
                                        <td>{{ shipment[2] or 'غير محدد' }}</td>
                                        <td>{{ shipment[3] or 'غير محدد' }}</td>
                                        <td>
                                            <span class="badge" style="background-color: {{ shipment[6] or '#6c757d' }}; color: white;">
                                                <i class="{{ shipment[7] or 'fas fa-circle' }} me-1"></i>
                                                {{ shipment[5] or 'غير محدد' }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                {{ shipment[10] or 'غير محدد' }}
                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                {{ shipment[8].strftime('%Y-%m-%d %H:%M') if shipment[8] else 'غير محدد' }}
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary btn-sm" 
                                                        onclick="updateReleaseStatus({{ shipment[0] }})">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-info btn-sm" 
                                                        onclick="showReleaseHistory({{ shipment[0] }})">
                                                    <i class="fas fa-history"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                            <p class="text-muted mb-0">لا توجد شحنات تحتاج إفراج حالياً</p>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- الشحنات المحجوزة -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-pause-circle me-2"></i>
                        شحنات محجوزة
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if on_hold_shipments %}
                    <div class="list-group list-group-flush">
                        {% for hold in on_hold_shipments %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ hold[1] or 'غير محدد' }}</h6>
                                    <p class="mb-1 small text-muted">{{ hold[2] or 'غير محدد' }} → {{ hold[3] or 'غير محدد' }}</p>
                                    {% if hold[4] %}
                                    <small class="text-danger">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        {{ hold[4] }}
                                    </small>
                                    {% endif %}
                                </div>
                                <small class="text-muted">
                                    {{ hold[5].strftime('%m-%d') if hold[5] else 'غير محدد' }}
                                </small>
                            </div>
                            <div class="mt-2">
                                <button class="btn btn-sm btn-outline-primary" onclick="updateReleaseStatus({{ hold[0] }})">
                                    <i class="fas fa-edit me-1"></i>
                                    تحديث
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <p class="text-muted">لا توجد شحنات محجوزة</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تحديث حالة الإفراج -->
<div id="updateReleaseOverlay" class="custom-modal-overlay" style="display: none;">
    <div class="custom-modal">
        <div class="custom-modal-header">
            <h5>
                <i class="fas fa-unlock-alt me-2"></i>
                تحديث حالة الإفراج
            </h5>
            <button type="button" class="custom-close-btn" onclick="closeReleaseModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="custom-modal-body">
            <div id="updateReleaseAlert" class="alert alert-info" style="display: none;">
                <i class="fas fa-info-circle me-2"></i>
                <span id="updateReleaseMessage"></span>
            </div>
            
            <form id="updateReleaseForm">
                <input type="hidden" id="releaseShipmentId" name="shipment_id">
                
                <div class="mb-3">
                    <label for="newReleaseStatus" class="form-label">
                        <i class="fas fa-tasks me-1"></i>
                        حالة الإفراج الجديدة
                    </label>
                    <select class="form-select" id="newReleaseStatus" name="new_release_status" required>
                        <option value="">اختر حالة الإفراج</option>
                        <option value="pending">⏳ في انتظار الإفراج</option>
                        <option value="documents_review">📋 مراجعة المستندات</option>
                        <option value="payment_verification">💳 التحقق من المدفوعات</option>
                        <option value="quality_check">🔍 فحص الجودة</option>
                        <option value="approved">✅ معتمد للإفراج</option>
                        <option value="released">🚚 تم الإفراج</option>
                        <option value="on_hold">⏸️ محجوز مؤقت</option>
                        <option value="rejected">❌ مرفوض الإفراج</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="releaseNotes" class="form-label">
                        <i class="fas fa-sticky-note me-1"></i>
                        ملاحظات الإفراج
                    </label>
                    <textarea class="form-control" id="releaseNotes" name="release_notes" rows="3" 
                              placeholder="أي ملاحظات أو أسباب التحديث..."></textarea>
                </div>
            </form>
        </div>
        
        <div class="custom-modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeReleaseModal()">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </button>
            <button type="button" class="btn btn-primary" id="submitReleaseBtn" onclick="submitReleaseUpdate()">
                <i class="fas fa-save me-1"></i>
                تحديث الإفراج
            </button>
        </div>
    </div>
</div>

<script>
// تحديث اللوحة
function refreshDashboard() {
    location.reload();
}

// تصدير تقرير الإفراج
function exportReleaseReport() {
    alert('تصدير تقرير الإفراج - قريباً');
}

// تحديث حالة الإفراج
function updateReleaseStatus(shipmentId) {
    console.log('🔄 فتح نافذة تحديث الإفراج للشحنة:', shipmentId);

    // التحقق من وجود العناصر
    const shipmentIdInput = document.getElementById('releaseShipmentId');
    const form = document.getElementById('updateReleaseForm');
    const alertDiv = document.getElementById('updateReleaseAlert');
    const overlay = document.getElementById('updateReleaseOverlay');
    const statusSelect = document.getElementById('newReleaseStatus');

    if (!shipmentIdInput || !form || !alertDiv || !overlay || !statusSelect) {
        console.error('❌ عناصر النافذة غير موجودة');
        alert('خطأ: عناصر النافذة غير موجودة');
        return;
    }

    // تعيين البيانات
    shipmentIdInput.value = shipmentId;
    form.reset();
    shipmentIdInput.value = shipmentId; // إعادة تعيين بعد reset

    // إخفاء التنبيهات
    alertDiv.style.display = 'none';

    // عرض النافذة
    overlay.style.display = 'flex';

    setTimeout(() => {
        overlay.classList.add('show');
        console.log('✅ تم عرض النافذة');
    }, 10);

    setTimeout(() => {
        statusSelect.focus();
        console.log('✅ تم التركيز على حقل الحالة');
    }, 300);
}

// إغلاق نافذة الإفراج
function closeReleaseModal() {
    const overlay = document.getElementById('updateReleaseOverlay');
    overlay.classList.remove('show');
    
    setTimeout(() => {
        overlay.style.display = 'none';
    }, 300);
}

// إرسال تحديث الإفراج
function submitReleaseUpdate() {
    console.log('📤 بدء إرسال تحديث الإفراج...');

    const form = document.getElementById('updateReleaseForm');
    const submitBtn = document.getElementById('submitReleaseBtn');

    const shipmentId = document.getElementById('releaseShipmentId').value;
    const newStatus = document.getElementById('newReleaseStatus').value;
    const notes = document.getElementById('releaseNotes').value;

    console.log('📋 DEBUG: البيانات المرسلة:', {
        shipment_id: shipmentId,
        new_release_status: newStatus,
        release_notes: notes
    });

    if (!shipmentId || !newStatus) {
        showReleaseAlert('يرجى اختيار حالة الإفراج الجديدة', 'warning');
        return;
    }

    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري التحديث...';

    // إنشاء FormData يدوياً للتأكد من الأسماء الصحيحة
    const formData = new FormData();
    formData.append('shipment_id', shipmentId);
    formData.append('new_release_status', newStatus);
    formData.append('release_notes', notes);

    console.log('📤 DEBUG: إرسال البيانات إلى الخادم...');

    fetch('/shipments/api/update-release-status', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('📡 DEBUG: استجابة الخادم:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('📊 DEBUG: بيانات الاستجابة:', data);

        if (data.success) {
            showReleaseAlert('تم تحديث حالة الإفراج بنجاح!', 'success');
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showReleaseAlert('خطأ: ' + (data.message || 'فشل في تحديث الإفراج'), 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الطلب:', error);
        showReleaseAlert('حدث خطأ في الاتصال بالخادم: ' + error.message, 'danger');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-save me-1"></i> تحديث الإفراج';
    });
}

// عرض رسائل التنبيه
function showReleaseAlert(message, type) {
    const alertDiv = document.getElementById('updateReleaseAlert');
    const messageSpan = document.getElementById('updateReleaseMessage');
    
    alertDiv.className = `alert alert-${type}`;
    messageSpan.textContent = message;
    alertDiv.style.display = 'block';
}

// عرض تاريخ الإفراج
function showReleaseHistory(shipmentId) {
    alert('تاريخ الإفراج - قريباً\nسيعرض جميع تغييرات حالة الإفراج للشحنة');
}

// إعداد الأحداث
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل لوحة الإفراج');

    // فحص العناصر المطلوبة
    const requiredElements = [
        'updateReleaseOverlay',
        'updateReleaseForm',
        'releaseShipmentId',
        'newReleaseStatus',
        'releaseNotes',
        'submitReleaseBtn'
    ];

    let missingElements = [];
    requiredElements.forEach(id => {
        const element = document.getElementById(id);
        if (!element) {
            missingElements.push(id);
        }
    });

    if (missingElements.length > 0) {
        console.error('❌ عناصر مفقودة:', missingElements);
    } else {
        console.log('✅ جميع العناصر المطلوبة موجودة');
    }

    // إعداد أحداث النافذة
    const overlay = document.getElementById('updateReleaseOverlay');
    if (overlay) {
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                closeReleaseModal();
            }
        });
        console.log('✅ تم إعداد حدث النقر خارج النافذة');
    }

    // إعداد مفتاح Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const overlay = document.getElementById('updateReleaseOverlay');
            if (overlay && overlay.style.display !== 'none') {
                closeReleaseModal();
            }
        }
    });
    console.log('✅ تم إعداد مفتاح Escape');

    console.log('🎉 تم إعداد لوحة الإفراج بنجاح');
});
</script>

<style>
/* تحسينات CSS للوحة الإفراج */
.release-stat-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.release-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

/* النافذة المخصصة */
.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.custom-modal-overlay.show {
    opacity: 1;
}

.custom-modal {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.7);
    transition: transform 0.3s ease;
}

.custom-modal-overlay.show .custom-modal {
    transform: scale(1);
}

.custom-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #dee2e6;
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.custom-modal-header h5 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.custom-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.custom-close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.custom-modal-body {
    padding: 1.5rem;
}

.custom-modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0 0 12px 12px;
}
</style>
{% endblock %}
