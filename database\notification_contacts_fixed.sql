-- إنشاء نظام إدارة جهات الاتصال للإشعارات (محسن)
-- Notification Contacts Management System (Fixed)

-- 1. جدول جهات الاتصال للإشعارات
CREATE TABLE notif_contacts (
    id NUMBER PRIMARY KEY,
    contact_name VARCHAR2(200) NOT NULL,
    contact_type VARCHAR2(50) NOT NULL, -- CUSTOMER, DRIVER, AGENT, MANAGER, EXTERNAL
    
    -- معلومات الاتصال
    phone_number VARCHAR2(20),
    email_address VARCHAR2(200),
    whatsapp_number VARCHAR2(20),
    
    -- معلومات إضافية
    company_name VARCHAR2(200),
    department VARCHAR2(100),
    position VARCHAR2(100),
    notes CLOB,
    
    -- إعدادات الإشعارات
    preferred_channels VARCHAR2(200), -- SMS,EMAIL,WHATSAPP,PUSH
    notif_preferences CLOB, -- JSON: أنواع الإشعارات المفضلة
    timezone VARCHAR2(50) DEFAULT 'Asia/Riyadh',
    language_pref VARCHAR2(10) DEFAULT 'ar',
    
    -- حالة جهة الاتصال
    is_active NUMBER(1) DEFAULT 1,
    is_vip NUMBER(1) DEFAULT 0,
    priority_level NUMBER DEFAULT 5, -- 1-10 (10 = أولوية عالية)
    
    -- معلومات النظام
    created_at DATE DEFAULT SYSDATE,
    created_by NUMBER,
    updated_at DATE DEFAULT SYSDATE,
    updated_by NUMBER
);

-- 2. إنشاء sequence
CREATE SEQUENCE notif_contacts_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 3. جدول مجموعات جهات الاتصال
CREATE TABLE notif_contact_groups (
    id NUMBER PRIMARY KEY,
    group_name VARCHAR2(200) NOT NULL,
    group_description CLOB,
    group_type VARCHAR2(50), -- DEPARTMENT, PROJECT, EMERGENCY, CUSTOM
    
    -- إعدادات المجموعة
    default_channels VARCHAR2(200),
    notif_schedule CLOB, -- JSON: أوقات الإرسال المفضلة
    
    -- حالة المجموعة
    is_active NUMBER(1) DEFAULT 1,
    created_at DATE DEFAULT SYSDATE,
    created_by NUMBER
);

-- 4. إنشاء sequence للمجموعات
CREATE SEQUENCE notif_contact_groups_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 5. جدول ربط جهات الاتصال بالمجموعات
CREATE TABLE notif_contact_members (
    id NUMBER PRIMARY KEY,
    contact_id NUMBER NOT NULL,
    group_id NUMBER NOT NULL,
    role_in_group VARCHAR2(50), -- MEMBER, ADMIN, MODERATOR
    added_at DATE DEFAULT SYSDATE,
    added_by NUMBER,
    
    CONSTRAINT ncm_contact_fk FOREIGN KEY (contact_id) REFERENCES notif_contacts(id),
    CONSTRAINT ncm_group_fk FOREIGN KEY (group_id) REFERENCES notif_contact_groups(id),
    CONSTRAINT ncm_unique UNIQUE (contact_id, group_id)
);

-- 6. إنشاء sequence لأعضاء المجموعات
CREATE SEQUENCE notif_contact_members_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 7. إدراج بيانات تجريبية
INSERT INTO notif_contacts (id, contact_name, contact_type, phone_number, email_address, whatsapp_number, preferred_channels, is_vip, priority_level) VALUES 
(notif_contacts_seq.NEXTVAL, 'أحمد محمد - مدير العمليات', 'MANAGER', '+966501234567', '<EMAIL>', '+966501234567', 'SMS,EMAIL,WHATSAPP', 1, 10);

INSERT INTO notif_contacts (id, contact_name, contact_type, phone_number, email_address, preferred_channels, priority_level) VALUES 
(notif_contacts_seq.NEXTVAL, 'فاطمة أحمد - خدمة العملاء', 'MANAGER', '+966507654321', '<EMAIL>', 'EMAIL,SMS', 8);

INSERT INTO notif_contacts (id, contact_name, contact_type, phone_number, whatsapp_number, preferred_channels, priority_level) VALUES 
(notif_contacts_seq.NEXTVAL, 'محمد علي - سائق', 'DRIVER', '+966512345678', '+966512345678', 'SMS,WHATSAPP', 6);

INSERT INTO notif_contacts (id, contact_name, contact_type, phone_number, email_address, preferred_channels, priority_level) VALUES 
(notif_contacts_seq.NEXTVAL, 'سارة خالد - عميل VIP', 'CUSTOMER', '+966598765432', '<EMAIL>', 'EMAIL,SMS', 9);

INSERT INTO notif_contacts (id, contact_name, contact_type, phone_number, email_address, preferred_channels, priority_level) VALUES 
(notif_contacts_seq.NEXTVAL, 'عبدالله أحمد - مخلص جمركي', 'AGENT', '+966556789012', '<EMAIL>', 'SMS,EMAIL', 7);

-- 8. إنشاء مجموعات تجريبية
INSERT INTO notif_contact_groups (id, group_name, group_description, group_type, default_channels) VALUES 
(notif_contact_groups_seq.NEXTVAL, 'الإدارة العليا', 'مجموعة المديرين وصناع القرار', 'DEPARTMENT', 'EMAIL,SMS,WHATSAPP');

INSERT INTO notif_contact_groups (id, group_name, group_description, group_type, default_channels) VALUES 
(notif_contact_groups_seq.NEXTVAL, 'السائقين النشطين', 'مجموعة السائقين المتاحين للعمل', 'DEPARTMENT', 'SMS,WHATSAPP');

INSERT INTO notif_contact_groups (id, group_name, group_description, group_type, default_channels) VALUES 
(notif_contact_groups_seq.NEXTVAL, 'العملاء المميزين', 'مجموعة العملاء ذوي الأولوية العالية', 'CUSTOM', 'EMAIL,SMS');

INSERT INTO notif_contact_groups (id, group_name, group_description, group_type, default_channels) VALUES 
(notif_contact_groups_seq.NEXTVAL, 'الطوارئ', 'مجموعة الاتصال في حالات الطوارئ', 'EMERGENCY', 'SMS,WHATSAPP,EMAIL');

-- 9. ربط جهات الاتصال بالمجموعات
INSERT INTO notif_contact_members (id, contact_id, group_id, role_in_group) VALUES 
(notif_contact_members_seq.NEXTVAL, 1, 1, 'ADMIN'); -- أحمد في الإدارة العليا

INSERT INTO notif_contact_members (id, contact_id, group_id, role_in_group) VALUES 
(notif_contact_members_seq.NEXTVAL, 2, 1, 'MEMBER'); -- فاطمة في الإدارة العليا

INSERT INTO notif_contact_members (id, contact_id, group_id, role_in_group) VALUES 
(notif_contact_members_seq.NEXTVAL, 3, 2, 'MEMBER'); -- محمد في السائقين

INSERT INTO notif_contact_members (id, contact_id, group_id, role_in_group) VALUES 
(notif_contact_members_seq.NEXTVAL, 4, 3, 'MEMBER'); -- سارة في العملاء المميزين

INSERT INTO notif_contact_members (id, contact_id, group_id, role_in_group) VALUES 
(notif_contact_members_seq.NEXTVAL, 1, 4, 'ADMIN'); -- أحمد في الطوارئ

-- 10. إنشاء فهارس للأداء
CREATE INDEX idx_notif_contacts_type ON notif_contacts(contact_type);
CREATE INDEX idx_notif_contacts_active ON notif_contacts(is_active);
CREATE INDEX idx_notif_contacts_vip ON notif_contacts(is_vip);
CREATE INDEX idx_notif_contacts_priority ON notif_contacts(priority_level);

-- 11. إنشاء view للاستعلامات السريعة
CREATE OR REPLACE VIEW v_notif_contacts_summary AS
SELECT 
    nc.id,
    nc.contact_name,
    nc.contact_type,
    nc.phone_number,
    nc.email_address,
    nc.whatsapp_number,
    nc.preferred_channels,
    nc.is_vip,
    nc.priority_level,
    nc.is_active,
    COUNT(ncm.group_id) as groups_count
FROM notif_contacts nc
LEFT JOIN notif_contact_members ncm ON nc.id = ncm.contact_id
WHERE nc.is_active = 1
GROUP BY nc.id, nc.contact_name, nc.contact_type, nc.phone_number, 
         nc.email_address, nc.whatsapp_number, nc.preferred_channels, 
         nc.is_vip, nc.priority_level, nc.is_active
ORDER BY nc.priority_level DESC, nc.contact_name;

COMMIT;
