<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الحل الجديد لزر الحذف</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h2>اختبار الحل الجديد لزر الحذف</h2>
        
        <div class="alert alert-info">
            <strong>الطريقة الجديدة:</strong> استخدام data attributes و event listeners بدلاً من onclick
            <br>افتح Developer Tools (F12) لمراقبة Console
        </div>
        
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>اسم المندوب</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>أحمد محمد الأحمد</td>
                    <td>
                        <div class="btn-group" role="group">
                            <button class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-danger btn-sm delete-rep-btn" 
                                    data-rep-id="1"
                                    data-rep-name="أحمد محمد الأحمد">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>سارة أحمد علي</td>
                    <td>
                        <div class="btn-group" role="group">
                            <button class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-danger btn-sm delete-rep-btn" 
                                    data-rep-id="2"
                                    data-rep-name="سارة أحمد علي">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>محمد عبدالله (اسم مع أقواس)</td>
                    <td>
                        <div class="btn-group" role="group">
                            <button class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-danger btn-sm delete-rep-btn" 
                                    data-rep-id="3"
                                    data-rep-name="محمد عبدالله (اسم مع أقواس)">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <div class="mt-3">
            <button class="btn btn-primary" onclick="testBasicFunction()">
                اختبار دالة بسيطة
            </button>
            <button class="btn btn-info" onclick="checkDeleteButtons()">
                فحص أزرار الحذف
            </button>
        </div>
        
        <div id="status" class="mt-3"></div>
    </div>

    <script>
        function testBasicFunction() {
            console.log('Basic test function called');
            alert('دالة الاختبار الأساسية تعمل!');
        }
        
        function checkDeleteButtons() {
            const buttons = document.querySelectorAll('.delete-rep-btn');
            const status = document.getElementById('status');
            status.innerHTML = `<div class="alert alert-info">تم العثور على ${buttons.length} أزرار حذف</div>`;
            console.log('Delete buttons found:', buttons.length);
            
            buttons.forEach((btn, index) => {
                console.log(`Button ${index + 1}:`, {
                    id: btn.getAttribute('data-rep-id'),
                    name: btn.getAttribute('data-rep-name')
                });
            });
        }
        
        // إضافة event listeners لأزرار الحذف
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded, setting up delete buttons');
            
            const deleteButtons = document.querySelectorAll('.delete-rep-btn');
            console.log('Found delete buttons:', deleteButtons.length);
            
            deleteButtons.forEach(function(button) {
                button.addEventListener('click', function() {
                    const repId = this.getAttribute('data-rep-id');
                    const repName = this.getAttribute('data-rep-name');
                    
                    console.log('Delete button clicked for:', repId, repName);
                    
                    if (confirm(`هل أنت متأكد من حذف المندوب "${repName}"؟\n\nتحذير: سيتم حذف جميع البيانات المرتبطة بهذا المندوب نهائياً.`)) {
                        console.log('User confirmed deletion');
                        
                        // إظهار loading indicator
                        this.disabled = true;
                        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                        
                        // للاختبار - عرض رسالة بدلاً من الإرسال الفعلي
                        setTimeout(() => {
                            alert(`تم تأكيد حذف المندوب: ${repName} (ID: ${repId})`);
                            
                            // إعادة تعيين الزر
                            this.disabled = false;
                            this.innerHTML = '<i class="fas fa-trash"></i>';
                        }, 2000);
                        
                        // في البيئة الحقيقية سيتم إنشاء form وإرساله
                        console.log('Would submit form to:', `/purchase-commissions/representatives/delete/${repId}`);
                        
                    } else {
                        console.log('User cancelled deletion');
                    }
                });
            });
            
            // تحديث status
            const status = document.getElementById('status');
            status.innerHTML = `<div class="alert alert-success">تم تحميل الصفحة وإعداد ${deleteButtons.length} أزرار حذف بنجاح</div>`;
        });
    </script>
</body>
</html>
