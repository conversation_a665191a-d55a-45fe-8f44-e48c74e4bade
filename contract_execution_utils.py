#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
أدوات تحديث حالة تنفيذ العقود
"""

import logging
from oracle_manager import OracleManager

logger = logging.getLogger(__name__)

def update_contract_execution_status(contract_id, oracle_manager=None):
    """
    تحديث حالة تنفيذ العقد بناءً على أوامر الشراء المرتبطة
    
    Args:
        contract_id (int): رقم العقد
        oracle_manager (OracleManager): مدير قاعدة البيانات (اختياري)
    
    Returns:
        dict: نتيجة التحديث
    """
    try:
        if oracle_manager is None:
            oracle_manager = OracleManager()
        
        logger.info(f"🔄 تحديث حالة تنفيذ العقد {contract_id}...")
        
        # الخطوة 1: حساب الكميات المنفذة لكل صنف في العقد
        calculate_query = """
        SELECT 
            cd.ITEM_ID,
            cd.QUANTITY as CONTRACT_QTY,
            NVL(SUM(pi.QUANTITY), 0) as ACTUAL_EXECUTED_QTY,
            cd.QUANTITY - NVL(SUM(pi.QUANTITY), 0) as ACTUAL_REMAINING_QTY
        FROM CONTRACT_DETAILS cd
        LEFT JOIN PURCHASE_ORDERS po ON cd.CONTRACT_ID = po.CONTRACT_ID
        LEFT JOIN PO_ITEMS pi ON po.ID = pi.PO_ID 
            AND cd.ITEM_ID = pi.ITEM_CODE
        WHERE cd.CONTRACT_ID = :1
        GROUP BY cd.ITEM_ID, cd.QUANTITY
        ORDER BY cd.ITEM_ID
        """
        
        calculated_results = oracle_manager.execute_query(calculate_query, [contract_id])
        
        if not calculated_results:
            logger.warning(f"⚠️ لم يتم العثور على تفاصيل للعقد {contract_id}")
            return {'success': False, 'message': 'لم يتم العثور على تفاصيل العقد'}
        
        # الخطوة 2: تحديث كل صنف في تفاصيل العقد
        update_sql = """
        UPDATE CONTRACT_DETAILS 
        SET EXECUTED_QUANTITY = :1,
            REMAINING_QUANTITY = :2,
            UPDATED_AT = SYSDATE
        WHERE CONTRACT_ID = :3 
        AND ITEM_ID = :4
        """
        
        updated_items = 0
        
        for row in calculated_results:
            item_id, contract_qty, actual_exec, actual_remain = row
            
            try:
                params = [actual_exec, actual_remain, contract_id, item_id]
                result = oracle_manager.execute_update(update_sql, params)
                
                if result > 0:
                    updated_items += 1
                    logger.info(f"✅ تم تحديث الصنف {item_id}: منفذة={actual_exec}, متبقية={actual_remain}")
                
            except Exception as e:
                logger.error(f"❌ خطأ في تحديث الصنف {item_id}: {e}")
        
        # الخطوة 3: حساب وتحديث حالة العقد
        contract_status_query = """
        SELECT 
            COUNT(*) as TOTAL_ITEMS,
            SUM(CASE WHEN EXECUTED_QUANTITY >= QUANTITY THEN 1 ELSE 0 END) as FULLY_EXECUTED_ITEMS,
            SUM(CASE WHEN EXECUTED_QUANTITY > 0 AND EXECUTED_QUANTITY < QUANTITY THEN 1 ELSE 0 END) as PARTIALLY_EXECUTED_ITEMS
        FROM CONTRACT_DETAILS
        WHERE CONTRACT_ID = :1
        """
        
        status_result = oracle_manager.execute_query(contract_status_query, [contract_id])
        
        if status_result:
            total_items, fully_executed, partially_executed = status_result[0]
            
            # تحديد الحالة الجديدة
            if fully_executed == total_items:
                new_status = 'FULLY_EXECUTED'
                status_ar = 'منفذ كلياً'
            elif partially_executed > 0 or fully_executed > 0:
                new_status = 'PARTIALLY_EXECUTED'
                status_ar = 'منفذ جزئياً'
            else:
                # فحص إذا كان العقد في حالة مسودة
                current_status_query = "SELECT CONTRACT_STATUS FROM CONTRACTS WHERE CONTRACT_ID = :1"
                current_status_result = oracle_manager.execute_query(current_status_query, [contract_id])
                
                if current_status_result and current_status_result[0][0] == 'DRAFT':
                    new_status = 'DRAFT'
                    status_ar = 'مسودة'
                else:
                    new_status = 'APPROVED'
                    status_ar = 'معتمد'
            
            # تحديث حالة العقد
            update_contract_sql = """
            UPDATE CONTRACTS 
            SET CONTRACT_STATUS = :1,
                UPDATED_AT = SYSDATE
            WHERE CONTRACT_ID = :2
            """
            
            try:
                result = oracle_manager.execute_update(update_contract_sql, [new_status, contract_id])
                if result > 0:
                    logger.info(f"✅ تم تحديث حالة العقد {contract_id} إلى: {status_ar}")
                    
                    return {
                        'success': True,
                        'message': f'تم تحديث حالة العقد إلى: {status_ar}',
                        'contract_status': new_status,
                        'contract_status_ar': status_ar,
                        'updated_items': updated_items,
                        'total_items': total_items,
                        'fully_executed': fully_executed,
                        'partially_executed': partially_executed
                    }
                else:
                    logger.error(f"❌ فشل في تحديث حالة العقد {contract_id}")
                    return {'success': False, 'message': 'فشل في تحديث حالة العقد'}
                    
            except Exception as e:
                logger.error(f"❌ خطأ في تحديث حالة العقد {contract_id}: {e}")
                return {'success': False, 'message': f'خطأ في تحديث حالة العقد: {e}'}
        
        return {'success': False, 'message': 'فشل في حساب حالة العقد'}
        
    except Exception as e:
        logger.error(f"❌ خطأ عام في تحديث حالة العقد {contract_id}: {e}")
        return {'success': False, 'message': f'خطأ عام: {e}'}


def update_all_contracts_execution_status(oracle_manager=None):
    """
    تحديث حالة تنفيذ جميع العقود
    
    Args:
        oracle_manager (OracleManager): مدير قاعدة البيانات (اختياري)
    
    Returns:
        dict: نتيجة التحديث
    """
    try:
        if oracle_manager is None:
            oracle_manager = OracleManager()
        
        logger.info("🔄 تحديث حالة تنفيذ جميع العقود...")
        
        # جلب جميع العقود
        contracts_query = "SELECT CONTRACT_ID FROM CONTRACTS ORDER BY CONTRACT_ID"
        contracts = oracle_manager.execute_query(contracts_query)
        
        if not contracts:
            return {'success': False, 'message': 'لا توجد عقود للتحديث'}
        
        updated_contracts = 0
        failed_contracts = 0
        
        for row in contracts:
            contract_id = row[0]
            result = update_contract_execution_status(contract_id, oracle_manager)
            
            if result['success']:
                updated_contracts += 1
            else:
                failed_contracts += 1
                logger.error(f"❌ فشل في تحديث العقد {contract_id}: {result['message']}")
        
        logger.info(f"✅ تم تحديث {updated_contracts} عقد بنجاح، فشل في {failed_contracts} عقد")
        
        return {
            'success': True,
            'message': f'تم تحديث {updated_contracts} عقد بنجاح',
            'updated_contracts': updated_contracts,
            'failed_contracts': failed_contracts,
            'total_contracts': len(contracts)
        }
        
    except Exception as e:
        logger.error(f"❌ خطأ عام في تحديث جميع العقود: {e}")
        return {'success': False, 'message': f'خطأ عام: {e}'}


def get_contract_execution_summary(contract_id, oracle_manager=None):
    """
    الحصول على ملخص حالة تنفيذ العقد
    
    Args:
        contract_id (int): رقم العقد
        oracle_manager (OracleManager): مدير قاعدة البيانات (اختياري)
    
    Returns:
        dict: ملخص حالة التنفيذ
    """
    try:
        if oracle_manager is None:
            oracle_manager = OracleManager()
        
        # جلب تفاصيل العقد
        details_query = """
        SELECT 
            cd.ITEM_ID,
            cd.ITEM_NAME,
            cd.QUANTITY as ORIGINAL_QTY,
            cd.EXECUTED_QUANTITY,
            cd.REMAINING_QUANTITY,
            CASE 
                WHEN cd.EXECUTED_QUANTITY = 0 THEN 'لم ينفذ'
                WHEN cd.EXECUTED_QUANTITY < cd.QUANTITY THEN 'منفذ جزئياً'
                WHEN cd.EXECUTED_QUANTITY >= cd.QUANTITY THEN 'منفذ كلياً'
            END AS EXECUTION_STATUS
        FROM CONTRACT_DETAILS cd
        WHERE cd.CONTRACT_ID = :1
        ORDER BY cd.DETAIL_ID
        """
        
        details = oracle_manager.execute_query(details_query, [contract_id])
        
        # جلب معلومات العقد
        contract_query = """
        SELECT 
            CONTRACT_NUMBER,
            CONTRACT_STATUS,
            SUPPLIER_NAME
        FROM CONTRACTS
        WHERE CONTRACT_ID = :1
        """
        
        contract_info = oracle_manager.execute_query(contract_query, [contract_id])
        
        if not contract_info:
            return {'success': False, 'message': 'العقد غير موجود'}
        
        contract_number, contract_status, supplier_name = contract_info[0]
        
        # حساب الإحصائيات
        total_items = len(details)
        fully_executed = sum(1 for row in details if row[3] >= row[2])  # executed >= original
        partially_executed = sum(1 for row in details if 0 < row[3] < row[2])  # 0 < executed < original
        not_executed = sum(1 for row in details if row[3] == 0)  # executed = 0
        
        return {
            'success': True,
            'contract_id': contract_id,
            'contract_number': contract_number,
            'contract_status': contract_status,
            'supplier_name': supplier_name,
            'total_items': total_items,
            'fully_executed_items': fully_executed,
            'partially_executed_items': partially_executed,
            'not_executed_items': not_executed,
            'details': details
        }
        
    except Exception as e:
        logger.error(f"❌ خطأ في جلب ملخص العقد {contract_id}: {e}")
        return {'success': False, 'message': f'خطأ: {e}'}
