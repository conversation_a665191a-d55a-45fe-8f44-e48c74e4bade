-- نظام الإشعارات المتقدم متعدد القنوات
-- Advanced Multi-Channel Notifications System

-- 1. إنشاء جدول سجل الإشعارات
CREATE TABLE notifications_log (
    id NUMBER PRIMARY KEY,
    recipient_type VARCHAR2(50) NOT NULL, -- user, agent, admin, system
    recipient_id NUMBER,
    recipient_name VARCHAR2(200),
    recipient_email VARCHAR2(100),
    recipient_phone VARCHAR2(20),
    
    -- تفاصيل الإشعار
    template_key VARCHAR2(100) NOT NULL,
    notification_title VARCHAR2(500),
    notification_content CLOB,
    
    -- القنوات
    channels_attempted VARCHAR2(200), -- SMS,EMAIL,WHATSAPP,PUSH
    channels_successful VARCHAR2(200),
    channels_failed VARCHAR2(200),
    
    -- البيانات والحالة
    notification_data CLOB, -- JSON data
    priority VARCHAR2(20) DEFAULT 'normal', -- urgent, high, normal, low
    status VARCHAR2(20) DEFAULT 'PENDING', -- PENDING, SENT, FAILED, PARTIAL
    
    -- التوقيتات
    sent_at DATE DEFAULT SYSDATE,
    delivered_at DATE,
    read_at DATE,
    
    -- معلومات إضافية
    error_message CLOB,
    retry_count NUMBER DEFAULT 0,
    max_retries NUMBER DEFAULT 3,
    
    -- ربط بالكيانات
    order_id NUMBER,
    shipment_id NUMBER,
    
    created_at DATE DEFAULT SYSDATE
);

-- 2. إنشاء sequence لسجل الإشعارات
CREATE SEQUENCE notifications_log_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 3. إنشاء جدول الإشعارات المجدولة
CREATE TABLE scheduled_notifications (
    id NUMBER PRIMARY KEY,
    order_id NUMBER,
    shipment_id NUMBER,
    recipient_id NUMBER,
    
    -- تفاصيل الجدولة
    notification_type VARCHAR2(100) NOT NULL, -- deadline_reminder, overdue_alert, status_update
    template_key VARCHAR2(100) NOT NULL,
    scheduled_time DATE NOT NULL,
    
    -- القنوات والمعاملات
    channels VARCHAR2(200), -- SMS,EMAIL,WHATSAPP
    notification_data CLOB, -- JSON parameters
    
    -- الحالة والتنفيذ
    status VARCHAR2(20) DEFAULT 'PENDING', -- PENDING, SENT, FAILED, CANCELLED
    sent_at DATE,
    error_message CLOB,
    
    -- إعدادات التكرار
    is_recurring NUMBER(1) DEFAULT 0,
    recurrence_pattern VARCHAR2(100), -- daily, weekly, monthly
    next_occurrence DATE,
    
    created_at DATE DEFAULT SYSDATE,
    created_by NUMBER
);

-- 4. إنشاء sequence للإشعارات المجدولة
CREATE SEQUENCE scheduled_notifications_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 5. إنشاء جدول قوالب الإشعارات
CREATE TABLE notification_templates (
    id NUMBER PRIMARY KEY,
    template_key VARCHAR2(100) UNIQUE NOT NULL,
    template_name VARCHAR2(200) NOT NULL,
    template_category VARCHAR2(50), -- delivery, shipment, system, reminder
    
    -- محتوى القوالب
    sms_template CLOB,
    email_subject_template VARCHAR2(500),
    email_body_template CLOB,
    whatsapp_template CLOB,
    push_title_template VARCHAR2(200),
    push_body_template VARCHAR2(500),
    
    -- إعدادات القالب
    default_channels VARCHAR2(200), -- SMS,EMAIL,WHATSAPP,PUSH
    priority VARCHAR2(20) DEFAULT 'normal',
    is_active NUMBER(1) DEFAULT 1,
    
    -- متغيرات القالب
    template_variables CLOB, -- JSON array of variable names
    
    -- معلومات النظام
    created_at DATE DEFAULT SYSDATE,
    created_by NUMBER,
    updated_at DATE,
    updated_by NUMBER
);

-- 6. إنشاء sequence لقوالب الإشعارات
CREATE SEQUENCE notification_templates_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 7. إنشاء جدول إعدادات الإشعارات للمستخدمين
CREATE TABLE user_notification_preferences (
    id NUMBER PRIMARY KEY,
    user_id NUMBER NOT NULL,
    user_type VARCHAR2(50) DEFAULT 'user', -- user, agent, admin
    
    -- تفضيلات القنوات
    sms_enabled NUMBER(1) DEFAULT 1,
    email_enabled NUMBER(1) DEFAULT 1,
    whatsapp_enabled NUMBER(1) DEFAULT 0,
    push_enabled NUMBER(1) DEFAULT 1,
    
    -- تفضيلات أنواع الإشعارات
    delivery_notifications NUMBER(1) DEFAULT 1,
    reminder_notifications NUMBER(1) DEFAULT 1,
    status_notifications NUMBER(1) DEFAULT 1,
    system_notifications NUMBER(1) DEFAULT 1,
    
    -- إعدادات التوقيت
    quiet_hours_start VARCHAR2(5), -- HH:MM format
    quiet_hours_end VARCHAR2(5),
    timezone VARCHAR2(50) DEFAULT 'Asia/Riyadh',
    
    -- معلومات الاتصال
    preferred_phone VARCHAR2(20),
    preferred_email VARCHAR2(100),
    
    created_at DATE DEFAULT SYSDATE,
    updated_at DATE
);

-- 8. إنشاء sequence لتفضيلات المستخدمين
CREATE SEQUENCE user_notification_prefs_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 9. إنشاء جدول إحصائيات الإشعارات
CREATE TABLE notification_statistics (
    id NUMBER PRIMARY KEY,
    stat_date DATE DEFAULT SYSDATE,
    stat_period VARCHAR2(20) DEFAULT 'DAILY', -- DAILY, WEEKLY, MONTHLY
    
    -- إحصائيات الإرسال
    total_notifications NUMBER DEFAULT 0,
    successful_notifications NUMBER DEFAULT 0,
    failed_notifications NUMBER DEFAULT 0,
    partial_notifications NUMBER DEFAULT 0,
    
    -- إحصائيات القنوات
    sms_sent NUMBER DEFAULT 0,
    sms_delivered NUMBER DEFAULT 0,
    email_sent NUMBER DEFAULT 0,
    email_opened NUMBER DEFAULT 0,
    whatsapp_sent NUMBER DEFAULT 0,
    whatsapp_delivered NUMBER DEFAULT 0,
    push_sent NUMBER DEFAULT 0,
    push_clicked NUMBER DEFAULT 0,
    
    -- إحصائيات الأنواع
    delivery_notifications NUMBER DEFAULT 0,
    reminder_notifications NUMBER DEFAULT 0,
    status_notifications NUMBER DEFAULT 0,
    system_notifications NUMBER DEFAULT 0,
    
    -- معدلات الأداء
    delivery_rate NUMBER(5,2) DEFAULT 0,
    open_rate NUMBER(5,2) DEFAULT 0,
    click_rate NUMBER(5,2) DEFAULT 0,
    
    created_at DATE DEFAULT SYSDATE
);

-- 10. إنشاء sequence لإحصائيات الإشعارات
CREATE SEQUENCE notification_stats_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 11. إنشاء فهارس للأداء
CREATE INDEX idx_notifications_log_recipient ON notifications_log(recipient_id, recipient_type);
CREATE INDEX idx_notifications_log_sent_at ON notifications_log(sent_at);
CREATE INDEX idx_notifications_log_status ON notifications_log(status);
CREATE INDEX idx_notifications_log_template ON notifications_log(template_key);
CREATE INDEX idx_scheduled_notifications_time ON scheduled_notifications(scheduled_time);
CREATE INDEX idx_scheduled_notifications_status ON scheduled_notifications(status);
CREATE INDEX idx_notification_templates_key ON notification_templates(template_key);
CREATE INDEX idx_user_notification_prefs_user ON user_notification_preferences(user_id);

-- 12. إدراج قوالب الإشعارات الافتراضية
INSERT INTO notification_templates (id, template_key, template_name, template_category, sms_template, email_subject_template, email_body_template, whatsapp_template, push_title_template, push_body_template, default_channels, template_variables) VALUES
(notification_templates_seq.NEXTVAL, 'delivery_order_created', 'إنشاء أمر تسليم جديد', 'delivery', 
'تم إنشاء أمر تسليم جديد رقم {order_number} للشحنة {tracking_number}',
'أمر تسليم جديد - {order_number}',
'تم إنشاء أمر تسليم جديد رقم {order_number} للشحنة {tracking_number}. يرجى المراجعة والمتابعة.',
'أمر تسليم جديد 📦\nرقم الأمر: {order_number}\nرقم التتبع: {tracking_number}\nالأولوية: {priority}',
'أمر تسليم جديد',
'أمر رقم {order_number} - {tracking_number}',
'SMS,EMAIL,WHATSAPP',
'["order_number", "tracking_number", "priority", "agent_name"]');

INSERT INTO notification_templates (id, template_key, template_name, template_category, sms_template, email_subject_template, email_body_template, whatsapp_template, push_title_template, push_body_template, default_channels, template_variables) VALUES
(notification_templates_seq.NEXTVAL, 'deadline_reminder', 'تذكير موعد نهائي', 'reminder',
'تذكير: أمر التسليم {order_number} مطلوب إنجازه خلال {hours_remaining} ساعة',
'تذكير موعد نهائي - {order_number}',
'يرجى إنجاز أمر التسليم {order_number} قبل {deadline_date}',
'⏰ تذكير موعد نهائي\nأمر التسليم: {order_number}\nالموعد النهائي: {deadline_date}\nالوقت المتبقي: {hours_remaining} ساعة',
'تذكير موعد نهائي',
'{order_number} - {hours_remaining} ساعة متبقية',
'SMS,EMAIL,WHATSAPP',
'["order_number", "deadline_date", "hours_remaining"]');

INSERT INTO notification_templates (id, template_key, template_name, template_category, sms_template, email_subject_template, email_body_template, whatsapp_template, push_title_template, push_body_template, default_channels, template_variables) VALUES
(notification_templates_seq.NEXTVAL, 'overdue_alert', 'تنبيه تأخير', 'reminder',
'تنبيه: أمر التسليم {order_number} متأخر بـ {days_overdue} يوم',
'تنبيه تأخير - {order_number}',
'أمر التسليم {order_number} متأخر عن الموعد المحدد بـ {days_overdue} يوم. يرجى المتابعة العاجلة.',
'🚨 تنبيه تأخير\nأمر التسليم: {order_number}\nمتأخر بـ: {days_overdue} يوم\nيرجى المتابعة العاجلة',
'تنبيه تأخير',
'{order_number} متأخر {days_overdue} يوم',
'SMS,EMAIL,WHATSAPP,PUSH',
'["order_number", "days_overdue"]');

INSERT INTO notification_templates (id, template_key, template_name, template_category, sms_template, email_subject_template, email_body_template, whatsapp_template, push_title_template, push_body_template, default_channels, template_variables) VALUES
(notification_templates_seq.NEXTVAL, 'shipment_arrived', 'وصول شحنة', 'shipment',
'وصلت الشحنة {tracking_number} إلى الميناء. تم إنشاء أمر تسليم تلقائياً.',
'وصول شحنة - {tracking_number}',
'وصلت الشحنة {tracking_number} إلى {port_name}. تم إنشاء أمر تسليم تلقائياً رقم {order_number}.',
'🚢 وصول شحنة\nرقم التتبع: {tracking_number}\nالميناء: {port_name}\nأمر التسليم: {order_number}',
'وصول شحنة',
'{tracking_number} وصلت إلى {port_name}',
'SMS,EMAIL,WHATSAPP',
'["tracking_number", "port_name", "order_number"]');

-- 13. إنشاء view شامل لإحصائيات الإشعارات
CREATE OR REPLACE VIEW v_notifications_dashboard AS
SELECT 
    -- إحصائيات اليوم
    (SELECT COUNT(*) FROM notifications_log WHERE TRUNC(sent_at) = TRUNC(SYSDATE)) as today_notifications,
    (SELECT COUNT(*) FROM notifications_log WHERE TRUNC(sent_at) = TRUNC(SYSDATE) AND status = 'SENT') as today_successful,
    (SELECT COUNT(*) FROM notifications_log WHERE TRUNC(sent_at) = TRUNC(SYSDATE) AND status = 'FAILED') as today_failed,
    
    -- إحصائيات القنوات اليوم
    (SELECT COUNT(*) FROM notifications_log WHERE TRUNC(sent_at) = TRUNC(SYSDATE) AND channels_successful LIKE '%SMS%') as today_sms,
    (SELECT COUNT(*) FROM notifications_log WHERE TRUNC(sent_at) = TRUNC(SYSDATE) AND channels_successful LIKE '%EMAIL%') as today_email,
    (SELECT COUNT(*) FROM notifications_log WHERE TRUNC(sent_at) = TRUNC(SYSDATE) AND channels_successful LIKE '%WHATSAPP%') as today_whatsapp,
    
    -- الإشعارات المجدولة
    (SELECT COUNT(*) FROM scheduled_notifications WHERE status = 'PENDING') as pending_scheduled,
    (SELECT COUNT(*) FROM scheduled_notifications WHERE scheduled_time <= SYSDATE AND status = 'PENDING') as overdue_scheduled,
    
    -- معدلات الأداء (آخر 30 يوم)
    (SELECT ROUND(AVG(CASE WHEN status = 'SENT' THEN 1 ELSE 0 END) * 100, 2) FROM notifications_log WHERE sent_at >= SYSDATE - 30) as success_rate_30d,
    (SELECT COUNT(DISTINCT template_key) FROM notifications_log WHERE sent_at >= SYSDATE - 30) as active_templates_30d
FROM DUAL;

COMMIT;
