-- إنشاء جداول المنافذ الجمركية
-- تاريخ الإنشاء: 2025-01-20

-- 1. جدول المنافذ الجمركية الرئيسي
CREATE TABLE customs_ports (
    id NUMBER PRIMARY KEY,
    port_code VARCHAR2(20) UNIQUE NOT NULL,
    port_name_ar VARCHAR2(200) NOT NULL,
    port_name_en VARCHAR2(200),
    port_type VARCHAR2(50) NOT NULL, -- 'SEA', 'AIR', 'LAND', 'DRY_PORT'
    country VARCHAR2(100) NOT NULL,
    city VARCHAR2(100) NOT NULL,
    region VARCHAR2(100),
    latitude NUMBER(10,8),
    longitude NUMBER(11,8),
    customs_authority VARCHAR2(200),
    contact_phone VARCHAR2(50),
    contact_email VARCHAR2(100),
    working_hours VARCHAR2(200),
    timezone VARCHAR2(50),
    is_active NUMBER(1) DEFAULT 1,
    is_24_hours NUMBER(1) DEFAULT 0,
    has_customs_clearance NUMBER(1) DEFAULT 1,
    has_quarantine NUMBER(1) DEFAULT 0,
    has_warehouse NUMBER(1) DEFAULT 0,
    max_container_size VARCHAR2(20), -- '20FT', '40FT', '45FT'
    handling_capacity NUMBER(10,2), -- طن/يوم
    storage_capacity NUMBER(10,2), -- متر مكعب
    notes CLOB,
    created_at DATE DEFAULT SYSDATE,
    updated_at DATE DEFAULT SYSDATE,
    created_by NUMBER,
    updated_by NUMBER
);

-- 2. جدول رسوم المنافذ الجمركية
CREATE TABLE customs_port_fees (
    id NUMBER PRIMARY KEY,
    port_id NUMBER NOT NULL,
    fee_type VARCHAR2(100) NOT NULL, -- 'CUSTOMS_CLEARANCE', 'STORAGE', 'HANDLING', 'INSPECTION'
    fee_name_ar VARCHAR2(200) NOT NULL,
    fee_name_en VARCHAR2(200),
    fee_amount NUMBER(10,2) NOT NULL,
    currency_code VARCHAR2(3) DEFAULT 'SAR',
    calculation_method VARCHAR2(50), -- 'FIXED', 'PER_CONTAINER', 'PER_TON', 'PERCENTAGE'
    min_amount NUMBER(10,2),
    max_amount NUMBER(10,2),
    is_mandatory NUMBER(1) DEFAULT 1,
    is_active NUMBER(1) DEFAULT 1,
    effective_from DATE DEFAULT SYSDATE,
    effective_to DATE,
    notes VARCHAR2(500),
    created_at DATE DEFAULT SYSDATE,
    updated_at DATE DEFAULT SYSDATE,
    CONSTRAINT fk_port_fees_port FOREIGN KEY (port_id) REFERENCES customs_ports(id)
);

-- 3. جدول ساعات عمل المنافذ
CREATE TABLE customs_port_working_hours (
    id NUMBER PRIMARY KEY,
    port_id NUMBER NOT NULL,
    day_of_week NUMBER(1) NOT NULL, -- 1=الأحد, 2=الاثنين, ... 7=السبت
    day_name_ar VARCHAR2(20) NOT NULL,
    is_working_day NUMBER(1) DEFAULT 1,
    opening_time VARCHAR2(8), -- HH:MM:SS
    closing_time VARCHAR2(8), -- HH:MM:SS
    break_start_time VARCHAR2(8),
    break_end_time VARCHAR2(8),
    overtime_available NUMBER(1) DEFAULT 0,
    overtime_rate NUMBER(5,2), -- معدل الإضافي
    notes VARCHAR2(200),
    created_at DATE DEFAULT SYSDATE,
    CONSTRAINT fk_port_hours_port FOREIGN KEY (port_id) REFERENCES customs_ports(id)
);

-- 4. جدول الخدمات المتاحة في المنافذ
CREATE TABLE customs_port_services (
    id NUMBER PRIMARY KEY,
    port_id NUMBER NOT NULL,
    service_code VARCHAR2(50) NOT NULL,
    service_name_ar VARCHAR2(200) NOT NULL,
    service_name_en VARCHAR2(200),
    service_category VARCHAR2(100), -- 'CLEARANCE', 'INSPECTION', 'STORAGE', 'HANDLING'
    is_available NUMBER(1) DEFAULT 1,
    requires_appointment NUMBER(1) DEFAULT 0,
    processing_time_hours NUMBER(5,2),
    service_fee NUMBER(10,2),
    currency_code VARCHAR2(3) DEFAULT 'SAR',
    contact_person VARCHAR2(100),
    contact_phone VARCHAR2(50),
    notes VARCHAR2(500),
    created_at DATE DEFAULT SYSDATE,
    CONSTRAINT fk_port_services_port FOREIGN KEY (port_id) REFERENCES customs_ports(id)
);

-- 5. جدول إحصائيات المنافذ
CREATE TABLE customs_port_statistics (
    id NUMBER PRIMARY KEY,
    port_id NUMBER NOT NULL,
    stat_date DATE NOT NULL,
    total_shipments NUMBER(10) DEFAULT 0,
    cleared_shipments NUMBER(10) DEFAULT 0,
    pending_shipments NUMBER(10) DEFAULT 0,
    rejected_shipments NUMBER(10) DEFAULT 0,
    total_containers NUMBER(10) DEFAULT 0,
    total_weight_tons NUMBER(12,2) DEFAULT 0,
    total_value_amount NUMBER(15,2) DEFAULT 0,
    avg_clearance_time_hours NUMBER(8,2) DEFAULT 0,
    peak_hour_start VARCHAR2(8),
    peak_hour_end VARCHAR2(8),
    created_at DATE DEFAULT SYSDATE,
    CONSTRAINT fk_port_stats_port FOREIGN KEY (port_id) REFERENCES customs_ports(id)
);

-- إنشاء التسلسلات
CREATE SEQUENCE customs_ports_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE customs_port_fees_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE customs_port_hours_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE customs_port_services_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE customs_port_stats_seq START WITH 1 INCREMENT BY 1;

-- إنشاء الفهارس (تجنب الفهارس المكررة للأعمدة UNIQUE)
-- port_code له فهرس تلقائي بسبب UNIQUE constraint
CREATE INDEX idx_customs_ports_type ON customs_ports(port_type);
CREATE INDEX idx_customs_ports_country ON customs_ports(country);
CREATE INDEX idx_customs_ports_active ON customs_ports(is_active);
CREATE INDEX idx_customs_ports_city ON customs_ports(city);
CREATE INDEX idx_port_fees_type ON customs_port_fees(fee_type);
CREATE INDEX idx_port_fees_active ON customs_port_fees(is_active);
CREATE INDEX idx_port_hours_day ON customs_port_working_hours(day_of_week);
CREATE INDEX idx_port_services_category ON customs_port_services(service_category);
CREATE INDEX idx_port_services_available ON customs_port_services(is_available);
CREATE INDEX idx_port_stats_date ON customs_port_statistics(stat_date);
CREATE INDEX idx_port_stats_port_date ON customs_port_statistics(port_id, stat_date);

-- إدراج بيانات تجريبية للمنافذ الجمركية السعودية
INSERT INTO customs_ports (id, port_code, port_name_ar, port_name_en, port_type, country, city, region, customs_authority, contact_phone, is_active, is_24_hours, has_customs_clearance, has_quarantine, has_warehouse) VALUES 
(customs_ports_seq.NEXTVAL, 'JEDDAH_SEA', 'ميناء جدة الإسلامي', 'Jeddah Islamic Port', 'SEA', 'السعودية', 'جدة', 'مكة المكرمة', 'الهيئة العامة للجمارك', '+966-12-6361000', 1, 1, 1, 1, 1);

INSERT INTO customs_ports (id, port_code, port_name_ar, port_name_en, port_type, country, city, region, customs_authority, contact_phone, is_active, is_24_hours, has_customs_clearance, has_quarantine, has_warehouse) VALUES 
(customs_ports_seq.NEXTVAL, 'DAMMAM_SEA', 'ميناء الملك عبدالعزيز', 'King Abdulaziz Port', 'SEA', 'السعودية', 'الدمام', 'المنطقة الشرقية', 'الهيئة العامة للجمارك', '+966-13-8576000', 1, 1, 1, 1, 1);

INSERT INTO customs_ports (id, port_code, port_name_ar, port_name_en, port_type, country, city, region, customs_authority, contact_phone, is_active, is_24_hours, has_customs_clearance, has_quarantine, has_warehouse) VALUES 
(customs_ports_seq.NEXTVAL, 'RIYADH_AIR', 'مطار الملك خالد الدولي', 'King Khalid International Airport', 'AIR', 'السعودية', 'الرياض', 'الرياض', 'الهيئة العامة للجمارك', '+966-11-2211000', 1, 1, 1, 1, 1);

INSERT INTO customs_ports (id, port_code, port_name_ar, port_name_en, port_type, country, city, region, customs_authority, contact_phone, is_active, is_24_hours, has_customs_clearance, has_quarantine, has_warehouse) VALUES 
(customs_ports_seq.NEXTVAL, 'JEDDAH_AIR', 'مطار الملك عبدالعزيز الدولي', 'King Abdulaziz International Airport', 'AIR', 'السعودية', 'جدة', 'مكة المكرمة', 'الهيئة العامة للجمارك', '+966-12-6844000', 1, 1, 1, 1, 1);

INSERT INTO customs_ports (id, port_code, port_name_ar, port_name_en, port_type, country, city, region, customs_authority, contact_phone, is_active, is_24_hours, has_customs_clearance, has_quarantine, has_warehouse) VALUES 
(customs_ports_seq.NEXTVAL, 'HADITHA_LAND', 'منفذ الحديثة البري', 'Haditha Land Port', 'LAND', 'السعودية', 'الحديثة', 'الحدود الشمالية', 'الهيئة العامة للجمارك', '+966-14-6221000', 1, 0, 1, 0, 1);

-- إدراج ساعات العمل للمنافذ
INSERT INTO customs_port_working_hours (id, port_id, day_of_week, day_name_ar, is_working_day, opening_time, closing_time) VALUES 
(customs_port_hours_seq.NEXTVAL, 1, 1, 'الأحد', 1, '08:00:00', '16:00:00');

INSERT INTO customs_port_working_hours (id, port_id, day_of_week, day_name_ar, is_working_day, opening_time, closing_time) VALUES 
(customs_port_hours_seq.NEXTVAL, 1, 2, 'الاثنين', 1, '08:00:00', '16:00:00');

INSERT INTO customs_port_working_hours (id, port_id, day_of_week, day_name_ar, is_working_day, opening_time, closing_time) VALUES 
(customs_port_hours_seq.NEXTVAL, 1, 3, 'الثلاثاء', 1, '08:00:00', '16:00:00');

INSERT INTO customs_port_working_hours (id, port_id, day_of_week, day_name_ar, is_working_day, opening_time, closing_time) VALUES 
(customs_port_hours_seq.NEXTVAL, 1, 4, 'الأربعاء', 1, '08:00:00', '16:00:00');

INSERT INTO customs_port_working_hours (id, port_id, day_of_week, day_name_ar, is_working_day, opening_time, closing_time) VALUES 
(customs_port_hours_seq.NEXTVAL, 1, 5, 'الخميس', 1, '08:00:00', '16:00:00');

-- إدراج الخدمات المتاحة
INSERT INTO customs_port_services (id, port_id, service_code, service_name_ar, service_name_en, service_category, is_available, processing_time_hours, service_fee) VALUES 
(customs_port_services_seq.NEXTVAL, 1, 'CUSTOMS_CLEAR', 'التخليص الجمركي', 'Customs Clearance', 'CLEARANCE', 1, 24, 500);

INSERT INTO customs_port_services (id, port_id, service_code, service_name_ar, service_name_en, service_category, is_available, processing_time_hours, service_fee) VALUES 
(customs_port_services_seq.NEXTVAL, 1, 'CONTAINER_INSPECT', 'فحص الحاويات', 'Container Inspection', 'INSPECTION', 1, 4, 200);

INSERT INTO customs_port_services (id, port_id, service_code, service_name_ar, service_name_en, service_category, is_available, processing_time_hours, service_fee) VALUES 
(customs_port_services_seq.NEXTVAL, 1, 'TEMP_STORAGE', 'التخزين المؤقت', 'Temporary Storage', 'STORAGE', 1, 0, 50);

-- إدراج رسوم المنافذ
INSERT INTO customs_port_fees (id, port_id, fee_type, fee_name_ar, fee_name_en, fee_amount, calculation_method, is_mandatory) VALUES 
(customs_port_fees_seq.NEXTVAL, 1, 'CUSTOMS_CLEARANCE', 'رسوم التخليص الجمركي', 'Customs Clearance Fee', 500, 'FIXED', 1);

INSERT INTO customs_port_fees (id, port_id, fee_type, fee_name_ar, fee_name_en, fee_amount, calculation_method, is_mandatory) VALUES 
(customs_port_fees_seq.NEXTVAL, 1, 'STORAGE', 'رسوم التخزين', 'Storage Fee', 25, 'PER_DAY', 0);

INSERT INTO customs_port_fees (id, port_id, fee_type, fee_name_ar, fee_name_en, fee_amount, calculation_method, is_mandatory) VALUES 
(customs_port_fees_seq.NEXTVAL, 1, 'HANDLING', 'رسوم المناولة', 'Handling Fee', 100, 'PER_CONTAINER', 1);

COMMIT;
