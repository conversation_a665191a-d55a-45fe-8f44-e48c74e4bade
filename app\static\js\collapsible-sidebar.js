/**
 * نظام الشريط الجانبي المطوي
 * Collapsible Sidebar System
 */

class CollapsibleSidebar {
    constructor() {
        this.favorites = this.loadFavorites();
        this.currentTheme = this.loadTheme();
        this.init();
    }

    init() {
        this.setupSearchBox();
        this.setupThemeSelector();
        this.setupCollapsibleHeaders();
        this.loadSavedStates();
        this.setupEventListeners();
        this.setupFavorites();
        this.setupMobileFeatures();
        this.applyTheme();
        console.log('🌟 نظام الشريط الجانبي المتقدم تم تحميله');
    }

    setupSearchBox() {
        const sidebar = document.querySelector('.ns-sidebar-real');
        const searchContainer = document.createElement('div');
        searchContainer.className = 'sidebar-search-container';
        searchContainer.innerHTML = `
            <div class="search-box">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="البحث في القوائم..." id="sidebarSearch">
                <button class="clear-search" id="clearSearch" style="display: none;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // إدراج صندوق البحث بعد أزرار التحكم
        const controls = sidebar.querySelector('.sidebar-controls');
        controls.parentNode.insertBefore(searchContainer, controls.nextSibling);

        this.setupSearchFunctionality();
    }

    setupSearchFunctionality() {
        const searchInput = document.getElementById('sidebarSearch');
        const clearButton = document.getElementById('clearSearch');

        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.toLowerCase().trim();
            this.filterMenuItems(query);

            clearButton.style.display = query ? 'block' : 'none';
        });

        clearButton.addEventListener('click', () => {
            searchInput.value = '';
            this.filterMenuItems('');
            clearButton.style.display = 'none';
            searchInput.focus();
        });
    }

    filterMenuItems(query) {
        const allItems = document.querySelectorAll('.ns-nav-item');
        const allSections = document.querySelectorAll('.ns-nav-section');

        if (!query) {
            // إظهار جميع العناصر والأقسام
            allItems.forEach(item => {
                item.style.display = '';
                item.classList.remove('search-highlight');
            });
            allSections.forEach(section => section.style.display = '');
            return;
        }

        let hasVisibleItems = false;

        allSections.forEach(section => {
            const items = section.querySelectorAll('.ns-nav-item');
            let sectionHasVisibleItems = false;

            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(query)) {
                    item.style.display = '';
                    item.classList.add('search-highlight');
                    sectionHasVisibleItems = true;
                    hasVisibleItems = true;

                    // فتح القسم الذي يحتوي على النتيجة
                    const header = section.querySelector('.collapsible-header');
                    if (header) {
                        this.openSectionByHeader(header);
                    }
                } else {
                    item.style.display = 'none';
                    item.classList.remove('search-highlight');
                }
            });

            section.style.display = sectionHasVisibleItems ? '' : 'none';
        });

        // إظهار رسالة عدم وجود نتائج
        this.showNoResultsMessage(!hasVisibleItems);
    }

    showNoResultsMessage(show) {
        let noResultsMsg = document.querySelector('.no-search-results');

        if (show && !noResultsMsg) {
            noResultsMsg = document.createElement('div');
            noResultsMsg.className = 'no-search-results';
            noResultsMsg.innerHTML = `
                <div class="text-center p-3">
                    <i class="fas fa-search-minus fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">لا توجد نتائج</p>
                </div>
            `;
            document.querySelector('.ns-sidebar-real').appendChild(noResultsMsg);
        } else if (!show && noResultsMsg) {
            noResultsMsg.remove();
        }
    }

    setupCollapsibleHeaders() {
        // تحويل جميع عناوين الأقسام إلى قابلة للطي
        const headers = document.querySelectorAll('.ns-nav-header');
        
        headers.forEach((header, index) => {
            // إضافة الكلاسات والأيقونات
            header.classList.add('collapsible-header');
            header.setAttribute('data-section', `section-${index}`);
            
            // إضافة أيقونة التبديل
            if (!header.querySelector('.toggle-icon')) {
                const icon = document.createElement('i');
                icon.className = 'fas fa-chevron-down toggle-icon ms-auto';
                header.appendChild(icon);
            }

            // العثور على العناصر التابعة
            const section = header.parentElement;
            const items = section.querySelectorAll('.ns-nav-item');
            
            if (items.length > 0) {
                // إنشاء حاوية للعناصر
                const itemsContainer = document.createElement('div');
                itemsContainer.className = 'ns-nav-items';
                itemsContainer.id = `items-section-${index}`;
                
                // نقل العناصر إلى الحاوية
                items.forEach(item => {
                    itemsContainer.appendChild(item);
                });
                
                // إضافة الحاوية بعد العنوان
                header.parentNode.insertBefore(itemsContainer, header.nextSibling);
            }
        });
    }

    setupEventListeners() {
        // إضافة مستمعي الأحداث للعناوين القابلة للطي
        document.addEventListener('click', (e) => {
            const header = e.target.closest('.collapsible-header');
            if (header) {
                this.toggleSection(header);
            }
        });
    }

    toggleSection(header) {
        const sectionId = header.getAttribute('data-section');
        const itemsContainer = document.getElementById(`items-${sectionId}`);
        const icon = header.querySelector('.toggle-icon');
        
        if (itemsContainer) {
            const isCollapsed = itemsContainer.classList.contains('collapsed');
            
            if (isCollapsed) {
                // فتح القسم
                itemsContainer.classList.remove('collapsed');
                icon.classList.remove('fa-chevron-left');
                icon.classList.add('fa-chevron-down');
                header.classList.remove('collapsed');
            } else {
                // إغلاق القسم
                itemsContainer.classList.add('collapsed');
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-left');
                header.classList.add('collapsed');
            }
            
            // حفظ الحالة
            this.saveState(sectionId, !isCollapsed);
        }
    }

    saveState(sectionId, isOpen) {
        const states = this.getSavedStates();
        states[sectionId] = isOpen;
        localStorage.setItem('sidebar-states', JSON.stringify(states));
    }

    getSavedStates() {
        const saved = localStorage.getItem('sidebar-states');
        return saved ? JSON.parse(saved) : {};
    }

    loadSavedStates() {
        const states = this.getSavedStates();
        
        // تطبيق الحالات المحفوظة أو الحالة الافتراضية (مطوي)
        document.querySelectorAll('.collapsible-header').forEach(header => {
            const sectionId = header.getAttribute('data-section');
            const itemsContainer = document.getElementById(`items-${sectionId}`);
            const icon = header.querySelector('.toggle-icon');
            
            if (itemsContainer) {
                // الحالة الافتراضية: مطوي (إلا إذا كان محفوظ كمفتوح)
                const shouldBeOpen = states[sectionId] === true;
                
                if (shouldBeOpen) {
                    // فتح القسم
                    itemsContainer.classList.remove('collapsed');
                    icon.classList.remove('fa-chevron-left');
                    icon.classList.add('fa-chevron-down');
                    header.classList.remove('collapsed');
                } else {
                    // إغلاق القسم (الحالة الافتراضية)
                    itemsContainer.classList.add('collapsed');
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-left');
                    header.classList.add('collapsed');
                }
            }
        });
    }

    // فتح قسم معين (مفيد للتنقل المباشر)
    openSection(sectionId) {
        const header = document.querySelector(`[data-section="${sectionId}"]`);
        if (header) {
            const itemsContainer = document.getElementById(`items-${sectionId}`);
            const icon = header.querySelector('.toggle-icon');
            
            if (itemsContainer && itemsContainer.classList.contains('collapsed')) {
                itemsContainer.classList.remove('collapsed');
                icon.classList.remove('fa-chevron-left');
                icon.classList.add('fa-chevron-down');
                header.classList.remove('collapsed');
                this.saveState(sectionId, true);
            }
        }
    }

    // إغلاق جميع الأقسام
    collapseAll() {
        document.querySelectorAll('.collapsible-header').forEach(header => {
            const sectionId = header.getAttribute('data-section');
            const itemsContainer = document.getElementById(`items-${sectionId}`);
            const icon = header.querySelector('.toggle-icon');
            
            if (itemsContainer && !itemsContainer.classList.contains('collapsed')) {
                itemsContainer.classList.add('collapsed');
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-left');
                header.classList.add('collapsed');
                this.saveState(sectionId, false);
            }
        });
    }

    // فتح جميع الأقسام
    expandAll() {
        document.querySelectorAll('.collapsible-header').forEach(header => {
            const sectionId = header.getAttribute('data-section');
            const itemsContainer = document.getElementById(`items-${sectionId}`);
            const icon = header.querySelector('.toggle-icon');
            
            if (itemsContainer && itemsContainer.classList.contains('collapsed')) {
                itemsContainer.classList.remove('collapsed');
                icon.classList.remove('fa-chevron-left');
                icon.classList.add('fa-chevron-down');
                header.classList.remove('collapsed');
                this.saveState(sectionId, true);
            }
        });
    }

    setupThemeSelector() {
        const sidebar = document.querySelector('.ns-sidebar-real');
        const themeContainer = document.createElement('div');
        themeContainer.className = 'sidebar-theme-container';
        themeContainer.innerHTML = `
            <div class="theme-selector">
                <label class="theme-label">
                    <i class="fas fa-palette me-1"></i>
                    الثيم:
                </label>
                <select class="theme-select" id="themeSelector">
                    <option value="default">افتراضي</option>
                    <option value="dark">داكن</option>
                    <option value="blue">أزرق</option>
                    <option value="green">أخضر</option>
                    <option value="purple">بنفسجي</option>
                </select>
            </div>
        `;

        const searchContainer = sidebar.querySelector('.sidebar-search-container');
        searchContainer.parentNode.insertBefore(themeContainer, searchContainer.nextSibling);

        document.getElementById('themeSelector').addEventListener('change', (e) => {
            this.changeTheme(e.target.value);
        });

        document.getElementById('themeSelector').value = this.currentTheme;
    }

    setupFavorites() {
        const navItems = document.querySelectorAll('.ns-nav-item');
        navItems.forEach(item => {
            const href = item.getAttribute('href');
            if (href && href !== '#') {
                const favoriteIcon = document.createElement('i');
                favoriteIcon.className = this.favorites.includes(href) ?
                    'fas fa-star favorite-icon active' :
                    'far fa-star favorite-icon';
                favoriteIcon.title = 'إضافة للمفضلة';
                favoriteIcon.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.toggleFavorite(href, favoriteIcon);
                });

                item.appendChild(favoriteIcon);
            }
        });

        this.createFavoritesSection();
    }

    createFavoritesSection() {
        if (this.favorites.length === 0) return;

        const sidebar = document.querySelector('.ns-sidebar-real');
        const favoritesSection = document.createElement('div');
        favoritesSection.className = 'ns-nav-section favorites-section';
        favoritesSection.innerHTML = `
            <div class="ns-nav-header collapsible-header" data-section="favorites">
                <i class="fas fa-star me-2"></i>
                المفضلة
                <i class="fas fa-chevron-down toggle-icon ms-auto"></i>
            </div>
            <div class="ns-nav-items" id="items-favorites">
                ${this.generateFavoritesHTML()}
            </div>
        `;

        const firstSection = sidebar.querySelector('.ns-nav-section');
        firstSection.parentNode.insertBefore(favoritesSection, firstSection);
    }

    generateFavoritesHTML() {
        return this.favorites.map(href => {
            const originalItem = document.querySelector(`[href="${href}"]`);
            if (originalItem) {
                const icon = originalItem.querySelector('i').className;
                const text = originalItem.textContent.trim();
                return `
                    <a class="ns-nav-item favorite-item" href="${href}">
                        <i class="${icon}"></i>
                        ${text}
                        <i class="fas fa-star favorite-icon active ms-auto"></i>
                    </a>
                `;
            }
            return '';
        }).join('');
    }

    toggleFavorite(href, iconElement) {
        const index = this.favorites.indexOf(href);

        if (index > -1) {
            this.favorites.splice(index, 1);
            iconElement.className = 'far fa-star favorite-icon';
        } else {
            this.favorites.push(href);
            iconElement.className = 'fas fa-star favorite-icon active';
        }

        this.saveFavorites();
        this.updateFavoritesSection();
    }

    updateFavoritesSection() {
        const favoritesSection = document.querySelector('.favorites-section');

        if (this.favorites.length === 0) {
            if (favoritesSection) favoritesSection.remove();
            return;
        }

        if (!favoritesSection) {
            this.createFavoritesSection();
            return;
        }

        const favoritesItems = favoritesSection.querySelector('.ns-nav-items');
        favoritesItems.innerHTML = this.generateFavoritesHTML();
    }

    changeTheme(theme) {
        this.currentTheme = theme;
        this.saveTheme();
        this.applyTheme();
    }

    applyTheme() {
        const sidebar = document.querySelector('.ns-sidebar-real');
        sidebar.classList.remove('theme-default', 'theme-dark', 'theme-blue', 'theme-green', 'theme-purple');
        sidebar.classList.add(`theme-${this.currentTheme}`);
    }

    loadFavorites() {
        const saved = localStorage.getItem('sidebar-favorites');
        return saved ? JSON.parse(saved) : [];
    }

    saveFavorites() {
        localStorage.setItem('sidebar-favorites', JSON.stringify(this.favorites));
    }

    loadTheme() {
        return localStorage.getItem('sidebar-theme') || 'default';
    }

    saveTheme() {
        localStorage.setItem('sidebar-theme', this.currentTheme);
    }

    openSectionByHeader(header) {
        const sectionId = header.getAttribute('data-section');
        const itemsContainer = document.getElementById(`items-${sectionId}`);
        const icon = header.querySelector('.toggle-icon');

        if (itemsContainer && itemsContainer.classList.contains('collapsed')) {
            itemsContainer.classList.remove('collapsed');
            icon.classList.remove('fa-chevron-left');
            icon.classList.add('fa-chevron-down');
            header.classList.remove('collapsed');
        }
    }

    setupMobileFeatures() {
        // إعداد overlay للجوال
        const overlay = document.getElementById('sidebarOverlay');
        const sidebar = document.querySelector('.ns-sidebar-real');

        if (overlay) {
            overlay.addEventListener('click', () => {
                this.closeMobileSidebar();
            });
        }

        // إضافة زر إغلاق للجوال
        if (window.innerWidth <= 768) {
            this.addMobileCloseButton();
        }

        // مراقبة تغيير حجم الشاشة
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768) {
                this.closeMobileSidebar();
            }
        });
    }

    addMobileCloseButton() {
        const controls = document.querySelector('.sidebar-controls');
        if (controls && !controls.querySelector('.mobile-close-btn')) {
            const closeBtn = document.createElement('button');
            closeBtn.className = 'btn btn-sm btn-outline-danger mobile-close-btn';
            closeBtn.innerHTML = '<i class="fas fa-times"></i>';
            closeBtn.title = 'إغلاق القائمة';
            closeBtn.addEventListener('click', () => {
                this.closeMobileSidebar();
            });

            controls.appendChild(closeBtn);
        }
    }

    openMobileSidebar() {
        if (window.innerWidth <= 768) {
            const sidebar = document.querySelector('.ns-sidebar-real');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.classList.add('show');
            overlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    closeMobileSidebar() {
        const sidebar = document.querySelector('.ns-sidebar-real');
        const overlay = document.getElementById('sidebarOverlay');

        sidebar.classList.remove('show');
        overlay.classList.remove('active');
        document.body.style.overflow = '';
    }

    // دالة عامة لتبديل الشريط الجانبي
    toggleSidebar() {
        if (window.innerWidth <= 768) {
            const sidebar = document.querySelector('.ns-sidebar-real');
            if (sidebar.classList.contains('show')) {
                this.closeMobileSidebar();
            } else {
                this.openMobileSidebar();
            }
        }
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.collapsibleSidebar = new CollapsibleSidebar();
});

// تصدير للاستخدام العام
window.CollapsibleSidebar = CollapsibleSidebar;
