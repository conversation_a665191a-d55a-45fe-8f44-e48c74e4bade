<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 تحليلات البريد الإلكتروني المتقدمة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            direction: rtl;
            color: #ffffff;
            overflow-x: hidden;
        }
        
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .page-title {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            text-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
        }
        
        .page-subtitle {
            font-size: 1.2rem;
            color: #a0a0a0;
            margin-bottom: 2rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .stat-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
            border-color: rgba(102, 126, 234, 0.5);
        }
        
        .stat-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #a0a0a0;
            font-size: 1rem;
        }
        
        .chart-container {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: visible;
            min-height: 350px;
        }
        
        .chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .chart-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .no-data-message {
            text-align: center;
            padding: 4rem 2rem;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            margin: 2rem 0;
        }
        
        .no-data-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .no-data-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 1rem;
        }
        
        .no-data-text {
            color: #a0a0a0;
            font-size: 1rem;
            line-height: 1.6;
        }
        
        .back-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            margin-top: 2rem;
        }
        
        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        
        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 3rem;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(102, 126, 234, 0.3);
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* شريط التنقل */
        .navbar-custom {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }

        .navbar-brand {
            color: #ffffff !important;
            font-weight: 700;
            font-size: 1.5rem;
        }

        .navbar-nav .nav-link {
            color: #a0a0a0 !important;
            font-weight: 500;
            padding: 0.8rem 1.2rem !important;
            border-radius: 50px;
            transition: all 0.3s ease;
            margin: 0 0.2rem;
        }

        .navbar-nav .nav-link:hover {
            color: #ffffff !important;
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        .navbar-nav .nav-link.active {
            color: #ffffff !important;
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .navbar-toggler {
            border: none;
            color: #ffffff;
        }

        .navbar-toggler:focus {
            box-shadow: none;
        }

        .breadcrumb-custom {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
        }

        .breadcrumb-custom .breadcrumb {
            margin: 0;
            background: none;
            padding: 0;
        }

        .breadcrumb-custom .breadcrumb-item {
            color: #a0a0a0;
        }

        .breadcrumb-custom .breadcrumb-item.active {
            color: #ffffff;
            font-weight: 600;
        }

        .breadcrumb-custom .breadcrumb-item a {
            color: #667eea;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .breadcrumb-custom .breadcrumb-item a:hover {
            color: #ffffff;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 2.5rem;
            margin-bottom: 3rem;
        }

        .insights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .insight-card {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(102, 126, 234, 0.3);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
        }
        
        .insight-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
        }
        
        .insight-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .insight-content {
            color: #a0a0a0;
            line-height: 1.6;
        }
        
        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }

            .page-title {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .charts-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .insights-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .chart-container {
                padding: 0.8rem;
                max-height: 220px;
            }

            .chart-title {
                font-size: 1rem;
                margin-bottom: 0.8rem;
            }

            .navbar-nav {
                text-align: center;
            }

            .navbar-nav .nav-link {
                margin: 0.2rem 0;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('email.inbox') }}">
                <i class="fas fa-envelope me-2"></i>
                نظام البريد الإلكتروني
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <i class="fas fa-bars"></i>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.inbox') }}">
                            <i class="fas fa-inbox me-1"></i>
                            صندوق الوارد
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.compose') }}">
                            <i class="fas fa-edit me-1"></i>
                            إنشاء رسالة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.contacts') }}">
                            <i class="fas fa-address-book me-1"></i>
                            دفتر العناوين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.templates') }}">
                            <i class="fas fa-file-alt me-1"></i>
                            القوالب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('email.analytics') }}">
                            <i class="fas fa-chart-line me-1"></i>
                            التحليلات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.settings') }}">
                            <i class="fas fa-cog me-1"></i>
                            الإعدادات
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user me-1"></i>
                            {{ current_user.username if current_user.is_authenticated else 'المستخدم' }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url_for('main.dashboard') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>لوحة المعلومات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <!-- Breadcrumb -->
        <div class="breadcrumb-custom">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('main.dashboard') }}">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('email.inbox') }}">
                            <i class="fas fa-envelope me-1"></i>
                            البريد الإلكتروني
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <i class="fas fa-chart-line me-1"></i>
                        التحليلات
                    </li>
                </ol>
            </nav>
        </div>

        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-chart-bar me-2"></i>
                تحليلات البريد الإلكتروني المتقدمة
            </h1>
            <p class="page-subtitle">
                <i class="fas fa-brain me-1"></i>
                تحليل ذكي وشامل لبيانات البريد الإلكتروني مع رؤى متقدمة
            </p>

            <!-- Export Button -->
            <div class="text-center mt-3">
                <button class="btn btn-outline-light btn-lg" onclick="exportAnalytics()" style="border-radius: 50px; padding: 0.8rem 2rem;">
                    <i class="fas fa-download me-2"></i>
                    تصدير التحليلات
                </button>
            </div>
        </div>
        
        <!-- Loading Indicator -->
        <div id="loadingIndicator" class="loading-spinner">
            <div class="spinner"></div>
        </div>
        
        <!-- Main Content -->
        <div id="mainContent" style="display: none;">
            <!-- Statistics Overview -->
            <div id="statsSection" class="stats-grid">
                <!-- Stats will be populated here -->
            </div>
            
            <!-- Charts Section -->
            <div id="chartsSection" class="charts-grid">
                <!-- Charts will be populated here -->
            </div>
            
            <!-- Insights Section -->
            <div id="insightsSection" class="insights-grid">
                <!-- Insights will be populated here -->
            </div>
        </div>
        
        <!-- No Data Message -->
        <div id="noDataMessage" class="no-data-message" style="display: none;">
            <div class="no-data-icon">
                <i class="fas fa-inbox"></i>
            </div>
            <h2 class="no-data-title">لا توجد بيانات للتحليل</h2>
            <p class="no-data-text">
                لم يتم العثور على رسائل بريد إلكتروني في حسابك.<br>
                قم بإضافة حساب بريد إلكتروني وجلب بعض الرسائل أولاً لرؤية التحليلات المتقدمة.
            </p>
            <a href="{{ url_for('email.settings') }}" class="back-btn">
                <i class="fas fa-cog"></i>
                إعداد حساب البريد
            </a>
        </div>
        
        <!-- Back Button -->
        <div class="text-center">
            <a href="{{ url_for('email.inbox') }}" class="back-btn">
                <i class="fas fa-arrow-right"></i>
                العودة إلى صندوق الوارد
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js إصدار مستقر -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>

    <script>
        // متغيرات عامة
        var emailAnalytics = {
            data: null,
            charts: {}
        };

        // دالة التهيئة الرئيسية
        function initEmailAnalytics() {
            console.log('🚀 بدء تحليلات البريد الإلكتروني...');

            // فحص الحسابات أولاً
            checkAccounts()
                .then(function() {
                    return loadAnalyticsData();
                })
                .then(function() {
                    renderAnalytics();
                })
                .catch(function(error) {
                    console.error('❌ خطأ في تحميل البيانات:', error);
                    showNoDataMessage(error.message);
                });
        }

        // فحص حسابات البريد
        function checkAccounts() {
            return fetch('/email/api/check-accounts')
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    console.log('📧 معلومات الحسابات:', data);

                    if (data.accounts && data.accounts.length > 0) {
                        console.log('✅ تم العثور على ' + data.total_accounts + ' حساب بريد');
                        data.accounts.forEach(function(account) {
                            console.log('📧 ' + account.email + ': ' + account.message_count + ' رسالة');
                        });
                    } else {
                        console.log('❌ لا توجد حسابات بريد إلكتروني');
                    }
                })
                .catch(function(error) {
                    console.error('خطأ في فحص الحسابات:', error);
                });
        }

        // تحميل بيانات التحليلات
        function loadAnalyticsData() {
            return fetch('/email/api/analytics-data')
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    if (data.error) {
                        throw new Error(data.error);
                    }

                    emailAnalytics.data = data;
                    console.log('📊 تم تحميل بيانات التحليلات:', data);
                    return data;
                });
        }

        // عرض التحليلات
        function renderAnalytics() {
            document.getElementById('loadingIndicator').style.display = 'none';

            // التحقق من وجود بيانات حقيقية
            if (hasRealData()) {
                document.getElementById('mainContent').style.display = 'block';
                renderStats();
                renderCharts();
                renderInsights();
            } else {
                showNoDataMessage();
            }
        }

        // فحص وجود بيانات حقيقية
        function hasRealData() {
            if (!emailAnalytics.data) return false;

            var hasEmails = {{ stats.total_emails or 0 }} > 0;
            var hasAnalyticsData =
                (emailAnalytics.data.peak_hours && emailAnalytics.data.peak_hours.length > 0) ||
                (emailAnalytics.data.top_senders && emailAnalytics.data.top_senders.length > 0) ||
                (emailAnalytics.data.weekly_trend && emailAnalytics.data.weekly_trend.length > 0) ||
                (emailAnalytics.data.size_analysis && emailAnalytics.data.size_analysis.length > 0);

            return hasEmails && hasAnalyticsData;
        }

        // عرض الإحصائيات
        function renderStats() {
            var statsContainer = document.getElementById('statsSection');
            var stats = [
                {
                    icon: 'fas fa-envelope',
                    number: {{ stats.total_emails or 0 }},
                    label: 'إجمالي الرسائل',
                    color: '#667eea'
                },
                {
                    icon: 'fas fa-calendar-week',
                    number: {{ stats.emails_this_week or 0 }},
                    label: 'رسائل هذا الأسبوع',
                    color: '#764ba2'
                },
                {
                    icon: 'fas fa-envelope-open',
                    number: {{ stats.unread_count or 0 }},
                    label: 'غير مقروءة',
                    color: '#f093fb'
                },
                {
                    icon: 'fas fa-star',
                    number: {{ stats.important_count or 0 }},
                    label: 'مهمة',
                    color: '#f5576c'
                }
            ];

            var statsHTML = '';
            for (var i = 0; i < stats.length; i++) {
                var stat = stats[i];
                statsHTML += '<div class="stat-card">' +
                    '<div class="stat-icon"><i class="' + stat.icon + '"></i></div>' +
                    '<div class="stat-number" data-target="' + stat.number + '">0</div>' +
                    '<div class="stat-label">' + stat.label + '</div>' +
                    '</div>';
            }

            statsContainer.innerHTML = statsHTML;

            // تحريك الأرقام
            animateNumbers();
        }

        // تحريك الأرقام
        function animateNumbers() {
            var numberElements = document.querySelectorAll('[data-target]');
            for (var i = 0; i < numberElements.length; i++) {
                var element = numberElements[i];
                var target = parseInt(element.getAttribute('data-target'));
                animateNumber(element, target);
            }
        }

        // تحريك رقم واحد
        function animateNumber(element, target) {
            var duration = 2000;
            var step = target / (duration / 16);
            var current = 0;

            var timer = setInterval(function() {
                current += step;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current);
            }, 16);
        }

        // عرض الرسوم البيانية
        function renderCharts() {
            var chartsContainer = document.getElementById('chartsSection');
            var chartsHTML = '';

            // التحقق من توفر Chart.js
            if (typeof Chart !== 'undefined') {
                console.log('✅ استخدام Chart.js للرسوم البيانية');
                chartsHTML = createAdvancedCharts();
            } else {
                console.log('⚠️ Chart.js غير متوفر، استخدام الرسوم البسيطة');
                chartsHTML = createSimpleCharts();
            }

            chartsContainer.innerHTML = chartsHTML;

            // إنشاء الرسوم المتقدمة إذا كان Chart.js متوفراً
            if (typeof Chart !== 'undefined' && chartsHTML.indexOf('canvas') !== -1) {
                setTimeout(function() {
                    createCharts();
                }, 100);
            }
        }

        // إنشاء رسوم بيانية بسيطة بدون Chart.js
        function createSimpleCharts() {
            var chartsHTML = '';

            // رسم بياني بسيط لأوقات الذروة
            if (emailAnalytics.data.peak_hours && emailAnalytics.data.peak_hours.length > 0) {
                var maxCount = Math.max.apply(Math, emailAnalytics.data.peak_hours.map(function(h) { return h.count; }));
                var barsHTML = '';

                for (var i = 0; i < Math.min(8, emailAnalytics.data.peak_hours.length); i++) {
                    var hour = emailAnalytics.data.peak_hours[i];
                    var percentage = (hour.count / maxCount * 100);
                    barsHTML += '<div style="display: flex; align-items: center; margin-bottom: 1.2rem;">' +
                        '<div style="width: 80px; color: #ffffff; font-size: 1rem; text-align: left; font-weight: 500;">' + hour.hour + '</div>' +
                        '<div style="flex: 1; background: rgba(255, 255, 255, 0.1); border-radius: 12px; margin: 0 1rem; height: 28px; position: relative;">' +
                        '<div style="background: linear-gradient(90deg, #667eea, #764ba2); height: 100%; width: ' + percentage + '%; border-radius: 12px; display: flex; align-items: center; justify-content: flex-end; padding-right: 0.8rem;">' +
                        '<span style="color: white; font-size: 0.9rem; font-weight: 600;">' + hour.count + '</span>' +
                        '</div></div></div>';
                }

                chartsHTML += '<div class="chart-container">' +
                    '<h3 class="chart-title"><i class="fas fa-clock"></i> أوقات النشاط الذروة</h3>' +
                    '<div style="padding: 1.5rem; min-height: 250px; overflow-y: auto;">' + barsHTML + '</div>' +
                    '</div>';
            }

            // رسم بسيط لأهم المرسلين
            if (emailAnalytics.data.top_senders && emailAnalytics.data.top_senders.length > 0) {
                var sendersHTML = '';
                var maxSenderCount = Math.max.apply(Math, emailAnalytics.data.top_senders.map(function(s) { return s.count; }));

                for (var i = 0; i < Math.min(5, emailAnalytics.data.top_senders.length); i++) {
                    var sender = emailAnalytics.data.top_senders[i];
                    var percentage = (sender.count / maxSenderCount * 100);
                    var displayEmail = sender.email.length > 30 ? sender.email.substring(0, 30) + '...' : sender.email;
                    sendersHTML += '<div style="display: flex; align-items: center; margin-bottom: 1.2rem;">' +
                        '<div style="width: 35px; height: 35px; background: #667eea; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; margin-left: 1rem; font-size: 1rem;">' + (i + 1) + '</div>' +
                        '<div style="flex: 1;">' +
                        '<div style="color: #ffffff; font-size: 1rem; margin-bottom: 0.5rem; font-weight: 500;">' + displayEmail + '</div>' +
                        '<div style="background: rgba(255, 255, 255, 0.1); border-radius: 10px; height: 10px;">' +
                        '<div style="background: linear-gradient(90deg, #667eea, #764ba2); height: 100%; width: ' + percentage + '%; border-radius: 10px;"></div>' +
                        '</div></div>' +
                        '<div style="color: #a0a0a0; font-size: 0.9rem; margin-right: 1rem; font-weight: 600;">' + sender.count + '</div>' +
                        '</div>';
                }

                chartsHTML += '<div class="chart-container">' +
                    '<h3 class="chart-title"><i class="fas fa-users"></i> أهم المرسلين</h3>' +
                    '<div style="padding: 1.5rem; min-height: 250px; overflow-y: auto;">' + sendersHTML + '</div>' +
                    '</div>';
            }

            // إذا لم توجد بيانات
            if (!chartsHTML) {
                chartsHTML = '<div class="chart-container">' +
                    '<h3 class="chart-title"><i class="fas fa-info-circle"></i> لا توجد بيانات كافية للتحليل</h3>' +
                    '<p style="color: #a0a0a0; text-align: center; padding: 2rem;">قم بجلب المزيد من الرسائل لرؤية التحليلات التفاعلية</p>' +
                    '</div>';
            }

            return chartsHTML;
        }

        // إنشاء رسوم متقدمة مع Chart.js
        function createAdvancedCharts() {
            var chartsHTML = '';

            // رسم بياني لأوقات الذروة
            if (emailAnalytics.data.peak_hours && emailAnalytics.data.peak_hours.length > 0) {
                chartsHTML += '<div class="chart-container">' +
                    '<h3 class="chart-title"><i class="fas fa-clock"></i> أوقات النشاط الذروة</h3>' +
                    '<canvas id="peakHoursChart" height="300"></canvas>' +
                    '</div>';
            }

            // رسم دائري لأحجام الرسائل
            if (emailAnalytics.data.size_analysis && emailAnalytics.data.size_analysis.length > 0) {
                chartsHTML += '<div class="chart-container">' +
                    '<h3 class="chart-title"><i class="fas fa-chart-pie"></i> توزيع أحجام الرسائل</h3>' +
                    '<canvas id="sizePieChart" height="300"></canvas>' +
                    '</div>';
            }

            return chartsHTML;
        }

        // إنشاء الرسوم البيانية
        function createCharts() {
            // التحقق من تحميل Chart.js
            if (typeof Chart === 'undefined') {
                console.error('❌ Chart.js غير محمل! جاري إعادة المحاولة...');
                setTimeout(function() {
                    createCharts();
                }, 1000);
                return;
            }

            console.log('✅ Chart.js محمل بنجاح، إنشاء الرسوم البيانية...');

            try {
                // رسم أوقات الذروة
                if (emailAnalytics.data.peak_hours && emailAnalytics.data.peak_hours.length > 0) {
                var ctx1 = document.getElementById('peakHoursChart');
                if (ctx1) {
                    var labels = [];
                    var data = [];
                    for (var i = 0; i < emailAnalytics.data.peak_hours.length; i++) {
                        labels.push(emailAnalytics.data.peak_hours[i].hour);
                        data.push(emailAnalytics.data.peak_hours[i].count);
                    }

                    emailAnalytics.charts.peakHours = new Chart(ctx1, {
                        type: 'bar',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: 'عدد الرسائل',
                                data: data,
                                backgroundColor: 'rgba(102, 126, 234, 0.8)',
                                borderColor: 'rgba(102, 126, 234, 1)',
                                borderWidth: 2,
                                borderRadius: 10
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: { labels: { color: '#ffffff' } }
                            },
                            scales: {
                                y: {
                                    ticks: { color: '#ffffff' },
                                    grid: { color: 'rgba(255, 255, 255, 0.1)' }
                                },
                                x: {
                                    ticks: { color: '#ffffff' },
                                    grid: { color: 'rgba(255, 255, 255, 0.1)' }
                                }
                            }
                        }
                    });
                }
            }

            // رسم أحجام الرسائل
            if (emailAnalytics.data.size_analysis && emailAnalytics.data.size_analysis.length > 0) {
                var ctx2 = document.getElementById('sizePieChart');
                if (ctx2) {
                    var labels = [];
                    var data = [];
                    for (var i = 0; i < emailAnalytics.data.size_analysis.length; i++) {
                        labels.push(emailAnalytics.data.size_analysis[i].category);
                        data.push(emailAnalytics.data.size_analysis[i].count);
                    }

                    emailAnalytics.charts.sizePie = new Chart(ctx2, {
                        type: 'doughnut',
                        data: {
                            labels: labels,
                            datasets: [{
                                data: data,
                                backgroundColor: [
                                    'rgba(102, 126, 234, 0.8)',
                                    'rgba(118, 75, 162, 0.8)',
                                    'rgba(240, 147, 251, 0.8)'
                                ],
                                borderColor: [
                                    'rgba(102, 126, 234, 1)',
                                    'rgba(118, 75, 162, 1)',
                                    'rgba(240, 147, 251, 1)'
                                ],
                                borderWidth: 2
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: { labels: { color: '#ffffff' } }
                            }
                        }
                    });
                }
            }

            // رسم الاتجاه الأسبوعي
            if (emailAnalytics.data.weekly_trend && emailAnalytics.data.weekly_trend.length > 0) {
                var ctx3 = document.getElementById('weeklyTrendChart');
                if (ctx3) {
                    var labels = [];
                    var data = [];
                    for (var i = 0; i < emailAnalytics.data.weekly_trend.length; i++) {
                        labels.push(emailAnalytics.data.weekly_trend[i].date);
                        data.push(emailAnalytics.data.weekly_trend[i].count);
                    }

                    emailAnalytics.charts.weeklyTrend = new Chart(ctx3, {
                        type: 'line',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: 'الرسائل اليومية',
                                data: data,
                                borderColor: 'rgba(102, 126, 234, 1)',
                                backgroundColor: 'rgba(102, 126, 234, 0.2)',
                                borderWidth: 3,
                                fill: true,
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: { labels: { color: '#ffffff' } }
                            },
                            scales: {
                                y: {
                                    ticks: { color: '#ffffff' },
                                    grid: { color: 'rgba(255, 255, 255, 0.1)' }
                                },
                                x: {
                                    ticks: { color: '#ffffff' },
                                    grid: { color: 'rgba(255, 255, 255, 0.1)' }
                                }
                            }
                        }
                    });
                }
            }

            } catch (error) {
                console.error('❌ خطأ في إنشاء الرسوم البيانية:', error);
                // عرض رسالة خطأ بدلاً من الرسوم البيانية
                var chartsContainer = document.getElementById('chartsSection');
                chartsContainer.innerHTML = '<div class="chart-container">' +
                    '<h3 class="chart-title"><i class="fas fa-exclamation-triangle"></i> خطأ في تحميل الرسوم البيانية</h3>' +
                    '<p style="color: #a0a0a0; text-align: center; padding: 2rem;">حدث خطأ في تحميل مكتبة الرسوم البيانية. يرجى إعادة تحميل الصفحة.</p>' +
                    '</div>';
            }
        }

        // عرض الرؤى الذكية
        function renderInsights() {
            var insightsContainer = document.getElementById('insightsSection');
            var insightsHTML = '';

            // رؤى أهم المرسلين - فقط إذا كانت البيانات موجودة
            if (emailAnalytics.data.top_senders && emailAnalytics.data.top_senders.length > 0) {
                var sendersHTML = '';
                for (var i = 0; i < emailAnalytics.data.top_senders.length; i++) {
                    var sender = emailAnalytics.data.top_senders[i];
                    sendersHTML += '<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem; padding: 0.5rem; background: rgba(255, 255, 255, 0.1); border-radius: 10px;">' +
                        '<div>' +
                        '<span style="background: #667eea; color: white; padding: 0.2rem 0.5rem; border-radius: 50%; font-size: 0.8rem; margin-left: 0.5rem;">' + (i + 1) + '</span>' +
                        '<span style="color: #ffffff;">' + sender.email + '</span>' +
                        '</div>' +
                        '<span style="background: #28a745; color: white; padding: 0.2rem 0.5rem; border-radius: 15px; font-size: 0.8rem;">' + sender.count + '</span>' +
                        '</div>';
                }

                insightsHTML += '<div class="insight-card">' +
                    '<h4 class="insight-title"><i class="fas fa-users"></i> أهم المرسلين</h4>' +
                    '<div class="insight-content">' + sendersHTML + '</div>' +
                    '</div>';
            }

            // رؤى ذكية حقيقية - فقط إذا كانت البيانات موجودة
            var realInsights = generateRealInsights();
            if (realInsights.length > 0) {
                var insightsListHTML = '';
                for (var i = 0; i < realInsights.length; i++) {
                    var insight = realInsights[i];
                    insightsListHTML += '<div style="margin-bottom: 1rem; padding: 1rem; background: rgba(102, 126, 234, 0.1); border-radius: 10px; border-right: 3px solid #667eea;">' +
                        '<div style="color: #ffffff; font-weight: 600; margin-bottom: 0.5rem;">' + insight.title + '</div>' +
                        '<div style="color: #a0a0a0;">' + insight.content + '</div>' +
                        '</div>';
                }

                insightsHTML += '<div class="insight-card">' +
                    '<h4 class="insight-title"><i class="fas fa-brain"></i> رؤى ذكية</h4>' +
                    '<div class="insight-content">' + insightsListHTML + '</div>' +
                    '</div>';
            }

            // إذا لم توجد رؤى
            if (!insightsHTML) {
                insightsHTML = '<div class="insight-card">' +
                    '<h4 class="insight-title"><i class="fas fa-info-circle"></i> لا توجد رؤى متاحة</h4>' +
                    '<div class="insight-content">قم بجلب المزيد من الرسائل لرؤية الرؤى الذكية</div>' +
                    '</div>';
            }

            insightsContainer.innerHTML = insightsHTML;
        }

        // توليد الرؤى الذكية الحقيقية
        function generateRealInsights() {
            var insights = [];
            var totalEmails = {{ stats.total_emails or 0 }};
            var unreadEmails = {{ stats.unread_count or 0 }};
            var thisWeekEmails = {{ stats.emails_this_week or 0 }};

            // رؤية معدل القراءة - فقط إذا كان هناك رسائل
            if (totalEmails > 0) {
                var readRate = ((totalEmails - unreadEmails) / totalEmails * 100).toFixed(1);
                var rateStatus = readRate > 80 ? 'ممتاز!' : readRate > 60 ? 'جيد' : 'يحتاج تحسين';
                insights.push({
                    title: '📊 معدل القراءة',
                    content: 'معدل قراءتك للرسائل هو ' + readRate + '% - ' + rateStatus
                });
            }

            // رؤية النشاط الأسبوعي - فقط إذا كان هناك نشاط
            if (thisWeekEmails > 0) {
                var dailyAverage = (thisWeekEmails / 7).toFixed(1);
                insights.push({
                    title: '📈 النشاط الأسبوعي',
                    content: 'تستقبل في المتوسط ' + dailyAverage + ' رسالة يومياً هذا الأسبوع'
                });
            }

            // رؤية أوقات الذروة - فقط إذا كانت البيانات موجودة
            if (emailAnalytics.data.peak_hours && emailAnalytics.data.peak_hours.length > 0) {
                var topHour = emailAnalytics.data.peak_hours[0];
                insights.push({
                    title: '🕐 وقت الذروة',
                    content: 'أكثر أوقاتك نشاطاً هو ' + topHour.hour + ' بـ ' + topHour.count + ' رسالة'
                });
            }

            // رؤية أحجام الرسائل - فقط إذا كانت البيانات موجودة
            if (emailAnalytics.data.size_analysis && emailAnalytics.data.size_analysis.length > 0) {
                var dominant = emailAnalytics.data.size_analysis[0];
                for (var i = 1; i < emailAnalytics.data.size_analysis.length; i++) {
                    if (emailAnalytics.data.size_analysis[i].count > dominant.count) {
                        dominant = emailAnalytics.data.size_analysis[i];
                    }
                }
                insights.push({
                    title: '📝 أحجام الرسائل',
                    content: 'معظم رسائلك ' + dominant.category + ' (' + dominant.count + ' رسالة)'
                });
            }

            // رؤية عامة عن الإنتاجية
            if (totalEmails > 0 && unreadEmails >= 0) {
                var productivityTip = '';
                if (unreadEmails === 0) {
                    productivityTip = 'ممتاز! لديك صندوق وارد نظيف 🎉';
                } else if (unreadEmails < 10) {
                    productivityTip = 'جيد! عدد قليل من الرسائل غير المقروءة';
                } else if (unreadEmails < 50) {
                    productivityTip = 'يمكنك تحسين إدارة البريد قليلاً';
                } else {
                    productivityTip = 'ينصح بتنظيم صندوق الوارد وقراءة الرسائل المهمة';
                }

                insights.push({
                    title: '⚡ تقييم الإنتاجية',
                    content: productivityTip
                });
            }

            return insights;
        }

        // عرض رسالة عدم وجود بيانات
        function showNoDataMessage(errorMessage) {
            document.getElementById('loadingIndicator').style.display = 'none';

            // تحديث رسالة الخطأ إذا كانت متوفرة
            if (errorMessage) {
                var noDataText = document.querySelector('.no-data-text');
                noDataText.innerHTML = '<strong>خطأ:</strong> ' + errorMessage + '<br><br>' +
                    'تأكد من وجود حساب بريد إلكتروني نشط وأن هناك رسائل للتحليل.';
            }

            document.getElementById('noDataMessage').style.display = 'block';
        }

        // دالة تصدير التحليلات
        function exportAnalytics() {
            if (!emailAnalytics.data) {
                alert('لا توجد بيانات للتصدير');
                return;
            }

            const exportData = {
                timestamp: new Date().toISOString(),
                stats: {
                    total_emails: {{ stats.total_emails or 0 }},
                    unread_count: {{ stats.unread_count or 0 }},
                    emails_this_week: {{ stats.emails_this_week or 0 }},
                    important_count: {{ stats.important_count or 0 }}
                },
                analytics: emailAnalytics.data
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = 'email_analytics_' + new Date().toISOString().split('T')[0] + '.json';
            link.click();

            console.log('📊 تم تصدير التحليلات بنجاح');
        }

        // تشغيل التطبيق عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📱 تم تحميل الصفحة، بدء التحليلات...');
            console.log('🔍 فحص Chart.js:', typeof Chart !== 'undefined' ? '✅ محمل' : '❌ غير محمل');
            initEmailAnalytics();
        });
    </script>
</body>
</html>
