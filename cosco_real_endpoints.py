#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الـ endpoints الحقيقية المستخرجة من JavaScript
"""

import requests
import urllib3
import json
import time

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_real_cosco_endpoints(booking_number):
    """اختبار الـ endpoints الحقيقية"""
    
    print(f"🎯 اختبار الـ endpoints الحقيقية لرقم: {booking_number}")
    print("=" * 60)
    
    session = requests.Session()
    session.verify = False
    
    # Headers مثل المتصفح تماماً
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Origin': 'https://elines.coscoshipping.com',
        'Referer': 'https://elines.coscoshipping.com/ebusiness/cargoTracking'
    })
    
    try:
        # الخطوة 1: زيارة الصفحة الرئيسية للحصول على cookies
        print("📡 الخطوة 1: زيارة الصفحة الرئيسية...")
        main_response = session.get("https://elines.coscoshipping.com/ebusiness/cargoTracking", timeout=20)
        
        if main_response.status_code == 200:
            print(f"✅ تم الوصول للموقع (Cookies: {len(session.cookies)})")
        else:
            print(f"❌ فشل في الوصول: {main_response.status_code}")
            return False
        
        # الخطوة 2: اختبار الـ endpoints الحقيقية المستخرجة من JavaScript
        print("\n📡 الخطوة 2: اختبار الـ endpoints الحقيقية...")
        
        # الـ endpoints المستخرجة من JavaScript
        real_endpoints = [
            "CARGOTRACKING_SEARCH_BY_BOOKING_GET",
            "CARGOTRACKING_SEARCH_BY_BILL_GET", 
            "CARGOTRACKING_SEARCH_BY_CNTRS_GET"
        ]
        
        # محاولة تخمين الـ URL الكامل
        base_patterns = [
            "https://elines.coscoshipping.com/ebusiness/api/",
            "https://elines.coscoshipping.com/api/",
            "https://elines.coscoshipping.com/ebusiness/",
            "https://elines.coscoshipping.com/ebusiness/cargoTracking/"
        ]
        
        for endpoint in real_endpoints:
            print(f"\n🔍 اختبار endpoint: {endpoint}")
            
            for base in base_patterns:
                full_url = base + endpoint.lower().replace('_', '/')
                
                try:
                    print(f"  📤 اختبار: {full_url}")
                    
                    # بيانات البحث
                    search_data = {
                        "bookingNo": booking_number,
                        "trackingType": "2"
                    }
                    
                    # محاولة POST
                    response = session.post(full_url, json=search_data, timeout=15)
                    print(f"    POST Status: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            print(f"    ✅ JSON Response: {str(data)[:200]}...")
                            
                            # تحليل البيانات
                            if analyze_response_data(data, booking_number):
                                return True
                                
                        except:
                            print(f"    📄 Text Response: {response.text[:200]}...")
                    
                    elif response.status_code not in [404, 405]:
                        print(f"    ⚠️ غير متوقع: {response.status_code}")
                        print(f"    📄 Response: {response.text[:100]}...")
                    
                    # محاولة GET
                    response = session.get(full_url, params=search_data, timeout=15)
                    print(f"    GET Status: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            print(f"    ✅ JSON Response: {str(data)[:200]}...")
                            
                            if analyze_response_data(data, booking_number):
                                return True
                                
                        except:
                            print(f"    📄 Text Response: {response.text[:200]}...")
                    
                except Exception as e:
                    print(f"    ❌ خطأ: {e}")
        
        # الخطوة 3: محاولة endpoints أخرى بناءً على patterns
        print("\n📡 الخطوة 3: محاولة patterns أخرى...")
        
        other_endpoints = [
            "https://elines.coscoshipping.com/ebusiness/cargotracking/search",
            "https://elines.coscoshipping.com/ebusiness/cargotracking/api/search", 
            "https://elines.coscoshipping.com/ebusiness/api/cargotracking/search",
            "https://elines.coscoshipping.com/api/cargotracking/booking",
            "https://elines.coscoshipping.com/ebusiness/rest/cargotracking"
        ]
        
        for url in other_endpoints:
            try:
                print(f"  🔍 اختبار: {url}")
                
                search_data = {
                    "bookingNo": booking_number,
                    "trackingType": "2"
                }
                
                response = session.post(url, json=search_data, timeout=15)
                print(f"    Status: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"    ✅ JSON Response: {str(data)[:200]}...")
                        
                        if analyze_response_data(data, booking_number):
                            return True
                            
                    except:
                        print(f"    📄 Text Response: {response.text[:200]}...")
                
            except Exception as e:
                print(f"    ❌ خطأ: {e}")
        
        return False
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def analyze_response_data(data, booking_number):
    """تحليل استجابة API"""
    try:
        print(f"    🔍 تحليل البيانات...")
        
        if isinstance(data, dict):
            # البحث عن مؤشرات النجاح
            if data.get('success') or data.get('status') == 'success' or data.get('code') == 200:
                print(f"    ✅ استجابة ناجحة!")
                
                # البحث عن البيانات
                content = data.get('data') or data.get('content') or data.get('result')
                
                if content:
                    print(f"    📊 وجد محتوى: {str(content)[:100]}...")
                    
                    # البحث عن تواريخ
                    dates_found = []
                    if isinstance(content, dict):
                        for key, value in content.items():
                            if 'date' in key.lower() or 'time' in key.lower():
                                dates_found.append(f"{key}: {value}")
                    
                    if dates_found:
                        print(f"    📅 وجد تواريخ: {dates_found}")
                        return True
                    
                    # البحث عن معلومات الشحن
                    shipping_info = []
                    if isinstance(content, dict):
                        for key, value in content.items():
                            if any(keyword in key.lower() for keyword in ['vessel', 'port', 'container', 'bl']):
                                shipping_info.append(f"{key}: {value}")
                    
                    if shipping_info:
                        print(f"    🚢 وجد معلومات شحن: {shipping_info}")
                        return True
                    
                    return True
            
            # البحث عن رسائل خطأ
            error_msg = data.get('message') or data.get('error') or data.get('msg')
            if error_msg:
                print(f"    ❌ رسالة خطأ: {error_msg}")
            
            # البحث عن تواريخ مباشرة
            etd = data.get('etd') or data.get('ETD') or data.get('departureDate')
            eta = data.get('eta') or data.get('ETA') or data.get('arrivalDate')
            
            if etd and eta:
                print(f"    📅 وجد تواريخ مباشرة: ETD={etd}, ETA={eta}")
                return True
        
        elif isinstance(data, list) and len(data) > 0:
            print(f"    📋 وجد قائمة بـ {len(data)} عنصر")
            
            # تحليل أول عنصر
            first_item = data[0]
            if isinstance(first_item, dict):
                return analyze_response_data(first_item, booking_number)
        
        return False
        
    except Exception as e:
        print(f"    ❌ خطأ في تحليل البيانات: {e}")
        return False

def main():
    """الاختبار الرئيسي"""
    
    print("=" * 80)
    print("🎯 اختبار الـ endpoints الحقيقية لـ COSCO")
    print("=" * 80)
    
    booking_number = "6425375050"
    
    success = test_real_cosco_endpoints(booking_number)
    
    print(f"\n" + "=" * 80)
    if success:
        print("✅ نجح في العثور على endpoint حقيقي!")
    else:
        print("❌ لم يتم العثور على endpoint يعمل")
    print("=" * 80)

if __name__ == "__main__":
    main()
