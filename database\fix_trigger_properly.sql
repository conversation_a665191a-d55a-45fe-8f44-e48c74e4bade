-- إصلاح الـ trigger بطريقة احترافية
-- المشكلة: الـ trigger يحاول تحديث جدول TRANSFERS أثناء إدراج في transfer_execution_suppliers
-- الحل: استخدام AUTONOMOUS TRANSACTION أو تأجيل التحديث

-- 1. حذف الـ trigger المعطل
DROP TRIGGER TRG_UPDATE_SUPPLIERS_COUNT;

-- 2. إنشاء trigger محسن يعمل بشكل صحيح
CREATE OR REPLACE TRIGGER trg_update_suppliers_count_fixed
    AFTER INSERT OR DELETE ON transfer_execution_suppliers
    FOR EACH ROW
DECLARE
    PRAGMA AUTONOMOUS_TRANSACTION;
    v_count NUMBER;
BEGIN
    -- حسا<PERSON> عدد الموردين للتحويل
    IF INSERTING THEN
        SELECT COUNT(*) INTO v_count
        FROM transfer_execution_suppliers 
        WHERE transfer_id = :NEW.transfer_id;
        
        -- تحديث عدد الموردين في جدول TRANSFERS
        UPDATE transfers 
        SET total_suppliers = v_count,
            updated_execution_at = CURRENT_TIMESTAMP
        WHERE id = :NEW.transfer_id;
        
    ELSIF DELETING THEN
        SELECT COUNT(*) INTO v_count
        FROM transfer_execution_suppliers 
        WHERE transfer_id = :OLD.transfer_id;
        
        -- تحديث عدد الموردين في جدول TRANSFERS
        UPDATE transfers 
        SET total_suppliers = v_count,
            updated_execution_at = CURRENT_TIMESTAMP
        WHERE id = :OLD.transfer_id;
    END IF;
    
    COMMIT; -- ضروري مع AUTONOMOUS_TRANSACTION
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        -- تسجيل الخطأ بدلاً من إيقاف العملية
        INSERT INTO error_log (error_message, error_date) 
        VALUES ('Trigger error: ' || SQLERRM, SYSDATE);
        COMMIT;
END;
/

-- 3. إنشاء جدول لتسجيل الأخطاء إذا لم يكن موجوداً
BEGIN
    EXECUTE IMMEDIATE 'CREATE TABLE error_log (
        id NUMBER PRIMARY KEY,
        error_message VARCHAR2(4000),
        error_date DATE DEFAULT SYSDATE
    )';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 THEN -- تجاهل خطأ "الجدول موجود"
            RAISE;
        END IF;
END;
/

-- 4. إنشاء sequence لجدول الأخطاء
BEGIN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE error_log_seq START WITH 1 INCREMENT BY 1';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 THEN -- تجاهل خطأ "الـ sequence موجود"
            RAISE;
        END IF;
END;
/

-- 5. إنشاء trigger لـ error_log
CREATE OR REPLACE TRIGGER error_log_trigger
    BEFORE INSERT ON error_log
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := error_log_seq.NEXTVAL;
    END IF;
END;
/

-- 6. اختبار الـ trigger الجديد
INSERT INTO transfer_execution_suppliers (transfer_id, supplier_id, supplier_name, amount) 
VALUES (3, 124, 'مورد اختبار', 2000);

-- 7. التحقق من النتيجة
SELECT t.id, t.transfer_number, t.total_suppliers, 
       COUNT(tes.id) as actual_suppliers_count
FROM transfers t
LEFT JOIN transfer_execution_suppliers tes ON t.id = tes.transfer_id
WHERE t.id = 3
GROUP BY t.id, t.transfer_number, t.total_suppliers;

COMMIT;
