-- =====================================================
-- إنشاء Trigger للأتمتة التلقائية
-- Create Automatic Automation Trigger
-- التاريخ: 2025-08-20
-- =====================================================

-- إنشاء trigger لتسجيل تغييرات حالات الشحنات وتشغيل الأتمتة
CREATE OR REPLACE TRIGGER trg_shipment_automation
    AFTER UPDATE OF status ON shipments
    FOR EACH ROW
    WHEN (OLD.status != NEW.status)
DECLARE
    v_count NUMBER;
BEGIN
    -- تسجيل تغيير الحالة في جدول المراقبة
    INSERT INTO shipment_status_changes (
        id, shipment_id, old_status, new_status, 
        changed_at, automation_processed
    ) VALUES (
        shipment_status_changes_seq.NEXTVAL,
        :NEW.id,
        :OLD.status,
        :NEW.status,
        CURRENT_TIMESTAMP,
        0  -- لم تتم المعالجة بعد
    );
    
    -- فحص وجود قواعد أتمتة نشطة تطابق هذا التغيير
    SELECT COUNT(*)
    INTO v_count
    FROM automation_rules
    WHERE is_active = 1
    AND trigger_condition = 'STATUS_CHANGE_ACTION'
    AND condition_value = :NEW.status;
    
    -- إذا وجدت قواعد مطابقة، قم بتشغيل الأتمتة
    IF v_count > 0 THEN
        -- إدراج مهمة أتمتة للمعالجة
        INSERT INTO automation_queue (
            id, shipment_id, old_status, new_status,
            created_at, processed
        ) VALUES (
            automation_queue_seq.NEXTVAL,
            :NEW.id,
            :OLD.status,
            :NEW.status,
            CURRENT_TIMESTAMP,
            0
        );
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        -- تسجيل الخطأ دون إيقاف العملية الأساسية
        INSERT INTO automation_errors (
            id, error_message, shipment_id, occurred_at
        ) VALUES (
            automation_errors_seq.NEXTVAL,
            SQLERRM,
            :NEW.id,
            CURRENT_TIMESTAMP
        );
END;
/

-- إنشاء جدول طابور الأتمتة
CREATE TABLE automation_queue (
    id NUMBER PRIMARY KEY,
    shipment_id NUMBER NOT NULL,
    old_status VARCHAR2(50),
    new_status VARCHAR2(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed NUMBER(1) DEFAULT 0,
    processed_at TIMESTAMP,
    processing_attempts NUMBER DEFAULT 0,
    last_error CLOB,
    
    CONSTRAINT fk_auto_queue_shipment FOREIGN KEY (shipment_id) REFERENCES shipments(id)
);

-- إنشاء sequence
CREATE SEQUENCE automation_queue_seq START WITH 1 INCREMENT BY 1;

-- إنشاء جدول أخطاء الأتمتة
CREATE TABLE automation_errors (
    id NUMBER PRIMARY KEY,
    error_message CLOB,
    shipment_id NUMBER,
    occurred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved NUMBER(1) DEFAULT 0
);

-- إنشاء sequence
CREATE SEQUENCE automation_errors_seq START WITH 1 INCREMENT BY 1;

-- إنشاء فهارس
CREATE INDEX idx_auto_queue_processed ON automation_queue(processed);
CREATE INDEX idx_auto_queue_shipment ON automation_queue(shipment_id);
CREATE INDEX idx_auto_errors_resolved ON automation_errors(resolved);

-- تأكيد التغييرات
COMMIT;

-- عرض ملخص الإنشاء
SELECT 'تم إنشاء نظام الأتمتة التلقائية بنجاح' AS status FROM dual;

PROMPT =====================================================
PROMPT تم إنشاء Trigger الأتمتة التلقائية بنجاح!
PROMPT الآن ستعمل الأتمتة تلقائياً عند تغيير حالة الشحنة
PROMPT =====================================================
