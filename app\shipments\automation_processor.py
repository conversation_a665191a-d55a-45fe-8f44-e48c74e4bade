"""
معالج طابور الأتمتة
Automation Queue Processor
"""

import time
import threading
from datetime import datetime
from database_manager import DatabaseManager
from .automation_engine import automation_engine


class AutomationProcessor:
    """
    معالج طابور الأتمتة التلقائية

    ملاحظة: Database Triggers تتولى إضافة العمليات للطابور فوراً عند تحديث البيانات.
    هذا المعالج يعمل كنظام احتياطي للتأكد من عدم تراكم العمليات غير المعالجة.
    الفحص كل ساعتين كافٍ لأن المعالجة الفورية تتم عبر Triggers.
    """
    
    def __init__(self):
        self.running = False
        self.thread = None
        self.check_interval = 7200  # فحص كل ساعتين (Database Triggers تتولى المعالجة الفورية)
        self.min_interval = 3600   # أقل فترة: ساعة واحدة
        self.max_interval = 14400  # أطول فترة: 4 ساعات
        self.consecutive_empty_checks = 0  # عداد الفحوصات الفارغة
        
    def start(self):
        """بدء معالج الأتمتة"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self._process_loop, daemon=True)
            self.thread.start()
            print("🚀 تم بدء معالج الأتمتة التلقائية")
    
    def stop(self):
        """إيقاف معالج الأتمتة"""
        self.running = False
        if self.thread:
            self.thread.join()
        print("⏹️ تم إيقاف معالج الأتمتة التلقائية")
    
    def _process_loop(self):
        """حلقة معالجة الطابور مع آلية تكيفية"""
        while self.running:
            try:
                processed_items = self._process_queue()

                # تكييف الفترة الزمنية حسب العمل
                if processed_items and processed_items > 0:
                    # يوجد عمل - تقليل الفترة
                    self.consecutive_empty_checks = 0
                    self.check_interval = max(self.min_interval, self.check_interval - 1800)  # تقليل 30 دقيقة
                    hours = self.check_interval / 3600
                    print(f"⚡ تم تسريع المعالجة: فحص كل {hours:.1f} ساعة")
                else:
                    # لا يوجد عمل - زيادة الفترة تدريجياً
                    self.consecutive_empty_checks += 1
                    if self.consecutive_empty_checks >= 2:  # تقليل العدد لأن الفترات طويلة
                        self.check_interval = min(self.max_interval, self.check_interval + 1800)  # زيادة 30 دقيقة
                        hours = self.check_interval / 3600
                        print(f"💤 تم إبطاء المعالجة لتوفير الموارد: فحص كل {hours:.1f} ساعة")

                time.sleep(self.check_interval)

            except Exception as e:
                print(f"❌ خطأ في معالج الأتمتة: {e}")
                time.sleep(self.check_interval * 2)  # انتظار أطول عند الخطأ
    
    def _process_queue(self):
        """معالجة عناصر الطابور"""
        db_manager = None
        processed_count = 0

        try:
            db_manager = DatabaseManager()

            # فحص سريع لوجود عناصر غير معالجة (توفير الموارد)
            count_query = """
                SELECT COUNT(*) FROM automation_queue
                WHERE processed = 0 AND processing_attempts < 3
            """

            count_result = db_manager.execute_query(count_query)

            if not count_result or count_result[0][0] == 0:
                return 0  # لا توجد عناصر للمعالجة - توفير الموارد

            # جلب العناصر غير المعالجة من الطابور
            queue_query = """
                SELECT id, shipment_id, old_status, new_status, created_at
                FROM automation_queue
                WHERE processed = 0
                AND processing_attempts < 3
                ORDER BY created_at ASC
                FETCH FIRST 10 ROWS ONLY
            """

            queue_items = db_manager.execute_query(queue_query)

            if not queue_items:
                return 0  # لا توجد عناصر للمعالجة

            print(f"🔄 معالجة {len(queue_items)} عنصر من طابور الأتمتة")
            
            for item in queue_items:
                queue_id = item[0]
                shipment_id = item[1]
                old_status = item[2]
                new_status = item[3]
                created_at = item[4]
                
                try:
                    # تحديث حالة المعالجة
                    update_processing_query = """
                        UPDATE automation_queue 
                        SET processing_attempts = processing_attempts + 1
                        WHERE id = :queue_id
                    """
                    db_manager.execute_update(update_processing_query, {'queue_id': queue_id})
                    
                    print(f"⚡ معالجة الشحنة {shipment_id}: {old_status} → {new_status}")
                    
                    # تشغيل محرك الأتمتة
                    automation_engine.process_shipment_status_change(
                        shipment_id, old_status, new_status
                    )
                    
                    # تحديث حالة المعالجة إلى مكتملة
                    complete_processing_query = """
                        UPDATE automation_queue 
                        SET processed = 1, processed_at = CURRENT_TIMESTAMP
                        WHERE id = :queue_id
                    """
                    db_manager.execute_update(complete_processing_query, {'queue_id': queue_id})
                    
                    print(f"✅ تم معالجة الشحنة {shipment_id} بنجاح")
                    processed_count += 1
                    
                except Exception as e:
                    error_msg = str(e)
                    print(f"❌ خطأ في معالجة الشحنة {shipment_id}: {error_msg}")
                    
                    # تسجيل الخطأ
                    error_update_query = """
                        UPDATE automation_queue 
                        SET last_error = :error_msg
                        WHERE id = :queue_id
                    """
                    db_manager.execute_update(error_update_query, {
                        'queue_id': queue_id,
                        'error_msg': error_msg
                    })
                    
                    # إذا فشلت المحاولات، ضع كمعالجة لتجنب إعادة المحاولة
                    check_attempts_query = """
                        SELECT processing_attempts FROM automation_queue WHERE id = :queue_id
                    """
                    attempts_result = db_manager.execute_query(check_attempts_query, {'queue_id': queue_id})
                    
                    if attempts_result and attempts_result[0][0] >= 3:
                        fail_processing_query = """
                            UPDATE automation_queue 
                            SET processed = 1, processed_at = CURRENT_TIMESTAMP
                            WHERE id = :queue_id
                        """
                        db_manager.execute_update(fail_processing_query, {'queue_id': queue_id})
                        print(f"⚠️ تم تجاهل الشحنة {shipment_id} بعد 3 محاولات فاشلة")
        
        except Exception as e:
            print(f"❌ خطأ في معالجة الطابور: {e}")
            return 0  # إرجاع 0 عند الخطأ

        finally:
            if db_manager:
                db_manager.close()

        return processed_count
    
    def process_now(self):
        """معالجة فورية للطابور (للاستخدام عند الحاجة)"""
        print("🚀 تشغيل معالجة فورية للطابور...")
        processed_count = self._process_queue()
        print(f"✅ تمت معالجة {processed_count} عنصر فورياً")
        return processed_count

    def get_queue_status(self):
        """الحصول على حالة الطابور"""
        db_manager = None
        
        try:
            db_manager = DatabaseManager()
            
            status_query = """
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN processed = 0 THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN processed = 1 THEN 1 ELSE 0 END) as completed,
                    SUM(CASE WHEN processing_attempts >= 3 AND processed = 1 THEN 1 ELSE 0 END) as failed
                FROM automation_queue
            """
            
            result = db_manager.execute_query(status_query)
            
            if result:
                row = result[0]
                return {
                    'total': row[0] or 0,
                    'pending': row[1] or 0,
                    'completed': row[2] or 0,
                    'failed': row[3] or 0,
                    'running': self.running,
                    'check_interval': self.check_interval,
                    'consecutive_empty_checks': self.consecutive_empty_checks
                }
            
            return {
                'total': 0,
                'pending': 0,
                'completed': 0,
                'failed': 0,
                'running': self.running
            }
        
        except Exception as e:
            print(f"❌ خطأ في جلب حالة الطابور: {e}")
            return {
                'total': 0,
                'pending': 0,
                'completed': 0,
                'failed': 0,
                'running': self.running,
                'error': str(e)
            }
        
        finally:
            if db_manager:
                db_manager.close()


# إنشاء مثيل عام من معالج الأتمتة
automation_processor = AutomationProcessor()

# بدء المعالج تلقائياً عند استيراد الوحدة
automation_processor.start()
