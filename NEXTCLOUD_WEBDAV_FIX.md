# دليل إصلاح مشكلة WebDAV في Nextcloud
# Nextcloud WebDAV Issue Fix Guide

## 🔍 **المشكلة المحددة:**
- خادم Nextcloud يعمل بشكل صحيح (status.php يعمل)
- WebDAV timeout عند محاولة المصادقة
- خطأ: `HTTPSConnectionPool read timed out`

## 🔧 **الحلول المقترحة:**

### 1️⃣ **فحص إعدادات WebDAV في Nextcloud:**

#### أ. تسجيل الدخول إلى لوحة إدارة Nextcloud:
```
URL: https://alselwy.net/nextcloud
Username: admin (أو المدير)
```

#### ب. فحص إعدادات WebDAV:
1. اذهب إلى **الإعدادات** → **الإدارة** → **إعدادات إضافية**
2. تأكد من تفعيل **WebDAV**
3. فحص **حدود الوقت** (Timeout settings)

#### ج. فحص إعدادات المستخدم:
1. اذهب إلى **المستخدمون**
2. تأكد من وجود المستخدم `mohmd` أو `MOHMD`
3. تحقق من **الصلاحيات** و **المجموعات**

### 2️⃣ **إعدادات الخادم (Apache/Nginx):**

#### للـ Apache:
```apache
# في ملف .htaccess أو إعدادات Apache
<IfModule mod_reqtimeout.c>
    RequestReadTimeout header=20-40,MinRate=500 body=20,MinRate=500
</IfModule>

# زيادة حدود الوقت
TimeOut 300
KeepAliveTimeout 15
```

#### للـ Nginx:
```nginx
# في إعدادات Nginx
client_body_timeout 60s;
client_header_timeout 60s;
proxy_read_timeout 300s;
proxy_connect_timeout 300s;
```

### 3️⃣ **إعدادات PHP:**

```ini
# في php.ini
max_execution_time = 300
max_input_time = 300
memory_limit = 512M
upload_max_filesize = 100M
post_max_size = 100M
```

### 4️⃣ **اختبار WebDAV يدوياً:**

#### أ. من سطر الأوامر:
```bash
# اختبار WebDAV مع curl
curl -X PROPFIND \
  -u "mohmd:كلمة_المرور" \
  -H "Depth: 1" \
  "https://alselwy.net/nextcloud/remote.php/dav/files/mohmd/"
```

#### ب. من متصفح الويب:
```
URL: https://alselwy.net/nextcloud/remote.php/dav/files/mohmd/
```

### 5️⃣ **إنشاء مستخدم جديد للاختبار:**

#### أ. في لوحة إدارة Nextcloud:
1. **المستخدمون** → **إضافة مستخدم**
2. اسم المستخدم: `saserp`
3. كلمة المرور: `SASERP2024!`
4. البريد الإلكتروني: `<EMAIL>`
5. المجموعة: `users`

#### ب. إنشاء كلمة مرور التطبيق:
1. سجل دخول بالمستخدم الجديد
2. **الإعدادات** → **الأمان**
3. **كلمات مرور التطبيق** → إنشاء جديد
4. الاسم: `SASERP_API`

### 6️⃣ **حل بديل - استخدام Nextcloud API:**

بدلاً من WebDAV، يمكن استخدام Nextcloud API:

```python
# استخدام Sharing API بدلاً من WebDAV
import requests

def create_nextcloud_share_via_api(server_url, username, password, file_path):
    """إنشاء رابط مشاركة عبر API بدلاً من WebDAV"""
    
    # رفع الملف عبر API
    upload_url = f"{server_url}/remote.php/webdav/SASERP_Documents/{filename}"
    
    # إنشاء رابط مشاركة عبر OCS API
    share_url = f"{server_url}/ocs/v2.php/apps/files_sharing/api/v1/shares"
    
    # ... باقي الكود
```

### 7️⃣ **تحديث إعدادات SASERP:**

```json
{
    "nextcloud": {
        "enabled": true,
        "server_url": "https://alselwy.net/nextcloud",
        "username": "saserp",
        "password": "SASERP2024!",
        "app_password": "كلمة_مرور_التطبيق_الجديدة",
        "default_folder": "SASERP_Documents",
        "use_api": true,
        "timeout": 60
    }
}
```

## 🚀 **خطوات الإصلاح السريع:**

### الخطوة 1: فحص المستخدم
```bash
# في خادم Nextcloud
sudo -u www-data php occ user:list
```

### الخطوة 2: إنشاء مستخدم جديد
```bash
sudo -u www-data php occ user:add saserp
```

### الخطوة 3: تفعيل WebDAV
```bash
sudo -u www-data php occ app:enable dav
```

### الخطوة 4: إعادة تشغيل الخدمات
```bash
sudo systemctl restart apache2  # أو nginx
sudo systemctl restart php7.4-fpm  # أو الإصدار المناسب
```

## 🔄 **حل مؤقت - تعطيل الخدمة:**

إذا لم تنجح الحلول أعلاه، يمكن:

1. **تعطيل Nextcloud مؤقتاً** في `cloud_config.json`
2. **استخدام التحميل المباشر** للوثائق
3. **إعداد OneDrive** كبديل
4. **العمل على إصلاح Nextcloud** في الخلفية

## 📞 **الدعم:**

إذا استمرت المشكلة:
1. فحص سجلات Nextcloud: `/var/log/nextcloud.log`
2. فحص سجلات الخادم: `/var/log/apache2/error.log`
3. اختبار WebDAV من أدوات أخرى
4. التواصل مع مدير الخادم
