<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإصلاحات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="app/static/css/netsuite-real.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>اختبار الإصلاحات</h1>
        
        <!-- اختبار حجم النصوص في البطاقات -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="ns-card-real">
                    <div class="ns-card-header">
                        <i class="fas fa-test-tube"></i>
                        اختبار حجم النصوص في البطاقات
                    </div>
                    <div class="ns-card-body">
                        <p>هذا نص تجريبي لاختبار حجم النصوص في البطاقات. يجب أن يكون الحجم أكبر من السابق.</p>
                        <table class="ns-table-real">
                            <thead>
                                <tr>
                                    <th>العمود الأول</th>
                                    <th>العمود الثاني</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>بيانات تجريبية</td>
                                    <td>بيانات أخرى</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="ns-card-real">
                    <div class="ns-card-header">
                        <i class="fas fa-search"></i>
                        اختبار البحث السريع
                    </div>
                    <div class="ns-card-body">
                        <div class="mb-3">
                            <label class="form-label">البحث السريع</label>
                            <input type="text" class="form-control" id="quickSearch" placeholder="ابحث هنا...">
                        </div>
                        <button class="ns-btn-real" onclick="testSearch()">اختبار البحث</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- اختبار زر إدارة الوثائق -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="ns-card-real">
                    <div class="ns-card-header">
                        <i class="fas fa-file-alt"></i>
                        اختبار زر إدارة الوثائق
                    </div>
                    <div class="ns-card-body">
                        <button class="btn btn-outline-info" onclick="testManageDocuments(123)">
                            <i class="fas fa-file-alt"></i>
                            اختبار إدارة الوثائق
                        </button>
                        <p class="mt-2 text-muted">انقر على الزر لاختبار فتح نافذة إدارة الوثائق</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- جدول تجريبي للبحث -->
        <div class="row">
            <div class="col-12">
                <div class="ns-card-real">
                    <div class="ns-card-header">
                        <i class="fas fa-table"></i>
                        جدول تجريبي للبحث
                    </div>
                    <div class="ns-card-body">
                        <table class="ns-table-real" id="shipmentsTable">
                            <thead>
                                <tr>
                                    <th>رقم التتبع</th>
                                    <th>المرسل</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>TRK001</td>
                                    <td>أحمد محمد</td>
                                    <td>قيد الشحن</td>
                                </tr>
                                <tr>
                                    <td>TRK002</td>
                                    <td>فاطمة علي</td>
                                    <td>تم التسليم</td>
                                </tr>
                                <tr>
                                    <td>TRK003</td>
                                    <td>محمد حسن</td>
                                    <td>معلق</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // دالة عرض الإشعارات
        function showNotification(message, type = 'info') {
            const typeClasses = {
                'success': 'alert-success',
                'warning': 'alert-warning',
                'error': 'alert-danger',
                'info': 'alert-info'
            };

            const typeIcons = {
                'success': 'fas fa-check-circle',
                'warning': 'fas fa-exclamation-triangle',
                'error': 'fas fa-times-circle',
                'info': 'fas fa-info-circle'
            };

            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${typeClasses[type] || 'alert-info'} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                <i class="${typeIcons[type] || 'fas fa-info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 4000);
        }

        // اختبار إدارة الوثائق
        function testManageDocuments(requestId) {
            console.log('📁 اختبار إدارة وثائق الطلب:', requestId);

            try {
                const documentsUrl = `/transfers/requests/${requestId}/documents`;
                const newWindow = window.open(documentsUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
                
                if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
                    showNotification('⚠️ تم حجب النافذة المنبثقة. يرجى السماح للنوافذ المنبثقة وإعادة المحاولة.', 'warning');
                    
                    if (confirm('هل تريد فتح صفحة إدارة الوثائق في نفس التبويب؟')) {
                        window.location.href = documentsUrl;
                    }
                } else {
                    showNotification('✅ تم فتح صفحة إدارة الوثائق في نافذة جديدة', 'success');
                }
            } catch (error) {
                console.error('❌ خطأ في فتح نافذة إدارة الوثائق:', error);
                showNotification('❌ حدث خطأ في فتح نافذة إدارة الوثائق', 'error');
            }
        }

        // اختبار البحث
        function testSearch() {
            const searchInput = document.getElementById('quickSearch');
            const searchTerm = searchInput.value.toLowerCase().trim();
            
            if (!searchTerm) {
                showNotification('يرجى إدخال نص للبحث', 'warning');
                return;
            }

            const table = document.querySelector('#shipmentsTable tbody');
            const rows = table.querySelectorAll('tr');
            let visibleCount = 0;

            rows.forEach(row => {
                const rowText = row.textContent.toLowerCase();
                
                if (rowText.includes(searchTerm)) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });

            if (visibleCount === 0) {
                showNotification(`لم يتم العثور على نتائج للبحث: "${searchTerm}"`, 'warning');
            } else {
                showNotification(`تم العثور على ${visibleCount} نتيجة`, 'success');
            }
        }

        // إعداد البحث التلقائي
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('quickSearch');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    if (this.value === '') {
                        // إظهار جميع الصفوف
                        const rows = document.querySelectorAll('#shipmentsTable tbody tr');
                        rows.forEach(row => row.style.display = '');
                    } else {
                        testSearch();
                    }
                });
            }
        });
    </script>
</body>
</html>
