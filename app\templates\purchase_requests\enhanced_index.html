{% extends "base.html" %}

{% block title %}طلبات الشراء المحسنة{% endblock %}

{% block extra_css %}
<style>
/* نظام الألوان المحسن */
:root {
    --primary-color: #3498DB;
    --secondary-color: #2ECC71;
    --accent-color: #9B59B6;
    --warning-color: #F1C40F;
    --danger-color: #E74C3C;
    --background-light: #F8F9FA;
    --card-background: #FFFFFF;
    --text-primary: #212529;
    --text-secondary: #6C757D;
}

.enhanced-index {
    background: var(--background-light);
    min-height: 100vh;
    padding: 2rem 0;
    direction: rtl;
}

.stats-card {
    background: var(--card-background);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.requests-table {
    background: var(--card-background);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table th {
    background: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-color: #E9ECEF;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-align: center;
    min-width: 80px;
    display: inline-block;
}

.status-draft { background: #FFF3CD; color: #856404; }
.status-sent { background: #D1ECF1; color: #0C5460; }
.status-approved { background: #D4EDDA; color: #155724; }
.status-rejected { background: #F8D7DA; color: #721C24; }

.action-buttons .btn {
    margin: 0 0.25rem;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.enhanced-toolbar {
    background: var(--card-background);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.toolbar-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    margin: 0 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.toolbar-btn:hover {
    background: #2980B9;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.toolbar-btn.btn-success { background: var(--secondary-color); }
.toolbar-btn.btn-success:hover { background: #27AE60; }

.toolbar-btn.btn-warning { background: var(--warning-color); color: #333; }
.toolbar-btn.btn-warning:hover { background: #F39C12; }

.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.page-link {
    border-radius: 8px;
    margin: 0 0.25rem;
    border: 1px solid #DEE2E6;
    color: var(--primary-color);
}

.page-link:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
}
</style>
{% endblock %}

{% block content %}
<div class="enhanced-index">
    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="display-6 fw-bold text-primary mb-2">
                            <i class="fas fa-file-alt me-3"></i>
                            طلبات الشراء المحسنة
                        </h1>
                        <p class="text-muted mb-0">إدارة شاملة لطلبات الشراء مع واجهة محسنة</p>
                    </div>
                    <div class="enhanced-toolbar">
                        <a href="{{ url_for('purchase_requests.enhanced') }}" class="toolbar-btn">
                            <i class="fas fa-plus me-2"></i>طلب جديد
                        </a>
                        <button class="toolbar-btn btn-success" onclick="refreshData()">
                            <i class="fas fa-sync me-2"></i>تحديث
                        </button>
                        <button class="toolbar-btn btn-warning" onclick="exportData()">
                            <i class="fas fa-download me-2"></i>تصدير
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card h-100">
                    <div class="card-body text-center">
                        <div class="stats-number">{{ stats.total }}</div>
                        <h6 class="card-title text-muted">إجمالي الطلبات</h6>
                        <div class="small text-secondary">
                            <i class="fas fa-file-alt me-1"></i>جميع طلبات الشراء
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card h-100">
                    <div class="card-body text-center">
                        <div class="stats-number text-warning">{{ stats.draft }}</div>
                        <h6 class="card-title text-muted">مسودات</h6>
                        <div class="small text-secondary">
                            <i class="fas fa-edit me-1"></i>طلبات قيد الإعداد
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card h-100">
                    <div class="card-body text-center">
                        <div class="stats-number text-info">{{ stats.sent }}</div>
                        <h6 class="card-title text-muted">مرسلة</h6>
                        <div class="small text-secondary">
                            <i class="fas fa-paper-plane me-1"></i>بانتظار الموافقة
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card h-100">
                    <div class="card-body text-center">
                        <div class="stats-number text-success">{{ stats.approved }}</div>
                        <h6 class="card-title text-muted">معتمدة</h6>
                        <div class="small text-secondary">
                            <i class="fas fa-check-circle me-1"></i>جاهزة للتنفيذ
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الطلبات -->
        <div class="row">
            <div class="col-12">
                <div class="requests-table">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>اسم الطالب</th>
                                    <th>القسم</th>
                                    <th>تاريخ الطلب</th>
                                    <th>الحالة</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in requests.items %}
                                <tr>
                                    <td>
                                        <strong class="text-primary">{{ request.req_no }}</strong>
                                        <br>
                                        <small class="text-muted">#{{ request.req_serial }}</small>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                {{ request.requester_name[0] if request.requester_name else 'ط' }}
                                            </div>
                                            <div>
                                                <div class="fw-bold">{{ request.requester_name }}</div>
                                                <small class="text-muted">{{ request.req_type }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ request.department_name }}</td>
                                    <td>
                                        <div>{{ request.req_date.strftime('%Y-%m-%d') if request.req_date else '-' }}</div>
                                        <small class="text-muted">{{ request.needed_date.strftime('%Y-%m-%d') if request.needed_date else '-' }}</small>
                                    </td>
                                    <td>
                                        <span class="status-badge status-{{ request.req_status.lower().replace('مسودة', 'draft').replace('مرسل', 'sent').replace('معتمد', 'approved').replace('مرفوض', 'rejected') }}">
                                            {{ request.req_status }}
                                        </span>
                                    </td>
                                    <td>
                                        <strong class="text-success">
                                            {% if request.total_amount %}
                                                {{ "%.2f"|format(request.total_amount) }} {{ request.currency or 'ريال' }}
                                            {% else %}
                                                0.00 ريال
                                            {% endif %}
                                        </strong>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{{ url_for('purchase_requests.enhanced') }}?id={{ request.id }}" 
                                               class="btn btn-sm btn-outline-primary" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('purchase_requests.enhanced') }}?id={{ request.id }}&edit=1" 
                                               class="btn btn-sm btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteRequest({{ request.id }})" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center py-5">
                                        <div class="text-muted">
                                            <i class="fas fa-inbox fa-3x mb-3"></i>
                                            <h5>لا توجد طلبات شراء</h5>
                                            <p>ابدأ بإنشاء طلب شراء جديد</p>
                                            <a href="{{ url_for('purchase_requests.enhanced') }}" class="btn btn-primary">
                                                <i class="fas fa-plus me-2"></i>إنشاء طلب جديد
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- التنقل بين الصفحات -->
                {% if requests.pages > 1 %}
                <div class="pagination-wrapper">
                    {{ requests.links }}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function refreshData() {
    location.reload();
}

function exportData() {
    // تصدير البيانات - يمكن تطويرها لاحقاً
    alert('سيتم تطوير وظيفة التصدير قريباً');
}

function deleteRequest(requestId) {
    if (confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
        fetch(`/purchase_requests/api/delete/${requestId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء الحذف: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ أثناء الحذف');
        });
    }
}

// تحديث تلقائي كل 30 ثانية
setInterval(function() {
    // يمكن إضافة تحديث تلقائي للإحصائيات هنا
}, 30000);
</script>
{% endblock %}
