#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة حالات الإفراج إلى الجدول
"""

import sys
sys.path.append('.')
from oracle_manager import OracleManager

def add_release_statuses():
    """إضافة حالات الإفراج إلى الجدول"""
    print('🔧 إضافة حالات الإفراج إلى الجدول...')

    try:
        oracle = OracleManager()
        
        # حذف البيانات القديمة
        delete_sql = "DELETE FROM shipment_release_status_config"
        oracle.execute_update(delete_sql)
        
        # البيانات الافتراضية لحالات الإفراج (بدون ID)
        release_statuses = [
            ('pending', 'في انتظار الإفراج', 'Pending Release', '#6c757d', 'fas fa-clock', 1),
            ('documents_review', 'مراجعة المستندات', 'Documents Review', '#17a2b8', 'fas fa-file-alt', 2),
            ('payment_verification', 'التحقق من المدفوعات', 'Payment Verification', '#ffc107', 'fas fa-credit-card', 3),
            ('quality_check', 'فحص الجودة', 'Quality Check', '#007bff', 'fas fa-search', 4),
            ('approved', 'معتمد للإفراج', 'Approved for Release', '#28a745', 'fas fa-check-circle', 5),
            ('released', 'تم الإفراج', 'Released', '#28a745', 'fas fa-unlock', 6),
            ('on_hold', 'محجوز مؤقت', 'On Hold', '#dc3545', 'fas fa-pause-circle', 7),
            ('rejected', 'مرفوض الإفراج', 'Release Rejected', '#dc3545', 'fas fa-times-circle', 8)
        ]
        
        # إدراج البيانات بدون ID
        insert_sql = """
            INSERT INTO shipment_release_status_config (
                status_code, status_name_ar, status_name_en, 
                status_color, status_icon, status_order, is_active
            ) VALUES (
                :1, :2, :3, :4, :5, :6, 1
            )
        """
        
        for status_data in release_statuses:
            oracle.execute_update(insert_sql, list(status_data))
        
        print(f'✅ تم إدراج {len(release_statuses)} حالة إفراج')
        
        # التحقق من البيانات
        check_query = """
            SELECT 
                status_code,
                status_name_ar,
                status_color,
                status_order
            FROM shipment_release_status_config
            WHERE is_active = 1
            ORDER BY status_order
        """
        
        statuses = oracle.execute_query(check_query)
        
        print('📋 حالات الإفراج المتاحة:')
        for status in statuses:
            code, name_ar, color, order = status
            print(f'   {order}. {code} → {name_ar} [{color}]')
        
        oracle.close()
        
        print(f'\n✅ تم إعداد {len(statuses)} حالة إفراج بنجاح!')
        return True
        
    except Exception as e:
        print(f'❌ خطأ في الإضافة: {e}')
        return False

def test_endpoint_again():
    """اختبار endpoint مرة أخرى"""
    print('\n🧪 اختبار endpoint مرة أخرى...')
    
    try:
        import requests
        
        response = requests.get('http://127.0.0.1:5000/shipments/get_available_release_statuses')
        
        if response.status_code == 200:
            data = response.json()
            
            if data['success']:
                print(f'✅ تم جلب {len(data["statuses"])} حالة إفراج من endpoint')
                
                print('📋 الحالات المتاحة من endpoint:')
                for i, status in enumerate(data['statuses'], 1):
                    print(f'   {i}. {status["code"]} → {status["name"]} [{status["color"]}]')
                
                return True
            else:
                print(f'❌ فشل endpoint: {data["message"]}')
                return False
        else:
            print(f'❌ خطأ HTTP: {response.status_code}')
            return False
            
    except Exception as e:
        print(f'❌ خطأ في اختبار endpoint: {e}')
        return False

if __name__ == '__main__':
    # إضافة البيانات
    if add_release_statuses():
        # اختبار endpoint
        if test_endpoint_again():
            print('\n🎉 تم إعداد نظام حالات الإفراج بنجاح!')
            print('💡 يمكنك الآن النقر على خلية حالة الإفراج لتعديلها')
        else:
            print('\n⚠️ البيانات تم إضافتها ولكن endpoint لا يعمل')
    else:
        print('\n⚠️ فشل في إضافة البيانات')
