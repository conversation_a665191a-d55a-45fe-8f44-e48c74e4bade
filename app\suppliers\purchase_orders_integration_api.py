# -*- coding: utf-8 -*-
"""
API التكامل بين أوامر الشراء والموردين والحوالات
Purchase Orders Integration API with Suppliers and Transfers
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from app.database_manager import DatabaseManager
import logging
import json
from datetime import datetime, date
from decimal import Decimal

# إنشاء Blueprint
purchase_orders_integration_bp = Blueprint('purchase_orders_integration', __name__, 
                                         url_prefix='/api/suppliers/purchase-orders')

logger = logging.getLogger(__name__)

def decimal_default(obj):
    """تحويل Decimal إلى float للـ JSON"""
    if isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, (date, datetime)):
        return obj.isoformat()
    raise TypeError

@purchase_orders_integration_bp.route('/outstanding/<int:supplier_id>')
@login_required
def get_outstanding_purchase_orders(supplier_id):
    """API للحصول على أوامر الشراء المستحقة للمورد"""
    try:
        db = DatabaseManager()
        
        # الحصول على أوامر الشراء المستحقة
        query = """
        SELECT 
            purchase_order_id, po_number, title, payment_due_date, payment_terms_days,
            days_overdue, days_until_due, currency, currency_symbol,
            total_amount_due, paid_amount, outstanding_amount, due_status,
            payment_priority, po_date, delivery_date, order_status, payment_status
        FROM V_PURCHASE_ORDERS_OUTSTANDING
        WHERE supplier_id = :1
        ORDER BY 
            CASE payment_priority 
                WHEN 'عاجل جداً' THEN 1
                WHEN 'عاجل' THEN 2  
                WHEN 'مهم' THEN 3
                ELSE 4 
            END,
            payment_due_date ASC
        """
        
        results = db.execute_query(query, [supplier_id])
        
        outstanding_orders = []
        total_outstanding = 0
        
        for row in results:
            order_data = {
                'purchase_order_id': row[0],
                'po_number': row[1],
                'title': row[2],
                'payment_due_date': row[3].strftime('%Y-%m-%d') if row[3] else None,
                'payment_terms_days': int(row[4]) if row[4] else 30,
                'days_overdue': int(row[5]) if row[5] else 0,
                'days_until_due': int(row[6]) if row[6] else 0,
                'currency': row[7],
                'currency_symbol': row[8],
                'total_amount_due': float(row[9]) if row[9] else 0,
                'paid_amount': float(row[10]) if row[10] else 0,
                'outstanding_amount': float(row[11]) if row[11] else 0,
                'due_status': row[12],
                'payment_priority': row[13],
                'po_date': row[14].strftime('%Y-%m-%d') if row[14] else None,
                'delivery_date': row[15].strftime('%Y-%m-%d') if row[15] else None,
                'order_status': row[16],
                'payment_status': row[17]
            }
            
            outstanding_orders.append(order_data)
            total_outstanding += order_data['outstanding_amount']
        
        # إحصائيات سريعة
        overdue_count = len([o for o in outstanding_orders if o['due_status'] == 'متأخر'])
        due_soon_count = len([o for o in outstanding_orders if o['due_status'] == 'مستحق قريباً'])
        
        return jsonify({
            'success': True,
            'outstanding_orders': outstanding_orders,
            'summary': {
                'total_orders': len(outstanding_orders),
                'total_outstanding_amount': total_outstanding,
                'overdue_orders': overdue_count,
                'due_soon_orders': due_soon_count,
                'currency': outstanding_orders[0]['currency'] if outstanding_orders else 'SAR'
            }
        })
        
    except Exception as e:
        logger.error(f"خطأ في جلب أوامر الشراء المستحقة: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@purchase_orders_integration_bp.route('/create-payment-request', methods=['POST'])
@login_required
def create_purchase_order_payment_request():
    """API لإنشاء طلب دفع مرتبط بأوامر الشراء"""
    try:
        db = DatabaseManager()
        data = request.get_json()
        
        # التحقق من البيانات المطلوبة
        required_fields = ['supplier_id', 'purchase_orders', 'money_changer_id', 'payment_purpose']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'success': False, 'message': f'الحقل {field} مطلوب'}), 400
        
        supplier_id = data['supplier_id']
        purchase_orders = data['purchase_orders']  # قائمة أوامر الشراء مع المبالغ
        money_changer_id = data['money_changer_id']
        payment_purpose = data['payment_purpose']
        notes = data.get('notes', '')
        discount_amount = float(data.get('discount_amount', 0))
        tax_amount = float(data.get('tax_amount', 0))
        
        # حساب إجمالي المبلغ
        total_amount = sum(float(po['payment_amount']) for po in purchase_orders)
        net_amount = total_amount - discount_amount - tax_amount
        
        if net_amount <= 0:
            return jsonify({'success': False, 'message': 'المبلغ الصافي يجب أن يكون أكبر من صفر'}), 400
        
        # الحصول على معلومات المورد
        supplier_query = """
        SELECT id, supplier_code, name_ar, name_en FROM SUPPLIERS WHERE id = :1
        """
        supplier_data = db.execute_query(supplier_query, [supplier_id])
        
        if not supplier_data:
            return jsonify({'success': False, 'message': 'المورد غير موجود'}), 404
        
        supplier = supplier_data[0]
        supplier_name = supplier[2]  # name_ar
        
        # الحصول على معلومات المستفيد للمورد
        beneficiary_query = """
        SELECT id FROM BENEFICIARIES 
        WHERE supplier_id = :1 AND is_active = 1
        ORDER BY is_default DESC, created_at DESC
        """
        beneficiary_data = db.execute_query(beneficiary_query, [supplier_id])
        
        if not beneficiary_data:
            return jsonify({'success': False, 'message': 'لا يوجد مستفيد مفعل للمورد'}), 400
        
        beneficiary_id = beneficiary_data[0][0]
        
        # تحديد العملة (من أول أمر شراء)
        currency_code = purchase_orders[0].get('currency', 'SAR')
        
        # إنشاء رقم طلب فريد
        request_number = f"PO{datetime.now().strftime('%Y%m%d%H%M%S')}{supplier_id:03d}"
        
        # إنشاء طلب حوالة
        transfer_request_query = """
        INSERT INTO TRANSFER_REQUESTS (
            request_number, beneficiary_id, amount, currency, purpose, notes,
            branch_id, status, created_by, updated_by, total_amount, 
            delivery_method, transfer_type, money_changer_bank_id,
            supplier_id, payment_type, discount_amount, tax_amount, 
            net_payment_amount, created_at, updated_at
        ) VALUES (
            :1, :2, :3, :4, :5, :6, 1, 'pending', :7, :8, :9, 
            'bank_transfer', 'supplier_payment', :10, :11, 'PURCHASE_ORDER_PAYMENT',
            :12, :13, :14, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        )
        """
        
        transfer_request_id = db.get_next_sequence_value('TRANSFER_REQUESTS_SEQ')
        db.execute_update(transfer_request_query, [
            request_number, beneficiary_id, total_amount, currency_code, payment_purpose, notes,
            current_user.id, current_user.id, total_amount, money_changer_id, supplier_id,
            discount_amount, tax_amount, net_amount
        ])
        
        # إنشاء سجل في جدول مدفوعات الموردين
        supplier_payment_query = """
        INSERT INTO SUPPLIER_PAYMENT_TRANSFERS (
            supplier_id, transfer_request_id, payment_amount, currency_code,
            payment_purpose, payment_status, payment_method,
            discount_applied, tax_withheld, net_amount_transferred,
            requested_date, created_at, created_by
        ) VALUES (
            :1, :2, :3, :4, :5, 'PENDING', 'BANK_TRANSFER',
            :6, :7, :8, SYSDATE, CURRENT_TIMESTAMP, :9
        )
        """
        
        supplier_payment_id = db.get_next_sequence_value('SUPPLIER_PAYMENT_TRANSFERS_SEQ')
        db.execute_update(supplier_payment_query, [
            supplier_id, transfer_request_id, total_amount, currency_code,
            payment_purpose, discount_amount, tax_amount, net_amount, current_user.id
        ])
        
        # إنشاء سجلات ربط مع أوامر الشراء
        for po_data in purchase_orders:
            po_id = po_data['purchase_order_id']
            po_amount = float(po_data['payment_amount'])
            po_type = po_data.get('payment_type', 'PARTIAL')
            
            # إنشاء سجل في PURCHASE_ORDER_PAYMENTS
            po_payment_query = """
            INSERT INTO PURCHASE_ORDER_PAYMENTS (
                purchase_order_id, supplier_payment_transfer_id, transfer_request_id,
                payment_type, payment_amount, currency_code, payment_description,
                payment_status, payment_due_date, requested_by, created_by
            ) VALUES (
                :1, :2, :3, :4, :5, :6, :7, 'PENDING', 
                (SELECT payment_due_date FROM PURCHASE_ORDERS WHERE id = :1),
                :8, :9
            )
            """
            
            db.execute_update(po_payment_query, [
                po_id, supplier_payment_id, transfer_request_id, po_type, po_amount, 
                currency_code, f'دفعة {po_type} لأمر الشراء', current_user.id, current_user.id
            ])
            
            # تحديث حالة الدفع في أمر الشراء
            update_po_query = """
            UPDATE PURCHASE_ORDERS SET
                payment_status = CASE 
                    WHEN :1 >= total_amount_due THEN 'PAID'
                    WHEN paid_amount + :1 >= total_amount_due THEN 'PAID'
                    WHEN paid_amount > 0 OR :1 > 0 THEN 'PARTIAL'
                    ELSE 'PENDING'
                END,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = :2
            """
            
            db.execute_update(update_po_query, [po_amount, po_id])
            
            # إنشاء معاملة في حساب المورد
            supplier_transaction_query = """
            INSERT INTO SUPPLIER_TRANSACTIONS (
                supplier_id, transaction_type, reference_type, reference_id, reference_number,
                transaction_date, currency_code, original_amount, credit_amount,
                description, status, purchase_order_id, purchase_order_number,
                payment_request_id, created_date, created_by
            ) VALUES (
                :1, 'PAYMENT_REQUEST', 'PURCHASE_ORDER', :2, 
                (SELECT po_number FROM PURCHASE_ORDERS WHERE id = :2),
                CURRENT_TIMESTAMP, :3, :4, :5, :6, 'PENDING', :7,
                (SELECT po_number FROM PURCHASE_ORDERS WHERE id = :7),
                :8, CURRENT_TIMESTAMP, :9
            )
            """
            
            db.execute_update(supplier_transaction_query, [
                supplier_id, po_id, currency_code, po_amount, po_amount,
                f'طلب دفع لأمر الشراء - {payment_purpose}', po_id, transfer_request_id, current_user.id
            ])
            
            # تسجيل تغيير الحالة
            status_log_query = """
            INSERT INTO PURCHASE_ORDER_STATUS_LOG (
                purchase_order_id, old_status, new_status, status_type,
                change_reason, changed_by, related_document_type, related_document_id
            ) VALUES (
                :1, 
                (SELECT payment_status FROM PURCHASE_ORDERS WHERE id = :1),
                'PAYMENT_REQUESTED', 'PAYMENT_STATUS',
                'تم إنشاء طلب دفع', :2, 'TRANSFER_REQUEST', :3
            )
            """
            
            db.execute_update(status_log_query, [po_id, current_user.id, transfer_request_id])
        
        return jsonify({
            'success': True,
            'message': 'تم إنشاء طلب الدفع بنجاح',
            'transfer_request_id': transfer_request_id,
            'request_number': request_number,
            'supplier_payment_id': supplier_payment_id,
            'total_amount': total_amount,
            'net_amount': net_amount,
            'purchase_orders_count': len(purchase_orders)
        })
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء طلب دفع أمر الشراء: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@purchase_orders_integration_bp.route('/payment-status/<int:purchase_order_id>')
@login_required
def get_purchase_order_payment_status(purchase_order_id):
    """API للحصول على حالة مدفوعات أمر الشراء"""
    try:
        db = DatabaseManager()
        
        # الحصول على معلومات أمر الشراء
        po_query = """
        SELECT 
            purchase_order_id, po_number, supplier_name, currency, currency_symbol,
            total_amount_due, paid_amount, outstanding_amount, payment_status,
            order_status, po_date, payment_due_date, payment_terms_days
        FROM V_PURCHASE_ORDERS_DETAILED
        WHERE purchase_order_id = :1
        """
        
        po_result = db.execute_query(po_query, [purchase_order_id])
        
        if not po_result:
            return jsonify({'success': False, 'message': 'أمر الشراء غير موجود'}), 404
        
        po_data = po_result[0]
        
        # الحصول على تفاصيل المدفوعات
        payments_query = """
        SELECT 
            pop.id, pop.payment_type, pop.payment_amount, pop.currency_code,
            pop.payment_status, pop.payment_description, pop.payment_due_date,
            pop.payment_requested_date, pop.payment_approved_date, 
            pop.payment_executed_date, pop.payment_completed_date,
            pop.transfer_request_id, pop.transfer_id,
            tr.request_number, tr.status as transfer_request_status,
            t.transfer_number, t.status as transfer_status
        FROM PURCHASE_ORDER_PAYMENTS pop
        LEFT JOIN TRANSFER_REQUESTS tr ON pop.transfer_request_id = tr.id
        LEFT JOIN TRANSFERS t ON pop.transfer_id = t.id
        WHERE pop.purchase_order_id = :1
        ORDER BY pop.payment_requested_date DESC
        """
        
        payments_result = db.execute_query(payments_query, [purchase_order_id])
        
        payments = []
        for payment in payments_result:
            payments.append({
                'payment_id': payment[0],
                'payment_type': payment[1],
                'payment_amount': float(payment[2]) if payment[2] else 0,
                'currency_code': payment[3],
                'payment_status': payment[4],
                'payment_description': payment[5],
                'payment_due_date': payment[6].strftime('%Y-%m-%d') if payment[6] else None,
                'payment_requested_date': payment[7].strftime('%Y-%m-%d %H:%M:%S') if payment[7] else None,
                'payment_approved_date': payment[8].strftime('%Y-%m-%d %H:%M:%S') if payment[8] else None,
                'payment_executed_date': payment[9].strftime('%Y-%m-%d %H:%M:%S') if payment[9] else None,
                'payment_completed_date': payment[10].strftime('%Y-%m-%d %H:%M:%S') if payment[10] else None,
                'transfer_request_id': payment[11],
                'transfer_id': payment[12],
                'transfer_request_number': payment[13],
                'transfer_request_status': payment[14],
                'transfer_number': payment[15],
                'transfer_status': payment[16]
            })
        
        # الحصول على سجل تغييرات الحالة
        status_log_query = """
        SELECT 
            status_type, old_status, new_status, change_reason,
            change_date, changed_by_name, related_document_type
        FROM V_PURCHASE_ORDER_STATUS_TRACKING
        WHERE purchase_order_id = :1
        ORDER BY change_date DESC
        FETCH FIRST 10 ROWS ONLY
        """
        
        status_log = db.execute_query(status_log_query, [purchase_order_id])
        
        status_history = []
        for log in status_log:
            status_history.append({
                'status_type': log[0],
                'old_status': log[1],
                'new_status': log[2],
                'change_reason': log[3],
                'change_date': log[4].strftime('%Y-%m-%d %H:%M:%S') if log[4] else None,
                'changed_by_name': log[5],
                'related_document_type': log[6]
            })
        
        return jsonify({
            'success': True,
            'purchase_order': {
                'id': po_data[0],
                'po_number': po_data[1],
                'supplier_name': po_data[2],
                'currency': po_data[3],
                'currency_symbol': po_data[4],
                'total_amount_due': float(po_data[5]) if po_data[5] else 0,
                'paid_amount': float(po_data[6]) if po_data[6] else 0,
                'outstanding_amount': float(po_data[7]) if po_data[7] else 0,
                'payment_status': po_data[8],
                'order_status': po_data[9],
                'po_date': po_data[10].strftime('%Y-%m-%d') if po_data[10] else None,
                'payment_due_date': po_data[11].strftime('%Y-%m-%d') if po_data[11] else None,
                'payment_terms_days': int(po_data[12]) if po_data[12] else 30
            },
            'payments': payments,
            'status_history': status_history,
            'summary': {
                'total_payments': len(payments),
                'completed_payments': len([p for p in payments if p['payment_status'] == 'COMPLETED']),
                'pending_payments': len([p for p in payments if p['payment_status'] == 'PENDING']),
                'total_requested': sum(p['payment_amount'] for p in payments),
                'total_completed': sum(p['payment_amount'] for p in payments if p['payment_status'] == 'COMPLETED')
            }
        })

    except Exception as e:
        logger.error(f"خطأ في جلب حالة مدفوعات أمر الشراء: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@purchase_orders_integration_bp.route('/supplier-statistics/<int:supplier_id>')
@login_required
def get_supplier_purchase_statistics(supplier_id):
    """API للحصول على إحصائيات أوامر الشراء للمورد"""
    try:
        db = DatabaseManager()

        # الحصول على إحصائيات المورد
        stats_query = """
        SELECT
            supplier_id, supplier_code, supplier_name, supplier_type, city,
            total_purchase_orders, completed_orders, pending_orders, cancelled_orders,
            total_order_value, total_paid_amount, total_outstanding_amount, average_order_value,
            fully_paid_orders, partially_paid_orders, overdue_orders,
            completion_rate, payment_rate, first_order_date, last_order_date, average_payment_days
        FROM V_SUPPLIER_PURCHASE_STATISTICS
        WHERE supplier_id = :1
        """

        stats_result = db.execute_query(stats_query, [supplier_id])

        if not stats_result:
            return jsonify({'success': False, 'message': 'لا توجد بيانات للمورد'}), 404

        stats = stats_result[0]

        # الحصول على أحدث أوامر الشراء
        recent_orders_query = """
        SELECT
            purchase_order_id, po_number, title, po_date, total_amount_due,
            outstanding_amount, payment_status, order_status
        FROM V_PURCHASE_ORDERS_DETAILED
        WHERE supplier_id = :1
        ORDER BY po_date DESC
        FETCH FIRST 5 ROWS ONLY
        """

        recent_orders = db.execute_query(recent_orders_query, [supplier_id])

        recent_orders_list = []
        for order in recent_orders:
            recent_orders_list.append({
                'purchase_order_id': order[0],
                'po_number': order[1],
                'title': order[2],
                'po_date': order[3].strftime('%Y-%m-%d') if order[3] else None,
                'total_amount_due': float(order[4]) if order[4] else 0,
                'outstanding_amount': float(order[5]) if order[5] else 0,
                'payment_status': order[6],
                'order_status': order[7]
            })

        # إحصائيات شهرية للسنة الحالية
        monthly_stats_query = """
        SELECT
            EXTRACT(MONTH FROM po.po_date) as month,
            COUNT(po.id) as orders_count,
            SUM(po.total_amount_due) as total_amount,
            SUM(po.paid_amount) as paid_amount
        FROM PURCHASE_ORDERS po
        WHERE po.supplier_id = :1
        AND EXTRACT(YEAR FROM po.po_date) = EXTRACT(YEAR FROM SYSDATE)
        GROUP BY EXTRACT(MONTH FROM po.po_date)
        ORDER BY month
        """

        monthly_stats = db.execute_query(monthly_stats_query, [supplier_id])

        monthly_data = []
        for month_stat in monthly_stats:
            monthly_data.append({
                'month': int(month_stat[0]),
                'orders_count': int(month_stat[1]),
                'total_amount': float(month_stat[2]) if month_stat[2] else 0,
                'paid_amount': float(month_stat[3]) if month_stat[3] else 0
            })

        return jsonify({
            'success': True,
            'supplier_statistics': {
                'supplier_id': stats[0],
                'supplier_code': stats[1],
                'supplier_name': stats[2],
                'supplier_type': stats[3],
                'city': stats[4],
                'orders_summary': {
                    'total_orders': int(stats[5]) if stats[5] else 0,
                    'completed_orders': int(stats[6]) if stats[6] else 0,
                    'pending_orders': int(stats[7]) if stats[7] else 0,
                    'cancelled_orders': int(stats[8]) if stats[8] else 0
                },
                'financial_summary': {
                    'total_order_value': float(stats[9]) if stats[9] else 0,
                    'total_paid_amount': float(stats[10]) if stats[10] else 0,
                    'total_outstanding_amount': float(stats[11]) if stats[11] else 0,
                    'average_order_value': float(stats[12]) if stats[12] else 0
                },
                'payment_summary': {
                    'fully_paid_orders': int(stats[13]) if stats[13] else 0,
                    'partially_paid_orders': int(stats[14]) if stats[14] else 0,
                    'overdue_orders': int(stats[15]) if stats[15] else 0
                },
                'performance_metrics': {
                    'completion_rate': float(stats[16]) if stats[16] else 0,
                    'payment_rate': float(stats[17]) if stats[17] else 0,
                    'average_payment_days': float(stats[20]) if stats[20] else 0
                },
                'date_range': {
                    'first_order_date': stats[18].strftime('%Y-%m-%d') if stats[18] else None,
                    'last_order_date': stats[19].strftime('%Y-%m-%d') if stats[19] else None
                }
            },
            'recent_orders': recent_orders_list,
            'monthly_statistics': monthly_data
        })

    except Exception as e:
        logger.error(f"خطأ في جلب إحصائيات أوامر الشراء للمورد: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@purchase_orders_integration_bp.route('/update-payment-status', methods=['POST'])
@login_required
def update_purchase_order_payment_status():
    """API لتحديث حالة مدفوعات أوامر الشراء"""
    try:
        db = DatabaseManager()
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        required_fields = ['payment_id', 'new_status']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'message': f'الحقل {field} مطلوب'}), 400

        payment_id = data['payment_id']
        new_status = data['new_status']
        notes = data.get('notes', '')
        transfer_id = data.get('transfer_id')

        # الحصول على معلومات الدفعة الحالية
        payment_query = """
        SELECT purchase_order_id, payment_status, payment_amount, currency_code
        FROM PURCHASE_ORDER_PAYMENTS
        WHERE id = :1
        """

        payment_result = db.execute_query(payment_query, [payment_id])

        if not payment_result:
            return jsonify({'success': False, 'message': 'الدفعة غير موجودة'}), 404

        purchase_order_id, old_status, payment_amount, currency_code = payment_result[0]

        # تحديث حالة الدفعة
        update_fields = ['payment_status = :1', 'updated_at = CURRENT_TIMESTAMP']
        params = [new_status]

        if transfer_id:
            update_fields.append('transfer_id = :' + str(len(params) + 1))
            params.append(transfer_id)

        if new_status == 'APPROVED':
            update_fields.append('payment_approved_date = CURRENT_TIMESTAMP')
        elif new_status == 'EXECUTED':
            update_fields.append('payment_executed_date = CURRENT_TIMESTAMP')
        elif new_status == 'COMPLETED':
            update_fields.append('payment_completed_date = CURRENT_TIMESTAMP')

        update_query = f"""
        UPDATE PURCHASE_ORDER_PAYMENTS SET
        {', '.join(update_fields)}
        WHERE id = :last_param
        """
        params.append(payment_id)

        db.execute_update(update_query, params)

        # تحديث حالة أمر الشراء إذا اكتملت جميع المدفوعات
        if new_status == 'COMPLETED':
            # حساب إجمالي المدفوعات المكتملة
            completed_payments_query = """
            SELECT SUM(payment_amount)
            FROM PURCHASE_ORDER_PAYMENTS
            WHERE purchase_order_id = :1 AND payment_status = 'COMPLETED'
            """

            completed_amount = db.execute_query(completed_payments_query, [purchase_order_id])[0][0] or 0

            # الحصول على إجمالي المبلغ المستحق
            po_amount_query = """
            SELECT total_amount_due FROM PURCHASE_ORDERS WHERE id = :1
            """

            total_due = db.execute_query(po_amount_query, [purchase_order_id])[0][0] or 0

            # تحديث حالة الدفع في أمر الشراء
            new_payment_status = 'PAID' if completed_amount >= total_due else 'PARTIAL'

            update_po_query = """
            UPDATE PURCHASE_ORDERS SET
                payment_status = :1,
                paid_amount = :2,
                outstanding_amount = :3 - :2,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = :4
            """

            db.execute_update(update_po_query, [new_payment_status, completed_amount, total_due, purchase_order_id])

        # تسجيل تغيير الحالة
        status_log_query = """
        INSERT INTO PURCHASE_ORDER_STATUS_LOG (
            purchase_order_id, old_status, new_status, status_type,
            change_reason, changed_by, related_document_type, related_document_id
        ) VALUES (
            :1, :2, :3, 'PAYMENT_STATUS', :4, :5, 'PAYMENT', :6
        )
        """

        change_reason = f'تحديث حالة الدفعة من {old_status} إلى {new_status}'
        if notes:
            change_reason += f' - {notes}'

        db.execute_update(status_log_query, [
            purchase_order_id, old_status, new_status, change_reason, current_user.id, payment_id
        ])

        return jsonify({
            'success': True,
            'message': 'تم تحديث حالة الدفعة بنجاح',
            'payment_id': payment_id,
            'old_status': old_status,
            'new_status': new_status
        })

    except Exception as e:
        logger.error(f"خطأ في تحديث حالة دفعة أمر الشراء: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@purchase_orders_integration_bp.route('/dashboard-stats')
@login_required
def get_purchase_orders_dashboard_stats():
    """API للحصول على إحصائيات لوحة المعلومات"""
    try:
        db = DatabaseManager()

        # إحصائيات عامة
        general_stats_query = """
        SELECT
            COUNT(*) as total_orders,
            COUNT(CASE WHEN payment_status = 'PAID' THEN 1 END) as paid_orders,
            COUNT(CASE WHEN payment_status = 'PARTIAL' THEN 1 END) as partial_orders,
            COUNT(CASE WHEN payment_status = 'OVERDUE' THEN 1 END) as overdue_orders,
            COUNT(CASE WHEN payment_status = 'PENDING' THEN 1 END) as pending_orders,
            SUM(total_amount_due) as total_value,
            SUM(paid_amount) as total_paid,
            SUM(outstanding_amount) as total_outstanding
        FROM PURCHASE_ORDERS
        WHERE status NOT IN ('CANCELLED', 'REJECTED')
        """

        general_stats = db.execute_query(general_stats_query)[0]

        # أوامر الشراء المستحقة قريباً
        due_soon_query = """
        SELECT COUNT(*)
        FROM PURCHASE_ORDERS
        WHERE payment_due_date BETWEEN SYSDATE AND SYSDATE + 7
        AND outstanding_amount > 0
        """

        due_soon_count = db.execute_query(due_soon_query)[0][0] or 0

        # إحصائيات حسب العملة
        currency_stats_query = """
        SELECT
            currency,
            COUNT(*) as orders_count,
            SUM(total_amount_due) as total_amount,
            SUM(outstanding_amount) as outstanding_amount
        FROM PURCHASE_ORDERS
        WHERE status NOT IN ('CANCELLED', 'REJECTED')
        GROUP BY currency
        ORDER BY total_amount DESC
        """

        currency_stats = db.execute_query(currency_stats_query)

        currency_breakdown = []
        for curr_stat in currency_stats:
            currency_breakdown.append({
                'currency': curr_stat[0],
                'orders_count': int(curr_stat[1]),
                'total_amount': float(curr_stat[2]) if curr_stat[2] else 0,
                'outstanding_amount': float(curr_stat[3]) if curr_stat[3] else 0
            })

        # أهم الموردين حسب قيمة أوامر الشراء
        top_suppliers_query = """
        SELECT
            s.id, s.name_ar,
            COUNT(po.id) as orders_count,
            SUM(po.total_amount_due) as total_value,
            SUM(po.outstanding_amount) as outstanding_amount
        FROM SUPPLIERS s
        JOIN PURCHASE_ORDERS po ON s.id = po.supplier_id
        WHERE po.status NOT IN ('CANCELLED', 'REJECTED')
        GROUP BY s.id, s.name_ar
        ORDER BY total_value DESC
        FETCH FIRST 10 ROWS ONLY
        """

        top_suppliers = db.execute_query(top_suppliers_query)

        top_suppliers_list = []
        for supplier in top_suppliers:
            top_suppliers_list.append({
                'supplier_id': supplier[0],
                'supplier_name': supplier[1],
                'orders_count': int(supplier[2]),
                'total_value': float(supplier[3]) if supplier[3] else 0,
                'outstanding_amount': float(supplier[4]) if supplier[4] else 0
            })

        return jsonify({
            'success': True,
            'dashboard_stats': {
                'general_statistics': {
                    'total_orders': int(general_stats[0]) if general_stats[0] else 0,
                    'paid_orders': int(general_stats[1]) if general_stats[1] else 0,
                    'partial_orders': int(general_stats[2]) if general_stats[2] else 0,
                    'overdue_orders': int(general_stats[3]) if general_stats[3] else 0,
                    'pending_orders': int(general_stats[4]) if general_stats[4] else 0,
                    'due_soon_orders': due_soon_count,
                    'total_value': float(general_stats[5]) if general_stats[5] else 0,
                    'total_paid': float(general_stats[6]) if general_stats[6] else 0,
                    'total_outstanding': float(general_stats[7]) if general_stats[7] else 0
                },
                'currency_breakdown': currency_breakdown,
                'top_suppliers': top_suppliers_list,
                'payment_completion_rate': round((float(general_stats[6]) / float(general_stats[5])) * 100, 2) if general_stats[5] and general_stats[5] > 0 else 0
            }
        })

    except Exception as e:
        logger.error(f"خطأ في جلب إحصائيات لوحة المعلومات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500
