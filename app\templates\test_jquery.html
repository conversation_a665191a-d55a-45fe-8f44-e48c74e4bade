{% extends "base.html" %}

{% block title %}اختبار jQuery{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>اختبار jQuery</h1>
    <div id="test-result" class="alert alert-info">
        جاري اختبار jQuery...
    </div>
    <button id="test-btn" class="btn btn-primary">اختبار AJAX</button>
    <div id="ajax-result" class="mt-3"></div>
</div>
{% endblock %}

{% block extra_js %}
<script>
console.log('JavaScript loaded');

$(document).ready(function() {
    console.log('jQuery loaded and ready');
    $('#test-result').html('✅ jQuery يعمل بنجاح!').removeClass('alert-info').addClass('alert-success');
    
    $('#test-btn').click(function() {
        console.log('Button clicked');
        $('#ajax-result').html('<div class="alert alert-info">جاري اختبار AJAX...</div>');
        
        $.ajax({
            url: '/purchase_settings/api/supplier_groups',
            method: 'GET',
            xhrFields: {
                withCredentials: true
            },
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                console.log('AJAX Success:', response);
                if (response.success) {
                    $('#ajax-result').html(`
                        <div class="alert alert-success">
                            ✅ AJAX يعمل بنجاح!<br>
                            تم العثور على ${response.total} مجموعة موردين
                        </div>
                    `);
                } else {
                    $('#ajax-result').html(`
                        <div class="alert alert-danger">
                            ❌ خطأ: ${response.message}
                        </div>
                    `);
                }
            },
            error: function(xhr, status, error) {
                console.log('AJAX Error:', xhr, status, error);
                $('#ajax-result').html(`
                    <div class="alert alert-danger">
                        ❌ خطأ في AJAX: ${error}<br>
                        Status: ${xhr.status}
                    </div>
                `);
            }
        });
    });
});
</script>
{% endblock %}
