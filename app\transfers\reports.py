#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
التقارير والتحليلات
Reports and Analytics
"""

from flask import render_template, request, jsonify
from flask_login import login_required, current_user
from . import transfers_bp
from database_manager import DatabaseManager
import logging

logger = logging.getLogger(__name__)

@transfers_bp.route('/reports')
@login_required
def reports():
    """صفحة التقارير والتحليلات"""
    return render_template('transfers/reports.html')
