-- =====================================================
-- تحديث النظام للتكامل الكامل مع أوامر الشراء
-- System Update for Complete Purchase Orders Integration
-- =====================================================

-- 1. تحديث جدول SUPPLIER_TRANSACTIONS لدعم أوامر الشراء بشكل كامل
ALTER TABLE SUPPLIER_TRANSACTIONS MODIFY (
    reference_type VARCHAR2(50) -- توسيع الحقل لدعم أنواع مراجع أكثر
);

-- إضافة قيم جديدة لأنواع المراجع
COMMENT ON COLUMN SUPPLIER_TRANSACTIONS.reference_type IS 'نوع المرجع: INVOICE, PURCHASE_ORDER, PAYMENT, CREDIT_NOTE, DEBIT_NOTE, ADJUSTMENT';

-- 2. إنشاء جدول ربط الموردين بالعقود (إذا لم يكن موجود)
CREATE TABLE IF NOT EXISTS SUPPLIER_CONTRACTS (
    id NUMBER PRIMARY KEY,
    supplier_id NUMBER NOT NULL,
    contract_id NUMBER NOT NULL,
    contract_role VARCHAR2(30) DEFAULT 'PRIMARY', -- PRIMARY, SECONDARY, SUBCONTRACTOR
    start_date DATE NOT NULL,
    end_date DATE,
    contract_value NUMBER(15,2),
    currency_code VARCHAR2(3),
    status VARCHAR2(20) DEFAULT 'ACTIVE', -- ACTIVE, SUSPENDED, TERMINATED, COMPLETED
    
    -- معلومات إضافية
    performance_rating NUMBER(3,2), -- تقييم الأداء من 1 إلى 5
    compliance_status VARCHAR2(20) DEFAULT 'COMPLIANT', -- COMPLIANT, NON_COMPLIANT, UNDER_REVIEW
    
    -- تواريخ النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_sc_supplier FOREIGN KEY (supplier_id) REFERENCES SUPPLIERS(id),
    CONSTRAINT fk_sc_contract FOREIGN KEY (contract_id) REFERENCES CONTRACTS(contract_id),
    CONSTRAINT fk_sc_currency FOREIGN KEY (currency_code) REFERENCES CURRENCIES(code),
    CONSTRAINT fk_sc_created_by FOREIGN KEY (created_by) REFERENCES USERS(id),
    CONSTRAINT fk_sc_updated_by FOREIGN KEY (updated_by) REFERENCES USERS(id),
    
    -- قيد فريد
    CONSTRAINT uk_supplier_contract UNIQUE (supplier_id, contract_id)
);

-- 3. إنشاء جدول تقييم أداء الموردين
CREATE TABLE SUPPLIER_PERFORMANCE_EVALUATIONS (
    id NUMBER PRIMARY KEY,
    supplier_id NUMBER NOT NULL,
    evaluation_period_from DATE NOT NULL,
    evaluation_period_to DATE NOT NULL,
    
    -- معايير التقييم
    quality_rating NUMBER(3,2), -- تقييم الجودة (1-5)
    delivery_rating NUMBER(3,2), -- تقييم التسليم (1-5)
    price_rating NUMBER(3,2), -- تقييم السعر (1-5)
    service_rating NUMBER(3,2), -- تقييم الخدمة (1-5)
    compliance_rating NUMBER(3,2), -- تقييم الالتزام (1-5)
    
    -- التقييم الإجمالي
    overall_rating NUMBER(3,2), -- التقييم الإجمالي (1-5)
    performance_category VARCHAR2(20), -- EXCELLENT, GOOD, AVERAGE, POOR, CRITICAL
    
    -- إحصائيات الأداء
    total_orders NUMBER DEFAULT 0,
    on_time_deliveries NUMBER DEFAULT 0,
    quality_issues NUMBER DEFAULT 0,
    payment_delays NUMBER DEFAULT 0,
    
    -- معدلات الأداء
    on_time_delivery_rate NUMBER(5,2), -- نسبة التسليم في الوقت المحدد
    quality_acceptance_rate NUMBER(5,2), -- نسبة قبول الجودة
    payment_compliance_rate NUMBER(5,2), -- نسبة الالتزام بالدفع
    
    -- ملاحظات التقييم
    evaluation_notes CLOB,
    improvement_recommendations CLOB,
    
    -- معلومات التقييم
    evaluated_by NUMBER,
    evaluation_date DATE DEFAULT SYSDATE,
    approved_by NUMBER,
    approval_date DATE,
    
    -- حالة التقييم
    evaluation_status VARCHAR2(20) DEFAULT 'DRAFT', -- DRAFT, SUBMITTED, APPROVED, REJECTED
    
    -- تواريخ النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_spe_supplier FOREIGN KEY (supplier_id) REFERENCES SUPPLIERS(id),
    CONSTRAINT fk_spe_evaluated_by FOREIGN KEY (evaluated_by) REFERENCES USERS(id),
    CONSTRAINT fk_spe_approved_by FOREIGN KEY (approved_by) REFERENCES USERS(id),
    CONSTRAINT fk_spe_created_by FOREIGN KEY (created_by) REFERENCES USERS(id)
);

-- 4. إنشاء جدول إشعارات التكامل
CREATE TABLE INTEGRATION_NOTIFICATIONS (
    id NUMBER PRIMARY KEY,
    notification_type VARCHAR2(50) NOT NULL, -- PAYMENT_DUE, DELIVERY_DELAY, QUALITY_ISSUE, etc.
    entity_type VARCHAR2(30) NOT NULL, -- PURCHASE_ORDER, SUPPLIER, TRANSFER, etc.
    entity_id NUMBER NOT NULL,
    
    -- محتوى الإشعار
    title VARCHAR2(200) NOT NULL,
    message CLOB NOT NULL,
    priority VARCHAR2(20) DEFAULT 'MEDIUM', -- LOW, MEDIUM, HIGH, CRITICAL
    
    -- معلومات المستلم
    recipient_user_id NUMBER,
    recipient_role VARCHAR2(50), -- PROCUREMENT_MANAGER, FINANCE_MANAGER, etc.
    
    -- حالة الإشعار
    notification_status VARCHAR2(20) DEFAULT 'PENDING', -- PENDING, SENT, READ, DISMISSED
    sent_date TIMESTAMP,
    read_date TIMESTAMP,
    
    -- معلومات إضافية
    action_required CHAR(1) DEFAULT 'N',
    action_url VARCHAR2(500),
    expiry_date DATE,
    
    -- تواريخ النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_in_recipient FOREIGN KEY (recipient_user_id) REFERENCES USERS(id),
    CONSTRAINT fk_in_created_by FOREIGN KEY (created_by) REFERENCES USERS(id)
);

-- 5. إنشاء Sequences للجداول الجديدة
CREATE SEQUENCE SUPPLIER_CONTRACTS_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SUPPLIER_PERFORMANCE_EVALUATIONS_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE INTEGRATION_NOTIFICATIONS_SEQ START WITH 1 INCREMENT BY 1;

-- 6. إنشاء Triggers للجداول الجديدة
CREATE OR REPLACE TRIGGER supplier_contracts_trigger
    BEFORE INSERT ON SUPPLIER_CONTRACTS
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := SUPPLIER_CONTRACTS_SEQ.NEXTVAL;
    END IF;
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;

CREATE OR REPLACE TRIGGER supplier_performance_evaluations_trigger
    BEFORE INSERT ON SUPPLIER_PERFORMANCE_EVALUATIONS
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := SUPPLIER_PERFORMANCE_EVALUATIONS_SEQ.NEXTVAL;
    END IF;
    
    -- حساب التقييم الإجمالي
    :NEW.overall_rating := (
        NVL(:NEW.quality_rating, 0) + 
        NVL(:NEW.delivery_rating, 0) + 
        NVL(:NEW.price_rating, 0) + 
        NVL(:NEW.service_rating, 0) + 
        NVL(:NEW.compliance_rating, 0)
    ) / 5;
    
    -- تحديد فئة الأداء
    IF :NEW.overall_rating >= 4.5 THEN
        :NEW.performance_category := 'EXCELLENT';
    ELSIF :NEW.overall_rating >= 3.5 THEN
        :NEW.performance_category := 'GOOD';
    ELSIF :NEW.overall_rating >= 2.5 THEN
        :NEW.performance_category := 'AVERAGE';
    ELSIF :NEW.overall_rating >= 1.5 THEN
        :NEW.performance_category := 'POOR';
    ELSE
        :NEW.performance_category := 'CRITICAL';
    END IF;
    
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;

CREATE OR REPLACE TRIGGER integration_notifications_trigger
    BEFORE INSERT ON INTEGRATION_NOTIFICATIONS
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := INTEGRATION_NOTIFICATIONS_SEQ.NEXTVAL;
    END IF;
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;

-- 7. إنشاء فهارس للأداء
CREATE INDEX idx_sc_supplier ON SUPPLIER_CONTRACTS(supplier_id);
CREATE INDEX idx_sc_contract ON SUPPLIER_CONTRACTS(contract_id);
CREATE INDEX idx_sc_status ON SUPPLIER_CONTRACTS(status);
CREATE INDEX idx_sc_dates ON SUPPLIER_CONTRACTS(start_date, end_date);

CREATE INDEX idx_spe_supplier ON SUPPLIER_PERFORMANCE_EVALUATIONS(supplier_id);
CREATE INDEX idx_spe_period ON SUPPLIER_PERFORMANCE_EVALUATIONS(evaluation_period_from, evaluation_period_to);
CREATE INDEX idx_spe_rating ON SUPPLIER_PERFORMANCE_EVALUATIONS(overall_rating);
CREATE INDEX idx_spe_category ON SUPPLIER_PERFORMANCE_EVALUATIONS(performance_category);

CREATE INDEX idx_in_type ON INTEGRATION_NOTIFICATIONS(notification_type);
CREATE INDEX idx_in_entity ON INTEGRATION_NOTIFICATIONS(entity_type, entity_id);
CREATE INDEX idx_in_recipient ON INTEGRATION_NOTIFICATIONS(recipient_user_id);
CREATE INDEX idx_in_status ON INTEGRATION_NOTIFICATIONS(notification_status);
CREATE INDEX idx_in_priority ON INTEGRATION_NOTIFICATIONS(priority);

-- 8. إنشاء View شامل للتكامل
CREATE OR REPLACE VIEW V_COMPLETE_INTEGRATION_DASHBOARD AS
SELECT 
    -- معلومات أمر الشراء
    po.id as purchase_order_id,
    po.po_number,
    po.title as po_title,
    po.po_date,
    po.delivery_date,
    po.status as order_status,
    po.payment_status,
    po.delivery_status,
    
    -- معلومات المورد
    s.id as supplier_id,
    s.supplier_code,
    s.name_ar as supplier_name,
    s.supplier_type,
    s.city as supplier_city,
    
    -- معلومات مالية
    po.currency,
    po.total_amount_due,
    po.paid_amount,
    po.outstanding_amount,
    
    -- معلومات المدفوعات
    COUNT(pop.id) as payments_count,
    SUM(CASE WHEN pop.payment_status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_payments,
    MAX(pop.payment_completed_date) as last_payment_date,
    
    -- معلومات الحوالات
    COUNT(DISTINCT tr.id) as transfer_requests_count,
    COUNT(DISTINCT t.id) as transfers_count,
    
    -- معلومات الاستلام
    COUNT(gr.id) as receipts_count,
    MAX(gr.receipt_date) as last_receipt_date,
    
    -- حالة التكامل
    CASE 
        WHEN po.payment_status = 'PAID' AND po.delivery_status = 'RECEIVED' THEN 'مكتمل'
        WHEN po.payment_status = 'PAID' THEN 'مدفوع - في انتظار الاستلام'
        WHEN po.delivery_status = 'RECEIVED' THEN 'مستلم - في انتظار الدفع'
        WHEN COUNT(pop.id) > 0 THEN 'قيد المعالجة'
        ELSE 'معلق'
    END as integration_status,
    
    -- تقييم الأداء
    spe.overall_rating as supplier_performance_rating,
    spe.performance_category,
    
    -- تواريخ مهمة
    po.created_at as po_created_date,
    po.updated_at as po_updated_date

FROM PURCHASE_ORDERS po
JOIN SUPPLIERS s ON po.supplier_id = s.id
LEFT JOIN PURCHASE_ORDER_PAYMENTS pop ON po.id = pop.purchase_order_id
LEFT JOIN TRANSFER_REQUESTS tr ON pop.transfer_request_id = tr.id
LEFT JOIN TRANSFERS t ON pop.transfer_id = t.id
LEFT JOIN GOODS_RECEIPTS gr ON po.id = gr.purchase_order_id
LEFT JOIN (
    SELECT supplier_id, overall_rating, performance_category,
           ROW_NUMBER() OVER (PARTITION BY supplier_id ORDER BY evaluation_date DESC) as rn
    FROM SUPPLIER_PERFORMANCE_EVALUATIONS
    WHERE evaluation_status = 'APPROVED'
) spe ON s.id = spe.supplier_id AND spe.rn = 1

GROUP BY po.id, po.po_number, po.title, po.po_date, po.delivery_date, po.status,
         po.payment_status, po.delivery_status, s.id, s.supplier_code, s.name_ar,
         s.supplier_type, s.city, po.currency, po.total_amount_due, po.paid_amount,
         po.outstanding_amount, spe.overall_rating, spe.performance_category,
         po.created_at, po.updated_at;

-- 9. إنشاء Trigger لتحديث أرصدة الموردين تلقائياً
CREATE OR REPLACE TRIGGER auto_update_supplier_balances
    AFTER INSERT OR UPDATE OR DELETE ON PURCHASE_ORDER_PAYMENTS
    FOR EACH ROW
DECLARE
    v_supplier_id NUMBER;
    v_currency_code VARCHAR2(3);
BEGIN
    -- الحصول على معرف المورد والعملة
    IF INSERTING OR UPDATING THEN
        SELECT po.supplier_id, po.currency
        INTO v_supplier_id, v_currency_code
        FROM PURCHASE_ORDERS po
        WHERE po.id = :NEW.purchase_order_id;
    ELSIF DELETING THEN
        SELECT po.supplier_id, po.currency
        INTO v_supplier_id, v_currency_code
        FROM PURCHASE_ORDERS po
        WHERE po.id = :OLD.purchase_order_id;
    END IF;
    
    -- تحديث أرصدة المورد
    UPDATE_SUPPLIER_BALANCES_FROM_PO(v_supplier_id, v_currency_code);
    
EXCEPTION
    WHEN OTHERS THEN
        -- تسجيل الخطأ دون إيقاف العملية
        NULL;
END;

-- 10. إنشاء تعليقات للجداول والـ Views الجديدة
COMMENT ON TABLE SUPPLIER_CONTRACTS IS 'ربط الموردين بالعقود';
COMMENT ON TABLE SUPPLIER_PERFORMANCE_EVALUATIONS IS 'تقييمات أداء الموردين';
COMMENT ON TABLE INTEGRATION_NOTIFICATIONS IS 'إشعارات نظام التكامل';
COMMENT ON VIEW V_COMPLETE_INTEGRATION_DASHBOARD IS 'لوحة معلومات التكامل الشاملة';

-- تم إكمال تحديث النظام للتكامل الكامل
-- System integration update completed successfully
