{% extends "base.html" %}

{% block title %}تعديل العقد{% endblock %}

{% block extra_css %}
<!-- Handsontable CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/handsontable@12.4.0/dist/handsontable.full.min.css">
<style>
/* تخصيص Handsontable للعربية */
.handsontable {
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.handsontable .ht_master .wtHolder {
    direction: rtl;
}

.handsontable .htCore {
    direction: rtl;
}

.handsontable .htCore td {
    text-align: right;
    border: 1px solid #ddd;
}

.handsontable .htCore th {
    background-color: #f8f9fa;
    font-weight: bold;
    text-align: center;
    border: 1px solid #ddd;
}

.handsontable .currentRow {
    background-color: #e3f2fd;
}

.handsontable .currentCol {
    background-color: #f3e5f5;
}

.handsontable .area {
    background-color: #bbdefb;
}

/* تحسين مظهر الجدول */
.contract-details-table {
    margin: 20px 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table-toolbar {
    background: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-toolbar .btn {
    margin-left: 5px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-edit text-warning"></i>
                        تعديل العقد رقم {{ contract.contract_id }}
                    </h4>
                    <div>
                        <a href="{{ url_for('contracts.view_contract', contract_id=contract.contract_id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> إلغاء
                        </a>
                        <button type="button" class="btn btn-success" onclick="saveContract()">
                            <i class="fas fa-save"></i> حفظ التعديلات
                        </button>
                    </div>
                </div>
                
                <div class="card-body">
                    <form id="editContractForm" method="POST" action="{{ url_for('contracts.update_contract_with_table', contract_id=contract.contract_id) }}">
                        <!-- معلومات العقد الأساسية -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">رقم العقد:</label>
                                    <input type="text" class="form-control" value="{{ contract.contract_id }}" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">رقم الفرع:</label>
                                    <input type="text" class="form-control" value="{{ contract.branch_id }}" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">تاريخ البداية:</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date"
                                           value="{{ contract.start_date }}" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">تاريخ النهاية:</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date"
                                           value="{{ contract.end_date }}" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">مبلغ العقد:</label>
                                    <input type="number" class="form-control" id="contract_amount" name="contract_amount"
                                           value="{{ contract.contract_amount }}" step="0.01" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">حالة الاستخدام:</label>
                                    <div class="form-check mt-2">
                                        <input class="form-check-input" type="checkbox" id="is_used" name="is_used"
                                               {% if contract.is_used == 1 %}checked{% endif %} disabled>
                                        <label class="form-check-label" for="is_used">
                                            <i class="fas fa-info-circle text-info me-1"></i>
                                            مُستخدم في أوامر الشراء
                                        </label>
                                    </div>
                                    <small class="text-muted">يتم التأشير تلقائياً عند استخدام العقد</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">حالة العقد:</label>
                                    <select class="form-select" id="contract_status" name="contract_status" required>
                                        {% set current_status = contract[14] if contract|length > 14 else 'DRAFT' %}
                                        <option value="DRAFT" {% if current_status == 'DRAFT' %}selected{% endif %}>
                                            <i class="fas fa-edit"></i> مسودة
                                        </option>
                                        <option value="APPROVED" {% if current_status == 'APPROVED' %}selected{% endif %}>
                                            <i class="fas fa-check"></i> معتمد
                                        </option>
                                        <option value="PARTIALLY_EXECUTED" {% if current_status == 'PARTIALLY_EXECUTED' %}selected{% endif %} disabled>
                                            <i class="fas fa-clock"></i> منفذ جزئياً (تلقائي)
                                        </option>
                                        <option value="FULLY_EXECUTED" {% if current_status == 'FULLY_EXECUTED' %}selected{% endif %} disabled>
                                            <i class="fas fa-check-double"></i> منفذ كلياً (تلقائي)
                                        </option>
                                    </select>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle text-info me-1"></i>
                                        يمكن تعديل "مسودة" و "معتمد" فقط. الحالات الأخرى تلقائية.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">كود المورد:</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="supplier_id" name="supplier_id"
                                               value="{{ contract.supplier_id }}" required readonly>
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-outline-secondary"
                                                    onclick="openSupplierSearch()" title="بحث عن مورد (F9)">
                                                <i class="fas fa-search"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">اسم المورد:</label>
                                    <input type="text" class="form-control" id="supplier_name" name="supplier_name"
                                           value="{{ contract.supplier_name }}" required readonly>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">العملة:</label>
                                    <select class="form-control" id="currency_code" name="currency_code" required>
                                        <option value="SAR" {% if contract.currency_code == 'SAR' %}selected{% endif %}>ريال سعودي</option>
                                        <option value="USD" {% if contract.currency_code == 'USD' %}selected{% endif %}>دولار أمريكي</option>
                                        <option value="EUR" {% if contract.currency_code == 'EUR' %}selected{% endif %}>يورو</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">سعر الصرف:</label>
                                    <input type="number" class="form-control" id="exchange_rate" name="exchange_rate"
                                           value="{{ contract.exchange_rate }}" step="0.0001" min="0.0001" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">الرقم المرجعي:</label>
                                    <input type="text" class="form-control" id="reference_number" name="reference_number"
                                           value="{{ contract.reference_number }}">
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">الوصف:</label>
                                    <textarea class="form-control" id="description" name="description"
                                              rows="3">{{ contract.description }}</textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <!-- تفاصيل العقد -->
                    <div class="row">
                        <div class="col-12">
                            <h5 class="mb-3">
                                <i class="fas fa-list text-info"></i>
                                تفاصيل الأصناف
                            </h5>

                            <!-- شريط أدوات الجدول -->
                            <div class="contract-details-table">
                                <div class="table-toolbar">
                                    <div>
                                        <button type="button" class="btn btn-sm btn-success" onclick="addNewRow()">
                                            <i class="fas fa-plus"></i> إضافة صنف
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="removeSelectedRows()">
                                            <i class="fas fa-trash"></i> حذف المحدد
                                        </button>
                                        <button type="button" class="btn btn-sm btn-info" onclick="duplicateSelectedRows()">
                                            <i class="fas fa-copy"></i> نسخ الصف
                                        </button>
                                    </div>
                                    <div>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToExcel()">
                                            <i class="fas fa-file-excel"></i> تصدير Excel
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="importFromExcel()">
                                            <i class="fas fa-upload"></i> استيراد Excel
                                        </button>
                                    </div>
                                </div>

                                <!-- جدول Handsontable -->
                                <div id="contractDetailsTable" style="height: 400px; overflow: hidden;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة البحث عن الموردين - نسخة من صفحة الإضافة -->
<div class="modal fade supplier-search-modal" id="supplierSearchModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-search"></i> البحث عن الموردين
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-8">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" id="supplierSearchTerm"
                                   placeholder="ابحث بالاسم أو الكود أو الهاتف...">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-primary w-100" onclick="searchSuppliers()">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <small class="text-muted">
                            <i class="fas fa-lightbulb"></i>
                            <strong>نصيحة:</strong> اتركه فارغاً لعرض جميع الموردين، أو ابحث بالاسم/الكود/الهاتف
                        </small>
                    </div>
                </div>

                <div id="supplierSearchResults">
                    <div class="text-center py-4" id="initialMessage">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-3 text-muted">جاري تحميل بيانات الموردين...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة البحث عن الأصناف من IAS_ITM_MST -->
<div class="modal fade item-search-modal" id="itemSearchModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-search"></i> البحث عن الأصناف
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-8">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" id="itemSearchTerm"
                                   placeholder="ابحث برقم الصنف (I_CODE) أو اسم الصنف (I_NAME)...">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-primary w-100" onclick="searchItems()">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <div id="itemSearchResults">
                            <!-- نتائج البحث ستظهر هنا -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function saveContract() {
    const form = document.getElementById('editContractForm');
    const formData = new FormData(form);

    // جمع بيانات التفاصيل
    const contractItems = [];
    const rows = document.querySelectorAll('#itemsTableBody tr');

    rows.forEach(row => {
        const detailId = row.getAttribute('data-detail-id');
        const itemCode = row.querySelector('input[name="item_code"]').value;
        const itemName = row.querySelector('input[name="item_name"]').value;
        const quantity = parseFloat(row.querySelector('input[name="quantity"]').value) || 0;
        const freeQuantity = parseFloat(row.querySelector('input[name="free_quantity"]').value) || 0;
        const unitName = row.querySelector('input[name="unit_name"]').value;
        const unitPrice = parseFloat(row.querySelector('input[name="unit_price"]').value) || 0;
        const discountPercentage = parseFloat(row.querySelector('input[name="discount_percentage"]').value) || 0;
        const taxAmount = parseFloat(row.querySelector('input[name="tax_amount"]').value) || 0;
        const lineTotal = parseFloat(row.querySelector('input[name="line_total"]').value) || 0;

        if (itemName && quantity > 0) {
            contractItems.push({
                detail_id: detailId,
                item_code: itemCode,
                item_name: itemName,
                quantity: quantity,
                free_quantity: freeQuantity,
                unit_name: unitName,
                unit_price: unitPrice,
                discount_percentage: discountPercentage,
                tax_amount: taxAmount,
                line_total: lineTotal
            });
        }
    });

    const contractData = {
        start_date: formData.get('start_date'),
        end_date: formData.get('end_date'),
        supplier_id: formData.get('supplier_id'),
        supplier_name: formData.get('supplier_name'),
        currency_code: formData.get('currency_code'),
        exchange_rate: formData.get('exchange_rate'),
        reference_number: formData.get('reference_number'),
        contract_amount: formData.get('contract_amount'),
        description: formData.get('description'),
        contract_items: contractItems
    };
    
    // التحقق من صحة البيانات
    if (!contractData.start_date || !contractData.end_date) {
        showAlert('يرجى تحديد تواريخ البداية والنهاية', 'error');
        return;
    }
    
    if (new Date(contractData.start_date) >= new Date(contractData.end_date)) {
        showAlert('تاريخ النهاية يجب أن يكون بعد تاريخ البداية', 'error');
        return;
    }
    
    if (!contractData.supplier_id || !contractData.supplier_name) {
        showAlert('يرجى تحديد بيانات المورد', 'error');
        return;
    }
    
    if (!contractData.contract_amount || parseFloat(contractData.contract_amount) <= 0) {
        showAlert('يرجى إدخال مبلغ العقد', 'error');
        return;
    }
    
    // إرسال البيانات
    $.ajax({
        url: `/contracts/api/contracts/{{ contract.contract_id }}`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(contractData),
        success: function(response) {
            if (response.success) {
                showAlert('تم تحديث العقد بنجاح', 'success');
                setTimeout(() => {
                    window.location.href = `/contracts/{{ contract.contract_id }}`;
                }, 1500);
            } else {
                showAlert(response.message || 'فشل في تحديث العقد', 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في تحديث العقد:', error);
            showAlert('خطأ في الاتصال بالخادم', 'error');
        }
    });
}

function showAlert(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;
    
    // إزالة التنبيهات السابقة
    $('.alert').remove();
    
    // إضافة التنبيه الجديد
    $('.card-body').prepend(alertHtml);
    
    // التمرير إلى أعلى الصفحة
    $('html, body').animate({ scrollTop: 0 }, 300);
}

// إضافة صنف جديد
function addNewItem() {
    const tbody = document.getElementById('itemsTableBody');
    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td>
            <div class="input-group">
                <input type="text" class="form-control form-control-sm" name="item_code" readonly>
                <div class="input-group-append">
                    <button type="button" class="btn btn-sm btn-outline-secondary"
                            onclick="openItemSearch(this)" title="بحث عن صنف (F9)">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm" name="item_name" required readonly>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm" name="quantity"
                   step="0.001" min="0" value="1" required onchange="calculateRowTotal(this)">
        </td>
        <td>
            <input type="number" class="form-control form-control-sm" name="free_quantity"
                   step="0.001" min="0" value="0" onchange="calculateRowTotal(this)">
        </td>
        <td>
            <input type="text" class="form-control form-control-sm" name="unit_name" value="قطعة">
        </td>
        <td>
            <input type="number" class="form-control form-control-sm" name="unit_price"
                   step="0.01" min="0" value="0" required onchange="calculateRowTotal(this)">
        </td>
        <td>
            <input type="number" class="form-control form-control-sm" name="discount_percentage"
                   step="0.01" min="0" max="100" value="0" onchange="calculateRowTotal(this)">
        </td>
        <td>
            <input type="number" class="form-control form-control-sm" name="tax_amount"
                   step="0.01" min="0" value="0" onchange="calculateRowTotal(this)">
        </td>
        <td>
            <input type="number" class="form-control form-control-sm" name="line_total"
                   step="0.01" value="0" readonly>
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-danger" onclick="removeItem(this)" title="حذف">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;

    tbody.appendChild(newRow);

    // إخفاء رسالة "لا توجد أصناف" إذا كانت موجودة
    const noItemsAlert = document.getElementById('noItemsAlert');
    if (noItemsAlert) {
        noItemsAlert.style.display = 'none';
    }

    // التركيز على حقل اسم الصنف
    newRow.querySelector('input[name="item_name"]').focus();
}

// حذف صنف
function removeItem(button) {
    if (confirm('هل أنت متأكد من حذف هذا الصنف؟')) {
        const row = button.closest('tr');
        row.remove();

        // إظهار رسالة "لا توجد أصناف" إذا لم تعد هناك أصناف
        const tbody = document.getElementById('itemsTableBody');
        if (tbody.children.length === 0) {
            const noItemsAlert = document.getElementById('noItemsAlert');
            if (noItemsAlert) {
                noItemsAlert.style.display = 'block';
            }
        }

        updateContractTotal();
    }
}

// حساب إجمالي السطر
function calculateRowTotal(input) {
    const row = input.closest('tr');
    const quantity = parseFloat(row.querySelector('input[name="quantity"]').value) || 0;
    const unitPrice = parseFloat(row.querySelector('input[name="unit_price"]').value) || 0;
    const discountPercentage = parseFloat(row.querySelector('input[name="discount_percentage"]').value) || 0;
    const taxAmount = parseFloat(row.querySelector('input[name="tax_amount"]').value) || 0;

    let lineTotal = quantity * unitPrice;

    // تطبيق الخصم
    if (discountPercentage > 0) {
        lineTotal = lineTotal * (1 - discountPercentage / 100);
    }

    // إضافة الضريبة
    lineTotal += taxAmount;

    // تحديث حقل الإجمالي
    row.querySelector('input[name="line_total"]').value = lineTotal.toFixed(2);

    // تحديث إجمالي العقد
    updateContractTotal();
}

// تحديث إجمالي العقد
function updateContractTotal() {
    let total = 0;
    const rows = document.querySelectorAll('#itemsTableBody tr');

    rows.forEach(row => {
        const lineTotal = parseFloat(row.querySelector('input[name="line_total"]').value) || 0;
        total += lineTotal;
    });

    // تحديث حقل مبلغ العقد
    document.getElementById('contract_amount').value = total.toFixed(2);
}
</script>

<style>
.form-label {
    font-weight: bold;
    color: #495057;
    margin-bottom: 5px;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.card-header {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
}

.card-header h4 {
    color: white;
}

.table th {
    background-color: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
    border: 1px solid #dee2e6;
}
</style>

<script>
// متغيرات للبحث
let selectedBranch = { branch_id: {{ contract.branch_id }} }; // الفرع الحقيقي للعقد

// البحث عن الموردين - نسخة من صفحة الإضافة
function searchSuppliers() {
    console.log('بدء البحث عن الموردين...');

    const searchTerm = $('#supplierSearchTerm').val().trim();
    console.log('مصطلح البحث:', searchTerm);

    const searchMessage = searchTerm ?
        `جاري البحث عن "${searchTerm}"...` :
        'جاري تحميل الموردين...';

    $('#supplierSearchResults').html(`
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري البحث...</span>
            </div>
            <p class="mt-2">${searchMessage}</p>
        </div>
    `);

    $.ajax({
        url: '/contracts/api/suppliers/search',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            branch_id: selectedBranch.branch_id,
            search_term: searchTerm
        }),
        success: function(response) {
            console.log('📋 استجابة البحث:', response);

            if (response.success) {
                displaySuppliers(
                    response.suppliers,
                    response.branch_info,
                    response.warning,
                    response.message,
                    response.source
                );
            } else {
                $('#supplierSearchResults').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i>
                        فشل في تحميل بيانات الموردين. يرجى المحاولة مرة أخرى.
                    </div>
                `);
            }
        },
        error: function(xhr, status, error) {
            $('#supplierSearchResults').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> خطأ في البحث: ${error}
                </div>
            `);
        }
    });
}

// عرض نتائج البحث - نسخة من صفحة الإضافة
function displaySuppliers(suppliers, branchInfo, warning, message, source) {
    let html = '';

    // عرض رسالة النجاح أو التحذير
    if (warning) {
        html += `<div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i> ${warning}
        </div>`;
    } else if (message) {
        html += `<div class="alert alert-success">
            <i class="fas fa-check-circle"></i> ${message}
        </div>`;
    }

    if (suppliers.length === 0) {
        const searchTerm = $('#supplierSearchTerm').val().trim();
        const emptyMessage = searchTerm ?
            `لم يتم العثور على موردين يحتوون على "${searchTerm}"` :
            'لا توجد موردين في قاعدة البيانات';

        html += `
            <div class="text-center py-5">
                <i class="fas fa-search fa-4x text-muted mb-4"></i>
                <h4 class="text-muted">لا توجد نتائج</h4>
                <p class="text-muted">${emptyMessage}</p>
                ${searchTerm ? `
                    <button class="btn btn-outline-primary" onclick="$('#supplierSearchTerm').val(''); searchSuppliers();">
                        <i class="fas fa-list"></i> عرض جميع الموردين
                    </button>
                ` : ''}
            </div>
        `;
    } else {
        html += `
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">
                    <i class="fas fa-list"></i> نتائج البحث
                    <span class="badge bg-primary">${suppliers.length}</span>
                </h6>
                <small class="text-muted">انقر على أي مورد لاختياره</small>
            </div>
            <div class="supplier-table">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th><i class="fas fa-barcode"></i> كود المورد</th>
                            <th><i class="fas fa-building"></i> اسم المورد</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        suppliers.forEach(function(supplier) {
            // تنظيف البيانات لتجنب مشاكل JavaScript
            const safeName = supplier.supplier_name.replace(/'/g, "\\'");

            // الحل النهائي: البيانات معكوسة تماماً!
            // supplier_id = الكود الصحيح
            // supplier_code = الاسم الصحيح
            const correctCode = supplier.supplier_id;
            const correctName = supplier.supplier_code.replace(/'/g, "\\'");

            html += `
                <tr class="supplier-row" onclick="selectSupplier('${correctCode}', '${correctName}', '${supplier.supplier_code}')"
                    style="cursor: pointer;" title="انقر لاختيار هذا المورد">
                    <td>
                        <span class="badge bg-primary fs-6">${correctCode}</span>
                    </td>
                    <td>
                        <strong class="text-success">${supplier.supplier_code}</strong>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;
    }

    $('#supplierSearchResults').html(html);
}

// اختيار المورد - نسخة من صفحة الإضافة
function selectSupplier(supplierId, supplierName, supplierCode) {
    console.log('✅ تم اختيار المورد:', {
        id: supplierId,
        name: supplierName,
        code: supplierCode
    });

    // إصلاح: حفظ البيانات بالشكل المطلوب لقاعدة البيانات
    // supplier_id يجب أن يحتوي على الاسم (للحفظ)
    // supplier_name يجب أن يحتوي على الكود (للحفظ)
    $('#supplier_id').val(supplierName);    // اسم المورد في supplier_id
    $('#supplier_name').val(supplierId);    // كود المورد في supplier_name

    console.log('✅ تم تعيين القيم للحفظ:', {
        supplier_id: $('#supplier_id').val(),
        supplier_name: $('#supplier_name').val()
    });
    $('#supplierSearchModal').modal('hide');

    showAlert(`تم اختيار المورد: ${supplierName} (${supplierId})`, 'success');
}

// البحث عن الأصناف - مبسط للتعديل
let currentItemRow = null;

function openItemSearch(button) {
    currentItemRow = button.closest('tr');
    $('#itemSearchModal').modal('show');
    setTimeout(() => {
        $('#itemSearchTerm').focus();
        // تحميل الأصناف فور فتح النافذة
        searchItems();
    }, 500);
}

function searchItems() {
    if (!selectedBranch) {
        alert('يرجى اختيار الفرع أولاً');
        return;
    }

    const searchTerm = $('#itemSearchTerm').val().trim();

    $('#itemSearchResults').html(`
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري البحث...</span>
            </div>
            <p class="mt-2">جاري البحث في جدول IAS_ITM_MST...</p>
        </div>
    `);

    $.ajax({
        url: '/contracts/api/items/search',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            branch_id: selectedBranch.branch_id,
            search_term: searchTerm
        }),
        success: function(response) {
            if (response.success) {
                displayItems(response.items);
            } else {
                $('#itemSearchResults').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i>
                        فشل في تحميل بيانات الأصناف: ${response.message}
                    </div>
                `);
            }
        },
        error: function(xhr, status, error) {
            $('#itemSearchResults').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> خطأ في البحث: ${error}
                </div>
            `);
        }
    });
}

// عرض نتائج البحث عن الأصناف - نسخة من صفحة الإضافة
function displayItems(items) {
    let html = '';

    if (items.length === 0) {
        html = `
            <div class="text-center py-5">
                <i class="fas fa-search fa-4x text-muted mb-4"></i>
                <h4 class="text-muted">لا توجد نتائج</h4>
                <p class="text-muted">لم يتم العثور على أصناف</p>
            </div>
        `;
    } else {
        html = `
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">
                    <i class="fas fa-list"></i> نتائج البحث
                    <span class="badge bg-primary">${items.length}</span>
                </h6>
                <small class="text-muted">انقر على أي صنف لاختياره</small>
            </div>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>رقم الصنف (I_CODE)</th>
                            <th>اسم الصنف (I_NAME)</th>
                            <th>الوحدة (ITM_UNT)</th>
                            <th>يخضع للضريبة</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        items.forEach(function(item) {
            const safeName = item.item_name.replace(/'/g, "\\'");
            const taxStatus = item.is_taxable ? 'نعم' : 'لا';
            const taxClass = item.is_taxable ? 'text-success' : 'text-muted';

            html += `
                <tr style="cursor: pointer;" onclick="selectItem('${item.item_id}', '${safeName}', '${item.unit}', ${item.is_taxable})"
                    title="انقر لاختيار هذا الصنف">
                    <td><span class="badge bg-primary">${item.item_id}</span></td>
                    <td><strong>${item.item_name}</strong></td>
                    <td><span class="badge bg-secondary">${item.unit}</span></td>
                    <td><span class="${taxClass}">${taxStatus}</span></td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;
    }

    $('#itemSearchResults').html(html);
}

// اختيار الصنف - محدث للعمل مع Handsontable
function selectItem(itemId, itemName, unit, isTaxable) {
    // التحقق من وجود Handsontable
    if (typeof window.currentEditingRow !== 'undefined' && contractDetailsTable) {
        // تحديث بيانات الصف في Handsontable
        contractDetailsTable.setDataAtCell(window.currentEditingRow, 0, itemId);
        contractDetailsTable.setDataAtCell(window.currentEditingRow, 1, itemName);
        contractDetailsTable.setDataAtCell(window.currentEditingRow, 6, unit || 'قطعة');

        // تعيين سعر افتراضي إذا لم يكن موجوداً
        const currentPrice = contractDetailsTable.getDataAtCell(window.currentEditingRow, 7);
        if (!currentPrice || currentPrice == 0) {
            contractDetailsTable.setDataAtCell(window.currentEditingRow, 7, 1);
        }

        $('#itemSearchModal').modal('hide');
        showAlert(`تم اختيار الصنف: ${itemName}`, 'success');

        // التركيز على خانة الكمية
        contractDetailsTable.selectCell(window.currentEditingRow, 2);

        console.log('✅ تم اختيار الصنف في Handsontable:', {
            row: window.currentEditingRow,
            itemId: itemId,
            itemName: itemName,
            unit: unit
        });
    }
    // الكود القديم للجدول العادي (للتوافق مع الصفحات الأخرى)
    else if (currentItemRow) {
        currentItemRow.querySelector('input[name="item_code"]').value = itemId;
        currentItemRow.querySelector('input[name="item_name"]').value = itemName;
        currentItemRow.querySelector('input[name="unit_name"]').value = unit || 'قطعة';

        // تعيين سعر افتراضي
        const priceInput = currentItemRow.querySelector('input[name="unit_price"]');
        if (priceInput.value == 0 || priceInput.value == '') {
            priceInput.value = 1;
        }

        // حساب الإجمالي
        calculateRowTotal(currentItemRow.querySelector('input[name="quantity"]'));

        $('#itemSearchModal').modal('hide');
        showAlert(`تم اختيار الصنف: ${itemName}`, 'success');

        console.log('✅ تم اختيار الصنف:', {
            itemId: itemId,
            itemName: itemName,
            unit: unit,
            isTaxable: isTaxable
        });
    }
}

// فتح نافذة البحث عن الموردين
function openSupplierSearch() {
    $('#supplierSearchModal').modal('show');
    setTimeout(() => {
        $('#supplierSearchTerm').focus();
        // تحميل الموردين تلقائياً
        searchSuppliers();
    }, 500);
}

// التعامل مع مفتاح F9
$(document).keydown(function(e) {
    if (e.key === 'F9') {
        e.preventDefault();
        openSupplierSearch();
    }
});

// البحث عند الضغط على Enter
$('#supplierSearchTerm').keypress(function(e) {
    if (e.which === 13) {
        searchSuppliers();
    }
});

$('#itemSearchTerm').keypress(function(e) {
    if (e.which === 13) {
        searchItems();
    }
});
</script>

{% endblock %}

{% block extra_js %}
<!-- Handsontable JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/handsontable@12.4.0/dist/handsontable.full.min.js"></script>

<script>
// متغيرات عامة
let contractDetailsTable;
let contractDetailsData = [];

// تهيئة الجدول عند تحميل الصفحة
$(document).ready(function() {
    initializeContractDetailsTable();
    loadExistingData();
});

// تهيئة جدول Handsontable
function initializeContractDetailsTable() {
    const container = document.getElementById('contractDetailsTable');

    contractDetailsTable = new Handsontable(container, {
        data: contractDetailsData,
        colHeaders: [
            'كود الصنف',
            'اسم الصنف',
            'الكمية الأصلية',
            'الكمية المنفذة',
            'الكمية المتبقية',
            'الكمية المجانية',
            'الوحدة',
            'سعر الوحدة',
            'نسبة الخصم %',
            'مبلغ الضريبة',
            'الإجمالي'
        ],
        columns: [
            {
                data: 'item_code',
                type: 'text',
                width: 120,
                renderer: function(instance, td, row, col, prop, value, cellProperties) {
                    // إضافة زر البحث
                    td.innerHTML = `
                        <div class="input-group input-group-sm">
                            <input type="text" class="form-control" value="${value || ''}" readonly>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="openItemSearchForRow(${row})" title="بحث عن صنف">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    `;
                    return td;
                }
            },
            {
                data: 'item_name',
                type: 'text',
                width: 200,
                readOnly: true
            },
            {
                data: 'quantity',
                type: 'numeric',
                numericFormat: {
                    pattern: '0,0.000'
                },
                width: 100,
                validator: function(value, callback) {
                    callback(value >= 0);
                }
            },
            {
                data: 'executed_quantity',
                type: 'numeric',
                numericFormat: {
                    pattern: '0,0.000'
                },
                width: 100,
                readOnly: true,
                className: 'text-info'
            },
            {
                data: 'remaining_quantity',
                type: 'numeric',
                numericFormat: {
                    pattern: '0,0.000'
                },
                width: 100,
                readOnly: true,
                className: 'text-warning'
            },
            {
                data: 'free_quantity',
                type: 'numeric',
                numericFormat: {
                    pattern: '0,0.000'
                },
                width: 100,
                validator: function(value, callback) {
                    callback(value >= 0);
                }
            },
            {
                data: 'unit_name',
                type: 'text',
                width: 80
            },
            {
                data: 'unit_price',
                type: 'numeric',
                numericFormat: {
                    pattern: '0,0.00'
                },
                width: 100,
                validator: function(value, callback) {
                    callback(value >= 0);
                }
            },
            {
                data: 'discount_percentage',
                type: 'numeric',
                numericFormat: {
                    pattern: '0.00'
                },
                width: 100,
                validator: function(value, callback) {
                    callback(value >= 0 && value <= 100);
                }
            },
            {
                data: 'tax_amount',
                type: 'numeric',
                numericFormat: {
                    pattern: '0,0.00'
                },
                width: 100,
                validator: function(value, callback) {
                    callback(value >= 0);
                }
            },
            {
                data: 'line_total',
                type: 'numeric',
                numericFormat: {
                    pattern: '0,0.00'
                },
                width: 120,
                readOnly: true,
                className: 'text-success font-weight-bold'
            }
        ],
        stretchH: 'all',
        autoWrapRow: true,
        autoWrapCol: true,
        rowHeaders: true,
        manualRowResize: true,
        manualColumnResize: true,
        contextMenu: {
            items: {
                'row_above': {
                    name: 'إدراج صف أعلى'
                },
                'row_below': {
                    name: 'إدراج صف أسفل'
                },
                'remove_row': {
                    name: 'حذف الصف'
                },
                'separator': Handsontable.plugins.ContextMenu.SEPARATOR,
                'copy': {
                    name: 'نسخ'
                },
                'cut': {
                    name: 'قص'
                },
                'paste': {
                    name: 'لصق'
                }
            }
        },
        afterChange: function(changes, source) {
            if (source !== 'loadData') {
                // إعادة حساب الإجماليات عند تغيير البيانات
                calculateRowTotals(changes);
            }
        },
        beforeRemoveRow: function(index, amount) {
            // تأكيد حذف الصفوف
            return confirm('هل أنت متأكد من حذف الصف المحدد؟');
        },
        licenseKey: 'non-commercial-and-evaluation'
    });
}

// تحميل البيانات الموجودة
function loadExistingData() {
    contractDetailsData = [];

    {% if details %}
    {% for detail in details %}
    contractDetailsData.push({
        detail_id: {{ detail[0] }},
        item_code: '{{ detail[1] or "" }}',
        item_name: '{{ detail[2] or "" }}',
        quantity: {{ detail[3] or 0 }},
        executed_quantity: {{ detail[4] or 0 }},
        remaining_quantity: {{ detail[5] or detail[3] or 0 }},
        free_quantity: {{ detail[6] or 0 }},
        unit_name: '{{ detail[7] or "" }}',
        unit_price: {{ detail[8] or 0 }},
        discount_percentage: {{ detail[9] or 0 }},
        tax_amount: {{ detail[10] or 0 }},
        line_total: {{ detail[11] or 0 }}
    });
    {% endfor %}
    {% endif %}

    // إضافة صفوف فارغة إذا لم توجد بيانات
    if (contractDetailsData.length === 0) {
        for (let i = 0; i < 5; i++) {
            contractDetailsData.push(createEmptyRow());
        }
    }

    contractDetailsTable.loadData(contractDetailsData);
}

// إنشاء صف فارغ
function createEmptyRow() {
    return {
        detail_id: null,
        item_code: '',
        item_name: '',
        quantity: 0,
        executed_quantity: 0,
        remaining_quantity: 0,
        free_quantity: 0,
        unit_name: 'قطعة',
        unit_price: 0,
        discount_percentage: 0,
        tax_amount: 0,
        line_total: 0
    };
}

// إضافة صف جديد
function addNewRow() {
    const newRow = createEmptyRow();
    contractDetailsData.push(newRow);
    contractDetailsTable.loadData(contractDetailsData);

    // التركيز على الصف الجديد
    const lastRowIndex = contractDetailsData.length - 1;
    contractDetailsTable.selectCell(lastRowIndex, 0);
}

// حذف الصفوف المحددة
function removeSelectedRows() {
    const selected = contractDetailsTable.getSelected();
    if (selected && selected.length > 0) {
        const [startRow, , endRow] = selected[0];
        const rowsToRemove = endRow - startRow + 1;

        if (confirm(`هل أنت متأكد من حذف ${rowsToRemove} صف؟`)) {
            contractDetailsTable.alter('remove_row', startRow, rowsToRemove);
        }
    } else {
        alert('يرجى تحديد الصفوف المراد حذفها أولاً');
    }
}

// نسخ الصفوف المحددة
function duplicateSelectedRows() {
    const selected = contractDetailsTable.getSelected();
    if (selected && selected.length > 0) {
        const [startRow, , endRow] = selected[0];

        for (let i = startRow; i <= endRow; i++) {
            const rowData = contractDetailsTable.getDataAtRow(i);
            const newRow = {...rowData};
            newRow.detail_id = null; // صف جديد
            contractDetailsData.push(newRow);
        }

        contractDetailsTable.loadData(contractDetailsData);
    } else {
        alert('يرجى تحديد الصفوف المراد نسخها أولاً');
    }
}

// حساب إجماليات الصفوف
function calculateRowTotals(changes) {
    if (!changes) return;

    changes.forEach(function([row, prop, oldValue, newValue]) {
        if (['quantity', 'unit_price', 'discount_percentage', 'tax_amount'].includes(prop)) {
            const rowData = contractDetailsTable.getDataAtRow(row);
            const quantity = parseFloat(rowData[2]) || 0;
            const unitPrice = parseFloat(rowData[7]) || 0;
            const discountPercentage = parseFloat(rowData[8]) || 0;
            const taxAmount = parseFloat(rowData[9]) || 0;

            // حساب الإجمالي
            const subtotal = quantity * unitPrice;
            const discountAmount = subtotal * (discountPercentage / 100);
            const afterDiscount = subtotal - discountAmount;
            const total = afterDiscount + taxAmount;

            // تحديث الإجمالي
            contractDetailsTable.setDataAtCell(row, 10, total);

            // تحديث الكمية المتبقية
            const executedQuantity = parseFloat(rowData[3]) || 0;
            const remainingQuantity = quantity - executedQuantity;
            contractDetailsTable.setDataAtCell(row, 4, remainingQuantity);
        }
    });
}

// فتح نافذة البحث عن الأصناف لصف معين
function openItemSearchForRow(rowIndex) {
    // حفظ فهرس الصف الحالي
    window.currentEditingRow = rowIndex;

    // فتح نافذة البحث عن الأصناف
    $('#itemSearchModal').modal('show');
}

// تصدير إلى Excel
function exportToExcel() {
    const exportPlugin = contractDetailsTable.getPlugin('exportFile');
    exportPlugin.downloadFile('xlsx', {
        filename: 'تفاصيل_العقد_' + new Date().toISOString().slice(0, 10),
        columnHeaders: true
    });
}

// استيراد من Excel
function importFromExcel() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.xlsx,.xls,.csv';

    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            // هنا يمكن إضافة منطق قراءة ملف Excel
            alert('سيتم تطوير ميزة الاستيراد قريباً');
        }
    };

    input.click();
}

// دالة لحفظ بيانات الجدول عند إرسال النموذج
function getTableDataForSubmission() {
    const data = contractDetailsTable.getData();
    const validRows = data.filter(row => {
        return row[0] && row[1] && row[2] > 0; // كود الصنف واسم الصنف والكمية مطلوبة
    });

    return validRows.map(row => ({
        detail_id: row.detail_id || null,
        item_code: row[0] || '',
        item_name: row[1] || '',
        quantity: parseFloat(row[2]) || 0,
        executed_quantity: parseFloat(row[3]) || 0,
        remaining_quantity: parseFloat(row[4]) || 0,
        free_quantity: parseFloat(row[5]) || 0,
        unit_name: row[6] || 'قطعة',
        unit_price: parseFloat(row[7]) || 0,
        discount_percentage: parseFloat(row[8]) || 0,
        tax_amount: parseFloat(row[9]) || 0,
        line_total: parseFloat(row[10]) || 0
    }));
}

// تحديث دالة حفظ العقد لتشمل بيانات الجدول الجديد
function saveContract() {
    const tableData = getTableDataForSubmission();

    if (tableData.length === 0) {
        alert('يرجى إضافة أصناف للعقد أولاً');
        return;
    }

    // إضافة بيانات الجدول إلى النموذج
    const form = document.getElementById('editContractForm');

    // إزالة الحقول القديمة إن وجدت
    const oldInputs = form.querySelectorAll('input[name^="table_data"]');
    oldInputs.forEach(input => input.remove());

    // إضافة بيانات الجدول كحقول مخفية
    const tableDataInput = document.createElement('input');
    tableDataInput.type = 'hidden';
    tableDataInput.name = 'table_data';
    tableDataInput.value = JSON.stringify(tableData);
    form.appendChild(tableDataInput);

    // إرسال النموذج
    form.submit();
}
</script>
{% endblock %}
