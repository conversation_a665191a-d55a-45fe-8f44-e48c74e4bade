<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}NetSuite Oracle - نظام ERP المتطور{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- NetSuite REAL CSS -->
    <link href="{{ url_for('static', filename='css/netsuite-real.css') }}" rel="stylesheet">
</head>

<body>
    <!-- NetSuite REAL Header -->
    <header class="ns-header-real">
        <div class="ns-logo">
            <i class="fas fa-building"></i>
            NetSuite Oracle - نظام ERP المتطور
        </div>
        
        {% if current_user.is_authenticated %}
        <div class="ns-user-menu">
            <div class="dropdown">
                <button class="dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user-circle"></i>
                    {{ current_user.full_name }}
                    <i class="fas fa-chevron-down"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                        <i class="fas fa-user me-2"></i>الملف الشخصي
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                        <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                    </a></li>
                </ul>
            </div>
        </div>
        {% endif %}
    </header>

    {% if current_user.is_authenticated %}
    <!-- NetSuite REAL Sidebar -->
    <nav class="ns-sidebar-real">
        <!-- Dashboard -->
        <div class="ns-nav-section">
            <a href="{{ url_for('main.dashboard') }}" class="ns-nav-item active">
                <i class="fas fa-tachometer-alt"></i>
                لوحة المعلومات
            </a>
        </div>
        
        <!-- المشتريات -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">المشتريات</div>
            <a class="ns-nav-item" href="{{ url_for('purchase_orders.index') }}">
                <i class="fas fa-shopping-cart"></i>
                أوامر الشراء
            </a>
            <a class="ns-nav-item" href="{{ url_for('purchase_requests.index') }}">
                <i class="fas fa-file-alt"></i>
                طلبات الشراء
            </a>
            <a class="ns-nav-item" href="{{ url_for('contracts.index') }}">
                <i class="fas fa-file-contract"></i>
                بيانات العقود
            </a>
        </div>
        
        <!-- الموردين -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">الموردين</div>
            <a class="ns-nav-item" href="{{ url_for('suppliers.index') }}">
                <i class="fas fa-building"></i>
                إدارة الموردين
            </a>
        </div>
        
        <!-- المخزون -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">المخزون</div>
            <a class="ns-nav-item" href="{{ url_for('inventory.index') }}">
                <i class="fas fa-boxes"></i>
                إدارة المخزون
            </a>
            <a class="ns-nav-item" href="{{ url_for('inventory.movements') }}">
                <i class="fas fa-exchange-alt"></i>
                حركات المخزون
            </a>
            <a class="ns-nav-item" href="{{ url_for('inventory.reports') }}">
                <i class="fas fa-chart-line"></i>
                تقارير المخزون
            </a>
        </div>
        
        <!-- التقارير -->
        <div class="ns-nav-section">
            <div class="ns-nav-header">التقارير</div>
            <a class="ns-nav-item" href="{{ url_for('reports.index') }}">
                <i class="fas fa-chart-bar"></i>
                جميع التقارير
            </a>
            <a class="ns-nav-item" href="{{ url_for('reports.financial') }}">
                <i class="fas fa-money-bill-wave"></i>
                التقارير المالية
            </a>
        </div>
    </nav>
    {% endif %}

    <!-- NetSuite REAL Main Content -->
    <main class="ns-main-real">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="ns-alert-real ns-alert-{{ 'danger' if category == 'error' else category }}-real">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <!-- Page Content -->
        {% block content %}{% endblock %}
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- NetSuite REAL JS -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set active navigation item
        const currentPath = window.location.pathname;
        const navItems = document.querySelectorAll('.ns-nav-item');
        
        navItems.forEach(item => {
            item.classList.remove('active');
            if (item.getAttribute('href') === currentPath) {
                item.classList.add('active');
            }
        });
        
        // Mobile sidebar toggle
        const sidebarToggle = document.querySelector('.ns-sidebar-toggle');
        const sidebar = document.querySelector('.ns-sidebar-real');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('show');
            });
        }
        
        console.log('NetSuite REAL Design loaded');
    });
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
