{% extends "base.html" %}

{% block title %}تحليل أصناف المشتريات{% endblock %}

{% block head %}
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header المتقدم -->
    <div class="row mb-4">
        <div class="col-12">
            <!-- Header الرئيسي -->
            <div class="card border-0 shadow-sm mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body text-white">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center mb-2">
                                <div class="me-3">
                                    <i class="fas fa-chart-line fa-3x opacity-75"></i>
                                </div>
                                <div>
                                    <h1 class="h2 mb-1 fw-bold">تحليل أصناف أوامر الشراء</h1>
                                    <p class="mb-0 opacity-90">تحليل شامل ومتقدم لجميع الأصناف الفعلية في أوامر الشراء مع إحصائيات تفصيلية ورسوم بيانية</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="d-flex flex-column align-items-end">
                                <div class="mb-2">
                                    <span class="badge bg-light text-dark px-3 py-2">
                                        <i class="fas fa-database me-1"></i>
                                        بيانات فعلية
                                    </span>
                                </div>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-light btn-sm" onclick="exportToExcel()">
                                        <i class="fas fa-file-excel me-1"></i>
                                        Excel
                                    </button>
                                    <button class="btn btn-outline-light btn-sm" onclick="exportToPDF()">
                                        <i class="fas fa-file-pdf me-1"></i>
                                        PDF
                                    </button>
                                    <button class="btn btn-outline-light btn-sm" onclick="refreshData()">
                                        <i class="fas fa-sync-alt me-1"></i>
                                        تحديث
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- شريط التنقل السريع -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body py-2">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item">
                                <a href="{{ url_for('main.dashboard') }}" class="text-decoration-none">
                                    <i class="fas fa-home me-1"></i>
                                    الرئيسية
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ url_for('purchase_orders.index') }}" class="text-decoration-none">
                                    <i class="fas fa-shopping-cart me-1"></i>
                                    أوامر الشراء
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">
                                <i class="fas fa-chart-line me-1"></i>
                                تحليل الأصناف
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات السريعة -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-primary bg-opacity-10 p-3 me-3">
                            <i class="fas fa-boxes text-primary fs-4"></i>
                        </div>
                        <div>
                            <h3 class="mb-0 text-primary">{{ "{:,}".format(quick_stats.total_orders) }}</h3>
                            <small class="text-muted">إجمالي أوامر الشراء</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-success bg-opacity-10 p-3 me-3">
                            <i class="fas fa-shopping-cart text-success fs-4"></i>
                        </div>
                        <div>
                            <h3 class="mb-0 text-success">{{ "{:,}".format(quick_stats.total_items) }}</h3>
                            <small class="text-muted">إجمالي الأصناف</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-info bg-opacity-10 p-3 me-3">
                            <i class="fas fa-dollar-sign text-info fs-4"></i>
                        </div>
                        <div>
                            {% if quick_stats.currency_breakdown and quick_stats.currency_breakdown|length > 1 %}
                                <!-- عملات متعددة -->
                                <div class="dropdown">
                                    <button class="btn btn-link text-info p-0" type="button" data-bs-toggle="dropdown">
                                        <h3 class="mb-0 text-info">عملات متعددة</h3>
                                    </button>
                                    <ul class="dropdown-menu">
                                        {% for currency in quick_stats.currency_breakdown %}
                                        <li class="dropdown-item">
                                            <span id="currency-{{ currency.currency }}">¥{{ "{:,.0f}".format(currency.total_value) }}</span>
                                            <small class="text-muted d-block">{{ currency.orders_count }} أمر</small>
                                        </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            {% else %}
                                <!-- عملة واحدة -->
                                <h3 class="mb-0 text-info">
                                    <span id="main-currency">¥{{ "{:,.0f}".format(quick_stats.total_value) }}</span>
                                </h3>
                            {% endif %}
                            <small class="text-muted">إجمالي القيمة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-warning bg-opacity-10 p-3 me-3">
                            <i class="fas fa-chart-bar text-warning fs-4"></i>
                        </div>
                        <div>
                            <h3 class="mb-0 text-warning">¥{{ "{:,.0f}".format(quick_stats.avg_order_value) }}</h3>
                            <small class="text-muted">متوسط قيمة الأمر</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الفلاتر -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    فلاتر البحث والتصفية
                </h5>
                <button class="btn btn-outline-secondary btn-sm" type="button" id="toggleFiltersBtn" onclick="toggleFilters()">
                    <i class="fas fa-eye-slash me-1" id="toggleFiltersIcon"></i>
                    <span id="toggleFiltersText">إخفاء الفلاتر</span>
                </button>
            </div>
        </div>
        <div class="card-body collapse" id="filtersContent">
            <form id="filtersForm">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="startDate" name="start_date" onchange="applyFilters()">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="endDate" name="end_date" onchange="applyFilters()">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">المورد</label>
                        <select class="form-select" id="supplierFilter" name="supplier_id" onchange="applyFilters()">
                            <option value="">جميع الموردين</option>
                            <!-- سيتم تحميل الموردين ديناميكياً -->
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" id="statusFilter" name="status" onchange="applyFilters()">
                            <option value="">جميع الحالات</option>
                            <!-- سيتم تحميل الحالات ديناميكياً -->
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">البحث في الأصناف</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" name="search"
                                   placeholder="ابحث في أسماء الأصناف أو الأكواد..." oninput="debounceSearch()">
                            <button class="btn btn-outline-secondary" type="button" id="voiceSearchBtn" onclick="startVoiceSearch()">
                                <i class="fas fa-microphone" id="voiceIcon"></i>
                            </button>
                        </div>
                        <small class="text-muted">اضغط على المايكروفون للبحث الصوتي</small>
                    </div>
                    <div class="col-md-12 d-flex align-items-end justify-content-center mt-3">
                        <button type="button" class="btn btn-primary me-2" onclick="applyFilters()">
                            <i class="fas fa-search me-1"></i>
                            بحث
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                            <i class="fas fa-times me-1"></i>
                            مسح
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول الأصناف -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    جدول الأصناف التفصيلي
                </h5>
                <div class="d-flex gap-2">
                    <!-- مجموعة التصدير -->
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToExcel()" title="تصدير إلى Excel">
                            <i class="fas fa-file-excel me-1"></i>
                            Excel
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="exportToPDF()" title="تصدير إلى PDF">
                            <i class="fas fa-file-pdf me-1"></i>
                            PDF
                        </button>
                    </div>

                    <!-- مجموعة العمليات -->
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshData()" title="تحديث البيانات">
                            <i class="fas fa-sync-alt me-1"></i>
                            تحديث
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="resetFilters()" title="مسح جميع الفلاتر">
                            <i class="fas fa-times me-1"></i>
                            مسح الفلاتر
                        </button>
                    </div>

                    <!-- مجموعة العرض -->
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="toggleTableView()" title="تبديل عرض الجدول">
                            <i class="fas fa-expand-alt me-1"></i>
                            ملء الشاشة
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="showTableStats()" title="إحصائيات الجدول">
                            <i class="fas fa-chart-bar me-1"></i>
                            إحصائيات
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="itemsTable" class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr style="text-align: right;">
                            <th style="text-align: right;">كود الصنف</th>
                            <th style="text-align: right;">اسم الصنف</th>
                            <th style="text-align: right;">أمر الشراء</th>
                            <th style="text-align: right;">المورد</th>
                            <th style="text-align: right;">الكمية</th>
                            <th style="text-align: right;">سعر الوحدة</th>
                            <th style="text-align: right;">إجمالي القيمة</th>
                            <th style="text-align: right;">الوحدة</th>
                            <th style="text-align: right;">تاريخ الإنشاء</th>
                            <th style="text-align: right;">الحالة</th>
                            <th style="text-align: right;">العمليات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم تحميل البيانات ديناميكياً -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Loading Spinner -->
<div id="loadingSpinner" class="d-none">
    <div class="d-flex justify-content-center align-items-center position-fixed top-0 start-0 w-100 h-100" 
         style="background: rgba(0,0,0,0.5); z-index: 9999;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- مكتبات التصدير -->
<!-- SheetJS for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

<!-- jsPDF for PDF export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>

<!-- DataTables JS -->
<script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>

<style>
    /* محاذاة عناوين الجدول لليمين */
    #itemsTable thead th {
        text-align: right !important;
        direction: rtl;
    }

    /* محاذاة محتوى الجدول */
    #itemsTable tbody td {
        text-align: right;
        direction: rtl;
    }

    /* الأعمدة الرقمية بالإنجليزية - محاذاة صحيحة */
    #itemsTable tbody td:nth-child(5), /* الكمية */
    #itemsTable tbody td:nth-child(6), /* سعر الوحدة */
    #itemsTable tbody td:nth-child(7), /* إجمالي القيمة */
    #itemsTable tbody td:nth-child(9)  /* تاريخ الإنشاء */
    {
        direction: ltr;
        text-align: right !important;
    }

    /* محاذاة عناوين الأعمدة الرقمية */
    #itemsTable thead th:nth-child(5), /* الكمية */
    #itemsTable thead th:nth-child(6), /* سعر الوحدة */
    #itemsTable thead th:nth-child(7), /* إجمالي القيمة */
    #itemsTable thead th:nth-child(9)  /* تاريخ الإنشاء */
    {
        text-align: right !important;
    }

    /* محاذاة الأعمدة النصية */
    #itemsTable tbody td:nth-child(1), /* كود الصنف */
    #itemsTable tbody td:nth-child(2), /* اسم الصنف */
    #itemsTable tbody td:nth-child(3), /* أمر الشراء */
    #itemsTable tbody td:nth-child(4), /* المورد */
    #itemsTable tbody td:nth-child(8), /* الوحدة */
    #itemsTable tbody td:nth-child(10), /* الحالة */
    #itemsTable tbody td:nth-child(11)  /* العمليات */
    {
        text-align: right !important;
        direction: rtl;
    }

    /* تنسيق Header المتقدم */
    .header-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .breadcrumb {
        background: none;
        padding: 0;
        margin: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: #6c757d;
        font-weight: bold;
    }

    .breadcrumb-item a {
        color: #495057;
        transition: color 0.3s ease;
    }

    .breadcrumb-item a:hover {
        color: #007bff;
    }

    .breadcrumb-item.active {
        color: #6c757d;
        font-weight: 500;
    }

    /* تنسيق الفلاتر القابلة للطي */
    #filtersContent {
        transition: all 0.3s ease-in-out;
    }

    #filtersContent.collapse:not(.show) {
        display: none;
    }

    #filtersContent.collapse.show {
        display: block;
    }

    /* تحسين زر التبديل */
    #toggleFiltersBtn {
        transition: all 0.3s ease;
        border-radius: 20px;
    }

    #toggleFiltersBtn:hover {
        background-color: #f8f9fa;
        border-color: #6c757d;
    }
</style>

<script>
// انتظار تحميل المكتبات
function waitForLibraries(callback) {
    let attempts = 0;
    const maxAttempts = 50;

    function check() {
        attempts++;

        if (typeof $ !== 'undefined' && typeof $.fn.DataTable !== 'undefined') {
            console.log('✅ المكتبات محملة');
            callback();
        } else if (attempts < maxAttempts) {
            console.log(`⏳ انتظار المكتبات... (${attempts}/${maxAttempts})`);
            setTimeout(check, 100);
        } else {
            console.error('❌ فشل في تحميل المكتبات');
            alert('خطأ: فشل في تحميل المكتبات المطلوبة. يرجى إعادة تحميل الصفحة.');
        }
    }

    check();
}

// تهيئة الصفحة
function initializePage() {
    console.log('🚀 بدء تحميل صفحة تحليل الأصناف...');

    try {
        // تهيئة DataTable
        initializeDataTable();

        // تحميل البيانات الأولية
        loadItemsData();

        // تحميل قوائم الفلاتر
        loadFilterOptions();

        // تهيئة البحث الصوتي
        initVoiceSearch();

        console.log('✅ تم تحميل الصفحة بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تهيئة الصفحة:', error);
        alert('حدث خطأ في تحميل الصفحة: ' + error.message);
    }
}

// بدء التهيئة
$(document).ready(function() {
    waitForLibraries(initializePage);
});

// خريطة العملات إلى رموزها
const currencySymbols = {
    'CNY': '¥',    // يوان صيني
    'USD': '$',    // دولار أمريكي
    'EUR': '€',    // يورو
    'SAR': '﷼',    // ريال سعودي
    'AED': 'د.إ',   // درهم إماراتي
    'EGP': 'ج.م',   // جنيه مصري
    'JOD': 'د.أ',   // دينار أردني
    'KWD': 'د.ك',   // دينار كويتي
    'QAR': 'ر.ق',   // ريال قطري
    'BHD': 'د.ب',   // دينار بحريني
    'OMR': 'ر.ع',   // ريال عماني
    'LBP': 'ل.ل',   // ليرة لبنانية
    'SYP': 'ل.س',   // ليرة سورية
    'IQD': 'د.ع',   // دينار عراقي
    'YER': 'ر.ي',   // ريال يمني
    'LYD': 'د.ل',   // دينار ليبي
    'TND': 'د.ت',   // دينار تونسي
    'DZD': 'د.ج',   // دينار جزائري
    'MAD': 'د.م',   // درهم مغربي
    'SDG': 'ج.س'    // جنيه سوداني
};

// دالة لتحويل كود العملة إلى رمز
function getCurrencySymbol(currencyCode) {
    return currencySymbols[currencyCode] || currencyCode;
}

// متغيرات البحث الصوتي
let recognition;
let isListening = false;

// تهيئة البحث الصوتي
function initVoiceSearch() {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        recognition = new SpeechRecognition();

        recognition.lang = 'ar-SA'; // العربية السعودية
        recognition.continuous = false;
        recognition.interimResults = false;
        recognition.maxAlternatives = 1;

        recognition.onstart = function() {
            isListening = true;
            document.getElementById('voiceIcon').className = 'fas fa-microphone-slash text-danger';
            document.getElementById('voiceSearchBtn').classList.add('btn-danger');
            document.getElementById('voiceSearchBtn').classList.remove('btn-outline-secondary');
            console.log('بدء الاستماع...');
        };

        recognition.onresult = function(event) {
            const transcript = event.results[0][0].transcript;
            console.log('النص المسموع:', transcript);

            // وضع النص في حقل البحث
            document.getElementById('searchInput').value = transcript;

            // تطبيق البحث
            applyFilters();
        };

        recognition.onerror = function(event) {
            console.error('خطأ في التعرف على الصوت:', event.error);
            stopVoiceSearch();

            let errorMessage = 'حدث خطأ في البحث الصوتي';
            switch(event.error) {
                case 'no-speech':
                    errorMessage = 'لم يتم سماع أي صوت';
                    break;
                case 'audio-capture':
                    errorMessage = 'لا يمكن الوصول للمايكروفون';
                    break;
                case 'not-allowed':
                    errorMessage = 'تم رفض الإذن للوصول للمايكروفون';
                    break;
            }

            showError(errorMessage);
        };

        recognition.onend = function() {
            stopVoiceSearch();
        };

        console.log('✅ تم تهيئة البحث الصوتي');
    } else {
        console.warn('❌ البحث الصوتي غير مدعوم في هذا المتصفح');
        document.getElementById('voiceSearchBtn').style.display = 'none';
    }
}

// بدء البحث الصوتي
function startVoiceSearch() {
    if (!recognition) {
        showError('البحث الصوتي غير مدعوم في هذا المتصفح');
        return;
    }

    if (isListening) {
        recognition.stop();
        return;
    }

    try {
        recognition.start();
    } catch (error) {
        console.error('خطأ في بدء البحث الصوتي:', error);
        showError('فشل في بدء البحث الصوتي');
    }
}

// إيقاف البحث الصوتي
function stopVoiceSearch() {
    isListening = false;
    document.getElementById('voiceIcon').className = 'fas fa-microphone';
    document.getElementById('voiceSearchBtn').classList.remove('btn-danger');
    document.getElementById('voiceSearchBtn').classList.add('btn-outline-secondary');
}

// متغير لحالة الفلاتر
let filtersVisible = false; // الافتراضي مخفي

// دالة إخفاء/إظهار الفلاتر
function toggleFilters() {
    const filtersContent = document.getElementById('filtersContent');
    const toggleIcon = document.getElementById('toggleFiltersIcon');
    const toggleText = document.getElementById('toggleFiltersText');

    if (filtersVisible) {
        // إخفاء الفلاتر
        filtersContent.classList.remove('show');
        toggleIcon.className = 'fas fa-eye me-1';
        toggleText.textContent = 'إظهار الفلاتر';
        filtersVisible = false;
        console.log('تم إخفاء الفلاتر');
    } else {
        // إظهار الفلاتر
        filtersContent.classList.add('show');
        toggleIcon.className = 'fas fa-eye-slash me-1';
        toggleText.textContent = 'إخفاء الفلاتر';
        filtersVisible = true;
        console.log('تم إظهار الفلاتر');
    }
}

// دالة تحديث البيانات
function refreshData() {
    console.log('🔄 تحديث البيانات...');
    showInfo('جاري تحديث البيانات...');
    loadItemsData();
}

// دالة مسح الفلاتر
function resetFilters() {
    console.log('🗑️ مسح جميع الفلاتر...');

    try {
        // مسح جميع حقول الفلاتر
        document.getElementById('startDate').value = '';
        document.getElementById('endDate').value = '';
        document.getElementById('supplierFilter').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('categoryFilter').value = '';
        document.getElementById('searchInput').value = '';

        // إعادة تحميل البيانات بدون فلاتر
        loadItemsData();

        showInfo('تم مسح جميع الفلاتر وإعادة تحميل البيانات');
        console.log('✅ تم مسح الفلاتر بنجاح');

    } catch (error) {
        console.error('❌ خطأ في مسح الفلاتر:', error);
        showError('خطأ في مسح الفلاتر: ' + error.message);
    }
}

// متغير لحالة ملء الشاشة
let isFullScreen = false;

// دالة تبديل عرض الجدول (ملء الشاشة)
function toggleTableView() {
    console.log('🖥️ تبديل عرض الجدول...');

    const tableCard = document.querySelector('.card.border-0.shadow-sm:last-of-type');
    const toggleBtn = event.target.closest('button');
    const icon = toggleBtn.querySelector('i');

    if (!isFullScreen) {
        // تفعيل ملء الشاشة
        tableCard.style.position = 'fixed';
        tableCard.style.top = '0';
        tableCard.style.left = '0';
        tableCard.style.width = '100vw';
        tableCard.style.height = '100vh';
        tableCard.style.zIndex = '9999';
        tableCard.style.backgroundColor = 'white';
        tableCard.style.overflow = 'auto';

        icon.className = 'fas fa-compress-alt me-1';
        toggleBtn.title = 'إلغاء ملء الشاشة';
        isFullScreen = true;

        showInfo('تم تفعيل وضع ملء الشاشة');
    } else {
        // إلغاء ملء الشاشة
        tableCard.style.position = '';
        tableCard.style.top = '';
        tableCard.style.left = '';
        tableCard.style.width = '';
        tableCard.style.height = '';
        tableCard.style.zIndex = '';
        tableCard.style.backgroundColor = '';
        tableCard.style.overflow = '';

        icon.className = 'fas fa-expand-alt me-1';
        toggleBtn.title = 'تفعيل ملء الشاشة';
        isFullScreen = false;

        showInfo('تم إلغاء وضع ملء الشاشة');
    }
}

// دالة عرض إحصائيات الجدول
function showTableStats() {
    console.log('📊 عرض إحصائيات الجدول...');

    try {
        const table = $('#itemsTable').DataTable();
        const data = table.rows({ search: 'applied' }).data().toArray();

        if (data.length === 0) {
            showInfo('لا توجد بيانات لعرض الإحصائيات');
            return;
        }

        // حساب الإحصائيات
        const totalItems = data.length;
        const totalQuantity = data.reduce((sum, row) => sum + (row.total_quantity || 0), 0);
        const totalValue = data.reduce((sum, row) => sum + (row.total_value || 0), 0);
        const avgValue = totalValue / totalItems;

        // عدد الموردين الفريدين
        const uniqueSuppliers = [...new Set(data.map(row => row.supplier_name))].filter(s => s).length;

        // أعلى قيمة وأقل قيمة
        const values = data.map(row => row.total_value || 0).filter(v => v > 0);
        const maxValue = Math.max(...values);
        const minValue = Math.min(...values);

        const statsHtml = `
            <div class="modal fade" id="statsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-chart-bar me-2"></i>
                                إحصائيات الجدول التفصيلية
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h3 class="text-primary">${totalItems.toLocaleString()}</h3>
                                            <small>إجمالي الأصناف المعروضة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h3 class="text-success">${totalQuantity.toLocaleString()}</h3>
                                            <small>إجمالي الكمية</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h3 class="text-info">¥${totalValue.toLocaleString()}</h3>
                                            <small>إجمالي القيمة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h3 class="text-warning">¥${avgValue.toFixed(2)}</h3>
                                            <small>متوسط قيمة الصنف</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h3 class="text-secondary">${uniqueSuppliers}</h3>
                                            <small>عدد الموردين</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h3 class="text-danger">¥${maxValue.toLocaleString()}</h3>
                                            <small>أعلى قيمة صنف</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <h6>معلومات إضافية:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-info-circle text-info me-2"></i>أقل قيمة صنف: ¥${minValue.toLocaleString()}</li>
                                    <li><i class="fas fa-info-circle text-info me-2"></i>نطاق القيم: ¥${(maxValue - minValue).toLocaleString()}</li>
                                    <li><i class="fas fa-info-circle text-info me-2"></i>تاريخ الإحصائيات: ${new Date().toLocaleDateString('ar-SA')}</li>
                                </ul>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="exportStatsToExcel()">
                                <i class="fas fa-file-excel me-1"></i>
                                تصدير الإحصائيات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إزالة النافذة القديمة إذا كانت موجودة
        $('#statsModal').remove();

        // إضافة النافذة الجديدة
        $('body').append(statsHtml);

        // عرض النافذة
        const modal = new bootstrap.Modal(document.getElementById('statsModal'));
        modal.show();

        console.log('✅ تم عرض إحصائيات الجدول');

    } catch (error) {
        console.error('❌ خطأ في عرض الإحصائيات:', error);
        showError('خطأ في عرض الإحصائيات: ' + error.message);
    }
}

// دالة تصدير الإحصائيات إلى Excel
function exportStatsToExcel() {
    console.log('📊 تصدير الإحصائيات إلى Excel...');

    try {
        const table = $('#itemsTable').DataTable();
        const data = table.rows({ search: 'applied' }).data().toArray();

        // إنشاء بيانات الإحصائيات
        const statsData = [
            ['إحصائيات جدول الأصناف التفصيلي'],
            [''],
            ['البيان', 'القيمة'],
            ['إجمالي الأصناف', data.length],
            ['إجمالي الكمية', data.reduce((sum, row) => sum + (row.total_quantity || 0), 0)],
            ['إجمالي القيمة', `¥${data.reduce((sum, row) => sum + (row.total_value || 0), 0).toLocaleString()}`],
            ['متوسط قيمة الصنف', `¥${(data.reduce((sum, row) => sum + (row.total_value || 0), 0) / data.length).toFixed(2)}`],
            ['عدد الموردين', [...new Set(data.map(row => row.supplier_name))].filter(s => s).length],
            ['تاريخ الإحصائيات', new Date().toLocaleDateString('ar-SA')]
        ];

        // إنشاء workbook
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.aoa_to_sheet(statsData);

        XLSX.utils.book_append_sheet(wb, ws, 'إحصائيات الأصناف');

        // تصدير الملف
        const fileName = `إحصائيات_الأصناف_${new Date().toISOString().split('T')[0]}.xlsx`;
        XLSX.writeFile(wb, fileName);

        showInfo('تم تصدير الإحصائيات إلى Excel بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تصدير الإحصائيات:', error);
        showError('خطأ في تصدير الإحصائيات: ' + error.message);
    }
}

// دالة تصدير Excel مبسطة
function exportToExcel() {
    console.log('🚀 بدء تصدير Excel...');

    // فحص توفر مكتبة XLSX
    if (typeof XLSX === 'undefined') {
        alert('مكتبة Excel غير متوفرة. يرجى إعادة تحميل الصفحة.');
        return;
    }

    try {
        // جلب البيانات من الجدول
        const table = $('#itemsTable').DataTable();
        if (!table) {
            alert('الجدول غير متوفر');
            return;
        }

        const data = table.rows({ search: 'applied' }).data().toArray();
        if (data.length === 0) {
            alert('لا توجد بيانات للتصدير');
            return;
        }

        console.log(`جاري تصدير ${data.length} صنف...`);

        // إنشاء workbook
        const wb = XLSX.utils.book_new();
        const exportData = [];

        // العناوين
        exportData.push([
            'كود الصنف', 'اسم الصنف', 'أمر الشراء', 'المورد',
            'الكمية', 'سعر الوحدة', 'إجمالي القيمة', 'الوحدة',
            'تاريخ الإنشاء', 'الحالة'
        ]);

        // البيانات
        data.forEach(row => {
            const currency = getCurrencySymbol(row.currency || 'CNY');
            exportData.push([
                row.item_code || '',
                row.item_name || '',
                row.category || '',
                row.supplier_name || '',
                row.total_quantity || 0,
                `${currency}${(row.avg_price || 0).toFixed(2)}`,
                `${currency}${(row.total_value || 0).toLocaleString('en-US')}`,
                row.unit || '',
                row.last_order_date || '',
                row.status || ''
            ]);
        });

        // إنشاء worksheet
        const ws = XLSX.utils.aoa_to_sheet(exportData);

        // تنسيق العرض
        ws['!cols'] = [
            {width: 15}, {width: 30}, {width: 20}, {width: 25}, {width: 12},
            {width: 15}, {width: 18}, {width: 10}, {width: 15}, {width: 12}
        ];

        // إضافة الـ worksheet إلى الـ workbook
        XLSX.utils.book_append_sheet(wb, ws, 'تحليل الأصناف');

        // تصدير الملف
        const fileName = `تحليل_أصناف_أوامر_الشراء_${new Date().toISOString().split('T')[0]}.xlsx`;
        XLSX.writeFile(wb, fileName);

        alert(`✅ تم تصدير ${data.length} صنف إلى ملف Excel بنجاح!`);
        console.log('✅ تم تصدير Excel بنجاح');

    } catch (error) {
        alert('فشل في تصدير ملف Excel: ' + error.message);
        console.error('❌ خطأ في تصدير Excel:', error);
    }
}

// دالة تصدير PDF
function exportToPDF() {
    console.log('بدء تصدير PDF...');

    // فحص توفر مكتبة jsPDF
    if (typeof window.jspdf === 'undefined') {
        showError('مكتبة PDF غير متوفرة. يرجى إعادة تحميل الصفحة.');
        return;
    }

    try {
        // إظهار رسالة تحميل
        showInfo('جاري تحضير ملف PDF...');

        // جلب البيانات الحالية من الجدول
        const table = $('#itemsTable').DataTable();

        if (!table) {
            showError('الجدول غير متوفر');
            return;
        }

        const data = table.rows({ search: 'applied' }).data().toArray();

        if (data.length === 0) {
            showError('لا توجد بيانات للتصدير');
            return;
        }

        console.log(`جاري تصدير ${data.length} صنف إلى PDF...`);

        // إنشاء مستند PDF جديد
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF('l', 'mm', 'a4'); // landscape orientation

        // إعداد الخط العربي
        doc.setFont('helvetica');

        // العنوان الرئيسي
        doc.setFontSize(20);
        doc.setTextColor(70, 110, 234); // لون أزرق
        doc.text('تحليل أصناف أوامر الشراء', 148, 20, { align: 'center' });

        // معلومات التصدير
        doc.setFontSize(10);
        doc.setTextColor(100, 100, 100);
        const exportDate = new Date().toLocaleDateString('ar-SA');
        const exportTime = new Date().toLocaleTimeString('ar-SA');
        doc.text(`تاريخ التصدير: ${exportDate}`, 20, 35);
        doc.text(`وقت التصدير: ${exportTime}`, 20, 42);
        doc.text(`عدد الأصناف: ${data.length}`, 200, 35);

        // خط فاصل
        doc.setDrawColor(200, 200, 200);
        doc.line(20, 48, 277, 48);

        // إعداد الجدول
        const tableColumns = [
            'كود الصنف',
            'اسم الصنف',
            'المورد',
            'الكمية',
            'سعر الوحدة',
            'إجمالي القيمة',
            'الحالة'
        ];

        const tableRows = [];

        // تحضير بيانات الجدول
        data.forEach(row => {
            const currency = getCurrencySymbol(row.currency || 'CNY');
            tableRows.push([
                row.item_code || '',
                (row.item_name || '').substring(0, 25) + ((row.item_name || '').length > 25 ? '...' : ''),
                (row.supplier_name || '').substring(0, 20) + ((row.supplier_name || '').length > 20 ? '...' : ''),
                (row.total_quantity || 0).toLocaleString('en-US'),
                `${currency}${(row.avg_price || 0).toFixed(2)}`,
                `${currency}${(row.total_value || 0).toLocaleString('en-US')}`,
                row.status || ''
            ]);
        });

        // إنشاء الجدول باستخدام autoTable
        doc.autoTable({
            head: [tableColumns],
            body: tableRows,
            startY: 55,
            styles: {
                fontSize: 8,
                cellPadding: 2,
                overflow: 'linebreak',
                halign: 'center'
            },
            headStyles: {
                fillColor: [70, 110, 234],
                textColor: [255, 255, 255],
                fontStyle: 'bold',
                halign: 'center'
            },
            alternateRowStyles: {
                fillColor: [245, 245, 245]
            },
            columnStyles: {
                0: { cellWidth: 25 }, // كود الصنف
                1: { cellWidth: 50, halign: 'right' }, // اسم الصنف
                2: { cellWidth: 40, halign: 'right' }, // المورد
                3: { cellWidth: 20 }, // الكمية
                4: { cellWidth: 25 }, // سعر الوحدة
                5: { cellWidth: 30 }, // إجمالي القيمة
                6: { cellWidth: 20 } // الحالة
            },
            margin: { top: 55, right: 20, bottom: 20, left: 20 },
            didDrawPage: function(data) {
                // إضافة رقم الصفحة
                const pageNumber = doc.internal.getNumberOfPages();
                const pageHeight = doc.internal.pageSize.height;
                doc.setFontSize(8);
                doc.setTextColor(100, 100, 100);
                doc.text(`صفحة ${data.pageNumber} من ${pageNumber}`, 148, pageHeight - 10, { align: 'center' });
            }
        });

        // إضافة صفحة إحصائيات إذا كان هناك مساحة
        const finalY = doc.lastAutoTable.finalY;
        const pageHeight = doc.internal.pageSize.height;

        if (finalY < pageHeight - 80) {
            // إضافة إحصائيات في نفس الصفحة
            doc.setFontSize(14);
            doc.setTextColor(70, 110, 234);
            doc.text('الإحصائيات', 20, finalY + 20);

            // حساب الإحصائيات
            const totalValue = data.reduce((sum, row) => sum + (row.total_value || 0), 0);
            const totalQuantity = data.reduce((sum, row) => sum + (row.total_quantity || 0), 0);
            const avgValue = totalValue / data.length;

            doc.setFontSize(10);
            doc.setTextColor(0, 0, 0);
            doc.text(`إجمالي الأصناف: ${data.length}`, 20, finalY + 35);
            doc.text(`إجمالي الكمية: ${totalQuantity.toLocaleString('en-US')}`, 20, finalY + 45);
            doc.text(`إجمالي القيمة: ¥${totalValue.toLocaleString('en-US')}`, 20, finalY + 55);
            doc.text(`متوسط قيمة الصنف: ¥${avgValue.toFixed(2)}`, 20, finalY + 65);
        }

        // حفظ الملف
        const fileName = `تحليل_أصناف_أوامر_الشراء_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);

        showInfo(`✅ تم تصدير ${data.length} صنف إلى ملف PDF بنجاح!`);
        console.log('✅ تم تصدير PDF بنجاح');

    } catch (error) {
        showError('فشل في تصدير ملف PDF: ' + error.message);
        console.error('❌ خطأ في تصدير PDF:', error);
    }
}

function initializeDataTable() {
    console.log('🔧 بدء تهيئة DataTable...');

    try {
        // التحقق من وجود العنصر
        if ($('#itemsTable').length === 0) {
            throw new Error('عنصر الجدول #itemsTable غير موجود');
        }

        // تدمير الجدول إذا كان موجوداً
        if ($.fn.DataTable.isDataTable('#itemsTable')) {
            $('#itemsTable').DataTable().destroy();
            console.log('تم تدمير الجدول القديم');
        }

        const table = $('#itemsTable').DataTable({
            processing: true,
            serverSide: false,
            responsive: true,
            destroy: true, // للسماح بإعادة التهيئة
            language: {
                processing: "جاري المعالجة...",
                search: "بحث:",
                lengthMenu: "أظهر _MENU_ مدخلات",
                info: "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                infoEmpty: "يعرض 0 إلى 0 من أصل 0 سجل",
                infoFiltered: "(منتقاة من مجموع _MAX_ مُدخل)",
                loadingRecords: "جاري التحميل...",
                zeroRecords: "لم يعثر على أية سجلات",
                emptyTable: "لا توجد بيانات متاحة في الجدول",
                paginate: {
                    first: "الأول",
                    previous: "السابق",
                    next: "التالي",
                    last: "الأخير"
                }
            },
            columns: [
                {
                    data: 'item_code',
                    title: 'كود الصنف',
                    defaultContent: ''
                },
                {
                    data: 'item_name',
                    title: 'اسم الصنف',
                    defaultContent: '',
                    render: function(data) {
                        return data ? '<strong>' + data + '</strong>' : '';
                    }
                },
                {
                    data: 'category',
                    title: 'أمر الشراء',
                    defaultContent: 'غير محدد',
                    render: function(data) {
                        return '<span class="badge bg-info">' + (data || 'غير محدد') + '</span>';
                    }
                },
                {
                    data: 'supplier_name',
                    title: 'المورد',
                    defaultContent: 'غير محدد'
                },
                {
                    data: 'total_quantity',
                    title: 'الكمية',
                    render: function(data) {
                        // عرض الأرقام بالإنجليزية
                        return data ? parseFloat(data).toLocaleString('en-US') : '0';
                    }
                },
                {
                    data: 'avg_price',
                    title: 'سعر الوحدة',
                    render: function(data, type, row) {
                        const currency = row.currency || 'CNY';
                        const symbol = getCurrencySymbol(currency);
                        return data ? symbol + parseFloat(data).toFixed(2) : symbol + '0.00';
                    }
                },
                {
                    data: 'total_value',
                    title: 'إجمالي القيمة',
                    render: function(data, type, row) {
                        const currency = row.currency || 'CNY';
                        const symbol = getCurrencySymbol(currency);
                        // عرض الأرقام بالإنجليزية
                        return data ? '<strong>' + symbol + parseFloat(data).toLocaleString('en-US') + '</strong>' : symbol + '0.00';
                    }
                },
                {
                    data: 'unit',
                    title: 'الوحدة',
                    defaultContent: 'قطعة'
                },
                {
                    data: 'last_order_date',
                    title: 'تاريخ الإنشاء',
                    defaultContent: '',
                    render: function(data) {
                        if (data) {
                            const date = new Date(data);
                            // عرض التاريخ بالأرقام الإنجليزية
                            return date.toLocaleDateString('en-GB');
                        }
                        return '';
                    }
                },
                {
                    data: 'status',
                    title: 'الحالة',
                    render: function(data) {
                        const statusMap = {
                            'pending': '<span class="badge bg-warning">معلق</span>',
                            'approved': '<span class="badge bg-info">موافق عليه</span>',
                            'completed': '<span class="badge bg-success">مكتمل</span>',
                            'cancelled': '<span class="badge bg-danger">ملغي</span>',
                            'تم التسليم': '<span class="badge bg-success">تم التسليم</span>',
                            'مسودة': '<span class="badge bg-secondary">مسودة</span>'
                        };
                        return statusMap[data] || '<span class="badge bg-secondary">' + (data || 'غير محدد') + '</span>';
                    }
                },
                {
                    data: null,
                    title: 'العمليات',
                    orderable: false,
                    render: function(data, type, row) {
                        return `
                            <button class="btn btn-sm btn-outline-primary" onclick="viewItemDetails('${row.item_code || ''}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="viewItemChart('${row.item_code || ''}')">
                                <i class="fas fa-chart-line"></i>
                            </button>
                        `;
                    }
                }
            ],
            order: [[6, 'desc']], // ترتيب حسب إجمالي القيمة
            pageLength: 25,
            dom: 'Bfrtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ]
        });

        console.log('✅ تم تهيئة DataTable بنجاح');
        return table;

    } catch (error) {
        console.error('❌ خطأ في تهيئة DataTable:', error);
        alert('خطأ في تهيئة الجدول: ' + error.message);
        return null;
    }
}

function loadItemsData() {
    console.log('بدء تحميل بيانات الأصناف...');
    showLoading();

    const formData = new FormData(document.getElementById('filtersForm'));
    const params = new URLSearchParams(formData);

    console.log('معاملات الطلب:', params.toString());

    fetch(`/purchase-orders/api/items/data?${params}`)
        .then(response => {
            console.log('حالة الاستجابة:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('البيانات المستلمة:', data);

            if (data.success) {
                const table = $('#itemsTable').DataTable();
                table.clear().rows.add(data.data).draw();
                console.log(`✅ تم تحميل ${data.data.length} صنف بنجاح`);

                if (data.data.length === 0) {
                    showInfo('لا توجد أصناف تطابق معايير البحث');
                }
            } else {
                console.error('خطأ في البيانات:', data.error);
                showError('خطأ في تحميل البيانات: ' + (data.error || 'خطأ غير معروف'));
            }
        })
        .catch(error => {
            console.error('خطأ في الشبكة:', error);
            showError('حدث خطأ في تحميل البيانات: ' + error.message);
        })
        .finally(() => {
            hideLoading();
        });
}

function loadFilterOptions() {
    console.log('بدء تحميل خيارات الفلاتر...');

    // تحميل قائمة الموردين
    fetch('/purchase-orders/api/items/suppliers')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const supplierSelect = document.getElementById('supplierFilter');
                // مسح الخيارات الموجودة (عدا الخيار الافتراضي)
                while (supplierSelect.children.length > 1) {
                    supplierSelect.removeChild(supplierSelect.lastChild);
                }

                data.data.forEach(supplier => {
                    const option = document.createElement('option');
                    option.value = supplier.value;
                    option.textContent = supplier.label;
                    supplierSelect.appendChild(option);
                });
                console.log(`✅ تم تحميل ${data.data.length} مورد`);
            } else {
                console.error('خطأ في جلب الموردين:', data.error);
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل الموردين:', error);
        });

    // تحميل قائمة الحالات
    fetch('/purchase-orders/api/items/statuses')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const statusSelect = document.getElementById('statusFilter');
                // مسح الخيارات الموجودة (عدا الخيار الافتراضي)
                while (statusSelect.children.length > 1) {
                    statusSelect.removeChild(statusSelect.lastChild);
                }

                data.data.forEach(status => {
                    const option = document.createElement('option');
                    option.value = status.value;
                    option.textContent = `${status.label} (${status.count})`;
                    statusSelect.appendChild(option);
                });
                console.log(`✅ تم تحميل ${data.data.length} حالة`);
            } else {
                console.error('خطأ في جلب الحالات:', data.error);
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل الحالات:', error);
        });
}

// متغير لتأخير البحث
let searchTimeout;

function debounceSearch() {
    // إلغاء التأخير السابق
    clearTimeout(searchTimeout);

    // تأخير البحث لمدة 500 مللي ثانية
    searchTimeout = setTimeout(() => {
        applyFilters();
    }, 500);
}

function applyFilters() {
    console.log('تطبيق الفلاتر...');
    loadItemsData();
}

function clearFilters() {
    document.getElementById('filtersForm').reset();
    loadItemsData();
}

function viewItemDetails(itemCode) {
    // عرض تفاصيل الصنف
    alert('عرض تفاصيل الصنف: ' + itemCode);
}

function viewItemChart(itemCode) {
    // عرض رسم بياني للصنف
    alert('عرض رسم بياني للصنف: ' + itemCode);
}

// تم نقل دوال التصدير إلى أعلى الملف

function showLoading() {
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        spinner.classList.remove('d-none');
    }
}

function hideLoading() {
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        spinner.classList.add('d-none');
    }
}

function showError(message) {
    console.error('خطأ:', message);

    // إنشاء تنبيه Bootstrap
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // إضافة التنبيه في أعلى الصفحة
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        const alert = container.querySelector('.alert-danger');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

function showInfo(message) {
    console.info('معلومات:', message);

    const alertHtml = `
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    setTimeout(() => {
        const alert = container.querySelector('.alert-info');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
