-- =====================================================
-- إجراء إلغاء الحوالة وعكس الترحيل المحاسبي
-- Cancel Transfer Accounting Procedure
-- =====================================================

-- إنشاء إجراء إلغاء الحوالة وعكس الترحيلات
CREATE OR REPLACE PROCEDURE CANCEL_TRANSFER_ACCOUNTING(
    p_transfer_id IN NUMBER,
    p_user_id IN NUMBER DEFAULT 1,
    p_ip_address IN VARCHAR2 DEFAULT NULL,
    p_session_id IN VARCHAR2 DEFAULT NULL,
    p_cancellation_reason IN VARCHAR2 DEFAULT NULL
) AS
    v_money_changer_id NUMBER;
    v_total_amount NUMBER;
    v_currency_code VARCHAR2(10);
    v_supplier_id NUMBER;
    v_supplier_amount NUMBER;
    v_error_msg VARCHAR2(4000);
    v_start_time TIMESTAMP;
    v_execution_time NUMBER;
    v_transfer_status VARCHAR2(50);
    v_distributions_count NUMBER := 0;
    v_total_reversed NUMBER := 0;
    
    -- Cursor لقراءة توزيعات الموردين
    CURSOR supplier_distributions_cur IS
        SELECT supplier_id, amount, currency_code
        FROM transfer_supplier_distributions
        WHERE transfer_id = p_transfer_id;
    
    -- Exception للأخطاء المخصصة
    e_transfer_not_executed EXCEPTION;
    e_transfer_not_found EXCEPTION;
    e_balance_insufficient EXCEPTION;
    
BEGIN
    v_start_time := CURRENT_TIMESTAMP;
    
    -- بدء المعاملة
    SAVEPOINT cancel_start;
    
    -- تسجيل بداية العملية
    LOG_TRANSFER_ACTIVITY(
        p_transfer_id => p_transfer_id,
        p_activity_type => 'CANCELLATION_STARTED',
        p_description => 'بدء عملية إلغاء الحوالة وعكس الترحيلات المحاسبية',
        p_operation_details => JSON_OBJECT('reason' VALUE p_cancellation_reason),
        p_user_id => p_user_id,
        p_ip_address => p_ip_address,
        p_session_id => p_session_id
    );
    
    -- 1. التحقق من صحة البيانات
    IF p_transfer_id IS NULL OR p_transfer_id <= 0 THEN
        v_error_msg := 'معرف الحوالة غير صحيح';
        RAISE e_transfer_not_found;
    END IF;
    
    -- 2. الحصول على بيانات الحوالة والتحقق من حالتها
    BEGIN
        SELECT money_changer_id, amount, currency, status
        INTO v_money_changer_id, v_total_amount, v_currency_code, v_transfer_status
        FROM transfers
        WHERE id = p_transfer_id;
        
        IF v_transfer_status != 'executed' THEN
            v_error_msg := 'لا يمكن إلغاء حوالة غير منفذة. الحالة الحالية: ' || v_transfer_status;
            RAISE e_transfer_not_executed;
        END IF;
        
        IF v_money_changer_id IS NULL THEN
            v_error_msg := 'لا يوجد صراف محدد للحوالة';
            RAISE e_transfer_not_executed;
        END IF;
        
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            v_error_msg := 'الحوالة غير موجودة';
            RAISE e_transfer_not_found;
    END;
    
    -- 3. التحقق من وجود توزيعات للموردين
    SELECT COUNT(*) INTO v_distributions_count
    FROM transfer_supplier_distributions
    WHERE transfer_id = p_transfer_id;
    
    IF v_distributions_count = 0 THEN
        v_error_msg := 'لا توجد توزيعات موردين لهذه الحوالة';
        RAISE e_transfer_not_executed;
    END IF;
    
    -- 4. عكس ترحيلات الموردين (تقليل أرصدة الموردين)
    FOR rec IN supplier_distributions_cur LOOP
        -- التحقق من كفاية رصيد المورد قبل العكس
        DECLARE
            v_supplier_balance NUMBER := 0;
        BEGIN
            SELECT NVL(current_balance, 0) INTO v_supplier_balance
            FROM CURRENT_BALANCES
            WHERE entity_type_code = 'SUPPLIER'
            AND entity_id = rec.supplier_id
            AND currency_code = rec.currency_code;
            
            IF v_supplier_balance < rec.amount THEN
                -- تسجيل تحذير ولكن المتابعة (قد يكون هناك مدفوعات أخرى)
                LOG_TRANSFER_ACTIVITY(
                    p_transfer_id => p_transfer_id,
                    p_activity_type => 'WARNING',
                    p_description => 'رصيد المورد ' || rec.supplier_id || ' أقل من المبلغ المطلوب عكسه',
                    p_amount_before => v_supplier_balance,
                    p_amount_after => rec.amount,
                    p_currency_code => rec.currency_code,
                    p_entity_type => 'SUPPLIER',
                    p_entity_id => rec.supplier_id,
                    p_user_id => p_user_id
                );
            END IF;
        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                -- إذا لم يكن هناك رصيد، سيتم إنشاء رصيد سالب
                NULL;
        END;
        
        -- عكس ترحيل المورد (تقليل الرصيد - دائن)
        UPDATE_CURRENT_BALANCE(
            p_entity_type => 'SUPPLIER',
            p_entity_id => rec.supplier_id,
            p_currency_code => rec.currency_code,
            p_debit_amount => 0,
            p_credit_amount => rec.amount, -- عكس العملية
            p_document_type => 'TRANSFER_CANCEL',
            p_document_number => 'CTRF-' || p_transfer_id,
            p_description => 'إلغاء حوالة رقم ' || p_transfer_id || CASE 
                WHEN p_cancellation_reason IS NOT NULL THEN ' - السبب: ' || p_cancellation_reason
                ELSE ''
            END,
            p_user_id => p_user_id
        );
        
        -- تسجيل النشاط للمورد
        LOG_TRANSFER_ACTIVITY(
            p_transfer_id => p_transfer_id,
            p_activity_type => 'BALANCE_UPDATED',
            p_description => 'تم عكس ترحيل مبلغ ' || rec.amount || ' ' || rec.currency_code || ' من رصيد المورد',
            p_amount_before => rec.amount,
            p_amount_after => 0,
            p_currency_code => rec.currency_code,
            p_entity_type => 'SUPPLIER',
            p_entity_id => rec.supplier_id,
            p_user_id => p_user_id,
            p_ip_address => p_ip_address,
            p_session_id => p_session_id
        );
        
        v_total_reversed := v_total_reversed + rec.amount;
    END LOOP;
    
    -- 5. عكس ترحيل الصراف/البنك (زيادة رصيد الصراف - مدين)
    UPDATE_CURRENT_BALANCE(
        p_entity_type => 'MONEY_CHANGER',
        p_entity_id => v_money_changer_id,
        p_currency_code => v_currency_code,
        p_debit_amount => v_total_amount, -- عكس العملية
        p_credit_amount => 0,
        p_document_type => 'TRANSFER_CANCEL',
        p_document_number => 'CTRF-' || p_transfer_id,
        p_description => 'إلغاء حوالة رقم ' || p_transfer_id || CASE 
            WHEN p_cancellation_reason IS NOT NULL THEN ' - السبب: ' || p_cancellation_reason
            ELSE ''
        END,
        p_user_id => p_user_id
    );
    
    -- 6. تسجيل النشاط للصراف
    LOG_TRANSFER_ACTIVITY(
        p_transfer_id => p_transfer_id,
        p_activity_type => 'BALANCE_UPDATED',
        p_description => 'تم إرجاع مبلغ ' || v_total_amount || ' ' || v_currency_code || ' لرصيد الصراف',
        p_amount_after => v_total_amount,
        p_currency_code => v_currency_code,
        p_entity_type => 'MONEY_CHANGER',
        p_entity_id => v_money_changer_id,
        p_user_id => p_user_id,
        p_ip_address => p_ip_address,
        p_session_id => p_session_id
    );
    
    -- 7. تحديث حالة الحوالة
    UPDATE transfers SET
        status = 'cancelled',
        cancelled_at = CURRENT_TIMESTAMP,
        cancelled_by = p_user_id,
        cancellation_reason = p_cancellation_reason,
        updated_at = CURRENT_TIMESTAMP,
        updated_by = p_user_id
    WHERE id = p_transfer_id;
    
    -- 8. إضافة علامة للتوزيعات المحذوفة (بدلاً من الحذف الفعلي للاحتفاظ بالسجل)
    UPDATE transfer_supplier_distributions SET
        notes = NVL(notes, '') || ' [تم إلغاء الحوالة في ' || TO_CHAR(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS') || ']',
        updated_at = CURRENT_TIMESTAMP,
        updated_by = p_user_id
    WHERE transfer_id = p_transfer_id;
    
    -- 9. حساب وقت التنفيذ
    v_execution_time := EXTRACT(SECOND FROM (CURRENT_TIMESTAMP - v_start_time)) * 1000;
    
    -- 10. تسجيل نجاح العملية
    LOG_TRANSFER_ACTIVITY(
        p_transfer_id => p_transfer_id,
        p_activity_type => 'CANCELLED',
        p_description => 'تم إلغاء الحوالة وعكس جميع الترحيلات المحاسبية بنجاح' || CASE 
            WHEN p_cancellation_reason IS NOT NULL THEN ' - السبب: ' || p_cancellation_reason
            ELSE ''
        END,
        p_old_status => 'executed',
        p_new_status => 'cancelled',
        p_amount_before => v_total_amount,
        p_amount_after => 0,
        p_currency_code => v_currency_code,
        p_entity_type => 'MONEY_CHANGER',
        p_entity_id => v_money_changer_id,
        p_operation_details => JSON_OBJECT(
            'distributions_count' VALUE v_distributions_count,
            'total_reversed' VALUE v_total_reversed,
            'reason' VALUE p_cancellation_reason
        ),
        p_execution_time_ms => v_execution_time,
        p_user_id => p_user_id,
        p_ip_address => p_ip_address,
        p_session_id => p_session_id
    );
    
    COMMIT;
    
    -- إرجاع رسالة نجاح
    DBMS_OUTPUT.PUT_LINE('SUCCESS: تم إلغاء الحوالة ' || p_transfer_id || ' وعكس جميع الترحيلات بنجاح');
    
EXCEPTION
    WHEN e_transfer_not_found THEN
        ROLLBACK TO cancel_start;
        
        LOG_TRANSFER_ACTIVITY(
            p_transfer_id => p_transfer_id,
            p_activity_type => 'ERROR',
            p_description => 'فشل في إلغاء الحوالة - الحوالة غير موجودة',
            p_error_message => v_error_msg,
            p_user_id => p_user_id,
            p_ip_address => p_ip_address,
            p_session_id => p_session_id
        );
        
        RAISE_APPLICATION_ERROR(-20001, v_error_msg);
        
    WHEN e_transfer_not_executed THEN
        ROLLBACK TO cancel_start;
        
        LOG_TRANSFER_ACTIVITY(
            p_transfer_id => p_transfer_id,
            p_activity_type => 'VALIDATION_FAILED',
            p_description => 'فشل في إلغاء الحوالة - الحوالة غير منفذة أو بيانات ناقصة',
            p_error_message => v_error_msg,
            p_old_status => v_transfer_status,
            p_user_id => p_user_id,
            p_ip_address => p_ip_address,
            p_session_id => p_session_id
        );
        
        RAISE_APPLICATION_ERROR(-20002, v_error_msg);
        
    WHEN e_balance_insufficient THEN
        ROLLBACK TO cancel_start;
        
        LOG_TRANSFER_ACTIVITY(
            p_transfer_id => p_transfer_id,
            p_activity_type => 'VALIDATION_FAILED',
            p_description => 'فشل في إلغاء الحوالة - رصيد غير كافي',
            p_error_message => v_error_msg,
            p_user_id => p_user_id,
            p_ip_address => p_ip_address,
            p_session_id => p_session_id
        );
        
        RAISE_APPLICATION_ERROR(-20003, v_error_msg);
        
    WHEN OTHERS THEN
        ROLLBACK TO cancel_start;
        v_error_msg := 'خطأ غير متوقع في إلغاء الحوالة: ' || SQLERRM;
        
        LOG_TRANSFER_ACTIVITY(
            p_transfer_id => p_transfer_id,
            p_activity_type => 'ERROR',
            p_description => 'خطأ غير متوقع في إلغاء الحوالة',
            p_error_message => v_error_msg,
            p_user_id => p_user_id,
            p_ip_address => p_ip_address,
            p_session_id => p_session_id
        );
        
        RAISE_APPLICATION_ERROR(-20000, v_error_msg);
END;
/

-- إنشاء إجراء مساعد للتحقق من إمكانية الإلغاء
CREATE OR REPLACE FUNCTION CAN_CANCEL_TRANSFER(
    p_transfer_id IN NUMBER
) RETURN VARCHAR2 AS
    v_status VARCHAR2(50);
    v_executed_at DATE;
    v_days_since_execution NUMBER;
    v_has_subsequent_transactions NUMBER := 0;
    v_result VARCHAR2(4000);
BEGIN
    -- الحصول على معلومات الحوالة
    SELECT status, executed_at
    INTO v_status, v_executed_at
    FROM transfers
    WHERE id = p_transfer_id;
    
    -- التحقق من الحالة
    IF v_status != 'executed' THEN
        RETURN 'ERROR: الحوالة غير منفذة';
    END IF;
    
    -- حساب الأيام منذ التنفيذ
    v_days_since_execution := SYSDATE - v_executed_at;
    
    -- التحقق من وجود معاملات لاحقة للموردين
    SELECT COUNT(*) INTO v_has_subsequent_transactions
    FROM CURRENT_BALANCES cb
    JOIN transfer_supplier_distributions tsd ON cb.entity_id = tsd.supplier_id
    WHERE tsd.transfer_id = p_transfer_id
    AND cb.entity_type_code = 'SUPPLIER'
    AND cb.last_transaction_date > v_executed_at;
    
    -- تحديد إمكانية الإلغاء
    IF v_days_since_execution > 30 THEN
        v_result := 'WARNING: مرت أكثر من 30 يوماً على التنفيذ';
    ELSIF v_has_subsequent_transactions > 0 THEN
        v_result := 'WARNING: توجد معاملات لاحقة للموردين';
    ELSE
        v_result := 'OK: يمكن الإلغاء';
    END IF;
    
    RETURN v_result;
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN 'ERROR: الحوالة غير موجودة';
    WHEN OTHERS THEN
        RETURN 'ERROR: ' || SQLERRM;
END;
/

-- إنشاء comments للتوثيق
COMMENT ON PROCEDURE CANCEL_TRANSFER_ACCOUNTING IS 'إجراء إلغاء الحوالة وعكس جميع الترحيلات المحاسبية مع التكامل مع جدول CURRENT_BALANCES';
COMMENT ON FUNCTION CAN_CANCEL_TRANSFER IS 'دالة للتحقق من إمكانية إلغاء الحوالة وتحديد أي تحذيرات';

-- إضافة عمود سبب الإلغاء لجدول transfers إذا لم يكن موجوداً
BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE transfers ADD cancellation_reason VARCHAR2(500)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN -- Column already exists
            RAISE;
        END IF;
END;
/

-- عرض رسالة نجاح
SELECT 'تم إنشاء إجراء CANCEL_TRANSFER_ACCOUNTING بنجاح' as result FROM DUAL;
