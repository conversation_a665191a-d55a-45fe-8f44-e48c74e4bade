# -*- coding: utf-8 -*-
"""
مسارات الأتمتة الاحترافية
Professional Automation Routes
"""

from flask import render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app.shipments import shipments_bp
import sys
import os

# إضافة مسار الخدمات
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'services'))

@shipments_bp.route('/professional-automation')
@login_required
def professional_automation_dashboard():
    """لوحة الأتمتة الاحترافية الجديدة"""
    try:
        return render_template('shipments/professional_automation_dashboard.html')
    except Exception as e:
        flash(f'خطأ في تحميل لوحة الأتمتة الاحترافية: {str(e)}', 'error')
        return redirect(url_for('shipments.delivery_orders_dashboard'))

@shipments_bp.route('/professional-automation/start', methods=['POST'])
@login_required
def start_professional_automation():
    """بدء خدمة الأتمتة الاحترافية"""
    try:
        from professional_automation_service import start_automation_service
        
        success = start_automation_service()
        
        if success:
            flash('تم بدء خدمة الأتمتة الاحترافية بنجاح', 'success')
            return jsonify({
                'success': True,
                'message': 'تم بدء خدمة الأتمتة بنجاح'
            })
        else:
            flash('فشل في بدء خدمة الأتمتة الاحترافية', 'error')
            return jsonify({
                'success': False,
                'message': 'فشل في بدء خدمة الأتمتة'
            }), 500
            
    except Exception as e:
        flash(f'خطأ في بدء خدمة الأتمتة: {str(e)}', 'error')
        return jsonify({
            'success': False,
            'message': f'خطأ في بدء خدمة الأتمتة: {str(e)}'
        }), 500

@shipments_bp.route('/professional-automation/stop', methods=['POST'])
@login_required
def stop_professional_automation():
    """إيقاف خدمة الأتمتة الاحترافية"""
    try:
        from professional_automation_service import stop_automation_service
        
        success = stop_automation_service()
        
        if success:
            flash('تم إيقاف خدمة الأتمتة الاحترافية بنجاح', 'success')
            return jsonify({
                'success': True,
                'message': 'تم إيقاف خدمة الأتمتة بنجاح'
            })
        else:
            flash('فشل في إيقاف خدمة الأتمتة الاحترافية', 'error')
            return jsonify({
                'success': False,
                'message': 'فشل في إيقاف خدمة الأتمتة'
            }), 500
            
    except Exception as e:
        flash(f'خطأ في إيقاف خدمة الأتمتة: {str(e)}', 'error')
        return jsonify({
            'success': False,
            'message': f'خطأ في إيقاف خدمة الأتمتة: {str(e)}'
        }), 500

@shipments_bp.route('/professional-automation/status')
@login_required
def get_professional_automation_status():
    """الحصول على حالة خدمة الأتمتة الاحترافية"""
    try:
        from professional_automation_service import get_automation_status
        
        status = get_automation_status()
        
        return jsonify({
            'success': True,
            'status': status
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب حالة الأتمتة: {str(e)}',
            'status': None
        }), 500

@shipments_bp.route('/professional-automation/test', methods=['POST'])
@login_required
def test_professional_automation():
    """اختبار دورة الأتمتة الاحترافية"""
    try:
        from professional_automation_service import professional_automation_service
        
        # تشغيل دورة واحدة للاختبار
        professional_automation_service.process_automation_cycle()
        
        flash('تم تشغيل دورة اختبار الأتمتة بنجاح', 'success')
        return jsonify({
            'success': True,
            'message': 'تم تشغيل دورة اختبار الأتمتة بنجاح'
        })
        
    except Exception as e:
        flash(f'خطأ في اختبار الأتمتة: {str(e)}', 'error')
        return jsonify({
            'success': False,
            'message': f'خطأ في اختبار الأتمتة: {str(e)}'
        }), 500

@shipments_bp.route('/professional-automation/settings', methods=['GET', 'POST'])
@login_required
def professional_automation_settings():
    """إدارة إعدادات الأتمتة الاحترافية"""
    try:
        from professional_automation_service import professional_automation_service, update_automation_settings
        
        if request.method == 'GET':
            # جلب الإعدادات الحالية
            settings = professional_automation_service.settings
            
            return jsonify({
                'success': True,
                'settings': settings
            })
            
        elif request.method == 'POST':
            # تحديث الإعدادات
            data = request.get_json()
            
            if not data:
                return jsonify({
                    'success': False,
                    'message': 'لا توجد بيانات للتحديث'
                }), 400
            
            success = update_automation_settings(data)
            
            if success:
                flash('تم تحديث إعدادات الأتمتة بنجاح', 'success')
                return jsonify({
                    'success': True,
                    'message': 'تم تحديث إعدادات الأتمتة بنجاح'
                })
            else:
                flash('فشل في تحديث إعدادات الأتمتة', 'error')
                return jsonify({
                    'success': False,
                    'message': 'فشل في تحديث إعدادات الأتمتة'
                }), 500
                
    except Exception as e:
        flash(f'خطأ في إدارة إعدادات الأتمتة: {str(e)}', 'error')
        return jsonify({
            'success': False,
            'message': f'خطأ في إدارة إعدادات الأتمتة: {str(e)}'
        }), 500

@shipments_bp.route('/professional-automation/pending-shipments')
@login_required
def get_professional_pending_shipments():
    """الحصول على الشحنات المعلقة للأتمتة الاحترافية"""
    try:
        from professional_automation_service import professional_automation_service
        
        shipments = professional_automation_service.get_pending_shipments()
        
        return jsonify({
            'success': True,
            'shipments': shipments,
            'count': len(shipments)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب الشحنات المعلقة: {str(e)}',
            'shipments': [],
            'count': 0
        }), 500

@shipments_bp.route('/professional-automation/logs')
@login_required
def get_professional_automation_logs():
    """الحصول على سجلات الأتمتة الاحترافية"""
    try:
        log_file = os.path.join(os.path.dirname(__file__), '..', '..', 'logs', 'automation.log')
        
        if not os.path.exists(log_file):
            return jsonify({
                'success': True,
                'logs': [],
                'message': 'لا توجد سجلات متاحة'
            })
        
        # قراءة آخر 100 سطر من السجل
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            recent_logs = lines[-100:] if len(lines) > 100 else lines
        
        return jsonify({
            'success': True,
            'logs': [line.strip() for line in recent_logs],
            'total_lines': len(lines)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب السجلات: {str(e)}',
            'logs': []
        }), 500

@shipments_bp.route('/professional-automation/statistics')
@login_required
def get_professional_automation_statistics():
    """الحصول على إحصائيات الأتمتة الاحترافية"""
    try:
        from database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # إحصائيات أوامر التسليم المُنشأة تلقائياً اليوم (Oracle syntax)
        today_query = """
            SELECT COUNT(*) as today_orders
            FROM delivery_orders
            WHERE TRUNC(created_date) = TRUNC(SYSDATE)
        """

        # إحصائيات هذا الأسبوع (Oracle syntax)
        week_query = """
            SELECT COUNT(*) as week_orders
            FROM delivery_orders
            WHERE created_date >= SYSDATE - 7
        """

        # إحصائيات الشحنات المعلقة (تحقق من وجود الجدول أولاً)
        pending_query = """
            SELECT COUNT(*) as pending_shipments
            FROM cargo_shipments
            WHERE shipment_status IN ('arrived', 'customs_clearance', 'ready_for_delivery')
            AND id NOT IN (
                SELECT DISTINCT shipment_id
                FROM delivery_orders
                WHERE shipment_id IS NOT NULL
            )
        """
        
        today_result = db_manager.execute_query(today_query)
        week_result = db_manager.execute_query(week_query)
        pending_result = db_manager.execute_query(pending_query)
        
        statistics = {
            'today_orders': today_result[0][0] if today_result else 0,
            'week_orders': week_result[0][0] if week_result else 0,
            'pending_shipments': pending_result[0][0] if pending_result else 0
        }
        
        db_manager.close()
        
        return jsonify({
            'success': True,
            'statistics': statistics
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب إحصائيات الأتمتة: {str(e)}',
            'statistics': {}
        }), 500
