#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حل حقيقي لجلب البيانات من موقع COSCO باستخدام Selenium
"""

import time
import json
from datetime import datetime

def install_selenium():
    """تثبيت Selenium و ChromeDriver"""
    import subprocess
    import sys
    
    print("🔧 تثبيت Selenium...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "selenium", "webdriver-manager"])
        print("✅ تم تثبيت Selenium بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في تثبيت Selenium: {e}")
        return False

def get_real_cosco_data(booking_number):
    """جلب البيانات الحقيقية من موقع COSCO"""
    
    try:
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.support.ui import Select
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.service import Service
        
        print(f"🚀 بدء جلب البيانات الحقيقية لرقم: {booking_number}")
        print("=" * 60)
        
        # إعداد Chrome options
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # تثبيت ChromeDriver تلقائياً
        print("🔧 تحضير ChromeDriver...")
        service = Service(ChromeDriverManager().install())
        
        # إنشاء driver
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(30)
        
        try:
            print("📡 الوصول لموقع COSCO...")
            driver.get("https://elines.coscoshipping.com/ebusiness/cargoTracking")
            
            print("⏳ انتظار تحميل الصفحة...")
            time.sleep(5)
            
            print(f"📊 العنوان: {driver.title}")
            print(f"📊 URL الحالي: {driver.current_url}")
            
            # البحث عن dropdown لنوع البحث
            print("🔍 البحث عن نوع البحث...")
            try:
                # محاولة العثور على dropdown
                dropdown_selectors = [
                    "select",
                    "[class*='select']",
                    "[class*='dropdown']",
                    "select[name*='type']",
                    "select[name*='tracking']"
                ]
                
                dropdown = None
                for selector in dropdown_selectors:
                    try:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            dropdown = elements[0]
                            print(f"✅ وجد dropdown: {selector}")
                            break
                    except:
                        continue
                
                if dropdown:
                    # اختيار "Booking No"
                    select = Select(dropdown)
                    options = [option.text for option in select.options]
                    print(f"📋 خيارات متاحة: {options}")
                    
                    # البحث عن "Booking No" أو ما يشبهه
                    for option in select.options:
                        if 'booking' in option.text.lower() or 'b/l' in option.text.lower():
                            select.select_by_visible_text(option.text)
                            print(f"✅ تم اختيار: {option.text}")
                            break
                
            except Exception as e:
                print(f"⚠️ لم يتم العثور على dropdown: {e}")
            
            # البحث عن حقل الإدخال
            print("🔍 البحث عن حقل الإدخال...")
            
            input_selectors = [
                "input[name*='booking']",
                "input[name*='tracking']", 
                "input[name*='number']",
                "input[type='text']",
                "input[placeholder*='number']",
                "input[placeholder*='booking']"
            ]
            
            tracking_input = None
            for selector in input_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        tracking_input = elements[0]
                        print(f"✅ وجد حقل إدخال: {selector}")
                        break
                except:
                    continue
            
            if not tracking_input:
                print("❌ لم يتم العثور على حقل الإدخال")
                return None
            
            # إدخال رقم الحجز
            print(f"📝 إدخال رقم الحجز: {booking_number}")
            tracking_input.clear()
            tracking_input.send_keys(booking_number)
            time.sleep(2)
            
            # البحث عن زر البحث
            print("🔍 البحث عن زر البحث...")
            
            search_selectors = [
                "button[type='submit']",
                "input[type='submit']", 
                "button:contains('Search')",
                "button:contains('Track')",
                "button:contains('search')",
                ".search-btn",
                ".track-btn",
                "button[class*='search']",
                "button[class*='btn']"
            ]
            
            search_button = None
            for selector in search_selectors:
                try:
                    if ':contains(' in selector:
                        # استخدام XPath للنص
                        xpath = f"//button[contains(text(), 'Search') or contains(text(), 'search') or contains(text(), 'Track')]"
                        elements = driver.find_elements(By.XPATH, xpath)
                    else:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    if elements:
                        search_button = elements[0]
                        print(f"✅ وجد زر البحث: {selector}")
                        break
                except:
                    continue
            
            if not search_button:
                print("❌ لم يتم العثور على زر البحث")
                return None
            
            # الضغط على زر البحث
            print("🔍 الضغط على زر البحث...")
            driver.execute_script("arguments[0].click();", search_button)
            
            print("⏳ انتظار النتائج...")
            time.sleep(10)
            
            # تحليل النتائج
            print("📊 تحليل النتائج...")
            page_source = driver.page_source
            
            # حفظ النتائج للفحص
            with open(f'cosco_selenium_result_{booking_number}.html', 'w', encoding='utf-8') as f:
                f.write(page_source)
            print(f"💾 تم حفظ النتائج في cosco_selenium_result_{booking_number}.html")
            
            # استخراج البيانات
            result = extract_tracking_data(driver, page_source, booking_number)
            
            return result
            
        finally:
            driver.quit()
            
    except ImportError:
        print("❌ Selenium غير مثبت")
        if install_selenium():
            print("🔄 إعادة المحاولة...")
            return get_real_cosco_data(booking_number)
        else:
            return None
    except Exception as e:
        print(f"❌ خطأ في Selenium: {e}")
        return None

def extract_tracking_data(driver, html, booking_number):
    """استخراج بيانات التتبع من الصفحة"""
    try:
        print("🔍 استخراج البيانات...")
        
        # البحث عن عناصر البيانات
        data_elements = {
            'bl_number': None,
            'pol': None,
            'pod': None,
            'etd': None,
            'eta': None,
            'vessel': None,
            'status': None
        }
        
        # محاولة العثور على البيانات بطرق مختلفة
        try:
            # البحث عن جدول البيانات
            tables = driver.find_elements(By.TAG_NAME, "table")
            print(f"📋 وجد {len(tables)} جدول")
            
            for table in tables:
                table_text = table.text
                if booking_number in table_text:
                    print("✅ وجد جدول يحتوي على رقم الحجز")
                    print(f"📄 محتوى الجدول: {table_text[:500]}...")
                    
                    # استخراج البيانات من الجدول
                    rows = table.find_elements(By.TAG_NAME, "tr")
                    for row in rows:
                        row_text = row.text
                        if 'ETD' in row_text or 'ETA' in row_text:
                            print(f"📅 وجد تاريخ: {row_text}")
                        if 'POL' in row_text or 'POD' in row_text:
                            print(f"🏭 وجد ميناء: {row_text}")
                        if 'vessel' in row_text.lower():
                            print(f"🚢 وجد سفينة: {row_text}")
        
        except Exception as e:
            print(f"⚠️ خطأ في استخراج من الجداول: {e}")
        
        # البحث في النص العام
        import re
        
        # البحث عن تواريخ
        dates = re.findall(r'\d{4}-\d{2}-\d{2}', html)
        if dates:
            print(f"📅 وجد تواريخ: {dates}")
        
        # البحث عن أرقام B/L
        bl_matches = re.findall(r'B/L\s*No[:\s]*(\w+)', html, re.IGNORECASE)
        if bl_matches:
            print(f"📋 وجد B/L: {bl_matches}")
        
        # البحث عن حالة الشحنة
        status_keywords = ['departure', 'arrival', 'vessel', 'port', 'transit']
        for keyword in status_keywords:
            if keyword in html.lower():
                print(f"📊 وجد كلمة مفتاحية: {keyword}")
        
        # إذا وجدت بيانات، أرجعها
        if dates or bl_matches:
            return {
                'success': True,
                'data': {
                    'booking_number': booking_number,
                    'dates_found': dates,
                    'bl_numbers': bl_matches,
                    'source': 'SELENIUM_REAL'
                },
                'message': 'تم استخراج بيانات من موقع COSCO الحقيقي'
            }
        else:
            return {
                'success': False,
                'data': None,
                'message': f'لم يتم العثور على بيانات للرقم {booking_number}'
            }
        
    except Exception as e:
        return {
            'success': False,
            'data': None,
            'message': f'خطأ في استخراج البيانات: {str(e)}'
        }

def main():
    """الاختبار الرئيسي"""
    
    print("=" * 80)
    print("🧪 حل Selenium الحقيقي لموقع COSCO")
    print("=" * 80)
    
    booking_number = "6425375050"
    
    result = get_real_cosco_data(booking_number)
    
    print(f"\n" + "=" * 80)
    if result and result['success']:
        print("✅ نجح الحل الحقيقي!")
        print(f"📊 البيانات: {result['data']}")
        print(f"📝 الرسالة: {result['message']}")
    else:
        print("❌ فشل الحل الحقيقي")
        if result:
            print(f"📝 الرسالة: {result['message']}")
    print("=" * 80)

if __name__ == "__main__":
    main()
