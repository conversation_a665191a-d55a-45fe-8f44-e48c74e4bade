# -*- coding: utf-8 -*-
"""
خدمة تتبع مدفوعات الموردين
Supplier Payment Tracking Service
"""

import logging
from datetime import datetime, timedelta
from app.database_manager import DatabaseManager
from app.services.notification_service import NotificationService
import json

logger = logging.getLogger(__name__)

class SupplierPaymentTrackingService:
    """خدمة تتبع مدفوعات الموردين"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.notification_service = NotificationService()
    
    def track_payment_status_change(self, transfer_id, old_status, new_status, updated_by=None):
        """تتبع تغيير حالة المدفوعات"""
        try:
            # الحصول على تفاصيل الدفعة
            payment_query = """
            SELECT 
                spt.id, spt.supplier_id, spt.payment_amount, spt.currency_code,
                spt.payment_purpose, s.name_ar as supplier_name,
                tr.request_number, t.transfer_number
            FROM SUPPLIER_PAYMENT_TRANSFERS spt
            JOIN SUPPLIERS s ON spt.supplier_id = s.id
            LEFT JOIN TRANSFER_REQUESTS tr ON spt.transfer_request_id = tr.id
            LEFT JOIN TRANSFERS t ON spt.transfer_id = t.id
            WHERE spt.transfer_id = :1 OR spt.transfer_request_id = :1
            """
            
            payment_data = self.db.execute_query(payment_query, [transfer_id])
            
            if not payment_data:
                logger.warning(f"لم يتم العثور على دفعة للحوالة {transfer_id}")
                return False
            
            payment = payment_data[0]
            payment_id = payment[0]
            supplier_id = payment[1]
            amount = payment[2]
            currency_code = payment[3]
            purpose = payment[4]
            supplier_name = payment[5]
            request_number = payment[6]
            transfer_number = payment[7]
            
            # تسجيل تغيير الحالة
            self._log_status_change(payment_id, old_status, new_status, updated_by)
            
            # معالجة الحالة الجديدة
            if new_status == 'APPROVED':
                self._handle_payment_approved(payment_id, supplier_id, amount, currency_code)
            elif new_status == 'EXECUTED':
                self._handle_payment_executed(payment_id, supplier_id, amount, currency_code)
            elif new_status == 'COMPLETED':
                self._handle_payment_completed(payment_id, supplier_id, amount, currency_code)
            elif new_status == 'CANCELLED':
                self._handle_payment_cancelled(payment_id, supplier_id, amount, currency_code)
            
            # إرسال إشعارات
            self._send_status_change_notifications(
                supplier_name, request_number, transfer_number, 
                old_status, new_status, amount, currency_code, purpose
            )
            
            # تحديث أرصدة المورد
            self._update_supplier_balance(supplier_id, currency_code)
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تتبع تغيير حالة الدفعة: {e}")
            return False
    
    def _log_status_change(self, payment_id, old_status, new_status, updated_by):
        """تسجيل تغيير الحالة في سجل التتبع"""
        try:
            log_query = """
            INSERT INTO SUPPLIER_PAYMENT_STATUS_LOG (
                payment_id, old_status, new_status, change_date, 
                changed_by, notes
            ) VALUES (
                :1, :2, :3, CURRENT_TIMESTAMP, :4, :5
            )
            """
            
            notes = f"تم تغيير حالة الدفعة من {old_status} إلى {new_status}"
            
            self.db.execute_update(log_query, [
                payment_id, old_status, new_status, updated_by, notes
            ])
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل تغيير الحالة: {e}")
    
    def _handle_payment_approved(self, payment_id, supplier_id, amount, currency_code):
        """معالجة اعتماد الدفعة"""
        try:
            # تحديث تاريخ الاعتماد
            update_query = """
            UPDATE SUPPLIER_PAYMENT_TRANSFERS SET
                approved_date = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = :1
            """
            
            self.db.execute_update(update_query, [payment_id])
            
            # إنشاء معاملة في حساب المورد (حجز المبلغ)
            transaction_query = """
            INSERT INTO SUPPLIER_TRANSACTIONS (
                supplier_id, transaction_type, reference_type, reference_id,
                transaction_date, currency_code, original_amount, credit_amount,
                description, status, created_date
            ) VALUES (
                :1, 'PAYMENT_RESERVED', 'PAYMENT_TRANSFER', :2,
                CURRENT_TIMESTAMP, :3, :4, :5,
                'حجز مبلغ للدفع المعتمد', 'ACTIVE', CURRENT_TIMESTAMP
            )
            """
            
            self.db.execute_update(transaction_query, [
                supplier_id, payment_id, currency_code, amount, amount
            ])
            
        except Exception as e:
            logger.error(f"خطأ في معالجة اعتماد الدفعة: {e}")
    
    def _handle_payment_executed(self, payment_id, supplier_id, amount, currency_code):
        """معالجة تنفيذ الدفعة"""
        try:
            # تحديث تاريخ التنفيذ
            update_query = """
            UPDATE SUPPLIER_PAYMENT_TRANSFERS SET
                executed_date = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = :1
            """
            
            self.db.execute_update(update_query, [payment_id])
            
            # تحديث معاملة الحجز إلى معاملة دفع فعلية
            update_transaction_query = """
            UPDATE SUPPLIER_TRANSACTIONS SET
                transaction_type = 'PAYMENT_EXECUTED',
                description = 'دفعة منفذة عبر نظام الحوالات',
                updated_date = CURRENT_TIMESTAMP
            WHERE reference_type = 'PAYMENT_TRANSFER' 
            AND reference_id = :1
            AND transaction_type = 'PAYMENT_RESERVED'
            """
            
            self.db.execute_update(update_transaction_query, [payment_id])
            
        except Exception as e:
            logger.error(f"خطأ في معالجة تنفيذ الدفعة: {e}")
    
    def _handle_payment_completed(self, payment_id, supplier_id, amount, currency_code):
        """معالجة إكمال الدفعة"""
        try:
            # تحديث تاريخ الإكمال
            update_query = """
            UPDATE SUPPLIER_PAYMENT_TRANSFERS SET
                completed_date = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = :1
            """
            
            self.db.execute_update(update_query, [payment_id])
            
            # تحديث المعاملة إلى مكتملة
            update_transaction_query = """
            UPDATE SUPPLIER_TRANSACTIONS SET
                transaction_type = 'PAYMENT',
                description = 'دفعة مكتملة عبر نظام الحوالات',
                updated_date = CURRENT_TIMESTAMP
            WHERE reference_type = 'PAYMENT_TRANSFER' 
            AND reference_id = :1
            AND transaction_type = 'PAYMENT_EXECUTED'
            """
            
            self.db.execute_update(update_transaction_query, [payment_id])
            
            # تخصيص المدفوعات للفواتير المحددة
            self._allocate_payment_to_invoices(payment_id)
            
        except Exception as e:
            logger.error(f"خطأ في معالجة إكمال الدفعة: {e}")
    
    def _handle_payment_cancelled(self, payment_id, supplier_id, amount, currency_code):
        """معالجة إلغاء الدفعة"""
        try:
            # إلغاء المعاملات المرتبطة
            cancel_transaction_query = """
            UPDATE SUPPLIER_TRANSACTIONS SET
                status = 'CANCELLED',
                description = description || ' - تم الإلغاء',
                updated_date = CURRENT_TIMESTAMP
            WHERE reference_type = 'PAYMENT_TRANSFER' 
            AND reference_id = :1
            """
            
            self.db.execute_update(cancel_transaction_query, [payment_id])
            
        except Exception as e:
            logger.error(f"خطأ في معالجة إلغاء الدفعة: {e}")
    
    def _allocate_payment_to_invoices(self, payment_id):
        """تخصيص المدفوعات للفواتير المحددة"""
        try:
            # الحصول على تخصيصات الدفعة
            allocations_query = """
            SELECT supplier_transaction_id, allocated_amount, currency_code
            FROM SUPPLIER_PAYMENT_ALLOCATIONS
            WHERE supplier_payment_transfer_id = :1
            """
            
            allocations = self.db.execute_query(allocations_query, [payment_id])
            
            for allocation in allocations:
                transaction_id = allocation[0]
                allocated_amount = allocation[1]
                currency_code = allocation[2]
                
                # تحديث رصيد الفاتورة
                update_invoice_query = """
                UPDATE SUPPLIER_TRANSACTIONS SET
                    credit_amount = credit_amount + :1,
                    updated_date = CURRENT_TIMESTAMP
                WHERE transaction_id = :2
                """
                
                self.db.execute_update(update_invoice_query, [allocated_amount, transaction_id])
            
        except Exception as e:
            logger.error(f"خطأ في تخصيص المدفوعات للفواتير: {e}")
    
    def _update_supplier_balance(self, supplier_id, currency_code):
        """تحديث رصيد المورد"""
        try:
            # استدعاء الإجراء المخزن لتحديث الرصيد
            self.db.execute_procedure('UPDATE_SUPPLIER_BALANCE', [supplier_id, currency_code])
            
        except Exception as e:
            logger.error(f"خطأ في تحديث رصيد المورد: {e}")
    
    def _send_status_change_notifications(self, supplier_name, request_number, 
                                        transfer_number, old_status, new_status, 
                                        amount, currency_code, purpose):
        """إرسال إشعارات تغيير الحالة"""
        try:
            # تحديد نوع الإشعار حسب الحالة
            notification_type = self._get_notification_type(new_status)
            
            # إنشاء رسالة الإشعار
            message = self._create_notification_message(
                supplier_name, request_number, transfer_number,
                old_status, new_status, amount, currency_code, purpose
            )
            
            # إرسال الإشعار
            self.notification_service.send_notification(
                notification_type=notification_type,
                title=f"تحديث حالة دفعة المورد {supplier_name}",
                message=message,
                priority='normal' if new_status != 'CANCELLED' else 'high'
            )
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعارات تغيير الحالة: {e}")
    
    def _get_notification_type(self, status):
        """تحديد نوع الإشعار حسب الحالة"""
        notification_types = {
            'APPROVED': 'PAYMENT_APPROVED',
            'EXECUTED': 'PAYMENT_EXECUTED', 
            'COMPLETED': 'PAYMENT_COMPLETED',
            'CANCELLED': 'PAYMENT_CANCELLED'
        }
        return notification_types.get(status, 'PAYMENT_STATUS_CHANGE')
    
    def _create_notification_message(self, supplier_name, request_number, 
                                   transfer_number, old_status, new_status, 
                                   amount, currency_code, purpose):
        """إنشاء رسالة الإشعار"""
        status_names = {
            'PENDING': 'معلقة',
            'APPROVED': 'معتمدة',
            'EXECUTED': 'منفذة',
            'COMPLETED': 'مكتملة',
            'CANCELLED': 'ملغية'
        }
        
        old_status_ar = status_names.get(old_status, old_status)
        new_status_ar = status_names.get(new_status, new_status)
        
        message = f"""
        تم تحديث حالة دفعة المورد:
        
        المورد: {supplier_name}
        رقم الطلب: {request_number}
        رقم الحوالة: {transfer_number or 'لم يتم التنفيذ بعد'}
        المبلغ: {amount} {currency_code}
        الغرض: {purpose}
        
        الحالة السابقة: {old_status_ar}
        الحالة الجديدة: {new_status_ar}
        
        التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        return message.strip()
    
    def get_payment_tracking_history(self, payment_id):
        """الحصول على تاريخ تتبع الدفعة"""
        try:
            query = """
            SELECT 
                old_status, new_status, change_date, 
                u.full_name as changed_by_name, notes
            FROM SUPPLIER_PAYMENT_STATUS_LOG spsl
            LEFT JOIN USERS u ON spsl.changed_by = u.id
            WHERE spsl.payment_id = :1
            ORDER BY spsl.change_date DESC
            """
            
            results = self.db.execute_query(query, [payment_id])
            
            tracking_history = []
            for row in results:
                tracking_history.append({
                    'old_status': row[0],
                    'new_status': row[1],
                    'change_date': row[2].strftime('%Y-%m-%d %H:%M:%S') if row[2] else None,
                    'changed_by_name': row[3],
                    'notes': row[4]
                })
            
            return tracking_history
            
        except Exception as e:
            logger.error(f"خطأ في جلب تاريخ تتبع الدفعة: {e}")
            return []
    
    def get_overdue_payments_report(self, days_threshold=7):
        """تقرير المدفوعات المتأخرة"""
        try:
            query = """
            SELECT 
                spt.id, s.name_ar as supplier_name, tr.request_number,
                spt.payment_amount, spt.currency_code, spt.payment_status,
                spt.requested_date, 
                TRUNC(SYSDATE - spt.requested_date) as days_pending
            FROM SUPPLIER_PAYMENT_TRANSFERS spt
            JOIN SUPPLIERS s ON spt.supplier_id = s.id
            LEFT JOIN TRANSFER_REQUESTS tr ON spt.transfer_request_id = tr.id
            WHERE spt.payment_status IN ('PENDING', 'APPROVED')
            AND TRUNC(SYSDATE - spt.requested_date) >= :1
            ORDER BY TRUNC(SYSDATE - spt.requested_date) DESC
            """
            
            results = self.db.execute_query(query, [days_threshold])
            
            overdue_payments = []
            for row in results:
                overdue_payments.append({
                    'payment_id': row[0],
                    'supplier_name': row[1],
                    'request_number': row[2],
                    'payment_amount': float(row[3]) if row[3] else 0,
                    'currency_code': row[4],
                    'payment_status': row[5],
                    'requested_date': row[6].strftime('%Y-%m-%d') if row[6] else None,
                    'days_pending': int(row[7]) if row[7] else 0
                })
            
            return overdue_payments
            
        except Exception as e:
            logger.error(f"خطأ في جلب تقرير المدفوعات المتأخرة: {e}")
            return []

# إنشاء مثيل عام للخدمة
payment_tracking_service = SupplierPaymentTrackingService()
