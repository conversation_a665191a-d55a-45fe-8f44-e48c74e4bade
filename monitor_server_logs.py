#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقب logs الخادم في الوقت الفعلي
Real-time server logs monitor
"""

import time
import subprocess
import sys
import threading
from datetime import datetime

def monitor_logs():
    """مراقبة logs الخادم"""
    print("🔍 بدء مراقبة logs الخادم...")
    print("=" * 60)
    print("⚠️  الآن جرب تحديث طلب حوالة من الواجهة")
    print("=" * 60)
    
    try:
        # قراءة logs من terminal الخادم
        while True:
            time.sleep(1)
            
            # طباعة timestamp
            current_time = datetime.now().strftime("%H:%M:%S")
            print(f"\r[{current_time}] مراقبة نشطة... (اضغط Ctrl+C للإيقاف)", end="", flush=True)
            
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف المراقبة")

def print_instructions():
    """طباعة التعليمات"""
    print("📋 تعليمات الاختبار:")
    print("1. افتح المتصفح واذهب إلى: https://localhost:5000")
    print("2. سجل دخولك (admin/admin)")
    print("3. اذهب إلى الحوالات > قائمة الطلبات")
    print("4. اضغط على 'تعديل' لأي طلب")
    print("5. غير المبلغ أو أي بيانات أخرى")
    print("6. اضغط 'حفظ'")
    print("7. راقب logs هنا لترى ما يحدث")
    print("=" * 60)

if __name__ == "__main__":
    print_instructions()
    
    # بدء المراقبة
    try:
        monitor_logs()
    except Exception as e:
        print(f"\n❌ خطأ في المراقبة: {e}")
