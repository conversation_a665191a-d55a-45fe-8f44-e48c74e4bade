<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحسينات العقود</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .control-panel {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .search-box {
            border-radius: 25px;
            padding: 12px 20px;
            font-size: 14px;
        }
        
        .advanced-filters {
            margin-bottom: 20px;
            animation: slideDown 0.3s ease-out;
        }
        
        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .stats-info .badge {
            font-size: 0.85em;
            padding: 6px 10px;
        }
        
        .contract-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            background: white;
        }
        
        .contract-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .contract-card-header {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            background-color: #f8f9fa;
        }
        
        .contract-card-body {
            padding: 15px;
        }
        
        .contract-card-footer {
            padding: 10px 15px;
            border-top: 1px solid #dee2e6;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">اختبار تحسينات نظام العقود</h1>
        
        <!-- لوحة التحكم والتصفية -->
        <div class="control-panel">
            <!-- البحث السريع -->
            <div class="row mb-3">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" id="searchInput" class="search-box form-control border-0" 
                               placeholder="البحث برقم العقد، اسم المورد، أو المبلغ..." 
                               onkeyup="performQuickSearch()">
                        <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()" title="مسح البحث">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-outline-primary me-2" onclick="loadContracts()">
                        <i class="fas fa-sync-alt me-2"></i>تحديث
                    </button>
                    <button class="btn btn-outline-info" onclick="toggleAdvancedFilters()">
                        <i class="fas fa-filter me-2"></i>تصفية متقدمة
                    </button>
                </div>
            </div>

            <!-- المرشحات المتقدمة -->
            <div id="advancedFilters" class="advanced-filters" style="display: none;">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-sliders-h me-2"></i>التصفية المتقدمة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label class="form-label">حالة العقد</label>
                                <select class="form-select" id="statusFilter" onchange="applyFilters()">
                                    <option value="">جميع الحالات</option>
                                    <option value="ACTIVE">نشط</option>
                                    <option value="DRAFT">مسودة</option>
                                    <option value="EXPIRED">منتهي</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">حالة الاستخدام</label>
                                <select class="form-select" id="usageFilter" onchange="applyFilters()">
                                    <option value="">جميع الحالات</option>
                                    <option value="0">غير مستخدم</option>
                                    <option value="1">مستخدم</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">المبلغ من</label>
                                <input type="number" class="form-control" id="amountFromFilter" 
                                       placeholder="0.00" step="0.01" onchange="applyFilters()">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">المبلغ إلى</label>
                                <input type="number" class="form-control" id="amountToFilter" 
                                       placeholder="0.00" step="0.01" onchange="applyFilters()">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <button class="btn btn-outline-warning me-2" onclick="resetFilters()">
                                    <i class="fas fa-undo me-1"></i>إعادة تعيين
                                </button>
                                <button class="btn btn-outline-success" onclick="exportFilteredData()">
                                    <i class="fas fa-download me-1"></i>تصدير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="stats-info">
                            <span class="badge bg-primary me-2">
                                <i class="fas fa-list me-1"></i>
                                إجمالي: <span id="totalCount">5</span>
                            </span>
                            <span class="badge bg-success me-2">
                                <i class="fas fa-check me-1"></i>
                                نشط: <span id="activeCount">3</span>
                            </span>
                            <span class="badge bg-warning me-2">
                                <i class="fas fa-clock me-1"></i>
                                مسودة: <span id="draftCount">1</span>
                            </span>
                            <span class="badge bg-info">
                                <i class="fas fa-eye me-1"></i>
                                معروض: <span id="visibleCount">5</span>
                            </span>
                        </div>
                        <div class="view-options">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-secondary btn-sm active" onclick="changeView('table')" id="tableViewBtn">
                                    <i class="fas fa-table"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeView('cards')" id="cardsViewBtn">
                                    <i class="fas fa-th-large"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- عرض الجدول -->
        <div id="tableView" class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>رقم العقد</th>
                        <th>تاريخ العقد</th>
                        <th>اسم المورد</th>
                        <th>المبلغ</th>
                        <th>الحالة</th>
                        <th>الاستخدام</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>CON-2024-001</td>
                        <td>2024-01-15</td>
                        <td>شركة الخليج للتجارة</td>
                        <td>150,000.00 ريال</td>
                        <td><span class="badge bg-success">نشط</span></td>
                        <td><span class="badge bg-success">مستخدم</span></td>
                    </tr>
                    <tr>
                        <td>CON-2024-002</td>
                        <td>2024-02-20</td>
                        <td>مؤسسة النور للمقاولات</td>
                        <td>75,500.00 ريال</td>
                        <td><span class="badge bg-warning">مسودة</span></td>
                        <td><span class="badge bg-secondary">غير مستخدم</span></td>
                    </tr>
                    <tr>
                        <td>CON-2024-003</td>
                        <td>2024-03-10</td>
                        <td>شركة الرياض للخدمات</td>
                        <td>200,000.00 ريال</td>
                        <td><span class="badge bg-success">نشط</span></td>
                        <td><span class="badge bg-success">مستخدم</span></td>
                    </tr>
                    <tr>
                        <td>CON-2024-004</td>
                        <td>2024-04-05</td>
                        <td>مجموعة الشرق الأوسط</td>
                        <td>50,000.00 ريال</td>
                        <td><span class="badge bg-success">نشط</span></td>
                        <td><span class="badge bg-secondary">غير مستخدم</span></td>
                    </tr>
                    <tr>
                        <td>CON-2024-005</td>
                        <td>2024-05-12</td>
                        <td>شركة الأمل للتطوير</td>
                        <td>300,000.00 ريال</td>
                        <td><span class="badge bg-danger">منتهي</span></td>
                        <td><span class="badge bg-success">مستخدم</span></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- عرض البطاقات -->
        <div id="cardsView" style="display: none;">
            <div class="row" id="cardsContainer">
                <!-- سيتم ملء البطاقات بـ JavaScript -->
            </div>
        </div>

        <!-- نتائج الاختبار -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-clipboard-check me-2"></i>نتائج الاختبار</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <p class="text-muted">قم بتجربة البحث والتصفية لرؤية النتائج</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // متغيرات عامة
        let allContracts = [];
        let filteredContracts = [];
        let currentView = 'table';

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeFilters();
            updateStatistics();
        });

        // تهيئة المرشحات
        function initializeFilters() {
            collectAllContracts();
            console.log('✅ تم تهيئة نظام البحث والتصفية');
        }

        // جمع جميع العقود من الجدول
        function collectAllContracts() {
            allContracts = [];
            const rows = document.querySelectorAll('#tableView tbody tr');
            
            rows.forEach((row, index) => {
                const cells = row.querySelectorAll('td');
                
                const contract = {
                    element: row,
                    index: index,
                    contractNumber: cells[0].textContent.trim(),
                    contractDate: cells[1].textContent.trim(),
                    supplierName: cells[2].textContent.trim(),
                    amount: cells[3].textContent.trim(),
                    status: cells[4].textContent.trim(),
                    usage: cells[5].textContent.trim(),
                    searchText: row.textContent.toLowerCase()
                };
                
                allContracts.push(contract);
            });
            
            filteredContracts = [...allContracts];
            console.log(`📊 تم جمع ${allContracts.length} عقد`);
        }

        // باقي الدوال ستكون مماثلة للكود الأصلي...
        // (تم اختصارها لتوفير المساحة)
        
        function performQuickSearch() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
            
            if (searchTerm === '') {
                filteredContracts = [...allContracts];
            } else {
                filteredContracts = allContracts.filter(contract => {
                    return contract.searchText.includes(searchTerm);
                });
            }
            
            updateDisplay();
            updateTestResults(`البحث عن "${searchTerm}": عرض ${filteredContracts.length} من ${allContracts.length} عقد`);
        }

        function clearSearch() {
            document.getElementById('searchInput').value = '';
            performQuickSearch();
        }

        function toggleAdvancedFilters() {
            const filtersDiv = document.getElementById('advancedFilters');
            const isVisible = filtersDiv.style.display !== 'none';
            filtersDiv.style.display = isVisible ? 'none' : 'block';
        }

        function updateDisplay() {
            if (currentView === 'table') {
                allContracts.forEach(contract => {
                    contract.element.style.display = 'none';
                });
                
                filteredContracts.forEach(contract => {
                    contract.element.style.display = '';
                });
            }
            
            updateStatistics();
        }

        function updateStatistics() {
            document.getElementById('visibleCount').textContent = filteredContracts.length;
        }

        function changeView(view) {
            currentView = view;
            
            const tableView = document.getElementById('tableView');
            const cardsView = document.getElementById('cardsView');
            const tableBtn = document.getElementById('tableViewBtn');
            const cardsBtn = document.getElementById('cardsViewBtn');
            
            if (view === 'table') {
                tableView.style.display = 'block';
                cardsView.style.display = 'none';
                tableBtn.classList.add('active');
                cardsBtn.classList.remove('active');
            } else {
                tableView.style.display = 'none';
                cardsView.style.display = 'block';
                tableBtn.classList.remove('active');
                cardsBtn.classList.add('active');
            }
            
            updateTestResults(`تم التبديل إلى وضع ${view === 'table' ? 'الجدول' : 'البطاقات'}`);
        }

        function updateTestResults(message) {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            resultsDiv.innerHTML += `<div class="alert alert-info">[${timestamp}] ${message}</div>`;
        }

        // دوال أخرى
        function loadContracts() { updateTestResults('تم تحديث العقود'); }
        function applyFilters() { updateTestResults('تم تطبيق المرشحات'); }
        function resetFilters() { updateTestResults('تم إعادة تعيين المرشحات'); }
        function exportFilteredData() { updateTestResults('تم تصدير البيانات'); }
    </script>
</body>
</html>
