{% extends "base.html" %}

{% block title %}لوحة تتبع الأصناف{% endblock %}

{% block head %}
<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-chart-line me-2"></i>
                        لوحة تتبع الأصناف
                    </h2>
                    <p class="text-muted mb-0">مراقبة شاملة لجميع الأصناف عبر كافة الشحنات</p>
                    <small class="text-info">
                        <i class="fas fa-info-circle me-1"></i>
                        حالة الأصناف تتحدث تلقائياً مع حالة الشحنة
                    </small>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt me-1"></i>
                        تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات عامة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        الإحصائيات العامة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if general_stats %}
                        {% for stat in general_stats %}
                        <div class="col-lg-2 col-md-3 col-sm-6 mb-3">
                            <div class="stat-card clickable" 
                                 style="border-left: 4px solid {{ stat[1] }};"
                                 onclick="filterByStatus('{{ stat[0] }}', '{{ stat[1] }}')">
                                <div class="stat-icon" style="color: {{ stat[1] }};">
                                    <i class="{{ stat[2] }}"></i>
                                </div>
                                <div class="stat-details">
                                    <h4>{{ stat[3] }}</h4>
                                    <p>{{ stat[0] }}</p>
                                    <small>{{ stat[4] }}%</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        {% else %}
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                لا توجد بيانات إحصائية متاحة حالياً
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الشحنات -->
    {% if shipments_stats %}
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="info-card bg-primary">
                <div class="info-icon">
                    <i class="fas fa-ship"></i>
                </div>
                <div class="info-details">
                    <h3>{{ shipments_stats[0] }}</h3>
                    <p>إجمالي الشحنات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="info-card bg-success">
                <div class="info-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="info-details">
                    <h3>{{ shipments_stats[1] }}</h3>
                    <p>شحنات مكتملة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="info-card bg-warning">
                <div class="info-icon">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="info-details">
                    <h3>{{ shipments_stats[2] }}</h3>
                    <p>شحنات نشطة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="info-card bg-danger">
                <div class="info-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="info-details">
                    <h3>{{ shipments_stats[3] }}</h3>
                    <p>شحنات بها تلف</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- أدوات البحث والفلترة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-search me-2"></i>
                        البحث والفلترة
                        <span class="filter-count text-muted small"></span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">البحث في الأصناف</label>
                            <input type="text" class="form-control" id="searchInput"
                                   placeholder="اسم الصنف أو رقم الشحنة..."
                                   oninput="applyFilters()" onkeyup="applyFilters()">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">فلترة بالحالة</label>
                            <select class="form-select" id="statusFilter" onchange="applyFilters()">
                                <option value="">جميع الحالات</option>
                                {% for status in available_statuses %}
                                <option value="{{ status[0] }}">{{ status[1] }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">رقم الشحنة</label>
                            <input type="text" class="form-control" id="shipmentFilter"
                                   placeholder="رقم الشحنة..."
                                   oninput="applyFilters()" onkeyup="applyFilters()">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="searchItems()">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="resetFilters()">
                                    <i class="fas fa-undo me-1"></i>
                                    إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نتائج البحث / الأصناف الحديثة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        <span id="itemsTitle">الأصناف الحديثة</span>
                        <span class="badge bg-secondary ms-2" id="itemsCount">{{ recent_items|length }}</span>
                    </h5>
                    <button class="btn btn-sm btn-outline-secondary" onclick="clearSearch()">
                        <i class="fas fa-times me-1"></i>
                        مسح البحث
                    </button>
                </div>
                <div class="card-body">
                    <div id="itemsContainer">
                        {% if recent_items %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم الصنف</th>
                                        <th>الكمية</th>
                                        <th>الشحنة</th>
                                        <th>رقم الحاوية</th>
                                        <th>اسم المستلم</th>
                                        <th>الحالة</th>
                                        <th>الموقع</th>
                                        <th>آخر تحديث</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="itemsTableBody">
                                    {% for item in recent_items %}
                                    <tr {% if item[7] %}class="table-danger"{% endif %}>
                                        <td>
                                            <strong>{{ item[1] }}</strong>
                                            {% if item[7] %}
                                            <br><small class="text-danger">
                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                تالف
                                            </small>
                                            {% endif %}
                                        </td>
                                        <td>{{ item[2] }} {{ item[3] or '' }}</td>
                                        <td>
                                            <a href="{{ url_for('shipments.items_tracking', shipment_id=item[9]) }}"
                                               class="text-decoration-none">
                                                {{ item[8] }}
                                            </a>
                                            {% if item[13] %}
                                            <br><small class="text-muted">أمر شراء: {{ item[13] }}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if item[14] %}
                                            <span class="badge bg-info">{{ item[14] }}</span>
                                            {% else %}
                                            <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if item[16] and item[16] != 'غير محدد' %}
                                            <span class="badge bg-secondary">{{ item[16] }}</span>
                                            {% else %}
                                            <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge" style="background-color: {{ item[19] }};">
                                                <i class="{{ item[20] }} me-1"></i>
                                                {{ item[18] }}
                                            </span>
                                        </td>
                                        <td>{{ item[5] or 'غير محدد' }}</td>
                                        <td>
                                            {% if item[6] %}
                                            {{ item[6].strftime('%Y-%m-%d %H:%M') }}
                                            {% else %}
                                            -
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary"
                                                        onclick="updateItemStatus({{ item[0] }})"
                                                        title="تحديث الحالة">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-info"
                                                        onclick="viewItemHistory({{ item[0] }})"
                                                        title="عرض التاريخ">
                                                    <i class="fas fa-history"></i>
                                                </button>
                                                <a href="{{ url_for('shipments.items_tracking', shipment_id=item[9]) }}"
                                                   class="btn btn-outline-secondary"
                                                   title="عرض الشحنة">
                                                    <i class="fas fa-ship"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <h5>لا توجد أصناف</h5>
                            <p class="text-muted">لم يتم العثور على أي أصناف</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الأصناف التالفة -->
    {% if damaged_items %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        الأصناف التالفة
                        <span class="badge bg-light text-danger ms-2">{{ damaged_items|length }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>اسم الصنف</th>
                                    <th>الشحنة</th>
                                    <th>وصف التلف</th>
                                    <th>تاريخ التلف</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in damaged_items %}
                                <tr>
                                    <td><strong>{{ item[1] }}</strong></td>
                                    <td>
                                        <a href="{{ url_for('shipments.items_tracking', shipment_id=item[5]) }}" 
                                           class="text-decoration-none">
                                            {{ item[4] }}
                                        </a>
                                    </td>
                                    <td>{{ item[2] or 'غير محدد' }}</td>
                                    <td>
                                        {% if item[3] %}
                                        {{ item[3].strftime('%Y-%m-%d %H:%M') }}
                                        {% else %}
                                        -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('shipments.items_tracking', shipment_id=item[5]) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye me-1"></i>
                                            عرض
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- تم استبدال النوافذ بـ SweetAlert2 لحل مشكلة التجميد -->

<style>
.stat-card {
    background: white;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.stat-card.clickable {
    cursor: pointer;
}

.stat-card.clickable:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.stat-icon {
    font-size: 2rem;
    width: 60px;
    text-align: center;
}

.stat-details h4 {
    margin: 0;
    font-weight: 700;
}

.stat-details p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.stat-details small {
    color: #28a745;
    font-weight: 600;
}

.info-card {
    background: linear-gradient(135deg, var(--bs-primary), var(--bs-primary-dark, #0056b3));
    color: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-card.bg-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.info-card.bg-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.info-card.bg-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.info-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.info-details h3 {
    margin: 0;
    font-weight: 700;
    font-size: 2rem;
}

.info-details p {
    margin: 0;
    opacity: 0.9;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,0.075);
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

.timeline-content {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 3px solid #dee2e6;
}

.timeline-content h6 {
    margin-bottom: 0.5rem;
    color: #495057;
}

/* إصلاح النوافذ المنبثقة */
.modal {
    z-index: 1050;
}

.modal-backdrop {
    z-index: 1040;
}

.modal.show {
    display: block !important;
}

.modal-open {
    overflow: hidden;
}

/* تحسين أزرار الإغلاق */
.btn-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    opacity: 0.5;
}

.btn-close:hover {
    opacity: 1;
}

/* تحسين النوافذ للجوال */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }
}
</style>

<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
let currentItemId = null;

function refreshDashboard() {
    location.reload();
}

function filterByStatus(statusName, statusColor) {
    console.log('🔍 فلترة بالحالة:', statusName);

    // تعيين قيمة الفلتر
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        // البحث عن القيمة المطابقة في الخيارات
        for (let option of statusFilter.options) {
            if (option.text === statusName || option.value === statusName) {
                statusFilter.value = option.value;
                break;
            }
        }

        // تطبيق الفلترة
        applyFilters();
    }
}

// وظيفة تطبيق الفلاتر تلقائياً
function applyFilters() {
    // تأخير قصير لتحسين الأداء
    clearTimeout(window.filterTimeout);
    window.filterTimeout = setTimeout(() => {
        searchItems();
    }, 300);
}

// وظيفة إعادة تعيين الفلاتر
function resetFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('shipmentFilter').value = '';

    // إعادة تحميل البيانات الأصلية
    location.reload();
}

function searchItems() {
    const searchTerm = document.getElementById('searchInput').value.trim();
    const statusFilter = document.getElementById('statusFilter').value;
    const shipmentFilter = document.getElementById('shipmentFilter').value.trim();

    console.log('🔍 بدء البحث:', {
        searchTerm: searchTerm,
        statusFilter: statusFilter,
        shipmentFilter: shipmentFilter
    });

    // إظهار مؤشر التحميل
    const searchBtn = document.querySelector('button[onclick="searchItems()"]');
    const originalText = searchBtn.innerHTML;
    searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري البحث...';
    searchBtn.disabled = true;

    const params = new URLSearchParams();
    if (searchTerm) params.append('q', searchTerm);
    if (statusFilter) params.append('status', statusFilter);
    if (shipmentFilter) params.append('shipment', shipmentFilter);

    fetch(`/shipments/api/items-search?${params.toString()}`)
    .then(response => response.json())
    .then(data => {
        console.log('📊 نتائج البحث:', data);

        if (data.success) {
            displaySearchResults(data.items, data.count);

            // إظهار رسالة النجاح
            if (data.count === 0) {
                showMessage('لا توجد نتائج تطابق معايير البحث', 'warning');
            } else {
                showMessage(`تم العثور على ${data.count} عنصر`, 'success');
            }
        } else {
            console.error('❌ خطأ في البحث:', data.message);
            showMessage('خطأ في البحث: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الشبكة:', error);
        showMessage('حدث خطأ في الاتصال بالخادم', 'danger');
    })
    .finally(() => {
        // إعادة تعيين زر البحث
        searchBtn.innerHTML = originalText;
        searchBtn.disabled = false;
    });
}

// وظيفة إظهار الرسائل
function showMessage(message, type = 'info') {
    // إزالة الرسائل السابقة
    const existingAlerts = document.querySelectorAll('.search-alert');
    existingAlerts.forEach(alert => alert.remove());

    // إنشاء رسالة جديدة
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show search-alert`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إضافة الرسالة قبل الجدول
    const tableCard = document.querySelector('.card:has(#itemsTable)');
    if (tableCard) {
        tableCard.parentNode.insertBefore(alertDiv, tableCard);

        // إزالة الرسالة تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

function displaySearchResults(items, count) {
    console.log('📋 عرض النتائج:', count, 'عنصر');

    // تحديث العنوان والعدد
    const titleElement = document.getElementById('itemsTitle');
    const countElement = document.getElementById('itemsCount');

    if (titleElement) {
        titleElement.textContent = count > 0 ? 'نتائج البحث' : 'لا توجد نتائج';
    }

    if (countElement) {
        countElement.textContent = count;
        countElement.className = count > 0 ? 'badge bg-success ms-2' : 'badge bg-warning ms-2';
    }

    const tbody = document.getElementById('itemsTableBody');
    if (!tbody) {
        console.error('❌ لم يتم العثور على جدول النتائج');
        return;
    }

    tbody.innerHTML = '';

    if (count === 0) {
        // عرض رسالة عدم وجود نتائج
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <h5>لا توجد أصناف تطابق معايير البحث</h5>
                        <p>جرب تغيير معايير البحث أو إعادة تعيين الفلاتر</p>
                        <button class="btn btn-outline-primary" onclick="resetFilters()">
                            <i class="fas fa-undo me-1"></i>
                            إعادة تعيين الفلاتر
                        </button>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    
    if (items.length === 0) {
        document.getElementById('itemsContainer').innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5>لا توجد نتائج</h5>
                <p class="text-muted">لم يتم العثور على أي أصناف تطابق البحث</p>
            </div>
        `;
        return;
    }
    
    items.forEach(item => {
        const row = document.createElement('tr');
        if (item.is_damaged) row.className = 'table-danger';

        row.innerHTML = `
            <td>
                <strong>${item.item_name}</strong>
                ${item.is_damaged ? '<br><small class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>تالف</small>' : ''}
            </td>
            <td>${item.quantity} ${item.unit || ''}</td>
            <td>
                <a href="/shipments/cargo/${item.shipment_id}/items-tracking" class="text-decoration-none">
                    ${item.shipment_number}
                </a>
            </td>
            <td>
                ${item.container_number && item.container_number !== 'غير محدد' ?
                    `<span class="badge bg-info">${item.container_number}</span>` :
                    '<span class="text-muted">غير محدد</span>'}
            </td>
            <td>
                ${item.recipient_name && item.recipient_name !== 'غير محدد' ?
                    `<span class="badge bg-secondary">${item.recipient_name}</span>` :
                    '<span class="text-muted">غير محدد</span>'}
            </td>
            <td>
                <span class="badge" style="background-color: ${item.shipment_status_color};">
                    <i class="${item.shipment_status_icon} me-1"></i>
                    ${item.shipment_status_name}
                </span>
            </td>
            <td>${item.item_location || 'غير محدد'}</td>
            <td>${item.status_updated_at || '-'}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="updateItemStatus(${item.id})" title="تحديث الحالة">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="viewItemHistory(${item.id})" title="عرض التاريخ">
                        <i class="fas fa-history"></i>
                    </button>
                    <a href="/shipments/cargo/${item.shipment_id}/items-tracking" class="btn btn-outline-secondary" title="عرض الشحنة">
                        <i class="fas fa-ship"></i>
                    </a>
                </div>
            </td>
        `;

        tbody.appendChild(row);
    });
}

function clearSearch() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('shipmentFilter').value = '';
    location.reload();
}

function updateItemStatus(itemId) {
    currentItemId = itemId;

    // إنشاء خيارات الحالات
    const statusOptions = {};
    {% for status in available_statuses %}
    statusOptions['{{ status[0] }}'] = '{{ status[1] }}';
    {% endfor %}

    Swal.fire({
        title: 'تحديث حالة الصنف',
        html: `
            <div class="text-start">
                <div class="mb-3">
                    <label class="form-label">الحالة الجديدة *</label>
                    <select class="form-select" id="swal-status" required>
                        <option value="">اختر الحالة</option>
                        {% for status in available_statuses %}
                        <option value="{{ status[0] }}">{{ status[1] }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">الموقع (اختياري)</label>
                    <input type="text" class="form-control" id="swal-location" placeholder="أدخل الموقع الحالي">
                </div>
                <div class="mb-3">
                    <label class="form-label">ملاحظات (اختياري)</label>
                    <textarea class="form-control" id="swal-notes" rows="3" placeholder="أدخل أي ملاحظات"></textarea>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'تحديث',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#007bff',
        cancelButtonColor: '#6c757d',
        width: '500px',
        preConfirm: () => {
            const status = document.getElementById('swal-status').value;
            const location = document.getElementById('swal-location').value;
            const notes = document.getElementById('swal-notes').value;

            if (!status) {
                Swal.showValidationMessage('يرجى اختيار الحالة الجديدة');
                return false;
            }

            return {
                status: status,
                location: location,
                notes: notes
            };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            submitStatusUpdateSwal(itemId, result.value);
        }
    });
}

function submitStatusUpdateSwal(itemId, data) {
    // إظهار مؤشر التحميل
    Swal.fire({
        title: 'جاري التحديث...',
        text: 'يرجى الانتظار',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    fetch(`/shipments/api/item/${itemId}/update-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            status: data.status,
            location: data.location,
            notes: data.notes
        })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Swal.fire({
                icon: 'success',
                title: 'تم بنجاح!',
                text: 'تم تحديث حالة الصنف بنجاح',
                confirmButtonText: 'موافق',
                confirmButtonColor: '#28a745'
            }).then(() => {
                location.reload();
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: result.message || 'فشل في تحديث حالة الصنف',
                confirmButtonText: 'موافق',
                confirmButtonColor: '#dc3545'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ في الاتصال!',
            text: 'حدث خطأ في تحديث حالة الصنف',
            confirmButtonText: 'موافق',
            confirmButtonColor: '#dc3545'
        });
    });
}

// تم استبدال النوافذ بـ SweetAlert2 - لا حاجة لدالة إغلاق النافذة

function viewItemHistory(itemId) {
    // إظهار مؤشر التحميل
    Swal.fire({
        title: 'جاري تحميل التاريخ...',
        text: 'يرجى الانتظار',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    // جلب التاريخ
    fetch(`/shipments/api/item/${itemId}/history`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayHistorySwal(data.history);
        } else {
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: 'خطأ في تحميل التاريخ',
                confirmButtonText: 'موافق',
                confirmButtonColor: '#dc3545'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ في الاتصال!',
            text: 'حدث خطأ في تحميل التاريخ',
            confirmButtonText: 'موافق',
            confirmButtonColor: '#dc3545'
        });
    });
}

function displayHistorySwal(history) {
    let html = '';

    if (history.length === 0) {
        html = '<div class="alert alert-info">لا يوجد تاريخ لهذا الصنف</div>';
    } else {
        html = '<div class="timeline" style="max-height: 400px; overflow-y: auto;">';
        history.forEach(item => {
            html += `
                <div class="timeline-item">
                    <div class="timeline-marker" style="background-color: ${item.new_status_color};">
                        <i class="${item.new_status_icon}"></i>
                    </div>
                    <div class="timeline-content">
                        <h6>${item.new_status_name}</h6>
                        <p class="text-muted mb-1">${new Date(item.status_date).toLocaleString('ar-SA')}</p>
                        ${item.location ? `<p><i class="fas fa-map-marker-alt me-1"></i>${item.location}</p>` : ''}
                        ${item.notes ? `<p>${item.notes}</p>` : ''}
                        ${item.auto_updated ? '<small class="badge bg-info">تحديث تلقائي</small>' : '<small class="badge bg-primary">تحديث يدوي</small>'}
                    </div>
                </div>
            `;
        });
        html += '</div>';
    }

    Swal.fire({
        title: 'تاريخ حالات الصنف',
        html: html,
        width: '600px',
        confirmButtonText: 'إغلاق',
        confirmButtonColor: '#007bff',
        customClass: {
            htmlContainer: 'text-start'
        }
    });
}

// البحث عند الضغط على Enter
document.getElementById('searchInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        searchItems();
    }
});

document.getElementById('shipmentFilter').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        searchItems();
    }
});

// SweetAlert2 يتعامل مع إغلاق النوافذ تلقائياً

// 🚀 تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    console.log('📦 تم تحميل لوحة تتبع الأصناف');

    // تفعيل الفلترة التلقائية
    const statusFilter = document.getElementById('statusFilter');
    const searchInput = document.getElementById('searchInput');
    const shipmentFilter = document.getElementById('shipmentFilter');

    // إضافة event listeners إضافية للتأكد
    if (statusFilter) {
        statusFilter.addEventListener('change', applyFilters);
        console.log('✅ تم تفعيل فلترة الحالة');
    }

    if (searchInput) {
        searchInput.addEventListener('input', applyFilters);
        console.log('✅ تم تفعيل البحث النصي');
    }

    if (shipmentFilter) {
        shipmentFilter.addEventListener('input', applyFilters);
        console.log('✅ تم تفعيل فلترة الشحنة');
    }

    // إضافة مؤشر بصري للفلاتر النشطة
    function updateFilterIndicators() {
        const filters = [statusFilter, searchInput, shipmentFilter];
        let activeFilters = 0;

        filters.forEach(filter => {
            if (filter && filter.value.trim()) {
                filter.style.borderColor = '#28a745';
                filter.style.boxShadow = '0 0 0 0.2rem rgba(40, 167, 69, 0.25)';
                activeFilters++;
            } else if (filter) {
                filter.style.borderColor = '';
                filter.style.boxShadow = '';
            }
        });

        // تحديث عداد الفلاتر النشطة
        const filterCount = document.querySelector('.filter-count');
        if (filterCount) {
            filterCount.textContent = activeFilters > 0 ? `(${activeFilters} فلتر نشط)` : '';
        }
    }

    // تطبيق مؤشرات الفلاتر عند التغيير
    [statusFilter, searchInput, shipmentFilter].forEach(filter => {
        if (filter) {
            filter.addEventListener('input', updateFilterIndicators);
            filter.addEventListener('change', updateFilterIndicators);
        }
    });

    console.log('🎉 تم تهيئة جميع الفلاتر بنجاح');
});
</script>
{% endblock %}
