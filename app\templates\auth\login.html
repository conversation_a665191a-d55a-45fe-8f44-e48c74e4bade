<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام ERP</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON>wal', sans-serif;
            background: #0f0f23;
            color: white;
            overflow: hidden;
            height: 100vh;
        }

        /* خلفية نيون متحركة */
        .neon-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, #120458 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, #ff006e 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, #8338ec 0%, transparent 50%),
                linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            z-index: -2;
        }

        .neon-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #ff006e;
            border-radius: 50%;
            animation: float 8s infinite linear;
            box-shadow: 0 0 10px #ff006e;
        }

        @keyframes float {
            0% { transform: translateY(100vh) translateX(0); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100px) translateX(100px); opacity: 0; }
        }

        /* الحاوي الرئيسي */
        .cyber-login {
            display: flex;
            min-height: 100vh;
            position: relative;
        }

        /* الجانب الأيسر - النموذج */
        .login-panel {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            position: relative;
        }

        .login-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        .login-box {
            position: relative;
            z-index: 2;
            width: 100%;
            max-width: 400px;
            background: rgba(15, 15, 35, 0.9);
            border: 1px solid #ff006e;
            border-radius: 15px;
            padding: 2rem;
            box-shadow:
                0 0 30px rgba(255, 0, 110, 0.3),
                inset 0 0 30px rgba(255, 0, 110, 0.1);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 30px rgba(255, 0, 110, 0.3), inset 0 0 30px rgba(255, 0, 110, 0.1); }
            to { box-shadow: 0 0 50px rgba(255, 0, 110, 0.5), inset 0 0 50px rgba(255, 0, 110, 0.2); }
        }

        /* الشعار */
        .cyber-logo {
            text-align: center;
            margin-bottom: 2rem;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
            background: linear-gradient(45deg, #ff006e, #8338ec);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            animation: rotate 4s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(45deg, #ff006e, #8338ec, #ff006e);
            border-radius: 50%;
            z-index: -1;
            animation: rotate 4s linear infinite reverse;
        }

        .logo-icon i {
            font-size: 2rem;
            color: white;
            z-index: 2;
        }

        .cyber-title {
            font-size: 2.5rem;
            font-weight: 900;
            background: linear-gradient(45deg, #ff006e, #8338ec);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            margin-bottom: 0.5rem;
            text-shadow: 0 0 20px rgba(255, 0, 110, 0.5);
        }

        .cyber-subtitle {
            text-align: center;
            color: #ccc;
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }

        /* حقول الإدخال */
        .cyber-input-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .cyber-input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #333;
            border-radius: 10px;
            color: white;
            font-size: 1rem;
            transition: all 0.3s ease;
            outline: none;
        }

        .cyber-input:focus {
            border-color: #ff006e;
            box-shadow: 0 0 20px rgba(255, 0, 110, 0.3);
            background: rgba(0, 0, 0, 0.7);
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            transition: color 0.3s ease;
        }

        .cyber-input:focus + .input-icon {
            color: #ff006e;
        }

        .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .password-toggle:hover {
            color: #ff006e;
        }

        /* زر تسجيل الدخول */
        .cyber-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(45deg, #ff006e, #8338ec);
            border: none;
            border-radius: 10px;
            color: white;
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            margin-bottom: 2rem;
        }

        .cyber-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .cyber-btn:hover::before {
            left: 100%;
        }

        .cyber-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(255, 0, 110, 0.4);
        }

        /* البيانات التجريبية */
        .demo-accounts {
            border-top: 1px solid #333;
            padding-top: 1.5rem;
        }

        .demo-title {
            text-align: center;
            color: #ccc;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .demo-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            background: rgba(255, 0, 110, 0.1);
            border: 1px solid rgba(255, 0, 110, 0.3);
            border-radius: 8px;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .demo-item:hover {
            background: rgba(255, 0, 110, 0.2);
            transform: translateX(-5px);
        }

        .demo-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 0.75rem;
            font-size: 0.8rem;
        }

        .demo-avatar.admin {
            background: linear-gradient(45deg, #ff006e, #8338ec);
        }

        .demo-avatar.user {
            background: linear-gradient(45deg, #8338ec, #3a86ff);
        }

        .demo-text {
            flex: 1;
            font-size: 0.85rem;
        }

        .demo-role {
            display: block;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .demo-creds {
            color: #ccc;
            font-family: monospace;
        }

        /* الجانب الأيمن - المعلومات */
        .info-panel {
            flex: 1;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            padding: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .info-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 30%, rgba(255, 0, 110, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(131, 56, 236, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .info-content {
            position: relative;
            z-index: 2;
            text-align: center;
            max-width: 500px;
        }

        /* رأس الترحيب */
        .welcome-section {
            margin-bottom: 3rem;
        }

        .hologram-icon {
            position: relative;
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .hologram-icon i {
            font-size: 3rem;
            color: #ff006e;
            z-index: 3;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .hologram-ring {
            position: absolute;
            border: 2px solid #ff006e;
            border-radius: 50%;
            animation: ring-rotate 4s linear infinite;
        }

        .hologram-ring:nth-child(2) {
            width: 80px;
            height: 80px;
            top: 20px;
            left: 20px;
        }

        .hologram-ring.ring-2 {
            width: 100px;
            height: 100px;
            top: 10px;
            left: 10px;
            animation-direction: reverse;
            animation-duration: 6s;
            border-color: #8338ec;
        }

        @keyframes ring-rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .welcome-section h2 {
            font-size: 2.5rem;
            font-weight: 900;
            background: linear-gradient(45deg, #ff006e, #8338ec, #3a86ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }

        .welcome-section p {
            color: #ccc;
            font-size: 1.2rem;
            line-height: 1.6;
        }

        /* شبكة المميزات */
        .features-matrix {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .feature-node {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 0, 110, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .feature-node::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 0, 110, 0.1), transparent);
            transition: left 0.5s;
        }

        .feature-node:hover::before {
            left: 100%;
        }

        .feature-node:hover {
            transform: translateY(-5px);
            border-color: #ff006e;
            box-shadow: 0 10px 30px rgba(255, 0, 110, 0.2);
        }

        .node-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            display: block;
        }

        .node-text h4 {
            color: white;
            font-size: 1rem;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .node-text p {
            color: #ccc;
            font-size: 0.85rem;
            margin: 0;
        }

        /* الإحصائيات المتقدمة */
        .cyber-stats {
            display: flex;
            gap: 1rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            flex: 1;
            background: rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(131, 56, 236, 0.3);
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 900;
            color: #8338ec;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #ccc;
            font-size: 0.8rem;
            margin-bottom: 0.5rem;
        }

        .stat-bar {
            height: 4px;
            background: rgba(131, 56, 236, 0.2);
            border-radius: 2px;
            overflow: hidden;
        }

        .stat-fill {
            height: 100%;
            background: linear-gradient(90deg, #8338ec, #ff006e);
            border-radius: 2px;
            animation: fill 2s ease-out;
        }

        @keyframes fill {
            from { width: 0%; }
        }

        /* شعار الشركة */
        .company-badge {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(58, 134, 255, 0.3);
            border-radius: 25px;
            padding: 1rem 2rem;
            position: relative;
            overflow: hidden;
        }

        .badge-glow {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(58, 134, 255, 0.1), rgba(131, 56, 236, 0.1));
            animation: badge-glow 3s ease-in-out infinite;
        }

        @keyframes badge-glow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .company-badge i {
            color: #3a86ff;
            font-size: 1.2rem;
            z-index: 2;
        }

        .company-badge span {
            color: white;
            font-weight: 600;
            z-index: 2;
        }

        /* التجاوب */
        @media (max-width: 768px) {
            .cyber-login {
                flex-direction: column;
            }

            .info-panel {
                order: -1;
                padding: 2rem;
            }

            .features-matrix {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .cyber-stats {
                flex-direction: column;
            }

            .welcome-section h2 {
                font-size: 2rem;
            }

            .login-panel {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>

<!-- خلفية نيون -->
<div class="neon-bg"></div>
<div class="neon-particles" id="particles"></div>

<!-- المحتوى الرئيسي -->
<div class="cyber-login">
    <!-- الجانب الأيسر - النموذج -->
    <div class="login-panel">
        <div class="login-box">
            <!-- الشعار -->
            <div class="cyber-logo">
                <div class="logo-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <h1 class="cyber-title">CYBER ERP</h1>
                <p class="cyber-subtitle">نظام إدارة الموارد المستقبلي</p>
            </div>

            <!-- نموذج تسجيل الدخول -->
            <form method="POST" action="{{ url_for('auth.login') }}">
                <div class="cyber-input-group">
                    <input type="text" name="username" id="username" class="cyber-input" placeholder="اسم المستخدم" required>
                    <i class="fas fa-user input-icon"></i>
                </div>

                <div class="cyber-input-group">
                    <input type="password" name="password" id="password" class="cyber-input" placeholder="كلمة المرور" required>
                    <i class="fas fa-lock input-icon"></i>
                    <button type="button" class="password-toggle" onclick="togglePassword()">
                        <i class="fas fa-eye" id="eyeIcon"></i>
                    </button>
                </div>

                <button type="submit" class="cyber-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    دخول إلى النظام
                </button>
            </form>

            <!-- رابط تسجيل دخول المخلصين -->
            <div style="text-align: center; margin: 20px 0;">
                <a href="{{ url_for('auth.agent_login') }}"
                   style="color: #ff006e; text-decoration: none; font-weight: 500;
                          padding: 10px 20px; border: 1px solid #ff006e; border-radius: 25px;
                          transition: all 0.3s ease; display: inline-block;"
                   onmouseover="this.style.background='#ff006e'; this.style.color='white';"
                   onmouseout="this.style.background='transparent'; this.style.color='#ff006e';">
                    <i class="fas fa-user-tie" style="margin-left: 8px;"></i>
                    تسجيل دخول المخلصين
                </a>
            </div>

            <!-- البيانات التجريبية -->
            <div class="demo-accounts">
                <div class="demo-title">🔑 حسابات تجريبية</div>

                <div class="demo-item" onclick="fillDemo('admin', 'admin')">
                    <div class="demo-avatar admin">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div class="demo-text">
                        <span class="demo-role">مدير النظام</span>
                        <span class="demo-creds">admin / admin</span>
                    </div>
                </div>

                <div class="demo-item" onclick="fillDemo('user', 'user123')">
                    <div class="demo-avatar user">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="demo-text">
                        <span class="demo-role">مستخدم عادي</span>
                        <span class="demo-creds">user / user123</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الجانب الأيمن - المعلومات -->
    <div class="info-panel">
        <div class="info-content">
            <!-- رأس الترحيب -->
            <div class="welcome-section">
                <div class="hologram-icon">
                    <i class="fas fa-chart-line"></i>
                    <div class="hologram-ring"></div>
                    <div class="hologram-ring ring-2"></div>
                </div>
                <h2>مرحباً بك في المستقبل</h2>
                <p>نظام ERP بتقنيات الذكاء الاصطناعي والواقع المعزز</p>
            </div>

            <!-- المميزات -->
            <div class="features-matrix">
                <div class="feature-node" data-feature="ai">
                    <div class="node-icon">🤖</div>
                    <div class="node-text">
                        <h4>ذكاء اصطناعي</h4>
                        <p>تحليل ذكي للبيانات</p>
                    </div>
                </div>

                <div class="feature-node" data-feature="blockchain">
                    <div class="node-icon">⛓️</div>
                    <div class="node-text">
                        <h4>بلوك تشين</h4>
                        <p>أمان متقدم للمعاملات</p>
                    </div>
                </div>

                <div class="feature-node" data-feature="cloud">
                    <div class="node-icon">☁️</div>
                    <div class="node-text">
                        <h4>الحوسبة السحابية</h4>
                        <p>وصول من أي مكان</p>
                    </div>
                </div>

                <div class="feature-node" data-feature="iot">
                    <div class="node-icon">📡</div>
                    <div class="node-text">
                        <h4>إنترنت الأشياء</h4>
                        <p>ربط ذكي للأجهزة</p>
                    </div>
                </div>
            </div>

            <!-- إحصائيات متقدمة -->
            <div class="cyber-stats">
                <div class="stat-card">
                    <div class="stat-value">99.99%</div>
                    <div class="stat-label">وقت التشغيل</div>
                    <div class="stat-bar">
                        <div class="stat-fill" style="width: 99.99%"></div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-value">24/7</div>
                    <div class="stat-label">دعم فني</div>
                    <div class="stat-bar">
                        <div class="stat-fill" style="width: 100%"></div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-value">256-bit</div>
                    <div class="stat-label">تشفير</div>
                    <div class="stat-bar">
                        <div class="stat-fill" style="width: 100%"></div>
                    </div>
                </div>
            </div>

            <!-- شعار الشركة -->
            <div class="company-badge">
                <div class="badge-glow"></div>
                <i class="fas fa-shield-alt"></i>
                <span>نظام معتمد ومؤمن</span>
            </div>
        </div>
    </div>
</div>

<script>
// إنشاء الجسيمات المتحركة
function createParticles() {
    const container = document.getElementById('particles');
    const particleCount = 50;

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 8 + 's';
        particle.style.animationDuration = (Math.random() * 3 + 5) + 's';

        // ألوان مختلفة للجسيمات
        const colors = ['#ff006e', '#8338ec', '#3a86ff'];
        const color = colors[Math.floor(Math.random() * colors.length)];
        particle.style.background = color;
        particle.style.boxShadow = `0 0 10px ${color}`;

        container.appendChild(particle);
    }
}

// تبديل كلمة المرور
function togglePassword() {
    const passwordField = document.getElementById('password');
    const eyeIcon = document.getElementById('eyeIcon');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        eyeIcon.classList.remove('fa-eye');
        eyeIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        eyeIcon.classList.remove('fa-eye-slash');
        eyeIcon.classList.add('fa-eye');
    }
}

// ملء البيانات التجريبية
function fillDemo(username, password) {
    const usernameField = document.getElementById('username');
    const passwordField = document.getElementById('password');

    // تأثير الكتابة
    usernameField.value = '';
    passwordField.value = '';

    let i = 0;
    const typeUsername = setInterval(() => {
        usernameField.value += username[i];
        i++;
        if (i >= username.length) {
            clearInterval(typeUsername);

            let j = 0;
            const typePassword = setInterval(() => {
                passwordField.value += password[j];
                j++;
                if (j >= password.length) {
                    clearInterval(typePassword);

                    // تأثير النجاح
                    usernameField.style.borderColor = '#00ff88';
                    passwordField.style.borderColor = '#00ff88';
                    usernameField.style.boxShadow = '0 0 20px rgba(0, 255, 136, 0.3)';
                    passwordField.style.boxShadow = '0 0 20px rgba(0, 255, 136, 0.3)';

                    setTimeout(() => {
                        usernameField.style.borderColor = '';
                        passwordField.style.borderColor = '';
                        usernameField.style.boxShadow = '';
                        passwordField.style.boxShadow = '';
                    }, 2000);
                }
            }, 80);
        }
    }, 100);
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    createParticles();

    // تركيز تلقائي
    setTimeout(() => {
        document.getElementById('username').focus();
    }, 500);

    console.log('🚀 CYBER ERP Login System Initialized');
});
</script>

</body>
</html>