🏗️ هيكل قاعدة البيانات المتقدم لنظام ERP
💱 1. إدارة العملات المتعددة:
-- جدول العملات
CURRENCIES (
    CURRENCY_CODE VARCHAR2(3) PRIMARY KEY, -- USD, EUR, SAR, etc.
    CURRENCY_NAME VARCHAR2(100),
    CURRENCY_SYMBOL VARCHAR2(10),
    DECIMAL_PLACES NUMBER(1) DEFAULT 2,
    IS_BASE_CURRENCY CHAR(1) DEFAULT 'N',
    IS_ACTIVE CHAR(1) DEFAULT 'Y',
    CREATED_DATE DATE,
    CREATED_BY NUMBER
)

-- جدول أسعار الصرف
EXCHANGE_RATES (
    RATE_ID NUMBER PRIMARY KEY,
    FROM_CURRENCY VARCHAR2(3),
    TO_CURRENCY VARCHAR2(3),
    EXCHANGE_RATE NUMBER(15,6),
    RATE_DATE DATE,
    RATE_TYPE VARCHAR2(20), -- BUYING, SELLING, AVERAGE
    IS_ACTIVE CHAR(1) DEFAULT 'Y',
    CREATED_DATE DATE,
    CREATED_BY NUMBER,
    FOREIGN KEY (FROM_CURRENCY) REFERENCES CURRENCIES(CURRENCY_CODE),
    FOREIGN KEY (TO_CURRENCY) REFERENCES CURRENCIES(CURRENCY_CODE)
)
🏢 2. هيكل الموردين المتقدم:
-- جدول الموردين الرئيسي
SUPPLIERS (
    SUPPLIER_ID NUMBER PRIMARY KEY,
    SUPPLIER_CODE VARCHAR2(20) UNIQUE,
    SUPPLIER_NAME VARCHAR2(200),
    SUPPLIER_NAME_EN VARCHAR2(200),
    SUPPLIER_TYPE VARCHAR2(50), -- LOCAL, INTERNATIONAL, GOVERNMENT
    TAX_NUMBER VARCHAR2(50),
    COMMERCIAL_REGISTER VARCHAR2(50),
    CREDIT_RATING VARCHAR2(10), -- AAA, AA, A, BBB, etc.
    PAYMENT_TERMS_ID NUMBER,
    DEFAULT_CURRENCY VARCHAR2(3),
    IS_ACTIVE CHAR(1) DEFAULT 'Y',
    CREATED_DATE DATE,
    CREATED_BY NUMBER,
    FOREIGN KEY (DEFAULT_CURRENCY) REFERENCES CURRENCIES(CURRENCY_CODE)
)

-- جدول عناوين الموردين (متعدد)
SUPPLIER_ADDRESSES (
    ADDRESS_ID NUMBER PRIMARY KEY,
    SUPPLIER_ID NUMBER,
    ADDRESS_TYPE VARCHAR2(20), -- BILLING, SHIPPING, MAIN
    COUNTRY_CODE VARCHAR2(3),
    CITY VARCHAR2(100),
    DISTRICT VARCHAR2(100),
    STREET VARCHAR2(200),
    BUILDING_NO VARCHAR2(20),
    POSTAL_CODE VARCHAR2(20),
    IS_DEFAULT CHAR(1) DEFAULT 'N',
    FOREIGN KEY (SUPPLIER_ID) REFERENCES SUPPLIERS(SUPPLIER_ID)
)

-- جدول جهات الاتصال (متعدد)
SUPPLIER_CONTACTS (
    CONTACT_ID NUMBER PRIMARY KEY,
    SUPPLIER_ID NUMBER,
    CONTACT_NAME VARCHAR2(100),
    POSITION VARCHAR2(100),
    EMAIL VARCHAR2(100),
    PHONE VARCHAR2(20),
    MOBILE VARCHAR2(20),
    IS_PRIMARY CHAR(1) DEFAULT 'N',
    FOREIGN KEY (SUPPLIER_ID) REFERENCES SUPPLIERS(SUPPLIER_ID)
)
💰 3. نظام الأرصدة متعدد العملات:
-- جدول أرصدة الموردين (لكل عملة)
SUPPLIER_BALANCES (
    BALANCE_ID NUMBER PRIMARY KEY,
    SUPPLIER_ID NUMBER,
    CURRENCY_CODE VARCHAR2(3),
    OPENING_BALANCE NUMBER(15,2) DEFAULT 0,
    DEBIT_AMOUNT NUMBER(15,2) DEFAULT 0,
    CREDIT_AMOUNT NUMBER(15,2) DEFAULT 0,
    CURRENT_BALANCE NUMBER(15,2) DEFAULT 0,
    CREDIT_LIMIT NUMBER(15,2) DEFAULT 0,
    LAST_TRANSACTION_DATE DATE,
    LAST_UPDATED DATE,
    FOREIGN KEY (SUPPLIER_ID) REFERENCES SUPPLIERS(SUPPLIER_ID),
    FOREIGN KEY (CURRENCY_CODE) REFERENCES CURRENCIES(CURRENCY_CODE),
    UNIQUE (SUPPLIER_ID, CURRENCY_CODE)
)

-- جدول حركات الموردين المتقدم
SUPPLIER_TRANSACTIONS (
    TRANSACTION_ID NUMBER PRIMARY KEY,
    SUPPLIER_ID NUMBER,
    TRANSACTION_TYPE VARCHAR2(30), -- INVOICE, PAYMENT, CREDIT_NOTE, DEBIT_NOTE, ADJUSTMENT
    REFERENCE_TYPE VARCHAR2(30), -- PO, INVOICE, PAYMENT_VOUCHER, etc.
    REFERENCE_ID NUMBER,
    REFERENCE_NUMBER VARCHAR2(50),
    TRANSACTION_DATE DATE,
    DUE_DATE DATE,
    CURRENCY_CODE VARCHAR2(3),
    ORIGINAL_AMOUNT NUMBER(15,2),
    EXCHANGE_RATE NUMBER(15,6) DEFAULT 1,
    BASE_CURRENCY_AMOUNT NUMBER(15,2), -- المبلغ بالعملة الأساسية
    DEBIT_AMOUNT NUMBER(15,2) DEFAULT 0,
    CREDIT_AMOUNT NUMBER(15,2) DEFAULT 0,
    DESCRIPTION VARCHAR2(500),
    STATUS VARCHAR2(20) DEFAULT 'ACTIVE', -- ACTIVE, CANCELLED, REVERSED
    CREATED_DATE DATE,
    CREATED_BY NUMBER,
    FOREIGN KEY (SUPPLIER_ID) REFERENCES SUPPLIERS(SUPPLIER_ID),
    FOREIGN KEY (CURRENCY_CODE) REFERENCES CURRENCIES(CURRENCY_CODE)
)
📊 4. نظام مطابقة الأرصدة المتقدم:
-- جدول دورات المطابقة
RECONCILIATION_CYCLES (
    CYCLE_ID NUMBER PRIMARY KEY,
    CYCLE_NAME VARCHAR2(100),
    RECONCILIATION_DATE DATE,
    PERIOD_FROM DATE,
    PERIOD_TO DATE,
    STATUS VARCHAR2(20), -- OPEN, IN_PROGRESS, COMPLETED, CANCELLED
    CREATED_BY NUMBER,
    CREATED_DATE DATE
)

-- جدول مطابقة الأرصدة التفصيلي
SUPPLIER_RECONCILIATION (
    RECONCILIATION_ID NUMBER PRIMARY KEY,
    CYCLE_ID NUMBER,
    SUPPLIER_ID NUMBER,
    CURRENCY_CODE VARCHAR2(3),
    SYSTEM_BALANCE NUMBER(15,2),
    SUPPLIER_STATEMENT_BALANCE NUMBER(15,2),
    DIFFERENCE_AMOUNT NUMBER(15,2),
    RECONCILIATION_STATUS VARCHAR2(20), -- MATCHED, UNMATCHED, ADJUSTED
    RECONCILIATION_NOTES CLOB,
    RECONCILED_BY NUMBER,
    RECONCILED_DATE DATE,
    FOREIGN KEY (CYCLE_ID) REFERENCES RECONCILIATION_CYCLES(CYCLE_ID),
    FOREIGN KEY (SUPPLIER_ID) REFERENCES SUPPLIERS(SUPPLIER_ID),
    FOREIGN KEY (CURRENCY_CODE) REFERENCES CURRENCIES(CURRENCY_CODE)
)

-- جدول تفاصيل الفروقات
RECONCILIATION_DIFFERENCES (
    DIFFERENCE_ID NUMBER PRIMARY KEY,
    RECONCILIATION_ID NUMBER,
    DIFFERENCE_TYPE VARCHAR2(50), -- TIMING, AMOUNT, MISSING_TRANSACTION
    TRANSACTION_ID NUMBER,
    EXPECTED_AMOUNT NUMBER(15,2),
    ACTUAL_AMOUNT NUMBER(15,2),
    DIFFERENCE_REASON VARCHAR2(500),
    ADJUSTMENT_REQUIRED CHAR(1) DEFAULT 'N',
    RESOLVED CHAR(1) DEFAULT 'N',
    FOREIGN KEY (RECONCILIATION_ID) REFERENCES SUPPLIER_RECONCILIATION(RECONCILIATION_ID)
)
💳 5. نظام المدفوعات المتقدم:
-- جدول طرق الدفع
PAYMENT_METHODS-- جدول طرق الدفع
PAYMENT_METHODS (
    METHOD_ID NUMBER PRIMARY KEY,
    METHOD_CODE VARCHAR2(20),
    METHOD_NAME VARCHAR2(100),
    METHOD_TYPE VARCHAR2(20), -- CASH, BANK_TRANSFER, CHECK, CREDIT_CARD
    REQUIRES_APPROVAL CHAR(1) DEFAULT 'N',
    IS_ACTIVE CHAR(1) DEFAULT 'Y'
)

-- جدول المدفوعات
SUPPLIER_PAYMENTS (
    PAYMENT_ID NUMBER PRIMARY KEY,
    SUPPLIER_ID NUMBER,
    PAYMENT_NUMBER VARCHAR2(50) UNIQUE,
    PAYMENT_DATE DATE,
    CURRENCY_CODE VARCHAR2(3),
    PAYMENT_AMOUNT NUMBER(15,2),
    EXCHANGE_RATE NUMBER(15,6) DEFAULT 1,
    BASE_CURRENCY_AMOUNT NUMBER(15,2),
    PAYMENT_METHOD_ID NUMBER,
    BANK_ACCOUNT_ID NUMBER,
    REFERENCE_NUMBER VARCHAR2(100),
    PAYMENT_STATUS VARCHAR2(20), -- PENDING, APPROVED, PAID, CANCELLED
    APPROVAL_STATUS VARCHAR2(20), -- PENDING, APPROVED, REJECTED
    NOTES CLOB,
    CREATED_BY NUMBER,
    CREATED_DATE DATE,
    APPROVED_BY NUMBER,
    APPROVED_DATE DATE,
    FOREIGN KEY (SUPPLIER_ID) REFERENCES SUPPLIERS(SUPPLIER_ID),
    FOREIGN KEY (CURRENCY_CODE) REFERENCES CURRENCIES(CURRENCY_CODE),
    FOREIGN KEY (PAYMENT_METHOD_ID) REFERENCES PAYMENT_METHODS(METHOD_ID)
)

-- جدول ربط المدفوعات بالفواتير
PAYMENT_ALLOCATIONS (
    ALLOCATION_ID NUMBER PRIMARY KEY,
    PAYMENT_ID NUMBER,
    TRANSACTION_ID NUMBER, -- ربط بجدول SUPPLIER_TRANSACTIONS
    ALLOCATED_AMOUNT NUMBER(15,2),
    ALLOCATION_DATE DATE,
    FOREIGN KEY (PAYMENT_ID) REFERENCES SUPPLIER_PAYMENTS(PAYMENT_ID),
    FOREIGN KEY (TRANSACTION_ID) REFERENCES SUPPLIER_TRANSACTIONS(TRANSACTION_ID)
)
📈 6. نظام التقارير والتحليلات:
-- جدول أعمار الديون
AGING_ANALYSIS (
    AGING_ID NUMBER PRIMARY KEY,
    SUPPLIER_ID NUMBER,
    CURRENCY_CODE VARCHAR2(3),
    AS_OF_DATE DATE,
    CURRENT_AMOUNT NUMBER(15,2), -- 0-30 days
    DAYS_31_60 NUMBER(15,2),
    DAYS_61_90 NUMBER(15,2),
    DAYS_91_120 NUMBER(15,2),
    OVER_120_DAYS NUMBER(15,2),
    TOTAL_OUTSTANDING NUMBER(15,2),
    CREATED_DATE DATE
)

-- جدول KPIs الموردين
SUPPLIER_KPI (
    KPI_ID NUMBER PRIMARY KEY,
    SUPPLIER_ID NUMBER,
    PERIOD_YEAR NUMBER(4),
    PERIOD_MONTH NUMBER(2),
    TOTAL_PURCHASES NUMBER(15,2),
    AVERAGE_PAYMENT_DAYS NUMBER(5,2),
    ON_TIME_PAYMENT_RATE NUMBER(5,2),
    DISCOUNT_TAKEN_RATE NUMBER(5,2),
    RETURN_RATE NUMBER(5,2),
    QUALITY_SCORE NUMBER(3,1)
)
🔧 7. الميزات المتقدمة المقترحة:
أ) إدارة العملات:
تحديث أسعار الصرف تلقائ<|im_start|> من مصادر خارجية
تقييم العملات الأجنبية في نهاية الفترة
تقارير مخاطر العملة
ب) سير العمل (Workflow):
موافقات متعددة المستويات للمدفوعات
تنبيهات ذكية للاستحقاقات
تصعيد تلقائي للمتأخرات
ج) التكامل:
API للبنوك لاستيراد كشوفات الحساب
تكامل مع أنظمة الخزينة
ربط مع أنظمة الموافقات
د) الذكاء الاصطناعي:
تنبؤ بالتدفقات النقدية
تحليل مخاطر الموردين
اقتراحات تحسين الشروط