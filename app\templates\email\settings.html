<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات البريد الإلكتروني - نظام متقدم</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --card-bg: rgba(255, 255, 255, 0.95);
            --sidebar-bg: rgba(255, 255, 255, 0.9);

            --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-heavy: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --shadow-extreme: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

            --border-radius: 16px;
            --border-radius-lg: 24px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .settings-app {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            padding-top: 100px; /* مساحة لشريط التنقل */
        }

        .settings-container {
            width: 100%;
            max-width: 1400px;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-extreme);
            overflow: hidden;
            animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(40px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .settings-header {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 40px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .settings-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .settings-title {
            font-size: 36px;
            font-weight: 800;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .settings-subtitle {
            color: #6b7280;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 24px;
        }

        .settings-nav {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .nav-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: var(--transition);
            background: rgba(255, 255, 255, 0.7);
            color: #374151;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .nav-btn:hover {
            background: var(--primary-gradient);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .nav-btn.active {
            background: var(--primary-gradient);
            color: white;
            box-shadow: var(--shadow-medium);
        }

        .settings-body {
            padding: 40px;
            background: var(--card-bg);
        }

        .settings-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            margin-bottom: 32px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: var(--transition);
        }

        .settings-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-heavy);
        }

        .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 24px 32px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .card-icon {
            width: 48px;
            height: 48px;
            background: var(--primary-gradient);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .card-title {
            font-size: 20px;
            font-weight: 700;
            color: #1f2937;
            margin: 0;
        }

        .card-body {
            padding: 32px;
        }

        .empty-state {
            text-align: center;
            padding: 80px 40px;
            color: #9ca3af;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 24px;
            color: #e5e7eb;
        }

        .empty-state h3 {
            font-size: 24px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
        }

        .empty-state p {
            font-size: 16px;
            color: #6b7280;
            max-width: 400px;
            margin: 0 auto 32px;
            line-height: 1.6;
        }

        .add-account-btn {
            background: var(--primary-gradient);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: var(--shadow-medium);
            position: relative;
            overflow: hidden;
        }

        .add-account-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s ease;
        }

        .add-account-btn:hover::before {
            left: 100%;
        }

        .add-account-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-heavy);
        }

        /* نافذة منبثقة احترافية */
        .simple-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(10px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        .simple-modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-extreme);
            max-width: 700px;
            width: 90%;
            max-height: 90vh;
            overflow: hidden;
            animation: slideInUp 0.3s ease;
        }

        .modal-header {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 32px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .modal-title {
            font-size: 24px;
            font-weight: 700;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }

        .close-btn {
            background: rgba(0, 0, 0, 0.05);
            border: none;
            color: #6b7280;
            font-size: 20px;
            cursor: pointer;
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            transition: var(--transition);
        }

        .close-btn:hover {
            background: rgba(0, 0, 0, 0.1);
            color: #374151;
            transform: scale(1.05);
        }

        .modal-body {
            padding: 32px;
            overflow-y: auto;
            max-height: calc(90vh - 200px);
        }

        .modal-footer {
            padding: 24px 32px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            gap: 16px;
            justify-content: flex-end;
            background: #f9fafb;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            display: block;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-control {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 16px 20px;
            width: 100%;
            transition: var(--transition);
            font-size: 16px;
            background: rgba(255, 255, 255, 0.8);
        }

        .form-control:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }

        .form-control:hover {
            border-color: #d1d5db;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* قائمة الحسابات الاحترافية */
        .accounts-list {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .account-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: var(--border-radius);
            padding: 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-light);
        }

        .account-card::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--primary-gradient);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .account-card:hover::before {
            transform: scaleY(1);
        }

        .account-card:hover {
            border-color: #667eea;
            box-shadow: var(--shadow-heavy);
            transform: translateY(-4px);
        }

        .account-info {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .account-avatar {
            width: 64px;
            height: 64px;
            background: var(--primary-gradient);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: 700;
            text-transform: uppercase;
        }

        .account-details {
            flex: 1;
        }

        .account-email {
            font-size: 18px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .account-name {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .account-servers {
            font-size: 14px;
            color: #9ca3af;
            display: flex;
            gap: 16px;
        }

        .server-info {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-active {
            background: rgba(34, 197, 94, 0.1);
            color: #16a34a;
        }

        .status-error {
            background: rgba(239, 68, 68, 0.1);
            color: #dc2626;
        }

        .account-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--primary-gradient);
            color: white;
            box-shadow: var(--shadow-light);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .btn-success {
            background: var(--success-gradient);
            color: white;
            box-shadow: var(--shadow-light);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .btn-warning {
            background: var(--warning-gradient);
            color: white;
            box-shadow: var(--shadow-light);
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .btn-danger {
            background: var(--danger-gradient);
            color: white;
            box-shadow: var(--shadow-light);
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .btn-outline {
            background: transparent;
            color: #6b7280;
            border: 2px solid #e5e7eb;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #d1d5db;
            color: #374151;
        }

        .text-small {
            font-size: 14px;
        }

        /* التصميم المتجاوب */
        @media (max-width: 768px) {
            .settings-app {
                padding: 10px;
                padding-top: 120px; /* مساحة أكبر للهواتف */
            }

            .settings-header {
                padding: 24px;
            }

            .settings-title {
                font-size: 28px;
            }

            .settings-subtitle {
                font-size: 16px;
            }

            .settings-body {
                padding: 24px;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .account-card {
                flex-direction: column;
                align-items: flex-start;
                gap: 20px;
                padding: 24px;
            }

            .account-info {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .account-actions {
                width: 100%;
                justify-content: flex-start;
            }

            .modal-content {
                width: 95%;
                margin: 10px;
            }

            .modal-header, .modal-body, .modal-footer {
                padding: 20px;
            }

            .nav-btn {
                padding: 10px 16px;
                font-size: 13px;
            }
        }

        @media (max-width: 480px) {
            .settings-title {
                font-size: 24px;
            }

            .account-avatar {
                width: 48px;
                height: 48px;
                font-size: 18px;
            }

            .account-email {
                font-size: 16px;
            }

            .action-btn {
                padding: 8px 16px;
                font-size: 13px;
            }
        }

        /* تحريك وتأثيرات إضافية */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .bounce-in {
            animation: bounceIn 0.6s ease-out;
        }

        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }

        .glow-on-hover {
            transition: var(--transition);
        }

        .glow-on-hover:hover {
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
        }

        /* تصميم نوافذ التعديل والحذف */
        .test-step {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            margin: 8px 0;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.5);
            border-left: 4px solid #3b82f6;
        }

        .test-step.success {
            border-left-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }

        .test-step.error {
            border-left-color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }

        .test-step i {
            font-size: 16px;
        }

        .test-step.success i {
            color: #10b981;
        }

        .test-step.error i {
            color: #ef4444;
        }

        /* تحسينات إضافية للنوافذ */
        .modal-content {
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .form-check-input:checked {
            background: var(--primary-gradient);
            border-color: transparent;
        }

        .form-check-label {
            font-weight: 500;
            color: #374151;
        }

        /* تصميم التبويبات */
        .tab-content {
            display: none;
            animation: fadeInUp 0.4s ease-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .nav-btn.active {
            background: var(--primary-gradient);
            color: white;
            box-shadow: var(--shadow-medium);
        }

        .settings-section {
            margin-bottom: 32px;
            padding: 24px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid rgba(102, 126, 234, 0.2);
        }

        .form-control {
            background: rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 14px;
            transition: var(--transition);
        }

        .form-control:focus {
            background: rgba(255, 255, 255, 0.95);
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
        }

        .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        /* شريط التنقل العلوي */
        .top-navigation {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            box-sizing: border-box;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .nav-back-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            background: var(--primary-gradient);
            color: white;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: var(--shadow-light);
        }

        .nav-back-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .nav-breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .breadcrumb-link {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #6b7280;
            text-decoration: none;
            padding: 6px 12px;
            border-radius: 8px;
            transition: var(--transition);
        }

        .breadcrumb-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .breadcrumb-separator {
            color: #d1d5db;
            font-size: 12px;
        }

        .breadcrumb-current {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #374151;
            font-weight: 600;
            padding: 6px 12px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
        }

        .nav-action-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            background: rgba(255, 255, 255, 0.8);
            color: #374151;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            font-size: 14px;
        }

        .nav-action-btn:hover {
            background: rgba(255, 255, 255, 0.95);
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-1px);
        }

        /* تحسينات للتصميم المتجاوب */
        @media (max-width: 768px) {
            .top-navigation {
                flex-direction: column;
                gap: 16px;
                padding: 16px;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                width: 100%;
            }

            .nav-left, .nav-right {
                width: 100%;
                justify-content: center;
            }

            .nav-breadcrumb {
                display: none;
            }

            .nav-back-btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* الزر العائم للعودة السريعة */
        .floating-back-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: var(--primary-gradient);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: var(--shadow-medium);
            transition: var(--transition);
            z-index: 1000;
            font-size: 20px;
        }

        .floating-back-btn:hover {
            transform: translateY(-3px) scale(1.1);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }

        .floating-back-btn:active {
            transform: translateY(-1px) scale(1.05);
        }

        /* تأثير نبضة للفت الانتباه */
        .floating-back-btn::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border-radius: 50%;
            background: var(--primary-gradient);
            opacity: 0.3;
            animation: pulse-ring 2s infinite;
        }

        @keyframes pulse-ring {
            0% {
                transform: scale(1);
                opacity: 0.3;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.1;
            }
            100% {
                transform: scale(1.4);
                opacity: 0;
            }
        }

        /* إخفاء الزر العائم على الشاشات الصغيرة */
        @media (max-width: 768px) {
            .floating-back-btn {
                bottom: 20px;
                right: 20px;
                width: 50px;
                height: 50px;
                font-size: 18px;
            }
        }
    </style>
</head>

<body>
    <!-- شريط التنقل العلوي -->
    <div class="top-navigation">
        <div class="nav-left">
            <button class="nav-back-btn" onclick="goToInbox()">
                <i class="fas fa-arrow-right"></i>
                العودة لصندوق الوارد
            </button>
            <div class="nav-breadcrumb">
                <a href="/dashboard" class="breadcrumb-link">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
                <i class="fas fa-chevron-left breadcrumb-separator"></i>
                <a href="/email/inbox" class="breadcrumb-link">
                    <i class="fas fa-envelope"></i>
                    البريد الإلكتروني
                </a>
                <i class="fas fa-chevron-left breadcrumb-separator"></i>
                <span class="breadcrumb-current">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </span>
            </div>
        </div>
        <div class="nav-right">
            <button class="nav-action-btn" onclick="goToInbox()">
                <i class="fas fa-inbox"></i>
                صندوق الوارد
            </button>
            <button class="nav-action-btn" onclick="goToDashboard()">
                <i class="fas fa-tachometer-alt"></i>
                لوحة التحكم
            </button>
        </div>
    </div>

    <div class="settings-app">
        <div class="settings-container">
            <!-- رأس الصفحة الاحترافي -->
            <div class="settings-header">
                <h1 class="settings-title">
                    <i class="fas fa-cog"></i>
                    إعدادات البريد الإلكتروني
                </h1>
                <p class="settings-subtitle">إدارة حسابات البريد الإلكتروني والإعدادات المتقدمة</p>

                <div class="settings-nav">
                    <button class="nav-btn active" onclick="showTab('accounts')">
                        <i class="fas fa-envelope"></i>
                        الحسابات
                    </button>
                    <button class="nav-btn" onclick="showTab('general')">
                        <i class="fas fa-cog"></i>
                        الإعدادات العامة
                    </button>
                    <button class="nav-btn" onclick="showTab('security')">
                        <i class="fas fa-shield-alt"></i>
                        الأمان
                    </button>
                    <button class="nav-btn" onclick="showTab('notifications')">
                        <i class="fas fa-bell"></i>
                        الإشعارات
                    </button>
                </div>
            </div>

            <!-- محتوى الإعدادات -->
            <div class="settings-body">
                <!-- تبويب الحسابات -->
                <div id="accounts-tab" class="tab-content active">
                    <div class="settings-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <h2 class="card-title">حسابات البريد الإلكتروني</h2>
                        </div>
                        <div class="card-body">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 32px;">
                            <p style="color: #6b7280; margin: 0;">إدارة حسابات البريد الإلكتروني المتصلة بالنظام</p>
                            <div style="display: flex; gap: 12px;">

                                <button class="add-account-btn" onclick="openModal()">
                                    <i class="fas fa-plus" style="margin-left: 8px;"></i>
                                    إضافة حساب جديد
                                </button>
                                <button class="action-btn btn-outline" onclick="testJS()">
                                    <i class="fas fa-cog"></i>
                                    اختبار
                                </button>
                            </div>
                        </div>



                        {% if accounts %}
                            <!-- عرض الحسابات الموجودة -->
                            <div class="accounts-list">
                                {% for account in accounts %}
                                <div class="account-card glow-on-hover">
                                    <div class="account-info">
                                        <div class="account-avatar">
                                            {{ account.email_address[0].upper() }}
                                        </div>
                                        <div class="account-details">
                                            <div class="account-email">
                                                {{ account.email_address }}
                                                {% if account.is_default %}
                                                    <span class="status-badge status-active">
                                                        <i class="fas fa-star"></i>
                                                        افتراضي
                                                    </span>
                                                {% endif %}
                                            </div>
                                            <div class="account-name">{{ account.display_name }}</div>
                                            <div class="account-servers">
                                                <div class="server-info">
                                                    <i class="fas fa-paper-plane"></i>
                                                    SMTP: {{ account.smtp_server }}:{{ account.smtp_port }}
                                                </div>
                                                <div class="server-info">
                                                    <i class="fas fa-inbox"></i>
                                                    IMAP: {{ account.imap_server }}:{{ account.imap_port }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="account-actions">
                                        <button class="action-btn btn-primary" onclick="editAccount({{ account.id }})">
                                            <i class="fas fa-edit"></i>
                                            تعديل
                                        </button>
                                        <button class="action-btn btn-success" onclick="testConnection({{ account.id }})">
                                            <i class="fas fa-plug"></i>
                                            اختبار
                                        </button>
                                        <button class="action-btn btn-warning" onclick="debugAccount({{ account.id }})">
                                            <i class="fas fa-bug"></i>
                                            تشخيص
                                        </button>
                                        {% if not account.is_default %}
                                        <button class="action-btn btn-outline" onclick="setDefault({{ account.id }})">
                                            <i class="fas fa-star"></i>
                                            افتراضي
                                        </button>
                                        {% endif %}
                                        <button class="action-btn btn-danger" onclick="deleteAccount({{ account.id }})">
                                            <i class="fas fa-trash"></i>
                                            حذف
                                        </button>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <!-- حالة عدم وجود حسابات -->
                            <div class="empty-state">
                                <i class="fas fa-envelope-open"></i>
                                <h3>لا توجد حسابات بريد إلكتروني</h3>
                                <p>ابدأ بإضافة حساب بريد إلكتروني لاستخدام النظام والاستفادة من جميع المميزات المتاحة</p>
                                <button class="add-account-btn bounce-in" onclick="openModal()">
                                    <i class="fas fa-plus" style="margin-left: 8px;"></i>
                                    إضافة حساب جديد
                                </button>
                            </div>
                        {% endif %}
                        </div>
                    </div>
                </div>

                <!-- تبويب الإعدادات العامة -->
                <div id="general-tab" class="tab-content">
                    <div class="settings-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <h2 class="card-title">الإعدادات العامة</h2>
                        </div>
                        <div class="card-body">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 32px;">
                                <p style="color: #6b7280; margin: 0;">إعدادات عامة للنظام والواجهة</p>
                            </div>

                            <!-- إعدادات اللغة والواجهة -->
                            <div class="settings-section">
                                <h6 class="section-title">اللغة والواجهة</h6>
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label class="form-label">لغة الواجهة</label>
                                        <select class="form-control" id="languageSelect">
                                            <option value="ar" selected>العربية</option>
                                            <option value="en">English</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">المنطقة الزمنية</label>
                                        <select class="form-control" id="timezoneSelect">
                                            <option value="Asia/Riyadh" selected>الرياض (GMT+3)</option>
                                            <option value="Asia/Dubai">دبي (GMT+4)</option>
                                            <option value="Africa/Cairo">القاهرة (GMT+2)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- إعدادات البريد الافتراضية -->
                            <div class="settings-section">
                                <h6 class="section-title">إعدادات البريد الافتراضية</h6>
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label class="form-label">عدد الرسائل في الصفحة</label>
                                        <select class="form-control" id="messagesPerPageSelect">
                                            <option value="10">10 رسائل</option>
                                            <option value="25" selected>25 رسالة</option>
                                            <option value="50">50 رسالة</option>
                                            <option value="100">100 رسالة</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">ترتيب الرسائل</label>
                                        <select class="form-control" id="sortOrderSelect">
                                            <option value="newest" selected>الأحدث أولاً</option>
                                            <option value="oldest">الأقدم أولاً</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                                        <label class="form-check-label" for="autoRefresh">تحديث تلقائي للرسائل كل 5 دقائق</label>
                                    </div>
                                </div>
                            </div>

                            <!-- أزرار الحفظ -->
                            <div style="margin-top: 32px; text-align: left;">
                                <button class="action-btn btn-primary" onclick="saveGeneralSettings()">
                                    <i class="fas fa-save"></i>
                                    حفظ الإعدادات
                                </button>
                                <button class="action-btn btn-outline" onclick="resetGeneralSettings()">
                                    <i class="fas fa-undo"></i>
                                    إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويب الأمان -->
                <div id="security-tab" class="tab-content">
                    <div class="settings-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h2 class="card-title">إعدادات الأمان</h2>
                        </div>
                        <div class="card-body">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 32px;">
                                <p style="color: #6b7280; margin: 0;">إعدادات الأمان وحماية الحساب</p>
                            </div>

                            <!-- تغيير كلمة المرور -->
                            <div class="settings-section">
                                <h6 class="section-title">تغيير كلمة المرور</h6>
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label class="form-label">كلمة المرور الحالية</label>
                                        <input type="password" class="form-control" id="currentPassword">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">كلمة المرور الجديدة</label>
                                        <input type="password" class="form-control" id="newPassword">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">تأكيد كلمة المرور الجديدة</label>
                                    <input type="password" class="form-control" id="confirmPassword">
                                </div>
                            </div>

                            <!-- إعدادات الأمان -->
                            <div class="settings-section">
                                <h6 class="section-title">إعدادات الحماية</h6>
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="twoFactorAuth">
                                        <label class="form-check-label" for="twoFactorAuth">تفعيل المصادقة الثنائية</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="loginNotifications" checked>
                                        <label class="form-check-label" for="loginNotifications">إشعارات تسجيل الدخول</label>
                                    </div>
                                </div>
                            </div>

                            <!-- أزرار الحفظ -->
                            <div style="margin-top: 32px; text-align: left;">
                                <button class="action-btn btn-primary" onclick="saveSecuritySettings()">
                                    <i class="fas fa-save"></i>
                                    حفظ الإعدادات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويب الإشعارات -->
                <div id="notifications-tab" class="tab-content">
                    <div class="settings-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-bell"></i>
                            </div>
                            <h2 class="card-title">إعدادات الإشعارات</h2>
                        </div>
                        <div class="card-body">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 32px;">
                                <p style="color: #6b7280; margin: 0;">إدارة الإشعارات والتنبيهات</p>
                            </div>

                            <!-- إشعارات البريد -->
                            <div class="settings-section">
                                <h6 class="section-title">إشعارات البريد الإلكتروني</h6>
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="newEmailNotif" checked>
                                        <label class="form-check-label" for="newEmailNotif">إشعار عند وصول رسائل جديدة</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="importantEmailNotif" checked>
                                        <label class="form-check-label" for="importantEmailNotif">إشعار للرسائل المهمة فقط</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="soundNotif">
                                        <label class="form-check-label" for="soundNotif">تشغيل صوت الإشعار</label>
                                    </div>
                                </div>
                            </div>

                            <!-- إشعارات النظام -->
                            <div class="settings-section">
                                <h6 class="section-title">إشعارات النظام</h6>
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="systemNotif" checked>
                                        <label class="form-check-label" for="systemNotif">إشعارات النظام والتحديثات</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="securityNotif" checked>
                                        <label class="form-check-label" for="securityNotif">إشعارات الأمان</label>
                                    </div>
                                </div>
                            </div>

                            <!-- أزرار الحفظ -->
                            <div style="margin-top: 32px; text-align: left;">
                                <button class="action-btn btn-primary" onclick="saveNotificationSettings()">
                                    <i class="fas fa-save"></i>
                                    حفظ الإعدادات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- زر العودة السريعة العائم -->
    <div class="floating-back-btn" onclick="goToInbox()" title="العودة لصندوق الوارد (ESC)">
        <i class="fas fa-arrow-right"></i>
    </div>

<!-- نافذة إضافة حساب -->
<div class="simple-modal" id="accountModal">
    <div class="modal-content">
        <div class="modal-header">
            <h5>إضافة حساب بريد إلكتروني</h5>
            <button class="close-btn" onclick="closeModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="accountForm">
                <!-- معلومات أساسية -->
                <h6 class="mb-3">معلومات الحساب</h6>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">عنوان البريد الإلكتروني *</label>
                        <input type="email" class="form-control" id="emailAddress" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">الاسم المعروض *</label>
                        <input type="text" class="form-control" id="displayName" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">كلمة المرور *</label>
                    <input type="password" class="form-control" id="password" required>
                </div>
                
                <!-- إعدادات خادم الإرسال -->
                <h6 class="mb-3 mt-4">إعدادات خادم الإرسال (SMTP)</h6>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">خادم SMTP *</label>
                        <input type="text" class="form-control" id="smtpServer" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">منفذ SMTP *</label>
                        <input type="number" class="form-control" id="smtpPort" value="587" required>
                    </div>
                </div>
                
                <!-- إعدادات خادم الاستقبال -->
                <h6 class="mb-3 mt-4">إعدادات خادم الاستقبال (IMAP)</h6>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">خادم IMAP *</label>
                        <input type="text" class="form-control" id="imapServer" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">منفذ IMAP *</label>
                        <input type="number" class="form-control" id="imapPort" value="993" required>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
            <button type="button" class="btn btn-primary" onclick="saveAccount()">
                <i class="fas fa-save me-1"></i>
                حفظ الحساب
            </button>
        </div>
    </div>
</div>

<!-- نافذة تعديل الحساب -->
<div class="simple-modal" id="editAccountModal">
    <div class="modal-content">
        <div class="modal-header">
            <h5>تعديل حساب البريد الإلكتروني</h5>
            <button class="close-btn" onclick="closeEditModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="editAccountForm">
                <input type="hidden" id="editAccountId">

                <!-- معلومات أساسية -->
                <h6 class="mb-3">معلومات الحساب</h6>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">عنوان البريد الإلكتروني *</label>
                        <input type="email" class="form-control" id="editEmailAddress" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">اسم العرض *</label>
                        <input type="text" class="form-control" id="editDisplayName" required>
                    </div>
                </div>

                <!-- إعدادات SMTP -->
                <h6 class="mb-3 mt-4">إعدادات الإرسال (SMTP)</h6>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">خادم SMTP *</label>
                        <input type="text" class="form-control" id="editSmtpServer" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">منفذ SMTP *</label>
                        <input type="number" class="form-control" id="editSmtpPort" value="587" required>
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editSmtpUseTls" checked>
                            <label class="form-check-label" for="editSmtpUseTls">استخدام TLS</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editSmtpUseSsl">
                            <label class="form-check-label" for="editSmtpUseSsl">استخدام SSL</label>
                        </div>
                    </div>
                </div>

                <!-- إعدادات IMAP -->
                <h6 class="mb-3 mt-4">إعدادات الاستقبال (IMAP)</h6>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">خادم IMAP *</label>
                        <input type="text" class="form-control" id="editImapServer" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">منفذ IMAP *</label>
                        <input type="number" class="form-control" id="editImapPort" value="993" required>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="editImapUseSsl" checked>
                        <label class="form-check-label" for="editImapUseSsl">استخدام SSL</label>
                    </div>
                </div>

                <!-- كلمة المرور -->
                <h6 class="mb-3 mt-4">كلمة المرور</h6>
                <div class="form-group">
                    <label class="form-label">كلمة المرور الجديدة (اتركها فارغة للاحتفاظ بالحالية)</label>
                    <input type="password" class="form-control" id="editPassword" placeholder="كلمة مرور جديدة (اختياري)">
                </div>


            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-outline" onclick="closeEditModal()">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </button>
            <button type="button" class="btn btn-primary" onclick="updateAccount()">
                <i class="fas fa-save me-1"></i>
                حفظ التغييرات
            </button>
        </div>
    </div>
</div>

<!-- نافذة تأكيد الحذف -->
<div class="simple-modal" id="deleteConfirmModal">
    <div class="modal-content" style="max-width: 400px;">
        <div class="modal-header">
            <h5>تأكيد الحذف</h5>
            <button class="close-btn" onclick="closeDeleteModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div style="text-align: center; padding: 20px;">
                <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: #f59e0b; margin-bottom: 16px;"></i>
                <h6>هل أنت متأكد من حذف هذا الحساب؟</h6>
                <p style="color: #6b7280; margin-top: 8px;">لا يمكن التراجع عن هذا الإجراء</p>
                <p id="deleteAccountInfo" style="font-weight: 600; color: #374151;"></p>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-outline" onclick="closeDeleteModal()">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </button>
            <button type="button" class="btn btn-danger" onclick="confirmDeleteAccount()">
                <i class="fas fa-trash me-1"></i>
                حذف الحساب
            </button>
        </div>
    </div>
</div>

<!-- نافذة اختبار الاتصال -->
<div class="simple-modal" id="testConnectionModal">
    <div class="modal-content" style="max-width: 500px;">
        <div class="modal-header">
            <h5>اختبار الاتصال</h5>
            <button class="close-btn" onclick="closeTestModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div id="testResults" style="padding: 20px;">
                <div class="test-step">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>جاري اختبار الاتصال...</span>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-outline" onclick="closeTestModal()">
                <i class="fas fa-times me-1"></i>
                إغلاق
            </button>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script>
console.log('JavaScript loaded');

// دالة اختبار
function testJS() {
    alert('JavaScript يعمل بشكل صحيح!');
    console.log('Test function called');
}

// فتح النافذة
function openModal() {
    console.log('Opening modal...');
    const modal = document.getElementById('accountModal');
    if (modal) {
        modal.style.display = 'flex';
        console.log('Modal opened');
    } else {
        console.error('Modal not found');
        alert('خطأ: لم يتم العثور على النافذة');
    }
}

// إغلاق النافذة
function closeModal() {
    console.log('Closing modal...');
    const modal = document.getElementById('accountModal');
    if (modal) {
        modal.style.display = 'none';
        console.log('Modal closed');
    }
}

// حفظ الحساب
function saveAccount() {
    const email = document.getElementById('emailAddress').value.trim();
    const name = document.getElementById('displayName').value.trim();
    const password = document.getElementById('password').value.trim();
    const smtp = document.getElementById('smtpServer').value.trim();
    const imap = document.getElementById('imapServer').value.trim();
    const smtpPort = document.getElementById('smtpPort').value || 587;
    const imapPort = document.getElementById('imapPort').value || 993;

    if (!email || !name || !password || !smtp || !imap) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // تعطيل الزر أثناء الحفظ
    const saveBtn = document.querySelector('.btn-primary[onclick="saveAccount()"]');
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الحفظ...';
    saveBtn.disabled = true;

    // إعداد البيانات
    const formData = {
        email_address: email,
        display_name: name,
        password: password,
        smtp_server: smtp,
        smtp_port: parseInt(smtpPort),
        smtp_use_tls: true,
        smtp_use_ssl: false,
        imap_server: imap,
        imap_port: parseInt(imapPort),
        imap_use_ssl: true
    };

    // إرسال البيانات إلى الخادم
    fetch('/email/api/account', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ تم حفظ الحساب بنجاح!');
            closeModal();
            // إعادة تحميل الصفحة لإظهار الحساب الجديد
            location.reload();
        } else {
            alert('❌ فشل في حفظ الحساب: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('❌ حدث خطأ في الاتصال بالخادم');
    })
    .finally(() => {
        // إعادة تفعيل الزر
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    });
}

// إغلاق النافذة عند الضغط على Escape
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeModal();
    }
});

// إغلاق النافذة عند النقر خارجها
document.getElementById('accountModal').addEventListener('click', function(event) {
    if (event.target === this) {
        closeModal();
    }
});

// الإعدادات التلقائية للخوادم الشائعة
function setupEmailAutoComplete() {
    const emailInput = document.getElementById('emailAddress');
    if (emailInput) {
        emailInput.addEventListener('blur', function() {
            const email = this.value.trim();
            if (!email) return;

            const domain = email.split('@')[1];
            if (!domain) return;

            const commonSettings = {
                'alselwy.net': {
                    smtp: 'mail.alselwy.net',
                    imap: 'mail.alselwy.net',
                    smtpPort: 587,
                    imapPort: 993
                },
                'gmail.com': {
                    smtp: 'smtp.gmail.com',
                    imap: 'imap.gmail.com',
                    smtpPort: 587,
                    imapPort: 993
                },
                'outlook.com': {
                    smtp: 'smtp-mail.outlook.com',
                    imap: 'outlook.office365.com',
                    smtpPort: 587,
                    imapPort: 993
                },
                'hotmail.com': {
                    smtp: 'smtp-mail.outlook.com',
                    imap: 'outlook.office365.com',
                    smtpPort: 587,
                    imapPort: 993
                },
                'yahoo.com': {
                    smtp: 'smtp.mail.yahoo.com',
                    imap: 'imap.mail.yahoo.com',
                    smtpPort: 587,
                    imapPort: 993
                }
            };

            if (commonSettings[domain]) {
                const settings = commonSettings[domain];
                document.getElementById('smtpServer').value = settings.smtp;
                document.getElementById('imapServer').value = settings.imap;
                document.getElementById('smtpPort').value = settings.smtpPort;
                document.getElementById('imapPort').value = settings.imapPort;

                // إظهار رسالة تأكيد
                const emailGroup = emailInput.closest('.form-group');
                if (emailGroup) {
                    const existingMsg = emailGroup.querySelector('.auto-config-msg');
                    if (existingMsg) existingMsg.remove();

                    const msg = document.createElement('small');
                    msg.className = 'auto-config-msg text-success';
                    msg.innerHTML = `<i class="fas fa-check me-1"></i>تم تطبيق إعدادات ${domain} تلقائياً`;
                    emailGroup.appendChild(msg);

                    setTimeout(() => msg.remove(), 3000);
                }
            }
        });
    }
}

// تشغيل الإعداد التلقائي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setupEmailAutoComplete();
});

// تعديل حساب
function editAccount(accountId) {
    console.log('تعديل الحساب:', accountId);

    // جلب بيانات الحساب
    fetch(`/email/api/account/${accountId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const account = data.account;

                // ملء النموذج بالبيانات الحالية
                document.getElementById('editAccountId').value = account.id;
                document.getElementById('editEmailAddress').value = account.email_address;
                document.getElementById('editDisplayName').value = account.display_name;
                document.getElementById('editSmtpServer').value = account.smtp_server;
                document.getElementById('editSmtpPort').value = account.smtp_port;
                document.getElementById('editSmtpUseTls').checked = account.smtp_use_tls;
                document.getElementById('editSmtpUseSsl').checked = account.smtp_use_ssl;
                document.getElementById('editImapServer').value = account.imap_server;
                document.getElementById('editImapPort').value = account.imap_port;
                document.getElementById('editImapUseSsl').checked = account.imap_use_ssl;

                // إظهار النافذة
                document.getElementById('editAccountModal').style.display = 'flex';
            } else {
                alert('❌ خطأ في جلب بيانات الحساب: ' + data.message);
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('❌ حدث خطأ في جلب بيانات الحساب');
        });
}

// إغلاق نافذة التعديل
function closeEditModal() {
    document.getElementById('editAccountModal').style.display = 'none';
    document.getElementById('editAccountForm').reset();
}

// تحديث الحساب
function updateAccount() {
    const formData = {
        account_id: document.getElementById('editAccountId').value,
        email_address: document.getElementById('editEmailAddress').value,
        display_name: document.getElementById('editDisplayName').value,
        smtp_server: document.getElementById('editSmtpServer').value,
        smtp_port: parseInt(document.getElementById('editSmtpPort').value),
        smtp_use_tls: document.getElementById('editSmtpUseTls').checked,
        smtp_use_ssl: document.getElementById('editSmtpUseSsl').checked,
        imap_server: document.getElementById('editImapServer').value,
        imap_port: parseInt(document.getElementById('editImapPort').value),
        imap_use_ssl: document.getElementById('editImapUseSsl').checked
    };

    // إضافة كلمة المرور إذا تم إدخالها
    const newPassword = document.getElementById('editPassword').value;
    if (newPassword.trim()) {
        formData.password = newPassword;
    }

    console.log('تحديث الحساب:', formData);

    fetch('/email/api/account/update', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ تم تحديث الحساب بنجاح');
            closeEditModal();
            location.reload(); // إعادة تحميل الصفحة لإظهار التغييرات
        } else {
            alert('❌ خطأ في تحديث الحساب: ' + data.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('❌ حدث خطأ في تحديث الحساب');
    });
}

// اختبار الاتصال
function testConnection(accountId) {
    // إظهار نافذة الاختبار
    document.getElementById('testConnectionModal').style.display = 'flex';

    // إعادة تعيين النتائج
    const resultsDiv = document.getElementById('testResults');
    resultsDiv.innerHTML = `
        <div class="test-step">
            <i class="fas fa-spinner fa-spin"></i>
            <span>جاري اختبار الاتصال...</span>
        </div>
    `;

    fetch(`/email/api/test-connection/${accountId}`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        let resultsHTML = '';

        if (data.success) {
            resultsHTML = `
                <div class="test-step success">
                    <i class="fas fa-check-circle"></i>
                    <span>✅ نجح الاتصال بالخادم!</span>
                </div>
                <div class="test-step success">
                    <i class="fas fa-info-circle"></i>
                    <span>${data.message}</span>
                </div>
            `;
        } else {
            resultsHTML = `
                <div class="test-step error">
                    <i class="fas fa-times-circle"></i>
                    <span>❌ فشل الاتصال</span>
                </div>
                <div class="test-step error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>${data.message}</span>
                </div>
            `;

            if (data.details) {
                resultsHTML += `
                    <div class="test-step error">
                        <i class="fas fa-list"></i>
                        <span>تفاصيل الخطأ: ${data.details}</span>
                    </div>
                `;
            }
        }

        resultsDiv.innerHTML = resultsHTML;
    })
    .catch(error => {
        console.error('خطأ:', error);
        resultsDiv.innerHTML = `
            <div class="test-step error">
                <i class="fas fa-times-circle"></i>
                <span>❌ حدث خطأ في اختبار الاتصال</span>
            </div>
        `;
    });
}

// إغلاق نافذة اختبار الاتصال
function closeTestModal() {
    document.getElementById('testConnectionModal').style.display = 'none';
}

// تعيين حساب افتراضي
function setDefault(accountId) {
    if (confirm('هل تريد جعل هذا الحساب افتراضي؟')) {
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التعيين...';
        btn.disabled = true;

        fetch(`/email/api/set-default/${accountId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ تم تعيين الحساب كافتراضي بنجاح!');
                location.reload();
            } else {
                alert('❌ فشل في تعيين الحساب: ' + data.message);
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('❌ حدث خطأ في تعيين الحساب');
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    }
}

// حذف حساب
let accountToDelete = null;

function deleteAccount(accountId) {
    // جلب معلومات الحساب للعرض
    fetch(`/email/api/account/${accountId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                accountToDelete = accountId;
                document.getElementById('deleteAccountInfo').textContent = data.account.email_address;
                document.getElementById('deleteConfirmModal').style.display = 'flex';
            } else {
                alert('❌ خطأ في جلب بيانات الحساب');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('❌ حدث خطأ في جلب بيانات الحساب');
        });
}

// إغلاق نافذة تأكيد الحذف
function closeDeleteModal() {
    document.getElementById('deleteConfirmModal').style.display = 'none';
    accountToDelete = null;
}

// تأكيد حذف الحساب
function confirmDeleteAccount() {
    if (!accountToDelete) return;

    fetch(`/email/api/account/${accountToDelete}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ تم حذف الحساب بنجاح');
            closeDeleteModal();
            location.reload();
        } else {
            alert('❌ خطأ في حذف الحساب: ' + data.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('❌ حدث خطأ في حذف الحساب');
    });
}

// تشخيص الحساب
function debugAccount(accountId) {
    fetch(`/email/api/debug-account/${accountId}`)
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('❌ خطأ في التشخيص: ' + data.error);
        } else {
            const info = `
معلومات الحساب:
- البريد الإلكتروني: ${data.email_address}
- الاسم: ${data.display_name}
- خادم SMTP: ${data.smtp_server}:${data.smtp_port}
- طول كلمة المرور: ${data.password_length}
- بداية كلمة المرور: ${data.password_starts_with}
- نوع كلمة المرور: ${data.password_encrypted_type}
- كلمة المرور فارغة: ${data.password_is_empty}
- كلمة المرور null: ${data.password_is_none}
- كلمة المرور bytes: ${data.password_is_bytes}

اختبار فك التشفير:
- نجح: ${data.decrypt_test.success}
- طول النتيجة: ${data.decrypt_test.result_length}
- رسالة: ${data.decrypt_test.error_message}
            `;
            alert(info);
        }
    })
    .catch(error => {
        alert('❌ حدث خطأ في التشخيص');
    });
}

// وظائف التبويبات
function showTab(tabName) {
    // إخفاء جميع التبويبات
    const tabs = document.querySelectorAll('.tab-content');
    tabs.forEach(tab => {
        tab.classList.remove('active');
    });

    // إزالة الحالة النشطة من جميع الأزرار
    const navBtns = document.querySelectorAll('.nav-btn');
    navBtns.forEach(btn => {
        btn.classList.remove('active');
    });

    // إظهار التبويب المحدد
    const targetTab = document.getElementById(tabName + '-tab');
    if (targetTab) {
        targetTab.classList.add('active');
    }

    // تفعيل الزر المحدد
    event.target.classList.add('active');
}

// حفظ الإعدادات العامة
function saveGeneralSettings() {
    console.log('🔧 بدء حفظ الإعدادات العامة...');

    try {
        // جلب القيم من النماذج باستخدام IDs
        const languageSelect = document.getElementById('languageSelect');
        const timezoneSelect = document.getElementById('timezoneSelect');
        const messagesSelect = document.getElementById('messagesPerPageSelect');
        const sortSelect = document.getElementById('sortOrderSelect');
        const autoRefreshCheckbox = document.getElementById('autoRefresh');

        console.log('🔍 العناصر الموجودة:', {
            language: languageSelect ? 'موجود' : 'غير موجود',
            timezone: timezoneSelect ? 'موجود' : 'غير موجود',
            messages: messagesSelect ? 'موجود' : 'غير موجود',
            sort: sortSelect ? 'موجود' : 'غير موجود',
            autoRefresh: autoRefreshCheckbox ? 'موجود' : 'غير موجود'
        });

        const settings = {
            language: languageSelect ? languageSelect.value : 'ar',
            timezone: timezoneSelect ? timezoneSelect.value : 'Asia/Riyadh',
            messagesPerPage: messagesSelect ? messagesSelect.value : '25',
            sortOrder: sortSelect ? sortSelect.value : 'newest',
            autoRefresh: autoRefreshCheckbox ? autoRefreshCheckbox.checked : true
        };

        console.log('📋 الإعدادات المجمعة:', settings);

        // إظهار مؤشر التحميل
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
        btn.disabled = true;

        // محاكاة حفظ الإعدادات مع إرسال للخادم
        fetch('/email/api/save-general-settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(settings)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ تم حفظ الإعدادات العامة بنجاح!');
                console.log('✅ تم حفظ الإعدادات بنجاح');
            } else {
                alert('❌ خطأ في حفظ الإعدادات: ' + data.message);
            }
        })
        .catch(error => {
            console.error('❌ خطأ في الشبكة:', error);
            // محاكاة النجاح للاختبار
            alert('✅ تم حفظ الإعدادات العامة بنجاح! (وضع المحاكاة)');
            console.log('✅ تم حفظ الإعدادات بنجاح (محاكاة)');
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });

    } catch (error) {
        console.error('❌ خطأ في حفظ الإعدادات:', error);
        alert('❌ حدث خطأ في حفظ الإعدادات: ' + error.message);
    }
}

// إعادة تعيين الإعدادات العامة
function resetGeneralSettings() {
    if (confirm('هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
        // إعادة تعيين القيم
        document.getElementById('autoRefresh').checked = true;
        alert('✅ تم إعادة تعيين الإعدادات!');
    }
}

// حفظ إعدادات الأمان
function saveSecuritySettings() {
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    if (newPassword && newPassword !== confirmPassword) {
        alert('❌ كلمة المرور الجديدة وتأكيدها غير متطابقين');
        return;
    }

    if (newPassword && newPassword.length < 6) {
        alert('❌ كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        return;
    }

    const settings = {
        currentPassword: currentPassword,
        newPassword: newPassword,
        twoFactorAuth: document.getElementById('twoFactorAuth').checked,
        loginNotifications: document.getElementById('loginNotifications').checked
    };

    console.log('حفظ إعدادات الأمان:', settings);

    // محاكاة حفظ الإعدادات
    setTimeout(() => {
        alert('✅ تم حفظ إعدادات الأمان بنجاح!');
        // مسح كلمات المرور
        document.getElementById('currentPassword').value = '';
        document.getElementById('newPassword').value = '';
        document.getElementById('confirmPassword').value = '';
    }, 500);
}

// حفظ إعدادات الإشعارات
function saveNotificationSettings() {
    const settings = {
        newEmailNotif: document.getElementById('newEmailNotif').checked,
        importantEmailNotif: document.getElementById('importantEmailNotif').checked,
        soundNotif: document.getElementById('soundNotif').checked,
        systemNotif: document.getElementById('systemNotif').checked,
        securityNotif: document.getElementById('securityNotif').checked
    };

    console.log('حفظ إعدادات الإشعارات:', settings);

    // محاكاة حفظ الإعدادات
    setTimeout(() => {
        alert('✅ تم حفظ إعدادات الإشعارات بنجاح!');
    }, 500);
}

// وظائف التنقل
function goToInbox() {
    console.log('🔄 الانتقال لصندوق الوارد...');
    window.location.href = '/email/inbox';
}

function goToDashboard() {
    console.log('🔄 الانتقال للوحة التحكم...');
    window.location.href = '/dashboard';
}

function goToHome() {
    console.log('🔄 الانتقال للصفحة الرئيسية...');
    window.location.href = '/';
}

// إضافة اختصارات لوحة المفاتيح
document.addEventListener('keydown', function(event) {
    // Ctrl + B للعودة لصندوق الوارد
    if (event.ctrlKey && event.key === 'b') {
        event.preventDefault();
        goToInbox();
    }

    // Ctrl + H للذهاب للوحة التحكم
    if (event.ctrlKey && event.key === 'h') {
        event.preventDefault();
        goToDashboard();
    }

    // ESC للعودة لصندوق الوارد
    if (event.key === 'Escape') {
        goToInbox();
    }
});







console.log('All functions loaded');
console.log('🎯 اختصارات لوحة المفاتيح:');
console.log('   Ctrl + B: العودة لصندوق الوارد');
console.log('   Ctrl + H: الذهاب للوحة التحكم');
console.log('   ESC: العودة لصندوق الوارد');
</script>
</body>
</html>
