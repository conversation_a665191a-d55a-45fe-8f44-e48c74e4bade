# -*- coding: utf-8 -*-
"""
مسارات استلام البضائع
Goods Receipt Routes
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, abort
from flask_login import login_required, current_user
from app.goods_receipt import bp
from app import db
from app.models import GoodsReceipt, GoodsReceiptItem, PurchaseOrder, PurchaseOrderItem, Item, StockMovement
from app.goods_receipt.forms import GoodsReceiptForm, GoodsReceiptUpdateForm, GoodsReceiptSearchForm
from datetime import datetime
from sqlalchemy import or_, and_
from decimal import Decimal

@bp.route('/')
def index():
    """صفحة قائمة استلام البضائع"""
    page = request.args.get('page', 1, type=int)
    search_form = GoodsReceiptSearchForm()
    
    # بناء الاستعلام الأساسي
    query = GoodsReceipt.query
    
    # تطبيق الفلاتر
    if request.args.get('search_term'):
        search_term = f"%{request.args.get('search_term')}%"
        query = query.filter(
            or_(
                GoodsReceipt.receipt_number.like(search_term),
                GoodsReceipt.delivery_note_number.like(search_term),
                GoodsReceipt.notes.like(search_term)
            )
        )
    
    if request.args.get('status'):
        query = query.filter(GoodsReceipt.status == request.args.get('status'))
    
    if request.args.get('quality_status'):
        query = query.filter(GoodsReceipt.quality_check_status == request.args.get('quality_status'))
    
    if request.args.get('date_from'):
        date_from = datetime.strptime(request.args.get('date_from'), '%Y-%m-%d').date()
        query = query.filter(GoodsReceipt.receipt_date >= date_from)
    
    if request.args.get('date_to'):
        date_to = datetime.strptime(request.args.get('date_to'), '%Y-%m-%d').date()
        query = query.filter(GoodsReceipt.receipt_date <= date_to)
    
    # ترتيب النتائج
    query = query.order_by(GoodsReceipt.created_at.desc())
    
    # تطبيق التصفح
    receipts = query.paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('goods_receipt/index.html',
                         receipts=receipts,
                         search_form=search_form,
                         title='استلام البضائع')

@bp.route('/new', methods=['GET', 'POST'])
def new():
    """إنشاء استلام بضائع جديد"""
    form = GoodsReceiptForm()
    
    if form.validate_on_submit():
        try:
            # إنشاء رقم استلام جديد
            receipt_number = generate_receipt_number()
            
            # إنشاء استلام البضائع
            receipt = GoodsReceipt(
                receipt_number=receipt_number,
                purchase_order_id=form.purchase_order_id.data,
                receipt_date=form.receipt_date.data,
                delivery_note_number=form.delivery_note_number.data,
                received_by=form.received_by.data,
                quality_check_status=form.quality_check_status.data,
                storage_location=form.storage_location.data,
                notes=form.notes.data,
                status='draft',
                created_by=current_user.id
            )
            
            db.session.add(receipt)
            db.session.commit()
            
            flash(f'تم إنشاء استلام البضائع {receipt_number} بنجاح', 'success')
            return redirect(url_for('goods_receipt.view', id=receipt.id))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إنشاء استلام البضائع', 'error')
            print(f"Error creating goods receipt: {e}")
    
    return render_template('goods_receipt/new.html',
                         form=form,
                         title='إنشاء استلام بضائع جديد')

@bp.route('/<int:id>')
def view(id):
    """عرض تفاصيل استلام البضائع"""
    receipt = GoodsReceipt.query.get_or_404(id)
    
    return render_template('goods_receipt/view.html',
                         receipt=receipt,
                         title=f'استلام البضائع {receipt.receipt_number}')

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
def edit(id):
    """تعديل استلام البضائع"""
    receipt = GoodsReceipt.query.get_or_404(id)
    form = GoodsReceiptUpdateForm(obj=receipt)
    
    if form.validate_on_submit():
        try:
            receipt.status = form.status.data
            receipt.quality_check_status = form.quality_check_status.data
            receipt.storage_location = form.storage_location.data
            receipt.notes = form.notes.data
            receipt.updated_at = datetime.utcnow()
            
            db.session.commit()
            
            flash('تم تحديث استلام البضائع بنجاح', 'success')
            return redirect(url_for('goods_receipt.view', id=receipt.id))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث استلام البضائع', 'error')
            print(f"Error updating goods receipt: {e}")
    
    return render_template('goods_receipt/edit.html',
                         form=form,
                         receipt=receipt,
                         title=f'تعديل استلام البضائع {receipt.receipt_number}')

@bp.route('/<int:id>/complete', methods=['POST'])
def complete(id):
    """إكمال استلام البضائع وتحديث المخزون"""
    receipt = GoodsReceipt.query.get_or_404(id)
    
    if receipt.status != 'draft':
        flash('لا يمكن إكمال هذا الاستلام', 'error')
        return redirect(url_for('goods_receipt.view', id=id))
    
    try:
        # تحديث حالة الاستلام
        receipt.status = 'completed'
        receipt.completed_at = datetime.utcnow()
        receipt.completed_by = current_user.id
        receipt.updated_at = datetime.utcnow()
        
        # تحديث المخزون لكل عنصر
        for item in receipt.items:
            if item.received_quantity > 0:
                # إنشاء حركة مخزون للكمية المستلمة
                stock_movement = StockMovement(
                    item_id=item.item_id,
                    movement_type='in',
                    quantity=item.received_quantity,
                    unit_price=item.unit_price,
                    reference_type='goods_receipt',
                    reference_id=receipt.id,
                    notes=f'استلام من أمر الشراء {receipt.purchase_order.order_number}',
                    created_by=current_user.id
                )
                db.session.add(stock_movement)
                
                # تحديث رصيد الصنف
                if item.item:
                    item.item.current_stock = (item.item.current_stock or 0) + item.received_quantity
        
        # تحديث حالة أمر الشراء
        purchase_order = receipt.purchase_order
        if purchase_order:
            # فحص إذا كانت جميع الكميات تم استلامها
            all_received = True
            partially_received = False
            
            for po_item in purchase_order.items:
                total_received = sum(
                    gr_item.received_quantity 
                    for gr in purchase_order.goods_receipts 
                    for gr_item in gr.items 
                    if gr_item.item_id == po_item.item_id and gr.status == 'completed'
                )
                
                if total_received < po_item.quantity:
                    all_received = False
                if total_received > 0:
                    partially_received = True
            
            if all_received:
                purchase_order.status = 'delivered'
                purchase_order.actual_delivery_date = receipt.receipt_date
            elif partially_received:
                purchase_order.status = 'partially_delivered'
        
        db.session.commit()
        
        flash('تم إكمال استلام البضائع وتحديث المخزون بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء إكمال استلام البضائع', 'error')
        print(f"Error completing goods receipt: {e}")
    
    return redirect(url_for('goods_receipt.view', id=id))

@bp.route('/<int:id>/cancel', methods=['POST'])
def cancel(id):
    """إلغاء استلام البضائع"""
    receipt = GoodsReceipt.query.get_or_404(id)
    
    if receipt.status == 'completed':
        flash('لا يمكن إلغاء استلام مكتمل', 'error')
        return redirect(url_for('goods_receipt.view', id=id))
    
    try:
        receipt.status = 'cancelled'
        receipt.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        flash('تم إلغاء استلام البضائع بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء إلغاء استلام البضائع', 'error')
        print(f"Error cancelling goods receipt: {e}")
    
    return redirect(url_for('goods_receipt.view', id=id))

@bp.route('/<int:id>/print')
def print_receipt(id):
    """طباعة استلام البضائع"""
    receipt = GoodsReceipt.query.get_or_404(id)
    
    return render_template('goods_receipt/print.html',
                         receipt=receipt,
                         title=f'طباعة استلام البضائع {receipt.receipt_number}')

def generate_receipt_number():
    """إنشاء رقم استلام بضائع جديد"""
    from datetime import datetime
    
    # الحصول على آخر استلام في نفس السنة
    current_year = datetime.now().year
    year_prefix = f"GR{current_year}"
    
    last_receipt = GoodsReceipt.query.filter(
        GoodsReceipt.receipt_number.like(f"{year_prefix}%")
    ).order_by(GoodsReceipt.id.desc()).first()
    
    if last_receipt:
        # استخراج الرقم التسلسلي من آخر استلام
        try:
            last_number = int(last_receipt.receipt_number.split('-')[-1])
            new_number = last_number + 1
        except:
            new_number = 1
    else:
        new_number = 1
    
    return f"{year_prefix}-{new_number:04d}"
