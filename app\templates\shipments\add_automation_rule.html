{% extends "base.html" %}

{% block title %}إضافة قاعدة أتمتة جديدة - النظام المحاسبي المتقدم{% endblock %}

{% block extra_css %}
<style>
.add-rule-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.rule-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: none;
    margin-bottom: 20px;
}

.page-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    color: white;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
}

.btn-modern {
    border-radius: 10px;
    padding: 12px 24px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.rule-preview {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.preview-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
}

.preview-content {
    color: #6c757d;
    font-style: italic;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

/* تحسينات للواجهة الجديدة */
.condition-item {
    transition: all 0.3s ease;
    cursor: pointer;
}

.condition-item:hover {
    background-color: #f8f9fa;
    border-color: #0d6efd !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.condition-item .form-check-input:checked + .form-check-label {
    background-color: #e7f3ff;
    border-radius: 8px;
    padding: 8px;
}

.selected-condition-item {
    transition: all 0.3s ease;
    border-left: 4px solid #28a745 !important;
}

.selected-condition-item:hover {
    background-color: #f8f9fa !important;
}

.badge {
    font-size: 0.75em;
}

.card-header h6 {
    margin: 0;
    font-weight: 600;
}
</style>
{% endblock %}

{% block content %}
<div class="add-rule-container">
    <div class="container">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-plus-circle me-3"></i>
                        إضافة قاعدة أتمتة جديدة
                    </h1>
                    <p class="mb-0 opacity-75">إنشاء قاعدة أتمتة مخصصة لتحسين كفاءة العمليات</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{{ url_for('shipments.automation_dashboard') }}" class="btn btn-outline-light btn-modern">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة الأتمتة
                    </a>
                </div>
            </div>
        </div>

        <!-- نموذج إضافة القاعدة -->
        <div class="rule-card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-cog me-2"></i>
                    تفاصيل قاعدة الأتمتة
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('shipments.save_automation_rule') }}">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="rule_name" class="form-label">اسم القاعدة *</label>
                                <input type="text" class="form-control" id="rule_name" name="rule_name" 
                                       placeholder="مثال: إنشاء أمر تسليم عند وصول الشحنة" required>
                                <small class="form-text text-muted">اسم وصفي للقاعدة يوضح وظيفتها</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="rule_type" class="form-label">نوع القاعدة *</label>
                                <select class="form-select" id="rule_type" name="rule_type" required onchange="updateRuleFields()">
                                    <option value="">اختر نوع القاعدة</option>
                                    <option value="SIMPLE_RULE">قاعدة بسيطة (شرط + إجراء)</option>
                                    <option value="CONDITION_ONLY">شرط فقط (للمراقبة)</option>
                                    <option value="ACTION_ONLY">إجراء فقط (تنفيذ مباشر)</option>
                                    <option value="SCHEDULED">قاعدة مجدولة (مبنية على الوقت)</option>
                                </select>
                                <small class="form-text text-muted mt-2">
                                    <span id="rule_type_description"></span>
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="trigger_condition_section">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="trigger_condition" class="form-label">شرط التشغيل *</label>
                                <select class="form-select" id="trigger_condition" name="trigger_condition" required onchange="updateConditionValue()">
                                    <option value="">اختر نوع القاعدة أولاً</option>
                                </select>
                                <small class="form-text text-muted">الشرط الذي يؤدي لتشغيل القاعدة</small>
                            </div>
                        </div>

                    </div>

                    <!-- حقل قيمة الشرط الديناميكي -->
                    <div class="row" id="condition_value_row" style="display: none;">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="condition_value" class="form-label" id="condition_value_label">قيمة الشرط *</label>
                                <div id="condition_value_container">
                                    <!-- سيتم ملء هذا الحقل ديناميكياً -->
                                </div>
                                <small class="form-text text-muted" id="condition_value_help">اختر القيمة المناسبة للشرط</small>
                            </div>
                        </div>
                    </div>

                    <!-- قسم ربط قواعد الشروط للإجراءات - محسن -->
                    <div class="row" id="condition_rules_section" style="display: none;">
                        <div class="col-md-12">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-link me-2"></i>ربط بقواعد الشروط الموجودة
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <!-- خيارات الربط -->
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">طريقة الربط:</label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="link_mode" id="link_mode_any" value="any" checked>
                                                    <label class="form-check-label" for="link_mode_any">
                                                        <strong>أي شرط (OR)</strong><br>
                                                        <small class="text-muted">تنفيذ الإجراء عند تحقق أي من الشروط المختارة</small>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="link_mode" id="link_mode_all" value="all">
                                                    <label class="form-check-label" for="link_mode_all">
                                                        <strong>جميع الشروط (AND)</strong><br>
                                                        <small class="text-muted">تنفيذ الإجراء فقط عند تحقق جميع الشروط المختارة</small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- قائمة الشروط المتاحة -->
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">الشروط المتاحة:</label>
                                        <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                            <div id="available_conditions_list">
                                                <!-- سيتم ملء هذه القائمة ديناميكياً -->
                                                <div class="text-center text-muted py-3">
                                                    <i class="fas fa-spinner fa-spin me-2"></i>جاري تحميل الشروط المتاحة...
                                                </div>
                                            </div>
                                        </div>
                                        <small class="form-text text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            اختر الشروط التي تريد ربطها بهذا الإجراء. يمكنك اختيار أكثر من شرط.
                                        </small>
                                    </div>

                                    <!-- الشروط المختارة -->
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">الشروط المختارة:</label>
                                        <div class="border rounded p-3 bg-light" style="min-height: 60px;">
                                            <div id="selected_conditions_list">
                                                <div class="text-muted text-center py-2">
                                                    <i class="fas fa-hand-pointer me-2"></i>لم يتم اختيار أي شروط بعد
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- حقل مخفي لحفظ الشروط المختارة -->
                                    <input type="hidden" id="linked_condition_rules" name="linked_condition_rules" value="">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- رسالة توضيحية للإجراء فقط -->
                    <div class="alert alert-warning" id="action_only_message" style="display: none;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تنبيه:</strong> هذا إجراء مستقل. إذا لم تربطه بقواعد شروط، سيتم تنفيذه فور تفعيل القاعدة. لربطه بشروط محددة، اختر قواعد الشروط من القائمة أعلاه.
                    </div>

                    <div class="row" id="action_section">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="action_type" class="form-label">نوع الإجراء *</label>
                                <select class="form-select" id="action_type" name="action_type" required>
                                    <option value="">اختر نوع الإجراء</option>
                                    <option value="CREATE_DELIVERY_ORDER_WITH_AGENT">إنشاء أمر تسليم وتعيين مخلص</option>
                                    <option value="SEND_NOTIFICATION">إرسال إشعار</option>
                                    <option value="UPDATE_STATUS">تحديث الحالة</option>
                                    <option value="UPDATE_RATINGS">تحديث التقييمات</option>
                                    <option value="GENERATE_REPORT">إنشاء تقرير</option>
                                    <option value="SCHEDULE_TASK">جدولة مهمة</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- قسم اختيار المخلص - تم نقله هنا ليظهر قبل مستوى الأولوية -->
                    <div class="row" id="agent_selection_section" style="display: none;">
                        <div class="col-md-12">
                            <div class="card border-success mb-3">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-user-tie me-2"></i>اختيار المخلص
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="selected_agent" class="form-label">اختيار المخلص *</label>
                                                <select class="form-select" id="selected_agent" name="selected_agent" required onchange="updateAgentInfo()">
                                                    <option value="">اختر المخلص</option>
                                                    <option value="AUTO">اختيار تلقائي (الأفضل تقييماً)</option>
                                                    {% for agent in agents %}
                                                    <option value="{{ agent.id }}">{{ agent.agent_name }} ({{ agent.rating }}⭐)</option>
                                                    {% endfor %}
                                                </select>
                                                <small class="form-text text-muted">اختر مخلص محدد أو اتركه للاختيار التلقائي</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="mb-3">
                                                <label for="agent_branch" class="form-label">الفرع</label>
                                                <input type="text" class="form-control" id="agent_branch" name="agent_branch_display" readonly>
                                                <input type="hidden" id="agent_branch_id" name="agent_branch_id">
                                                <small class="form-text text-muted">فرع المخلص المختار</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="mb-3">
                                                <label for="agent_port" class="form-label">المنفذ الجمركي</label>
                                                <input type="text" class="form-control" id="agent_port" name="agent_port_display" readonly>
                                                <input type="hidden" id="agent_port_id" name="agent_port_id">
                                                <small class="form-text text-muted">منفذ المخلص المختار</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="agent_assignment_notes" class="form-label">ملاحظات تعيين المخلص</label>
                                                <textarea class="form-control" id="agent_assignment_notes" name="agent_assignment_notes" rows="2" placeholder="ملاحظات إضافية حول تعيين المخلص..."></textarea>
                                                <small class="form-text text-muted">ملاحظات اختيارية حول سبب اختيار هذا المخلص</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قسم إعدادات الإشعارات - جديد -->
                    <div class="row" id="notification_settings_section" style="display: none;">
                        <div class="col-md-12">
                            <div class="card border-info mb-3">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-bell me-2"></i>إعدادات الإشعارات
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <!-- اختيار المستلمين -->
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <label class="form-label fw-bold">مجموعات جهات الاتصال</label>
                                            <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                                <div class="contact-groups-container">
                                                    <!-- سيتم ملؤها بـ JavaScript -->
                                                    <div class="text-center text-muted py-3">
                                                        <i class="fas fa-spinner fa-spin me-2"></i>جاري تحميل مجموعات جهات الاتصال...
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label fw-bold">جهات اتصال مخصصة</label>
                                            <div class="d-flex align-items-center mb-2">
                                                <button type="button" class="btn btn-outline-primary select-custom-contacts-btn">
                                                    <i class="fas fa-address-book me-2"></i>
                                                    اختيار جهات اتصال
                                                </button>
                                                <span class="ms-2 text-muted" id="customContactsCount">0 محدد</span>
                                            </div>
                                            <div id="selectedContactsDisplay" class="border rounded p-3" style="min-height: 100px; max-height: 200px; overflow-y: auto;">
                                                <p class="text-muted">لم يتم اختيار أي جهة اتصال</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- المجموعات المختارة -->
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">المجموعات المختارة</label>
                                        <div id="selectedGroupsDisplay" class="border rounded p-2" style="min-height: 50px;">
                                            <p class="text-muted">لم يتم اختيار أي مجموعة</p>
                                        </div>
                                    </div>

                                    <!-- قنوات الإشعار -->
                                    <div class="mb-4">
                                        <label class="form-label fw-bold">قنوات الإشعار</label>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input notification-channel-checkbox"
                                                           type="checkbox" value="SMS" id="channel_SMS" checked>
                                                    <label class="form-check-label" for="channel_SMS">
                                                        <i class="fas fa-sms text-success me-2"></i>
                                                        رسائل SMS
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input notification-channel-checkbox"
                                                           type="checkbox" value="EMAIL" id="channel_EMAIL" checked>
                                                    <label class="form-check-label" for="channel_EMAIL">
                                                        <i class="fas fa-envelope text-primary me-2"></i>
                                                        البريد الإلكتروني
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input notification-channel-checkbox"
                                                           type="checkbox" value="WHATSAPP" id="channel_WHATSAPP">
                                                    <label class="form-check-label" for="channel_WHATSAPP">
                                                        <i class="fab fa-whatsapp text-success me-2"></i>
                                                        واتساب
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- قالب الرسالة -->
                                    <div class="mb-4">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <label class="form-label fw-bold">نص الرسالة</label>
                                                <textarea class="form-control" id="messageTemplate" name="message_template" rows="4"
                                                          placeholder="أدخل نص الرسالة...">عزيزي العميل، تم تغيير حالة شحنتكم رقم {shipment_id} إلى "{new_status}".
للاستفسار اتصل بنا على: {company_phone}

شركة الفجيحي للشحن</textarea>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label fw-bold">المتغيرات المتاحة</label>
                                                <div class="border rounded p-3" style="max-height: 150px; overflow-y: auto;">
                                                    <small class="text-muted">
                                                        <div><code>{shipment_id}</code> - رقم الشحنة</div>
                                                        <div><code>{new_status}</code> - الحالة الجديدة</div>
                                                        <div><code>{old_status}</code> - الحالة السابقة</div>
                                                        <div><code>{customer_name}</code> - اسم العميل</div>
                                                        <div><code>{company_phone}</code> - هاتف الشركة</div>
                                                        <div><code>{tracking_url}</code> - رابط التتبع</div>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- إعدادات إضافية للإشعار -->
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="notificationPriority" class="form-label">أولوية الإشعار</label>
                                                <select class="form-select" id="notificationPriority" name="notification_priority">
                                                    <option value="1">منخفضة</option>
                                                    <option value="5" selected>متوسطة</option>
                                                    <option value="8">عالية</option>
                                                    <option value="10">عاجلة</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="sendDelay" class="form-label">تأخير الإرسال (بالدقائق)</label>
                                                <input type="number" class="form-control" id="sendDelay" name="send_delay"
                                                       value="0" min="0" max="1440">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check mt-4">
                                                <input class="form-check-input" type="checkbox" id="sendImmediately" name="send_immediately" checked>
                                                <label class="form-check-label" for="sendImmediately">
                                                    إرسال فوري
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- حقول مخفية لحفظ البيانات -->
                                    <input type="hidden" id="selected_contact_groups" name="selected_contact_groups" value="">
                                    <input type="hidden" id="selected_custom_contacts" name="selected_custom_contacts" value="">
                                    <input type="hidden" id="selected_notification_channels" name="selected_notification_channels" value="">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قسم مستوى الأولوية - تم نقله بعد اختيار المخلص -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="priority_level" class="form-label">مستوى الأولوية</label>
                                <select class="form-select" id="priority_level" name="priority_level">
                                    <option value="1">عالية</option>
                                    <option value="2" selected>متوسطة</option>
                                    <option value="3">منخفضة</option>
                                </select>
                                <small class="form-text text-muted">أولوية تنفيذ القاعدة</small>
                            </div>
                        </div>
                    </div>



                    <!-- رسالة توضيحية للشرط فقط -->
                    <div class="alert alert-info" id="condition_only_message" style="display: none;">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> هذا شرط للمراقبة فقط. سيتم تسجيل الأحداث عند تحقق الشرط، ولكن لن يتم تنفيذ أي إجراء تلقائي. يمكنك ربط إجراءات لاحقاً من خلال قواعد منفصلة.
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">وصف القاعدة</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="وصف تفصيلي لما تقوم به هذه القاعدة..."></textarea>
                        <small class="form-text text-muted">وصف اختياري يوضح تفاصيل عمل القاعدة</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                    <label class="form-check-label" for="is_active">
                                        تفعيل القاعدة فور الإنشاء
                                    </label>
                                </div>
                            </div>
                        </div>

                    </div>

                    <!-- معاينة القاعدة -->
                    <div class="rule-preview">
                        <div class="preview-title">
                            <i class="fas fa-eye me-2"></i>
                            معاينة القاعدة
                        </div>
                        <div class="preview-content" id="rule-preview">
                            قم بملء البيانات أعلاه لرؤية معاينة القاعدة
                        </div>
                    </div>

                    <div class="text-end mt-4">
                        <a href="{{ url_for('shipments.automation_dashboard') }}" class="btn btn-secondary btn-modern me-2">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary btn-modern">
                            <i class="fas fa-save me-2"></i>
                            حفظ القاعدة
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- أمثلة على القواعد -->
        <div class="rule-card">
            <div class="card-header bg-info text-white">
                <h4 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    أمثلة على قواعد الأتمتة
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="border rounded p-3 mb-3">
                            <h6 class="text-primary">إنشاء أمر تسليم تلقائي</h6>
                            <small class="text-muted">
                                <strong>النوع:</strong> SHIPMENT_STATUS<br>
                                <strong>الشرط:</strong> arrived_port<br>
                                <strong>الإجراء:</strong> CREATE_DELIVERY_ORDER
                            </small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border rounded p-3 mb-3">
                            <h6 class="text-success">تعيين مخلص تلقائي</h6>
                            <small class="text-muted">
                                <strong>النوع:</strong> DELIVERY_ORDER<br>
                                <strong>الشرط:</strong> order_created<br>
                                <strong>الإجراء:</strong> ASSIGN_AGENT
                            </small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border rounded p-3 mb-3">
                            <h6 class="text-warning">إشعار تأخير</h6>
                            <small class="text-muted">
                                <strong>النوع:</strong> SCHEDULED<br>
                                <strong>الشرط:</strong> daily_check<br>
                                <strong>الإجراء:</strong> SEND_NOTIFICATION
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- تضمين ملف JavaScript لإدارة جهات الاتصال -->
<script src="{{ url_for('static', filename='js/automation-contacts.js') }}"></script>

<!-- تضمين واجهة اختيار جهات الاتصال -->
{% include 'notifications/contacts/automation_selector.html' %}

<script>
// تحديث معاينة القاعدة
function updatePreview() {
    const ruleName = document.getElementById('rule_name').value;
    const ruleType = document.getElementById('rule_type').value;
    const triggerCondition = document.getElementById('trigger_condition').value;
    const actionType = document.getElementById('action_type').value;
    const description = document.getElementById('description').value;
    const isActive = document.getElementById('is_active').checked;
    
    let preview = '';
    
    if (ruleName) {
        preview += `<strong>القاعدة:</strong> ${ruleName}<br>`;
    }
    
    if (ruleType && triggerCondition && actionType) {
        preview += `<strong>الوصف:</strong> عندما يحدث "${triggerCondition}" في "${ruleType}"، سيتم تنفيذ "${actionType}"<br>`;
    }
    
    if (description) {
        preview += `<strong>التفاصيل:</strong> ${description}<br>`;
    }
    
    preview += `<strong>الحالة:</strong> ${isActive ? 'نشطة' : 'معطلة'}`;
    
    if (!preview.trim()) {
        preview = 'قم بملء البيانات أعلاه لرؤية معاينة القاعدة';
    }
    
    document.getElementById('rule-preview').innerHTML = preview;
}

// ربط الأحداث
document.getElementById('rule_name').addEventListener('input', updatePreview);
document.getElementById('rule_type').addEventListener('change', updatePreview);
document.getElementById('trigger_condition').addEventListener('input', updatePreview);
document.getElementById('action_type').addEventListener('change', function() {
    updatePreview();
    updateActionTypeSections();
});
document.getElementById('description').addEventListener('input', updatePreview);
document.getElementById('is_active').addEventListener('change', updatePreview);

// دالة تحديث الأقسام حسب نوع الإجراء
function updateActionTypeSections() {
    const actionType = document.getElementById('action_type').value;
    const agentSection = document.getElementById('agent_selection_section');
    const notificationSection = document.getElementById('notification_settings_section');

    // إخفاء جميع الأقسام أولاً
    if (agentSection) agentSection.style.display = 'none';
    if (notificationSection) notificationSection.style.display = 'none';

    // إدارة خاصية required للحقول
    updateFieldRequirements(actionType);

    // إظهار الأقسام المناسبة حسب نوع الإجراء
    if (actionType === 'CREATE_DELIVERY_ORDER_WITH_AGENT' && agentSection) {
        agentSection.style.display = 'block';
    } else if (actionType === 'SEND_NOTIFICATION' && notificationSection) {
        notificationSection.style.display = 'block';
        // تحميل مجموعات جهات الاتصال
        loadContactGroupsForAutomation();
    }
}

// دالة إدارة خاصية required للحقول حسب نوع الإجراء
function updateFieldRequirements(actionType) {
    // حقول المخلص
    const agentFields = [
        'selected_agent'
    ];

    // حقول الإشعارات (يمكن إضافة المزيد لاحقاً)
    const notificationFields = [
        // لا توجد حقول مطلوبة حالياً للإشعارات
    ];

    // إزالة required من جميع الحقول أولاً
    [...agentFields, ...notificationFields].forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.removeAttribute('required');
        }
    });

    // إضافة required للحقول المناسبة حسب نوع الإجراء
    if (actionType === 'CREATE_DELIVERY_ORDER_WITH_AGENT') {
        agentFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.setAttribute('required', 'required');
            }
        });
    } else if (actionType === 'SEND_NOTIFICATION') {
        notificationFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.setAttribute('required', 'required');
            }
        });
    }
}

// تحديث المعاينة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updatePreview();
    // تحديث متطلبات الحقول عند تحميل الصفحة
    const actionType = document.getElementById('action_type').value;
    if (actionType) {
        updateFieldRequirements(actionType);
    }
});

// التحقق من صحة النموذج
document.querySelector('form').addEventListener('submit', function(e) {
    const ruleType = document.getElementById('rule_type').value;
    const actionType = document.getElementById('action_type').value;

    let requiredFields = ['rule_name', 'rule_type'];

    // تحديد الحقول المطلوبة حسب نوع القاعدة
    if (ruleType === 'CONDITION_ONLY') {
        // شرط فقط - يتطلب شرط التشغيل فقط
        requiredFields.push('trigger_condition');
    } else if (ruleType === 'ACTION_ONLY') {
        // إجراء فقط - يتطلب نوع الإجراء فقط
        requiredFields.push('action_type');
    } else {
        // قاعدة بسيطة أو مجدولة - يتطلب كلاهما
        requiredFields.push('trigger_condition', 'action_type');
    }

    // إضافة حقول مطلوبة حسب نوع الإجراء (فقط للحقول المرئية)
    if (actionType === 'CREATE_DELIVERY_ORDER_WITH_AGENT') {
        const agentSection = document.getElementById('agent_selection_section');
        if (agentSection && agentSection.style.display !== 'none') {
            requiredFields.push('selected_agent');
        }
    }

    let isValid = true;
    let missingFields = [];

    // التحقق من الحقول الأساسية
    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        // فحص أن الحقل موجود ومرئي
        if (field && field.offsetParent !== null) {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;

                // إضافة اسم الحقل للرسالة
                switch(fieldId) {
                    case 'rule_name':
                        missingFields.push('اسم القاعدة');
                        break;
                    case 'rule_type':
                        missingFields.push('نوع القاعدة');
                        break;
                    case 'trigger_condition':
                        missingFields.push('شرط التشغيل');
                        break;
                    case 'action_type':
                        missingFields.push('نوع الإجراء');
                        break;
                    case 'selected_agent':
                        missingFields.push('اختيار المخلص');
                        break;
                }
            } else {
                field.classList.remove('is-invalid');
            }
        }
    });

    // تحقق خاص للإشعارات
    if (actionType === 'SEND_NOTIFICATION') {
        const notificationSection = document.getElementById('notification_settings_section');
        if (notificationSection && notificationSection.style.display !== 'none') {

            // التحقق من اختيار جهات الاتصال أو المجموعات
            const hasContactGroups = document.querySelectorAll('.contact-group-checkbox:checked').length > 0;
            const hasCustomContacts = selectedContactsForAutomation && selectedContactsForAutomation.length > 0;

            if (!hasContactGroups && !hasCustomContacts) {
                isValid = false;
                missingFields.push('اختيار جهات الاتصال أو المجموعات');
            }

            // التحقق من اختيار قنوات الإشعار
            const hasChannels = document.querySelectorAll('.notification-channel-checkbox:checked').length > 0;
            if (!hasChannels) {
                isValid = false;
                missingFields.push('اختيار قنوات الإشعار');
            }

            // التحقق من وجود نص الرسالة
            const messageTemplate = document.getElementById('messageTemplate');
            if (messageTemplate && !messageTemplate.value.trim()) {
                isValid = false;
                missingFields.push('نص الرسالة');
                messageTemplate.classList.add('is-invalid');
            } else if (messageTemplate) {
                messageTemplate.classList.remove('is-invalid');
            }
        }
    }

    if (!isValid) {
        e.preventDefault();
        showValidationError(`يرجى ملء الحقول المطلوبة التالية:\n• ${missingFields.join('\n• ')}`);
    }
});

// دالة عرض رسالة خطأ التحقق
function showValidationError(message) {
    // إزالة أي رسائل خطأ سابقة
    const existingAlerts = document.querySelectorAll('.validation-error-alert');
    existingAlerts.forEach(alert => alert.remove());

    // إنشاء رسالة خطأ جديدة
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show mt-3 validation-error-alert';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        <pre style="margin: 0; white-space: pre-wrap; font-family: inherit;">${message}</pre>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إدراج الرسالة في أعلى النموذج
    const form = document.querySelector('form');
    form.insertBefore(alertDiv, form.firstChild);

    // التمرير إلى أعلى النموذج
    alertDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

    // إزالة الرسالة تلقائياً بعد 7 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 7000);
}

// بيانات حالات الشحنة من قاعدة البيانات
const shipmentStatuses = {{ shipment_statuses|tojson|safe }};

// بيانات قواعد الشروط من قاعدة البيانات
const conditionRules = {{ condition_rules|tojson|safe }};

// دالة للحصول على خيارات حالات الشحنة (محدثة لاستخدام API)
function getShipmentStatusOptions() {
    // هذه الدالة لم تعد مستخدمة - تم استبدالها بـ loadShipmentStatuses()
    // نحتفظ بها للتوافق مع الكود القديم
    return '<option value="">جاري تحميل الحالات...</option>';
}

// دالة تحديث شرط التشغيل حسب نوع القاعدة
function updateRuleFields() {
    console.log('🔄 تحديث حقول القاعدة...');

    const ruleType = document.getElementById('rule_type').value;
    const triggerCondition = document.getElementById('trigger_condition');
    const triggerConditionSection = document.getElementById('trigger_condition_section');
    const actionSection = document.getElementById('action_section');
    const actionType = document.getElementById('action_type');

    console.log('📋 نوع القاعدة المختار:', ruleType);

    // مسح الخيارات السابقة
    triggerCondition.innerHTML = '<option value="">اختر شرط التشغيل</option>';

    if (!ruleType) {
        triggerCondition.innerHTML = '<option value="">اختر نوع القاعدة أولاً</option>';
        triggerConditionSection.style.display = 'block';
        actionSection.style.display = 'block';
        triggerCondition.required = true;
        triggerCondition.setAttribute('required', 'required');
        actionType.required = true;
        actionType.setAttribute('required', 'required');
        console.log('⚠️ لم يتم اختيار نوع قاعدة');
        return;
    }

    // إدارة إظهار/إخفاء الأقسام والرسائل التوضيحية
    const conditionOnlyMessage = document.getElementById('condition_only_message');
    const conditionRulesSection = document.getElementById('condition_rules_section');
    const actionOnlyMessage = document.getElementById('action_only_message');
    const agentSelectionSection = document.getElementById('agent_selection_section');

    if (ruleType === 'CONDITION_ONLY') {
        // شرط فقط
        triggerConditionSection.style.display = 'block';
        triggerCondition.required = true;
        triggerCondition.setAttribute('required', 'required');
        actionSection.style.display = 'none';
        actionType.required = false;
        actionType.removeAttribute('required');
        actionType.value = '';
        if (conditionOnlyMessage) conditionOnlyMessage.style.display = 'block';
        if (conditionRulesSection) conditionRulesSection.style.display = 'none';
        if (actionOnlyMessage) actionOnlyMessage.style.display = 'none';
        if (agentSelectionSection) agentSelectionSection.style.display = 'none';
        console.log('📋 تم تكوين: شرط فقط');
    } else if (ruleType === 'ACTION_ONLY') {
        // إجراء فقط
        triggerConditionSection.style.display = 'none';
        triggerCondition.required = false;
        triggerCondition.removeAttribute('required');
        triggerCondition.value = '';
        actionSection.style.display = 'block';
        actionType.required = true;
        actionType.setAttribute('required', 'required');
        if (conditionOnlyMessage) conditionOnlyMessage.style.display = 'none';
        if (conditionRulesSection) conditionRulesSection.style.display = 'none';
        if (actionOnlyMessage) actionOnlyMessage.style.display = 'block';
        if (agentSelectionSection) agentSelectionSection.style.display = 'none';
        console.log('📋 تم تكوين: إجراء فقط');
    } else {
        // قاعدة بسيطة أو مجدولة
        triggerConditionSection.style.display = 'block';
        triggerCondition.required = true;
        triggerCondition.setAttribute('required', 'required');
        actionSection.style.display = 'block';
        actionType.required = true;
        actionType.setAttribute('required', 'required');
        if (conditionOnlyMessage) conditionOnlyMessage.style.display = 'none';
        if (conditionRulesSection) conditionRulesSection.style.display = 'none';
        if (actionOnlyMessage) actionOnlyMessage.style.display = 'none';
        if (agentSelectionSection) agentSelectionSection.style.display = 'none';
        console.log('📋 تم تكوين: قاعدة كاملة');
    }

    // إضافة مستمع لتغيير نوع الإجراء
    const actionTypeSelect = document.getElementById('action_type');
    if (actionTypeSelect) {
        actionTypeSelect.addEventListener('change', function() {
            const actionType = this.value;
            const agentSelectionSection = document.getElementById('agent_selection_section');

            // الإجراءات التي تتطلب تعيين مخلص
            const actionsRequiringAgent = [
                'CREATE_DELIVERY_ORDER_WITH_AGENT',
                'AUTO_DELIVERY_WITH_AGENT'
            ];

            if (actionsRequiringAgent.includes(actionType)) {
                agentSelectionSection.style.display = 'block';
            } else {
                agentSelectionSection.style.display = 'none';
            }
        });
    }

    // إضافة الخيارات حسب نوع القاعدة
    let options = [];
    let description = '';

    switch(ruleType) {
        case 'SIMPLE_RULE':
            description = 'قاعدة كاملة تجمع بين الشرط والإجراء - الأكثر استخداماً';
            options = [
                { value: 'STATUS_CHANGE_ACTION', text: 'عند تغيير حالة الشحنة' },
                { value: 'ARRIVAL_NOTIFICATION', text: 'إشعار عند الوصول' },
                { value: 'AUTO_DELIVERY_WITH_AGENT', text: 'إنشاء أمر تسليم وتعيين مخلص عند الجاهزية' },
                { value: 'PAYMENT_REMINDER', text: 'تذكير بالدفع' },
                { value: 'CUSTOMS_ALERT', text: 'تنبيه التخليص الجمركي' }
            ];
            break;

        case 'CONDITION_ONLY':
            description = 'مراقبة شرط معين دون تنفيذ إجراء تلقائي';
            options = [
                { value: 'SHIPMENT_ARRIVAL', text: 'وصول الشحنة' },
                { value: 'STATUS_CHANGE', text: 'تغيير الحالة' },
                { value: 'PAYMENT_STATUS', text: 'حالة الدفع' },
                { value: 'CUSTOMS_STATUS', text: 'حالة التخليص الجمركي' }
            ];
            break;

        case 'ACTION_ONLY':
            description = 'تنفيذ إجراء مباشر دون انتظار شرط معين';
            // لا حاجة لشروط في هذا النوع - سيتم إخفاء قسم الشروط
            options = [];
            break;

        case 'SCHEDULED':
            description = 'قاعدة مبنية على الوقت - تنفذ في أوقات محددة';
            options = [
                { value: 'DAILY_REPORT', text: 'تقرير يومي' },
                { value: 'WEEKLY_SUMMARY', text: 'ملخص أسبوعي' },
                { value: 'MONTHLY_ANALYSIS', text: 'تحليل شهري' },
                { value: 'REMINDER_SCHEDULE', text: 'تذكيرات مجدولة' }
            ];
            break;
    }

    // تحديث الوصف
    const descriptionElement = document.getElementById('rule_type_description');
    if (descriptionElement) {
        descriptionElement.textContent = description;
        console.log('📝 تم تحديث الوصف:', description);
    }

    // إضافة الخيارات للقائمة
    console.log('📋 إضافة', options.length, 'خيارات لشرط التشغيل');
    options.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option.value;
        optionElement.textContent = option.text;
        triggerCondition.appendChild(optionElement);
    });

    console.log('✅ تم تحديث حقول القاعدة بنجاح');
}

// دالة تحديث حقل قيمة الشرط
function updateConditionValue() {
    const triggerCondition = document.getElementById('trigger_condition').value;
    const conditionValueRow = document.getElementById('condition_value_row');
    const conditionValueContainer = document.getElementById('condition_value_container');
    const conditionValueLabel = document.getElementById('condition_value_label');
    const conditionValueHelp = document.getElementById('condition_value_help');

    // إخفاء الحقل إذا لم يتم اختيار شرط
    if (!triggerCondition) {
        conditionValueRow.style.display = 'none';
        return;
    }

    // إظهار الحقل
    conditionValueRow.style.display = 'block';

    // تحديد نوع الحقل والخيارات حسب الشرط
    let fieldHTML = '';
    let labelText = 'قيمة الشرط *';
    let helpText = 'اختر القيمة المناسبة للشرط';

    switch(triggerCondition) {
        // شروط الحالة
        case 'STATUS_CHANGE_ACTION':
            labelText = 'الحالة الجديدة للشحنة *';
            helpText = 'اختر الحالة التي ستؤدي لتشغيل القاعدة عند الوصول إليها';
            fieldHTML = `
                <select class="form-select" id="condition_value" name="condition_value" required>
                    <option value="">جاري تحميل الحالات...</option>
                </select>
                <div class="form-text mt-2">
                    <i class="fas fa-info-circle me-1"></i>
                    ستنفذ القاعدة عندما تصل الشحنة إلى هذه الحالة
                </div>
            `;
            // تحميل حالات الشحنة من API
            setTimeout(() => loadShipmentStatuses(), 100);
            break;

        case 'STATUS_EQUALS':
        case 'STATUS_NOT_EQUALS':
            labelText = 'حالة الشحنة *';
            helpText = 'اختر الحالة المطلوبة للمقارنة';
            fieldHTML = `
                <select class="form-select" id="condition_value" name="condition_value" required>
                    <option value="">جاري تحميل الحالات...</option>
                </select>
            `;
            // تحميل حالات الشحنة من API
            setTimeout(() => loadShipmentStatuses(), 100);
            break;

        // شروط التاريخ
        case 'DATE_BEFORE':
        case 'DATE_AFTER':
            labelText = 'التاريخ *';
            helpText = 'اختر التاريخ المرجعي';
            fieldHTML = `
                <input type="date" class="form-control" id="condition_value" name="condition_value" required>
            `;
            break;

        // شروط المبلغ
        case 'AMOUNT_GREATER':
        case 'AMOUNT_LESS':
            labelText = 'المبلغ *';
            helpText = 'أدخل المبلغ بالريال السعودي';
            fieldHTML = `
                <input type="number" class="form-control" id="condition_value" name="condition_value"
                       placeholder="0.00" step="0.01" min="0" required>
            `;
            break;

        // تقييم المخلص
        case 'AGENT_RATING':
            labelText = 'تقييم المخلص *';
            helpText = 'اختر الحد الأدنى للتقييم';
            fieldHTML = `
                <select class="form-select" id="condition_value" name="condition_value" required>
                    <option value="">اختر التقييم</option>
                    <option value="5">ممتاز (5 نجوم)</option>
                    <option value="4">جيد جداً (4 نجوم)</option>
                    <option value="3">جيد (3 نجوم)</option>
                    <option value="2">مقبول (نجمتان)</option>
                    <option value="1">ضعيف (نجمة واحدة)</option>
                </select>
            `;
            break;

        // نوع الحاوية
        case 'CONTAINER_TYPE':
            labelText = 'نوع الحاوية *';
            helpText = 'اختر نوع الحاوية';
            fieldHTML = `
                <select class="form-select" id="condition_value" name="condition_value" required>
                    <option value="">اختر النوع</option>
                    <option value="20ft">20 قدم</option>
                    <option value="40ft">40 قدم</option>
                    <option value="40ft_hc">40 قدم عالي</option>
                    <option value="refrigerated">مبرد</option>
                    <option value="open_top">مفتوح من الأعلى</option>
                    <option value="flat_rack">مسطح</option>
                </select>
            `;
            break;

        // شروط الوصول والمغادرة
        case 'SHIPMENT_ARRIVAL':
        case 'SHIPMENT_DEPARTURE':
            labelText = 'الميناء *';
            helpText = 'اختر الميناء';
            fieldHTML = `
                <select class="form-select" id="condition_value" name="condition_value" required>
                    <option value="">اختر الميناء</option>
                    <option value="jeddah">ميناء جدة الإسلامي</option>
                    <option value="dammam">ميناء الملك عبدالعزيز - الدمام</option>
                    <option value="jubail">ميناء الملك فهد الصناعي - الجبيل</option>
                    <option value="yanbu">ميناء ينبع التجاري</option>
                    <option value="jizan">ميناء جازان</option>
                </select>
            `;
            break;

        // التخليص الجمركي
        case 'CUSTOMS_CLEARANCE':
            labelText = 'حالة التخليص *';
            helpText = 'اختر حالة التخليص الجمركي';
            fieldHTML = `
                <select class="form-select" id="condition_value" name="condition_value" required>
                    <option value="">اختر الحالة</option>
                    <option value="started">بدء التخليص</option>
                    <option value="documents_submitted">تم تقديم المستندات</option>
                    <option value="under_review">قيد المراجعة</option>
                    <option value="approved">تم الموافقة</option>
                    <option value="completed">تم الانتهاء</option>
                </select>
            `;
            break;

        // رفع المستندات
        case 'DOCUMENT_UPLOAD':
            labelText = 'نوع المستند *';
            helpText = 'اختر نوع المستند المطلوب';
            fieldHTML = `
                <select class="form-select" id="condition_value" name="condition_value" required>
                    <option value="">اختر النوع</option>
                    <option value="bill_of_lading">بوليصة الشحن</option>
                    <option value="commercial_invoice">الفاتورة التجارية</option>
                    <option value="packing_list">قائمة التعبئة</option>
                    <option value="certificate_of_origin">شهادة المنشأ</option>
                    <option value="customs_declaration">البيان الجمركي</option>
                </select>
            `;
            break;

        // استلام الدفعة
        case 'PAYMENT_RECEIVED':
            labelText = 'نوع الدفعة *';
            helpText = 'اختر نوع الدفعة';
            fieldHTML = `
                <select class="form-select" id="condition_value" name="condition_value" required>
                    <option value="">اختر النوع</option>
                    <option value="full_payment">دفعة كاملة</option>
                    <option value="partial_payment">دفعة جزئية</option>
                    <option value="advance_payment">دفعة مقدمة</option>
                    <option value="final_payment">دفعة نهائية</option>
                </select>
            `;
            break;

        // مبني على الوقت
        case 'TIME_BASED':
            labelText = 'التوقيت *';
            helpText = 'اختر التوقيت المطلوب';
            fieldHTML = `
                <select class="form-select" id="condition_value" name="condition_value" required>
                    <option value="">اختر التوقيت</option>
                    <option value="daily_8am">يومياً في 8 صباحاً</option>
                    <option value="daily_6pm">يومياً في 6 مساءً</option>
                    <option value="weekly_sunday">أسبوعياً يوم الأحد</option>
                    <option value="monthly_1st">شهرياً في اليوم الأول</option>
                    <option value="after_3_days">بعد 3 أيام من الحدث</option>
                    <option value="after_1_week">بعد أسبوع من الحدث</option>
                </select>
            `;
            break;

        // الإجراءات - لا تحتاج قيم إضافية عادة
        case 'CREATE_DELIVERY_ORDER':
        case 'ASSIGN_AGENT':
        case 'SEND_NOTIFICATION':
            // إظهار قسم إعدادات الإشعارات
            document.getElementById('notification_settings_section').style.display = 'block';
            conditionValueRow.style.display = 'none';
            // تحميل مجموعات جهات الاتصال
            loadContactGroupsForAutomation();
            return;
        case 'UPDATE_STATUS':
        case 'GENERATE_REPORT':
        case 'UPDATE_RATINGS':
        case 'SCHEDULE_TASK':
            conditionValueRow.style.display = 'none';
            return;

        // حقل نص عام للحالات الأخرى
        default:
            labelText = 'قيمة الشرط *';
            helpText = 'أدخل القيمة المطلوبة';
            fieldHTML = `
                <input type="text" class="form-control" id="condition_value" name="condition_value"
                       placeholder="أدخل القيمة..." required>
            `;
    }

    // تحديث العناصر
    conditionValueLabel.textContent = labelText;
    conditionValueHelp.textContent = helpText;
    conditionValueContainer.innerHTML = fieldHTML;
}

// دالة تحميل حالات الشحنة من API
function loadShipmentStatuses() {
    console.log('🔄 تحميل حالات الشحنة...');

    const conditionValueSelect = document.getElementById('condition_value');
    if (!conditionValueSelect) {
        console.warn('⚠️ عنصر condition_value غير موجود');
        return;
    }

    // إظهار حالة التحميل
    conditionValueSelect.innerHTML = '<option value="">🔄 جاري تحميل الحالات...</option>';
    conditionValueSelect.disabled = true;

    fetch('/shipments/api/shipment-statuses')
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success && data.statuses) {
            // مسح الخيارات الحالية
            conditionValueSelect.innerHTML = '<option value="">اختر الحالة</option>';

            // إضافة حالات الشحنة مع ترتيب جميل
            data.statuses.forEach(status => {
                const option = document.createElement('option');
                option.value = status.code;

                // عرض الاسم العربي مع الإنجليزي إذا كان متوفراً
                let displayText = status.name_ar;
                if (status.name_en && status.name_en.trim()) {
                    displayText += ` (${status.name_en})`;
                }

                option.textContent = displayText;
                conditionValueSelect.appendChild(option);
            });

            // إعادة تفعيل القائمة
            conditionValueSelect.disabled = false;

            console.log(`✅ تم تحميل ${data.statuses.length} حالة شحنة بنجاح`);

            // إضافة تأثير بصري للنجاح
            conditionValueSelect.style.borderColor = '#28a745';
            setTimeout(() => {
                conditionValueSelect.style.borderColor = '';
            }, 2000);

        } else {
            throw new Error(data.message || 'فشل في تحميل البيانات');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في تحميل حالات الشحنة:', error);

        // عرض رسالة خطأ واضحة
        conditionValueSelect.innerHTML = `
            <option value="">❌ خطأ في التحميل</option>
            <option value="" disabled>يرجى المحاولة مرة أخرى</option>
        `;
        conditionValueSelect.disabled = false;

        // إضافة تأثير بصري للخطأ
        conditionValueSelect.style.borderColor = '#dc3545';
        setTimeout(() => {
            conditionValueSelect.style.borderColor = '';
        }, 3000);

        // إظهار رسالة للمستخدم
        if (typeof showNotification === 'function') {
            showNotification('فشل في تحميل حالات الشحنة. يرجى المحاولة مرة أخرى.', 'error');
        }
    });
}

// ===== دوال إدارة جهات الاتصال للأتمتة =====

// متغيرات عامة لجهات الاتصال
let selectedContactsForAutomation = [];
let contactGroupsForAutomation = [];

// تحميل مجموعات جهات الاتصال للأتمتة
function loadContactGroupsForAutomation() {
    fetch('/notifications/contacts/api/automation/contact-groups')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            contactGroupsForAutomation = data.groups;
            renderContactGroupsForAutomation();
        } else {
            console.error('فشل في تحميل مجموعات جهات الاتصال:', data.message);
        }
    })
    .catch(error => {
        console.error('خطأ في تحميل مجموعات جهات الاتصال:', error);
    });
}

// عرض مجموعات جهات الاتصال
function renderContactGroupsForAutomation() {
    const container = document.querySelector('.contact-groups-container');
    if (!container) return;

    container.innerHTML = '';

    contactGroupsForAutomation.forEach(group => {
        const groupDiv = document.createElement('div');
        groupDiv.className = 'form-check mb-2';
        groupDiv.innerHTML = `
            <input class="form-check-input contact-group-checkbox"
                   type="checkbox"
                   value="${group.id}"
                   id="automation_group_${group.id}"
                   onchange="updateSelectedContactGroups()">
            <label class="form-check-label" for="automation_group_${group.id}">
                <i class="${group.icon} me-2"></i>
                ${group.name}
                <small class="text-muted d-block">${group.description}</small>
            </label>
        `;
        container.appendChild(groupDiv);
    });
}

// تحديث المجموعات المختارة
function updateSelectedContactGroups() {
    const selectedGroups = [];
    const checkboxes = document.querySelectorAll('.contact-group-checkbox:checked');

    checkboxes.forEach(checkbox => {
        const group = contactGroupsForAutomation.find(g => g.id === checkbox.value);
        if (group) {
            selectedGroups.push(group);
        }
    });

    displaySelectedContactGroups(selectedGroups);
    updateHiddenContactGroupsField();
}

// عرض المجموعات المختارة
function displaySelectedContactGroups(groups) {
    const container = document.getElementById('selectedGroupsDisplay');
    if (!container) return;

    if (groups.length === 0) {
        container.innerHTML = '<p class="text-muted">لم يتم اختيار أي مجموعة</p>';
        return;
    }

    container.innerHTML = groups.map(group => `
        <span class="badge bg-primary me-2 mb-2">
            <i class="${group.icon} me-1"></i>
            ${group.name}
        </span>
    `).join('');
}

// فتح واجهة اختيار جهات الاتصال المخصصة
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('select-custom-contacts-btn')) {
        e.preventDefault();

        // التحقق من وجود دالة اختيار جهات الاتصال
        if (typeof openContactSelector === 'function') {
            openContactSelector((selectedContacts) => {
                selectedContactsForAutomation = selectedContacts;
                displaySelectedContactsForAutomation();
                updateHiddenCustomContactsField();
            });
        } else {
            // إنشاء modal بسيط إذا لم تكن الدالة متاحة
            showSimpleContactSelector();
        }
    }
});

// عرض جهات الاتصال المختارة
function displaySelectedContactsForAutomation() {
    const container = document.getElementById('selectedContactsDisplay');
    const countElement = document.getElementById('customContactsCount');

    if (!container) return;

    if (selectedContactsForAutomation.length === 0) {
        container.innerHTML = '<p class="text-muted">لم يتم اختيار أي جهة اتصال</p>';
        if (countElement) countElement.textContent = '0 محدد';
        return;
    }

    container.innerHTML = selectedContactsForAutomation.map(contact => `
        <div class="selected-contact-item d-flex align-items-center justify-content-between mb-2 p-2 border rounded">
            <div>
                <strong>${contact.name}</strong>
                ${contact.is_vip ? '<i class="fas fa-star text-warning ms-1"></i>' : ''}
                <br>
                <small class="text-muted">
                    ${contact.type} - الأولوية: ${contact.priority}
                    ${contact.phone ? ` - ${contact.phone}` : ''}
                </small>
            </div>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeContactFromAutomation(${contact.id})">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `).join('');

    if (countElement) {
        countElement.textContent = `${selectedContactsForAutomation.length} محدد`;
    }
}

// إزالة جهة اتصال من القائمة
function removeContactFromAutomation(contactId) {
    selectedContactsForAutomation = selectedContactsForAutomation.filter(c => c.id !== contactId);
    displaySelectedContactsForAutomation();
    updateHiddenCustomContactsField();
}

// تحديث الحقول المخفية
function updateHiddenContactGroupsField() {
    const selectedGroupIds = Array.from(document.querySelectorAll('.contact-group-checkbox:checked'))
        .map(cb => cb.value);

    const hiddenField = document.getElementById('selected_contact_groups');
    if (hiddenField) {
        hiddenField.value = JSON.stringify(selectedGroupIds);
    }
}

function updateHiddenCustomContactsField() {
    const hiddenField = document.getElementById('selected_custom_contacts');
    if (hiddenField) {
        hiddenField.value = JSON.stringify(selectedContactsForAutomation.map(c => c.id));
    }
}

function updateHiddenChannelsField() {
    const selectedChannels = Array.from(document.querySelectorAll('.notification-channel-checkbox:checked'))
        .map(cb => cb.value);

    const hiddenField = document.getElementById('selected_notification_channels');
    if (hiddenField) {
        hiddenField.value = JSON.stringify(selectedChannels);
    }
}

// إضافة event listeners للقنوات
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('notification-channel-checkbox')) {
        updateHiddenChannelsField();
    }
});

// واجهة بسيطة لاختيار جهات الاتصال إذا لم تكن الواجهة المتقدمة متاحة
function showSimpleContactSelector() {
    // جلب جهات الاتصال من API
    fetch('/notifications/contacts/api/automation/contacts')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const contacts = data.contacts;

            // إنشاء modal بسيط
            const modalHtml = `
                <div class="modal fade" id="simpleContactModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">اختيار جهات الاتصال</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    ${contacts.map(contact => `
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input simple-contact-checkbox"
                                                       type="checkbox"
                                                       value="${contact.id}"
                                                       id="simple_contact_${contact.id}"
                                                       data-contact='${JSON.stringify(contact)}'>
                                                <label class="form-check-label" for="simple_contact_${contact.id}">
                                                    ${contact.name} (${contact.type})
                                                    ${contact.is_vip ? '<i class="fas fa-star text-warning"></i>' : ''}
                                                </label>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="confirmSimpleContactSelection()">تأكيد</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // إضافة modal للصفحة
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // فتح modal
            const modal = new bootstrap.Modal(document.getElementById('simpleContactModal'));
            modal.show();

            // إزالة modal عند الإغلاق
            document.getElementById('simpleContactModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }
    })
    .catch(error => {
        console.error('خطأ في جلب جهات الاتصال:', error);
        alert('حدث خطأ في تحميل جهات الاتصال');
    });
}

// تأكيد اختيار جهات الاتصال البسيط
function confirmSimpleContactSelection() {
    const selectedCheckboxes = document.querySelectorAll('.simple-contact-checkbox:checked');
    const selectedContacts = [];

    selectedCheckboxes.forEach(checkbox => {
        const contactData = JSON.parse(checkbox.getAttribute('data-contact'));
        selectedContacts.push(contactData);
    });

    selectedContactsForAutomation = selectedContacts;
    displaySelectedContactsForAutomation();
    updateHiddenCustomContactsField();

    // إغلاق modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('simpleContactModal'));
    modal.hide();
}

// دالة تحميل قواعد الشروط المتاحة
function loadConditionRules() {
    const availableConditionsList = document.getElementById('available_conditions_list');

    // مسح المحتوى السابق
    availableConditionsList.innerHTML = '<div class="text-center text-muted py-3"><i class="fas fa-spinner fa-spin me-2"></i>جاري تحميل الشروط المتاحة...</div>';

    // فحص وجود قواعد شروط
    if (!conditionRules || conditionRules.length === 0) {
        availableConditionsList.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-exclamation-circle fa-2x mb-2"></i>
                <p class="mb-0">لا توجد قواعد شروط محفوظة حالياً</p>
                <small>يمكنك إنشاء قواعد شروط أولاً ثم ربطها بهذا الإجراء</small>
            </div>
        `;
        return;
    }

    // إنشاء قائمة الشروط المتاحة
    let conditionsHTML = '';
    conditionRules.forEach(rule => {
        const conditionText = getConditionDisplayText(rule);
        const priorityBadge = getPriorityBadge(rule.priority);
        const statusBadge = rule.is_active ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-secondary">غير نشط</span>';

        conditionsHTML += `
            <div class="condition-item border rounded p-3 mb-2" data-rule-id="${rule.id}">
                <div class="form-check">
                    <input class="form-check-input condition-checkbox" type="checkbox"
                           id="condition_${rule.id}" value="${rule.id}"
                           onchange="updateSelectedConditions()">
                    <label class="form-check-label w-100" for="condition_${rule.id}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">${rule.rule_name}</h6>
                                <p class="mb-1 text-muted small">${conditionText}</p>
                                <div class="d-flex gap-2">
                                    ${priorityBadge}
                                    ${statusBadge}
                                </div>
                            </div>
                            <div class="text-end">
                                <small class="text-muted">ID: ${rule.id}</small>
                            </div>
                        </div>
                    </label>
                </div>
            </div>
        `;
    });

    availableConditionsList.innerHTML = conditionsHTML;
    console.log(`✅ تم تحميل ${conditionRules.length} قاعدة شرط`);
}

// دالة تحديث الشروط المختارة
function updateSelectedConditions() {
    const selectedConditionsList = document.getElementById('selected_conditions_list');
    const linkedConditionRulesInput = document.getElementById('linked_condition_rules');
    const checkboxes = document.querySelectorAll('.condition-checkbox:checked');

    if (checkboxes.length === 0) {
        selectedConditionsList.innerHTML = `
            <div class="text-muted text-center py-2">
                <i class="fas fa-hand-pointer me-2"></i>لم يتم اختيار أي شروط بعد
            </div>
        `;
        linkedConditionRulesInput.value = '';
        return;
    }

    // إنشاء قائمة الشروط المختارة
    let selectedHTML = '';
    const selectedIds = [];

    checkboxes.forEach(checkbox => {
        const ruleId = checkbox.value;
        const rule = conditionRules.find(r => r.id == ruleId);
        if (rule) {
            selectedIds.push(ruleId);
            const conditionText = getConditionDisplayText(rule);

            selectedHTML += `
                <div class="selected-condition-item bg-white border rounded p-2 mb-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${rule.rule_name}</strong>
                            <br><small class="text-muted">${conditionText}</small>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-danger"
                                onclick="removeSelectedCondition(${ruleId})">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
        }
    });

    selectedConditionsList.innerHTML = selectedHTML;
    linkedConditionRulesInput.value = selectedIds.join(',');

    console.log('✅ تم تحديث الشروط المختارة:', selectedIds);
}

// دالة إزالة شرط مختار
function removeSelectedCondition(ruleId) {
    const checkbox = document.getElementById(`condition_${ruleId}`);
    if (checkbox) {
        checkbox.checked = false;
        updateSelectedConditions();
    }
}

// دالة الحصول على نص الشرط للعرض
function getConditionDisplayText(rule) {
    const conditionText = {
        'STATUS_EQUALS': 'الحالة تساوي',
        'STATUS_NOT_EQUALS': 'الحالة لا تساوي',
        'DATE_BEFORE': 'التاريخ قبل',
        'DATE_AFTER': 'التاريخ بعد',
        'AMOUNT_GREATER': 'المبلغ أكبر من',
        'AMOUNT_LESS': 'المبلغ أقل من',
        'AGENT_RATING': 'تقييم المخلص',
        'CONTAINER_TYPE': 'نوع الحاوية'
    };

    const condition = conditionText[rule.trigger_condition] || rule.trigger_condition;
    return `${condition} ${rule.condition_value || ''}`;
}

// دالة الحصول على شارة الأولوية
function getPriorityBadge(priority) {
    const priorityConfig = {
        'high': { class: 'bg-danger', text: 'عالية' },
        'medium': { class: 'bg-warning', text: 'متوسطة' },
        'low': { class: 'bg-info', text: 'منخفضة' }
    };

    const config = priorityConfig[priority] || { class: 'bg-secondary', text: priority || 'غير محدد' };
    return `<span class="badge ${config.class}">${config.text}</span>`;
}

// تحديث معلومات المخلص عند الاختيار
function updateAgentInfo() {
    console.log('🔄 بدء تحديث معلومات المخلص...');

    const selectedAgentSelect = document.getElementById('selected_agent');
    const selectedAgentId = selectedAgentSelect ? selectedAgentSelect.value : null;
    const branchField = document.getElementById('agent_branch');
    const portField = document.getElementById('agent_port');

    console.log('📋 عنصر اختيار المخلص:', selectedAgentSelect);
    console.log('📋 المخلص المختار:', selectedAgentId);
    console.log('📋 حقل الفرع:', branchField);
    console.log('📋 حقل المنفذ:', portField);

    // التحقق من وجود العناصر
    if (!selectedAgentSelect) {
        console.error('❌ لم يتم العثور على قائمة اختيار المخلص');
        return;
    }

    if (!branchField) {
        console.error('❌ لم يتم العثور على حقل الفرع (agent_branch)');
        return;
    }

    if (!portField) {
        console.error('❌ لم يتم العثور على حقل المنفذ (agent_port)');
        return;
    }

    // مسح الحقول أولاً
    branchField.value = '';
    portField.value = '';

    if (!selectedAgentId || selectedAgentId === '' || selectedAgentId === 'AUTO') {
        if (selectedAgentId === 'AUTO') {
            branchField.value = 'اختيار تلقائي';
            portField.value = 'اختيار تلقائي';
            console.log('✅ تم تعيين القيم للاختيار التلقائي');
        } else {
            branchField.value = '';
            portField.value = '';
            console.log('✅ تم مسح الحقول (لا يوجد مخلص مختار)');
        }
        return;
    }

    // إظهار رسالة تحميل
    branchField.value = 'جاري التحميل...';
    portField.value = 'جاري التحميل...';

    const apiUrl = `/shipments/api/customs-agents/${selectedAgentId}`;
    console.log(`🌐 جلب معلومات المخلص من: ${apiUrl}`);

    // جلب معلومات المخلص
    fetch(apiUrl, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('📡 حالة الاستجابة:', response.status);
        console.log('📡 نوع المحتوى:', response.headers.get('content-type'));

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.json();
    })
    .then(data => {
        console.log('📋 البيانات المستلمة:', data);

        if (data.success && data.agent) {
            const agent = data.agent;
            const branchName = agent.branch_name || 'غير محدد';
            const portName = agent.port_name || 'غير محدد';

            branchField.value = branchName;
            portField.value = portName;

            // حفظ معرفات الفرع والمنفذ في الحقول المخفية
            const branchIdField = document.getElementById('agent_branch_id');
            const portIdField = document.getElementById('agent_port_id');

            if (branchIdField) {
                branchIdField.value = agent.branch_id || '';
            }
            if (portIdField) {
                portIdField.value = agent.customs_port_id || '';
            }

            console.log('✅ تم تحديث معلومات المخلص بنجاح');
            console.log('🏢 الفرع:', branchName, '(ID:', agent.branch_id, ')');
            console.log('🚢 المنفذ:', portName, '(ID:', agent.customs_port_id, ')');
            console.log('📊 جميع بيانات المخلص:', agent);
        } else {
            branchField.value = 'خطأ في البيانات';
            portField.value = 'خطأ في البيانات';
            console.error('❌ فشل في جلب معلومات المخلص:', data.message || 'لا توجد رسالة خطأ');
            console.error('❌ البيانات المستلمة:', data);
        }
    })
    .catch(error => {
        branchField.value = 'خطأ في الاتصال';
        portField.value = 'خطأ في الاتصال';
        console.error('❌ خطأ في جلب معلومات المخلص:', error);
        console.error('❌ تفاصيل الخطأ:', error.message);
    });
}

// دالة اختبار العناصر
function testAgentElements() {
    console.log('🧪 اختبار وجود عناصر المخلص...');

    const selectedAgentSelect = document.getElementById('selected_agent');
    const branchField = document.getElementById('agent_branch');
    const portField = document.getElementById('agent_port');
    const agentSection = document.getElementById('agent_selection_section');

    console.log('🔍 قائمة اختيار المخلص:', selectedAgentSelect);
    console.log('🔍 حقل الفرع:', branchField);
    console.log('🔍 حقل المنفذ:', portField);
    console.log('🔍 قسم اختيار المخلص:', agentSection);

    if (agentSection) {
        console.log('📊 حالة عرض قسم المخلص:', agentSection.style.display);
        console.log('📊 محتوى قسم المخلص:', agentSection.innerHTML.substring(0, 200) + '...');
    }

    // اختبار تحديث المعلومات يدوياً
    if (selectedAgentSelect && selectedAgentSelect.value) {
        console.log('🧪 اختبار تحديث معلومات المخلص للقيمة:', selectedAgentSelect.value);
        updateAgentInfo();
    }
}

// ربط الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تحميل صفحة قواعد الأتمتة');

    // تحديث حقول القاعدة عند التحميل
    updateRuleFields();

    // انتظار قليل للتأكد من تحميل جميع العناصر
    setTimeout(() => {
        // اختبار العناصر
        testAgentElements();

        // ربط حدث تغيير المخلص
        const selectedAgentSelect = document.getElementById('selected_agent');
        if (selectedAgentSelect) {
            selectedAgentSelect.addEventListener('change', updateAgentInfo);
            console.log('✅ تم ربط حدث تغيير المخلص');

            // اختبار فوري إذا كان هناك قيمة مختارة
            if (selectedAgentSelect.value && selectedAgentSelect.value !== '') {
                console.log('🔄 تحديث فوري للمخلص المختار:', selectedAgentSelect.value);
                updateAgentInfo();
            }
        } else {
            console.warn('⚠️ لم يتم العثور على قائمة اختيار المخلص');
        }

        // اختبار وجود حقول الفرع والمنفذ
        const branchField = document.getElementById('agent_branch');
        const portField = document.getElementById('agent_port');

        if (branchField && portField) {
            console.log('✅ تم العثور على حقول الفرع والمنفذ');
        } else {
            console.warn('⚠️ لم يتم العثور على حقول الفرع أو المنفذ');
            console.log('🔍 حقل الفرع:', branchField);
            console.log('🔍 حقل المنفذ:', portField);
        }
    }, 500); // انتظار نصف ثانية
});
</script>
{% endblock %}
