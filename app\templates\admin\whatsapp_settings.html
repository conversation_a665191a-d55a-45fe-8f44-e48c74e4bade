{% extends "base.html" %}

{% block title %}إعدادات WhatsApp Business API{% endblock %}

{% block extra_css %}
<style>
.settings-card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-connected {
    background-color: #28a745;
}

.status-disconnected {
    background-color: #dc3545;
}

.status-testing {
    background-color: #ffc107;
}

.config-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.test-section {
    background: #e7f3ff;
    border: 2px dashed #007bff;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fab fa-whatsapp text-success me-2"></i>
                        إعدادات WhatsApp Business API
                    </h2>
                    <p class="text-muted mb-0">تكوين وإدارة إعدادات الإرسال عبر WhatsApp</p>
                </div>
                <div>
                    <button class="btn btn-info" onclick="testConnection()">
                        <i class="fas fa-wifi me-1"></i>
                        اختبار الاتصال
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- حالة الاتصال -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card settings-card">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-signal me-2"></i>
                        حالة الاتصال
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="d-flex align-items-center mb-2">
                                <span class="status-indicator {{ 'status-connected' if config.is_configured else 'status-disconnected' }}"></span>
                                <strong>حالة التكوين:</strong>
                                <span class="ms-2 badge {{ 'bg-success' if config.is_configured else 'bg-danger' }}">
                                    {{ 'مكون' if config.is_configured else 'غير مكون' }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="d-flex align-items-center mb-2">
                                <span class="status-indicator {{ 'status-testing' if config.test_mode else 'status-connected' }}"></span>
                                <strong>وضع التشغيل:</strong>
                                <span class="ms-2 badge {{ 'bg-warning' if config.test_mode else 'bg-primary' }}">
                                    {{ 'اختبار' if config.test_mode else 'إنتاج' }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="d-flex align-items-center mb-2">
                                <span class="status-indicator status-connected"></span>
                                <strong>رقم الهاتف:</strong>
                                <span class="ms-2">{{ config.phone_number_id or 'غير محدد' }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إعدادات API -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card settings-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات API
                    </h5>
                </div>
                <div class="card-body">
                    <form id="whatsappSettingsForm">
                        <div class="config-section">
                            <h6 class="text-primary mb-3">الإعدادات الأساسية</h6>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Access Token <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="accessToken"
                                           value="{{ (config.access_token[:20] + '...') if config.access_token else '' }}"
                                           placeholder="EAAG...">
                                    <small class="text-muted">من Facebook Developer Console</small>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Phone Number ID <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="phoneNumberId" 
                                           value="{{ config.phone_number_id }}"
                                           placeholder="***************">
                                    <small class="text-muted">معرف رقم الهاتف من WhatsApp Business</small>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Business Account ID</label>
                                    <input type="text" class="form-control" id="businessAccountId" 
                                           value="{{ config.business_account_id }}"
                                           placeholder="***************">
                                    <small class="text-muted">معرف حساب الأعمال (اختياري)</small>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">API URL</label>
                                    <input type="url" class="form-control" id="apiUrl" 
                                           value="{{ config.api_url }}"
                                           placeholder="https://graph.facebook.com/v18.0">
                                    <small class="text-muted">رابط Facebook Graph API</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="config-section">
                            <h6 class="text-primary mb-3">إعدادات التشغيل</h6>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="testMode" 
                                               {{ 'checked' if config.test_mode else '' }}>
                                        <label class="form-check-label" for="testMode">
                                            وضع الاختبار
                                        </label>
                                    </div>
                                    <small class="text-muted">لا يرسل رسائل حقيقية</small>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">مهلة الاتصال (ثانية)</label>
                                    <input type="number" class="form-control" id="messageTimeout" 
                                           value="{{ config.message_timeout }}" min="5" max="120">
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">عدد المحاولات</label>
                                    <input type="number" class="form-control" id="maxRetries" 
                                           value="{{ config.max_retries }}" min="1" max="5">
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-primary" onclick="saveSettings()">
                                <i class="fas fa-save me-1"></i>
                                حفظ الإعدادات
                            </button>
                            
                            <button type="button" class="btn btn-secondary" onclick="resetSettings()">
                                <i class="fas fa-undo me-1"></i>
                                إعادة تعيين
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- قسم الاختبار -->
        <div class="col-lg-4">
            <div class="card settings-card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-vial me-2"></i>
                        اختبار الإرسال
                    </h5>
                </div>
                <div class="card-body">
                    <div class="test-section">
                        <h6 class="text-primary mb-3">إرسال رسالة تجريبية</h6>
                        
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="testPhone" 
                                   placeholder="+966501234567">
                            <small class="text-muted">يجب أن يبدأ بـ + ورمز الدولة</small>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">نص الرسالة</label>
                            <textarea class="form-control" id="testMessage" rows="4" 
                                      placeholder="مرحباً، هذه رسالة تجريبية من نظام إدارة الشحنات.">مرحباً، هذه رسالة تجريبية من نظام إدارة الشحنات.</textarea>
                        </div>
                        
                        <button type="button" class="btn btn-success w-100" onclick="sendTestMessage()">
                            <i class="fab fa-whatsapp me-1"></i>
                            إرسال رسالة تجريبية
                        </button>
                    </div>
                    
                    <!-- إحصائيات سريعة -->
                    <div class="mt-4">
                        <h6 class="text-primary mb-3">إحصائيات سريعة</h6>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border rounded p-2">
                                    <div class="h5 text-success mb-0">{{ stats.sent_today or 0 }}</div>
                                    <small class="text-muted">اليوم</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="border rounded p-2">
                                    <div class="h5 text-primary mb-0">{{ stats.sent_total or 0 }}</div>
                                    <small class="text-muted">الإجمالي</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// حفظ الإعدادات
function saveSettings() {
    alert('سيتم تطوير حفظ الإعدادات قريباً');
}

// إعادة تعيين الإعدادات
function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين الإعدادات؟')) {
        location.reload();
    }
}

// اختبار الاتصال
function testConnection() {
    alert('سيتم تطوير اختبار الاتصال قريباً');
}

// إرسال رسالة تجريبية
function sendTestMessage() {
    const phone = document.getElementById('testPhone').value.trim();
    const message = document.getElementById('testMessage').value.trim();
    
    if (!phone || !message) {
        alert('يرجى ملء رقم الهاتف ونص الرسالة');
        return;
    }
    
    alert('سيتم تطوير إرسال الرسالة التجريبية قريباً');
}
</script>
{% endblock %}
