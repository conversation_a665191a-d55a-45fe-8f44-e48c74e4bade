        <!-- Control Panel -->
        <div class="control-panel">
            <div class="row align-items-end">
                <div class="col-md-3">
                    <label class="form-label fw-bold">البحث السريع</label>
                    <div class="position-relative">
                        <input type="text" id="searchInput" class="form-control form-control-modern"
                               placeholder="ابحث برقم الطلب أو المستفيد..." autocomplete="off">
                        <div class="position-absolute top-50 end-0 translate-middle-y me-2">
                            <i class="fas fa-search text-muted"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">الحالة</label>
                    <select id="statusFilter" class="form-control form-control-modern">
                        <option value="">جميع الحالات</option>
                        <option value="pending">معلق</option>
                        <option value="approved">معتمد</option>
                        <option value="rejected">مرفوض</option>
                        <option value="executed">منفذ</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">العملة</label>
                    <select id="currencyFilter" class="form-control form-control-modern">
                        <option value="">جميع العملات</option>
                        <option value="USD">دولار أمريكي</option>
                        <option value="EUR">يورو</option>
                        <option value="SAR">ريال سعودي</option>
                        <option value="AED">درهم إماراتي</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">نوع الحوالة</label>
                    <select id="typeFilter" class="form-control form-control-modern">
                        <option value="">جميع الأنواع</option>
                        <option value="bank">بنكية</option>
                        <option value="cash">نقدية</option>
                        <option value="online">إلكترونية</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary btn-modern" onclick="loadData()">
                            <i class="fas fa-sync-alt me-2"></i>تحديث
                        </button>
                        <button class="btn btn-success btn-modern" onclick="exportToExcel()">
                            <i class="fas fa-file-excel me-2"></i>تصدير
                        </button>
                        <button class="btn btn-info btn-modern" onclick="printTable()">
                            <i class="fas fa-print me-2"></i>طباعة
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transfer Requests Table -->
        <div class="card-modern">
            <div class="card-header-modern">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        قائمة طلبات الحوالات
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-warning btn-sm" onclick="bulkApprove()">
                            <i class="fas fa-check me-1"></i>اعتماد جماعي
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="bulkReject()">
                            <i class="fas fa-times me-1"></i>رفض جماعي
                        </button>
                    </div>
                </div>
            </div>
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-modern" id="transferRequestsTable">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>رقم الطلب</th>
                                <th>المستفيد</th>
                                <th>المبلغ</th>
                                <th>العملة</th>
                                <th>نوع الحوالة</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="transferRequestsTableBody">
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                    <div class="mt-2">جاري تحميل البيانات...</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div>جاري معالجة الطلب...</div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Global variables
        let transferRequestsData = [];
        let filteredData = [];

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            initializeEventListeners();
        });

        // Initialize event listeners
        function initializeEventListeners() {
            // Search input
            document.getElementById('searchInput').addEventListener('input', filterData);
            
            // Filter selects
            document.getElementById('statusFilter').addEventListener('change', filterData);
            document.getElementById('currencyFilter').addEventListener('change', filterData);
            document.getElementById('typeFilter').addEventListener('change', filterData);
            
            // Select all checkbox
            document.getElementById('selectAll').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
                checkboxes.forEach(cb => cb.checked = this.checked);
            });
        }

        // Load data from API
        async function loadData() {
            try {
                showLoading();
                
                const response = await fetch('/transfers/api/transfer-requests');
                const data = await response.json();
                
                if (data.success) {
                    transferRequestsData = data.data;
                    filteredData = [...transferRequestsData];
                    updateStatistics();
                    renderTable();
                } else {
                    showError('فشل في تحميل البيانات: ' + data.message);
                }
            } catch (error) {
                console.error('Error loading data:', error);
                showError('حدث خطأ في تحميل البيانات');
            } finally {
                hideLoading();
            }
        }

        // Update statistics
        function updateStatistics() {
            const stats = {
                total: transferRequestsData.length,
                pending: transferRequestsData.filter(r => r.status === 'pending').length,
                approved: transferRequestsData.filter(r => r.status === 'approved').length,
                totalAmount: transferRequestsData.reduce((sum, r) => sum + parseFloat(r.amount || 0), 0)
            };

            document.getElementById('totalRequests').textContent = stats.total;
            document.getElementById('pendingRequests').textContent = stats.pending;
            document.getElementById('approvedRequests').textContent = stats.approved;
            document.getElementById('totalAmount').textContent = formatCurrency(stats.totalAmount);
        }

        // Filter data
        function filterData() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const currencyFilter = document.getElementById('currencyFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;

            filteredData = transferRequestsData.filter(request => {
                const matchesSearch = !searchTerm || 
                    request.request_number.toLowerCase().includes(searchTerm) ||
                    request.beneficiary_name.toLowerCase().includes(searchTerm);
                
                const matchesStatus = !statusFilter || request.status === statusFilter;
                const matchesCurrency = !currencyFilter || request.currency === currencyFilter;
                const matchesType = !typeFilter || request.transfer_type === typeFilter;

                return matchesSearch && matchesStatus && matchesCurrency && matchesType;
            });

            renderTable();
        }

        // Render table
        function renderTable() {
            const tbody = document.getElementById('transferRequestsTableBody');
            
            if (filteredData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <div>لا توجد طلبات حوالات</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredData.map(request => `
                <tr>
                    <td>
                        <input type="checkbox" class="form-check-input" value="${request.id}">
                    </td>
                    <td>
                        <strong class="text-primary">${request.request_number}</strong>
                    </td>
                    <td>${request.beneficiary_name}</td>
                    <td>
                        <strong>${formatCurrency(request.amount)}</strong>
                    </td>
                    <td>
                        <span class="badge bg-secondary">${request.currency}</span>
                    </td>
                    <td>${getTransferTypeLabel(request.transfer_type)}</td>
                    <td>
                        <span class="status-badge status-${request.status}">
                            ${getStatusLabel(request.status)}
                        </span>
                    </td>
                    <td>${formatDate(request.created_at)}</td>
                    <td>
                        <div class="d-flex gap-1">
                            <button class="action-btn btn-view" onclick="viewRequest(${request.id})" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn btn-edit" onclick="editRequest(${request.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            ${request.status === 'pending' ? `
                                <button class="action-btn btn-approve" onclick="approveRequest(${request.id})" title="اعتماد">
                                    <i class="fas fa-check"></i>
                                </button>
                            ` : ''}
                            <button class="action-btn btn-delete" onclick="deleteRequest(${request.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // Helper functions
        function getStatusLabel(status) {
            const labels = {
                'pending': 'معلق',
                'approved': 'معتمد',
                'rejected': 'مرفوض',
                'executed': 'منفذ'
            };
            return labels[status] || status;
        }

        function getTransferTypeLabel(type) {
            const labels = {
                'bank': 'بنكية',
                'cash': 'نقدية',
                'online': 'إلكترونية'
            };
            return labels[type] || type;
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount);
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('ar-SA');
        }

        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        function showError(message) {
            alert(message); // يمكن استبدالها بنظام إشعارات أفضل
        }

        // Action functions
        function refreshData() {
            loadData();
        }

        function viewRequest(id) {
            window.open(`/transfers/view-request/${id}`, '_blank');
        }

        function editRequest(id) {
            window.location.href = `/transfers/edit-request/${id}`;
        }

        function deleteRequest(id) {
            if (confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
                // API call to delete
                console.log('Delete request:', id);
            }
        }

        function approveRequest(id) {
            if (confirm('هل أنت متأكد من اعتماد هذا الطلب؟')) {
                // API call to approve
                console.log('Approve request:', id);
            }
        }

        function bulkApprove() {
            const selected = getSelectedRequests();
            if (selected.length === 0) {
                alert('يرجى اختيار طلبات للاعتماد');
                return;
            }
            if (confirm(`هل أنت متأكد من اعتماد ${selected.length} طلب؟`)) {
                console.log('Bulk approve:', selected);
            }
        }

        function bulkReject() {
            const selected = getSelectedRequests();
            if (selected.length === 0) {
                alert('يرجى اختيار طلبات للرفض');
                return;
            }
            if (confirm(`هل أنت متأكد من رفض ${selected.length} طلب؟`)) {
                console.log('Bulk reject:', selected);
            }
        }

        function getSelectedRequests() {
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        function exportToExcel() {
            console.log('Export to Excel');
            // تنفيذ تصدير Excel
        }

        function printTable() {
            window.print();
        }
    </script>
</body>
</html>
