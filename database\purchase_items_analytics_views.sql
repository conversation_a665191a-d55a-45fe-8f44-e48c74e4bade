-- =====================================================
-- Views متقدمة لتحليل أصناف أوامر الشراء
-- Advanced Views for Purchase Order Items Analytics
-- التاريخ: 2025-01-03
-- =====================================================

-- 1. View شامل لأصناف أوامر الشراء مع التحليلات
CREATE OR REPLACE VIEW V_PURCHASE_ITEMS_ANALYTICS AS
SELECT 
    poi.ID as item_id,
    poi.ITEM_CODE,
    poi.ITEM_NAME,
    poi.UNIT,
    poi.QUANTITY,
    poi.UNIT_PRICE,
    poi.TOTAL_PRICE,
    poi.PRODUCTION_DATE,
    poi.EXPIRY_DATE,
    
    -- بيانات أمر الشراء
    po.ID as purchase_order_id,
    po.PO_NUMBER,
    po.PO_DATE,
    po.DELIVERY_DATE,
    po.STATUS as po_status,
    po.CURRENCY,
    po.SUPPLIER_NAME,
    po.SUPPLIER_CODE,
    
    -- بيانات الفئة
    ic.CATEGORY_NAME_AR as category_name,
    ic.CATEGORY_CODE,
    ic.PARENT_CATEGORY_ID,
    
    -- حسابات متقدمة
    CASE 
        WHEN poi.EXPIRY_DATE IS NOT NULL AND poi.EXPIRY_DATE <= SYSDATE + 30 THEN 'منتهي الصلاحية قريباً'
        WHEN poi.EXPIRY_DATE IS NOT NULL AND poi.EXPIRY_DATE <= SYSDATE THEN 'منتهي الصلاحية'
        ELSE 'صالح'
    END as expiry_status,
    
    CASE 
        WHEN po.DELIVERY_DATE < SYSDATE AND po.STATUS NOT IN ('مكتمل', 'مستلم') THEN 'متأخر'
        WHEN po.DELIVERY_DATE BETWEEN SYSDATE AND SYSDATE + 7 THEN 'مستحق قريباً'
        ELSE 'في الموعد'
    END as delivery_status,
    
    -- إحصائيات الصنف
    (SELECT COUNT(*) FROM PO_ITEMS poi2 WHERE poi2.ITEM_CODE = poi.ITEM_CODE) as total_orders_count,
    (SELECT SUM(poi2.QUANTITY) FROM PO_ITEMS poi2 WHERE poi2.ITEM_CODE = poi.ITEM_CODE) as total_quantity_ordered,
    (SELECT AVG(poi2.UNIT_PRICE) FROM PO_ITEMS poi2 WHERE poi2.ITEM_CODE = poi.ITEM_CODE) as avg_unit_price,
    (SELECT SUM(poi2.TOTAL_PRICE) FROM PO_ITEMS poi2 WHERE poi2.ITEM_CODE = poi.ITEM_CODE) as total_value_ordered,
    
    -- ترتيب الصنف حسب القيمة
    RANK() OVER (ORDER BY poi.TOTAL_PRICE DESC) as value_rank,
    RANK() OVER (PARTITION BY ic.CATEGORY_CODE ORDER BY poi.TOTAL_PRICE DESC) as category_value_rank,
    
    -- نسب مئوية
    ROUND(poi.TOTAL_PRICE / SUM(poi.TOTAL_PRICE) OVER() * 100, 2) as percentage_of_total_value,
    
    -- بيانات النظام
    po.CREATED_AT,
    po.UPDATED_AT

FROM PO_ITEMS poi
JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
LEFT JOIN ITEM_CATEGORIES ic ON poi.ITEM_CODE = ic.CATEGORY_CODE -- ربط مؤقت، يحتاج تحسين
WHERE po.STATUS != 'ملغي';

-- 2. View تحليل ABC للأصناف
CREATE OR REPLACE VIEW V_ITEMS_ABC_ANALYSIS AS
WITH item_totals AS (
    SELECT 
        poi.ITEM_CODE,
        poi.ITEM_NAME,
        SUM(poi.TOTAL_PRICE) as total_value,
        SUM(poi.QUANTITY) as total_quantity,
        COUNT(*) as order_count,
        AVG(poi.UNIT_PRICE) as avg_price
    FROM PO_ITEMS poi
    JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
    WHERE po.STATUS != 'ملغي'
    GROUP BY poi.ITEM_CODE, poi.ITEM_NAME
),
ranked_items AS (
    SELECT 
        *,
        SUM(total_value) OVER() as grand_total,
        SUM(total_value) OVER(ORDER BY total_value DESC ROWS UNBOUNDED PRECEDING) as cumulative_value,
        ROW_NUMBER() OVER(ORDER BY total_value DESC) as value_rank
    FROM item_totals
)
SELECT 
    ITEM_CODE,
    ITEM_NAME,
    total_value,
    total_quantity,
    order_count,
    avg_price,
    value_rank,
    ROUND(total_value / grand_total * 100, 2) as value_percentage,
    ROUND(cumulative_value / grand_total * 100, 2) as cumulative_percentage,
    CASE 
        WHEN cumulative_value / grand_total <= 0.8 THEN 'A'
        WHEN cumulative_value / grand_total <= 0.95 THEN 'B'
        ELSE 'C'
    END as abc_classification,
    CASE 
        WHEN cumulative_value / grand_total <= 0.8 THEN 'عالي القيمة - يحتاج مراقبة دقيقة'
        WHEN cumulative_value / grand_total <= 0.95 THEN 'متوسط القيمة - مراقبة عادية'
        ELSE 'منخفض القيمة - مراقبة أساسية'
    END as abc_description
FROM ranked_items
ORDER BY total_value DESC;

-- 3. View تحليل الموردين حسب الأصناف
CREATE OR REPLACE VIEW V_SUPPLIER_ITEMS_ANALYSIS AS
SELECT 
    po.SUPPLIER_NAME,
    po.SUPPLIER_CODE,
    poi.ITEM_CODE,
    poi.ITEM_NAME,
    COUNT(*) as order_count,
    SUM(poi.QUANTITY) as total_quantity,
    AVG(poi.UNIT_PRICE) as avg_unit_price,
    MIN(poi.UNIT_PRICE) as min_unit_price,
    MAX(poi.UNIT_PRICE) as max_unit_price,
    SUM(poi.TOTAL_PRICE) as total_value,
    
    -- حساب الانحراف المعياري للأسعار
    ROUND(STDDEV(poi.UNIT_PRICE), 2) as price_std_deviation,
    
    -- نسبة هذا المورد من إجمالي مشتريات الصنف
    ROUND(SUM(poi.TOTAL_PRICE) / 
        (SELECT SUM(poi2.TOTAL_PRICE) 
         FROM PO_ITEMS poi2 
         JOIN PURCHASE_ORDERS po2 ON poi2.PO_ID = po2.ID
         WHERE poi2.ITEM_CODE = poi.ITEM_CODE 
         AND po2.STATUS != 'ملغي') * 100, 2) as supplier_share_percentage,
    
    -- ترتيب المورد حسب القيمة للصنف
    RANK() OVER (PARTITION BY poi.ITEM_CODE ORDER BY SUM(poi.TOTAL_PRICE) DESC) as supplier_rank_for_item,
    
    -- آخر تاريخ طلب
    MAX(po.PO_DATE) as last_order_date,
    
    -- متوسط وقت التسليم
    ROUND(AVG(po.DELIVERY_DATE - po.PO_DATE), 1) as avg_delivery_days

FROM PO_ITEMS poi
JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
WHERE po.STATUS != 'ملغي'
GROUP BY po.SUPPLIER_NAME, po.SUPPLIER_CODE, poi.ITEM_CODE, poi.ITEM_NAME
ORDER BY poi.ITEM_CODE, total_value DESC;

-- 4. View تحليل الاتجاهات الشهرية
CREATE OR REPLACE VIEW V_MONTHLY_ITEMS_TRENDS AS
SELECT 
    TO_CHAR(po.PO_DATE, 'YYYY-MM') as order_month,
    EXTRACT(YEAR FROM po.PO_DATE) as order_year,
    EXTRACT(MONTH FROM po.PO_DATE) as order_month_num,
    poi.ITEM_CODE,
    poi.ITEM_NAME,
    
    -- إحصائيات الشهر
    COUNT(*) as monthly_orders,
    SUM(poi.QUANTITY) as monthly_quantity,
    SUM(poi.TOTAL_PRICE) as monthly_value,
    AVG(poi.UNIT_PRICE) as monthly_avg_price,
    
    -- مقارنة مع الشهر السابق
    LAG(SUM(poi.TOTAL_PRICE)) OVER (PARTITION BY poi.ITEM_CODE ORDER BY TO_CHAR(po.PO_DATE, 'YYYY-MM')) as prev_month_value,
    LAG(SUM(poi.QUANTITY)) OVER (PARTITION BY poi.ITEM_CODE ORDER BY TO_CHAR(po.PO_DATE, 'YYYY-MM')) as prev_month_quantity,
    LAG(AVG(poi.UNIT_PRICE)) OVER (PARTITION BY poi.ITEM_CODE ORDER BY TO_CHAR(po.PO_DATE, 'YYYY-MM')) as prev_month_avg_price,
    
    -- حساب نسبة التغيير
    ROUND((SUM(poi.TOTAL_PRICE) - LAG(SUM(poi.TOTAL_PRICE)) OVER (PARTITION BY poi.ITEM_CODE ORDER BY TO_CHAR(po.PO_DATE, 'YYYY-MM'))) / 
          NULLIF(LAG(SUM(poi.TOTAL_PRICE)) OVER (PARTITION BY poi.ITEM_CODE ORDER BY TO_CHAR(po.PO_DATE, 'YYYY-MM')), 0) * 100, 2) as value_change_percentage,
    
    -- متوسط متحرك لـ 3 أشهر
    ROUND(AVG(SUM(poi.TOTAL_PRICE)) OVER (PARTITION BY poi.ITEM_CODE ORDER BY TO_CHAR(po.PO_DATE, 'YYYY-MM') ROWS 2 PRECEDING), 2) as three_month_avg_value

FROM PO_ITEMS poi
JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
WHERE po.STATUS != 'ملغي'
AND po.PO_DATE >= ADD_MONTHS(SYSDATE, -24) -- آخر سنتين
GROUP BY TO_CHAR(po.PO_DATE, 'YYYY-MM'), EXTRACT(YEAR FROM po.PO_DATE), EXTRACT(MONTH FROM po.PO_DATE), poi.ITEM_CODE, poi.ITEM_NAME
ORDER BY poi.ITEM_CODE, order_month;

-- 5. View تحليل الفئات
CREATE OR REPLACE VIEW V_CATEGORY_ANALYSIS AS
SELECT 
    ic.CATEGORY_CODE,
    ic.CATEGORY_NAME_AR,
    ic.CATEGORY_NAME_EN,
    
    -- إحصائيات الفئة
    COUNT(DISTINCT poi.ITEM_CODE) as unique_items_count,
    COUNT(*) as total_orders,
    SUM(poi.QUANTITY) as total_quantity,
    SUM(poi.TOTAL_PRICE) as total_value,
    AVG(poi.UNIT_PRICE) as avg_unit_price,
    
    -- نسبة الفئة من إجمالي المشتريات
    ROUND(SUM(poi.TOTAL_PRICE) / (SELECT SUM(poi2.TOTAL_PRICE) FROM PO_ITEMS poi2 JOIN PURCHASE_ORDERS po2 ON poi2.PO_ID = po2.ID WHERE po2.STATUS != 'ملغي') * 100, 2) as category_percentage,
    
    -- أعلى صنف في الفئة
    (SELECT poi3.ITEM_NAME FROM PO_ITEMS poi3 JOIN PURCHASE_ORDERS po3 ON poi3.PO_ID = po3.ID WHERE poi3.ITEM_CODE LIKE ic.CATEGORY_CODE || '%' AND po3.STATUS != 'ملغي' GROUP BY poi3.ITEM_CODE, poi3.ITEM_NAME ORDER BY SUM(poi3.TOTAL_PRICE) DESC FETCH FIRST 1 ROWS ONLY) as top_item_name,
    
    -- عدد الموردين للفئة
    COUNT(DISTINCT po.SUPPLIER_NAME) as suppliers_count,
    
    -- متوسط وقت التسليم للفئة
    ROUND(AVG(po.DELIVERY_DATE - po.PO_DATE), 1) as avg_delivery_days

FROM ITEM_CATEGORIES ic
LEFT JOIN PO_ITEMS poi ON poi.ITEM_CODE LIKE ic.CATEGORY_CODE || '%' -- ربط مؤقت، يحتاج تحسين
LEFT JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID AND po.STATUS != 'ملغي'
WHERE ic.IS_ACTIVE = 1
GROUP BY ic.CATEGORY_CODE, ic.CATEGORY_NAME_AR, ic.CATEGORY_NAME_EN
ORDER BY total_value DESC NULLS LAST;

-- 6. View لوحة المعلومات السريعة
CREATE OR REPLACE VIEW V_ITEMS_DASHBOARD_STATS AS
SELECT 
    -- إحصائيات عامة
    (SELECT COUNT(DISTINCT poi.ITEM_CODE) FROM PO_ITEMS poi JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID WHERE po.STATUS != 'ملغي') as total_unique_items,
    (SELECT COUNT(*) FROM PO_ITEMS poi JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID WHERE po.STATUS != 'ملغي') as total_item_orders,
    (SELECT SUM(poi.TOTAL_PRICE) FROM PO_ITEMS poi JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID WHERE po.STATUS != 'ملغي') as total_items_value,
    (SELECT COUNT(DISTINCT po.SUPPLIER_NAME) FROM PO_ITEMS poi JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID WHERE po.STATUS != 'ملغي') as total_suppliers,
    
    -- إحصائيات هذا الشهر
    (SELECT COUNT(DISTINCT poi.ITEM_CODE) FROM PO_ITEMS poi JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID WHERE po.STATUS != 'ملغي' AND EXTRACT(MONTH FROM po.PO_DATE) = EXTRACT(MONTH FROM SYSDATE) AND EXTRACT(YEAR FROM po.PO_DATE) = EXTRACT(YEAR FROM SYSDATE)) as this_month_items,
    (SELECT SUM(poi.TOTAL_PRICE) FROM PO_ITEMS poi JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID WHERE po.STATUS != 'ملغي' AND EXTRACT(MONTH FROM po.PO_DATE) = EXTRACT(MONTH FROM SYSDATE) AND EXTRACT(YEAR FROM po.PO_DATE) = EXTRACT(YEAR FROM SYSDATE)) as this_month_value,
    
    -- أعلى صنف قيمة
    (SELECT poi.ITEM_NAME FROM PO_ITEMS poi JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID WHERE po.STATUS != 'ملغي' GROUP BY poi.ITEM_CODE, poi.ITEM_NAME ORDER BY SUM(poi.TOTAL_PRICE) DESC FETCH FIRST 1 ROWS ONLY) as top_item_by_value,
    
    -- أعلى صنف كمية
    (SELECT poi.ITEM_NAME FROM PO_ITEMS poi JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID WHERE po.STATUS != 'ملغي' GROUP BY poi.ITEM_CODE, poi.ITEM_NAME ORDER BY SUM(poi.QUANTITY) DESC FETCH FIRST 1 ROWS ONLY) as top_item_by_quantity,
    
    -- أعلى مورد
    (SELECT po.SUPPLIER_NAME FROM PO_ITEMS poi JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID WHERE po.STATUS != 'ملغي' GROUP BY po.SUPPLIER_NAME ORDER BY SUM(poi.TOTAL_PRICE) DESC FETCH FIRST 1 ROWS ONLY) as top_supplier,
    
    -- تاريخ آخر تحديث
    SYSDATE as last_updated

FROM DUAL;

-- 7. إضافة تعليقات للـ Views
COMMENT ON VIEW V_PURCHASE_ITEMS_ANALYTICS IS 'View شامل لتحليل أصناف أوامر الشراء';
COMMENT ON VIEW V_ITEMS_ABC_ANALYSIS IS 'تحليل ABC للأصناف حسب القيمة';
COMMENT ON VIEW V_SUPPLIER_ITEMS_ANALYSIS IS 'تحليل الموردين حسب الأصناف';
COMMENT ON VIEW V_MONTHLY_ITEMS_TRENDS IS 'تحليل الاتجاهات الشهرية للأصناف';
COMMENT ON VIEW V_CATEGORY_ANALYSIS IS 'تحليل الفئات';
COMMENT ON VIEW V_ITEMS_DASHBOARD_STATS IS 'إحصائيات سريعة للوحة المعلومات';

PROMPT 'تم إنشاء Views تحليل أصناف أوامر الشراء بنجاح!';
