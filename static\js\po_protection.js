/**
 * 🛡️ نظام حماية أوامر الشراء
 * Purchase Order Protection System
 * 
 * يوفر وظائف لفحص وإظهار حماية أوامر الشراء المستخدمة في الشحنات
 */

class POProtectionManager {
    constructor() {
        this.protectionCache = new Map();
        this.init();
    }

    init() {
        console.log('🛡️ تم تهيئة نظام حماية أوامر الشراء');
        this.bindEvents();
    }

    bindEvents() {
        // ربط الأحداث بأزرار التعديل والحذف
        document.addEventListener('click', (e) => {
            if (e.target.matches('.edit-po-btn')) {
                this.handleEditAttempt(e);
            } else if (e.target.matches('.delete-po-btn')) {
                this.handleDeleteAttempt(e);
            }
        });

        // فحص الحماية عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            this.checkAllPOProtection();
        });
    }

    /**
     * فحص حماية أمر شراء واحد
     */
    async checkPOProtection(poId) {
        try {
            // فحص الكاش أولاً
            if (this.protectionCache.has(poId)) {
                return this.protectionCache.get(poId);
            }

            const response = await fetch(`/purchase_orders/api/check-protection/${poId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            
            if (result.success) {
                // حفظ في الكاش
                this.protectionCache.set(poId, result);
                return result;
            } else {
                console.error('فشل في فحص حماية أمر الشراء:', result.message);
                return null;
            }

        } catch (error) {
            console.error('خطأ في فحص حماية أمر الشراء:', error);
            return null;
        }
    }

    /**
     * فحص حماية عدة أوامر شراء دفعة واحدة
     */
    async checkMultiplePOProtection(poIds) {
        try {
            const response = await fetch('/purchase_orders/api/protection-status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ po_ids: poIds })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            
            if (result.success) {
                // حفظ النتائج في الكاش
                Object.entries(result.protection_status).forEach(([poId, status]) => {
                    this.protectionCache.set(parseInt(poId), {
                        success: true,
                        ...status,
                        po_id: parseInt(poId)
                    });
                });
                return result.protection_status;
            } else {
                console.error('فشل في فحص حماية أوامر الشراء:', result.message);
                return null;
            }

        } catch (error) {
            console.error('خطأ في فحص حماية أوامر الشراء:', error);
            return null;
        }
    }

    /**
     * فحص جميع أوامر الشراء في الصفحة الحالية
     */
    async checkAllPOProtection() {
        const poElements = document.querySelectorAll('[data-po-id]');
        const poIds = Array.from(poElements).map(el => parseInt(el.dataset.poId));

        if (poIds.length === 0) {
            return;
        }

        console.log(`🔍 فحص حماية ${poIds.length} أمر شراء...`);

        const protectionStatus = await this.checkMultiplePOProtection(poIds);
        
        if (protectionStatus) {
            // تطبيق الحماية على العناصر
            Object.entries(protectionStatus).forEach(([poId, status]) => {
                this.applyProtectionToElement(parseInt(poId), status);
            });
        }
    }

    /**
     * تطبيق الحماية على عنصر أمر الشراء
     */
    applyProtectionToElement(poId, protectionStatus) {
        const poElement = document.querySelector(`[data-po-id="${poId}"]`);
        if (!poElement) return;

        const editBtn = poElement.querySelector('.edit-po-btn');
        const deleteBtn = poElement.querySelector('.delete-po-btn');

        if (protectionStatus.protection_level === 'full') {
            // تطبيق الحماية الكاملة
            this.protectButton(editBtn, 'تعديل', protectionStatus);
            this.protectButton(deleteBtn, 'حذف', protectionStatus);

            // إضافة أيقونة الحماية
            this.addProtectionIcon(poElement, protectionStatus);
        } else {
            // إزالة الحماية إذا كانت موجودة
            this.unprotectButton(editBtn, 'تعديل');
            this.unprotectButton(deleteBtn, 'حذف');
            this.removeProtectionIcon(poElement);
        }
    }

    /**
     * حماية زر
     */
    protectButton(button, action, protectionStatus) {
        if (!button) return;

        button.disabled = true;
        button.classList.add('btn-protected');
        button.innerHTML = `🔒 ${action} محمي`;
        button.title = `لا يمكن ${action} أمر الشراء: ${protectionStatus.protection_reason}`;
        
        // إضافة كلاس CSS للتنسيق
        button.style.backgroundColor = '#6c757d';
        button.style.borderColor = '#6c757d';
        button.style.cursor = 'not-allowed';
    }

    /**
     * إزالة حماية زر
     */
    unprotectButton(button, action) {
        if (!button) return;

        button.disabled = false;
        button.classList.remove('btn-protected');
        button.innerHTML = action;
        button.title = '';
        
        // إزالة التنسيق المخصص
        button.style.backgroundColor = '';
        button.style.borderColor = '';
        button.style.cursor = '';
    }

    /**
     * إضافة أيقونة الحماية
     */
    addProtectionIcon(poElement, protectionStatus) {
        // إزالة الأيقونة الموجودة إن وجدت
        this.removeProtectionIcon(poElement);

        const protectionIcon = document.createElement('span');
        protectionIcon.className = 'protection-icon badge badge-warning ms-2';
        protectionIcon.innerHTML = '🛡️ محمي';
        protectionIcon.title = protectionStatus.protection_reason;

        // إضافة الأيقونة بجانب رقم أمر الشراء
        const poNumberElement = poElement.querySelector('.po-number');
        if (poNumberElement) {
            poNumberElement.appendChild(protectionIcon);
        }
    }

    /**
     * إزالة أيقونة الحماية
     */
    removeProtectionIcon(poElement) {
        const existingIcon = poElement.querySelector('.protection-icon');
        if (existingIcon) {
            existingIcon.remove();
        }
    }

    /**
     * معالجة محاولة التعديل
     */
    async handleEditAttempt(event) {
        const button = event.target;
        const poId = parseInt(button.dataset.poId || button.closest('[data-po-id]')?.dataset.poId);
        
        if (!poId) return;

        const protectionStatus = await this.checkPOProtection(poId);
        
        if (protectionStatus && !protectionStatus.can_edit) {
            event.preventDefault();
            event.stopPropagation();
            
            this.showProtectionAlert('تعديل', protectionStatus);
            return false;
        }
    }

    /**
     * معالجة محاولة الحذف
     */
    async handleDeleteAttempt(event) {
        const button = event.target;
        const poId = parseInt(button.dataset.poId || button.closest('[data-po-id]')?.dataset.poId);
        
        if (!poId) return;

        const protectionStatus = await this.checkPOProtection(poId);
        
        if (protectionStatus && !protectionStatus.can_delete) {
            event.preventDefault();
            event.stopPropagation();
            
            this.showProtectionAlert('حذف', protectionStatus);
            return false;
        }
    }

    /**
     * إظهار تنبيه الحماية
     */
    showProtectionAlert(action, protectionStatus) {
        const message = `لا يمكن ${action} أمر الشراء ${protectionStatus.po_number}\n\nالسبب: ${protectionStatus.protection_reason}`;
        
        // استخدام SweetAlert إذا كان متاحاً، وإلا استخدم alert عادي
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'warning',
                title: `🛡️ أمر شراء محمي`,
                text: message,
                confirmButtonText: 'موافق'
            });
        } else {
            alert(`🛡️ أمر شراء محمي\n\n${message}`);
        }
    }

    /**
     * تحديث حالة الحماية لأمر شراء معين
     */
    async refreshProtection(poId) {
        // إزالة من الكاش
        this.protectionCache.delete(poId);
        
        // فحص جديد
        const protectionStatus = await this.checkPOProtection(poId);
        
        if (protectionStatus) {
            this.applyProtectionToElement(poId, protectionStatus);
        }
    }

    /**
     * تحديث حماية جميع أوامر الشراء
     */
    async refreshAllProtection() {
        this.protectionCache.clear();
        await this.checkAllPOProtection();
    }
}

// إنشاء مثيل عام من مدير الحماية
window.poProtectionManager = new POProtectionManager();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = POProtectionManager;
}
