#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام الحقيقي لـ COSCO
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.tracking_service import tracking_service

def test_cosco_real_system():
    """اختبار النظام الحقيقي لـ COSCO"""
    
    print("=" * 80)
    print("🧪 اختبار النظام الحقيقي لـ COSCO")
    print("=" * 80)
    
    # أرقام اختبار مختلفة
    test_cases = [
        {
            'number': 'COSU1234567890',
            'type': 'container',
            'description': 'رقم حاوية تجريبي'
        },
        {
            'number': 'TEST123456',
            'type': 'booking',
            'description': 'رقم حجز تجريبي'
        },
        {
            'number': 'COSCO2024001',
            'type': 'booking',
            'description': 'رقم حجز تجريبي آخر'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 اختبار {i}: {test_case['description']}")
        print(f"📋 الرقم: {test_case['number']}")
        print(f"📋 النوع: {test_case['type']}")
        print("-" * 60)
        
        try:
            # استدعاء النظام الحقيقي
            success, schedule_data, message = tracking_service.get_shipping_schedule(
                shipping_company='COSCO',
                reference_number=test_case['number'],
                reference_type=test_case['type']
            )
            
            print(f"📊 النتيجة: {'✅ نجح' if success else '❌ فشل'}")
            print(f"📝 الرسالة: {message}")
            
            if success and schedule_data:
                print(f"📅 ETD: {schedule_data.get('etd', 'غير محدد')}")
                print(f"📅 ETA: {schedule_data.get('eta', 'غير محدد')}")
                print(f"🚢 السفينة: {schedule_data.get('vessel_name', 'غير محدد')}")
                print(f"🔢 الرحلة: {schedule_data.get('voyage_number', 'غير محدد')}")
                print(f"🏭 ميناء التحميل: {schedule_data.get('port_of_loading', 'غير محدد')}")
                print(f"🏭 ميناء التفريغ: {schedule_data.get('port_of_discharge', 'غير محدد')}")
                print(f"⏱️ مدة الشحن: {schedule_data.get('transit_time_days', 'غير محدد')} يوم")
                print(f"🔗 المصدر: {schedule_data.get('source', 'غير محدد')}")
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
        
        print("-" * 60)
    
    print(f"\n" + "=" * 80)
    print("✅ انتهى الاختبار")
    print("=" * 80)

def test_cosco_direct():
    """اختبار مباشر لدوال COSCO"""
    
    print("\n🔧 اختبار مباشر لدوال COSCO...")
    
    try:
        # اختبار دالة COSCO المتقدمة مباشرة
        result = tracking_service._scrape_cosco_advanced('TEST123456', 'booking')
        
        print(f"📊 النتيجة المباشرة: {'✅ نجح' if result[0] else '❌ فشل'}")
        print(f"📝 الرسالة: {result[2]}")
        
        if result[0] and result[1]:
            print(f"📋 البيانات: {result[1]}")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار المباشر: {e}")

if __name__ == "__main__":
    # اختبار النظام الكامل
    test_cosco_real_system()
    
    # اختبار مباشر
    test_cosco_direct()
