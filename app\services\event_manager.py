# -*- coding: utf-8 -*-
"""
نظام إدارة الأحداث الفورية
Event Management System for Instant Actions
"""

import logging
from typing import Dict, List, Callable, Any
from datetime import datetime
import json

class EventManager:
    """مدير الأحداث الفورية"""
    
    def __init__(self):
        """تهيئة مدير الأحداث"""
        self.listeners: Dict[str, List[Callable]] = {}
        self.logger = logging.getLogger(__name__)
        self.event_history: List[Dict] = []
        self.max_history = 100  # الاحتفاظ بآخر 100 حدث
        
        self.logger.info("🎯 تم تهيئة مدير الأحداث الفورية")
    
    def on(self, event_name: str, callback: Callable):
        """تسجيل مستمع للحدث
        
        Args:
            event_name: اسم الحدث
            callback: الدالة التي ستستدعى عند إطلاق الحدث
        """
        if event_name not in self.listeners:
            self.listeners[event_name] = []
        
        self.listeners[event_name].append(callback)
        self.logger.info(f"📝 تم تسجيل مستمع للحدث: {event_name}")
    
    def fire(self, event_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """إطلاق الحدث فورياً
        
        Args:
            event_name: اسم الحدث
            data: بيانات الحدث
            
        Returns:
            نتائج تنفيذ المستمعين
        """
        start_time = datetime.now()
        
        self.logger.info(f"🔥 إطلاق الحدث: {event_name}")
        self.logger.info(f"📋 بيانات الحدث: {data}")
        
        results = {
            'event_name': event_name,
            'fired_at': start_time.isoformat(),
            'listeners_count': 0,
            'success_count': 0,
            'error_count': 0,
            'results': [],
            'errors': []
        }
        
        if event_name not in self.listeners:
            self.logger.warning(f"⚠️ لا يوجد مستمعين للحدث: {event_name}")
            self._add_to_history(results)
            return results
        
        listeners = self.listeners[event_name]
        results['listeners_count'] = len(listeners)
        
        self.logger.info(f"👥 عدد المستمعين: {len(listeners)}")
        
        for i, callback in enumerate(listeners):
            try:
                self.logger.info(f"🔄 تنفيذ المستمع {i+1}/{len(listeners)}")
                
                # تنفيذ المستمع
                result = callback(data)
                
                results['results'].append({
                    'listener_index': i,
                    'success': True,
                    'result': result,
                    'executed_at': datetime.now().isoformat()
                })
                
                results['success_count'] += 1
                self.logger.info(f"✅ نجح تنفيذ المستمع {i+1}")
                
            except Exception as e:
                error_msg = str(e)
                self.logger.error(f"❌ خطأ في تنفيذ المستمع {i+1}: {error_msg}")
                
                results['errors'].append({
                    'listener_index': i,
                    'error': error_msg,
                    'occurred_at': datetime.now().isoformat()
                })
                
                results['error_count'] += 1
        
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        results['execution_time_seconds'] = execution_time
        results['completed_at'] = end_time.isoformat()
        
        self.logger.info(f"🎉 انتهى تنفيذ الحدث {event_name}")
        self.logger.info(f"📊 النتائج: {results['success_count']} نجح، {results['error_count']} فشل")
        self.logger.info(f"⏱️ وقت التنفيذ: {execution_time:.3f} ثانية")
        
        # إضافة للتاريخ
        self._add_to_history(results)
        
        return results
    
    def _add_to_history(self, event_result: Dict):
        """إضافة الحدث لتاريخ الأحداث"""
        self.event_history.append(event_result)
        
        # الاحتفاظ بآخر 100 حدث فقط
        if len(self.event_history) > self.max_history:
            self.event_history = self.event_history[-self.max_history:]
    
    def get_listeners(self, event_name: str = None) -> Dict:
        """الحصول على قائمة المستمعين
        
        Args:
            event_name: اسم الحدث (اختياري)
            
        Returns:
            قائمة المستمعين
        """
        if event_name:
            return {
                event_name: len(self.listeners.get(event_name, []))
            }
        
        return {
            event: len(listeners) 
            for event, listeners in self.listeners.items()
        }
    
    def get_event_history(self, limit: int = 10) -> List[Dict]:
        """الحصول على تاريخ الأحداث
        
        Args:
            limit: عدد الأحداث المطلوبة
            
        Returns:
            قائمة آخر الأحداث
        """
        return self.event_history[-limit:] if self.event_history else []
    
    def get_statistics(self) -> Dict:
        """الحصول على إحصائيات الأحداث"""
        total_events = len(self.event_history)
        total_success = sum(event['success_count'] for event in self.event_history)
        total_errors = sum(event['error_count'] for event in self.event_history)
        
        # أكثر الأحداث استخداماً
        event_counts = {}
        for event in self.event_history:
            event_name = event['event_name']
            event_counts[event_name] = event_counts.get(event_name, 0) + 1
        
        most_used_events = sorted(event_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            'total_events_fired': total_events,
            'total_successful_executions': total_success,
            'total_errors': total_errors,
            'success_rate': (total_success / max(total_success + total_errors, 1)) * 100,
            'registered_listeners': self.get_listeners(),
            'most_used_events': most_used_events,
            'last_event': self.event_history[-1] if self.event_history else None
        }

# إنشاء مثيل عام من مدير الأحداث
event_manager = EventManager()

# دالة مساعدة للتسجيل السريع
def on_event(event_name: str):
    """ديكوريتر لتسجيل مستمع للحدث
    
    Usage:
        @on_event('shipment_status_changed')
        def handle_status_change(data):
            print(f"تغيرت حالة الشحنة: {data}")
    """
    def decorator(func):
        event_manager.on(event_name, func)
        return func
    return decorator

# دالة مساعدة لإطلاق الأحداث
def fire_event(event_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
    """إطلاق حدث فوري
    
    Args:
        event_name: اسم الحدث
        data: بيانات الحدث
        
    Returns:
        نتائج التنفيذ
    """
    return event_manager.fire(event_name, data)
