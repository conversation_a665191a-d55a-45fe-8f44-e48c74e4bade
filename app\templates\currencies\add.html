<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة عملة جديدة</title>
    
    <!-- Bootstrap 5.3 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 15px;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            margin: 0 auto;
            max-width: 1200px;
        }
        
        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 25px 30px;
            color: white;
            position: relative;
        }
        
        .breadcrumb-modern {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 8px 20px;
            margin-bottom: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 0.9rem;
        }
        
        .breadcrumb-modern .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-weight: 500;
        }
        
        .breadcrumb-modern .breadcrumb-item.active {
            color: white;
            font-weight: 600;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 8px;
            line-height: 1.2;
        }
        
        .page-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            margin: 0;
        }
        
        .content-section {
            padding: 25px 30px;
            background: white;
        }
        
        .form-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border-left: 4px solid #667eea;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f5f9;
        }
        
        .section-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            font-size: 1.2rem;
            color: white;
            flex-shrink: 0;
        }
        
        .section-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0 0 4px 0;
        }
        
        .section-subtitle {
            color: #64748b;
            font-size: 0.9rem;
            margin: 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .form-control, .form-select {
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: #fafbfc;
            font-weight: 500;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }
        
        .form-check-modern {
            background: #f8fafc;
            border-radius: 12px;
            padding: 15px;
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }
        
        .form-check-modern:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        .form-check-input {
            width: 1.3rem;
            height: 1.3rem;
            border-radius: 6px;
            border: 2px solid #d1d5db;
        }
        
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .form-check-label {
            font-weight: 600;
            color: #374151;
            margin-right: 12px;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .preview-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 20px;
            color: white;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .preview-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .preview-amount {
            font-size: 2.2rem;
            font-weight: 800;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .preview-details {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 15px;
        }
        
        .preview-detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.85rem;
            font-weight: 500;
        }
        
        .preview-detail-item:last-child {
            margin-bottom: 0;
        }
        
        .preview-detail-value {
            font-weight: 700;
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 6px;
        }
        
        .action-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            position: sticky;
            top: 20px;
        }
        
        .btn-premium {
            padding: 12px 20px;
            border-radius: 10px;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            min-height: 45px;
            text-decoration: none;
            cursor: pointer;
        }
        
        .btn-premium:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            text-decoration: none;
        }
        
        .btn-save {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }
        
        .btn-reset {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            color: white;
        }
        
        .btn-back {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 18px;
            height: 18px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
            }
            
            .header-section, .content-section {
                padding: 20px;
            }
            
            .page-title {
                font-size: 1.6rem;
            }
            
            .action-section {
                position: static;
                margin-top: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-modern">
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('main.dashboard') }}">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('currencies.index') }}">
                            <i class="fas fa-coins"></i> إدارة العملات
                        </a>
                    </li>
                    <li class="breadcrumb-item active">إضافة عملة جديدة</li>
                </ol>
            </nav>

            <h1 class="page-title">
                <i class="fas fa-plus-circle"></i>
                إضافة عملة جديدة
            </h1>
            <p class="page-subtitle">إنشاء عملة جديدة وتكوين إعداداتها في النظام</p>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <div class="row">
                <!-- العمود الأيسر - النموذج -->
                <div class="col-lg-8">
                    <!-- نموذج الإضافة -->
                    <form id="addCurrencyForm">
                        <!-- المعلومات الأساسية -->
                        <div class="form-section">
                            <div class="section-header">
                                <div class="section-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div>
                                    <h3 class="section-title">المعلومات الأساسية</h3>
                                    <p class="section-subtitle">بيانات العملة الأساسية والمعرفات</p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-hashtag"></i> كود العملة *
                                        </label>
                                        <input type="text" class="form-control" name="code" required
                                               placeholder="مثال: SAR, USD, EUR" maxlength="3">
                                        <small class="text-muted">كود مكون من 3 أحرف (ISO 4217)</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-dollar-sign"></i> رمز العملة *
                                        </label>
                                        <input type="text" class="form-control" name="symbol"
                                               onchange="updatePreview()" required
                                               placeholder="مثال: $, €, ﷼">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-font"></i> الاسم بالعربية *
                                        </label>
                                        <input type="text" class="form-control" name="name_ar" required
                                               placeholder="مثال: الريال السعودي">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-globe"></i> الاسم بالإنجليزية *
                                        </label>
                                        <input type="text" class="form-control" name="name_en" required
                                               placeholder="Example: Saudi Riyal">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-exchange-alt"></i> سعر الصرف *
                                        </label>
                                        <input type="number" class="form-control" name="exchange_rate"
                                               step="0.0001" min="0.0001" value="1.0000" required
                                               placeholder="1.0000">
                                        <small class="text-muted">سعر الصرف مقابل العملة الأساسية</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-flag"></i> الدولة
                                        </label>
                                        <input type="text" class="form-control" name="country"
                                               placeholder="مثال: المملكة العربية السعودية">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات التنسيق -->
                        <div class="form-section">
                            <div class="section-header">
                                <div class="section-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <div>
                                    <h3 class="section-title">إعدادات التنسيق</h3>
                                    <p class="section-subtitle">تخصيص طريقة عرض العملة</p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-sort-numeric-up"></i> المنازل العشرية
                                        </label>
                                        <select class="form-select" name="decimal_places" onchange="updatePreview()">
                                            <option value="0">0 - بدون كسور</option>
                                            <option value="1">1 - منزلة واحدة</option>
                                            <option value="2" selected>2 - منزلتان (الأكثر شيوعاً)</option>
                                            <option value="3">3 - ثلاث منازل</option>
                                            <option value="4">4 - أربع منازل</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-arrows-alt-h"></i> موضع الرمز
                                        </label>
                                        <select class="form-select" name="position" onchange="updatePreview()">
                                            <option value="before" selected>قبل المبلغ (مثال: $100)</option>
                                            <option value="after">بعد المبلغ (مثال: 100$)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-grip-lines-vertical"></i> فاصل الآلاف
                                        </label>
                                        <select class="form-select" name="thousands_separator" onchange="updatePreview()">
                                            <option value="," selected>, (فاصلة) - 1,234</option>
                                            <option value=".">. (نقطة) - 1.234</option>
                                            <option value=" ">(مسافة) - 1 234</option>
                                            <option value="">بدون فاصل - 1234</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-circle"></i> فاصل العشرية
                                        </label>
                                        <select class="form-select" name="decimal_separator" onchange="updatePreview()">
                                            <option value="." selected>. (نقطة) - 123.45</option>
                                            <option value=".">, (فاصلة) - 123,45</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات النظام -->
                        <div class="form-section">
                            <div class="section-header">
                                <div class="section-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                    <i class="fas fa-sliders-h"></i>
                                </div>
                                <div>
                                    <h3 class="section-title">إعدادات النظام</h3>
                                    <p class="section-subtitle">تحكم في حالة العملة ونوعها</p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check-modern">
                                        <input class="form-check-input" type="checkbox" name="is_active"
                                               checked id="isActive">
                                        <label class="form-check-label" for="isActive">
                                            <i class="fas fa-power-off"></i> عملة نشطة
                                        </label>
                                        <small class="form-text text-muted d-block mt-1">
                                            العملات غير النشطة لا تظهر في القوائم
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check-modern">
                                        <input class="form-check-input" type="checkbox" name="is_base_currency"
                                               id="isBaseCurrency">
                                        <label class="form-check-label" for="isBaseCurrency">
                                            <i class="fas fa-star"></i> عملة أساسية
                                        </label>
                                        <small class="form-text text-muted d-block mt-1">
                                            العملة المرجعية لحساب أسعار الصرف
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- العمود الأيمن - المعاينة والأزرار -->
                <div class="col-lg-4">
                    <!-- معاينة العملة -->
                    <div class="preview-card">
                        <div class="preview-title">
                            <i class="fas fa-eye"></i> معاينة العملة
                        </div>
                        <div class="preview-amount" id="currencyPreview">
                            $1,234.56
                        </div>
                        <div class="preview-details">
                            <div class="preview-detail-item">
                                <span>المنازل العشرية:</span>
                                <span class="preview-detail-value" id="previewDecimals">2</span>
                            </div>
                            <div class="preview-detail-item">
                                <span>فاصل الآلاف:</span>
                                <span class="preview-detail-value" id="previewThousands">,</span>
                            </div>
                            <div class="preview-detail-item">
                                <span>فاصل العشرية:</span>
                                <span class="preview-detail-value" id="previewDecimal">.</span>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="action-section">
                        <div class="d-grid gap-2">
                            <button type="button" class="btn-premium btn-save" onclick="saveCurrency()">
                                <i class="fas fa-plus"></i> إضافة العملة
                            </button>

                            <button type="button" class="btn-premium btn-reset" onclick="resetForm()">
                                <i class="fas fa-undo"></i> إعادة تعيين
                            </button>

                            <a href="{{ url_for('currencies.index') }}" class="btn-premium btn-back">
                                <i class="fas fa-arrow-right"></i> العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // تحديث المعاينة
        function updatePreview() {
            try {
                const previewElement = document.getElementById('currencyPreview');
                if (!previewElement) return;

                let symbol = '$';
                let position = 'before';
                let decimalPlaces = 2;
                let thousandsSeparator = ',';
                let decimalSeparator = '.';

                const symbolInput = document.querySelector('input[name="symbol"]');
                const positionSelect = document.querySelector('select[name="position"]');
                const decimalPlacesSelect = document.querySelector('select[name="decimal_places"]');
                const thousandsSeparatorSelect = document.querySelector('select[name="thousands_separator"]');
                const decimalSeparatorSelect = document.querySelector('select[name="decimal_separator"]');

                if (symbolInput && symbolInput.value) symbol = symbolInput.value;
                if (positionSelect && positionSelect.value) position = positionSelect.value;
                if (decimalPlacesSelect && decimalPlacesSelect.value) decimalPlaces = parseInt(decimalPlacesSelect.value);
                if (thousandsSeparatorSelect && thousandsSeparatorSelect.value !== undefined) thousandsSeparator = thousandsSeparatorSelect.value;
                if (decimalSeparatorSelect && decimalSeparatorSelect.value) decimalSeparator = decimalSeparatorSelect.value;

                let amount = 1234.56;
                let formattedAmount = decimalPlaces === 0 ? Math.round(amount).toString() : amount.toFixed(decimalPlaces);

                if (thousandsSeparator) {
                    const parts = formattedAmount.split('.');
                    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator);
                    formattedAmount = parts.join(decimalSeparator);
                } else if (decimalSeparator !== '.') {
                    formattedAmount = formattedAmount.replace('.', decimalSeparator);
                }

                previewElement.style.opacity = '0.7';
                setTimeout(() => {
                    previewElement.innerHTML = position === 'before' ? `${symbol} ${formattedAmount}` : `${formattedAmount} ${symbol}`;
                    previewElement.style.opacity = '1';
                }, 150);

                const previewDecimals = document.getElementById('previewDecimals');
                const previewThousands = document.getElementById('previewThousands');
                const previewDecimal = document.getElementById('previewDecimal');

                if (previewDecimals) previewDecimals.textContent = decimalPlaces;
                if (previewThousands) previewThousands.textContent = thousandsSeparator || 'بدون';
                if (previewDecimal) previewDecimal.textContent = decimalSeparator;

            } catch (error) {
                console.error('خطأ في تحديث المعاينة:', error);
            }
        }

        // حفظ العملة
        function saveCurrency() {
            const form = document.getElementById('addCurrencyForm');
            if (!form) {
                showAlert('لم يتم العثور على النموذج', 'error');
                return;
            }

            const formData = new FormData(form);

            // التحقق من الحقول المطلوبة
            const requiredFields = ['code', 'symbol', 'name_ar', 'name_en', 'exchange_rate'];
            for (let field of requiredFields) {
                if (!formData.get(field)) {
                    showAlert(`الحقل "${field}" مطلوب`, 'error');
                    return;
                }
            }

            const isActiveCheckbox = document.querySelector('input[name="is_active"]');
            const isBaseCurrencyCheckbox = document.querySelector('input[name="is_base_currency"]');

            const data = {
                code: formData.get('code').toUpperCase(),
                name_ar: formData.get('name_ar'),
                name_en: formData.get('name_en'),
                symbol: formData.get('symbol'),
                decimal_places: formData.get('decimal_places'),
                position: formData.get('position'),
                thousands_separator: formData.get('thousands_separator'),
                decimal_separator: formData.get('decimal_separator'),
                exchange_rate: parseFloat(formData.get('exchange_rate')),
                country: formData.get('country') || '',
                is_active: isActiveCheckbox ? (isActiveCheckbox.checked ? 1 : 0) : 1,
                is_base_currency: isBaseCurrencyCheckbox ? (isBaseCurrencyCheckbox.checked ? 1 : 0) : 0
            };

            const saveBtn = document.querySelector('.btn-save');
            if (!saveBtn) return;

            const originalContent = saveBtn.innerHTML;
            saveBtn.innerHTML = '<div class="loading-spinner"></div> جاري الإضافة...';
            saveBtn.disabled = true;

            fetch('{{ url_for("currencies.add_currency") }}', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    saveBtn.innerHTML = '<i class="fas fa-check"></i> تمت الإضافة!';
                    showAlert('تم إضافة العملة بنجاح! 🎉', 'success');
                    setTimeout(() => window.location.href = '{{ url_for("currencies.index") }}', 2000);
                } else {
                    showAlert(data.message || 'حدث خطأ غير معروف', 'error');
                    saveBtn.innerHTML = originalContent;
                    saveBtn.disabled = false;
                }
            })
            .catch(error => {
                showAlert('حدث خطأ في الاتصال', 'error');
                saveBtn.innerHTML = originalContent;
                saveBtn.disabled = false;
            });
        }

        // إعادة تعيين النموذج
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟')) {
                document.getElementById('addCurrencyForm').reset();
                updatePreview();
            }
        }

        // عرض الرسائل
        function showAlert(message, type = 'info') {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show"
                     style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px; border-radius: 10px;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', alertHtml);
            setTimeout(() => {
                const alert = document.querySelector('.alert');
                if (alert) alert.remove();
            }, 3000);
        }

        // تحديث المعاينة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', updatePreview);
    </script>
</body>
</html>
