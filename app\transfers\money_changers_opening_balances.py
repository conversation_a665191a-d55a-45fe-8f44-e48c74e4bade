#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نظام الأرصدة الافتتاحية للصرافين والبنوك
Money Changers/Banks Opening Balances System
"""

from flask import Blueprint, render_template, request, jsonify, session, flash, redirect, url_for
from flask_login import login_required, current_user
from database_manager import DatabaseManager
from . import transfers_bp
import logging
import json
from datetime import datetime

logger = logging.getLogger(__name__)

print("✅ تم تحميل ملف money_changers_opening_balances.py")

@transfers_bp.route('/money-changers-opening-balances')
@login_required
def money_changers_opening_balances():
    """صفحة إدارة الأرصدة الافتتاحية للصرافين والبنوك"""
    try:
        db_manager = DatabaseManager()
        # DatabaseManager يتم تهيئته تلقائياً ولا يحتاج connect()
        
        # جلب الصرافين/البنوك النشطين
        query = """
        SELECT
            mcb.id,
            mcb.name,
            mcb.type,
            mcb.contact_person,
            mcb.phone,
            mcb.email,
            mcb.is_active,
            -- التحقق من وجود رصيد افتتاحي للفترة الحالية في النظام المركزي
            CASE
                WHEN EXISTS (
                    SELECT 1 FROM OPENING_BALANCES ob
                    WHERE ob.entity_type_code = 'MONEY_CHANGER'
                    AND ob.entity_id = mcb.id
                    AND ob.fiscal_period_start_date = DATE '2024-01-01'
                    AND ob.is_active = 1
                ) THEN 1 ELSE 0
            END as has_opening_balance
        FROM money_changers_banks mcb
        WHERE mcb.is_active = 1
        ORDER BY mcb.name
        """
        
        money_changers = db_manager.execute_query(query)
        
        # تحويل النتائج إلى قائمة قواميس
        money_changers_list = []
        if money_changers:
            for row in money_changers:
                money_changers_list.append({
                    'id': row[0],
                    'name': row[1],
                    'type': row[2],
                    'contact_person': row[3],
                    'phone': row[4],
                    'email': row[5],
                    'is_active': row[6],
                    'has_opening_balance': row[7]
                })
        
        # قائمة العملات المدعومة
        currencies = [
            {'code': 'SAR', 'name': 'ريال سعودي'},
            {'code': 'USD', 'name': 'دولار أمريكي'},
            {'code': 'EUR', 'name': 'يورو'},
            {'code': 'AED', 'name': 'درهم إماراتي'},
            {'code': 'YER', 'name': 'ريال يمني'},
            {'code': 'EGP', 'name': 'جنيه مصري'},
            {'code': 'JOD', 'name': 'دينار أردني'},
            {'code': 'KWD', 'name': 'دينار كويتي'},
            {'code': 'QAR', 'name': 'ريال قطري'},
            {'code': 'BHD', 'name': 'دينار بحريني'},
            {'code': 'OMR', 'name': 'ريال عماني'}
        ]
        
        return render_template('transfers/money_changers_opening_balances.html', 
                             money_changers=money_changers_list, 
                             currencies=currencies)
        
    except Exception as e:
        logger.error(f"خطأ في صفحة الأرصدة الافتتاحية للصرافين: {e}")
        flash('حدث خطأ في تحميل الصفحة', 'error')
        return render_template('transfers/money_changers_opening_balances.html', 
                             money_changers=[], currencies=[])

@transfers_bp.route('/api/money-changers-opening-balances')
def get_money_changers_opening_balances():
    """جلب الأرصدة الافتتاحية الموجودة للصرافين/البنوك"""
    try:
        fiscal_date = request.args.get('fiscal_date', '2024-01-01')
        currency_code = request.args.get('currency_code', '')
        status_filter = request.args.get('status', 'all')
        money_changer_id = request.args.get('money_changer_id', '')
        
        db_manager = DatabaseManager()
        # DatabaseManager يتم تهيئته تلقائياً
        
        # بناء الاستعلام للنظام المركزي
        where_conditions = [
            "ob.entity_type_code = 'MONEY_CHANGER'",
            "ob.fiscal_period_start_date = TO_DATE(:fiscal_date, 'YYYY-MM-DD')",
            "ob.is_active = 1"
        ]

        params = {
            'fiscal_date': fiscal_date
        }

        if currency_code:
            where_conditions.append("ob.currency_code = :currency_code")
            params['currency_code'] = currency_code

        if status_filter != 'all':
            where_conditions.append("ob.status = :status")
            params['status'] = status_filter.upper()

        if money_changer_id:
            where_conditions.append("ob.entity_id = :money_changer_id")
            params['money_changer_id'] = money_changer_id

        query = f"""
        SELECT 
            ob.id,
            ob.entity_id as money_changer_id,
            mcb.name as money_changer_name,
            mcb.type as money_changer_type,
            ob.currency_code,
            ob.opening_balance_amount,
            ob.balance_type,
            ob.exchange_rate,
            ob.status,
            ob.notes,
            ob.reference_document,
            ob.created_date,
            ob.updated_date
        FROM OPENING_BALANCES ob
        JOIN money_changers_banks mcb ON ob.entity_id = mcb.id
        WHERE {' AND '.join(where_conditions)}
        ORDER BY mcb.name, ob.currency_code
        """
        
        result = db_manager.execute_query(query, params)
        
        opening_balances = []
        if result:
            for row in result:
                opening_balances.append({
                    'id': row[0],
                    'money_changer_id': row[1],
                    'money_changer_name': row[2],
                    'money_changer_type': row[3],
                    'currency_code': row[4],
                    'opening_balance_amount': float(row[5]) if row[5] else 0,
                    'balance_type': row[6],
                    'exchange_rate': float(row[7]) if row[7] else 1,
                    'status': row[8],
                    'notes': row[9],
                    'reference_document': row[10],
                    'created_date': row[11].strftime('%Y-%m-%d %H:%M:%S') if row[11] else '',
                    'updated_date': row[12].strftime('%Y-%m-%d %H:%M:%S') if row[12] else ''
                })
        
        return jsonify({
            'success': True,
            'opening_balances': opening_balances
        })
        
    except Exception as e:
        logger.error(f"خطأ في جلب الأرصدة الافتتاحية: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب البيانات: {str(e)}'
        }), 500

@transfers_bp.route('/api/money-changers-opening-balances/add', methods=['POST'])
@login_required
def add_money_changer_opening_balance():
    """إضافة رصيد افتتاحي جديد للصراف/البنك"""
    try:
        data = request.get_json()
        
        # التحقق من البيانات المطلوبة
        required_fields = ['money_changer_id', 'fiscal_date', 'currency_code', 'amount']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'success': False,
                    'message': f'الحقل {field} مطلوب'
                }), 400
        
        db_manager = DatabaseManager()
        # DatabaseManager يتم تهيئته تلقائياً
        
        # التحقق من عدم وجود رصيد مكرر
        check_query = """
        SELECT COUNT(*) FROM OPENING_BALANCES
        WHERE entity_type_code = 'MONEY_CHANGER'
        AND entity_id = :money_changer_id
        AND fiscal_period_start_date = TO_DATE(:fiscal_date, 'YYYY-MM-DD')
        AND currency_code = :currency_code
        AND is_active = 1
        """
        
        existing = db_manager.execute_query(check_query, {
            'money_changer_id': data['money_changer_id'],
            'fiscal_date': data['fiscal_date'],
            'currency_code': data['currency_code']
        })

        if existing and existing[0][0] > 0:
            return jsonify({
                'success': False,
                'message': 'يوجد رصيد افتتاحي مسبق لهذا الصراف/البنك في نفس الفترة والعملة'
            }), 400
        
        # إدراج الرصيد الجديد
        insert_query = """
        INSERT INTO OPENING_BALANCES (
            entity_type_code, entity_id, fiscal_year, fiscal_period_start_date,
            currency_code, opening_balance_amount, exchange_rate, balance_type,
            notes, reference_document, created_by, status, document_type_code
        ) VALUES (
            'MONEY_CHANGER', :money_changer_id, EXTRACT(YEAR FROM TO_DATE(:fiscal_date, 'YYYY-MM-DD')),
            TO_DATE(:fiscal_date, 'YYYY-MM-DD'), :currency_code, :amount, :exchange_rate,
            :balance_type, :notes, :reference_doc, :user_id, 'DRAFT', 'OPENING_BALANCE'
        )
        """
        
        db_manager.execute_update(insert_query, {
            'money_changer_id': data['money_changer_id'],
            'fiscal_date': data['fiscal_date'],
            'currency_code': data['currency_code'],
            'amount': data['amount'],
            'exchange_rate': data.get('exchange_rate', 1.0),
            'balance_type': data.get('balance_type', 'DEBIT'),
            'notes': data.get('notes', ''),
            'reference_doc': data.get('reference_document', ''),
            'user_id': session.get('user_id', 1)
        })

        db_manager.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم إضافة الرصيد الافتتاحي بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في إضافة الرصيد الافتتاحي: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في إضافة الرصيد: {str(e)}'
        }), 500

@transfers_bp.route('/api/money-changers-opening-balances/update/<int:balance_id>', methods=['PUT'])
@login_required
def update_money_changer_opening_balance(balance_id):
    """تحديث رصيد افتتاحي للصراف/البنك"""
    try:
        data = request.get_json()

        db_manager = DatabaseManager()
        # DatabaseManager يتم تهيئته تلقائياً

        # التحقق من وجود الرصيد
        check_query = """
        SELECT status FROM OPENING_BALANCES
        WHERE id = :balance_id AND entity_type_code = 'MONEY_CHANGER'
        """

        existing = db_manager.execute_query(check_query, {'balance_id': balance_id})

        if not existing:
            return jsonify({
                'success': False,
                'message': 'الرصيد الافتتاحي غير موجود'
            }), 404

        current_status = existing[0][0]

        # التحقق من إمكانية التعديل
        if current_status in ['POSTED', 'APPROVED']:
            return jsonify({
                'success': False,
                'message': 'لا يمكن تعديل رصيد معتمد أو مرحل'
            }), 400

        # تحديث البيانات
        update_query = """
        UPDATE OPENING_BALANCES SET
            opening_balance_amount = :amount,
            exchange_rate = :exchange_rate,
            balance_type = :balance_type,
            notes = :notes,
            reference_document = :reference_doc,
            updated_date = CURRENT_TIMESTAMP,
            updated_by = :user_id
        WHERE id = :balance_id
        """

        db_manager.execute_update(update_query, {
            'amount': data.get('amount'),
            'exchange_rate': data.get('exchange_rate', 1.0),
            'balance_type': data.get('balance_type', 'DEBIT'),
            'notes': data.get('notes', ''),
            'reference_doc': data.get('reference_document', ''),
            'user_id': session.get('user_id', 1),
            'balance_id': balance_id
        })

        db_manager.commit()

        return jsonify({
            'success': True,
            'message': 'تم تحديث الرصيد الافتتاحي بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في تحديث الرصيد الافتتاحي: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في التحديث: {str(e)}'
        }), 500

@transfers_bp.route('/api/money-changers-opening-balances/delete/<int:balance_id>', methods=['DELETE'])
@login_required
def delete_money_changer_opening_balance(balance_id):
    """حذف رصيد افتتاحي للصراف/البنك"""
    try:
        db_manager = DatabaseManager()
        # DatabaseManager يتم تهيئته تلقائياً

        # التحقق من وجود الرصيد وحالته
        check_query = """
        SELECT status FROM OPENING_BALANCES
        WHERE id = :balance_id AND entity_type_code = 'MONEY_CHANGER'
        """

        existing = db_manager.execute_query(check_query, {'balance_id': balance_id})

        if not existing:
            return jsonify({
                'success': False,
                'message': 'الرصيد الافتتاحي غير موجود'
            }), 404

        current_status = existing[0][0]

        # التحقق من إمكانية الحذف
        if current_status in ['POSTED', 'APPROVED']:
            return jsonify({
                'success': False,
                'message': 'لا يمكن حذف رصيد معتمد أو مرحل'
            }), 400

        # حذف الرصيد (حذف منطقي)
        delete_query = """
        UPDATE OPENING_BALANCES SET
            is_active = 0,
            updated_date = CURRENT_TIMESTAMP,
            updated_by = :user_id
        WHERE id = :balance_id
        """

        db_manager.execute_update(delete_query, {
            'user_id': session.get('user_id', 1),
            'balance_id': balance_id
        })

        db_manager.commit()

        return jsonify({
            'success': True,
            'message': 'تم حذف الرصيد الافتتاحي بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في حذف الرصيد الافتتاحي: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في الحذف: {str(e)}'
        }), 500

@transfers_bp.route('/api/money-changers-opening-balances/approve', methods=['POST'])
@login_required
def approve_money_changers_opening_balances():
    """اعتماد الأرصدة الافتتاحية للصرافين/البنوك"""
    try:
        data = request.get_json()
        fiscal_date = data.get('fiscal_date')
        currency_code = data.get('currency_code', '')

        if not fiscal_date:
            return jsonify({
                'success': False,
                'message': 'تاريخ الفترة المحاسبية مطلوب'
            }), 400

        db_manager = DatabaseManager()
        # DatabaseManager يتم تهيئته تلقائياً

        # بناء شروط الاستعلام
        where_conditions = [
            "entity_type_code = 'MONEY_CHANGER'",
            "fiscal_period_start_date = TO_DATE(:fiscal_date, 'YYYY-MM-DD')",
            "status = 'DRAFT'",
            "is_active = 1"
        ]

        params = {'fiscal_date': fiscal_date}

        if currency_code:
            where_conditions.append("currency_code = :currency_code")
            params['currency_code'] = currency_code

        # اعتماد الأرصدة
        approve_query = f"""
        UPDATE OPENING_BALANCES SET
            status = 'APPROVED',
            approved_date = CURRENT_TIMESTAMP,
            approved_by = :user_id,
            updated_date = CURRENT_TIMESTAMP,
            updated_by = :user_id
        WHERE {' AND '.join(where_conditions)}
        """

        params['user_id'] = session.get('user_id', 1)

        result = db_manager.execute_update(approve_query, params)
        db_manager.commit()

        return jsonify({
            'success': True,
            'message': f'تم اعتماد {result} رصيد افتتاحي بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في اعتماد الأرصدة الافتتاحية: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في الاعتماد: {str(e)}'
        }), 500

@transfers_bp.route('/api/money-changers-opening-balances/currencies')
def get_currencies():
    """جلب قائمة العملات المدعومة"""
    try:
        # قائمة العملات الثابتة (يمكن تطويرها لاحقاً لجلبها من قاعدة البيانات)
        currencies = [
            {'code': 'SAR', 'name_ar': 'ريال سعودي', 'symbol': 'ر.س', 'is_base_currency': True, 'exchange_rate': 1.0},
            {'code': 'USD', 'name_ar': 'دولار أمريكي', 'symbol': '$', 'is_base_currency': False, 'exchange_rate': 3.75},
            {'code': 'EUR', 'name_ar': 'يورو', 'symbol': '€', 'is_base_currency': False, 'exchange_rate': 4.1},
            {'code': 'AED', 'name_ar': 'درهم إماراتي', 'symbol': 'د.إ', 'is_base_currency': False, 'exchange_rate': 1.02},
            {'code': 'YER', 'name_ar': 'ريال يمني', 'symbol': 'ر.ي', 'is_base_currency': False, 'exchange_rate': 0.015},
            {'code': 'EGP', 'name_ar': 'جنيه مصري', 'symbol': 'ج.م', 'is_base_currency': False, 'exchange_rate': 0.12},
            {'code': 'JOD', 'name_ar': 'دينار أردني', 'symbol': 'د.أ', 'is_base_currency': False, 'exchange_rate': 5.29},
            {'code': 'KWD', 'name_ar': 'دينار كويتي', 'symbol': 'د.ك', 'is_base_currency': False, 'exchange_rate': 12.25},
            {'code': 'QAR', 'name_ar': 'ريال قطري', 'symbol': 'ر.ق', 'is_base_currency': False, 'exchange_rate': 1.03},
            {'code': 'BHD', 'name_ar': 'دينار بحريني', 'symbol': 'د.ب', 'is_base_currency': False, 'exchange_rate': 9.95},
            {'code': 'OMR', 'name_ar': 'ريال عماني', 'symbol': 'ر.ع', 'is_base_currency': False, 'exchange_rate': 9.75}
        ]

        return jsonify({
            'success': True,
            'currencies': currencies
        })

    except Exception as e:
        logger.error(f"خطأ في جلب العملات: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب العملات: {str(e)}'
        }), 500

@transfers_bp.route('/api/money-changers-opening-balances/money-changers')
def get_money_changers():
    """جلب قائمة الصرافين/البنوك"""
    try:
        db_manager = DatabaseManager()

        query = """
        SELECT
            id,
            name,
            type,
            contact_person,
            phone,
            email,
            is_active
        FROM money_changers_banks
        WHERE is_active = 1
        ORDER BY name
        """

        result = db_manager.execute_query(query)

        money_changers = []
        if result:
            for row in result:
                money_changers.append({
                    'id': row[0],
                    'name': row[1],
                    'type': row[2],
                    'contact_person': row[3],
                    'phone': row[4],
                    'email': row[5],
                    'is_active': row[6]
                })

        return jsonify({
            'success': True,
            'money_changers': money_changers
        })

    except Exception as e:
        logger.error(f"خطأ في جلب الصرافين/البنوك: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب الصرافين/البنوك: {str(e)}'
        }), 500

@transfers_bp.route('/api/money-changers-opening-balances/opening_balances')
def get_opening_balances():
    """جلب الأرصدة الافتتاحية للصرافين/البنوك"""
    try:
        fiscal_date = request.args.get('fiscal_date', '2024-01-01')
        currency_code = request.args.get('currency_code', '')
        status_filter = request.args.get('status', 'all')
        money_changer_id = request.args.get('money_changer_id', '')

        db_manager = DatabaseManager()

        # بناء الاستعلام
        where_conditions = [
            "ob.entity_type_code = 'MONEY_CHANGER'",
            "ob.fiscal_period_start_date = TO_DATE(:fiscal_date, 'YYYY-MM-DD')",
            "ob.is_active = 1"
        ]

        params = {
            'fiscal_date': fiscal_date
        }

        if currency_code:
            where_conditions.append("ob.currency_code = :currency_code")
            params['currency_code'] = currency_code

        if status_filter != 'all':
            where_conditions.append("ob.status = :status")
            params['status'] = status_filter.upper()

        if money_changer_id:
            where_conditions.append("ob.entity_id = :money_changer_id")
            params['money_changer_id'] = money_changer_id

        query = f"""
        SELECT
            ob.id,
            ob.entity_id as money_changer_id,
            mcb.name as money_changer_name,
            mcb.type as money_changer_type,
            ob.currency_code,
            ob.opening_balance_amount,
            ob.balance_type,
            ob.exchange_rate,
            ob.status,
            ob.notes,
            ob.reference_document,
            ob.created_date,
            ob.updated_date,
            ob.created_by
        FROM OPENING_BALANCES ob
        LEFT JOIN money_changers_banks mcb ON ob.entity_id = mcb.id
        WHERE {' AND '.join(where_conditions)}
        ORDER BY mcb.name, ob.currency_code
        """

        result = db_manager.execute_query(query, params)

        opening_balances = []
        if result:
            for row in result:
                opening_balances.append({
                    'id': row[0],
                    'money_changer_id': row[1],
                    'money_changer_name': row[2] or 'غير محدد',
                    'money_changer_code': f'MC{row[1]:04d}' if row[1] else '',
                    'money_changer_type': row[3] or 'غير محدد',
                    'currency_code': row[4],
                    'opening_balance_amount': float(row[5]) if row[5] else 0,
                    'balance_type': row[6],
                    'exchange_rate': float(row[7]) if row[7] else 1,
                    'status': row[8],
                    'notes': row[9],
                    'reference_document': row[10],
                    'created_date': row[11].strftime('%Y-%m-%d %H:%M:%S') if row[11] else '',
                    'updated_date': row[12].strftime('%Y-%m-%d %H:%M:%S') if row[12] else '',
                    'created_by': row[13] or ''
                })

        return jsonify({
            'success': True,
            'opening_balances': opening_balances
        })

    except Exception as e:
        logger.error(f"خطأ في جلب الأرصدة الافتتاحية: {e}")
        # إرجاع بيانات وهمية في حالة عدم توفر قاعدة البيانات
        dummy_opening_balances = [
            {
                'id': 1,
                'money_changer_id': 1,
                'money_changer_name': 'البنك الأهلي السعودي - فرع الرياض',
                'money_changer_code': 'MC0001',
                'money_changer_type': 'bank',
                'currency_code': 'SAR',
                'opening_balance_amount': 50000.00,
                'balance_type': 'DEBIT',
                'exchange_rate': 1.0000,
                'status': 'DRAFT',
                'notes': 'رصيد افتتاحي تجريبي للبنك الأهلي السعودي',
                'reference_document': 'REF-001',
                'created_date': '2024-01-01 10:00:00',
                'updated_date': '',
                'created_by': 'admin'
            },
            {
                'id': 2,
                'money_changer_id': 2,
                'money_changer_name': 'صرافة الراجحي - فرع جدة',
                'money_changer_code': 'MC0002',
                'money_changer_type': 'money_changer',
                'currency_code': 'USD',
                'opening_balance_amount': 10000.00,
                'balance_type': 'CREDIT',
                'exchange_rate': 3.7500,
                'status': 'APPROVED',
                'notes': 'رصيد افتتاحي تجريبي لصرافة الراجحي بالدولار',
                'reference_document': 'REF-002',
                'created_date': '2024-01-01 11:00:00',
                'updated_date': '2024-01-02 09:00:00',
                'created_by': 'admin'
            },
            {
                'id': 3,
                'money_changer_id': 3,
                'money_changer_name': 'بنك الرياض - فرع الدمام',
                'money_changer_code': 'MC0003',
                'money_changer_type': 'bank',
                'currency_code': 'EUR',
                'opening_balance_amount': 5000.00,
                'balance_type': 'DEBIT',
                'exchange_rate': 4.1000,
                'status': 'POSTED',
                'notes': 'رصيد افتتاحي لبنك الرياض باليورو',
                'reference_document': 'REF-003',
                'created_date': '2024-01-01 12:00:00',
                'updated_date': '2024-01-03 10:00:00',
                'created_by': 'admin'
            },
            {
                'id': 4,
                'money_changer_id': 4,
                'money_changer_name': 'صرافة الأهلي للصرافة',
                'money_changer_code': 'MC0004',
                'money_changer_type': 'money_changer',
                'currency_code': 'SAR',
                'opening_balance_amount': 25000.00,
                'balance_type': 'CREDIT',
                'exchange_rate': 1.0000,
                'status': 'DRAFT',
                'notes': 'رصيد افتتاحي لصرافة الأهلي',
                'reference_document': 'REF-004',
                'created_date': '2024-01-01 13:00:00',
                'updated_date': '',
                'created_by': 'admin'
            }
        ]

        return jsonify({
            'success': True,
            'opening_balances': dummy_opening_balances
        })

@transfers_bp.route('/api/money-changers-opening-balances/summary')
def get_summary():
    """جلب ملخص الأرصدة الافتتاحية"""
    try:
        fiscal_date = request.args.get('fiscal_date', '2024-01-01')
        currency_code = request.args.get('currency_code', '')
        money_changer_id = request.args.get('money_changer_id', '')

        db_manager = DatabaseManager()

        # بناء شروط الاستعلام
        where_conditions = [
            "ob.entity_type_code = 'MONEY_CHANGER'",
            "ob.fiscal_period_start_date = TO_DATE(:fiscal_date, 'YYYY-MM-DD')",
            "ob.is_active = 1"
        ]

        params = {
            'fiscal_date': fiscal_date
        }

        if currency_code:
            where_conditions.append("ob.currency_code = :currency_code")
            params['currency_code'] = currency_code

        if money_changer_id:
            where_conditions.append("ob.entity_id = :money_changer_id")
            params['money_changer_id'] = money_changer_id

        # استعلام الملخص
        summary_query = f"""
        SELECT
            COUNT(DISTINCT ob.entity_id) as total_money_changers,
            SUM(CASE WHEN ob.balance_type = 'DEBIT' THEN ob.opening_balance_amount ELSE 0 END) as total_debit,
            SUM(CASE WHEN ob.balance_type = 'CREDIT' THEN ob.opening_balance_amount ELSE 0 END) as total_credit,
            SUM(CASE WHEN ob.balance_type = 'DEBIT' THEN ob.opening_balance_amount
                     WHEN ob.balance_type = 'CREDIT' THEN -ob.opening_balance_amount
                     ELSE 0 END) as net_balance
        FROM OPENING_BALANCES ob
        WHERE {' AND '.join(where_conditions)}
        """

        result = db_manager.execute_query(summary_query, params)

        summary = {
            'total_money_changers': 0,
            'total_debit': 0,
            'total_credit': 0,
            'net_balance': 0
        }

        if result and result[0]:
            row = result[0]
            summary = {
                'total_money_changers': int(row[0]) if row[0] else 0,
                'total_debit': float(row[1]) if row[1] else 0,
                'total_credit': float(row[2]) if row[2] else 0,
                'net_balance': float(row[3]) if row[3] else 0
            }

        return jsonify({
            'success': True,
            'summary': summary
        })

    except Exception as e:
        logger.error(f"خطأ في جلب الملخص: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب الملخص: {str(e)}'
        }), 500

# API endpoints للعمليات على الأرصدة الافتتاحية

@transfers_bp.route('/api/money-changers-opening-balances/<int:balance_id>/approve', methods=['POST'])
@login_required
def approve_money_changer_opening_balance(balance_id):
    """اعتماد رصيد افتتاحي للصراف/البنك"""
    try:
        db_manager = DatabaseManager()

        # التحقق من وجود الرصيد وحالته
        check_query = """
        SELECT status FROM OPENING_BALANCES
        WHERE id = :balance_id AND entity_type_code = 'MONEY_CHANGER'
        """

        existing = db_manager.execute_query(check_query, {'balance_id': balance_id})

        if not existing:
            return jsonify({
                'success': False,
                'message': 'الرصيد الافتتاحي غير موجود'
            }), 404

        if existing[0][0] != 'DRAFT':
            return jsonify({
                'success': False,
                'message': 'يمكن اعتماد الأرصدة في حالة المسودة فقط'
            }), 400

        # تحديث حالة الرصيد إلى معتمد
        update_query = """
        UPDATE OPENING_BALANCES
        SET status = 'APPROVED',
            approved_date = CURRENT_TIMESTAMP,
            approved_by = :user_id,
            updated_date = CURRENT_TIMESTAMP,
            updated_by = :user_id
        WHERE id = :balance_id
        """

        db_manager.execute_update(update_query, {
            'user_id': session.get('user_id', 1),
            'balance_id': balance_id
        })

        db_manager.commit()

        return jsonify({
            'success': True,
            'message': 'تم اعتماد الرصيد الافتتاحي بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في اعتماد الرصيد الافتتاحي: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في الاعتماد: {str(e)}'
        }), 500

@transfers_bp.route('/api/money-changers-opening-balances/<int:balance_id>/unapprove', methods=['POST'])
@login_required
def unapprove_money_changer_opening_balance(balance_id):
    """إلغاء اعتماد رصيد افتتاحي للصراف/البنك"""
    try:
        db_manager = DatabaseManager()

        # التحقق من وجود الرصيد وحالته
        check_query = """
        SELECT status FROM OPENING_BALANCES
        WHERE id = :balance_id AND entity_type_code = 'MONEY_CHANGER'
        """

        existing = db_manager.execute_query(check_query, {'balance_id': balance_id})

        if not existing:
            return jsonify({
                'success': False,
                'message': 'الرصيد الافتتاحي غير موجود'
            }), 404

        if existing[0][0] not in ['APPROVED']:
            return jsonify({
                'success': False,
                'message': 'يمكن إلغاء اعتماد الأرصدة المعتمدة فقط'
            }), 400

        # تحديث حالة الرصيد إلى مسودة
        update_query = """
        UPDATE OPENING_BALANCES
        SET status = 'DRAFT',
            approved_date = NULL,
            approved_by = NULL,
            updated_date = CURRENT_TIMESTAMP,
            updated_by = :user_id
        WHERE id = :balance_id
        """

        db_manager.execute_update(update_query, {
            'user_id': session.get('user_id', 1),
            'balance_id': balance_id
        })

        db_manager.commit()

        return jsonify({
            'success': True,
            'message': 'تم إلغاء اعتماد الرصيد الافتتاحي بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في إلغاء اعتماد الرصيد الافتتاحي: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في إلغاء الاعتماد: {str(e)}'
        }), 500

@transfers_bp.route('/api/money-changers-opening-balances/<int:balance_id>/post', methods=['POST'])
@login_required
def post_money_changer_opening_balance(balance_id):
    """ترحيل رصيد افتتاحي للصراف/البنك"""
    try:
        db_manager = DatabaseManager()

        # التحقق من وجود الرصيد وحالته
        check_query = """
        SELECT status FROM OPENING_BALANCES
        WHERE id = :balance_id AND entity_type_code = 'MONEY_CHANGER'
        """

        existing = db_manager.execute_query(check_query, {'balance_id': balance_id})

        if not existing:
            return jsonify({
                'success': False,
                'message': 'الرصيد الافتتاحي غير موجود'
            }), 404

        if existing[0][0] != 'APPROVED':
            return jsonify({
                'success': False,
                'message': 'يمكن ترحيل الأرصدة المعتمدة فقط'
            }), 400

        # تحديث حالة الرصيد إلى مرحل
        update_query = """
        UPDATE OPENING_BALANCES
        SET status = 'POSTED',
            posted_date = CURRENT_TIMESTAMP,
            posted_by = :user_id,
            updated_date = CURRENT_TIMESTAMP,
            updated_by = :user_id
        WHERE id = :balance_id
        """

        db_manager.execute_update(update_query, {
            'user_id': session.get('user_id', 1),
            'balance_id': balance_id
        })

        db_manager.commit()

        return jsonify({
            'success': True,
            'message': 'تم ترحيل الرصيد الافتتاحي بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في ترحيل الرصيد الافتتاحي: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في الترحيل: {str(e)}'
        }), 500

@transfers_bp.route('/api/money-changers-opening-balances/<int:balance_id>/unpost', methods=['POST'])
@login_required
def unpost_money_changer_opening_balance(balance_id):
    """إلغاء ترحيل رصيد افتتاحي للصراف/البنك"""
    try:
        db_manager = DatabaseManager()

        # التحقق من وجود الرصيد وحالته
        check_query = """
        SELECT status FROM OPENING_BALANCES
        WHERE id = :balance_id AND entity_type_code = 'MONEY_CHANGER'
        """

        existing = db_manager.execute_query(check_query, {'balance_id': balance_id})

        if not existing:
            return jsonify({
                'success': False,
                'message': 'الرصيد الافتتاحي غير موجود'
            }), 404

        if existing[0][0] != 'POSTED':
            return jsonify({
                'success': False,
                'message': 'يمكن إلغاء ترحيل الأرصدة المرحلة فقط'
            }), 400

        # تحديث حالة الرصيد إلى معتمد
        update_query = """
        UPDATE OPENING_BALANCES
        SET status = 'APPROVED',
            posted_date = NULL,
            posted_by = NULL,
            updated_date = CURRENT_TIMESTAMP,
            updated_by = :user_id
        WHERE id = :balance_id
        """

        db_manager.execute_update(update_query, {
            'user_id': session.get('user_id', 1),
            'balance_id': balance_id
        })

        db_manager.commit()

        return jsonify({
            'success': True,
            'message': 'تم إلغاء ترحيل الرصيد الافتتاحي بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في إلغاء ترحيل الرصيد الافتتاحي: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في إلغاء الترحيل: {str(e)}'
        }), 500

@transfers_bp.route('/api/money-changers-opening-balances/<int:balance_id>', methods=['GET'])
@login_required
def get_money_changer_opening_balance(balance_id):
    """الحصول على رصيد افتتاحي واحد للصراف/البنك"""
    try:
        db_manager = DatabaseManager()

        query = """
        SELECT
            ob.id,
            ob.entity_id as money_changer_id,
            mcb.name as money_changer_name,
            ob.currency_code,
            ob.opening_balance_amount,
            ob.exchange_rate,
            ob.balance_type,
            TO_CHAR(ob.fiscal_period_start_date, 'YYYY-MM-DD') as fiscal_period_start_date,
            ob.reference_document,
            ob.notes,
            ob.status,
            TO_CHAR(ob.created_date, 'YYYY-MM-DD HH24:MI:SS') as created_date
        FROM OPENING_BALANCES ob
        JOIN MONEY_CHANGERS_BANKS mcb ON ob.entity_id = mcb.id
        WHERE ob.id = :balance_id AND ob.entity_type_code = 'MONEY_CHANGER'
        """

        result = db_manager.execute_query(query, {'balance_id': balance_id})

        if not result:
            return jsonify({
                'success': False,
                'message': 'الرصيد الافتتاحي غير موجود'
            }), 404

        row = result[0]
        balance = {
            'id': row[0],
            'money_changer_id': row[1],
            'money_changer_name': row[2],
            'currency_code': row[3],
            'opening_balance_amount': float(row[4]) if row[4] else 0,
            'exchange_rate': float(row[5]) if row[5] else 1.0,
            'balance_type': row[6],
            'fiscal_period_start_date': row[7],
            'reference_document': row[8],
            'notes': row[9],
            'status': row[10],
            'created_date': row[11]
        }

        return jsonify({
            'success': True,
            'data': balance
        })

    except Exception as e:
        logger.error(f"خطأ في جلب الرصيد الافتتاحي: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب البيانات: {str(e)}'
        }), 500
