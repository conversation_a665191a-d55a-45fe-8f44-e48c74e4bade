#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
sys.path.insert(0, os.getcwd())

from oracle_manager import OracleManager

def main():
    oracle = OracleManager()
    if not oracle.connect():
        print("❌ فشل الاتصال بقاعدة البيانات")
        return
    
    try:
        print("🗑️ تطبيق الحذف الفعلي في procedure DELETE_BAL...")
        
        # إنشاء package body محدث مع الحذف الفعلي
        package_body = """
        CREATE OR REPLACE PACKAGE BODY OB_PKG AS
            
            PROCEDURE INSERT_BAL(
                p_ent_type VARCHAR2,
                p_ent_id NUMBER,
                p_curr VARCHAR2,
                p_amount NUMBER,
                p_branch NUMBER,
                p_user NUMBER
            ) AS
                v_doc_number VARCHAR2(100);
            BEGIN
                -- إنشاء رقم المستند
                v_doc_number := 'OB-' || p_ent_type || '-' || p_ent_id || '-' || EXTRACT(YEAR FROM SYSDATE);
                
                -- إدراج في OPENING_BALANCES
                INSERT INTO OPENING_BALANCES (
                    entity_type_code,
                    entity_id,
                    currency_code,
                    opening_balance_amount,
                    base_currency_amount,
                    branch_id,
                    fiscal_year,
                    fiscal_period_start_date,
                    document_number,
                    document_type_code,
                    created_by,
                    created_date,
                    is_active,
                    status
                ) VALUES (
                    p_ent_type,
                    p_ent_id,
                    p_curr,
                    p_amount,
                    p_amount,
                    p_branch,
                    EXTRACT(YEAR FROM SYSDATE),
                    TRUNC(SYSDATE, 'YEAR'),
                    v_doc_number,
                    'OPENING_BALANCE',
                    p_user,
                    SYSTIMESTAMP,
                    1,
                    'ACTIVE'
                );
                
                -- إدراج في BALANCE_TRANSACTIONS
                INSERT INTO BALANCE_TRANSACTIONS (
                    entity_type_code,
                    entity_id,
                    document_type_code,
                    document_number,
                    document_date,
                    currency_code,
                    debit_amount,
                    credit_amount,
                    base_currency_debit,
                    base_currency_credit,
                    bal,
                    bal_f,
                    description,
                    status,
                    month_no,
                    year_no,
                    branch_id,
                    created_by,
                    created_date
                ) VALUES (
                    p_ent_type,
                    p_ent_id,
                    'OPENING_BALANCE',
                    v_doc_number,
                    SYSDATE,
                    p_curr,
                    CASE WHEN p_amount > 0 THEN p_amount ELSE 0 END,
                    CASE WHEN p_amount < 0 THEN ABS(p_amount) ELSE 0 END,
                    CASE WHEN p_amount > 0 THEN p_amount ELSE 0 END,
                    CASE WHEN p_amount < 0 THEN ABS(p_amount) ELSE 0 END,
                    p_amount,
                    p_amount,
                    'رصيد افتتاحي - ' || p_ent_type || ' رقم ' || p_ent_id,
                    'ACTIVE',
                    EXTRACT(MONTH FROM SYSDATE),
                    EXTRACT(YEAR FROM SYSDATE),
                    p_branch,
                    p_user,
                    SYSTIMESTAMP
                );
                
                COMMIT;
            END INSERT_BAL;
            
            PROCEDURE UPDATE_BAL(
                p_id NUMBER,
                p_ent_type VARCHAR2,
                p_ent_id NUMBER,
                p_curr VARCHAR2,
                p_amount NUMBER,
                p_branch NUMBER,
                p_user NUMBER
            ) AS
                v_doc_number VARCHAR2(100);
                v_old_doc_number VARCHAR2(100);
            BEGIN
                -- جلب رقم المستند الحالي (مع معالجة NULL)
                BEGIN
                    SELECT NVL(document_number, 'OB-' || entity_type_code || '-' || entity_id || '-' || EXTRACT(YEAR FROM SYSDATE))
                    INTO v_old_doc_number
                    FROM OPENING_BALANCES 
                    WHERE id = p_id;
                EXCEPTION
                    WHEN NO_DATA_FOUND THEN
                        v_old_doc_number := 'OB-' || p_ent_type || '-' || p_ent_id || '-' || EXTRACT(YEAR FROM SYSDATE);
                END;
                
                -- إنشاء رقم مستند جديد
                v_doc_number := 'OB-' || p_ent_type || '-' || p_ent_id || '-' || EXTRACT(YEAR FROM SYSDATE);
                
                -- تحديث OPENING_BALANCES
                UPDATE OPENING_BALANCES SET
                    entity_type_code = p_ent_type,
                    entity_id = p_ent_id,
                    currency_code = p_curr,
                    opening_balance_amount = p_amount,
                    base_currency_amount = p_amount,
                    branch_id = p_branch,
                    document_number = v_doc_number,
                    updated_by = p_user,
                    updated_date = SYSTIMESTAMP
                WHERE id = p_id;
                
                -- تحديث BALANCE_TRANSACTIONS
                UPDATE BALANCE_TRANSACTIONS SET
                    entity_type_code = p_ent_type,
                    entity_id = p_ent_id,
                    currency_code = p_curr,
                    debit_amount = CASE WHEN p_amount > 0 THEN p_amount ELSE 0 END,
                    credit_amount = CASE WHEN p_amount < 0 THEN ABS(p_amount) ELSE 0 END,
                    base_currency_debit = CASE WHEN p_amount > 0 THEN p_amount ELSE 0 END,
                    base_currency_credit = CASE WHEN p_amount < 0 THEN ABS(p_amount) ELSE 0 END,
                    bal = p_amount,
                    bal_f = p_amount,
                    document_number = v_doc_number,
                    branch_id = p_branch,
                    updated_by = p_user,
                    updated_date = SYSTIMESTAMP,
                    description = 'رصيد افتتاحي محدث - ' || p_ent_type || ' رقم ' || p_ent_id
                WHERE (document_number = v_old_doc_number OR entity_id = p_ent_id)
                AND entity_type_code = p_ent_type;
                
                -- إذا لم يتم العثور على سجل في BALANCE_TRANSACTIONS، أنشئ واحد جديد
                IF SQL%ROWCOUNT = 0 THEN
                    INSERT INTO BALANCE_TRANSACTIONS (
                        entity_type_code,
                        entity_id,
                        document_type_code,
                        document_number,
                        document_date,
                        currency_code,
                        debit_amount,
                        credit_amount,
                        base_currency_debit,
                        base_currency_credit,
                        bal,
                        bal_f,
                        description,
                        status,
                        month_no,
                        year_no,
                        branch_id,
                        created_by,
                        created_date
                    ) VALUES (
                        p_ent_type,
                        p_ent_id,
                        'OPENING_BALANCE',
                        v_doc_number,
                        SYSDATE,
                        p_curr,
                        CASE WHEN p_amount > 0 THEN p_amount ELSE 0 END,
                        CASE WHEN p_amount < 0 THEN ABS(p_amount) ELSE 0 END,
                        CASE WHEN p_amount > 0 THEN p_amount ELSE 0 END,
                        CASE WHEN p_amount < 0 THEN ABS(p_amount) ELSE 0 END,
                        p_amount,
                        p_amount,
                        'رصيد افتتاحي محدث - ' || p_ent_type || ' رقم ' || p_ent_id,
                        'ACTIVE',
                        EXTRACT(MONTH FROM SYSDATE),
                        EXTRACT(YEAR FROM SYSDATE),
                        p_branch,
                        p_user,
                        SYSTIMESTAMP
                    );
                END IF;
                
                COMMIT;
            END UPDATE_BAL;
            
            PROCEDURE DELETE_BAL(
                p_id NUMBER,
                p_ent_type VARCHAR2,
                p_ent_id NUMBER,
                p_curr VARCHAR2,
                p_branch NUMBER,
                p_user NUMBER
            ) AS
            BEGIN
                -- حذف فعلي من BALANCE_TRANSACTIONS أولاً (بسبب Foreign Key)
                DELETE FROM BALANCE_TRANSACTIONS 
                WHERE entity_type_code = p_ent_type
                AND entity_id = p_ent_id;
                
                -- حذف فعلي من OPENING_BALANCES
                DELETE FROM OPENING_BALANCES 
                WHERE id = p_id;
                
                COMMIT;
            END DELETE_BAL;
            
        END OB_PKG;
        """
        
        oracle.execute_update(package_body)
        print("✅ تم تحديث package body مع الحذف الفعلي")
        
        # اختبار الـ procedure المحدث
        print("\n🧪 اختبار DELETE_BAL مع الحذف الفعلي:")
        
        # تنظيف البيانات الاختبارية
        oracle.execute_update("DELETE FROM OPENING_BALANCES WHERE entity_id = 977")
        oracle.execute_update("DELETE FROM BALANCE_TRANSACTIONS WHERE entity_id = 977")
        
        # إنشاء سجل اختبار
        insert_call = """
        BEGIN
            OB_PKG.INSERT_BAL(
                p_ent_type => 'CUSTOMER',
                p_ent_id => 977,
                p_curr => 'GBP',
                p_amount => 6500.00,
                p_branch => 21,
                p_user => 1
            );
        END;
        """
        
        oracle.execute_update(insert_call)
        print("✅ تم إنشاء سجل اختبار")
        
        # التحقق من وجود السجل قبل الحذف
        ob_before = oracle.execute_query("SELECT COUNT(*) FROM OPENING_BALANCES WHERE entity_id = 977")
        bt_before = oracle.execute_query("SELECT COUNT(*) FROM BALANCE_TRANSACTIONS WHERE entity_id = 977")
        
        print(f"📊 قبل الحذف:")
        print(f"   OPENING_BALANCES: {ob_before[0][0]} سجل")
        print(f"   BALANCE_TRANSACTIONS: {bt_before[0][0]} سجل")
        
        # جلب معرف السجل
        record_id = oracle.execute_query("SELECT id FROM OPENING_BALANCES WHERE entity_id = 977")[0][0]
        print(f"📋 معرف السجل: {record_id}")
        
        # اختبار الحذف الفعلي
        delete_call = f"""
        BEGIN
            OB_PKG.DELETE_BAL(
                p_id => {record_id},
                p_ent_type => 'CUSTOMER',
                p_ent_id => 977,
                p_curr => 'GBP',
                p_branch => 21,
                p_user => 1
            );
        END;
        """
        
        oracle.execute_update(delete_call)
        print("✅ تم تنفيذ الحذف الفعلي")
        
        # التحقق من النتيجة
        ob_after = oracle.execute_query("SELECT COUNT(*) FROM OPENING_BALANCES WHERE entity_id = 977")
        bt_after = oracle.execute_query("SELECT COUNT(*) FROM BALANCE_TRANSACTIONS WHERE entity_id = 977")
        
        print(f"📊 بعد الحذف:")
        print(f"   OPENING_BALANCES: {ob_after[0][0]} سجل")
        print(f"   BALANCE_TRANSACTIONS: {bt_after[0][0]} سجل")
        
        if ob_after[0][0] == 0 and bt_after[0][0] == 0:
            print("✅ تم الحذف الفعلي بنجاح من كلا الجدولين!")
        else:
            print("❌ لم يتم الحذف بشكل كامل")
        
        print("\n🎉 تم تطبيق الحذف الفعلي بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
    finally:
        oracle.disconnect()

if __name__ == "__main__":
    main()
