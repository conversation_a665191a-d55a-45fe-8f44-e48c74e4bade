#!/usr/bin/env python3
"""
تحديث جدول وثائق أوامر الشراء ليطابق جدول وثائق العقود
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oracle_manager import get_oracle_manager

def update_po_documents_table_complete():
    """تحديث جدول وثائق أوامر الشراء بالكامل"""
    
    oracle_manager = get_oracle_manager()
    
    try:
        print("🔄 تحديث جدول وثائق أوامر الشراء ليطابق جدول وثائق العقود")
        print("=" * 80)
        
        # 1. فحص الجدول الحالي
        print("\n1️⃣ فحص الجدول الحالي...")
        
        current_structure_query = """
            SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE
            FROM USER_TAB_COLUMNS
            WHERE TABLE_NAME = 'PO_DOCUMENTS'
            ORDER BY COLUMN_ID
        """
        
        current_result = oracle_manager.execute_query(current_structure_query, [])
        current_columns = [col[0] for col in current_result] if current_result else []
        
        print(f"📋 الأعمدة الحالية: {len(current_columns)}")
        for col in current_columns:
            print(f"   - {col}")
        
        # 2. قائمة الأعمدة المطلوبة (مطابقة لجدول العقود)
        print("\n2️⃣ الأعمدة المطلوبة...")
        
        required_columns = {
            'ID': 'NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY',
            'PO_ID': 'NUMBER NOT NULL',
            'TITLE': 'VARCHAR2(255) NOT NULL',
            'DOCUMENT_TYPE': 'VARCHAR2(50) DEFAULT \'other\'',
            'FILENAME': 'VARCHAR2(500) NOT NULL',
            'ORIGINAL_FILENAME': 'VARCHAR2(255)',
            'FILE_SIZE': 'NUMBER DEFAULT 0',
            'FILE_PATH': 'VARCHAR2(1000)',
            'DESCRIPTION': 'CLOB',
            'CREATED_BY': 'VARCHAR2(100)',
            'CREATED_AT': 'DATE DEFAULT SYSDATE',
            'UPDATED_BY': 'VARCHAR2(100)',
            'UPDATED_AT': 'DATE',
            'IS_ACTIVE': 'NUMBER(1) DEFAULT 1',
            'NEXTCLOUD_SHARE_LINK': 'VARCHAR2(1000)',
            'ONEDRIVE_SHARE_LINK': 'VARCHAR2(1000)',
            'URL': 'VARCHAR2(1000)',
            'DOCUMENT_CATEGORY': 'VARCHAR2(50)',
            'VERSION_NUMBER': 'NUMBER DEFAULT 1',
            'PARENT_DOCUMENT_ID': 'NUMBER',
            'APPROVAL_STATUS': 'VARCHAR2(20) DEFAULT \'PENDING\'',
            'APPROVED_BY': 'VARCHAR2(100)',
            'APPROVED_AT': 'DATE',
            'EXPIRY_DATE': 'DATE',
            'TAGS': 'VARCHAR2(500)',
            'ACCESS_LEVEL': 'VARCHAR2(20) DEFAULT \'PUBLIC\'',
            'DOWNLOAD_COUNT': 'NUMBER DEFAULT 0',
            'LAST_ACCESSED': 'DATE',
            'CHECKSUM': 'VARCHAR2(64)',
            'MIME_TYPE': 'VARCHAR2(100)',
            'THUMBNAIL_PATH': 'VARCHAR2(500)',
            'IS_ENCRYPTED': 'NUMBER(1) DEFAULT 0',
            'ENCRYPTION_KEY': 'VARCHAR2(256)'
        }
        
        print(f"📋 الأعمدة المطلوبة: {len(required_columns)}")
        
        # 3. إنشاء جدول جديد مؤقت
        print("\n3️⃣ إنشاء جدول جديد مؤقت...")
        
        # نسخ البيانات الموجودة أولاً
        backup_data = []
        if current_columns:
            try:
                backup_query = "SELECT * FROM PO_DOCUMENTS"
                backup_data = oracle_manager.execute_query(backup_query, [])
                print(f"📦 تم نسخ {len(backup_data)} سجل")
            except Exception as backup_error:
                print(f"⚠️ خطأ في نسخ البيانات: {backup_error}")
        
        # حذف الجدول القديم
        try:
            drop_query = "DROP TABLE PO_DOCUMENTS CASCADE CONSTRAINTS"
            oracle_manager.execute_query(drop_query, [])
            print("🗑️ تم حذف الجدول القديم")
        except Exception as drop_error:
            print(f"⚠️ خطأ في حذف الجدول القديم: {drop_error}")
        
        # إنشاء الجدول الجديد
        create_table_query = """
            CREATE TABLE PO_DOCUMENTS (
                ID NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                PO_ID NUMBER NOT NULL,
                TITLE VARCHAR2(255) NOT NULL,
                DOCUMENT_TYPE VARCHAR2(50) DEFAULT 'other',
                FILENAME VARCHAR2(500) NOT NULL,
                ORIGINAL_FILENAME VARCHAR2(255),
                FILE_SIZE NUMBER DEFAULT 0,
                FILE_PATH VARCHAR2(1000),
                DESCRIPTION CLOB,
                CREATED_BY VARCHAR2(100),
                CREATED_AT DATE DEFAULT SYSDATE,
                UPDATED_BY VARCHAR2(100),
                UPDATED_AT DATE,
                IS_ACTIVE NUMBER(1) DEFAULT 1,
                NEXTCLOUD_SHARE_LINK VARCHAR2(1000),
                ONEDRIVE_SHARE_LINK VARCHAR2(1000),
                URL VARCHAR2(1000),
                DOCUMENT_CATEGORY VARCHAR2(50),
                VERSION_NUMBER NUMBER DEFAULT 1,
                PARENT_DOCUMENT_ID NUMBER,
                APPROVAL_STATUS VARCHAR2(20) DEFAULT 'PENDING',
                APPROVED_BY VARCHAR2(100),
                APPROVED_AT DATE,
                EXPIRY_DATE DATE,
                TAGS VARCHAR2(500),
                ACCESS_LEVEL VARCHAR2(20) DEFAULT 'PUBLIC',
                DOWNLOAD_COUNT NUMBER DEFAULT 0,
                LAST_ACCESSED DATE,
                CHECKSUM VARCHAR2(64),
                MIME_TYPE VARCHAR2(100),
                THUMBNAIL_PATH VARCHAR2(500),
                IS_ENCRYPTED NUMBER(1) DEFAULT 0,
                ENCRYPTION_KEY VARCHAR2(256),
                
                -- قيود المرجعية
                CONSTRAINT FK_PO_DOCUMENTS_PO 
                    FOREIGN KEY (PO_ID) 
                    REFERENCES PURCHASE_ORDERS(ID) 
                    ON DELETE CASCADE,
                
                CONSTRAINT FK_PO_DOCUMENTS_PARENT
                    FOREIGN KEY (PARENT_DOCUMENT_ID)
                    REFERENCES PO_DOCUMENTS(ID),
                
                -- قيود التحقق
                CONSTRAINT CHK_PO_DOCUMENTS_TYPE 
                    CHECK (DOCUMENT_TYPE IN ('purchase_order', 'amendment', 'invoice', 'receipt', 'certificate', 'specification', 'quality_report', 'shipping_document', 'customs_document', 'insurance', 'correspondence', 'attachment', 'link', 'other')),
                
                CONSTRAINT CHK_PO_DOCUMENTS_ACTIVE 
                    CHECK (IS_ACTIVE IN (0, 1)),
                
                CONSTRAINT CHK_PO_DOCUMENTS_APPROVAL
                    CHECK (APPROVAL_STATUS IN ('PENDING', 'APPROVED', 'REJECTED')),
                
                CONSTRAINT CHK_PO_DOCUMENTS_ACCESS
                    CHECK (ACCESS_LEVEL IN ('PUBLIC', 'PRIVATE', 'RESTRICTED')),
                
                CONSTRAINT CHK_PO_DOCUMENTS_ENCRYPTED
                    CHECK (IS_ENCRYPTED IN (0, 1))
            )
        """
        
        oracle_manager.execute_query(create_table_query, [])
        print("✅ تم إنشاء الجدول الجديد")
        
        # 4. إنشاء الفهارس
        print("\n4️⃣ إنشاء الفهارس...")
        
        indexes = [
            ("IDX_PO_DOCS_PO_ID", "PO_ID"),
            ("IDX_PO_DOCS_TYPE", "DOCUMENT_TYPE"),
            ("IDX_PO_DOCS_CREATED_AT", "CREATED_AT"),
            ("IDX_PO_DOCS_ACTIVE", "IS_ACTIVE"),
            ("IDX_PO_DOCS_CATEGORY", "DOCUMENT_CATEGORY"),
            ("IDX_PO_DOCS_APPROVAL", "APPROVAL_STATUS"),
            ("IDX_PO_DOCS_ACCESS", "ACCESS_LEVEL"),
            ("IDX_PO_DOCS_TITLE", "TITLE"),
            ("IDX_PO_DOCS_FILENAME", "FILENAME")
        ]
        
        for index_name, column in indexes:
            try:
                index_query = f"CREATE INDEX {index_name} ON PO_DOCUMENTS ({column})"
                oracle_manager.execute_query(index_query, [])
                print(f"✅ فهرس {index_name}")
            except Exception as index_error:
                print(f"⚠️ خطأ في فهرس {index_name}: {index_error}")
        
        # 5. إنشاء trigger للتحديث التلقائي
        print("\n5️⃣ إنشاء trigger للتحديث التلقائي...")
        
        trigger_query = """
            CREATE OR REPLACE TRIGGER TRG_PO_DOCUMENTS_UPDATED_AT
                BEFORE UPDATE ON PO_DOCUMENTS
                FOR EACH ROW
            BEGIN
                :NEW.UPDATED_AT := SYSDATE;
            END;
        """
        
        try:
            oracle_manager.execute_query(trigger_query, [])
            print("✅ تم إنشاء trigger التحديث التلقائي")
        except Exception as trigger_error:
            print(f"⚠️ خطأ في إنشاء trigger: {trigger_error}")
        
        # 6. إضافة التعليقات
        print("\n6️⃣ إضافة التعليقات...")
        
        comments = [
            ("TABLE", "PO_DOCUMENTS", "جدول وثائق أوامر الشراء - يحتوي على جميع المستندات المرفقة بأوامر الشراء"),
            ("COLUMN", "PO_DOCUMENTS.ID", "معرف الوثيقة الفريد"),
            ("COLUMN", "PO_DOCUMENTS.PO_ID", "معرف أمر الشراء المرتبط"),
            ("COLUMN", "PO_DOCUMENTS.TITLE", "عنوان الوثيقة"),
            ("COLUMN", "PO_DOCUMENTS.DOCUMENT_TYPE", "نوع الوثيقة"),
            ("COLUMN", "PO_DOCUMENTS.FILENAME", "اسم الملف المحفوظ في النظام"),
            ("COLUMN", "PO_DOCUMENTS.ORIGINAL_FILENAME", "اسم الملف الأصلي"),
            ("COLUMN", "PO_DOCUMENTS.FILE_SIZE", "حجم الملف بالبايت"),
            ("COLUMN", "PO_DOCUMENTS.FILE_PATH", "مسار الملف في النظام"),
            ("COLUMN", "PO_DOCUMENTS.DESCRIPTION", "وصف الوثيقة"),
            ("COLUMN", "PO_DOCUMENTS.IS_ACTIVE", "حالة الوثيقة (1=نشط، 0=غير نشط)"),
            ("COLUMN", "PO_DOCUMENTS.NEXTCLOUD_SHARE_LINK", "رابط مشاركة Nextcloud"),
            ("COLUMN", "PO_DOCUMENTS.ONEDRIVE_SHARE_LINK", "رابط مشاركة OneDrive"),
            ("COLUMN", "PO_DOCUMENTS.APPROVAL_STATUS", "حالة الموافقة على الوثيقة"),
            ("COLUMN", "PO_DOCUMENTS.ACCESS_LEVEL", "مستوى الوصول للوثيقة")
        ]
        
        for comment_type, object_name, comment_text in comments:
            try:
                comment_query = f"COMMENT ON {comment_type} {object_name} IS '{comment_text}'"
                oracle_manager.execute_query(comment_query, [])
            except Exception as comment_error:
                print(f"⚠️ خطأ في تعليق {object_name}: {comment_error}")
        
        print("✅ تم إضافة التعليقات")
        
        # 7. استعادة البيانات إذا كانت موجودة
        if backup_data:
            print(f"\n7️⃣ استعادة البيانات ({len(backup_data)} سجل)...")
            
            for row in backup_data:
                try:
                    # تحويل البيانات القديمة للهيكل الجديد
                    insert_query = """
                        INSERT INTO PO_DOCUMENTS (
                            PO_ID, TITLE, DOCUMENT_TYPE, FILENAME, ORIGINAL_FILENAME,
                            FILE_SIZE, FILE_PATH, DESCRIPTION, CREATED_BY, CREATED_AT
                        ) VALUES (
                            :1, :2, :3, :4, :5, :6, :7, :8, :9, :10
                        )
                    """
                    
                    # تحويل البيانات القديمة
                    values = [
                        row[1],  # PO_ID
                        row[2] or row[3],  # TITLE (DOCUMENT_NAME or FILE_NAME)
                        row[1] if len(row) > 1 else 'other',  # DOCUMENT_TYPE
                        row[3],  # FILENAME
                        row[3],  # ORIGINAL_FILENAME
                        row[4] if len(row) > 4 else 0,  # FILE_SIZE
                        row[5] if len(row) > 5 else None,  # FILE_PATH
                        row[6] if len(row) > 6 else None,  # DESCRIPTION (NOTES)
                        row[8] if len(row) > 8 else 'system',  # CREATED_BY (UPLOADED_BY)
                        row[7] if len(row) > 7 else None   # CREATED_AT (UPLOADED_AT)
                    ]
                    
                    oracle_manager.execute_query(insert_query, values)
                    
                except Exception as restore_error:
                    print(f"⚠️ خطأ في استعادة سجل: {restore_error}")
            
            print("✅ تم استعادة البيانات")
        
        # 8. فحص الهيكل النهائي
        print("\n8️⃣ فحص الهيكل النهائي...")
        
        final_structure_query = """
            SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE
            FROM USER_TAB_COLUMNS
            WHERE TABLE_NAME = 'PO_DOCUMENTS'
            ORDER BY COLUMN_ID
        """
        
        final_result = oracle_manager.execute_query(final_structure_query, [])
        
        print(f"\n📋 هيكل الجدول النهائي ({len(final_result)} عمود):")
        print(f"{'العمود':<25} {'النوع':<15} {'الطول':<10} {'يقبل NULL'}")
        print("-" * 65)
        
        for col in final_result:
            column_name = col[0]
            data_type = col[1]
            data_length = col[2] if col[2] else ''
            nullable = 'نعم' if col[3] == 'Y' else 'لا'
            
            print(f"{column_name:<25} {data_type:<15} {str(data_length):<10} {nullable}")
        
        print("\n🎉 تم تحديث جدول وثائق أوامر الشراء بنجاح!")
        print("\n📋 الخلاصة:")
        print("   ✅ جدول PO_DOCUMENTS محدث بالكامل")
        print("   ✅ يطابق الآن جدول وثائق العقود")
        print("   ✅ أعمدة إضافية للمميزات المتقدمة")
        print("   ✅ فهارس محسنة للأداء")
        print("   ✅ قيود تحقق شاملة")
        print("   ✅ trigger للتحديث التلقائي")
        print("   ✅ تعليقات مفصلة")
        print("   ✅ النظام جاهز للاستخدام المتقدم")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث جدول وثائق أوامر الشراء: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء تحديث جدول وثائق أوامر الشراء...")
    success = update_po_documents_table_complete()
    
    if success:
        print("\n🌟 تم تحديث الجدول بنجاح!")
        print("الآن جدول وثائق أوامر الشراء مطابق تماماً لجدول وثائق العقود")
    else:
        print("\n❌ فشل في تحديث الجدول")
        sys.exit(1)
