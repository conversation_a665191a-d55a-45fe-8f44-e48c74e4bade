<!-- نافذة البحث الذكية للموانئ -->
<div class="modal fade" id="smartPortSelector" tabindex="-1" role="dialog" aria-labelledby="smartPortSelectorLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="smartPortSelectorLabel">
                    <i class="fas fa-search me-2"></i>
                    البحث الذكي في موانئ العالم
                    <span class="badge bg-light text-primary ms-2" id="portCount">جاري التحميل...</span>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            
            <div class="modal-body p-0">
                <!-- شريط البحث المتقدم -->
                <div class="search-header bg-light p-3 border-bottom">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search text-primary"></i>
                                </span>
                                <input type="text" class="form-control form-control-lg" id="portSearchInput" 
                                       placeholder="ابحث بالاسم، الكود، البلد، أو المدينة..." autocomplete="off">
                                <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="row">
                                <div class="col-md-4">
                                    <select class="form-select" id="continentFilter">
                                        <option value="">جميع القارات</option>
                                        <option value="Asia">آسيا</option>
                                        <option value="Europe">أوروبا</option>
                                        <option value="North America">أمريكا الشمالية</option>
                                        <option value="South America">أمريكا الجنوبية</option>
                                        <option value="Africa">أفريقيا</option>
                                        <option value="Oceania">أوقيانوسيا</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <select class="form-select" id="portTypeFilter">
                                        <option value="">جميع الأنواع</option>
                                        <option value="1">موانئ رئيسية</option>
                                        <option value="0">موانئ ثانوية</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <button class="btn btn-primary w-100" onclick="searchPorts()">
                                        <i class="fas fa-filter me-1"></i>
                                        تصفية
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- فلاتر سريعة -->
                    <div class="mt-3">
                        <div class="d-flex flex-wrap gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="quickFilter('Middle East')">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                الشرق الأوسط
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="quickFilter('East Asia')">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                شرق آسيا
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="quickFilter('Western Europe')">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                أوروبا الغربية
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="quickFilter('major')">
                                <i class="fas fa-star me-1"></i>
                                الموانئ الرئيسية
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="showFavoritePorts()">
                                <i class="fas fa-heart me-1"></i>
                                المفضلة
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- نتائج البحث -->
                <div class="search-results" style="height: 500px; overflow-y: auto;">
                    <div id="loadingSpinner" class="text-center p-5" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري البحث...</span>
                        </div>
                        <div class="mt-2">جاري البحث في موانئ العالم...</div>
                    </div>
                    
                    <div id="noResults" class="text-center p-5" style="display: none;">
                        <i class="fas fa-search text-muted mb-3" style="font-size: 3rem;"></i>
                        <h5 class="text-muted">لا توجد نتائج</h5>
                        <p class="text-muted">جرب البحث بكلمات مختلفة أو قم بتوسيع معايير البحث</p>
                    </div>
                    
                    <div id="searchResults" class="p-3">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </div>
                </div>
            </div>
            
            <div class="modal-footer bg-light">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <div>
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            اختر ميناء لإضافته إلى قائمة المفضلة
                        </small>
                    </div>
                    <div>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="closePortSelector()">إغلاق</button>
                        <button type="button" class="btn btn-primary" onclick="addCustomPort()">
                            <i class="fas fa-plus me-2"></i>
                            إضافة ميناء مخصص
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة ميناء مخصص -->
<div class="modal fade" id="customPortModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة ميناء مخصص</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="customPortForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">كود الميناء *</label>
                            <input type="text" class="form-control" name="port_code" maxlength="10" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم الميناء *</label>
                            <input type="text" class="form-control" name="port_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">البلد *</label>
                            <input type="text" class="form-control" name="country" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">المدينة *</label>
                            <input type="text" class="form-control" name="city" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">خط العرض</label>
                            <input type="number" class="form-control" name="latitude" step="0.000001">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">خط الطول</label>
                            <input type="number" class="form-control" name="longitude" step="0.000001">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.port-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.port-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
    transform: translateY(-1px);
}

.port-card.selected {
    border-color: #007bff;
    background: #f8f9ff;
}

.port-code {
    background: #007bff;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.port-major {
    background: #ffc107;
    color: #000;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.7rem;
    font-weight: bold;
}

.port-location {
    color: #6c757d;
    font-size: 0.9rem;
}

.port-details {
    font-size: 0.8rem;
    color: #6c757d;
}

.search-highlight {
    background: #fff3cd;
    padding: 0.1rem 0.2rem;
    border-radius: 2px;
}

.quick-filter-active {
    background: #007bff !important;
    color: white !important;
}
</style>

<script>
let currentPortType = ''; // 'origin' أو 'destination'
let searchTimeout;
let allPorts = [];
let filteredPorts = [];

// بيانات تجريبية كحل احتياطي
const fallbackPorts = [
    {
        id: 1,
        port_code: 'SARIG',
        port_name: 'King Abdulaziz Port',
        port_name_arabic: 'ميناء الملك عبدالعزيز',
        country: 'Saudi Arabia',
        country_arabic: 'السعودية',
        city: 'Dammam',
        city_arabic: 'الدمام',
        region: 'Middle East',
        continent: 'Asia',
        latitude: 26.3927,
        longitude: 50.1059,
        major_port: true,
        cargo_types: 'حاويات,بضائع عامة,بتروكيماويات'
    },
    {
        id: 2,
        port_code: 'SAJED',
        port_name: 'King Abdullah Port',
        port_name_arabic: 'ميناء الملك عبدالله',
        country: 'Saudi Arabia',
        country_arabic: 'السعودية',
        city: 'Rabigh',
        city_arabic: 'رابغ',
        region: 'Middle East',
        continent: 'Asia',
        latitude: 22.7206,
        longitude: 39.0142,
        major_port: true,
        cargo_types: 'حاويات,بضائع عامة'
    },
    {
        id: 3,
        port_code: 'AEJEA',
        port_name: 'Jebel Ali Port',
        port_name_arabic: 'ميناء جبل علي',
        country: 'UAE',
        country_arabic: 'الإمارات',
        city: 'Dubai',
        city_arabic: 'دبي',
        region: 'Middle East',
        continent: 'Asia',
        latitude: 25.0657,
        longitude: 55.1713,
        major_port: true,
        cargo_types: 'حاويات,بضائع عامة,ترانزيت'
    },
    {
        id: 4,
        port_code: 'CNSHA',
        port_name: 'Port of Shanghai',
        port_name_arabic: 'ميناء شنغهاي',
        country: 'China',
        country_arabic: 'الصين',
        city: 'Shanghai',
        city_arabic: 'شنغهاي',
        region: 'East Asia',
        continent: 'Asia',
        latitude: 31.2304,
        longitude: 121.4737,
        major_port: true,
        cargo_types: 'حاويات,بضائع عامة,بضائع سائبة'
    },
    {
        id: 5,
        port_code: 'NLRTM',
        port_name: 'Port of Rotterdam',
        port_name_arabic: 'ميناء روتردام',
        country: 'Netherlands',
        country_arabic: 'هولندا',
        city: 'Rotterdam',
        city_arabic: 'روتردام',
        region: 'Western Europe',
        continent: 'Europe',
        latitude: 51.9225,
        longitude: 4.4792,
        major_port: true,
        cargo_types: 'حاويات,بضائع عامة,بتروكيماويات,بضائع سائبة'
    }
];

// فتح نافذة البحث
function openPortSelector(type) {
    console.log('Opening port selector for:', type);
    currentPortType = type;

    // التحقق من وجود Bootstrap
    if (typeof bootstrap === 'undefined') {
        alert('خطأ: Bootstrap غير محمل. يرجى إعادة تحميل الصفحة.');
        console.error('Bootstrap is not loaded!');
        return;
    }

    try {
        // التأكد من وجود العنصر
        const modalElement = document.getElementById('smartPortSelector');
        if (!modalElement) {
            console.error('Modal element not found!');
            alert('خطأ: نافذة البحث غير موجودة');
            return;
        }

        // فتح النافذة
        const modal = new bootstrap.Modal(modalElement, {
            backdrop: 'static',
            keyboard: true,
            focus: true
        });

        modal.show();

        // تحديث العنوان
        const title = type === 'origin' ? 'اختيار ميناء الشحن' : 'اختيار ميناء الوصول';
        const titleElement = document.getElementById('smartPortSelectorLabel');
        if (titleElement) {
            titleElement.innerHTML = `
                <i class="fas fa-search me-2"></i>
                ${title}
                <span class="badge bg-light text-primary ms-2" id="portCount">جاري التحميل...</span>
            `;
        }

        // تحميل الموانئ
        setTimeout(() => {
            loadSimplePorts();
        }, 500);

    } catch (error) {
        console.error('Error opening modal:', error);
        alert('خطأ في فتح نافذة البحث: ' + error.message);
    }
}

// تحميل بيانات بسيطة للموانئ
function loadSimplePorts() {
    console.log('Loading simple ports...');

    // بيانات موانئ بسيطة
    const simplePorts = [
        {
            id: 1,
            port_code: 'SARIG',
            port_name: 'King Abdulaziz Port',
            port_name_arabic: 'ميناء الملك عبدالعزيز',
            country: 'Saudi Arabia',
            city: 'Dammam',
            major_port: true,
            cargo_types: 'حاويات، بضائع عامة'
        },
        {
            id: 2,
            port_code: 'SAJED',
            port_name: 'King Abdullah Port',
            port_name_arabic: 'ميناء الملك عبدالله',
            country: 'Saudi Arabia',
            city: 'Rabigh',
            major_port: true,
            cargo_types: 'حاويات، بضائع عامة'
        },
        {
            id: 3,
            port_code: 'AEJEA',
            port_name: 'Jebel Ali Port',
            port_name_arabic: 'ميناء جبل علي',
            country: 'UAE',
            city: 'Dubai',
            major_port: true,
            cargo_types: 'حاويات، ترانزيت'
        },
        {
            id: 4,
            port_code: 'CNSHA',
            port_name: 'Port of Shanghai',
            port_name_arabic: 'ميناء شنغهاي',
            country: 'China',
            city: 'Shanghai',
            major_port: true,
            cargo_types: 'حاويات، بضائع سائبة'
        },
        {
            id: 5,
            port_code: 'NLRTM',
            port_name: 'Port of Rotterdam',
            port_name_arabic: 'ميناء روتردام',
            country: 'Netherlands',
            city: 'Rotterdam',
            major_port: true,
            cargo_types: 'حاويات، بتروكيماويات'
        }
    ];

    displayPorts(simplePorts);
    updatePortCount(simplePorts.length);
}

// إغلاق النافذة
function closePortSelector() {
    const modalElement = document.getElementById('smartPortSelector');
    if (modalElement) {
        if (typeof bootstrap !== 'undefined') {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        } else {
            modalElement.style.display = 'none';
            modalElement.classList.remove('show');
            document.body.classList.remove('modal-open');

            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
        }
    }
}

// تحميل الموانئ من قاعدة البيانات
function loadPorts(searchTerm = '', continent = '', portType = '', region = '') {
    console.log('Loading ports with params:', {searchTerm, continent, portType, region});
    showLoading(true);

    const params = new URLSearchParams();
    if (searchTerm) params.append('search', searchTerm);
    if (continent) params.append('continent', continent);
    if (portType) params.append('port_type', portType);
    if (region) params.append('region', region);

    const url = `/shipments/api/search-ports?${params.toString()}`;
    console.log('Fetching URL:', url);

    fetch(url)
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Received data:', data);
            if (data.success) {
                allPorts = data.ports || [];
                filteredPorts = allPorts;
                displayPorts(filteredPorts);
                updatePortCount(filteredPorts.length);
            } else {
                showError('خطأ في تحميل الموانئ: ' + (data.message || 'خطأ غير معروف'));
                displayPorts([]); // عرض قائمة فارغة
            }
        })
        .catch(error => {
            console.error('Error loading ports:', error);
            showError('خطأ في الاتصال بالخادم: ' + error.message);
            displayPorts([]); // عرض قائمة فارغة
        })
        .finally(() => {
            showLoading(false);
        });
}

// عرض الموانئ
function displayPorts(ports) {
    console.log('Displaying ports:', ports);
    const container = document.getElementById('searchResults');
    const noResults = document.getElementById('noResults');

    if (!ports || ports.length === 0) {
        if (noResults) noResults.style.display = 'block';
        container.innerHTML = '';
        return;
    }

    if (noResults) noResults.style.display = 'none';

    try {
        const html = ports.map(port => {
            // تنظيف البيانات وتجنب الأخطاء
            const portCode = (port.port_code || '').replace(/'/g, '&#39;');
            const portName = (port.port_name || '').replace(/'/g, '&#39;');
            const country = (port.country || '').replace(/'/g, '&#39;');
            const city = (port.city || '').replace(/'/g, '&#39;');
            const portNameArabic = port.port_name_arabic || '';
            const region = port.region || '';
            const cargoTypes = port.cargo_types || '';

            return `
                <div class="port-card" onclick="selectPort(${port.id}, '${portCode}', '${portName}', '${country}', '${city}')">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div>
                            <span class="port-code">${portCode}</span>
                            ${port.major_port ? '<span class="port-major ms-2">رئيسي</span>' : ''}
                        </div>
                        <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); addToFavorites(${port.id})">
                            <i class="fas fa-heart"></i>
                        </button>
                    </div>

                    <h6 class="mb-1">${portName}</h6>
                    ${portNameArabic ? `<div class="text-muted small mb-1">${portNameArabic}</div>` : ''}

                    <div class="port-location mb-2">
                        <i class="fas fa-map-marker-alt me-1"></i>
                        ${city}, ${country}
                        ${region ? `<span class="text-muted">• ${region}</span>` : ''}
                    </div>

                    ${cargoTypes ? `
                        <div class="port-details">
                            <i class="fas fa-boxes me-1"></i>
                            ${cargoTypes}
                        </div>
                    ` : ''}

                    ${port.latitude && port.longitude ? `
                        <div class="port-details mt-1">
                            <i class="fas fa-globe me-1"></i>
                            ${parseFloat(port.latitude).toFixed(4)}, ${parseFloat(port.longitude).toFixed(4)}
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');

        container.innerHTML = html;
    } catch (error) {
        console.error('Error displaying ports:', error);
        container.innerHTML = '<div class="alert alert-danger">خطأ في عرض الموانئ</div>';
    }
}

// اختيار ميناء
function selectPort(id, code, name, country, city) {
    // إضافة إلى المفضلة تلقائياً
    addToFavorites(id);
    
    // تحديث الحقل المناسب
    const fieldName = currentPortType === 'origin' ? 'origin_port_id' : 'destination_port_id';
    const selectElement = document.querySelector(`[name="${fieldName}"]`);
    
    if (selectElement) {
        // إضافة خيار جديد إذا لم يكن موجوداً
        let option = selectElement.querySelector(`option[value="${id}"]`);
        if (!option) {
            option = document.createElement('option');
            option.value = id;
            option.textContent = `${name} - ${country}`;
            selectElement.appendChild(option);
        }
        
        // اختيار الميناء
        selectElement.value = id;
        
        // إظهار رسالة نجاح
        showSuccess(`تم اختيار ${name} كميناء ${currentPortType === 'origin' ? 'الشحن' : 'الوصول'}`);
    }
    
    // إغلاق النافذة
    bootstrap.Modal.getInstance(document.getElementById('smartPortSelector')).hide();
}

// إضافة إلى المفضلة
function addToFavorites(portId) {
    fetch('/shipments/api/add-favorite-port', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ port_id: portId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('تم إضافة الميناء إلى المفضلة');
        }
    })
    .catch(error => console.error('Error adding to favorites:', error));
}

// البحث
function searchPorts() {
    const searchTerm = document.getElementById('portSearchInput').value;
    const continent = document.getElementById('continentFilter').value;
    const portType = document.getElementById('portTypeFilter').value;
    
    loadPorts(searchTerm, continent, portType);
}

// البحث السريع
function quickFilter(filter) {
    // إزالة التفعيل من جميع الأزرار
    document.querySelectorAll('.btn-outline-primary').forEach(btn => {
        btn.classList.remove('quick-filter-active');
    });
    
    // تفعيل الزر المختار
    event.target.classList.add('quick-filter-active');
    
    if (filter === 'major') {
        document.getElementById('portTypeFilter').value = '1';
        loadPorts('', '', '1');
    } else {
        loadPorts('', '', '', filter);
    }
}

// عرض المفضلة
function showFavoritePorts() {
    fetch('/shipments/api/favorite-ports')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayPorts(data.ports);
                updatePortCount(data.ports.length);
            }
        })
        .catch(error => console.error('Error loading favorites:', error));
}

// البحث المباشر
document.getElementById('portSearchInput').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        searchPorts();
    }, 300);
});

// مسح البحث
document.getElementById('clearSearch').addEventListener('click', function() {
    document.getElementById('portSearchInput').value = '';
    document.getElementById('continentFilter').value = '';
    document.getElementById('portTypeFilter').value = '';
    loadPorts();
});

// إضافة ميناء مخصص
function addCustomPort() {
    const modal = new bootstrap.Modal(document.getElementById('customPortModal'));
    modal.show();
}

// حفظ ميناء مخصص
document.getElementById('customPortForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('/shipments/api/add-custom-port', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('تم إضافة الميناء المخصص بنجاح');
            bootstrap.Modal.getInstance(document.getElementById('customPortModal')).hide();
            loadPorts(); // إعادة تحميل القائمة
        } else {
            showError('خطأ في إضافة الميناء: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error adding custom port:', error);
        showError('خطأ في إضافة الميناء');
    });
});

// وظائف مساعدة
function showLoading(show) {
    document.getElementById('loadingSpinner').style.display = show ? 'block' : 'none';
}

function updatePortCount(count) {
    document.getElementById('portCount').textContent = `${count} ميناء`;
}

function showSuccess(message) {
    // يمكن استخدام مكتبة toast أو alert
    alert(message);
}

function showError(message) {
    alert(message);
}

// تحميل الموانئ عند فتح النافذة
document.getElementById('smartPortSelector').addEventListener('shown.bs.modal', function() {
    if (allPorts.length === 0) {
        loadPorts();
    }
});
</script>
