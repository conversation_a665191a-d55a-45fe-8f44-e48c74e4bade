-- =====================================================
-- سكريبت تشخيص البيئة
-- Environment Diagnosis Script
-- =====================================================

SET SERVEROUTPUT ON;
SET PAGESIZE 1000;
SET LINESIZE 200;

PROMPT =====================================================
PROMPT تشخيص البيئة الحالية
PROMPT Environment Diagnosis
PROMPT =====================================================

-- 1. التحقق من المستخدم الحالي والصلاحيات
PROMPT 
PROMPT 1. معلومات المستخدم الحالي:
SELECT USER as current_user, 
       SYS_CONTEXT('USERENV', 'SESSION_USER') as session_user,
       SYS_CONTEXT('USERENV', 'DB_NAME') as database_name
FROM DUAL;

-- 2. التحقق من الصلاحيات
PROMPT 
PROMPT 2. الصلاحيات المتاحة:
SELECT privilege 
FROM user_sys_privs 
WHERE privilege IN ('CREATE TABLE', 'CREATE SEQUENCE', 'CREATE TRIGGER', 'CREATE VIEW', 'CREATE PROCEDURE')
ORDER BY privilege;

-- 3. التحقق من وجود الجداول المطلوبة
PROMPT 
PROMPT 3. الجداول الموجودة:
SELECT table_name, status 
FROM user_tables 
WHERE table_name IN ('TRANSFERS', 'SUPPLIERS', 'CURRENT_BALANCES', 'TRANSFER_SUPPLIER_DISTRIBUTIONS', 'TRANSFER_ACTIVITY_LOG')
ORDER BY table_name;

-- 4. التحقق من الكائنات الموجودة التي قد تتعارض
PROMPT 
PROMPT 4. الكائنات الموجودة ذات الصلة:
SELECT object_name, object_type, status 
FROM user_objects 
WHERE object_name LIKE '%TRANSFER%' OR object_name LIKE '%SUPPLIER%'
ORDER BY object_type, object_name;

-- 5. التحقق من الـ Sequences الموجودة
PROMPT 
PROMPT 5. الـ Sequences الموجودة:
SELECT sequence_name, last_number 
FROM user_sequences 
WHERE sequence_name LIKE '%TRANSFER%' OR sequence_name LIKE '%SUPPLIER%'
ORDER BY sequence_name;

-- 6. التحقق من الـ Triggers الموجودة
PROMPT 
PROMPT 6. الـ Triggers الموجودة:
SELECT trigger_name, table_name, status 
FROM user_triggers 
WHERE trigger_name LIKE '%TSD%' OR trigger_name LIKE '%TRANSFER%'
ORDER BY trigger_name;

-- 7. التحقق من الـ Views الموجودة
PROMPT 
PROMPT 7. الـ Views الموجودة:
SELECT view_name 
FROM user_views 
WHERE view_name LIKE '%TRANSFER%' OR view_name LIKE '%SUPPLIER%'
ORDER BY view_name;

-- 8. التحقق من الـ Procedures الموجودة
PROMPT 
PROMPT 8. الـ Procedures الموجودة:
SELECT object_name 
FROM user_objects 
WHERE object_type = 'PROCEDURE' 
AND (object_name LIKE '%TRANSFER%' OR object_name LIKE '%SUPPLIER%')
ORDER BY object_name;

-- 9. التحقق من هيكل جدول transfers إذا كان موجوداً
PROMPT 
PROMPT 9. هيكل جدول transfers (إذا كان موجوداً):
BEGIN
    FOR rec IN (SELECT column_name, data_type, data_length, nullable 
                FROM user_tab_columns 
                WHERE table_name = 'TRANSFERS' 
                ORDER BY column_id) LOOP
        DBMS_OUTPUT.PUT_LINE(rec.column_name || ' - ' || rec.data_type || 
                           CASE WHEN rec.data_length IS NOT NULL THEN '(' || rec.data_length || ')' ELSE '' END ||
                           ' - ' || rec.nullable);
    END LOOP;
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('جدول transfers غير موجود');
END;
/

-- 10. التحقق من هيكل جدول suppliers إذا كان موجوداً
PROMPT 
PROMPT 10. هيكل جدول suppliers (إذا كان موجوداً):
BEGIN
    FOR rec IN (SELECT column_name, data_type, data_length, nullable 
                FROM user_tab_columns 
                WHERE table_name = 'SUPPLIERS' 
                ORDER BY column_id) LOOP
        DBMS_OUTPUT.PUT_LINE(rec.column_name || ' - ' || rec.data_type || 
                           CASE WHEN rec.data_length IS NOT NULL THEN '(' || rec.data_length || ')' ELSE '' END ||
                           ' - ' || rec.nullable);
    END LOOP;
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('جدول suppliers غير موجود');
END;
/

-- 11. التحقق من إعدادات قاعدة البيانات
PROMPT 
PROMPT 11. إعدادات قاعدة البيانات المهمة:
SELECT name, value 
FROM v$parameter 
WHERE name IN ('compatible', 'db_block_size', 'processes')
ORDER BY name;

PROMPT 
PROMPT =====================================================
PROMPT انتهى التشخيص
PROMPT Diagnosis Complete
PROMPT =====================================================
