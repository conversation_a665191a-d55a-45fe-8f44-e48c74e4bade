#!/usr/bin/env python3
"""
إضافة أعمدة روابط المشاركة لجدول وثائق أوامر الشراء
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oracle_manager import get_oracle_manager

def add_share_link_columns():
    """إضافة أعمدة روابط المشاركة"""
    
    oracle_manager = get_oracle_manager()
    
    try:
        print("🔗 إضافة أعمدة روابط المشاركة لجدول PO_DOCUMENTS")
        print("=" * 70)
        
        # 1. فحص الأعمدة الموجودة
        print("\n1️⃣ فحص الأعمدة الموجودة...")
        
        check_columns_query = """
            SELECT COLUMN_NAME
            FROM USER_TAB_COLUMNS
            WHERE TABLE_NAME = 'PO_DOCUMENTS'
            AND COLUMN_NAME IN ('NEXTCLOUD_SHARE_LINK', 'ONEDRIVE_SHARE_LINK')
        """
        
        existing_columns = oracle_manager.execute_query(check_columns_query, [])
        existing_column_names = [col[0] for col in existing_columns] if existing_columns else []
        
        print(f"📋 الأعمدة الموجودة: {existing_column_names}")
        
        # 2. إضافة عمود Nextcloud إذا لم يكن موجوداً
        if 'NEXTCLOUD_SHARE_LINK' not in existing_column_names:
            print("\n2️⃣ إضافة عمود NEXTCLOUD_SHARE_LINK...")
            
            try:
                add_nextcloud_query = """
                    ALTER TABLE PO_DOCUMENTS 
                    ADD NEXTCLOUD_SHARE_LINK VARCHAR2(1000)
                """
                oracle_manager.execute_query(add_nextcloud_query, [])
                print("✅ تم إضافة عمود NEXTCLOUD_SHARE_LINK")
                
                # إضافة تعليق
                comment_query = """
                    COMMENT ON COLUMN PO_DOCUMENTS.NEXTCLOUD_SHARE_LINK 
                    IS 'رابط مشاركة Nextcloud للوثيقة'
                """
                oracle_manager.execute_query(comment_query, [])
                
            except Exception as e:
                print(f"❌ خطأ في إضافة عمود NEXTCLOUD_SHARE_LINK: {e}")
        else:
            print("✅ عمود NEXTCLOUD_SHARE_LINK موجود بالفعل")
        
        # 3. إضافة عمود OneDrive إذا لم يكن موجوداً
        if 'ONEDRIVE_SHARE_LINK' not in existing_column_names:
            print("\n3️⃣ إضافة عمود ONEDRIVE_SHARE_LINK...")
            
            try:
                add_onedrive_query = """
                    ALTER TABLE PO_DOCUMENTS 
                    ADD ONEDRIVE_SHARE_LINK VARCHAR2(1000)
                """
                oracle_manager.execute_query(add_onedrive_query, [])
                print("✅ تم إضافة عمود ONEDRIVE_SHARE_LINK")
                
                # إضافة تعليق
                comment_query = """
                    COMMENT ON COLUMN PO_DOCUMENTS.ONEDRIVE_SHARE_LINK 
                    IS 'رابط مشاركة OneDrive للوثيقة'
                """
                oracle_manager.execute_query(comment_query, [])
                
            except Exception as e:
                print(f"❌ خطأ في إضافة عمود ONEDRIVE_SHARE_LINK: {e}")
        else:
            print("✅ عمود ONEDRIVE_SHARE_LINK موجود بالفعل")
        
        # 4. فحص الهيكل النهائي
        print("\n4️⃣ فحص الهيكل النهائي...")
        
        final_structure_query = """
            SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE
            FROM USER_TAB_COLUMNS
            WHERE TABLE_NAME = 'PO_DOCUMENTS'
            ORDER BY COLUMN_ID
        """
        
        final_result = oracle_manager.execute_query(final_structure_query, [])
        
        print(f"\n📋 هيكل الجدول النهائي:")
        print(f"{'العمود':<25} {'النوع':<15} {'الطول':<10} {'يقبل NULL'}")
        print("-" * 65)
        
        for col in final_result:
            column_name = col[0]
            data_type = col[1]
            data_length = col[2] if col[2] else ''
            nullable = 'نعم' if col[3] == 'Y' else 'لا'
            
            # تمييز الأعمدة الجديدة
            marker = " 🆕" if column_name in ['NEXTCLOUD_SHARE_LINK', 'ONEDRIVE_SHARE_LINK'] else ""
            
            print(f"{column_name:<25} {data_type:<15} {str(data_length):<10} {nullable}{marker}")
        
        # 5. اختبار إدراج وثيقة تجريبية (اختياري)
        print("\n5️⃣ اختبار الجدول...")
        
        # فحص عدد الوثائق
        count_query = "SELECT COUNT(*) FROM PO_DOCUMENTS"
        count_result = oracle_manager.execute_query(count_query, [])
        doc_count = count_result[0][0] if count_result else 0
        
        print(f"📄 عدد الوثائق الحالية: {doc_count}")
        
        # فحص أوامر الشراء المتاحة
        po_query = "SELECT COUNT(*) FROM PURCHASE_ORDERS"
        po_result = oracle_manager.execute_query(po_query, [])
        po_count = po_result[0][0] if po_result else 0
        
        print(f"📋 عدد أوامر الشراء المتاحة: {po_count}")
        
        print("\n🎉 تم تحديث جدول وثائق أوامر الشراء بنجاح!")
        print("\n📋 الخلاصة:")
        print("   ✅ جدول PO_DOCUMENTS محدث")
        print("   ✅ عمود NEXTCLOUD_SHARE_LINK متاح")
        print("   ✅ عمود ONEDRIVE_SHARE_LINK متاح")
        print("   ✅ النظام جاهز لإنشاء روابط المشاركة")
        print("   ✅ يمكن الآن رفع الوثائق وإنشاء الروابط")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة أعمدة روابط المشاركة: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء إضافة أعمدة روابط المشاركة...")
    success = add_share_link_columns()
    
    if success:
        print("\n🌟 تم تحديث الجدول بنجاح!")
        print("الآن يمكنك استخدام جميع مميزات إدارة الوثائق")
    else:
        print("\n❌ فشل في تحديث الجدول")
        sys.exit(1)
