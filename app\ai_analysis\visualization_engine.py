"""
محرك التصور الذكي لتحليل بيانات ITEM_MOVEMENT
Intelligent Visualization Engine for ITEM_MOVEMENT Analysis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.figure_factory as ff
from wordcloud import WordCloud

import warnings
warnings.filterwarnings('ignore')

# إعداد الخطوط العربية
plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']

import logging
from typing import Dict, List, Optional
import base64
from io import BytesIO

logger = logging.getLogger(__name__)

class VisualizationEngine:
    """محرك التصور الذكي"""
    
    def __init__(self, analyzer):
        """
        تهيئة محرك التصور
        
        Args:
            analyzer: محلل البيانات الذكي
        """
        self.analyzer = analyzer
        self.color_palette = px.colors.qualitative.Set3
        
    def create_dashboard(self) -> Dict:
        """إنشاء لوحة تحكم شاملة"""
        try:
            dashboard = {
                'overview_charts': self._create_overview_charts(),
                'distribution_charts': self._create_distribution_charts(),
                'correlation_heatmap': self._create_correlation_heatmap(),
                'time_series_charts': self._create_time_series_charts(),
                'advanced_charts': self._create_advanced_charts()
            }
            
            return dashboard
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء لوحة التحكم: {str(e)}")
            return {}
    
    def _create_overview_charts(self) -> Dict:
        """إنشاء الرسوم البيانية العامة"""
        try:
            if self.analyzer.processed_data is None:
                return {}
            
            charts = {}
            
            # رسم بياني لتوزيع أنواع البيانات
            data_types = self.analyzer.processed_data.dtypes.value_counts()
            
            fig_types = go.Figure(data=[
                go.Pie(
                    labels=data_types.index.astype(str),
                    values=data_types.values,
                    hole=0.3,
                    title="توزيع أنواع البيانات"
                )
            ])
            
            fig_types.update_layout(
                title="توزيع أنواع البيانات في الجدول",
                font=dict(size=14),
                showlegend=True
            )
            
            charts['data_types_distribution'] = fig_types.to_html(include_plotlyjs='cdn')
            
            # رسم بياني للقيم المفقودة
            missing_data = self.analyzer.processed_data.isnull().sum()
            missing_data = missing_data[missing_data > 0].sort_values(ascending=True)
            
            if not missing_data.empty:
                fig_missing = go.Figure(data=[
                    go.Bar(
                        x=missing_data.values,
                        y=missing_data.index,
                        orientation='h',
                        marker_color='lightcoral'
                    )
                ])
                
                fig_missing.update_layout(
                    title="القيم المفقودة في كل عمود",
                    xaxis_title="عدد القيم المفقودة",
                    yaxis_title="الأعمدة",
                    font=dict(size=12)
                )
                
                charts['missing_values'] = fig_missing.to_html(include_plotlyjs='cdn')
            
            # إحصائيات عامة
            stats_data = {
                'إجمالي الصفوف': len(self.analyzer.processed_data),
                'إجمالي الأعمدة': len(self.analyzer.processed_data.columns),
                'الصفوف المكررة': self.analyzer.processed_data.duplicated().sum(),
                'حجم البيانات (MB)': round(self.analyzer.processed_data.memory_usage(deep=True).sum() / 1024**2, 2)
            }
            
            fig_stats = go.Figure(data=[
                go.Bar(
                    x=list(stats_data.keys()),
                    y=list(stats_data.values()),
                    marker_color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
                )
            ])
            
            fig_stats.update_layout(
                title="إحصائيات عامة عن البيانات",
                xaxis_title="المؤشرات",
                yaxis_title="القيم",
                font=dict(size=12)
            )
            
            charts['general_stats'] = fig_stats.to_html(include_plotlyjs='cdn')
            
            return charts
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الرسوم العامة: {str(e)}")
            return {}
    
    def _create_distribution_charts(self) -> Dict:
        """إنشاء رسوم التوزيع"""
        try:
            charts = {}
            numeric_columns = self.analyzer.processed_data.select_dtypes(include=[np.number]).columns
            
            # رسوم التوزيع للأعمدة الرقمية
            for i, column in enumerate(numeric_columns[:6]):  # أول 6 أعمدة
                try:
                    data = self.analyzer.processed_data[column].dropna()
                    
                    if len(data) == 0:
                        continue
                    
                    # رسم الهيستوجرام
                    fig = go.Figure()
                    
                    fig.add_trace(go.Histogram(
                        x=data,
                        nbinsx=30,
                        name=f'توزيع {column}',
                        marker_color=self.color_palette[i % len(self.color_palette)]
                    ))
                    
                    fig.update_layout(
                        title=f'توزيع البيانات - {column}',
                        xaxis_title=column,
                        yaxis_title='التكرار',
                        font=dict(size=12),
                        showlegend=False
                    )
                    
                    charts[f'distribution_{column}'] = fig.to_html(include_plotlyjs='cdn')
                    
                    # Box Plot
                    fig_box = go.Figure()
                    
                    fig_box.add_trace(go.Box(
                        y=data,
                        name=column,
                        marker_color=self.color_palette[i % len(self.color_palette)]
                    ))
                    
                    fig_box.update_layout(
                        title=f'صندوق الشوارب - {column}',
                        yaxis_title=column,
                        font=dict(size=12),
                        showlegend=False
                    )
                    
                    charts[f'boxplot_{column}'] = fig_box.to_html(include_plotlyjs='cdn')
                    
                except Exception as e:
                    logger.warning(f"لا يمكن إنشاء رسم التوزيع للعمود {column}: {str(e)}")
            
            return charts
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء رسوم التوزيع: {str(e)}")
            return {}
    
    def _create_correlation_heatmap(self) -> str:
        """إنشاء خريطة حرارية للارتباطات"""
        try:
            numeric_data = self.analyzer.processed_data.select_dtypes(include=[np.number])
            
            if numeric_data.empty or len(numeric_data.columns) < 2:
                return ""
            
            # حساب مصفوفة الارتباط
            correlation_matrix = numeric_data.corr()
            
            # إنشاء الخريطة الحرارية
            fig = go.Figure(data=go.Heatmap(
                z=correlation_matrix.values,
                x=correlation_matrix.columns,
                y=correlation_matrix.columns,
                colorscale='RdBu',
                zmid=0,
                text=np.round(correlation_matrix.values, 2),
                texttemplate="%{text}",
                textfont={"size": 10},
                hoverongaps=False
            ))
            
            fig.update_layout(
                title='خريطة الارتباطات بين المتغيرات',
                font=dict(size=12),
                width=800,
                height=600
            )
            
            return fig.to_html(include_plotlyjs='cdn')
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء خريطة الارتباطات: {str(e)}")
            return ""
    
    def _create_time_series_charts(self) -> Dict:
        """إنشاء رسوم السلاسل الزمنية"""
        try:
            charts = {}
            date_columns = self.analyzer.processed_data.select_dtypes(include=['datetime64']).columns
            numeric_columns = self.analyzer.processed_data.select_dtypes(include=[np.number]).columns
            
            if len(date_columns) == 0 or len(numeric_columns) == 0:
                return {}
            
            # رسم السلاسل الزمنية
            for date_col in date_columns[:1]:  # أول عمود تاريخ
                for i, num_col in enumerate(numeric_columns[:3]):  # أول 3 أعمدة رقمية
                    try:
                        # إعداد البيانات
                        temp_df = self.analyzer.processed_data[[date_col, num_col]].dropna()
                        temp_df = temp_df.sort_values(date_col)
                        
                        if len(temp_df) < 2:
                            continue
                        
                        # تجميع البيانات حسب التاريخ (إذا كانت هناك قيم متعددة لنفس التاريخ)
                        daily_data = temp_df.groupby(temp_df[date_col].dt.date)[num_col].mean().reset_index()
                        
                        fig = go.Figure()
                        
                        fig.add_trace(go.Scatter(
                            x=daily_data[date_col],
                            y=daily_data[num_col],
                            mode='lines+markers',
                            name=f'{num_col} عبر الزمن',
                            line=dict(color=self.color_palette[i % len(self.color_palette)])
                        ))
                        
                        fig.update_layout(
                            title=f'تطور {num_col} عبر الزمن',
                            xaxis_title='التاريخ',
                            yaxis_title=num_col,
                            font=dict(size=12),
                            showlegend=True
                        )
                        
                        charts[f'timeseries_{num_col}'] = fig.to_html(include_plotlyjs='cdn')
                        
                    except Exception as e:
                        logger.warning(f"لا يمكن إنشاء السلسلة الزمنية للعمود {num_col}: {str(e)}")
            
            return charts
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء رسوم السلاسل الزمنية: {str(e)}")
            return {}
    
    def _create_advanced_charts(self) -> Dict:
        """إنشاء الرسوم المتقدمة"""
        try:
            charts = {}
            
            # رسم PCA إذا كان لدينا بيانات رقمية كافية
            numeric_data = self.analyzer.processed_data.select_dtypes(include=[np.number])
            
            if len(numeric_data.columns) >= 3:
                from sklearn.decomposition import PCA
                from sklearn.preprocessing import StandardScaler
                
                # تطبيع البيانات
                scaler = StandardScaler()
                scaled_data = scaler.fit_transform(numeric_data.fillna(0))
                
                # تطبيق PCA
                pca = PCA(n_components=2)
                pca_result = pca.fit_transform(scaled_data)
                
                fig_pca = go.Figure()
                
                fig_pca.add_trace(go.Scatter(
                    x=pca_result[:, 0],
                    y=pca_result[:, 1],
                    mode='markers',
                    marker=dict(
                        size=8,
                        color=np.arange(len(pca_result)),
                        colorscale='Viridis',
                        showscale=True
                    ),
                    name='نقاط البيانات'
                ))
                
                fig_pca.update_layout(
                    title=f'تحليل المكونات الأساسية (PCA)<br>نسبة التباين المفسر: {pca.explained_variance_ratio_.sum():.2%}',
                    xaxis_title=f'المكون الأول ({pca.explained_variance_ratio_[0]:.2%})',
                    yaxis_title=f'المكون الثاني ({pca.explained_variance_ratio_[1]:.2%})',
                    font=dict(size=12)
                )
                
                charts['pca_analysis'] = fig_pca.to_html(include_plotlyjs='cdn')
            
            return charts
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الرسوم المتقدمة: {str(e)}")
            return {}
