{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- شريط الأدوات العلوي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-file-contract me-2"></i>
                            عقود الشراء
                        </h5>
                        <div class="btn-group">
                            <a href="{{ url_for('purchase_contracts.new') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>عقد جديد
                            </a>
                            <a href="{{ url_for('purchase_contracts.search') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-search me-1"></i>بحث متقدم
                            </a>
                            <a href="{{ url_for('purchase_contracts.reports') }}" class="btn btn-outline-info">
                                <i class="fas fa-chart-bar me-1"></i>التقارير
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- شريط البحث السريع -->
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <input type="text" name="contract_no" class="form-control" 
                                   placeholder="رقم العقد" value="{{ request.args.get('contract_no', '') }}">
                        </div>
                        <div class="col-md-3">
                            <input type="text" name="vendor_name" class="form-control" 
                                   placeholder="اسم المورد" value="{{ request.args.get('vendor_name', '') }}">
                        </div>
                        <div class="col-md-2">
                            <select name="status" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="نشط" {{ 'selected' if request.args.get('status') == 'نشط' }}>نشط</option>
                                <option value="منتهي" {{ 'selected' if request.args.get('status') == 'منتهي' }}>منتهي</option>
                                <option value="ملغي" {{ 'selected' if request.args.get('status') == 'ملغي' }}>ملغي</option>
                                <option value="معلق" {{ 'selected' if request.args.get('status') == 'معلق' }}>معلق</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select name="contract_type" class="form-select">
                                <option value="">جميع الأنواع</option>
                                <option value="إطاري" {{ 'selected' if request.args.get('contract_type') == 'إطاري' }}>إطاري</option>
                                <option value="محدد" {{ 'selected' if request.args.get('contract_type') == 'محدد' }}>محدد</option>
                                <option value="خدمات" {{ 'selected' if request.args.get('contract_type') == 'خدمات' }}>خدمات</option>
                                <option value="توريد" {{ 'selected' if request.args.get('contract_type') == 'توريد' }}>توريد</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-outline-primary w-100">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي العقود</h6>
                            <h3 class="stats-number">{{ stats.total_contracts }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file-contract fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">العقود النشطة</h6>
                            <h3 class="stats-number">{{ stats.active_contracts }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">العقود المنتهية</h6>
                            <h3 class="stats-number">{{ stats.expired_contracts }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي القيمة</h6>
                            <h3 class="stats-number">{{ "{:,.2f}".format(stats.total_value) }}</h3>
                            <small>ريال سعودي</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول العقود -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">قائمة عقود الشراء</h6>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th width="80">م</th>
                                    <th width="120">رقم العقد</th>
                                    <th width="100">تاريخ العقد</th>
                                    <th>اسم المورد</th>
                                    <th width="100">نوع العقد</th>
                                    <th width="120">المبلغ</th>
                                    <th width="80">العملة</th>
                                    <th width="80">الحالة</th>
                                    <th width="150">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for contract in contracts.items %}
                                <tr>
                                    <td>{{ loop.index + (contracts.page - 1) * contracts.per_page }}</td>
                                    <td>
                                        <a href="{{ url_for('purchase_contracts.view', id=contract.id) }}" 
                                           class="text-decoration-none fw-bold">
                                            {{ contract.contract_no }}
                                        </a>
                                    </td>
                                    <td>{{ contract.contract_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ contract.vendor_name }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ contract.contract_type }}</span>
                                    </td>
                                    <td class="text-end">{{ "{:,.2f}".format(contract.net_amount) }}</td>
                                    <td>{{ contract.currency_code }}</td>
                                    <td>
                                        {% if contract.status == 'نشط' %}
                                            <span class="badge bg-success">{{ contract.status }}</span>
                                        {% elif contract.status == 'منتهي' %}
                                            <span class="badge bg-warning">{{ contract.status }}</span>
                                        {% elif contract.status == 'ملغي' %}
                                            <span class="badge bg-danger">{{ contract.status }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ contract.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('purchase_contracts.view', id=contract.id) }}" 
                                               class="btn btn-outline-primary" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('purchase_contracts.edit', id=contract.id) }}" 
                                               class="btn btn-outline-secondary" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('purchase_contracts.print_contract', id=contract.id) }}" 
                                               class="btn btn-outline-info" title="طباعة" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="deleteContract({{ contract.id }}, '{{ contract.contract_no }}')" 
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد عقود شراء</p>
                                        <a href="{{ url_for('purchase_contracts.new') }}" class="btn btn-primary">
                                            <i class="fas fa-plus me-1"></i>إضافة عقد جديد
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- التصفح -->
                {% if contracts.pages > 1 %}
                <div class="card-footer">
                    <nav aria-label="تصفح العقود">
                        <ul class="pagination justify-content-center mb-0">
                            {% if contracts.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('purchase_contracts.index', page=contracts.prev_num, **request.args) }}">
                                        السابق
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in contracts.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != contracts.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('purchase_contracts.index', page=page_num, **request.args) }}">
                                                {{ page_num }}
                                            </a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if contracts.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('purchase_contracts.index', page=contracts.next_num, **request.args) }}">
                                        التالي
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function deleteContract(id, contractNo) {
    if (confirm('هل أنت متأكد من حذف عقد الشراء رقم ' + contractNo + '؟')) {
        fetch(`/purchase-contracts/${id}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف العقد');
            }
        });
    }
}
</script>
{% endblock %}
