#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
اختبار نهائي للتحقق من إصلاح الترحيل المحاسبي
Final Verification Test for Accounting Fix
"""

import sys
import os
sys.path.insert(0, os.getcwd())

from oracle_manager import OracleManager

def final_verification():
    """اختبار نهائي للترحيل المحاسبي"""
    
    oracle = OracleManager()
    
    print('🧪 اختبار نهائي للترحيل المحاسبي...')
    print('=' * 80)

    # فحص الرصيد الحالي
    balance_query = """
    SELECT current_balance, debit_amount, credit_amount, last_document_number
    FROM CURRENT_BALANCES 
    WHERE entity_type_code = 'MONEY_CHANGER' 
    AND entity_id = 2
    AND currency_code = 'USD'
    """

    print('📊 الرصيد الحالي للصراف 2:')
    current_result = oracle.execute_query(balance_query)
    if current_result:
        current_balance, current_debit, current_credit, current_doc = current_result[0]
        print(f'   رصيد حالي: {current_balance}')
        print(f'   مدين: {current_debit}, دائن: {current_credit}')
        print(f'   آخر مستند: {current_doc}')
        
        if current_debit and current_credit:
            calculated = current_debit - current_credit
            print(f'   الحساب: {current_debit} - {current_credit} = {calculated}')
        else:
            print('   الحساب: غير محسوب (قيم NULL)')

    # إنشاء حوالة اختبار أخيرة
    print('\n🧪 إنشاء حوالة اختبار أخيرة...')
    try:
        final_transfer_query = """
        INSERT INTO TRANSFER_REQUESTS (
            id, request_number, beneficiary_id, amount, currency, total_amount,
            purpose, delivery_method, status, money_changer_bank_id,
            created_at, updated_at, created_by, updated_by
        ) VALUES (
            6666, 'TEST-6666', 1, 1000, 'USD', 1000,
            'Final verification test', 'BANK', 'approved', 2,
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1
        )
        """
        
        oracle.execute_update(final_transfer_query)
        print('✅ تم إنشاء حوالة اختبار 6666')
        
    except Exception as e:
        if 'unique constraint' in str(e).lower():
            print('⚠️ الحوالة 6666 موجودة مسبقاً')
            update_query = """
            UPDATE TRANSFER_REQUESTS SET
                status = 'approved',
                updated_at = CURRENT_TIMESTAMP
            WHERE id = 6666
            """
            oracle.execute_update(update_query)
            print('✅ تم تحديث حالة الحوالة 6666')

    # تنفيذ الحوالة الأخيرة
    print('\n🚀 تنفيذ الحوالة 6666...')
    try:
        exec_query = """
        BEGIN
            EXECUTE_TRANSFER_ACCOUNTING(
                p_transfer_id => 6666,
                p_money_changer_id => 2,
                p_total_amount => 1000,
                p_currency_code => 'USD',
                p_supplier_distributions => '[{"supplier_id": 1, "amount": 1000}]',
                p_user_id => 1
            );
        END;
        """
        
        oracle.execute_update(exec_query)
        print('✅ تم تنفيذ الحوالة 6666 بنجاح!')
        
        # فحص النتيجة النهائية
        print('\n📊 النتيجة النهائية:')
        final_result = oracle.execute_query(balance_query)
        if final_result:
            final_balance, final_debit, final_credit, final_doc = final_result[0]
            print(f'   رصيد نهائي: {final_balance}')
            print(f'   مدين: {final_debit}, دائن: {final_credit}')
            print(f'   آخر مستند: {final_doc}')
            
            # حساب التغيير
            if current_balance is not None and final_balance is not None:
                balance_change = final_balance - current_balance
                print(f'   تغيير الرصيد: {balance_change}')
                
                if abs(balance_change + 1000) < 0.01:
                    print('   ✅ الترحيل المحاسبي يعمل بشكل مثالي!')
                    print('   ✅ تم خصم 1000 من رصيد الصراف بشكل صحيح')
                    return True
                else:
                    print(f'   ⚠️ تغيير غير متوقع: متوقع -1000, فعلي {balance_change}')
                    return False
            
            # التحقق من رصيد المورد
            print('\n📊 فحص رصيد المورد 1:')
            supplier_query = """
            SELECT current_balance, debit_amount, credit_amount, last_document_number
            FROM CURRENT_BALANCES 
            WHERE entity_type_code = 'SUPPLIER' 
            AND entity_id = 1
            AND currency_code = 'USD'
            """
            
            supplier_result = oracle.execute_query(supplier_query)
            if supplier_result:
                sup_balance, sup_debit, sup_credit, sup_doc = supplier_result[0]
                print(f'   رصيد المورد: {sup_balance}')
                print(f'   مدين: {sup_debit}, دائن: {sup_credit}')
                print(f'   آخر مستند: {sup_doc}')
            else:
                print('   لا يوجد رصيد للمورد')
        
    except Exception as e:
        print(f'❌ خطأ في تنفيذ الحوالة: {str(e)}')
        return False

def check_transfer_status():
    """فحص حالة الحوالة المنفذة"""
    
    oracle = OracleManager()
    
    print('\n🔍 فحص حالة الحوالة 6666:')
    
    # فحص في TRANSFER_REQUESTS
    req_query = "SELECT status, processed_at FROM TRANSFER_REQUESTS WHERE id = 6666"
    req_result = oracle.execute_query(req_query)
    if req_result:
        req_status, processed_at = req_result[0]
        print(f'   TRANSFER_REQUESTS: {req_status}, معالجة: {processed_at}')
    
    # فحص في TRANSFERS
    trans_query = "SELECT status, execution_date FROM TRANSFERS WHERE request_id = 6666"
    trans_result = oracle.execute_query(trans_query)
    if trans_result:
        trans_status, exec_date = trans_result[0]
        print(f'   TRANSFERS: {trans_status}, تنفيذ: {exec_date}')
    else:
        print('   TRANSFERS: لا يوجد سجل')

if __name__ == "__main__":
    print("🎯 بدء الاختبار النهائي للترحيل المحاسبي")
    print("=" * 80)
    
    if final_verification():
        check_transfer_status()
        
        print("\n🎉 تم إصلاح كارثة عدم ترحيل الأرصدة بنجاح!")
        print("✅ زر تنفيذ الحوالة يعمل الآن بشكل مثالي")
        print("✅ يتم ترحيل أرصدة الصرافين والموردين بشكل صحيح")
        print("✅ جميع الجداول يتم تحديثها بشكل متسق")
    else:
        print("\n❌ ما زالت هناك مشكلة في الترحيل المحاسبي")
    
    print("\n" + "=" * 80)
    print("🏁 انتهى الاختبار النهائي")
