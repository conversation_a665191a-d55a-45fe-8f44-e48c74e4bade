{% extends "base.html" %}

{% block title %}إضافة عقد جديد{% endblock %}

{% block extra_css %}


<style>
.contract-form {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 10px 0;
}

.form-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.form-header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 700;
}

.form-header p {
    margin: 10px 0 0 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.form-body {
    padding: 5px;
}

.section-title {
    color: #667eea;
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e9ecef;
    display: flex;
    align-items: center;
}

.section-title i {
    margin-left: 10px;
    color: #764ba2;
}

.form-group {
    margin-bottom: 10px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.required {
    color: #dc3545;
}

.btn-search {
    background: #667eea;
    border: none;
    color: white;
    padding: 12px 15px;
    border-radius: 0 8px 8px 0;
    transition: all 0.3s ease;
}

.btn-search:hover {
    background: #5a6fd8;
    color: white;
}

.table-container {
    background: #f8f9fa;
    border-radius: 5px;
    padding: 5px;
    margin-top: 10px;
    position: relative;
    overflow: hidden;
    contain: layout style;
}

/* تأثير بصري لتحديث مبلغ العقد */
.contract-amount-updated {
    background-color: #d1e7dd !important;
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    transition: all 0.3s ease;
}

.bg-success-subtle {
    background-color: #d1e7dd !important;
    transition: background-color 0.3s ease;
}

/* تحسين مظهر الحقول المطلوبة */
.required {
    color: #dc3545;
    font-weight: bold;
}

/* تحسين مظهر الحقول عند التحقق */
.field-error {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.field-success {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

.table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.toolbar-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.btn-toolbar {
    padding: 8px 15px;
    border-radius: 6px;
    border: none;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary-toolbar {
    background: #667eea;
    color: white;
}

.btn-primary-toolbar:hover {
    background: #5a6fd8;
    color: white;
}

.btn-success-toolbar {
    background: #28a745;
    color: white;
}

.btn-success-toolbar:hover {
    background: #218838;
    color: white;
}

.btn-danger-toolbar {
    background: #dc3545;
    color: white;
}

.btn-danger-toolbar:hover {
    background: #c82333;
    color: white;
}

.btn-info-toolbar {
    background: #17a2b8;
    color: white;
}

.btn-info-toolbar:hover {
    background: #138496;
    color: white;
}

.statistics-bar {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    border: 1px solid #dee2e6;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 5px;
}

.action-buttons {
    background: #f8f9fa;
    padding: 30px;
    text-align: center;
    border-top: 1px solid #dee2e6;
}

.btn-action {
    padding: 12px 30px;
    border-radius: 8px;
    font-weight: 600;
    margin: 0 10px;
    min-width: 150px;
    transition: all 0.3s ease;
}

.btn-save {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
}

.btn-save:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    color: white;
}

.btn-cancel:hover {
    background: #5a6268;
    color: white;
}

/* Handsontable RTL Support */
.handsontable {
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    position: relative !important;
    z-index: 1 !important;
}

.handsontable .htCore {
    direction: rtl;
}

/* تأكد من أن الجدول يظهر في المكان الصحيح */
#contractDetailsTable {
    position: static !important;
    z-index: auto !important;
    clear: both !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* منع الجدول من الظهور في مكان خاطئ */
.handsontable {
    position: static !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    transform: none !important;
    margin: 0 !important;
    float: none !important;
}

/* إجبار الجدول على البقاء داخل الحاوي */
.table-container .handsontable {
    position: relative !important;
    display: block !important;
    width: 100% !important;
    height: 400px !important;
}

/* منع أي تداخل من CSS خارجي */
.contract-form .handsontable,
.form-container .handsontable,
.form-body .handsontable {
    position: static !important;
    z-index: 1 !important;
}

/* إصلاح مشكلة ظهور الجدول في المكان الخاطئ */
.handsontable {
    position: relative !important;
    z-index: 1 !important;
}

#contractDetailsTable .handsontable {
    position: relative !important;
    top: 0 !important;
    left: 0 !important;
    right: auto !important;
    bottom: auto !important;
}

.readonly-cell {
    background-color: #f8f9fa !important;
    color: #6c757d;
}

.calculated-cell {
    background-color: #e3f2fd !important;
    font-weight: 600;
}

.handsontable .htInvalid {
    background-color: #ffebee !important;
    color: #c62828;
}

/* تنسيق أعمدة متابعة التنفيذ */
.executed-quantity {
    background-color: #e9ecef !important;
    border: 1px solid #ced4da;
}

.remaining-quantity-complete {
    background-color: #d4edda !important; /* أخضر - مكتمل */
    border: 1px solid #c3e6cb;
    color: #155724;
}

.remaining-quantity-partial {
    background-color: #fff3cd !important; /* أصفر - جزئي */
    border: 1px solid #ffeaa7;
    color: #856404;
}

.remaining-quantity-pending {
    background-color: #f8d7da !important; /* أحمر - غير منفذ */
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* تحسين مظهر الجدول */
.table th {
    font-size: 13px;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    white-space: nowrap;
}

.table td {
    padding: 5px;
    vertical-align: middle;
}

.table td input {
    font-size: 12px;
    text-align: center;
}

/* إصلاح مشكلة النوافذ المنبثقة */
.modal {
    z-index: 99999 !important;
}

.modal-backdrop {
    z-index: 99998 !important;
}

.modal-dialog {
    z-index: 100000 !important;
}

.modal-content {
    z-index: 100001 !important;
    position: relative;
}

.modal-body {
    z-index: 100002 !important;
    position: relative;
}

.modal-body .table {
    z-index: 100003 !important;
    position: relative;
}

.modal-body .btn {
    z-index: 100004 !important;
    position: relative;
    pointer-events: auto !important;
}



@media (max-width: 768px) {
    .form-body {
        padding: 20px;
    }
    
    .table-toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .toolbar-group {
        justify-content: center;
    }
    
    .btn-action {
        margin: 5px 0;
        width: 100%;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="contract-form">
    <div class="container-fluid px-2">
        <div class="row">
            <div class="col-12">
                <div class="form-container">
                    <!-- رأس النموذج -->
                    <div class="form-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h1><i class="fas fa-file-contract me-3"></i>
                                    {% if edit_mode %}
                                        تعديل العقد رقم {{ contract.contract_id }}
                                    {% else %}
                                        إضافة عقد جديد
                                    {% endif %}
                                </h1>
                                <p>
                                    {% if edit_mode %}
                                        تعديل بيانات العقد وتفاصيله
                                    {% else %}
                                        تسجيل بيانات العقود مع الموردين وربطها بأوامر الشراء
                                    {% endif %}
                                </p>
                            </div>
                            <button type="button" class="btn btn-outline-light btn-sm" onclick="$('#diagnosticBar').toggle()">
                                <i class="fas fa-tools"></i> أدوات التشخيص
                            </button>
                        </div>
                    </div>

                    <!-- شريط التشخيص (مخفي افتراضياً) -->
                    <div id="diagnosticBar" class="alert alert-info mb-4" style="display: none;">
                        <div class="row">
                            <div class="col-md-8">
                                <strong>🔧 أدوات التشخيص:</strong>
                                <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="diagnoseSystem()">
                                    تشخيص النظام
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success ms-2" onclick="openSupplierSearch()">
                                    اختبار الموردين
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-info ms-2" onclick="openItemSearchModal()">
                                    اختبار الأصناف
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-warning ms-2" onclick="testSupplierSearch()">
                                    بيانات وهمية للموردين
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="closeAllModals()">
                                    إغلاق جميع النوافذ
                                </button>
                            </div>
                            <div class="col-md-4 text-end">
                                <small class="text-muted">اضغط F12 لفتح Console ومراقبة الرسائل</small>
                            </div>
                        </div>
                    </div>

                    <!-- نموذج البيانات -->
                    <form id="contractForm" method="POST"
                          action="{% if edit_mode %}{{ url_for('contracts.update_contract_with_table', contract_id=contract.contract_id) }}{% else %}{{ url_for('contracts.create_contract_with_table') }}{% endif %}">
                        <div class="form-body">
                            <!-- القسم الأول: البيانات الرئيسية -->
                            <div class="section-title">
                                <i class="fas fa-info-circle"></i>
                                البيانات الرئيسية
                            </div>

                            <div class="row">
                                <!-- رقم الفرع -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">رقم الفرع <span class="required">*</span></label>
                                        <select class="form-select" id="branch_id" name="branch_id" required>
                                            <option value="">اختر الفرع</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- رقم العقد -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">رقم العقد</label>
                                        <input type="text" class="form-control" id="contract_number" name="contract_number" 
                                               placeholder="سيتم توليده تلقائياً" readonly>
                                    </div>
                                </div>

                                <!-- التاريخ -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">التاريخ <span class="required">*</span></label>
                                        <input type="date" class="form-control" id="contract_date" name="contract_date" required>
                                    </div>
                                </div>

                                <!-- مجدد من عقد -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">مجدد من عقد</label>
                                        <input type="text" class="form-control" id="renewed_from" name="renewed_from" 
                                               placeholder="رقم العقد الأصلي" readonly>
                                    </div>
                                </div>

                                <!-- تاريخ بداية العقد -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">تاريخ بداية العقد <span class="required">*</span></label>
                                        <input type="date" class="form-control" id="start_date" name="start_date" required>
                                    </div>
                                </div>

                                <!-- تاريخ نهاية العقد -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">تاريخ نهاية العقد <span class="required">*</span></label>
                                        <input type="date" class="form-control" id="end_date" name="end_date" required>
                                    </div>
                                </div>

                                <!-- تاريخ انتهاء التمديد -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">تاريخ انتهاء التمديد</label>
                                        <input type="date" class="form-control" id="extension_end_date" name="extension_end_date" readonly>
                                    </div>
                                </div>

                                <!-- كود المورد -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">كود المورد <span class="required">*</span></label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="supplier_code" name="supplier_code"
                                                   placeholder="اضغط F9 للبحث" required>
                                            <button type="button" class="btn btn-search" onclick="openSupplierSearch()">
                                                <i class="fas fa-search"></i>
                                            </button>
                                        </div>
                                        <!-- حقل مخفي لـ supplier_id -->
                                        <input type="hidden" id="supplier_id" name="supplier_id">
                                    </div>
                                </div>

                                <!-- اسم المورد -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">اسم المورد <span class="required">*</span></label>
                                        <input type="text" class="form-control" id="name_ar" name="name_ar"
                                               placeholder="اسم المورد" readonly required>
                                    </div>
                                </div>

                                <!-- العملة -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">العملة</label>
                                        <select class="form-select" id="currency_code" name="currency_code" onchange="updateExchangeRate()">
                                            {% if currencies %}
                                                {% for currency in currencies %}
                                                    <option value="{{ currency.code }}"
                                                            data-rate="{{ currency.exchange_rate }}"
                                                            data-symbol="{{ currency.symbol }}"
                                                            {% if currency.is_base_currency %}selected{% endif %}>
                                                        {{ currency.name_ar }} ({{ currency.code }}) - {{ currency.symbol }}
                                                    </option>
                                                {% endfor %}
                                            {% else %}
                                                <!-- قائمة احتياطية في حالة عدم وجود عملات -->
                                                <option value="SAR" data-rate="1.0" data-symbol="ر.س" selected>ريال سعودي (SAR) - ر.س</option>
                                                <option value="USD" data-rate="3.75" data-symbol="$">دولار أمريكي (USD) - $</option>
                                                <option value="EUR" data-rate="4.1" data-symbol="€">يورو (EUR) - €</option>
                                                <option value="GBP" data-rate="4.7" data-symbol="£">جنيه إسترليني (GBP) - £</option>
                                                <option value="AED" data-rate="1.02" data-symbol="د.إ">درهم إماراتي (AED) - د.إ</option>
                                            {% endif %}
                                        </select>
                                    </div>
                                </div>

                                <!-- سعر التحويل -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">سعر التحويل</label>
                                        <input type="number" class="form-control" id="exchange_rate" name="exchange_rate"
                                               value="1" step="0.0001" min="0">
                                    </div>
                                </div>

                                <!-- البيان -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">البيان</label>
                                        <textarea class="form-control" id="description" name="description" rows="2"
                                                  placeholder="البيان التوضيحي للعقد"></textarea>
                                    </div>
                                </div>

                                <!-- المرجع -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">المرجع</label>
                                        <input type="text" class="form-control" id="reference_number" name="reference_number"
                                               placeholder="رقم المرجع أو الملف">
                                    </div>
                                </div>

                                <!-- مبلغ العقد -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">مبلغ العقد</label>
                                        <input type="number" class="form-control" id="contract_amount" name="contract_amount"
                                               step="0.001" min="0" placeholder="0.000">
                                    </div>
                                </div>

                                <!-- الخصم -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">الخصم</label>
                                        <input type="number" class="form-control" id="discount_amount" name="discount_amount"
                                               step="0.001" min="0" value="0" placeholder="0.000">
                                    </div>
                                </div>

                                <!-- صافي المبلغ -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">صافي المبلغ</label>
                                        <input type="number" class="form-control" id="net_amount" name="net_amount"
                                               step="0.001" min="0" readonly>
                                    </div>
                                </div>

                                <!-- مُستخدم -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <div class="form-check mt-4">
                                            <input class="form-check-input" type="checkbox" id="is_used" name="is_used" disabled>
                                            <label class="form-check-label" for="is_used">
                                                مُستخدم (يتم التأشير تلقائياً عند الاستخدام في أوامر الشراء)
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- حالة العقد -->
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">حالة العقد <span class="required">*</span></label>
                                        <select class="form-select" id="contract_status" name="contract_status" required>
                                            <option value="">اختر حالة العقد...</option>
                                            <!-- سيتم ملء الخيارات من API -->
                                        </select>
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            سيتم تحديد الحالة بناءً على مرحلة العقد
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- القسم الثاني: البيانات التفصيلية -->
                            <div class="section-title mt-4">
                                <i class="fas fa-table"></i>
                                البيانات التفصيلية - أصناف العقد
                            </div>

                            <!-- حاوي الجدول -->
                            <div class="table-container">
                                <!-- الجدول العادي -->
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="contractDetailsTable">
                                        <thead class="table-dark">
                                            <tr>
                                                <th width="160">كود الصنف</th>
                                                <th width="400">اسم الصنف</th>
                                                <th width="100">الوحدة</th>
                                                <th width="130">الكمية</th>
                                                <th width="130">ك. مجانية</th>
                                                <th width="140">سعر الوحدة</th>
                                                <th width="150">تاريخ الإنتاج</th>
                                                <th width="150">تاريخ الانتهاء</th>
                                                <th width="110">خصم %</th>
                                                <th width="150">الإجمالي</th>
                                                <th width="140">الكمية المنفذة</th>
                                                <th width="140">الكمية المتبقية</th>
                                                <th width="200">ملاحظات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="contractTableBody">
                                            <!-- سيتم إضافة الصفوف هنا بـ JavaScript -->
                                        </tbody>
                                    </table>
                                </div>

                                <!-- أزرار إدارة الجدول -->
                                <div class="table-controls mt-3">
                                    <button type="button" class="btn btn-primary btn-sm" onclick="addTableRow()">
                                        <i class="fas fa-plus"></i> إضافة صف
                                    </button>
                                    <button type="button" class="btn btn-success btn-sm" onclick="addMultipleRows()">
                                        <i class="fas fa-plus"></i> إضافة 5 صفوف
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm" onclick="removeLastRow()">
                                        <i class="fas fa-minus"></i> حذف آخر صف
                                    </button>
                                    <button type="button" class="btn btn-warning btn-sm" onclick="recalculateAllTotals()">
                                        <i class="fas fa-calculator"></i> إعادة حساب الإجماليات
                                    </button>
                                </div>

                                <!-- شريط الإحصائيات -->
                                <div class="statistics-bar">
                                    <div class="row text-center">
                                        <div class="col-6 col-md-3">
                                            <div class="stat-item">
                                                <div class="stat-value" id="totalRows">0</div>
                                                <div class="stat-label">إجمالي الصفوف</div>
                                            </div>
                                        </div>
                                        <div class="col-6 col-md-3">
                                            <div class="stat-item">
                                                <div class="stat-value" id="completedRows">0</div>
                                                <div class="stat-label">الصفوف المكتملة</div>
                                            </div>
                                        </div>
                                        <div class="col-6 col-md-3">
                                            <div class="stat-item">
                                                <div class="stat-value" id="totalQuantity">0.000</div>
                                                <div class="stat-label">إجمالي الكمية</div>
                                            </div>
                                        </div>
                                        <div class="col-6 col-md-3">
                                            <div class="stat-item">
                                                <div class="stat-value" id="totalAmount">0.000</div>
                                                <div class="stat-label">إجمالي المبلغ</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="action-buttons">
                            <button type="submit" class="btn btn-action btn-save" title="اضغط F10 للحفظ السريع">
                                <i class="fas fa-save me-2"></i>
                                {% if edit_mode %}
                                    تحديث العقد <small>(F10)</small>
                                {% else %}
                                    حفظ العقد <small>(F10)</small>
                                {% endif %}
                            </button>
                            <button type="button" class="btn btn-action btn-warning" onclick="forceSubmit()" title="حفظ بدون تحقق (للاختبار)">
                                <i class="fas fa-exclamation-triangle me-2"></i>حفظ مباشر
                            </button>
                            <button type="button" class="btn btn-action btn-cancel" onclick="cancelForm()">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </button>
                            <button type="button" class="btn btn-action btn-info" onclick="saveDraft()">
                                <i class="fas fa-file-alt me-2"></i>حفظ كمسودة
                            </button>
                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-keyboard me-1"></i>
                                    <strong>اختصارات المفاتيح:</strong> F9 للبحث | F10 للحفظ | Enter للانتقال للحقل التالي
                                </small>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>








<!-- نافذة البحث البسيطة للموردين -->
<div id="supplierSearchModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 10px; width: 90%; max-width: 800px; max-height: 80%; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
        <!-- رأس النافذة -->
        <div style="background: #28a745; color: white; padding: 15px; display: flex; justify-content: space-between; align-items: center;">
            <h3 style="margin: 0; font-size: 18px;">
                <i class="fas fa-search" style="margin-left: 10px;"></i>
                البحث في الموردين
            </h3>
            <button onclick="closeSupplierSearchModal()" style="background: none; border: none; color: white; font-size: 24px; cursor: pointer; padding: 0; width: 30px; height: 30px;">×</button>
        </div>

        <!-- محتوى النافذة -->
        <div style="padding: 20px; max-height: 500px; overflow-y: auto;">
            <!-- مربع البحث -->
            <div style="margin-bottom: 20px;">
                <div style="display: flex; gap: 10px;">
                    <input type="text" id="supplierSearchInput" placeholder="ابحث برقم المورد أو اسم المورد..."
                           style="flex: 1; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px;"
                           onkeypress="if(event.key==='Enter') performSupplierSearch()">
                    <button onclick="performSupplierSearch()"
                            style="background: #28a745; color: white; border: none; padding: 12px 20px; border-radius: 5px; cursor: pointer; font-size: 16px;">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
                <small style="color: #666; margin-top: 5px; display: block;">اضغط Enter للبحث السريع</small>
            </div>

            <!-- منطقة النتائج -->
            <div id="supplierSearchResults" style="border: 1px solid #ddd; border-radius: 5px; padding: 20px; min-height: 300px; background: #f9f9f9;">
                <div style="text-align: center; color: #666;">
                    <i class="fas fa-search" style="font-size: 48px; margin-bottom: 15px; color: #ccc;"></i>
                    <h4>ابدأ البحث للعثور على الموردين</h4>
                    <p>اكتب في مربع البحث أعلاه واضغط Enter</p>
                </div>
            </div>
        </div>

        <!-- أسفل النافذة -->
        <div style="background: #f8f9fa; padding: 15px; display: flex; justify-content: space-between; border-top: 1px solid #ddd;">
            <button onclick="testSupplierSearch()"
                    style="background: #ffc107; color: black; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                <i class="fas fa-flask"></i> اختبار
            </button>
            <button onclick="closeSupplierSearchModal()"
                    style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                <i class="fas fa-times"></i> إغلاق
            </button>
        </div>
    </div>
</div>

<!-- نافذة البحث البسيطة للأصناف -->
<div id="itemSearchModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 10px; width: 90%; max-width: 800px; max-height: 80%; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
        <!-- رأس النافذة -->
        <div style="background: #007bff; color: white; padding: 15px; display: flex; justify-content: space-between; align-items: center;">
            <h3 style="margin: 0; font-size: 18px;">
                <i class="fas fa-search" style="margin-left: 10px;"></i>
                البحث في الأصناف
            </h3>
            <button onclick="closeItemSearchModal()" style="background: none; border: none; color: white; font-size: 24px; cursor: pointer; padding: 0; width: 30px; height: 30px;">×</button>
        </div>

        <!-- محتوى النافذة -->
        <div style="padding: 20px; max-height: 500px; overflow-y: auto;">
            <!-- مربع البحث -->
            <div style="margin-bottom: 20px;">
                <div style="display: flex; gap: 10px;">
                    <input type="text" id="itemSearchInput" placeholder="ابحث برقم الصنف أو اسم الصنف..."
                           style="flex: 1; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px;"
                           onkeypress="if(event.key==='Enter') performItemSearch()">
                    <button onclick="performItemSearch()"
                            style="background: #007bff; color: white; border: none; padding: 12px 20px; border-radius: 5px; cursor: pointer; font-size: 16px;">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
                <small style="color: #666; margin-top: 5px; display: block;">اضغط Enter للبحث السريع</small>
            </div>

            <!-- منطقة النتائج -->
            <div id="itemSearchResults" style="border: 1px solid #ddd; border-radius: 5px; padding: 20px; min-height: 300px; background: #f9f9f9;">
                <div style="text-align: center; color: #666;">
                    <i class="fas fa-search" style="font-size: 48px; margin-bottom: 15px; color: #ccc;"></i>
                    <h4>ابدأ البحث للعثور على الأصناف</h4>
                    <p>اكتب في مربع البحث أعلاه واضغط Enter</p>
                </div>
            </div>
        </div>

        <!-- أسفل النافذة -->
        <div style="background: #f8f9fa; padding: 15px; display: flex; justify-content: space-between; border-top: 1px solid #ddd;">
            <button onclick="testItemSearch()"
                    style="background: #ffc107; color: black; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                <i class="fas fa-flask"></i> اختبار
            </button>
            <button onclick="closeItemSearchModal()"
                    style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                <i class="fas fa-times"></i> إغلاق
            </button>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}


<script>
// متغيرات عامة
let contractTable;
let selectedBranch = null;
let itemsCache = {};
let currentEditingRow = null;
let currentActiveInput = null; // المدخل النشط حالياً

// تحميل الصفحة
$(document).ready(function() {
    console.log('🚀 بدء تحميل صفحة العقد الجديد...');

    {% if edit_mode and contract %}
    // وضع التعديل - تعبئة البيانات الموجودة
    console.log('📝 وضع التعديل - تعبئة البيانات...');
    console.log('📊 بيانات العقد المستلمة (البيانات معكوسة في قاعدة البيانات):', {
        contract_id: '{{ contract.contract_id }}',
        branch_id: '{{ contract.branch_id }}',
        contract_date: '{{ contract.contract_date }}',
        start_date: '{{ contract.start_date }}',
        end_date: '{{ contract.end_date }}',
        supplier_id: '{{ contract.supplier_id }}',     // يحتوي على الاسم
        supplier_name: '{{ contract.supplier_name }}', // يحتوي على الكود
        currency_code: '{{ contract.currency_code }}',
        exchange_rate: '{{ contract.exchange_rate }}',
        reference_number: '{{ contract.reference_number }}',
        contract_amount: '{{ contract.contract_amount }}',
        description: '{{ contract.description }}'
    });
    console.log('⚠️ ملاحظة: supplier_id يحتوي على الاسم، supplier_name يحتوي على الكود');

    // تعبئة البيانات الأساسية مع تأخير للتأكد من تحميل العناصر
    setTimeout(() => {
        console.log('🔄 بدء تعبئة الحقول...');

        // تعبئة الفرع
        const branchSelect = $('#branch_id');
        if (branchSelect.length) {
            branchSelect.val('{{ contract.branch_id }}');
            console.log('🏢 تم تعبئة الفرع:', '{{ contract.branch_id }}');
        } else {
            console.log('❌ لم يتم العثور على حقل الفرع');
        }

        // تعبئة التواريخ
        $('#contract_date').val('{{ contract.contract_date }}');
        $('#start_date').val('{{ contract.start_date }}');
        $('#end_date').val('{{ contract.end_date }}');
        console.log('📅 تم تعبئة التواريخ');

        // تعبئة بيانات المورد (تصحيح البيانات المعكوسة لتظهر بشكل منطقي للمستخدم)
        // ملاحظة: البيانات معكوسة في قاعدة البيانات
        // contract.supplier_id يحتوي على الاسم
        // contract.supplier_name يحتوي على الكود
        // نعكسها لتظهر بشكل صحيح للمستخدم
        $('#supplier_code').val('{{ contract.supplier_name }}');  // عرض الكود في حقل الكود
        $('#supplier_id').val('{{ contract.supplier_name }}');    // حفظ الكود في الحقل المخفي
        $('#name_ar').val('{{ contract.supplier_id }}');          // عرض الاسم في حقل الاسم
        console.log('🏪 تم تعبئة بيانات المورد بشكل صحيح للمستخدم:');
        console.log('  - حقل الكود (supplier_code): {{ contract.supplier_name }}');
        console.log('  - الحقل المخفي (supplier_id): {{ contract.supplier_name }}');
        console.log('  - حقل الاسم (name_ar): {{ contract.supplier_id }}');

        // تعبئة باقي البيانات
        $('#currency_code').val('{{ contract.currency_code }}');
        $('#exchange_rate').val('{{ contract.exchange_rate }}');
        $('#reference_number').val('{{ contract.reference_number }}');
        $('#contract_amount').val('{{ contract.contract_amount }}');
        $('#description').val('{{ contract.description }}');

        console.log('✅ تم تعبئة جميع البيانات الأساسية');
    }, 1500); // تأخير أطول للتأكد من تحميل الفروع
    {% else %}
    // وضع الإنشاء - تعيين التاريخ الحالي
    const today = new Date().toISOString().split('T')[0];
    $('#contract_date').val(today);
    $('#start_date').val(today);
    {% endif %}

    // تحميل الفروع
    loadBranches();

    // تهيئة الجدول فوراً
    initializeContractTable();

    // إعداد مستمعي الأحداث
    setupEventListeners();

    {% if edit_mode and details %}
    // تعبئة تفاصيل العقد في وضع التعديل
    console.log('📦 تعبئة تفاصيل العقد...');

    setTimeout(() => {
        {% for detail in details %}
        // إضافة صف جديد
        addTableRow();
        const rowNumber = {{ loop.index }};

        // تعبئة بيانات الصف
        $(`input[name="item_code_${rowNumber}"]`).val('{{ detail[1] }}');
        $(`input[name="item_name_${rowNumber}"]`).val('{{ detail[2] }}');
        $(`input[name="quantity_${rowNumber}"]`).val('{{ detail[3] }}');
        $(`input[name="executed_quantity_${rowNumber}"]`).val('{{ detail[4] }}');
        $(`input[name="remaining_quantity_${rowNumber}"]`).val('{{ detail[5] }}');
        $(`input[name="free_quantity_${rowNumber}"]`).val('{{ detail[6] }}');
        $(`input[name="unit_${rowNumber}"]`).val('{{ detail[7] }}');
        $(`input[name="unit_price_${rowNumber}"]`).val('{{ detail[8] }}');
        $(`input[name="discount_percent_${rowNumber}"]`).val('{{ detail[9] }}');
        $(`input[name="total_${rowNumber}"]`).val('{{ detail[11] }}');
        {% if detail[12] %}
        $(`input[name="production_date_${rowNumber}"]`).val('{{ detail[12] }}');
        {% endif %}
        {% if detail[13] %}
        $(`input[name="expiry_date_${rowNumber}"]`).val('{{ detail[13] }}');
        {% endif %}

        console.log(`📦 تم تعبئة الصف ${rowNumber}: {{ detail[2] }}`);
        {% endfor %}

        // تحديث الإحصائيات
        updateContractStatistics();
        console.log('✅ تم تعبئة جميع التفاصيل');
    }, 1000);
    {% endif %}

    console.log('✅ تم تحميل الصفحة بنجاح');
});



// تحميل الفروع
function loadBranches() {
    $.ajax({
        url: '/contracts/api/branches',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const branchSelect = $('#branch_id');
                branchSelect.empty().append('<option value="">اختر الفرع</option>');

                response.branches.forEach(branch => {
                    branchSelect.append(`
                        <option value="${branch.branch_id}">${branch.branch_name}</option>
                    `);
                });
            }
        },
        error: function() {
            console.error('خطأ في تحميل الفروع');
        }
    });
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // تغيير الفرع
    $('#branch_id').change(function() {
        selectedBranch = $(this).val();
        if (selectedBranch) {
            console.log('تم اختيار الفرع:', selectedBranch);
        }
    });

    // حساب صافي المبلغ عند تغيير مبلغ العقد أو الخصم
    $('#contract_amount, #discount_amount').on('input change', function() {
        calculateNetAmount();
    });

    // تحديث مبلغ العقد والخصم عند تغيير أي حقل في التفاصيل
    $(document).on('input', 'input[name^="quantity_"], input[name^="unit_price_"], input[name^="discount_percent_"]', function() {
        const inputName = $(this).attr('name');
        const rowNumber = inputName.split('_')[1];

        // حساب إجمالي الصف
        calculateRowTotal(rowNumber);

        console.log('🔄 تم تغيير قيمة في الصف:', rowNumber, '- سيتم تحديث مبلغ العقد والخصم');
    });

    // اختصارات لوحة المفاتيح
    $(document).keydown(function(e) {
        // F9 للبحث (حسب نوع الحقل المركز عليه)
        if (e.keyCode === 120) { // F9
            e.preventDefault();
            console.log('🔑 تم الضغط على F9');

            // التحقق من العنصر النشط الحالي
            const activeElement = document.activeElement;
            console.log('العنصر النشط:', activeElement);
            console.log('اسم الحقل:', activeElement.name);
            console.log('ID الحقل:', activeElement.id);

            // تحديد نوع الحقل
            const isItemCodeField = activeElement &&
                                  activeElement.name &&
                                  activeElement.name.startsWith('item_code_');

            const isSupplierField = activeElement &&
                                  (activeElement.id === 'supplier_code' ||
                                   activeElement.name === 'supplier_code' ||
                                   activeElement.id === 'supplier_id' ||
                                   activeElement.name === 'supplier_id' ||
                                   activeElement.classList.contains('supplier-field'));

            console.log('هل هو حقل رقم صنف؟', isItemCodeField);
            console.log('هل هو حقل مورد؟', isSupplierField);

            if (isItemCodeField) {
                console.log('✅ فتح نافذة البحث للأصناف');
                // تحديث currentActiveInput
                currentActiveInput = activeElement;
                openItemSearchModal();
            } else if (isSupplierField) {
                console.log('✅ فتح نافذة البحث للموردين');
                if (typeof openSupplierSearch === 'function') {
                    openSupplierSearch();
                } else {
                    console.log('⚠️ دالة البحث عن الموردين غير متاحة');
                    showAlert('دالة البحث عن الموردين غير متاحة', 'warning');
                }
            } else {
                console.log('⚠️ الحقل النشط ليس حقل بحث معروف');
                showAlert('يرجى التركيز على حقل المورد أو رقم الصنف أولاً', 'info');
            }
        }

        // F10 للحفظ (بدلاً من Ctrl+S)
        if (e.keyCode === 121) { // F10
            e.preventDefault();
            console.log('💾 تم الضغط على F10 - حفظ العقد');
            $('#contractForm').submit();
        }

        // منع Enter من حفظ النموذج
        if (e.keyCode === 13 && !$(e.target).is('textarea')) {
            // السماح بـ Enter فقط في مربعات البحث
            if ($(e.target).is('#itemSearchInput, #supplierSearchInput')) {
                return true; // السماح بـ Enter في مربعات البحث
            }

            e.preventDefault();
            console.log('⚠️ تم منع Enter من حفظ النموذج - استخدم F10 للحفظ');

            // الانتقال للحقل التالي بدلاً من الحفظ
            const inputs = $('input:visible:enabled');
            const currentIndex = inputs.index(e.target);
            if (currentIndex >= 0 && currentIndex < inputs.length - 1) {
                inputs.eq(currentIndex + 1).focus();
            }
            return false;
        }
    });

// تم نقل دالة validateMainData إلى window.validateMainData أعلاه

    // تتبع المدخل النشط لحقول التفاصيل مع التحقق
    $(document).on('focus', 'input[name^="item_code_"], input[name^="quantity_"], input[name^="unit_price_"], input[name^="production_date_"], input[name^="expiry_date_"]', function() {
        // التحقق من البيانات الرئيسية قبل السماح بالإدخال في التفاصيل
        const validation = window.validateMainData();

        if (!validation.isValid) {
            // منع التركيز على الحقل
            this.blur();

            // إضافة تأثير بصري للحقل المطلوب
            $(`#${validation.fieldId}`).addClass('field-error');
            setTimeout(() => {
                $(`#${validation.fieldId}`).removeClass('field-error');
            }, 3000);

            // إظهار تنبيه
            showAlert(`يجب إدخال بيانات الحقل "${validation.missingField}" أولاً`, 'warning');

            // التركيز على الحقل المطلوب
            setTimeout(() => {
                $(`#${validation.fieldId}`).focus();
            }, 100);

            return false;
        }

        // إذا كان الحقل صالح، تحديث المتغير النشط
        if (this.name && this.name.startsWith('item_code_')) {
            currentActiveInput = this;
            console.log('🎯 تم التركيز على حقل رقم الصنف:', $(this).attr('name'));
        }
    });

    // إزالة التركيز عند الخروج من حقول رقم الصنف
    $(document).on('blur', 'input[name^="item_code_"]', function() {
        // لا نقوم بإزالة currentActiveInput فوراً للسماح بـ F9
        setTimeout(() => {
            if (document.activeElement !== this) {
                currentActiveInput = null;
            }
        }, 100);
    });

    // معالج Enter في نافذة البحث للأصناف
    $(document).on('keypress', '#itemSearchInput', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            performItemSearch();
        }
    });

    // معالج Enter في نافذة البحث للموردين
    $(document).on('keypress', '#supplierSearchInput', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            performSupplierSearch();
        }
    });

    // معالج Escape لإغلاق النوافذ
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllModals();
        }
    });

    // معالج النقر على backdrop لإغلاق النوافذ
    $(document).on('click', '.modal-backdrop', function() {
        closeAllModals();
    });

    // معالج النقر خارج النافذة
    $(document).on('click', '.modal', function(e) {
        if (e.target === this) {
            closeAllModals();
        }
    });
}

// تهيئة الجدول العادي
function initializeContractTable() {
    console.log('🔧 تهيئة الجدول العادي...');

    // إضافة 10 صفوف فارغة في البداية
    for (let i = 0; i < 10; i++) {
        addTableRow();
    }

    // تحديث الإحصائيات الأولية
    updateContractStatistics();

    console.log('✅ تم تهيئة الجدول بنجاح');
}

// إضافة صف جديد للجدول
function addTableRow() {
    const tbody = document.getElementById('contractTableBody');
    const rowCount = tbody.rows.length + 1;

    const row = tbody.insertRow();
    row.innerHTML = `
        <td>
            <input type="text" class="form-control form-control-sm item-code-input"
                   name="item_code_${rowCount}"
                   placeholder="كود الصنف"
                   onchange="fetchItemDetails(this, ${rowCount})"
                   onblur="fetchItemDetails(this, ${rowCount})">
        </td>
        <td><input type="text" class="form-control form-control-sm" name="item_name_${rowCount}" placeholder="اسم الصنف" readonly style="background-color: #f8f9fa;"></td>
        <td><input type="text" class="form-control form-control-sm" name="unit_${rowCount}" placeholder="الوحدة" readonly style="background-color: #f8f9fa;"></td>
        <td><input type="number" class="form-control form-control-sm" name="quantity_${rowCount}" placeholder="0" step="0.001" onchange="calculateRemainingQuantity(${rowCount}); calculateRowTotal(${rowCount}); updateContractStatistics()"></td>
        <td><input type="number" class="form-control form-control-sm" name="free_quantity_${rowCount}" placeholder="0" step="0.001"></td>
        <td><input type="number" class="form-control form-control-sm" name="unit_price_${rowCount}" placeholder="0.000" step="0.001" onchange="calculateRowTotal(${rowCount}); updateContractStatistics()"></td>
        <td><input type="date" class="form-control form-control-sm" name="production_date_${rowCount}"></td>
        <td><input type="date" class="form-control form-control-sm" name="expiry_date_${rowCount}"></td>
        <td><input type="number" class="form-control form-control-sm" name="discount_percent_${rowCount}" placeholder="0" step="0.01" onchange="calculateRowTotal(${rowCount}); updateContractStatistics()"></td>
        <td><input type="number" class="form-control form-control-sm" name="total_${rowCount}" placeholder="0.000" step="0.001" readonly style="background-color: #e8f5e8;" title="الإجمالي = (الكمية × السعر) - الخصم"></td>
        <td><input type="number" class="form-control form-control-sm" name="executed_quantity_${rowCount}" placeholder="0" step="0.001" readonly style="background-color: #e9ecef;" title="يتم تحديثها تلقائياً من أوامر الشراء"></td>
        <td><input type="number" class="form-control form-control-sm" name="remaining_quantity_${rowCount}" placeholder="0" step="0.001" readonly style="background-color: #fff3cd;" title="الكمية المتبقية = الكمية - الكمية المنفذة"></td>
        <td><input type="text" class="form-control form-control-sm" name="notes_${rowCount}" placeholder="ملاحظات"></td>
    `;

    // تحديث الإحصائيات بعد إضافة الصف
    updateContractStatistics();
}

// إضافة عدة صفوف
function addMultipleRows() {
    for (let i = 0; i < 5; i++) {
        addTableRow();
    }
}

// حذف آخر صف
function removeLastRow() {
    const tbody = document.getElementById('contractTableBody');
    if (tbody.rows.length > 0) {
        tbody.deleteRow(tbody.rows.length - 1);
        // تحديث الإحصائيات بعد حذف الصف
        updateContractStatistics();
    }
}

// حساب الكمية المتبقية
function calculateRemainingQuantity(rowNumber) {
    const quantityInput = document.querySelector(`input[name="quantity_${rowNumber}"]`);
    const executedQuantityInput = document.querySelector(`input[name="executed_quantity_${rowNumber}"]`);
    const remainingQuantityInput = document.querySelector(`input[name="remaining_quantity_${rowNumber}"]`);

    if (quantityInput && executedQuantityInput && remainingQuantityInput) {
        const quantity = parseFloat(quantityInput.value) || 0;
        const executedQuantity = parseFloat(executedQuantityInput.value) || 0;
        const remainingQuantity = quantity - executedQuantity;

        remainingQuantityInput.value = remainingQuantity.toFixed(3);

        // تغيير لون الخلفية حسب حالة التنفيذ
        if (remainingQuantity <= 0) {
            remainingQuantityInput.style.backgroundColor = '#d4edda'; // أخضر - مكتمل
            remainingQuantityInput.title = 'العقد مكتمل التنفيذ';
        } else if (executedQuantity > 0) {
            remainingQuantityInput.style.backgroundColor = '#fff3cd'; // أصفر - جزئي
            remainingQuantityInput.title = 'العقد منفذ جزئياً';
        } else {
            remainingQuantityInput.style.backgroundColor = '#f8d7da'; // أحمر - غير منفذ
            remainingQuantityInput.title = 'العقد غير منفذ';
        }
    }
}

// حساب إجمالي الصف
function calculateRowTotal(rowNumber) {
    const quantityInput = document.querySelector(`input[name="quantity_${rowNumber}"]`);
    const unitPriceInput = document.querySelector(`input[name="unit_price_${rowNumber}"]`);
    const discountPercentInput = document.querySelector(`input[name="discount_percent_${rowNumber}"]`);
    const totalInput = document.querySelector(`input[name="total_${rowNumber}"]`);

    if (quantityInput && unitPriceInput && totalInput) {
        const quantity = parseFloat(quantityInput.value) || 0;
        const unitPrice = parseFloat(unitPriceInput.value) || 0;
        const discountPercent = parseFloat(discountPercentInput.value) || 0;

        // حساب الإجمالي: (الكمية × السعر) - (الكمية × السعر × نسبة الخصم / 100)
        const subtotal = quantity * unitPrice;
        const discountAmount = subtotal * (discountPercent / 100);
        const total = subtotal - discountAmount;

        totalInput.value = total.toFixed(3);

        // تأثير بصري للإجمالي
        if (total > 0) {
            $(totalInput).removeClass('bg-warning-subtle').addClass('bg-success-subtle');
            setTimeout(() => {
                $(totalInput).removeClass('bg-success-subtle');
            }, 1000);
        } else {
            totalInput.style.backgroundColor = '#e8f5e8';
        }

        // تحديث الإحصائيات العامة
        updateContractStatistics();

        console.log(`💰 تم حساب إجمالي الصف ${rowNumber}:`, {
            quantity: quantity,
            unitPrice: unitPrice,
            discountPercent: discountPercent,
            subtotal: subtotal,
            discountAmount: discountAmount,
            total: total
        });

        // تحديث إحصائيات العقد ومبلغ العقد
        updateContractStats();
    }
}

// تحديث إحصائيات العقد العامة
function updateContractStatistics() {
    console.log('🔄 بدء تحديث إحصائيات العقد...');
    const tbody = document.getElementById('contractTableBody');
    if (!tbody) {
        console.log('❌ لم يتم العثور على جدول التفاصيل');
        return;
    }

    let totalRows = 0;
    let completedRows = 0;
    let totalQuantity = 0;
    let totalAmountBeforeDiscount = 0; // الإجمالي قبل الخصم
    let totalDiscount = 0;

    // حساب الإحصائيات من جميع الصفوف
    for (let i = 0; i < tbody.rows.length; i++) {
        const rowNumber = i + 1;

        // الحصول على القيم من الحقول
        const itemCodeInput = document.querySelector(`input[name="item_code_${rowNumber}"]`);
        const quantityInput = document.querySelector(`input[name="quantity_${rowNumber}"]`);
        const unitPriceInput = document.querySelector(`input[name="unit_price_${rowNumber}"]`);
        const discountPercentInput = document.querySelector(`input[name="discount_percent_${rowNumber}"]`);

        // إذا كان هناك كود صنف، احسب الصف
        if (itemCodeInput && itemCodeInput.value.trim()) {
            totalRows++;

            // إذا كان هناك كمية وسعر، احسبهم
            const quantity = parseFloat(quantityInput?.value) || 0;
            const unitPrice = parseFloat(unitPriceInput?.value) || 0;
            const discountPercent = parseFloat(discountPercentInput?.value) || 0;

            if (quantity > 0 && unitPrice > 0) {
                completedRows++;
                totalQuantity += quantity;

                // حساب الإجمالي قبل الخصم لهذا الصف
                const subtotal = quantity * unitPrice;
                totalAmountBeforeDiscount += subtotal;

                // حساب مبلغ الخصم لهذا الصف
                const discountAmount = subtotal * (discountPercent / 100);
                totalDiscount += discountAmount;

                console.log(`💰 حساب الصف ${rowNumber}:`, {
                    quantity: quantity,
                    unitPrice: unitPrice,
                    discountPercent: discountPercent,
                    subtotal: subtotal,
                    discountAmount: discountAmount,
                    totalAmountBeforeDiscount: totalAmountBeforeDiscount,
                    totalDiscount: totalDiscount
                });
            }
        }
    }

    // تحديث عناصر الإحصائيات في الواجهة
    const totalRowsElement = document.getElementById('totalRows');
    const completedRowsElement = document.getElementById('completedRows');
    const totalQuantityElement = document.getElementById('totalQuantity');
    const totalAmountElement = document.getElementById('totalAmount');

    if (totalRowsElement) totalRowsElement.textContent = totalRows;
    if (completedRowsElement) completedRowsElement.textContent = completedRows;
    if (totalQuantityElement) totalQuantityElement.textContent = totalQuantity.toFixed(3);
    if (totalAmountElement) totalAmountElement.textContent = (totalAmountBeforeDiscount - totalDiscount).toFixed(3);

    // تحديث مبلغ العقد تلقائياً (الإجمالي قبل الخصم)
    const contractAmountInput = document.getElementById('contract_amount');
    if (contractAmountInput) {
        const oldValue = parseFloat(contractAmountInput.value) || 0;
        const newValue = totalAmountBeforeDiscount; // الإجمالي قبل الخصم

        // تحديث القيمة فقط إذا تغيرت
        if (Math.abs(oldValue - newValue) > 0.001) {
            contractAmountInput.value = newValue.toFixed(3);

            // تأثير بصري لإظهار التحديث
            contractAmountInput.classList.add('contract-amount-updated');
            setTimeout(() => {
                contractAmountInput.classList.remove('contract-amount-updated');
            }, 1500);

            // إعادة حساب صافي المبلغ
            calculateNetAmount();

            console.log('💰 تم تحديث مبلغ العقد (قبل الخصم) تلقائياً:', {
                من: oldValue.toFixed(3),
                إلى: newValue.toFixed(3),
                ملاحظة: 'هذا هو الإجمالي قبل الخصم'
            });
        }
    }

    // تحديث حقل الخصم تلقائياً من إجمالي خصومات التفاصيل
    const discountAmountInput = document.getElementById('discount_amount');
    if (discountAmountInput) {
        const oldDiscountValue = parseFloat(discountAmountInput.value) || 0;
        const newDiscountValue = totalDiscount;

        // تحديث القيمة فقط إذا تغيرت
        if (Math.abs(oldDiscountValue - newDiscountValue) > 0.001) {
            discountAmountInput.value = newDiscountValue.toFixed(3);

            // تأثير بصري لإظهار التحديث
            discountAmountInput.classList.add('contract-amount-updated');
            setTimeout(() => {
                discountAmountInput.classList.remove('contract-amount-updated');
            }, 1500);

            // إعادة حساب صافي المبلغ
            calculateNetAmount();

            console.log('💸 تم تحديث مبلغ الخصم تلقائياً:', {
                من: oldDiscountValue.toFixed(3),
                إلى: newDiscountValue.toFixed(3)
            });
        }
    }

    console.log('📊 تحديث إحصائيات العقد:', {
        totalRows: totalRows,
        completedRows: completedRows,
        totalQuantity: totalQuantity,
        totalAmountBeforeDiscount: totalAmountBeforeDiscount,
        totalDiscount: totalDiscount,
        netAmount: totalAmountBeforeDiscount - totalDiscount
    });
}

// إعادة حساب جميع الإجماليات
function recalculateAllTotals() {
    const tbody = document.getElementById('contractTableBody');
    if (!tbody) return;

    let recalculatedCount = 0;

    // إعادة حساب كل صف
    for (let i = 0; i < tbody.rows.length; i++) {
        const rowNumber = i + 1;

        // التحقق من وجود بيانات في الصف
        const itemCodeInput = document.querySelector(`input[name="item_code_${rowNumber}"]`);
        const quantityInput = document.querySelector(`input[name="quantity_${rowNumber}"]`);
        const unitPriceInput = document.querySelector(`input[name="unit_price_${rowNumber}"]`);

        if (itemCodeInput && itemCodeInput.value.trim() &&
            quantityInput && parseFloat(quantityInput.value) > 0 &&
            unitPriceInput && parseFloat(unitPriceInput.value) > 0) {

            // إعادة حساب الكمية المتبقية والإجمالي
            calculateRemainingQuantity(rowNumber);
            calculateRowTotal(rowNumber);
            recalculatedCount++;
        }
    }

    // تحديث الإحصائيات العامة
    updateContractStatistics();

    // إظهار رسالة نجاح
    showAlert(`تم إعادة حساب ${recalculatedCount} صف بنجاح`, 'success');

    console.log(`🔄 تم إعادة حساب ${recalculatedCount} صف`);
}

// دالة اختبار لتحديث الخصم يدوياً
function testDiscountUpdate() {
    console.log('🧪 اختبار تحديث الخصم...');
    updateContractStatistics();
}

// إضافة الدالة للنافذة العامة للاختبار
window.testDiscountUpdate = testDiscountUpdate;

// دالة الحفظ المباشر بدون تحقق (للاختبار)
function forceSubmit() {
    console.log('🚨 حفظ مباشر بدون تحقق...');

    // تجاوز التحقق وإرسال النموذج مباشرة
    const form = document.getElementById('contractForm');

    // إزالة معالج الإرسال المخصص مؤقتاً
    $(form).off('submit');

    // إرسال النموذج
    form.submit();
}

// دالة التحقق من البيانات الرئيسية المطلوبة (نسخة عامة)
window.validateMainData = function() {
    console.log('🔍 التحقق من البيانات الرئيسية...');

    const requiredFields = [
        { id: 'branch_id', name: 'الفرع' },
        { id: 'contract_date', name: 'تاريخ العقد' },
        { id: 'start_date', name: 'تاريخ البداية' },
        { id: 'end_date', name: 'تاريخ النهاية' },
        { id: 'supplier_id', name: 'المورد' }
    ];

    for (let field of requiredFields) {
        const element = document.getElementById(field.id);
        const value = element ? element.value : '';
        console.log(`  📋 ${field.name}: "${value}"`);

        if (!value || value.trim() === '') {
            console.log(`  ❌ الحقل "${field.name}" مطلوب`);
            return {
                isValid: false,
                missingField: field.name,
                fieldId: field.id
            };
        }
    }

    console.log('  ✅ جميع البيانات الرئيسية مكتملة');
    return { isValid: true };
};

// دالة التحقق من صحة بيانات التفاصيل (نسخة عامة)
window.validateContractDetails = function() {
    console.log('🔍 بدء التحقق من تفاصيل العقد...');

    const errors = [];
    let hasValidItems = false;

    // البحث عن جميع حقول الأصناف
    const itemCodeInputs = document.querySelectorAll('input[name^="item_code_"]');
    console.log(`📦 عدد حقول الأصناف الموجودة: ${itemCodeInputs.length}`);

    itemCodeInputs.forEach((itemCodeInput, index) => {
        const rowNumber = itemCodeInput.name.replace('item_code_', '');
        const itemCode = itemCodeInput.value ? itemCodeInput.value.trim() : '';

        console.log(`🔍 فحص الصف ${rowNumber}: كود الصنف = "${itemCode}"`);

        // إذا كان هناك كود صنف، تحقق من باقي البيانات المطلوبة
        if (itemCode && itemCode !== '') {
            hasValidItems = true;
            const rowErrors = [];

            // التحقق من الكمية
            const quantityInput = document.querySelector(`input[name="quantity_${rowNumber}"]`);
            const quantity = quantityInput ? parseFloat(quantityInput.value) || 0 : 0;
            console.log(`  📊 الكمية: ${quantity}`);
            if (quantity <= 0) {
                rowErrors.push('الكمية');
            }

            // التحقق من سعر الوحدة
            const unitPriceInput = document.querySelector(`input[name="unit_price_${rowNumber}"]`);
            const unitPrice = unitPriceInput ? parseFloat(unitPriceInput.value) || 0 : 0;
            console.log(`  💰 سعر الوحدة: ${unitPrice}`);
            if (unitPrice <= 0) {
                rowErrors.push('سعر الوحدة');
            }

            // إضافة أخطاء الصف إذا وجدت
            if (rowErrors.length > 0) {
                errors.push(`الصف ${rowNumber} (${itemCode}): يجب إدخال ${rowErrors.join(', ')}`);
                console.log(`  ❌ أخطاء في الصف ${rowNumber}: ${rowErrors.join(', ')}`);
            } else {
                console.log(`  ✅ الصف ${rowNumber} صحيح`);
            }
        }
    });

    // التحقق من وجود أصناف صالحة
    if (!hasValidItems) {
        errors.push('يجب إضافة صنف واحد على الأقل مع البيانات المطلوبة');
        console.log('❌ لا توجد أصناف صالحة');
    } else {
        console.log(`✅ تم العثور على أصناف صالحة`);
    }

    const result = {
        isValid: errors.length === 0,
        errors: errors
    };

    console.log('📋 نتيجة التحقق من التفاصيل:', result);
    return result;
};

// دالة حساب صافي المبلغ
function calculateNetAmount() {
    const contractAmountInput = document.getElementById('contract_amount');
    const discountAmountInput = document.getElementById('discount_amount');
    const netAmountInput = document.getElementById('net_amount');

    if (contractAmountInput && discountAmountInput && netAmountInput) {
        const contractAmount = parseFloat(contractAmountInput.value) || 0;
        const discountAmount = parseFloat(discountAmountInput.value) || 0;
        const netAmount = contractAmount - discountAmount;

        netAmountInput.value = netAmount.toFixed(3);

        console.log('💰 تم حساب صافي المبلغ:', {
            contractAmount: contractAmount,
            discountAmount: discountAmount,
            netAmount: netAmount
        });
    }
}

// تحديث الكمية المنفذة (يتم استدعاؤها من أوامر الشراء)
function updateExecutedQuantity(itemCode, executedQuantity) {
    const tbody = document.getElementById('contractTableBody');
    const rows = tbody.rows;

    for (let i = 0; i < rows.length; i++) {
        const codeInput = rows[i].querySelector(`input[name="item_code_${i + 1}"]`);
        if (codeInput && codeInput.value === itemCode) {
            const executedInput = rows[i].querySelector(`input[name="executed_quantity_${i + 1}"]`);
            if (executedInput) {
                executedInput.value = executedQuantity.toFixed(3);
                calculateRemainingQuantity(i + 1);
                break;
            }
        }
    }
}



// معالجة تغييرات الخلايا
function handleCellChanges(changes) {
    console.log('تم تغيير البيانات:', changes);
    // هنا يمكن إضافة منطق حساب الإجماليات
}
// تحديث الإحصائيات
function updateStatistics() {
    console.log('تحديث الإحصائيات...');
}

// ==================== دوال البحث الاحترافية للموردين ====================

// فتح نافذة البحث للموردين - يعمل من أي مكان
function openSupplierSearch() {
    console.log('🔍 فتح نافذة البحث للموردين...');

    // إغلاق أي نوافذ مفتوحة
    closeAllModals();

    // فتح النافذة
    const modal = document.getElementById('supplierSearchModal');
    if (modal) {
        modal.style.display = 'block';

        // مسح البحث السابق
        document.getElementById('supplierSearchInput').value = '';

        // تحميل جميع الموردين مباشرة عند فتح النافذة
        loadAllSuppliers();

        // التركيز على مربع البحث
        setTimeout(() => {
            document.getElementById('supplierSearchInput').focus();
        }, 100);

        console.log('✅ تم فتح نافذة البحث للموردين');
    } else {
        alert('نافذة البحث غير موجودة!');
    }
}

// إعادة تعيين نتائج البحث للموردين
function resetSupplierSearchResults() {
    document.getElementById('supplierSearchResults').innerHTML = `
        <div style="text-align: center; color: #666;">
            <i class="fas fa-search" style="font-size: 48px; margin-bottom: 15px; color: #ccc;"></i>
            <h4>ابدأ البحث للعثور على الموردين</h4>
            <p>اكتب في مربع البحث أعلاه واضغط Enter</p>
        </div>
    `;
}

// تحميل جميع الموردين عند فتح النافذة
function loadAllSuppliers() {
    console.log('📋 تحميل جميع الموردين...');

    // إظهار مؤشر التحميل
    document.getElementById('supplierSearchResults').innerHTML = `
        <div style="text-align: center; padding: 40px; color: #28a745;">
            <div style="border: 4px solid #f3f3f3; border-top: 4px solid #28a745; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <h4>جاري تحميل الموردين...</h4>
            <p>يرجى الانتظار</p>
        </div>
    `;

    // تحميل الموردين من API
    $.ajax({
        url: '/contracts/api/suppliers/search',
        method: 'GET',
        data: {
            search_term: '' // بحث فارغ لجلب جميع الموردين
        },
        timeout: 30000,
        success: function(response) {
            console.log('📥 تم تحميل الموردين:', response);

            // التعامل مع أشكال مختلفة من الاستجابة
            let suppliers = [];

            if (response.success && response.suppliers) {
                suppliers = response.suppliers;
            } else if (Array.isArray(response)) {
                suppliers = response;
            } else if (response.data && Array.isArray(response.data)) {
                suppliers = response.data;
            } else if (response.results && Array.isArray(response.results)) {
                suppliers = response.results;
            }

            if (suppliers.length > 0) {
                console.log(`✅ تم تحميل ${suppliers.length} مورد`);
                displaySupplierSearchResults(suppliers);
            } else {
                console.log('⚠️ لا توجد موردين في قاعدة البيانات');
                document.getElementById('supplierSearchResults').innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #856404; background: #fff3cd; border-radius: 5px;">
                        <i class="fas fa-info-circle" style="font-size: 48px; margin-bottom: 15px;"></i>
                        <h4>لا توجد موردين في قاعدة البيانات</h4>
                        <p>يرجى إضافة موردين أولاً أو التحقق من الاتصال بقاعدة البيانات</p>
                    </div>
                `;
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ خطأ في تحميل الموردين:', error);
            document.getElementById('supplierSearchResults').innerHTML = `
                <div style="text-align: center; padding: 40px; color: #721c24; background: #f8d7da; border-radius: 5px;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 15px;"></i>
                    <h4>خطأ في تحميل الموردين</h4>
                    <p>تعذر الاتصال بقاعدة البيانات</p>
                    <button onclick="loadAllSuppliers()"
                            style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 15px;">
                        <i class="fas fa-redo"></i> إعادة المحاولة
                    </button>
                </div>
            `;
        }
    });
}

// تنفيذ البحث في الموردين
function performSupplierSearch() {
    console.log('🔍 بدء تنفيذ البحث في الموردين...');

    const searchTerm = $('#supplierSearchInput').val().trim();
    console.log('مصطلح البحث:', searchTerm);

    if (!searchTerm) {
        showAlert('يرجى إدخال كلمة البحث', 'warning');
        $('#supplierSearchInput').focus();
        return;
    }

    // إظهار مؤشر التحميل
    document.getElementById('supplierSearchResults').innerHTML = `
        <div style="text-align: center; padding: 40px; color: #28a745;">
            <div style="border: 4px solid #f3f3f3; border-top: 4px solid #28a745; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <h4>جاري البحث في قاعدة البيانات...</h4>
            <p>البحث عن: "${searchTerm}"</p>
        </div>
        <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        </style>
    `;

    // تنفيذ البحث باستخدام API الصحيح
    $.ajax({
        url: '/contracts/api/suppliers/search',
        method: 'GET',
        data: {
            search_term: searchTerm
        },
        timeout: 30000,
        beforeSend: function(xhr) {
            console.log('📤 إرسال طلب البحث في الموردين...');
        },
        success: function(response) {
            console.log('📥 استلام رد من الخادم:', response);

            // التعامل مع أشكال مختلفة من الاستجابة
            let suppliers = [];

            if (response.success && response.suppliers) {
                suppliers = response.suppliers;
            } else if (Array.isArray(response)) {
                suppliers = response;
            } else if (response.data && Array.isArray(response.data)) {
                suppliers = response.data;
            } else if (response.results && Array.isArray(response.results)) {
                suppliers = response.results;
            }

            if (suppliers.length > 0) {
                console.log(`✅ تم العثور على ${suppliers.length} مورد`);
                displaySupplierSearchResults(suppliers);
            } else {
                console.log('⚠️ لم يتم العثور على موردين');
                displayNoSupplierResults(searchTerm);
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ خطأ في طلب البحث:', {
                status: status,
                error: error,
                responseText: xhr.responseText,
                statusCode: xhr.status
            });

            let errorMessage = 'خطأ في الاتصال بالخادم';
            if (xhr.status === 404) {
                errorMessage = 'API البحث غير موجود';
            } else if (xhr.status === 500) {
                errorMessage = 'خطأ في الخادم';
            } else if (status === 'timeout') {
                errorMessage = 'انتهت مهلة الاتصال';
            }

            displaySupplierSearchError(errorMessage);
        }
    });

    console.log('🔍 تم إرسال طلب البحث عن:', searchTerm);
}

// عرض نتائج البحث للموردين بشكل بسيط وأنيق
function displaySupplierSearchResults(suppliers) {
    let html = `
        <div style="margin-bottom: 15px; padding: 10px; background: #d4edda; border-radius: 5px;">
            <h4 style="margin: 0; color: #155724;">
                <i class="fas fa-check-circle"></i> تم العثور على ${suppliers.length} مورد
            </h4>
            <small style="color: #155724;">انقر على "اختيار" أو انقر مرتين على الصف</small>
        </div>
        <div style="max-height: 300px; overflow-y: auto;">
            <table style="width: 100%; border-collapse: collapse; background: white;">
                <thead>
                    <tr style="background: #28a745; color: white;">
                        <th style="padding: 10px; border: 1px solid #ddd;">رقم المورد</th>
                        <th style="padding: 10px; border: 1px solid #ddd;">اسم المورد</th>
                        <th style="padding: 10px; border: 1px solid #ddd;">الهاتف</th>
                        <th style="padding: 10px; border: 1px solid #ddd;">الإجراء</th>
                    </tr>
                </thead>
                <tbody>
    `;

    suppliers.forEach((supplier, index) => {
        // التعامل مع أسماء الحقول المختلفة من APIs مختلفة
        const supplierCode = supplier.supplier_code || supplier.code || supplier.id || `SUP${index + 1}`;
        const supplierName = supplier.supplier_name || supplier.name_ar || supplier.name || 'مورد غير محدد';
        const supplierPhone = supplier.phone || supplier.telephone || 'غير محدد';

        html += `
            <tr style="cursor: pointer; border-bottom: 1px solid #ddd;"
                ondblclick="selectSupplierFromSearch('${supplierCode}', '${supplierName}', '${supplierPhone}')"
                onmouseover="this.style.backgroundColor='#e8f5e8'"
                onmouseout="this.style.backgroundColor='white'">
                <td style="padding: 8px; border: 1px solid #ddd;">
                    <strong style="color: #28a745;">${supplierCode}</strong>
                </td>
                <td style="padding: 8px; border: 1px solid #ddd;">${supplierName}</td>
                <td style="padding: 8px; border: 1px solid #ddd;">
                    <span style="background: #6c757d; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">
                        ${supplierPhone}
                    </span>
                </td>
                <td style="padding: 8px; border: 1px solid #ddd;">
                    <button onclick="selectSupplierFromSearch('${supplierCode}', '${supplierName}', '${supplierPhone}')"
                            style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; font-size: 12px;">
                        <i class="fas fa-check"></i> اختيار
                    </button>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
        <div style="text-align: center; margin-top: 10px; color: #666; font-size: 12px;">
            <i class="fas fa-info-circle"></i>
            يمكنك النقر مرتين على أي صف لاختيار المورد مباشرة
        </div>
    `;

    document.getElementById('supplierSearchResults').innerHTML = html;
}

// عرض رسالة عدم وجود نتائج للموردين
function displayNoSupplierResults(searchTerm) {
    document.getElementById('supplierSearchResults').innerHTML = `
        <div style="text-align: center; padding: 40px; color: #856404; background: #fff3cd; border-radius: 5px;">
            <i class="fas fa-search-minus" style="font-size: 48px; margin-bottom: 15px;"></i>
            <h4>لم يتم العثور على موردين</h4>
            <p>لم يتم العثور على موردين يحتوون على: "<strong>${searchTerm}</strong>"</p>
            <button onclick="document.getElementById('supplierSearchInput').focus()"
                    style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 10px;">
                <i class="fas fa-search"></i> جرب البحث مرة أخرى
            </button>
        </div>
    `;
}

// عرض رسالة خطأ في البحث للموردين
function displaySupplierSearchError(error) {
    document.getElementById('supplierSearchResults').innerHTML = `
        <div style="text-align: center; padding: 40px; color: #721c24; background: #f8d7da; border-radius: 5px;">
            <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 15px;"></i>
            <h4>خطأ في البحث</h4>
            <p>حدث خطأ أثناء البحث في قاعدة البيانات</p>
            <small>تفاصيل الخطأ: ${error}</small>
            <br>
            <button onclick="performSupplierSearch()"
                    style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 15px;">
                <i class="fas fa-redo"></i> إعادة المحاولة
            </button>
        </div>
    `;
}

// اختيار مورد من نتائج البحث
function selectSupplierFromSearch(supplierCode, supplierName, supplierPhone) {
    console.log('✅ اختيار مورد:', {
        code: supplierCode,
        name: supplierName,
        phone: supplierPhone
    });

    // ملء الحقول
    // SUPPLIER_ID في جدول العقود = SUPPLIER_CODE من جدول الموردين
    const supplierIdInput = document.getElementById('supplier_id');
    const supplierCodeInput = document.getElementById('supplier_code');
    const supplierNameInput = document.getElementById('name_ar');

    if (supplierIdInput) {
        // حفظ SUPPLIER_CODE في حقل SUPPLIER_ID (هذا ما يتوقعه جدول العقود)
        supplierIdInput.value = supplierCode;
        console.log('✅ تم ملء supplier_id بقيمة supplier_code:', supplierCode);
    }

    if (supplierCodeInput) {
        supplierCodeInput.value = supplierCode;
        // تأثير بصري للنجاح
        $(supplierCodeInput).removeClass('is-invalid').addClass('is-valid');
        setTimeout(() => $(supplierCodeInput).removeClass('is-valid'), 2000);
    }

    if (supplierNameInput) {
        supplierNameInput.value = supplierName;
        $(supplierNameInput).addClass('bg-success-subtle');
        setTimeout(() => $(supplierNameInput).removeClass('bg-success-subtle'), 2000);
    }

    // إغلاق النافذة
    closeSupplierSearchModal();

    // إظهار رسالة نجاح
    showAlert(`تم اختيار المورد: ${supplierName}`, 'success');

    // التركيز على الحقل التالي
    setTimeout(() => {
        const nextField = document.getElementById('contract_date');
        if (nextField) {
            nextField.focus();
        }
    }, 300);

    console.log('✅ تم اختيار المورد بنجاح');
}

// فتح نافذة البحث للأصناف من زر البحث في الجدول
function openItemSearchFromTable(rowIndex) {
    console.log('🔍 فتح نافذة البحث للأصناف من الجدول، الصف:', rowIndex);

    // تعيين الصف المطلوب
    window.targetRowIndex = rowIndex;

    // فتح النافذة
    openItemSearchModal();
}

// دالة اختبار لعرض بيانات وهمية للموردين
function testSupplierSearch() {
    console.log('🧪 تشغيل اختبار البحث للموردين...');

    // فتح النافذة أولاً إذا لم تكن مفتوحة
    const modal = document.getElementById('supplierSearchModal');
    if (!modal || modal.style.display === 'none') {
        openSupplierSearch();
        setTimeout(() => {
            testSupplierSearch();
        }, 200);
        return;
    }

    const testSuppliers = [
        {
            supplier_code: 'SUP001',
            supplier_name: 'شركة الأمل للتجارة',
            name_ar: 'شركة الأمل للتجارة',
            phone: '123456789'
        },
        {
            supplier_code: 'SUP002',
            supplier_name: 'مؤسسة النور التجارية',
            name_ar: 'مؤسسة النور التجارية',
            phone: '987654321'
        },
        {
            supplier_code: 'SUP003',
            supplier_name: 'شركة الفجر للمواد',
            name_ar: 'شركة الفجر للمواد',
            phone: '555666777'
        }
    ];

    console.log('عرض البيانات التجريبية للموردين...');
    displaySupplierSearchResults(testSuppliers);
}

// الطريقة الثانية: جلب تفاصيل الصنف تلقائياً (التعبئة المباشرة)
function fetchItemDetails(input, rowNumber) {
    const itemCode = input.value.trim();

    if (!itemCode) {
        clearItemDetails(rowNumber);
        return;
    }

    // التحقق من وجود الفرع
    if (!selectedBranch) {
        showAlert('يرجى اختيار الفرع أولاً', 'warning');
        clearItemDetails(rowNumber);
        return;
    }

    const itemNameInput = document.querySelector(`input[name="item_name_${rowNumber}"]`);
    const unitInput = document.querySelector(`input[name="unit_${rowNumber}"]`);

    // إظهار مؤشر التحميل الأنيق
    if (itemNameInput) {
        itemNameInput.value = '🔍 جاري البحث...';
        itemNameInput.style.background = '#e3f2fd';
    }
    if (unitInput) {
        unitInput.value = '⏳';
        unitInput.style.background = '#e3f2fd';
    }

    // إضافة مؤشر تحميل على الحقل نفسه
    $(input).addClass('border-primary');

    // البحث في قاعدة البيانات
    $.ajax({
        url: '/contracts/api/items/details',
        method: 'GET',
        data: {
            item_code: itemCode,
            branch_id: selectedBranch
        },
        success: function(response) {
            if (response.success && response.item) {
                // ملء تفاصيل الصنف بتأثيرات بصرية محسنة
                if (itemNameInput) {
                    itemNameInput.value = response.item.item_name;
                    $(itemNameInput).removeClass('is-invalid').addClass('is-valid bg-success-subtle');
                    setTimeout(() => {
                        $(itemNameInput).removeClass('is-valid bg-success-subtle');
                        itemNameInput.style.background = '#f8f9fa';
                    }, 2000);
                }

                if (unitInput) {
                    unitInput.value = response.item.unit_name || 'قطعة';
                    $(unitInput).addClass('bg-success-subtle');
                    setTimeout(() => {
                        $(unitInput).removeClass('bg-success-subtle');
                        unitInput.style.background = '#f8f9fa';
                    }, 2000);
                }

                // تأثير بصري للنجاح على حقل الكود
                $(input).removeClass('border-primary is-invalid').addClass('is-valid');
                setTimeout(() => {
                    $(input).removeClass('is-valid');
                }, 2000);

                // حفظ في الكاش
                itemsCache[itemCode] = {
                    item_code: itemCode,
                    item_name: response.item.item_name,
                    unit_name: response.item.unit_name || 'قطعة'
                };

                // التركيز على الحقل التالي (الكمية)
                setTimeout(() => {
                    const quantityInput = document.querySelector(`input[name="quantity_${rowNumber}"]`);
                    if (quantityInput) {
                        quantityInput.focus();
                    }
                }, 500);

                // تحديث الإحصائيات بعد جلب تفاصيل الصنف
                updateContractStatistics();

                console.log('✅ تم جلب تفاصيل الصنف تلقائياً:', response.item);
            } else {
                // الصنف غير موجود
                if (itemNameInput) {
                    itemNameInput.value = '❌ صنف غير موجود';
                    $(itemNameInput).addClass('is-invalid bg-danger-subtle');
                }
                if (unitInput) {
                    unitInput.value = '-';
                    $(unitInput).addClass('bg-danger-subtle');
                }

                $(input).removeClass('border-primary is-valid').addClass('is-invalid');

                // إظهار تنبيه
                showAlert(`الصنف "${itemCode}" غير موجود في قاعدة البيانات`, 'warning');

                console.log('❌ الصنف غير موجود:', itemCode);
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ خطأ في جلب تفاصيل الصنف:', error);

            if (itemNameInput) {
                itemNameInput.value = '⚠️ خطأ في التحميل';
                $(itemNameInput).addClass('is-invalid bg-danger-subtle');
            }
            if (unitInput) {
                unitInput.value = '-';
                $(unitInput).addClass('bg-danger-subtle');
            }

            $(input).removeClass('border-primary is-valid').addClass('is-invalid');

            // إظهار تنبيه خطأ
            showAlert('حدث خطأ أثناء البحث عن الصنف', 'error');
        }
    });
}

// مسح تفاصيل الصنف
function clearItemDetails(rowNumber) {
    const itemCodeInput = document.querySelector(`input[name="item_code_${rowNumber}"]`);
    const itemNameInput = document.querySelector(`input[name="item_name_${rowNumber}"]`);
    const unitInput = document.querySelector(`input[name="unit_${rowNumber}"]`);

    if (itemCodeInput) {
        $(itemCodeInput).removeClass('is-valid is-invalid border-primary');
    }
    if (itemNameInput) {
        itemNameInput.value = '';
        $(itemNameInput).removeClass('is-valid is-invalid bg-success-subtle bg-danger-subtle');
        itemNameInput.style.background = '#f8f9fa';
    }
    if (unitInput) {
        unitInput.value = '';
        $(unitInput).removeClass('bg-success-subtle bg-danger-subtle');
        unitInput.style.background = '#f8f9fa';
    }
}

// ==================== دوال البحث الاحترافية للأصناف ====================

// فتح نافذة البحث للأصناف - يعمل من أي مكان
function openItemSearchModal() {
    console.log('🔍 فتح نافذة البحث للأصناف...');

    // التحقق من الفرع فقط (اختياري)
    if (!selectedBranch) {
        console.log('⚠️ لم يتم اختيار فرع، سيتم استخدام الفرع الافتراضي');
        // يمكن تعيين فرع افتراضي هنا إذا لزم الأمر
    }

    // تحديد العنصر النشط (إذا كان في حقل صنف)
    const activeElement = document.activeElement;
    const isItemCodeField = activeElement &&
                          activeElement.name &&
                          activeElement.name.startsWith('item_code_');

    if (isItemCodeField) {
        currentActiveInput = activeElement;
        console.log('✅ تم تحديد حقل الصنف النشط');
    } else {
        console.log('⚠️ لم يتم التركيز على حقل صنف، سيتم البحث العام');
        currentActiveInput = null;
    }

    // إغلاق أي نوافذ مفتوحة
    closeAllModals();

    // فتح النافذة
    const modal = document.getElementById('itemSearchModal');
    if (modal) {
        modal.style.display = 'block';

        // مسح البحث السابق
        document.getElementById('itemSearchInput').value = '';

        // تحميل جميع الأصناف مباشرة عند فتح النافذة
        loadAllItems();

        // التركيز على مربع البحث
        setTimeout(() => {
            document.getElementById('itemSearchInput').focus();
        }, 100);

        console.log('✅ تم فتح نافذة البحث للأصناف');
    } else {
        alert('نافذة البحث غير موجودة!');
    }
}

// إعادة تعيين نتائج البحث للأصناف
function resetSearchResults() {
    document.getElementById('itemSearchResults').innerHTML = `
        <div style="text-align: center; color: #666;">
            <i class="fas fa-search" style="font-size: 48px; margin-bottom: 15px; color: #ccc;"></i>
            <h4>ابدأ البحث للعثور على الأصناف</h4>
            <p>اكتب في مربع البحث أعلاه واضغط Enter</p>
        </div>
    `;
}

// تحميل جميع الأصناف عند فتح النافذة
function loadAllItems() {
    console.log('📋 تحميل جميع الأصناف...');

    // التحقق من الفرع المختار
    const branchId = selectedBranch || '1';
    console.log('🏢 الفرع المختار:', branchId);

    // إظهار مؤشر التحميل
    document.getElementById('itemSearchResults').innerHTML = `
        <div style="text-align: center; padding: 40px; color: #007bff;">
            <div style="border: 4px solid #f3f3f3; border-top: 4px solid #007bff; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <h4>جاري تحميل الأصناف...</h4>
            <p>الفرع: ${branchId}</p>
        </div>
    `;

    // تحميل الأصناف من API
    $.ajax({
        url: '/contracts/api/items/search',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            branch_id: selectedBranch || '1', // استخدام الفرع المختار أو الافتراضي
            search_term: '' // بحث فارغ لجلب جميع الأصناف
        }),
        timeout: 30000,
        success: function(response) {
            console.log('📥 تم تحميل الأصناف:', response);

            // التعامل مع أشكال مختلفة من الاستجابة
            let items = [];

            if (response.success && response.items) {
                items = response.items;
            } else if (Array.isArray(response)) {
                items = response;
            } else if (response.data && Array.isArray(response.data)) {
                items = response.data;
            } else if (response.results && Array.isArray(response.results)) {
                items = response.results;
            }

            if (items.length > 0) {
                console.log(`✅ تم تحميل ${items.length} صنف`);
                displaySearchResults(items);
            } else {
                console.log('⚠️ لا توجد أصناف في قاعدة البيانات');
                document.getElementById('itemSearchResults').innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #856404; background: #fff3cd; border-radius: 5px;">
                        <i class="fas fa-info-circle" style="font-size: 48px; margin-bottom: 15px;"></i>
                        <h4>لا توجد أصناف في قاعدة البيانات</h4>
                        <p>يرجى إضافة أصناف أولاً أو التحقق من الاتصال بقاعدة البيانات</p>
                    </div>
                `;
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ خطأ في تحميل الأصناف:', error);
            document.getElementById('itemSearchResults').innerHTML = `
                <div style="text-align: center; padding: 40px; color: #721c24; background: #f8d7da; border-radius: 5px;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 15px;"></i>
                    <h4>خطأ في تحميل الأصناف</h4>
                    <p>تعذر الاتصال بقاعدة البيانات</p>
                    <button onclick="loadAllItems()"
                            style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 15px;">
                        <i class="fas fa-redo"></i> إعادة المحاولة
                    </button>
                </div>
            `;
        }
    });
}

// تنفيذ البحث في الأصناف
function performItemSearch() {
    console.log('🔍 بدء تنفيذ البحث...');

    const searchTerm = $('#itemSearchInput').val().trim();
    console.log('مصطلح البحث:', searchTerm);

    if (!searchTerm) {
        showAlert('يرجى إدخال كلمة البحث', 'warning');
        $('#itemSearchInput').focus();
        return;
    }

    // استخدام الفرع المختار أو الافتراضي
    const branchId = selectedBranch || '1';
    console.log('🏢 الفرع المختار للبحث:', branchId);

    console.log('الفرع المختار:', selectedBranch);

    // إظهار مؤشر التحميل
    document.getElementById('itemSearchResults').innerHTML = `
        <div style="text-align: center; padding: 40px; color: #007bff;">
            <div style="border: 4px solid #f3f3f3; border-top: 4px solid #007bff; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <h4>جاري البحث في قاعدة البيانات...</h4>
            <p>البحث عن: "${searchTerm}"</p>
        </div>
        <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        </style>
    `;

    // إعداد بيانات الطلب
    const requestData = {
        branch_id: selectedBranch,
        search_term: searchTerm
    };

    console.log('بيانات الطلب:', requestData);

    // تنفيذ البحث
    $.ajax({
        url: '/contracts/api/items/search',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            branch_id: selectedBranch || '1', // استخدام الفرع المختار أو الافتراضي
            search_term: searchTerm
        }),
        timeout: 30000,
        beforeSend: function(xhr) {
            console.log('📤 إرسال طلب البحث...');
        },
        success: function(response) {
            console.log('📥 استلام رد من الخادم:', response);

            // التعامل مع أشكال مختلفة من الاستجابة
            let items = [];

            if (response.success && response.items) {
                items = response.items;
            } else if (Array.isArray(response)) {
                items = response;
            } else if (response.data && Array.isArray(response.data)) {
                items = response.data;
            } else if (response.results && Array.isArray(response.results)) {
                items = response.results;
            }

            if (items.length > 0) {
                console.log(`✅ تم العثور على ${items.length} صنف`);
                displaySearchResults(items);
            } else {
                console.log('⚠️ لم يتم العثور على أصناف');
                displayNoResults(searchTerm);
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ خطأ في طلب البحث:', {
                status: status,
                error: error,
                responseText: xhr.responseText,
                statusCode: xhr.status
            });

            let errorMessage = 'خطأ في الاتصال بالخادم';
            if (xhr.status === 404) {
                errorMessage = 'API البحث غير موجود';
            } else if (xhr.status === 500) {
                errorMessage = 'خطأ في الخادم';
            } else if (status === 'timeout') {
                errorMessage = 'انتهت مهلة الاتصال';
            }

            displaySearchError(errorMessage);
        }
    });

    console.log('🔍 تم إرسال طلب البحث عن:', searchTerm);
}

// دالة اختبار لعرض بيانات وهمية (للتجربة)
function testItemSearch() {
    console.log('🧪 تشغيل اختبار البحث...');

    // فتح النافذة أولاً إذا لم تكن مفتوحة
    const modal = document.getElementById('itemSearchModal');
    if (!modal || modal.style.display === 'none') {
        openItemSearchModal();
        setTimeout(() => {
            testItemSearch();
        }, 200);
        return;
    }

    const testItems = [
        {
            item_id: 'ITM001',
            item_code: 'ITM001',
            item_name: 'صنف تجريبي 1',
            unit: 'قطعة',
            unit_name: 'قطعة'
        },
        {
            item_id: 'ITM002',
            item_code: 'ITM002',
            item_name: 'صنف تجريبي 2',
            unit: 'كيلو',
            unit_name: 'كيلو'
        },
        {
            item_id: 'ITM003',
            item_code: 'ITM003',
            item_name: 'صنف تجريبي 3',
            unit: 'متر',
            unit_name: 'متر'
        }
    ];

    console.log('عرض البيانات التجريبية...');
    displaySearchResults(testItems);
}

// دالة تشخيص شاملة
function diagnoseSystem() {
    console.log('🔧 بدء التشخيص الشامل...');
    console.log('='.repeat(50));

    // فحص jQuery
    console.log('jQuery متاح:', typeof $ !== 'undefined');

    // فحص Bootstrap
    console.log('Bootstrap متاح:', typeof bootstrap !== 'undefined');

    // فحص النافذة
    const modal = document.getElementById('itemSearchModal');
    console.log('نافذة البحث موجودة:', !!modal);

    // فحص الحقول
    const itemCodeFields = document.querySelectorAll('input[name^="item_code_"]');
    const supplierField = document.getElementById('supplier_id');
    console.log('عدد حقول رقم الصنف:', itemCodeFields.length);
    console.log('حقل المورد موجود:', !!supplierField);

    // فحص الفرع المختار
    console.log('الفرع المختار:', selectedBranch);

    // فحص الحقل النشط
    console.log('الحقل النشط:', currentActiveInput);
    console.log('العنصر المركز عليه:', document.activeElement);

    // توضيح آلية عمل F9
    console.log('='.repeat(50));
    console.log('📋 آلية عمل F9:');
    console.log('• في حقل المورد (supplier_code) → البحث في جدول IAS20251.V_DETAILS');
    console.log('• في حقل رقم الصنف (item_code_*) → البحث في جدول IAS20251.IAS_ITM_MST');
    console.log('📊 المستخدم المستخدم: IAS20251');
    console.log('🔧 الحقول المدعومة:');
    console.log('  - supplier_code (ID)');
    console.log('  - supplier_id (ID أو Name)');
    console.log('  - item_code_1, item_code_2, ... (Name)');
    console.log('='.repeat(50));

    // اختبار فتح النافذة
    if (modal) {
        try {
            console.log('محاولة فتح النافذة للاختبار...');
            $(modal).modal('show');
            setTimeout(() => {
                console.log('إغلاق النافذة...');
                $(modal).modal('hide');
            }, 2000);
        } catch (error) {
            console.error('خطأ في اختبار النافذة:', error);
        }
    }

    console.log('✅ انتهى التشخيص');
}

// دالة إغلاق جميع النوافذ - بسيطة ومضمونة
function closeAllModals() {
    console.log('🔧 إغلاق جميع النوافذ...');

    // إغلاق نافذة الموردين
    const supplierModal = document.getElementById('supplierSearchModal');
    if (supplierModal) {
        supplierModal.style.display = 'none';
    }

    // إغلاق نافذة الأصناف
    const itemModal = document.getElementById('itemSearchModal');
    if (itemModal) {
        itemModal.style.display = 'none';
    }

    console.log('✅ تم إغلاق جميع النوافذ');
}

// دالة إغلاق نافذة الأصناف
function closeItemSearchModal() {
    const modal = document.getElementById('itemSearchModal');
    if (modal) {
        modal.style.display = 'none';
    }
    console.log('✅ تم إغلاق نافذة الأصناف');
}

// دالة إغلاق نافذة الموردين
function closeSupplierSearchModal() {
    const modal = document.getElementById('supplierSearchModal');
    if (modal) {
        modal.style.display = 'none';
    }
    console.log('✅ تم إغلاق نافذة الموردين');
}

// إضافة الدوال للوحة التحكم (يمكن استدعاؤها من Console)
window.diagnoseSystem = diagnoseSystem;
window.testItemSearch = testItemSearch;
window.testSupplierSearch = testSupplierSearch;
window.openItemSearchModal = openItemSearchModal;
window.openSupplierSearch = openSupplierSearch;
window.closeItemSearchModal = closeItemSearchModal;
window.closeSupplierSearchModal = closeSupplierSearchModal;
window.closeAllModals = closeAllModals;
window.loadAllSuppliers = loadAllSuppliers;
window.loadAllItems = loadAllItems;
window.openItemSearchFromTable = openItemSearchFromTable;

// عرض نتائج البحث للأصناف بشكل بسيط وأنيق
function displaySearchResults(items) {
    let html = `
        <div style="margin-bottom: 15px; padding: 10px; background: #cce5ff; border-radius: 5px;">
            <h4 style="margin: 0; color: #004085;">
                <i class="fas fa-check-circle"></i> تم العثور على ${items.length} صنف
            </h4>
            <small style="color: #004085;">انقر على "اختيار" أو انقر مرتين على الصف</small>
        </div>
        <div style="max-height: 300px; overflow-y: auto;">
            <table style="width: 100%; border-collapse: collapse; background: white;">
                <thead>
                    <tr style="background: #007bff; color: white;">
                        <th style="padding: 10px; border: 1px solid #ddd;">رقم الصنف</th>
                        <th style="padding: 10px; border: 1px solid #ddd;">اسم الصنف</th>
                        <th style="padding: 10px; border: 1px solid #ddd;">الوحدة</th>
                        <th style="padding: 10px; border: 1px solid #ddd;">الإجراء</th>
                    </tr>
                </thead>
                <tbody>
    `;

    items.forEach((item, index) => {
        // التعامل مع أسماء الحقول من API الأصناف الصحيح
        const itemCode = item.ITEM_ID || item.item_id || item.item_code || item.I_CODE || `ITM${index + 1}`;
        const itemName = item.ITEM_NAME || item.item_name || item.I_NAME || item.name_ar || 'صنف غير محدد';
        const itemUnit = item.UNIT || item.unit || item.ITM_UNT || item.unit_name || 'قطعة';

        html += `
            <tr style="cursor: pointer; border-bottom: 1px solid #ddd;"
                ondblclick="selectItemFromSearch('${itemCode}', '${itemName}', '${itemUnit}')"
                onmouseover="this.style.backgroundColor='#e3f2fd'"
                onmouseout="this.style.backgroundColor='white'">
                <td style="padding: 8px; border: 1px solid #ddd;">
                    <strong style="color: #007bff;">${itemCode}</strong>
                </td>
                <td style="padding: 8px; border: 1px solid #ddd;">${itemName}</td>
                <td style="padding: 8px; border: 1px solid #ddd;">
                    <span style="background: #6c757d; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">
                        ${itemUnit}
                    </span>
                </td>
                <td style="padding: 8px; border: 1px solid #ddd;">
                    <button onclick="selectItemFromSearch('${itemCode}', '${itemName}', '${itemUnit}')"
                            style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; font-size: 12px;">
                        <i class="fas fa-check"></i> اختيار
                    </button>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
        <div style="text-align: center; margin-top: 10px; color: #666; font-size: 12px;">
            <i class="fas fa-info-circle"></i>
            يمكنك النقر مرتين على أي صف لاختيار الصنف مباشرة
        </div>
    `;

    document.getElementById('itemSearchResults').innerHTML = html;
}

// عرض رسالة عدم وجود نتائج للأصناف
function displayNoResults(searchTerm) {
    document.getElementById('itemSearchResults').innerHTML = `
        <div style="text-align: center; padding: 40px; color: #856404; background: #fff3cd; border-radius: 5px;">
            <i class="fas fa-search-minus" style="font-size: 48px; margin-bottom: 15px;"></i>
            <h4>لم يتم العثور على أصناف</h4>
            <p>لم يتم العثور على أصناف تحتوي على: "<strong>${searchTerm}</strong>"</p>
            <button onclick="document.getElementById('itemSearchInput').focus()"
                    style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 10px;">
                <i class="fas fa-search"></i> جرب البحث مرة أخرى
            </button>
        </div>
    `;
}

// عرض رسالة خطأ في البحث للأصناف
function displaySearchError(error) {
    document.getElementById('itemSearchResults').innerHTML = `
        <div style="text-align: center; padding: 40px; color: #721c24; background: #f8d7da; border-radius: 5px;">
            <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 15px;"></i>
            <h4>خطأ في البحث</h4>
            <p>حدث خطأ أثناء البحث في قاعدة البيانات</p>
            <small>تفاصيل الخطأ: ${error}</small>
            <br>
            <button onclick="performItemSearch()"
                    style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 15px;">
                <i class="fas fa-redo"></i> إعادة المحاولة
            </button>
        </div>
    `;
}

// اختيار صنف من نتائج البحث
function selectItemFromSearch(itemCode, itemName, itemUnit) {
    console.log('✅ اختيار صنف:', { code: itemCode, name: itemName, unit: itemUnit });

    // البحث عن الحقل المناسب للإدراج
    let targetInput = null;
    let rowNumber = null;

    // إذا تم فتح النافذة من الجدول، استخدم الصف المحدد
    if (window.targetRowIndex !== undefined) {
        rowNumber = window.targetRowIndex;
        console.log('🎯 استخدام الصف المحدد من الجدول:', rowNumber);

        // إدراج البيانات في الجدول مباشرة
        if (contractTable) {
            contractTable.setDataAtCell(rowNumber, 0, itemCode); // كود الصنف
            contractTable.setDataAtCell(rowNumber, 1, itemName); // اسم الصنف
            contractTable.setDataAtCell(rowNumber, 2, itemUnit); // الوحدة

            // إغلاق النافذة
            closeItemSearchModal();

            // مسح المتغير
            delete window.targetRowIndex;

            // إظهار رسالة نجاح
            showAlert(`تم اختيار الصنف: ${itemName}`, 'success');

            console.log('✅ تم إدراج الصنف في الجدول');
            return;
        }
    }

    if (currentActiveInput) {
        // إذا كان هناك حقل نشط، استخدمه
        targetInput = currentActiveInput;
        const inputName = $(currentActiveInput).attr('name');
        rowNumber = inputName.replace('item_code_', '');
        console.log('🎯 استخدام الحقل النشط:', inputName);
    } else {
        // البحث عن أول حقل فارغ أو آخر حقل
        const itemCodeInputs = document.querySelectorAll('input[name^="item_code_"]');

        // البحث عن أول حقل فارغ
        for (let input of itemCodeInputs) {
            if (!input.value.trim()) {
                targetInput = input;
                const inputName = input.getAttribute('name');
                rowNumber = inputName.replace('item_code_', '');
                console.log('🎯 استخدام أول حقل فارغ:', inputName);
                break;
            }
        }

        // إذا لم يوجد حقل فارغ، استخدم آخر حقل
        if (!targetInput && itemCodeInputs.length > 0) {
            targetInput = itemCodeInputs[itemCodeInputs.length - 1];
            const inputName = targetInput.getAttribute('name');
            rowNumber = inputName.replace('item_code_', '');
            console.log('🎯 استخدام آخر حقل:', inputName);
        }
    }

    if (!targetInput || !rowNumber) {
        showAlert('خطأ: لم يتم العثور على حقل مناسب لإدراج الصنف', 'error');
        return;
    }

    // ملء الحقول
    const itemCodeInput = document.querySelector(`input[name="item_code_${rowNumber}"]`);
    const itemNameInput = document.querySelector(`input[name="item_name_${rowNumber}"]`);
    const unitInput = document.querySelector(`input[name="unit_${rowNumber}"]`);

    if (itemCodeInput) {
        itemCodeInput.value = itemCode;
        // تأثير بصري للنجاح
        $(itemCodeInput).removeClass('is-invalid').addClass('is-valid');
        setTimeout(() => $(itemCodeInput).removeClass('is-valid'), 2000);
    }

    if (itemNameInput) {
        itemNameInput.value = itemName;
        $(itemNameInput).addClass('bg-success-subtle');
        setTimeout(() => $(itemNameInput).removeClass('bg-success-subtle'), 2000);
    }

    if (unitInput) {
        unitInput.value = itemUnit;
        $(unitInput).addClass('bg-success-subtle');
        setTimeout(() => $(unitInput).removeClass('bg-success-subtle'), 2000);
    }

    // حفظ في الكاش
    itemsCache[itemCode] = {
        item_code: itemCode,
        item_name: itemName,
        unit_name: itemUnit
    };

    // إغلاق النافذة
    closeItemSearchModal();

    // إظهار رسالة نجاح
    showAlert(`تم اختيار الصنف: ${itemName}`, 'success');

    // التركيز على الحقل التالي (الكمية)
    setTimeout(() => {
        const quantityInput = document.querySelector(`input[name="quantity_${rowNumber}"]`);
        if (quantityInput) {
            quantityInput.focus();
        }
    }, 300);

    // تحديث الإحصائيات بعد اختيار الصنف
    updateContractStatistics();

    console.log('✅ تم اختيار الصنف:', {
        code: itemCode,
        name: itemName,
        unit: itemUnit,
        row: rowNumber
    });
}

// دالة إظهار التنبيهات
function showAlert(message, type = 'info') {
    // إنشاء التنبيه
    const alertId = 'alert_' + Date.now();
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';

    // تحويل الرسائل متعددة الأسطر إلى HTML
    const formattedMessage = message.replace(/\n/g, '<br>');

    const alertHtml = `
        <div id="${alertId}" class="alert ${alertClass} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 500px; white-space: pre-line;">
            <i class="fas fa-${type === 'success' ? 'check-circle' :
                              type === 'error' ? 'exclamation-circle' :
                              type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${formattedMessage}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // إضافة التنبيه إلى الصفحة
    $('body').append(alertHtml);

    // إزالة التنبيه تلقائياً (مدة أطول للرسائل الطويلة)
    const timeout = message.length > 100 ? 8000 : 5000;
    setTimeout(() => {
        $(`#${alertId}`).alert('close');
    }, timeout);
}







// التحقق من كود الصنف
function validateItemCode(value, callback) {
    if (!value) {
        callback(true);
        return;
    }

    if (itemsCache[value]) {
        callback(true);
    } else {
        // البحث في الخادم
        $.ajax({
            url: '/contracts/api/items/search',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                branch_id: selectedBranch,
                search_term: value
            }),
            success: function(response) {
                if (response.success) {
                    const exactMatch = response.items.find(item => item.item_id === value);
                    if (exactMatch) {
                        itemsCache[value] = exactMatch;
                        callback(true);
                    } else {
                        callback(false);
                    }
                } else {
                    callback(false);
                }
            },
            error: function() {
                callback(false);
            }
        });
    }
}

// إضافة صفوف للجدول
function addTableRows(count) {
    if (contractTable) {
        const currentData = contractTable.getData();
        const newRows = generateEmptyData(count);
        contractTable.loadData([...currentData, ...newRows]);
        updateStatistics();
        console.log(`✅ تم إضافة ${count} صف جديد`);
    }
}

// حذف الصفوف المحددة
function deleteSelectedRows() {
    if (!contractTable) return;

    const selected = contractTable.getSelected();
    if (selected && selected.length > 0) {
        const [row1, , row2] = selected[0];
        const rowsToDelete = Math.abs(row2 - row1) + 1;

        if (confirm(`هل أنت متأكد من حذف ${rowsToDelete} صف؟`)) {
            contractTable.alter('remove_row', Math.min(row1, row2), rowsToDelete);
            updateStatistics();
            console.log(`✅ تم حذف ${rowsToDelete} صف`);
        }
    } else {
        alert('يرجى تحديد الصفوف المراد حذفها أولاً');
    }
}

// مسح الخلايا المحددة
function clearSelectedCells() {
    if (!contractTable) return;

    const selected = contractTable.getSelected();
    if (selected && selected.length > 0) {
        const [row1, col1, row2, col2] = selected[0];

        for (let row = row1; row <= row2; row++) {
            for (let col = col1; col <= col2; col++) {
                contractTable.setDataAtCell(row, col, '');
            }
        }
        updateStatistics();
        console.log('✅ تم مسح البيانات المحددة');
    } else {
        alert('يرجى تحديد الخلايا المراد مسحها أولاً');
    }
}









// تحديث معلومات التحديد
function updateSelectionInfo(row, column, row2, column2) {
    // يمكن إضافة معلومات عن التحديد الحالي هنا
}

// استيراد من Excel
function importFromExcel() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.xlsx,.xls,.csv';

    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            alert('سيتم تطوير ميزة الاستيراد قريباً');
        }
    };

    input.click();
}

// تصدير إلى Excel
function exportToExcel() {
    if (!contractTable) return;

    const data = contractTable.getData();
    const validRows = data.filter(row => row[0]); // الصفوف التي تحتوي على كود صنف

    if (validRows.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }

    // يمكن استخدام مكتبة مثل SheetJS هنا
    alert('سيتم تطوير ميزة التصدير قريباً');
}

// حفظ كمسودة
function saveDraft() {
    if (!contractTable) return;

    const data = contractTable.getData();
    const contractData = {
        contract_info: {
            branch_id: $('#branch_id').val(),
            contract_date: $('#contract_date').val(),
            start_date: $('#start_date').val(),
            end_date: $('#end_date').val(),
            supplier_id: $('#supplier_id').val(),
            supplier_name: $('#supplier_name').val(),
            currency_code: $('#currency_code').val(),
            exchange_rate: $('#exchange_rate').val(),
            description: $('#description').val(),
            reference_number: $('#reference_number').val(),
            contract_amount: $('#contract_amount').val(),
            discount_amount: $('#discount_amount').val(),
            contract_status: 'مسودة'
        },
        table_data: data,
        timestamp: new Date().toISOString()
    };

    localStorage.setItem('contract_draft', JSON.stringify(contractData));
    alert('تم حفظ المسودة محلياً');
}

// إلغاء النموذج
function cancelForm() {
    if (confirm('هل أنت متأكد من إلغاء النموذج؟ سيتم فقدان جميع البيانات غير المحفوظة.')) {
        window.location.href = '/contracts/';
    }
}

// منع إرسال النموذج بـ Enter
$('#contractForm').on('keypress', function(e) {
    if (e.keyCode === 13 && !$(e.target).is('textarea, #itemSearchInput, #supplierSearchInput')) {
        e.preventDefault();
        console.log('⚠️ تم منع Enter - استخدم F10 للحفظ');
        return false;
    }
});

// تم نقل دالة validateContractDetails إلى window.validateContractDetails أعلاه

// معالجة إرسال النموذج
$('#contractForm').on('submit', function(e) {
    e.preventDefault();

    console.log('🔍 بدء التحقق من البيانات قبل الحفظ...');

    // التحقق من البيانات الأساسية
    const mainValidation = window.validateMainData();
    console.log('📋 نتيجة التحقق من البيانات الرئيسية:', mainValidation);

    if (!mainValidation.isValid) {
        console.log('❌ البيانات الرئيسية غير مكتملة');
        showAlert(`يجب إدخال بيانات الحقل "${mainValidation.missingField}" أولاً`, 'error');
        $(`#${mainValidation.fieldId}`).focus();
        return;
    }

    // التحقق من تفاصيل العقد
    const detailsValidation = window.validateContractDetails();
    console.log('📦 نتيجة التحقق من تفاصيل العقد:', detailsValidation);

    if (!detailsValidation.isValid) {
        console.log('❌ تفاصيل العقد غير مكتملة');
        let errorMessage = 'يرجى إكمال البيانات المطلوبة:\n\n';
        errorMessage += detailsValidation.errors.join('\n');

        showAlert(errorMessage, 'error');
        return;
    }

    console.log('✅ جميع البيانات صحيحة - المتابعة للحفظ...');

    // جمع بيانات التفاصيل من الحقول
    const contractItems = [];

    // البحث عن جميع حقول الأصناف
    const itemCodeInputs = document.querySelectorAll('input[name^="item_code_"]');

    itemCodeInputs.forEach((itemCodeInput, index) => {
        const rowNumber = itemCodeInput.name.replace('item_code_', '');

        const itemCode = itemCodeInput.value.trim();
        const itemName = document.querySelector(`input[name="item_name_${rowNumber}"]`)?.value.trim() || '';
        const unit = document.querySelector(`input[name="unit_${rowNumber}"]`)?.value.trim() || 'قطعة';
        const quantity = parseFloat(document.querySelector(`input[name="quantity_${rowNumber}"]`)?.value) || 0;
        const freeQuantity = parseFloat(document.querySelector(`input[name="free_quantity_${rowNumber}"]`)?.value) || 0;
        const unitPrice = parseFloat(document.querySelector(`input[name="unit_price_${rowNumber}"]`)?.value) || 0;
        const discountPercentage = parseFloat(document.querySelector(`input[name="discount_percent_${rowNumber}"]`)?.value) || 0;
        const lineTotal = parseFloat(document.querySelector(`input[name="total_${rowNumber}"]`)?.value) || (quantity * unitPrice);
        const productionDate = document.querySelector(`input[name="production_date_${rowNumber}"]`)?.value || '';
        const expiryDate = document.querySelector(`input[name="expiry_date_${rowNumber}"]`)?.value || '';
        const notes = document.querySelector(`input[name="notes_${rowNumber}"]`)?.value.trim() || '';

        // إضافة الصف إذا كان يحتوي على كود صنف وكمية
        if (itemCode && quantity > 0) {
            contractItems.push({
                item_code: itemCode,
                item_name: itemName,
                unit_name: unit,
                quantity: quantity,
                free_quantity: freeQuantity,
                unit_price: unitPrice,
                discount_percentage: discountPercentage,
                line_total: lineTotal,
                production_date: productionDate,
                expiry_date: expiryDate,
                notes: notes
            });
        }
    });

    if (contractItems.length === 0) {
        alert('يرجى إضافة صنف واحد على الأقل مع كمية صحيحة');
        return;
    }

    // إضافة البيانات إلى النموذج
    const form = document.getElementById('contractForm');

    // إزالة أي input مخفي سابق
    const existingInput = form.querySelector('input[name="table_data"]');
    if (existingInput) {
        existingInput.remove();
    }

    // التأكد من القيم المالية قبل الإرسال
    const contractAmount = $('#contract_amount').val() || '0';
    const discountAmount = $('#discount_amount').val() || '0';
    const netAmount = $('#net_amount').val() || '0';

    // تحديث الحقول في النموذج
    $('#contract_amount').val(contractAmount);
    $('#discount_amount').val(discountAmount);
    $('#net_amount').val(netAmount);

    console.log('💰 القيم المالية قبل الإرسال:', {
        contract_amount: contractAmount,
        discount_amount: discountAmount,
        net_amount: netAmount
    });

    // إضافة input مخفي جديد
    const itemsInput = document.createElement('input');
    itemsInput.type = 'hidden';
    itemsInput.name = 'table_data';
    itemsInput.value = JSON.stringify(contractItems);
    form.appendChild(itemsInput);

    console.log('✅ تم تحضير البيانات للإرسال');
    console.log('📋 عدد الأصناف:', contractItems.length);

    // إرسال النموذج
    form.submit();
});

// تحميل حالات العقود للنموذج
function loadContractStatusesForForm() {
    console.log('🔄 تحميل حالات العقود للنموذج...');

    fetch('/contracts/api/contract-statuses')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.statuses) {
                console.log(`✅ تم جلب ${data.statuses.length} حالة للنموذج`);
                populateContractStatusSelect(data.statuses);
            } else {
                console.warn('⚠️ فشل في جلب حالات العقود، استخدام الحالات الافتراضية');
                populateDefaultStatuses();
            }
        })
        .catch(error => {
            console.error('❌ خطأ في جلب حالات العقود:', error);
            populateDefaultStatuses();
        });
}

// ملء قائمة حالات العقد
function populateContractStatusSelect(statuses) {
    const statusSelect = document.getElementById('contract_status');
    if (!statusSelect) return;

    // مسح الخيارات الموجودة (عدا الأول)
    statusSelect.innerHTML = '<option value="">اختر حالة العقد...</option>';

    // إضافة الحالات من API
    statuses.forEach(status => {
        const option = document.createElement('option');
        option.value = status.status_name_ar;
        option.textContent = status.status_name_ar;
        option.setAttribute('data-color', status.status_color);
        option.setAttribute('data-icon', status.status_icon);
        option.setAttribute('data-code', status.status_code);

        // تحديد "مسودة" كخيار افتراضي
        if (status.status_code === 'DRAFT') {
            option.selected = true;
        }

        statusSelect.appendChild(option);
    });

    console.log(`📋 تم تحديث قائمة حالات العقد بـ ${statuses.length} حالة`);
}

// ملء الحالات الافتراضية (حل احتياطي)
function populateDefaultStatuses() {
    const statusSelect = document.getElementById('contract_status');
    if (!statusSelect) return;

    const defaultStatuses = [
        { value: 'مسودة', text: 'مسودة', selected: true },
        { value: 'معتمد', text: 'معتمد' },
        { value: 'منفذ جزئياً', text: 'منفذ جزئياً' },
        { value: 'منفذ كلياً', text: 'منفذ كلياً' }
    ];

    statusSelect.innerHTML = '<option value="">اختر حالة العقد...</option>';

    defaultStatuses.forEach(status => {
        const option = document.createElement('option');
        option.value = status.value;
        option.textContent = status.text;
        if (status.selected) option.selected = true;
        statusSelect.appendChild(option);
    });

    console.log('📋 تم تحديث قائمة حالات العقد بالحالات الافتراضية');
}

// دالة تحديث سعر الصرف عند تغيير العملة
function updateExchangeRate() {
    const currencySelect = document.getElementById('currency_code');
    const exchangeRateInput = document.getElementById('exchange_rate');

    if (currencySelect && exchangeRateInput) {
        const selectedOption = currencySelect.options[currencySelect.selectedIndex];
        const exchangeRate = selectedOption.getAttribute('data-rate');

        if (exchangeRate) {
            exchangeRateInput.value = parseFloat(exchangeRate).toFixed(4);
            console.log(`✅ تم تحديث سعر الصرف للعملة ${currencySelect.value}: ${exchangeRate}`);

            // إعادة حساب صافي المبلغ إذا كان هناك مبلغ عقد
            calculateNetAmount();
        }
    }
}

// تحميل الحالات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل صفحة إنشاء عقد جديد');

    // تهيئة التاريخ الحالي
    const today = new Date().toISOString().split('T')[0];
    const contractDateField = document.getElementById('contract_date');
    if (contractDateField) {
        contractDateField.value = today;
        console.log('✅ تم تعيين التاريخ الحالي:', today);
    }

    // تحميل حالات العقود
    loadContractStatusesForForm();

    // تهيئة سعر الصرف للعملة المختارة افتراضياً
    updateExchangeRate();
});

</script>
{% endblock %}
