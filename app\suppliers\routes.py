# -*- coding: utf-8 -*-
"""
مسارات إدارة الموردين
Suppliers Management Routes
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, abort
from flask_login import login_required, current_user
from app.suppliers import bp
from oracle_manager import get_oracle_manager
from datetime import datetime
from decimal import Decimal
import logging

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

@bp.route('/accounts_management')
@login_required
def accounts_management():
    """صفحة إدارة حسابات الموردين"""
    try:
        return render_template('suppliers/accounts_management.html')
    except Exception as e:
        logger.error(f"خطأ في تحميل صفحة إدارة حسابات الموردين: {e}")
        flash('حدث خطأ في تحميل الصفحة', 'error')
        return redirect(url_for('suppliers.index'))

@bp.route('/api/accounts_data')
@login_required
def get_accounts_data():
    """API لجلب بيانات حسابات الموردين"""
    try:
        oracle_mgr = get_oracle_manager()

        if not oracle_mgr.connect():
            return jsonify({'error': 'فشل في الاتصال بقاعدة البيانات'}), 500

        # جلب إحصائيات الموردين
        stats_query = """
            SELECT
                COUNT(*) as total_suppliers,
                COUNT(CASE WHEN IS_ACTIVE = 1 THEN 1 END) as active_suppliers,
                COUNT(CASE WHEN IS_ACTIVE = 0 THEN 1 END) as inactive_suppliers,
                COUNT(CASE WHEN RATING <= 2 THEN 1 END) as pending_suppliers
            FROM SUPPLIERS
        """

        stats_result = oracle_mgr.execute_query(stats_query)
        stats = stats_result[0] if stats_result else [0, 0, 0, 0]

        # جلب بيانات الموردين
        suppliers_query = """
            SELECT
                ID,
                SUPPLIER_CODE,
                NAME_AR,
                NVL(SUPPLIER_TYPE, 'تجاري') as supplier_type,
                CASE WHEN IS_ACTIVE = 1 THEN 'نشط' ELSE 'غير نشط' END as status,
                TO_CHAR(CREATED_AT, 'YYYY-MM-DD') as registration_date,
                TO_CHAR(UPDATED_AT, 'YYYY-MM-DD') as last_transaction,
                NVL(CREDIT_LIMIT, 0) as balance
            FROM SUPPLIERS
            ORDER BY ID DESC
            FETCH FIRST 50 ROWS ONLY
        """

        suppliers_result = oracle_mgr.execute_query(suppliers_query)

        suppliers_data = []
        if suppliers_result:
            for row in suppliers_result:
                suppliers_data.append({
                    'id': row[0],
                    'code': row[1],
                    'name': row[2],
                    'type': row[3] or 'تجاري',
                    'status': row[4],
                    'registration_date': row[5] or '',
                    'last_transaction': row[6] or 'لا يوجد',
                    'balance': f"{float(row[7]):,.0f}" if row[7] else '0'
                })

        # جلب توزيع الموردين حسب النوع
        types_query = """
            SELECT
                NVL(SUPPLIER_TYPE, 'تجاري') as type,
                COUNT(*) as count
            FROM SUPPLIERS
            WHERE IS_ACTIVE = 1
            GROUP BY SUPPLIER_TYPE
            ORDER BY count DESC
        """

        types_result = oracle_mgr.execute_query(types_query)
        types_data = []
        if types_result:
            for row in types_result:
                types_data.append({
                    'type': row[0],
                    'count': row[1]
                })

        # جلب اتجاه نمو الموردين
        growth_query = """
            SELECT
                TO_CHAR(CREATED_AT, 'YYYY-MM') as month,
                COUNT(*) as new_suppliers
            FROM SUPPLIERS
            WHERE CREATED_AT >= ADD_MONTHS(SYSDATE, -6)
            GROUP BY TO_CHAR(CREATED_AT, 'YYYY-MM')
            ORDER BY month
        """

        growth_result = oracle_mgr.execute_query(growth_query)
        growth_data = []
        if growth_result:
            for row in growth_result:
                growth_data.append({
                    'month': row[0],
                    'count': row[1]
                })

        oracle_mgr.disconnect()

        return jsonify({
            'statistics': {
                'total_suppliers': stats[0],
                'active_suppliers': stats[1],
                'inactive_suppliers': stats[2],
                'pending_suppliers': stats[3]
            },
            'suppliers': suppliers_data,
            'types_distribution': types_data,
            'growth_trend': growth_data
        })

    except Exception as e:
        logger.error(f"خطأ في جلب بيانات حسابات الموردين: {e}")
        return jsonify({'error': 'حدث خطأ في جلب البيانات'}), 500

@bp.route('/')
def index():
    """صفحة قائمة الموردين"""
    try:
        # معاملات البحث والتصفح
        page = request.args.get('page', 1, type=int)
        per_page = 20
        search_term = request.args.get('search_term', '')
        category = request.args.get('category', '')
        status = request.args.get('status', '')
        city = request.args.get('city', '')

        oracle_mgr = get_oracle_manager()

        # بناء الاستعلام الأساسي
        base_query = """
        SELECT s.id, s.supplier_code, s.name_ar, s.name_en, s.contact_person, s.phone,
               s.email, s.city, s.supplier_type, s.is_active, s.created_at,
               sg.group_name_ar as group_name
        FROM suppliers s
        LEFT JOIN supplier_groups sg ON s.supplier_group_id = sg.group_id
        WHERE 1=1
        """

        params = []
        param_counter = 1

        # تطبيق الفلاتر
        if search_term:
            base_query += f"""
            AND (UPPER(s.name_ar) LIKE UPPER(:{param_counter})
                 OR UPPER(s.name_en) LIKE UPPER(:{param_counter + 1})
                 OR s.code LIKE :{param_counter + 2}
                 OR s.phone LIKE :{param_counter + 3}
                 OR s.email LIKE :{param_counter + 4})
            """
            search_pattern = f"%{search_term}%"
            params.extend([search_pattern] * 5)
            param_counter += 5

        if category:
            base_query += f" AND s.supplier_type = :{param_counter}"
            params.append(category)
            param_counter += 1

        if status == 'active':
            base_query += " AND s.is_active = 1"
        elif status == 'inactive':
            base_query += " AND s.is_active = 0"

        if city:
            base_query += f" AND UPPER(s.city) LIKE UPPER(:{param_counter})"
            params.append(f"%{city}%")
            param_counter += 1

        # ترتيب النتائج
        base_query += " ORDER BY s.name_ar"

        # تنفيذ الاستعلام
        suppliers_data = oracle_mgr.execute_query(base_query, params)

        # تحويل البيانات
        suppliers_list = []
        for supplier_data in suppliers_data:
            supplier_dict = {
                'id': supplier_data[0],
                'code': supplier_data[1],
                'name_ar': supplier_data[2],
                'name_en': supplier_data[3],
                'contact_person': supplier_data[4],
                'phone': supplier_data[5],
                'email': supplier_data[6],
                'city': supplier_data[7],
                'category': supplier_data[8],  # supplier_type
                'is_active': bool(supplier_data[9]) if supplier_data[9] is not None else True,
                'created_at': supplier_data[10],
                'group_name': supplier_data[11]
            }
            suppliers_list.append(supplier_dict)

        # تطبيق التصفح يدوياً
        total = len(suppliers_list)
        start = (page - 1) * per_page
        end = start + per_page
        suppliers_page = suppliers_list[start:end]

        # حساب معلومات التصفح
        has_prev = page > 1
        has_next = end < total
        prev_num = page - 1 if has_prev else None
        next_num = page + 1 if has_next else None

        # إنشاء كائن pagination مبسط
        class SimplePagination:
            def __init__(self, items, page, per_page, total, has_prev, has_next, prev_num, next_num):
                self.items = items
                self.page = page
                self.per_page = per_page
                self.total = total
                self.has_prev = has_prev
                self.has_next = has_next
                self.prev_num = prev_num
                self.next_num = next_num
                self.pages = (total + per_page - 1) // per_page

            def iter_pages(self, left_edge=2, left_current=2, right_current=3, right_edge=2):
                """إنشاء قائمة أرقام الصفحات للعرض"""
                last = self.pages
                for num in range(1, last + 1):
                    if num <= left_edge or \
                       (self.page - left_current - 1 < num < self.page + right_current) or \
                       num > last - right_edge:
                        yield num

        suppliers = SimplePagination(
            suppliers_page, page, per_page, total,
            has_prev, has_next, prev_num, next_num
        )

        # إحصائيات سريعة باستخدام Oracle
        stats_queries = {
            'total_suppliers': "SELECT COUNT(*) FROM suppliers WHERE is_active = 1",
            'total_groups': "SELECT COUNT(*) FROM supplier_groups WHERE is_active = 1",
            'active_suppliers': "SELECT COUNT(*) FROM suppliers WHERE is_active = 1",
            'inactive_suppliers': "SELECT COUNT(*) FROM suppliers WHERE is_active = 0"
        }

        stats = {}
        for key, query in stats_queries.items():
            try:
                result = oracle_mgr.execute_query(query)
                stats[key] = result[0][0] if result else 0
            except Exception as e:
                logger.error(f"خطأ في حساب إحصائية {key}: {e}")
                stats[key] = 0

        # الحصول على الفئات المتاحة
        categories_query = "SELECT DISTINCT supplier_type FROM suppliers WHERE supplier_type IS NOT NULL ORDER BY supplier_type"
        categories_data = oracle_mgr.execute_query(categories_query)
        categories = [cat[0] for cat in categories_data] if categories_data else []

        # الحصول على المدن المتاحة
        cities_query = "SELECT DISTINCT city FROM suppliers WHERE city IS NOT NULL ORDER BY city"
        cities_data = oracle_mgr.execute_query(cities_query)
        cities = [city[0] for city in cities_data] if cities_data else []

        return render_template('suppliers/index.html',
                             suppliers=suppliers,
                             stats=stats,
                             categories=categories,
                             cities=cities,
                             search_term=search_term,
                             selected_category=category,
                             selected_status=status,
                             selected_city=city)

    except Exception as e:
        logger.error(f"خطأ في صفحة الموردين: {e}")
        return render_template('suppliers/temp_index.html',
                             message=f"خطأ في تحميل صفحة الموردين: {str(e)}")
    
    # تم نقل return إلى نهاية try block أعلاه

@bp.route('/import', methods=['GET', 'POST'])
@login_required
def import_suppliers():
    """استيراد الموردين من المصدر الخارجي"""
    try:
        if request.method == 'POST':
            # استيراد الموردين من المصدر الخارجي
            oracle_mgr = get_oracle_manager()

            # اختبار الاتصال بالمصدر الخارجي أولاً
            test_query = "SELECT COUNT(*) FROM <EMAIL>"

            try:
                test_result = oracle_mgr.execute_query(test_query)
                logger.info(f"اختبار الاتصال: {test_result}")

                if not test_result or test_result[0][0] == 0:
                    return jsonify({
                        'success': False,
                        'message': 'جدول V_DETAILS فارغ أو غير متاح'
                    })

            except Exception as e:
                logger.error(f"خطأ في الاتصال بالمصدر الخارجي: {e}")
                return jsonify({
                    'success': False,
                    'message': f'خطأ في الاتصال بالمصدر الخارجي: {str(e)}'
                })

            # الاتصال بالمصدر الخارجي - استيراد جميع الموردين
            external_query = """
            SELECT
                V_CODE,
                V_A_NAME,
                V_E_NAME,
                V_ADDRESS,
                V_PHONE,
                V_E_MAIL
            FROM <EMAIL>
            """

            try:
                external_suppliers = oracle_mgr.execute_query(external_query)

                if not external_suppliers:
                    return jsonify({
                        'success': False,
                        'message': 'لم يتم العثور على موردين في المصدر الخارجي'
                    })

                # عداد الموردين المستوردين والمحدثين
                imported_count = 0
                updated_count = 0
                errors = []

                # معالجة كل مورد من المصدر الخارجي
                for supplier_data in external_suppliers:
                    try:
                        code = str(supplier_data[0]) if supplier_data[0] else ''
                        name_ar = supplier_data[1] if supplier_data[1] else ''
                        name_en = supplier_data[2] if supplier_data[2] else ''
                        address = supplier_data[3] if supplier_data[3] else ''
                        phone = supplier_data[4] if supplier_data[4] else ''
                        email = supplier_data[5] if supplier_data[5] else ''

                        if not name_ar or not code:
                            continue

                        # التحقق من وجود المورد
                        check_sql = "SELECT id FROM suppliers WHERE supplier_code = :1"
                        existing = oracle_mgr.execute_query(check_sql, [code])

                        if existing:
                            # تجاوز المورد الموجود (لا نفعل شيء)
                            continue
                        else:
                            # إضافة مورد جديد
                            insert_sql = """
                            INSERT INTO suppliers (id, name_ar, name_en, supplier_code, phone, email, address)
                            VALUES (suppliers_seq.NEXTVAL, :1, :2, :3, :4, :5, :6)
                            """
                            params = [name_ar, name_en, code, phone, email, address]
                            oracle_mgr.execute_update(insert_sql, params)
                            imported_count += 1

                    except Exception as e:
                        errors.append(f"خطأ في معالجة المورد {code}: {str(e)}")
                        continue

                # حساب الموردين المتجاوزين
                total_processed = len(external_suppliers)
                skipped_count = total_processed - imported_count - len(errors)

                # إرجاع النتائج
                message = f"تم استيراد {imported_count} مورد جديد، تم تجاوز {skipped_count} مورد موجود"
                if errors:
                    message += f". عدد الأخطاء: {len(errors)}"

                return jsonify({
                    'success': True,
                    'message': message,
                    'imported': imported_count,
                    'skipped': skipped_count,
                    'total_processed': total_processed,
                    'errors': errors[:10]  # أول 10 أخطاء فقط
                })

            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': f'خطأ في الاتصال بالمصدر الخارجي: {str(e)}'
                })

        # عرض صفحة الاستيراد
        return render_template('suppliers/import.html', title='استيراد الموردين')

    except Exception as e:
        logger.error(f"خطأ في استيراد الموردين: {e}")
        if request.is_json:
            return jsonify({'success': False, 'message': f'خطأ: {str(e)}'})
        return render_template('suppliers/import.html', title='استيراد الموردين')

@bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل المورد"""
    try:
        oracle_mgr = get_oracle_manager()

        # الحصول على بيانات المورد
        supplier_query = """
        SELECT s.id, s.code, s.name_ar, s.name_en, s.commercial_register, s.tax_number,
               s.contact_person, s.phone, s.mobile, s.email, s.website, s.address,
               s.city, s.country, s.postal_code, s.bank_name, s.bank_account, s.iban,
               s.payment_terms, s.credit_limit, s.rating, s.category, s.is_active,
               s.is_approved, s.created_at, s.updated_at, s.notes,
               sg.group_name_ar as group_name
        FROM suppliers s
        LEFT JOIN supplier_groups sg ON s.supplier_group_id = sg.group_id
        WHERE s.id = :1
        """

        supplier_data = oracle_mgr.execute_query(supplier_query, [id])

        if not supplier_data:
            flash('المورد غير موجود', 'error')
            return redirect(url_for('suppliers.index'))

        supplier_info = supplier_data[0]
        supplier = {
            'id': supplier_info[0],
            'code': supplier_info[1],
            'name_ar': supplier_info[2],
            'name_en': supplier_info[3],
            'commercial_register': supplier_info[4],
            'tax_number': supplier_info[5],
            'contact_person': supplier_info[6],
            'phone': supplier_info[7],
            'mobile': supplier_info[8],
            'email': supplier_info[9],
            'website': supplier_info[10],
            'address': supplier_info[11],
            'city': supplier_info[12],
            'country': supplier_info[13],
            'postal_code': supplier_info[14],
            'bank_name': supplier_info[15],
            'bank_account': supplier_info[16],
            'iban': supplier_info[17],
            'payment_terms': supplier_info[18],
            'credit_limit': supplier_info[19],
            'rating': supplier_info[20],
            'category': supplier_info[21],
            'is_active': bool(supplier_info[22]) if supplier_info[22] is not None else True,
            'is_approved': bool(supplier_info[23]) if supplier_info[23] is not None else False,
            'created_at': supplier_info[24],
            'updated_at': supplier_info[25],
            'notes': supplier_info[26],
            'group_name': supplier_info[27]
        }

        # إحصائيات المورد (يمكن إضافتها لاحقاً عند إنشاء جداول أوامر الشراء)
        stats = {
            'total_orders': 0,
            'total_amount': 0,
            'last_order_date': None,
            'average_rating': supplier['rating'] or 0
        }

        return render_template('suppliers/view.html',
                             supplier=supplier,
                             stats=stats,
                             title=f'تفاصيل المورد - {supplier["name_ar"]}')

    except Exception as e:
        logger.error(f"خطأ في عرض المورد {id}: {e}")
        flash(f'خطأ في عرض المورد: {str(e)}', 'error')
        return redirect(url_for('suppliers.index'))

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """تعديل المورد"""
    try:
        oracle_mgr = get_oracle_manager()

        if request.method == 'POST':
            # معالجة تحديث البيانات
            data = request.get_json() if request.is_json else request.form

            name_ar = data.get('name_ar', '').strip()
            name_en = data.get('name_en', '').strip()
            supplier_code = data.get('supplier_code', '').strip()
            commercial_register = data.get('commercial_register', '').strip()
            tax_number = data.get('tax_number', '').strip()
            contact_person = data.get('contact_person', '').strip()
            phone = data.get('phone', '').strip()
            mobile = data.get('mobile', '').strip()
            email = data.get('email', '').strip()
            website = data.get('website', '').strip()
            address = data.get('address', '').strip()
            city = data.get('city', '').strip()
            country = data.get('country', '').strip()
            postal_code = data.get('postal_code', '').strip()
            supplier_type = data.get('supplier_type', '').strip()
            supplier_group_id = data.get('supplier_group_id', '')
            bank_name = data.get('bank_name', '').strip()
            bank_account = data.get('bank_account', '').strip()
            iban = data.get('iban', '').strip()
            payment_terms = data.get('payment_terms', 30)
            credit_limit = data.get('credit_limit', 0)
            notes = data.get('notes', '').strip()
            is_active = data.get('is_active', '1') == '1'

            # التحقق من البيانات المطلوبة
            if not name_ar:
                if request.is_json:
                    return jsonify({'success': False, 'message': 'اسم المورد مطلوب'})
                flash('اسم المورد مطلوب', 'error')
                return redirect(url_for('suppliers.edit', id=id))

            # التحقق من عدم تكرار الكود (باستثناء المورد الحالي)
            if supplier_code:
                check_code_sql = "SELECT COUNT(*) FROM suppliers WHERE supplier_code = :1 AND id != :2"
                code_result = oracle_mgr.execute_query(check_code_sql, [supplier_code, id])
                if code_result and code_result[0][0] > 0:
                    if request.is_json:
                        return jsonify({'success': False, 'message': 'كود المورد موجود بالفعل'})
                    flash('كود المورد موجود بالفعل', 'error')
                    return redirect(url_for('suppliers.edit', id=id))

            # تحديث المورد
            update_sql = """
            UPDATE suppliers SET
                name_ar = :1, name_en = :2, supplier_code = :3, commercial_register = :4,
                tax_number = :5, contact_person = :6, phone = :7, mobile = :8, email = :9,
                website = :10, address = :11, city = :12, country = :13, postal_code = :14,
                supplier_type = :15, supplier_group_id = :16, bank_name = :17, bank_account = :18,
                iban = :19, payment_terms = :20, credit_limit = :21, notes = :22,
                is_active = :23, updated_at = SYSDATE, updated_by = :24
            WHERE id = :25
            """

            params = [
                name_ar, name_en, supplier_code, commercial_register, tax_number,
                contact_person, phone, mobile, email, website, address, city, country,
                postal_code, supplier_type,
                int(supplier_group_id) if supplier_group_id else None,
                bank_name, bank_account, iban,
                int(payment_terms) if payment_terms else 30,
                float(credit_limit) if credit_limit else 0,
                notes, 1 if is_active else 0, current_user.id, id
            ]

            rows_affected = oracle_mgr.execute_update(update_sql, params)

            if rows_affected > 0:
                if request.is_json:
                    return jsonify({'success': True, 'message': 'تم تحديث المورد بنجاح'})
                flash('تم تحديث المورد بنجاح', 'success')
                return redirect(url_for('suppliers.view', id=id))
            else:
                if request.is_json:
                    return jsonify({'success': False, 'message': 'فشل في تحديث المورد'})
                flash('فشل في تحديث المورد', 'error')

        # الحصول على بيانات المورد للعرض
        supplier_query = """
        SELECT s.id, s.supplier_code, s.name_ar, s.name_en, s.commercial_register, s.tax_number,
               s.contact_person, s.phone, s.mobile, s.email, s.website, s.address,
               s.city, s.country, s.postal_code, s.supplier_type, s.supplier_group_id,
               s.bank_name, s.bank_account, s.iban, s.payment_terms, s.credit_limit,
               s.notes, s.is_active, sg.group_name_ar as group_name
        FROM suppliers s
        LEFT JOIN supplier_groups sg ON s.supplier_group_id = sg.group_id
        WHERE s.id = :1
        """

        supplier_data = oracle_mgr.execute_query(supplier_query, [id])

        if not supplier_data:
            flash('المورد غير موجود', 'error')
            return redirect(url_for('suppliers.index'))

        supplier_info = supplier_data[0]
        supplier = {
            'id': supplier_info[0],
            'supplier_code': supplier_info[1],
            'name_ar': supplier_info[2],
            'name_en': supplier_info[3],
            'commercial_register': supplier_info[4],
            'tax_number': supplier_info[5],
            'contact_person': supplier_info[6],
            'phone': supplier_info[7],
            'mobile': supplier_info[8],
            'email': supplier_info[9],
            'website': supplier_info[10],
            'address': supplier_info[11],
            'city': supplier_info[12],
            'country': supplier_info[13],
            'postal_code': supplier_info[14],
            'supplier_type': supplier_info[15],
            'supplier_group_id': supplier_info[16],
            'bank_name': supplier_info[17],
            'bank_account': supplier_info[18],
            'iban': supplier_info[19],
            'payment_terms': supplier_info[20],
            'credit_limit': supplier_info[21],
            'notes': supplier_info[22],
            'is_active': bool(supplier_info[23]) if supplier_info[23] is not None else True,
            'group_name': supplier_info[24]
        }

        # الحصول على مجموعات الموردين
        groups_query = "SELECT group_id, group_name_ar FROM supplier_groups WHERE is_active = 1 ORDER BY group_name_ar"
        groups_data = oracle_mgr.execute_query(groups_query)
        supplier_groups = [{'id': g[0], 'name': g[1]} for g in groups_data] if groups_data else []

        return render_template('suppliers/edit.html',
                             supplier=supplier,
                             supplier_groups=supplier_groups,
                             title=f'تعديل المورد - {supplier["name_ar"]}')

    except Exception as e:
        logger.error(f"خطأ في تعديل المورد {id}: {e}")
        if request.is_json:
            return jsonify({'success': False, 'message': f'خطأ: {str(e)}'})
        flash(f'خطأ في تعديل المورد: {str(e)}', 'error')
        return redirect(url_for('suppliers.index'))

@bp.route('/<int:id>/edit_simple')
@login_required
def edit_simple(id):
    """صفحة تعديل المورد المبسطة"""
    try:
        oracle_mgr = get_oracle_manager()

        # الحصول على بيانات المورد
        supplier_query = """
        SELECT s.id, s.supplier_code, s.name_ar, s.name_en, s.phone, s.email,
               s.city, s.supplier_type, s.is_active
        FROM suppliers s
        WHERE s.id = :1
        """

        supplier_data = oracle_mgr.execute_query(supplier_query, [id])

        if not supplier_data:
            return f"المورد رقم {id} غير موجود", 404

        supplier_info = supplier_data[0]
        supplier = {
            'id': supplier_info[0],
            'supplier_code': supplier_info[1],
            'name_ar': supplier_info[2],
            'name_en': supplier_info[3],
            'phone': supplier_info[4],
            'email': supplier_info[5],
            'city': supplier_info[6],
            'supplier_type': supplier_info[7],
            'is_active': bool(supplier_info[8]) if supplier_info[8] is not None else True
        }

        return f"""
        <html dir="rtl">
        <head>
            <title>تعديل المورد - {supplier['name_ar']}</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        </head>
        <body>
            <div class="container mt-4">
                <h1>تعديل المورد: {supplier['name_ar']}</h1>
                <div class="card">
                    <div class="card-body">
                        <p><strong>رقم المورد:</strong> {supplier['id']}</p>
                        <p><strong>كود المورد:</strong> {supplier['supplier_code'] or 'غير محدد'}</p>
                        <p><strong>اسم المورد (عربي):</strong> {supplier['name_ar']}</p>
                        <p><strong>اسم المورد (إنجليزي):</strong> {supplier['name_en'] or 'غير محدد'}</p>
                        <p><strong>الهاتف:</strong> {supplier['phone'] or 'غير محدد'}</p>
                        <p><strong>البريد الإلكتروني:</strong> {supplier['email'] or 'غير محدد'}</p>
                        <p><strong>المدينة:</strong> {supplier['city'] or 'غير محدد'}</p>
                        <p><strong>نوع المورد:</strong> {supplier['supplier_type'] or 'غير محدد'}</p>
                        <p><strong>الحالة:</strong> {'نشط' if supplier['is_active'] else 'غير نشط'}</p>

                        <div class="mt-4">
                            <a href="/suppliers/" class="btn btn-secondary">العودة لقائمة الموردين</a>
                            <a href="/suppliers/{id}/edit" class="btn btn-primary">تعديل كامل</a>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        """

    except Exception as e:
        logger.error(f"خطأ في عرض المورد {id}: {e}")
        return f"خطأ في عرض المورد: {str(e)}", 500

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """حذف المورد"""
    return jsonify({'success': False, 'message': 'وظيفة الحذف قيد التطوير'})

# API Routes
@bp.route('/api/suppliers', methods=['GET'])
@login_required
def api_get_suppliers():
    """API للحصول على قائمة الموردين"""
    try:
        oracle_mgr = get_oracle_manager()

        query = """
        SELECT s.id, s.supplier_code, s.name_ar, s.name_en, s.phone, s.email,
               s.city, s.supplier_type, s.is_active, sg.group_name_ar
        FROM suppliers s
        LEFT JOIN supplier_groups sg ON s.supplier_group_id = sg.group_id
        ORDER BY s.name_ar
        """

        suppliers_data = oracle_mgr.execute_query(query)

        suppliers = []
        for supplier_data in suppliers_data:
            supplier_dict = {
                'id': supplier_data[0],
                'code': supplier_data[1],
                'name_ar': supplier_data[2],
                'name_en': supplier_data[3],
                'phone': supplier_data[4],
                'email': supplier_data[5],
                'city': supplier_data[6],
                'category': supplier_data[7],  # supplier_type
                'is_active': bool(supplier_data[8]) if supplier_data[8] is not None else True,
                'group_name': supplier_data[9]
            }
            suppliers.append(supplier_dict)

        return jsonify({
            'success': True,
            'suppliers': suppliers,
            'total': len(suppliers)
        })

    except Exception as e:
        logger.error(f"خطأ في API الموردين: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

# دوال مؤقتة للوظائف المتقدمة - سيتم تطويرها لاحقاً
@bp.route('/contracts')
@login_required
def contracts():
    """صفحة العقود - قيد التطوير"""
    return render_template('suppliers/temp_index.html',
                         message="صفحة العقود قيد التطوير")

@bp.route('/evaluations')
@login_required
def evaluations():
    """صفحة تقييمات الموردين - قيد التطوير"""
    return render_template('suppliers/temp_index.html',
                         message="صفحة تقييمات الموردين قيد التطوير")

@bp.route('/reports/performance')
@login_required
def performance_report():
    """تقرير أداء الموردين - قيد التطوير"""
    return render_template('suppliers/temp_index.html',
                         message="تقرير أداء الموردين قيد التطوير")

@bp.route('/purchase_orders_integration')
@login_required
def purchase_orders_integration():
    """صفحة التكامل بين أوامر الشراء والموردين والحوالات"""
    return render_template('suppliers/purchase_orders_integration.html')

@bp.route('/balances_management')
@login_required
def balances_management():
    """نظام إدارة أرصدة الموردين المتقدم"""
    try:
        # جلب البيانات الحقيقية من قاعدة البيانات
        dashboard_data = get_real_balances_data()
        return render_template('suppliers/balances_management.html', data=dashboard_data)
    except Exception as e:
        logger.error(f"خطأ في جلب بيانات الأرصدة: {e}")
        flash('حدث خطأ في جلب البيانات', 'error')
        return render_template('suppliers/balances_management.html', data=None)

def get_real_balances_data():
    """جلب البيانات الحقيقية لأرصدة الموردين"""
    try:
        # محاولة الاتصال بقاعدة البيانات
        from oracle_manager import get_oracle_manager
        oracle_mgr = get_oracle_manager()

        if not oracle_mgr.connect():
            logger.warning("فشل في الاتصال بقاعدة البيانات، استخدام البيانات الافتراضية")
            return get_default_balances_data()

        # محاولة جلب البيانات من النظام المركزي الموحد CURRENT_BALANCES
        try:
            return get_data_from_supplier_balances(oracle_mgr)
        except Exception as e:
            logger.warning(f"فشل في جلب البيانات من الجداول: {e}")
            oracle_mgr.disconnect()
            return get_default_balances_data()

        # استعلام إجمالي الأرصدة من جداول حقيقية
        total_balance_query = """
            SELECT
                COUNT(DISTINCT s.id) as total_suppliers,
                COALESCE(SUM(CASE WHEN cb.current_balance > 0 THEN cb.current_balance ELSE 0 END), 0) as total_positive_balance,
                COALESCE(SUM(CASE WHEN cb.current_balance < 0 THEN ABS(cb.current_balance) ELSE 0 END), 0) as total_outstanding,
                0 as overdue_amount
            FROM SUPPLIERS s
            LEFT JOIN CURRENT_BALANCES cb ON s.id = cb.entity_id AND cb.entity_type_code = 'SUPPLIER'
            WHERE s.is_active = 1
        """

        balance_summary = oracle_mgr.execute_query(total_balance_query)

        # استعلام تحليل الاستحقاقات
        aging_query = """
            SELECT
                COALESCE(SUM(CASE WHEN bt.document_date >= SYSDATE - 30 THEN bt.debit_amount - bt.credit_amount ELSE 0 END), 0) as current_due,
                COALESCE(SUM(CASE WHEN bt.document_date < SYSDATE - 30 AND bt.document_date >= SYSDATE - 60 THEN bt.debit_amount - bt.credit_amount ELSE 0 END), 0) as overdue_1_30,
                COALESCE(SUM(CASE WHEN bt.document_date < SYSDATE - 60 AND bt.document_date >= SYSDATE - 90 THEN bt.debit_amount - bt.credit_amount ELSE 0 END), 0) as overdue_31_60,
                COALESCE(SUM(CASE WHEN bt.document_date < SYSDATE - 90 AND bt.document_date >= SYSDATE - 120 THEN bt.debit_amount - bt.credit_amount ELSE 0 END), 0) as overdue_61_90,
                COALESCE(SUM(CASE WHEN bt.document_date < SYSDATE - 120 THEN bt.debit_amount - bt.credit_amount ELSE 0 END), 0) as overdue_over_90
            FROM BALANCE_TRANSACTIONS bt
            WHERE bt.entity_type_code = 'SUPPLIER'
            AND bt.status = 'POSTED'
            AND bt.document_type_code IN ('PURCHASE_INVOICE', 'DEBIT_NOTE')
            AND bt.debit_amount - bt.credit_amount > 0
        """

        aging_data = oracle_mgr.execute_query(aging_query)

        # استعلام توزيع العملات
        currency_query = """
            SELECT
                cb.currency_code,
                COALESCE(SUM(ABS(cb.current_balance)), 0) as total_amount
            FROM CURRENT_BALANCES cb
            WHERE cb.entity_type_code = 'SUPPLIER'
            AND cb.current_balance != 0
            AND cb.currency_code IS NOT NULL
            GROUP BY cb.currency_code
            ORDER BY total_amount DESC
        """

        currency_data = oracle_mgr.execute_query(currency_query)

        # استعلام بيانات الموردين من الجدول الحقيقي
        suppliers_query = """
            SELECT
                s.id,
                s.supplier_code,
                s.name_ar,
                NVL(s.supplier_type, 'TRADE') as supplier_type,
                NVL(cb.current_balance, 0) as balance_amount,
                NVL(cb.currency_code, 'USD') as currency_code,
                NVL(s.rating, 'MEDIUM') as risk_rating,
                NVL(cb.last_transaction_date, s.created_at) as last_transaction_date,
                0 as overdue_amount
            FROM SUPPLIERS s
            LEFT JOIN CURRENT_BALANCES cb ON TO_CHAR(s.supplier_code) = TO_CHAR(cb.entity_id)
                AND cb.entity_type_code = 'SUPPLIER'
            WHERE s.is_active = 1
            AND s.supplier_code IS NOT NULL
            ORDER BY NVL(cb.current_balance, 0) DESC
            FETCH FIRST 20 ROWS ONLY
        """

        suppliers_data = oracle_mgr.execute_query(suppliers_query)

        # استعلام الاتجاهات الشهرية
        trends_query = """
            SELECT
                TO_CHAR(bt.document_date, 'YYYY-MM') as month_year,
                COALESCE(SUM(bt.credit_amount), 0) as total_balance,
                COALESCE(SUM(bt.debit_amount), 0) as outstanding_amount
            FROM BALANCE_TRANSACTIONS bt
            WHERE bt.entity_type_code = 'SUPPLIER'
            AND bt.status = 'POSTED'
            AND bt.document_date >= ADD_MONTHS(SYSDATE, -6)
            GROUP BY TO_CHAR(bt.document_date, 'YYYY-MM')
            ORDER BY month_year
        """

        trends_data = oracle_mgr.execute_query(trends_query)

        # تنظيم البيانات
        balance_row = balance_summary[0] if balance_summary else [0, 0, 0, 0]
        aging_row = aging_data[0] if aging_data else [0, 0, 0, 0, 0]

        dashboard_data = {
            'statistics': {
                'total_suppliers': int(balance_row[0]) if balance_row[0] else 0,
                'total_balance': float(balance_row[1]) if balance_row[1] else 0,
                'total_outstanding': float(balance_row[2]) if balance_row[2] else 0,
                'overdue_amount': float(balance_row[3]) if balance_row[3] else 0,
                'aging_analysis': {
                    'current_due': float(aging_row[0]) if aging_row[0] else 0,
                    'overdue_1_30': float(aging_row[1]) if aging_row[1] else 0,
                    'overdue_31_60': float(aging_row[2]) if aging_row[2] else 0,
                    'overdue_61_90': float(aging_row[3]) if aging_row[3] else 0,
                    'overdue_over_90': float(aging_row[4]) if aging_row[4] else 0
                }
            },
            'charts': {
                'by_currency': [
                    {
                        'currency': get_currency_name(row[0]),
                        'amount': float(row[1]),
                        'code': row[0]
                    } for row in currency_data
                ] if currency_data else [],
                'trends': [
                    {
                        'month': format_month_name(row[0]),
                        'total_balance': float(row[1]),
                        'outstanding_amount': float(row[2])
                    } for row in trends_data
                ] if trends_data else []
            },
            'suppliers': [
                {
                    'supplier_id': row[0],
                    'supplier_code': row[1],
                    'supplier_name': row[2],
                    'supplier_type': get_supplier_type_name(row[3]),
                    'balance_amount': float(row[4]) if row[4] else 0,
                    'currency_code': row[5] or 'SAR',
                    'risk_rating': get_risk_rating_name(row[6]),
                    'last_transaction_date': row[7].strftime('%Y-%m-%d') if hasattr(row[7], 'strftime') else str(row[7])[:10],
                    'overdue_amount': float(row[8]) if row[8] else 0
                } for row in suppliers_data
            ] if suppliers_data else []
        }

        oracle_mgr.disconnect()

        return dashboard_data

    except Exception as e:
        logger.error(f"خطأ في جلب البيانات الحقيقية: {e}")
        # إرجاع بيانات افتراضية في حالة الخطأ
        return get_default_balances_data()

def get_currency_name(currency_code):
    """تحويل كود العملة إلى اسم باللغة العربية"""
    currency_names = {
        'SAR': 'ريال سعودي',
        'USD': 'دولار أمريكي',
        'EUR': 'يورو',
        'GBP': 'جنيه إسترليني',
        'AED': 'درهم إماراتي'
    }
    return currency_names.get(currency_code, currency_code)

def format_month_name(month_year):
    """تحويل الشهر إلى اسم باللغة العربية"""
    try:
        year, month = month_year.split('-')
        month_names = {
            '01': 'يناير', '02': 'فبراير', '03': 'مارس', '04': 'أبريل',
            '05': 'مايو', '06': 'يونيو', '07': 'يوليو', '08': 'أغسطس',
            '09': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
        }
        return f"{month_names.get(month, month)} {year}"
    except:
        return month_year

def get_data_from_supplier_balances(oracle_mgr):
    """جلب البيانات الحقيقية من النظام المركزي الموحد CURRENT_BALANCES"""
    try:
        # جلب إحصائيات الأرصدة من النظام المركزي الموحد
        stats_query = """
        SELECT
            COUNT(DISTINCT cb.entity_id) as total_suppliers,
            COALESCE(SUM(cb.current_balance), 0) as total_balance,
            COALESCE(SUM(CASE WHEN cb.current_balance > 0 THEN cb.current_balance ELSE 0 END), 0) as total_debit,
            COALESCE(SUM(CASE WHEN cb.current_balance < 0 THEN ABS(cb.current_balance) ELSE 0 END), 0) as total_credit
        FROM CURRENT_BALANCES cb
        WHERE cb.entity_type_code = 'SUPPLIER'
        AND cb.current_balance != 0
        AND cb.currency_code IS NOT NULL
        """

        stats_result = oracle_mgr.execute_query(stats_query)
        stats = stats_result[0] if stats_result else [0, 0, 0, 0]

        # جلب بيانات الموردين مع أرصدتهم من النظام المركزي الموحد
        suppliers_query = """
        SELECT
            s.id,
            s.supplier_code,
            s.name_ar,
            s.supplier_type,
            cb.current_balance,
            cb.currency_code,
            s.rating,
            cb.updated_at,
            0 as overdue_amount
        FROM SUPPLIERS s
        JOIN CURRENT_BALANCES cb ON TO_CHAR(s.supplier_code) = TO_CHAR(cb.entity_id)
        WHERE cb.entity_type_code = 'SUPPLIER'
        AND cb.current_balance != 0
        ORDER BY ABS(cb.current_balance) DESC
        """

        suppliers_data = oracle_mgr.execute_query(suppliers_query)

        # جلب توزيع الأرصدة حسب العملة من النظام المركزي (العملات الفعلية فقط)
        currency_query = """
        SELECT
            cb.currency_code,
            COALESCE(SUM(cb.current_balance), 0) as total_amount,
            COUNT(*) as suppliers_count
        FROM CURRENT_BALANCES cb
        WHERE cb.entity_type_code = 'SUPPLIER'
        AND cb.current_balance != 0
        AND cb.currency_code IS NOT NULL
        AND cb.currency_code != ''
        GROUP BY cb.currency_code
        ORDER BY ABS(SUM(cb.current_balance)) DESC
        """

        currency_data = oracle_mgr.execute_query(currency_query)

        # تنسيق البيانات
        dashboard_data = {
            'statistics': {
                'total_balance': float(stats[1]) if stats[1] else 0,
                'total_outstanding': float(stats[2]) if stats[2] else 0,
                'total_suppliers': int(stats[0]) if stats[0] else 0,
                'overdue_amount': 0,  # سيتم حسابه لاحقاً من جدول المعاملات
                'aging_analysis': {
                    'current_due': float(stats[2]) if stats[2] else 0,
                    'overdue_1_30': 0,
                    'overdue_31_60': 0,
                    'overdue_61_90': 0,
                    'overdue_over_90': 0
                }
            },
            'charts': {
                'by_currency': [
                    {
                        'currency': row[0],
                        'amount': float(row[1]) if row[1] else 0,
                        'suppliers_count': int(row[2]) if row[2] else 0
                    } for row in currency_data if row[0] and row[0].strip()
                ] if currency_data else [],
                'trends': []  # سيتم إضافتها لاحقاً
            },
            'suppliers': [
                {
                    'supplier_id': row[0],
                    'supplier_code': row[1],
                    'supplier_name': row[2],
                    'supplier_type': get_supplier_type_name(row[3]),
                    'balance_amount': float(row[4]) if row[4] else 0,
                    'currency_code': row[5],
                    'risk_rating': get_risk_rating_name(row[6]),
                    'last_transaction_date': row[7].strftime('%Y-%m-%d') if hasattr(row[7], 'strftime') else str(row[7])[:10] if row[7] else '2024-01-01',
                    'overdue_amount': float(row[8]) if row[8] else 0
                } for row in suppliers_data
            ] if suppliers_data else []
        }

        return dashboard_data

    except Exception as e:
        logger.error(f"خطأ في جلب البيانات من SUPPLIER_BALANCES: {e}")
        raise e

def get_default_balances_data():
    """بيانات افتراضية في حالة عدم توفر البيانات الحقيقية"""
    return {
        'statistics': {
            'total_balance': 0,
            'total_outstanding': 0,
            'total_suppliers': 0,
            'overdue_amount': 0,
            'aging_analysis': {
                'current_due': 0,
                'overdue_1_30': 0,
                'overdue_31_60': 0,
                'overdue_61_90': 0,
                'overdue_over_90': 0
            }
        },
        'charts': {
            'by_currency': [],
            'trends': []
        },
        'suppliers': []
    }

@bp.route('/api/balances_data')
@login_required
def api_balances_data():
    """API لجلب بيانات الأرصدة بشكل ديناميكي"""
    try:
        # جلب المعاملات من الطلب
        period = request.args.get('period', 'current_month')
        currency = request.args.get('currency', 'all')
        balance_status = request.args.get('balance_status', 'all')
        supplier_type = request.args.get('supplier_type', 'all')
        risk_rating = request.args.get('risk_rating', 'all')
        search_term = request.args.get('search', '')

        # جلب البيانات المفلترة
        filtered_data = get_filtered_balances_data(
            period, currency, balance_status, supplier_type, risk_rating, search_term
        )

        return jsonify({
            'success': True,
            'data': filtered_data
        })

    except Exception as e:
        logger.error(f"خطأ في API البيانات: {e}")

        # تحسين رسائل الخطأ للمستخدم
        error_message = str(e)
        user_friendly_message = "حدث خطأ أثناء جلب البيانات"

        if "ORA-00942" in error_message:
            user_friendly_message = "جدول البيانات غير موجود. يرجى الاتصال بالدعم الفني."
        elif "ORA-01017" in error_message:
            user_friendly_message = "خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة مرة أخرى."
        elif "connection" in error_message.lower():
            user_friendly_message = "مشكلة في الاتصال بقاعدة البيانات. يرجى المحاولة مرة أخرى."
        elif "timeout" in error_message.lower():
            user_friendly_message = "انتهت مهلة العملية. يرجى المحاولة مرة أخرى."

        return jsonify({
            'success': False,
            'message': user_friendly_message,
            'technical_details': error_message if current_user.is_authenticated and hasattr(current_user, 'is_admin') and current_user.is_admin else None
        }), 500

def get_filtered_balances_data(period, currency, balance_status, supplier_type, risk_rating, search_term):
    """جلب البيانات المفلترة حسب المعايير المحددة"""
    try:
        from oracle_manager import get_oracle_manager
        oracle_mgr = get_oracle_manager()

        if not oracle_mgr.connect():
            logger.error("فشل في الاتصال بقاعدة البيانات")
            return {'statistics': {'total_balance': 0, 'total_outstanding': 0, 'total_suppliers': 0, 'overdue_amount': 0}, 'suppliers': []}

        # استعلام مبسط للاختبار
        where_clause = "s.IS_ACTIVE = 1"

        # تطبيق فلتر البحث إذا وجد
        if search_term:
            where_clause += f" AND UPPER(s.SUPPLIER_NAME) LIKE UPPER('%{search_term}%')"

        suppliers_query = f"""
            SELECT
                s.id,
                s.supplier_code,
                s.name_ar,
                NVL(s.supplier_type, 'TRADE') as supplier_type,
                COALESCE(SUM(cb.current_balance), 0) as balance_amount,
                cb.currency_code,
                NVL(s.rating, 'MEDIUM') as risk_rating,
                NVL(cb.last_transaction_date, SYSDATE) as last_transaction_date,
                0 as overdue_amount
            FROM SUPPLIERS s
            LEFT JOIN CURRENT_BALANCES cb ON s.id = cb.entity_id AND cb.entity_type_code = 'SUPPLIER'
            WHERE {where_clause}
            GROUP BY s.id, s.supplier_code, s.name_ar, s.supplier_type,
                     cb.currency_code, s.rating, cb.last_transaction_date
            ORDER BY balance_amount DESC
            FETCH FIRST 50 ROWS ONLY
        """

        suppliers_data = oracle_mgr.execute_query(suppliers_query)

        # حساب الإحصائيات المفلترة
        stats_query = f"""
            SELECT
                COALESCE(SUM(CASE WHEN cb.current_balance > 0 THEN cb.current_balance ELSE 0 END), 0) as total_positive,
                COALESCE(SUM(CASE WHEN cb.current_balance < 0 THEN ABS(cb.current_balance) ELSE 0 END), 0) as total_outstanding,
                COUNT(DISTINCT s.id) as total_suppliers,
                0 as overdue_amount
            FROM SUPPLIERS s
            LEFT JOIN CURRENT_BALANCES cb ON s.id = cb.entity_id AND cb.entity_type_code = 'SUPPLIER'
            WHERE {where_clause}
        """

        stats_result = oracle_mgr.execute_query(stats_query)
        stats_data = stats_result[0] if stats_result else [0, 0, 0, 0]

        # تنظيم البيانات
        result = {
            'statistics': {
                'total_balance': float(stats_data[0]) if stats_data[0] else 0,
                'total_outstanding': float(stats_data[1]) if stats_data[1] else 0,
                'total_suppliers': int(stats_data[2]) if stats_data[2] else 0,
                'overdue_amount': float(stats_data[3]) if stats_data[3] else 0
            },
            'suppliers': [
                {
                    'supplier_id': row[0],
                    'supplier_code': row[1],
                    'supplier_name': row[2],
                    'supplier_type': get_supplier_type_name(row[3]),
                    'balance_amount': float(row[4]) if row[4] else 0,
                    'currency_code': row[5] or 'SAR',
                    'risk_rating': get_risk_rating_name(row[6]),
                    'last_transaction_date': row[7].strftime('%Y-%m-%d') if row[7] else '',
                    'overdue_amount': float(row[8]) if row[8] else 0
                } for row in suppliers_data
            ] if suppliers_data else []
        }

        oracle_mgr.disconnect()

        return result

    except Exception as e:
        logger.error(f"خطأ في جلب البيانات المفلترة: {e}")
        return {
            'statistics': {'total_balance': 0, 'total_outstanding': 0, 'total_suppliers': 0, 'overdue_amount': 0},
            'suppliers': []
        }

def get_supplier_type_name(supplier_type):
    """تحويل نوع المورد إلى اسم باللغة العربية"""
    type_names = {
        'TRADE': 'تجاري',
        'SERVICE': 'خدمي',
        'CONTRACTOR': 'مقاول',
        'GOVERNMENT': 'حكومي'
    }
    return type_names.get(supplier_type, supplier_type)

def get_risk_rating_name(risk_rating):
    """تحويل تقييم المخاطر إلى اسم باللغة العربية"""
    risk_names = {
        'LOW': 'منخفض',
        'MEDIUM': 'متوسط',
        'HIGH': 'عالي'
    }
    return risk_names.get(risk_rating, risk_rating)



@bp.route('/account/<int:account_id>')
@login_required
def account_details(account_id):
    """تفاصيل حساب مورد محدد"""
    return render_template('suppliers/account_details.html', account_id=account_id)

@bp.route('/statements')
@login_required
def statements_management():
    """نظام إدارة كشوفات الحساب"""
    return render_template('suppliers/statements_management.html')

@bp.route('/statements/generate')
@login_required
def generate_statement():
    """إنشاء كشف حساب جديد"""
    return render_template('suppliers/generate_statement.html')

@bp.route('/reconciliation')
@login_required
def reconciliation_management():
    """نظام إدارة المطابقة"""
    return render_template('suppliers/reconciliation_management.html')

@bp.route('/reconciliation/cycle/<int:cycle_id>')
@login_required
def reconciliation_cycle_details(cycle_id):
    """تفاصيل دورة المطابقة"""
    return render_template('suppliers/reconciliation_cycle_details.html', cycle_id=cycle_id)

@bp.route('/payments')
@login_required
def payments_management():
    """نظام إدارة المدفوعات المتكاملة"""
    return render_template('suppliers/payments_management.html')

# APIs التكامل مع أوامر الشراء
@bp.route('/api/purchase-orders/dashboard-stats')
@login_required
def api_purchase_orders_dashboard_stats():
    """API للحصول على إحصائيات لوحة المعلومات"""
    try:
        oracle_mgr = get_oracle_manager()

        # إحصائيات عامة - استعلام آمن مع الحقول الصحيحة
        general_stats = oracle_mgr.execute_query("""
            SELECT
                COUNT(*) as total_orders,
                COUNT(CASE WHEN NVL(payment_status, 'PENDING') = 'PAID' THEN 1 END) as paid_orders,
                COUNT(CASE WHEN NVL(payment_status, 'PENDING') = 'PENDING' THEN 1 END) as pending_orders,
                COUNT(CASE WHEN NVL(payment_status, 'PENDING') = 'OVERDUE' THEN 1 END) as overdue_orders,
                SUM(NVL(total_amount, 0)) as total_amount,
                SUM(NVL(paid_amount, 0)) as total_paid,
                SUM(NVL(outstanding_amount, 0)) as total_outstanding
            FROM PURCHASE_ORDERS
            WHERE NVL(status, 'ACTIVE') != 'CANCELLED'
        """)

        # إحصائيات الموردين - استعلام آمن مع الحقول الصحيحة
        supplier_stats = oracle_mgr.execute_query("""
            SELECT
                COUNT(DISTINCT supplier_code) as active_suppliers,
                AVG(NVL(outstanding_amount, 0)) as avg_outstanding
            FROM PURCHASE_ORDERS
            WHERE NVL(status, 'ACTIVE') != 'CANCELLED'
            AND NVL(outstanding_amount, 0) > 0
            AND supplier_code IS NOT NULL
        """)

        # معالجة النتائج بأمان
        if general_stats and len(general_stats) > 0:
            stats = general_stats[0]
        else:
            stats = {}

        if supplier_stats and len(supplier_stats) > 0:
            supplier_data = supplier_stats[0]
        else:
            supplier_data = {}

        # إنشاء الاستجابة مع قيم افتراضية آمنة
        dashboard_stats = {
            'general_statistics': {
                'total_orders': int(stats.get('TOTAL_ORDERS', 0) or 0),
                'paid_orders': int(stats.get('PAID_ORDERS', 0) or 0),
                'pending_orders': int(stats.get('PENDING_ORDERS', 0) or 0),
                'overdue_orders': int(stats.get('OVERDUE_ORDERS', 0) or 0),
                'total_amount': float(stats.get('TOTAL_AMOUNT', 0) or 0),
                'total_paid': float(stats.get('TOTAL_PAID', 0) or 0),
                'total_outstanding': float(stats.get('TOTAL_OUTSTANDING', 0) or 0)
            },
            'supplier_statistics': {
                'active_suppliers': int(supplier_data.get('ACTIVE_SUPPLIERS', 0) or 0),
                'avg_outstanding': float(supplier_data.get('AVG_OUTSTANDING', 0) or 0)
            }
        }

        return jsonify({
            'success': True,
            'dashboard_stats': dashboard_stats
        })

    except Exception as e:
        logger.error(f"خطأ في جلب إحصائيات لوحة المعلومات: {e}")
        # إرجاع بيانات افتراضية في حالة الخطأ
        return jsonify({
            'success': True,
            'dashboard_stats': {
                'general_statistics': {
                    'total_orders': 0,
                    'paid_orders': 0,
                    'pending_orders': 0,
                    'overdue_orders': 0,
                    'total_amount': 0.0,
                    'total_paid': 0.0,
                    'total_outstanding': 0.0
                },
                'supplier_statistics': {
                    'active_suppliers': 0,
                    'avg_outstanding': 0.0
                }
            },
            'error_message': f'تحذير: {str(e)}'
        })

@bp.route('/api/purchase-orders/dashboard-stats-simple')
@login_required
def api_purchase_orders_dashboard_stats_simple():
    """API مبسط للحصول على إحصائيات لوحة المعلومات"""
    try:
        oracle_mgr = get_oracle_manager()

        # استعلام مبسط للعد فقط
        count_query = oracle_mgr.execute_query("""
            SELECT COUNT(*) as total_count
            FROM PURCHASE_ORDERS
        """)

        total_orders = count_query[0]['TOTAL_COUNT'] if count_query else 0

        return jsonify({
            'success': True,
            'dashboard_stats': {
                'general_statistics': {
                    'total_orders': total_orders,
                    'paid_orders': 0,
                    'pending_orders': total_orders,
                    'overdue_orders': 0,
                    'total_amount': 0.0,
                    'total_paid': 0.0,
                    'total_outstanding': 0.0
                },
                'supplier_statistics': {
                    'active_suppliers': 0,
                    'avg_outstanding': 0.0
                }
            }
        })

    except Exception as e:
        logger.error(f"خطأ في API المبسط: {e}")
        return jsonify({
            'success': True,
            'dashboard_stats': {
                'general_statistics': {
                    'total_orders': 0,
                    'paid_orders': 0,
                    'pending_orders': 0,
                    'overdue_orders': 0,
                    'total_amount': 0.0,
                    'total_paid': 0.0,
                    'total_outstanding': 0.0
                },
                'supplier_statistics': {
                    'active_suppliers': 0,
                    'avg_outstanding': 0.0
                }
            }
        })

@bp.route('/api/purchase-orders/test')
@login_required
def api_purchase_orders_test():
    """API اختبار للتأكد من الاتصال"""
    try:
        oracle_mgr = get_oracle_manager()

        # اختبار بسيط
        test_query = oracle_mgr.execute_query("""
            SELECT COUNT(*) as total_count
            FROM PURCHASE_ORDERS
        """)

        return jsonify({
            'success': True,
            'message': 'الاتصال يعمل بنجاح',
            'total_purchase_orders': test_query[0]['TOTAL_COUNT'] if test_query else 0
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في الاختبار: {str(e)}'
        })

@bp.route('/api/suppliers')
@login_required
def api_suppliers_list():
    """API للحصول على قائمة الموردين للتكامل"""
    try:
        oracle_mgr = get_oracle_manager()

        # جلب الموردين من جدول PURCHASE_ORDERS مباشرة
        suppliers = oracle_mgr.execute_query("""
            SELECT DISTINCT
                supplier_code as id,
                supplier_code,
                supplier_name as name_ar,
                supplier_name as name_en,
                'غير محدد' as city,
                'عام' as supplier_type
            FROM PURCHASE_ORDERS
            WHERE supplier_code IS NOT NULL
            AND supplier_name IS NOT NULL
            ORDER BY supplier_name
        """)

        suppliers_list = []
        for supplier in suppliers:
            suppliers_list.append({
                'id': supplier['ID'],
                'supplier_code': supplier['SUPPLIER_CODE'],
                'name_ar': supplier['NAME_AR'],
                'name_en': supplier['NAME_EN'],
                'city': supplier['CITY'],
                'supplier_type': supplier['SUPPLIER_TYPE']
            })

        return jsonify({
            'success': True,
            'suppliers': suppliers_list
        })

    except Exception as e:
        logger.error(f"خطأ في جلب قائمة الموردين: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

@bp.route('/api/money-changers-banks')
@login_required
def api_money_changers_banks():
    """API للحصول على قائمة الصرافين والبنوك"""
    try:
        # بيانات تجريبية - يمكن تطويرها لاحقاً
        money_changers = [
            {'id': 1, 'name_ar': 'البنك الأهلي', 'type': 'بنك'},
            {'id': 2, 'name_ar': 'بنك الراجحي', 'type': 'بنك'},
            {'id': 3, 'name_ar': 'صرافة الحرمين', 'type': 'صرافة'},
            {'id': 4, 'name_ar': 'صرافة الأمان', 'type': 'صرافة'}
        ]

        return jsonify({
            'success': True,
            'money_changers_banks': money_changers
        })

    except Exception as e:
        logger.error(f"خطأ في جلب قائمة الصرافين: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

@bp.route('/api/purchase-orders/outstanding/<int:supplier_id>')
@login_required
def api_purchase_orders_outstanding(supplier_id):
    """API للحصول على أوامر الشراء المستحقة للمورد"""
    try:
        oracle_mgr = get_oracle_manager()

        outstanding_orders = oracle_mgr.execute_query("""
            SELECT
                po.id as purchase_order_id,
                po.po_number,
                po.title,
                po.po_date,
                po.delivery_date,
                po.payment_due_date,
                po.total_amount as total_amount_due,
                po.paid_amount,
                po.outstanding_amount,
                po.currency,
                po.payment_status,
                po.supplier_code,
                po.supplier_name,
                CASE
                    WHEN po.payment_due_date < SYSDATE THEN 'متأخر'
                    WHEN po.payment_due_date <= SYSDATE + 7 THEN 'مستحق قريباً'
                    ELSE 'مستحق'
                END as due_status,
                CASE
                    WHEN NVL(po.outstanding_amount, 0) > 10000 THEN 'عاجل جداً'
                    WHEN NVL(po.outstanding_amount, 0) > 5000 THEN 'عاجل'
                    WHEN NVL(po.outstanding_amount, 0) > 1000 THEN 'مهم'
                    ELSE 'عادي'
                END as payment_priority
            FROM PURCHASE_ORDERS po
            WHERE po.supplier_code = :supplier_code
            AND NVL(po.outstanding_amount, 0) > 0
            AND NVL(po.status, 'ACTIVE') != 'CANCELLED'
            ORDER BY po.payment_due_date ASC, NVL(po.outstanding_amount, 0) DESC
        """, {'supplier_code': supplier_id})

        # تحويل النتائج للتنسيق المطلوب
        formatted_orders = []
        for order in outstanding_orders:
            formatted_orders.append({
                'purchase_order_id': order['PURCHASE_ORDER_ID'],
                'po_number': order['PO_NUMBER'],
                'title': order['TITLE'],
                'po_date': order['PO_DATE'].strftime('%Y-%m-%d') if order['PO_DATE'] else None,
                'delivery_date': order['DELIVERY_DATE'].strftime('%Y-%m-%d') if order['DELIVERY_DATE'] else None,
                'payment_due_date': order['PAYMENT_DUE_DATE'].strftime('%Y-%m-%d') if order['PAYMENT_DUE_DATE'] else None,
                'total_amount_due': float(order['TOTAL_AMOUNT_DUE'] or 0),
                'paid_amount': float(order['PAID_AMOUNT'] or 0),
                'outstanding_amount': float(order['OUTSTANDING_AMOUNT'] or 0),
                'currency': order['CURRENCY'],
                'payment_status': order['PAYMENT_STATUS'],
                'delivery_status': order['DELIVERY_STATUS'],
                'supplier_name': order['SUPPLIER_NAME'],
                'due_status': order['DUE_STATUS'],
                'payment_priority': order['PAYMENT_PRIORITY']
            })

        return jsonify({
            'success': True,
            'outstanding_orders': formatted_orders
        })

    except Exception as e:
        logger.error(f"خطأ في جلب أوامر الشراء المستحقة للمورد {supplier_id}: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

# APIs نظام إدارة أرصدة الموردين المتقدم
@bp.route('/api/balances/dashboard')
@login_required
def api_balances_dashboard():
    """API للحصول على بيانات لوحة معلومات الأرصدة مع تطبيق الفلاتر"""
    try:
        # الحصول على معايير الفلترة
        period = request.args.get('period', 'current_month')
        currency = request.args.get('currency', 'all')
        balance_status = request.args.get('balance_status', 'all')
        supplier_type = request.args.get('supplier_type', 'all')
        risk_rating = request.args.get('risk_rating', 'all')
        search = request.args.get('search', '').strip()

        logger.info(f"تطبيق الفلاتر: currency={currency}, balance_status={balance_status}, supplier_type={supplier_type}, search={search}")

        # استعلام البيانات الحقيقية من قاعدة البيانات مع تطبيق الفلاتر
        oracle_mgr = get_oracle_manager()
        oracle_mgr.connect()

        try:
            # جلب البيانات المفلترة
            filtered_data = get_filtered_balances_data_real(oracle_mgr, currency, balance_status, supplier_type, search)

            # حساب الإحصائيات من البيانات المفلترة
            stats = filtered_data['stats']
            suppliers_data = filtered_data['suppliers']
            currency_distribution = filtered_data['currency_distribution']

            dashboard_data = {
                'statistics': {
                    'total_balance': float(stats[1]) if stats[1] else 0,
                    'total_outstanding': float(stats[3]) if stats[3] else 0,
                    'total_suppliers': int(stats[0]) if stats[0] else 0,
                    'overdue_amount': 0,  # سيتم حسابه من المعاملات
                    'aging_analysis': {
                        'current_due': 0,
                        'overdue_1_30': 0,
                        'overdue_31_60': 0,
                        'overdue_61_90': 0,
                        'overdue_over_90': 0
                    }
                },
            }

            # إضافة بيانات الرسوم البيانية من البيانات الحقيقية
            dashboard_data['charts'] = {
                'by_currency': [
                    {
                        'currency': get_currency_name(row[0]) if row[0] else 'غير محدد',
                        'amount': float(row[1]) if row[1] else 0,
                        'code': row[0] if row[0] else 'N/A'
                    } for row in currency_distribution
                ],
                'trends': []  # سيتم ملؤها لاحقاً من البيانات التاريخية
            }

            # تحديد العملة الرئيسية
            if len(currency_distribution) == 1:
                dashboard_data['main_currency'] = currency_distribution[0][0]
            elif len(currency_distribution) > 1:
                dashboard_data['main_currency'] = 'عملات متعددة'
            else:
                dashboard_data['main_currency'] = 'غير محدد'

            # إضافة معلومات الفلاتر المطبقة
            dashboard_data['filters_applied'] = {
                'currency': currency,
                'balance_status': balance_status,
                'supplier_type': supplier_type,
                'search': search,
                'total_filtered_suppliers': len(suppliers_data)
            }

            # إضافة قائمة الموردين المفلترة
            dashboard_data['suppliers'] = [
                {
                    'id': row[0],
                    'supplier_code': row[1],
                    'name': row[2],
                    'supplier_type': row[3] or 'غير محدد',
                    'balance_amount': float(row[4]),
                    'currency': row[5],
                    'risk_rating': row[6],
                    'last_transaction_date': str(row[7]) if row[7] else None
                } for row in suppliers_data
            ]
        except Exception as e:
            logger.error(f"خطأ في استعلام البيانات: {e}")
            # بيانات احتياطية بسيطة
            dashboard_data = {
                'statistics': {
                    'total_balance': 0,
                    'total_outstanding': 0,
                    'total_suppliers': 0,
                    'overdue_amount': 0,
                    'aging_analysis': {
                        'current_due': 0,
                        'overdue_1_30': 0,
                        'overdue_31_60': 0,
                        'overdue_61_90': 0,
                        'overdue_over_90': 0
                    }
                },
                'charts': {
                    'by_currency': [],
                    'trends': []
                }
            }
        finally:
            oracle_mgr.disconnect()

        return jsonify({
            'success': True,
            'data': dashboard_data
        })

    except Exception as e:
        logger.error(f"خطأ في API لوحة المعلومات: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

@bp.route('/api/supplier-types')
@login_required
def api_supplier_types():
    """API للحصول على أنواع الموردين"""
    try:
        oracle_mgr = get_oracle_manager()
        oracle_mgr.connect()

        # جلب أنواع الموردين الفعلية من قاعدة البيانات
        types_query = """
        SELECT DISTINCT SUPPLIER_TYPE
        FROM SUPPLIERS
        WHERE IS_ACTIVE = 1
        AND SUPPLIER_TYPE IS NOT NULL
        ORDER BY SUPPLIER_TYPE
        """

        types_result = oracle_mgr.execute_query(types_query)

        supplier_types = []
        if types_result:
            for row in types_result:
                if row[0]:
                    supplier_types.append(row[0])

        # إضافة أنواع محاسبية صحيحة إذا لم توجد بيانات
        if not supplier_types:
            supplier_types = [
                'مورد تجاري',
                'مورد خدمات',
                'مقاول',
                'مورد مواد خام',
                'مورد معدات',
                'مورد استشاري',
                'مورد نقل وشحن',
                'مورد صيانة'
            ]

        oracle_mgr.disconnect()

        return jsonify({
            'success': True,
            'types': supplier_types
        })

    except Exception as e:
        logger.error(f"خطأ في API أنواع الموردين: {e}")
        return jsonify({
            'success': False,
            'message': str(e),
            'types': ['مورد تجاري', 'مورد خدمات', 'مقاول']  # أنواع احتياطية
        })

@bp.route('/balances-simple')
@login_required
def balances_simple():
    """نافذة أرصدة الموردين المحسنة"""
    return render_template('suppliers/balances_simple.html')

@bp.route('/api/balances/simple')
@login_required
def api_balances_simple():
    """API بسيط وفعال لأرصدة الموردين مع فلاتر تعمل فعلياً"""
    try:
        # الحصول على معاملات الفلاتر
        currency = request.args.get('currency', '').strip()
        balance_status = request.args.get('balance_status', '').strip()
        supplier_type = request.args.get('supplier_type', '').strip()
        search = request.args.get('search', '').strip()
        period = request.args.get('period', '').strip()
        from_date = request.args.get('from_date', '').strip()
        to_date = request.args.get('to_date', '').strip()

        logger.info(f"API بسيط - الفلاتر: currency={currency}, balance_status={balance_status}, supplier_type={supplier_type}, search={search}, period={period}")

        oracle_mgr = get_oracle_manager()
        oracle_mgr.connect()

        # استعلام محسن - عرض الأرصدة الموجودة فقط من جدول CURRENT_BALANCES
        base_query = """
        SELECT
            s.SUPPLIER_CODE,
            s.NAME_AR,
            s.SUPPLIER_TYPE,
            cb.current_balance as balance_amount,
            cb.currency_code as currency,
            cb.last_transaction_date,
            CASE
                WHEN cb.current_balance > 0 THEN 'مدين'
                WHEN cb.current_balance < 0 THEN 'دائن'
                ELSE 'متوازن'
            END as balance_type
        FROM CURRENT_BALANCES cb
        JOIN SUPPLIERS s ON cb.entity_id = s.SUPPLIER_CODE
        WHERE cb.entity_type_code = 'SUPPLIER'
        AND s.IS_ACTIVE = 1
        """

        # إضافة شروط الفلترة بطريقة مبسطة
        if currency:
            base_query += f" AND cb.currency_code = '{currency}'"
            logger.info(f"تطبيق فلتر العملة: {currency}")

        if balance_status:
            if balance_status == 'creditor':
                base_query += " AND cb.current_balance > 0"
            elif balance_status == 'debtor':
                base_query += " AND cb.current_balance < 0"
            elif balance_status == 'zero':
                base_query += " AND cb.current_balance = 0"
            logger.info(f"تطبيق فلتر حالة الرصيد: {balance_status}")

        if supplier_type:
            base_query += f" AND s.SUPPLIER_TYPE = '{supplier_type}'"
            logger.info(f"تطبيق فلتر نوع المورد: {supplier_type}")

        if search:
            base_query += f" AND (UPPER(s.NAME_AR) LIKE UPPER('%{search}%') OR s.SUPPLIER_CODE LIKE '%{search}%')"
            logger.info(f"تطبيق فلتر البحث: {search}")

        # فلتر الفترة الزمنية
        if period:
            if period == 'today':
                base_query += " AND DATE(cb.last_transaction_date) = DATE(SYSDATE)"
            elif period == 'this_week':
                base_query += " AND cb.last_transaction_date >= TRUNC(SYSDATE, 'IW')"
            elif period == 'this_month':
                base_query += " AND cb.last_transaction_date >= TRUNC(SYSDATE, 'MM')"
            elif period == 'last_month':
                base_query += " AND cb.last_transaction_date >= ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -1) AND cb.last_transaction_date < TRUNC(SYSDATE, 'MM')"
            elif period == 'this_quarter':
                base_query += " AND cb.last_transaction_date >= TRUNC(SYSDATE, 'Q')"
            elif period == 'this_year':
                base_query += " AND cb.last_transaction_date >= TRUNC(SYSDATE, 'YYYY')"
            elif period == 'custom' and from_date and to_date:
                base_query += f" AND DATE(cb.last_transaction_date) BETWEEN DATE'{from_date}' AND DATE'{to_date}'"
            logger.info(f"تطبيق فلتر الفترة: {period}")

        base_query += " ORDER BY ABS(COALESCE(cb.current_balance, 0)) DESC FETCH FIRST 100 ROWS ONLY"

        # تنفيذ الاستعلام
        logger.info(f"تنفيذ الاستعلام: {base_query}")

        result = oracle_mgr.execute_query(base_query)

        # معالجة النتائج
        suppliers = []
        total_count = 0
        creditor_count = 0
        debtor_count = 0

        if result:
            for row in result:
                supplier_code, name_ar, supplier_type_val, balance_amount, currency_code, last_transaction, balance_type = row

                suppliers.append({
                    'supplier_code': supplier_code,
                    'name': name_ar,
                    'supplier_type': supplier_type_val,
                    'balance_amount': float(balance_amount) if balance_amount else 0,
                    'currency': currency_code,
                    'last_transaction_date': str(last_transaction) if last_transaction else None,
                    'balance_type': balance_type  # نوع الرصيد (مدين/دائن)
                })

                total_count += 1
                # تصحيح المحاسبة: الرصيد الموجب = مدين، الرصيد السالب = دائن
                if balance_amount and balance_amount > 0:
                    debtor_count += 1  # مدين
                elif balance_amount and balance_amount < 0:
                    creditor_count += 1  # دائن

        oracle_mgr.disconnect()

        logger.info(f"نتائج API البسيط: {total_count} مورد، {creditor_count} دائن، {debtor_count} مدين")

        return jsonify({
            'success': True,
            'data': {
                'suppliers': suppliers,
                'total': total_count,
                'creditor_count': creditor_count,
                'debtor_count': debtor_count,
                'filters_applied': {
                    'currency': currency,
                    'balance_status': balance_status,
                    'supplier_type': supplier_type,
                    'search': search,
                    'period': period,
                    'from_date': from_date,
                    'to_date': to_date
                }
            }
        })

    except Exception as e:
        logger.error(f"خطأ في API البسيط: {e}")
        import traceback
        traceback.print_exc()

        # إرجاع بيانات فارغة بدلاً من خطأ
        try:
            if 'oracle_mgr' in locals():
                oracle_mgr.disconnect()
        except:
            pass

        return jsonify({
            'success': True,
            'data': {
                'suppliers': [],
                'total': 0,
                'creditor_count': 0,
                'debtor_count': 0,
                'filters_applied': {
                    'currency': currency if 'currency' in locals() else '',
                    'balance_status': balance_status if 'balance_status' in locals() else '',
                    'supplier_type': supplier_type if 'supplier_type' in locals() else '',
                    'search': search if 'search' in locals() else ''
                },
                'error_message': f'خطأ في جلب البيانات: {str(e)}'
            }
        })

def get_filtered_balances_data_real(oracle_mgr, currency, balance_status, supplier_type, search):
    """جلب البيانات المفلترة من قاعدة البيانات"""
    try:
        # بناء شروط الفلترة
        where_conditions = ["s.IS_ACTIVE = 1"]
        params = []
        param_counter = 1

        # فلتر العملة
        if currency and currency != 'all':
            where_conditions.append(f"cb.currency_code = :{param_counter}")
            params.append(currency)
            param_counter += 1
            logger.info(f"تطبيق فلتر العملة: {currency}")

        # فلتر حالة الرصيد
        if balance_status and balance_status != 'all':
            if balance_status == 'creditor':
                where_conditions.append("cb.current_balance > 0")
                logger.info("تطبيق فلتر: دائن (رصيد موجب)")
            elif balance_status == 'debtor':
                where_conditions.append("cb.current_balance < 0")
                logger.info("تطبيق فلتر: مدين (رصيد سالب)")
            elif balance_status == 'zero':
                where_conditions.append("cb.current_balance = 0")
                logger.info("تطبيق فلتر: رصيد صفر")

        # فلتر نوع المورد
        if supplier_type and supplier_type != 'all':
            where_conditions.append(f"s.SUPPLIER_TYPE = :{param_counter}")
            params.append(supplier_type)
            param_counter += 1
            logger.info(f"تطبيق فلتر نوع المورد: {supplier_type}")

        # فلتر البحث
        if search:
            search_condition = f"(UPPER(s.NAME_AR) LIKE UPPER(:{param_counter}) OR s.SUPPLIER_CODE LIKE :{param_counter + 1})"
            where_conditions.append(search_condition)
            search_pattern = f"%{search}%"
            params.extend([search_pattern, search_pattern])
            param_counter += 2
            logger.info(f"تطبيق فلتر البحث: {search}")

        where_clause = " AND ".join(where_conditions)

        # استعلام الإحصائيات المفلترة
        stats_query = f"""
        SELECT
            COUNT(DISTINCT s.id) as total_suppliers,
            COALESCE(SUM(cb.current_balance), 0) as total_balance,
            COALESCE(SUM(CASE WHEN cb.current_balance > 0 THEN cb.current_balance ELSE 0 END), 0) as total_creditor,
            COALESCE(SUM(CASE WHEN cb.current_balance < 0 THEN ABS(cb.current_balance) ELSE 0 END), 0) as total_debtor
        FROM SUPPLIERS s
        LEFT JOIN CURRENT_BALANCES cb ON s.SUPPLIER_CODE = cb.entity_id AND cb.entity_type_code = 'SUPPLIER'
        WHERE {where_clause}
        """

        stats_result = oracle_mgr.execute_query(stats_query, params)
        stats = stats_result[0] if stats_result else [0, 0, 0, 0]

        # استعلام بيانات الموردين المفلترة
        suppliers_query = f"""
        SELECT
            s.id,
            s.supplier_code,
            s.name_ar,
            s.supplier_type,
            COALESCE(cb.current_balance, 0) as balance_amount,
            COALESCE(cb.currency_code, 'USD') as currency_code,
            COALESCE(s.rating, 'MEDIUM') as risk_rating,
            COALESCE(cb.last_transaction_date, s.created_at) as last_transaction_date
        FROM SUPPLIERS s
        LEFT JOIN CURRENT_BALANCES cb ON s.SUPPLIER_CODE = cb.entity_id AND cb.entity_type_code = 'SUPPLIER'
        WHERE {where_clause}
        ORDER BY ABS(COALESCE(cb.current_balance, 0)) DESC
        FETCH FIRST 50 ROWS ONLY
        """

        suppliers_result = oracle_mgr.execute_query(suppliers_query, params)

        # استعلام توزيع العملات المفلترة
        currency_query = f"""
        SELECT
            cb.currency_code,
            COALESCE(SUM(ABS(cb.current_balance)), 0) as total_amount
        FROM SUPPLIERS s
        JOIN CURRENT_BALANCES cb ON s.SUPPLIER_CODE = cb.entity_id AND cb.entity_type_code = 'SUPPLIER'
        WHERE {where_clause}
        AND cb.currency_code IS NOT NULL
        GROUP BY cb.currency_code
        ORDER BY total_amount DESC
        """

        currency_result = oracle_mgr.execute_query(currency_query, params)

        logger.info(f"نتائج الفلترة: {len(suppliers_result) if suppliers_result else 0} مورد، {len(currency_result) if currency_result else 0} عملة")

        return {
            'stats': stats,
            'suppliers': suppliers_result or [],
            'currency_distribution': currency_result or []
        }

    except Exception as e:
        logger.error(f"خطأ في جلب البيانات المفلترة: {e}")
        # إرجاع بيانات فارغة في حالة الخطأ
        return {
            'stats': [0, 0, 0, 0],
            'suppliers': [],
            'currency_distribution': []
        }

@bp.route('/api/accounts')
@login_required
def api_accounts():
    """API للحصول على قائمة حسابات الموردين"""
    try:
        # بيانات تجريبية للاختبار
        accounts_data = {
            'accounts': [
                    {
                        'account_id': 1,
                        'supplier_code': 'SUP001',
                        'supplier_name': 'شركة الخليج للتجارة والمقاولات',
                        'account_type': 'TRADE',
                        'current_balance': 185000.00,
                        'currency_code': 'SAR',
                        'risk_rating': 'LOW',
                        'last_transaction_date': '2024-09-15',
                        'overdue_amount': 0.00,
                        'payment_score': 4.8
                    },
                    {
                        'account_id': 2,
                        'supplier_code': 'SUP002',
                        'supplier_name': 'مؤسسة النور للخدمات اللوجستية',
                        'account_type': 'SERVICE',
                        'current_balance': 95000.00,
                        'currency_code': 'SAR',
                        'risk_rating': 'LOW',
                        'last_transaction_date': '2024-09-12',
                        'overdue_amount': 0.00,
                        'payment_score': 4.5
                    },
                    {
                        'account_id': 3,
                        'supplier_code': 'SUP003',
                        'supplier_name': 'شركة البناء المتطور المحدودة',
                        'account_type': 'CONTRACTOR',
                        'current_balance': 320000.00,
                        'currency_code': 'SAR',
                        'risk_rating': 'MEDIUM',
                        'last_transaction_date': '2024-09-10',
                        'overdue_amount': 45000.00,
                        'payment_score': 3.8
                    },
                    {
                        'account_id': 4,
                        'supplier_code': 'SUP004',
                        'supplier_name': 'مجموعة الرياض للتكنولوجيا',
                        'account_type': 'SERVICE',
                        'current_balance': 125000.00,
                        'currency_code': 'SAR',
                        'risk_rating': 'LOW',
                        'last_transaction_date': '2024-09-14',
                        'overdue_amount': 0.00,
                        'payment_score': 4.9
                    },
                    {
                        'account_id': 5,
                        'supplier_code': 'SUP005',
                        'supplier_name': 'شركة المواد الصناعية المتقدمة',
                        'account_type': 'TRADE',
                        'current_balance': 275000.00,
                        'currency_code': 'SAR',
                        'risk_rating': 'MEDIUM',
                        'last_transaction_date': '2024-09-08',
                        'overdue_amount': 85000.00,
                        'payment_score': 3.2
                    },
                    {
                        'account_id': 6,
                        'supplier_code': 'SUP006',
                        'supplier_name': 'مؤسسة الشرق الأوسط للاستشارات',
                        'account_type': 'SERVICE',
                        'current_balance': 65000.00,
                        'currency_code': 'SAR',
                        'risk_rating': 'LOW',
                        'last_transaction_date': '2024-09-13',
                        'overdue_amount': 0.00,
                        'payment_score': 4.6
                    }
                ]
        }

        return jsonify({
            'success': True,
            'accounts': accounts_data['accounts']
        })

    except Exception as e:
        logger.error(f"خطأ في جلب بيانات لوحة معلومات الأرصدة: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

@bp.route('/api/reconciliation/create-cycle', methods=['POST'])
@login_required
def api_create_reconciliation_cycle():
    """API لإنشاء دورة مطابقة جديدة"""
    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        required_fields = ['cycle_name', 'period_from', 'period_to', 'cycle_type', 'scope']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'الحقل {field} مطلوب'
                })

        # محاكاة إنشاء دورة المطابقة
        cycle_id = 12345  # سيتم استبداله بالمعرف الفعلي من قاعدة البيانات

        return jsonify({
            'success': True,
            'message': 'تم إنشاء دورة المطابقة بنجاح',
            'cycle_id': cycle_id,
            'cycle_data': {
                'cycle_name': data['cycle_name'],
                'period_from': data['period_from'],
                'period_to': data['period_to'],
                'cycle_type': data['cycle_type'],
                'scope': data['scope'],
                'status': 'OPEN',
                'created_date': datetime.now().isoformat()
            }
        })

    except Exception as e:
        logger.error(f"خطأ في إنشاء دورة المطابقة: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

@bp.route('/api/accounts_detailed')
@login_required
def api_accounts_detailed():
    """API للحصول على قائمة حسابات الموردين"""
    try:
        # بيانات تجريبية للاختبار
        accounts_data = [
            {
                'account_id': 1,
                'supplier_code': 'SUP001',
                'supplier_name': 'شركة الخليج للتجارة',
                'account_number': 'SUP20240001',
                'account_type': 'TRADE',
                'account_status': 'ACTIVE',
                'risk_rating': 'LOW',
                'currency_code': 'SAR',
                'credit_limit': 500000.00,
                'current_balance': 150000.00,
                'payment_terms_days': 30,
                'contact_person': 'أحمد محمد',
                'phone': '+************',
                'email': '<EMAIL>',
                'last_transaction_date': '2024-09-01',
                'total_transactions': 45,
                'avg_payment_days': 28.5
            },
            {
                'account_id': 2,
                'supplier_code': 'SUP002',
                'supplier_name': 'مؤسسة النور للخدمات',
                'account_number': 'SUP20240002',
                'account_type': 'SERVICE',
                'account_status': 'ACTIVE',
                'risk_rating': 'MEDIUM',
                'currency_code': 'SAR',
                'credit_limit': 200000.00,
                'current_balance': -25000.00,
                'payment_terms_days': 15,
                'contact_person': 'فاطمة علي',
                'phone': '+************',
                'email': '<EMAIL>',
                'last_transaction_date': '2024-08-28',
                'total_transactions': 32,
                'avg_payment_days': 18.2
            },
            {
                'account_id': 3,
                'supplier_code': 'SUP003',
                'supplier_name': 'شركة البناء المتطور',
                'account_number': 'SUP20240003',
                'account_type': 'CONTRACTOR',
                'account_status': 'SUSPENDED',
                'risk_rating': 'HIGH',
                'currency_code': 'SAR',
                'credit_limit': 1000000.00,
                'current_balance': 75000.00,
                'payment_terms_days': 45,
                'contact_person': 'محمد سالم',
                'phone': '+************',
                'email': '<EMAIL>',
                'last_transaction_date': '2024-09-03',
                'total_transactions': 18,
                'avg_payment_days': 52.1
            }
        ]

        statistics = {
            'total_accounts': len(accounts_data),
            'active_accounts': len([a for a in accounts_data if a['account_status'] == 'ACTIVE']),
            'high_risk_accounts': len([a for a in accounts_data if a['risk_rating'] in ['HIGH', 'CRITICAL']]),
            'suspended_accounts': len([a for a in accounts_data if a['account_status'] == 'SUSPENDED'])
        }

        return jsonify({
            'success': True,
            'accounts': accounts_data,
            'statistics': statistics
        })

    except Exception as e:
        logger.error(f"خطأ في جلب بيانات الحسابات: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })
