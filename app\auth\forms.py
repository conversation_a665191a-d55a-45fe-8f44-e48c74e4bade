# -*- coding: utf-8 -*-
"""
نماذج المصادقة والتفويض
Authentication and Authorization Forms
"""

from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, BooleanField, SubmitField, SelectField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError
from app.models import User

class LoginForm(FlaskForm):
    """نموذج تسجيل الدخول"""
    username = StringField('اسم المستخدم', validators=[DataRequired()], 
                          render_kw={'placeholder': 'أدخل اسم المستخدم', 'dir': 'ltr'})
    password = PasswordField('كلمة المرور', validators=[DataRequired()],
                           render_kw={'placeholder': 'أدخل كلمة المرور', 'dir': 'ltr'})
    remember_me = BooleanField('تذكرني')
    submit = SubmitField('تسجيل الدخول')

class RegistrationForm(FlaskForm):
    """نموذج إنشاء حساب جديد"""
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=4, max=20)],
                          render_kw={'placeholder': 'أدخل اسم المستخدم', 'dir': 'ltr'})
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()],
                       render_kw={'placeholder': 'أدخل البريد الإلكتروني', 'dir': 'ltr'})
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(min=2, max=100)],
                           render_kw={'placeholder': 'أدخل الاسم الكامل'})
    department = StringField('القسم', validators=[Length(max=100)],
                            render_kw={'placeholder': 'أدخل اسم القسم'})
    position = StringField('المنصب', validators=[Length(max=100)],
                          render_kw={'placeholder': 'أدخل المنصب'})
    phone = StringField('رقم الهاتف', validators=[Length(max=20)],
                       render_kw={'placeholder': 'أدخل رقم الهاتف', 'dir': 'ltr'})
    password = PasswordField('كلمة المرور', validators=[DataRequired(), Length(min=6)],
                           render_kw={'placeholder': 'أدخل كلمة المرور', 'dir': 'ltr'})
    password2 = PasswordField('تأكيد كلمة المرور', 
                             validators=[DataRequired(), EqualTo('password', message='كلمات المرور غير متطابقة')],
                             render_kw={'placeholder': 'أعد إدخال كلمة المرور', 'dir': 'ltr'})
    submit = SubmitField('إنشاء الحساب')
    
    def validate_username(self, username):
        """التحقق من عدم وجود اسم المستخدم"""
        user = User.query.filter_by(username=username.data).first()
        if user is not None:
            raise ValidationError('اسم المستخدم موجود بالفعل. يرجى اختيار اسم آخر.')
    
    def validate_email(self, email):
        """التحقق من عدم وجود البريد الإلكتروني"""
        user = User.query.filter_by(email=email.data).first()
        if user is not None:
            raise ValidationError('البريد الإلكتروني موجود بالفعل. يرجى استخدام بريد آخر.')

class ChangePasswordForm(FlaskForm):
    """نموذج تغيير كلمة المرور"""
    old_password = PasswordField('كلمة المرور الحالية', validators=[DataRequired()],
                                render_kw={'placeholder': 'أدخل كلمة المرور الحالية', 'dir': 'ltr'})
    new_password = PasswordField('كلمة المرور الجديدة', validators=[DataRequired(), Length(min=6)],
                                render_kw={'placeholder': 'أدخل كلمة المرور الجديدة', 'dir': 'ltr'})
    new_password2 = PasswordField('تأكيد كلمة المرور الجديدة',
                                 validators=[DataRequired(), EqualTo('new_password', message='كلمات المرور غير متطابقة')],
                                 render_kw={'placeholder': 'أعد إدخال كلمة المرور الجديدة', 'dir': 'ltr'})
    submit = SubmitField('تغيير كلمة المرور')

class EditProfileForm(FlaskForm):
    """نموذج تعديل الملف الشخصي"""
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(min=2, max=100)],
                           render_kw={'placeholder': 'أدخل الاسم الكامل'})
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()],
                       render_kw={'placeholder': 'أدخل البريد الإلكتروني', 'dir': 'ltr'})
    department = StringField('القسم', validators=[Length(max=100)],
                            render_kw={'placeholder': 'أدخل اسم القسم'})
    position = StringField('المنصب', validators=[Length(max=100)],
                          render_kw={'placeholder': 'أدخل المنصب'})
    phone = StringField('رقم الهاتف', validators=[Length(max=20)],
                       render_kw={'placeholder': 'أدخل رقم الهاتف', 'dir': 'ltr'})
    submit = SubmitField('حفظ التغييرات')
    
    def __init__(self, original_email, *args, **kwargs):
        super(EditProfileForm, self).__init__(*args, **kwargs)
        self.original_email = original_email
    
    def validate_email(self, email):
        """التحقق من عدم وجود البريد الإلكتروني"""
        if email.data != self.original_email:
            user = User.query.filter_by(email=email.data).first()
            if user is not None:
                raise ValidationError('البريد الإلكتروني موجود بالفعل. يرجى استخدام بريد آخر.')

class UserForm(FlaskForm):
    """نموذج إدارة المستخدمين"""
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=4, max=20)],
                          render_kw={'placeholder': 'أدخل اسم المستخدم', 'dir': 'ltr'})
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()],
                       render_kw={'placeholder': 'أدخل البريد الإلكتروني', 'dir': 'ltr'})
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(min=2, max=100)],
                           render_kw={'placeholder': 'أدخل الاسم الكامل'})
    department = StringField('القسم', validators=[Length(max=100)],
                            render_kw={'placeholder': 'أدخل اسم القسم'})
    position = StringField('المنصب', validators=[Length(max=100)],
                          render_kw={'placeholder': 'أدخل المنصب'})
    phone = StringField('رقم الهاتف', validators=[Length(max=20)],
                       render_kw={'placeholder': 'أدخل رقم الهاتف', 'dir': 'ltr'})
    is_active = BooleanField('نشط')
    is_admin = BooleanField('مدير')
    password = PasswordField('كلمة المرور', validators=[Length(min=6)],
                           render_kw={'placeholder': 'اتركها فارغة للاحتفاظ بكلمة المرور الحالية', 'dir': 'ltr'})
    submit = SubmitField('حفظ')
    
    def __init__(self, original_username=None, original_email=None, *args, **kwargs):
        super(UserForm, self).__init__(*args, **kwargs)
        self.original_username = original_username
        self.original_email = original_email
    
    def validate_username(self, username):
        """التحقق من عدم وجود اسم المستخدم"""
        if username.data != self.original_username:
            user = User.query.filter_by(username=username.data).first()
            if user is not None:
                raise ValidationError('اسم المستخدم موجود بالفعل. يرجى اختيار اسم آخر.')
    
    def validate_email(self, email):
        """التحقق من عدم وجود البريد الإلكتروني"""
        if email.data != self.original_email:
            user = User.query.filter_by(email=email.data).first()
            if user is not None:
                raise ValidationError('البريد الإلكتروني موجود بالفعل. يرجى استخدام بريد آخر.')
