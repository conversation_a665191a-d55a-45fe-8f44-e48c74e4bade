<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة أوامر الشراء - SASERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 🎨 التصميم الحديث المضمون */
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .container-fluid {
            max-width: none;
            margin: 0;
        }
        
        .header-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        
        .header-card h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header-card p {
            color: #6c757d;
            font-size: 1.1rem;
            margin-bottom: 0;
        }
        
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            border-left: 5px solid #667eea;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .stat-card:nth-child(1) { border-left-color: #28a745; }
        .stat-card:nth-child(2) { border-left-color: #ffc107; }
        .stat-card:nth-child(3) { border-left-color: #dc3545; }
        .stat-card:nth-child(4) { border-left-color: #17a2b8; }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .control-panel {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .table {
            margin-bottom: 0;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #343a40 0%, #495057 100%);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.85rem;
            padding: 15px;
            border: none;
        }
        
        .table tbody tr {
            transition: background-color 0.3s ease;
        }
        
        .table tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.1);
        }
        
        .table tbody td {
            padding: 15px;
            vertical-align: middle;
            border-bottom: 1px solid #e9ecef;
        }
        
        .btn {
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 10px 15px;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .badge {
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .breadcrumb {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 15px 20px;
            margin-bottom: 20px;
        }
        
        .success-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            font-weight: 600;
            z-index: 1000;
            animation: slideIn 0.5s ease;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @media (max-width: 768px) {
            body { padding: 10px; }
            .header-card h1 { font-size: 2rem; }
            .stat-number { font-size: 2rem; }
            .control-panel { padding: 15px; }
        }
    </style>
</head>
<body>
    <!-- مؤشر نجاح التحميل -->
    <div class="success-indicator">
        <i class="fas fa-check-circle me-2"></i>
        تم تحميل التصميم الحديث بنجاح!
    </div>

    <div class="container-fluid px-3">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="/dashboard" class="text-decoration-none text-primary">
                        <i class="fas fa-home me-1"></i>الرئيسية
                    </a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">
                    <i class="fas fa-shopping-cart me-1"></i>إدارة أوامر الشراء
                </li>
            </ol>
        </nav>

        <!-- Header -->
        <div class="header-card">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-shopping-cart me-3"></i>إدارة أوامر الشراء</h1>
                    <p>إدارة شاملة لجميع أوامر الشراء والمشتريات من الموردين</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="/purchase-orders/new" class="btn btn-primary btn-lg">
                        <i class="fas fa-plus me-2"></i>إضافة أمر شراء جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_count if stats else 0 }}</div>
                <div class="stat-label">إجمالي الأوامر</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.draft_count if stats else 0 }}</div>
                <div class="stat-label">المسودات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.confirmed_count if stats else 0 }}</div>
                <div class="stat-label">المؤكدة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.used_count if stats else 0 }}</div>
                <div class="stat-label">المُستخدمة</div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="control-panel">
            <div class="row align-items-end">
                <div class="col-md-4">
                    <label class="form-label fw-bold">البحث السريع</label>
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" id="searchInput" class="form-control"
                               placeholder="البحث برقم الأمر، اسم المورد، أو المبلغ..."
                               onkeyup="performQuickSearch()">
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">الحالة</label>
                    <select class="form-select" id="statusFilter" onchange="applyFilters()">
                        <option value="">جميع الحالات</option>
                        <option value="مسودة">مسودة</option>
                        <option value="مرسل">مرسل</option>
                        <option value="مؤكد">مؤكد</option>
                        <option value="ملغي">ملغي</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">من تاريخ</label>
                    <input type="date" class="form-control" id="dateFrom" onchange="applyFilters()">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">إلى تاريخ</label>
                    <input type="date" class="form-control" id="dateTo" onchange="applyFilters()">
                </div>
                <div class="col-md-2">
                    <div class="d-flex gap-2">
                        <button class="btn btn-secondary" onclick="resetFilters()" title="إعادة تعيين">
                            <i class="fas fa-undo"></i>
                        </button>
                        <button class="btn btn-primary" onclick="location.reload()" title="تحديث">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button type="button" class="btn btn-success" onclick="exportData()" title="تصدير">
                            <i class="fas fa-file-excel"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Table -->
        <div class="table-container">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>رقم الأمر</th>
                            <th>رقم العقد</th>
                            <th>المورد</th>
                            <th>تاريخ الأمر</th>
                            <th>تاريخ التسليم</th>
                            <th>القيمة الإجمالية</th>
                            <th>الحالة</th>
                            <th>مُستخدم</th>
                            <th>العمليات</th>
                        </tr>
                    </thead>
                    <tbody id="purchaseOrdersTableBody">
                        {% for po in purchase_orders %}
                        <tr>
                            <td><strong class="text-primary">{{ po[1] }}</strong></td>
                            <td>
                                {% if po[11] %}
                                    <span class="badge bg-info">{{ po[11] }}</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>{{ po[2] or '-' }}</td>
                            <td>{{ po[3].strftime('%Y-%m-%d') if po[3] else '-' }}</td>
                            <td>{{ po[4].strftime('%Y-%m-%d') if po[4] else '-' }}</td>
                            <td>
                                <strong class="text-success">
                                    {{ "{:,.2f}".format(po[6]) if po[6] else '0.00' }}
                                    {{ po[13] if po[13] else po[7] or '' }}
                                </strong>
                            </td>
                            <td>
                                {% set status_class = 'secondary' %}
                                {% if po[5] == 'مؤكد' %}
                                    {% set status_class = 'success' %}
                                {% elif po[5] == 'مرسل' %}
                                    {% set status_class = 'info' %}
                                {% elif po[5] == 'ملغي' %}
                                    {% set status_class = 'danger' %}
                                {% endif %}
                                <span class="badge bg-{{ status_class }}">{{ po[5] or 'مسودة' }}</span>
                            </td>
                            <td>
                                {% if po[8] %}
                                    <span class="badge bg-success">مُستخدم</span>
                                {% else %}
                                    <span class="badge bg-secondary">غير مُستخدم</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="/purchase-orders/view/{{ po[0] }}" class="btn btn-outline-info btn-sm" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="/purchase-orders/edit/{{ po[0] }}" class="btn btn-outline-warning btn-sm" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="/purchase-orders/{{ po[0] }}/documents" class="btn btn-outline-primary btn-sm" title="إدارة الوثائق">
                                        <i class="fas fa-file-alt"></i>
                                    </a>
                                    <button onclick="deletePO({{ po[0] }})" class="btn btn-outline-danger btn-sm" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // إخفاء مؤشر النجاح بعد 3 ثوان
        setTimeout(() => {
            const indicator = document.querySelector('.success-indicator');
            if (indicator) {
                indicator.style.animation = 'slideIn 0.5s ease reverse';
                setTimeout(() => indicator.remove(), 500);
            }
        }, 3000);

        // دوال البحث والفلترة
        function performQuickSearch() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const tableBody = document.getElementById('purchaseOrdersTableBody');
            const rows = tableBody.getElementsByTagName('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const text = row.textContent.toLowerCase();
                
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        function applyFilters() {
            const statusFilter = document.getElementById('statusFilter').value;
            const dateFrom = document.getElementById('dateFrom').value;
            const dateTo = document.getElementById('dateTo').value;
            const tableBody = document.getElementById('purchaseOrdersTableBody');
            const rows = tableBody.getElementsByTagName('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                let showRow = true;

                if (statusFilter) {
                    const statusCell = row.cells[6];
                    if (statusCell && !statusCell.textContent.includes(statusFilter)) {
                        showRow = false;
                    }
                }

                if (dateFrom || dateTo) {
                    const dateCell = row.cells[3];
                    if (dateCell) {
                        const rowDate = dateCell.textContent.trim();
                        if (dateFrom && rowDate < dateFrom) showRow = false;
                        if (dateTo && rowDate > dateTo) showRow = false;
                    }
                }

                row.style.display = showRow ? '' : 'none';
            }
        }

        function resetFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('dateFrom').value = '';
            document.getElementById('dateTo').value = '';
            
            const tableBody = document.getElementById('purchaseOrdersTableBody');
            const rows = tableBody.getElementsByTagName('tr');
            
            for (let i = 0; i < rows.length; i++) {
                rows[i].style.display = '';
            }
        }

        function exportData() {
            alert('ميزة التصدير ستكون متاحة قريباً');
        }

        function deletePO(id) {
            if (confirm('هل أنت متأكد من حذف هذا الأمر؟')) {
                alert('ميزة الحذف ستكون متاحة قريباً');
            }
        }

        console.log('🎨 تم تحميل التصميم الحديث البسيط بنجاح!');
    </script>
</body>
</html>
