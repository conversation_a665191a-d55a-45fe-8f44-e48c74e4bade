
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الفلاتر المصححة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h1>اختبار الفلاتر المصححة</h1>
        
        <div class="alert alert-success">
            <h5>✅ الإصلاحات المطبقة:</h5>
            <ul>
                <li>إصلاح فلترة المورد باستخدام :supplier_id</li>
                <li>إنشاء API للحالات</li>
                <li>تحديث JavaScript لتحميل الحالات</li>
                <li>إصلاح معاملات Oracle</li>
            </ul>
        </div>
        
        <!-- اختبار API الموردين -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>اختبار API الموردين</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testSuppliersAPI()">اختبار الموردين</button>
                        <div id="suppliersResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>اختبار API الحالات</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-success" onclick="testStatusesAPI()">اختبار الحالات</button>
                        <div id="statusesResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- اختبار الفلترة -->
        <div class="mt-4">
            <div class="card">
                <div class="card-header">
                    <h5>اختبار الفلترة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label>المورد:</label>
                            <select id="testSupplier" class="form-select">
                                <option value="">جميع الموردين</option>
        <option value="شركة رايسن">شركة رايسن</option><option value="شركة وايسدوم هاوس">شركة وايسدوم هاوس</option><option value="شركة يابايشينج-الصين">شركة يابايشينج-الصين</option><option value="شركة ياهوا فود كومبنى">شركة ياهوا فود كومبنى</option><option value="شركة يونجي-الصين">شركة يونجي-الصين</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label>الحالة:</label>
                            <select id="testStatus" class="form-select">
                                <option value="">جميع الحالات</option>
        <option value="تم التسليم">تم التسليم (7)</option><option value="مسودة">مسودة (6)</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label>&nbsp;</label><br>
                            <button class="btn btn-info" onclick="testFiltering()">اختبار الفلترة</button>
                        </div>
                    </div>
                    <div id="filteringResult" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
    function testSuppliersAPI() {
        $('#suppliersResult').html('<div class="spinner-border spinner-border-sm"></div> جاري الاختبار...');
        
        fetch('/purchase-orders/api/items/suppliers')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    $('#suppliersResult').html(`
                        <div class="alert alert-success">
                            <strong>✅ نجح!</strong><br>
                            تم جلب ${data.data.length} مورد
                        </div>
                    `);
                } else {
                    $('#suppliersResult').html(`
                        <div class="alert alert-danger">
                            <strong>❌ فشل!</strong><br>
                            ${data.error}
                        </div>
                    `);
                }
            })
            .catch(error => {
                $('#suppliersResult').html(`
                    <div class="alert alert-danger">
                        <strong>❌ خطأ!</strong><br>
                        ${error}
                    </div>
                `);
            });
    }
    
    function testStatusesAPI() {
        $('#statusesResult').html('<div class="spinner-border spinner-border-sm"></div> جاري الاختبار...');
        
        fetch('/purchase-orders/api/items/statuses')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    $('#statusesResult').html(`
                        <div class="alert alert-success">
                            <strong>✅ نجح!</strong><br>
                            تم جلب ${data.data.length} حالة
                        </div>
                    `);
                } else {
                    $('#statusesResult').html(`
                        <div class="alert alert-danger">
                            <strong>❌ فشل!</strong><br>
                            ${data.error}
                        </div>
                    `);
                }
            })
            .catch(error => {
                $('#statusesResult').html(`
                    <div class="alert alert-danger">
                        <strong>❌ خطأ!</strong><br>
                        ${error}
                    </div>
                `);
            });
    }
    
    function testFiltering() {
        $('#filteringResult').html('<div class="spinner-border spinner-border-sm"></div> جاري اختبار الفلترة...');
        
        const supplier = $('#testSupplier').val();
        const status = $('#testStatus').val();
        
        const params = new URLSearchParams();
        if (supplier) params.append('supplier_id', supplier);
        if (status) params.append('status', status);
        
        fetch(`/purchase-orders/api/items/data?${params}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    $('#filteringResult').html(`
                        <div class="alert alert-success">
                            <strong>✅ الفلترة تعمل!</strong><br>
                            تم جلب ${data.data.length} صنف
                            ${supplier ? '<br>المورد: ' + supplier : ''}
                            ${status ? '<br>الحالة: ' + status : ''}
                        </div>
                    `);
                } else {
                    $('#filteringResult').html(`
                        <div class="alert alert-danger">
                            <strong>❌ فشل في الفلترة!</strong><br>
                            ${data.error}
                        </div>
                    `);
                }
            })
            .catch(error => {
                $('#filteringResult').html(`
                    <div class="alert alert-danger">
                        <strong>❌ خطأ في الفلترة!</strong><br>
                        ${error}
                    </div>
                `);
            });
    }
    </script>
</body>
</html>
        