#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة أعمدة التتبع الجديدة لجدول أصناف الشحنات
"""

from oracle_manager import OracleManager

def add_tracking_columns():
    """إضافة أعمدة رقم الحاوية واسم المستلم للتتبع"""
    
    db = OracleManager()
    if not db.connect():
        print("❌ فشل الاتصال بقاعدة البيانات")
        return False

    try:
        print("🔧 بدء إضافة أعمدة التتبع...")
        
        # 1. إضافة عمود رقم الحاوية الفعلي
        try:
            sql1 = "ALTER TABLE cargo_shipment_items ADD container_number VARCHAR2(50)"
            db.execute_update(sql1)
            print("✅ تم إضافة عمود container_number")
        except Exception as e:
            if "ORA-01430" in str(e):
                print("ℹ️ عمود container_number موجود بالفعل")
            else:
                print(f"⚠️ خطأ في إضافة container_number: {e}")
        
        # 2. إضافة عمود اسم المستلم الفعلي
        try:
            sql2 = "ALTER TABLE cargo_shipment_items ADD recipient_name VARCHAR2(200)"
            db.execute_update(sql2)
            print("✅ تم إضافة عمود recipient_name")
        except Exception as e:
            if "ORA-01430" in str(e):
                print("ℹ️ عمود recipient_name موجود بالفعل")
            else:
                print(f"⚠️ خطأ في إضافة recipient_name: {e}")
        
        # 3. إضافة تعليقات للأعمدة
        try:
            comment1 = "COMMENT ON COLUMN cargo_shipment_items.container_number IS 'رقم الحاوية الفعلي للتتبع'"
            db.execute_update(comment1)
            
            comment2 = "COMMENT ON COLUMN cargo_shipment_items.recipient_name IS 'اسم المستلم الفعلي للتتبع'"
            db.execute_update(comment2)
            print("✅ تم إضافة التعليقات")
        except Exception as e:
            print(f"⚠️ خطأ في إضافة التعليقات: {e}")
        
        # 4. إنشاء فهارس للبحث السريع
        try:
            index1 = "CREATE INDEX idx_cargo_items_container_number ON cargo_shipment_items(container_number)"
            db.execute_update(index1)
            print("✅ تم إنشاء فهرس container_number")
        except Exception as e:
            if "ORA-00955" in str(e):
                print("ℹ️ فهرس container_number موجود بالفعل")
            else:
                print(f"⚠️ خطأ في إنشاء فهرس container_number: {e}")
        
        try:
            index2 = "CREATE INDEX idx_cargo_items_recipient_name ON cargo_shipment_items(recipient_name)"
            db.execute_update(index2)
            print("✅ تم إنشاء فهرس recipient_name")
        except Exception as e:
            if "ORA-00955" in str(e):
                print("ℹ️ فهرس recipient_name موجود بالفعل")
            else:
                print(f"⚠️ خطأ في إنشاء فهرس recipient_name: {e}")
        
        # 5. عرض هيكل الجدول المحدث
        print("\n📋 هيكل الجدول المحدث:")
        columns_query = """
            SELECT column_name, data_type, nullable, data_default 
            FROM user_tab_columns 
            WHERE table_name = 'CARGO_SHIPMENT_ITEMS' 
            ORDER BY column_id
        """
        columns = db.execute_query(columns_query)
        
        for col in columns:
            nullable = "NULL" if col[2] == "Y" else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  {col[0]:<25} {col[1]:<15} {nullable}{default}")
        
        print("\n🎉 تم تحديث جدول الأصناف بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False
        
    finally:
        if db.connection:
            db.connection.close()

if __name__ == "__main__":
    print("🚀 بدء تحديث جدول أصناف الشحنات...")
    success = add_tracking_columns()
    
    if success:
        print("✅ تم التحديث بنجاح!")
    else:
        print("❌ فشل التحديث!")
