{% extends "base.html" %}

{% block title %}إضافة جهة اتصال جديدة{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: white;
        border-radius: 0.5rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }
    
    .section-title {
        color: #5a5c69;
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #e3e6f0;
    }
    
    .required-field::after {
        content: " *";
        color: #e74a3b;
    }
    
    .form-check-group {
        background: #f8f9fc;
        padding: 1rem;
        border-radius: 0.35rem;
        border: 1px solid #e3e6f0;
    }
    
    .priority-indicator {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: inline-block;
        margin-left: 0.5rem;
    }
    
    .priority-high { background-color: #e74a3b; }
    .priority-medium { background-color: #f39c12; }
    .priority-low { background-color: #28a745; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-user-plus text-primary me-2"></i>
                        إضافة جهة اتصال جديدة
                    </h1>
                    <p class="text-muted mb-0">إضافة جهة اتصال جديدة لنظام الإشعارات</p>
                </div>
                <div>
                    <a href="/notifications/contacts/" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <form id="addContactForm" onsubmit="saveContact(event)">
        <!-- معلومات أساسية -->
        <div class="form-section">
            <h4 class="section-title">
                <i class="fas fa-user me-2"></i>
                المعلومات الأساسية
            </h4>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label required-field">اسم جهة الاتصال</label>
                        <input type="text" class="form-control" name="contact_name" required 
                               placeholder="مثال: أحمد محمد - مدير العمليات">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">نوع جهة الاتصال</label>
                        <select class="form-select" name="contact_type">
                            <option value="CUSTOMER">عميل</option>
                            <option value="DRIVER">سائق</option>
                            <option value="AGENT">مخلص جمركي</option>
                            <option value="MANAGER">مدير</option>
                            <option value="EXTERNAL">خارجي</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الشركة</label>
                        <input type="text" class="form-control" name="company_name" 
                               placeholder="اسم الشركة أو المؤسسة">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">المنصب</label>
                        <input type="text" class="form-control" name="position" 
                               placeholder="المنصب أو الوظيفة">
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات الاتصال -->
        <div class="form-section">
            <h4 class="section-title">
                <i class="fas fa-phone me-2"></i>
                معلومات الاتصال
            </h4>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" name="phone_number" 
                               placeholder="+966xxxxxxxxx">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" name="email_address" 
                               placeholder="<EMAIL>">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">رقم WhatsApp</label>
                        <input type="tel" class="form-control" name="whatsapp_number" 
                               placeholder="+966xxxxxxxxx">
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات الإشعارات -->
        <div class="form-section">
            <h4 class="section-title">
                <i class="fas fa-bell me-2"></i>
                إعدادات الإشعارات
            </h4>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">القنوات المفضلة</label>
                        <div class="form-check-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="channels" value="SMS" checked>
                                <label class="form-check-label">
                                    <i class="fas fa-sms text-primary me-2"></i>
                                    الرسائل النصية (SMS)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="channels" value="EMAIL" checked>
                                <label class="form-check-label">
                                    <i class="fas fa-envelope text-info me-2"></i>
                                    البريد الإلكتروني
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="channels" value="WHATSAPP">
                                <label class="form-check-label">
                                    <i class="fab fa-whatsapp text-success me-2"></i>
                                    واتساب
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="channels" value="PUSH">
                                <label class="form-check-label">
                                    <i class="fas fa-bell text-warning me-2"></i>
                                    الإشعارات المباشرة
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">مستوى الأولوية</label>
                        <select class="form-select" name="priority_level" onchange="updatePriorityIndicator(this.value)">
                            <option value="10">عالية جداً (10) <span class="priority-indicator priority-high"></span></option>
                            <option value="8">عالية (8)</option>
                            <option value="5" selected>متوسطة (5)</option>
                            <option value="3">منخفضة (3)</option>
                            <option value="1">منخفضة جداً (1)</option>
                        </select>
                        <div class="mt-2">
                            <span class="priority-indicator priority-medium" id="priorityIndicator"></span>
                            <small class="text-muted ms-2">مؤشر الأولوية</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="is_vip" id="isVip">
                            <label class="form-check-label" for="isVip">
                                <i class="fas fa-star text-warning me-2"></i>
                                جهة اتصال مميزة (VIP)
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ملاحظات -->
        <div class="form-section">
            <h4 class="section-title">
                <i class="fas fa-sticky-note me-2"></i>
                ملاحظات إضافية
            </h4>
            
            <div class="mb-3">
                <label class="form-label">ملاحظات</label>
                <textarea class="form-control" name="notes" rows="4" 
                          placeholder="أي ملاحظات أو معلومات إضافية عن جهة الاتصال..."></textarea>
            </div>
        </div>

        <!-- أزرار الحفظ -->
        <div class="form-section">
            <div class="d-flex justify-content-between">
                <a href="/notifications/contacts/" class="btn btn-secondary">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </a>
                <div>
                    <button type="button" class="btn btn-outline-primary me-2" onclick="resetForm()">
                        <i class="fas fa-undo me-2"></i>
                        إعادة تعيين
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>
                        حفظ جهة الاتصال
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
// تحديث مؤشر الأولوية
function updatePriorityIndicator(priority) {
    const indicator = document.getElementById('priorityIndicator');
    if (indicator) {
        indicator.className = 'priority-indicator ';
        if (priority >= 8) {
            indicator.className += 'priority-high';
        } else if (priority >= 5) {
            indicator.className += 'priority-medium';
        } else {
            indicator.className += 'priority-low';
        }
    }
}

// إعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
        document.getElementById('addContactForm').reset();
        updatePriorityIndicator(5);
    }
}

// حفظ جهة الاتصال
function saveContact(event) {
    event.preventDefault();
    
    const form = document.getElementById('addContactForm');
    const formData = new FormData(form);
    
    // جمع القنوات المختارة
    const selectedChannels = [];
    document.querySelectorAll('input[name="channels"]:checked').forEach(checkbox => {
        selectedChannels.push(checkbox.value);
    });
    
    const contactData = {
        contact_name: formData.get('contact_name'),
        contact_type: formData.get('contact_type'),
        phone_number: formData.get('phone_number'),
        email_address: formData.get('email_address'),
        whatsapp_number: formData.get('whatsapp_number'),
        company_name: formData.get('company_name'),
        position: formData.get('position'),
        preferred_channels: selectedChannels.join(','),
        priority_level: parseInt(formData.get('priority_level')),
        is_vip: formData.get('is_vip') ? true : false,
        notes: formData.get('notes')
    };
    
    // التحقق من البيانات المطلوبة
    if (!contactData.contact_name) {
        alert('يرجى إدخال اسم جهة الاتصال');
        return;
    }
    
    // إرسال البيانات
    fetch('/notifications/contacts/api/contacts', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(contactData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ تم حفظ جهة الاتصال بنجاح!');
            window.location.href = '/notifications/contacts/';
        } else {
            alert('❌ خطأ: ' + (data.message || 'فشل في حفظ جهة الاتصال'));
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('❌ حدث خطأ في حفظ جهة الاتصال');
    });
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updatePriorityIndicator(5);
});
</script>
{% endblock %}
