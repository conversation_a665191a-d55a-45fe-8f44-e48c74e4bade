#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحليل JavaScript الخاص بموقع COSCO
"""

import requests
import urllib3
import re
import json

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def analyze_main_js():
    """تحليل الملف الرئيسي للـ JavaScript"""
    print("🔍 تحليل main JavaScript file...")
    
    session = requests.Session()
    session.verify = False
    
    js_url = "https://elines.coscoshipping.com/ebusiness/dist/main.c94d89.js"
    
    try:
        response = session.get(js_url, timeout=20)
        
        if response.status_code == 200:
            js_content = response.text
            print(f"✅ تم تحميل JavaScript ({len(js_content)} حرف)")
            
            # البحث عن API endpoints
            print("\n🔗 البحث عن API endpoints...")
            api_patterns = [
                r'["\']([^"\']*api[^"\']*)["\']',
                r'["\']([^"\']*tracking[^"\']*)["\']',
                r'["\']([^"\']*cargo[^"\']*)["\']',
                r'url\s*:\s*["\']([^"\']+)["\']',
                r'baseURL\s*:\s*["\']([^"\']+)["\']'
            ]
            
            found_apis = set()
            for pattern in api_patterns:
                matches = re.findall(pattern, js_content, re.IGNORECASE)
                for match in matches:
                    if 'api' in match.lower() or 'tracking' in match.lower():
                        found_apis.add(match)
            
            print("📋 API endpoints found:")
            for api in sorted(found_apis):
                print(f"  - {api}")
            
            # البحث عن HTTP methods
            print("\n📤 البحث عن HTTP methods...")
            http_patterns = [
                r'method\s*:\s*["\']([^"\']+)["\']',
                r'\.(get|post|put|delete)\s*\(',
                r'type\s*:\s*["\']([^"\']+)["\']'
            ]
            
            methods = set()
            for pattern in http_patterns:
                matches = re.findall(pattern, js_content, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        methods.add(match[0].upper())
                    else:
                        methods.add(match.upper())
            
            print(f"HTTP methods: {sorted(methods)}")
            
            # البحث عن data structures
            print("\n📊 البحث عن data structures...")
            data_patterns = [
                r'trackingNo\s*:\s*([^,}]+)',
                r'trackingType\s*:\s*([^,}]+)',
                r'containerNo\s*:\s*([^,}]+)',
                r'bookingNo\s*:\s*([^,}]+)'
            ]
            
            for pattern in data_patterns:
                matches = re.findall(pattern, js_content, re.IGNORECASE)
                if matches:
                    print(f"  {pattern}: {matches[:3]}")
            
            # البحث عن response handlers
            print("\n📥 البحث عن response handlers...")
            response_patterns = [
                r'success\s*:\s*function\s*\([^)]*\)\s*{([^}]{0,200})}',
                r'\.then\s*\(\s*function\s*\([^)]*\)\s*{([^}]{0,200})}',
                r'data\s*\.\s*(\w+)',
                r'response\s*\.\s*(\w+)'
            ]
            
            for pattern in response_patterns:
                matches = re.findall(pattern, js_content, re.IGNORECASE)
                if matches:
                    print(f"  Response pattern: {matches[:2]}")
            
            return js_content
            
        else:
            print(f"❌ فشل في تحميل JavaScript: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return None

def find_real_api_endpoint():
    """محاولة العثور على API endpoint الحقيقي"""
    print("\n🎯 البحث عن API endpoint الحقيقي...")
    
    session = requests.Session()
    session.verify = False
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json'
    })
    
    # قائمة endpoints محتملة بناءً على التحليل
    potential_endpoints = [
        "/ebusiness/api/cargoTracking/search",
        "/ebusiness/api/tracking/search",
        "/ebusiness/api/cargo/search",
        "/ebusiness/cargoTracking/api/search",
        "/api/cargoTracking",
        "/api/tracking",
        "/ebusiness/rest/cargoTracking",
        "/ebusiness/service/cargoTracking"
    ]
    
    test_data = {
        "trackingNo": "TEST123456",
        "trackingType": "2"
    }
    
    for endpoint in potential_endpoints:
        try:
            url = f"https://elines.coscoshipping.com{endpoint}"
            print(f"🔍 اختبار: {endpoint}")
            
            # محاولة POST
            response = session.post(url, json=test_data, timeout=10)
            print(f"  POST Status: {response.status_code}")
            
            if response.status_code not in [404, 405]:
                print(f"  ✅ استجابة مثيرة للاهتمام!")
                print(f"  Content: {response.text[:200]}")
            
            # محاولة GET
            response = session.get(url, params=test_data, timeout=10)
            print(f"  GET Status: {response.status_code}")
            
            if response.status_code not in [404, 405]:
                print(f"  ✅ استجابة مثيرة للاهتمام!")
                print(f"  Content: {response.text[:200]}")
                
        except Exception as e:
            print(f"  خطأ: {e}")

def analyze_network_traffic():
    """محاولة تحليل network traffic"""
    print("\n🌐 تحليل network traffic...")
    
    session = requests.Session()
    session.verify = False
    
    # محاولة تتبع redirects
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # زيارة الصفحة مع تتبع كامل
    url = "https://elines.coscoshipping.com/ebusiness/cargoTracking"
    
    try:
        response = session.get(url, timeout=20, allow_redirects=True)
        
        print(f"📊 Final URL: {response.url}")
        print(f"📊 Status: {response.status_code}")
        print(f"📊 Headers: {dict(response.headers)}")
        
        # البحث في response headers عن API hints
        for header, value in response.headers.items():
            if 'api' in header.lower() or 'api' in str(value).lower():
                print(f"🔗 API hint in header {header}: {value}")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

def main():
    print("=" * 80)
    print("🔬 تحليل JavaScript لموقع COSCO")
    print("=" * 80)
    
    # تحليل JavaScript الرئيسي
    js_content = analyze_main_js()
    
    # البحث عن API endpoint الحقيقي
    find_real_api_endpoint()
    
    # تحليل network traffic
    analyze_network_traffic()
    
    print("\n" + "=" * 80)
    print("✅ انتهى التحليل")
    print("=" * 80)

if __name__ == "__main__":
    main()
