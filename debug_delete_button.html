<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص زر الحذف</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h2>تشخيص زر الحذف</h2>
        
        <div class="alert alert-info">
            افتح Developer Tools (F12) وانظر إلى Console للرسائل التشخيصية
        </div>
        
        <table class="table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>اسم المندوب</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>أحمد محمد الأحمد</td>
                    <td>
                        <div class="btn-group">
                            <button class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" 
                                    id="delete-btn-1"
                                    onclick="deleteRepresentative(1, 'أحمد محمد الأحمد')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>سارة أحمد</td>
                    <td>
                        <div class="btn-group">
                            <button class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" 
                                    id="delete-btn-2"
                                    onclick="deleteRepresentative(2, 'سارة أحمد')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <div class="mt-3">
            <button class="btn btn-primary" onclick="testFunction()">
                اختبار دالة بسيطة
            </button>
        </div>
    </div>

    <script>
        function testFunction() {
            console.log('Test function called');
            alert('دالة الاختبار تعمل!');
        }
        
        function deleteRepresentative(id, name) {
            console.log('deleteRepresentative called with:', id, name);
            if (confirm(`هل أنت متأكد من حذف المندوب "${name}"؟\n\nتحذير: سيتم حذف جميع البيانات المرتبطة بهذا المندوب نهائياً.`)) {
                console.log('User confirmed deletion');
                
                // إظهار loading indicator
                const deleteBtn = document.getElementById(`delete-btn-${id}`);
                console.log('Delete button found:', deleteBtn);
                if (deleteBtn) {
                    deleteBtn.disabled = true;
                    deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                }
                
                // إنشاء form للحذف
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/purchase-commissions/representatives/delete/${id}`;
                form.style.display = 'none';
                
                // إضافة CSRF token إذا كان متوفراً
                const csrfToken = document.querySelector('meta[name="csrf-token"]');
                if (csrfToken) {
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrf_token';
                    csrfInput.value = csrfToken.getAttribute('content');
                    form.appendChild(csrfInput);
                }
                
                document.body.appendChild(form);
                console.log('Form created and submitted:', form.action);
                
                // للاختبار - عرض رسالة بدلاً من الإرسال الفعلي
                alert(`سيتم إرسال طلب حذف إلى: ${form.action}`);
                
                // في البيئة الحقيقية:
                // form.submit();
            } else {
                console.log('User cancelled deletion');
            }
        }
        
        // اختبار تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded successfully');
            console.log('Delete buttons found:', document.querySelectorAll('[id^="delete-btn-"]').length);
        });
    </script>
</body>
</html>
