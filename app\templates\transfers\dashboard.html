<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم نظام الحوالات - نظام الفوجي</title>

    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom Styles -->
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --dark-bg: #2c3e50;
            --border-color: #dee2e6;
            --text-muted: #6c757d;
            --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        /* Professional Header */
        .dashboard-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0.5rem 0 0 0;
            font-weight: 300;
        }

        /* Enterprise Buttons */
        .btn-enterprise {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            border: none;
            box-shadow: var(--shadow);
        }

        .btn-primary-enterprise {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
            color: white;
        }

        .btn-primary-enterprise:hover {
            background: linear-gradient(135deg, #2980b9 0%, var(--secondary-color) 100%);
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .btn-secondary-enterprise {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            color: white;
        }

        .btn-secondary-enterprise:hover {
            background: linear-gradient(135deg, #7f8c8d 0%, #95a5a6 100%);
            transform: translateY(-2px);
        }

        /* Header Actions */
        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        /* Breadcrumb Navigation */
        .breadcrumb-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        .breadcrumb-custom {
            background: none;
            margin: 0;
            padding: 0;
            font-size: 0.95rem;
        }

        .breadcrumb-custom .breadcrumb-item {
            color: var(--text-muted);
            font-weight: 500;
        }

        .breadcrumb-custom .breadcrumb-item.active {
            color: var(--primary-color);
            font-weight: 600;
        }

        .breadcrumb-custom .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: var(--text-muted);
            font-weight: 600;
            font-size: 1.1rem;
        }

        .breadcrumb-custom a {
            color: var(--secondary-color);
            text-decoration: none;
            transition: all 0.3s ease;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
        }

        .breadcrumb-custom a:hover {
            color: var(--primary-color);
            background-color: rgba(52, 152, 219, 0.1);
            transform: translateY(-1px);
        }

        .breadcrumb-icon {
            margin-left: 0.5rem;
            font-size: 0.9rem;
        }

        /* Enhanced Stats Cards */
        .stats-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--secondary-color), var(--success-color));
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 1.5rem 3rem rgba(0, 0, 0, 0.2);
        }

        .stats-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stats-icon.primary {
            background: linear-gradient(135deg, var(--primary-color), #34495e);
        }

        .stats-icon.success {
            background: linear-gradient(135deg, var(--success-color), #229954);
        }

        .stats-icon.warning {
            background: linear-gradient(135deg, var(--warning-color), #e67e22);
        }

        .stats-icon.danger {
            background: linear-gradient(135deg, var(--danger-color), #c0392b);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin: 0.5rem 0;
        }

        .stats-label {
            font-size: 1rem;
            color: var(--text-muted);
            font-weight: 500;
        }

        .stats-change {
            font-size: 0.875rem;
            font-weight: 600;
            margin-top: 0.5rem;
        }

        .stats-change.positive {
            color: var(--success-color);
        }

        .stats-change.negative {
            color: var(--danger-color);
        }

        /* Quick Actions */
        .quick-actions {
            background: white;
            border-radius: 16px;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
        }

        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-color);
            margin: 0;
        }

        .action-btn {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
            border: none;
            border-radius: 12px;
            color: white;
            padding: 1.5rem;
            margin: 0.75rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
            text-align: center;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            color: white;
            background: linear-gradient(135deg, #2980b9 0%, var(--secondary-color) 100%);
        }

        .action-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .action-label {
            font-weight: 600;
            font-size: 1rem;
        }

        /* Chart Container */
        .chart-container {
            background: white;
            border-radius: 16px;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        /* Recent Activity */
        .recent-activity {
            background: white;
            border-radius: 16px;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
        }

        .activity-item {
            border-bottom: 1px solid var(--border-color);
            padding: 1.5rem;
            transition: background-color 0.3s ease;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-item:hover {
            background-color: #f8f9fa;
        }

        .activity-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1rem;
            font-size: 1.25rem;
        }

        .activity-success {
            background: linear-gradient(135deg, var(--success-color), #229954);
            color: white;
        }

        .activity-warning {
            background: linear-gradient(135deg, var(--warning-color), #e67e22);
            color: white;
        }

        .activity-info {
            background: linear-gradient(135deg, var(--info-color), #138496);
            color: white;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.25rem;
        }

        .activity-description {
            color: var(--text-muted);
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .activity-time {
            color: var(--text-muted);
            font-size: 0.8rem;
        }

        /* Alert Items */
        .alert-item {
            border-radius: 12px;
            border: none;
            margin-bottom: 1rem;
            box-shadow: var(--shadow);
        }
    </style>
</head>
<body>
<!-- Professional Header -->
<div class="dashboard-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="page-title">
                    <i class="fas fa-money-bill-transfer ms-3"></i>
                    لوحة تحكم نظام الحوالات
                </h1>
                <p class="page-subtitle">
                    إدارة ومراقبة شاملة لجميع عمليات الحوالات المالية مع تحليلات متقدمة ولوحة تحكم تفاعلية
                </p>
            </div>
            <div class="col-lg-4">
                <div class="header-actions d-flex gap-2 justify-content-lg-end">
                    <button class="btn-enterprise btn-secondary-enterprise" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                    <div class="dropdown">
                        <button class="btn-enterprise btn-secondary-enterprise dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-download"></i>
                            تقارير
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="exportDashboardReport()"><i class="fas fa-file-excel me-2"></i>تقرير Excel</a></li>
                            <li><a class="dropdown-item" href="#" onclick="printDashboard()"><i class="fas fa-print me-2"></i>طباعة</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="breadcrumb-container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb breadcrumb-custom">
                <li class="breadcrumb-item">
                    <a href="/" onclick="navigateToHome()">
                        <i class="fas fa-home breadcrumb-icon"></i>
                        الرئيسية
                    </a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">
                    <i class="fas fa-money-bill-transfer breadcrumb-icon"></i>
                    لوحة تحكم نظام الحوالات
                </li>
            </ol>
        </nav>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-icon primary">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                </div>
                <div class="stats-number">{{ stats.total_requests or 0 }}</div>
                <div class="stats-label">إجمالي الطلبات</div>
                <div class="stats-change positive">
                    <i class="fas fa-arrow-up"></i> +12% من الشهر الماضي
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-icon warning">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="stats-number">{{ stats.pending_requests or 0 }}</div>
                <div class="stats-label">طلبات معلقة</div>
                <div class="stats-change negative">
                    <i class="fas fa-arrow-down"></i> -5% من الأسبوع الماضي
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-icon success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="stats-number">{{ stats.executed_transfers or 0 }}</div>
                <div class="stats-label">حوالات منفذة</div>
                <div class="stats-change positive">
                    <i class="fas fa-arrow-up"></i> +8% من الأسبوع الماضي
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-icon danger">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
                <div class="stats-number">${{ "{:,.0f}".format(stats.total_amount_today or 0) }}</div>
                <div class="stats-label">مبلغ اليوم</div>
                <div class="stats-change positive">
                    <i class="fas fa-arrow-up"></i> +15% من أمس
                </div>
            </div>
        </div>
    </div>

    <!-- إجراءات سريعة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="quick-actions">
                <div class="section-header">
                    <h5 class="section-title">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="row p-3">
                    <div class="col-lg-3 col-md-6">
                        <a href="{{ url_for('transfers.new_request') }}" class="action-btn">
                            <i class="fas fa-plus-circle action-icon"></i>
                            <div class="action-label">طلب حوالة جديدة</div>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="{{ url_for('transfers.list_requests') }}?status=pending" class="action-btn">
                            <i class="fas fa-clock action-icon"></i>
                            <div class="action-label">الطلبات المعلقة</div>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="{{ url_for('transfers.execution') }}" class="action-btn">
                            <i class="fas fa-play-circle action-icon"></i>
                            <div class="action-label">تنفيذ الحوالات</div>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="{{ url_for('transfers.list_requests') }}" class="action-btn">
                            <i class="fas fa-search action-icon"></i>
                            <div class="action-label">تتبع الحوالات</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- آخر الأنشطة -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        آخر الأنشطة
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_activities %}
                        {% for activity in recent_activities %}
                        <div class="activity-item {{ activity.type }}">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ activity.number }}</strong>
                                    <span class="text-muted ms-2">
                                        {% if activity.type == 'request' %}
                                            طلب حوالة
                                        {% else %}
                                            حوالة منفذة
                                        {% endif %}
                                    </span>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold">${{ "{:,.2f}".format(activity.amount) }} {{ activity.currency }}</div>
                                    <small class="text-muted">{{ activity.date.strftime('%Y-%m-%d %H:%M') if activity.date else '' }}</small>
                                </div>
                            </div>
                            <div class="mt-1">
                                <span class="badge bg-{{ 'success' if activity.status == 'completed' else 'warning' if activity.status == 'pending' else 'info' }}">
                                    {{ activity.status }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>لا توجد أنشطة حديثة</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- التنبيهات والإشعارات -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bell me-2"></i>
                        التنبيهات
                    </h5>
                </div>
                <div class="card-body" id="alertsContainer">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                        <p>جاري تحميل التنبيهات...</p>
                    </div>
                </div>
            </div>

            <!-- إحصائيات إضافية -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        إحصائيات إضافية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>العمولة اليوم</span>
                            <strong>${{ "{:,.2f}".format(stats.total_commission_today or 0) }}</strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>الطلبات المعتمدة</span>
                            <strong>{{ stats.approved_requests or 0 }}</strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>متوسط المبلغ</span>
                            <strong>
                                {% if stats.executed_transfers and stats.executed_transfers > 0 %}
                                    ${{ "{:,.0f}".format((stats.total_amount_today or 0) / stats.executed_transfers) }}
                                {% else %}
                                    $0
                                {% endif %}
                            </strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
function refreshDashboard() {
    location.reload();
}

// تحميل التنبيهات
function loadAlerts() {
    // تعطيل تحميل التنبيهات مؤقتاً
    displayAlerts([]);
    return;

    fetch('/transfers/api/dashboard-widgets')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayAlerts(data.data.alerts || []);
            } else {
                console.error('خطأ في تحميل التنبيهات:', data.message);
            }
        })
        .catch(error => {
            console.error('خطأ في الاتصال:', error);
            document.getElementById('alertsContainer').innerHTML = `
                <div class="text-center text-danger py-4">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                    <p>خطأ في تحميل التنبيهات</p>
                </div>
            `;
        });
}

function displayAlerts(alerts) {
    const container = document.getElementById('alertsContainer');
    
    if (alerts.length === 0) {
        container.innerHTML = `
            <div class="text-center text-success py-4">
                <i class="fas fa-check-circle fa-2x mb-3"></i>
                <p>لا توجد تنبيهات</p>
            </div>
        `;
        return;
    }
    
    let html = '';
    alerts.forEach(alert => {
        html += `
            <div class="alert alert-${alert.type} alert-item">
                <div class="d-flex align-items-center">
                    <i class="fas fa-${alert.type === 'warning' ? 'exclamation-triangle' : alert.type === 'danger' ? 'times-circle' : 'info-circle'} me-2"></i>
                    <div class="flex-grow-1">
                        <small>${alert.message}</small>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// تحميل التنبيهات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadAlerts();

    // تحديث التنبيهات كل 30 ثانية
    setInterval(loadAlerts, 30000);
});

// دوال التنقل في مسار التنقل
function navigateToHome() {
    window.location.href = '/';
}

// دوال التصدير والطباعة
function exportDashboardReport() {
    alert('تصدير تقرير Excel - قيد التطوير');
}

function printDashboard() {
    window.print();
}
</script>

</body>
</html>
