-- ========================================================================
-- تنفيذ Packages النظام المحاسبي الموحد
-- Unified Accounting System Packages Implementation
-- ========================================================================

-- ========================================================================
-- تنفيذ Package الأرصدة الافتتاحية
-- Opening Balances Package Implementation
-- ========================================================================

CREATE OR REPLACE PACKAGE BODY OB_PKG AS

    -- إدراج رصيد افتتاحي
    PROCEDURE INSERT_BALANCE(
        p_entity_type_code IN VARCHAR2,
        p_entity_id IN NUMBER,
        p_currency_code IN VARCHAR2,
        p_balance_amount IN NUMBER,
        p_branch_id IN NUMBER DEFAULT 1,
        p_year_number IN NUMBER DEFAULT EXTRACT(YEAR FROM SYSDATE),
        p_user_id IN NUMBER DEFAULT 1
    ) AS
        v_document_number VARCHAR2(50);
        v_balance_amount NUMBER;
        v_balance_f_amount NUMBER;
    BEGIN
        -- إنشاء رقم مستند فريد
        v_document_number := 'OB-' || p_year_number || '-' || p_entity_type_code || '-' || p_entity_id;
        
        -- تحديد قيم الرصيد
        v_balance_amount := p_balance_amount;
        v_balance_f_amount := p_balance_amount; -- افتراض عدم وجود تحويل عملة
        
        -- إدراج في BALANCE_TRANSACTIONS
        INSERT INTO BALANCE_TRANSACTIONS (
            entity_type_code, entity_id, document_type_code,
            document_number, document_date, currency_code,
            debit_amount, credit_amount, balance, balance_f,
            month_number, year_number, branch_id,
            description, status, created_by, created_date
        ) VALUES (
            p_entity_type_code, p_entity_id, 'OPENING_BALANCE',
            v_document_number, DATE '2024-01-01', p_currency_code,
            CASE WHEN p_balance_amount > 0 THEN p_balance_amount ELSE 0 END,
            CASE WHEN p_balance_amount < 0 THEN ABS(p_balance_amount) ELSE 0 END,
            v_balance_amount, v_balance_f_amount,
            1, p_year_number, p_branch_id,
            'Opening balance for ' || p_entity_type_code || ' ' || p_entity_id,
            'POSTED', p_user_id, CURRENT_TIMESTAMP
        );
        
        COMMIT;
    END INSERT_BALANCE;

    -- تعديل رصيد افتتاحي
    PROCEDURE UPDATE_BALANCE(
        p_entity_type_code IN VARCHAR2,
        p_entity_id IN NUMBER,
        p_currency_code IN VARCHAR2,
        p_new_balance_amount IN NUMBER,
        p_branch_id IN NUMBER DEFAULT 1,
        p_year_number IN NUMBER DEFAULT EXTRACT(YEAR FROM SYSDATE),
        p_user_id IN NUMBER DEFAULT 1
    ) AS
        v_old_balance NUMBER;
        v_adjustment_amount NUMBER;
        v_document_number VARCHAR2(50);
    BEGIN
        -- الحصول على الرصيد الحالي
        v_old_balance := GET_BALANCE(
            p_entity_type_code, p_entity_id, p_currency_code,
            p_branch_id, p_year_number
        );
        
        -- حساب مبلغ التعديل
        v_adjustment_amount := p_new_balance_amount - v_old_balance;
        
        IF v_adjustment_amount != 0 THEN
            -- إنشاء رقم مستند للتعديل
            v_document_number := 'OB-ADJ-' || p_year_number || '-' || p_entity_type_code || '-' || p_entity_id;
            
            -- إدراج معاملة التعديل
            INSERT INTO BALANCE_TRANSACTIONS (
                entity_type_code, entity_id, document_type_code,
                document_number, document_date, currency_code,
                debit_amount, credit_amount, balance, balance_f,
                month_number, year_number, branch_id,
                description, status, created_by, created_date
            ) VALUES (
                p_entity_type_code, p_entity_id, 'OPENING_BALANCE_ADJUSTMENT',
                v_document_number, SYSDATE, p_currency_code,
                CASE WHEN v_adjustment_amount > 0 THEN v_adjustment_amount ELSE 0 END,
                CASE WHEN v_adjustment_amount < 0 THEN ABS(v_adjustment_amount) ELSE 0 END,
                v_adjustment_amount, v_adjustment_amount,
                EXTRACT(MONTH FROM SYSDATE), p_year_number, p_branch_id,
                'Opening balance adjustment for ' || p_entity_type_code || ' ' || p_entity_id,
                'POSTED', p_user_id, CURRENT_TIMESTAMP
            );
            
            COMMIT;
        END IF;
    END UPDATE_OPENING_BALANCE;

    -- حذف رصيد افتتاحي
    PROCEDURE DELETE_OPENING_BALANCE(
        p_entity_type_code IN VARCHAR2,
        p_entity_id IN NUMBER,
        p_currency_code IN VARCHAR2,
        p_branch_id IN NUMBER DEFAULT 1,
        p_year_number IN NUMBER DEFAULT EXTRACT(YEAR FROM SYSDATE)
    ) AS
        v_current_balance NUMBER;
        v_document_number VARCHAR2(50);
    BEGIN
        -- الحصول على الرصيد الحالي
        v_current_balance := GET_OPENING_BALANCE(
            p_entity_type_code, p_entity_id, p_currency_code, 
            p_branch_id, p_year_number
        );
        
        IF v_current_balance != 0 THEN
            -- إنشاء رقم مستند للحذف
            v_document_number := 'OB-DEL-' || p_year_number || '-' || p_entity_type_code || '-' || p_entity_id;
            
            -- إدراج معاملة عكسية لإلغاء الرصيد
            INSERT INTO BALANCE_TRANSACTIONS (
                entity_type_code, entity_id, document_type_code,
                document_number, document_date, currency_code,
                debit_amount, credit_amount, balance, balance_f,
                month_number, year_number, branch_id,
                description, status, created_by, created_date
            ) VALUES (
                p_entity_type_code, p_entity_id, 'OPENING_BALANCE_DELETION',
                v_document_number, SYSDATE, p_currency_code,
                CASE WHEN v_current_balance < 0 THEN ABS(v_current_balance) ELSE 0 END,
                CASE WHEN v_current_balance > 0 THEN v_current_balance ELSE 0 END,
                -v_current_balance, -v_current_balance,
                EXTRACT(MONTH FROM SYSDATE), p_year_number, p_branch_id,
                'Opening balance deletion for ' || p_entity_type_code || ' ' || p_entity_id,
                'POSTED', 1, CURRENT_TIMESTAMP
            );
            
            COMMIT;
        END IF;
    END DELETE_OPENING_BALANCE;

    -- الحصول على رصيد افتتاحي
    FUNCTION GET_OPENING_BALANCE(
        p_entity_type_code IN VARCHAR2,
        p_entity_id IN NUMBER,
        p_currency_code IN VARCHAR2,
        p_branch_id IN NUMBER DEFAULT 1,
        p_year_number IN NUMBER DEFAULT EXTRACT(YEAR FROM SYSDATE)
    ) RETURN NUMBER AS
        v_balance NUMBER := 0;
    BEGIN
        SELECT NVL(SUM(balance), 0)
        INTO v_balance
        FROM BALANCE_TRANSACTIONS
        WHERE entity_type_code = p_entity_type_code
        AND entity_id = p_entity_id
        AND currency_code = p_currency_code
        AND branch_id = p_branch_id
        AND year_number = p_year_number
        AND document_type_code IN ('OPENING_BALANCE', 'OPENING_BALANCE_ADJUSTMENT');
        
        RETURN v_balance;
    EXCEPTION
        WHEN OTHERS THEN
            RETURN 0;
    END GET_OPENING_BALANCE;

END OPENING_BALANCES_PKG;
/

-- ========================================================================
-- تنفيذ Package ترحيل الأرصدة
-- Balance Transactions Package Implementation
-- ========================================================================

CREATE OR REPLACE PACKAGE BODY BALANCE_TRANSACTIONS_PKG AS

    -- ترحيل معاملة
    PROCEDURE POST_TRANSACTION(
        p_entity_type_code IN VARCHAR2,
        p_entity_id IN NUMBER,
        p_document_type_code IN VARCHAR2,
        p_document_number IN VARCHAR2,
        p_document_date IN DATE,
        p_currency_code IN VARCHAR2,
        p_debit_amount IN NUMBER DEFAULT 0,
        p_credit_amount IN NUMBER DEFAULT 0,
        p_exchange_rate IN NUMBER DEFAULT 1,
        p_description IN VARCHAR2 DEFAULT NULL,
        p_branch_id IN NUMBER DEFAULT 1,
        p_user_id IN NUMBER DEFAULT 1
    ) AS
        v_balance NUMBER;
        v_balance_f NUMBER;
        v_month_number NUMBER;
        v_year_number NUMBER;
    BEGIN
        -- حساب الرصيد الموحد
        v_balance := NVL(p_debit_amount, 0) - NVL(p_credit_amount, 0);
        
        -- حساب الرصيد بالعملة الأساسية
        v_balance_f := v_balance * NVL(p_exchange_rate, 1);
        
        -- استخراج الشهر والسنة
        v_month_number := EXTRACT(MONTH FROM p_document_date);
        v_year_number := EXTRACT(YEAR FROM p_document_date);
        
        -- إدراج المعاملة
        INSERT INTO BALANCE_TRANSACTIONS (
            entity_type_code, entity_id, document_type_code,
            document_number, document_date, currency_code,
            debit_amount, credit_amount, exchange_rate,
            balance, balance_f,
            month_number, year_number, branch_id,
            description, status, created_by, created_date
        ) VALUES (
            p_entity_type_code, p_entity_id, p_document_type_code,
            p_document_number, p_document_date, p_currency_code,
            NVL(p_debit_amount, 0), NVL(p_credit_amount, 0), NVL(p_exchange_rate, 1),
            v_balance, v_balance_f,
            v_month_number, v_year_number, p_branch_id,
            p_description, 'POSTED', p_user_id, CURRENT_TIMESTAMP
        );
        
        COMMIT;
    END POST_TRANSACTION;

    -- عكس معاملة
    PROCEDURE REVERSE_TRANSACTION(
        p_original_document_number IN VARCHAR2,
        p_reversal_document_number IN VARCHAR2,
        p_reversal_reason IN VARCHAR2,
        p_user_id IN NUMBER DEFAULT 1
    ) AS
        CURSOR c_original_transactions IS
            SELECT * FROM BALANCE_TRANSACTIONS
            WHERE document_number = p_original_document_number
            AND status = 'POSTED';
    BEGIN
        -- عكس كل معاملة مرتبطة بالمستند الأصلي
        FOR rec IN c_original_transactions LOOP
            INSERT INTO BALANCE_TRANSACTIONS (
                entity_type_code, entity_id, document_type_code,
                document_number, document_date, currency_code,
                debit_amount, credit_amount, exchange_rate,
                balance, balance_f,
                month_number, year_number, branch_id,
                description, reference_number, status, created_by, created_date
            ) VALUES (
                rec.entity_type_code, rec.entity_id, rec.document_type_code || '_REVERSAL',
                p_reversal_document_number, SYSDATE, rec.currency_code,
                rec.credit_amount, rec.debit_amount, rec.exchange_rate,
                -rec.balance, -rec.balance_f,
                EXTRACT(MONTH FROM SYSDATE), EXTRACT(YEAR FROM SYSDATE), rec.branch_id,
                'Reversal: ' || p_reversal_reason, p_original_document_number,
                'POSTED', p_user_id, CURRENT_TIMESTAMP
            );
        END LOOP;
        
        COMMIT;
    END REVERSE_TRANSACTION;

    -- الحصول على الرصيد الحالي
    FUNCTION GET_CURRENT_BALANCE(
        p_entity_type_code IN VARCHAR2,
        p_entity_id IN NUMBER,
        p_currency_code IN VARCHAR2,
        p_branch_id IN NUMBER DEFAULT 1,
        p_as_of_date IN DATE DEFAULT SYSDATE
    ) RETURN NUMBER AS
        v_balance NUMBER := 0;
    BEGIN
        SELECT NVL(SUM(balance), 0)
        INTO v_balance
        FROM BALANCE_TRANSACTIONS
        WHERE entity_type_code = p_entity_type_code
        AND entity_id = p_entity_id
        AND currency_code = p_currency_code
        AND branch_id = p_branch_id
        AND document_date <= p_as_of_date
        AND status = 'POSTED';
        
        RETURN v_balance;
    EXCEPTION
        WHEN OTHERS THEN
            RETURN 0;
    END GET_CURRENT_BALANCE;

    -- الحصول على رصيد شهري
    FUNCTION GET_MONTHLY_BALANCE(
        p_entity_type_code IN VARCHAR2,
        p_entity_id IN NUMBER,
        p_currency_code IN VARCHAR2,
        p_year_number IN NUMBER,
        p_month_number IN NUMBER,
        p_branch_id IN NUMBER DEFAULT 1
    ) RETURN NUMBER AS
        v_balance NUMBER := 0;
    BEGIN
        SELECT NVL(SUM(balance), 0)
        INTO v_balance
        FROM BALANCE_TRANSACTIONS
        WHERE entity_type_code = p_entity_type_code
        AND entity_id = p_entity_id
        AND currency_code = p_currency_code
        AND branch_id = p_branch_id
        AND year_number = p_year_number
        AND month_number = p_month_number
        AND status = 'POSTED';
        
        RETURN v_balance;
    EXCEPTION
        WHEN OTHERS THEN
            RETURN 0;
    END GET_MONTHLY_BALANCE;

    -- الحصول على تاريخ الرصيد
    PROCEDURE GET_BALANCE_HISTORY(
        p_entity_type_code IN VARCHAR2,
        p_entity_id IN NUMBER,
        p_currency_code IN VARCHAR2,
        p_from_date IN DATE,
        p_to_date IN DATE,
        p_branch_id IN NUMBER DEFAULT 1,
        p_cursor OUT SYS_REFCURSOR
    ) AS
    BEGIN
        OPEN p_cursor FOR
            SELECT 
                document_date,
                document_type_code,
                document_number,
                debit_amount,
                credit_amount,
                balance,
                SUM(balance) OVER (ORDER BY document_date, created_date) as running_balance,
                description
            FROM BALANCE_TRANSACTIONS
            WHERE entity_type_code = p_entity_type_code
            AND entity_id = p_entity_id
            AND currency_code = p_currency_code
            AND branch_id = p_branch_id
            AND document_date BETWEEN p_from_date AND p_to_date
            AND status = 'POSTED'
            ORDER BY document_date, created_date;
    END GET_BALANCE_HISTORY;

END BALANCE_TRANSACTIONS_PKG;
/
