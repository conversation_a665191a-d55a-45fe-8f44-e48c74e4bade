{% extends "base.html" %}

{% block content %}
<style>
.form-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid #007bff;
}

.form-section h6 {
    color: #007bff;
    font-weight: 600;
    margin-bottom: 1rem;
}

.cost-calculator {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 1.5rem;
}

.address-autocomplete {
    position: relative;
}

.suggestions-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.suggestion-item {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.suggestion-item:hover {
    background: #f8f9fa;
}

.tracking-preview {
    background: #e3f2fd;
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
}

.tracking-number-display {
    font-family: 'Courier New', monospace;
    font-size: 1.5rem;
    font-weight: bold;
    color: #1976d2;
}
</style>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-plus-circle text-primary me-2"></i>
                        إنشاء شحنة جديدة
                    </h1>
                    <p class="text-muted mb-0">إنشاء شحنة جديدة مع تتبع متطور</p>
                </div>
                <div>
                    <a href="{{ url_for('shipments.dashboard_fullscreen') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form id="shipmentForm" method="POST">
        <div class="row">
            <!-- Main Form -->
            <div class="col-lg-8">
                <!-- معلومات المرسل -->
                <div class="form-section">
                    <h6><i class="fas fa-user me-2"></i>معلومات المرسل</h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم المرسل *</label>
                            <input type="text" class="form-control" name="sender_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الهاتف *</label>
                            <input type="tel" class="form-control" name="sender_phone" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="sender_email">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">المدينة *</label>
                            <input type="text" class="form-control" name="sender_city" required>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">العنوان التفصيلي *</label>
                            <div class="address-autocomplete">
                                <textarea class="form-control" name="sender_address" rows="2" required></textarea>
                                <div class="suggestions-dropdown" id="senderSuggestions"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات المستقبل -->
                <div class="form-section">
                    <h6><i class="fas fa-user-check me-2"></i>معلومات المستقبل</h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم المستقبل *</label>
                            <input type="text" class="form-control" name="recipient_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الهاتف *</label>
                            <input type="tel" class="form-control" name="recipient_phone" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="recipient_email">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">المدينة *</label>
                            <input type="text" class="form-control" name="recipient_city" required>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">العنوان التفصيلي *</label>
                            <div class="address-autocomplete">
                                <textarea class="form-control" name="recipient_address" rows="2" required></textarea>
                                <div class="suggestions-dropdown" id="recipientSuggestions"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الشحنة -->
                <div class="form-section">
                    <h6><i class="fas fa-box me-2"></i>تفاصيل الشحنة</h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">نوع الشحنة</label>
                            <select class="form-select" name="shipment_type" onchange="calculateCost()">
                                {% for type in shipment_types %}
                                <option value="{{ type.value }}">{{ type.value }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الأولوية</label>
                            <select class="form-select" name="priority" onchange="calculateCost()">
                                {% for priority in priorities %}
                                <option value="{{ priority }}">{{ priority }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">الوزن (كجم) *</label>
                            <input type="number" class="form-control" name="weight" step="0.1" min="0" required onchange="calculateCost()">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">الطول (سم)</label>
                            <input type="number" class="form-control" name="length" step="0.1" min="0" onchange="calculateCost()">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">العرض (سم)</label>
                            <input type="number" class="form-control" name="width" step="0.1" min="0" onchange="calculateCost()">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">الارتفاع (سم)</label>
                            <input type="number" class="form-control" name="height" step="0.1" min="0" onchange="calculateCost()">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">القيمة المعلنة (ريال)</label>
                            <input type="number" class="form-control" name="declared_value" step="0.01" min="0" onchange="calculateCost()">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">شركة الشحن</label>
                            <select class="form-select" name="carrier_id">
                                <option value="">اختيار تلقائي</option>
                                {% for carrier in carriers %}
                                <option value="{{ carrier.id }}">{{ carrier.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">وصف المحتويات</label>
                            <textarea class="form-control" name="description" rows="2"></textarea>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">تعليمات خاصة</label>
                            <textarea class="form-control" name="special_instructions" rows="2"></textarea>
                        </div>
                        <div class="col-12">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" name="fragile" id="fragile">
                                <label class="form-check-label" for="fragile">قابل للكسر</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" name="signature_required" id="signature_required">
                                <label class="form-check-label" for="signature_required">يتطلب توقيع</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- معاينة رقم التتبع -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <div class="tracking-preview">
                            <h6 class="mb-2">رقم التتبع المتوقع</h6>
                            <div class="tracking-number-display" id="previewTrackingNumber">
                                SAS{{ moment().format('YYYYMMDD') }}######
                            </div>
                            <small class="text-muted">سيتم إنشاؤه تلقائياً عند الحفظ</small>
                        </div>
                    </div>
                </div>

                <!-- حاسبة التكلفة -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body p-0">
                        <div class="cost-calculator">
                            <h6 class="mb-3">
                                <i class="fas fa-calculator me-2"></i>
                                حاسبة التكلفة
                            </h6>
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="mb-2">
                                        <small>التكلفة الأساسية</small>
                                        <div class="h5 mb-0" id="baseCost">25.00</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-2">
                                        <small>تكلفة الوزن</small>
                                        <div class="h5 mb-0" id="weightCost">0.00</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-2">
                                        <small>تكلفة المسافة</small>
                                        <div class="h5 mb-0" id="distanceCost">0.00</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-2">
                                        <small>رسوم إضافية</small>
                                        <div class="h5 mb-0" id="additionalCost">0.00</div>
                                    </div>
                                </div>
                            </div>
                            <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
                            <div class="text-center">
                                <small>إجمالي التكلفة المقدرة</small>
                                <div class="h3 mb-0" id="totalCost">25.00 ريال</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- خريطة المسار -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="mb-0">
                            <i class="fas fa-route me-2"></i>
                            معاينة المسار
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        <div id="routePreviewMap" style="height: 200px; background: #f8f9fa; display: flex; align-items: center; justify-content: center;">
                            <div class="text-center text-muted">
                                <i class="fas fa-map-marked-alt mb-2" style="font-size: 2rem;"></i>
                                <p class="mb-0">أدخل العناوين لمعاينة المسار</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>
                        إنشاء الشحنة
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="previewShipment()">
                        <i class="fas fa-eye me-2"></i>
                        معاينة
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="saveDraft()">
                        <i class="fas fa-file-alt me-2"></i>
                        حفظ كمسودة
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
// حاسبة التكلفة
function calculateCost() {
    const weight = parseFloat(document.querySelector('[name="weight"]').value) || 0;
    const length = parseFloat(document.querySelector('[name="length"]').value) || 0;
    const width = parseFloat(document.querySelector('[name="width"]').value) || 0;
    const height = parseFloat(document.querySelector('[name="height"]').value) || 0;
    const declaredValue = parseFloat(document.querySelector('[name="declared_value"]').value) || 0;
    const shipmentType = document.querySelector('[name="shipment_type"]').value;
    const priority = document.querySelector('[name="priority"]').value;

    // التكلفة الأساسية
    let baseCost = 25.00;
    
    // تكلفة الوزن (5 ريال لكل كيلو)
    let weightCost = weight * 5.0;
    
    // تكلفة الحجم
    let volume = (length * width * height) / 1000000; // متر مكعب
    let volumeCost = volume * 10.0;
    
    // تكلفة نوع الشحنة
    const typeMultipliers = {
        'عادي': 1.0,
        'سريع': 1.5,
        'ليلي': 2.0,
        'نفس اليوم': 3.0,
        'دولي': 5.0
    };
    
    let typeMultiplier = typeMultipliers[shipmentType] || 1.0;
    
    // تكلفة الأولوية
    const priorityCosts = {
        'عادي': 0,
        'عالي': 15,
        'عاجل': 30
    };
    
    let priorityCost = priorityCosts[priority] || 0;
    
    // تكلفة التأمين
    let insuranceCost = declaredValue * 0.01;
    
    // الحساب النهائي
    let subtotal = (baseCost + weightCost + volumeCost) * typeMultiplier;
    let additionalCost = priorityCost + insuranceCost;
    let totalCost = subtotal + additionalCost;

    // تحديث العرض
    document.getElementById('baseCost').textContent = baseCost.toFixed(2);
    document.getElementById('weightCost').textContent = weightCost.toFixed(2);
    document.getElementById('distanceCost').textContent = '0.00'; // سيتم حسابها لاحقاً
    document.getElementById('additionalCost').textContent = additionalCost.toFixed(2);
    document.getElementById('totalCost').textContent = totalCost.toFixed(2) + ' ريال';
}

// معاينة الشحنة
function previewShipment() {
    alert('معاينة الشحنة - قريباً');
}

// حفظ كمسودة
function saveDraft() {
    alert('حفظ كمسودة - قريباً');
}

// تهيئة النموذج
document.addEventListener('DOMContentLoaded', function() {
    // حساب التكلفة الأولية
    calculateCost();
    
    // تهيئة النموذج
    document.getElementById('shipmentForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // جمع البيانات
        const formData = new FormData(this);
        
        // إرسال البيانات
        fetch('{{ url_for("shipments.new_shipment") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إنشاء الشحنة بنجاح!\nرقم التتبع: ' + data.tracking_number);
                window.location.href = `/shipments/track/${data.tracking_number}`;
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في إنشاء الشحنة');
        });
    });
});
</script>

{% endblock %}
