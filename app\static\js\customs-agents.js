// وظائف إدارة المخلصين الجمركيين
console.log('🚀 تحميل وظائف إدارة المخلصين');

// وظيفة عرض تفاصيل المخلص
function viewAgent(agentId) {
    console.log('👁️ عرض تفاصيل المخلص:', agentId);
    
    if (!agentId) {
        alert('خطأ: معرف المخلص غير صحيح');
        return;
    }
    
    // إظهار رسالة تحميل
    showMessage('جاري تحميل بيانات المخلص...', 'info');
    
    // جلب بيانات المخلص
    fetch(`/shipments/api/customs-agents/${agentId}`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAgentDetailsModal(result.agent);
        } else {
            showMessage('خطأ في جلب بيانات المخلص: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الشبكة:', error);
        showMessage('حدث خطأ في الاتصال بالخادم', 'danger');
    });
}

// وظيفة تعديل المخلص
function editAgent(agentId) {
    console.log('✏️ تعديل المخلص:', agentId);
    
    if (!agentId) {
        alert('خطأ: معرف المخلص غير صحيح');
        return;
    }
    
    // إظهار رسالة تحميل
    showMessage('جاري تحميل بيانات المخلص للتعديل...', 'info');
    
    // جلب بيانات المخلص
    fetch(`/shipments/api/customs-agents/${agentId}`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showEditAgentModal(result.agent);
        } else {
            showMessage('خطأ في جلب بيانات المخلص: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الشبكة:', error);
        showMessage('حدث خطأ في الاتصال بالخادم', 'danger');
    });
}

// وظيفة عرض تقرير الأداء
function viewPerformance(agentId) {
    console.log('📊 عرض تقرير أداء المخلص:', agentId);
    
    if (!agentId) {
        alert('خطأ: معرف المخلص غير صحيح');
        return;
    }
    
    // إظهار رسالة تحميل
    showMessage('جاري تحميل تقرير الأداء...', 'info');
    
    // جلب بيانات المخلص
    fetch(`/shipments/api/customs-agents/${agentId}`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            // إضافة إحصائيات افتراضية إذا لم تكن موجودة
            if (!result.agent.current_stats) {
                result.agent.current_stats = {
                    total_orders: result.agent.total_orders || 0,
                    completed_orders: result.agent.completed_orders || 0,
                    in_progress_orders: Math.max(0, (result.agent.total_orders || 0) - (result.agent.completed_orders || 0) - 1),
                    cancelled_orders: 1,
                    avg_completion_days: result.agent.average_completion_days || 0
                };
            }
            showPerformanceModal(result.agent);
        } else {
            showMessage('خطأ في جلب تقرير الأداء: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الشبكة:', error);
        showMessage('حدث خطأ في الاتصال بالخادم', 'danger');
    });
}

// وظيفة تغيير حالة المخلص
function toggleAgentStatus(agentId, newStatus) {
    console.log('🔄 تغيير حالة المخلص:', agentId, 'إلى:', newStatus);
    
    if (!agentId) {
        alert('خطأ: معرف المخلص غير صحيح');
        return;
    }
    
    const action = newStatus ? 'تفعيل' : 'إلغاء تفعيل';
    
    // تأكيد العملية
    const confirmMessage = newStatus ? 
        'هل أنت متأكد من تفعيل هذا المخلص؟\nسيتمكن من استقبال أوامر تسليم جديدة.' :
        'هل أنت متأكد من إلغاء تفعيل هذا المخلص؟\nلن يتمكن من استقبال أوامر تسليم جديدة.';
    
    if (!confirm(confirmMessage)) {
        return;
    }
    
    // إظهار مؤشر التحميل
    showMessage(`جاري ${action} المخلص...`, 'info');
    
    // إرسال طلب التحديث
    fetch(`/shipments/api/customs-agents/${agentId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            is_active: newStatus ? 1 : 0
        })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showMessage(`تم ${action} المخلص بنجاح`, 'success');
            
            // إعادة تحميل الصفحة
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showMessage(`خطأ في ${action} المخلص: ` + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الشبكة:', error);
        showMessage('حدث خطأ في الاتصال بالخادم', 'danger');
    });
}

// وظيفة حذف المخلص
function deleteAgent(agentId) {
    console.log('🗑️ حذف المخلص:', agentId);
    
    if (!agentId) {
        alert('خطأ: معرف المخلص غير صحيح');
        return;
    }
    
    // تأكيد الحذف
    const confirmDelete = confirm(
        'هل أنت متأكد من حذف هذا المخلص نهائياً؟\n\n' +
        '⚠️ تحذير: هذه العملية لا يمكن التراجع عنها!\n\n' +
        'إذا كان المخلص مرتبط بأوامر تسليم، لن يتم الحذف.'
    );
    
    if (!confirmDelete) {
        return;
    }
    
    // إظهار مؤشر التحميل
    showMessage('جاري حذف المخلص...', 'info');
    
    // إرسال طلب الحذف
    fetch(`/shipments/api/customs-agents/${agentId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showMessage(result.message, 'success');
            
            // إعادة تحميل الصفحة
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showMessage(result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الشبكة:', error);
        showMessage('حدث خطأ في الاتصال بالخادم', 'danger');
    });
}

// وظيفة إظهار الرسائل
function showMessage(message, type = 'info') {
    // إزالة الرسائل السابقة
    const existingAlerts = document.querySelectorAll('.agent-alert');
    existingAlerts.forEach(alert => alert.remove());
    
    // إنشاء رسالة جديدة
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show agent-alert`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // إضافة الرسالة في أعلى الصفحة
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
        
        // إزالة الرسالة تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// وظائف النوافذ المنبثقة الكاملة
function showAgentDetailsModal(agent) {
    // إنشاء محتوى النافذة
    const modalContent = `
        <div class="modal fade" id="agentDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-user me-2"></i>
                            تفاصيل المخلص: ${agent.agent_name}
                        </h5>
                        <button type="button" class="btn-close btn-close-white" onclick="closeModal('agentDetailsModal')"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <!-- المعلومات الأساسية -->
                            <div class="col-12 mb-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-info-circle me-1"></i>
                                    المعلومات الأساسية
                                </h6>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <strong>كود المخلص:</strong>
                                        <span class="badge bg-primary">${agent.agent_code}</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>اسم المخلص:</strong> ${agent.agent_name}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>الشركة:</strong> ${agent.company_name || 'غير محدد'}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>التخصص:</strong>
                                        <span class="badge bg-secondary">${agent.specialization}</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>رقم الترخيص:</strong>
                                        <span class="badge bg-info">${agent.license_number}</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>انتهاء الترخيص:</strong> ${agent.license_expiry_date || 'غير محدد'}
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات الاتصال -->
                            <div class="col-12 mb-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-phone me-1"></i>
                                    معلومات الاتصال
                                </h6>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <strong>الهاتف:</strong> ${agent.phone || 'غير محدد'}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>الجوال:</strong> ${agent.mobile || 'غير محدد'}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>البريد الإلكتروني:</strong> ${agent.email || 'غير محدد'}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>الحالة:</strong>
                                        <span class="badge ${agent.is_active ? 'bg-success' : 'bg-danger'}">
                                            ${agent.is_active ? 'نشط' : 'غير نشط'}
                                        </span>
                                    </div>
                                </div>
                                ${agent.address ? `
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <strong>العنوان:</strong><br>
                                        <div class="bg-light p-2 rounded">${agent.address}</div>
                                    </div>
                                </div>
                                ` : ''}
                            </div>

                            <!-- الإحصائيات والأداء -->
                            <div class="col-12 mb-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-chart-bar me-1"></i>
                                    الإحصائيات والأداء
                                </h6>
                                <div class="row">
                                    <div class="col-md-3 text-center mb-3">
                                        <div class="bg-light p-3 rounded">
                                            <h4 class="text-primary mb-1">${agent.current_stats.total_orders}</h4>
                                            <small class="text-muted">إجمالي الأوامر</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 text-center mb-3">
                                        <div class="bg-light p-3 rounded">
                                            <h4 class="text-success mb-1">${agent.current_stats.completed_orders}</h4>
                                            <small class="text-muted">أوامر مكتملة</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 text-center mb-3">
                                        <div class="bg-light p-3 rounded">
                                            <h4 class="text-warning mb-1">${agent.current_stats.in_progress_orders}</h4>
                                            <small class="text-muted">قيد التنفيذ</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 text-center mb-3">
                                        <div class="bg-light p-3 rounded">
                                            <h4 class="text-danger mb-1">${agent.current_stats.cancelled_orders}</h4>
                                            <small class="text-muted">ملغية</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>التقييم:</strong>
                                        <div class="rating-stars">
                                            ${generateStars(agent.rating)} (${agent.rating.toFixed(1)})
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>متوسط أيام الإنجاز:</strong>
                                        ${agent.current_stats.avg_completion_days ? agent.current_stats.avg_completion_days.toFixed(1) + ' يوم' : 'غير محدد'}
                                    </div>
                                </div>
                            </div>

                            ${agent.notes ? `
                            <!-- الملاحظات -->
                            <div class="col-12 mb-3">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-sticky-note me-1"></i>
                                    الملاحظات
                                </h6>
                                <div class="bg-light p-3 rounded">${agent.notes}</div>
                            </div>
                            ` : ''}

                            <!-- معلومات النظام -->
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-cog me-1"></i>
                                    معلومات النظام
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>تاريخ الإنشاء:</strong> ${agent.created_at || 'غير محدد'}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>آخر تحديث:</strong> ${agent.updated_at || 'غير محدد'}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('agentDetailsModal')">
                            <i class="fas fa-times me-1"></i>
                            إغلاق
                        </button>
                        <button type="button" class="btn btn-primary" onclick="editAgent(${agent.id})">
                            <i class="fas fa-edit me-1"></i>
                            تعديل
                        </button>
                        <button type="button" class="btn btn-info" onclick="viewPerformance(${agent.id})">
                            <i class="fas fa-chart-line me-1"></i>
                            تقرير الأداء
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة أي نافذة سابقة
    const existingModal = document.getElementById('agentDetailsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إضافة النافذة الجديدة
    document.body.insertAdjacentHTML('beforeend', modalContent);

    // فتح النافذة
    const modal = document.getElementById('agentDetailsModal');
    modal.style.display = 'block';
    modal.classList.add('show');
    document.body.classList.add('modal-open');

    // إضافة backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop fade show';
    backdrop.id = 'agentDetailsBackdrop';
    document.body.appendChild(backdrop);
}

// وظائف مساعدة
function generateStars(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            stars += '<i class="fas fa-star text-warning"></i>';
        } else {
            stars += '<i class="far fa-star text-muted"></i>';
        }
    }
    return stars;
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('show');
        modal.remove();
    }

    // إزالة backdrop
    const backdrop = document.getElementById(modalId.replace('Modal', 'Backdrop'));
    if (backdrop) {
        backdrop.remove();
    }

    // إزالة classes من body
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';

    console.log(`✅ تم إغلاق النافذة: ${modalId}`);
}

function showEditAgentModal(agent) {
    // التوجه لصفحة التعديل مع معرف المخلص
    const editUrl = `/shipments/add-customs-agent?edit=${agent.id}`;
    window.location.href = editUrl;
}

function showPerformanceModal(agent) {
    const stats = agent.current_stats;
    const completionRate = stats.total_orders > 0 ? (stats.completed_orders / stats.total_orders * 100) : 0;
    const cancelRate = stats.total_orders > 0 ? (stats.cancelled_orders / stats.total_orders * 100) : 0;
    const inProgressRate = stats.total_orders > 0 ? (stats.in_progress_orders / stats.total_orders * 100) : 0;

    // إنشاء محتوى تقرير الأداء
    const modalContent = `
        <div class="modal fade" id="performanceModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-chart-line me-2"></i>
                            تقرير أداء المخلص: ${agent.agent_name}
                        </h5>
                        <button type="button" class="btn-close btn-close-white" onclick="closeModal('performanceModal')"></button>
                    </div>
                    <div class="modal-body">
                        <!-- معلومات المخلص -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <strong>كود المخلص:</strong><br>
                                                <span class="badge bg-primary fs-6">${agent.agent_code}</span>
                                            </div>
                                            <div class="col-md-3">
                                                <strong>الشركة:</strong><br>
                                                ${agent.company_name || 'غير محدد'}
                                            </div>
                                            <div class="col-md-3">
                                                <strong>التخصص:</strong><br>
                                                <span class="badge bg-secondary">${agent.specialization}</span>
                                            </div>
                                            <div class="col-md-3">
                                                <strong>التقييم العام:</strong><br>
                                                <div class="d-flex align-items-center">
                                                    ${generateStars(agent.rating)}
                                                    <span class="ms-2 fw-bold">${agent.rating.toFixed(1)}/5.0</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- الإحصائيات الرئيسية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-info border-bottom pb-2">
                                    <i class="fas fa-chart-bar me-1"></i>
                                    الإحصائيات الرئيسية
                                </h6>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card text-center border-primary">
                                    <div class="card-body">
                                        <i class="fas fa-clipboard-list fa-2x text-primary mb-2"></i>
                                        <h3 class="text-primary mb-1">${stats.total_orders}</h3>
                                        <p class="card-text text-muted mb-0">إجمالي الأوامر</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card text-center border-success">
                                    <div class="card-body">
                                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                        <h3 class="text-success mb-1">${stats.completed_orders}</h3>
                                        <p class="card-text text-muted mb-0">أوامر مكتملة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card text-center border-warning">
                                    <div class="card-body">
                                        <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                        <h3 class="text-warning mb-1">${stats.in_progress_orders}</h3>
                                        <p class="card-text text-muted mb-0">قيد التنفيذ</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card text-center border-danger">
                                    <div class="card-body">
                                        <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                                        <h3 class="text-danger mb-1">${stats.cancelled_orders}</h3>
                                        <p class="card-text text-muted mb-0">أوامر ملغية</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- مؤشرات الأداء -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-info border-bottom pb-2">
                                    <i class="fas fa-tachometer-alt me-1"></i>
                                    مؤشرات الأداء
                                </h6>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">معدل الإنجاز</h6>
                                        <div class="progress mb-2" style="height: 20px;">
                                            <div class="progress-bar ${completionRate >= 90 ? 'bg-success' : completionRate >= 70 ? 'bg-warning' : 'bg-danger'}"
                                                 style="width: ${completionRate}%">
                                                ${completionRate.toFixed(1)}%
                                            </div>
                                        </div>
                                        <small class="text-muted">
                                            ${completionRate >= 90 ? 'ممتاز' : completionRate >= 70 ? 'جيد' : 'يحتاج تحسين'}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">معدل الإلغاء</h6>
                                        <div class="progress mb-2" style="height: 20px;">
                                            <div class="progress-bar ${cancelRate <= 5 ? 'bg-success' : cancelRate <= 15 ? 'bg-warning' : 'bg-danger'}"
                                                 style="width: ${cancelRate}%">
                                                ${cancelRate.toFixed(1)}%
                                            </div>
                                        </div>
                                        <small class="text-muted">
                                            ${cancelRate <= 5 ? 'ممتاز' : cancelRate <= 15 ? 'مقبول' : 'مرتفع'}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">متوسط أيام الإنجاز</h6>
                                        <h4 class="text-primary">${stats.avg_completion_days ? stats.avg_completion_days.toFixed(1) : 'غير محدد'}</h4>
                                        <small class="text-muted">يوم</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- الرسم البياني -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-info border-bottom pb-2">
                                    <i class="fas fa-chart-pie me-1"></i>
                                    توزيع الأوامر
                                </h6>
                                <div class="card">
                                    <div class="card-body">
                                        <canvas id="ordersChart" width="400" height="200"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقييم الأداء -->
                        <div class="row">
                            <div class="col-12">
                                <h6 class="text-info border-bottom pb-2">
                                    <i class="fas fa-star me-1"></i>
                                    تقييم الأداء العام
                                </h6>
                                <div class="card">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="performance-rating">
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <span>الكفاءة:</span>
                                                        <div class="rating-bar">
                                                            <div class="rating-fill" style="width: ${completionRate}%"></div>
                                                        </div>
                                                        <span class="fw-bold">${completionRate.toFixed(0)}%</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <span>الموثوقية:</span>
                                                        <div class="rating-bar">
                                                            <div class="rating-fill" style="width: ${100 - cancelRate}%"></div>
                                                        </div>
                                                        <span class="fw-bold">${(100 - cancelRate).toFixed(0)}%</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <span>التقييم العام:</span>
                                                        <div class="rating-stars">
                                                            ${generateStars(agent.rating)}
                                                        </div>
                                                        <span class="fw-bold">${agent.rating.toFixed(1)}/5.0</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="performance-summary">
                                                    <h6>ملخص الأداء:</h6>
                                                    <ul class="list-unstyled">
                                                        <li class="mb-1">
                                                            <i class="fas fa-circle text-${completionRate >= 90 ? 'success' : completionRate >= 70 ? 'warning' : 'danger'} me-2"></i>
                                                            معدل الإنجاز: ${completionRate >= 90 ? 'ممتاز' : completionRate >= 70 ? 'جيد' : 'يحتاج تحسين'}
                                                        </li>
                                                        <li class="mb-1">
                                                            <i class="fas fa-circle text-${cancelRate <= 5 ? 'success' : cancelRate <= 15 ? 'warning' : 'danger'} me-2"></i>
                                                            معدل الإلغاء: ${cancelRate <= 5 ? 'منخفض (ممتاز)' : cancelRate <= 15 ? 'متوسط' : 'مرتفع'}
                                                        </li>
                                                        <li class="mb-1">
                                                            <i class="fas fa-circle text-${agent.rating >= 4 ? 'success' : agent.rating >= 3 ? 'warning' : 'danger'} me-2"></i>
                                                            التقييم: ${agent.rating >= 4 ? 'ممتاز' : agent.rating >= 3 ? 'جيد' : 'يحتاج تحسين'}
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('performanceModal')">
                            <i class="fas fa-times me-1"></i>
                            إغلاق
                        </button>
                        <button type="button" class="btn btn-primary" onclick="printPerformanceReport('${agent.id}')">
                            <i class="fas fa-print me-1"></i>
                            طباعة التقرير
                        </button>
                        <button type="button" class="btn btn-success" onclick="exportPerformanceReport('${agent.id}')">
                            <i class="fas fa-file-excel me-1"></i>
                            تصدير Excel
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <style>
        .rating-bar {
            width: 100px;
            height: 8px;
            background-color: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        .rating-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
            transition: width 0.3s ease;
        }
        .performance-rating .d-flex {
            font-size: 0.9rem;
        }
        </style>
    `;

    // إزالة أي نافذة سابقة
    const existingModal = document.getElementById('performanceModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إضافة النافذة الجديدة
    document.body.insertAdjacentHTML('beforeend', modalContent);

    // فتح النافذة
    const modal = document.getElementById('performanceModal');
    modal.style.display = 'block';
    modal.classList.add('show');
    document.body.classList.add('modal-open');

    // إضافة backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop fade show';
    backdrop.id = 'performanceBackdrop';
    document.body.appendChild(backdrop);

    // رسم الرسم البياني
    setTimeout(() => {
        drawOrdersChart(stats);
    }, 100);
}

// وظيفة رسم الرسم البياني
function drawOrdersChart(stats) {
    const canvas = document.getElementById('ordersChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 20;

    // البيانات
    const data = [
        { label: 'مكتملة', value: stats.completed_orders, color: '#28a745' },
        { label: 'قيد التنفيذ', value: stats.in_progress_orders, color: '#ffc107' },
        { label: 'ملغية', value: stats.cancelled_orders, color: '#dc3545' }
    ];

    const total = data.reduce((sum, item) => sum + item.value, 0);

    if (total === 0) {
        // رسم دائرة فارغة
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        ctx.strokeStyle = '#dee2e6';
        ctx.lineWidth = 2;
        ctx.stroke();

        ctx.fillStyle = '#6c757d';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('لا توجد بيانات', centerX, centerY);
        return;
    }

    // رسم الرسم البياني الدائري
    let currentAngle = -Math.PI / 2;

    data.forEach((item, index) => {
        if (item.value > 0) {
            const sliceAngle = (item.value / total) * 2 * Math.PI;

            // رسم القطعة
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
            ctx.closePath();
            ctx.fillStyle = item.color;
            ctx.fill();

            // رسم الحدود
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 2;
            ctx.stroke();

            // إضافة النص
            const textAngle = currentAngle + sliceAngle / 2;
            const textX = centerX + Math.cos(textAngle) * (radius * 0.7);
            const textY = centerY + Math.sin(textAngle) * (radius * 0.7);

            ctx.fillStyle = '#fff';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(item.value.toString(), textX, textY);

            currentAngle += sliceAngle;
        }
    });

    // رسم المفتاح
    const legendY = canvas.height - 40;
    let legendX = 20;

    data.forEach((item) => {
        if (item.value > 0) {
            // مربع اللون
            ctx.fillStyle = item.color;
            ctx.fillRect(legendX, legendY, 15, 15);

            // النص
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(`${item.label}: ${item.value}`, legendX + 20, legendY + 12);

            legendX += 120;
        }
    });
}

// وظيفة طباعة التقرير
function printPerformanceReport(agentId) {
    console.log('🖨️ طباعة تقرير الأداء للمخلص:', agentId);

    // إخفاء الأزرار قبل الطباعة
    const modal = document.getElementById('performanceModal');
    const footer = modal.querySelector('.modal-footer');
    footer.style.display = 'none';

    // طباعة النافذة
    window.print();

    // إظهار الأزرار مرة أخرى
    setTimeout(() => {
        footer.style.display = 'flex';
    }, 1000);
}

// وظيفة تصدير التقرير
function exportPerformanceReport(agentId) {
    console.log('📊 تصدير تقرير الأداء للمخلص:', agentId);

    // إظهار رسالة
    showMessage('جاري تحضير ملف Excel...', 'info');

    // محاكاة التصدير (يمكن تطويرها لاحقاً)
    setTimeout(() => {
        showMessage('سيتم تطوير ميزة التصدير قريباً', 'warning');
    }, 2000);
}

console.log('✅ تم تحميل جميع وظائف إدارة المخلصين');
