#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام محاسبي مبسط مع Oracle Database
Simple Accounting System with Oracle Database
"""

import os
import logging
from flask import Flask, render_template_string, request, jsonify, redirect, url_for, flash
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
import oracledb
from werkzeug.security import generate_password_hash, check_password_hash

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# إعداد Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'oracle-accounting-system-2024'

# إعداد Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'

# إعدادات Oracle Database
ORACLE_CONFIG = {
    'host': 'localhost',
    'port': 1521,
    'service_name': 'ORCL',  # أو SID
    'user': 'accounting_user',
    'password': 'accounting_password'
}

class OracleManager:
    """مدير Oracle Database"""
    
    def __init__(self):
        self.connection = None
        
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            # استخدام oracledb للاتصال
            dsn = f"{ORACLE_CONFIG['host']}:{ORACLE_CONFIG['port']}/{ORACLE_CONFIG['service_name']}"
            self.connection = oracledb.connect(
                user=ORACLE_CONFIG['user'],
                password=ORACLE_CONFIG['password'],
                dsn=dsn
            )
            logger.info("✅ تم الاتصال بـ Oracle Database بنجاح")
            return True
        except Exception as e:
            logger.error(f"❌ فشل الاتصال بـ Oracle: {e}")
            return False
    
    def execute_query(self, sql, params=None):
        """تنفيذ استعلام"""
        if not self.connection:
            if not self.connect():
                return None
        
        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            
            if sql.strip().upper().startswith('SELECT'):
                result = cursor.fetchall()
                cursor.close()
                return result
            else:
                self.connection.commit()
                cursor.close()
                return True
                
        except Exception as e:
            logger.error(f"❌ خطأ في تنفيذ الاستعلام: {e}")
            return None

# إنشاء مدير Oracle
oracle_manager = OracleManager()

class User(UserMixin):
    """نموذج المستخدم"""
    
    def __init__(self, id, username, email, full_name, is_admin=False):
        self.id = str(id)
        self.username = username
        self.email = email
        self.full_name = full_name
        self.is_admin = is_admin
    
    def get_id(self):
        return self.id
    
    @staticmethod
    def get(user_id):
        """الحصول على مستخدم بالمعرف"""
        sql = "SELECT id, username, email, full_name, is_admin FROM users WHERE id = :1"
        result = oracle_manager.execute_query(sql, [user_id])
        if result:
            user_data = result[0]
            return User(
                id=user_data[0],
                username=user_data[1],
                email=user_data[2],
                full_name=user_data[3],
                is_admin=bool(user_data[4])
            )
        return None
    
    @staticmethod
    def authenticate(username, password):
        """التحقق من المستخدم"""
        sql = "SELECT id, username, email, full_name, password_hash, is_admin FROM users WHERE username = :1"
        result = oracle_manager.execute_query(sql, [username])
        if result:
            user_data = result[0]
            if check_password_hash(user_data[4], password):
                return User(
                    id=user_data[0],
                    username=user_data[1],
                    email=user_data[2],
                    full_name=user_data[3],
                    is_admin=bool(user_data[5])
                )
        return None

@login_manager.user_loader
def load_user(user_id):
    return User.get(user_id)

@app.route('/')
def home():
    """الصفحة الرئيسية"""
    return render_template_string("""
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>النظام المحاسبي المتقدم - Oracle Edition</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
            }
            .main-container {
                background: white;
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                margin: 20px;
                padding: 40px;
            }
            .header {
                text-align: center;
                margin-bottom: 40px;
            }
            .header h1 {
                color: #333;
                font-size: 2.5rem;
                margin-bottom: 10px;
            }
            .status-card {
                background: #d4edda;
                color: #155724;
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
                text-align: center;
            }
            .feature-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin: 30px 0;
            }
            .feature-card {
                background: #f8f9fa;
                padding: 25px;
                border-radius: 15px;
                border-right: 4px solid #667eea;
                transition: transform 0.3s ease;
            }
            .feature-card:hover {
                transform: translateY(-5px);
            }
            .feature-card h4 {
                color: #333;
                margin-bottom: 15px;
                display: flex;
                align-items: center;
                gap: 10px;
            }
            .feature-card p {
                color: #666;
                margin: 0;
            }
            .login-section {
                background: #e3f2fd;
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
                text-align: center;
            }
            .btn-custom {
                background: #667eea;
                color: white;
                border: none;
                padding: 12px 30px;
                border-radius: 25px;
                font-weight: bold;
                transition: all 0.3s ease;
            }
            .btn-custom:hover {
                background: #5a6fd8;
                transform: translateY(-2px);
            }
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <div class="main-container">
                <div class="header">
                    <h1><i class="fas fa-building"></i> النظام المحاسبي المتقدم</h1>
                    <h3>Oracle Database Edition</h3>
                </div>
                
                <div class="status-card">
                    <h4><i class="fas fa-check-circle"></i> النظام يعمل بنجاح مع Oracle Database!</h4>
                    <p>تم الاتصال بقاعدة البيانات Oracle بنجاح</p>
                </div>
                
                {% if current_user.is_authenticated %}
                <div class="alert alert-success text-center">
                    <h5>مرحباً {{ current_user.full_name }}!</h5>
                    <a href="{{ url_for('dashboard') }}" class="btn btn-custom me-2">
                        <i class="fas fa-tachometer-alt"></i> لوحة المعلومات
                    </a>
                    <a href="{{ url_for('logout') }}" class="btn btn-outline-danger">
                        <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                    </a>
                </div>
                {% else %}
                <div class="login-section">
                    <h5><i class="fas fa-key"></i> تسجيل الدخول</h5>
                    <p>للوصول إلى النظام الكامل</p>
                    <a href="{{ url_for('login') }}" class="btn btn-custom">
                        <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                    </a>
                </div>
                {% endif %}
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4><i class="fas fa-shopping-cart text-primary"></i> طلبات الشراء</h4>
                        <p>إدارة كاملة لطلبات الشراء مع نظام الموافقات المتعدد المستويات</p>
                    </div>
                    <div class="feature-card">
                        <h4><i class="fas fa-truck text-success"></i> أوامر الشراء</h4>
                        <p>تحويل الطلبات إلى أوامر وإدارة التنفيذ والتسليم</p>
                    </div>
                    <div class="feature-card">
                        <h4><i class="fas fa-boxes text-warning"></i> إدارة المخزون</h4>
                        <p>إدارة شاملة للأصناف وحركات المخزون مع التنبيهات الذكية</p>
                    </div>
                    <div class="feature-card">
                        <h4><i class="fas fa-users text-info"></i> إدارة الموردين</h4>
                        <p>قاعدة بيانات الموردين والتقييمات والعقود</p>
                    </div>
                    <div class="feature-card">
                        <h4><i class="fas fa-chart-line text-danger"></i> التقارير والتحليل</h4>
                        <p>تقارير شاملة مع رسوم بيانية تفاعلية ومؤشرات الأداء</p>
                    </div>
                    <div class="feature-card">
                        <h4><i class="fas fa-database text-secondary"></i> Oracle Database</h4>
                        <p>قاعدة بيانات Oracle عالية الأداء والموثوقية</p>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <p class="text-muted">
                        <i class="fas fa-code"></i> 
                        تم تطوير النظام باستخدام Flask + Oracle Database
                    </p>
                </div>
            </div>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """)

@app.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح!', 'success')
    return redirect(url_for('home'))

@app.route('/dashboard')
@login_required
def dashboard():
    """لوحة المعلومات"""
    return render_template_string("""
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>لوحة المعلومات - النظام المحاسبي</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { background: #f8f9fa; }
            .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
            .card { border: none; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
            .stat-card { text-align: center; padding: 30px; }
            .stat-number { font-size: 2.5rem; font-weight: bold; }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-dark">
            <div class="container">
                <span class="navbar-brand">
                    <i class="fas fa-tachometer-alt"></i> لوحة المعلومات
                </span>
                <div>
                    <span class="text-white me-3">مرحباً {{ current_user.full_name }}</span>
                    <a href="{{ url_for('logout') }}" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-sign-out-alt"></i> خروج
                    </a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="row">
                <div class="col-md-3 mb-4">
                    <div class="card stat-card text-primary">
                        <div class="stat-number">{{ stats.users or 0 }}</div>
                        <div>المستخدمين</div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card stat-card text-success">
                        <div class="stat-number">{{ stats.suppliers or 0 }}</div>
                        <div>الموردين</div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card stat-card text-warning">
                        <div class="stat-number">{{ stats.items or 0 }}</div>
                        <div>الأصناف</div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card stat-card text-info">
                        <div class="stat-number">{{ stats.requests or 0 }}</div>
                        <div>طلبات الشراء</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-line"></i> الإحصائيات السريعة</h5>
                        </div>
                        <div class="card-body">
                            <p><strong>حالة قاعدة البيانات:</strong> <span class="text-success">متصلة</span></p>
                            <p><strong>نوع قاعدة البيانات:</strong> Oracle Database</p>
                            <p><strong>آخر تحديث:</strong> {{ current_time }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-cogs"></i> الإجراءات السريعة</h5>
                        </div>
                        <div class="card-body">
                            <a href="{{ url_for('test_oracle') }}" class="btn btn-primary mb-2 w-100">
                                <i class="fas fa-database"></i> اختبار Oracle
                            </a>
                            <a href="{{ url_for('home') }}" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-home"></i> الصفحة الرئيسية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """, stats=get_dashboard_stats(), current_time=get_current_time())

@app.route('/test-oracle')
@login_required
def test_oracle():
    """اختبار Oracle Database"""
    try:
        # اختبار الاتصال
        result = oracle_manager.execute_query("SELECT SYSDATE FROM DUAL")
        if result:
            db_time = result[0][0]

            # اختبار الجداول
            tables_info = []
            test_tables = ['users', 'suppliers', 'items', 'purchase_requests']

            for table in test_tables:
                try:
                    count_result = oracle_manager.execute_query(f"SELECT COUNT(*) FROM {table}")
                    count = count_result[0][0] if count_result else 0
                    tables_info.append({'name': table, 'count': count, 'status': 'success'})
                except Exception as e:
                    tables_info.append({'name': table, 'count': 0, 'status': 'error', 'error': str(e)})

            return jsonify({
                'status': 'success',
                'message': 'Oracle Database يعمل بشكل صحيح',
                'db_time': str(db_time),
                'tables': tables_info
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'فشل في تنفيذ الاستعلام'
            })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في الاتصال: {str(e)}'
        })

def get_dashboard_stats():
    """الحصول على إحصائيات لوحة المعلومات"""
    stats = {}
    try:
        # عدد المستخدمين
        result = oracle_manager.execute_query("SELECT COUNT(*) FROM users")
        stats['users'] = result[0][0] if result else 0

        # عدد الموردين
        result = oracle_manager.execute_query("SELECT COUNT(*) FROM suppliers")
        stats['suppliers'] = result[0][0] if result else 0

        # عدد الأصناف
        result = oracle_manager.execute_query("SELECT COUNT(*) FROM items")
        stats['items'] = result[0][0] if result else 0

        # عدد طلبات الشراء
        result = oracle_manager.execute_query("SELECT COUNT(*) FROM purchase_requests")
        stats['requests'] = result[0][0] if result else 0

    except Exception as e:
        logger.error(f"خطأ في الحصول على الإحصائيات: {e}")
        stats = {'users': 0, 'suppliers': 0, 'items': 0, 'requests': 0}

    return stats

def get_current_time():
    """الحصول على الوقت الحالي"""
    from datetime import datetime
    return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

if __name__ == '__main__':
    print("=" * 70)
    print("🏢 النظام المحاسبي المبسط - Oracle Edition")
    print("Simple Accounting System - Oracle Edition")
    print("=" * 70)

    # اختبار الاتصال بـ Oracle
    if oracle_manager.connect():
        print("✅ تم الاتصال بـ Oracle Database بنجاح")

        # إنشاء مستخدم تجريبي إذا لم يكن موجوداً
        try:
            # التحقق من وجود جدول المستخدمين
            result = oracle_manager.execute_query("SELECT COUNT(*) FROM users WHERE username = 'admin'")
            if result and result[0][0] == 0:
                # إنشاء مستخدم admin
                password_hash = generate_password_hash('admin')
                oracle_manager.execute_query("""
                    INSERT INTO users (username, email, full_name, password_hash, is_admin)
                    VALUES ('admin', '<EMAIL>', 'مدير النظام', :1, 1)
                """, [password_hash])
                print("✅ تم إنشاء مستخدم admin تجريبي")
        except Exception as e:
            print(f"⚠️ تحذير: {e}")

        print("\n🌐 التطبيق متاح على:")
        print("   - http://127.0.0.1:5000")
        print("   - http://localhost:5000")
        print("\n👤 بيانات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin")
        print("\n" + "=" * 70)

        app.run(host='0.0.0.0', port=5000, debug=True)
    else:
        print("❌ فشل في الاتصال بـ Oracle Database")
        print("يرجى التحقق من:")
        print("1. تشغيل خدمة Oracle")
        print("2. صحة بيانات الاتصال في ORACLE_CONFIG")
        print("3. وجود قاعدة البيانات والمستخدم")

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.authenticate(username, password)
        if user:
            login_user(user)
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template_string("""
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تسجيل الدخول - النظام المحاسبي</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .login-card {
                background: white;
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                padding: 40px;
                max-width: 400px;
                width: 100%;
            }
        </style>
    </head>
    <body>
        <div class="login-card">
            <div class="text-center mb-4">
                <h2><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</h2>
                <p class="text-muted">النظام المحاسبي المتقدم</p>
            </div>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">اسم المستخدم</label>
                    <input type="text" class="form-control" name="username" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">كلمة المرور</label>
                    <input type="password" class="form-control" name="password" required>
                </div>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-sign-in-alt"></i> دخول
                </button>
            </form>
            
            <div class="text-center mt-3">
                <small class="text-muted">
                    بيانات تجريبية: admin / admin
                </small>
            </div>
            
            <div class="text-center mt-3">
                <a href="{{ url_for('home') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للرئيسية
                </a>
            </div>
        </div>
    </body>
    </html>
    """)
