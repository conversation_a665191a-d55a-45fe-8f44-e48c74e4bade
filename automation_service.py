#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خدمة الأتمتة المستقلة
Independent Automation Service

هذه الخدمة تعمل بشكل مستقل عن التطبيق الرئيسي
وتضمن معالجة طابور الأتمتة حتى عند إيقاف الخادم
"""

import time
import sys
import os
import logging
from datetime import datetime
import signal
import threading

# إضافة مسار التطبيق
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app'))

from database_manager import DatabaseManager

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('automation_service.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class AutomationService:
    """خدمة الأتمتة المستقلة"""
    
    def __init__(self):
        self.running = False
        self.check_interval = 30  # فحص كل 30 ثانية
        self.max_interval = 300   # أقصى فترة: 5 دقائق
        self.min_interval = 10    # أقل فترة: 10 ثوان
        self.consecutive_empty_checks = 0
        
    def start(self):
        """بدء الخدمة"""
        self.running = True
        logger.info("🚀 بدء خدمة الأتمتة المستقلة")
        
        # إعداد معالج الإشارات للإيقاف الآمن
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # بدء حلقة المعالجة
        self._process_loop()
    
    def stop(self):
        """إيقاف الخدمة"""
        self.running = False
        logger.info("🛑 إيقاف خدمة الأتمتة المستقلة")
    
    def _signal_handler(self, signum, frame):
        """معالج الإشارات للإيقاف الآمن"""
        logger.info(f"📡 تم استلام إشارة {signum} - إيقاف الخدمة...")
        self.stop()
    
    def _process_loop(self):
        """حلقة معالجة الطابور"""
        while self.running:
            try:
                processed_count = self._process_queue()
                
                # تكييف الفترة الزمنية
                if processed_count > 0:
                    self.consecutive_empty_checks = 0
                    self.check_interval = max(self.min_interval, self.check_interval - 5)
                    logger.info(f"⚡ تم تسريع المعالجة: فحص كل {self.check_interval} ثانية")
                else:
                    self.consecutive_empty_checks += 1
                    if self.consecutive_empty_checks >= 3:
                        self.check_interval = min(self.max_interval, self.check_interval + 30)
                        logger.info(f"💤 تم إبطاء المعالجة: فحص كل {self.check_interval} ثانية")
                
                # انتظار قبل الفحص التالي
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"❌ خطأ في حلقة المعالجة: {e}")
                time.sleep(60)  # انتظار دقيقة عند الخطأ
    
    def _process_queue(self):
        """معالجة طابور الأتمتة"""
        processed_count = 0
        db_manager = None
        
        try:
            db_manager = DatabaseManager()
            
            # فحص سريع لوجود عمليات معلقة
            count_query = """
                SELECT COUNT(*) FROM automation_queue
                WHERE processed = 0 AND processing_attempts < 3
            """
            
            count_result = db_manager.execute_query(count_query)
            
            if not count_result or count_result[0][0] == 0:
                return 0  # لا توجد عمليات للمعالجة
            
            # جلب العمليات المعلقة
            queue_query = """
                SELECT id, shipment_id, old_status, new_status, created_at
                FROM automation_queue
                WHERE processed = 0
                AND processing_attempts < 3
                ORDER BY created_at ASC
                FETCH FIRST 10 ROWS ONLY
            """
            
            queue_items = db_manager.execute_query(queue_query)
            
            if not queue_items:
                return 0
            
            logger.info(f"🔄 معالجة {len(queue_items)} عملية من طابور الأتمتة")
            
            # معالجة كل عملية
            for item in queue_items:
                queue_id, shipment_id, old_status, new_status, created_at = item
                
                try:
                    # تحديث محاولة المعالجة
                    update_attempt_query = """
                        UPDATE automation_queue
                        SET processing_attempts = processing_attempts + 1
                        WHERE id = :queue_id
                    """
                    
                    db_manager.execute_update(update_attempt_query, {'queue_id': queue_id})
                    
                    # معالجة العملية
                    success = self._process_shipment_change(db_manager, shipment_id, old_status, new_status)
                    
                    if success:
                        # تحديث حالة المعالجة
                        mark_processed_query = """
                            UPDATE automation_queue 
                            SET processed = 1, processed_at = CURRENT_TIMESTAMP
                            WHERE id = :queue_id
                        """
                        
                        db_manager.execute_update(mark_processed_query, {'queue_id': queue_id})
                        processed_count += 1
                        logger.info(f"✅ تم معالجة العملية {queue_id} للشحنة {shipment_id}")
                    else:
                        logger.warning(f"⚠️ فشل في معالجة العملية {queue_id} للشحنة {shipment_id}")
                
                except Exception as e:
                    logger.error(f"❌ خطأ في معالجة العملية {queue_id}: {e}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة الطابور: {e}")
        
        finally:
            if db_manager:
                db_manager.close()
        
        return processed_count
    
    def _process_shipment_change(self, db_manager, shipment_id, old_status, new_status):
        """معالجة تغيير حالة الشحنة"""
        try:
            # تعيين متغيرات Green API
            os.environ['GREEN_API_INSTANCE_ID'] = '7105306929'
            os.environ['GREEN_API_TOKEN'] = '0260de395caa4adfb2aa02d53511f9ffa19b29b41f094fd68c'
            os.environ['GREEN_API_URL'] = 'https://7105.api.greenapi.com'
            os.environ['GREEN_API_TEST_MODE'] = 'false'

            # استيراد محرك الأتمتة
            sys.path.append('app/shipments')
            from automation_engine import AutomationEngine

            # إنشاء instance وتنفيذ الأتمتة
            engine = AutomationEngine()
            engine.process_shipment_status_change(shipment_id, old_status, new_status)
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة الشحنة {shipment_id}: {e}")
            return False

def main():
    """الدالة الرئيسية"""
    print("🤖 خدمة الأتمتة المستقلة")
    print("=" * 50)
    print("هذه الخدمة تعمل بشكل مستقل عن التطبيق الرئيسي")
    print("وتضمن معالجة طابور الأتمتة حتى عند إيقاف الخادم")
    print("=" * 50)
    
    service = AutomationService()
    
    try:
        service.start()
    except KeyboardInterrupt:
        logger.info("📡 تم استلام إشارة الإيقاف من المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في الخدمة: {e}")
    finally:
        service.stop()
        logger.info("👋 تم إيقاف خدمة الأتمتة المستقلة")

if __name__ == "__main__":
    main()
