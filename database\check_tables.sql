-- فحص حالة الجداول والـ triggers

-- 1. فحص جدول transfer_execution_suppliers
SELECT 'transfer_execution_suppliers table' as check_type, 
       CASE WHEN COUNT(*) > 0 THEN 'EXISTS' ELSE 'NOT EXISTS' END as status
FROM user_tables 
WHERE table_name = 'TRANSFER_EXECUTION_SUPPLIERS';

-- 2. فحص الـ triggers
SELECT 'Triggers' as check_type, trigger_name, status
FROM user_triggers 
WHERE trigger_name IN ('TRG_EXEC_SUPPLIERS', 'TRG_UPDATE_SUPPLIERS_COUNT');

-- 3. فحص الـ sequences
SELECT 'Sequences' as check_type, sequence_name
FROM user_sequences 
WHERE sequence_name LIKE '%EXEC%SUPP%';

-- 4. فحص أعمدة جدول transfers
SELECT 'transfers columns' as check_type, column_name, data_type
FROM user_tab_columns 
WHERE table_name = 'TRANSFERS' 
AND column_name IN ('EXECUTION_REFERENCE', 'EXECUTION_METHOD', 'TOTAL_SUPPLIERS', 'UPDATED_EXECUTION_AT');

-- 5. فحص بيانات الموردين
SELECT 'money_changers_banks data' as check_type, COUNT(*) as count
FROM money_changers_banks 
WHERE is_active = 1;
