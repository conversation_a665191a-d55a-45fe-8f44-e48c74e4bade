/**
 * نظام البحث الذكي للمستفيدين
 * Smart Beneficiary Search System
 */

class SmartBeneficiarySearch {
    constructor(inputElement, options = {}) {
        this.input = inputElement;
        this.options = {
            minChars: 2,
            maxResults: 10,
            debounceDelay: 300,
            placeholder: 'ابحث عن مستفيد (الاسم، البنك، رقم الحساب...)',
            noResultsText: 'لا توجد نتائج',
            loadingText: 'جاري البحث...',
            ...options
        };
        
        this.selectedBeneficiary = null;
        this.isOpen = false;
        this.searchTimeout = null;
        this.currentRequest = null;
        this.currentResults = null;
        
        this.init();
    }
    
    init() {
        this.createElements();
        this.bindEvents();
        this.setupStyling();
    }
    
    createElements() {
        // إنشاء container للبحث
        this.container = document.createElement('div');
        this.container.className = 'smart-search-container';
        this.container.style.position = 'relative';
        
        // تحديث input
        this.input.placeholder = this.options.placeholder;
        this.input.autocomplete = 'off';
        this.input.className += ' smart-search-input';
        
        // إنشاء قائمة النتائج
        this.dropdown = document.createElement('div');
        this.dropdown.className = 'smart-search-dropdown';
        this.dropdown.style.display = 'none';
        
        // إنشاء أيقونة البحث
        this.searchIcon = document.createElement('i');
        this.searchIcon.className = 'fas fa-search smart-search-icon';
        
        // إنشاء أيقونة المسح
        this.clearIcon = document.createElement('i');
        this.clearIcon.className = 'fas fa-times smart-search-clear';
        this.clearIcon.style.display = 'none';
        this.clearIcon.title = 'مسح';
        
        // تجميع العناصر
        this.input.parentNode.insertBefore(this.container, this.input);
        this.container.appendChild(this.input);
        this.container.appendChild(this.searchIcon);
        this.container.appendChild(this.clearIcon);
        this.container.appendChild(this.dropdown);
    }
    
    bindEvents() {
        // البحث عند الكتابة
        this.input.addEventListener('input', (e) => {
            this.handleInput(e.target.value);
        });
        
        // إخفاء القائمة عند فقدان التركيز
        this.input.addEventListener('blur', (e) => {
            // تأخير أطول لإتاحة النقر على النتائج
            setTimeout(() => {
                if (!this.container.contains(document.activeElement)) {
                    console.log('👋 إخفاء القائمة بسبب فقدان التركيز');
                    this.hideDropdown();
                }
            }, 300);
        });
        
        // إظهار القائمة عند التركيز إذا كان هناك نص
        this.input.addEventListener('focus', () => {
            if (this.input.value.length >= this.options.minChars) {
                this.showDropdown();
            }
        });
        
        // مسح البحث
        this.clearIcon.addEventListener('click', () => {
            this.clearSearch();
        });
        
        // التنقل بالكيبورد
        this.input.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });
        
        // إخفاء القائمة عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target)) {
                this.hideDropdown();
            }
        });
    }
    
    setupStyling() {
        // إضافة CSS للبحث الذكي
        if (!document.getElementById('smart-search-styles')) {
            const style = document.createElement('style');
            style.id = 'smart-search-styles';
            style.textContent = `
                .smart-search-container {
                    position: relative;
                    display: inline-block;
                    width: 100%;
                }
                
                .smart-search-input {
                    padding-right: 35px !important;
                    padding-left: 35px !important;
                }
                
                .smart-search-icon {
                    position: absolute;
                    right: 10px;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #6c757d;
                    pointer-events: none;
                    z-index: 2;
                }
                
                .smart-search-clear {
                    position: absolute;
                    left: 10px;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #6c757d;
                    cursor: pointer;
                    z-index: 2;
                    padding: 2px;
                    border-radius: 50%;
                    transition: all 0.2s;
                }
                
                .smart-search-clear:hover {
                    background-color: #f8f9fa;
                    color: #dc3545;
                }
                
                .smart-search-dropdown {
                    position: absolute;
                    top: 100%;
                    left: 0;
                    right: 0;
                    background: white;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                    max-height: 300px;
                    overflow-y: auto;
                    z-index: 1000;
                    margin-top: 2px;
                }
                
                .search-result-item {
                    padding: 12px 15px;
                    cursor: pointer;
                    border-bottom: 1px solid #f0f0f0;
                    transition: background-color 0.2s;
                    font-size: 14px;
                    line-height: 1.4;
                }
                
                .search-result-item:hover,
                .search-result-item.highlighted {
                    background-color: #f8f9fa;
                }
                
                .search-result-item:last-child {
                    border-bottom: none;
                }
                
                .search-result-name {
                    font-weight: 600;
                    color: #2c3e50;
                    margin-bottom: 4px;
                }
                
                .search-result-details {
                    color: #6c757d;
                    font-size: 12px;
                }
                
                .search-loading,
                .search-no-results {
                    padding: 15px;
                    text-align: center;
                    color: #6c757d;
                    font-style: italic;
                }
                
                .search-loading {
                    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12a9 9 0 11-6.219-8.56"/></svg>') no-repeat center left;
                    background-size: 16px;
                    padding-left: 25px;
                    animation: spin 1s linear infinite;
                }
                
                @keyframes spin {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    handleInput(value) {
        // إظهار/إخفاء أيقونة المسح
        this.clearIcon.style.display = value ? 'block' : 'none';
        
        // إلغاء البحث السابق
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        
        // إلغاء الطلب السابق
        if (this.currentRequest) {
            this.currentRequest.abort();
        }
        
        if (value.length < this.options.minChars) {
            this.hideDropdown();
            this.selectedBeneficiary = null;
            return;
        }
        
        // تأخير البحث لتحسين الأداء
        this.searchTimeout = setTimeout(() => {
            this.performSearch(value);
        }, this.options.debounceDelay);
    }
    
    async performSearch(query) {
        try {
            this.showLoading();
            
            // إنشاء AbortController للتحكم في الطلب
            const controller = new AbortController();
            this.currentRequest = controller;
            
            const response = await fetch(`/transfers/api/beneficiaries/smart-search?q=${encodeURIComponent(query)}&limit=${this.options.maxResults}`, {
                signal: controller.signal
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const results = await response.json();
            this.displayResults(results);
            
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('خطأ في البحث:', error);
                this.showError('حدث خطأ في البحث');
            }
        } finally {
            this.currentRequest = null;
        }
    }
    
    showLoading() {
        this.dropdown.innerHTML = `<div class="search-loading">${this.options.loadingText}</div>`;
        this.showDropdown();
    }
    
    displayResults(results) {
        // حفظ النتائج الحالية
        this.currentResults = results;

        if (!results || results.length === 0) {
            this.dropdown.innerHTML = `<div class="search-no-results">${this.options.noResultsText}</div>`;
            this.showDropdown();
            return;
        }

        console.log(`📋 عرض ${results.length} نتيجة`);

        const html = results.map((item, index) => `
            <div class="search-result-item" data-index="${index}" data-id="${item.id}">
                <div class="search-result-name">${this.highlightMatch(item.beneficiary_name, this.input.value)}</div>
                <div class="search-result-details">${this.highlightMatch(item.text, this.input.value)}</div>
            </div>
        `).join('');

        this.dropdown.innerHTML = html;
        this.bindResultEvents(results);
        this.showDropdown();
    }
    
    bindResultEvents(results) {
        const items = this.dropdown.querySelectorAll('.search-result-item');
        console.log(`🔗 ربط الأحداث لـ ${items.length} نتيجة`);

        items.forEach((item, index) => {
            // إضافة معرف للعنصر
            item.setAttribute('data-index', index);

            // حدث النقر
            item.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log(`🖱️ تم النقر على النتيجة ${index}:`, results[index]);
                this.selectBeneficiary(results[index]);
            });

            // حدث mousedown للتأكد
            item.addEventListener('mousedown', (e) => {
                e.preventDefault();
                console.log(`🖱️ mousedown على النتيجة ${index}`);
                this.selectBeneficiary(results[index]);
            });

            // حدث hover
            item.addEventListener('mouseenter', () => {
                this.highlightItem(index);
            });
        });
    }
    
    selectBeneficiary(beneficiary) {
        console.log('🎯 تم اختيار مستفيد:', beneficiary);

        this.selectedBeneficiary = beneficiary;
        this.input.value = beneficiary.beneficiary_name;
        this.hideDropdown();

        // إطلاق حدث التحديد
        const event = new CustomEvent('beneficiarySelected', {
            detail: beneficiary
        });

        console.log('📤 إطلاق حدث beneficiarySelected');
        this.input.dispatchEvent(event);

        // إخفاء أيقونة المسح وإظهار أيقونة النجاح
        this.clearIcon.style.display = 'block';
        this.searchIcon.className = 'fas fa-check smart-search-icon';
        this.searchIcon.style.color = '#28a745';

        console.log('✅ تم إكمال اختيار المستفيد');
    }
    
    highlightMatch(text, query) {
        if (!text || !query) return text || '';
        
        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<strong>$1</strong>');
    }
    
    highlightItem(index) {
        const items = this.dropdown.querySelectorAll('.search-result-item');
        items.forEach((item, i) => {
            item.classList.toggle('highlighted', i === index);
        });
    }
    
    handleKeydown(e) {
        if (!this.isOpen) return;

        const items = this.dropdown.querySelectorAll('.search-result-item');
        const highlighted = this.dropdown.querySelector('.highlighted');
        let currentIndex = highlighted ? Array.from(items).indexOf(highlighted) : -1;

        console.log(`⌨️ مفتاح: ${e.key}, العنصر المحدد: ${currentIndex}`);

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                currentIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
                this.highlightItem(currentIndex);
                console.log(`⬇️ تحديد العنصر: ${currentIndex}`);
                break;

            case 'ArrowUp':
                e.preventDefault();
                currentIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
                this.highlightItem(currentIndex);
                console.log(`⬆️ تحديد العنصر: ${currentIndex}`);
                break;

            case 'Enter':
                e.preventDefault();
                if (highlighted) {
                    const selectedIndex = parseInt(highlighted.getAttribute('data-index'));
                    console.log(`✅ Enter - اختيار العنصر: ${selectedIndex}`);

                    // الحصول على البيانات من العنصر المحدد
                    if (this.currentResults && this.currentResults[selectedIndex]) {
                        this.selectBeneficiary(this.currentResults[selectedIndex]);
                    }
                } else if (items.length > 0) {
                    // إذا لم يكن هناك عنصر محدد، اختر الأول
                    console.log(`✅ Enter - اختيار العنصر الأول`);
                    if (this.currentResults && this.currentResults[0]) {
                        this.selectBeneficiary(this.currentResults[0]);
                    }
                }
                break;

            case 'Escape':
                this.hideDropdown();
                break;
        }
    }
    
    showDropdown() {
        this.dropdown.style.display = 'block';
        this.isOpen = true;
    }
    
    hideDropdown() {
        this.dropdown.style.display = 'none';
        this.isOpen = false;
    }
    
    showError(message) {
        this.dropdown.innerHTML = `<div class="search-no-results" style="color: #dc3545;">${message}</div>`;
        this.showDropdown();
    }
    
    clearSearch() {
        this.input.value = '';
        this.selectedBeneficiary = null;
        this.hideDropdown();
        this.clearIcon.style.display = 'none';
        this.searchIcon.className = 'fas fa-search smart-search-icon';
        this.searchIcon.style.color = '#6c757d';
        
        // إطلاق حدث المسح
        const event = new CustomEvent('beneficiaryCleared');
        this.input.dispatchEvent(event);
        
        this.input.focus();
    }
    
    getSelectedBeneficiary() {
        return this.selectedBeneficiary;
    }
    
    setBeneficiary(beneficiary) {
        if (beneficiary) {
            this.selectBeneficiary(beneficiary);
        } else {
            this.clearSearch();
        }
    }
}

// تصدير للاستخدام العام
window.SmartBeneficiarySearch = SmartBeneficiarySearch;
