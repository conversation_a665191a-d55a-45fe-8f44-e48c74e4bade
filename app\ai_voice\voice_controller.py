"""
نظام التحكم الصوتي الذكي لعقود الشراء
AI Voice Controller for Purchase Contracts
"""

import speech_recognition as sr
import pyttsx3
import re
import json
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Tuple
import arabic_reshaper
from bidi.algorithm import get_display

logger = logging.getLogger(__name__)

class ArabicVoiceController:
    """نظام التحكم الصوتي العربي الذكي"""
    
    def __init__(self):
        """تهيئة نظام التحكم الصوتي"""
        try:
            self.recognizer = sr.Recognizer()
            # تأخير تهيئة الميكروفون حتى الحاجة إليه
            self.microphone = None
            self.tts_engine = pyttsx3.init()

            # إعداد محرك النطق العربي
            self.setup_arabic_tts()

        except Exception as e:
            logger.error(f"خطأ في تهيئة نظام التحكم الصوتي: {str(e)}")
            raise
        
        # قاموس الحقول والكلمات المفتاحية
        self.field_keywords = {
            'contract_number': ['رقم العقد', 'رقم', 'عقد رقم', 'العقد رقم'],
            'supplier_name': ['اسم المورد', 'المورد', 'اسم الشركة', 'الشركة'],
            'contract_date': ['تاريخ العقد', 'التاريخ', 'بتاريخ', 'في تاريخ'],
            'delivery_date': ['تاريخ التسليم', 'موعد التسليم', 'التسليم', 'تسليم في'],
            'total_amount': ['المبلغ الإجمالي', 'المبلغ', 'القيمة', 'إجمالي', 'المجموع'],
            'currency': ['العملة', 'بالريال', 'بالدولار', 'ريال', 'دولار'],
            'payment_terms': ['شروط الدفع', 'الدفع', 'طريقة الدفع', 'شروط السداد'],
            'description': ['الوصف', 'تفاصيل', 'البنود', 'المواد', 'الأصناف'],
            'notes': ['ملاحظات', 'تعليقات', 'ملحوظة', 'إضافة']
        }
        
        # أنماط التعرف على البيانات
        self.patterns = {
            'number': r'\d+',
            'amount': r'[\d,]+\.?\d*',
            'date': r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}',
            'currency_sar': r'ريال|ر\.س|SAR',
            'currency_usd': r'دولار|USD|\$'
        }
        
        # حالة المحادثة
        self.conversation_state = {
            'current_field': None,
            'extracted_data': {},
            'confirmation_pending': False,
            'listening': False
        }
    
    def setup_arabic_tts(self):
        """إعداد محرك النطق العربي"""
        try:
            # إعداد الصوت العربي
            voices = self.tts_engine.getProperty('voices')
            for voice in voices:
                if 'arabic' in voice.name.lower() or 'ar' in voice.id.lower():
                    self.tts_engine.setProperty('voice', voice.id)
                    break
            
            # إعداد سرعة النطق
            self.tts_engine.setProperty('rate', 150)
            self.tts_engine.setProperty('volume', 0.8)
            
        except Exception as e:
            logger.warning(f"تعذر إعداد النطق العربي: {str(e)}")
    
    def speak_arabic(self, text: str):
        """نطق النص العربي"""
        try:
            # تنسيق النص العربي للعرض الصحيح
            reshaped_text = arabic_reshaper.reshape(text)
            display_text = get_display(reshaped_text)
            
            self.tts_engine.say(display_text)
            self.tts_engine.runAndWait()
            
        except Exception as e:
            logger.error(f"خطأ في النطق: {str(e)}")
            print(f"🔊 {text}")  # عرض النص بدلاً من النطق
    
    def listen_for_speech(self, timeout: int = 5) -> Optional[str]:
        """الاستماع للكلام وتحويله إلى نص"""
        try:
            # تهيئة الميكروفون عند الحاجة
            if self.microphone is None:
                self.microphone = sr.Microphone()

            with self.microphone as source:
                # تقليل الضوضاء
                self.recognizer.adjust_for_ambient_noise(source, duration=1)

            self.speak_arabic("أستمع إليك الآن...")

            with self.microphone as source:
                # الاستماع للصوت
                audio = self.recognizer.listen(source, timeout=timeout, phrase_time_limit=10)

            # تحويل الصوت إلى نص
            text = self.recognizer.recognize_google(audio, language='ar-SA')
            logger.info(f"تم التعرف على: {text}")
            return text
            
        except sr.WaitTimeoutError:
            self.speak_arabic("لم أسمع شيئاً، يرجى المحاولة مرة أخرى")
            return None
        except sr.UnknownValueError:
            self.speak_arabic("لم أتمكن من فهم ما قلته، يرجى الإعادة")
            return None
        except sr.RequestError as e:
            logger.error(f"خطأ في خدمة التعرف على الصوت: {str(e)}")
            self.speak_arabic("حدث خطأ في خدمة التعرف على الصوت")
            return None
    
    def identify_field_from_speech(self, text: str) -> Optional[str]:
        """تحديد الحقل المطلوب من النص المنطوق"""
        text_lower = text.lower()
        
        for field, keywords in self.field_keywords.items():
            for keyword in keywords:
                if keyword in text_lower:
                    return field
        
        return None
    
    def extract_data_from_speech(self, text: str, field: str) -> Optional[str]:
        """استخراج البيانات من النص حسب نوع الحقل"""
        
        if field == 'contract_number':
            # البحث عن رقم العقد
            numbers = re.findall(self.patterns['number'], text)
            return numbers[0] if numbers else None
        
        elif field in ['total_amount']:
            # البحث عن المبلغ
            amounts = re.findall(self.patterns['amount'], text)
            if amounts:
                # تنظيف المبلغ من الفواصل
                return amounts[0].replace(',', '')
            return None
        
        elif field in ['contract_date', 'delivery_date']:
            # البحث عن التاريخ
            dates = re.findall(self.patterns['date'], text)
            if dates:
                return self.parse_arabic_date(dates[0])
            
            # محاولة فهم التاريخ بالكلمات
            return self.parse_spoken_date(text)
        
        elif field == 'currency':
            # تحديد العملة
            if re.search(self.patterns['currency_sar'], text):
                return 'SAR'
            elif re.search(self.patterns['currency_usd'], text):
                return 'USD'
            return 'SAR'  # افتراضي
        
        elif field in ['supplier_name', 'description', 'notes', 'payment_terms']:
            # استخراج النص الحر
            return self.extract_free_text(text, field)
        
        return None
    
    def parse_spoken_date(self, text: str) -> Optional[str]:
        """تحليل التاريخ المنطوق بالعربية"""
        today = datetime.now()
        
        # كلمات مفتاحية للتواريخ
        if 'اليوم' in text:
            return today.strftime('%Y-%m-%d')
        elif 'غداً' in text or 'غدا' in text:
            return (today + timedelta(days=1)).strftime('%Y-%m-%d')
        elif 'بعد غد' in text:
            return (today + timedelta(days=2)).strftime('%Y-%m-%d')
        elif 'الأسبوع القادم' in text:
            return (today + timedelta(weeks=1)).strftime('%Y-%m-%d')
        elif 'الشهر القادم' in text:
            next_month = today.replace(month=today.month + 1) if today.month < 12 else today.replace(year=today.year + 1, month=1)
            return next_month.strftime('%Y-%m-%d')
        
        # محاولة استخراج أرقام للتاريخ
        numbers = re.findall(r'\d+', text)
        if len(numbers) >= 3:
            try:
                day, month, year = numbers[:3]
                if len(year) == 2:
                    year = '20' + year
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            except:
                pass
        
        return None
    
    def parse_arabic_date(self, date_str: str) -> str:
        """تحليل التاريخ من النص"""
        try:
            # تجربة تنسيقات مختلفة
            for fmt in ['%d/%m/%Y', '%d-%m-%Y', '%d/%m/%y', '%d-%m-%y']:
                try:
                    date_obj = datetime.strptime(date_str, fmt)
                    return date_obj.strftime('%Y-%m-%d')
                except ValueError:
                    continue
            return date_str
        except:
            return date_str
    
    def extract_free_text(self, text: str, field: str) -> str:
        """استخراج النص الحر بعد إزالة الكلمات المفتاحية"""
        # إزالة الكلمات المفتاحية للحقل
        keywords = self.field_keywords.get(field, [])
        cleaned_text = text
        
        for keyword in keywords:
            cleaned_text = cleaned_text.replace(keyword, '').strip()
        
        # إزالة كلمات الربط الشائعة
        stop_words = ['هو', 'هي', 'في', 'من', 'إلى', 'على', 'عن', 'مع', 'بـ', 'لـ', 'كـ']
        words = cleaned_text.split()
        filtered_words = [word for word in words if word not in stop_words]
        
        return ' '.join(filtered_words).strip()
    
    def start_voice_input_session(self) -> Dict:
        """بدء جلسة إدخال البيانات الصوتية"""
        self.conversation_state = {
            'current_field': None,
            'extracted_data': {},
            'confirmation_pending': False,
            'listening': True
        }
        
        self.speak_arabic("مرحباً! سأساعدك في إدخال بيانات عقد الشراء صوتياً. قل لي ما تريد إدخاله.")
        
        return self.process_voice_commands()
    
    def process_voice_commands(self) -> Dict:
        """معالجة الأوامر الصوتية"""
        while self.conversation_state['listening']:
            try:
                # الاستماع للأمر
                speech_text = self.listen_for_speech()
                
                if not speech_text:
                    continue
                
                # التحقق من أوامر الإنهاء
                if any(word in speech_text.lower() for word in ['انتهيت', 'توقف', 'إنهاء', 'خلاص']):
                    self.speak_arabic("تم حفظ البيانات. شكراً لك!")
                    self.conversation_state['listening'] = False
                    break
                
                # تحديد الحقل المطلوب
                field = self.identify_field_from_speech(speech_text)
                
                if field:
                    # استخراج البيانات
                    data = self.extract_data_from_speech(speech_text, field)
                    
                    if data:
                        # حفظ البيانات
                        self.conversation_state['extracted_data'][field] = data
                        
                        # تأكيد البيانات
                        field_name_ar = self.get_arabic_field_name(field)
                        self.speak_arabic(f"تم حفظ {field_name_ar}: {data}")
                        
                    else:
                        self.speak_arabic("لم أتمكن من فهم البيانات. يرجى الإعادة بوضوح أكثر.")
                else:
                    self.speak_arabic("لم أفهم أي حقل تريد ملؤه. يرجى ذكر اسم الحقل مثل 'رقم العقد' أو 'اسم المورد'.")
                
            except Exception as e:
                logger.error(f"خطأ في معالجة الأمر الصوتي: {str(e)}")
                self.speak_arabic("حدث خطأ. يرجى المحاولة مرة أخرى.")
        
        return self.conversation_state['extracted_data']
    
    def get_arabic_field_name(self, field: str) -> str:
        """الحصول على الاسم العربي للحقل"""
        field_names = {
            'contract_number': 'رقم العقد',
            'supplier_name': 'اسم المورد',
            'contract_date': 'تاريخ العقد',
            'delivery_date': 'تاريخ التسليم',
            'total_amount': 'المبلغ الإجمالي',
            'currency': 'العملة',
            'payment_terms': 'شروط الدفع',
            'description': 'الوصف',
            'notes': 'الملاحظات'
        }
        return field_names.get(field, field)
    
    def get_field_suggestions(self) -> List[str]:
        """الحصول على اقتراحات الحقول المتاحة"""
        return [
            "رقم العقد - مثال: عقد رقم 12345",
            "اسم المورد - مثال: اسم المورد شركة الأمل",
            "تاريخ العقد - مثال: تاريخ العقد اليوم",
            "تاريخ التسليم - مثال: التسليم الأسبوع القادم",
            "المبلغ الإجمالي - مثال: المبلغ 50000 ريال",
            "شروط الدفع - مثال: الدفع نقداً عند التسليم",
            "الوصف - مثال: توريد أجهزة كمبيوتر",
            "ملاحظات - مثال: ملاحظة يجب التسليم في المخزن الرئيسي"
        ]
