-- =====================================================
-- إنشاء دالة ذكية لتوليد أرقام طلبات الحوالات
-- Create Smart Transfer Request Number Generation Function
-- =====================================================

-- 1. إنشاء جدول لتتبع أرقام طلبات الحوالات
CREATE TABLE TR_NUMBER_COUNTERS (
    year_value NUMBER(4) PRIMARY KEY,
    last_number NUMBER(6) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. <PERSON><PERSON><PERSON><PERSON><PERSON> جدول لتسجيل عمليات توليد الأرقام
CREATE TABLE TR_NUMBER_LOG (
    id NUMBER PRIMARY KEY,
    old_tr_number VARCHAR2(50),
    new_tr_number VARCHAR2(50),
    operation_type VARCHAR2(20), -- GENERATE, RESET, MIGRATE
    year_context NUMBER(4),
    notes VARCHAR2(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER
);

-- 3. إنشاء sequence للـ log
CREATE SEQUENCE TR_NUMBER_LOG_SEQ START WITH 1 INCREMENT BY 1;

-- 4. إنشاء الدالة الذكية لتوليد أرقام طلبات الحوالات
CREATE OR REPLACE FUNCTION GENERATE_SMART_TR_NUMBER RETURN VARCHAR2 IS
    v_year NUMBER(4);
    v_next_number NUMBER(6);
    v_tr_number VARCHAR2(50);
    v_counter_exists NUMBER;
BEGIN
    -- الحصول على السنة الحالية
    v_year := EXTRACT(YEAR FROM SYSDATE);
    
    -- فحص وجود عداد للسنة الحالية
    SELECT COUNT(*) INTO v_counter_exists 
    FROM TR_NUMBER_COUNTERS 
    WHERE year_value = v_year;
    
    IF v_counter_exists = 0 THEN
        -- إنشاء عداد جديد للسنة
        INSERT INTO TR_NUMBER_COUNTERS (year_value, last_number) 
        VALUES (v_year, 0);
        COMMIT;
    END IF;
    
    -- تحديث العداد والحصول على الرقم التالي
    UPDATE TR_NUMBER_COUNTERS 
    SET last_number = last_number + 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE year_value = v_year
    RETURNING last_number INTO v_next_number;
    
    -- تكوين رقم طلب الحوالة
    v_tr_number := 'TR-' || v_year || '-' || LPAD(v_next_number, 4, '0');
    
    COMMIT;
    
    RETURN v_tr_number;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        -- في حالة الخطأ، إرجاع رقم افتراضي
        RETURN 'TR-' || EXTRACT(YEAR FROM SYSDATE) || '-0001';
END GENERATE_SMART_TR_NUMBER;
/

-- 5. إنشاء دالة لإعادة تعيين العداد (للسنة الجديدة أو الصيانة)
CREATE OR REPLACE FUNCTION RESET_TR_COUNTER(p_year NUMBER, p_start_from NUMBER DEFAULT 0) RETURN VARCHAR2 IS
    v_result VARCHAR2(100);
BEGIN
    -- تحديث أو إدراج العداد
    MERGE INTO TR_NUMBER_COUNTERS tc
    USING (SELECT p_year as year_value, p_start_from as last_number FROM DUAL) src
    ON (tc.year_value = src.year_value)
    WHEN MATCHED THEN
        UPDATE SET last_number = src.last_number, updated_at = CURRENT_TIMESTAMP
    WHEN NOT MATCHED THEN
        INSERT (year_value, last_number) VALUES (src.year_value, src.last_number);
    
    -- تسجيل العملية
    INSERT INTO TR_NUMBER_LOG (
        id, operation_type, year_context, notes, created_at
    ) VALUES (
        TR_NUMBER_LOG_SEQ.NEXTVAL, 'RESET', p_year, 
        'تم إعادة تعيين العداد إلى ' || p_start_from, CURRENT_TIMESTAMP
    );
    
    COMMIT;
    
    v_result := 'تم إعادة تعيين عداد السنة ' || p_year || ' إلى ' || p_start_from;
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RETURN 'خطأ في إعادة تعيين العداد: ' || SQLERRM;
END RESET_TR_COUNTER;
/

-- 6. إنشاء دالة للحصول على الرقم التالي بدون تحديث العداد (للمعاينة)
CREATE OR REPLACE FUNCTION PREVIEW_NEXT_TR_NUMBER RETURN VARCHAR2 IS
    v_year NUMBER(4);
    v_next_number NUMBER(6);
    v_tr_number VARCHAR2(50);
BEGIN
    v_year := EXTRACT(YEAR FROM SYSDATE);
    
    -- الحصول على الرقم التالي بدون تحديث
    SELECT NVL(last_number, 0) + 1 INTO v_next_number
    FROM TR_NUMBER_COUNTERS 
    WHERE year_value = v_year;
    
    v_tr_number := 'TR-' || v_year || '-' || LPAD(v_next_number, 4, '0');
    
    RETURN v_tr_number;
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN 'TR-' || v_year || '-0001';
    WHEN OTHERS THEN
        RETURN 'TR-' || v_year || '-0001';
END PREVIEW_NEXT_TR_NUMBER;
/

-- 7. إنشاء دالة لمزامنة العدادات مع البيانات الموجودة
CREATE OR REPLACE FUNCTION SYNC_TR_COUNTERS RETURN VARCHAR2 IS
    v_result VARCHAR2(1000) := '';
    v_count NUMBER := 0;
BEGIN
    -- مزامنة العدادات لكل سنة موجودة في البيانات
    FOR year_rec IN (
        SELECT EXTRACT(YEAR FROM created_at) as year_value,
               COUNT(*) as tr_count
        FROM TRANSFER_REQUESTS 
        WHERE created_at IS NOT NULL
        GROUP BY EXTRACT(YEAR FROM created_at)
        ORDER BY year_value
    ) LOOP
        -- تحديث أو إدراج العداد
        MERGE INTO TR_NUMBER_COUNTERS tc
        USING (SELECT year_rec.year_value as year_value, year_rec.tr_count as last_number FROM DUAL) src
        ON (tc.year_value = src.year_value)
        WHEN MATCHED THEN
            UPDATE SET last_number = GREATEST(tc.last_number, src.last_number), 
                      updated_at = CURRENT_TIMESTAMP
        WHEN NOT MATCHED THEN
            INSERT (year_value, last_number) VALUES (src.year_value, src.last_number);
        
        v_result := v_result || 'السنة ' || year_rec.year_value || ': ' || year_rec.tr_count || ' طلب; ';
        v_count := v_count + 1;
    END LOOP;
    
    -- تسجيل العملية
    INSERT INTO TR_NUMBER_LOG (
        id, operation_type, notes, created_at
    ) VALUES (
        TR_NUMBER_LOG_SEQ.NEXTVAL, 'SYNC', 
        'تم مزامنة ' || v_count || ' سنة: ' || v_result, CURRENT_TIMESTAMP
    );
    
    COMMIT;
    
    RETURN 'تم مزامنة ' || v_count || ' سنة بنجاح: ' || v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RETURN 'خطأ في المزامنة: ' || SQLERRM;
END SYNC_TR_COUNTERS;
/

-- 8. إنشاء فهارس للأداء
CREATE INDEX idx_tr_counters_year ON TR_NUMBER_COUNTERS(year_value);
CREATE INDEX idx_tr_log_year ON TR_NUMBER_LOG(year_context);
CREATE INDEX idx_tr_log_created ON TR_NUMBER_LOG(created_at);

-- 9. إضافة تعليقات
COMMENT ON TABLE TR_NUMBER_COUNTERS IS 'جدول عدادات أرقام طلبات الحوالات لكل سنة';
COMMENT ON TABLE TR_NUMBER_LOG IS 'سجل عمليات توليد وتعديل أرقام طلبات الحوالات';
COMMENT ON FUNCTION GENERATE_SMART_TR_NUMBER IS 'دالة ذكية لتوليد أرقام طلبات الحوالات بنمط TR-YYYY-NNNN';
COMMENT ON FUNCTION RESET_TR_COUNTER IS 'دالة إعادة تعيين عداد طلبات الحوالات لسنة معينة';
COMMENT ON FUNCTION PREVIEW_NEXT_TR_NUMBER IS 'دالة معاينة الرقم التالي بدون تحديث العداد';
COMMENT ON FUNCTION SYNC_TR_COUNTERS IS 'دالة مزامنة العدادات مع البيانات الموجودة';

-- 10. منح الصلاحيات
GRANT EXECUTE ON GENERATE_SMART_TR_NUMBER TO PUBLIC;
GRANT EXECUTE ON RESET_TR_COUNTER TO PUBLIC;
GRANT EXECUTE ON PREVIEW_NEXT_TR_NUMBER TO PUBLIC;
GRANT EXECUTE ON SYNC_TR_COUNTERS TO PUBLIC;

-- 11. اختبار الدوال
SELECT PREVIEW_NEXT_TR_NUMBER() as "الرقم التالي" FROM DUAL;
SELECT SYNC_TR_COUNTERS() as "نتيجة المزامنة" FROM DUAL;
SELECT GENERATE_SMART_TR_NUMBER() as "رقم جديد" FROM DUAL;

COMMIT;
