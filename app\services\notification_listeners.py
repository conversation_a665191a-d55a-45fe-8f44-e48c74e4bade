# -*- coding: utf-8 -*-
"""
مستمعي الإشعارات الفورية
Instant Notification Listeners
"""

import logging
import os
from datetime import datetime
from .event_manager import on_event
from .green_whatsapp_service import green_whatsapp_service

logger = logging.getLogger(__name__)

# إعداد تلقائي لـ Green API
def _ensure_green_api_configured():
    """التأكد من إعداد Green API"""
    if not green_whatsapp_service.is_configured():
        os.environ['GREEN_API_INSTANCE_ID'] = '7105306929'
        os.environ['GREEN_API_TOKEN'] = '0260de395caa4adfb2aa02d53511f9ffa19b29b41f094fd68c'
        os.environ['GREEN_API_URL'] = 'https://7105.api.greenapi.com'
        os.environ['GREEN_API_TEST_MODE'] = 'false'
        logger.info("🔧 تم إعداد Green API تلقائياً")

# ==========================================
# مستمعي إشعارات التخليص الجمركي
# ==========================================

@on_event('status_changed_to_customs_clearance')
def send_customs_clearance_notification(data):
    """إرسال إشعار فوري عند التخليص الجمركي"""
    try:
        # التأكد من إعداد Green API
        _ensure_green_api_configured()

        logger.info("🔥 EVENT: بدء إرسال إشعار التخليص الجمركي")
        
        # استخراج بيانات الشحنة
        shipment_id = data.get('shipment_id')
        tracking_number = data.get('tracking_number', f'شحنة-{shipment_id}')
        old_status = data.get('old_status', 'غير محدد')
        new_status = data.get('new_status', 'customs_clearance')
        
        logger.info(f"📦 الشحنة: {tracking_number} ({shipment_id})")
        logger.info(f"🔄 التغيير: {old_status} → {new_status}")
        
        # إعداد الرسالة
        message = f"تم تغيير حالة الشحنة {tracking_number} إلى قيد التخليص الجمركي"
        phone = "967774893877"  # رقم المدير العام
        
        logger.info(f"📱 إرسال WhatsApp إلى: {phone}")
        logger.info(f"💬 الرسالة: {message}")
        
        # إرسال WhatsApp
        success, msg_id, response_msg = green_whatsapp_service.send_text_message(phone, message)
        
        if success:
            logger.info(f"✅ EVENT: تم إرسال إشعار التخليص بنجاح!")
            logger.info(f"🆔 معرف الرسالة: {msg_id}")
            
            return {
                'success': True,
                'message_id': msg_id,
                'phone': phone,
                'message': message,
                'sent_at': datetime.now().isoformat()
            }
        else:
            logger.error(f"❌ EVENT: فشل إرسال إشعار التخليص!")
            logger.error(f"📝 السبب: {response_msg}")
            
            return {
                'success': False,
                'error': response_msg,
                'phone': phone,
                'message': message,
                'failed_at': datetime.now().isoformat()
            }
            
    except Exception as e:
        logger.error(f"❌ EVENT: خطأ في إرسال إشعار التخليص: {e}")
        import traceback
        traceback.print_exc()
        
        return {
            'success': False,
            'error': str(e),
            'failed_at': datetime.now().isoformat()
        }

# ==========================================
# مستمعي إشعارات التسليم
# ==========================================

@on_event('status_changed_to_delivered')
def send_delivery_notification(data):
    """إرسال إشعار فوري عند التسليم"""
    try:
        logger.info("🔥 EVENT: بدء إرسال إشعار التسليم")
        
        # استخراج بيانات الشحنة
        shipment_id = data.get('shipment_id')
        tracking_number = data.get('tracking_number', f'شحنة-{shipment_id}')
        
        # إعداد الرسالة
        message = f"تم تسليم الشحنة {tracking_number} بنجاح ✅"
        phone = "967774893877"
        
        logger.info(f"📱 إرسال إشعار التسليم إلى: {phone}")
        
        # إرسال WhatsApp
        success, msg_id, response_msg = green_whatsapp_service.send_text_message(phone, message)
        
        if success:
            logger.info(f"✅ EVENT: تم إرسال إشعار التسليم بنجاح!")
            return {
                'success': True,
                'message_id': msg_id,
                'notification_type': 'delivery_confirmation'
            }
        else:
            logger.error(f"❌ EVENT: فشل إرسال إشعار التسليم: {response_msg}")
            return {
                'success': False,
                'error': response_msg
            }
            
    except Exception as e:
        logger.error(f"❌ EVENT: خطأ في إرسال إشعار التسليم: {e}")
        return {
            'success': False,
            'error': str(e)
        }

# ==========================================
# مستمعي إشعارات وصول الميناء
# ==========================================

@on_event('status_changed_to_arrived_port')
def send_port_arrival_notification(data):
    """إرسال إشعار فوري عند وصول الميناء"""
    try:
        logger.info("🔥 EVENT: بدء إرسال إشعار وصول الميناء")
        
        # استخراج بيانات الشحنة
        shipment_id = data.get('shipment_id')
        tracking_number = data.get('tracking_number', f'شحنة-{shipment_id}')
        port_name = data.get('port_of_discharge', 'الميناء')
        
        # إعداد الرسالة
        message = f"وصلت الشحنة {tracking_number} إلى {port_name} 🚢"
        phone = "967774893877"
        
        logger.info(f"📱 إرسال إشعار وصول الميناء إلى: {phone}")
        
        # إرسال WhatsApp
        success, msg_id, response_msg = green_whatsapp_service.send_text_message(phone, message)
        
        if success:
            logger.info(f"✅ EVENT: تم إرسال إشعار وصول الميناء بنجاح!")
            return {
                'success': True,
                'message_id': msg_id,
                'notification_type': 'port_arrival'
            }
        else:
            logger.error(f"❌ EVENT: فشل إرسال إشعار وصول الميناء: {response_msg}")
            return {
                'success': False,
                'error': response_msg
            }
            
    except Exception as e:
        logger.error(f"❌ EVENT: خطأ في إرسال إشعار وصول الميناء: {e}")
        return {
            'success': False,
            'error': str(e)
        }

# ==========================================
# مستمع عام لجميع تغييرات الحالة
# ==========================================

@on_event('shipment_status_changed')
def log_status_change(data):
    """تسجيل جميع تغييرات الحالة"""
    try:
        shipment_id = data.get('shipment_id')
        tracking_number = data.get('tracking_number')
        old_status = data.get('old_status')
        new_status = data.get('new_status')
        
        logger.info(f"📋 EVENT LOG: شحنة {tracking_number} ({shipment_id}): {old_status} → {new_status}")
        
        return {
            'success': True,
            'logged_at': datetime.now().isoformat(),
            'action': 'status_change_logged'
        }
        
    except Exception as e:
        logger.error(f"❌ EVENT: خطأ في تسجيل تغيير الحالة: {e}")
        return {
            'success': False,
            'error': str(e)
        }

# ==========================================
# دالة تهيئة المستمعين
# ==========================================

def initialize_notification_listeners():
    """تهيئة جميع مستمعي الإشعارات"""
    logger.info("🎯 تم تهيئة مستمعي الإشعارات الفورية")
    logger.info("📝 المستمعين المسجلين:")
    logger.info("   - إشعار التخليص الجمركي")
    logger.info("   - إشعار التسليم")
    logger.info("   - إشعار وصول الميناء")
    logger.info("   - تسجيل تغييرات الحالة")
    
    return True

# تهيئة تلقائية عند استيراد الملف
initialize_notification_listeners()
