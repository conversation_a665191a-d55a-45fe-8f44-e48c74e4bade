{% extends "base.html" %}

{% block title %}إدارة الموردين - NetSuite Oracle{% endblock %}

{% block content %}
<!-- NetSuite REAL Page Header -->
<div class="ns-page-header-real">
    <h1>
        <i class="fas fa-building"></i>
        إدارة الموردين
    </h1>
</div>

<!-- NetSuite REAL Action Buttons -->
<div class="mb-3">
    <a href="{{ url_for('suppliers.import_suppliers') }}" class="ns-btn-real ns-btn-primary-real">
        <i class="fas fa-download me-2"></i>
        استيراد الموردين
    </a>
    <a href="{{ url_for('suppliers.contracts') }}" class="ns-btn-real ns-btn-secondary-real">
        <i class="fas fa-file-contract me-2"></i>
        العقود
    </a>
            </div>
        </div>
    </div>

<!-- NetSuite REAL Stats Cards -->
<div class="ns-stats-real">
    <div class="ns-stat-card-real">
        <div class="ns-stat-icon">
            <i class="fas fa-building"></i>
        </div>
        <div class="ns-stat-number">{{ suppliers.total if suppliers else 0 }}</div>
        <div class="ns-stat-label">إجمالي الموردين</div>
    </div>

    <div class="ns-stat-card-real">
        <div class="ns-stat-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="ns-stat-number">{{ suppliers.items|selectattr('is_active', 'equalto', true)|list|length if suppliers else 0 }}</div>
        <div class="ns-stat-label">الموردين النشطين</div>
    </div>

    <div class="ns-stat-card-real">
        <div class="ns-stat-icon">
            <i class="fas fa-file-contract"></i>
        </div>
        <div class="ns-stat-number">0</div>
        <div class="ns-stat-label">العقود النشطة</div>
    </div>

    <div class="ns-stat-card-real">
        <div class="ns-stat-icon">
            <i class="fas fa-star"></i>
        </div>
        <div class="ns-stat-number">0</div>
        <div class="ns-stat-label">الموردين المميزين</div>
    </div>
</div>
                    <p class="ns-text-secondary" style="font-weight: 600; margin: 0;">موردين نشطين</p>
                </div>
            </div>
        </div>

        <div class="ns-col ns-col-3 ns-col-md-6 ns-col-sm-12">
            <div class="ns-card ns-hover-lift">
                <div class="ns-card-body">
                    <div class="ns-stat-icon ns-warning-bg">
                        <i class="fas fa-clock ns-icon"></i>
                    </div>
                    <h3 class="ns-warning" style="font-size: 2rem; font-weight: 700; margin: 0.5rem 0;">
                        {{ suppliers.items|selectattr('is_approved', 'equalto', false)|list|length if suppliers else 0 }}
                    </h3>
                    <p class="ns-text-secondary" style="font-weight: 600; margin: 0;">في انتظار الموافقة</p>
                </div>
            </div>
        </div>

        <div class="ns-col ns-col-3 ns-col-md-6 ns-col-sm-12">
            <div class="ns-card ns-hover-lift">
                <div class="ns-card-body">
                    <div class="ns-stat-icon ns-orange-bg">
                        <i class="fas fa-file-contract ns-icon"></i>
                    </div>
                    <h3 class="ns-orange" style="font-size: 2rem; font-weight: 700; margin: 0.5rem 0;">0</h3>
                    <p class="ns-text-secondary" style="font-weight: 600; margin: 0;">عقود نشطة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="ns-card-real" style="margin-top: 2rem;">
        <div class="ns-card-header">
            <i class="fas fa-filter me-2"></i>
            البحث والفلاتر
        </div>
        <div class="ns-card-body">
            <form method="GET" class="ns-form-real">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">البحث</label>
                            <input type="text" name="search_term" class="form-control"
                                   value="{{ request.args.get('search_term', '') }}"
                                   placeholder="اسم أو كود المورد...">
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label class="form-label">النوع</label>
                            <select name="type" class="form-control">
                                <option value="">جميع الأنواع</option>
                                <option value="local" {{ 'selected' if request.args.get('type') == 'local' }}>محلي</option>
                                <option value="international" {{ 'selected' if request.args.get('type') == 'international' }}>دولي</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label class="form-label">الفئة</label>
                            <select name="category" class="form-control">
                                <option value="">جميع الفئات</option>
                                <option value="goods" {{ 'selected' if request.args.get('category') == 'goods' }}>بضائع</option>
                                <option value="services" {{ 'selected' if request.args.get('category') == 'services' }}>خدمات</option>
                                <option value="both" {{ 'selected' if request.args.get('category') == 'both' }}>كلاهما</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label class="form-label">الحالة</label>
                            <select name="is_active" class="form-control">
                                <option value="">الكل</option>
                                <option value="1" {{ 'selected' if request.args.get('is_active') == '1' }}>نشط</option>
                                <option value="0" {{ 'selected' if request.args.get('is_active') == '0' }}>غير نشط</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="ns-btn-real ns-btn-primary-real">
                                    <i class="fas fa-search me-2"></i>
                                    بحث
                                </button>
                                <a href="{{ url_for('suppliers.index') }}" class="ns-btn-real ns-btn-secondary-real">
                                    <i class="fas fa-times me-2"></i>
                                    مسح
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Suppliers Table -->
        {% if suppliers.items %}
            <div class="ns-card-real">
                <div class="ns-card-header">
                    <i class="fas fa-table me-2"></i>
                    قائمة الموردين
                </div>
                <div class="ns-card-body">
                    <table class="ns-table-real">
                    <thead>
                        <tr>
                            <th>الكود</th>
                            <th>اسم المورد</th>
                            <th>النوع</th>
                            <th>الفئة</th>
                            <th>الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>التقييم</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for supplier in suppliers.items %}
                            <tr>
                                <td class="cell-number"><strong>{{ supplier.code }}</strong></td>
                                <td>{{ supplier.name_ar }}</td>
                                <td class="cell-status">
                                    {% if supplier.type == 'local' %}
                                        <span class="ns-badge ns-badge-success">محلي</span>
                                    {% elif supplier.type == 'international' %}
                                        <span class="ns-badge ns-badge-primary">دولي</span>
                                    {% else %}
                                        <span class="ns-text-tertiary">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if supplier.category == 'goods' %}
                                        بضائع
                                    {% elif supplier.category == 'services' %}
                                        خدمات
                                    {% elif supplier.category == 'both' %}
                                        كلاهما
                                    {% else %}
                                        غير محدد
                                    {% endif %}
                                </td>
                                <td>{{ supplier.phone or '-' }}</td>
                                <td>{{ supplier.email or '-' }}</td>
                                <td>
                                    {% if supplier.rating %}
                                        <div style="display: flex; align-items: center; gap: 0.25rem;">
                                            {% for i in range(1, 6) %}
                                                {% if i <= supplier.rating %}
                                                    <i class="fas fa-star ns-warning"></i>
                                                {% else %}
                                                    <i class="far fa-star ns-text-tertiary"></i>
                                                {% endif %}
                                            {% endfor %}
                                            <span style="margin-right: 0.5rem; font-size: 0.875rem;">({{ supplier.rating }})</span>
                                        </div>
                                    {% else %}
                                        <span class="ns-text-tertiary">غير مقيم</span>
                                    {% endif %}
                                </td>
                                <td class="cell-status">
                                    {% if supplier.is_active %}
                                        {% if supplier.is_approved %}
                                            <span class="ns-badge ns-badge-success">نشط ومعتمد</span>
                                        {% else %}
                                            <span class="ns-badge ns-badge-warning">نشط - في انتظار الموافقة</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="ns-badge ns-badge-gray">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td class="cell-actions">
                                    <a href="{{ url_for('suppliers.view', id=supplier.id) }}"
                                       class="ns-btn-real ns-btn-outline-real" style="padding: 3px 8px; font-size: 11px; margin-left: 5px;" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('suppliers.edit', id=supplier.id) }}"
                                       class="ns-btn-real ns-btn-outline-real" style="padding: 3px 8px; font-size: 11px; margin-left: 5px;" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ url_for('suppliers.evaluations', id=supplier.id) }}"
                                       class="ns-btn-real ns-btn-outline-real" style="padding: 3px 8px; font-size: 11px;" title="التقييمات">
                                        <i class="fas fa-star"></i>
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
                </div>
            </div>

        {% else %}
            <div class="ns-card-real">
                <div class="ns-card-body">
                    <div class="text-center" style="padding: 4rem 2rem;">
                        <i class="fas fa-building" style="font-size: 4rem; margin-bottom: 1.5rem; opacity: 0.5; color: #6c757d;"></i>
                        <h5 style="margin-bottom: 1rem; color: #495057;">لا توجد موردين</h5>
                        <p style="margin-bottom: 2rem; color: #6c757d;">لم يتم العثور على موردين يطابقون معايير البحث</p>
                        <a href="{{ url_for('suppliers.new') }}" class="ns-btn-real ns-btn-primary-real">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مورد جديد
                        </a>
                    </div>
                </div>
            </div>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if suppliers and suppliers.pages > 1 %}
    <div class="d-flex justify-content-center mt-3">
        <nav>
            <ul class="pagination">
                {% if suppliers.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('suppliers.index', page=suppliers.prev_num, **request.args) }}">
                            السابق
                        </a>
                    </li>
                {% endif %}

                {% for page_num in suppliers.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != suppliers.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('suppliers.index', page=page_num, **request.args) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if suppliers.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('suppliers.index', page=suppliers.next_num, **request.args) }}">
                            التالي
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}

{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('NetSuite REAL Suppliers page loaded');
});
</script>
{% endblock %}
