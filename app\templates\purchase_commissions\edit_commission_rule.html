{% extends "base.html" %}

{% block title %}تعديل قاعدة العمولة{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-edit text-primary me-2"></i>
                        تعديل قاعدة العمولة
                    </h2>
                    <p class="text-muted mb-0">تعديل قاعدة: {{ rule.rule_name }}</p>
                </div>
                <div>
                    <a href="{{ url_for('purchase_commissions.commission_rules') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        تعديل بيانات قاعدة العمولة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">المعلومات الأساسية</h6>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="rule_name" class="form-label">اسم القاعدة *</label>
                                    <input type="text" class="form-control" id="rule_name" name="rule_name" 
                                           value="{{ rule.rule_name }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="commission_type_id" class="form-label">نوع العمولة *</label>
                                    <select class="form-select" id="commission_type_id" name="commission_type_id" required>
                                        <option value="">اختر نوع العمولة</option>
                                        {% for type in commission_types %}
                                        <option value="{{ type.id }}" 
                                                {% if type.id == rule.commission_type_id %}selected{% endif %}>
                                            {{ type.type_name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="rep_id" class="form-label">المندوب</label>
                                    <select class="form-select" id="rep_id" name="rep_id">
                                        <option value="">جميع المندوبين</option>
                                        {% for rep in representatives %}
                                        <option value="{{ rep.id }}" 
                                                {% if rep.id == rule.rep_id %}selected{% endif %}>
                                            {{ rep.rep_name }} ({{ rep.rep_code }})
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="item_category" class="form-label">فئة الصنف</label>
                                    <input type="text" class="form-control" id="item_category" name="item_category" 
                                           value="{{ rule.item_category or '' }}" 
                                           placeholder="مثال: أجهزة كمبيوتر، مواد غذائية...">
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="rule_description" class="form-label">وصف القاعدة</label>
                                    <textarea class="form-control" id="rule_description" name="rule_description" 
                                              rows="3">{{ rule.rule_description or '' }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Commission Settings -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">إعدادات العمولة</h6>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="fixed_amount" class="form-label">المبلغ الثابت (ريال)</label>
                                    <input type="number" class="form-control" id="fixed_amount" name="fixed_amount" 
                                           min="0" step="0.01" value="{{ rule.fixed_amount or '' }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="percentage_rate" class="form-label">النسبة المئوية (%)</label>
                                    <input type="number" class="form-control" id="percentage_rate" name="percentage_rate" 
                                           min="0" max="100" step="0.01" value="{{ rule.percentage_rate or '' }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="quantity_rate" class="form-label">عمولة الكمية (ريال/وحدة)</label>
                                    <input type="number" class="form-control" id="quantity_rate" name="quantity_rate" 
                                           min="0" step="0.01" value="{{ rule.quantity_rate or '' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="quantity_unit" class="form-label">وحدة الكمية</label>
                                    <input type="text" class="form-control" id="quantity_unit" name="quantity_unit" 
                                           value="{{ rule.quantity_unit or '' }}" placeholder="مثال: قطعة، كيلو، متر...">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="priority_order" class="form-label">ترتيب الأولوية</label>
                                    <input type="number" class="form-control" id="priority_order" name="priority_order" 
                                           min="1" value="{{ rule.priority_order or 1 }}">
                                </div>
                            </div>
                        </div>

                        <!-- Limits and Conditions -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">الحدود والشروط</h6>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="min_order_value" class="form-label">أدنى قيمة طلب (ريال)</label>
                                    <input type="number" class="form-control" id="min_order_value" name="min_order_value" 
                                           min="0" step="0.01" value="{{ rule.min_order_value or '' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_commission" class="form-label">أقصى عمولة (ريال)</label>
                                    <input type="number" class="form-control" id="max_commission" name="max_commission" 
                                           min="0" step="0.01" value="{{ rule.max_commission or '' }}">
                                </div>
                            </div>
                        </div>

                        <!-- Validity Period -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">فترة الصلاحية</h6>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="effective_from" class="form-label">تاريخ البداية</label>
                                    <input type="date" class="form-control" id="effective_from" name="effective_from" 
                                           value="{{ rule.effective_from.strftime('%Y-%m-%d') if rule.effective_from else '' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="effective_to" class="form-label">تاريخ النهاية</label>
                                    <input type="date" class="form-control" id="effective_to" name="effective_to" 
                                           value="{{ rule.effective_to.strftime('%Y-%m-%d') if rule.effective_to else '' }}">
                                </div>
                            </div>
                        </div>

                        <!-- Status -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">الحالة</h6>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           {% if rule.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        <strong>قاعدة نشطة</strong>
                                        <br><small class="text-muted">يتم تطبيق القاعدة النشطة على الحسابات الجديدة</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>
                                            حفظ التعديلات
                                        </button>
                                        <a href="{{ url_for('purchase_commissions.commission_rules') }}" 
                                           class="btn btn-secondary ms-2">
                                            <i class="fas fa-times me-2"></i>
                                            إلغاء
                                        </a>
                                    </div>
                                    <div>
                                        <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                            <i class="fas fa-trash me-2"></i>
                                            حذف القاعدة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكيد الحذف
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                    <h5>هل أنت متأكد من حذف هذه القاعدة؟</h5>
                    <p class="text-muted">
                        <strong>{{ rule.rule_name }}</strong><br>
                        هذا الإجراء لا يمكن التراجع عنه!
                    </p>
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم حذف جميع البيانات المرتبطة بهذه القاعدة
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </button>
                <form method="POST" action="{{ url_for('purchase_commissions.delete_commission_rule', rule_id=rule.id) }}" 
                      style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        تأكيد الحذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const ruleName = document.getElementById('rule_name').value;
    const commissionTypeId = document.getElementById('commission_type_id').value;
    
    if (!ruleName || !commissionTypeId) {
        e.preventDefault();
        alert('اسم القاعدة ونوع العمولة مطلوبان');
        return false;
    }
});

// Commission type change handler
document.getElementById('commission_type_id').addEventListener('change', function() {
    const typeId = this.value;
    const fixedAmount = document.getElementById('fixed_amount');
    const percentageRate = document.getElementById('percentage_rate');
    const quantityRate = document.getElementById('quantity_rate');
    
    // Reset all fields
    fixedAmount.disabled = false;
    percentageRate.disabled = false;
    quantityRate.disabled = false;
    
    // Enable/disable based on commission type
    if (typeId == '1') { // Fixed commission
        percentageRate.disabled = true;
        quantityRate.disabled = true;
        percentageRate.value = '';
        quantityRate.value = '';
    } else if (typeId == '2') { // Percentage commission
        fixedAmount.disabled = true;
        quantityRate.disabled = true;
        fixedAmount.value = '';
        quantityRate.value = '';
    } else if (typeId == '4' || typeId == '5') { // Quantity-based
        fixedAmount.disabled = true;
        percentageRate.disabled = true;
        fixedAmount.value = '';
        percentageRate.value = '';
    }
});

// Trigger change event on page load
document.getElementById('commission_type_id').dispatchEvent(new Event('change'));
</script>
{% endblock %}
